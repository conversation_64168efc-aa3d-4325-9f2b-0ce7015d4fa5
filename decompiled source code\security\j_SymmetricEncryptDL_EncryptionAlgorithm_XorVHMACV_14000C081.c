/*
 * Function: j_?SymmetricEncrypt@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE1_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x14000C081
 */

void __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::SymmetricEncrypt(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, CryptoPP::RandomNumberGenerator *rng, const char *key, const char *plaintext, unsigned __int64 plaintextLength, char *ciphertext, CryptoPP::NameValuePairs *parameters)
{
  CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::SymmetricEncrypt(
    this,
    rng,
    key,
    plaintext,
    plaintextLength,
    ciphertext,
    parameters);
}
