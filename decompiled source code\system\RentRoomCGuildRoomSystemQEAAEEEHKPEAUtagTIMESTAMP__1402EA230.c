/*
 * Function: ?RentRoom@CGuildRoomSystem@@QEAAEEEHKPEAUtagTIMESTAMP_STRUCT@@_N@Z
 * Address: 0x1402EA230
 */

char __fastcall CGuildRoomSystem::RentRoom(CGuildRoomSystem *this, char byRace, char byRoomType, int iGuildInx, unsigned int dwGuildSerial, tagTIMESTAMP_STRUCT *ts, bool bRestore)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CGuildRoomInfo *v10; // rax@12
  CGuildRoomInfo *v11; // rax@13
  CGuildRoomInfo *v12; // rax@15
  CGuildRoomInfo *v13; // rax@18
  __int64 v14; // [sp+0h] [bp-88h]@1
  int v15; // [sp+20h] [bp-68h]@7
  int v16; // [sp+24h] [bp-64h]@9
  int v17; // [sp+28h] [bp-60h]@9
  char Dst; // [sp+38h] [bp-50h]@15
  char v19; // [sp+60h] [bp-28h]@15
  CGuildRoomSystem *v20; // [sp+90h] [bp+8h]@1
  char v21; // [sp+98h] [bp+10h]@1
  char v22; // [sp+A0h] [bp+18h]@1
  int iGuildInxa; // [sp+A8h] [bp+20h]@1

  iGuildInxa = iGuildInx;
  v22 = byRoomType;
  v21 = byRace;
  v20 = this;
  v7 = &v14;
  for ( i = 30i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 && (signed int)(unsigned __int8)byRoomType < 2 )
  {
    v15 = 0;
    v15 = CGuildRoomSystem::GetRoomCountByType(v20, byRace, byRoomType);
    if ( v15 < *((_BYTE *)&CGuildRoomInfo::sm_RoomInfo + 8 * (unsigned __int8)v22 + 9) )
    {
      v16 = 30 * (unsigned __int8)v21;
      v17 = 30 * (unsigned __int8)v21 + 30;
      while ( v16 < v17 )
      {
        v10 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v20->m_vecGuildRoom, v16);
        if ( !CGuildRoomInfo::IsRent(v10) )
        {
          v11 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v20->m_vecGuildRoom, v16);
          if ( (unsigned __int8)CGuildRoomInfo::GetRoomType(v11) == (unsigned __int8)v22 )
          {
            if ( bRestore == 1 )
            {
              memcpy_0(&Dst, ts, 0x10ui64);
              qmemcpy(&v19, &Dst, 0x10ui64);
              v12 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v20->m_vecGuildRoom, v16);
              if ( !CGuildRoomInfo::SetRoom_Restore(v12, iGuildInxa, dwGuildSerial, (tagTIMESTAMP_STRUCT *)&v19) )
                return 3;
            }
            else
            {
              v13 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v20->m_vecGuildRoom, v16);
              if ( !CGuildRoomInfo::SetRoom(v13, iGuildInxa, dwGuildSerial) )
                return 3;
            }
            break;
          }
        }
        ++v16;
      }
      if ( v16 == v17 )
        result = 2;
      else
        result = 0;
    }
    else
    {
      result = 2;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
