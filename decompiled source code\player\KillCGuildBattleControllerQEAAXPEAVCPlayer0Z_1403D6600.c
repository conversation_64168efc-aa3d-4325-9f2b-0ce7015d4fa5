/*
 * Function: ?Kill@CGuildBattleController@@QEAAXPEAVCPlayer@@0@Z
 * Address: 0x1403D6600
 */

void __fastcall CGuildBattleController::Kill(CGuildBattleController *this, CPlayer *pkSrcPlayer, CPlayer *pkDestPlayer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@4
  unsigned int dwGuildSerial; // [sp+24h] [bp-24h]@7
  unsigned int dwSrcCharacSerial; // [sp+28h] [bp-20h]@7
  unsigned int dwDestCharacSerial; // [sp+2Ch] [bp-1Ch]@7
  unsigned int v11; // [sp+30h] [bp-18h]@5
  CPlayer *v12; // [sp+58h] [bp+10h]@1
  CPlayer *v13; // [sp+60h] [bp+18h]@1

  v13 = pkDestPlayer;
  v12 = pkSrcPlayer;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CPlayerDB::GetRaceCode(&pkSrcPlayer->m_Param);
  if ( v12->m_Param.m_pGuild )
    v11 = v12->m_Param.m_pGuild->m_dwSerial;
  else
    v11 = -1;
  dwGuildSerial = v11;
  dwSrcCharacSerial = v12->m_pUserDB->m_dwSerial;
  dwDestCharacSerial = v13->m_pUserDB->m_dwSerial;
  v5 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::Kill(v5, dwGuildSerial, dwSrcCharacSerial, dwDestCharacSerial);
}
