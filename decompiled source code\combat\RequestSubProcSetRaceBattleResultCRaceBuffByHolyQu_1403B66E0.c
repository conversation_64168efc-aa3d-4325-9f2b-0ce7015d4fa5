/*
 * Function: ?RequestSubProcSetRaceBattleResult@CRaceBuffByHolyQuestProcedure@@AEAA_NXZ
 * Address: 0x1403B66E0
 */

char __fastcall CRaceBuffByHolyQuestProcedure::RequestSubProcSetRaceBattleResult(CRaceBuffByHolyQuestProcedure *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@10
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@5
  int v7; // [sp+24h] [bp-14h]@8
  CRaceBuffByHolyQuestProcedure *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CHolyStoneSystem::GetDestroyStoneRace(&g_HolySys) == -1 )
    v6 = 255;
  else
    v6 = CHolyStoneSystem::GetDestroyStoneRace(&g_HolySys);
  if ( CHolyStoneSystem::GetHolyMasterRace(&g_HolySys) == -1 )
    v7 = 255;
  else
    v7 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
  v3 = CRaceBuffInfoByHolyQuestList::GetMaxThCnt(&v8->m_kBuffInfo);
  CRaceBuffHolyQuestResultInfo::SetResult(&v8->m_kBuffHolyQestResultInfo, v7, v6, v3);
  return 1;
}
