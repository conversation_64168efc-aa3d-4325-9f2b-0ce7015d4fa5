/*
 * Function: ?BreakStealth@CCharacter@@QEAAXXZ
 * Address: 0x140175460
 */

void __usercall CCharacter::BreakStealth(CCharacter *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CCharacter *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( _effect_parameter::GetEff_State(&v5->m_EP, 5)
    || (_effect_parameter::GetEff_Plus(&v5->m_EP, 21), a2 > 0.0)
    || _effect_parameter::GetEff_State(&v5->m_EP, 26) )
  {
    CGameObject::SetBreakTranspar((CGameObject *)&v5->vfptr, 1);
  }
}
