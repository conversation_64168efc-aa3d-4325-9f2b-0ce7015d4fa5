/*
 * Function: ?Remaintime_Personal@CBilling@@QEAAXPEADFJPEAU_SYSTEMTIME@@@Z
 * Address: 0x14028CF50
 */

void __fastcall CBilling::Remaintime_Personal(CBilling *this, char *szID, __int16 iType, int lRemaintime, _SYSTEMTIME *pstEndDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPlayer *v8; // [sp+20h] [bp-18h]@5
  CBilling *v9; // [sp+40h] [bp+8h]@1
  __int16 v10; // [sp+50h] [bp+18h]@1
  int lRemainTime; // [sp+58h] [bp+20h]@1

  lRemainTime = lRemaintime;
  v10 = iType;
  v9 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v9->m_bOper )
  {
    v8 = GetPtrPlayerFromAccount(&g_Player, 2532, szID);
    if ( v8 )
    {
      if ( v8->m_pUserDB->m_BillingInfo.iType >= 100
        && (v8->m_pUserDB->m_BillingInfo.iType == 6 || v8->m_pUserDB->m_BillingInfo.iType == 7) )
      {
        CPlayer::SendMsg_RemainTimeInform(v8, v10, lRemainTime, pstEndDate);
        CUserDB::SetRemainTime(v8->m_pUserDB, lRemainTime);
      }
    }
  }
}
