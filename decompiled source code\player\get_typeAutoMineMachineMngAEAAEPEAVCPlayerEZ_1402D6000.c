/*
 * Function: ?get_type@AutoMineMachineMng@@AEAAEPEAVCPlayer@@E@Z
 * Address: 0x1402D6000
 */

char __fastcall AutoMineMachineMng::get_type(AutoMineMachineMng *this, CPlayer *pUser, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  AutoMineMachineMng *v7; // [sp+30h] [bp+8h]@1
  CPlayer *pUsera; // [sp+38h] [bp+10h]@1
  char v9; // [sp+40h] [bp+18h]@1

  v9 = byRace;
  pUsera = pUser;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( AutoMineMachine::IsMaster((AutoMineMachine *)v7 + 2 * (unsigned __int8)byRace, pUser) )
  {
    result = 0;
  }
  else if ( AutoMineMachine::IsMaster(&v7->m_Machine[(unsigned __int8)v9][1], pUsera) )
  {
    result = 1;
  }
  else
  {
    result = -1;
  }
  return result;
}
