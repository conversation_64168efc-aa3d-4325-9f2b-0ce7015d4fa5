/*
 * Function: ?CallProc_RFOnlineAvg_Event@CRFCashItemDatabase@@QEAAHAEAK@Z
 * Address: 0x1404832B0
 */

__int64 __fastcall CRFCashItemDatabase::CallProc_RFOnlineAvg_Event(CRFCashItemDatabase *this, unsigned int *iAvgCashSelling)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-188h]@1
  char _Dest[256]; // [sp+40h] [bp-148h]@4
  char v7; // [sp+144h] [bp-44h]@4
  unsigned __int16 ColumnNumber; // [sp+154h] [bp-34h]@8
  unsigned __int64 v9; // [sp+170h] [bp-18h]@4
  CRFCashItemDatabase *v10; // [sp+190h] [bp+8h]@1
  unsigned int *v11; // [sp+198h] [bp+10h]@1

  v11 = iAvgCashSelling;
  v10 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  sprintf_s<256>(
    (char (*)[256])_Dest,
    "declare @out_amount int  exec Prc_RFONLINE_AVG_EVENT @t_amount = @out_amount output select @out_amount");
  v7 = CRFNewDatabase::SQLExecDirect_RetErrCode((CRFNewDatabase *)&v10->vfptr, _Dest);
  if ( v7 )
  {
    result = (unsigned __int8)v7;
  }
  else
  {
    v7 = CRFNewDatabase::SQLFetch_RetErrCode((CRFNewDatabase *)&v10->vfptr, _Dest);
    if ( v7 )
    {
      result = (unsigned __int8)v7;
    }
    else
    {
      ColumnNumber = 1;
      v7 = CRFNewDatabase::SQLGetData_RetErrCode((CRFNewDatabase *)&v10->vfptr, _Dest, &ColumnNumber, 4, v11);
      if ( v7 )
      {
        result = (unsigned __int8)v7;
      }
      else
      {
        CRFNewDatabase::SelectCleanUp((CRFNewDatabase *)&v10->vfptr, _Dest);
        result = 0i64;
      }
    }
  }
  return result;
}
