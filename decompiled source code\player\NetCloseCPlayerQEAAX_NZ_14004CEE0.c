/*
 * Function: ?NetClose@CPlayer@@QEAAX_N@Z
 * Address: 0x14004CEE0
 */

void __usercall CPlayer::NetClose(CPlayer *this@<rcx>, bool bMoveOutLobby@<dl>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  PatriarchElectProcessor *v5; // rax@5
  LendItemMng *v6; // rax@8
  TimeLimitJadeMng *v7; // rax@11
  CExchangeEvent *v8; // rax@19
  CExchangeEvent *v9; // rax@21
  CPcBangFavor *v10; // rax@22
  int v11; // eax@23
  CGuildBattleController *v12; // rax@31
  CUnmannedTraderController *v13; // rax@56
  CUnmannedTraderController *v14; // rax@56
  char *v15; // rax@56
  int v16; // ecx@56
  CUnmannedTraderController *v17; // rax@56
  CMoveMapLimitManager *v18; // rax@56
  int v19; // eax@56
  int v20; // ecx@56
  float v21; // xmm0_4@56
  CBillingManager *v22; // rax@62
  CGuildRoomSystem *v23; // rax@73
  CNationSettingManager *v24; // rax@91
  __int64 v25; // [sp+0h] [bp-1B8h]@1
  unsigned __int16 wKillPoint[4]; // [sp+20h] [bp-198h]@26
  unsigned __int16 wDiePoint[4]; // [sp+28h] [bp-190h]@26
  unsigned int byCristalBattleDBInfo[2]; // [sp+30h] [bp-188h]@26
  char byHSKTime[8]; // [sp+38h] [bp-180h]@26
  unsigned int dwIP[2]; // [sp+40h] [bp-178h]@56
  unsigned int dwExpRate[2]; // [sp+48h] [bp-170h]@56
  CUnmannedTraderRegistItemInfo *pkInfo; // [sp+50h] [bp-168h]@56
  char byMaxCnt; // [sp+58h] [bp-160h]@56
  char *pszFileName; // [sp+60h] [bp-158h]@56
  char pdata; // [sp+74h] [bp-144h]@5
  int j; // [sp+84h] [bp-134h]@12
  _TRAP_PARAM::_param *v37; // [sp+88h] [bp-130h]@15
  char v38; // [sp+90h] [bp-128h]@37
  float pfPos; // [sp+A8h] [bp-110h]@52
  CMapData *v40; // [sp+C8h] [bp-F0h]@52
  _dh_player_mgr::_pos poutPlayerPos; // [sp+D8h] [bp-E0h]@55
  _BUDDY_LIST::__list *v42; // [sp+F8h] [bp-C0h]@67
  DWORD v43; // [sp+100h] [bp-B8h]@74
  int k; // [sp+104h] [bp-B4h]@75
  _ATTACK_DELAY_CHECKER *v45; // [sp+108h] [bp-B0h]@77
  _ATTACK_DELAY_CHECKER::_mas_list *v46; // [sp+110h] [bp-A8h]@81
  CExchangeEvent *v47; // [sp+118h] [bp-A0h]@20
  int *v48; // [sp+120h] [bp-98h]@56
  char *v49; // [sp+128h] [bp-90h]@56
  char v50; // [sp+130h] [bp-88h]@56
  CUnmannedTraderRegistItemInfo *v51; // [sp+138h] [bp-80h]@56
  CUserDB *v52; // [sp+140h] [bp-78h]@56
  CUserDB *v53; // [sp+148h] [bp-70h]@56
  char *pszID; // [sp+150h] [bp-68h]@56
  _AVATOR_DATA *pBackupData; // [sp+158h] [bp-60h]@56
  _AVATOR_DATA *pLoadData; // [sp+160h] [bp-58h]@56
  char *v57; // [sp+168h] [bp-50h]@56
  unsigned int *pdwAlter; // [sp+170h] [bp-48h]@56
  _MASTERY_PARAM *pData; // [sp+178h] [bp-40h]@56
  int *pnMaxPoint; // [sp+180h] [bp-38h]@56
  int nGrade; // [sp+188h] [bp-30h]@56
  long double v62; // [sp+190h] [bp-28h]@56
  CUserDB *v63; // [sp+198h] [bp-20h]@73
  int n; // [sp+1A0h] [bp-18h]@73
  CGuild *v65; // [sp+1A8h] [bp-10h]@73
  CPlayer *pOne; // [sp+1C0h] [bp+8h]@1
  bool v67; // [sp+1C8h] [bp+10h]@1

  v67 = bMoveOutLobby;
  pOne = this;
  v3 = &v25;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pOne->m_byPatriarchAppointPropose != 255 )
  {
    pdata = 1;
    v5 = PatriarchElectProcessor::Instance();
    PatriarchElectProcessor::Doit(v5, _eRespAppoint, pOne, &pdata);
  }
  if ( pOne->m_Param.m_pAPM )
    AutominePersonal::unregist_from_map(pOne->m_Param.m_pAPM, 2);
  pOne->m_Param.m_bPersonalAmineInven = 0;
  v6 = LendItemMng::Instance();
  LendItemMng::Release(v6, pOne->m_ObjID.m_wIndex);
  if ( pOne->m_bLoad && pOne->m_bOper )
  {
    TimeLimitMgr::Pop_Data(qword_1799CA2D0, pOne->m_pUserDB->m_dwAccountSerial, pOne->m_id.wIndex);
    CLogFile::Write(
      &stru_1799C8E78,
      "CPlayer::NetClose() : Time Limit System Stop!! : ID = %s",
      pOne->m_pUserDB->m_szAccountID);
  }
  v7 = TimeLimitJadeMng::Instance();
  TimeLimitJadeMng::Release(v7, pOne->m_ObjID.m_wIndex);
  if ( pOne->m_bLive )
  {
    CPlayer::DTradeInit(pOne);
    CPlayer::_TowerAllReturn(pOne, 2, 0);
    CPlayer::_AnimusReturn(pOne, 2);
    CPlayer::ForcePullUnit(pOne, 1);
    for ( j = 0; j < 20; ++j )
    {
      v37 = &pOne->m_pmTrp.m_Item[j];
      if ( _TRAP_PARAM::_param::isLoad(v37) && v37->pItem->m_dwObjSerial == v37->dwSerial )
      {
        CPlayerDB::GetPvPPoint(&pOne->m_Param);
        CTrap::MasterNetClose(v37->pItem, a3);
      }
    }
    v8 = CExchangeEvent::Instance();
    if ( CExchangeEvent::IsDelete(v8)
      || (v47 = CExchangeEvent::Instance(),
          (unsigned __int8)((int (__fastcall *)(CExchangeEvent *))v47->vfptr->IsEnable)(v47)) )
    {
      v9 = CExchangeEvent::Instance();
      CExchangeEvent::DeleteExchangeEventItem(v9, pOne);
    }
    v10 = CPcBangFavor::Instance();
    CPcBangFavor::PcBangDeleteItem(v10, pOne);
    if ( pOne->m_pCurMap == (CMapData *)*((_QWORD *)&g_TransportShip + 10162 * CPlayerDB::GetRaceCode(&pOne->m_Param)
                                                                     + 2) )
    {
      v11 = CPlayerDB::GetRaceCode(&pOne->m_Param);
      CTransportShip::ExitMember((CTransportShip *)&g_TransportShip + v11, pOne, 1);
    }
    if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 && pOne->m_byHSKQuestCode != 100 )
    {
      byHSKTime[0] = pOne->m_byHSKTime;
      LOBYTE(byCristalBattleDBInfo[0]) = pOne->m_byCristalBattleDBInfo;
      wDiePoint[0] = pOne->m_wDiePoint;
      wKillPoint[0] = pOne->m_wKillPoint;
      CHolyStoneSystem::PushStoreQuestCash(
        &g_HolySys,
        pOne->m_dwObjSerial,
        pOne->m_byHSKQuestCode,
        pOne->m_nHSKPvpPoint,
        wKillPoint[0],
        wDiePoint[0],
        byCristalBattleDBInfo[0],
        byHSKTime[0]);
    }
    if ( ((int (__fastcall *)(CPlayer *))pOne->vfptr->GetLevel)(pOne) >= 25
      && (g_HolySys.m_bScheduleCodePre == 1 || CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1) )
    {
      CHolyStoneSystem::PushQuestCash_Other(&g_HolySys, pOne->m_dwObjSerial, pOne->m_byStoneMapMoveInfo);
    }
    v12 = CGuildBattleController::Instance();
    CGuildBattleController::NetClose(v12, pOne);
    if ( pOne->m_Param.m_pGuild )
    {
      _effect_parameter::GetEff_Have(&pOne->m_EP, 50);
      if ( *(float *)&a3 <= 0.0 )
      {
        CGuild::LogoffMember(pOne->m_Param.m_pGuild, pOne->m_dwObjSerial);
        CGuild::SendMsg_GuildMemberLogoff(pOne->m_Param.m_pGuild, pOne->m_dwObjSerial);
      }
    }
    if ( pOne->m_Param.m_pApplyGuild )
    {
      _effect_parameter::GetEff_Have(&pOne->m_EP, 50);
      if ( *(float *)&a3 <= 0.0 )
        CGuild::PopApplier(pOne->m_Param.m_pApplyGuild, pOne->m_dwObjSerial, 2);
    }
    CPlayer::SendMsg_Destroy(pOne);
    CCharacter::Destroy((CCharacter *)&pOne->vfptr);
    v38 = 0;
    if ( pOne->m_bOutOfMap || pOne->m_bCheckMovePacket )
    {
      v38 = 1;
    }
    else if ( unk_1799C9ACC )
    {
      v38 = 0;
    }
    else if ( pOne->m_bOper )
    {
      v38 = 0;
    }
    else if ( CPlayerDB::GetLevel(&pOne->m_Param) <= 7 && ((int (__fastcall *)(CPlayer *))pOne->vfptr->GetHP)(pOne) > 0 )
    {
      v38 = 1;
    }
    if ( CHolyStoneSystem::IsControlScene(&g_HolySys)
      && !strncmp(pOne->m_pCurMap->m_pMapSet->m_strCode, "resources", 9ui64) )
    {
      v38 = 1;
    }
    if ( !pOne->m_pDHChannel )
    {
      if ( v38 )
      {
        v40 = CPlayer::GetBindMap(pOne, &pfPos, 0);
        if ( v40 )
        {
          pOne->m_pCurMap = v40;
          memcpy_0(pOne->m_fCurPos, &pfPos, 0xCui64);
        }
      }
    }
    if ( pOne->m_pDHChannel )
    {
      CDarkHoleChannel::ClearMember(pOne->m_pDHChannel, pOne, pOne->m_bOper, &poutPlayerPos);
      pOne->m_pCurMap = poutPlayerPos.pMap;
      memcpy_0(pOne->m_fCurPos, poutPlayerPos.fPos, 0xCui64);
    }
    CPlayerDB::SetMapCode(&pOne->m_Param, pOne->m_pCurMap->m_pMapSet->m_dwIndex);
    CPlayerDB::SetCurPos(&pOne->m_Param, pOne->m_fCurPos);
    CPlayer::ExitUpdateDataToWorld(pOne);
    --CPlayer::s_nLiveNum;
    v48 = &CPlayer::s_nRaceNum + CPlayerDB::GetRaceCode(&pOne->m_Param);
    --*v48;
    v49 = pOne->m_szItemHistoryFileName;
    v13 = CUnmannedTraderController::Instance();
    v50 = CUnmannedTraderController::GetMaxRegistCnt(v13, pOne->m_ObjID.m_wIndex, pOne->m_dwObjSerial);
    v14 = CUnmannedTraderController::Instance();
    v51 = CUnmannedTraderController::GetRegItemInfo(v14, pOne->m_ObjID.m_wIndex, pOne->m_dwObjSerial);
    v52 = pOne->m_pUserDB;
    v53 = pOne->m_pUserDB;
    pszID = pOne->m_pUserDB->m_szAccountID;
    pBackupData = &pOne->m_pUserDB->m_AvatorData_bk;
    pLoadData = &pOne->m_pUserDB->m_AvatorData;
    v15 = CPlayerDB::GetCharNameA(&pOne->m_Param);
    v16 = pOne->m_ObjID.m_wIndex;
    pszFileName = v49;
    byMaxCnt = v50;
    pkInfo = v51;
    dwExpRate[0] = pOne->m_dwExpRate;
    dwIP[0] = v52->m_ipAddress;
    byHSKTime[0] = pOne->m_byUserDgr;
    byCristalBattleDBInfo[0] = v53->m_dwAccountSerial;
    CMgrAvatorItemHistory::have_item_close(
      &CPlayer::s_MgrItemHistory,
      v16,
      v15,
      pLoadData,
      pBackupData,
      pszID,
      byCristalBattleDBInfo[0],
      byHSKTime[0],
      dwIP[0],
      dwExpRate[0],
      v51,
      v50,
      v49);
    v17 = CUnmannedTraderController::Instance();
    CUnmannedTraderController::LogOut(v17, pOne->m_ObjID.m_wIndex, pOne->m_dwObjSerial);
    CMgrAvatorItemHistory::post_storage(
      &CPlayer::s_MgrItemHistory,
      &pOne->m_Param.m_PostStorage,
      pOne->m_szItemHistoryFileName);
    CMgrAvatorItemHistory::return_post_storage(
      &CPlayer::s_MgrItemHistory,
      &pOne->m_Param.m_ReturnPostStorage,
      pOne->m_szItemHistoryFileName);
    v18 = CMoveMapLimitManager::Instance();
    CMoveMapLimitManager::LogOut(v18, pOne);
    wa_ExitWorld(&pOne->m_id);
    v57 = pOne->m_szLvHistoryFileName;
    pdwAlter = pOne->m_Param.m_dwAlterMastery;
    pData = &pOne->m_pmMst;
    pnMaxPoint = pOne->m_nMaxPoint;
    nGrade = pOne->m_Param.m_byPvPGrade;
    CPlayerDB::GetExp(&pOne->m_Param);
    v62 = a3;
    v19 = CPlayerDB::GetLevel(&pOne->m_Param);
    v20 = pOne->m_ObjID.m_wIndex;
    pszFileName = 0i64;
    byMaxCnt = 1;
    pkInfo = (CUnmannedTraderRegistItemInfo *)v57;
    *(_QWORD *)dwExpRate = pdwAlter;
    *(_QWORD *)dwIP = pData;
    *(_QWORD *)byHSKTime = pnMaxPoint;
    byCristalBattleDBInfo[0] = nGrade;
    *(_DWORD *)wDiePoint = pOne->m_dwExpRate;
    v21 = *(float *)&v62;
    *(long double *)wKillPoint = v62;
    CMgrAvatorLvHistory::update_mastery(
      &CPlayer::s_MgrLvHistory,
      v20,
      pOne->m_byUserDgr,
      v19,
      v62,
      *(unsigned int *)wDiePoint,
      nGrade,
      pnMaxPoint,
      pData,
      pdwAlter,
      v57,
      1,
      0i64);
    ++CPlayer::s_dwTotalCloseCount;
    if ( pOne->m_pUserDB )
    {
      if ( pOne->m_bOper )
      {
        CMgrAvatorItemHistory::close(
          &CPlayer::s_MgrItemHistory,
          pOne->m_ObjID.m_wIndex,
          "Abnormal",
          pOne->m_szItemHistoryFileName);
        ++CPlayer::s_dwAbnormalCloseCount;
      }
      else
      {
        CMgrAvatorItemHistory::close(
          &CPlayer::s_MgrItemHistory,
          pOne->m_ObjID.m_wIndex,
          "Normal",
          pOne->m_szItemHistoryFileName);
      }
    }
    if ( !pOne->m_pUserDB->m_byUserDgr && !pOne->m_pUserDB->m_bBillingNoLogout )
    {
      v22 = CTSingleton<CBillingManager>::Instance();
      CBillingManager::Logout(v22, pOne->m_pUserDB);
    }
    _effect_parameter::GetEff_Have(&pOne->m_EP, 50);
    if ( v21 <= 0.0 )
    {
      for ( j = 0; j < 50; ++j )
      {
        v42 = &pOne->m_pmBuddy.m_List[j];
        if ( _BUDDY_LIST::__list::fill(v42)
          && v42->pPtr
          && _BUDDY_LIST::SearchBuddyLogoff(&v42->pPtr->m_pmBuddy, pOne->m_dwObjSerial) )
        {
          CPlayer::SendMsg_BuddyLogoffInform(v42->pPtr, pOne->m_dwObjSerial);
        }
      }
    }
  }
  if ( pOne->m_Param.m_pGuild )
  {
    v63 = pOne->m_pUserDB;
    n = pOne->m_ObjID.m_wIndex;
    v65 = pOne->m_Param.m_pGuild;
    v23 = CGuildRoomSystem::GetInstance();
    CGuildRoomSystem::RoomOut(v23, v65->m_dwSerial, n, v63->m_dwSerial);
  }
  v43 = pOne->m_AttDelayChker.m_nNextAddTime + timeGetTime();
  if ( pOne->m_pUserDB )
  {
    for ( k = 0; k < 10; ++k )
    {
      v45 = (_ATTACK_DELAY_CHECKER *)((char *)&pOne->m_AttDelayChker + 8 * k);
      if ( v45->EFF[0].byEffectCode != 255 && v45->EFF[0].dwNextTime > v43 )
      {
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].byEffectCode = v45->EFF[0].byEffectCode;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].wEffectIndex = v45->EFF[0].wEffectIndex;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].dwNextTime = v45->EFF[0].dwNextTime - v43;
      }
      else
      {
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].byEffectCode = -1;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].wEffectIndex = 0;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.EFF[k].dwNextTime = 0;
      }
      v46 = &pOne->m_AttDelayChker.MAS[k];
      if ( v46->byEffectCode != 255 && v46->dwNextTime > v43 )
      {
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byEffectCode = v46->byEffectCode;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byMastery = v46->byMastery;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].dwNextTime = v46->dwNextTime - v43;
      }
      else
      {
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byEffectCode = -1;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].byMastery = 0;
        pOne->m_pUserDB->m_AvatorData.dbSFDelay.MAS[k].dwNextTime = 0;
      }
    }
  }
  if ( CPlayer::IsApplyPcbangPrimium(pOne) )
  {
    if ( v67 || pOne->m_bOper )
      CCouponMgr::LogOut(&pOne->m_kPcBangCoupon, 1);
    else
      CCouponMgr::LogOut(&pOne->m_kPcBangCoupon, 0);
  }
  v24 = CTSingleton<CNationSettingManager>::Instance();
  CNationSettingManager::NetClose(v24, pOne);
  CPlayerDB::InitPlayerDB(&pOne->m_Param, pOne);
  pOne->m_byUserDgr = 0;
  pOne->m_bCorpse = 0;
  pOne->m_bLoad = 0;
  pOne->m_bOper = 0;
  pOne->m_bMapLoading = 0;
  pOne->m_bFullMode = 0;
  pOne->m_pUserDB = 0i64;
  pOne->m_pUsingUnit = 0i64;
  pOne->m_pParkingUnit = 0i64;
  pOne->m_pRecalledAnimusItem = 0i64;
  pOne->m_pRecalledAnimusChar = 0i64;
  pOne->m_dwLastRecallTime = 0;
  pOne->m_byNextRecallReturn = -1;
  pOne->m_bBaseDownload = 1;
  pOne->m_bInvenDownload = 1;
  pOne->m_bForceDownload = 1;
  pOne->m_bCumDownload = 1;
  pOne->m_bSpecialDownload = 1;
  pOne->m_bQuestDownload = 1;
  pOne->m_bLinkBoardDownload = 1;
  pOne->m_bRecvMapChat = 0;
  pOne->m_bSpyGM = 0;
  CSetItemEffect::Init_Info(&pOne->m_clsSetItem);
  pOne->m_id.dwSerial = -1;
  pOne->m_dwObjSerial = -1;
  pOne->m_bTakeGravityStone = 0;
  pOne->m_bBlockGuildBattleMsg = 0;
  pOne->m_bInGuildBattle = 0;
  pOne->m_bNotifyPosition = 0;
  pOne->m_byGuildBattleColorInx = -1;
  pOne->m_bTakeSoccerBall = 0;
  pOne->m_pSoccerItem = 0i64;
  pOne->m_nChaosMode = 0;
  pOne->m_dwChaosModeTime10Per = 0;
  pOne->m_dwChaosModeEndTime = 0;
  pOne->m_bSnowMan = 0;
  pOne->m_bAfterEffect = 0;
}
