/*
 * Function: j_?back@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAAEAPEAVCLogTypeDBTask@@XZ
 * Address: 0x1400022E8
 */

CLogTypeDBTask **__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::back(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::back(this);
}
