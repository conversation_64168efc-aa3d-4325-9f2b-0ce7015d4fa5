/*
 * Function: ?Insert_Guild@CRFWorldDatabase@@QEAA_NPEADE@Z
 * Address: 0x1404989A0
 */

bool __fastcall CRFWorldDatabase::Insert_Guild(CRFWorldDatabase *this, char *pwszGuildName, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-258h]@1
  char Dst; // [sp+30h] [bp-228h]@4
  unsigned __int64 v8; // [sp+240h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+260h] [bp+8h]@1
  char *v10; // [sp+268h] [bp+10h]@1
  char v11; // [sp+270h] [bp+18h]@1

  v11 = byRace;
  v10 = pwszGuildName;
  v9 = this;
  v3 = &v6;
  for ( i = 148i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(&Dst, 0, 0x200ui64);
  sprintf(&Dst, "{ CALL pInsert_Guild('%s', %d) }", v10, (unsigned __int8)v11);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dst, 1);
}
