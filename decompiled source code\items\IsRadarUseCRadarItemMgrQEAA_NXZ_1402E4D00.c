/*
 * Function: ?IsRadarUse@CRadarItemMgr@@QEAA_NXZ
 * Address: 0x1402E4D00
 */

bool __fastcall CRadarItemMgr::IsRadarUse(CRadarItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@4
  DWORD v6; // [sp+24h] [bp-14h]@4
  CRadarItemMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = timeGetTime();
  v6 = v7->m_dwDelayTime + v7->m_dwStartTime;
  if ( v6 || v7->m_bUse )
  {
    if ( v5 >= v6 )
    {
      CRadarItemMgr::Init(v7);
      v7->m_bUpdate = 1;
    }
    result = v7->m_bUse;
  }
  else
  {
    result = v7->m_bUse;
  }
  return result;
}
