#pragma once

/**
 * @file MonsterSetInfoData.h
 * @brief Monster configuration data management class for NexusProtection
 * @details Manages monster settings including drop rates, tolerance, and blocking information
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include <cstdint>
#include <string>
#include <memory>
#include <array>

// Forward declarations
struct _mon_block_info;

namespace NexusProtection {
namespace World {

/**
 * @enum MonsterGrade
 * @brief Monster grade levels for configuration
 */
enum class MonsterGrade : int32_t {
    Grade0 = 0,
    Grade1 = 1,
    Grade2 = 2,
    Grade3 = 3,
    Grade4 = 4,
    Grade5 = 5,
    Grade6 = 6,
    MaxGrades = 7
};

/**
 * @struct MonsterRotationBlock
 * @brief Information about monster rotation blocking
 */
struct MonsterRotationBlock {
    std::string blockName;      ///< Name of the rotation block
    int32_t blockId;           ///< Unique identifier for the block
    bool isActive;             ///< Whether the block is currently active
    
    MonsterRotationBlock() : blockId(0), isActive(false) {}
};

/**
 * @class MonsterSetInfoData
 * @brief Configuration data class for monster settings
 * 
 * This class manages various monster configuration parameters including:
 * - Drop rate calculations based on level differences
 * - Tolerance probability settings by monster grade
 * - Monster force power rates
 * - Rotation blocking information
 * - Loading configuration from files
 */
class MonsterSetInfoData {
public:
    /**
     * @brief Default constructor
     * Initializes all monster configuration data to default values
     */
    MonsterSetInfoData();

    /**
     * @brief Destructor
     * Cleans up allocated resources
     */
    ~MonsterSetInfoData();

    /**
     * @brief Copy constructor (deleted)
     * Monster configuration data should not be copied
     */
    MonsterSetInfoData(const MonsterSetInfoData&) = delete;

    /**
     * @brief Copy assignment operator (deleted)
     * Monster configuration data should not be copied
     */
    MonsterSetInfoData& operator=(const MonsterSetInfoData&) = delete;

    /**
     * @brief Move constructor
     * @param other The MonsterSetInfoData to move from
     */
    MonsterSetInfoData(MonsterSetInfoData&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The MonsterSetInfoData to move from
     * @return Reference to this object
     */
    MonsterSetInfoData& operator=(MonsterSetInfoData&& other) noexcept;

    /**
     * @brief Load monster configuration from file
     * @param fileName Path to the configuration file
     * @return true if loading was successful, false otherwise
     */
    bool Load(const std::string& fileName);

    /**
     * @brief Get monster drop rate based on level difference
     * @param levelDifference Difference between player and monster level
     * @return Drop rate as a percentage (0-100)
     */
    [[nodiscard]] uint32_t GetMonsterDropRate(int32_t levelDifference) const;

    /**
     * @brief Get maximum tolerance probability for a monster grade
     * @param monsterGrade Grade of the monster (0-6)
     * @return Maximum tolerance probability (0.0-1.0)
     */
    [[nodiscard]] float GetMaxToleranceProbMax(int32_t monsterGrade) const;

    /**
     * @brief Get maximum tolerance probability for a monster grade (enum version)
     * @param grade Monster grade enum value
     * @return Maximum tolerance probability (0.0-1.0)
     */
    [[nodiscard]] float GetMaxToleranceProbMax(MonsterGrade grade) const;

    /**
     * @brief Get monster force power rate
     * @return Force power rate multiplier
     */
    [[nodiscard]] float GetMonsterForcePowerRate() const;

    /**
     * @brief Get lost monster target distance
     * @return Distance at which monster loses target
     */
    [[nodiscard]] float GetLostMonsterTargetDistance() const;

    /**
     * @brief Check if rotation blocking is enabled for a specific block
     * @param blockInfo Pointer to monster block information
     * @return true if rotation is blocked, false otherwise
     */
    [[nodiscard]] bool IsRotateBlock(const struct _mon_block_info* blockInfo) const;

    /**
     * @brief Get the number of rotation blocks
     * @return Number of configured rotation blocks
     */
    [[nodiscard]] std::size_t GetRotationBlockCount() const;

    /**
     * @brief Get rotation block by index
     * @param index Index of the rotation block
     * @return Pointer to rotation block, or nullptr if index is invalid
     */
    [[nodiscard]] const MonsterRotationBlock* GetRotationBlock(std::size_t index) const;

    /**
     * @brief Initialize all data to default values
     * Called by constructor and can be used to reset data
     */
    void Init();

    /**
     * @brief Check if configuration data is valid
     * @return true if all data is within valid ranges, false otherwise
     */
    [[nodiscard]] bool IsValid() const;

    /**
     * @brief Get drop rate for level difference (positive levels)
     * @param levelDiff Level difference (1-10+)
     * @return Drop rate percentage
     */
    [[nodiscard]] uint32_t GetDropRateUp(int32_t levelDiff) const;

    /**
     * @brief Get drop rate for level difference (negative levels)
     * @param levelDiff Absolute level difference (1-10+)
     * @return Drop rate percentage
     */
    [[nodiscard]] uint32_t GetDropRateDown(int32_t levelDiff) const;

    /**
     * @brief Get drop rate for same level
     * @return Drop rate percentage for same level monsters
     */
    [[nodiscard]] uint32_t GetDropRateSame() const;

private:
    /// Drop rates for monsters higher level than player (index 0-9 = level diff 1-10, index 10 = 11+)
    std::array<uint32_t, 11> m_iMonsterLootingRateUp;
    
    /// Drop rates for monsters lower level than player (index 0-9 = level diff 1-10, index 10 = 11+)
    std::array<uint32_t, 11> m_iMonsterLootingRateDown;
    
    /// Drop rate for same level monsters
    uint32_t m_iMonsterLootRateSame;
    
    /// Maximum tolerance probabilities by monster grade (0-6)
    std::array<float, static_cast<size_t>(MonsterGrade::MaxGrades)> m_fToleranceProbMax;
    
    /// Monster force power rate multiplier
    float m_fMonsterForcePowerRate;
    
    /// Distance at which monster loses target
    float m_fLostMonsterTargetDistance;
    
    /// Array of rotation block information
    std::unique_ptr<MonsterRotationBlock[]> m_strRotMonBlk_Ar;
    
    /// Number of rotation blocks
    int32_t m_nMonBlkCount;

    /**
     * @brief Validate level difference parameter
     * @param levelDiff Level difference to validate
     * @return Clamped level difference within valid range
     */
    [[nodiscard]] int32_t ValidateLevelDifference(int32_t levelDiff) const;

    /**
     * @brief Validate monster grade parameter
     * @param grade Monster grade to validate
     * @return Clamped grade within valid range (0-6)
     */
    [[nodiscard]] int32_t ValidateMonsterGrade(int32_t grade) const;

    /**
     * @brief Load drop rate configuration from file
     * @param fileName Configuration file path
     * @return true if successful, false otherwise
     */
    bool LoadDropRates(const std::string& fileName);

    /**
     * @brief Load tolerance configuration from file
     * @param fileName Configuration file path
     * @return true if successful, false otherwise
     */
    bool LoadToleranceSettings(const std::string& fileName);

    /**
     * @brief Load rotation block configuration from file
     * @param fileName Configuration file path
     * @return true if successful, false otherwise
     */
    bool LoadRotationBlocks(const std::string& fileName);

    /**
     * @brief Set default values for all configuration parameters
     */
    void SetDefaults();

    /**
     * @brief Cleanup allocated resources
     */
    void Cleanup();
};

} // namespace World
} // namespace NexusProtection
