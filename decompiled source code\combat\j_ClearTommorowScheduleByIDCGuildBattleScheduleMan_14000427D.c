/*
 * Function: j_?ClearTommorowScheduleByID@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAA_NIK@Z
 * Address: 0x14000427D
 */

bool __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::ClearTommorowScheduleByID(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiMapID, unsigned int dwID)
{
  return GUILD_BATTLE::CGuildBattleScheduleManager::ClearTommorowScheduleByID(this, uiMapID, dwID);
}
