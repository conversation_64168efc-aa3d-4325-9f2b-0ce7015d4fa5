/*
 * Function: ?SetMstPt@CPlayer@@QEAAXHM_NH@Z
 * Address: 0x140052C20
 */

void __fastcall CPlayer::SetMstPt(CPlayer *this, int nMstCode, float fVal, bool bAdd, int nWpType)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@8
  float v8; // xmm0_4@8
  float v9; // xmm0_4@8
  float v10; // xmm0_4@8
  float v11; // xmm0_4@8
  float v12; // xmm0_4@13
  float v13; // xmm0_4@13
  float v14; // xmm0_4@13
  float v15; // xmm0_4@13
  float v16; // xmm0_4@13
  float v17; // xmm0_4@17
  float v18; // xmm0_4@17
  float v19; // xmm0_4@20
  float v20; // xmm0_4@20
  __int64 v21; // [sp+0h] [bp-78h]@1
  int v22; // [sp+40h] [bp-38h]@6
  unsigned int dwOldData; // [sp+44h] [bp-34h]@8
  unsigned int dwNewData; // [sp+48h] [bp-30h]@8
  int v25; // [sp+4Ch] [bp-2Ch]@8
  int j; // [sp+50h] [bp-28h]@15
  float v27; // [sp+54h] [bp-24h]@8
  float v28; // [sp+58h] [bp-20h]@8
  float v29; // [sp+5Ch] [bp-1Ch]@13
  float v30; // [sp+60h] [bp-18h]@13
  CPlayer *v31; // [sp+80h] [bp+8h]@1
  int nMstCodea; // [sp+88h] [bp+10h]@1
  float v33; // [sp+90h] [bp+18h]@1

  v33 = fVal;
  nMstCodea = nMstCode;
  v31 = this;
  v5 = &v21;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( !bAdd )
    v33 = fVal * -1.0;
  v22 = _MASTERY_PARAM::GetMasteryPerMast(&v31->m_pmMst, nMstCode, nWpType);
  if ( (float)((float)v22 + v33) > 99.0 )
    v33 = (float)(99 - v22);
  pow(1000.0, 2);
  v27 = FLOAT_1000_0;
  v7 = (float)v22;
  pow((float)v22, 2);
  v8 = v27 + (float)((float)(4.0 * v7) * 1000.0);
  sqrt(v8);
  v9 = (float)(v8 + -1000.0) / 2.0;
  pow(v9, 2);
  dwOldData = (signed int)ffloor(v9);
  pow(1000.0, 2);
  v28 = FLOAT_1000_0;
  v10 = (float)v22 + v33;
  pow(v10, 2);
  v11 = v28 + (float)((float)(4.0 * v10) * 1000.0);
  sqrt(v11);
  pow((float)(v11 + -1000.0) / 2.0, 2);
  dwNewData = (signed int)ffloor((float)(v11 + -1000.0) / 2.0);
  v25 = 0;
  if ( !nMstCodea )
  {
    v25 = CPlayer::Emb_UpdateStat(v31, nWpType, dwNewData, dwOldData);
    _MASTERY_PARAM::UpdateCumPerMast(&v31->m_pmMst, 0, nWpType, v25 + dwNewData);
    CPlayer::SendMsg_StatInform(v31, nWpType, v25 + dwNewData, 0);
    CMgrAvatorItemHistory::mastery_change_jade(
      &CPlayer::s_MgrItemHistory,
      0,
      dwOldData,
      dwNewData,
      v22,
      v33,
      v31->m_szItemHistoryFileName,
      nWpType);
  }
  if ( nMstCodea == 1 )
  {
    v25 = CPlayer::Emb_UpdateStat(v31, 2u, dwNewData, dwOldData);
    _MASTERY_PARAM::UpdateCumPerMast(&v31->m_pmMst, 1, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v31, 2, v25 + dwNewData, 0);
  }
  if ( nMstCodea == 2 )
  {
    pow(100.0, 2);
    v29 = FLOAT_100_0;
    v12 = (float)v22;
    pow((float)v22, 2);
    v13 = v29 + (float)((float)(4.0 * v12) * 100.0);
    sqrt(v13);
    v14 = (float)(v13 + -100.0) / 2.0;
    pow(v14, 2);
    dwOldData = (signed int)ffloor(v14);
    pow(100.0, 2);
    v30 = FLOAT_100_0;
    v15 = (float)v22 + v33;
    pow(v15, 2);
    v16 = v30 + (float)((float)(4.0 * v15) * 100.0);
    sqrt(v16);
    pow((float)(v16 + -100.0) / 2.0, 2);
    dwNewData = (signed int)ffloor((float)(v16 + -100.0) / 2.0);
    v25 = CPlayer::Emb_UpdateStat(v31, 3u, dwNewData, dwOldData);
    _MASTERY_PARAM::UpdateCumPerMast(&v31->m_pmMst, 2, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v31, 3, v25 + dwNewData, 0);
  }
  if ( nMstCodea == 4 )
  {
    for ( j = 0; j < 24; ++j )
    {
      v22 = _MASTERY_PARAM::GetMasteryPerMast(&v31->m_pmMst, 4, j);
      v17 = (float)v22;
      pow((float)v22, 2);
      pow(v17, 2);
      dwOldData = (signed int)ffloor((float)(CalcRoundUp(v17 / 14.0) - 1) + 0.0099999998);
      v18 = (float)v22 + v33;
      pow(v18, 2);
      pow(v18, 2);
      dwNewData = (signed int)ffloor((float)(CalcRoundUp(v18 / 14.0) - 1) + 0.0099999998);
      v25 = CPlayer::Emb_UpdateStat(v31, j + 52, dwNewData, dwOldData);
      _MASTERY_PARAM::UpdateCumPerMast(&v31->m_pmMst, 4, j, dwNewData);
      CPlayer::SendMsg_StatInform(v31, j + 52, v25 + dwNewData, 0);
    }
  }
  if ( nMstCodea == 6 && CPlayerDB::GetRaceCode(&v31->m_Param) != 2 )
  {
    v19 = (float)(v22 - 1) + v33;
    pow(v19, 2);
    dwNewData = (signed int)ffloor(v19 * 15000.0);
    v20 = (float)(v22 - 1);
    pow(v20, 2);
    dwOldData = (signed int)ffloor(v20 * 15000.0);
    v25 = CPlayer::Emb_UpdateStat(v31, 0x4Fu, dwNewData, dwOldData);
    _MASTERY_PARAM::UpdateCumPerMast(&v31->m_pmMst, 6, 0, dwNewData);
    CPlayer::SendMsg_StatInform(v31, 79, v25 + dwNewData, 0);
  }
  CPlayer::ReCalcMaxHFSP(v31, 1, 0);
}
