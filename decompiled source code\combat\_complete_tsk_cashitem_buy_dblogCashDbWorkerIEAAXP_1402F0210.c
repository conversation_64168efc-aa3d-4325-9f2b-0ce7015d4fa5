/*
 * Function: ?_complete_tsk_cashitem_buy_dblog@CashDbWorker@@IEAAXPEAVTask@@@Z
 * Address: 0x1402F0210
 */

void __fastcall CashDbWorker::_complete_tsk_cashitem_buy_dblog(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // edx@7
  unsigned int v5; // er8@7
  __int64 v6; // [sp+0h] [bp-58h]@1
  int v7; // [sp+20h] [bp-38h]@7
  int v8; // [sp+28h] [bp-30h]@7
  int v9; // [sp+30h] [bp-28h]@7
  char *v10; // [sp+40h] [bp-18h]@4
  int j; // [sp+48h] [bp-10h]@4
  unsigned int v12; // [sp+4Ch] [bp-Ch]@7
  CashDbWorker *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = Task::GetTaskBuf(pkTsk);
  for ( j = 0; j < *((_DWORD *)v10 + 8); ++j )
  {
    if ( v10[16 * j + 36] )
    {
      v4 = (unsigned __int8)v10[16 * j + 40];
      v5 = *(_WORD *)&v10[16 * j + 38];
      v12 = (unsigned __int8)v10[16 * j + 37];
      v9 = *(_DWORD *)&v10[16 * j + 48];
      v8 = *(_DWORD *)&v10[16 * j + 44];
      v7 = v4;
      CLogFile::Write(v13->_kLogger, "Failed insert to DB >> tblcode:%d itemidx:%d num:%d cost:%d discount:%d", v12, v5);
    }
  }
}
