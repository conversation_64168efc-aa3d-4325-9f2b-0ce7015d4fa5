/*
 * Function: _CUnmannedTraderGroupIDInfo::GetGroupID_::_1_::dtor$2_0
 * Address: 0x140386B00
 */

void __fastcall CUnmannedTraderGroupIDInfo::GetGroupID_::_1_::dtor_2_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>((std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)(a2 + 184));
}
