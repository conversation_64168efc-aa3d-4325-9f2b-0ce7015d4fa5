/**
 * @file CAutoTradeAuth.cpp
 * @brief Auto Trade Authentication System Implementation
 * 
 * Provides secure authentication for auto trade login operations and management.
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "CAutoTradeAuth.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <fstream>

namespace NexusProtection::Authentication {

    // Global instance
    static std::unique_ptr<CAutoTradeAuth> g_pAutoTradeAuth = nullptr;
    static std::mutex g_instanceMutex;

    // External global buffers (equivalent to original sData and sBuf)
    static char sData[20000] = {0};
    static char sBuf[10240] = {0};

    // StorageListDbCon implementation

    bool StorageListDbCon::IsValid() const {
        return m_byTableCode > 0 && m_wItemIndex > 0 && m_lnUID > 0;
    }

    std::string StorageListDbCon::ToString() const {
        std::ostringstream oss;
        oss << "StorageListDbCon{";
        oss << "TableCode: " << static_cast<int>(m_byTableCode) << ", ";
        oss << "ItemIndex: " << m_wItemIndex << ", ";
        oss << "Dur: " << m_dwDur << ", ";
        oss << "Lv: " << m_dwLv << ", ";
        oss << "UID: " << m_lnUID;
        oss << "}";
        return oss.str();
    }

    // AutoTradeTransaction implementation

    bool AutoTradeTransaction::IsValid() const {
        return registSerial > 0 && !buyerName.empty() && item.IsValid();
    }

    bool AutoTradeTransaction::IsExpired() const {
        auto now = std::chrono::system_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::hours>(now - resultTime);
        return duration.count() > 24; // Expire after 24 hours
    }

    std::string AutoTradeTransaction::ToString() const {
        std::ostringstream oss;
        oss << "AutoTradeTransaction{";
        oss << "RegistSerial: " << registSerial << ", ";
        oss << "Buyer: " << buyerName << " (" << buyerSerial << "), ";
        oss << "Price: " << price << ", ";
        oss << "Tax: " << tax << ", ";
        oss << "Operation: " << AutoTradeOperationToString(operation);
        oss << "}";
        return oss.str();
    }

    std::string AutoTradeTransaction::GetFormattedTime() const {
        auto time_t = std::chrono::system_clock::to_time_t(resultTime);
        std::tm tm_buf;
        #ifdef _WIN32
            localtime_s(&tm_buf, &time_t);
            auto* tm = &tm_buf;
        #else
            auto* tm = std::localtime(&time_t);
        #endif
        
        std::ostringstream oss;
        oss << std::put_time(tm, "%Y-%m-%d %H:%M:%S");
        return oss.str();
    }

    // CAutoTradeLogger implementation

    CAutoTradeLogger::CAutoTradeLogger() = default;

    void CAutoTradeLogger::LogSellTransaction(const AutoTradeTransaction& transaction, const std::string& fileName) {
        std::string logMessage = FormatTransactionLog(transaction, "AUTO TRADE(SELL)");
        WriteLog(logMessage, fileName);
    }

    void CAutoTradeLogger::LogCancelTransaction(const AutoTradeTransaction& transaction, const std::string& fileName) {
        std::string logMessage = FormatTransactionLog(transaction, "TIMEOUT_AUTO_TRADE");
        WriteLog(logMessage, fileName);
    }

    void CAutoTradeLogger::LogBuyTransaction(const AutoTradeTransaction& transaction, const std::string& fileName) {
        std::string logMessage = FormatTransactionLog(transaction, "AUTO TRADE(BUY)");
        WriteLog(logMessage, fileName);
    }

    void CAutoTradeLogger::LogError(const std::string& error, const std::string& fileName) {
        std::ostringstream oss;
        oss << "AUTO TRADE ERROR: " << error;
        WriteLog(oss.str(), fileName);
    }

    void CAutoTradeLogger::WriteLog(const std::string& message, const std::string& fileName) {
        std::lock_guard<std::mutex> lock(m_logMutex);
        
        // Write to global buffer (equivalent to original strcat_s(sData, ...))
        if (strlen(sData) + message.length() < sizeof(sData) - 1) {
            strcat_s(sData, sizeof(sData), message.c_str());
        }
        
        // Also write to console for debugging
        std::cout << "[AUTO_TRADE] " << message << std::endl;
        
        // Write to file if specified
        if (!fileName.empty()) {
            try {
                std::ofstream file(fileName, std::ios::app);
                if (file.is_open()) {
                    file << message;
                    file.close();
                }
            } catch (const std::exception& e) {
                std::cout << "[ERROR] Failed to write to file " << fileName << ": " << e.what() << std::endl;
            }
        }
    }

    std::string CAutoTradeLogger::FormatTransactionLog(const AutoTradeTransaction& transaction, const std::string& operation) {
        std::ostringstream oss;
        
        if (operation == "AUTO TRADE(SELL)") {
            oss << operation << ": login sell selldate(" << transaction.GetFormattedTime() << ") ";
            oss << "reg(" << transaction.registSerial << ") ";
            oss << "buyer(" << transaction.buyerName << ":" << transaction.buyerSerial << " id:" << transaction.buyerID << ") ";
            oss << "recv(D:" << transaction.price << ") tax(" << transaction.tax << ") ";
            oss << "$D:" << transaction.leftDalant << " $G:" << transaction.leftGold << "\r\n";
            
            // Add item information
            oss << "\t- " << GetItemDisplayInfo(transaction.item) << "\r\n";
        } else if (operation == "TIMEOUT_AUTO_TRADE") {
            oss << operation << ": login canceldate(" << transaction.GetFormattedTime() << ") ";
            oss << "reg(" << transaction.registSerial << ") ";
            oss << GetItemDisplayInfo(transaction.item) << "\r\n";
        }
        
        return oss.str();
    }

    std::string CAutoTradeLogger::GetItemDisplayInfo(const StorageListDbCon& item) {
        // Equivalent to DisplayItemUpgInfo and item record formatting
        std::ostringstream oss;
        oss << "Item_" << static_cast<int>(item.m_byTableCode) << "_" << item.m_wItemIndex;
        oss << "_" << item.m_dwDur << "_@Lv" << item.m_dwLv << "[" << item.m_lnUID << "]";
        return oss.str();
    }

    // CMgrAvatorItemHistory implementation

    CMgrAvatorItemHistory::CMgrAvatorItemHistory() {
        m_logger = std::make_unique<CAutoTradeLogger>();
        UpdateCurrentTime();
    }

    bool CMgrAvatorItemHistory::Initialize() {
        if (m_isInitialized) {
            return true;
        }

        try {
            UpdateCurrentTime();
            m_isInitialized = true;
            std::cout << "[INFO] CMgrAvatorItemHistory initialized successfully" << std::endl;
            return true;
        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to initialize CMgrAvatorItemHistory: " << e.what() << std::endl;
            return false;
        }
    }

    void CMgrAvatorItemHistory::Shutdown() {
        if (!m_isInitialized) {
            return;
        }

        m_isInitialized = false;
        std::cout << "[INFO] CMgrAvatorItemHistory shutdown completed" << std::endl;
    }

    void CMgrAvatorItemHistory::AutoTradeLoginSell(const std::string& buyerName, uint32_t buyerSerial,
                                                  const std::string& buyerID, uint32_t registSerial,
                                                  const StorageListDbCon& item, int64_t resultTime,
                                                  uint32_t price, uint32_t tax, uint32_t leftDalant,
                                                  uint32_t leftGold, const std::string& fileName) {
        try {
            // Create transaction object
            AutoTradeTransaction transaction(registSerial, buyerName, buyerSerial, buyerID);
            transaction.item = item;
            transaction.price = price;
            transaction.tax = tax;
            transaction.leftDalant = leftDalant;
            transaction.leftGold = leftGold;
            transaction.operation = AutoTradeOperation::Sell;
            
            // Convert time
            transaction.resultTime = std::chrono::system_clock::from_time_t(static_cast<time_t>(resultTime));
            
            // Update current time
            UpdateCurrentTime();
            
            // Log the transaction
            if (m_logger) {
                m_logger->LogSellTransaction(transaction, fileName);
            }
            
            std::cout << "[INFO] Auto trade sell logged: " << transaction.ToString() << std::endl;
            
        } catch (const std::exception& e) {
            if (m_logger) {
                m_logger->LogError("Auto trade sell error: " + std::string(e.what()), fileName);
            }
        }
    }

    void CMgrAvatorItemHistory::LoginCancelAutoTrade(int sessionId, uint32_t registSerial,
                                                    const StorageListDbCon& regItem, int64_t resultTime,
                                                    const std::string& fileName) {
        try {
            // Create transaction object for cancellation
            AutoTradeTransaction transaction;
            transaction.registSerial = registSerial;
            transaction.item = regItem;
            transaction.operation = AutoTradeOperation::Cancel;
            
            // Convert time
            transaction.resultTime = std::chrono::system_clock::from_time_t(static_cast<time_t>(resultTime));
            
            // Update current time
            UpdateCurrentTime();
            
            // Log the cancellation
            if (m_logger) {
                m_logger->LogCancelTransaction(transaction, fileName);
            }
            
            std::cout << "[INFO] Auto trade cancel logged: " << transaction.ToString() << std::endl;
            
        } catch (const std::exception& e) {
            if (m_logger) {
                m_logger->LogError("Auto trade cancel error: " + std::string(e.what()), fileName);
            }
        }
    }

    void CMgrAvatorItemHistory::UpdateCurrentTime() {
        std::lock_guard<std::mutex> lock(m_timeMutex);
        FormatCurrentDateTime();
    }

    void CMgrAvatorItemHistory::FormatCurrentDateTime() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::tm tm_buf;
        #ifdef _WIN32
            localtime_s(&tm_buf, &time_t);
            auto* tm = &tm_buf;
        #else
            auto* tm = std::localtime(&time_t);
        #endif
        
        // Format date (YYYY-MM-DD)
        std::ostringstream dateOss;
        dateOss << std::put_time(tm, "%Y-%m-%d");
        m_szCurDate = dateOss.str();
        
        // Format time (HH:MM:SS)
        std::ostringstream timeOss;
        timeOss << std::put_time(tm, "%H:%M:%S");
        m_szCurTime = timeOss.str();
    }

    // CAutoTradeAuth implementation

    CAutoTradeAuth::CAutoTradeAuth() {
        m_statistics.startTime = std::chrono::steady_clock::now();
        m_logger = std::make_unique<CAutoTradeLogger>();
    }

    CAutoTradeAuth::~CAutoTradeAuth() {
        Shutdown();
    }

    bool CAutoTradeAuth::Initialize() {
        if (m_isInitialized) {
            return true;
        }

        try {
            // Initialize item history manager
            if (!m_itemHistoryManager.Initialize()) {
                return false;
            }

            // Clear any existing data
            {
                std::lock_guard<std::mutex> lock(m_transactionsMutex);
                m_transactions.clear();
            }

            m_isOperational = true;
            m_isInitialized = true;

            std::cout << "[INFO] CAutoTradeAuth initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to initialize CAutoTradeAuth: " << e.what() << std::endl;
            return false;
        }
    }

    void CAutoTradeAuth::Shutdown() {
        if (!m_isInitialized) {
            return;
        }

        // Shutdown item history manager
        m_itemHistoryManager.Shutdown();

        // Clear all transactions
        {
            std::lock_guard<std::mutex> lock(m_transactionsMutex);
            m_transactions.clear();
        }

        m_isOperational = false;
        m_isInitialized = false;

        std::cout << "[INFO] CAutoTradeAuth shutdown completed" << std::endl;
    }

    bool CAutoTradeAuth::LoadConfiguration() {
        // Load configuration from file or database
        // For now, use default configuration
        return true;
    }

    AutoTradeAuthResult CAutoTradeAuth::AuthenticateAutoTradeSell(const std::string& buyerName, uint32_t buyerSerial,
                                                                 const std::string& buyerID, uint32_t registSerial,
                                                                 const StorageListDbCon& item, int64_t resultTime,
                                                                 uint32_t price, uint32_t tax, uint32_t leftDalant,
                                                                 uint32_t leftGold, const std::string& fileName) {
        try {
            if (!ValidateTradeParameters(buyerName, buyerSerial, buyerID, registSerial)) {
                UpdateStatistics(AutoTradeOperation::Sell, false);
                return AutoTradeAuthResult::InvalidParameters;
            }

            if (!ValidateItemData(item)) {
                UpdateStatistics(AutoTradeOperation::Sell, false);
                return AutoTradeAuthResult::ItemNotFound;
            }

            if (!ValidateFinancialData(price, tax, leftDalant, leftGold)) {
                UpdateStatistics(AutoTradeOperation::Sell, false);
                return AutoTradeAuthResult::InsufficientFunds;
            }

            // Create and register transaction
            AutoTradeTransaction transaction(registSerial, buyerName, buyerSerial, buyerID);
            transaction.item = item;
            transaction.price = price;
            transaction.tax = tax;
            transaction.leftDalant = leftDalant;
            transaction.leftGold = leftGold;
            transaction.operation = AutoTradeOperation::Sell;
            transaction.resultTime = std::chrono::system_clock::from_time_t(static_cast<time_t>(resultTime));

            bool success = ProcessSellTransaction(transaction, fileName);

            UpdateStatistics(AutoTradeOperation::Sell, success);
            LogAuthenticationEvent("Auto trade sell authentication", registSerial, success);

            return success ? AutoTradeAuthResult::Success : AutoTradeAuthResult::SystemError;

        } catch (const std::exception& e) {
            UpdateStatistics(AutoTradeOperation::Sell, false);
            LogAuthenticationEvent("Auto trade sell authentication error: " + std::string(e.what()),
                                 registSerial, false);
            return AutoTradeAuthResult::SystemError;
        }
    }

    AutoTradeAuthResult CAutoTradeAuth::AuthenticateAutoTradeCancel(int sessionId, uint32_t registSerial,
                                                                   const StorageListDbCon& regItem, int64_t resultTime,
                                                                   const std::string& fileName) {
        try {
            if (registSerial == 0 || !regItem.IsValid()) {
                UpdateStatistics(AutoTradeOperation::Cancel, false);
                return AutoTradeAuthResult::InvalidParameters;
            }

            // Check if transaction exists
            AutoTradeTransaction* existingTransaction = GetTransaction(registSerial);
            if (!existingTransaction) {
                UpdateStatistics(AutoTradeOperation::Cancel, false);
                return AutoTradeAuthResult::TradeNotFound;
            }

            // Create cancellation transaction
            AutoTradeTransaction cancelTransaction;
            cancelTransaction.registSerial = registSerial;
            cancelTransaction.item = regItem;
            cancelTransaction.operation = AutoTradeOperation::Cancel;
            cancelTransaction.resultTime = std::chrono::system_clock::from_time_t(static_cast<time_t>(resultTime));

            bool success = ProcessCancelTransaction(registSerial, fileName);

            UpdateStatistics(AutoTradeOperation::Cancel, success);
            LogAuthenticationEvent("Auto trade cancel authentication", registSerial, success);

            return success ? AutoTradeAuthResult::Success : AutoTradeAuthResult::SystemError;

        } catch (const std::exception& e) {
            UpdateStatistics(AutoTradeOperation::Cancel, false);
            LogAuthenticationEvent("Auto trade cancel authentication error: " + std::string(e.what()),
                                 registSerial, false);
            return AutoTradeAuthResult::SystemError;
        }
    }

    bool CAutoTradeAuth::RegisterTransaction(const AutoTradeTransaction& transaction) {
        std::lock_guard<std::mutex> lock(m_transactionsMutex);

        if (m_transactions.size() >= MAX_ACTIVE_TRADES) {
            return false;
        }

        auto newTransaction = std::make_unique<AutoTradeTransaction>(transaction);
        m_transactions[transaction.registSerial] = std::move(newTransaction);

        std::cout << "[INFO] Registered auto trade transaction: " << transaction.registSerial << std::endl;
        return true;
    }

    bool CAutoTradeAuth::CancelTransaction(uint32_t registSerial) {
        std::lock_guard<std::mutex> lock(m_transactionsMutex);

        auto it = m_transactions.find(registSerial);
        if (it != m_transactions.end()) {
            it->second->isActive = false;
            it->second->operation = AutoTradeOperation::Cancel;
            std::cout << "[INFO] Cancelled auto trade transaction: " << registSerial << std::endl;
            return true;
        }

        return false;
    }

    AutoTradeTransaction* CAutoTradeAuth::GetTransaction(uint32_t registSerial) {
        std::lock_guard<std::mutex> lock(m_transactionsMutex);

        auto it = m_transactions.find(registSerial);
        return (it != m_transactions.end()) ? it->second.get() : nullptr;
    }

    std::vector<AutoTradeTransaction> CAutoTradeAuth::GetActiveTransactions() const {
        std::lock_guard<std::mutex> lock(m_transactionsMutex);

        std::vector<AutoTradeTransaction> activeTransactions;
        for (const auto& pair : m_transactions) {
            if (pair.second->isActive) {
                activeTransactions.push_back(*pair.second);
            }
        }

        return activeTransactions;
    }

    bool CAutoTradeAuth::ValidateTradeParameters(const std::string& buyerName, uint32_t buyerSerial,
                                               const std::string& buyerID, uint32_t registSerial) const {
        return !buyerName.empty() && buyerSerial > 0 && !buyerID.empty() && registSerial > 0;
    }

    bool CAutoTradeAuth::ValidateItemData(const StorageListDbCon& item) const {
        return item.IsValid();
    }

    bool CAutoTradeAuth::ValidateFinancialData(uint32_t price, uint32_t tax, uint32_t leftDalant, uint32_t leftGold) const {
        // Basic validation - ensure reasonable values
        return price > 0 && tax <= price && leftDalant >= 0 && leftGold >= 0;
    }

    size_t CAutoTradeAuth::GetActiveTradeCount() const {
        std::lock_guard<std::mutex> lock(m_transactionsMutex);

        size_t count = 0;
        for (const auto& pair : m_transactions) {
            if (pair.second->isActive) {
                count++;
            }
        }

        return count;
    }

    void CAutoTradeAuth::ResetStatistics() {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics = Statistics{};
        m_statistics.startTime = std::chrono::steady_clock::now();
    }

    void CAutoTradeAuth::AutoTradeLoginSell_Legacy(const char* buyerName, uint32_t buyerSerial, const char* buyerID,
                                                  uint32_t registSerial, const StorageListDbCon& item, int64_t resultTime,
                                                  uint32_t price, uint32_t tax, uint32_t leftDalant, uint32_t leftGold,
                                                  const char* fileName) {
        std::string buyerNameStr = buyerName ? buyerName : "";
        std::string buyerIDStr = buyerID ? buyerID : "";
        std::string fileNameStr = fileName ? fileName : "";

        AuthenticateAutoTradeSell(buyerNameStr, buyerSerial, buyerIDStr, registSerial, item, resultTime,
                                 price, tax, leftDalant, leftGold, fileNameStr);
    }

    void CAutoTradeAuth::LoginCancelAutoTrade_Legacy(int sessionId, uint32_t registSerial, const StorageListDbCon& regItem,
                                                    int64_t resultTime, const char* fileName) {
        std::string fileNameStr = fileName ? fileName : "";
        AuthenticateAutoTradeCancel(sessionId, registSerial, regItem, resultTime, fileNameStr);
    }

    // Private method implementations

    bool CAutoTradeAuth::ValidateParameters(const std::string& buyerName, uint32_t buyerSerial,
                                          const std::string& buyerID, uint32_t registSerial) const {
        return ValidateTradeParameters(buyerName, buyerSerial, buyerID, registSerial);
    }

    void CAutoTradeAuth::UpdateStatistics(AutoTradeOperation operation, bool success) {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);

        switch (operation) {
            case AutoTradeOperation::Sell:
                m_statistics.totalSellTransactions++;
                break;
            case AutoTradeOperation::Cancel:
                m_statistics.totalCancelTransactions++;
                break;
            case AutoTradeOperation::Buy:
                m_statistics.totalBuyTransactions++;
                break;
            default:
                break;
        }

        if (success) {
            m_statistics.successfulTransactions++;
        } else {
            m_statistics.failedTransactions++;
        }

        m_statistics.activeTransactions = static_cast<uint32_t>(GetActiveTradeCount());
    }

    void CAutoTradeAuth::LogAuthenticationEvent(const std::string& event, uint32_t registSerial, bool success) {
        if (m_logger) {
            std::ostringstream oss;
            oss << event << " - RegistSerial: " << registSerial
                << " - Result: " << (success ? "SUCCESS" : "FAILED");
            m_logger->WriteLog(oss.str(), "");
        }
    }

    bool CAutoTradeAuth::ProcessSellTransaction(const AutoTradeTransaction& transaction, const std::string& fileName) {
        try {
            // Register the transaction
            if (!RegisterTransaction(transaction)) {
                return false;
            }

            // Log through item history manager
            m_itemHistoryManager.AutoTradeLoginSell(
                transaction.buyerName, transaction.buyerSerial, transaction.buyerID,
                transaction.registSerial, transaction.item,
                std::chrono::system_clock::to_time_t(transaction.resultTime),
                transaction.price, transaction.tax, transaction.leftDalant, transaction.leftGold,
                fileName
            );

            return true;

        } catch (const std::exception& e) {
            if (m_logger) {
                m_logger->LogError("Process sell transaction error: " + std::string(e.what()), fileName);
            }
            return false;
        }
    }

    bool CAutoTradeAuth::ProcessCancelTransaction(uint32_t registSerial, const std::string& fileName) {
        try {
            // Get the transaction to cancel
            AutoTradeTransaction* transaction = GetTransaction(registSerial);
            if (!transaction) {
                return false;
            }

            // Cancel the transaction
            if (!CancelTransaction(registSerial)) {
                return false;
            }

            // Log through item history manager
            m_itemHistoryManager.LoginCancelAutoTrade(
                0, // sessionId not used in original
                registSerial,
                transaction->item,
                std::chrono::system_clock::to_time_t(transaction->resultTime),
                fileName
            );

            return true;

        } catch (const std::exception& e) {
            if (m_logger) {
                m_logger->LogError("Process cancel transaction error: " + std::string(e.what()), fileName);
            }
            return false;
        }
    }

    // Global instance access
    CAutoTradeAuth& GetAutoTradeAuth() {
        std::lock_guard<std::mutex> lock(g_instanceMutex);
        if (!g_pAutoTradeAuth) {
            g_pAutoTradeAuth = std::make_unique<CAutoTradeAuth>();
        }
        return *g_pAutoTradeAuth;
    }

    // Utility functions
    std::string AutoTradeAuthResultToString(AutoTradeAuthResult result) {
        switch (result) {
            case AutoTradeAuthResult::Success: return "Success";
            case AutoTradeAuthResult::InvalidParameters: return "Invalid Parameters";
            case AutoTradeAuthResult::ItemNotFound: return "Item Not Found";
            case AutoTradeAuthResult::TradeNotFound: return "Trade Not Found";
            case AutoTradeAuthResult::BuyerNotFound: return "Buyer Not Found";
            case AutoTradeAuthResult::SellerNotFound: return "Seller Not Found";
            case AutoTradeAuthResult::InsufficientFunds: return "Insufficient Funds";
            case AutoTradeAuthResult::TradeExpired: return "Trade Expired";
            case AutoTradeAuthResult::TradeAlreadyCancelled: return "Trade Already Cancelled";
            case AutoTradeAuthResult::SystemError: return "System Error";
            case AutoTradeAuthResult::NotInitialized: return "Not Initialized";
            default: return "Unknown";
        }
    }

    std::string AutoTradeOperationToString(AutoTradeOperation operation) {
        switch (operation) {
            case AutoTradeOperation::None: return "None";
            case AutoTradeOperation::Sell: return "Sell";
            case AutoTradeOperation::Buy: return "Buy";
            case AutoTradeOperation::Cancel: return "Cancel";
            case AutoTradeOperation::Register: return "Register";
            case AutoTradeOperation::ReRegister: return "Re-Register";
            default: return "Unknown";
        }
    }

    // Legacy C interface implementation
    extern "C" {
        void CMgrAvatorItemHistory_auto_trade_login_sell(CMgrAvatorItemHistory_Legacy* manager,
                                                        const char* buyerName, uint32_t buyerSerial,
                                                        const char* buyerID, uint32_t registSerial,
                                                        _STORAGE_LIST_db_con_Legacy* item, int64_t resultTime,
                                                        uint32_t price, uint32_t tax, uint32_t leftDalant,
                                                        uint32_t leftGold, char* fileName) {
            if (!manager || !item) {
                return;
            }

            auto& auth = GetAutoTradeAuth();

            // Convert legacy item structure
            StorageListDbCon modernItem(item->m_byTableCode, item->m_wItemIndex,
                                       item->m_dwDur, item->m_dwLv, item->m_lnUID);

            auth.AutoTradeLoginSell_Legacy(buyerName, buyerSerial, buyerID, registSerial, modernItem,
                                          resultTime, price, tax, leftDalant, leftGold, fileName);
        }

        void CMgrAvatorItemHistory_login_cancel_auto_trade(CMgrAvatorItemHistory_Legacy* manager,
                                                          int sessionId, uint32_t registSerial,
                                                          _STORAGE_LIST_db_con_Legacy* regItem,
                                                          int64_t resultTime, char* fileName) {
            if (!manager || !regItem) {
                return;
            }

            auto& auth = GetAutoTradeAuth();

            // Convert legacy item structure
            StorageListDbCon modernItem(regItem->m_byTableCode, regItem->m_wItemIndex,
                                       regItem->m_dwDur, regItem->m_dwLv, regItem->m_lnUID);

            auth.LoginCancelAutoTrade_Legacy(sessionId, registSerial, modernItem, resultTime, fileName);
        }

        void j_CMgrAvatorItemHistory_auto_trade_login_sell(CMgrAvatorItemHistory_Legacy* manager,
                                                          const char* buyerName, uint32_t buyerSerial,
                                                          const char* buyerID, uint32_t registSerial,
                                                          _STORAGE_LIST_db_con_Legacy* item, int64_t resultTime,
                                                          uint32_t price, uint32_t tax, uint32_t leftDalant,
                                                          uint32_t leftGold, char* fileName) {
            // Jump table function - calls the main implementation
            CMgrAvatorItemHistory_auto_trade_login_sell(manager, buyerName, buyerSerial, buyerID,
                                                       registSerial, item, resultTime, price, tax,
                                                       leftDalant, leftGold, fileName);
        }

        void j_CMgrAvatorItemHistory_login_cancel_auto_trade(CMgrAvatorItemHistory_Legacy* manager,
                                                            int sessionId, uint32_t registSerial,
                                                            _STORAGE_LIST_db_con_Legacy* regItem,
                                                            int64_t resultTime, char* fileName) {
            // Jump table function - calls the main implementation
            CMgrAvatorItemHistory_login_cancel_auto_trade(manager, sessionId, registSerial,
                                                         regItem, resultTime, fileName);
        }
    }

} // namespace NexusProtection::Authentication
