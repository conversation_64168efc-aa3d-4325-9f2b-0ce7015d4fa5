/*
 * Function: ?SendMsg_GuildBattleSuggestResult@CGuild@@QEAAXEPEAD@Z
 * Address: 0x1402583F0
 */

void __fastcall CGuild::SendMsg_GuildBattleSuggestResult(CGuild *this, char byRet, char *wszDestGuildName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@13
  __int64 v6; // [sp+0h] [bp-98h]@1
  _guild_battle_suggest_request_result_zocl v7; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@7
  char v9; // [sp+65h] [bp-33h]@7
  int j; // [sp+74h] [bp-24h]@7
  _guild_member_info *v11; // [sp+78h] [bp-20h]@10
  unsigned __int64 v12; // [sp+88h] [bp-10h]@4
  CGuild *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  v7.byRet = byRet;
  if ( !byRet && wszDestGuildName )
    strcpy_0(v7.wszDestGuildName, wszDestGuildName);
  pbyType = 27;
  v9 = 53;
  for ( j = 0; j < 50; ++j )
  {
    v11 = &v13->m_MemberData[j];
    if ( _guild_member_info::IsFill(v11) && v11->pPlayer && v11->byClassInGuild == 2 )
    {
      v5 = _guild_battle_suggest_request_result_zocl::size(&v7);
      CNetProcess::LoadSendMsg(unk_1414F2088, v11->pPlayer->m_ObjID.m_wIndex, &pbyType, &v7.byRet, v5);
    }
  }
}
