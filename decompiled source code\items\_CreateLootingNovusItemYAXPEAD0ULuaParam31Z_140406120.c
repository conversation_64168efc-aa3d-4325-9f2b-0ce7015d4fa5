/*
 * Function: ?_CreateLootingNovusItem@@YAXPEAD0ULuaParam3@@1@Z
 * Address: 0x140406120
 */

void __fastcall _CreateLootingNovusItem(char *strItemCode, char *strMapName, LuaParam3 *Pos, LuaParam3 *vParam)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CLuaLootingMgr *v6; // rax@5
  __int64 v7; // [sp+0h] [bp-98h]@1
  float v8; // [sp+58h] [bp-40h]@4
  float v9; // [sp+5Ch] [bp-3Ch]@4
  float v10; // [sp+60h] [bp-38h]@4
  CMapData *pMap; // [sp+78h] [bp-20h]@4
  unsigned __int16 v12; // [sp+80h] [bp-18h]@5
  unsigned int v13; // [sp+84h] [bp-14h]@5
  unsigned int v14; // [sp+88h] [bp-10h]@5
  const char *strItemCodea; // [sp+A0h] [bp+8h]@1
  LuaParam3 *v16; // [sp+B8h] [bp+20h]@1

  v16 = vParam;
  strItemCodea = strItemCode;
  v4 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = (float)Pos->m_1;
  v9 = (float)Pos->m_2;
  v10 = (float)Pos->m_3;
  pMap = CMapOperation::GetMap(&g_MapOper, strMapName);
  if ( pMap )
  {
    v12 = v16->m_1;
    v13 = v16->m_2;
    v14 = v16->m_3;
    v6 = CLuaLootingMgr::Instance();
    CLuaLootingMgr::AddNovusItem(v6, strItemCodea, pMap, 0, &v8, v12, v13, v14, 0);
  }
}
