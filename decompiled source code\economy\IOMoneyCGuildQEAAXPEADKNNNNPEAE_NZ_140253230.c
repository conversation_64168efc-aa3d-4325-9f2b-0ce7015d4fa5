/*
 * Function: ?<PERSON><PERSON><PERSON>@CGuild@@QEAAXPEADKNNNNPEAE_N@Z
 * Address: 0x140253230
 */

void __fastcall CGuild::I<PERSON>oney(CGuild *this, char *pwszIOerName, unsigned int dwIOerSerial, long double dIODalant, long double dIOGold, long double dTotalDalant, long double dTotalGold, char *pbyDate, bool bInPut)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v11; // [sp-20h] [bp-58h]@1
  CGuild *v12; // [sp+40h] [bp+8h]@1
  char *pwszIOerNamea; // [sp+48h] [bp+10h]@1
  unsigned int dwIOerSeriala; // [sp+50h] [bp+18h]@1

  dwIOerSeriala = dwIOerSerial;
  pwszIOerNamea = pwszIOerName;
  v12 = this;
  v9 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v12->m_dTotalDalant = dTotalDalant;
  v12->m_dTotalGold = dTotalGold;
  CGuild::MakeDownMemberPacket(v12);
  CGuild::PushHistory_IOMoney(
    v12,
    bInPut,
    pwszIOerNamea,
    dwIOerSeriala,
    dIODalant,
    dIOGold,
    dTotalDalant,
    dTotalGold,
    pbyDate);
  CGuild::MakeMoneyIOPacket(v12);
}
