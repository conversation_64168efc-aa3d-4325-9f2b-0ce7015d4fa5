/*
 * Function: ?SubProcNotifyForceMoveHQ@CMoveMapLimitInfoPortal@@AEAAXXZ
 * Address: 0x1403A4880
 */

void __fastcall CMoveMapLimitInfoPortal::SubProcNotifyForceMoveHQ(CMoveMapLimitInfoPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-B8h]@1
  int v4; // [sp+30h] [bp-88h]@4
  __int64 v5; // [sp+38h] [bp-80h]@4
  int v6; // [sp+48h] [bp-70h]@4
  char v7; // [sp+4Ch] [bp-6Ch]@4
  char pbyType; // [sp+74h] [bp-44h]@4
  char v9; // [sp+75h] [bp-43h]@4
  _notice_move_limit_map_msg_zocl v10; // [sp+94h] [bp-24h]@4
  unsigned int j; // [sp+A4h] [bp-14h]@4
  CMoveMapLimitInfoPortal *v12; // [sp+C0h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = 0;
  v5 = 0i64;
  v6 = 0;
  memset(&v7, 0, 8ui64);
  pbyType = 59;
  v9 = 1;
  _notice_move_limit_map_msg_zocl::_notice_move_limit_map_msg_zocl(&v10);
  v10.byType = 0;
  for ( j = v12->m_uiProcNotifyInx; j < 0x9E4; ++j )
  {
    if ( *(&g_Player.m_bLive + 50856 * j) )
    {
      if ( v12->m_iMapInx == *(_DWORD *)(*((_QWORD *)&g_Player.m_pCurMap + 6357 * j) + 584i64) )
      {
        CNetProcess::LoadSendMsg(unk_1414F2088, *(&g_Player.m_ObjID.m_wIndex + 25428 * j), &pbyType, &v10.byType, 1u);
        if ( (unsigned int)++v4 >= 0x64 )
        {
          v12->m_uiProcNotifyInx = j + 1;
          break;
        }
      }
    }
  }
  if ( j >= 0x9E4 || v12->m_uiProcNotifyInx >= 0x9E4 )
  {
    v12->m_uiProcNotifyInx = 0;
    CMyTimer::BeginTimer(v12->m_pkNotifyForceMoveHQTimer, 0x2710u);
    v12->m_eNotifyForceMoveHQState = 3;
  }
}
