/*
 * Function: ?TruncatedFinal@?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXPEAE_K@Z
 * Address: 0x140572180
 */

int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::TruncatedFinal(CryptoPP::HashTransformation *a1, void *a2, unsigned __int64 a3)
{
  __int64 v3; // rax@1
  const void *v4; // rax@1
  __int64 v5; // r8@1
  int v6; // eax@7
  const void *v8; // [sp+20h] [bp-38h]@1
  __int64 v9; // [sp+28h] [bp-30h]@1
  int v10; // [sp+30h] [bp-28h]@1
  unsigned int v11; // [sp+34h] [bp-24h]@1
  int v12; // [sp+38h] [bp-20h]@2
  int v13; // [sp+3Ch] [bp-1Ch]@5
  CryptoPP::HashTransformation *v14; // [sp+60h] [bp+8h]@1
  void *v15; // [sp+68h] [bp+10h]@1
  unsigned __int64 v16; // [sp+70h] [bp+18h]@1

  v16 = a3;
  v15 = a2;
  v14 = a1;
  CryptoPP::HashTransformation::ThrowIfInvalidTruncatedSize(a1, a3);
  LODWORD(v3) = ((int (__fastcall *)(CryptoPP::HashTransformation *))v14->vfptr[11].__vecDelDtor)(v14);
  v9 = v3;
  LODWORD(v4) = ((int (__fastcall *)(CryptoPP::HashTransformation *))v14->vfptr[11].Clone)(v14);
  v8 = v4;
  v11 = ((int (__fastcall *)(CryptoPP::HashTransformation *))v14->vfptr[4].__vecDelDtor)(v14);
  v10 = ((int (__fastcall *)(CryptoPP::HashTransformation *))v14->vfptr[9].Clone)(v14);
  LOBYTE(v5) = -128;
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::PadLastBlock(
    v14,
    v11 - 8,
    v5);
  CryptoPP::ConditionalByteReverse<unsigned int>((unsigned int)v10, v9, v9, v11 - 8i64);
  if ( v10 )
    v12 = CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::GetBitCountHi((__int64)v14);
  else
    v12 = CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::GetBitCountLo((__int64)v14);
  *(_DWORD *)(v9 + 4 * (v11 / 4ui64) - 8) = v12;
  if ( v10 )
    v13 = CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::GetBitCountLo((__int64)v14);
  else
    v13 = CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::GetBitCountHi((__int64)v14);
  *(_DWORD *)(v9 + 4 * (v11 / 4ui64) - 4) = v13;
  ((void (__fastcall *)(CryptoPP::HashTransformation *, __int64))v14->vfptr[10].__vecDelDtor)(v14, v9);
  v6 = ((int (__fastcall *)(CryptoPP::HashTransformation *))v14->vfptr[3].Clone)(v14);
  CryptoPP::ConditionalByteReverse<unsigned int>((unsigned int)v10, v8, v8, (unsigned int)v6);
  qmemcpy(v15, v8, v16);
  return ((int (__fastcall *)(CryptoPP::HashTransformation *))v14->vfptr[3].__vecDelDtor)(v14);
}
