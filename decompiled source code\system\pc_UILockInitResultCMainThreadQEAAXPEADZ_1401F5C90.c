/*
 * Function: ?pc_UILockInitResult@CMainThread@@QEAAXPEAD@Z
 * Address: 0x1401F5C90
 */

void __fastcall CMainThread::pc_UILockInitResult(CMainThread *this, char *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char *v5; // [sp+20h] [bp-18h]@4
  CUserDB *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pMsg;
  v6 = &g_UserDB[*(_WORD *)(pMsg + 1)];
  if ( v6->m_bActive )
    CUserDB::UILockInfo_Init(v6, pMsg);
}
