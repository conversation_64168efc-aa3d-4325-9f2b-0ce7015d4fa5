/*
 * Function: j_??$advance@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@_J@std@@YAXAEAV?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@0@_J@Z
 * Address: 0x140001073
 */

void __fastcall std::advance<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,__int64>(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Where, __int64 _Off)
{
  std::advance<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,__int64>(
    _Where,
    _Off);
}
