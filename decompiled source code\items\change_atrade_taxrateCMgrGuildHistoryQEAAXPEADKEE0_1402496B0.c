/*
 * Function: ?change_atrade_taxrate@CMgrGuildHistory@@QEAAXPEADKEE0@Z
 * Address: 0x1402496B0
 */

void __fastcall CMgrGuildHistory::change_atrade_taxrate(CMgrGuildHistory *this, char *pszSugerName, unsigned int dwSugerSerial, char byCurTax, char byNextTax, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned int v9; // [sp+20h] [bp-28h]@4
  int v10; // [sp+28h] [bp-20h]@4
  int v11; // [sp+30h] [bp-18h]@4
  char *v12; // [sp+38h] [bp-10h]@4
  CMgrGuildHistory *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v6 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  sData_2[0] = 0;
  v12 = v13->m_szCurTime;
  v11 = (unsigned __int8)byNextTax;
  v10 = (unsigned __int8)byCurTax;
  v9 = dwSugerSerial;
  sprintf_s(sData_2, 0x2710ui64, "[CHANGE TAXRATE SUGGEST] %s(%u) %d >> %d [%s]\r\n", pszSugerName);
  sData_2[9999] = 0;
  CMgrGuildHistory::WriteFile(v13, pszFileName, sData_2);
}
