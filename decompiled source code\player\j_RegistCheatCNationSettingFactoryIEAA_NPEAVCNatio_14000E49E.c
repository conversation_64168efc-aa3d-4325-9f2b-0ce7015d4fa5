/*
 * Function: j_?RegistCheat@CNationSettingFactory@@IEAA_NPEAVCNationSettingData@@PEBDP6A_NPEAVCPlayer@@@ZHH@Z
 * Address: 0x14000E49E
 */

bool __fastcall CNationSettingFactory::RegistCheat(CNationSettingFactory *this, CNationSettingData *pkData, const char *szCheat, bool (__cdecl *pCheatCommandFn)(CPlayer *), int iUseDegree, int iMgrDegree)
{
  return CNationSettingFactory::RegistCheat(this, pkData, szCheat, pCheatCommandFn, iUseDegree, iMgrDegree);
}
