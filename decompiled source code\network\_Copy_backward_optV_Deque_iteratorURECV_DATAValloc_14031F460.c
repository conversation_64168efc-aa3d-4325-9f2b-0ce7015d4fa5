/*
 * Function: ??$_Copy_backward_opt@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@Urandom_access_iterator_tag@2@@std@@YA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@V10@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14031F460
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Copy_backward_opt<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::random_access_iterator_tag>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_First, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Last, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a6, std::_Range_checked_iterator_tag a7)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v9; // rax@6
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v10; // rax@6
  RECV_DATA *v11; // rax@6
  __int64 v13; // [sp+0h] [bp-68h]@1
  int v14; // [sp+20h] [bp-48h]@4
  char v15; // [sp+28h] [bp-40h]@6
  __int64 v16; // [sp+40h] [bp-28h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v17; // [sp+70h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v18; // [sp+78h] [bp+10h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right; // [sp+80h] [bp+18h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that; // [sp+88h] [bp+20h]@1

  __that = _Dest;
  _Right = _Last;
  v18 = _First;
  v17 = result;
  v7 = &v13;
  for ( i = 22i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v16 = -2i64;
  v14 = 0;
  while ( std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator!=(
            (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v18->_Mycont,
            (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&_Right->_Mycont) )
  {
    v9 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator--(_Right);
    qmemcpy(&v15, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*(v9), 0x18ui64);
    v10 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator--(__that);
    v11 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*(v10);
    qmemcpy(v11, &v15, sizeof(RECV_DATA));
  }
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v17,
    __that);
  v14 |= 1u;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(v18);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(_Right);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(__that);
  return v17;
}
