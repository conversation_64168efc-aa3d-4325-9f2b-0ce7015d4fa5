/*
 * Function: ?SectorDamageProc@CAttack@@IEAAXHHHHHH_N@Z
 * Address: 0x14016CDD0
 */

void __fastcall CAttack::SectorDamageProc(CAttack *this, int nSkillLv, int nAttPower, int nAngle, int nShotNum, int nWeaponRange, int nEffAttPower, bool bUseEffBullet)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  CCharacter **v10; // rcx@7
  _attack_param *v11; // rdx@7
  _attack_param *v12; // r8@7
  CCharacter **v13; // rcx@8
  _attack_param *v14; // rdx@8
  _attack_param *v15; // r8@8
  int v16; // eax@13
  _sec_info *v17; // rax@18
  CCharacter *v18; // rdx@33
  int v19; // eax@33
  CPlayer *v20; // rax@39
  float v21; // xmm0_4@55
  float v22; // xmm1_4@56
  float *v23; // rax@62
  float *v24; // rcx@62
  int v25; // eax@64
  __int64 v26; // [sp+0h] [bp-118h]@1
  CCharacter *pDst; // [sp+20h] [bp-F8h]@7
  bool bBackAttack; // [sp+28h] [bp-F0h]@7
  int v29; // [sp+30h] [bp-E8h]@4
  int v30; // [sp+34h] [bp-E4h]@4
  bool pbInGuildBattle; // [sp+44h] [bp-D4h]@12
  CMapData *v32; // [sp+58h] [bp-C0h]@13
  _pnt_rect pRect; // [sp+68h] [bp-B0h]@13
  int j; // [sp+84h] [bp-94h]@13
  int k; // [sp+88h] [bp-90h]@15
  unsigned int dwSecIndex; // [sp+8Ch] [bp-8Ch]@18
  CObjectList *v37; // [sp+90h] [bp-88h]@18
  CObjectList *v38; // [sp+98h] [bp-80h]@19
  CGameObject *pObject; // [sp+A0h] [bp-78h]@21
  CPlayer *v40; // [sp+A8h] [bp-70h]@35
  CPlayer *v41; // [sp+B0h] [bp-68h]@36
  AutominePersonal *v42; // [sp+B8h] [bp-60h]@39
  CAnimus *v43; // [sp+C0h] [bp-58h]@43
  CPlayer *v44; // [sp+C8h] [bp-50h]@46
  CGameObject *v45; // [sp+D0h] [bp-48h]@48
  float v46; // [sp+D8h] [bp-40h]@55
  int v47; // [sp+DCh] [bp-3Ch]@59
  int v48; // [sp+E0h] [bp-38h]@33
  CGameObjectVtbl *v49; // [sp+E8h] [bp-30h]@33
  float v50; // [sp+F0h] [bp-28h]@56
  float v51; // [sp+F4h] [bp-24h]@57
  float v52; // [sp+F8h] [bp-20h]@57
  float *chkpos; // [sp+100h] [bp-18h]@62
  _attack_param *v54; // [sp+108h] [bp-10h]@64
  CAttack *v55; // [sp+120h] [bp+8h]@1
  int nAttPnt; // [sp+130h] [bp+18h]@1
  int v57; // [sp+138h] [bp+20h]@1

  v57 = nAngle;
  nAttPnt = nAttPower;
  v55 = this;
  v8 = &v26;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v29 = nWeaponRange;
  v30 = nWeaponRange / 4;
  if ( v55->m_pp->pDst )
  {
    v55->m_DamList[0].m_pChar = v55->m_pp->pDst;
    if ( v55->m_pp->bMatchless )
    {
      v55->m_DamList[0].m_nDamage = ((int (__fastcall *)(CCharacter *))v55->m_pp->pDst->vfptr->GetHP)(v55->m_pp->pDst);
    }
    else if ( bUseEffBullet )
    {
      v10 = &v55->m_pp->pDst;
      v11 = v55->m_pp;
      v12 = v55->m_pp;
      bBackAttack = v55->m_pp->bBackAttack;
      pDst = *v10;
      v55->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                      v55->m_pAttChar,
                                      nEffAttPower,
                                      v12->nPart,
                                      v11->nTol,
                                      pDst,
                                      bBackAttack);
    }
    else
    {
      v13 = &v55->m_pp->pDst;
      v14 = v55->m_pp;
      v15 = v55->m_pp;
      bBackAttack = v55->m_pp->bBackAttack;
      pDst = *v13;
      v55->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                      v55->m_pAttChar,
                                      nAttPnt,
                                      v15->nPart,
                                      v14->nTol,
                                      pDst,
                                      bBackAttack);
    }
    v55->m_nDamagedObjNum = 1;
    if ( CGameObject::GetCurSecNum((CGameObject *)&v55->m_pAttChar->vfptr) != -1 )
    {
      pbInGuildBattle = 0;
      if ( nShotNum > 1 )
      {
        v32 = v55->m_pAttChar->m_pCurMap;
        v16 = CGameObject::GetCurSecNum((CGameObject *)&v55->m_pAttChar->vfptr);
        CMapData::GetRectInRadius(v32, &pRect, 1, v16);
        for ( j = pRect.nStarty; ; ++j )
        {
          if ( j > pRect.nEndy )
            return;
          for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
          {
            v17 = CMapData::GetSecInfo(v32);
            dwSecIndex = v17->m_nSecNumW * j + k;
            v37 = CMapData::GetSectorListObj(v32, v55->m_pAttChar->m_wMapLayerIndex, dwSecIndex);
            if ( v37 )
            {
              v38 = (CObjectList *)v37->m_Head.m_pNext;
              while ( 1 )
              {
                while ( 1 )
                {
                  if ( (_object_list_point *)v38 == &v37->m_Tail )
                    goto LABEL_16;
                  pObject = (CGameObject *)v38->vfptr;
                  v38 = (CObjectList *)v38->m_Head.m_pItem;
                  if ( v55->m_nDamagedObjNum >= 30 || v55->m_nDamagedObjNum >= nShotNum )
                    return;
                  if ( !pObject->m_ObjID.m_byKind
                    && pObject != (CGameObject *)v55->m_pAttChar
                    && (CCharacter *)pObject != v55->m_pp->pDst )
                  {
                    if ( pObject->m_bLive )
                    {
                      if ( !pObject->m_bCorpse )
                      {
                        if ( (unsigned __int8)((int (__fastcall *)(CGameObject *, _QWORD))pObject->vfptr->IsBeAttackedAble)(
                                                pObject,
                                                0i64) )
                        {
                          pbInGuildBattle = 0;
                          if ( !CAttack::CheckGuildBattleLimit(v55, pObject, &pbInGuildBattle) )
                          {
                            if ( pbInGuildBattle
                              || (v48 = ((int (__fastcall *)(CGameObject *))pObject->vfptr->GetObjRace)(pObject),
                                  v18 = v55->m_pAttChar,
                                  v49 = v55->m_pAttChar->vfptr,
                                  v19 = ((int (__fastcall *)(CCharacter *))v49->GetObjRace)(v18),
                                  v48 != v19)
                              || v55->m_pAttChar->m_ObjID.m_byID
                              || ((v40 = (CPlayer *)v55->m_pAttChar, pObject->m_ObjID.m_byID)
                               || (v41 = (CPlayer *)pObject, CPlayer::IsPunished((CPlayer *)pObject, 1, 0))
                               || CPlayer::IsChaosMode(v40))
                              && (pObject->m_ObjID.m_byID != 11
                               || (v42 = (AutominePersonal *)pObject,
                                   v20 = AutominePersonal::GetOwner((AutominePersonal *)pObject),
                                   v20 != v40)
                               && CPlayer::IsChaosMode(v40)) )
                            {
                              if ( (unsigned __int8)((int (__fastcall *)(CGameObject *, CCharacter *))pObject->vfptr->IsBeDamagedAble)(
                                                      pObject,
                                                      v55->m_pAttChar) )
                              {
                                if ( v55->m_pAttChar->m_ObjID.m_byID
                                  || (v43 = CPlayer::GetRecallAnimus((CPlayer *)v55->m_pAttChar)) == 0i64
                                  || (CGameObject *)v43 != pObject )
                                {
                                  if ( v55->m_pAttChar->m_ObjID.m_byID
                                    || (v44 = (CPlayer *)v55->m_pAttChar,
                                        ((int (__fastcall *)(CPlayer *))v44->vfptr->GetObjRace)(v44) != 1)
                                    || CPlayer::IsChaosMode(v44)
                                    || (v45 = pObject) == 0i64 )
                                  {
                                    if ( pObject->m_ObjID.m_byID != 7
                                      && ((unsigned __int8)((int (__fastcall *)(CCharacter *))v55->m_pAttChar->vfptr->IsAttackableInTown)(v55->m_pAttChar)
                                       || (unsigned __int8)((int (__fastcall *)(CGameObject *))pObject->vfptr->IsAttackableInTown)(pObject)
                                       || !(unsigned __int8)((int (__fastcall *)(CCharacter *))v55->m_pAttChar->vfptr->IsInTown)(v55->m_pAttChar)
                                       && !(unsigned __int8)((int (__fastcall *)(CGameObject *))pObject->vfptr->IsInTown)(pObject)) )
                                    {
                                      v21 = v55->m_pAttChar->m_fCurPos[1] - pObject->m_fCurPos[1];
                                      abs(v21);
                                      v46 = v21;
                                      if ( v21 <= 350.0 )
                                      {
                                        GetSqrt(v55->m_pAttChar->m_fCurPos, pObject->m_fCurPos);
                                        v50 = v21;
                                        ((void (__fastcall *)(CGameObject *))pObject->vfptr->GetWidth)(pObject);
                                        v22 = v50 - v21;
                                        if ( (float)(v50 - v21) <= 0.0 )
                                        {
                                          v52 = 0.0;
                                        }
                                        else
                                        {
                                          GetSqrt(v55->m_pAttChar->m_fCurPos, pObject->m_fCurPos);
                                          v51 = v22;
                                          ((void (__fastcall *)(CGameObject *))pObject->vfptr->GetWidth)(pObject);
                                          v52 = v51 - v22;
                                        }
                                        v47 = (signed int)ffloor(v52);
                                        if ( v47 <= v29 && v47 >= v30 )
                                        {
                                          v23 = v55->m_pp->pDst->m_fCurPos;
                                          v24 = v55->m_pAttChar->m_fCurPos;
                                          chkpos = pObject->m_fCurPos;
                                          if ( CAttack::IsCharInSector(
                                                 pObject->m_fCurPos,
                                                 v24,
                                                 v23,
                                                 (float)v57,
                                                 (float)v29) )
                                          {
                                            break;
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
                v55->m_DamList[v55->m_nDamagedObjNum].m_pChar = (CCharacter *)pObject;
                if ( v55->m_pp->bMatchless )
                  break;
                v54 = v55->m_pp;
                v25 = CCharacter::GetAttackRandomPart((CCharacter *)pObject);
                bBackAttack = 0;
                pDst = (CCharacter *)pObject;
                v55->m_DamList[v55->m_nDamagedObjNum].m_nDamage = CCharacter::GetAttackDamPoint(
                                                                    v55->m_pAttChar,
                                                                    nAttPnt,
                                                                    v25,
                                                                    v54->nTol,
                                                                    (CCharacter *)pObject,
                                                                    0);
                if ( v55->m_DamList[v55->m_nDamagedObjNum].m_nDamage != -2 )
                {
                  v55->m_DamList[v55->m_nDamagedObjNum].m_nDamage = (signed int)ffloor((float)v55->m_DamList[v55->m_nDamagedObjNum].m_nDamage * (float)((float)(v29 - v47) / (float)v29));
                  if ( v55->m_DamList[v55->m_nDamagedObjNum].m_nDamage < 1 )
                    continue;
                }
LABEL_68:
                ++v55->m_nDamagedObjNum;
              }
              v55->m_DamList[v55->m_nDamagedObjNum].m_nDamage = ((int (__fastcall *)(CGameObject *))pObject->vfptr->GetHP)(pObject);
              goto LABEL_68;
            }
LABEL_16:
            ;
          }
        }
      }
    }
  }
}
