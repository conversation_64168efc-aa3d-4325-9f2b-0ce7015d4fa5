/*
 * Function: ?Init@CRealMoveRequestDelayChe<PERSON>@@QEAA_NK@Z
 * Address: 0x1401220E0
 */

char __fastcall CRealMoveRequestDelayChecker::Init(CRealMoveRequestDelayChecker *this, unsigned int dwListCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int dwIndex; // [sp+20h] [bp-18h]@6
  unsigned int _Val; // [sp+24h] [bp-14h]@6
  CRealMoveRequestDelayChecker *v8; // [sp+40h] [bp+8h]@1
  unsigned int _Count; // [sp+48h] [bp+10h]@1

  _Count = dwListCnt;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwListCnt )
  {
    _Val = 0;
    std::vector<unsigned long,std::allocator<unsigned long>>::assign(&v8->m_vecDelayList, dwListCnt, &_Val);
    CIndexList::SetList(&v8->m_kNodeInxOrderList, _Count, 0, 0);
    for ( dwIndex = 0; dwIndex < _Count; ++dwIndex )
      CIndexList::PushNode_Back(&v8->m_kNodeInxOrderList, dwIndex, 0i64);
    result = 1;
  }
  else
  {
    CLogFile::Write(&stru_1799C8F30, "CRealMoveRequestDelayChecker::init() iListCnt(0) Fail!");
    result = 0;
  }
  return result;
}
