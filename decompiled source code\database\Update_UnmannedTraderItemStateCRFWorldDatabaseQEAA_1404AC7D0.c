/*
 * Function: ?Update_UnmannedTraderItemState@CRFWorldDatabase@@QEAA_NEKEAEBU_SYSTEMTIME@@@Z
 * Address: 0x1404AC7D0
 */

bool __fastcall CRFWorldDatabase::Update_UnmannedTraderItemState(CRFWorldDatabase *this, char byType, unsigned int dwItemSerial, char byState, _SYSTEMTIME *kCurTime)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // ecx@4
  int v8; // edx@4
  int v9; // er8@4
  int v10; // er9@4
  int v11; // er10@4
  int v12; // er11@4
  __int64 v14; // [sp+0h] [bp-4A8h]@1
  int v15; // [sp+20h] [bp-488h]@4
  int v16; // [sp+28h] [bp-480h]@4
  int v17; // [sp+30h] [bp-478h]@4
  int v18; // [sp+38h] [bp-470h]@4
  int v19; // [sp+40h] [bp-468h]@4
  int v20; // [sp+48h] [bp-460h]@4
  int v21; // [sp+50h] [bp-458h]@4
  int v22; // [sp+58h] [bp-450h]@4
  char Dest; // [sp+70h] [bp-438h]@4
  unsigned __int64 v24; // [sp+480h] [bp-28h]@4
  CRFWorldDatabase *v25; // [sp+4B0h] [bp+8h]@1
  char v26; // [sp+4B8h] [bp+10h]@1
  unsigned int v27; // [sp+4C0h] [bp+18h]@1
  char v28; // [sp+4C8h] [bp+20h]@1

  v28 = byState;
  v27 = dwItemSerial;
  v26 = byType;
  v25 = this;
  v5 = &v14;
  for ( i = 294i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v24 = (unsigned __int64)&v14 ^ _security_cookie;
  v7 = kCurTime->wSecond;
  v8 = kCurTime->wMinute;
  v9 = kCurTime->wHour;
  v10 = kCurTime->wDay;
  v11 = kCurTime->wMonth;
  v12 = kCurTime->wYear;
  v22 = kCurTime->wMilliseconds;
  v21 = v7;
  v20 = v8;
  v19 = v9;
  v18 = v10;
  v17 = v11;
  v16 = v12;
  v15 = (unsigned __int8)v28;
  sprintf(
    &Dest,
    "{ CALL pUpdate_utitemstate( %u, %d, %u,'%04d-%02d-%02d %02d:%02d:%02d.%03d' ) }",
    (unsigned __int8)v26,
    v27);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v25->vfptr, &Dest, 1);
}
