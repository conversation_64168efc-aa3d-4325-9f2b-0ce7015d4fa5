/*
 * Function: _std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::_Insert_n_::_1_::dtor$1
 * Address: 0x140399280
 */

void __fastcall std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::_Insert_n_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CUnmannedTraderGroupDivisionVersionInfo::~CUnmannedTraderGroupDivisionVersionInfo((CUnmannedTraderGroupDivisionVersionInfo *)(a2 + 40));
}
