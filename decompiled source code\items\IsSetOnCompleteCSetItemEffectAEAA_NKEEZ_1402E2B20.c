/*
 * Function: ?IsSetOnComplete@CSetItemEffect@@AEAA_NKEE@Z
 * Address: 0x1402E2B20
 */

char __fastcall CSetItemEffect::IsSetOnComplete(CSetItemEffect *this, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum)
{
  int *v4; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CSetItemEffect *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v4 = &j;
  for ( i = 4i64; i; --i )
  {
    *v4 = -858993460;
    ++v4;
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( v8->m_setCount[j].m_bCheckSetEffect
      && v8->m_setCount[j].m_dwSetItem == dwSetItem
      && v8->m_setCount[j].m_bySetItemNum == (unsigned __int8)bySetItemNum
      && v8->m_setCount[j].m_bySetEffectNum == (unsigned __int8)bySetEffectNum )
    {
      return 1;
    }
  }
  return 0;
}
