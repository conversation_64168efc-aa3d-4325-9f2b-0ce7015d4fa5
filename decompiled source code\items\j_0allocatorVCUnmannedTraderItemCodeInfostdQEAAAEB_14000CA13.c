/*
 * Function: j_??0?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@QEAA@AEBV01@@Z
 * Address: 0x14000CA13
 */

void __fastcall std::allocator<CUnmannedTraderItemCodeInfo>::allocator<CUnmannedTraderItemCodeInfo>(std::allocator<CUnmannedTraderItemCodeInfo> *this, std::allocator<CUnmannedTraderItemCodeInfo> *__formal)
{
  std::allocator<CUnmannedTraderItemCodeInfo>::allocator<CUnmannedTraderItemCodeInfo>(this, __formal);
}
