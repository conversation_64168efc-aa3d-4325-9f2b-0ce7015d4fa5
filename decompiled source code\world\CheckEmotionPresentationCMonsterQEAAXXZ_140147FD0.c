/*
 * Function: ?CheckEmotionPresentation@CMonster@@QEAAXXZ
 * Address: 0x140147FD0
 */

void __fastcall CMonster::CheckEmotionPresentation(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int nSendTargetIndex; // [sp+20h] [bp-28h]@7
  int v5; // [sp+30h] [bp-18h]@5
  CMonster *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_EmotionPresentationCheck.m_bIsSet )
  {
    v5 = -1;
    if ( v6->m_EmotionPresentationCheck.m_pTarget )
      v5 = v6->m_EmotionPresentationCheck.m_pTarget->m_ObjID.m_wIndex;
    nSendTargetIndex = v5;
    CMonster::SendMsg_Emotion_Presentation(
      v6,
      v6->m_EmotionPresentationCheck.m_byType,
      v6->m_EmotionPresentationCheck.m_wIndex,
      v6->m_EmotionPresentationCheck.m_wRandIndex,
      v5);
    EmotionPresentationChecker::ReSet(&v6->m_EmotionPresentationCheck);
  }
}
