#pragma once

#include "AuthenticationTypes.h"
#include <memory>
#include <mutex>
#include <queue>
#include <thread>
#include <condition_variable>
#include <fstream>
#include <atomic>

namespace NexusProtection::Authentication {

    /**
     * @brief Log file management class
     */
    class CLogFile {
    public:
        CLogFile();
        ~CLogFile();

        bool SetWriteLogFile(const std::string& filePath, bool append = true, 
                           bool autoFlush = false, bool timestamped = true, bool enabled = true);
        void Write(const std::string& message);
        void Write(const char* format, ...);
        void Flush();
        void Close();

        bool IsOpen() const noexcept { return m_isOpen; }
        const std::string& GetFilePath() const noexcept { return m_filePath; }

    private:
        std::ofstream m_file;
        std::string m_filePath;
        bool m_isOpen{false};
        bool m_autoFlush{false};
        bool m_timestamped{true};
        bool m_enabled{true};
        mutable std::mutex m_fileMutex;

        std::string FormatTimestamp() const;
    };

    /**
     * @brief Asynchronous logging buffer list
     */
    class CAsyncLogBufferList {
    public:
        CAsyncLogBufferList();
        ~CAsyncLogBufferList();

        void AddEntry(const LogEntry& entry);
        bool GetNextEntry(LogEntry& entry);
        void Clear();
        size_t GetSize() const;
        bool IsEmpty() const;

    private:
        std::queue<LogEntry> m_buffer;
        mutable std::mutex m_bufferMutex;
        std::condition_variable m_bufferCondition;
        static constexpr size_t MAX_BUFFER_SIZE = 10000;
    };

    /**
     * @brief Asynchronous Log Information Manager
     * 
     * Manages asynchronous logging operations with buffering and file management.
     * Refactored from decompiled C source to modern C++20.
     */
    class CAsyncLogInfo {
    public:
        // Constructor and destructor
        CAsyncLogInfo();
        ~CAsyncLogInfo();

        // Core lifecycle
        bool Initialize(LogType logType, const std::string& dirPath, const std::string& typeName,
                       bool addDateFileName = true, uint32_t updateFileNameDelay = 0xFFFFFFFF,
                       CLogFile* loadingLog = nullptr);
        void Shutdown();

        // Logging operations
        void WriteLog(const std::string& message);
        void WriteLog(LogType type, const std::string& message);
        void WriteLogFormatted(const char* format, ...);
        void FlushLogs();

        // File management
        void UpdateLogFileName();
        bool SetLogDirectory(const std::string& dirPath);
        bool SetTypeName(const std::string& typeName);

        // Statistics and information
        uint32_t GetCount() const noexcept { return m_logCount; }
        void IncreaseCount() noexcept { ++m_logCount; }
        const std::string& GetDirPath() const noexcept { return m_dirPath; }
        const std::string& GetFileName() const noexcept { return m_fileName; }
        const std::string& GetTypeName() const noexcept { return m_typeName; }

        // State management
        bool IsInitialized() const noexcept { return m_isInitialized; }
        bool IsActive() const noexcept { return m_isActive; }
        void SetActive(bool active) noexcept { m_isActive = active; }

        // Buffer management
        void ProcessLogBuffer();
        size_t GetBufferSize() const;
        void ClearBuffer();

    private:
        // Internal methods
        bool CreateLogDirectory();
        std::string GenerateFileName() const;
        std::string FormatLogMessage(const std::string& message) const;
        void StartAsyncProcessing();
        void StopAsyncProcessing();
        void AsyncProcessingLoop();

        // Member variables
        LogType m_logType{LogType::System};
        std::string m_dirPath;
        std::string m_typeName;
        std::string m_fileName;
        bool m_addDateFileName{true};
        uint32_t m_updateFileNameDelay{0xFFFFFFFF};

        std::unique_ptr<CLogFile> m_logFile;
        std::unique_ptr<CAsyncLogBufferList> m_bufferList;
        CLogFile* m_loadingLog{nullptr};

        std::atomic<uint32_t> m_logCount{0};
        bool m_isInitialized{false};
        bool m_isActive{true};

        // Async processing
        std::unique_ptr<std::thread> m_processingThread;
        std::atomic<bool> m_shouldStop{false};
        std::condition_variable m_processingCondition;
        mutable std::mutex m_processingMutex;

        // File name update timing
        std::chrono::steady_clock::time_point m_lastFileNameUpdate;
        mutable std::mutex m_fileNameMutex;
    };

    /**
     * @brief Asynchronous Logger Manager
     * 
     * Manages multiple async log info instances and coordinates logging operations.
     */
    class CAsyncLogger {
    public:
        CAsyncLogger();
        ~CAsyncLogger();

        // Core lifecycle
        bool Initialize();
        void Shutdown();

        // Log management
        bool RegisterLog(LogType logType, const std::string& dirPath, const std::string& typeName,
                        bool addDateFileName = true, uint32_t updateFileNameDelay = 0xFFFFFFFF);
        bool UnregisterLog(LogType logType);
        CAsyncLogInfo* GetLogInfo(LogType logType);

        // Logging operations
        void WriteLog(LogType logType, const std::string& message);
        void WriteSystemLog(const std::string& message);
        void WriteAuthLog(const std::string& message);
        void WriteBillingLog(const std::string& message);
        void WriteSecurityLog(const std::string& message);

        // State management
        bool IsInitialized() const noexcept { return m_isInitialized; }
        size_t GetLogCount() const;

    private:
        // Internal methods
        bool InitializeSystemLog();
        void CleanupLogs();

        // Member variables
        std::unordered_map<LogType, std::unique_ptr<CAsyncLogInfo>> m_logInfoMap;
        CAsyncLogInfo* m_systemLogInfo{nullptr};
        CLogFile m_loadingLog;

        bool m_isInitialized{false};
        mutable std::mutex m_logMapMutex;

        // Buffer management
        std::array<std::unique_ptr<CAsyncLogBufferList>, static_cast<size_t>(LogType::Count)> m_bufferLists;
    };

    // Legacy C interface
    extern "C" {
        enum ASYNC_LOG_TYPE {
            ALT_ASYNC_LOGGER_SYSTEM_LOG = 0,
            ALT_ASYNC_LOGGER_AUTH_LOG = 1,
            ALT_ASYNC_LOGGER_BILLING_LOG = 2,
            ALT_ASYNC_LOGGER_SECURITY_LOG = 3,
            ALT_ASYNC_LOGGER_ERROR_LOG = 4,
            ALT_ASYNC_LOGGER_DEBUG_LOG = 5,
            ALT_MAX_LOG_TYPES = 15
        };

        struct CAsyncLogInfo_Legacy {
            ASYNC_LOG_TYPE m_eType;
            char m_szDirPath[256];
            char m_szTypeName[64];
            char m_szFileName[256];
            bool m_bAddDateFileName;
            uint32_t m_dwUpdateFileNameDelay;
            uint32_t m_dwCount;
            CLogFile* m_pLogFile;
            void* m_pBufferList;
        };

        // Legacy function declarations
        CAsyncLogInfo_Legacy* CAsyncLogInfo_Create();
        void CAsyncLogInfo_Destroy(CAsyncLogInfo_Legacy* logInfo);
        bool CAsyncLogInfo_Init(CAsyncLogInfo_Legacy* logInfo, ASYNC_LOG_TYPE eType,
                               const char* szDirPath, const char* szTypeName,
                               bool bAddDateFileName, uint32_t dwUpdateFileNameDelay,
                               CLogFile* logLoading);
        void CAsyncLogInfo_WriteLog(CAsyncLogInfo_Legacy* logInfo, const char* message);
        uint32_t CAsyncLogInfo_GetCount(CAsyncLogInfo_Legacy* logInfo);
        void CAsyncLogInfo_IncreaseCount(CAsyncLogInfo_Legacy* logInfo);
        void CAsyncLogInfo_UpdateLogFileName(CAsyncLogInfo_Legacy* logInfo);
    }

    // Global instance access
    CAsyncLogger& GetAsyncLogger();

} // namespace NexusProtection::Authentication

// Global legacy compatibility
extern NexusProtection::Authentication::CAsyncLogger* g_pAsyncLogger;
