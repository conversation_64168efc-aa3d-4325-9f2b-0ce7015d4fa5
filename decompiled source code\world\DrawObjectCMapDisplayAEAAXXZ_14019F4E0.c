/*
 * Function: ?DrawObject@CMapDisplay@@AEAAXXZ
 * Address: 0x14019F4E0
 */

void __fastcall CMapDisplay::DrawObject(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  CGameObject *pObj; // [sp+28h] [bp-20h]@7
  char *v6; // [sp+30h] [bp-18h]@11
  CSurface *pSF; // [sp+38h] [bp-10h]@11
  CMapDisplay *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < CGameObject::s_nTotalObjectNum; ++j )
  {
    pObj = (CGameObject *)CGameObject::s_pTotalObject[j];
    if ( pObj->m_bLive && pObj->m_pCurMap == v8->m_pActMap && pObj->m_wMapLayerIndex == v8->m_wLayerIndex )
    {
      v6 = &pObj->m_ObjID.m_byKind;
      pSF = v8->m_pSFObj[pObj->m_ObjID.m_byKind][pObj->m_ObjID.m_byID];
      if ( pObj->m_bCorpse )
        pSF = v8->m_pSFCorpse;
      CMapDisplay::_DrawObject(v8, pObj, pSF);
    }
  }
  if ( CGameObject::s_pSelectObject )
  {
    if ( CGameObject::s_pSelectObject->m_pCurMap == v8->m_pActMap
      && CGameObject::s_pSelectObject->m_wMapLayerIndex == v8->m_wLayerIndex )
    {
      CMapDisplay::_DrawObject(v8, CGameObject::s_pSelectObject, v8->m_pSFSelect);
      if ( !CGameObject::s_pSelectObject->m_ObjID.m_byKind && CGameObject::s_pSelectObject->m_ObjID.m_byID == 1 )
        CMapDisplay::DrawSelectMonsterLookAtPos(v8, (CMonster *)CGameObject::s_pSelectObject);
    }
  }
}
