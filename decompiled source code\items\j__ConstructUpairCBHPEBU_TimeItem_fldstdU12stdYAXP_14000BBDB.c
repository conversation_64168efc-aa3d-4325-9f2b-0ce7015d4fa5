/*
 * Function: j_??$_Construct@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@U12@@std@@YAXPEAU?$pair@$$CBHPEBU_TimeItem_fld@@@0@AEBU10@@Z
 * Address: 0x14000BBDB
 */

void __fastcall std::_Construct<std::pair<int const,_TimeItem_fld const *>,std::pair<int const,_TimeItem_fld const *>>(std::pair<int const ,_TimeItem_fld const *> *_Ptr, std::pair<int const ,_TimeItem_fld const *> *_Val)
{
  std::_Construct<std::pair<int const,_TimeItem_fld const *>,std::pair<int const,_TimeItem_fld const *>>(_Ptr, _Val);
}
