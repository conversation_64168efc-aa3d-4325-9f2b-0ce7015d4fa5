/*
 * Function: ?SendInfo@CPossibleBattleGuildListManager@GUILD_BATTLE@@AEAAEHEEK@Z
 * Address: 0x1403CA110
 */

char __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::SendInfo(GUILD_BATTLE::CPossibleBattleGuildListManager *this, int n, char byRace, char byPage, unsigned int dwVer)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v8; // ax@14
  __int64 v9; // [sp+0h] [bp-58h]@1
  char pbyType; // [sp+34h] [bp-24h]@14
  char v11; // [sp+35h] [bp-23h]@14
  GUILD_BATTLE::CPossibleBattleGuildListManager *v12; // [sp+60h] [bp+8h]@1
  int dwClientIndex; // [sp+68h] [bp+10h]@1
  char v14; // [sp+70h] [bp+18h]@1
  char v15; // [sp+78h] [bp+20h]@1

  v15 = byPage;
  v14 = byRace;
  dwClientIndex = n;
  v12 = this;
  v5 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v12->m_bInit )
  {
    if ( (signed int)(unsigned __int8)byRace <= 3 )
    {
      if ( v12->m_pMaxPage[(unsigned __int8)byRace] )
      {
        if ( v12->m_pMaxPage[(unsigned __int8)byRace] >= (signed int)(unsigned __int8)byPage )
        {
          if ( v12->m_pdwVer[(unsigned __int8)byRace] == dwVer )
          {
            result = 1;
          }
          else
          {
            pbyType = 27;
            v11 = 49;
            v8 = _possible_battle_guild_list_result_zocl::size(&v12->m_ppkList[(unsigned __int8)byRace][(unsigned __int8)byPage]);
            CNetProcess::LoadSendMsg(
              unk_1414F2088,
              dwClientIndex,
              &pbyType,
              &v12->m_ppkList[(unsigned __int8)v14][(unsigned __int8)v15].byPage,
              v8);
            result = 0;
          }
        }
        else
        {
          result = -113;
        }
      }
      else
      {
        result = -109;
      }
    }
    else
    {
      result = -113;
    }
  }
  else
  {
    result = 110;
  }
  return result;
}
