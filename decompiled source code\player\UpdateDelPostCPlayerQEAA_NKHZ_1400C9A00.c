/*
 * Function: ?UpdateDelPost@CPlayer@@QEAA_NKH@Z
 * Address: 0x1400C9A00
 */

bool __fastcall CPlayer::UpdateDelPost(CPlayer *this, unsigned int dwPostSerial, int nIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return CUserDB::Update_DelPost(v7->m_pUserDB, dwPostSerial, nIndex);
}
