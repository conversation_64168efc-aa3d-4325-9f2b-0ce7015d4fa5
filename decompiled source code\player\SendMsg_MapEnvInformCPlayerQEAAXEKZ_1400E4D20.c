/*
 * Function: ?SendMsg_MapEnvInform@CPlayer@@QEAAXEK@Z
 * Address: 0x1400E4D20
 */

void __fastcall CPlayer::SendMsg_MapEnvInform(CPlayer *this, char byMapCode, unsigned int dwMapEnvCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-E8h]@1
  char szMsg; // [sp+38h] [bp-B0h]@4
  unsigned int v7; // [sp+39h] [bp-AFh]@4
  int v8; // [sp+3Dh] [bp-ABh]@4
  __int64 _Time; // [sp+68h] [bp-80h]@4
  void *Src; // [sp+78h] [bp-70h]@4
  int Dst; // [sp+88h] [bp-60h]@4
  int v12; // [sp+8Ch] [bp-5Ch]@4
  int v13; // [sp+90h] [bp-58h]@4
  char pbyType; // [sp+C4h] [bp-24h]@4
  char v15; // [sp+C5h] [bp-23h]@4
  CPlayer *v16; // [sp+F0h] [bp+8h]@1

  v16 = this;
  v3 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byMapCode;
  v7 = dwMapEnvCode;
  time_1(&_Time);
  Src = localtime(&_Time);
  memcpy_0(&Dst, Src, 0x24ui64);
  v13 = g_WorldSch.m_nCurHour;
  v12 = g_WorldSch.m_nCurMin;
  Dst = g_WorldSch.m_nCurMilSec / 1000;
  v8 = mktime((tm *)&Dst);
  pbyType = 8;
  v15 = 5;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
