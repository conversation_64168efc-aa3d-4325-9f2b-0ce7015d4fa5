/*
 * Function: ?Insert_RenamePotionLog@CRFWorldDatabase@@QEAA_NKPEAD0@Z
 * Address: 0x1404C5DD0
 */

bool __fastcall CRFWorldDatabase::Insert_RenamePotionLog(CRFWorldDatabase *this, unsigned int dwSerial, char *wszOldName, char *wszNewName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-468h]@1
  char *v8; // [sp+20h] [bp-448h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  unsigned __int64 v10; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+470h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = wszNewName;
  sprintf(&Dest, "{ CALL pInsert_RenamePotionLog( %d, '%s', '%s' ) }", dwSerial, wszOldName);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1);
}
