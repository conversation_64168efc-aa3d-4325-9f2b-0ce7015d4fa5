/*
 * Function: ?CompleteUpdateCheatRegistTime@CUnmannedTraderUserInfoTable@@QEAAXPEAD@Z
 * Address: 0x1403647F0
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteUpdateCheatRegistTime(CUnmannedTraderUserInfoTable *this, char *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char *v5; // [sp+20h] [bp-28h]@4
  CUnmannedTraderUserInfo *v6; // [sp+28h] [bp-20h]@4
  CPlayer *v7; // [sp+30h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v8; // [sp+50h] [bp+8h]@1
  char *pLoadDataa; // [sp+58h] [bp+10h]@1

  pLoadDataa = pLoadData;
  v8 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pLoadData;
  v6 = CUnmannedTraderUserInfoTable::FindUser(v8, *((_WORD *)pLoadData + 1), *((_DWORD *)pLoadData + 1));
  v7 = 0i64;
  if ( !CUnmannedTraderUserInfo::IsNull(v6) )
  {
    v7 = CUnmannedTraderUserInfo::FindOwner(v6);
    if ( v7 )
    {
      if ( v7->m_bOper )
        CUnmannedTraderUserInfo::CompleteUpdateCheatRegistTime(v6, pLoadDataa);
    }
  }
}
