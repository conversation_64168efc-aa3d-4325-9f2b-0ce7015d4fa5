/*
 * Function: ?AlgorithmName@?$AlgorithmImpl@V?$IteratedHash@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@VHashTransformation@2@@CryptoPP@@VSHA1@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x1404640D0
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA1>::AlgorithmName(CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA1> *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  const char *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int8 v7; // [sp+20h] [bp-18h]@4
  int v8; // [sp+24h] [bp-14h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v9; // [sp+48h] [bp+10h]@1

  v9 = result;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  memset(&v7, 0, sizeof(v7));
  v4 = CryptoPP::SHA1::StaticAlgorithmName();
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    v9,
    v4,
    v7);
  return v9;
}
