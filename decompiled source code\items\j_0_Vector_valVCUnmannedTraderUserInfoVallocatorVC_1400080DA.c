/*
 * Function: j_??0?$_Vector_val@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderUserInfo@@@1@@Z
 * Address: 0x1400080DA
 */

void __fastcall std::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(std::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, std::allocator<CUnmannedTraderUserInfo> _Al)
{
  std::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Vector_val<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    this,
    _Al);
}
