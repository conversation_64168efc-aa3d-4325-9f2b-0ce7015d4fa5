/*
 * Function: ?SendMsg_PartyLeaveSelfResult@CPlayer@@QEAAXPEAVCPartyPlayer@@_N@Z
 * Address: 0x1400DD190
 */

void __fastcall CPlayer::SendMsg_PartyLeaveSelfResult(CPlayer *this, CPartyPlayer *pLeaver, bool bWorldExit)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@5
  bool v7; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@7
  char v9; // [sp+55h] [bp-23h]@7
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = bWorldExit;
  if ( pLeaver )
    *(_DWORD *)szMsg = pLeaver->m_id.dwSerial;
  else
    *(_DWORD *)szMsg = -1;
  pbyType = 16;
  v9 = 10;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 5u);
}
