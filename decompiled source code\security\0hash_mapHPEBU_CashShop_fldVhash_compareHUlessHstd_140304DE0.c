/*
 * Function: ??0?$hash_map@HPEBU_CashShop_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@std@@@stdext@@QEAA@XZ
 * Address: 0x140304DE0
 */

void __fastcall stdext::hash_map<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::hash_map<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>>(stdext::hash_map<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> > > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<int const ,_CashShop_fld const *> > *v3; // rax@4
  stdext::hash_compare<int,std::less<int> > *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  char v7; // [sp+21h] [bp-17h]@4
  std::allocator<std::pair<int const ,_CashShop_fld const *> > *_Al; // [sp+28h] [bp-10h]@4
  stdext::hash_map<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> > > *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::allocator<std::pair<int const,_CashShop_fld const *>>::allocator<std::pair<int const,_CashShop_fld const *>>((std::allocator<std::pair<int const ,_CashShop_fld const *> > *)&v6);
  _Al = v3;
  stdext::hash_compare<int,std::less<int>>::hash_compare<int,std::less<int>>((stdext::hash_compare<int,std::less<int> > *)&v7);
  stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>(
    (stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> >,0> > *)&v9->_Myfirstiter,
    v4,
    _Al);
}
