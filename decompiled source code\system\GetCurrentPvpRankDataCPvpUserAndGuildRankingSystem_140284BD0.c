/*
 * Function: ?GetCurrentPvpRankData@CPvpUserAndGuildRankingSystem@@QEAAPEBU_PVP_RANK_DATA@@EE@Z
 * Address: 0x140284BD0
 */

_PVP_RANK_DATA *__fastcall CPvpUserAndGuildRankingSystem::GetCurrentPvpRankData(CPvpUserAndGuildRankingSystem *this, char byRace, char byNth)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPvpUserAndGuildRankingSystem *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return CUserRankingProcess::GetCurrentPvpRankData(&v7->m_kUserRankingProcess, byRace, byNth);
}
