/*
 * Function: ?Init@CMonsterSkill@@QEAAXXZ
 * Address: 0x140156030
 */

void __fastcall CMonsterSkill::Init(CMonsterSkill *this)
{
  this->m_bExit = 0;
  this->m_UseType = -1;
  this->m_nSFCode = -1;
  this->m_wSFIndex = 0;
  this->m_pSF_Fld = 0i64;
  this->m_BefTime = 0;
  this->m_dwDelayTime = 0;
  LODWORD(this->m_fAttackDist) = 0;
  this->m_dwCastDelay = 0;
  this->m_nSFLv = 1;
  this->m_Element = -1;
  this->m_StdDmg = 0;
  this->m_MinDmg = 0;
  this->m_MaxDmg = 0;
  this->m_MinProb = 0;
  this->m_MaxProb = 100;
  this->m_nMotive = 0;
  this->m_nMotivevalue = -1;
  this->m_nCaseType = 0;
  this->m_nAccumulationCount = 0;
  this->m_pSPConst = 0i64;
}
