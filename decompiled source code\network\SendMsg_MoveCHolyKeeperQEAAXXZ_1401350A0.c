/*
 * Function: ?SendMsg_Move@CHoly<PERSON>eeper@@QEAAXXZ
 * Address: 0x1401350A0
 */

void __fastcall CHolyKeeper::SendMsg_Move(CHolyKeeper *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  __int16 pShort; // [sp+3Ch] [bp-5Ch]@4
  __int16 v6; // [sp+42h] [bp-56h]@4
  __int16 v7; // [sp+44h] [bp-54h]@4
  char v8; // [sp+46h] [bp-52h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v10; // [sp+65h] [bp-33h]@4
  int v11; // [sp+80h] [bp-18h]@4
  unsigned __int64 v12; // [sp+88h] [bp-10h]@4
  CHolyKeeper *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v3 ^ _security_cookie;
  *(_DWORD *)szMsg = v13->m_dwObjSerial;
  FloatToShort(v13->m_fCurPos, &pShort, 3);
  v6 = (signed int)ffloor(v13->m_fTarPos[0]);
  v7 = (signed int)ffloor(v13->m_fTarPos[2]);
  v11 = v13->m_bExit != 0;
  v8 = v11;
  pbyType = 4;
  v10 = -79;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 15, 0);
}
