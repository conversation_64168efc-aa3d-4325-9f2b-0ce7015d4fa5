/*
 * Function: ?Load@CPlayer@@QEAA_NPEAVCUserDB@@_N@Z
 * Address: 0x1400489B0
 */

char __usercall CPlayer::Load@<al>(CPlayer *this@<rcx>, CUserDB *pUser@<rdx>, bool bFirstStart@<r8b>, signed __int64 a4@<rax>)
{
  void *v4; // rsp@1
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@10
  LendItemMng *v8; // rax@14
  CUnmannedTraderController *v9; // rax@16
  cStaticMember_Player *v10; // rax@16
  char v11; // al@16
  int v12; // eax@17
  _PCBANG_PLAY_TIME *v13; // rax@39
  CUserDB *v14; // rcx@39
  CUserDB *v15; // rax@42
  __int64 v16; // [sp-20h] [bp-9268h]@1
  int v17; // [sp+0h] [bp-9248h]@10
  int v18; // [sp+8h] [bp-9240h]@10
  int v19; // [sp+10h] [bp-9238h]@10
  char v20; // [sp+20h] [bp-9228h]@4
  _AVATOR_DATA Dst; // [sp+40h] [bp-9208h]@4
  CMapData *v22; // [sp+91A8h] [bp-A0h]@4
  int v23; // [sp+91B0h] [bp-98h]@4
  CMapData *v24; // [sp+91B8h] [bp-90h]@9
  int j; // [sp+91C0h] [bp-88h]@18
  unsigned int dwMapEnvCode; // [sp+91C4h] [bp-84h]@25
  int k; // [sp+91C8h] [bp-80h]@31
  int l; // [sp+91CCh] [bp-7Ch]@36
  int m; // [sp+91D0h] [bp-78h]@39
  char v30; // [sp+91E0h] [bp-68h]@10
  char v31; // [sp+91E1h] [bp-67h]@13
  char v32; // [sp+91E2h] [bp-66h]@40
  char v33; // [sp+91E3h] [bp-65h]@43
  __int64 v34; // [sp+91E8h] [bp-60h]@4
  int v35; // [sp+91F0h] [bp-58h]@10
  int v36; // [sp+91F4h] [bp-54h]@10
  double v37; // [sp+91F8h] [bp-50h]@16
  int lv; // [sp+9200h] [bp-48h]@16
  _MASTERY_PARAM *v39; // [sp+9208h] [bp-40h]@16
  _PVPPOINT_LIMIT_DB_BASE *pkInfo; // [sp+9210h] [bp-38h]@29
  _PVP_ORDER_VIEW_DB_BASE *v41; // [sp+9218h] [bp-30h]@34
  CCouponMgr *v42; // [sp+9220h] [bp-28h]@39
  CPotionParam *v43; // [sp+9228h] [bp-20h]@42
  unsigned __int64 v44; // [sp+9230h] [bp-18h]@4
  CPlayer *pThis; // [sp+9250h] [bp+8h]@1
  CUserDB *v46; // [sp+9258h] [bp+10h]@1
  bool v47; // [sp+9260h] [bp+18h]@1

  v47 = bFirstStart;
  v46 = pUser;
  pThis = this;
  v4 = alloca(a4);
  v5 = &v16;
  for ( i = 9368i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v34 = -2i64;
  v44 = (unsigned __int64)&v16 ^ _security_cookie;
  v20 = 0;
  _AVATOR_DATA::_AVATOR_DATA(&Dst);
  memcpy_0(&Dst, &v46->m_AvatorData, 0x915Fui64);
  pThis->m_id.dwSerial = Dst.dbAvator.m_dwRecordNum;
  pThis->m_pUserDB = 0i64;
  pThis->m_byUserDgr = v46->m_byUserDgr;
  pThis->m_bySubDgr = v46->m_bySubDgr;
  pThis->m_bFirstStart = v47;
  CPlayerDB::InitPlayerDB(&pThis->m_Param, pThis);
  pThis->m_bMapLoading = 0;
  pThis->m_bOper = 0;
  pThis->m_bFullMode = v46->m_bWndFullMode;
  pThis->m_bCheat_100SuccMake = 0;
  pThis->m_bCheat_makeitem_no_use_matrial = 0;
  pThis->m_bCheat_Matchless = 0;
  pThis->m_nMaxAttackPnt = 0;
  pThis->m_nAnimusAttackPnt = 0;
  pThis->m_nTrapMaxAttackPnt = 0;
  pThis->m_byDamagePart = -1;
  pThis->m_bFreeRecallWaitTime = 0;
  pThis->m_bFreeSFByClass = 0;
  pThis->m_bLootFree = 0;
  pThis->m_bOutOfMap = 0;
  pThis->m_dwNextTimeDungeonDie = 0;
  pThis->m_bNeverDie = 0;
  pThis->m_nVoteSerial = -1;
  pThis->m_bBlockParty = 0;
  pThis->m_bBlockWhisper = 0;
  pThis->m_bBlockTrade = 0;
  pThis->m_bSpyGM = 0;
  pThis->m_bTakeGravityStone = 0;
  pThis->m_bBlockGuildBattleMsg = 0;
  pThis->m_bInGuildBattle = 0;
  pThis->m_bNotifyPosition = 0;
  pThis->m_byGuildBattleColorInx = -1;
  pThis->m_bTakeSoccerBall = 0;
  pThis->m_pSoccerItem = 0i64;
  pThis->m_dwSelfDestructionTime = 0;
  LODWORD(pThis->m_fSelfDestructionDamage) = 0;
  LODWORD(pThis->m_fTalik_DefencePoint) = 0;
  LODWORD(pThis->m_fTalik_AvoidPoint) = 0;
  pThis->m_bCheckMovePacket = 0;
  pThis->m_bLinkBoardDownload = 0;
  pThis->m_bSpecialDownload = 0;
  pThis->m_bQuestDownload = 0;
  pThis->m_bCumDownload = 0;
  pThis->m_bForceDownload = 0;
  pThis->m_bInvenDownload = 0;
  pThis->m_bBaseDownload = 0;
  pThis->m_bAMPInvenDownload = 0;
  pThis->m_bBuddyListDownload = 0;
  pThis->m_bGuildDownload = 0;
  pThis->m_bGuildListDownload = 0;
  pThis->m_byMapInModeBuffer = 0;
  pThis->m_pBeforeTownCheckMap = 0i64;
  pThis->m_bCreateComplete = 0;
  pThis->m_bUpCheckEquipEffect = 1;
  pThis->m_bDownCheckEquipEffect = 0;
  memset_0(pThis->m_byEffectEquipCode, 0, 0xFui64);
  pThis->m_dwPcBangGiveItemListIndex = -1;
  v22 = CMapOperation::GetMap(&g_MapOper, (unsigned __int8)Dst.dbAvator.m_byMapCode);
  v23 = (signed int)(unsigned __int8)Dst.dbAvator.m_byRaceSexCode >> 1;
  if ( CTransportShip::IsMemberBeforeLogoff((CTransportShip *)&g_TransportShip + v23, Dst.dbAvator.m_dwRecordNum) )
  {
    v22 = (CMapData *)*((_QWORD *)&g_TransportShip + 10162 * v23 + 2);
    Dst.dbAvator.m_byMapCode = v22->m_pMapSet->m_dwIndex;
    CTransportShip::GetStartPosInShip((CTransportShip *)&g_TransportShip + v23, Dst.dbAvator.m_fStartPos);
  }
  else
  {
    if ( !v22 || !CMapData::IsMapIn(v22, Dst.dbAvator.m_fStartPos) || v22->m_pMapSet->m_nMapType )
    {
      v24 = CMapOperation::GetPosStartMap(
              &g_MapOper,
              (signed int)(unsigned __int8)Dst.dbAvator.m_byRaceSexCode >> 1,
              1,
              Dst.dbAvator.m_fStartPos);
      if ( !v24 )
      {
        v35 = (signed int)ffloor(Dst.dbAvator.m_fStartPos[2]);
        v36 = (signed int)ffloor(Dst.dbAvator.m_fStartPos[0]);
        v19 = v35;
        v18 = (signed int)ffloor(Dst.dbAvator.m_fStartPos[1]);
        v17 = v36;
        CLogFile::Write(
          &stru_1799C8E78,
          "Load() : failure : %s.. no find start position(race:%d, x:%d, y:%d, z:%d)..",
          v46->m_aszAvatorName,
          (unsigned int)((signed int)(unsigned __int8)Dst.dbAvator.m_byRaceSexCode >> 1));
        v30 = 0;
        _AVATOR_DATA::~_AVATOR_DATA(&Dst);
        return v30;
      }
      Dst.dbAvator.m_byMapCode = v24->m_pMapSet->m_dwIndex;
    }
    v22 = CMapOperation::GetMap(&g_MapOper, (unsigned __int8)Dst.dbAvator.m_byMapCode);
    if ( !v22->m_bUse )
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "Load() : failure : %s.. no used map(%s)..",
        v46->m_aszAvatorName,
        v22->m_pMapSet->m_strCode);
      v31 = 0;
      _AVATOR_DATA::~_AVATOR_DATA(&Dst);
      return v31;
    }
  }
  v8 = LendItemMng::Instance();
  LendItemMng::Release(v8, pThis->m_ObjID.m_wIndex);
  if ( CPlayerDB::ConvertAvatorDB(&pThis->m_Param, &Dst)
    && CPlayerDB::ConvertGeneralDB(&pThis->m_Param, &Dst, &v46->m_AvatorData) )
  {
    v9 = CUnmannedTraderController::Instance();
    CUnmannedTraderController::Load(v9, pThis->m_ObjID.m_wIndex, pThis->m_id.dwSerial, &Dst.dbTrade);
    pThis->m_bLoad = 1;
    pThis->m_pUserDB = v46;
    pThis->m_bMapLoading = 1;
    pThis->m_dwLastCheckRegionTime = 0;
    pThis->m_wRegionIndex = -1;
    pThis->m_wRegionMapIndex = -1;
    CPlayerDB::GetExp(&pThis->m_Param);
    v37 = 0.0;
    lv = CPlayerDB::GetLevel(&pThis->m_Param);
    v10 = cStaticMember_Player::Instance();
    cStaticMember_Player::GetLimitExp(v10, lv);
    pThis->m_dwExpRate = (signed int)floor(v37 / 0.0 * 1000000.0);
    v11 = CPlayerDB::GetRaceCode(&pThis->m_Param);
    v39 = &pThis->m_pmMst;
    _MASTERY_PARAM::Init(&pThis->m_pmMst, &v46->m_AvatorData.dbStat, v11);
    _DTRADE_PARAM::Init(&pThis->m_pmTrd);
    _WEAPON_PARAM::Init(&pThis->m_pmWpn);
    _TOWER_PARAM::Init(&pThis->m_pmTwr);
    _TRAP_PARAM::Init(&pThis->m_pmTrp);
    _BUDDY_LIST::Init(&pThis->m_pmBuddy);
    _CRYMSG_LIST::Init(&pThis->m_pmCryMsg);
    CPlayer::CalcAddPointByClass(pThis);
    pThis->m_nMaxPoint[0] = CPlayer::_CalcMaxHP(pThis);
    pThis->m_nMaxPoint[1] = CPlayer::_CalcMaxFP(pThis);
    pThis->m_nMaxPoint[2] = CPlayer::_CalcMaxSP(pThis);
    CPlayer::CalcEquipMaxDP(pThis, 1);
    memcpy_0(pThis->m_nOldPoint, pThis->m_nMaxPoint, 0x10ui64);
    pThis->m_nOldMaxDP = CPlayer::GetMaxDP(pThis);
    if ( v47 )
    {
      ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))pThis->vfptr->SetHP)(pThis, pThis->m_nMaxPoint[0], 0i64);
      CPlayer::SetFP(pThis, pThis->m_nMaxPoint[1], 0);
      CPlayer::SetSP(pThis, pThis->m_nMaxPoint[2], 0);
      v12 = CPlayer::GetMaxDP(pThis);
      CPlayer::SetDP(pThis, v12, 0);
    }
    LODWORD(pThis->m_fEquipSpeed) = 0;
    for ( j = 0; j < 50; ++j )
    {
      if ( _BUDDY_DB_BASE::_LIST::IsFilled((_BUDDY_DB_BASE::_LIST *)&pThis->m_pUserDB->m_AvatorData.dbBuddy + j) )
      {
        if ( pThis->m_pUserDB->m_AvatorData.dbBuddy.m_List[j].wszName[0] == 42 )
          CUserDB::Update_DelBuddy(pThis->m_pUserDB, j);
        pThis->m_pmBuddy.m_List[j].dwSerial = pThis->m_pUserDB->m_AvatorData.dbBuddy.m_List[j].dwSerial;
        strcpy_0(pThis->m_pmBuddy.m_List[j].wszName, pThis->m_pUserDB->m_AvatorData.dbBuddy.m_List[j].wszName);
      }
    }
    _NameChangeBuddyInfo::Init(&pThis->m_NameChangeBuddyInfo);
    dwMapEnvCode = CLevel::GetEnvironment(&v22->m_Level);
    CPlayer::SendMsg_MapEnvInform(pThis, v22->m_pMapSet->m_dwIndex, dwMapEnvCode);
    pThis->m_pDHChannel = 0i64;
    if ( pThis->m_pUserDB->m_byUserDgr == 2 )
      pThis->m_bSpyGM = !strncmp(&pThis->m_pUserDB->m_szAccountID[1], "SPY", 3ui64);
    pkInfo = &pThis->m_pUserDB->m_AvatorData.dbPvpPointLimit;
    CPlayerDB::GetPvPPoint(&pThis->m_Param);
    if ( CPvpPointLimiter::Set(&pThis->m_kPvpPointLimiter, 0.0, pkInfo) )
      CUserDB::UpdateContUserSave(pThis->m_pUserDB, 1);
    for ( k = 0; k < 10; ++k )
      strcpy_0((char *)&pThis->m_pmCryMsg + 65 * k, (const char *)&pThis->m_pUserDB->m_AvatorData.dbBossCry + 65 * k);
    v41 = &pThis->m_pUserDB->m_AvatorData.dbPvpOrderView;
    CPlayerDB::GetPvPPoint(&pThis->m_Param);
    if ( CPvpOrderView::SetPvpOrderView(&pThis->m_kPvpOrderView, 0.0, v41, pThis) )
      CUserDB::UpdateContUserSave(pThis->m_pUserDB, 1);
    for ( l = 0; l < 3; ++l )
    {
      LODWORD(pThis->m_fGroupMapPoint[l][0]) = 0;
      LODWORD(pThis->m_fGroupMapPoint[l][1]) = 0;
      pThis->m_byGroupMapPointMapCode[l] = 0;
      pThis->m_wGroupMapPointLayerIndex[l] = 0;
      pThis->m_dwLastGroupMapPointTime[l] = 0;
    }
    pThis->m_byPlusKey = rand();
    pThis->m_wXorKey = rand();
    pThis->m_dwMoveCount = 0;
    pThis->m_dwTargetCount = 0;
    pThis->m_dwAttackCount = 0;
    pThis->m_bSFDelayNotCheck = 0;
    CPvpCashPoint::Init(&pThis->m_kPvpCashPoint, &pThis->m_pUserDB->m_AvatorData.dbPvpOrderView);
    v13 = &pThis->m_pUserDB->m_AvatorData.dbPlayTimeInPcbang;
    v14 = pThis->m_pUserDB;
    v42 = &pThis->m_kPcBangCoupon;
    CCouponMgr::LoadData(&pThis->m_kPcBangCoupon, v14->m_dwAccountSerial, v13);
    CPlayer::SetLastAttBuff(pThis, pThis->m_pUserDB->m_AvatorData.dbSupplement.bLastAttBuff);
    for ( m = 0; m < 38; ++m )
    {
      v15 = pThis->m_pUserDB;
      v43 = &pThis->m_PotionParam;
      CPotionParam::SetPotionActDelay(
        &pThis->m_PotionParam,
        m,
        0,
        v15->m_AvatorData.dbPotionNextUseTime.dwPotionNextUseTime[m]);
    }
    pThis->m_bCntEnable = 0;
    v33 = 1;
    _AVATOR_DATA::~_AVATOR_DATA(&Dst);
    result = v33;
  }
  else
  {
    CLogFile::Write(&stru_1799C8E78, "Load() : failure : %d:%s", Dst.dbAvator.m_dwRecordNum, v46->m_aszAvatorName);
    v32 = 0;
    _AVATOR_DATA::~_AVATOR_DATA(&Dst);
    result = v32;
  }
  return result;
}
