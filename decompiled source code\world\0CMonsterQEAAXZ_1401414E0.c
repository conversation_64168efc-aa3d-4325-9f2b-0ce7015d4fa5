/*
 * Function: ??0CMonster@@QEAA@XZ
 * Address: 0x1401414E0
 */

void __fastcall CMonster::CMonster(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CMonster *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CCharacter::CCharacter((CCharacter *)&v5->vfptr);
  v5->vfptr = (CGameObjectVtbl *)&CMonster::`vftable';
  CLootingMgr::CLootingMgr(&v5->m_LootMgr);
  CMonsterAggroMgr::CMonsterAggroMgr(&v5->m_AggroMgr);
  CMonsterHierarchy::CMonsterHierarchy(&v5->m_MonHierarcy);
  MonsterSFContDamageToleracne::MonsterSFContDamageToleracne(&v5->m_SFContDamageTolerance);
  EmotionPresentationChecker::EmotionPresentationChecker(&v5->m_EmotionPresentationCheck);
  MonsterStateData::MonsterStateData(&v5->m_MonsterStateData);
  MonsterStateData::MonsterStateData(&v5->m_BeforeMonsterStateData);
  v5->m_pTargetChar = 0i64;
  CMonsterSkillPool::CMonsterSkillPool(&v5->m_MonsterSkillPool);
  CMonsterAI::CMonsterAI(&v5->m_AI);
  CLuaSignalReActor::CLuaSignalReActor(&v5->m_LuaSignalReActor);
  CMonsterAggroMgr::OnlyOnceInit(&v5->m_AggroMgr, v5);
  CMonsterHierarchy::OnlyOnceInit(&v5->m_MonHierarcy, v5);
  MonsterSFContDamageToleracne::OnlyOnceInit(&v5->m_SFContDamageTolerance, v5);
  CMonster::_InitSDM();
  ++CMonster::s_nAllocNum;
}
