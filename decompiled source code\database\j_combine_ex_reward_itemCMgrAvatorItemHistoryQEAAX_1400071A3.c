/*
 * Function: j_?combine_ex_reward_item@CMgrAvatorItemHistory@@QEAAXHEPEAU_ITEMCOMBINE_DB_BASE@@PEAEPEA_KPEAD@Z
 * Address: 0x1400071A3
 */

void __fastcall CMgrAvatorItemHistory::combine_ex_reward_item(CMgrAvatorItemHistory *this, int n, char by<PERSON>ake<PERSON><PERSON>, _ITEMCOMBINE_DB_BASE *pCombineDB, char *pbyRewardTypeList, unsigned __int64 *lnUIDs, char *strFileName)
{
  CMgrAvatorItemHistory::combine_ex_reward_item(this, n, byMakeNum, pCombineDB, pbyRewardTypeList, lnUIDs, strFileName);
}
