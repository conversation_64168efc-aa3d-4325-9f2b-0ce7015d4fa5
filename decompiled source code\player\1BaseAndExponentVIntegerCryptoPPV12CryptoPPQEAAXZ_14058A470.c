/*
 * Function: ??1?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@QEAA@XZ
 * Address: 0x14058A470
 */

void __fastcall CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(__int64 a1)
{
  CryptoPP::Integer *v1; // [sp+40h] [bp+8h]@1

  v1 = (CryptoPP::Integer *)a1;
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a1 + 40));
  CryptoPP::Integer::~Integer(v1);
}
