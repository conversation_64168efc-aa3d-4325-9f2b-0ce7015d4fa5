/*
 * Function: ??0CNormalGuildBattleStateList@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403F1E80
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateList::CNormalGuildBattleStateList(GUILD_BATTLE::CNormalGuildBattleStateList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleStateList *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  GUILD_BATTLE::CGuildBattleStateList::CGuildBattleStateList(
    (GUILD_BATTLE::CGuildBattleStateList *)&v5->vfptr,
    7,
    0,
    1u);
  v5->vfptr = (GUILD_BATTLE::CGuildBattleStateListVtbl *)&GUILD_BATTLE::CNormalGuildBattleStateList::`vftable';
  GUILD_BATTLE::CNormalGuildBattleStateNotify::CNormalGuildBattleStateNotify(&v5->NOTIFY);
  GUILD_BATTLE::CNormalGuildBattleStateReady::CNormalGuildBattleStateReady(&v5->READY);
  GUILD_BATTLE::CNormalGuildBattleStateCountDown::CNormalGuildBattleStateCountDown(&v5->COUNT);
  GUILD_BATTLE::CNormalGuildBattleStateInBattle::CNormalGuildBattleStateInBattle(&v5->INBATTLE);
  GUILD_BATTLE::CNormalGuildBattleStateDivide::CNormalGuildBattleStateDivide(&v5->DIVIDE);
  GUILD_BATTLE::CNormalGuildBattleStateReturn::CNormalGuildBattleStateReturn(&v5->RETURN);
  GUILD_BATTLE::CNormalGuildBattleStateFin::CNormalGuildBattleStateFin(&v5->FIN);
  v5->m_pStateList[0] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->NOTIFY.vfptr;
  v5->m_pStateList[1] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->READY.vfptr;
  v5->m_pStateList[2] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->COUNT.vfptr;
  v5->m_pStateList[3] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->INBATTLE.vfptr;
  v5->m_pStateList[4] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->DIVIDE.vfptr;
  v5->m_pStateList[5] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->RETURN.vfptr;
  v5->m_pStateList[6] = (GUILD_BATTLE::CNormalGuildBattleState *)&v5->FIN.vfptr;
}
