/*
 * Function: ?SendMsg_ExchangeMoneyResult@CPlayer@@QEAAXE@Z
 * Address: 0x1400D7700
 */

void __fastcall CPlayer::SendMsg_ExchangeMoneyResult(CPlayer *this, char byErrCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned int v6; // [sp+39h] [bp-4Fh]@4
  unsigned int v7; // [sp+3Dh] [bp-4Bh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v9; // [sp+65h] [bp-23h]@4
  CPlayer *v10; // [sp+90h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byErrCode;
  v7 = CPlayerDB::GetDalant(&v10->m_Param);
  v6 = CPlayerDB::GetGold(&v10->m_Param);
  pbyType = 12;
  v9 = 13;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
