/*
 * Function: ?InputRecoverableMessage@TF_SignerBase@CryptoPP@@UEBAXAEAVPK_MessageAccumulator@2@PEBE_K@Z
 * Address: 0x140622820
 */

void __fastcall CryptoPP::TF_SignerBase::InputRecoverableMessage(CryptoPP::TF_SignerBase *this, struct CryptoPP::PK_MessageAccumulator *a2, const unsigned __int8 *a3, unsigned __int64 a4)
{
  __int64 *v4; // rax@1
  __int64 v5; // rax@1
  int v6; // eax@1
  unsigned __int64 v7; // rax@1
  unsigned __int64 v8; // rax@1
  __int64 v9; // rax@4
  __int64 v10; // rax@4
  __int64 v11; // rax@4
  unsigned __int64 v12; // rax@4
  __int64 v13; // rax@10
  char v14; // [sp+40h] [bp-208h]@1
  __int64 v15; // [sp+48h] [bp-200h]@1
  struct CryptoPP::PK_MessageAccumulator *v16; // [sp+50h] [bp-1F8h]@1
  unsigned __int64 v17; // [sp+58h] [bp-1F0h]@4
  __int64 *v18; // [sp+60h] [bp-1E8h]@1
  CryptoPP::PK_SignatureScheme::KeyTooShort v19; // [sp+68h] [bp-1E0h]@2
  char v20; // [sp+B8h] [bp-190h]@4
  CryptoPP::NotImplemented v21; // [sp+C8h] [bp-180h]@5
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+118h] [bp-130h]@5
  unsigned __int8 v23; // [sp+148h] [bp-100h]@5
  CryptoPP::InvalidArgument v24; // [sp+150h] [bp-F8h]@8
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > v25; // [sp+1A0h] [bp-A8h]@8
  unsigned __int8 v26; // [sp+1D0h] [bp-78h]@8
  __int64 v27; // [sp+1D8h] [bp-70h]@1
  CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v28; // [sp+1E0h] [bp-68h]@1
  __int64 v29; // [sp+1E8h] [bp-60h]@1
  __int64 v30; // [sp+1F0h] [bp-58h]@1
  unsigned __int64 v31; // [sp+1F8h] [bp-50h]@1
  __int64 v32; // [sp+200h] [bp-48h]@4
  __int64 v33; // [sp+208h] [bp-40h]@4
  CryptoPP::PK_SignatureSchemeVtbl *v34; // [sp+210h] [bp-38h]@4
  __int64 v35; // [sp+218h] [bp-30h]@4
  __int64 v36; // [sp+220h] [bp-28h]@4
  struct CryptoPP::PK_MessageAccumulator *v37; // [sp+228h] [bp-20h]@10
  CryptoPP::ClonableVtbl *v38; // [sp+230h] [bp-18h]@10
  __int64 v39; // [sp+238h] [bp-10h]@10
  CryptoPP::TF_SignerBase *v40; // [sp+250h] [bp+8h]@1
  char *t; // [sp+260h] [bp+18h]@1
  unsigned __int64 len; // [sp+268h] [bp+20h]@1

  len = a4;
  t = (char *)a3;
  v40 = this;
  v27 = -2i64;
  v16 = a2;
  ((void (__fastcall *)(CryptoPP::TF_SignerBase *, char *))this->vfptr[1].AllowNonrecoverablePart)(this, &v14);
  v28 = v40->vfptr;
  LODWORD(v4) = ((int (__fastcall *)(signed __int64))v28->GetMessageEncodingInterface)((signed __int64)&v40->vfptr);
  v18 = v4;
  LODWORD(v5) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v16->vfptr[9].__vecDelDtor)(v16);
  v29 = v5;
  v6 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 56i64))(v5);
  v30 = *v18;
  LODWORD(v7) = (*(int (__fastcall **)(__int64 *, __int64, _QWORD))(v30 + 8))(v18, v15, (unsigned int)v6);
  v31 = v7;
  LODWORD(v8) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v40);
  if ( v8 < v31 )
  {
    CryptoPP::PK_SignatureScheme::KeyTooShort::KeyTooShort(&v19);
    CxxThrowException_0((__int64)&v19, (__int64)&TI4_AVKeyTooShort_PK_SignatureScheme_CryptoPP__);
  }
  LODWORD(v9) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v16->vfptr[9].__vecDelDtor)(v16);
  v32 = v9;
  v33 = (unsigned int)(*(int (__fastcall **)(__int64))(*(_QWORD *)v9 + 56i64))(v9);
  v34 = v40->vfptr;
  LODWORD(v10) = ((int (__fastcall *)(CryptoPP::TF_SignerBase *, char *))v34[1].AllowNonrecoverablePart)(v40, &v20);
  v35 = v10;
  LODWORD(v11) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v40);
  v36 = *v18;
  LODWORD(v12) = (*(int (__fastcall **)(__int64 *, __int64, _QWORD, __int64))(v36 + 16))(
                   v18,
                   v11,
                   *(_QWORD *)(v35 + 8),
                   v33);
  v17 = v12;
  if ( !v12 )
  {
    memset(&v23, 0, sizeof(v23));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "TF_SignerBase: this algorithm does not support messsage recovery or the key is too short",
      v23);
    CryptoPP::NotImplemented::NotImplemented(&v21, &s);
    CxxThrowException_0((__int64)&v21, (__int64)&TI3_AVNotImplemented_CryptoPP__);
  }
  if ( len > v17 )
  {
    memset(&v26, 0, sizeof(v26));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &v25,
      "TF_SignerBase: the recoverable message part is too long for the given key and algorithm",
      v26);
    CryptoPP::InvalidArgument::InvalidArgument(&v24, &v25);
    CxxThrowException_0((__int64)&v24, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::Assign(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v16[1],
    t,
    len);
  v37 = v16 + 10;
  v38 = v16->vfptr;
  LODWORD(v13) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v38[9].__vecDelDtor)(v16);
  v39 = *v18;
  (*(void (__fastcall **)(__int64 *, __int64, char *, unsigned __int64))(v39 + 40))(v18, v13, t, len);
}
