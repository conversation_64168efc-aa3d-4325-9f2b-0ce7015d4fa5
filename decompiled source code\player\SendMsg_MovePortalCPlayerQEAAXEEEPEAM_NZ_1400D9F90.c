/*
 * Function: ?SendMsg_MovePortal@CPlayer@@QEAAXEEEPEAM_N@Z
 * Address: 0x1400D9F90
 */

void __fastcall CPlayer::SendMsg_MovePortal(CPlayer *this, char byRet, char byMapIndex, char byPotalIndex, float *pfStartPos, bool bEqualZone)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v10; // [sp+39h] [bp-4Fh]@4
  char Dst; // [sp+3Ah] [bp-4Eh]@4
  bool v12; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v14; // [sp+65h] [bp-23h]@4
  CPlayer *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v6 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  szMsg = byRet;
  v10 = byMapIndex;
  memcpy_0(&Dst, pfStartPos, 0xCui64);
  v12 = bEqualZone == 0;
  pbyType = 8;
  v14 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xFu);
}
