/*
 * Function: ??0BossSchedule_Map@@QEAA@XZ
 * Address: 0x14041B720
 */

void __fastcall BossSchedule_Map::BossSchedule_Map(BossSchedule_Map *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  BossSchedule_Map *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CIniFile::CIniFile(&Dst->m_INIFile);
  memset_0(Dst, 0, 0x198ui64);
}
