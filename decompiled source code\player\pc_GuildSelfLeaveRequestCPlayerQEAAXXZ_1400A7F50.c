/*
 * Function: ?pc_GuildSelfLeaveRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400A7F50
 */

void __fastcall CPlayer::pc_GuildSelfLeaveRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@17
  __int64 v4; // [sp+0h] [bp-88h]@1
  char v5; // [sp+30h] [bp-58h]@4
  CGuild *v6; // [sp+38h] [bp-50h]@4
  char v7; // [sp+40h] [bp-48h]@17
  _qry_case_selfleave v8; // [sp+58h] [bp-30h]@17
  CPlayer *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  v6 = v9->m_Param.m_pGuild;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v9->m_id.wIndex) == 99 )
  {
    v5 = 106;
  }
  else if ( v9->m_Param.m_bGuildLock )
  {
    v5 = -51;
  }
  else if ( v6 )
  {
    if ( v6->m_bRankWait )
    {
      v5 = 71;
    }
    else if ( v6->m_bNowProcessSgtMter
           && !v6->m_SuggestedMatter.byMatterType
           && v6->m_SuggestedMatter.dwMatterDst == v9->m_dwObjSerial )
    {
      v5 = 81;
    }
  }
  else
  {
    v5 = -54;
  }
  if ( !v5 )
  {
    v7 = 1;
    v8.in_leaverserial = v9->m_dwObjSerial;
    v8.tmp_leaverindex = v9->m_ObjID.m_wIndex;
    v8.tmp_guildindex = v6->m_nIndex;
    v8.tmp_guildserial = v6->m_dwSerial;
    v8.in_MemberNum = CGuild::GetMemberNum(v6) - 1;
    v3 = _qry_case_selfleave::size(&v8);
    if ( !CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, &v9->m_id, 17, (char *)&v8, v3) )
      v7 = 0;
    if ( v7 )
      v9->m_Param.m_bGuildLock = 1;
    else
      v5 = -1;
  }
  if ( v5 )
    CPlayer::SendMsg_GuildSelfLeaveResult(v9, v5);
  if ( !v5 )
  {
    if ( v9->m_GroupTargetObject[1].pObject )
    {
      CPlayer::__target::init(&v9->m_GroupTargetObject[1]);
      CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v9, 1);
    }
  }
}
