/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor$1
 * Address: 0x140452680
 */

void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>::~AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>(*(CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> > **)(a2 + 64));
}
