/*
 * Function: ?GetCurScheduleID@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAII@Z
 * Address: 0x1403DD2D0
 */

unsigned int __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::GetCurScheduleID(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiMapID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetCurScheduleID(v6->m_pkTodaySchedule, uiMapID);
}
