/*
 * Function: ?Create@CMerchant@@QEAA_NPEAU_npc_create_setdata@@@Z
 * Address: 0x140139140
 */

char __fastcall CMerchant::Create(CMerchant *this, _npc_create_setdata *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMerchant *v6; // [sp+30h] [bp+8h]@1
  _npc_create_setdata *pDataa; // [sp+38h] [bp+10h]@1

  pDataa = pData;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CCharacter::Create((CCharacter *)&v6->vfptr, (_character_create_setdata *)&pData->m_pRecordSet) )
  {
    v6->m_pItemStore = pDataa->m_pLinkItemStore;
    v6->m_dwObjSerial = CMerchant::GetNewMonSerial();
    v6->m_byRaceCode = pDataa->m_byRaceCode;
    CMerchant::SendMsg_Create(v6);
    ++CMerchant::s_nLiveNum;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
