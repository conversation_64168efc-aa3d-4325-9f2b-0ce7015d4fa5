/*
 * Function: ?Init@_WEAPON_PARAM@@QEAAXXZ
 * Address: 0x140077FB0
 */

void __fastcall _WEAPON_PARAM::Init(_WEAPON_PARAM *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _WEAPON_PARAM *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->nGaMaxAF = 2;
  v4->nGaMinAF = 1;
  v4->nMaMaxAF = 2;
  v4->nMaMinAF = 1;
  v4->byGaMinSel = 35;
  v4->byGaMaxSel = 60;
  v4->byMaMinSel = 35;
  v4->byMaMaxSel = 60;
  v4->byAttTolType = -1;
  v4->byWpClass = 0;
  v4->byWpType = 0;
  v4->pFixWp = 0i64;
  v4->pFixUnit = 0i64;
  v4->wGaAttRange = 40;
  v4->nActiveType = 0;
  v4->nActiveEffLvl = 0;
  v4->nActiveProb = 0;
  memset_0(v4->strActiveCode_key, 0, 0x40ui64);
  memset_0(v4->strEffBulletType, 0, 0x40ui64);
}
