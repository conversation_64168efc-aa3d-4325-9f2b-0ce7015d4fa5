/*
 * Function: ?Set_LimitedSale_count@CashItemRemoteStore@@QEAAXEK@Z
 * Address: 0x1402FDA00
 */

void __fastcall CashItemRemoteStore::Set_LimitedSale_count(CashItemRemoteStore *this, char byTableCode, unsigned int dwIndex)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CashItemRemoteStore *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < v6->m_lim_event.m_byEventNum; ++j )
  {
    if ( v6->m_lim_event.m_EventItemInfo[j].byTableCode == (unsigned __int8)byTableCode
      && v6->m_lim_event.m_EventItemInfo[j].dwIndex == dwIndex )
    {
      --v6->m_lim_event.m_EventItemInfo[j].wCount;
      return;
    }
  }
}
