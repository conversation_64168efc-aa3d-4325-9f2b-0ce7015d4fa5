/*
 * Function: ?_Tidy@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@IEAAXXZ
 * Address: 0x1406008E0
 */

__int64 __fastcall std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::_Tidy(__int64 a1)
{
  __int64 result; // rax@11
  __int64 j; // [sp+20h] [bp-18h]@4
  __int64 i; // [sp+40h] [bp+8h]@1

  for ( i = a1;
        !std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::empty(i);
        std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::pop_back(i) )
  {
    ;
  }
  for ( j = *(_QWORD *)(i + 32);
        j;
        std::allocator<CryptoPP::MeterFilter::MessageRange *>::destroy(i + 8, *(_QWORD *)(i + 24) + 8 * j) )
  {
    if ( *(_QWORD *)(*(_QWORD *)(i + 24) + 8 * --j) )
      std::allocator<CryptoPP::MeterFilter::MessageRange>::deallocate(
        i + 16,
        *(_QWORD *)(*(_QWORD *)(i + 24) + 8 * j),
        1i64);
  }
  if ( *(_QWORD *)(i + 24) )
    std::allocator<CryptoPP::MeterFilter::MessageRange *>::deallocate(i + 8, *(_QWORD *)(i + 24), *(_QWORD *)(i + 32));
  *(_QWORD *)(i + 32) = 0i64;
  result = i;
  *(_QWORD *)(i + 24) = 0i64;
  return result;
}
