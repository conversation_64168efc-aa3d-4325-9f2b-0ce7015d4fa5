/*
 * Function: ?Load_db@CGuildRoomSystem@@QEAA_NXZ
 * Address: 0x1402E9690
 */

char __fastcall CGuildRoomSystem::Load_db(CGuildRoomSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildRoomSystem *v4; // rax@10
  unsigned int v5; // ecx@11
  unsigned int v6; // edx@11
  CItemStoreManager *v7; // rax@12
  CItemStoreManager *v8; // rax@14
  __int64 v9; // [sp+0h] [bp-EC8h]@1
  unsigned int dwGuildSerial; // [sp+20h] [bp-EA8h]@8
  tagTIMESTAMP_STRUCT *ts; // [sp+28h] [bp-EA0h]@8
  bool bRestore; // [sp+30h] [bp-E98h]@8
  unsigned __int8 Dst; // [sp+50h] [bp-E78h]@4
  unsigned int v14[5]; // [sp+54h] [bp-E74h]@8
  char v15; // [sp+69h] [bp-E5Fh]@8
  char v16[2]; // [sp+6Ah] [bp-E5Eh]@8
  tagTIMESTAMP_STRUCT v17; // [sp+6Ch] [bp-E5Ch]@8
  char v18; // [sp+E74h] [bp-54h]@6
  char v19; // [sp+E75h] [bp-53h]@6
  unsigned __int8 j; // [sp+E76h] [bp-52h]@6
  CMapData *v21; // [sp+E78h] [bp-50h]@10
  CMapItemStoreList *pDest; // [sp+E80h] [bp-48h]@12
  CMapItemStoreList *v23; // [sp+E88h] [bp-40h]@14
  __int64 v24; // [sp+E98h] [bp-30h]@8
  __int64 v25; // [sp+EA0h] [bp-28h]@10
  __int64 v26; // [sp+EA8h] [bp-20h]@10
  unsigned __int64 v27; // [sp+EB0h] [bp-18h]@4
  CGuildRoomSystem *v28; // [sp+ED0h] [bp+8h]@1

  v28 = this;
  v1 = &v9;
  for ( i = 944i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v27 = (unsigned __int64)&v9 ^ _security_cookie;
  memset_0(&Dst, 0, 0xE14ui64);
  if ( CRFWorldDatabase::Select_GuildRoomInfo(pkDB, (_guildroom_info *)&Dst) )
  {
    v18 = 0;
    v19 = 0;
    for ( j = 0; j < (signed int)Dst; ++j )
    {
      v24 = 40i64 * j;
      bRestore = 1;
      ts = (tagTIMESTAMP_STRUCT *)((char *)&v17 + 40 * j);
      dwGuildSerial = v14[10 * (unsigned __int64)j];
      if ( CGuildRoomSystem::RentRoom(v28, v16[40 * j], *(&v15 + 40 * j), 0, dwGuildSerial, ts, 1) )
      {
        CLogFile::Write(&stru_1799C8F30, "CGuildRoomSystem::Load_db() : RentRoom(...) %uth Fail!", j);
        return 0;
      }
      v25 = 40i64 * j;
      v26 = 40i64 * j;
      v4 = CGuildRoomSystem::GetInstance();
      v21 = CGuildRoomSystem::GetMapData(v4, v16[v26], *(&v15 + v25));
      if ( !v21 )
      {
        v5 = (unsigned __int8)*(&v15 + 40 * j);
        v6 = (unsigned __int8)v16[40 * j];
        dwGuildSerial = j;
        CLogFile::Write(
          &stru_1799C8F30,
          "CGuildRoomSystem::Load_db() : CGuildRoomSystem::GetInstance()->GetMapData( byRace(%u), byRoomType(%u) ) %uth NULL!",
          v6,
          v5);
        return 0;
      }
      v7 = CItemStoreManager::Instance();
      pDest = CItemStoreManager::GetMapItemStoreListBySerial(v7, v21->m_nMapIndex);
      if ( !pDest )
      {
        CLogFile::Write(
          &stru_1799C8F30,
          "CGuildRoomSystem::Load_db() : CItemStoreManager::Instance()->GetMapItemStoreListBySerial(pMap->m_nMapIndex(%d)) %uth NULL!",
          v21->m_nMapIndex,
          j);
        return 0;
      }
      v8 = CItemStoreManager::Instance();
      v23 = CItemStoreManager::GetEmptyInstanceItemStore(v8);
      if ( !v23 )
      {
        CLogFile::Write(
          &stru_1799C8F30,
          "CGuildRoomSystem::Load_db() : CItemStoreManager::Instance()->GetEmptyInstanceItemStore() %uth NULL!",
          j);
        return 0;
      }
      if ( !CMapItemStoreList::CopyItemStoreData(v23, pDest) )
      {
        CLogFile::Write(
          &stru_1799C8F30,
          "CGuildRoomSystem::Load_db() : pGuildStoreList->CopyItemStoreData(pBaseStoreList) %uth Fail!\r\n"
          "Map(%s) GuildRoom Store NPC Not Exist!",
          j,
          &v21->m_BspInfo);
        return 0;
      }
      CMapItemStoreList::SetTypeNSerial(v23, 1, v14[10 * (unsigned __int64)j]);
    }
    result = 1;
  }
  else
  {
    CLogFile::Write(&stru_1799C8F30, "CGuildRoomSystem::Load_db() : g_Main.m_pWorldDB->Select_GuildRoomInfo(...) Fail!");
    result = 0;
  }
  return result;
}
