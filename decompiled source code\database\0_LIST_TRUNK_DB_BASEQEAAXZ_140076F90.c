/*
 * Function: ??0_LIST@_TRUNK_DB_BASE@@QEAA@XZ
 * Address: 0x140076F90
 */

void __fastcall _TRUNK_DB_BASE::_LIST::_LIST(_TRUNK_DB_BASE::_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _TRUNK_DB_BASE::_LIST *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _INVENKEY::_INVENKEY(&v4->Key);
  _TRUNK_DB_BASE::_LIST::Init(v4);
}
