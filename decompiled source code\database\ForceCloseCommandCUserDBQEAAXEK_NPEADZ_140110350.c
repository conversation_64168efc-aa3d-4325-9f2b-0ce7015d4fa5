/*
 * Function: ?ForceCloseCommand@CUserDB@@QEAAXEK_NPEAD@Z
 * Address: 0x140110350
 */

void __fastcall CUserDB::ForceCloseCommand(CUserDB *this, char byKickType, unsigned int dwPushIP, bool bSlow, char *pszCause)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@4
  __int64 v8; // [sp+0h] [bp-128h]@1
  _server_notify_inform_zone v9; // [sp+34h] [bp-F4h]@4
  char pbyType; // [sp+54h] [bp-D4h]@4
  char v11; // [sp+55h] [bp-D3h]@4
  char Dest; // [sp+80h] [bp-A8h]@4
  unsigned __int64 v13; // [sp+110h] [bp-18h]@4
  CUserDB *v14; // [sp+130h] [bp+8h]@1
  char v15; // [sp+138h] [bp+10h]@1
  bool v16; // [sp+148h] [bp+20h]@1

  v16 = bSlow;
  v15 = byKickType;
  v14 = this;
  v5 = &v8;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = (unsigned __int64)&v8 ^ _security_cookie;
  v9.wMsgCode = (unsigned __int8)byKickType;
  v9.dwPushIP = dwPushIP;
  pbyType = 1;
  v11 = 16;
  v7 = _server_notify_inform_zone::size(&v9);
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_idWorld.wIndex, &pbyType, (char *)&v9, v7);
  sprintf(&Dest, "CLOSE>> ForceCloseCommand Type: %d, ID: %s Reason : ", (unsigned __int8)v15, v14->m_szAccountID);
  if ( pszCause )
    strcat_0(&Dest, pszCause);
  CNetworkEX::Close(&g_Network, 0, v14->m_idWorld.wIndex, v16, &Dest);
}
