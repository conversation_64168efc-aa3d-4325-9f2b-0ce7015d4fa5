/*
 * Function: ?_complete_tsk_cash_total_selling_select@CashDbWorker@@IEAAXPEAVTask@@@Z
 * Address: 0x1402F03B0
 */

void __fastcall CashDbWorker::_complete_tsk_cash_total_selling_select(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CashItemRemoteStore *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int *v6; // [sp+20h] [bp-18h]@4
  CashDbWorker *v7; // [sp+40h] [bp+8h]@1
  Task *v8; // [sp+48h] [bp+10h]@1

  v8 = pkTsk;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = (unsigned int *)Task::GetTaskBuf(pkTsk);
  if ( Task::GetRetCode(v8) == 1 )
  {
    CLogFile::Write(v7->_kLogger, "[ERR_QRY] cash total selling select ");
  }
  else
  {
    v4 = CashItemRemoteStore::Instance();
    CashItemRemoteStore::Check_Grosssales(v4, *v6);
  }
}
