/*
 * Function: ?CompleteUpdateState@CUnmannedTraderUserInfo@@QEAA_NKE_N@Z
 * Address: 0x1403548E0
 */

char __fastcall CUnmannedTraderUserInfo::CompleteUpdateState(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, char byState, bool bReCountRegist)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@5
  CUnmannedTraderRegistItemInfo *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-88h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+28h] [bp-60h]@4
  bool v10; // [sp+44h] [bp-44h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v11; // [sp+48h] [bp-40h]@4
  char v12; // [sp+60h] [bp-28h]@5
  char v13; // [sp+61h] [bp-27h]@7
  char v14; // [sp+62h] [bp-26h]@10
  __int64 v15; // [sp+68h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v16; // [sp+70h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v17; // [sp+78h] [bp-10h]@4
  CUnmannedTraderUserInfo *v18; // [sp+90h] [bp+8h]@1
  char v19; // [sp+A0h] [bp+18h]@1
  bool v20; // [sp+A8h] [bp+20h]@1

  v20 = bReCountRegist;
  v19 = byState;
  v18 = this;
  v4 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  CUnmannedTraderUserInfo::Find(v18, &result, dwRegistSerial);
  v16 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v18->m_vecRegistItemInfo,
          &v11);
  v17 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v16;
  v10 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v16->_Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v11);
  if ( v10 )
  {
    v12 = 0;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v6 = v12;
  }
  else
  {
    v7 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
    if ( CUnmannedTraderRegistItemInfo::SetState(v7, v19) )
    {
      if ( v20 )
        CUnmannedTraderUserInfo::CountRegistItem(v18);
      v14 = 1;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v6 = v14;
    }
    else
    {
      v13 = 0;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v6 = v13;
    }
  }
  return v6;
}
