/*
 * Function: ??4CGuildBattleSchedule@GUILD_BATTLE@@QEAAAEBV01@AEBV01@@Z
 * Address: 0x1403DA350
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleSchedule::operator=(GUILD_BATTLE::CGuildBattleSchedule *this, GUILD_BATTLE::CGuildBattleSchedule *kObj)
{
  GUILD_BATTLE::CGuildBattleSchedule *result; // rax@1

  this->m_eState = kObj->m_eState;
  this->m_kNextStartTime.m_time = kObj->m_kNextStartTime.m_time;
  this->m_kBattleTime.m_timeSpan = kObj->m_kBattleTime.m_timeSpan;
  result = this;
  this->m_pkStateList = kObj->m_pkStateList;
  return result;
}
