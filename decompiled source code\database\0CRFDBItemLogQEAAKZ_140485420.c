/*
 * Function: ??0CRFDBItemLog@@QEAA@K@Z
 * Address: 0x140485420
 */

void __fastcall CRFDBItemLog::CRFDBItemLog(CRFDBItemLog *this, unsigned int dwLocalDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRFDBItemLog *v5; // [sp+30h] [bp+8h]@1
  unsigned int v6; // [sp+38h] [bp+10h]@1

  v6 = dwLocalDate;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CRFNewDatabase::CRFNewDatabase((CRFNewDatabase *)&v5->vfptr);
  v5->vfptr = (CRFNewDatabaseVtbl *)&CRFDBItemLog::`vftable';
  v5->m_bTblLtd = 0;
  v5->m_bTblLtdItemInfo = 0;
  v5->m_bTblLtdExpend = 0;
  v5->m_dwKorTime = v6;
}
