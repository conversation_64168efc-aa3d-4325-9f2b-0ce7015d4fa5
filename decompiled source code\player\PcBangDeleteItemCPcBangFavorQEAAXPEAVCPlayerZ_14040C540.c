/*
 * Function: ?PcBangDeleteItem@CPcBangFavor@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14040C540
 */

void __fastcall CPcBangFavor::PcBangDeleteItem(CPcBangFavor *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  bool bDelete; // [sp+20h] [bp-28h]@12
  char *strErrorCodePos; // [sp+28h] [bp-20h]@12
  int j; // [sp+30h] [bp-18h]@7
  _STORAGE_LIST::_db_con *Item; // [sp+38h] [bp-10h]@7
  CPlayer *v10; // [sp+58h] [bp+10h]@1

  v10 = pOne;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne && pOne->m_bLive )
  {
    Item = 0i64;
    for ( j = 0; ; ++j )
    {
      v4 = CPlayerDB::GetBagNum(&v10->m_Param);
      if ( j >= 20 * (unsigned __int8)v4 )
        break;
      Item = &v10->m_Param.m_dbInven.m_pStorageList[j];
      if ( Item
        && Item->m_bLoad
        && _PCBANG_FAVOR_ITEM_DB_BASE::IsDeleteItem(&v10->m_pUserDB->m_AvatorData.dbPcBangFavorItem, Item) )
      {
        CPlayer::SendMsg_DeleteStorageInform(v10, 0, Item->m_wSerial);
        strErrorCodePos = 0i64;
        bDelete = 1;
        CPlayer::Emb_DelStorage(v10, 0, Item->m_byStorageIndex, 0, 1, 0i64);
      }
    }
    for ( j = 0; j < 8; ++j )
    {
      Item = &v10->m_Param.m_dbEquip.m_pStorageList[j];
      if ( Item
        && Item->m_bLoad
        && _PCBANG_FAVOR_ITEM_DB_BASE::IsDeleteItem(&v10->m_pUserDB->m_AvatorData.dbPcBangFavorItem, Item) )
      {
        CPlayer::SendMsg_DeleteStorageInform(v10, 1, Item->m_wSerial);
        strErrorCodePos = 0i64;
        bDelete = 1;
        CPlayer::Emb_DelStorage(v10, 1, Item->m_byStorageIndex, 0, 1, 0i64);
      }
    }
    for ( j = 0; j < 7; ++j )
    {
      Item = &v10->m_Param.m_dbEmbellish.m_pStorageList[j];
      if ( Item
        && Item->m_bLoad
        && _PCBANG_FAVOR_ITEM_DB_BASE::IsDeleteItem(&v10->m_pUserDB->m_AvatorData.dbPcBangFavorItem, Item) )
      {
        CPlayer::SendMsg_DeleteStorageInform(v10, 2, Item->m_wSerial);
        strErrorCodePos = 0i64;
        bDelete = 1;
        CPlayer::Emb_DelStorage(v10, 2, Item->m_byStorageIndex, 0, 1, 0i64);
      }
    }
    _PCBANG_FAVOR_ITEM_DB_BASE::Init(&v10->m_pUserDB->m_AvatorData.dbPcBangFavorItem);
  }
}
