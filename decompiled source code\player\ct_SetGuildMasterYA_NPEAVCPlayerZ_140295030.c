/*
 * Function: ?ct_SetGuildMaster@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295030
 */

bool __fastcall ct_SetGuildMaster(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  unsigned int v4; // eax@9
  __int64 v5; // [sp+0h] [bp-38h]@1
  CGuild *v6; // [sp+20h] [bp-18h]@9
  _guild_member_info *pNewguildMaster; // [sp+28h] [bp-10h]@9
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8 && v8->m_bOper )
  {
    if ( v8->m_Param.m_pGuild )
    {
      v6 = v8->m_Param.m_pGuild;
      v4 = CPlayerDB::GetCharSerial(&v8->m_Param);
      pNewguildMaster = CGuild::GetMemberFromSerial(v6, v4);
      if ( pNewguildMaster )
        result = CGuild::DB_Update_GuildMaster(v6, pNewguildMaster);
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
