/*
 * Function: j_?_Destroy@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAAXPEAPEAVCUnmannedTraderDivisionInfo@@0@Z
 * Address: 0x140003D2D
 */

void __fastcall std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Destroy(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last)
{
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Destroy(
    this,
    _First,
    _Last);
}
