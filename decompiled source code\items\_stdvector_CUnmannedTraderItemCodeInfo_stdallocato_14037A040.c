/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::insert_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo______::_1_::dtor$1
 * Address: 0x14037A040
 */

void __fastcall std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::insert_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo______::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 240));
}
