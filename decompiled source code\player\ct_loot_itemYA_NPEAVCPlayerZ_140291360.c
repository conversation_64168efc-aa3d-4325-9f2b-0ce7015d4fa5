/*
 * Function: ?ct_loot_item@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291360
 */

bool __fastcall ct_loot_item(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-98h]@1
  int nNum; // [sp+30h] [bp-68h]@7
  char szTran; // [sp+48h] [bp-50h]@9
  unsigned __int64 v7; // [sp+80h] [bp-18h]@4
  CPlayer *v8; // [sp+A0h] [bp+8h]@1

  v8 = pOne;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v8 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      nNum = 1;
      if ( s_nWordCount >= 2 )
        nNum = atoi(s_pwszDstCheat[1]);
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      result = CPlayer::dev_loot_item(v8, &szTran, nNum, 0i64, 0);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
