/*
 * Function: ??1?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x140377B60
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::~_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>((std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &> *)&v4->_Mycont);
}
