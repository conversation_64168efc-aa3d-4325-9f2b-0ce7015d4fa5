/*
 * Function: ?SendMsg_StartContSF@CPlayer@@QEAAXPEAU_sf_continous@@@Z
 * Address: 0x1400E09B0
 */

void __fastcall CPlayer::SendMsg_StartContSF(CPlayer *this, _sf_continous *pCont)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v6; // [sp+35h] [bp-43h]@4
  unsigned __int16 v7; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1
  _sf_continous *v11; // [sp+88h] [bp+10h]@1

  v11 = pCont;
  v10 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = pCont->m_byLv;
  v6 = CCharacter::CalcEffectBit((CCharacter *)&v10->vfptr, pCont->m_byEffectCode, pCont->m_wEffectIndex);
  v7 = v11->m_wDurSec;
  pbyType = 17;
  v9 = 25;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &szMsg, 5u);
}
