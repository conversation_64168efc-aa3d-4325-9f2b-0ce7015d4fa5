#pragma once

/**
 * @file CNetworkMessageSystem.h
 * @brief Modern C++20 Network Message System
 * 
 * This file provides a comprehensive network message handling system with
 * type-safe message structures, serialization, and protocol management.
 * 
 * Refactored from decompiled sources:
 * - _InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0.c (145 lines)
 * - LoadSendMsgCNetProcessQEAAHKGPEADGZ_140479680.c (26 lines)
 * - Various message processing and handling files
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <queue>
#include <variant>
#include <optional>

namespace NexusProtection {
namespace Network {

/**
 * @brief Message type enumeration
 */
enum class MessageType : uint8_t {
    Unknown = 0,
    
    // System messages (100-109)
    SystemHeartbeat = 101,
    SystemPing = 102,
    SystemPong = 103,
    SystemDisconnect = 104,
    
    // Authentication messages (110-119)
    AuthLogin = 110,
    AuthLogout = 111,
    AuthChallenge = 112,
    AuthResponse = 113,
    
    // Game messages (120-199)
    GameEnterWorld = 120,
    GameLeaveWorld = 121,
    GameMove = 122,
    GameAttack = 123,
    GameChat = 124,
    
    // Speed hack detection (200-209)
    SpeedHackChallenge = 200,
    SpeedHackResponse = 201,
    SpeedHackVerify = 202,
    
    // Custom range (210-255)
    CustomStart = 210,
    CustomEnd = 255
};

/**
 * @brief Message priority levels
 */
enum class MessagePriority : uint8_t {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
    System = 4
};

/**
 * @brief Message processing result
 */
enum class MessageProcessResult : uint8_t {
    Success = 0,
    InvalidMessage,
    InvalidHeader,
    InvalidData,
    ProcessingError,
    SecurityViolation,
    RateLimited,
    SystemError
};

/**
 * @brief Message flags
 */
enum class MessageFlags : uint16_t {
    None = 0x0000,
    Encrypted = 0x0001,
    Compressed = 0x0002,
    Reliable = 0x0004,
    Ordered = 0x0008,
    Fragmented = 0x0010,
    Broadcast = 0x0020,
    Internal = 0x0040,
    Debug = 0x0080
};

// Enable bitwise operations for MessageFlags
inline MessageFlags operator|(MessageFlags a, MessageFlags b) {
    return static_cast<MessageFlags>(static_cast<uint16_t>(a) | static_cast<uint16_t>(b));
}

inline MessageFlags operator&(MessageFlags a, MessageFlags b) {
    return static_cast<MessageFlags>(static_cast<uint16_t>(a) & static_cast<uint16_t>(b));
}

/**
 * @brief Modern message header structure
 */
struct MessageHeader {
    uint16_t size{0};                    // Total message size including header
    MessageType type{MessageType::Unknown};
    uint8_t subType{0};
    MessageFlags flags{MessageFlags::None};
    uint32_t sequenceNumber{0};
    uint32_t timestamp{0};
    uint32_t checksum{0};
    
    MessageHeader() = default;
    MessageHeader(MessageType msgType, uint8_t subMsgType = 0, MessageFlags msgFlags = MessageFlags::None)
        : type(msgType), subType(subMsgType), flags(msgFlags) {
        timestamp = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count());
    }
    
    bool IsValid() const;
    uint32_t CalculateChecksum() const;
    void UpdateChecksum();
    
    static constexpr size_t HEADER_SIZE = sizeof(MessageHeader);
};

/**
 * @brief Message data variant type
 */
using MessageData = std::variant<
    std::vector<uint8_t>,    // Raw binary data
    std::string,             // Text data
    std::monostate           // Empty data
>;

/**
 * @brief Network message class
 */
class NetworkMessage {
public:
    NetworkMessage() = default;
    NetworkMessage(MessageType type, uint8_t subType = 0, MessageFlags flags = MessageFlags::None);
    NetworkMessage(const MessageHeader& header, const MessageData& data);
    
    // Header access
    const MessageHeader& GetHeader() const { return m_header; }
    MessageHeader& GetHeader() { return m_header; }
    
    // Data access
    const MessageData& GetData() const { return m_data; }
    void SetData(const MessageData& data) { m_data = data; }
    void SetData(MessageData&& data) { m_data = std::move(data); }
    
    // Convenience methods
    MessageType GetType() const { return m_header.type; }
    uint8_t GetSubType() const { return m_header.subType; }
    MessageFlags GetFlags() const { return m_header.flags; }
    uint16_t GetSize() const { return m_header.size; }
    
    // Data type checks
    bool HasBinaryData() const;
    bool HasTextData() const;
    bool IsEmpty() const;
    
    // Data extraction
    std::optional<std::vector<uint8_t>> GetBinaryData() const;
    std::optional<std::string> GetTextData() const;
    
    // Serialization
    std::vector<uint8_t> Serialize() const;
    bool Deserialize(const std::vector<uint8_t>& buffer);
    bool Deserialize(const uint8_t* buffer, size_t size);
    
    // Validation
    bool IsValid() const;
    MessageProcessResult Validate() const;
    
    // Utility
    void UpdateSize();
    void UpdateTimestamp();
    void UpdateChecksum();
    
private:
    MessageHeader m_header;
    MessageData m_data;
};

/**
 * @brief Message handler function type
 */
using MessageHandler = std::function<MessageProcessResult(uint32_t clientId, const NetworkMessage& message)>;

/**
 * @brief Message statistics
 */
struct MessageStatistics {
    std::atomic<uint64_t> totalMessages{0};
    std::atomic<uint64_t> processedMessages{0};
    std::atomic<uint64_t> failedMessages{0};
    std::atomic<uint64_t> droppedMessages{0};
    std::atomic<uint64_t> bytesReceived{0};
    std::atomic<uint64_t> bytesSent{0};
    std::chrono::steady_clock::time_point startTime;
    
    MessageStatistics() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetSuccessRate() const {
        uint64_t total = totalMessages.load();
        return total > 0 ? (static_cast<double>(processedMessages.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Message queue entry
 */
struct MessageQueueEntry {
    NetworkMessage message;
    uint32_t clientId{0};
    MessagePriority priority{MessagePriority::Normal};
    std::chrono::steady_clock::time_point timestamp;
    uint32_t retryCount{0};
    
    MessageQueueEntry() : timestamp(std::chrono::steady_clock::now()) {}
    MessageQueueEntry(const NetworkMessage& msg, uint32_t id, MessagePriority prio = MessagePriority::Normal)
        : message(msg), clientId(id), priority(prio), timestamp(std::chrono::steady_clock::now()) {}
    
    bool operator<(const MessageQueueEntry& other) const {
        // Higher priority messages come first
        if (priority != other.priority) {
            return priority < other.priority;
        }
        // Earlier timestamps come first for same priority
        return timestamp > other.timestamp;
    }
};

/**
 * @brief Modern C++20 Network Message System class
 * 
 * This class provides comprehensive network message handling with type-safe
 * message structures, serialization, and protocol management.
 */
class CNetworkMessageSystem {
public:
    // Constructor and Destructor
    CNetworkMessageSystem();
    virtual ~CNetworkMessageSystem();

    // Disable copy constructor and assignment operator
    CNetworkMessageSystem(const CNetworkMessageSystem&) = delete;
    CNetworkMessageSystem& operator=(const CNetworkMessageSystem&) = delete;

    // Enable move constructor and assignment operator
    CNetworkMessageSystem(CNetworkMessageSystem&&) noexcept = default;
    CNetworkMessageSystem& operator=(CNetworkMessageSystem&&) noexcept = default;

    /**
     * @brief Initialize message system
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown message system
     */
    void Shutdown();

    /**
     * @brief Register message handler
     * 
     * @param type Message type to handle
     * @param handler Handler function
     * @return true if registration successful, false otherwise
     */
    bool RegisterHandler(MessageType type, MessageHandler handler);

    /**
     * @brief Unregister message handler
     * 
     * @param type Message type to unregister
     * @return true if unregistration successful, false otherwise
     */
    bool UnregisterHandler(MessageType type);

    /**
     * @brief Process incoming message
     * 
     * @param clientId Client identifier
     * @param buffer Message buffer
     * @param size Buffer size
     * @return Message processing result
     */
    MessageProcessResult ProcessMessage(uint32_t clientId, const uint8_t* buffer, size_t size);

    /**
     * @brief Process incoming message
     * 
     * @param clientId Client identifier
     * @param message Network message
     * @return Message processing result
     */
    MessageProcessResult ProcessMessage(uint32_t clientId, const NetworkMessage& message);

    /**
     * @brief Queue outgoing message
     * 
     * @param clientId Target client identifier
     * @param message Message to send
     * @param priority Message priority
     * @return true if queued successfully, false otherwise
     */
    bool QueueMessage(uint32_t clientId, const NetworkMessage& message, 
                     MessagePriority priority = MessagePriority::Normal);

    /**
     * @brief Send message immediately
     * 
     * @param clientId Target client identifier
     * @param message Message to send
     * @return true if sent successfully, false otherwise
     */
    bool SendMessage(uint32_t clientId, const NetworkMessage& message);

    /**
     * @brief Process message queue
     * 
     * Processes pending outgoing messages from the queue.
     * 
     * @param maxMessages Maximum number of messages to process
     * @return Number of messages processed
     */
    size_t ProcessMessageQueue(size_t maxMessages = 100);

    /**
     * @brief Get message statistics
     * 
     * @return Current message statistics
     */
    const MessageStatistics& GetStatistics() const { return m_statistics; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Check if message type is registered
     * 
     * @param type Message type to check
     * @return true if registered, false otherwise
     */
    bool IsHandlerRegistered(MessageType type) const;

    /**
     * @brief Get queue size
     * 
     * @return Number of messages in queue
     */
    size_t GetQueueSize() const;

    /**
     * @brief Clear message queue
     */
    void ClearQueue();

protected:
    // Message handlers
    std::unordered_map<MessageType, MessageHandler> m_handlers;
    
    // Message queue
    std::priority_queue<MessageQueueEntry> m_messageQueue;
    
    // Statistics
    MessageStatistics m_statistics;
    
    // Synchronization
    mutable std::mutex m_handlersMutex;
    mutable std::mutex m_queueMutex;
    mutable std::mutex m_statisticsMutex;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};

private:
    /**
     * @brief Validate message header
     * 
     * @param header Message header to validate
     * @return true if valid, false otherwise
     */
    bool ValidateMessageHeader(const MessageHeader& header) const;

    /**
     * @brief Update statistics
     * 
     * @param result Processing result
     * @param messageSize Message size in bytes
     */
    void UpdateStatistics(MessageProcessResult result, size_t messageSize);

    /**
     * @brief Register default handlers
     */
    void RegisterDefaultHandlers();

    /**
     * @brief Handle system message
     * 
     * @param clientId Client identifier
     * @param message System message
     * @return Processing result
     */
    MessageProcessResult HandleSystemMessage(uint32_t clientId, const NetworkMessage& message);

    /**
     * @brief Handle speed hack message
     * 
     * @param clientId Client identifier
     * @param message Speed hack message
     * @return Processing result
     */
    MessageProcessResult HandleSpeedHackMessage(uint32_t clientId, const NetworkMessage& message);
};

/**
 * @brief Network Message System Factory
 */
class CNetworkMessageSystemFactory {
public:
    /**
     * @brief Create message system
     * 
     * @return Unique pointer to message system
     */
    static std::unique_ptr<CNetworkMessageSystem> CreateMessageSystem();
};

/**
 * @brief Message utility functions
 */
namespace MessageUtils {
    std::string MessageTypeToString(MessageType type);
    MessageType StringToMessageType(const std::string& typeStr);
    std::string MessagePriorityToString(MessagePriority priority);
    MessagePriority StringToMessagePriority(const std::string& priorityStr);
    std::string MessageFlagsToString(MessageFlags flags);
    MessageFlags StringToMessageFlags(const std::string& flagsStr);
}

} // namespace Network
} // namespace NexusProtection
