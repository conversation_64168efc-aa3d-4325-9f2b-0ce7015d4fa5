/*
 * Function: ?PushM<PERSON>ber@CDarkHoleChannel@@QEAA_NPEAVCPlayer@@_NPEAVCMapData@@GPEAM@Z
 * Address: 0x14026A8C0
 */

char __fastcall CDarkHoleChannel::PushMember(CDarkHoleChannel *this, CPlayer *pMember, bool bReconnect, CMapData *pOldMap, unsigned __int16 wLastLayer, float *pfOldPos)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v9; // [sp+0h] [bp-58h]@1
  _dh_player_mgr *v10; // [sp+20h] [bp-38h]@6
  int j; // [sp+28h] [bp-30h]@6
  CDarkHoleChannel::__enter_member v12; // [sp+38h] [bp-20h]@16
  CDarkHoleChannel *v13; // [sp+60h] [bp+8h]@1
  CPlayer *pNewMember; // [sp+68h] [bp+10h]@1
  bool v15; // [sp+70h] [bp+18h]@1
  CMapData *map; // [sp+78h] [bp+20h]@1

  map = pOldMap;
  v15 = bReconnect;
  pNewMember = pMember;
  v13 = this;
  v6 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( CDarkHoleChannel::GetCurrentMemberNum(v13) < 32 )
  {
    v10 = 0i64;
    for ( j = 0; j < 32; ++j )
    {
      if ( !_dh_player_mgr::IsFill(&v13->m_Quester[j]) )
      {
        v10 = &v13->m_Quester[j];
        break;
      }
    }
    if ( v10 )
    {
      v10->pOne = pNewMember;
      v10->dwSerial = pNewMember->m_dwObjSerial;
      _dh_player_mgr::_pos::set(&v10->LastPos, map, wLastLayer, pfOldPos);
      v10->nEnterOrder = v13->m_dwEnterOrderCounter++;
      CDarkHoleChannel::SendMsg_NewMember(v13, pNewMember, v15);
      if ( v13->m_dwOpenerSerial == pNewMember->m_dwObjSerial )
        v13->m_pLeaderPtr = v10;
      CDarkHoleChannel::SendMsg_LeaderChange(v13, pNewMember);
      CDarkHoleChannel::SendMsg_QuestInfo(v13, pNewMember);
      CDarkHoleChannel::SendMsg_MissionInfo(v13, pNewMember);
      CDarkHoleChannel::SendMsg_MemberInfo(v13, pNewMember);
      pNewMember->m_pDHChannel = v13;
      if ( !CIndexList::IsInList(&v13->m_listEnterMember, pNewMember->m_dwObjSerial, 0i64) )
      {
        CDarkHoleChannel::__enter_member::__enter_member(&v12, 1, 0, 0);
        CIndexList::PushNode_Back(&v13->m_listEnterMember, pNewMember->m_dwObjSerial, (char *)&v12);
      }
      v13->m_bCheckMemberClose = 1;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
