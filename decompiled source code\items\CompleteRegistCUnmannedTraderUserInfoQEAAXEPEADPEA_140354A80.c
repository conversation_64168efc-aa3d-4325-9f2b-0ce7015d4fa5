/*
 * Function: ?CompleteRegist@CUnmannedTraderUserInfo@@QEAAXEPEADPEAVCLogFile@@@Z
 * Address: 0x140354A80
 */

void __fastcall CUnmannedTraderUserInfo::CompleteRegist(CUnmannedTraderUserInfo *this, char byRet, char *pLoadData, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // edi@13
  char *v7; // rax@13
  int v8; // eax@14
  unsigned int v9; // eax@16
  int v10; // ecx@16
  unsigned int v11; // eax@16
  CUnmannedTraderTaxRateManager *v12; // rax@17
  CHonorGuild *v13; // rax@17
  int v14; // eax@18
  CUnmannedTraderTaxRateManager *v15; // rax@21
  CHonorGuild *v16; // rax@21
  CUnmannedTraderGroupItemInfoTable *v17; // rax@22
  int v18; // edi@23
  char *v19; // rax@23
  int v20; // eax@26
  __int64 v21; // [sp+0h] [bp-938h]@1
  unsigned int dwPrice; // [sp+20h] [bp-918h]@12
  char bySellTurm[8]; // [sp+28h] [bp-910h]@12
  char byTableCode[8]; // [sp+30h] [bp-908h]@12
  unsigned __int16 wItemIndex[4]; // [sp+38h] [bp-900h]@12
  char byStorageIndex[4]; // [sp+40h] [bp-8F8h]@12
  unsigned __int64 dwD; // [sp+48h] [bp-8F0h]@12
  unsigned int dwU; // [sp+50h] [bp-8E8h]@12
  bool bInserted[4]; // [sp+58h] [bp-8E0h]@12
  int v30; // [sp+60h] [bp-8D8h]@13
  int v31; // [sp+68h] [bp-8D0h]@13
  int v32; // [sp+70h] [bp-8C8h]@13
  char *v33; // [sp+80h] [bp-8B8h]@4
  unsigned __int16 v34; // [sp+88h] [bp-8B0h]@7
  CPlayer *v35; // [sp+90h] [bp-8A8h]@7
  _STORAGE_LIST::_storage_con *v36; // [sp+98h] [bp-8A0h]@7
  _INVENKEY v37; // [sp+A4h] [bp-894h]@12
  char szStateStr; // [sp+D0h] [bp-868h]@13
  char v39; // [sp+D1h] [bp-867h]@13
  unsigned int v40; // [sp+8D4h] [bp-64h]@17
  unsigned int dwTax; // [sp+8D8h] [bp-60h]@17
  unsigned __int16 v42; // [sp+8E8h] [bp-50h]@5
  int v43; // [sp+8ECh] [bp-4Ch]@13
  int v44; // [sp+8F0h] [bp-48h]@13
  int v45; // [sp+8F4h] [bp-44h]@13
  char *pszFileName; // [sp+8F8h] [bp-40h]@16
  int v47; // [sp+900h] [bp-38h]@17
  int v48; // [sp+904h] [bp-34h]@17
  int v49; // [sp+908h] [bp-30h]@18
  int v50; // [sp+90Ch] [bp-2Ch]@21
  int v51; // [sp+910h] [bp-28h]@21
  int v52; // [sp+914h] [bp-24h]@23
  int v53; // [sp+918h] [bp-20h]@23
  int v54; // [sp+91Ch] [bp-1Ch]@23
  unsigned __int64 v55; // [sp+920h] [bp-18h]@4
  CUnmannedTraderUserInfo *v56; // [sp+940h] [bp+8h]@1
  char v57; // [sp+948h] [bp+10h]@1
  char *pLoadDataa; // [sp+950h] [bp+18h]@1
  CLogFile *v59; // [sp+958h] [bp+20h]@1

  v59 = pkLogger;
  pLoadDataa = pLoadData;
  v57 = byRet;
  v56 = this;
  v4 = &v21;
  for ( i = 588i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v55 = (unsigned __int64)&v21 ^ _security_cookie;
  v33 = pLoadData;
  if ( *((_WORD *)pLoadData + 4) == 0xFFFF )
    v42 = *((_WORD *)v33 + 1);
  else
    v42 = *((_WORD *)v33 + 4);
  v34 = v42;
  v35 = CUnmannedTraderUserInfo::FindOwner(v56);
  v36 = 0i64;
  if ( v35 && v35->m_bOper )
  {
    v36 = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                           (_STORAGE_LIST *)&v35->m_Param.m_dbInven.m_nListNum,
                                           v34);
    if ( v36 )
    {
      if ( v57 )
      {
        if ( *((_WORD *)v33 + 4) != 0xFFFF )
        {
          v20 = (unsigned __int8)v33[10];
          bySellTurm[0] = 0;
          LOBYTE(dwPrice) = 0;
          CPlayer::Emb_AlterDurPoint(v35, 0, v33[16], v20, 0, 0);
          *(_QWORD *)bySellTurm = "CUnmannedTraderUserInfo::CompleteRegist()";
          LOBYTE(dwPrice) = 0;
          CPlayer::Emb_DelStorage(v35, 0, v33[17], 0, 0, "CUnmannedTraderUserInfo::CompleteRegist()");
        }
        _STORAGE_LIST::_storage_con::lock(v36, 0);
        CPlayer::AddDalant(v35, *((_DWORD *)v33 + 20), 1);
        dwPrice = CPlayerDB::GetDalant(&v35->m_Param);
        CUnmannedTraderUserInfo::SendRegistItemErrorResult(v56, v35->m_ObjID.m_wIndex, 24, v34, dwPrice);
      }
      else
      {
        _INVENKEY::_INVENKEY(&v37);
        _INVENKEY::LoadDBKey(&v37, *((_DWORD *)v33 + 10));
        bInserted[0] = v33[24];
        dwU = *((_DWORD *)v33 + 14);
        dwD = *((_QWORD *)v33 + 6);
        byStorageIndex[0] = v33[17];
        wItemIndex[0] = v37.wItemIndex;
        byTableCode[0] = v37.byTableCode;
        bySellTurm[0] = v33[26];
        dwPrice = *((_DWORD *)v33 + 8);
        if ( CUnmannedTraderUserInfo::CompleteRegistItem(
               v56,
               *((_DWORD *)v33 + 5),
               v34,
               v36->m_dwETSerialNumber,
               dwPrice,
               bySellTurm[0],
               v37.byTableCode,
               v37.wItemIndex,
               byStorageIndex[0],
               dwD,
               dwU,
               bInserted[0]) )
        {
          pszFileName = v35->m_szItemHistoryFileName;
          v9 = CPlayerDB::GetDalant(&v35->m_Param);
          v10 = v35->m_ObjID.m_wIndex;
          *(_QWORD *)wItemIndex = pszFileName;
          *(_DWORD *)byTableCode = v9;
          *(_DWORD *)bySellTurm = *((_DWORD *)v33 + 20);
          dwPrice = *((_DWORD *)v33 + 8);
          CMgrAvatorItemHistory::reg_auto_trade(
            &CPlayer::s_MgrItemHistory,
            v10,
            *((_DWORD *)v33 + 5),
            (_STORAGE_LIST::_db_con *)v36,
            dwPrice,
            *(unsigned int *)bySellTurm,
            v9,
            pszFileName);
          v11 = CPlayerDB::GetDalant(&v35->m_Param);
          CUnmannedTraderUserInfo::SendRegistItemSuccessResult(v56, v11, v35->m_ObjID.m_wIndex, pLoadDataa);
          if ( CHolyStoneSystem::GetHolyMasterRace(&g_HolySys) == -1 )
          {
            v40 = 75 * *((_DWORD *)v33 + 20) / 0x64u;
            dwTax = v40 / 2;
            v47 = CPlayerDB::GetRaceCode(&v35->m_Param);
            v12 = CUnmannedTraderTaxRateManager::Instance();
            CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(v12, v47, dwTax);
            v48 = CPlayerDB::GetRaceCode(&v35->m_Param);
            v13 = CHonorGuild::Instance();
            CHonorGuild::SetGuildMaintainMoney(v13, v48, v40, v35->m_dwObjSerial);
          }
          else
          {
            v49 = CPlayerDB::GetRaceCode(&v35->m_Param);
            v14 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
            if ( v49 == v14 )
            {
              v40 = 75 * *((_DWORD *)v33 + 20) / 0x64u;
              dwTax = v40 / 2;
            }
            else
            {
              v40 = 50 * *((_DWORD *)v33 + 20) / 0x64u;
              dwTax = v40 / 2;
            }
            v50 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
            v15 = CUnmannedTraderTaxRateManager::Instance();
            CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(v15, v50, dwTax);
            v51 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
            v16 = CHonorGuild::Instance();
            CHonorGuild::SetGuildMaintainMoney(v16, v51, v40, v35->m_dwObjSerial);
          }
          v17 = CUnmannedTraderGroupItemInfoTable::Instance();
          LOBYTE(dwPrice) = v33[63];
          if ( !CUnmannedTraderGroupItemInfoTable::IncreaseVersion(
                  v17,
                  v37.byTableCode,
                  v37.wItemIndex,
                  v33[62],
                  dwPrice) )
          {
            v52 = (unsigned __int8)v33[63];
            v53 = (unsigned __int8)v33[62];
            v54 = v37.wItemIndex;
            v18 = (unsigned __int8)v37.byTableCode;
            v19 = CPlayerDB::GetCharNameA(&v35->m_Param);
            LODWORD(dwD) = v52;
            *(_DWORD *)byStorageIndex = v53;
            *(_DWORD *)wItemIndex = v54;
            *(_DWORD *)byTableCode = v18;
            *(_QWORD *)bySellTurm = v19;
            dwPrice = v35->m_dwObjSerial;
            CLogFile::Write(
              v59,
              "CUnmannedTraderController::ComleteRegistItem( BYTE byRet(%u), char * pLoadData )\r\n"
              "\t\tOwner : (%u)(%u)%s\r\n"
              "\t\tCUnmannedTraderGroupItemInfoTable::Instance()->IncreaseVersion(\r\n"
              "\t\tkInvenKey.byTableCode(%u), kInvenKey.wItemIndex(%u), pkQuery->byClass1(%u), pkQuery->byClass2(%u) ) Fail!\r\n",
              0i64,
              v56->m_dwUserSerial);
          }
        }
        else
        {
          szStateStr = 0;
          memset(&v39, 0, 0x7FFui64);
          CUnmannedTraderUserInfo::GetCurrentRegItemStateStr(v56, &szStateStr, 2048);
          v43 = v37.wItemIndex;
          v44 = (unsigned __int8)v37.byTableCode;
          v45 = (unsigned __int8)v33[26];
          v6 = v34;
          v7 = CPlayerDB::GetCharNameA(&v35->m_Param);
          v32 = v43;
          v31 = v44;
          v30 = v45;
          *(_DWORD *)bInserted = *((_DWORD *)v33 + 20);
          dwU = *((_DWORD *)v33 + 8);
          LODWORD(dwD) = v36->m_dwETSerialNumber;
          *(_DWORD *)byStorageIndex = v6;
          *(_DWORD *)wItemIndex = *((_DWORD *)v33 + 5);
          *(_QWORD *)byTableCode = &szStateStr;
          *(_QWORD *)bySellTurm = v7;
          dwPrice = v35->m_dwObjSerial;
          CLogFile::Write(
            v59,
            "CUnmannedTraderUserInfo::ComleteRegistItem( BYTE byRet(%u), char * pLoadData )\r\n"
            "\t\tOwner : (%u)(%u)%s\r\n"
            "\t\tCurrent Reg Item State %s\r\n"
            "\t\tCompleteRegistItem( pkQuery->dwRegedSerial(%u), wItemSerial(%u), pRegItem->m_dwETSerialNumber(%u), pkQue"
            "ry->dwPrice(%u), pkQuery->dwTax(%u)\r\n"
            "\t\t, pkQuery->bySellTurm(%u), kInvenKey.byTableCode(%u), kInvenKey.wItemIndex(%u)\r\n"
            "\t\t, pkQuery->bInserted, pkLogger ) Fail!\r\n",
            0i64,
            v56->m_dwUserSerial);
          if ( *((_WORD *)v33 + 4) != 0xFFFF )
          {
            v8 = (unsigned __int8)v33[10];
            bySellTurm[0] = 0;
            LOBYTE(dwPrice) = 0;
            CPlayer::Emb_AlterDurPoint(v35, 0, v33[16], v8, 0, 0);
            *(_QWORD *)bySellTurm = "CUnmannedTraderUserInfo::CompleteRegist()";
            LOBYTE(dwPrice) = 0;
            CPlayer::Emb_DelStorage(v35, 0, v33[17], 0, 0, "CUnmannedTraderUserInfo::CompleteRegist()");
          }
          _STORAGE_LIST::_storage_con::lock(v36, 0);
          CPlayer::AddDalant(v35, *((_DWORD *)v33 + 20), 1);
          dwPrice = CPlayerDB::GetDalant(&v35->m_Param);
          CUnmannedTraderUserInfo::SendRegistItemErrorResult(v56, v35->m_ObjID.m_wIndex, 6, v34, dwPrice);
          *(_WORD *)byTableCode = -1;
          bySellTurm[0] = -1;
          LOWORD(dwPrice) = v34;
          CUnmannedTraderItemState::PushUpdateState(
            v33[25],
            *((_DWORD *)v33 + 5),
            12,
            *((_DWORD *)v33 + 7),
            v34,
            -1,
            0xFFFFu);
        }
      }
    }
    else
    {
      CLogFile::Write(
        v59,
        "CUnmannedTraderUserInfo::CompleteRegist( BYTE byRet, char * pLoadData, CLogFile * pkLogger )\r\n"
        "\t\tpkRegister->m_Param.m_dbInven.GetPtrFromSerial( wItemSerial(%u) ) NULL!\r\n",
        v34);
    }
  }
}
