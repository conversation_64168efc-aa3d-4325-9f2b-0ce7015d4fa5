/*
 * Function: ?isConEventTime@CashItemRemoteStore@@QEAA_NXZ
 * Address: 0x1402FBB80
 */

bool __fastcall CashItemRemoteStore::isConEventTime(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@6
  CashItemRemoteStore *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_con_event.m_ini.m_bUseConEvent )
  {
    _time32(&Time);
    result = v6->m_con_event.m_eventtime.m_EventTime[0] <= Time && v6->m_con_event.m_eventtime.m_EventTime[1] >= Time;
  }
  else
  {
    result = 0;
  }
  return result;
}
