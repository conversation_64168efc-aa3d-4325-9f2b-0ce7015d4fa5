#pragma once

/**
 * @file CGuildRankingManager.h
 * @brief Modern C++20 Guild Ranking System
 * 
 * This file provides comprehensive guild ranking and statistics system with
 * real-time updates, leaderboards, and performance tracking.
 * 
 * Refactored from decompiled sources:
 * - CheckRecordCGuildBattleRankManagerGUILD_BATTLEIEAA_1403CB7C0.c
 * - SelectGuildBattleRankListCGuildBattleRankManagerGU_1403CAF70.c
 * - UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLEQE_1403CAFF0.c
 * - LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA610.c
 * - ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_1403CB4B0.c
 * - CreateGuildBattleRankTableCRFWorldDatabaseQEAA_NPE_1404A3810.c
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <optional>
#include <array>

// Forward declarations
class CRFWorldDatabase;

namespace NexusProtection {
namespace Guild {

/**
 * @brief Guild race enumeration
 */
enum class GuildRace : uint8_t {
    Bellato = 0,
    Cora = 1,
    Accretia = 2,
    All = 3
};

/**
 * @brief Guild ranking sort criteria
 */
enum class RankingSortCriteria : uint8_t {
    Score = 0,
    Wins = 1,
    WinRate = 2,
    Draws = 3,
    Losses = 4,
    TotalBattles = 5
};

/**
 * @brief Guild ranking entry
 */
struct GuildRankingEntry {
    uint32_t rank{0};
    uint32_t guildSerial{0};
    uint8_t race{0};
    uint8_t grade{0};
    std::string guildName;
    uint32_t wins{0};
    uint32_t draws{0};
    uint32_t losses{0};
    uint32_t score{0};
    double winRate{0.0};
    uint32_t totalBattles{0};
    std::chrono::system_clock::time_point lastBattleTime;
    
    GuildRankingEntry() = default;
    GuildRankingEntry(uint32_t serial, uint8_t guildRace, const std::string& name)
        : guildSerial(serial), race(guildRace), guildName(name) {
        lastBattleTime = std::chrono::system_clock::now();
    }
    
    void UpdateStats(uint32_t newWins, uint32_t newDraws, uint32_t newLosses, uint32_t newScore) {
        wins = newWins;
        draws = newDraws;
        losses = newLosses;
        score = newScore;
        totalBattles = wins + draws + losses;
        winRate = totalBattles > 0 ? (static_cast<double>(wins) / totalBattles) * 100.0 : 0.0;
        lastBattleTime = std::chrono::system_clock::now();
    }
    
    void AddBattleResult(bool isWin, bool isDraw, uint32_t scoreGained) {
        if (isWin) {
            wins++;
        } else if (isDraw) {
            draws++;
        } else {
            losses++;
        }
        score += scoreGained;
        totalBattles = wins + draws + losses;
        winRate = totalBattles > 0 ? (static_cast<double>(wins) / totalBattles) * 100.0 : 0.0;
        lastBattleTime = std::chrono::system_clock::now();
    }
};

/**
 * @brief Guild ranking page data
 */
struct GuildRankingPage {
    uint8_t pageNumber{0};
    uint8_t totalPages{0};
    uint32_t version{0};
    std::vector<GuildRankingEntry> entries;
    std::chrono::system_clock::time_point lastUpdate;
    
    static constexpr size_t ENTRIES_PER_PAGE = 20;
    
    GuildRankingPage() : lastUpdate(std::chrono::system_clock::now()) {}
    
    bool IsValid() const {
        return !entries.empty() && entries.size() <= ENTRIES_PER_PAGE;
    }
    
    void Clear() {
        entries.clear();
        lastUpdate = std::chrono::system_clock::now();
        version++;
    }
};

/**
 * @brief Guild ranking statistics
 */
struct GuildRankingStats {
    std::atomic<uint64_t> totalRankingUpdates{0};
    std::atomic<uint64_t> totalBattleResults{0};
    std::atomic<uint64_t> totalGuildsRanked{0};
    std::atomic<uint32_t> activeGuilds{0};
    std::array<std::atomic<uint32_t>, 3> guildsByRace{};
    std::chrono::steady_clock::time_point startTime;
    
    GuildRankingStats() : startTime(std::chrono::steady_clock::now()) {}
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Guild ranking search result
 */
struct GuildRankingSearchResult {
    bool found{false};
    uint32_t rank{0};
    uint8_t page{0};
    int32_t indexInPage{-1};
    GuildRankingEntry entry;
    
    GuildRankingSearchResult() = default;
    GuildRankingSearchResult(bool isFound, uint32_t guildRank, uint8_t pageNum, int32_t index)
        : found(isFound), rank(guildRank), page(pageNum), indexInPage(index) {}
};

/**
 * @brief Modern C++20 Guild Ranking Manager class
 * 
 * This class provides comprehensive guild ranking and statistics system with
 * real-time updates, leaderboards, and performance tracking.
 */
class CGuildRankingManager {
public:
    // Constructor and Destructor
    CGuildRankingManager();
    virtual ~CGuildRankingManager();

    // Disable copy constructor and assignment operator
    CGuildRankingManager(const CGuildRankingManager&) = delete;
    CGuildRankingManager& operator=(const CGuildRankingManager&) = delete;

    // Enable move constructor and assignment operator
    CGuildRankingManager(CGuildRankingManager&&) noexcept = default;
    CGuildRankingManager& operator=(CGuildRankingManager&&) noexcept = default;

    /**
     * @brief Initialize guild ranking manager
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown guild ranking manager
     */
    void Shutdown();

    /**
     * @brief Load guild ranking data
     * 
     * Modern implementation of CGuildBattleRankManager::Load method.
     * 
     * @return true if loading successful, false otherwise
     */
    bool LoadData();

    /**
     * @brief Load guild ranking data for specific race
     * 
     * @param race Guild race
     * @return true if loading successful, false otherwise
     */
    bool LoadData(GuildRace race);

    /**
     * @brief Update guild ranking after battle
     * 
     * Modern implementation of CGuildBattleRankManager::UpdateWinLose method.
     * 
     * @param winnerRace Winner guild race
     * @param winnerGuildSerial Winner guild serial
     * @param loserRace Loser guild race
     * @param loserGuildSerial Loser guild serial
     * @param isDraw Whether the battle was a draw
     * @return true if update successful, false otherwise
     */
    bool UpdateBattleResult(GuildRace winnerRace, uint32_t winnerGuildSerial,
                           GuildRace loserRace, uint32_t loserGuildSerial, bool isDraw = false);

    /**
     * @brief Get guild ranking list
     * 
     * Modern implementation of CGuildBattleRankManager::SelectGuildBattleRankList method.
     * 
     * @param race Guild race
     * @param page Page number (0-based)
     * @return Optional ranking page data
     */
    std::optional<GuildRankingPage> GetRankingList(GuildRace race, uint8_t page = 0);

    /**
     * @brief Send ranking list to client
     * 
     * Modern implementation of CGuildBattleRankManager::Send method.
     * 
     * @param clientIndex Client index
     * @param selfRace Client's race
     * @param currentVersion Current version
     * @param tabRace Requested race tab
     * @param page Requested page
     * @param guildSerial Client's guild serial
     * @return true if sent successfully, false otherwise
     */
    bool SendRankingList(int clientIndex, GuildRace selfRace, uint32_t currentVersion,
                        GuildRace tabRace, uint8_t page, uint32_t guildSerial);

    /**
     * @brief Find guild in rankings
     * 
     * Modern implementation of CGuildBattleRankManager::Find method.
     * 
     * @param race Guild race
     * @param guildSerial Guild serial
     * @return Search result with rank and page information
     */
    GuildRankingSearchResult FindGuild(GuildRace race, uint32_t guildSerial);

    /**
     * @brief Check if guild has ranking record
     * 
     * Modern implementation of CGuildBattleRankManager::CheckRecord method.
     * 
     * @param guildSerial Guild serial
     * @return true if record exists or created, false otherwise
     */
    bool CheckRecord(uint32_t guildSerial);

    /**
     * @brief Clear rankings for specific race
     * 
     * Modern implementation of CGuildBattleRankManager::Clear method.
     * 
     * @param race Guild race
     */
    void ClearRankings(GuildRace race);

    /**
     * @brief Clear all rankings
     * 
     * Modern implementation of CGuildBattleRankManager::Clear method.
     */
    void ClearAllRankings();

    /**
     * @brief Update rankings from database
     * 
     * Modern implementation of CGuildBattleRankManager::Update method.
     * 
     * @param race Guild race
     * @param loadData Data buffer for loading
     * @return true if update successful, false otherwise
     */
    bool UpdateRankings(GuildRace race, uint8_t* loadData);

    /**
     * @brief Create guild battle rank table
     * 
     * Modern implementation of CreateGuildBattleRankTable method.
     * 
     * @param date Date string for table name
     * @return true if table created successfully, false otherwise
     */
    bool CreateRankTable(const std::string& date);

    /**
     * @brief Get ranking version for race
     * 
     * @param race Guild race
     * @return Current version number
     */
    uint32_t GetVersion(GuildRace race) const;

    /**
     * @brief Get total pages for race
     * 
     * @param race Guild race
     * @return Total number of pages
     */
    uint8_t GetTotalPages(GuildRace race) const;

    /**
     * @brief Get guild ranking statistics
     * 
     * @return Current ranking statistics
     */
    const GuildRankingStats& GetStatistics() const { return m_stats; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Get ranking report
     * 
     * @return Detailed ranking system report
     */
    std::string GetRankingReport() const;

    /**
     * @brief Check if manager is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized; }

    // Singleton access (for legacy compatibility)
    static CGuildRankingManager& Instance();
    static void SetInstance(std::unique_ptr<CGuildRankingManager> instance);

protected:
    // Ranking data by race
    std::array<std::unordered_map<uint32_t, GuildRankingEntry>, 3> m_rankings;
    std::array<std::vector<GuildRankingPage>, 3> m_rankingPages;
    std::array<std::atomic<uint32_t>, 3> m_versions{};
    
    // Database connection
    std::shared_ptr<CRFWorldDatabase> m_database;
    
    // Statistics
    GuildRankingStats m_stats;
    
    // Synchronization
    mutable std::array<std::mutex, 3> m_rankingMutexes;
    mutable std::mutex m_databaseMutex;
    mutable std::mutex m_statsMutex;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    
    // Configuration
    RankingSortCriteria m_sortCriteria{RankingSortCriteria::Score};
    
    // Singleton instance
    static std::unique_ptr<CGuildRankingManager> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Sort rankings for race
     * 
     * @param race Guild race
     */
    void SortRankings(GuildRace race);

    /**
     * @brief Update ranking pages for race
     * 
     * @param race Guild race
     */
    void UpdateRankingPages(GuildRace race);

    /**
     * @brief Load rankings from database
     * 
     * @param race Guild race
     * @return true if successful, false otherwise
     */
    bool LoadRankingsFromDatabase(GuildRace race);

    /**
     * @brief Save rankings to database
     * 
     * @param race Guild race
     * @return true if successful, false otherwise
     */
    bool SaveRankingsToDatabase(GuildRace race);

    /**
     * @brief Calculate guild score
     * 
     * @param wins Number of wins
     * @param draws Number of draws
     * @param losses Number of losses
     * @return Calculated score
     */
    uint32_t CalculateScore(uint32_t wins, uint32_t draws, uint32_t losses) const;

    /**
     * @brief Validate race parameter
     * 
     * @param race Guild race
     * @return true if valid, false otherwise
     */
    bool IsValidRace(GuildRace race) const;

    /**
     * @brief Get race index for arrays
     * 
     * @param race Guild race
     * @return Array index
     */
    size_t GetRaceIndex(GuildRace race) const;

    /**
     * @brief Update statistics
     * 
     * @param race Guild race
     */
    void UpdateStatistics(GuildRace race);

    /**
     * @brief Serialize ranking page for network
     * 
     * @param page Ranking page
     * @param buffer Output buffer
     * @param bufferSize Buffer size
     * @return Number of bytes written
     */
    size_t SerializeRankingPage(const GuildRankingPage& page, uint8_t* buffer, size_t bufferSize) const;
};

/**
 * @brief Guild Ranking Manager Factory
 */
class CGuildRankingManagerFactory {
public:
    /**
     * @brief Create guild ranking manager
     * 
     * @return Unique pointer to ranking manager
     */
    static std::unique_ptr<CGuildRankingManager> CreateRankingManager();

    /**
     * @brief Create guild ranking manager with database
     * 
     * @param database Database connection
     * @return Unique pointer to ranking manager
     */
    static std::unique_ptr<CGuildRankingManager> CreateRankingManager(
        std::shared_ptr<CRFWorldDatabase> database);
};

/**
 * @brief Guild ranking utility functions
 */
namespace GuildRankingUtils {
    std::string GuildRaceToString(GuildRace race);
    std::string RankingSortCriteriaToString(RankingSortCriteria criteria);
    GuildRace StringToGuildRace(const std::string& raceStr);
    RankingSortCriteria StringToRankingSortCriteria(const std::string& criteriaStr);
    uint8_t GuildRaceToIndex(GuildRace race);
    GuildRace IndexToGuildRace(uint8_t index);
    bool IsValidGuildSerial(uint32_t guildSerial);
    std::string FormatRankingEntry(const GuildRankingEntry& entry);
}

} // namespace Guild
} // namespace NexusProtection
