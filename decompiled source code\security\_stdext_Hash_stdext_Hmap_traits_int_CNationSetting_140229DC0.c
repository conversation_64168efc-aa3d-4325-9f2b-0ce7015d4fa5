/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0___::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0____::_1_::dtor$0
 * Address: 0x140229DC0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0___::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::~list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>((std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *)(*(_QWORD *)(a2 + 112) + 16i64));
}
