/*
 * Function: j_?GetCloseItemForPassTimeUpdateInfo@CUnmannedTraderUserInfo@@QEAA?AW4STATE@CUnmannedTraderItemState@@KAEAPEAVCPlayer@@@Z
 * Address: 0x140011423
 */

CUnmannedTraderItemState::STATE __fastcall CUnmannedTraderUserInfo::GetCloseItemForPassTimeUpdateInfo(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, CPlayer **pkOwner)
{
  return CUnmannedTraderUserInfo::GetCloseItemForPassTimeUpdateInfo(this, dwRegistSerial, pkOwner);
}
