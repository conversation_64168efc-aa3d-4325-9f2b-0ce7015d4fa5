/*
 * Function: j_?ParameterSupported@?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_NPEBD@Z
 * Address: 0x1400131DD
 */

bool __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::ParameterSupported(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *this, const char *name)
{
  return CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::ParameterSupported(
           this,
           name);
}
