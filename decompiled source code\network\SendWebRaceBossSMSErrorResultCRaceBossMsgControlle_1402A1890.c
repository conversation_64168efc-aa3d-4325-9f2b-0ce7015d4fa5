/*
 * Function: ?SendWebRaceBossSMSErrorResult@CRaceBossMsgController@@IEAAXHK@Z
 * Address: 0x1402A1890
 */

void __fastcall CRaceBossMsgController::SendWebRaceBossSMSErrorResult(CRaceBossMsgController *this, int iRet, unsigned int dwWebDBID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned int v7; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4

  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = iRet + 10;
  v7 = dwWebDBID;
  pbyType = 51;
  v9 = 13;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, &szMsg, 5u);
}
