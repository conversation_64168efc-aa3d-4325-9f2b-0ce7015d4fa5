/*
 * Function: ?CompleteCancelRegist@CUnmannedTraderUserInfo@@QEAAXEPEADPEAVCLogFile@@@Z
 * Address: 0x1403556C0
 */

void __fastcall CUnmannedTraderUserInfo::CompleteCancelRegist(CUnmannedTraderUserInfo *this, char byRet, char *pLoadData, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // ecx@11
  CUnmannedTraderGroupItemInfoTable *v7; // rax@14
  int v8; // ecx@16
  __int64 v9; // [sp+0h] [bp-58h]@1
  char *pszFileName; // [sp+20h] [bp-38h]@11
  char *v11; // [sp+30h] [bp-28h]@4
  CPlayer *v12; // [sp+38h] [bp-20h]@4
  _STORAGE_LIST::_storage_con *v13; // [sp+40h] [bp-18h]@10
  CUnmannedTraderUserInfo *v14; // [sp+60h] [bp+8h]@1
  char v15; // [sp+68h] [bp+10h]@1
  CLogFile *pkLoggera; // [sp+78h] [bp+20h]@1

  pkLoggera = pkLogger;
  v15 = byRet;
  v14 = this;
  v4 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = pLoadData;
  v12 = CUnmannedTraderUserInfo::FindOwner(v14);
  if ( v12 && v12->m_bLive )
  {
    if ( v15 || v11[45] )
    {
      CUnmannedTraderUserInfo::SendCancelRegistErrorResult(v14, v12->m_ObjID.m_wIndex, v11[45]);
    }
    else
    {
      v13 = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                             (_STORAGE_LIST *)&v12->m_Param.m_dbInven.m_nListNum,
                                             *((_WORD *)v11 + 1));
      if ( v13 )
      {
        _STORAGE_LIST::_storage_con::lock(v13, 0);
        if ( CUnmannedTraderUserInfo::CompleteCancelRegistItem(
               v14,
               *((_DWORD *)v11 + 10),
               *((_WORD *)v11 + 1),
               pkLoggera) )
        {
          v7 = CUnmannedTraderGroupItemInfoTable::Instance();
          if ( !CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v7, v13->m_byTableCode, v13->m_wItemIndex) )
            CLogFile::Write(
              pkLoggera,
              "CUnmannedTraderUserInfo::CompleteCancelRegist( BYTE byRet, char * pLoadData, CLogFile * pkLogger )\r\n"
              "\t\tCUnmannedTraderGroupItemInfoTable::Instance()->IncreaseVersion( pRegItem->m_byTableCode(%u), pRegItem-"
              ">m_wItemIndex(%u) )\r\n",
              v13->m_byTableCode,
              v13->m_wItemIndex);
          CUnmannedTraderUserInfo::SendCancelRegistSuccessResult(
            v14,
            v12->m_ObjID.m_wIndex,
            *((_WORD *)v11 + 1),
            *((_DWORD *)v11 + 10));
          v8 = v12->m_ObjID.m_wIndex;
          pszFileName = v12->m_szItemHistoryFileName;
          CMgrAvatorItemHistory::self_cancel_auto_trade(
            &CPlayer::s_MgrItemHistory,
            v8,
            *((_DWORD *)v11 + 10),
            (_STORAGE_LIST::_db_con *)v13,
            v12->m_szItemHistoryFileName);
        }
        else
        {
          CUnmannedTraderUserInfo::SendCancelRegistErrorResult(v14, v12->m_ObjID.m_wIndex, 29);
        }
      }
      else
      {
        v6 = (unsigned __int8)v11[38];
        LODWORD(pszFileName) = *((_WORD *)v11 + 1);
        CLogFile::Write(
          pkLoggera,
          "CUnmannedTraderUserInfo::CompleteCancelRegist( BYTE byRet, char * pLoadData, CLogFile * pkLogger )\r\n"
          "\t\tpkQuery->byType(%u), pkQuery->dwRegistSerial(%u)\r\n"
          "\t\tpkRegister->m_Param.m_dbInven.GetPtrFromSerial( wItemSerial(%u) ) NULL!\r\n",
          v6,
          *((_DWORD *)v11 + 10));
        CUnmannedTraderUserInfo::SendCancelRegistErrorResult(v14, v12->m_ObjID.m_wIndex, 8);
      }
    }
  }
}
