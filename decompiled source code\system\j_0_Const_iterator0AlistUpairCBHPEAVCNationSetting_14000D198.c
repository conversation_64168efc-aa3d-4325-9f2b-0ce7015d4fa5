/*
 * Function: j_??0?$_Const_iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@QEAA@XZ
 * Address: 0x14000D198
 */

void __fastcall std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::_Const_iterator<0>(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *this)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::_Const_iterator<0>(this);
}
