/*
 * Function: j_?insert@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@2@_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x14000DDFF
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::insert(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *_Where, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::insert(this, _Where, _Count, _Val);
}
