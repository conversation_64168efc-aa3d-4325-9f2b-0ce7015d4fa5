/*
 * Function: ?SetSuggested@CUnmannedTraderTaxRateManager@@QEAAXEEKPEADK@Z
 * Address: 0x14038E1B0
 */

void __fastcall CUnmannedTraderTaxRateManager::SetSuggested(CUnmannedTraderTaxRateManager *this, char byRace, char byMatterType, unsigned int dwMatterDst, char *wszMatterDst, unsigned int dwNext)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  TRC_AutoTrade **v8; // rax@7
  __int64 v9; // [sp+0h] [bp-38h]@1
  unsigned int v10; // [sp+20h] [bp-18h]@7
  CUnmannedTraderTaxRateManager *v11; // [sp+40h] [bp+8h]@1
  char v12; // [sp+48h] [bp+10h]@1
  char v13; // [sp+50h] [bp+18h]@1
  unsigned int dwMatterDsta; // [sp+58h] [bp+20h]@1

  dwMatterDsta = dwMatterDst;
  v13 = byMatterType;
  v12 = byRace;
  v11 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v11->m_vecTRC)
    && std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v11->m_vecTRC) > (unsigned __int8)v12 )
  {
    v8 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v11->m_vecTRC, (unsigned __int8)v12);
    v10 = dwNext;
    TRC_AutoTrade::set_suggested(*v8, v13, dwMatterDsta, wszMatterDst, dwNext);
  }
}
