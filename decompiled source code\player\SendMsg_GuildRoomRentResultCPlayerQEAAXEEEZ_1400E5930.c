/*
 * Function: ?SendMsg_GuildRoomRentResult@CPlayer@@QEAAXEEE@Z
 * Address: 0x1400E5930
 */

void __fastcall CPlayer::SendMsg_GuildRoomRentResult(CPlayer *this, char byRetCode, char bySubRetCode, char byRoomType)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-78h]@1
  _guildroom_rent_result_zocl v8; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v10; // [sp+55h] [bp-23h]@4
  CPlayer *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8.byRetCode = byRetCode;
  v8.bySubRetCode = bySubRetCode;
  v8.byRoomType = byRoomType;
  pbyType = 27;
  v10 = 103;
  v6 = _guildroom_rent_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &v8.byRetCode, v6);
}
