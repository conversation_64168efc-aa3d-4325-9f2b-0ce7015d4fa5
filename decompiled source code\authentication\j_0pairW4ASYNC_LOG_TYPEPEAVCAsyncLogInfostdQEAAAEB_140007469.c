/*
 * Function: j_??0?$pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@QEAA@AEBW4ASYNC_LOG_TYPE@@AEBQEAVCAsyncLogInfo@@@Z
 * Address: 0x140007469
 */

void __fastcall std::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *this, ASYNC_LOG_TYPE *_Val1, CAsyncLogInfo *const *_Val2)
{
  std::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(this, _Val1, _Val2);
}
