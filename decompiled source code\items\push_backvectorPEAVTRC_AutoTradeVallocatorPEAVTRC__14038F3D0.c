/*
 * Function: ?push_back@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAXAEBQEAVTRC_AutoTrade@@@Z
 * Address: 0x14038F3D0
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::push_back(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade *const *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  char v6; // [sp+20h] [bp-58h]@6
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result; // [sp+38h] [bp-40h]@6
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > v8; // [sp+40h] [bp-38h]@6
  unsigned __int64 v9; // [sp+58h] [bp-20h]@4
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v10; // [sp+60h] [bp-18h]@6
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v11; // [sp+80h] [bp+8h]@1
  TRC_AutoTrade **_Vala; // [sp+88h] [bp+10h]@1

  _Vala = (TRC_AutoTrade **)_Val;
  v11 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(v11);
  v4 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::capacity(v11);
  if ( v9 >= v4 )
  {
    result = (std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *)&v6;
    v10 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::end(
            v11,
            (std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *)&v6);
    std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::insert(v11, &v8, v10, _Vala);
    std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(&v8);
  }
  else
  {
    v11->_Mylast = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Ufill(v11, v11->_Mylast, 1ui64, _Vala);
  }
}
