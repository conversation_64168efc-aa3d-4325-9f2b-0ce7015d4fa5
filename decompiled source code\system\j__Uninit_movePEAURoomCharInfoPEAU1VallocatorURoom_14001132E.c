/*
 * Function: j_??$_Uninit_move@PEAURoomCharInfo@@PEAU1@V?$allocator@URoomCharInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAURoomCharInfo@@PEAU1@00AEAV?$allocator@URoomCharInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001132E
 */

RoomCharInfo *__fastcall std::_Uninit_move<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>,std::_Undefined_move_tag>(RoomCharInfo *_First, RoomCharInfo *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
