/*
 * Function: ?ReadGoods@TimeItem@@QEAA_NXZ
 * Address: 0x14030E6B0
 */

bool __fastcall TimeItem::ReadGoods(TimeItem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-D8h]@1
  char pszErrMsg; // [sp+30h] [bp-A8h]@4
  unsigned __int64 v6; // [sp+C0h] [bp-18h]@4
  TimeItem *v7; // [sp+E0h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( CRecordData::ReadRecord(&v7->_kRecTimeItem, ".\\Script\\TimerItem.dat", 0x8Cu, &pszErrMsg) )
  {
    if ( TimeItem::MakeLinkTable(v7, &pszErrMsg, 128) )
    {
      result = TimeItem::CheckGoods(v7) != 0;
    }
    else
    {
      MyMessageBox("TimeItem", &pszErrMsg);
      CLogFile::Write(&stru_1799C8F30, "Failed TimeItem::MakeLinkTable()");
      result = 0;
    }
  }
  else
  {
    MyMessageBox("TimeItem", &pszErrMsg);
    result = 0;
  }
  return result;
}
