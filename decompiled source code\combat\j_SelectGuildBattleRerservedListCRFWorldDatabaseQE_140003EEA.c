/*
 * Function: j_?SelectGuildBattleRerservedList@CRFWorldDatabase@@QEAA_NIIPEAU_worlddb_guild_battle_reserved_schedule_info@@@Z
 * Address: 0x140003EEA
 */

bool __fastcall CRFWorldDatabase::SelectGuildBattleRerservedList(CRFWorldDatabase *this, unsigned int uiStartSLID, unsigned int uiEndSLID, _worlddb_guild_battle_reserved_schedule_info *pkInfo)
{
  return CRFWorldDatabase::SelectGuildBattleRerservedList(this, uiStartSLID, uiEndSLID, pkInfo);
}
