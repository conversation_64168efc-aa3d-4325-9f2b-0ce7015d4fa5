/*
 * Function: j_?sell_item@CMgrAvatorItemHistory@@QEAAXHPEAU_sell_offer@@EKKKKPEAD@Z
 * Address: 0x1400073A6
 */

void __fastcall CMgrAvatorItemHistory::sell_item(CMgrAvatorItemHistory *this, int n, _sell_offer *pOffer, char by<PERSON><PERSON><PERSON><PERSON>, unsigned int dwIncomeDalant, unsigned int dwIncomeGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::sell_item(
    this,
    n,
    pOffer,
    byOfferNum,
    dwIncomeDalant,
    dwIncomeGold,
    dwNewDalant,
    dwNewGold,
    pszFileName);
}
