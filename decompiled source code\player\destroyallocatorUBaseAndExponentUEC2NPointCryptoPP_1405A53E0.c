/*
 * Function: ?destroy@?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A53E0
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::destroy(__int64 a1, __int64 a2)
{
  return std::_Destroy<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(a2);
}
