/*
 * Function: ?UpdateGuildMoney@CRFWorldDatabase@@QEAA_NKNN@Z
 * Address: 0x1404A0B10
 */

bool __fastcall CRFWorldDatabase::UpdateGuildMoney(CRFWorldDatabase *this, unsigned int dwSerial, long double dDalant, long double dGold)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-468h]@1
  unsigned int v8; // [sp+0h] [bp-448h]@4
  char Dest; // [sp+20h] [bp-428h]@4
  unsigned __int64 v10; // [sp+430h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+450h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = dwSerial;
  sprintf(&Dest, "update [dbo].[tbl_guild] set Dalant = %f, Gold = %f where serial = %u", dDalant, dGold);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1);
}
