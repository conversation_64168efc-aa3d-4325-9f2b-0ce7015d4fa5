/*
 * Function: j_?construct@?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@QEAAXPEAVCUnmannedTraderRegistItemInfo@@AEBV3@@Z
 * Address: 0x14000A5D8
 */

void __fastcall std::allocator<CUnmannedTraderRegistItemInfo>::construct(std::allocator<CUnmannedTraderRegistItemInfo> *this, CUnmannedTraderRegistItemInfo *_Ptr, CUnmannedTraderRegistItemInfo *_Val)
{
  std::allocator<CUnmannedTraderRegistItemInfo>::construct(this, _Ptr, _Val);
}
