/*
 * Function: ?Init@CAtlAllocator@@QEAA_NPEBDK@Z
 * Address: 0x140673F10
 */

bool __fastcall CAtlAllocator::Init(CAtlAllocator *this, const char *pszFileName, unsigned int dwMaxSize)
{
  HANDLE v3; // rax@6
  const void *v4; // rax@15
  __int64 v5; // rax@23
  void *TokenHandle; // [sp+30h] [bp-78h]@5
  DWORD v8; // [sp+38h] [bp-70h]@11
  _SYSTEM_INFO SystemInfo; // [sp+40h] [bp-68h]@12
  int v10; // [sp+70h] [bp-38h]@6
  SIZE_T dwSize; // [sp+74h] [bp-34h]@18
  CAtlTraceProcess *v12; // [sp+80h] [bp-28h]@22
  int *v13; // [sp+88h] [bp-20h]@15
  CAtlTraceProcess *v14; // [sp+90h] [bp-18h]@23
  CAtlAllocator *v15; // [sp+B0h] [bp+8h]@1
  LPCSTR v16; // [sp+B8h] [bp+10h]@1
  unsigned int v17; // [sp+C0h] [bp+18h]@1

  v17 = dwMaxSize;
  v16 = pszFileName;
  v15 = this;
  CAtlAllocator::Close(this, 0);
  if ( (v15->m_hMap || v15->m_pBufferStart)
    && CrtDbgReportW_0(
         2i64,
         (__int64)L"f:\\dd\\vctools\\vc7libs\\ship\\atlmfc\\src\\atl\\atls\\allocate.cpp",
         26i64,
         0i64) == 1 )
  {
    __debugbreak();
  }
  TokenHandle = 0i64;
  if ( !(GetVersion() & 0x80000000) )
  {
    v3 = GetCurrentThread();
    v10 = OpenThreadToken(v3, 6u, 1, &TokenHandle);
    if ( (!v10 || TokenHandle) && !RevertToSelf() )
    {
      CloseHandle(TokenHandle);
      TokenHandle = 0i64;
      goto $LN35_5;
    }
  }
  v15->m_hMap = CreateFileMappingA((HANDLE)0xFFFFFFFF, 0i64, 0x4000004u, 0, v17, v16);
  if ( v15->m_hMap )
  {
    v8 = GetLastError();
    v15->m_pBufferStart = (char *)MapViewOfFile(v15->m_hMap, 0xF001Fu, 0, 0, 0i64);
    if ( v15->m_pBufferStart )
    {
      GetSystemInfo(&SystemInfo);
      if ( v8 == 183 )
      {
        v15->m_pProcess = (CAtlTraceProcess *)v15->m_pBufferStart;
        if ( !v15->m_pProcess )
          goto $LN35_5;
        if ( !CAtlTraceProcess::Base(v15->m_pProcess) )
          goto $LN35_5;
        v13 = &v15->m_pProcess->m_iFirstCategory;
        v4 = CAtlTraceProcess::Base(v15->m_pProcess);
        if ( memcmp_0(v15->m_pBufferStart, v4, *((_QWORD *)v13 + 131)) )
          goto $LN35_5;
        CAtlTraceProcess::IncRef(v15->m_pProcess);
        v15->m_pProcess = (CAtlTraceProcess *)CAtlTraceProcess::Base(v15->m_pProcess);
        UnmapViewOfFile(v15->m_pBufferStart);
        v15->m_pBufferStart = (char *)v15->m_pProcess;
LABEL_26:
        v15->m_dwPageSize = SystemInfo.dwPageSize;
        v15->m_bValid = 1;
        goto $LN35_5;
      }
      LODWORD(dwSize) = SystemInfo.dwPageSize;
      while ( (unsigned int)dwSize < 0x470ui64 )
        LODWORD(dwSize) = SystemInfo.dwPageSize + dwSize;
      if ( VirtualAlloc(v15->m_pBufferStart, (unsigned int)dwSize, 0x1000u, 4u) )
      {
        v12 = (CAtlTraceProcess *)operator new(0x470ui64, v15->m_pBufferStart);
        if ( v12 )
        {
          CAtlTraceProcess::CAtlTraceProcess(v12, v17);
          v14 = (CAtlTraceProcess *)v5;
        }
        else
        {
          v14 = 0i64;
        }
        v15->m_pProcess = v14;
        v15->m_pProcess->m_dwFrontAlloc = (unsigned int)dwSize;
        v15->m_pProcess->m_dwCurrFront = 1136i64;
        goto LABEL_26;
      }
    }
  }
$LN35_5:
  if ( TokenHandle )
  {
    HIDWORD(dwSize) = SetThreadToken(0i64, TokenHandle);
    if ( !HIDWORD(dwSize)
      && CrtDbgReportW_0(
           2i64,
           (__int64)L"f:\\dd\\vctools\\vc7libs\\ship\\atlmfc\\src\\atl\\atls\\allocate.cpp",
           117i64,
           0i64) == 1 )
    {
      __debugbreak();
    }
    CloseHandle(TokenHandle);
  }
  if ( !v15->m_bValid )
  {
    if ( v15->m_pBufferStart )
    {
      UnmapViewOfFile(v15->m_pBufferStart);
      v15->m_pBufferStart = 0i64;
    }
    if ( v15->m_hMap )
    {
      CloseHandle(v15->m_hMap);
      v15->m_hMap = 0i64;
    }
  }
  return v15->m_bValid;
}
