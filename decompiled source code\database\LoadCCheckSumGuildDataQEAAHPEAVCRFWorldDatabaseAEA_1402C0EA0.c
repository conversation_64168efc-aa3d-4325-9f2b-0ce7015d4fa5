/*
 * Function: ?Load@CCheckSumGuildData@@QEAAHPEAVCRFWorldDatabase@@AEAV1@@Z
 * Address: 0x1402C0EA0
 */

signed __int64 __fastcall CCheckSumGuildData::Load(CCheckSumGuildData *this, CRFWorldDatabase *pkDB, CCheckSumGuildData *kSrcValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@8
  char v8; // [sp+24h] [bp-14h]@8
  CCheckSumGuildData *v9; // [sp+40h] [bp+8h]@1
  CRFWorldDatabase *pkDBa; // [sp+48h] [bp+10h]@1
  CCheckSumGuildData *v11; // [sp+50h] [bp+18h]@1

  v11 = kSrcValue;
  pkDBa = pkDB;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !v9->m_dwGuildSerial || v9->m_dwGuildSerial == -1 || !pkDB )
    return 0i64;
  v7 = 0;
  v8 = CRFWorldDatabase::Select_UnitData(pkDB, v9->m_dwGuildSerial, v9->m_dValues);
  if ( v8 == 1 )
    return 0xFFFFFFFFi64;
  if ( v8 == 2 )
  {
    if ( !CCheckSumGuildData::Insert(v11, pkDBa) )
      return 0xFFFFFFFFi64;
    v7 = 1;
  }
  return v7;
}
