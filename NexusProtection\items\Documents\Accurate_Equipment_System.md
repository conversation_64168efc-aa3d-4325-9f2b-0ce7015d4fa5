# Accurate Equipment System Documentation

## Overview

This document provides the accurate equipment system based on the actual decompiled source code from the game. The equipment system has been corrected to match the real game implementation, removing fictional slots and using the actual table codes and item types.

## Source Code Analysis

### Key Functions Analyzed

1. **GetItemTableCode** (Address: 0x1400362B0)
   - Maps item code prefixes to table codes
   - Defines the actual item categorization system

2. **pc_EquipPart** (Address: 0x1400AD960)
   - Validates equipment items (table codes 0-7)
   - Handles equipment operations and validation

3. **pc_OffPart** (Address: 0x1400AE4D0)
   - Handles unequipping operations
   - Validates storage codes and item states

## Actual Equipment System

### Equipment Table Codes (0-7)

Based on the `GetItemTableCode` function and `pc_EquipPart` validation (line 55: `if ( pFixingItem->m_byTableCode < 8 )`):

| Table Code | Item Prefix | Equipment Slot | Description |
|------------|-------------|----------------|-------------|
| 0 | "iu" | UpperArmor | Upper body armor/clothing |
| 1 | "il" | LowerArmor | Lower body armor/clothing |
| 2 | "ig" | Gloves | Gloves/hand equipment |
| 3 | "is" | Shoes | Shoes/foot equipment |
| 4 | "ih" | Helmet | Helmet/head equipment |
| 5 | "id" | Shield | Shield/defensive equipment |
| 6 | "iw" | Weapon | Weapon/offensive equipment |
| 7 | "ik" | Cloak | Cloak/cape equipment |

### Non-Equipment Items (8+)

| Table Code | Item Prefix | Category | Description |
|------------|-------------|----------|-------------|
| 8 | "ii" | Ring | Ring/finger equipment |
| 9 | "ia" | Amulet | Amulet/neck equipment |
| 10 | "ib" | Bullet | Bullet/ammunition |
| 11 | "im" | Material | Crafting tools/materials |
| 12 | "ie" | Enhancement | Enhancement items |
| 13 | "ip" | Potion | Potion items |
| 14 | "if" | Food | Food items |
| 15 | "ic" | Crystal | Crystal items |
| 16 | "it" | Tool | Tool items |
| 17 | "io" | Other | Other items |
| 18 | "ir" | Resource | Resource items |
| 19 | "in" | Note | Note items |
| 20 | "iy" | Mystery | Mystery items |
| 21 | "iz" | Zone | Zone items |
| 22 | "iq" | Quest | Quest items |
| 23 | "ix" | Experience | Experience items |
| 24 | "ij" | Jewel | Jewel items |
| 25 | "gt" | Gate | Gate items |
| 26 | "tr" | Treasure | Treasure items |
| 27 | "sk" | Skill | Skill items |
| 28 | "ti" | Time | Time items |
| 29 | "ev" | Event | Event items |
| 30 | "re" | Reward | Reward items |
| 31 | "bx" | Box | Box items |
| 32 | "fi" | Fish | Fish items |
| 33 | "un" | Union | Union items |
| 34 | "rd" | Raid | Raid items |
| 35 | "lk" | Link | Link items |
| 36 | "cu" | Currency | Currency items |

## Equipment Validation Logic

### From pc_EquipPart Function

1. **Table Code Validation** (Line 55)
   ```c
   if ( pFixingItem->m_byTableCode < 8 )
   ```
   Only items with table codes 0-7 can be equipped.

2. **Lock Status Check** (Lines 57-60)
   ```c
   if ( pFixingItem->m_bLock )
   {
     v7 = 10; // Error code for locked item
   }
   ```

3. **Equipment Grade Check** (Lines 70-76)
   ```c
   v13 = GetItemEquipGrade(pFixingItem->m_byTableCode, pFixingItem->m_wItemIndex);
   if ( CPlayer::IsEquipAbleGrade(v16, v13) )
   ```

4. **Equipment Part Check** (Line 73)
   ```c
   if ( !CPlayer::_check_equip_part(v16, pFixingItem) )
     v7 = 7; // Error code for invalid equipment part
   ```

## Corrected Equipment Enum

```cpp
enum class EquipmentSlot : uint8_t {
    // Equipment slots (table codes 0-7) - CORRECTED INTERPRETATIONS
    UpperArmor = 0,  // "iu" - Upper body armor/clothing (table code 0)
    LowerArmor = 1,  // "il" - Lower body armor/clothing (table code 1)
    Gloves = 2,      // "ig" - Gloves/hand equipment (table code 2)
    Shoes = 3,       // "is" - Shoes/foot equipment (table code 3)
    Helmet = 4,      // "ih" - Helmet/head equipment (table code 4)
    Shield = 5,      // "id" - Shield/defensive equipment (table code 5)
    Weapon = 6,      // "iw" - Weapon/offensive equipment (table code 6)
    Cloak = 7,       // "ik" - Cloak/cape equipment (table code 7)
    MaxEquipSlots = 8,

    // Non-equipment items (for reference)
    Ring = 8,        // "ii" - Ring/finger equipment (table code 8)
    Amulet = 9,      // "ia" - Amulet/neck equipment (table code 9)
    Bullet = 10,     // "ib" - Bullet/ammunition (table code 10)
    Material = 11,   // "im" - Crafting tools/materials (table code 11)

    None = 255
};
```

## Key Differences from Original Implementation

### Removed Fictional Slots
- **Belt**: Not present in actual game equipment system
- **Necklace**: Not present in actual game equipment system  
- **Earring1/Earring2**: Not present in actual game equipment system
- **Cape**: Not present in actual game equipment system
- **Ring2**: Only one ring slot exists in actual game

### Added Actual Slots
- **LeftHand**: For shields and off-hand weapons (table code 1)
- **Wings**: For wing equipment (table code 6)
- **Shoes**: Correct term for foot equipment (table code 3)

### Corrected Slot Mappings
- **Weapon**: Table code 0 (not 1)
- **Gloves**: Table code 2 (not 5)
- **Helmet**: Table code 4 (not 3)
- **Armor**: Table code 5 (not 4)
- **Ring**: Table code 7 (not 7-8)

## Equipment System Features

### Actual Game Features
1. **8 Equipment Slots**: Exactly 8 equipment slots (table codes 0-7)
2. **Single Ring Slot**: Only one ring slot, not multiple
3. **Wings Equipment**: Special wing equipment slot
4. **Left Hand Slot**: Dedicated slot for shields and off-hand weapons
5. **No Accessories**: No belt, necklace, or earring equipment slots

### Storage System
- **Equipment Storage**: Storage code 1 for equipped items
- **Inventory Storage**: Storage code 0 for inventory items
- **Additional Storage**: Storage codes 2-7 for various storage types

## Implementation Notes

### Equipment Validation
```cpp
// Check if item is equipment (table codes 0-7)
if (pItem->m_byTableCode >= 8) {
    // Not equipment item
    return false;
}

// Check if item is locked
if (pItem->m_bLock) {
    // Item is locked, cannot equip
    return false;
}
```

### Slot Mapping
```cpp
EquipmentSlot GetEquipmentSlot(_STORAGE_LIST* pItem) {
    switch (pItem->m_byTableCode) {
        case 0: return EquipmentSlot::Weapon;      // "iu"
        case 1: return EquipmentSlot::LeftHand;    // "il"
        case 2: return EquipmentSlot::Gloves;      // "ig"
        case 3: return EquipmentSlot::Shoes;       // "is"
        case 4: return EquipmentSlot::Helmet;      // "ih"
        case 5: return EquipmentSlot::Armor;       // "id"
        case 6: return EquipmentSlot::Wings;       // "iw"
        case 7: return EquipmentSlot::Ring;        // "ik"
        default: return EquipmentSlot::None;
    }
}
```

## Usage Examples

### Equipping Items by Type
```cpp
// Equip weapon (table code 0)
if (pItem->m_byTableCode == 0) {
    EquipmentContext context;
    context.pPlayer = pPlayer;
    context.pItem = pItem;
    context.slot = EquipmentSlot::Weapon;
    equipmentSystem->EquipItem(context);
}

// Equip shield (table code 1)
if (pItem->m_byTableCode == 1) {
    EquipmentContext context;
    context.pPlayer = pPlayer;
    context.pItem = pItem;
    context.slot = EquipmentSlot::LeftHand;
    equipmentSystem->EquipItem(context);
}
```

### Item Code Examples
```cpp
// Weapon items start with "iu"
// Example: "iu001", "iu002", etc.

// Shield items start with "il"  
// Example: "il001", "il002", etc.

// Glove items start with "ig"
// Example: "ig001", "ig002", etc.

// Shoe items start with "is"
// Example: "is001", "is002", etc.

// Helmet items start with "ih"
// Example: "ih001", "ih002", etc.

// Armor items start with "id"
// Example: "id001", "id002", etc.

// Wing items start with "iw"
// Example: "iw001", "iw002", etc.

// Ring items start with "ik"
// Example: "ik001", "ik002", etc.
```

## Conclusion

The equipment system has been corrected to accurately reflect the actual game implementation. This ensures compatibility with the existing game data and prevents issues that would arise from using fictional equipment slots that don't exist in the real game.

The corrected system:
- Uses the actual 8 equipment slots (table codes 0-7)
- Removes fictional slots (belt, necklace, earrings, cape)
- Adds the actual wing and left-hand equipment slots
- Properly maps table codes to equipment slots
- Validates equipment using the same logic as the original game
