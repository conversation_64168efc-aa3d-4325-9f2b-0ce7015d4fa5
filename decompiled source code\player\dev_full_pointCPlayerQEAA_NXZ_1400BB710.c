/*
 * Function: ?dev_full_point@CPlayer@@QEAA_NXZ
 * Address: 0x1400BB710
 */

char __fastcall CPlayer::dev_full_point(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  int v4; // eax@4
  int v5; // eax@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  CGameObjectVtbl *v8; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = ((int (__fastcall *)(CPlayer *))v9->vfptr->GetMaxHP)(v9);
  v8 = v9->vfptr;
  ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v8->SetHP)(v9, (unsigned int)v3, 0i64);
  v4 = CPlayer::GetMaxFP(v9);
  CPlayer::SetFP(v9, v4, 0);
  v5 = CPlayer::GetMaxSP(v9);
  CPlayer::SetSP(v9, v5, 0);
  CPlayer::SendMsg_Recover(v9);
  return 1;
}
