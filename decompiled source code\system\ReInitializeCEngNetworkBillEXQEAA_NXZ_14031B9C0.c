/*
 * Function: ?ReInitialize@CEngNetworkBillEX@@QEAA_NXZ
 * Address: 0x14031B9C0
 */

char __fastcall CEngNetworkBillEX::ReInitialize(CEngNetworkBillEX *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v3; // eax@4
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CEngNetworkBillEX *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = inet_addr(v6->m_ip);
  if ( ((int (__fastcall *)(_QWORD, _QWORD, _QWORD, _QWORD))v6->Connect)(0i64, 0i64, v3, LOWORD(v6->m_port)) )
  {
    v6->m_bConnect = 0;
    result = 0;
  }
  else
  {
    v6->m_bConnect = 1;
    result = 1;
  }
  return result;
}
