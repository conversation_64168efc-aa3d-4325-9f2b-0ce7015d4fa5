/*
 * Function: j_?AlgorithmName@?$AlgorithmImpl@V?$DL_EncryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$ECIES@VECP@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@$0A@@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x140011CE8
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::AlgorithmName(CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0> > *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  return CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::AlgorithmName(
           this,
           result);
}
