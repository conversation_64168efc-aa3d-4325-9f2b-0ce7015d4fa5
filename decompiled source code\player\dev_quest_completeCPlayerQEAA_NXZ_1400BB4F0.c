/*
 * Function: ?dev_quest_complete@CPlayer@@QEAA_NXZ
 * Address: 0x1400BB4F0
 */

char __fastcall CPlayer::dev_quest_complete(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@6
  _QUEST_DB_BASE::_LIST *pSlotData; // [sp+28h] [bp-20h]@9
  int k; // [sp+30h] [bp-18h]@10
  _base_fld *v8; // [sp+38h] [bp-10h]@13
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_pUserDB )
  {
    for ( j = 0; ; ++j )
    {
      if ( j >= 30 )
        return 0;
      pSlotData = &v9->m_Param.m_QuestDB.m_List[j];
      if ( pSlotData->byQuestType != 255 )
        break;
    }
    for ( k = 0; k < 3; ++k )
      pSlotData->wNum[k] = -1;
    pSlotData->dwPassSec = -1;
    CUserDB::Update_QuestUpdate(v9->m_pUserDB, j, pSlotData, 1);
    v8 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, pSlotData->wIndex);
    if ( *(_DWORD *)&v8[13].m_strCode[60] || *(_DWORD *)&v8[1].m_strCode[24] )
      CPlayer::SendMsg_SelectQuestReward(v9, j);
    else
      CPlayer::Emb_CompleteQuest(v9, j, -1, -1);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
