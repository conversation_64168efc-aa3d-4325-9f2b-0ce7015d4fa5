/*
 * Function: ?pc_TrunkAlterItemSlotRequest@CPlayer@@QEAAXKEE@Z
 * Address: 0x1400FA560
 */

void __fastcall CPlayer::pc_TrunkAlterItemSlotRequest(CPlayer *this, unsigned int dwItemSerial, char byClientSlotIndex, char byStorageIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _STORAGE_LIST::_db_con *v7; // [sp+20h] [bp-18h]@5
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+50h] [bp+18h]@1
  char v10; // [sp+58h] [bp+20h]@1

  v10 = byStorageIndex;
  v9 = byClientSlotIndex;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v8->m_pUserDB )
  {
    v7 = _STORAGE_LIST::GetPtrFromSerial(v8->m_Param.m_pStoragePtr[(unsigned __int8)byStorageIndex], dwItemSerial);
    if ( v7 )
    {
      v7->m_byClientIndex = v9;
      CUserDB::Update_ItemSlot(v8->m_pUserDB, v10, v7->m_byStorageIndex, v7->m_byClientIndex);
    }
  }
}
