/*
 * Function: ??$_Uninit_copy@PEAEPEAEV?$allocator@E@std@@@std@@YAPEAEPEAE00AEAV?$allocator@E@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140340220
 */

char *__fastcall std::_Uninit_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  rsize_t DstSize; // [sp+20h] [bp-18h]@4
  char *v11; // [sp+28h] [bp-10h]@4
  char *Src; // [sp+40h] [bp+8h]@1

  Src = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  DstSize = _Last - Src;
  v11 = &_Dest[_Last - Src];
  if ( _Last != Src )
    memmove_s(_Dest, DstSize, Src, DstSize);
  return v11;
}
