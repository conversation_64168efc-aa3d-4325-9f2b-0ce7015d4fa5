/*
 * Function: ?UpdateRePrice@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034D230
 */

char __fastcall CUnmannedTraderController::UpdateRePrice(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  char *byProcRet; // [sp+20h] [bp-48h]@4
  char *v7; // [sp+30h] [bp-38h]@4
  char byState; // [sp+44h] [bp-24h]@4
  char v9; // [sp+54h] [bp-14h]@4
  CUnmannedTraderController *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData;
  pData[20] = 0;
  byState = -1;
  byProcRet = v7 + 20;
  v9 = CUnmannedTraderController::CheckDBItemState(v10, v7[21], *((_DWORD *)v7 + 6), &byState, v7 + 20);
  if ( v7[20] )
  {
    result = v9;
  }
  else
  {
    LODWORD(byProcRet) = *((_DWORD *)v7 + 7);
    if ( CRFWorldDatabase::Update_UnmannedTraderSellInfoPrice(
           pkDB,
           v7[21],
           *((_DWORD *)v7 + 6),
           *((_DWORD *)v7 + 2),
           (unsigned int)byProcRet) )
    {
      result = 0;
    }
    else
    {
      v7[20] = 27;
      result = 24;
    }
  }
  return result;
}
