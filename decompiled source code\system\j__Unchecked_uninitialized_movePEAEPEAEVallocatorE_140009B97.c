/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAEPEAEV?$allocator@E@std@@@stdext@@YAPEAEPEAE00AEAV?$allocator@E@std@@@Z
 * Address: 0x140009B97
 */

char *__fastcall stdext::_Unchecked_uninitialized_move<unsigned char *,unsigned char *,std::allocator<unsigned char>>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<unsigned char *,unsigned char *,std::allocator<unsigned char>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
