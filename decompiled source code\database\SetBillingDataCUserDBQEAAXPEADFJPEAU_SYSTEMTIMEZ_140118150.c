/*
 * Function: ?SetBillingData@CUserDB@@QEAAXPEADFJPEAU_SYSTEMTIME@@@Z
 * Address: 0x140118150
 */

void __fastcall CUserDB::SetBillingData(CUserDB *this, char *szCMSCode, __int16 iType, int lRemainTime, _SYSTEMTIME *pstEndDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8->m_BillingInfo.iType = iType;
  v8->m_BillingInfo.lRemainTime = lRemainTime;
  if ( iType != 6 && iType != 7 )
    memset_0(v8->m_BillingInfo.szCMS, 0, 7ui64);
  else
    memcpy_0(v8->m_BillingInfo.szCMS, szCMSCode, 7ui64);
  if ( pstEndDate )
    memcpy_0(&v8->m_BillingInfo.stEndDate, pstEndDate, 0x10ui64);
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    if ( v8->m_BillingInfo.bPCCheat )
    {
      v8->m_BillingInfo.bIsPcBang = 1;
      v8->m_BillingInfo.iType = 7;
    }
  }
}
