/*
 * Function: j_?auto_trade_login_sell@CMgrAvatorItemHistory@@QEAAXPEBDK0KPEAU_db_con@_STORAGE_LIST@@_JKKKKPEAD@Z
 * Address: 0x140006FAA
 */

void __fastcall CMgrAvatorItemHistory::auto_trade_login_sell(CMgrAvatorItemHistory *this, const char *szBuyerName, unsigned int dwBuyerSerial, const char *szBuyerID, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pItem, __int64 tResultTime, unsigned int dwPrice, unsigned int dwTax, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  CMgrAvatorItemHistory::auto_trade_login_sell(
    this,
    szBuyerName,
    dwBuyerSerial,
    szBuyerID,
    dwRegistSerial,
    pItem,
    tResultTime,
    dwPrice,
    dwTax,
    dwLeftDalant,
    dwLeftGold,
    pszFileName);
}
