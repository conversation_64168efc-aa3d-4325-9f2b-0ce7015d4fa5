/*
 * Function: ?TransferMessagesTo2@BufferedTransformation@CryptoPP@@QEAA_KAEAV12@AEAIAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z
 * Address: 0x1405F53D0
 */

__int64 __fastcall CryptoPP::BufferedTransformation::TransferMessagesTo2(__int64 a1, __int64 a2, int *a3, __int64 a4, unsigned __int8 a5)
{
  __int64 v5; // rax@1
  char v6; // al@2
  __int64 result; // rax@2
  __int64 v8; // rax@7
  int v9; // eax@10
  unsigned int v10; // [sp+30h] [bp-28h]@3
  __int64 v11; // [sp+38h] [bp-20h]@7
  __int64 v12; // [sp+40h] [bp-18h]@7
  char v13; // [sp+48h] [bp-10h]@12
  __int64 v14; // [sp+60h] [bp+8h]@1
  __int64 v15; // [sp+68h] [bp+10h]@1
  int *v16; // [sp+70h] [bp+18h]@1
  __int64 v17; // [sp+78h] [bp+20h]@1

  v17 = a4;
  v16 = a3;
  v15 = a2;
  v14 = a1;
  LODWORD(v5) = (*(int (**)(void))(*(_QWORD *)a1 + 328i64))();
  if ( v5 )
  {
    v6 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v14 + 328i64))(v14);
    result = CryptoPP::BufferedTransformation::TransferMessagesTo2(v6);
  }
  else
  {
    v10 = *v16;
    for ( *v16 = 0; *v16 < v10 && (unsigned __int8)(*(int (__fastcall **)(__int64))(*(_QWORD *)v14 + 192i64))(v14); ++*v16 )
    {
      while ( (unsigned __int8)(*(int (__fastcall **)(__int64))(*(_QWORD *)v14 + 128i64))(v14) )
      {
        v11 = -1i64;
        LODWORD(v8) = (*(int (__fastcall **)(__int64, __int64, __int64 *, __int64))(*(_QWORD *)v14 + 248i64))(
                        v14,
                        v15,
                        &v11,
                        v17);
        v12 = v8;
        if ( v8 )
          return v12;
      }
      v9 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v14 + 112i64))(v14);
      if ( (unsigned __int8)CryptoPP::BufferedTransformation::ChannelMessageEnd(v15, v17, (unsigned int)v9, a5) )
        return 1i64;
      v13 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v14 + 200i64))(v14);
      if ( !v13 )
        _wassert(L"result", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\cryptlib.cpp", 0x19Bu);
    }
    result = 0i64;
  }
  return result;
}
