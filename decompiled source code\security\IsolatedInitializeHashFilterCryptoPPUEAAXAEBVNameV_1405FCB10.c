/*
 * Function: ?IsolatedInitialize@HashFilter@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FCB10
 */

void __fastcall CryptoPP::HashFilter::IsolatedInitialize(CryptoPP::HashFilter *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@1
  const char *v3; // rax@1
  CryptoPP::HashFilter *v4; // [sp+30h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v5; // [sp+38h] [bp+10h]@1

  v5 = a2;
  v4 = this;
  v2 = CryptoPP::Name::PutMessage((CryptoPP::Name *)this);
  v4->m_putMessage = CryptoPP::NameValuePairs::GetValueWithDefault<bool>(v5, v2, 0i64);
  v3 = CryptoPP::Name::TruncatedDigestSize((CryptoPP::Name *)v4);
  v4->m_truncatedDigestSize = CryptoPP::NameValuePairs::GetIntValueWithDefault(
                                (CryptoPP::NameValuePairs *)v5,
                                v3,
                                0xFFFFFFFF);
  ((void (__fastcall *)(_QWORD))v4->m_hashModule->vfptr[3].__vecDelDtor)(v4->m_hashModule);
}
