/*
 * Function: ?Remaintime_PCBang@CBillingManager@@QEAAXPEADFJPEAU_SYSTEMTIME@@@Z
 * Address: 0x1401C4060
 */

void __fastcall CBillingManager::Remaintime_PCBang(CBillingManager *this, char *szCMSCode, __int16 iType, int lRemaintime, _SYSTEMTIME *pstEndDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  _SYSTEMTIME *v8; // [sp+20h] [bp-18h]@4
  CBillingManager *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = pstEndDate;
  CBilling::Remaintime_PCBang(v9->m_pBill, szCMSCode, iType, lRemaintime, pstEndDate);
}
