/*
 * Function: ??$unchecked_copy@PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@@stdext@@YAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@00@Z
 * Address: 0x140616900
 */

int __fastcall stdext::unchecked_copy<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(__int64 a1, __int64 a2, __int64 a3)
{
  _BYTE *v3; // rax@1
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  char v7; // [sp+32h] [bp-16h]@1
  __int64 v8; // [sp+50h] [bp+8h]@1
  __int64 v9; // [sp+58h] [bp+10h]@1
  __int64 v10; // [sp+60h] [bp+18h]@1

  v10 = a3;
  v9 = a2;
  v8 = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Ptr_cat<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>();
  v3 = (_BYTE *)std::_Iter_random<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>((__int64)&v7);
  return std::_Copy_opt<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,std::random_access_iterator_tag>(
           v8,
           v9,
           v10,
           *v3);
}
