/*
 * Function: ?RecoverMessageFromRepresentative@PK_SignatureMessageEncodingMethod@CryptoPP@@UEBA?AUDecodingResult@2@AEAVHashTransformation@2@U?$pair@PEBE_K@std@@_NPEAE_K3@Z
 * Address: 0x14058F970
 */

void __noreturn CryptoPP::PK_SignatureMessageEncodingMethod::RecoverMessageFromRepresentative()
{
  CryptoPP::NotImplemented v0; // [sp+20h] [bp-98h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@1
  unsigned __int8 v2; // [sp+A0h] [bp-18h]@1
  __int64 v3; // [sp+A8h] [bp-10h]@1

  v3 = -2i64;
  memset(&v2, 0, sizeof(v2));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &s,
    "PK_MessageEncodingMethod: this signature scheme does not support message recovery",
    v2);
  CryptoPP::NotImplemented::NotImplemented(&v0, &s);
  CxxThrowException_0((__int64)&v0, (__int64)&TI3_AVNotImplemented_CryptoPP__);
}
