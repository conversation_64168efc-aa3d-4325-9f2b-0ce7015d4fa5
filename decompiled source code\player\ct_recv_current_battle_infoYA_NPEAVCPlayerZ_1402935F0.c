/*
 * Function: ?ct_recv_current_battle_info@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402935F0
 */

char __fastcall ct_recv_current_battle_info(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int uiMapID; // [sp+20h] [bp-18h]@6
  int n; // [sp+24h] [bp-14h]@6
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8 )
  {
    uiMapID = CPlayerDB::GetRaceCode(&v8->m_Param);
    n = v8->m_ObjID.m_wIndex;
    v4 = CGuildBattleController::Instance();
    CGuildBattleController::SendCurrentBattleInfoRequest(v4, n, uiMapID);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
