/*
 * Function: ?pop_back@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@QEAAXXZ
 * Address: 0x140600AD0
 */

__int64 __fastcall std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::pop_back(__int64 a1)
{
  __int64 result; // rax@1
  unsigned __int64 v2; // [sp+28h] [bp-10h]@2
  __int64 v3; // [sp+40h] [bp+8h]@1

  v3 = a1;
  result = std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::empty(a1);
  if ( !(_BYTE)result )
  {
    v2 = *(_QWORD *)(v3 + 48) + *(_QWORD *)(v3 + 40) - 1i64;
    if ( *(_QWORD *)(v3 + 32) <= v2 )
      v2 -= *(_QWORD *)(v3 + 32);
    std::allocator<CryptoPP::MeterFilter::MessageRange>::destroy(v3 + 16, *(_QWORD *)(*(_QWORD *)(v3 + 24) + 8 * v2));
    --*(_QWORD *)(v3 + 48);
    result = v3;
    if ( !*(_QWORD *)(v3 + 48) )
    {
      result = v3;
      *(_QWORD *)(v3 + 40) = 0i64;
    }
  }
  return result;
}
