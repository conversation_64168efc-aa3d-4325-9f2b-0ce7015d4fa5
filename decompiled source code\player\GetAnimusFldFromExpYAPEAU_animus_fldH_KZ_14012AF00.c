/*
 * Function: ?GetAnimusFldFromExp@@YAPEAU_animus_fld@@H_K@Z
 * Address: 0x14012AF00
 */

_base_fld *__fastcall GetAnimusFldFromExp(int nAnimusClass, unsigned __int64 dwExp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@11
  __int64 v6; // [sp+0h] [bp-48h]@1
  CRecordData *v7; // [sp+20h] [bp-28h]@4
  int n; // [sp+28h] [bp-20h]@4
  _base_fld *v9; // [sp+30h] [bp-18h]@6
  int v10; // [sp+50h] [bp+8h]@1
  unsigned __int64 v11; // [sp+58h] [bp+10h]@1

  v11 = dwExp;
  v10 = nAnimusClass;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (CRecordData *)((char *)&CAnimus::s_tblParameter + 176 * v10);
  for ( n = 0; n < 65; ++n )
  {
    v9 = CRecordData::GetRecord(v7, n);
    if ( !v9 )
      return 0i64;
    if ( *(_QWORD *)&v9[1].m_strCode[0] > v11 )
      return v9;
  }
  v5 = CRecordData::GetRecordNum(v7);
  return CRecordData::GetRecord(v7, v5 - 1);
}
