/*
 * Function: j_?SellWaitItem@CUnmannedTraderRegistItemInfo@@QEAAEGPEAVCLogFile@@_JAEAE@Z
 * Address: 0x1400035FD
 */

char __fastcall CUnmannedTraderRegistItemInfo::SellWaitItem(CUnmannedTraderRegistItemInfo *this, unsigned __int16 wInx, CLogFile *pkLogger, __int64 tResultTime, char *byStorageInx)
{
  return CUnmannedTraderRegistItemInfo::SellWaitItem(this, wInx, pkLogger, tResultTime, byStorageInx);
}
