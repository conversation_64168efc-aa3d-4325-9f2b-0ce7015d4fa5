/*
 * Function: ?IsTalikItem@CPvpCashMng@@QEAA_NPEBD@Z
 * Address: 0x1403F66F0
 */

char __fastcall CPvpCashMng::IsTalikItem(CPvpCashMng *this, const char *strCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPvpCashMng *v7; // [sp+40h] [bp+8h]@1
  char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = (char *)strCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 14; ++j )
  {
    if ( !strcmp_0(v7->m_TalikList.TalikInfo[j].m_pFld->m_strCode, Str2) )
      return 1;
  }
  return 0;
}
