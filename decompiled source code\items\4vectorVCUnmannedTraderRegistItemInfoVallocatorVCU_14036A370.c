/*
 * Function: ??4?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14036A370
 */

std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator=(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@7
  unsigned __int64 v5; // rax@9
  unsigned __int64 v6; // rax@13
  __int64 v8; // [sp+0h] [bp-48h]@1
  CUnmannedTraderRegistItemInfo *_First; // [sp+20h] [bp-28h]@8
  CUnmannedTraderRegistItemInfo *_Last; // [sp+28h] [bp-20h]@10
  unsigned __int64 v11; // [sp+30h] [bp-18h]@7
  unsigned __int64 v12; // [sp+38h] [bp-10h]@9
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v13; // [sp+50h] [bp+8h]@1
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v14; // [sp+58h] [bp+10h]@1

  v14 = _Right;
  v13 = this;
  v2 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v13 != _Right )
  {
    if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(_Right) )
    {
      v11 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v14);
      v4 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v13);
      if ( v11 > v4 )
      {
        v12 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v14);
        v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::capacity(v13);
        if ( v12 > v5 )
        {
          if ( v13->_Myfirst )
          {
            std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
              v13,
              v13->_Myfirst,
              v13->_Mylast);
            std::allocator<CUnmannedTraderRegistItemInfo>::deallocate(
              &v13->_Alval,
              v13->_Myfirst,
              (unsigned int)((char *)v13->_Myend - (char *)v13->_Myfirst) / 104i64);
          }
          v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v14);
          if ( std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Buy(v13, v6) )
            v13->_Mylast = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ucopy<CUnmannedTraderRegistItemInfo *>(
                             v13,
                             v14->_Myfirst,
                             v14->_Mylast,
                             v13->_Myfirst);
        }
        else
        {
          _Last = &v14->_Myfirst[std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v13)];
          stdext::unchecked_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(
            v14->_Myfirst,
            _Last,
            v13->_Myfirst);
          v13->_Mylast = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ucopy<CUnmannedTraderRegistItemInfo *>(
                           v13,
                           _Last,
                           v14->_Mylast,
                           v13->_Mylast);
        }
      }
      else
      {
        _First = stdext::unchecked_copy<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *>(
                   v14->_Myfirst,
                   v14->_Mylast,
                   v13->_Myfirst);
        std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
          v13,
          _First,
          v13->_Mylast);
        v13->_Mylast = &v13->_Myfirst[std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(v14)];
      }
    }
    else
    {
      std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::clear(v13);
    }
  }
  return v13;
}
