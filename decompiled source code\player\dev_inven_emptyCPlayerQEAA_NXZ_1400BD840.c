/*
 * Function: ?dev_inven_empty@CPlayer@@QEAA_NXZ
 * Address: 0x1400BD840
 */

char __usercall CPlayer::dev_inven_empty@<al>(CPlayer *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@6
  __int64 v7; // [sp-20h] [bp-13F8h]@1
  char __t[5012]; // [sp+20h] [bp-13B8h]@4
  char v9; // [sp+13B4h] [bp-24h]@4
  int j; // [sp+13B8h] [bp-20h]@4
  void *Src; // [sp+13C0h] [bp-18h]@8
  CPlayer *v12; // [sp+13E0h] [bp+8h]@1

  v12 = this;
  v2 = alloca(a2);
  v3 = &v7;
  for ( i = 1276i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  `vector constructor iterator'(__t, 0x32ui64, 100, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  v9 = 0;
  for ( j = 0; ; ++j )
  {
    v5 = CPlayerDB::GetBagNum(&v12->m_Param);
    if ( j >= 20 * (unsigned __int8)v5 )
      break;
    if ( v12->m_Param.m_dbInven.m_pStorageList[j].m_bLoad )
    {
      Src = &v12->m_Param.m_dbInven.m_pStorageList[j];
      if ( !*((_BYTE *)Src + 19) && *((_BYTE *)Src + 1) != 19 )
      {
        CPlayer::SendMsg_DeleteStorageInform(v12, 0, *(_WORD *)((char *)Src + 17));
        if ( CPlayer::Emb_DelStorage(v12, 0, j, 0, 1, "CPlayer::dev_inven_empty()") )
          memcpy_0(&__t[50 * (unsigned __int8)v9++], Src, 0x32ui64);
      }
    }
  }
  CMgrAvatorItemHistory::cheat_del_item(
    &CPlayer::s_MgrItemHistory,
    v12->m_ObjID.m_wIndex,
    (_STORAGE_LIST::_db_con *)__t,
    v9,
    v12->m_szItemHistoryFileName);
  return 1;
}
