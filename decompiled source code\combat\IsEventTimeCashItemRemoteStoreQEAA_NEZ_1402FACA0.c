/*
 * Function: ?IsEventTime@CashItemRemoteStore@@QEAA_NE@Z
 * Address: 0x1402FACA0
 */

char __fastcall CashItemRemoteStore::IsEventTime(CashItemRemoteStore *this, char byEventType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@7
  CashItemRemoteStore *v7; // [sp+50h] [bp+8h]@1
  char v8; // [sp+58h] [bp+10h]@1

  v8 = byEventType;
  v7 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !v7->m_con_event.m_bConEvent && !v7->m_cash_event[(unsigned __int8)byEventType].m_ini.m_bUseCashEvent )
    return 0;
  _time32(&Time);
  if ( v7->m_con_event.m_bConEvent )
  {
    if ( v7->m_con_event.m_ini.m_byEventKind == (unsigned __int8)v8
      && Time > v7->m_con_event.m_eventtime.m_EventTime[0]
      && Time < v7->m_con_event.m_eventtime.m_EventTime[1] )
    {
      return 1;
    }
  }
  else if ( Time > v7->m_cash_event[(unsigned __int8)v8].m_ini.m_EventTime[0]
         && Time < v7->m_cash_event[(unsigned __int8)v8].m_ini.m_EventTime[1] )
  {
    return 1;
  }
  return 0;
}
