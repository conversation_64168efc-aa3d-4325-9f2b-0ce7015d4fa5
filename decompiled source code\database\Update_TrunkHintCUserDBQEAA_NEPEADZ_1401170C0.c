/*
 * Function: ?Update_TrunkHint@CUserDB@@QEAA_NEPEAD@Z
 * Address: 0x1401170C0
 */

char __fastcall CUserDB::Update_TrunkHint(CUserDB *this, char byHintIndex, char *pwszHintAnswer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _TRUNK_DB_BASE *v7; // [sp+20h] [bp-18h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &v8->m_AvatorData.dbTrunk;
  v8->m_AvatorData.dbTrunk.byHintIndex = byHintIndex;
  strcpy_0(v7->wszHintAnswer, pwszHintAnswer);
  v8->m_bDataUpdate = 1;
  return 1;
}
