/*
 * Function: j_??0?$_Ranit@VCMoveMapLimitRightInfo@@_JPEBV1@AEBV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140005E52
 */

void __fastcall std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>(std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &> *this, std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &> *__that)
{
  std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>(
    this,
    __that);
}
