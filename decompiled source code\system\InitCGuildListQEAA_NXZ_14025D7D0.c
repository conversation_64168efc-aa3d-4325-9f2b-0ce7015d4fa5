/*
 * Function: ?Init@CGuildList@@QEAA_NXZ
 * Address: 0x14025D7D0
 */

char __fastcall CGuildList::Init(CGuildList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@6
  void *v6; // [sp+28h] [bp-30h]@11
  void *__t; // [sp+30h] [bp-28h]@8
  __int64 v8; // [sp+38h] [bp-20h]@4
  void *v9; // [sp+40h] [bp-18h]@9
  CGuildList *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( v10->m_bInit )
  {
    result = 1;
  }
  else
  {
    for ( j = 0; j < 3; ++j )
    {
      __t = operator new[](0x294Fui64);
      if ( __t )
      {
        `vector constructor iterator'(
          __t,
          0x8Dui64,
          75,
          (void *(__cdecl *)(void *))__guild_list_page::__guild_list_page);
        v9 = __t;
      }
      else
      {
        v9 = 0i64;
      }
      v6 = v9;
      v10->m_pGuildList[j] = (__guild_list_page *)v9;
      if ( !v10->m_pGuildList[j] )
        return 0;
    }
    v10->m_bInit = 1;
    result = 1;
  }
  return result;
}
