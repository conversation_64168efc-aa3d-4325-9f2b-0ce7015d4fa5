/*
 * Function: ?SendMsg_DTradeLockInform@CPlayer@@QEAAXXZ
 * Address: 0x1400E1490
 */

void __fastcall CPlayer::SendMsg_DTradeLockInform(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  CPlayer *v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pbyType = 18;
  v6 = 12;
  CNetProcess::LoadSendMsg(unk_1414F2088, v7->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
}
