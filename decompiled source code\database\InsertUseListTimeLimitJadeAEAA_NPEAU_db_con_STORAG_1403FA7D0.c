/*
 * Function: ?InsertUseList@TimeLimitJade@@AEAA_NPEAU_db_con@_STORAGE_LIST@@KK@Z
 * Address: 0x1403FA7D0
 */

bool __fastcall TimeLimitJade::InsertUseList(TimeLimitJade *this, _STORAGE_LIST::_db_con *pkItem, unsigned int dwStart, unsigned int dwEnd)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  TimeLimitJade::UseCell *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-38h]@1
  TimeLimitJade::UseCell v9; // [sp+20h] [bp-18h]@4
  TimeLimitJade *v10; // [sp+40h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItem; // [sp+48h] [bp+10h]@1
  unsigned int v12; // [sp+50h] [bp+18h]@1
  unsigned int dwUseTime; // [sp+58h] [bp+20h]@1

  dwUseTime = dwEnd;
  v12 = dwStart;
  pItem = pkItem;
  v10 = this;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CPlayer::SetHaveEffectUseTime(v10->_pkOwner, pkItem, 1);
  TimeLimitJade::UseCell::UseCell(&v9, pItem, v12, dwUseTime);
  return ListHeap<TimeLimitJade::UseCell>::push(&v10->_heapUseRow, v6);
}
