/*
 * Function: j_??$unchecked_uninitialized_copy@PEAHPEAHV?$allocator@H@std@@@stdext@@YAPEAHPEAH00AEAV?$allocator@H@std@@@Z
 * Address: 0x140009354
 */

int *__fastcall stdext::unchecked_uninitialized_copy<int *,int *,std::allocator<int>>(int *_First, int *_Last, int *_Dest, std::allocator<int> *_Al)
{
  return stdext::unchecked_uninitialized_copy<int *,int *,std::allocator<int>>(_First, _Last, _Dest, _Al);
}
