/*
 * Function: ?Init@CHonorGuild@@QEAA_NXZ
 * Address: 0x14025E540
 */

char __fastcall CHonorGuild::Init(CHonorGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@7
  __int64 v5; // rax@12
  __int64 v6; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@4
  _guild_honor_list_result_zocl *v8; // [sp+28h] [bp-40h]@9
  _guild_honor_list_result_zocl *v9; // [sp+30h] [bp-38h]@6
  _guild_honor_list_result_zocl *v10; // [sp+38h] [bp-30h]@14
  _guild_honor_list_result_zocl *v11; // [sp+40h] [bp-28h]@11
  __int64 v12; // [sp+48h] [bp-20h]@4
  _guild_honor_list_result_zocl *v13; // [sp+50h] [bp-18h]@7
  _guild_honor_list_result_zocl *v14; // [sp+58h] [bp-10h]@12
  CHonorGuild *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  for ( j = 0; j < 3; ++j )
  {
    v9 = (_guild_honor_list_result_zocl *)operator new(0xEDui64, THIS_FILE_12, 57);
    if ( v9 )
    {
      _guild_honor_list_result_zocl::_guild_honor_list_result_zocl(v9);
      v13 = (_guild_honor_list_result_zocl *)v3;
    }
    else
    {
      v13 = 0i64;
    }
    v8 = v13;
    v15->m_pCurrHonorGuild[j] = v13;
    if ( !v15->m_pCurrHonorGuild[j] )
      return 0;
    v11 = (_guild_honor_list_result_zocl *)operator new(0xEDui64, THIS_FILE_12, 61);
    if ( v11 )
    {
      _guild_honor_list_result_zocl::_guild_honor_list_result_zocl(v11);
      v14 = (_guild_honor_list_result_zocl *)v5;
    }
    else
    {
      v14 = 0i64;
    }
    v10 = v14;
    v15->m_pNextHonorGuild[j] = v14;
    if ( !v15->m_pNextHonorGuild[j] )
      return 0;
    v15->m_bNext[j] = 0;
    v15->m_bSendInform[j] = 0;
  }
  return 1;
}
