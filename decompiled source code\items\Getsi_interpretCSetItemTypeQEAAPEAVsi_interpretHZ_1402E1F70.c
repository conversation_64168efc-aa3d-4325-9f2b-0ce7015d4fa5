/*
 * Function: ?Getsi_interpret@CSetItemType@@QEAAPEAVsi_interpret@@H@Z
 * Address: 0x1402E1F70
 */

si_interpret *__fastcall CSetItemType::Getsi_interpret(CSetItemType *this, int set_pos)
{
  si_interpret *result; // rax@2

  if ( set_pos >= 0 )
  {
    if ( this->m_pEffectCountInfo )
    {
      if ( set_pos <= this->m_iEffectTypeCount )
        result = this->m_pEffectCountInfo[set_pos];
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
