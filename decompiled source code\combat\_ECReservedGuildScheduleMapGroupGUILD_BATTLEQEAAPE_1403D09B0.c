/*
 * Function: ??_ECReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAAPEAXI@Z
 * Address: 0x1403D09B0
 */

void *__fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::`vector deleting destructor'(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  void *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x158ui64,
      *(_DWORD *)&ptr[-1].m_kList[5].m_ucPageInx,
      (void (__cdecl *)(void *))GUILD_BATTLE::CReservedGuildScheduleMapGroup::~CReservedGuildScheduleMapGroup);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].m_kList[5].m_ucPageInx);
    result = &ptr[-1].m_kList[5].m_ucPageInx;
  }
  else
  {
    GUILD_BATTLE::CReservedGuildScheduleMapGroup::~CReservedGuildScheduleMapGroup(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
