/*
 * Function: ?Insert_GuidRoom@CRFWorldDatabase@@QEAA_NKEE@Z
 * Address: 0x1404B07C0
 */

bool __fastcall CRFWorldDatabase::Insert_GuidRoom(CRFWorldDatabase *this, unsigned int dwGuildSerial, char byRoomType, char byRace)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  __int64 v8; // [sp+0h] [bp-1C8h]@1
  int v9; // [sp+20h] [bp-1A8h]@4
  int v10; // [sp+28h] [bp-1A0h]@4
  int v11; // [sp+30h] [bp-198h]@4
  int v12; // [sp+38h] [bp-190h]@4
  int v13; // [sp+40h] [bp-188h]@4
  int v14; // [sp+48h] [bp-180h]@4
  int v15; // [sp+50h] [bp-178h]@4
  char Dst; // [sp+70h] [bp-158h]@4
  ATL::CTime result; // [sp+188h] [bp-40h]@4
  int v18; // [sp+1A0h] [bp-28h]@4
  int v19; // [sp+1A4h] [bp-24h]@4
  int v20; // [sp+1A8h] [bp-20h]@4
  int v21; // [sp+1ACh] [bp-1Ch]@4
  int v22; // [sp+1B0h] [bp-18h]@4
  unsigned __int64 v23; // [sp+1B8h] [bp-10h]@4
  CRFWorldDatabase *v24; // [sp+1D0h] [bp+8h]@1
  unsigned int v25; // [sp+1D8h] [bp+10h]@1
  char v26; // [sp+1E0h] [bp+18h]@1
  char v27; // [sp+1E8h] [bp+20h]@1

  v27 = byRace;
  v26 = byRoomType;
  v25 = dwGuildSerial;
  v24 = this;
  v4 = &v8;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = (unsigned __int64)&v8 ^ _security_cookie;
  memset_0(&Dst, 0, 0x100ui64);
  ATL::CTime::GetTickCount(&result);
  v18 = ATL::CTime::GetSecond(&result);
  v19 = ATL::CTime::GetMinute(&result);
  v20 = ATL::CTime::GetHour(&result);
  v21 = ATL::CTime::GetDay(&result);
  v22 = ATL::CTime::GetMonth(&result);
  v6 = ATL::CTime::GetYear(&result);
  v15 = v18;
  v14 = v19;
  v13 = v20;
  v12 = v21;
  v11 = v22;
  v10 = v6;
  v9 = (unsigned __int8)v27;
  sprintf(
    &Dst,
    "insert into tbl_GuildRoom(dck, guildserial, roomtype, roomrace, roomrentdate, logdate) values(0, %d, %d, %d, '%04d-%"
    "02d-%02d %02d:%02d:%02d.000', getdate())",
    v25,
    (unsigned __int8)v26);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, &Dst, 1);
}
