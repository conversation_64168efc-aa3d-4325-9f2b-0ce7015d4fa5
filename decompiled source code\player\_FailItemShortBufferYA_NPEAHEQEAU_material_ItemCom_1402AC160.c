/*
 * Function: ?_FailItemShortBuffer@@YA_NPEAHEQEAU_material@_ItemCombine_exp_fld@@PEAPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402AC160
 */

char __fastcall _FailItemShortBuffer(int *nBuffer, char byMtSlotNum, _ItemCombine_exp_fld::_material *pMtlList, _STORAGE_LIST::_db_con **pMt_Sv_Inv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-78h]@1
  int v8[7]; // [sp+28h] [bp-50h]@14
  unsigned __int8 v9; // [sp+44h] [bp-34h]@6
  unsigned __int8 j; // [sp+45h] [bp-33h]@6
  unsigned __int8 k; // [sp+46h] [bp-32h]@8
  _base_fld *v12; // [sp+48h] [bp-30h]@12
  char bySelectLinkIndex; // [sp+54h] [bp-24h]@13
  int l; // [sp+64h] [bp-14h]@19
  int *v15; // [sp+80h] [bp+8h]@1
  char v16; // [sp+88h] [bp+10h]@1
  _ItemCombine_exp_fld::_material *v17; // [sp+90h] [bp+18h]@1
  _STORAGE_LIST::_db_con **v18; // [sp+98h] [bp+20h]@1

  v18 = pMt_Sv_Inv;
  v17 = pMtlList;
  v16 = byMtSlotNum;
  v15 = nBuffer;
  v4 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byMtSlotNum <= 5 )
  {
    v9 = 0;
    for ( j = 0; j < (signed int)(unsigned __int8)v16; ++j )
    {
      for ( k = 0; k < (signed int)(unsigned __int8)v16; ++k )
      {
        if ( !v18[k] )
          return 0;
        v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v18[k]->m_byTableCode, v18[k]->m_wItemIndex);
        if ( v12 )
        {
          bySelectLinkIndex = 0;
          if ( _CheckSameItem(v17[j].m_itmPdMat, v12->m_strCode, &bySelectLinkIndex, 1) )
          {
            v8[(unsigned __int64)j] = k;
            ++v9;
            break;
          }
        }
      }
    }
    if ( v9 == (unsigned __int8)v16 )
    {
      for ( l = 0; l < v9; ++l )
        v15[l] = v8[v9 - l - 1];
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
