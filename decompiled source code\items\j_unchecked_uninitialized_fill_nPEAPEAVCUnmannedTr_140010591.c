/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@Z
 * Address: 0x140010591
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
