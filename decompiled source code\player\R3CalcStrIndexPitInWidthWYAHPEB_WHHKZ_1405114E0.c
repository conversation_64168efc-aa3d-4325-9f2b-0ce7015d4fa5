/*
 * Function: ?R3CalcStrIndexPitInWidthW@@YAHPEB_WHHK@Z
 * Address: 0x1405114E0
 */

int __fastcall R3CalcStrIndexPitInWidthW(const wchar_t *a1, int a2, int a3, int a4)
{
  signed __int64 v4; // rax@1
  int v5; // er8@1
  const wchar_t *v6; // rdx@1
  signed __int64 v7; // rcx@2
  const wchar_t *v8; // rdi@2
  bool v9; // zf@4
  int result; // eax@7

  LODWORD(v4) = a3;
  v5 = a2;
  v6 = a1;
  if ( (_DWORD)v4 == -1 )
  {
    v7 = -1i64;
    v8 = v6;
    do
    {
      if ( !v7 )
        break;
      v9 = *v8 == 0;
      ++v8;
      --v7;
    }
    while ( !v9 );
    v4 = ~v7 - 1;
  }
  if ( _bittest(&a4, 0x1Eu) )
  {
    result = CR3Font::CalcStrIndexPitInWidthW((CR3Font *)&unk_184A85FE0, v6, v5, v4);
  }
  else if ( _bittest(&a4, 0x1Fu) )
  {
    result = CR3Font::CalcStrIndexPitInWidthW((CR3Font *)&unk_184A86810, v6, v5, v4);
  }
  else
  {
    result = CR3Font::CalcStrIndexPitInWidthW((CR3Font *)&unk_184A857B0, v6, v5, v4);
  }
  return result;
}
