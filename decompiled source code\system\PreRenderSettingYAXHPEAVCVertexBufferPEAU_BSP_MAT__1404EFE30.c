/*
 * Function: ?PreRenderSetting@@YAXHPEAVCVertexBuffer@@PEAU_BSP_MAT_GROUP@@@Z
 * Address: 0x1404EFE30
 */

void __fastcall PreRenderSetting(int a1, struct CVertexBuffer *a2, struct _BSP_MAT_GROUP *a3)
{
  struct _BSP_MAT_GROUP *v3; // r12@1
  CVertexBuffer *v4; // r13@1
  __int64 v5; // rbx@1
  struct IDirect3DDevice8 *v6; // rdi@1
  struct _R3MATERIAL *v7; // rax@1
  signed __int64 v8; // rsi@1
  int v9; // eax@1
  int v10; // eax@2
  signed __int64 v11; // rdx@5
  signed __int64 v12; // r9@5
  signed __int64 v13; // r8@5
  signed __int64 v14; // rbp@10
  struct _D3DR3VERTEX_TEX2 *v15; // rax@11
  int v16; // ecx@13
  IUnknownVtbl *v17; // rbx@15
  struct IDirect3DTexture8 *v18; // rax@15
  int v19; // ecx@17
  struct D3DXMATRIX v20; // [sp+40h] [bp-68h]@1

  v3 = a3;
  v4 = a2;
  v5 = a1;
  v6 = GetD3dDevice();
  v7 = GetMainMaterial();
  v8 = (signed __int64)&v7[v3->MtlId].m_iMatNum;
  v9 = GetTextureMatrix(&v7[v3->MtlId], v5, &v20, 0.0);
  dword_184A79C28 = v9;
  if ( v9 )
  {
    v10 = 46 * v5;
    if ( *(_BYTE *)(46 * v5 + v8 + 162) & 0xA )
    {
      LOWORD(v10) = *(_WORD *)(v8 + 208);
      if ( _bittest(&v10, 0xFu) && dword_184A797A8 )
      {
        ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, struct D3DXMATRIX *))v6->vfptr[12].AddRef)(
          v6,
          17i64,
          &v20);
        ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
          v6,
          1i64,
          24i64,
          3i64);
        v11 = 1i64;
        v12 = 0x20000i64;
        v13 = 11i64;
LABEL_9:
        v9 = ((int (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
               v6,
               v11,
               v13,
               v12);
        goto LABEL_10;
      }
      ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, struct D3DXMATRIX *))v6->vfptr[12].AddRef)(
        v6,
        16i64,
        &v20);
      ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
        v6,
        0i64,
        24i64,
        3i64);
      v12 = 0x20000i64;
      v13 = 11i64;
    }
    else
    {
      ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, struct D3DXMATRIX *))v6->vfptr[12].AddRef)(
        v6,
        16i64,
        &v20);
      v12 = 2i64;
      v13 = 24i64;
    }
    v11 = 0i64;
    goto LABEL_9;
  }
LABEL_10:
  v14 = 46 * v5;
  if ( *(_BYTE *)(v8 + 46 * v5 + 162) & 4 )
  {
    v15 = (struct _D3DR3VERTEX_TEX2 *)CVertexBuffer::VPLock(v4, 44 * v3->VBMinIndex, 44 * v3->VCnt, 0x1000u);
    MakeUV(v15, v3->VCnt, v3->MultiSourceUV, v3->MultiSourceST, (struct _R3MATERIAL *)v8, v5, 0.0);
    CVertexBuffer::VPUnLock(v4);
  }
  LOWORD(v9) = *(_WORD *)(v8 + v14 + 162);
  if ( _bittest(&v9, 0xCu) )
    v16 = (unsigned __int64)GetTileAniTextureAddId((struct _R3MATERIAL *)v8, v5, 0.0) + *(_DWORD *)(v8 + v14 + 150);
  else
    v16 = *(_DWORD *)(v8 + v14 + 150);
  v17 = v6->vfptr;
  v18 = R3GetSurface(v16);
  ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, struct IDirect3DTexture8 *))v17[20].AddRef)(v6, 0i64, v18);
  if ( _bittest((const signed __int32 *)(v8 + v14 + 162), 0x10u) )
  {
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
      v6,
      0i64,
      13i64,
      3i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
      v6,
      0i64,
      14i64,
      3i64);
  }
  v19 = *(_DWORD *)(v8 + v14 + 154);
  if ( v19 )
    BlendOn(v19);
  if ( *(_BYTE *)(v8 + v14 + 162) & 1 )
  {
    *(_QWORD *)&v20.m[3][1] = 0i64;
    v20._44 = FLOAT_1_0;
    v20._33 = FLOAT_1_0;
    v20._22 = FLOAT_1_0;
    v20._11 = FLOAT_1_0;
    *(_QWORD *)&v20.m[2][3] = 0i64;
    *(_QWORD *)v20.m[2] = 0i64;
    *(_QWORD *)&v20.m[1][2] = 0i64;
    *(_QWORD *)&v20.m[0][3] = 0i64;
    *(_QWORD *)&v20.m[0][1] = 0i64;
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, struct D3DXMATRIX *))v6->vfptr[12].AddRef)(
      v6,
      16i64,
      &v20);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
      v6,
      0i64,
      11i64,
      196608i64);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, _QWORD, signed __int64, signed __int64))v6->vfptr[21].QueryInterface)(
      v6,
      0i64,
      24i64,
      3i64);
  }
}
