/*
 * Function: ?Encode@CCheckSumGuildData@@QEAAXNN@Z
 * Address: 0x1402C1160
 */

void __fastcall CCheckSumGuildData::Encode(CCheckSumGuildData *this, long double dDalant, long double dGold)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp-24h] [bp-48h]@1
  char v6; // [sp+0h] [bp-24h]@4
  CCheckSumGuildData *pkCheckSum; // [sp+2Ch] [bp+8h]@1

  pkCheckSum = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CCheckSumGuildConverter::Convert((CCheckSumGuildConverter *)&v6, dDalant, dGold, pkCheckSum);
}
