#pragma once

/**
 * @file CCharacter.h
 * @brief Base Character Class for Game Entities
 * 
 * Provides the base character functionality for all game entities including
 * players, monsters, NPCs, and other interactive objects. This class serves
 * as the foundation for the character hierarchy in the NexusProtection engine.
 * 
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <array>
#include <vector>
#include "CGameObject.h"

namespace NexusProtection::World {

    // Forward declarations
    struct _character_create_setdata;
    struct _effect_parameter;
    class CMyTimer;

    /**
     * @brief Character movement state
     */
    enum class CharacterMoveState : uint8_t {
        Idle = 0,
        Walking = 1,
        Running = 2,
        Flying = 3,
        Falling = 4,
        Swimming = 5,
        Teleporting = 6
    };

    /**
     * @brief Character status flags
     */
    enum class CharacterStatus : uint32_t {
        None = 0x00000000,
        Alive = 0x00000001,
        Dead = 0x00000002,
        Stunned = 0x00000004,
        Paralyzed = 0x00000008,
        Sleeping = 0x00000010,
        Confused = 0x00000020,
        Invisible = 0x00000040,
        Invulnerable = 0x00000080,
        Flying = 0x00000100,
        Swimming = 0x00000200,
        Mounted = 0x00000400,
        InCombat = 0x00000800,
        Trading = 0x00001000,
        Crafting = 0x00002000,
        Meditating = 0x00004000,
        Channeling = 0x00008000
    };

    /**
     * @brief Effect parameter structure
     */
    struct _effect_parameter {
        uint32_t effectType{0};
        uint32_t duration{0};
        float intensity{0.0f};
        uint32_t flags{0};

        void InitEffParam();
        bool IsActive() const;
        void Reset();

        // Static methods for compatibility with legacy code
        static bool GetEff_State(_effect_parameter* pThis, int effectType) {
            return pThis ? pThis->IsActive() : false;
        }

        static int GetEff_Plus(_effect_parameter* pThis, int effectType) {
            return pThis ? static_cast<int>(pThis->intensity) : 0;
        }

        // Instance methods
        bool GetEff_State(int effectType) const {
            return IsActive() && this->effectType == static_cast<uint32_t>(effectType);
        }

        int GetEff_Plus(int effectType) const {
            return (IsActive() && this->effectType == static_cast<uint32_t>(effectType)) ?
                   static_cast<int>(intensity) : 0;
        }
    };

    /**
     * @brief Timer class for character operations
     */
    class CMyTimer {
    public:
        CMyTimer();
        ~CMyTimer();
        
        void Start();
        void Stop();
        void Reset();
        uint32_t GetElapsed() const;
        bool IsRunning() const;
        
    private:
        std::chrono::steady_clock::time_point m_startTime;
        bool m_isRunning{false};
    };

    // _object_id is defined in CGameObject.h - using forward declaration here

    /**
     * @brief Character creation setup data
     */
    struct _character_create_setdata {
        _object_id* pObjectId{nullptr};
        uint32_t characterType{0};
        float position[3]{0.0f, 0.0f, 0.0f};
        float rotation[3]{0.0f, 0.0f, 0.0f};
        uint32_t flags{0};
        uint32_t level{1};
        uint32_t hp{100};
        uint32_t mp{100};
        uint32_t mapIndex{0};
        void* pRecordSet{nullptr};
        
        bool IsValid() const;
    };

    /**
     * @brief Base Character Class
     * 
     * This class provides the fundamental functionality for all character entities
     * in the game world. It handles position, movement, status effects, lifecycle,
     * and basic character properties.
     */
    class CCharacter : public CGameObject {
    public:
        // Constants
        static constexpr size_t MAX_SF_CONT = 192;          // 0x300 / 4 = 192 entries
        static constexpr size_t MAX_SF_CONT_AURA = 192;     // 0x300 / 4 = 192 entries
        static constexpr size_t MAX_DEF_PARTS = 5;
        static constexpr float DEFAULT_MOVE_SPEED = 5.0f;

        // Constructor and Destructor
        CCharacter();
        virtual ~CCharacter();

        // Core lifecycle methods
        virtual bool Create(const _character_create_setdata* pData);
        virtual bool Init(_object_id* pID);
        virtual bool Destroy();
        virtual void Update(float deltaTime);

        // Movement and position
        virtual void Move(float deltaTime);
        virtual void SetPosition(float x, float y, float z);
        virtual void SetPosition(const float* pos);
        virtual void GetPosition(float* pos) const;
        virtual void SetRotation(float x, float y, float z);
        virtual void GetRotation(float* rot) const;
        virtual float GetMoveSpeed() const { return m_fMoveSpeed; }
        virtual void SetMoveSpeed(float speed) { m_fMoveSpeed = speed; }

        // Status and state management
        virtual bool IsAlive() const { return m_bLive; }
        virtual void SetAlive(bool alive) { m_bLive = alive; }
        virtual bool GetStun() const { return (m_dwStatus & static_cast<uint32_t>(CharacterStatus::Stunned)) != 0; }
        virtual void SetStun(bool stunned);
        virtual bool IsInCombat() const { return (m_dwStatus & static_cast<uint32_t>(CharacterStatus::InCombat)) != 0; }
        virtual void SetCombatState(bool inCombat);

        // Health and mana
        virtual int GetHP() const { return m_nHP; }
        virtual void SetHP(int hp) { m_nHP = hp; }
        virtual int GetMaxHP() const { return m_nMaxHP; }
        virtual void SetMaxHP(int maxHp) { m_nMaxHP = maxHp; }
        virtual int GetMP() const { return m_nMP; }
        virtual void SetMP(int mp) { m_nMP = mp; }
        virtual int GetMaxMP() const { return m_nMaxMP; }
        virtual void SetMaxMP(int maxMp) { m_nMaxMP = maxMp; }

        // Level and experience
        virtual uint32_t GetLevel() const { return m_dwLevel; }
        virtual void SetLevel(uint32_t level) { m_dwLevel = level; }
        virtual uint64_t GetExp() const { return m_qwExp; }
        virtual void SetExp(uint64_t exp) { m_qwExp = exp; }

        // Object identification
        virtual uint32_t GetSerial() const { return m_dwObjSerial; }
        virtual void SetSerial(uint32_t serial) { m_dwObjSerial = serial; }
        virtual uint16_t GetMapLayerIndex() const { return m_wMapLayerIndex; }
        virtual void SetMapLayerIndex(uint16_t index) { m_wMapLayerIndex = index; }

        // Effect management
        virtual void ApplyEffect(const _effect_parameter& effect);
        virtual void RemoveEffect(uint32_t effectType);
        virtual void UpdateEffects(float deltaTime);
        virtual bool HasEffect(uint32_t effectType) const;

        // Status effect containers
        virtual void SetSFCont(int index, uint32_t value);
        virtual uint32_t GetSFCont(int index) const;
        virtual void SetSFContAura(int index, uint32_t value);
        virtual uint32_t GetSFContAura(int index) const;

        // Utility methods
        virtual bool ValidateState() const;
        virtual void LogError(const std::string& message, const std::string& function = "") const;
        virtual std::string GetDebugInfo() const;

    protected:
        // Core character data
        uint32_t m_dwObjSerial{0};                          ///< Object serial number
        uint16_t m_wMapLayerIndex{0};                       ///< Map layer index
        bool m_bLive{true};                                 ///< Is character alive
        uint32_t m_dwStatus{static_cast<uint32_t>(CharacterStatus::Alive)};  ///< Status flags

        // Position and movement
        float m_fCurPos[3]{0.0f, 0.0f, 0.0f};             ///< Current position [x, y, z]
        float m_fRotation[3]{0.0f, 0.0f, 0.0f};           ///< Current rotation [x, y, z]
        float m_fMoveSpeed{DEFAULT_MOVE_SPEED};             ///< Movement speed
        CharacterMoveState m_moveState{CharacterMoveState::Idle};  ///< Current movement state

        // Health and mana
        int m_nHP{100};                                     ///< Current health points
        int m_nMaxHP{100};                                  ///< Maximum health points
        int m_nMP{100};                                     ///< Current mana points
        int m_nMaxMP{100};                                  ///< Maximum mana points

        // Level and experience
        uint32_t m_dwLevel{1};                              ///< Character level
        uint64_t m_qwExp{0};                                ///< Experience points

        // Status effects and timers
        std::array<uint32_t, MAX_SF_CONT> m_SFCont{};      ///< Status effect container
        std::array<uint32_t, MAX_SF_CONT_AURA> m_SFContAura{};  ///< Aura effect container
        bool m_bLastContEffectUpdate{false};               ///< Last continuous effect update flag
        uint16_t m_wLastCont{0};                            ///< Last continuous effect ID

        // Effect management
        _effect_parameter m_EP;                             ///< Effect parameter
        CMyTimer m_tmrSFCont;                              ///< Status effect timer

        // Defense parts
        std::array<int, MAX_DEF_PARTS> m_nDefPart{};       ///< Defense parts array

        // Record set pointer
        void* m_pRecordSet{nullptr};                        ///< Pointer to record set data

        // Thread safety
        mutable std::mutex m_characterMutex;               ///< Mutex for thread safety

        // Internal methods
        virtual void InitializeDefaults();
        virtual void ResetEffects();
        virtual bool ValidateCreateData(const _character_create_setdata* pData) const;
        virtual void UpdatePosition(float deltaTime);
        virtual void UpdateStatusEffects(float deltaTime);

    private:
        // Static counters
        static uint32_t s_dwNextSerial;                     ///< Next available serial number
        static std::mutex s_serialMutex;                   ///< Mutex for serial generation

        // Internal utility methods
        uint32_t GenerateSerial();
        void InitializeStatusEffects();
        void CleanupEffects();
    };

    /**
     * @brief Utility functions for character management
     */
    namespace CharacterUtils {
        std::string CharacterStatusToString(CharacterStatus status);
        std::string CharacterMoveStateToString(CharacterMoveState state);
        bool IsValidPosition(const float* pos);
        float CalculateDistance(const float* pos1, const float* pos2);
        void NormalizeRotation(float* rotation);
    }

} // namespace NexusProtection::World

// Legacy C interface for compatibility
extern "C" {
    struct CCharacter_Legacy {
        void* vfptr;
        // Additional legacy fields would be defined based on actual structure
    };

    // Legacy function declarations
    void CCharacter_Constructor(CCharacter_Legacy* character);
    void CCharacter_Destructor(CCharacter_Legacy* character);
    bool CCharacter_Create(CCharacter_Legacy* character, _character_create_setdata* pData);
    void CCharacter_Init(CCharacter_Legacy* character, _object_id* pID);
    bool CCharacter_Destroy(CCharacter_Legacy* character);
    void CCharacter_Move(CCharacter_Legacy* character, float deltaTime);
}
