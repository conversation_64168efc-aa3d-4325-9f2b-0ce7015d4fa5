/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@0AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x140007473
 */

void __fastcall std::_Destroy_range<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, std::allocator<CUnmannedTraderRegistItemInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    _First,
    _Last,
    _Al,
    __formal);
}
