/*
 * Function: ??D?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEBAAEAPEAVCLogTypeDBTask@@XZ
 * Address: 0x1402C5100
 */

CLogTypeDBTask **__fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator*(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return (CLogTypeDBTask **)std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator*((std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)&v5->_Mycont);
}
