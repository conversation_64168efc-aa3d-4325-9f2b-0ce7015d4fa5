/*
 * Function: ?DeleteGuildBattleScheduleInfo@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404A0FD0
 */

bool __fastcall CRFWorldDatabase::DeleteGuildBattleScheduleInfo(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  unsigned __int64 v6; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v7; // [sp+460h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  sprintf(&Dest, "truncate table [dbo].[tbl_GuildBattleScheduleInfo]");
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v7->vfptr, &Dest, 1);
}
