/*
 * Function: ?take_ground_item@CMgrAvatorItemHistory@@QEAAXHEPEAU_db_con@_STORAGE_LIST@@PEADK1G1PEAM1@Z
 * Address: 0x1402381B0
 */

void __fastcall CMgrAvatorItemHistory::take_ground_item(CMgrAvatorItemHistory *this, int n, char byItemBoxCode, _STORAGE_LIST::_db_con *pItem, char *pszThrowerName, unsigned int dwThrowerSerial, char *pszThrowerID, unsigned __int16 wMonRecIndex, char *pMapCode, float *pfPos, char *pszFileName)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  char *v13; // rax@17
  char *v14; // rax@18
  __int64 v15; // [sp+0h] [bp-128h]@1
  char *v16; // [sp+20h] [bp-108h]@17
  unsigned __int64 v17; // [sp+28h] [bp-100h]@17
  char *v18; // [sp+30h] [bp-F8h]@17
  char *v19; // [sp+38h] [bp-F0h]@17
  char *v20; // [sp+40h] [bp-E8h]@17
  char *v21; // [sp+48h] [bp-E0h]@17
  int v22; // [sp+50h] [bp-D8h]@17
  char *v23; // [sp+58h] [bp-D0h]@17
  char *v24; // [sp+60h] [bp-C8h]@17
  char *v25; // [sp+68h] [bp-C0h]@18
  char *v26; // [sp+70h] [bp-B8h]@18
  _base_fld *v27; // [sp+80h] [bp-A8h]@4
  char Dest; // [sp+98h] [bp-90h]@7
  _base_fld *v29; // [sp+C8h] [bp-60h]@7
  char *v30; // [sp+D8h] [bp-50h]@17
  char *v31; // [sp+E0h] [bp-48h]@17
  int v32; // [sp+E8h] [bp-40h]@17
  int v33; // [sp+ECh] [bp-3Ch]@17
  int v34; // [sp+F0h] [bp-38h]@17
  char *v35; // [sp+F8h] [bp-30h]@18
  char *v36; // [sp+100h] [bp-28h]@18
  int v37; // [sp+108h] [bp-20h]@18
  int v38; // [sp+10Ch] [bp-1Ch]@18
  int v39; // [sp+110h] [bp-18h]@18
  unsigned __int64 v40; // [sp+118h] [bp-10h]@4
  CMgrAvatorItemHistory *v41; // [sp+130h] [bp+8h]@1
  char v42; // [sp+140h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v43; // [sp+148h] [bp+20h]@1

  v43 = pItem;
  v42 = byItemBoxCode;
  v41 = this;
  v11 = &v15;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  v40 = (unsigned __int64)&v15 ^ _security_cookie;
  v27 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  if ( dwThrowerSerial == -1 )
  {
    if ( v42 || wMonRecIndex == 0xFFFF )
    {
      switch ( v42 )
      {
        case 2:
          sprintf(&Dest, "cheat");
          break;
        case 3:
          sprintf(&Dest, "reward");
          break;
        case 4:
          sprintf(&Dest, "crystal");
          break;
        case 6:
          sprintf(&Dest, "holykeeper");
          break;
        default:
          sprintf(&Dest, "loot");
          break;
      }
    }
    else
    {
      v29 = CRecordData::GetRecord(&stru_1799C6210, wMonRecIndex);
      strcpy_0(&Dest, v29->m_strCode);
    }
    v30 = v41->m_szCurTime;
    v31 = v41->m_szCurDate;
    v32 = (signed int)ffloor(pfPos[2]);
    v33 = (signed int)ffloor(pfPos[1]);
    v34 = (signed int)ffloor(*pfPos);
    v13 = DisplayItemUpgInfo(v43->m_byTableCode, v43->m_dwLv);
    v24 = v30;
    v23 = v31;
    v22 = v32;
    LODWORD(v21) = v33;
    LODWORD(v20) = v34;
    v19 = pMapCode;
    v18 = &Dest;
    v17 = v43->m_lnUID;
    v16 = v13;
    sprintf(
      sData,
      "PICK UP: %s_%u_@%s[%I64u] mob(%s) \t{POS:%s (%d, %d, %d)} [%s %s]\r\n",
      v27->m_strCode,
      v43->m_dwDur);
  }
  else
  {
    v35 = v41->m_szCurTime;
    v36 = v41->m_szCurDate;
    v37 = (signed int)ffloor(pfPos[2]);
    v38 = (signed int)ffloor(pfPos[1]);
    v39 = (signed int)ffloor(*pfPos);
    v14 = DisplayItemUpgInfo(v43->m_byTableCode, v43->m_dwLv);
    v26 = v35;
    v25 = v36;
    LODWORD(v24) = v37;
    LODWORD(v23) = v38;
    v22 = v39;
    v21 = pMapCode;
    v20 = pszThrowerID;
    LODWORD(v19) = dwThrowerSerial;
    v18 = pszThrowerName;
    v17 = v43->m_lnUID;
    v16 = v14;
    sprintf(
      sData,
      "PICK UP: %s_%u_@%s[%I64u] twr(%s:%d id:%s) \t{POS:%s (%d, %d, %d)} [%s %s]\r\n",
      v27->m_strCode,
      v43->m_dwDur);
  }
  CMgrAvatorItemHistory::WriteFile(v41, pszFileName, sData);
}
