/*
 * Function: ?CheckGen@DfAIMgr@@SAHPEAVCMonsterAI@@PEAVCMonster@@@Z
 * Address: 0x140152E40
 */

int __usercall DfAIMgr::Check<PERSON><PERSON>@<eax>(CMonsterAI *pAI@<rcx>, CMonster *pMon@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@6
  float v6; // xmm0_4@9
  float v7; // xmm0_4@13
  float v8; // xmm0_4@14
  unsigned int v9; // eax@22
  float v10; // xmm1_4@22
  __int64 v11; // [sp+0h] [bp-58h]@1
  CCharacter *pDst; // [sp+20h] [bp-38h]@7
  float v13; // [sp+28h] [bp-30h]@9
  int nIndex; // [sp+2Ch] [bp-2Ch]@15
  CMonsterSkill *pSkill; // [sp+30h] [bp-28h]@18
  float v16; // [sp+38h] [bp-20h]@14
  float v17; // [sp+3Ch] [bp-1Ch]@22
  float v18; // [sp+40h] [bp-18h]@22
  CMonsterAI *v19; // [sp+60h] [bp+8h]@1
  CMonster *v20; // [sp+68h] [bp+10h]@1

  v20 = pMon;
  v19 = pAI;
  v3 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v19 && pMon )
  {
    pDst = CMonster::GetAttackTarget(pMon);
    if ( pDst )
    {
      GetSqrt(v20->m_fCurPos, pDst->m_fCurPos);
      v13 = a3;
      v6 = v20->m_fCurPos[1] - pDst->m_fCurPos[1];
      abs(v6);
      if ( v6 <= 200.0 )
      {
        if ( pDst->m_bMove && (((void (__fastcall *)(CMonster *))v20->vfptr->GetAttackRange)(v20), v6 < 50.0)
          || (v7 = FLOAT_50_0, v13 < 50.0) )
        {
          v8 = R3GetLoopTime() * 15.0;
          v16 = v8;
          CMonster::GetMoveSpeed(v20);
          v7 = v13 - (float)((float)(v16 * v8) * 0.40000001);
          v13 = v7;
        }
        for ( nIndex = 0; nIndex < 16; ++nIndex )
        {
          pSkill = CMonsterSkillPool::GetMonSkill(&v20->m_MonsterSkillPool, nIndex);
          if ( pSkill )
          {
            if ( CMonsterSkill::IsExit(pSkill) && !CMonsterSkill::GetType(pSkill) && !CMonsterSkill::GetUseType(pSkill) )
            {
              CMonster::GetSkillDelayTime(v20, pSkill);
              v17 = v7;
              v18 = (float)(signed int)GetLoopTime();
              v9 = CMonsterSkill::GetBeforeTime(pSkill);
              v10 = v18 - (float)(signed int)v9;
              v7 = v10;
              if ( v10 >= v17 )
              {
                CMonsterSkill::GetAttackDist(pSkill);
                if ( v10 >= v13 )
                  return CMonster::Attack(v20, pDst, pSkill);
              }
            }
          }
        }
        result = 0;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
