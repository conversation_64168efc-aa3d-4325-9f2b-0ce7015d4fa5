/*
 * Function: ?change_player@CGuildMasterEffect@@QEAA_NPEAVCPlayer@@EE@Z
 * Address: 0x1403F4A00
 */

char __fastcall CGuildMasterEffect::change_player(CGuildMasterEffect *this, CPlayer *pP, char byBeforeGrade, char byAfterGrade)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v7; // [sp+0h] [bp-38h]@1
  CGuildMasterEffect *v8; // [sp+40h] [bp+8h]@1
  CPlayer *pPa; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1
  char v11; // [sp+58h] [bp+20h]@1

  v11 = byAfterGrade;
  v10 = byBeforeGrade;
  pPa = pP;
  v8 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pP && (signed int)(unsigned __int8)byBeforeGrade <= 8 && (signed int)(unsigned __int8)byAfterGrade <= 8 )
  {
    if ( (unsigned __int8)byBeforeGrade == (unsigned __int8)byAfterGrade )
    {
      result = 1;
    }
    else if ( pP->m_Param.m_pGuild )
    {
      if ( pP->m_Param.m_byClassInGuild == 2 )
      {
        CGuildMasterEffect::adjust_effect(v8, pP, byBeforeGrade, 0);
        if ( (unsigned __int8)v11 >= (signed int)v8->m_byAdjustableGrade )
          CGuildMasterEffect::adjust_effect(v8, pPa, v11, 1);
        CGuildMasterEffect::show_to_all(v8, pPa, v10, v11, 2);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
