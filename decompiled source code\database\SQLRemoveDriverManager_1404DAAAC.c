/*
 * Function: SQLRemoveDriverManager
 * Address: 0x1404DAAAC
 */

int __fastcall SQLRemoveDriverManager(unsigned int *lpdwUsageCount)
{
  unsigned int *v1; // rbx@1
  __int64 (__cdecl *v2)(); // rax@1
  int result; // eax@2

  v1 = lpdwUsageCount;
  v2 = ODBC___GetSetupProc("SQLRemoveDriverManager");
  if ( v2 )
    result = ((int (__fastcall *)(unsigned int *))v2)(v1);
  else
    result = 0;
  return result;
}
