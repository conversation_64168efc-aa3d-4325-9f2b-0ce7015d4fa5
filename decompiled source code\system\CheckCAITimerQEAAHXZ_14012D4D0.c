/*
 * Function: ?Check@CAITimer@@QEAAHXZ
 * Address: 0x14012D4D0
 */

signed __int64 __fastcall CAITimer::Check(CAITimer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@5
  unsigned int v6; // [sp+24h] [bp-14h]@5
  CAITimer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_Delay )
  {
    v5 = timeGetTime();
    v6 = v5 - v7->m_BefTime;
    if ( v7->m_Delay < v6 )
    {
      v7->m_Delay = v7->m_DDelay;
      v7->m_BefTime = v5;
      return 1i64;
    }
    if ( (v6 & 0x80000000) != 0 )
      v7->m_BefTime = 0;
  }
  return 0i64;
}
