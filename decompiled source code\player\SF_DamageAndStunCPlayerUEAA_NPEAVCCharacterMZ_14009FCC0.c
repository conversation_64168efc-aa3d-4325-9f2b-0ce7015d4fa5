/*
 * Function: ?SF_DamageAndStun@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009FCC0
 */

char __fastcall CPlayer::SF_DamageAndStun(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  __int64 v7; // [sp+0h] [bp-58h]@1
  char v8; // [sp+20h] [bp-38h]@4
  int v9; // [sp+28h] [bp-30h]@4
  int v10; // [sp+30h] [bp-28h]@4
  char v11; // [sp+38h] [bp-20h]@4
  CGameObjectVtbl *v12; // [sp+40h] [bp-18h]@4
  CPlayer *v13; // [sp+60h] [bp+8h]@1
  CCharacter *v14; // [sp+68h] [bp+10h]@1

  v14 = pDstObj;
  v13 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = ((int (__fastcall *)(CPlayer *))v13->vfptr->GetLevel)(v13);
  v12 = v14->vfptr;
  v11 = 1;
  v10 = 0;
  v9 = -1;
  v8 = 1;
  ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v12->SetDamage)(v14, 0i64, v13, (unsigned int)v5);
  return 1;
}
