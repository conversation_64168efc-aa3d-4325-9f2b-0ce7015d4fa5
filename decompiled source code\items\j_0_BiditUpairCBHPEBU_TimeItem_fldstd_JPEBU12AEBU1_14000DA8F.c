/*
 * Function: j_??0?$_Bidit@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@_JPEBU12@AEBU12@@std@@QEAA@AEBU01@@Z
 * Address: 0x14000DA8F
 */

void __fastcall std::_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>::_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>(std::_Bidit<std::pair<int const ,_TimeItem_fld const *>,__int64,std::pair<int const ,_TimeItem_fld const *> const *,std::pair<int const ,_TimeItem_fld const *> const &> *this, std::_Bidit<std::pair<int const ,_TimeItem_fld const *>,__int64,std::pair<int const ,_TimeItem_fld const *> const *,std::pair<int const ,_TimeItem_fld const *> const &> *__that)
{
  std::_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>::_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>(
    this,
    __that);
}
