/*
 * Function: ?CheckLoadData@ItemCombineMgr@@SA_NXZ
 * Address: 0x1402AB8E0
 */

char __cdecl ItemCombineMgr::CheckLoadData()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  int v2; // eax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  _base_fld *v6; // [sp+28h] [bp-10h]@6

  v0 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  for ( n = 0; ; ++n )
  {
    v2 = CRecordData::GetRecordNum(&ItemCombineMgr::ms_tbl_ItemCombine);
    if ( n >= v2 )
      break;
    v6 = CRecordData::GetRecord(&ItemCombineMgr::ms_tbl_ItemCombine, n);
  }
  return 1;
}
