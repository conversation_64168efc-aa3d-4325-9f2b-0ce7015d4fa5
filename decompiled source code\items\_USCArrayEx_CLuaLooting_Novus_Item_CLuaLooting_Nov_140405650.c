/*
 * Function: _US::CArrayEx_CLuaLooting_Novus_Item_CLuaLooting_Novus_Item::_State_::_CArrayEx_CLuaLooting_Novus_Item_CLuaLooting_Novus_Item::_State__::_1_::dtor$0
 * Address: 0x140405650
 */

void __fastcall US::CArrayEx_CLuaLooting_Novus_Item_CLuaLooting_Novus_Item::_State_::_CArrayEx_CLuaLooting_Novus_Item_CLuaLooting_Novus_Item::_State__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  US::CArray<CLuaLooting_Novus_Item>::~CArray<CLuaLooting_Novus_Item>(*(US::CArray<CLuaLooting_Novus_Item> **)(a2 + 64));
}
