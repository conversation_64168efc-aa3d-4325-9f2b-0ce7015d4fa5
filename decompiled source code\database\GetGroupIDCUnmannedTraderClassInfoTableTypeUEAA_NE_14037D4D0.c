/*
 * Function: ?GetGroupID@CUnmannedTraderClassInfoTableType@@UEAA_NEGAEAE0@Z
 * Address: 0x14037D4D0
 */

char __fastcall CUnmannedTraderClassInfoTableType::GetGroupID(CUnmannedTraderClassInfoTableType *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byClass, char *bySubClass)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@6
  __int64 v8; // [sp+0h] [bp-B8h]@1
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > result; // [sp+28h] [bp-90h]@7
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > v10; // [sp+48h] [bp-70h]@11
  bool v11; // [sp+60h] [bp-58h]@8
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > v12; // [sp+68h] [bp-50h]@8
  char v13; // [sp+80h] [bp-38h]@10
  bool v14; // [sp+81h] [bp-37h]@12
  __int64 v15; // [sp+88h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v16; // [sp+90h] [bp-28h]@8
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Right; // [sp+98h] [bp-20h]@8
  CUnmannedTraderSubClassInfo *v18; // [sp+A0h] [bp-18h]@9
  CUnmannedTraderClassInfoTableType *v19; // [sp+C0h] [bp+8h]@1
  char v20; // [sp+C8h] [bp+10h]@1
  unsigned __int16 v21; // [sp+D0h] [bp+18h]@1
  char *v22; // [sp+D8h] [bp+20h]@1

  v22 = byClass;
  v21 = wItemTableIndex;
  v20 = byTableCode;
  v19 = this;
  v5 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = -2i64;
  if ( v19->m_byTableCode != (unsigned __int8)byTableCode
    || std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&v19->m_vecSubClass) )
  {
    v7 = 0;
  }
  else
  {
    std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::begin(
      &v19->m_vecSubClass,
      &result);
    while ( 1 )
    {
      v16 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(
              &v19->m_vecSubClass,
              &v12);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)v16;
      v11 = std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v16->_Mycont);
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v12);
      if ( !v11 )
        break;
      v18 = *std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator*(&result);
      if ( (unsigned __int8)((int (__fastcall *)(CUnmannedTraderSubClassInfo *, _QWORD, _QWORD, char *))v18->vfptr->GetGroupID)(
                              v18,
                              (unsigned __int8)v20,
                              v21,
                              bySubClass) )
      {
        *v22 = v19->m_dwID;
        v13 = 1;
        std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
        return v13;
      }
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator++(
        &result,
        &v10,
        0);
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v10);
    }
    v14 = 0;
    std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
    v7 = v14;
  }
  return v7;
}
