/*
 * Function: ?BoxItemDataCopy@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x140415500
 */

void __fastcall CGoldenBoxItemMgr::BoxItemDataCopy(CGoldenBoxItemMgr *this)
{
  char *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // [sp+0h] [bp-678h]@1
  CGoldenBoxItemMgr *v4; // [sp+680h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 410i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 4;
  }
  qmemcpy(&v3, &v4->m_golden_box_item_New, 0x658ui64);
  qmemcpy(&v4->m_golden_box_item_Old, &v3, sizeof(v4->m_golden_box_item_Old));
}
