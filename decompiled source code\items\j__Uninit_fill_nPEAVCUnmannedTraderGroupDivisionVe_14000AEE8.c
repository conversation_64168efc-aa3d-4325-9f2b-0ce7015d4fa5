/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderGroupDivisionVersionInfo@@_KV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000AEE8
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned __int64,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, unsigned __int64 _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned __int64,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
