/*
 * Function: j_??$_Uninit_copy@PEAVCUnmannedTraderSchedule@@PEAV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00AEAV?$allocator@VCUnmannedTraderSchedule@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400055A1
 */

CUnmannedTraderSchedule *__fastcall std::_Uninit_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::allocator<CUnmannedTraderSchedule> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
