/*
 * Function: ?IsInRegion@CMapOperation@@QEAA_NPEADPEAVCGameObject@@@Z
 * Address: 0x140197C50
 */

char __fastcall CMapOperation::IsInRegion(CMapOperation *this, char *pszRegionCode, CGameObject *pObj)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v5; // ecx@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  char *Str1; // [sp+28h] [bp-20h]@7
  CExtDummy *v10; // [sp+30h] [bp-18h]@9
  CMapOperation *v11; // [sp+50h] [bp+8h]@1
  const char *Str2; // [sp+58h] [bp+10h]@1
  CGameObject *v13; // [sp+60h] [bp+18h]@1

  v13 = pObj;
  Str2 = pszRegionCode;
  v11 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < v11->m_nRegionNum; ++j )
  {
    Str1 = v11->m_RegionData[j].szRegionData;
    if ( !strcmp_0(Str1, Str2) && (CMapData *)*((_QWORD *)Str1 + 4) == v13->m_pCurMap )
    {
      v5 = *((_WORD *)Str1 + 20);
      v10 = (CExtDummy *)(*((_QWORD *)Str1 + 4) + 608i64);
      if ( CExtDummy::IsInBBox(v10, v5, v13->m_fCurPos) )
        return 1;
    }
  }
  return 0;
}
