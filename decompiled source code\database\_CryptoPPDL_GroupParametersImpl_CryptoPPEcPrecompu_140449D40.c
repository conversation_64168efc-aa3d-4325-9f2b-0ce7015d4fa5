/*
 * Function: _CryptoPP::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint___::_DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint____::_1_::dtor$0
 * Address: 0x140449D40
 */

void __fastcall CryptoPP::DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint___::_DL_GroupParametersImpl_CryptoPP::EcPrecomputation_CryptoPP::ECP__CryptoPP::DL_FixedBasePrecomputationImpl_CryptoPP::ECPPoint__CryptoPP::DL_GroupParameters_CryptoPP::ECPPoint____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::~DL_GroupParameters<CryptoPP::ECPPoint>((CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)(*(_QWORD *)(a2 + 64) - 240i64 + 32));
}
