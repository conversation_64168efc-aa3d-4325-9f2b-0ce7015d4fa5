/*
 * Function: ?_check_embel_part@CPlayer@@QEAA_NPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400FF4C0
 */

bool __fastcall CPlayer::_check_embel_part(CPlayer *this, _STORAGE_LIST::_db_con *pFixingItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  int v5; // ecx@4
  bool result; // al@5
  int v7; // eax@6
  float v8; // xmm0_4@6
  __int64 v9; // [sp+0h] [bp-38h]@1
  int nTableCode; // [sp+20h] [bp-18h]@4
  float v11; // [sp+24h] [bp-14h]@6
  float v12; // [sp+28h] [bp-10h]@6
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v14; // [sp+48h] [bp+10h]@1

  v14 = pFixingItem;
  v13 = this;
  v2 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CPlayerDB::GetRaceSexCode(&v13->m_Param);
  v5 = v14->m_wItemIndex;
  nTableCode = v14->m_byTableCode;
  if ( IsItemEquipCivil(nTableCode, v5, v4) )
  {
    v11 = (float)GetItemEquipLevel(v14->m_byTableCode, v14->m_wItemIndex);
    v7 = CPlayerDB::GetLevel(&v13->m_Param);
    v8 = (float)v7;
    v12 = (float)v7;
    _effect_parameter::GetEff_Have(&v13->m_EP, 4);
    result = v11 <= (float)(v12 + v8);
  }
  else
  {
    result = 0;
  }
  return result;
}
