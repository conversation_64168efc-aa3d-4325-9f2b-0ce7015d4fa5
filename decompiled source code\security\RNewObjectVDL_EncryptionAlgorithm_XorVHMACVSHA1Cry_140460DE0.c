/*
 * Function: ??R?$NewObject@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@@CryptoPP@@QEBAPEAV?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@1@XZ
 * Address: 0x140460DE0
 */

CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *__fastcall CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>::operator()(CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *v6; // [sp+20h] [bp-18h]@4
  __int64 v7; // [sp+28h] [bp-10h]@5

  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *)operator new(8ui64);
  if ( v6 )
  {
    CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>(v6);
    v7 = v3;
  }
  else
  {
    v7 = 0i64;
  }
  return (CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *)v7;
}
