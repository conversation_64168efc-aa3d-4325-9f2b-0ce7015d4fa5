/*
 * Function: ?PushDQSUpdate@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x140414490
 */

void __usercall CGoldenBoxItemMgr::PushDQSUpdate(CGoldenBoxItemMgr *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  __int64 v6; // [sp-20h] [bp-19E8h]@1
  qry_case_golden_box_item v7; // [sp+20h] [bp-19A8h]@4
  char v8; // [sp+CF8h] [bp-CD0h]@4
  char v9; // [sp+1350h] [bp-678h]@4
  unsigned __int64 v10; // [sp+19A8h] [bp-20h]@4
  CGoldenBoxItemMgr *v11; // [sp+19D0h] [bp+8h]@1

  v11 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for ( i = 1654i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  qry_case_golden_box_item::qry_case_golden_box_item(&v7);
  v7.nSerial = v11->m_nDBSerial;
  qmemcpy(&v9, &v11->m_golden_box_item_New, 0x658ui64);
  qmemcpy(&v7.NewData, &v9, sizeof(v7.NewData));
  qmemcpy(&v8, &v11->m_golden_box_item_Old, 0x658ui64);
  qmemcpy(&v7.OldData, &v8, sizeof(v7.OldData));
  v5 = qry_case_golden_box_item::size(&v7);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -91, (char *)&v7, v5);
}
