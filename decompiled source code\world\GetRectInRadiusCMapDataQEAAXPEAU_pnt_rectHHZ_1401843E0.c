/*
 * Function: ?GetRectInRadius@CMapData@@QEAAXPEAU_pnt_rect@@HH@Z
 * Address: 0x1401843E0
 */

void __fastcall CMapData::GetRectInRadius(CMapData *this, _pnt_rect *pRect, int nRadius, int nSecNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  int v8; // [sp+24h] [bp-14h]@6
  CMapData *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (unsigned int)nSecNum < v9->m_SecInfo.m_nSecNum )
  {
    v7 = nSecNum % v9->m_SecInfo.m_nSecNumW;
    v8 = nSecNum / v9->m_SecInfo.m_nSecNumW;
    pRect->nStartx = v7 - nRadius;
    if ( pRect->nStartx < 0 )
      pRect->nStartx = 0;
    pRect->nStarty = v8 - nRadius;
    if ( pRect->nStarty < 0 )
      pRect->nStarty = 0;
    pRect->nEndx = nRadius + v7;
    if ( pRect->nEndx > v9->m_SecInfo.m_nSecNumW - 1 )
      pRect->nEndx = v9->m_SecInfo.m_nSecNumW - 1;
    pRect->nEndy = nRadius + v8;
    if ( pRect->nEndy > v9->m_SecInfo.m_nSecNumH - 1 )
      pRect->nEndy = v9->m_SecInfo.m_nSecNumH - 1;
  }
  else
  {
    memset_0(pRect, 0, 0x10ui64);
  }
}
