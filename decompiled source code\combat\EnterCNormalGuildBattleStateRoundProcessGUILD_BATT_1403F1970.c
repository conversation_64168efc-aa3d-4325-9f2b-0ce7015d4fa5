/*
 * Function: ?Enter@CNormalGuildBattleStateRoundProcess@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F1970
 */

__int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundProcess::Enter(GUILD_BATTLE::CNormalGuildBattleStateRoundProcess *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v4; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateRoundProcess *v7; // [sp+30h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+38h] [bp+10h]@1

  pkBattlea = pkBattle;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !v7->m_pkTimer )
  {
    v4 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(pkBattle);
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleStateRoundProcess::Enter( CNormalGuildBattle * pkBattle ) :  0 == m_pkTimer !");
  }
  CMyTimer::BeginTimer(v7->m_pkTimer, 0x3E8u);
  GUILD_BATTLE::CNormalGuildBattleStateRound::Log(
    (GUILD_BATTLE::CNormalGuildBattleStateRound *)&v7->vfptr,
    pkBattlea,
    "Enter : RoundProcess Start");
  return 0i64;
}
