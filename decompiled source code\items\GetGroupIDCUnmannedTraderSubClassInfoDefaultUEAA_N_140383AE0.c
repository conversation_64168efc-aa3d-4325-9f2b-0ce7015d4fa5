/*
 * Function: ?GetGroupID@CUnmannedTraderSubClassInfoDefault@@UEAA_NEGAEAE@Z
 * Address: 0x140383AE0
 */

bool __fastcall CUnmannedTraderSubClassInfoDefault::GetGroupID(CUnmannedTraderSubClassInfoDefault *this, char byTableCode, unsigned __int16 wItemTableIndex, char *bySubClass)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoDefault *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return CUnmannedTraderSubClassInfo::GetGroupID(
           (CUnmannedTraderSubClassInfo *)&v8->vfptr,
           byTableCode,
           wItemTableIndex,
           bySubClass);
}
