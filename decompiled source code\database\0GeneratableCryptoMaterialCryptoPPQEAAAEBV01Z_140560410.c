/*
 * Function: ??0GeneratableCryptoMaterial@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x140560410
 */

CryptoPP::GeneratableCryptoMaterial *__fastcall CryptoPP::GeneratableCryptoMaterial::GeneratableCryptoMaterial(CryptoPP::GeneratableCryptoMaterial *this, const struct CryptoPP::GeneratableCryptoMaterial *a2, int a3)
{
  CryptoPP::GeneratableCryptoMaterial *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  if ( a3 )
  {
    *(_QWORD *)&this->gap8[0] = &CryptoPP::GeneratableCryptoMaterial::`vbtable';
    if ( a2 )
      CryptoPP::CryptoMaterial::CryptoMaterial(
        (CryptoPP::CryptoMaterial *)&this->gap8[8],
        (const struct CryptoPP::CryptoMaterial *)&a2->gap8[*(_DWORD *)(*(_QWORD *)&a2->gap8[0] + 4i64)]);
    else
      CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)&this->gap8[8], 0i64);
  }
  return v4;
}
