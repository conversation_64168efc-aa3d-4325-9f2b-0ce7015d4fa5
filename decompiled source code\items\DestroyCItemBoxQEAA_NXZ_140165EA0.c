/*
 * Function: ?Destroy@CItemBox@@QEAA_NXZ
 * Address: 0x140165EA0
 */

bool __fastcall CItemBox::Destroy(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CItemBox *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_dwOwnerSerial = -1;
  v5->m_wOwnerIndex = -1;
  v5->m_dwThrowerSerial = -1;
  v5->m_wThrowerIndex = -1;
  v5->m_dwThrowerCharSerial = -1;
  v5->m_byThrowerID = -1;
  v5->m_wszThrowerName[0] = 0;
  v5->m_aszThrowerName[0] = 0;
  v5->m_szThrowerID[0] = 0;
  v5->m_byThrowerDegree = -1;
  v5->m_bPartyShare = 0;
  v5->m_byThrowerRaceCode = -1;
  v5->m_wMonRecIndex = -1;
  v5->m_bBossMob = 0;
  v5->m_dwEventPartyBoss = -1;
  v5->m_dwEventGuildSerial = -1;
  v5->m_byEventRaceCode = -1;
  v5->m_byEventLootAuth = 3;
  v5->m_bHolyScanner = 0;
  v5->m_dwLastDestroyTime = timeGetTime();
  CItemBox::SendMsg_Destroy(v5);
  --CItemBox::s_nLiveNum;
  return CGameObject::Destroy((CGameObject *)&v5->vfptr);
}
