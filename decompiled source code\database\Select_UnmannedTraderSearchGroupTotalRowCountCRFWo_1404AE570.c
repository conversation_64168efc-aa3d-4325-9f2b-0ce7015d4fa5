/*
 * Function: ?Select_UnmannedTraderSearchGroupTotalRowCount@CRFWorldDatabase@@QEAAEEEEEEAEAK@Z
 * Address: 0x1404AE570
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderSearchGroupTotalRowCount(CRFWorldDatabase *this, char byType, char byRace, char byClass1, char byClass2, char byClass3, unsigned int *dwCount)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v10; // [sp+0h] [bp-4A8h]@1
  SQLLEN BufferLength; // [sp+20h] [bp-488h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-480h]@4
  int v13; // [sp+30h] [bp-478h]@4
  int v14; // [sp+38h] [bp-470h]@4
  int v15; // [sp+40h] [bp-468h]@4
  SQLLEN v16; // [sp+58h] [bp-450h]@22
  __int16 v17; // [sp+64h] [bp-444h]@9
  char DstBuf; // [sp+80h] [bp-428h]@4
  char v19; // [sp+484h] [bp-24h]@16
  unsigned __int64 v20; // [sp+490h] [bp-18h]@4
  CRFWorldDatabase *v21; // [sp+4B0h] [bp+8h]@1

  v21 = this;
  v7 = &v10;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v20 = (unsigned __int64)&v10 ^ _security_cookie;
  v15 = (unsigned __int8)byClass3;
  v14 = (unsigned __int8)byClass2;
  v13 = (unsigned __int8)byClass1;
  LODWORD(StrLen_or_IndPtr) = (unsigned __int8)byType;
  LODWORD(BufferLength) = (unsigned __int8)byRace;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "select count(si.serial) from [dbo].[tbl_utsingleiteminfo] as si join [dbo].[tbl_utsellinfo] as s on s.type = %u and "
    "s.race = %u and s.serial = si.serial join [dbo].[tbl_utresultinfo] as r on r.type = %u and s.serial = r.serial where"
    " r.state in (1, 2) and si.class1=%u and si.class2=%u and si.class3=%u",
    (unsigned __int8)byType);
  if ( v21->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v21->vfptr, &DstBuf);
  if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
  {
    v17 = SQLExecDirectA_0(v21->m_hStmtSelect, &DstBuf, -3);
    if ( v17 && v17 != 1 )
    {
      if ( v17 == 100 )
      {
        result = 2;
      }
      else
      {
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v17, v21->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v17 = SQLFetch_0(v21->m_hStmtSelect);
      if ( v17 && v17 != 1 )
      {
        v19 = 0;
        if ( v17 == 100 )
        {
          v19 = 2;
        }
        else
        {
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v17, v21->m_hStmtSelect);
          v19 = 1;
        }
        if ( v21->m_hStmtSelect )
          SQLCloseCursor_0(v21->m_hStmtSelect);
        result = v19;
      }
      else
      {
        StrLen_or_IndPtr = &v16;
        BufferLength = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 1u, -18, dwCount, 0i64, &v16);
        if ( v17 && v17 != 1 )
        {
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v17, v21->m_hStmtSelect);
          if ( v21->m_hStmtSelect )
            SQLCloseCursor_0(v21->m_hStmtSelect);
          result = 1;
        }
        else
        {
          if ( v21->m_hStmtSelect )
            SQLCloseCursor_0(v21->m_hStmtSelect);
          if ( v21->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v21->vfptr, "%s Success", &DstBuf);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1;
  }
  return result;
}
