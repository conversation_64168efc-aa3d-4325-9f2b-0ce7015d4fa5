/*
 * Function: ?GetSID@CGuildBattleSchedulePool@GUILD_BATTLE@@QEAAKIK@Z
 * Address: 0x1403DEC80
 */

unsigned int __fastcall GUILD_BATTLE::CGuildBattleSchedulePool::GetSID(GUILD_BATTLE::CGuildBattleSchedulePool *this, unsigned int uiSLID, unsigned int dwStartInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedule *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleSchedulePool *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = GUILD_BATTLE::CGuildBattleSchedulePool::Get(v8, uiSLID, dwStartInx);
  return GUILD_BATTLE::CGuildBattleSchedule::GetSID(v5);
}
