/*
 * Function: ?dtor$0@?0??AddToRunnables@InternalContextBase@details@Concurrency@@MEAAXVlocation@3@@Z@4HA_9
 * Address: 0x14065B9A0
 */

int __fastcall `Concurrency::details::InternalContextBase::AddToRunnables'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 264);
  return std::_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>::~_Deque_const_iterator<unsigned __int64,std::allocator<unsigned __int64>,0>();
}
