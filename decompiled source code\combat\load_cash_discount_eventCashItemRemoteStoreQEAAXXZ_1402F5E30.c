/*
 * Function: ?load_cash_discount_event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F5E30
 */

void __fastcall CashItemRemoteStore::load_cash_discount_event(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CLogFile::Write(&v4->m_cde.m_cde_log, "Loading Cash Discount-Rate Event");
  CNetTimer::BeginTimer(&v4->m_cde.m_cde_timer, 0x3E8u);
  v4->m_cde.m_cde_inform_before[0] = 1800;
  v4->m_cde.m_cde_inform_before[1] = 300;
  v4->m_cde.m_cde_status = 0;
  CashItemRemoteStore::load_cde_ini(v4, &v4->m_cde.m_ini, &v4->m_cde.m_cde_ini_file_time);
  CashItemRemoteStore::log_about_cash_event(v4, "Loaded From Ini File <When Server Started>", &v4->m_cde.m_ini);
  CashItemRemoteStore::check_loaded_cde_status(v4);
  CLogFile::Write(&v4->m_cde.m_cde_log, "Complete For Cash Discount-Rate Event");
}
