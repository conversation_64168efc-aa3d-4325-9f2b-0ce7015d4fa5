/*
 * Function: ??0const_iterator@?$_Tree@V?$_Tmap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@@std@@@2@$0A@@std@@@std@@QEAA@XZ
 * Address: 0x140191BB0
 */

void __fastcall std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>::const_iterator::const_iterator(std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Bidit<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>,__int64,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList> const *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList> const &>::_Bidit<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>,__int64,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList> const *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList> const &>((std::_Bidit<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList>,__int64,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> const *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> const &> *)&v4->_Mycont);
  v4->_Ptr = 0i64;
}
