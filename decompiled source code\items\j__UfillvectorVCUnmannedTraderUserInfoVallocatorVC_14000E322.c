/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderUserInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000E322
 */

CUnmannedTraderUserInfo *__fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Ufill(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, CUnmannedTraderUserInfo *_Ptr, unsigned __int64 _Count, CUnmannedTraderUserInfo *_Val)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_U<PERSON>(this, _Ptr, _Count, _<PERSON>);
}
