/*
 * Function: j_?deallocate@?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@QEAAXPEAPEAVCUnmannedTraderClassInfo@@_K@Z
 * Address: 0x14001244A
 */

void __fastcall std::allocator<CUnmannedTraderClassInfo *>::deallocate(std::allocator<CUnmannedTraderClassInfo *> *this, CUnmannedTraderClassInfo **_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderClassInfo *>::deallocate(this, _Ptr, __formal);
}
