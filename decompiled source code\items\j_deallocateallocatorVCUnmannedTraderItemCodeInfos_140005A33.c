/*
 * Function: j_?deallocate@?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@QEAAXPEAVCUnmannedTraderItemCodeInfo@@_K@Z
 * Address: 0x140005A33
 */

void __fastcall std::allocator<CUnmannedTraderItemCodeInfo>::deallocate(std::allocator<CUnmannedTraderItemCodeInfo> *this, CUnmannedTraderItemCodeInfo *_Ptr, unsigned __int64 __formal)
{
  std::allocator<CUnmannedTraderItemCodeInfo>::deallocate(this, _Ptr, __formal);
}
