/*
 * Function: ?Init@CUserRankingProcess@@QEAA_NXZ
 * Address: 0x1403405E0
 */

bool __fastcall CUserRankingProcess::Init(CUserRankingProcess *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-68h]@1
  int piHour; // [sp+24h] [bp-44h]@6
  int piMin; // [sp+44h] [bp-24h]@6
  CUserRankingProcess *v7; // [sp+70h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7->m_iOldDay = GetCurrentDay();
  if ( v7->m_iOldDay == -1 )
  {
    CLogFile::Write(v7->m_pkLogger, "CUserRankingProcess::Init() : GetCurrentDay() Fail!");
    result = 0;
  }
  else
  {
    piHour = 23;
    piMin = 40;
    CUserRankingProcess::LoadINI(v7, &piHour, &piMin);
    if ( CUserRankingProcess::SetRankingStartTime(v7, piHour, piMin) )
    {
      if ( CUserRankingProcess::AllocObject(v7) )
      {
        if ( CUserRankingProcess::InitProcFunc(v7) )
        {
          result = CGuildRanking::Init(&v7->m_kGuildRanking) != 0;
        }
        else
        {
          CLogFile::Write(v7->m_pkLogger, "CUserRankingProcess::Init() : InitProcFunc() Fail!");
          result = 0;
        }
      }
      else
      {
        CLogFile::Write(v7->m_pkLogger, "CUserRankingProcess::Init() : AllocObject() Fail!");
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        v7->m_pkLogger,
        "CUserRankingProcess::Init() : SetRankingStartTime( iHour(%d), iMin(%d) ) Fail",
        (unsigned int)piHour,
        (unsigned int)piMin);
      result = 0;
    }
  }
  return result;
}
