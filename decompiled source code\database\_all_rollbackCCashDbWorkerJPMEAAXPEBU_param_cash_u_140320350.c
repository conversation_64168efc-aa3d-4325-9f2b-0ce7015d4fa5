/*
 * Function: ?_all_rollback@CCashDbWorkerJP@@MEAAXPEBU_param_cash_update@@@Z
 * Address: 0x140320350
 */

void __fastcall CCashDbWorkerJP::_all_rollback(CCashDbWorkerJP *this, _param_cash_update *psheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@4
  char *v5; // rax@7
  int v6; // eax@9
  __int64 v7; // [sp+0h] [bp-4A8h]@1
  char *v8; // [sp+20h] [bp-488h]@7
  _param_cash_rollback v9; // [sp+40h] [bp-468h]@4
  int j; // [sp+474h] [bp-34h]@4
  char *v11; // [sp+478h] [bp-30h]@6
  __int64 v12; // [sp+488h] [bp-20h]@4
  CLogFile *v13; // [sp+490h] [bp-18h]@7
  unsigned __int64 v14; // [sp+498h] [bp-10h]@4
  CCashDbWorkerJP *v15; // [sp+4B0h] [bp+8h]@1
  _param_cash_update *v16; // [sp+4B8h] [bp+10h]@1

  v16 = psheet;
  v15 = this;
  v2 = &v7;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  _param_cash_rollback::_param_cash_rollback(
    &v9,
    psheet->in_dwAccountSerial,
    psheet->in_dwAvatorSerial,
    psheet->in_wSockIndex);
  strcpy_s(v9.in_szAcc, 0xDui64, v16->in_szAcc);
  v4 = inet_ntoa((struct in_addr)v16->in_dwIP);
  strcpy_s(v9.in_UserIP, 0xFui64, v4);
  strcpy_s(v9.in_szWorldName, 0x21ui64, v16->in_szSvrName);
  strcpy_s(v9.in_szAvatorName, 0x11ui64, v16->in_szAvatorName);
  for ( j = 0; j < v16->in_nNum10; ++j )
  {
    v11 = &v16->in_item[(signed __int64)j].byRet;
    if ( !*v11 )
    {
      v9.data[v9.in_byNum].in_lnUID = v16->in_item[(signed __int64)j].in_lnUID;
      v9.data[v9.in_byNum].in_nPrice = v16->in_item[(signed __int64)j].in_nPrice;
      v9.data[v9.in_byNum].in_nDiscount = v16->in_item[(signed __int64)j].in_nDiscount;
      v9.data[v9.in_byNum].in_byOverlapNum = v16->in_item[(signed __int64)j].in_byOverlapNum;
      strcpy_s(v9.data[v9.in_byNum].in_strItemCode, 8ui64, v11 + 1);
      ++v9.in_byNum;
      v5 = GetItemKorName((unsigned __int8)v11[10], *((_WORD *)v11 + 6));
      v13 = v15->_kLogger;
      v8 = v5;
      CLogFile::Write(v15->_kLogger, "Rollback >> %I64d-%s(%s)", *((_QWORD *)v11 + 4), v11 + 1);
    }
  }
  v6 = _param_cash_rollback::size(&v9);
  CashDbWorker::PushTask((CashDbWorker *)&v15->vfptr, 2, (char *)&v9, v6);
  _param_cash_rollback::~_param_cash_rollback(&v9);
}
