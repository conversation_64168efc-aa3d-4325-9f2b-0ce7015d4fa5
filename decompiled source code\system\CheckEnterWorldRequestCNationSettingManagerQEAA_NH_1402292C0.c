/*
 * Function: ?CheckEnterWorldRequest@CNationSettingManager@@QEAA_NHPEAD@Z
 * Address: 0x1402292C0
 */

bool __fastcall CNationSettingManager::CheckEnterWorldRequest(CNationSettingManager *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  INationGameGuardSystem *v7; // [sp+20h] [bp-18h]@5
  CNationSettingManager *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1
  char *v10; // [sp+50h] [bp+18h]@1

  v10 = pBuf;
  v9 = n;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !CNationSettingData::GetGameGuardSystem(v8->m_pData)
    || (v7 = CNationSettingData::GetGameGuardSystem(v8->m_pData),
        (unsigned __int8)((int (__fastcall *)(INationGameGuardSystem *, _QWORD))v7->vfptr->OnCheckSession_FirstVerify)(
                           v7,
                           (unsigned int)v9)) )
  {
    result = (unsigned __int8)((int (__fastcall *)(CNationSettingData *, _QWORD, char *))v8->m_pData->vfptr->CheckEnterWorldRequest)(
                                v8->m_pData,
                                (unsigned int)v9,
                                v10) != 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
