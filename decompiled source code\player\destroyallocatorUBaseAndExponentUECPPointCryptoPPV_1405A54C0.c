/*
 * Function: ?destroy@?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A54C0
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::destroy(__int64 a1, __int64 a2)
{
  return std::_Destroy<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(a2);
}
