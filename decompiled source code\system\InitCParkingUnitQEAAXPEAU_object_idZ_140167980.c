/*
 * Function: ?Init@CParkingUnit@@QEAAXPEAU_object_id@@@Z
 * Address: 0x140167980
 */

void __fastcall CParkingUnit::Init(CParkingUnit *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CParkingUnit *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CGameObject::Init((CGameObject *)&v6->vfptr, pID);
  v6->m_dwLastDestroyTime = 0;
  v6->m_byFrame = -1;
  for ( j = 0; j < 6; ++j )
    v6->m_byPartCode[j] = -1;
  v6->m_byCreateType = -1;
  v6->m_byTransDistCode = -1;
  v6->m_wHPRate = 0;
}
