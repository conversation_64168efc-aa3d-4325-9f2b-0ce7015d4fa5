/*
 * Function: ??$_Construct@URECV_DATA@@U1@@std@@YAXPEAURECV_DATA@@AEBU1@@Z
 * Address: 0x14031B0D0
 */

void __fastcall std::_Construct<RECV_DATA,RECV_DATA>(RECV_DATA *_Ptr, RECV_DATA *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  void *_Where; // [sp+20h] [bp-48h]@4
  char *v6; // [sp+28h] [bp-40h]@4
  char v7; // [sp+30h] [bp-38h]@5
  RECV_DATA *v8; // [sp+70h] [bp+8h]@1
  RECV_DATA *v9; // [sp+78h] [bp+10h]@1

  v9 = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 22i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _Where = v8;
  v6 = (char *)operator new(0x18ui64, v8);
  if ( v6 )
  {
    qmemcpy(&v7, v9, 0x18ui64);
    qmemcpy(v6, &v7, 0x18ui64);
  }
}
