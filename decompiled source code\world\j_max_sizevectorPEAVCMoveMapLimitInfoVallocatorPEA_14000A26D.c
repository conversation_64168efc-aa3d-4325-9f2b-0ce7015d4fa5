/*
 * Function: j_?max_size@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x14000A26D
 */

unsigned __int64 __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::max_size(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::max_size(this);
}
