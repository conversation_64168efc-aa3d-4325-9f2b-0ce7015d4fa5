/*
 * Function: ?pc_TrunkHintAnswerRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400FB170
 */

void __fastcall CPlayer::pc_TrunkHintAnswerRequest(CPlayer *this, char *pwszAnswer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char v5; // [sp+20h] [bp-58h]@4
  char Dest; // [sp+38h] [bp-40h]@10
  char v7; // [sp+39h] [bp-3Fh]@10
  unsigned __int64 v8; // [sp+60h] [bp-18h]@4
  CPlayer *p; // [sp+80h] [bp+8h]@1
  const char *Str2; // [sp+88h] [bp+10h]@1

  Str2 = pwszAnswer;
  p = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v4 ^ _security_cookie;
  v5 = 0;
  if ( IsBeNearStore(p, 10) )
  {
    if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) > 0 )
    {
      if ( strcmp_0(p->m_Param.m_wszTrunkHintAnswer, Str2) )
        v5 = 15;
    }
    else
    {
      v5 = 2;
    }
  }
  else
  {
    v5 = 13;
  }
  Dest = 0;
  memset(&v7, 0, 0xCui64);
  if ( !v5 )
    strcpy_0(&Dest, p->m_Param.m_wszTrunkPasswd);
  CPlayer::SendMsg_TrunkHintAnswerResult(p, v5, &Dest);
}
