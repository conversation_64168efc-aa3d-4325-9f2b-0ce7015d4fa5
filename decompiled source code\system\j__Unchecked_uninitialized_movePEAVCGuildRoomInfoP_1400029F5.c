/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAVCGuildRoomInfo@@PEAV1@V?$allocator@VCGuildRoomInfo@@@std@@@stdext@@YAPEAVCGuildRoomInfo@@PEAV1@00AEAV?$allocator@VCGuildRoomInfo@@@std@@@Z
 * Address: 0x1400029F5
 */

CGuildRoomInfo *__fastcall stdext::_Unchecked_uninitialized_move<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>>(CGuildRoomInfo *_First, CGuildRoomInfo *_Last, CGuildRoomInfo *_Dest, std::allocator<CGuildRoomInfo> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>>(
           _First,
           _Last,
           _Dest,
           _<PERSON>);
}
