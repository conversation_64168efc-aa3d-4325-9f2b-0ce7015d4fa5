/*
 * Function: ?ct_ut_cancel_regist@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402960E0
 */

bool __fastcall ct_ut_cancel_regist(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderController *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -1;
  if ( s_nWordCount == 1 )
  {
    v6 = atoi(s_pwszDstCheat[0]);
  }
  else if ( s_nWordCount )
  {
    return 0;
  }
  v4 = CUnmannedTraderController::Instance();
  return CUnmannedTraderController::CheatCancelRegist(v4, v7->m_ObjID.m_wIndex, v7->m_dwObjSerial, v6);
}
