/*
 * Function: ?Is<PERSON><PERSON>ber@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NK@Z
 * Address: 0x1403EB1B0
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::IsMember(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(v6, dwSerial) >= 0;
}
