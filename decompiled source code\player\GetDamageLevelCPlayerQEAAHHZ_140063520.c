/*
 * Function: ?GetDamageLevel@CPlayer@@QEAAHH@Z
 * Address: 0x140063520
 */

signed __int64 __fastcall CPlayer::GetDamageLevel(CPlayer *this, int nAttackPart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@8
  _base_fld *v7; // [sp+28h] [bp-10h]@10
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1

  v9 = nAttackPart;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPlayer::IsRidingUnit(v8) )
  {
    result = 1i64;
  }
  else if ( v9 <= 5 )
  {
    v6 = &v8->m_Param.m_dbEquip.m_pStorageList[v9].m_bLoad;
    if ( *v6 )
    {
      v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v9, *(_WORD *)(v6 + 3));
      if ( v7 )
        result = *(_DWORD *)&v7[4].m_strCode[8];
      else
        result = 1i64;
    }
    else
    {
      result = 1i64;
    }
  }
  else
  {
    result = 1i64;
  }
  return result;
}
