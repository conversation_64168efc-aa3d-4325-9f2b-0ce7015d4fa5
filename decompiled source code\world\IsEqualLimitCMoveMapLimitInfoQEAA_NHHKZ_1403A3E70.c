/*
 * Function: ?IsEqualLimit@CMoveMapLimitInfo@@QEAA_NHHK@Z
 * Address: 0x1403A3E70
 */

bool __fastcall CMoveMapLimitInfo::IsEqualLimit(CMoveMapLimitInfo *this, int iType, int iMapInx, unsigned int dwStoreRecordIndex)
{
  int *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // [sp+0h] [bp-18h]@1
  CMoveMapLimitInfo *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 4i64; i; --i )
  {
    *v4 = -858993460;
    ++v4;
  }
  return v8->m_eType == iType && v8->m_iMapInx == iMapInx;
}
