/*
 * Function: ?make_skill_attack_param@CMonster@@QEAA_NPEAVCCharacter@@PEAVCMonsterSkill@@HPEAU_attack_param@@@Z
 * Address: 0x14014E260
 */

char __usercall CMonster::make_skill_attack_param@<al>(CMonster *this@<rcx>, CCharacter *pDst@<rdx>, CMonsterSkill *pSkill@<r8>, int nEffectType@<r9d>, float a5@<xmm0>, _attack_param *pAP)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v9; // [sp+0h] [bp-38h]@1
  _base_fld *v10; // [sp+20h] [bp-18h]@16
  CMonster *v11; // [sp+40h] [bp+8h]@1
  CCharacter *v12; // [sp+48h] [bp+10h]@1
  CMonsterSkill *v13; // [sp+50h] [bp+18h]@1
  int v14; // [sp+58h] [bp+20h]@1

  v14 = nEffectType;
  v13 = pSkill;
  v12 = pDst;
  v11 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( pSkill && (CMonsterSkill::GetType(pSkill) == 1 || CMonsterSkill::GetType(v13) == 2) )
  {
    pAP->pDst = v12;
    if ( v12 )
      pAP->nPart = CCharacter::GetAttackRandomPart(v12);
    else
      pAP->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&v11->vfptr);
    pAP->nClass = v11->m_pMonRec->m_bAttRangeType;
    pAP->nTol = CMonsterSkill::GetElement(v13);
    pAP->nMinAF = CMonsterSkill::GetMinDmg(v13);
    pAP->nMaxAF = CMonsterSkill::GetMaxDmg(v13);
    pAP->nMinSel = CMonsterSkill::GetMinProb(v13);
    pAP->nMaxSel = CMonsterSkill::GetMaxProb(v13);
    pAP->nExtentRange = 20;
    pAP->nShotNum = 1;
    pAP->nAddAttPnt = 0;
    pAP->pFld = CMonsterSkill::GetFld(v13);
    if ( v14 )
    {
      pAP->byEffectCode = 2;
      pAP->nLevel = 1;
      pAP->nMastery = 99;
    }
    else
    {
      pAP->byEffectCode = 0;
      pAP->nLevel = CMonsterSkill::GetSFLv(v13);
      _effect_parameter::GetEff_Plus(&v11->m_EP, 19);
      pAP->nLevel = (signed int)ffloor((float)pAP->nLevel + a5);
      if ( pAP->nLevel > 7 )
        pAP->nLevel = 7;
      pAP->nMastery = 99;
    }
    v10 = pAP->pFld;
    if ( v12 )
      memcpy_0(pAP->fArea, v12->m_fCurPos, 0xCui64);
    else
      memcpy_0(pAP->fArea, v11->m_fCurPos, 0xCui64);
    pAP->nMaxAttackPnt = 0;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
