/*
 * Function: ?InitData@_AVATOR_DATA@@QEAAXXZ
 * Address: 0x140077A00
 */

void __fastcall _AVATOR_DATA::InitData(_AVATOR_DATA *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _AVATOR_DATA *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _AVATOR_DB_BASE::Init(&v4->dbAvator);
  _LINK_DB_BASE::Init(&v4->dbLink);
  _EQUIP_DB_BASE::Init(&v4->dbEquip);
  _FORCE_DB_BASE::Init(&v4->dbForce);
  _ANIMUS_DB_BASE::Init(&v4->dbAnimus);
  _STAT_DB_BASE::Init(&v4->dbStat);
  _INVEN_DB_BASE::Init(&v4->dbInven);
  _CUTTING_DB_BASE::Init(&v4->dbCutting);
  _CUTTING_DB_BASE::ReSetOldDataLoad(&v4->dbCutting);
  _QUEST_DB_BASE::Init(&v4->dbQuest);
  _SFCONT_DB_BASE::Init(&v4->dbSfcont);
  _TRADE_DB_BASE::Init(&v4->dbTrade);
  _BUDDY_DB_BASE::Init(&v4->dbBuddy);
  _TRUNK_DB_BASE::Init(&v4->dbTrunk);
  _ITEMCOMBINE_DB_BASE::Init(&v4->dbItemCombineEx);
  _PERSONALAMINE_INVEN_DB_BASE::Init(&v4->dbPersonalAmineInven);
  _PVPPOINT_LIMIT_DB_BASE::Init(&v4->dbPvpPointLimit);
  _POSTDATA_DB_BASE::Init(&v4->dbPostData);
  _CRYMSG_DB_BASE::Init(&v4->dbBossCry);
  _PVP_ORDER_VIEW_DB_BASE::Init(&v4->dbPvpOrderView);
  _SUPPLEMENT_DB_BASE::Init(&v4->dbSupplement);
  _PCBANG_PLAY_TIME::Init(&v4->dbPlayTimeInPcbang);
  _POTION_NEXT_USE_TIME_DB_BASE::Init(&v4->dbPotionNextUseTime);
  _PCBANG_FAVOR_ITEM_DB_BASE::Init(&v4->dbPcBangFavorItem);
  v4->m_bCristalBattleDateUpdate = 1;
}
