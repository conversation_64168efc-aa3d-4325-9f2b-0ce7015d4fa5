/*
 * Function: j_?_Ufill@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x14000CF5E
 */

CMoveMapLimitRight **__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Ufill(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_Ptr, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_U<PERSON>(this, _Ptr, _Count, _<PERSON>);
}
