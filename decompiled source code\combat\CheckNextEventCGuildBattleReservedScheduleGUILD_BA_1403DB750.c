/*
 * Function: ?CheckNextEvent@CGuildBattleReservedSchedule@GUILD_BATTLE@@AEAA_NH@Z
 * Address: 0x1403DB750
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::CheckNextEvent(GUILD_BATTLE::CGuildBattleReservedSchedule *this, int iRet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@7
  GUILD_BATTLE::CGuildBattleReservedSchedule *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( iRet )
  {
    if ( iRet == -1 )
    {
      v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v7 = v8->m_uiCurScheduleInx;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v4,
        "CGuildBattleReservedSchedule::CheckNextEvent( iRet(%d) ) : m_uiScheduleID(%u) : -1 == m_pkSchedule[%u]->Check() Invalid!",
        0xFFFFFFFFi64,
        v8->m_uiCurScheduleInx);
    }
    else if ( iRet == 1 )
    {
      return 1;
    }
  }
  else
  {
    v8->m_pkSchedule[v8->m_uiCurScheduleInx] = 0i64;
  }
  return 0;
}
