/*
 * Function: ?GetNewFileName@CMgrAvatorItemHistory@@QEAAXKPEAD@Z
 * Address: 0x140235F80
 */

void __fastcall CMgrAvatorItemHistory::GetNewFileName(CMgrAvatorItemHistory *this, unsigned int dwAvatorSerial, char *pszFileName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-228h]@1
  char *v6; // [sp+20h] [bp-208h]@4
  unsigned int v7; // [sp+28h] [bp-200h]@13
  char *v8; // [sp+30h] [bp-1F8h]@13
  unsigned int v9; // [sp+40h] [bp-1E8h]@4
  char Dest; // [sp+60h] [bp-1C8h]@4
  unsigned int v11; // [sp+E4h] [bp-144h]@4
  char PathName; // [sp+100h] [bp-128h]@4
  char v13; // [sp+194h] [bp-94h]@13
  char v14; // [sp+1B4h] [bp-74h]@5
  char v15; // [sp+1D4h] [bp-54h]@8
  char v16; // [sp+1F4h] [bp-34h]@11
  unsigned int v17; // [sp+204h] [bp-24h]@7
  unsigned int v18; // [sp+208h] [bp-20h]@10
  unsigned __int64 v19; // [sp+218h] [bp-10h]@4
  CMgrAvatorItemHistory *v20; // [sp+230h] [bp+8h]@1
  unsigned int v21; // [sp+238h] [bp+10h]@1
  char *v22; // [sp+240h] [bp+18h]@1

  v22 = pszFileName;
  v21 = dwAvatorSerial;
  v20 = this;
  v3 = &v5;
  for ( i = 136i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = (unsigned __int64)&v5 ^ _security_cookie;
  v9 = GetLocalDate();
  sprintf(&Dest, "%s\\%d", v20, v9);
  CreateDirectoryA(&Dest, 0i64);
  v20->m_dwLastLocalDate = v9;
  v11 = GetCurrentHour();
  LODWORD(v6) = v11;
  sprintf(&PathName, "%s\\%d\\%d", v20, v20->m_dwLastLocalDate);
  CreateDirectoryA(&PathName, 0i64);
  v20->m_dwLastLocalHour = v11;
  if ( v11 <= 9 )
    sprintf(&v14, "0%d", v11);
  else
    sprintf(&v14, "%d", v11);
  v17 = GetCurrentMin();
  if ( v17 <= 9 )
    sprintf(&v15, "0%d", v17);
  else
    sprintf(&v15, "%d", v17);
  v18 = GetCurrentSec();
  if ( v18 <= 9 )
    sprintf(&v16, "0%d", v18);
  else
    sprintf(&v16, "%d", v18);
  v6 = &v16;
  sprintf(&v13, "%s%s%s", &v14, &v15);
  v8 = &v13;
  v7 = v21;
  LODWORD(v6) = v20->m_dwLastLocalHour;
  sprintf(v22, "%s\\%d\\%d\\%d_%s.his", v20, v20->m_dwLastLocalDate);
}
