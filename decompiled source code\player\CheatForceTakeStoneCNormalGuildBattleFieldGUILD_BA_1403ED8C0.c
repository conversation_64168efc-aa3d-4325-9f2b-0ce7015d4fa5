/*
 * Function: ?CheatForceTakeStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403ED8C0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::CheatForceTakeStone(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+30h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+38h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bInit )
  {
    GUILD_BATTLE::CNormalGuildBattleField::ClearRegen(v6);
    CGravityStone::SetOwner(v6->m_pkBall, pkPlayera);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
