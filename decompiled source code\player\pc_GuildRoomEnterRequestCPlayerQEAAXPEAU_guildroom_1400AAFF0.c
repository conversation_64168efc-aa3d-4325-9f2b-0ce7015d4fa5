/*
 * Function: ?pc_GuildRoomEnterRequest@CPlayer@@QEAAXPEAU_guildroom_enter_request_clzo@@@Z
 * Address: 0x1400AAFF0
 */

void __fastcall CPlayer::pc_GuildRoomEnterRequest(CPlayer *this, _guildroom_enter_request_clzo *pProtocol)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v4; // rax@15
  CGuildRoomSystem *v5; // rax@17
  CGuildRoomSystem *v6; // rax@19
  CGuildRoomSystem *v7; // rax@20
  CGuildRoomSystem *v8; // rax@21
  CGuildRoomSystem *v9; // rax@21
  __int64 v10; // [sp+0h] [bp-118h]@1
  unsigned __int16 wMapLayer[4]; // [sp+20h] [bp-F8h]@19
  float *pPos; // [sp+28h] [bp-F0h]@19
  char v13; // [sp+40h] [bp-D8h]@4
  char v14; // [sp+41h] [bp-D7h]@4
  char v15; // [sp+42h] [bp-D6h]@4
  unsigned __int16 v16; // [sp+54h] [bp-C4h]@4
  float pfStartPos; // [sp+78h] [bp-A0h]@4
  int v18; // [sp+7Ch] [bp-9Ch]@4
  int v19; // [sp+80h] [bp-98h]@4
  int v20; // [sp+A4h] [bp-74h]@4
  CGuild *v21; // [sp+B8h] [bp-60h]@13
  CMapData *pMap; // [sp+C0h] [bp-58h]@19
  char byRoomType; // [sp+D4h] [bp-44h]@19
  CUserDB *v24; // [sp+E8h] [bp-30h]@17
  int n; // [sp+F0h] [bp-28h]@17
  CUserDB *v26; // [sp+F8h] [bp-20h]@20
  int v27; // [sp+100h] [bp-18h]@20
  int v28; // [sp+104h] [bp-14h]@21
  CPlayer *v29; // [sp+120h] [bp+8h]@1
  _guildroom_enter_request_clzo *v30; // [sp+128h] [bp+10h]@1

  v30 = pProtocol;
  v29 = this;
  v2 = &v10;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = 0;
  v14 = 3;
  v15 = 0;
  v16 = 0;
  pfStartPos = 0.0;
  v18 = 0;
  v19 = 0;
  v20 = 0;
  if ( CGameObject::GetCurSecNum((CGameObject *)&v29->vfptr) == -1 || v29->m_bMapLoading )
  {
    v13 = 5;
    CPlayer::SendMsg_GuildRoomEnterResult(v29, 5, v14, v15, v16, &pfStartPos, v20);
  }
  else if ( CPlayer::IsRidingUnit(v29) )
  {
    v13 = 6;
    CPlayer::SendMsg_GuildRoomEnterResult(v29, 6, v14, v15, v16, &pfStartPos, v20);
  }
  else if ( v29->m_byStandType == 1 )
  {
    v13 = 7;
    CPlayer::SendMsg_GuildRoomEnterResult(v29, 7, v14, v15, v16, &pfStartPos, v20);
  }
  else if ( v29->m_Param.m_pGuild )
  {
    v21 = v29->m_Param.m_pGuild;
    if ( v30->dwGuildSerial == v21->m_dwSerial )
    {
      v4 = CGuildRoomSystem::GetInstance();
      if ( CGuildRoomSystem::IsRoomRented(v4, v21->m_dwSerial) )
      {
        v24 = v29->m_pUserDB;
        n = v29->m_ObjID.m_wIndex;
        v5 = CGuildRoomSystem::GetInstance();
        v14 = CGuildRoomSystem::RoomIn(v5, v21->m_dwSerial, n, v24->m_dwSerial);
        if ( v14 )
        {
          v13 = 3;
          CPlayer::SendMsg_GuildRoomEnterResult(v29, 3, v14, v15, v16, &pfStartPos, v20);
        }
        else
        {
          pMap = 0i64;
          byRoomType = 2;
          v6 = CGuildRoomSystem::GetInstance();
          pPos = (float *)&byRoomType;
          *(_QWORD *)wMapLayer = &v16;
          if ( CGuildRoomSystem::GetMapPos(v6, v21->m_dwSerial, &pfStartPos, pMap, &v16, &byRoomType) )
          {
            v13 = 10;
            v28 = CPlayerDB::GetRaceCode(&v29->m_Param);
            v8 = CGuildRoomSystem::GetInstance();
            pMap = CGuildRoomSystem::GetMapData(v8, v28, byRoomType);
            CPlayer::OutOfMap(v29, pMap, v16, 3, &pfStartPos);
            v15 = pMap->m_pMapSet->m_dwIndex;
            v9 = CGuildRoomSystem::GetInstance();
            CGuildRoomSystem::GetRestTime(v9, v21->m_dwSerial, &v20);
            CPlayer::SendMsg_GuildRoomEnterResult(v29, v13, v14, v15, v16, &pfStartPos, v20);
          }
          else
          {
            v26 = v29->m_pUserDB;
            v27 = v29->m_ObjID.m_wIndex;
            v7 = CGuildRoomSystem::GetInstance();
            CGuildRoomSystem::RoomOut(v7, v21->m_dwSerial, v27, v26->m_dwSerial);
            v13 = 4;
            CPlayer::SendMsg_GuildRoomEnterResult(v29, 4, v14, v15, v16, &pfStartPos, v20);
          }
        }
      }
      else
      {
        v13 = 2;
        CPlayer::SendMsg_GuildRoomEnterResult(v29, 2, v14, v15, v16, &pfStartPos, v20);
      }
    }
    else
    {
      v13 = 1;
      CPlayer::SendMsg_GuildRoomEnterResult(v29, 1, v14, v15, v16, &pfStartPos, v20);
    }
  }
  else
  {
    v13 = 1;
    CPlayer::SendMsg_GuildRoomEnterResult(v29, 1, v14, v15, v16, &pfStartPos, v20);
  }
}
