/*
 * Function: ?init@TiXmlString@@AEAAX_K@Z
 * Address: 0x14052E090
 */

void __fastcall TiXmlString::init(TiXmlString *this, unsigned __int64 a2)
{
  unsigned __int64 v2; // rdi@1
  TiXmlString *v3; // rbx@1
  TiXmlString::Rep *v4; // rax@2

  v2 = a2;
  v3 = this;
  if ( a2 )
  {
    v4 = (TiXmlString::Rep *)operator new(saturated_mul(4ui64, (a2 + 27) >> 2));
    v3->rep_ = v4;
    v4->size = v2;
    v3->rep_->str[v2] = 0;
    v3->rep_->capacity = v2;
  }
  else
  {
    this->rep_ = (TiXmlString::Rep *)&TiXmlString::nullrep_;
  }
}
