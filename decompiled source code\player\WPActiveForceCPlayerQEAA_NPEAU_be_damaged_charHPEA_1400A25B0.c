/*
 * Function: ?WPActiveForce@CPlayer@@QEAA_NPEAU_be_damaged_char@@HPEAU_force_fld@@@Z
 * Address: 0x1400A25B0
 */

bool __fastcall CPlayer::WPActiveForce(CPlayer *this, _be_damaged_char *pDamList, int nDamagedObjNum, _force_fld *pForceFld)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v7; // rax@31
  CGuildRoomSystem *v8; // rax@48
  __int64 v9; // [sp+0h] [bp-C8h]@1
  CCharacter *pDst; // [sp+30h] [bp-98h]@4
  int nForceLv; // [sp+38h] [bp-90h]@12
  bool v12; // [sp+3Ch] [bp-8Ch]@12
  bool v13; // [sp+3Dh] [bp-8Bh]@12
  int j; // [sp+40h] [bp-88h]@15
  char v15; // [sp+54h] [bp-74h]@50
  bool v16; // [sp+74h] [bp-54h]@50
  CUserDB *v17; // [sp+88h] [bp-40h]@31
  int n; // [sp+90h] [bp-38h]@31
  CGuild *v19; // [sp+98h] [bp-30h]@31
  CUserDB *v20; // [sp+A0h] [bp-28h]@48
  int v21; // [sp+A8h] [bp-20h]@48
  CGuild *v22; // [sp+B0h] [bp-18h]@48
  CPlayer *v23; // [sp+D0h] [bp+8h]@1
  _be_damaged_char *v24; // [sp+D8h] [bp+10h]@1
  int v25; // [sp+E0h] [bp+18h]@1
  _force_fld *pForceFlda; // [sp+E8h] [bp+20h]@1

  pForceFlda = pForceFld;
  v25 = nDamagedObjNum;
  v24 = pDamList;
  v23 = this;
  v4 = &v9;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  pDst = pDamList->m_pChar;
  if ( !pDst || !pForceFld )
    return 0;
  if ( pForceFld->m_nTempEffectType >= 150 )
    return 0;
  if ( pForceFld->m_nTempEffectType == -1 && pForceFld->m_nContEffectType == -1 )
    return 0;
  nForceLv = v23->m_pmWpn.nActiveEffLvl;
  v12 = IsUsableTempEffectAtStoneState(pForceFld->m_nTempEffectType);
  v13 = 0;
  if ( nForceLv > 7 )
    nForceLv = 7;
  if ( v25 <= 1 )
  {
    if ( !CCharacter::IsEffectableDst((CCharacter *)&v23->vfptr, pForceFlda->m_strActableDst, pDst) )
      return 0;
    if ( pForceFlda->m_nContEffectType != -1
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst) )
    {
      return 0;
    }
    if ( !pForceFlda->m_nContEffectType
      && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v23->vfptr->IsAttackableInTown)(v23)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst) )
    {
      if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))v23->vfptr->IsInTown)(v23) )
        return 0;
      if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst) )
        return 0;
      if ( v23->m_Param.m_pGuild )
      {
        v20 = v23->m_pUserDB;
        v21 = v23->m_ObjID.m_wIndex;
        v22 = v23->m_Param.m_pGuild;
        v8 = CGuildRoomSystem::GetInstance();
        if ( CGuildRoomSystem::IsGuildRoomMemberIn(v8, v22->m_dwSerial, v21, v20->m_dwSerial) )
          return 0;
      }
    }
    v13 = CCharacter::AssistForce((CCharacter *)&v23->vfptr, pDst, pForceFlda, nForceLv, &v15, &v16);
    if ( !v15 )
      v24->m_bActiveSucc = 1;
  }
  else
  {
    for ( j = 0; j < v25; ++j )
    {
      if ( CCharacter::IsEffectableDst((CCharacter *)&v23->vfptr, pForceFlda->m_strActableDst, v24[j].m_pChar)
        && (pForceFlda->m_nContEffectType == -1
         || (unsigned __int8)((int (__fastcall *)(CCharacter *))v24[j].m_pChar->vfptr->IsRecvableContEffect)(v24[j].m_pChar))
        && (!_effect_parameter::GetEff_State(&v24[j].m_pChar->m_EP, 20) || pForceFlda->m_nTempEffectType != -1 && v12)
        && !_effect_parameter::GetEff_State(&v24[j].m_pChar->m_EP, 28) )
      {
        if ( pForceFlda->m_nContEffectType
          || (unsigned __int8)((int (__fastcall *)(CPlayer *))v23->vfptr->IsAttackableInTown)(v23)
          || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst)
          || !(unsigned __int8)((int (__fastcall *)(CPlayer *))v23->vfptr->IsInTown)(v23)
          && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst)
          && (!v23->m_Param.m_pGuild
           || (v17 = v23->m_pUserDB,
               n = v23->m_ObjID.m_wIndex,
               v19 = v23->m_Param.m_pGuild,
               v7 = CGuildRoomSystem::GetInstance(),
               !CGuildRoomSystem::IsGuildRoomMemberIn(v7, v19->m_dwSerial, n, v17->m_dwSerial))) )
        {
          if ( CCharacter::AssistForceToOne((CCharacter *)&v23->vfptr, v24[j].m_pChar, pForceFlda, nForceLv) )
          {
            v24[j].m_bActiveSucc = 1;
            v13 = 1;
          }
        }
      }
    }
  }
  return v13;
}
