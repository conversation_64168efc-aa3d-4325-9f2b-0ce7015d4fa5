/*
 * Function: _CUnmannedTraderUserInfo::CUnmannedTraderUserInfo_::_1_::dtor$2
 * Address: 0x140353130
 */

void __fastcall CUnmannedTraderUserInfo::CUnmannedTraderUserInfo_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(*(_QWORD *)(a2 + 64) + 64i64));
}
