/*
 * Function: j_?end@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@2@XZ
 * Address: 0x140007DA6
 */

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::end(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::end(this, result);
}
