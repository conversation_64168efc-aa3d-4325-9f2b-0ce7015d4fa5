/*
 * Function: ?pc_RequestUILockCertify@CPlayer@@QEAAXPEAVCUserDB@@PEAD@Z
 * Address: 0x140100E20
 */

void __fastcall CPlayer::pc_RequestUILockCertify(CPlayer *this, CUserDB *pUserDB, char *uszUILockPW)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+30h] [bp-18h]@5
  CPlayer *v7; // [sp+50h] [bp+8h]@1
  CUserDB *v8; // [sp+58h] [bp+10h]@1
  const char *Str; // [sp+60h] [bp+18h]@1

  Str = uszUILockPW;
  v8 = pUserDB;
  v7 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !pUserDB->m_byUserDgr )
  {
    v6 = 0;
    if ( uszUILockPW && strlen_0(uszUILockPW) )
    {
      if ( v8->m_byUILock )
      {
        if ( v8->m_byUILock == 2 )
        {
          v6 = 2;
        }
        else if ( strcmp_0(v8->m_szUILock_PW, Str) )
        {
          if ( ++v8->m_byUILock_FailCnt < 5 )
            v6 = 1;
          else
            v6 = 4;
        }
      }
      else
      {
        v6 = 3;
      }
    }
    else
    {
      v6 = 5;
    }
    if ( v6 )
    {
      if ( v6 == 4 )
      {
        CPlayer::SendMsg_UILock_Login_Result(v7, 4, v8->m_byUILock_FailCnt);
        CUserDB::ForceCloseCommand(v8, 8, 0, 1, "UILOCK Certify Fail");
      }
      else
      {
        CPlayer::SendMsg_UILock_Login_Result(v7, v6, v8->m_byUILock_FailCnt);
      }
    }
    else
    {
      v8->m_byUILock = 2;
      v8->m_byUILock_FailCnt = 0;
      v8->m_byUILockFindPassFailCount = 0;
      CPlayer::SendMsg_UILock_Login_Result(v7, v6, v8->m_byUILock_FailCnt);
    }
  }
}
