/*
 * Function: ?GetWorldFromLocal@CExtDummy@@QEAAHPEAY02MKQEAM@Z
 * Address: 0x1404DF130
 */

signed __int64 __fastcall CExtDummy::GetWorldFromLocal(CExtDummy *this, float (*a2)[3], unsigned int a3, float *const a4)
{
  _EXT_DUMMY *v4; // r10@1
  unsigned int v5; // eax@2
  signed __int64 result; // rax@4

  v4 = this->mDummy;
  if ( v4 && (v5 = this->mNum) != 0 && a3 < v5 )
  {
    Vector3fTransform((float *const )a2, a4, v4[a3].mMat);
    result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
