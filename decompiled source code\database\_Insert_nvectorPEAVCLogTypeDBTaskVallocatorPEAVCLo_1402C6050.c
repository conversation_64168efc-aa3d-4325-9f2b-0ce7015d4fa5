/*
 * Function: ?_Insert_n@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@2@_KAEBQEAVCLogTypeDBTask@@@Z
 * Address: 0x1402C6050
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Insert_n(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Where, unsigned __int64 _Count, CLogTypeDBTask *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-98h]@1
  CLogTypeDBTask *_Vala; // [sp+28h] [bp-70h]@4
  unsigned __int64 _Counta; // [sp+38h] [bp-60h]@4
  CLogTypeDBTask **_Ptr; // [sp+40h] [bp-58h]@13
  CLogTypeDBTask **v14; // [sp+48h] [bp-50h]@13
  CLogTypeDBTask **_Last; // [sp+50h] [bp-48h]@18
  __int64 v16; // [sp+58h] [bp-40h]@4
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v17; // [sp+60h] [bp-38h]@5
  unsigned __int64 v18; // [sp+68h] [bp-30h]@8
  unsigned __int64 v19; // [sp+70h] [bp-28h]@9
  CLogTypeDBTask **v20; // [sp+78h] [bp-20h]@13
  CLogTypeDBTask **v21; // [sp+80h] [bp-18h]@13
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v22; // [sp+A0h] [bp+8h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v23; // [sp+A8h] [bp+10h]@1
  unsigned __int64 v24; // [sp+B0h] [bp+18h]@1
  unsigned __int64 v25; // [sp+B0h] [bp+18h]@13

  v24 = _Count;
  v23 = _Where;
  v22 = this;
  v4 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  _Vala = *_Val;
  _Counta = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::capacity(v22);
  if ( v24 )
  {
    v17 = (std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v22);
    v6 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::max_size(v22);
    if ( v6 - (unsigned __int64)v17 < v24 )
      std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Xlen(v17);
    v7 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v22);
    if ( _Counta >= v24 + v7 )
    {
      if ( v22->_Mylast - v23->_Myptr >= v24 )
      {
        _Last = v22->_Mylast;
        v22->_Mylast = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(
                         v22,
                         &_Last[-v24],
                         _Last,
                         v22->_Mylast);
        stdext::_Unchecked_move_backward<CLogTypeDBTask * *,CLogTypeDBTask * *>(v23->_Myptr, &_Last[-v24], _Last);
        std::fill<CLogTypeDBTask * *,CLogTypeDBTask *>(v23->_Myptr, &v23->_Myptr[v24], &_Vala);
      }
      else
      {
        std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(
          v22,
          v23->_Myptr,
          v22->_Mylast,
          &v23->_Myptr[v24]);
        std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(
          v22,
          v22->_Mylast,
          v24 - (v22->_Mylast - v23->_Myptr),
          &_Vala);
        v22->_Mylast += v24;
        std::fill<CLogTypeDBTask * *,CLogTypeDBTask *>(v23->_Myptr, &v22->_Mylast[-v24], &_Vala);
      }
    }
    else
    {
      v18 = _Counta / 2;
      v8 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::max_size(v22);
      if ( v8 - v18 >= _Counta )
        v19 = _Counta / 2 + _Counta;
      else
        v19 = 0i64;
      _Counta = v19;
      v9 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v22);
      if ( _Counta < v24 + v9 )
        _Counta = v24 + std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v22);
      _Ptr = std::allocator<CLogTypeDBTask *>::allocate(&v22->_Alval, _Counta);
      v14 = _Ptr;
      v20 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(
              v22,
              v22->_Myfirst,
              v23->_Myptr,
              _Ptr);
      v14 = v20;
      v21 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Ufill(v22, v20, v24, &_Vala);
      v14 = v21;
      std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(
        v22,
        v23->_Myptr,
        v22->_Mylast,
        v21);
      v25 = std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::size(v22) + v24;
      if ( v22->_Myfirst )
      {
        std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Destroy(v22, v22->_Myfirst, v22->_Mylast);
        std::allocator<CLogTypeDBTask *>::deallocate(&v22->_Alval, v22->_Myfirst, v22->_Myend - v22->_Myfirst);
      }
      v22->_Myend = &_Ptr[_Counta];
      v22->_Mylast = &_Ptr[v25];
      v22->_Myfirst = _Ptr;
    }
  }
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(v23);
}
