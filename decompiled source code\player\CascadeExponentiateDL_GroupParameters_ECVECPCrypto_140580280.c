/*
 * Function: ?CascadeExponentiate@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@QEBA?AUECPPoint@2@AEBU32@AEBVInteger@2@01@Z
 * Address: 0x140580280
 */

struct CryptoPP::ECPPoint *__fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::CascadeExponentiate(struct CryptoPP::ECPPoint *a1, struct CryptoPP::Integer *a2, struct CryptoPP::ECPPoint *a3, struct CryptoPP::Integer *a4, struct CryptoPP::ECPPoint *a5, struct CryptoPP::Integer *a6)
{
  CryptoPP::ECP *v6; // rax@1
  struct CryptoPP::ECPPoint *v8; // [sp+58h] [bp+10h]@1
  struct CryptoPP::ECPPoint *v9; // [sp+60h] [bp+18h]@1
  struct CryptoPP::Integer *v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = (struct CryptoPP::ECPPoint *)a2;
  v6 = CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetCurve((CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *)a1);
  CryptoPP::ECP::CascadeMultiply(v6, v8, v10, v9, a6, a5);
  return v8;
}
