/*
 * Function: ??_EPK_DefaultDecryptionFilter@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x1405F75F0
 */

CryptoPP::PK_DefaultDecryptionFilter *__fastcall CryptoPP::PK_DefaultDecryptionFilter::`vector deleting destructor'(CryptoPP::PK_DefaultDecryptionFilter *a1, int a2)
{
  CryptoPP::PK_DefaultDecryptionFilter *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::PK_DefaultDecryptionFilter::~PK_DefaultDecryptionFilter(a1);
  if ( v4 & 1 )
    operator delete((void *)v3);
  return v3;
}
