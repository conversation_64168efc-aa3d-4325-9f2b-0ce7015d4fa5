/*
 * Function: ??0?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x1403A2120
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CMoveMapLimitRightInfo> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CMoveMapLimitRightInfo> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CMoveMapLimitRightInfo> *)&v5;
  std::allocator<CMoveMapLimitRightInfo>::allocator<CMoveMapLimitRightInfo>((std::allocator<CMoveMapLimitRightInfo> *)&v5);
  std::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    (std::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Buy(v7, 0i64);
}
