/*
 * Function: ??1?$DL_ObjectImplBase@V?$DL_DecryptorBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_CryptoSchemeOptions@U?$DLIES@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@CryptoPP@@$00@CryptoPP@@UDL_CryptoKeys_GFP@2@V?$DL_KeyAgreementAlgorithm_DH@VInteger@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@2@V?$DL_KeyDerivationAlgorithm_P1363@VInteger@CryptoPP@@$00V?$P1363_KDF2@VSHA1@CryptoPP@@@2@@2@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@2@@2@V?$DL_PrivateK<PERSON>_<PERSON><PERSON>@VDL_GroupParameters_GFP_DefaultSafePrime@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140632980
 */

int __fastcall CryptoPP::DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::Integer>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>,CryptoPP::DL_CryptoKeys_GFP,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::Integer,1,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>,CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>>::~DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::Integer>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>,CryptoPP::DL_CryptoKeys_GFP,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::Integer,1,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>,CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>>(__int64 a1)
{
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>::`vbase destructor(a1 + 24);
  return CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>::~AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::Integer>,CryptoPP::DLIES<CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,1>>(v2);
}
