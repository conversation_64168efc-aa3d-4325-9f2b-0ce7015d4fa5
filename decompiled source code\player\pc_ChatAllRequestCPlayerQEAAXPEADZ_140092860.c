/*
 * Function: ?pc_ChatAllRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140092860
 */

void __fastcall CPlayer::pc_ChatAllRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@26
  CChatStealSystem *v5; // rax@26
  int v6; // eax@30
  __int64 v7; // [sp+0h] [bp-1C8h]@1
  bool bUpdate[2]; // [sp+20h] [bp-1A8h]@18
  bool bSend[8]; // [sp+28h] [bp-1A0h]@18
  _STORAGE_LIST::_db_con *pItem; // [sp+30h] [bp-198h]@11
  unsigned int dwDur; // [sp+38h] [bp-190h]@18
  _announ_message_receipt_udp Dst; // [sp+50h] [bp-178h]@26
  char pbyType; // [sp+184h] [bp-44h]@26
  char v14; // [sp+185h] [bp-43h]@26
  int v15; // [sp+194h] [bp-34h]@26
  int j; // [sp+198h] [bp-30h]@26
  CPlayer *v17; // [sp+1A0h] [bp-28h]@29
  int v18; // [sp+1B0h] [bp-18h]@30
  unsigned __int64 v19; // [sp+1B8h] [bp-10h]@4
  CPlayer *pPlayer; // [sp+1D0h] [bp+8h]@1
  const char *Str; // [sp+1D8h] [bp+10h]@1

  Str = pwszChatData;
  pPlayer = this;
  v2 = &v7;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pPlayer->m_pUserDB )
  {
    if ( !pPlayer->m_pUserDB->m_bChatLock
      && !CPlayer::IsPunished(pPlayer, 0, 1)
      && CGameObject::GetCurSecNum((CGameObject *)&pPlayer->vfptr) != -1
      && !pPlayer->m_bMapLoading )
    {
      pItem = 0i64;
      if ( !unk_1799C6084
        || (pItem = _STORAGE_LIST::GetPtrFromItemCode(
                      (_STORAGE_LIST *)&pPlayer->m_Param.m_dbInven.m_nListNum,
                      pwszItemCode)) != 0i64 )
      {
        if ( !unk_1799C6085 || dwSub <= CPlayer::GetMoney(pPlayer, 0) )
        {
          if ( unk_1799C6084 && pItem )
          {
            if ( IsOverLapItem(pItem->m_byTableCode) )
            {
              bSend[0] = 0;
              bUpdate[0] = 0;
              dwDur = CPlayer::Emb_AlterDurPoint(pPlayer, 0, pItem->m_byStorageIndex, -1, 0, 0);
              if ( dwDur )
                CPlayer::SendMsg_AdjustAmountInform(pPlayer, 0, pItem->m_wSerial, dwDur);
              else
                CMgrAvatorItemHistory::consume_del_item(
                  &CPlayer::s_MgrItemHistory,
                  pPlayer->m_ObjID.m_wIndex,
                  pItem,
                  pPlayer->m_szItemHistoryFileName);
            }
            else
            {
              *(_QWORD *)bSend = "CPlayer::pcChatAllRequest()";
              bUpdate[0] = 1;
              if ( !CPlayer::Emb_DelStorage(pPlayer, 0, pItem->m_byStorageIndex, 0, 1, "CPlayer::pcChatAllRequest()") )
                return;
              CMgrAvatorItemHistory::consume_del_item(
                &CPlayer::s_MgrItemHistory,
                pPlayer->m_ObjID.m_wIndex,
                pItem,
                pPlayer->m_szItemHistoryFileName);
            }
          }
          if ( unk_1799C6085 )
          {
            CPlayer::SubDalant(pPlayer, dwSub);
            CPlayer::SendMsg_AlterMoneyInform(pPlayer, 0);
          }
          _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
          Dst.byMessageType = 14;
          Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
          Dst.bySenderRace = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
          v4 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
          strcpy_0(Dst.wszSenderName, v4);
          Dst.bySize = strlen_0(Str);
          memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
          Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
          Dst.byPvpGrade = pPlayer->m_Param.m_byPvPGrade;
          pbyType = 2;
          v14 = 11;
          v5 = CChatStealSystem::Instance();
          CChatStealSystem::StealChatMsg(v5, pPlayer, Dst.byMessageType, (char *)Str);
          v15 = _announ_message_receipt_udp::size(&Dst);
          for ( j = 0; j < 2532; ++j )
          {
            v17 = &g_Player + j;
            if ( v17->m_bLive )
            {
              v18 = CPlayerDB::GetRaceCode(&v17->m_Param);
              v6 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
              if ( v18 == v6 )
                CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v15);
            }
          }
        }
      }
    }
  }
}
