/*
 * Function: ?Load@CNormalGuildBattleManager@GUILD_BATTLE@@QEAA_NHIHHHH@Z
 * Address: 0x1403D38F0
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Load(GUILD_BATTLE::CNormalGuildBattleManager *this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  GUILD_BATTLE::CNormalGuildBattleFieldList *v10; // rax@6
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@8
  GUILD_BATTLE::CGuildBattleLogger *v12; // rax@11
  GUILD_BATTLE::CGuildBattleLogger *v13; // rax@13
  GUILD_BATTLE::CGuildBattleLogger *v14; // rax@17
  GUILD_BATTLE::CGuildBattleLogger *v15; // rax@21
  GUILD_BATTLE::CGuildBattleScheduleManager *v16; // rax@22
  GUILD_BATTLE::CGuildBattleLogger *v17; // rax@23
  __int64 v18; // [sp+0h] [bp-68h]@1
  int v19; // [sp+20h] [bp-48h]@8
  int v20; // [sp+28h] [bp-40h]@8
  int v21; // [sp+30h] [bp-38h]@8
  int v22; // [sp+38h] [bp-30h]@8
  int v23; // [sp+40h] [bp-28h]@11
  unsigned int v24; // [sp+50h] [bp-18h]@6
  int v25; // [sp+54h] [bp-14h]@6
  GUILD_BATTLE::CNormalGuildBattleManager *v26; // [sp+70h] [bp+8h]@1
  int v27; // [sp+78h] [bp+10h]@1
  unsigned int v28; // [sp+80h] [bp+18h]@1
  int v29; // [sp+88h] [bp+20h]@1

  v29 = iToday;
  v28 = uiOldMapCnt;
  v27 = iCurDay;
  v26 = this;
  v7 = &v18;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( !v26->m_ppkNormalBattle )
    return 0;
  v26->m_bLoad = 1;
  v10 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  v24 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v10);
  v25 = CRFWorldDatabase::SelectRowCountGuildBattleInfo(pkDB);
  if ( v24 != v28 || v26->m_uiMaxBattleCnt != v25 )
  {
    v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    v22 = iTomorrowDayID;
    v21 = iTomorrow;
    v20 = iTodayDayID;
    v19 = v29;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v11,
      "CNormalGuildBattleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTomorro"
      "wDayID(%d) : AddDefaultDBRecord()",
      (unsigned int)v27,
      v28);
    return GUILD_BATTLE::CNormalGuildBattleManager::AddDefaultDBRecord(v26);
  }
  if ( v29 == v27 )
  {
    v26->m_ppkTodayBattle = &v26->m_ppkNormalBattle[iTodayDayID * 23 * v26->m_uiMapCnt];
    v26->m_ppkTomorrowBattle = &v26->m_ppkNormalBattle[iTomorrowDayID * 23 * v26->m_uiMapCnt];
    if ( !GUILD_BATTLE::CNormalGuildBattleManager::Load(v26, 1, iTodayDayID, v26->m_ppkTodayBattle) )
    {
      v12 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v23 = iTodayDayID;
      v22 = iTomorrowDayID;
      v21 = iTomorrow;
      v20 = iTodayDayID;
      v19 = v29;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v12,
        "CNormalGuildBattleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTomor"
        "rowDayID(%d) : ( iToday == iCurDay ) : Load( true, %d, m_ppkTodayBattle ) Fail!",
        (unsigned int)v27,
        v28);
      return 0;
    }
    if ( !GUILD_BATTLE::CNormalGuildBattleManager::Load(v26, 0, iTomorrowDayID, v26->m_ppkTomorrowBattle) )
    {
      v13 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v23 = iTomorrowDayID;
      v22 = iTomorrowDayID;
      v21 = iTomorrow;
      v20 = iTodayDayID;
      v19 = v29;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v13,
        "CNormalGuildBattleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTomor"
        "rowDayID(%d) : ( iToday == iCurDay ) : Load( false, %d, m_ppkTomorrowBattle ) Fail!",
        (unsigned int)v27,
        v28);
      return 0;
    }
  }
  else if ( iTomorrow == v27 )
  {
    v26->m_ppkTodayBattle = &v26->m_ppkNormalBattle[iTomorrowDayID * 23 * v26->m_uiMapCnt];
    v26->m_ppkTomorrowBattle = &v26->m_ppkNormalBattle[iTodayDayID * 23 * v26->m_uiMapCnt];
    GUILD_BATTLE::CNormalGuildBattleManager::Clear(v26, v26->m_ppkTomorrowBattle);
    if ( !GUILD_BATTLE::CNormalGuildBattleManager::Load(v26, 1, iTomorrowDayID, v26->m_ppkTodayBattle) )
    {
      v14 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v23 = iTomorrowDayID;
      v22 = iTomorrowDayID;
      v21 = iTomorrow;
      v20 = iTodayDayID;
      v19 = v29;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v14,
        "CNormalGuildBattleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTomor"
        "rowDayID(%d) : ( iTomorrow == iCurDay ) : Load( true, %d, m_ppkTodayBattle ) Fail!",
        (unsigned int)v27,
        v28);
      return 0;
    }
  }
  else
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleManager::AddDefaultDBRecord(v26) )
      return 0;
    v15 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    v21 = iTomorrow;
    v20 = iTodayDayID;
    v19 = v29;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v15,
      "CNormalGuildBattleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) AddDefaultDBRecord()",
      (unsigned int)v27,
      v28);
  }
  v16 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
  if ( GUILD_BATTLE::CGuildBattleScheduleManager::CleanUpDanglingReservedSchedule(v16) )
  {
    result = 1;
  }
  else
  {
    v17 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    v21 = iTomorrow;
    v20 = iTodayDayID;
    v19 = v29;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v17,
      "CNormalGuildBattleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) CGuildBa"
      "ttleScheduleManager::Instance()->CleanUpDanglingReservedSchedule() Fail!",
      (unsigned int)v27,
      v28);
    result = 0;
  }
  return result;
}
