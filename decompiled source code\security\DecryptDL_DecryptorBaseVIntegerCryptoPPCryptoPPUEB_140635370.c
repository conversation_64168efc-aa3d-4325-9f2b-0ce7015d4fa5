/*
 * Function: ?Decrypt@?$DL_DecryptorBase@VInteger@CryptoPP@@@CryptoPP@@UEBA?AUDecodingResult@2@AEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameV<PERSON>ue<PERSON>airs@2@@Z
 * Address: 0x140635370
 */

__int64 __fastcall CryptoPP::DL_DecryptorBase<CryptoPP::Integer>::Decrypt(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5)
{
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  __int64 v9; // rax@1
  __int64 v10; // r9@1
  __int64 v11; // rax@1
  __int64 v12; // rdx@1
  int v13; // eax@1
  __int64 v14; // rax@1
  __int64 v15; // rax@1
  __int64 v16; // rax@1
  unsigned __int64 v17; // rax@1
  __int64 v18; // rax@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v20; // [sp+48h] [bp-120h]@1
  __int64 v21; // [sp+60h] [bp-108h]@1
  __int64 v22; // [sp+68h] [bp-100h]@1
  __int64 v23; // [sp+70h] [bp-F8h]@1
  __int64 v24; // [sp+78h] [bp-F0h]@1
  CryptoPP::Integer v25; // [sp+80h] [bp-E8h]@1
  CryptoPP::Integer v26; // [sp+A8h] [bp-C0h]@1
  __int64 v27; // [sp+D0h] [bp-98h]@1
  __int64 v28; // [sp+D8h] [bp-90h]@1
  __int64 v29; // [sp+E0h] [bp-88h]@1
  __int64 v30; // [sp+E8h] [bp-80h]@1
  __int64 v31; // [sp+F0h] [bp-78h]@1
  __int64 v32; // [sp+F8h] [bp-70h]@1
  __int64 v33; // [sp+100h] [bp-68h]@1
  __int64 v34; // [sp+108h] [bp-60h]@1
  __int64 v35; // [sp+110h] [bp-58h]@1
  int v36; // [sp+118h] [bp-50h]@1
  __int64 v37; // [sp+120h] [bp-48h]@1
  __int64 v38; // [sp+128h] [bp-40h]@1
  __int64 v39; // [sp+130h] [bp-38h]@1
  unsigned __int64 size; // [sp+138h] [bp-30h]@1
  __int64 v41; // [sp+140h] [bp-28h]@1
  unsigned __int64 v42; // [sp+148h] [bp-20h]@1
  char *v43; // [sp+150h] [bp-18h]@1
  char *v44; // [sp+158h] [bp-10h]@1
  __int64 v45; // [sp+170h] [bp+8h]@1
  __int64 v46; // [sp+178h] [bp+10h]@1
  __int64 v47; // [sp+188h] [bp+20h]@1
  __int64 v48; // [sp+188h] [bp+20h]@1
  __int64 v49; // [sp+190h] [bp+28h]@1

  v47 = a4;
  v46 = a2;
  v45 = a1;
  v28 = -2i64;
  LODWORD(v5) = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  v29 = v5;
  v23 = v5;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v45 + 72i64))(v45);
  v30 = v6;
  v21 = v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v45 + 80i64))(v45);
  v31 = v7;
  v27 = v7;
  LODWORD(v8) = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::GetAbstractGroupParameters(v45 + 16);
  v32 = v8;
  v22 = v8;
  v33 = *(_QWORD *)(v45 + 16);
  LODWORD(v9) = (*(int (__fastcall **)(signed __int64))(v33 + 8))(v45 + 16);
  v34 = v9;
  v24 = v9;
  LOBYTE(v10) = 1;
  LODWORD(v11) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, __int64, __int64))(*(_QWORD *)v22 + 112i64))(
                   v22,
                   &v25,
                   v47,
                   v10);
  v35 = v11;
  LOBYTE(v12) = 1;
  v13 = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v22 + 96i64))(v22, v12);
  v36 = v13;
  v48 = (unsigned int)v13 + v47;
  v49 = a5 - (unsigned int)v13;
  LODWORD(v14) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v24 + 16i64))(v24);
  v37 = v14;
  LODWORD(v15) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, __int64, CryptoPP::Integer *))(*(_QWORD *)v23 + 8i64))(
                   v23,
                   &v26,
                   v22,
                   &v25);
  v38 = v15;
  LODWORD(v16) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v27 + 24i64))(v27, v49);
  v39 = v16;
  LODWORD(v17) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v27 + 8i64))(v27, v16);
  size = v17;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v20,
    v17);
  v41 = v18;
  v42 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v20);
  v43 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v20);
  (*(void (__fastcall **)(__int64, __int64, char *, unsigned __int64))(*(_QWORD *)v21 + 8i64))(v21, v22, v43, v42);
  v44 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v20);
  (*(void (__fastcall **)(__int64, __int64, char *, __int64))(*(_QWORD *)v27 + 40i64))(v27, v46, v44, v48);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v20);
  CryptoPP::Integer::~Integer(&v26);
  CryptoPP::Integer::~Integer(&v25);
  return v46;
}
