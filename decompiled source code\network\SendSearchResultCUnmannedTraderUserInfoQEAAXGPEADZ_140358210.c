/*
 * Function: ?SendSearchResult@CUnmannedTraderUserInfo@@QEAAXGPEAD@Z
 * Address: 0x140358210
 */

void __fastcall CUnmannedTraderUserInfo::SendSearchResult(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-2D8h]@1
  char *v6; // [sp+30h] [bp-2A8h]@4
  char szMsg; // [sp+50h] [bp-288h]@4
  int v8; // [sp+51h] [bp-287h]@4
  char v9; // [sp+55h] [bp-283h]@4
  char v10; // [sp+56h] [bp-282h]@4
  char v11; // [sp+57h] [bp-281h]@4
  char v12; // [sp+58h] [bp-280h]@4
  int v13; // [sp+59h] [bp-27Fh]@4
  char v14; // [sp+5Dh] [bp-27Bh]@4
  int v15; // [sp+5Eh] [bp-27Ah]@4
  unsigned __int8 v16; // [sp+62h] [bp-276h]@4
  int v17; // [sp+63h] [bp-275h]@6
  char v18; // [sp+67h] [bp-271h]@6
  __int16 v19; // [sp+68h] [bp-270h]@6
  __int64 v20; // [sp+6Ah] [bp-26Eh]@6
  int v21; // [sp+72h] [bp-266h]@6
  int v22; // [sp+76h] [bp-262h]@6
  int v23; // [sp+7Ah] [bp-25Eh]@6
  char Dst[17]; // [sp+7Eh] [bp-25Ah]@9
  int v25[118]; // [sp+8Fh] [bp-249h]@9
  __int64 _Time; // [sp+268h] [bp-70h]@4
  __int64 v27; // [sp+278h] [bp-60h]@9
  _INVENKEY v28; // [sp+284h] [bp-54h]@4
  unsigned int j; // [sp+294h] [bp-44h]@4
  char pbyType; // [sp+2A4h] [bp-34h]@10
  char v31; // [sp+2A5h] [bp-33h]@10
  char *Src; // [sp+2C0h] [bp-18h]@7
  unsigned __int64 v33; // [sp+2C8h] [bp-10h]@4
  unsigned __int16 v34; // [sp+2E8h] [bp+10h]@1

  v34 = wInx;
  v3 = &v5;
  for ( i = 180i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v33 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = pLoadData;
  szMsg = 0;
  v8 = *((_DWORD *)pLoadData + 3);
  v9 = pLoadData[16];
  v10 = pLoadData[17];
  v11 = pLoadData[18];
  v12 = pLoadData[19];
  v13 = *((_DWORD *)pLoadData + 5);
  v14 = pLoadData[24];
  v15 = *((_DWORD *)pLoadData + 39);
  v16 = pLoadData[160];
  time_18(&_Time);
  _INVENKEY::_INVENKEY(&v28);
  for ( j = 0; j < v16; ++j )
  {
    *(&v17 + 12 * j) = *(_DWORD *)&v6[72 * j + 168];
    _INVENKEY::LoadDBKey(&v28, *(_DWORD *)&v6[72 * j + 172]);
    *(&v18 + 48 * j) = v28.byTableCode;
    *(&v19 + 24 * j) = v28.wItemIndex;
    *(&v20 + 6 * j) = *(_QWORD *)&v6[72 * j + 176];
    *(&v21 + 12 * j) = *(_DWORD *)&v6[72 * j + 184];
    *(&v22 + 12 * j) = *(_DWORD *)&v6[72 * j + 188];
    *(&v23 + 12 * j) = *(_DWORD *)&v6[72 * j + 192];
    if ( v6[72 * j + 196] )
      Src = &v6[72 * j + 196];
    else
      Src = "NULL";
    strcpy_s(&Dst[48 * j], 0x11ui64, Src);
    v27 = *(_QWORD *)&v6[72 * j + 216] + 3600 * (unsigned __int8)v6[72 * j + 224];
    v25[12 * (unsigned __int64)j] = v27 - _Time;
  }
  pbyType = 30;
  v31 = 33;
  CNetProcess::LoadSendMsg(unk_1414F2088, v34, &pbyType, &szMsg, 0x1F3u);
}
