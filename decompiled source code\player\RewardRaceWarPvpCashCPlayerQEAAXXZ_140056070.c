/*
 * Function: ?RewardRaceWarPvpCash@CPlayer@@QEAAXXZ
 * Address: 0x140056070
 */

void __usercall CPlayer::RewardRaceWarPvpCash(CPlayer *this@<rcx>, double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  double v5; // [sp+20h] [bp-18h]@4
  double v6; // [sp+28h] [bp-10h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v7->m_id.wIndex);
  v5 = a2;
  v6 = (double)(5 * ((int (__fastcall *)(CPlayer *))v7->vfptr->GetLevel)(v7));
  if ( v6 > 0.0 )
    v6 = v6 * v5;
  CPlayer::AlterPvPCashBag(v7, v6, pm_scaner);
  CPvpCashPoint::SetRaceWarRecvr(&v7->m_kPvpCashPoint, 1);
  CPvpOrderView::Update_RaceWarRecvr(&v7->m_kPvpOrderView, 1);
}
