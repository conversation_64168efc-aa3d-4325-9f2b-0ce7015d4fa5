/*
 * Function: ?Expire_IPOverflow@CBillingManager@@QEAAXPEAD@Z
 * Address: 0x1401C4250
 */

void __fastcall CBillingManager::Expire_IPOverflow(CBillingManager *this, char *szID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CBillingManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CBilling::Expire_IPOverflow(v5->m_pBill, szID);
}
