/*
 * CAttackForce.cpp - Modern C++ Attack Force System Implementation
 * Refactored from AttackForceCAttackQEAAXPEAU_attack_param_NZ_14016A210.c
 * Original Address: 0x14016A210
 */

#include "../Headers/CAttackForce.h"
#include "../Headers/CAttackForceConstants.h"
#include "../Headers/CAttackForceIntegration.h"
#include "../Headers/CAttackForceErrorHandler.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CMonsterAttack.h"

#include <cmath>
#include <random>
#include <algorithm>
#include <stdexcept>

using namespace NexusProtection::Combat::AttackForceConstants;

namespace NexusProtection {
namespace Combat {

/**
 * Constructor
 */
CAttackForce::CAttackForce(CCharacter* pAttacker)
    : m_pA<PERSON>cker(pAttacker) {

    if (!pAttacker) {
        throw std::invalid_argument("CAttackForce: Attacker cannot be null");
    }

    // Initialize error handler
    m_pErrorHandler = std::make_unique<CAttackForceErrorHandler>();

    // Validate attacker during construction
    ValidationResult validation = m_pErrorHandler->ValidateAttacker(pAttacker);
    if (!validation.bValid) {
        Logger::Warning("CAttackForce::CAttackForce - Attacker validation warnings: %s", validation.GetSummary().c_str());
    }

    Logger::Debug("CAttackForce::CAttackForce - Attack force system initialized for character %p", pAttacker);
}

/**
 * Destructor
 */
CAttackForce::~CAttackForce() {
    try {
        Reset();
        Logger::Debug("CAttackForce::~CAttackForce - Attack force system destroyed");
    } catch (const std::exception& e) {
        Logger::Error("CAttackForce::~CAttackForce - Exception during destruction: %s", e.what());
    }
}

/**
 * Execute force attack - Main entry point
 */
AttackForceResult CAttackForce::ExecuteAttack(_attack_param* pParam, bool bUseEffBullet, float fAccuracyBonus) {
    AttackForceResult result;

    try {
        // Comprehensive validation using error handler
        if (m_pErrorHandler) {
            // Validate attack parameters
            ATTACK_FORCE_VALIDATE_PARAM(*m_pErrorHandler, pParam, result);

            // Validate attacker
            ATTACK_FORCE_VALIDATE_ATTACKER(*m_pErrorHandler, m_pAttacker, result);

            // Validate target if present
            if (pParam->pDst) {
                ATTACK_FORCE_VALIDATE_TARGET(*m_pErrorHandler, pParam->pDst, m_pAttacker, result);
            }

            // Validate damage context
            ValidationResult contextValidation = m_pErrorHandler->ValidateDamageContext(m_pAttacker, pParam->pDst, pParam);
            if (!contextValidation.bValid) {
                result.errorMessage = contextValidation.GetSummary();
                Logger::Error("CAttackForce::ExecuteAttack - Context validation failed: %s", result.errorMessage.c_str());
                return result;
            }
        } else {
            // Fallback validation
            if (!pParam) {
                result.errorMessage = "Invalid attack parameters";
                Logger::Error("CAttackForce::ExecuteAttack - Invalid attack parameters");
                return result;
            }

            if (!IsValidAttacker()) {
                result.errorMessage = "Invalid attacker";
                Logger::Error("CAttackForce::ExecuteAttack - Invalid attacker");
                return result;
            }
        }
        
        // Initialize attack state
        Reset();
        
        // Break stealth on attacker (original line 63)
        CAttackForceIntegration::BreakStealth(m_pAttacker);
        
        // Check if target exists and calculate hit chance
        if (pParam->pDst) {
            // Check for invulnerability (original lines 66-68)
            if (CAttackForceIntegration::GetEffectState(pParam->pDst, static_cast<int>(EffectType::Invulnerability))) {
                // Target is invulnerable - attack misses but still processes
                result.bSuccess = true;
                result.bHit = false;
                result.nDamage = 0;
                result.nTargetsHit = 1;
                result.hitTargets.push_back(pParam->pDst);
                Logger::Debug("CAttackForce::ExecuteAttack - Target invulnerable, attack missed");
                return result;
            }
            
            // Calculate hit chance (original lines 72-94)
            float fHitChance = CalculateHitChance(pParam->pDst, fAccuracyBonus);
            
            // Random hit/miss determination (original line 92)
            static std::random_device rd;
            static std::mt19937 gen(rd());
            std::uniform_int_distribution<> dis(0, 99);
            
            if (dis(gen) >= static_cast<int>(fHitChance)) {
                // Attack missed
                result.bSuccess = true;
                result.bHit = false;
                result.nDamage = 0;
                result.nTargetsHit = 1;
                result.hitTargets.push_back(pParam->pDst);
                Logger::Debug("CAttackForce::ExecuteAttack - Attack missed (chance: %.1f%%)", fHitChance);
                return result;
            }
        }
        
        // Calculate damage values (original lines 106-145)
        DamageCalculationParams damageParams = CalculateDamage(pParam, bUseEffBullet);
        
        // Get attack type configuration from field data (original lines 146-147)
        AttackTypeConfig config = GetAttackTypeConfig(static_cast<_base_fld*>(pParam->pFld), pParam);
        
        // Process attack based on type (original lines 148-221)
        if (config.IsSingleTarget()) {
            result = ProcessSingleTargetAttack(pParam, damageParams);
        } else if (config.IsFlashAttack()) {
            result = ProcessFlashAttack(pParam, damageParams, config);
        } else if (config.IsAreaAttack()) {
            result = ProcessAreaAttack(pParam, damageParams, config);
        } else {
            result.errorMessage = "Unknown attack type";
            Logger::Warning("CAttackForce::ExecuteAttack - Unknown attack type: %d", config.nType);
            return result;
        }
        
        // Cache result for potential reuse
        m_lastResult = result;
        
        Logger::Debug("CAttackForce::ExecuteAttack - Attack completed successfully, targets hit: %d", result.nTargetsHit);
        
    } catch (const std::exception& e) {
        result.errorMessage = std::string("Exception: ") + e.what();
        Logger::Error("CAttackForce::ExecuteAttack - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Calculate hit chance for target
 */
float CAttackForce::CalculateHitChance(CCharacter* pTarget, float fAccuracyBonus) const {
    if (!pTarget || !m_pAttacker) {
        return 0.0f;
    }
    
    try {
        // Base hit chance with accuracy bonus (original lines 72-73)
        float fHitChance = HitChance::BASE_HIT_CHANCE + fAccuracyBonus;
        
        // Get accuracy bonus effect (original line 72)
        float fAccuracyEffect = CAttackForceIntegration::GetEffectPlus(m_pAttacker, static_cast<int>(EffectType::AccuracyBonus));
        fHitChance += fAccuracyEffect;
        
        // Get target's avoidance rate (original lines 74-75)
        int nAvoidanceRate = CAttackForceIntegration::GetAvoidanceRate(pTarget);
        fHitChance -= static_cast<float>(nAvoidanceRate);
        
        // Apply player-specific accuracy bonus (original lines 77-81)
        if (m_pAttacker && m_pAttacker->m_ObjID.m_byID == 0) { // Player character
            float fPlayerAccuracy = CAttackForceIntegration::GetEffectPlus(m_pAttacker, static_cast<int>(EffectType::PlayerAccuracy));
            fHitChance += fPlayerAccuracy;
        }
        
        // Clamp to valid range (original lines 82-91)
        fHitChance = Utils::Clamp(fHitChance, static_cast<float>(HitChance::MIN_HIT_CHANCE), static_cast<float>(HitChance::MAX_HIT_CHANCE));
        
        Logger::Debug("CAttackForce::CalculateHitChance - Hit chance: %.1f%% (avoidance: %d)", fHitChance, nAvoidanceRate);
        
        return fHitChance;
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForce::CalculateHitChance - Exception: %s", e.what());
        return 0.0f;
    }
}

/**
 * Calculate damage values
 */
DamageCalculationParams CAttackForce::CalculateDamage(_attack_param* pParam, bool bUseEffBullet) const {
    DamageCalculationParams params;
    
    if (!pParam || !m_pAttacker) {
        return params;
    }
    
    try {
        params.bUseEffBullet = bUseEffBullet;
        
        // Calculate base damage (original lines 106-109)
        int nBaseAttackPoints = CalculateForceAttackPoints(false);
        params.fBaseDamage = static_cast<float>(pParam->nAddAttPnt + nBaseAttackPoints);
        
        // Apply damage rate effect
        float fDamageRate = CAttackForceIntegration::GetEffectRate(m_pAttacker, static_cast<int>(EffectType::DamageRate));
        params.fBaseDamage *= fDamageRate;
        
        // Calculate effective damage (original lines 110-113)
        int nEffAttackPoints = CalculateForceAttackPoints(bUseEffBullet);
        params.fEffectiveDamage = static_cast<float>(pParam->nAddAttPnt + nEffAttackPoints);
        params.fEffectiveDamage *= fDamageRate;
        
        // Apply damage multipliers (original lines 114-145)
        auto [fModifiedBase, fModifiedEffective] = ApplyDamageMultipliers(params.fBaseDamage, params.fEffectiveDamage);
        params.fBaseDamage = fModifiedBase;
        params.fEffectiveDamage = fModifiedEffective;
        
        Logger::Debug("CAttackForce::CalculateDamage - Base: %.1f, Effective: %.1f", params.fBaseDamage, params.fEffectiveDamage);
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForce::CalculateDamage - Exception: %s", e.what());
        params.Reset();
    }
    
    return params;
}

/**
 * Get attack type configuration from field data
 */
AttackTypeConfig CAttackForce::GetAttackTypeConfig(_base_fld* pField, _attack_param* pParam) const {
    AttackTypeConfig config;
    
    if (!pField) {
        Logger::Warning("CAttackForce::GetAttackTypeConfig - No field data, using single target");
        return config; // Default is single target (type 0)
    }
    
    try {
        // Extract attack type from field data (original line 146)
        // The original code accesses: *(_DWORD *)&v24[11].m_strCode[4]
        // This corresponds to a specific offset in the field structure
        config.nType = *reinterpret_cast<const int*>(&pField->m_strCode[4]);
        
        // Validate attack type
        if (config.nType < 0) {
            Logger::Warning("CAttackForce::GetAttackTypeConfig - Invalid attack type: %d", config.nType);
            config.nType = 0; // Default to single target
            return config;
        }
        
        // Set up configuration based on attack type
        if (config.IsAreaAttack()) {
            // Area attack configuration (original lines 214-219)
            if (pParam && pParam->fArea.size() >= 3) {
                config.targetArea[0] = pParam->fArea[0];
                config.targetArea[1] = pParam->fArea[1];
                config.targetArea[2] = pParam->fArea[2];
            }
            
            // Get radius from field data (original line 215)
            int nRadiusIndex = *reinterpret_cast<const int*>(&pField->m_strCode[60]);
            config.nLimitRadius = Utils::GetLimitRadius(nRadiusIndex);
            
        } else if (config.IsFlashAttack()) {
            // Flash attack configuration (original lines 196-206)
            int nDistanceIndex = *reinterpret_cast<const int*>(&pField->m_strCode[60]);
            config.nLimitDistance = Utils::GetLimitDistance(nDistanceIndex);
            
            int nAngleIndex = nDistanceIndex; // Same index used for angle
            config.nLimitAngle = Utils::GetLimitAngle(1, nAngleIndex);
        }
        
        Logger::Debug("CAttackForce::GetAttackTypeConfig - Type: %d, Radius: %d, Distance: %d, Angle: %d", 
                     config.nType, config.nLimitRadius, config.nLimitDistance, config.nLimitAngle);
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForce::GetAttackTypeConfig - Exception: %s", e.what());
        config = AttackTypeConfig(); // Reset to default
    }
    
    return config;
}

/**
 * Check if attacker is valid
 */
bool CAttackForce::IsValidAttacker() const {
    return m_pAttacker != nullptr;
}

/**
 * Check if target is valid for attack
 */
bool CAttackForce::IsValidTarget(CCharacter* pTarget) const {
    return pTarget != nullptr && pTarget != m_pAttacker;
}

/**
 * Reset attack state
 */
void CAttackForce::Reset() {
    m_lastResult.reset();
}

/**
 * Calculate base force attack points (protected method)
 */
int CAttackForce::CalculateForceAttackPoints(bool bUseEffBullet) const {
    if (!m_pAttacker) {
        return 0;
    }

    try {
        // This calls the legacy _CalcForceAttPnt function (original line 106, 110)
        // The implementation would depend on the specific character type and equipment
        return CAttackForceIntegration::CalculateForceAttackPoints(m_pAttacker, nullptr, bUseEffBullet);

    } catch (const std::exception& e) {
        Logger::Error("CAttackForce::CalculateForceAttackPoints - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Apply damage multipliers (destroyer, PvP bonuses, etc.)
 */
std::pair<float, float> CAttackForce::ApplyDamageMultipliers(float fBaseDamage, float fEffectiveDamage) const {
    if (!m_pAttacker) {
        return {fBaseDamage, fEffectiveDamage};
    }

    try {
        float fModifiedBase = fBaseDamage;
        float fModifiedEffective = fEffectiveDamage;

        // Check for destroyer or last attack buff bonuses (original lines 114-120)
        if (m_pAttacker->m_ObjID.m_byID == 0) { // Player character
            bool bIsDestroyer = CAttackForceIntegration::IsDestroyer(m_pAttacker);
            bool bHasLastAttBuff = CAttackForceIntegration::HasLastAttackBuff(static_cast<CPlayer*>(m_pAttacker));

            if (bIsDestroyer || bHasLastAttBuff) {
                fModifiedBase *= DamageMultipliers::DESTROYER_BONUS;
                fModifiedEffective *= DamageMultipliers::DESTROYER_BONUS;
                Logger::Debug("CAttackForce::ApplyDamageMultipliers - Applied destroyer/last attack bonus (%.1fx)", DamageMultipliers::DESTROYER_BONUS);
            }
        }

        // Check for PvP ranking bonuses (original lines 121-145)
        if (m_pAttacker->m_ObjID.m_byID == 0) { // Player character
            int nBossType = CAttackForceIntegration::GetPvPBossType(m_pAttacker);

            if (nBossType == static_cast<int>(PvPBossType::BossType2) ||
                nBossType == static_cast<int>(PvPBossType::BossType6)) {
                fModifiedBase *= DamageMultipliers::BOSS_TYPE_2_6_BONUS;
                fModifiedEffective *= DamageMultipliers::BOSS_TYPE_2_6_BONUS;
                Logger::Debug("CAttackForce::ApplyDamageMultipliers - Applied boss type 2/6 bonus (%.1fx)", DamageMultipliers::BOSS_TYPE_2_6_BONUS);
            } else if (nBossType == static_cast<int>(PvPBossType::NoBoss)) {
                fModifiedBase *= DamageMultipliers::BOSS_TYPE_0_BONUS;
                fModifiedEffective *= DamageMultipliers::BOSS_TYPE_0_BONUS;
                Logger::Debug("CAttackForce::ApplyDamageMultipliers - Applied no boss bonus (%.1fx)", DamageMultipliers::BOSS_TYPE_0_BONUS);
            }
        }

        return {fModifiedBase, fModifiedEffective};

    } catch (const std::exception& e) {
        Logger::Error("CAttackForce::ApplyDamageMultipliers - Exception: %s", e.what());
        return {fBaseDamage, fEffectiveDamage};
    }
}

/**
 * Process single target attack
 */
AttackForceResult CAttackForce::ProcessSingleTargetAttack(_attack_param* pParam, const DamageCalculationParams& damageParams) {
    AttackForceResult result;

    if (!pParam || !pParam->pDst) {
        result.errorMessage = "No target for single target attack";
        return result;
    }

    try {
        result.bSuccess = true;
        result.bHit = true;
        result.nTargetsHit = 1;
        result.hitTargets.push_back(pParam->pDst);

        // Calculate final damage (original lines 154-187)
        int nFinalAttackPower;
        if (damageParams.bUseEffBullet) {
            nFinalAttackPower = static_cast<int>(std::floor(damageParams.fEffectiveDamage));
        } else {
            nFinalAttackPower = static_cast<int>(std::floor(damageParams.fBaseDamage));
        }

        // Calculate actual damage using legacy system (original lines 163-186)
        result.nDamage = CAttackForceIntegration::CalculateAttackDamagePoint(
            m_pAttacker,
            nFinalAttackPower,
            pParam->nPart,
            pParam->nTol,
            pParam->pDst,
            false // bBackAttack - would need to be determined from pParam
        );

        Logger::Debug("CAttackForce::ProcessSingleTargetAttack - Damage: %d to target %p", result.nDamage, pParam->pDst);

    } catch (const std::exception& e) {
        result.errorMessage = std::string("Single target attack failed: ") + e.what();
        result.bSuccess = false;
        Logger::Error("CAttackForce::ProcessSingleTargetAttack - Exception: %s", e.what());
    }

    return result;
}

/**
 * Process area damage attack
 */
AttackForceResult CAttackForce::ProcessAreaAttack(_attack_param* pParam, const DamageCalculationParams& damageParams, const AttackTypeConfig& config) {
    AttackForceResult result;

    if (!pParam) {
        result.errorMessage = "Invalid parameters for area attack";
        return result;
    }

    try {
        // Set up area damage parameters for modern damage processor (original lines 214-220)
        AreaDamageParams areaParams;
        areaParams.nLimitRadius = config.nLimitRadius;
        areaParams.nAttPower = static_cast<int>(std::floor(damageParams.fBaseDamage));
        areaParams.nEffAttPower = static_cast<int>(std::floor(damageParams.fEffectiveDamage));
        areaParams.bUseEffBullet = damageParams.bUseEffBullet;
        areaParams.targetArea = config.targetArea;

        // Process area damage using modern damage processor
        std::vector<DamageResult> damageResults = CAttackForceIntegration::ProcessAreaDamage(m_pAttacker, areaParams, pParam);

        // Convert results
        result.bSuccess = true;
        result.bHit = !damageResults.empty();
        result.nTargetsHit = static_cast<int>(damageResults.size());

        for (const auto& damageResult : damageResults) {
            result.hitTargets.push_back(damageResult.pTarget);
            result.nDamage += damageResult.nDamage; // Total damage
        }

        Logger::Debug("CAttackForce::ProcessAreaAttack - Hit %d targets, total damage: %d", result.nTargetsHit, result.nDamage);

    } catch (const std::exception& e) {
        result.errorMessage = std::string("Area attack failed: ") + e.what();
        result.bSuccess = false;
        Logger::Error("CAttackForce::ProcessAreaAttack - Exception: %s", e.what());
    }

    return result;
}

/**
 * Process flash damage attack
 */
AttackForceResult CAttackForce::ProcessFlashAttack(_attack_param* pParam, const DamageCalculationParams& damageParams, const AttackTypeConfig& config) {
    AttackForceResult result;

    if (!pParam) {
        result.errorMessage = "Invalid parameters for flash attack";
        return result;
    }

    try {
        // Set up flash damage parameters for modern damage processor (original lines 196-207)
        FlashDamageParams flashParams;
        flashParams.nLimDist = config.nLimitDistance;
        flashParams.nAttPower = static_cast<int>(std::floor(damageParams.fBaseDamage));
        flashParams.nAngle = config.nLimitAngle;
        flashParams.nEffAttPower = static_cast<int>(std::floor(damageParams.fEffectiveDamage));
        flashParams.bUseEffBullet = damageParams.bUseEffBullet;

        // Process flash damage using modern damage processor
        std::vector<DamageResult> damageResults = CAttackForceIntegration::ProcessFlashDamage(m_pAttacker, flashParams, pParam);

        // Convert results
        result.bSuccess = true;
        result.bHit = !damageResults.empty();
        result.nTargetsHit = static_cast<int>(damageResults.size());

        for (const auto& damageResult : damageResults) {
            result.hitTargets.push_back(damageResult.pTarget);
            result.nDamage += damageResult.nDamage; // Total damage
        }

        Logger::Debug("CAttackForce::ProcessFlashAttack - Hit %d targets, total damage: %d", result.nTargetsHit, result.nDamage);

    } catch (const std::exception& e) {
        result.errorMessage = std::string("Flash attack failed: ") + e.what();
        result.bSuccess = false;
        Logger::Error("CAttackForce::ProcessFlashAttack - Exception: %s", e.what());
    }

    return result;
}

} // namespace Combat
} // namespace NexusProtection
