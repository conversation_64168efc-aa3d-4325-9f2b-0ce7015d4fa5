/*
 * Function: ?SetBase@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEBUEC2NPoint@2@@Z
 * Address: 0x1405759B0
 */

_BYTE *__fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::SetBase(__int64 a1, int (__fastcall ***a2)(_QWORD), _BYTE *a3)
{
  _BYTE *v3; // rax@2
  __int64 v4; // rax@7
  _BYTE *v5; // rax@8
  _BYTE *result; // rax@9
  CryptoPP::EC2NPoint v7; // [sp+28h] [bp-70h]@2
  int v8; // [sp+60h] [bp-38h]@1
  __int64 v9; // [sp+68h] [bp-30h]@1
  _BYTE *v10; // [sp+70h] [bp-28h]@2
  _BYTE *v11; // [sp+78h] [bp-20h]@2
  _BYTE *v12; // [sp+80h] [bp-18h]@2
  _BYTE *v13; // [sp+88h] [bp-10h]@8
  __int64 v14; // [sp+A0h] [bp+8h]@1
  int (__fastcall ***v15)(_QWORD); // [sp+A8h] [bp+10h]@1
  _BYTE *v16; // [sp+B0h] [bp+18h]@1

  v16 = a3;
  v15 = a2;
  v14 = a1;
  v9 = -2i64;
  v8 = 0;
  if ( (unsigned __int8)(**a2)(a2) )
  {
    LODWORD(v3) = ((int (__fastcall *)(int (__fastcall ***)(_QWORD), CryptoPP::EC2NPoint *, _BYTE *))(*v15)[1])(
                    v15,
                    &v7,
                    v16);
    v10 = v3;
    v11 = v3;
    v8 |= 1u;
    v12 = v3;
  }
  else
  {
    v12 = v16;
  }
  CryptoPP::EC2NPoint::operator=((_BYTE *)(v14 + 8), v12);
  if ( v8 & 1 )
  {
    v8 &= 0xFFFFFFFE;
    CryptoPP::EC2NPoint::~EC2NPoint(&v7);
  }
  if ( std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::empty(v14 + 112)
    || (LODWORD(v4) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v14 + 112, 0i64),
        !CryptoPP::EC2NPoint::operator==(v14 + 8, v4)) )
  {
    std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::resize(v14 + 112, 1i64);
    v13 = (_BYTE *)(v14 + 8);
    LODWORD(v5) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v14 + 112, 0i64);
    CryptoPP::EC2NPoint::operator=(v5, v13);
  }
  result = (_BYTE *)(unsigned __int8)(**v15)(v15);
  if ( (_BYTE)result )
    result = CryptoPP::EC2NPoint::operator=((_BYTE *)(v14 + 8), v16);
  return result;
}
