/*
 * Function: ?CallProc_RFONLINE_Cancel_Jap@CRFCashItemDatabase@@QEAAHAEAU_param_cash_rollback@@H@Z
 * Address: 0x140484090
 */

signed __int64 __fastcall CRFCashItemDatabase::CallProc_RFONLINE_Cancel_Jap(CRFCashItemDatabase *this, _param_cash_rollback *list, int iIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@4
  unsigned __int64 v9; // [sp+30h] [bp-178h]@4
  char *v10; // [sp+38h] [bp-170h]@4
  char *v11; // [sp+40h] [bp-168h]@4
  SQLLEN v12; // [sp+58h] [bp-150h]@22
  __int16 v13; // [sp+64h] [bp-144h]@9
  char DstBuf; // [sp+80h] [bp-128h]@4
  char v15; // [sp+81h] [bp-127h]@4
  unsigned __int8 v16; // [sp+184h] [bp-24h]@16
  unsigned __int8 v17; // [sp+185h] [bp-23h]@24
  unsigned __int64 v18; // [sp+190h] [bp-18h]@4
  CRFCashItemDatabase *v19; // [sp+1B0h] [bp+8h]@1
  _param_cash_rollback *v20; // [sp+1B8h] [bp+10h]@1
  int v21; // [sp+1C0h] [bp+18h]@1

  v21 = iIndex;
  v20 = list;
  v19 = this;
  v3 = &v6;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  DstBuf = 0;
  memset(&v15, 0, 0xFFui64);
  v11 = list->in_UserIP;
  v10 = list->in_szWorldName;
  v9 = list->data[iIndex].in_lnUID;
  LODWORD(StrLen_or_IndPtr) = list->data[iIndex].in_nPrice;
  SQLStmt = list->in_szAvatorName;
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "declare @out_status int exec dbo.SP_RF_FAIL_CHG_ITEM_GAMEON @uid = '%s',@charname = N'%s', @use_point = %d, @itemno "
    "= %I64d, @world = '%s', @ip_address = '%s', @s_status = @out_status output select @out_status",
    list->in_szAcc);
  if ( v19->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v19->vfptr, &DstBuf);
  if ( v19->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v19->vfptr) )
  {
    v13 = SQLExecDirect_0(v19->m_hStmtSelect, &DstBuf, -3);
    if ( v13 && v13 != 1 )
    {
      if ( v13 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v13, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v13, v19->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v13 = SQLFetch_0(v19->m_hStmtSelect);
      if ( v13 && v13 != 1 )
      {
        v16 = 0;
        if ( v13 == 100 )
        {
          v16 = 2;
        }
        else
        {
          SQLStmt = v19->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v13, &DstBuf, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v13, v19->m_hStmtSelect);
          v16 = 1;
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        result = v16;
      }
      else
      {
        StrLen_or_IndPtr = &v12;
        SQLStmt = 0i64;
        v13 = SQLGetData_0(v19->m_hStmtSelect, 1u, 4, &v20->data[v21].out_nStatus, 0i64, &v12);
        if ( v13 && v13 != 1 )
        {
          v17 = 0;
          if ( v13 == 100 )
          {
            v17 = 2;
          }
          else
          {
            SQLStmt = v19->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v13, &DstBuf, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v13, v19->m_hStmtSelect);
            v17 = 1;
          }
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          result = v17;
        }
        else
        {
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          if ( v19->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v19->vfptr, "%s Success", &DstBuf);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v19->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
