/*
 * Function: ?_db_Load_PvpOrderView@CMainThread@@AEAAEKAEAU_PVP_ORDER_VIEW_DB_BASE@@@Z
 * Address: 0x1401B4CB0
 */

char __fastcall CMainThread::_db_Load_PvpOrderView(CMainThread *this, unsigned int dwSerial, _PVP_ORDER_VIEW_DB_BASE *kData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-A8h]@1
  __int64 Dst; // [sp+30h] [bp-78h]@4
  int v8; // [sp+38h] [bp-70h]@10
  int v9; // [sp+3Ch] [bp-6Ch]@10
  long double v10; // [sp+40h] [bp-68h]@10
  long double v11; // [sp+48h] [bp-60h]@10
  long double v12; // [sp+50h] [bp-58h]@10
  long double v13; // [sp+58h] [bp-50h]@10
  int v14[10]; // [sp+60h] [bp-48h]@12
  char v15; // [sp+88h] [bp-20h]@13
  char v16; // [sp+89h] [bp-1Fh]@13
  bool v17; // [sp+8Ah] [bp-1Eh]@13
  char v18; // [sp+94h] [bp-14h]@4
  int j; // [sp+98h] [bp-10h]@10
  CMainThread *v20; // [sp+B0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+B8h] [bp+10h]@1
  _PVP_ORDER_VIEW_DB_BASE *v22; // [sp+C0h] [bp+18h]@1

  v22 = kData;
  dwSeriala = dwSerial;
  v20 = this;
  v3 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(&Dst, 0, 0x60ui64);
  v18 = CRFWorldDatabase::Select_PvpOrderViewInfo(v20->m_pWorldDB, dwSeriala, (_pvporderview_info *)&Dst);
  if ( v18 == 1 )
  {
    result = 24;
  }
  else if ( v18 == 2 )
  {
    if ( CRFWorldDatabase::Insert_PvpOrderViewInfo(v20->m_pWorldDB, dwSeriala) )
      result = 0;
    else
      result = 24;
  }
  else
  {
    v22->tUpdatedate = Dst;
    v22->nDeath = v8;
    v22->nKill = v9;
    v22->dTodayStacked = v10;
    v22->dPvpPoint = v11;
    v22->dPvpTempCash = v12;
    v22->dPvpCash = v13;
    for ( j = 0; j < 10; ++j )
      v22->dwKillerSerial[j] = v14[j];
    v22->byContHaveCash = v15;
    v22->byContLoseCash = v16;
    v22->bRaceWarRecvr = v17;
    result = 0;
  }
  return result;
}
