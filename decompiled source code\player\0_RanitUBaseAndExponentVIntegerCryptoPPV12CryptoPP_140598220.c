/*
 * Function: ??0?$_Ranit@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x140598220
 */

std::_Iterator_base *__fastcall std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Iterator_base::_Iterator_base(a1);
  return v2;
}
