/*
 * Function: ?SendMsg_RaceBossCryMsg@CPlayer@@QEAAXXZ
 * Address: 0x1400E6FF0
 */

void __fastcall CPlayer::SendMsg_RaceBossCryMsg(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-318h]@1
  int j; // [sp+30h] [bp-2E8h]@4
  char Dest[676]; // [sp+50h] [bp-2C8h]@6
  char pbyType; // [sp+2F4h] [bp-24h]@7
  char v7; // [sp+2F5h] [bp-23h]@7
  CPlayer *v8; // [sp+320h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 196i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
    strcpy_0(&Dest[65 * j], (const char *)&v8->m_pmCryMsg + 65 * j);
  pbyType = 13;
  v7 = 105;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, Dest, 0x28Au);
}
