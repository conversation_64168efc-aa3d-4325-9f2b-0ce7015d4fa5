#pragma once

/**
 * @file CGravityStoneRegener.h
 * @brief Gravity Stone Regenerator game object class for NexusProtection
 * @details Represents a gravity stone that can be regenerated and collected by players
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include <cstdint>
#include <memory>

// Forward declarations
class CMapData;
class CGameObject;
struct _object_create_setdata;

namespace NexusProtection {
namespace World {

/**
 * @enum GravityStoneState
 * @brief Represents the current state of a gravity stone regenerator
 */
enum class GravityStoneState : int32_t {
    Inactive = -1,      ///< Stone is inactive/not spawned
    Initializing = 0,   ///< Stone is being initialized
    Active = 1,         ///< Stone is active and can be created
    Available = 2,      ///< Stone is available for taking
    Taken = 3,          ///< Stone has been taken by a player
    Regenerating = 4    ///< Stone is regenerating after being taken
};

/**
 * @struct RegenPosition
 * @brief Represents a regeneration position with center coordinates
 */
struct RegenPosition {
    float m_fCenterPos[3];  ///< Center position coordinates [x, y, z]
    
    RegenPosition() {
        m_fCenterPos[0] = 0.0f;
        m_fCenterPos[1] = 0.0f;
        m_fCenterPos[2] = 0.0f;
    }
    
    RegenPosition(float x, float y, float z) {
        m_fCenterPos[0] = x;
        m_fCenterPos[1] = y;
        m_fCenterPos[2] = z;
    }
};

/**
 * @class CGravityStoneRegener
 * @brief Gravity stone regenerator game object
 * 
 * This class represents a gravity stone that can be:
 * - Created and placed on the map
 * - Taken by players when they are near enough
 * - Regenerated after being taken
 * - Tracked through various states
 */
class CGravityStoneRegener : public CGameObject {
public:
    /**
     * @brief Default constructor
     * Initializes the gravity stone regenerator with default values
     */
    CGravityStoneRegener();

    /**
     * @brief Virtual destructor
     * Ensures proper cleanup of derived class resources
     */
    virtual ~CGravityStoneRegener();

    /**
     * @brief Copy constructor (deleted)
     * Gravity stone regenerators should not be copied
     */
    CGravityStoneRegener(const CGravityStoneRegener&) = delete;

    /**
     * @brief Copy assignment operator (deleted)
     * Gravity stone regenerators should not be copied
     */
    CGravityStoneRegener& operator=(const CGravityStoneRegener&) = delete;

    /**
     * @brief Move constructor
     * @param other The CGravityStoneRegener to move from
     */
    CGravityStoneRegener(CGravityStoneRegener&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The CGravityStoneRegener to move from
     * @return Reference to this object
     */
    CGravityStoneRegener& operator=(CGravityStoneRegener&& other) noexcept;

    /**
     * @brief Create and initialize the gravity stone on the map
     * @param pkMap Pointer to the map data where the stone will be created
     * @return true if creation was successful, false otherwise
     */
    bool Create(CMapData* pkMap);

    /**
     * @brief Attempt to take the gravity stone
     * @param pkMap Pointer to the map data
     * @param pfCurPos Current position coordinates [x, y, z] of the player
     * @return Result code (0 = success, negative = error, positive = info)
     */
    int8_t Take(CMapData* pkMap, const float* pfCurPos);

    /**
     * @brief Check if a position is near enough to interact with the stone
     * @param pfCurPos Current position coordinates [x, y, z]
     * @return true if position is near enough, false otherwise
     */
    bool IsNearPosition(const float* pfCurPos) const;

    /**
     * @brief Get the current state of the gravity stone
     * @return The current state of the stone
     */
    [[nodiscard]] GravityStoneState GetState() const noexcept;

    /**
     * @brief Set the state of the gravity stone
     * @param state The new state for the stone
     */
    void SetState(GravityStoneState state) noexcept;

    /**
     * @brief Get the object serial number
     * @return The unique serial number of this object
     */
    [[nodiscard]] uint32_t GetObjectSerial() const noexcept;

    /**
     * @brief Set the regeneration position
     * @param regenPos Shared pointer to the regeneration position
     */
    void SetRegenPosition(std::shared_ptr<RegenPosition> regenPos);

    /**
     * @brief Get the regeneration position
     * @return Shared pointer to the regeneration position, or nullptr if not set
     */
    [[nodiscard]] std::shared_ptr<const RegenPosition> GetRegenPosition() const noexcept;

    /**
     * @brief Check if the stone is available for taking
     * @return true if the stone can be taken, false otherwise
     */
    [[nodiscard]] bool IsAvailable() const noexcept;

    /**
     * @brief Destroy the gravity stone
     * Cleans up resources and removes the stone from the map
     */
    void Destroy();

private:
    /// Current state of the gravity stone
    GravityStoneState m_eState;
    
    /// Object serial number for unique identification
    uint32_t m_dwObjSerial;
    
    /// Regeneration position data
    std::shared_ptr<RegenPosition> m_pkRegenPos;
    
    /// Static serial counter for unique object identification
    static uint32_t ms_dwSerialCnt;

    /**
     * @brief Initialize the gravity stone with default values
     */
    void Initialize();

    /**
     * @brief Send message to notify state change
     * Notifies the system that the stone state has changed
     */
    void SendMsgAlterState();

    /**
     * @brief Calculate distance to regeneration position
     * @param pfCurPos Current position coordinates
     * @return Distance to the regeneration position
     */
    [[nodiscard]] float CalculateDistanceToRegen(const float* pfCurPos) const;

    /**
     * @brief Check if the stone can be created
     * @return true if creation conditions are met, false otherwise
     */
    [[nodiscard]] bool CanCreate() const noexcept;

    /**
     * @brief Check if the stone can be taken
     * @return true if taking conditions are met, false otherwise
     */
    [[nodiscard]] bool CanTake() const noexcept;
};

} // namespace World
} // namespace NexusProtection
