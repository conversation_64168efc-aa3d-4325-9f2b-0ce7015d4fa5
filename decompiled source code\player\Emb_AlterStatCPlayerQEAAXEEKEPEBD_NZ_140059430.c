/*
 * Function: ?Emb_AlterStat@CPlayer@@QEAAXEEKEPEBD_N@Z
 * Address: 0x140059430
 */

void __usercall CPlayer::Emb_AlterStat(CPlayer *this@<rcx>, char byMasteryClass@<dl>, char byIndex@<r8b>, unsigned int dwAlter@<r9d>, double a5@<xmm0>, char by<PERSON><PERSON>on, const char *strErrorCodePos, bool bPcbangPrimiumFavorReward)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  char *v10; // rax@9
  char *v11; // rax@10
  double v12; // xmm1_8@18
  double v13; // xmm1_8@19
  double v14; // xmm1_8@26
  float v15; // xmm0_4@29
  double v16; // xmm1_8@29
  int v17; // eax@35
  __int64 v18; // rax@47
  __int64 v19; // [sp+0h] [bp-A8h]@1
  unsigned int *pdwAfterCum; // [sp+20h] [bp-88h]@9
  const char *v21; // [sp+28h] [bp-80h]@9
  char v22; // [sp+30h] [bp-78h]@4
  unsigned int dwNewStat; // [sp+44h] [bp-64h]@4
  int v24; // [sp+54h] [bp-54h]@14
  unsigned int v25; // [sp+58h] [bp-50h]@22
  float v26; // [sp+5Ch] [bp-4Ch]@22
  double v27; // [sp+60h] [bp-48h]@30
  char v28; // [sp+68h] [bp-40h]@31
  _base_fld *v29; // [sp+70h] [bp-38h]@32
  int v30; // [sp+78h] [bp-30h]@35
  int v31; // [sp+7Ch] [bp-2Ch]@40
  bool v32; // [sp+80h] [bp-28h]@41
  int v33; // [sp+84h] [bp-24h]@9
  unsigned int v34; // [sp+88h] [bp-20h]@9
  int v35; // [sp+8Ch] [bp-1Ch]@10
  unsigned int v36; // [sp+90h] [bp-18h]@10
  float v37; // [sp+94h] [bp-14h]@29
  unsigned int v38; // [sp+98h] [bp-10h]@35
  CPlayer *v39; // [sp+B0h] [bp+8h]@1
  char v40; // [sp+B8h] [bp+10h]@1
  char v41; // [sp+C0h] [bp+18h]@1
  signed int dwAlterCum; // [sp+C8h] [bp+20h]@1
  signed int dwAlterCuma; // [sp+C8h] [bp+20h]@30

  dwAlterCum = dwAlter;
  v41 = byIndex;
  v40 = byMasteryClass;
  v39 = this;
  v8 = &v19;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v22 = 10;
  dwNewStat = 0;
  if ( dwAlter && (byReason || !v39->m_bInGuildBattle) )
  {
    if ( _STAT_DB_BASE::IsRangePerMastery(byMasteryClass, byIndex) )
    {
      if ( !byReason )
        dwAlterCum *= 5 * (((int (__fastcall *)(CPlayer *))v39->vfptr->GetLevel)(v39) / 10 + 1);
      v24 = dwAlterCum;
      if ( byReason != 1 )
      {
        if ( v40 != 6 || CPlayerDB::GetRaceCode(&v39->m_Param) != 1 )
        {
          if ( CPlayer::IsApplyPcbangPrimium(v39) )
          {
            *(_QWORD *)&v12 = LODWORD(PCBANG_PRIMIUM_FAVOR::BASE_MASTERY);
            *(float *)&v12 = (float)dwAlterCum + (float)((float)v24 * (float)(PCBANG_PRIMIUM_FAVOR::BASE_MASTERY - 1.0));
            a5 = v12;
            dwAlterCum = (signed int)ffloor(*(float *)&v12);
          }
          else
          {
            *(_QWORD *)&v13 = LODWORD(MASTERY_GET_RATE);
            *(float *)&v13 = (float)dwAlterCum + (float)((float)v24 * (float)(MASTERY_GET_RATE - 1.0));
            a5 = v13;
            dwAlterCum = (signed int)ffloor(*(float *)&v13);
          }
        }
        if ( v40 != 6 || CPlayerDB::GetRaceCode(&v39->m_Param) != 1 )
        {
          _effect_parameter::GetEff_Have(&v39->m_EP, 6);
          v25 = LODWORD(a5);
          _effect_parameter::GetEff_Have(&v39->m_EP, 82);
          v26 = *(float *)&a5;
          *(float *)&v25 = *(float *)&v25 == 0.0 ? v26 : *(float *)&v25 + v26;
          *(_QWORD *)&a5 = v25;
          if ( *(float *)&v25 > 1.0 )
          {
            *(_QWORD *)&v14 = v25;
            *(float *)&v14 = (float)dwAlterCum + (float)((float)v24 * (float)(*(float *)&v25 - 1.0));
            a5 = v14;
            dwAlterCum = (signed int)ffloor(*(float *)&v14);
          }
        }
        if ( v40 != 6 || CPlayerDB::GetRaceCode(&v39->m_Param) != 1 )
        {
          v15 = (float)v24;
          v37 = (float)v24;
          _effect_parameter::GetEff_Rate(&v39->m_EP, 35);
          *(_QWORD *)&v16 = LODWORD(v37);
          *(float *)&v16 = (float)dwAlterCum + (float)(v37 * (float)(v15 - 1.0));
          a5 = v16;
          dwAlterCum = (signed int)ffloor(*(float *)&v16);
        }
      }
      TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v39->m_id.wIndex);
      v27 = a5;
      dwAlterCuma = (signed int)floor((double)dwAlterCum * a5);
      if ( dwAlterCuma )
      {
        v28 = v41;
        if ( v40 == 3 )
        {
          v29 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, (unsigned __int8)v41);
          if ( v29 )
          {
            if ( *(_DWORD *)&v29[1].m_strCode[4] >= 8 )
              return;
            v28 = v29[1].m_strCode[4];
          }
        }
        v38 = CPlayer::_check_mastery_cum_lim(v39, v40, v28);
        v17 = _MASTERY_PARAM::GetCumPerMast(&v39->m_pmMst, v40, v28);
        v30 = v38 - v17;
        if ( ((v38 - v17) & 0x80000000) != 0 )
          v30 = 0;
        if ( dwAlterCuma > (unsigned int)v30 )
          dwAlterCuma = v30;
        if ( dwAlterCuma )
        {
          v31 = _STAT_DB_BASE::GetStatIndex(v40, v41);
          if ( v39->m_pmMst.m_BaseCum.m_dwDamWpCnt[v31] <= 0xEE6B2800 )
          {
            v32 = _MASTERY_PARAM::AlterCumPerMast(&v39->m_pmMst, v40, v41, dwAlterCuma, &dwNewStat);
            if ( v39->m_pmMst.m_bUpdateEquipMast )
              v39->m_bUpCheckEquipEffect = 1;
            CPlayer::SendMsg_StatInform(v39, v31, dwNewStat, byReason);
            if ( v39->m_pmMst.m_MastUpData.bUpdate )
              CPlayer::ReCalcMaxHFSP(v39, 1, 0);
            if ( v39->m_pUserDB )
              CUserDB::Update_Stat(v39->m_pUserDB, v31, dwNewStat, v32);
            v39->m_Param.m_dwAlterMastery[v31] += dwAlterCuma;
            v18 = v39->m_pmMst.m_MastUpData.bUpdate;
          }
        }
      }
    }
    else if ( strErrorCodePos )
    {
      v33 = (unsigned __int8)v41;
      v34 = (unsigned __int8)v40;
      v10 = CPlayerDB::GetCharNameA(&v39->m_Param);
      v21 = strErrorCodePos;
      LODWORD(pdwAfterCum) = v33;
      CLogFile::Write(&stru_1799C8E78, "%s: _STAT_DB_BASE::IsRangePerMastery(%d, %d) == false : CodePos: %s", v10, v34);
    }
    else
    {
      v35 = (unsigned __int8)v41;
      v36 = (unsigned __int8)v40;
      v11 = CPlayerDB::GetCharNameA(&v39->m_Param);
      LODWORD(pdwAfterCum) = v35;
      CLogFile::Write(&stru_1799C8E78, "%s: _STAT_DB_BASE::IsRangePerMastery(%d, %d) == false", v11, v36);
    }
  }
}
