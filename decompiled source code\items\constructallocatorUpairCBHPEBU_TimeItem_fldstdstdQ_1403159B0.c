/*
 * Function: ?construct@?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@QEAAXPEAU?$pair@$$CBHPEBU_TimeItem_fld@@@2@AEBU32@@Z
 * Address: 0x1403159B0
 */

void __fastcall std::allocator<std::pair<int const,_TimeItem_fld const *>>::construct(std::allocator<std::pair<int const ,_TimeItem_fld const *> > *this, std::pair<int const ,_TimeItem_fld const *> *_Ptr, std::pair<int const ,_TimeItem_fld const *> *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<std::pair<int const,_TimeItem_fld const *>,std::pair<int const,_TimeItem_fld const *>>(_Ptr, _Val);
}
