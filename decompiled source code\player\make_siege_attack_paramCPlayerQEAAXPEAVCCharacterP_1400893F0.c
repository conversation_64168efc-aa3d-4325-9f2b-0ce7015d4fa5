/*
 * Function: ?make_siege_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAMEPEAU_BulletItem_fld@@MPEAU_attack_param@@2M@Z
 * Address: 0x1400893F0
 */

void __fastcall CPlayer::make_siege_attack_param(CPlayer *this, <PERSON>haracter *pDst, float *pfAttackPos, char by<PERSON><PERSON>, _BulletItem_fld *pBulletFld, float fAddBulletFc, _attack_param *pAP, _BulletItem_fld *pEffBulletFld, float fAddEffBtFc)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  float v11; // xmm0_4@17
  float v12; // xmm1_4@17
  float v13; // xmm0_4@17
  float v14; // xmm0_4@17
  float v15; // xmm1_4@17
  float v16; // xmm0_4@17
  float v17; // xmm0_4@18
  float v18; // xmm1_4@18
  float v19; // xmm0_4@18
  float v20; // xmm0_4@18
  float v21; // xmm1_4@18
  float v22; // xmm0_4@18
  __int64 v23; // [sp+0h] [bp-58h]@1
  int v24; // [sp+20h] [bp-38h]@12
  _base_fld *v25; // [sp+28h] [bp-30h]@15
  float v26; // [sp+30h] [bp-28h]@17
  float v27; // [sp+34h] [bp-24h]@17
  float v28; // [sp+38h] [bp-20h]@17
  float v29; // [sp+3Ch] [bp-1Ch]@17
  float v30; // [sp+40h] [bp-18h]@18
  float v31; // [sp+44h] [bp-14h]@18
  float v32; // [sp+48h] [bp-10h]@18
  float v33; // [sp+4Ch] [bp-Ch]@18
  CPlayer *pTarget; // [sp+60h] [bp+8h]@1
  CMonster *v35; // [sp+68h] [bp+10h]@1
  float *Src; // [sp+70h] [bp+18h]@1

  Src = pfAttackPos;
  v35 = (CMonster *)pDst;
  pTarget = this;
  v9 = &v23;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  pAP->pDst = pDst;
  if ( !pDst || pDst->m_ObjID.m_byID || pDst[25].m_SFCont[0][5].m_wszPlayerName[16] == 255 )
    pAP->nPart = (unsigned __int8)byPart;
  else
    pAP->nPart = pDst[25].m_SFCont[0][5].m_wszPlayerName[16];
  if ( pBulletFld )
    pAP->nTol = pBulletFld->m_nProperty;
  else
    pAP->nTol = pTarget->m_pmWpn.byAttTolType;
  pAP->nClass = pTarget->m_pmWpn.byWpClass;
  pAP->nWpType = pTarget->m_pmWpn.byWpType;
  v24 = 1;
  if ( pTarget->m_pmWpn.byWpType == 7 )
    v24 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
  else
    v24 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
  v25 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, pTarget->m_pSiegeItem->m_wItemIndex);
  if ( strncmp(pTarget->m_pmWpn.strEffBulletType, "-1", 2ui64) && pEffBulletFld )
  {
    v26 = (float)pTarget->m_pmWpn.nGaMinAF;
    v11 = v26;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v12 = v26 * v11;
    v13 = (float)((float)((float)((float)(v26 * v11) * fAddBulletFc) * fAddEffBtFc)
                + (float)(signed int)CPlayer::s_nAddMstFc[v24])
        * *(float *)&v25[5].m_strCode[24];
    v27 = (float)((float)((float)(v12 * fAddBulletFc) * fAddEffBtFc) + (float)(signed int)CPlayer::s_nAddMstFc[v24])
        * *(float *)&v25[5].m_strCode[24];
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
    pAP->nMinAFPlus = (signed int)ffloor(v27 * v13);
    v28 = (float)pTarget->m_pmWpn.nGaMaxAF;
    v14 = v28;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    v15 = v28 * v14;
    v16 = (float)((float)((float)((float)(v28 * v14) * fAddBulletFc) * fAddEffBtFc)
                + (float)(signed int)CPlayer::s_nAddMstFc[v24])
        * *(float *)&v25[5].m_strCode[24];
    v29 = (float)((float)((float)(v15 * fAddBulletFc) * fAddEffBtFc) + (float)(signed int)CPlayer::s_nAddMstFc[v24])
        * *(float *)&v25[5].m_strCode[24];
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
    pAP->nMaxAFPlus = (signed int)ffloor(v29 * v16);
    pAP->nEffShotNum = 1;
  }
  v30 = (float)pTarget->m_pmWpn.nGaMinAF;
  v17 = v30;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  v18 = v30 * v17;
  v19 = (float)((float)((float)(v30 * v17) * fAddBulletFc) + (float)(signed int)CPlayer::s_nAddMstFc[v24])
      * *(float *)&v25[5].m_strCode[24];
  v31 = (float)((float)(v18 * fAddBulletFc) + (float)(signed int)CPlayer::s_nAddMstFc[v24])
      * *(float *)&v25[5].m_strCode[24];
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
  pAP->nMinAF = (signed int)ffloor(v31 * v19);
  v32 = (float)pTarget->m_pmWpn.nGaMaxAF;
  v20 = v32;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  v21 = v32 * v20;
  v22 = (float)((float)((float)(v32 * v20) * fAddBulletFc) + (float)(signed int)CPlayer::s_nAddMstFc[v24])
      * *(float *)&v25[5].m_strCode[24];
  v33 = (float)((float)(v21 * fAddBulletFc) + (float)(signed int)CPlayer::s_nAddMstFc[v24])
      * *(float *)&v25[5].m_strCode[24];
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 23);
  pAP->nMaxAF = (signed int)ffloor(v33 * v22);
  pAP->nMinSel = pTarget->m_pmWpn.byGaMinSel;
  pAP->nMaxSel = pTarget->m_pmWpn.byGaMaxSel;
  pAP->nExtentRange = 20;
  if ( pBulletFld )
  {
    pAP->nAttactType = pBulletFld->m_nEffectGroup;
    pAP->nShotNum = 1;
  }
  memcpy_0(pAP->fArea, Src, 0xCui64);
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v35 && v35->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v35, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
}
