/*
 * Function: ?OnLoop_GuildSystem@@YAX_N@Z
 * Address: 0x1402589E0
 */

void __fastcall OnLoop_GuildSystem(bool bChangeDay)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  bool v5; // [sp+40h] [bp+8h]@1

  v5 = bChangeDay;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 500; ++j )
  {
    if ( CGuild::IsFill(&g_Guild[j]) )
      CGuild::Loop(&g_Guild[j], v5);
  }
}
