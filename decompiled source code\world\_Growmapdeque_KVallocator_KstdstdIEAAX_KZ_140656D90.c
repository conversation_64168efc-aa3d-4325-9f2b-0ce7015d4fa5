/*
 * Function: ?_Growmap@?$deque@_KV?$allocator@_K@std@@@std@@IEAAX_K@Z
 * Address: 0x140656D90
 */

unsigned __int64 __fastcall std::deque<unsigned __int64,std::allocator<unsigned __int64>>::_Growmap(__int64 a1, unsigned __int64 a2)
{
  unsigned __int64 v2; // rax@1
  unsigned __int64 v3; // rax@6
  __int64 v4; // rax@8
  signed __int64 v5; // rcx@8
  __int64 v6; // rax@8
  __int64 v7; // rax@9
  signed __int64 v8; // rcx@10
  __int64 v9; // rax@10
  signed __int64 v10; // rcx@11
  unsigned __int64 result; // rax@13
  unsigned __int64 v12; // [sp+20h] [bp-58h]@8
  unsigned __int64 v13; // [sp+28h] [bp-50h]@3
  __int64 v14; // [sp+38h] [bp-40h]@8
  __int64 v15; // [sp+40h] [bp-38h]@9
  __int64 v16; // [sp+48h] [bp-30h]@9
  __int64 v17; // [sp+50h] [bp-28h]@10
  unsigned __int64 v18; // [sp+58h] [bp-20h]@8
  unsigned __int64 v19; // [sp+60h] [bp-18h]@10
  unsigned __int64 v20; // [sp+68h] [bp-10h]@11
  __int64 v21; // [sp+80h] [bp+8h]@1
  unsigned __int64 v22; // [sp+88h] [bp+10h]@1

  v22 = a2;
  v21 = a1;
  LODWORD(v2) = ((int (*)(void))std::deque<unsigned __int64,std::allocator<unsigned __int64>>::max_size)();
  if ( v2 / 2 - *(_QWORD *)(v21 + 32) < v22 )
    std::deque<unsigned __int64,std::allocator<unsigned __int64>>::_Xlen(v21, v2 % 2);
  v13 = *(_QWORD *)(v21 + 32) / 2ui64;
  if ( v13 < 8 )
    v13 = 8i64;
  if ( v22 < v13 )
  {
    LODWORD(v3) = std::deque<unsigned __int64,std::allocator<unsigned __int64>>::max_size(
                    v21,
                    *(_QWORD *)(v21 + 32) % 2ui64);
    if ( *(_QWORD *)(v21 + 32) <= v3 / 2 - v13 )
      v22 = v13;
  }
  v12 = *(_QWORD *)(v21 + 40) / 2ui64;
  LODWORD(v4) = std::allocator<unsigned __int64 *>::allocate(v21 + 8, v22 + *(_QWORD *)(v21 + 32));
  v14 = v4;
  v5 = *(_QWORD *)(v21 + 24) + 8i64 * *(_QWORD *)(v21 + 32);
  v18 = *(_QWORD *)(v21 + 24) + 8 * v12;
  LODWORD(v6) = stdext::unchecked_uninitialized_copy<unsigned __int64 * *,unsigned __int64 * *,std::allocator<unsigned __int64 *>>(
                  v18,
                  v5,
                  v4 + 8 * v12,
                  v21 + 8);
  if ( v12 > v22 )
  {
    stdext::unchecked_uninitialized_copy<unsigned __int64 * *,unsigned __int64 * *,std::allocator<unsigned __int64 *>>(
      *(_QWORD *)(v21 + 24),
      *(_QWORD *)(v21 + 24) + 8 * v22,
      v6,
      v21 + 8);
    v8 = *(_QWORD *)(v21 + 24) + 8 * v12;
    v19 = *(_QWORD *)(v21 + 24) + 8 * v22;
    LODWORD(v9) = stdext::unchecked_uninitialized_copy<unsigned __int64 * *,unsigned __int64 * *,std::allocator<unsigned __int64 *>>(
                    v19,
                    v8,
                    v14,
                    v21 + 8);
    v17 = 0i64;
    stdext::unchecked_uninitialized_fill_n<unsigned __int64 * *,unsigned __int64,unsigned __int64 *,std::allocator<unsigned __int64 *>>(
      v9,
      v22,
      &v17,
      v21 + 8);
  }
  else
  {
    LODWORD(v7) = stdext::unchecked_uninitialized_copy<unsigned __int64 * *,unsigned __int64 * *,std::allocator<unsigned __int64 *>>(
                    *(_QWORD *)(v21 + 24),
                    *(_QWORD *)(v21 + 24) + 8 * v12,
                    v6,
                    v21 + 8);
    v15 = 0i64;
    stdext::unchecked_uninitialized_fill_n<unsigned __int64 * *,unsigned __int64,unsigned __int64 *,std::allocator<unsigned __int64 *>>(
      v7,
      v22 - v12,
      &v15,
      v21 + 8);
    v16 = 0i64;
    stdext::unchecked_uninitialized_fill_n<unsigned __int64 * *,unsigned __int64,unsigned __int64 *,std::allocator<unsigned __int64 *>>(
      v14,
      v12,
      &v16,
      v21 + 8);
  }
  v10 = *(_QWORD *)(v21 + 24) + 8i64 * *(_QWORD *)(v21 + 32);
  v20 = *(_QWORD *)(v21 + 24) + 8 * v12;
  std::_Destroy_range<unsigned __int64 *,std::allocator<unsigned __int64 *>>(v20, v10, v21 + 8);
  if ( *(_QWORD *)(v21 + 24) )
    std::allocator<unsigned __int64 *>::deallocate(v21 + 8, *(_QWORD *)(v21 + 24), *(_QWORD *)(v21 + 32));
  *(_QWORD *)(v21 + 24) = v14;
  result = v22 + *(_QWORD *)(v21 + 32);
  *(_QWORD *)(v21 + 32) = result;
  return result;
}
