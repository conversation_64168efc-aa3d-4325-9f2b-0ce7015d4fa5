/*
 * Function: ?SendNextHonorGuildList@CHonorGuild@@QEAAXGE@Z
 * Address: 0x14025EDB0
 */

void __fastcall CHonorGuild::SendNextHonorGuildList(CHonorGuild *this, unsigned __int16 wIndex, char byRace)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-38h]@5
  char pbyType; // [sp+34h] [bp-24h]@4
  char v8; // [sp+35h] [bp-23h]@4
  CHonorGuild *v9; // [sp+60h] [bp+8h]@1
  unsigned __int16 v10; // [sp+68h] [bp+10h]@1
  char v11; // [sp+70h] [bp+18h]@1

  v11 = byRace;
  v10 = wIndex;
  v9 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pbyType = 27;
  v8 = 119;
  if ( v9->m_bNext[(unsigned __int8)byRace] )
  {
    nLen = _guild_honor_list_result_zocl::size(v9->m_pNextHonorGuild[(unsigned __int8)byRace]);
    CNetProcess::LoadSendMsg(
      unk_1414F2088,
      v10,
      &pbyType,
      &v9->m_pNextHonorGuild[(unsigned __int8)v11]->byListNum,
      nLen);
  }
  else
  {
    nLen = _guild_honor_list_result_zocl::size(v9->m_pCurrHonorGuild[(unsigned __int8)byRace]);
    CNetProcess::LoadSendMsg(
      unk_1414F2088,
      v10,
      &pbyType,
      &v9->m_pCurrHonorGuild[(unsigned __int8)v11]->byListNum,
      nLen);
  }
}
