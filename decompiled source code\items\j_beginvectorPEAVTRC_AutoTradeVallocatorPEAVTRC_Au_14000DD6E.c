/*
 * Function: j_?begin@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@2@XZ
 * Address: 0x14000DD6E
 */

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::begin(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::begin(this, result);
}
