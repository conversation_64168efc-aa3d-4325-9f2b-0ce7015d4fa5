/*
 * Function: ?Check_Load_Event_Status@CActionPointSystemMgr@@QEAAXEPEAU_action_point_system_ini@@@Z
 * Address: 0x140411250
 */

void __fastcall CActionPointSystemMgr::Check_Load_Event_Status(CActionPointSystemMgr *this, char byActionCode, _action_point_system_ini *pIni)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@5
  int v7; // [sp+34h] [bp-14h]@9
  CActionPointSystemMgr *v8; // [sp+50h] [bp+8h]@1
  char v9; // [sp+58h] [bp+10h]@1
  _action_point_system_ini *v10; // [sp+60h] [bp+18h]@1

  v10 = pIni;
  v9 = byActionCode;
  v8 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pIni->m_bUse_event )
  {
    _time32(&Time);
    if ( Time <= v10->m_EventTime[1] )
    {
      if ( Time < v10->m_EventTime[0] || Time > v10->m_EventTime[1] )
      {
        if ( Time < v10->m_EventTime[0] )
          CActionPointSystemMgr::SetEventStatus(v8, v9, 1);
      }
      else
      {
        v7 = v10->m_EventTime[1] - Time;
        if ( v7 > 0 )
        {
          if ( v7 <= 0 )
            CActionPointSystemMgr::SetEventStatus(v8, v9, 0);
          else
            CActionPointSystemMgr::SetEventStatus(v8, v9, 2);
        }
        else
        {
          CActionPointSystemMgr::SetEventStatus(v8, v9, 0);
        }
      }
    }
    else
    {
      CActionPointSystemMgr::SetEventStatus(v8, v9, 0);
    }
  }
}
