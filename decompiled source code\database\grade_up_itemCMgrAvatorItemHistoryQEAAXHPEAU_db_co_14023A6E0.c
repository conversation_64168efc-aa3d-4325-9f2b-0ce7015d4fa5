/*
 * Function: ?grade_up_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@00EEKPEAD@Z
 * Address: 0x14023A6E0
 */

void __fastcall CMgrAvatorItemHistory::grade_up_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, _STORAGE_LIST::_db_con *pTalik, _STORAGE_LIST::_db_con *pJewel, char byJewel<PERSON>um, char byErrCode, unsigned int dwAfterLv, char *pszFileName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@4
  char *v12; // rax@5
  char *v13; // rax@9
  __int64 v14; // [sp+0h] [bp-F8h]@1
  char *v15; // [sp+20h] [bp-D8h]@5
  unsigned __int64 v16; // [sp+28h] [bp-D0h]@5
  char *v17; // [sp+30h] [bp-C8h]@5
  char *v18; // [sp+38h] [bp-C0h]@5
  char *v19; // [sp+40h] [bp-B8h]@5
  char Dest; // [sp+58h] [bp-A0h]@4
  _base_fld *v21; // [sp+88h] [bp-70h]@4
  _base_fld *v22; // [sp+90h] [bp-68h]@12
  int j; // [sp+98h] [bp-60h]@12
  _base_fld *v24; // [sp+A0h] [bp-58h]@14
  char *v25; // [sp+B0h] [bp-48h]@5
  char *v26; // [sp+B8h] [bp-40h]@5
  int nTableCode; // [sp+C0h] [bp-38h]@5
  char *v28; // [sp+C8h] [bp-30h]@9
  char *v29; // [sp+D0h] [bp-28h]@9
  int v30; // [sp+D8h] [bp-20h]@9
  unsigned __int64 v31; // [sp+E0h] [bp-18h]@4
  CMgrAvatorItemHistory *v32; // [sp+100h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v33; // [sp+110h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v34; // [sp+118h] [bp+20h]@1

  v34 = pTalik;
  v33 = pItem;
  v32 = this;
  v9 = &v14;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v31 = (unsigned __int64)&v14 ^ _security_cookie;
  sData[0] = 0;
  v11 = DisplayItemUpgInfo(pItem->m_byTableCode, pItem->m_dwLv);
  strcpy_0(&Dest, v11);
  v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v33->m_byTableCode, v33->m_wItemIndex);
  if ( byErrCode )
  {
    switch ( byErrCode )
    {
      case 100:
        v18 = v32->m_szCurTime;
        v17 = v32->m_szCurDate;
        v16 = v33->m_lnUID;
        v15 = &Dest;
        sprintf(sBuf, "UPGRADE(FAIL): %s_%u_@%s[%I64u] [%s %s]\r\n", v21->m_strCode, v33->m_dwDur);
        strcat_0(sData, sBuf);
        break;
      case 101:
        v28 = v32->m_szCurTime;
        v29 = v32->m_szCurDate;
        v30 = v33->m_byTableCode;
        v13 = DisplayItemUpgInfo(v30, dwAfterLv);
        v19 = v28;
        v18 = v29;
        v17 = v13;
        v16 = v33->m_lnUID;
        v15 = &Dest;
        sprintf(sBuf, "UPGRADE(ZERO): %s_%u_@%s[%I64u]->%s [%s %s]\r\n", v21->m_strCode, v33->m_dwDur);
        strcat_0(sData, sBuf);
        break;
      case 102:
        v18 = v32->m_szCurTime;
        v17 = v32->m_szCurDate;
        v16 = v33->m_lnUID;
        v15 = &Dest;
        sprintf(sBuf, "UPGRADE(LOST): %s_%u_@%s[%I64u] [%s %s]\r\n", v21->m_strCode, v33->m_dwDur);
        strcat_0(sData, sBuf);
        break;
    }
  }
  else
  {
    v25 = v32->m_szCurTime;
    v26 = v32->m_szCurDate;
    nTableCode = v33->m_byTableCode;
    v12 = DisplayItemUpgInfo(nTableCode, dwAfterLv);
    v19 = v25;
    v18 = v26;
    v17 = v12;
    v16 = v33->m_lnUID;
    v15 = &Dest;
    sprintf(sBuf, "UPGRADE(SUCC): %s_%u_@%s[%I64u]->%s [%s %s]\r\n", v21->m_strCode, v33->m_dwDur);
    strcat_0(sData, sBuf);
  }
  v22 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v34->m_byTableCode, v34->m_wItemIndex);
  sprintf(sBuf, "\t- T %s \r\n", v22->m_strCode);
  strcat_0(sData, sBuf);
  for ( j = 0; j < (unsigned __int8)byJewelNum; ++j )
  {
    v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pJewel[j].m_byTableCode, pJewel[j].m_wItemIndex);
    sprintf(sBuf, "\t- R %s\r\n", v24->m_strCode);
    strcat_0(sData, sBuf);
  }
  CMgrAvatorItemHistory::WriteFile(v32, pszFileName, sData);
}
