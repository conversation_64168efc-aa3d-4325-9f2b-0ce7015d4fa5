/*
 * Function: _std::vector_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0_____::_Insert_n_::_1_::dtor$1
 * Address: 0x14021ED60
 */

void __fastcall std::vector_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0_____::_Insert_n_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *)(a2 + 40));
}
