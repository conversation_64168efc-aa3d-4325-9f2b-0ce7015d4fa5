/*
 * Function: ?AttackSkillRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C17F0
 */

char __fastcall CNetworkEX::AttackSkillRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@11
  char *v7; // rax@13
  __int64 v8; // [sp+0h] [bp-98h]@1
  unsigned __int16 wSkillIndex; // [sp+20h] [bp-78h]@14
  unsigned __int16 wBulletSerial; // [sp+28h] [bp-70h]@14
  unsigned __int16 *pConsumeSerial; // [sp+30h] [bp-68h]@14
  unsigned __int16 wEffBtSerial; // [sp+38h] [bp-60h]@14
  char *v13; // [sp+40h] [bp-58h]@4
  CPlayer *v14; // [sp+48h] [bp-50h]@4
  CCharacter *pDst; // [sp+50h] [bp-48h]@8
  float pfAttackPos; // [sp+68h] [bp-30h]@14
  int v17; // [sp+6Ch] [bp-2Ch]@14
  float v18; // [sp+70h] [bp-28h]@14
  int v19; // [sp+84h] [bp-14h]@8
  CNetworkEX *v20; // [sp+A0h] [bp+8h]@1

  v20 = this;
  v3 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = pBuf;
  v14 = &g_Player + n;
  if ( !v14->m_bOper || v14->m_pmTrd.bDTradeMode || v14->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)*v13, *(_WORD *)(v13 + 1));
    v19 = (signed int)(unsigned __int8)v13[3] > 4;
    if ( v19 && v13[3] != 2 && v13[3] != 3 )
    {
      v6 = CPlayerDB::GetCharNameA(&v14->m_Param);
      CLogFile::Write(
        &v20->m_LogFile,
        "odd.. %s: AttackSkillRequest()..  if(pRecv->byEffectCode != effect_code_skill && pRecv->byEffectCode != effect_code_class)",
        v6);
      result = 0;
    }
    else if ( CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)v13[3], *((_WORD *)v13 + 2)) )
    {
      pfAttackPos = (float)*((_WORD *)v13 + 4);
      v17 = 0;
      v18 = (float)*((_WORD *)v13 + 5);
      wEffBtSerial = *((_WORD *)v13 + 9);
      pConsumeSerial = (unsigned __int16 *)(v13 + 12);
      wBulletSerial = *((_WORD *)v13 + 3);
      wSkillIndex = *((_WORD *)v13 + 2);
      CPlayer::pc_PlayAttack_Skill(
        v14,
        pDst,
        &pfAttackPos,
        v13[3],
        wSkillIndex,
        wBulletSerial,
        (unsigned __int16 *)v13 + 6,
        wEffBtSerial);
      result = 1;
    }
    else
    {
      v7 = CPlayerDB::GetCharNameA(&v14->m_Param);
      CLogFile::Write(
        &v20->m_LogFile,
        "odd.. %s: AttackSkillRequest()..  if(!g_Main.m_tblEffectData[pRecv->byEffectCode].GetRecord(pRecv->wSkillIndex))",
        v7);
      result = 0;
    }
  }
  return result;
}
