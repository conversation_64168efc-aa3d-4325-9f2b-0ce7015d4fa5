/*
 * Function: ?_db_Update_Data_For_Post_Send@CMainThread@@AEAAEPEAD@Z
 * Address: 0x1401B8FD0
 */

char __fastcall CMainThread::_db_Update_Data_For_Post_Send(CMainThread *this, char *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *pSzQuery; // [sp+20h] [bp-28h]@6
  char *v7; // [sp+30h] [bp-18h]@4
  CMainThread *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pSheet;
  if ( CRFWorldDatabase::Update_Gold(v8->m_pWorldDB, *(_DWORD *)pSheet, *((_DWORD *)pSheet + 1)) )
  {
    pSzQuery = pszQuery;
    if ( CMainThread::_db_Update_Inven(
           v8,
           *(_DWORD *)v7,
           *((_AVATOR_DATA **)v7 + 1),
           *((_AVATOR_DATA **)v7 + 2),
           pszQuery) )
    {
      if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->m_pWorldDB->vfptr, pszQuery, 1) )
        result = 0;
      else
        result = 24;
    }
    else
    {
      CLogFile::Write(&v8->m_logSystemError, "_db_Update_Inven..failed ..", *(_DWORD *)v7);
      result = 24;
    }
  }
  else
  {
    CLogFile::Write(&v8->m_logSystemError, "_db_Update_Glod(sr:%d) ..failed ..", *(_DWORD *)v7);
    result = 24;
  }
  return result;
}
