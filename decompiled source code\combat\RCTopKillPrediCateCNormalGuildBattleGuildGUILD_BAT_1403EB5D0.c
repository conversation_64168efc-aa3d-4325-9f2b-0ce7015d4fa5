/*
 * Function: ??RCTopKillPrediCate@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NAEBVCNormalGuildBattleGuildMember@2@0@Z
 * Address: 0x1403EB5D0
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate::operator()(GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate *this, GUILD_BATTLE::CNormalGuildBattleGuildMember *lhs, GUILD_BATTLE::CNormalGuildBattleGuildMember *rhs)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v9; // [sp+50h] [bp+18h]@1

  v9 = rhs;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetKillCount(lhs);
  v5 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetKillCount(v9);
  return v8 < (unsigned __int16)v5;
}
