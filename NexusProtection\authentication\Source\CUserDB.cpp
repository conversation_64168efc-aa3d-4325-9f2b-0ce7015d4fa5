#include "../Headers/CUserDB.h"
#include "../Headers/CAsyncLogInfo.h"
#include <filesystem>
#include <fstream>
#include <random>
#include <algorithm>
#include <sstream>
#include <iomanip>

namespace NexusProtection::Authentication {

    // PasswordSecurity implementation
    std::string PasswordSecurity::HashPassword(const std::string& password, const std::string& salt) {
        // Simple hash implementation - in production, use a proper cryptographic hash
        std::hash<std::string> hasher;
        std::string saltedPassword = password + salt;
        size_t hashValue = hasher(saltedPassword);
        
        std::stringstream ss;
        ss << std::hex << hashValue;
        return ss.str();
    }

    bool PasswordSecurity::VerifyPassword(const std::string& password, const std::string& hashedPassword) {
        // Extract salt from hashed password (simplified implementation)
        std::string salt = "default_salt"; // In production, extract from hashedPassword
        std::string computedHash = HashPassword(password, salt);
        return computedHash == hashedPassword;
    }

    std::string PasswordSecurity::GenerateSalt() {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 255);
        
        std::stringstream ss;
        for (int i = 0; i < 16; ++i) {
            ss << std::hex << std::setw(2) << std::setfill('0') << dis(gen);
        }
        
        return ss.str();
    }

    bool PasswordSecurity::IsPasswordStrong(const std::string& password) {
        if (password.length() < 8) {
            return false;
        }
        
        bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;
        
        for (char c : password) {
            if (std::isupper(c)) hasUpper = true;
            else if (std::islower(c)) hasLower = true;
            else if (std::isdigit(c)) hasDigit = true;
            else if (std::ispunct(c)) hasSpecial = true;
        }
        
        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    std::string PasswordSecurity::GenerateRandomPassword(size_t length) {
        const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, chars.size() - 1);
        
        std::string password;
        password.reserve(length);
        
        for (size_t i = 0; i < length; ++i) {
            password += chars[dis(gen)];
        }
        
        return password;
    }

    // Global instance
    static std::unique_ptr<CUserDB> s_userDBInstance;
    static std::mutex s_userDBMutex;

    CUserDB::CUserDB() {
        m_databasePath = "./Database/";
        m_configPath = "./Config/UserDB.ini";
        m_statistics.startTime = std::chrono::steady_clock::now();
    }

    CUserDB::~CUserDB() {
        Shutdown();
    }

    bool CUserDB::Initialize() {
        std::lock_guard<std::mutex> lock(m_accountsMutex);
        
        if (m_isInitialized) {
            return true;
        }

        try {
            // Load configuration
            if (!LoadConfiguration()) {
                return false;
            }

            // Create database directory
            std::filesystem::create_directories(m_databasePath);

            // Load accounts from database
            if (!LoadFromDatabase()) {
                LogAccountEvent("Failed to load accounts from database", "");
                // Continue with empty database
            }

            m_isInitialized = true;
            
            LogAccountEvent("User database initialized successfully", "");
            return true;
        }
        catch (const std::exception& e) {
            LogAccountEvent("Failed to initialize user database: " + std::string(e.what()), "");
            return false;
        }
    }

    void CUserDB::Shutdown() {
        std::lock_guard<std::mutex> accountsLock(m_accountsMutex);
        std::lock_guard<std::mutex> billingLock(m_billingMutex);
        std::lock_guard<std::mutex> trunkLock(m_trunkPasswordMutex);
        
        if (!m_isInitialized) {
            return;
        }

        // Save accounts to database
        SaveToDatabase();

        // Clear all data
        m_accounts.clear();
        m_serialToName.clear();
        m_trunkPasswords.clear();
        m_billingInfo.clear();

        m_isInitialized = false;
        
        LogAccountEvent("User database shut down", "");
    }

    bool CUserDB::LoadConfiguration() {
        // Load configuration from INI file
        // For now, we'll use default values
        m_maxAccountNameLength = MAX_ACCOUNT_NAME_LENGTH;
        m_maxPasswordLength = MAX_PASSWORD_LENGTH;
        m_requireStrongPasswords = true;
        m_enableAccountLocking = true;
        m_maxLoginAttempts = 3;
        
        return true;
    }

    bool CUserDB::CreateAccount(const std::string& accountName, const std::string& password,
                               BillingType billingType) {
        if (!ValidateAccountName(accountName) || !ValidatePassword(password)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_accountsMutex);

        // Check if account already exists
        if (m_accounts.find(accountName) != m_accounts.end()) {
            LogAccountEvent("Account creation failed - account already exists", accountName);
            return false;
        }

        // Check password policy
        if (m_requireStrongPasswords && !CheckPasswordPolicy(password)) {
            LogAccountEvent("Account creation failed - password policy violation", accountName);
            return false;
        }

        try {
            AccountInfo account;
            account.accountSerial = GenerateAccountSerial();
            account.accountName = accountName;
            account.hashedPassword = PasswordSecurity::HashPassword(password);
            account.billingType = billingType;
            account.isActive = true;
            account.creationDate = std::chrono::system_clock::now();
            account.lastLogin = std::chrono::system_clock::time_point{};
            account.loginCount = 0;

            m_accounts[accountName] = account;
            m_serialToName[account.accountSerial] = accountName;

            std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
            ++m_statistics.totalAccounts;
            ++m_statistics.activeAccounts;

            LogAccountEvent("Account created successfully", accountName);
            return true;
        }
        catch (const std::exception& e) {
            LogAccountEvent("Account creation failed: " + std::string(e.what()), accountName);
            return false;
        }
    }

    bool CUserDB::DeleteAccount(const std::string& accountName) {
        std::lock_guard<std::mutex> accountsLock(m_accountsMutex);
        std::lock_guard<std::mutex> billingLock(m_billingMutex);
        std::lock_guard<std::mutex> trunkLock(m_trunkPasswordMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        uint32_t accountSerial = it->second.accountSerial;

        // Remove from all maps
        m_accounts.erase(it);
        m_serialToName.erase(accountSerial);
        m_trunkPasswords.erase(accountName);
        m_billingInfo.erase(accountName);

        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        if (m_statistics.totalAccounts > 0) {
            --m_statistics.totalAccounts;
        }
        if (m_statistics.activeAccounts > 0) {
            --m_statistics.activeAccounts;
        }

        LogAccountEvent("Account deleted", accountName);
        return true;
    }

    bool CUserDB::UpdateAccount(const AccountInfo& accountInfo) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountInfo.accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        it->second = accountInfo;
        LogAccountEvent("Account updated", accountInfo.accountName);
        return true;
    }

    AccountInfo CUserDB::GetAccount(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            return it->second;
        }

        return AccountInfo{}; // Return empty account info
    }

    AccountInfo CUserDB::GetAccount(uint32_t accountSerial) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_serialToName.find(accountSerial);
        if (it != m_serialToName.end()) {
            return GetAccount(it->second);
        }

        return AccountInfo{}; // Return empty account info
    }

    bool CUserDB::AccountExists(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);
        return m_accounts.find(accountName) != m_accounts.end();
    }

    AuthenticationResult CUserDB::AuthenticateUser(const std::string& accountName, const std::string& password) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            LogAccountEvent("Authentication failed - account not found", accountName);
            return AuthenticationResult::InvalidCredentials;
        }

        const auto& account = it->second;

        // Check if account is active
        if (!account.isActive) {
            LogAccountEvent("Authentication failed - account inactive", accountName);
            return AuthenticationResult::AccountLocked;
        }

        // Verify password
        if (!PasswordSecurity::VerifyPassword(password, account.hashedPassword)) {
            LogAccountEvent("Authentication failed - invalid password", accountName);
            return AuthenticationResult::InvalidCredentials;
        }

        LogAccountEvent("Authentication successful", accountName);
        return AuthenticationResult::Success;
    }

    bool CUserDB::ValidateCredentials(const std::string& accountName, const std::string& password) const {
        return AuthenticateUser(accountName, password) == AuthenticationResult::Success;
    }

    bool CUserDB::IsAccountActive(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            return it->second.isActive;
        }

        return false;
    }

    bool CUserDB::IsAccountLocked(const std::string& accountName) const {
        return !IsAccountActive(accountName);
    }

    bool CUserDB::ChangePassword(const std::string& accountName, const std::string& oldPassword,
                                const std::string& newPassword) {
        if (!ValidatePassword(newPassword)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        // Verify old password
        if (!PasswordSecurity::VerifyPassword(oldPassword, it->second.hashedPassword)) {
            LogAccountEvent("Password change failed - invalid old password", accountName);
            return false;
        }

        // Check new password policy
        if (m_requireStrongPasswords && !CheckPasswordPolicy(newPassword)) {
            LogAccountEvent("Password change failed - password policy violation", accountName);
            return false;
        }

        // Update password
        it->second.hashedPassword = PasswordSecurity::HashPassword(newPassword);
        
        LogAccountEvent("Password changed successfully", accountName);
        return true;
    }

    bool CUserDB::ResetPassword(const std::string& accountName, const std::string& newPassword) {
        if (!ValidatePassword(newPassword)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        // Check password policy
        if (m_requireStrongPasswords && !CheckPasswordPolicy(newPassword)) {
            LogAccountEvent("Password reset failed - password policy violation", accountName);
            return false;
        }

        // Update password
        it->second.hashedPassword = PasswordSecurity::HashPassword(newPassword);
        
        LogAccountEvent("Password reset successfully", accountName);
        return true;
    }

    bool CUserDB::UpdateTrunkPassword(const std::string& accountName, const std::string& trunkPassword) {
        if (!AccountExists(accountName)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_trunkPasswordMutex);

        m_trunkPasswords[accountName] = PasswordSecurity::HashPassword(trunkPassword);

        LogAccountEvent("Trunk password updated", accountName);
        return true;
    }

    std::string CUserDB::GetTrunkPassword(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_trunkPasswordMutex);

        auto it = m_trunkPasswords.find(accountName);
        if (it != m_trunkPasswords.end()) {
            return it->second;
        }

        return "";
    }

    bool CUserDB::LockAccount(const std::string& accountName, const std::string& reason) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        it->second.isActive = false;

        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        if (m_statistics.activeAccounts > 0) {
            --m_statistics.activeAccounts;
        }
        ++m_statistics.lockedAccounts;

        LogAccountEvent("Account locked" + (reason.empty() ? "" : " - " + reason), accountName);
        return true;
    }

    bool CUserDB::UnlockAccount(const std::string& accountName) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        it->second.isActive = true;

        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        ++m_statistics.activeAccounts;
        if (m_statistics.lockedAccounts > 0) {
            --m_statistics.lockedAccounts;
        }

        LogAccountEvent("Account unlocked", accountName);
        return true;
    }

    bool CUserDB::SuspendAccount(const std::string& accountName, const std::chrono::system_clock::time_point& until) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        it->second.isActive = false;

        std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
        if (m_statistics.activeAccounts > 0) {
            --m_statistics.activeAccounts;
        }
        ++m_statistics.suspendedAccounts;

        auto time_t = std::chrono::system_clock::to_time_t(until);
        LogAccountEvent("Account suspended until " + std::string(std::ctime(&time_t)), accountName);
        return true;
    }

    bool CUserDB::ActivateAccount(const std::string& accountName) {
        return UnlockAccount(accountName);
    }

    void CUserDB::RecordLogin(const std::string& accountName, const std::string& clientIP) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            it->second.lastLogin = std::chrono::system_clock::now();
            ++it->second.loginCount;

            LogAccountEvent("Login recorded from IP: " + clientIP, accountName);
        }
    }

    void CUserDB::RecordLogout(const std::string& accountName) {
        LogAccountEvent("Logout recorded", accountName);
    }

    void CUserDB::UpdateLastActivity(const std::string& accountName) {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            it->second.lastLogin = std::chrono::system_clock::now();
        }
    }

    std::chrono::system_clock::time_point CUserDB::GetLastLogin(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            return it->second.lastLogin;
        }

        return std::chrono::system_clock::time_point{};
    }

    uint32_t CUserDB::GetLoginCount(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            return it->second.loginCount;
        }

        return 0;
    }

    bool CUserDB::UpdateBillingType(const std::string& accountName, BillingType billingType) {
        std::lock_guard<std::mutex> accountsLock(m_accountsMutex);
        std::lock_guard<std::mutex> billingLock(m_billingMutex);

        auto it = m_accounts.find(accountName);
        if (it == m_accounts.end()) {
            return false;
        }

        it->second.billingType = billingType;

        LogAccountEvent("Billing type updated to " + BillingTypeToString(billingType), accountName);
        return true;
    }

    BillingType CUserDB::GetBillingType(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        auto it = m_accounts.find(accountName);
        if (it != m_accounts.end()) {
            return it->second.billingType;
        }

        return BillingType::Free;
    }

    bool CUserDB::SetBillingInfo(const std::string& accountName, const BillingInfo& billingInfo) {
        if (!AccountExists(accountName)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_billingMutex);

        m_billingInfo[accountName] = billingInfo;

        LogAccountEvent("Billing info updated", accountName);
        return true;
    }

    BillingInfo CUserDB::GetBillingInfo(const std::string& accountName) const {
        std::lock_guard<std::mutex> lock(m_billingMutex);

        auto it = m_billingInfo.find(accountName);
        if (it != m_billingInfo.end()) {
            return it->second;
        }

        return BillingInfo{}; // Return empty billing info
    }

    bool CUserDB::SaveToDatabase() {
        try {
            std::string filePath = m_databasePath + "accounts.dat";
            std::ofstream file(filePath, std::ios::binary);

            if (!file.is_open()) {
                return false;
            }

            // Write account count
            size_t accountCount = m_accounts.size();
            file.write(reinterpret_cast<const char*>(&accountCount), sizeof(accountCount));

            // Write accounts
            for (const auto& pair : m_accounts) {
                const auto& account = pair.second;

                // Write account data
                file.write(reinterpret_cast<const char*>(&account.accountSerial), sizeof(account.accountSerial));

                size_t nameLen = account.accountName.length();
                file.write(reinterpret_cast<const char*>(&nameLen), sizeof(nameLen));
                file.write(account.accountName.c_str(), nameLen);

                size_t passLen = account.hashedPassword.length();
                file.write(reinterpret_cast<const char*>(&passLen), sizeof(passLen));
                file.write(account.hashedPassword.c_str(), passLen);

                file.write(reinterpret_cast<const char*>(&account.billingType), sizeof(account.billingType));
                file.write(reinterpret_cast<const char*>(&account.isActive), sizeof(account.isActive));
                file.write(reinterpret_cast<const char*>(&account.loginCount), sizeof(account.loginCount));

                // Write timestamps
                auto creationTime = account.creationDate.time_since_epoch().count();
                auto lastLoginTime = account.lastLogin.time_since_epoch().count();
                file.write(reinterpret_cast<const char*>(&creationTime), sizeof(creationTime));
                file.write(reinterpret_cast<const char*>(&lastLoginTime), sizeof(lastLoginTime));
            }

            file.close();
            LogAccountEvent("Database saved successfully", "");
            return true;
        }
        catch (const std::exception& e) {
            LogAccountEvent("Failed to save database: " + std::string(e.what()), "");
            return false;
        }
    }

    bool CUserDB::LoadFromDatabase() {
        try {
            std::string filePath = m_databasePath + "accounts.dat";
            std::ifstream file(filePath, std::ios::binary);

            if (!file.is_open()) {
                // File doesn't exist, start with empty database
                return true;
            }

            // Read account count
            size_t accountCount;
            file.read(reinterpret_cast<char*>(&accountCount), sizeof(accountCount));

            // Read accounts
            for (size_t i = 0; i < accountCount; ++i) {
                AccountInfo account;

                // Read account data
                file.read(reinterpret_cast<char*>(&account.accountSerial), sizeof(account.accountSerial));

                size_t nameLen;
                file.read(reinterpret_cast<char*>(&nameLen), sizeof(nameLen));
                account.accountName.resize(nameLen);
                file.read(&account.accountName[0], nameLen);

                size_t passLen;
                file.read(reinterpret_cast<char*>(&passLen), sizeof(passLen));
                account.hashedPassword.resize(passLen);
                file.read(&account.hashedPassword[0], passLen);

                file.read(reinterpret_cast<char*>(&account.billingType), sizeof(account.billingType));
                file.read(reinterpret_cast<char*>(&account.isActive), sizeof(account.isActive));
                file.read(reinterpret_cast<char*>(&account.loginCount), sizeof(account.loginCount));

                // Read timestamps
                int64_t creationTime, lastLoginTime;
                file.read(reinterpret_cast<char*>(&creationTime), sizeof(creationTime));
                file.read(reinterpret_cast<char*>(&lastLoginTime), sizeof(lastLoginTime));

                account.creationDate = std::chrono::system_clock::time_point(std::chrono::system_clock::duration(creationTime));
                account.lastLogin = std::chrono::system_clock::time_point(std::chrono::system_clock::duration(lastLoginTime));

                // Add to maps
                m_accounts[account.accountName] = account;
                m_serialToName[account.accountSerial] = account.accountName;

                // Update next serial
                if (account.accountSerial >= m_nextAccountSerial) {
                    m_nextAccountSerial = account.accountSerial + 1;
                }
            }

            file.close();

            // Update statistics
            std::lock_guard<std::mutex> statsLock(m_statisticsMutex);
            m_statistics.totalAccounts = m_accounts.size();
            m_statistics.activeAccounts = std::count_if(m_accounts.begin(), m_accounts.end(),
                [](const auto& pair) { return pair.second.isActive; });

            LogAccountEvent("Database loaded successfully - " + std::to_string(m_accounts.size()) + " accounts", "");
            return true;
        }
        catch (const std::exception& e) {
            LogAccountEvent("Failed to load database: " + std::string(e.what()), "");
            return false;
        }
    }

    bool CUserDB::BackupDatabase(const std::string& backupPath) {
        std::string targetPath = backupPath.empty() ? (m_databasePath + "backup/") : backupPath;

        try {
            std::filesystem::create_directories(targetPath);

            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            auto tm = *std::localtime(&time_t);

            std::stringstream ss;
            ss << targetPath << "accounts_backup_"
               << std::put_time(&tm, "%Y%m%d_%H%M%S") << ".dat";

            std::string backupFile = ss.str();
            std::filesystem::copy_file(m_databasePath + "accounts.dat", backupFile);

            LogAccountEvent("Database backup created: " + backupFile, "");
            return true;
        }
        catch (const std::exception& e) {
            LogAccountEvent("Failed to backup database: " + std::string(e.what()), "");
            return false;
        }
    }

    bool CUserDB::RestoreDatabase(const std::string& backupPath) {
        try {
            std::filesystem::copy_file(backupPath, m_databasePath + "accounts.dat",
                                     std::filesystem::copy_options::overwrite_existing);

            // Reload from restored file
            m_accounts.clear();
            m_serialToName.clear();
            LoadFromDatabase();

            LogAccountEvent("Database restored from: " + backupPath, "");
            return true;
        }
        catch (const std::exception& e) {
            LogAccountEvent("Failed to restore database: " + std::string(e.what()), "");
            return false;
        }
    }

    size_t CUserDB::GetAccountCount() const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);
        return m_accounts.size();
    }

    size_t CUserDB::GetActiveAccountCount() const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);
        return std::count_if(m_accounts.begin(), m_accounts.end(),
            [](const auto& pair) { return pair.second.isActive; });
    }

    std::vector<std::string> CUserDB::GetAccountList() const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        std::vector<std::string> accountList;
        accountList.reserve(m_accounts.size());

        for (const auto& pair : m_accounts) {
            accountList.push_back(pair.first);
        }

        return accountList;
    }

    std::vector<AccountInfo> CUserDB::GetAccountsByBillingType(BillingType billingType) const {
        std::lock_guard<std::mutex> lock(m_accountsMutex);

        std::vector<AccountInfo> accounts;

        for (const auto& pair : m_accounts) {
            if (pair.second.billingType == billingType) {
                accounts.push_back(pair.second);
            }
        }

        return accounts;
    }

    bool CUserDB::LoadAccountsFromFile() {
        return LoadFromDatabase();
    }

    bool CUserDB::SaveAccountsToFile() {
        return SaveToDatabase();
    }

    bool CUserDB::ValidateAccountName(const std::string& accountName) const {
        if (accountName.empty() || accountName.length() > m_maxAccountNameLength) {
            return false;
        }

        // Check for reserved names
        if (IsAccountNameReserved(accountName)) {
            return false;
        }

        // Check for valid characters (alphanumeric and underscore)
        return std::all_of(accountName.begin(), accountName.end(),
            [](char c) { return std::isalnum(c) || c == '_'; });
    }

    bool CUserDB::ValidatePassword(const std::string& password) const {
        return !password.empty() && password.length() <= m_maxPasswordLength;
    }

    uint32_t CUserDB::GenerateAccountSerial() {
        return m_nextAccountSerial++;
    }

    void CUserDB::CleanupExpiredAccounts() {
        // This would remove accounts that haven't been used for a long time
        // Implementation depends on business requirements
    }

    std::string CUserDB::GetAccountFilePath(const std::string& accountName) const {
        return m_databasePath + accountName + ".dat";
    }

    bool CUserDB::CheckPasswordPolicy(const std::string& password) const {
        return PasswordSecurity::IsPasswordStrong(password);
    }

    void CUserDB::LogAccountEvent(const std::string& event, const std::string& accountName) {
        auto& asyncLogger = GetAsyncLogger();
        std::string message = accountName.empty() ? event : (accountName + ": " + event);
        asyncLogger.WriteAuthLog(message);
    }

    bool CUserDB::IsAccountNameReserved(const std::string& accountName) const {
        static const std::vector<std::string> reservedNames = {
            "admin", "administrator", "root", "system", "guest", "test", "demo"
        };

        std::string lowerName = accountName;
        std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);

        return std::find(reservedNames.begin(), reservedNames.end(), lowerName) != reservedNames.end();
    }

    // Legacy C interface implementations
    bool CUserDB::Update_TrunkPassword(char* accountName, char* trunkPassword) {
        if (!accountName || !trunkPassword) {
            return false;
        }
        return UpdateTrunkPassword(std::string(accountName), std::string(trunkPassword));
    }

    char* CUserDB::Get_TrunkPassword(char* accountName) {
        if (!accountName) {
            return nullptr;
        }

        static std::string result;
        result = GetTrunkPassword(std::string(accountName));
        return const_cast<char*>(result.c_str());
    }

    bool CUserDB::Authenticate_User(char* accountName, char* password) {
        if (!accountName || !password) {
            return false;
        }
        return AuthenticateUser(std::string(accountName), std::string(password)) == AuthenticationResult::Success;
    }

    // CUserDBFactory implementation
    std::unique_ptr<CUserDB> CUserDBFactory::CreateUserDB() {
        return std::make_unique<CUserDB>();
    }

    std::unique_ptr<CUserDB> CUserDBFactory::CreateUserDB(const std::string& databasePath) {
        auto userDB = std::make_unique<CUserDB>();
        userDB->SetDatabasePath(databasePath);
        return userDB;
    }

    bool CUserDBFactory::ValidateDatabasePath(const std::string& path) {
        try {
            std::filesystem::create_directories(path);
            return std::filesystem::exists(path) && std::filesystem::is_directory(path);
        }
        catch (const std::exception&) {
            return false;
        }
    }

    CUserDB& GetUserDB() {
        std::lock_guard<std::mutex> lock(s_userDBMutex);
        if (!s_userDBInstance) {
            s_userDBInstance = std::make_unique<CUserDB>();
        }
        return *s_userDBInstance;
    }

} // namespace NexusProtection::Authentication

// Legacy C interface implementation
extern "C" {
    CUserDB_Legacy* CUserDB_Create() {
        static CUserDB_Legacy s_legacyUserDB;

        // Initialize global pointer if needed
        if (!g_pUserDB) {
            g_pUserDB = &NexusProtection::Authentication::GetUserDB();
        }

        strncpy_s(s_legacyUserDB.m_szDatabasePath, sizeof(s_legacyUserDB.m_szDatabasePath),
                 g_pUserDB->GetDatabasePath().c_str(), _TRUNCATE);
        s_legacyUserDB.m_bInitialized = g_pUserDB->IsInitialized();
        s_legacyUserDB.m_dwAccountCount = static_cast<uint32_t>(g_pUserDB->GetAccountCount());
        s_legacyUserDB.m_dwNextSerial = 1; // This would be tracked internally

        return &s_legacyUserDB;
    }

    void CUserDB_Destroy(CUserDB_Legacy* userDB) {
        (void)userDB; // Suppress unused parameter warning
        // Modern implementation handles cleanup automatically
    }

    bool CUserDB_Initialize(CUserDB_Legacy* userDB) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB) {
            g_pUserDB = &NexusProtection::Authentication::GetUserDB();
        }
        return g_pUserDB->Initialize();
    }

    bool CUserDB_CreateAccount(CUserDB_Legacy* userDB, const char* accountName,
                              const char* password, uint8_t billingType) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB || !accountName || !password) {
            return false;
        }

        NexusProtection::Authentication::BillingType type =
            static_cast<NexusProtection::Authentication::BillingType>(billingType);

        return g_pUserDB->CreateAccount(accountName, password, type);
    }

    bool CUserDB_DeleteAccount(CUserDB_Legacy* userDB, const char* accountName) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB || !accountName) {
            return false;
        }
        return g_pUserDB->DeleteAccount(accountName);
    }

    bool CUserDB_AuthenticateUser(CUserDB_Legacy* userDB, const char* accountName,
                                 const char* password) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB || !accountName || !password) {
            return false;
        }
        return g_pUserDB->AuthenticateUser(accountName, password) ==
               NexusProtection::Authentication::AuthenticationResult::Success;
    }

    bool CUserDB_ChangePassword(CUserDB_Legacy* userDB, const char* accountName,
                               const char* oldPassword, const char* newPassword) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB || !accountName || !oldPassword || !newPassword) {
            return false;
        }
        return g_pUserDB->ChangePassword(accountName, oldPassword, newPassword);
    }

    bool CUserDB_Update_TrunkPassword(CUserDB_Legacy* userDB, char* accountName,
                                     char* trunkPassword) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB) {
            return false;
        }
        return g_pUserDB->Update_TrunkPassword(accountName, trunkPassword);
    }

    char* CUserDB_Get_TrunkPassword(CUserDB_Legacy* userDB, char* accountName) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB) {
            return nullptr;
        }
        return g_pUserDB->Get_TrunkPassword(accountName);
    }

    bool CUserDB_LockAccount(CUserDB_Legacy* userDB, const char* accountName) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB || !accountName) {
            return false;
        }
        return g_pUserDB->LockAccount(accountName);
    }

    bool CUserDB_UnlockAccount(CUserDB_Legacy* userDB, const char* accountName) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB || !accountName) {
            return false;
        }
        return g_pUserDB->UnlockAccount(accountName);
    }

    uint32_t CUserDB_GetAccountCount(CUserDB_Legacy* userDB) {
        (void)userDB; // Suppress unused parameter warning
        if (!g_pUserDB) {
            return 0;
        }
        return static_cast<uint32_t>(g_pUserDB->GetAccountCount());
    }
}

// Global legacy compatibility
NexusProtection::Authentication::CUserDB* g_pUserDB = nullptr;
