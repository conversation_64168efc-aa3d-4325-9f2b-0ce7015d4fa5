/*
 * Function: ??$_Umove@PEAVCUnmannedTraderSchedule@@@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAPEAVCUnmannedTraderSchedule@@PEAV2@00@Z
 * Address: 0x140396920
 */

CUnmannedTraderSchedule *__fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
