/*
 * Function: j_??0?$_Ranit@VCGuildBattleRewardItem@GUILD_BATTLE@@_JPEBV12@AEBV12@@std@@QEAA@XZ
 * Address: 0x1400085AD
 */

void __fastcall std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>(std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &> *this)
{
  std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>(this);
}
