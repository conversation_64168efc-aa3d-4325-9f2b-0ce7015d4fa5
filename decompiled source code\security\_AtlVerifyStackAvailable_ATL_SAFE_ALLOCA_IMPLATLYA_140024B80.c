/*
 * Function: ?_AtlVerifyStackAvailable@_ATL_SAFE_ALLOCA_IMPL@ATL@@YA_N_K@Z
 * Address: 0x140024B80
 */

char __fastcall ATL::_ATL_SAFE_ALLOCA_IMPL::_AtlVerifyStackAvailable(unsigned __int64 Size)
{
  char *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@8
  signed __int64 v4; // rax@10
  void *v5; // rsp@10
  void *v6; // rsp@10
  void *v7; // rsp@10
  char frame; // [sp+0h] [bp-30h]@1
  const wchar_t *v10; // [sp+20h] [bp-10h]@5
  char v11; // [sp+30h] [bp+0h]@4
  char v12; // [sp+34h] [bp+4h]@4
  __int64 v13; // [sp+50h] [bp+20h]@10
  unsigned __int64 ptResult; // [sp+58h] [bp+28h]@4
  int v15; // [sp+64h] [bp+34h]@4
  __int64 *v16; // [sp+68h] [bp+38h]@10
  _RTC_ALLOCA_NODE *pAllocaInfoList; // [sp+88h] [bp+58h]@4
  unsigned __int64 cbSize; // [sp+A8h] [bp+78h]@8
  _RTC_ALLOCA_NODE *pAllocaBase; // [sp+B0h] [bp+80h]@10
  unsigned __int64 v20; // [sp+C0h] [bp+90h]@4
  unsigned __int64 tLeft; // [sp+F0h] [bp+C0h]@1

  tLeft = Size;
  v1 = &frame;
  for ( i = 54i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 4;
  }
  v20 = (unsigned __int64)&v11 ^ _security_cookie;
  pAllocaInfoList = 0i64;
  v12 = 1;
  ptResult = 0i64;
  v15 = ATL::AtlAdd<unsigned __int64>(&ptResult, tLeft, 0x4000ui64);
  if ( v15 >= 0 )
  {
    cbSize = ptResult + 36;
    v3 = ptResult + 51;
    if ( ptResult + 51 <= ptResult + 36 )
      v3 = 1152921504606846960i64;
    v4 = v3 & 0xFFFFFFFFFFFFFFF0ui64;
    v5 = alloca(v4);
    v6 = alloca(v4);
    v7 = alloca((signed __int64)&v11);
    pAllocaBase = (_RTC_ALLOCA_NODE *)&v13;
    v16 = &v13;
  }
  else
  {
    v10 = L"0";
    if ( _CrtDbgReportW(
           2i64,
           L"C:\\Program Files (x86)\\Microsoft Visual Studio 8\\VC\\atlmfc\\include\\atlalloc.h",
           589i64,
           0i64) == 1 )
      __debugbreak();
    v12 = 0;
  }
  return v12;
}
