/*
 * Function: ?construct@?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@AEBU34@@Z
 * Address: 0x1405A5490
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::construct(__int64 a1, __int64 a2, __int64 a3)
{
  return std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(
           a2,
           a3);
}
