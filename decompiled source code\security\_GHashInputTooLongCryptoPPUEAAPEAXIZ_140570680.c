/*
 * Function: ??_GHashInputTooLong@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x140570680
 */

CryptoPP::HashInputTooLong *__fastcall CryptoPP::HashInputTooLong::`scalar deleting destructor'(CryptoPP::HashInputTooLong *a1, int a2)
{
  CryptoPP::HashInputTooLong *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::HashInputTooLong::~HashInputTooLong(a1);
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
