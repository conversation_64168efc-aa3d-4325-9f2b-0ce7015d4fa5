/*
 * CItemTradingSystem.cpp - Modern Item Trading and Storage System Implementation
 * Refactored from decompiled C item trading and storage functions
 * Provides comprehensive item trading, storage management, and item creation
 */

#include "../Headers/CItemTradingSystem.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CPlayer.h"
#include "../../network/Headers/CNetworkEX.h"

#include <algorithm>
#include <chrono>
#include <stdexcept>
#include <cstring>

// External references to legacy systems
extern "C" {
    // Legacy function declarations
    class CUnmannedTraderController;
    struct _unmannedtrader_buy_item_request_clzo;
    struct _a_trade_clear_item_request_clzo;
    struct _STORAGE_LIST;
    struct _STORAGE_POS_INDIV;
    struct _base_fld;
    
    extern CPlayer g_Player[];
    extern CUnmannedTraderController* CUnmannedTraderController_Instance();
    extern void CUnmannedTraderController_Buy(CUnmannedTraderController* pController, int playerIndex, 
                                             _unmannedtrader_buy_item_request_clzo* pRequest);
    extern void CUnmannedTraderController_CancelRegist(CUnmannedTraderController* pController, int playerIndex, 
                                                       _a_trade_clear_item_request_clzo* pRequest);
    extern uint32_t _STORAGE_LIST_TransInCon(void* pStorage, _STORAGE_LIST* pItem);
    extern char* CPlayerDB_GetCharNameA(void* pPlayerDB);
    extern bool IsOverLapItem(int tableCode);
    extern uint32_t GetItemDurPoint(int tableCode, uint32_t itemIndex);
    extern uint8_t GetItemKindCode(int tableCode);
    extern uint8_t GetDefItemUpgSocketNum(int tableCode, uint32_t itemIndex);
    extern uint32_t GetBitAfterSetLimSocket(uint8_t socketNum);
    extern uint16_t CPlayerDB_GetNewItemSerial(void* pPlayerDB);
    extern bool CPlayer_Emb_AddStorage(CPlayer* pPlayer, char storageCode, _STORAGE_LIST* pItem, 
                                      bool bEquipChange, bool bAdd);
    extern void CPlayer_SendMsg_MakeItemResult(CPlayer* pPlayer, int result);
    extern void CPlayer_SendMsg_DTradeAddInform(CPlayer* pPlayer, uint8_t position, _STORAGE_LIST* pItem, uint8_t amount);
    extern void CPlayer_SendMsg_DTradeAddResult(CPlayer* pPlayer, int result);
    extern void CPlayer_SendMsg_DTradeCloseInform(CPlayer* pPlayer, int reason);
    extern void CLogFile_Write(void* pLogFile, const char* format, ...);
}

namespace NexusProtection {
namespace Items {

/**
 * Constructor
 */
CItemTradingSystem::CItemTradingSystem() 
    : m_bDetailedLogging(false), m_bInitialized(false) {
    
    Logger::Debug("CItemTradingSystem::CItemTradingSystem - Item trading system created");
}

/**
 * Destructor
 */
CItemTradingSystem::~CItemTradingSystem() {
    try {
        Shutdown();
        Logger::Debug("CItemTradingSystem::~CItemTradingSystem - Item trading system destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Initialize trading system
 */
bool CItemTradingSystem::Initialize() {
    try {
        if (m_bInitialized) {
            Logger::Warning("CItemTradingSystem::Initialize - Already initialized");
            return true;
        }
        
        // Reset statistics
        ResetStatistics();
        
        m_bInitialized = true;
        Logger::Info("CItemTradingSystem::Initialize - Item trading system initialized");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::Initialize - Exception: %s", e.what());
        return false;
    }
}

/**
 * Shutdown trading system
 */
void CItemTradingSystem::Shutdown() {
    try {
        if (!m_bInitialized) {
            return;
        }
        
        m_bInitialized = false;
        Logger::Info("CItemTradingSystem::Shutdown - Item trading system shutdown");
        
    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::Shutdown - Exception: %s", e.what());
    }
}

/**
 * Process trade buy item request
 * Refactored from: ATradeBuyItemRequestCNetworkEXAEAA_NHPEADZ_1401D3A20.c
 */
TradingOperationDetails CItemTradingSystem::ProcessTradeBuyRequest(CNetworkEX* pNetwork, int playerIndex, char* pBuffer) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    if (!pNetwork || !pBuffer || playerIndex < 0) {
        return CreateTradingResult(TradingResult::InvalidPlayer, startTime, "Invalid parameters");
    }
    
    try {
        // Initialize stack variables (original lines 21-26)
        int stackInit[12];
        for (int i = 0; i < 12; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Get request and player (original lines 27-28)
        _unmannedtrader_buy_item_request_clzo* pRequest = 
            reinterpret_cast<_unmannedtrader_buy_item_request_clzo*>(pBuffer);
        CPlayer* pPlayer = &g_Player[playerIndex];
        
        // Validate player state (original lines 29-32)
        if (!pPlayer->m_bOper || pPlayer->m_pmTrd.bDTradeMode || pPlayer->m_bCorpse) {
            return CreateTradingResult(TradingResult::TradeNotAllowed, startTime, "Player cannot trade");
        }
        
        // Validate request parameters (original lines 33-39)
        if (pRequest->byNum > 10) {
            char* playerName = CPlayerDB_GetCharNameA(&pPlayer->m_Param);
            Logger::Warning("CItemTradingSystem::ProcessTradeBuyRequest - Invalid request from %s: byNum > 10", 
                           playerName ? playerName : "Unknown");
            return CreateTradingResult(TradingResult::InvalidItem, startTime, "Invalid item count");
        }
        
        // Process unmanned trader buy (original lines 35-37)
        if (!ProcessUnmannedTraderBuy(playerIndex, pRequest)) {
            return CreateTradingResult(TradingResult::SystemError, startTime, "Unmanned trader buy failed");
        }
        
        // Update statistics
        m_stats.RecordTrade(true);
        
        TradingOperationDetails result = CreateTradingResult(TradingResult::Success, startTime);
        result.transactionId = GenerateTransactionId();
        result.context.pBuyer = pPlayer;
        result.context.isUnmannedTrade = true;
        
        LogTradingOperation(result);
        
        if (m_tradingCallback) {
            m_tradingCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordTrade(false);
        Logger::Error("CItemTradingSystem::ProcessTradeBuyRequest - Exception: %s", e.what());
        return CreateTradingResult(TradingResult::SystemError, startTime, 
                                 std::string("Exception: ") + e.what());
    }
}

/**
 * Process trade clear item request
 * Refactored from: ATradeClearItemRequestCNetworkEXAEAA_NHPEADZ_1401D3980.c
 */
TradingOperationDetails CItemTradingSystem::ProcessTradeClearRequest(CNetworkEX* pNetwork, int playerIndex, char* pBuffer) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    if (!pNetwork || !pBuffer || playerIndex < 0) {
        return CreateTradingResult(TradingResult::InvalidPlayer, startTime, "Invalid parameters");
    }
    
    try {
        // Initialize stack variables (original lines 18-23)
        int stackInit[12];
        for (int i = 0; i < 12; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Get request and player (original lines 24-25)
        _a_trade_clear_item_request_clzo* pRequest = 
            reinterpret_cast<_a_trade_clear_item_request_clzo*>(pBuffer);
        CPlayer* pPlayer = &g_Player[playerIndex];
        
        // Validate player state (original line 26)
        if (pPlayer->m_bOper && !pPlayer->m_pmTrd.bDTradeMode) {
            // Process unmanned trader clear (original lines 28-29)
            if (!ProcessUnmannedTraderClear(playerIndex, pRequest)) {
                return CreateTradingResult(TradingResult::SystemError, startTime, "Unmanned trader clear failed");
            }
        }
        
        // Update statistics
        m_stats.RecordTrade(true);
        
        TradingOperationDetails result = CreateTradingResult(TradingResult::Success, startTime);
        result.transactionId = GenerateTransactionId();
        result.context.pBuyer = pPlayer;
        result.context.isUnmannedTrade = true;
        
        LogTradingOperation(result);
        
        if (m_tradingCallback) {
            m_tradingCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordTrade(false);
        Logger::Error("CItemTradingSystem::ProcessTradeClearRequest - Exception: %s", e.what());
        return CreateTradingResult(TradingResult::SystemError, startTime, 
                                 std::string("Exception: ") + e.what());
    }
}

/**
 * Add item to storage
 * Refactored from: Emb_AddStorageCPlayerQEAAPEAU_db_con_STORAGE_LISTE_140057D90.c
 */
StorageOperationDetails CItemTradingSystem::AddItemToStorage(const StorageContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        if (!ValidateStorageContext(context)) {
            return CreateStorageResult(StorageResult::InvalidStorage, startTime, "Invalid storage context");
        }
        
        // Initialize stack variables (original lines 43-48)
        int stackInit[32];
        for (int i = 0; i < 32; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Validate storage code (original line 49)
        if (context.storageCode >= 8) {
            return CreateStorageResult(StorageResult::InvalidStorage, startTime, "Invalid storage code");
        }
        
        // Transfer item to storage (original line 51)
        uint32_t position = TransferItemToStorage(context.pPlayer, context.storageCode, context.pItem);
        if (position == 255) {
            // Log error (original lines 54-61)
            char* playerName = CPlayerDB_GetCharNameA(&context.pPlayer->m_Param);
            Logger::Error("CItemTradingSystem::AddItemToStorage - TransInCon error for player %s, storage: %d", 
                         playerName ? playerName : "Unknown", context.storageCode);
            return CreateStorageResult(StorageResult::StorageFull, startTime, "Storage transfer failed");
        }
        
        // Get storage item (original line 65)
        _STORAGE_LIST* pStorageItem = &context.pPlayer->m_Param.m_pStoragePtr[context.storageCode]->m_pStorageList[position];
        
        // Update statistics
        m_stats.storageOperations++;
        
        StorageOperationDetails result = CreateStorageResult(StorageResult::Success, startTime);
        result.context = context;
        result.pResultItem = pStorageItem;
        
        Logger::Debug("CItemTradingSystem::AddItemToStorage - Item added to storage %d at position %d", 
                     context.storageCode, position);
        
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::AddItemToStorage - Exception: %s", e.what());
        return CreateStorageResult(StorageResult::SystemError, startTime,
                                 std::string("Exception: ") + e.what());
    }
}

/**
 * Create item for player
 * Refactored from: pc_MakeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIVGE0Z_1400AE750.c
 */
ItemCreationOperationDetails CItemTradingSystem::CreateItemForPlayer(const ItemCreationContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        if (!ValidateItemCreationContext(context)) {
            return CreateItemCreationResult(ItemCreationResult::InvalidMaterials, startTime, "Invalid creation context");
        }

        // Create storage item (original lines 332-346)
        _STORAGE_LIST* pCreatedItem = CreateStorageItem(context);
        if (!pCreatedItem) {
            CPlayer_SendMsg_MakeItemResult(context.pPlayer, -1);
            return CreateItemCreationResult(ItemCreationResult::CreationFailed, startTime, "Failed to create storage item");
        }

        // Add item to player storage (original line 347)
        if (!CPlayer_Emb_AddStorage(context.pPlayer, 0, pCreatedItem, false, true)) {
            CPlayer_SendMsg_MakeItemResult(context.pPlayer, -1);
            return CreateItemCreationResult(ItemCreationResult::InventoryFull, startTime, "Failed to add item to storage");
        }

        // Send success result
        CPlayer_SendMsg_MakeItemResult(context.pPlayer, 1);

        // Update statistics
        m_stats.itemsCreated++;

        ItemCreationOperationDetails result = CreateItemCreationResult(ItemCreationResult::Success, startTime);
        result.context = context;
        result.pCreatedItem = pCreatedItem;

        Logger::Debug("CItemTradingSystem::CreateItemForPlayer - Item created successfully for player");

        return result;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::CreateItemForPlayer - Exception: %s", e.what());
        return CreateItemCreationResult(ItemCreationResult::SystemError, startTime,
                                      std::string("Exception: ") + e.what());
    }
}

/**
 * Process direct trade add request
 * Refactored from: pc_DTradeAddRequestCPlayerQEAAXEEKEZ_1400F4080.c
 */
TradingOperationDetails CItemTradingSystem::ProcessDirectTradeAdd(CPlayer* pPlayer, uint8_t storageCode,
                                                                 uint8_t position, uint32_t serial, uint8_t amount) {
    auto startTime = std::chrono::high_resolution_clock::now();

    if (!pPlayer) {
        return CreateTradingResult(TradingResult::InvalidPlayer, startTime, "Invalid player");
    }

    try {
        // Get item from storage
        _STORAGE_LIST* pItem = nullptr; // In real implementation, get from player storage

        if (!pItem) {
            return CreateTradingResult(TradingResult::ItemNotFound, startTime, "Item not found in storage");
        }

        // Check if item is overlap type (original line 118)
        if (IsOverlapItem(pItem->m_byTableCode)) {
            if (pItem->m_dwDur == amount) {
                pPlayer->m_pmTrd.byEmptyInvenNum++;
            }
        } else {
            pPlayer->m_pmTrd.byEmptyInvenNum++;
        }

        // Update trade parameters (original lines 128-129)
        pPlayer->m_pmTrd.bySellItemNum++;
        // In real implementation: _DTRADE_ITEM::SetData(&pPlayer->m_pmTrd.DItemNode[position], ?, serial, amount);

        // Send trade messages (original lines 130-131)
        CPlayer* pDstPlayer = nullptr; // In real implementation, get trade partner
        if (pDstPlayer) {
            CPlayer_SendMsg_DTradeAddInform(pDstPlayer, position, pItem, amount);
        }
        CPlayer_SendMsg_DTradeAddResult(pPlayer, 0);

        // Update statistics
        m_stats.RecordTrade(true);

        TradingOperationDetails result = CreateTradingResult(TradingResult::Success, startTime);
        result.transactionId = GenerateTransactionId();
        result.context.pSeller = pPlayer;
        result.context.itemSerial = serial;
        result.context.quantity = amount;

        LogTradingOperation(result);

        if (m_tradingCallback) {
            m_tradingCallback(result);
        }

        return result;

    } catch (const std::exception& e) {
        m_stats.RecordTrade(false);
        Logger::Error("CItemTradingSystem::ProcessDirectTradeAdd - Exception: %s", e.what());
        return CreateTradingResult(TradingResult::SystemError, startTime,
                                 std::string("Exception: ") + e.what());
    }
}

/**
 * Validate trading context
 */
bool CItemTradingSystem::ValidateTradingContext(const TradingContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemTradingSystem::ValidateTradingContext - Invalid context");
            return false;
        }

        if (context.quantity == 0 || context.quantity > 100) {
            Logger::Warning("CItemTradingSystem::ValidateTradingContext - Invalid quantity: %d", context.quantity);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::ValidateTradingContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate storage context
 */
bool CItemTradingSystem::ValidateStorageContext(const StorageContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemTradingSystem::ValidateStorageContext - Invalid context");
            return false;
        }

        if (!context.pItem) {
            Logger::Warning("CItemTradingSystem::ValidateStorageContext - No item specified");
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::ValidateStorageContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate item creation context
 */
bool CItemTradingSystem::ValidateItemCreationContext(const ItemCreationContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemTradingSystem::ValidateItemCreationContext - Invalid context");
            return false;
        }

        if (context.materials.empty()) {
            Logger::Warning("CItemTradingSystem::ValidateItemCreationContext - No materials specified");
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::ValidateItemCreationContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Reset statistics
 */
void CItemTradingSystem::ResetStatistics() {
    try {
        std::lock_guard<std::mutex> lock(m_statsMutex);

        m_stats.totalTrades = 0;
        m_stats.successfulTrades = 0;
        m_stats.failedTrades = 0;
        m_stats.totalValue = 0;
        m_stats.itemsCreated = 0;
        m_stats.storageOperations = 0;
        m_stats.lastTrade = std::chrono::system_clock::now();

        Logger::Debug("CItemTradingSystem::ResetStatistics - Statistics reset");

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::ResetStatistics - Exception: %s", e.what());
    }
}

/**
 * Set trading callback
 */
void CItemTradingSystem::SetTradingCallback(std::function<void(const TradingOperationDetails&)> callback) {
    m_tradingCallback = callback;
}

/**
 * Process unmanned trader buy request
 */
bool CItemTradingSystem::ProcessUnmannedTraderBuy(int playerIndex, _unmannedtrader_buy_item_request_clzo* pRequest) {
    try {
        if (!pRequest) {
            return false;
        }

        CUnmannedTraderController* pController = CUnmannedTraderController_Instance();
        if (!pController) {
            return false;
        }

        CUnmannedTraderController_Buy(pController, playerIndex, pRequest);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::ProcessUnmannedTraderBuy - Exception: %s", e.what());
        return false;
    }
}

/**
 * Process unmanned trader clear request
 */
bool CItemTradingSystem::ProcessUnmannedTraderClear(int playerIndex, _a_trade_clear_item_request_clzo* pRequest) {
    try {
        if (!pRequest) {
            return false;
        }

        CUnmannedTraderController* pController = CUnmannedTraderController_Instance();
        if (!pController) {
            return false;
        }

        CUnmannedTraderController_CancelRegist(pController, playerIndex, pRequest);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::ProcessUnmannedTraderClear - Exception: %s", e.what());
        return false;
    }
}

/**
 * Transfer item to storage
 */
uint32_t CItemTradingSystem::TransferItemToStorage(CPlayer* pPlayer, uint8_t storageCode, _STORAGE_LIST* pItem) {
    try {
        if (!pPlayer || !pItem || storageCode >= 8) {
            return 255;
        }

        // Call legacy transfer function
        return _STORAGE_LIST_TransInCon(pPlayer->m_Param.m_pStoragePtr[storageCode], pItem);

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::TransferItemToStorage - Exception: %s", e.what());
        return 255;
    }
}

/**
 * Create storage item
 */
_STORAGE_LIST* CItemTradingSystem::CreateStorageItem(const ItemCreationContext& context) {
    try {
        if (!context.IsValid()) {
            return nullptr;
        }

        // In real implementation, this would create a proper storage item
        // based on the materials and recipe
        // For now, return nullptr as placeholder
        return nullptr;

    } catch (const std::exception& e) {
        Logger::Error("CItemTradingSystem::CreateStorageItem - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Log trading operation
 */
void CItemTradingSystem::LogTradingOperation(const TradingOperationDetails& details) {
    try {
        if (m_bDetailedLogging) {
            Logger::Info("Trading Operation - Result: %s, Transaction: %u, Time: %lldms",
                        details.GetResultString().c_str(),
                        details.transactionId,
                        details.executionTime.count());
        }

        if (!details.IsSuccess()) {
            Logger::Warning("Trading Operation Failed - %s: %s",
                           details.GetResultString().c_str(),
                           details.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        // Don't log errors in logging function to avoid recursion
    }
}

/**
 * Create trading result with timing
 */
TradingOperationDetails CItemTradingSystem::CreateTradingResult(TradingResult result,
                                                               std::chrono::high_resolution_clock::time_point startTime,
                                                               const std::string& errorMessage) {
    TradingOperationDetails tradingResult;
    tradingResult.result = result;
    tradingResult.errorMessage = errorMessage;
    tradingResult.transactionId = GenerateTransactionId();
    tradingResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return tradingResult;
}

/**
 * Create storage result with timing
 */
StorageOperationDetails CItemTradingSystem::CreateStorageResult(StorageResult result,
                                                               std::chrono::high_resolution_clock::time_point startTime,
                                                               const std::string& errorMessage) {
    StorageOperationDetails storageResult;
    storageResult.result = result;
    storageResult.errorMessage = errorMessage;
    storageResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return storageResult;
}

/**
 * Create item creation result with timing
 */
ItemCreationOperationDetails CItemTradingSystem::CreateItemCreationResult(ItemCreationResult result,
                                                                         std::chrono::high_resolution_clock::time_point startTime,
                                                                         const std::string& errorMessage) {
    ItemCreationOperationDetails creationResult;
    creationResult.result = result;
    creationResult.errorMessage = errorMessage;
    creationResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return creationResult;
}

/**
 * Legacy compatibility functions
 */
namespace LegacyCompatibility {

/**
 * Legacy trade buy item request wrapper
 */
char ATradeBuyItemRequest_Legacy(CNetworkEX* pNetwork, int playerIndex, char* pBuffer) {
    try {
        static CItemTradingSystem tradingSystem;

        if (!tradingSystem.Initialize()) {
            Logger::Error("LegacyCompatibility::ATradeBuyItemRequest_Legacy - Failed to initialize trading system");
            return 0;
        }

        TradingOperationDetails result = tradingSystem.ProcessTradeBuyRequest(pNetwork, playerIndex, pBuffer);
        return result.IsSuccess() ? 1 : 0;

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::ATradeBuyItemRequest_Legacy - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Legacy trade clear item request wrapper
 */
char ATradeClearItemRequest_Legacy(CNetworkEX* pNetwork, int playerIndex, char* pBuffer) {
    try {
        static CItemTradingSystem tradingSystem;

        if (!tradingSystem.Initialize()) {
            Logger::Error("LegacyCompatibility::ATradeClearItemRequest_Legacy - Failed to initialize trading system");
            return 0;
        }

        TradingOperationDetails result = tradingSystem.ProcessTradeClearRequest(pNetwork, playerIndex, pBuffer);
        return result.IsSuccess() ? 1 : 0;

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::ATradeClearItemRequest_Legacy - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Legacy add storage wrapper
 */
_STORAGE_LIST* Emb_AddStorage_Legacy(CPlayer* pPlayer, char storageCode, _STORAGE_LIST* pItem,
                                    bool bEquipChange, bool bAdd) {
    try {
        static CItemTradingSystem tradingSystem;

        if (!tradingSystem.Initialize()) {
            Logger::Error("LegacyCompatibility::Emb_AddStorage_Legacy - Failed to initialize trading system");
            return nullptr;
        }

        StorageContext context;
        context.pPlayer = pPlayer;
        context.storageCode = static_cast<uint8_t>(storageCode);
        context.pItem = pItem;
        context.bEquipChange = bEquipChange;
        context.bAdd = bAdd;

        StorageOperationDetails result = tradingSystem.AddItemToStorage(context);
        return result.IsSuccess() ? result.pResultItem : nullptr;

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::Emb_AddStorage_Legacy - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Legacy make item wrapper
 */
void pc_MakeItem_Legacy(CPlayer* pPlayer, _STORAGE_POS_INDIV* pMakeTool, uint16_t manualIndex,
                       uint8_t materialNum, _STORAGE_POS_INDIV* pMaterials) {
    try {
        static CItemTradingSystem tradingSystem;

        if (!tradingSystem.Initialize()) {
            Logger::Error("LegacyCompatibility::pc_MakeItem_Legacy - Failed to initialize trading system");
            return;
        }

        ItemCreationContext context;
        context.pPlayer = pPlayer;
        context.pMakeTool = pMakeTool;
        context.manualIndex = manualIndex;
        context.materialNum = materialNum;

        // Add materials to context
        for (int i = 0; i < materialNum && i < 10; ++i) {
            context.materials.push_back(&pMaterials[i]);
        }

        ItemCreationOperationDetails result = tradingSystem.CreateItemForPlayer(context);
        if (!result.IsSuccess()) {
            Logger::Error("LegacyCompatibility::pc_MakeItem_Legacy - Item creation failed: %s",
                         result.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::pc_MakeItem_Legacy - Exception: %s", e.what());
    }
}

} // namespace LegacyCompatibility

} // namespace Items
} // namespace NexusProtection
