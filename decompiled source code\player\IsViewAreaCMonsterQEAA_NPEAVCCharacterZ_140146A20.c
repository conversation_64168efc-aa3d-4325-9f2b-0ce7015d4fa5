/*
 * Function: ?Is<PERSON>iewArea@CMonster@@QEAA_NPEAVCCharacter@@@Z
 * Address: 0x140146A20
 */

bool __fastcall CMonster::IsViewArea(CMonster *this, CCharacter *pTarget)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-B8h]@1
  float v6; // [sp+34h] [bp-84h]@6
  float v7; // [sp+44h] [bp-74h]@6
  float v8; // [sp+48h] [bp-70h]@6
  float v; // [sp+58h] [bp-60h]@6
  float v10; // [sp+5Ch] [bp-5Ch]@6
  float v11; // [sp+60h] [bp-58h]@6
  float src; // [sp+88h] [bp-30h]@6
  float v13; // [sp+8Ch] [bp-2Ch]@6
  float v14; // [sp+90h] [bp-28h]@6
  bool v15; // [sp+A4h] [bp-14h]@6
  CMonster *v16; // [sp+C0h] [bp+8h]@1
  CCharacter *v17; // [sp+C8h] [bp+10h]@1

  v17 = pTarget;
  v16 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pTarget )
  {
    v6 = 0.0;
    CMonster::GetVisualAngle(v16);
    v7 = 0.0;
    CMonster::GetVisualField(v16);
    v8 = 0.0;
    v = v16->m_fLookAtPos[0] - v16->m_fCurPos[0];
    v10 = v16->m_fLookAtPos[1] - v16->m_fCurPos[1];
    v11 = v16->m_fLookAtPos[2] - v16->m_fCurPos[2];
    Normalize(&v);
    v = 3.0 * v;
    v10 = 3.0 * v10;
    v11 = 3.0 * v11;
    src = v16->m_fCurPos[0] - v;
    v13 = v16->m_fCurPos[1] - v10;
    v14 = v16->m_fCurPos[2] - v11;
    v15 = CMonsterHelper::IsInSector(v17->m_fCurPos, &src, v16->m_fLookAtPos, v7, v8, &v6) != 0;
    result = v15;
  }
  else
  {
    result = 0;
  }
  return result;
}
