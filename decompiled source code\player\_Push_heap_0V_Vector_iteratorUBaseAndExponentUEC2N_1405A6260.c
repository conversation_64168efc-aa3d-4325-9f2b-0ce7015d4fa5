/*
 * Function: ??$_Push_heap_0@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@0PEA_JPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A6260
 */

int std::_Push_heap_0<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>()
{
  __int64 v0; // rax@1
  const struct CryptoPP::EC2NPoint *v1; // rax@2
  __int64 v3; // [sp+20h] [bp-B8h]@1
  char v4; // [sp+28h] [bp-B0h]@2
  CryptoPP::EC2NPoint *v5; // [sp+88h] [bp-50h]@2
  char v6; // [sp+90h] [bp-48h]@2
  char *v7; // [sp+A8h] [bp-30h]@2
  __int64 v8; // [sp+B0h] [bp-28h]@1
  CryptoPP::EC2NPoint *v9; // [sp+B8h] [bp-20h]@2
  CryptoPP::EC2NPoint *v10; // [sp+C0h] [bp-18h]@2
  __int64 v11; // [sp+C8h] [bp-10h]@2

  v8 = -2i64;
  LODWORD(v0) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-();
  v3 = v0;
  if ( v0 > 0 )
  {
    v5 = (CryptoPP::EC2NPoint *)&v4;
    v7 = &v6;
    LODWORD(v1) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator*();
    v9 = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
           v5,
           v1);
    v10 = v9;
    v11 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v7);
    std::_Push_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
      v11,
      v3,
      0i64,
      v10);
  }
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
}
