/*
 * Function: ?Process@CGuildBattleStateList@GUILD_BATTLE@@QEAAXPEAVCGuildBattle@2@@Z
 * Address: 0x1403DF090
 */

void __fastcall GUILD_BATTLE::CGuildBattleStateList::Process(GUILD_BATTLE::CGuildBattleStateList *this, GUILD_BATTLE::CGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int iAdvance; // [sp+20h] [bp-18h]@8
  int v6; // [sp+24h] [bp-14h]@15
  GUILD_BATTLE::CGuildBattleStateList *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_bEnter && v7->m_iState < v7->STATE_MAX )
  {
    if ( v7->m_pkCurState != v7->m_pkNextState )
      v7->m_pkCurState = v7->m_pkNextState;
    v7->m_bEnter = 0;
    iAdvance = ((int (__fastcall *)(GUILD_BATTLE::CGuildBattleState *))v7->m_pkCurState->vfptr->Enter)(v7->m_pkCurState);
    GUILD_BATTLE::CGuildBattleStateList::Advance(v7, iAdvance);
  }
  else if ( v7->m_pkCurState == v7->m_pkNextState && v7->STATE_MAX != v7->m_iState )
  {
    if ( v7->STATE_MAX > v7->m_iState )
    {
      v6 = ((int (__fastcall *)(GUILD_BATTLE::CGuildBattleState *))v7->m_pkCurState->vfptr->Loop)(v7->m_pkCurState);
      GUILD_BATTLE::CGuildBattleStateList::Advance(v7, v6);
      if ( v7->m_iForceAdvance != -3 )
      {
        GUILD_BATTLE::CGuildBattleStateList::Advance(v7, v7->m_iForceAdvance);
        v7->m_iForceAdvance = -3;
      }
    }
  }
  else
  {
    ((void (__fastcall *)(GUILD_BATTLE::CGuildBattleState *))v7->m_pkCurState->vfptr->Fin)(v7->m_pkCurState);
    v7->m_bEnter = 1;
    if ( v7->STATE_MAX == v7->m_iState )
      GUILD_BATTLE::CGuildBattleStateList::Next(v7, 1);
  }
}
