/*
 * Function: ?GoodsList@CashItemRemoteStore@@QEAA_NGPEAD@Z
 * Address: 0x1402F5220
 */

bool __fastcall CashItemRemoteStore::GoodsList(CashItemRemoteStore *this, unsigned __int16 wSock, char *pPacket)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->_bIsBuyCashItemByGold )
    result = CashItemRemoteStore::GoodsListBuyByGold(v7, wSock, pPacket);
  else
    result = CashItemRemoteStore::GoodsListBuyByCash(v7, wSock, pPacket);
  return result;
}
