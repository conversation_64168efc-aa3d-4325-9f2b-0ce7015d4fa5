/*
 * Function: j_?_Destroy@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAXPEAPEAVTRC_AutoTrade@@0@Z
 * Address: 0x14000D404
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Destroy(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_First, TRC_AutoTrade **_Last)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Destroy(this, _First, _Last);
}
