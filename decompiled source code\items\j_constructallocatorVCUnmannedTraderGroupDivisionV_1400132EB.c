/*
 * Function: j_?construct@?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@QEAAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@AEBV3@@Z
 * Address: 0x1400132EB
 */

void __fastcall std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::construct(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *this, CUnmannedTraderGroupDivisionVersionInfo *_Ptr, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::construct(this, _Ptr, _Val);
}
