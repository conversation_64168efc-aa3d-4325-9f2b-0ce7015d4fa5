/*
 * Function: ?dev_loot_item@CPlayer@@QEAA_NPEADH0H@Z
 * Address: 0x1400BD2F0
 */

bool __fastcall CPlayer::dev_loot_item(CPlayer *this, char *pszItemCode, int nNum, char *pszUpTalCode, int nUpNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  CPlayer *pOwner; // [sp+40h] [bp+8h]@1

  pOwner = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  return loot_item(pOwner, pszItemCode, nNum, pszUpTalCode, nUpNum);
}
