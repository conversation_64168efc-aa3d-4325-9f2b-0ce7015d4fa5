/*
 * Function: ??_G?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14055E840
 */

void *__fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::`scalar deleting destructor'(void *a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::`vbase destructor();
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
