/*
 * Function: ?get_slot@?$TInventory@U_INVENKEY@@@@QEAAPEAV?$TInvenSlot@U_INVENKEY@@@@HH@Z
 * Address: 0x1402D4660
 */

TInvenSlot<_INVENKEY> *__fastcall TInventory<_INVENKEY>::get_slot(TInventory<_INVENKEY> *this, int nP, int nS)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  TInvenSlot<_INVENKEY> *result; // rax@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  TInventory<_INVENKEY> *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_nMaxPageNum > nP && v7->m_nMaxSlotNum > nS )
    result = TInvenPage<_INVENKEY>::get_slot(&v7->m_pPage[nP], nS);
  else
    result = 0i64;
  return result;
}
