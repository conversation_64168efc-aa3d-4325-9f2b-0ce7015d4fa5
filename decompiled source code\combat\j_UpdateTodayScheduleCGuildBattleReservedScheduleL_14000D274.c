/*
 * Function: j_?UpdateTodaySchedule@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@AEAA_NI@Z
 * Address: 0x14000D274
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTodaySchedule(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int uiMapID)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTodaySchedule(this, uiMapID);
}
