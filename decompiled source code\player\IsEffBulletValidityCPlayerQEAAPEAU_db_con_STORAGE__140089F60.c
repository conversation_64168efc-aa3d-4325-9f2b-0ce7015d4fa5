/*
 * Function: ?IsEffBulletValidity@CPlayer@@QEAAPEAU_db_con@_STORAGE_LIST@@G@Z
 * Address: 0x140089F60
 */

_STORAGE_LIST::_db_con *__fastcall CPlayer::IsEffBulletValidity(CPlayer *this, unsigned __int16 wEffBulletSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *result; // rax@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  _STORAGE_LIST::_db_con *v6; // [sp+20h] [bp-48h]@6
  char *v7; // [sp+28h] [bp-40h]@8
  int nCashType; // [sp+30h] [bp-38h]@12
  _base_fld *v9; // [sp+38h] [bp-30h]@14
  _base_fld *v10; // [sp+40h] [bp-28h]@14
  char v11; // [sp+48h] [bp-20h]@17
  int v12; // [sp+4Ch] [bp-1Ch]@26
  int j; // [sp+50h] [bp-18h]@26
  size_t v14; // [sp+58h] [bp-10h]@24
  CPlayer *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( wEffBulletSerial == 0xFFFF )
  {
    result = 0i64;
  }
  else
  {
    v6 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v15->m_Param.m_dbEmbellish.m_nListNum, wEffBulletSerial);
    if ( v6 )
    {
      v7 = &v15->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
      if ( v7 )
      {
        if ( v6->m_byTableCode == 10 )
        {
          nCashType = GetUsePcCashType(v6->m_byTableCode, v6->m_wItemIndex);
          if ( CPlayer::IsUsableAccountType(v15, nCashType) )
          {
            v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v7 + 3));
            v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v6->m_wItemIndex);
            if ( v9 && v10 )
            {
              v11 = 0;
              if ( !strncmp(&v9[5].m_strCode[12], "-1", 2ui64) )
              {
                result = 0i64;
              }
              else if ( !strncmp(&v9[5].m_strCode[12], "D", 1ui64) )
              {
                if ( !strncmp((const char *)&v10[5], "-1", 2ui64) )
                  result = 0i64;
                else
                  result = v6;
              }
              else
              {
                if ( strlen_0(v10[4].m_strCode) >= 0x40 )
                  v14 = 64i64;
                else
                  v14 = strlen_0(v10[4].m_strCode);
                v12 = v14;
                for ( j = 0; j < v12; ++j )
                {
                  if ( strchr(&v9[4].m_strCode[16], v10[4].m_strCode[j]) )
                    return v6;
                }
                result = 0i64;
              }
            }
            else
            {
              result = 0i64;
            }
          }
          else
          {
            CPlayer::SendMsg_PremiumCashItemUse(v15, 0xFFFFu);
            result = 0i64;
          }
        }
        else
        {
          result = 0i64;
        }
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  return result;
}
