/*
 * Function: ?InsertNotEnoughLimitItemRecord@CItemStoreManager@@QEAA_NH@Z
 * Address: 0x14034A020
 */

char __fastcall CItemStoreManager::InsertNotEnoughLimitItemRecord(CItemStoreManager *this, int nNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int pdwSerial; // [sp+24h] [bp-24h]@6
  int j; // [sp+34h] [bp-14h]@4
  CItemStoreManager *v8; // [sp+50h] [bp+8h]@1
  int v9; // [sp+58h] [bp+10h]@1

  v9 = nNum;
  v8 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < v9; ++j )
  {
    if ( !CRFWorldDatabase::Insert_LimitItemRecord(pkDB, &pdwSerial) )
    {
      CItemStoreManager::Log(
        v8,
        "CItemStoreManager::InsertNotEnoughLimitItemRecord\r\n\t\tg_Main.m_pWorldDB->Insert_LimitItemRecord() Fail!\r\n");
      return 0;
    }
  }
  return 1;
}
