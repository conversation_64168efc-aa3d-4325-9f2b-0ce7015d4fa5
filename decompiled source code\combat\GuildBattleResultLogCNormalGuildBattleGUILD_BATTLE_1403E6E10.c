/*
 * Function: ?GuildBattleResultLog@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E6E10
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLog(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@6
  unsigned __int16 v4; // ax@14
  __int64 v5; // [sp+0h] [bp-138h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *pkTopGoalMember; // [sp+20h] [bp-118h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v7; // [sp+28h] [bp-110h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *pkTopKillMember; // [sp+30h] [bp-108h]@12
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v9; // [sp+38h] [bp-100h]@12
  _qry_case_guild_battel_result_log Sheet; // [sp+50h] [bp-E8h]@20
  int v11; // [sp+120h] [bp-18h]@6
  int v12; // [sp+124h] [bp-14h]@14
  unsigned __int64 v13; // [sp+128h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattle *v14; // [sp+140h] [bp+8h]@1

  v14 = this;
  v1 = &v5;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  pkTopGoalMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetTopGoalMember(v14->m_pkRed);
  v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetTopGoalMember(v14->m_pkBlue);
  if ( pkTopGoalMember )
  {
    if ( v7 )
    {
      v11 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetGoalCount(pkTopGoalMember);
      v3 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetGoalCount(v7);
      if ( v11 < v3 )
        pkTopGoalMember = v7;
    }
  }
  else if ( v7 )
  {
    pkTopGoalMember = v7;
  }
  else
  {
    pkTopGoalMember = 0i64;
  }
  pkTopKillMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetTopKillMember(v14->m_pkRed);
  v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetTopKillMember(v14->m_pkBlue);
  if ( pkTopKillMember )
  {
    if ( v9 )
    {
      v12 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetKillCount(pkTopKillMember);
      v4 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetKillCount(v9);
      if ( v12 < v4 )
        pkTopKillMember = v9;
    }
  }
  else if ( v9 )
  {
    pkTopKillMember = v9;
  }
  else
  {
    pkTopKillMember = 0i64;
  }
  GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogPushDBLog(v14, &Sheet, pkTopGoalMember, pkTopKillMember);
  GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogNotifyWeb(v14, &Sheet);
}
