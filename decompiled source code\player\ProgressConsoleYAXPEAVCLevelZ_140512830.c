/*
 * Function: ?ProgressConsole@@YAXPEAVCLevel@@@Z
 * Address: 0x140512830
 */

void __usercall ProgressConsole(struct CLevel *a1@<rcx>, int a2@<xmm0>)
{
  CLevel *v2; // rbx@1
  struct IDirect3DDevice8 *v3; // rax@1
  int v4; // ecx@1
  int v5; // eax@4
  float v6; // xmm10_4@5
  float v7; // xmm9_4@5
  int v8; // eax@6
  float v9; // xmm0_4@9
  float v10; // xmm1_4@9
  int v11; // eax@15
  int v12; // ecx@18
  int v13; // eax@20
  int v14; // ecx@29
  struct IDirect3DDevice8 *v15; // rax@31
  float v16; // xmm8_4@32
  float v17; // xmm9_4@32
  int v18; // esi@32
  signed int v19; // edi@32
  char *v20; // rbp@33
  float v21; // xmm1_4@34
  signed int v22; // ebx@35
  signed int v23; // ebx@37
  struct IDirect3DDevice8 *v24; // rax@37

  v2 = a1;
  v3 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64))v3->vfptr[16].Release)(v3, 28i64);
  v4 = dword_140978D98;
  stCLevel = v2;
  if ( dword_140978D98 == -1 )
  {
    if ( dword_184A89164 )
    {
      v5 = sub_140511900(byte_184A88FA0, a2);
      sub_140511FB0(v5);
    }
  }
  else
  {
    dword_140978D98 = -1;
    sub_140511FB0(v4);
  }
  v6 = *(float *)&dword_184A7978C;
  v7 = *(float *)&dword_184A79790;
  if ( dword_184A7A5A0 )
  {
    dword_184A7A5A0 = 0;
    v8 = dword_184A89150 == 0;
    dword_184A89150 = dword_184A89150 == 0;
    if ( v8 )
    {
      dword_184A8915C = LODWORD(FLOAT_1_0);
      dword_184A89154 = 1;
    }
    else
    {
      dword_184A8915C = LODWORD(FLOAT_N1_0);
      memset_0(byte_184A88FA0, 0, 0x50ui64);
      dword_184A88F80 = 0;
    }
  }
  v9 = (float)(R3GetLoopTime() * *(float *)&dword_184A8915C) * 3.0;
  v10 = *(float *)&dword_184A89158 + v9;
  *(float *)&dword_184A89158 = *(float *)&dword_184A89158 + v9;
  if ( *(float *)&dword_184A8915C != 0.0 && v10 > 1.0 )
  {
    dword_184A89158 = LODWORD(FLOAT_1_0);
    v10 = FLOAT_1_0;
  }
  if ( *(float *)&dword_184A8915C < 0.0 && v10 < 0.0 )
  {
    dword_184A89158 = 0;
    dword_184A89154 = 0;
  }
  v11 = GetCharFromKey();
  if ( dword_184A89154 )
  {
    if ( v11 )
    {
      switch ( v11 )
      {
        case 32:
          v12 = dword_184A88F80;
          if ( dword_184A88F80 < 79 )
          {
            byte_184A88FA0[dword_184A88F80] = 32;
            dword_184A88F80 = v12 + 1;
          }
          break;
        case 4294967295:
          v13 = dword_184A88F80;
          if ( dword_184A88F80 > 0 )
          {
            --dword_184A88F80;
            byte_184A88FA0[v13 - 1] = 0;
          }
          break;
        case 4294967294:
          if ( dword_184A88F80 > 0 )
            --dword_184A88F80;
          break;
        case 4294967293:
          dword_184A89164 = 1;
          break;
        case 4294967292:
          if ( dword_184A88F80 > 0 )
            --dword_184A88F80;
          break;
        case 4294967291:
          if ( dword_184A88F80 < 79 )
            ++dword_184A88F80;
          break;
        default:
          v14 = dword_184A88F80;
          if ( dword_184A88F80 < 79 )
          {
            byte_184A88FA0[dword_184A88F80] = v11;
            dword_184A88F80 = v14 + 1;
          }
          break;
        case 4294967289:
        case 4294967290:
          break;
      }
    }
    v15 = GetD3dDevice();
    if ( ((int (__fastcall *)(struct IDirect3DDevice8 *))v15->vfptr[11].AddRef)(v15) >= 0 )
    {
      v16 = (float)(*(float *)&dword_184A89158 * v7) - v7;
      v17 = v7 * 0.5;
      Draw2DSprite(0.0, v16, v6, v17, qword_184A88F90, 0xFFFFFFFF);
      v18 = 0;
      v19 = 0;
      do
      {
        v20 = (char *)&_ImageBase + 80 * ((dword_184A89160 - v18 + 100) % 100) + (_QWORD)&byte_184A87040[-5368709120i64];
        if ( *v20 )
        {
          v21 = (float)((float)(v17 + v16) - 20.0) - (float)v19;
          if ( v21 > -16.0 )
          {
            v22 = (signed int)ffloor(v21);
            DrawR3HangulA(8, v22, ">", 0xFF00FFFF, 0, 1.0);
            DrawR3HangulA(16, v22, v20, 0xFFFFFFFF, 0, 1.0);
          }
        }
        v19 += 16;
        ++v18;
      }
      while ( v19 < 1600 );
      v23 = (signed int)ffloor((float)(v17 + v16) - 20.0);
      DrawR3HangulA(8, v23, ">", 0xFF00FFFF, 0, 1.0);
      DrawR3HangulA(16, v23, byte_184A88FA0, 0xFFFFFFFF, 0, 1.0);
      DrawR3HangulA(6 * dword_184A88F80 + 16, v23, "_", 0xFF00FFFF, 0, 1.0);
      v24 = GetD3dDevice();
      ((void (__fastcall *)(struct IDirect3DDevice8 *))v24->vfptr[11].Release)(v24);
    }
  }
}
