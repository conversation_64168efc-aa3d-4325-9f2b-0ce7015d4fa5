/*
 * Function: ?LoadWorldInfoINI@CMainThread@@AEAAHXZ
 * Address: 0x1401E6BF0
 */

signed __int64 __fastcall CMainThread::LoadWorldInfoINI(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  const char *v4; // rax@20
  CNationSettingManager *v5; // rax@20
  __int64 v6; // [sp+0h] [bp-178h]@1
  DWORD nSize[2]; // [sp+20h] [bp-158h]@24
  LPCSTR lpFileName; // [sp+28h] [bp-150h]@24
  char ReturnedString; // [sp+38h] [bp-140h]@6
  char v10; // [sp+39h] [bp-13Fh]@6
  char Str1; // [sp+64h] [bp-114h]@11
  char v12; // [sp+65h] [bp-113h]@11
  char szCodeStr; // [sp+84h] [bp-F4h]@16
  char v14; // [sp+85h] [bp-F3h]@16
  int iType; // [sp+94h] [bp-E4h]@16
  CNationCodeStrTable v16; // [sp+B0h] [bp-C8h]@16
  int v17; // [sp+134h] [bp-44h]@20
  unsigned int v18; // [sp+140h] [bp-38h]@17
  unsigned int v19; // [sp+144h] [bp-34h]@19
  unsigned int v20; // [sp+148h] [bp-30h]@24
  unsigned int v21; // [sp+14Ch] [bp-2Ch]@25
  __int64 v22; // [sp+150h] [bp-28h]@4
  const char *v23; // [sp+158h] [bp-20h]@22
  unsigned __int64 v24; // [sp+160h] [bp-18h]@4
  CMainThread *v25; // [sp+180h] [bp+8h]@1

  v25 = this;
  v1 = &v6;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = -2i64;
  v24 = (unsigned __int64)&v6 ^ _security_cookie;
  GetPrivateProfileStringA("System", "WorldName", "X", v25->m_szWorldName, 0x21u, "..\\WorldInfo\\WorldInfo.ini");
  if ( !strcmp_0(v25->m_szWorldName, "X") )
    return 0xFFFFFFFFi64;
  M2W(v25->m_szWorldName, v25->m_wszWorldName, 0x21u);
  v25->m_bFreeServer = GetPrivateProfileIntA("System", "FreeServer", 0, "..\\WorldInfo\\WorldInfo.ini");
  v25->m_byWorldType = GetPrivateProfileIntA("System", "ServerType", 2, "..\\WorldInfo\\WorldInfo.ini");
  ReturnedString = 0;
  memset(&v10, 0, 8ui64);
  GetPrivateProfileStringA("ServerMode", "ReleaseType", "X", &ReturnedString, 9u, "..\\WorldInfo\\WorldInfo.ini");
  if ( !_stricmp(&ReturnedString, "Internal") )
  {
    v25->m_bReleaseServiceMode = 0;
  }
  else
  {
    if ( _stricmp(&ReturnedString, "Release") )
    {
      MyMessageBox(
        "CMainThread::LoadWorldSystemINI()",
        "WorldInfo.ini\r\n[ServerMode]\r\nReleaseType = %s Invalid!!",
        &ReturnedString);
      return 4294967294i64;
    }
    v25->m_bReleaseServiceMode = 1;
  }
  Str1 = 0;
  memset(&v12, 0, 5ui64);
  GetPrivateProfileStringA("ServerMode", "ExcuteService", "X", &Str1, 6u, "..\\WorldInfo\\WorldInfo.ini");
  if ( !_stricmp(&Str1, "true") )
  {
    v25->m_bExcuteService = 1;
  }
  else
  {
    if ( _stricmp(&Str1, "false") )
    {
      MyMessageBox(
        "CMainThread::LoadWorldSystemINI()",
        "WorldInfo.ini\r\n[ServerMode]\r\nExcuteService = %s Invalid!!",
        &Str1);
      return 4294967293i64;
    }
    v25->m_bExcuteService = 0;
  }
  szCodeStr = 0;
  memset(&v14, 0, 2ui64);
  iType = -1;
  GetPrivateProfileStringA("System", "NationCode", "X", &szCodeStr, 3u, "..\\WorldInfo\\WorldInfo.ini");
  CNationCodeStrTable::CNationCodeStrTable(&v16);
  if ( CNationCodeStrTable::Init(&v16) )
  {
    iType = CNationCodeStrTable::GetCode(&v16, &szCodeStr);
    if ( iType == -1 )
    {
      MyMessageBox(
        "CMainThread::LoadWorldSystemINI()",
        "WorldInfo.ini\r\n[System]\r\nNationCode = %s Invalid!!",
        &szCodeStr);
      v19 = -5;
      CNationCodeStrTable::~CNationCodeStrTable(&v16);
      result = v19;
    }
    else
    {
      v4 = CNationCodeStrTable::GetStr(&v16, iType);
      strcpy_s<3>((char (*)[3])&szCodeStr, v4);
      v5 = CTSingleton<CNationSettingManager>::Instance();
      v17 = CNationSettingManager::Init(v5, iType, &szCodeStr, v25->m_bReleaseServiceMode);
      if ( v17 )
      {
        if ( v25->m_bReleaseServiceMode )
          v23 = "true";
        else
          v23 = "false";
        LODWORD(lpFileName) = v17;
        *(_QWORD *)nSize = v23;
        MyMessageBox(
          "CMainThread::LoadWorldSystemINI()",
          "CNationSettingManager::Instance()->Init( iNationCode(%d), szNationCodeStr(%s), bServiceMode(%s) ) :  iRet(%d) Fail!",
          (unsigned int)iType,
          &szCodeStr);
        v20 = -6;
        CNationCodeStrTable::~CNationCodeStrTable(&v16);
        result = v20;
      }
      else
      {
        v21 = 0;
        CNationCodeStrTable::~CNationCodeStrTable(&v16);
        result = v21;
      }
    }
  }
  else
  {
    v18 = -4;
    CNationCodeStrTable::~CNationCodeStrTable(&v16);
    result = v18;
  }
  return result;
}
