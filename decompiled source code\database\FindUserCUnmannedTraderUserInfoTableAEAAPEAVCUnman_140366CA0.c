/*
 * Function: ?FindUser@CUnmannedTraderUserInfoTable@@AEAAPEAVCUnmannedTraderUserInfo@@GK@Z
 * Address: 0x140366CA0
 */

CUnmannedTraderUserInfo *__fastcall CUnmannedTraderUserInfoTable::FindUser(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, unsigned int dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *result; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  CUnmannedTraderUserInfo *v7; // [sp+20h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+50h] [bp+18h]@1

  dwSeriala = dwSerial;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CUnmannedTraderUserInfoTable::FindByIndex(v8, wInx);
  if ( v7 && CUnmannedTraderUserInfo::operator==(v7, dwSeriala) )
    result = v7;
  else
    result = CUnmannedTraderUserInfoTable::Find(v8, dwSeriala);
  return result;
}
