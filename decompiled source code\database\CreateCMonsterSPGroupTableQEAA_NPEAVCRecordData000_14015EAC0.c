/*
 * Function: ?Create@CMonsterSPGroupTable@@QEAA_NPEAVCRecordData@@0000@Z
 * Address: 0x14015EAC0
 */

char __fastcall CMonsterSPGroupTable::Create(CMonsterSPGroupTable *this, CRecordData *pMonsterRecordData, CRecordData *pMonsterSPRecordData, CRecordData *pSkillRecordData, CRecordData *pForceRecordData, CRecordData *pClassSkillRecordData)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v9; // [sp+0h] [bp-78h]@1
  int v10; // [sp+20h] [bp-58h]@12
  int n; // [sp+24h] [bp-54h]@15
  _monster_sp_group *v12; // [sp+28h] [bp-50h]@17
  _base_fld *v13; // [sp+30h] [bp-48h]@17
  unsigned int j; // [sp+38h] [bp-40h]@17
  _base_fld *v15; // [sp+40h] [bp-38h]@22
  int __n[2]; // [sp+48h] [bp-30h]@12
  void *v17; // [sp+50h] [bp-28h]@15
  void *__t; // [sp+58h] [bp-20h]@12
  __int64 v19; // [sp+60h] [bp-18h]@4
  void *v20; // [sp+68h] [bp-10h]@13
  CMonsterSPGroupTable *v21; // [sp+80h] [bp+8h]@1
  CRecordData *v22; // [sp+88h] [bp+10h]@1
  CRecordData *v23; // [sp+90h] [bp+18h]@1
  CRecordData *v24; // [sp+98h] [bp+20h]@1

  v24 = pSkillRecordData;
  v23 = pMonsterSPRecordData;
  v22 = pMonsterRecordData;
  v21 = this;
  v6 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v19 = -2i64;
  if ( pMonsterSPRecordData && pMonsterRecordData && pSkillRecordData && pForceRecordData && pClassSkillRecordData )
  {
    if ( CRecordData::GetRecordNum(pMonsterSPRecordData) > 0 )
    {
      v10 = CRecordData::GetRecordNum(v22);
      v21->m_dwRecordNum = v10;
      *(_QWORD *)__n = v21->m_dwRecordNum;
      __t = operator new[](saturated_mul(0x136ui64, *(unsigned __int64 *)__n));
      if ( __t )
      {
        `vector constructor iterator'(
          __t,
          0x136ui64,
          __n[0],
          (void *(__cdecl *)(void *))_monster_sp_group::_monster_sp_group);
        v20 = __t;
      }
      else
      {
        v20 = 0i64;
      }
      v17 = v20;
      v21->m_pRecordData = (_monster_sp_group *)v20;
      for ( n = 0; n < v21->m_dwRecordNum; ++n )
      {
        v12 = &v21->m_pRecordData[n];
        v13 = CRecordData::GetRecord(v22, n);
        v12->m_dwIndex = n;
        strcpy_0(v12->m_strCode, v13->m_strCode);
        v12->m_wCount = 0;
        for ( j = 0; j < 0xF; ++j )
        {
          if ( !strcmp_0(&v13[5].m_strCode[64 * (unsigned __int64)j + 36], "-1") )
          {
            v12->m_pSPData[j] = 0i64;
          }
          else
          {
            v15 = CRecordData::GetRecord(v23, &v13[5].m_strCode[64 * (unsigned __int64)j + 36]);
            if ( !v15 )
              return 0;
            v12->m_pSPData[v12->m_wCount] = (_monster_sp_fld *)v15;
            if ( v15[1].m_dwIndex )
            {
              if ( v15[1].m_dwIndex == 1 )
              {
                v12->m_SPDataFld[v12->m_wCount].m_SkillFld = (_skill_fld *)CRecordData::GetRecord(
                                                                             pForceRecordData,
                                                                             v15[1].m_strCode);
              }
              else
              {
                if ( v15[1].m_dwIndex != 2 )
                  return 0;
                v12->m_SPDataFld[v12->m_wCount].m_SkillFld = (_skill_fld *)CRecordData::GetRecord(
                                                                             pClassSkillRecordData,
                                                                             v15[1].m_strCode);
              }
            }
            else
            {
              v12->m_SPDataFld[v12->m_wCount].m_SkillFld = (_skill_fld *)CRecordData::GetRecord(v24, v15[1].m_strCode);
            }
            if ( v12->m_SPDataFld[v12->m_wCount].m_SkillFld )
              ++v12->m_wCount;
          }
        }
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
