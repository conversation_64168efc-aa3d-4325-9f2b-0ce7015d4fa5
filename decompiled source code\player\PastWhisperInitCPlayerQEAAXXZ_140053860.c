/*
 * Function: ?PastWhisperInit@CPlayer@@QEAAXXZ
 * Address: 0x140053860
 */

void __fastcall CPlayer::PastWhisperInit(CPlayer *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CPlayer *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 10; ++j )
    v4->m_PastWhiper[j].bMemory = 0;
}
