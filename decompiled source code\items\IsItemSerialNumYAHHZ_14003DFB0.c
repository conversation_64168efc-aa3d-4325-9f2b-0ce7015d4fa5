/*
 * Function: ?IsItemSerialNum@@YAHH@Z
 * Address: 0x14003DFB0
 */

signed __int64 __fastcall IsItemSerialNum(int nTableCode)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CRecordData *v4; // [sp+0h] [bp-18h]@1
  int v5; // [sp+8h] [bp-10h]@4
  int v6; // [sp+20h] [bp+8h]@1

  v6 = nTableCode;
  v1 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = &s_ptblItemData[v6];
  v5 = v6;
  switch ( v6 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      result = 1i64;
      break;
    case 6:
      result = 1i64;
      break;
    case 15:
      result = 1i64;
      break;
    case 8:
      result = 1i64;
      break;
    case 9:
      result = 1i64;
      break;
    case 24:
      result = 1i64;
      break;
    case 33:
      result = 1i64;
      break;
    default:
      result = 0i64;
      break;
  }
  return result;
}
