/*
 * Function: j_?MakeLimitItemUpdateQuery@CItemStoreManager@@QEAAXKEHKPEAU_limit_item_db_data@@_KPEADH@Z
 * Address: 0x140013EBC
 */

void __fastcall CItemStoreManager::MakeLimitItemUpdateQuery(CItemStoreManager *this, unsigned int dwSerial, char byStoreType, int nTypeSerial, unsigned int dwStoreIndex, _limit_item_db_data *pItemData, unsigned __int64 dwLimitInitTime, char *pszQuery, int nBufSize)
{
  CItemStoreManager::MakeLimitItemUpdateQuery(
    this,
    dwSerial,
    byStoreType,
    nTypeSerial,
    dwStoreIndex,
    pItemData,
    dwLimitInitTime,
    pszQuery,
    nBufSize);
}
