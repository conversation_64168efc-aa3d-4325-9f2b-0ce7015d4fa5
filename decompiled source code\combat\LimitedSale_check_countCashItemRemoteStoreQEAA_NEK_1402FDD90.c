/*
 * Function: ?LimitedSale_check_count@CashItemRemoteStore@@QEAA_NEK@Z
 * Address: 0x1402FDD90
 */

char __fastcall CashItemRemoteStore::LimitedSale_check_count(CashItemRemoteStore *this, char byTableCode, unsigned int dwIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int16 v7; // [sp+20h] [bp-18h]@4
  unsigned __int16 j; // [sp+24h] [bp-14h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@7
  CashItemRemoteStore *v10; // [sp+40h] [bp+8h]@1
  char v11; // [sp+48h] [bp+10h]@1
  unsigned int dwIndexa; // [sp+50h] [bp+18h]@1

  dwIndexa = dwIndex;
  v11 = byTableCode;
  v10 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CashItemRemoteStore::BuyLimSale(v10, byTableCode, dwIndex);
  for ( j = 0; (signed int)j < 2532; ++j )
  {
    v9 = &g_Player + j;
    if ( v9->m_bOper )
    {
      if ( v9->m_bLive )
        ICsSendInterface::SendMsg_LimitedsaleEventInform(v9->m_ObjID.m_wIndex, v11, dwIndexa, v7);
    }
  }
  return 1;
}
