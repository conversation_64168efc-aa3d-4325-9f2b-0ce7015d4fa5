/*
 * Function: ?SendMsg_CombineItemResult@CPlayer@@QEAAXEKPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400D8000
 */

void __fastcall CPlayer::SendMsg_CombineItemResult(CPlayer *this, char byErrCode, unsigned int dwFee, _STORAGE_LIST::_db_con *pNewItem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned int v8; // [sp+39h] [bp-4Fh]@4
  unsigned int v9; // [sp+3Dh] [bp-4Bh]@4
  char v10; // [sp+41h] [bp-47h]@4
  unsigned __int16 v11; // [sp+42h] [bp-46h]@4
  int v12; // [sp+44h] [bp-44h]@4
  unsigned __int16 v13; // [sp+48h] [bp-40h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v15; // [sp+65h] [bp-23h]@4
  CPlayer *v16; // [sp+90h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v17; // [sp+A8h] [bp+20h]@1

  v17 = pNewItem;
  v16 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  v8 = dwFee;
  v9 = CPlayerDB::GetDalant(&v16->m_Param);
  v10 = v17->m_byTableCode;
  v11 = v17->m_wItemIndex;
  v12 = v17->m_dwDur;
  v13 = v17->m_wSerial;
  pbyType = 7;
  v15 = 29;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x16u);
}
