/*
 * Function: ?SendMsg_HSKQuestActCum@CPlayer@@QEAAXXZ
 * Address: 0x1400DF310
 */

void __fastcall CPlayer::SendMsg_HSKQuestActCum(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  unsigned int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  char byCurrentHour[2]; // [sp+20h] [bp-58h]@4
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  unsigned __int16 v8; // [sp+38h] [bp-40h]@4
  bool v9; // [sp+3Ah] [bp-3Eh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  char v12; // [sp+64h] [bp-14h]@4
  char v13; // [sp+65h] [bp-13h]@4
  char v14; // [sp+66h] [bp-12h]@4
  char v15; // [sp+67h] [bp-11h]@4
  CPlayer *v16; // [sp+80h] [bp+8h]@1

  v16 = this;
  v1 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v16->m_nHSKPvpPoint;
  v8 = v16->m_wKillPoint;
  v12 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
  v13 = CHolyStoneSystem::GetStartHour(&g_HolySys);
  v14 = CHolyStoneSystem::GetStartDay(&g_HolySys);
  v15 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
  v3 = CHolyStoneSystem::GetStartYear(&g_HolySys);
  v9 = MiningTicket::AuthLastMentalTicket(&v16->m_MinigTicket, v3, v15, v14, v13, v12) != 0;
  pbyType = 25;
  v11 = 12;
  v4 = v16->m_ObjID.m_wIndex;
  strcpy(byCurrentHour, "\a");
  CNetProcess::LoadSendMsg(unk_1414F2088, v4, &pbyType, szMsg, *(unsigned __int16 *)byCurrentHour);
}
