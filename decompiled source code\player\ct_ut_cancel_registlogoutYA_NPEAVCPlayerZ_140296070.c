/*
 * Function: ?ct_ut_cancel_registlogout@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296070
 */

char __fastcall ct_ut_cancel_registlogout(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPlayer *pOnea; // [sp+40h] [bp+8h]@1

  pOnea = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( ct_ut_cancel_regist(pOnea) )
  {
    CNetworkEX::Close(&g_Network, 0, pOnea->m_ObjID.m_wIndex, 0, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
