/*
 * Function: ??0?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x14037E1F0
 */

void __fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<CUnmannedTraderSubClassInfo *> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<CUnmannedTraderSubClassInfo *> *v6; // [sp+28h] [bp-10h]@4
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<CUnmannedTraderSubClassInfo *> *)&v5;
  std::allocator<CUnmannedTraderSubClassInfo *>::allocator<CUnmannedTraderSubClassInfo *>((std::allocator<CUnmannedTraderSubClassInfo *> *)&v5);
  std::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    (std::_Vector_val<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v7->_Myfirstiter,
    v3);
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Buy(v7, 0i64);
}
