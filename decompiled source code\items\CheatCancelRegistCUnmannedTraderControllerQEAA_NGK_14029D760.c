/*
 * Function: ?CheatCancelRegist@CUnmannedTraderController@@QEAA_NGKE@Z
 * Address: 0x14029D760
 */

bool __fastcall CUnmannedTraderController::CheatCancelRegist(CUnmannedTraderController *this, unsigned __int16 wInx, unsigned int dwOwnerSerial, char byNth)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-28h]@1
  unsigned __int16 v9; // [sp+38h] [bp+10h]@1
  unsigned int dwOwnerSeriala; // [sp+40h] [bp+18h]@1
  char v11; // [sp+48h] [bp+20h]@1

  v11 = byNth;
  dwOwnerSeriala = dwOwnerSerial;
  v9 = wInx;
  v4 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = CUnmannedTraderUserInfoTable::Instance();
  return CUnmannedTraderUserInfoTable::CheatCancelRegist(v6, v9, dwOwnerSeriala, v11);
}
