/*
 * Function: ??0?$CipherModeFinalTemplate_ExternalCipher@VCBC_Decryption@CryptoPP@@@CryptoPP@@QEAA@AEAV?$SimpleKeyedTransformation@VBlockTransformation@CryptoPP@@@1@@Z
 * Address: 0x14055B5B0
 */

CryptoPP::CBC_Decryption *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Decryption>::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Decryption>(CryptoPP::CBC_Decryption *a1, __int64 a2)
{
  CryptoPP::CBC_Decryption *v3; // [sp+40h] [bp+8h]@1
  __int64 v4; // [sp+48h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::CBC_Decryption::CBC_Decryption(a1);
  v3->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Decryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v3->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Decryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_Decryption>::SetCipher(v3, v4);
  return v3;
}
