/*
 * Function: ?ExponentiateElement@?$DL_GroupParameters@VInteger@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@AEBV32@0@Z
 * Address: 0x140552070
 */

CryptoPP::Integer *__fastcall CryptoPP::DL_GroupParameters<CryptoPP::Integer>::ExponentiateElement(__int64 a1, CryptoPP::Integer *a2, __int64 a3, __int64 a4)
{
  CryptoPP::Integer v5; // [sp+30h] [bp-48h]@1
  int v6; // [sp+58h] [bp-20h]@1
  __int64 v7; // [sp+60h] [bp-18h]@1
  __int64 v8; // [sp+80h] [bp+8h]@1
  CryptoPP::Integer *v9; // [sp+88h] [bp+10h]@1
  __int64 v10; // [sp+90h] [bp+18h]@1
  __int64 v11; // [sp+98h] [bp+20h]@1

  v11 = a4;
  v10 = a3;
  v9 = a2;
  v8 = a1;
  v7 = -2i64;
  v6 = 0;
  CryptoPP::Integer::Integer(&v5);
  (*(void (__fastcall **)(__int64, CryptoPP::Integer *, __int64, __int64))(*(_QWORD *)v8 + 160i64))(v8, &v5, v10, v11);
  CryptoPP::Integer::Integer(v9, &v5);
  v6 |= 1u;
  CryptoPP::Integer::~Integer(&v5);
  return v9;
}
