/*
 * Function: ?_LootItem_EventSet@CMonster@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140144FF0
 */

char __fastcall CMonster::_LootItem_EventSet(CMonster *this, CPlayer *pOwner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-138h]@1
  float *pNewPos; // [sp+20h] [bp-118h]@20
  bool bHide; // [sp+28h] [bp-110h]@20
  CPlayer *pAttacker; // [sp+30h] [bp-108h]@20
  int bHolyScanner; // [sp+38h] [bp-100h]@20
  char byEventItemLootAuth; // [sp+40h] [bp-F8h]@20
  char v11; // [sp+50h] [bp-E8h]@4
  _event_set_looting *v12; // [sp+58h] [bp-E0h]@6
  int j; // [sp+60h] [bp-D8h]@7
  char *psItemCode; // [sp+68h] [bp-D0h]@10
  char v15; // [sp+70h] [bp-C8h]@11
  _base_fld *v16; // [sp+78h] [bp-C0h]@11
  int v17; // [sp+80h] [bp-B8h]@11
  int k; // [sp+84h] [bp-B4h]@11
  int v19; // [sp+88h] [bp-B0h]@14
  char v20; // [sp+8Ch] [bp-ACh]@17
  char v21; // [sp+8Dh] [bp-ABh]@17
  unsigned int v22; // [sp+90h] [bp-A8h]@19
  _STORAGE_LIST::_db_con pItem; // [sp+A8h] [bp-90h]@19
  float pStdPos; // [sp+F8h] [bp-40h]@19
  int v25; // [sp+FCh] [bp-3Ch]@19
  int v26; // [sp+100h] [bp-38h]@19
  CMapData *pMap; // [sp+118h] [bp-20h]@19
  int v28; // [sp+120h] [bp-18h]@10
  CMonster *v29; // [sp+140h] [bp+8h]@1
  CPlayer *v30; // [sp+148h] [bp+10h]@1

  v30 = pOwner;
  v29 = this;
  v2 = &v5;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = 0;
  if ( v29->m_pEventSet )
  {
    v12 = CMonsterEventSet::GetEvenSetLooting(g_MonsterEventSet, v29->m_pMonRec->m_strCode);
    if ( v12 )
    {
      for ( j = 0; j < v12->nItemCount; ++j )
      {
        psItemCode = v12->stEventItemList[j].strCode;
        v28 = v12->stEventItemList[j].byProb;
        if ( v28 > rand() % 100 )
        {
          v15 = GetItemTableCode(psItemCode);
          v16 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v15, psItemCode);
          v17 = v12->wMagnifications * *((_WORD *)psItemCode + 32);
          for ( k = 0; k < v17; ++k )
          {
            v19 = 0;
            if ( IsOverLapItem((unsigned __int8)v15) )
              v19 = *((_WORD *)psItemCode + 33);
            else
              v19 = GetItemDurPoint((unsigned __int8)v15, v16->m_dwIndex);
            v20 = GetDefItemUpgSocketNum((unsigned __int8)v15, v16->m_dwIndex);
            v21 = 0;
            if ( (signed int)(unsigned __int8)v20 > 0 )
              v21 = rand() % (unsigned __int8)v20 + 1;
            v22 = GetBitAfterSetLimSocket(v21);
            _STORAGE_LIST::_db_con::_db_con(&pItem);
            pItem.m_byTableCode = v15;
            pItem.m_wItemIndex = v16->m_dwIndex;
            pItem.m_dwDur = (unsigned int)v19;
            pItem.m_dwLv = v22;
            pStdPos = 0.0;
            v25 = 0;
            v26 = 0;
            pMap = v29->m_pCurMap;
            if ( CMapData::GetRandPosVirtualDumExcludeStdRange(pMap, v29->m_fCurPos, v12->wRange, 0, &pStdPos) )
            {
              byEventItemLootAuth = v12->byLootAuth;
              bHolyScanner = v12->bWithHolyScanner;
              pAttacker = v30;
              bHide = 0;
              pNewPos = &pStdPos;
              if ( CreateItemBox(
                     &pItem,
                     7,
                     pMap,
                     v29->m_wMapLayerIndex,
                     &pStdPos,
                     0,
                     v30,
                     bHolyScanner,
                     byEventItemLootAuth) )
              {
                v11 = 1;
                CLogFile::Write(&stru_1799C95A8, "Event Set Item >> %s, %d", psItemCode, pItem.m_dwDur);
              }
            }
          }
        }
      }
    }
    result = v11;
  }
  else
  {
    result = v11;
  }
  return result;
}
