/*
 * Function: ?Find@CUnmannedTraderUserInfo@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@K@Z
 * Address: 0x140359C40
 */

std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall CUnmannedTraderUserInfo::Find(CUnmannedTraderUserInfo *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result, unsigned int dwRegistSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v5; // rax@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v6; // rax@7
  __int64 v8; // [sp+0h] [bp-78h]@1
  unsigned __int8 j; // [sp+20h] [bp-58h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > resulta; // [sp+28h] [bp-50h]@7
  int v11; // [sp+40h] [bp-38h]@4
  __int64 v12; // [sp+48h] [bp-30h]@4
  __int64 _Off; // [sp+50h] [bp-28h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v14; // [sp+58h] [bp-20h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v15; // [sp+60h] [bp-18h]@7
  CUnmannedTraderUserInfo *v16; // [sp+80h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v17; // [sp+88h] [bp+10h]@1
  unsigned int v18; // [sp+90h] [bp+18h]@1

  v18 = dwRegistSerial;
  v17 = result;
  v16 = this;
  v3 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = -2i64;
  v11 = 0;
  for ( j = 0; j < (signed int)v16->m_byMaxRegistCnt; ++j )
  {
    v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v16->m_vecRegistItemInfo,
           j);
    if ( v18 == CUnmannedTraderRegistItemInfo::GetRegistSerial(v5) )
    {
      _Off = j;
      v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(
             &v16->m_vecRegistItemInfo,
             &resulta);
      v14 = v6;
      v15 = v6;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator+(
        v6,
        v17,
        _Off);
      v11 |= 1u;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&resulta);
      return v17;
    }
  }
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
    &v16->m_vecRegistItemInfo,
    v17);
  return v17;
}
