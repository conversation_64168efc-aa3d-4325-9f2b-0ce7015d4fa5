/*
 * Function: ?Insert_ItemChargeInGame@CRFWorldDatabase@@QEAA_NKK_KKE@Z
 * Address: 0x1404988C0
 */

bool __fastcall CRFWorldDatabase::Insert_ItemChargeInGame(CRFWorldDatabase *this, unsigned int dwAvatorSerial, unsigned int dwItemCode_K, unsigned __int64 dwItemCode_D, unsigned int dwItemCode_U, char byType)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-478h]@1
  unsigned int v10; // [sp+20h] [bp-458h]@4
  unsigned __int64 v11; // [sp+28h] [bp-450h]@4
  unsigned int v12; // [sp+30h] [bp-448h]@4
  int v13; // [sp+38h] [bp-440h]@4
  char DstBuf; // [sp+50h] [bp-428h]@4
  unsigned __int64 v15; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+480h] [bp+8h]@1

  v16 = this;
  v6 = &v9;
  for ( i = 284i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = (unsigned __int64)&v9 ^ _security_cookie;
  v13 = (unsigned __int8)byType;
  v12 = dwItemCode_U;
  v11 = dwItemCode_D;
  v10 = dwItemCode_K;
  sprintf_s(&DstBuf, 0x400ui64, "{ CALL pInsert_ItemChargeInGame( %u, %d, %d, %d, %u ) }", dwAvatorSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v16->vfptr, &DstBuf, 1);
}
