/*
 * Function: ?GuildBattleResultLogPushDBLog@CNormalGuildBattle@GUILD_BATTLE@@IEAAXAEAV_qry_case_guild_battel_result_log@@PEAVCNormalGuildBattleGuildMember@2@1@Z
 * Address: 0x1403E7D30
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogPushDBLog(GUILD_BATTLE::CNormalGuildBattle *this, _qry_case_guild_battel_result_log *Sheet, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkTopGoalMember, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkTopKillMember)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedulePool *v6; // rax@4
  ATL::CTimeSpan *v7; // rax@6
  __int64 v8; // rax@6
  char *v9; // rax@11
  char *v10; // rax@11
  char *v11; // rax@13
  char *v12; // rax@17
  __int64 v13; // [sp+0h] [bp-C8h]@1
  char *pQryData; // [sp+20h] [bp-A8h]@8
  int nSize; // [sp+28h] [bp-A0h]@8
  int v16; // [sp+30h] [bp-98h]@8
  int v17; // [sp+38h] [bp-90h]@8
  GUILD_BATTLE::CGuildBattleSchedule *v18; // [sp+40h] [bp-88h]@4
  tm *v19; // [sp+48h] [bp-80h]@4
  tm *v20; // [sp+50h] [bp-78h]@4
  __int64 _Time; // [sp+68h] [bp-60h]@6
  __int64 v22; // [sp+88h] [bp-40h]@6
  CPlayer *v23; // [sp+98h] [bp-30h]@11
  CPlayer *v24; // [sp+A0h] [bp-28h]@15
  ATL::CTimeSpan result; // [sp+A8h] [bp-20h]@6
  char *Src; // [sp+B0h] [bp-18h]@20
  GUILD_BATTLE::CNormalGuildBattle *v27; // [sp+D0h] [bp+8h]@1
  _qry_case_guild_battel_result_log *Dst; // [sp+D8h] [bp+10h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v29; // [sp+E0h] [bp+18h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v30; // [sp+E8h] [bp+20h]@1

  v30 = pkTopKillMember;
  v29 = pkTopGoalMember;
  Dst = Sheet;
  v27 = this;
  v4 = &v13;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v18 = GUILD_BATTLE::CGuildBattleSchedulePool::GetRef(v6, v27->m_dwID);
  v19 = 0i64;
  v20 = 0i64;
  if ( v18 )
  {
    _Time = GUILD_BATTLE::CGuildBattleSchedule::GetRealStartTime(v18);
    v7 = GUILD_BATTLE::CGuildBattleSchedule::GetBattleTime(v18, &result);
    v8 = ATL::CTimeSpan::GetTimeSpan(v7);
    v22 = v8 + _Time;
    v19 = localtime_14(&_Time);
    if ( v19 )
    {
      ++v19->tm_mon;
      v19->tm_year += 1900;
      v17 = v19->tm_min;
      v16 = v19->tm_hour;
      nSize = v19->tm_mday;
      LODWORD(pQryData) = v19->tm_mon;
      sprintf_s(Dst->szStartTime, 0x11ui64, "%04d-%02d-%02d %02d:%02d", v19->tm_year);
      v20 = localtime_14(&v22);
      if ( v20 )
      {
        ++v20->tm_mon;
        v20->tm_year += 1900;
        v17 = v20->tm_min;
        v16 = v20->tm_hour;
        nSize = v20->tm_mday;
        LODWORD(pQryData) = v20->tm_mon;
        sprintf_s(Dst->szEndTime, 0x11ui64, "%04d-%02d-%02d %02d:%02d", v20->tm_year);
      }
      else
      {
        strcpy_s(Dst->szEndTime, 0x11ui64, "1900-00-00 00:00");
      }
    }
    else
    {
      strcpy_s(Dst->szStartTime, 0x11ui64, "1900-00-00 00:00");
      strcpy_s(Dst->szEndTime, 0x11ui64, "1900-00-00 00:00");
    }
  }
  else
  {
    strcpy_s(Dst->szStartTime, 0x11ui64, "1900-00-00 00:00");
    strcpy_s(Dst->szEndTime, 0x11ui64, "1900-00-00 00:00");
  }
  Dst->dwRedSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v27->m_pkRed);
  v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v27->m_pkRed);
  strcpy_s(Dst->wszRedName, 0x11ui64, v9);
  Dst->dwBlueSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v27->m_pkBlue);
  v10 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v27->m_pkBlue);
  strcpy_s(Dst->wszBlueName, 0x11ui64, v10);
  Dst->dwRedScore = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v27->m_pkRed);
  Dst->dwBlueScore = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v27->m_pkBlue);
  Dst->dwRedMaxJoinCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetMaxJoinMemberCount(v27->m_pkRed);
  Dst->dwBlueMaxJoinCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetMaxJoinMemberCount(v27->m_pkBlue);
  Dst->dwRedGoalCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v27->m_pkRed);
  Dst->dwBlueGoalCnt = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v27->m_pkBlue);
  Dst->dwRedKillCntSum = GUILD_BATTLE::CNormalGuildBattleGuild::GetKillCountSum(v27->m_pkRed);
  Dst->dwBlueKillCntSum = GUILD_BATTLE::CNormalGuildBattleGuild::GetKillCountSum(v27->m_pkBlue);
  Dst->byBattleResult = v27->m_byWinResult;
  v23 = 0i64;
  if ( v29 && (v23 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(v29)) != 0i64 )
  {
    Dst->dwMaxGoalCharacSerial = v23->m_dwObjSerial;
    v11 = CPlayerDB::GetCharNameW(&v23->m_Param);
    strcpy_s(Dst->wszMaxGoalCharacName, 0x11ui64, v11);
  }
  else
  {
    Dst->dwMaxGoalCharacSerial = 0;
    strcpy_s(Dst->wszMaxGoalCharacName, 0x11ui64, "NONE");
  }
  v24 = 0i64;
  if ( v30 && (v24 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(v30)) != 0i64 )
  {
    Dst->dwMaxKillCharacSerial = v24->m_dwObjSerial;
    v12 = CPlayerDB::GetCharNameW(&v24->m_Param);
    strcpy_s(Dst->wszMaxKillCharacName, 0x11ui64, v12);
  }
  else
  {
    Dst->dwMaxKillCharacSerial = 0;
    strcpy_s(Dst->wszMaxKillCharacName, 0x11ui64, "NONE");
  }
  Dst->byJoinLimit = v27->m_byGuildBattleNumber;
  Dst->dwGuildBattleCostGold = 5000;
  if ( v27->m_pkField )
    Src = GUILD_BATTLE::CNormalGuildBattleField::GetMapStrCode(v27->m_pkField);
  else
    Src = "NONE";
  strcpy_s(Dst->szBattleMapCode, 0xCui64, Src);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 84, Dst->szStartTime, 184);
}
