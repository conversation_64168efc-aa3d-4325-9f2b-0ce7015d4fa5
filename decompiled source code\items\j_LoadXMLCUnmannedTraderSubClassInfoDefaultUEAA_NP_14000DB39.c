/*
 * Function: j_?LoadXML@CUnmannedTraderSubClassInfoDefault@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x14000DB39
 */

bool __fastcall CUnmannedTraderSubClassInfoDefault::LoadXML(CUnmannedTraderSubClassInfoDefault *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  return CUnmannedTraderSubClassInfoDefault::LoadXML(this, elemSubClass, kLogger, dwDivisionID, dwClassID);
}
