/*
 * Function: _CUnmannedTraderUserInfo::CompleteCancelRegistItem_::_1_::dtor$0
 * Address: 0x14035B780
 */

void __fastcall CUnmannedTraderUserInfo::CompleteCancelRegistItem_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 56));
}
