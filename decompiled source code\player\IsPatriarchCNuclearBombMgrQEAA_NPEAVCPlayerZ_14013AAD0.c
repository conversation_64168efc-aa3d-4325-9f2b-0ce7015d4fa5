/*
 * Function: ?IsPatriarch@CNuclearBombMgr@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x14013AAD0
 */

bool __fastcall CNuclearBombMgr::IsPatriarch(CNuclearBombMgr *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  unsigned int dwSerial; // [sp+24h] [bp-14h]@4
  CNuclearBombMgr *v8; // [sp+40h] [bp+8h]@1
  CPlayer *v9; // [sp+48h] [bp+10h]@1

  v9 = pOne;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  dwSerial = CPlayerDB::GetCharSerial(&v9->m_Param);
  return (signed int)(unsigned __int8)CNuclearBombMgr::GetBossType(v8, v6, dwSerial) < 3;
}
