/*
 * Function: ?GetRewardItems_DarkDungeon@CPlayer@@QEAAHPEAV_dh_reward_sub_setup@@PEAU_db_con@_STORAGE_LIST@@H@Z
 * Address: 0x1400CDA30
 */

__int64 __fastcall CPlayer::GetRewardItems_DarkDungeon(CPlayer *this, _dh_reward_sub_setup *pSetup, _STORAGE_LIST::_db_con *pItems, int bRealBoss)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-58h]@1
  unsigned int v8; // [sp+20h] [bp-38h]@4
  unsigned int v9; // [sp+24h] [bp-34h]@4
  int v10; // [sp+28h] [bp-30h]@4
  unsigned int v11; // [sp+2Ch] [bp-2Ch]@4
  int j; // [sp+30h] [bp-28h]@6
  unsigned int v13; // [sp+34h] [bp-24h]@8
  int k; // [sp+38h] [bp-20h]@14
  char v15; // [sp+3Ch] [bp-1Ch]@17
  int v16; // [sp+40h] [bp-18h]@17
  int v17; // [sp+44h] [bp-14h]@4
  unsigned int v18; // [sp+48h] [bp-10h]@10
  CPlayer *v19; // [sp+60h] [bp+8h]@1
  _dh_reward_sub_setup *v20; // [sp+68h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v21; // [sp+70h] [bp+18h]@1
  int v22; // [sp+78h] [bp+20h]@1

  v22 = bRealBoss;
  v21 = pItems;
  v20 = pSetup;
  v19 = this;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = 2147450880;
  v10 = rand();
  v17 = v10 << 16;
  v11 = rand() + v17;
  if ( !v22 )
    v11 *= 10;
  for ( j = 0; j < v20->nItemNum; ++j )
  {
    v13 = v20->m_dwGivePercent[j];
    if ( v11 <= v13 )
    {
      if ( v9 >= v13 )
        v18 = v13;
      else
        v18 = v9;
      v9 = v18;
    }
  }
  for ( k = 0; k < v20->nItemNum; ++k )
  {
    if ( v20->m_dwGivePercent[k] == v9 )
    {
      v15 = v19->m_pUserDB->m_AvatorData.dbAvator.m_byRaceSexCode;
      v16 = IsItemEquipCivil(v20->Item[k]->m_byTableCode, v20->Item[k]->m_wItemIndex, v15);
      if ( v16 )
        memcpy_0(&v21[v8++], v20->Item[k], 0x32ui64);
    }
  }
  return v8;
}
