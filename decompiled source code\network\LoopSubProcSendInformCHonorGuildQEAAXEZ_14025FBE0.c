/*
 * Function: ?LoopSubProcSendInform@CHonorGuild@@QEAAXE@Z
 * Address: 0x14025FBE0
 */

void __fastcall CHonorGuild::LoopSubProcSendInform(CHonorGuild *this, char byRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+24h] [bp-14h]@4
  CHonorGuild *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byRace;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = v7->m_uiProccessIndex[(unsigned __int8)byRace] + 50;
  for ( j = v7->m_uiProccessIndex[(unsigned __int8)byRace]; j < v5 && j < 0x9E4; ++j )
  {
    if ( *(&g_Player.m_bOper + 50856 * j) )
    {
      if ( CPlayerDB::GetRaceCode((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * j)) == (unsigned __int8)v8 )
        CHonorGuild::SendCurrHonorGuildList(v7, *(&g_Player.m_ObjID.m_wIndex + 25428 * j), v8, -1);
    }
  }
  v7->m_uiProccessIndex[(unsigned __int8)v8] = j;
  if ( v7->m_uiProccessIndex[(unsigned __int8)v8] >= 0x9E4 )
  {
    v7->m_uiProccessIndex[(unsigned __int8)v8] = 0;
    v7->m_bChageInform[(unsigned __int8)v8] = 0;
  }
}
