/*
 * Function: ?IsolatedInitialize@RandomNumberSink@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FB5F0
 */

void __fastcall CryptoPP::RandomNumberSink::IsolatedInitialize(CryptoPP::RandomNumberSink *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::NameValuePairs::GetRequiredParameter<CryptoPP::RandomNumberGenerator *>(
    a2,
    "RandomNumberSink",
    "RandomNumberGeneratorPointer",
    &this->m_rng);
}
