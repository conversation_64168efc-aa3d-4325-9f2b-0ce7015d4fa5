/*
 * Function: ??$_Destroy_range@PEAURECV_DATA@@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAXPEAPEAURECV_DATA@@0AEAV?$allocator@PEAURECV_DATA@@@0@@Z
 * Address: 0x14031AFA0
 */

void __fastcall std::_Destroy_range<RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, RECV_DATA **_Last, std::allocator<RECV_DATA *> *_Al)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Scalar_ptr_iterator_tag v6; // [sp+20h] [bp-18h]@4
  RECV_DATA **__formal; // [sp+40h] [bp+8h]@1
  RECV_DATA **_Lasta; // [sp+48h] [bp+10h]@1
  std::allocator<RECV_DATA *> *_Ala; // [sp+50h] [bp+18h]@1

  _Ala = _Al;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = std::_Ptr_cat<RECV_DATA * *,RECV_DATA * *>(&__formal, &_Lasta);
  std::_Destroy_range<RECV_DATA *,std::allocator<RECV_DATA *>>(__formal, _Lasta, _Ala, v6);
}
