/*
 * Function: ?pc_DTradeCancleRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400F3E60
 */

void __fastcall CPlayer::pc_DTradeCancleRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  char v4; // [sp+20h] [bp-38h]@4
  CPlayer *p_pDst; // [sp+38h] [bp-20h]@4
  CPlayer *lp_pOne; // [sp+60h] [bp+8h]@1

  lp_pOne = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = 0;
  p_pDst = 0i64;
  if ( !DTradeEqualPerson(lp_pOne, &p_pDst) )
    v4 = 1;
  if ( v4 )
  {
    _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
    if ( p_pDst )
    {
      _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
      CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
    }
  }
  else
  {
    _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
    CPlayer::SendMsg_DTradeCancleResult(lp_pOne, v4);
    CPlayer::SendMsg_DTradeCancleInform(p_pDst);
  }
}
