/*
 * Function: ?InitializeWorker@CCashDBWorkManager@@QEAA_NXZ
 * Address: 0x1402F32A0
 */

bool __fastcall CCashDBWorkManager::InitializeWorker(CCashDBWorkManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CCashDBWorkManager *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CashDbWorker::IsNULL(v5->m_pWorker) )
    result = 0;
  else
    result = ((int (__fastcall *)(CashDbWorker *))v5->m_pWorker->vfptr[1].~Worker)(v5->m_pWorker);
  return result;
}
