/*
 * Function: ?RemoveSFContHelpByEffect@CCharacter@@QEAAXHH@Z
 * Address: 0x140174BD0
 */

void __fastcall CCharacter::RemoveSFContHelpByEffect(CCharacter *this, int nContParamCode, int nContParamIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  int j; // [sp+30h] [bp-38h]@4
  bool *v7; // [sp+38h] [bp-30h]@7
  _base_fld *v8; // [sp+40h] [bp-28h]@8
  char *v9; // [sp+48h] [bp-20h]@8
  int k; // [sp+50h] [bp-18h]@12
  char *v11; // [sp+58h] [bp-10h]@14
  CCharacter *v12; // [sp+70h] [bp+8h]@1
  int v13; // [sp+78h] [bp+10h]@1
  int v14; // [sp+80h] [bp+18h]@1

  v14 = nContParamIndex;
  v13 = nContParamCode;
  v12 = this;
  v3 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 8; ++j )
  {
    v7 = &v12->m_SFCont[1][j].m_bExist;
    if ( *v7 )
    {
      v8 = CRecordData::GetRecord(&stru_1799C8410 + v7[1], *((_WORD *)v7 + 1));
      v9 = 0i64;
      v9 = v7[1] == 1 ? &v8[12].m_strCode[56] : &v8[13].m_strCode[48];
      if ( v9 )
      {
        for ( k = 0; k < 5; ++k )
        {
          v11 = &v9[36 * k];
          if ( *(_DWORD *)v11 == -1 )
            break;
          if ( *(_DWORD *)v11 == v13 && *((_DWORD *)v11 + 1) == v14 )
          {
            CCharacter::RemoveSFContEffect(v12, 1, j, 0, 0);
            break;
          }
        }
      }
    }
  }
}
