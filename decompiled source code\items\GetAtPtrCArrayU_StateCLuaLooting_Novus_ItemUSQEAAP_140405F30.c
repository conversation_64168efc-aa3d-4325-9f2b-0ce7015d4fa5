/*
 * Function: ?GetAtPtr@?$CArray@U_State@CLuaLooting_Novus_Item@@@US@@QEAAPEAU_State@CLuaLooting_Novus_Item@@K@Z
 * Address: 0x140405F30
 */

CLuaLooting_Novus_Item::_State *__fastcall US::CArray<CLuaLooting_Novus_Item::_State>::GetAtPtr(US::CArray<CLuaLooting_Novus_Item::_State> *this, unsigned int dwIndex)
{
  CLuaLooting_Novus_Item::_State *result; // rax@3

  if ( this->m_bAlloc && dwIndex < this->m_dwCount )
    result = &this->m_pBuffer[dwIndex];
  else
    result = 0i64;
  return result;
}
