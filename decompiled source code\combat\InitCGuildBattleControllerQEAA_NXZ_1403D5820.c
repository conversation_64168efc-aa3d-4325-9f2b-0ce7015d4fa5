/*
 * Function: ?Init@CGuildBattleController@@QEAA_NXZ
 * Address: 0x1403D5820
 */

bool __fastcall CGuildBattleController::Init(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@4
  bool result; // al@5
  GUILD_BATTLE::CGuildBattleRewardItemManager *v5; // rax@6
  GUILD_BATTLE::CGuildBattleRankManager *v6; // rax@8
  GUILD_BATTLE::CNormalGuildBattleFieldList *v7; // rax@10
  GUILD_BATTLE::CGuildBattleScheduler *v8; // rax@12
  GUILD_BATTLE::CPossibleBattleGuildListManager *v9; // rax@14
  GUILD_BATTLE::CNormalGuildBattleManager *v10; // rax@16
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v11; // rax@18
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v12; // rax@20
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v13; // rax@22
  __int64 v14; // [sp+0h] [bp-28h]@1

  v1 = &v14;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
  if ( GUILD_BATTLE::CGuildBattleLogger::Init(v3) )
  {
    v5 = GUILD_BATTLE::CGuildBattleRewardItemManager::Instance();
    if ( GUILD_BATTLE::CGuildBattleRewardItemManager::Init(v5) )
    {
      v6 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
      if ( GUILD_BATTLE::CGuildBattleRankManager::Init(v6) )
      {
        v7 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
        if ( GUILD_BATTLE::CNormalGuildBattleFieldList::Init(v7) )
        {
          v8 = GUILD_BATTLE::CGuildBattleScheduler::Instance();
          if ( GUILD_BATTLE::CGuildBattleScheduler::Init(v8) )
          {
            v9 = GUILD_BATTLE::CPossibleBattleGuildListManager::Instance();
            if ( GUILD_BATTLE::CPossibleBattleGuildListManager::Init(v9) )
            {
              v10 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
              if ( GUILD_BATTLE::CNormalGuildBattleManager::Init(v10) )
              {
                v11 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
                if ( GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Init(v11) )
                {
                  v12 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
                  if ( GUILD_BATTLE::CCurrentGuildBattleInfoManager::Init(v12) )
                  {
                    v13 = GUILD_BATTLE::CNormalGuildBattleStateListPool::Instance();
                    result = GUILD_BATTLE::CNormalGuildBattleStateListPool::Init(v13) != 0;
                  }
                  else
                  {
                    result = 0;
                  }
                }
                else
                {
                  result = 0;
                }
              }
              else
              {
                result = 0;
              }
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
