/**
 * @file CGuildSystemManager_Core.cpp
 * @brief Modern C++20 Guild System Manager core implementation
 * 
 * This file provides the core implementation of the CGuildSystemManager class
 * with comprehensive guild system management, proper error handling, and statistics.
 */

#include "../Headers/CGuildSystemManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>
#include <format>

// Legacy includes for compatibility
extern "C" {
    class CGuild {
    public:
        bool IsFill() const;
        void Loop(bool changeDay);
    };
    
    extern CGuild* g_Guild;
    extern int GetCurDay();
}

namespace NexusProtection::Guild {

CGuildSystemManager& CGuildSystemManager::GetInstance() {
    static CGuildSystemManager instance;
    return instance;
}

GuildSystemResult CGuildSystemManager::Initialize(const GuildSystemConfig& config) {
    try {
        if (m_isInitialized.load()) {
            return GuildSystemResult::Success;
        }
        
        m_state.store(GuildSystemState::Initializing);
        
        // Validate configuration
        if (!config.IsValid()) {
            m_state.store(GuildSystemState::Error);
            return GuildSystemResult::InitializationFailed;
        }
        
        // Store configuration
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }
        
        // Validate guild array
        if (!ValidateGuildArray()) {
            m_state.store(GuildSystemState::Error);
            return GuildSystemResult::InvalidGuildArray;
        }
        
        // Initialize statistics
        m_stats = GuildSystemStats{};
        
        // Initialize day tracking
        int currentDay = GetCurDay();
        m_lastDay.store(currentDay);
        
        m_isInitialized.store(true);
        m_isShutdown.store(false);
        m_state.store(GuildSystemState::Running);
        
        std::cout << "[INFO] Guild System Manager initialized successfully" << std::endl;
        std::cout << "[INFO] Configuration: maxGuilds=" << config.maxGuilds 
                  << ", dayChangeProcessing=" << (config.enableDayChangeProcessing ? "enabled" : "disabled")
                  << ", statistics=" << (config.enableStatistics ? "enabled" : "disabled") << std::endl;
        
        return GuildSystemResult::Success;
        
    } catch (const std::exception& e) {
        m_state.store(GuildSystemState::Error);
        std::cerr << "[ERROR] Exception in Initialize: " << e.what() << std::endl;
        return GuildSystemResult::InitializationFailed;
    }
}

GuildSystemResult CGuildSystemManager::Shutdown() {
    try {
        if (!m_isInitialized.load() || m_isShutdown.load()) {
            return GuildSystemResult::Success;
        }
        
        m_state.store(GuildSystemState::Shutdown);
        
        // Clear callbacks
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            m_guildProcessedCallback = nullptr;
            m_guildErrorCallback = nullptr;
            m_dayChangeCallback = nullptr;
        }
        
        m_isShutdown.store(true);
        m_isInitialized.store(false);
        
        std::cout << "[INFO] Guild System Manager shutdown completed" << std::endl;
        
        return GuildSystemResult::Success;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in Shutdown: " << e.what() << std::endl;
        return GuildSystemResult::SystemError;
    }
}

GuildSystemResult CGuildSystemManager::ProcessGuildLoop(bool dayChanged) {
    try {
        if (!m_isInitialized.load() || m_isShutdown.load()) {
            return GuildSystemResult::InitializationFailed;
        }
        
        if (m_state.load() != GuildSystemState::Running) {
            return GuildSystemResult::SystemError;
        }
        
        // Get current configuration
        GuildSystemConfig config;
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            config = m_config;
        }
        
        // Check for day change
        int currentDay = GetCurDay();
        bool actualDayChanged = dayChanged;
        
        if (config.enableDayChangeProcessing) {
            int lastDay = m_lastDay.load();
            if (currentDay != lastDay && lastDay != -1) {
                actualDayChanged = true;
                m_lastDay.store(currentDay);
                HandleDayChange(currentDay);
            } else if (lastDay == -1) {
                m_lastDay.store(currentDay);
            }
        }
        
        // Process all guilds
        uint32_t processedCount = 0;
        uint32_t activeCount = 0;
        uint32_t errorCount = 0;
        
        for (uint32_t i = 0; i < config.maxGuilds; ++i) {
            if (ProcessGuild(i, actualDayChanged)) {
                processedCount++;
                activeCount++;
            } else {
                errorCount++;
            }
        }
        
        // Update statistics
        if (config.enableStatistics) {
            m_stats.totalLoopCycles.fetch_add(1);
            m_stats.totalGuildsProcessed.fetch_add(processedCount);
            m_stats.activeGuilds.store(activeCount);
            m_stats.processingErrors.fetch_add(errorCount);
            
            if (actualDayChanged) {
                m_stats.dayChangeEvents.fetch_add(1);
            }
        }
        
        return GuildSystemResult::Success;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ProcessGuildLoop: " << e.what() << std::endl;
        m_stats.processingErrors.fetch_add(1);
        return GuildSystemResult::ProcessingError;
    }
}

void CGuildSystemManager::OnLoop_GuildSystem(bool dayChanged) {
    // Legacy compatibility wrapper
    ProcessGuildLoop(dayChanged);
}

bool CGuildSystemManager::ProcessGuild(uint32_t guildIndex, bool dayChanged) {
    try {
        if (!g_Guild) {
            LogError(guildIndex, "Guild array is null");
            return false;
        }
        
        CGuild* guild = &g_Guild[guildIndex];
        if (!guild) {
            LogError(guildIndex, "Guild pointer is null");
            return false;
        }
        
        // Check if guild is active/filled
        if (!guild->IsFill()) {
            return true; // Not an error, just inactive
        }
        
        // Process guild loop
        guild->Loop(dayChanged);
        
        // Notify callback if set
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            if (m_guildProcessedCallback) {
                try {
                    m_guildProcessedCallback(guildIndex, dayChanged);
                } catch (const std::exception& e) {
                    LogError(guildIndex, std::format("Callback exception: {}", e.what()));
                }
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        LogError(guildIndex, std::format("Processing exception: {}", e.what()));
        return false;
    }
}

bool CGuildSystemManager::ValidateGuildArray() const {
    try {
        if (!g_Guild) {
            std::cerr << "[ERROR] Guild array (g_Guild) is null" << std::endl;
            return false;
        }
        
        // Basic validation - could be extended with more checks
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ValidateGuildArray: " << e.what() << std::endl;
        return false;
    }
}

void CGuildSystemManager::UpdateStatistics(uint32_t guildIndex, bool success, bool dayChanged) {
    try {
        GuildSystemConfig config;
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            config = m_config;
        }
        
        if (!config.enableStatistics) {
            return;
        }
        
        if (success) {
            m_stats.totalGuildsProcessed.fetch_add(1);
        } else {
            m_stats.processingErrors.fetch_add(1);
        }
        
        if (dayChanged) {
            m_stats.dayChangeEvents.fetch_add(1);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UpdateStatistics: " << e.what() << std::endl;
    }
}

void CGuildSystemManager::LogError(uint32_t guildIndex, const std::string& message) {
    try {
        GuildSystemConfig config;
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            config = m_config;
        }
        
        if (!config.enableErrorLogging) {
            return;
        }
        
        std::cerr << "[ERROR] Guild " << guildIndex << ": " << message << std::endl;
        
        // Track consecutive errors
        m_consecutiveErrors.fetch_add(1);
        m_lastErrorTime = std::chrono::steady_clock::now();
        
        // Notify error callback if set
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            if (m_guildErrorCallback) {
                try {
                    m_guildErrorCallback(guildIndex, message);
                } catch (const std::exception& e) {
                    std::cerr << "[ERROR] Error callback exception: " << e.what() << std::endl;
                }
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in LogError: " << e.what() << std::endl;
    }
}

void CGuildSystemManager::HandleDayChange(int currentDay) {
    try {
        std::cout << "[INFO] Day change detected: " << m_lastDay.load() << " -> " << currentDay << std::endl;
        
        // Notify day change callback if set
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            if (m_dayChangeCallback) {
                try {
                    m_dayChangeCallback(currentDay, m_lastDay.load());
                } catch (const std::exception& e) {
                    std::cerr << "[ERROR] Day change callback exception: " << e.what() << std::endl;
                }
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in HandleDayChange: " << e.what() << std::endl;
    }
}

GuildSystemStats CGuildSystemManager::GetStatistics() const {
    return m_stats;
}

GuildSystemConfig CGuildSystemManager::GetConfiguration() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_config;
}

bool CGuildSystemManager::UpdateConfiguration(const GuildSystemConfig& config) {
    try {
        if (!config.IsValid()) {
            std::cerr << "[ERROR] Invalid configuration provided" << std::endl;
            return false;
        }

        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }

        std::cout << "[INFO] Guild system configuration updated" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UpdateConfiguration: " << e.what() << std::endl;
        return false;
    }
}

void CGuildSystemManager::SetGuildProcessedCallback(GuildProcessedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_guildProcessedCallback = std::move(callback);
}

void CGuildSystemManager::SetGuildErrorCallback(GuildErrorCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_guildErrorCallback = std::move(callback);
}

void CGuildSystemManager::SetDayChangeCallback(DayChangeCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_dayChangeCallback = std::move(callback);
}

uint32_t CGuildSystemManager::GetActiveGuildCount() const {
    try {
        if (!m_isInitialized.load() || !g_Guild) {
            return 0;
        }

        GuildSystemConfig config;
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            config = m_config;
        }

        uint32_t activeCount = 0;
        for (uint32_t i = 0; i < config.maxGuilds; ++i) {
            CGuild* guild = &g_Guild[i];
            if (guild && guild->IsFill()) {
                activeCount++;
            }
        }

        return activeCount;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in GetActiveGuildCount: " << e.what() << std::endl;
        return 0;
    }
}

GuildSystemResult CGuildSystemManager::ForceDayChange() {
    try {
        if (!m_isInitialized.load()) {
            return GuildSystemResult::InitializationFailed;
        }

        int currentDay = GetCurDay();
        int previousDay = m_lastDay.load();

        m_lastDay.store(currentDay);
        HandleDayChange(currentDay);

        std::cout << "[INFO] Forced day change: " << previousDay << " -> " << currentDay << std::endl;

        return ProcessGuildLoop(true);

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ForceDayChange: " << e.what() << std::endl;
        return GuildSystemResult::ProcessingError;
    }
}

void CGuildSystemManager::ResetStatistics() {
    try {
        m_stats = GuildSystemStats{};
        m_consecutiveErrors.store(0);
        m_lastErrorTime = std::chrono::steady_clock::now();

        std::cout << "[INFO] Guild system statistics reset" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ResetStatistics: " << e.what() << std::endl;
    }
}

} // namespace NexusProtection::Guild

// Legacy C-style wrapper functions for compatibility
extern "C" {
    /**
     * @brief Legacy wrapper for OnLoop_GuildSystem
     *
     * @param bChangeDay Whether a day change event occurred
     */
    void OnLoop_GuildSystem(bool bChangeDay) {
        NexusProtection::Guild::CGuildSystemManager::GetInstance().OnLoop_GuildSystem(bChangeDay);
    }

    /**
     * @brief Legacy jump wrapper for OnLoop_GuildSystem
     *
     * @param bChangeDay Whether a day change event occurred
     */
    void j_OnLoop_GuildSystem(bool bChangeDay) {
        OnLoop_GuildSystem(bChangeDay);
    }
}
