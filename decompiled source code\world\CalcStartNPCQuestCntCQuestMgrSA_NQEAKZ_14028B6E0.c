/*
 * Function: ?CalcStartNPCQuestCnt@CQuestMgr@@SA_NQEAK@Z
 * Address: 0x14028B6E0
 */

char __fastcall CQuestMgr::CalcStartNPCQuestCnt(unsigned int *pdwCnt)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+20h] [bp-28h]@4
  int n; // [sp+24h] [bp-24h]@4
  int j; // [sp+28h] [bp-20h]@6
  _base_fld *v9; // [sp+30h] [bp-18h]@9
  unsigned int *v10; // [sp+50h] [bp+8h]@1

  v10 = pdwCnt;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = 0;
  for ( n = 0; ; ++n )
  {
    v3 = CRecordData::GetRecordNum(CQuestMgr::s_tblQuestHappenEvent + 1);
    if ( n >= v3 )
      break;
    for ( j = 0; j < 3; ++j )
    {
      v9 = CRecordData::GetRecord(CQuestMgr::s_tblQuestHappenEvent + 1, n);
      if ( strcmp_0(&v9[6].m_strCode[704 * j + 44], "-1")
        && strcmp_0(&v9[6].m_strCode[704 * j + 44], "0")
        && *(_DWORD *)&v9[1].m_strCode[704 * j + 8] == 1 )
      {
        ++v10[j];
      }
    }
  }
  return 1;
}
