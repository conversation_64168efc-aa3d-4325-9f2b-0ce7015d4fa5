/*
 * Function: ?Set@CUnmannedTraderSchedule@@QEAAXEK_JKK@Z
 * Address: 0x140397620
 */

void __fastcall CUnmannedTraderSchedule::Set(CUnmannedTraderSchedule *this, char byType, unsigned int dwSerial, __int64 tEndTime, unsigned int dwOwnerSerial, unsigned int dwK)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-48h]@1
  _INVENKEY v9; // [sp+24h] [bp-24h]@4
  CUnmannedTraderSchedule *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v6 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10->m_eState = 1;
  v10->m_byType = byType;
  v10->m_dwRegistSerial = dwSerial;
  v10->m_tEndTime = tEndTime;
  v10->m_dwOwnerSerial = dwOwnerSerial;
  _INVENKEY::_INVENKEY(&v9);
  _INVENKEY::LoadDBKey(&v9, dwK);
  v10->m_byItemTableCode = v9.byTableCode;
  v10->m_wItemTableIndex = v9.wItemIndex;
}
