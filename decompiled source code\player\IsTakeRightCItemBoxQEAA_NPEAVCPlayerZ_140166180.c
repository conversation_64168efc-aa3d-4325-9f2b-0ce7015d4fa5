/*
 * Function: ?IsTakeRight@CItemBox@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140166180
 */

bool __fastcall CItemBox::IsTakeRight(CItemBox *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@6
  bool result; // al@7
  int v6; // eax@14
  CPvpUserAndGuildRankingSystem *v7; // rax@20
  unsigned int v8; // eax@20
  int v9; // eax@21
  unsigned int v10; // eax@23
  __int64 v11; // [sp+0h] [bp-68h]@1
  CPartyPlayer **v12; // [sp+30h] [bp-38h]@50
  int j; // [sp+38h] [bp-30h]@51
  char v14; // [sp+3Ch] [bp-2Ch]@6
  char v15; // [sp+3Dh] [bp-2Bh]@6
  char v16; // [sp+3Eh] [bp-2Ah]@6
  char v17; // [sp+3Fh] [bp-29h]@6
  int v18; // [sp+40h] [bp-28h]@8
  int v19; // [sp+44h] [bp-24h]@14
  unsigned int v20; // [sp+48h] [bp-20h]@20
  int v21; // [sp+4Ch] [bp-1Ch]@21
  unsigned int v22; // [sp+50h] [bp-18h]@23
  CItemBox *v23; // [sp+70h] [bp+8h]@1
  CPlayer *pOnea; // [sp+78h] [bp+10h]@1

  pOnea = pOne;
  v23 = this;
  v2 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v23->m_byCreateCode == 7 )
  {
    if ( !v23->m_bHolyScanner
      || (v14 = CHolyStoneSystem::GetNumOfTime(&g_HolySys),
          v15 = CHolyStoneSystem::GetStartHour(&g_HolySys),
          v16 = CHolyStoneSystem::GetStartDay(&g_HolySys),
          v17 = CHolyStoneSystem::GetStartMonth(&g_HolySys),
          v4 = CHolyStoneSystem::GetStartYear(&g_HolySys),
          MiningTicket::AuthLastMentalTicket(&pOnea->m_MinigTicket, v4, v17, v16, v15, v14)) )
    {
      v18 = v23->m_byEventLootAuth;
      switch ( v18 )
      {
        case 0:
          result = v23->m_dwEventPartyBoss == CPlayerDB::GetCharSerial(&pOnea->m_Param)
                || CPartyPlayer::IsPartyMode(pOnea->m_pPartyMgr)
                && v23->m_dwEventPartyBoss == pOnea->m_pPartyMgr->m_pPartyBoss->m_id.dwSerial;
          break;
        case 1:
          v19 = v23->m_byEventRaceCode;
          v6 = CPlayerDB::GetRaceCode(&pOnea->m_Param);
          result = v19 == v6;
          break;
        case 2:
          result = pOnea->m_Param.m_pGuild && v23->m_dwEventGuildSerial == pOnea->m_Param.m_pGuild->m_dwSerial;
          break;
        case 3:
          result = 1;
          break;
        case 4:
          v7 = CPvpUserAndGuildRankingSystem::Instance();
          v20 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v7, v23->m_byEventRaceCode, 0);
          v8 = CPlayerDB::GetCharSerial(&pOnea->m_Param);
          result = v20 == v8;
          break;
        case 5:
          v21 = v23->m_byEventRaceCode;
          v9 = CPlayerDB::GetRaceCode(&pOnea->m_Param);
          result = 0;
          if ( v21 == v9 )
          {
            if ( pOnea->m_Param.m_pGuild )
            {
              v22 = CGuild::GetGuildMasterSerial(pOnea->m_Param.m_pGuild);
              v10 = CPlayerDB::GetCharSerial(&pOnea->m_Param);
              if ( v22 == v10 )
                result = 1;
            }
          }
          break;
        default:
          result = 0;
          break;
      }
    }
    else
    {
      result = 0;
    }
  }
  else if ( v23->m_byCreateCode != 4 && v23->m_byCreateCode != 6 )
  {
    if ( v23->m_dwOwnerSerial == pOne->m_dwObjSerial )
    {
      result = 1;
    }
    else if ( v23->m_dwThrowerCharSerial == pOne->m_dwObjSerial )
    {
      result = 1;
    }
    else if ( v23->m_byCreateCode == 3 )
    {
      result = 0;
    }
    else if ( !v23->m_bCompDgr || pOne->m_byUserDgr )
    {
      if ( v23->m_byThrowerDegree == 255 || v23->m_byThrowerDegree == pOne->m_byUserDgr )
      {
        if ( v23->m_byThrowerRaceCode == 255 || CPlayerDB::GetRaceCode(&pOne->m_Param) == v23->m_byThrowerRaceCode )
        {
          if ( v23->m_dwOwnerSerial == -1 )
          {
            result = 1;
          }
          else if ( v23->m_nStateCode <= 0 )
          {
            if ( v23->m_bPartyShare )
            {
              v12 = CPartyPlayer::GetPtrPartyMember(pOnea->m_pPartyMgr);
              if ( v12 )
              {
                for ( j = 0; j < 8 && v12[j]; ++j )
                {
                  if ( v12[j]->m_id.dwSerial == v23->m_dwOwnerSerial )
                    return 1;
                }
              }
            }
            result = 0;
          }
          else
          {
            result = 1;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = CHolyStoneSystem::IsItemLootAuthority(&g_HolySys, pOne, v23->m_byCreateCode);
  }
  return result;
}
