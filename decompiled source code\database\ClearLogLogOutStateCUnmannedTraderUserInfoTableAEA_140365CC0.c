/*
 * Function: ?ClearLogLogOutState@CUnmannedTraderUserInfoTable@@AEAAXPEBDKQEBDPEADKKKK@Z
 * Address: 0x140365CC0
 */

void __fastcall CUnmannedTraderUserInfoTable::ClearLogLogOutState(CUnmannedTraderUserInfoTable *this, const char *szType, unsigned int dwOwner, const char *const szAccount, char *wszName, unsigned int dwRegistSerial, unsigned int dwK, unsigned int dwD, unsigned int dwU)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@18
  __int64 v12; // [sp+0h] [bp-148h]@1
  char *v13; // [sp+20h] [bp-128h]@11
  const char *v14; // [sp+28h] [bp-120h]@11
  unsigned int v15; // [sp+30h] [bp-118h]@11
  char *v16; // [sp+38h] [bp-110h]@11
  unsigned int v17; // [sp+40h] [bp-108h]@11
  void *v18; // [sp+48h] [bp-100h]@11
  char *v19; // [sp+50h] [bp-F8h]@11
  char *v20; // [sp+58h] [bp-F0h]@11
  char szTran; // [sp+68h] [bp-E0h]@4
  char v22; // [sp+69h] [bp-DFh]@4
  _INVENKEY v23; // [sp+94h] [bp-B4h]@4
  _base_fld *v24; // [sp+A8h] [bp-A0h]@4
  char Buffer; // [sp+B8h] [bp-90h]@12
  char v26; // [sp+BDh] [bp-8Bh]@12
  char v27; // [sp+E8h] [bp-60h]@12
  char v28; // [sp+EDh] [bp-5Bh]@12
  char *v29; // [sp+110h] [bp-38h]@6
  const char *v30; // [sp+118h] [bp-30h]@9
  const char *v31; // [sp+120h] [bp-28h]@13
  char *v32; // [sp+128h] [bp-20h]@16
  unsigned __int64 v33; // [sp+130h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v34; // [sp+150h] [bp+8h]@1
  const char *v35; // [sp+158h] [bp+10h]@1
  unsigned int v36; // [sp+160h] [bp+18h]@1
  const char *v37; // [sp+168h] [bp+20h]@1

  v37 = szAccount;
  v36 = dwOwner;
  v35 = szType;
  v34 = this;
  v9 = &v12;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v9 = -*********;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v33 = (unsigned __int64)&v12 ^ _security_cookie;
  szTran = 0;
  memset(&v22, 0, 0x10ui64);
  W2M(wszName, &szTran, 0x11u);
  _INVENKEY::_INVENKEY(&v23);
  _INVENKEY::LoadDBKey(&v23, dwK);
  v24 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v23.byTableCode, v23.wItemIndex);
  if ( v24 )
  {
    _strdate(&Buffer);
    v26 = 0;
    _strtime(&v27);
    v28 = 0;
    if ( *v37 )
      v31 = v37;
    else
      v31 = "NULL";
    if ( szTran )
      v32 = &szTran;
    else
      v32 = "NULL";
    v11 = DisplayItemUpgInfo((unsigned __int8)v23.byTableCode, dwU);
    v20 = &v27;
    v19 = &Buffer;
    v18 = v11;
    v17 = dwD;
    v16 = v24->m_strCode;
    v15 = dwRegistSerial;
    v14 = v31;
    LODWORD(v13) = v36;
    CUnmannedTraderUserInfoTable::ServiceLog(
      v34,
      "OWNER_LOGOUT : Type(%s) %s(%u) ID(%s) RegistItemSerial(%u) %s_%u_@%s[null] [%s %s]\r\n",
      v35,
      v32);
  }
  else
  {
    if ( szTran )
      v29 = &szTran;
    else
      v29 = "NULL";
    if ( *v37 )
      v30 = v37;
    else
      v30 = "NULL";
    LODWORD(v20) = v23.wItemIndex;
    LODWORD(v19) = (unsigned __int8)v23.byTableCode;
    v18 = (void *)v35;
    v17 = dwU;
    LODWORD(v16) = dwD;
    v15 = dwK;
    LODWORD(v14) = dwRegistSerial;
    v13 = v29;
    CUnmannedTraderUserInfoTable::Log(
      v34,
      "CUnmannedTraderUserInfoTable::ClearLogLogOutState( dwOwner(%u) szAccount(%s), wszName(%s) dwRegistSerial(%u), dwK("
      "%u), dwD(%u), dwU(%u) )\r\n"
      "\t\tType(%s) _base_fld * p = g_Main.m_tblItemData[kKey.byTableCode(%u)].GetRecord( kKey.wItemIndex(%u) ) NULL!\r\n",
      v36,
      v30);
  }
}
