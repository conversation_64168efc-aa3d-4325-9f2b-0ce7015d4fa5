/*
 * Function: ??$advance@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@_J@std@@YAXAEAV?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@0@_J@Z
 * Address: 0x14037BD30
 */

void __fastcall std::advance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,__int64>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Where, __int64 _Off)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > __formal; // [sp+20h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Wherea; // [sp+40h] [bp+8h]@1
  __int64 _Offa; // [sp+48h] [bp+10h]@1

  _Offa = _Off;
  _Wherea = _Where;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  LOBYTE(v4) = std::_Iter_cat<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>>(&__formal);
  std::_Advance<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,__int64>(
    _Wherea,
    _Offa,
    (std::random_access_iterator_tag)v4->0);
}
