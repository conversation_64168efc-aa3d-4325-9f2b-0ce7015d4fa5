/*
 * Function: ?SQLGetData_Binary_RetErrCode@CRFNewDatabase@@QEAAEPEADAEAGPEAE_K@Z
 * Address: 0x1404DA460
 */

char __fastcall CRFNewDatabase::SQLGetData_Binary_RetErrCode(CRFNewDatabase *this, char *strQuery, unsigned __int16 *ColumnNumber, char *pData, unsigned __int64 tDataSize)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-4A8h]@1
  SQLLEN BufferLength; // [sp+20h] [bp-488h]@5
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-480h]@5
  __int16 v11; // [sp+30h] [bp-478h]@5
  char TargetValue; // [sp+50h] [bp-458h]@5
  SQLLEN v13; // [sp+468h] [bp-40h]@5
  int v14; // [sp+474h] [bp-34h]@4
  int v15; // [sp+478h] [bp-30h]@4
  __int64 v16; // [sp+488h] [bp-20h]@11
  unsigned __int64 v17; // [sp+490h] [bp-18h]@4
  CRFNewDatabase *v18; // [sp+4B0h] [bp+8h]@1
  char *strQuerya; // [sp+4B8h] [bp+10h]@1
  unsigned __int16 *v20; // [sp+4C0h] [bp+18h]@1
  char *Dst; // [sp+4C8h] [bp+20h]@1

  Dst = pData;
  v20 = ColumnNumber;
  strQuerya = strQuery;
  v18 = this;
  v5 = &v8;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  v14 = 0;
  v15 = 0;
  while ( 1 )
  {
    StrLen_or_IndPtr = &v13;
    BufferLength = 1024i64;
    v11 = SQLGetData_0(v18->m_hStmtSelect, *v20, -2, &TargetValue, 1024i64, &v13);
    if ( v11 == 100 )
    {
      ++*v20;
      return 0;
    }
    if ( v13 == -1 )
      break;
    if ( v11 )
      v16 = 1024i64;
    else
      v16 = v13;
    v14 = v16;
    v15 += v16;
    memcpy_s(Dst, tDataSize, &TargetValue, (signed int)v16);
    Dst += v14;
    tDataSize -= v14;
  }
  BufferLength = (SQLLEN)v18->m_hStmtSelect;
  CRFNewDatabase::ErrorMsgLog(v18, v11, strQuerya, "SQLGetData_Binary_RetErrCode", (void *)BufferLength);
  CRFNewDatabase::ErrorAction(v18, v11, v18->m_hStmtSelect);
  if ( v18->m_hStmtSelect )
    SQLCloseCursor_0(v18->m_hStmtSelect);
  return 1;
}
