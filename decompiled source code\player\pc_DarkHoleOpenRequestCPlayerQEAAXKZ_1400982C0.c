/*
 * Function: ?pc_DarkHoleOpenRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400982C0
 */

void __fastcall CPlayer::pc_DarkHoleOpenRequest(CPlayer *this, unsigned int dwItemSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@30
  int v5; // eax@31
  char *v6; // rax@35
  int v7; // eax@38
  __int64 v8; // [sp+0h] [bp-B8h]@1
  unsigned __int16 bUpdate[4]; // [sp+20h] [bp-98h]@35
  _DWORD bSend[2]; // [sp+28h] [bp-90h]@35
  char v11; // [sp+30h] [bp-88h]@4
  __int64 v12; // [sp+38h] [bp-80h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+40h] [bp-78h]@4
  _base_fld *v14; // [sp+48h] [bp-70h]@4
  __int64 v15; // [sp+50h] [bp-68h]@4
  CDarkHole *v16; // [sp+58h] [bp-60h]@4
  _dh_quest_setup *v17; // [sp+60h] [bp-58h]@4
  unsigned int dwHoleSerial; // [sp+68h] [bp-50h]@33
  _darkhole_create_setdata pParam; // [sp+78h] [bp-40h]@34
  char *v20; // [sp+A8h] [bp-10h]@35
  CPlayer *v21; // [sp+C0h] [bp+8h]@1
  unsigned int v22; // [sp+C8h] [bp+10h]@1

  v22 = dwItemSerial;
  v21 = this;
  v2 = &v8;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = 0;
  v12 = 0i64;
  pItem = 0i64;
  v14 = 0i64;
  v15 = 0i64;
  v16 = 0i64;
  v17 = 0i64;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v21->m_id.wIndex) == 99 )
  {
    v11 = 28;
  }
  else if ( IsExistDarkHoleOpenGate() )
  {
    v11 = 3;
  }
  else if ( v21->m_pmTrd.bDTradeMode || v21->m_bCorpse )
  {
    v11 = 16;
  }
  else if ( v21->m_bInGuildBattle )
  {
    v11 = 20;
  }
  else
  {
    v16 = SerarchEmptyDarkHole();
    if ( v16 )
    {
      pItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v21->m_Param.m_dbInven.m_nListNum, v22);
      if ( pItem )
      {
        if ( pItem->m_bLock )
        {
          v11 = 13;
        }
        else if ( pItem->m_byTableCode == 23 )
        {
          v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 23, pItem->m_wItemIndex);
          if ( v14 )
          {
            v17 = CDarkHoleDungeonQuestSetup::GetQuestSetupPtr(
                    (CDarkHoleDungeonQuestSetup *)&g_DarkHoleQuest.vfptr,
                    pItem->m_wItemIndex);
            if ( v17 )
            {
              if ( CDarkHoleDungeonQuest::CanOpenChannel(&g_DarkHoleQuest, v14->m_dwIndex) )
              {
                if ( !v17->bPartyOnly || CPartyPlayer::IsPartyMode(v21->m_pPartyMgr) )
                {
                  v4 = ((int (__fastcall *)(CPlayer *))v21->vfptr->GetLevel)(v21);
                  if ( v4 < v17->nLimitLvMin
                    || (v5 = ((int (__fastcall *)(CPlayer *))v21->vfptr->GetLevel)(v21), v5 > v17->nLimitLvMax) )
                  {
                    v11 = 19;
                  }
                }
                else
                {
                  v11 = 17;
                }
              }
              else
              {
                v11 = 3;
              }
            }
            else
            {
              v11 = 100;
            }
          }
          else
          {
            v11 = 100;
          }
        }
        else
        {
          v11 = 2;
        }
      }
      else
      {
        v11 = 1;
      }
    }
    else
    {
      v11 = 3;
    }
  }
  dwHoleSerial = -1;
  if ( !v11 )
  {
    _darkhole_create_setdata::_darkhole_create_setdata(&pParam);
    pParam.m_pRecordSet = v14;
    pParam.m_pMap = v21->m_pCurMap;
    pParam.m_nLayerIndex = v21->m_wMapLayerIndex;
    CMapData::GetRandPosInRange(v21->m_pCurMap, v21->m_fCurPos, 10, pParam.m_fStartPos);
    pParam.pOpener = v21;
    if ( CDarkHole::Create(v16, &pParam) )
    {
      LOBYTE(bSend[0]) = 0;
      LOBYTE(bUpdate[0]) = 0;
      CPlayer::Emb_AlterDurPoint(v21, 0, pItem->m_byStorageIndex, -1, 0, 0);
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v21->m_ObjID.m_wIndex,
        pItem,
        v21->m_szItemHistoryFileName);
      v20 = v14[2].m_strCode;
      v6 = CPlayerDB::GetCharNameA(&v21->m_Param);
      CLogFile::Write(&stru_1799C8FE8, "OPEN: %s, DARKHOLE:%s", v6, v20);
      dwHoleSerial = v16->m_dwObjSerial;
      v21->m_pPartyMgr->m_pDarkHole = v16;
    }
    else
    {
      v11 = 4;
    }
  }
  if ( v11 )
  {
    CPlayer::SendMsg_DarkHoleOpenFail(v21, v21->m_id.wIndex, v11);
  }
  else
  {
    v7 = v21->m_id.wIndex;
    bSend[0] = dwHoleSerial;
    bUpdate[0] = v16->m_ObjID.m_wIndex;
    CPlayer::SendMsg_DarkHoleOpenResult(v21, v7, v17->bPartyOnly, 0, bUpdate[0], dwHoleSerial);
  }
}
