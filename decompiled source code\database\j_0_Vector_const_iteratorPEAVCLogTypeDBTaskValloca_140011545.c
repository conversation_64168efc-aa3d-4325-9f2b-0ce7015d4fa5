/*
 * Function: j_??0?$_Vector_const_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@PEAPEAVCLogTypeDBTask@@@Z
 * Address: 0x140011545
 */

void __fastcall std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_Ptr)
{
  std::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_const_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    this,
    _Ptr);
}
