/*
 * Function: ?_db_Load_Trade@CMainThread@@AEAAEEKPEAU_TRADE_DB_BASE@@@Z
 * Address: 0x1401A8DA0
 */

char __fastcall CMainThread::_db_Load_Trade(CMainThread *this, char byRace, unsigned int dwSerial, _TRADE_DB_BASE *pTrade)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-6B8h]@1
  _worlddb_trade_info *pTradeData; // [sp+20h] [bp-698h]@4
  char Dst; // [sp+40h] [bp-678h]@4
  char v10[4]; // [sp+48h] [bp-670h]@10
  int v11; // [sp+4Ch] [bp-66Ch]@10
  char v12[4]; // [sp+50h] [bp-668h]@10
  int v13; // [sp+54h] [bp-664h]@10
  __int64 v14; // [sp+58h] [bp-660h]@10
  char v15[4]; // [sp+60h] [bp-658h]@10
  int v16; // [sp+64h] [bp-654h]@10
  int v17[2]; // [sp+68h] [bp-650h]@10
  __int64 v18; // [sp+70h] [bp-648h]@10
  char Src[17]; // [sp+78h] [bp-640h]@10
  char v20[1547]; // [sp+89h] [bp-62Fh]@10
  char v21; // [sp+694h] [bp-24h]@4
  int j; // [sp+698h] [bp-20h]@8
  unsigned __int64 v23; // [sp+6A8h] [bp-10h]@4
  CMainThread *v24; // [sp+6C0h] [bp+8h]@1
  char v25; // [sp+6C8h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+6D0h] [bp+18h]@1
  _TRADE_DB_BASE *v27; // [sp+6D8h] [bp+20h]@1

  v27 = pTrade;
  dwSeriala = dwSerial;
  v25 = byRace;
  v24 = this;
  v4 = &v7;
  for ( i = 428i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = (unsigned __int64)&v7 ^ _security_cookie;
  memset_0(&Dst, 0, 0x648ui64);
  pTradeData = (_worlddb_trade_info *)&Dst;
  v21 = CRFWorldDatabase::Select_Trade(v24->m_pWorldDB, 0, dwSeriala, v25, (_worlddb_trade_info *)&Dst);
  if ( v21 == 1 )
  {
    result = 24;
  }
  else if ( v21 == 2 )
  {
    result = 0;
  }
  else
  {
    for ( j = 0; j < 20; ++j )
    {
      v27->m_List[j].byState = v10[80 * j];
      v27->m_List[j].dwRegistSerial = *(&v11 + 20 * j);
      v27->m_List[j].byInvenIndex = v12[80 * j];
      v27->m_List[j].dwPrice = *(&v13 + 20 * j);
      v27->m_List[j].tStartTime = *(&v14 + 10 * j);
      v27->m_List[j].bySellTurm = v15[80 * j];
      v27->m_List[j].dwBuyerSerial = *(&v16 + 20 * j);
      v27->m_List[j].dwTax = v17[20 * j];
      v27->m_List[j].tResultTime = *(&v18 + 10 * j);
      strcpy_s(v27->m_List[j].wszBuyerName, 0x11ui64, &Src[80 * j]);
      strcpy_s(v27->m_List[j].szBuyerAccount, 0xDui64, &v20[80 * j]);
    }
    result = 0;
  }
  return result;
}
