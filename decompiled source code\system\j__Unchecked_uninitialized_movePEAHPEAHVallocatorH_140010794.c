/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAHPEAHV?$allocator@H@std@@@stdext@@YAPEAHPEAH00AEAV?$allocator@H@std@@@Z
 * Address: 0x140010794
 */

int *__fastcall stdext::_Unchecked_uninitialized_move<int *,int *,std::allocator<int>>(int *_First, int *_Last, int *_Dest, std::allocator<int> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<int *,int *,std::allocator<int>>(_First, _Last, _Dest, _Al);
}
