/*
 * Function: j_??$unchecked_fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@@stdext@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@@Z
 * Address: 0x140004D5E
 */

void __fastcall stdext::unchecked_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val)
{
  stdext::unchecked_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *>(_First, _Count, _Val);
}
