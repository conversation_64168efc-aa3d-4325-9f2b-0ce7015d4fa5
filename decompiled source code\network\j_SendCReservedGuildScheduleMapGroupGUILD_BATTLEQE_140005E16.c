/*
 * Function: j_?Send@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAAXHKEPEAVCReservedGuildSchedulePage@2@@Z
 * Address: 0x140005E16
 */

void __fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Send(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, int n, unsigned int dwVer, char byPage, GUILD_BATTLE::CReservedGuildSchedulePage *pkSelfInfoPage)
{
  GUILD_BATTLE::CReservedGuildScheduleMapGroup::Send(this, n, dwVer, byPage, pkSelfInfoPage);
}
