/**
 * @file CBspEntityLoader.cpp
 * @brief Modern C++20 BSP Entity Loading System Implementation
 * 
 * This file implements the entity loading functionality for BSP (Binary Space Partitioning)
 * maps with proper error handling, memory management, and modern C++ patterns.
 * 
 * Refactored from: decompiled source code/world/LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 * Original Function: CBsp::LoadEntities
 * Original Address: 0x1404F96C0
 * 
 * <AUTHOR> Refactoring Team
 * @date 2024
 * @version 1.0
 */

#include "../Headers/CBspEntityLoader.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <random>
#include <cstring>

// Legacy includes (to be replaced with modern equivalents)
extern "C" {
    // Legacy memory allocation functions
    void* Dmalloc(size_t size);
    void* memset_0(void* dest, int value, size_t count);
    void* memcpy_0(void* dest, const void* src, size_t count);
    
    // Legacy file and warning functions
    void Warning(const char* filename, const char* message);
    void SetMergeFileManager(void* manager);
    
    // Legacy global variables
    extern char byte_184A790F0[];
    extern int dword_184A797AC;
}

// Forward declarations for legacy classes
class CBsp {
public:
    uint32_t mEntityListNum;
    uint32_t mMapEntitiesListNum;
    size_t mTotalAllocSize;
    CEntity* mEntity;
    CParticle* mParticle;
    struct _ENTITY_LIST* mEntityList;
    struct _MAP_ENTITIES_LIST* mMapEntitiesList;
    void* mMapEntityMFM;
};

class CEntity {
public:
    uint32_t mFlag;
    static uint32_t LoadEntity(CEntity* entity, const char* filename, uint32_t flags);
    static void RestoreTexMem(CEntity* entity);
};

class CParticle {
public:
    static uint32_t LoadParticleSPT(CParticle* particle, const char* filename, uint32_t flags);
    static void InitParticle(CParticle* particle);
    static void SetParticleState(CParticle* particle, uint32_t state);
    CParticle();
};

struct _ENTITY_LIST {
    char Name[256];
    bool IsParticle;
    bool IsFileExist;
    uint32_t ShaderID;
    uint32_t Flag;
};

struct _READ_MAP_ENTITIES_LIST {
    uint16_t ID;
    float Scale;
    float Pos[3];
    float RotX;
    float RotY;
    int16_t BBMin[3];
    int16_t BBMax[3];
};

struct _MAP_ENTITIES_LIST {
    uint16_t ID;
    float Pos[3];
    float RotX;
    float RotY;
    float Scale;
    int16_t BBMin[3];
    int16_t BBMax[3];
    float AddFrame;
    CParticle* Particle;
};

namespace NexusProtection {
namespace World {

// Constructor implementations
CBspEntityLoader::CBspEntityLoader() 
    : m_bsp(nullptr), m_entitiesLoaded(false), m_verboseLogging(true) {
    LogMessage("CBspEntityLoader initialized");
}

CBspEntityLoader::CBspEntityLoader(CBsp* bspInstance) 
    : m_bsp(bspInstance), m_entitiesLoaded(false), m_verboseLogging(true) {
    LogMessage("CBspEntityLoader initialized with BSP instance");
}

// Main entity loading function
EntityLoadResult CBspEntityLoader::LoadEntities(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList) {
    return LoadEntitiesWithFlags(bsp, entityList, EntityLoadFlags::RestoreTexture);
}

EntityLoadResult CBspEntityLoader::LoadEntitiesWithFlags(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList, 
                                                        EntityLoadFlags loadFlags) {
    if (!bsp) {
        m_lastError = "BSP instance is null";
        LogMessage(m_lastError, true);
        return EntityLoadResult::InvalidEntityData;
    }

    if (!entityList) {
        m_lastError = "Entity list is null";
        LogMessage(m_lastError, true);
        return EntityLoadResult::InvalidEntityData;
    }

    m_bsp = bsp;
    m_stats = EntityLoadingStats();
    m_loadedEntities.clear();
    m_entityIdToIndex.clear();

    LogMessage("Starting entity loading process...");

    // Validate entity data
    if (!ValidateEntityData(entityList)) {
        m_lastError = "Invalid entity data provided";
        LogMessage(m_lastError, true);
        return EntityLoadResult::InvalidEntityData;
    }

    // Initialize entity arrays if needed
    if (bsp->mEntityListNum > 0) {
        if (!InitializeEntityArrays(bsp, bsp->mEntityListNum)) {
            m_lastError = "Failed to initialize entity arrays";
            LogMessage(m_lastError, true);
            return EntityLoadResult::MemoryAllocationFailed;
        }

        // Setup merge file manager
        SetMergeFileManager(&bsp->mMapEntityMFM);

        // Load individual entities
        for (uint32_t i = 0; i < bsp->mEntityListNum; ++i) {
            EntityInfo entityInfo;
            entityInfo.id = static_cast<uint16_t>(i);
            entityInfo.filename = ProcessEntityName(bsp->mEntityList[i].Name);
            entityInfo.isParticle = bsp->mEntityList[i].IsParticle;
            entityInfo.shaderID = bsp->mEntityList[i].ShaderID;
            entityInfo.flags = bsp->mEntityList[i].Flag;
            entityInfo.type = BspEntityLoaderUtils::GetEntityTypeFromFlags(
                entityInfo.isParticle, entityInfo.shaderID, entityInfo.flags);

            // Build file path
            std::string basePath = std::string(byte_184A790F0);
            std::string fullPath = BuildEntityFilePath(basePath, entityInfo.filename);

            bool loadSuccess = false;
            if (entityInfo.isParticle) {
                loadSuccess = LoadParticleEntity(entityInfo, fullPath);
            } else {
                loadSuccess = LoadStaticEntity(entityInfo, fullPath, loadFlags);
            }

            entityInfo.isFileExist = loadSuccess;
            bsp->mEntityList[i].IsFileExist = loadSuccess;

            m_loadedEntities.push_back(std::move(entityInfo));
            m_entityIdToIndex[entityInfo.id] = m_loadedEntities.size() - 1;

            UpdateStatistics(loadSuccess, entityInfo.type, 
                           BspEntityLoaderUtils::CalculateEntityMemoryUsage(entityInfo.type));
        }

        // Process map entities list
        if (bsp->mMapEntitiesListNum > 0) {
            ProcessMapEntitiesList(bsp, entityList);
        }
    }

    m_stats.Complete();
    m_entitiesLoaded = true;

    LogMessage("Entity loading completed. Success rate: " + 
               std::to_string(m_stats.GetSuccessRate()) + "%");

    return EntityLoadResult::Success;
}

bool CBspEntityLoader::InitializeEntityArrays(CBsp* bsp, uint32_t entityCount) {
    try {
        // Allocate entity array
        bsp->mEntity = static_cast<CEntity*>(Dmalloc(244 * entityCount));
        if (!bsp->mEntity) {
            LogMessage("Failed to allocate entity array", true);
            return false;
        }
        memset_0(bsp->mEntity, 0, 244 * entityCount);

        // Allocate particle array
        bsp->mParticle = static_cast<CParticle*>(Dmalloc(1168 * entityCount));
        if (!bsp->mParticle) {
            LogMessage("Failed to allocate particle array", true);
            return false;
        }
        memset_0(bsp->mParticle, 0, 1168 * entityCount);

        // Update total allocation size
        bsp->mTotalAllocSize += 1412 * entityCount;

        LogMessage("Entity arrays initialized successfully. Count: " + std::to_string(entityCount));
        return true;
    }
    catch (const std::exception& e) {
        LogMessage("Exception during entity array initialization: " + std::string(e.what()), true);
        return false;
    }
}

std::string CBspEntityLoader::BuildEntityFilePath(const std::string& basePath, const std::string& entityName) {
    std::string fullPath = basePath;
    
    // Add path separator if needed
    if (!fullPath.empty() && fullPath.back() != '\\' && fullPath.back() != '/') {
        fullPath += "\\";
    }
    
    fullPath += entityName;
    return fullPath;
}

std::string CBspEntityLoader::ProcessEntityName(const std::string& entityName) {
    if (entityName.empty()) {
        return entityName;
    }

    // Handle backslash prefix (skip first character if it's a backslash)
    if (entityName[0] == '\\') {
        return entityName.substr(1);
    }

    return entityName;
}

bool CBspEntityLoader::LoadParticleEntity(EntityInfo& entityInfo, const std::string& basePath) {
    try {
        size_t entityIndex = entityInfo.id;
        
        if (CParticle::LoadParticleSPT(&m_bsp->mParticle[entityIndex], basePath.c_str(), 0)) {
            CParticle::InitParticle(&m_bsp->mParticle[entityIndex]);
            CParticle::SetParticleState(&m_bsp->mParticle[entityIndex], 1);
            
            LogMessage("Particle entity loaded successfully: " + basePath);
            return true;
        } else {
            std::string errorMsg = basePath + " <- 파티클 파일이 아닙니다, 파티클 spt가 아닙니다.";
            Warning(basePath.c_str(), " <- 파티클 파일이 아닙니다, 파티클 spt가 아닙니다.");
            LogMessage("Failed to load particle entity: " + errorMsg, true);
            return false;
        }
    }
    catch (const std::exception& e) {
        LogMessage("Exception during particle entity loading: " + std::string(e.what()), true);
        return false;
    }
}

bool CBspEntityLoader::LoadStaticEntity(EntityInfo& entityInfo, const std::string& filePath, EntityLoadFlags loadFlags) {
    try {
        size_t entityIndex = entityInfo.id;
        uint32_t flags = static_cast<uint32_t>(loadFlags);
        
        // Add shader flag if needed
        if (entityInfo.shaderID != 0) {
            flags |= static_cast<uint32_t>(EntityLoadFlags::UseShader);
        }

        if (CEntity::LoadEntity(&m_bsp->mEntity[entityIndex], filePath.c_str(), flags)) {
            // Apply entity flags
            if (entityInfo.flags & static_cast<uint32_t>(EntityLoadFlags::EnableFlag)) {
                m_bsp->mEntity[entityIndex].mFlag |= static_cast<uint32_t>(EntityLoadFlags::EnableFlag);
            }

            // Restore texture memory if requested
            if (flags & static_cast<uint32_t>(EntityLoadFlags::RestoreTexture)) {
                CEntity::RestoreTexMem(&m_bsp->mEntity[entityIndex]);
            }

            LogMessage("Static entity loaded successfully: " + filePath);
            return true;
        } else {
            LogMessage("Failed to load static entity: " + filePath, true);
            return false;
        }
    }
    catch (const std::exception& e) {
        LogMessage("Exception during static entity loading: " + std::string(e.what()), true);
        return false;
    }
}

void CBspEntityLoader::ProcessMapEntitiesList(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList) {
    for (uint32_t i = 0; i < bsp->mMapEntitiesListNum; ++i) {
        const auto& readEntity = entityList[i];

        if (readEntity.ID < bsp->mEntityListNum && bsp->mEntityList[readEntity.ID].IsFileExist) {
            auto& mapEntity = bsp->mMapEntitiesList[i];

            // Copy entity data
            mapEntity.ID = readEntity.ID;
            mapEntity.Pos[0] = readEntity.Pos[0];
            mapEntity.Pos[1] = readEntity.Pos[1];
            mapEntity.Pos[2] = readEntity.Pos[2];
            mapEntity.RotX = readEntity.RotX;
            mapEntity.RotY = readEntity.RotY;
            mapEntity.Scale = readEntity.Scale;
            mapEntity.BBMin[0] = readEntity.BBMin[0];
            mapEntity.BBMin[1] = readEntity.BBMin[1];
            mapEntity.BBMin[2] = readEntity.BBMin[2];
            mapEntity.BBMax[0] = readEntity.BBMax[0];
            mapEntity.BBMax[1] = readEntity.BBMax[1];
            mapEntity.BBMax[2] = readEntity.BBMax[2];
            mapEntity.AddFrame = GenerateRandomFrameOffset();
            mapEntity.Particle = nullptr;

            // Handle particle entities
            if (bsp->mEntityList[mapEntity.ID].IsParticle) {
                try {
                    mapEntity.Particle = new CParticle();
                    if (mapEntity.Particle) {
                        memcpy_0(mapEntity.Particle, &bsp->mParticle[mapEntity.ID], 0x490);
                        CParticle::InitParticle(mapEntity.Particle);
                    }
                } catch (const std::exception& e) {
                    LogMessage("Failed to create particle for map entity: " + std::string(e.what()), true);
                    mapEntity.Particle = nullptr;
                }
            }
        } else {
            // Clear invalid entity
            memset_0(&bsp->mMapEntitiesList[i], 0, sizeof(_MAP_ENTITIES_LIST));
        }
    }
}

float CBspEntityLoader::GenerateRandomFrameOffset() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 255);

    int randomValue = dis(gen);
    return static_cast<float>(randomValue) * 0.25f;
}

bool CBspEntityLoader::ValidateEntityData(const struct _READ_MAP_ENTITIES_LIST* entityList) {
    if (!entityList) {
        return false;
    }

    if (!m_bsp) {
        return false;
    }

    // Basic validation - could be expanded
    return true;
}

void CBspEntityLoader::LogMessage(const std::string& message, bool isError) {
    if (m_verboseLogging || isError) {
        std::string prefix = isError ? "[ERROR] " : "[INFO] ";
        std::cout << prefix << "CBspEntityLoader: " << message << std::endl;
    }
}

void CBspEntityLoader::UpdateStatistics(bool success, EntityType entityType, size_t memoryUsed) {
    m_stats.totalEntities++;

    if (success) {
        m_stats.successfullyLoaded++;
        m_stats.totalMemoryAllocated += memoryUsed;

        if (entityType == EntityType::ParticleEntity) {
            m_stats.particleEntities++;
        } else {
            m_stats.staticEntities++;
        }
    } else {
        m_stats.failedToLoad++;
    }
}

bool CBspEntityLoader::CleanupEntities() {
    try {
        m_loadedEntities.clear();
        m_entityIdToIndex.clear();
        m_entitiesLoaded = false;

        LogMessage("Entity cleanup completed successfully");
        return true;
    }
    catch (const std::exception& e) {
        LogMessage("Exception during entity cleanup: " + std::string(e.what()), true);
        return false;
    }
}

std::optional<EntityInfo> CBspEntityLoader::GetEntityById(uint16_t entityId) const {
    auto it = m_entityIdToIndex.find(entityId);
    if (it != m_entityIdToIndex.end() && it->second < m_loadedEntities.size()) {
        return m_loadedEntities[it->second];
    }
    return std::nullopt;
}

std::vector<EntityInfo> CBspEntityLoader::GetEntitiesByType(EntityType type) const {
    std::vector<EntityInfo> result;

    for (const auto& entity : m_loadedEntities) {
        if (entity.type == type) {
            result.push_back(entity);
        }
    }

    return result;
}

std::string CBspEntityLoader::EntityLoadResultToString(EntityLoadResult result) {
    return BspEntityLoaderUtils::EntityLoadResultToString(result);
}

// Factory implementations
std::unique_ptr<CBspEntityLoader> CBspEntityLoaderFactory::CreateDefaultLoader() {
    return std::make_unique<CBspEntityLoader>();
}

std::unique_ptr<CBspEntityLoader> CBspEntityLoaderFactory::CreateLoader(CBsp* bsp) {
    return std::make_unique<CBspEntityLoader>(bsp);
}

// Utility function implementations
namespace BspEntityLoaderUtils {

std::string EntityLoadResultToString(EntityLoadResult result) {
    switch (result) {
        case EntityLoadResult::Success:
            return "Success";
        case EntityLoadResult::MemoryAllocationFailed:
            return "Memory allocation failed";
        case EntityLoadResult::EntityFileNotFound:
            return "Entity file not found";
        case EntityLoadResult::ParticleFileNotFound:
            return "Particle file not found";
        case EntityLoadResult::InvalidEntityData:
            return "Invalid entity data";
        case EntityLoadResult::ShaderLoadFailed:
            return "Shader load failed";
        case EntityLoadResult::ParticleInitFailed:
            return "Particle initialization failed";
        case EntityLoadResult::EntityInitFailed:
            return "Entity initialization failed";
        case EntityLoadResult::UnknownError:
        default:
            return "Unknown error";
    }
}

bool IsSuccessResult(EntityLoadResult result) {
    return result == EntityLoadResult::Success;
}

EntityType GetEntityTypeFromFlags(bool isParticle, uint32_t shaderID, uint32_t flags) {
    if (isParticle) {
        return EntityType::ParticleEntity;
    }

    if (shaderID != 0) {
        return EntityType::ShaderEntity;
    }

    if (flags != 0) {
        return EntityType::AnimatedEntity;
    }

    return EntityType::StaticEntity;
}

size_t CalculateEntityMemoryUsage(EntityType entityType) {
    switch (entityType) {
        case EntityType::ParticleEntity:
            return 1168; // Size from original code
        case EntityType::StaticEntity:
        case EntityType::ShaderEntity:
        case EntityType::AnimatedEntity:
            return 244; // Size from original code
        default:
            return 0;
    }
}

bool ValidateEntityFilePath(const std::string& filePath) {
    return !filePath.empty() && filePath.length() < 260; // MAX_PATH on Windows
}

EntityTransform CreateEntityTransformFromRawData(const void* rawData) {
    // This would need to be implemented based on the actual data structure
    EntityTransform transform;
    // Implementation would parse the raw data and populate the transform
    return transform;
}

} // namespace BspEntityLoaderUtils

} // namespace World
} // namespace NexusProtection
