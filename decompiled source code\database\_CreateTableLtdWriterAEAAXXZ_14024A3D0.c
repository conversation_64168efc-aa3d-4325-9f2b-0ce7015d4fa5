/*
 * Function: ?_CreateTable@LtdWriter@@AEAAXXZ
 * Address: 0x14024A3D0
 */

void __fastcall LtdWriter::_CreateTable(LtdWriter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-E8h]@1
  int nKorTime; // [sp+20h] [bp-C8h]@4
  bool v5; // [sp+24h] [bp-C4h]@5
  bool v6; // [sp+25h] [bp-C3h]@5
  bool v7; // [sp+26h] [bp-C2h]@5
  char Dest; // [sp+40h] [bp-A8h]@5
  char v9; // [sp+41h] [bp-A7h]@5
  unsigned __int64 v10; // [sp+D0h] [bp-18h]@4
  LtdWriter *v11; // [sp+F0h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v3 ^ _security_cookie;
  nKorTime = LtdWriter::_GetLocalDate(v11);
  if ( nKorTime - v11->m_nTblCurSerial >= 1 )
  {
    v5 = 1;
    v6 = 1;
    v7 = 1;
    Dest = 0;
    memset(&v9, 0, 0x7Fui64);
    sprintf(&Dest, "tbl_ltd_%d", (unsigned int)nKorTime);
    if ( !CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->m_pLtdDB->vfptr, &Dest) )
      v5 = CRFDBItemLog::CreateTblLtd(v11->m_pLtdDB, nKorTime);
    sprintf(&Dest, "tbl_ltd_iteminfo_%d", (unsigned int)nKorTime);
    if ( !CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->m_pLtdDB->vfptr, &Dest) )
      v6 = CRFDBItemLog::CreateTblLtd_ItemInfo(v11->m_pLtdDB, nKorTime);
    sprintf(&Dest, "tbl_ltd_expend_%d", (unsigned int)nKorTime);
    if ( !CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->m_pLtdDB->vfptr, &Dest) )
      v7 = CRFDBItemLog::CreateTblLtd_Expend(v11->m_pLtdDB, nKorTime);
    if ( v5 && v6 && v7 )
      v11->m_nTblCurSerial = nKorTime;
    CRFDBItemLog::SetKorTime(v11->m_pLtdDB, nKorTime);
  }
}
