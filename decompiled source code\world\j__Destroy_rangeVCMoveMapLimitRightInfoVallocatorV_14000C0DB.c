/*
 * Function: j_??$_Destroy_range@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x14000C0DB
 */

void __fastcall std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(_First, _Last, _Al, __formal);
}
