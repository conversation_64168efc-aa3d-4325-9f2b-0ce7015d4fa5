/*
 * Function: ?GetPosStartMap@CMapOperation@@QEAAPEAVCMapData@@E_NPEAM@Z
 * Address: 0x140197B90
 */

CMapData *__fastcall CMapOperation::GetPosStartMap(CMapOperation *this, char byRaceCode, bool bRand, float *pfoutPos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CMapData *result; // rax@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  CMapData *v8; // [sp+20h] [bp-18h]@4
  int v9; // [sp+28h] [bp-10h]@6
  CMapOperation *v10; // [sp+40h] [bp+8h]@1
  bool v11; // [sp+50h] [bp+18h]@1
  float *pNewPos; // [sp+58h] [bp+20h]@1

  pNewPos = pfoutPos;
  v11 = bRand;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = CMapOperation::GetStartMap(v10, byRaceCode);
  if ( v8 )
  {
    v9 = 0;
    if ( v11 )
      v9 = rand() % v8->m_nStartDumNum;
    if ( CMapData::GetRandPosInDummy(v8, v8->m_pStartDummy[v9].m_pDumPos, pNewPos, 1) )
      result = v8;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
