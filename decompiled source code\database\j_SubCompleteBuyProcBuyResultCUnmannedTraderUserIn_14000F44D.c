/*
 * Function: j_?SubCompleteBuyProcBuyResult@CUnmannedTraderUserInfoTable@@AEAA_NEPEAU__list@_qry_case_unmandtrader_buy_update_complete@@PEAE@Z
 * Address: 0x14000F44D
 */

bool __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(CUnmannedTraderUserInfoTable *this, char byRet, _qry_case_unmandtrader_buy_update_complete::__list *pUpdateCompleteList, char *byCompleteUpdateNum)
{
  return CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(
           this,
           byRet,
           pUpdateCompleteList,
           byCompleteUpdateNum);
}
