/*
 * Function: _std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::erase_::_1_::dtor$2
 * Address: 0x140395A70
 */

void __fastcall std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(*(std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > **)(a2 + 88));
  }
}
