/*
 * Function: ?ObjectInit@CMainThread@@AEAA_NXZ
 * Address: 0x1401EB650
 */

bool __fastcall CMainThread::ObjectInit(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@17
  CRaceBossMsgController *v4; // rax@101
  CReturnGateController *v5; // rax@101
  CRecallEffectController *v6; // rax@101
  __int64 v7; // rax@105
  LendItemMng *v8; // rax@111
  CNuclearBombMgr *v9; // rax@113
  TimeLimitJadeMng *v10; // rax@115
  CPvpCashMng *v11; // rax@117
  CMonsterEventSet *v12; // rax@122
  __int64 v13; // [sp+0h] [bp-2E8h]@1
  unsigned int dwIndex; // [sp+30h] [bp-2B8h]@4
  _object_id pID; // [sp+44h] [bp-2A4h]@12
  _object_id v16; // [sp+64h] [bp-284h]@20
  _object_id v17; // [sp+84h] [bp-264h]@28
  _object_id v18; // [sp+A4h] [bp-244h]@36
  _object_id v19; // [sp+C4h] [bp-224h]@44
  _object_id v20; // [sp+E4h] [bp-204h]@52
  _object_id v21; // [sp+104h] [bp-1E4h]@60
  _object_id v22; // [sp+124h] [bp-1C4h]@68
  _object_id v23; // [sp+144h] [bp-1A4h]@76
  _object_id v24; // [sp+164h] [bp-184h]@84
  _object_id v25; // [sp+184h] [bp-164h]@92
  void *v26; // [sp+198h] [bp-150h]@16
  void *v27; // [sp+1A0h] [bp-148h]@13
  CMerchant *v28; // [sp+1A8h] [bp-140h]@24
  void *v29; // [sp+1B0h] [bp-138h]@21
  CAnimus *v30; // [sp+1B8h] [bp-130h]@32
  void *v31; // [sp+1C0h] [bp-128h]@29
  struct CGuardTower *v32; // [sp+1C8h] [bp-120h]@40
  void *v33; // [sp+1D0h] [bp-118h]@37
  struct CHolyStone *v34; // [sp+1D8h] [bp-110h]@48
  void *v35; // [sp+1E0h] [bp-108h]@45
  CHolyKeeper *v36; // [sp+1E8h] [bp-100h]@56
  void *v37; // [sp+1F0h] [bp-F8h]@53
  struct CTrap *v38; // [sp+1F8h] [bp-F0h]@64
  void *v39; // [sp+200h] [bp-E8h]@61
  struct CItemBox *v40; // [sp+208h] [bp-E0h]@72
  void *v41; // [sp+210h] [bp-D8h]@69
  CParkingUnit *v42; // [sp+218h] [bp-D0h]@80
  void *v43; // [sp+220h] [bp-C8h]@77
  struct CDarkHole *v44; // [sp+228h] [bp-C0h]@88
  void *v45; // [sp+230h] [bp-B8h]@85
  CGuild *v46; // [sp+238h] [bp-B0h]@96
  void *v47; // [sp+240h] [bp-A8h]@93
  RFEventBase *v48; // [sp+248h] [bp-A0h]@107
  RFEvent_ClassRefine *v49; // [sp+250h] [bp-98h]@104
  CMonsterEventSet *v50; // [sp+260h] [bp-88h]@121
  __int64 v51; // [sp+268h] [bp-80h]@4
  void *v52; // [sp+270h] [bp-78h]@14
  CMerchant *v53; // [sp+278h] [bp-70h]@22
  CAnimus *v54; // [sp+280h] [bp-68h]@30
  struct CGuardTower *v55; // [sp+288h] [bp-60h]@38
  struct CHolyStone *v56; // [sp+290h] [bp-58h]@46
  CHolyKeeper *v57; // [sp+298h] [bp-50h]@54
  struct CTrap *v58; // [sp+2A0h] [bp-48h]@62
  struct CItemBox *v59; // [sp+2A8h] [bp-40h]@70
  CParkingUnit *v60; // [sp+2B0h] [bp-38h]@78
  struct CDarkHole *v61; // [sp+2B8h] [bp-30h]@86
  CGuild *v62; // [sp+2C0h] [bp-28h]@94
  RFEventBase *v63; // [sp+2C8h] [bp-20h]@105
  CMonsterEventSet *v64; // [sp+2D0h] [bp-18h]@122
  CMainThread *v65; // [sp+2F0h] [bp+8h]@1

  v65 = this;
  v1 = &v13;
  for ( i = 184i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v51 = -2i64;
  for ( dwIndex = 0; (signed int)dwIndex < 2532; ++dwIndex )
    CUserDB::Init(&g_UserDB[dwIndex], dwIndex);
  for ( dwIndex = 0; (signed int)dwIndex < 2532; ++dwIndex )
    CPartyPlayer::Init((CPartyPlayer *)&g_PartyPlayer + (signed int)dwIndex, dwIndex);
  for ( dwIndex = 0; (signed int)dwIndex < 2532; ++dwIndex )
  {
    _object_id::_object_id(&pID, 0, 0, dwIndex);
    CPlayer::Init(&g_Player + (signed int)dwIndex, &pID);
  }
  v27 = operator new[](0xB7CAC88ui64);
  if ( v27 )
  {
    *(_DWORD *)v27 = 30000;
    `eh vector constructor iterator'(
      (char *)v27 + 8,
      0x1918ui64,
      30000,
      (void (__cdecl *)(void *))CMonster::CMonster,
      (void (__cdecl *)(void *))CMonster::~CMonster);
    v52 = (char *)v27 + 8;
  }
  else
  {
    v52 = 0i64;
  }
  v26 = v52;
  g_Monster = v52;
  if ( v52 )
  {
    for ( dwIndex = 0; (signed int)dwIndex < 30000; ++dwIndex )
    {
      _object_id::_object_id(&v16, 0, 1, dwIndex);
      CMonster::Init((CMonster *)g_Monster + (signed int)dwIndex, &v16);
    }
    v29 = operator new[](0xE7728ui64);
    if ( v29 )
    {
      *(_DWORD *)v29 = 500;
      `eh vector constructor iterator'(
        (char *)v29 + 8,
        0x768ui64,
        500,
        (void (__cdecl *)(void *))CMerchant::CMerchant,
        (void (__cdecl *)(void *))CMerchant::~CMerchant);
      v53 = (CMerchant *)((char *)v29 + 8);
    }
    else
    {
      v53 = 0i64;
    }
    v28 = v53;
    g_NPC = v53;
    if ( v53 )
    {
      for ( dwIndex = 0; (signed int)dwIndex < 500; ++dwIndex )
      {
        _object_id::_object_id(&v17, 0, 2, dwIndex);
        CMerchant::Init(&g_NPC[dwIndex], &v17);
      }
      v31 = operator new[](0x12CC88ui64);
      if ( v31 )
      {
        *(_DWORD *)v31 = 500;
        `eh vector constructor iterator'(
          (char *)v31 + 8,
          0x9A0ui64,
          500,
          (void (__cdecl *)(void *))CAnimus::CAnimus,
          (void (__cdecl *)(void *))CAnimus::~CAnimus);
        v54 = (CAnimus *)((char *)v31 + 8);
      }
      else
      {
        v54 = 0i64;
      }
      v30 = v54;
      g_Animus = v54;
      if ( v54 )
      {
        for ( dwIndex = 0; (signed int)dwIndex < 500; ++dwIndex )
        {
          _object_id::_object_id(&v18, 0, 3, dwIndex);
          CAnimus::Init(&g_Animus[dwIndex], &v18);
        }
        v33 = operator new[](0xED4E8ui64);
        if ( v33 )
        {
          *(_DWORD *)v33 = 500;
          `eh vector constructor iterator'(
            (char *)v33 + 8,
            0x798ui64,
            500,
            (void (__cdecl *)(void *))CGuardTower::CGuardTower,
            (void (__cdecl *)(void *))CGuardTower::~CGuardTower);
          v55 = (struct CGuardTower *)((char *)v33 + 8);
        }
        else
        {
          v55 = 0i64;
        }
        v32 = v55;
        g_Tower = v55;
        if ( v55 )
        {
          for ( dwIndex = 0; (signed int)dwIndex < 500; ++dwIndex )
          {
            _object_id::_object_id(&v19, 0, 4, dwIndex);
            CGuardTower::Init(&g_Tower[dwIndex], &v19);
          }
          v35 = operator new[](0xF2308ui64);
          if ( v35 )
          {
            *(_DWORD *)v35 = 500;
            `eh vector constructor iterator'(
              (char *)v35 + 8,
              0x7C0ui64,
              500,
              (void (__cdecl *)(void *))CHolyStone::CHolyStone,
              (void (__cdecl *)(void *))CHolyStone::~CHolyStone);
            v56 = (struct CHolyStone *)((char *)v35 + 8);
          }
          else
          {
            v56 = 0i64;
          }
          v34 = v56;
          g_Stone = v56;
          if ( v56 )
          {
            for ( dwIndex = 0; (signed int)dwIndex < 3; ++dwIndex )
            {
              _object_id::_object_id(&v20, 0, 5, dwIndex);
              CHolyStone::Init(&g_Stone[dwIndex], &v20);
            }
            v37 = operator new[](0x858ui64);
            if ( v37 )
            {
              *(_DWORD *)v37 = 1;
              `eh vector constructor iterator'(
                (char *)v37 + 8,
                0x850ui64,
                1,
                (void (__cdecl *)(void *))CHolyKeeper::CHolyKeeper,
                (void (__cdecl *)(void *))CHolyKeeper::~CHolyKeeper);
              v57 = (CHolyKeeper *)((char *)v37 + 8);
            }
            else
            {
              v57 = 0i64;
            }
            v36 = v57;
            g_Keeper = v57;
            if ( v57 )
            {
              for ( dwIndex = 0; (signed int)dwIndex < 1; ++dwIndex )
              {
                _object_id::_object_id(&v21, 0, 6, dwIndex);
                CHolyKeeper::Init(&g_Keeper[dwIndex], &v21);
              }
              v39 = operator new[](0xF29C0ui64);
              if ( v39 )
              {
                *(_DWORD *)v39 = 507;
                `eh vector constructor iterator'(
                  (char *)v39 + 8,
                  0x7A8ui64,
                  507,
                  (void (__cdecl *)(void *))CTrap::CTrap,
                  (void (__cdecl *)(void *))CTrap::~CTrap);
                v58 = (struct CTrap *)((char *)v39 + 8);
              }
              else
              {
                v58 = 0i64;
              }
              v38 = v58;
              g_Trap = v58;
              if ( v58 )
              {
                for ( dwIndex = 0; (signed int)dwIndex < 507; ++dwIndex )
                {
                  _object_id::_object_id(&v22, 0, 7, dwIndex);
                  CTrap::Init(&g_Trap[dwIndex], &v22);
                }
                v41 = operator new[](0x1D0DC8ui64);
                if ( v41 )
                {
                  *(_DWORD *)v41 = 5064;
                  `eh vector constructor iterator'(
                    (char *)v41 + 8,
                    0x178ui64,
                    5064,
                    (void (__cdecl *)(void *))CItemBox::CItemBox,
                    (void (__cdecl *)(void *))CItemBox::~CItemBox);
                  v59 = (struct CItemBox *)((char *)v41 + 8);
                }
                else
                {
                  v59 = 0i64;
                }
                v40 = v59;
                g_ItemBox = v59;
                if ( v59 )
                {
                  for ( dwIndex = 0; (signed int)dwIndex < 5064; ++dwIndex )
                  {
                    _object_id::_object_id(&v23, 1, 0, dwIndex);
                    CItemBox::Init(&g_ItemBox[dwIndex], &v23);
                  }
                  v43 = operator new[](0x10FE8ui64);
                  if ( v43 )
                  {
                    *(_DWORD *)v43 = 300;
                    `eh vector constructor iterator'(
                      (char *)v43 + 8,
                      0xE8ui64,
                      300,
                      (void (__cdecl *)(void *))CParkingUnit::CParkingUnit,
                      (void (__cdecl *)(void *))CParkingUnit::~CParkingUnit);
                    v60 = (CParkingUnit *)((char *)v43 + 8);
                  }
                  else
                  {
                    v60 = 0i64;
                  }
                  v42 = v60;
                  g_ParkingUnit = v60;
                  if ( v60 )
                  {
                    for ( dwIndex = 0; (signed int)dwIndex < 300; ++dwIndex )
                    {
                      _object_id::_object_id(&v24, 1, 2, dwIndex);
                      CParkingUnit::Init(&g_ParkingUnit[dwIndex], &v24);
                    }
                    v45 = operator new[](0x3208ui64);
                    if ( v45 )
                    {
                      *(_DWORD *)v45 = 50;
                      `eh vector constructor iterator'(
                        (char *)v45 + 8,
                        0x100ui64,
                        50,
                        (void (__cdecl *)(void *))CDarkHole::CDarkHole,
                        (void (__cdecl *)(void *))CDarkHole::~CDarkHole);
                      v61 = (struct CDarkHole *)((char *)v45 + 8);
                    }
                    else
                    {
                      v61 = 0i64;
                    }
                    v44 = v61;
                    g_DarkHole = v61;
                    if ( v61 )
                    {
                      for ( dwIndex = 0; (signed int)dwIndex < 50; ++dwIndex )
                      {
                        _object_id::_object_id(&v25, 1, 1, dwIndex);
                        CDarkHole::Init(&g_DarkHole[(signed __int64)(signed int)dwIndex], &v25);
                      }
                      v47 = operator new[](0x3A49E8ui64);
                      if ( v47 )
                      {
                        *(_DWORD *)v47 = 500;
                        `eh vector constructor iterator'(
                          (char *)v47 + 8,
                          0x1DD8ui64,
                          500,
                          (void (__cdecl *)(void *))CGuild::CGuild,
                          (void (__cdecl *)(void *))CGuild::~CGuild);
                        v62 = (CGuild *)((char *)v47 + 8);
                      }
                      else
                      {
                        v62 = 0i64;
                      }
                      v46 = v62;
                      g_Guild = v62;
                      if ( v62 )
                      {
                        for ( dwIndex = 0; (signed int)dwIndex < 500; ++dwIndex )
                          CGuild::Init(&g_Guild[dwIndex], dwIndex);
                        v4 = CRaceBossMsgController::Instance();
                        CRaceBossMsgController::Init(v4);
                        v5 = CReturnGateController::Instance();
                        CReturnGateController::Init(v5, 0x7Eu);
                        v6 = CRecallEffectController::Instance();
                        CRecallEffectController::Init(v6, 0xFDu);
                        CPlayer::SetStaticMember();
                        CAnimus::SetStaticMember();
                        CAttack::SetStaticMember(v65->m_tblEffectData);
                        _WEAPON_PARAM::SetStaticMember(&v65->m_tblItemData[6]);
                        _MASTERY_PARAM::SetStaticMember(v65->m_tblEffectData, &v65->m_tblEffectData[1]);
                        CNetIndexList::SetList(&v65->m_listDQSData, 0x3174u);
                        CNetIndexList::SetList(&v65->m_listDQSDataComplete, 0x3174u);
                        CNetIndexList::SetList(&v65->m_listDQSDataEmpty, 0x3174u);
                        for ( dwIndex = 0; (signed int)dwIndex < 12660; ++dwIndex )
                          CNetIndexList::PushNode_Back(&v65->m_listDQSDataEmpty, dwIndex);
                        v49 = (RFEvent_ClassRefine *)operator new(0x60ui64);
                        if ( v49 )
                        {
                          RFEvent_ClassRefine::RFEvent_ClassRefine(v49);
                          v63 = (RFEventBase *)v7;
                        }
                        else
                        {
                          v63 = 0i64;
                        }
                        v48 = v63;
                        v65->m_pRFEvent_ClassRefine = v63;
                        if ( v65->m_pRFEvent_ClassRefine )
                        {
                          if ( (unsigned __int8)(*(int (__fastcall **)(RFEventBase *))&v65->m_pRFEvent_ClassRefine->vfptr->gap8[0])(v65->m_pRFEvent_ClassRefine) )
                          {
                            v8 = LendItemMng::Instance();
                            if ( LendItemMng::Initialize(v8) )
                            {
                              v9 = CNuclearBombMgr::Instance();
                              if ( CNuclearBombMgr::MissileInit(v9) )
                              {
                                v10 = TimeLimitJadeMng::Instance();
                                if ( TimeLimitJadeMng::Init(v10) )
                                {
                                  v11 = CPvpCashMng::Instance();
                                  if ( CPvpCashMng::LoadData(v11) )
                                  {
                                    if ( DfAIMgr::OnUsStateTBLInit() )
                                    {
                                      v50 = (CMonsterEventSet *)operator new(0x93F00ui64);
                                      if ( v50 )
                                      {
                                        CMonsterEventSet::CMonsterEventSet(v50);
                                        v64 = v12;
                                      }
                                      else
                                      {
                                        v64 = 0i64;
                                      }
                                      g_MonsterEventSet = v64;
                                      result = v64 != 0i64;
                                    }
                                    else
                                    {
                                      MyMessageBox("ObjectInit() Error", " !DfAIMgr::OnUsStateTBLInit() ");
                                      result = 0;
                                    }
                                  }
                                  else
                                  {
                                    MyMessageBox(
                                      "ObjectInit() Error",
                                      "CPvpCashMng::Instance()->LoadData() failed initalize");
                                    result = 0;
                                  }
                                }
                                else
                                {
                                  MyMessageBox("ObjectInit() Error", "TimeLimitJadeMng failed initalize");
                                  result = 0;
                                }
                              }
                              else
                              {
                                MyMessageBox("ObjectInit() Error", "Nuclear Missile failed initalize");
                                result = 0;
                              }
                            }
                            else
                            {
                              MyMessageBox("ObjectInit() Error", "Lend item manager failed initialzie.");
                              result = 0;
                            }
                          }
                          else
                          {
                            result = 0;
                          }
                        }
                        else
                        {
                          result = 0;
                        }
                      }
                      else
                      {
                        result = 0;
                      }
                    }
                    else
                    {
                      result = 0;
                    }
                  }
                  else
                  {
                    result = 0;
                  }
                }
                else
                {
                  result = 0;
                }
              }
              else
              {
                result = 0;
              }
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
