/*
 * Function: ?WorkProc@CBossMonsterScheduleSystem@@MEAAHXZ
 * Address: 0x140419C10
 */

__int64 __fastcall CBossMonsterScheduleSystem::WorkProc(CBossMonsterScheduleSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  ScheduleMSG *pMSG; // [sp+20h] [bp-18h]@6
  CBossMonsterScheduleSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->m_bRunning = 1;
  while ( US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::IsRunning((US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *)&v6->vfptr) )
  {
    pMSG = US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>::PopProc((US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0> > *)&v6->m_MSG_POOL.vfptr);
    if ( pMSG )
    {
      CBossMonsterScheduleSystem::AnalysisMsg(v6, pMSG);
      US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>::PushEmpty(
        (US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0> > *)&v6->m_MSG_POOL.vfptr,
        pMSG);
    }
    Sleep(1u);
  }
  return 0i64;
}
