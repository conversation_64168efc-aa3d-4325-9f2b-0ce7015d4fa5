/*
 * Function: ?_db_complete_event_classrefine@CMainThread@@QEAAXGKEK@Z
 * Address: 0x1401BFC10
 */

void __fastcall CMainThread::_db_complete_event_classrefine(CMainThread *this, unsigned __int16 wSock, unsigned int dwAvatorSerial, char byRefinedCnt, unsigned int dwRefineDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@4
  __int64 v8; // [sp+0h] [bp-58h]@1
  _event_participant_classrefine v9; // [sp+28h] [bp-30h]@4
  __int64 v10; // [sp+48h] [bp-10h]@4

  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9.nSock = wSock;
  v9.nAvatorSerial = dwAvatorSerial;
  v9.nCurRefineCnt = byRefinedCnt;
  v9.dwRefineDate = dwRefineDate;
  v7 = _event_participant_classrefine::size(&v9);
  v10 = *qword_1799C9AF0;
  (*(void (__fastcall **)(_QWORD *, _event_participant_classrefine *, _QWORD))(v10 + 56))(
    qword_1799C9AF0,
    &v9,
    (unsigned int)v7);
}
