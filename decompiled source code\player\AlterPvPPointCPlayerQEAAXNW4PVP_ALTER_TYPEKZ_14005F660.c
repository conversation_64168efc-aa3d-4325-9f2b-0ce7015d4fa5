/*
 * Function: ?AlterPvPPoint@CPlayer@@QEAAXNW4PVP_ALTER_TYPE@@K@Z
 * Address: 0x14005F660
 */

void __usercall CPlayer::AlterPvPPoint(CPlayer *this@<rcx>, long double dAlter@<xmm1>, PVP_ALTER_TYPE AlterType@<r8d>, unsigned int dwDstSerial@<r9d>, double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  double v7; // xmm0_8@4
  __int64 v8; // xmm0_8@8
  int v9; // ecx@12
  char *v10; // rax@17
  __int64 v11; // [sp-20h] [bp-88h]@1
  char *pszFileName; // [sp+0h] [bp-68h]@12
  char *v13; // [sp+8h] [bp-60h]@17
  long double v14; // [sp+10h] [bp-58h]@17
  __int64 v15; // [sp+18h] [bp-50h]@17
  double v16; // [sp+20h] [bp-48h]@4
  double v17; // [sp+28h] [bp-40h]@6
  double v18; // [sp+30h] [bp-38h]@6
  struct CHolyStone *v19; // [sp+38h] [bp-30h]@9
  __int64 v20; // [sp+40h] [bp-28h]@17
  __int64 v21; // [sp+48h] [bp-20h]@17
  char **v22; // [sp+50h] [bp-18h]@17
  CPlayer *v23; // [sp+70h] [bp+8h]@1
  long double v24; // [sp+78h] [bp+10h]@1
  PVP_ALTER_TYPE v25; // [sp+80h] [bp+18h]@1
  unsigned int v26; // [sp+88h] [bp+20h]@1

  v26 = dwDstSerial;
  v25 = AlterType;
  v24 = dAlter;
  v23 = this;
  v5 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v23->m_id.wIndex);
  v16 = a5;
  v7 = dAlter;
  if ( dAlter > 0.0 )
  {
    v7 = dAlter * v16;
    v24 = dAlter * v16;
  }
  CPlayerDB::GetPvPPoint(&v23->m_Param);
  v17 = v7;
  v18 = v7 + v24;
  if ( v7 + v24 < 0.0 )
    v18 = 0.0;
  *(double *)&v8 = v17;
  if ( v17 != v18 )
  {
    v19 = &g_Stone[CPlayerDB::GetRaceCode(&v23->m_Param)];
    if ( v19->m_pCurMap == v23->m_pCurMap && CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 )
      CPlayer::AlterPvpPointLeak(v23, v24);
    CPlayerDB::SetPvPPoint(&v23->m_Param, v18);
    CPvpOrderView::UpdatePvPPoint(&v23->m_kPvpOrderView, v24, v18);
    CPvpOrderView::Notify_OrderView(&v23->m_kPvpOrderView, v23->m_ObjID.m_wIndex);
    v9 = v23->m_ObjID.m_wIndex;
    pszFileName = v23->m_szLvHistoryFileName;
    CMgrAvatorLvHistory::alter_pvp(&CPlayer::s_MgrLvHistory, v9, v24, v23->m_pPartyMgr, v23->m_szLvHistoryFileName);
    CPlayerDB::GetPvPPoint(&v23->m_Param);
    if ( v17 != *(double *)&v8 )
    {
      if ( v23->m_pUserDB )
      {
        CPlayerDB::GetPvPPoint(&v23->m_Param);
        CUserDB::Update_AlterPvPPoint(v23->m_pUserDB, *(long double *)&v8);
      }
      CPlayer::SendMsg_AlterPvPPoint(v23);
    }
    if ( (signed int)v25 < 10 )
    {
      CPlayerDB::GetPvPPoint(&v23->m_Param);
      v20 = v8;
      v21 = v25;
      v22 = sType;
      v10 = CPlayerDB::GetCharNameA(&v23->m_Param);
      v15 = v20;
      v14 = v24;
      v13 = v22[v21];
      LODWORD(pszFileName) = v26;
      CLogFile::Write(
        &stru_1799C9940,
        "%s [ %d ] DST: [ %d ] type: %s  >> pvp : %.0f  last: %.0f",
        v10,
        v23->m_dwObjSerial);
    }
  }
}
