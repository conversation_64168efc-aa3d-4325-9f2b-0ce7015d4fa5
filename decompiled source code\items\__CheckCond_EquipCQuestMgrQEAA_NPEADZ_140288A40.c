/*
 * Function: ?__CheckCond_Equip@CQuestMgr@@QEAA_NPEAD@Z
 * Address: 0x140288A40
 */

bool __fastcall CQuestMgr::__CheckCond_Equip(CQuestMgr *this, char *pszItemCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@4
  char *v7; // [sp+28h] [bp-20h]@6
  _base_fld *v8; // [sp+30h] [bp-18h]@8
  CQuestMgr *v9; // [sp+50h] [bp+8h]@1
  const char *psItemCode; // [sp+58h] [bp+10h]@1

  psItemCode = pszItemCode;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = GetItemTableCode(pszItemCode);
  if ( v6 == -1 )
  {
    result = 0;
  }
  else
  {
    v7 = &v9->m_pMaster->m_Param.m_dbEquip.m_pStorageList[v6].m_bLoad;
    if ( *v7 )
    {
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v6, *(_WORD *)(v7 + 3));
      if ( v8 )
        result = strncmp(v8->m_strCode, psItemCode, 7ui64) == 0;
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
