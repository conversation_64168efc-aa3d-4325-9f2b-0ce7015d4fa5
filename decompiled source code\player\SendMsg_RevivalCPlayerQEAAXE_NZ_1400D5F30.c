/*
 * Function: ?SendMsg_Revival@CPlayer@@QEAAXE_N@Z
 * Address: 0x1400D5F30
 */

void __fastcall CPlayer::SendMsg_Revival(CPlayer *this, char byRet, bool bEquialZone)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  __int16 v7; // [sp+39h] [bp-5Fh]@4
  __int16 pShort; // [sp+3Bh] [bp-5Dh]@4
  char v9; // [sp+41h] [bp-57h]@4
  __int16 v10; // [sp+42h] [bp-56h]@4
  __int16 v11; // [sp+44h] [bp-54h]@4
  __int16 v12; // [sp+46h] [bp-52h]@4
  bool v13; // [sp+48h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v15; // [sp+65h] [bp-33h]@4
  unsigned __int64 v16; // [sp+80h] [bp-18h]@4
  CPlayer *v17; // [sp+A0h] [bp+8h]@1
  bool v18; // [sp+B0h] [bp+18h]@1

  v18 = bEquialZone;
  v17 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byRet;
  v9 = CPlayerDB::GetLevel(&v17->m_Param);
  v11 = CPlayerDB::GetFP(&v17->m_Param);
  v10 = CPlayerDB::GetHP(&v17->m_Param);
  v12 = CPlayerDB::GetSP(&v17->m_Param);
  v7 = CMapOperation::GetMap(&g_MapOper, v17->m_pCurMap);
  FloatToShort(v17->m_fCurPos, &pShort, 3);
  v13 = v18 == 0;
  pbyType = 3;
  v15 = 38;
  CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x11u);
}
