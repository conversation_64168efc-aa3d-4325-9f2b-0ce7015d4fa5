/*
 * Function: ?guild_pop_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x14023B280
 */

void __fastcall CMgrAvatorItemHistory::guild_pop_money(CMgrAvatorItemHistory *this, int n, char *pszGuildName, unsigned int dwPopDalant, unsigned int dwPopGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-58h]@1
  unsigned int v11; // [sp+20h] [bp-38h]@4
  unsigned int v12; // [sp+28h] [bp-30h]@4
  unsigned int v13; // [sp+30h] [bp-28h]@4
  char *v14; // [sp+38h] [bp-20h]@4
  char *v15; // [sp+40h] [bp-18h]@4
  CMgrAvatorItemHistory *v16; // [sp+60h] [bp+8h]@1

  v16 = this;
  v8 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v15 = v16->m_szCurTime;
  v14 = v16->m_szCurDate;
  v13 = dwLeftGold;
  v12 = dwLeftDalant;
  v11 = dwPopGold;
  sprintf(sData, "GUILD MONEY POP: guild(%s) rev(D:%u G%u) $D:%u $G:%u [%s %s]\r\n", pszGuildName);
  CMgrAvatorItemHistory::WriteFile(v16, pszFileName, sData);
}
