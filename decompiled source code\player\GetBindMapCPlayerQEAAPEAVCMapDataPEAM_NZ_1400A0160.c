/*
 * Function: ?GetBindMap@CPlayer@@QEAAPEAVCMapData@@PEAM_N@Z
 * Address: 0x1400A0160
 */

CMapData *__fastcall CPlayer::GetBindMap(CPlayer *this, float *pfPos, bool bIgnoreMapClass)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CMapData *result; // rax@5
  char v6; // al@12
  int v7; // eax@20
  int v8; // eax@22
  char v9; // al@26
  __int64 v10; // [sp+0h] [bp-38h]@1
  CMapData *v11; // [sp+20h] [bp-18h]@6
  CMapData *v12; // [sp+28h] [bp-10h]@6
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  float *pfoutPos; // [sp+48h] [bp+10h]@1
  bool v15; // [sp+50h] [bp+18h]@1

  v15 = bIgnoreMapClass;
  pfoutPos = pfPos;
  v13 = this;
  v3 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v13->m_ObjID.m_byKind )
    return 0i64;
  v11 = 0i64;
  v12 = v13->m_pCurMap;
  if ( CGameObject::GetCurSecNum((CGameObject *)&v13->vfptr) == -1 || v13->m_bMapLoading )
    return 0i64;
  if ( !v15 && v12->m_pMapSet->m_nMapClass )
  {
    v7 = CPlayerDB::GetRaceCode(&v13->m_Param);
    if ( v12->m_nStartDumNum <= v7 )
      return 0i64;
    v8 = CPlayerDB::GetRaceCode(&v13->m_Param);
    if ( !CMapData::GetRandPosInDummy(v12, v12->m_pStartDummy[v8].m_pDumPos, pfoutPos, 1) )
      return 0i64;
    v11 = v12;
  }
  else if ( v13->m_pBindMapData )
  {
    v11 = v13->m_pBindMapData;
    if ( !v13->m_pBindDummyData )
    {
      CUserDB::Update_Bind(v13->m_pUserDB, "0", "0", 0);
      return 0i64;
    }
    if ( !CMapData::GetRandPosInDummy(v13->m_pBindMapData, v13->m_pBindDummyData, pfoutPos, 1) )
    {
      CUserDB::Update_Bind(v13->m_pUserDB, "0", "0", 0);
      return 0i64;
    }
  }
  else
  {
    v6 = CPlayerDB::GetRaceCode(&v13->m_Param);
    v11 = CMapOperation::GetPosStartMap(&g_MapOper, v6, 0, pfoutPos);
    if ( !v11 )
      return 0i64;
  }
  if ( Major_Bind_HQ )
  {
    v9 = CPlayerDB::GetRaceCode(&v13->m_Param);
    result = CMapOperation::GetPosStartMap(&g_MapOper, v9, 0, pfoutPos);
    v11 = result;
  }
  else
  {
    result = v11;
  }
  return result;
}
