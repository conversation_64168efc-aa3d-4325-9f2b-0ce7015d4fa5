/*
 * Function: ?mgr_user_ban@CPlayer@@QEAA_NPEADH0E@Z
 * Address: 0x1400BF3D0
 */

char __fastcall CPlayer::mgr_user_ban(CPlayer *this, char *uszCharName, int iPeriod, char *uszReason, char byBlockType)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@5
  char result; // al@5
  __int64 v9; // [sp+0h] [bp-1A8h]@1
  CUserDB *v10; // [sp+40h] [bp-168h]@4
  char szMsg; // [sp+60h] [bp-148h]@5
  int v12; // [sp+61h] [bp-147h]@5
  char Dst; // [sp+65h] [bp-143h]@5
  char v14; // [sp+6Dh] [bp-13Bh]@5
  char Dest; // [sp+73h] [bp-135h]@5
  char v16; // [sp+93h] [bp-115h]@5
  unsigned int v17; // [sp+B3h] [bp-F5h]@5
  char pbyType; // [sp+D4h] [bp-D4h]@5
  char v19; // [sp+D5h] [bp-D3h]@5
  char _Dest[128]; // [sp+100h] [bp-A8h]@6
  unsigned __int64 v21; // [sp+190h] [bp-18h]@4
  CPlayer *v22; // [sp+1B0h] [bp+8h]@1
  char *pwszName; // [sp+1B8h] [bp+10h]@1
  int v24; // [sp+1C0h] [bp+18h]@1
  const char *Source; // [sp+1C8h] [bp+20h]@1

  Source = uszReason;
  v24 = iPeriod;
  pwszName = uszCharName;
  v22 = this;
  v5 = &v9;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v21 = (unsigned __int64)&v9 ^ _security_cookie;
  v10 = SearchAvatorWithName(g_UserDB, 2532, uszCharName);
  if ( v10 )
  {
    CUserDB::SetChatLock(v10, 1);
    szMsg = byBlockType;
    v12 = v24;
    v17 = v10->m_dwAccountSerial;
    memcpy_0(&Dst, &v10->m_gidGlobal, 8ui64);
    memcpy_0(&v14, &v22->m_pUserDB->m_idWorld, 6ui64);
    strncpy(&Dest, Source, 0x1Fui64);
    v7 = CPlayerDB::GetCharNameW(&v22->m_Param);
    strncpy(&v16, v7, 0x1Fui64);
    pbyType = 50;
    v19 = 110;
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &szMsg, 0x57u);
    result = 1;
  }
  else
  {
    sprintf_s<128>((char (*)[128])_Dest, "%s is not connected", pwszName);
    CPlayer::SendData_ChatTrans(v22, 0, 0xFFFFFFFF, -1, 0, _Dest, -1, 0i64);
    result = 0;
  }
  return result;
}
