/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::_CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor$0
 * Address: 0x140448A10
 */

void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::_CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  if ( *(_QWORD *)(a2 + 64) )
    *(_QWORD *)(a2 + 40) = *(_QWORD *)(a2 + 64) + 96i64;
  else
    *(_QWORD *)(a2 + 40) = 0i64;
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>>::~ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>>(*(CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec> > **)(a2 + 40));
}
