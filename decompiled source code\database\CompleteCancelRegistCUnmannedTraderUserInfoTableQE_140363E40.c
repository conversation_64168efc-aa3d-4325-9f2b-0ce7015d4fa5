/*
 * Function: ?CompleteCancelRegist@CUnmannedTraderUserInfoTable@@QEAAXEPEAD@Z
 * Address: 0x140363E40
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteCancelRegist(CUnmannedTraderUserInfoTable *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  char *wszName; // [sp+20h] [bp-48h]@7
  unsigned int dwRegistSerial; // [sp+28h] [bp-40h]@7
  unsigned int dwK; // [sp+30h] [bp-38h]@7
  unsigned int dwD; // [sp+38h] [bp-30h]@7
  unsigned int dwU; // [sp+40h] [bp-28h]@7
  char *v11; // [sp+50h] [bp-18h]@4
  CUnmannedTraderUserInfo *v12; // [sp+58h] [bp-10h]@4
  CUnmannedTraderUserInfoTable *v13; // [sp+70h] [bp+8h]@1
  char v14; // [sp+78h] [bp+10h]@1
  char *pLoadDataa; // [sp+80h] [bp+18h]@1

  pLoadDataa = pLoadData;
  v14 = byRet;
  v13 = this;
  v3 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = pLoadData;
  v12 = CUnmannedTraderUserInfoTable::FindUser(v13, *(_WORD *)pLoadData, *((_DWORD *)pLoadData + 1));
  if ( !CUnmannedTraderUserInfo::IsNull(v12)
    && *(&g_Player.m_bOper + 50856 * CUnmannedTraderUserInfo::GetIndex(v12))
    && CUnmannedTraderUserInfo::FindOwner(v12) )
  {
    CUnmannedTraderUserInfo::ClearRequest(v12);
    CUnmannedTraderUserInfo::CompleteCancelRegist(v12, v14, pLoadDataa, v13->m_pkLogger);
  }
  else
  {
    dwU = *((_DWORD *)v11 + 14);
    dwD = *((_DWORD *)v11 + 13);
    dwK = *((_DWORD *)v11 + 12);
    dwRegistSerial = *((_DWORD *)v11 + 10);
    wszName = v11 + 21;
    CUnmannedTraderUserInfoTable::ClearLogLogOutState(
      v13,
      "Self Cancel",
      *((_DWORD *)v11 + 1),
      v11 + 8,
      v11 + 21,
      dwRegistSerial,
      dwK,
      dwD,
      dwU);
  }
}
