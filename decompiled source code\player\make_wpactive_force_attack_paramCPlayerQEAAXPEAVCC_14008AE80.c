/*
 * Function: ?make_wpactive_force_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAU_force_fld@@PEAMPEAU_attack_param@@@Z
 * Address: 0x14008AE80
 */

void __fastcall CPlayer::make_wpactive_force_attack_param(CPlayer *this, CCharacter *pDst, _force_fld *pForceFld, float *pfAttackPos, _attack_param *pAP)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@7
  float v8; // xmm0_4@7
  __int64 v9; // [sp+0h] [bp-38h]@1
  float v10; // [sp+20h] [bp-18h]@7
  float v11; // [sp+24h] [bp-14h]@7
  CPlayer *pTarget; // [sp+40h] [bp+8h]@1
  CMonster *v13; // [sp+48h] [bp+10h]@1
  _force_fld *v14; // [sp+50h] [bp+18h]@1
  float *Src; // [sp+58h] [bp+20h]@1

  Src = pfAttackPos;
  v14 = pForceFld;
  v13 = (CMonster *)pDst;
  pTarget = this;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  pAP->pDst = pDst;
  if ( pDst )
    pAP->nPart = CCharacter::GetAttackRandomPart(pDst);
  else
    pAP->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pTarget->vfptr);
  pAP->nTol = v14->m_nProperty;
  v10 = (float)pTarget->m_pmWpn.nMaMinAF;
  v7 = v10;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  pAP->nMinAF = (signed int)ffloor((float)(v10 * v7) + (float)pTarget->m_pmMst.m_mtyStaff);
  v11 = (float)pTarget->m_pmWpn.nMaMaxAF;
  v8 = v11;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  pAP->nMaxAF = (signed int)ffloor((float)(v11 * v8) + (float)pTarget->m_pmMst.m_mtyStaff);
  pAP->nMinSel = pTarget->m_pmWpn.byMaMinSel;
  pAP->nMaxSel = pTarget->m_pmWpn.byMaMaxSel;
  pAP->pFld = (_base_fld *)v14;
  pAP->byEffectCode = 1;
  pAP->nLevel = pTarget->m_pmWpn.nActiveEffLvl;
  pAP->nMastery = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 4, v14->m_nMastIndex);
  memcpy_0(pAP->fArea, Src, 0xCui64);
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v13 && v13->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v13, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
}
