/*
 * Function: ??E?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA?AV01@H@Z
 * Address: 0x14031D940
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator++(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, int __formal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-68h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> v7; // [sp+28h] [bp-40h]@4
  int v8; // [sp+54h] [bp-14h]@4
  __int64 v9; // [sp+58h] [bp-10h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that; // [sp+70h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v11; // [sp+78h] [bp+10h]@1

  v11 = result;
  __that = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = -2i64;
  v8 = 0;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    &v7,
    __that);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator++(__that);
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v11,
    &v7);
  v8 |= 1u;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(&v7);
  return v11;
}
