/*
 * Function: ?DatafileInit@CPotionMgr@@QEAA_NXZ
 * Address: 0x14039C7B0
 */

char __fastcall CPotionMgr::DatafileInit(CPotionMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-D8h]@1
  char pszErrMsg; // [sp+30h] [bp-A8h]@4
  unsigned __int64 v6; // [sp+C0h] [bp-18h]@4
  CPotionMgr *v7; // [sp+E0h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( CRecordData::ReadRecord(&v7->m_tblPotionEffectData, ".\\script\\PotionItemEffect.dat", 0x490u, &pszErrMsg) )
  {
    if ( CRecordData::ReadRecord(&v7->m_tblPotionCheckData, ".\\script\\CheckPotionEffect.dat", 0xDCu, &pszErrMsg) )
    {
      result = 1;
    }
    else
    {
      MyMessageBox("DatafileInit", &pszErrMsg);
      result = 0;
    }
  }
  else
  {
    MyMessageBox("DatafileInit", &pszErrMsg);
    result = 0;
  }
  return result;
}
