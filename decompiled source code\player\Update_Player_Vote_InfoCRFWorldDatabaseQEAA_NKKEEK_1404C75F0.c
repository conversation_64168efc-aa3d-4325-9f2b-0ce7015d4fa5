/*
 * Function: ?Update_Player_Vote_Info@CRFWorldDatabase@@QEAA_NKKEEK@Z
 * Address: 0x1404C75F0
 */

bool __fastcall CRFWorldDatabase::Update_Player_Vote_Info(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwAccPlayTime, char IsVote, char VoteEnable, unsigned int dwScanerData)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-478h]@1
  unsigned int v10; // [sp+20h] [bp-458h]@4
  int v11; // [sp+28h] [bp-450h]@4
  int v12; // [sp+30h] [bp-448h]@4
  unsigned int v13; // [sp+38h] [bp-440h]@4
  char DstBuf; // [sp+50h] [bp-428h]@4
  char v15; // [sp+51h] [bp-427h]@4
  unsigned __int64 v16; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+480h] [bp+8h]@1

  v17 = this;
  v6 = &v9;
  for ( i = 284i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v16 = (unsigned __int64)&v9 ^ _security_cookie;
  DstBuf = 0;
  memset(&v15, 0, 0x3FFui64);
  v13 = dwScanerData;
  v12 = (unsigned __int8)VoteEnable;
  v11 = (unsigned __int8)IsVote;
  v10 = dwAccPlayTime;
  sprintf_s(&DstBuf, 0x400ui64, "{ CALL pUpdate_Supplement_Ex_20080609( %d, %d, %d, %d, %d) }", dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v17->vfptr, &DstBuf, 1);
}
