/*
 * Function: ?SetPlayerOut@CGuildRoomInfo@@QEAA_NHKH@Z
 * Address: 0x1402E6310
 */

char __fastcall CGuildRoomInfo::SetPlayerOut(CGuildRoomInfo *this, int n, unsigned int dwCharSerial, int iMemberIdx)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int64 v7; // rax@9
  __int64 v8; // rax@17
  __int64 v9; // rax@21
  char *v10; // rax@21
  __int64 v11; // [sp+0h] [bp-98h]@1
  float *pfStartPos; // [sp+20h] [bp-78h]@21
  int j; // [sp+30h] [bp-68h]@6
  char v14; // [sp+34h] [bp-64h]@17
  float pNewPos; // [sp+48h] [bp-50h]@17
  CPlayer *v16; // [sp+68h] [bp-30h]@19
  unsigned __int64 v17; // [sp+70h] [bp-28h]@9
  __int64 v18; // [sp+78h] [bp-20h]@17
  struct CMapData * near **v19; // [sp+80h] [bp-18h]@17
  CGuildRoomInfo *v20; // [sp+A0h] [bp+8h]@1
  int v21; // [sp+A8h] [bp+10h]@1
  unsigned int v22; // [sp+B0h] [bp+18h]@1

  v22 = dwCharSerial;
  v21 = n;
  v20 = this;
  v4 = &v11;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( iMemberIdx < 100 )
  {
    j = 0;
    if ( iMemberIdx == -1 )
    {
      for ( j = 0; ; ++j )
      {
        v17 = j;
        v7 = std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::size(&v20->m_vecRoomMember);
        if ( v17 >= v7 )
          break;
        if ( std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::operator[](&v20->m_vecRoomMember, j)->bIn
          && std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::operator[](&v20->m_vecRoomMember, j)->iCharIdx == v21
          && std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::operator[](&v20->m_vecRoomMember, j)->dwCharSerial == v22 )
        {
          std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::operator[](&v20->m_vecRoomMember, j)->bIn = 0;
          break;
        }
      }
    }
    else
    {
      std::vector<RoomCharInfo,std::allocator<RoomCharInfo>>::operator[](&v20->m_vecRoomMember, iMemberIdx)->bIn = 0;
    }
    v14 = 0;
    v8 = v20->m_byRace;
    v18 = v20->m_byRace;
    v19 = &CGuildRoomInfo::sm_neutal_map;
    if ( CMapData::GetRandPosInDummy(
           (CMapData *)(&CGuildRoomInfo::sm_neutal_map)[v18],
           (_dummy_position *)(&CGuildRoomInfo::sm_neutral_hq_dummy)[v8],
           &pNewPos,
           1) )
    {
      v16 = &g_Player + v21;
      if ( v16->m_bOper )
      {
        v9 = v20->m_byRace;
        pfStartPos = &pNewPos;
        CPlayer::OutOfMap(v16, (CMapData *)(&CGuildRoomInfo::sm_neutal_map)[v9], 0, 3, &pNewPos);
        v14 = 10;
        v10 = (char *)(&CGuildRoomInfo::sm_neutal_map)[v20->m_byRace][93];
        pfStartPos = &pNewPos;
        CPlayer::SendMsg_GuildRoomOutResult(v16, 10, *v10, 0, &pNewPos);
        result = 1;
      }
      else
      {
        v14 = 7;
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
