#pragma once

/**
 * @file CDatabaseManager.h
 * @brief Modern C++20 Database Manager class definition
 * 
 * Refactored from decompiled sources:
 * - _GameDataBaseInitCMainThreadAEAA_NXZ_1401ED4E0.c (270 lines)
 * - DatabaseInitCMainThreadAEAA_NPEAD0Z_1401ED230.c (94 lines)
 * - InitDBCLogTypeDBTaskManagerQEAA_NPEBD0Z_1402C2E50.c
 * 
 * This file provides a modern, centralized database management system
 * that coordinates all database operations and manager initializations.
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <future>

// Forward declarations
class CRFNewDatabase;
class CRFWorldDatabase;
class CUserDB;
class CLogTypeDBTaskManager;

// Manager forward declarations
class CMoneySupplyMgr;
class CPvpUserAndGuildRankingSystem;
class CGuildBattleController;
class CTotalGuildRankManager;
class CWeeklyGuildRankManager;
class AutoMineMachineMng;
class CUnmannedTraderTaxRateManager;
class AutominePersonalMgr;
class CUnmannedTraderController;
class CGuildRoomSystem;
class CPostSystemManager;
class CItemStoreManager;
class CHonorGuild;
class CRaceBossWinRate;
class CCashDBWorkManager;
class CGoldenBoxItemMgr;

namespace NexusProtection {
namespace Database {

/**
 * @brief Database initialization result enumeration
 */
enum class DatabaseInitResult : uint8_t {
    Success = 0,
    ConnectionFailed,
    ConfigurationError,
    ManagerInitFailed,
    TableCreationFailed,
    DataLoadFailed,
    SystemError
};

/**
 * @brief Database manager state enumeration
 */
enum class DatabaseManagerState : uint8_t {
    Uninitialized = 0,
    Initializing,
    Connected,
    ManagersLoading,
    Ready,
    Error,
    Shutdown
};

/**
 * @brief Database connection configuration
 */
struct DatabaseConfig {
    std::string odbcName;
    std::string serverIP;
    std::string accountName;
    std::string password;
    uint16_t port{1433};
    uint32_t connectionTimeout{30};
    uint32_t queryTimeout{60};
    bool enableConnectionPooling{true};
    uint32_t maxConnections{10};
    bool enableAutoReconnect{true};
    uint32_t reconnectInterval{5};
    
    DatabaseConfig() = default;
    
    bool IsValid() const {
        return !odbcName.empty() && !serverIP.empty() && 
               !accountName.empty() && !password.empty();
    }
};

/**
 * @brief Manager initialization info
 */
struct ManagerInfo {
    std::string name;
    std::function<bool()> initFunction;
    std::function<bool()> loadFunction;
    std::vector<std::string> dependencies;
    bool isRequired{true};
    bool isInitialized{false};
    std::chrono::steady_clock::time_point initTime;
    std::string lastError;
    
    ManagerInfo() = default;
    ManagerInfo(const std::string& n, std::function<bool()> init, std::function<bool()> load)
        : name(n), initFunction(std::move(init)), loadFunction(std::move(load)) {}
};

/**
 * @brief Database statistics
 */
struct DatabaseStats {
    std::atomic<uint64_t> totalQueries{0};
    std::atomic<uint64_t> successfulQueries{0};
    std::atomic<uint64_t> failedQueries{0};
    std::atomic<uint64_t> reconnections{0};
    std::atomic<uint32_t> activeConnections{0};
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point lastActivity;
    
    DatabaseStats() {
        auto now = std::chrono::steady_clock::now();
        startTime = now;
        lastActivity = now;
    }
    
    double GetSuccessRate() const {
        uint64_t total = totalQueries.load();
        return total > 0 ? (static_cast<double>(successfulQueries.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Modern C++20 Database Manager class
 * 
 * This class provides centralized database management and coordinates
 * all database operations and manager initializations for the game server.
 */
class CDatabaseManager {
public:
    // Constructor and Destructor
    CDatabaseManager();
    virtual ~CDatabaseManager();

    // Disable copy constructor and assignment operator
    CDatabaseManager(const CDatabaseManager&) = delete;
    CDatabaseManager& operator=(const CDatabaseManager&) = delete;

    // Enable move constructor and assignment operator
    CDatabaseManager(CDatabaseManager&&) noexcept = default;
    CDatabaseManager& operator=(CDatabaseManager&&) noexcept = default;

    /**
     * @brief Initialize database system
     * 
     * Modern implementation of DatabaseInit method.
     * Sets up database connections and prepares for manager initialization.
     * 
     * @param config Database configuration
     * @return DatabaseInitResult indicating success or failure
     */
    DatabaseInitResult Initialize(const DatabaseConfig& config);

    /**
     * @brief Initialize all game data managers
     * 
     * Modern implementation of _GameDataBaseInit method.
     * Coordinates initialization of all game managers and systems.
     * 
     * @return DatabaseInitResult indicating success or failure
     */
    DatabaseInitResult InitializeGameData();

    /**
     * @brief Shutdown database system
     * 
     * Safely shuts down all database connections and managers.
     */
    void Shutdown();

    /**
     * @brief Update database system
     * 
     * Called periodically to maintain connections and update statistics.
     * 
     * @param deltaTime Time elapsed since last update
     */
    void Update(float deltaTime);

    // State Management
    DatabaseManagerState GetState() const { return m_state; }
    bool IsReady() const { return m_state == DatabaseManagerState::Ready; }
    bool IsConnected() const { return m_state >= DatabaseManagerState::Connected; }

    // Database Access
    std::shared_ptr<CRFWorldDatabase> GetWorldDatabase() const { return m_worldDatabase; }
    std::shared_ptr<CUserDB> GetUserDatabase() const { return m_userDatabase; }
    std::shared_ptr<CLogTypeDBTaskManager> GetLogDatabase() const { return m_logDatabase; }

    // Configuration
    const DatabaseConfig& GetConfig() const { return m_config; }
    void SetConfig(const DatabaseConfig& config) { m_config = config; }

    // Statistics
    const DatabaseStats& GetStats() const { return m_stats; }
    void ResetStats();

    // Manager Registration
    void RegisterManager(const std::string& name, std::function<bool()> initFunc, 
                        std::function<bool()> loadFunc, const std::vector<std::string>& deps = {});
    bool IsManagerInitialized(const std::string& name) const;
    std::vector<std::string> GetInitializedManagers() const;
    std::vector<std::string> GetFailedManagers() const;

    // Health Monitoring
    bool IsHealthy() const;
    std::string GetHealthReport() const;
    void RunHealthCheck();

    // Error Handling
    const std::string& GetLastError() const { return m_lastError; }
    std::vector<std::string> GetErrorHistory() const;

    // Singleton Access (for legacy compatibility)
    static CDatabaseManager& Instance();
    static void SetInstance(std::unique_ptr<CDatabaseManager> instance);

protected:
    // Core state
    std::atomic<DatabaseManagerState> m_state{DatabaseManagerState::Uninitialized};
    DatabaseConfig m_config;
    DatabaseStats m_stats;
    
    // Database instances
    std::shared_ptr<CRFWorldDatabase> m_worldDatabase;
    std::shared_ptr<CUserDB> m_userDatabase;
    std::shared_ptr<CLogTypeDBTaskManager> m_logDatabase;
    
    // Manager management
    std::unordered_map<std::string, ManagerInfo> m_managers;
    std::vector<std::string> m_initializationOrder;
    
    // Synchronization
    mutable std::mutex m_managerMutex;
    mutable std::mutex m_statsMutex;
    mutable std::mutex m_errorMutex;
    
    // Error handling
    std::string m_lastError;
    std::vector<std::string> m_errorHistory;
    static constexpr size_t MAX_ERROR_HISTORY = 100;
    
    // Timing
    std::chrono::steady_clock::time_point m_lastUpdate;
    std::chrono::steady_clock::time_point m_lastHealthCheck;
    
    // Singleton instance
    static std::unique_ptr<CDatabaseManager> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Set last error message
     * @param error Error message
     */
    void SetLastError(const std::string& error);

    /**
     * @brief Initialize database connections
     * @return true if successful, false otherwise
     */
    bool InitializeDatabaseConnections();

    /**
     * @brief Initialize world database
     * @return true if successful, false otherwise
     */
    bool InitializeWorldDatabase();

    /**
     * @brief Initialize user database
     * @return true if successful, false otherwise
     */
    bool InitializeUserDatabase();

    /**
     * @brief Initialize log database
     * @return true if successful, false otherwise
     */
    bool InitializeLogDatabase();

    /**
     * @brief Register all default managers
     */
    void RegisterDefaultManagers();

    /**
     * @brief Initialize managers in dependency order
     * @return true if successful, false otherwise
     */
    bool InitializeManagersInOrder();

    /**
     * @brief Calculate initialization order based on dependencies
     * @return true if successful, false if circular dependencies detected
     */
    bool CalculateInitializationOrder();

    /**
     * @brief Initialize economy system
     * @return true if successful, false otherwise
     */
    bool InitializeEconomySystem();

    /**
     * @brief Load economy history data
     * @return true if successful, false otherwise
     */
    bool LoadEconomyHistory();

    /**
     * @brief Validate database configuration
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool ValidateConfig(const DatabaseConfig& config) const;

    /**
     * @brief Update statistics
     */
    void UpdateStatistics();

    /**
     * @brief Check for failed connections and attempt reconnection
     */
    void CheckAndReconnect();
};

/**
 * @brief Database Manager Factory
 */
class CDatabaseManagerFactory {
public:
    static std::unique_ptr<CDatabaseManager> CreateDatabaseManager();
    static std::unique_ptr<CDatabaseManager> CreateDatabaseManager(const DatabaseConfig& config);
};

} // namespace Database
} // namespace NexusProtection
