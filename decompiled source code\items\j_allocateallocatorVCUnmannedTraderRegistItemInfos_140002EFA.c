/*
 * Function: j_?allocate@?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@QEAAPEAVCUnmannedTraderRegistItemInfo@@_K@Z
 * Address: 0x140002EFA
 */

CUnmannedTraderRegistItemInfo *__fastcall std::allocator<CUnmannedTraderRegistItemInfo>::allocate(std::allocator<CUnmannedTraderRegistItemInfo> *this, unsigned __int64 _Count)
{
  return std::allocator<CUnmannedTraderRegistItemInfo>::allocate(this, _Count);
}
