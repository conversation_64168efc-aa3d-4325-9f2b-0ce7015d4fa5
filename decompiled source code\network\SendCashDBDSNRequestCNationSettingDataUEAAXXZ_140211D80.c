/*
 * Function: ?SendCashDB<PERSON>NRequest@CNationSettingData@@UEAAXXZ
 * Address: 0x140211D80
 */

void __fastcall CNationSettingData::SendCashDBDSNRequest(CNationSettingData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4

  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pbyType = 1;
  v7 = 31;
  v3 = _cashdb_setting_request_wrac::size((_cashdb_setting_request_wrac *)&szMsg);
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &szMsg, v3);
}
