/*
 * Function: ?Update_Start_NpcQuest_History@CRFWorldDatabase@@QEAA_NKPEADE0_J@Z
 * Address: 0x1404C3850
 */

bool __fastcall CRFWorldDatabase::Update_Start_NpcQuest_History(CRFWorldDatabase *this, unsigned int dwSerial, char *szQuestCode, char byLevel, char *szTime, __int64 nEndTime)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-178h]@1
  char *v10; // [sp+20h] [bp-158h]@4
  int v11; // [sp+28h] [bp-150h]@4
  char *v12; // [sp+30h] [bp-148h]@4
  __int64 v13; // [sp+38h] [bp-140h]@4
  char DstBuf; // [sp+50h] [bp-128h]@4
  unsigned __int64 v15; // [sp+160h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+180h] [bp+8h]@1

  v16 = this;
  v6 = &v9;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = (unsigned __int64)&v9 ^ _security_cookie;
  v13 = nEndTime;
  v12 = szTime;
  v11 = (unsigned __int8)byLevel;
  v10 = szQuestCode;
  sprintf_s(&DstBuf, 0x100ui64, "{ CALL pUpdate_Start_Npc_Quest_History_Type1( %d, '%s', %d, '%s', %I64d) }", dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v16->vfptr, &DstBuf, 1);
}
