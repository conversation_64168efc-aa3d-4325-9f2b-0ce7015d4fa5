/*
 * Function: ?SetNextEvnet@CGuildBattleScheduleManager@GUILD_BATTLE@@AEAAXXZ
 * Address: 0x1403DD6C0
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::SetNextEvnet(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-28h]@1

  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::SetNextEvent(v3);
}
