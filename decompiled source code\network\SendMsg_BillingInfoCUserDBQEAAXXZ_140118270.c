/*
 * Function: ?SendMsg_BillingInfo@CUserDB@@QEAAXXZ
 * Address: 0x140118270
 */

void __fastcall CUserDB::SendMsg_BillingInfo(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  int v5; // [sp+3Ah] [bp-4Eh]@4
  char Dst; // [sp+3Eh] [bp-4Ah]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v8; // [sp+65h] [bp-23h]@4
  CUserDB *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_WORD *)szMsg = v9->m_BillingInfo.iType;
  v5 = v9->m_BillingInfo.lRemainTime;
  memcpy_0(&Dst, &v9->m_BillingInfo.stEndDate, 0x10ui64);
  pbyType = 29;
  v8 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_idWorld.wIndex, &pbyType, szMsg, 0x16u);
}
