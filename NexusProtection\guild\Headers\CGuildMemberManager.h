/**
 * @file CGuildMemberManager.h
 * @brief Modern C++20 Guild Member Manager
 * 
 * This file provides comprehensive guild member management with modern C++20 patterns,
 * proper member lifecycle management, authentication, permissions, and communication.
 * 
 * Refactored from decompiled sources:
 * - SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.c (110 lines)
 * - LoginMemberCGuildQEAAPEAU_guild_member_infoKPEAVCP_140253750.c (37 lines)
 * - SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.c (complex messaging)
 * - SendMsg_GuildMemberLogoffCGuildQEAAXKZ_140257250.c (member logout)
 * - SendMsg_LeaveMemberCGuildQEAAXK_N0Z_140255A30.c (member leaving)
 * - DB_Update_GuildMasterCGuildQEAA_NPEAU_guild_member_140252BD0.c (master updates)
 */

#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>
#include <optional>
#include <array>

// Forward declarations for legacy compatibility
extern "C" {
    class CGuild;
    class CPlayer;
    class CRFWorldDatabase;
    
    struct _guild_member_info {
        uint32_t dwSerial;
        char wszName[50];
        uint8_t byLv;
        uint32_t dwPvpPoint;
        uint8_t byClassInGuild;
        CPlayer* pPlayer;
        bool IsFill() const;
    };
    
    struct _guild_master_info {
        uint32_t dwSerial;
        uint8_t byPrevGrade;
        _guild_member_info* pMember;
        void init();
        bool IsFill() const;
    };
    
    struct _io_money_data {
        uint64_t timestamp;
        double amount;
        uint8_t type;
        char description[64];
    };
    
    extern CRFWorldDatabase* pkDB;
}

namespace NexusProtection::Guild {

/**
 * @brief Guild member operation result enumeration
 */
enum class GuildMemberResult : uint8_t {
    Success = 0,
    MemberNotFound,
    InvalidParameters,
    PermissionDenied,
    GuildFull,
    AlreadyMember,
    DatabaseError,
    SystemError
};

/**
 * @brief Guild member class/rank enumeration
 */
enum class GuildMemberClass : uint8_t {
    Member = 0,        // Regular member
    Committee = 1,     // Committee member
    Master = 2         // Guild master
};

/**
 * @brief Guild member status enumeration
 */
enum class GuildMemberStatus : uint8_t {
    Offline = 0,
    Online,
    InBattle,
    InRoom,
    Away
};

/**
 * @brief Modern guild member information structure
 */
struct GuildMemberInfo {
    uint32_t memberSerial = 0;
    std::string memberName;
    uint8_t level = 0;
    uint32_t pvpPoints = 0;
    GuildMemberClass memberClass = GuildMemberClass::Member;
    GuildMemberStatus status = GuildMemberStatus::Offline;
    CPlayer* playerPtr = nullptr;
    
    // Additional modern features
    std::chrono::steady_clock::time_point joinTime;
    std::chrono::steady_clock::time_point lastLoginTime;
    std::chrono::steady_clock::time_point lastActivityTime;
    uint16_t mapCode = 0;
    uint16_t regionIndex = 0;
    
    // Statistics
    uint32_t totalLogins = 0;
    uint32_t totalBattles = 0;
    uint32_t totalContributions = 0;
    
    GuildMemberInfo() : joinTime(std::chrono::steady_clock::now()) {}
    
    bool IsValid() const {
        return memberSerial != 0 && !memberName.empty();
    }
    
    bool IsOnline() const {
        return status != GuildMemberStatus::Offline && playerPtr != nullptr;
    }
    
    std::string ToString() const;
    void UpdateActivity();
};

/**
 * @brief Guild master information structure
 */
struct GuildMasterInfo {
    uint32_t masterSerial = 0;
    uint8_t previousGrade = 0;
    std::shared_ptr<GuildMemberInfo> memberInfo;
    std::chrono::steady_clock::time_point lastConnectionCheck;
    bool canElectNewMaster = false;
    
    bool IsValid() const {
        return masterSerial != 0 && memberInfo && memberInfo->IsValid();
    }
};

/**
 * @brief Guild financial transaction record
 */
struct GuildMoneyTransaction {
    uint64_t timestamp;
    double amount;
    uint8_t transactionType;
    std::string description;
    uint32_t memberSerial;
    
    GuildMoneyTransaction(double amt, uint8_t type, const std::string& desc, uint32_t member = 0)
        : timestamp(std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count())
        , amount(amt), transactionType(type), description(desc), memberSerial(member) {}
};

/**
 * @brief Guild setup configuration
 */
struct GuildSetupData {
    uint32_t guildSerial;
    uint8_t guildGrade;
    uint8_t guildRace;
    std::string guildName;
    std::string greetingMessage;
    uint32_t emblemBack;
    uint32_t emblemMark;
    
    // Financial data
    double totalDalant = 0.0;
    double totalGold = 0.0;
    
    // Battle statistics
    uint32_t battleWins = 0;
    uint32_t battleDraws = 0;
    uint32_t battleLosses = 0;
    
    // Member data
    std::vector<GuildMemberInfo> members;
    GuildMasterInfo masterInfo;
    std::vector<GuildMoneyTransaction> moneyHistory;
    
    bool IsValid() const {
        return guildSerial != 0 && !guildName.empty() && 
               guildRace < 3 && !members.empty() && masterInfo.IsValid();
    }
};

/**
 * @brief Guild member statistics
 */
struct GuildMemberStats {
    std::atomic<uint64_t> totalMemberLogins{0};
    std::atomic<uint64_t> totalMemberLogouts{0};
    std::atomic<uint64_t> totalMemberJoins{0};
    std::atomic<uint64_t> totalMemberLeaves{0};
    std::atomic<uint64_t> totalMasterChanges{0};
    std::atomic<uint64_t> totalPermissionChanges{0};
    std::atomic<uint64_t> activeMembersOnline{0};
    std::atomic<uint64_t> messagesSent{0};
    std::chrono::steady_clock::time_point startTime;
    
    GuildMemberStats() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetAverageOnlineMembers() const {
        uint64_t total = totalMemberLogins.load();
        return total > 0 ? static_cast<double>(activeMembersOnline.load()) / total : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Guild member configuration
 */
struct GuildMemberConfig {
    uint32_t maxMembers = 50;              // Maximum guild members
    uint32_t maxCommitteeMembers = 3;      // Maximum committee members
    bool enableMemberTracking = true;      // Enable member activity tracking
    bool enableStatistics = true;          // Enable statistics collection
    bool enableAutoPromotion = false;     // Enable automatic promotion system
    std::chrono::hours masterInactivityThreshold{504}; // 21 days for master election
    
    bool IsValid() const {
        return maxMembers > 0 && maxMembers <= 100 && 
               maxCommitteeMembers <= 10;
    }
};

/**
 * @brief Guild member event callback types
 */
using MemberLoginCallback = std::function<void(uint32_t memberSerial, uint16_t mapCode, uint16_t regionIndex)>;
using MemberLogoutCallback = std::function<void(uint32_t memberSerial)>;
using MemberJoinCallback = std::function<void(const GuildMemberInfo& member)>;
using MemberLeaveCallback = std::function<void(uint32_t memberSerial, bool selfLeave, bool punishment)>;
using MasterChangeCallback = std::function<void(uint32_t oldMaster, uint32_t newMaster)>;
using PermissionChangeCallback = std::function<void(uint32_t memberSerial, GuildMemberClass oldClass, GuildMemberClass newClass)>;
using MemberMessageCallback = std::function<void(uint32_t fromMember, const std::string& message)>;

/**
 * @brief Modern C++20 Guild Member Manager
 * 
 * Provides comprehensive guild member management with proper lifecycle handling,
 * authentication, permissions, communication, and administrative functions.
 */
class CGuildMemberManager {
public:
    /**
     * @brief Get singleton instance
     * 
     * @return Reference to the singleton instance
     */
    static CGuildMemberManager& GetInstance();

    /**
     * @brief Initialize guild member manager
     * 
     * @param config Member management configuration
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult Initialize(const GuildMemberConfig& config = {});

    /**
     * @brief Shutdown guild member manager
     * 
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult Shutdown();

    /**
     * @brief Setup guild with comprehensive member data
     * 
     * Modern implementation of CGuild::SetGuild function.
     * Initializes guild with all member data, financial information, and battle statistics.
     * 
     * @param setupData Complete guild setup data
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult SetupGuild(const GuildSetupData& setupData);

    /**
     * @brief Login guild member
     * 
     * Modern implementation of CGuild::LoginMember function.
     * Handles member authentication and session management.
     * 
     * @param memberSerial Member serial number
     * @param playerPtr Player pointer
     * @return Pointer to member info or nullptr
     */
    std::shared_ptr<GuildMemberInfo> LoginMember(uint32_t memberSerial, CPlayer* playerPtr);

    /**
     * @brief Send member login notification
     * 
     * Modern implementation of CGuild::SendMsg_GuildMemberLogin function.
     * Notifies all guild members of member login.
     * 
     * @param memberSerial Member serial number
     * @param mapCode Map code where member logged in
     * @param regionIndex Region index
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult SendMemberLoginNotification(uint32_t memberSerial, uint16_t mapCode, uint16_t regionIndex);

    /**
     * @brief Send member logout notification
     * 
     * Modern implementation of CGuild::SendMsg_GuildMemberLogoff function.
     * Notifies all guild members of member logout.
     * 
     * @param memberSerial Member serial number
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult SendMemberLogoutNotification(uint32_t memberSerial);

    /**
     * @brief Send member leave notification
     * 
     * Modern implementation of CGuild::SendMsg_LeaveMember function.
     * Notifies all guild members of member leaving.
     * 
     * @param memberSerial Member serial number
     * @param selfLeave Whether member left voluntarily
     * @param punishment Whether member was kicked/punished
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult SendMemberLeaveNotification(uint32_t memberSerial, bool selfLeave, bool punishment);

    /**
     * @brief Update guild master
     * 
     * Modern implementation of CGuild::DB_Update_GuildMaster function.
     * Updates guild master with database synchronization.
     * 
     * @param newMasterSerial New master serial number
     * @return GuildMemberResult indicating success or failure
     */
    GuildMemberResult UpdateGuildMaster(uint32_t newMasterSerial);

    /**
     * @brief Get member information
     * 
     * @param memberSerial Member serial number
     * @return Member information or nullptr
     */
    std::shared_ptr<GuildMemberInfo> GetMember(uint32_t memberSerial) const;

    /**
     * @brief Get all online members
     * 
     * @return Vector of online member information
     */
    std::vector<std::shared_ptr<GuildMemberInfo>> GetOnlineMembers() const;

    /**
     * @brief Get member count
     * 
     * @return Total number of guild members
     */
    uint32_t GetMemberCount() const;

    /**
     * @brief Get online member count
     * 
     * @return Number of online guild members
     */
    uint32_t GetOnlineMemberCount() const;

    /**
     * @brief Check if member has permission
     * 
     * @param memberSerial Member serial number
     * @param requiredClass Required member class
     * @return true if member has permission, false otherwise
     */
    bool HasPermission(uint32_t memberSerial, GuildMemberClass requiredClass) const;

    /**
     * @brief Set event callbacks
     */
    void SetMemberLoginCallback(MemberLoginCallback callback);
    void SetMemberLogoutCallback(MemberLogoutCallback callback);
    void SetMemberJoinCallback(MemberJoinCallback callback);
    void SetMemberLeaveCallback(MemberLeaveCallback callback);
    void SetMasterChangeCallback(MasterChangeCallback callback);
    void SetPermissionChangeCallback(PermissionChangeCallback callback);
    void SetMemberMessageCallback(MemberMessageCallback callback);

    /**
     * @brief Get member statistics
     * 
     * @return Copy of current statistics
     */
    GuildMemberStats GetStatistics() const;

    /**
     * @brief Get member configuration
     * 
     * @return Copy of current configuration
     */
    GuildMemberConfig GetConfiguration() const;

    /**
     * @brief Update member configuration
     * 
     * @param config New configuration
     * @return true if successful, false otherwise
     */
    bool UpdateConfiguration(const GuildMemberConfig& config);

    /**
     * @brief Check if manager is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized.load(); }

    /**
     * @brief Reset member statistics
     */
    void ResetStatistics();

private:
    // Singleton pattern
    CGuildMemberManager() = default;
    ~CGuildMemberManager() = default;
    CGuildMemberManager(const CGuildMemberManager&) = delete;
    CGuildMemberManager& operator=(const CGuildMemberManager&) = delete;
    CGuildMemberManager(CGuildMemberManager&&) = delete;
    CGuildMemberManager& operator=(CGuildMemberManager&&) = delete;

    /**
     * @brief Initialize member data from legacy structure
     * 
     * @param legacyMembers Legacy member array
     * @param memberCount Number of members
     * @return Vector of modern member info
     */
    std::vector<GuildMemberInfo> InitializeMemberData(const _guild_member_info* legacyMembers, int memberCount);

    /**
     * @brief Setup committee members
     * 
     * @param members Member list
     * @return true if successful, false otherwise
     */
    bool SetupCommitteeMembers(const std::vector<GuildMemberInfo>& members);

    /**
     * @brief Check master inactivity for election
     * 
     * @param masterSerial Master serial number
     * @return true if master can be elected, false otherwise
     */
    bool CheckMasterElectionEligibility(uint32_t masterSerial);

    /**
     * @brief Generate member packets
     * 
     * @return true if successful, false otherwise
     */
    bool GenerateMemberPackets();

    /**
     * @brief Validate member permissions
     * 
     * @param memberSerial Member serial number
     * @param action Action being performed
     * @return true if allowed, false otherwise
     */
    bool ValidateMemberPermissions(uint32_t memberSerial, const std::string& action);

    /**
     * @brief Log member activity
     * 
     * @param memberSerial Member serial number
     * @param activity Activity description
     */
    void LogMemberActivity(uint32_t memberSerial, const std::string& activity);

    /**
     * @brief Update member statistics
     * 
     * @param operation Operation type
     * @param success Whether operation was successful
     */
    void UpdateStatistics(const std::string& operation, bool success);

    /**
     * @brief Notify event callbacks
     */
    void NotifyMemberLogin(uint32_t memberSerial, uint16_t mapCode, uint16_t regionIndex);
    void NotifyMemberLogout(uint32_t memberSerial);
    void NotifyMemberJoin(const GuildMemberInfo& member);
    void NotifyMemberLeave(uint32_t memberSerial, bool selfLeave, bool punishment);
    void NotifyMasterChange(uint32_t oldMaster, uint32_t newMaster);
    void NotifyPermissionChange(uint32_t memberSerial, GuildMemberClass oldClass, GuildMemberClass newClass);
    void NotifyMemberMessage(uint32_t fromMember, const std::string& message);

    // Member variables
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    
    GuildMemberConfig m_config;
    GuildMemberStats m_stats;
    
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    mutable std::mutex m_membersMutex;
    
    // Member data
    std::unordered_map<uint32_t, std::shared_ptr<GuildMemberInfo>> m_members;
    std::array<std::shared_ptr<GuildMemberInfo>, 3> m_committeeMembers{};
    GuildMasterInfo m_masterInfo;
    
    // Guild data
    uint32_t m_guildSerial = 0;
    std::string m_guildName;
    uint8_t m_guildRace = 0;
    uint8_t m_guildGrade = 0;
    
    // Financial data
    double m_totalDalant = 0.0;
    double m_totalGold = 0.0;
    std::vector<GuildMoneyTransaction> m_moneyHistory;
    
    // Battle statistics
    uint32_t m_battleWins = 0;
    uint32_t m_battleDraws = 0;
    uint32_t m_battleLosses = 0;
    
    // Event callbacks
    MemberLoginCallback m_memberLoginCallback;
    MemberLogoutCallback m_memberLogoutCallback;
    MemberJoinCallback m_memberJoinCallback;
    MemberLeaveCallback m_memberLeaveCallback;
    MasterChangeCallback m_masterChangeCallback;
    PermissionChangeCallback m_permissionChangeCallback;
    MemberMessageCallback m_memberMessageCallback;
    
    // Error tracking
    std::chrono::steady_clock::time_point m_lastErrorTime;
    std::atomic<uint32_t> m_consecutiveErrors{0};
};

} // namespace NexusProtection::Guild
