/*
 * Function: j_??0?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140007EEB
 */

void __fastcall std::allocator<std::pair<int const,_TimeItem_fld const *>>::allocator<std::pair<int const,_TimeItem_fld const *>>(std::allocator<std::pair<int const ,_TimeItem_fld const *> > *this, std::allocator<std::pair<int const ,_TimeItem_fld const *> > *__formal)
{
  std::allocator<std::pair<int const,_TimeItem_fld const *>>::allocator<std::pair<int const,_TimeItem_fld const *>>(
    this,
    __formal);
}
