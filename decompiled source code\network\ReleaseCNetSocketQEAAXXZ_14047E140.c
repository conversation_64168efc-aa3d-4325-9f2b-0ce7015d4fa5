/*
 * Function: ?Release@CNetSocket@@QEAAXXZ
 * Address: 0x14047E140
 */

void __fastcall CNetSocket::Release(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  unsigned int j; // [sp+20h] [bp-48h]@7
  void *v5; // [sp+28h] [bp-40h]@11
  void *v6; // [sp+30h] [bp-38h]@13
  _socket *v7; // [sp+38h] [bp-30h]@15
  _socket *v8; // [sp+40h] [bp-28h]@15
  void *v9; // [sp+48h] [bp-20h]@19
  void *v10; // [sp+50h] [bp-18h]@16
  CNetSocket *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11->m_bSetSocket )
  {
    CNetSocket::CloseAll(v11);
    if ( v11->m_SockType.m_bServer )
      WSACloseEvent(v11->m_AcceptEvent);
    for ( j = 0; j < v11->m_SockType.m_wSocketMaxNum; ++j )
      WSACloseEvent(v11->m_Event[j]);
    v11->m_bSetSocket = 0;
    if ( v11->m_ndIPCheck )
    {
      v5 = v11->m_ndIPCheck;
      operator delete(v5);
    }
    if ( v11->m_dwIPCheckBufferList )
    {
      v6 = v11->m_dwIPCheckBufferList;
      operator delete(v6);
    }
    if ( v11->m_Socket )
    {
      v8 = v11->m_Socket;
      v7 = v8;
      if ( v8 )
        v10 = _socket::`vector deleting destructor'(v7, 3u);
      else
        v10 = 0i64;
    }
    if ( v11->m_Event )
    {
      v9 = v11->m_Event;
      operator delete(v9);
    }
  }
}
