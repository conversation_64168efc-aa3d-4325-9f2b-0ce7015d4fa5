/*
 * Function: ?Ref@?$Singleton@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@U?$NewObject@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@@2@$0A@@CryptoPP@@QEBAAEBV?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@2@XZ
 * Address: 0x14045FA00
 */

CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *__fastcall CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,0>::Ref(CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 v5; // [sp+20h] [bp-28h]@4
  char v6; // [sp+28h] [bp-20h]@6
  CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *v7; // [sp+30h] [bp-18h]@9
  CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,0> *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -2i64;
  if ( !(_S6 & 1) )
  {
    _S6 |= 1u;
    CryptoPP::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>(&s_pObject_1);
    atexit(CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_2_::_dynamic_atexit_destructor_for__s_pObject__);
  }
  while ( 1 )
  {
    v6 = s_objectState_1[0];
    if ( !s_objectState_1[0] )
      break;
    if ( v6 != 1 )
      return s_pObject_1.m_p;
  }
  s_objectState_1[0] = 1;
  v7 = CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>::operator()(&v8->m_objectFactory);
  s_pObject_1.m_p = v7;
  s_objectState_1[0] = 2;
  return s_pObject_1.m_p;
}
