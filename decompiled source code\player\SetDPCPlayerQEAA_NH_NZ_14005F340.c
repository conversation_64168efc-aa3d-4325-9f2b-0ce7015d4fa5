/*
 * Function: ?Set<PERSON>@CPlayer@@QEAA_NH_N@Z
 * Address: 0x14005F340
 */

char __fastcall CPlayer::SetDP(CPlayer *this, int nDP, bool bOver)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@6
  int v6; // eax@9
  char result; // al@11
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  int dwDP; // [sp+48h] [bp+10h]@1
  bool v12; // [sp+50h] [bp+18h]@1

  v12 = bOver;
  dwDP = nDP;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = CPlayerDB::GetDP(&v10->m_Param);
  if ( !v12 && dwDP > v9 )
  {
    v5 = CPlayer::GetMaxDP(v10);
    if ( v9 >= v5 || dwDP <= CPlayer::GetMaxDP(v10) )
    {
      v6 = CPlayer::GetMaxDP(v10);
      if ( v9 >= v6 && dwDP >= v9 )
        return 0;
    }
    else
    {
      dwDP = CPlayer::GetMaxDP(v10);
    }
  }
  if ( dwDP < 0 )
    dwDP = 0;
  if ( v9 == dwDP )
  {
    result = 0;
  }
  else
  {
    CPlayerDB::SetDP(&v10->m_Param, dwDP);
    result = 1;
  }
  return result;
}
