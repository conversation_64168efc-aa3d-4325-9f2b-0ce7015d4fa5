/*
 * Function: ?Update_SFContDelete@CUserDB@@QEAA_NEE@Z
 * Address: 0x140116C30
 */

char __fastcall CUserDB::Update_SFContDelete(CUserDB *this, char byContCode, char bySlotIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@5
  CUserDB *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = bySlotIndex;
  v9 = byContCode;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byContCode < 2 )
  {
    if ( (signed int)(unsigned __int8)bySlotIndex < 8 )
    {
      if ( _SFCONT_DB_BASE::_LIST::IsFilled((_SFCONT_DB_BASE::_LIST *)&v8->m_AvatorData.dbSfcont + 8
                                                                                                 * (unsigned __int8)byContCode
                                                                                                 + (unsigned __int8)bySlotIndex) )
      {
        _SFCONT_DB_BASE::_LIST::Init((_SFCONT_DB_BASE::_LIST *)&v8->m_AvatorData.dbSfcont + 8 * (unsigned __int8)v9
                                                                                          + (unsigned __int8)v10);
        result = 1;
      }
      else
      {
        v7 = (unsigned __int8)v10;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_SFContDelete(NOTHING) : code : %d, slot : %d",
          v8->m_aszAvatorName,
          (unsigned __int8)v9);
        result = 0;
      }
    }
    else
    {
      v7 = (unsigned __int8)bySlotIndex;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_SFContDelete(SlotIndex OVER) : code : %dslot : %d",
        v8->m_aszAvatorName,
        (unsigned __int8)byContCode);
      result = 0;
    }
  }
  else
  {
    v7 = (unsigned __int8)bySlotIndex;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_SFContDelete(byContCode OVER) : code : %d, slot : %d",
      v8->m_aszAvatorName,
      (unsigned __int8)byContCode);
    result = 0;
  }
  return result;
}
