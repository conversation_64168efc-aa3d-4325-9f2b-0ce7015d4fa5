/*
 * Function: j_?invoke@?$void2type@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@SAP6APEAVCMonster@@PEAD0MMM@ZPEAX@Z
 * Address: 0x14000DDAF
 */

CMonster *(__cdecl *__fastcall lua_tinker::void2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(lua_tinker::void2type<CMonster * (__cdecl*)(char *,char *,float,float,float)> *this, void *ptr))(char *, char *, float, float, float)
{
  return lua_tinker::void2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(this, ptr);
}
