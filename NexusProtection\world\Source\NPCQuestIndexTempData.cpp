/**
 * @file NPCQuestIndexTempData.cpp
 * @brief Implementation of temporary data structure for NPC quest indexing
 * @details Provides temporary storage and indexing for NPC quest data during processing
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "NPCQuestIndexTempData.h"
#include <algorithm>
#include <unordered_map>
#include <stdexcept>

namespace NexusProtection {
namespace World {

// Constructor
NPCQuestIndexTempData::NPCQuestIndexTempData()
    : m_indicesNeedRebuild(false) {
    Init();
}

// Destructor
NPCQuestIndexTempData::~NPCQuestIndexTempData() {
    Clear();
}

// Move constructor
NPCQuestIndexTempData::NPCQuestIndexTempData(NPCQuestIndexTempData&& other) noexcept
    : m_quests(std::move(other.m_quests))
    , m_questIdIndex(std::move(other.m_questIdIndex))
    , m_npcIndex(std::move(other.m_npcIndex))
    , m_indicesNeedRebuild(other.m_indicesNeedRebuild) {
    
    // Reset the moved-from object
    other.m_indicesNeedRebuild = false;
}

// Move assignment operator
NPCQuestIndexTempData& NPCQuestIndexTempData::operator=(NPCQuestIndexTempData&& other) noexcept {
    if (this != &other) {
        // Move data from other
        m_quests = std::move(other.m_quests);
        m_questIdIndex = std::move(other.m_questIdIndex);
        m_npcIndex = std::move(other.m_npcIndex);
        m_indicesNeedRebuild = other.m_indicesNeedRebuild;
        
        // Reset the moved-from object
        other.m_indicesNeedRebuild = false;
    }
    return *this;
}

// Initialize the temporary data structure
void NPCQuestIndexTempData::Init() {
    Clear();
    
    // Reserve some initial capacity to avoid frequent reallocations
    m_quests.reserve(100);
    m_questIdIndex.reserve(100);
    m_npcIndex.reserve(50);
}

// Clear all temporary data
void NPCQuestIndexTempData::Clear() {
    m_quests.clear();
    m_questIdIndex.clear();
    m_npcIndex.clear();
    m_indicesNeedRebuild = false;
}

// Add a quest to the temporary index
bool NPCQuestIndexTempData::AddQuest(const NPCQuestInfo& questInfo) {
    if (!ValidateQuestInfo(questInfo)) {
        return false;
    }
    
    // Check if quest already exists
    if (FindQuest(questInfo.questId) != nullptr) {
        return false; // Quest already exists
    }
    
    // Check capacity
    if (m_quests.size() >= GetMaxCapacity()) {
        return false; // At capacity
    }
    
    // Add the quest
    m_quests.push_back(questInfo);
    InvalidateIndices();
    
    return true;
}

// Remove a quest from the temporary index
bool NPCQuestIndexTempData::RemoveQuest(uint32_t questId) {
    std::size_t index = FindQuestIndex(questId);
    if (index == SIZE_MAX) {
        return false; // Quest not found
    }
    
    // Remove the quest (swap with last element for efficiency)
    if (index != m_quests.size() - 1) {
        std::swap(m_quests[index], m_quests.back());
    }
    m_quests.pop_back();
    
    InvalidateIndices();
    return true;
}

// Find a quest by ID
const NPCQuestInfo* NPCQuestIndexTempData::FindQuest(uint32_t questId) const {
    std::size_t index = FindQuestIndex(questId);
    if (index == SIZE_MAX) {
        return nullptr;
    }
    return &m_quests[index];
}

// Get all quests for a specific NPC
std::vector<const NPCQuestInfo*> NPCQuestIndexTempData::GetQuestsForNPC(uint32_t npcId) const {
    std::vector<const NPCQuestInfo*> result;
    
    for (const auto& quest : m_quests) {
        if (quest.npcId == npcId) {
            result.push_back(&quest);
        }
    }
    
    return result;
}

// Get quests by status
std::vector<const NPCQuestInfo*> NPCQuestIndexTempData::GetQuestsByStatus(QuestStatus status) const {
    std::vector<const NPCQuestInfo*> result;
    uint32_t statusValue = static_cast<uint32_t>(status);
    
    for (const auto& quest : m_quests) {
        if (quest.questStatus == statusValue) {
            result.push_back(&quest);
        }
    }
    
    return result;
}

// Get quests by type
std::vector<const NPCQuestInfo*> NPCQuestIndexTempData::GetQuestsByType(QuestType type) const {
    std::vector<const NPCQuestInfo*> result;
    uint32_t typeValue = static_cast<uint32_t>(type);
    
    for (const auto& quest : m_quests) {
        if (quest.questType == typeValue) {
            result.push_back(&quest);
        }
    }
    
    return result;
}

// Update quest status
bool NPCQuestIndexTempData::UpdateQuestStatus(uint32_t questId, QuestStatus newStatus) {
    std::size_t index = FindQuestIndex(questId);
    if (index == SIZE_MAX) {
        return false; // Quest not found
    }
    
    m_quests[index].questStatus = static_cast<uint32_t>(newStatus);
    return true;
}

// Get the number of quests in the temporary data
std::size_t NPCQuestIndexTempData::GetQuestCount() const noexcept {
    return m_quests.size();
}

// Check if the temporary data is empty
bool NPCQuestIndexTempData::IsEmpty() const noexcept {
    return m_quests.empty();
}

// Validate the internal data structure
bool NPCQuestIndexTempData::Validate() const {
    // Check for duplicate quest IDs
    std::unordered_map<uint32_t, std::size_t> questIdCounts;
    
    for (const auto& quest : m_quests) {
        if (!ValidateQuestInfo(quest)) {
            return false;
        }
        
        questIdCounts[quest.questId]++;
        if (questIdCounts[quest.questId] > 1) {
            return false; // Duplicate quest ID found
        }
    }
    
    return true;
}

// Get all quest IDs currently stored
std::vector<uint32_t> NPCQuestIndexTempData::GetAllQuestIds() const {
    std::vector<uint32_t> questIds;
    questIds.reserve(m_quests.size());
    
    for (const auto& quest : m_quests) {
        questIds.push_back(quest.questId);
    }
    
    return questIds;
}

// Get comprehensive statistics
NPCQuestIndexTempData::Statistics NPCQuestIndexTempData::GetStatistics() const {
    Statistics stats{};
    stats.totalQuests = m_quests.size();
    
    // Initialize arrays
    std::fill(std::begin(stats.questsByStatus), std::end(stats.questsByStatus), 0);
    std::fill(std::begin(stats.questsByType), std::end(stats.questsByType), 0);
    
    std::unordered_map<uint32_t, bool> uniqueNPCs;
    
    for (const auto& quest : m_quests) {
        // Count by status
        if (quest.questStatus < 6) {
            stats.questsByStatus[quest.questStatus]++;
        }
        
        // Count by type
        if (quest.questType < 8) {
            stats.questsByType[quest.questType]++;
        }
        
        // Track unique NPCs
        uniqueNPCs[quest.npcId] = true;
    }
    
    stats.uniqueNPCs = uniqueNPCs.size();
    return stats;
}

// Sort quests by a specific criteria
void NPCQuestIndexTempData::SortQuests(bool sortByLevel) {
    if (sortByLevel) {
        std::sort(m_quests.begin(), m_quests.end(),
            [](const NPCQuestInfo& a, const NPCQuestInfo& b) {
                return a.requiredLevel < b.requiredLevel;
            });
    } else {
        std::sort(m_quests.begin(), m_quests.end(),
            [](const NPCQuestInfo& a, const NPCQuestInfo& b) {
                return a.questId < b.questId;
            });
    }
    
    InvalidateIndices();
}

// Export quest data to a vector
std::vector<NPCQuestInfo> NPCQuestIndexTempData::ExportQuests() const {
    return m_quests; // Return a copy
}

// Import quest data from a vector
std::size_t NPCQuestIndexTempData::ImportQuests(const std::vector<NPCQuestInfo>& quests) {
    std::size_t importedCount = 0;
    
    for (const auto& quest : quests) {
        if (AddQuest(quest)) {
            ++importedCount;
        }
    }
    
    return importedCount;
}

// Rebuild internal indices for fast lookup
void NPCQuestIndexTempData::RebuildIndices() const {
    if (!m_indicesNeedRebuild) {
        return;
    }
    
    // Rebuild quest ID index
    m_questIdIndex.clear();
    m_questIdIndex.reserve(m_quests.size());
    
    for (std::size_t i = 0; i < m_quests.size(); ++i) {
        m_questIdIndex.emplace_back(m_quests[i].questId, i);
    }
    
    // Sort for binary search
    std::sort(m_questIdIndex.begin(), m_questIdIndex.end());
    
    // Rebuild NPC index
    m_npcIndex.clear();
    std::unordered_map<uint32_t, std::vector<std::size_t>> npcMap;
    
    for (std::size_t i = 0; i < m_quests.size(); ++i) {
        npcMap[m_quests[i].npcId].push_back(i);
    }
    
    m_npcIndex.reserve(npcMap.size());
    for (auto& pair : npcMap) {
        m_npcIndex.emplace_back(pair.first, std::move(pair.second));
    }
    
    // Sort NPC index
    std::sort(m_npcIndex.begin(), m_npcIndex.end());
    
    m_indicesNeedRebuild = false;
}

// Find quest position in vector by ID
std::size_t NPCQuestIndexTempData::FindQuestIndex(uint32_t questId) const {
    RebuildIndices();
    
    auto it = std::lower_bound(m_questIdIndex.begin(), m_questIdIndex.end(),
        std::make_pair(questId, std::size_t(0)));
    
    if (it != m_questIdIndex.end() && it->first == questId) {
        return it->second;
    }
    
    return SIZE_MAX;
}

// Mark indices as needing rebuild
void NPCQuestIndexTempData::InvalidateIndices() {
    m_indicesNeedRebuild = true;
}

// Validate a quest info structure
bool NPCQuestIndexTempData::ValidateQuestInfo(const NPCQuestInfo& questInfo) {
    // Basic validation
    if (questInfo.questId == 0) return false;
    if (questInfo.npcId == 0) return false;
    if (questInfo.questType > static_cast<uint32_t>(QuestType::Custom)) return false;
    if (questInfo.questStatus > static_cast<uint32_t>(QuestStatus::Repeatable)) return false;
    if (questInfo.requiredLevel > 1000) return false; // Reasonable level cap
    
    return true;
}

// Get the size of the temporary data structure in bytes
std::size_t NPCQuestIndexTempData::GetMemoryUsage() const {
    std::size_t size = sizeof(*this);
    
    // Add vector capacities
    size += m_quests.capacity() * sizeof(NPCQuestInfo);
    size += m_questIdIndex.capacity() * sizeof(std::pair<uint32_t, std::size_t>);
    
    for (const auto& npcEntry : m_npcIndex) {
        size += npcEntry.second.capacity() * sizeof(std::size_t);
    }
    size += m_npcIndex.capacity() * sizeof(std::pair<uint32_t, std::vector<std::size_t>>);
    
    // Add string sizes
    for (const auto& quest : m_quests) {
        size += quest.questName.capacity();
        size += quest.description.capacity();
    }
    
    return size;
}

} // namespace World
} // namespace NexusProtection
