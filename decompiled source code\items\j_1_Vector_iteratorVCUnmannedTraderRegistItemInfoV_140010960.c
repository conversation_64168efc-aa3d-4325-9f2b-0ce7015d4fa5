/*
 * Function: j_??1?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x140010960
 */

void __fastcall std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(this);
}
