/*
 * Function: j_?PushUpdateState@CUnmannedTraderItemState@@SA_NEKEKGEG@Z
 * Address: 0x14000FE25
 */

bool __fastcall CUnmannedTraderItemState::PushUpdateState(char byType, unsigned int dwRegistSerial, char byState, unsigned int dwOwnerSerial, unsigned __int16 wItemSerial, char byItemTableCode, unsigned __int16 wItemTableIndex)
{
  return CUnmannedTraderItemState::PushUpdateState(
           byType,
           dwRegistSerial,
           byState,
           dwOwnerSerial,
           wItemSerial,
           byItemTableCode,
           wItemTableIndex);
}
