/*
 * Function: ??0CUnmannedTraderSubClassInfo@@QEAA@AEBV0@@Z
 * Address: 0x140384630
 */

void __fastcall CUnmannedTraderSubClassInfo::CUnmannedTraderSubClassInfo(CUnmannedTraderSubClassInfo *this, CUnmannedTraderSubClassInfo *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfo *v5; // [sp+30h] [bp+8h]@1
  CUnmannedTraderSubClassInfo *lhsa; // [sp+38h] [bp+10h]@1

  lhsa = lhs;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->vfptr = (CUnmannedTraderSubClassInfoVtbl *)&CUnmannedTraderSubClassInfo::`vftable';
  strcpy_0(v5->m_szName, "NONE");
  CUnmannedTraderSubClassInfo::Copy(v5, lhsa);
}
