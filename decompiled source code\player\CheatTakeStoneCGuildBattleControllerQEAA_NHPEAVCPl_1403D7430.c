/*
 * Function: ?CheatTakeStone@CGuildBattleController@@QEAA_NHPEAVCPlayer@@@Z
 * Address: 0x1403D7430
 */

char __fastcall CGuildBattleController::CheatTakeStone(CGuildBattleController *this, int iPortalInx, CPlayer *pkPlayer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v5; // rax@5
  char result; // al@7
  char *v7; // rax@9
  __int64 v8; // [sp+0h] [bp-C8h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v9; // [sp+30h] [bp-98h]@4
  char Dst; // [sp+48h] [bp-80h]@8
  char v11; // [sp+49h] [bp-7Fh]@9
  char Dest; // [sp+5Ah] [bp-6Eh]@9
  char pbyType; // [sp+84h] [bp-44h]@10
  char v14; // [sp+85h] [bp-43h]@10
  CMapData *v15; // [sp+A0h] [bp-28h]@5
  int v16; // [sp+A8h] [bp-20h]@5
  unsigned __int64 v17; // [sp+B0h] [bp-18h]@4
  int iPortalInxa; // [sp+D8h] [bp+10h]@1
  CPlayer *pkPlayera; // [sp+E0h] [bp+18h]@1

  pkPlayera = pkPlayer;
  iPortalInxa = iPortalInx;
  v3 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  v9 = 0i64;
  if ( pkPlayer->m_pCurMap )
  {
    v15 = pkPlayer->m_pCurMap;
    v16 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
    v5 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
    v9 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v5, v16, v15->m_nMapCode);
  }
  if ( v9 )
  {
    memset_0(&Dst, 0, 0x28ui64);
    Dst = GUILD_BATTLE::CNormalGuildBattleField::CheatTakeStone(v9, iPortalInxa, pkPlayera);
    if ( !Dst )
    {
      v7 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
      strcpy_0(&Dest, v7);
      strcpy_0(&v11, "Cheat");
    }
    pbyType = 27;
    v14 = 72;
    CNetProcess::LoadSendMsg(unk_1414F2088, pkPlayera->m_ObjID.m_wIndex, &pbyType, &Dst, 0x28u);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
