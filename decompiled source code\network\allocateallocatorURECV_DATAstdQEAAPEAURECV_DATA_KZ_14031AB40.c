/*
 * Function: ?allocate@?$allocator@URECV_DATA@@@std@@QEAAPEAURECV_DATA@@_K@Z
 * Address: 0x14031AB40
 */

RECV_DATA *__fastcall std::allocator<RECV_DATA>::allocate(std::allocator<RECV_DATA> *this, unsigned __int64 _Count)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return std::_Allocate<RECV_DATA>(_Count, 0i64);
}
