/*
 * Function: ??1CNormalGuildBattleManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403D3430
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::~CNormalGuildBattleManager(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  unsigned int j; // [sp+20h] [bp-38h]@6
  GUILD_BATTLE::CNormalGuildBattle *v5; // [sp+28h] [bp-30h]@9
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+30h] [bp-28h]@9
  void *v7; // [sp+38h] [bp-20h]@13
  void *v8; // [sp+40h] [bp-18h]@10
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_ppkNormalBattle && v9->m_uiMaxBattleCnt )
  {
    for ( j = 0; j < v9->m_uiMaxBattleCnt; ++j )
    {
      if ( v9->m_ppkNormalBattle[j] )
      {
        v6 = v9->m_ppkNormalBattle[j];
        v5 = v6;
        if ( v6 )
          v8 = GUILD_BATTLE::CNormalGuildBattle::`scalar deleting destructor'(v5, 1u);
        else
          v8 = 0i64;
      }
    }
    v7 = v9->m_ppkNormalBattle;
    operator delete[](v7);
    v9->m_ppkNormalBattle = 0i64;
    v9->m_uiMaxBattleCnt = 0;
  }
}
