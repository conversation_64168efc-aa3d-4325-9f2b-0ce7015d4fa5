/*
 * Function: ?pc_UnitReturnRequest@CPlayer@@QEAAXXZ
 * Address: 0x140105E20
 */

void __usercall CPlayer::pc_UnitReturnRequest(CPlayer *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+20h] [bp-28h]@4
  char v6; // [sp+21h] [bp-27h]@4
  __int64 v7; // [sp+28h] [bp-20h]@4
  unsigned int dwPull; // [sp+30h] [bp-18h]@14
  CPlayer *p; // [sp+50h] [bp+8h]@1

  p = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  v6 = 1;
  v7 = 0i64;
  if ( CPlayerDB::GetRaceCode(&p->m_Param) )
  {
    v5 = 1;
  }
  else if ( p->m_pParkingUnit )
  {
    if ( p->m_pParkingUnit->m_dwOwnerSerial == p->m_dwObjSerial )
    {
      GetSqrt(p->m_fCurPos, p->m_pParkingUnit->m_fCurPos);
      if ( a2 <= 540.0 )
      {
        if ( IsBeNearStore(p, 4) )
          v6 = 0;
      }
      else
      {
        v5 = 20;
      }
    }
    else
    {
      v5 = 2;
    }
  }
  else
  {
    v5 = 2;
  }
  dwPull = 0;
  if ( !v5 )
  {
    CParkingUnit::Destroy(p->m_pParkingUnit, 0);
    if ( v6 == 1 )
      dwPull = 1000;
    CPlayer::_UpdateUnitDebt(p, p->m_pUsingUnit->bySlotIndex, dwPull);
    CPlayer::_LockUnitKey(p, p->m_pUsingUnit->bySlotIndex, 0);
    p->m_pUsingUnit = 0i64;
    p->m_pParkingUnit = 0i64;
  }
  CPlayer::SendMsg_UnitReturnResult(p, v5, dwPull);
}
