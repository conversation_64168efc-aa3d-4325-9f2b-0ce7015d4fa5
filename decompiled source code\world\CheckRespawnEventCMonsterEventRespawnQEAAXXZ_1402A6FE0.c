/*
 * Function: ?CheckRespawnEvent@CMonsterEventRespawn@@QEAAXXZ
 * Address: 0x1402A6FE0
 */

void __fastcall CMonsterEventRespawn::CheckRespawnEvent(CMonsterEventRespawn *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@16
  __int64 v4; // [sp+0h] [bp-88h]@1
  CMonster *pParent; // [sp+20h] [bp-68h]@16
  bool bRobExp; // [sp+28h] [bp-60h]@16
  bool bRewardExp; // [sp+30h] [bp-58h]@16
  bool bDungeon; // [sp+38h] [bp-50h]@16
  bool bWithoutFail; // [sp+40h] [bp-48h]@16
  bool bApplyRopExpField; // [sp+48h] [bp-40h]@16
  unsigned int v11; // [sp+50h] [bp-38h]@4
  int j; // [sp+54h] [bp-34h]@4
  _event_respawn *pEventRespawn; // [sp+58h] [bp-30h]@7
  int k; // [sp+60h] [bp-28h]@10
  _event_respawn::_state::_mon *v15; // [sp+68h] [bp-20h]@13
  CMonster *v16; // [sp+70h] [bp-18h]@16
  CMonsterEventRespawn *v17; // [sp+90h] [bp+8h]@1

  v17 = this;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = timeGetTime();
  for ( j = 0; j < v17->m_nLoadEventRespawn; ++j )
  {
    pEventRespawn = &v17->m_EventRespawn[j];
    if ( pEventRespawn->bLoad
      && pEventRespawn->bActive
      && v11 - pEventRespawn->State.dwLastUpdateTime >= pEventRespawn->dwTermMSec )
    {
      for ( k = 0; k < pEventRespawn->State.nRespawnNum; ++k )
      {
        v15 = &pEventRespawn->State.MonInfo[k];
        if ( !v15->pMon || !v15->pMon->m_bLive || v15->pMon->m_dwObjSerial != v15->dwSerial )
        {
          v3 = v15->pMonFld->m_strCode;
          bApplyRopExpField = 0;
          bWithoutFail = 0;
          bDungeon = 0;
          bRewardExp = pEventRespawn->Option.bExpReward;
          bRobExp = pEventRespawn->Option.bExpPenalty;
          pParent = 0i64;
          v16 = CreateRepMonster(pEventRespawn->pMap, 0, pEventRespawn->fPos, v3, 0i64, bRobExp, bRewardExp, 0, 0, 0);
          if ( v16 )
          {
            v15->pMon = v16;
            v15->dwSerial = v16->m_dwObjSerial;
            if ( !pEventRespawn->Option.bItemLoot )
              CMonster::DisableStdItemLoot(v16);
            CMonster::LinkEventRespawn(v16, pEventRespawn);
          }
          else
          {
            v15->pMon = 0i64;
          }
        }
      }
      pEventRespawn->State.dwLastUpdateTime = v11;
    }
  }
}
