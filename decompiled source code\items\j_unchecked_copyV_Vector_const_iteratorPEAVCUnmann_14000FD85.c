/*
 * Function: j_??$unchecked_copy@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderSubClassInfo@@@stdext@@YAPEAPEAVCUnmannedTraderSubClassInfo@@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@0PEAPEAV1@@Z
 * Address: 0x14000FD85
 */

CUnmannedTraderSubClassInfo **__fastcall stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *>(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Last, CUnmannedTraderSubClassInfo **_Dest)
{
  return stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *>(
           _First,
           _Last,
           _Dest);
}
