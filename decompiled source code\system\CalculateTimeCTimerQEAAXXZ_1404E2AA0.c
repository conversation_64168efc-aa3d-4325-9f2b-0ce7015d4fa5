/*
 * Function: ?CalculateTime@CTimer@@QEAAXXZ
 * Address: 0x1404E2AA0
 */

void __fastcall CTimer::CalculateTime(CTimer *this)
{
  CTimer *v1; // rbx@1
  unsigned __int64 v2; // rax@4
  signed __int64 v3; // rdx@4
  float v4; // xmm0_4@4
  unsigned __int64 v5; // rax@7
  DWORD v6; // eax@8
  unsigned int v7; // er11@8
  signed int v8; // eax@8
  float v9; // xmm1_4@9
  signed int v10; // eax@9
  float v11; // xmm2_4@10
  float v12; // xmm1_4@10
  float v13; // xmm1_4@13
  float v14; // xmm0_4@13
  float v15; // xmm1_4@13
  float v16; // xmm0_4@13
  signed int v17; // eax@14

  v1 = this;
  if ( !this->m_bTimerInitialized )
  {
    this->m_bTimerInitialized = 1;
    if ( !QueryPerformanceFrequency((LARGE_INTEGER *)&this->m_qwTicksPerSec) )
      v1->m_qwTicksPerSec = 1000i64;
    v2 = v1->m_qwTicksPerSec;
    v3 = 0i64;
    LODWORD(v1->m_fSecsPerFrame) = 981668463;
    v1->m_fTicksPerSec = (float)(signed int)v2;
    v4 = FLOAT_1000_0;
    if ( 1000.0 > 9.223372e18 )
    {
      v4 = 1000.0 - 9.223372e18;
      v3 = 0x7FFFFFFFFFFFFFFFi64;
      if ( (float)(1000.0 - 9.223372e18) > 9.223372e18 )
      {
        v4 = v4 - 9.223372e18;
        v3 = -2i64;
      }
    }
    v1->m_qwTicksPerFrame = v2 / (v3 + (unsigned __int64)(unsigned int)(signed int)ffloor(v4));
    v5 = CTimer::GetTicks(v1);
    v1->m_qwTicks = v5;
    v1->m_qwStartTicks = v5;
  }
  v6 = timeGetTime();
  ++v1->mLoopCnt;
  v7 = v6;
  v1->mLoopHop += v6 - v1->mOldTime;
  v8 = v1->mLoopHop;
  if ( (unsigned int)v8 > 0xA )
  {
    v9 = (float)v8;
    v10 = v1->mLoopCnt;
    v1->mLoopCnt = 0;
    v1->mLoopHop = 0;
    v1->mLoopTime = (float)(v9 / (float)v10) / 1000.0;
  }
  v11 = v1->mLoopTime;
  v12 = v1->mMinFPS;
  v1->mOldTime = v7;
  if ( v11 > v12 || v11 < 0.0 )
    v1->mLoopTime = v12;
  v13 = v1->mLoopTime;
  ++v1->mLoopFPSCnt;
  v14 = v13;
  v15 = v13 + v1->mFPSTime;
  v16 = v14 + v1->mRealTime;
  v1->mFPSTime = v15;
  v1->mRealTime = v16;
  if ( v15 > 1.0 )
  {
    v17 = v1->mLoopFPSCnt;
    LODWORD(v1->mFPSTime) = 0;
    v1->mLoopFPSCnt = 0;
    v1->mFPS = (float)v17 / v15;
  }
}
