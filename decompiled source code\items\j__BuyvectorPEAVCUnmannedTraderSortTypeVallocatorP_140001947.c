/*
 * Function: j_?_Buy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140001947
 */

bool __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Buy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, unsigned __int64 _Capacity)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Buy(this, _Capacity);
}
