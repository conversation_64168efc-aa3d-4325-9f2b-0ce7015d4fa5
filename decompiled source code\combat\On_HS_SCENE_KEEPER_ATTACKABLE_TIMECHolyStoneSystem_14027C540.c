/*
 * Function: ?On_HS_SCENE_KEEPER_ATTACKABLE_TIME@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027C540
 */

void __fastcall CHolyStoneSystem::On_HS_SCENE_KEEPER_ATTACKABLE_TIME(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CHolyStoneSystem *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->m_SaveData.m_eDestroyerState == 2 )
  {
    v4->m_SaveData.m_eDestroyerState = 0;
    v4->m_SaveData.m_dwDestroyerSerial = -1;
    CHolyStoneSystem::ReleaseLastAttBuff(v4);
    v4->m_SaveData.m_dwDestroyerGuildSerial = -1;
    CHolyStoneSystem::SendIsArriveDestroyer(v4, 0);
  }
  CHolyStoneSystem::UnAllRegisterPerAutoMine(v4);
  if ( CHolyStoneSystem::GetHolyMasterRace(v4) != -1 )
  {
    CHolyStoneSystem::SetKeeperDestroyRace(v4, -1);
    CHolyStoneSystem::CreateHolyKeeper(v4, 0);
    CHolyKeeper::SetDamageAbleState(g_Keeper, 1);
    CHolyStoneSystem::SendMsg_EnterKeeper(v4, -1);
  }
}
