/*
 * Function: ?GetRestTime@CGuildRoomSystem@@QEAA_NKAEAJ@Z
 * Address: 0x1402EAA10
 */

char __fastcall CGuildRoomSystem::GetRestTime(CGuildRoomSystem *this, unsigned int dwGuildSerial, int *tt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v5; // rax@7
  CGuildRoomInfo *v6; // rax@8
  CGuildRoomInfo *v7; // rax@9
  __int64 v9; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGuildRoomSystem *v11; // [sp+40h] [bp+8h]@1
  unsigned int v12; // [sp+48h] [bp+10h]@1
  int *v13; // [sp+50h] [bp+18h]@1

  v13 = tt;
  v12 = dwGuildSerial;
  v11 = this;
  v3 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 90; ++j )
  {
    v5 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v11->m_vecGuildRoom, j);
    if ( CGuildRoomInfo::IsRent(v5) )
    {
      v6 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v11->m_vecGuildRoom, j);
      if ( CGuildRoomInfo::GetGuildSerial(v6) == v12 )
      {
        v7 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v11->m_vecGuildRoom, j);
        *v13 = CGuildRoomInfo::GetRestTime(v7);
        return 1;
      }
    }
  }
  return 0;
}
