/*
 * Function: ?SendMsg_TalikList@CPvpCashPoint@@QEAAXH@Z
 * Address: 0x1403F57A0
 */

void __fastcall CPvpCashPoint::SendMsg_TalikList(CPvpCashPoint *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 j; // rcx@1
  CPvpCashMng *v4; // rax@4
  CPvpCashMng *v5; // rax@6
  unsigned __int16 v6; // ax@7
  __int64 v7; // [sp+0h] [bp-B8h]@1
  _pvp_cash_recover_itemlist_result_zocl v8; // [sp+38h] [bp-80h]@4
  int i; // [sp+84h] [bp-34h]@4
  char pbyType; // [sp+94h] [bp-24h]@7
  char v11; // [sp+95h] [bp-23h]@7
  int dwClientIndex; // [sp+C8h] [bp+10h]@1

  dwClientIndex = n;
  v2 = &v7;
  for ( j = 44i64; j; --j )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _pvp_cash_recover_itemlist_result_zocl::_pvp_cash_recover_itemlist_result_zocl(&v8);
  v4 = CPvpCashMng::Instance();
  v8.byItemNum = CPvpCashMng::GetTalikNum(v4);
  for ( i = 0; i < (unsigned __int8)v8.byItemNum; ++i )
  {
    v5 = CPvpCashMng::Instance();
    v8.nTalikInfo[i] = CPvpCashMng::GetTalikRecvrPoint(v5, i);
  }
  pbyType = 12;
  v11 = 20;
  v6 = _pvp_cash_recover_itemlist_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v8.byItemNum, v6);
}
