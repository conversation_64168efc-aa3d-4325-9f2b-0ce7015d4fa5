/*
 * Function: ?CreateSelectCharacterLogTable@CMainThread@@AEAAXE@Z
 * Address: 0x1401F9E50
 */

void __fastcall CMainThread::CreateSelectCharacterLogTable(CMainThread *this, char byMonth)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-D8h]@1
  tm *v5; // [sp+20h] [bp-B8h]@4
  __int64 _Time; // [sp+38h] [bp-A0h]@4
  int v7; // [sp+44h] [bp-94h]@5
  int v8; // [sp+48h] [bp-90h]@5
  char Dst; // [sp+58h] [bp-80h]@5
  char Dest; // [sp+88h] [bp-50h]@5
  unsigned __int8 j; // [sp+B4h] [bp-24h]@5
  unsigned __int64 v12; // [sp+C0h] [bp-18h]@4
  CMainThread *v13; // [sp+E0h] [bp+8h]@1
  char v14; // [sp+E8h] [bp+10h]@1

  v14 = byMonth;
  v13 = this;
  v2 = &v4;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  time_6(&_Time);
  v5 = localtime_4(&_Time);
  if ( v5 )
  {
    v7 = v5->tm_year + 1900;
    v8 = v5->tm_mon + 1;
    memset_0(&Dst, 0, 0xAui64);
    memset_0(&Dest, 0, 0x20ui64);
    for ( j = 0; j < (signed int)(unsigned __int8)v14; ++j )
    {
      sprintf(&Dst, "%04d%02d", (unsigned int)v7, (unsigned int)v8);
      sprintf(&Dest, "tbl_characterselect_log_%s", &Dst);
      if ( !CRFNewDatabase::TableExist((CRFNewDatabase *)&v13->m_pWorldDB->vfptr, &Dest) )
      {
        if ( !CRFWorldDatabase::CreateCharacterSelectLogTable(v13->m_pWorldDB, &Dest) )
        {
          MyMessageBox("CMainThread::_GameDataBaseInit()", "Create %s Table Failed!", &Dest);
          return;
        }
        CLogFile::Write(&v13->m_logLoadingError, "Character Select Log Table(%s) Make Complete!!", &Dest);
        if ( ++v8 > 12 )
        {
          v8 = 1;
          ++v7;
        }
      }
    }
  }
}
