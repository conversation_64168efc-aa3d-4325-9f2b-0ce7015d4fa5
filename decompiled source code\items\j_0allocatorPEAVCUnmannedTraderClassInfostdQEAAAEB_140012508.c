/*
 * Function: j_??0?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140012508
 */

void __fastcall std::allocator<CUnmannedTraderClassInfo *>::allocator<CUnmannedTraderClassInfo *>(std::allocator<CUnmannedTraderClassInfo *> *this, std::allocator<CUnmannedTraderClassInfo *> *__formal)
{
  std::allocator<CUnmannedTraderClassInfo *>::allocator<CUnmannedTraderClassInfo *>(this, __formal);
}
