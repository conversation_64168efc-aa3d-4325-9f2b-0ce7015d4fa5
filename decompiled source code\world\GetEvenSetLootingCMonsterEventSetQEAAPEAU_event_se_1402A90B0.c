/*
 * Function: ?GetEvenSetLooting@CMonsterEventSet@@QEAAPEAU_event_set_looting@@PEBD@Z
 * Address: 0x1402A90B0
 */

_event_set_looting *__fastcall CMonsterEventSet::GetEvenSetLooting(CMonsterEventSet *this, const char *pszCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CMonsterEventSet *v7; // [sp+40h] [bp+8h]@1
  char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = (char *)pszCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 100; ++j )
  {
    if ( !strcmp_0(v7->m_EventSetLootingList[j].strCode, Str2) )
      return &v7->m_EventSetLootingList[j];
  }
  return 0i64;
}
