/*
 * Function: ?ConsumeMeterial_And_CalculateNewItems@ItemCombineMgr@@IEAAEPEAPEAU_db_con@_STORAGE_LIST@@EPEAU_list@_combine_ex_item_request_clzo@@PEAU_combine_ex_item_result_zocl@@PEAU_ItemCombine_exp_fld@@EH@Z
 * Address: 0x1402AC350
 */

char __fastcall ItemCombineMgr::ConsumeMeterial_And_CalculateNewItems(ItemCombineMgr *this, _STORAGE_LIST::_db_con **pMt_Sv_Inv, char byMtSlotNum, _combine_ex_item_request_clzo::_list *pipMaterials, _combine_ex_item_result_zocl *pSaveData, _ItemCombine_exp_fld *pfld, char byLinkTableIndex, int nType)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v10; // eax@9
  int v11; // eax@74
  _STORAGE_LIST::_db_con *v12; // rcx@74
  _STORAGE_LIST::_db_con *v13; // rax@75
  CMoneySupplyMgr *v15; // rax@84
  char *v16; // rax@85
  int v17; // ecx@85
  int v18; // eax@108
  _STORAGE_LIST::_db_con *v19; // rcx@108
  CMoneySupplyMgr *v20; // rax@114
  char *v21; // rax@115
  int v22; // ecx@115
  __int64 v23; // [sp+0h] [bp-1C8h]@1
  _STORAGE_LIST::_db_con **ppMaterial; // [sp+20h] [bp-1A8h]@51
  bool bSend[8]; // [sp+28h] [bp-1A0h]@74
  unsigned int dwFee; // [sp+30h] [bp-198h]@85
  char *strFileName; // [sp+38h] [bp-190h]@85
  int bSucc; // [sp+40h] [bp-188h]@85
  unsigned int dwFailCount; // [sp+48h] [bp-180h]@85
  int j; // [sp+50h] [bp-178h]@6
  int k; // [sp+54h] [bp-174h]@6
  char Dst[16]; // [sp+64h] [bp-164h]@6
  char v33; // [sp+74h] [bp-154h]@9
  int v34; // [sp+78h] [bp-150h]@9
  unsigned int v35; // [sp+7Ch] [bp-14Ch]@9
  int v36; // [sp+80h] [bp-148h]@9
  int v37; // [sp+84h] [bp-144h]@9
  unsigned int v38; // [sp+88h] [bp-140h]@9
  char v39; // [sp+8Ch] [bp-13Ch]@9
  unsigned int v40; // [sp+90h] [bp-138h]@9
  int nIndex[26]; // [sp+B0h] [bp-118h]@9
  char *Str1; // [sp+118h] [bp-B0h]@16
  char *v43; // [sp+120h] [bp-A8h]@31
  char v44; // [sp+128h] [bp-A0h]@35
  int l; // [sp+12Ch] [bp-9Ch]@35
  _combine_ex_item_result_zocl::__item *pItem; // [sp+130h] [bp-98h]@51
  char *v47; // [sp+138h] [bp-90h]@61
  _combine_ex_item_result_zocl::__item *v48; // [sp+140h] [bp-88h]@67
  unsigned int dwSub; // [sp+148h] [bp-80h]@79
  int v50; // [sp+14Ch] [bp-7Ch]@80
  int nBuffer[7]; // [sp+158h] [bp-70h]@91
  int nSocketIndex; // [sp+174h] [bp-54h]@87
  unsigned int v53; // [sp+178h] [bp-50h]@87
  int v54; // [sp+17Ch] [bp-4Ch]@88
  _combine_ex_item_result_zocl::__item *v55; // [sp+180h] [bp-48h]@103
  unsigned int nAmount; // [sp+188h] [bp-40h]@109
  int v57; // [sp+18Ch] [bp-3Ch]@110
  int v58; // [sp+198h] [bp-30h]@6
  int v59; // [sp+19Ch] [bp-2Ch]@7
  int nLv; // [sp+1A0h] [bp-28h]@84
  int v61; // [sp+1A4h] [bp-24h]@84
  int v62; // [sp+1A8h] [bp-20h]@114
  int v63; // [sp+1ACh] [bp-1Ch]@114
  unsigned __int64 v64; // [sp+1B0h] [bp-18h]@4
  ItemCombineMgr *v65; // [sp+1D0h] [bp+8h]@1
  _STORAGE_LIST::_db_con **pMt_Sv_Inva; // [sp+1D8h] [bp+10h]@1
  char v67; // [sp+1E0h] [bp+18h]@1
  _combine_ex_item_request_clzo::_list *v68; // [sp+1E8h] [bp+20h]@1

  v68 = pipMaterials;
  v67 = byMtSlotNum;
  pMt_Sv_Inva = pMt_Sv_Inv;
  v65 = this;
  v8 = &v23;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v64 = (unsigned __int64)&v23 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byMtSlotNum > 5 )
    v67 = 5;
  j = 0;
  k = 0;
  memset_0(Dst, 0, 5ui64);
  v58 = pfld->m_bSelectItem == 0;
  pSaveData->byDlgType = v58;
  if ( pfld->m_bSelectItem )
    v59 = pfld->m_nOperationCount;
  else
    v59 = 0;
  pSaveData->bySelectItemCount = v59;
  pSaveData->dwDalant = pfld->m_dwCommit;
  v10 = timeGetTime();
  pSaveData->dwCheckKey = CombineExCheckKeyGen(v10, pfld->m_dwIndex);
  v33 = 0;
  v34 = pfld->m_nOperationCount;
  v35 = 10000;
  v36 = 0;
  v37 = 0;
  v38 = 0;
  v39 = 0;
  v40 = rand() % 0x2710u;
  memset_0(nIndex, -1, 0x60ui64);
  if ( pSaveData->byDlgType == 1 )
  {
    for ( j = 0; j < v34; ++j )
    {
      if ( nType )
      {
        v38 = 0;
        v39 = 0;
        if ( v35 )
          v40 = rand() % v35;
        else
          v40 = 0;
        for ( k = 0; k < 24; ++k )
        {
          v43 = pfld->m_listOutput[k].m_itmPdOutput;
          if ( strncmp(v43, "-", 1ui64) && strncmp(v43, "-1", 2ui64) )
          {
            if ( v37 <= 0 )
              goto LABEL_119;
            v44 = 0;
            for ( l = 0; l < v37; ++l )
            {
              if ( nIndex[l] == k )
              {
                v44 = 1;
                break;
              }
            }
            if ( !v44 )
            {
LABEL_119:
              v38 += *((_DWORD *)v43 + 5);
              if ( v40 < v38 )
              {
                nIndex[v37++] = k;
                v35 -= *((_DWORD *)v43 + 5);
                v39 = 1;
                break;
              }
            }
          }
        }
        if ( !v39 )
        {
          v33 = 10;
          goto LABEL_70;
        }
      }
      else
      {
        v38 = 0;
        v39 = 0;
        v40 = rand() % 10000;
        for ( k = 0; k < 24; ++k )
        {
          Str1 = pfld->m_listOutput[k].m_itmPdOutput;
          if ( strncmp(Str1, "-", 1ui64) )
          {
            if ( strncmp(Str1, "-1", 2ui64) )
            {
              v38 += *((_DWORD *)Str1 + 5);
              if ( v40 < v38 )
              {
                nIndex[v37++] = k;
                v39 = 1;
                break;
              }
            }
          }
        }
        if ( !v39 )
        {
          v33 = 10;
          goto LABEL_70;
        }
      }
    }
    if ( v37 <= v34 )
    {
      _combine_ex_item_result_zocl::_Result_ItemList_Buff::Init(&pSaveData->ItemBuff);
      pSaveData->ItemBuff.byItemListNum = v37;
      for ( j = 0; j < v37; ++j )
      {
        pItem = &pSaveData->ItemBuff.RewardItemList[j];
        LOBYTE(ppMaterial) = byLinkTableIndex;
        if ( __make_item(v65->m_pMaster, pItem, pfld, nIndex[j], byLinkTableIndex) )
        {
          v33 = 12;
          goto LABEL_70;
        }
      }
      if ( v37 == 1 )
      {
        pSaveData->dwResultEffectType = pfld->m_listOutput[nIndex[0]].m_dwEffectType;
        pSaveData->dwResultEffectMsgCode = pfld->m_listOutput[nIndex[0]].m_dwResultEffectMsgCode;
      }
      else
      {
        pSaveData->dwResultEffectType = -1;
        pSaveData->dwResultEffectMsgCode = 0;
      }
    }
    else
    {
      v33 = 11;
    }
  }
  else
  {
    v37 = 0;
    _combine_ex_item_result_zocl::_Result_ItemList_Buff::Init(&pSaveData->ItemBuff);
    for ( j = 0; j < 24; ++j )
    {
      v47 = pfld->m_listOutput[j].m_itmPdOutput;
      if ( strncmp(v47, "-", 1ui64) && strncmp(v47, "-1", 2ui64) )
        nIndex[v37++] = j;
    }
    pSaveData->ItemBuff.byItemListNum = v37;
    for ( j = 0; j < v37; ++j )
    {
      v48 = &pSaveData->ItemBuff.RewardItemList[j];
      LOBYTE(ppMaterial) = byLinkTableIndex;
      if ( __make_item(v65->m_pMaster, v48, pfld, nIndex[j], byLinkTableIndex) )
      {
        v33 = 12;
        break;
      }
    }
  }
LABEL_70:
  if ( v33 )
  {
    if ( v33 == 10 )
    {
      nSocketIndex = -1;
      v53 = 0;
      if ( pfld->m_nFailOutItem >= 0 )
      {
        v53 = (unsigned __int8)v67 - pfld->m_nFailOutItem;
        if ( (v53 & 0x80000000) != 0 || (signed int)(unsigned __int8)v67 <= 0 )
        {
          v33 = 11;
          return 11;
        }
        CTakeOut::TakeOut_Lotto(nBuffer, (unsigned __int8)v67);
      }
      else
      {
        v54 = abs_0(pfld->m_nFailOutItem);
        v53 = (unsigned __int8)v67 - v54;
        if ( (v53 & 0x80000000) != 0 || (signed int)(unsigned __int8)v67 <= 0 )
        {
          v33 = 11;
          return 11;
        }
        if ( !_FailItemShortBuffer(nBuffer, v67, pfld->m_Material, pMt_Sv_Inva) )
        {
          v33 = 11;
          return 11;
        }
      }
      _combine_ex_item_result_zocl::_Result_ItemList_Buff::Init(&pSaveData->ItemBuff);
      pSaveData->ItemBuff.byItemListNum = v53;
      for ( j = 0; j < (signed int)v53; ++j )
      {
        nSocketIndex = nBuffer[j];
        if ( (unsigned __int8)v67 <= nSocketIndex || nSocketIndex < 0 )
        {
          v33 = 11;
          return 11;
        }
        v55 = &pSaveData->ItemBuff.RewardItemList[j];
        LODWORD(ppMaterial) = nSocketIndex;
        if ( !__destroy_item(v65->m_pMaster, v55, pMt_Sv_Inva[nSocketIndex], &v68[nSocketIndex], nSocketIndex) )
        {
          v33 = 4;
          return 4;
        }
      }
      for ( j = 0; j < (signed int)v53; ++j )
      {
        nSocketIndex = nBuffer[j];
        v18 = -v68[nSocketIndex].byAmount;
        v19 = pMt_Sv_Inva[nSocketIndex];
        bSend[0] = 1;
        LOBYTE(ppMaterial) = 1;
        CPlayer::Emb_AlterDurPoint(v65->m_pMaster, 0, v19->m_byStorageIndex, v18, 1, 1);
        Dst[nSocketIndex] = v68[nSocketIndex].byAmount;
      }
      nAmount = pfld->m_dwCommit;
      if ( nAmount )
      {
        CPlayer::SubDalant(v65->m_pMaster, nAmount);
        v57 = CPlayerDB::GetLevel(&v65->m_pMaster->m_Param);
        if ( v57 == 30 || v57 == 40 || v57 == 50 || v57 == 60 )
        {
          v62 = CPlayerDB::GetLevel(&v65->m_pMaster->m_Param);
          v63 = CPlayerDB::GetRaceCode(&v65->m_pMaster->m_Param);
          v20 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateFeeMoneyData(v20, v63, v62, nAmount);
        }
      }
      CPlayer::SendMsg_AlterMoneyInform(v65->m_pMaster, 0);
      v21 = v65->m_pMaster->m_szItemHistoryFileName;
      v22 = v65->m_pMaster->m_ObjID.m_wIndex;
      dwFailCount = v53;
      bSucc = 0;
      strFileName = v21;
      dwFee = nAmount;
      *(_QWORD *)bSend = Dst;
      ppMaterial = pMt_Sv_Inva;
      CMgrAvatorItemHistory::combine_ex_using_material(
        &CPlayer::s_MgrItemHistory,
        v22,
        pSaveData->dwCheckKey,
        v67,
        pMt_Sv_Inva,
        Dst,
        nAmount,
        v21,
        0,
        v53);
    }
  }
  else
  {
    for ( j = 0; j < (unsigned __int8)v67; ++j )
    {
      if ( IsOverLapItem(pMt_Sv_Inva[j]->m_byTableCode) )
      {
        v11 = -v68[j].byAmount;
        v12 = pMt_Sv_Inva[j];
        bSend[0] = 1;
        LOBYTE(ppMaterial) = 1;
        CPlayer::Emb_AlterDurPoint(v65->m_pMaster, 0, v12->m_byStorageIndex, v11, 1, 1);
        Dst[j] = v68[j].byAmount;
      }
      else
      {
        v13 = pMt_Sv_Inva[j];
        *(_QWORD *)bSend = "ItemCombineMgr::ConsumeMeterial_And_CalculateNewItems()";
        LOBYTE(ppMaterial) = 1;
        if ( !CPlayer::Emb_DelStorage(
                v65->m_pMaster,
                0,
                v13->m_byStorageIndex,
                1,
                1,
                "ItemCombineMgr::ConsumeMeterial_And_CalculateNewItems()") )
          return 5;
        Dst[j] = v68[j].byAmount;
      }
    }
    dwSub = pfld->m_dwCommit;
    if ( dwSub )
    {
      CPlayer::SubDalant(v65->m_pMaster, dwSub);
      v50 = CPlayerDB::GetLevel(&v65->m_pMaster->m_Param);
      if ( v50 == 30 || v50 == 40 || v50 == 50 || v50 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&v65->m_pMaster->m_Param);
        v61 = CPlayerDB::GetRaceCode(&v65->m_pMaster->m_Param);
        v15 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v15, v61, nLv, dwSub);
      }
    }
    CPlayer::SendMsg_AlterMoneyInform(v65->m_pMaster, 0);
    v16 = v65->m_pMaster->m_szItemHistoryFileName;
    v17 = v65->m_pMaster->m_ObjID.m_wIndex;
    dwFailCount = 0;
    bSucc = 1;
    strFileName = v16;
    dwFee = dwSub;
    *(_QWORD *)bSend = Dst;
    ppMaterial = pMt_Sv_Inva;
    CMgrAvatorItemHistory::combine_ex_using_material(
      &CPlayer::s_MgrItemHistory,
      v17,
      pSaveData->dwCheckKey,
      v67,
      pMt_Sv_Inva,
      Dst,
      dwSub,
      v16,
      1,
      0);
  }
  return v33;
}
