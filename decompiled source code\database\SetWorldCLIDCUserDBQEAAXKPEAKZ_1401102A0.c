/*
 * Function: ?SetWorldCLID@CUserDB@@QEAAXKPEAK@Z
 * Address: 0x1401102A0
 */

void __fastcall CUserDB::SetWorldCLID(CUserDB *this, unsigned int dwSerial, unsigned int *pipAddr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8->m_idWorld.dwSerial = dwSerial;
  v8->m_ipAddress = *pipAddr;
  _SYNC_STATE::Init(&v8->m_ss);
  n = v8->m_idWorld.wIndex;
  v5 = CTSingleton<CNationSettingManager>::Instance();
  CNationSettingManager::OnConnectSession(v5, n);
}
