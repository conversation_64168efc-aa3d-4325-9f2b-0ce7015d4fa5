/*
 * Function: j_?InsertChangeClassLogAfterInitClass@CRFWorldDatabase@@QEAA_NKEPEAD0HEGEEEEE@Z
 * Address: 0x1400029F0
 */

bool __fastcall CRFWorldDatabase::InsertChangeClassLogAfterInitClass(CRFWorldDatabase *this, unsigned int dwCharacSerial, char byType, char *szPrevClass, char *szNextClass, int nClassInitCnt, char byLastClassGrade, unsigned __int16 dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec)
{
  return CRFWorldDatabase::InsertChangeClassLogAfterInitClass(
           this,
           dwCharacSerial,
           byType,
           szPrevClass,
           szNextClass,
           nClassInitCnt,
           byLastClassGrade,
           dwYear,
           byMonth,
           byDay,
           byHour,
           byMin,
           bySec);
}
