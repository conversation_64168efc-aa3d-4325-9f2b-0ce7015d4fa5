/*
 * Function: ?SFContDelMessage@CPlayer@@UEAAXEE_N0@Z
 * Address: 0x140053F70
 */

void __fastcall CPlayer::SFContDelMessage(CPlayer *this, char byContCode, char byListIndex, bool bSend, bool bAura)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  _sf_continous *pSF_Cont; // [sp+20h] [bp-18h]@6
  _base_fld *v9; // [sp+28h] [bp-10h]@11
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  char v11; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1

  v12 = byListIndex;
  v11 = byContCode;
  v10 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v10->m_bOper )
  {
    if ( bSend )
    {
      pSF_Cont = 0i64;
      if ( bAura )
        pSF_Cont = &v10->m_SFContAura[(unsigned __int8)byContCode][(unsigned __int8)byListIndex];
      else
        pSF_Cont = &v10->m_SFCont[(unsigned __int8)byContCode][(unsigned __int8)byListIndex];
      CPlayer::SendMsg_DelEffect(v10, pSF_Cont->m_byEffectCode, pSF_Cont->m_wEffectIndex, pSF_Cont->m_byLv);
      CEquipItemSFAgent::EndContSF(&v10->EquipItemSFAgent, pSF_Cont);
      if ( _IsXmasSnowEffect(pSF_Cont) )
        v10->m_bSnowMan = 0;
      v9 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
      if ( v9 && pSF_Cont->m_byEffectCode == 3 && pSF_Cont->m_wEffectIndex == v9->m_dwIndex )
        v10->m_bAfterEffect = 0;
    }
    if ( v10->m_pUserDB )
    {
      if ( !bAura )
        CUserDB::Update_SFContDelete(v10->m_pUserDB, v11, v12);
    }
  }
}
