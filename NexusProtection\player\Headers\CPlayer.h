#pragma once

/**
 * @file CPlayer.h
 * @brief Modern C++20 CPlayer class definition
 * 
 * Refactored from decompiled source: LoadCPlayerQEAA_NPEAVCUserDB_NZ_1400489B0.c
 * Original complexity: 287 lines, 37KB+ data structures
 * 
 * This file provides a modern, type-safe, and maintainable implementation
 * of the core player management system for NexusProtection.
 */

#include <memory>
#include <string>
#include <array>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <chrono>
#include <optional>
#include <atomic>

// Forward declarations
class CUserDB;
class CPlayerDB;
class CMapData;
class CGuild;
class CPartyPlayer;
class CDarkHoleChannel;

// Legacy structure forward declarations
struct _AVATOR_DATA;
struct _object_id;
struct _MASTERY_PARAM;
struct _DTRADE_PARAM;
struct _WEAPON_PARAM;
struct _TOWER_PARAM;
struct _TRAP_PARAM;
struct _BUDDY_LIST;
struct _CRYMSG_LIST;
struct _NameChangeBuddyInfo;
struct CPvpPointLimiter;
struct CPvpOrderView;
struct CPvpCashPoint;
struct CCouponMgr;
struct CPotionParam;

namespace NexusProtection {
namespace Player {

/**
 * @brief Player loading result enumeration
 */
enum class PlayerLoadResult : uint8_t {
    Success = 0,
    InvalidUser,
    InvalidMap,
    DatabaseError,
    ConversionError,
    SystemError
};

/**
 * @brief Player state enumeration
 */
enum class PlayerState : uint8_t {
    Inactive = 0,
    Loading,
    Active,
    MapLoading,
    Disconnecting,
    Error
};

/**
 * @brief Player cheat flags structure
 */
struct PlayerCheatFlags {
    bool successfulMake{false};
    bool noMaterialUse{false};
    bool matchless{false};
    bool freeRecallWait{false};
    bool freeSFByClass{false};
    bool lootFree{false};
    bool neverDie{false};
    bool sfDelayNotCheck{false};
};

/**
 * @brief Player blocking flags structure
 */
struct PlayerBlockFlags {
    bool party{false};
    bool whisper{false};
    bool trade{false};
    bool guildBattleMsg{false};
};

/**
 * @brief Player download flags structure
 */
struct PlayerDownloadFlags {
    bool linkBoard{false};
    bool special{false};
    bool quest{false};
    bool cum{false};
    bool force{false};
    bool inventory{false};
    bool base{false};
    bool ampInventory{false};
    bool buddyList{false};
    bool guild{false};
    bool guildList{false};
};

/**
 * @brief Player statistics structure
 */
struct PlayerStats {
    std::array<int32_t, 4> maxPoints{};      // HP, FP, SP, DP
    std::array<int32_t, 4> oldPoints{};      // Previous values
    int32_t oldMaxDP{0};
    int32_t maxAttackPoint{0};
    int32_t animusAttackPoint{0};
    int32_t trapMaxAttackPoint{0};
    uint32_t expRate{0};
    float equipSpeed{0.0f};
    float talikDefencePoint{0.0f};
    float talikAvoidPoint{0.0f};
};

/**
 * @brief Player position and movement data
 */
struct PlayerMovement {
    std::array<std::array<float, 2>, 3> groupMapPoints{};
    std::array<uint8_t, 3> groupMapPointMapCodes{};
    std::array<uint16_t, 3> groupMapPointLayerIndices{};
    std::array<uint32_t, 3> lastGroupMapPointTimes{};
    uint32_t moveCount{0};
    uint32_t targetCount{0};
    uint32_t attackCount{0};
    uint16_t regionIndex{0xFFFF};
    uint16_t regionMapIndex{0xFFFF};
    uint32_t lastCheckRegionTime{0};
};

/**
 * @brief Player security and anti-cheat data
 */
struct PlayerSecurity {
    uint8_t plusKey{0};
    uint16_t xorKey{0};
    bool checkMovePacket{false};
    bool spyGM{false};
    std::array<uint8_t, 15> effectEquipCodes{};
    uint32_t pcBangGiveItemListIndex{0xFFFFFFFF};
};

/**
 * @brief Modern C++20 CPlayer class
 *
 * This class represents a player character in the game world.
 * It manages all player-related data, state, and operations.
 */
class CPlayer : public std::enable_shared_from_this<CPlayer> {
public:
    // Constructor and Destructor
    CPlayer();
    virtual ~CPlayer();

    // Disable copy constructor and assignment operator
    CPlayer(const CPlayer&) = delete;
    CPlayer& operator=(const CPlayer&) = delete;

    // Enable move constructor and assignment operator
    CPlayer(CPlayer&&) noexcept = default;
    CPlayer& operator=(CPlayer&&) noexcept = default;

    /**
     * @brief Load player data from user database
     * 
     * Modern implementation of the original Load method.
     * Loads and initializes all player data from the database.
     * 
     * @param userDB Pointer to user database
     * @param firstStart Whether this is the first login
     * @return PlayerLoadResult indicating success or failure
     */
    PlayerLoadResult Load(std::shared_ptr<CUserDB> userDB, bool firstStart = false);

    /**
     * @brief Initialize player systems
     * 
     * Initializes all player subsystems after loading.
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown player systems
     * 
     * Safely shuts down all player subsystems.
     */
    void Shutdown();

    /**
     * @brief Update player state
     * 
     * Called every frame to update player state.
     * 
     * @param deltaTime Time elapsed since last update
     */
    void Update(float deltaTime);

    // State Management
    PlayerState GetState() const { return m_state; }
    void SetState(PlayerState state) { m_state = state; }
    bool IsLoaded() const { return m_loaded; }
    bool IsActive() const { return m_state == PlayerState::Active; }

    // Basic Properties
    uint32_t GetSerial() const { return m_serial; }
    uint16_t GetIndex() const { return m_index; }
    uint8_t GetUserDegree() const { return m_userDegree; }
    uint8_t GetSubDegree() const { return m_subDegree; }
    bool IsFirstStart() const { return m_firstStart; }

    // Map and Position
    std::shared_ptr<CMapData> GetCurrentMap() const { return m_currentMap; }
    bool IsMapLoading() const { return m_mapLoading; }
    void SetMapLoading(bool loading) { m_mapLoading = loading; }
    bool IsOutOfMap() const { return m_outOfMap; }

    // Player Database Access
    std::shared_ptr<CPlayerDB> GetPlayerDB() const { return m_playerDB; }
    std::shared_ptr<CUserDB> GetUserDB() const { return m_userDB; }

    // Statistics and Combat
    const PlayerStats& GetStats() const { return m_stats; }
    PlayerStats& GetStats() { return m_stats; }
    
    // Flags and Settings
    const PlayerCheatFlags& GetCheatFlags() const { return m_cheatFlags; }
    const PlayerBlockFlags& GetBlockFlags() const { return m_blockFlags; }
    const PlayerDownloadFlags& GetDownloadFlags() const { return m_downloadFlags; }
    
    // Movement and Position
    const PlayerMovement& GetMovement() const { return m_movement; }
    PlayerMovement& GetMovement() { return m_movement; }

    // Security
    const PlayerSecurity& GetSecurity() const { return m_security; }

    /**
     * @brief Get last error message
     * @return Last error message
     */
    const std::string& GetLastError() const { return m_lastError; }

protected:
    // Core player data
    std::atomic<PlayerState> m_state{PlayerState::Inactive};
    std::atomic<bool> m_loaded{false};
    std::atomic<bool> m_mapLoading{false};
    std::atomic<bool> m_outOfMap{false};
    std::atomic<bool> m_firstStart{false};
    
    // Identifiers
    uint32_t m_serial{0};
    uint16_t m_index{0};
    uint8_t m_userDegree{0};
    uint8_t m_subDegree{0};
    uint8_t m_damagePart{0xFF};
    
    // Database references
    std::shared_ptr<CUserDB> m_userDB;
    std::shared_ptr<CPlayerDB> m_playerDB;
    std::shared_ptr<CMapData> m_currentMap;
    
    // Player subsystems
    PlayerStats m_stats;
    PlayerCheatFlags m_cheatFlags;
    PlayerBlockFlags m_blockFlags;
    PlayerDownloadFlags m_downloadFlags;
    PlayerMovement m_movement;
    PlayerSecurity m_security;
    
    // Timing and synchronization
    std::chrono::steady_clock::time_point m_lastUpdate;
    mutable std::mutex m_playerMutex;
    
    // Error handling
    std::string m_lastError;

    // Legacy compatibility members (for gradual migration)
    void* m_legacyVfptr{nullptr};  ///< Legacy virtual function table pointer

private:
    /**
     * @brief Set last error message
     * @param error Error message
     */
    void SetLastError(const std::string& error) { m_lastError = error; }

    /**
     * @brief Initialize player statistics
     * @return true if successful, false otherwise
     */
    bool InitializeStats();

    /**
     * @brief Initialize player subsystems
     * @return true if successful, false otherwise
     */
    bool InitializeSubsystems();

    /**
     * @brief Validate player data
     * @return true if valid, false otherwise
     */
    bool ValidatePlayerData() const;

    /**
     * @brief Setup map and position
     * @param avatorData Avatar data from database
     * @return true if successful, false otherwise
     */
    bool SetupMapAndPosition(const _AVATOR_DATA& avatorData);

    /**
     * @brief Initialize player database
     * @return true if successful, false otherwise
     */
    bool InitializePlayerDatabase();

    /**
     * @brief Load avatar data from user database
     * @param avatorData Reference to avatar data structure
     * @return true if successful, false otherwise
     */
    bool LoadAvatarData(_AVATOR_DATA& avatorData);

    /**
     * @brief Initialize player systems
     * @return true if successful, false otherwise
     */
    bool InitializePlayerSystems();

    /**
     * @brief Finalize loading process
     * @return true if successful, false otherwise
     */
    bool FinalizeLoading();

    /**
     * @brief Initialize default values
     */
    void InitializeDefaults();
};

} // namespace Player
} // namespace NexusProtection
