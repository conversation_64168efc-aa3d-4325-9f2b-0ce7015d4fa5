/*
 * Function: ?SendMsg_SetGroupMapPoint@CPlayer@@QEAAXEEEPEAME@Z
 * Address: 0x1400E7250
 */

void __fastcall CPlayer::SendMsg_SetGroupMapPoint(CPlayer *this, char byRetCode, char byGroupType, char byMapCode, float *pzTar, char byRemain)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v10; // [sp+39h] [bp-4Fh]@4
  char v11; // [sp+3Ah] [bp-4Eh]@4
  char v12; // [sp+3Bh] [bp-4Dh]@4
  int v13; // [sp+3Ch] [bp-4Ch]@4
  int v14; // [sp+40h] [bp-48h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v16; // [sp+65h] [bp-23h]@4
  CPlayer *v17; // [sp+90h] [bp+8h]@1

  v17 = this;
  v6 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  szMsg = byRetCode;
  v10 = byGroupType;
  v11 = byMapCode;
  v13 = *(_DWORD *)pzTar;
  v14 = *((_DWORD *)pzTar + 1);
  v12 = byRemain;
  pbyType = 13;
  v16 = 114;
  CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xCu);
}
