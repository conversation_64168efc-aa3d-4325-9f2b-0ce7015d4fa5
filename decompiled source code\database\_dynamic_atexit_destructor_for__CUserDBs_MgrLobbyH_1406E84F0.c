/*
 * Function: _dynamic_atexit_destructor_for__CUserDB::s_MgrLobbyHistory__
 * Address: 0x1406E84F0
 */

void __cdecl dynamic_atexit_destructor_for__CUserDB::s_MgrLobbyHistory__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -*********;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CMgrAccountLobbyHistory::~CMgrAccountLobbyHistory(&CUserDB::s_MgrLobbyHistory);
}
