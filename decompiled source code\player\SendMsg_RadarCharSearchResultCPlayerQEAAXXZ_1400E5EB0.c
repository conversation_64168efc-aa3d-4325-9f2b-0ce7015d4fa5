/*
 * Function: ?SendMsg_RadarCharSearchResult@CPlayer@@QEAAXXZ
 * Address: 0x1400E5EB0
 */

void __fastcall CPlayer::SendMsg_RadarCharSearchResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@11
  __int64 v4; // [sp+0h] [bp-258h]@1
  _radar_char_list_result_zocl Dst; // [sp+40h] [bp-218h]@4
  char *v6; // [sp+218h] [bp-40h]@8
  int j; // [sp+220h] [bp-38h]@8
  char pbyType; // [sp+234h] [bp-24h]@11
  char v9; // [sp+235h] [bp-23h]@11
  CPlayer *v10; // [sp+260h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 148i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _radar_char_list_result_zocl::_radar_char_list_result_zocl(&Dst);
  Dst.bEnd = v10->m_pUserDB->m_RadarItemMgr.m_bPlayerEnd && v10->m_pUserDB->m_RadarItemMgr.m_bMonEnd;
  v6 = (char *)&v10->m_pUserDB->m_RadarItemMgr.m_RadarResult;
  Dst.byListNum = *v6;
  for ( j = 0; j < *(_DWORD *)v6; ++j )
  {
    Dst.CharInfo[j].m_byCharType = v6[12 * j + 4];
    memcpy_0(Dst.CharInfo[j].m_fPos, &v6[12 * j + 8], 8ui64);
  }
  pbyType = 7;
  v9 = 52;
  v3 = _radar_char_list_result_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, (char *)&Dst.bEnd, v3);
}
