/*
 * Function: j_?InsertCharacterSelectLog@CRFWorldDatabase@@QEAA_NKPEADK0GEEEEE@Z
 * Address: 0x140011E14
 */

bool __fastcall CRFWorldDatabase::InsertCharacterSelectLog(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *wszAccount, unsigned int dwCharacSerial, char *pwszCharacName, unsigned __int16 dwYear, char by<PERSON><PERSON><PERSON>, char byDay, char byHour, char byMin, char bySec)
{
  return CRFWorldDatabase::InsertCharacterSelectLog(
           this,
           dwAccountSerial,
           wszAccount,
           dwCharacSerial,
           pwszCharacName,
           dwYear,
           byMonth,
           byDay,
           byHour,
           byMin,
           bySec);
}
