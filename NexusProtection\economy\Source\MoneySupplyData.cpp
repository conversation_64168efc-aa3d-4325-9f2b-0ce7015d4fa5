#include "../Headers/MoneySupplyData.h"
#include <cstring>
#include <algorithm>

namespace NexusProtection::Economy {

    void ConvertToLegacy(const MoneySupplyData& modern, _MONEY_SUPPLY_DATA& legacy) noexcept {
        // Clear legacy structure
        std::memset(&legacy, 0, sizeof(_MONEY_SUPPLY_DATA));

        // Copy amounts
        for (size_t i = 0; i < std::min(modern.amounts.size(), static_cast<size_t>(8)); ++i) {
            legacy.dwAmount[i] = modern.amounts[i];
        }

        // Copy trade statistics
        for (size_t tradeType = 0; tradeType < std::min(modern.tradeStats.size(), static_cast<size_t>(4)); ++tradeType) {
            const auto& stats = modern.tradeStats[tradeType];
            
            // Copy level statistics
            for (size_t level = 0; level < std::min(stats.levelStats.levelCounts.size(), static_cast<size_t>(4)); ++level) {
                legacy.ms_data[tradeType].nLv[level] = stats.levelStats.levelCounts[level];
            }
            
            // Copy race statistics
            for (size_t race = 0; race < std::min(stats.raceStats.raceCounts.size(), static_cast<size_t>(3)); ++race) {
                legacy.ms_data[tradeType].nRace[race] = stats.raceStats.raceCounts[race];
            }
        }

        // Copy fee race statistics
        for (size_t race = 0; race < std::min(modern.feeRaceStats.raceCounts.size(), static_cast<size_t>(3)); ++race) {
            legacy.nFeeRace[race] = modern.feeRaceStats.raceCounts[race];
        }

        // Copy fee level statistics
        for (size_t level = 0; level < std::min(modern.feeLevelStats.levelCounts.size(), static_cast<size_t>(4)); ++level) {
            legacy.nFeeLv[level] = modern.feeLevelStats.levelCounts[level];
        }

        // Copy honor guild race statistics
        for (size_t tradeType = 0; tradeType < std::min(modern.honorGuildRaceStats.size(), static_cast<size_t>(2)); ++tradeType) {
            for (size_t race = 0; race < std::min(modern.honorGuildRaceStats[tradeType].size(), static_cast<size_t>(3)); ++race) {
                legacy.nHonorGuildRace[tradeType][race] = modern.honorGuildRaceStats[tradeType][race];
            }
        }

        // Copy buy unit level statistics
        for (size_t level = 0; level < std::min(modern.buyUnitLevelStats.levelCounts.size(), static_cast<size_t>(4)); ++level) {
            legacy.nBuyUnitLv[level] = modern.buyUnitLevelStats.levelCounts[level];
        }
    }

    void ConvertFromLegacy(const _MONEY_SUPPLY_DATA& legacy, MoneySupplyData& modern) noexcept {
        // Initialize modern structure
        modern.Initialize();

        // Copy amounts
        for (size_t i = 0; i < std::min(modern.amounts.size(), static_cast<size_t>(8)); ++i) {
            modern.amounts[i] = legacy.dwAmount[i];
        }

        // Copy trade statistics
        for (size_t tradeType = 0; tradeType < std::min(modern.tradeStats.size(), static_cast<size_t>(4)); ++tradeType) {
            auto& stats = modern.tradeStats[tradeType];
            
            // Copy level statistics
            for (size_t level = 0; level < std::min(stats.levelStats.levelCounts.size(), static_cast<size_t>(4)); ++level) {
                stats.levelStats.levelCounts[level] = legacy.ms_data[tradeType].nLv[level];
            }
            
            // Copy race statistics
            for (size_t race = 0; race < std::min(stats.raceStats.raceCounts.size(), static_cast<size_t>(3)); ++race) {
                stats.raceStats.raceCounts[race] = legacy.ms_data[tradeType].nRace[race];
            }
        }

        // Copy fee race statistics
        for (size_t race = 0; race < std::min(modern.feeRaceStats.raceCounts.size(), static_cast<size_t>(3)); ++race) {
            modern.feeRaceStats.raceCounts[race] = legacy.nFeeRace[race];
        }

        // Copy fee level statistics
        for (size_t level = 0; level < std::min(modern.feeLevelStats.levelCounts.size(), static_cast<size_t>(4)); ++level) {
            modern.feeLevelStats.levelCounts[level] = legacy.nFeeLv[level];
        }

        // Copy honor guild race statistics
        for (size_t tradeType = 0; tradeType < std::min(modern.honorGuildRaceStats.size(), static_cast<size_t>(2)); ++tradeType) {
            for (size_t race = 0; race < std::min(modern.honorGuildRaceStats[tradeType].size(), static_cast<size_t>(3)); ++race) {
                modern.honorGuildRaceStats[tradeType][race] = legacy.nHonorGuildRace[tradeType][race];
            }
        }

        // Copy buy unit level statistics
        for (size_t level = 0; level < std::min(modern.buyUnitLevelStats.levelCounts.size(), static_cast<size_t>(4)); ++level) {
            modern.buyUnitLevelStats.levelCounts[level] = legacy.nBuyUnitLv[level];
        }
    }

} // namespace NexusProtection::Economy

// Legacy C interface implementation
extern "C" {
    void _MONEY_SUPPLY_DATA_init(_MONEY_SUPPLY_DATA* data) {
        if (data) {
            std::memset(data, 0, sizeof(_MONEY_SUPPLY_DATA));
        }
    }

    int _MONEY_SUPPLY_DATA_size(const _MONEY_SUPPLY_DATA* data) {
        (void)data; // Suppress unused parameter warning
        return sizeof(_MONEY_SUPPLY_DATA);
    }
}
