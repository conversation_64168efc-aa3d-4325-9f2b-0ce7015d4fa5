/*
 * Function: ?pc_DTradeAnswerRequest@CPlayer@@QEAAXPEAU_CLID@@@Z
 * Address: 0x1400F3A20
 */

void __usercall CPlayer::pc_DTradeAnswerRequest(CPlayer *this@<rcx>, _CLID *pid<PERSON>ker@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@17
  int v6; // eax@32
  int v7; // eax@32
  __int64 v8; // [sp+0h] [bp-C8h]@1
  unsigned int *pl_dwKey; // [sp+20h] [bp-A8h]@32
  char v10; // [sp+30h] [bp-98h]@4
  CGameObject *v11; // [sp+38h] [bp-90h]@4
  unsigned int pdwCode[12]; // [sp+48h] [bp-80h]@31
  unsigned int pdwKey[7]; // [sp+78h] [bp-50h]@31
  int j; // [sp+94h] [bp-34h]@29
  int v15; // [sp+98h] [bp-30h]@17
  int v16; // [sp+9Ch] [bp-2Ch]@31
  int v17; // [sp+A0h] [bp-28h]@31
  unsigned int *v18; // [sp+A8h] [bp-20h]@32
  unsigned int *v19; // [sp+B0h] [bp-18h]@32
  CPlayer *pAnswer; // [sp+D0h] [bp+8h]@1
  _CLID *v21; // [sp+D8h] [bp+10h]@1

  v21 = pidAsker;
  pAnswer = this;
  v3 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = 0;
  v11 = (CGameObject *)(&g_Player.vfptr + 6357 * pidAsker->wIndex);
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pAnswer->m_id.wIndex) == 99 )
  {
    pAnswer->m_pmTrd.bDTradeMode = 0;
    v11[229].m_bCorpse = 0;
    v10 = 23;
  }
  else if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v11[226].m_rtPer100.m_wCurTable) == 99 )
  {
    pAnswer->m_pmTrd.bDTradeMode = 0;
    v11[229].m_bCorpse = 0;
    v10 = 24;
  }
  else if ( v11->m_dwObjSerial == v21->dwSerial )
  {
    if ( LOWORD(v11[229].m_dwLastSendTime) == pAnswer->m_ObjID.m_wIndex
      && LODWORD(v11[229].m_fCurPos[0]) == pAnswer->m_dwObjSerial )
    {
      if ( pAnswer->m_pmTrd.bDTradeMode || v11[229].m_bCorpse )
      {
        v10 = 2;
      }
      else if ( v11->m_bLive
             && (v15 = CPlayerDB::GetRaceCode((CPlayerDB *)&v11[10].m_bCorpse),
                 v5 = CPlayerDB::GetRaceCode(&pAnswer->m_Param),
                 v15 == v5)
             && !v11->m_bCorpse
             && v11->m_pCurMap == pAnswer->m_pCurMap
             && CGameObject::GetCurSecNum(v11) != -1 )
      {
        if ( pAnswer->m_pCurMap->m_pMapSet->m_nMapType == 1 )
        {
          v10 = 6;
        }
        else if ( pAnswer->m_byUserDgr == BYTE4(v11[9].m_SectorNetPoint.m_pNext) )
        {
          GetSqrt(v11->m_fCurPos, pAnswer->m_fCurPos);
          if ( a3 > 100.0 )
            v10 = 5;
        }
        else
        {
          v10 = 6;
        }
      }
      else
      {
        v10 = 3;
      }
    }
    else
    {
      v10 = 4;
    }
  }
  else
  {
    v10 = 1;
  }
  if ( v10 )
  {
    CPlayer::SendMsg_DTradeAnswerResult(pAnswer, v10);
  }
  else
  {
    for ( j = 0; j < 4; ++j )
    {
      v16 = rand() << 16;
      pdwCode[j] = rand() + v16;
      v17 = rand() << 16;
      pdwKey[j] = rand() + v17;
    }
    v18 = CalcCodeKey(pdwCode);
    v6 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&pAnswer->m_Param.m_dbInven.m_nListNum);
    pl_dwKey = v18;
    _DTRADE_PARAM::SetDTradeStart(&pAnswer->m_pmTrd, v11->m_ObjID.m_wIndex, v11->m_dwObjSerial, v6, v18);
    v19 = CalcCodeKey(pdwKey);
    v7 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)((char *)&v11[10].m_SectorNetPoint.m_pItem + 3));
    pl_dwKey = v19;
    _DTRADE_PARAM::SetDTradeStart(
      (_DTRADE_PARAM *)&v11[229].m_bCorpse,
      pAnswer->m_ObjID.m_wIndex,
      pAnswer->m_dwObjSerial,
      v7,
      v19);
    CPlayer::SendMsg_DTradeStartInform(pAnswer, (CPlayer *)v11, pAnswer, pdwCode);
    CPlayer::SendMsg_DTradeStartInform((CPlayer *)v11, (CPlayer *)v11, pAnswer, pdwKey);
  }
}
