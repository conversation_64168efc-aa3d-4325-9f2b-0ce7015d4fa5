/*
 * Function: ??$GetValue@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@@NameValuePairs@CryptoPP@@QEBA_NPEBDAEAV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@1@@Z
 * Address: 0x1405A23C0
 */

int __fastcall CryptoPP::NameValuePairs::GetValue<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(__int64 a1, __int64 a2, __int64 a3)
{
  return (*(int (__fastcall **)(__int64, __int64, type_info *, __int64))(*(_QWORD *)a1 + 8i64))(
           a1,
           a2,
           &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
           a3);
}
