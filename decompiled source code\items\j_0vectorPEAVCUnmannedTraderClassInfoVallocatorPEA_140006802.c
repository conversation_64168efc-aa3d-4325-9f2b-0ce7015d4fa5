/*
 * Function: j_??0?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x140006802
 */

void __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(this);
}
