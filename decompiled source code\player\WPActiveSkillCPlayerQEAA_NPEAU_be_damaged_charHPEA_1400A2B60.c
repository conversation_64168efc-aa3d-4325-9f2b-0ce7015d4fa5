/*
 * Function: ?WPActiveSkill@CPlayer@@QEAA_NPEAU_be_damaged_char@@HPEAU_skill_fld@@H@Z
 * Address: 0x1400A2B60
 */

bool __fastcall CPlayer::WPActiveSkill(CPlayer *this, _be_damaged_char *pDam<PERSON>ist, int nDamagedObjNum, _skill_fld *pSkillFld, int nEffectCode)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v8; // rax@37
  CGuildRoomSystem *v9; // rax@54
  __int64 v10; // [sp+0h] [bp-D8h]@1
  int nSkillLv; // [sp+20h] [bp-B8h]@39
  CCharacter *pDst; // [sp+40h] [bp-98h]@4
  bool v13; // [sp+48h] [bp-90h]@15
  bool v14; // [sp+49h] [bp-8Fh]@15
  int v15; // [sp+4Ch] [bp-8Ch]@16
  int j; // [sp+50h] [bp-88h]@21
  char v17; // [sp+64h] [bp-74h]@56
  bool v18; // [sp+84h] [bp-54h]@56
  CUserDB *v19; // [sp+98h] [bp-40h]@37
  int n; // [sp+A0h] [bp-38h]@37
  CGuild *v21; // [sp+A8h] [bp-30h]@37
  CUserDB *v22; // [sp+B0h] [bp-28h]@54
  int v23; // [sp+B8h] [bp-20h]@54
  CGuild *v24; // [sp+C0h] [bp-18h]@54
  CPlayer *v25; // [sp+E0h] [bp+8h]@1
  _be_damaged_char *v26; // [sp+E8h] [bp+10h]@1
  int v27; // [sp+F0h] [bp+18h]@1
  _skill_fld *pSkillFlda; // [sp+F8h] [bp+20h]@1

  pSkillFlda = pSkillFld;
  v27 = nDamagedObjNum;
  v26 = pDamList;
  v25 = this;
  v5 = &v10;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  pDst = pDamList->m_pChar;
  if ( !pDst || !pSkillFld )
    return 0;
  if ( !nEffectCode && pSkillFld->m_nMastIndex > 8u )
    return 0;
  if ( pSkillFld->m_nTempEffectType >= 150 )
    return 0;
  if ( pSkillFld->m_nTempEffectType == -1 && pSkillFld->m_nContEffectType == -1 )
    return 0;
  v13 = IsUsableTempEffectAtStoneState(pSkillFld->m_nTempEffectType);
  v14 = 0;
  if ( nEffectCode )
    v15 = 1;
  else
    v15 = v25->m_pmWpn.nActiveEffLvl;
  if ( v15 > 7 )
    v15 = 7;
  if ( v27 <= 1 )
  {
    if ( !CCharacter::IsEffectableDst((CCharacter *)&v25->vfptr, pSkillFlda->m_strActableDst, pDst) )
      return 0;
    if ( pSkillFlda->m_nContEffectType != -1
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst) )
    {
      return 0;
    }
    if ( !pSkillFlda->m_nContEffectType
      && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v25->vfptr->IsAttackableInTown)(v25)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst) )
    {
      if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))v25->vfptr->IsInTown)(v25) )
        return 0;
      if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst) )
        return 0;
      if ( v25->m_Param.m_pGuild )
      {
        v22 = v25->m_pUserDB;
        v23 = v25->m_ObjID.m_wIndex;
        v24 = v25->m_Param.m_pGuild;
        v9 = CGuildRoomSystem::GetInstance();
        if ( CGuildRoomSystem::IsGuildRoomMemberIn(v9, v24->m_dwSerial, v23, v22->m_dwSerial) )
          return 0;
      }
    }
    v14 = CCharacter::AssistSkill((CCharacter *)&v25->vfptr, pDst, nEffectCode, pSkillFlda, v15, &v17, &v18);
    if ( !v17 )
      v26->m_bActiveSucc = 1;
  }
  else
  {
    for ( j = 0; j < v27; ++j )
    {
      if ( CCharacter::IsEffectableDst((CCharacter *)&v25->vfptr, pSkillFlda->m_strActableDst, pDst)
        && (pSkillFlda->m_nContEffectType == -1
         || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst))
        && (!_effect_parameter::GetEff_State(&pDst->m_EP, 20) || pSkillFlda->m_nTempEffectType != -1 && v13)
        && !_effect_parameter::GetEff_State(&pDst->m_EP, 28) )
      {
        if ( pSkillFlda->m_nContEffectType
          || (unsigned __int8)((int (__fastcall *)(CPlayer *))v25->vfptr->IsAttackableInTown)(v25)
          || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst)
          || !(unsigned __int8)((int (__fastcall *)(CPlayer *))v25->vfptr->IsInTown)(v25)
          && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst)
          && (!v25->m_Param.m_pGuild
           || (v19 = v25->m_pUserDB,
               n = v25->m_ObjID.m_wIndex,
               v21 = v25->m_Param.m_pGuild,
               v8 = CGuildRoomSystem::GetInstance(),
               !CGuildRoomSystem::IsGuildRoomMemberIn(v8, v21->m_dwSerial, n, v19->m_dwSerial))) )
        {
          nSkillLv = v15;
          if ( CCharacter::AssistSkillToOne((CCharacter *)&v25->vfptr, v26[j].m_pChar, nEffectCode, pSkillFlda, v15) )
          {
            v26[j].m_bActiveSucc = 1;
            v14 = 1;
          }
        }
      }
    }
  }
  return v14;
}
