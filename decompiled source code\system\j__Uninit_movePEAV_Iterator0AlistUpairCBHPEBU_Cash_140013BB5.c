/*
 * Function: j_??$_Uninit_move@PEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@2@@std@@PEAV123@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@2@@std@@@3@U_Undefined_move_tag@3@@std@@YAPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@2@@0@PEAV120@00AEAV?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@2@@std@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013BB5
 */

std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *__fastcall std::_Uninit_move<std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0> *,std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0> *,std::allocator<std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0>>,std::_Undefined_move_tag>(std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *_First, std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *_Last, std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *_Dest, std::allocator<std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> > *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0> *,std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0> *,std::allocator<std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0>>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
