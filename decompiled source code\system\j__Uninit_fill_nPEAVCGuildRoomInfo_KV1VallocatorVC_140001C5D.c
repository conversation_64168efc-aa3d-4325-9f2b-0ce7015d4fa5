/*
 * Function: j_??$_Uninit_fill_n@PEAVCGuildRoomInfo@@_KV1@V?$allocator@VCGuildRoomInfo@@@std@@@std@@YAXPEAVCGuildRoomInfo@@_KAEBV1@AEAV?$allocator@VCGuildRoomInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140001C5D
 */

void __fastcall std::_Uninit_fill_n<CGuildRoomInfo *,unsigned __int64,CGuildRoomInfo,std::allocator<CGuildRoomInfo>>(CGuildRoomInfo *_First, unsigned __int64 _Count, CGuildRoomInfo *_Val, std::allocator<CGuildRoomInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CGuildRoomInfo *,unsigned __int64,CGuildRoomInfo,std::allocator<CGuildRoomInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
