/*
 * Function: ?StoreInitialize@FileStore@CryptoPP@@EEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14061D310
 */

void __fastcall CryptoPP::FileStore::StoreInitialize(CryptoPP::FileStore *this, const struct CryptoPP::NameValuePairs *a2)
{
  const char *v2; // rax@4
  CryptoPP::Name *v3; // rcx@4
  const char *v4; // rax@5
  __int64 v5; // rax@8
  __int64 v6; // rax@8
  std::basic_istream<char,std::char_traits<char> > *v7; // rax@11
  const char *v8; // rax@12
  __int64 v9; // [sp+20h] [bp-D8h]@4
  int v10; // [sp+28h] [bp-D0h]@8
  void *v11; // [sp+30h] [bp-C8h]@4
  _QWORD *v12; // [sp+38h] [bp-C0h]@1
  __int64 v13; // [sp+40h] [bp-B8h]@8
  char v14; // [sp+48h] [bp-B0h]@9
  char v15; // [sp+98h] [bp-60h]@9
  unsigned __int8 v16; // [sp+C8h] [bp-30h]@9
  __int64 v17; // [sp+D0h] [bp-28h]@1
  void *v18; // [sp+D8h] [bp-20h]@2
  int v19; // [sp+E0h] [bp-18h]@6
  int v20; // [sp+E4h] [bp-14h]@8
  std::basic_istream<char,std::char_traits<char> > **v21; // [sp+E8h] [bp-10h]@12
  CryptoPP::FileStore *v22; // [sp+100h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v23; // [sp+108h] [bp+10h]@1

  v23 = a2;
  v22 = this;
  v17 = -2i64;
  v12 = operator new(0x100ui64);
  if ( v12 )
  {
    std::basic_ifstream<char,std::char_traits<char>>::basic_ifstream<char,std::char_traits<char>>(v12, 1i64);
    v12[20] = &std::basic_ifstream<char,std::char_traits<char>>::`local vftable';
    v18 = v12;
  }
  else
  {
    v18 = 0i64;
  }
  v11 = v18;
  CryptoPP::member_ptr<std::basic_ifstream<char,std::char_traits<char>>>::reset(&v22->m_file, v18);
  v2 = CryptoPP::Name::InputFileName();
  if ( (unsigned __int8)CryptoPP::NameValuePairs::GetValue<char const *>(v23, v2, &v9) )
  {
    v4 = CryptoPP::Name::InputBinaryMode();
    if ( CryptoPP::NameValuePairs::GetValueWithDefault<bool>((__int64)v23, (__int64)v4, 1) )
      v19 = 32;
    else
      v19 = 0;
    v10 = v19;
    v20 = v19 | 1;
    LODWORD(v5) = CryptoPP::member_ptr<std::basic_ifstream<char,std::char_traits<char>>>::operator->(&v22->m_file);
    std::basic_ifstream<char,std::char_traits<char>>::open(v5, v9, (unsigned int)v20, 64i64);
    LODWORD(v6) = CryptoPP::member_ptr<std::basic_ifstream<char,std::char_traits<char>>>::operator*(&v22->m_file);
    v13 = v6;
    if ( (unsigned __int8)std::ios_base::operator!(*(_DWORD *)(*(_QWORD *)v6 + 4i64) + v6) )
    {
      memset(&v16, 0, sizeof(v16));
      std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
        &v15,
        v9,
        v16);
      CryptoPP::FileStore::OpenErr::OpenErr(&v14, &v15);
      CxxThrowException_0((__int64)&v14, (__int64)&TI4_AVOpenErr_FileStore_CryptoPP__);
    }
    LODWORD(v7) = CryptoPP::member_ptr<std::basic_ifstream<char,std::char_traits<char>>>::get(&v22->m_file);
    v22->m_stream = v7;
  }
  else
  {
    v22->m_stream = 0i64;
    v21 = &v22->m_stream;
    v8 = CryptoPP::Name::InputStreamPointer(v3);
    CryptoPP::NameValuePairs::GetValue<std::basic_istream<char,std::char_traits<char>> *>(v23, v8, v21);
  }
  v22->m_waiting = 0;
}
