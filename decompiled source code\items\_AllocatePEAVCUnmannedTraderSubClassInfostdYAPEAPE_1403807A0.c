/*
 * Function: ??$_Allocate@PEAVCUnmannedTraderSubClassInfo@@@std@@YAPEAPEAVCUnmannedTraderSubClassInfo@@_KPEAPEAV1@@Z
 * Address: 0x1403807A0
 */

CUnmannedTraderSubClassInfo **__fastcall std::_Allocate<CUnmannedTraderSubClassInfo *>(unsigned __int64 _Count, CUnmannedTraderSubClassInfo **__formal)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  std::bad_alloc v6; // [sp+20h] [bp-28h]@7
  unsigned __int64 v7; // [sp+50h] [bp+8h]@1

  v7 = _Count;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7 )
  {
    if ( 0xFFFFFFFFFFFFFFFFui64 / v7 < 8 )
    {
      std::bad_alloc::bad_alloc(&v6, 0i64);
      CxxThrowException_0(&v6, &TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v7 = 0i64;
  }
  return (CUnmannedTraderSubClassInfo **)operator new(8 * v7);
}
