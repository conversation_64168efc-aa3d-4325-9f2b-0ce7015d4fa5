#include "../Headers/ChangeMonster.h"
#include <algorithm>
#include <sstream>
#include <cstring>
#include <stdexcept>

namespace NexusProtection::World {

    // ChangeMonster implementation

    ChangeMonster::ChangeMonster() {
        Initialize();
    }

    ChangeMonster::ChangeMonster(const std::string& missionCode, const std::string& completeMsg)
        : m_missionDescriptionCode(missionCode)
        , m_completionMessage(completeMsg) {
        ValidateAndTruncate();
        m_isInitialized = true;
    }

    ChangeMonster::ChangeMonster(const ChangeMonster& other)
        : m_missionDescriptionCode(other.m_missionDescriptionCode)
        , m_completionMessage(other.m_completionMessage)
        , m_isInitialized(other.m_isInitialized) {
    }

    ChangeMonster& ChangeMonster::operator=(const ChangeMonster& other) {
        if (this != &other) {
            m_missionDescriptionCode = other.m_missionDescriptionCode;
            m_completionMessage = other.m_completionMessage;
            m_isInitialized = other.m_isInitialized;
        }
        return *this;
    }

    ChangeMonster::ChangeMonster(ChangeMonster&& other) noexcept
        : m_missionDescriptionCode(std::move(other.m_missionDescriptionCode))
        , m_completionMessage(std::move(other.m_completionMessage))
        , m_isInitialized(other.m_isInitialized) {
        other.m_isInitialized = false;
    }

    ChangeMonster& ChangeMonster::operator=(ChangeMonster&& other) noexcept {
        if (this != &other) {
            m_missionDescriptionCode = std::move(other.m_missionDescriptionCode);
            m_completionMessage = std::move(other.m_completionMessage);
            m_isInitialized = other.m_isInitialized;
            other.m_isInitialized = false;
        }
        return *this;
    }

    void ChangeMonster::Initialize() {
        m_missionDescriptionCode.clear();
        m_completionMessage.clear();
        m_isInitialized = true;
    }

    void ChangeMonster::Reset() {
        Initialize();
    }

    bool ChangeMonster::IsValid() const {
        return m_isInitialized && (HasMissionDescriptionCode() || HasCompletionMessage());
    }

    void ChangeMonster::SetMissionDescriptionCode(const std::string& code) {
        m_missionDescriptionCode = code;
        if (m_missionDescriptionCode.length() > MAX_MISSION_CODE_LENGTH) {
            m_missionDescriptionCode.resize(MAX_MISSION_CODE_LENGTH);
        }
    }

    void ChangeMonster::SetCompletionMessage(const std::string& message) {
        m_completionMessage = message;
        if (m_completionMessage.length() > MAX_COMPLETION_MESSAGE_LENGTH) {
            m_completionMessage.resize(MAX_COMPLETION_MESSAGE_LENGTH);
        }
    }

    void ChangeMonster::SetConfiguration(const std::string& missionCode, const std::string& completeMsg) {
        SetMissionDescriptionCode(missionCode);
        SetCompletionMessage(completeMsg);
    }

    void ChangeMonster::ClearConfiguration() {
        m_missionDescriptionCode.clear();
        m_completionMessage.clear();
    }

    std::string ChangeMonster::ToString() const {
        std::ostringstream oss;
        oss << "ChangeMonster{";
        oss << "MissionCode: \"" << m_missionDescriptionCode << "\", ";
        oss << "CompleteMsg: \"" << m_completionMessage << "\", ";
        oss << "Initialized: " << (m_isInitialized ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    size_t ChangeMonster::GetMemoryUsage() const {
        return sizeof(ChangeMonster) + 
               m_missionDescriptionCode.capacity() + 
               m_completionMessage.capacity();
    }

    const char* ChangeMonster::GetMissionDescriptionCodeCStr() const {
        return m_missionDescriptionCode.c_str();
    }

    const char* ChangeMonster::GetCompletionMessageCStr() const {
        return m_completionMessage.c_str();
    }

    void ChangeMonster::SetMissionDescriptionCodeFromCStr(const char* code) {
        if (code) {
            SetMissionDescriptionCode(std::string(code));
        } else {
            m_missionDescriptionCode.clear();
        }
    }

    void ChangeMonster::SetCompletionMessageFromCStr(const char* message) {
        if (message) {
            SetCompletionMessage(std::string(message));
        } else {
            m_completionMessage.clear();
        }
    }

    bool ChangeMonster::ValidateMissionCode() const {
        return IsValidString(m_missionDescriptionCode, MAX_MISSION_CODE_LENGTH);
    }

    bool ChangeMonster::ValidateCompletionMessage() const {
        return IsValidString(m_completionMessage, MAX_COMPLETION_MESSAGE_LENGTH);
    }

    void ChangeMonster::ValidateAndTruncate() {
        if (m_missionDescriptionCode.length() > MAX_MISSION_CODE_LENGTH) {
            m_missionDescriptionCode.resize(MAX_MISSION_CODE_LENGTH);
        }
        if (m_completionMessage.length() > MAX_COMPLETION_MESSAGE_LENGTH) {
            m_completionMessage.resize(MAX_COMPLETION_MESSAGE_LENGTH);
        }
    }

    bool ChangeMonster::IsValidString(const std::string& str, size_t maxLength) const {
        return str.length() <= maxLength;
    }

    // ChangeMonsterFactory implementation

    std::unique_ptr<ChangeMonster> ChangeMonsterFactory::CreateChangeMonster() {
        return std::make_unique<ChangeMonster>();
    }

    std::unique_ptr<ChangeMonster> ChangeMonsterFactory::CreateChangeMonster(const std::string& missionCode) {
        return std::make_unique<ChangeMonster>(missionCode);
    }

    std::unique_ptr<ChangeMonster> ChangeMonsterFactory::CreateChangeMonster(const std::string& missionCode, 
                                                                            const std::string& completeMsg) {
        return std::make_unique<ChangeMonster>(missionCode, completeMsg);
    }

    std::vector<std::unique_ptr<ChangeMonster>> ChangeMonsterFactory::CreateChangeMonsters(
        const std::vector<std::pair<std::string, std::string>>& configurations) {
        std::vector<std::unique_ptr<ChangeMonster>> result;
        result.reserve(configurations.size());
        
        for (const auto& config : configurations) {
            result.push_back(CreateChangeMonster(config.first, config.second));
        }
        
        return result;
    }

    // ChangeMonsterUtils implementation

    namespace ChangeMonsterUtils {

        bool ValidateChangeMonster(const ChangeMonster& changeMonster) {
            return changeMonster.IsValid() && 
                   changeMonster.ValidateMissionCode() && 
                   changeMonster.ValidateCompletionMessage();
        }

        bool ValidateMissionCode(const std::string& code) {
            return code.length() <= ChangeMonster::MAX_MISSION_CODE_LENGTH;
        }

        bool ValidateCompletionMessage(const std::string& message) {
            return message.length() <= ChangeMonster::MAX_COMPLETION_MESSAGE_LENGTH;
        }

        std::string SanitizeMissionCode(const std::string& code) {
            std::string sanitized = code;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > ChangeMonster::MAX_MISSION_CODE_LENGTH) {
                sanitized.resize(ChangeMonster::MAX_MISSION_CODE_LENGTH);
            }
            return sanitized;
        }

        std::string SanitizeCompletionMessage(const std::string& message) {
            std::string sanitized = message;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > ChangeMonster::MAX_COMPLETION_MESSAGE_LENGTH) {
                sanitized.resize(ChangeMonster::MAX_COMPLETION_MESSAGE_LENGTH);
            }
            return sanitized;
        }

        size_t CalculateMemoryFootprint(const ChangeMonster& changeMonster) {
            return changeMonster.GetMemoryUsage();
        }

        std::string ChangeMonsterToJson(const ChangeMonster& changeMonster) {
            std::ostringstream oss;
            oss << "{";
            oss << "\"missionCode\": \"" << changeMonster.GetMissionDescriptionCode() << "\",";
            oss << "\"completionMessage\": \"" << changeMonster.GetCompletionMessage() << "\"";
            oss << "}";
            return oss.str();
        }

        std::unique_ptr<ChangeMonster> ChangeMonsterFromJson(const std::string& json) {
            // Simple JSON parsing - in a real implementation, use a proper JSON library
            auto changeMonster = std::make_unique<ChangeMonster>();
            
            // This is a simplified implementation
            // In production, use a proper JSON parser like nlohmann/json
            
            return changeMonster;
        }

    } // namespace ChangeMonsterUtils

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {

    void __change_monster_Constructor(_change_monster* this_ptr) {
        if (this_ptr) {
            // Initialize pointers to null (matching original constructor behavior)
            this_ptr->pszIfMissionDescirptCode = nullptr;
            this_ptr->pszifCompleteMsg = nullptr;
        }
    }

    void __change_monster_Destructor(_change_monster* this_ptr) {
        if (this_ptr) {
            // Clean up allocated memory (matching original destructor behavior)
            if (this_ptr->pszIfMissionDescirptCode) {
                delete[] this_ptr->pszIfMissionDescirptCode;
                this_ptr->pszIfMissionDescirptCode = nullptr;
            }
            if (this_ptr->pszifCompleteMsg) {
                delete[] this_ptr->pszifCompleteMsg;
                this_ptr->pszifCompleteMsg = nullptr;
            }
        }
    }

    void __change_monster_SetMissionCode(_change_monster* this_ptr, const char* code) {
        if (!this_ptr) {
            return;
        }

        // Clean up existing memory
        if (this_ptr->pszIfMissionDescirptCode) {
            delete[] this_ptr->pszIfMissionDescirptCode;
            this_ptr->pszIfMissionDescirptCode = nullptr;
        }

        // Allocate and copy new string
        if (code) {
            size_t len = std::strlen(code) + 1;
            this_ptr->pszIfMissionDescirptCode = new char[len];
            std::strcpy(this_ptr->pszIfMissionDescirptCode, code);
        }
    }

    void __change_monster_SetCompleteMsg(_change_monster* this_ptr, const char* message) {
        if (!this_ptr) {
            return;
        }

        // Clean up existing memory
        if (this_ptr->pszifCompleteMsg) {
            delete[] this_ptr->pszifCompleteMsg;
            this_ptr->pszifCompleteMsg = nullptr;
        }

        // Allocate and copy new string
        if (message) {
            size_t len = std::strlen(message) + 1;
            this_ptr->pszifCompleteMsg = new char[len];
            std::strcpy(this_ptr->pszifCompleteMsg, message);
        }
    }

    const char* __change_monster_GetMissionCode(_change_monster* this_ptr) {
        return (this_ptr && this_ptr->pszIfMissionDescirptCode) ? this_ptr->pszIfMissionDescirptCode : "";
    }

    const char* __change_monster_GetCompleteMsg(_change_monster* this_ptr) {
        return (this_ptr && this_ptr->pszifCompleteMsg) ? this_ptr->pszifCompleteMsg : "";
    }

} // extern "C"

// Global legacy compatibility
NexusProtection::World::ChangeMonster* g_pChangeMonster = nullptr;
