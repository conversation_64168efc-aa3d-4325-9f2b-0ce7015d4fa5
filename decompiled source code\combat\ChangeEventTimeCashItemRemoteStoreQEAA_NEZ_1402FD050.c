/*
 * Function: ?ChangeEventTime@CashItemRemoteStore@@QEAA_NE@Z
 * Address: 0x1402FD050
 */

char __fastcall CashItemRemoteStore::ChangeEventTime(CashItemRemoteStore *this, char byEventType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  int Dst; // [sp+28h] [bp-40h]@6
  int v7; // [sp+2Ch] [bp-3Ch]@6
  int v8; // [sp+30h] [bp-38h]@6
  int v9; // [sp+34h] [bp-34h]@6
  int v10; // [sp+38h] [bp-30h]@6
  int v11; // [sp+3Ch] [bp-2Ch]@6
  int v12; // [sp+48h] [bp-20h]@6
  CashItemRemoteStore *v13; // [sp+70h] [bp+8h]@1
  char v14; // [sp+78h] [bp+10h]@1

  v14 = byEventType;
  v13 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v13->m_cash_event[(unsigned __int8)byEventType].m_ini.m_bRepeat )
  {
    memset_0(&Dst, 0, 0x24ui64);
    v11 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_wYear[0] - 1900;
    v10 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMonth[0] - 1;
    v9 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byDay[0]
       + v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byRepeatDay;
    v8 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byHour[0];
    v7 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMinute[0];
    Dst = 0;
    v12 = -1;
    v13->m_cash_event[(unsigned __int8)v14].m_ini.m_EventTime[0] = _mktime32((struct tm *)&Dst);
    if ( v13->m_cash_event[(unsigned __int8)v14].m_ini.m_EventTime[0] == -1 )
    {
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_bRepeat = 0;
      CLogFile::Write(
        &v13->m_cash_event[(unsigned __int8)v14].m_event_log,
        "ChangeEventTime() : Fail When Calculate Event Begin Time");
      result = 0;
    }
    else
    {
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_wYear[0] = v11 + 1900;
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMonth[0] = v10 + 1;
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byDay[0] = v9;
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byHour[0] = v8;
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMinute[0] = v7;
      memset_0(&Dst, 0, 0x24ui64);
      v11 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_wYear[1] - 1900;
      v10 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMonth[1] - 1;
      v9 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byDay[1]
         + v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byRepeatDay;
      v8 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byHour[1];
      v7 = v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMinute[1];
      Dst = 0;
      v12 = -1;
      v13->m_cash_event[(unsigned __int8)v14].m_ini.m_EventTime[1] = _mktime32((struct tm *)&Dst);
      if ( v13->m_cash_event[(unsigned __int8)v14].m_ini.m_EventTime[1] == -1 )
      {
        v13->m_cash_event[(unsigned __int8)v14].m_ini.m_bRepeat = 0;
        CLogFile::Write(
          &v13->m_cash_event[(unsigned __int8)v14].m_event_log,
          "ChangeEventTime() : Fail When Calculate Event End Time");
        result = 0;
      }
      else
      {
        v13->m_cash_event[(unsigned __int8)v14].m_ini.m_wYear[1] = v11 + 1900;
        v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMonth[1] = v10 + 1;
        v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byDay[1] = v9;
        v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byHour[1] = v8;
        v13->m_cash_event[(unsigned __int8)v14].m_ini.m_byMinute[1] = v7;
        result = 1;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
