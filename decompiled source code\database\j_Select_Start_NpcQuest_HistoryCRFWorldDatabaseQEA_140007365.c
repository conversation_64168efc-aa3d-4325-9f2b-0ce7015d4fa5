/*
 * Function: j_?Select_Start_NpcQuest_History@CRFWorldDatabase@@QEAAEKPEAU_worlddb_start_npc_quest_complete_history@@K@Z
 * Address: 0x140007365
 */

char __fastcall CRFWorldDatabase::Select_Start_NpcQuest_History(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_start_npc_quest_complete_history *pNpcQHis, unsigned int dwCount)
{
  return CRFWorldDatabase::Select_Start_NpcQuest_History(this, dwSerial, pNpcQHis, dwCount);
}
