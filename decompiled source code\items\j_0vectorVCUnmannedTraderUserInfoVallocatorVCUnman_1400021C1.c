/*
 * Function: j_??0?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x1400021C1
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(this);
}
