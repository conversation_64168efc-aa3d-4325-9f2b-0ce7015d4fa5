/*
 * Function: ?dtor$0@?0??AddRunnableContext@ScheduleGroupSegmentBase@details@Concurrency@@IEAAXPEAVInternalContextBase@23@Vlocation@3@@Z@4HA_1
 * Address: 0x1405A1830
 */

int __fastcall `Concurrency::details::ScheduleGroupSegmentBase::AddRunnableContext'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 192);
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
}
