/*
 * Function: ?SkillRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C21F0
 */

char __fastcall CNetworkEX::SkillRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  int v7; // eax@10
  char *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-48h]@1
  char *v10; // [sp+20h] [bp-28h]@4
  CPlayer *v11; // [sp+28h] [bp-20h]@4
  int v12; // [sp+30h] [bp-18h]@10
  CNetworkEX *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)v10[1], *((_WORD *)v10 + 1)) )
  {
    v12 = (unsigned __int8)*v10;
    v7 = CRecordData::GetRecordNum(&stru_1799C8410);
    if ( v12 < v7 )
    {
      CPlayer::pc_SkillRequest(v11, *v10, (_CHRID *)(v10 + 1), (unsigned __int16 *)(v10 + 9));
      result = 1;
    }
    else
    {
      v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v13->m_LogFile,
        "odd.. %s: ForceRequest()..  if(pRecv->bySkillIndex >= g_Main.m_tblEffectData[effect_code_skill].GetRecordNum())",
        v8);
      result = 0;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v13->m_LogFile,
      "odd.. %s: ForceRequest()..  if(!g_Main.GetObject(obj_kind_char, pRecv->idDst.byID, pRecv->idDst.wIndex))",
      v6);
    result = 0;
  }
  return result;
}
