/*
 * Function: j_?Add@CGuildBattleController@@QEAAEPEAVCGuild@@0KKEK@Z
 * Address: 0x1400092CD
 */

char __fastcall CGuildBattleController::Add(CGuildBattleController *this, CGuild *pSrcGuild, CGuild *pDestGuild, unsigned int dwStartTime, unsigned int dwElapseTimeCnt, char byNum<PERSON>, unsigned int dwMapInx)
{
  return CGuildBattleController::Add(this, pSrcGuild, pDestGuild, dwStartTime, dwElapseTimeCnt, byNumber, dwMapInx);
}
