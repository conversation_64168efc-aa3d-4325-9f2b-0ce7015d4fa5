/*
 * Function: ?Update_Set_Limit_Run@CRFWorldDatabase@@QEAA_NPEAEH@Z
 * Address: 0x1404C8940
 */

bool __fastcall CRFWorldDatabase::Update_Set_Limit_Run(CRFWorldDatabase *this, char *pData, int iSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-468h]@1
  char _Dest[1024]; // [sp+40h] [bp-428h]@4
  unsigned __int64 v8; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+470h] [bp+8h]@1
  char *buf; // [sp+478h] [bp+10h]@1
  int size; // [sp+480h] [bp+18h]@1

  size = iSize;
  buf = pData;
  v9 = this;
  v3 = &v6;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x3FFui64);
  sprintf_s<1024>((char (*)[1024])_Dest, "update [dbo].[tbl_sf_delay] set [effect] = ? where [aserial] = 0");
  return CRFNewDatabase::ExecUpdateBinaryQuery((CRFNewDatabase *)&v9->vfptr, _Dest, buf, size, 1);
}
