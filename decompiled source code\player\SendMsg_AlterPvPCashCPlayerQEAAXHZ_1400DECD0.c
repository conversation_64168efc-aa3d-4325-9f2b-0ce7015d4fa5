/*
 * Function: ?SendMsg_AlterPvPCash@CPlayer@@QEAAXH@Z
 * Address: 0x1400DECD0
 */

void __usercall CPlayer::SendMsg_AlterPvPCash(CPlayer *this@<rcx>, int nIOCode@<edx>, __int64 a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg[8]; // [sp+38h] [bp-50h]@4
  char v7; // [sp+40h] [bp-48h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v9; // [sp+65h] [bp-23h]@4
  CPlayer *v10; // [sp+90h] [bp+8h]@1
  int v11; // [sp+98h] [bp+10h]@1

  v11 = nIOCode;
  v10 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPvpOrderView::GetPvpCash(&v10->m_kPvpOrderView);
  *(_QWORD *)szMsg = a3;
  v7 = v11;
  pbyType = 11;
  v9 = 11;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 9u);
}
