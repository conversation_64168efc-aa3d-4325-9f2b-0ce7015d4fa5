/*
 * Function: ?<PERSON><PERSON><PERSON>@CGuildBattleStateList@GUILD_BATTLE@@IEAAHXZ
 * Address: 0x1403DF4D0
 */

signed __int64 __fastcall GUILD_BATTLE::CGuildBattleStateList::CheckLoop(GUILD_BATTLE::CGuildBattleStateList *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleStateList::GBS_LOOP_TYPE v4; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleStateList *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (int *)&v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = v5->m_eLoopType;
  if ( v4 )
  {
    if ( v4 == 1 )
    {
      if ( v5->STATE_MAX == v5->m_iState )
        return 1i64;
      if ( v5->STATE_MAX < v5->m_iState )
        v5->m_iState = 0;
    }
    else if ( v4 == 2 )
    {
      if ( v5->STATE_MAX == v5->m_iState )
        return 1i64;
      if ( v5->STATE_MAX < v5->m_iState )
      {
        v5->m_iState = 0;
        if ( v5->m_uiLoopCnt <= ++v5->m_uiCurLoopCnt )
          return 0i64;
      }
    }
  }
  else
  {
    if ( v5->STATE_MAX == v5->m_iState )
      return 1i64;
    if ( v5->STATE_MAX < v5->m_iState )
    {
      v5->m_iState = v5->STATE_MAX;
      return 0i64;
    }
  }
  return 2i64;
}
