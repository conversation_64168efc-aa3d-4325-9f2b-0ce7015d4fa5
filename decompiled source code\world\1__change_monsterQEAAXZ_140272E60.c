/*
 * Function: ??1__change_monster@@QEAA@XZ
 * Address: 0x140272E60
 */

void __fastcall __change_monster::~__change_monster(__change_monster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  void *v5; // [sp+28h] [bp-10h]@7
  __change_monster *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->pszIfMissionDescirptCode )
  {
    v4 = v6->pszIfMissionDescirptCode;
    operator delete[](v4);
  }
  if ( v6->pszifCompleteMsg )
  {
    v5 = v6->pszifCompleteMsg;
    operator delete[](v5);
  }
}
