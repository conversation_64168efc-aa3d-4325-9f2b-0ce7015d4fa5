/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@stdext@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitInfo@@@std@@@Z
 * Address: 0x140013796
 */

CMoveMapLimitInfo **__fastcall stdext::unchecked_uninitialized_copy<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest, std::allocator<CMoveMapLimitInfo *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
