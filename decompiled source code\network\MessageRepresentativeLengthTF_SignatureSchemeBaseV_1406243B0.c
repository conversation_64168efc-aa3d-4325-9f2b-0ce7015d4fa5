/*
 * Function: ?MessageRepresentativeLength@?$TF_SignatureSchemeBase@VPK_Verifier@CryptoPP@@V?$TF_Base@VTrapdoorFunction@CryptoPP@@VPK_SignatureMessageEncodingMethod@2@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x1406243B0
 */

unsigned __int64 __fastcall CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
