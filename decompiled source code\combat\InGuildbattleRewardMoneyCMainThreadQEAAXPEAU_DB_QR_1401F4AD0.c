/*
 * Function: ?InGuildbattle<PERSON><PERSON>ard<PERSON><PERSON>@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F4AD0
 */

void __fastcall CMainThread::InGuildbattleRewardMoney(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  long double v4; // xmm0_8@6
  long double v5; // xmm1_8@6
  __int64 v6; // [sp+0h] [bp-68h]@1
  long double v7; // [sp+20h] [bp-48h]@6
  long double v8; // [sp+28h] [bp-40h]@6
  long double v9; // [sp+30h] [bp-38h]@6
  char *pbyDate; // [sp+38h] [bp-30h]@6
  bool bInPut; // [sp+40h] [bp-28h]@6
  char *v12; // [sp+50h] [bp-18h]@4
  CGuild *v13; // [sp+58h] [bp-10h]@4

  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = pData->m_sData;
  v13 = &g_Guild[*(_DWORD *)&pData->m_sData[0]];
  if ( v13->m_dwSerial == *(_DWORD *)&pData->m_sData[4] )
  {
    v13->m_bIOWait = 0;
    if ( !pData->m_byResult )
    {
      v13->m_byMoneyOutputKind = 5;
      v4 = (double)*((signed int *)v12 + 2);
      v5 = (double)*((signed int *)v12 + 3);
      bInPut = 1;
      pbyDate = v12 + 16;
      v9 = *((double *)v12 + 3);
      v8 = *((double *)v12 + 4);
      v7 = v4;
      CGuild::IOMoney(v13, "Scramble Reward", *((_DWORD *)v12 + 1), v5, v4, v8, v9, v12 + 16, 1);
    }
  }
}
