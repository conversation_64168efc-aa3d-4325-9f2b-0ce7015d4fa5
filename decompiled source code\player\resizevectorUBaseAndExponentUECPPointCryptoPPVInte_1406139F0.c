/*
 * Function: ?resize@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAX_KU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1406139F0
 */

void __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::resize(__int64 a1, unsigned __int64 a2, __int64 a3)
{
  char v3; // [sp+20h] [bp-D8h]@2
  char *v4; // [sp+38h] [bp-C0h]@2
  char v5; // [sp+40h] [bp-B8h]@4
  char *v6; // [sp+58h] [bp-A0h]@4
  char v7; // [sp+60h] [bp-98h]@4
  char *v8; // [sp+78h] [bp-80h]@4
  char v9; // [sp+80h] [bp-78h]@4
  char v10; // [sp+98h] [bp-60h]@4
  __int64 v11; // [sp+B0h] [bp-48h]@1
  unsigned __int64 v12; // [sp+B8h] [bp-40h]@2
  __int64 v13; // [sp+C0h] [bp-38h]@2
  __int64 v14; // [sp+C8h] [bp-30h]@4
  __int64 v15; // [sp+D0h] [bp-28h]@4
  __int64 v16; // [sp+D8h] [bp-20h]@4
  __int64 v17; // [sp+E0h] [bp-18h]@4
  __int64 v18; // [sp+E8h] [bp-10h]@4
  __int64 v19; // [sp+100h] [bp+8h]@1
  unsigned __int64 v20; // [sp+108h] [bp+10h]@1
  __int64 v21; // [sp+110h] [bp+18h]@1

  v21 = a3;
  v20 = a2;
  v19 = a1;
  v11 = -2i64;
  if ( std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(a1) >= a2 )
  {
    if ( v20 < std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v19) )
    {
      v6 = &v5;
      v8 = &v7;
      v14 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::end(
              v19,
              (__int64)&v5);
      v15 = v14;
      v16 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::begin(
              v19,
              (__int64)&v9);
      v17 = v16;
      v18 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
              v16,
              (__int64)v8,
              v20);
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::erase(
        v19,
        &v10,
        v18,
        v15);
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    }
  }
  else
  {
    v4 = &v3;
    v12 = v20
        - std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v19);
    v13 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::end(
            v19,
            (__int64)v4);
    std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Insert_n(
      v19,
      v13,
      v12,
      v21);
  }
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(v21);
}
