/*
 * Function: ?pc_DarkHoleEnterRequest@CPlayer@@QEAAXGK@Z
 * Address: 0x140098780
 */

void __fastcall CPlayer::pc_DarkHoleEnterRequest(CPlayer *this, unsigned __int16 wHoleIndex, unsigned int dwHoleSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@24
  int v6; // eax@25
  char *v7; // rax@29
  char *v8; // rax@30
  __int64 v9; // [sp+0h] [bp-D8h]@1
  float *pfStartPos; // [sp+20h] [bp-B8h]@29
  char v11; // [sp+30h] [bp-A8h]@4
  _dh_quest_setup *v12; // [sp+38h] [bp-A0h]@4
  CDarkHole *v13; // [sp+40h] [bp-98h]@4
  _ENTER_DUNGEON_NEW_POS pNewPos; // [sp+58h] [bp-80h]@21
  CMapData *pOldMap; // [sp+78h] [bp-60h]@28
  float fOldPos; // [sp+88h] [bp-50h]@28
  float v17; // [sp+8Ch] [bp-4Ch]@28
  float v18; // [sp+90h] [bp-48h]@28
  unsigned __int16 v19; // [sp+A4h] [bp-34h]@28
  CMapData *pIntoMap; // [sp+A8h] [bp-30h]@28
  float *v21; // [sp+B0h] [bp-28h]@29
  char *v22; // [sp+B8h] [bp-20h]@29
  char *v23; // [sp+C0h] [bp-18h]@30
  CPlayer *v24; // [sp+E0h] [bp+8h]@1
  unsigned int dwHoleSeriala; // [sp+F0h] [bp+18h]@1

  dwHoleSeriala = dwHoleSerial;
  v24 = this;
  v3 = &v9;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  v12 = 0i64;
  v13 = &g_DarkHole[(unsigned __int64)wHoleIndex];
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v24->m_id.wIndex) == 99 )
  {
    v11 = 28;
  }
  else if ( v24->m_bInGuildBattle )
  {
    v11 = 20;
  }
  else if ( v13->m_bLive && v13->m_dwObjSerial == dwHoleSeriala && v13->m_pChannel )
  {
    if ( v24->m_pCurMap->m_pMapSet->m_nMapType )
    {
      v11 = 11;
    }
    else if ( CGameObject::GetCurSecNum((CGameObject *)&v24->vfptr) == -1 || v24->m_bMapLoading )
    {
      v11 = 8;
    }
    else if ( CDarkHole::IsNewEnterAbleNum(v13) )
    {
      if ( CDarkHole::IsNewEnterAblePlayer(v13, v24) )
      {
        if ( CDarkHole::GetEnterNewPos(v13, &pNewPos) )
        {
          v12 = v13->m_pChannel->m_pQuestSetup;
          if ( !v12 )
            return;
          v5 = ((int (__fastcall *)(CPlayer *))v24->vfptr->GetLevel)(v24);
          if ( v5 < v12->nLimitLvMin
            || (v6 = ((int (__fastcall *)(CPlayer *))v24->vfptr->GetLevel)(v24), v6 > v12->nLimitLvMax) )
          {
            v11 = 19;
          }
        }
        else
        {
          v11 = 7;
        }
      }
      else
      {
        v11 = 6;
      }
    }
    else
    {
      v11 = 18;
    }
  }
  else
  {
    v11 = 5;
  }
  if ( !v11 )
  {
    pOldMap = v24->m_pCurMap;
    fOldPos = v24->m_fCurPos[0];
    v17 = v24->m_fCurPos[1];
    v18 = v24->m_fCurPos[2];
    v19 = v24->m_wMapLayerIndex;
    pIntoMap = CMapOperation::GetMap(&g_MapOper, (unsigned __int8)pNewPos.byMapCode);
    CPlayer::OutOfMap(v24, pIntoMap, pNewPos.wLayerIndex, 5, pNewPos.fPos);
    CDarkHole::EnterPlayer(v13, v24, pOldMap, v19, &fOldPos, 0);
    if ( v13->m_pRecordSet )
    {
      v21 = (float *)v13->m_pRecordSet[2].m_strCode;
      v22 = v13->m_aszOpenerName;
      v7 = CPlayerDB::GetCharNameA(&v24->m_Param);
      pfStartPos = v21;
      CLogFile::Write(&stru_1799C8FE8, "ENTER: %s, (OPENER:%s, DARKHOLE:%s)", v7, v22);
    }
    else
    {
      v23 = v13->m_aszOpenerName;
      v8 = CPlayerDB::GetCharNameA(&v24->m_Param);
      pfStartPos = (float *)"InvalidDungenName";
      CLogFile::Write(&stru_1799C8FE8, "ENTER: %s, (OPENER:%s, DARKHOLE:%s)", v8, v23);
    }
  }
  CPlayer::SendMsg_EnterDarkHole(v24, v11, dwHoleSeriala);
}
