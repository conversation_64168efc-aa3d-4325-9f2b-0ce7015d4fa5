/*
 * Function: ?Update_Player_TimeLimit_Info@CRFWorldDatabase@@QEAA_NKKE@Z
 * Address: 0x1404C86F0
 */

bool __fastcall CRFWorldDatabase::Update_Player_TimeLimit_Info(CRFWorldDatabase *this, unsigned int dwAccSerial, unsigned int dwFatigue, char wStatus)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-468h]@1
  unsigned int v8; // [sp+20h] [bp-448h]@4
  int v9; // [sp+28h] [bp-440h]@4
  char DstBuf; // [sp+40h] [bp-428h]@4
  char v11; // [sp+41h] [bp-427h]@4
  unsigned __int64 v12; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+470h] [bp+8h]@1

  v13 = this;
  v4 = &v7;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  DstBuf = 0;
  memset(&v11, 0, 0x3FFui64);
  v9 = (unsigned __int8)wStatus;
  v8 = dwFatigue;
  sprintf_s(&DstBuf, 0x400ui64, "{ CALL pUpdate_TimeLimit_Info( %d, %d, %d) }", dwAccSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &DstBuf, 1);
}
