/*
 * Function: ??$?0U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@?$allocator@PEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@@std@@QEAA@AEBV?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@1@@Z
 * Address: 0x14022ACF0
 */

void __fastcall std::allocator<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Node *>::allocator<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Node *>(std::allocator<std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node *> *this, std::allocator<std::pair<int const ,CNationSettingFactory *> > *__formal)
{
  ;
}
