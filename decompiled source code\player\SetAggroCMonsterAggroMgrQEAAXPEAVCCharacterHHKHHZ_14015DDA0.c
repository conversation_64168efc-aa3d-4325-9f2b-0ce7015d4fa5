/*
 * Function: ?SetAggro@CMonsterAggroMgr@@QEAAXPEAVCCharacter@@HHKHH@Z
 * Address: 0x14015DDA0
 */

void __fastcall CMonsterAggroMgr::SetAggro(CMonsterAggroMgr *this, CCharacter *pCharacter, int nDam, int nAttackType, unsigned int dwAttackSerial, int bOtherPlayerSupport, int bTempSkill)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-58h]@1
  CAggroNode *v10; // [sp+40h] [bp-18h]@4
  float v11; // [sp+48h] [bp-10h]@8
  int v12; // [sp+4Ch] [bp-Ch]@13
  CMonsterAggroMgr *v13; // [sp+60h] [bp+8h]@1
  CCharacter *pCharactera; // [sp+68h] [bp+10h]@1
  int nDama; // [sp+70h] [bp+18h]@1
  int nAttackTypea; // [sp+78h] [bp+20h]@1

  nAttackTypea = nAttackType;
  nDama = nDam;
  pCharactera = pCharacter;
  v13 = this;
  v7 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v10 = 0i64;
  if ( (unsigned __int8)((int (__fastcall *)(CMonster *))v13->m_pMonster->vfptr->IsBeDamagedAble)(v13->m_pMonster)
    && pCharactera
    && pCharactera->m_bLive
    && !pCharactera->m_bCorpse )
  {
    v10 = CMonsterAggroMgr::_SearchAggroNode(v13, pCharactera);
    v11 = 0.0;
    if ( !pCharactera->m_ObjID.m_byKind )
    {
      _effect_parameter::GetEff_Have(&pCharactera->m_EP, 28);
      v11 = 0.0;
    }
    if ( v10 )
    {
      CAggroNode::SetAggro(v10, nDama, v11, nAttackTypea, dwAttackSerial, bOtherPlayerSupport, 0, 0);
      CMonsterAggroMgr::SendChangeAggroData(v13);
    }
    else
    {
      v10 = CMonsterAggroMgr::_GetBlinkNode(v13);
      if ( v10 )
      {
        CAggroNode::Set(v10, pCharactera);
        v12 = v13->m_dwAggroCount == 0;
        CAggroNode::SetAggro(v10, nDama, v11, nAttackTypea, dwAttackSerial, bOtherPlayerSupport, v12, bTempSkill);
        CMonsterAggroMgr::SendChangeAggroData(v13);
        ++v13->m_dwAggroCount;
      }
    }
  }
}
