/*
 * Function: ??_E?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$00VDec@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Decryption@2@@CryptoPP@@W7EAAPEAXI@Z
 * Address: 0x14046A480
 */

void *__fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::`vector deleting destructor'(__int64 a1, unsigned int a2)
{
  return CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>::`vector deleting destructor'(
           (CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> *)(a1 - 8),
           a2);
}
