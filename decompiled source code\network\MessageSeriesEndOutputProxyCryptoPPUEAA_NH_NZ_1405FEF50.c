/*
 * Function: ?MessageSeriesEnd@OutputProxy@CryptoPP@@UEAA_NH_N@Z
 * Address: 0x1405FEF50
 */

char __fastcall CryptoPP::OutputProxy::MessageSeriesEnd(CryptoPP::OutputProxy *this, unsigned int a2, unsigned __int8 a3)
{
  __int64 v3; // rax@2
  char v5; // [sp+28h] [bp-10h]@2
  unsigned int v6; // [sp+48h] [bp+10h]@1
  unsigned __int8 v7; // [sp+50h] [bp+18h]@1

  v7 = a3;
  v6 = a2;
  if ( this->m_passSignal )
  {
    LODWORD(v3) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))this->m_owner->vfptr[20].Clone)(this->m_owner);
    v5 = (*(int (__fastcall **)(__int64, _QWORD, _QWORD))(*(_QWORD *)v3 + 96i64))(v3, v6, v7);
  }
  else
  {
    v5 = 0;
  }
  return v5;
}
