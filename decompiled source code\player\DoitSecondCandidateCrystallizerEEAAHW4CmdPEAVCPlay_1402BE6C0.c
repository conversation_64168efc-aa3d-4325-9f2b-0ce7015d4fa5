/*
 * Function: ?Doit@SecondCandidateCrystallizer@@EEAAHW4Cmd@@PEAVCPlayer@@PEAD@Z
 * Address: 0x1402BE6C0
 */

__int64 __fastcall SecondCandidateCrystallizer::Doit(SecondCandidateCrystallizer *this, Cmd eCmd, CPlayer *pOne, char *pdata)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  PatriarchElectProcessor *v6; // rax@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned int v9; // [sp+20h] [bp-18h]@4
  Cmd v10; // [sp+24h] [bp-14h]@4

  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  v10 = eCmd;
  if ( eCmd == 4 )
  {
    v6 = PatriarchElectProcessor::Instance();
    PatriarchElectProcessor::PushDQSCheckInvalidChar(v6);
  }
  else
  {
    v9 = 255;
  }
  return v9;
}
