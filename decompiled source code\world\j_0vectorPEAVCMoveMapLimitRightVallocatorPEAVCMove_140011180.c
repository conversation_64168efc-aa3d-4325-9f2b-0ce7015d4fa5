/*
 * Function: j_??0?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA@XZ
 * Address: 0x140011180
 */

void __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(this);
}
