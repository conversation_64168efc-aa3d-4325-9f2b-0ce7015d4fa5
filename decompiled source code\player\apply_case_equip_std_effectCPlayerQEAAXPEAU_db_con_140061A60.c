/*
 * Function: ?apply_case_equip_std_effect@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@_N@Z
 * Address: 0x140061A60
 */

void __fastcall CPlayer::apply_case_equip_std_effect(CPlayer *this, _STORAGE_LIST::_db_con *pItem, bool bEquip)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  _ITEM_EFFECT *v6; // [sp+20h] [bp-18h]@4
  int j; // [sp+28h] [bp-10h]@5
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  bool v9; // [sp+50h] [bp+18h]@1

  v9 = bEquip;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = CPlayer::_GetItemEffect(v8, pItem);
  if ( v6 )
  {
    for ( j = 0; j < 4; ++j )
      CPlayer::apply_normal_item_std_effect(v8, v6[j].nEffectCode, v6[j].fEffectValue, v9);
  }
}
