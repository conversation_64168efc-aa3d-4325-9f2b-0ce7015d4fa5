/**
 * @file CRFMonsterAIMgr.h
 * @brief RF Monster AI Manager for managing monster artificial intelligence states
 * @details Provides singleton management for monster AI state tables and processing
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#pragma once

#ifndef CRFMONSTERAIMGR_H
#define CRFMONSTERAIMGR_H

#include <cstdint>
#include <memory>
#include <vector>
#include <array>
#include <mutex>

namespace NexusProtection {
namespace World {

// Forward declarations
enum class MonsterAIState : uint8_t;
class CMonster;

/**
 * @brief Legacy template wrapper class for compatibility
 */
template<typename T>
class UsPoint {
public:
    UsPoint() : m_data(nullptr) {}
    explicit UsPoint(T* data) : m_data(data) {}
    ~UsPoint() = default;

    // Copy constructor and assignment
    UsPoint(const UsPoint& other) : m_data(other.m_data) {}
    UsPoint& operator=(const UsPoint& other) {
        if (this != &other) {
            m_data = other.m_data;
        }
        return *this;
    }

    // Move constructor and assignment
    UsPoint(UsPoint&& other) noexcept : m_data(other.m_data) {
        other.m_data = nullptr;
    }
    UsPoint& operator=(UsPoint&& other) noexcept {
        if (this != &other) {
            m_data = other.m_data;
            other.m_data = nullptr;
        }
        return *this;
    }

    T* Get() const { return m_data; }
    bool IsValid() const { return m_data != nullptr; }

private:
    T* m_data;
};

/**
 * @brief Legacy state table class for compatibility
 */
class UsStateTBL {
public:
    UsStateTBL() = default;
    ~UsStateTBL() = default;

    // Legacy compatibility method
    int SetHFSM(Us_HFSM* hfsm, CMonster* monster) {
        // Placeholder implementation for legacy compatibility
        return 1;
    }
};

// MonsterAIState is defined in CMonsterManagement.h

/**
 * @brief AI Priority levels for state transitions
 */
enum class AIPriority : uint32_t {
    Low = 0,            ///< Low priority state
    Normal = 1,         ///< Normal priority state
    High = 2,           ///< High priority state
    Critical = 3        ///< Critical priority state
};

/**
 * @brief AI State Table entry structure
 */
struct AIStateEntry {
    uint32_t stateId;           ///< Unique state identifier
    MonsterAIState state;       ///< AI state type
    AIPriority priority;        ///< State priority
    uint32_t duration;          ///< State duration in milliseconds
    uint32_t cooldown;          ///< Cooldown before state can be used again
    bool isActive;              ///< Whether this state is currently active
    std::string stateName;      ///< Human-readable state name
    std::string description;    ///< State description
    
    AIStateEntry() noexcept
        : stateId(0), state(MonsterAIState::Idle), priority(AIPriority::Normal)
        , duration(0), cooldown(0), isActive(false) {}
};

/**
 * @brief Statistics for AI manager monitoring
 */
struct AIManagerStatistics {
    std::size_t totalStates;        ///< Total number of AI states
    std::size_t activeStates;       ///< Number of currently active states
    std::size_t stateTransitions;   ///< Total number of state transitions
    uint64_t totalProcessingTime;   ///< Total processing time in milliseconds
    uint64_t averageProcessingTime; ///< Average processing time per update
    std::chrono::system_clock::time_point lastUpdateTime; ///< Last update timestamp
};

/**
 * @brief RF Monster AI Manager class (Singleton)
 * 
 * This class manages the artificial intelligence states and behaviors for monsters
 * in the RF (Rising Force) system. It provides:
 * - Singleton pattern for global AI management
 * - State table management for monster behaviors
 * - AI state transitions and processing
 * - Performance monitoring and statistics
 * 
 * The class is thread-safe and designed for high-performance monster AI processing.
 */
class CRFMonsterAIMgr {
public:
    /**
     * @brief Get the singleton instance
     * @return Pointer to the singleton instance
     */
    [[nodiscard]] static CRFMonsterAIMgr* Instance();
    
    /**
     * @brief Destroy the singleton instance
     * @details This method safely destroys the singleton instance and cleans up resources
     */
    static void Destroy();
    
    // Delete copy constructor and copy assignment operator
    CRFMonsterAIMgr(const CRFMonsterAIMgr&) = delete;
    CRFMonsterAIMgr& operator=(const CRFMonsterAIMgr&) = delete;
    
    /**
     * @brief Destructor
     */
    ~CRFMonsterAIMgr();

    // Core AI management operations
    
    /**
     * @brief Initialize the AI manager
     * @return true if initialization was successful, false otherwise
     */
    [[nodiscard]] bool Initialize();
    
    /**
     * @brief Shutdown the AI manager
     */
    void Shutdown();
    
    /**
     * @brief Update AI processing
     * @details This should be called periodically to process AI state updates
     */
    void Update();
    
    /**
     * @brief Get state table by index
     * @param nIndex Index of the state table (0-based)
     * @return UsPoint containing the state table, or empty if invalid index
     */
    [[nodiscard]] UsPoint<UsStateTBL> GetStateTBL(int32_t nIndex) const;
    
    /**
     * @brief Set state table at specific index
     * @param nIndex Index of the state table
     * @param stateTable The state table to set
     * @return true if successful, false otherwise
     */
    [[nodiscard]] bool SetStateTBL(int32_t nIndex, const UsPoint<UsStateTBL>& stateTable);
    
    // AI state management
    
    /**
     * @brief Add a new AI state entry
     * @param entry The AI state entry to add
     * @return true if the state was added successfully, false otherwise
     */
    [[nodiscard]] bool AddAIState(const AIStateEntry& entry);
    
    /**
     * @brief Remove an AI state by ID
     * @param stateId The ID of the state to remove
     * @return true if the state was removed, false if not found
     */
    [[nodiscard]] bool RemoveAIState(uint32_t stateId);
    
    /**
     * @brief Update an existing AI state
     * @param stateId The ID of the state to update
     * @param entry The new state data
     * @return true if the state was updated, false if not found
     */
    [[nodiscard]] bool UpdateAIState(uint32_t stateId, const AIStateEntry& entry);
    
    /**
     * @brief Find an AI state by ID
     * @param stateId The ID of the state to find
     * @return Pointer to the state entry, or nullptr if not found
     */
    [[nodiscard]] const AIStateEntry* FindAIState(uint32_t stateId) const;
    
    /**
     * @brief Get all AI states of a specific type
     * @param state The AI state type to filter by
     * @return Vector of pointers to matching state entries
     */
    [[nodiscard]] std::vector<const AIStateEntry*> GetStatesByType(MonsterAIState state) const;
    
    /**
     * @brief Get all active AI states
     * @return Vector of pointers to active state entries
     */
    [[nodiscard]] std::vector<const AIStateEntry*> GetActiveStates() const;
    
    // Status and monitoring
    
    /**
     * @brief Check if the AI manager is initialized
     * @return true if initialized, false otherwise
     */
    [[nodiscard]] bool IsInitialized() const noexcept;
    
    /**
     * @brief Get the number of state tables
     * @return Number of state tables
     */
    [[nodiscard]] std::size_t GetStateTableCount() const noexcept;
    
    /**
     * @brief Get the number of AI states
     * @return Total number of AI states
     */
    [[nodiscard]] std::size_t GetAIStateCount() const noexcept;
    
    /**
     * @brief Check if the AI manager is empty
     * @return true if no states exist, false otherwise
     */
    [[nodiscard]] bool IsEmpty() const noexcept;
    
    /**
     * @brief Get comprehensive statistics
     * @return Statistics structure with current data
     */
    [[nodiscard]] AIManagerStatistics GetStatistics() const;
    
    /**
     * @brief Clear all AI states
     */
    void ClearAIStates();
    
    /**
     * @brief Reset all state tables
     */
    void ResetStateTables();
    
    // Utility functions
    
    /**
     * @brief Validate an AI state entry
     * @param entry The state entry to validate
     * @return true if the entry is valid, false otherwise
     */
    [[nodiscard]] static bool ValidateAIState(const AIStateEntry& entry);
    
    /**
     * @brief Get the maximum number of state tables
     * @return Maximum state table count
     */
    [[nodiscard]] static constexpr std::size_t GetMaxStateTableCount() noexcept {
        return 8; // Based on the decompiled code showing 8 elements
    }
    
    /**
     * @brief Get memory usage of the AI manager
     * @return Memory usage in bytes
     */
    [[nodiscard]] std::size_t GetMemoryUsage() const;

private:
    /**
     * @brief Private constructor for singleton pattern
     */
    CRFMonsterAIMgr();
    
    // Private member variables
    static CRFMonsterAIMgr* ms_Instance;        ///< Singleton instance
    static std::mutex ms_InstanceMutex;         ///< Mutex for thread-safe singleton access
    
    std::array<UsPoint<UsStateTBL>, 8> m_stateTables;  ///< Array of state tables
    std::vector<AIStateEntry> m_aiStates;              ///< Collection of AI states
    bool m_isInitialized;                              ///< Initialization status
    
    mutable AIManagerStatistics m_statistics;          ///< Statistics tracking
    mutable std::mutex m_stateMutex;                   ///< Mutex for thread-safe state access
    
    // Private helper methods
    
    /**
     * @brief Initialize internal data structures
     */
    void InitializeInternal();
    
    /**
     * @brief Update statistics
     */
    void UpdateStatistics() const;
    
    /**
     * @brief Validate internal state
     * @return true if internal state is valid, false otherwise
     */
    [[nodiscard]] bool ValidateInternalState() const;
    
    /**
     * @brief Get maximum number of AI states allowed
     * @return Maximum AI state count
     */
    [[nodiscard]] static constexpr std::size_t GetMaxAIStateCount() noexcept {
        return 1000; // Reasonable limit for AI states
    }
};

} // namespace World
} // namespace NexusProtection

#endif // CRFMONSTERAIMGR_H
