/*
 * Function: ?SetLoadInfo@CUnmannedTraderUserInfo@@AEAA_NEKAEAU_TRADE_DB_BASE@@PEAVCLogFile@@@Z
 * Address: 0x140358590
 */

char __fastcall CUnmannedTraderUserInfo::SetLoadInfo(CUnmannedTraderUserInfo *this, char byType, unsigned int dwSerial, _TRADE_DB_BASE *kInfo, CLogFile *pkLogger)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CUnmannedTraderRegistItemInfo *v8; // rax@9
  CUnmannedTraderRegistItemInfo *v9; // rax@14
  char *v10; // rax@16
  CUnmannedTraderRegistItemInfo *v11; // rax@17
  __int64 v12; // [sp+0h] [bp-78h]@1
  _TRADE_DB_BASE *kInfoa; // [sp+20h] [bp-58h]@14
  CLogFile *v14; // [sp+28h] [bp-50h]@14
  unsigned __int16 wItemTableIndex[4]; // [sp+30h] [bp-48h]@15
  unsigned int v16; // [sp+38h] [bp-40h]@16
  int j; // [sp+40h] [bp-38h]@7
  unsigned int uiInx; // [sp+44h] [bp-34h]@10
  __int64 v19; // [sp+48h] [bp-30h]@14
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v20; // [sp+50h] [bp-28h]@14
  __int64 v21; // [sp+58h] [bp-20h]@16
  CPlayer *v22; // [sp+60h] [bp-18h]@16
  CUnmannedTraderUserInfo *v23; // [sp+80h] [bp+8h]@1
  char v24; // [sp+88h] [bp+10h]@1
  unsigned int dwOwnerSerial; // [sp+90h] [bp+18h]@1
  _TRADE_DB_BASE *v26; // [sp+98h] [bp+20h]@1

  v26 = kInfo;
  dwOwnerSerial = dwSerial;
  v24 = byType;
  v23 = this;
  v5 = &v12;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( CUnmannedTraderUserInfo::IsNull(v23) || (signed int)(unsigned __int8)v24 >= 2 )
  {
    result = 0;
  }
  else
  {
    CUnmannedTraderUserInfo::Clear(v23);
    for ( j = 0; j < 20; ++j )
    {
      v8 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v23->m_vecLoadItemInfo,
             j);
      CUnmannedTraderRegistItemInfo::SetState(v8, 0);
    }
    for ( uiInx = 0;
          (signed int)uiInx < 20 && !_TRADE_DB_BASE::_LIST::IsEmpty((_TRADE_DB_BASE::_LIST *)v26 + (signed int)uiInx);
          ++uiInx )
    {
      v19 = 65i64 * (signed int)uiInx;
      v20 = &v23->m_vecLoadItemInfo;
      v9 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v23->m_vecLoadItemInfo,
             (signed int)uiInx);
      v14 = pkLogger;
      kInfoa = v26;
      if ( !CUnmannedTraderRegistItemInfo::Set(
              v9,
              v23->m_wInx,
              v26->m_List[(unsigned __int64)v19 / 0x41].byInvenIndex,
              uiInx,
              v26,
              pkLogger) )
      {
        wItemTableIndex[0] = -1;
        LOBYTE(v14) = -1;
        LOWORD(kInfoa) = -1;
        CUnmannedTraderItemState::PushUpdateState(
          v24,
          v26->m_List[uiInx].dwRegistSerial,
          7,
          dwOwnerSerial,
          0xFFFFu,
          -1,
          0xFFFFu);
        if ( pkLogger )
        {
          v21 = 50856i64 * v23->m_wInx;
          v22 = &g_Player;
          v10 = CPlayerDB::GetCharNameA((CPlayerDB *)((char *)&g_Player.m_Param + 50856 * v23->m_wInx));
          v16 = v22[(unsigned __int64)v21 / 0xC6A8].m_id.dwSerial;
          *(_QWORD *)wItemTableIndex = v10;
          LODWORD(v14) = v26->m_List[uiInx].dwRegistSerial;
          LODWORD(kInfoa) = (unsigned __int8)v24;
          CLogFile::Write(
            pkLogger,
            "CUnmannedTraderUserInfo::SetLoadInfo( byType(%u), dwSerial(%u), _TRADE_DB_BASE & kInfo, CLogFile * pkLogger "
            ")\r\n"
            "\t\tCUnmannedTraderItemState::PushUpdateState( byType(%u)\r\n"
            "\t\t, kInfo.m_List[i].dwRegistSerial(%u)\r\n"
            "\t\t, CUnmannedTraderItemState::CANCELREGISTFORSERVERINTERNALERROR, dwSerial )\r\n"
            "\t\tName(%s) Serial(%u) Invalid DB Data!\r\n",
            (unsigned __int8)v24,
            dwOwnerSerial);
        }
        v11 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                &v23->m_vecLoadItemInfo,
                (signed int)uiInx);
        CUnmannedTraderRegistItemInfo::ClearToWaitState(v11);
      }
    }
    v23->m_dwUserSerial = dwOwnerSerial;
    CUnmannedTraderRequestLimiter::ClearRequset(&v23->m_kRequestState);
    result = 1;
  }
  return result;
}
