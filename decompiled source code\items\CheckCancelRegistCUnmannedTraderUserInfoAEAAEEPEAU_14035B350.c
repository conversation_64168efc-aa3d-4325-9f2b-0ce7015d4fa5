/*
 * Function: ?CheckCancelRegist@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_clear_item_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x14035B350
 */

char __fastcall CUnmannedTraderUserInfo::CheckCancelRegist(CUnmannedTraderUserInfo *this, char byType, _a_trade_clear_item_request_clzo *pRequest, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@5
  CUnmannedTraderRegistItemInfo *v7; // rax@16
  unsigned __int16 v8; // ax@16
  __int64 v9; // [sp+0h] [bp-A8h]@1
  CPlayer *v10; // [sp+20h] [bp-88h]@6
  _STORAGE_LIST::_db_con *v11; // [sp+28h] [bp-80h]@10
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-70h]@14
  bool v13; // [sp+54h] [bp-54h]@14
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v14; // [sp+58h] [bp-50h]@14
  char v15; // [sp+70h] [bp-38h]@15
  char v16; // [sp+71h] [bp-37h]@17
  char v17; // [sp+72h] [bp-36h]@18
  __int64 v18; // [sp+78h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v19; // [sp+80h] [bp-28h]@14
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+88h] [bp-20h]@14
  int v21; // [sp+90h] [bp-18h]@16
  CUnmannedTraderUserInfo *v22; // [sp+B0h] [bp+8h]@1
  _a_trade_clear_item_request_clzo *v23; // [sp+C0h] [bp+18h]@1

  v23 = pRequest;
  v22 = this;
  v4 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v18 = -2i64;
  if ( (signed int)v22->m_wInx < 2532 )
  {
    v10 = &g_Player + v22->m_wInx;
    if ( v10->m_dwObjSerial == v22->m_dwUserSerial )
    {
      if ( CUnmannedTraderRequestLimiter::IsEmpty(&v22->m_kRequestState) )
      {
        v11 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v10->m_Param.m_dbInven.m_nListNum, v23->wItemSerial);
        if ( v11 )
        {
          if ( v23->dwRegistSerial )
          {
            CUnmannedTraderUserInfo::Find(v22, &result, v23->dwRegistSerial);
            v19 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
                    &v22->m_vecRegistItemInfo,
                    &v14);
            _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v19;
            v13 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
                    (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
                    (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v19->_Mycont);
            std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v14);
            if ( v13 )
            {
              v15 = 14;
              std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
              v6 = v15;
            }
            else
            {
              v21 = v23->wItemSerial;
              v7 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
              v8 = CUnmannedTraderRegistItemInfo::GetItemSerial(v7);
              if ( v21 == v8 )
              {
                v17 = 0;
                std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
                v6 = v17;
              }
              else
              {
                v16 = 25;
                std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
                v6 = v16;
              }
            }
          }
          else
          {
            v6 = 70;
          }
        }
        else
        {
          v6 = 14;
        }
      }
      else
      {
        v6 = 95;
      }
    }
    else
    {
      v6 = 99;
    }
  }
  else
  {
    v6 = 99;
  }
  return v6;
}
