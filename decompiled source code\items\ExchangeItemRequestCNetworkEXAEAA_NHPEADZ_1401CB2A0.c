/*
 * Function: ?ExchangeItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB2A0
 */

char __fastcall CNetworkEX::ExchangeItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  int v6; // eax@8
  char *v7; // rax@9
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *v9; // [sp+20h] [bp-28h]@4
  CPlayer *v10; // [sp+28h] [bp-20h]@4
  int v11; // [sp+30h] [bp-18h]@8
  CNetworkEX *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = pBuf;
  v10 = &g_Player + n;
  if ( !v10->m_bOper || v10->m_pmTrd.bDTradeMode || v10->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    v11 = *(_WORD *)v9;
    v6 = CRecordData::GetRecordNum(&stru_1799C6928);
    if ( v11 < v6 )
    {
      CPlayer::pc_ExchangeItem(v10, *(_WORD *)v9, *((_WORD *)v9 + 1));
      result = 1;
    }
    else
    {
      v7 = CPlayerDB::GetCharNameA(&v10->m_Param);
      CLogFile::Write(
        &v12->m_LogFile,
        "odd.. %s: ExchangeItemRequest()..  if(pRecv->wManualIndex >= g_Main.m_tblItemExchangeData.GetRecordNum())",
        v7);
      result = 0;
    }
  }
  return result;
}
