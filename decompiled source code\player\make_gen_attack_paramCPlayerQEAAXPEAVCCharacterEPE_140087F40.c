/*
 * Function: ?make_gen_attack_param@CPlayer@@QEAAXPEAVCCharacter@@EPEAU_BulletItem_fld@@MPEAU_attack_param@@1M@Z
 * Address: 0x140087F40
 */

void __fastcall CPlayer::make_gen_attack_param(CPlayer *this, CCharacter *pDst, char by<PERSON>art, _BulletItem_fld *pBulletFld, float fAddBulletFc, _attack_param *pAP, _BulletItem_fld *pEffBtFld, float fAddEffBtFc)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  float v10; // xmm0_4@17
  float v11; // xmm0_4@17
  float v12; // xmm0_4@18
  float v13; // xmm0_4@18
  __int64 v14; // [sp+0h] [bp-48h]@1
  int v15; // [sp+20h] [bp-28h]@12
  float v16; // [sp+24h] [bp-24h]@17
  float v17; // [sp+28h] [bp-20h]@17
  float v18; // [sp+2Ch] [bp-1Ch]@18
  float v19; // [sp+30h] [bp-18h]@18
  CPlayer *pTarget; // [sp+50h] [bp+8h]@1
  CMonster *v21; // [sp+58h] [bp+10h]@1
  _BulletItem_fld *v22; // [sp+68h] [bp+20h]@1

  v22 = pBulletFld;
  v21 = (CMonster *)pDst;
  pTarget = this;
  v8 = &v14;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  pAP->pDst = pDst;
  if ( !pDst || pDst->m_ObjID.m_byID || pDst[25].m_SFCont[0][5].m_wszPlayerName[16] == 255 )
    pAP->nPart = (unsigned __int8)byPart;
  else
    pAP->nPart = pDst[25].m_SFCont[0][5].m_wszPlayerName[16];
  if ( pBulletFld )
    pAP->nTol = pBulletFld->m_nProperty;
  else
    pAP->nTol = pTarget->m_pmWpn.byAttTolType;
  pAP->nClass = pTarget->m_pmWpn.byWpClass;
  v15 = 1;
  if ( pTarget->m_pmWpn.byWpType == 7 )
    v15 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
  else
    v15 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 0, pTarget->m_pmWpn.byWpClass);
  if ( strncmp(pTarget->m_pmWpn.strEffBulletType, "-1", 2ui64) && pEffBtFld )
  {
    v16 = (float)pTarget->m_pmWpn.nGaMinAF;
    v10 = v16;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    pAP->nMinAFPlus = (signed int)ffloor((float)((float)((float)(v16 * v10) * fAddBulletFc) * fAddEffBtFc) + (float)(signed int)CPlayer::s_nAddMstFc[v15]);
    v17 = (float)pTarget->m_pmWpn.nGaMaxAF;
    v11 = v17;
    _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
    pAP->nMaxAFPlus = (signed int)ffloor((float)((float)((float)(v17 * v11) * fAddBulletFc) * fAddEffBtFc) + (float)(signed int)CPlayer::s_nAddMstFc[v15]);
  }
  v18 = (float)pTarget->m_pmWpn.nGaMinAF;
  v12 = v18;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  pAP->nMinAF = (signed int)ffloor((float)((float)(v18 * v12) * fAddBulletFc) + (float)(signed int)CPlayer::s_nAddMstFc[v15]);
  v19 = (float)pTarget->m_pmWpn.nGaMaxAF;
  v13 = v19;
  _effect_parameter::GetEff_Rate(&pTarget->m_EP, 32);
  pAP->nMaxAF = (signed int)ffloor((float)((float)(v19 * v13) * fAddBulletFc) + (float)(signed int)CPlayer::s_nAddMstFc[v15]);
  pAP->nMinSel = pTarget->m_pmWpn.byGaMinSel;
  pAP->nMaxSel = pTarget->m_pmWpn.byGaMaxSel;
  pAP->nExtentRange = 20;
  if ( v22 )
  {
    pAP->nAttactType = v22->m_nEffectGroup;
    pAP->nShotNum = 1;
  }
  if ( pEffBtFld )
    pAP->nEffShotNum = 1;
  if ( v21 )
    memcpy_0(pAP->fArea, v21->m_fCurPos, 0xCui64);
  pAP->bMatchless = pTarget->m_bCheat_Matchless;
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v21 && v21->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v21, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
}
