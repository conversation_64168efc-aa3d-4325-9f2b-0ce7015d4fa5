/*
 * Function: ?RequestGMCall@GMCallMgr@@QEAA_NPEAVCPlayer@@H@Z
 * Address: 0x1402AA6A0
 */

bool __fastcall GMCallMgr::RequestGMCall(GMCallMgr *this, CPlayer *pOne, int bCall)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  GMRequestData *pInst; // [sp+20h] [bp-18h]@6
  GMRequestData *v9; // [sp+28h] [bp-10h]@8
  GMCallMgr *v10; // [sp+40h] [bp+8h]@1
  CPlayer *pOnea; // [sp+48h] [bp+10h]@1
  int v12; // [sp+50h] [bp+18h]@1

  v12 = bCall;
  pOnea = pOne;
  v10 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !pOne )
    return 0;
  pInst = GMCallMgr::GetGMRequestDataPtr(v10, pOne);
  if ( v12 )
  {
    if ( !pInst )
    {
      v9 = GMCallMgr::PopReqEmptNode(v10);
      if ( v9 )
      {
        v6 = timeGetTime();
        GMRequestData::Set(v9, pOnea->m_dwObjSerial, pOnea->m_pUserDB->m_wszAvatorName, v6);
        return GMCallMgr::SendResponseGMCall(v10, pOnea, 1);
      }
    }
  }
  else if ( pInst )
  {
    GMCallMgr::PushReqNode(v10, pInst);
    return GMCallMgr::SendResponseGMCall(v10, pOnea, 0);
  }
  return 0;
}
