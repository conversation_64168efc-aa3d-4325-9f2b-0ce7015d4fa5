/*
 * Function: ?gm_UpdateMap@CMainThread@@QEAAXXZ
 * Address: 0x1401F7E60
 */

void __fastcall CMainThread::gm_UpdateMap(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMapTab::UpdateTab(&g_pDoc->m_InfoSheet.m_tabMap);
}
