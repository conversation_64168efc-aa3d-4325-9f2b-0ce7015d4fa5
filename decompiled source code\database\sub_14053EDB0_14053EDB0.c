/*
 * Function: sub_14053EDB0
 * Address: 0x14053EDB0
 */

signed __int64 __fastcall sub_14053EDB0(__int64 a1, __int64 a2, __int64 a3, int a4)
{
  __int64 v4; // rsi@1
  int v5; // er12@1
  __int64 v6; // rbp@1
  __int64 v7; // rbx@1
  __int64 v8; // rax@1
  __int64 v9; // rdi@1
  signed __int64 result; // rax@2
  __int64 v11; // rax@3
  __int64 v12; // r11@4
  int v13; // eax@4

  v4 = a3;
  v5 = a4;
  v6 = a2;
  v7 = a1;
  LODWORD(v8) = luaT_gettmbyobj(a1, a2, a4);
  v9 = v8;
  if ( *(_DWORD *)(v8 + 8) && (LODWORD(v11) = luaT_gettmbyobj(v7, v4, v5), (unsigned int)luaO_rawequalObj(v9, v11)) )
  {
    sub_14053E8D0(v7, *(_QWORD *)(v7 + 16), v9, v6, v4);
    v12 = *(_QWORD *)(v7 + 16);
    v13 = *(_DWORD *)(v12 + 8);
    result = v13 && (v13 != 1 || *(_DWORD *)v12);
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
