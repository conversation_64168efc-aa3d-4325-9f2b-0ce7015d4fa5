/*
 * Function: ?SendMsgGoal@CCircleZone@@AEAAXXZ
 * Address: 0x14012DDA0
 */

void __fastcall CCircleZone::SendMsgGoal(CCircleZone *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  CCircleZone *v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v7->m_iPortalInx;
  pbyType = 4;
  v6 = -82;
  CGameObject::CircleReport((CGameObject *)&v7->vfptr, &pbyType, szMsg, 4, 0);
}
