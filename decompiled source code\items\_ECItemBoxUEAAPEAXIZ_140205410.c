/*
 * Function: ??_ECItemBox@@UEAAPEAXI@Z
 * Address: 0x140205410
 */

CItemBox *__fastcall CItemBox::`vector deleting destructor'(CItemBox *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CItemBox *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CItemBox *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x178ui64,
      *(_DWORD *)((char *)&ptr[-1].m_Item.m_pInList + 7),
      (void (__cdecl *)(void *))CItemBox::~CItemBox);
    if ( v7 & 1 )
      operator delete[]((char *)&ptr[-1].m_Item.m_pInList + 7);
    result = (CItemBox *)((char *)ptr - 8);
  }
  else
  {
    CItemBox::~CItemBox(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
