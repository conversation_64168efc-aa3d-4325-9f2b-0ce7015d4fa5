/*
 * Function: ?SetPrivateExponent@?$DL_PrivateKeyImpl@VDL_GroupParameters_GFP_DefaultSafePrime@CryptoPP@@@CryptoPP@@UEAAXAEBVInteger@2@@Z
 * Address: 0x1406383C0
 */

CryptoPP::Integer *__fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_GFP_DefaultSafePrime>::SetPrivateExponent(__int64 a1, CryptoPP::Integer *a2)
{
  return CryptoPP::Integer::operator=((CryptoPP::Integer *)(a1 + 352), a2);
}
