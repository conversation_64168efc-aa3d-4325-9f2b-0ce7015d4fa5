/*
 * Function: j_?LoadXML@CUnmannedTraderClassInfoTableCodeType@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@K@Z
 * Address: 0x140013304
 */

bool __fastcall CUnmannedTraderClassInfoTableCodeType::LoadXML(CUnmannedTraderClassInfoTableCodeType *this, TiXmlElement *elemClass, CLogFile *kLogger, unsigned int dwDivisionID)
{
  return CUnmannedTraderClassInfoTableCodeType::LoadXML(this, elemClass, kLogger, dwDivisionID);
}
