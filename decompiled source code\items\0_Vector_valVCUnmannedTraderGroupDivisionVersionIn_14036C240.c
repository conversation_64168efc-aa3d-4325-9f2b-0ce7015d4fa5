/*
 * Function: ??0?$_Vector_val@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@1@@Z
 * Address: 0x14036C240
 */

void __fastcall std::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(std::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocator<CUnmannedTraderGroupDivisionVersionInfo>(
    &v5->_Alval,
    __formal);
}
