/*
 * Function: ?ReLoadAllMaterial@CLevel@@QEAAXXZ
 * Address: 0x1404E0580
 */

void __fastcall CLevel::ReLoadAllMaterial(CLevel *this)
{
  CLevel *v1; // rbx@1
  char *v2; // rbx@1
  char *v3; // rcx@1
  char v4; // al@2
  struct _R3MATERIAL *v5; // rax@3
  char v6[256]; // [sp+20h] [bp-118h]@1

  v1 = this;
  SetReLoadState(1);
  v2 = v1->mMapName;
  v3 = (char *)(v6 - v2);
  do
  {
    v4 = *v2++;
    v2[(_QWORD)v3 - 1] = v4;
  }
  while ( v4 );
  v5 = GetMainMaterial();
  ReLoadMaterial(v6, v5);
  SetReLoadState(0);
}
