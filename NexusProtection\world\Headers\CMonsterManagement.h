#pragma once

/**
 * @file CMonsterManagement.h
 * @brief Monster Management Classes for Game Entities
 * 
 * Provides comprehensive monster management functionality including looting,
 * aggro management, hierarchy, skill pools, and AI systems for the
 * NexusProtection engine.
 * 
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <array>
#include <vector>
#include <unordered_map>
#include <queue>
#include <functional>

namespace NexusProtection::World {

    // Forward declarations
    class CMonster;
    class CPlayer;
    class CItem;
    struct _object_id;

    /**
     * @brief Loot item structure
     */
    struct LootItem {
        uint32_t itemId{0};
        uint32_t quantity{1};
        float dropChance{0.0f};
        uint32_t minLevel{1};
        uint32_t maxLevel{999};
        bool isRare{false};
        
        bool IsValid() const;
        std::string ToString() const;
    };

    /**
     * @brief Aggro entry structure
     */
    struct AggroEntry {
        _object_id* targetId{nullptr};
        uint32_t aggroValue{0};
        std::chrono::steady_clock::time_point lastUpdate;
        bool isActive{true};
        
        bool IsValid() const;
        void UpdateAggro(uint32_t value);
        uint32_t GetDecayedAggro() const;
    };

    /**
     * @brief Monster hierarchy level enumeration
     */
    enum class MonsterHierarchyLevel : uint8_t {
        Minion = 0,
        Elite = 1,
        Champion = 2,
        Boss = 3,
        WorldBoss = 4,
        RaidBoss = 5
    };

    /**
     * @brief Monster skill structure
     */
    struct MonsterSkill {
        uint32_t skillId{0};
        uint32_t level{1};
        uint32_t cooldown{0};
        uint32_t manaCost{0};
        float range{0.0f};
        bool isActive{true};
        std::chrono::steady_clock::time_point lastUsed;
        
        bool CanUse() const;
        void Use();
        uint32_t GetRemainingCooldown() const;
    };

    /**
     * @brief Monster AI state enumeration
     */
    enum class MonsterAIState : uint8_t {
        Idle = 0,
        Patrol = 1,
        Chasing = 2,
        Combat = 3,
        Returning = 4,
        Dead = 5,
        Stunned = 6,
        Fleeing = 7
    };

    /**
     * @brief Looting Manager Class
     * 
     * Manages loot generation, distribution, and collection for monsters.
     * Handles drop rates, item quality, and player loot rights.
     */
    class CLootingMgr {
    public:
        // Constants
        static constexpr size_t MAX_LOOT_ITEMS = 32;
        static constexpr float DEFAULT_DROP_CHANCE = 0.1f;
        static constexpr uint32_t LOOT_TIMEOUT_MS = 300000; // 5 minutes

        // Constructor and Destructor
        CLootingMgr();
        virtual ~CLootingMgr();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // Loot generation
        virtual std::vector<LootItem> GenerateLoot(uint32_t monsterId, uint32_t monsterLevel);
        virtual bool AddLootItem(const LootItem& item);
        virtual bool RemoveLootItem(uint32_t itemId);
        virtual void ClearLoot();

        // Loot distribution
        virtual bool DistributeLoot(CMonster* monster, CPlayer* killer);
        virtual bool CanLoot(CPlayer* player, CMonster* monster) const;
        virtual std::vector<LootItem> GetPlayerLoot(CPlayer* player) const;

        // Configuration
        virtual void SetDropChanceMultiplier(float multiplier) { m_dropChanceMultiplier = multiplier; }
        virtual float GetDropChanceMultiplier() const { return m_dropChanceMultiplier; }
        virtual void SetLootTimeout(uint32_t timeoutMs) { m_lootTimeoutMs = timeoutMs; }

        // Utility
        virtual size_t GetLootCount() const { return m_lootItems.size(); }
        virtual bool IsEmpty() const { return m_lootItems.empty(); }
        virtual std::string GetDebugInfo() const;

    protected:
        std::vector<LootItem> m_lootItems;
        std::unordered_map<uint32_t, std::vector<LootItem>> m_playerLoot;
        float m_dropChanceMultiplier{1.0f};
        uint32_t m_lootTimeoutMs{LOOT_TIMEOUT_MS};
        mutable std::mutex m_lootMutex;

        virtual float CalculateDropChance(const LootItem& item, uint32_t monsterLevel) const;
        virtual bool RollForDrop(float chance) const;
        virtual void CleanupExpiredLoot();
    };

    /**
     * @brief Monster Aggro Manager Class
     * 
     * Manages monster aggro (threat) system, tracking which players
     * have generated threat and determining primary targets.
     */
    class CMonsterAggroMgr {
    public:
        // Constants
        static constexpr size_t MAX_AGGRO_ENTRIES = 16;
        static constexpr uint32_t AGGRO_DECAY_RATE = 10; // per second
        static constexpr uint32_t AGGRO_TIMEOUT_MS = 30000; // 30 seconds

        // Constructor and Destructor
        CMonsterAggroMgr();
        virtual ~CMonsterAggroMgr();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // Aggro management
        virtual void AddAggro(_object_id* targetId, uint32_t aggroValue);
        virtual void RemoveAggro(_object_id* targetId);
        virtual void ClearAggro();
        virtual void DecayAggro(float deltaTime);

        // Target selection
        virtual _object_id* GetPrimaryTarget() const;
        virtual _object_id* GetSecondaryTarget() const;
        virtual std::vector<_object_id*> GetAllTargets() const;

        // Query methods
        virtual uint32_t GetAggro(_object_id* targetId) const;
        virtual bool HasAggro(_object_id* targetId) const;
        virtual size_t GetAggroCount() const { return m_aggroEntries.size(); }
        virtual bool IsEmpty() const { return m_aggroEntries.empty(); }

        // Configuration
        virtual void SetDecayRate(uint32_t rate) { m_aggroDecayRate = rate; }
        virtual void SetTimeout(uint32_t timeoutMs) { m_aggroTimeoutMs = timeoutMs; }

        // Utility
        virtual std::string GetDebugInfo() const;

    protected:
        std::vector<AggroEntry> m_aggroEntries;
        uint32_t m_aggroDecayRate{AGGRO_DECAY_RATE};
        uint32_t m_aggroTimeoutMs{AGGRO_TIMEOUT_MS};
        mutable std::mutex m_aggroMutex;

        virtual void SortAggroEntries();
        virtual void CleanupExpiredAggro();
        virtual AggroEntry* FindAggroEntry(_object_id* targetId);
    };

    /**
     * @brief Monster Hierarchy Manager Class
     * 
     * Manages monster hierarchy levels, determining monster difficulty,
     * rewards, and special abilities based on hierarchy position.
     */
    class CMonsterHierarchy {
    public:
        // Constructor and Destructor
        CMonsterHierarchy();
        virtual ~CMonsterHierarchy();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();

        // Hierarchy management
        virtual MonsterHierarchyLevel GetHierarchyLevel(uint32_t monsterId) const;
        virtual void SetHierarchyLevel(uint32_t monsterId, MonsterHierarchyLevel level);
        virtual bool IsElite(uint32_t monsterId) const;
        virtual bool IsBoss(uint32_t monsterId) const;

        // Modifiers based on hierarchy
        virtual float GetHealthMultiplier(MonsterHierarchyLevel level) const;
        virtual float GetDamageMultiplier(MonsterHierarchyLevel level) const;
        virtual float GetExperienceMultiplier(MonsterHierarchyLevel level) const;
        virtual float GetLootMultiplier(MonsterHierarchyLevel level) const;

        // Utility
        virtual std::string GetHierarchyName(MonsterHierarchyLevel level) const;
        virtual std::string GetDebugInfo() const;

        // Legacy compatibility methods
        virtual void OnChildRegenLoop();
        virtual CMonster* GetParent() const;
        virtual uint32_t ChildKindCount() const;

    protected:
        std::unordered_map<uint32_t, MonsterHierarchyLevel> m_monsterHierarchy;
        mutable std::mutex m_hierarchyMutex;

        virtual void LoadDefaultHierarchy();
    };

    /**
     * @brief Monster Skill Pool Manager Class
     * 
     * Manages monster skills, cooldowns, and skill usage patterns.
     * Handles skill selection and execution for monster AI.
     */
    class CMonsterSkillPool {
    public:
        // Constants
        static constexpr size_t MAX_SKILLS = 8;

        // Constructor and Destructor
        CMonsterSkillPool();
        virtual ~CMonsterSkillPool();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // Skill management
        virtual bool AddSkill(const MonsterSkill& skill);
        virtual bool RemoveSkill(uint32_t skillId);
        virtual void ClearSkills();
        virtual MonsterSkill* GetSkill(uint32_t skillId);

        // Skill usage
        virtual MonsterSkill* SelectRandomSkill();
        virtual MonsterSkill* SelectBestSkill(float targetDistance) const;
        virtual bool CanUseSkill(uint32_t skillId) const;
        virtual bool UseSkill(uint32_t skillId);

        // Query methods
        virtual size_t GetSkillCount() const { return m_skills.size(); }
        virtual bool IsEmpty() const { return m_skills.empty(); }
        virtual std::vector<MonsterSkill> GetAvailableSkills() const;

        // Utility
        virtual std::string GetDebugInfo() const;

    protected:
        std::vector<MonsterSkill> m_skills;
        mutable std::mutex m_skillMutex;

        virtual void UpdateCooldowns(float deltaTime);
        virtual bool IsSkillInRange(const MonsterSkill& skill, float distance) const;
    };

    /**
     * @brief Monster AI Controller Class
     * 
     * Manages monster artificial intelligence, behavior patterns,
     * and decision making for combat and movement.
     */
    class CMonsterAI {
    public:
        // Constructor and Destructor
        CMonsterAI();
        virtual ~CMonsterAI();

        // Core functionality
        virtual bool Initialize(CMonster* monster);
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // State management
        virtual MonsterAIState GetState() const { return m_currentState; }
        virtual void SetState(MonsterAIState state);
        virtual void ForceState(MonsterAIState state);

        // Behavior control
        virtual void SetTarget(_object_id* targetId);
        virtual _object_id* GetTarget() const { return m_currentTarget; }
        virtual void ClearTarget();

        // AI parameters
        virtual void SetAggroRange(float range) { m_aggroRange = range; }
        virtual void SetChaseRange(float range) { m_chaseRange = range; }
        virtual void SetReturnRange(float range) { m_returnRange = range; }
        virtual void SetPatrolRange(float range) { m_patrolRange = range; }

        // Utility
        virtual std::string GetStateString() const;
        virtual std::string GetDebugInfo() const;

    protected:
        CMonster* m_monster{nullptr};
        MonsterAIState m_currentState{MonsterAIState::Idle};
        MonsterAIState m_previousState{MonsterAIState::Idle};
        _object_id* m_currentTarget{nullptr};
        
        // AI ranges
        float m_aggroRange{10.0f};
        float m_chaseRange{20.0f};
        float m_returnRange{30.0f};
        float m_patrolRange{5.0f};
        
        // Timing
        std::chrono::steady_clock::time_point m_lastStateChange;
        std::chrono::steady_clock::time_point m_lastUpdate;
        
        mutable std::mutex m_aiMutex;

        // State handlers
        virtual void UpdateIdleState(float deltaTime);
        virtual void UpdatePatrolState(float deltaTime);
        virtual void UpdateChasingState(float deltaTime);
        virtual void UpdateCombatState(float deltaTime);
        virtual void UpdateReturningState(float deltaTime);
        virtual void UpdateStunnedState(float deltaTime);
        virtual void UpdateFleeingState(float deltaTime);

        // Utility methods
        virtual bool IsTargetInRange(_object_id* target, float range) const;
        virtual float GetDistanceToTarget(_object_id* target) const;
        virtual _object_id* FindNearestEnemy() const;
        virtual bool ShouldReturnToSpawn() const;

    public:
        // Legacy compatibility member (for backward compatibility with old CMonster code)
        void* vfptr{nullptr};  ///< Virtual function table pointer for legacy compatibility
    };

    /**
     * @brief Utility functions for monster management
     */
    namespace MonsterManagementUtils {
        std::string MonsterHierarchyLevelToString(MonsterHierarchyLevel level);
        std::string MonsterAIStateToString(MonsterAIState state);
        float CalculateDistance(const float* pos1, const float* pos2);
        bool IsValidObjectId(const _object_id* id);
        uint32_t GenerateRandomValue(uint32_t min, uint32_t max);
        float GenerateRandomFloat(float min, float max);
    }

} // namespace NexusProtection::World

// Legacy C interface for compatibility
extern "C" {
    // CLootingMgr legacy interface
    struct CLootingMgr_Legacy {
        void* vfptr;
        // Additional legacy fields
    };

    void CLootingMgr_Constructor(CLootingMgr_Legacy* mgr);
    void CLootingMgr_Destructor(CLootingMgr_Legacy* mgr);
    bool CLootingMgr_Initialize(CLootingMgr_Legacy* mgr);
    void CLootingMgr_GenerateLoot(CLootingMgr_Legacy* mgr, uint32_t monsterId, uint32_t level);

    // CMonsterAggroMgr legacy interface
    struct CMonsterAggroMgr_Legacy {
        void* vfptr;
        // Additional legacy fields
    };

    void CMonsterAggroMgr_Constructor(CMonsterAggroMgr_Legacy* mgr);
    void CMonsterAggroMgr_Destructor(CMonsterAggroMgr_Legacy* mgr);
    void CMonsterAggroMgr_AddAggro(CMonsterAggroMgr_Legacy* mgr, _object_id* target, uint32_t value);
    _object_id* CMonsterAggroMgr_GetPrimaryTarget(CMonsterAggroMgr_Legacy* mgr);
}
