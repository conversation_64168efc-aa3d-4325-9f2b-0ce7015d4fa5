/*
 * Function: ?mgr_gotoDstCoordinates@CPlayer@@QEAA_NPEAD0MMM@Z
 * Address: 0x1400B88E0
 */

char __fastcall CPlayer::mgr_gotoDstCoordinates(CPlayer *this, char *pwszDstName, char *pszMapCode, float fX, float fY, float fZ)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v9; // rax@25
  __int64 v10; // [sp+0h] [bp-78h]@1
  float *pfStartPos; // [sp+20h] [bp-58h]@25
  CPlayer *v12; // [sp+30h] [bp-48h]@4
  CMapData *pIntoMap; // [sp+38h] [bp-40h]@19
  float fPos; // [sp+48h] [bp-30h]@23
  float v15; // [sp+4Ch] [bp-2Ch]@23
  float v16; // [sp+50h] [bp-28h]@23
  CPlayer *v17; // [sp+80h] [bp+8h]@1
  char *szMapCode; // [sp+90h] [bp+18h]@1

  szMapCode = pszMapCode;
  v17 = this;
  v6 = &v10;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v12 = 0i64;
  if ( pwszDstName )
  {
    v12 = GetPtrPlayerFromName(&g_Player, 2532, pwszDstName);
  }
  else
  {
    if ( !v17->m_TargetObject.pObject )
      return 0;
    if ( !v17->m_TargetObject.pObject->m_bLive )
      return 0;
    if ( v17->m_TargetObject.pObject->m_ObjID.m_byKind || v17->m_TargetObject.pObject->m_ObjID.m_byID )
      return 0;
    if ( v17->m_TargetObject.byKind != v17->m_TargetObject.pObject->m_ObjID.m_byKind
      || v17->m_TargetObject.byID != v17->m_TargetObject.pObject->m_ObjID.m_byID )
    {
      return 0;
    }
    v12 = (CPlayer *)v17->m_TargetObject.pObject;
  }
  if ( v12 )
  {
    pIntoMap = CMapOperation::GetMap(&g_MapOper, szMapCode);
    if ( pIntoMap )
    {
      if ( pIntoMap->m_pMapSet->m_nMapType )
      {
        result = 0;
      }
      else
      {
        fPos = fX;
        v15 = fY;
        v16 = fZ;
        if ( CMapData::IsMapIn(pIntoMap, &fPos) )
        {
          CPlayer::OutOfMap(v12, pIntoMap, 0, 4, &fPos);
          v9 = (char *)pIntoMap->m_pMapSet;
          LOBYTE(pfStartPos) = 4;
          CPlayer::SendMsg_GotoRecallResult(v12, 0, *v9, &fPos, 4);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
