/*
 * Function: ?GetTotalTol@CCharacter@@QEAAHEH@Z
 * Address: 0x1401733B0
 */

__int64 __fastcall CCharacter::GetTotalTol(CCharacter *this, char byAttTolType, int nDamPoint)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-68h]@1
  float v7; // [sp+20h] [bp-48h]@4
  float v8; // [sp+38h] [bp-30h]@4
  float v9; // [sp+3Ch] [bp-2Ch]@4
  float v10; // [sp+40h] [bp-28h]@4
  float v11; // [sp+44h] [bp-24h]@4
  float v12; // [sp+54h] [bp-14h]@4
  float v13; // [sp+58h] [bp-10h]@4
  char v14; // [sp+5Ch] [bp-Ch]@4
  CCharacter *v15; // [sp+70h] [bp+8h]@1
  char v16; // [sp+78h] [bp+10h]@1

  v16 = byAttTolType;
  v15 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (float)nDamPoint;
  v8 = (float)((int (__fastcall *)(CCharacter *))v15->vfptr->GetFireTol)(v15) / 100.0;
  v9 = (float)((int (__fastcall *)(CCharacter *))v15->vfptr->GetWaterTol)(v15) / 100.0;
  v10 = (float)((int (__fastcall *)(CCharacter *))v15->vfptr->GetSoilTol)(v15) / 100.0;
  v11 = (float)((int (__fastcall *)(CCharacter *))v15->vfptr->GetWindTol)(v15) / 100.0;
  v12 = FLOAT_0_1;
  v13 = 1.0 - 0.1;
  v14 = v16;
  if ( v16 )
  {
    switch ( v14 )
    {
      case 1:
        result = (unsigned int)(signed int)ffloor((float)((float)((float)(v7 * v8) * v13)
                                                        - (float)((float)(v7 * v9) * v12)) - (float)((float)(v7 * v11) * v13));
        break;
      case 2:
        result = (unsigned int)(signed int)ffloor((float)((float)((float)(-0.0 - (float)(v7 * v8)) * v13)
                                                        - (float)((float)(v7 * v10) * v12)) + (float)((float)(v7 * v11) * v13));
        break;
      case 3:
        result = (unsigned int)(signed int)ffloor((float)((float)((float)(v7 * v9) * v13)
                                                        - (float)((float)(v7 * v10) * v13)) - (float)((float)(v7 * v11) * v12));
        break;
      default:
        result = 0i64;
        break;
    }
  }
  else
  {
    result = (unsigned int)(signed int)ffloor((float)((float)((float)(-0.0 - (float)(v7 * v8)) * v12)
                                                    - (float)((float)(v7 * v9) * v13)) + (float)((float)(v7 * v10) * v13));
  }
  return result;
}
