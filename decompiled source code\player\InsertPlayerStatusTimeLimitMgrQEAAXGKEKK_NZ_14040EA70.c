/*
 * Function: ?InsertPlayerStatus@TimeLimitMgr@@QEAAXGKEKK_N@Z
 * Address: 0x14040EA70
 */

void __fastcall TimeLimitMgr::InsertPlayerStatus(TimeLimitMgr *this, unsigned __int16 wIndex, unsigned int dwAccountSerial, char byStatus, unsigned int dwFatigue, unsigned int dwLastLogoutTime, bool bAgeLimit)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-68h]@1
  Player_TL_Status data; // [sp+28h] [bp-40h]@4
  TimeLimitMgr *v11; // [sp+70h] [bp+8h]@1
  unsigned __int16 v12; // [sp+78h] [bp+10h]@1
  unsigned int v13; // [sp+80h] [bp+18h]@1

  v13 = dwAccountSerial;
  v12 = wIndex;
  v11 = this;
  v7 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v7 = -*********;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  data.m_dwFatigue = dwFatigue;
  data.m_byTL_Status = byStatus;
  data.m_dwStartTime = timeGetTime();
  data.m_dwAccountSerial = v13;
  data.m_dwLastLogoutTime = dwLastLogoutTime;
  data.m_bAgeLimit = bAgeLimit;
  TimeLimitMgr::Push_Data(v11, &data, v12);
}
