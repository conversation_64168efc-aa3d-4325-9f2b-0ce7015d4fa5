/*
 * Function: ?Loop_Event@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x1404129A0
 */

void __fastcall CGoldenBoxItemMgr::Loop_Event(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@7
  unsigned __int16 v4; // ax@13
  __int64 v5; // [sp+0h] [bp-28h]@1
  CGoldenBoxItemMgr *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkDB && CRFNewDatabase::IsConectionActive((CRFNewDatabase *)&pkDB->vfptr) )
  {
    GetLocalTime(&v6->tm);
    if ( !v6->tm.wHour )
    {
      v3 = v6->tm.wMinute;
      if ( (signed int)v6->tm.wMinute < 1 )
      {
        if ( v6->tm1.wMinute >= (signed int)v6->tm.wMinute
          && v6->tm1.wSecond >= (signed int)v6->tm.wSecond
          && v6->tm1.wMilliseconds >= (signed int)v6->tm.wMilliseconds )
        {
          v6->m_bInit = 0;
        }
        if ( !v6->m_bInit )
        {
          v4 = CGoldenBoxItemMgr::Get_StarterBox_Count(v6);
          CGoldenBoxItemMgr::Set_StarterBox_Count(v6, v4, 0);
          CGoldenBoxItemMgr::Set_StarterBox_Count(v6, v6->m_wStarterBoxNum, 1);
          CGoldenBoxItemMgr::Set_ToStruct(v6);
          GetLocalTime(&v6->tm1);
          v6->m_bInit = 1;
        }
      }
    }
    if ( CMyTimer::CountingTimer(&v6->m_tmLoopTimer) )
      CGoldenBoxItemMgr::Check_Event_Status(v6);
    if ( unk_1799C608D
      && CNetTimer::CountingTimer(&v6->m_golden_box_event.m_event_timer)
      && v6->m_golden_box_event.m_event_status == 2 )
    {
      CGoldenBoxItemMgr::PushDQSUpdate(v6);
    }
  }
}
