/*
 * Function: _CHashMapPtrPool_int_CNationCodeStr_::_CHashMapPtrPool_int_CNationCodeStr__::_1_::dtor$0
 * Address: 0x140208430
 */

void __fastcall CHashMapPtrPool_int_CNationCodeStr_::_CHashMapPtrPool_int_CNationCodeStr__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  stdext::hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>>::~hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>>((stdext::hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> > > *)(*(_QWORD *)(a2 + 64) + 8i64));
}
