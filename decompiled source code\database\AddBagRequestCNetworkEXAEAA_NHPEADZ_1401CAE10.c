/*
 * Function: ?AddBagRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CAE10
 */

char __fastcall CNetworkEX::AddBagRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int16 *v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned __int16 *)pBuf;
  v8 = &g_Player + n;
  if ( !v8->m_bOper || v8->m_pmTrd.bDTradeMode || v8->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    CPlayer::pc_AddBag(v8, *v7);
    result = 1;
  }
  return result;
}
