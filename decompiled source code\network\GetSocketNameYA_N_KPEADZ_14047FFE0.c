/*
 * Function: ?GetSocketName@@YA_N_KPEAD@Z
 * Address: 0x14047FFE0
 */

char __fastcall GetSocketName(unsigned __int64 socket, char *pOut)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-88h]@1
  struct sockaddr name; // [sp+28h] [bp-60h]@4
  int namelen; // [sp+54h] [bp-34h]@4
  int v8; // [sp+64h] [bp-24h]@5
  unsigned __int64 v9; // [sp+70h] [bp-18h]@4
  unsigned __int64 s; // [sp+90h] [bp+8h]@1
  char *dest; // [sp+98h] [bp+10h]@1

  dest = pOut;
  s = socket;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  namelen = 16;
  if ( getsockname(s, &name, &namelen) == -1 )
  {
    v8 = WSAGetLastError();
    result = 0;
  }
  else
  {
    GetAddrString((sockaddr_ipx *)&name, dest);
    result = 1;
  }
  return result;
}
