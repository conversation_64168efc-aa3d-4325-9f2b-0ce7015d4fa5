/*
 * Function: ?Update_GuildEmblem@CRFWorldDatabase@@QEAA_NKNKK@Z
 * Address: 0x14049B020
 */

bool __fastcall CRFWorldDatabase::Update_GuildEmblem(CRFWorldDatabase *this, unsigned int dwGuildSerial, long double dCurDalant, unsigned int dwEmblemBack, unsigned int dwEmblemMark)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp-20h] [bp-178h]@1
  unsigned int v9; // [sp+0h] [bp-158h]@4
  long double v10; // [sp+8h] [bp-150h]@4
  unsigned int v11; // [sp+10h] [bp-148h]@4
  char DstBuf; // [sp+30h] [bp-128h]@4
  char v13; // [sp+31h] [bp-127h]@4
  unsigned __int64 v14; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+160h] [bp+8h]@1

  v15 = this;
  v5 = &v8;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  DstBuf = 0;
  memset(&v13, 0, 0xFFui64);
  v11 = dwGuildSerial;
  v10 = dCurDalant;
  v9 = dwEmblemMark;
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "update [dbo].[tbl_guild] set emblemBack=%d, emblemMark=%d, dalant=%f where Serial=%d",
    dwEmblemBack);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v15->vfptr, &DstBuf, 1);
}
