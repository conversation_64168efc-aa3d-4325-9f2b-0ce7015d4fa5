/*
 * Function: j_??$_Uninit_move@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013C7D
 */

CUnmannedTraderRegistItemInfo *__fastcall std::_Uninit_move<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>,std::_Undefined_move_tag>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::allocator<CUnmannedTraderRegistItemInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
