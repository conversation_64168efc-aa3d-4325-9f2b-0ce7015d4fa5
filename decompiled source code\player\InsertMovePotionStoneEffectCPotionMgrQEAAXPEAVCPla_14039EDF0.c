/*
 * Function: ?InsertMovePotionStoneEffect@CPotionMgr@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14039EDF0
 */

void __fastcall CPotionMgr::InsertMovePotionStoneEffect(CPotionMgr *this, CPlayer *pApplyPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+38h] [bp+10h]@1

  v6 = pApplyPlayer;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pApplyPlayer )
  {
    _effect_parameter::SetEff_State(&pApplyPlayer->m_EP, 28, 1);
    v4 = _sf_continous::GetSFContCurTime();
    _ContPotionData::Set(&v6->m_PotionParam.m_StoneOfMovePotionData, 0x1B8u, v4, 0xAu);
  }
}
