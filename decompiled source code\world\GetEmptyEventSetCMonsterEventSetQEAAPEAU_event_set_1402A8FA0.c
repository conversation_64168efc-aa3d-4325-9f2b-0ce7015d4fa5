/*
 * Function: ?GetEmptyEventSet@CMonsterEventSet@@QEAAPEAU_event_set@@XZ
 * Address: 0x1402A8FA0
 */

_event_set *__fastcall CMonsterEventSet::GetEmptyEventSet(CMonsterEventSet *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CMonsterEventSet *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
  {
    if ( !strcmp_0(v6->m_EventSet[j].m_strId, byte_1407B5561) )
      return &v6->m_EventSet[j];
  }
  return 0i64;
}
