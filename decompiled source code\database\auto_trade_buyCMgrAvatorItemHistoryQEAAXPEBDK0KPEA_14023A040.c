/*
 * Function: ?auto_trade_buy@CMgrAvatorItemHistory@@QEAAXPEBDK0KPEAU_db_con@_STORAGE_LIST@@KKKPEAD@Z
 * Address: 0x14023A040
 */

void __fastcall CMgrAvatorItemHistory::auto_trade_buy(CMgrAvatorItemHistory *this, const char *szSellerName, unsigned int dwSellerSerial, const char *szSellerID, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pItem, unsigned int dwPrice, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  char *v12; // rax@4
  __int64 v13; // [sp+0h] [bp-78h]@1
  unsigned __int64 v14; // [sp+20h] [bp-58h]@4
  char *v15; // [sp+28h] [bp-50h]@4
  unsigned __int64 v16; // [sp+30h] [bp-48h]@4
  unsigned int v17; // [sp+38h] [bp-40h]@4
  unsigned int v18; // [sp+40h] [bp-38h]@4
  unsigned int v19; // [sp+48h] [bp-30h]@4
  char *v20; // [sp+50h] [bp-28h]@4
  char *v21; // [sp+58h] [bp-20h]@4
  _base_fld *v22; // [sp+60h] [bp-18h]@4
  CMgrAvatorItemHistory *v23; // [sp+80h] [bp+8h]@1

  v23 = this;
  v10 = &v13;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v10 = -*********;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  sData[0] = 0;
  v21 = v23->m_szCurTime;
  v20 = v23->m_szCurDate;
  v19 = dwLeftGold;
  v18 = dwLeftDalant;
  v17 = dwPrice;
  v16 = (unsigned __int64)szSellerID;
  LODWORD(v15) = dwSellerSerial;
  v14 = (unsigned __int64)szSellerName;
  sprintf_s(
    sBuf,
    0x2800ui64,
    "AUTO TRADE(BUY): reg(%u) seller(%s:%d id:%s) pay(D:%u) tax(none) $D:%u $G:%u [%s %s]\r\n",
    dwRegistSerial);
  strcat_0(sData, sBuf);
  v22 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v12 = DisplayItemUpgInfo(pItem->m_byTableCode, pItem->m_dwLv);
  v16 = pItem->m_lnUID;
  v15 = v12;
  v14 = pItem->m_dwDur;
  sprintf_s(sBuf, 0x2800ui64, "\t+ %s_%u_@%s[%I64u]\r\n", v22->m_strCode);
  strcat_s(sData, 0x4E20ui64, sBuf);
  CMgrAvatorItemHistory::WriteFile(v23, pszFileName, sData);
}
