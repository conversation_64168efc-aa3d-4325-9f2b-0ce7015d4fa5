/*
 * Function: _CUnmannedTraderGroupIDInfo::_CUnmannedTraderGroupIDInfo_::_1_::dtor$0
 * Address: 0x140385D80
 */

void __fastcall CUnmannedTraderGroupIDInfo::_CUnmannedTraderGroupIDInfo_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>((std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)(*(_QWORD *)(a2 + 64) + 8i64));
}
