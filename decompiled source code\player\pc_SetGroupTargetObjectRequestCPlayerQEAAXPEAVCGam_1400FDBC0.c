/*
 * Function: ?pc_SetGroupTargetObjectRequest@CPlayer@@QEAAXPEAVCGameObject@@KE@Z
 * Address: 0x1400FDBC0
 */

void __fastcall CPlayer::pc_SetGroupTargetObjectRequest(CPlayer *this, CGameObject *pTar, unsigned int dwSerial, char byGroupType)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@32
  CPvpUserAndGuildRankingSystem *v7; // rax@44
  unsigned int v8; // eax@44
  char *v9; // rax@48
  int v10; // eax@53
  __int64 v11; // [sp+0h] [bp-98h]@1
  int j; // [sp+20h] [bp-78h]@21
  char v13; // [sp+24h] [bp-74h]@4
  CPlayer *v14; // [sp+28h] [bp-70h]@4
  CPartyPlayer **v15; // [sp+30h] [bp-68h]@4
  _guild_member_info *v16; // [sp+38h] [bp-60h]@4
  char Dst; // [sp+48h] [bp-50h]@45
  char v18; // [sp+70h] [bp-28h]@14
  unsigned int v19; // [sp+74h] [bp-24h]@32
  int v20; // [sp+78h] [bp-20h]@44
  unsigned int v21; // [sp+7Ch] [bp-1Ch]@44
  int v22; // [sp+80h] [bp-18h]@53
  unsigned __int64 v23; // [sp+88h] [bp-10h]@4
  CPlayer *v24; // [sp+A0h] [bp+8h]@1
  CGameObject *pTara; // [sp+A8h] [bp+10h]@1
  char v26; // [sp+B8h] [bp+20h]@1

  v26 = byGroupType;
  pTara = pTar;
  v24 = this;
  v4 = &v11;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = (unsigned __int64)&v11 ^ _security_cookie;
  v13 = 0;
  v14 = 0i64;
  v15 = 0i64;
  v16 = 0i64;
  if ( pTar )
  {
    if ( pTar->m_bLive )
    {
      if ( pTar->m_dwObjSerial == dwSerial )
      {
        if ( IsTargeting(pTar) )
        {
          if ( pTara->m_ObjID.m_byID == 2 )
          {
            v13 = 4;
            CPlayer::SendMsg_SetGroupTargetObjectResult(v24, 4, v26);
          }
          else
          {
            v18 = v26;
            if ( v26 )
            {
              if ( v18 == 1 )
              {
                if ( v24->m_Param.m_pGuild )
                {
                  v19 = CGuild::GetGuildMasterSerial(v24->m_Param.m_pGuild);
                  v6 = CPlayerDB::GetCharSerial(&v24->m_Param);
                  if ( v19 == v6 )
                  {
                    for ( j = 0; j < 50; ++j )
                    {
                      v16 = &v24->m_Param.m_pGuild->m_MemberData[j];
                      if ( _guild_member_info::IsFill(v16) )
                      {
                        v14 = v16->pPlayer;
                        if ( v14 )
                        {
                          if ( v14->m_bOper
                            && v14->m_pCurMap == v24->m_pCurMap
                            && v14->m_wMapLayerIndex == v24->m_wMapLayerIndex )
                          {
                            CPlayer::__target::init(&v14->m_GroupTargetObject[(unsigned __int8)v26]);
                            v14->m_GroupTargetObject[(unsigned __int8)v26].pObject = pTara;
                            v14->m_GroupTargetObject[(unsigned __int8)v26].byKind = pTara->m_ObjID.m_byKind;
                            v14->m_GroupTargetObject[(unsigned __int8)v26].byID = pTara->m_ObjID.m_byID;
                            v14->m_GroupTargetObject[(unsigned __int8)v26].dwSerial = pTara->m_dwObjSerial;
                            CPlayer::SendMsg_SetGroupTargetObjectResult(v14, v13, v26);
                          }
                        }
                      }
                    }
                  }
                }
              }
              else if ( v18 == 2 )
              {
                v20 = CPlayerDB::GetRaceCode(&v24->m_Param);
                v7 = CPvpUserAndGuildRankingSystem::Instance();
                v21 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v7, v20, 0);
                v8 = CPlayerDB::GetCharSerial(&v24->m_Param);
                if ( v21 == v8 )
                {
                  memset_0(&Dst, 0, 0x11ui64);
                  if ( !pTara->m_ObjID.m_byKind && !pTara->m_ObjID.m_byID && BYTE1(pTara[9].m_SectorNetPoint.m_pNext) )
                  {
                    v9 = CPlayerDB::GetCharNameW((CPlayerDB *)&pTara[10].m_bCorpse);
                    strcpy_0(&Dst, v9);
                  }
                  for ( j = 0; j < 2532; ++j )
                  {
                    v14 = &g_Player + j;
                    if ( v14->m_bOper )
                    {
                      v22 = CPlayerDB::GetRaceCode(&v14->m_Param);
                      v10 = CPlayerDB::GetRaceCode(&v24->m_Param);
                      if ( v22 == v10 )
                      {
                        if ( ((int (__fastcall *)(CPlayer *))v14->vfptr->GetLevel)(v14) >= 25 && Dst )
                          CPlayer::SendMsg_GroupTargetInform(v14, v26, &Dst);
                        if ( v14->m_pCurMap == v24->m_pCurMap && v14->m_wMapLayerIndex == v24->m_wMapLayerIndex )
                        {
                          CPlayer::__target::init(&v14->m_GroupTargetObject[(unsigned __int8)v26]);
                          v14->m_GroupTargetObject[(unsigned __int8)v26].pObject = pTara;
                          v14->m_GroupTargetObject[(unsigned __int8)v26].byKind = pTara->m_ObjID.m_byKind;
                          v14->m_GroupTargetObject[(unsigned __int8)v26].byID = pTara->m_ObjID.m_byID;
                          v14->m_GroupTargetObject[(unsigned __int8)v26].dwSerial = pTara->m_dwObjSerial;
                          CPlayer::SendMsg_SetGroupTargetObjectResult(v14, v13, v26);
                        }
                      }
                    }
                  }
                }
              }
            }
            else if ( CPartyPlayer::IsPartyMode(v24->m_pPartyMgr) && CPartyPlayer::IsPartyBoss(v24->m_pPartyMgr) )
            {
              v15 = CPartyPlayer::GetPtrPartyMember(v24->m_pPartyMgr);
              if ( v15 )
              {
                for ( j = 0; j < 8; ++j )
                {
                  if ( v15[j] )
                  {
                    v14 = &g_Player + v15[j]->m_id.wIndex;
                    if ( v14->m_bOper )
                    {
                      if ( v14->m_pCurMap == v24->m_pCurMap && v14->m_wMapLayerIndex == v24->m_wMapLayerIndex )
                      {
                        CPlayer::__target::init(v14->m_GroupTargetObject);
                        v14->m_GroupTargetObject[0].pObject = pTara;
                        v14->m_GroupTargetObject[0].byKind = pTara->m_ObjID.m_byKind;
                        v14->m_GroupTargetObject[0].byID = pTara->m_ObjID.m_byID;
                        v14->m_GroupTargetObject[0].dwSerial = pTara->m_dwObjSerial;
                        CPlayer::SendMsg_SetGroupTargetObjectResult(v14, v13, 0);
                      }
                    }
                  }
                }
              }
            }
          }
        }
        else
        {
          v13 = 4;
          CPlayer::SendMsg_SetGroupTargetObjectResult(v24, 4, v26);
        }
      }
      else
      {
        v13 = 3;
        CPlayer::SendMsg_SetGroupTargetObjectResult(v24, 3, byGroupType);
      }
    }
    else
    {
      v13 = 2;
      CPlayer::SendMsg_SetGroupTargetObjectResult(v24, 2, byGroupType);
    }
  }
  else
  {
    v13 = 1;
    CPlayer::SendMsg_SetGroupTargetObjectResult(v24, 1, byGroupType);
  }
}
