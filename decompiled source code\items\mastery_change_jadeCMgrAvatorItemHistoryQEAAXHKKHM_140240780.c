/*
 * Function: ?mastery_change_jade@CMgrAvatorItemHistory@@QEAAXHKKHMPEADH@Z
 * Address: 0x140240780
 */

void __fastcall CMgrAvatorItemHistory::mastery_change_jade(CMgrAvatorItemHistory *this, int nMstCode, unsigned int dwOldCum, unsigned int dwNewCum, int nLv, float fVal, char *szFileName, int nWpType)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-98h]@1
  unsigned int v11; // [sp+20h] [bp-78h]@14
  double v12; // [sp+28h] [bp-70h]@14
  unsigned int v13; // [sp+30h] [bp-68h]@14
  char Dest; // [sp+48h] [bp-50h]@6
  int v15; // [sp+80h] [bp-18h]@4
  unsigned __int64 v16; // [sp+88h] [bp-10h]@4
  CMgrAvatorItemHistory *v17; // [sp+A0h] [bp+8h]@1
  unsigned int v18; // [sp+B0h] [bp+18h]@1
  unsigned int v19; // [sp+B8h] [bp+20h]@1

  v19 = dwNewCum;
  v18 = dwOldCum;
  v17 = this;
  v8 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v16 = (unsigned __int64)&v10 ^ _security_cookie;
  v15 = nMstCode;
  switch ( nMstCode )
  {
    case 0:
      if ( nWpType )
        strcpy_0(&Dest, "mastery_weapon_long");
      else
        strcpy_0(&Dest, "mastery_weapon_close");
      break;
    case 1:
      strcpy_0(&Dest, "mastery_code_defence");
      break;
    case 2:
      strcpy_0(&Dest, "mastery_code_shield");
      break;
    case 3:
      strcpy_0(&Dest, "mastery_code_shield");
      break;
    case 4:
      strcpy_0(&Dest, "mastery_code_force");
      break;
    case 6:
      strcpy_0(&Dest, "mastery_code_special");
      break;
    default:
      break;
  }
  v13 = v19;
  v12 = (float)((float)nLv + fVal);
  v11 = v18;
  sprintf_s<20000>((char (*)[20000])sData, "MasteryChange(%s) : %d Lv[%d] => %d Lv[%d] \r\n", &Dest, (unsigned int)nLv);
  CMgrAvatorItemHistory::WriteFile(v17, szFileName, sData);
}
