/*
 * Function: ??$_Fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140391C90
 */

void __fastcall std::_Fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::random_access_iterator_tag>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  std::_Range_checked_iterator_tag v8; // [sp+20h] [bp-18h]@4
  TRC_AutoTrade **_Firsta; // [sp+40h] [bp+8h]@1

  _Firsta = _First;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  std::_Fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *>(_Firsta, _Count, _Val, v8);
}
