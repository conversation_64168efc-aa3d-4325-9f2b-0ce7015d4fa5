#pragma once

/**
 * @file CGuildBattleController.h
 * @brief Modern C++20 Guild Battle Controller System
 * 
 * This file provides comprehensive guild battle control and coordination
 * with modern C++20 patterns, proper initialization, and modular design.
 * 
 * Refactored from decompiled sources:
 * - InitCGuildBattleControllerQEAA_NXZ_1403D5820.c (105 lines)
 * - LoadCGuildBattleControllerQEAA_NXZ_1403D5950.c (116 lines)
 * - AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1403D5DB0.c
 * - LoopCGuildBattleControllerQEAAXXZ_1403D6760.c
 * - CleanUpCGuildBattleControllerIEAAXXZ_1403D7900.c
 * - ClearCGuildBattleControllerQEAAXXZ_1403D62F0.c
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>

// Forward declarations
class CGuild;

namespace NexusProtection {
namespace Guild {

// Forward declarations
class CGuildBattleManager;
class CGuildRankingManager;
class CGuildBattleScheduler;

/**
 * @brief Guild battle controller result enumeration
 */
enum class GuildBattleControllerResult : uint8_t {
    Success = 0,
    InitializationFailed,
    LoadFailed,
    InvalidGuild,
    BattleCreationFailed,
    SystemError
};

/**
 * @brief Guild battle controller state enumeration
 */
enum class GuildBattleControllerState : uint8_t {
    Uninitialized = 0,
    Initializing,
    Loading,
    Ready,
    Running,
    Error,
    Shutdown
};

/**
 * @brief Guild battle controller configuration
 */
struct GuildBattleControllerConfig {
    // System settings
    bool enableLogging{true};
    bool enableRanking{true};
    bool enableScheduling{true};
    bool enableRewards{true};
    
    // Battle settings
    uint32_t maxConcurrentBattles{10};
    uint32_t maxParticipantsPerBattle{100};
    std::chrono::minutes defaultBattleDuration{60};
    std::chrono::minutes preparationTime{10};
    
    // Map settings
    uint32_t defaultMapCount{5};
    std::vector<uint32_t> availableMaps;
    
    // Timing settings
    std::chrono::seconds updateInterval{1};
    std::chrono::seconds cleanupInterval{300};
    
    GuildBattleControllerConfig() {
        // Initialize with default available maps
        availableMaps = {1, 2, 3, 4, 5};
    }
    
    bool IsValid() const {
        return maxConcurrentBattles > 0 && 
               maxParticipantsPerBattle > 0 &&
               defaultBattleDuration.count() > 0 &&
               !availableMaps.empty();
    }
};

/**
 * @brief Guild battle controller statistics
 */
struct GuildBattleControllerStats {
    std::atomic<uint64_t> totalBattlesCreated{0};
    std::atomic<uint64_t> totalBattlesCompleted{0};
    std::atomic<uint64_t> totalGuildsParticipated{0};
    std::atomic<uint32_t> currentActiveBattles{0};
    std::atomic<uint32_t> systemErrors{0};
    std::chrono::steady_clock::time_point startTime;
    
    GuildBattleControllerStats() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetCompletionRate() const {
        uint64_t total = totalBattlesCreated.load();
        return total > 0 ? (static_cast<double>(totalBattlesCompleted.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Modern C++20 Guild Battle Controller class
 * 
 * This class provides comprehensive guild battle control and coordination
 * with modern C++20 patterns, proper initialization, and modular design.
 */
class CGuildBattleController {
public:
    // Constructor and Destructor
    CGuildBattleController();
    explicit CGuildBattleController(const GuildBattleControllerConfig& config);
    virtual ~CGuildBattleController();

    // Disable copy constructor and assignment operator
    CGuildBattleController(const CGuildBattleController&) = delete;
    CGuildBattleController& operator=(const CGuildBattleController&) = delete;

    // Enable move constructor and assignment operator
    CGuildBattleController(CGuildBattleController&&) noexcept = default;
    CGuildBattleController& operator=(CGuildBattleController&&) noexcept = default;

    /**
     * @brief Initialize guild battle controller
     * 
     * Modern implementation of CGuildBattleController::Init method.
     * Initializes all subsystems including logger, rank manager, scheduler, etc.
     * 
     * @return GuildBattleControllerResult indicating success or failure
     */
    GuildBattleControllerResult Initialize();

    /**
     * @brief Load guild battle data
     * 
     * Modern implementation of CGuildBattleController::Load method.
     * Loads configuration, maps, schedules, and persistent data.
     * 
     * @return GuildBattleControllerResult indicating success or failure
     */
    GuildBattleControllerResult LoadData();

    /**
     * @brief Update guild battle controller
     * 
     * Modern implementation of CGuildBattleController::Loop method.
     * Updates all subsystems and processes pending operations.
     * 
     * @param deltaTime Time elapsed since last update
     */
    void Update(float deltaTime);

    /**
     * @brief Shutdown guild battle controller
     * 
     * Modern implementation of CGuildBattleController::CleanUp method.
     * Safely shuts down all subsystems and saves state.
     */
    void Shutdown();

    /**
     * @brief Clear guild battle data
     * 
     * Modern implementation of CGuildBattleController::Clear method.
     * Clears all active battles and resets system state.
     */
    void Clear();

    /**
     * @brief Add guild battle
     * 
     * Modern implementation of CGuildBattleController::Add method.
     * Creates a new guild battle with specified parameters.
     * 
     * @param srcGuild Source guild
     * @param destGuild Destination guild
     * @param startTime Battle start time
     * @param elapseTimeCnt Elapsed time count
     * @param battleNumber Battle number
     * @param mapIndex Map index
     * @return Battle ID if successful, 0 if failed
     */
    uint32_t AddBattle(std::shared_ptr<CGuild> srcGuild, std::shared_ptr<CGuild> destGuild,
                      uint32_t startTime, uint32_t elapseTimeCnt, uint8_t battleNumber, uint32_t mapIndex);

    /**
     * @brief Add guild battle (simplified version)
     * 
     * @param srcGuild Source guild
     * @param destGuild Destination guild
     * @param startTime Battle start time
     * @param battleNumber Battle number
     * @param mapIndex Map index
     * @return Battle ID if successful, 0 if failed
     */
    uint32_t AddBattle(std::shared_ptr<CGuild> srcGuild, std::shared_ptr<CGuild> destGuild,
                      uint32_t startTime, uint8_t battleNumber, uint32_t mapIndex);

    /**
     * @brief Join guild to battle
     * 
     * Modern implementation of CGuildBattleController::JoinGuild method.
     * 
     * @param battleNumber Battle number
     * @param guildSerial Guild serial
     * @param characterSerial Character serial
     * @return true if joined successfully, false otherwise
     */
    bool JoinGuild(int battleNumber, uint32_t guildSerial, uint32_t characterSerial);

    /**
     * @brief Complete battle
     * 
     * Modern implementation of CGuildBattleController::AddComplete method.
     * 
     * @param result Battle result
     * @param mapId Map identifier
     * @param battleId Battle identifier
     * @param scheduleId Schedule identifier
     * @return true if completed successfully, false otherwise
     */
    bool CompleteBattle(uint8_t result, uint32_t mapId, uint32_t battleId, uint32_t scheduleId);

    /**
     * @brief Update guild ranking
     * 
     * Modern implementation of CGuildBattleController::UpdateRank method.
     * 
     * @param race Guild race
     * @param outData Output data buffer
     * @return true if updated successfully, false otherwise
     */
    bool UpdateRanking(uint8_t race, uint8_t* outData);

    /**
     * @brief Create guild battle rank table
     * 
     * Modern implementation of CGuildBattleController::PushCreateGuildBattleRankTable method.
     */
    void CreateGuildBattleRankTable();

    /**
     * @brief Get controller state
     * 
     * @return Current controller state
     */
    GuildBattleControllerState GetState() const { return m_state; }

    /**
     * @brief Check if controller is ready
     * 
     * @return true if ready, false otherwise
     */
    bool IsReady() const { return m_state == GuildBattleControllerState::Ready; }

    /**
     * @brief Check if controller is running
     * 
     * @return true if running, false otherwise
     */
    bool IsRunning() const { return m_state == GuildBattleControllerState::Running; }

    /**
     * @brief Get controller configuration
     * 
     * @return Current configuration
     */
    const GuildBattleControllerConfig& GetConfig() const { return m_config; }

    /**
     * @brief Set controller configuration
     * 
     * @param config New configuration
     */
    void SetConfig(const GuildBattleControllerConfig& config);

    /**
     * @brief Get controller statistics
     * 
     * @return Current statistics
     */
    const GuildBattleControllerStats& GetStatistics() const { return m_stats; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Get battle manager
     * 
     * @return Shared pointer to battle manager
     */
    std::shared_ptr<CGuildBattleManager> GetBattleManager() const { return m_battleManager; }

    /**
     * @brief Get ranking manager
     * 
     * @return Shared pointer to ranking manager
     */
    std::shared_ptr<CGuildRankingManager> GetRankingManager() const { return m_rankingManager; }

    /**
     * @brief Get scheduler
     * 
     * @return Shared pointer to scheduler
     */
    std::shared_ptr<CGuildBattleScheduler> GetScheduler() const { return m_scheduler; }

    /**
     * @brief Get controller report
     * 
     * @return Detailed controller status report
     */
    std::string GetControllerReport() const;

    // Singleton access (for legacy compatibility)
    static CGuildBattleController& Instance();
    static void SetInstance(std::unique_ptr<CGuildBattleController> instance);

protected:
    // Configuration
    GuildBattleControllerConfig m_config;
    
    // Core subsystems
    std::shared_ptr<CGuildBattleManager> m_battleManager;
    std::shared_ptr<CGuildRankingManager> m_rankingManager;
    std::shared_ptr<CGuildBattleScheduler> m_scheduler;
    
    // Statistics
    GuildBattleControllerStats m_stats;
    
    // State
    std::atomic<GuildBattleControllerState> m_state{GuildBattleControllerState::Uninitialized};
    
    // Timing
    std::chrono::steady_clock::time_point m_lastUpdate;
    std::chrono::steady_clock::time_point m_lastCleanup;
    
    // Synchronization
    mutable std::mutex m_configMutex;
    mutable std::mutex m_statsMutex;
    
    // Singleton instance
    static std::unique_ptr<CGuildBattleController> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Initialize subsystems
     * 
     * @return true if successful, false otherwise
     */
    bool InitializeSubsystems();

    /**
     * @brief Load configuration from INI
     * 
     * @param mapCount Output map count
     * @param today Output today value
     * @param todayDayId Output today day ID
     * @param tomorrow Output tomorrow value
     * @param tomorrowDayId Output tomorrow day ID
     * @return true if successful, false otherwise
     */
    bool LoadINI(uint32_t* mapCount, int* today, int* todayDayId, int* tomorrow, int* tomorrowDayId);

    /**
     * @brief Save configuration to INI
     * 
     * @return true if successful, false otherwise
     */
    bool SaveINI();

    /**
     * @brief Validate guild for battle
     * 
     * @param guild Guild to validate
     * @return true if valid, false otherwise
     */
    bool ValidateGuildForBattle(std::shared_ptr<CGuild> guild) const;

    /**
     * @brief Update subsystems
     * 
     * @param deltaTime Time elapsed since last update
     */
    void UpdateSubsystems(float deltaTime);

    /**
     * @brief Perform periodic cleanup
     */
    void PerformCleanup();

    /**
     * @brief Set controller state
     * 
     * @param state New state
     */
    void SetState(GuildBattleControllerState state);

    /**
     * @brief Log controller event
     * 
     * @param message Log message
     */
    void LogEvent(const std::string& message);
};

/**
 * @brief Guild Battle Controller Factory
 */
class CGuildBattleControllerFactory {
public:
    /**
     * @brief Create guild battle controller with default configuration
     * 
     * @return Unique pointer to controller
     */
    static std::unique_ptr<CGuildBattleController> CreateDefaultController();

    /**
     * @brief Create guild battle controller with custom configuration
     * 
     * @param config Controller configuration
     * @return Unique pointer to controller
     */
    static std::unique_ptr<CGuildBattleController> CreateController(const GuildBattleControllerConfig& config);
};

/**
 * @brief Guild battle controller utility functions
 */
namespace GuildBattleControllerUtils {
    std::string GuildBattleControllerResultToString(GuildBattleControllerResult result);
    std::string GuildBattleControllerStateToString(GuildBattleControllerState state);
    GuildBattleControllerResult StringToGuildBattleControllerResult(const std::string& resultStr);
    GuildBattleControllerState StringToGuildBattleControllerState(const std::string& stateStr);
}

} // namespace Guild
} // namespace NexusProtection
