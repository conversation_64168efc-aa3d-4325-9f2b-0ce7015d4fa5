/*
 * Function: ?IsolatedInitialize@BufferedTransformation@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405F77D0
 */

void __fastcall __noreturn CryptoPP::BufferedTransformation::IsolatedInitialize(CryptoPP::BufferedTransformation *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::NotImplemented v2; // [sp+20h] [bp-98h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@1
  unsigned __int8 v4; // [sp+A0h] [bp-18h]@1
  __int64 v5; // [sp+A8h] [bp-10h]@1

  v5 = -2i64;
  memset(&v4, 0, sizeof(v4));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &s,
    "BufferedTransformation: this object can't be reinitialized",
    v4);
  CryptoPP::NotImplemented::NotImplemented(&v2, &s);
  CxxThrowException_0((__int64)&v2, (__int64)&TI3_AVNotImplemented_CryptoPP__);
}
