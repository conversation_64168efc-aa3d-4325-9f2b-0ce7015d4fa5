/*
 * Function: j_??0?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@PEAPEAVTRC_AutoTrade@@@Z
 * Address: 0x140008B75
 */

void __fastcall std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_Ptr)
{
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    this,
    _Ptr);
}
