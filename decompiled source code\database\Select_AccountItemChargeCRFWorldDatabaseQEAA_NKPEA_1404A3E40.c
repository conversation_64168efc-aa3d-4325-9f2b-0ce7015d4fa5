/*
 * Function: ?Select_AccountItemCharge@CRFWorldDatabase@@QEAA_NKPEAEPEANPEAKPEA_K202PEAH@Z
 * Address: 0x1404A3E40
 */

char __fastcall CRFWorldDatabase::Select_AccountItemCharge(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *pbyType, long double *pdMoney, unsigned int *pdwItemCode_K, unsigned __int64 *pdwItemCode_D, unsigned int *pdwItemCode_U, char *pbyRace, unsigned int *pdwDBID, int *piTime)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v12; // ecx@5
  char result; // al@11
  __int64 v14; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@5
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@24
  SQLLEN v17; // [sp+38h] [bp-150h]@24
  __int16 v18; // [sp+44h] [bp-144h]@12
  char Dest; // [sp+60h] [bp-128h]@5
  int v20; // [sp+164h] [bp-24h]@4
  unsigned __int64 v21; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v22; // [sp+190h] [bp+8h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1
  long double *v24; // [sp+1A8h] [bp+20h]@1

  v24 = pdMoney;
  TargetValue = pbyType;
  v22 = this;
  v10 = &v14;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v10 = -*********;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v21 = (unsigned __int64)&v14 ^ _security_cookie;
  v20 = 0;
  if ( (unsigned __int8)*pbyRace == 255 )
  {
    sprintf(
      &Dest,
      "{ CALL pSelect_TrunkItemChargeByType_20070420( %u, %d ) }",
      dwAccountSerial,
      (unsigned __int8)*pbyType);
  }
  else
  {
    v12 = (unsigned __int8)*pbyType;
    LODWORD(SQLStmt) = (unsigned __int8)*pbyRace;
    sprintf(&Dest, "{ CALL pSelect_TrunkItemChargeByTypeRace_20070420( %u, %u, %u ) }", dwAccountSerial, v12);
  }
  if ( v22->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v22->vfptr, &Dest);
  if ( v22->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v22->vfptr) )
  {
    v18 = SQLExecDirectA_0(v22->m_hStmtSelect, &Dest, -3);
    if ( v18 && v18 != 1 )
    {
      if ( v18 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v22->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v22->vfptr, v18, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v22->vfptr, v18, v22->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v18 = SQLFetch_0(v22->m_hStmtSelect);
      if ( v18 && v18 != 1 )
      {
        if ( v18 != 100 )
        {
          SQLStmt = v22->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v22->vfptr, v18, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v22->vfptr, v18, v22->m_hStmtSelect);
        }
        if ( v22->m_hStmtSelect )
          SQLCloseCursor_0(v22->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 1u, 4, pdwDBID, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 2u, -28, TargetValue, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 3u, 8, v24, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 4u, 4, pdwItemCode_K, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 5u, -25, pdwItemCode_D, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 6u, 4, pdwItemCode_U, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 7u, -28, pbyRace, 0i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = 0i64;
        v18 = SQLGetData_0(v22->m_hStmtSelect, 8u, 4, piTime, 0i64, &v17);
        if ( v18 == 100 )
        {
          if ( v22->m_hStmtSelect )
            SQLCloseCursor_0(v22->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v22->m_hStmtSelect )
            SQLCloseCursor_0(v22->m_hStmtSelect);
          if ( v22->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v22->vfptr, "%s Success", &Dest);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v22->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
