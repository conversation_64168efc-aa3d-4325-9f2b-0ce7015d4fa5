/*
 * Function: j_??4?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14000491C
 */

CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::operator=(CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *this, CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__that)
{
  return CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::operator=(this, __that);
}
