/*
 * Function: j_??0?$_Vector_val@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAA@V?$allocator@PEAVCUnmannedTraderSortType@@@1@@Z
 * Address: 0x140002EA0
 */

void __fastcall std::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::allocator<CUnmannedTraderSortType *> _Al)
{
  std::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_val<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    this,
    _Al);
}
