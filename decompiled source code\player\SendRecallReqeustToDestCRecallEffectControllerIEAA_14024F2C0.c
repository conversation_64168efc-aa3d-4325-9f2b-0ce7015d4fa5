/*
 * Function: ?SendRecallReqeustToDest@CRecallEffectController@@IEAAXGPEAVCPlayer@@0@Z
 * Address: 0x14024F2C0
 */

void __fastcall CRecallEffectController::SendRecallReqeustToDest(CRecallEffectController *this, unsigned __int16 wRequestID, CPlayer *pkPerformer, CPlayer *pkDest)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  char Dest; // [sp+3Ah] [bp-5Eh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v11; // [sp+65h] [bp-33h]@4
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CPlayer *v13; // [sp+B8h] [bp+20h]@1

  v13 = pkDest;
  v4 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  *(_WORD *)szMsg = wRequestID;
  v6 = CPlayerDB::GetCharNameW(&pkPerformer->m_Param);
  strcpy_0(&Dest, v6);
  pbyType = 17;
  v11 = 33;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, szMsg, 0x13u);
}
