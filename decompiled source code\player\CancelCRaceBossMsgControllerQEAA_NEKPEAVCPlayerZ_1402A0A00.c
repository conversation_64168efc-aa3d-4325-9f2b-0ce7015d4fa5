/*
 * Function: ?Can<PERSON>@CRaceBossMsgController@@QEAA_NEKPEAVCPlayer@@@Z
 * Address: 0x1402A0A00
 */

char __fastcall CRaceBossMsgController::Can<PERSON>(CRaceBossMsgController *this, char ucRace, unsigned int dwMsgID, CPlayer *pkManager)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v7; // eax@9
  __int64 v8; // [sp+0h] [bp-58h]@1
  char *pwszName; // [sp+20h] [bp-38h]@8
  RACE_BOSS_MSG::CMsg *pkMsg; // [sp+38h] [bp-20h]@7
  int v11; // [sp+44h] [bp-14h]@7
  CRaceBossMsgController *v12; // [sp+60h] [bp+8h]@1
  char v13; // [sp+68h] [bp+10h]@1
  unsigned int dwMsgIDa; // [sp+70h] [bp+18h]@1
  CPlayer *v15; // [sp+78h] [bp+20h]@1

  v15 = pkManager;
  dwMsgIDa = dwMsgID;
  v13 = ucRace;
  v12 = this;
  v4 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkManager->m_byUserDgr == 2 && pkManager->m_bySubDgr != 3 )
  {
    pkMsg = 0i64;
    v11 = RACE_BOSS_MSG::CMsgListManager::Cancel(&v12->m_kManager, ucRace, dwMsgID, &pkMsg);
    if ( v11 )
    {
      pwszName = 0i64;
      CRaceBossMsgController::SendCancleInfomManager(v12, v15->m_ObjID.m_wIndex, v11, 0xFFFFFFFF, 0i64);
      result = 0;
    }
    else
    {
      v7 = RACE_BOSS_MSG::CMsg::GetSerial(pkMsg);
      CRaceBossMsgController::SendCancleInfomSender(v12, v7);
      pwszName = (char *)RACE_BOSS_MSG::CMsg::GetBossName(pkMsg);
      CRaceBossMsgController::SendCancleInfomManager(v12, v15->m_ObjID.m_wIndex, 0, dwMsgIDa, pwszName);
      CRaceBossMsgController::SendCancelWeb(v12, v13, pkMsg);
      RACE_BOSS_MSG::CMsgListManager::CleanUpCancel(&v12->m_kManager, v13, pkMsg);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
