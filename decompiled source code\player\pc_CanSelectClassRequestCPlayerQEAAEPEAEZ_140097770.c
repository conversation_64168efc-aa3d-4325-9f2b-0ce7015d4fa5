/*
 * Function: ?pc_CanSelectClassRequest@CPlayer@@QEAAEPEAE@Z
 * Address: 0x140097770
 */

char __fastcall CPlayer::pc_CanSelectClassRequest(CPlayer *this, char *pIsRealClassUp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned __int8 v6; // [sp+20h] [bp-18h]@6
  int j; // [sp+24h] [bp-14h]@6
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  char *v9; // [sp+48h] [bp+10h]@1

  v9 = pIsRealClassUp;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPlayerDB::IsClassChangeableLv(&v8->m_Param) )
  {
    v6 = -1;
    for ( j = 0; j < 3; ++j )
    {
      if ( !v8->m_Param.m_pClassHistory[j] )
      {
        v6 = j;
        break;
      }
    }
    if ( (signed int)v6 <= 1 )
    {
      if ( v9 )
        *v9 = v6 + 1 <= v8->m_pUserDB->m_AvatorData.dbAvator.m_byLastClassGrade;
      result = 0;
    }
    else
    {
      result = 3;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
