/*
 * Function: ?SellComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@0KKKK_JPEAVCLogFile@@@Z
 * Address: 0x140356AF0
 */

char __fastcall CUnmannedTraderUserInfo::SellComplete(CUnmannedTraderUserInfo *this, CPlayer *pkSellPlayer, CPlayer *pkBuyer, unsigned int dwOriPrice, unsigned int dwRealPrice, unsigned int dwTax, unsigned int dwRegistSerial, __int64 tResultTime, CLogFile *pkLogger)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v11; // rax@4
  unsigned __int16 v12; // ax@4
  char v13; // al@5
  CUnmannedTraderRegistItemInfo *v14; // rax@8
  CMoneySupplyMgr *v15; // rax@12
  char *v16; // rax@13
  int v17; // eax@14
  int v18; // ecx@14
  char *v19; // rax@16
  __int64 v20; // [sp+0h] [bp-168h]@1
  bool bDelete[8]; // [sp+20h] [bp-148h]@6
  char *strErrorCodePos; // [sp+28h] [bp-140h]@6
  char *szBuyerAccount; // [sp+30h] [bp-138h]@8
  unsigned int v24; // [sp+38h] [bp-130h]@13
  unsigned int dwLeftDalant[2]; // [sp+40h] [bp-128h]@13
  unsigned int dwLeftGold; // [sp+48h] [bp-120h]@13
  char *pszFileName; // [sp+50h] [bp-118h]@13
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+68h] [bp-100h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+88h] [bp-E0h]@4
  int v30; // [sp+90h] [bp-D8h]@8
  tm *v31; // [sp+98h] [bp-D0h]@13
  char DstBuf; // [sp+B0h] [bp-B8h]@14
  _base_fld *v33; // [sp+F8h] [bp-70h]@16
  char v34; // [sp+108h] [bp-60h]@5
  char v35; // [sp+109h] [bp-5Fh]@7
  char v36; // [sp+10Ah] [bp-5Eh]@16
  __int64 v37; // [sp+110h] [bp-58h]@4
  char *v38; // [sp+118h] [bp-50h]@8
  char *wszBuyerName; // [sp+120h] [bp-48h]@8
  int nLv; // [sp+128h] [bp-40h]@12
  int v41; // [sp+12Ch] [bp-3Ch]@12
  char *v42; // [sp+130h] [bp-38h]@13
  unsigned int v43; // [sp+138h] [bp-30h]@13
  unsigned int v44; // [sp+13Ch] [bp-2Ch]@13
  char *szBuyerID; // [sp+140h] [bp-28h]@13
  char *v46; // [sp+148h] [bp-20h]@16
  unsigned __int64 v47; // [sp+150h] [bp-18h]@4
  CUnmannedTraderUserInfo *v48; // [sp+170h] [bp+8h]@1
  CPlayer *v49; // [sp+178h] [bp+10h]@1
  CPlayer *v50; // [sp+180h] [bp+18h]@1
  unsigned int dwPrice; // [sp+188h] [bp+20h]@1

  dwPrice = dwOriPrice;
  v50 = pkBuyer;
  v49 = pkSellPlayer;
  v48 = this;
  v9 = &v20;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v37 = -2i64;
  v47 = (unsigned __int64)&v20 ^ _security_cookie;
  CUnmannedTraderUserInfo::Find(v48, &result, dwRegistSerial);
  v11 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
  v12 = CUnmannedTraderRegistItemInfo::GetItemSerial(v11);
  pItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v49->m_Param.m_dbInven.m_nListNum, v12);
  if ( pItem )
  {
    strErrorCodePos = "CUnmannedTraderUserInfo::SellComplete()";
    bDelete[0] = 1;
    if ( CPlayer::Emb_DelStorage(v49, 0, pItem->m_byStorageIndex, 0, 1, "CUnmannedTraderUserInfo::SellComplete()") )
    {
      CPlayer::AddDalant(v49, dwRealPrice, 1);
      v38 = v50->m_pUserDB->m_szAccountID;
      wszBuyerName = CPlayerDB::GetCharNameW(&v50->m_Param);
      v14 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
      szBuyerAccount = v38;
      strErrorCodePos = wszBuyerName;
      *(_QWORD *)bDelete = tResultTime;
      CUnmannedTraderRegistItemInfo::SellComplete(
        v14,
        dwPrice,
        v50->m_dwObjSerial,
        dwTax,
        tResultTime,
        wszBuyerName,
        v38);
      CUnmannedTraderUserInfo::CountRegistItem(v48);
      v30 = CPlayerDB::GetLevel(&v49->m_Param);
      if ( v30 == 30 || v30 == 40 || v30 == 50 || v30 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&v49->m_Param);
        v41 = CPlayerDB::GetRaceCode(&v49->m_Param);
        v15 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v15, v41, nLv, dwTax);
      }
      v42 = v49->m_szItemHistoryFileName;
      v43 = CPlayerDB::GetGold(&v49->m_Param);
      v44 = CPlayerDB::GetDalant(&v49->m_Param);
      szBuyerID = v50->m_pUserDB->m_szAccountID;
      v16 = CPlayerDB::GetCharNameA(&v50->m_Param);
      pszFileName = v42;
      dwLeftGold = v43;
      dwLeftDalant[0] = v44;
      v24 = dwTax;
      LODWORD(szBuyerAccount) = dwRealPrice;
      strErrorCodePos = (char *)pItem;
      *(_DWORD *)bDelete = dwRegistSerial;
      CMgrAvatorItemHistory::auto_trade_sell(
        &CPlayer::s_MgrItemHistory,
        v16,
        v50->m_dwObjSerial,
        szBuyerID,
        dwRegistSerial,
        pItem,
        dwRealPrice,
        dwTax,
        v44,
        v43,
        v42);
      v31 = localtime_13(&tResultTime);
      if ( v31 )
      {
        v17 = v31->tm_mon + 1;
        v18 = v31->tm_year;
        dwLeftDalant[0] = v31->tm_sec;
        v24 = v31->tm_min;
        LODWORD(szBuyerAccount) = v31->tm_hour;
        LODWORD(strErrorCodePos) = v31->tm_mday;
        *(_DWORD *)bDelete = v17;
        sprintf_s(&DstBuf, 0x40ui64, "%04d-%02d-%02d %02d:%02d:%02d", (unsigned int)(v18 + 1900));
      }
      else
      {
        sprintf_s(&DstBuf, 0x40ui64, "Invalid(%u)", tResultTime);
      }
      v33 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
      v46 = CPlayerDB::GetCharNameA(&v49->m_Param);
      v19 = CPlayerDB::GetCharNameA(&v50->m_Param);
      LODWORD(pszFileName) = dwTax;
      dwLeftGold = dwPrice;
      *(_QWORD *)dwLeftDalant = v46;
      v24 = v49->m_dwObjSerial;
      szBuyerAccount = v19;
      LODWORD(strErrorCodePos) = v50->m_dwObjSerial;
      *(_DWORD *)bDelete = dwRegistSerial;
      CLogFile::Write(
        &stru_1799C94F0,
        "%s %s >> sell (%u) user(%u)%s -> user(%u)%s price(%u) tax(%u)",
        v33->m_strCode,
        &DstBuf);
      LODWORD(strErrorCodePos) = CPlayerDB::GetDalant(&v49->m_Param);
      *(_DWORD *)bDelete = dwTax;
      CUnmannedTraderUserInfo::SendSellInfom(
        v48,
        v49->m_ObjID.m_wIndex,
        pItem->m_wSerial,
        dwRealPrice,
        dwTax,
        (unsigned int)strErrorCodePos);
      v36 = 90;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v13 = v36;
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "CUnmannedTraderUserInfo::SellComplete() : User(%u) pkSellPlayer->Emb_DelStorage() Fail!",
        v48->m_dwUserSerial);
      v35 = -105;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v13 = v35;
    }
  }
  else
  {
    v34 = 37;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v13 = v34;
  }
  return v13;
}
