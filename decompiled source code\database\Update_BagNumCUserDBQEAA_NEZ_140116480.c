/*
 * Function: ?Update_BagNum@CUserDB@@QEAA_NE@Z
 * Address: 0x140116480
 */

char __fastcall CUserDB::Update_BagNum(CUserDB *this, char bagnum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v5; // ecx@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@7
  CUserDB *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned __int8)bagnum <= 5 )
  {
    if ( v8->m_AvatorData.dbAvator.m_byBagNum < (signed int)(unsigned __int8)bagnum )
    {
      v8->m_AvatorData.dbAvator.m_byBagNum = bagnum;
      v8->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      v5 = v8->m_AvatorData.dbAvator.m_byBagNum;
      v7 = (unsigned __int8)bagnum;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_BagNum(CODE) m_AvatorData.dbAvator.m_byBagNum(%d) > pCon->byBagNum(%d) => failed ",
        v8->m_aszAvatorName,
        v5);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_BagNum(CODE) byBagNum (%d) => failed ",
      v8->m_aszAvatorName,
      (unsigned __int8)bagnum);
    result = 0;
  }
  return result;
}
