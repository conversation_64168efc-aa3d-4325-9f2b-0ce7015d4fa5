/*
 * Function: j_?_Buy@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140012251
 */

bool __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Buy(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned __int64 _Capacity)
{
  return std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Buy(this, _Capacity);
}
