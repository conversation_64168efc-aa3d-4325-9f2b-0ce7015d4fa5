/*
 * Function: ?SendMsg_GuildRoomEnterResult@CPlayer@@QEAAXEEEGPEAMJ@Z
 * Address: 0x1400E59E0
 */

void __fastcall CPlayer::SendMsg_GuildRoomEnterResult(CPlayer *this, char byRetCode, char bySubRetCode, char byMapIndex, unsigned __int16 wMapLayer, float *pPos, int tt)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v9; // ax@4
  __int64 v10; // [sp+0h] [bp-98h]@1
  _guildroom_enter_result_zocl v11; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v13; // [sp+65h] [bp-33h]@4
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4
  CPlayer *v15; // [sp+A0h] [bp+8h]@1

  v15 = this;
  v7 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v14 = (unsigned __int64)&v10 ^ _security_cookie;
  v11.byRetCode = byRetCode;
  v11.byRetSubCode = bySubRetCode;
  v11.byMapIndex = byMapIndex;
  v11.wMapLayerIndex = wMapLayer;
  FloatToShort(pPos, v11.sNewPos, 3);
  v11.restTime = tt;
  pbyType = 27;
  v13 = 105;
  v9 = _guildroom_enter_result_zocl::size(&v11);
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &v11.byRetCode, v9);
}
