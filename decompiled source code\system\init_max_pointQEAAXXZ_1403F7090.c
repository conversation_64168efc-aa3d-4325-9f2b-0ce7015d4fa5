/*
 * Function: ?init@_max_point@@QEAAXXZ
 * Address: 0x1403F7090
 */

void __fastcall _max_point::init(_max_point *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-28h]@1
  _max_point *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = _max_point::size(v5);
  memset_0(v5, 0, v3);
}
