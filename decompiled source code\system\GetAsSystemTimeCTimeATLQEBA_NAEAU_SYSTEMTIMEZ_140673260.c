/*
 * Function: ?GetAsSystemTime@CTime@ATL@@QEBA_NAEAU_SYSTEMTIME@@@Z
 * Address: 0x140673260
 */

char __fastcall ATL::CTime::GetAsSystemTime(ATL::CTime *this, _SYSTEMTIME *timeDest)
{
  char result; // al@2
  tm ptm; // [sp+20h] [bp-38h]@1
  tm *v4; // [sp+48h] [bp-10h]@1
  _SYSTEMTIME *v5; // [sp+68h] [bp+10h]@1

  v5 = timeDest;
  v4 = ATL::CTime::GetLocalTm(this, &ptm);
  if ( v4 )
  {
    v5->wYear = v4->tm_year + 1900;
    v5->wMonth = v4->tm_mon + 1;
    v5->wDayOfWeek = v4->tm_wday;
    v5->wDay = v4->tm_mday;
    v5->wHour = v4->tm_hour;
    v5->wMinute = v4->tm_min;
    v5->wSecond = v4->tm_sec;
    v5->wMilliseconds = 0;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
