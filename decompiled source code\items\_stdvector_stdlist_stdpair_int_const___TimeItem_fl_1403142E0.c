/*
 * Function: _std::vector_std::list_std::pair_int_const___TimeItem_fld_const_____ptr64__std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const___TimeItem_fld_const_____ptr64__std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64_____::_Iterator_0_____::_Insert_n_::_1_::catch$1
 * Address: 0x1403142E0
 */

void __fastcall __noreturn std::vector_std::list_std::pair_int_const___TimeItem_fld_const_____ptr64__std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const___TimeItem_fld_const_____ptr64__std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64_____::_Iterator_0_____::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>>::_Destroy(
    *(std::vector<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0>,std::allocator<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> > > **)(a2 + 176),
    (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *)(*(_QWORD *)(*(_QWORD *)(a2 + 184) + 16i64) + 24i64 * *(_QWORD *)(a2 + 192)),
    (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *)(*(_QWORD *)(*(_QWORD *)(a2 + 176) + 24i64) + 24i64 * *(_QWORD *)(a2 + 192)));
  CxxThrowException_0(0i64, 0i64);
}
