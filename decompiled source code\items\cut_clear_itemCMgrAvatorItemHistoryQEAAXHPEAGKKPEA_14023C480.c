/*
 * Function: ?cut_clear_item@CMgrAvatorItemHistory@@QEAAXHPEAGKKPEAD@Z
 * Address: 0x14023C480
 */

void __fastcall CMgrAvatorItemHistory::cut_clear_item(CMgrAvatorItemHistory *this, int n, unsigned __int16 *pwCuttingResBuffer, unsigned int dwAddGold, unsigned int dwNewGold, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *v9; // [sp+20h] [bp-28h]@4
  char *v10; // [sp+28h] [bp-20h]@4
  int na; // [sp+30h] [bp-18h]@4
  _base_fld *v12; // [sp+38h] [bp-10h]@7
  CMgrAvatorItemHistory *v13; // [sp+50h] [bp+8h]@1
  unsigned __int16 *v14; // [sp+60h] [bp+18h]@1

  v14 = pwCuttingResBuffer;
  v13 = this;
  v6 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  sData[0] = 0;
  v10 = v13->m_szCurTime;
  v9 = v13->m_szCurDate;
  sprintf(sBuf, "CUT SELL: rev(G:%u) $G:%u [%s %s]\r\n", dwAddGold, dwNewGold);
  strcat_0(sData, sBuf);
  for ( na = 0; na < GetMaxResKind(); ++na )
  {
    if ( (signed int)v14[na] > 0 )
    {
      v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, na);
      sprintf(sBuf, "\t- %s_%d\r\n", v12->m_strCode, v14[na]);
      strcat_0(sData, sBuf);
    }
  }
  CMgrAvatorItemHistory::WriteFile(v13, pszFileName, sData);
}
