/*
 * Function: ?InitProcess@PatriarchElectProcessor@@QEAA_NXZ
 * Address: 0x1402BA630
 */

char __fastcall PatriarchElectProcessor::InitProcess(PatriarchElectProcessor *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  ElectProcessor::ProcessorType v5; // [sp+20h] [bp-18h]@7
  PatriarchElectProcessor *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->_eProcessType < 6 )
  {
    if ( !v6->_kProcessor[v6->_eProcessType] )
      return 0;
    v6->_kRunningProcessor = v6->_kProcessor[v6->_eProcessType];
    v5 = v6->_eProcessType;
    if ( v5 )
    {
      if ( v5 == 2 )
      {
        if ( !(unsigned __int8)((int (__fastcall *)(ElectProcessor *))v6->_kRunningProcessor->vfptr->Initialize)(v6->_kRunningProcessor) )
          return 0;
      }
      else if ( v5 == 3 )
      {
        ((void (__fastcall *)(ElectProcessor *, signed __int64, _QWORD, _QWORD))v6->_kRunningProcessor->vfptr->Doit)(
          v6->_kRunningProcessor,
          10i64,
          0i64,
          0i64);
      }
    }
    else
    {
      if ( !(unsigned __int8)((int (__fastcall *)(ElectProcessor *))v6->_kRunningProcessor->vfptr->Initialize)(v6->_kRunningProcessor) )
        return 0;
      ((void (__fastcall *)(ElectProcessor *, _QWORD, _QWORD, _QWORD))v6->_kRunningProcessor->vfptr->Doit)(
        v6->_kRunningProcessor,
        0i64,
        0i64,
        0i64);
    }
  }
  v6->_bInitProce = 1;
  return 1;
}
