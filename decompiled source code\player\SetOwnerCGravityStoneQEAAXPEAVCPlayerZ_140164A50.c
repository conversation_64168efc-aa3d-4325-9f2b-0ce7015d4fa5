/*
 * Function: ?SetOwner@CGravityStone@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140164A50
 */

void __fastcall CGravityStone::SetOwner(CGravityStone *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CGravityStone *v5; // [sp+30h] [bp+8h]@1
  CPlayer *v6; // [sp+38h] [bp+10h]@1

  v6 = pkPlayer;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer )
  {
    v5->m_dwTakeLimitTime = GetLoopTime() + 120000;
    v5->m_pkOwner = v6;
    CPlayer::TakeGravityStone(v5->m_pkOwner);
  }
}
