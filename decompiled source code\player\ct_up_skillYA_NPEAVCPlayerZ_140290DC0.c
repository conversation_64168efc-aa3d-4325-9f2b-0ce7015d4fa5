/*
 * Function: ?ct_up_skill@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290DC0
 */

bool __fastcall ct_up_skill(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szTran; // [sp+28h] [bp-50h]@7
  unsigned __int64 v7; // [sp+60h] [bp-18h]@4
  CPlayer *v8; // [sp+80h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v8 )
  {
    if ( s_nWordCount < 2 )
    {
      result = 0;
    }
    else
    {
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      v4 = atoi(s_pwszDstCheat[1]);
      result = CPlayer::dev_up_skill(v8, &szTran, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
