/*
 * Function: ?Init@_DELAY_PROCESS@@QEAA_NKK@Z
 * Address: 0x14007DCD0
 */

char __fastcall _DELAY_PROCESS::Init(_DELAY_PROCESS *this, unsigned int dwObjectNum, unsigned int dwTerm)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int *v6; // rax@8
  unsigned int *v7; // rax@8
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned int *v9; // [sp+20h] [bp-28h]@8
  unsigned int *v10; // [sp+28h] [bp-20h]@8
  __int64 v11; // [sp+30h] [bp-18h]@8
  __int64 v12; // [sp+38h] [bp-10h]@8
  _DELAY_PROCESS *v13; // [sp+50h] [bp+8h]@1
  unsigned int dwMaxBufNum; // [sp+58h] [bp+10h]@1

  dwMaxBufNum = dwObjectNum;
  v13 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v13->m_pdwPushTime )
  {
    result = 0;
  }
  else if ( dwObjectNum )
  {
    v13->m_dwObjectNum = dwObjectNum;
    v13->m_dwTerm = dwTerm;
    v11 = dwObjectNum;
    v6 = (unsigned int *)operator new[](saturated_mul(4ui64, dwObjectNum));
    v9 = v6;
    v13->m_pdwPushTime = v6;
    v12 = dwMaxBufNum;
    v7 = (unsigned int *)operator new[](saturated_mul(4ui64, dwMaxBufNum));
    v10 = v7;
    v13->m_pdwPushSerial = v7;
    CNetIndexList::SetList(&v13->m_list, dwMaxBufNum);
    memset_0(v13->m_pdwPushSerial, -1, 4i64 * dwMaxBufNum);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
