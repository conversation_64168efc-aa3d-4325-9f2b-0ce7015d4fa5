/*
 * Function: ?_LootItem_Std@CMonster@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140144240
 */

char __usercall CMonster::_LootItem_Std@<al>(CMonster *this@<rcx>, CPlayer *pOwner@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  float v6; // xmm0_4@17
  float v7; // xmm0_4@22
  float v8; // xmm0_4@22
  float v9; // xmm1_4@22
  float v10; // xmm0_4@22
  float v11; // xmm0_4@28
  int v12; // eax@41
  signed int v13; // eax@42
  float v14; // xmm0_4@42
  signed int v15; // eax@42
  int v16; // eax@43
  float v17; // xmm0_4@43
  __int64 v18; // [sp+0h] [bp-138h]@1
  CMapData *pMap; // [sp+30h] [bp-108h]@61
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-100h]@61
  float *pStdPos; // [sp+40h] [bp-F8h]@61
  bool bHide; // [sp+48h] [bp-F0h]@61
  int v23; // [sp+50h] [bp-E8h]@6
  int v24; // [sp+54h] [bp-E4h]@6
  int v25; // [sp+58h] [bp-E0h]@6
  bool v26; // [sp+5Ch] [bp-DCh]@6
  double v27; // [sp+60h] [bp-D8h]@8
  bool v28; // [sp+68h] [bp-D0h]@8
  CPlayer *pOwnera; // [sp+70h] [bp-C8h]@8
  unsigned int dwPartyBossSerial; // [sp+78h] [bp-C0h]@8
  char v31; // [sp+7Ch] [bp-BCh]@11
  int n; // [sp+80h] [bp-B8h]@11
  _base_fld *v33; // [sp+88h] [bp-B0h]@14
  float v34; // [sp+90h] [bp-A8h]@15
  int v35; // [sp+94h] [bp-A4h]@31
  int v36; // [sp+98h] [bp-A0h]@31
  int j; // [sp+9Ch] [bp-9Ch]@33
  int v38; // [sp+A0h] [bp-98h]@37
  unsigned int v39; // [sp+A4h] [bp-94h]@37
  unsigned int v40; // [sp+A8h] [bp-90h]@37
  _base_fld *v41; // [sp+B0h] [bp-88h]@37
  int iDiffLevel; // [sp+B8h] [bp-80h]@38
  unsigned int v43; // [sp+BCh] [bp-7Ch]@48
  char v44; // [sp+C0h] [bp-78h]@52
  __int64 v45; // [sp+C8h] [bp-70h]@52
  __int64 v46; // [sp+D0h] [bp-68h]@52
  int k; // [sp+D8h] [bp-60h]@52
  int v48; // [sp+DCh] [bp-5Ch]@56
  _STORAGE_LIST::_db_con *pItem; // [sp+E0h] [bp-58h]@60
  _base_fld *v50; // [sp+E8h] [bp-50h]@63
  char v51; // [sp+F0h] [bp-48h]@64
  CGameStatistics::_DAY *v52; // [sp+F8h] [bp-40h]@69
  int v53; // [sp+100h] [bp-38h]@37
  int v54; // [sp+104h] [bp-34h]@41
  CGameObjectVtbl *v55; // [sp+108h] [bp-30h]@41
  float v56; // [sp+110h] [bp-28h]@42
  float v57; // [sp+114h] [bp-24h]@42
  int v58; // [sp+118h] [bp-20h]@43
  CGameObjectVtbl *v59; // [sp+120h] [bp-18h]@43
  float v60; // [sp+128h] [bp-10h]@43
  CMonster *v61; // [sp+140h] [bp+8h]@1
  CPlayer *v62; // [sp+148h] [bp+10h]@1

  v62 = pOwner;
  v61 = this;
  v3 = &v18;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v61->m_bStdItemLoot )
  {
    v23 = CMonster::s_idxMonsterLoot[v61->m_pRecordSet->m_dwIndex].nStartRecIndex;
    v24 = CMonster::s_idxMonsterLoot[v61->m_pRecordSet->m_dwIndex].nEndRecIndex;
    v25 = 0;
    v26 = pOwner->m_bLootFree;
    if ( v23 == -1 )
    {
      result = 0;
    }
    else
    {
      TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, pOwner->m_id.wIndex);
      v27 = a3;
      v28 = 0;
      pOwnera = v62;
      dwPartyBossSerial = -1;
      if ( CPartyPlayer::IsPartyMode(v62->m_pPartyMgr) )
      {
        dwPartyBossSerial = v62->m_pPartyMgr->m_pPartyBoss->m_id.dwSerial;
        pOwnera = CPartyPlayer::GetLootAuthor(v62->m_pPartyMgr);
        if ( !pOwnera )
        {
          v28 = 1;
          pOwnera = v62;
        }
      }
      v31 = 0;
      for ( n = v23; n <= v24; ++n )
      {
        v33 = CRecordData::GetRecord(&stru_1799C6638, n);
        if ( v33 )
        {
          v34 = 0.0;
          if ( !CPlayer::IsApplyPcbangPrimium(v62) || CMonster::IsBossMonster(v61) )
          {
            v6 = ITEM_ROOT_RATE;
            v34 = ITEM_ROOT_RATE;
          }
          else
          {
            v6 = PCBANG_PRIMIUM_FAVOR::ITEM_DROP;
            v34 = PCBANG_PRIMIUM_FAVOR::ITEM_DROP;
          }
          if ( !CMonster::IsBossMonster(v61) )
          {
            if ( CPlayer::IsRidingUnit(v62) )
              _effect_parameter::SetLock(&v62->m_EP, 0);
            _effect_parameter::GetEff_Have(&v62->m_EP, 8);
            v7 = v34 + (float)(v6 - 1.0);
            v34 = v7;
            _effect_parameter::GetEff_Rate(&v62->m_EP, 36);
            v8 = v34 + (float)(v7 - 1.0);
            v34 = v8;
            _effect_parameter::GetEff_Have(&v62->m_EP, 73);
            v9 = v34 + (float)(v8 - 1.0);
            v10 = v9;
            v34 = v9;
            if ( CPlayer::IsApplyPcbangPrimium(v62) )
            {
              _effect_parameter::GetEff_Have(&v62->m_EP, 74);
              if ( v9 > PCBANG_PRIMIUM_FAVOR::ITEM_DROP )
              {
                _effect_parameter::GetEff_Have(&v62->m_EP, 74);
                v10 = v34 + (float)(v9 - PCBANG_PRIMIUM_FAVOR::ITEM_DROP);
                v34 = v34 + (float)(v9 - PCBANG_PRIMIUM_FAVOR::ITEM_DROP);
              }
              _effect_parameter::GetEff_Have(&v62->m_EP, 75);
              if ( v10 > PCBANG_PRIMIUM_FAVOR::ITEM_DROP )
              {
                _effect_parameter::GetEff_Have(&v62->m_EP, 75);
                v34 = v34 + (float)(v10 - PCBANG_PRIMIUM_FAVOR::ITEM_DROP);
              }
            }
            else
            {
              _effect_parameter::GetEff_Have(&v62->m_EP, 74);
              v34 = v34 + (float)(v9 - 1.0);
              v11 = v34;
              _effect_parameter::GetEff_Have(&v62->m_EP, 75);
              v34 = v34 + (float)(v11 - 1.0);
            }
            if ( CPlayer::IsRidingUnit(v62) )
              _effect_parameter::SetLock(&v62->m_EP, 1);
          }
          v35 = 0;
          v36 = 0;
          v35 = (signed int)floor((float)((float)*(signed int *)&v33[1].m_strCode[4] * v34) * v27);
          v36 = (signed int)floor((float)((float)*(signed int *)&v33[1].m_strCode[4] * v34) * v27 * 10.0) % 10;
          if ( v36 >= 5 )
            ++v35;
          for ( j = 0; j < v35; ++j )
          {
            if ( v26 )
              goto LABEL_77;
            v38 = rand();
            v53 = v38 << 16;
            v39 = rand() + v53;
            v40 = 0;
            v41 = 0i64;
            if ( v61->m_pRecordSet )
            {
              v41 = v61->m_pRecordSet;
              iDiffLevel = 11;
              if ( v62 )
              {
                if ( CPlayer::IsRidingUnit(v62) )
                  _effect_parameter::SetLock(&v62->m_EP, 0);
                v54 = ((int (__fastcall *)(CMonster *))v61->vfptr->GetLevel)(v61);
                v55 = v62->vfptr;
                v12 = ((int (__fastcall *)(CPlayer *))v55->GetLevel)(v62);
                if ( v54 <= v12 )
                {
                  v58 = ((int (__fastcall *)(CMonster *))v61->vfptr->GetLevel)(v61);
                  v59 = v62->vfptr;
                  v16 = ((int (__fastcall *)(CPlayer *))v59->GetLevel)(v62);
                  v17 = (float)(v58 - v16);
                  v60 = (float)(v58 - v16);
                  _effect_parameter::GetEff_Have(&v62->m_EP, 52);
                  iDiffLevel = (signed int)ffloor(v60 + v17);
                }
                else
                {
                  v13 = ((int (__fastcall *)(CMonster *))v61->vfptr->GetLevel)(v61);
                  v14 = (float)v13;
                  v56 = (float)v13;
                  _effect_parameter::GetEff_Have(&v62->m_EP, 52);
                  v57 = v56 - v14;
                  v15 = ((int (__fastcall *)(CPlayer *))v62->vfptr->GetLevel)(v62);
                  iDiffLevel = (signed int)ffloor(v57 - (float)v15);
                }
                if ( CPlayer::IsRidingUnit(v62) )
                  _effect_parameter::SetLock(&v62->m_EP, 1);
              }
              v40 = v33[1].m_dwIndex;
              if ( *(_DWORD *)&v41[4].m_strCode[8] != -1 && *(_DWORD *)&v41[4].m_strCode[8] )
              {
                iDiffLevel -= *(_DWORD *)&v41[4].m_strCode[8];
                v43 = MonsterSetInfoData::GetMonsterDropRate(&g_MonsterSetInfoData, iDiffLevel);
                if ( !v43 )
                  v43 = 1;
                v40 = v43 * (v40 / 0x64);
              }
              if ( v39 < v40 )
              {
LABEL_77:
                v44 = 0;
                v45 = 0i64;
                v46 = 0i64;
                for ( k = 0; k < 10; ++k )
                {
                  if ( *(_DWORD *)&v33[1].m_strCode[8] > 0 )
                  {
                    v48 = rand() % *(_DWORD *)&v33[1].m_strCode[8];
                    v46 = *(_QWORD *)(unk_1799C66F0 + 8i64 * v33->m_dwIndex) + 8i64 * v48;
                    if ( *(_DWORD *)(v46 + 4) )
                    {
                      v44 = 1;
                      break;
                    }
                  }
                }
                if ( v44 )
                {
                  pItem = MakeLoot(*(_BYTE *)v46, *(_WORD *)(v46 + 2));
                  if ( pItem )
                  {
                    bHide = 0;
                    pStdPos = v61->m_fCurPos;
                    wLayerIndex = v61->m_wMapLayerIndex;
                    pMap = v61->m_pCurMap;
                    if ( CreateItemBox(
                           pItem,
                           pOwnera,
                           dwPartyBossSerial,
                           v28,
                           (CCharacter *)&v61->vfptr,
                           0,
                           pMap,
                           wLayerIndex,
                           v61->m_fCurPos,
                           0) )
                    {
                      v31 = 1;
                      if ( v61->m_pMonRec->m_bMonsterCondition == 1 )
                      {
                        v50 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + *(_BYTE *)v46, *(_WORD *)(v46 + 2));
                        CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t LootItem : %s", v50->m_strCode);
                      }
                      v51 = 1;
                      if ( CMainThread::IsReleaseServiceMode(&g_Main) && v62->m_byUserDgr )
                        v51 = 0;
                      if ( v51 && (signed int)*(_BYTE *)v46 < 8 )
                      {
                        v52 = CGameStatistics::CurWriteData(&g_GameStatistics);
                        if ( (signed int)(unsigned __int8)GetItemGrade(*(_BYTE *)v46, *(_WORD *)(v46 + 2)) <= 0 )
                          ++v52->dwDropStdItem_Evt;
                        else
                          ++v52->dwDropRareItem_Evt;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      result = v31;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
