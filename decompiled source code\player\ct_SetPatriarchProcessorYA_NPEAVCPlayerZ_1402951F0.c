/*
 * Function: ?ct_SetPatriarchProcessor@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402951F0
 */

bool __fastcall ct_SetPatriarchProcessor(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  PatriarchElectProcessor *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-38h]@1
  ElectProcessor::ProcessorType eProc; // [sp+20h] [bp-18h]@10
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    if ( atoi(s_pwszDstCheat[0]) >= 0 && atoi(s_pwszDstCheat[0]) < 6 )
    {
      eProc = atoi(s_pwszDstCheat[0]);
      v4 = PatriarchElectProcessor::Instance();
      result = PatriarchElectProcessor::ForceChangeProcessor(v4, eProc);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
