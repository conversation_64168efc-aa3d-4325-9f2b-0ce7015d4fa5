/*
 * Function: ??0?$CArray@U_State@CLuaLooting_Novus_Item@@@US@@QEAA@XZ
 * Address: 0x140405D90
 */

void __fastcall US::CArray<CLuaLooting_Novus_Item::_State>::CArray<CLuaLooting_Novus_Item::_State>(US::CArray<CLuaLooting_Novus_Item::_State> *this)
{
  this->vfptr = (US::CArray<CLuaLooting_Novus_Item::_State>Vtbl *)&US::CArray<CLuaLooting_Novus_Item::_State>::`vftable';
  this->m_bAlloc = 0;
  this->m_pBuffer = 0i64;
  this->m_dwCount = 0;
}
