/*
 * Function: ??RNewPrimeTable@CryptoPP@@QEBAPEAV?$vector@GV?$allocator@G@std@@@std@@XZ
 * Address: 0x14064D5D0
 */

__int64 CryptoPP::NewPrimeTable::operator()()
{
  __int64 v0; // rax@2
  __int64 v1; // rax@4
  _WORD *v2; // rax@8
  __int64 v3; // rax@13
  __int64 v5; // [sp+28h] [bp-70h]@4
  unsigned int v6; // [sp+30h] [bp-68h]@4
  char v7; // [sp+38h] [bp-60h]@4
  unsigned int i; // [sp+40h] [bp-58h]@4
  unsigned int j; // [sp+44h] [bp-54h]@6
  __int64 v10; // [sp+48h] [bp-50h]@4
  void *v11; // [sp+50h] [bp-48h]@1
  __int16 v12; // [sp+58h] [bp-40h]@4
  __int16 v13; // [sp+5Ah] [bp-3Eh]@11
  __int64 v14; // [sp+60h] [bp-38h]@11
  int v15; // [sp+68h] [bp-30h]@11
  __int64 v16; // [sp+70h] [bp-28h]@13
  __int64 v17; // [sp+78h] [bp-20h]@1
  __int64 v18; // [sp+80h] [bp-18h]@2
  unsigned int v19; // [sp+88h] [bp-10h]@8

  v17 = -2i64;
  v11 = operator new(0x28ui64);
  if ( v11 )
  {
    LODWORD(v0) = std::vector<unsigned short,std::allocator<unsigned short>>::vector<unsigned short,std::allocator<unsigned short>>(v11);
    v18 = v0;
  }
  else
  {
    v18 = 0i64;
  }
  v10 = v18;
  std::auto_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>::auto_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>(
    &v7,
    v18);
  LODWORD(v1) = std::auto_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>::operator*(&v7);
  v5 = v1;
  std::vector<unsigned short,std::allocator<unsigned short>>::reserve(v1, 3511i64);
  v12 = 2;
  std::vector<unsigned short,std::allocator<unsigned short>>::push_back(v5, &v12);
  v6 = 1;
  for ( i = 3; i <= 0x7FCFui64; i += 2 )
  {
    for ( j = 1; j < v6; ++j )
    {
      LODWORD(v2) = std::vector<unsigned short,std::allocator<unsigned short>>::operator[](v5, j);
      v19 = *v2;
      if ( !(i % v19) )
        break;
    }
    if ( j == v6 )
    {
      v13 = i;
      std::vector<unsigned short,std::allocator<unsigned short>>::push_back(v5, &v13);
      v14 = std::vector<unsigned short,std::allocator<unsigned short>>::size(v5);
      v15 = 54;
      v6 = CryptoPP::UnsignedMin<unsigned int,unsigned __int64>(&v15, &v14);
    }
  }
  LODWORD(v3) = std::auto_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>::release(&v7);
  v16 = v3;
  std::auto_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>::~auto_ptr<std::vector<unsigned short,std::allocator<unsigned short>>>(&v7);
  return v16;
}
