/*
 * Function: ?CheckEventSetRespawn@CMonsterEventSet@@QEAAXXZ
 * Address: 0x1402A8A90
 */

void __fastcall CMonsterEventSet::CheckEventSetRespawn(CMonsterEventSet *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@39
  __int64 v4; // [sp+0h] [bp-D8h]@1
  float *pNewPos; // [sp+20h] [bp-B8h]@39
  bool bRobExp; // [sp+28h] [bp-B0h]@39
  bool bRewardExp; // [sp+30h] [bp-A8h]@39
  bool bDungeon; // [sp+38h] [bp-A0h]@39
  bool bWithoutFail; // [sp+40h] [bp-98h]@39
  bool bApplyRopExpField; // [sp+48h] [bp-90h]@39
  DWORD v11; // [sp+50h] [bp-88h]@4
  int j; // [sp+54h] [bp-84h]@4
  _event_set *pEventSet; // [sp+58h] [bp-80h]@7
  char v14; // [sp+60h] [bp-78h]@8
  int k; // [sp+64h] [bp-74h]@8
  _event_set::_monster_set *v16; // [sp+68h] [bp-70h]@11
  int l; // [sp+70h] [bp-68h]@17
  _event_set::_monster_set::_state::_mon *v18; // [sp+78h] [bp-60h]@20
  int nRange; // [sp+80h] [bp-58h]@30
  int m; // [sp+84h] [bp-54h]@30
  _event_set::_monster_set::_state::_mon *v21; // [sp+88h] [bp-50h]@33
  float Dst; // [sp+98h] [bp-40h]@37
  int v23; // [sp+9Ch] [bp-3Ch]@37
  int v24; // [sp+A0h] [bp-38h]@37
  CMapData *v25; // [sp+B8h] [bp-20h]@37
  CMonster *v26; // [sp+C0h] [bp-18h]@39
  int v27; // [sp+C8h] [bp-10h]@28
  int v28; // [sp+CCh] [bp-Ch]@36
  CMonsterEventSet *v29; // [sp+E0h] [bp+8h]@1

  v29 = this;
  v1 = &v4;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = timeGetTime();
  for ( j = 0; j < 10; ++j )
  {
    pEventSet = &v29->m_EventSet[j];
    if ( v29->m_EventSet[j].m_bOper )
    {
      v14 = 0;
      for ( k = 0; k < 10; ++k )
      {
        v16 = &pEventSet->m_MonSet[k];
        if ( pEventSet->m_MonSet[k].bIsSet && v16->m_State.bOper )
        {
          if ( !v14 )
            v14 = 1;
          if ( v16->dwDuring && v11 - v16->m_State.dwStartTime >= v16->dwDuring )
          {
            for ( l = 0; l < v16->m_State.nRespawnNum; ++l )
            {
              v18 = &v16->m_State.MonInfo[l];
              if ( v18->pMon && v18->pMon->m_bLive && v18->pMon->m_dwObjSerial == v18->dwSerial )
                CMonster::Destroy(v18->pMon, 1, 0i64);
            }
            _event_set::_monster_set::_state::init(&v16->m_State);
            CLogFile::Write(&stru_1799C95A8, "Stop Event Monster Set (by during) >> %s", v16->pMonsterFld->m_strCode);
          }
          else if ( v11 - v16->m_State.dwLastUpdateTime >= v16->dwRegenTerm )
          {
            if ( 20 * v16->m_State.nRespawnNum >= 500 )
              v27 = 500;
            else
              v27 = 20 * v16->m_State.nRespawnNum;
            nRange = v27;
            for ( m = 0; m < v16->m_State.nRespawnNum; ++m )
            {
              v21 = &v16->m_State.MonInfo[m];
              if ( !v21->pMon || !v21->pMon->m_bLive || v21->pMon->m_dwObjSerial != v21->dwSerial )
              {
                v28 = v16->byRegenProb;
                if ( v28 >= rand() % 100 )
                {
                  Dst = 0.0;
                  v23 = 0;
                  v24 = 0;
                  v25 = v16->pMap;
                  if ( !CMapData::GetRandPosVirtualDumExcludeStdRange(v25, v16->fPos, nRange, 0, &Dst) )
                    memcpy_0(&Dst, v16->fPos, 0xCui64);
                  v3 = v16->pMonsterFld->m_strCode;
                  bApplyRopExpField = 0;
                  bWithoutFail = 0;
                  bDungeon = 0;
                  bRewardExp = 1;
                  bRobExp = 0;
                  pNewPos = 0i64;
                  v26 = CreateRepMonster(v16->pMap, 0, &Dst, v3, 0i64, 0, 1, 0, 0, 0);
                  if ( v26 )
                  {
                    v21->pMon = v26;
                    v21->dwSerial = v26->m_dwObjSerial;
                    v21->pMonFld = v16->pMonsterFld;
                    CMonster::DisableStdItemLoot(v26);
                    CMonster::LinkEventSet(v26, pEventSet);
                  }
                  else
                  {
                    v21->pMon = 0i64;
                  }
                }
              }
            }
            v16->m_State.dwLastUpdateTime = v11;
          }
        }
      }
      if ( !v14 )
      {
        pEventSet->m_bOper = 0;
        CLogFile::Write(&stru_1799C95A8, "Stop Event Set (by during) >> %s", pEventSet);
      }
    }
  }
  if ( CMonsterEventSet::IsINIFileChanged(v29, ".\\Initialize\\EventSetLooting.ini", v29->m_ftLootingWrite)
    && !CMonsterEventSet::LoadEventSetLooting(v29) )
  {
    CLogFile::Write(
      &stru_1799C95A8,
      "Reload Event set looting INI file fail >> %s",
      ".\\Initialize\\EventSetLooting.ini");
  }
}
