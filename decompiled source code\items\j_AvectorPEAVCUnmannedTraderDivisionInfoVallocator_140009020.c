/*
 * Function: j_??A?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderDivisionInfo@@_K@Z
 * Address: 0x140009020
 */

CUnmannedTraderDivisionInfo **__fastcall std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator[](std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator[](
           this,
           _Pos);
}
