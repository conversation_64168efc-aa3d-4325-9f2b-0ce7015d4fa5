/*
 * Function: ??0?$PK_FinalTemplate@V?$DL_VerifierImpl@U?$DL_SignatureSchemeOptions@UDSA@CryptoPP@@UDL_Keys_DSA@2@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@CryptoPP@@@CryptoPP@@@CryptoPP@@QEAA@AEBVAsymmetricAlgorithm@1@@Z
 * Address: 0x140561880
 */

__int64 __fastcall CryptoPP::PK_FinalTemplate<CryptoPP::DL_VerifierImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>>::PK_FinalTemplate<CryptoPP::DL_VerifierImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>>(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1
  __int64 v3; // ST20_8@1
  __int64 v4; // rax@1
  __int64 v6; // [sp+50h] [bp+8h]@1
  __int64 v7; // [sp+58h] [bp+10h]@1

  v7 = a2;
  v6 = a1;
  CryptoPP::DL_VerifierImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>::DL_VerifierImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>();
  LODWORD(v2) = CryptoPP::DL_ObjectImplBase<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>>::AccessKey(v6);
  v3 = v2;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v7 + 32i64))(v7);
  (*(void (__fastcall **)(signed __int64, __int64))(*(_QWORD *)(v3 + *(_DWORD *)(*(_QWORD *)(v3 + 16) + 4i64) + 16)
                                                  + 16i64))(
    v3 + *(_DWORD *)(*(_QWORD *)(v3 + 16) + 4i64) + 16,
    v4);
  return v6;
}
