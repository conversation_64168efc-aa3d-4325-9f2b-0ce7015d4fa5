/*
 * Function: j_??$_Iter_random@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderSubClassInfo@@0@Z
 * Address: 0x140012530
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(CUnmannedTraderSubClassInfo **const *__formal, CUnmannedTraderSubClassInfo **const *a2)
{
  return std::_Iter_random<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(__formal, a2);
}
