/*
 * Function: ?Send_Logout@CChiNetworkEX@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140410190
 */

void __fastcall CChiNetworkEX::Send_Logout(CChiNetworkEX *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _apex_id *v4; // rax@5
  char *v5; // rax@5
  CAsyncLogger *v6; // rax@5
  __int64 v7; // [sp+0h] [bp-88h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-68h]@5
  char Dst; // [sp+38h] [bp-50h]@5
  _apex_id v10; // [sp+60h] [bp-28h]@5
  unsigned __int16 v11[2]; // [sp+64h] [bp-24h]@5
  CUserDB *v12; // [sp+68h] [bp-20h]@5
  CUserDB *v13; // [sp+70h] [bp-18h]@5
  unsigned __int64 v14; // [sp+78h] [bp-10h]@4
  CChiNetworkEX *v15; // [sp+90h] [bp+8h]@1
  CPlayer *v16; // [sp+98h] [bp+10h]@1

  v16 = pOne;
  v15 = this;
  v2 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pOne->m_bLoad )
  {
    memcpy_0(&Dst, pOne->m_pUserDB->m_szAccountID, 0xDui64);
    *(_DWORD *)v11 = _apex_send_logout::size((_apex_send_logout *)&Dst);
    v12 = v16->m_pUserDB;
    _apex_id::_apex_id(&v10, 71);
    v5 = _apex_id::operator&(v4);
    nLen = v11[0];
    CChiNetworkEX::Send(v15, v5, v12->m_dwAccountSerial, &Dst, v11[0]);
    v13 = v16->m_pUserDB;
    v6 = CAsyncLogger::Instance();
    CAsyncLogger::FormatLog(v6, 12, "Send_Logout - %d", v13->m_dwAccountSerial);
  }
}
