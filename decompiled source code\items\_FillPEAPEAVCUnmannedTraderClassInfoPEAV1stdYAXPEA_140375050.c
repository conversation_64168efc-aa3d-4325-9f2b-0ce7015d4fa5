/*
 * Function: ??$_Fill@PEAPEAVCUnmannedTraderClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@0AEBQEAV1@@Z
 * Address: 0x140375050
 */

void __fastcall std::_Fill<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo *const *_Val)
{
  CUnmannedTraderClassInfo **i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}
