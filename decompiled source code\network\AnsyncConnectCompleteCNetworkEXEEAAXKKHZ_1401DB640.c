/*
 * Function: ?AnsyncConnectComplete@CNetworkEX@@EEAAXKKH@Z
 * Address: 0x1401DB640
 */

void __fastcall CNetworkEX::AnsyncConnectComplete(CNetworkEX *this, unsigned int dwProID, unsigned int dwIndex, int nResult)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1

  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwProID == 1 )
  {
    if ( nResult )
      __trace("Account Server Connectting Fail : %d", (unsigned int)nResult);
    else
      __trace("Account Server Connectting Success");
  }
}
