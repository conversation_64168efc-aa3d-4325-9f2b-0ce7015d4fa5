/*
 * Function: ??9?$_Vector_const_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x1403B1640
 */

bool __fastcall std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator!=(std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *_Right)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator==(
           v6,
           _Right) == 0;
}
