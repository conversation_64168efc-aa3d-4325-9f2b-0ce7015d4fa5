/*
 * CAttackForceDamageCalculator.h - Advanced Damage Calculation System
 * Handles complex damage calculations for the AttackForce system
 */

#pragma once

#include "CAttackForceConstants.h"
#include <memory>
#include <functional>
#include <unordered_map>

// Forward declarations
class CCharacter;
class CPlayer;
class CMonster;
struct _attack_param;
struct _effect_parameter;

namespace NexusProtection {
namespace Combat {

/**
 * Damage calculation context
 */
struct DamageCalculationContext {
    CCharacter* pAttacker{nullptr};
    CCharacter* pTarget{nullptr};
    _attack_param* pParam{nullptr};
    bool bUseEffBullet{false};
    bool bBackAttack{false};
    bool bCriticalHit{false};
    float fAccuracyBonus{0.0f};
    
    // Calculated values
    float fBaseAttackPower{0.0f};
    float fEffectiveAttackPower{0.0f};
    float fDamageMultiplier{1.0f};
    float fCriticalMultiplier{1.0f};
    
    DamageCalculationContext() = default;
    
    void Reset() {
        pAttacker = nullptr;
        pTarget = nullptr;
        pParam = nullptr;
        bUseEffBullet = false;
        bBackAttack = false;
        bCriticalHit = false;
        fAccuracyBonus = 0.0f;
        fBaseAttackPower = 0.0f;
        fEffectiveAttackPower = 0.0f;
        fDamageMultiplier = 1.0f;
        fCriticalMultiplier = 1.0f;
    }
    
    bool IsValid() const {
        return pAttacker != nullptr && pParam != nullptr;
    }
};

/**
 * Damage modifier information
 */
struct DamageModifier {
    std::string name;
    float multiplier{1.0f};
    bool bAdditive{false};
    int priority{0};
    
    DamageModifier() = default;
    DamageModifier(const std::string& n, float m, bool add = false, int p = 0)
        : name(n), multiplier(m), bAdditive(add), priority(p) {}
};

/**
 * Damage calculation result with detailed breakdown
 */
struct DetailedDamageResult {
    int nFinalDamage{0};
    int nBaseDamage{0};
    float fTotalMultiplier{1.0f};
    bool bCriticalHit{false};
    bool bBackAttack{false};
    std::vector<DamageModifier> appliedModifiers;
    std::string calculationLog;
    
    DetailedDamageResult() = default;
    
    void AddModifier(const DamageModifier& modifier) {
        appliedModifiers.push_back(modifier);
        if (modifier.bAdditive) {
            fTotalMultiplier += (modifier.multiplier - 1.0f);
        } else {
            fTotalMultiplier *= modifier.multiplier;
        }
    }
    
    void LogCalculation(const std::string& step) {
        if (!calculationLog.empty()) {
            calculationLog += " -> ";
        }
        calculationLog += step;
    }
};

/**
 * Advanced damage calculation system for AttackForce
 */
class CAttackForceDamageCalculator {
public:
    /**
     * Constructor
     */
    CAttackForceDamageCalculator();
    
    /**
     * Destructor
     */
    virtual ~CAttackForceDamageCalculator();
    
    /**
     * Calculate damage with full context
     * @param context Damage calculation context
     * @return Detailed damage result
     */
    DetailedDamageResult CalculateDamage(const DamageCalculationContext& context);
    
    /**
     * Calculate base attack power
     * @param pAttacker Attacking character
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @return Base attack power
     */
    float CalculateBaseAttackPower(CCharacter* pAttacker, _attack_param* pParam, bool bUseEffBullet = false);
    
    /**
     * Calculate hit chance
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @param fAccuracyBonus Additional accuracy bonus
     * @return Hit chance percentage (0-100)
     */
    float CalculateHitChance(CCharacter* pAttacker, CCharacter* pTarget, float fAccuracyBonus = 0.0f);
    
    /**
     * Calculate critical hit chance
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @return Critical hit chance percentage (0-100)
     */
    float CalculateCriticalChance(CCharacter* pAttacker, CCharacter* pTarget);
    
    /**
     * Apply damage modifiers
     * @param context Damage calculation context
     * @param result Damage result to modify
     */
    void ApplyDamageModifiers(const DamageCalculationContext& context, DetailedDamageResult& result);
    
    /**
     * Apply destroyer bonus
     * @param pAttacker Attacking character
     * @param result Damage result to modify
     */
    void ApplyDestroyerBonus(CCharacter* pAttacker, DetailedDamageResult& result);
    
    /**
     * Apply PvP ranking bonuses
     * @param pAttacker Attacking character
     * @param result Damage result to modify
     */
    void ApplyPvPBonuses(CCharacter* pAttacker, DetailedDamageResult& result);
    
    /**
     * Apply effect-based modifiers
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @param result Damage result to modify
     */
    void ApplyEffectModifiers(CCharacter* pAttacker, CCharacter* pTarget, DetailedDamageResult& result);
    
    /**
     * Apply critical hit modifier
     * @param context Damage calculation context
     * @param result Damage result to modify
     */
    void ApplyCriticalHit(const DamageCalculationContext& context, DetailedDamageResult& result);
    
    /**
     * Apply back attack modifier
     * @param context Damage calculation context
     * @param result Damage result to modify
     */
    void ApplyBackAttack(const DamageCalculationContext& context, DetailedDamageResult& result);
    
    /**
     * Calculate final damage from attack power
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @param nAttackPower Attack power
     * @param nPart Attack part
     * @param nTol Tolerance/element type
     * @param bBackAttack Back attack flag
     * @return Final damage value
     */
    int CalculateFinalDamage(CCharacter* pAttacker, CCharacter* pTarget, int nAttackPower, int nPart, int nTol, bool bBackAttack);

protected:
    /**
     * Get character-specific attack power
     * @param pCharacter Character to check
     * @param bUseEffBullet Use effect bullet flag
     * @return Character attack power
     */
    virtual int GetCharacterAttackPower(CCharacter* pCharacter, bool bUseEffBullet = false);
    
    /**
     * Get weapon-specific modifiers
     * @param pCharacter Character to check
     * @return Weapon damage modifier
     */
    virtual float GetWeaponModifier(CCharacter* pCharacter);
    
    /**
     * Get level-based modifiers
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @return Level-based modifier
     */
    virtual float GetLevelModifier(CCharacter* pAttacker, CCharacter* pTarget);
    
    /**
     * Get defense reduction
     * @param pTarget Target character
     * @param nAttackType Attack type
     * @return Defense reduction percentage
     */
    virtual float GetDefenseReduction(CCharacter* pTarget, int nAttackType);

private:
    // Damage modifier registry
    std::unordered_map<std::string, std::function<DamageModifier(const DamageCalculationContext&)>> m_modifierRegistry;
    
    // Configuration
    bool m_bEnableDetailedLogging{false};
    bool m_bEnableCriticalHits{true};
    bool m_bEnableBackAttackBonus{true};
    
    /**
     * Initialize modifier registry
     */
    void InitializeModifierRegistry();
    
    /**
     * Register a damage modifier
     * @param name Modifier name
     * @param calculator Modifier calculation function
     */
    void RegisterModifier(const std::string& name, std::function<DamageModifier(const DamageCalculationContext&)> calculator);
    
    /**
     * Log calculation step
     * @param step Calculation step description
     * @param value Current value
     */
    void LogCalculationStep(const std::string& step, float value);
    
    // Disable copy constructor and assignment operator
    CAttackForceDamageCalculator(const CAttackForceDamageCalculator&) = delete;
    CAttackForceDamageCalculator& operator=(const CAttackForceDamageCalculator&) = delete;
};

/**
 * Damage calculation utility functions
 */
namespace DamageCalculationUtils {
    /**
     * Calculate damage variance
     * @param nBaseDamage Base damage
     * @param fVariancePercent Variance percentage (0.0-1.0)
     * @return Damage with variance applied
     */
    int ApplyDamageVariance(int nBaseDamage, float fVariancePercent = 0.1f);
    
    /**
     * Calculate elemental damage modifier
     * @param nAttackerElement Attacker's element type
     * @param nTargetElement Target's element type
     * @return Elemental modifier
     */
    float CalculateElementalModifier(int nAttackerElement, int nTargetElement);
    
    /**
     * Calculate distance-based damage falloff
     * @param fDistance Distance between attacker and target
     * @param fMaxRange Maximum effective range
     * @return Distance modifier (0.0-1.0)
     */
    float CalculateDistanceFalloff(float fDistance, float fMaxRange);
    
    /**
     * Check if attack is a critical hit
     * @param fCriticalChance Critical hit chance (0-100)
     * @return true if critical hit
     */
    bool IsCriticalHit(float fCriticalChance);
    
    /**
     * Clamp damage to valid range
     * @param nDamage Damage value
     * @return Clamped damage value
     */
    int ClampDamage(int nDamage);
}

} // namespace Combat
} // namespace NexusProtection
