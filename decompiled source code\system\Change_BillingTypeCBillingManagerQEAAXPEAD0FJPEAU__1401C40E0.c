/*
 * Function: ?Change_BillingType@CBillingManager@@QEAAXPEAD0FJPEAU_SYSTEMTIME@@E@Z
 * Address: 0x1401C40E0
 */

void __fastcall CBillingManager::Change_BillingType(CBillingManager *this, char *szID, char *szCMSCode, __int16 iType, int lRemainTime, _SYSTEMTIME *pstEndDate, char byReason)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-48h]@1
  int v10; // [sp+20h] [bp-28h]@4
  _SYSTEMTIME *v11; // [sp+28h] [bp-20h]@4
  char v12; // [sp+30h] [bp-18h]@4
  CBillingManager *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v7 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v12 = byReason;
  v11 = pstEndDate;
  v10 = lRemainTime;
  CBilling::Change_BillingType(v13->m_pBill, szID, szCMSCode, iType, lRemainTime, pstEndDate, byReason);
}
