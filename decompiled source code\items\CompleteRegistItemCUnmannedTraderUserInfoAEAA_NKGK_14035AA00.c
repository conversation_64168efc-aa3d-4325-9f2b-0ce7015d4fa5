/*
 * Function: ?CompleteRegistItem@CUnmannedTraderUserInfo@@AEAA_NKGKKEEGE_KK_N@Z
 * Address: 0x14035AA00
 */

char __fastcall CUnmannedTraderUserInfo::CompleteRegistItem(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, unsigned __int16 dwItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, unsigned __int16 wItemIndex, char byStorageIndex, unsigned __int64 dwD, unsigned int dwU, bool bInserted)
{
  __int64 *v12; // rdi@1
  signed __int64 i; // rcx@1
  char v14; // al@5
  CUnmannedTraderRegistItemInfo *v15; // rax@6
  __int64 v16; // [sp+0h] [bp-C8h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+68h] [bp-60h]@4
  bool v18; // [sp+84h] [bp-44h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v19; // [sp+88h] [bp-40h]@4
  char v20; // [sp+A0h] [bp-28h]@5
  char v21; // [sp+A1h] [bp-27h]@6
  __int64 v22; // [sp+A8h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v23; // [sp+B0h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v24; // [sp+B8h] [bp-10h]@4
  CUnmannedTraderUserInfo *v25; // [sp+D0h] [bp+8h]@1
  unsigned int dwRegistSeriala; // [sp+D8h] [bp+10h]@1
  unsigned __int16 v27; // [sp+E0h] [bp+18h]@1
  unsigned int dwETSerialNumbera; // [sp+E8h] [bp+20h]@1

  dwETSerialNumbera = dwETSerialNumber;
  v27 = dwItemSerial;
  dwRegistSeriala = dwRegistSerial;
  v25 = this;
  v12 = &v16;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v12 = -858993460;
    v12 = (__int64 *)((char *)v12 + 4);
  }
  v22 = -2i64;
  CUnmannedTraderUserInfo::FindEmpty(v25, &result);
  v23 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v25->m_vecRegistItemInfo,
          &v19);
  v24 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v23;
  v18 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v23->_Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v19);
  if ( v18 )
  {
    v20 = 0;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v14 = v20;
  }
  else
  {
    v15 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
    CUnmannedTraderRegistItemInfo::RegistItem(
      v15,
      dwRegistSeriala,
      v27,
      dwETSerialNumbera,
      dwPrice,
      bySellTurm,
      byTableCode,
      wItemIndex,
      byStorageIndex,
      dwD,
      dwU,
      bInserted);
    CUnmannedTraderUserInfo::CountRegistItem(v25);
    v21 = 1;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v14 = v21;
  }
  return v14;
}
