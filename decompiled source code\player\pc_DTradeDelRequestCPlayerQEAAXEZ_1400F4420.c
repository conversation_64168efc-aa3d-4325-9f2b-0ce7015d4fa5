/*
 * Function: ?pc_DTradeDelRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400F4420
 */

void __fastcall CPlayer::pc_DTradeDelRequest(CPlayer *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char v5; // [sp+20h] [bp-48h]@4
  CPlayer *p_pDst; // [sp+38h] [bp-30h]@4
  bool *v7; // [sp+48h] [bp-20h]@15
  _STORAGE_LIST::_db_con *v8; // [sp+50h] [bp-18h]@16
  CPlayer *lp_pOne; // [sp+70h] [bp+8h]@1
  char v10; // [sp+78h] [bp+10h]@1

  v10 = bySlotIndex;
  lp_pOne = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  p_pDst = 0i64;
  if ( DTradeEqualPerson(lp_pOne, &p_pDst) )
  {
    if ( lp_pOne->m_pCurMap->m_pMapSet->m_nMapType == 1 )
    {
      v5 = 6;
    }
    else if ( lp_pOne->m_pmTrd.bDTradeLock )
    {
      v5 = 2;
    }
    else if ( (signed int)(unsigned __int8)v10 < 15 )
    {
      if ( !lp_pOne->m_pmTrd.DItemNode[(unsigned __int8)v10].bLoad )
        v5 = 4;
    }
    else
    {
      v5 = 3;
    }
  }
  else
  {
    v5 = 1;
  }
  if ( v5 )
  {
    _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
    if ( p_pDst )
    {
      _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
      CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
    }
  }
  else
  {
    v7 = &lp_pOne->m_pmTrd.DItemNode[(unsigned __int8)v10].bLoad;
    if ( !lp_pOne->m_pmTrd.DItemNode[(unsigned __int8)v10].byStorageCode )
    {
      v8 = _STORAGE_LIST::GetPtrFromSerial(lp_pOne->m_Param.m_pStoragePtr[v7[1]], *((_WORD *)v7 + 2));
      if ( !v8 )
        return;
      if ( IsOverLapItem(v8->m_byTableCode) )
      {
        if ( v8->m_dwDur == v7[8] )
          --lp_pOne->m_pmTrd.byEmptyInvenNum;
      }
      else
      {
        --lp_pOne->m_pmTrd.byEmptyInvenNum;
      }
    }
    --lp_pOne->m_pmTrd.bySellItemNum;
    _DTRADE_ITEM::ReleaseData(&lp_pOne->m_pmTrd.DItemNode[(unsigned __int8)v10]);
    CPlayer::SendMsg_DTradeDelInform(p_pDst, v10);
    CPlayer::SendMsg_DTradeDelResult(lp_pOne, v5);
  }
}
