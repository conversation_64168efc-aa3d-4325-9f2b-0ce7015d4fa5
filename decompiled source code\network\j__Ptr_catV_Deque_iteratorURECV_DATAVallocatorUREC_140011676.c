/*
 * Function: j_??$_Ptr_cat@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@0@Z
 * Address: 0x140011676
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *a2)
{
  return std::_Ptr_cat<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
           __formal,
           a2);
}
