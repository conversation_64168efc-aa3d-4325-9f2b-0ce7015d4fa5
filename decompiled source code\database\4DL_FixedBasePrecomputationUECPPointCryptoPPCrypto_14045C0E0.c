/*
 * Function: ??4?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14045C0E0
 */

CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::operator=(CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *this, CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__that)
{
  return this;
}
