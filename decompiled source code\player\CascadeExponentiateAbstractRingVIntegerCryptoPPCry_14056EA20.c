/*
 * Function: ?CascadeExponentiate@?$AbstractRing@VInteger@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@AEBV32@000@Z
 * Address: 0x14056EA20
 */

CryptoPP::Integer *__fastcall CryptoPP::AbstractRing<CryptoPP::Integer>::CascadeExponentiate(__int64 a1, CryptoPP::Integer *a2, __int64 a3, CryptoPP::Integer *a4, __int64 a5, CryptoPP::Integer *a6)
{
  __int64 v6; // rax@1
  CryptoPP::Integer *v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1
  CryptoPP::Integer *v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  LODWORD(v6) = (*(int (**)(void))(*(_QWORD *)a1 + 176i64))();
  CryptoPP::AbstractGroup<CryptoPP::Integer>::CascadeScalarMultiply(v6, v8, v9, v10, a5, a6);
  return v8;
}
