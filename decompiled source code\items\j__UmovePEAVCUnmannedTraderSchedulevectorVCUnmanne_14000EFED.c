/*
 * Function: j_??$_Umove@PEAVCUnmannedTraderSchedule@@@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAPEAVCUnmannedTraderSchedule@@PEAV2@00@Z
 * Address: 0x14000EFED
 */

CUnmannedTraderSchedule *__fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Ptr)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Umove<CUnmannedTraderSchedule *>(
           this,
           _First,
           _Last,
           _Ptr);
}
