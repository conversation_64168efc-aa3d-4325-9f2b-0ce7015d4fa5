/*
 * Function: ?Regist_UnmannedTraderSingleItem@CRFWorldDatabase@@QEAA_NKAEBU_unmannedtrader_registsingleitem@@_N@Z
 * Address: 0x1404ACC00
 */

char __fastcall CRFWorldDatabase::Regist_UnmannedTraderSingleItem(CRFWorldDatabase *this, unsigned int dwRegSerial, _unmannedtrader_registsingleitem *kInfo, bool bInsertRecord)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-78h]@1
  char Dst; // [sp+48h] [bp-30h]@4
  int v9; // [sp+64h] [bp-14h]@9
  CRFWorldDatabase *v10; // [sp+80h] [bp+8h]@1
  unsigned int dwRegSeriala; // [sp+88h] [bp+10h]@1
  _unmannedtrader_registsingleitem *kInfoa; // [sp+90h] [bp+18h]@1
  bool v13; // [sp+98h] [bp+20h]@1

  v13 = bInsertRecord;
  kInfoa = kInfo;
  dwRegSeriala = dwRegSerial;
  v10 = this;
  v4 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 0);
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  if ( CRFWorldDatabase::Update_UnmannedTraderSingleItemInfo(v10, dwRegSeriala, kInfoa) )
  {
    if ( CRFWorldDatabase::Update_UnmannedTraderSellInfo(v10, dwRegSeriala, kInfoa, (_SYSTEMTIME *)&Dst) )
    {
      if ( v13 )
        v9 = 2;
      else
        v9 = 1;
      if ( CRFWorldDatabase::Update_UnmannedTraderResutlInfo(v10, 0, dwRegSeriala, v9, 0, 0, (_SYSTEMTIME *)&Dst) )
      {
        CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v10->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
        result = 1;
      }
      else
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v10->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v10->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v10->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v10->vfptr, 1);
    result = 0;
  }
  return result;
}
