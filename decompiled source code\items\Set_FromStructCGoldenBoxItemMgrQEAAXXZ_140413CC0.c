/*
 * Function: ?Set_FromStruct@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x140413CC0
 */

void __fastcall CGoldenBoxItemMgr::Set_FromStruct(CGoldenBoxItemMgr *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-28h]@1
  int *v4; // [sp+8h] [bp-20h]@6
  int k; // [sp+10h] [bp-18h]@6
  _db_golden_box_item::_db_golden_box_item_List *v6; // [sp+18h] [bp-10h]@8
  CGoldenBoxItemMgr *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v1 = &j;
  for ( i = 8i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v7->m_golden_box_item.m_bydck = v7->m_golden_box_item_New.bydck;
  v7->m_golden_box_item.m_dwStarterBoxCnt = v7->m_golden_box_item_New.dwStarterBoxCnt;
  for ( j = 0; j < v7->m_golden_box_event.m_ini.m_byLoopCnt; ++j )
  {
    v4 = &v7->m_golden_box_item_New.nBoxcode[j];
    v7->m_golden_box_item.m_byBoxTableCode[j] = BYTE1(v7->m_golden_box_item_New.nBoxcode[j]);
    v7->m_golden_box_item.m_dwBoxIndex[j] = *((_WORD *)v4 + 1);
    v7->m_golden_box_item.m_wBoxMax[j] = v7->m_golden_box_item.m_wBoxMax[j];
    v7->m_golden_box_item.m_bygolden_item_num[j] = v7->m_golden_box_item_New.bygolden_item_num[j];
    for ( k = 0; k < v7->m_golden_box_item.m_bygolden_item_num[k]; ++k )
    {
      v6 = &v7->m_golden_box_item_New.List[j][k];
      v7->m_golden_box_item.m_golden_box_item_info[j][k].m_byTableCode = BYTE1(v6->ncode);
      v7->m_golden_box_item.m_golden_box_item_info[j][k].m_dwIndex = HIWORD(v6->ncode);
      v7->m_golden_box_item.m_golden_box_item_info[j][k].m_wNum = v7->m_golden_box_item_New.List[j][k].wcount;
    }
  }
}
