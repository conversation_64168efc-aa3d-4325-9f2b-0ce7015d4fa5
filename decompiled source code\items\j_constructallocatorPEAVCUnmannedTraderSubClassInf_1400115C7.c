/*
 * Function: j_?construct@?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@QEAAXPEAPEAVCUnmannedTraderSubClassInfo@@AEBQEAV3@@Z
 * Address: 0x1400115C7
 */

void __fastcall std::allocator<CUnmannedTraderSubClassInfo *>::construct(std::allocator<CUnmannedTraderSubClassInfo *> *this, CUnmannedTraderSubClassInfo **_Ptr, CUnmannedTraderSubClassInfo *const *_Val)
{
  std::allocator<CUnmannedTraderSubClassInfo *>::construct(this, _Ptr, _Val);
}
