/*
 * Function: ??1?$_Ranit@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14058A6F0
 */

void __fastcall std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}
