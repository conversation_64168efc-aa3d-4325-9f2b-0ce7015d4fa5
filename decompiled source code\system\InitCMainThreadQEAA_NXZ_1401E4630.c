/*
 * Function: ?Init@CMainThread@@QEAA_NXZ
 * Address: 0x1401E4630
 */

char __fastcall CMainThread::Init(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CRtc *v3; // rax@4
  DWORD v4; // eax@4
  char result; // al@5
  CAsyncLogger *v6; // rax@6
  CAsyncLogger *v7; // rax@6
  CAsyncLogger *v8; // rax@6
  CAsyncLogger *v9; // rax@6
  CAsyncLogger *v10; // rax@6
  CAsyncLogger *v11; // rax@26
  CTotalGuildRankManager *v12; // rax@32
  CWeeklyGuildRankManager *v13; // rax@34
  CandidateMgr *v14; // rax@36
  PatriarchElectProcessor *v15; // rax@38
  AutominePersonalMgr *v16; // rax@40
  CGuildRoomSystem *v17; // rax@42
  CUnmannedTraderController *v18; // rax@44
  CLogTypeDBTaskManager *v19; // rax@46
  TimeItem *v20; // rax@48
  CCashDBWorkManager *v21; // rax@50
  CashItemRemoteStore *v22; // rax@52
  CPostSystemManager *v23; // rax@54
  CPvpUserAndGuildRankingSystem *v24; // rax@56
  CMoveMapLimitManager *v25; // rax@58
  CRaceBuffManager *v26; // rax@60
  CHonorGuild *v27; // rax@62
  CPcBangFavor *v28; // rax@66
  CActionPointSystemMgr *v29; // rax@68
  CLuaScriptMgr *v30; // rax@70
  CCryptor *v31; // rax@72
  CBossMonsterScheduleSystem *v32; // rax@74
  cStaticMember_Player *v33; // rax@76
  CBillingManager *v34; // rax@78
  CGoldenBoxItemMgr *v35; // rax@80
  __int64 v36; // [sp+0h] [bp-528h]@1
  DWORD v37; // [sp+30h] [bp-4F8h]@4
  int v38; // [sp+34h] [bp-4F4h]@4
  char Dest; // [sp+50h] [bp-4D8h]@6
  unsigned int v40; // [sp+D4h] [bp-454h]@6
  char pwszErrCode; // [sp+F0h] [bp-438h]@22
  char v42; // [sp+F1h] [bp-437h]@22
  int v43; // [sp+4F4h] [bp-34h]@26
  CGuildBattleController *v44; // [sp+4F8h] [bp-30h]@30
  CExchangeEvent *v45; // [sp+510h] [bp-18h]@64
  unsigned __int64 v46; // [sp+518h] [bp-10h]@4
  CMainThread *v47; // [sp+530h] [bp+8h]@1

  v47 = this;
  v1 = &v36;
  for ( i = 328i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v46 = (unsigned __int64)&v36 ^ _security_cookie;
  v3 = CRtc::GetIntance();
  CRtc::Reg_Fn(v3);
  WheatyExceptionReport::SetLogName(&g_WheatyExceptionReport, "ZoneServer_MainLoop");
  WheatyExceptionReport::SetDescription(&g_WheatyExceptionReport, "ZoneServer : Exception Program");
  v4 = timeGetTime();
  WriteServerStartHistory("Init >> tickcount: %d", v4);
  v47->m_bWorldOpen = 0;
  v47->m_bWorldService = 0;
  v47->m_bCheckOverTickCount = 0;
  CMyTimer::BeginTimer(&v47->m_tmServerState, 0x2710u);
  CMyTimer::BeginTimer(&v47->m_tmrStateMsgGotoWeb, 0xEA60u);
  v47->m_dwCheckAccountOldTick = GetLoopTime();
  v37 = timeGetTime();
  v38 = *********;
  if ( -1 - v37 >= 0x2932E000 )
  {
    CreateDirectoryA("..\\ZoneServerLog\\", 0i64);
    CreateDirectoryA("..\\ZoneServerLog\\Systemlog", 0i64);
    clear_file("..\\ZoneServerLog\\Systemlog", 0xFu);
    CreateDirectoryA("..\\ZoneServerLog\\ServiceLog", 0i64);
    clear_file("..\\ZoneServerLog\\ServiceLog", 0xFu);
    CreateDirectoryA("..\\ZoneServerLog\\DBLog", 0i64);
    clear_file("..\\ZoneServerLog\\DBLog", 0xFu);
    CreateDirectoryA("..\\ZoneServerLog\\CharLog", 0i64);
    clear_file("..\\ZoneServerLog\\CharLog", 0xFu);
    CreateDirectoryA("..\\SystemSave", 0i64);
    clear_file("..\\SystemSave", 0xFu);
    CreateDirectoryA("..\\ZoneServerLog\\BillingLog", 0i64);
    clear_file("..\\ZoneServerLog\\BillingLog", 0xFu);
    CreateDirectoryA("..\\ZoneServerLog\\NetLog", 0i64);
    clear_file("..\\ZoneServerLog\\NetLog", 0xFu);
    CreateDirectoryA("..\\ZoneServerLog\\ServerExitLog", 0i64);
    clear_file("..\\ZoneServerLog\\ServerExitLog", 0xFu);
    v40 = GetKorLocalTime();
    _CrtSetReportHook(MyCrtDebugReportHook);
    sprintf(&Dest, "..\\ZoneServerLog\\Systemlog\\SystemError%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logSystemError, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\Systemlog\\LoadingProcess.log");
    CLogFile::SetWriteLogFile(&v47->m_logLoadingError, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\KillMon%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logKillMon, &Dest, 1, 0, 0, 0);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Dungeon%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logDungeon, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\ServerState%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logServerState, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\DTrade%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logDTrade, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Guild%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logGuild, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Rename%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logRename, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\AutoTrade%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logAutoTrade, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Event%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logEvent, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Move%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logMove, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Save%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logSave, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\ReturnGate%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logReturnGate, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\Hack%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logHack, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\PvP%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logPvP, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\MonNum_%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logMonNum, &Dest, 1, 0, 1, 1);
    sprintf(&Dest, "..\\ZoneServerLog\\ServiceLog\\CheckBilling_%d.log", v40);
    CLogFile::SetWriteLogFile(&v47->m_logBillCheck, &Dest, 1, 0, 1, 1);
    v6 = CAsyncLogger::Instance();
    CAsyncLogger::Regist(
      v6,
      ALT_HACKSHIELD_SYSTEM_LOG,
      "..\\ZoneServerLog\\SystemLog\\HacShiled",
      "HS_System",
      1,
      0x36EE80u);
    v7 = CAsyncLogger::Instance();
    CAsyncLogger::Regist(v7, ALT_APEX_SYSTEM_LOG, "..\\ZoneServerLog\\SystemLog\\Apex", "Apex_System", 1, 0x36EE80u);
    v8 = CAsyncLogger::Instance();
    CAsyncLogger::Regist(
      v8,
      ALT_FIREGUARD_DETECT_LOG,
      "..\\ZoneServerLog\\SystemLog\\fireguard",
      "CCRFG_SystemLog",
      1,
      0x36EE80u);
    v9 = CAsyncLogger::Instance();
    CAsyncLogger::Regist(
      v9,
      ALT_HONOR_GUILD_LOG,
      "..\\ZoneServerLog\\SystemLog\\HonorGuild",
      "HonorGuild_SysLog",
      1,
      0x5265C00u);
    v10 = CAsyncLogger::Instance();
    CAsyncLogger::Regist(
      v10,
      ALT_BUY_CASH_ITEM_LOG,
      "..\\ZoneServerLog\\ServiceLog\\PartiallyPaid",
      "BuyCashItemHistory",
      1,
      0x36EE80u);
    CLogFile::Write(&v47->m_logLoadingError, "Server Load Start!!");
    if ( CMainThread::LoadINI(v47) )
    {
      result = 0;
    }
    else if ( CMainThread::CheckDefine(v47) )
    {
      if ( CMainThread::check_dbsyn_data_size(v47) )
      {
        if ( CMainThread::DataFileInit(v47) )
        {
          if ( CMainThread::ObjectInit(v47) )
          {
            CLogFile::Write(&v47->m_logLoadingError, "Game Data Load Complete!!");
            if ( CMainThread::NetworkInit(v47) )
            {
              CLogFile::Write(&v47->m_logLoadingError, "Network Init Complete!!");
              CLogFile::Write(&v47->m_logLoadingError, "Map Load Start!!");
              if ( CMapOperation::Init(&g_MapOper) )
              {
                if ( CMonsterEventRespawn::SetEventRespawn(&g_MonsterEventRespawn) )
                {
                  pwszErrCode = 0;
                  memset(&v42, 0, 0x3FFui64);
                  if ( CMonsterEventSet::LoadEventSet(g_MonsterEventSet, &pwszErrCode) )
                  {
                    if ( CMonsterEventSet::LoadEventSetLooting(g_MonsterEventSet) )
                    {
                      CLogFile::Write(&v47->m_logLoadingError, "Map Load Complete!!");
                      v11 = CAsyncLogger::Instance();
                      v43 = CAsyncLogger::Init(v11);
                      if ( v43 )
                      {
                        MyMessageBox(
                          "CMainThread::Init() : ",
                          "CAsyncLogger::Instance()->Init() Ret(%d) Fail!\r\nCheck LoadingProcess.Log!",
                          (unsigned int)v43);
                        CLogFile::Write(
                          &v47->m_logLoadingError,
                          "CAsyncLogger::Instace()->Init() Ret(%d) Fail!",
                          (unsigned int)v43);
                        result = 0;
                      }
                      else
                      {
                        v47->m_pTimeLimitMgr = TimeLimitMgr::Instance();
                        TimeLimitMgr::LoadTLINIFile(v47->m_pTimeLimitMgr);
                        TimeLimitMgr::InitializeTLMgr(v47->m_pTimeLimitMgr);
                        if ( CDarkHoleDungeonQuest::LoadDarkHoleQuest(&g_DarkHoleQuest) )
                        {
                          v44 = CGuildBattleController::Instance();
                          if ( CGuildBattleController::Init(v44) )
                          {
                            v12 = CTotalGuildRankManager::Instance();
                            if ( CTotalGuildRankManager::Init(v12) )
                            {
                              v13 = CWeeklyGuildRankManager::Instance();
                              if ( CWeeklyGuildRankManager::Init(v13) )
                              {
                                v14 = CandidateMgr::Instance();
                                if ( CandidateMgr::Initialize(v14, 500) )
                                {
                                  v15 = PatriarchElectProcessor::Instance();
                                  if ( PatriarchElectProcessor::Initialize(v15) )
                                  {
                                    v16 = AutominePersonalMgr::instance();
                                    if ( AutominePersonalMgr::initialize(v16) )
                                    {
                                      v17 = CGuildRoomSystem::GetInstance();
                                      if ( CGuildRoomSystem::Init(v17) )
                                      {
                                        v18 = CUnmannedTraderController::Instance();
                                        if ( CUnmannedTraderController::Init(v18) )
                                        {
                                          v19 = CLogTypeDBTaskManager::Instance();
                                          if ( CLogTypeDBTaskManager::Init(v19) )
                                          {
                                            v20 = TimeItem::Instance();
                                            if ( TimeItem::Init(v20) )
                                            {
                                              v21 = CTSingleton<CCashDBWorkManager>::Instance();
                                              if ( CCashDBWorkManager::Initialize(v21) )
                                              {
                                                v22 = CashItemRemoteStore::Instance();
                                                if ( CashItemRemoteStore::Initialize(v22) )
                                                {
                                                  v23 = CPostSystemManager::Instace();
                                                  if ( CPostSystemManager::Init(v23) )
                                                  {
                                                    v24 = CPvpUserAndGuildRankingSystem::Instance();
                                                    if ( CPvpUserAndGuildRankingSystem::Init(v24) )
                                                    {
                                                      v25 = CMoveMapLimitManager::Instance();
                                                      if ( CMoveMapLimitManager::Init(v25) )
                                                      {
                                                        v26 = CRaceBuffManager::Instance();
                                                        if ( CRaceBuffManager::Init(v26) )
                                                        {
                                                          v27 = CHonorGuild::Instance();
                                                          if ( CHonorGuild::Init(v27) )
                                                          {
                                                            v45 = CExchangeEvent::Instance();
                                                            if ( (unsigned __int8)(*(int (__fastcall **)(CExchangeEvent *))&v45->vfptr->gap8[0])(v45) )
                                                            {
                                                              v28 = CPcBangFavor::Instance();
                                                              if ( CPcBangFavor::Initialzie(v28) )
                                                              {
                                                                v29 = CActionPointSystemMgr::Instance();
                                                                if ( CActionPointSystemMgr::Initialize(v29) )
                                                                {
                                                                  GuildCreateEventInfo::Init(&v47->m_GuildCreateEventInfo);
                                                                  CMsgData::Init((CMsgData *)&v47->m_GameMsg.vfptr, 100);
                                                                  CMyTimer::BeginTimer(&v47->m_tmrCheckAvator, 0x3E8u);
                                                                  CMyTimer::BeginTimer(&v47->m_tmrCheckLoop, 0x3E8u);
                                                                  CMyTimer::BeginTimer(&v47->m_tmrAccountPing, 0x3E8u);
                                                                  CMyTimer::BeginTimer(
                                                                    &v47->m_tmrCheckRadarDelay,
                                                                    0x2710u);
                                                                  v30 = CLuaScriptMgr::Instance();
                                                                  if ( CLuaScriptMgr::InitSDM(v30) )
                                                                  {
                                                                    v31 = CTSingleton<CCryptor>::Instance();
                                                                    if ( CCryptor::Init(
                                                                           v31,
                                                                           ".\\Initialize\\WorldSystem.bin",
                                                                           0) )
                                                                    {
                                                                      v32 = CBossMonsterScheduleSystem::Instance();
                                                                      if ( CBossMonsterScheduleSystem::Init(
                                                                             v32,
                                                                             &g_MapOper) )
                                                                      {
                                                                        CNotifyNotifyRaceLeaderSownerUTaxrate::Init(&v47->m_kEtcNotifyInfo);
                                                                        v33 = cStaticMember_Player::Instance();
                                                                        if ( cStaticMember_Player::Initialize(v33) )
                                                                        {
                                                                          v47->m_bRuleThread = 1;
                                                                          _beginthread(
                                                                            (void (__cdecl *)(void *))CMainThread::RuleThread,
                                                                            0,
                                                                            v47);
                                                                          v47->m_bDQSThread = 1;
                                                                          _beginthread(
                                                                            (void (__cdecl *)(void *))CMainThread::DQSThread,
                                                                            0,
                                                                            v47);
                                                                          CLogFile::Write(
                                                                            &v47->m_logLoadingError,
                                                                            "Thread Setting Complete!!");
                                                                          CConnNumPHMgr::Init(&v47->m_MgrConnNum);
                                                                          CConnNumPHMgr::Init(&v47->m_HisMainFPS);
                                                                          CConnNumPHMgr::Init(&v47->m_HisSendFPS);
                                                                          CConnNumPHMgr::Init(&v47->m_HisDataFPS);
                                                                          CWnd::SendMessageA(g_pFrame, 0xCu, 0i64, 0i64);
                                                                          CMainThread::MakeSystemTower(v47);
                                                                          CLogFile::Write(
                                                                            &v47->m_logLoadingError,
                                                                            "System Tower Make Complete!!");
                                                                          v47->m_byWebAgentServerNetInx = 0;
                                                                          v47->m_bConnectedWebAgentServer = 0;
                                                                          v47->m_byControllServerNetInx = 0;
                                                                          v47->m_bConnectedControllServer = 0;
                                                                          v34 = CTSingleton<CBillingManager>::Instance();
                                                                          if ( CBillingManager::Init(v34) )
                                                                          {
                                                                            v47->m_dwCheatSetPlayTime = 600;
                                                                            v47->m_dwCheatSetScanerCnt = 0;
                                                                            v47->m_dwCheatSetLevel = 50;
                                                                            v47->m_dwServerResetToken = 1990011;
                                                                            v35 = CGoldenBoxItemMgr::Instance();
                                                                            if ( CGoldenBoxItemMgr::Initialize(v35) )
                                                                            {
                                                                              result = 1;
                                                                            }
                                                                            else
                                                                            {
                                                                              MyMessageBox(
                                                                                "CMainThread::Init() : ",
                                                                                "CGoldenBoxItemMgr::Instance()->Initialize() Fail!");
                                                                              CLogFile::Write(
                                                                                &v47->m_logLoadingError,
                                                                                "CGoldenBoxItemMgr::Instance()->Initialize() Fail");
                                                                              result = 0;
                                                                            }
                                                                          }
                                                                          else
                                                                          {
                                                                            MyMessageBox(
                                                                              "CBillingManager::",
                                                                              "Init() Failed!");
                                                                            result = 0;
                                                                          }
                                                                        }
                                                                        else
                                                                        {
                                                                          MyMessageBox(
                                                                            "CMainThread::Init() : ",
                                                                            "cStaticMember_Player::Instance()->Initialize() Fail!");
                                                                          CLogFile::Write(
                                                                            &v47->m_logLoadingError,
                                                                            "cStaticMember_Player::Instance()->Initialize() Fail");
                                                                          result = 0;
                                                                        }
                                                                      }
                                                                      else
                                                                      {
                                                                        MyMessageBox(
                                                                          "CMainThread::Init() : ",
                                                                          "CBossMonsterScheduleSystem::Instance()->Init(&"
                                                                          "g_MapOper) Fail!");
                                                                        CLogFile::Write(
                                                                          &v47->m_logLoadingError,
                                                                          "CBossMonsterScheduleSystem::Instance()->Init(&g_MapOper) Fail");
                                                                        result = 0;
                                                                      }
                                                                    }
                                                                    else
                                                                    {
                                                                      MyMessageBox(
                                                                        "CMainThread::Init() : ",
                                                                        "CCryptor::Instance()->Init() Fail");
                                                                      CLogFile::Write(
                                                                        &v47->m_logLoadingError,
                                                                        "CCryptor::Instance()->Init()");
                                                                      result = 0;
                                                                    }
                                                                  }
                                                                  else
                                                                  {
                                                                    MyMessageBox(
                                                                      "CMainThread::Init() : ",
                                                                      "CLuaScriptMgr::Instance()->InitSDM() Fail!");
                                                                    CLogFile::Write(
                                                                      &v47->m_logLoadingError,
                                                                      "CLuaScriptMgr::Instance()->InitSDM()");
                                                                    result = 0;
                                                                  }
                                                                }
                                                                else
                                                                {
                                                                  MyMessageBox(
                                                                    "CMainThread::Init() : ",
                                                                    "CActionPointSystemMgr::Instance()->Initialize() Fail!");
                                                                  CLogFile::Write(
                                                                    &v47->m_logLoadingError,
                                                                    "CActionPointSystemMgr::Instance()->Initialize() Fail");
                                                                  result = 0;
                                                                }
                                                              }
                                                              else
                                                              {
                                                                MyMessageBox(
                                                                  "CMainThread::Init() : ",
                                                                  "CPcBangFavor::Instance()->Initialzie() Fail!");
                                                                CLogFile::Write(
                                                                  &v47->m_logLoadingError,
                                                                  "CPcBangFavor::Instance()->Initialzie() Fail");
                                                                result = 0;
                                                              }
                                                            }
                                                            else
                                                            {
                                                              MyMessageBox(
                                                                "CMainThread::Init() : ",
                                                                "CExchangeEvent::Instance()->Initialzie() Fail!");
                                                              CLogFile::Write(
                                                                &v47->m_logLoadingError,
                                                                "CExchangeEvent::Instance()->Initialzie() Fail!");
                                                              result = 0;
                                                            }
                                                          }
                                                          else
                                                          {
                                                            MyMessageBox(
                                                              "CMainThread::Init() : ",
                                                              "CHonorGuild::Instance()->Init() Fail!");
                                                            CLogFile::Write(
                                                              &v47->m_logLoadingError,
                                                              "CHonorGuild::Instance()->Init() Fail!");
                                                            result = 0;
                                                          }
                                                        }
                                                        else
                                                        {
                                                          MyMessageBox(
                                                            "CMainThread::Init() : ",
                                                            "CRaceBuffManager::Instance()->Init() Fail!\r\n"
                                                            "Check LoadingProcess.Log!");
                                                          CLogFile::Write(
                                                            &v47->m_logLoadingError,
                                                            "CRaceBuffManager::Instace()->Init() Fail!");
                                                          result = 0;
                                                        }
                                                      }
                                                      else
                                                      {
                                                        MyMessageBox(
                                                          "CMainThread::Init() : ",
                                                          "CMoveMapLimitManager::Instance()->Init() Fail!\r\n"
                                                          "Check LoadingProcess.Log!");
                                                        CLogFile::Write(
                                                          &v47->m_logLoadingError,
                                                          "CPvpUserAndGuildRankingSystem::Instace()->Init() Fail!");
                                                        result = 0;
                                                      }
                                                    }
                                                    else
                                                    {
                                                      MyMessageBox(
                                                        "CMainThread::Init() : ",
                                                        "CPvpUserAndGuildRankingSystem::Instace()->Init() Fail!");
                                                      CLogFile::Write(
                                                        &v47->m_logLoadingError,
                                                        "CPvpUserAndGuildRankingSystem::Instace()->Init() Fail!");
                                                      result = 0;
                                                    }
                                                  }
                                                  else
                                                  {
                                                    MyMessageBox(
                                                      "CMainThread::Init() : ",
                                                      "CPostSystemManager::Instace()->Init() Fail!");
                                                    CLogFile::Write(
                                                      &v47->m_logLoadingError,
                                                      "CPostSystemManager::Instace()->Init() Fail!");
                                                    result = 0;
                                                  }
                                                }
                                                else
                                                {
                                                  MyMessageBox(
                                                    "CMainThread::Init() : ",
                                                    "CashItemRemoteStore::Instance()->Initialize() Fail!");
                                                  CLogFile::Write(
                                                    &v47->m_logLoadingError,
                                                    "CashItemRemoteStore::Instance()->Initialize() Fail!");
                                                  result = 0;
                                                }
                                              }
                                              else
                                              {
                                                MyMessageBox(
                                                  "CMainThread::Init() : ",
                                                  "CashDbWorker::Instance()->Initialize() Fail!");
                                                CLogFile::Write(
                                                  &v47->m_logLoadingError,
                                                  "CashDbWorker::Instance()->Initialize() Fail!");
                                                result = 0;
                                              }
                                            }
                                            else
                                            {
                                              MyMessageBox(
                                                "CMainThread::Init() : ",
                                                "TimeItem::Instance()->Init() Fail!");
                                              CLogFile::Write(
                                                &v47->m_logLoadingError,
                                                "TimeItem::Instance()->Init() Fail!");
                                              result = 0;
                                            }
                                          }
                                          else
                                          {
                                            MyMessageBox(
                                              "CMainThread::Init() : ",
                                              "CLogTypeDBTaskManager::Instance()->Init() Fail!");
                                            CLogFile::Write(
                                              &v47->m_logLoadingError,
                                              "CLogTypeDBTaskManager::Instance()->Init() Fail!");
                                            result = 0;
                                          }
                                        }
                                        else
                                        {
                                          MyMessageBox(
                                            "CMainThread::Init() : ",
                                            "CUnmannedTraderController::Instance()->Init() Fail!");
                                          CLogFile::Write(
                                            &v47->m_logLoadingError,
                                            "CUnmannedTraderController::Instance()->Init() Fail!");
                                          result = 0;
                                        }
                                      }
                                      else
                                      {
                                        MyMessageBox(
                                          "CMainThread::_GameDataBaseInit()",
                                          "CGuildRoomSystem::GetInstance()->Init() Fail!\r\n",
                                          "Check LoadingProcess.log!");
                                        CLogFile::Write(
                                          &v47->m_logLoadingError,
                                          "CGuildRoomSystem::GetInstance()->Init() Fail!");
                                        result = 0;
                                      }
                                    }
                                    else
                                    {
                                      MyMessageBox(
                                        "ObjectInit() Error",
                                        "AutominePersonalMgr::instance()->Initialize() Fail!");
                                      CLogFile::Write(
                                        &v47->m_logLoadingError,
                                        "AutominePersonalMgr::instance()->Initialize() Fail!");
                                      result = 0;
                                    }
                                  }
                                  else
                                  {
                                    MyMessageBox(
                                      "CMainThread::Init() : ",
                                      "PatriarchElectProcessor::Instance()->Initialize() Fail!");
                                    CLogFile::Write(
                                      &v47->m_logLoadingError,
                                      "PatriarchElectProcessor::Instance()->Initialize() Fail!");
                                    result = 0;
                                  }
                                }
                                else
                                {
                                  MyMessageBox(
                                    "CMainThread::Init() : ",
                                    "CandidateMgr::Instance()->Initialize(300) Fail!");
                                  CLogFile::Write(
                                    &v47->m_logLoadingError,
                                    "CandidateMgr::Instance()->Initialize(300) Fail!");
                                  result = 0;
                                }
                              }
                              else
                              {
                                MyMessageBox(
                                  "CMainThread::Init() : ",
                                  "CWeeklyGuildRankManager::Instance()->Init() Fail!");
                                CLogFile::Write(
                                  &v47->m_logLoadingError,
                                  "CWeeklyGuildRankManager::Instance()->Init() Fail!");
                                result = 0;
                              }
                            }
                            else
                            {
                              MyMessageBox("CMainThread::Init() : ", "CTotalGuildRankManager::Instance()->Init() Fail!");
                              CLogFile::Write(
                                &v47->m_logLoadingError,
                                "CTotalGuildRankManager::Instance()->Init() Fail!");
                              result = 0;
                            }
                          }
                          else
                          {
                            MyMessageBox(
                              "CMainThread::Init() : ",
                              "CGuildBattleController::Instance()->Init() == false");
                            CLogFile::Write(
                              &v47->m_logLoadingError,
                              "CGuildBattleController::Instance()->Init() == false");
                            result = 0;
                          }
                        }
                        else
                        {
                          MyMessageBox("DarkHole", "Failed g_DarkHoleQuest.LoadDarkHoleQuest()");
                          CLogFile::Write(&v47->m_logLoadingError, "Failed g_DarkHoleQuest.LoadDarkHoleQuest()");
                          result = 0;
                        }
                      }
                    }
                    else
                    {
                      MyMessageBox("CGameServerDoc Error", "Read Error Event Set Looting Script");
                      result = 0;
                    }
                  }
                  else
                  {
                    MyMessageBox("CGameServerDoc Error", "Read Error Event Set Script, Reason : %s", &pwszErrCode);
                    result = 0;
                  }
                }
                else
                {
                  MyMessageBox("CGameServerDoc Error", "Read Error Monster Respawn Script");
                  result = 0;
                }
              }
              else
              {
                MyMessageBox("CGameServerDoc Error", "g_MapOper.Init() == false");
                result = 0;
              }
            }
            else
            {
              MyMessageBox("CGameServerDoc Error", "NetworkInit() == false");
              result = 0;
            }
          }
          else
          {
            MyMessageBox("CGameServerDoc Error", "ObjectInit()");
            result = 0;
          }
        }
        else
        {
          MyMessageBox("CGameServerDoc Error", "DataFileInit()");
          result = 0;
        }
      }
      else
      {
        MyMessageBox("CGameServerDoc Error", "check_dbsyn_data_size()");
        result = 0;
      }
    }
    else
    {
      MyMessageBox("CGameServerDoc Error", "CheckDefine()");
      result = 0;
    }
  }
  else
  {
    MyMessageBox("Start Error", "Must Reboot OS To Service");
    result = 0;
  }
  return result;
}
