/*
 * Function: ?invoke@?$ptr2lua@VCMonster@@@lua_tinker@@SAXPEAUlua_State@@PEAVCMonster@@@Z
 * Address: 0x14040B670
 */

void __fastcall lua_tinker::ptr2lua<CMonster>::invoke(struct lua_State *L, CMonster *input)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  void *v4; // rax@5
  __int64 v5; // rax@6
  __int64 v6; // [sp+0h] [bp-58h]@1
  __int64 v7; // [sp+20h] [bp-38h]@8
  lua_tinker::ptr2user<CMonster> *v8; // [sp+28h] [bp-30h]@5
  void *_Where; // [sp+30h] [bp-28h]@5
  __int64 v10; // [sp+38h] [bp-20h]@4
  __int64 v11; // [sp+40h] [bp-18h]@6
  struct lua_State *v12; // [sp+60h] [bp+8h]@1
  CMonster *t; // [sp+68h] [bp+10h]@1

  t = input;
  v12 = L;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  if ( input )
  {
    LODWORD(v4) = lua_newuserdata(v12, 16i64);
    _Where = v4;
    v8 = (lua_tinker::ptr2user<CMonster> *)operator new(0x10ui64, v4);
    if ( v8 )
    {
      lua_tinker::ptr2user<CMonster>::ptr2user<CMonster>(v8, t);
      v11 = v5;
    }
    else
    {
      v11 = 0i64;
    }
    v7 = v11;
  }
  else
  {
    lua_pushnil(v12);
  }
}
