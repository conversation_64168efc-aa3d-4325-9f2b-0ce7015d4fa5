/*
 * Function: ?pc_UnitLeaveRequest@CPlayer@@QEAAXPEAM@Z
 * Address: 0x140106310
 */

void __usercall CPlayer::pc_UnitLeaveRequest(CPlayer *this@<rcx>, float *pfNewPos@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char v6; // [sp+20h] [bp-78h]@4
  CParkingUnit *pCreateUnit; // [sp+28h] [bp-70h]@4
  _parkingunit_create_setdata Dst; // [sp+38h] [bp-60h]@14
  unsigned int v9; // [sp+74h] [bp-24h]@14
  _base_fld *v10; // [sp+78h] [bp-20h]@14
  unsigned __int64 v11; // [sp+88h] [bp-10h]@4
  CPlayer *v12; // [sp+A0h] [bp+8h]@1
  float *fTar; // [sp+A8h] [bp+10h]@1

  fTar = pfNewPos;
  v12 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = 0;
  pCreateUnit = 0i64;
  if ( CPlayerDB::GetRaceCode(&v12->m_Param) )
  {
    v6 = 1;
  }
  else if ( v12->m_pUsingUnit && !v12->m_pParkingUnit )
  {
    GetSqrt(v12->m_fCurPos, fTar);
    if ( a3 <= 40.0 )
    {
      pCreateUnit = FindEmptyParkingUnit(g_ParkingUnit, 300);
      if ( !pCreateUnit )
        v6 = 19;
    }
    else
    {
      v6 = 24;
    }
  }
  else
  {
    v6 = 22;
  }
  if ( !v6 )
  {
    _parkingunit_create_setdata::_parkingunit_create_setdata(&Dst);
    Dst.byFrame = v12->m_pUsingUnit->byFrame;
    memcpy_0(Dst.byPartCode, v12->m_pUsingUnit->byPart, 6ui64);
    Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C8BA0, v12->m_pUsingUnit->byFrame);
    Dst.pOwner = v12;
    Dst.byCreateType = 1;
    Dst.m_pMap = v12->m_pCurMap;
    Dst.m_nLayerIndex = v12->m_wMapLayerIndex;
    memcpy_0(Dst.m_fStartPos, v12->m_fCurPos, 0xCui64);
    v9 = 10000;
    v10 = CRecordData::GetRecord(&stru_1799C8BA0, v12->m_pUsingUnit->byFrame);
    if ( *(_DWORD *)&v10[1].m_strCode[0] > 0 )
      v9 = *(_DWORD *)&v10[1].m_strCode[0];
    Dst.wHPRate = 10000 * v12->m_pUsingUnit->dwGauge / v9;
    CParkingUnit::Create(pCreateUnit, &Dst);
    CPlayer::Emb_RidindUnit(v12, 0, pCreateUnit);
  }
  CPlayer::SendMsg_UnitLeaveResult(v12, v6);
}
