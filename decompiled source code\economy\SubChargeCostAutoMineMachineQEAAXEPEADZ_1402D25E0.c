/*
 * Function: ?SubChargeCost@AutoMineMachine@@QEAAXEPEAD@Z
 * Address: 0x1402D25E0
 */

void __fastcall AutoMineMachine::SubChargeCost(AutoMineMachine *this, char byRet, char *pdata)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@11
  long double v6; // xmm0_8@14
  CGuild *v7; // rcx@14
  __int64 v8; // [sp+0h] [bp-A8h]@1
  unsigned __int16 nLen[4]; // [sp+20h] [bp-88h]@14
  long double v10; // [sp+28h] [bp-80h]@14
  long double v11; // [sp+30h] [bp-78h]@14
  char *pbyDate; // [sp+38h] [bp-70h]@14
  bool bInPut; // [sp+40h] [bp-68h]@14
  char *v14; // [sp+50h] [bp-58h]@4
  CPlayer *v15; // [sp+58h] [bp-50h]@6
  _pt_automine_charge_money_db_update_fail_zocl v16; // [sp+64h] [bp-44h]@11
  char pbyType; // [sp+84h] [bp-24h]@11
  char v18; // [sp+85h] [bp-23h]@11
  AutoMineMachine *v19; // [sp+B0h] [bp+8h]@1

  v19 = this;
  v3 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = pdata;
  if ( byRet || v14[45] )
  {
    v15 = &g_Player + *(_WORD *)(v14 + 11);
    if ( v15->m_dwObjSerial != *(_DWORD *)(v14 + 13) )
      v15 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)(v14 + 13));
    if ( v15 )
    {
      if ( v15->m_bLive )
      {
        v16.nCharge = *(_DWORD *)(v14 + 17);
        pbyType = 14;
        v18 = 28;
        v5 = _pt_automine_charge_money_db_update_fail_zocl::size(&v16);
        CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, (char *)&v16, v5);
      }
    }
  }
  else
  {
    v19->m_Battery.m_nCurGage += *(_DWORD *)(v14 + 17);
    if ( v19->m_Battery.m_nCurGage > v19->m_Battery.m_nMaxGage )
      v19->m_Battery.m_nCurGage = v19->m_Battery.m_nMaxGage;
    v19->m_pOwnerGuild->m_byMoneyOutputKind = 7;
    v6 = (double)-*(_DWORD *)(v14 + 21);
    v7 = v19->m_pOwnerGuild;
    bInPut = 0;
    pbyDate = v14 + 41;
    v11 = *(double *)(v14 + 33);
    v10 = *(double *)(v14 + 25);
    *(long double *)nLen = v6;
    CGuild::IOMoney(v19->m_pOwnerGuild, "AutoMine Charge", v7->m_MasterData.dwSerial, 0.0, v6, v10, v11, v14 + 41, 0);
    *(_QWORD *)nLen = *(_QWORD *)(v14 + 33);
    CLogFile::Write(&v19->m_Log, "Cost : serial:%d, out:%d, total:%.0f)", *(_DWORD *)(v14 + 3), *(_DWORD *)(v14 + 21));
  }
}
