/*
 * Function: ?AddGold@CPlayer@@QEAAXK_N@Z
 * Address: 0x140055D30
 */

void __usercall CPlayer::AddGold(CPlayer *this@<rcx>, unsigned int dwPush@<edx>, bool bApply@<r8b>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@6
  unsigned int v7; // eax@8
  unsigned int v8; // eax@9
  __int64 v9; // [sp+0h] [bp-38h]@1
  double v10; // [sp+20h] [bp-18h]@5
  unsigned int dwGold; // [sp+28h] [bp-10h]@6
  unsigned int gold; // [sp+2Ch] [bp-Ch]@9
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  unsigned int ui64AddGold; // [sp+48h] [bp+10h]@1

  ui64AddGold = dwPush;
  v13 = this;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( bApply )
  {
    TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v13->m_id.wIndex);
    v10 = a4;
    ui64AddGold = (signed int)floor((double)(signed int)ui64AddGold * a4);
  }
  dwGold = ui64AddGold + CPlayerDB::GetGold(&v13->m_Param);
  v6 = CPlayerDB::GetGold(&v13->m_Param);
  if ( !CanAddMoneyForMaxLimGold(ui64AddGold, v6) )
    dwGold = 500000;
  v7 = CPlayerDB::GetGold(&v13->m_Param);
  if ( dwGold != v7 )
  {
    CPlayerDB::SetGold(&v13->m_Param, dwGold);
    gold = CPlayerDB::GetGold(&v13->m_Param);
    v8 = CPlayerDB::GetDalant(&v13->m_Param);
    CUserDB::Update_Money(v13->m_pUserDB, v8, gold);
  }
}
