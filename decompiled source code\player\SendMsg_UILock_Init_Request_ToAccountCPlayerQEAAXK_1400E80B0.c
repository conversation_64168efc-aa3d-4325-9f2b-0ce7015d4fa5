/*
 * Function: ?SendMsg_UILock_Init_Request_ToAccount@CPlayer@@QEAAXKPEADGE0@Z
 * Address: 0x1400E80B0
 */

void __fastcall CPlayer::SendMsg_UILock_Init_Request_ToAccount(CPlayer *this, unsigned int dwSerial, char *uszUILockPW, unsigned __int16 wUserIndex, char byHintIndex, char *uszHintAnswer)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@4
  unsigned int v10; // [sp+3Ah] [bp-6Eh]@4
  char Dst; // [sp+3Eh] [bp-6Ah]@4
  char v12; // [sp+4Bh] [bp-5Dh]@4
  char v13; // [sp+4Ch] [bp-5Ch]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v15; // [sp+75h] [bp-33h]@4
  unsigned __int64 v16; // [sp+90h] [bp-18h]@4

  v6 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v16 = (unsigned __int64)&v8 ^ _security_cookie;
  v10 = dwSerial;
  *(_WORD *)szMsg = wUserIndex;
  strcpy_s(&Dst, 0xDui64, uszUILockPW);
  v12 = byHintIndex;
  strcpy_s(&v13, 0x11ui64, uszHintAnswer);
  pbyType = 1;
  v15 = 15;
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, szMsg, 0x25u);
}
