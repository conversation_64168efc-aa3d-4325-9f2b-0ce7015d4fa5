/*
 * Function: ??0?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14055E330
 */

__int64 __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>(__int64 a1, __int64 a2, int a3)
{
  __int64 v4; // [sp+50h] [bp+8h]@1
  __int64 v5; // [sp+58h] [bp+10h]@1

  v5 = a2;
  v4 = a1;
  if ( a3 )
  {
    *(_QWORD *)(a1 + 16) = &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::`vbtable';
    if ( a2 )
      CryptoPP::CryptoMaterial::CryptoMaterial(
        (CryptoPP::CryptoMaterial *)(a1 + 232),
        (const struct CryptoPP::CryptoMaterial *)(a2 + *(_DWORD *)(*(_QWORD *)(a2 + 16) + 4i64) + 16));
    else
      CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 232), 0i64);
  }
  CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(
    v4,
    v5,
    0i64);
  return v4;
}
