/*
 * Function: ?send_current_state@AutominePersonal@@QEAAXXZ
 * Address: 0x1402DCEA0
 */

void __fastcall AutominePersonal::send_current_state(AutominePersonal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@7
  __int64 v4; // [sp+0h] [bp-78h]@1
  _personal_automine_current_state_zocl v5; // [sp+34h] [bp-44h]@7
  char pbyType; // [sp+54h] [bp-24h]@7
  char v7; // [sp+55h] [bp-23h]@7
  AutominePersonal *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_pOwner )
  {
    if ( v8->m_pOwner->m_bOper )
    {
      v8->m_dwNextSendTime_CurState += 60000;
      _personal_automine_current_state_zocl::_personal_automine_current_state_zocl(&v5);
      v5.wItemSerial = v8->m_wItemSerial;
      v5.byFilledSlotCnt = v8->m_byFilledSlotCnt;
      v5.dwBatteryGage = AutominePersonal::get_battery(v8);
      pbyType = 14;
      v7 = 67;
      v3 = _personal_automine_current_state_zocl::size(&v5);
      CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_pOwner->m_id.wIndex, &pbyType, (char *)&v5, v3);
    }
  }
}
