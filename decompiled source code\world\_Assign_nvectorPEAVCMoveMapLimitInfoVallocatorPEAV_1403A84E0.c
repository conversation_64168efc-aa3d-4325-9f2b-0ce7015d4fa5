/*
 * Function: ?_Assign_n@?$vector@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAAX_KAEBQEAVCMoveMapLimitInfo@@@Z
 * Address: 0x1403A84E0
 */

void __fastcall std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Assign_n(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-E8h]@1
  CMoveMapLimitInfo *v6; // [sp+28h] [bp-C0h]@4
  char v7; // [sp+38h] [bp-B0h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *result; // [sp+50h] [bp-98h]@4
  char v9; // [sp+58h] [bp-90h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v10; // [sp+70h] [bp-78h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v11; // [sp+78h] [bp-70h]@4
  char v12; // [sp+90h] [bp-58h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v13; // [sp+A8h] [bp-40h]@4
  __int64 v14; // [sp+B0h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v15; // [sp+B8h] [bp-30h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v16; // [sp+C0h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v17; // [sp+C8h] [bp-20h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v18; // [sp+D0h] [bp-18h]@4
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v19; // [sp+F0h] [bp+8h]@1
  unsigned __int64 v20; // [sp+F8h] [bp+10h]@1

  v20 = _Count;
  v19 = this;
  v3 = &v5;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  v6 = *_Val;
  result = (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v7;
  v10 = (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v9;
  v15 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::end(
          v19,
          (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v7);
  v16 = v15;
  v17 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::begin(v19, v10);
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::erase(v19, &v11, v17, v16);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v11);
  v13 = (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v12;
  v18 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::begin(
          v19,
          (std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v12);
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::insert(v19, v18, v20, &v6);
}
