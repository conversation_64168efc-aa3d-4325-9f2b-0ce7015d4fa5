/*
 * Function: j_?OptimalDataAlignment@?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@UEBAIXZ
 * Address: 0x1400030CB
 */

unsigned int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::OptimalDataAlignment(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *this)
{
  return CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::OptimalDataAlignment(this);
}
