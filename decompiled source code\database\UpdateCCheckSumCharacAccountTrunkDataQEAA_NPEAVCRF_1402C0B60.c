/*
 * Function: ?Update@CCheckSumCharacAccountTrunkData@@QEAA_NPEAVCRFWorldDatabase@@@Z
 * Address: 0x1402C0B60
 */

bool __fastcall CCheckSumCharacAccountTrunkData::Update(CCheckSumCharacAccountTrunkData *this, CRFWorldDatabase *pkDB)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCheckSumCharacAccountTrunkData *v6; // [sp+30h] [bp+8h]@1
  CRFWorldDatabase *v7; // [sp+38h] [bp+10h]@1

  v7 = pkDB;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkDB )
    result = CRFWorldDatabase::Update_NpcData(pkDB, v6->m_dwSerial, v6->m_dwValues)
          && CRFWorldDatabase::Update_AnimusData(v7, v6->m_dwAccountSerial, v6->m_byRace, v6->m_dValues);
  else
    result = 0;
  return result;
}
