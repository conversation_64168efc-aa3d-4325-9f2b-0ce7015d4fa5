/*
 * Function: ?pc_ChatMapRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140092480
 */

void __fastcall CPlayer::pc_ChatMapRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@11
  CChatStealSystem *v5; // rax@11
  int v6; // eax@19
  __int64 v7; // [sp+0h] [bp-1E8h]@1
  _chat_message_receipt_udp Dst; // [sp+40h] [bp-1A8h]@11
  char pbyType; // [sp+174h] [bp-74h]@11
  char v10; // [sp+175h] [bp-73h]@11
  int v11; // [sp+184h] [bp-64h]@11
  _sec_info *v12; // [sp+188h] [bp-60h]@11
  int v13; // [sp+190h] [bp-58h]@11
  int j; // [sp+194h] [bp-54h]@11
  int k; // [sp+198h] [bp-50h]@13
  unsigned int dwSecIndex; // [sp+19Ch] [bp-4Ch]@16
  CObjectList *v17; // [sp+1A0h] [bp-48h]@16
  CObjectList *v18; // [sp+1A8h] [bp-40h]@17
  CObjectListVtbl *v19; // [sp+1B0h] [bp-38h]@19
  CObjectListVtbl *v20; // [sp+1B8h] [bp-30h]@19
  CObjectListVtbl *v21; // [sp+1C0h] [bp-28h]@19
  int v22; // [sp+1D0h] [bp-18h]@19
  unsigned __int64 v23; // [sp+1D8h] [bp-10h]@4
  CPlayer *pPlayer; // [sp+1F0h] [bp+8h]@1
  const char *Str; // [sp+1F8h] [bp+10h]@1

  Str = pwszChatData;
  pPlayer = this;
  v2 = &v7;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v23 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pPlayer->m_pUserDB
    && !pPlayer->m_pUserDB->m_bChatLock
    && !CPlayer::IsPunished(pPlayer, 0, 1)
    && CGameObject::GetCurSecNum((CGameObject *)&pPlayer->vfptr) != -1
    && !pPlayer->m_bMapLoading )
  {
    _chat_message_receipt_udp::_chat_message_receipt_udp(&Dst);
    Dst.byMessageType = 9;
    Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
    Dst.byRaceCode = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
    Dst.bFiltering = 0;
    Dst.bySize = strlen_0(Str);
    memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    v4 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
    strcpy_0(Dst.wszSenderName, v4);
    Dst.byPvpGrade = pPlayer->m_Param.m_byPvPGrade;
    pbyType = 2;
    v10 = 10;
    v5 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v5, pPlayer, Dst.byMessageType, (char *)Str);
    v11 = _chat_message_receipt_udp::size(&Dst);
    v12 = CMapData::GetSecInfo(pPlayer->m_pCurMap);
    v13 = v12->m_nSecNumW;
    for ( j = 0; j < v12->m_nSecNumH; ++j )
    {
      for ( k = 0; k < v12->m_nSecNumW; ++k )
      {
        dwSecIndex = k + v12->m_nSecNumW * j;
        v17 = CMapData::GetSectorListPlayer(pPlayer->m_pCurMap, pPlayer->m_wMapLayerIndex, dwSecIndex);
        if ( v17 )
        {
          v18 = (CObjectList *)v17->m_Head.m_pNext;
          while ( (_object_list_point *)v18 != &v17->m_Tail )
          {
            v19 = v18->vfptr;
            v18 = (CObjectList *)v18->m_Head.m_pItem;
            v20 = v19 + 2;
            v21 = v19;
            v22 = CPlayerDB::GetRaceCode((CPlayerDB *)&v19[244]);
            v6 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
            if ( v22 == v6 )
            {
              if ( BYTE1(v21[5917].__vecDelDtor) )
                CNetProcess::LoadSendMsg(unk_1414F2088, WORD1(v20->__vecDelDtor), &pbyType, &Dst.byMessageType, v11);
            }
          }
        }
      }
    }
  }
}
