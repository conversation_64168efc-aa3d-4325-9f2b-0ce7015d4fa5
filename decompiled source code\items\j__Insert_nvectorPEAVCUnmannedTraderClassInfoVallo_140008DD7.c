/*
 * Function: j_?_Insert_n@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@2@_KAEBQEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x140008DD7
 */

void __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Insert_n(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Where, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Insert_n(
    this,
    _Where,
    _Count,
    _Val);
}
