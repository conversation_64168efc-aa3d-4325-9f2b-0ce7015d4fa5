/*
 * Function: ?pc_OreCutting@CPlayer@@QEAAXGE@Z
 * Address: 0x1400D26B0
 */

void __usercall CPlayer::pc_OreCutting(CPlayer *this@<rcx>, unsigned __int16 wOreSerial@<dx>, char by<PERSON><PERSON><PERSON><PERSON><PERSON>@<r8b>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  int v7; // eax@4
  float v8; // xmm0_4@16
  signed __int64 v9; // rax@16
  unsigned int v10; // eax@19
  int v11; // eax@34
  CMoneySupplyMgr *v12; // rax@39
  CActionPointSystemMgr *v13; // rax@40
  unsigned int v14; // eax@41
  unsigned int v15; // eax@42
  int v16; // edx@42
  int v17; // eax@51
  __int64 v18; // [sp+0h] [bp-198h]@1
  bool bUpdate[8]; // [sp+20h] [bp-178h]@32
  _DWORD bSend[2]; // [sp+28h] [bp-170h]@32
  unsigned int dwNewDalant; // [sp+30h] [bp-168h]@42
  char *pszFileName; // [sp+38h] [bp-160h]@42
  char v23; // [sp+40h] [bp-158h]@4
  _STORAGE_LIST::_db_con *pOreItem; // [sp+48h] [bp-150h]@4
  unsigned int v25; // [sp+50h] [bp-148h]@4
  _base_fld *v26; // [sp+58h] [bp-140h]@4
  int j; // [sp+60h] [bp-138h]@22
  unsigned int v28; // [sp+64h] [bp-134h]@4
  unsigned int dwSub[2]; // [sp+68h] [bp-130h]@4
  float v30; // [sp+70h] [bp-128h]@4
  unsigned int v31; // [sp+74h] [bp-124h]@32
  unsigned int k; // [sp+78h] [bp-120h]@24
  int v33; // [sp+7Ch] [bp-11Ch]@27
  unsigned int dwRate; // [sp+80h] [bp-118h]@27
  unsigned int v35; // [sp+84h] [bp-114h]@27
  int v36; // [sp+88h] [bp-110h]@33
  int v37; // [sp+8Ch] [bp-10Ch]@35
  unsigned int dwPoint; // [sp+90h] [bp-108h]@41
  int v39; // [sp+94h] [bp-104h]@43
  char __t; // [sp+B0h] [bp-E8h]@43
  char v41; // [sp+B1h] [bp-E7h]@47
  __int16 v42; // [sp+B2h] [bp-E6h]@47
  int v43[40]; // [sp+B4h] [bp-E4h]@47
  float v44; // [sp+154h] [bp-44h]@16
  float v45; // [sp+158h] [bp-40h]@16
  int v46; // [sp+15Ch] [bp-3Ch]@27
  unsigned __int64 v47; // [sp+160h] [bp-38h]@33
  int nAdd; // [sp+168h] [bp-30h]@34
  int nLv; // [sp+16Ch] [bp-2Ch]@39
  int v50; // [sp+170h] [bp-28h]@39
  char *v51; // [sp+178h] [bp-20h]@42
  int v52; // [sp+180h] [bp-18h]@51
  CPlayer *v53; // [sp+1A0h] [bp+8h]@1
  unsigned __int16 v54; // [sp+1A8h] [bp+10h]@1
  char v55; // [sp+1B0h] [bp+18h]@1

  v55 = byProcessNum;
  v54 = wOreSerial;
  v53 = this;
  v4 = &v18;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = 0;
  pOreItem = 0i64;
  v25 = 0;
  v26 = 0i64;
  v6 = CPlayerDB::GetRaceCode(&v53->m_Param);
  v28 = eGetTexRate(v6) + 10000;
  *(_QWORD *)dwSub = 0i64;
  v7 = CPlayerDB::GetRaceCode(&v53->m_Param);
  eGetTex(v7);
  v30 = a4 + 1.0;
  if ( (signed int)(unsigned __int8)CPlayerDB::GetResBufferNum(&v53->m_Param) <= 0 )
  {
    pOreItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v53->m_Param.m_dbInven.m_nListNum, v54);
    if ( pOreItem )
    {
      if ( pOreItem->m_byTableCode == 17 )
      {
        if ( pOreItem->m_bLock )
        {
          v23 = 11;
        }
        else
        {
          v26 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, pOreItem->m_wItemIndex);
          if ( v26 )
          {
            if ( (unsigned __int8)v55 <= pOreItem->m_dwDur )
            {
              v44 = (float)*(signed int *)&v26[4].m_strCode[4];
              v45 = v44 / 1000.0;
              v8 = (float)(v44 + (float)(v45 * (float)eGetMgrValue())) * (float)(unsigned __int8)v55;
              v9 = 0i64;
              if ( v8 > 9.223372e18 )
              {
                v8 = v8 - 9.223372e18;
                v9 = 0x7FFFFFFFFFFFFFFFi64;
                if ( v8 > 9.223372e18 )
                {
                  v8 = v8 - 9.223372e18;
                  v9 = -2i64;
                }
              }
              *(_QWORD *)dwSub = v28 * (v9 + (unsigned __int64)(unsigned int)(signed int)ffloor(v8)) / 0x2710;
              v10 = CPlayerDB::GetDalant(&v53->m_Param);
              if ( *(_QWORD *)dwSub > (unsigned __int64)v10 )
                v23 = 3;
            }
            else
            {
              v23 = 2;
            }
          }
          else
          {
            v23 = 1;
          }
        }
      }
      else
      {
        v23 = 1;
      }
    }
    else
    {
      v23 = 1;
    }
  }
  else
  {
    v23 = 4;
  }
  if ( !v23 )
  {
    for ( j = 0; j < (unsigned __int8)v55; ++j )
    {
      v25 = v26[4].m_dwIndex + rand() % (signed int)(*(_DWORD *)&v26[4].m_strCode[0] - v26[4].m_dwIndex + 1);
      for ( k = 0; (signed int)k < (signed int)v25; ++k )
      {
        v33 = rand();
        v46 = v33 << 16;
        dwRate = rand() + v46;
        v35 = COreCuttingTable::GetOreIndexFromRate(&stru_1799C6700, v26->m_dwIndex, dwRate);
        if ( v35 != -1 && (signed int)v53->m_Param.m_wCuttingResBuffer[v35] < 255 )
          ++v53->m_Param.m_wCuttingResBuffer[v35];
      }
    }
    LOBYTE(bSend[0]) = 0;
    bUpdate[0] = 0;
    v31 = CPlayer::Emb_AlterDurPoint(v53, 0, pOreItem->m_byStorageIndex, -(unsigned __int8)v55, 0, 0);
    CPlayer::SubDalant(v53, dwSub[0]);
    if ( !v53->m_byUserDgr )
    {
      v47 = eGetMgrValue() + 1000;
      v36 = *(_QWORD *)dwSub / v47;
      if ( v36 > 0 )
      {
        nAdd = -v36;
        v11 = CPlayerDB::GetRaceCode(&v53->m_Param);
        eAddDalant(v11, nAdd);
      }
    }
    v37 = CPlayerDB::GetLevel(&v53->m_Param);
    if ( v37 == 30 || v37 == 40 || v37 == 50 || v37 == 60 )
    {
      nLv = CPlayerDB::GetLevel(&v53->m_Param);
      v50 = CPlayerDB::GetRaceCode(&v53->m_Param);
      v12 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v12, v50, nLv, dwSub[0]);
    }
    v13 = CActionPointSystemMgr::Instance();
    if ( CActionPointSystemMgr::GetEventStatus(v13, 0) == 2 )
    {
      v14 = CUserDB::GetActPoint(v53->m_pUserDB, 0);
      dwPoint = (unsigned __int8)v55 * *(_DWORD *)&v26[3].m_strCode[36] + v14;
      CUserDB::Update_User_Action_Point(v53->m_pUserDB, 0, dwPoint);
      CPlayer::SendMsg_Alter_Action_Point(v53, 0, dwPoint);
    }
    v51 = v53->m_szItemHistoryFileName;
    v15 = CPlayerDB::GetDalant(&v53->m_Param);
    v16 = v53->m_ObjID.m_wIndex;
    pszFileName = v51;
    dwNewDalant = v15;
    bSend[0] = dwSub[0];
    *(_QWORD *)bUpdate = v53->m_Param.m_wCuttingResBuffer;
    CMgrAvatorItemHistory::cut_item(
      &CPlayer::s_MgrItemHistory,
      v16,
      pOreItem,
      (unsigned __int8)v55,
      *(unsigned __int16 **)bUpdate,
      dwSub[0],
      v15,
      v51);
    if ( v53->m_pUserDB )
    {
      v39 = 0;
      `vector constructor iterator'(&__t, 8ui64, 20, (void *(__cdecl *)(void *))_CUTTING_DB_BASE::_LIST::_LIST);
      for ( j = 0; j < GetMaxResKind() && v39 < 20; ++j )
      {
        if ( (signed int)v53->m_Param.m_wCuttingResBuffer[j] > 0 )
        {
          *(&v41 + 8 * v39) = 18;
          *(&v42 + 4 * v39) = j;
          v43[2 * v39++] = v53->m_Param.m_wCuttingResBuffer[j];
        }
      }
      CUserDB::Update_CuttingPush(v53->m_pUserDB, v39, (_CUTTING_DB_BASE::_LIST *)&__t);
    }
    if ( v26 )
    {
      v52 = (unsigned __int8)v55;
      v17 = CPlayerDB::GetRaceCode(&v53->m_Param);
      eAddCutOre(v17, v26[3].m_strCode[0], v52);
    }
  }
  CPlayer::SendMsg_OreCuttingResult(v53, v23, v31, dwSub[0]);
}
