/*
 * Function: ?MessageRepresentativeBitLength@?$DL_SignatureSchemeBase@VPK_Verifier@CryptoPP@@V?$DL_PublicKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C0B0
 */

__int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::MessageRepresentativeBitLength(__int64 a1)
{
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v1; // rax@1
  CryptoPP::Integer *v2; // rax@1

  v1 = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *)(a1 + 16));
  LODWORD(v2) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))v1->vfptr[8].__vecDelDtor)(v1);
  return CryptoPP::Integer::BitCount(v2);
}
