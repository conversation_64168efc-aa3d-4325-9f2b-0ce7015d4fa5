/*
 * Function: ?SendMsg_InvenDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D8F70
 */

void __fastcall CPlayer::SendMsg_InvenDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@5
  unsigned __int16 v4; // ax@9
  __int64 v5; // [sp+0h] [bp-8C8h]@1
  _inven_download_result_zocl v6; // [sp+40h] [bp-888h]@4
  int v7; // [sp+884h] [bp-44h]@4
  int j; // [sp+888h] [bp-40h]@4
  char *v9; // [sp+890h] [bp-38h]@6
  char pbyType; // [sp+8A4h] [bp-24h]@9
  char v11; // [sp+8A5h] [bp-23h]@9
  CPlayer *v12; // [sp+8D0h] [bp+8h]@1

  v12 = this;
  v1 = &v5;
  for ( i = 560i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12->m_bInvenDownload = 1;
  _inven_download_result_zocl::_inven_download_result_zocl(&v6);
  v6.byRetCode = 0;
  v6.byBagNum = v12->m_Param.m_dbChar.m_byUseBagNum;
  v7 = 0;
  for ( j = 0; ; ++j )
  {
    v3 = CPlayerDB::GetUseSlot(&v12->m_Param);
    if ( j >= (unsigned __int8)v3 )
      break;
    v9 = &v12->m_Param.m_dbInven.m_pStorageList[j].m_bLoad;
    if ( *v9 )
    {
      v6.ItemSlotInfo[v7].byTableCode = v9[1];
      v6.ItemSlotInfo[v7].sClientIndex = v9[2];
      v6.ItemSlotInfo[v7].wItemIndex = *(_WORD *)(v9 + 3);
      v6.ItemSlotInfo[v7].dwUptInfo = *(_DWORD *)(v9 + 13);
      v6.ItemSlotInfo[v7].dwDurPoint = *(_QWORD *)(v9 + 5);
      v6.ItemSlotInfo[v7].byCsMethod = v9[32];
      v6.ItemSlotInfo[v7++].dwT = *(_DWORD *)(v9 + 33);
    }
  }
  v6.bySlotNum = v7;
  pbyType = 3;
  v11 = 6;
  v4 = _inven_download_result_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &v6.byRetCode, v4);
}
