/*
 * Function: ?Init@CMonsterAI@@UEAAXXZ
 * Address: 0x14014FAC0
 */

void __fastcall CMonsterAI::Init(CMonsterAI *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMonsterAI *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  Us_HFSM::Init((Us_HFSM *)&v4->vfptr);
  CPathMgr::Init(&v4->m_PathFinder);
  v4->m_pAsistMonster = 0i64;
  v4->m_nCurPathFindFailCount = 0;
}
