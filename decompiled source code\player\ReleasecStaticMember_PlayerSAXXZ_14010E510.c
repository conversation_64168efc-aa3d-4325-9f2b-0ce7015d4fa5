/*
 * Function: ?Release@cStaticMember_Player@@SAXXZ
 * Address: 0x14010E510
 */

void cStaticMember_Player::Release(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-48h]@1
  cStaticMember_Player *v3; // [sp+20h] [bp-28h]@5
  struct cStaticMember_Player *v4; // [sp+28h] [bp-20h]@5
  void *v5; // [sp+30h] [bp-18h]@6

  v0 = &v2;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  if ( cStaticMember_Player::_pInstance )
  {
    v4 = cStaticMember_Player::_pInstance;
    v3 = cStaticMember_Player::_pInstance;
    if ( cStaticMember_Player::_pInstance )
      v5 = cStaticMember_Player::`scalar deleting destructor'(v3, 1u);
    else
      v5 = 0i64;
    cStaticMember_Player::_pInstance = 0i64;
  }
}
