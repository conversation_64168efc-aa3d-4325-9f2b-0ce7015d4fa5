/*
 * Function: ?InsertChangeClassLogAfterInitClass@CRFWorldDatabase@@QEAA_NKEPEAD0HEGEEEEE@Z
 * Address: 0x1404A0E90
 */

bool __fastcall CRFWorldDatabase::InsertChangeClassLogAfterInitClass(CRFWorldDatabase *this, unsigned int dwCharacSerial, char byType, char *szPrevClass, char *szNextClass, int nClassInitCnt, char byLastClassGrade, unsigned __int16 dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec)
{
  __int64 *v13; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v16; // [sp+0h] [bp-4B8h]@1
  char *v17; // [sp+20h] [bp-498h]@4
  char *v18; // [sp+28h] [bp-490h]@4
  int v19; // [sp+30h] [bp-488h]@4
  int v20; // [sp+38h] [bp-480h]@4
  int v21; // [sp+40h] [bp-478h]@4
  int v22; // [sp+48h] [bp-470h]@4
  int v23; // [sp+50h] [bp-468h]@4
  int v24; // [sp+58h] [bp-460h]@4
  int v25; // [sp+60h] [bp-458h]@4
  int v26; // [sp+68h] [bp-450h]@4
  char Dest; // [sp+80h] [bp-438h]@4
  unsigned __int64 v28; // [sp+490h] [bp-28h]@4
  CRFWorldDatabase *v29; // [sp+4C0h] [bp+8h]@1

  v29 = this;
  v13 = &v16;
  for ( i = 298i64; i; --i )
  {
    *(_DWORD *)v13 = -858993460;
    v13 = (__int64 *)((char *)v13 + 4);
  }
  v28 = (unsigned __int64)&v16 ^ _security_cookie;
  v26 = (unsigned __int8)bySec;
  v25 = (unsigned __int8)byMin;
  v24 = (unsigned __int8)byHour;
  v23 = (unsigned __int8)byDay;
  v22 = (unsigned __int8)byMonth;
  v21 = dwYear;
  v20 = (unsigned __int8)byLastClassGrade;
  v19 = nClassInitCnt;
  v18 = szNextClass;
  v17 = szPrevClass;
  sprintf(
    &Dest,
    "{ CALL pInsert_ClassLogAfterInitClass ( %d, %d, '%s', '%s', %d, %d, '%04d-%02d-%02d %02d:%02d:%02d' ) }",
    dwCharacSerial,
    (unsigned __int8)byType);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v29->vfptr, &Dest, 1);
}
