/*
 * Function: ?GetLeftTime@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@IEAAEI@Z
 * Address: 0x1403CE5C0
 */

char __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::GetLeftTime(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v4; // rax@4
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@5
  char result; // al@5
  GUILD_BATTLE::CGuildBattleSchedulePool *v7; // rax@6
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@9
  __int64 v10; // [sp+0h] [bp-38h]@1
  unsigned int dwSID; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleSchedule *v12; // [sp+28h] [bp-10h]@6
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v13; // [sp+40h] [bp+8h]@1
  unsigned int uiMapIDa; // [sp+48h] [bp+10h]@1

  uiMapIDa = uiMapID;
  v13 = this;
  v2 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
  dwSID = GUILD_BATTLE::CGuildBattleScheduleManager::GetCurScheduleID(v4, uiMapIDa);
  if ( dwSID == -1 )
  {
    v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v5,
      "CCurrentGuildBattleInfoManager::GetLeftTime( %u )CGuildBattleScheduleManager::Instance()->GetCurScheduleID( %u ) Fail!",
      uiMapIDa,
      uiMapIDa);
    result = 1;
  }
  else
  {
    v7 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
    v12 = GUILD_BATTLE::CGuildBattleSchedulePool::GetRef(v7, dwSID);
    if ( v12 )
    {
      if ( GUILD_BATTLE::CGuildBattleSchedule::GetLeftTime(
             v12,
             &v13->m_pkInfo[uiMapIDa].byLeftHour,
             &v13->m_pkInfo[uiMapIDa].byLeftMin,
             &v13->m_pkInfo[uiMapIDa].byLeftSec) )
      {
        result = 0;
      }
      else
      {
        v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v9,
          "CCurrentGuildBattleInfoManager::GetLeftTime( %u )pCurSchedule->GetLeftTime( ... )",
          uiMapIDa);
        result = 2;
      }
    }
    else
    {
      v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v8,
        "CCurrentGuildBattleInfoManager::GetLeftTime( %u )CGuildBattleSchedulePool::Instance()->GetRef(%u) NULL!",
        uiMapIDa,
        dwSID);
      result = 2;
    }
  }
  return result;
}
