/*
 * Function: SQLInstallDriverManagerW
 * Address: 0x1404DAEB4
 */

int __fastcall SQLInstallDriverManagerW(unsigned __int16 *lpszPath, unsigned __int16 cbPathMax, unsigned __int16 *pcbPathOut)
{
  unsigned __int16 *v3; // rsi@1
  unsigned __int16 *v4; // rbx@1
  unsigned __int16 v5; // di@1
  __int64 (__cdecl *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszPath;
  v4 = pcbPathOut;
  v5 = cbPathMax;
  v6 = ODBC___GetSetupProc("SQLInstallDriverManagerW");
  if ( v6 )
    result = ((int (__fastcall *)(unsigned __int16 *, _QWORD, unsigned __int16 *))v6)(v3, v5, v4);
  else
    result = 0;
  return result;
}
