/*
 * Function: __security_init_cookie
 * Address: 0x1404DE390
 */

void __cdecl _security_init_cookie()
{
  _FILETIME SystemTimeAsFileTime; // [sp+20h] [bp-28h]@1
  uintptr_t v1; // [sp+28h] [bp-20h]@3
  LARGE_INTEGER PerformanceCount; // [sp+30h] [bp-18h]@3

  SystemTimeAsFileTime = 0i64;
  if ( _security_cookie == 47936899621426i64 )
  {
    GetSystemTimeAsFileTime(&SystemTimeAsFileTime);
    v1 = (uintptr_t)SystemTimeAsFileTime;
    v1 ^= GetCurrentProcessId();
    v1 ^= GetCurrentThreadId();
    v1 ^= GetTickCount();
    QueryPerformanceCounter(&PerformanceCount);
    v1 ^= PerformanceCount.QuadPart;
    v1 &= 0xFFFFFFFFFFFFui64;
    if ( v1 == 47936899621426i64 )
      v1 = 47936899621427i64;
    _security_cookie = v1;
    _security_cookie_complement = ~v1;
  }
  else
  {
    _security_cookie_complement = ~_security_cookie;
  }
}
