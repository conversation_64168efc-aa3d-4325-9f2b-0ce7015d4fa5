#include "../Headers/CMoneySupplyMgr.h"
#include <algorithm>
#include <cstring>

// External dependencies (these would be defined elsewhere in the project)
extern bool unk_1799C608D; // Global flag for web service availability
extern void* unk_1414F2098; // Network process instance
extern int unk_1799C9ADD; // Network connection ID
extern bool unk_1799C9ADE; // Network availability flag

// External functions (these would be implemented elsewhere)
extern "C" {
    unsigned int GetLoopTime();
    unsigned int timeGetTime();
    void memcpy_0(void* dest, const void* src, size_t size);
    void CNetProcess_LoadSendMsg(void* netProcess, int connectionId, const char* msgType, const char* data, unsigned int size);
}

namespace NexusProtection::Economy {

    // Static member definitions
    std::unique_ptr<CMoneySupplyMgr> CMoneySupplyMgr::s_instance;
    std::mutex CMoneySupplyMgr::s_instanceMutex;

    CMoneySupplyMgr::CMoneySupplyMgr() {
        Initialize();
    }

    CMoneySupplyMgr& CMoneySupplyMgr::Instance() {
        std::lock_guard<std::mutex> lock(s_instanceMutex);
        if (!s_instance) {
            s_instance = std::unique_ptr<CMoneySupplyMgr>(new CMoneySupplyMgr());
        }
        return *s_instance;
    }

    void CMoneySupplyMgr::DestroyInstance() {
        std::lock_guard<std::mutex> lock(s_instanceMutex);
        s_instance.reset();
    }

    void CMoneySupplyMgr::Initialize() {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_lastSendTime = std::chrono::steady_clock::now();
        m_systemStartTime = std::chrono::steady_clock::now();
        
        m_currentData.Initialize();
        m_sendData.Initialize();
    }

    void CMoneySupplyMgr::LoopMoneySupply() {
        if (!unk_1799C608D) {
            return; // Web service not available
        }

        auto currentTime = std::chrono::steady_clock::now();
        auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_lastSendTime);

        if (timeSinceLastSend >= SEND_INTERVAL) {
            std::lock_guard<std::mutex> lock(m_dataMutex);
            
            // Copy current data to send buffer
            m_sendData = m_currentData;
            
            // Reset current data for new collection period
            m_currentData.Initialize();
            
            // Send data to web service
            SendMoneySupplyDataToWeb(m_sendData);
            
            m_lastSendTime = currentTime;
        }
    }

    void CMoneySupplyMgr::UpdateSellData(RaceType race, int32_t level, const std::string& className, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_currentData.AddAmount(MoneyType::Sell, amount);
        UpdateTradeStatistics(0, race, level, className); // Sell is index 0
    }

    void CMoneySupplyMgr::UpdateBuyData(RaceType race, int32_t level, const std::string& className, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_currentData.AddAmount(MoneyType::Buy, amount);
        UpdateTradeStatistics(3, race, level, className); // Buy is index 3
    }

    void CMoneySupplyMgr::UpdateBuyUnitData(int32_t level, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_currentData.AddAmount(MoneyType::BuyUnit, amount);
        m_currentData.buyUnitLevelStats.IncrementLevel(level);
    }

    void CMoneySupplyMgr::UpdateFeeMoneyData(RaceType race, int32_t level, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_currentData.AddAmount(MoneyType::Fee, amount);
        m_currentData.feeLevelStats.IncrementLevel(level);
        m_currentData.feeRaceStats.IncrementRace(race);
    }

    void CMoneySupplyMgr::UpdateQuestRewardMoneyData(RaceType race, int32_t level, const std::string& className, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_currentData.AddAmount(MoneyType::QuestReward, amount);
        UpdateTradeStatistics(1, race, level, className); // Quest reward is index 1
    }

    void CMoneySupplyMgr::UpdateGateRewardMoneyData(RaceType race, int32_t level, const std::string& className, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_currentData.AddAmount(MoneyType::GateReward, amount);
        UpdateTradeStatistics(2, race, level, className); // Gate reward is index 2
    }

    void CMoneySupplyMgr::UpdateHonorGuildMoneyData(TradeType tradeType, RaceType race, uint32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        if (tradeType == TradeType::Dalant) {
            m_currentData.AddAmount(MoneyType::HonorGuildDalant, amount);
        } else if (tradeType == TradeType::Gold) {
            m_currentData.AddAmount(MoneyType::HonorGuildGold, amount);
        }
        
        m_currentData.IncrementHonorGuildRace(static_cast<uint8_t>(tradeType), race);
    }

    void CMoneySupplyMgr::SendMoneySupplyDataToWeb(const MoneySupplyData& data) {
        if (!unk_1799C9ADE) {
            return; // Network not available
        }

        // Convert to legacy format for network transmission
        _MONEY_SUPPLY_DATA legacyData;
        ConvertToLegacy(data, legacyData);

        // Prepare network message
        char msgType[2] = {51, 20}; // Message type for money supply data
        
        // Send to web service
        CNetProcess_LoadSendMsg(unk_1414F2098, unk_1799C9ADD, msgType, 
                               reinterpret_cast<const char*>(&legacyData), sizeof(legacyData));
    }

    void CMoneySupplyMgr::UpdateTradeStatistics(size_t tradeIndex, RaceType race, int32_t level, const std::string& className) {
        if (tradeIndex >= m_currentData.tradeStats.size()) {
            return;
        }

        auto& stats = m_currentData.GetTradeStats(tradeIndex);
        stats.levelStats.IncrementLevel(level);
        stats.raceStats.IncrementRace(race);
        
        // Note: className could be used for additional statistics if needed
        (void)className; // Suppress unused parameter warning for now
    }

    RaceType CMoneySupplyMgr::ConvertLegacyRace(char byRace) const noexcept {
        switch (byRace) {
            case 0: return RaceType::Bellato;
            case 1: return RaceType::Cora;
            case 2: return RaceType::Accretia;
            default: return RaceType::Bellato; // Default fallback
        }
    }

    TradeType CMoneySupplyMgr::ConvertLegacyTradeType(char byTradeType) const noexcept {
        switch (byTradeType) {
            case 0: return TradeType::Dalant;
            case 1: return TradeType::Gold;
            default: return TradeType::Dalant; // Default fallback
        }
    }

    // Legacy C interface implementations
    void CMoneySupplyMgr::UpdateSellData(char byRace, int nLv, char* szClass, unsigned int nAmount) {
        std::string className = szClass ? szClass : "";
        UpdateSellData(ConvertLegacyRace(byRace), nLv, className, nAmount);
    }

    void CMoneySupplyMgr::UpdateBuyData(char byRace, int nLv, char* szClass, unsigned int nAmount) {
        std::string className = szClass ? szClass : "";
        UpdateBuyData(ConvertLegacyRace(byRace), nLv, className, nAmount);
    }

    void CMoneySupplyMgr::UpdateBuyUnitData(int nLv, unsigned int nAmount) {
        UpdateBuyUnitData(nLv, nAmount);
    }

    void CMoneySupplyMgr::UpdateFeeMoneyData(char byRace, int nLv, unsigned int nAmount) {
        UpdateFeeMoneyData(ConvertLegacyRace(byRace), nLv, nAmount);
    }

    void CMoneySupplyMgr::UpdateQuestRewardMoneyData(char byRace, int nLv, char* szClass, unsigned int nAmount) {
        std::string className = szClass ? szClass : "";
        UpdateQuestRewardMoneyData(ConvertLegacyRace(byRace), nLv, className, nAmount);
    }

    void CMoneySupplyMgr::UpdateGateRewardMoneyData(char byRace, int nLv, char* szClass, unsigned int nAmount) {
        std::string className = szClass ? szClass : "";
        UpdateGateRewardMoneyData(ConvertLegacyRace(byRace), nLv, className, nAmount);
    }

    void CMoneySupplyMgr::UpdateHonorGuildMoneyData(char byTradeType, char byRace, unsigned int nAmount) {
        UpdateHonorGuildMoneyData(ConvertLegacyTradeType(byTradeType), ConvertLegacyRace(byRace), nAmount);
    }

} // namespace NexusProtection::Economy

// Global legacy compatibility
NexusProtection::Economy::CMoneySupplyMgr* g_pMoneySupplyMgr = nullptr;

// Legacy C interface implementation
extern "C" {
    static NexusProtection::Economy::_MONEY_SUPPLY_DATA s_legacyCurrentData;
    static NexusProtection::Economy::_MONEY_SUPPLY_DATA s_legacySendData;
    static uint32_t s_legacyLastSendTime = 0;
    static uint32_t s_legacySystemStartTime = 0;

    CMoneySupplyMgr_Legacy* CMoneySupplyMgr_Instance() {
        // Initialize global pointer if needed
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }

        // Return a legacy-compatible structure
        static CMoneySupplyMgr_Legacy s_legacyInstance;
        s_legacyInstance.vfptr = nullptr; // Virtual function table not needed for modern implementation

        // Convert current data to legacy format
        NexusProtection::Economy::ConvertToLegacy(g_pMoneySupplyMgr->GetCurrentData(), s_legacyInstance.m_MS_data);
        NexusProtection::Economy::ConvertToLegacy(g_pMoneySupplyMgr->GetSendData(), s_legacyInstance.m_MS_Senddata);

        // Convert timing information
        auto lastSend = g_pMoneySupplyMgr->GetLastSendTime();
        auto systemStart = g_pMoneySupplyMgr->GetSystemStartTime();
        auto now = std::chrono::steady_clock::now();

        s_legacyInstance.m_dwLastSendTime = static_cast<uint32_t>(
            std::chrono::duration_cast<std::chrono::milliseconds>(lastSend.time_since_epoch()).count());
        s_legacyInstance.m_dwSystemOperStartTime = static_cast<uint32_t>(
            std::chrono::duration_cast<std::chrono::milliseconds>(systemStart.time_since_epoch()).count());

        return &s_legacyInstance;
    }

    void CMoneySupplyMgr_Initialize(CMoneySupplyMgr_Legacy* mgr) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->Initialize();
    }

    void CMoneySupplyMgr_LoopMoneySupply(CMoneySupplyMgr_Legacy* mgr) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->LoopMoneySupply();
    }

    void CMoneySupplyMgr_UpdateSellData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateSellData(byRace, nLv, szClass, nAmount);
    }

    void CMoneySupplyMgr_UpdateBuyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateBuyData(byRace, nLv, szClass, nAmount);
    }

    void CMoneySupplyMgr_UpdateBuyUnitData(CMoneySupplyMgr_Legacy* mgr, int nLv, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateBuyUnitData(nLv, nAmount);
    }

    void CMoneySupplyMgr_UpdateFeeMoneyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateFeeMoneyData(byRace, nLv, nAmount);
    }

    void CMoneySupplyMgr_UpdateQuestRewardMoneyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateQuestRewardMoneyData(byRace, nLv, szClass, nAmount);
    }

    void CMoneySupplyMgr_UpdateGateRewardMoneyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateGateRewardMoneyData(byRace, nLv, szClass, nAmount);
    }

    void CMoneySupplyMgr_UpdateHonorGuildMoneyData(CMoneySupplyMgr_Legacy* mgr, char byTradeType, char byRace, unsigned int nAmount) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr) {
            g_pMoneySupplyMgr = &NexusProtection::Economy::CMoneySupplyMgr::Instance();
        }
        g_pMoneySupplyMgr->UpdateHonorGuildMoneyData(byTradeType, byRace, nAmount);
    }

    void CMoneySupplyMgr_SendMsg_MoneySupplyDataToWeb(CMoneySupplyMgr_Legacy* mgr, _MONEY_SUPPLY_DATA* pMSData) {
        (void)mgr; // Suppress unused parameter warning
        if (!g_pMoneySupplyMgr || !pMSData) {
            return;
        }

        // Convert legacy data to modern format and send
        NexusProtection::Economy::MoneySupplyData modernData;
        NexusProtection::Economy::ConvertFromLegacy(*pMSData, modernData);
        g_pMoneySupplyMgr->SendMoneySupplyDataToWeb(modernData);
    }
}
