/*
 * Function: j_?CancelPlayerRaceBuff@CRaceBuffByHolyQuestProcedure@@QEAAHPEAVCPlayer@@W4RESULT_TYPE@CRaceBuffInfoByHolyQuestfGroup@@I@Z
 * Address: 0x140005899
 */

int __fastcall CRaceBuffByHolyQuestProcedure::CancelPlayerRaceBuff(CRaceBuffByHolyQuestProcedure *this, CPlayer *pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiReleaseLv)
{
  return CRaceBuffByHolyQuestProcedure::CancelPlayerRaceBuff(this, pkPlayer, eReleaseType, uiReleaseLv);
}
