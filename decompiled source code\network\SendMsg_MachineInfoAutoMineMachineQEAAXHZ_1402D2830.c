/*
 * Function: ?SendMsg_MachineInfo@AutoMineMachine@@QEAAXH@Z
 * Address: 0x1402D2830
 */

void __fastcall AutoMineMachine::SendMsg_MachineInfo(AutoMineMachine *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@15
  __int64 v5; // [sp+0h] [bp-468h]@1
  _pt_automine_info_zocl Dst; // [sp+40h] [bp-428h]@4
  int nP; // [sp+424h] [bp-44h]@4
  int nS; // [sp+428h] [bp-40h]@6
  TInvenSlot<_INVENKEY> *v9; // [sp+430h] [bp-38h]@9
  void *Src; // [sp+438h] [bp-30h]@11
  char pbyType; // [sp+444h] [bp-24h]@15
  char v12; // [sp+445h] [bp-23h]@15
  AutoMineMachine *v13; // [sp+470h] [bp+8h]@1
  int dwClientIndex; // [sp+478h] [bp+10h]@1

  dwClientIndex = n;
  v13 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _pt_automine_info_zocl::_pt_automine_info_zocl(&Dst);
  Dst.INFO.byCollisionType = v13->m_byCollisionType;
  Dst.INFO.byRace = v13->m_byRace;
  Dst.INFO.dwGuildSerial = v13->m_pOwnerGuild->m_dwSerial;
  Dst.INFO.dwGMasterSerial = v13->m_pOwnerGuild->m_MasterData.dwSerial;
  Dst.INFO.bWorking = v13->m_bRunning;
  Dst.INFO.bySelectedOre = v13->m_bySelectedOre;
  Dst.INFO.dwBatteryGage = v13->m_Battery.m_nCurGage;
  Dst.INFO.bySlotCnt = 0;
  for ( nP = 0; nP < 2; ++nP )
  {
    for ( nS = 0; nS < 40; ++nS )
    {
      v9 = TInventory<_INVENKEY>::get_slot(&v13->m_Inven, nP, nS);
      if ( v9 && !TInvenSlot<_INVENKEY>::is_empty(v9) )
      {
        Src = TInvenSlot<_INVENKEY>::get_pitem(v9);
        if ( Src )
        {
          Dst.INFO.slot[(unsigned __int8)Dst.INFO.bySlotCnt].nLumpIndex = nP;
          memcpy_0(&Dst.INFO.slot[(unsigned __int8)Dst.INFO.bySlotCnt].item, Src, 4ui64);
          Dst.INFO.slot[(unsigned __int8)Dst.INFO.bySlotCnt].nOverlapNum = TInvenSlot<_INVENKEY>::get_overlapnum(v9);
          ++Dst.INFO.bySlotCnt;
        }
      }
    }
  }
  pbyType = 14;
  v12 = 16;
  v4 = _pt_automine_info_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &Dst.INFO.byCollisionType, v4);
}
