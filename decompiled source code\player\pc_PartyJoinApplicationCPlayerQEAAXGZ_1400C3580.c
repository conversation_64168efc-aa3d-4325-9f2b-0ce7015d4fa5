/*
 * Function: ?pc_PartyJoinApplication@CPlayer@@QEAAXG@Z
 * Address: 0x1400C3580
 */

void __usercall CPlayer::pc_PartyJoinApplication(CPlayer *this@<rcx>, unsigned __int16 wBossIndex@<dx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@13
  CNationSettingManager *v6; // rax@20
  int v7; // eax@22
  __int64 v8; // [sp+0h] [bp-48h]@1
  CPlayer *v9; // [sp+20h] [bp-28h]@7
  int v10; // [sp+28h] [bp-20h]@13
  __int16 *pDest1; // [sp+30h] [bp-18h]@20
  float v12; // [sp+38h] [bp-10h]@22
  CPlayer *pJoiner; // [sp+50h] [bp+8h]@1
  unsigned __int16 v14; // [sp+58h] [bp+10h]@1

  v14 = wBossIndex;
  pJoiner = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pJoiner->m_id.wIndex) == 99 )
  {
    CPlayer::SendMsg_TLStatusPenalty(pJoiner, 1);
    return;
  }
  if ( !CPlayer::IsPunished(pJoiner, 2, 1) )
  {
    v9 = &g_Player + v14;
    if ( v9->m_bLive )
    {
      if ( !v9->m_bCorpse )
      {
        if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v9->m_id.wIndex) == 99 )
        {
          CPlayer::SendMsg_TLStatusPenalty(pJoiner, 3);
          return;
        }
        if ( !CPlayer::IsPunished(v9, 2, 0) )
        {
          v10 = CPlayerDB::GetRaceCode(&pJoiner->m_Param);
          v5 = CPlayerDB::GetRaceCode(&v9->m_Param);
          if ( v10 == v5
            && !CPartyPlayer::IsPartyMode(pJoiner->m_pPartyMgr)
            && CPartyPlayer::IsPartyBoss(v9->m_pPartyMgr) )
          {
            if ( v9->m_byUserDgr )
            {
              if ( !pJoiner->m_byUserDgr )
                return;
            }
            else if ( pJoiner->m_byUserDgr )
            {
              return;
            }
            pDest1 = &pJoiner->m_pUserDB->m_BillingInfo.iType;
            v6 = CTSingleton<CNationSettingManager>::Instance();
            if ( !CNationSettingManager::IsPersonalFreeFixedAmountBillingType(v6, pDest1, 0i64)
              && !v9->m_pPartyMgr->m_bLock )
            {
              _effect_parameter::GetEff_Have(&pJoiner->m_EP, 53);
              v12 = a3;
              v7 = ((int (__fastcall *)(CPlayer *))pJoiner->vfptr->GetLevel)(pJoiner);
              if ( CPartyPlayer::IsJoinPartyLevel(v9->m_pPartyMgr, v7, v12) )
                CPlayer::SendMsg_PartyJoinApplicationQuestion(v9, pJoiner);
              else
                CPlayer::SendMsg_PartyJoinFailLevel(pJoiner);
            }
          }
        }
      }
    }
  }
}
