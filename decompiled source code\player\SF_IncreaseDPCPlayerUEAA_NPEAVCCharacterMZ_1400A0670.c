/*
 * Function: ?SF_IncreaseDP@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x1400A0670
 */

char __fastcall CPlayer::SF_IncreaseDP(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v6; // eax@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPlayer *v8; // [sp+20h] [bp-18h]@6
  int nDP; // [sp+28h] [bp-10h]@6
  CPlayer *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pDstObj->m_ObjID.m_byID )
  {
    result = 0;
  }
  else
  {
    v8 = (CPlayer *)pDstObj;
    nDP = (signed int)ffloor((float)CPlayer::GetMaxDP((CPlayer *)pDstObj) * fEffectValue);
    v6 = CPlayer::GetDP((CPlayer *)pDstObj);
    nDP += v6;
    CPlayer::SetDP(v8, nDP, 0);
    CPlayer::SendMsg_Recover(v10);
    result = 1;
  }
  return result;
}
