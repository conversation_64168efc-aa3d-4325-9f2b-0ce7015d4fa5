/*
 * Function: ?Regist@CandidateMgr@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1402B1BA0
 */

char __usercall CandidateMgr::Regist@<al>(CandidateMgr *this@<rcx>, CPlayer *pOne@<rdx>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@4
  char result; // al@5
  char v7; // al@6
  char v8; // al@6
  char *v9; // rax@6
  PatriarchElectProcessor *v10; // rax@9
  unsigned int v11; // eax@9
  int v12; // eax@9
  __int64 v13; // [sp+0h] [bp-78h]@1
  unsigned int dwS[2]; // [sp+20h] [bp-58h]@9
  _candidate_info *v15; // [sp+30h] [bp-48h]@4
  _qry_case_insert_candidate v16; // [sp+48h] [bp-30h]@9
  CandidateMgr *v17; // [sp+80h] [bp+8h]@1
  CPlayer *v18; // [sp+88h] [bp+10h]@1

  v18 = pOne;
  v17 = this;
  v3 = &v13;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  v15 = CandidateMgr::GetEmpty(v17, v5);
  if ( v15 )
  {
    v15->bLoad = 1;
    v7 = CPlayerDB::GetRaceCode(&v18->m_Param);
    v15->byRace = v7;
    v15->dwAvatorSerial = CPlayerDB::GetCharSerial(&v18->m_Param);
    v15->byGrade = v18->m_Param.m_byPvPGrade;
    v15->dwRank = CPlayerDB::GetPvpRank(&v18->m_Param);
    CPlayerDB::GetPvPPoint(&v18->m_Param);
    v15->dPvpPoint = a3;
    v8 = CPlayerDB::GetLevel(&v18->m_Param);
    v15->byLevel = v8;
    v15->eStatus = 1;
    v9 = CPlayerDB::GetCharNameW(&v18->m_Param);
    strcpy_s(v15->wszName, 0x11ui64, v9);
    if ( v18->m_Param.m_pGuild )
    {
      v15->dwGuildSerial = v18->m_Param.m_pGuild->m_dwSerial;
      strcpy_s(v15->wszGuildName, 0x11ui64, v18->m_Param.m_pGuild->m_wszName);
    }
    else
    {
      v15->dwGuildSerial = -1;
      memset_0(v15->wszGuildName, 0, 0x11ui64);
    }
    v17->_pkCandidateLink_1st[v15->byRace][v17->_nCandidateCnt_1st[v15->byRace]++] = v15;
    v10 = PatriarchElectProcessor::Instance();
    v11 = PatriarchElectProcessor::GetElectSerial(v10);
    dwS[0] = v15->dwAvatorSerial;
    _qry_case_insert_candidate::_qry_case_insert_candidate(&v16, v15->byRace, v18->m_id.wIndex, v11, dwS[0]);
    v12 = _qry_case_insert_candidate::size(&v16);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 117, &v16.byRace, v12);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
