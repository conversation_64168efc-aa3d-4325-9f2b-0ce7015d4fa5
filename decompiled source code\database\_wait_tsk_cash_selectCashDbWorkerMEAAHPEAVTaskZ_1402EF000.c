/*
 * Function: ?_wait_tsk_cash_select@CashDbWorker@@MEAAHPEAVTask@@@Z
 * Address: 0x1402EF000
 */

__int64 __fastcall CashDbWorker::_wait_tsk_cash_select(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  _param_cash_select *rParam; // [sp+20h] [bp-18h]@4
  CashDbWorker *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  rParam = (_param_cash_select *)Task::GetTaskBuf(pkTsk);
  return CRFCashItemDatabase::CallProc_RFOnlineAuth(v7->_pkDb, rParam) != 0;
}
