/*
 * Function: j_?Create@?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@UEAA_NPEAU?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@2@P6AKPEAX@Z@Z
 * Address: 0x14000AEA2
 */

bool __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Create(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *this, US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *pThreadParam, unsigned int (__cdecl *pDefaultThread)(void *))
{
  return US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Create(
           this,
           pThreadParam,
           pDefaultThread);
}
