/*
 * Function: luaL_newmetatable
 * Address: 0x140539520
 */

signed __int64 __fastcall luaL_newmetatable(__int64 a1)
{
  __int64 v1; // rbx@1
  signed __int64 result; // rax@2

  v1 = a1;
  lua_getfield(a1, -10000);
  if ( (unsigned int)lua_type(v1, -1) )
  {
    result = 0i64;
  }
  else
  {
    lua_settop(v1, -2);
    lua_createtable(v1, 0, 0);
    lua_pushvalue(v1, -1);
    lua_setfield(v1, -10000);
    result = 1i64;
  }
  return result;
}
