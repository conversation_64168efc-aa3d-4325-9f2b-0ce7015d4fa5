/*
 * Function: ?_delete_from_inven@CashDbWorker@@AEAAXQEAVCPlayer@@PEBU_param_cash_update@@H@Z
 * Address: 0x1402F1370
 */

void __fastcall CashDbWorker::_delete_from_inven(Cash<PERSON>b<PERSON>orker *this, CPlayer *const pOne, _param_cash_update *psheet, int nNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool bDelete[8]; // [sp+20h] [bp-28h]@9
  char *strErrorCodePos; // [sp+28h] [bp-20h]@9
  int j; // [sp+30h] [bp-18h]@4
  unsigned __int16 v10; // [sp+34h] [bp-14h]@7
  _STORAGE_LIST::_db_con *v11; // [sp+38h] [bp-10h]@7
  CashDbWorker *v12; // [sp+50h] [bp+8h]@1
  CPlayer *v13; // [sp+58h] [bp+10h]@1
  _param_cash_update *v14; // [sp+60h] [bp+18h]@1
  int v15; // [sp+68h] [bp+20h]@1

  v15 = nNum;
  v14 = psheet;
  v13 = pOne;
  v12 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < v15; ++j )
  {
    v10 = v14->in_item[(signed __int64)j].out_wItemSerial;
    v11 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v13->m_Param.m_dbInven.m_nListNum, v10);
    if ( v11 )
    {
      strErrorCodePos = "CashDbWorker::_delete_from_inven";
      bDelete[0] = 1;
      if ( !CPlayer::Emb_DelStorage(v13, 0, v11->m_byStorageIndex, 0, 1, "CashDbWorker::_delete_from_inven") )
        CLogFile::Write(
          v12->_kLogger,
          "CashDbWorker::_delete_from_inven : Emb_DelStorage Failed! item::%s",
          v14->in_item[(signed __int64)j].in_strItemCode);
      *(_QWORD *)bDelete = (char *)v13 + 50608;
      CMgrAvatorItemHistory::cashitem_del_from_inven(
        &CPlayer::s_MgrItemHistory,
        v11->m_byTableCode,
        v11->m_wItemIndex,
        v14->in_item[(signed __int64)j].in_lnUID,
        v13->m_szItemHistoryFileName);
    }
    else
    {
      CLogFile::Write(v12->_kLogger, "Failed delete item::%s", v14->in_item[(signed __int64)j].in_strItemCode);
    }
  }
}
