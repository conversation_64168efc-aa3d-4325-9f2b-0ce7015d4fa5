/*
 * Function: ??0?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@VInteger@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140638D70
 */

CryptoPP::PK_Decryptor *__fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>(CryptoPP::PK_Decryptor *a1)
{
  CryptoPP::PK_Decryptor *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::PK_Decryptor::PK_Decryptor(a1);
  CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::Integer>>((__int64)&v2[1]);
  return v2;
}
