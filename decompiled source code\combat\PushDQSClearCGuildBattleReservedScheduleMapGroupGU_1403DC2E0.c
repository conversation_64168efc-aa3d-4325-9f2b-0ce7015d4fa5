/*
 * Function: ?PushDQ<PERSON>lear@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403DC2E0
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::PushDQSClear(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedulePool *v3; // rax@4
  GUILD_BATTLE::CGuildBattleSchedulePool *v4; // rax@4
  int v5; // eax@4
  __int64 v6; // [sp+0h] [bp-68h]@1
  unsigned int uiSLID; // [sp+38h] [bp-30h]@4
  unsigned int v8; // [sp+3Ch] [bp-2Ch]@4
  unsigned int v9; // [sp+40h] [bp-28h]@4
  unsigned int v10; // [sp+44h] [bp-24h]@4
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  uiSLID = GUILD_BATTLE::CGuildBattleReservedSchedule::GetID(*v11->m_ppkReservedSchedule);
  v8 = GUILD_BATTLE::CGuildBattleReservedSchedule::GetID(v11->m_ppkReservedSchedule[v11->m_uiMapCnt - 1]);
  v3 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v9 = GUILD_BATTLE::CGuildBattleSchedulePool::GetSID(v3, uiSLID, 0);
  v4 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v10 = GUILD_BATTLE::CGuildBattleSchedulePool::GetSID(v4, v8, 0x16u);
  v5 = _qry_case_updateclearguildbattleDayInfo::size((_qry_case_updateclearguildbattleDayInfo *)&uiSLID);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 36, (char *)&uiSLID, v5);
}
