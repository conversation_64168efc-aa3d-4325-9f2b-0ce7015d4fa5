/*
 * Function: ?UpdatePlayerStatus@TimeLimitMgr@@QEAA_NGKE@Z
 * Address: 0x14040EB20
 */

char __fastcall TimeLimitMgr::UpdatePlayerStatus(TimeLimitMgr *this, unsigned __int16 wIndex, unsigned int dwFatigue, char wStatus)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  Player_TL_Status *v8; // [sp+20h] [bp-18h]@4
  TimeLimitMgr *v9; // [sp+40h] [bp+8h]@1
  unsigned __int16 v10; // [sp+48h] [bp+10h]@1
  unsigned int dwFatiguea; // [sp+50h] [bp+18h]@1
  char v12; // [sp+58h] [bp+20h]@1

  v12 = wStatus;
  dwFatiguea = dwFatigue;
  v10 = wIndex;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = TimeLimitMgr::Find_Data(v9, wIndex);
  if ( v8 )
  {
    v8->m_dwFatigue = dwFatiguea;
    CUserDB::Update_UserFatigue(*(&g_Player.m_pUserDB + 6357 * v10), dwFatiguea);
    v8->m_byTL_Status = v12;
    CUserDB::Update_UserTLStatus(*(&g_Player.m_pUserDB + 6357 * v10), v12);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
