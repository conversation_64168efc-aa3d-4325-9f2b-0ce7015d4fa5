/*
 * Function: ?dev_lv@CPlayer@@QEAA_NH@Z
 * Address: 0x1400BB180
 */

char __fastcall CPlayer::dev_lv(CPlayer *this, int nLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  cStaticMember_Player *v5; // rax@11
  int v6; // eax@11
  char *v7; // rax@12
  cStaticMember_Player *v8; // rax@15
  __int64 v9; // [sp+0h] [bp-38h]@1
  char v10; // [sp+20h] [bp-18h]@7
  int nVal; // [sp+24h] [bp-14h]@10
  _base_fld *v12; // [sp+28h] [bp-10h]@10
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  int v14; // [sp+48h] [bp+10h]@1

  v14 = nLv;
  v13 = this;
  v2 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nLv > 0 )
  {
    if ( nLv <= CPlayerDB::GetLevel(&v13->m_Param) )
    {
      if ( v14 < CPlayerDB::GetLevel(&v13->m_Param) )
      {
        CPlayerDB::SetExp(&v13->m_Param, 0.0);
        v13->m_dwExpRate = 0;
        CPlayer::SetLevelD(v13, v14);
      }
    }
    else
    {
      v10 = CPlayerDB::GetLevel(&v13->m_Param);
      CPlayerDB::SetExp(&v13->m_Param, 0.0);
      v13->m_dwExpRate = 0;
      CPlayer::SetLevel(v13, v14);
    }
    nVal = v14;
    v12 = 0i64;
    while ( 1 )
    {
      v5 = cStaticMember_Player::Instance();
      v6 = cStaticMember_Player::GetMaxLv(v5);
      if ( nVal >= v6 )
        break;
      v7 = cvt_string(nVal);
      v12 = CRecordData::GetRecord(CQuestMgr::s_tblQuestHappenEvent + 8, v7);
      if ( v12 )
        break;
      *(_QWORD *)&nVal = (unsigned int)(nVal + 1);
    }
    if ( !v12 )
    {
      v8 = cStaticMember_Player::Instance();
      nVal = cStaticMember_Player::GetMaxLv(v8);
    }
    CPlayerDB::SetMaxLevel(&v13->m_Param, nVal);
    if ( v13->m_pUserDB )
      CUserDB::Update_MaxLevel(v13->m_pUserDB, nVal);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
