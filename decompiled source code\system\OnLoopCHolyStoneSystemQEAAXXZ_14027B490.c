/*
 * Function: ?OnLoop@CHolyStoneSystem@@QEAAXXZ
 * Address: 0x14027B490
 */

void __fastcall CHolyStoneSystem::OnLoop(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int v4; // [sp+20h] [bp-18h]@6
  CHolyStoneSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v5->m_tmrHSKSystem) )
    CHolyStoneSystem::HSKRespawnSystem(v5);
  v4 = CHolyStoneSystem::GetSceneCode(v5);
  if ( v4 == 1 )
  {
    CHolyStoneSystem::UpdateNotifyHolyStoneHPToRaceBoss(v5);
  }
  else if ( v4 == 2 )
  {
    CHolyStoneSystem::CheckDestroyerIsArriveMine(v5);
  }
}
