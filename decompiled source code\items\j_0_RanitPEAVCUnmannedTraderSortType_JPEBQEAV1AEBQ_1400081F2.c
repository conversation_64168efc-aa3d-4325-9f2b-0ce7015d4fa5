/*
 * Function: j_??0?$_Ranit@PEAVCUnmannedTraderSortType@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x1400081F2
 */

void __fastcall std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>::_<PERSON>t<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>(std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &> *this, std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &> *__that)
{
  std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>(
    this,
    __that);
}
