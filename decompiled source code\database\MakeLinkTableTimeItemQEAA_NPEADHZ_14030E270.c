/*
 * Function: ?MakeLinkTable@TimeItem@@QEAA_NPEADH@Z
 * Address: 0x14030E270
 */

char __fastcall TimeItem::MakeLinkTable(TimeItem *this, char *szMsg, int nSize)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@8
  std::pair<unsigned long,_TimeItem_fld *> *v7; // rax@15
  __int64 v8; // [sp+0h] [bp-C8h]@1
  int n; // [sp+30h] [bp-98h]@7
  _TimeItem_fld *v10; // [sp+38h] [bp-90h]@9
  _TimeItem_fld *_Val2; // [sp+40h] [bp-88h]@11
  int v12; // [sp+48h] [bp-80h]@11
  _base_fld *v13; // [sp+50h] [bp-78h]@13
  char *v14; // [sp+58h] [bp-70h]@7
  void *v15; // [sp+60h] [bp-68h]@4
  std::pair<int const ,_TimeItem_fld const *> _Val; // [sp+68h] [bp-60h]@15
  std::pair<unsigned long,_TimeItem_fld *> result; // [sp+78h] [bp-50h]@15
  std::pair<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0>,bool> v18; // [sp+88h] [bp-40h]@15
  __int64 v19; // [sp+A8h] [bp-20h]@4
  char *v20; // [sp+B0h] [bp-18h]@5
  TimeItem *v21; // [sp+D0h] [bp+8h]@1
  char *Dst; // [sp+D8h] [bp+10h]@1
  int v23; // [sp+E0h] [bp+18h]@1

  v23 = nSize;
  Dst = szMsg;
  v21 = this;
  v3 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = -2i64;
  v15 = operator new[](0x1160ui64);
  if ( v15 )
  {
    *(_DWORD *)v15 = 37;
    `eh vector constructor iterator'(
      (char *)v15 + 8,
      0x78ui64,
      37,
      (void (__cdecl *)(void *))stdext::hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>,
      (void (__cdecl *)(void *))stdext::hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::~hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>);
    v20 = (char *)v15 + 8;
  }
  else
  {
    v20 = 0i64;
  }
  v14 = v20;
  TimeItem::_phmapTbl = (__int64)v20;
  for ( n = 0; ; ++n )
  {
    v5 = CRecordData::GetRecordNum(&v21->_kRecTimeItem);
    if ( n >= v5 )
      break;
    v10 = (_TimeItem_fld *)CRecordData::GetRecord(&v21->_kRecTimeItem, n);
    if ( !v10 )
    {
      strcpy_s(Dst, v23, "Wrong index of time item\r\n");
      return 0;
    }
    _Val2 = v10;
    v12 = GetItemTableCode(v10->m_strLendItemCode);
    if ( v12 == -1 )
    {
      strcpy_s(Dst, v23, "Wrong string code[_TimeItem_fld::m_strLendItemCode]\r\n");
      return 0;
    }
    v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v12, _Val2->m_strLendItemCode, 7);
    if ( !v13 )
    {
      strcpy_s(Dst, v23, "Wrong string code[_TimeItem_fld::m_strLendItemCode]-NonExist\r\n");
      return 0;
    }
    v7 = std::make_pair<unsigned long,_TimeItem_fld *>(&result, v13->m_dwIndex, _Val2);
    std::pair<int const,_TimeItem_fld const *>::pair<int const,_TimeItem_fld const *>(&_Val, v7);
    stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::insert(
      (stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *)(120i64 * v12 + TimeItem::_phmapTbl),
      &v18,
      &_Val);
    std::pair<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,bool>(&v18);
  }
  return 1;
}
