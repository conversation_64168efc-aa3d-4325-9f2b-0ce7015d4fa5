/*
 * Function: ?qc_RewardExp@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x1402743F0
 */

bool __fastcall qc_RewardExp(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-108h]@1
  char poutszWord; // [sp+30h] [bp-D8h]@4
  char v8; // [sp+B4h] [bp-54h]@6
  long double pdoutVal; // [sp+C8h] [bp-40h]@8
  _dh_reward_sub_setup *v10; // [sp+D8h] [bp-30h]@13
  _dh_reward_sub_setup *v11; // [sp+E8h] [bp-20h]@11
  unsigned __int64 v12; // [sp+F0h] [bp-18h]@4
  strFILE *fstra; // [sp+110h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+118h] [bp+10h]@1

  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( strFILE::word(fstra, &poutszWord) )
  {
    v8 = 0;
    if ( !strcmp_0(&poutszWord, "one") )
      v8 = 1;
    pdoutVal = 0.0;
    if ( strFILE::word(fstra, &pdoutVal) )
    {
      if ( v8 )
        v11 = &pSetupa->m_pCurLoadQuest->RewardOne;
      else
        v11 = &pSetupa->m_pCurLoadQuest->RewardOther;
      v10 = v11;
      if ( pdoutVal >= 0.0 )
      {
        v10->dExp = pdoutVal;
        result = 1;
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
