/*
 * Function: j_?down_exp@CMgrAvatorLvHistory@@QEAAXHNGNGPEAD0@Z
 * Address: 0x14000518C
 */

void __fastcall CMgrAvatorLvHistory::down_exp(CMgrAvatorLvHistory *this, int n, long double dOldExp, unsigned __int16 wOldExpRate, long double dNewExp, unsigned __int16 wNewExpRate, char *pCause, char *pszFileName)
{
  CMgrAvatorLvHistory::down_exp(this, n, dOldExp, wOldExpRate, dNewExp, wNewExpRate, pCause, pszFileName);
}
