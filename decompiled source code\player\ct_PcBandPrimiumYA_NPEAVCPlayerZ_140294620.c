/*
 * Function: ?ct_PcBandPrimium@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294620
 */

char __fastcall ct_PcBandPrimium(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  float v4; // xmm0_4@7
  float v5; // xmm0_4@10
  __int64 v6; // [sp+0h] [bp-38h]@1
  float v7; // [sp+20h] [bp-18h]@7
  float v8; // [sp+24h] [bp-14h]@10
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = pOne;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9 && v9->m_bOper )
  {
    v4 = atof(s_pwszDstCheat[0]);
    v7 = v4;
    if ( v4 > 0.0 && v7 <= 2.0 )
    {
      v5 = atof(s_pwszDstCheat[1]);
      v8 = v5;
      if ( v5 > 0.0 && v8 <= 2.0 )
      {
        PCBANG_PRIMIUM_FAVOR::MINING_SPEED = v7;
        PCBANG_PRIMIUM_FAVOR::PLAYER_EXP = v7;
        PCBANG_PRIMIUM_FAVOR::ANIMUS_EXP = v7;
        PCBANG_PRIMIUM_FAVOR::BASE_MASTERY = v7;
        PCBANG_PRIMIUM_FAVOR::SKILL_FORCE_MASTERY = v7;
        PCBANG_PRIMIUM_FAVOR::ITEM_DROP = v8;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
