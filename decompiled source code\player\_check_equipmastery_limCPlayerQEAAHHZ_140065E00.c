/*
 * Function: ?_check_equipmastery_lim@CPlayer@@QEAAHH@Z
 * Address: 0x140065E00
 */

unsigned int __fastcall CPlayer::_check_equipmastery_lim(CPlayer *this, int EquipMasteryCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int result; // eax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  int v7; // [sp+24h] [bp-14h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = -1;
  v7 = -1;
  if ( EquipMasteryCode )
  {
    switch ( EquipMasteryCode )
    {
      case 1:
        result = CPlayer::_check_mastery_lim(v8, 0, 1);
        break;
      case 2:
        result = CPlayer::_check_mastery_lim(v8, 6, 0);
        break;
      case 3:
        result = 99;
        break;
      case 4:
        result = CPlayer::_check_mastery_lim(v8, 2, 0);
        break;
      case 5:
        result = CPlayer::_check_mastery_lim(v8, 1, 0);
        break;
      default:
        result = 99;
        break;
    }
  }
  else
  {
    result = CPlayer::_check_mastery_lim(v8, 0, 0);
  }
  return result;
}
