/*
 * Function: ?destroy@?$allocator@PEAURECV_DATA@@@std@@QEAAXPEAPEAURECV_DATA@@@Z
 * Address: 0x14031FD70
 */

void __fastcall std::allocator<RECV_DATA *>::destroy(std::allocator<RECV_DATA *> *this, RECV_DATA **_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1

  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Destroy<RECV_DATA *>(_Ptr);
}
