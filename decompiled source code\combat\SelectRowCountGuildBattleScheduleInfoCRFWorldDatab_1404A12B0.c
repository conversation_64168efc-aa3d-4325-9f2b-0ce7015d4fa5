/*
 * Function: ?SelectRowCountGuildBattleScheduleInfo@CRFWorldDatabase@@QEAAHXZ
 * Address: 0x1404A12B0
 */

signed __int64 __fastcall CRFWorldDatabase::SelectRowCountGuildBattleScheduleInfo(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v4; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@22
  SQLLEN v7; // [sp+38h] [bp-170h]@22
  __int16 v8; // [sp+44h] [bp-164h]@9
  char Dest; // [sp+60h] [bp-148h]@4
  int v10; // [sp+164h] [bp-44h]@4
  unsigned int v11; // [sp+168h] [bp-40h]@16
  unsigned int TargetValue; // [sp+174h] [bp-34h]@22
  unsigned __int64 v13; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+1B0h] [bp+8h]@1

  v14 = this;
  v1 = &v4;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v4 ^ _security_cookie;
  v10 = 0;
  sprintf(&Dest, "select count(id) from [dbo].[tbl_GuildBattleScheduleInfo]");
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v8 = SQLExecDirectA_0(v14->m_hStmtSelect, &Dest, -3);
    if ( v8 && v8 != 1 )
    {
      if ( v8 == 100 )
      {
        result = 4294967294i64;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v8, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v8, v14->m_hStmtSelect);
        result = 0i64;
      }
    }
    else
    {
      v8 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v8 && v8 != 1 )
      {
        v11 = 0;
        if ( v8 == 100 )
        {
          v11 = -3;
        }
        else
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v8, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v8, v14->m_hStmtSelect);
          v11 = -4;
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = v11;
      }
      else
      {
        TargetValue = 0;
        StrLen_or_IndPtr = &v7;
        SQLStmt = 0i64;
        v8 = SQLGetData_0(v14->m_hStmtSelect, 1u, 4, &TargetValue, 0i64, &v7);
        if ( v8 == 100 )
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 4294967291i64;
        }
        else
        {
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &Dest);
          result = TargetValue;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0xFFFFFFFFi64;
  }
  return result;
}
