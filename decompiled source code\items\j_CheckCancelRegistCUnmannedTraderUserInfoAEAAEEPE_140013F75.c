/*
 * Function: j_?CheckCancelRegist@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_clear_item_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x140013F75
 */

char __fastcall CUnmannedTraderUserInfo::CheckCancelRegist(CUnmannedTraderUserInfo *this, char byType, _a_trade_clear_item_request_clzo *pRequest, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::CheckCancelRegist(this, byType, pRequest, pkLogger);
}
