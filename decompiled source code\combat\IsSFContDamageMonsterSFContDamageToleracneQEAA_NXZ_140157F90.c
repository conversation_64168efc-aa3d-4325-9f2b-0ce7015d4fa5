/*
 * Function: ?IsSFContDamage@MonsterSFContDamageToleracne@@QEAA_NXZ
 * Address: 0x140157F90
 */

bool __fastcall MonsterSFContDamageToleracne::IsSFContDamage(MonsterSFContDamageToleracne *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  MonsterSFContDamageToleracne *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = (signed int)ffloor(v6->m_fToleranceProb * 100.0);
  return v5 <= (unsigned int)(rand() % 100);
}
