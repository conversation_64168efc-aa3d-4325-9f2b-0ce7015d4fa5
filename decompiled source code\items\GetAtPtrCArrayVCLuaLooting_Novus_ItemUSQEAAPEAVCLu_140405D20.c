/*
 * Function: ?GetAtPtr@?$CArray@VCLuaLooting_Novus_Item@@@US@@QEAAPEAVCLuaLooting_Novus_Item@@K@Z
 * Address: 0x140405D20
 */

CLuaLooting_Novus_Item *__fastcall US::CArray<CLuaLooting_Novus_Item>::GetAtPtr(US::CArray<CLuaLooting_Novus_Item> *this, unsigned int dwIndex)
{
  CLuaLooting_Novus_Item *result; // rax@3

  if ( this->m_bAlloc && dwIndex < this->m_dwCount )
    result = &this->m_pBuffer[dwIndex];
  else
    result = 0i64;
  return result;
}
