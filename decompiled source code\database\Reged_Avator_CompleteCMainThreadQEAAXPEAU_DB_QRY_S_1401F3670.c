/*
 * Function: ?Reged_Avator_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3670
 */

void __fastcall CMainThread::Reged_Avator_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+20h] [bp-28h]@4
  CUserDB *v6; // [sp+28h] [bp-20h]@4
  char *v7; // [sp+30h] [bp-18h]@7

  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  v6 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v6->m_bActive )
  {
    if ( v6->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
    {
      v7 = pData->m_sData;
      CUserDB::Reged_Char_Complete(
        v6,
        pData->m_byResult,
        (_REGED *)&pData->m_sData[4],
        (_NOT_ARRANGED_AVATOR_DB *)&pData->m_sData[811]);
    }
  }
}
