/*
 * Function: ?Insert_PatrirchItemChargeRefund@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404BE8B0
 */

bool __fastcall CRFWorldDatabase::Insert_PatrirchItemChargeRefund(CRFWorldDatabase *this, char *szData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-168h]@1
  char *v6; // [sp+20h] [bp-148h]@4
  char Dest; // [sp+40h] [bp-128h]@4
  char v8; // [sp+41h] [bp-127h]@4
  unsigned __int64 v9; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+170h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = szData;
  Dest = 0;
  memset(&v8, 0, 0xFFui64);
  sprintf(
    &Dest,
    "insert [dbo].[tbl_itemcharge] (nAvatorSerial, nItemCode_K, nItemCode_D, nItemCode_U, dtGiveDate, dtTakeDate, Type) v"
    "alues (%d, 0, %d, default, default, default, 1)",
    *((_DWORD *)v6 + 1),
    *((_QWORD *)v6 + 1));
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &Dest, 1);
}
