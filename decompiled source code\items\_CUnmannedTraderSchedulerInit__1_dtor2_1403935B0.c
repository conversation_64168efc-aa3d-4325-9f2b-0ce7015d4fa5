/*
 * Function: _CUnmannedTraderScheduler::Init_::_1_::dtor$2
 * Address: 0x1403935B0
 */

void __fastcall CUnmannedTraderScheduler::Init_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>((std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)(a2 + 80));
}
