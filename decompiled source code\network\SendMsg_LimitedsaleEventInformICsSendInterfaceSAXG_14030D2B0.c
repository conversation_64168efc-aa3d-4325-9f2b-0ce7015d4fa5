/*
 * Function: ?SendMsg_LimitedsaleEventInform@ICsSendInterface@@SAXGEKG@Z
 * Address: 0x14030D2B0
 */

void __fastcall ICsSendInterface::SendMsg_LimitedsaleEventInform(unsigned __int16 wSock, char byTableCode, unsigned int dwIndex, unsigned __int16 wNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  unsigned __int16 v7; // ax@4
  __int64 v8; // [sp+0h] [bp-78h]@1
  _limitedsale_event_inform_zocl Dst; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  unsigned __int16 v12; // [sp+80h] [bp+8h]@1
  char v13; // [sp+88h] [bp+10h]@1
  unsigned int v14; // [sp+90h] [bp+18h]@1
  unsigned __int16 v15; // [sp+98h] [bp+20h]@1

  v15 = wNum;
  v14 = dwIndex;
  v13 = byTableCode;
  v12 = wSock;
  v4 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = _limitedsale_event_inform_zocl::size(&Dst);
  memset_0(&Dst, 0, v6);
  Dst.byTableCode = v13;
  Dst.dwIndex = v14;
  Dst.wNum = v15;
  pbyType = 57;
  v11 = 10;
  v7 = _limitedsale_event_inform_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12, &pbyType, &Dst.byTableCode, v7);
}
