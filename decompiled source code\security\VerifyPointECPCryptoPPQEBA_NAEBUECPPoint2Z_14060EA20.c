/*
 * Function: ?VerifyPoint@ECP@CryptoPP@@QEBA_NAEBUECPPoint@2@@Z
 * Address: 0x14060EA20
 */

char __fastcall CryptoPP::ECP::VerifyPoint(CryptoPP::ECP *this, const struct CryptoPP::ECPPoint *a2)
{
  CryptoPP::Integer b; // [sp+20h] [bp-1F8h]@1
  CryptoPP::Integer *v4; // [sp+48h] [bp-1D0h]@1
  CryptoPP::Integer *a; // [sp+50h] [bp-1C8h]@1
  char v6; // [sp+58h] [bp-1C0h]@9
  CryptoPP::Integer result; // [sp+60h] [bp-1B8h]@6
  CryptoPP::Integer v8; // [sp+88h] [bp-190h]@6
  CryptoPP::Integer v9; // [sp+B0h] [bp-168h]@6
  CryptoPP::Integer v10; // [sp+D8h] [bp-140h]@6
  CryptoPP::Integer v11; // [sp+100h] [bp-118h]@6
  CryptoPP::Integer v12; // [sp+128h] [bp-F0h]@6
  CryptoPP::Integer v13; // [sp+150h] [bp-C8h]@6
  int v14; // [sp+178h] [bp-A0h]@1
  __int64 v15; // [sp+180h] [bp-98h]@1
  CryptoPP::Integer *v16; // [sp+188h] [bp-90h]@6
  CryptoPP::Integer *v17; // [sp+190h] [bp-88h]@6
  const struct CryptoPP::Integer *v18; // [sp+198h] [bp-80h]@6
  const struct CryptoPP::Integer *v19; // [sp+1A0h] [bp-78h]@6
  CryptoPP::Integer *v20; // [sp+1A8h] [bp-70h]@6
  CryptoPP::Integer *v21; // [sp+1B0h] [bp-68h]@6
  CryptoPP::Integer *v22; // [sp+1B8h] [bp-60h]@6
  CryptoPP::Integer *v23; // [sp+1C0h] [bp-58h]@6
  CryptoPP::Integer *v24; // [sp+1C8h] [bp-50h]@6
  CryptoPP::Integer *v25; // [sp+1D0h] [bp-48h]@6
  CryptoPP::Integer *v26; // [sp+1D8h] [bp-40h]@6
  CryptoPP::Integer *v27; // [sp+1E0h] [bp-38h]@6
  CryptoPP::Integer *v28; // [sp+1E8h] [bp-30h]@6
  CryptoPP::Integer *v29; // [sp+1F0h] [bp-28h]@6
  CryptoPP::Integer *v30; // [sp+1F8h] [bp-20h]@6
  CryptoPP::Integer *v31; // [sp+200h] [bp-18h]@6
  int v32; // [sp+208h] [bp-10h]@7
  CryptoPP::ECP *v33; // [sp+220h] [bp+8h]@1
  const struct CryptoPP::ECPPoint *v34; // [sp+228h] [bp+10h]@1

  v34 = a2;
  v33 = this;
  v15 = -2i64;
  v14 = 0;
  a = &a2->x;
  v4 = &a2->y;
  CryptoPP::ECP::FieldSize(this, &b);
  v32 = v34->identity
     || !CryptoPP::Integer::IsNegative(a)
     && CryptoPP::operator<(a, &b)
     && !CryptoPP::Integer::IsNegative(v4)
     && CryptoPP::operator<(v4, &b)
     && (v16 = CryptoPP::operator*(&result, v4, v4),
         v17 = v16,
         v14 |= 1u,
         v18 = &v33->m_b,
         v19 = &v33->m_a,
         v20 = CryptoPP::operator*(&v8, a, a),
         v21 = v20,
         v14 |= 2u,
         v22 = CryptoPP::operator+(&v9, v20, v19),
         v23 = v22,
         v14 |= 4u,
         v24 = CryptoPP::operator*(&v10, v22, a),
         v25 = v24,
         v14 |= 8u,
         v26 = CryptoPP::operator+(&v11, v24, v18),
         v27 = v26,
         v14 |= 0x10u,
         v28 = CryptoPP::operator-(&v12, v26, v17),
         v29 = v28,
         v14 |= 0x20u,
         v30 = CryptoPP::operator%(&v13, v28, &b),
         v31 = v30,
         v14 |= 0x40u,
         CryptoPP::Integer::operator!(v30));
  v6 = v32;
  if ( v14 & 0x40 )
  {
    v14 &= 0xFFFFFFBF;
    CryptoPP::Integer::~Integer(&v13);
  }
  if ( v14 & 0x20 )
  {
    v14 &= 0xFFFFFFDF;
    CryptoPP::Integer::~Integer(&v12);
  }
  if ( v14 & 0x10 )
  {
    v14 &= 0xFFFFFFEF;
    CryptoPP::Integer::~Integer(&v11);
  }
  if ( v14 & 8 )
  {
    v14 &= 0xFFFFFFF7;
    CryptoPP::Integer::~Integer(&v10);
  }
  if ( v14 & 4 )
  {
    v14 &= 0xFFFFFFFB;
    CryptoPP::Integer::~Integer(&v9);
  }
  if ( v14 & 2 )
  {
    v14 &= 0xFFFFFFFD;
    CryptoPP::Integer::~Integer(&v8);
  }
  if ( v14 & 1 )
  {
    v14 &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(&result);
  }
  CryptoPP::Integer::~Integer(&b);
  return v6;
}
