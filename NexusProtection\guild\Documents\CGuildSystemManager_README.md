# CGuildSystemManager - Modern C++20 Guild System Manager

## Overview

The `CGuildSystemManager` is a comprehensive, modern C++20 implementation that manages the guild system loop processing. This system refactors the original decompiled C functions into a robust, maintainable, and feature-rich guild management system.

## Refactored Sources

This implementation modernizes the following decompiled files:
- **OnLoop_GuildSystemYAX_NZ_1402589E0.c** (27 lines) - Main guild system loop
- **j_OnLoop_GuildSystemYAX_NZ_140013732.c** (9 lines) - Jump wrapper function

## Key Features

### 🚀 **Modern C++20 Implementation**
- **RAII Memory Management**: Automatic resource cleanup and exception safety
- **Smart Pointers**: Safe memory handling with proper ownership semantics
- **STL Containers**: Modern containers for efficient data management
- **Atomic Operations**: Thread-safe operations for concurrent access
- **Exception Safety**: Comprehensive error handling and recovery

### 📊 **Advanced Statistics and Monitoring**
- **Real-time Metrics**: Track loop cycles, processed guilds, and active guilds
- **Performance Monitoring**: Average guilds per cycle and system uptime
- **Error Tracking**: Consecutive error counting and error rate monitoring
- **Day Change Events**: Track and monitor day change processing

### 🔧 **Flexible Configuration**
- **Configurable Parameters**: Maximum guilds, processing intervals, feature toggles
- **Runtime Configuration**: Update settings without system restart
- **Validation**: Comprehensive configuration validation
- **Feature Toggles**: Enable/disable statistics, logging, and day change processing

### 🎯 **Event-Driven Architecture**
- **Guild Processing Callbacks**: Notifications for guild processing events
- **Error Callbacks**: Custom error handling and notification
- **Day Change Callbacks**: Custom day change event handling
- **Asynchronous Processing**: Non-blocking event notifications

## Architecture

### Class Structure

```cpp
namespace NexusProtection::Guild {
    class CGuildSystemManager {
        // Singleton pattern with thread-safe initialization
        // Comprehensive guild system management
        // Event-driven architecture with callbacks
        // Advanced statistics and monitoring
    };
}
```

### Key Components

1. **Guild Processing Engine**: Core loop processing for all guilds
2. **Statistics Collection**: Real-time metrics and performance monitoring
3. **Configuration Management**: Dynamic configuration with validation
4. **Event System**: Callback-based notifications for system events
5. **Error Handling**: Comprehensive error tracking and recovery

## Usage Examples

### Basic Initialization

```cpp
#include "CGuildSystemManager.h"

using namespace NexusProtection::Guild;

// Get singleton instance
auto& guildManager = CGuildSystemManager::GetInstance();

// Configure system
GuildSystemConfig config;
config.maxGuilds = 500;
config.enableDayChangeProcessing = true;
config.enableStatistics = true;
config.enableErrorLogging = true;

// Initialize system
auto result = guildManager.Initialize(config);
if (result != GuildSystemResult::Success) {
    std::cerr << "Failed to initialize guild system" << std::endl;
    return -1;
}
```

### Processing Guild Loop

```cpp
// Process guild system loop
bool dayChanged = false; // Set based on your day change detection
auto result = guildManager.ProcessGuildLoop(dayChanged);

if (result == GuildSystemResult::Success) {
    std::cout << "Guild loop processed successfully" << std::endl;
} else {
    std::cerr << "Guild loop processing failed" << std::endl;
}
```

### Event Callbacks

```cpp
// Set guild processing callback
guildManager.SetGuildProcessedCallback([](uint32_t guildIndex, bool dayChanged) {
    std::cout << "Guild " << guildIndex << " processed";
    if (dayChanged) {
        std::cout << " (day changed)";
    }
    std::cout << std::endl;
});

// Set error callback
guildManager.SetGuildErrorCallback([](uint32_t guildIndex, const std::string& error) {
    std::cerr << "Guild " << guildIndex << " error: " << error << std::endl;
});

// Set day change callback
guildManager.SetDayChangeCallback([](int currentDay, int previousDay) {
    std::cout << "Day changed from " << previousDay << " to " << currentDay << std::endl;
});
```

### Statistics Monitoring

```cpp
// Get current statistics
auto stats = guildManager.GetStatistics();

std::cout << "Guild System Statistics:" << std::endl;
std::cout << "  Total Loop Cycles: " << stats.totalLoopCycles.load() << std::endl;
std::cout << "  Total Guilds Processed: " << stats.totalGuildsProcessed.load() << std::endl;
std::cout << "  Active Guilds: " << stats.activeGuilds.load() << std::endl;
std::cout << "  Day Change Events: " << stats.dayChangeEvents.load() << std::endl;
std::cout << "  Processing Errors: " << stats.processingErrors.load() << std::endl;
std::cout << "  Average Guilds/Cycle: " << stats.GetAverageGuildsPerCycle() << std::endl;
std::cout << "  System Uptime: " << stats.GetUptime().count() << " seconds" << std::endl;
```

### Legacy Compatibility

```cpp
// Legacy C-style function calls are automatically handled
extern "C" {
    void OnLoop_GuildSystem(bool bChangeDay);
    void j_OnLoop_GuildSystem(bool bChangeDay);
}

// These functions automatically delegate to the modern implementation
OnLoop_GuildSystem(true);  // Calls CGuildSystemManager internally
```

## Configuration Options

### GuildSystemConfig Structure

```cpp
struct GuildSystemConfig {
    uint32_t maxGuilds = 500;                    // Maximum guilds to process
    bool enableDayChangeProcessing = true;       // Enable day change detection
    bool enableStatistics = true;                // Enable statistics collection
    bool enableErrorLogging = true;              // Enable error logging
    std::chrono::milliseconds loopInterval{100}; // Processing interval
};
```

### Configuration Validation

The system automatically validates configuration parameters:
- `maxGuilds`: Must be between 1 and 10,000
- All boolean flags are validated for proper values
- Interval values are checked for reasonable ranges

## Error Handling

### Result Codes

```cpp
enum class GuildSystemResult : uint8_t {
    Success = 0,              // Operation completed successfully
    InitializationFailed,     // System initialization failed
    InvalidGuildArray,        // Guild array validation failed
    ProcessingError,          // Error during guild processing
    SystemError              // General system error
};
```

### Error Recovery

The system implements comprehensive error recovery:
- **Graceful Degradation**: Continue processing other guilds if one fails
- **Error Tracking**: Monitor consecutive errors and error rates
- **Automatic Recovery**: Reset error counters on successful operations
- **Callback Notifications**: Custom error handling through callbacks

## Performance Characteristics

### Optimizations

- **Atomic Operations**: Lock-free statistics updates where possible
- **Minimal Locking**: Reduced contention with targeted mutex usage
- **Exception Safety**: Strong exception safety guarantees
- **Memory Efficiency**: Minimal memory overhead with efficient data structures

### Scalability

- **Thread-Safe**: Safe for concurrent access from multiple threads
- **Configurable Limits**: Adjustable guild limits based on system capacity
- **Resource Management**: Automatic cleanup and resource management
- **Performance Monitoring**: Real-time performance metrics

## Integration

### Project Integration

The guild system manager integrates seamlessly with the existing NexusProtection architecture:

1. **Header Inclusion**: Include `CGuildSystemManager.h` in your project
2. **Namespace Usage**: Use `NexusProtection::Guild` namespace
3. **Legacy Compatibility**: Existing C-style calls work automatically
4. **Event Integration**: Connect to existing event systems through callbacks

### Dependencies

- **C++20 Standard**: Requires C++20 compatible compiler
- **STL Libraries**: Uses standard library containers and utilities
- **Legacy Guild System**: Interfaces with existing CGuild implementation
- **Threading Support**: Requires std::thread and std::mutex support

## Best Practices

### Initialization

1. **Early Initialization**: Initialize the guild system early in application startup
2. **Configuration Validation**: Always validate configuration before use
3. **Error Checking**: Check initialization results and handle failures
4. **Resource Cleanup**: Ensure proper shutdown on application exit

### Usage Patterns

1. **Regular Processing**: Call `ProcessGuildLoop()` regularly from main game loop
2. **Event Handling**: Set up callbacks for important events
3. **Statistics Monitoring**: Regularly check statistics for system health
4. **Error Monitoring**: Monitor error rates and implement alerting

### Performance Tips

1. **Batch Processing**: Process multiple guilds in single loop calls
2. **Configuration Tuning**: Adjust `maxGuilds` based on system capacity
3. **Statistics Usage**: Disable statistics in production if not needed
4. **Callback Efficiency**: Keep callback functions lightweight and fast

## Troubleshooting

### Common Issues

1. **Initialization Failures**: Check guild array validity and configuration
2. **Processing Errors**: Monitor error callbacks for specific guild issues
3. **Performance Issues**: Check statistics for bottlenecks and error rates
4. **Memory Issues**: Ensure proper shutdown and resource cleanup

### Debugging

1. **Enable Logging**: Use `enableErrorLogging` for detailed error information
2. **Statistics Monitoring**: Use statistics to identify performance issues
3. **Callback Debugging**: Implement debug callbacks to trace execution
4. **State Checking**: Monitor system state for unexpected transitions

## Future Enhancements

### Planned Features

1. **Advanced Metrics**: More detailed performance and health metrics
2. **Configuration Persistence**: Save and load configuration from files
3. **Load Balancing**: Distribute guild processing across multiple threads
4. **Health Monitoring**: Advanced system health monitoring and alerting

### Extension Points

1. **Custom Callbacks**: Additional callback types for specific events
2. **Plugin Architecture**: Support for guild processing plugins
3. **External Integration**: APIs for external monitoring and management
4. **Advanced Configuration**: More granular configuration options
