/*
 * Function: ?Inform_ConditionalEvent_Status_All@CashItemRemoteStore@@QEAAXEEPEAD@Z
 * Address: 0x1402FC640
 */

void __fastcall CashItemRemoteStore::Inform_ConditionalEvent_Status_All(CashItemRemoteStore *this, char byEventType, char byStatus, char *pszMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@13
  int v7; // ecx@13
  int v8; // edx@13
  int v9; // er8@13
  __int64 v10; // [sp+0h] [bp-108h]@1
  char *pEMsg; // [sp+20h] [bp-E8h]@13
  int v12; // [sp+28h] [bp-E0h]@13
  int v13; // [sp+30h] [bp-D8h]@13
  int v14; // [sp+38h] [bp-D0h]@13
  int v15; // [sp+40h] [bp-C8h]@13
  int v16; // [sp+48h] [bp-C0h]@13
  int v17; // [sp+50h] [bp-B8h]@13
  int v18; // [sp+58h] [bp-B0h]@13
  int v19; // [sp+60h] [bp-A8h]@13
  int v20; // [sp+68h] [bp-A0h]@13
  unsigned __int16 j; // [sp+70h] [bp-98h]@4
  CPlayer *v22; // [sp+78h] [bp-90h]@7
  unsigned __int16 v23; // [sp+80h] [bp-88h]@10
  char szEventName; // [sp+A0h] [bp-68h]@13
  unsigned __int64 v25; // [sp+F0h] [bp-18h]@4
  CashItemRemoteStore *v26; // [sp+110h] [bp+8h]@1
  char v27; // [sp+118h] [bp+10h]@1
  char v28; // [sp+120h] [bp+18h]@1
  char *v29; // [sp+128h] [bp+20h]@1

  v29 = pszMsg;
  v28 = byStatus;
  v27 = byEventType;
  v26 = this;
  v4 = &v10;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v25 = (unsigned __int64)&v10 ^ _security_cookie;
  for ( j = 0; (signed int)j < 2532; ++j )
  {
    v22 = &g_Player + j;
    if ( v22->m_bOper && v22->m_bLive )
    {
      v23 = 0;
      if ( v27 == 2 )
        v23 = v26->m_cde.m_ini.m_wCsDiscount;
      ICsSendInterface::SendMsg_ConditionalEventInform(j, v27, v23, v28, v29);
    }
  }
  CashItemRemoteStore::Get_Conditional_Event_Name(v26, v27, &szEventName);
  v6 = v26->m_con_event.m_eventtime.m_nMonth[1] + 1;
  v7 = v26->m_con_event.m_eventtime.m_nYear[1] + 1900;
  v8 = v26->m_con_event.m_eventtime.m_nMonth[0] + 1;
  v9 = v26->m_con_event.m_eventtime.m_nYear[0] + 1900;
  v20 = v26->m_con_event.m_eventtime.m_nMinute[1];
  v19 = v26->m_con_event.m_eventtime.m_nHour[1];
  v18 = v26->m_con_event.m_eventtime.m_nDay[1];
  v17 = v6;
  v16 = v7;
  v15 = v26->m_con_event.m_eventtime.m_nMinute[0];
  v14 = v26->m_con_event.m_eventtime.m_nHour[0];
  v13 = v26->m_con_event.m_eventtime.m_nDay[0];
  v12 = v8;
  LODWORD(pEMsg) = v9;
  CLogFile::Write(
    &v26->m_con_event.m_conevent_log,
    "[ %s Conditional Event Inform when Event status change] [EventState : %d] [EventTime : %d/%d/%d %d:%d  ~ %d/%d/%d %d:%d ]",
    &szEventName,
    (unsigned __int8)v28);
}
