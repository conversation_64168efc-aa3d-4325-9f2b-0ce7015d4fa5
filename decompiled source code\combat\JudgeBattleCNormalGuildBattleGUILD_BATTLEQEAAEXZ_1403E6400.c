/*
 * Function: ?JudgeBattle@CNormalGuildBattle@GUILD_BATTLE@@QEAAEXZ
 * Address: 0x1403E6400
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::JudgeBattle(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattle *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_byWinResult = GUILD_BATTLE::CNormalGuildBattle::DecideWin(v5);
  if ( v5->m_byWinResult )
  {
    GUILD_BATTLE::CNormalGuildBattle::PushDQSWinLoseRank(v5);
    GUILD_BATTLE::CNormalGuildBattle::SendWinLoseResult(v5);
    result = v5->m_byWinResult;
  }
  else
  {
    GUILD_BATTLE::CNormalGuildBattle::PushDQSDrawRank(v5);
    GUILD_BATTLE::CNormalGuildBattle::SendDrawResult(v5);
    result = 0;
  }
  return result;
}
