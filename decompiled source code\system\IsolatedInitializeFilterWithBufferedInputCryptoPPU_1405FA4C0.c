/*
 * Function: ?IsolatedInitialize@FilterWithBufferedInput@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405FA4C0
 */

void __fastcall CryptoPP::FilterWithBufferedInput::IsolatedInitialize(CryptoPP::FilterWithBufferedInput *this, const struct CryptoPP::NameValuePairs *a2)
{
  bool v2; // zf@1
  CryptoPP::InvalidArgument v3; // [sp+30h] [bp-A8h]@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+80h] [bp-58h]@3
  unsigned __int8 v5; // [sp+B0h] [bp-28h]@3
  __int64 v6; // [sp+B8h] [bp-20h]@1
  CryptoPP::ClonableVtbl *v7; // [sp+C0h] [bp-18h]@1
  CryptoPP::FilterWithBufferedInput *v8; // [sp+E0h] [bp+8h]@1

  v8 = this;
  v6 = -2i64;
  v7 = this->vfptr;
  ((void (__fastcall *)(CryptoPP::FilterWithBufferedInput *, const struct CryptoPP::NameValuePairs *, unsigned __int64 *, unsigned __int64 *))v7[23].Clone)(
    this,
    a2,
    &this->m_firstSize,
    &this->m_blockSize);
  v2 = v8->m_firstSize == 0;
  if ( v8->m_blockSize < 1 )
  {
    memset(&v5, 0, sizeof(v5));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "FilterWithBufferedInput: invalid buffer size",
      v5);
    CryptoPP::InvalidArgument::InvalidArgument(&v3, &s);
    CxxThrowException_0((__int64)&v3, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  v2 = v8->m_lastSize == 0;
  CryptoPP::FilterWithBufferedInput::BlockQueue::ResetQueue(&v8->m_queue, 1ui64, v8->m_firstSize);
  v8->m_firstInputDone = 0;
}
