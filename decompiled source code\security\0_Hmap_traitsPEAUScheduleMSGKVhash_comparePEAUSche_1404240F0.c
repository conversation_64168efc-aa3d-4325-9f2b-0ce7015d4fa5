/*
 * Function: ??0?$_Hmap_traits@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@$0A@@stdext@@QEAA@AEBV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@1@@Z
 * Address: 0x1404240F0
 */

void __fastcall stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>(stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *this, stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *_Traits)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *v5; // [sp+30h] [bp+8h]@1
  stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *v6; // [sp+38h] [bp+10h]@1

  v6 = _Traits;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  v5->comp = (stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >)v6->comp.0;
}
