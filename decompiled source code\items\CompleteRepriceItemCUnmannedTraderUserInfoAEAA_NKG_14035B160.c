/*
 * Function: ?CompleteRepriceItem@CUnmannedTraderUserInfo@@AEAA_NKGK@Z
 * Address: 0x14035B160
 */

char __fastcall CUnmannedTraderUserInfo::CompleteRepriceItem(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, unsigned __int16 dwItemSerial, unsigned int dwPrice)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v6; // rax@5
  char v7; // al@11
  CUnmannedTraderRegistItemInfo *v8; // rax@12
  __int64 v9; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+28h] [bp-70h]@4
  char v11; // [sp+44h] [bp-54h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v12; // [sp+48h] [bp-50h]@4
  char v13; // [sp+60h] [bp-38h]@11
  char v14; // [sp+61h] [bp-37h]@12
  int v15; // [sp+64h] [bp-34h]@4
  __int64 v16; // [sp+68h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v17; // [sp+70h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v18; // [sp+78h] [bp-20h]@4
  int v19; // [sp+80h] [bp-18h]@6
  CUnmannedTraderUserInfo *v20; // [sp+A0h] [bp+8h]@1
  unsigned __int16 v21; // [sp+B0h] [bp+18h]@1
  unsigned int dwPricea; // [sp+B8h] [bp+20h]@1

  dwPricea = dwPrice;
  v21 = dwItemSerial;
  v20 = this;
  v4 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  v15 = 0;
  CUnmannedTraderUserInfo::Find(v20, &result, dwRegistSerial);
  v17 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v20->m_vecRegistItemInfo,
          &v12);
  v18 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v17;
  v15 |= 1u;
  v19 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v17->_Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont)
     || (v6 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result),
         CUnmannedTraderRegistItemInfo::GetItemSerial(v6) != v21);
  v11 = v19;
  if ( v15 & 1 )
  {
    v15 &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v12);
  }
  if ( v11 )
  {
    v13 = 0;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v7 = v13;
  }
  else
  {
    v8 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
    CUnmannedTraderRegistItemInfo::RepriceItem(v8, dwPricea);
    v14 = 1;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v7 = v14;
  }
  return v7;
}
