/*
 * Function: _CLogTypeDBTaskPool::_CLogTypeDBTaskPool_::_1_::dtor$3
 * Address: 0x1402C20E0
 */

void __fastcall CLogTypeDBTaskPool::_CLogTypeDBTaskPool_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>((std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *)(*(_QWORD *)(a2 + 64) + 488i64));
}
