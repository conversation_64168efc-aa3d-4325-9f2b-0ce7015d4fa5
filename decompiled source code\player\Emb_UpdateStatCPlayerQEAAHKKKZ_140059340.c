/*
 * Function: ?Emb_UpdateStat@CPlayer@@QEAAHKKK@Z
 * Address: 0x140059340
 */

__int64 __fastcall CPlayer::Emb_UpdateStat(CPlayer *this, unsigned int dwStatIndex, unsigned int dwNewData, unsigned int dwOldData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@4
  unsigned int v9; // [sp+24h] [bp-14h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  unsigned int v11; // [sp+48h] [bp+10h]@1
  unsigned int dwNewCum; // [sp+50h] [bp+18h]@4

  v11 = dwStatIndex;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = dwNewData - v10->m_pmMst.m_BaseCum.m_dwDamWpCnt[dwStatIndex];
  v9 = v10->m_pmMst.m_BaseCum.m_dwDamWpCnt[dwStatIndex] - dwOldData;
  dwNewCum = v9 + dwNewData;
  v10->m_pmMst.m_BaseCum.m_dwDamWpCnt[dwStatIndex] = v9 + dwNewData;
  if ( v10->m_pUserDB )
    CUserDB::Update_Stat(v10->m_pUserDB, dwStatIndex, dwNewCum, 0);
  v10->m_Param.m_dwAlterMastery[v11] += v8;
  return v9;
}
