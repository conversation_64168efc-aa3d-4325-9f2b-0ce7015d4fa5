/*
 * Function: ?ct_boss_sms_cancel@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291B70
 */

char __fastcall ct_boss_sms_cancel(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CRaceBossMsgController *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int dwMsgID; // [sp+20h] [bp-18h]@7
  int v7; // [sp+24h] [bp-14h]@7
  CPlayer *pkManager; // [sp+40h] [bp+8h]@1

  pkManager = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkManager )
  {
    if ( s_nWordCount < 2 )
    {
      result = 0;
    }
    else
    {
      dwMsgID = atoi(s_pwszDstCheat[1]);
      v7 = atoi(s_pwszDstCheat[0]);
      v4 = CRaceBossMsgController::Instance();
      CRaceBossMsgController::Cancel(v4, v7, dwMsgID, pkManager);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
