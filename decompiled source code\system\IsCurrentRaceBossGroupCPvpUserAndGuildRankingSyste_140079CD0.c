/*
 * Function: ?IsCurrentRaceBossGroup@CPvpUserAndGuildRankingSystem@@QEAA_NEK@Z
 * Address: 0x140079CD0
 */

bool __fastcall CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(CPvpUserAndGuildRankingSystem *this, char byRace, unsigned int dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPvpUserAndGuildRankingSystem *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return CUserRankingProcess::IsCurrentRaceBossGroup(&v7->m_kUserRankingProcess, byRace, dwSerial);
}
