/*
 * Function: ?Select_SFDelayInfo@CRFWorldDatabase@@QEAAEKPEAU_worlddb_sf_delay_info@@@Z
 * Address: 0x1404C2490
 */

char __fastcall CRFWorldDatabase::Select_SFDelayInfo(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_sf_delay_info *pSFDelay)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-5B8h]@1
  void *SQLStmt; // [sp+20h] [bp-598h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-590h]@23
  char TargetValue; // [sp+40h] [bp-578h]@23
  SQLLEN v10; // [sp+458h] [bp-160h]@23
  void *Dst; // [sp+468h] [bp-150h]@22
  int v12; // [sp+470h] [bp-148h]@29
  int v13; // [sp+474h] [bp-144h]@4
  __int16 v14; // [sp+478h] [bp-140h]@9
  char Dest; // [sp+490h] [bp-128h]@4
  char v16; // [sp+594h] [bp-24h]@16
  unsigned __int64 v17; // [sp+5A0h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+5C0h] [bp+8h]@1
  _worlddb_sf_delay_info *v19; // [sp+5D0h] [bp+18h]@1

  v19 = pSFDelay;
  v18 = this;
  v3 = &v6;
  for ( i = 364i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_SFDelay( %d ) }", dwSerial);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v14 = SQLExecDirectA_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v14 && v14 != 1 )
    {
      if ( v14 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v14, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v14, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v14 = SQLFetch_0(v18->m_hStmtSelect);
      if ( v14 && v14 != 1 )
      {
        v16 = 0;
        if ( v14 == 100 )
        {
          v16 = 2;
        }
        else
        {
          SQLStmt = v18->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v14, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v14, v18->m_hStmtSelect);
          v16 = 1;
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        result = v16;
      }
      else
      {
        for ( Dst = v19; ; Dst = (char *)Dst + v12 )
        {
          StrLen_or_IndPtr = &v10;
          SQLStmt = (void *)1024;
          v14 = SQLGetData_0(v18->m_hStmtSelect, 1u, -2, &TargetValue, 1024i64, &v10);
          if ( v14 == 100 )
            break;
          if ( v10 == -1 )
          {
            SQLStmt = v18->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v14, &Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v14, v18->m_hStmtSelect);
            if ( v18->m_hStmtSelect )
              SQLCloseCursor_0(v18->m_hStmtSelect);
            return 1;
          }
          if ( v14 )
            v12 = 1024;
          else
            v12 = v10;
          v13 += v12;
          memcpy_0(Dst, &TargetValue, v12);
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        if ( v18->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
