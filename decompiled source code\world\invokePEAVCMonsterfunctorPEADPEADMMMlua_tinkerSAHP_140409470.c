/*
 * Function: ??$invoke@PEAVCMonster@@@?$functor@PEADPEADMMM@lua_tinker@@SAHPEAUlua_State@@@Z
 * Address: 0x140409470
 */

signed __int64 __usercall lua_tinker::functor<char *,char *,float,float,float>::invoke<CMonster *>@<rax>(struct lua_State *a1@<rcx>, int a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *(__cdecl *v4)(char *, char *, float, float, float); // rax@4
  CMonster *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-58h]@1
  int v8; // [sp+20h] [bp-38h]@4
  int v9; // [sp+30h] [bp-28h]@4
  int v10; // [sp+34h] [bp-24h]@4
  int v11; // [sp+38h] [bp-20h]@4
  char *v12; // [sp+40h] [bp-18h]@4
  char *v13; // [sp+48h] [bp-10h]@4
  struct lua_State *L; // [sp+60h] [bp+8h]@1

  L = a1;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  lua_tinker::read<float>(L, 5);
  v9 = a2;
  lua_tinker::read<float>(L, 4);
  v10 = a2;
  lua_tinker::read<float>(L, 3);
  v11 = a2;
  v12 = lua_tinker::read<char *>(L, 2);
  v13 = lua_tinker::read<char *>(L, 1);
  v4 = lua_tinker::upvalue_<CMonster * (*)(char *,char *,float,float,float)>(L);
  v8 = v9;
  LODWORD(v5) = ((int (__fastcall *)(char *, char *))v4)(v13, v12);
  lua_tinker::push<CMonster *>(L, v5);
  return 1i64;
}
