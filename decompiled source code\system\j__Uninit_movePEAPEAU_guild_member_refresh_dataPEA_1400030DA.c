/*
 * Function: j_??$_Uninit_move@PEAPEAU_guild_member_refresh_data@@PEAPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAU_guild_member_refresh_data@@PEAPEAU1@00AEAV?$allocator@PEAU_guild_member_refresh_data@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400030DA
 */

_guild_member_refresh_data **__fastcall std::_Uninit_move<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>,std::_Undefined_move_tag>(_guild_member_refresh_data **_First, _guild_member_refresh_data **_Last, _guild_member_refresh_data **_Dest, std::allocator<_guild_member_refresh_data *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
