/*
 * Function: ?GetRecord@CRecordData@@QEAAPEAU_base_fld@@PEBDH@Z
 * Address: 0x14008F410
 */

_base_fld *__fastcall CRecordData::GetRecord(CRecordData *this, const char *szRecordCode, int nCompareLen)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int n; // [sp+20h] [bp-18h]@4
  _base_fld *v8; // [sp+28h] [bp-10h]@6
  CRecordData *v9; // [sp+40h] [bp+8h]@1
  char *Str2; // [sp+48h] [bp+10h]@1
  int v11; // [sp+50h] [bp+18h]@1

  v11 = nCompareLen;
  Str2 = (char *)szRecordCode;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( n = 0; n < v9->m_Header.m_nRecordNum; ++n )
  {
    v8 = CRecordData::GetRecord(v9, n);
    if ( !strncmp(v8->m_strCode, Str2, v11) )
      return v8;
  }
  return 0i64;
}
