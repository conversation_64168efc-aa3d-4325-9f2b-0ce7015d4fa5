/*
 * Function: ??0?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x1404580E0
 */

void __fastcall CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>::DL_DecryptorBase<CryptoPP::ECPPoint>(CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>((CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *)&v4->vfptr);
}
