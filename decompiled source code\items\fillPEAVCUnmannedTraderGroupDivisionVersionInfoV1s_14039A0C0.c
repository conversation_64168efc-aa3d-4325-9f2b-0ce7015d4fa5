/*
 * Function: ??$fill@PEAVCUnmannedTraderGroupDivisionVersionInfo@@V1@@std@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@0AEBV1@@Z
 * Address: 0x14039A0C0
 */

void __fastcall std::fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupDivisionVersionInfo *_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo>(_Firsta, _Last, _Val);
}
