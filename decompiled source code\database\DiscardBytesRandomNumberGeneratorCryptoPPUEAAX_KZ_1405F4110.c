/*
 * Function: ?DiscardBytes@RandomNumberGenerator@CryptoPP@@UEAAX_K@Z
 * Address: 0x1405F4110
 */

void __fastcall CryptoPP::RandomNumberGenerator::DiscardBytes(CryptoPP::RandomNumberGenerator *this, __int64 a2)
{
  CryptoPP::BitBucket *v2; // rax@1
  CryptoPP::RandomNumberGenerator *v3; // [sp+40h] [bp+8h]@1
  __int64 v4; // [sp+48h] [bp+10h]@1

  v4 = a2;
  v3 = this;
  v2 = CryptoPP::TheBitBucket((CryptoPP *)this);
  ((void (__fastcall *)(CryptoPP::RandomNumberGenerator *, CryptoPP::BitBucket *, void *, __int64))v3->vfptr[5].__vecDelDtor)(
    v3,
    v2,
    &CryptoPP::BufferedTransformation::NULL_CHANNEL,
    v4);
}
