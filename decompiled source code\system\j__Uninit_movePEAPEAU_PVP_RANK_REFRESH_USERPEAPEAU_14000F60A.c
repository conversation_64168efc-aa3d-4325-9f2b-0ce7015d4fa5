/*
 * Function: j_??$_Uninit_move@PEAPEAU_PVP_RANK_REFRESH_USER@@PEAPEAU1@V?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAU_PVP_RANK_REFRESH_USER@@PEAPEAU1@00AEAV?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F60A
 */

_PVP_RANK_REFRESH_USER **__fastcall std::_Uninit_move<_PVP_RANK_REFRESH_USER * *,_PVP_RANK_REFRESH_USER * *,std::allocator<_PVP_RANK_REFRESH_USER *>,std::_Undefined_move_tag>(_PVP_RANK_REFRESH_USER **_First, _PVP_RANK_REFRESH_USER **_Last, _PVP_RANK_REFRESH_USER **_Dest, std::allocator<_PVP_RANK_REFRESH_USER *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<_PVP_RANK_REFRESH_USER * *,_PVP_RANK_REFRESH_USER * *,std::allocator<_PVP_RANK_REFRESH_USER *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
