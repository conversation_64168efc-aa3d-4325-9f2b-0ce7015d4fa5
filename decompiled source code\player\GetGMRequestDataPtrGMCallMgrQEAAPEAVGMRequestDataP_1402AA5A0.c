/*
 * Function: ?GetGMRequestDataPtr@GMCallMgr@@QEAAPEAVGMRequestData@@PEAVCPlayer@@@Z
 * Address: 0x1402AA5A0
 */

GMRequestData *__usercall GMCallMgr::GetGMRequestDataPtr@<rax>(GMCallMgr *this@<rcx>, CPlayer *pOne@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-27E8h]@1
  GMRequestData *v8; // [sp+0h] [bp-27C8h]@4
  unsigned int pdwList[2533]; // [sp+20h] [bp-27A8h]@4
  int v10; // [sp+27B4h] [bp-14h]@4
  int j; // [sp+27B8h] [bp-10h]@4
  GMCallMgr *v12; // [sp+27D0h] [bp+8h]@1
  CPlayer *v13; // [sp+27D8h] [bp+10h]@1

  v13 = pOne;
  v12 = this;
  v3 = alloca(a3);
  v4 = &v7;
  for ( i = 2552i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0i64;
  v10 = CNetIndexList::CopyIndexList(&v12->m_listGMRequestDataTask, pdwList, 2532);
  for ( j = 0; j < v10; ++j )
  {
    v8 = &v12->m_buffGMRequestData[pdwList[j]];
    if ( v8->m_dwPlayerSerial == v13->m_dwObjSerial )
      return v8;
  }
  return 0i64;
}
