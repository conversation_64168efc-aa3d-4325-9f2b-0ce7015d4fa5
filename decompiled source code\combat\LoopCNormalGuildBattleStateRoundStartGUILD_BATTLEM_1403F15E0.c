/*
 * Function: ?Loop@CNormalGuildBattleStateRoundStart@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F15E0
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundStart::Loop(GUILD_BATTLE::CNormalGuildBattleStateRoundStart *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v4; // rax@5
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateRoundStart *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_pkTimer )
  {
    if ( CMyTimer::CountingTimer(v7->m_pkTimer) )
      result = 2i64;
    else
      result = 0i64;
  }
  else
  {
    v4 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(pkBattle);
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleStateRoundStart::Enter( CNormalGuildBattle * pkBattle ) :  0 == m_pkTimer !");
    result = 2i64;
  }
  return result;
}
