/*
 * Function: ??0?$CipherModeFinalTemplate_ExternalCipher@VCBC_CTS_Encryption@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14055B6C0
 */

CryptoPP::CBC_CTS_Encryption *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>(CryptoPP::CBC_CTS_Encryption *a1)
{
  CryptoPP::CBC_CTS_Encryption *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::CBC_CTS_Encryption::CBC_CTS_Encryption(a1);
  v2->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v2->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Encryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  return v2;
}
