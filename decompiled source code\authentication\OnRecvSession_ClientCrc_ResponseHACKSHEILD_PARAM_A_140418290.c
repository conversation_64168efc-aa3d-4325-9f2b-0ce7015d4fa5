/*
 * Function: ?OnRecvSession_ClientCrc_Response@HACKSHEILD_PARAM_ANTICP@@QEAA_N_KPEAD@Z
 * Address: 0x140418290
 */

char __fastcall HACKSHEILD_PARAM_ANTICP::OnRecvSession_ClientCrc_Response(HACKSHEILD_PARAM_ANTICP *this, unsigned __int64 tSize, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  char *v7; // [sp+20h] [bp-28h]@11
  unsigned int dwRet; // [sp+28h] [bp-20h]@11
  _HSHIELD_CLIENT_CONTEXT *v9; // [sp+30h] [bp-18h]@11
  HACKSHEILD_PARAM_ANTICP *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( tSize == 74 )
  {
    if ( v10->m_nSocketIndex >= 0 && v10->m_nSocketIndex < 2532 )
    {
      if ( v10->m_byVerifyState == 3 )
      {
        v7 = pMsg;
        v9 = &v10->m_CrcInfo;
        dwRet = _AntiCpSvr_AnalyzeAckMsg(&v10->m_CrcInfo, pMsg + 2, v10->m_byGUIDClientInfo);
        if ( dwRet )
        {
          HACKSHEILD_PARAM_ANTICP::Kick(v10, 3, dwRet);
          result = 1;
        }
        else
        {
          v10->m_byVerifyState = 4;
          result = 1;
        }
      }
      else
      {
        HACKSHEILD_PARAM_ANTICP::Kick(v10, 3, 0xFFFFFFFF);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
