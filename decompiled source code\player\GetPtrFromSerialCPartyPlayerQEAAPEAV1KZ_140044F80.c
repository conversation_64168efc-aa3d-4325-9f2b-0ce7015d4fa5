/*
 * Function: ?GetPtrFromSerial@CPartyPlayer@@QEAAPEAV1@K@Z
 * Address: 0x140044F80
 */

CPartyPlayer *__fastcall CPartyPlayer::GetPtrFromSerial(CPartyPlayer *this, unsigned int dwWorldSerial)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPartyPlayer *result; // rax@5
  int j; // [sp+0h] [bp-18h]@1
  CPartyPlayer *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( v6->m_pPartyBoss )
  {
    for ( j = 0; j < 8; ++j )
    {
      if ( !v6->m_pPartyBoss->m_pPartyMember[j] )
        return 0i64;
      if ( v6->m_pPartyBoss->m_pPartyMember[j]->m_id.dwSerial == dwWorldSerial )
        return v6->m_pPartyBoss->m_pPartyMember[j];
    }
    result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
