/*
 * Function: ?Precompute@?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@VInteger@CryptoPP@@@2@II@Z
 * Address: 0x14056EFD0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::Precompute(__int64 a1, __int64 a2, unsigned int a3, unsigned int a4)
{
  __int64 result; // rax@8
  __int64 *v5; // rax@9
  __int64 v6; // rax@9
  __int64 v7; // rax@9
  __int64 v8; // rax@9
  signed int i; // [sp+20h] [bp-A8h]@7
  CryptoPP::Integer v10; // [sp+28h] [bp-A0h]@6
  CryptoPP::Integer v11; // [sp+50h] [bp-78h]@9
  __int64 v12; // [sp+78h] [bp-50h]@1
  struct CryptoPP::Integer *v13; // [sp+80h] [bp-48h]@6
  struct CryptoPP::Integer *v14; // [sp+88h] [bp-40h]@6
  __int64 *v15; // [sp+90h] [bp-38h]@9
  __int64 v16; // [sp+98h] [bp-30h]@9
  __int64 v17; // [sp+A0h] [bp-28h]@9
  __int64 v18; // [sp+A8h] [bp-20h]@9
  __int64 v19; // [sp+B0h] [bp-18h]@9
  __int64 v20; // [sp+B8h] [bp-10h]@9
  __int64 v21; // [sp+D0h] [bp+8h]@1
  __int64 v22; // [sp+D8h] [bp+10h]@1
  unsigned int v23; // [sp+E0h] [bp+18h]@1
  unsigned int v24; // [sp+E8h] [bp+20h]@1

  v24 = a4;
  v23 = a3;
  v22 = a2;
  v21 = a1;
  v12 = -2i64;
  if ( !std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::size(a1 + 96) )
    _wassert(L"m_bases.size() > 0", L"d:\\rf project\\rf_server64\\28 crypto++\\eprecomp.cpp", 0x1Fu);
  if ( v24 > v23 )
    _wassert(L"storage <= maxExpBits", L"d:\\rf project\\rf_server64\\28 crypto++\\eprecomp.cpp", 0x20u);
  if ( v24 > 1 )
  {
    *(_DWORD *)(v21 + 48) = (v23 + v24 - 1) / v24;
    v13 = CryptoPP::Integer::Power2(&v10, *(_DWORD *)(v21 + 48));
    v14 = v13;
    CryptoPP::Integer::operator=(v21 + 56);
    CryptoPP::Integer::~Integer(&v10);
  }
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::resize(v21 + 96, v24);
  for ( i = 1; ; ++i )
  {
    result = v24;
    if ( i >= v24 )
      break;
    LODWORD(v5) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v22 + 24i64))(v22);
    v15 = v5;
    v16 = v21 + 56;
    v17 = v21 + 96;
    LODWORD(v6) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](
                    v21 + 96,
                    (unsigned int)(i - 1));
    v18 = *v15;
    LODWORD(v7) = (*(int (__fastcall **)(__int64 *, CryptoPP::Integer *, __int64, __int64))(v18 + 80))(
                    v15,
                    &v11,
                    v6,
                    v16);
    v19 = v7;
    v20 = v7;
    LODWORD(v8) = std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator[](
                    v21 + 96,
                    (unsigned int)i);
    CryptoPP::Integer::operator=(v8);
    CryptoPP::Integer::~Integer(&v11);
  }
  return result;
}
