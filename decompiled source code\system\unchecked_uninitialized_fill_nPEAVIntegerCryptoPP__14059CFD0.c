/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAVInteger@CryptoPP@@_KV12@V?$allocator@VInteger@CryptoPP@@@std@@@stdext@@YAXPEAVInteger@CryptoPP@@_KAEBV12@AEAV?$allocator@VInteger@CryptoPP@@@std@@@Z
 * Address: 0x14059CFD0
 */

int __fastcall stdext::unchecked_uninitialized_fill_n<CryptoPP::Integer *,unsigned __int64,CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  __int64 v7; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1
  __int64 v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Ptr_cat<CryptoPP::Integer *,CryptoPP::Integer *>();
  return std::_Uninit_fill_n<CryptoPP::Integer *,unsigned __int64,CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(
           v7,
           v8,
           v9,
           v10);
}
