/*
 * Function: ?GetAveSkillMasteryPerClass@_MASTERY_PARAM@@QEAAME@Z
 * Address: 0x14007D7A0
 */

float __fastcall _MASTERY_PARAM::GetAveSkillMasteryPerClass(_MASTERY_PARAM *this, char byClass)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  int v6; // [sp+10h] [bp-18h]@4
  int v7; // [sp+14h] [bp-14h]@4
  int k; // [sp+18h] [bp-10h]@5
  int j; // [sp+1Ch] [bp-Ch]@9
  _MASTERY_PARAM *v10; // [sp+30h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  v7 = 1;
  if ( byClass )
  {
    for ( j = 4; j <= 6; ++j )
      v6 += v10->m_ppbyMasteryPtr[3][j];
    v7 = 3;
  }
  else
  {
    for ( k = 0; k <= 3; ++k )
      v6 += v10->m_ppbyMasteryPtr[3][k];
    v7 = 4;
  }
  return (float)v6 / (float)v7;
}
