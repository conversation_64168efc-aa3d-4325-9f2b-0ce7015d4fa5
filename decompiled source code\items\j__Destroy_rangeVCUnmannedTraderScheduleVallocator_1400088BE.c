/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@YAXPEAVCUnmannedTraderSchedule@@0AEAV?$allocator@VCUnmannedTraderSchedule@@@0@@Z
 * Address: 0x1400088BE
 */

void __fastcall std::_Destroy_range<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, std::allocator<CUnmannedTraderSchedule> *_Al)
{
  std::_Destroy_range<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(_First, _Last, _Al);
}
