/*
 * Function: ?ct_manage_guild@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296AE0
 */

char __fastcall ct_manage_guild(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-68h]@1
  _guild_manage_request_clzo v5; // [sp+38h] [bp-30h]@7
  int v6; // [sp+54h] [bp-14h]@9
  CPlayer *v7; // [sp+70h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    _guild_manage_request_clzo::_guild_manage_request_clzo(&v5);
    v5.byManageType = atoi(s_pwszDstCheat[0]);
    if ( (signed int)(unsigned __int8)v5.byManageType < 6 )
    {
      v6 = (unsigned __int8)v5.byManageType;
      switch ( v5.byManageType )
      {
        case 0:
          if ( s_nWordCount == 5 )
          {
            v5.dwManageDst = atoi(s_pwszDstCheat[1]);
            v5.dwManageObj1 = atoi(s_pwszDstCheat[2]);
            v5.dwManageObj2 = atoi(s_pwszDstCheat[3]);
            v5.dwManageObj3 = atoi(s_pwszDstCheat[4]);
            goto LABEL_28;
          }
          result = 0;
          break;
        case 1:
          if ( s_nWordCount == 2 )
          {
            v5.dwManageDst = atoi(s_pwszDstCheat[1]);
            goto LABEL_28;
          }
          result = 0;
          break;
        case 2:
          if ( s_nWordCount == 4 )
          {
            v5.dwManageDst = atoi(s_pwszDstCheat[1]);
            v5.dwManageObj1 = atoi(s_pwszDstCheat[2]);
            v5.dwManageObj2 = atoi(s_pwszDstCheat[3]);
            goto LABEL_28;
          }
          result = 0;
          break;
        case 3:
          if ( s_nWordCount == 4 )
          {
            v5.dwManageDst = atoi(s_pwszDstCheat[1]);
            v5.dwManageObj1 = atoi(s_pwszDstCheat[2]);
            v5.dwManageObj2 = atoi(s_pwszDstCheat[3]);
            goto LABEL_28;
          }
          result = 0;
          break;
        case 4:
          if ( s_nWordCount == 3 )
          {
            v5.dwManageDst = atoi(s_pwszDstCheat[1]);
            v5.dwManageObj1 = atoi(s_pwszDstCheat[2]);
            goto LABEL_28;
          }
          result = 0;
          break;
        case 5:
          if ( s_nWordCount == 2 )
          {
            v5.dwManageDst = atoi(s_pwszDstCheat[1]);
            goto LABEL_28;
          }
          result = 0;
          break;
        default:
LABEL_28:
          CPlayer::pc_GuildManageRequest(
            v7,
            v5.byManageType,
            v5.dwManageDst,
            v5.dwManageObj1,
            v5.dwManageObj2,
            v5.dwManageObj3);
          result = 1;
          break;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
