/*
 * Function: ?insert_expend@CRFDBItemLog@@QEAA_NPEAU_LTD_EXPEND@@@Z
 * Address: 0x140485B80
 */

bool __fastcall CRFDBItemLog::insert_expend(CRFDBItemLog *this, _LTD_EXPEND *pe)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // ecx@4
  int v5; // edx@4
  int v6; // edi@4
  int v7; // er8@4
  int v8; // er9@4
  int v9; // er10@4
  int v10; // er11@4
  unsigned int v11; // ebx@4
  __int64 v13; // [sp+0h] [bp-2A8h]@1
  int v14; // [sp+20h] [bp-288h]@4
  int v15; // [sp+28h] [bp-280h]@4
  int v16; // [sp+30h] [bp-278h]@4
  int v17; // [sp+38h] [bp-270h]@4
  int v18; // [sp+40h] [bp-268h]@4
  int v19; // [sp+48h] [bp-260h]@4
  int v20; // [sp+50h] [bp-258h]@4
  char *v21; // [sp+58h] [bp-250h]@4
  char Dest; // [sp+70h] [bp-238h]@4
  char v23; // [sp+71h] [bp-237h]@4
  unsigned __int64 v24; // [sp+280h] [bp-28h]@4
  CRFDBItemLog *v25; // [sp+2B0h] [bp+8h]@1
  _LTD_EXPEND *v26; // [sp+2B8h] [bp+10h]@1

  v26 = pe;
  v25 = this;
  v2 = &v13;
  for ( i = 166i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v24 = (unsigned __int64)&v13 ^ _security_cookie;
  Dest = 0;
  memset(&v23, 0, 0x1FFui64);
  v4 = pe->m_bySubLogType;
  v5 = pe->m_timeLocal.wMilliseconds;
  v6 = v26->m_timeLocal.wSecond;
  v7 = v26->m_timeLocal.wMinute;
  v8 = v26->m_timeLocal.wHour;
  v9 = v26->m_timeLocal.wDay;
  v10 = v26->m_timeLocal.wMonth;
  v11 = v26->m_timeLocal.wYear;
  v21 = v26->m_wszEtcInfo;
  v20 = v4;
  v19 = v5;
  v18 = v6;
  v17 = v7;
  v16 = v8;
  v15 = v9;
  v14 = v10;
  sprintf(
    &Dest,
    "insert into tbl_ltd_expend_%d(LogSerial, SubType, Expend)values ('%04d-%02d-%02d %02d:%02d:%02d.%03d', %d, '%s')",
    v25->m_dwKorTime,
    v11);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v25->vfptr, &Dest, 1);
}
