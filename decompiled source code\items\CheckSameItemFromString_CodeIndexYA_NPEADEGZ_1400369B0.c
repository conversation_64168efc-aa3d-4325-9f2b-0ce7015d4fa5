/*
 * Function: ?CheckSameItemFromString_CodeIndex@@YA_NPEADEG@Z
 * Address: 0x1400369B0
 */

bool __fastcall CheckSameItemFromString_CodeIndex(char *psItemCode, char byTableCode, unsigned __int16 wIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _base_fld *v7; // [sp+20h] [bp-18h]@8
  const char *Str2; // [sp+40h] [bp+8h]@1

  Str2 = psItemCode;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( Str2 )
  {
    if ( (signed int)(unsigned __int8)byTableCode < 37 )
    {
      v7 = CRecordData::GetRecord(&s_ptblItemData[(unsigned __int8)byTableCode], wIndex);
      if ( v7 )
        result = strcmp_0(v7->m_strCode, Str2) == 0;
      else
        result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
