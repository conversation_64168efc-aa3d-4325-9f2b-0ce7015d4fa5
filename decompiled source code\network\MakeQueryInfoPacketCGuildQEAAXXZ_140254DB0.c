/*
 * Function: ?MakeQueryInfoPacket@CGuild@@QEAAXXZ
 * Address: 0x140254DB0
 */

void __usercall CGuild::MakeQueryInfoPacket(CGuild *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderTaxRateManager *v4; // rax@4
  CUnmannedTraderTaxRateManager *v5; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CGuild *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7->m_QueryPacket_Info->dwGuildSerial = v7->m_dwSerial;
  v7->m_QueryPacket_Info->byGrade = v7->m_byGrade;
  v7->m_QueryPacket_Info->dwEmblemBack = v7->m_dwEmblemBack;
  v7->m_QueryPacket_Info->dwEmblemMark = v7->m_dwEmblemMark;
  strcpy_0(v7->m_QueryPacket_Info->wszGuildName, v7->m_wszName);
  v7->m_QueryPacket_Info->dwTotWin = v7->m_dwGuildBattleTotWin;
  v7->m_QueryPacket_Info->dwTotDraw = v7->m_dwGuildBattleTotDraw;
  v7->m_QueryPacket_Info->dwTotLose = v7->m_dwGuildBattleTotLose;
  v7->m_QueryPacket_Info->byCurTax = -1;
  v4 = CUnmannedTraderTaxRateManager::Instance();
  if ( CUnmannedTraderTaxRateManager::IsOwnerGuild(v4, v7->m_byRace, v7->m_dwSerial) )
  {
    v5 = CUnmannedTraderTaxRateManager::Instance();
    CUnmannedTraderTaxRateManager::GetTaxRate(v5, v7->m_byRace);
    v7->m_QueryPacket_Info->byCurTax = (signed int)ffloor(a2 * 100.0);
  }
}
