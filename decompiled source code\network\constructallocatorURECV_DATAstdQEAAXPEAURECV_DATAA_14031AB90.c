/*
 * Function: ?construct@?$allocator@URECV_DATA@@@std@@QEAAXPEAURECV_DATA@@AEBU3@@Z
 * Address: 0x14031AB90
 */

void __fastcall std::allocator<RECV_DATA>::construct(std::allocator<RECV_DATA> *this, RECV_DATA *_Ptr, RECV_DATA *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<RECV_DATA,RECV_DATA>(_Ptr, _Val);
}
