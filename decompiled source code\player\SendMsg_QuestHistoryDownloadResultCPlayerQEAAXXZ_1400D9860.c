/*
 * Function: ?SendMsg_QuestHistoryDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D9860
 */

void __fastcall CPlayer::SendMsg_QuestHistoryDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@9
  __int64 v4; // [sp+0h] [bp-318h]@1
  _quest_history_download_result_zocl v5; // [sp+40h] [bp-2D8h]@4
  int v6; // [sp+2C4h] [bp-54h]@4
  int j; // [sp+2C8h] [bp-50h]@4
  char *Source; // [sp+2D0h] [bp-48h]@6
  char pbyType; // [sp+2E4h] [bp-34h]@9
  char v10; // [sp+2E5h] [bp-33h]@9
  unsigned __int64 v11; // [sp+300h] [bp-18h]@4
  CPlayer *v12; // [sp+320h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 196i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  _quest_history_download_result_zocl::_quest_history_download_result_zocl(&v5);
  v6 = 0;
  for ( j = 0; j < 70; ++j )
  {
    Source = v12->m_Param.m_QuestDB.m_History[j].szQuestCode;
    if ( v12->m_Param.m_QuestDB.m_History[j].byLevel != 255 )
    {
      v5.SlotInfo[v6].byIndex = j;
      strcpy_0(v5.SlotInfo[v6++].szQuestCode, Source);
    }
  }
  v5.bySlotNum = v6;
  pbyType = 3;
  v10 = 57;
  v3 = _quest_history_download_result_zocl::size(&v5);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &v5.bySlotNum, v3);
}
