/*
 * Function: ?Init@CGuildRanking@@QEAA_NXZ
 * Address: 0x140339210
 */

char __fastcall CGuildRanking::Init(CGuildRanking *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@5
  _guild_member_refresh_data **v4; // rax@6
  __int64 v6; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  unsigned int _Val; // [sp+24h] [bp-34h]@4
  char v9; // [sp+28h] [bp-30h]@4
  _guild_member_refresh_data *v10; // [sp+30h] [bp-28h]@4
  _guild_member_refresh_data *v11; // [sp+38h] [bp-20h]@6
  unsigned __int64 v12; // [sp+40h] [bp-18h]@5
  CGuildRanking *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v1 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _Val = 0;
  std::vector<unsigned long,std::allocator<unsigned long>>::assign(&v13->m_vecAllGuildSerial, 0x1F4ui64, &_Val);
  v9 = 0;
  std::vector<unsigned char,std::allocator<unsigned char>>::assign(&v13->m_vecAllGuildGrade, 0x1F4ui64, &v9);
  v10 = 0i64;
  std::vector<_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>::assign(
    &v13->m_vecGuildMemberRefresh,
    0x1F4ui64,
    &v10);
  for ( j = 0; ; ++j )
  {
    v12 = j;
    v3 = std::vector<_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>::size(&v13->m_vecGuildMemberRefresh);
    if ( v12 >= v3 )
      break;
    v11 = (_guild_member_refresh_data *)operator new(0x260ui64);
    v4 = std::vector<_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>::operator[](
           &v13->m_vecGuildMemberRefresh,
           j);
    *v4 = v11;
    if ( !v11 )
      return 0;
  }
  return 1;
}
