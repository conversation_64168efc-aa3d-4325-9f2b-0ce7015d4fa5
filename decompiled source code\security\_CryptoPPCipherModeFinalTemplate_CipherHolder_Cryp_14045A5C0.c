/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::StaticAlgorithmName_::_1_::dtor$2
 * Address: 0x14045A5C0
 */

int __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::StaticAlgorithmName_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  int result; // eax@1

  result = *(_DWORD *)(a2 + 128) & 1;
  if ( result )
  {
    *(_DWORD *)(a2 + 128) &= 0xFFFFFFFE;
    result = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(*(_QWORD *)(a2 + 208));
  }
  return result;
}
