/*
 * Function: ?GetNextYposFarProgress@CLevel@@QEAAHQEAM0PEAM@Z
 * Address: 0x1404E15B0
 */

__int64 __usercall CLevel::GetNextYposFarProgress@<rax>(CLevel *this@<rcx>, float *const a2@<rdx>, float *const a3@<r8>, float *a4@<r9>, signed __int64 a5@<rax>)
{
  void *v5; // rsp@1
  float *v6; // r15@1
  float *v7; // r14@1
  CLevel *v8; // r12@1
  __int64 result; // rax@2
  float v10; // xmm1_4@4
  int v11; // eax@7
  float v12; // xmm8_4@7
  float v13; // xmm1_4@7
  float v14; // xmm0_4@7
  float v15; // xmm1_4@7
  float v16; // xmm9_4@8
  float v17; // xmm7_4@8
  float v18; // xmm7_4@9
  float v19; // xmm8_4@9
  float v20; // xmm6_4@9
  float v21; // xmm12_4@9
  float v22; // xmm11_4@9
  float v23; // xmm13_4@9
  float v24; // xmm0_4@9
  float v25; // xmm12_4@9
  int v26; // xmm13_4@9
  float v27; // xmm11_4@9
  float v28; // xmm7_4@11
  CBsp *v29; // rcx@13
  signed int v30; // esi@13
  __int32 v31; // er13@13
  signed int v32; // ebx@13
  float v33; // xmm6_4@13
  float *v34; // rbp@14
  signed __int64 v35; // rdi@15
  CBsp *v36; // rcx@16
  float v37; // xmm0_4@16
  signed int v38; // eax@17
  float v39; // xmm5_4@17
  float v40; // xmm1_4@22
  float v41; // xmm2_4@22
  float v42; // xmm1_4@25
  int v43; // xmm0_4@27
  __int64 v44; // [sp-20h] [bp-53A8h]@1
  unsigned __int32 v45; // [sp+8h] [bp-5380h]@16
  float v46; // [sp+10h] [bp-5378h]@7
  float i; // [sp+14h] [bp-5374h]@7
  int v48; // [sp+18h] [bp-5370h]@7
  __int32 v49; // [sp+20h] [bp-5368h]@7
  float v50; // [sp+28h] [bp-5360h]@7
  float v51; // [sp+2Ch] [bp-535Ch]@7
  float v52; // [sp+30h] [bp-5358h]@7
  float v53; // [sp+38h] [bp-5350h]@13
  float v54; // [sp+3Ch] [bp-534Ch]@13
  int v55; // [sp+40h] [bp-5348h]@13
  float v56; // [sp+48h] [bp-5340h]@13
  float v57; // [sp+4Ch] [bp-533Ch]@13
  int v58; // [sp+50h] [bp-5338h]@13
  float v59; // [sp+58h] [bp-5330h]@13
  float v60; // [sp+5Ch] [bp-532Ch]@13
  int v61; // [sp+60h] [bp-5328h]@13
  float v62; // [sp+70h] [bp-5318h]@13
  unsigned __int64 v63; // [sp+5290h] [bp-F8h]@1

  v5 = alloca(a5);
  v63 = (unsigned __int64)&v44 ^ _security_cookie;
  v6 = a4;
  v7 = a3;
  v8 = this;
  if ( this->mIsLoadedBsp )
  {
    if ( *a2 != *a3 || (v10 = a2[1], v10 != a3[1]) || a2[2] != a3[2] )
    {
      v11 = *((_DWORD *)a2 + 1);
      v46 = *a3;
      v12 = FLOAT_1_0;
      *((_DWORD *)a3 + 1) = v11;
      v49 = 0;
      v13 = a2[1];
      v50 = *a2;
      v14 = a2[2];
      v51 = v13;
      v15 = a3[1];
      v52 = v14;
      v48 = *((int *)a3 + 2);
      for ( i = v15; ; i = v16 )
      {
        v16 = FLOAT_N100000_0;
        v17 = GetDist(&v50, &v46);
        if ( v17 <= 25.0 )
        {
          v26 = v48;
          v27 = i;
          v25 = v46;
        }
        else
        {
          v18 = v50;
          v19 = v51;
          v20 = v52;
          v21 = v46 - v50;
          v22 = i - v51;
          v23 = *(float *)&v48 - v52;
          v24 = sqrtf_0((float)((float)(v22 * v22) + (float)(v21 * v21)) + (float)(v23 * v23));
          v25 = (float)((float)(v21 / v24) * 25.0) + v18;
          *(float *)&v26 = (float)((float)(v23 / v24) * 25.0) + v20;
          v27 = (float)((float)(v22 / v24) * 25.0) + v19;
          v17 = FLOAT_25_0;
          v12 = FLOAT_1_0;
          v46 = v25;
          v48 = v26;
        }
        v28 = v17 * 1.73;
        if ( v28 < 23.0 )
          v28 = FLOAT_23_0;
        v29 = v8->mBsp;
        v59 = v25;
        v61 = v26;
        v60 = v27 + 30000.0;
        i = v27 + (float)(0.0 - 30000.0);
        CBsp::GetLeafList(v29, &v59, &v46, &v49, (__int16 *)&v62, 0x2710u);
        v30 = 0;
        v31 = 0;
        v32 = 10000;
        v53 = v46;
        v55 = v48;
        v56 = v46;
        v58 = v48;
        i = i + 30000.0;
        v33 = i;
        v54 = i + 30000.0;
        v57 = i - 30000.0;
        if ( v49 <= 0 )
          break;
        v34 = &v62;
        do
        {
          v35 = 2i64;
          do
          {
            v36 = v8->mBsp;
            v45 = *(_WORD *)v34;
            v37 = CBsp::GetBestYposInLeaf(v36, &v53, &v56, v28, v33, v45);
            v33 = i;
            if ( v37 != -32000.0 )
            {
              v38 = sub_1404E1570(i, v28, v37);
              if ( v32 == v38 )
              {
                if ( v39 > v16 )
                {
                  v30 = 1;
                  v16 = v39;
                }
              }
              else if ( v32 > (unsigned int)v38 )
              {
                v32 = v38;
                v30 = 1;
                v16 = v39;
              }
            }
            --v35;
            v40 = v53 + 0.5;
            v41 = v56 + 0.5;
            v53 = v53 + 0.5;
            v56 = v56 + 0.5;
          }
          while ( v35 );
          ++v31;
          v34 = (float *)((char *)v34 + 2);
          v53 = v40 + (float)(0.0 - v12);
          v56 = v41 + (float)(0.0 - v12);
        }
        while ( v31 < v49 );
        if ( !v30 )
          break;
        v42 = *v7;
        if ( v46 == *v7 && *(float *)&v48 == v7[2] )
        {
          *v6 = v16;
          return 1i64;
        }
        v52 = *(float *)&v48;
        v43 = *((int *)v7 + 2);
        v50 = v46;
        v48 = v43;
        v46 = v42;
        v51 = v16;
      }
      result = 0i64;
    }
    else
    {
      *a4 = v10;
      result = 1i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
