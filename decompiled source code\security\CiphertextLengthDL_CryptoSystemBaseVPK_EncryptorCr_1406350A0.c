/*
 * Function: ?CiphertextLength@?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@VInteger@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x1406350A0
 */

__int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::CiphertextLength(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rax@1
  __int64 v4; // rax@3
  __int64 v5; // rdx@3
  __int64 v7; // [sp+20h] [bp-28h]@1
  __int64 v8; // [sp+30h] [bp-18h]@2
  __int64 v9; // [sp+50h] [bp+8h]@1
  __int64 v10; // [sp+58h] [bp+10h]@1

  v10 = a2;
  v9 = a1;
  LODWORD(v2) = (*(int (**)(void))(*(_QWORD *)a1 + 80i64))();
  LODWORD(v3) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v2 + 16i64))(v2, v10);
  v7 = v3;
  if ( v3 )
  {
    LODWORD(v4) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>::GetAbstractGroupParameters(v9 + 16);
    LOBYTE(v5) = 1;
    v8 = v7 + (unsigned int)(*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v4 + 96i64))(v4, v5);
  }
  else
  {
    v8 = 0i64;
  }
  return v8;
}
