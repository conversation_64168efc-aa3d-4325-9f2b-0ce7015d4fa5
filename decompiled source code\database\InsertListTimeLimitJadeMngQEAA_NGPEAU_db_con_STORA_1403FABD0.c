/*
 * Function: ?InsertList@TimeLimitJadeMng@@QEAA_NGPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1403FABD0
 */

bool __fastcall TimeLimitJadeMng::InsertList(TimeLimitJadeMng *this, unsigned __int16 wIdx, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  TimeLimitJadeMng *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)wIdx <= 2532 )
    result = TimeLimitJade::InsertWaitList(v7->_ppkTimeLimitJade[wIdx], pkItem) != 0;
  else
    result = 0;
  return result;
}
