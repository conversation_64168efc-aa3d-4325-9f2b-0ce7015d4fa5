/*
 * CAttackForceTests.cpp - Comprehensive Unit Tests Implementation
 * Tests all damage calculation scenarios, edge cases, and error conditions
 */

#include "CAttackForceTests.h"
#include "../Headers/CAttackForceIntegration.h"
#include "../../common/Headers/Logger.h"

#include <chrono>
#include <random>

namespace NexusProtection {
namespace Combat {
namespace Tests {

/**
 * Test basic attack execution
 */
bool CAttackForceTests::TestBasicAttackExecution() {
    try {
        m_fixture.SetupBasicAttack();
        
        AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);
        
        // Verify basic execution
        ASSERT_TRUE(result.bSuccess, "Attack execution should succeed");
        ASSERT_NOT_NULL(result.hitTargets.size() > 0, "Should have at least one target");
        ASSERT_TRUE(result.nTargetsHit > 0, "Should hit at least one target");
        
        Logger::Info("TestBasicAttackExecution - Attack executed successfully, damage: %d", result.nDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestBasicAttackExecution - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test hit chance calculation
 */
bool CAttackForceTests::TestHitChanceCalculation() {
    try {
        m_fixture.SetupBasicAttack();
        
        // Test normal hit chance
        float hitChance = m_fixture.m_pAttackForce->CalculateHitChance(m_fixture.m_pTarget.get());
        ASSERT_TRUE(hitChance >= 0.0f && hitChance <= 100.0f, "Hit chance should be between 0-100");
        
        // Test with accuracy bonus
        float bonusHitChance = m_fixture.m_pAttackForce->CalculateHitChance(m_fixture.m_pTarget.get(), 20.0f);
        ASSERT_TRUE(bonusHitChance >= hitChance, "Accuracy bonus should increase hit chance");
        
        // Test with high avoidance target
        m_fixture.SetupHighAvoidanceTarget();
        float lowHitChance = m_fixture.m_pAttackForce->CalculateHitChance(m_fixture.m_pTarget.get());
        ASSERT_TRUE(lowHitChance < hitChance, "High avoidance should reduce hit chance");
        
        Logger::Info("TestHitChanceCalculation - Normal: %.1f%%, Bonus: %.1f%%, High Avoid: %.1f%%", 
                    hitChance, bonusHitChance, lowHitChance);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestHitChanceCalculation - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test damage calculation
 */
bool CAttackForceTests::TestDamageCalculation() {
    try {
        m_fixture.SetupBasicAttack();
        
        // Test basic damage calculation
        DamageCalculationParams params = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);
        ASSERT_TRUE(params.fBaseDamage > 0.0f, "Base damage should be positive");
        ASSERT_TRUE(params.fEffectiveDamage > 0.0f, "Effective damage should be positive");
        
        // Test with effect bullet
        DamageCalculationParams effParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam, true);
        ASSERT_TRUE(effParams.fEffectiveDamage >= params.fEffectiveDamage, "Effect bullet should not reduce damage");
        
        // Test with damage rate bonus
        m_fixture.SetupDamageRateBonus();
        DamageCalculationParams bonusParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);
        ASSERT_TRUE(bonusParams.fBaseDamage > params.fBaseDamage, "Damage rate bonus should increase damage");
        
        Logger::Info("TestDamageCalculation - Base: %.1f, Effective: %.1f, Bonus: %.1f", 
                    params.fBaseDamage, params.fEffectiveDamage, bonusParams.fBaseDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestDamageCalculation - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test critical hit calculation
 */
bool CAttackForceTests::TestCriticalHitCalculation() {
    try {
        // Test critical hit chance calculation
        float critChance = m_fixture.m_pDamageCalculator->CalculateCriticalChance(
            m_fixture.m_pAttacker.get(), m_fixture.m_pTarget.get());
        ASSERT_TRUE(critChance >= 0.0f && critChance <= 100.0f, "Critical chance should be between 0-100");
        
        // Test critical hit damage modifier
        DamageCalculationContext context;
        context.pAttacker = m_fixture.m_pAttacker.get();
        context.pTarget = m_fixture.m_pTarget.get();
        context.pParam = &m_fixture.m_attackParam;
        context.bCriticalHit = true;
        
        DetailedDamageResult result = m_fixture.m_pDamageCalculator->CalculateDamage(context);
        ASSERT_TRUE(result.bCriticalHit, "Result should indicate critical hit");
        ASSERT_TRUE(result.fTotalMultiplier > 1.0f, "Critical hit should increase damage multiplier");
        
        Logger::Info("TestCriticalHitCalculation - Crit chance: %.1f%%, Multiplier: %.2f", 
                    critChance, result.fTotalMultiplier);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestCriticalHitCalculation - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test single target attack
 */
bool CAttackForceTests::TestSingleTargetAttack() {
    try {
        m_fixture.SetupBasicAttack();
        
        AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);
        
        ASSERT_TRUE(result.bSuccess, "Single target attack should succeed");
        ASSERT_TRUE(result.nTargetsHit == 1, "Should hit exactly one target");
        ASSERT_TRUE(result.hitTargets.size() == 1, "Should have exactly one target in results");
        ASSERT_TRUE(result.hitTargets[0] == m_fixture.m_pTarget.get(), "Should hit the correct target");
        ASSERT_TRUE(result.nDamage > 0, "Should deal damage");
        
        Logger::Info("TestSingleTargetAttack - Damage: %d to target", result.nDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestSingleTargetAttack - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test area damage attack
 */
bool CAttackForceTests::TestAreaDamageAttack() {
    try {
        m_fixture.SetupAreaAttack();
        
        // Create area damage configuration
        AreaDamageConfig config;
        config.fCenterX = 100.0f;
        config.fCenterY = 100.0f;
        config.fCenterZ = 0.0f;
        config.fRadius = 150.0f;
        config.fInnerRadius = 50.0f;
        config.bUseFalloff = true;
        config.nMaxTargets = 10;
        
        AreaDamageResult result = m_fixture.m_pAreaProcessor->ProcessAreaDamage(
            m_fixture.m_pAttacker.get(), config, &m_fixture.m_attackParam);
        
        ASSERT_TRUE(result.bSuccess, "Area damage should succeed");
        ASSERT_TRUE(result.nTargetsHit >= 0, "Should have valid target count");
        
        // Test area damage utility functions
        AreaDamageConfig utilConfig = AreaDamageUtils::CreateAreaConfig(&m_fixture.m_attackParam, 100);
        ASSERT_TRUE(utilConfig.IsValid(), "Utility-created config should be valid");
        ASSERT_TRUE(utilConfig.fRadius == 100.0f, "Radius should match input");
        
        Logger::Info("TestAreaDamageAttack - Targets hit: %d, Total damage: %d", 
                    result.nTargetsHit, result.nTotalDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestAreaDamageAttack - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test flash damage attack
 */
bool CAttackForceTests::TestFlashDamageAttack() {
    try {
        m_fixture.SetupFlashAttack();
        
        // Create flash damage configuration
        FlashDamageConfig config;
        config.fOriginX = 50.0f;
        config.fOriginY = 50.0f;
        config.fOriginZ = 0.0f;
        config.fDirectionX = 1.0f;
        config.fDirectionY = 0.0f;
        config.fDirectionZ = 0.0f;
        config.fMaxDistance = 200.0f;
        config.fAngle = 45.0f;
        config.bUseFalloff = true;
        config.nMaxTargets = 8;
        
        AreaDamageResult result = m_fixture.m_pAreaProcessor->ProcessFlashDamage(
            m_fixture.m_pAttacker.get(), config, &m_fixture.m_attackParam);
        
        ASSERT_TRUE(result.bSuccess, "Flash damage should succeed");
        ASSERT_TRUE(result.nTargetsHit >= 0, "Should have valid target count");
        
        // Test flash damage utility functions
        FlashDamageConfig utilConfig = AreaDamageUtils::CreateFlashConfig(&m_fixture.m_attackParam, 150, 30);
        ASSERT_TRUE(utilConfig.IsValid(), "Utility-created config should be valid");
        ASSERT_TRUE(utilConfig.fMaxDistance == 150.0f, "Distance should match input");
        ASSERT_TRUE(utilConfig.fAngle == 30.0f, "Angle should match input");
        
        Logger::Info("TestFlashDamageAttack - Targets hit: %d, Total damage: %d", 
                    result.nTargetsHit, result.nTotalDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestFlashDamageAttack - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test sector damage attack
 */
bool CAttackForceTests::TestSectorDamageAttack() {
    try {
        // Create sector damage configuration
        SectorDamageConfig config;
        config.fOriginX = 0.0f;
        config.fOriginY = 0.0f;
        config.fOriginZ = 0.0f;
        config.fDirectionX = 1.0f;
        config.fDirectionY = 0.0f;
        config.fDirectionZ = 0.0f;
        config.fRadius = 120.0f;
        config.fAngle = 60.0f;
        config.bUseFalloff = true;
        config.nMaxTargets = 12;
        
        AreaDamageResult result = m_fixture.m_pAreaProcessor->ProcessSectorDamage(
            m_fixture.m_pAttacker.get(), config, &m_fixture.m_attackParam);
        
        ASSERT_TRUE(result.bSuccess, "Sector damage should succeed");
        ASSERT_TRUE(result.nTargetsHit >= 0, "Should have valid target count");
        
        // Test sector damage utility functions
        SectorDamageConfig utilConfig = AreaDamageUtils::CreateSectorConfig(&m_fixture.m_attackParam, 100, 45);
        ASSERT_TRUE(utilConfig.IsValid(), "Utility-created config should be valid");
        ASSERT_TRUE(utilConfig.fRadius == 100.0f, "Radius should match input");
        ASSERT_TRUE(utilConfig.fAngle == 45.0f, "Angle should match input");
        
        Logger::Info("TestSectorDamageAttack - Targets hit: %d, Total damage: %d", 
                    result.nTargetsHit, result.nTotalDamage);
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("TestSectorDamageAttack - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test destroyer bonus calculation
 */
bool CAttackForceTests::TestDestroyerBonusCalculation() {
    try {
        m_fixture.SetupBasicAttack();

        // Test without destroyer bonus
        DamageCalculationParams normalParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        // Test with destroyer bonus
        m_fixture.SetupDestroyerAttacker();
        DamageCalculationParams destroyerParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        ASSERT_TRUE(destroyerParams.fBaseDamage > normalParams.fBaseDamage, "Destroyer should increase damage");
        ASSERT_TRUE(destroyerParams.fEffectiveDamage > normalParams.fEffectiveDamage, "Destroyer should increase effective damage");

        // Verify the multiplier is approximately 1.3x
        float multiplier = destroyerParams.fBaseDamage / normalParams.fBaseDamage;
        ASSERT_TRUE(multiplier >= 1.25f && multiplier <= 1.35f, "Destroyer multiplier should be around 1.3x");

        Logger::Info("TestDestroyerBonusCalculation - Normal: %.1f, Destroyer: %.1f, Multiplier: %.2fx",
                    normalParams.fBaseDamage, destroyerParams.fBaseDamage, multiplier);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestDestroyerBonusCalculation - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test last attack buff bonus
 */
bool CAttackForceTests::TestLastAttackBuffBonus() {
    try {
        m_fixture.SetupBasicAttack();

        // Test without last attack buff
        DamageCalculationParams normalParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        // Test with last attack buff
        m_fixture.SetupLastAttackBuffAttacker();
        DamageCalculationParams buffParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        ASSERT_TRUE(buffParams.fBaseDamage > normalParams.fBaseDamage, "Last attack buff should increase damage");
        ASSERT_TRUE(buffParams.fEffectiveDamage > normalParams.fEffectiveDamage, "Last attack buff should increase effective damage");

        // Verify the multiplier is approximately 1.3x
        float multiplier = buffParams.fBaseDamage / normalParams.fBaseDamage;
        ASSERT_TRUE(multiplier >= 1.25f && multiplier <= 1.35f, "Last attack buff multiplier should be around 1.3x");

        Logger::Info("TestLastAttackBuffBonus - Normal: %.1f, Buff: %.1f, Multiplier: %.2fx",
                    normalParams.fBaseDamage, buffParams.fBaseDamage, multiplier);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestLastAttackBuffBonus - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test PvP boss type bonus
 */
bool CAttackForceTests::TestPvPBossTypeBonus() {
    try {
        m_fixture.SetupBasicAttack();

        // Test without boss type
        DamageCalculationParams normalParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        // Test with boss type 2
        m_fixture.SetupPvPBossAttacker(2);
        DamageCalculationParams boss2Params = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        // Test with boss type 6
        m_fixture.SetupPvPBossAttacker(6);
        DamageCalculationParams boss6Params = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        // Test with boss type 0 (no boss bonus)
        m_fixture.SetupPvPBossAttacker(0);
        DamageCalculationParams boss0Params = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        ASSERT_TRUE(boss2Params.fBaseDamage > normalParams.fBaseDamage, "Boss type 2 should increase damage");
        ASSERT_TRUE(boss6Params.fBaseDamage > normalParams.fBaseDamage, "Boss type 6 should increase damage");
        ASSERT_TRUE(boss0Params.fBaseDamage > normalParams.fBaseDamage, "Boss type 0 should increase damage");

        // Verify multipliers
        float multiplier2 = boss2Params.fBaseDamage / normalParams.fBaseDamage;
        float multiplier6 = boss6Params.fBaseDamage / normalParams.fBaseDamage;
        float multiplier0 = boss0Params.fBaseDamage / normalParams.fBaseDamage;

        ASSERT_TRUE(multiplier2 >= 1.15f && multiplier2 <= 1.25f, "Boss type 2/6 multiplier should be around 1.2x");
        ASSERT_TRUE(multiplier6 >= 1.15f && multiplier6 <= 1.25f, "Boss type 2/6 multiplier should be around 1.2x");
        ASSERT_TRUE(multiplier0 >= 1.25f && multiplier0 <= 1.35f, "Boss type 0 multiplier should be around 1.3x");

        Logger::Info("TestPvPBossTypeBonus - Type2: %.2fx, Type6: %.2fx, Type0: %.2fx",
                    multiplier2, multiplier6, multiplier0);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestPvPBossTypeBonus - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test effect modifiers
 */
bool CAttackForceTests::TestEffectModifiers() {
    try {
        m_fixture.SetupBasicAttack();

        // Test without effect modifiers
        DamageCalculationParams normalParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        // Test with accuracy bonus
        m_fixture.SetupAccuracyBonusAttacker();
        float normalHitChance = m_fixture.m_pAttackForce->CalculateHitChance(m_fixture.m_pTarget.get());
        float bonusHitChance = m_fixture.m_pAttackForce->CalculateHitChance(m_fixture.m_pTarget.get(), 0.0f);

        ASSERT_TRUE(bonusHitChance > normalHitChance, "Accuracy effects should increase hit chance");

        // Test with damage rate modifier
        m_fixture.SetupDamageRateBonus();
        DamageCalculationParams bonusParams = m_fixture.m_pAttackForce->CalculateDamage(&m_fixture.m_attackParam);

        ASSERT_TRUE(bonusParams.fBaseDamage > normalParams.fBaseDamage, "Damage rate effect should increase damage");

        Logger::Info("TestEffectModifiers - Hit chance bonus: %.1f%%, Damage bonus: %.1f",
                    bonusHitChance - normalHitChance, bonusParams.fBaseDamage - normalParams.fBaseDamage);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestEffectModifiers - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test invulnerable target
 */
bool CAttackForceTests::TestInvulnerableTarget() {
    try {
        m_fixture.SetupBasicAttack();
        m_fixture.SetupInvulnerableTarget();

        AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);

        ASSERT_TRUE(result.bSuccess, "Attack should succeed even against invulnerable target");
        ASSERT_FALSE(result.bHit, "Attack should miss invulnerable target");
        ASSERT_TRUE(result.nDamage == 0, "No damage should be dealt to invulnerable target");
        ASSERT_TRUE(result.nTargetsHit == 1, "Target should still be counted as processed");

        Logger::Info("TestInvulnerableTarget - Attack processed correctly against invulnerable target");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestInvulnerableTarget - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test high avoidance target
 */
bool CAttackForceTests::TestHighAvoidanceTarget() {
    try {
        m_fixture.SetupBasicAttack();
        m_fixture.SetupHighAvoidanceTarget();

        // Test multiple attacks to verify avoidance is working
        int hits = 0;
        int misses = 0;
        const int numTests = 100;

        for (int i = 0; i < numTests; ++i) {
            AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);
            if (result.bHit && result.nDamage > 0) {
                hits++;
            } else {
                misses++;
            }
        }

        float hitRate = static_cast<float>(hits) / numTests * 100.0f;
        ASSERT_TRUE(hitRate < 50.0f, "High avoidance target should have low hit rate");
        ASSERT_TRUE(misses > hits, "Should miss more than hit against high avoidance target");

        Logger::Info("TestHighAvoidanceTarget - Hit rate: %.1f%% (%d hits, %d misses)",
                    hitRate, hits, misses);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestHighAvoidanceTarget - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test zero damage attack
 */
bool CAttackForceTests::TestZeroDamageAttack() {
    try {
        // Set up attack with zero damage potential
        m_fixture.m_attackParam.SetAttackForce(0, 0);
        m_fixture.m_attackParam.nAddAttPnt = 0;
        m_fixture.m_pAttacker->m_nAttackPower = 0;

        AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);

        ASSERT_TRUE(result.bSuccess, "Zero damage attack should still succeed");
        ASSERT_TRUE(result.nDamage >= 0, "Damage should not be negative");

        Logger::Info("TestZeroDamageAttack - Zero damage attack handled correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestZeroDamageAttack - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test maximum damage attack
 */
bool CAttackForceTests::TestMaximumDamageAttack() {
    try {
        // Set up attack with maximum damage potential
        m_fixture.m_attackParam.SetAttackForce(999999, 999999);
        m_fixture.m_attackParam.nAddAttPnt = 999999;
        m_fixture.m_pAttacker->m_nAttackPower = 999999;
        m_fixture.SetupDestroyerAttacker();
        m_fixture.SetupPvPBossAttacker(0);
        m_fixture.SetupDamageRateBonus();

        AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);

        ASSERT_TRUE(result.bSuccess, "Maximum damage attack should succeed");
        ASSERT_TRUE(result.nDamage <= 999999, "Damage should be clamped to maximum");
        ASSERT_TRUE(result.nDamage > 0, "Should deal significant damage");

        Logger::Info("TestMaximumDamageAttack - Maximum damage: %d", result.nDamage);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestMaximumDamageAttack - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test null parameter handling
 */
bool CAttackForceTests::TestNullParameterHandling() {
    try {
        // Test with null parameters
        AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(nullptr);

        ASSERT_FALSE(result.bSuccess, "Attack with null parameters should fail");
        ASSERT_FALSE(result.errorMessage.empty(), "Should have error message");

        // Test hit chance calculation with null target
        float hitChance = m_fixture.m_pAttackForce->CalculateHitChance(nullptr);
        ASSERT_TRUE(hitChance == 0.0f, "Hit chance with null target should be 0");

        // Test damage calculation with null parameters
        DamageCalculationParams params = m_fixture.m_pAttackForce->CalculateDamage(nullptr);
        ASSERT_TRUE(params.fBaseDamage == 0.0f, "Damage with null parameters should be 0");

        Logger::Info("TestNullParameterHandling - Null parameters handled correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestNullParameterHandling - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test invalid character handling
 */
bool CAttackForceTests::TestInvalidCharacterHandling() {
    try {
        // Test validation with invalid character
        ValidationResult result = m_fixture.m_pErrorHandler->ValidateAttacker(nullptr);
        ASSERT_FALSE(result.bValid, "Null attacker should fail validation");
        ASSERT_TRUE(result.HasErrors(), "Should have validation errors");

        // Test with character having invalid properties
        MockCharacter invalidChar(-1, 0); // Invalid ID and serial
        ValidationResult invalidResult = m_fixture.m_pErrorHandler->ValidateAttacker(&invalidChar);
        ASSERT_FALSE(invalidResult.bValid, "Invalid character should fail validation");

        Logger::Info("TestInvalidCharacterHandling - Invalid characters handled correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestInvalidCharacterHandling - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test invalid attack parameters
 */
bool CAttackForceTests::TestInvalidAttackParameters() {
    try {
        // Test with invalid attack force values
        MockAttackParam invalidParam;
        invalidParam.nMinAF = -100;
        invalidParam.nMaxAF = -50;
        invalidParam.nMinSel = 150;
        invalidParam.nMaxSel = 200;

        ValidationResult result = m_fixture.m_pErrorHandler->ValidateAttackParameters(&invalidParam);
        ASSERT_FALSE(result.bValid, "Invalid parameters should fail validation");
        ASSERT_TRUE(result.HasErrors(), "Should have validation errors");
        ASSERT_TRUE(result.GetErrorCount() > 0, "Should have multiple errors");

        // Test with out-of-range values
        MockAttackParam rangeParam;
        rangeParam.nExtentRange = -1;
        rangeParam.nPart = 20;
        rangeParam.nTol = 15;

        ValidationResult rangeResult = m_fixture.m_pErrorHandler->ValidateAttackParameters(&rangeParam);
        ASSERT_FALSE(rangeResult.bValid, "Out-of-range parameters should fail validation");

        Logger::Info("TestInvalidAttackParameters - Invalid parameters handled correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestInvalidAttackParameters - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test error recovery mechanisms
 */
bool CAttackForceTests::TestErrorRecoveryMechanisms() {
    try {
        // Test error creation and handling
        AttackForceError error = m_fixture.m_pErrorHandler->CreateError(
            AttackForceErrorCategory::CalculationError,
            AttackForceErrorSeverity::Warning,
            "Test error for recovery"
        );

        ASSERT_TRUE(error.category == AttackForceErrorCategory::CalculationError, "Error category should match");
        ASSERT_TRUE(error.severity == AttackForceErrorSeverity::Warning, "Error severity should match");
        ASSERT_FALSE(error.message.empty(), "Error should have message");

        // Test error recovery context
        ErrorRecoveryContext context;
        context.strategy = ErrorRecoveryStrategy::Retry;
        context.maxRetries = 3;

        bool handled = m_fixture.m_pErrorHandler->HandleError(error, context);
        ASSERT_TRUE(handled, "Error should be handled successfully");

        // Test recovery strategies
        m_fixture.m_pErrorHandler->SetRecoveryStrategy(AttackForceErrorCategory::InvalidInput, ErrorRecoveryStrategy::UseDefault);

        Logger::Info("TestErrorRecoveryMechanisms - Error recovery working correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestErrorRecoveryMechanisms - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test attack performance benchmark
 */
bool CAttackForceTests::TestAttackPerformanceBenchmark() {
    try {
        m_fixture.SetupBasicAttack();

        const int numAttacks = 1000;
        auto startTime = std::chrono::high_resolution_clock::now();

        int successfulAttacks = 0;
        for (int i = 0; i < numAttacks; ++i) {
            AttackForceResult result = m_fixture.m_pAttackForce->ExecuteAttack(&m_fixture.m_attackParam);
            if (result.bSuccess) {
                successfulAttacks++;
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);

        double avgTimePerAttack = static_cast<double>(duration.count()) / numAttacks;
        double attacksPerSecond = 1000000.0 / avgTimePerAttack;

        ASSERT_TRUE(successfulAttacks > 0, "Should have successful attacks");
        ASSERT_TRUE(avgTimePerAttack < 1000.0, "Average attack time should be under 1ms");

        Logger::Info("TestAttackPerformanceBenchmark - %d attacks, %.2f μs/attack, %.0f attacks/sec",
                    successfulAttacks, avgTimePerAttack, attacksPerSecond);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestAttackPerformanceBenchmark - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test area attack performance
 */
bool CAttackForceTests::TestAreaAttackPerformance() {
    try {
        m_fixture.SetupAreaAttack();

        AreaDamageConfig config;
        config.fCenterX = 0.0f;
        config.fCenterY = 0.0f;
        config.fCenterZ = 0.0f;
        config.fRadius = 100.0f;
        config.nMaxTargets = 20;

        const int numAttacks = 500;
        auto startTime = std::chrono::high_resolution_clock::now();

        int successfulAttacks = 0;
        for (int i = 0; i < numAttacks; ++i) {
            AreaDamageResult result = m_fixture.m_pAreaProcessor->ProcessAreaDamage(
                m_fixture.m_pAttacker.get(), config, &m_fixture.m_attackParam);
            if (result.bSuccess) {
                successfulAttacks++;
            }
        }

        auto endTime = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(endTime - startTime);

        double avgTimePerAttack = static_cast<double>(duration.count()) / numAttacks;

        ASSERT_TRUE(successfulAttacks > 0, "Should have successful area attacks");
        ASSERT_TRUE(avgTimePerAttack < 2000.0, "Average area attack time should be under 2ms");

        Logger::Info("TestAreaAttackPerformance - %d area attacks, %.2f μs/attack",
                    successfulAttacks, avgTimePerAttack);
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestAreaAttackPerformance - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test AttackForce integration
 */
bool CAttackForceTests::TestAttackForceIntegration() {
    try {
        // Test integration initialization
        bool initialized = CAttackForceIntegration::Initialize();
        ASSERT_TRUE(initialized, "Integration should initialize successfully");
        ASSERT_TRUE(CAttackForceIntegration::IsInitialized(), "Integration should report as initialized");

        // Test legacy function calls
        CAttackForceIntegration::BreakStealth(m_fixture.m_pAttacker.get());

        bool effectState = CAttackForceIntegration::GetEffectState(m_fixture.m_pTarget.get(), 8);
        ASSERT_FALSE(effectState, "Default effect state should be false");

        float effectPlus = CAttackForceIntegration::GetEffectPlus(m_fixture.m_pAttacker.get(), 31);
        ASSERT_TRUE(effectPlus >= 0.0f, "Effect plus should be non-negative");

        float effectRate = CAttackForceIntegration::GetEffectRate(m_fixture.m_pAttacker.get(), 4);
        ASSERT_TRUE(effectRate > 0.0f, "Effect rate should be positive");

        int avoidanceRate = CAttackForceIntegration::GetAvoidanceRate(m_fixture.m_pTarget.get());
        ASSERT_TRUE(avoidanceRate >= 0 && avoidanceRate <= 100, "Avoidance rate should be 0-100");

        // Test attack type conversion
        auto attackType = CAttackForceIntegration::ConvertAttackType(5);
        ASSERT_TRUE(attackType == AttackForceConstants::AttackType::FlashDamage, "Attack type conversion should work");

        int legacyType = CAttackForceIntegration::ConvertAttackType(AttackForceConstants::AttackType::AreaDamage1);
        ASSERT_TRUE(legacyType == 4, "Legacy type conversion should work");

        // Test parameter validation
        bool validParams = CAttackForceIntegration::ValidateAttackParameters(&m_fixture.m_attackParam);
        ASSERT_TRUE(validParams, "Valid parameters should pass validation");

        Logger::Info("TestAttackForceIntegration - Integration working correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestAttackForceIntegration - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test damage calculator integration
 */
bool CAttackForceTests::TestDamageCalculatorIntegration() {
    try {
        // Test damage calculation context
        DamageCalculationContext context;
        context.pAttacker = m_fixture.m_pAttacker.get();
        context.pTarget = m_fixture.m_pTarget.get();
        context.pParam = &m_fixture.m_attackParam;
        context.bUseEffBullet = false;
        context.bCriticalHit = false;
        context.bBackAttack = false;

        ASSERT_TRUE(context.IsValid(), "Context should be valid");

        // Test detailed damage calculation
        DetailedDamageResult result = m_fixture.m_pDamageCalculator->CalculateDamage(context);
        ASSERT_TRUE(result.nFinalDamage >= 0, "Final damage should be non-negative");
        ASSERT_TRUE(result.fTotalMultiplier > 0.0f, "Total multiplier should be positive");
        ASSERT_FALSE(result.calculationLog.empty(), "Should have calculation log");

        // Test with critical hit
        context.bCriticalHit = true;
        DetailedDamageResult critResult = m_fixture.m_pDamageCalculator->CalculateDamage(context);
        ASSERT_TRUE(critResult.bCriticalHit, "Should indicate critical hit");
        ASSERT_TRUE(critResult.fTotalMultiplier > result.fTotalMultiplier, "Critical hit should increase multiplier");

        // Test utility functions
        int varianceDamage = DamageCalculationUtils::ApplyDamageVariance(100, 0.1f);
        ASSERT_TRUE(varianceDamage >= 90 && varianceDamage <= 110, "Variance should be within expected range");

        float elementalMod = DamageCalculationUtils::CalculateElementalModifier(1, 2);
        ASSERT_TRUE(elementalMod > 0.0f, "Elemental modifier should be positive");

        bool isCrit = DamageCalculationUtils::IsCriticalHit(50.0f);
        // Can't assert the result since it's random, but should not crash

        int clampedDamage = DamageCalculationUtils::ClampDamage(1000000);
        ASSERT_TRUE(clampedDamage <= 999999, "Damage should be clamped to maximum");

        Logger::Info("TestDamageCalculatorIntegration - Damage calculator working correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestDamageCalculatorIntegration - Exception: %s", e.what());
        return false;
    }
}

/**
 * Test error handler integration
 */
bool CAttackForceTests::TestErrorHandlerIntegration() {
    try {
        // Test error statistics
        auto initialStats = m_fixture.m_pErrorHandler->GetErrorStatistics();

        // Create some errors
        AttackForceError error1 = m_fixture.m_pErrorHandler->CreateError(
            AttackForceErrorCategory::InvalidInput, AttackForceErrorSeverity::Warning, "Test error 1");
        AttackForceError error2 = m_fixture.m_pErrorHandler->CreateError(
            AttackForceErrorCategory::InvalidInput, AttackForceErrorSeverity::Error, "Test error 2");

        auto updatedStats = m_fixture.m_pErrorHandler->GetErrorStatistics();
        ASSERT_TRUE(updatedStats.size() >= initialStats.size(), "Statistics should be updated");

        // Test error callback
        bool callbackCalled = false;
        m_fixture.m_pErrorHandler->SetErrorCallback([&callbackCalled](const AttackForceError& error) {
            callbackCalled = true;
        });

        m_fixture.m_pErrorHandler->LogError(error1);
        ASSERT_TRUE(callbackCalled, "Error callback should be called");

        // Test recovery strategies
        m_fixture.m_pErrorHandler->SetRecoveryStrategy(AttackForceErrorCategory::InvalidInput, ErrorRecoveryStrategy::Retry);

        ErrorRecoveryContext context;
        bool handled = m_fixture.m_pErrorHandler->HandleError(error1, context);
        ASSERT_TRUE(handled, "Error should be handled");
        ASSERT_TRUE(context.strategy == ErrorRecoveryStrategy::Retry, "Recovery strategy should be set");

        // Test detailed logging
        m_fixture.m_pErrorHandler->SetDetailedLogging(true);
        ASSERT_TRUE(m_fixture.m_pErrorHandler->IsDetailedLoggingEnabled(), "Detailed logging should be enabled");

        // Clear statistics
        m_fixture.m_pErrorHandler->ClearStatistics();
        auto clearedStats = m_fixture.m_pErrorHandler->GetErrorStatistics();
        ASSERT_TRUE(clearedStats.empty(), "Statistics should be cleared");

        Logger::Info("TestErrorHandlerIntegration - Error handler working correctly");
        return true;

    } catch (const std::exception& e) {
        Logger::Error("TestErrorHandlerIntegration - Exception: %s", e.what());
        return false;
    }
}

} // namespace Tests
} // namespace Combat
} // namespace NexusProtection
