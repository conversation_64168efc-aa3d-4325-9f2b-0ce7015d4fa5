/*
 * Function: ?_<PERSON><PERSON>_<PERSON>@CPlayer@@QEAAPEAU_Quest_fld@@PEAU2@E@Z
 * Address: 0x1400CCE90
 */

_base_fld *__usercall CPlayer::_Re<PERSON>_Quest@<rax>(CPlayer *this@<rcx>, _Quest_fld *pQuestFld@<rdx>, char by<PERSON><PERSON><PERSON><PERSON>temIndex@<r8b>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@14
  CMoneySupplyMgr *v7; // rax@19
  CMoneySupplyMgr *v8; // rax@21
  __int64 v10; // [sp+0h] [bp-2B8h]@1
  bool bUseExpAdditionItem[8]; // [sp+20h] [bp-298h]@7
  unsigned int dwSumDalant[2]; // [sp+28h] [bp-290h]@49
  unsigned int dwSumGold[2]; // [sp+30h] [bp-288h]@44
  char *pszFileName; // [sp+38h] [bp-280h]@44
  float *pStdPos; // [sp+40h] [bp-278h]@44
  bool bHide; // [sp+48h] [bp-270h]@44
  _base_fld *v17; // [sp+50h] [bp-268h]@4
  double v18; // [sp+58h] [bp-260h]@4
  unsigned int dwAddDalant; // [sp+60h] [bp-258h]@14
  unsigned int dwAddGold; // [sp+64h] [bp-254h]@14
  int v21; // [sp+68h] [bp-250h]@14
  int j; // [sp+6Ch] [bp-24Ch]@22
  char *Str1; // [sp+70h] [bp-248h]@25
  int nTableCode; // [sp+78h] [bp-240h]@31
  unsigned __int16 *v25; // [sp+80h] [bp-238h]@32
  _STORAGE_LIST::_db_con pItem; // [sp+98h] [bp-220h]@33
  char v27; // [sp+D4h] [bp-1E4h]@36
  _TimeItem_fld *v28; // [sp+D8h] [bp-1E0h]@37
  __time32_t Time; // [sp+E4h] [bp-1D4h]@39
  char Dest; // [sp+110h] [bp-1A8h]@42
  char pszClause; // [sp+1B0h] [bp-108h]@44
  int *v32; // [sp+238h] [bp-80h]@48
  char *v33; // [sp+240h] [bp-78h]@51
  _base_fld *v34; // [sp+248h] [bp-70h]@54
  int k; // [sp+250h] [bp-68h]@55
  char *v36; // [sp+258h] [bp-60h]@58
  unsigned int dwNewStat; // [sp+260h] [bp-58h]@60
  char *v38; // [sp+270h] [bp-48h]@14
  unsigned int v39; // [sp+278h] [bp-40h]@14
  char *szClass; // [sp+280h] [bp-38h]@19
  int nLv; // [sp+288h] [bp-30h]@19
  int v42; // [sp+28Ch] [bp-2Ch]@19
  unsigned int nAmount; // [sp+290h] [bp-28h]@21
  char *v44; // [sp+298h] [bp-20h]@21
  int v45; // [sp+2A0h] [bp-18h]@21
  int v46; // [sp+2A4h] [bp-14h]@21
  unsigned __int64 v47; // [sp+2A8h] [bp-10h]@4
  CPlayer *pOwner; // [sp+2C0h] [bp+8h]@1
  _Quest_fld *v49; // [sp+2C8h] [bp+10h]@1
  char v50; // [sp+2D0h] [bp+18h]@1

  v50 = byRewardItemIndex;
  v49 = pQuestFld;
  pOwner = this;
  v4 = &v10;
  for ( i = 172i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v47 = (unsigned __int64)&v10 ^ _security_cookie;
  v17 = 0i64;
  TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, pOwner->m_id.wIndex);
  v18 = a4;
  if ( v49->m_nMaxLevel != -1 )
    CPlayer::AlterMaxLevel(pOwner, v49->m_nMaxLevel);
  if ( v49->m_dConsExp > 0.0 )
  {
    bUseExpAdditionItem[0] = 1;
    CPlayer::AlterExp(pOwner, v49->m_dConsExp, 1, 0, 1);
  }
  if ( v49->m_nConsContribution > 0 )
    CPlayer::AlterPvPPoint(pOwner, (double)v49->m_nConsContribution, quest_inc, 0xFFFFFFFF);
  if ( v49->m_nConspvppoint > 0 )
  {
    v49->m_nConspvppoint = (signed int)floor((double)v49->m_nConspvppoint * v18);
    CPlayer::AlterPvPCashBag(pOwner, (double)v49->m_nConspvppoint, pm_quest);
  }
  if ( v49->m_nConsDalant > 0 || v49->m_nConsGold > 0 )
  {
    CPlayer::AddDalant(pOwner, v49->m_nConsDalant, 1);
    CPlayer::AddGold(pOwner, v49->m_nConsGold, 1);
    CPlayer::SendMsg_ExchangeMoneyResult(pOwner, 0);
    dwAddDalant = (signed int)floor((double)v49->m_nConsDalant * v18);
    dwAddGold = (signed int)floor((double)v49->m_nConsGold * v18);
    v38 = pOwner->m_szItemHistoryFileName;
    v39 = CPlayerDB::GetGold(&pOwner->m_Param);
    v6 = CPlayerDB::GetDalant(&pOwner->m_Param);
    CMgrAvatorItemHistory::reward_add_money(
      &CPlayer::s_MgrItemHistory,
      pOwner->m_ObjID.m_wIndex,
      "Quest",
      dwAddDalant,
      dwAddGold,
      v6,
      v39,
      v38);
    v21 = CPlayerDB::GetLevel(&pOwner->m_Param);
    if ( v21 == 30 || v21 == 40 || v21 == 50 || v21 == 60 )
    {
      if ( v49->m_nConsDalant > 0 )
      {
        szClass = CPlayerDB::GetPtrCurClass(&pOwner->m_Param)->m_strCode;
        nLv = CPlayerDB::GetLevel(&pOwner->m_Param);
        v42 = CPlayerDB::GetRaceCode(&pOwner->m_Param);
        v7 = CMoneySupplyMgr::Instance();
        *(_DWORD *)bUseExpAdditionItem = v49->m_nConsDalant;
        CMoneySupplyMgr::UpdateQuestRewardMoneyData(v7, v42, nLv, szClass, *(unsigned int *)bUseExpAdditionItem);
      }
      if ( v49->m_nConsGold > 0 )
      {
        nAmount = 2000 * v49->m_nConsGold;
        v44 = CPlayerDB::GetPtrCurClass(&pOwner->m_Param)->m_strCode;
        v45 = CPlayerDB::GetLevel(&pOwner->m_Param);
        v46 = CPlayerDB::GetRaceCode(&pOwner->m_Param);
        v8 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateQuestRewardMoneyData(v8, v46, v45, v44, nAmount);
      }
    }
  }
  for ( j = 0; j < 6; ++j )
  {
    Str1 = v49->m_RewardItem[j].m_strConsITCode;
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOwner->m_id.wIndex) == 99 )
      break;
    if ( (unsigned __int8)v50 != 255 )
    {
      if ( j != (unsigned __int8)v50 )
        continue;
      if ( *((_DWORD *)Str1 + 17) != -1 )
        v17 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v49->m_strLinkQuest[(signed __int64)*((_DWORD *)Str1 + 17)]);
    }
    if ( !strncmp(Str1, "-1", 2ui64) )
      break;
    nTableCode = GetItemTableCode(Str1);
    if ( nTableCode != -1 )
    {
      v25 = (unsigned __int16 *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, Str1);
      if ( v25 )
      {
        _STORAGE_LIST::_db_con::_db_con(&pItem);
        pItem.m_byTableCode = nTableCode;
        pItem.m_wItemIndex = *v25;
        if ( IsOverLapItem(nTableCode) )
          pItem.m_dwDur = *((_DWORD *)Str1 + 16);
        else
          pItem.m_dwDur = GetItemDurPoint(nTableCode, *(_DWORD *)v25);
        v27 = GetDefItemUpgSocketNum(nTableCode, *(_DWORD *)v25);
        pItem.m_dwLv = GetBitAfterSetLimSocket(v27);
        if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
        {
          if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOwner->m_id.wIndex) == 99 )
            break;
          bHide = 0;
          pStdPos = pOwner->m_fCurPos;
          LOWORD(pszFileName) = pOwner->m_wMapLayerIndex;
          *(_QWORD *)dwSumGold = pOwner->m_pCurMap;
          CreateItemBox(
            &pItem,
            pOwner,
            0xFFFFFFFF,
            0,
            0i64,
            3,
            *(CMapData **)dwSumGold,
            (unsigned __int16)pszFileName,
            pOwner->m_fCurPos,
            0);
          sprintf(&pszClause, "Quest G (%s)", v49->m_strCode);
          CMgrAvatorItemHistory::reward_add_item(
            &CPlayer::s_MgrItemHistory,
            pOwner->m_ObjID.m_wIndex,
            &pszClause,
            &pItem,
            pOwner->m_szItemHistoryFileName);
        }
        else
        {
          pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
          v28 = TimeItem::FindTimeRec(nTableCode, *(_DWORD *)v25);
          if ( v28 )
          {
            if ( v28->m_nCheckType )
            {
              pItem.m_byCsMethod = v28->m_nCheckType;
              _time32(&Time);
              pItem.m_dwT = v28->m_nUseTime + Time;
              pItem.m_dwLendRegdTime = Time;
            }
          }
          if ( !CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
          {
            CMgrAvatorItemHistory::add_storage_fail(
              &CPlayer::s_MgrItemHistory,
              pOwner->m_ObjID.m_wIndex,
              &pItem,
              "CPlayer::_Reward_Quest() - Emb_AddStorage() Fail",
              pOwner->m_szItemHistoryFileName);
            continue;
          }
          CPlayer::SendMsg_RewardAddItem(pOwner, &pItem, 2);
          sprintf(&Dest, "Quest (%s)", v49->m_strCode);
          CMgrAvatorItemHistory::reward_add_item(
            &CPlayer::s_MgrItemHistory,
            pOwner->m_ObjID.m_wIndex,
            &Dest,
            &pItem,
            pOwner->m_szItemHistoryFileName);
        }
        CPlayer::SendMsg_FanfareItem(pOwner, 5, &pItem, 0i64);
      }
    }
  }
  for ( j = 0; j < 2; ++j )
  {
    v32 = &v49->m_RewardMastery[j].m_nConsMasteryID;
    if ( *v32 == -1 )
      break;
    LOBYTE(dwSumGold[0]) = 1;
    *(_QWORD *)dwSumDalant = "CPlayer::_Reward_Quest()---0";
    bUseExpAdditionItem[0] = 2;
    CPlayer::Emb_AlterStat(pOwner, *(_BYTE *)v32, *((_BYTE *)v32 + 4), v32[2], 2, "CPlayer::_Reward_Quest()---0", 1);
  }
  if ( strncmp(v49->m_strConsSkillCode, "-1", 2ui64) )
  {
    v33 = (char *)CRecordData::GetRecord(&stru_1799C8410, v49->m_strConsSkillCode);
    if ( v33 )
    {
      LOBYTE(dwSumGold[0]) = 1;
      *(_QWORD *)dwSumDalant = "CPlayer::_Reward_Quest()---1";
      bUseExpAdditionItem[0] = 2;
      CPlayer::Emb_AlterStat(pOwner, 3, *v33, v49->m_nConsSkillCnt, 2, "CPlayer::_Reward_Quest()---1", 1);
    }
  }
  if ( strncmp(v49->m_strConsForceCode, "-1", 2ui64) )
  {
    v34 = CRecordData::GetRecord(&stru_1799C8410 + 1, v49->m_strConsSkillCode);
    if ( v34 )
    {
      for ( k = 0; k < 88; ++k )
      {
        v36 = &pOwner->m_Param.m_dbForce.m_pStorageList[j].m_bLoad;
        if ( *v36 && *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + *(_WORD *)(v36 + 3)) == v34->m_dwIndex )
        {
          LOBYTE(dwSumDalant[0]) = 0;
          bUseExpAdditionItem[0] = 0;
          dwNewStat = CPlayer::Emb_AlterDurPoint(pOwner, 3, v36[49], v49->m_nConsForceCnt, 0, 0);
          CPlayer::SendMsg_FcitemInform(pOwner, *(_WORD *)(v36 + 17), dwNewStat);
          return v17;
        }
      }
    }
  }
  return v17;
}
