/*
 * Function: ?GetBattle@CNormalGuildBattleManager@GUILD_BATTLE@@IEAAPEAVCNormalGuildBattle@2@K@Z
 * Address: 0x1403D5520
 */

GUILD_BATTLE::CNormalGuildBattle *__fastcall GUILD_BATTLE::CNormalGuildBattleManager::GetBattle(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwID)
{
  GUILD_BATTLE::CNormalGuildBattle *result; // rax@3

  if ( this->m_ppkNormalBattle && this->m_uiMaxBattleCnt > dwID )
  {
    if ( this->m_ppkNormalBattle[dwID] )
      result = this->m_ppkNormalBattle[dwID];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
