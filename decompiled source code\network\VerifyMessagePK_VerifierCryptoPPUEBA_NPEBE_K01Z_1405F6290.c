/*
 * Function: ?VerifyMessage@PK_Verifier@CryptoPP@@UEBA_NPEBE_K01@Z
 * Address: 0x1405F6290
 */

char __fastcall CryptoPP::PK_Verifier::VerifyMessage(CryptoPP::PK_Verifier *this, const unsigned __int8 *a2, __int64 a3, const unsigned __int8 *a4, unsigned __int64 a5)
{
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  char v10; // [sp+20h] [bp-38h]@1
  char v11; // [sp+28h] [bp-30h]@1
  __int64 v12; // [sp+30h] [bp-28h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v13; // [sp+38h] [bp-20h]@1
  __int64 v14; // [sp+40h] [bp-18h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v15; // [sp+48h] [bp-10h]@1
  CryptoPP::PK_Verifier *v16; // [sp+60h] [bp+8h]@1
  const unsigned __int8 *v17; // [sp+68h] [bp+10h]@1
  __int64 v18; // [sp+70h] [bp+18h]@1
  const unsigned __int8 *v19; // [sp+78h] [bp+20h]@1

  v19 = a4;
  v18 = a3;
  v17 = a2;
  v16 = this;
  v12 = -2i64;
  LODWORD(v5) = ((int (*)(void))this->vfptr[1].__vecDelDtor)();
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v10, v5);
  LODWORD(v6) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v10);
  v13 = v16->vfptr;
  (*(void (__fastcall **)(CryptoPP::PK_Verifier *, __int64, const unsigned __int8 *, unsigned __int64))&v13[1].gap8[0])(
    v16,
    v6,
    v19,
    a5);
  LODWORD(v7) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator->(&v10);
  v14 = v7;
  (*(void (__fastcall **)(__int64, const unsigned __int8 *, __int64))(*(_QWORD *)v7 + 24i64))(v7, v17, v18);
  LODWORD(v8) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v10);
  v15 = v16->vfptr;
  v11 = ((int (__fastcall *)(CryptoPP::PK_Verifier *, __int64))v15[1].MaxRecoverableLength)(v16, v8);
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v10);
  return v11;
}
