/*
 * Function: ?ct_SetPatriarchAuto@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295150
 */

char __fastcall ct_SetPatriarchAuto(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  PatriarchElectProcessor *v4; // rax@8
  PatriarchElectProcessor *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@7
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = pOne;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v8 || !v8->m_bOper )
    return 0;
  v7 = atoi(s_pwszDstCheat[0]);
  if ( v7 )
  {
    if ( v7 != 1 )
      return 0;
    v5 = PatriarchElectProcessor::Instance();
    PatriarchElectProcessor::SetTimeCheck(v5, 1);
  }
  else
  {
    v4 = PatriarchElectProcessor::Instance();
    PatriarchElectProcessor::SetTimeCheck(v4, 0);
  }
  return 1;
}
