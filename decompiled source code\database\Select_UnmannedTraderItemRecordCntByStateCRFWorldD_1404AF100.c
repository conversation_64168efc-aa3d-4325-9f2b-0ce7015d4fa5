/*
 * Function: ?Select_UnmannedTraderItemRecordCntByState@CRFWorldDatabase@@QEAAEEEPEAKGPEAG@Z
 * Address: 0x1404AF100
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderItemRecordCntByState(CRFWorldDatabase *this, char byType, char byState, unsigned int *pdwSerial, unsigned __int16 wMaxCnt, unsigned __int16 *pwRecordCnt)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  unsigned int *v9; // rax@17
  __int64 v10; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@17
  SQLLEN v13; // [sp+38h] [bp-150h]@17
  __int16 v14; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v16; // [sp+164h] [bp-24h]@4
  unsigned __int64 v17; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+190h] [bp+8h]@1
  unsigned int *v19; // [sp+1A8h] [bp+20h]@1

  v19 = pdwSerial;
  v18 = this;
  v6 = &v10;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v17 = (unsigned __int64)&v10 ^ _security_cookie;
  v16 = 0;
  LODWORD(SQLStmt) = (unsigned __int8)byState;
  sprintf(
    &Dest,
    "select top %u [serial] from tbl_utresultinfo where type=%u and state=%u",
    wMaxCnt,
    (unsigned __int8)byType);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v14 = SQLExecDirectA_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v14 && v14 != 1 )
    {
      if ( v14 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v14, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v14, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      *pwRecordCnt = 0;
      do
      {
        v14 = SQLFetch_0(v18->m_hStmtSelect);
        if ( v14 && v14 != 1 )
          break;
        v9 = &v19[*pwRecordCnt];
        StrLen_or_IndPtr = &v13;
        SQLStmt = 0i64;
        v14 = SQLGetData_0(v18->m_hStmtSelect, 1u, 4, v9, 0i64, &v13);
        ++*pwRecordCnt;
      }
      while ( wMaxCnt > (signed int)*pwRecordCnt );
      if ( v18->m_hStmtSelect )
        SQLCloseCursor_0(v18->m_hStmtSelect);
      if ( v18->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
