/*
 * Function: ?DoDataExchange@CMapTab@@MEAAXPEAVCDataExchange@@@Z
 * Address: 0x14002E5C0
 */

void __fastcall CMapTab::DoDataExchange(CMapTab *this, CDataExchange *pDX)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMapTab *v5; // [sp+30h] [bp+8h]@1
  struct CDataExchange *v6; // [sp+38h] [bp+10h]@1

  v6 = pDX;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CWnd::DoDataExchange((CWnd *)&v5->vfptr, pDX);
  DDX_Control(v6, 1005, (struct CWnd *)&v5->m_trMap.vfptr);
}
