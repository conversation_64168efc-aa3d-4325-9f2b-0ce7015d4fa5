/*
 * Function: SQLInstallDriverEx
 * Address: 0x1404DAD3C
 */

int __fastcall SQLInstallDriverEx(const char *lpszDriver, const char *lpszPathIn, char *lpszPathOut, unsigned __int16 cbPathOutMax, unsigned __int16 *pcbPathOut, unsigned __int16 fRequest, unsigned int *lpdwUsageCount)
{
  const char *v7; // rbp@1
  unsigned __int16 v8; // bx@1
  char *v9; // rdi@1
  const char *v10; // rsi@1
  __int64 (__cdecl *v11)(); // rax@1
  int result; // eax@2

  v7 = lpszDriver;
  v8 = cbPathOutMax;
  v9 = lpszPathOut;
  v10 = lpszPathIn;
  v11 = ODBC___GetSetupProc("SQLInstallDriverEx");
  if ( v11 )
    result = ((int (__fastcall *)(const char *, const char *, char *, _QWORD))v11)(v7, v10, v9, v8);
  else
    result = 0;
  return result;
}
