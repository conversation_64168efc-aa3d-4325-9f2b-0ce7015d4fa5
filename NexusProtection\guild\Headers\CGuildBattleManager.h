#pragma once

/**
 * @file CGuildBattleManager.h
 * @brief Modern C++20 Guild Battle Management System
 * 
 * This file provides comprehensive guild battle management with battle
 * coordination, scheduling, state management, and modern C++20 patterns.
 * 
 * Refactored from decompiled sources:
 * - InitCGuildBattleControllerQEAA_NXZ_1403D5820.c (105 lines)
 * - LoadCGuildBattleControllerQEAA_NXZ_1403D5950.c (116 lines)
 * - AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1403D5DB0.c
 * - LoopCGuildBattleControllerQEAAXXZ_1403D6760.c
 * - CleanUpCGuildBattleControllerIEAAXXZ_1403D7900.c
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <queue>
#include <optional>

// Forward declarations
class CGuild;

namespace NexusProtection {
namespace Guild {

/**
 * @brief Guild battle state enumeration
 */
enum class GuildBattleState : uint8_t {
    None = 0,
    Scheduled = 1,
    Preparing = 2,
    Active = 3,
    Ending = 4,
    Completed = 5,
    Cancelled = 6,
    Error = 7
};

/**
 * @brief Guild battle result enumeration
 */
enum class GuildBattleResult : uint8_t {
    None = 0,
    Victory = 1,
    Defeat = 2,
    Draw = 3,
    Cancelled = 4,
    Timeout = 5
};

/**
 * @brief Guild battle type enumeration
 */
enum class GuildBattleType : uint8_t {
    Normal = 0,
    Tournament = 1,
    Siege = 2,
    Raid = 3,
    Championship = 4
};

/**
 * @brief Guild battle priority enumeration
 */
enum class GuildBattlePriority : uint8_t {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
    Championship = 4
};

/**
 * @brief Guild battle participant information
 */
struct GuildBattleParticipant {
    uint32_t guildId{0};
    uint32_t guildSerial{0};
    std::string guildName;
    uint8_t race{0};
    uint32_t memberCount{0};
    uint32_t averageLevel{0};
    uint32_t totalPower{0};
    std::vector<uint32_t> participantIds;
    
    // Battle statistics
    uint32_t kills{0};
    uint32_t deaths{0};
    uint32_t assists{0};
    uint32_t score{0};
    
    GuildBattleParticipant() = default;
    GuildBattleParticipant(uint32_t id, uint32_t serial, const std::string& name)
        : guildId(id), guildSerial(serial), guildName(name) {}
};

/**
 * @brief Guild battle configuration
 */
struct GuildBattleConfig {
    uint32_t battleId{0};
    GuildBattleType type{GuildBattleType::Normal};
    GuildBattlePriority priority{GuildBattlePriority::Normal};
    
    // Timing
    std::chrono::system_clock::time_point startTime;
    std::chrono::minutes duration{60};
    std::chrono::minutes preparationTime{10};
    
    // Map and location
    uint32_t mapId{0};
    uint32_t mapCode{0};
    std::string mapName;
    uint32_t maxParticipants{100};
    
    // Rules
    bool allowRespawn{true};
    uint32_t respawnDelay{30};
    uint32_t maxDeaths{0}; // 0 = unlimited
    bool friendlyFire{false};
    
    // Rewards
    uint32_t winnerRewardId{0};
    uint32_t loserRewardId{0};
    uint32_t participationRewardId{0};
    
    GuildBattleConfig() {
        startTime = std::chrono::system_clock::now() + std::chrono::hours(1);
    }
    
    bool IsValid() const {
        return battleId > 0 && mapId > 0 && maxParticipants > 0 && 
               duration.count() > 0 && !mapName.empty();
    }
};

/**
 * @brief Guild battle instance
 */
class GuildBattle {
public:
    GuildBattle(const GuildBattleConfig& config);
    ~GuildBattle() = default;

    // State management
    GuildBattleState GetState() const { return m_state; }
    void SetState(GuildBattleState state);
    
    // Configuration
    const GuildBattleConfig& GetConfig() const { return m_config; }
    uint32_t GetBattleId() const { return m_config.battleId; }
    
    // Participants
    bool AddParticipant(const GuildBattleParticipant& participant);
    bool RemoveParticipant(uint32_t guildId);
    std::shared_ptr<GuildBattleParticipant> GetParticipant(uint32_t guildId) const;
    std::vector<std::shared_ptr<GuildBattleParticipant>> GetAllParticipants() const;
    size_t GetParticipantCount() const;
    
    // Battle management
    bool StartBattle();
    bool EndBattle(GuildBattleResult result);
    bool CancelBattle(const std::string& reason);
    
    // Statistics
    void UpdateParticipantStats(uint32_t guildId, uint32_t kills, uint32_t deaths, uint32_t assists);
    void UpdateScore(uint32_t guildId, uint32_t score);
    
    // Timing
    std::chrono::system_clock::time_point GetStartTime() const { return m_config.startTime; }
    std::chrono::system_clock::time_point GetEndTime() const;
    std::chrono::seconds GetRemainingTime() const;
    bool IsActive() const;
    bool IsScheduled() const;
    bool IsCompleted() const;
    
    // Results
    GuildBattleResult GetResult() const { return m_result; }
    uint32_t GetWinnerGuildId() const { return m_winnerGuildId; }
    std::string GetResultSummary() const;

private:
    GuildBattleConfig m_config;
    std::atomic<GuildBattleState> m_state{GuildBattleState::None};
    std::unordered_map<uint32_t, std::shared_ptr<GuildBattleParticipant>> m_participants;
    
    // Battle results
    GuildBattleResult m_result{GuildBattleResult::None};
    uint32_t m_winnerGuildId{0};
    std::string m_cancelReason;
    
    // Timing
    std::chrono::system_clock::time_point m_actualStartTime;
    std::chrono::system_clock::time_point m_actualEndTime;
    
    // Synchronization
    mutable std::mutex m_participantsMutex;
    mutable std::mutex m_stateMutex;
};

/**
 * @brief Guild battle statistics
 */
struct GuildBattleStatistics {
    std::atomic<uint64_t> totalBattles{0};
    std::atomic<uint64_t> activeBattles{0};
    std::atomic<uint64_t> completedBattles{0};
    std::atomic<uint64_t> cancelledBattles{0};
    std::atomic<uint64_t> totalParticipants{0};
    std::chrono::steady_clock::time_point startTime;
    
    GuildBattleStatistics() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetCompletionRate() const {
        uint64_t total = totalBattles.load();
        return total > 0 ? (static_cast<double>(completedBattles.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Guild battle event callback function type
 */
using GuildBattleEventCallback = std::function<void(uint32_t battleId, GuildBattleState state)>;

/**
 * @brief Modern C++20 Guild Battle Manager class
 * 
 * This class provides comprehensive guild battle management with battle
 * coordination, scheduling, state management, and modern C++20 patterns.
 */
class CGuildBattleManager {
public:
    // Constructor and Destructor
    CGuildBattleManager();
    virtual ~CGuildBattleManager();

    // Disable copy constructor and assignment operator
    CGuildBattleManager(const CGuildBattleManager&) = delete;
    CGuildBattleManager& operator=(const CGuildBattleManager&) = delete;

    // Enable move constructor and assignment operator
    CGuildBattleManager(CGuildBattleManager&&) noexcept = default;
    CGuildBattleManager& operator=(CGuildBattleManager&&) noexcept = default;

    /**
     * @brief Initialize guild battle manager
     * 
     * Modern implementation of CGuildBattleController::Init method.
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown guild battle manager
     * 
     * Modern implementation of CGuildBattleController::CleanUp method.
     */
    void Shutdown();

    /**
     * @brief Load guild battle data
     * 
     * Modern implementation of CGuildBattleController::Load method.
     * 
     * @return true if loading successful, false otherwise
     */
    bool LoadData();

    /**
     * @brief Update guild battle system
     * 
     * Modern implementation of CGuildBattleController::Loop method.
     * 
     * @param deltaTime Time elapsed since last update
     */
    void Update(float deltaTime);

    /**
     * @brief Create new guild battle
     * 
     * Modern implementation of CGuildBattleController::Add method.
     * 
     * @param config Battle configuration
     * @return Battle ID if successful, 0 if failed
     */
    uint32_t CreateBattle(const GuildBattleConfig& config);

    /**
     * @brief Add guild to battle
     * 
     * @param battleId Battle identifier
     * @param guild Guild to add
     * @return true if added successfully, false otherwise
     */
    bool AddGuildToBattle(uint32_t battleId, std::shared_ptr<CGuild> guild);

    /**
     * @brief Remove guild from battle
     * 
     * @param battleId Battle identifier
     * @param guildId Guild identifier
     * @return true if removed successfully, false otherwise
     */
    bool RemoveGuildFromBattle(uint32_t battleId, uint32_t guildId);

    /**
     * @brief Start guild battle
     * 
     * @param battleId Battle identifier
     * @return true if started successfully, false otherwise
     */
    bool StartBattle(uint32_t battleId);

    /**
     * @brief End guild battle
     * 
     * @param battleId Battle identifier
     * @param result Battle result
     * @return true if ended successfully, false otherwise
     */
    bool EndBattle(uint32_t battleId, GuildBattleResult result);

    /**
     * @brief Cancel guild battle
     * 
     * @param battleId Battle identifier
     * @param reason Cancellation reason
     * @return true if cancelled successfully, false otherwise
     */
    bool CancelBattle(uint32_t battleId, const std::string& reason);

    /**
     * @brief Get guild battle
     * 
     * @param battleId Battle identifier
     * @return Guild battle instance or nullptr if not found
     */
    std::shared_ptr<GuildBattle> GetBattle(uint32_t battleId) const;

    /**
     * @brief Get active battles
     * 
     * @return Vector of active battle instances
     */
    std::vector<std::shared_ptr<GuildBattle>> GetActiveBattles() const;

    /**
     * @brief Get battles for guild
     * 
     * @param guildId Guild identifier
     * @return Vector of battles involving the guild
     */
    std::vector<std::shared_ptr<GuildBattle>> GetBattlesForGuild(uint32_t guildId) const;

    /**
     * @brief Check if guild is in battle
     * 
     * @param guildId Guild identifier
     * @return true if guild is in active battle, false otherwise
     */
    bool IsGuildInBattle(uint32_t guildId) const;

    /**
     * @brief Get guild battle statistics
     * 
     * @return Current guild battle statistics
     */
    const GuildBattleStatistics& GetStatistics() const { return m_statistics; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Register battle event callback
     * 
     * @param callback Callback function
     */
    void RegisterEventCallback(GuildBattleEventCallback callback);

    /**
     * @brief Get battle report
     * 
     * @return Detailed battle system report
     */
    std::string GetBattleReport() const;

    /**
     * @brief Check if manager is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized; }

    // Singleton access (for legacy compatibility)
    static CGuildBattleManager& Instance();
    static void SetInstance(std::unique_ptr<CGuildBattleManager> instance);

protected:
    // Battle management
    std::unordered_map<uint32_t, std::shared_ptr<GuildBattle>> m_battles;
    std::queue<uint32_t> m_pendingBattles;
    
    // Statistics
    GuildBattleStatistics m_statistics;
    
    // Event callback
    GuildBattleEventCallback m_eventCallback;
    
    // Synchronization
    mutable std::mutex m_battlesMutex;
    mutable std::mutex m_statisticsMutex;
    mutable std::mutex m_callbackMutex;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    std::atomic<uint32_t> m_nextBattleId{1};
    
    // Timing
    std::chrono::steady_clock::time_point m_lastUpdate;
    
    // Singleton instance
    static std::unique_ptr<CGuildBattleManager> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Process pending battles
     */
    void ProcessPendingBattles();

    /**
     * @brief Update active battles
     */
    void UpdateActiveBattles();

    /**
     * @brief Cleanup completed battles
     */
    void CleanupCompletedBattles();

    /**
     * @brief Notify battle event
     * 
     * @param battleId Battle identifier
     * @param state New battle state
     */
    void NotifyBattleEvent(uint32_t battleId, GuildBattleState state);

    /**
     * @brief Validate battle configuration
     * 
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool ValidateBattleConfig(const GuildBattleConfig& config) const;

    /**
     * @brief Generate unique battle ID
     * 
     * @return Unique battle identifier
     */
    uint32_t GenerateBattleId();
};

/**
 * @brief Guild Battle Manager Factory
 */
class CGuildBattleManagerFactory {
public:
    /**
     * @brief Create guild battle manager
     * 
     * @return Unique pointer to guild battle manager
     */
    static std::unique_ptr<CGuildBattleManager> CreateGuildBattleManager();
};

/**
 * @brief Guild battle utility functions
 */
namespace GuildBattleUtils {
    std::string GuildBattleStateToString(GuildBattleState state);
    std::string GuildBattleResultToString(GuildBattleResult result);
    std::string GuildBattleTypeToString(GuildBattleType type);
    std::string GuildBattlePriorityToString(GuildBattlePriority priority);
    GuildBattleState StringToGuildBattleState(const std::string& stateStr);
    GuildBattleResult StringToGuildBattleResult(const std::string& resultStr);
    GuildBattleType StringToGuildBattleType(const std::string& typeStr);
    GuildBattlePriority StringToGuildBattlePriority(const std::string& priorityStr);
}

} // namespace Guild
} // namespace NexusProtection
