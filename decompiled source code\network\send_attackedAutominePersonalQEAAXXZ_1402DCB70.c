/*
 * Function: ?send_attacked@AutominePersonal@@QEAAXXZ
 * Address: 0x1402DCB70
 */

void __fastcall AutominePersonal::send_attacked(AutominePersonal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-B8h]@1
  _personal_automine_alter_dur_zocl v6; // [sp+34h] [bp-84h]@4
  char pbyType; // [sp+54h] [bp-64h]@4
  char v8; // [sp+55h] [bp-63h]@4
  _personal_automine_attacked_zocl v9; // [sp+74h] [bp-44h]@4
  char v10; // [sp+94h] [bp-24h]@4
  char v11; // [sp+95h] [bp-23h]@4
  AutominePersonal *v12; // [sp+C0h] [bp+8h]@1

  v12 = this;
  v1 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _personal_automine_alter_dur_zocl::_personal_automine_alter_dur_zocl(&v6);
  v6.dwObjSerial = v12->m_dwObjSerial;
  v6.wHPRate = CGameObject::CalcCurHPRate((CGameObject *)&v12->vfptr);
  pbyType = 14;
  v8 = 60;
  v3 = _personal_automine_alter_dur_zocl::size(&v6);
  CGameObject::CircleReport((CGameObject *)&v12->vfptr, &pbyType, (char *)&v6, v3, 0);
  _personal_automine_attacked_zocl::_personal_automine_attacked_zocl(&v9);
  v9.wItemSerial = v12->m_wItemSerial;
  v10 = 14;
  v11 = 66;
  v4 = _personal_automine_attacked_zocl::size(&v9);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_pOwner->m_id.wIndex, &v10, (char *)&v9, v4);
}
