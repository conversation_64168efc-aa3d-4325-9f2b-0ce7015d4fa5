/*
 * Function: ?Create@CNationSettingFactoryBR@@UEAAPEAVCNationSettingData@@HPEBD_N@Z
 * Address: 0x14022F4F0
 */

CNationSettingData *__fastcall CNationSettingFactoryBR::Create(CNationSettingFactoryBR *this, int iNationCode, const char *szNationCodeStr, bool bServiceMode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingData *v6; // rax@5
  CNationSettingData *result; // rax@8
  __int64 v8; // [sp+0h] [bp-58h]@1
  CNationSettingData *pkData; // [sp+20h] [bp-38h]@7
  CNationSettingData *v10; // [sp+28h] [bp-30h]@7
  CNationSettingDataBR *v11; // [sp+30h] [bp-28h]@4
  __int64 v12; // [sp+38h] [bp-20h]@4
  CNationSettingData *v13; // [sp+40h] [bp-18h]@5
  CNationSettingFactoryBR *v14; // [sp+60h] [bp+8h]@1
  int v15; // [sp+68h] [bp+10h]@1
  char *_Source; // [sp+70h] [bp+18h]@1
  bool v17; // [sp+78h] [bp+20h]@1

  v17 = bServiceMode;
  _Source = (char *)szNationCodeStr;
  v15 = iNationCode;
  v14 = this;
  v4 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = -2i64;
  v11 = (CNationSettingDataBR *)operator new(0x1C0ui64);
  if ( v11 )
  {
    CNationSettingDataBR::CNationSettingDataBR(v11);
    v13 = v6;
  }
  else
  {
    v13 = 0i64;
  }
  v10 = v13;
  pkData = v13;
  if ( v13 )
  {
    pkData->m_bServiceMode = v17;
    pkData->m_iNationCode = v15;
    strcpy_s<3>((char (*)[3])pkData->m_szNationCodeStr, _Source);
    pkData->m_iANSICodePage = 1252;
    strcpy_s<64>((char (*)[64])pkData->m_szWorldDBID, "rfbraworld");
    strcpy_s<64>((char (*)[64])pkData->m_szWorldDBPW, "#rf%braworld");
    CNationSettingData::SetCahsDBUseExtRefFlag(pkData);
    if ( CNationSettingFactory::RegistCheatTable((CNationSettingFactory *)&v14->vfptr, pkData) )
      result = pkData;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
