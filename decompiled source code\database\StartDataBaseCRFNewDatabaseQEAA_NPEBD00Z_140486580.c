/*
 * Function: ?StartDataBase@CRFNewDatabase@@QEAA_NPEBD00@Z
 * Address: 0x140486580
 */

char __fastcall CRFNewDatabase::StartDataBase(CRFNewDatabase *this, const char *odbcName, const char *accountName, const char *passWord)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  size_t v7; // rax@18
  size_t v8; // rax@18
  size_t v9; // rax@18
  __int64 v10; // [sp+0h] [bp-58h]@1
  SQLSMALLINT NameLength2; // [sp+20h] [bp-38h]@18
  SQLCHAR *Authentication; // [sp+28h] [bp-30h]@18
  SQLSMALLINT NameLength3; // [sp+30h] [bp-28h]@18
  __int16 v14; // [sp+40h] [bp-18h]@4
  CRFNewDatabase *v15; // [sp+60h] [bp+8h]@1
  char *Str; // [sp+68h] [bp+10h]@1
  SQLCHAR *UserName; // [sp+70h] [bp+18h]@1
  SQLCHAR *v18; // [sp+78h] [bp+20h]@1

  v18 = (SQLCHAR *)passWord;
  UserName = (SQLCHAR *)accountName;
  Str = (char *)odbcName;
  v15 = this;
  v4 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CRFNewDatabase::FmtLog(v15, "StartDatabase : %s", odbcName);
  v15->m_bConectionActive = 0;
  v14 = SQLAllocHandle_0(1, 0i64, &v15->m_hEnv);
  if ( v14 )
  {
    CRFNewDatabase::Log(v15, "SQLAllocHandle( SQL_HANDLE_ENV ) Failed!");
    if ( v15->m_hEnv )
    {
      SQLFreeHandle_0(1, v15->m_hEnv);
      v15->m_hEnv = 0i64;
    }
    result = 0;
  }
  else if ( SQLSetEnvAttr_0(v15->m_hEnv, 200, (SQLPOINTER)3, -6) )
  {
    CRFNewDatabase::Log(v15, "SQLSetEnvAttr() Failed!");
    if ( v15->m_hEnv )
    {
      SQLFreeHandle_0(1, v15->m_hEnv);
      v15->m_hEnv = 0i64;
    }
    result = 0;
  }
  else
  {
    v14 = SQLAllocHandle_0(2, v15->m_hEnv, &v15->m_hDbc);
    if ( v14 )
    {
      CRFNewDatabase::Log(v15, "SQLAllocHandle( SQL_HANDLE_DBC ) Failed!");
      if ( v15->m_hEnv )
      {
        SQLFreeHandle_0(1, v15->m_hEnv);
        v15->m_hEnv = 0i64;
      }
      if ( v15->m_hDbc )
      {
        SQLFreeHandle_0(2, v15->m_hDbc);
        v15->m_hDbc = 0i64;
      }
      result = 0;
    }
    else
    {
      v7 = strlen_0(Str);
      memcpy_0(v15->m_szOdbcName, Str, v7);
      v8 = strlen_0(UserName);
      memcpy_0(v15->m_szAccountName, UserName, v8);
      v9 = strlen_0(v18);
      memcpy_0(v15->m_szPassword, v18, v9);
      SQLSetConnectAttr_0(v15->m_hDbc, 103, (SQLPOINTER)3, 0);
      NameLength3 = -3;
      Authentication = v18;
      NameLength2 = -3;
      v14 = SQLConnect_0(v15->m_hDbc, Str, -3, UserName, -3, v18, -3);
      if ( v14 && v14 != 1 )
      {
        CRFNewDatabase::FmtLog(v15, "StartDataBase Fail : %s", Str);
        if ( v15->m_hEnv )
        {
          SQLFreeHandle_0(1, v15->m_hEnv);
          v15->m_hEnv = 0i64;
        }
        if ( v15->m_hDbc )
        {
          SQLFreeHandle_0(2, v15->m_hDbc);
          v15->m_hDbc = 0i64;
        }
        result = 0;
      }
      else if ( CRFNewDatabase::AllocSelectHandle(v15) )
      {
        if ( CRFNewDatabase::AllocUpdateHandle(v15) )
        {
          v15->m_bConectionActive = 1;
          CRFNewDatabase::FmtLog(v15, "StartDatabase Success : %s", Str);
          result = 1;
        }
        else
        {
          CRFNewDatabase::FmtLog(v15, "StartDataBase Fail : %s", Str);
          if ( v15->m_hEnv )
          {
            SQLFreeHandle_0(1, v15->m_hEnv);
            v15->m_hEnv = 0i64;
          }
          if ( v15->m_hDbc )
          {
            SQLFreeHandle_0(2, v15->m_hDbc);
            v15->m_hDbc = 0i64;
          }
          result = 0;
        }
      }
      else
      {
        CRFNewDatabase::FmtLog(v15, "StartDataBase Fail : %s", Str);
        if ( v15->m_hEnv )
        {
          SQLFreeHandle_0(1, v15->m_hEnv);
          v15->m_hEnv = 0i64;
        }
        if ( v15->m_hDbc )
        {
          SQLFreeHandle_0(2, v15->m_hDbc);
          v15->m_hDbc = 0i64;
        }
        result = 0;
      }
    }
  }
  return result;
}
