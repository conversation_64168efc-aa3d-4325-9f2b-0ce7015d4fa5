/*
 * Function: ?RequestAcceptGMCall@GMCallMgr@@QEAA_NPEAVCPlayer@@K@Z
 * Address: 0x1402AA810
 */

char __fastcall GMCallMgr::RequestAcceptGMCall(GMCallMgr *this, CPlayer *pOne, unsigned int dwUserSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@18
  char result; // al@20
  __int64 v7; // [sp+0h] [bp-78h]@1
  float *pfStartPos; // [sp+20h] [bp-58h]@16
  int nErrorCode; // [sp+30h] [bp-48h]@4
  GMRequestData *v10; // [sp+38h] [bp-40h]@4
  float pNewPos; // [sp+48h] [bp-30h]@14
  CPlayer *pOnea; // [sp+68h] [bp-10h]@4
  GMCallMgr *v13; // [sp+80h] [bp+8h]@1
  CPlayer *pOneGM; // [sp+88h] [bp+10h]@1

  pOneGM = pOne;
  v13 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  nErrorCode = 0;
  v10 = 0i64;
  pOnea = 0i64;
  if ( pOne && pOne->m_byUserDgr >= 2 )
  {
    pOnea = GetPtrPlayerFromSerial(&g_Player, 2532, dwUserSerial);
    if ( pOnea && pOnea->m_pCurMap && pOnea->m_pCurMap->m_pMapSet && !pOnea->m_pCurMap->m_pMapSet->m_nMapType )
    {
      v10 = GMCallMgr::GetGMRequestDataPtr(v13, pOnea);
      if ( v10 )
      {
        CMapData::GetRandPosInRange(pOnea->m_pCurMap, pOnea->m_fCurPos, 30, &pNewPos);
        if ( CMapData::IsMapIn(pOnea->m_pCurMap, &pNewPos) )
        {
          pfStartPos = &pNewPos;
          if ( CPlayer::OutOfMap(pOneGM, pOnea->m_pCurMap, 0, 4, &pNewPos) )
          {
            v5 = (char *)pOnea->m_pCurMap->m_pMapSet;
            LOBYTE(pfStartPos) = 4;
            CPlayer::SendMsg_GotoRecallResult(pOneGM, 0, *v5, &pNewPos, 4);
            GMCallMgr::RequestGMCall(v13, pOnea, 0);
          }
          else
          {
            nErrorCode = 2;
          }
        }
        else
        {
          nErrorCode = 2;
        }
      }
      else
      {
        nErrorCode = 1;
      }
    }
    else
    {
      nErrorCode = 3;
    }
  }
  else
  {
    nErrorCode = 3;
  }
  if ( nErrorCode )
  {
    GMCallMgr::SendResponseAcceptResult(v13, pOneGM, pOnea, nErrorCode);
    result = 0;
  }
  else
  {
    GMCallMgr::SendResponseAcceptResult(v13, pOneGM, pOnea, 0);
    result = 1;
  }
  return result;
}
