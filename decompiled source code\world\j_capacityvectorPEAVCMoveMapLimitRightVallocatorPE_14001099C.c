/*
 * Function: j_?capacity@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBA_KXZ
 * Address: 0x14001099C
 */

unsigned __int64 __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::capacity(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::capacity(this);
}
