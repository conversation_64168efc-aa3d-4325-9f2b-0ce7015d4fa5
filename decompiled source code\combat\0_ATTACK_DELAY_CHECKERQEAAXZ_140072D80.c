/*
 * Function: ??0_ATTACK_DELAY_CHECKER@@QEAA@XZ
 * Address: 0x140072D80
 */

void __fastcall _ATTACK_DELAY_CHECKER::_ATTACK_DELAY_CHECKER(_ATTACK_DELAY_CHECKER *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _ATTACK_DELAY_CHECKER *__t; // [sp+30h] [bp+8h]@1

  __t = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(__t, 8ui64, 10, (void *(__cdecl *)(void *))_ATTACK_DELAY_CHECKER::_eff_list::_eff_list);
  `vector constructor iterator'(
    __t->MAS,
    8ui64,
    10,
    (void *(__cdecl *)(void *))_ATTACK_DELAY_CHECKER::_mas_list::_mas_list);
  _ATTACK_DELAY_CHECKER::Init(__t);
}
