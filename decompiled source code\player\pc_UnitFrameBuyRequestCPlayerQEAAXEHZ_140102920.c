/*
 * Function: ?pc_UnitFrameBuyRequest@CPlayer@@QEAAXEH@Z
 * Address: 0x140102920
 */

void __usercall CPlayer::pc_UnitFrameBuyRequest(CPlayer *this@<rcx>, char byFrameCode@<dl>, int bUseNPCLinkIntem@<r8d>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@27
  unsigned int v7; // eax@42
  CMoneySupplyMgr *v8; // rax@52
  CMoneySupplyMgr *v9; // rax@54
  __int64 v10; // [sp+0h] [bp-188h]@1
  unsigned int dwLv[2]; // [sp+20h] [bp-168h]@40
  unsigned __int16 wSerial[2]; // [sp+28h] [bp-160h]@40
  char v13; // [sp+40h] [bp-148h]@4
  char v14; // [sp+41h] [bp-147h]@4
  unsigned int dwSub; // [sp+58h] [bp-130h]@4
  unsigned int v16; // [sp+5Ch] [bp-12Ch]@4
  _base_fld *v17; // [sp+88h] [bp-100h]@4
  unsigned __int16 *v18; // [sp+90h] [bp-F8h]@4
  char Dst[20]; // [sp+A4h] [bp-E4h]@29
  _base_fld *v20; // [sp+B8h] [bp-D0h]@4
  unsigned int v21; // [sp+C0h] [bp-C8h]@7
  float v22; // [sp+C4h] [bp-C4h]@7
  int j; // [sp+C8h] [bp-C0h]@16
  unsigned __int64 v24; // [sp+D0h] [bp-B8h]@27
  _base_fld *v25; // [sp+D8h] [bp-B0h]@33
  unsigned __int16 v26; // [sp+E0h] [bp-A8h]@36
  __int16 v27; // [sp+E4h] [bp-A4h]@36
  _UNIT_DB_BASE::_LIST *pSlotData; // [sp+E8h] [bp-A0h]@37
  int v29; // [sp+F0h] [bp-98h]@40
  _STORAGE_LIST::_storage_con pCon; // [sp+108h] [bp-80h]@40
  int v31; // [sp+144h] [bp-44h]@47
  __int64 v32; // [sp+150h] [bp-38h]@27
  char *v33; // [sp+158h] [bp-30h]@42
  unsigned int dwNewGold; // [sp+160h] [bp-28h]@42
  unsigned int nAmount; // [sp+164h] [bp-24h]@52
  int nLv; // [sp+168h] [bp-20h]@52
  int v37; // [sp+16Ch] [bp-1Ch]@54
  unsigned __int64 v38; // [sp+170h] [bp-18h]@4
  CPlayer *p; // [sp+190h] [bp+8h]@1
  char v40; // [sp+198h] [bp+10h]@1
  int v41; // [sp+1A0h] [bp+18h]@1

  v41 = bUseNPCLinkIntem;
  v40 = byFrameCode;
  p = this;
  v4 = &v10;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v38 = (unsigned __int64)&v10 ^ _security_cookie;
  v13 = 0;
  v14 = -1;
  dwSub = 0;
  memset(&v16, 0, 0x18ui64);
  v17 = CRecordData::GetRecord(&stru_1799C8BA0, (unsigned __int8)byFrameCode);
  v18 = 0i64;
  v20 = 0i64;
  if ( p->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      v13 = 34;
    }
    else
    {
      v21 = eGetTexRate(0) + 10000;
      eGetTex(0);
      v22 = a4 + 1.0;
      if ( v41 || IsBeNearStore(p, 4) )
      {
        if ( v17 )
        {
          if ( CPlayerDB::GetRaceCode(&p->m_Param) )
          {
            v13 = 1;
          }
          else if ( p->m_pUsingUnit )
          {
            v13 = 2;
          }
          else
          {
            for ( j = 0; j < 4; ++j )
            {
              if ( p->m_Param.m_UnitDB.m_List[j].byFrame == 255 )
              {
                v14 = j;
                break;
              }
            }
            if ( (unsigned __int8)v14 == 255 )
            {
              v13 = 3;
            }
            else if ( _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&p->m_Param.m_dbInven.m_nListNum) )
            {
              v18 = (unsigned __int16 *)gGetUnitKeyMatchFrame(v40);
              if ( v18 )
              {
                v24 = *(_DWORD *)&v17[1].m_strCode[28];
                v24 *= v21;
                *(&dwSub + *(_DWORD *)&v17[1].m_strCode[24]) = v24 / 0x2710;
                v32 = *(_DWORD *)&v17[1].m_strCode[24];
                v6 = CPlayer::GetMoney(p, v17[1].m_strCode[24]);
                if ( *(&dwSub + v32) <= v6 )
                {
                  memset_0(Dst, 255, 6ui64);
                  v20 = v17 + 2;
                  for ( j = 0; j < 6; ++j )
                  {
                    if ( strncmp((const char *)v20 + 64 * (signed __int64)j, "-1", 2ui64) )
                    {
                      v25 = CRecordData::GetRecord(&stru_1799C86D0 + j, (const char *)v20 + 64 * (signed __int64)j);
                      if ( !v25 )
                      {
                        v13 = 100;
                        break;
                      }
                      Dst[j] = v25->m_dwIndex;
                    }
                  }
                }
                else
                {
                  v13 = 7;
                }
              }
              else
              {
                v13 = 6;
              }
            }
            else
            {
              v13 = 4;
            }
          }
        }
        else
        {
          v13 = 9;
        }
      }
      else
      {
        v13 = 21;
      }
    }
    v26 = -1;
    v27 = -1;
    if ( !v13 )
    {
      pSlotData = &p->m_Param.m_UnitDB.m_List[(unsigned __int8)v14];
      _UNIT_DB_BASE::_LIST::DelUnit(pSlotData);
      pSlotData->byFrame = v40;
      for ( j = 0; j < 6; ++j )
        pSlotData->byPart[j] = Dst[j];
      pSlotData->dwGauge = *(_DWORD *)&v17[1].m_strCode[0];
      CUserDB::Update_UnitInsert(p->m_pUserDB, v14, pSlotData);
      v26 = CPlayerDB::GetNewItemSerial(&p->m_Param);
      v27 = *v18;
      v29 = -1;
      wSerial[0] = v26;
      dwLv[0] = (unsigned __int8)v14;
      _STORAGE_LIST::_storage_con::_storage_con(&pCon, 19, *v18, 0, (unsigned __int8)v14, v26);
      if ( !CPlayer::Emb_AddStorage(p, 0, &pCon, 0, 1) )
      {
        CPlayer::SendMsg_UnitFrameBuyResult(p, -1, v40, v14, v27, v26, &dwSub);
        return;
      }
      CPlayer::SubDalant(p, dwSub);
      CPlayer::SubGold(p, v16);
      v33 = p->m_szItemHistoryFileName;
      dwNewGold = CPlayerDB::GetGold(&p->m_Param);
      v7 = CPlayerDB::GetDalant(&p->m_Param);
      CMgrAvatorItemHistory::buy_unit(
        &CPlayer::s_MgrItemHistory,
        p->m_ObjID.m_wIndex,
        v14,
        pSlotData,
        &dwSub,
        v7,
        dwNewGold,
        v33);
      if ( !p->m_byUserDgr )
      {
        if ( v16 )
          eAddGold(0, v16);
        if ( dwSub )
          eAddDalant(0, dwSub);
      }
      v31 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v31 == 30 || v31 == 40 || v31 == 50 || v31 == 60 )
      {
        if ( v16 )
        {
          nAmount = 2000 * v16;
          nLv = CPlayerDB::GetLevel(&p->m_Param);
          v8 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v8, nLv, nAmount);
        }
        if ( dwSub )
        {
          v37 = CPlayerDB::GetLevel(&p->m_Param);
          v9 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v9, v37, dwSub);
        }
      }
    }
    CPlayer::SendMsg_UnitFrameBuyResult(p, v13, v40, v14, v27, v26, &dwSub);
  }
}
