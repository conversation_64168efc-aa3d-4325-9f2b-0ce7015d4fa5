/*
 * Function: ?GetInvisible@CCharacter@@QEAA_NXZ
 * Address: 0x1401753E0
 */

bool __fastcall CCharacter::GetInvisible(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CCharacter *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_bCorpse )
  {
    result = 0;
  }
  else if ( v5->m_bBreakTranspar )
  {
    result = 0;
  }
  else
  {
    result = _effect_parameter::GetEff_State(&v5->m_EP, 26) != 0;
  }
  return result;
}
