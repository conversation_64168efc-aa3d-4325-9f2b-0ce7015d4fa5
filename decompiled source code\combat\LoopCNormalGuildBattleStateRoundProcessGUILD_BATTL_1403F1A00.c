/*
 * Function: ?Loop@CNormalGuildBattleStateRoundProcess@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F1A00
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundProcess::Loop(GUILD_BATTLE::CNormalGuildBattleStateRoundProcess *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v4; // rax@5
  signed __int64 result; // rax@5
  GUILD_BATTLE::CNormalGuildBattleLogger *v6; // rax@9
  GUILD_BATTLE::CNormalGuildBattleLogger *v7; // rax@11
  __int64 v8; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v9; // [sp+20h] [bp-18h]@8
  GUILD_BATTLE::CNormalGuildBattleStateRoundProcess *v10; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *v11; // [sp+48h] [bp+10h]@1

  v11 = pkBattle;
  v10 = this;
  v2 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pkTimer )
  {
    if ( CMyTimer::CountingTimer(v10->m_pkTimer) )
    {
      v9 = GUILD_BATTLE::CNormalGuildBattle::GetField(v11);
      if ( GUILD_BATTLE::CNormalGuildBattleField::CheckIsInTown(v9) )
      {
        GUILD_BATTLE::CNormalGuildBattle::SetGotoRegenStart(v11);
        v6 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(v11);
        GUILD_BATTLE::CNormalGuildBattleLogger::Log(
          v6,
          "CNormalGuildBattleStateRoundProcess::Loop( CNormalGuildBattle * pkBattle ) : Stone Owner Into The Town! -> GotoRegenStart");
        result = 3i64;
      }
      else if ( GUILD_BATTLE::CNormalGuildBattleField::CheckBallTakeLimitTime(v9) )
      {
        GUILD_BATTLE::CNormalGuildBattle::NotifyPassGravityStoneLimitTime(v11);
        GUILD_BATTLE::CNormalGuildBattle::SetGotoRegenStart(v11);
        v7 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(v11);
        GUILD_BATTLE::CNormalGuildBattleLogger::Log(
          v7,
          "CNormalGuildBattleStateRoundProcess::Loop( CNormalGuildBattle * pkBattle ) : Stone Have Time Limit! -> GotoRegenStart");
        result = 3i64;
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    v4 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(pkBattle);
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleStateRoundProcess::Loop( CNormalGuildBattle * pkBattle ) :  0 == m_pkTimer !");
    result = 0i64;
  }
  return result;
}
