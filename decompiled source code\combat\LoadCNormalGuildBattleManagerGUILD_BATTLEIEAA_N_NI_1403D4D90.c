/*
 * Function: ?Load@CNormalGuildBattleManager@GUILD_BATTLE@@IEAA_N_NIPEAPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403D4D90
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Load(GUILD_BATTLE::CNormalGuildBattleManager *this, bool bToday, unsigned int uiDayID, GUILD_BATTLE::CNormalGuildBattle **ppkStart)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@4
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-5D8h]@1
  unsigned int dwP1GuildSerial; // [sp+20h] [bp-5B8h]@8
  unsigned int dwP2GuildSerial; // [sp+28h] [bp-5B0h]@8
  unsigned int dwMapID; // [sp+30h] [bp-5A8h]@8
  char byNumber; // [sp+38h] [bp-5A0h]@8
  _worlddb_guild_battle_info kInfo; // [sp+50h] [bp-588h]@4
  unsigned int v14; // [sp+5C4h] [bp-14h]@6
  unsigned int j; // [sp+5C8h] [bp-10h]@6
  GUILD_BATTLE::CNormalGuildBattleManager *v16; // [sp+5E0h] [bp+8h]@1
  bool v17; // [sp+5E8h] [bp+10h]@1
  unsigned int uiDayIDa; // [sp+5F0h] [bp+18h]@1
  GUILD_BATTLE::CNormalGuildBattle **v19; // [sp+5F8h] [bp+20h]@1

  v19 = ppkStart;
  uiDayIDa = uiDayID;
  v17 = bToday;
  v16 = this;
  v4 = &v8;
  for ( i = 372i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = GUILD_BATTLE::CNormalGuildBattle::GetID(*ppkStart);
  if ( GUILD_BATTLE::CNormalGuildBattleManager::LoadDBGuildBattleInfo(v16, v6, &kInfo) )
  {
    v14 = 0;
    for ( j = 0; j < kInfo.wCount; ++j )
    {
      v14 = kInfo.list[j].dwID % (23 * v16->m_uiMapCnt);
      byNumber = kInfo.list[j].byNumber;
      dwMapID = kInfo.list[j].dwMapID;
      dwP2GuildSerial = kInfo.list[j].dwP2GuildSerial;
      dwP1GuildSerial = kInfo.list[j].dwP1GuildSerial;
      if ( !GUILD_BATTLE::CNormalGuildBattle::Init(
              v19[v14],
              v17,
              uiDayIDa,
              kInfo.list[j].dwID,
              dwP1GuildSerial,
              dwP2GuildSerial,
              dwMapID,
              byNumber) )
        GUILD_BATTLE::CNormalGuildBattle::Clear(v19[v14]);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
