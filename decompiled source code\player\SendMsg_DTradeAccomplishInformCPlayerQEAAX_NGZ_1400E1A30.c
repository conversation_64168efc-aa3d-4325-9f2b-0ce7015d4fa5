/*
 * Function: ?SendMsg_DTradeAccomplishInform@CPlayer@@QEAAX_NG@Z
 * Address: 0x1400E1A30
 */

void __fastcall CPlayer::SendMsg_DTradeAccomplishInform(CPlayer *this, bool bSucc, unsigned __int16 wStartSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  unsigned int v7; // [sp+3Ch] [bp-4Ch]@4
  unsigned __int16 v8; // [sp+40h] [bp-48h]@4
  bool v9; // [sp+42h] [bp-46h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v11; // [sp+65h] [bp-23h]@4
  CPlayer *v12; // [sp+90h] [bp+8h]@1
  unsigned __int16 v13; // [sp+A0h] [bp+18h]@1

  v13 = wStartSerial;
  v12 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = bSucc;
  *(_DWORD *)szMsg = CPlayerDB::GetDalant(&v12->m_Param);
  v7 = CPlayerDB::GetGold(&v12->m_Param);
  v8 = v13;
  pbyType = 18;
  v11 = 25;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 0xBu);
}
