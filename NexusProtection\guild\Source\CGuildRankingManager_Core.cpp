/**
 * @file CGuildRankingManager_Core.cpp
 * @brief Modern C++20 Guild Ranking Manager core implementation
 * 
 * This file provides the core implementation of the CGuildRankingManager class
 * with comprehensive ranking and statistics management.
 */

#include "../Headers/CGuildRankingManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>
#include <iomanip>

// Legacy includes for compatibility
extern "C" {
    class CRFWorldDatabase {
    public:
        static bool SelectGuildBattleRankRecord(CRFWorldDatabase* db, uint32_t guildSerial);
        static bool InsertGuildBattleRankRecord(CRFWorldDatabase* db, uint32_t guildSerial);
        static bool SelectGuildBattleRankList(CRFWorldDatabase* db, uint8_t race, void* outData);
        static bool UpdateGuildBattleRank(CRFWorldDatabase* db, uint32_t guildSerial, 
                                         uint32_t wins, uint32_t draws, uint32_t losses, uint32_t score);
        static bool ExecUpdateQuery(CRFWorldDatabase* db, const char* query, int param);
    };
    
    extern CRFWorldDatabase* pkDB;
    extern void* memset_0(void* dest, int value, size_t count);
}

namespace NexusProtection {
namespace Guild {

// Static member definitions
std::unique_ptr<CGuildRankingManager> CGuildRankingManager::s_instance = nullptr;
std::mutex CGuildRankingManager::s_instanceMutex;

// CGuildRankingManager implementation
CGuildRankingManager::CGuildRankingManager() {
    std::cout << "[INFO] CGuildRankingManager constructor called" << std::endl;
    
    // Initialize versions
    for (size_t i = 0; i < 3; ++i) {
        m_versions[i] = 1;
    }
}

CGuildRankingManager::~CGuildRankingManager() {
    std::cout << "[INFO] CGuildRankingManager destructor called" << std::endl;
    Shutdown();
}

bool CGuildRankingManager::Initialize() {
    try {
        std::cout << "[INFO] Initializing CGuildRankingManager" << std::endl;
        
        if (m_isInitialized) {
            std::cout << "[WARNING] Guild ranking manager already initialized" << std::endl;
            return true;
        }
        
        // Initialize database connection
        m_database = std::shared_ptr<CRFWorldDatabase>(pkDB, [](CRFWorldDatabase*) {
            // Don't delete pkDB as it's managed externally
        });
        
        if (!m_database) {
            std::cerr << "[ERROR] Database connection not available" << std::endl;
            return false;
        }
        
        // Initialize ranking data structures
        for (size_t i = 0; i < 3; ++i) {
            m_rankings[i].clear();
            m_rankingPages[i].clear();
            m_versions[i] = 1;
        }
        
        // Reset statistics
        m_stats = GuildRankingStats{};
        
        m_isInitialized = true;
        m_isShutdown = false;
        
        std::cout << "[INFO] CGuildRankingManager initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CGuildRankingManager::Initialize: " << e.what() << std::endl;
        return false;
    }
}

void CGuildRankingManager::Shutdown() {
    try {
        std::cout << "[INFO] Shutting down CGuildRankingManager" << std::endl;
        
        if (m_isShutdown) {
            return;
        }
        
        // Clear all ranking data
        for (size_t i = 0; i < 3; ++i) {
            std::lock_guard<std::mutex> lock(m_rankingMutexes[i]);
            m_rankings[i].clear();
            m_rankingPages[i].clear();
        }
        
        m_isInitialized = false;
        m_isShutdown = true;
        
        std::cout << "[INFO] CGuildRankingManager shutdown completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CGuildRankingManager::Shutdown: " << e.what() << std::endl;
    }
}

bool CGuildRankingManager::LoadData() {
    try {
        std::cout << "[INFO] Loading all guild ranking data" << std::endl;
        
        if (!m_isInitialized) {
            std::cerr << "[ERROR] Guild ranking manager not initialized" << std::endl;
            return false;
        }
        
        // Load data for all races
        for (size_t i = 0; i < 3; ++i) {
            if (!LoadData(static_cast<GuildRace>(i))) {
                std::cerr << "[ERROR] Failed to load ranking data for race " << i << std::endl;
                return false;
            }
        }
        
        std::cout << "[INFO] All guild ranking data loaded successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in LoadData: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildRankingManager::LoadData(GuildRace race) {
    try {
        if (!IsValidRace(race)) {
            std::cerr << "[ERROR] Invalid race: " << static_cast<int>(race) << std::endl;
            return false;
        }
        
        size_t raceIndex = GetRaceIndex(race);
        std::lock_guard<std::mutex> lock(m_rankingMutexes[raceIndex]);
        
        std::cout << "[DEBUG] Loading ranking data for race " << static_cast<int>(race) << std::endl;
        
        // Load from database
        if (!LoadRankingsFromDatabase(race)) {
            std::cerr << "[ERROR] Failed to load rankings from database for race " << static_cast<int>(race) << std::endl;
            return false;
        }
        
        // Sort and update pages
        SortRankings(race);
        UpdateRankingPages(race);
        
        // Update statistics
        UpdateStatistics(race);
        
        std::cout << "[DEBUG] Loaded " << m_rankings[raceIndex].size() 
                 << " guilds for race " << static_cast<int>(race) << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in LoadData for race: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildRankingManager::UpdateBattleResult(GuildRace winnerRace, uint32_t winnerGuildSerial,
                                             GuildRace loserRace, uint32_t loserGuildSerial, bool isDraw) {
    try {
        if (!m_isInitialized) {
            return false;
        }
        
        std::cout << "[DEBUG] Updating battle result - Winner: " << winnerGuildSerial 
                 << " (Race " << static_cast<int>(winnerRace) << "), Loser: " << loserGuildSerial 
                 << " (Race " << static_cast<int>(loserRace) << "), Draw: " << isDraw << std::endl;
        
        // Update winner
        if (IsValidRace(winnerRace)) {
            size_t winnerRaceIndex = GetRaceIndex(winnerRace);
            std::lock_guard<std::mutex> lock(m_rankingMutexes[winnerRaceIndex]);
            
            auto& winnerRankings = m_rankings[winnerRaceIndex];
            auto winnerIt = winnerRankings.find(winnerGuildSerial);
            
            if (winnerIt != winnerRankings.end()) {
                if (isDraw) {
                    winnerIt->second.AddBattleResult(false, true, 1); // Draw gives 1 point
                } else {
                    winnerIt->second.AddBattleResult(true, false, 3); // Win gives 3 points
                }
                
                // Update in database
                SaveRankingsToDatabase(winnerRace);
            }
        }
        
        // Update loser
        if (IsValidRace(loserRace)) {
            size_t loserRaceIndex = GetRaceIndex(loserRace);
            std::lock_guard<std::mutex> lock(m_rankingMutexes[loserRaceIndex]);
            
            auto& loserRankings = m_rankings[loserRaceIndex];
            auto loserIt = loserRankings.find(loserGuildSerial);
            
            if (loserIt != loserRankings.end()) {
                if (isDraw) {
                    loserIt->second.AddBattleResult(false, true, 1); // Draw gives 1 point
                } else {
                    loserIt->second.AddBattleResult(false, false, 0); // Loss gives 0 points
                }
                
                // Update in database
                SaveRankingsToDatabase(loserRace);
            }
        }
        
        // Re-sort and update pages for affected races
        if (IsValidRace(winnerRace)) {
            SortRankings(winnerRace);
            UpdateRankingPages(winnerRace);
        }
        
        if (IsValidRace(loserRace) && loserRace != winnerRace) {
            SortRankings(loserRace);
            UpdateRankingPages(loserRace);
        }
        
        m_stats.totalBattleResults++;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UpdateBattleResult: " << e.what() << std::endl;
        return false;
    }
}

std::optional<GuildRankingPage> CGuildRankingManager::GetRankingList(GuildRace race, uint8_t page) {
    try {
        if (!IsValidRace(race)) {
            return std::nullopt;
        }
        
        size_t raceIndex = GetRaceIndex(race);
        std::lock_guard<std::mutex> lock(m_rankingMutexes[raceIndex]);
        
        const auto& pages = m_rankingPages[raceIndex];
        
        if (page >= pages.size()) {
            return std::nullopt;
        }
        
        return pages[page];
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in GetRankingList: " << e.what() << std::endl;
        return std::nullopt;
    }
}

GuildRankingSearchResult CGuildRankingManager::FindGuild(GuildRace race, uint32_t guildSerial) {
    try {
        if (!IsValidRace(race)) {
            return GuildRankingSearchResult();
        }
        
        size_t raceIndex = GetRaceIndex(race);
        std::lock_guard<std::mutex> lock(m_rankingMutexes[raceIndex]);
        
        const auto& rankings = m_rankings[raceIndex];
        auto it = rankings.find(guildSerial);
        
        if (it == rankings.end()) {
            return GuildRankingSearchResult();
        }
        
        // Find rank by iterating through sorted rankings
        uint32_t rank = 1;
        for (const auto& pair : rankings) {
            if (pair.second.score > it->second.score ||
                (pair.second.score == it->second.score && pair.second.wins > it->second.wins)) {
                rank++;
            }
        }
        
        // Calculate page
        uint8_t page = static_cast<uint8_t>((rank - 1) / GuildRankingPage::ENTRIES_PER_PAGE);
        int32_t indexInPage = static_cast<int32_t>((rank - 1) % GuildRankingPage::ENTRIES_PER_PAGE);
        
        GuildRankingSearchResult result(true, rank, page, indexInPage);
        result.entry = it->second;
        
        return result;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in FindGuild: " << e.what() << std::endl;
        return GuildRankingSearchResult();
    }
}

bool CGuildRankingManager::CheckRecord(uint32_t guildSerial) {
    try {
        std::lock_guard<std::mutex> lock(m_databaseMutex);
        
        if (CRFWorldDatabase::SelectGuildBattleRankRecord(m_database.get(), guildSerial)) {
            return true;
        } else {
            return CRFWorldDatabase::InsertGuildBattleRankRecord(m_database.get(), guildSerial);
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CheckRecord: " << e.what() << std::endl;
        return false;
    }
}

void CGuildRankingManager::ClearRankings(GuildRace race) {
    try {
        if (!IsValidRace(race)) {
            return;
        }
        
        size_t raceIndex = GetRaceIndex(race);
        std::lock_guard<std::mutex> lock(m_rankingMutexes[raceIndex]);
        
        m_rankings[raceIndex].clear();
        m_rankingPages[raceIndex].clear();
        m_versions[raceIndex]++;
        
        std::cout << "[DEBUG] Cleared rankings for race " << static_cast<int>(race) << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ClearRankings: " << e.what() << std::endl;
    }
}

void CGuildRankingManager::ClearAllRankings() {
    try {
        for (size_t i = 0; i < 3; ++i) {
            std::lock_guard<std::mutex> lock(m_rankingMutexes[i]);
            m_rankings[i].clear();
            m_rankingPages[i].clear();
            m_versions[i]++;
        }
        
        std::cout << "[DEBUG] Cleared all rankings" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ClearAllRankings: " << e.what() << std::endl;
    }
}

bool CGuildRankingManager::CreateRankTable(const std::string& date) {
    try {
        std::lock_guard<std::mutex> lock(m_databaseMutex);
        
        std::ostringstream query;
        query << "select identity(int,1,1) as rank, g.serial, g.race, g.grade, g.id, "
              << "r.win, r.draw, r.lose, r.score into [dbo].[tbl_GuildBattleRank" << date << "] "
              << "from [dbo].[tbl_GuildBattleRank] as r "
              << "join [dbo].[tbl_Guild] as g on r.serial = g.Serial and g.dck = 0 "
              << "order by score desc, win desc, draw desc, lose";
        
        return CRFWorldDatabase::ExecUpdateQuery(m_database.get(), query.str().c_str(), 1);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CreateRankTable: " << e.what() << std::endl;
        return false;
    }
}

uint32_t CGuildRankingManager::GetVersion(GuildRace race) const {
    if (!IsValidRace(race)) {
        return 0;
    }
    
    size_t raceIndex = GetRaceIndex(race);
    return m_versions[raceIndex].load();
}

uint8_t CGuildRankingManager::GetTotalPages(GuildRace race) const {
    if (!IsValidRace(race)) {
        return 0;
    }
    
    size_t raceIndex = GetRaceIndex(race);
    std::lock_guard<std::mutex> lock(m_rankingMutexes[raceIndex]);
    
    return static_cast<uint8_t>(m_rankingPages[raceIndex].size());
}

void CGuildRankingManager::SortRankings(GuildRace race) {
    // Rankings are stored in unordered_map, sorting happens during page creation
    // This method is called to trigger page updates
}

void CGuildRankingManager::UpdateRankingPages(GuildRace race) {
    try {
        size_t raceIndex = GetRaceIndex(race);
        auto& rankings = m_rankings[raceIndex];
        auto& pages = m_rankingPages[raceIndex];
        
        // Clear existing pages
        pages.clear();
        
        // Create sorted vector of rankings
        std::vector<std::pair<uint32_t, GuildRankingEntry>> sortedRankings;
        sortedRankings.reserve(rankings.size());
        
        for (const auto& pair : rankings) {
            sortedRankings.emplace_back(pair.first, pair.second);
        }
        
        // Sort by score (descending), then by wins (descending)
        std::sort(sortedRankings.begin(), sortedRankings.end(),
                 [](const auto& a, const auto& b) {
                     if (a.second.score != b.second.score) {
                         return a.second.score > b.second.score;
                     }
                     if (a.second.wins != b.second.wins) {
                         return a.second.wins > b.second.wins;
                     }
                     return a.second.draws > b.second.draws;
                 });
        
        // Create pages
        size_t totalEntries = sortedRankings.size();
        size_t totalPages = (totalEntries + GuildRankingPage::ENTRIES_PER_PAGE - 1) / GuildRankingPage::ENTRIES_PER_PAGE;
        
        for (size_t pageNum = 0; pageNum < totalPages; ++pageNum) {
            GuildRankingPage page;
            page.pageNumber = static_cast<uint8_t>(pageNum);
            page.totalPages = static_cast<uint8_t>(totalPages);
            page.version = m_versions[raceIndex].load();
            
            size_t startIndex = pageNum * GuildRankingPage::ENTRIES_PER_PAGE;
            size_t endIndex = std::min(startIndex + GuildRankingPage::ENTRIES_PER_PAGE, totalEntries);
            
            for (size_t i = startIndex; i < endIndex; ++i) {
                GuildRankingEntry entry = sortedRankings[i].second;
                entry.rank = static_cast<uint32_t>(i + 1);
                page.entries.push_back(entry);
            }
            
            pages.push_back(page);
        }
        
        m_stats.totalRankingUpdates++;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UpdateRankingPages: " << e.what() << std::endl;
    }
}

bool CGuildRankingManager::LoadRankingsFromDatabase(GuildRace race) {
    try {
        // TODO: Implement actual database loading
        // This would use CRFWorldDatabase::SelectGuildBattleRankList
        
        std::cout << "[DEBUG] Loading rankings from database for race " << static_cast<int>(race) << std::endl;
        
        // For now, return true to indicate successful loading
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in LoadRankingsFromDatabase: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildRankingManager::SaveRankingsToDatabase(GuildRace race) {
    try {
        // TODO: Implement actual database saving
        // This would use CRFWorldDatabase::UpdateGuildBattleRank
        
        std::cout << "[DEBUG] Saving rankings to database for race " << static_cast<int>(race) << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in SaveRankingsToDatabase: " << e.what() << std::endl;
        return false;
    }
}

uint32_t CGuildRankingManager::CalculateScore(uint32_t wins, uint32_t draws, uint32_t losses) const {
    // Standard scoring: 3 points for win, 1 point for draw, 0 points for loss
    return (wins * 3) + (draws * 1);
}

bool CGuildRankingManager::IsValidRace(GuildRace race) const {
    return race >= GuildRace::Bellato && race <= GuildRace::Accretia;
}

size_t CGuildRankingManager::GetRaceIndex(GuildRace race) const {
    return static_cast<size_t>(race);
}

void CGuildRankingManager::UpdateStatistics(GuildRace race) {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    
    size_t raceIndex = GetRaceIndex(race);
    m_stats.guildsByRace[raceIndex] = static_cast<uint32_t>(m_rankings[raceIndex].size());
    
    // Update total active guilds
    uint32_t totalActive = 0;
    for (size_t i = 0; i < 3; ++i) {
        totalActive += m_stats.guildsByRace[i].load();
    }
    m_stats.activeGuilds = totalActive;
}

// Singleton implementation
CGuildRankingManager& CGuildRankingManager::Instance() {
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    if (!s_instance) {
        s_instance = std::make_unique<CGuildRankingManager>();
    }
    
    return *s_instance;
}

void CGuildRankingManager::SetInstance(std::unique_ptr<CGuildRankingManager> instance) {
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    s_instance = std::move(instance);
}

// Factory implementation
std::unique_ptr<CGuildRankingManager> CGuildRankingManagerFactory::CreateRankingManager() {
    return std::make_unique<CGuildRankingManager>();
}

std::unique_ptr<CGuildRankingManager> CGuildRankingManagerFactory::CreateRankingManager(
    std::shared_ptr<CRFWorldDatabase> database) {
    
    auto manager = std::make_unique<CGuildRankingManager>();
    // TODO: Set database connection
    return manager;
}

// Utility functions
namespace GuildRankingUtils {
    std::string GuildRaceToString(GuildRace race) {
        switch (race) {
            case GuildRace::Bellato: return "Bellato";
            case GuildRace::Cora: return "Cora";
            case GuildRace::Accretia: return "Accretia";
            case GuildRace::All: return "All";
            default: return "Unknown";
        }
    }
    
    std::string RankingSortCriteriaToString(RankingSortCriteria criteria) {
        switch (criteria) {
            case RankingSortCriteria::Score: return "Score";
            case RankingSortCriteria::Wins: return "Wins";
            case RankingSortCriteria::WinRate: return "WinRate";
            case RankingSortCriteria::Draws: return "Draws";
            case RankingSortCriteria::Losses: return "Losses";
            case RankingSortCriteria::TotalBattles: return "TotalBattles";
            default: return "Unknown";
        }
    }
    
    uint8_t GuildRaceToIndex(GuildRace race) {
        return static_cast<uint8_t>(race);
    }
    
    GuildRace IndexToGuildRace(uint8_t index) {
        if (index <= 2) {
            return static_cast<GuildRace>(index);
        }
        return GuildRace::All;
    }
    
    bool IsValidGuildSerial(uint32_t guildSerial) {
        return guildSerial > 0;
    }
    
    std::string FormatRankingEntry(const GuildRankingEntry& entry) {
        std::ostringstream oss;
        oss << "Rank " << entry.rank << ": " << entry.guildName 
            << " (Score: " << entry.score << ", W:" << entry.wins 
            << " D:" << entry.draws << " L:" << entry.losses 
            << " Rate:" << std::fixed << std::setprecision(1) << entry.winRate << "%)";
        return oss.str();
    }
}

} // namespace Guild
} // namespace NexusProtection
