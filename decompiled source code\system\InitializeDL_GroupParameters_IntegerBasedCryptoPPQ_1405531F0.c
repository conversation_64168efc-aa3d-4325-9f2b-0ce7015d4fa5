/*
 * Function: ?Initialize@DL_GroupParameters_IntegerBased@CryptoPP@@QEAAXAEBVInteger@2@0@Z
 * Address: 0x1405531F0
 */

void __fastcall CryptoPP::DL_GroupParameters_IntegerBased::Initialize(CryptoPP::DL_GroupParameters_IntegerBased *this, const struct CryptoPP::Integer *a2, const struct CryptoPP::Integer *a3)
{
  struct CryptoPP::Integer *v3; // rax@1
  CryptoPP::Integer v4; // [sp+20h] [bp-88h]@1
  CryptoPP::Integer v5; // [sp+48h] [bp-60h]@1
  __int64 v6; // [sp+70h] [bp-38h]@1
  struct CryptoPP::Integer *v7; // [sp+78h] [bp-30h]@1
  struct CryptoPP::Integer *v8; // [sp+80h] [bp-28h]@1
  struct CryptoPP::Integer *v9; // [sp+88h] [bp-20h]@1
  struct CryptoPP::Integer *v10; // [sp+90h] [bp-18h]@1
  CryptoPP::DL_GroupParameters_IntegerBased *v11; // [sp+B0h] [bp+8h]@1
  struct CryptoPP::Integer *v12; // [sp+B8h] [bp+10h]@1

  v12 = (struct CryptoPP::Integer *)a2;
  v11 = this;
  v6 = -2i64;
  ((void (*)(void))this->vfptr[1].BERDecode)();
  v7 = CryptoPP::DL_GroupParameters_IntegerBased::ComputeGroupOrder(v11, &v4, v12);
  v8 = v7;
  LODWORD(v3) = CryptoPP::operator/(&v5, v7, 2i64);
  v9 = v3;
  v10 = v3;
  CryptoPP::DL_GroupParameters_IntegerBased::SetSubgroupOrder(v11, v3);
  CryptoPP::Integer::~Integer(&v5);
  CryptoPP::Integer::~Integer(&v4);
}
