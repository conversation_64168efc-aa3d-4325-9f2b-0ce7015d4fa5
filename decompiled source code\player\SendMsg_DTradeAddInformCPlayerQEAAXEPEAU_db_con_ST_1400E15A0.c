/*
 * Function: ?SendMsg_DTradeAddInform@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@E@Z
 * Address: 0x1400E15A0
 */

void __fastcall CPlayer::SendMsg_DTradeAddInform(CPlayer *this, char bySlotIndex, _STORAGE_LIST::_db_con *pItem, char byAmount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v8; // [sp+39h] [bp-4Fh]@4
  unsigned __int16 v9; // [sp+3Ah] [bp-4Eh]@4
  unsigned __int64 v10; // [sp+3Ch] [bp-4Ch]@4
  unsigned int v11; // [sp+44h] [bp-44h]@4
  char v12; // [sp+48h] [bp-40h]@4
  char v13; // [sp+49h] [bp-3Fh]@4
  char v14; // [sp+4Ah] [bp-3Eh]@4
  unsigned int v15; // [sp+4Bh] [bp-3Dh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v17; // [sp+65h] [bp-23h]@4
  CPlayer *v18; // [sp+90h] [bp+8h]@1

  v18 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = bySlotIndex;
  v8 = pItem->m_byTableCode;
  v9 = pItem->m_wItemIndex;
  v10 = pItem->m_dwDur;
  v11 = pItem->m_dwLv;
  v12 = byAmount;
  v13 = v18->m_pmTrd.byEmptyInvenNum;
  v14 = pItem->m_byCsMethod;
  v15 = pItem->m_dwT;
  pbyType = 18;
  v17 = 15;
  CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x17u);
}
