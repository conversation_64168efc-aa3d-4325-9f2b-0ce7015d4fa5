#include "../Headers/Authentication.h"
#include <iostream>
#include <fstream>
#include <filesystem>

namespace NexusProtection::Authentication {

    // Module state
    static bool s_moduleInitialized = false;
    static std::chrono::steady_clock::time_point s_moduleStartTime;
    static AuthenticationConfig s_config;

    bool InitializeAuthenticationModule() {
        if (s_moduleInitialized) {
            return true; // Already initialized
        }

        try {
            // Record start time
            s_moduleStartTime = std::chrono::steady_clock::now();

            // Set default configuration
            s_config.SetDefaults();

            // Load configuration
            LoadAuthenticationConfig();

            // Initialize async logger
            auto& asyncLogger = GetAsyncLogger();
            if (!asyncLogger.Initialize()) {
                std::cerr << "Failed to initialize async logger" << std::endl;
                return false;
            }

            // Register authentication logs
            asyncLogger.RegisterLog(LogType::Authentication, s_config.logPath, "authentication", true);
            asyncLogger.RegisterLog(LogType::Billing, s_config.logPath, "billing", true);
            asyncLogger.RegisterLog(LogType::Security, s_config.logPath, "security", true);
            asyncLogger.RegisterLog(LogType::Error, s_config.logPath, "error", true);

            // Initialize user database
            auto& userDB = GetUserDB();
            userDB.SetDatabasePath(s_config.databasePath);
            if (!userDB.Initialize()) {
                std::cerr << "Failed to initialize user database" << std::endl;
                return false;
            }

            // Initialize billing manager
            if (s_config.enableBilling) {
                auto& billingManager = GetBillingManager();
                if (!billingManager.Initialize()) {
                    std::cerr << "Failed to initialize billing manager" << std::endl;
                    return false;
                }
                g_pBillingManager = &billingManager;
            }

            // Initialize security system
            if (s_config.enableSecurity) {
                auto& hackShieldSystem = GetHackShieldExSystem();
                if (!hackShieldSystem.Initialize()) {
                    std::cerr << "Failed to initialize security system" << std::endl;
                    return false;
                }
                g_pHackShieldExSystem = &hackShieldSystem;
            }

            // Set global pointers for legacy compatibility
            g_pAsyncLogger = &asyncLogger;
            g_pUserDB = &userDB;

            s_moduleInitialized = true;
            
            asyncLogger.WriteSystemLog("Authentication Module initialized successfully");
            std::cout << "Authentication Module initialized successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Failed to initialize Authentication Module: " << e.what() << std::endl;
            return false;
        }
    }

    void ShutdownAuthenticationModule() {
        if (!s_moduleInitialized) {
            return; // Not initialized
        }

        try {
            auto& asyncLogger = GetAsyncLogger();
            asyncLogger.WriteSystemLog("Authentication Module shutting down");

            // Shutdown security system
            if (g_pHackShieldExSystem) {
                g_pHackShieldExSystem->Shutdown();
                g_pHackShieldExSystem = nullptr;
            }

            // Shutdown billing manager
            if (g_pBillingManager) {
                g_pBillingManager->Shutdown();
                g_pBillingManager = nullptr;
            }

            // Shutdown user database
            if (g_pUserDB) {
                g_pUserDB->Shutdown();
                g_pUserDB = nullptr;
            }

            // Shutdown async logger last
            asyncLogger.Shutdown();
            g_pAsyncLogger = nullptr;

            s_moduleInitialized = false;
            
            std::cout << "Authentication Module shut down successfully" << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Error during Authentication Module shutdown: " << e.what() << std::endl;
        }
    }

    void UpdateAuthenticationModule(std::chrono::milliseconds deltaTime) {
        if (!s_moduleInitialized) {
            return;
        }

        try {
            // Update billing manager
            if (g_pBillingManager) {
                // Billing manager doesn't need regular updates in this implementation
            }

            // Update security system
            if (g_pHackShieldExSystem) {
                // Security system monitoring is handled internally
            }

            // Process async logs
            if (g_pAsyncLogger) {
                // Async logger processes logs automatically in background threads
            }
        }
        catch (const std::exception& e) {
            if (g_pAsyncLogger) {
                g_pAsyncLogger->WriteSystemLog("Error during Authentication Module update: " + std::string(e.what()));
            }
        }
    }

    std::pair<AuthenticationResult, uint32_t> AuthenticateUser(
        const std::string& accountName, 
        const std::string& password,
        const std::string& clientIP,
        uint16_t clientPort) {
        
        if (!s_moduleInitialized || !g_pUserDB || !g_pBillingManager) {
            return {AuthenticationResult::SystemError, 0};
        }

        try {
            // Authenticate with user database
            AuthenticationResult result = g_pUserDB->AuthenticateUser(accountName, password);
            
            if (result != AuthenticationResult::Success) {
                if (g_pAsyncLogger) {
                    g_pAsyncLogger->WriteAuthLog("Authentication failed for user: " + accountName + 
                                               " from IP: " + clientIP);
                }
                return {result, 0};
            }

            // Get account information
            AccountInfo account = g_pUserDB->GetAccount(accountName);
            if (!account.IsValid()) {
                return {AuthenticationResult::SystemError, 0};
            }

            // Create session
            uint32_t sessionId = g_pBillingManager->CreateSession(account, clientIP, clientPort);
            if (sessionId == 0) {
                return {AuthenticationResult::SystemError, 0};
            }

            // Record login
            g_pUserDB->RecordLogin(accountName, clientIP);

            // Initialize security for session
            if (g_pHackShieldExSystem && s_config.enableSecurity) {
                ClientSecurityInfo clientInfo;
                clientInfo.sessionId = sessionId;
                clientInfo.securityLevel = s_config.defaultSecurityLevel;
                g_pHackShieldExSystem->RegisterClient(sessionId, clientInfo);
            }

            if (g_pAsyncLogger) {
                g_pAsyncLogger->WriteAuthLog("User authenticated successfully: " + accountName + 
                                           " from IP: " + clientIP + " (Session: " + std::to_string(sessionId) + ")");
            }

            return {AuthenticationResult::Success, sessionId};
        }
        catch (const std::exception& e) {
            if (g_pAsyncLogger) {
                g_pAsyncLogger->WriteAuthLog("Authentication exception for user: " + accountName + 
                                           " - " + std::string(e.what()));
            }
            return {AuthenticationResult::SystemError, 0};
        }
    }

    uint32_t CreateUserSession(const AccountInfo& accountInfo, 
                              const std::string& clientIP, 
                              uint16_t clientPort) {
        if (!s_moduleInitialized || !g_pBillingManager) {
            return 0;
        }

        return g_pBillingManager->CreateSession(accountInfo, clientIP, clientPort);
    }

    bool DestroyUserSession(uint32_t sessionId) {
        if (!s_moduleInitialized) {
            return false;
        }

        bool result = true;

        // Destroy billing session
        if (g_pBillingManager) {
            result &= g_pBillingManager->DestroySession(sessionId);
        }

        // Unregister from security system
        if (g_pHackShieldExSystem) {
            result &= g_pHackShieldExSystem->UnregisterClient(sessionId);
        }

        if (g_pAsyncLogger) {
            g_pAsyncLogger->WriteAuthLog("Session destroyed: " + std::to_string(sessionId));
        }

        return result;
    }

    bool ValidateUserSession(uint32_t sessionId) {
        if (!s_moduleInitialized || !g_pBillingManager) {
            return false;
        }

        return g_pBillingManager->ValidateSession(sessionId);
    }

    const char* GetAuthenticationModuleVersion() {
        return AUTHENTICATION_MODULE_VERSION;
    }

    const char* GetAuthenticationModuleBuildInfo() {
        return "NexusProtection Authentication Module v" AUTHENTICATION_MODULE_VERSION " - Built on " AUTHENTICATION_MODULE_BUILD_DATE;
    }

    const AuthenticationConfig& GetAuthenticationConfig() {
        return s_config;
    }

    bool SetAuthenticationConfig(const AuthenticationConfig& config) {
        s_config = config;
        return true;
    }

    bool LoadAuthenticationConfig(const std::string& configPath) {
        std::string path = configPath.empty() ? (s_config.configPath + "Authentication.ini") : configPath;
        
        try {
            // For now, we'll use default configuration
            // In a full implementation, this would read from an INI file
            s_config.SetDefaults();
            return true;
        }
        catch (const std::exception& e) {
            if (g_pAsyncLogger) {
                g_pAsyncLogger->WriteSystemLog("Failed to load authentication config: " + std::string(e.what()));
            }
            return false;
        }
    }

    bool SaveAuthenticationConfig(const std::string& configPath) {
        std::string path = configPath.empty() ? (s_config.configPath + "Authentication.ini") : configPath;
        
        try {
            // Create config directory
            std::filesystem::create_directories(std::filesystem::path(path).parent_path());
            
            // For now, we'll just return true
            // In a full implementation, this would write to an INI file
            return true;
        }
        catch (const std::exception& e) {
            if (g_pAsyncLogger) {
                g_pAsyncLogger->WriteSystemLog("Failed to save authentication config: " + std::string(e.what()));
            }
            return false;
        }
    }

} // namespace NexusProtection::Authentication

// Legacy C interface implementation
extern "C" {
    int InitializeAuthentication() {
        return NexusProtection::Authentication::InitializeAuthenticationModule() ? 1 : 0;
    }

    void ShutdownAuthentication() {
        NexusProtection::Authentication::ShutdownAuthenticationModule();
    }

    void UpdateAuthentication(unsigned int deltaTimeMs) {
        std::chrono::milliseconds deltaTime(deltaTimeMs);
        NexusProtection::Authentication::UpdateAuthenticationModule(deltaTime);
    }

    int AuthenticateUser_C(const char* accountName, const char* password, unsigned int* sessionId) {
        if (!accountName || !password || !sessionId) {
            return static_cast<int>(NexusProtection::Authentication::AuthenticationResult::SystemError);
        }

        auto [result, session] = NexusProtection::Authentication::AuthenticateUser(
            accountName, password, "", 0);

        *sessionId = session;
        return static_cast<int>(result);
    }

    int ValidateSession_C(unsigned int sessionId) {
        return NexusProtection::Authentication::ValidateUserSession(sessionId) ? 1 : 0;
    }

    int DestroySession_C(unsigned int sessionId) {
        return NexusProtection::Authentication::DestroyUserSession(sessionId) ? 1 : 0;
    }

    const char* GetAuthenticationVersion() {
        return NexusProtection::Authentication::GetAuthenticationModuleVersion();
    }
}

// Global legacy compatibility
NexusProtection::Authentication::CBillingManager* g_pBillingManager = nullptr;
NexusProtection::Authentication::CAsyncLogger* g_pAsyncLogger = nullptr;
NexusProtection::Authentication::CHackShieldExSystem* g_pHackShieldExSystem = nullptr;
NexusProtection::Authentication::CUserDB* g_pUserDB = nullptr;
