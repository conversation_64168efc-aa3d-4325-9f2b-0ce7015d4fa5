/*
 * Function: ?CheckSPF_MON_MOTIVE_MY_HP_DOWN@DfAIMgr@@SAHPEAVCMonsterSkill@@HPEAVCMonsterAI@@PEAVCMonster@@PEAPEAVCCharacter@@@Z
 * Address: 0x1401527A0
 */

signed __int64 __usercall DfAIMgr::CheckSPF_MON_MOTIVE_MY_HP_DOWN@<rax>(CMonsterSkill *pSkill@<rcx>, int nMotiveValue@<edx>, CMonsterAI *pAI@<r8>, CMonster *pMon@<r9>, float a5@<xmm0>, CCharacter **ppTar)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  unsigned int v9; // eax@9
  signed int v10; // eax@11
  int v11; // eax@12
  __int64 v12; // [sp+0h] [bp-48h]@1
  float v13; // [sp+20h] [bp-28h]@11
  CCharacter *v14; // [sp+28h] [bp-20h]@12
  float v15; // [sp+30h] [bp-18h]@9
  float v16; // [sp+34h] [bp-14h]@9
  float v17; // [sp+38h] [bp-10h]@11
  CMonsterSkill *pSkilla; // [sp+50h] [bp+8h]@1
  int v19; // [sp+58h] [bp+10h]@1
  CMonsterAI *pAIa; // [sp+60h] [bp+18h]@1
  CMonster *pMona; // [sp+68h] [bp+20h]@1

  pMona = pMon;
  pAIa = pAI;
  v19 = nMotiveValue;
  pSkilla = pSkill;
  v6 = &v12;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !pMon || !pAI || !pSkilla || !ppTar )
    return 0i64;
  CMonster::GetSkillDelayTime(pMon, pSkilla);
  v15 = a5;
  v16 = (float)(signed int)GetLoopTime();
  v9 = CMonsterSkill::GetBeforeTime(pSkilla);
  if ( v15 > (float)(v16 - (float)(signed int)v9) )
    return 0i64;
  v17 = (float)((int (__fastcall *)(CMonster *))pMona->vfptr->GetHP)(pMona);
  v10 = ((int (__fastcall *)(CMonster *))pMona->vfptr->GetMaxHP)(pMona);
  v13 = (float)(v17 / (float)v10) * 100.0;
  if ( (float)v19 <= v13 )
    goto LABEL_19;
  v11 = CMonsterSkill::GetDstCaseType(pSkilla);
  v14 = DfAIMgr::GetWisdomTarget(v11, pAIa, pMona);
  if ( !v14 )
    return 0i64;
  if ( ppTar )
  {
    *ppTar = v14;
    result = 1i64;
  }
  else
  {
LABEL_19:
    result = 0i64;
  }
  return result;
}
