/*
 * Function: ?set@_pos@_dh_player_mgr@@QEAAXPEAVCMapData@@GPEAM@Z
 * Address: 0x14026F390
 */

void __fastcall _dh_player_mgr::_pos::set(_dh_player_mgr::_pos *this, CMapData *map, unsigned __int16 layer, float *pos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  _dh_player_mgr::_pos *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7->pMap = map;
  v7->wLayer = layer;
  memcpy_0(v7->fPos, pos, 0xCui64);
}
