/*
 * Function: ?GetCheckRecvTime@CNetWorking@@QEAAKKK@Z
 * Address: 0x140481D60
 */

signed __int64 __fastcall CNetWorking::GetCheckRecvTime(CNetWorking *this, unsigned int dwProID, unsigned int dwSocketIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _socket *v7; // [sp+20h] [bp-18h]@4
  CNetWorking *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CNetWorking::GetSocket(v8, dwProID, dwSocketIndex);
  if ( v7 )
    result = v7->m_dwLastRecvTime;
  else
    result = 0xFFFFFFFFi64;
  return result;
}
