/*
 * Function: ??1?$ptr2user@VCMonster@@@lua_tinker@@UEAA@XZ
 * Address: 0x14040BAC0
 */

void __fastcall lua_tinker::ptr2user<CMonster>::~ptr2user<CMonster>(lua_tinker::ptr2user<CMonster> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  lua_tinker::ptr2user<CMonster> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  lua_tinker::user::~user((lua_tinker::user *)&v4->vfptr);
}
