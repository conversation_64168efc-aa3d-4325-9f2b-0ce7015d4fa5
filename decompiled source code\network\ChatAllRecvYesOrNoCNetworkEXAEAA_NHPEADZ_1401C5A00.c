/*
 * Function: ?ChatAllRecvYesOrNo@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C5A00
 */

char __fastcall CNetworkEX::ChatAllRecvYesOrNo(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+8h] [bp-20h]@4
  int v8; // [sp+10h] [bp-18h]@6

  v3 = (__int64 *)&v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = pBuf;
  v7 = &g_Player + n;
  if ( v7->m_bLoad )
  {
    v8 = *v6 == 0;
    v7->m_bRecvAllChat = v8;
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
