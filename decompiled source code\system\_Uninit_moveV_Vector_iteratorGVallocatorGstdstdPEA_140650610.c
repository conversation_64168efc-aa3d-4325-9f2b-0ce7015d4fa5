/*
 * Function: ??$_Uninit_move@V?$_Vector_iterator@GV?$allocator@G@std@@@std@@PEAGV?$allocator@G@2@U_Undefined_move_tag@2@@std@@YAPEAGV?$_Vector_iterator@GV?$allocator@G@std@@@0@0PEAGAEAV?$allocator@G@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140650610
 */

__int64 __fastcall std::_Uninit_move<std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>,unsigned short *,std::allocator<unsigned short>,std::_Undefined_move_tag>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // ST20_8@1
  char v9; // [sp+28h] [bp-70h]@1
  char *v10; // [sp+40h] [bp-58h]@1
  char v11; // [sp+48h] [bp-50h]@1
  char *v12; // [sp+60h] [bp-38h]@1
  __int64 v13; // [sp+68h] [bp-30h]@1
  __int64 v14; // [sp+70h] [bp-28h]@1
  __int64 v15; // [sp+78h] [bp-20h]@1
  __int64 v16; // [sp+80h] [bp-18h]@1
  __int64 v17; // [sp+B0h] [bp+18h]@1
  __int64 v18; // [sp+B8h] [bp+20h]@1

  v18 = a4;
  v17 = a3;
  v13 = -2i64;
  v10 = &v9;
  v12 = &v11;
  v4 = std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::_Vector_iterator<unsigned short,std::allocator<unsigned short>>((__int64)&v9);
  v14 = v4;
  v15 = v4;
  v5 = std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::_Vector_iterator<unsigned short,std::allocator<unsigned short>>((__int64)v12);
  v16 = v5;
  LODWORD(v6) = stdext::unchecked_uninitialized_copy<std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>,unsigned short *,std::allocator<unsigned short>>(
                  v5,
                  v15,
                  v17,
                  v18);
  v7 = v6;
  std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
  std::_Vector_iterator<unsigned short,std::allocator<unsigned short>>::~_Vector_iterator<unsigned short,std::allocator<unsigned short>>();
  return v7;
}
