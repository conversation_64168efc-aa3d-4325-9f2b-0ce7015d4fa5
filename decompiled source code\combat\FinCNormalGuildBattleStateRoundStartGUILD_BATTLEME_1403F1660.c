/*
 * Function: ?Fin@CNormalGuildBattleStateRoundStart@GUILD_BATTLE@@MEAAHPEAVCNormalGuildBattle@2@@Z
 * Address: 0x1403F1660
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundStart::Fin(GUILD_BATTLE::CNormalGuildBattleStateRoundStart *this, GUILD_BATTLE::CNormalGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v4; // rax@5
  signed __int64 result; // rax@5
  GUILD_BATTLE::CNormalGuildBattleLogger *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v8; // [sp+20h] [bp-28h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v9; // [sp+28h] [bp-20h]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+30h] [bp-18h]@6
  int iPortalInx; // [sp+38h] [bp-10h]@6
  GUILD_BATTLE::CNormalGuildBattleStateRoundStart *v12; // [sp+50h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattle *pkBattlea; // [sp+58h] [bp+10h]@1

  pkBattlea = pkBattle;
  v12 = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = GUILD_BATTLE::CNormalGuildBattle::GetField(pkBattle);
  if ( v8 )
  {
    v9 = GUILD_BATTLE::CNormalGuildBattle::GetRed(pkBattlea);
    v10 = GUILD_BATTLE::CNormalGuildBattle::GetBlue(pkBattlea);
    iPortalInx = GUILD_BATTLE::CNormalGuildBattleField::RegenBall(v8);
    if ( iPortalInx >= 0 )
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::SendRegenBall(v9, iPortalInx);
      GUILD_BATTLE::CNormalGuildBattleGuild::SendRegenBall(v10, iPortalInx);
      GUILD_BATTLE::CNormalGuildBattleStateRound::Log(
        (GUILD_BATTLE::CNormalGuildBattleStateRound *)&v12->vfptr,
        pkBattlea,
        "Fin : Pos(%d) Regen Ball",
        (unsigned int)iPortalInx);
      result = 2i64;
    }
    else
    {
      v6 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(pkBattlea);
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        v6,
        "CNormalGuildBattleStateRoundStart::Enter() : %d = = pkField->RegenBall() Fail!",
        (unsigned int)iPortalInx);
      result = 1i64;
    }
  }
  else
  {
    v4 = GUILD_BATTLE::CNormalGuildBattle::GetLogger(pkBattlea);
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleStateRoundStart::Enter() : pkField = pkBattle->GetField() NULL!");
    result = 1i64;
  }
  return result;
}
