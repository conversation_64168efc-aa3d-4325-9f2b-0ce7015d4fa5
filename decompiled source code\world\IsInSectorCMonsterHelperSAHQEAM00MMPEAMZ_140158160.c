/*
 * Function: ?IsInSector@CMonsterHelper@@SAHQEAM00MMPEAM@Z
 * Address: 0x140158160
 */

__int64 __fastcall CMonsterHelper::IsInSector(float *chkpos, float *src, float *dest, float angle, float radius, float *pfDist)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@10
  float v9; // xmm0_4@11
  float v10; // xmm0_4@11
  __int64 v11; // [sp+0h] [bp-C8h]@1
  float v12; // [sp+20h] [bp-A8h]@4
  float Dst; // [sp+38h] [bp-90h]@4
  float v14; // [sp+3Ch] [bp-8Ch]@11
  float v15; // [sp+40h] [bp-88h]@6
  float v; // [sp+68h] [bp-60h]@7
  float v17; // [sp+6Ch] [bp-5Ch]@7
  float v18; // [sp+70h] [bp-58h]@7
  float v19; // [sp+98h] [bp-30h]@11
  float v20; // [sp+9Ch] [bp-2Ch]@11
  float v21; // [sp+A0h] [bp-28h]@11
  float v22; // [sp+B4h] [bp-14h]@7
  float *v23; // [sp+D0h] [bp+8h]@1
  float *v24; // [sp+D8h] [bp+10h]@1
  float *Src; // [sp+E0h] [bp+18h]@1

  Src = dest;
  v24 = src;
  v23 = chkpos;
  v6 = &v11;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v12 = src[1] - dest[1];
  memcpy_0(&Dst, dest, 0xCui64);
  if ( *v24 == *Src && v24[2] == Src[2] )
    v15 = v24[2] + 1.0;
  v = *v23 - *v24;
  v17 = v23[1] - v24[1];
  v18 = v23[2] - v24[2];
  v22 = sqrtf((float)((float)(v * v) + (float)(v17 * v17)) + (float)(v18 * v18));
  if ( pfDist )
    *pfDist = v22;
  if ( v22 <= radius )
  {
    v19 = Dst - *v24;
    v20 = v14 - v24[1];
    v21 = v15 - v24[2];
    Normalize(&v);
    Normalize(&v19);
    v22 = (float)((float)(v * v19) + (float)(v17 * v20)) + (float)(v18 * v21);
    v9 = (float)((float)(v * v19) + (float)(v17 * v20)) + (float)(v18 * v21);
    acos(v22);
    v10 = v9 * 360.0 / 6.283184;
    result = (float)(angle / 2.0) > v10;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
