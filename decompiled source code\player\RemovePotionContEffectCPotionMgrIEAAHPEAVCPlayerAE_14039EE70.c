/*
 * Function: ?RemovePotionContEffect@CPotionMgr@@IEAAHPEAVCPlayer@@AEAV_ContPotionData@@@Z
 * Address: 0x14039EE70
 */

signed __int64 __fastcall CPotionMgr::RemovePotionContEffect(CPotionMgr *this, CPlayer *pApplyPlayer, _ContPotionData *ContPotionData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  unsigned __int16 v6; // ax@20
  __int64 v7; // [sp+0h] [bp-58h]@1
  _base_fld *v8; // [sp+20h] [bp-38h]@6
  char *v9; // [sp+28h] [bp-30h]@8
  int j; // [sp+30h] [bp-28h]@9
  char *v11; // [sp+38h] [bp-20h]@11
  int v12; // [sp+40h] [bp-18h]@12
  CPotionMgr *v13; // [sp+60h] [bp+8h]@1
  CPlayer *v14; // [sp+68h] [bp+10h]@1
  _ContPotionData *v15; // [sp+70h] [bp+18h]@1

  v15 = ContPotionData;
  v14 = pApplyPlayer;
  v13 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( _ContPotionData::IsLive(ContPotionData) )
  {
    v8 = CRecordData::GetRecord(&v13->m_tblPotionEffectData, v15->m_dwPotionEffectIndex);
    if ( v8 )
    {
      v9 = &v8[13].m_strCode[48];
      if ( v8 != (_base_fld *)-936 )
      {
        for ( j = 0; j < 5; ++j )
        {
          v11 = &v9[36 * j];
          if ( *(_DWORD *)v11 == -1 )
            break;
          v12 = *(_DWORD *)v11;
          if ( v12 )
          {
            if ( v12 == 1 )
            {
              _effect_parameter::SetEff_Plus(&v14->m_EP, *((_DWORD *)v11 + 1), *((float *)v11 + 2), 0);
            }
            else if ( v12 == 2 )
            {
              _effect_parameter::SetEff_State(&v14->m_EP, *((_DWORD *)v11 + 1), 0);
            }
          }
          else
          {
            _effect_parameter::SetEff_Rate(&v14->m_EP, *((_DWORD *)v11 + 1), *((float *)v11 + 2), 0);
          }
        }
      }
      v6 = _ContPotionData::GetEffectIndex(v15);
      CExtPotionBuf::SednMsg_RemovePotionContEffect(&v14->m_PotionBufUse, v6, v14->m_ObjID.m_wIndex);
      _ContPotionData::Init(v15);
      result = 0i64;
    }
    else
    {
      result = 0xFFFFFFFFi64;
    }
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
