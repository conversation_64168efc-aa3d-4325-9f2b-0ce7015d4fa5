/*
 * Function: ?IsApplyPcbangPrimium@CNationSettingManager@@QEAA_NQEBVCPlayer@@@Z
 * Address: 0x14007E070
 */

int __fastcall CNationSettingManager::IsApplyPcbangPrimium(CNationSettingManager *this, CPlayer *const pUser)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNationSettingManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return ((int (__fastcall *)(CNationSettingData *))v6->m_pData->vfptr->IsApplyPcbangPrimium)(v6->m_pData);
}
