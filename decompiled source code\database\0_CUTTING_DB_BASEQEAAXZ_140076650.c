/*
 * Function: ??0_CUTTING_DB_BASE@@QEAA@XZ
 * Address: 0x140076650
 */

void __fastcall _CUTTING_DB_BASE::_CUTTING_DB_BASE(_CUTTING_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _CUTTING_DB_BASE *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(v4->m_List, 8ui64, 20, (void *(__cdecl *)(void *))_CUTTING_DB_BASE::_LIST::_LIST);
  _CUTTING_DB_BASE::Init(v4);
}
