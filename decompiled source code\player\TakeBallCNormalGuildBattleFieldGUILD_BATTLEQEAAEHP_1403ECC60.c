/*
 * Function: ?TakeBall@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEHPEAVCPlayer@@@Z
 * Address: 0x1403ECC60
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::TakeBall(GUILD_BATTLE::CNormalGuildBattleField *this, int iPortalInx, CPlayer *pkPlayer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@10
  unsigned int j; // [sp+24h] [bp-14h]@10
  GUILD_BATTLE::CNormalGuildBattleField *v9; // [sp+40h] [bp+8h]@1
  int v10; // [sp+48h] [bp+10h]@1
  CPlayer *pkPlayera; // [sp+50h] [bp+18h]@1

  pkPlayera = pkPlayer;
  v10 = iPortalInx;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v9->m_bInit )
  {
    if ( CPlayer::IsRidingUnit(pkPlayer) )
    {
      result = -117;
    }
    else if ( CPlayer::IsRecallAnimus(pkPlayera) )
    {
      result = -85;
    }
    else
    {
      v7 = 0;
      for ( j = 0; ; ++j )
      {
        if ( j >= v9->m_uiRegenPosCnt )
          return -119;
        if ( CGravityStoneRegener::GetPortalInx(&v9->m_pkRegenPos[j]) == v10 )
          break;
      }
      v7 = CGravityStoneRegener::Take(&v9->m_pkRegenPos[j], v9->m_pkMap, pkPlayera->m_fCurPos);
      if ( v7 )
      {
        result = v7;
      }
      else
      {
        CGravityStone::SetOwner(v9->m_pkBall, pkPlayera);
        result = 0;
      }
    }
  }
  else
  {
    result = 110;
  }
  return result;
}
