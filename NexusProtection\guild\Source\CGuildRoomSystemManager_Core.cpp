/**
 * @file CGuildRoomSystemManager_Core.cpp
 * @brief Modern C++20 Guild Room System Manager core implementation
 * 
 * This file provides the core implementation of the CGuildRoomSystemManager class
 * with comprehensive guild room management, map loading, and room operations.
 */

#include "../Headers/CGuildRoomSystemManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>
#include <sstream>

// Legacy includes for compatibility
extern "C" {
    class CMapData {
    public:
        struct _portal* m_pPortal;
        struct _layer_set* m_ls;
    };
    
    class CMapOperation {
    public:
        CMapData* GetMap(const char* mapName);
    };
    
    class CGuildRoomInfo {
    public:
        void SetRoomMapInfo(CMapData* pMap, uint16_t wMapLayer, uint8_t byRoomType, uint8_t byRace);
        bool IsRent() const;
        uint32_t GetGuildSerial() const;
        CMapData* GetMapData() const;
        uint16_t GetMapLayer() const;
        uint8_t GetRoomType() const;
    };
    
    struct _portal {
        struct _portal_dummy* m_pDumPos;
    };
    
    struct _portal_dummy {
        struct _dummy_position* m_pDumPos;
    };
    
    extern CMapOperation g_MapOper;
    extern std::vector<CGuildRoomInfo> g_vecGuildRoom;
}

namespace NexusProtection::Guild {

// Helper function to format strings (replacement for std::format)
template<typename... Args>
std::string FormatString(const std::string& format, Args... args) {
    std::ostringstream oss;
    size_t pos = 0;
    size_t argIndex = 0;
    std::array<std::string, sizeof...(args)> argStrings = {std::to_string(args)...};

    while (pos < format.length()) {
        size_t found = format.find("{}", pos);
        if (found != std::string::npos) {
            oss << format.substr(pos, found - pos);
            if (argIndex < argStrings.size()) {
                oss << argStrings[argIndex++];
            }
            pos = found + 2;
        } else {
            oss << format.substr(pos);
            break;
        }
    }
    return oss.str();
}

CGuildRoomSystemManager& CGuildRoomSystemManager::GetInstance() {
    static CGuildRoomSystemManager instance;
    return instance;
}

GuildRoomResult CGuildRoomSystemManager::Initialize(const GuildRoomConfig& config) {
    try {
        if (m_isInitialized.load()) {
            return GuildRoomResult::Success;
        }
        
        m_state.store(GuildRoomSystemState::Initializing);
        
        // Validate configuration
        if (!config.IsValid()) {
            m_state.store(GuildRoomSystemState::Error);
            LogError("Initialize", "Invalid configuration provided");
            return GuildRoomResult::InitializationFailed;
        }
        
        // Store configuration
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }
        
        // Reset statistics
        ResetStatistics();
        
        // Load all required maps
        m_state.store(GuildRoomSystemState::LoadingMaps);
        if (!LoadAllMaps()) {
            m_state.store(GuildRoomSystemState::Error);
            LogError("Initialize", "Failed to load required maps");
            return GuildRoomResult::MapLoadingFailed;
        }
        
        // Configure room system
        m_state.store(GuildRoomSystemState::ConfiguringRooms);
        if (!ConfigureRoomSystem()) {
            m_state.store(GuildRoomSystemState::Error);
            LogError("Initialize", "Failed to configure room system");
            return GuildRoomResult::InitializationFailed;
        }
        
        m_isInitialized.store(true);
        m_isShutdown.store(false);
        m_state.store(GuildRoomSystemState::Running);
        
        std::cout << "[INFO] Guild Room System Manager initialized successfully" << std::endl;
        std::cout << "[INFO] Configuration: totalRooms=" << config.totalRooms 
                  << ", maxRoomsPerRace=" << config.maxRoomsPerRace << std::endl;
        
        return GuildRoomResult::Success;
        
    } catch (const std::exception& e) {
        m_state.store(GuildRoomSystemState::Error);
        LogError("Initialize", std::string("Exception: ") + e.what());
        return GuildRoomResult::InitializationFailed;
    }
}

GuildRoomResult CGuildRoomSystemManager::Shutdown() {
    try {
        if (!m_isInitialized.load() || m_isShutdown.load()) {
            return GuildRoomResult::Success;
        }
        
        m_state.store(GuildRoomSystemState::Shutdown);
        
        // Clear callbacks
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            m_roomInitializedCallback = nullptr;
            m_roomRentedCallback = nullptr;
            m_roomAccessCallback = nullptr;
            m_roomErrorCallback = nullptr;
            m_mapLoadedCallback = nullptr;
        }
        
        // Clear map data
        {
            std::lock_guard<std::mutex> lock(m_mapMutex);
            for (auto& raceArray : m_roomMaps) {
                for (auto& mapPtr : raceArray) {
                    mapPtr = nullptr;
                }
            }
            m_mapInfo.clear();
        }
        
        m_isShutdown.store(true);
        m_isInitialized.store(false);
        
        std::cout << "[INFO] Guild Room System Manager shutdown completed" << std::endl;
        
        return GuildRoomResult::Success;
        
    } catch (const std::exception& e) {
        LogError("Shutdown", std::string("Exception: ") + e.what());
        return GuildRoomResult::SystemError;
    }
}

bool CGuildRoomSystemManager::Init() {
    // Legacy compatibility wrapper
    auto result = Initialize();
    return result == GuildRoomResult::Success;
}

bool CGuildRoomSystemManager::LoadAllMaps() {
    try {
        std::lock_guard<std::mutex> lock(m_mapMutex);
        
        // Define required maps
        std::array<std::string, 6> roomMaps = {
            "BellaGSD",  // Bellato Standard
            "CoraGSD",   // Cora Standard
            "AccGSD",    // Accretia Standard
            "BellaGSP",  // Bellato Premium
            "CoraGSP",   // Cora Premium
            "AccGSP"     // Accretia Premium
        };
        
        std::array<std::string, 3> neutralMaps = {
            "NeutralB",  // Bellato Neutral
            "NeutralC",  // Cora Neutral
            "NeutralA"   // Accretia Neutral
        };
        
        // Load room maps
        for (size_t i = 0; i < roomMaps.size(); ++i) {
            CMapData* mapData = LoadMap(roomMaps[i]);
            if (!mapData) {
                LogError("LoadAllMaps", std::string("Failed to load room map: ") + roomMaps[i]);
                return false;
            }
            
            // Store in appropriate array position
            size_t race = i / 2;  // 0=Bellato, 1=Cora, 2=Accretia
            size_t type = i % 2;  // 0=Standard, 1=Premium
            m_roomMaps[race][type] = mapData;
            
            NotifyMapLoaded(roomMaps[i], true);
        }
        
        // Load neutral maps
        for (size_t i = 0; i < neutralMaps.size(); ++i) {
            CMapData* mapData = LoadMap(neutralMaps[i]);
            if (!mapData) {
                LogError("LoadAllMaps", std::string("Failed to load neutral map: ") + neutralMaps[i]);
                return false;
            }
            
            // Store neutral maps
            switch (i) {
                case 0: // Bellato
                    m_neutralMapBellato = mapData;
                    break;
                case 1: // Cora
                    m_neutralMapCora = mapData;
                    break;
                case 2: // Accretia
                    m_neutralMapAccretia = mapData;
                    break;
            }
            
            NotifyMapLoaded(neutralMaps[i], true);
        }
        
        // Load portal dummy positions
        if (!LoadPortalDummies()) {
            LogError("LoadAllMaps", "Failed to load portal dummy positions");
            return false;
        }
        
        return ValidateMapLoading();
        
    } catch (const std::exception& e) {
        LogError("LoadAllMaps", std::string("Exception: ") + e.what());
        return false;
    }
}

CMapData* CGuildRoomSystemManager::LoadMap(std::string_view mapName) {
    try {
        CMapData* mapData = g_MapOper.GetMap(mapName.data());
        if (!mapData) {
            m_stats.mapLoadingErrors.fetch_add(1);
            NotifyMapLoaded(std::string(mapName), false);
            return nullptr;
        }
        
        // Store map information
        MapInfo info(mapName);
        info.mapData = mapData;
        info.isLoaded = true;
        info.loadTime = std::chrono::steady_clock::now();
        
        m_mapInfo[std::string(mapName)] = std::move(info);
        
        std::cout << "[INFO] Successfully loaded map: " << mapName << std::endl;
        return mapData;
        
    } catch (const std::exception& e) {
        LogError("LoadMap", std::string("Failed to load map ") + std::string(mapName) + ": " + e.what());
        m_stats.mapLoadingErrors.fetch_add(1);
        NotifyMapLoaded(std::string(mapName), false);
        return nullptr;
    }
}

bool CGuildRoomSystemManager::LoadPortalDummies() {
    try {
        // Load Bellato neutral HQ dummy
        if (m_neutralMapBellato) {
            auto* portal = GetPortal(m_neutralMapBellato, "dpfrom_bellato_camp");
            if (portal && portal->m_pDumPos) {
                m_neutralHQDummyBellato = portal->m_pDumPos;
            } else {
                LogError("LoadPortalDummies", "Failed to load Bellato HQ dummy");
                return false;
            }
        }
        
        // Load Cora neutral HQ dummy
        if (m_neutralMapCora) {
            auto* portal = GetPortal(m_neutralMapCora, "dpfrom_cora_camp");
            if (portal && portal->m_pDumPos) {
                m_neutralHQDummyCora = portal->m_pDumPos;
            } else {
                LogError("LoadPortalDummies", "Failed to load Cora HQ dummy");
                return false;
            }
        }
        
        // Load Accretia neutral HQ dummy
        if (m_neutralMapAccretia) {
            auto* portal = GetPortal(m_neutralMapAccretia, "dpfrom_accretia_camp");
            if (portal && portal->m_pDumPos) {
                m_neutralHQDummyAccretia = portal->m_pDumPos;
            } else {
                LogError("LoadPortalDummies", "Failed to load Accretia HQ dummy");
                return false;
            }
        }
        
        return true;
        
    } catch (const std::exception& e) {
        LogError("LoadPortalDummies", std::string("Exception: ") + e.what());
        return false;
    }
}

bool CGuildRoomSystemManager::ConfigureRoomSystem() {
    try {
        if (!InitializeRoomConfigurations()) {
            LogError("ConfigureRoomSystem", "Failed to initialize room configurations");
            return false;
        }
        
        // Set room pricing and duration configuration
        SetRoomConfiguration();
        
        m_stats.totalRoomsInitialized.store(m_config.totalRooms);
        
        std::cout << "[INFO] Room system configured successfully" << std::endl;
        std::cout << "[INFO] Total rooms: " << m_config.totalRooms << std::endl;
        std::cout << "[INFO] Standard rooms per race: " << m_config.standardRoomsPerRace << std::endl;
        std::cout << "[INFO] Premium rooms per race: " << m_config.premiumRoomsPerRace << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        LogError("ConfigureRoomSystem", std::string("Exception: ") + e.what());
        return false;
    }
}

bool CGuildRoomSystemManager::InitializeRoomConfigurations() {
    try {
        // Initialize room configurations for each race
        for (uint32_t race = 0; race < static_cast<uint32_t>(GuildRace::MaxRaces); ++race) {
            uint32_t baseIndex = race * m_config.maxRoomsPerRace;

            // Configure standard rooms (0-19 for each race)
            for (uint32_t room = 0; room < m_config.standardRoomsPerRace; ++room) {
                uint32_t roomIndex = baseIndex + room;
                ConfigureRoom(roomIndex, static_cast<GuildRace>(race), GuildRoomType::Standard, room);
                NotifyRoomInitialized(roomIndex, static_cast<GuildRace>(race), GuildRoomType::Standard);
            }

            // Configure premium rooms (20-29 for each race)
            for (uint32_t room = 0; room < m_config.premiumRoomsPerRace; ++room) {
                uint32_t roomIndex = baseIndex + m_config.standardRoomsPerRace + room;
                ConfigureRoom(roomIndex, static_cast<GuildRace>(race), GuildRoomType::Premium, room);
                NotifyRoomInitialized(roomIndex, static_cast<GuildRace>(race), GuildRoomType::Premium);
            }
        }

        return true;

    } catch (const std::exception& e) {
        LogError("InitializeRoomConfigurations", std::string("Exception: ") + e.what());
        return false;
    }
}

void CGuildRoomSystemManager::ConfigureRoom(uint32_t roomIndex, GuildRace race, GuildRoomType roomType, uint32_t layerIndex) {
    try {
        // Get the appropriate map data
        CMapData* mapData = m_roomMaps[static_cast<size_t>(race)][static_cast<size_t>(roomType)];
        if (!mapData) {
            LogError("ConfigureRoom", std::string("No map data for race ") + std::to_string(static_cast<int>(race)) +
                     " room type " + std::to_string(static_cast<int>(roomType)));
            return;
        }

        // Configure the room using legacy interface
        // This would typically call CGuildRoomInfo::SetRoomMapInfo
        // SetRoomMapInfo(roomIndex, mapData, layerIndex, roomType, race);

        std::cout << "[DEBUG] Configured room " << roomIndex
                  << " for race " << static_cast<int>(race)
                  << " type " << static_cast<int>(roomType)
                  << " layer " << layerIndex << std::endl;

    } catch (const std::exception& e) {
        LogError("ConfigureRoom", std::string("Exception configuring room ") + std::to_string(roomIndex) + ": " + e.what());
    }
}

bool CGuildRoomSystemManager::ValidateMapLoading() const {
    try {
        // Check all room maps are loaded
        for (size_t race = 0; race < static_cast<size_t>(GuildRace::MaxRaces); ++race) {
            for (size_t type = 0; type < static_cast<size_t>(GuildRoomType::MaxTypes); ++type) {
                if (!m_roomMaps[race][type]) {
                    LogError("ValidateMapLoading", std::string("Missing map for race ") + std::to_string(race) + " type " + std::to_string(type));
                    return false;
                }
            }
        }

        // Check neutral maps are loaded
        if (!m_neutralMapBellato || !m_neutralMapCora || !m_neutralMapAccretia) {
            LogError("ValidateMapLoading", "Missing neutral maps");
            return false;
        }

        // Check neutral HQ dummies are loaded
        if (!m_neutralHQDummyBellato || !m_neutralHQDummyCora || !m_neutralHQDummyAccretia) {
            LogError("ValidateMapLoading", "Missing neutral HQ dummies");
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        LogError("ValidateMapLoading", std::string("Exception: ") + e.what());
        return false;
    }
}

void CGuildRoomSystemManager::SetRoomConfiguration() {
    try {
        // Set standard room configuration
        // These would typically be set in global variables or configuration system
        // LODWORD(CGuildRoomInfo::sm_RoomInfo) = m_config.standardRoomPrice;
        // dword_184A6F654 = m_config.standardRoomDuration;
        // byte_184A6F658 = static_cast<uint8_t>(GuildRoomType::Standard);
        // byte_184A6F659 = m_config.standardRoomMaxMembers;

        // Set premium room configuration
        // dword_184A6F65C = m_config.premiumRoomPrice;
        // dword_184A6F660 = m_config.premiumRoomDuration;
        // byte_184A6F664 = static_cast<uint8_t>(GuildRoomType::Premium);
        // byte_184A6F665 = m_config.premiumRoomMaxMembers;

        std::cout << "[INFO] Room configuration set:" << std::endl;
        std::cout << "[INFO]   Standard: " << m_config.standardRoomPrice << " credits, "
                  << m_config.standardRoomDuration << " seconds" << std::endl;
        std::cout << "[INFO]   Premium: " << m_config.premiumRoomPrice << " credits, "
                  << m_config.premiumRoomDuration << " seconds" << std::endl;

    } catch (const std::exception& e) {
        LogError("SetRoomConfiguration", std::string("Exception: ") + e.what());
    }
}

_portal_dummy* CGuildRoomSystemManager::GetPortal(CMapData* mapData, const char* portalName) {
    try {
        if (!mapData || !mapData->m_pPortal) {
            return nullptr;
        }

        // This would typically call CMapData::GetPortal(portalName)
        // For now, return a placeholder that would be the actual portal lookup
        // return mapData->GetPortal(portalName);

        // Placeholder implementation
        return mapData->m_pPortal;

    } catch (const std::exception& e) {
        LogError("GetPortal", std::string("Exception getting portal ") + portalName + ": " + e.what());
        return nullptr;
    }
}

GuildRoomStats CGuildRoomSystemManager::GetStatistics() const {
    return m_stats;
}

GuildRoomConfig CGuildRoomSystemManager::GetConfiguration() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_config;
}

bool CGuildRoomSystemManager::UpdateConfiguration(const GuildRoomConfig& config) {
    try {
        if (!config.IsValid()) {
            LogError("UpdateConfiguration", "Invalid configuration provided");
            return false;
        }

        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }

        std::cout << "[INFO] Guild room system configuration updated" << std::endl;
        return true;

    } catch (const std::exception& e) {
        LogError("UpdateConfiguration", std::string("Exception: ") + e.what());
        return false;
    }
}

int CGuildRoomSystemManager::RoomIn(uint32_t guildSerial, int roomIndex, uint32_t characterSerial) {
    try {
        if (!m_isInitialized.load()) {
            return -1; // System not initialized
        }

        // Validate parameters
        if (roomIndex < 0 || roomIndex >= static_cast<int>(m_config.totalRooms)) {
            LogError("RoomIn", std::string("Invalid room index: ") + std::to_string(roomIndex));
            return -2; // Invalid room index
        }

        // Create access info for tracking
        RoomAccessInfo accessInfo(guildSerial, characterSerial, roomIndex, true);

        // Process room entry logic
        // This would typically involve:
        // 1. Check if room is rented by the guild
        // 2. Check if character has permission
        // 3. Add character to room member list
        // 4. Update room state

        m_stats.totalRoomEntries.fetch_add(1);
        NotifyRoomAccess(accessInfo);

        std::cout << "[INFO] Character " << characterSerial
                  << " from guild " << guildSerial
                  << " entered room " << roomIndex << std::endl;

        return 0; // Success

    } catch (const std::exception& e) {
        LogError("RoomIn", std::string("Exception: ") + e.what());
        m_stats.roomOperationErrors.fetch_add(1);
        return -3; // System error
    }
}

int CGuildRoomSystemManager::RoomOut(uint32_t guildSerial, int roomIndex, uint32_t characterSerial) {
    try {
        if (!m_isInitialized.load()) {
            return -1; // System not initialized
        }

        // Validate parameters
        if (roomIndex < 0 || roomIndex >= static_cast<int>(m_config.totalRooms)) {
            LogError("RoomOut", std::string("Invalid room index: ") + std::to_string(roomIndex));
            return -2; // Invalid room index
        }

        // Create access info for tracking
        RoomAccessInfo accessInfo(guildSerial, characterSerial, roomIndex, false);

        // Process room exit logic
        // This would typically involve:
        // 1. Remove character from room member list
        // 2. Update room state
        // 3. Handle any cleanup if room becomes empty

        m_stats.totalRoomExits.fetch_add(1);
        NotifyRoomAccess(accessInfo);

        std::cout << "[INFO] Character " << characterSerial
                  << " from guild " << guildSerial
                  << " exited room " << roomIndex << std::endl;

        return 0; // Success

    } catch (const std::exception& e) {
        LogError("RoomOut", std::string("Exception: ") + e.what());
        m_stats.roomOperationErrors.fetch_add(1);
        return -3; // System error
    }
}

uint8_t CGuildRoomSystemManager::RentRoom(GuildRace race, GuildRoomType roomType, int guildIndex,
                                          uint32_t guildSerial, tagTIMESTAMP_STRUCT* timestamp, bool isRestore) {
    try {
        if (!m_isInitialized.load()) {
            return 1; // System not initialized
        }

        // Validate parameters
        if (race >= GuildRace::MaxRaces || roomType >= GuildRoomType::MaxTypes) {
            LogError("RentRoom", "Invalid race or room type");
            return 2; // Invalid parameters
        }

        // Find available room of the specified type for the race
        uint32_t baseIndex = static_cast<uint32_t>(race) * m_config.maxRoomsPerRace;
        uint32_t startIndex = (roomType == GuildRoomType::Premium) ?
                              baseIndex + m_config.standardRoomsPerRace : baseIndex;
        uint32_t endIndex = (roomType == GuildRoomType::Premium) ?
                            baseIndex + m_config.maxRoomsPerRace : baseIndex + m_config.standardRoomsPerRace;

        // Process room rental logic
        // This would typically involve:
        // 1. Find available room
        // 2. Check guild has sufficient funds
        // 3. Set room as rented
        // 4. Set rental duration
        // 5. Update database

        m_stats.totalRoomsRented.fetch_add(1);
        m_stats.activeRentals.fetch_add(1);

        int roomIndex = static_cast<int>(startIndex); // Simplified - would find actual available room
        NotifyRoomRented(guildSerial, roomIndex, roomType);

        std::cout << "[INFO] Guild " << guildSerial
                  << " rented " << (roomType == GuildRoomType::Premium ? "premium" : "standard")
                  << " room " << roomIndex
                  << " for race " << static_cast<int>(race) << std::endl;

        return 0; // Success

    } catch (const std::exception& e) {
        LogError("RentRoom", std::string("Exception: ") + e.what());
        m_stats.roomOperationErrors.fetch_add(1);
        return 3; // System error
    }
}

CMapData* CGuildRoomSystemManager::GetMapData(GuildRace race, GuildRoomType roomType) const {
    try {
        if (race >= GuildRace::MaxRaces || roomType >= GuildRoomType::MaxTypes) {
            return nullptr;
        }

        std::lock_guard<std::mutex> lock(m_mapMutex);
        return m_roomMaps[static_cast<size_t>(race)][static_cast<size_t>(roomType)];

    } catch (const std::exception& e) {
        LogError("GetMapData", std::string("Exception: ") + e.what());
        return nullptr;
    }
}

bool CGuildRoomSystemManager::GetMapPos(uint32_t guildSerial, float* position, CMapData** mapData,
                                        uint16_t* mapLayer, uint8_t* roomType) const {
    try {
        if (!m_isInitialized.load() || !position || !mapData || !mapLayer || !roomType) {
            return false;
        }

        // Find the room rented by this guild
        // This would typically iterate through all rooms to find the one rented by guildSerial
        // For now, return placeholder values

        *mapData = nullptr;
        *mapLayer = 0;
        *roomType = 0;

        // Set position to default values
        position[0] = 0.0f; // X
        position[1] = 0.0f; // Y
        position[2] = 0.0f; // Z

        return true;

    } catch (const std::exception& e) {
        LogError("GetMapPos", std::string("Exception: ") + e.what());
        return false;
    }
}

void CGuildRoomSystemManager::LogError(std::string_view context, std::string_view message) const {
    try {
        std::cerr << "[ERROR] CGuildRoomSystemManager::" << context << ": " << message << std::endl;

        // Track consecutive errors
        m_consecutiveErrors.fetch_add(1);
        m_lastErrorTime = std::chrono::steady_clock::now();

    } catch (const std::exception& e) {
        std::cerr << "[CRITICAL] Exception in LogError: " << e.what() << std::endl;
    }
}

} // namespace NexusProtection::Guild
