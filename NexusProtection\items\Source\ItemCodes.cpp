/*
 * ItemCodes.cpp - Complete Item Code System Implementation
 * Based on decompiled GetItemTableCode function (Address: 0x1400362B0)
 * Provides modern C++ interface for item code mapping and validation
 */

#include "../Headers/ItemCodes.h"
#include <algorithm>
#include <cctype>

namespace NexusProtection {
namespace Items {

// Static mapping from prefix to table code (based on GetItemTableCode function)
const std::unordered_map<std::string, ItemTableCode> ItemCodeMapper::s_prefixToTableCode = {
    // Equipment Items (0-7)
    {"iu", ItemTableCode::UpperArmor},      // 0
    {"il", ItemTableCode::LowerArmor},      // 1
    {"ig", ItemTableCode::Gloves},          // 2
    {"is", ItemTableCode::Shoes},           // 3
    {"ih", ItemTableCode::Helmet},          // 4
    {"id", ItemTableCode::Shield},          // 5
    {"iw", ItemTableCode::Weapon},          // 6
    {"ik", ItemTableCode::Cloak},           // 7
    
    // Non-Equipment Items (8+)
    {"ii", ItemTableCode::Ring},            // 8
    {"ia", ItemTableCode::Amulet},          // 9
    {"ib", ItemTableCode::Bullet},          // 10
    {"im", ItemTableCode::Material},        // 11
    {"ie", ItemTableCode::Elixir},          // 12
    {"ip", ItemTableCode::Potion},          // 13
    {"if", ItemTableCode::Food},            // 14
    {"ic", ItemTableCode::Charm},           // 15
    {"it", ItemTableCode::Ticket},          // 16
    {"io", ItemTableCode::Ore},             // 17
    {"ir", ItemTableCode::Rare},            // 18
    {"in", ItemTableCode::Natural},         // 19 (FORBIDDEN)
    {"iy", ItemTableCode::Yggdrasil},       // 20
    {"iz", ItemTableCode::Zone},            // 21
    {"iq", ItemTableCode::Quest},           // 22
    {"ix", ItemTableCode::Experience},      // 23
    {"ij", ItemTableCode::Jewel},           // 24
    {"gt", ItemTableCode::Gate},            // 25
    {"tr", ItemTableCode::Treasure},        // 26
    {"sk", ItemTableCode::Skill},           // 27
    {"ti", ItemTableCode::Time},            // 28
    {"ev", ItemTableCode::Event},           // 29
    {"re", ItemTableCode::Reward},          // 30
    {"bx", ItemTableCode::Box},             // 31
    {"fi", ItemTableCode::Fish},            // 32
    {"un", ItemTableCode::Union},           // 33
    {"rd", ItemTableCode::Raid},            // 34
    {"lk", ItemTableCode::Link},            // 35
    {"cu", ItemTableCode::Currency}         // 36
};

// Reverse mapping from table code to prefix
const std::unordered_map<ItemTableCode, std::string> ItemCodeMapper::s_tableCodeToPrefix = {
    {ItemTableCode::UpperArmor, "iu"}, {ItemTableCode::LowerArmor, "il"},
    {ItemTableCode::Gloves, "ig"}, {ItemTableCode::Shoes, "is"},
    {ItemTableCode::Helmet, "ih"}, {ItemTableCode::Shield, "id"},
    {ItemTableCode::Weapon, "iw"}, {ItemTableCode::Cloak, "ik"},
    {ItemTableCode::Ring, "ii"}, {ItemTableCode::Amulet, "ia"},
    {ItemTableCode::Bullet, "ib"}, {ItemTableCode::Material, "im"},
    {ItemTableCode::Elixir, "ie"}, {ItemTableCode::Potion, "ip"},
    {ItemTableCode::Food, "if"}, {ItemTableCode::Charm, "ic"},
    {ItemTableCode::Ticket, "it"}, {ItemTableCode::Ore, "io"},
    {ItemTableCode::Rare, "ir"}, {ItemTableCode::Natural, "in"},
    {ItemTableCode::Yggdrasil, "iy"}, {ItemTableCode::Zone, "iz"},
    {ItemTableCode::Quest, "iq"}, {ItemTableCode::Experience, "ix"},
    {ItemTableCode::Jewel, "ij"}, {ItemTableCode::Gate, "gt"},
    {ItemTableCode::Treasure, "tr"}, {ItemTableCode::Skill, "sk"},
    {ItemTableCode::Time, "ti"}, {ItemTableCode::Event, "ev"},
    {ItemTableCode::Reward, "re"}, {ItemTableCode::Box, "bx"},
    {ItemTableCode::Fish, "fi"}, {ItemTableCode::Union, "un"},
    {ItemTableCode::Raid, "rd"}, {ItemTableCode::Link, "lk"},
    {ItemTableCode::Currency, "cu"}
};

// Table code to description mapping
const std::unordered_map<ItemTableCode, std::string> ItemCodeMapper::s_tableCodeToDescription = {
    {ItemTableCode::UpperArmor, "Upper body armor/clothing"},
    {ItemTableCode::LowerArmor, "Lower body armor/clothing"},
    {ItemTableCode::Gloves, "Gloves/hand equipment"},
    {ItemTableCode::Shoes, "Shoes/foot equipment"},
    {ItemTableCode::Helmet, "Helmet/head equipment"},
    {ItemTableCode::Shield, "Shield/defensive equipment"},
    {ItemTableCode::Weapon, "Weapon/offensive equipment"},
    {ItemTableCode::Cloak, "Cloak/cape equipment"},
    {ItemTableCode::Ring, "Ring/finger accessories"},
    {ItemTableCode::Amulet, "Amulet/neck accessories"},
    {ItemTableCode::Bullet, "Bullet/ammunition"},
    {ItemTableCode::Material, "Crafting materials/tools"},
    {ItemTableCode::Elixir, "Elixir/potions"},
    {ItemTableCode::Potion, "Potions/consumables"},
    {ItemTableCode::Food, "Food items"},
    {ItemTableCode::Charm, "Charm items"},
    {ItemTableCode::Ticket, "Ticket items"},
    {ItemTableCode::Ore, "Ore/mining materials"},
    {ItemTableCode::Rare, "Rare items"},
    {ItemTableCode::Natural, "Natural items (FORBIDDEN)"},
    {ItemTableCode::Yggdrasil, "Yggdrasil items"},
    {ItemTableCode::Zone, "Zone items"},
    {ItemTableCode::Quest, "Quest items"},
    {ItemTableCode::Experience, "Experience items"},
    {ItemTableCode::Jewel, "Jewel items"},
    {ItemTableCode::Gate, "Gate items"},
    {ItemTableCode::Treasure, "Treasure items"},
    {ItemTableCode::Skill, "Skill items"},
    {ItemTableCode::Time, "Time items"},
    {ItemTableCode::Event, "Event items"},
    {ItemTableCode::Reward, "Reward items"},
    {ItemTableCode::Box, "Box items"},
    {ItemTableCode::Fish, "Fish items"},
    {ItemTableCode::Union, "Union items"},
    {ItemTableCode::Raid, "Raid items"},
    {ItemTableCode::Link, "Link items"},
    {ItemTableCode::Currency, "Currency items"}
};

/**
 * Get table code from item code prefix
 */
ItemTableCode ItemCodeMapper::GetTableCode(const std::string& prefix) {
    if (prefix.length() != ItemValidation::ITEM_PREFIX_LENGTH) {
        return ItemTableCode::Invalid;
    }
    
    // Convert to lowercase for case-insensitive comparison
    std::string lowerPrefix = prefix;
    std::transform(lowerPrefix.begin(), lowerPrefix.end(), lowerPrefix.begin(), ::tolower);
    
    auto it = s_prefixToTableCode.find(lowerPrefix);
    return (it != s_prefixToTableCode.end()) ? it->second : ItemTableCode::Invalid;
}

/**
 * Get table code from full item code
 */
ItemTableCode ItemCodeMapper::GetTableCodeFromItem(const std::string& itemCode) {
    if (itemCode.length() < ItemValidation::ITEM_PREFIX_LENGTH) {
        return ItemTableCode::Invalid;
    }
    
    return GetTableCode(itemCode.substr(0, ItemValidation::ITEM_PREFIX_LENGTH));
}

/**
 * Check if table code represents equipment
 */
bool ItemCodeMapper::IsEquipmentTableCode(ItemTableCode tableCode) {
    int32_t code = static_cast<int32_t>(tableCode);
    return code >= ItemValidation::MIN_EQUIPMENT_TABLE_CODE && 
           code <= ItemValidation::MAX_EQUIPMENT_TABLE_CODE;
}

/**
 * Check if table code represents equipment (integer version)
 */
bool ItemCodeMapper::IsEquipmentTableCode(int32_t tableCode) {
    return tableCode >= ItemValidation::MIN_EQUIPMENT_TABLE_CODE && 
           tableCode <= ItemValidation::MAX_EQUIPMENT_TABLE_CODE;
}

/**
 * Get item type description
 */
std::string ItemCodeMapper::GetItemTypeDescription(ItemTableCode tableCode) {
    auto it = s_tableCodeToDescription.find(tableCode);
    return (it != s_tableCodeToDescription.end()) ? it->second : "Unknown item type";
}

/**
 * Get item prefix from table code
 */
std::string ItemCodeMapper::GetItemPrefix(ItemTableCode tableCode) {
    auto it = s_tableCodeToPrefix.find(tableCode);
    return (it != s_tableCodeToPrefix.end()) ? it->second : "";
}

/**
 * Convert table code to equipment slot
 */
EquipmentSlot EquipmentSlotMapper::TableCodeToSlot(ItemTableCode tableCode) {
    if (ItemCodeMapper::IsEquipmentTableCode(tableCode)) {
        return static_cast<EquipmentSlot>(static_cast<int32_t>(tableCode));
    }
    return EquipmentSlot::Invalid;
}

/**
 * Convert table code to equipment slot (integer version)
 */
EquipmentSlot EquipmentSlotMapper::TableCodeToSlot(int32_t tableCode) {
    if (ItemCodeMapper::IsEquipmentTableCode(tableCode)) {
        return static_cast<EquipmentSlot>(tableCode);
    }
    return EquipmentSlot::Invalid;
}

/**
 * Get equipment slot name
 */
std::string EquipmentSlotMapper::GetSlotName(EquipmentSlot slot) {
    switch (slot) {
        case EquipmentSlot::UpperArmor: return "Upper Armor";
        case EquipmentSlot::LowerArmor: return "Lower Armor";
        case EquipmentSlot::Gloves: return "Gloves";
        case EquipmentSlot::Shoes: return "Shoes";
        case EquipmentSlot::Helmet: return "Helmet";
        case EquipmentSlot::Shield: return "Shield";
        case EquipmentSlot::Weapon: return "Weapon";
        case EquipmentSlot::Cloak: return "Cloak";
        default: return "Invalid";
    }
}

/**
 * Check if slot is valid equipment slot
 */
bool EquipmentSlotMapper::IsValidEquipmentSlot(EquipmentSlot slot) {
    return slot != EquipmentSlot::Invalid && 
           static_cast<uint8_t>(slot) <= static_cast<uint8_t>(EquipmentSlot::Cloak);
}

} // namespace Items
} // namespace NexusProtection
