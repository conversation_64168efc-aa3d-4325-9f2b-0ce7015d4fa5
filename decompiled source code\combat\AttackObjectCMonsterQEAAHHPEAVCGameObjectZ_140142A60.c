/*
 * Function: ?AttackObject@CMonster@@QEAAHHPEAVCGameObject@@@Z
 * Address: 0x140142A60
 */

signed __int64 __fastcall CMonster::AttackObject(CMonster *this, int nDamage, CGameObject *pOri)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMonster *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_LifeCicle = GetLoopTime();
  return 1i64;
}
