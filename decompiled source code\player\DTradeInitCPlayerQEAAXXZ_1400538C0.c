/*
 * Function: ?DTradeInit@CPlayer@@QEAAXXZ
 * Address: 0x1400538C0
 */

void __fastcall CPlayer::DTradeInit(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CPlayer *p_pDst; // [sp+28h] [bp-20h]@5
  CPlayer *lp_pOne; // [sp+50h] [bp+8h]@1

  lp_pOne = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( lp_pOne->m_pmTrd.bDTradeMode )
  {
    p_pDst = 0i64;
    if ( DTradeEqualPerson(lp_pOne, &p_pDst) )
    {
      _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
      CPlayer::SendMsg_DTradeCancleInform(p_pDst);
    }
    _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    CPlayer::SendMsg_DTradeCancleInform(lp_pOne);
  }
}
