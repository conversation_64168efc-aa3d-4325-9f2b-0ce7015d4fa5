/*
 * Function: ?OnInitialUpdate@CGameServerView@@MEAAXXZ
 * Address: 0x14002AC20
 */

void __fastcall CGameServerView::OnInitialUpdate(CGameServerView *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  struct CFrameWnd *v4; // [sp+20h] [bp-18h]@4
  CGameServerView *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CFormView::OnInitialUpdate((CFormView *)&v5->vfptr);
  v4 = CWnd::GetParentFrame((CWnd *)&v5->vfptr);
  ((void (__fastcall *)(struct CFrameWnd *, signed __int64))v4->vfptr[16].Dump)(v4, 1i64);
  CScrollView::ResizeParentToFit((CScrollView *)&v5->vfptr, 1);
}
