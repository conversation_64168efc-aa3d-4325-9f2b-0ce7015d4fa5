/*
 * Function: ?request_db_query@AutoMineMachineMng@@QEAAEPEAD@Z
 * Address: 0x1402D6C30
 */

char __fastcall AutoMineMachineMng::request_db_query(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  AutoMineMachineMng *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = *pdata;
  switch ( v6 )
  {
    case 0:
      result = AutoMineMachineMng::_db_qry_insert_newowner(v7, pdata);
      break;
    case 1:
      result = AutoMineMachineMng::_db_qry_update_battery_charge(v7, pdata);
      break;
    case 5:
      result = AutoMineMachineMng::_db_qry_update_battery_discharge(v7, pdata);
      break;
    case 2:
      result = AutoMineMachineMng::_db_qry_update_mineore(v7, pdata);
      break;
    case 3:
      result = AutoMineMachineMng::_db_qry_update_workstate(v7, pdata);
      break;
    case 4:
      result = AutoMineMachineMng::_db_qry_update_selore(v7, pdata);
      break;
    case 6:
      result = AutoMineMachineMng::_db_qry_update_moveore(v7, pdata);
      break;
    default:
      result = 24;
      break;
  }
  return result;
}
