/*
 * Function: ?Init@CMoveMapLimitInfoPortal@@UEAA_NXZ
 * Address: 0x1403A4120
 */

bool __fastcall CMoveMapLimitInfoPortal::Init(CMoveMapLimitInfoPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  bool result; // al@8
  __int64 v5; // [sp+0h] [bp-48h]@1
  CMyTimer *v6; // [sp+20h] [bp-28h]@7
  CMyTimer *v7; // [sp+28h] [bp-20h]@4
  __int64 v8; // [sp+30h] [bp-18h]@4
  CMyTimer *v9; // [sp+38h] [bp-10h]@5
  CMoveMapLimitInfoPortal *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  v7 = (CMyTimer *)operator new(0x18ui64);
  if ( v7 )
  {
    CMyTimer::CMyTimer(v7);
    v9 = (CMyTimer *)v3;
  }
  else
  {
    v9 = 0i64;
  }
  v6 = v9;
  v10->m_pkNotifyForceMoveHQTimer = v9;
  if ( v10->m_pkNotifyForceMoveHQTimer )
  {
    result = CMoveMapLimitInfoPortal::LoadINI(v10);
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8F30,
      "CMoveMapLimitInfoPortal::Init() : m_pkNotifyForceMoveHQTimer = new CMyTimer Fail!");
    result = 0;
  }
  return result;
}
