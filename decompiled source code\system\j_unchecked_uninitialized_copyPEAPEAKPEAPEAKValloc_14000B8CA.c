/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAKPEAPEAKV?$allocator@PEAK@std@@@stdext@@YAPEAPEAKPEAPEAK00AEAV?$allocator@PEAK@std@@@Z
 * Address: 0x14000B8CA
 */

unsigned int **__fastcall stdext::unchecked_uninitialized_copy<unsigned long * *,unsigned long * *,std::allocator<unsigned long *>>(unsigned int **_First, unsigned int **_Last, unsigned int **_Dest, std::allocator<unsigned long *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<unsigned long * *,unsigned long * *,std::allocator<unsigned long *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
