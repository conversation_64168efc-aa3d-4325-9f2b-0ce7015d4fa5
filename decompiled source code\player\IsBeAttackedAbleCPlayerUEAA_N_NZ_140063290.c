/*
 * Function: ?IsBeAttackedAble@CPlayer@@UEAA_N_N@Z
 * Address: 0x140063290
 */

bool __fastcall CPlayer::IsBeAttackedAble(CPlayer *this, bool bFirst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1
  bool v7; // [sp+38h] [bp+10h]@1

  v7 = bFirst;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !v6->m_bLive || v6->m_bObserver || v6->m_bCorpse )
  {
    result = 0;
  }
  else if ( _effect_parameter::GetEff_State(&v6->m_EP, 20) )
  {
    result = 0;
  }
  else if ( _effect_parameter::GetEff_State(&v6->m_EP, 28) )
  {
    result = 0;
  }
  else
  {
    result = !v7 || !_effect_parameter::GetEff_State(&v6->m_EP, 21);
  }
  return result;
}
