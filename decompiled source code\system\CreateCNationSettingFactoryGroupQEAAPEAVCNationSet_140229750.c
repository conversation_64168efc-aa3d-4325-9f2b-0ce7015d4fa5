/*
 * Function: ?Create@CNationSettingFactoryGroup@@QEAAPEAVCNationSettingData@@HPEBD_N@Z
 * Address: 0x140229750
 */

int __fastcall CNationSettingFactoryGroup::Create(CNationSettingFactoryGroup *this, int iNationCode, const char *szNationCodeStr, bool bServiceMode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  CNationSettingFactory *pData; // [sp+28h] [bp-20h]@4
  CNationSettingFactoryGroup *v9; // [sp+50h] [bp+8h]@1
  int kKey; // [sp+58h] [bp+10h]@1
  const char *v11; // [sp+60h] [bp+18h]@1
  bool v12; // [sp+68h] [bp+20h]@1

  v12 = bServiceMode;
  v11 = szNationCodeStr;
  kKey = iNationCode;
  v9 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  pData = 0i64;
  if ( CHashMapPtrPool<int,CNationSettingFactory>::get(&v9->m_kPool, iNationCode, &pData) )
    result = ((int (__fastcall *)(CNationSettingFactory *, _QWORD, const char *, _QWORD))pData->vfptr->Create)(
               pData,
               (unsigned int)kKey,
               v11,
               v12);
  else
    result = 0;
  return result;
}
