/*
 * Function: ?Init@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CD4A0
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Init(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v3; // rax@4
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  v6->m_uiMapCnt = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v3);
  if ( v6->m_uiMapCnt )
    result = GUILD_BATTLE::CReservedGuildScheduleDayGroup::Init(v6->m_kList, v6->m_uiMapCnt)
          && GUILD_BATTLE::CReservedGuildScheduleDayGroup::Init(&v6->m_kList[1], v6->m_uiMapCnt);
  else
    result = 0;
  return result;
}
