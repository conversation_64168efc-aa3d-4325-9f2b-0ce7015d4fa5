/*
 * Function: ?MakeHash@CCryptor@@QEAA_NPEBE_KPEAE1@Z
 * Address: 0x14046B5C0
 */

char __fastcall CCryptor::MakeHash(CCryptor *this, const char *pBuff, unsigned __int64 tBufSize, char *pHash, unsigned __int64 tHashSize)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CryptoPP::SHA256 *v8; // rax@6
  CryptoPP::SHA256 *v9; // rax@6
  __int64 v10; // [sp+0h] [bp-78h]@1
  __int64 v11; // [sp+58h] [bp-20h]@4
  CryptoPP::ClonableVtbl *v12; // [sp+60h] [bp-18h]@6
  CryptoPP::ClonableVtbl *v13; // [sp+68h] [bp-10h]@6
  CCryptor *v14; // [sp+80h] [bp+8h]@1
  char *v15; // [sp+98h] [bp+20h]@1

  v15 = pHash;
  v14 = this;
  v5 = &v10;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v11 = -2i64;
  if ( tHashSize == 32 )
  {
    v8 = v14->m_pHash;
    v12 = v14->m_pHash->vfptr;
    ((void (__fastcall *)(CryptoPP::SHA256 *))v12[1].Clone)(v8);
    v9 = v14->m_pHash;
    v13 = v14->m_pHash->vfptr;
    ((void (__fastcall *)(CryptoPP::SHA256 *, char *))v13[2].Clone)(v9, v15);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
