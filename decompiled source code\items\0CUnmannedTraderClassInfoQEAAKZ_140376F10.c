/*
 * Function: ??0CUnmannedTraderClassInfo@@QEAA@K@Z
 * Address: 0x140376F10
 */

void __fastcall CUnmannedTraderClassInfo::CUnmannedTraderClassInfo(CUnmannedTraderClassInfo *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderClassInfo *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->vfptr = (CUnmannedTraderClassInfoVtbl *)&CUnmannedTraderClassInfo::`vftable';
  v5->m_dwID = dwID;
  v5->m_dwVer = 0;
  strcpy_0(v5->m_szTypeName, "NONE");
  strcpy_0(v5->m_szClassName, "NONE");
}
