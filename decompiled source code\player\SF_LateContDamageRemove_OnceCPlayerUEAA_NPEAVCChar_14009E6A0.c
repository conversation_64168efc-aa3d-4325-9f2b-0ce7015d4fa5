/*
 * Function: ?SF_LateContDamageRemove_Once@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x14009E6A0
 */

char __fastcall CPlayer::SF_LateContDamageRemove_Once(CPlayer *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@21
  __int64 v5; // [sp+0h] [bp-68h]@1
  int v6; // [sp+30h] [bp-38h]@4
  _sf_continous (*v7)[8]; // [sp+38h] [bp-30h]@4
  int j; // [sp+40h] [bp-28h]@4
  _sf_continous (*v9)[8]; // [sp+48h] [bp-20h]@7
  _base_fld *v10; // [sp+50h] [bp-18h]@8
  CCharacter *v11; // [sp+58h] [bp-10h]@9
  CCharacter *v12; // [sp+78h] [bp+10h]@1

  v12 = pDstObj;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = -1;
  v7 = 0i64;
  for ( j = 0; j < 8; ++j )
  {
    v9 = (_sf_continous (*)[8])((char *)v12->m_SFCont + 48 * j);
    if ( v9 )
    {
      v10 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
      if ( !v10
        || (v11 = v12, !HIBYTE(v12[25].m_SFContAura[0][5].m_wDurSec))
        || (*v9)[0].m_byEffectCode != 3
        || (*v9)[0].m_wEffectIndex != v10->m_dwIndex )
      {
        if ( v6 == -1 )
        {
          v6 = j;
          v7 = v9;
        }
        else if ( (*v9)[0].m_dwStartSec > (*v7)[0].m_dwStartSec )
        {
          v6 = j;
          v7 = v9;
        }
      }
    }
  }
  if ( v6 == -1 )
  {
    result = 0;
  }
  else if ( CPlayer::ms_pXmas_Snow_Bullet_Effect
         && CPlayer::ms_pXmas_Snow_Bullet_Effect->m_dwIndex == (*v7)[0].m_wEffectIndex
         && (*v7)[0].m_byEffectCode == 3 )
  {
    result = 0;
  }
  else
  {
    CCharacter::RemoveSFContEffect(v12, 0, v6, 0, 0);
    result = 1;
  }
  return result;
}
