/*
 * Function: ??$_Ptr_cat@V?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@0@AEAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x1405ABEC0
 */

char std::_Ptr_cat<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *>()
{
  char v1; // [sp+0h] [bp-18h]@0

  return v1;
}
