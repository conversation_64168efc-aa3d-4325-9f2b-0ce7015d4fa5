/**
 * @file MonsterSetInfoData.cpp
 * @brief Implementation of monster configuration data management class
 * @details Manages monster settings including drop rates, tolerance, and blocking information
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "MonsterSetInfoData.h"
#include <algorithm>
#include <fstream>
#include <sstream>
#include <cmath>
#include <stdexcept>

// Forward declaration includes (these would normally be in separate headers)
// For now, we'll use placeholder structures until we refactor the dependencies
struct _mon_block_info {
    int32_t blockId;
    char blockName[64];
    bool isActive;
    
    _mon_block_info() : blockId(0), isActive(false) {
        blockName[0] = '\0';
    }
};

namespace NexusProtection {
namespace World {

// Constructor
MonsterSetInfoData::MonsterSetInfoData()
    : m_iMonsterLootRateSame(100)
    , m_fMonsterForcePowerRate(1.0f)
    , m_fLostMonsterTargetDistance(50.0f)
    , m_strRotMonBlk_Ar(nullptr)
    , m_nMonBlkCount(0) {
    Init();
}

// Destructor
MonsterSetInfoData::~MonsterSetInfoData() {
    Cleanup();
}

// Move constructor
MonsterSetInfoData::MonsterSetInfoData(MonsterSetInfoData&& other) noexcept
    : m_iMonsterLootingRateUp(std::move(other.m_iMonsterLootingRateUp))
    , m_iMonsterLootingRateDown(std::move(other.m_iMonsterLootingRateDown))
    , m_iMonsterLootRateSame(other.m_iMonsterLootRateSame)
    , m_fToleranceProbMax(std::move(other.m_fToleranceProbMax))
    , m_fMonsterForcePowerRate(other.m_fMonsterForcePowerRate)
    , m_fLostMonsterTargetDistance(other.m_fLostMonsterTargetDistance)
    , m_strRotMonBlk_Ar(std::move(other.m_strRotMonBlk_Ar))
    , m_nMonBlkCount(other.m_nMonBlkCount) {
    
    // Reset the moved-from object
    other.m_iMonsterLootRateSame = 100;
    other.m_fMonsterForcePowerRate = 1.0f;
    other.m_fLostMonsterTargetDistance = 50.0f;
    other.m_nMonBlkCount = 0;
}

// Move assignment operator
MonsterSetInfoData& MonsterSetInfoData::operator=(MonsterSetInfoData&& other) noexcept {
    if (this != &other) {
        // Clean up current resources
        Cleanup();
        
        // Move data from other
        m_iMonsterLootingRateUp = std::move(other.m_iMonsterLootingRateUp);
        m_iMonsterLootingRateDown = std::move(other.m_iMonsterLootingRateDown);
        m_iMonsterLootRateSame = other.m_iMonsterLootRateSame;
        m_fToleranceProbMax = std::move(other.m_fToleranceProbMax);
        m_fMonsterForcePowerRate = other.m_fMonsterForcePowerRate;
        m_fLostMonsterTargetDistance = other.m_fLostMonsterTargetDistance;
        m_strRotMonBlk_Ar = std::move(other.m_strRotMonBlk_Ar);
        m_nMonBlkCount = other.m_nMonBlkCount;
        
        // Reset the moved-from object
        other.m_iMonsterLootRateSame = 100;
        other.m_fMonsterForcePowerRate = 1.0f;
        other.m_fLostMonsterTargetDistance = 50.0f;
        other.m_nMonBlkCount = 0;
    }
    return *this;
}

// Load monster configuration from file
bool MonsterSetInfoData::Load(const std::string& fileName) {
    try {
        // Initialize to defaults first
        Init();
        
        // Load different configuration sections
        bool success = true;
        success &= LoadDropRates(fileName);
        success &= LoadToleranceSettings(fileName);
        success &= LoadRotationBlocks(fileName);
        
        return success;
    }
    catch (const std::exception&) {
        // Reset to defaults on any error
        Init();
        return false;
    }
}

// Get monster drop rate based on level difference
uint32_t MonsterSetInfoData::GetMonsterDropRate(int32_t levelDifference) const {
    if (levelDifference == 0) {
        return m_iMonsterLootRateSame;
    }
    else if (levelDifference > 0) {
        return GetDropRateUp(levelDifference);
    }
    else {
        return GetDropRateDown(std::abs(levelDifference));
    }
}

// Get maximum tolerance probability for a monster grade
float MonsterSetInfoData::GetMaxToleranceProbMax(int32_t monsterGrade) const {
    int32_t validGrade = ValidateMonsterGrade(monsterGrade);
    return m_fToleranceProbMax[validGrade];
}

// Get maximum tolerance probability for a monster grade (enum version)
float MonsterSetInfoData::GetMaxToleranceProbMax(MonsterGrade grade) const {
    return GetMaxToleranceProbMax(static_cast<int32_t>(grade));
}

// Get monster force power rate
float MonsterSetInfoData::GetMonsterForcePowerRate() const {
    return m_fMonsterForcePowerRate;
}

// Get lost monster target distance
float MonsterSetInfoData::GetLostMonsterTargetDistance() const {
    return m_fLostMonsterTargetDistance;
}

// Check if rotation blocking is enabled for a specific block
bool MonsterSetInfoData::IsRotateBlock(const struct _mon_block_info* blockInfo) const {
    if (!blockInfo || !m_strRotMonBlk_Ar || m_nMonBlkCount <= 0) {
        return false;
    }
    
    // Search for matching block
    for (int32_t i = 0; i < m_nMonBlkCount; ++i) {
        const auto& rotBlock = m_strRotMonBlk_Ar[i];
        if (rotBlock.blockId == blockInfo->blockId && rotBlock.isActive) {
            return true;
        }
    }
    
    return false;
}

// Get the number of rotation blocks
std::size_t MonsterSetInfoData::GetRotationBlockCount() const {
    return static_cast<std::size_t>(std::max(0, m_nMonBlkCount));
}

// Get rotation block by index
const MonsterRotationBlock* MonsterSetInfoData::GetRotationBlock(std::size_t index) const {
    if (!m_strRotMonBlk_Ar || index >= GetRotationBlockCount()) {
        return nullptr;
    }
    return &m_strRotMonBlk_Ar[index];
}

// Initialize all data to default values
void MonsterSetInfoData::Init() {
    SetDefaults();
}

// Check if configuration data is valid
bool MonsterSetInfoData::IsValid() const {
    // Check drop rates are within reasonable bounds (0-1000%)
    for (const auto& rate : m_iMonsterLootingRateUp) {
        if (rate > 1000) return false;
    }
    
    for (const auto& rate : m_iMonsterLootingRateDown) {
        if (rate > 1000) return false;
    }
    
    if (m_iMonsterLootRateSame > 1000) return false;
    
    // Check tolerance probabilities are within valid range (0.0-1.0)
    for (const auto& prob : m_fToleranceProbMax) {
        if (prob < 0.0f || prob > 1.0f) return false;
    }
    
    // Check other parameters are reasonable
    if (m_fMonsterForcePowerRate < 0.0f || m_fMonsterForcePowerRate > 10.0f) return false;
    if (m_fLostMonsterTargetDistance < 0.0f || m_fLostMonsterTargetDistance > 1000.0f) return false;
    
    return true;
}

// Get drop rate for level difference (positive levels)
uint32_t MonsterSetInfoData::GetDropRateUp(int32_t levelDiff) const {
    int32_t validDiff = ValidateLevelDifference(levelDiff);
    if (validDiff <= 0) return m_iMonsterLootRateSame;
    
    // Clamp to array bounds (1-10, with 10+ using index 10)
    int32_t index = std::min(validDiff - 1, 10);
    return m_iMonsterLootingRateUp[index];
}

// Get drop rate for level difference (negative levels)
uint32_t MonsterSetInfoData::GetDropRateDown(int32_t levelDiff) const {
    int32_t validDiff = ValidateLevelDifference(levelDiff);
    if (validDiff <= 0) return m_iMonsterLootRateSame;
    
    // Clamp to array bounds (1-10, with 10+ using index 10)
    int32_t index = std::min(validDiff - 1, 10);
    return m_iMonsterLootingRateDown[index];
}

// Get drop rate for same level
uint32_t MonsterSetInfoData::GetDropRateSame() const {
    return m_iMonsterLootRateSame;
}

// Validate level difference parameter
int32_t MonsterSetInfoData::ValidateLevelDifference(int32_t levelDiff) const {
    return std::max(0, std::abs(levelDiff));
}

// Validate monster grade parameter
int32_t MonsterSetInfoData::ValidateMonsterGrade(int32_t grade) const {
    return std::clamp(grade, 0, static_cast<int32_t>(MonsterGrade::MaxGrades) - 1);
}

// Load drop rate configuration from file
bool MonsterSetInfoData::LoadDropRates(const std::string& fileName) {
    // Placeholder implementation - in real code this would parse INI/config files
    // For now, set reasonable default values
    
    // Default drop rates for higher level monsters (decreasing as level diff increases)
    for (size_t i = 0; i < m_iMonsterLootingRateUp.size(); ++i) {
        m_iMonsterLootingRateUp[i] = std::max(10u, 100u - static_cast<uint32_t>(i * 8));
    }
    
    // Default drop rates for lower level monsters (decreasing as level diff increases)
    for (size_t i = 0; i < m_iMonsterLootingRateDown.size(); ++i) {
        m_iMonsterLootingRateDown[i] = std::max(5u, 80u - static_cast<uint32_t>(i * 6));
    }
    
    m_iMonsterLootRateSame = 100; // 100% for same level
    
    return true;
}

// Load tolerance configuration from file
bool MonsterSetInfoData::LoadToleranceSettings(const std::string& fileName) {
    // Placeholder implementation - set reasonable defaults
    for (size_t i = 0; i < m_fToleranceProbMax.size(); ++i) {
        // Higher grades have higher tolerance (0.1 to 0.7)
        m_fToleranceProbMax[i] = 0.1f + (static_cast<float>(i) * 0.1f);
    }
    
    m_fMonsterForcePowerRate = 1.0f;
    m_fLostMonsterTargetDistance = 50.0f;
    
    return true;
}

// Load rotation block configuration from file
bool MonsterSetInfoData::LoadRotationBlocks(const std::string& fileName) {
    // Placeholder implementation - in real code this would load from config
    m_nMonBlkCount = 0;
    m_strRotMonBlk_Ar.reset();
    
    return true;
}

// Set default values for all configuration parameters
void MonsterSetInfoData::SetDefaults() {
    // Initialize drop rate arrays
    m_iMonsterLootingRateUp.fill(50);
    m_iMonsterLootingRateDown.fill(30);
    m_iMonsterLootRateSame = 100;
    
    // Initialize tolerance probabilities
    m_fToleranceProbMax.fill(0.5f);
    
    // Initialize other parameters
    m_fMonsterForcePowerRate = 1.0f;
    m_fLostMonsterTargetDistance = 50.0f;
    
    // Initialize rotation blocks
    m_nMonBlkCount = 0;
    m_strRotMonBlk_Ar.reset();
}

// Cleanup allocated resources
void MonsterSetInfoData::Cleanup() {
    m_strRotMonBlk_Ar.reset();
    m_nMonBlkCount = 0;
}

} // namespace World
} // namespace NexusProtection
