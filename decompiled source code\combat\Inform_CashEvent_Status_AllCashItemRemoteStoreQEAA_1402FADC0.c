/*
 * Function: ?Inform_CashEvent_Status_All@CashItemRemoteStore@@QEAAXEEPEAU_cash_event_ini@@@Z
 * Address: 0x1402FADC0
 */

void __fastcall CashItemRemoteStore::Inform_CashEvent_Status_All(CashItemRemoteStore *this, char byEventType, char byStatus, _cash_event_ini *pIni)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-F8h]@1
  _cash_lim_sale *pLim; // [sp+20h] [bp-D8h]@10
  int v8; // [sp+28h] [bp-D0h]@11
  int v9; // [sp+30h] [bp-C8h]@11
  int v10; // [sp+38h] [bp-C0h]@11
  int v11; // [sp+40h] [bp-B8h]@11
  int v12; // [sp+48h] [bp-B0h]@11
  int v13; // [sp+50h] [bp-A8h]@11
  int v14; // [sp+58h] [bp-A0h]@11
  int v15; // [sp+60h] [bp-98h]@11
  int v16; // [sp+68h] [bp-90h]@11
  unsigned __int16 j; // [sp+70h] [bp-88h]@4
  CPlayer *v18; // [sp+78h] [bp-80h]@7
  char szEventName; // [sp+90h] [bp-68h]@11
  unsigned __int64 v20; // [sp+E0h] [bp-18h]@4
  CashItemRemoteStore *v21; // [sp+100h] [bp+8h]@1
  char v22; // [sp+108h] [bp+10h]@1
  char v23; // [sp+110h] [bp+18h]@1
  _cash_event_ini *pInia; // [sp+118h] [bp+20h]@1

  pInia = pIni;
  v23 = byStatus;
  v22 = byEventType;
  v21 = this;
  v4 = &v6;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v20 = (unsigned __int64)&v6 ^ _security_cookie;
  for ( j = 0; (signed int)j < 2532; ++j )
  {
    v18 = &g_Player + j;
    if ( v18->m_bOper )
    {
      if ( v18->m_bLive )
      {
        pLim = &v21->m_lim_event;
        ICsSendInterface::SendMsg_CashEventInform(v18->m_ObjID.m_wIndex, v22, v23, pInia, &v21->m_lim_event);
      }
    }
  }
  Get_CashEvent_Name(v22, &szEventName);
  v16 = pInia->m_byMinute[1];
  v15 = pInia->m_byHour[1];
  v14 = pInia->m_byDay[1];
  v13 = pInia->m_byMonth[1];
  v12 = pInia->m_wYear[1];
  v11 = pInia->m_byMinute[0];
  v10 = pInia->m_byHour[0];
  v9 = pInia->m_byDay[0];
  v8 = pInia->m_byMonth[0];
  LODWORD(pLim) = pInia->m_wYear[0];
  CLogFile::Write(
    &v21->m_cash_event[(unsigned __int8)v22].m_event_log,
    "[ %s CashEvent Inform when Event status change] [EventState : %d] [EventTime : %d/%d/%d %d:%d  ~ %d/%d/%d %d:%d ]",
    &szEventName,
    (unsigned __int8)v23);
}
