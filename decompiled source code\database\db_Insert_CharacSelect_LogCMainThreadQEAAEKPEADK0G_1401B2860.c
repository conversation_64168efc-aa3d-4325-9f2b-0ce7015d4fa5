/*
 * Function: ?db_Insert_CharacSelect_Log@CMainThread@@QEAAEKPEADK0GEEEEE@Z
 * Address: 0x1401B2860
 */

char __fastcall CMainThread::db_Insert_CharacSelect_Log(CMainThread *this, unsigned int dwAccountSerial, char *szAccount, unsigned int dwCharacSerial, char *pwszCharacName, unsigned __int16 dwYear, char byMonth, char byDay, char byHour, char byMin, char bySec)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v14; // [sp+0h] [bp-118h]@1
  char *v15; // [sp+20h] [bp-F8h]@4
  unsigned __int16 v16; // [sp+28h] [bp-F0h]@8
  char v17; // [sp+30h] [bp-E8h]@8
  char v18; // [sp+38h] [bp-E0h]@8
  char v19; // [sp+40h] [bp-D8h]@8
  char v20; // [sp+48h] [bp-D0h]@8
  char v21; // [sp+50h] [bp-C8h]@8
  char DstBuf; // [sp+70h] [bp-A8h]@4
  char v23; // [sp+71h] [bp-A7h]@4
  unsigned __int64 v24; // [sp+100h] [bp-18h]@4
  CMainThread *v25; // [sp+120h] [bp+8h]@1
  unsigned int dwAccountSeriala; // [sp+128h] [bp+10h]@1
  char *wszAccount; // [sp+130h] [bp+18h]@1
  unsigned int dwCharacSeriala; // [sp+138h] [bp+20h]@1

  dwCharacSeriala = dwCharacSerial;
  wszAccount = szAccount;
  dwAccountSeriala = dwAccountSerial;
  v25 = this;
  v11 = &v14;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v11 = -*********;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  v24 = (unsigned __int64)&v14 ^ _security_cookie;
  DstBuf = 0;
  memset(&v23, 0, 0x7Fui64);
  LODWORD(v15) = (unsigned __int8)byMonth;
  sprintf_s(&DstBuf, 0x80ui64, "tbl_characterselect_log_%04d%02d", dwYear);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v25->m_pWorldDB->vfptr, &DstBuf)
    || CRFWorldDatabase::CreateCharacterSelectLogTable(v25->m_pWorldDB, &DstBuf)
    || CRFWorldDatabase::CreateCharacterSelectLogTable(v25->m_pWorldDB, &DstBuf) )
  {
    v21 = bySec;
    v20 = byMin;
    v19 = byHour;
    v18 = byDay;
    v17 = byMonth;
    v16 = dwYear;
    v15 = pwszCharacName;
    if ( CRFWorldDatabase::InsertCharacterSelectLog(
           v25->m_pWorldDB,
           dwAccountSeriala,
           wszAccount,
           dwCharacSeriala,
           pwszCharacName,
           dwYear,
           byMonth,
           byDay,
           byHour,
           byMin,
           bySec) )
    {
      result = 0;
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
