/*
 * Function: ?CountRegistItem@CUnmannedTraderUserInfo@@AEAAXXZ
 * Address: 0x14035A100
 */

void __fastcall CUnmannedTraderUserInfo::CountRegistItem(CUnmannedTraderUserInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v3; // rax@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@4
  CUnmannedTraderUserInfo *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->m_byRegistCnt = 0;
  for ( j = 0; j < (signed int)v6->m_byMaxRegistCnt; ++j )
  {
    v3 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v6->m_vecRegistItemInfo,
           j);
    if ( CUnmannedTraderRegistItemInfo::IsRegist(v3) )
      ++v6->m_byRegistCnt;
  }
}
