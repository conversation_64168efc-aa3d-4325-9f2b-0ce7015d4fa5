/*
 * Function: ?BeginTimerAddLapse@CMyTimer@@QEAAXKK@Z
 * Address: 0x140438A30
 */

void __fastcall CMyTimer::BeginTimerAddLapse(CMyTimer *this, unsigned int dwTerm, unsigned int dwAddLapse)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMyTimer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6->m_bOper = 1;
  v6->m_nTickTerm = dwTerm;
  v6->m_dwTickOld = dwAddLapse + timeGetTime();
}
