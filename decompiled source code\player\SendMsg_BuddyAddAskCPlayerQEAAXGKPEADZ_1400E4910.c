/*
 * Function: ?SendMsg_BuddyAddAsk@CPlayer@@QEAAXGKPEAD@Z
 * Address: 0x1400E4910
 */

void __fastcall CPlayer::SendMsg_BuddyAddAsk(CPlayer *this, unsigned __int16 wAskerIndex, unsigned int dwAskerSerial, char *pwszAskerName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned int v8; // [sp+3Ah] [bp-5Eh]@4
  char Dest; // [sp+3Eh] [bp-5Ah]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v11; // [sp+65h] [bp-33h]@4
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CPlayer *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v4 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  *(_WORD *)szMsg = wAskerIndex;
  v8 = dwAskerSerial;
  strcpy_0(&Dest, pwszAskerName);
  pbyType = 31;
  v11 = 11;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, szMsg, 0x17u);
}
