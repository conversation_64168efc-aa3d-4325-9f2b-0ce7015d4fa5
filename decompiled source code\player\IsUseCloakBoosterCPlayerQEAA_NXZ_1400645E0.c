/*
 * Function: ?IsUseCloakBooster@CPlayer@@QEAA_NXZ
 * Address: 0x1400645E0
 */

bool __fastcall CPlayer::IsUseCloakBooster(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CEquipItemSFAgent::IsUseBooster(&v5->EquipItemSFAgent);
}
