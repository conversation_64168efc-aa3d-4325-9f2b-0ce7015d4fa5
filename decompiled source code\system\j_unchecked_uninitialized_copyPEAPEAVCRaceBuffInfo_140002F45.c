/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@stdext@@YAPEAPEAVCRaceBuffInfoByHolyQuest@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@Z
 * Address: 0x140002F45
 */

CRaceBuffInfoByHolyQuest **__fastcall stdext::unchecked_uninitialized_copy<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(CRaceBuffInfoByHolyQuest **_First, CRaceBuffInfoByHolyQuest **_Last, CRaceBuffInfoByHolyQuest **_Dest, std::allocator<CRaceBuffInfoByHolyQuest *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *,std::allocator<CRaceBuffInfoByHolyQuest *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
