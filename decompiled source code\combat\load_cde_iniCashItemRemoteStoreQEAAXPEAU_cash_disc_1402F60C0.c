/*
 * Function: ?load_cde_ini@CashItemRemoteStore@@QEAAXPEAU_cash_discount_ini_@@PEAU_FILETIME@@@Z
 * Address: 0x1402F60C0
 */

void __fastcall CashItemRemoteStore::load_cde_ini(CashItemRemoteStore *this, _cash_discount_ini_ *pIni, _FILETIME *pft)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  UINT v6; // [sp+20h] [bp-78h]@7
  int Dst; // [sp+38h] [bp-60h]@25
  int v8; // [sp+3Ch] [bp-5Ch]@25
  int v9; // [sp+40h] [bp-58h]@25
  int v10; // [sp+44h] [bp-54h]@25
  int v11; // [sp+48h] [bp-50h]@25
  int v12; // [sp+4Ch] [bp-4Ch]@25
  int v13; // [sp+58h] [bp-40h]@25
  _FILETIME ftWrite; // [sp+78h] [bp-20h]@7
  _cash_discount_ini_ *v15; // [sp+A8h] [bp+10h]@1
  _FILETIME *v16; // [sp+B0h] [bp+18h]@1

  v16 = pft;
  v15 = pIni;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pIni )
  {
    if ( pft )
    {
      v6 = 0;
      pIni->m_bUseCashDiscount = 0;
      pIni->m_cdeTime[0] = 0;
      pIni->m_cdeTime[1] = 0;
      pIni->m_bRepeat = 0;
      if ( GetLastWriteFileTime("./initialize/cash_discount.ini", &ftWrite) )
      {
        v16->dwHighDateTime = ftWrite.dwHighDateTime;
        v16->dwLowDateTime = ftWrite.dwLowDateTime;
        v6 = GetPrivateProfileIntA("Cash", "USE", 1, "./initialize/cash_discount.ini");
        if ( v6 == 1 )
        {
          v15->m_bUseCashDiscount = 0;
        }
        else
        {
          v15->m_bUseCashDiscount = 1;
          v15->m_bRepeat = GetPrivateProfileIntA("Cash", "REPEAT", 0, "./initialize/cash_discount.ini");
          v6 = GetPrivateProfileIntA("Cash", "REPEAT_DAY", 0, "./initialize/cash_discount.ini");
          if ( (signed int)v6 > 0 )
            v15->m_byRepeatDay = v6;
          else
            v15->m_bRepeat = 0;
          v15->m_wCsDiscount = GetPrivateProfileIntA("Cash", "SALE", 0, "./initialize/cash_discount.ini");
          v6 = GetPrivateProfileIntA("Cash", "BEGIN_YEAR", 0, "./initialize/cash_discount.ini");
          if ( v6 )
          {
            v15->m_wYear[0] = v6;
            v6 = GetPrivateProfileIntA("Cash", "BEGIN_MONTH", 0, "./initialize/cash_discount.ini");
            if ( v6 )
            {
              v15->m_byMonth[0] = v6;
              v6 = GetPrivateProfileIntA("Cash", "BEGIN_DAY", 0, "./initialize/cash_discount.ini");
              if ( v6 )
              {
                v15->m_byDay[0] = v6;
                v6 = GetPrivateProfileIntA("Cash", "BEGIN_HOUR", 0, "./initialize/cash_discount.ini");
                if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 23 )
                {
                  v15->m_byHour[0] = v6;
                  v6 = GetPrivateProfileIntA("Cash", "BEGIN_MINUTE", 0, "./initialize/cash_discount.ini");
                  if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 59 )
                  {
                    v15->m_byMinute[0] = v6;
                    memset_0(&Dst, 0, 0x24ui64);
                    v12 = v15->m_wYear[0] - 1900;
                    v11 = v15->m_byMonth[0] - 1;
                    v10 = v15->m_byDay[0];
                    v9 = v15->m_byHour[0];
                    v8 = v15->m_byMinute[0];
                    Dst = 0;
                    v13 = -1;
                    v15->m_cdeTime[0] = _mktime32((struct tm *)&Dst);
                    if ( v15->m_cdeTime[0] == -1 )
                    {
                      v15->m_bUseCashDiscount = 0;
                    }
                    else
                    {
                      v6 = GetPrivateProfileIntA("Cash", "END_YEAR", 0, "./initialize/cash_discount.ini");
                      if ( v6 )
                      {
                        v15->m_wYear[1] = v6;
                        v6 = GetPrivateProfileIntA("Cash", "END_MONTH", 0, "./initialize/cash_discount.ini");
                        if ( v6 )
                        {
                          v15->m_byMonth[1] = v6;
                          v6 = GetPrivateProfileIntA("Cash", "END_DAY", 0, "./initialize/cash_discount.ini");
                          if ( v6 )
                          {
                            v15->m_byDay[1] = v6;
                            v6 = GetPrivateProfileIntA("Cash", "END_HOUR", 0, "./initialize/cash_discount.ini");
                            if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 23 )
                            {
                              v15->m_byHour[1] = v6;
                              v6 = GetPrivateProfileIntA("Cash", "END_MINUTE", 0, "./initialize/cash_discount.ini");
                              if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 59 )
                              {
                                v15->m_byMinute[1] = v6;
                                memset_0(&Dst, 0, 0x24ui64);
                                v12 = v15->m_wYear[1] - 1900;
                                v11 = v15->m_byMonth[1] - 1;
                                v10 = v15->m_byDay[1];
                                v9 = v15->m_byHour[1];
                                v8 = v15->m_byMinute[1];
                                Dst = 0;
                                v13 = -1;
                                v15->m_cdeTime[1] = _mktime32((struct tm *)&Dst);
                                if ( v15->m_cdeTime[1] == -1 )
                                {
                                  v15->m_bUseCashDiscount = 0;
                                }
                                else
                                {
                                  v6 = GetPrivateProfileIntA("Cash", "EXPIRE_YEAR", 0, "./initialize/cash_discount.ini");
                                  if ( (signed int)v6 > 0 )
                                    v15->m_wYear[2] = v6;
                                  else
                                    v15->m_bRepeat = 0;
                                  v6 = GetPrivateProfileIntA(
                                         "Cash",
                                         "EXPIRE_MONTH",
                                         0,
                                         "./initialize/cash_discount.ini");
                                  if ( (signed int)v6 > 0 )
                                    v15->m_byMonth[2] = v6;
                                  else
                                    v15->m_bRepeat = 0;
                                  v6 = GetPrivateProfileIntA("Cash", "EXPIRE_DAY", 0, "./initialize/cash_discount.ini");
                                  if ( (signed int)v6 > 0 )
                                    v15->m_byDay[2] = v6;
                                  else
                                    v15->m_bRepeat = 0;
                                  v6 = GetPrivateProfileIntA("Cash", "EXPIRE_HOUR", 0, "./initialize/cash_discount.ini");
                                  if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 23 )
                                    v15->m_byHour[2] = v6;
                                  else
                                    v15->m_bRepeat = 0;
                                  v6 = GetPrivateProfileIntA(
                                         "Cash",
                                         "EXPIRE_MINUTE",
                                         0,
                                         "./initialize/cash_discount.ini");
                                  if ( (v6 & 0x80000000) == 0 && (signed int)v6 <= 59 )
                                    v15->m_byMinute[2] = v6;
                                  else
                                    v15->m_bRepeat = 0;
                                  memset_0(&Dst, 0, 0x24ui64);
                                  v12 = v15->m_wYear[2] - 1900;
                                  v11 = v15->m_byMonth[2] - 1;
                                  v10 = v15->m_byDay[2];
                                  v9 = v15->m_byHour[2];
                                  v8 = v15->m_byMinute[2];
                                  Dst = 0;
                                  v13 = -1;
                                  v15->m_cdeTime[2] = _mktime32((struct tm *)&Dst);
                                  if ( v15->m_cdeTime[2] == -1 )
                                    v15->m_bRepeat = 0;
                                }
                              }
                              else
                              {
                                v15->m_bUseCashDiscount = 0;
                              }
                            }
                            else
                            {
                              v15->m_bUseCashDiscount = 0;
                            }
                          }
                          else
                          {
                            v15->m_bUseCashDiscount = 0;
                          }
                        }
                        else
                        {
                          v15->m_bUseCashDiscount = 0;
                        }
                      }
                      else
                      {
                        v15->m_bUseCashDiscount = 0;
                      }
                    }
                  }
                  else
                  {
                    v15->m_bUseCashDiscount = 0;
                  }
                }
                else
                {
                  v15->m_bUseCashDiscount = 0;
                }
              }
              else
              {
                v15->m_bUseCashDiscount = 0;
              }
            }
            else
            {
              v15->m_bUseCashDiscount = 0;
            }
          }
          else
          {
            v15->m_bUseCashDiscount = 0;
          }
        }
      }
    }
  }
}
