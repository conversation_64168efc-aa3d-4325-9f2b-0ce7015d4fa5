/*
 * Function: ?ConvertTo@TimerBase@CryptoPP@@AEAAN_KW4Unit@12@@Z
 * Address: 0x1406608A0
 */

int __fastcall CryptoPP::TimerBase::ConvertTo(__int64 a1, __int64 a2, int a3)
{
  double v3; // xmm0_8@3
  double v4; // ST20_8@5

  if ( (unsigned __int64)a3 >= 4 )
    _wassert(
      L"unit < sizeof(unitsPerSecondTable) / sizeof(unitsPerSecondTable[0])",
      L"D:\\RF Project\\RF_Server64\\28 Crypto++\\hrtimer.cpp",
      0x1Bu);
  v3 = (double)(signed int)a2;
  if ( a2 < 0 )
    v3 = v3 + 1.844674407370955e19;
  v4 = v3 * (double)dword_1409850D0[a3];
  return (*(int (**)(void))(*(_QWORD *)a1 + 8i64))();
}
