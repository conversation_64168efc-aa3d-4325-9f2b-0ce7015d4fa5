/*
 * Function: ?dev_trap_attack_grade@CPlayer@@QEAA_NH@Z
 * Address: 0x1400C08B0
 */

char __fastcall CPlayer::dev_trap_attack_grade(CPlayer *this, int nPoint)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1

  v8 = nPoint;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7->m_nTrapMaxAttackPnt = nPoint;
  for ( j = 0; j < 20; ++j )
  {
    if ( _TRAP_PARAM::_param::isLoad((_TRAP_PARAM::_param *)&v7->m_pmTrp + j) )
      v7->m_pmTrp.m_Item[j].pItem->m_nTrapMaxAttackPnt = v8;
  }
  return 1;
}
