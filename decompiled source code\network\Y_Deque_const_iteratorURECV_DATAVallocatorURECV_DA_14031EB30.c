/*
 * Function: ??Y?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAAAEAV01@_J@Z
 * Address: 0x14031EB30
 */

std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+=(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, __int64 _Off)
{
  this->_Myoff += _Off;
  return this;
}
