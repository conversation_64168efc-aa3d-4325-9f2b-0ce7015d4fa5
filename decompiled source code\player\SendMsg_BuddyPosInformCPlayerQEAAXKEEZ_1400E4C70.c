/*
 * Function: ?SendMsg_BuddyPosInform@CPlayer@@QEAAXKEE@Z
 * Address: 0x1400E4C70
 */

void __fastcall CPlayer::SendMsg_BuddyPosInform(CPlayer *this, unsigned int dwDstSerial, char byMapIndex, char byPosCode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v8; // [sp+38h] [bp-40h]@4
  char v9; // [sp+39h] [bp-3Fh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  CPlayer *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v4 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  *(_DWORD *)szMsg = dwDstSerial;
  v8 = byMapIndex;
  v9 = byPosCode;
  pbyType = 31;
  v11 = 5;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 6u);
}
