/*
 * Function: j_??0?$_Vector_val@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderSchedule@@@1@@Z
 * Address: 0x1400021BC
 */

void __fastcall std::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, std::allocator<CUnmannedTraderSchedule> _Al)
{
  std::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_val<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    this,
    _Al);
}
