/*
 * Function: ?_db_load@TRC_AutoTrade@@QEAA_NE@Z
 * Address: 0x1402D8EA0
 */

char __fastcall TRC_AutoTrade::_db_load(TRC_AutoTrade *this, char byRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char byCurrTax; // [sp+34h] [bp-84h]@4
  char v7; // [sp+54h] [bp-64h]@4
  char pwszName; // [sp+78h] [bp-40h]@4
  int v9; // [sp+94h] [bp-24h]@4
  unsigned __int64 v10; // [sp+A0h] [bp-18h]@4
  TRC_AutoTrade *v11; // [sp+C0h] [bp+8h]@1
  char v12; // [sp+C8h] [bp+10h]@1

  v12 = byRace;
  v11 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  byCurrTax = 0;
  v7 = 0;
  v9 = CRFWorldDatabase::select_atrade_taxrate(pkDB, byRace, &pwszName, &byCurrTax, &v7);
  if ( v9 == 1 )
  {
    result = 0;
  }
  else
  {
    if ( (signed int)(unsigned __int8)byCurrTax < 5 || (signed int)(unsigned __int8)byCurrTax > 20 )
      byCurrTax = 5;
    if ( (signed int)(unsigned __int8)v7 < 5 || (signed int)(unsigned __int8)v7 > 20 )
      v7 = 5;
    ControllerTaxRate::setCurTaxRate(&v11->m_Controller, (float)(unsigned __int8)byCurrTax / 100.0);
    CLogFile::Write(
      &v11->m_serviceLog,
      "Init tax rate : Suggester:%s tax: %.2f",
      &pwszName,
      (float)((float)(unsigned __int8)byCurrTax / 100.0));
    v11->m_suggested.dwNext = (unsigned __int8)v7;
    strcpy_0(v11->m_suggested.wszMatterDst, &pwszName);
    CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateTaxRate(&stru_1799C9AF8, v12, byCurrTax);
    v11->m_bInit = 1;
    result = 1;
  }
  return result;
}
