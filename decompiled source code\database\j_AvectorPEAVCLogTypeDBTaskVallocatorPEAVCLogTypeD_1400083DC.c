/*
 * Function: j_??A?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAAEAPEAVCLogTypeDBTask@@_K@Z
 * Address: 0x1400083DC
 */

CLogTypeDBTask **__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator[](std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator[](this, _Pos);
}
