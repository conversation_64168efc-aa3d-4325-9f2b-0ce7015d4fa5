/*
 * Function: ??1CItemStoreManager@@QEAA@XZ
 * Address: 0x140348170
 */

void __fastcall CItemStoreManager::~CItemStoreManager(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  CMapItemStoreList *v4; // [sp+20h] [bp-48h]@5
  CMapItemStoreList *v5; // [sp+28h] [bp-40h]@5
  CMapItemStoreList *v6; // [sp+30h] [bp-38h]@10
  CMapItemStoreList *v7; // [sp+38h] [bp-30h]@10
  __int64 v8; // [sp+40h] [bp-28h]@4
  void *v9; // [sp+48h] [bp-20h]@6
  void *v10; // [sp+50h] [bp-18h]@11
  CItemStoreManager *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( v11->m_MapItemStoreList )
  {
    v5 = v11->m_MapItemStoreList;
    v4 = v5;
    if ( v5 )
      v9 = CMapItemStoreList::`vector deleting destructor'(v4, 3u);
    else
      v9 = 0i64;
    v11->m_MapItemStoreList = 0i64;
  }
  if ( v11->m_InstanceItemStoreList )
  {
    v7 = v11->m_InstanceItemStoreList;
    v6 = v7;
    if ( v7 )
      v10 = CMapItemStoreList::`vector deleting destructor'(v6, 3u);
    else
      v10 = 0i64;
    v11->m_InstanceItemStoreList = 0i64;
  }
  CRecordData::~CRecordData(&v11->m_tblItemStore);
  _qry_case_all_store_limit_item::~_qry_case_all_store_limit_item(&v11->m_Sheet);
  CMyTimer::~CMyTimer(&v11->m_tmrSaveTime);
  CMyTimer::~CMyTimer(&v11->m_tmrCheckTime);
}
