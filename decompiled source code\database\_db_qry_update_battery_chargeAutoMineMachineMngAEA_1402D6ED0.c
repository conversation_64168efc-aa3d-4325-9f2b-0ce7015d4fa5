/*
 * Function: ?_db_qry_update_battery_charge@AutoMineMachineMng@@AEAAEPEAD@Z
 * Address: 0x1402D6ED0
 */

char __fastcall AutoMineMachineMng::_db_qry_update_battery_charge(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned int dwBattery; // [sp+20h] [bp-48h]@4
  long double *dTotalDalant; // [sp+28h] [bp-40h]@6
  long double *dTotalGold; // [sp+30h] [bp-38h]@6
  char *byDate; // [sp+38h] [bp-30h]@6
  char *pwszName; // [sp+40h] [bp-28h]@6
  char *pbyProcRet; // [sp+48h] [bp-20h]@6
  char *v12; // [sp+50h] [bp-18h]@4

  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = pdata;
  dwBattery = *(_DWORD *)(pdata + 7);
  if ( CRFWorldDatabase::update_amine_battery(pkDB, pdata[1], pdata[2], *(_DWORD *)(pdata + 3), dwBattery) )
  {
    pbyProcRet = v12 + 45;
    pwszName = "AutoMine Charge";
    byDate = v12 + 41;
    dTotalGold = (long double *)(v12 + 33);
    dTotalDalant = (long double *)(v12 + 25);
    dwBattery = *(_DWORD *)(v12 + 21);
    result = CMainThread::db_output_guild_money(
               &g_Main,
               *(_DWORD *)(v12 + 13),
               *(_DWORD *)(v12 + 3),
               0,
               dwBattery,
               (long double *)(v12 + 25),
               (long double *)(v12 + 33),
               v12 + 41,
               "AutoMine Charge",
               v12 + 45);
  }
  else
  {
    result = 24;
  }
  return result;
}
