/*
 * Function: ?Initialize@LendItemMng@@QEAA_NXZ
 * Address: 0x14030DA20
 */

char __fastcall LendItemMng::Initialize(LendItemMng *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // rax@9
  unsigned int v5; // eax@16
  __int64 v6; // [sp+0h] [bp-118h]@1
  int j; // [sp+30h] [bp-E8h]@6
  char _Dest[126]; // [sp+50h] [bp-C8h]@16
  LendItemSheet **v9; // [sp+E0h] [bp-38h]@4
  LendItemSheet *v10; // [sp+E8h] [bp-30h]@11
  LendItemSheet *v11; // [sp+F0h] [bp-28h]@8
  __int64 v12; // [sp+F8h] [bp-20h]@4
  LendItemSheet *v13; // [sp+100h] [bp-18h]@9
  unsigned __int64 v14; // [sp+108h] [bp-10h]@4
  LendItemMng *v15; // [sp+120h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  v9 = (LendItemSheet **)operator new[](0x4F20ui64);
  v15->_ppkLendItem = v9;
  if ( v15->_ppkLendItem )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v11 = (LendItemSheet *)operator new(0x158ui64);
      if ( v11 )
      {
        LendItemSheet::LendItemSheet(v11, &g_Player + j);
        v13 = (LendItemSheet *)v4;
      }
      else
      {
        v13 = 0i64;
      }
      v10 = v13;
      v15->_ppkLendItem[j] = v13;
      if ( !v15->_ppkLendItem[j] )
      {
        LendItemMng::ReleaseAll(v15);
        return 0;
      }
      if ( !LendItemSheet::Initialzie(v15->_ppkLendItem[j]) )
      {
        LendItemMng::ReleaseAll(v15);
        return 0;
      }
    }
    _Dest[0] = 0;
    memset(&_Dest[1], 0, 0x7Dui64);
    v5 = GetKorLocalTime();
    sprintf_s<126>((char (*)[126])_Dest, "..\\ZoneServerLog\\SystemLog\\PartiallyPaid\\LendLog%d.log", v5);
    CLogFile::SetWriteLogFile(&v15->_kLogSys, _Dest, 1, 0, 1, 1);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
