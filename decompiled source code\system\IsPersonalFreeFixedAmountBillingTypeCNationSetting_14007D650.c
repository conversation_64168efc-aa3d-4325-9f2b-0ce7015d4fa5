/*
 * Function: ?IsPersonalFreeFixedAmountBillingType@CNationSettingManager@@QEAA_NPEAF0@Z
 * Address: 0x14007D650
 */

int __fastcall CNationSettingManager::IsPersonalFreeFixedAmountBillingType(CNationSettingManager *this, __int16 *pDest1, __int16 *pDest2)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CNationSettingManager *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return ((int (__fastcall *)(CNationSettingData *))v7->m_pData->vfptr->IsPersonalFreeFixedAmountBillingType)(v7->m_pData);
}
