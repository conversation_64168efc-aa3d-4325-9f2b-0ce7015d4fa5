/*
 * Function: ?ct_set_guildbattle_color@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292E30
 */

char __fastcall ct_set_guildbattle_color(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-108h]@1
  int v5; // [sp+40h] [bp-C8h]@8
  char Dest; // [sp+60h] [bp-A8h]@9
  unsigned __int64 v7; // [sp+F0h] [bp-18h]@4
  CPlayer *v8; // [sp+110h] [bp+8h]@1

  v8 = pOne;
  v1 = &v4;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v8 )
  {
    if ( s_nWordCount == 1 )
    {
      v5 = atoi(s_pwszDstCheat[0]);
      if ( v5 == -1 )
      {
        v8->m_bInGuildBattle = 0;
        v8->m_byGuildBattleColorInx = -1;
        sprintf(&Dest, "Clear GuildBattle Color!");
      }
      else if ( v5 && v5 != 1 )
      {
        sprintf(&Dest, "Invalid Color Inx! ( 0 or 1 or -1 )");
      }
      else
      {
        v8->m_bInGuildBattle = 1;
        v8->m_byGuildBattleColorInx = v5;
        sprintf(&Dest, "Set GuildBattle Color : %d", (unsigned int)v5);
      }
      CPlayer::SendData_ChatTrans(v8, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
