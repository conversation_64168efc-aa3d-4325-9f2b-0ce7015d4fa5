/*
 * Function: ?Is<PERSON><PERSON><PERSON>@_ATTACK_DELAY_CHECKER@@QEAA_NEGE@Z
 * Address: 0x14008EC60
 */

char __fastcall _ATTACK_DELAY_CHECKER::IsDelay(_ATTACK_DELAY_CHECKER *this, char code, unsigned __int16 index, char mastery)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  _ATTACK_DELAY_CHECKER *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8->m_nNextAddTime = 0;
  if ( _ATTACK_DELAY_CHECKER::_delay_check(v8, code, index, mastery) )
  {
    result = 1;
  }
  else
  {
    ++v8->nFailCount;
    result = 0;
  }
  return result;
}
