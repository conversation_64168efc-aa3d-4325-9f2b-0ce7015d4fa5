/*
 * Function: ?ct_elect_set_player@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298E90
 */

char __fastcall ct_elect_set_player(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@8
  unsigned __int16 v6; // [sp+24h] [bp-14h]@8
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    if ( s_nWordCount < 2 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[0]);
      v6 = atoi(s_pwszDstCheat[1]);
      v7->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime = v5;
      v7->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt = v6;
      v7->m_pUserDB->m_AvatorData.dbSupplement.dwScanerGetDate = 199001011;
      CPlayer::PushDQSUpdatePlyerVoteInfo(v7);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
