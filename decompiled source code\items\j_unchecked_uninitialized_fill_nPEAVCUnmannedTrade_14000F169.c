/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderItemCodeInfo@@_KV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderItemCodeInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@Z
 * Address: 0x14000F169
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderItemCodeInfo *,unsigned __int64,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(CUnmannedTraderItemCodeInfo *_First, unsigned __int64 _Count, CUnmannedTraderItemCodeInfo *_Val, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderItemCodeInfo *,unsigned __int64,CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
