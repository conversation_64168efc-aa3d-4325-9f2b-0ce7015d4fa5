/*
 * Function: ?IsStorageCodeWithItemKind@@YAHHH@Z
 * Address: 0x14003BD10
 */

signed __int64 __fastcall IsStorageCodeWithItemKind(int nTableCode, int nStorageCode)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  int v6; // [sp+20h] [bp+8h]@1

  v6 = nTableCode;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = nStorageCode;
  if ( nStorageCode == 1 )
  {
    if ( v6 >= 8 )
      return 0i64;
  }
  else if ( v5 == 2 )
  {
    if ( v6 != 8 && v6 != 9 && v6 != 10 )
      return 0i64;
  }
  else if ( v5 == 3 )
  {
    if ( v6 != 15 )
      return 0i64;
  }
  else if ( v5 == 4 && v6 != 24 )
  {
    return 0i64;
  }
  return 1i64;
}
