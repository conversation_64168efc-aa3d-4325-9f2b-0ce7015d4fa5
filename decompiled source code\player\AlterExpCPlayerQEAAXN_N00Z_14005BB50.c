/*
 * Function: ?AlterExp@CPlayer@@QEAAXN_N00@Z
 * Address: 0x14005BB50
 */

void __usercall CPlayer::AlterExp(CPlayer *this@<rcx>, long double dAlterExp@<xmm1>, bool b<PERSON><PERSON>ard@<r8b>, bool bUseExpRecoverItem@<r9b>, double a5@<xmm0>, bool bUseExpAdditionItem)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@6
  int v9; // eax@7
  double v10; // xmm0_8@19
  float v11; // xmm0_4@23
  float v12; // xmm0_4@23
  float v13; // xmm0_4@23
  float v14; // xmm0_4@25
  float v15; // xmm0_4@29
  float v16; // xmm0_4@33
  double v17; // xmm0_8@44
  cStaticMember_Player *v18; // rax@44
  int v19; // eax@47
  cStaticMember_Player *v20; // rax@49
  long double v21; // xmm0_8@49
  cStaticMember_Player *v22; // rax@49
  cStaticMember_Player *v23; // rax@52
  __int64 v24; // [sp-20h] [bp-98h]@1
  double v25; // [sp+0h] [bp-78h]@4
  double v26; // [sp+8h] [bp-70h]@4
  float v27; // [sp+10h] [bp-68h]@23
  long double v28; // [sp+18h] [bp-60h]@44
  double v29; // [sp+20h] [bp-58h]@44
  unsigned __int8 v30; // [sp+28h] [bp-50h]@45
  unsigned __int8 v31; // [sp+29h] [bp-4Fh]@45
  int v32; // [sp+2Ch] [bp-4Ch]@47
  int v33; // [sp+30h] [bp-48h]@6
  int v34; // [sp+34h] [bp-44h]@7
  float v35; // [sp+38h] [bp-40h]@12
  float v36; // [sp+3Ch] [bp-3Ch]@16
  float v37; // [sp+40h] [bp-38h]@25
  float v38; // [sp+44h] [bp-34h]@29
  int lv; // [sp+48h] [bp-30h]@44
  int v40; // [sp+4Ch] [bp-2Ch]@49
  double v41; // [sp+50h] [bp-28h]@49
  int v42; // [sp+58h] [bp-20h]@49
  double v43; // [sp+60h] [bp-18h]@52
  int v44; // [sp+68h] [bp-10h]@52
  CPlayer *v45; // [sp+80h] [bp+8h]@1
  double v46; // [sp+88h] [bp+10h]@14
  double v47; // [sp+88h] [bp+10h]@37
  double v48; // [sp+88h] [bp+10h]@42
  double v49; // [sp+88h] [bp+10h]@44
  bool v50; // [sp+90h] [bp+18h]@1
  bool v51; // [sp+98h] [bp+20h]@1

  v51 = bUseExpRecoverItem;
  v50 = bReward;
  v45 = this;
  v6 = &v24;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v45->m_id.wIndex);
  v25 = a5;
  v26 = dAlterExp;
  if ( v45->m_byUserDgr && v45->m_byUserDgr != 1
    || (v33 = CPlayerDB::GetLevel(&v45->m_Param), v8 = CPlayerDB::GetMaxLevel(&v45->m_Param), v33 < v8) )
  {
    v34 = CPlayerDB::GetLevel(&v45->m_Param);
    v9 = CPlayerDB::GetMaxLevel(&v45->m_Param);
    if ( v34 < v9 && dAlterExp != 0.0 )
    {
      if ( !CPlayer::IsApplyPcbangPrimium(v45) || v50 )
      {
        if ( v26 < 0.0 )
          v36 = PLAYER_LOST_EXP;
        else
          v36 = PLAYER_EXP_RATE;
        v46 = dAlterExp + v26 * (float)(v36 - 1.0);
      }
      else
      {
        if ( v26 < 0.0 )
          v35 = PLAYER_LOST_EXP;
        else
          v35 = PCBANG_PRIMIUM_FAVOR::PLAYER_EXP;
        v46 = dAlterExp + v26 * (float)(v35 - 1.0);
      }
      LODWORD(v10) = LODWORD(v46);
      if ( v46 > 0.0 && !v51 && !v50 && !v45->m_EP.m_bLock )
      {
        v27 = 0.0;
        _effect_parameter::GetEff_Have(&v45->m_EP, 2);
        v27 = v27 + (float)(0.0 - 1.0);
        v11 = v27;
        _effect_parameter::GetEff_Have(&v45->m_EP, 5);
        v12 = v27 + (float)(v11 - 1.0);
        v27 = v12;
        _effect_parameter::GetEff_Have(&v45->m_EP, 9);
        v13 = v27 + (float)(v12 - 1.0);
        v27 = v13;
        if ( CPlayer::IsApplyPcbangPrimium(v45) )
        {
          if ( v26 < 0.0 )
          {
            v14 = PLAYER_LOST_EXP;
            v37 = PLAYER_LOST_EXP;
          }
          else
          {
            v14 = PCBANG_PRIMIUM_FAVOR::PLAYER_EXP;
            v37 = PCBANG_PRIMIUM_FAVOR::PLAYER_EXP;
          }
          _effect_parameter::GetEff_Have(&v45->m_EP, 71);
          if ( v14 > v37 )
          {
            if ( v26 < 0.0 )
            {
              v15 = PLAYER_LOST_EXP;
              v38 = PLAYER_LOST_EXP;
            }
            else
            {
              v15 = PCBANG_PRIMIUM_FAVOR::PLAYER_EXP;
              v38 = PCBANG_PRIMIUM_FAVOR::PLAYER_EXP;
            }
            _effect_parameter::GetEff_Have(&v45->m_EP, 71);
            v14 = v27 + (float)(v15 - v38);
            v27 = v14;
          }
          _effect_parameter::GetEff_Have(&v45->m_EP, 72);
          v27 = v27 + (float)(v14 - 1.0);
        }
        else
        {
          _effect_parameter::GetEff_Have(&v45->m_EP, 71);
          v16 = v27 + (float)(v13 - 1.0);
          v27 = v16;
          _effect_parameter::GetEff_Have(&v45->m_EP, 72);
          v27 = v27 + (float)(v16 - 1.0);
        }
        *(float *)&v10 = v27;
        if ( v27 > 0.0 )
        {
          v10 = v46 + v26 * v27;
          v46 = v46 + v26 * v27;
        }
      }
      if ( v51 )
      {
        v47 = v26;
        CPlayerDB::SetLossExp(&v45->m_Param, 0.0);
        CUserDB::Update_LossExp(v45->m_pUserDB, 0.0);
      }
      else if ( bUseExpAdditionItem )
      {
        v47 = v26;
        if ( v26 < 0.0 )
        {
          CPlayerDB::SetLossExp(&v45->m_Param, -0.0 - v26);
          CUserDB::Update_LossExp(v45->m_pUserDB, -0.0 - v47);
        }
      }
      else
      {
        _effect_parameter::GetEff_Rate(&v45->m_EP, 34);
        v48 = v46 + v26 * (float)(*(float *)&v10 - 1.0);
        _effect_parameter::GetEff_Rate(&v45->m_EP, 38);
        v47 = v48 + v26 * (float)(*(float *)&v48 - 1.0);
        if ( v26 < 0.0 )
        {
          CPlayerDB::SetLossExp(&v45->m_Param, -0.0 - v47);
          CUserDB::Update_LossExp(v45->m_pUserDB, -0.0 - v47);
        }
      }
      v49 = v47 * v25;
      CPlayerDB::GetExp(&v45->m_Param);
      v28 = v49;
      CPlayerDB::GetExp(&v45->m_Param);
      v17 = v49 + v49;
      v29 = v49 + v49;
      CPlayer::SendMsg_NotifyGetExpInfo(v45, v28, v49, v49 + v49);
      lv = CPlayerDB::GetLevel(&v45->m_Param);
      v18 = cStaticMember_Player::Instance();
      cStaticMember_Player::GetLimitExp(v18, lv);
      if ( v29 < v49 + v49 )
      {
        CPlayerDB::SetExp(&v45->m_Param, v29);
        CPlayerDB::GetExp(&v45->m_Param);
        v43 = v49 + v49;
        v44 = CPlayerDB::GetLevel(&v45->m_Param);
        v23 = cStaticMember_Player::Instance();
        cStaticMember_Player::GetLimitExp(v23, v44);
        v17 = v43 / v17 * 1000000.0;
        v45->m_dwExpRate = (signed int)floor(v17);
        CPlayer::SendMsg_AlterExpInform(v45);
      }
      else
      {
        v30 = ((int (__fastcall *)(CPlayer *))v45->vfptr->GetLevel)(v45);
        v31 = v30 + 1;
        CPlayer::SetLevel(v45, v30 + 1);
        if ( v50 )
        {
          v32 = CPlayerDB::GetMaxLevel(&v45->m_Param);
          v19 = CPlayerDB::GetLevel(&v45->m_Param);
          if ( v32 > v19 )
          {
            v40 = v30;
            v20 = cStaticMember_Player::Instance();
            cStaticMember_Player::GetLimitExp(v20, v40);
            v21 = v29 - v17;
            CPlayerDB::SetExp(&v45->m_Param, v21);
            CPlayerDB::GetExp(&v45->m_Param);
            v41 = v21;
            v42 = v31;
            v22 = cStaticMember_Player::Instance();
            cStaticMember_Player::GetLimitExp(v22, v42);
            v17 = v41 / v21 * 1000000.0;
            v45->m_dwExpRate = (signed int)floor(v17);
          }
          else
          {
            CPlayerDB::SetExp(&v45->m_Param, 0.0);
            v45->m_dwExpRate = 0;
          }
          CPlayer::SendMsg_AlterExpInform(v45);
        }
        else
        {
          CPlayerDB::SetExp(&v45->m_Param, 0.0);
          v45->m_dwExpRate = 0;
          CPlayer::SendMsg_AlterExpInform(v45);
        }
        v45->m_bDownCheckEquipEffect = 1;
        CPlayer::SendMsg_EquipItemLevelLimit(v45, v31);
      }
      if ( v45->m_pUserDB )
      {
        CPlayerDB::GetExp(&v45->m_Param);
        CUserDB::Update_Exp(v45->m_pUserDB, v17);
      }
    }
  }
}
