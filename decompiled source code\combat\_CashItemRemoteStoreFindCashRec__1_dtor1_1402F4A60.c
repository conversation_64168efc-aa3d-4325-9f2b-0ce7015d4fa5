/*
 * Function: _CashItemRemoteStore::FindCashRec_::_1_::dtor$1
 * Address: 0x1402F4A60
 */

void __fastcall CashItemRemoteStore::FindCashRec_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *)(a2 + 72));
}
