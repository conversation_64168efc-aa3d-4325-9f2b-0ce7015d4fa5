/*
 * Function: ?SF_OthersContHelpSFRemove_Once@CPlayer@@UEAA_NM@Z
 * Address: 0x14009EBA0
 */

bool __usercall CPlayer::SF_OthersContHelpSFRemove_Once@<al>(CPlayer *this@<rcx>, float fEffectValue@<xmm1>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  _sec_info *v6; // rax@9
  __int64 v8; // [sp+0h] [bp-98h]@1
  int v9; // [sp+30h] [bp-68h]@4
  _pnt_rect pRect; // [sp+48h] [bp-50h]@4
  int j; // [sp+64h] [bp-34h]@4
  int k; // [sp+68h] [bp-30h]@6
  unsigned int dwSecIndex; // [sp+6Ch] [bp-2Ch]@9
  _object_list_point *v14; // [sp+70h] [bp-28h]@10
  CCharacter *v15; // [sp+78h] [bp-20h]@12
  CObjectList *v16; // [sp+80h] [bp-18h]@9
  int v17; // [sp+88h] [bp-10h]@15
  int l; // [sp+8Ch] [bp-Ch]@16
  CPlayer *v19; // [sp+A0h] [bp+8h]@1

  v19 = this;
  v3 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  v5 = CGameObject::GetCurSecNum((CGameObject *)&v19->vfptr);
  CMapData::GetRectInRadius(v19->m_pCurMap, &pRect, 2, v5);
  for ( j = pRect.nStarty; j < pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k < pRect.nEndx; ++k )
    {
      v6 = CMapData::GetSecInfo(v19->m_pCurMap);
      dwSecIndex = v6->m_nSecNumW * j + k;
      v16 = CMapData::GetSectorListObj(v19->m_pCurMap, v19->m_wMapLayerIndex, dwSecIndex);
      if ( v16 )
      {
        v14 = v16->m_Head.m_pNext;
        while ( v14 != &v16->m_Tail )
        {
          v15 = (CCharacter *)v14->m_pItem;
          v14 = v14->m_pNext;
          if ( (CPlayer *)v15 != v19 && !v15->m_ObjID.m_byKind && !v15->m_ObjID.m_byID )
          {
            GetSqrt(v19->m_fCurPos, v15->m_fCurPos);
            v17 = (signed int)ffloor(a3);
            a3 = (float)v17;
            if ( (float)v17 <= fEffectValue )
            {
              for ( l = 0; l < 8; ++l )
              {
                if ( v15->m_SFCont[1][l].m_bExist )
                {
                  CCharacter::RemoveSFContEffect(v15, 1, l, 0, 0);
                  ++v9;
                }
              }
              CCharacter::BreakStealth(v15);
            }
          }
        }
      }
    }
  }
  return v9 > 0;
}
