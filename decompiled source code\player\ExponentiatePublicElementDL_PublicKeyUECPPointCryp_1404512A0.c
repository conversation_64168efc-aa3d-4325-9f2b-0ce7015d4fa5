/*
 * Function: ?ExponentiatePublicElement@?$DL_PublicKey@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@@Z
 * Address: 0x1404512A0
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::ExponentiatePublicElement(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *exponent)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@4
  __int64 v6; // rax@4
  __int64 v7; // rax@4
  __int64 v9; // [sp+0h] [bp-48h]@1
  __int64 v10; // [sp+20h] [bp-28h]@4
  int v11; // [sp+28h] [bp-20h]@4
  __int64 v12; // [sp+30h] [bp-18h]@4
  CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *v13; // [sp+50h] [bp+8h]@1
  CryptoPP::ECPPoint *v14; // [sp+58h] [bp+10h]@1
  CryptoPP::Integer *v15; // [sp+60h] [bp+18h]@1

  v15 = exponent;
  v14 = result;
  v13 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  LODWORD(v5) = ((int (__fastcall *)(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *))v13->vfptr->GetAbstractGroupParameters)(v13);
  v10 = v5;
  LODWORD(v6) = ((int (__fastcall *)(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *))v13->vfptr[3].GetAbstractGroupParameters)(v13);
  v12 = v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v10 + 40i64))(v10);
  (*(void (__fastcall **)(__int64, CryptoPP::ECPPoint *, __int64, CryptoPP::Integer *))(*(_QWORD *)v12 + 48i64))(
    v12,
    v14,
    v7,
    v15);
  return v14;
}
