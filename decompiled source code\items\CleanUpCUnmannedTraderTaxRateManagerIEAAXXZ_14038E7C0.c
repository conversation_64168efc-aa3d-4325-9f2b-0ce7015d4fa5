/*
 * Function: ?CleanUp@CUnmannedTraderTaxRateManager@@IEAAXXZ
 * Address: 0x14038E7C0
 */

void __fastcall CUnmannedTraderTaxRateManager::CleanUp(CUnmannedTraderTaxRateManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@6
  unsigned __int64 v4; // rax@11
  __int64 v5; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@10
  CMyTimer *v7; // [sp+28h] [bp-40h]@5
  CMyTimer *v8; // [sp+30h] [bp-38h]@5
  TRC_AutoTrade *v9; // [sp+38h] [bp-30h]@13
  TRC_AutoTrade *v10; // [sp+40h] [bp-28h]@13
  __int64 v11; // [sp+48h] [bp-20h]@6
  unsigned __int64 v12; // [sp+50h] [bp-18h]@11
  void *v13; // [sp+58h] [bp-10h]@14
  CUnmannedTraderTaxRateManager *v14; // [sp+70h] [bp+8h]@1

  v14 = this;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v14->m_pkTimer )
  {
    v8 = v14->m_pkTimer;
    v7 = v8;
    if ( v8 )
    {
      LODWORD(v3) = ((int (__fastcall *)(CMyTimer *, signed __int64))v7->vfptr->__vecDelDtor)(v7, 1i64);
      v11 = v3;
    }
    else
    {
      v11 = 0i64;
    }
    v14->m_pkTimer = 0i64;
  }
  if ( !std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v14->m_vecTRC) )
  {
    for ( j = 0; ; ++j )
    {
      v12 = j;
      v4 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v14->m_vecTRC);
      if ( v12 >= v4 )
        break;
      if ( *std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v14->m_vecTRC, j) )
      {
        v10 = *std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v14->m_vecTRC, j);
        v9 = v10;
        if ( v10 )
          v13 = TRC_AutoTrade::`scalar deleting destructor'(v9, 1u);
        else
          v13 = 0i64;
      }
    }
    std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::clear(&v14->m_vecTRC);
  }
}
