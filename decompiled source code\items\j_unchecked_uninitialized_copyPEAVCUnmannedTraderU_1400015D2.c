/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCUnmannedTraderUserInfo@@PEAV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderUserInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderUserInfo@@@std@@@Z
 * Address: 0x1400015D2
 */

CUnmannedTraderUserInfo *__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Dest, std::allocator<CUnmannedTraderUserInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
