/*
 * Function: ?SendOhterNotifyCommitteeMemberPosition@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403E22D0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendOhterNotifyCommitteeMemberPosition(GUILD_BATTLE::CNormalGuildBattleGuild *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  unsigned int dwSerial; // [sp+20h] [bp-78h]@5
  char pMsg[2]; // [sp+38h] [bp-60h]@5
  __int16 v7; // [sp+3Ah] [bp-5Eh]@5
  char v8; // [sp+3Ch] [bp-5Ch]@5
  unsigned int v9; // [sp+3Dh] [bp-5Bh]@5
  char byType; // [sp+64h] [bp-34h]@5
  char v11; // [sp+65h] [bp-33h]@5
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( pkPlayer )
  {
    v9 = pkPlayer->m_dwObjSerial;
    v8 = v13->m_byColorInx;
    *(_WORD *)pMsg = (signed int)ffloor(pkPlayer->m_fCurPos[0]);
    v7 = (signed int)ffloor(pkPlayer->m_fCurPos[2]);
    byType = 27;
    v11 = 82;
    dwSerial = pkPlayer->m_dwObjSerial;
    GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(v13, &byType, pMsg, 9u, dwSerial);
  }
}
