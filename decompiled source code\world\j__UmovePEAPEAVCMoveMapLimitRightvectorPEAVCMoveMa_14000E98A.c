/*
 * Function: j_??$_Umove@PEAPEAVCMoveMapLimitRight@@@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAPEAPEAVCMoveMapLimitRight@@PEAPEAV2@00@Z
 * Address: 0x14000E98A
 */

CMoveMapLimitRight **__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Umove<CMoveMapLimitRight * *>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Ptr)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Umove<CMoveMapLimitRight * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
