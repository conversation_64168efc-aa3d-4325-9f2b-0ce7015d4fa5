/*
 * Function: ?CloseSocket@CNetWorking@@QEAAXKK_N@Z
 * Address: 0x140481AE0
 */

void __fastcall CNetWorking::CloseSocket(CNetWorking *this, unsigned int dwProID, unsigned int dwSocketIndex, bool bSlowClose)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _socket *v7; // [sp+20h] [bp-18h]@6
  CNetWorking *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1
  int nIndex; // [sp+50h] [bp+18h]@1

  nIndex = dwSocketIndex;
  v9 = dwProID;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( bSlowClose )
  {
    v7 = CNetSocket::GetSocket(&v8->m_Process[dwProID].m_NetSocket, dwSocketIndex);
    if ( !_FORCE_CLOSE::PushNode(&v8->m_Process[v9].m_FC, nIndex, v7->m_dwSerial) )
      CNetProcess::PushCloseNode(&v8->m_Process[v9], nIndex);
  }
  else
  {
    CNetProcess::PushCloseNode(&v8->m_Process[dwProID], dwSocketIndex);
  }
}
