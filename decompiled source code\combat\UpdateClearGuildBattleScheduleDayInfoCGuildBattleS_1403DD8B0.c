/*
 * Function: ?UpdateClearGuildBattleScheduleDayInfo@CGuildBattleScheduler@GUILD_BATTLE@@QEAA_NKK@Z
 * Address: 0x1403DD8B0
 */

char __fastcall GUILD_BATTLE::CGuildBattleScheduler::UpdateClearGuildBattleScheduleDayInfo(GUILD_BATTLE::CGuildBattleScheduler *this, unsigned int dwStartSLID, unsigned int dwEndSLID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned int uiStartListID; // [sp+38h] [bp+10h]@1
  unsigned int uiEndListID; // [sp+40h] [bp+18h]@1

  uiEndListID = dwEndSLID;
  uiStartListID = dwStartSLID;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CRFWorldDatabase::UpdateClearGuildBattleScheduleInfo(pkDB, dwStartSLID, dwEndSLID) )
  {
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C9380,
      "CGuildBattleReservedScheduleMapGroup::UpdateClearGuildBattleScheduleDayInfo() : g_Main.m_pWorldDB->UpdateClearGuil"
      "dBattleScheduleInfo( %u, %u ) Fail!",
      uiStartListID,
      uiEndListID);
    result = 0;
  }
  return result;
}
