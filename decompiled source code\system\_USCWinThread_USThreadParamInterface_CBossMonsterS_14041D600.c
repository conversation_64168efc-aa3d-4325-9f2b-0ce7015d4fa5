/*
 * Function: _US::CWinThread_US::ThreadParamInterface_CBossMonsterScheduleSystem_US::AbstractThreadPool___::CWinThread_US::ThreadParamInterface_CBossMonsterScheduleSystem_US::AbstractThreadPool____::_1_::dtor$0
 * Address: 0x14041D600
 */

void __fastcall US::CWinThread_US::ThreadParamInterface_CBossMonsterScheduleSystem_US::AbstractThreadPool___::CWinThread_US::ThreadParamInterface_CBossMonsterScheduleSystem_US::AbstractThreadPool____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  US::AbstractThread::~AbstractThread(*(US::AbstractThread **)(a2 + 64));
}
