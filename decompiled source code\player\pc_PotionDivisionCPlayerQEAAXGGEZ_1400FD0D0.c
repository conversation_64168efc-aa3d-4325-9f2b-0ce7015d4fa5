/*
 * Function: ?pc_PotionDivision@CPlayer@@QEAAXGGE@Z
 * Address: 0x1400FD0D0
 */

void __fastcall CPlayer::pc_PotionDivision(CPlayer *this, unsigned __int16 wSerial, unsigned __int16 wTarSerial, char byAmount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  bool bUpdate; // [sp+20h] [bp-38h]@22
  bool bSend[4]; // [sp+28h] [bp-30h]@22
  char v9; // [sp+30h] [bp-28h]@4
  _STORAGE_LIST::_db_con *v10; // [sp+38h] [bp-20h]@4
  _STORAGE_LIST::_db_con *v11; // [sp+40h] [bp-18h]@4
  char v12; // [sp+48h] [bp-10h]@22
  char v13; // [sp+49h] [bp-Fh]@22
  CPlayer *v14; // [sp+60h] [bp+8h]@1
  unsigned __int16 v15; // [sp+68h] [bp+10h]@1
  unsigned __int16 v16; // [sp+70h] [bp+18h]@1
  char v17; // [sp+78h] [bp+20h]@1

  v17 = byAmount;
  v16 = wTarSerial;
  v15 = wSerial;
  v14 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  v10 = 0i64;
  v11 = 0i64;
  v10 = _STORAGE_LIST::GetPtrFromSerial(v14->m_Param.m_pStoragePtr[0], wSerial);
  if ( v10 )
  {
    if ( v10->m_bLock )
    {
      v9 = -11;
    }
    else
    {
      v11 = _STORAGE_LIST::GetPtrFromSerial(v14->m_Param.m_pStoragePtr[0], v16);
      if ( v11 )
      {
        if ( v11->m_bLock )
        {
          v9 = -11;
        }
        else if ( v10->m_byTableCode == 13 && v11->m_byTableCode == 13 )
        {
          if ( v10->m_wItemIndex == v11->m_wItemIndex )
          {
            if ( v10->m_dwDur >= (unsigned __int8)v17 )
            {
              if ( v11->m_dwDur + (unsigned __int8)v17 > 0x63 )
                v9 = -4;
            }
            else
            {
              v9 = -4;
            }
          }
          else
          {
            v9 = -6;
          }
        }
        else
        {
          v9 = -3;
        }
      }
      else
      {
        v9 = -5;
      }
    }
  }
  else
  {
    v9 = -5;
  }
  if ( !v9 )
  {
    bSend[0] = 0;
    bUpdate = 0;
    v12 = CPlayer::Emb_AlterDurPoint(v14, 0, v10->m_byStorageIndex, -(unsigned __int8)v17, 0, 0);
    bSend[0] = 0;
    bUpdate = 0;
    v13 = CPlayer::Emb_AlterDurPoint(v14, 0, v11->m_byStorageIndex, (unsigned __int8)v17, 0, 0);
  }
  CPlayer::SendMsg_PotionDivision(v14, v15, v12, v16, v13, v9);
}
