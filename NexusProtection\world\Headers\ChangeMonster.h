#pragma once

#include <string>
#include <memory>
#include <cstdint>

namespace NexusProtection::World {

    /**
     * @brief Change Monster Configuration
     * 
     * Represents configuration data for monster transformation/change operations.
     * This class manages mission description codes and completion messages for
     * monster change events in the game world.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - 0__change_monsterQEAAXZ_14027A4D0.c (constructor)
     * - 1__change_monsterQEAAXZ_140272E60.c (destructor)
     */
    class ChangeMonster {
    public:
        // Constructor and destructor
        ChangeMonster();
        explicit ChangeMonster(const std::string& missionCode, const std::string& completeMsg = "");
        ~ChangeMonster() = default;

        // Copy and move semantics
        ChangeMonster(const ChangeMonster& other);
        ChangeMonster& operator=(const ChangeMonster& other);
        ChangeMonster(ChangeMonster&& other) noexcept;
        ChangeMonster& operator=(ChangeMonster&& other) noexcept;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsValid() const;

        // Mission description code management
        void SetMissionDescriptionCode(const std::string& code);
        const std::string& GetMissionDescriptionCode() const { return m_missionDescriptionCode; }
        bool HasMissionDescriptionCode() const { return !m_missionDescriptionCode.empty(); }

        // Completion message management
        void SetCompletionMessage(const std::string& message);
        const std::string& GetCompletionMessage() const { return m_completionMessage; }
        bool HasCompletionMessage() const { return !m_completionMessage.empty(); }

        // Configuration management
        void SetConfiguration(const std::string& missionCode, const std::string& completeMsg);
        void ClearConfiguration();

        // Utility methods
        std::string ToString() const;
        size_t GetMemoryUsage() const;

        // Legacy C interface compatibility
        const char* GetMissionDescriptionCodeCStr() const;
        const char* GetCompletionMessageCStr() const;
        void SetMissionDescriptionCodeFromCStr(const char* code);
        void SetCompletionMessageFromCStr(const char* message);

        // Validation
        bool ValidateMissionCode() const;
        bool ValidateCompletionMessage() const;

    private:
        // Member variables
        std::string m_missionDescriptionCode;   // Mission description code (originally pszIfMissionDescirptCode)
        std::string m_completionMessage;        // Completion message (originally pszifCompleteMsg)
        
        // Internal state
        bool m_isInitialized{false};
        
        // Configuration limits
        static constexpr size_t MAX_MISSION_CODE_LENGTH = 256;
        static constexpr size_t MAX_COMPLETION_MESSAGE_LENGTH = 512;
        
        // Internal methods
        void ValidateAndTruncate();
        bool IsValidString(const std::string& str, size_t maxLength) const;
    };

    /**
     * @brief Change Monster Factory
     * 
     * Factory class for creating ChangeMonster instances with proper configuration.
     */
    class ChangeMonsterFactory {
    public:
        static std::unique_ptr<ChangeMonster> CreateChangeMonster();
        static std::unique_ptr<ChangeMonster> CreateChangeMonster(const std::string& missionCode);
        static std::unique_ptr<ChangeMonster> CreateChangeMonster(const std::string& missionCode, 
                                                                 const std::string& completeMsg);
        
        // Batch creation
        static std::vector<std::unique_ptr<ChangeMonster>> CreateChangeMonsters(
            const std::vector<std::pair<std::string, std::string>>& configurations);
    };

    /**
     * @brief Change Monster Utilities
     * 
     * Utility functions for ChangeMonster management and operations.
     */
    namespace ChangeMonsterUtils {
        // Validation utilities
        bool ValidateChangeMonster(const ChangeMonster& changeMonster);
        bool ValidateMissionCode(const std::string& code);
        bool ValidateCompletionMessage(const std::string& message);
        
        // String utilities
        std::string SanitizeMissionCode(const std::string& code);
        std::string SanitizeCompletionMessage(const std::string& message);
        
        // Memory utilities
        size_t CalculateMemoryFootprint(const ChangeMonster& changeMonster);
        
        // Conversion utilities
        std::string ChangeMonsterToJson(const ChangeMonster& changeMonster);
        std::unique_ptr<ChangeMonster> ChangeMonsterFromJson(const std::string& json);
    }

    // Legacy C interface
    extern "C" {
        // Legacy structure for compatibility
        struct _change_monster {
            char* pszIfMissionDescirptCode;     // Mission description code pointer
            char* pszifCompleteMsg;             // Completion message pointer
        };

        // Legacy function declarations
        void __change_monster_Constructor(_change_monster* this_ptr);
        void __change_monster_Destructor(_change_monster* this_ptr);
        
        // Legacy utility functions
        void __change_monster_SetMissionCode(_change_monster* this_ptr, const char* code);
        void __change_monster_SetCompleteMsg(_change_monster* this_ptr, const char* message);
        const char* __change_monster_GetMissionCode(_change_monster* this_ptr);
        const char* __change_monster_GetCompleteMsg(_change_monster* this_ptr);
    }

} // namespace NexusProtection::World

// Legacy global compatibility
extern NexusProtection::World::ChangeMonster* g_pChangeMonster;
