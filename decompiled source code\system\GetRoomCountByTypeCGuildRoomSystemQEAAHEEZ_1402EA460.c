/*
 * Function: ?GetRoomCountByType@CGuildRoomSystem@@QEAAHEE@Z
 * Address: 0x1402EA460
 */

__int64 __fastcall CGuildRoomSystem::GetRoomCountByType(CGuildRoomSystem *this, char byRace, char byRoomType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v5; // rax@7
  CGuildRoomInfo *v6; // rax@8
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned int v9; // [sp+20h] [bp-18h]@4
  int v10; // [sp+24h] [bp-14h]@4
  int v11; // [sp+28h] [bp-10h]@4
  CGuildRoomSystem *v12; // [sp+40h] [bp+8h]@1
  char v13; // [sp+50h] [bp+18h]@1

  v13 = byRoomType;
  v12 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  v10 = 30 * (unsigned __int8)byRace;
  v11 = 30 * (unsigned __int8)byRace + 30;
  while ( v10 < v11 )
  {
    v5 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v12->m_vecGuildRoom, v10);
    if ( CGuildRoomInfo::IsRent(v5) )
    {
      v6 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v12->m_vecGuildRoom, v10);
      if ( (unsigned __int8)CGuildRoomInfo::GetRoomType(v6) == (unsigned __int8)v13 )
        ++v9;
    }
    ++v10;
  }
  return v9;
}
