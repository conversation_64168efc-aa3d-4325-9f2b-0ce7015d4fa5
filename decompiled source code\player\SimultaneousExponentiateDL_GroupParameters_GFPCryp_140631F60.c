/*
 * Function: ?SimultaneousExponentiate@DL_GroupParameters_GFP@CryptoPP@@UEBAXPEAVInteger@2@AEBV32@PEBV32@I@Z
 * Address: 0x140631F60
 */

void __fastcall CryptoPP::DL_GroupParameters_GFP::SimultaneousExponentiate(CryptoPP::DL_GroupParameters_GFP *this, struct CryptoPP::Integer *a2, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, unsigned int a5)
{
  CryptoPP::Integer *v5; // rax@1
  CryptoPP::ModularArithmetic v6; // [sp+30h] [bp-A8h]@1
  __int64 v7; // [sp+C0h] [bp-18h]@1
  __int64 v8; // [sp+C8h] [bp-10h]@1
  struct CryptoPP::Integer *v9; // [sp+E8h] [bp+10h]@1
  struct CryptoPP::Integer *v10; // [sp+F0h] [bp+18h]@1
  struct CryptoPP::Integer *v11; // [sp+F8h] [bp+20h]@1

  v11 = (struct CryptoPP::Integer *)a4;
  v10 = (struct CryptoPP::Integer *)a3;
  v9 = a2;
  v7 = -2i64;
  v8 = *(_QWORD *)&this[-1].gapE0[8];
  LODWORD(v5) = (*(int (__fastcall **)(_BYTE *))(v8 + 32))(&this[-1].gapE0[8]);
  CryptoPP::ModularArithmetic::ModularArithmetic(&v6, v5);
  CryptoPP::ModularArithmetic::SimultaneousExponentiate(&v6, v9, v10, v11, a5);
  CryptoPP::ModularArithmetic::~ModularArithmetic(&v6);
}
