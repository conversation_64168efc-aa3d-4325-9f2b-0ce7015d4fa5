/*
 * Function: ?CancelPlayerRaceBuff@CRaceBuffByHolyQuestProcedure@@QEAAHPEAVCPlayer@@W4RESULT_TYPE@CRaceBuffInfoByHolyQuestfGroup@@I@Z
 * Address: 0x1403B63D0
 */

signed __int64 __fastcall CRaceBuffByHolyQuestProcedure::CancelPlayerRaceBuff(CRaceBuffByHolyQuestProcedure *this, CPlayer *pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiReleaseLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  char v7; // al@7
  __int64 v8; // [sp+0h] [bp-38h]@1
  int iType; // [sp+20h] [bp-18h]@7
  unsigned int uiContinueCnt; // [sp+24h] [bp-14h]@11
  bool v11; // [sp+28h] [bp-10h]@7
  CRaceBuffByHolyQuestProcedure *v12; // [sp+40h] [bp+8h]@1
  CPlayer *pkDest; // [sp+48h] [bp+10h]@1
  CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE v14; // [sp+50h] [bp+18h]@1
  unsigned int v15; // [sp+58h] [bp+20h]@1

  v15 = uiReleaseLv;
  v14 = eReleaseType;
  pkDest = pkPlayer;
  v12 = this;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkPlayer && pkPlayer->m_bOper )
  {
    v11 = CPlayer::IsHaveMentalTicket(pkPlayer);
    v7 = CPlayerDB::GetRaceCode(&pkDest->m_Param);
    iType = CRaceBuffHolyQuestResultInfo::GetResultType(&v12->m_kBuffHolyQestResultInfo, v7, v11);
    if ( iType >= 0 )
    {
      if ( v14 == iType )
      {
        uiContinueCnt = CRaceBuffHolyQuestResultInfo::GetContinueCnt(&v12->m_kBuffHolyQestResultInfo, iType);
        if ( uiContinueCnt <= v15 )
        {
          if ( CRaceBuffInfoByHolyQuestList::Release(&v12->m_kBuffInfo, uiContinueCnt, iType, pkDest) )
            result = 0i64;
          else
            result = 4294967291i64;
        }
        else
        {
          result = 4294967292i64;
        }
      }
      else
      {
        result = 4294967293i64;
      }
    }
    else
    {
      result = 4294967294i64;
    }
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
