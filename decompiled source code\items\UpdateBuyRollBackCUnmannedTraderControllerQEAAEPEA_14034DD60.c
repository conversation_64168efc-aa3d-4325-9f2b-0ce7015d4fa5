/*
 * Function: ?UpdateBuyRollBack@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034DD60
 */

char __fastcall CUnmannedTraderController::UpdateBuyRollBack(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  unsigned int dwBuyer; // [sp+20h] [bp-68h]@6
  unsigned int dwTax; // [sp+28h] [bp-60h]@6
  _SYSTEMTIME *kCurTime; // [sp+30h] [bp-58h]@6
  char *v9; // [sp+40h] [bp-48h]@4
  char Dst; // [sp+58h] [bp-30h]@4
  int v11; // [sp+74h] [bp-14h]@4
  int j; // [sp+78h] [bp-10h]@4

  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = pData;
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  v11 = 0;
  for ( j = 0; j < (unsigned __int8)v9[10]; ++j )
  {
    v9[12 * j + 12] = 0;
    kCurTime = (_SYSTEMTIME *)&Dst;
    dwTax = 0;
    dwBuyer = 0;
    if ( !CRFWorldDatabase::Update_UnmannedTraderResutlInfo(
            pkDB,
            v9[9],
            *(_DWORD *)&v9[12 * j + 16],
            v9[12 * j + 20],
            0,
            0,
            (_SYSTEMTIME *)&Dst) )
      v9[12 * j + 12] = 36;
  }
  return 0;
}
