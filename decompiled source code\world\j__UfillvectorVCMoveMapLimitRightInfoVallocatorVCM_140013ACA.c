/*
 * Function: j_?_Ufill@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAAPEAVCMoveMapLimitRightInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x140013ACA
 */

CMoveMapLimitRightInfo *__fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Ufill(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, CMoveMapLimitRightInfo *_Ptr, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  return std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Ufill(this, _Ptr, _Count, _Val);
}
