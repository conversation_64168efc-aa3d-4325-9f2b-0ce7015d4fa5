/*
 * Function: ?PushDQSUpdateVoteAvilable@CPlayer@@QEAAXXZ
 * Address: 0x140069900
 */

void __fastcall CPlayer::PushDQSUpdateVoteAvilable(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@4
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-88h]@1
  _qry_case_update_vote_available v6; // [sp+38h] [bp-50h]@4
  unsigned __int64 v7; // [sp+70h] [bp-18h]@4
  CPlayer *v8; // [sp+90h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  v6.byVoteEnable = 0;
  v6.dwAccountSerial = v8->m_pUserDB->m_dwAccountSerial;
  v6.dwCharSerial = CPlayerDB::GetCharSerial(&v8->m_Param);
  v3 = CPlayerDB::GetCharNameW(&v8->m_Param);
  strcpy_0(v6.wszCharName, v3);
  v4 = _qry_case_update_vote_available::size(&v6);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -107, &v6.byVoteEnable, v4);
}
