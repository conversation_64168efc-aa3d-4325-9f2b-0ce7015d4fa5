/*
 * Function: ?Select_BattleResultLogLatest@CRFWorldDatabase@@QEAAEPEAK@Z
 * Address: 0x1404B0DE0
 */

char __fastcall CRFWorldDatabase::Select_BattleResultLogLatest(CRFWorldDatabase *this, unsigned int *pkLogSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@22
  SQLLEN v8; // [sp+38h] [bp-450h]@22
  __int16 v9; // [sp+44h] [bp-444h]@9
  char Dest; // [sp+60h] [bp-428h]@4
  char v11; // [sp+464h] [bp-24h]@16
  unsigned __int64 v12; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+490h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+498h] [bp+10h]@1

  TargetValue = pkLogSerial;
  v13 = this;
  v2 = &v5;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  *pkLogSerial = 0;
  sprintf(&Dest, "{ CALL pSelect_guildbattleresulloglatest }");
  if ( v13->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &Dest);
  if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
  {
    v9 = SQLExecDirectA_0(v13->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v13->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v9 = SQLFetch_0(v13->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v11 = 0;
        if ( v9 == 100 )
        {
          v11 = 2;
        }
        else
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
          v11 = 1;
        }
        if ( v13->m_hStmtSelect )
          SQLCloseCursor_0(v13->m_hStmtSelect);
        result = v11;
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v8);
        if ( v9 == 100 )
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          result = 2;
        }
        else
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          if ( v13->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
