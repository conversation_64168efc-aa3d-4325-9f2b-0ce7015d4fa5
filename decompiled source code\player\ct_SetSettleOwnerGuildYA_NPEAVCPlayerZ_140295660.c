/*
 * Function: ?ct_SetSettleOwnerGuild@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295660
 */

char __fastcall ct_SetSettleOwnerGuild(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  AutoMineMachineMng *v4; // rax@12
  AutoMineMachineMng *v5; // rax@13
  AutoMineMachine *v6; // rax@13
  AutoMineMachineMng *v7; // rax@17
  AutoMineMachine *v8; // rax@17
  __int64 v9; // [sp+0h] [bp-58h]@1
  int v10; // [sp+20h] [bp-38h]@7
  CGuild *pGuild; // [sp+28h] [bp-30h]@10
  CGuild *v12; // [sp+30h] [bp-28h]@13
  CGuild *v13; // [sp+38h] [bp-20h]@17
  int nRaceCode; // [sp+40h] [bp-18h]@12
  unsigned int dw2ThGuildSerial; // [sp+44h] [bp-14h]@14
  unsigned int dw1ThGuildSerial; // [sp+48h] [bp-10h]@18
  CPlayer *v17; // [sp+60h] [bp+8h]@1

  v17 = pOne;
  v1 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v17 && s_nWordCount >= 2 )
  {
    v10 = atoi(s_pwszDstCheat[0]);
    if ( v10 >= 0 && v10 < 2 )
    {
      pGuild = GetGuildPtrFromName(g_Guild, 500, s_pwszDstCheat[1]);
      if ( pGuild )
      {
        nRaceCode = pGuild->m_byRace;
        v4 = AutoMineMachineMng::Instance();
        AutoMineMachineMng::ChangeOwner(v4, nRaceCode, pGuild, v10);
        if ( v10 )
        {
          v7 = AutoMineMachineMng::Instance();
          v8 = AutoMineMachineMng::GetMachine(v7, pGuild->m_byRace, 0);
          v13 = AutoMineMachine::GetOwnerGuild(v8);
          if ( v13 )
            dw1ThGuildSerial = v13->m_dwSerial;
          else
            dw1ThGuildSerial = 0;
          CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateSettlementOwner(
            &stru_1799C9AF8,
            pGuild->m_byRace,
            dw1ThGuildSerial,
            pGuild->m_dwSerial);
        }
        else
        {
          v5 = AutoMineMachineMng::Instance();
          v6 = AutoMineMachineMng::GetMachine(v5, pGuild->m_byRace, 1);
          v12 = AutoMineMachine::GetOwnerGuild(v6);
          if ( v12 )
            dw2ThGuildSerial = v12->m_dwSerial;
          else
            dw2ThGuildSerial = 0;
          CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateSettlementOwner(
            &stru_1799C9AF8,
            pGuild->m_byRace,
            pGuild->m_dwSerial,
            dw2ThGuildSerial);
        }
        CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, pGuild->m_byRace);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
