/*
 * Function: ?AddGuildBattleSchdule@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F4170
 */

void __fastcall CMainThread::AddGuildBattleSchdule(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuildBattleController *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int dwSLID; // [sp+20h] [bp-28h]@4
  char *v7; // [sp+30h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v8; // [sp+58h] [bp+10h]@1

  v8 = pData;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData->m_sData;
  v4 = CGuildBattleController::Instance();
  dwSLID = *((_DWORD *)v7 + 5);
  CGuildBattleController::AddComplete(v4, v8->m_byResult, *((_DWORD *)v7 + 3), *(_DWORD *)v7, dwSLID);
}
