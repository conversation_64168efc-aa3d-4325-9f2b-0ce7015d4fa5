/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderUserInfo@@_KV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderUserInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderUserInfo@@@std@@@Z
 * Address: 0x140012968
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderUserInfo *,unsigned __int64,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, unsigned __int64 _Count, CUnmannedTraderUserInfo *_Val, std::allocator<CUnmannedTraderUserInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderUserInfo *,unsigned __int64,CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
