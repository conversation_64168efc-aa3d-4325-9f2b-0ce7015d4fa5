/*
 * Function: j_??0?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140008094
 */

void __fastcall std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__that)
{
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    this,
    __that);
}
