/*
 * Function: ??0CRecordData@@QEAA@XZ
 * Address: 0x14007F490
 */

void __fastcall CRecordData::CRecordData(CRecordData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CRecordData *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->vfptr = (CRecordDataVtbl *)&CRecordData::`vftable';
  _record_bin_header::_record_bin_header(&v4->m_Header);
  v4->m_bLoad = 0;
  v4->m_dwTotalSize = 0;
  v4->m_nLowNum = 0;
  v4->m_ppsRecord = 0i64;
  v4->m_pdwHashList = 0i64;
}
