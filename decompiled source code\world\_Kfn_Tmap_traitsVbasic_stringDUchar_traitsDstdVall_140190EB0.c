/*
 * Function: ?_Kfn@?$_Tmap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@@std@@@2@$0A@@std@@SAAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@2@AEBU?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@@2@@Z
 * Address: 0x140190EB0
 */

std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> *__fastcall std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>::_Kfn(std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> *_Val)
{
  return _Val;
}
