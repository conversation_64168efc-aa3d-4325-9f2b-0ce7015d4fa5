/*
 * Function: ??0ModExpPrecomputation@CryptoPP@@QEAA@XZ
 * Address: 0x14055F0A0
 */

CryptoPP::ModExpPrecomputation *__fastcall CryptoPP::ModExpPrecomputation::ModExpPrecomputation(CryptoPP::ModExpPrecomputation *this)
{
  CryptoPP::ModExpPrecomputation *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>::DL_GroupPrecomputation<CryptoPP::Integer>();
  v2->vfptr = (CryptoPP::DL_GroupPrecomputation<CryptoPP::Integer>Vtbl *)&CryptoPP::ModExpPrecomputation::`vftable';
  CryptoPP::value_ptr<CryptoPP::MontgomeryRepresentation>::value_ptr<CryptoPP::MontgomeryRepresentation>(
    &v2->m_mr,
    0i64);
  return v2;
}
