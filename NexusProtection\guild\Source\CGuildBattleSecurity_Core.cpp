/**
 * @file CGuildBattleSecurity_Core.cpp
 * @brief Modern C++20 Guild Battle Security core implementation
 * 
 * This file provides the core implementation of the CGuildBattleSecurity class
 * with comprehensive anti-cheat measures and validation.
 */

#include "../Headers/CGuildBattleSecurity.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>
#include <random>
#include <cmath>

// Legacy includes for compatibility
extern "C" {
    class CPlayer {
    public:
        uint32_t GetSerial() const;
        uint32_t GetGuildSerial() const;
        const char* GetName() const;
        float GetX() const;
        float GetY() const;
        float GetZ() const;
        bool IsInGuildBattle() const;
        void SetInGuildBattle(bool inBattle);
        void Kick(const char* reason);
    };
    
    class CGameObject {
    public:
        uint32_t GetSerial() const;
        uint8_t GetObjectType() const;
        bool IsPlayer() const;
        bool IsValid() const;
    };
    
    class CAttack {
    public:
        CPlayer* GetAttacker() const;
        uint32_t GetDamage() const;
        uint32_t GetSkillId() const;
    };
    
    extern uint32_t timeGetTime();
    extern void* memcmp_0(const void* buf1, const void* buf2, size_t count);
}

namespace NexusProtection {
namespace Guild {

// Static member definitions
std::unique_ptr<CGuildBattleSecurity> CGuildBattleSecurity::s_instance = nullptr;
std::mutex CGuildBattleSecurity::s_instanceMutex;

// PlayerSecurityProfile implementation
void PlayerSecurityProfile::AddViolation(const GuildBattleSecurityViolation& violation) {
    violationCounts[violation.type]++;
    recentViolations.push_back(violation);
    
    // Keep only recent violations (last 10)
    if (recentViolations.size() > 10) {
        recentViolations.erase(recentViolations.begin());
    }
    
    currentThreatLevel = CalculateThreatLevel();
}

SecurityThreatLevel PlayerSecurityProfile::CalculateThreatLevel() const {
    uint32_t totalViolations = 0;
    uint32_t criticalViolations = 0;
    
    for (const auto& pair : violationCounts) {
        totalViolations += pair.second;
        
        // Count critical violation types
        if (pair.first == SecurityViolationType::SpeedHack ||
            pair.first == SecurityViolationType::DamageHack ||
            pair.first == SecurityViolationType::GuildBattleExploit) {
            criticalViolations += pair.second;
        }
    }
    
    if (criticalViolations >= 3) {
        return SecurityThreatLevel::Critical;
    } else if (criticalViolations >= 1 || totalViolations >= 10) {
        return SecurityThreatLevel::High;
    } else if (totalViolations >= 5) {
        return SecurityThreatLevel::Medium;
    } else if (totalViolations >= 1) {
        return SecurityThreatLevel::Low;
    }
    
    return SecurityThreatLevel::None;
}

bool PlayerSecurityProfile::IsSuspicious() const {
    return currentThreatLevel >= SecurityThreatLevel::Medium ||
           speedHackViolations >= 2 ||
           suspiciousDamageEvents >= 3;
}

// CGuildBattleSecurity implementation
CGuildBattleSecurity::CGuildBattleSecurity() {
    std::cout << "[INFO] CGuildBattleSecurity constructor called" << std::endl;
}

CGuildBattleSecurity::CGuildBattleSecurity(const SecurityConfig& config) 
    : m_config(config) {
    std::cout << "[INFO] CGuildBattleSecurity constructor called with config" << std::endl;
}

CGuildBattleSecurity::~CGuildBattleSecurity() {
    std::cout << "[INFO] CGuildBattleSecurity destructor called" << std::endl;
    Shutdown();
}

bool CGuildBattleSecurity::Initialize() {
    std::lock_guard<std::mutex> lock(m_profilesMutex);
    
    try {
        std::cout << "[INFO] Initializing CGuildBattleSecurity" << std::endl;
        
        if (m_isInitialized) {
            std::cout << "[WARNING] Guild battle security already initialized" << std::endl;
            return true;
        }
        
        // Clear all data structures
        m_playerProfiles.clear();
        m_bannedPlayers.clear();
        m_banExpirations.clear();
        m_violations.clear();
        
        // Reset statistics
        m_stats = SecurityStatistics{};
        
        m_isInitialized = true;
        m_isShutdown = false;
        
        std::cout << "[INFO] CGuildBattleSecurity initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CGuildBattleSecurity::Initialize: " << e.what() << std::endl;
        return false;
    }
}

void CGuildBattleSecurity::Shutdown() {
    try {
        std::cout << "[INFO] Shutting down CGuildBattleSecurity" << std::endl;
        
        if (m_isShutdown) {
            return;
        }
        
        // Clear all data
        {
            std::lock_guard<std::mutex> lock(m_profilesMutex);
            m_playerProfiles.clear();
        }
        
        {
            std::lock_guard<std::mutex> lock(m_bansMutex);
            m_bannedPlayers.clear();
            m_banExpirations.clear();
        }
        
        {
            std::lock_guard<std::mutex> lock(m_violationsMutex);
            m_violations.clear();
        }
        
        m_isInitialized = false;
        m_isShutdown = true;
        
        std::cout << "[INFO] CGuildBattleSecurity shutdown completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CGuildBattleSecurity::Shutdown: " << e.what() << std::endl;
    }
}

bool CGuildBattleSecurity::CheckGuildBattleLimit(CGameObject* attacker, CGameObject* target, bool* inGuildBattle) {
    try {
        if (!m_isInitialized) {
            return false;
        }
        
        if (inGuildBattle) {
            *inGuildBattle = false;
        }
        
        if (!ValidateGameObject(attacker) || !ValidateGameObject(target)) {
            return false;
        }
        
        // Check if both objects are players
        if (!attacker->IsPlayer() || !target->IsPlayer()) {
            return true; // Allow non-player attacks
        }
        
        CPlayer* attackerPlayer = reinterpret_cast<CPlayer*>(attacker);
        CPlayer* targetPlayer = reinterpret_cast<CPlayer*>(target);
        
        if (!ValidatePlayer(attackerPlayer) || !ValidatePlayer(targetPlayer)) {
            return false;
        }
        
        // Check if both players are in guild battle
        bool attackerInBattle = attackerPlayer->IsInGuildBattle();
        bool targetInBattle = targetPlayer->IsInGuildBattle();
        
        if (inGuildBattle) {
            *inGuildBattle = attackerInBattle && targetInBattle;
        }
        
        // If in guild battle, check additional restrictions
        if (attackerInBattle && targetInBattle) {
            // Check friendly fire
            if (!m_config.allowFriendlyFire) {
                uint32_t attackerGuild = attackerPlayer->GetGuildSerial();
                uint32_t targetGuild = targetPlayer->GetGuildSerial();
                
                if (attackerGuild == targetGuild && attackerGuild != 0) {
                    std::cout << "[DEBUG] Friendly fire blocked between guild members" << std::endl;
                    return false;
                }
            }
            
            // Update player activity
            UpdatePlayerActivity(attackerPlayer->GetSerial());
            UpdatePlayerActivity(targetPlayer->GetSerial());
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CheckGuildBattleLimit: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::CheckGuildBattleGoal(CPlayer* player, int portalIndex) {
    try {
        if (!m_isInitialized || !ValidatePlayer(player)) {
            return false;
        }
        
        uint32_t playerId = player->GetSerial();
        
        std::cout << "[DEBUG] Checking guild battle goal for player " << playerId 
                 << ", portal: " << portalIndex << std::endl;
        
        // Validate portal index
        if (portalIndex < 0 || portalIndex > 10) {
            GuildBattleSecurityViolation violation(playerId, player->GetGuildSerial(), 0,
                SecurityViolationType::GuildBattleExploit, "Invalid portal index");
            ReportViolation(violation);
            return false;
        }
        
        // Check if player is in guild battle
        if (!player->IsInGuildBattle()) {
            std::cout << "[WARNING] Player " << playerId << " not in guild battle" << std::endl;
            return false;
        }
        
        // Update player activity
        UpdatePlayerActivity(playerId);
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CheckGuildBattleGoal: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::AddPlayerMonitoring(uint32_t playerId, uint32_t guildId, const std::string& playerName) {
    std::lock_guard<std::mutex> lock(m_profilesMutex);
    
    try {
        if (IsPlayerBanned(playerId)) {
            std::cout << "[WARNING] Attempted to add banned player " << playerId << " to monitoring" << std::endl;
            return false;
        }
        
        auto profile = std::make_shared<PlayerSecurityProfile>();
        profile->playerId = playerId;
        profile->guildId = guildId;
        profile->playerName = playerName;
        
        m_playerProfiles[playerId] = profile;
        m_stats.activeMonitoredPlayers++;
        
        std::cout << "[DEBUG] Added player " << playerName << " (" << playerId << ") to security monitoring" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in AddPlayerMonitoring: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::RemovePlayerMonitoring(uint32_t playerId) {
    std::lock_guard<std::mutex> lock(m_profilesMutex);
    
    try {
        auto it = m_playerProfiles.find(playerId);
        if (it != m_playerProfiles.end()) {
            std::cout << "[DEBUG] Removed player " << playerId << " from security monitoring" << std::endl;
            m_playerProfiles.erase(it);
            m_stats.activeMonitoredPlayers--;
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in RemovePlayerMonitoring: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::ValidatePlayerPosition(uint32_t playerId, float x, float y, float z) {
    try {
        if (!m_config.enablePositionValidation) {
            return true;
        }
        
        auto profile = GetPlayerProfile(playerId);
        if (!profile) {
            return true; // Not monitoring this player
        }
        
        auto now = std::chrono::steady_clock::now();
        auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - profile->lastPositionUpdate);
        
        if (timeDiff.count() > 0) {
            double speed = CalculateMovementSpeed(*profile, x, y, z);
            
            if (speed > m_config.maxMovementSpeed) {
                GuildBattleSecurityViolation violation(playerId, profile->guildId, 0,
                    SecurityViolationType::SpeedHack, 
                    "Excessive movement speed: " + std::to_string(speed));
                ReportViolation(violation);
                return false;
            }
            
            // Check for teleportation
            float dx = x - profile->lastPosition.x;
            float dy = y - profile->lastPosition.y;
            float dz = z - profile->lastPosition.z;
            double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
            
            if (distance > m_config.maxTeleportDistance && timeDiff.count() < 1000) {
                GuildBattleSecurityViolation violation(playerId, profile->guildId, 0,
                    SecurityViolationType::PositionHack, 
                    "Teleportation detected: " + std::to_string(distance) + " units");
                ReportViolation(violation);
                return false;
            }
        }
        
        // Update position history
        profile->lastPosition = {x, y, z};
        profile->lastPositionUpdate = now;
        
        // Maintain position history
        if (profile->positionHistory.size() >= m_config.positionHistorySize) {
            profile->positionHistory.erase(profile->positionHistory.begin());
        }
        profile->positionHistory.push_back({x, y, z});
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ValidatePlayerPosition: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::ValidateDamage(uint32_t attackerId, uint32_t targetId, uint32_t damage, uint32_t skillId) {
    try {
        if (!m_config.enableDamageValidation) {
            return true;
        }
        
        auto attackerProfile = GetPlayerProfile(attackerId);
        if (!attackerProfile) {
            return true; // Not monitoring this player
        }
        
        // Check for excessive damage
        if (damage > m_config.maxDamagePerSecond) {
            GuildBattleSecurityViolation violation(attackerId, attackerProfile->guildId, 0,
                SecurityViolationType::DamageHack, 
                "Excessive damage: " + std::to_string(damage));
            ReportViolation(violation);
            return false;
        }
        
        // Update damage statistics
        attackerProfile->totalDamageDealt += damage;
        attackerProfile->lastDamageDealt = std::chrono::steady_clock::now();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ValidateDamage: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::ProcessSpeedHackChallenge(uint32_t playerId, const std::array<uint32_t, 4>& challengeData) {
    try {
        auto profile = GetPlayerProfile(playerId);
        if (!profile) {
            return false;
        }
        
        // Store challenge data
        profile->speedHackKey = challengeData;
        profile->lastSpeedHackChallenge = std::chrono::steady_clock::now();
        
        std::cout << "[DEBUG] Speed hack challenge sent to player " << playerId << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ProcessSpeedHackChallenge: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::ProcessSpeedHackResponse(uint32_t playerId, const std::array<uint32_t, 4>& responseData, 
                                                   uint32_t responseTime) {
    try {
        auto profile = GetPlayerProfile(playerId);
        if (!profile) {
            return false;
        }
        
        // Verify response data
        if (memcmp_0(responseData.data(), profile->speedHackKey.data(), 16) != 0) {
            GuildBattleSecurityViolation violation(playerId, profile->guildId, 0,
                SecurityViolationType::SpeedHack, "Invalid speed hack response key");
            ReportViolation(violation);
            return false;
        }
        
        // Check response timing
        if (responseTime < m_config.minResponseTime.count() || 
            responseTime > m_config.maxResponseTime.count()) {
            
            profile->speedHackViolations++;
            
            if (profile->speedHackViolations >= m_config.maxSpeedHackViolations) {
                GuildBattleSecurityViolation violation(playerId, profile->guildId, 0,
                    SecurityViolationType::SpeedHack, 
                    "Speed hack detected - response time: " + std::to_string(responseTime) + "ms");
                ReportViolation(violation);
                return false;
            }
        } else {
            // Reset violation count on successful response
            profile->speedHackViolations = 0;
        }
        
        profile->lastSpeedHackResponse = std::chrono::steady_clock::now();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ProcessSpeedHackResponse: " << e.what() << std::endl;
        return false;
    }
}

SecurityAction CGuildBattleSecurity::ReportViolation(const GuildBattleSecurityViolation& violation) {
    try {
        std::lock_guard<std::mutex> lock(m_violationsMutex);
        
        // Add violation to list
        GuildBattleSecurityViolation newViolation = violation;
        newViolation.violationId = GenerateViolationId();
        m_violations.push_back(newViolation);
        
        // Update player profile
        auto profile = GetPlayerProfile(violation.playerId);
        if (profile) {
            profile->AddViolation(newViolation);
        }
        
        // Determine action
        SecurityAction action = m_config.defaultAction;
        auto it = m_config.violationActions.find(violation.type);
        if (it != m_config.violationActions.end()) {
            action = it->second;
        }
        
        // Execute action
        ExecuteSecurityAction(violation.playerId, action, newViolation);
        
        // Update statistics
        m_stats.totalViolations++;
        
        switch (violation.type) {
            case SecurityViolationType::SpeedHack:
                m_stats.speedHackDetections++;
                break;
            case SecurityViolationType::PositionHack:
                m_stats.positionHackDetections++;
                break;
            case SecurityViolationType::DamageHack:
                m_stats.damageHackDetections++;
                break;
            default:
                break;
        }
        
        // Notify event callback
        NotifySecurityEvent(newViolation);
        
        std::cout << "[SECURITY] Violation reported: " << SecurityUtils::SecurityViolationTypeToString(violation.type)
                 << " by player " << violation.playerId << std::endl;
        
        return action;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ReportViolation: " << e.what() << std::endl;
        return SecurityAction::None;
    }
}

std::shared_ptr<PlayerSecurityProfile> CGuildBattleSecurity::GetPlayerProfile(uint32_t playerId) const {
    std::lock_guard<std::mutex> lock(m_profilesMutex);
    
    auto it = m_playerProfiles.find(playerId);
    if (it != m_playerProfiles.end()) {
        return it->second;
    }
    
    return nullptr;
}

bool CGuildBattleSecurity::IsPlayerBanned(uint32_t playerId) const {
    std::lock_guard<std::mutex> lock(m_bansMutex);
    
    auto it = m_bannedPlayers.find(playerId);
    if (it != m_bannedPlayers.end()) {
        // Check if ban has expired
        auto expirationIt = m_banExpirations.find(playerId);
        if (expirationIt != m_banExpirations.end()) {
            auto now = std::chrono::system_clock::now();
            if (now >= expirationIt->second) {
                // Ban expired, remove it
                const_cast<CGuildBattleSecurity*>(this)->m_bannedPlayers.erase(it);
                const_cast<CGuildBattleSecurity*>(this)->m_banExpirations.erase(expirationIt);
                return false;
            }
        }
        return true;
    }
    
    return false;
}

bool CGuildBattleSecurity::BanPlayer(uint32_t playerId, const std::string& reason, std::chrono::seconds duration) {
    std::lock_guard<std::mutex> lock(m_bansMutex);
    
    try {
        m_bannedPlayers.insert(playerId);
        
        if (duration.count() > 0) {
            auto expirationTime = std::chrono::system_clock::now() + duration;
            m_banExpirations[playerId] = expirationTime;
        }
        
        m_stats.playersBanned++;
        
        std::cout << "[SECURITY] Player " << playerId << " banned: " << reason << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in BanPlayer: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildBattleSecurity::KickPlayer(uint32_t playerId, const std::string& reason) {
    try {
        // TODO: Implement actual player kicking
        // This would interface with the player management system
        
        m_stats.playersKicked++;
        
        std::cout << "[SECURITY] Player " << playerId << " kicked: " << reason << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in KickPlayer: " << e.what() << std::endl;
        return false;
    }
}

void CGuildBattleSecurity::UpdatePlayerActivity(uint32_t playerId) {
    auto profile = GetPlayerProfile(playerId);
    if (profile) {
        profile->lastActivity = std::chrono::system_clock::now();
    }
}

bool CGuildBattleSecurity::ValidatePlayer(CPlayer* player) const {
    return player != nullptr && player->GetSerial() > 0;
}

bool CGuildBattleSecurity::ValidateGameObject(CGameObject* object) const {
    return object != nullptr && object->IsValid();
}

double CGuildBattleSecurity::CalculateMovementSpeed(const PlayerSecurityProfile& profile, float newX, float newY, float newZ) const {
    if (profile.positionHistory.empty()) {
        return 0.0;
    }
    
    const auto& lastPos = profile.lastPosition;
    float dx = newX - lastPos.x;
    float dy = newY - lastPos.y;
    float dz = newZ - lastPos.z;
    
    double distance = std::sqrt(dx*dx + dy*dy + dz*dz);
    
    auto now = std::chrono::steady_clock::now();
    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(now - profile.lastPositionUpdate);
    
    if (timeDiff.count() > 0) {
        return distance / (timeDiff.count() / 1000.0); // units per second
    }
    
    return 0.0;
}

void CGuildBattleSecurity::ExecuteSecurityAction(uint32_t playerId, SecurityAction action, 
                                                 const GuildBattleSecurityViolation& violation) {
    switch (action) {
        case SecurityAction::Log:
            // Already logged
            break;
        case SecurityAction::Warn:
            std::cout << "[WARNING] Security warning for player " << playerId << std::endl;
            break;
        case SecurityAction::Kick:
            KickPlayer(playerId, violation.description);
            break;
        case SecurityAction::TempBan:
            BanPlayer(playerId, violation.description, std::chrono::hours(24));
            break;
        case SecurityAction::PermBan:
            BanPlayer(playerId, violation.description);
            break;
        case SecurityAction::Alert:
            std::cout << "[ALERT] Critical security violation by player " << playerId << std::endl;
            break;
        default:
            break;
    }
}

std::array<uint32_t, 4> CGuildBattleSecurity::GenerateSpeedHackChallenge() const {
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint32_t> dis;
    
    return {dis(gen), dis(gen), dis(gen), dis(gen)};
}

uint32_t CGuildBattleSecurity::GenerateViolationId() {
    return m_nextViolationId++;
}

void CGuildBattleSecurity::NotifySecurityEvent(const GuildBattleSecurityViolation& violation) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    
    if (m_eventCallback) {
        try {
            m_eventCallback(violation);
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Exception in security event callback: " << e.what() << std::endl;
        }
    }
}

// Singleton implementation
CGuildBattleSecurity& CGuildBattleSecurity::Instance() {
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    if (!s_instance) {
        s_instance = std::make_unique<CGuildBattleSecurity>();
    }
    
    return *s_instance;
}

void CGuildBattleSecurity::SetInstance(std::unique_ptr<CGuildBattleSecurity> instance) {
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    s_instance = std::move(instance);
}

// Factory implementation
std::unique_ptr<CGuildBattleSecurity> CGuildBattleSecurityFactory::CreateDefaultSecurity() {
    return std::make_unique<CGuildBattleSecurity>();
}

std::unique_ptr<CGuildBattleSecurity> CGuildBattleSecurityFactory::CreateSecurity(const SecurityConfig& config) {
    return std::make_unique<CGuildBattleSecurity>(config);
}

// Utility functions
namespace SecurityUtils {
    std::string SecurityThreatLevelToString(SecurityThreatLevel level) {
        switch (level) {
            case SecurityThreatLevel::None: return "None";
            case SecurityThreatLevel::Low: return "Low";
            case SecurityThreatLevel::Medium: return "Medium";
            case SecurityThreatLevel::High: return "High";
            case SecurityThreatLevel::Critical: return "Critical";
            default: return "Unknown";
        }
    }
    
    std::string SecurityViolationTypeToString(SecurityViolationType type) {
        switch (type) {
            case SecurityViolationType::None: return "None";
            case SecurityViolationType::SpeedHack: return "SpeedHack";
            case SecurityViolationType::PositionHack: return "PositionHack";
            case SecurityViolationType::DamageHack: return "DamageHack";
            case SecurityViolationType::SkillHack: return "SkillHack";
            case SecurityViolationType::ItemHack: return "ItemHack";
            case SecurityViolationType::GuildBattleExploit: return "GuildBattleExploit";
            case SecurityViolationType::UnauthorizedAccess: return "UnauthorizedAccess";
            case SecurityViolationType::InvalidBattleState: return "InvalidBattleState";
            case SecurityViolationType::SuspiciousActivity: return "SuspiciousActivity";
            case SecurityViolationType::ClientModification: return "ClientModification";
            default: return "Unknown";
        }
    }
    
    std::string SecurityActionToString(SecurityAction action) {
        switch (action) {
            case SecurityAction::None: return "None";
            case SecurityAction::Log: return "Log";
            case SecurityAction::Warn: return "Warn";
            case SecurityAction::Kick: return "Kick";
            case SecurityAction::TempBan: return "TempBan";
            case SecurityAction::PermBan: return "PermBan";
            case SecurityAction::Alert: return "Alert";
            default: return "Unknown";
        }
    }
    
    bool IsValidPlayerId(uint32_t playerId) {
        return playerId > 0;
    }
    
    bool IsValidGuildId(uint32_t guildId) {
        return guildId > 0;
    }
    
    std::string FormatSecurityViolation(const GuildBattleSecurityViolation& violation) {
        std::ostringstream oss;
        oss << "Violation #" << violation.violationId 
            << " - Player: " << violation.playerId 
            << " (" << violation.playerName << ")"
            << " - Type: " << SecurityViolationTypeToString(violation.type)
            << " - Level: " << SecurityThreatLevelToString(violation.level)
            << " - Description: " << violation.description;
        return oss.str();
    }
}

} // namespace Guild
} // namespace NexusProtection
