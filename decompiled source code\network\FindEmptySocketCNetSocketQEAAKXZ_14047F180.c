/*
 * Function: ?FindEmptySocket@CNetSocket@@QEAAKXZ
 * Address: 0x14047F180
 */

signed __int64 __fastcall CNetSocket::FindEmptySocket(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+24h] [bp-14h]@4
  unsigned int v7; // [sp+28h] [bp-10h]@7
  CNetSocket *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = timeGetTime();
  for ( j = 0; j < v8->m_SockType.m_wSocketMaxNum; ++j )
  {
    if ( !v8->m_Socket[j].m_bAccept )
    {
      v7 = v5 - v8->m_Socket[j].m_dwLastCloseTime;
      if ( v7 > v8->m_SockType.m_dwSocketRecycleTerm )
        return j;
    }
  }
  return 0xFFFFFFFFi64;
}
