/*
 * Function: ??1_qry_case_all_store_limit_item@@QEAA@XZ
 * Address: 0x14034B840
 */

void __fastcall _qry_case_all_store_limit_item::~_qry_case_all_store_limit_item(_qry_case_all_store_limit_item *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  _qry_case_all_store_limit_item *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->pStoreList )
  {
    v4 = v5->pStoreList;
    operator delete[](v4);
  }
}
