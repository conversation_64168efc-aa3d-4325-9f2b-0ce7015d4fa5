/*
 * Function: ?ct_pcanimusexp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298960
 */

char __fastcall ct_pcanimusexp(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  float v4; // xmm0_4@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  float v6; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v4 = atof(s_pwszDstCheat[0]);
      v6 = v4;
      if ( v4 > 0.0 )
      {
        if ( v6 > 100.0 )
          v6 = FLOAT_100_0;
        PCBANG_PRIMIUM_FAVOR::ANIMUS_EXP = v6;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
