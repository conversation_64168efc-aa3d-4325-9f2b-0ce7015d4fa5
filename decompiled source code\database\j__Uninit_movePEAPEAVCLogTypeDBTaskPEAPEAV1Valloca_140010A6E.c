/*
 * Function: j_??$_Uninit_move@PEAPEAVCLogTypeDBTask@@PEAPEAV1@V?$allocator@PEAVCLogTypeDBTask@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCLogTypeDBTask@@PEAPEAV1@00AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140010A6E
 */

CLogTypeDBTask **__fastcall std::_Uninit_move<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>,std::_Undefined_move_tag>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
