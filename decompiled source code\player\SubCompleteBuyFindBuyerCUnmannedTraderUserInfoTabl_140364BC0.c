/*
 * Function: ?SubCompleteBuyFindBuyer@CUnmannedTraderUserInfoTable@@AEAA_NPEAU_qry_case_unmandtrader_buy_update_wait@@PEAPEAVCUnmannedTraderUserInfo@@PEAPEAVCPlayer@@@Z
 * Address: 0x140364BC0
 */

char __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyFindBuyer(CUnmannedTraderUserInfoTable *this, _qry_case_unmandtrader_buy_update_wait *pkQuery, CUnmannedTraderUserInfo **ppkBuyUser, CPlayer **ppkBuyPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@8
  int v7; // eax@9
  char result; // al@9
  int v9; // eax@13
  unsigned int v10; // eax@14
  __int64 v11; // [sp+0h] [bp-898h]@1
  int v12; // [sp+20h] [bp-878h]@9
  char *v13; // [sp+28h] [bp-870h]@9
  char Dest; // [sp+40h] [bp-858h]@6
  char v15; // [sp+41h] [bp-857h]@6
  int v16; // [sp+444h] [bp-454h]@6
  unsigned __int8 k; // [sp+448h] [bp-450h]@6
  char v18; // [sp+460h] [bp-438h]@11
  char v19; // [sp+461h] [bp-437h]@11
  int v20; // [sp+864h] [bp-34h]@11
  unsigned __int8 j; // [sp+868h] [bp-30h]@11
  int v22; // [sp+878h] [bp-20h]@14
  unsigned __int64 v23; // [sp+880h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v24; // [sp+8A0h] [bp+8h]@1
  _qry_case_unmandtrader_buy_update_wait *pkQuerya; // [sp+8A8h] [bp+10h]@1
  CUnmannedTraderUserInfo **v26; // [sp+8B0h] [bp+18h]@1
  CPlayer **v27; // [sp+8B8h] [bp+20h]@1

  v27 = ppkBuyPlayer;
  v26 = ppkBuyUser;
  pkQuerya = pkQuery;
  v24 = this;
  v4 = &v11;
  for ( i = 548i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v23 = (unsigned __int64)&v11 ^ _security_cookie;
  *ppkBuyUser = CUnmannedTraderUserInfoTable::FindUser(v24, pkQuery->wInx, pkQuery->dwBuyer);
  if ( !CUnmannedTraderUserInfo::IsNull(*v26) && *(&g_Player.m_bOper + 50856 * CUnmannedTraderUserInfo::GetIndex(*v26)) )
  {
    *v27 = CUnmannedTraderUserInfo::FindOwner(*v26);
    if ( *v27 )
    {
      result = 1;
    }
    else
    {
      CUnmannedTraderUserInfoTable::PushUpdateBuyRollBack(v24, pkQuerya);
      v18 = 0;
      memset(&v19, 0, 0x3FFui64);
      v20 = 0;
      for ( j = 0; j < (signed int)pkQuerya->byNum; ++j )
      {
        v9 = sprintf(&v18 + v20, "%u ", pkQuerya->List[j].dwRegistSerial);
        v20 += v9;
      }
      ++CUnmannedTraderUserInfoTable::m_uiBuyRollBackCallCountSum;
      v22 = pkQuerya->byNum;
      v10 = CUnmannedTraderUserInfo::GetSerial(*v26);
      v13 = &v18;
      v12 = v22;
      CUnmannedTraderUserInfoTable::Log(
        v24,
        "SubCompleteBuyFindBuyer:: Count(%u) PushUpdateBuyRollBack Call! dwOwner(%u) pkBuyer->FindOwner() NULL!\r\n"
        "\t\tItemCnt(%u) %s\r\n",
        CUnmannedTraderUserInfoTable::m_uiBuyRollBackCallCountSum,
        v10);
      result = 0;
    }
  }
  else
  {
    CUnmannedTraderUserInfoTable::PushUpdateBuyRollBack(v24, pkQuerya);
    Dest = 0;
    memset(&v15, 0, 0x3FFui64);
    v16 = 0;
    for ( k = 0; k < (signed int)pkQuerya->byNum; ++k )
    {
      v6 = sprintf(&Dest + v16, "%u ", pkQuerya->List[k].dwRegistSerial);
      v16 += v6;
    }
    ++CUnmannedTraderUserInfoTable::m_uiBuyRollBackCallCountSum;
    v7 = pkQuerya->byNum;
    v13 = &Dest;
    v12 = v7;
    CUnmannedTraderUserInfoTable::Log(
      v24,
      "SubCompleteBuyFindBuyer:: Count(%u) PushUpdateBuyRollBack Call! dwOwner(%u) Not Connected!\r\n"
      "\t\tItemCnt(%u) %s\r\n",
      CUnmannedTraderUserInfoTable::m_uiBuyRollBackCallCountSum,
      pkQuerya->dwBuyer);
    result = 0;
  }
  return result;
}
