/*
 * Function: j_??G?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBA_JAEBV?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@1@@Z
 * Address: 0x140003E7C
 */

__int64 __fastcall std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator-(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Right)
{
  return std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator-(this, _Right);
}
