/*
 * Function: ?guild_suggest_change_taxrate@CMgrAvatorItemHistory@@QEAAXKKPEAD@Z
 * Address: 0x1402404A0
 */

void __fastcall CMgrAvatorItemHistory::guild_suggest_change_taxrate(CMgrAvatorItemHistory *this, unsigned int dwGuild, unsigned int dwMatterObj2, char *szFile)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@4
  unsigned int v8; // [sp+28h] [bp-10h]@4
  CMgrAvatorItemHistory *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1
  unsigned int v11; // [sp+50h] [bp+18h]@1
  char *pszFileName; // [sp+58h] [bp+20h]@1

  pszFileName = szFile;
  v11 = dwMatterObj2;
  v10 = dwGuild;
  v9 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset_0(sData, 0, 0x4E20ui64);
  v8 = GetKorLocalTime();
  v7 = v11;
  sprintf_s(sData, 0x4E20ui64, "[SUGGEST TAX RATE][Guild:%d] - %d - %d\r\n", v10);
  CMgrAvatorItemHistory::WriteFile(v9, pszFileName, sData);
}
