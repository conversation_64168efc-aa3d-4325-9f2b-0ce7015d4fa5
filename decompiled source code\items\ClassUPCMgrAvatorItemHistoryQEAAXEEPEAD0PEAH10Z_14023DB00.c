/*
 * Function: ?ClassUP@CMgrAvatorItemHistory@@QEAAXEEPEAD0PEAH10@Z
 * Address: 0x14023DB00
 */

void __fastcall CMgrAvatorItemHistory::ClassUP(CMgrAvatorItemHistory *this, char byCurClassGrade, char byLastClassGrade, char *szOldClass, char *szCurClass, int *piOldMaxPoint, int *piAlterMaxPoint, char *pszFileName)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-68h]@1
  char *v11; // [sp+20h] [bp-48h]@4
  char *v12; // [sp+28h] [bp-40h]@4
  int v13; // [sp+30h] [bp-38h]@4
  int v14; // [sp+38h] [bp-30h]@4
  int v15; // [sp+40h] [bp-28h]@4
  int v16; // [sp+48h] [bp-20h]@4
  int v17; // [sp+50h] [bp-18h]@4
  int v18; // [sp+58h] [bp-10h]@4
  CMgrAvatorItemHistory *v19; // [sp+70h] [bp+8h]@1

  v19 = this;
  v8 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v18 = piAlterMaxPoint[2];
  v17 = piAlterMaxPoint[1];
  v16 = *piAlterMaxPoint;
  v15 = piOldMaxPoint[2];
  v14 = piOldMaxPoint[1];
  v13 = *piOldMaxPoint;
  v12 = szCurClass;
  v11 = szOldClass;
  sprintf(
    sData,
    "CLASS UP: CGd:%d LGd:%d %s -> %s H:%d F:%d S:%d -> H:%d F:%d S:%d\r\n",
    (unsigned __int8)byCurClassGrade,
    (unsigned __int8)byLastClassGrade);
  CMgrAvatorItemHistory::WriteFile(v19, pszFileName, sData);
}
