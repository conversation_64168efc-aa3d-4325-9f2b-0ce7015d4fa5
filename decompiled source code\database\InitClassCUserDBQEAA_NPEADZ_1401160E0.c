/*
 * Function: ?InitClass@CUserDB@@QEAA_NPEAD@Z
 * Address: 0x1401160E0
 */

char __fastcall CUserDB::InitClass(CUserDB *this, char *pszClassCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned __int8 v6; // [sp+20h] [bp-18h]@4
  unsigned __int8 j; // [sp+21h] [bp-17h]@4
  CUserDB *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  for ( j = 2; v8->m_AvatorData.dbAvator.m_zClassHistory[j] == -1; --j )
    ;
  v6 = j + 1;
  if ( v6 && v6 > (signed int)v8->m_AvatorData.dbAvator.m_byLastClassGrade )
    v8->m_AvatorData.dbAvator.m_byLastClassGrade = v6;
  strcpy_0(v8->m_AvatorData.dbAvator.m_szClassCode, pszClassCode);
  for ( j = 0; (signed int)j < 3; ++j )
    v8->m_AvatorData.dbAvator.m_zClassHistory[j] = -1;
  v8->m_bDataUpdate = 1;
  return 1;
}
