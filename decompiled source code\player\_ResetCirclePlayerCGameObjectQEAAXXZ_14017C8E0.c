/*
 * Function: ?_ResetCirclePlayer@CGameObject@@QEAAXXZ
 * Address: 0x14017C8E0
 */

void __fastcall CGameObject::_ResetCirclePlayer(CGameObject *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  _sec_info *v4; // rax@10
  unsigned int v5; // eax@15
  unsigned int v6; // eax@16
  __int64 v7; // [sp+0h] [bp-AA8h]@1
  char Dst[2568]; // [sp+30h] [bp-A78h]@5
  _pnt_rect pRect; // [sp+A38h] [bp-70h]@5
  int j; // [sp+A54h] [bp-54h]@5
  int k; // [sp+A58h] [bp-50h]@7
  unsigned int dwSecIndex; // [sp+A5Ch] [bp-4Ch]@10
  CObjectList *v13; // [sp+A60h] [bp-48h]@10
  CObjectList *v14; // [sp+A68h] [bp-40h]@11
  CObjectListVtbl *v15; // [sp+A70h] [bp-38h]@13
  CGameObjectVtbl *v16; // [sp+A80h] [bp-28h]@15
  CGameObjectVtbl *v17; // [sp+A88h] [bp-20h]@16
  unsigned __int64 v18; // [sp+A90h] [bp-18h]@4
  CGameObject *v19; // [sp+AB0h] [bp+8h]@1

  v19 = this;
  v1 = &v7;
  for ( i = 680i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v18 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( v19->m_bPlayerCircleList )
  {
    memcpy_0(Dst, v19->m_bPlayerCircleList, 0x9E4ui64);
    memset_0(v19->m_bPlayerCircleList, 0, 0x9E4ui64);
    v3 = CGameObject::GetCurSecNum(v19);
    CMapData::GetRectInRadius(v19->m_pCurMap, &pRect, 11, v3);
    for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
    {
      for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
      {
        v4 = CMapData::GetSecInfo(v19->m_pCurMap);
        dwSecIndex = v4->m_nSecNumW * j + k;
        v13 = CMapData::GetSectorListPlayer(v19->m_pCurMap, v19->m_wMapLayerIndex, dwSecIndex);
        if ( v13 )
        {
          v14 = (CObjectList *)v13->m_Head.m_pNext;
          while ( (_object_list_point *)v14 != &v13->m_Tail )
          {
            v15 = v14->vfptr;
            v14 = (CObjectList *)v14->m_Head.m_pItem;
            if ( !Dst[WORD1(v15[2].__vecDelDtor)] )
            {
              if ( v19->m_bMove )
              {
                v5 = WORD1(v15[2].__vecDelDtor);
                v16 = v19->vfptr;
                (*(void (__fastcall **)(CGameObject *, _QWORD))&v16->gap8[56])(v19, v5);
              }
              else
              {
                v6 = WORD1(v15[2].__vecDelDtor);
                v17 = v19->vfptr;
                (*(void (__fastcall **)(CGameObject *, _QWORD))&v17->gap8[48])(v19, v6);
              }
            }
            v19->m_bPlayerCircleList[WORD1(v15[2].__vecDelDtor)] = 1;
          }
        }
      }
    }
  }
}
