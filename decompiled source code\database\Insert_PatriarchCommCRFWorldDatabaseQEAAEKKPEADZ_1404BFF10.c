/*
 * Function: ?Insert_PatriarchComm@CRFWorldDatabase@@QEAAEKKPEAD@Z
 * Address: 0x1404BFF10
 */

char __fastcall CRFWorldDatabase::Insert_PatriarchComm(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwDalant, char *pszDepDate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-108h]@1
  char *v8; // [sp+20h] [bp-E8h]@8
  unsigned int dwCnt; // [sp+34h] [bp-D4h]@4
  char Dest; // [sp+60h] [bp-A8h]@8
  int v11; // [sp+E4h] [bp-24h]@4
  unsigned __int64 v12; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+110h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+118h] [bp+10h]@1
  unsigned int dwDalanta; // [sp+120h] [bp+18h]@1
  char *pszDate; // [sp+128h] [bp+20h]@1

  pszDate = pszDepDate;
  dwDalanta = dwDalant;
  dwSeriala = dwSerial;
  v13 = this;
  v4 = &v7;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  dwCnt = 0;
  v11 = CRFWorldDatabase::Select_PatriarchCommCount(v13, dwSerial, pszDepDate, &dwCnt);
  if ( v11 == 1 )
  {
    result = 0;
  }
  else if ( v11 != 2 && dwCnt )
  {
    result = CRFWorldDatabase::Update_PatriarchComm(v13, dwSeriala, dwDalanta, pszDate);
  }
  else
  {
    v8 = pszDate;
    sprintf(&Dest, "{ CALL pInsert_PatriarchComm( %d, %d, '%s') }", dwSeriala, dwDalanta);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &Dest, 1);
  }
  return result;
}
