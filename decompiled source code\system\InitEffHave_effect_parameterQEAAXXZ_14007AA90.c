/*
 * Function: ?InitEffHave@_effect_parameter@@QEAAXXZ
 * Address: 0x14007AA90
 */

void __fastcall _effect_parameter::InitEffHave(_effect_parameter *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _effect_parameter *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v4->m_pDataParam )
  {
    LODWORD(v4->m_pDataParam->m_fEff_Have[0]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[1]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[2]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[3]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[4]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[5]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[6]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[7]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[8]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[9]) = 0;
    for ( j = 10; j < 83; ++j )
      LODWORD(v4->m_pDataParam->m_fEff_Have[j]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[71]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[72]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[73]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[74]) = 0;
    LODWORD(v4->m_pDataParam->m_fEff_Have[75]) = 0;
  }
}
