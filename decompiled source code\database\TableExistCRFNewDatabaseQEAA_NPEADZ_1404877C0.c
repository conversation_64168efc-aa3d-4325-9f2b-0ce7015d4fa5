/*
 * Function: ?TableExist@CRFNewDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404877C0
 */

char __fastcall CRFNewDatabase::TableExist(CRFNewDatabase *this, char *szTableName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-178h]@1
  void *SQLStmt; // [sp+20h] [bp-158h]@13
  __int16 v7; // [sp+30h] [bp-148h]@9
  char _Dest[256]; // [sp+50h] [bp-128h]@4
  unsigned __int64 v9; // [sp+160h] [bp-18h]@4
  CRFNewDatabase *v10; // [sp+180h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf_s<256>((char (*)[256])_Dest, "SELECT top 1 name FROM dbo.sysobjects WHERE id = object_id('%s')", szTableName);
  if ( v10->m_bSaveDBLog )
    CRFNewDatabase::Log(v10, _Dest);
  if ( v10->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase(v10) )
  {
    v7 = SQLExecDirect_0(v10->m_hStmtSelect, _Dest, -3);
    if ( v7 && v7 != 1 )
    {
      if ( v7 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v10->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog(v10, v7, _Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction(v10, v7, v10->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v7 = SQLFetch_0(v10->m_hStmtSelect);
      if ( v7 && v7 != 1 )
      {
        if ( v10->m_hStmtSelect )
          SQLCloseCursor_0(v10->m_hStmtSelect);
        if ( v7 == 100 )
        {
          result = 0;
        }
        else
        {
          SQLStmt = v10->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog(v10, v7, _Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction(v10, v7, v10->m_hStmtSelect);
          result = 0;
        }
      }
      else
      {
        if ( v10->m_hStmtSelect )
          SQLCloseCursor_0(v10->m_hStmtSelect);
        if ( v10->m_bSaveDBLog )
          CRFNewDatabase::FmtLog(v10, "%s Success", _Dest);
        result = 1;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog(v10, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 0;
  }
  return result;
}
