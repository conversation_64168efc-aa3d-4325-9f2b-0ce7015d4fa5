/*
 * Function: ?CascadeExponentiate@MontgomeryRepresentation@CryptoPP@@UEBA?AVInteger@2@AEBV32@000@Z
 * Address: 0x140561080
 */

struct CryptoPP::Integer *__fastcall CryptoPP::MontgomeryRepresentation::CascadeExponentiate(CryptoPP::MontgomeryRepresentation *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5, const struct CryptoPP::Integer *a6)
{
  struct CryptoPP::Integer *v7; // [sp+58h] [bp+10h]@1

  v7 = retstr;
  CryptoPP::AbstractRing<CryptoPP::Integer>::CascadeExponentiate((__int64)this, (__int64)retstr);
  return v7;
}
