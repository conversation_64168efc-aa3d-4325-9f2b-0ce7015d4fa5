/*
 * Function: ?pc_SetRaceBossCryMsg@CPlayer@@QEAAXEPEAD@Z
 * Address: 0x1400FF970
 */

void __fastcall CPlayer::pc_SetRaceBossCryMsg(CPlayer *this, char bySlot, char *pwszCryMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  const char *wszStr; // [sp+40h] [bp+18h]@1

  wszStr = pwszCryMsg;
  v8 = bySlot;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_pUserDB && IsSQLValidString(pwszCryMsg) )
  {
    v5 = CTSingleton<CNationSettingManager>::Instance();
    if ( CNationSettingManager::IsNormalString(v5, wszStr) )
    {
      strcpy_0((char *)&v7->m_pmCryMsg + 65 * (unsigned __int8)v8, wszStr);
      CUserDB::Update_BossCryMsg(v7->m_pUserDB, v8, (char *)wszStr);
    }
  }
}
