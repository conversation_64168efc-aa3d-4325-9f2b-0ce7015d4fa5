/*
 * Function: ?ClassSkillRecallTeleportRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C3090
 */

char __fastcall CNetworkEX::ClassSkillRecallTeleportRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  int v6; // eax@8
  char *v7; // rax@9
  CRecallEffectController *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-68h]@1
  char *v10; // [sp+20h] [bp-48h]@4
  CPlayer *pkObj; // [sp+28h] [bp-40h]@4
  CPlayer *v12; // [sp+30h] [bp-38h]@10
  _CHRID pidDst; // [sp+44h] [bp-24h]@12
  int v14; // [sp+54h] [bp-14h]@8
  CNetworkEX *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v3 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  pkObj = &g_Player + n;
  if ( !pkObj->m_bOper || pkObj->m_pmTrd.bDTradeMode || pkObj->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    v14 = *(_WORD *)v10;
    v6 = CRecordData::GetRecordNum(&stru_1799C8410);
    if ( v14 < v6 )
    {
      v12 = GetPtrPlayerFromName(&g_Player, 2532, v10 + 2);
      if ( v12 )
      {
        pidDst.byID = v12->m_ObjID.m_byID;
        pidDst.dwSerial = v12->m_ObjID.m_wIndex;
        pidDst.wIndex = v12->m_dwObjSerial;
        CPlayer::pc_ClassSkillRequest(pkObj, *(_WORD *)v10, &pidDst, (unsigned __int16 *)(v10 + 19));
        result = 1;
      }
      else
      {
        v8 = CRecallEffectController::Instance();
        CRecallEffectController::SendRecallReqeustResult(v8, 17, pkObj);
        result = 1;
      }
    }
    else
    {
      v7 = CPlayerDB::GetCharNameA(&pkObj->m_Param);
      CLogFile::Write(
        &v15->m_LogFile,
        "odd.. %s: ClassSkillRecallTeleportRequest()..  if(pRecv->wSkillIndex >= g_Main.m_tblEffectData[effect_code_skill"
        "].GetRecordNum())",
        v7);
      result = 0;
    }
  }
  return result;
}
