/*
 * Function: ?Select_PvpRankInfo@CRFWorldDatabase@@QEAAEEPEADPEAU_PVP_RANK_DATA@@@Z
 * Address: 0x140497510
 */

char __fastcall CRFWorldDatabase::Select_PvpRankInfo(CRFWorldDatabase *this, char byRace, char *szDate, _PVP_RANK_DATA *rankData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@16
  SQLLEN v10; // [sp+38h] [bp-150h]@16
  __int16 v11; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  char v13; // [sp+61h] [bp-127h]@4
  int v14; // [sp+164h] [bp-24h]@4
  unsigned __int64 v15; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+190h] [bp+8h]@1
  _PVP_RANK_DATA *v17; // [sp+1A8h] [bp+20h]@1

  v17 = rankData;
  v16 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  Dest = 0;
  memset(&v13, 0, 0xFFui64);
  v14 = 0;
  sprintf(
    &Dest,
    "select top 100 Rank,Lv,Rate,Grade,PvpPoint,Name,GuildName,serial,guildserial from tbl_PvpRank%s where race = %d orde"
    "r by grade desc, rate ",
    szDate);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      do
      {
        v11 = SQLFetch_0(v16->m_hStmtSelect);
        if ( v11 && v11 != 1 )
          break;
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, -28, &v17[v14], 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 2u, -28, &v17[v14].byLv, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 3u, -17, &v17[v14].wRate, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 4u, -28, &v17[v14].byGrade, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 5u, 8, &v17[v14].dPvpPoint, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 6u, 1, v17[v14].wszName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 7u, 1, v17[v14].wszGuildName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 8u, 4, &v17[v14].dwAvatorSerial, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 9u, 4, &v17[v14++].dwGuildSerial, 0i64, &v10);
        if ( v14 >= 100 )
          break;
      }
      while ( !v11 || v11 == 1 );
      if ( v16->m_hStmtSelect )
        SQLCloseCursor_0(v16->m_hStmtSelect);
      if ( v16->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
