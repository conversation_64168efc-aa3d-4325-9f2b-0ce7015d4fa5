/*
 * Function: ?FindDummy@CDummyPosTable@@SA_NPEAD0PEAU_dummy_position@@@Z
 * Address: 0x14018A780
 */

char __fastcall CDummyPosTable::FindDummy(char *pszTextFileName, char *pszDummyCode, _dummy_position *pDummyPos)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-198h]@1
  FILE *File; // [sp+20h] [bp-178h]@4
  char Str1; // [sp+40h] [bp-158h]@7
  char Src; // [sp+41h] [bp-157h]@18
  int v10; // [sp+C4h] [bp-D4h]@6
  char Dest[2]; // [sp+E0h] [bp-B8h]@6
  char v12; // [sp+E2h] [bp-B6h]@6
  int v13; // [sp+164h] [bp-34h]@14
  int j; // [sp+168h] [bp-30h]@18
  int v15; // [sp+16Ch] [bp-2Ch]@14
  char *String; // [sp+170h] [bp-28h]@18
  char *v17; // [sp+178h] [bp-20h]@18
  unsigned __int64 v18; // [sp+188h] [bp-10h]@4
  const char *Filename; // [sp+1A0h] [bp+8h]@1
  const char *Source; // [sp+1A8h] [bp+10h]@1
  _dummy_position *v21; // [sp+1B0h] [bp+18h]@1

  v21 = pDummyPos;
  Source = pszDummyCode;
  Filename = pszTextFileName;
  v3 = &v6;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  File = fopen(Filename, "rt");
  if ( File )
  {
    v10 = 0;
    strcpy(Dest, "*");
    memset(&v12, 0, 0x7Eui64);
    strcat_0(Dest, Source);
    while ( fscanf(File, "%s", &Str1) != -1 )
    {
      if ( !strcmp_0(&Str1, Dest) )
      {
        ++v10;
      }
      else if ( !strcmp_0(&Str1, "[HelperObjectEnd]") )
      {
        break;
      }
    }
    if ( v10 == 1 )
    {
      rewind(File);
      v13 = 0;
      v15 = 0;
      while ( fscanf(File, "%s", &Str1) != -1 )
      {
        if ( Str1 == 42 )
        {
          if ( !strcmp_0(&Str1, Dest) )
          {
            v21->m_wLineIndex = v15;
            String = _strdup(&Src);
            v17 = _strlwr(String);
            strcpy_0(v21->m_szCode, v17);
            free(String);
            for ( j = 0; j < 3; ++j )
              fscanf(File, "%d", &v21->m_zLocalMin[j]);
            for ( j = 0; j < 3; ++j )
              fscanf(File, "%d", &v21->m_zLocalMax[j]);
            break;
          }
          ++v15;
        }
        else if ( !strcmp_0(&Str1, "[HelperObjectEnd]") )
        {
          break;
        }
      }
      fclose(File);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
