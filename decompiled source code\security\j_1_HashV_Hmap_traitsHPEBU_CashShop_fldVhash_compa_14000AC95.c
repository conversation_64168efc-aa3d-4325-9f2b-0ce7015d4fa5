/*
 * Function: j_??1?$_Hash@V?$_Hmap_traits@HPEBU_CashShop_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA@XZ
 * Address: 0x14000AC95
 */

void __fastcall stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::~_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>(stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> >,0> > *this)
{
  stdext::_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>::~_Hash<stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>>(this);
}
