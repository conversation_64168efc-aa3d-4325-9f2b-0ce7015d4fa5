/*
 * Function: ?pc_RequestQuestListFromNPC@CPlayer@@QEAAXPEAVCItemStore@@@Z
 * Address: 0x1400CC720
 */

void __usercall CPlayer::pc_RequestQuestListFromNPC(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float *v5; // rax@4
  int v6; // eax@5
  char v7; // al@8
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *pszEventCode; // [sp+20h] [bp-28h]@7
  int v10; // [sp+28h] [bp-20h]@5
  _NPCQuestIndexTempData *pQuestIndexData; // [sp+30h] [bp-18h]@8
  CPlayer *v12; // [sp+50h] [bp+8h]@1
  CItemStore *v13; // [sp+58h] [bp+10h]@1

  v13 = pStore;
  v12 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CItemStore::GetStorePos(pStore);
  GetSqrt(v12->m_fCurPos, v5);
  if ( a3 <= 80.0 )
  {
    v10 = v13->m_byNpcRaceCode;
    v6 = CPlayerDB::GetRaceCode(&v12->m_Param);
    if ( v10 == v6 || v13->m_byNpcRaceCode == 255 )
    {
      pszEventCode = CItemStore::GetNpcCode(v13);
      if ( pszEventCode )
      {
        _NPCQuestIndexTempData::Init(&v12->m_NPCQuestIndexTempData);
        pQuestIndexData = &v12->m_NPCQuestIndexTempData;
        v7 = CPlayerDB::GetRaceCode(&v12->m_Param);
        CQuestMgr::CheckNPCQuestList(&v12->m_QuestMgr, pszEventCode, v7, pQuestIndexData);
        CPlayer::SendMsg_NpcQuestListResult(v12, &v12->m_NPCQuestIndexTempData);
      }
    }
  }
}
