/*
 * Function: ?GetRandPosInRange@CMapData@@QEAA_NPEAMH0@Z
 * Address: 0x140185B10
 */

char __fastcall CMapData::GetRandPosInRange(CMapData *this, float *pStdPos, int nRange, float *pNewPos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-58h]@1
  float v8[3]; // [sp+28h] [bp-30h]@5
  int v9; // [sp+44h] [bp-14h]@4
  float v10; // [sp+48h] [bp-10h]@5
  float v11; // [sp+4Ch] [bp-Ch]@5
  CMapData *v12; // [sp+60h] [bp+8h]@1
  float *v13; // [sp+68h] [bp+10h]@1
  int v14; // [sp+70h] [bp+18h]@1
  float *v15; // [sp+78h] [bp+20h]@1

  v15 = pNewPos;
  v14 = nRange;
  v13 = pStdPos;
  v12 = this;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  do
  {
    v10 = *v13 - (float)(v14 / 2);
    *v15 = v10 + (float)(rand() % v14);
    v11 = v13[2] - (float)(v14 / 2);
    v15[2] = v11 + (float)(rand() % v14);
    v15[1] = v13[1];
    if ( (unsigned int)CBsp::CanYouGoThere(v12->m_Level.mBsp, v13, v15, (float (*)[3])v8) )
      return 1;
    ++v9;
  }
  while ( v9 <= 50 );
  return 0;
}
