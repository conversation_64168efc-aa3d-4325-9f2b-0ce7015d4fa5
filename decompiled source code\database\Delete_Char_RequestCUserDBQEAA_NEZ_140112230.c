/*
 * Function: ?Delete_Char_Request@CUserDB@@QEAA_NE@Z
 * Address: 0x140112230
 */

char __fastcall CUserDB::Delete_Char_Request(CUserDB *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v5; // ax@17
  __int64 v6; // [sp+0h] [bp-A8h]@1
  unsigned __int16 nLen[4]; // [sp+20h] [bp-88h]@18
  int nSize; // [sp+28h] [bp-80h]@18
  char v9; // [sp+30h] [bp-78h]@13
  _del_char_result_zone v10; // [sp+44h] [bp-64h]@14
  char pbyType; // [sp+64h] [bp-44h]@17
  char v12; // [sp+65h] [bp-43h]@17
  _qry_sheet_delete v13; // [sp+88h] [bp-20h]@18
  CUserDB *v14; // [sp+B0h] [bp+8h]@1
  char v15; // [sp+B8h] [bp+10h]@1

  v15 = bySlotIndex;
  v14 = this;
  v2 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v14->m_bActive )
  {
    if ( v14->m_ss.bReged && !v14->m_ss.bSelect )
    {
      if ( v14->m_bDBWaitState )
      {
        result = 0;
      }
      else if ( v14->m_RegedList[(unsigned __int8)bySlotIndex].m_bySlotIndex == 255 )
      {
        result = 0;
      }
      else
      {
        v9 = CUserDB::IsExistRequestMoveCharacterList(v14, v14->m_RegedList[(unsigned __int8)bySlotIndex].m_dwRecordNum);
        if ( v9 )
        {
          v10.byRetCode = 0;
          memset(&v10.bySlotIndex, 0, sizeof(v10.bySlotIndex));
          if ( v9 == 1 )
          {
            v10.byRetCode = 74;
          }
          else
          {
            v9 = 2;
            v10.byRetCode = 76;
          }
          v10.bySlotIndex = v15;
          pbyType = 1;
          v12 = 13;
          v5 = _del_char_result_zone::size(&v10);
          CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_idWorld.wIndex, &pbyType, &v10.byRetCode, v5);
          result = 1;
        }
        else
        {
          CMgrAccountLobbyHistory::del_char_request(
            &CUserDB::s_MgrLobbyHistory,
            v15,
            v14->m_RegedList[(unsigned __int8)v15].m_dwRecordNum,
            v14->m_szLobbyHistoryFileName);
          v13.bySlotIndex = v15;
          v13.byRaceCode = v14->m_RegedList[(unsigned __int8)v15].m_byRaceSexCode >> 1;
          v13.dwAvatorSerial = v14->m_RegedList[(unsigned __int8)v15].m_dwRecordNum;
          nSize = _qry_sheet_delete::size(&v13);
          *(_QWORD *)nLen = &v13;
          if ( CMainThread::PushDQSData(&g_Main, v14->m_dwAccountSerial, &v14->m_idWorld, 2, (char *)&v13, nSize) )
          {
            v14->m_bDBWaitState = 1;
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
