/*
 * Function: ?GetMap@CMapOperation@@QEAAHPEAVCMapData@@@Z
 * Address: 0x1401979B0
 */

signed __int64 __fastcall CMapOperation::GetMap(CMapOperation *this, CMapData *pMap)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  CMapOperation *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; (signed int)j < v6->m_nMapNum; ++j )
  {
    if ( &v6->m_Map[j] == pMap )
      return j;
  }
  return 0xFFFFFFFFi64;
}
