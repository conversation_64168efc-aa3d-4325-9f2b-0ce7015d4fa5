#pragma once

/**
 * @file MonsterUtils.h
 * @brief Monster utility functions for NexusProtection
 * @details Provides utility functions for monster management, searching, and operations
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include <cstdint>
#include <memory>
#include <vector>
#include <functional>
#include <optional>

// Forward declarations
class CMonster;
class CMapData;
class CMonsterHierarchy;

namespace NexusProtection {
namespace World {

/**
 * @enum MonsterSearchCriteria
 * @brief Criteria for searching monsters
 */
enum class MonsterSearchCriteria : uint32_t {
    Any = 0,                    ///< Any monster
    EmptySlot = 1,              ///< Empty monster slot
    NotLive = 2,                ///< Monster that is not alive
    NoChildren = 4,             ///< Monster with no children
    NoParent = 8,               ///< Monster with no parent
    NoTarget = 16,              ///< Monster with no target
    NormalCondition = 32,       ///< Monster in normal condition
    NoEmotion = 64,             ///< Monster with no emotion state
    InNormalMap = 128           ///< Monster in normal map type
};

/**
 * @struct MonsterSearchOptions
 * @brief Options for monster searching operations
 */
struct MonsterSearchOptions {
    bool withoutFail;           ///< Allow destroying monsters if no empty slots found
    uint32_t maxSearchCount;    ///< Maximum number of monsters to search
    MonsterSearchCriteria criteria; ///< Search criteria flags
    
    MonsterSearchOptions() 
        : withoutFail(false)
        , maxSearchCount(30000)
        , criteria(MonsterSearchCriteria::EmptySlot) {}
};

/**
 * @namespace MonsterUtils
 * @brief Utility functions for monster management
 * 
 * This namespace contains utility functions for monster operations including:
 * - Searching for empty monster slots
 * - Finding monsters by criteria
 * - Monster validation and cleanup
 * - Monster pool management
 */
namespace MonsterUtils {

    /**
     * @brief Search for an empty monster slot
     * @param withoutFail If true, will destroy existing monsters to make space if needed
     * @return Pointer to an empty monster slot, or nullptr if none found
     * 
     * This function searches through the global monster array to find an empty slot.
     * If withoutFail is true and no empty slots are found, it will attempt to
     * destroy suitable monsters to free up space.
     * 
     * @note Original function: SearchEmptyMonster (Address: 0x140148F20)
     */
    CMonster* SearchEmptyMonster(bool withoutFail = false);

    /**
     * @brief Search for an empty monster slot with options
     * @param options Search options and criteria
     * @return Pointer to an empty monster slot, or nullptr if none found
     */
    CMonster* SearchEmptyMonster(const MonsterSearchOptions& options);

    /**
     * @brief Find monsters matching specific criteria
     * @param criteria Search criteria flags
     * @param maxResults Maximum number of results to return
     * @return Vector of monster pointers matching the criteria
     */
    std::vector<CMonster*> FindMonsters(MonsterSearchCriteria criteria, 
                                       std::size_t maxResults = 100);

    /**
     * @brief Check if a monster slot is empty
     * @param monster Pointer to the monster to check
     * @return true if the slot is empty, false otherwise
     */
    bool IsEmptySlot(const CMonster* monster);

    /**
     * @brief Check if a monster can be safely destroyed
     * @param monster Pointer to the monster to check
     * @return true if the monster can be destroyed, false otherwise
     */
    bool CanDestroyMonster(const CMonster* monster);

    /**
     * @brief Get the total number of monster slots
     * @return Total number of monster slots in the global array
     */
    [[nodiscard]] constexpr std::size_t GetMaxMonsterSlots() noexcept {
        return 30000;
    }

    /**
     * @brief Get the size of each monster slot in bytes
     * @return Size of each monster slot
     */
    [[nodiscard]] constexpr std::size_t GetMonsterSlotSize() noexcept {
        return 6424;
    }

    /**
     * @brief Get monster by index
     * @param index Index in the global monster array
     * @return Pointer to the monster at the specified index, or nullptr if invalid
     */
    CMonster* GetMonsterByIndex(std::size_t index);

    /**
     * @brief Get the index of a monster in the global array
     * @param monster Pointer to the monster
     * @return Index of the monster, or std::nullopt if not found
     */
    std::optional<std::size_t> GetMonsterIndex(const CMonster* monster);

    /**
     * @brief Count empty monster slots
     * @return Number of empty monster slots
     */
    std::size_t CountEmptySlots();

    /**
     * @brief Count live monsters
     * @return Number of live monsters
     */
    std::size_t CountLiveMonsters();

    /**
     * @brief Validate monster pointer
     * @param monster Pointer to validate
     * @return true if the pointer is valid and within the monster array bounds
     */
    bool ValidateMonsterPointer(const CMonster* monster);

    /**
     * @brief Apply a function to all monsters matching criteria
     * @param criteria Search criteria
     * @param func Function to apply to each matching monster
     * @return Number of monsters processed
     */
    std::size_t ForEachMonster(MonsterSearchCriteria criteria, 
                              std::function<void(CMonster*)> func);

    /**
     * @brief Get monster statistics
     * @return Structure containing various monster statistics
     */
    struct MonsterStats {
        std::size_t totalSlots;
        std::size_t emptySlots;
        std::size_t liveMonsters;
        std::size_t destroyableMonsters;
        std::size_t monstersWithChildren;
        std::size_t monstersWithParents;
        std::size_t monstersWithTargets;
    };

    /**
     * @brief Get comprehensive monster statistics
     * @return MonsterStats structure with current statistics
     */
    MonsterStats GetMonsterStatistics();

    /**
     * @brief Cleanup destroyed monsters
     * @details Performs cleanup operations on destroyed monsters
     * @return Number of monsters cleaned up
     */
    std::size_t CleanupDestroyedMonsters();

    /**
     * @brief Check if monster search criteria flags are set
     * @param criteria The criteria to check
     * @param flag The flag to test
     * @return true if the flag is set in the criteria
     */
    constexpr bool HasCriteria(MonsterSearchCriteria criteria, MonsterSearchCriteria flag) noexcept {
        return (static_cast<uint32_t>(criteria) & static_cast<uint32_t>(flag)) != 0;
    }

    /**
     * @brief Combine monster search criteria flags
     * @param lhs Left-hand side criteria
     * @param rhs Right-hand side criteria
     * @return Combined criteria
     */
    constexpr MonsterSearchCriteria operator|(MonsterSearchCriteria lhs, MonsterSearchCriteria rhs) noexcept {
        return static_cast<MonsterSearchCriteria>(static_cast<uint32_t>(lhs) | static_cast<uint32_t>(rhs));
    }

    /**
     * @brief Test monster search criteria flags
     * @param lhs Left-hand side criteria
     * @param rhs Right-hand side criteria
     * @return Result of bitwise AND operation
     */
    constexpr MonsterSearchCriteria operator&(MonsterSearchCriteria lhs, MonsterSearchCriteria rhs) noexcept {
        return static_cast<MonsterSearchCriteria>(static_cast<uint32_t>(lhs) & static_cast<uint32_t>(rhs));
    }

} // namespace MonsterUtils

} // namespace World
} // namespace NexusProtection

// Global monster array declaration (defined elsewhere)
extern char* g_Monster;

// Legacy C-style interface for compatibility
extern "C" {
    /**
     * @brief Legacy C interface for SearchEmptyMonster
     * @param bWithoutFail Whether to allow destroying monsters if no empty slots
     * @return Pointer to an empty monster slot
     */
    CMonster* SearchEmptyMonster(bool bWithoutFail);
}
