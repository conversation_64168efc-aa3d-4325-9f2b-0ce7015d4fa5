/*
 * Function: ?IsPvpMap@CPvpCashPoint@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1403F52D0
 */

bool __fastcall CPvpCashPoint::IsPvpMap(CPvpCashPoint *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  _map_fld *v7; // [sp+20h] [bp-18h]@7
  CGameObjectVtbl *v8; // [sp+28h] [bp-10h]@7

  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne && pOne->m_bOper )
  {
    v7 = pOne->m_pCurMap->m_pMapSet;
    v8 = pOne->vfptr;
    v5 = ((int (__fastcall *)(CPlayer *))v8->GetObjRace)(pOne);
    result = v7->m_nRacePvpUsable[v5] != 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
