/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000C743
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Ufill(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, CUnmannedTraderGroupDivisionVersionInfo *_Ptr, unsigned __int64 _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  return std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
