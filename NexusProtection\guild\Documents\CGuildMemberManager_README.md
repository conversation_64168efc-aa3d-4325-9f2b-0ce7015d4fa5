# CGuildMemberManager - Modern C++20 Guild Member Manager

## Overview

The `CGuildMemberManager` is a comprehensive, modern C++20 implementation that manages all guild member operations including authentication, permissions, communication, and administrative functions. This system refactors the original decompiled C functions into a robust, maintainable, and feature-rich guild member management system.

## Refactored Sources

This implementation modernizes the following decompiled files:
- **SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.c** (110 lines) - Comprehensive guild setup with member management
- **LoginMemberCGuildQEAAPEAU_guild_member_infoKPEAVCP_140253750.c** (37 lines) - Member login authentication
- **SendMsg_GuildMemberLoginCGuildQEAAXKGGZ_1402570F0.c** - Member login notifications
- **SendMsg_GuildMemberLogoffCGuildQEAAXKZ_140257250.c** - Member logout notifications
- **SendMsg_LeaveMemberCGuildQEAAXK_N0Z_140255A30.c** - Member leave notifications
- **DB_Update_GuildMasterCGuildQEAA_NPEAU_guild_member_140252BD0.c** - Guild master updates

## Key Features

### 🚀 **Modern C++20 Implementation**
- **RAII Memory Management**: Automatic resource cleanup and exception safety
- **Smart Pointers**: Safe memory handling with shared ownership semantics
- **STL Containers**: Modern containers for efficient member data management
- **Atomic Operations**: Thread-safe statistics and member tracking
- **Exception Safety**: Comprehensive error handling and recovery

### 👥 **Advanced Member Management**
- **Member Lifecycle**: Complete member join, login, logout, and leave management
- **Authentication System**: Secure member login with session management
- **Permission System**: Hierarchical permission system (Member → Committee → Master)
- **Activity Tracking**: Real-time member activity and status monitoring
- **Communication**: Guild-wide messaging and notification system

### 📊 **Comprehensive Statistics and Monitoring**
- **Member Activity**: Track logins, logouts, joins, and leaves
- **Online Presence**: Real-time online member counting and monitoring
- **Permission Changes**: Track master changes and permission modifications
- **Message Statistics**: Monitor guild communication activity
- **Performance Metrics**: Member management performance tracking

### 🔒 **Security and Permissions**
- **Role-Based Access**: Three-tier permission system (Member/Committee/Master)
- **Action Validation**: Comprehensive permission checking for all operations
- **Master Election**: Automatic master election eligibility checking
- **Committee Management**: Automatic committee member assignment and tracking

## Architecture

### Class Structure

```cpp
namespace NexusProtection::Guild {
    class CGuildMemberManager {
        // Singleton pattern with thread-safe member operations
        // Comprehensive member lifecycle management
        // Event-driven architecture with callbacks
        // Advanced permission and security system
    };
}
```

### Key Components

1. **Member Lifecycle Engine**: Complete member join/leave/login/logout management
2. **Authentication System**: Secure member login and session management
3. **Permission Manager**: Hierarchical role-based access control
4. **Communication System**: Guild-wide messaging and notifications
5. **Statistics Collection**: Real-time metrics and activity monitoring
6. **Event System**: Callback-based notifications for member events

## Member Management System

### Member Hierarchy

The system supports a three-tier member hierarchy:

| Role | Permissions | Capabilities |
|------|-------------|--------------|
| **Master** | Full Control | All guild operations, member management, guild settings |
| **Committee** | Administrative | Member management, some guild operations |
| **Member** | Basic Access | Basic guild participation, limited operations |

### Member Status Tracking

- **Offline**: Member not currently logged in
- **Online**: Member actively logged in and available
- **InBattle**: Member participating in guild battle
- **InRoom**: Member in guild room
- **Away**: Member logged in but inactive

### Guild Setup Process

The system handles comprehensive guild initialization:

1. **Basic Information** → Guild serial, name, race, grade, emblems
2. **Member Data** → Initialize all founding members with roles
3. **Master Setup** → Assign guild master and check election eligibility
4. **Committee Assignment** → Automatically assign committee members
5. **Financial Data** → Initialize guild funds and transaction history
6. **Battle Statistics** → Setup win/draw/loss counters
7. **Packet Generation** → Create all required network packets

## Usage Examples

### Basic Initialization

```cpp
#include "CGuildMemberManager.h"

using namespace NexusProtection::Guild;

// Get singleton instance
auto& memberManager = CGuildMemberManager::GetInstance();

// Configure system
GuildMemberConfig config;
config.maxMembers = 50;
config.maxCommitteeMembers = 3;
config.enableMemberTracking = true;
config.enableStatistics = true;

// Initialize system
auto result = memberManager.Initialize(config);
if (result != GuildMemberResult::Success) {
    std::cerr << "Failed to initialize guild member manager" << std::endl;
    return -1;
}
```

### Guild Setup

```cpp
// Create guild setup data
GuildSetupData setupData;
setupData.guildSerial = 12345;
setupData.guildName = "Elite Warriors";
setupData.guildRace = 0; // Bellato
setupData.guildGrade = 1;
setupData.greetingMessage = "Welcome to Elite Warriors!";
setupData.totalDalant = 1000000.0;
setupData.totalGold = 500000.0;

// Add founding members
GuildMemberInfo master;
master.memberSerial = 67890;
master.memberName = "GuildMaster";
master.level = 50;
master.memberClass = GuildMemberClass::Master;
setupData.members.push_back(master);

GuildMemberInfo committee;
committee.memberSerial = 11111;
committee.memberName = "Committee1";
committee.level = 45;
committee.memberClass = GuildMemberClass::Committee;
setupData.members.push_back(committee);

// Setup master info
setupData.masterInfo.masterSerial = 67890;
setupData.masterInfo.previousGrade = 0;

// Setup guild
auto result = memberManager.SetupGuild(setupData);
if (result == GuildMemberResult::Success) {
    std::cout << "Guild setup completed successfully" << std::endl;
}
```

### Member Login/Logout

```cpp
// Member login
CPlayer* playerPtr = GetPlayerPointer(67890);
auto member = memberManager.LoginMember(67890, playerPtr);

if (member) {
    std::cout << "Member logged in: " << member->memberName << std::endl;
    
    // Send login notification to all guild members
    auto result = memberManager.SendMemberLoginNotification(67890, 1001, 5);
    if (result == GuildMemberResult::Success) {
        std::cout << "Login notification sent successfully" << std::endl;
    }
}

// Member logout
auto logoutResult = memberManager.SendMemberLogoutNotification(67890);
if (logoutResult == GuildMemberResult::Success) {
    std::cout << "Logout notification sent successfully" << std::endl;
}
```

### Permission Management

```cpp
// Check if member can kick other members
bool canKick = memberManager.HasPermission(67890, GuildMemberClass::Committee);
if (canKick) {
    std::cout << "Member has permission to kick other members" << std::endl;
}

// Update guild master
auto masterResult = memberManager.UpdateGuildMaster(11111);
if (masterResult == GuildMemberResult::Success) {
    std::cout << "Guild master updated successfully" << std::endl;
}
```

### Event Callbacks

```cpp
// Set member login callback
memberManager.SetMemberLoginCallback([](uint32_t memberSerial, uint16_t mapCode, uint16_t regionIndex) {
    std::cout << "Member " << memberSerial << " logged in at map " << mapCode 
              << ", region " << regionIndex << std::endl;
});

// Set member logout callback
memberManager.SetMemberLogoutCallback([](uint32_t memberSerial) {
    std::cout << "Member " << memberSerial << " logged out" << std::endl;
});

// Set master change callback
memberManager.SetMasterChangeCallback([](uint32_t oldMaster, uint32_t newMaster) {
    std::cout << "Guild master changed from " << oldMaster << " to " << newMaster << std::endl;
});

// Set member leave callback
memberManager.SetMemberLeaveCallback([](uint32_t memberSerial, bool selfLeave, bool punishment) {
    std::cout << "Member " << memberSerial << " left guild (self=" << selfLeave 
              << ", punishment=" << punishment << ")" << std::endl;
});
```

### Statistics Monitoring

```cpp
// Get current statistics
auto stats = memberManager.GetStatistics();

std::cout << "Guild Member Statistics:" << std::endl;
std::cout << "  Total Member Logins: " << stats.totalMemberLogins << std::endl;
std::cout << "  Total Member Logouts: " << stats.totalMemberLogouts << std::endl;
std::cout << "  Total Member Joins: " << stats.totalMemberJoins << std::endl;
std::cout << "  Total Member Leaves: " << stats.totalMemberLeaves << std::endl;
std::cout << "  Active Members Online: " << stats.activeMembersOnline << std::endl;
std::cout << "  Total Master Changes: " << stats.totalMasterChanges << std::endl;
std::cout << "  Messages Sent: " << stats.messagesSent << std::endl;
std::cout << "  Average Online Members: " << stats.GetAverageOnlineMembers() << std::endl;
std::cout << "  System Uptime: " << stats.GetUptime().count() << " seconds" << std::endl;
```

### Member Information

```cpp
// Get specific member information
auto member = memberManager.GetMember(67890);
if (member) {
    std::cout << "Member Info: " << member->ToString() << std::endl;
    std::cout << "  Status: " << (member->IsOnline() ? "Online" : "Offline") << std::endl;
    std::cout << "  Class: " << static_cast<int>(member->memberClass) << std::endl;
    std::cout << "  Total Logins: " << member->totalLogins << std::endl;
}

// Get all online members
auto onlineMembers = memberManager.GetOnlineMembers();
std::cout << "Online Members (" << onlineMembers.size() << "):" << std::endl;
for (const auto& member : onlineMembers) {
    std::cout << "  " << member->memberName << " (Level " << static_cast<int>(member->level) << ")" << std::endl;
}

// Get member counts
uint32_t totalMembers = memberManager.GetMemberCount();
uint32_t onlineCount = memberManager.GetOnlineMemberCount();
std::cout << "Guild has " << totalMembers << " total members, " << onlineCount << " online" << std::endl;
```

### Legacy Compatibility

```cpp
// Legacy C-style function calls are automatically handled
extern "C" {
    void CGuild_SetGuild(void* guild, uint32_t dwSerial, uint8_t byGrade, uint8_t byRace, 
                         const char* pwszName, const char* pwszGreetingMsg, 
                         uint32_t dwEmblemBack, uint32_t dwEmblemMark, int nNum, 
                         _guild_member_info* pEstMember, double dTotalDalant, double dTotalGold,
                         uint32_t dwMasterSerial, uint8_t byMasterPrevGrade, 
                         int nIOMoneyHisNum, _io_money_data* pIOMonHisList,
                         uint32_t dwGuildBattleTotalWinCnt, uint32_t dwGuildBattleTotalDrawCnt, 
                         uint32_t dwGuildBattleTotalLoseCnt);
    
    _guild_member_info* CGuild_LoginMember(void* guild, uint32_t dwMemberSerial, CPlayer* pPtr);
    void CGuild_SendMsg_GuildMemberLogin(void* guild, uint32_t dwSerial, uint16_t wMapCode, uint16_t wRegionIndex);
    void CGuild_SendMsg_GuildMemberLogoff(void* guild, uint32_t dwSerial);
    void CGuild_SendMsg_LeaveMember(void* guild, uint32_t dwMemberSerial, bool bSelf, bool bPunish);
    bool CGuild_DB_Update_GuildMaster(void* guild, _guild_member_info* pNewguildMaster);
}

// These functions automatically delegate to the modern implementation
CGuild_SetGuild(guildPtr, 12345, 1, 0, "TestGuild", "Welcome!", 0, 0, 1, memberArray, 
                1000000.0, 500000.0, 67890, 0, 0, nullptr, 0, 0, 0);
```

## Configuration Options

### GuildMemberConfig Structure

```cpp
struct GuildMemberConfig {
    uint32_t maxMembers = 50;              // Maximum guild members
    uint32_t maxCommitteeMembers = 3;      // Maximum committee members
    bool enableMemberTracking = true;      // Enable member activity tracking
    bool enableStatistics = true;          // Enable statistics collection
    bool enableAutoPromotion = false;     // Enable automatic promotion system
    std::chrono::hours masterInactivityThreshold{504}; // 21 days for master election
};
```

### Configuration Validation

The system automatically validates configuration parameters:
- `maxMembers`: Must be between 1 and 100
- `maxCommitteeMembers`: Must be between 0 and 10
- All thresholds are validated for reasonable ranges

## Error Handling

### Result Codes

```cpp
enum class GuildMemberResult : uint8_t {
    Success = 0,              // Operation completed successfully
    MemberNotFound,           // Member not found in guild
    InvalidParameters,        // Invalid parameters provided
    PermissionDenied,         // Insufficient permissions
    GuildFull,               // Guild has reached maximum capacity
    AlreadyMember,           // Member already exists in guild
    DatabaseError,           // Database operation failed
    SystemError              // General system error
};
```

### Error Recovery

The system implements comprehensive error recovery:
- **Graceful Degradation**: Continue operation even if some members fail to load
- **Error Tracking**: Monitor consecutive errors and error rates
- **Automatic Recovery**: Reset error counters on successful operations
- **Callback Notifications**: Custom error handling through callbacks

## Performance Characteristics

### Optimizations

- **Atomic Operations**: Lock-free statistics updates where possible
- **Minimal Locking**: Reduced contention with targeted mutex usage
- **Exception Safety**: Strong exception safety guarantees
- **Memory Efficiency**: Shared pointer usage for efficient member management
- **Fast Lookups**: Hash map-based member lookups for O(1) access

### Scalability

- **Thread-Safe**: Safe for concurrent access from multiple threads
- **Configurable Limits**: Adjustable member limits based on guild capacity
- **Resource Management**: Automatic cleanup and resource management
- **Performance Monitoring**: Real-time performance metrics

## Integration

### Project Integration

The guild member manager integrates seamlessly with the existing NexusProtection architecture:

1. **Header Inclusion**: Include `CGuildMemberManager.h` in your project
2. **Namespace Usage**: Use `NexusProtection::Guild` namespace
3. **Legacy Compatibility**: Existing C-style calls work automatically
4. **Event Integration**: Connect to existing event systems through callbacks

### Dependencies

- **C++20 Standard**: Requires C++20 compatible compiler
- **STL Libraries**: Uses standard library containers and utilities
- **Legacy Guild System**: Interfaces with existing CGuild and CPlayer classes
- **Threading Support**: Requires std::thread and std::mutex support

## Best Practices

### Initialization

1. **Early Initialization**: Initialize the member manager early in application startup
2. **Configuration Validation**: Always validate configuration before use
3. **Error Checking**: Check initialization results and handle failures
4. **Resource Cleanup**: Ensure proper shutdown on application exit

### Usage Patterns

1. **Regular Monitoring**: Monitor member statistics for guild health
2. **Event Handling**: Set up callbacks for important member events
3. **Permission Checking**: Always validate permissions before operations
4. **Activity Tracking**: Monitor member activity for engagement metrics

### Performance Tips

1. **Batch Operations**: Process multiple member operations efficiently
2. **Configuration Tuning**: Adjust member limits based on server capacity
3. **Statistics Usage**: Disable statistics in production if not needed
4. **Callback Efficiency**: Keep callback functions lightweight and fast
