/*
 * Function: ?Init@CMonsterHierarchy@@QEAAXXZ
 * Address: 0x140157370
 */

void __fastcall CMonsterHierarchy::Init(CMonsterHierarchy *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  unsigned int k; // [sp+4h] [bp-14h]@6
  CMonsterHierarchy *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v5->m_dwTotalCount = 0;
  v5->m_pParentMon = 0i64;
  v5->m_dwParentSerial = -1;
  v5->m_byChildMonSetNum = 0;
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 0xA; ++k )
      v5->m_pChildMon[j][k] = 0i64;
    v5->m_dwMonCount[j] = 0;
  }
  v5->m_dwChildRecallTime = 0;
}
