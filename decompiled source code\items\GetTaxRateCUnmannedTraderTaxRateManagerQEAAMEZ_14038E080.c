/*
 * Function: ?GetTaxRate@CUnmannedTraderTaxRateManager@@QEAAME@Z
 * Address: 0x14038E080
 */

float __fastcall CUnmannedTraderTaxRateManager::GetTaxRate(CUnmannedTraderTaxRateManager *this, char byRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@6
  TRC_AutoTrade **v5; // rax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  CUnmannedTraderTaxRateManager *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byRace;
  v7 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v7->m_vecTRC)
    || std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v7->m_vecTRC) <= (unsigned __int8)v8 )
  {
    result = FLOAT_255_0;
  }
  else
  {
    v5 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v7->m_vecTRC, (unsigned __int8)v8);
    TRC_AutoTrade::get_taxrate(*v5);
  }
  return result;
}
