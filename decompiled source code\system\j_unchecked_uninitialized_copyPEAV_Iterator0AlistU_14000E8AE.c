/*
 * Function: j_??$unchecked_uninitialized_copy@PEAV?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUSchedule<PERSON>G@@K@std@@@2@@std@@PEAV123@V?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@@3@@stdext@@YAPEAV?$_Iterator@$0A@@?$list@U?$pair@QEAUScheduleMSG@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@PEAV123@00AEAV?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@QEAUSchedule<PERSON>G@@K@std@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@2@@std@@@3@@Z
 * Address: 0x14000E8AE
 */

std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *__fastcall stdext::unchecked_uninitialized_copy<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>(std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_First, std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Last, std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> *_Dest, std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0> > *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0> *,std::allocator<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
