/*
 * Function: j_?db_Load_Avator@CMainThread@@QEAAEKKPEAU_AVATOR_DATA@@PEA_NPEAK21PEAEPEAN4113_N2@Z
 * Address: 0x140005259
 */

char __fastcall CMainThread::db_Load_Avator(CMainThread *this, unsigned int dwSerial, unsigned int dwAccountSerial, _AVATOR_DATA *pData, bool *pbAddItem, unsigned int *pdwAddDalant, unsigned int *pdwAddGold, bool *pbTrunkAddItem, char *pbyTrunkOldSlot, long double *pdTrunkOldDalant, long double *pdTrunkOldGold, bool *pbCreateTrunkFree, bool *pbExtTrunkAddItem, char *pbyExtTrunkOldSlot, bool bAll, unsigned int *pdwCheckSum)
{
  return CMainThread::db_Load_Avator(
           this,
           dwSerial,
           dwAccountSerial,
           pData,
           pbAddItem,
           pdwAddDalant,
           pdwAddGold,
           pbTrunkAddItem,
           pbyTrunkOldSlot,
           pdTrunkOldDalant,
           pdTrunkOldGold,
           pbCreateTrunkFree,
           pbExtTrunkAddItem,
           pbyExtTrunkOldSlot,
           bAll,
           pdwCheckSum);
}
