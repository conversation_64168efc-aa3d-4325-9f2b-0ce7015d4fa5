/*
 * Function: ?Doit@CTalkCrystalCombineManager@@QEAA_NPEAVCPlayer@@EPEAU_list@_talik_crystal_exchange_clzo@@@Z
 * Address: 0x1404315B0
 */

char __fastcall CTalkCrystalCombineManager::Doit(CTalkCrystalCombineManager *this, CPlayer *pPlayer, char byExchang<PERSON><PERSON><PERSON>, _talik_crystal_exchange_clzo::_list *pList)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@5
  char result; // al@5
  unsigned __int16 v8; // ax@7
  int v9; // eax@14
  _STORAGE_LIST::_db_con *v10; // rcx@14
  unsigned __int16 v11; // ax@31
  __int64 v12; // [sp+0h] [bp-B8h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-98h]@14
  bool bSend; // [sp+28h] [bp-90h]@14
  _talik_crystal_exchange_zocl v15; // [sp+38h] [bp-80h]@4
  char pbyType; // [sp+74h] [bp-44h]@4
  char v17; // [sp+75h] [bp-43h]@4
  char v18; // [sp+84h] [bp-34h]@4
  int v19; // [sp+88h] [bp-30h]@8
  int j; // [sp+8Ch] [bp-2Ch]@8
  CTalkCrystalCombineManager *v21; // [sp+90h] [bp-28h]@11
  int k; // [sp+98h] [bp-20h]@12
  _talk_crystal_matrial_combine_node::_matrialinfo *v23; // [sp+A0h] [bp-18h]@14
  unsigned __int8 v24; // [sp+A8h] [bp-10h]@14
  unsigned __int8 v25; // [sp+A9h] [bp-Fh]@17
  unsigned __int8 v26; // [sp+AAh] [bp-Eh]@17
  unsigned __int8 l; // [sp+ABh] [bp-Dh]@17
  int m; // [sp+ACh] [bp-Ch]@23
  CTalkCrystalCombineManager *v29; // [sp+C0h] [bp+8h]@1
  CPlayer *pPlayera; // [sp+C8h] [bp+10h]@1
  char v31; // [sp+D0h] [bp+18h]@1
  _talik_crystal_exchange_clzo::_list *pLista; // [sp+D8h] [bp+20h]@1

  pLista = pList;
  v31 = byExchangeNum;
  pPlayera = pPlayer;
  v29 = this;
  v4 = &v12;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  _talik_crystal_exchange_zocl::_talik_crystal_exchange_zocl(&v15);
  pbyType = 7;
  v17 = 76;
  v18 = 0;
  v18 = CTalkCrystalCombineManager::CombinePreProcess(v29, pPlayera, v31, pLista);
  if ( v18 )
  {
    v15.byErrorCode = v18;
    v6 = _talik_crystal_exchange_zocl::size(&v15);
    CNetProcess::LoadSendMsg(unk_1414F2088, pPlayera->m_ObjID.m_wIndex, &pbyType, &v15.byErrorCode, v6);
    result = 0;
  }
  else
  {
    v18 = CTalkCrystalCombineManager::CombineProcess(v29);
    if ( v18 )
    {
      v15.byErrorCode = v18;
      v8 = _talik_crystal_exchange_zocl::size(&v15);
      CNetProcess::LoadSendMsg(unk_1414F2088, pPlayera->m_ObjID.m_wIndex, &pbyType, &v15.byErrorCode, v8);
      result = 0;
    }
    else
    {
      v19 = 0;
      for ( j = 0; j < 24; ++j )
      {
        v21 = (CTalkCrystalCombineManager *)((char *)v29 + 464 * j);
        if ( v21->m_NodeList[0].m_bUse )
        {
          for ( k = 0; k < v21->m_NodeList[0].m_nMatrialCount; ++k )
          {
            v23 = &v21->m_NodeList[0].m_matrialList[k];
            v24 = v21->m_NodeList[0].m_matrialList[k].m_byClientIndex;
            v15.RemainItem[v24].byRemainNum = v21->m_NodeList[0].m_matrialList[k].m_byUseCount
                                            - v21->m_NodeList[0].m_matrialList[k].m_byConsume;
            v9 = -v23->m_byConsume;
            v10 = v23->m_pMatrial;
            bSend = 0;
            LOBYTE(nLen) = 1;
            CPlayer::Emb_AlterDurPoint(pPlayera, 0, v10->m_byStorageIndex, v9, 1, 0);
          }
          if ( v21->m_NodeList[0].m_nMakeCount > 0 )
          {
            if ( IsOverLapItem(v21->m_NodeList[0].m_MakeItem.m_byTableCode) )
            {
              v21->m_NodeList[0].m_MakeItem.m_dwDur = v21->m_NodeList[0].m_nMakeCount;
              v25 = 0;
              v26 = 0;
              v25 = v21->m_NodeList[0].m_MakeItem.m_dwDur / 0x63;
              v26 = v21->m_NodeList[0].m_MakeItem.m_dwDur % 0x63;
              for ( l = 0; l < (signed int)v25; ++l )
              {
                v21->m_NodeList[0].m_MakeItem.m_dwDur = 99i64;
                v21->m_NodeList[0].m_MakeItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pPlayera->m_Param);
                CPlayer::Emb_AddStorage(
                  pPlayera,
                  0,
                  (_STORAGE_LIST::_storage_con *)&v21->m_NodeList[0].m_MakeItem.m_bLoad,
                  0,
                  1);
                CPlayer::SendMsg_RewardAddItem(pPlayera, &v21->m_NodeList[0].m_MakeItem, 0);
              }
              if ( (signed int)v26 > 0 )
              {
                v21->m_NodeList[0].m_MakeItem.m_dwDur = v26;
                v21->m_NodeList[0].m_MakeItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pPlayera->m_Param);
                CPlayer::Emb_AddStorage(
                  pPlayera,
                  0,
                  (_STORAGE_LIST::_storage_con *)&v21->m_NodeList[0].m_MakeItem.m_bLoad,
                  0,
                  1);
                CPlayer::SendMsg_RewardAddItem(pPlayera, &v21->m_NodeList[0].m_MakeItem, 0);
              }
            }
            else
            {
              for ( m = 0; m < v21->m_NodeList[0].m_nMakeCount; ++m )
              {
                v21->m_NodeList[0].m_MakeItem.m_dwDur = GetItemDurPoint(
                                                          v21->m_NodeList[0].m_MakeItem.m_byTableCode,
                                                          v21->m_NodeList[0].m_MakeItem.m_wItemIndex);
                v21->m_NodeList[0].m_MakeItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pPlayera->m_Param);
                CPlayer::Emb_AddStorage(
                  pPlayera,
                  0,
                  (_STORAGE_LIST::_storage_con *)&v21->m_NodeList[0].m_MakeItem.m_bLoad,
                  0,
                  1);
                CPlayer::SendMsg_RewardAddItem(pPlayera, &v21->m_NodeList[0].m_MakeItem, 0);
              }
            }
            ++v19;
          }
        }
      }
      if ( v19 <= 0 )
        v15.byErrorCode = 17;
      else
        v15.byErrorCode = 0;
      v11 = _talik_crystal_exchange_zocl::size(&v15);
      CNetProcess::LoadSendMsg(unk_1414F2088, pPlayera->m_ObjID.m_wIndex, &pbyType, &v15.byErrorCode, v11);
      result = 1;
    }
  }
  return result;
}
