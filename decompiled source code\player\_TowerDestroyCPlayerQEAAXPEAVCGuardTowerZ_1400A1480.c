/*
 * Function: ?_<PERSON><PERSON><PERSON><PERSON>@CPlayer@@QEAAXPEAVCGuardTower@@@Z
 * Address: 0x1400A1480
 */

void __fastcall CPlayer::_TowerD<PERSON>roy(CPlayer *this, CGuardTower *pTowerObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  bool bDelete; // [sp+20h] [bp-28h]@10
  char *strErrorCodePos; // [sp+28h] [bp-20h]@10
  _STORAGE_LIST::_db_con *pItem; // [sp+30h] [bp-18h]@4
  int j; // [sp+38h] [bp-10h]@4
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pItem = 0i64;
  for ( j = 0; j < 6; ++j )
  {
    if ( v9->m_pmTwr.m_List[j].m_pTowerObj == pTowerObj )
    {
      pItem = v9->m_pmTwr.m_List[j].m_pTowerItem;
      _TOWER_PARAM::_list::init(&v9->m_pmTwr.m_List[j]);
      --v9->m_pmTwr.m_nCount;
      break;
    }
  }
  if ( pItem )
  {
    pItem->m_bLock = 0;
    CPlayer::SendMsg_AlterTowerHP(v9, pItem->m_wSerial, 0);
    strErrorCodePos = "CPlayer::_TowerDestroy()";
    bDelete = 1;
    CPlayer::Emb_DelStorage(v9, 0, pItem->m_byStorageIndex, 0, 1, "CPlayer::_TowerDestroy()");
    CMgrAvatorItemHistory::consume_del_item(
      &CPlayer::s_MgrItemHistory,
      v9->m_ObjID.m_wIndex,
      pItem,
      v9->m_szItemHistoryFileName);
  }
}
