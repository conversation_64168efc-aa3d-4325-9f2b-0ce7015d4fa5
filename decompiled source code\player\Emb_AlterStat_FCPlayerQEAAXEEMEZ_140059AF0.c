/*
 * Function: ?Emb_AlterStat_F@CPlayer@@QEAAXEEME@Z
 * Address: 0x140059AF0
 */

void __fastcall CPlayer::Emb_AlterStat_F(CPlayer *this, char byMasteryClass, char byIndex, float fAlter, char byReason)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@5
  int v8; // eax@12
  __int64 v9; // rax@24
  __int64 v10; // [sp+0h] [bp-88h]@1
  unsigned int *pdwAfterCum; // [sp+20h] [bp-68h]@5
  char v12; // [sp+30h] [bp-58h]@4
  unsigned int dwNewStat; // [sp+44h] [bp-44h]@4
  char v14; // [sp+54h] [bp-34h]@8
  _base_fld *v15; // [sp+58h] [bp-30h]@9
  unsigned int v16; // [sp+60h] [bp-28h]@12
  int v17; // [sp+64h] [bp-24h]@17
  bool v18; // [sp+68h] [bp-20h]@18
  int v19; // [sp+6Ch] [bp-1Ch]@5
  unsigned int v20; // [sp+70h] [bp-18h]@5
  unsigned int v21; // [sp+74h] [bp-14h]@12
  CPlayer *v22; // [sp+90h] [bp+8h]@1
  char v23; // [sp+98h] [bp+10h]@1
  char v24; // [sp+A0h] [bp+18h]@1
  float v25; // [sp+A8h] [bp+20h]@1
  float v26; // [sp+A8h] [bp+20h]@17

  v25 = fAlter;
  v24 = byIndex;
  v23 = byMasteryClass;
  v22 = this;
  v5 = &v10;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = 10;
  dwNewStat = 0;
  if ( !_STAT_DB_BASE::IsRangePerMastery(byMasteryClass, byIndex) )
  {
    v19 = (unsigned __int8)v24;
    v20 = (unsigned __int8)v23;
    v7 = CPlayerDB::GetCharNameA(&v22->m_Param);
    LODWORD(pdwAfterCum) = v19;
    CLogFile::Write(&stru_1799C8E78, "%s: _STAT_DB_BASE::IsRangePerMastery(%d, %d) == false", v7, v20);
    return;
  }
  if ( !byReason )
    v25 = fAlter * (float)(((int (__fastcall *)(CPlayer *))v22->vfptr->GetLevel)(v22) / 10 + 1);
  v14 = v24;
  if ( v23 == 3 )
  {
    v15 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, (unsigned __int8)v24);
    if ( v15 )
    {
      if ( *(_DWORD *)&v15[1].m_strCode[4] >= 8 )
        return;
      v14 = v15[1].m_strCode[4];
    }
  }
  v21 = CPlayer::_check_mastery_cum_lim(v22, v23, v14);
  v8 = _MASTERY_PARAM::GetCumPerMast(&v22->m_pmMst, v23, v14);
  v16 = v21 - v8;
  if ( ((v21 - v8) & 0x80000000) != 0 )
    v16 = 0;
  if ( v25 > (float)(signed int)v16 )
    v25 = (float)(signed int)v16;
  if ( v25 > 0.0 )
  {
    v26 = v25 + 0.5;
    v17 = _STAT_DB_BASE::GetStatIndex(v23, v24);
    if ( v22->m_pmMst.m_BaseCum.m_dwDamWpCnt[v17] <= 0xEE6B2800 )
    {
      v18 = _MASTERY_PARAM::AlterCumPerMast(&v22->m_pmMst, v23, v24, (signed int)ffloor(v26), &dwNewStat);
      if ( v22->m_pmMst.m_bUpdateEquipMast )
        v22->m_bUpCheckEquipEffect = 1;
      CPlayer::SendMsg_StatInform(v22, v17, dwNewStat, byReason);
      if ( v22->m_pmMst.m_MastUpData.bUpdate )
        CPlayer::ReCalcMaxHFSP(v22, 1, 0);
      if ( v22->m_pUserDB )
        CUserDB::Update_Stat(v22->m_pUserDB, v17, dwNewStat, v18);
      v22->m_Param.m_dwAlterMastery[v17] += (signed int)ffloor(v26);
      v9 = v22->m_pmMst.m_MastUpData.bUpdate;
    }
  }
}
