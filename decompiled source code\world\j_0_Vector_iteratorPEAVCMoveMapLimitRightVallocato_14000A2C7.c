/*
 * Function: j_??0?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA@PEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x14000A2C7
 */

void __fastcall std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_Ptr)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    this,
    _Ptr);
}
