/*
 * Function: j_?guild_pop_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x14000FD76
 */

void __fastcall CMgrAvatorItemHistory::guild_pop_money(CMgrAvatorItemHistory *this, int n, char *pszGuildName, unsigned int dwPopDalant, unsigned int dwPopGold, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  CMgrAvatorItemHistory::guild_pop_money(
    this,
    n,
    pszGuildName,
    dwPopDalant,
    dwPopGold,
    dwLeftDalant,
    dwLeftGold,
    pszFileName);
}
