/*
 * Function: ?ChangeOwner@TRC_AutoTrade@@QEAAHPEAVCGuild@@@Z
 * Address: 0x1402D8C70
 */

signed __int64 __fastcall TRC_AutoTrade::ChangeOwner(TRC_AutoTrade *this, CGuild *pGuild)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  CGuild *v6; // [sp+20h] [bp-18h]@9
  TRC_AutoTrade *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pGuild )
  {
    if ( v7->m_pOwnerGuild && v7->m_pOwnerGuild->m_dwSerial == pGuild->m_dwSerial )
    {
      result = 0i64;
    }
    else
    {
      v6 = v7->m_pOwnerGuild;
      v7->m_pOwnerGuild = pGuild;
      if ( v7->m_pOwnerGuild )
        CGuild::MakeDownMemberPacket(v7->m_pOwnerGuild);
      if ( v6 )
        CGuild::MakeDownMemberPacket(v6);
      result = 0i64;
    }
  }
  else
  {
    result = 4i64;
  }
  return result;
}
