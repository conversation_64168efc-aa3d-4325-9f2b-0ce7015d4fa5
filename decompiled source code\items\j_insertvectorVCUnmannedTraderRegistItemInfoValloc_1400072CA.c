/*
 * Function: j_?insert@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@2@_KAEBVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x1400072CA
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::insert(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Where, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::insert(
    this,
    _Where,
    _Count,
    _Val);
}
