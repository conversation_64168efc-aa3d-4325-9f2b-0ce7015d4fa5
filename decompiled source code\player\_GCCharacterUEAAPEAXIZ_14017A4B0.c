/*
 * Function: ??_<PERSON><PERSON><PERSON><PERSON>@@UEAAPEAXI@Z
 * Address: 0x14017A4B0
 */

CCharacter *__fastcall CCharacter::`scalar deleting destructor'(CCharacter *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCharacter *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::~CCharacter(v6);
  if ( v7 & 1 )
    operator delete(v6);
  return v6;
}
