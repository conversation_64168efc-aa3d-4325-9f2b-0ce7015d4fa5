/*
 * Function: ?MessageEnd@BufferedTransformation@CryptoPP@@QEAA_NH_N@Z
 * Address: 0x14054F440
 */

bool __fastcall CryptoPP::BufferedTransformation::MessageEnd(CryptoPP::BufferedTransformation *this, int a2)
{
  __int64 v2; // rax@4
  int v4; // [sp+30h] [bp-18h]@2

  if ( a2 >= 0 )
    v4 = a2 + 1;
  else
    v4 = -1;
  LODWORD(v2) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *, _QWORD, _QWORD, _QWORD))this->vfptr[2].Clone)(
                  this,
                  0i64,
                  0i64,
                  (unsigned int)v4);
  return v2 != 0;
}
