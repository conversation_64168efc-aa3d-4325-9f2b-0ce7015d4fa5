/*
 * Function: ?GetRegItemInfo@CUnmannedTraderUserInfo@@QEAAPEBVCUnmannedTraderRegistItemInfo@@XZ
 * Address: 0x140366F80
 */

CUnmannedTraderRegistItemInfo *__fastcall CUnmannedTraderUserInfo::GetRegItemInfo(CUnmannedTraderUserInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *result; // rax@6
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfo *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CUnmannedTraderUserInfo::IsNull(v5)
    || std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::empty(&v5->m_vecRegistItemInfo) )
  {
    result = 0i64;
  }
  else
  {
    result = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
               &v5->m_vecRegistItemInfo,
               0i64);
  }
  return result;
}
