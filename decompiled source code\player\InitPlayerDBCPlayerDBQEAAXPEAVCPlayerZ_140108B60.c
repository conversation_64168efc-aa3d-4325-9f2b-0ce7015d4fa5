/*
 * Function: ?InitPlayerDB@CPlayerDB@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140108B60
 */

void __fastcall CPlayerDB::InitPlayerDB(CPlayerDB *this, CPlayer *pThis)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  AutominePersonalMgr *v5; // rax@15
  __int64 v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@6
  unsigned __int16 *v8; // [sp+28h] [bp-20h]@5
  unsigned __int64 v9; // [sp+30h] [bp-18h]@5
  int nIdx; // [sp+38h] [bp-10h]@15
  CPlayerDB *v11; // [sp+50h] [bp+8h]@1
  CPlayer *v12; // [sp+58h] [bp+10h]@1

  v12 = pThis;
  v11 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !v11->m_wCuttingResBuffer )
  {
    v9 = GetMaxResKind();
    v8 = (unsigned __int16 *)operator new[](saturated_mul(2ui64, v9));
    v11->m_wCuttingResBuffer = v8;
  }
  v4 = GetMaxResKind();
  memset_0(v11->m_wCuttingResBuffer, 0, 2i64 * v4);
  v11->m_pThis = v12;
  v11->m_wSerialCount = 0;
  CPlayerDB::InitResBuffer(v11);
  for ( j = 0; j < 8; ++j )
    _STORAGE_LIST::SetAllEmpty(v11->m_pStoragePtr[j]);
  _QUEST_DB_BASE::Init(&v11->m_QuestDB);
  _UNIT_DB_BASE::Init(&v11->m_UnitDB);
  _ITEMCOMBINE_DB_BASE::Init(&v11->m_ItemCombineDB);
  CPlayerDB::InitAlterMastery(v11);
  v11->m_byPvPGrade = 0;
  v11->m_pClassData = 0i64;
  for ( j = 0; j < 3; ++j )
    v11->m_pClassHistory[j] = 0i64;
  for ( j = 0; j < 50; ++j )
    _quick_link::init(&v11->m_QLink[j]);
  v11->m_ppHistoryEffect[0] = &v11->m_pClassData;
  v11->m_ppHistoryEffect[1] = v11->m_pClassHistory;
  v11->m_ppHistoryEffect[2] = &v11->m_pClassHistory[1];
  v11->m_ppHistoryEffect[3] = &v11->m_pClassHistory[2];
  v11->m_pGuild = 0i64;
  v11->m_pGuildMemPtr = 0i64;
  v11->m_pApplyGuild = 0i64;
  v11->m_byClassInGuild = -1;
  v11->m_bGuildLock = 0;
  v11->m_nMakeTrapMaxNum = 0;
  nIdx = v12->m_id.wIndex;
  v5 = AutominePersonalMgr::instance();
  v11->m_pAPM = AutominePersonalMgr::get_machine(v5, nIdx);
  if ( !v11->m_pAPM )
    v11->m_bPersonalAmineInven = 0;
  CPostStorage::Init(&v11->m_PostStorage);
  CPostReturnStorage::Init(&v11->m_ReturnPostStorage);
  *(_QWORD *)&v11->m_dPvpPointLeak = 0i64;
}
