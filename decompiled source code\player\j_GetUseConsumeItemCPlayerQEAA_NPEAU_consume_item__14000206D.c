/*
 * Function: j_?GetUseConsumeItem@CPlayer@@QEAA_NPEAU_consume_item_list@@PEAGPEAPEAU_db_con@_STORAGE_LIST@@PEAHPEA_N@Z
 * Address: 0x14000206D
 */

bool __fastcall CPlayer::GetUseConsumeItem(CPlayer *this, _consume_item_list *pConsumeList, unsigned __int16 *pItemSerials, _STORAGE_LIST::_db_con **ppConsumeItems, int *pnConsume, bool *pbOverLap)
{
  return CPlayer::GetUseConsumeItem(this, pConsumeList, pItemSerials, ppConsumeItems, pnConsume, pbOverLap);
}
