/*
 * Function: ?pc_DarkHoleClearOutRequest@CPlayer@@QEAAXXZ
 * Address: 0x140098CA0
 */

void __fastcall CPlayer::pc_DarkHoleClearOutRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  float *pfStartPos; // [sp+20h] [bp-28h]@11
  char v5; // [sp+30h] [bp-18h]@4
  _dh_player_mgr *v6; // [sp+38h] [bp-10h]@4
  CPlayer *pMember; // [sp+50h] [bp+8h]@1

  pMember = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  v6 = 0i64;
  if ( pMember->m_pCurMap->m_pMapSet->m_nMapType == 1 )
  {
    if ( pMember->m_pDHChannel )
    {
      v6 = CDarkHoleChannel::GetPlayerInfo(pMember->m_pDHChannel, pMember->m_dwObjSerial);
      if ( !v6 )
        v5 = 9;
    }
    else
    {
      v5 = 9;
    }
  }
  else
  {
    v5 = 9;
  }
  if ( !v5 )
  {
    pfStartPos = v6->LastPos.fPos;
    CPlayer::OutOfMap(pMember, v6->LastPos.pMap, 0, 4, v6->LastPos.fPos);
    CDarkHoleChannel::ClearMember(pMember->m_pDHChannel, pMember, 0, 0i64);
  }
  CPlayer::SendMsg_ClearDarkHole(pMember, v5);
}
