/*
 * Function: ?PushRenamePotionDBLog@CPotionMgr@@AEAAXPEAD@Z
 * Address: 0x14039F780
 */

void __fastcall CPotionMgr::PushRenamePotionDBLog(CPotionMgr *this, char *pInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CLogTypeDBTaskManager *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-88h]@1
  char *v6; // [sp+20h] [bp-68h]@4
  int Dst; // [sp+38h] [bp-50h]@4
  char v8; // [sp+3Ch] [bp-4Ch]@4
  char v9; // [sp+4Dh] [bp-3Bh]@4
  unsigned __int64 v10; // [sp+70h] [bp-18h]@4

  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = pInfo;
  memset_0(&Dst, 0, 0x28ui64);
  Dst = *(_DWORD *)v6;
  strcpy_s(&v8, 0x11ui64, v6 + 29);
  strcpy_s(&v9, 0x11ui64, v6 + 12);
  v4 = CLogTypeDBTaskManager::Instance();
  CLogTypeDBTaskManager::Push(v4, 2, (char *)&Dst, 0x28u);
  CLogFile::Write(&stru_1799C9438, "CPotionMgr::PushRenamePotionDBLog()!");
}
