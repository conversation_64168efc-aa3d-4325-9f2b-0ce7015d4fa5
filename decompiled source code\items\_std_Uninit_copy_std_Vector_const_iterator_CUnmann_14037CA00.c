/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____CUnmannedTraderItemCodeInfo_____ptr64_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::dtor$1
 * Address: 0x14037CA00
 */

void __fastcall std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo____CUnmannedTraderItemCodeInfo_____ptr64_std::allocator_CUnmannedTraderItemCodeInfo____::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 96));
}
