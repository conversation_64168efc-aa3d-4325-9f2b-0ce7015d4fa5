/*
 * Function: j_?Add@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAEIKKPEAPEAVCGuildBattleSchedule@2@AEAI@Z
 * Address: 0x14000BC5D
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Add(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, GUILD_BATTLE::CGuildBattleSchedule **ppkSchedule, unsigned int *uiSLID)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Add(
           this,
           uiFieldInx,
           dwStartTimeInx,
           dwElapseTimeCnt,
           ppkSchedule,
           uiSLID);
}
