/*
 * Function: ??H?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEBA?AV01@_J@Z
 * Address: 0x1402C6870
 */

std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator+(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *result, __int64 _Off)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-58h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > v8; // [sp+28h] [bp-30h]@4
  int v9; // [sp+44h] [bp-14h]@4
  __int64 v10; // [sp+48h] [bp-10h]@4
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__that; // [sp+60h] [bp+8h]@1
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v12; // [sp+68h] [bp+10h]@1
  __int64 _Offa; // [sp+70h] [bp+18h]@1

  _Offa = _Off;
  v12 = result;
  __that = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = -2i64;
  v9 = 0;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    &v8,
    __that);
  v5 = std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator+=(&v8, _Offa);
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    v12,
    v5);
  v9 |= 1u;
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(&v8);
  return v12;
}
