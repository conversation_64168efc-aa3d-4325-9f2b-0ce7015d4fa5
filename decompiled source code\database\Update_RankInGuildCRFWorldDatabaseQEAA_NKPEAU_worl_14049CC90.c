/*
 * Function: ?Update_RankInGuild@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_rankinguild_info@@@Z
 * Address: 0x14049CC90
 */

char __fastcall CRFWorldDatabase::Update_RankInGuild(CRFWorldDatabase *this, unsigned int dwGuildSerial, _worlddb_rankinguild_info *pGuildMemberRankData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-618h]@1
  void *SQLStmt; // [sp+20h] [bp-5F8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-5F0h]@25
  __int16 v9; // [sp+30h] [bp-5E8h]@9
  SQLLEN v10; // [sp+48h] [bp-5D0h]@25
  char Dest; // [sp+70h] [bp-5A8h]@4
  char v12; // [sp+71h] [bp-5A7h]@4
  char Source; // [sp+290h] [bp-388h]@4
  char v14; // [sp+291h] [bp-387h]@4
  bool v15; // [sp+314h] [bp-304h]@14
  int j; // [sp+318h] [bp-300h]@14
  int TargetValue; // [sp+324h] [bp-2F4h]@14
  int Dst; // [sp+350h] [bp-2C8h]@14
  char v19[624]; // [sp+354h] [bp-2C4h]@25
  int v20; // [sp+5C4h] [bp-54h]@36
  double v21; // [sp+5E8h] [bp-30h]@36
  int k; // [sp+5F4h] [bp-24h]@42
  unsigned __int64 v23; // [sp+600h] [bp-18h]@4
  CRFWorldDatabase *v24; // [sp+620h] [bp+8h]@1
  unsigned int v25; // [sp+628h] [bp+10h]@1
  _worlddb_rankinguild_info *v26; // [sp+630h] [bp+18h]@1

  v26 = pGuildMemberRankData;
  v25 = dwGuildSerial;
  v24 = this;
  v3 = &v6;
  for ( i = 388i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v23 = (unsigned __int64)&v6 ^ _security_cookie;
  Dest = 0;
  memset(&v12, 0, 0x1FFui64);
  Source = 0;
  memset(&v14, 0, 0x7Fui64);
  if ( v24->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v24->vfptr, "Update_RankInGuild Start");
  if ( v24->m_hStmtUpdate || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
  {
    sprintf(&Dest, "select top %u IDENTITY(int, 1, 1) AS Rank, -1 as Rate, -1 as NewGrade, ", 50i64);
    strcat_0(&Dest, "b.serial, b.lv, g.Pvppoint, g.GuildGrade as CurGrade into #tbl_RankInGuild ");
    strcat_0(&Dest, "from tbl_general as g join tbl_base as b on g.serial = b.serial ");
    sprintf(&Source, "where g.guildserial=%d and b.dck=0 order by g.Pvppoint desc", v25);
    strcat_0(&Dest, &Source);
    v9 = SQLExecDirect_0(v24->m_hStmtUpdate, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuild", 0);
        result = 0;
      }
      else
      {
        CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuild", 0);
        SQLStmt = v24->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
        result = 0;
      }
    }
    else
    {
      v15 = 0;
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set Rate = ( (Rank*10000)/(select count(*) from #tbl_RankInGuild) )",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "update #tbl_RankInGuild set NewGrade=0", 0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=3 where lv >= 30 and lv <= 34 and rate <= 6500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=2 where lv >= 30 and lv <= 34 and rate > 6500 and rate <= 8500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=1 where lv >= 30 and lv <= 34 and rate > 8500 and rate <= 9500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=4 where lv >= 35 and lv <= 39 and rate <= 3500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=3 where lv >= 35 and lv <= 39 and rate > 3500 and rate <= 6500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=2 where lv >= 35 and lv <= 39 and rate > 6500 and rate <= 8500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=1 where lv >= 35 and lv <= 39 and rate > 8500 and rate <= 9500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=5 where lv >= 40 and lv <= 44 and rate <= 1500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=4 where lv >= 40 and lv <= 44 and rate > 1500 and rate <= 3500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=3 where lv >= 40 and lv <= 44 and rate > 3500 and rate <= 6500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=2 where lv >= 40 and lv <= 44 and rate > 6500 and rate <= 8500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=1 where lv >= 40 and lv <= 44 and rate > 8500 and rate <= 9500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=6 where lv >= 45 and lv <= 49 and rate <= 500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=5 where lv >= 45 and lv <= 49 and rate > 500 and rate <= 1500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=4 where lv >= 45 and lv <= 49 and rate > 1500 and rate <= 3500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=3 where lv >= 45 and lv <= 49 and rate > 3500 and rate <= 6500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=2 where lv >= 45 and lv <= 49 and rate > 6500 and rate <= 8500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=1 where lv >= 45 and lv <= 49 and rate > 8500 and rate <= 9500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=7 where lv >= 50 and rate <= 100",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=6 where lv >= 50 and rate > 100 and rate <= 500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=5 where lv >= 50 and rate > 500 and rate <= 1500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=4 where lv >= 50 and rate > 1500 and rate <= 3500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=3 where lv >= 50 and rate > 3500 and rate <= 6500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=2 where lv >= 50 and rate > 6500 and rate <= 8500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuild set NewGrade=1 where lv >= 50 and rate > 8500 and rate <= 9500",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "select IDENTITY(int, 1, 1) AS NewRank, -1 as Grade, serial, CurGrade, lv, Pvppoint into #tbl_RankInGuildAl"
              "l from #tbl_RankInGuild order by NewGrade desc, rate ",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "select IDENTITY(int, 1, 1) AS NewRank, -1 as NewRate, -1 as Grade, serial, CurGrade, lv, PvpPoint into #tb"
              "l_RankInGuildCom from #tbl_RankInGuild where CurGrade <> 0 and CurGrade <> 3 order by NewGrade desc, rate ",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuildCom set NewRate = ( (NewRank*10000)/(select count(*) from #tbl_RankInGuildCom) ) ",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuildCom set Grade = 2",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update #tbl_RankInGuildCom set Grade = 1 where NewRate <= 1000",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update tbl_general set GuildGrade = Grade from ( select serial, Grade from #tbl_RankInGuildCom ) as rank w"
              "here tbl_general.serial = rank.serial",
              0);
      v15 = CRFNewDatabase::ExecUpdateQuery(
              (CRFNewDatabase *)&v24->vfptr,
              "update tbl_general set GuildRank = rank.NewRank\tfrom ( select serial, NewRank from #tbl_RankInGuildAll ) "
              "as rank where tbl_general.serial = rank.serial",
              0);
      j = 0;
      TargetValue = 2;
      memset_0(&Dst, 0, 0x258ui64);
      sprintf(&Dest, "select serial, Grade from #tbl_RankInGuildCom order by Grade");
      if ( v24->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
      {
        v9 = SQLExecDirect_0(v24->m_hStmtSelect, &Dest, -3);
        if ( v9 && v9 != 1 )
        {
          CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuild", 0);
          CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuildAll", 0);
          CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuildCom", 0);
          if ( v9 == 100 )
          {
            result = 0;
          }
          else
          {
            SQLStmt = v24->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v9, v24->m_hStmtSelect);
            result = 0;
          }
        }
        else
        {
          for ( j = 0; ; ++j )
          {
            v9 = SQLFetch_0(v24->m_hStmtSelect);
            if ( v9 )
            {
              if ( v9 != 1 )
                break;
            }
            StrLen_or_IndPtr = &v10;
            SQLStmt = 0i64;
            v9 = SQLGetData_0(v24->m_hStmtSelect, 1u, 4, &Dst + 3 * j, 0i64, &v10);
            StrLen_or_IndPtr = &v10;
            SQLStmt = 0i64;
            v9 = SQLGetData_0(v24->m_hStmtSelect, 2u, 4, &TargetValue, 0i64, &v10);
            v19[12 * j] = TargetValue;
          }
          if ( v24->m_hStmtSelect )
            SQLCloseCursor_0(v24->m_hStmtSelect);
          sprintf(&Dest, "select serial, lv, Pvppoint, CurGrade from #tbl_RankInGuildAll order by NewRank");
          if ( v24->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
          {
            v9 = SQLExecDirect_0(v24->m_hStmtSelect, &Dest, -3);
            if ( v9 && v9 != 1 )
            {
              CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuild", 0);
              CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuildAll", 0);
              CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuildCom", 0);
              if ( v9 == 100 )
              {
                result = 0;
              }
              else
              {
                SQLStmt = v24->m_hStmtSelect;
                CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
                CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v9, v24->m_hStmtSelect);
                result = 0;
              }
            }
            else
            {
              j = 0;
              v20 = 0;
              v21 = 0.0;
              while ( 1 )
              {
                v9 = SQLFetch_0(v24->m_hStmtSelect);
                if ( v9 )
                {
                  if ( v9 != 1 )
                    break;
                }
                StrLen_or_IndPtr = &v10;
                SQLStmt = 0i64;
                v9 = SQLGetData_0(v24->m_hStmtSelect, 1u, 4, &v26->MemberData[j], 0i64, &v10);
                StrLen_or_IndPtr = &v10;
                SQLStmt = 0i64;
                v9 = SQLGetData_0(v24->m_hStmtSelect, 2u, -18, &v20, 0i64, &v10);
                StrLen_or_IndPtr = &v10;
                SQLStmt = 0i64;
                v9 = SQLGetData_0(v24->m_hStmtSelect, 3u, 8, &v21, 0i64, &v10);
                StrLen_or_IndPtr = &v10;
                SQLStmt = 0i64;
                v9 = SQLGetData_0(v24->m_hStmtSelect, 4u, 4, &v26->MemberData[j].byGrade, 0i64, &v10);
                v26->MemberData[j].byLv = v20;
                v26->MemberData[j].dwPvpPoint = (signed int)floor(v21);
                if ( v26->MemberData[j].byGrade == 2 )
                  v26->MemberData[j].byGrade = 2;
                else
                  v26->MemberData[j].byGrade = 0;
                for ( k = 0; k < 50; ++k )
                {
                  if ( v26->MemberData[j].dwSerial == *(&Dst + 3 * k) )
                  {
                    v26->MemberData[j].byGrade = v19[12 * k];
                    break;
                  }
                }
                ++j;
              }
              v26->wRecordCount = j;
              if ( v24->m_hStmtSelect )
                SQLCloseCursor_0(v24->m_hStmtSelect);
              CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuild", 0);
              CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuildAll", 0);
              CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v24->vfptr, "drop table #tbl_RankInGuildCom", 0);
              result = 1;
            }
          }
          else
          {
            CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
            result = 0;
          }
        }
      }
      else
      {
        CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
