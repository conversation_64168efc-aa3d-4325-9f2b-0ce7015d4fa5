/*
 * Function: ?db_buy_emblem@CMainThread@@QEAAEKHKKKPEAN0PEAEPEAD1@Z
 * Address: 0x1401B1E60
 */

char __fastcall CMainThread::db_buy_emblem(CMainThread *this, unsigned int dwGuildSerial, int nEmblemDalant, unsigned int dwEmblemBack, unsigned int dwEmblemMark, unsigned int dwSuggestorSerial, long double *dTotalDalant, long double *dTotalGold, char *byDate, char *pwszName, char *pbyProcRet)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v14; // ecx@10
  unsigned int v15; // edx@10
  unsigned int v16; // edi@10
  __int64 v17; // [sp+0h] [bp-E8h]@1
  unsigned int v18[2]; // [sp+20h] [bp-C8h]@10
  long double dResultGold; // [sp+28h] [bp-C0h]@10
  char *wszDate; // [sp+30h] [bp-B8h]@10
  unsigned int dwAvatorSerial; // [sp+38h] [bp-B0h]@14
  char *v22; // [sp+40h] [bp-A8h]@14
  char v23; // [sp+50h] [bp-98h]@4
  char DstBuf; // [sp+68h] [bp-80h]@10
  char v25; // [sp+69h] [bp-7Fh]@10
  CCheckSumGuildData v26; // [sp+98h] [bp-50h]@12
  char v27; // [sp+C0h] [bp-28h]@13
  char v28; // [sp+C1h] [bp-27h]@15
  char v29; // [sp+C2h] [bp-26h]@16
  __int64 v30; // [sp+C8h] [bp-20h]@4
  unsigned __int64 v31; // [sp+D0h] [bp-18h]@4
  CMainThread *v32; // [sp+F0h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+F8h] [bp+10h]@1
  int v34; // [sp+100h] [bp+18h]@1
  unsigned int dwEmblemBacka; // [sp+108h] [bp+20h]@1

  dwEmblemBacka = dwEmblemBack;
  v34 = nEmblemDalant;
  dwGuildSeriala = dwGuildSerial;
  v32 = this;
  v11 = &v17;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  v30 = -2i64;
  v31 = (unsigned __int64)&v17 ^ _security_cookie;
  v23 = CMainThread::check_min_max_guild_money(v32, dwGuildSerial, dTotalDalant, dTotalGold);
  if ( v23 )
  {
    result = v23;
  }
  else if ( *dTotalDalant == 0.0 && v34 || (double)-v34 > *dTotalDalant )
  {
    *pbyProcRet = 1;
    result = 0;
  }
  else
  {
    *dTotalDalant = *dTotalDalant + (double)v34;
    DstBuf = 0;
    memset(&v25, 0, 8ui64);
    v14 = (unsigned __int8)byDate[2];
    v15 = (unsigned __int8)byDate[1];
    v16 = (unsigned __int8)*byDate;
    LODWORD(wszDate) = (unsigned __int8)byDate[3];
    LODWORD(dResultGold) = v14;
    v18[0] = v15;
    sprintf_s(&DstBuf, 9ui64, "%02d%02d%02d%02d", v16);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v32->m_pWorldDB->vfptr, 0);
    v18[0] = dwEmblemMark;
    if ( CRFWorldDatabase::Update_GuildEmblem(
           v32->m_pWorldDB,
           dwGuildSeriala,
           *dTotalDalant,
           dwEmblemBacka,
           dwEmblemMark) )
    {
      CCheckSumGuildData::CCheckSumGuildData(&v26, dwGuildSeriala);
      CCheckSumGuildData::Encode(&v26, *dTotalDalant, *dTotalGold);
      if ( CCheckSumGuildData::Update(&v26, v32->m_pWorldDB) )
      {
        v22 = pwszName;
        dwAvatorSerial = dwSuggestorSerial;
        wszDate = &DstBuf;
        dResultGold = *dTotalGold;
        *(long double *)v18 = *dTotalDalant;
        if ( CRFWorldDatabase::Insert_GuildMoneyHistory(
               v32->m_pWorldDB,
               dwGuildSeriala,
               (double)v34,
               0.0,
               *(long double *)v18,
               dResultGold,
               &DstBuf,
               dwSuggestorSerial,
               pwszName) )
        {
          CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v32->m_pWorldDB->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v32->m_pWorldDB->vfptr, 1);
          v29 = 0;
          CCheckSumGuildData::~CCheckSumGuildData(&v26);
          result = v29;
        }
        else
        {
          CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v32->m_pWorldDB->vfptr);
          CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v32->m_pWorldDB->vfptr, 1);
          v28 = 24;
          CCheckSumGuildData::~CCheckSumGuildData(&v26);
          result = v28;
        }
      }
      else
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v32->m_pWorldDB->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v32->m_pWorldDB->vfptr, 1);
        v27 = 24;
        CCheckSumGuildData::~CCheckSumGuildData(&v26);
        result = v27;
      }
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v32->m_pWorldDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v32->m_pWorldDB->vfptr, 1);
      result = 24;
    }
  }
  return result;
}
