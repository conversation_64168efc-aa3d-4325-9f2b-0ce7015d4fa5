/*
 * Function: j_??$_Uninit_move@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000B785
 */

CMoveMapLimitInfo **__fastcall std::_Uninit_move<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>,std::_Undefined_move_tag>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest, std::allocator<CMoveMapLimitInfo *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
