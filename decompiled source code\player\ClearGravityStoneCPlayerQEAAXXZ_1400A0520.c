/*
 * Function: ?ClearGravityStone@CPlayer@@QEAAXXZ
 * Address: 0x1400A0520
 */

void __fastcall CPlayer::ClearGravityStone(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPlayer *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bTakeGravityStone = 0;
  _effect_parameter::SetEff_Plus(&v4->m_EP, 3, -30.0, 0);
  CPlayer::ApplyEquipItemEffect(v4, 12, 1);
}
