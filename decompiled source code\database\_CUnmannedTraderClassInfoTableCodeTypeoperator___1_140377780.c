/*
 * Function: _CUnmannedTraderClassInfoTableCodeType::operator__::_1_::dtor$1
 * Address: 0x140377780
 */

void __fastcall CUnmannedTraderClassInfoTableCodeType::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 88));
}
