/*
 * Function: j_??$_Fill_n@PEAPEAVCLogTypeDBTask@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCLogTypeDBTask@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400014D8
 */

void __fastcall std::_Fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *,std::random_access_iterator_tag>(CLogTypeDBTask **_First, unsigned __int64 _Count, CLogTypeDBTask *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CLogTypeDBTask * *,unsigned __int64,CLogTypeDBTask *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
