/*
 * Function: ?pc_PlayAttack_SelfDestruction@CPlayer@@QEAAXXZ
 * Address: 0x140084E10
 */

void __fastcall CPlayer::pc_PlayAttack_SelfDestruction(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@24
  float v4; // xmm0_4@24
  float v5; // xmm0_4@24
  float v6; // xmm0_4@24
  int v7; // eax@30
  __int64 v8; // [sp+0h] [bp-4E8h]@1
  bool v9; // [sp+20h] [bp-4C8h]@30
  int v10; // [sp+28h] [bp-4C0h]@30
  int v11; // [sp+30h] [bp-4B8h]@30
  char v12; // [sp+38h] [bp-4B0h]@30
  char v13; // [sp+40h] [bp-4A8h]@4
  CAttack pAt; // [sp+60h] [bp-488h]@24
  _attack_param Dst; // [sp+370h] [bp-178h]@24
  CPartyModeKillMonsterExpNotify kPartyExpNotify; // [sp+410h] [bp-D8h]@26
  int v17; // [sp+4A4h] [bp-44h]@28
  int v18; // [sp+4A8h] [bp-40h]@28
  int j; // [sp+4ACh] [bp-3Ch]@28
  __int64 v20; // [sp+4B0h] [bp-38h]@4
  float v21; // [sp+4B8h] [bp-30h]@24
  float v22; // [sp+4BCh] [bp-2Ch]@24
  float v23; // [sp+4C0h] [bp-28h]@24
  float v24; // [sp+4C4h] [bp-24h]@24
  CCharacter *v25; // [sp+4C8h] [bp-20h]@30
  CGameObjectVtbl *v26; // [sp+4D0h] [bp-18h]@30
  CPlayer *v27; // [sp+4F0h] [bp+8h]@1

  v27 = this;
  v1 = &v8;
  for ( i = 312i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v20 = -2i64;
  v13 = 0;
  if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v27->vfptr->IsAttackableInTown)(v27)
    && (unsigned __int8)((int (__fastcall *)(CPlayer *))v27->vfptr->IsInTown)(v27) )
  {
    v13 = -31;
  }
  if ( v27->m_pmWpn.byWpType == 11 || v27->m_pmWpn.byWpType == 10 )
    v13 = -9;
  if ( CPlayer::IsRidingUnit(v27) )
    v13 = -21;
  if ( _effect_parameter::GetEff_State(&v27->m_EP, 20) )
    v13 = -37;
  if ( _effect_parameter::GetEff_State(&v27->m_EP, 28) )
    v13 = -37;
  if ( _effect_parameter::GetEff_State(&v27->m_EP, 21) )
    v13 = -38;
  if ( v27->m_byMoveType == 2 )
    v13 = -41;
  if ( v13 )
  {
    CPlayer::SendMsg_AttackResult_Error(v27, v13);
    if ( v27->m_bMove )
    {
      CCharacter::Stop((CCharacter *)&v27->vfptr);
      CGameObject::SendMsg_BreakStop((CGameObject *)&v27->vfptr);
    }
  }
  else
  {
    CAttack::CAttack(&pAt, (CCharacter *)&v27->vfptr);
    _attack_param::_attack_param(&Dst);
    Dst.pDst = 0i64;
    memcpy_0(Dst.fArea, v27->m_fCurPos, 0xCui64);
    Dst.nPart = CCharacter::GetAttackRandomPart((CCharacter *)&v27->vfptr);
    Dst.nTol = -1;
    Dst.nClass = 1;
    v21 = (float)v27->m_pmWpn.nGaMinAF;
    v3 = v21;
    _effect_parameter::GetEff_Rate(&v27->m_EP, 32);
    v4 = v21 * v3;
    v22 = v4;
    _effect_parameter::GetEff_Rate(&v27->m_EP, 29);
    Dst.nMinAF = (signed int)ffloor((float)(v22 * v4) * v27->m_fSelfDestructionDamage);
    v23 = (float)v27->m_pmWpn.nGaMaxAF;
    v5 = v23;
    _effect_parameter::GetEff_Rate(&v27->m_EP, 32);
    v6 = v23 * v5;
    v24 = v6;
    _effect_parameter::GetEff_Rate(&v27->m_EP, 29);
    Dst.nMaxAF = (signed int)ffloor((float)(v24 * v6) * v27->m_fSelfDestructionDamage);
    Dst.nMaxAttackPnt = v27->m_nMaxAttackPnt;
    Dst.nMinSel = 30;
    Dst.nMaxSel = 50;
    Dst.nAttactType = 6;
    Dst.nExtentRange = 110;
    CAttack::AttackGen(&pAt, &Dst, 0, 0);
    if ( _effect_parameter::GetEff_State(&v27->m_EP, 14) )
      CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v27->vfptr, 2, 14);
    CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
    if ( !pAt.m_bFailure )
      CPlayer::_check_exp_after_attack(v27, pAt.m_nDamagedObjNum, pAt.m_DamList, &kPartyExpNotify);
    CPlayer::SendMsg_AttackResult_SelfDestruction(v27, &pAt);
    CPlayer::SetBattleMode(v27, 1);
    CPartyModeKillMonsterExpNotify::Notify(&kPartyExpNotify);
    v17 = 0;
    v18 = 0;
    for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
    {
      v7 = CPlayerDB::GetLevel(&v27->m_Param);
      v25 = pAt.m_DamList[j].m_pChar;
      v26 = v25->vfptr;
      v12 = 1;
      v11 = 0;
      v10 = -1;
      v9 = pAt.m_bIsCrtAtt;
      ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v26->SetDamage)(
        v25,
        pAt.m_DamList[j].m_nDamage,
        v27,
        (unsigned int)v7);
    }
    v27->m_byMoveType = 0;
    CPlayer::SenseState(v27);
    ((void (__fastcall *)(CPlayer *, signed __int64, _QWORD))v27->vfptr->SetHP)(v27, 1i64, 0i64);
    CPlayer::SetFP(v27, 1, 0);
    CPlayer::SetSP(v27, 1, 0);
    (*(void (__fastcall **)(CPlayer *))&v27->vfptr->gap8[72])(v27);
    CPlayer::SendMsg_SetFPInform(v27);
    CPlayer::SendMsg_SetSPInform(v27);
    CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
  }
}
