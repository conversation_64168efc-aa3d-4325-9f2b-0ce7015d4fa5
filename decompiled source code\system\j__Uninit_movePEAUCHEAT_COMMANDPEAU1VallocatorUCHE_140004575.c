/*
 * Function: j_??$_Uninit_move@PEAUCHEAT_COMMAND@@PEAU1@V?$allocator@UCHEAT_COMMAND@@@std@@U_Undefined_move_tag@3@@std@@YAPEAUCHEAT_COMMAND@@PEAU1@00AEAV?$allocator@UCHEAT_COMMAND@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140004575
 */

CHEAT_COMMAND *__fastcall std::_Uninit_move<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>,std::_Undefined_move_tag>(CHEAT_COMMAND *_First, CHEAT_COMMAND *_Last, CHEAT_COMMAND *_Dest, std::allocator<CHEAT_COMMAND> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
