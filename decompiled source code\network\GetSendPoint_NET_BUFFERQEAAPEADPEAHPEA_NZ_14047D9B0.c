/*
 * Function: ?GetSendPoint@_NET_BUFFER@@QEAAPEADPEAHPEA_N@Z
 * Address: 0x14047D9B0
 */

char *__fastcall _NET_BUFFER::GetSendPoint(_NET_BUFFER *this, int *pnSendSize, bool *pMiss)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@4
  size_t Size; // [sp+24h] [bp-14h]@7
  _NET_BUFFER *v9; // [sp+40h] [bp+8h]@1
  int *v10; // [sp+48h] [bp+10h]@1
  bool *v11; // [sp+50h] [bp+18h]@1

  v11 = pMiss;
  v10 = pnSendSize;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *pnSendSize = _NET_BUFFER::GetLeftLoadSize(v9);
  v7 = v9->m_dwPopPnt;
  if ( *v10 > 0 )
  {
    if ( *v10 + v7 <= v9->m_nMaxSize )
    {
      result = &v9->m_sMainBuffer[v7];
    }
    else
    {
      LODWORD(Size) = v9->m_nMaxSize - v7;
      if ( (unsigned int)Size <= v9->m_nEtrSize )
      {
        memcpy_0(v9->m_sTempBuffer, &v9->m_sMainBuffer[v7], (unsigned int)Size);
        memcpy_0(&v9->m_sTempBuffer[(unsigned int)Size], v9->m_sMainBuffer, (unsigned int)(*v10 - Size));
        result = v9->m_sTempBuffer;
      }
      else
      {
        *v11 = 1;
        result = 0i64;
      }
    }
  }
  else
  {
    *v11 = 0;
    result = 0i64;
  }
  return result;
}
