/*
 * Function: ?ct_set_animus_lv@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295CC0
 */

bool __fastcall ct_set_animus_lv(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v4; // eax@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( s_nWordCount <= 2 )
  {
    v4 = atoi(s_pwszDstCheat[0]);
    result = CPlayer::dev_set_animus_lv(v6, v4);
  }
  else
  {
    result = 0;
  }
  return result;
}
