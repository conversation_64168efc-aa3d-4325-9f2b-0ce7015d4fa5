/*
 * Function: ?Update_BattleResultLogBattleResultAndPvpPoint@CRFWorldDatabase@@QEAA_NKKK@Z
 * Address: 0x1404B1100
 */

bool __fastcall CRFWorldDatabase::Update_BattleResultLogBattleResultAndPvpPoint(CRFWorldDatabase *this, unsigned int kLogSerial, unsigned int dwRedSerial, unsigned int dwBlueSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-468h]@1
  unsigned int v8; // [sp+20h] [bp-448h]@4
  char _Dest[1024]; // [sp+40h] [bp-428h]@4
  unsigned __int64 v10; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+470h] [bp+8h]@1

  v11 = this;
  v4 = &v7;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = dwBlueSerial;
  sprintf_s<1024>((char (*)[1024])_Dest, "{ CALL pUpdate_guildbattleresultlog( %u, %u, %u ) }", kLogSerial, dwRedSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, _Dest, 1);
}
