/*
 * Function: ?Init@CPostData@@QEAAXXZ
 * Address: 0x140322790
 */

void __fastcall CPostData::Init(CPostData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPostData *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_nNumber = 0;
  v4->m_byState = -1;
  v4->m_byErrCode = 0;
  v4->m_bySendRace = 0;
  v4->m_dwSenderSerial = 0;
  memset_0(v4->m_wszSendName, 0, 0x11ui64);
  memset_0(v4->m_wszRecvName, 0, 0x11ui64);
  memset_0(v4->m_wszTitle, 0, 0x15ui64);
  memset_0(v4->m_wszContent, 0, 0xC9ui64);
  _INVENKEY::SetRelease(&v4->m_Key);
  v4->m_dwDur = 0i64;
  v4->m_dwUpt = 0xFFFFFFF;
  v4->m_dwGold = 0;
  v4->m_dwPSSerial = 0;
  v4->m_lnUID = 0i64;
  v4->m_bContentLoad = 0;
  v4->m_bUpdateIndex = 0;
}
