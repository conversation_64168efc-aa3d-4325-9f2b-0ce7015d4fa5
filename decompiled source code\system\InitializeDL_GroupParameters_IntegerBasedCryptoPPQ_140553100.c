/*
 * Function: ?Initialize@DL_GroupParameters_IntegerBased@CryptoPP@@QEAAXAEBVInteger@2@00@Z
 * Address: 0x140553100
 */

void __fastcall CryptoPP::DL_GroupParameters_IntegerBased::Initialize(CryptoPP::DL_GroupParameters_IntegerBased *this, const struct CryptoPP::Integer *a2, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v4; // [sp+30h] [bp+8h]@1
  struct CryptoPP::Integer *v5; // [sp+40h] [bp+18h]@1

  v5 = (struct CryptoPP::Integer *)a3;
  v4 = this;
  ((void (__fastcall *)(_QWORD, _QWORD, _QWORD))this->vfptr[1].BERDecode)(this, a2, a4);
  CryptoPP::DL_GroupParameters_IntegerBased::SetSubgroupOrder(v4, v5);
}
