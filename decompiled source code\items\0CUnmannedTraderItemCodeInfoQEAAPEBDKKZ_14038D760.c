/*
 * Function: ??0CUnmannedTraderItemCodeInfo@@QEAA@PEBDKK@Z
 * Address: 0x14038D760
 */

void __fastcall CUnmannedTraderItemCodeInfo::CUnmannedTraderItemCodeInfo(CUnmannedTraderItemCodeInfo *this, const char *szCode, unsigned int dwStartIndex, unsigned int dwEndIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderItemCodeInfo *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7->m_dwStartInx = dwStartIndex;
  v7->m_dwEndInx = dwEndIndex;
  v7->m_szCode[0] = 0;
  if ( szCode )
  {
    strcpy_0(v7->m_szCode, szCode);
    v7->m_szCode[63] = 0;
  }
}
