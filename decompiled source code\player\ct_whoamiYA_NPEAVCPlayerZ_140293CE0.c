/*
 * Function: ?ct_whoami@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293CE0
 */

char __fastcall ct_whoami(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CPlayer *v5; // [sp+50h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5 )
  {
    sprintf(wszRespon, "Grade:%d / SubGrade:%d", v5->m_byUserDgr, v5->m_bySubDgr);
    CPlayer::SendData_ChatTrans(v5, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
