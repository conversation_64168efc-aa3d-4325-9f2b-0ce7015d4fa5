/*
 * Function: ?_Assign_n@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderUserInfo@@@Z
 * Address: 0x1403673F0
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Assign_n(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, unsigned __int64 _Count, CUnmannedTraderUserInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-158h]@1
  CUnmannedTraderUserInfo v6; // [sp+30h] [bp-128h]@4
  char v7; // [sp+A8h] [bp-B0h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *result; // [sp+C0h] [bp-98h]@4
  char v9; // [sp+C8h] [bp-90h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v10; // [sp+E0h] [bp-78h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > v11; // [sp+E8h] [bp-70h]@4
  char v12; // [sp+100h] [bp-58h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v13; // [sp+118h] [bp-40h]@4
  __int64 v14; // [sp+120h] [bp-38h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v15; // [sp+128h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v16; // [sp+130h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v17; // [sp+138h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v18; // [sp+140h] [bp-18h]@4
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v19; // [sp+160h] [bp+8h]@1
  unsigned __int64 v20; // [sp+168h] [bp+10h]@1

  v20 = _Count;
  v19 = this;
  v3 = &v5;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  CUnmannedTraderUserInfo::CUnmannedTraderUserInfo(&v6, _Val);
  result = (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v7;
  v10 = (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v9;
  v15 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::end(
          v19,
          (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v7);
  v16 = v15;
  v17 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::begin(v19, v10);
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::erase(v19, &v11, v17, v16);
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(&v11);
  v13 = (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v12;
  v18 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::begin(
          v19,
          (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v12);
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::insert(v19, v18, v20, &v6);
  CUnmannedTraderUserInfo::~CUnmannedTraderUserInfo(&v6);
}
