/*
 * Function: j_??$_Uninit_copy@PEAEPEAEV?$allocator@E@std@@@std@@YAPEAEPEAE00AEAV?$allocator@E@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140012206
 */

char *__fastcall std::_Uninit_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
