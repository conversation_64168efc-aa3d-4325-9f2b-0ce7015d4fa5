/*
 * Function: ?dev_set_animus_exp@CPlayer@@QEAA_N_K@Z
 * Address: 0x1400BC8B0
 */

char __fastcall CPlayer::dev_set_animus_exp(CPlayer *this, unsigned __int64 dwExpPoint)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-98h]@1
  bool bUpdate[2]; // [sp+20h] [bp-78h]@13
  int j; // [sp+30h] [bp-68h]@6
  char szMsg[2]; // [sp+48h] [bp-50h]@14
  __int64 v9; // [sp+4Ah] [bp-4Eh]@14
  char pbyType; // [sp+74h] [bp-24h]@14
  char v11; // [sp+75h] [bp-23h]@14
  CPlayer *v12; // [sp+A0h] [bp+8h]@1
  __int64 amount; // [sp+A8h] [bp+10h]@1

  amount = dwExpPoint;
  v12 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v12->m_pUserDB )
  {
    for ( j = 0; j < 4; ++j )
    {
      if ( v12->m_Param.m_dbAnimus.m_pStorageList[j].m_bLoad
        && !v12->m_Param.m_dbAnimus.m_pStorageList[j].m_bLock
        && v12->m_Param.m_dbAnimus.m_pStorageList[j].m_dwDur != amount )
      {
        v12->m_Param.m_dbAnimus.m_pStorageList[j].m_dwDur = amount;
        if ( v12->m_pUserDB )
        {
          bUpdate[0] = 0;
          CUserDB::Update_ItemDur(v12->m_pUserDB, 4, j, amount, 0);
        }
        *(_WORD *)szMsg = v12->m_Param.m_dbAnimus.m_pStorageList[j].m_wSerial;
        v9 = amount;
        pbyType = 22;
        v11 = 11;
        CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 0xAu);
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
