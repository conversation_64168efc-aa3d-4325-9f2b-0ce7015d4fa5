/*
 * Function: ?Flip@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403D41C0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Flip(GUILD_BATTLE::CNormalGuildBattleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattle **v3; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = (__int64 *)&v3;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = v4->m_ppkTomorrowBattle;
  v4->m_ppkTomorrowBattle = v4->m_ppkTodayBattle;
  v4->m_ppkTodayBattle = v3;
}
