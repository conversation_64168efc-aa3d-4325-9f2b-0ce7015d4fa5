/*
 * Function: ?FindRegist@CUnmannedTraderUserInfo@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@V23@@Z
 * Address: 0x140359F20
 */

std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall CUnmannedTraderUserInfo::FindRegist(CUnmannedTraderUserInfo *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *iterStart)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v5; // rax@6
  CUnmannedTraderRegistItemInfo *v6; // rax@7
  __int64 v8; // [sp+0h] [bp-68h]@1
  bool v9; // [sp+20h] [bp-48h]@5
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > resulta; // [sp+28h] [bp-40h]@5
  int v11; // [sp+40h] [bp-28h]@4
  __int64 v12; // [sp+48h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v13; // [sp+50h] [bp-18h]@5
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+58h] [bp-10h]@5
  CUnmannedTraderUserInfo *v15; // [sp+70h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v16; // [sp+78h] [bp+10h]@1
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__that; // [sp+80h] [bp+18h]@1

  __that = iterStart;
  v16 = result;
  v15 = this;
  v3 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = -2i64;
  v11 = 0;
  while ( 1 )
  {
    v13 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
            &v15->m_vecRegistItemInfo,
            &resulta);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v13;
    v9 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator!=(
           (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&__that->_Mycont,
           (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v13->_Mycont);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&resulta);
    if ( !v9 )
      break;
    v5 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(__that);
    if ( CUnmannedTraderRegistItemInfo::GetState(v5) == 1
      || (v6 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(__that),
          CUnmannedTraderRegistItemInfo::GetState(v6) == 2) )
    {
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
        v16,
        __that);
      v11 |= 1u;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(__that);
      return v16;
    }
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator++(__that);
  }
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
    &v15->m_vecRegistItemInfo,
    v16);
  v11 |= 1u;
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(__that);
  return v16;
}
