/*
 * Function: ?Initialize@?$DL_PrivateKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@CryptoPP@@QEAAXAEBVInteger@2@000@Z
 * Address: 0x140553960
 */

int __fastcall CryptoPP::DL_PrivateKey_GFP<CryptoPP::DL_GroupParameters_DSA>::Initialize(__int64 a1, struct CryptoPP::Integer *a2, struct CryptoPP::Integer *a3, struct CryptoPP::Integer *a4, __int64 a5)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v5; // rax@1
  __int64 v7; // [sp+30h] [bp+8h]@1
  struct CryptoPP::Integer *v8; // [sp+38h] [bp+10h]@1
  struct CryptoPP::Integer *v9; // [sp+40h] [bp+18h]@1
  struct CryptoPP::Integer *v10; // [sp+48h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  LODWORD(v5) = CryptoPP::DL_KeyImpl<CryptoPP::PKCS8PrivateKey,CryptoPP::DL_GroupParameters_DSA,CryptoPP::OID>::AccessGroupParameters(a1 + 8);
  CryptoPP::DL_GroupParameters_IntegerBased::Initialize(v5, v8, v9, v10);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v7 + 24i64))(v7, a5);
}
