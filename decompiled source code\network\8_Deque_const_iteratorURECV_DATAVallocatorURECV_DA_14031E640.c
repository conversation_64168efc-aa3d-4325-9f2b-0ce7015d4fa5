/*
 * Function: ??8?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBA_NAEBV01@@Z
 * Address: 0x14031E640
 */

bool __fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator==(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->_Mycont == _Right->_Mycont && v6->_Myoff == _Right->_Myoff;
}
