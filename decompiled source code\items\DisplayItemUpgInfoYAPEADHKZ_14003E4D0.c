/*
 * Function: ?DisplayItemUpgInfo@@YAPEADHK@Z
 * Address: 0x14003E4D0
 */

char *__fastcall DisplayItemUpgInfo(int nTableCode, unsigned int dwLvBit)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *result; // rax@5
  size_t v5; // rax@9
  __int64 v6; // [sp+0h] [bp-38h]@1
  size_t Size; // [sp+20h] [bp-18h]@9
  int nTableCodea; // [sp+40h] [bp+8h]@1
  unsigned int Value; // [sp+48h] [bp+10h]@1

  Value = dwLvBit;
  nTableCodea = nTableCode;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwLvBit )
  {
    if ( GetItemKindCode(nTableCodea) || Value == 0xFFFFFFF )
    {
      result = g_szUPT;
    }
    else
    {
      strcpy_0(g_szLv, "00000000");
      _itoa(Value, szBufUpt, 16);
      Size = strlen_0(szBufUpt);
      v5 = strlen_0(szBufUpt);
      memcpy_0(&g_szLv[8 - v5], szBufUpt, Size);
      g_szLv[8] = 0;
      if ( !strcmp_0(&g_szLv[1], "fffffff") )
        g_szLv[2] = 0;
      result = g_szLv;
    }
  }
  else
  {
    result = g_szUPT;
  }
  return result;
}
