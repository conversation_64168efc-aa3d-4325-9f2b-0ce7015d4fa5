/*
 * Function: ?Init@CItemBox@@QEAAXPEAU_object_id@@@Z
 * Address: 0x140165820
 */

void __fastcall CItemBox::Init(CItemBox *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CItemBox *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CGameObject::Init((CGameObject *)&v5->vfptr, pID);
  v5->m_dwLastDestroyTime = 0;
  v5->m_wMonRecIndex = -1;
  v5->m_bBossMob = 0;
  memset_0(v5->m_wszThrowerName, 0, 0x11ui64);
  memset_0(v5->m_aszThrowerName, 0, 0x11ui64);
  memset_0(v5->m_szThrowerID, 0, 0xDui64);
  if ( v5->m_szThrowerItemHistoryFileName )
    memset_0(v5->m_szThrowerItemHistoryFileName, 0, 0x40ui64);
  v5->m_byCreateCode = -1;
  v5->m_bCompDgr = 0;
  v5->m_bHide = 0;
}
