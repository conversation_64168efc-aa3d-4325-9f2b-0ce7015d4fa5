/*
 * Function: ?RequestElanMapUserForceMoveHQ@CMoveMapLimitManager@@QEAAEXZ
 * Address: 0x140284700
 */

char __fastcall CMoveMapLimitManager::RequestElanMapUserForceMoveHQ(CMoveMapLimitManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  CMoveMapLimitManager *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CMoveMapLimitManager::Request(
           v5,
           0,
           0,
           CMoveMapLimitEnviromentValues::ELAN_MAP_CODE,
           CMoveMapLimitEnviromentValues::ELAN_1TH_LIMIT_NPC_RECORD_INDEX,
           -1,
           0i64);
}
