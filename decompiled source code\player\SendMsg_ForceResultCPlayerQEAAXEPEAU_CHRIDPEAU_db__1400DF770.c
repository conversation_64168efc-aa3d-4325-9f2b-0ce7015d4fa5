/*
 * Function: ?SendMsg_ForceResult@CPlayer@@QEAAXEPEAU_CHRID@@PEAU_db_con@_STORAGE_LIST@@H@Z
 * Address: 0x1400DF770
 */

void __fastcall CPlayer::SendMsg_ForceResult(CPlayer *this, char byErrCode, _CHRID *pidDst, _STORAGE_LIST::_db_con *pForceItem, int nSFLv)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-C8h]@1
  char szMsg; // [sp+34h] [bp-94h]@4
  int v9; // [sp+35h] [bp-93h]@5
  char pbyType; // [sp+54h] [bp-74h]@6
  char v11; // [sp+55h] [bp-73h]@6
  char v12; // [sp+78h] [bp-50h]@11
  char v13; // [sp+79h] [bp-4Fh]@12
  char v14; // [sp+7Ah] [bp-4Eh]@14
  char v15; // [sp+7Bh] [bp-4Dh]@11
  unsigned __int16 v16; // [sp+7Ch] [bp-4Ch]@11
  unsigned int v17; // [sp+7Eh] [bp-4Ah]@11
  char Dst; // [sp+82h] [bp-46h]@11
  char v19; // [sp+A4h] [bp-24h]@14
  char v20; // [sp+A5h] [bp-23h]@14
  CPlayer *v21; // [sp+D0h] [bp+8h]@1
  char v22; // [sp+D8h] [bp+10h]@1
  _CHRID *Src; // [sp+E0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v24; // [sp+E8h] [bp+20h]@1

  v24 = pForceItem;
  Src = pidDst;
  v22 = byErrCode;
  v21 = this;
  v5 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byErrCode;
  if ( pForceItem )
    v9 = pForceItem->m_dwDur;
  pbyType = 17;
  v11 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &pbyType, &szMsg, 6u);
  if ( (!v22 || v22 == 100)
    && (v21->m_nCirclePlayerNum <= 500 || v21->m_ObjID.m_byID != Src->byID || v21->m_dwObjSerial != Src->dwSerial) )
  {
    v12 = v22;
    memcpy_0(&Dst, Src, 7ui64);
    v15 = v21->m_ObjID.m_byID;
    v16 = v21->m_ObjID.m_wIndex;
    v17 = v21->m_dwObjSerial;
    if ( v24 )
      v13 = *((_BYTE *)CPlayer::s_pnLinkForceItemToEffect + 4 * v24->m_wItemIndex);
    else
      v13 = -1;
    v14 = nSFLv;
    v19 = 17;
    v20 = 3;
    CGameObject::CircleReport((CGameObject *)&v21->vfptr, &v19, &v12, 18, 0);
  }
}
