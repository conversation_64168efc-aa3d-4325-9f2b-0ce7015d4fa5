/*
 * Function: ??0_worlddb_potion_delay_info@@QEAA@XZ
 * Address: 0x1401BF480
 */

void __fastcall _worlddb_potion_delay_info::_worlddb_potion_delay_info(_worlddb_potion_delay_info *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _worlddb_potion_delay_info *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x98ui64);
}
