/*
 * Function: ?CompleteClear@CUnmannedTraderScheduler@@QEAAXEEEK@Z
 * Address: 0x140393B60
 */

void __fastcall CUnmannedTraderScheduler::CompleteClear(CUnmannedTraderScheduler *this, char byDBQueryRet, char byProcRet, char byType, unsigned int dwRegistSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSchedule *v7; // rax@9
  __int64 v8; // [sp+0h] [bp-88h]@1
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > result; // [sp+28h] [bp-60h]@4
  bool v10; // [sp+44h] [bp-44h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > v11; // [sp+48h] [bp-40h]@4
  __int64 v12; // [sp+60h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v13; // [sp+68h] [bp-20h]@4
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Right; // [sp+70h] [bp-18h]@4
  CUnmannedTraderScheduler *v15; // [sp+90h] [bp+8h]@1
  char v16; // [sp+98h] [bp+10h]@1
  char v17; // [sp+A0h] [bp+18h]@1
  char v18; // [sp+A8h] [bp+20h]@1

  v18 = byType;
  v17 = byProcRet;
  v16 = byDBQueryRet;
  v15 = this;
  v5 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = -2i64;
  CUnmannedTraderScheduler::FindItem(v15, &result, byType, dwRegistSerial);
  v13 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::end(&v15->m_veckSchdule, &v11);
  _Right = (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)v13;
  v10 = std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&result._Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v13->_Mycont);
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v11);
  if ( v10 )
  {
    if ( !v16 && !v17 )
      CUnmannedTraderScheduler::Log(
        v15,
        "CUnmannedTraderScheduler::CompleteClear( byType(%u), dwRegistSerial(%u) )\r\n"
        "\t\tFindItem( byType, dwRegistSerial ) Fail!\r\n",
        (unsigned __int8)v18,
        dwRegistSerial);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
  }
  else
  {
    v7 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&result);
    CUnmannedTraderSchedule::CompleteClear(v7, v16, v17);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
  }
}
