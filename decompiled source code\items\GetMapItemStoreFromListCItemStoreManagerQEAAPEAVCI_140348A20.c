/*
 * Function: ?GetMapItemStoreFromList@CItemStoreManager@@QEAAPEAVCItemStore@@HH@Z
 * Address: 0x140348A20
 */

CItemStore *__fastcall CItemStoreManager::GetMapItemStoreFromList(CItemStoreManager *this, int nMapNum, int nStoreNum)
{
  CItemStore *result; // rax@3

  if ( nMapNum >= 0 && nMapNum < this->m_nMapItemStoreListNum )
  {
    if ( nStoreNum >= 0 && nStoreNum < this->m_MapItemStoreList[nMapNum].m_nItemStoreNum )
      result = &this->m_MapItemStoreList[nMapNum].m_ItemStore[nStoreNum];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
