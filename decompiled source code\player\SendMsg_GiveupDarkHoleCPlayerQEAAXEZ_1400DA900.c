/*
 * Function: ?SendMsg_GiveupDarkHole@CPlayer@@QEAAXE@Z
 * Address: 0x1400DA900
 */

void __fastcall CPlayer::SendMsg_GiveupDarkHole(CPlayer *this, char byErrCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  _darkhole_giveup_out_result_zocl v6; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v8; // [sp+65h] [bp-33h]@4
  unsigned __int64 v9; // [sp+80h] [bp-18h]@4
  CPlayer *v10; // [sp+A0h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  v6.byRetCode = byErrCode;
  v6.wTarMapIndex = v10->m_pCurMap->m_pMapSet->m_dwIndex;
  FloatToShort(v10->m_fCurPos, v6.zTarPos, 3);
  pbyType = 35;
  v8 = -50;
  v4 = _darkhole_giveup_out_result_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v6.byRetCode, v4);
}
