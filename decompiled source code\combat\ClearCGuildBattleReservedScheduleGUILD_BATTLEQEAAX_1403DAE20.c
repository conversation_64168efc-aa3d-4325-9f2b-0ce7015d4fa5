/*
 * Function: ?Clear@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403DAE20
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Clear(GUILD_BATTLE::CGuildBattleReservedSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bDone = 0;
  v4->m_uiCurScheduleInx = 0;
  memset_0(v4->m_bU<PERSON><PERSON><PERSON>, 0, 0x17ui64);
  memset_0(v4->m_pkSchedule, 0, 0xB8ui64);
}
