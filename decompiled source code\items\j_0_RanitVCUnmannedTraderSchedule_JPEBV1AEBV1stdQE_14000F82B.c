/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderSchedule@@_JPEBV1@AEBV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x14000F82B
 */

void __fastcall std::_<PERSON>t<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>::_<PERSON>t<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>(std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *this, std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *__that)
{
  std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>(
    this,
    __that);
}
