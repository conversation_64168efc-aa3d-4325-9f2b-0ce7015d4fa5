/*
 * Function: ?IsExistItem@@YAHHH@Z
 * Address: 0x14003C920
 */

signed __int64 __fastcall IsExistItem(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-108h]@1
  CRecordData *v6; // [sp+20h] [bp-E8h]@4
  _base_fld *v7; // [sp+28h] [bp-E0h]@5
  _base_fld *v8; // [sp+30h] [bp-D8h]@8
  _base_fld *v9; // [sp+38h] [bp-D0h]@11
  _base_fld *v10; // [sp+40h] [bp-C8h]@14
  _base_fld *v11; // [sp+48h] [bp-C0h]@17
  _base_fld *v12; // [sp+50h] [bp-B8h]@20
  _base_fld *v13; // [sp+58h] [bp-B0h]@23
  _base_fld *v14; // [sp+60h] [bp-A8h]@26
  _base_fld *v15; // [sp+68h] [bp-A0h]@29
  _base_fld *v16; // [sp+70h] [bp-98h]@32
  _base_fld *v17; // [sp+78h] [bp-90h]@35
  _base_fld *v18; // [sp+80h] [bp-88h]@38
  _base_fld *v19; // [sp+88h] [bp-80h]@41
  _base_fld *v20; // [sp+90h] [bp-78h]@44
  _base_fld *v21; // [sp+98h] [bp-70h]@47
  _base_fld *v22; // [sp+A0h] [bp-68h]@50
  _base_fld *v23; // [sp+A8h] [bp-60h]@55
  _base_fld *v24; // [sp+B0h] [bp-58h]@58
  _base_fld *v25; // [sp+B8h] [bp-50h]@61
  _base_fld *v26; // [sp+C0h] [bp-48h]@65
  _base_fld *v27; // [sp+C8h] [bp-40h]@68
  _base_fld *v28; // [sp+D0h] [bp-38h]@71
  _base_fld *v29; // [sp+D8h] [bp-30h]@74
  _base_fld *v30; // [sp+E0h] [bp-28h]@77
  _base_fld *v31; // [sp+E8h] [bp-20h]@80
  _base_fld *v32; // [sp+F0h] [bp-18h]@83
  int v33; // [sp+F8h] [bp-10h]@4
  int v34; // [sp+110h] [bp+8h]@1

  v34 = nTableCode;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v34];
  v33 = v34;
  switch ( v34 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v7 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v7 )
        goto LABEL_85;
      result = v7[1].m_dwIndex;
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v8 )
        goto LABEL_85;
      result = v8[1].m_dwIndex;
      break;
    case 11:
      v9 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v9 )
        goto LABEL_85;
      result = v9[1].m_dwIndex;
      break;
    case 12:
      v10 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v10 )
        goto LABEL_85;
      result = v10[1].m_dwIndex;
      break;
    case 13:
      v11 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v11 )
        goto LABEL_85;
      result = v11[1].m_dwIndex;
      break;
    case 10:
      v12 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v12 )
        goto LABEL_85;
      result = v12[1].m_dwIndex;
      break;
    case 18:
      v13 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v13 )
        goto LABEL_85;
      result = v13[1].m_dwIndex;
      break;
    case 19:
      v14 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v14 )
        goto LABEL_85;
      result = v14[1].m_dwIndex;
      break;
    case 20:
      v15 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v15 )
        goto LABEL_85;
      result = v15[1].m_dwIndex;
      break;
    case 16:
      v16 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v16 )
        goto LABEL_85;
      result = v16[1].m_dwIndex;
      break;
    case 15:
      v17 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v17 )
        goto LABEL_85;
      result = v17[1].m_dwIndex;
      break;
    case 8:
      v18 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v18 )
        goto LABEL_85;
      result = v18[1].m_dwIndex;
      break;
    case 9:
      v19 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v19 )
        goto LABEL_85;
      result = v19[1].m_dwIndex;
      break;
    case 21:
      v20 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v20 )
        goto LABEL_85;
      result = v20[1].m_dwIndex;
      break;
    case 22:
      v21 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v21 )
        goto LABEL_85;
      result = v21[1].m_dwIndex;
      break;
    case 23:
      v22 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v22 )
        goto LABEL_85;
      result = v22[1].m_dwIndex;
      break;
    case 24:
      result = 1i64;
      break;
    case 25:
      result = 1i64;
      break;
    case 26:
      v23 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v23 )
        goto LABEL_85;
      result = v23[1].m_dwIndex;
      break;
    case 27:
      v24 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v24 )
        goto LABEL_85;
      result = v24[1].m_dwIndex;
      break;
    case 28:
      v25 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v25 )
        goto LABEL_85;
      result = v25[1].m_dwIndex;
      break;
    case 29:
      result = 1i64;
      break;
    case 30:
      v26 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v26 )
        goto LABEL_85;
      result = v26[1].m_dwIndex;
      break;
    case 31:
      v27 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v27 )
        goto LABEL_85;
      result = v27[1].m_dwIndex;
      break;
    case 32:
      v28 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v28 )
        goto LABEL_85;
      result = v28[1].m_dwIndex;
      break;
    case 33:
      v29 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v29 )
        goto LABEL_85;
      result = v29[1].m_dwIndex;
      break;
    case 34:
      v30 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v30 )
        goto LABEL_85;
      result = v30[1].m_dwIndex;
      break;
    case 35:
      v31 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v31 )
        goto LABEL_85;
      result = v31[1].m_dwIndex;
      break;
    case 36:
      v32 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v32 )
        goto LABEL_85;
      result = v32[1].m_dwIndex;
      break;
    default:
LABEL_85:
      result = 0i64;
      break;
  }
  return result;
}
