/*
 * Function: ?Ref@?$Singleton@VDL_SignatureMessageEncodingMethod_NR@CryptoPP@@U?$NewObject@VDL_SignatureMessageEncodingMethod_NR@CryptoPP@@@2@$0A@@CryptoPP@@QEBAAEBVDL_SignatureMessageEncodingMethod_NR@2@XZ
 * Address: 0x14063AB10
 */

__int64 __fastcall CryptoPP::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>,0>::Ref(__int64 a1)
{
  __int64 v1; // rax@6
  __int64 v3; // [sp+50h] [bp+8h]@1

  v3 = a1;
  if ( !(dword_184A8A608 & 1) )
  {
    dword_184A8A608 |= 1u;
    CryptoPP::simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_NR>::simple_ptr<CryptoPP::DL_SignatureMessageEncodingMethod_NR>(&qword_184A8A600);
    atexit(sub_1406E9BD0);
  }
  while ( byte_184A8A5FC )
  {
    if ( byte_184A8A5FC != 1 )
      return qword_184A8A600;
  }
  byte_184A8A5FC = 1;
  LODWORD(v1) = CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>::operator()(v3);
  qword_184A8A600 = v1;
  byte_184A8A5FC = 2;
  return qword_184A8A600;
}
