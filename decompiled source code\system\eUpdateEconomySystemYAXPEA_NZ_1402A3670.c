/*
 * Function: ?eUpdateEconomySystem@@YAXPEA_N@Z
 * Address: 0x1402A3670
 */

void __fastcall eUpdateEconomySystem(bool *pbChangeDay)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@38
  __int64 v4; // [sp+0h] [bp-398h]@1
  int j; // [sp+30h] [bp-368h]@8
  unsigned int v6; // [sp+34h] [bp-364h]@7
  unsigned int v7; // [sp+38h] [bp-360h]@7
  char v8; // [sp+3Ch] [bp-35Ch]@11
  unsigned int v9; // [sp+48h] [bp-350h]@12
  unsigned int v10; // [sp+4Ch] [bp-34Ch]@12
  unsigned int v11; // [sp+50h] [bp-348h]@12
  int k; // [sp+64h] [bp-334h]@21
  int l; // [sp+68h] [bp-330h]@24
  _log_sheet_economy v14; // [sp+80h] [bp-318h]@32
  int m; // [sp+154h] [bp-244h]@32
  int n; // [sp+158h] [bp-240h]@34
  unsigned int v17; // [sp+15Ch] [bp-23Ch]@39
  double v18; // [sp+160h] [bp-238h]@42
  _economy_calc_data pData; // [sp+180h] [bp-218h]@44
  int ii; // [sp+284h] [bp-114h]@44
  int jj; // [sp+288h] [bp-110h]@51
  _economy_history_data Dst; // [sp+2A0h] [bp-F8h]@55
  int kk; // [sp+374h] [bp-24h]@57
  unsigned __int64 v24; // [sp+380h] [bp-18h]@4
  bool *v25; // [sp+3A0h] [bp+8h]@1

  v25 = pbChangeDay;
  v1 = &v4;
  for ( i = 228i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v24 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v25 )
    *v25 = 0;
  if ( e_EconomySystem.m_bLoad )
  {
    v6 = GetLoopTime();
    v7 = v6 - e_EconomySystem.m_dwLastUpdateTime;
    if ( v6 - e_EconomySystem.m_dwLastUpdateTime >= 0xEA60 )
    {
      e_EconomySystem.m_dwLastUpdateTime = v6;
      ++e_dwMinCount;
      for ( j = 0; j < 3; ++j )
        e_dwUserCumCount[j] += *(&CPlayer::s_nRaceNum + j);
      v8 = GetCurrentHour();
      if ( (unsigned __int8)e_EconomySystem.m_byCurHour != (unsigned __int8)v8 )
      {
        e_EconomySystem.m_byCurHour = v8;
        v9 = e_dwUserCumCount[0] / e_dwMinCount;
        v10 = e_dwUserCumCount[1] / e_dwMinCount;
        v11 = e_dwUserCumCount[2] / e_dwMinCount;
        e_dwMinCount = 0;
        memset_0(e_dwUserCumCount, 0, 0xCui64);
        for ( j = 0; j < 3; ++j )
        {
          if ( (signed int)v9 > 10 && (signed int)v10 > 10 && (signed int)v11 > 10 )
          {
            e_EconomySystem.m_dCurTradeDalant[j] = e_EconomySystem.m_dCurTradeDalant[j]
                                                 + e_EconomySystem.m_dBufTradeDalant[j];
            if ( e_EconomySystem.m_dCurTradeDalant[j] < 1.0 )
              e_EconomySystem.m_dCurTradeDalant[j] = DOUBLE_1_0;
            e_EconomySystem.m_dCurTradeGold[j] = e_EconomySystem.m_dCurTradeGold[j] + e_EconomySystem.m_dBufTradeGold[j];
            if ( e_EconomySystem.m_dCurTradeGold[j] < 1.0 )
              e_EconomySystem.m_dCurTradeGold[j] = DOUBLE_1_0;
            for ( k = 0; k < 3; ++k )
            {
              e_EconomySystem.m_dCurOreMineCount[j][k] = e_EconomySystem.m_dCurOreMineCount[j][k]
                                                       + e_EconomySystem.m_dBufOreMineCount[j][k];
              e_EconomySystem.m_dCurOreCutCount[j][k] = e_EconomySystem.m_dCurOreCutCount[j][k]
                                                      + e_EconomySystem.m_dBufOreCutCount[j][k];
            }
          }
          *(_QWORD *)&e_EconomySystem.m_dBufTradeDalant[j] = 0i64;
          *(_QWORD *)&e_EconomySystem.m_dBufTradeGold[j] = 0i64;
          for ( l = 0; l < 3; ++l )
          {
            *(_QWORD *)&e_EconomySystem.m_dBufOreMineCount[j][l] = 0i64;
            *(_QWORD *)&e_EconomySystem.m_dBufOreCutCount[j][l] = 0i64;
          }
        }
        if ( CMainThread::IsReleaseServiceMode(&g_Main)
          && (signed int)v9 > 10
          && (signed int)v10 > 10
          && (signed int)v11 > 10 )
        {
          v14.dwDate = e_EconomySystem.m_dwLastDate;
          memcpy_0(v14.dTradeDalant, e_EconomySystem.m_dCurTradeDalant, 0x18ui64);
          memcpy_0(v14.dTradeGold, e_EconomySystem.m_dCurTradeGold, 0x18ui64);
          v14.nMgrValue = 1000;
          for ( m = 0; m < 3; ++m )
          {
            for ( n = 0; n < 3; ++n )
            {
              v14.dMineOre[m][n] = e_EconomySystem.m_dCurOreMineCount[m][n];
              v14.dCutOre[m][n] = e_EconomySystem.m_dCurOreCutCount[m][n];
            }
          }
          v3 = _log_sheet_economy::size(&v14);
          CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 9, (char *)&v14, v3);
        }
        SendMsg_EconomyDataToWeb();
        v17 = eGetLocalDate();
        if ( v17 != e_EconomySystem.m_dwLastDate )
        {
          if ( v25 )
            *v25 = 1;
          e_EconomySystem.m_dwLastDate = v17;
          _ReadEconomyIniFile();
          v18 = 0.0;
          for ( j = 0; j < 3; ++j )
          {
            pData.dTradeDalant[j] = e_EconomySystem.m_dCurTradeDalant[j];
            pData.dTradeGold[j] = DOUBLE_1_0;
            for ( ii = 0; ii < 3; ++ii )
            {
              pData.dOreMineCount[j][ii] = e_EconomySystem.m_dCurOreMineCount[j][ii];
              pData.dOreCutCount[j][ii] = e_EconomySystem.m_dCurOreCutCount[j][ii];
            }
            v18 = v18 + e_EconomySystem.m_dCurTradeDalant[j];
          }
          if ( v18 > 3.0 )
          {
            _UpdateNewEconomy(&pData);
            for ( j = 0; j < 3; ++j )
            {
              e_EconomySystem.m_CurRate[j].dOldTradeDalant = pData.dTradeDalant[j];
              e_EconomySystem.m_CurRate[j].dOldTradeGold = pData.dTradeGold[j];
              e_EconomySystem.m_CurRate[j].fPayExgRate = pData.out_fPayExgRate[j];
              e_EconomySystem.m_CurRate[j].fTexRate = pData.out_fTexRate[j];
              e_EconomySystem.m_CurRate[j].fOreRate = pData.out_fOreRate[j];
              e_EconomySystem.m_CurRate[j].wEconomyGuide = pData.out_wEconomyGuide[j];
              e_EconomySystem.m_CurRate[j].dwTexRate = pData.out_dwTexRate[j];
              for ( jj = 0; jj < 3; ++jj )
              {
                e_EconomySystem.m_CurRate[j].dOldOreMineCount[jj] = pData.dOreMineCount[j][jj];
                e_EconomySystem.m_CurRate[j].dOldOreCutCount[jj] = pData.dOreCutCount[j][jj];
              }
            }
            _economy_history_data::_economy_history_data(&Dst);
            memcpy_0(&Dst, e_EconomySystem.m_dCurTradeGold, 0x18ui64);
            memcpy_0(Dst.dTradeDalant, e_EconomySystem.m_dCurTradeDalant, 0x18ui64);
            for ( j = 0; j < 3; ++j )
            {
              Dst.wEconomyGuide[j] = e_EconomySystem.m_CurRate[j].wEconomyGuide;
              for ( kk = 0; kk < 3; ++kk )
              {
                Dst.dOreMineCount[j][kk] = e_EconomySystem.m_dCurOreMineCount[j][kk];
                Dst.dOreCutCount[j][kk] = e_EconomySystem.m_dCurOreCutCount[j][kk];
              }
            }
            memcpy_0(e_EconomyHistory, &e_EconomyHistory[1], 0x898ui64);
            memcpy_0(&e_EconomyHistory[11], &Dst, 0xC8ui64);
            _UpdateRateSendToAllPlayer();
          }
          _ECONOMY_SYSTEM::CurTradeMoneyInit(&e_EconomySystem);
          SendMsg_EconomyDataToWeb();
        }
      }
    }
  }
}
