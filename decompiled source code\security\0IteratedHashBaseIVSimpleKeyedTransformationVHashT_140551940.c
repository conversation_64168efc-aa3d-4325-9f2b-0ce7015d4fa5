/*
 * Function: ??0?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140551940
 */

CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *__fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>(CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *a1)
{
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>::SimpleKeyedTransformation<CryptoPP::HashTransformation>(a1);
  LODWORD(v2[1].vfptr) = 0;
  HIDWORD(v2[1].vfptr) = 0;
  return v2;
}
