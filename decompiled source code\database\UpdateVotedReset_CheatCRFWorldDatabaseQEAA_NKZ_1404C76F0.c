/*
 * Function: ?UpdateVotedReset_Cheat@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404C76F0
 */

bool __fastcall CRFWorldDatabase::UpdateVotedReset_Cheat(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v6; // [sp+30h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+38h] [bp+10h]@1

  dwSeriala = dwSerial;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::UpdateVotedReset_Supplement(v6, dwSerial) )
    result = CRFWorldDatabase::UpdateVotedReset_General(v6, dwSeriala) != 0;
  else
    result = 0;
  return result;
}
