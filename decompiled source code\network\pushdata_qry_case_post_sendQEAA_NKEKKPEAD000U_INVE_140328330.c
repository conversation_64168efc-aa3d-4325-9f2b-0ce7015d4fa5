/*
 * Function: ?pushdata@_qry_case_post_send@@QEAA_NKEKKPEAD000U_INVENKEY@@_KKK2@Z
 * Address: 0x140328330
 */

char __fastcall _qry_case_post_send::pushdata(_qry_case_post_send *this, unsigned int dwIndex, char byErr, unsigned int dwReceiverSerial, unsigned int dwSenderSerial, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, _INVENKEY key, unsigned __int64 dwDur, unsigned int dwUpt, unsigned int dwGold, unsigned __int64 lnUID)
{
  __int64 *v14; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v17; // [sp+0h] [bp-28h]@1
  _qry_case_post_send *v18; // [sp+30h] [bp+8h]@1

  v18 = this;
  v14 = &v17;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v14 = -858993460;
    v14 = (__int64 *)((char *)v14 + 4);
  }
  if ( v18->dwCount < 0xF )
  {
    v18->List[v18->dwCount].dwIndex = dwIndex;
    v18->List[v18->dwCount].byErr = byErr;
    v18->List[v18->dwCount].dwReceiverSerial = dwReceiverSerial;
    v18->List[v18->dwCount].dwSenderSerial = dwSenderSerial;
    _INVENKEY::operator=(&v18->List[v18->dwCount].key, &key);
    v18->List[v18->dwCount].dwDur = dwDur;
    v18->List[v18->dwCount].dwUpt = dwUpt;
    v18->List[v18->dwCount].dwGold = dwGold;
    v18->List[v18->dwCount].lnUID = lnUID;
    strcpy_s(v18->List[v18->dwCount].wszSendName, 0x11ui64, wszSendName);
    strcpy_s(v18->List[v18->dwCount].wszRecvName, 0x11ui64, wszRecvName);
    strcpy_s(v18->List[v18->dwCount].wszTitle, 0x15ui64, wszTitle);
    strcpy_s(v18->List[v18->dwCount].wszContent, 0xC9ui64, wszContent);
    ++v18->dwCount;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
