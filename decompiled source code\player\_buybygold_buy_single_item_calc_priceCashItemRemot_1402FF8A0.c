/*
 * Function: ?_buybygold_buy_single_item_calc_price@CashItemRemoteStore@@AEAAKPEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU__item@3@PEAU_param_cashitem_dblog@@PEAU_CashShop_fld@@PEA_NAEAU_result_csi_buy_zocl@@AEAK@Z
 * Address: 0x1402FF8A0
 */

__int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, _CashShop_fld *pCsFld, bool *bCouponUseCheck, _result_csi_buy_zocl *Send, unsigned int *dwDiscount)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v12; // [sp+0h] [bp-58h]@1
  int nCsPrice; // [sp+20h] [bp-38h]@5
  bool *v14; // [sp+28h] [bp-30h]@5
  unsigned int *v15; // [sp+30h] [bp-28h]@5
  unsigned int v16; // [sp+40h] [bp-18h]@4
  CashItemRemoteStore *v17; // [sp+60h] [bp+8h]@1

  v17 = this;
  v9 = &v12;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v16 = pSrc->byOverlapNum * pCsFld->m_nCsPrice;
  if ( pRecv->byCouponNum <= 0 )
  {
    if ( pSheet->in_bAdjustDiscount && pSrc->byEventType == 1 )
    {
      v16 = CashItemRemoteStore::_buybygold_buy_single_item_calc_price_discount(v17, pCsFld, pSrc->byOverlapNum);
    }
    else if ( pSheet->in_bSetDiscount && pRecv->bySetKind > 0 && pRecv->bySetKind <= 4 )
    {
      v16 = CashItemRemoteStore::_buybygold_buy_single_item_calc_price_one_n_one(
              v17,
              pRecv->bySetKind,
              pCsFld->m_nCsPrice,
              pSrc->byOverlapNum);
    }
    else if ( (!pSheet->in_bOneN_One || pSrc->byEventType != 3) && pSheet->in_bLimited_Sale && pSrc->byEventType == 5 )
    {
      v16 = CashItemRemoteStore::_buybygold_buy_single_item_calc_price_limitsale(
              v17,
              pCsFld->m_nCsPrice,
              pSrc->byOverlapNum);
    }
  }
  else
  {
    v15 = dwDiscount;
    v14 = bCouponUseCheck;
    nCsPrice = pCsFld->m_nCsPrice;
    v16 = CashItemRemoteStore::_buybygold_buy_single_item_calc_price_coupon(
            v17,
            pOne,
            pRecv,
            pSrc->byOverlapNum,
            nCsPrice,
            bCouponUseCheck,
            dwDiscount);
  }
  return v16;
}
