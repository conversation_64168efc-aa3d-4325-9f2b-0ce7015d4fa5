/*
 * Function: ?used_cash@CMgrAvatorItemHistory@@QEAAXHHPEAD@Z
 * Address: 0x14023E0D0
 */

void __fastcall CMgrAvatorItemHistory::used_cash(CMgrAvatorItemHistory *this, int nCurCash, int nUseCash, char *pFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v8; // [sp+40h] [bp+8h]@1
  char *pszFileName; // [sp+58h] [bp+20h]@1

  pszFileName = pFileName;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  sData[0] = 0;
  v7 = nCurCash - nUseCash;
  sprintf(
    sData,
    "[CASH_AMOUNT] : [cash:%d] - [used:%d] = [remain:%d]\r\n",
    (unsigned int)nCurCash,
    (unsigned int)nUseCash);
  CMgrAvatorItemHistory::WriteFile(v8, pszFileName, sData);
}
