/*
 * Function: sub_14055BDB0
 * Address: 0x14055BDB0
 */

int __fastcall sub_14055BDB0(__int64 a1, __int64 a2)
{
  int result; // eax@1

  result = *(_DWORD *)(a2 + 232) & 1;
  if ( result )
  {
    *(_DWORD *)(a2 + 232) &= 0xFFFFFFFE;
    result = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(*(_QWORD *)(a2 + 352));
  }
  return result;
}
