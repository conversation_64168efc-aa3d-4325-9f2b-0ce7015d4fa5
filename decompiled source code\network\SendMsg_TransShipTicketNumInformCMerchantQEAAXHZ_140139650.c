/*
 * Function: ?SendMsg_TransShipTicketNumInform@CMerchant@@QEAAXH@Z
 * Address: 0x140139650
 */

void __fastcall CMerchant::SendMsg_TransShipTicketNumInform(CMerchant *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@18
  int v5; // eax@26
  __int64 v6; // [sp+0h] [bp-108h]@1
  CMapData **v7; // [sp+30h] [bp-D8h]@7
  char szMsg[4]; // [sp+48h] [bp-C0h]@7
  char v9; // [sp+4Ch] [bp-BCh]@9
  __int16 v10; // [sp+4Dh] [bp-BBh]@9
  char v11; // [sp+64h] [bp-A4h]@7
  int j; // [sp+68h] [bp-A0h]@7
  char pbyType; // [sp+74h] [bp-94h]@16
  char v14; // [sp+75h] [bp-93h]@16
  _pnt_rect pRect; // [sp+98h] [bp-70h]@18
  _sec_info *v16; // [sp+B8h] [bp-50h]@18
  int nRadius; // [sp+C0h] [bp-48h]@18
  int k; // [sp+C4h] [bp-44h]@18
  int l; // [sp+C8h] [bp-40h]@20
  unsigned int dwSecIndex; // [sp+CCh] [bp-3Ch]@23
  CObjectList *v21; // [sp+D0h] [bp-38h]@23
  CObjectList *v22; // [sp+D8h] [bp-30h]@24
  CObjectListVtbl *v23; // [sp+E0h] [bp-28h]@26
  CObjectListVtbl *v24; // [sp+E8h] [bp-20h]@26
  int v25; // [sp+F0h] [bp-18h]@26
  CGameObjectVtbl *v26; // [sp+F8h] [bp-10h]@26
  CMerchant *v27; // [sp+110h] [bp+8h]@1
  int dwClientIndex; // [sp+118h] [bp+10h]@1

  dwClientIndex = n;
  v27 = this;
  v2 = &v6;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v27->m_pItemStore
    && v27->m_pItemStore->m_pRec->m_nStore_trade == 18
    && (unsigned int)((int (__fastcall *)(CMerchant *))v27->vfptr->GetObjRace)(v27) < 3 )
  {
    v7 = &g_TransportShip[10162 * ((int (__fastcall *)(CMerchant *))v27->vfptr->GetObjRace)(v27)];
    *(_DWORD *)szMsg = v27->m_dwObjSerial;
    v11 = 0;
    for ( j = 0; j < 2; ++j )
    {
      *(&v9 + 3 * j) = j;
      *(__int16 *)((char *)&v10 + 3 * j) = (__int16)v7[3 * j + 10156];
      if ( dwClientIndex == -1 )
      {
        if ( v27->m_nLeftTicketNum[j] != LODWORD(v7[3 * j + 10156]) )
          v11 = 1;
        v27->m_nLeftTicketNum[j] = (int)v7[3 * j + 10156];
      }
    }
    if ( dwClientIndex != -1 || v11 )
    {
      pbyType = 33;
      v14 = 4;
      if ( dwClientIndex == -1 )
      {
        v16 = CMapData::GetSecInfo(v27->m_pCurMap);
        nRadius = CGameObject::GetUseSectorRange((CGameObject *)&v27->vfptr);
        v4 = CGameObject::GetCurSecNum((CGameObject *)&v27->vfptr);
        CMapData::GetRectInRadius(v27->m_pCurMap, &pRect, nRadius, v4);
        for ( k = pRect.nStarty; k <= pRect.nEndy; ++k )
        {
          for ( l = pRect.nStartx; l <= pRect.nEndx; ++l )
          {
            dwSecIndex = v16->m_nSecNumW * k + l;
            v21 = CMapData::GetSectorListPlayer(v27->m_pCurMap, v27->m_wMapLayerIndex, dwSecIndex);
            if ( v21 )
            {
              v22 = (CObjectList *)v21->m_Head.m_pNext;
              while ( (_object_list_point *)v22 != &v21->m_Tail )
              {
                v23 = v22->vfptr;
                v22 = (CObjectList *)v22->m_Head.m_pItem;
                v24 = v23 + 2;
                v25 = (*((int (__fastcall **)(__int64))v23->__vecDelDtor + 41))((__int64)v23);
                v26 = v27->vfptr;
                v5 = ((int (__fastcall *)(CMerchant *))v26->GetObjRace)(v27);
                if ( v25 == v5 )
                  CNetProcess::LoadSendMsg(unk_1414F2088, WORD1(v24->__vecDelDtor), &pbyType, szMsg, 0xAu);
              }
            }
          }
        }
      }
      else
      {
        CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0xAu);
      }
    }
  }
}
