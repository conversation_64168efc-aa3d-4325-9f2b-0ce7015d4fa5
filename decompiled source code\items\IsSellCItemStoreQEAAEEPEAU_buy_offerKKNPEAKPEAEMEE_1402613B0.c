/*
 * Function: ?Is<PERSON>ell@CItemStore@@QEAAEEPEAU_buy_offer@@KKNPEAKPEAEMEE@Z
 * Address: 0x1402613B0
 */

char __usercall CItemStore::Is<PERSON>ell@<al>(CItemStore *this@<rcx>, char by<PERSON><PERSON><PERSON><PERSON>@<dl>, _buy_offer *pOffer@<r8>, unsigned int dwHasDalant@<r9d>, float a5@<xmm0>, unsigned int dwHasGold, long double dHasPoint, unsigned int *dwHasActPoint, char *pbyActCode, float fDiscountRate, char byRace, char byGrade)
{
  __int64 *v12; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  int v15; // eax@14
  float v16; // xmm0_4@24
  signed __int64 v17; // rax@24
  bool v18; // zf@47
  __int64 v19; // [sp+0h] [bp-128h]@1
  unsigned int v20; // [sp+20h] [bp-108h]@7
  double v21; // [sp+28h] [bp-100h]@7
  char v22; // [sp+30h] [bp-F8h]@7
  char v23; // [sp+31h] [bp-F7h]@7
  int v24; // [sp+34h] [bp-F4h]@7
  int v25; // [sp+50h] [bp-D8h]@7
  char v26; // [sp+54h] [bp-D4h]@7
  int j; // [sp+94h] [bp-94h]@7
  char *v28; // [sp+98h] [bp-90h]@11
  char pbyMoneyUnit; // [sp+A4h] [bp-84h]@14
  __int64 v30; // [sp+B8h] [bp-70h]@14
  __int64 v31; // [sp+C0h] [bp-68h]@27
  __int64 v32; // [sp+C8h] [bp-60h]@27
  unsigned __int64 v33; // [sp+D0h] [bp-58h]@29
  __int64 v34; // [sp+D8h] [bp-50h]@29
  char *v35; // [sp+E0h] [bp-48h]@57
  char v36; // [sp+E8h] [bp-40h]@60
  _TimeItem_fld *v37; // [sp+F0h] [bp-38h]@65
  __time32_t Time; // [sp+104h] [bp-24h]@66
  char v39; // [sp+114h] [bp-14h]@14
  CItemStore *v40; // [sp+130h] [bp+8h]@1
  char v41; // [sp+138h] [bp+10h]@1
  _buy_offer *v42; // [sp+140h] [bp+18h]@1
  unsigned int v43; // [sp+148h] [bp+20h]@1

  v43 = dwHasDalant;
  v42 = pOffer;
  v41 = byOfferNum;
  v40 = this;
  v12 = &v19;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v12 = -858993460;
    v12 = (__int64 *)((char *)v12 + 4);
  }
  if ( v40->m_pStorageItem && v40->m_pLimitStorageItem )
  {
    CItemStore::SetZeroTradeMoney(v40);
    v20 = eGetTexRate((unsigned __int8)byRace) + 10000;
    eGetTex((unsigned __int8)byRace);
    v21 = (float)(a5 + 1.0);
    v22 = 0;
    v23 = 0;
    v24 = 0;
    v25 = 0;
    memset(&v26, 0, 0x3Cui64);
    for ( j = 0; j < (unsigned __int8)v41; ++j )
    {
      if ( v42[j].byGoodIndex >= v40->m_nStorageItemNum )
        return 17;
      v28 = &v40->m_pStorageItem[v42[j].byGoodIndex].byItemTableCode;
      if ( v28[40] == 1 )
      {
        *(&v25 + *((_DWORD *)v28 + 11)) += v42[j].byGoodAmount;
        if ( *(&v25 + *((_DWORD *)v28 + 11)) > v40->m_pLimitStorageItem[*((_DWORD *)v28 + 11)].nLimitNum )
          return 19;
      }
      v15 = CItemStore::CalcSellPrice(v40, v42[j].byGoodIndex, &pbyMoneyUnit);
      v30 = v42[j].byGoodAmount * v15;
      v39 = pbyMoneyUnit;
      if ( pbyMoneyUnit == 4 )
      {
        *pbyActCode = 0;
      }
      else if ( v39 == 5 )
      {
        *pbyActCode = 1;
      }
      else if ( v39 == 6 )
      {
        *pbyActCode = 2;
      }
      if ( fDiscountRate > 0.0 )
      {
        if ( fDiscountRate > 1.0 )
          fDiscountRate = FLOAT_1_0;
        v16 = fDiscountRate * 10000.0;
        v17 = 0i64;
        if ( (float)(fDiscountRate * 10000.0) > 9.223372e18 )
        {
          v16 = v16 - 9.223372e18;
          v17 = 0x7FFFFFFFFFFFFFFFi64;
          if ( v16 > 9.223372e18 )
          {
            v16 = v16 - 9.223372e18;
            v17 = -2i64;
          }
        }
        v31 = v17 + (unsigned int)(signed int)ffloor(v16);
        v32 = v31 * v30;
        v30 -= v31 * v30 / 0x2710ui64;
        if ( !v30 )
          v30 = 1i64;
      }
      v33 = v20 * v30;
      v34 = v20 * v30;
      v30 = v33 / 0x2710;
      if ( pbyMoneyUnit )
      {
        if ( pbyMoneyUnit == 1 )
        {
          v40->m_dwLastTradeGold += v30;
          if ( dwHasGold < v40->m_dwLastTradeGold )
            return 14;
        }
        else if ( pbyMoneyUnit != 2 && pbyMoneyUnit != 3 )
        {
          if ( pbyMoneyUnit == 4 || pbyMoneyUnit == 5 || pbyMoneyUnit == 6 )
          {
            v40->m_dwLastTradeActPoint[(unsigned __int8)*pbyActCode] += v30;
            if ( dwHasActPoint[(unsigned __int8)*pbyActCode] < v40->m_dwLastTradeActPoint[(unsigned __int8)*pbyActCode] )
            {
              if ( pbyMoneyUnit == 4 )
                return 25;
              if ( pbyMoneyUnit == 5 )
                return 26;
              if ( pbyMoneyUnit == 6 )
                return 27;
            }
            else
            {
              v18 = dwHasActPoint[(unsigned __int8)*pbyActCode] == 0;
            }
          }
        }
        else
        {
          v40->m_dwLastTradePoint += v30;
          if ( (double)(signed int)v40->m_dwLastTradePoint > dHasPoint || dHasPoint < 0.0 )
            return 21;
        }
      }
      else
      {
        v40->m_dwLastTradeDalant += v30;
        if ( v43 < v40->m_dwLastTradeDalant )
          return 13;
      }
    }
    for ( j = 0; j < (unsigned __int8)v41; ++j )
    {
      v35 = &v40->m_pStorageItem[v42[j].byGoodIndex].byItemTableCode;
      v42[j].Item.m_byTableCode = *v35;
      v42[j].Item.m_wItemIndex = *((_WORD *)v35 + 1);
      if ( IsOverLapItem((unsigned __int8)*v35) )
        v42[j].Item.m_dwDur = v42[j].byGoodAmount;
      else
        v42[j].Item.m_dwDur = *((_DWORD *)v35 + 8);
      v36 = GetItemKindCode((unsigned __int8)*v35);
      if ( v36 )
      {
        if ( v36 != 1 )
          return 100;
        v42[j].Item.m_dwLv = GetMaxParamFromExp(*((_WORD *)v35 + 1), v42[j].Item.m_dwDur);
      }
      else
      {
        v42[j].Item.m_dwLv = *((_DWORD *)v35 + 9);
      }
      v37 = TimeItem::FindTimeRec((unsigned __int8)*v35, *((_WORD *)v35 + 1));
      if ( v37 )
      {
        _time32(&Time);
        v42[j].Item.m_byCsMethod = v37->m_nCheckType;
        v42[j].Item.m_dwT = v37->m_nUseTime + Time;
        v42[j].Item.m_dwLendRegdTime = Time;
      }
    }
    result = 0;
  }
  else
  {
    result = 100;
  }
  return result;
}
