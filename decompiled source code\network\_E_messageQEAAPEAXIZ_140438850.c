/*
 * Function: ??_E_message@@QEAAPEAXI@Z
 * Address: 0x140438850
 */

void *__fastcall _message::`vector deleting destructor'(_message *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  void *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  _message *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(ptr, 0x20ui64, (int)ptr[-1].pPrev, (void (__cdecl *)(void *))_message::~_message);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].pPrev);
    result = &ptr[-1].pPrev;
  }
  else
  {
    _message::~_message(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
