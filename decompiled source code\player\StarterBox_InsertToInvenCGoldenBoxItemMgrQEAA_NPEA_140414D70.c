/*
 * Function: ?StarterBox_InsertToInven@CGoldenBoxItemMgr@@QEAA_NPEAVCPlayer@@PEAD@Z
 * Address: 0x140414D70
 */

bool __fastcall CGoldenBoxItemMgr::StarterBox_InsertToInven(CGoldenBoxItemMgr *this, CPlayer *pOne, char *szItemCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  unsigned __int16 *v8; // [sp+28h] [bp-10h]@6
  CGoldenBoxItemMgr *v9; // [sp+40h] [bp+8h]@1
  CPlayer *pOnea; // [sp+48h] [bp+10h]@1
  const char *psItemCode; // [sp+50h] [bp+18h]@1

  psItemCode = szItemCode;
  pOnea = pOne;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = GetItemTableCode(szItemCode);
  if ( (unsigned __int8)v7 == 255 )
  {
    result = 0;
  }
  else
  {
    v8 = (unsigned __int16 *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v7, psItemCode);
    if ( v8 )
      result = CGoldenBoxItemMgr::_insert_to_inven(v9, pOnea, v7, *v8) != 0;
    else
      result = 0;
  }
  return result;
}
