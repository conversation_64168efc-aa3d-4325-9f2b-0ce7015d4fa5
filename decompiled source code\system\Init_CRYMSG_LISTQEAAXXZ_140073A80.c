/*
 * Function: ?Init@_CRYMSG_LIST@@QEAAXXZ
 * Address: 0x140073A80
 */

void __fastcall _CRYMSG_LIST::Init(_CRYMSG_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _CRYMSG_LIST *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
    _CRYMSG_LIST::_LIST::Init((_CRYMSG_LIST::_LIST *)v5 + j);
}
