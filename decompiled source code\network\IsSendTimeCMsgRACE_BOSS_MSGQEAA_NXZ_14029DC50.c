/*
 * Function: ?IsSendTime@CMsg@RACE_BOSS_MSG@@QEAA_NXZ
 * Address: 0x14029DC50
 */

bool __fastcall RACE_BOSS_MSG::CMsg::IsSendTime(RACE_BOSS_MSG::CMsg *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@7
  int v6; // [sp+24h] [bp-14h]@7
  RACE_BOSS_MSG::CMsg *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_uiState && v7->m_uiState != 3 )
  {
    v5 = timeGetTime();
    v6 = v7->m_dwSendTime <= v5;
    result = v6;
  }
  else
  {
    result = 0;
  }
  return result;
}
