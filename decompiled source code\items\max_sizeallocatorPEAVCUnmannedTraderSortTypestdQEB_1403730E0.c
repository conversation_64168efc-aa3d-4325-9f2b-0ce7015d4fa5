/*
 * Function: ?max_size@?$allocator@PEAVCUnmannedTraderSortType@@@std@@QEBA_KXZ
 * Address: 0x1403730E0
 */

signed __int64 __fastcall std::allocator<CUnmannedTraderSortType *>::max_size(std::allocator<CUnmannedTraderSortType *> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1

  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return 0x1FFFFFFFFFFFFFFFi64;
}
