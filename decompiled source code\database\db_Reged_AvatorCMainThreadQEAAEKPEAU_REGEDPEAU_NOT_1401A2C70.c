/*
 * Function: ?db_Reged_Avator@CMainThread@@QEAAEKPEAU_REGED@@PEAU_NOT_ARRANGED_AVATOR_DB@@PEAD@Z
 * Address: 0x1401A2C70
 */

char __usercall CMainThread::db_Reged_Avator@<al>(CMainThread *this@<rcx>, unsigned int dwAccountSerial@<edx>, _REGED *pRegedList@<r8>, _NOT_ARRANGED_AVATOR_DB *pArrangedList@<r9>, signed __int64 a5@<rax>, char *pszIP)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v10; // [sp-30h] [bp-BD18h]@1
  unsigned __int16 Dst; // [sp+0h] [bp-BCE8h]@4
  char Source[20]; // [sp+8h] [bp-BCE0h]@19
  int v13; // [sp+1Ch] [bp-BCCCh]@19
  char v14; // [sp+20h] [bp-BCC8h]@19
  char v15[5]; // [sp+21h] [bp-BCC7h]@19
  char v16; // [sp+26h] [bp-BCC2h]@9
  char v17; // [sp+27h] [bp-BCC1h]@19
  int v18; // [sp+28h] [bp-BCC0h]@19
  int v19; // [sp+2Ch] [bp-BCBCh]@19
  int v20; // [sp+30h] [bp-BCB8h]@19
  int v21[11192]; // [sp+34h] [bp-BCB4h]@19
  char v22; // [sp+AF14h] [bp-DD4h]@4
  int j; // [sp+AF18h] [bp-DD0h]@6
  unsigned __int8 v24; // [sp+AF1Ch] [bp-DCCh]@9
  int v25; // [sp+AF20h] [bp-DC8h]@12
  int k; // [sp+AF24h] [bp-DC4h]@12
  int l; // [sp+AF28h] [bp-DC0h]@19
  _worlddb_arrange_char_info pCharData; // [sp+AF40h] [bp-DA8h]@23
  _EQUIPKEY *v29; // [sp+BCD0h] [bp-18h]@21
  unsigned __int64 v30; // [sp+BCD8h] [bp-10h]@4
  CMainThread *v31; // [sp+BCF0h] [bp+8h]@1
  unsigned int dwAccountSeriala; // [sp+BCF8h] [bp+10h]@1
  _REGED *v33; // [sp+BD00h] [bp+18h]@1
  _NOT_ARRANGED_AVATOR_DB *v34; // [sp+BD08h] [bp+20h]@1

  v34 = pArrangedList;
  v33 = pRegedList;
  dwAccountSeriala = dwAccountSerial;
  v31 = this;
  v6 = alloca(a5);
  v7 = &v10;
  for ( i = 12100i64; i; --i )
  {
    *(_DWORD *)v7 = -*********;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v30 = (unsigned __int64)&v10 ^ _security_cookie;
  memset_0(&Dst, 0, 0xAF08ui64);
  v22 = CRFWorldDatabase::Select_CharacterBaseInfoBySerial(
          v31->m_pWorldDB,
          dwAccountSeriala,
          (_worlddb_character_base_info_array *)&Dst);
  if ( v22 == 1 )
    return 24;
  for ( j = 0; j < Dst; ++j )
  {
    v24 = *(&v16 + 224 * j);
    if ( (signed int)v24 > 3 )
      return 24;
    if ( v33[v24].m_bySlotIndex == 255 )
      goto LABEL_19;
    v25 = -1;
    for ( k = 0; k < 3; ++k )
    {
      if ( v33[k].m_bySlotIndex == 255 )
      {
        v25 = k;
        break;
      }
    }
    if ( v25 != -1 )
    {
      v24 = v25;
LABEL_19:
      v33[v24].m_bySlotIndex = v24;
      v33[v24].m_dwRecordNum = *(&v13 + 56 * j);
      strcpy_0(v33[v24].m_wszAvatorName, &Source[224 * j]);
      v33[v24].m_byRaceSexCode = *(&v14 + 224 * j);
      strcpy_0(v33[v24].m_szClassCode, &v15[224 * j]);
      v33[v24].m_byLevel = *(&v17 + 224 * j);
      v33[v24].m_dwDalant = *(&v18 + 56 * j);
      v33[v24].m_dwGold = *(&v19 + 56 * j);
      v33[v24].m_dwBaseShape = *(&v20 + 56 * j);
      v33[v24].m_dwLastConnTime = v21[56 * j];
      for ( l = 0; l < 8; ++l )
      {
        v29 = &v33[v24].m_EquipKey[l];
        _EQUIPKEY::LoadDBKey(v29, *((_WORD *)&Source[224 * j + 66] + l));
        v33[v24].m_dwFixEquipLv[l] = *((_DWORD *)&Source[224 * j + 84] + l);
        v33[v24].m_lnUID[l] = *((_QWORD *)&Source[224 * j + 152] + l);
        v33[v24].m_dwET[l] = *((_DWORD *)&Source[224 * j + 116] + l);
      }
      continue;
    }
  }
  _worlddb_arrange_char_info::_worlddb_arrange_char_info(&pCharData);
  memset_0(&pCharData, 0, 0xD7Bui64);
  v22 = CRFWorldDatabase::Select_NotArrangeCharacter(v31->m_pWorldDB, dwAccountSeriala, &pCharData);
  if ( v22 == 1 )
  {
    result = 0;
  }
  else
  {
    for ( j = 0; j < (unsigned __int8)pCharData.byCount; ++j )
      memcpy_0(&v34[j], &pCharData.ArrangeChar[j], 0x45ui64);
    result = 0;
  }
  return result;
}
