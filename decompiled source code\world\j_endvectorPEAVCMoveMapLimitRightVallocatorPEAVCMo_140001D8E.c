/*
 * Function: j_?end@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@2@XZ
 * Address: 0x140001D8E
 */

std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *__fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *result)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::end(this, result);
}
