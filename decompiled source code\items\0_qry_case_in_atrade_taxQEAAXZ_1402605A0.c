/*
 * Function: ??0_qry_case_in_atrade_tax@@QEAA@XZ
 * Address: 0x1402605A0
 */

void __fastcall _qry_case_in_atrade_tax::_qry_case_in_atrade_tax(_qry_case_in_atrade_tax *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _qry_case_in_atrade_tax *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->byDate[0] = GetCurrentMonth();
  v4->byDate[1] = GetCurrentDay();
  v4->byDate[2] = GetCurrentHour();
  v4->byDate[3] = GetCurrentMin();
}
