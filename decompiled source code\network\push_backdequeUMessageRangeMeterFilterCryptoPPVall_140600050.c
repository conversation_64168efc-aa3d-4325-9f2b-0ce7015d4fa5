/*
 * Function: ?push_back@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@QEAAXAEBUMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x140600050
 */

signed __int64 __fastcall std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::push_back(__int64 a1, __int64 a2)
{
  __int64 v2; // rt1@1
  __int64 v3; // rt1@1
  __int64 v4; // rax@6
  signed __int64 result; // rax@7
  unsigned __int64 v6; // [sp+28h] [bp-10h]@3
  __int64 v7; // [sp+40h] [bp+8h]@1
  __int64 v8; // [sp+48h] [bp+10h]@1

  v8 = a2;
  v7 = a1;
  v2 = *(_QWORD *)(a1 + 40);
  v3 = *(_QWORD *)(a1 + 48);
  if ( *(_QWORD *)(a1 + 32) <= (unsigned __int64)(*(_QWORD *)(a1 + 48) + 1i64) )
    std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::_Growmap(
      a1,
      1i64);
  v6 = *(_QWORD *)(v7 + 48) + *(_QWORD *)(v7 + 40);
  if ( *(_QWORD *)(v7 + 32) <= v6 )
    v6 -= *(_QWORD *)(v7 + 32);
  if ( !*(_QWORD *)(*(_QWORD *)(v7 + 24) + 8 * v6) )
  {
    LODWORD(v4) = std::allocator<CryptoPP::MeterFilter::MessageRange>::allocate(v7 + 16, 1i64);
    *(_QWORD *)(*(_QWORD *)(v7 + 24) + 8 * v6) = v4;
  }
  std::allocator<CryptoPP::MeterFilter::MessageRange>::construct(
    v7 + 16,
    *(_QWORD *)(*(_QWORD *)(v7 + 24) + 8 * v6),
    v8);
  result = *(_QWORD *)(v7 + 48) + 1i64;
  *(_QWORD *)(v7 + 48) = result;
  return result;
}
