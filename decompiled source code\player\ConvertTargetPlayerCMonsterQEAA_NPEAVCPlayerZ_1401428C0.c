/*
 * Function: ?ConvertTargetPlayer@CMonster@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1401428C0
 */

char __fastcall CMonster::ConvertTargetPlayer(CMonster *this, CPlayer *pTar)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  CMonster *v6; // [sp+50h] [bp+8h]@1
  CPlayer *pCharacter; // [sp+58h] [bp+10h]@1

  pCharacter = pTar;
  v6 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_pTargetChar = 0i64;
  if ( pTar )
  {
    CMonsterAggroMgr::SetAggro(&v6->m_AggroMgr, (CCharacter *)&pTar->vfptr, 0, -2, 0, 0, 0);
    CMonsterAggroMgr::SetTopAggroCharacter(&v6->m_AggroMgr, (CCharacter *)&pCharacter->vfptr);
    CMonster::CheckEventEmotionPresentation(v6, 7, (CCharacter *)&pCharacter->vfptr);
    Us_HFSM::SendExternMsg((Us_HFSM *)&v6->m_AI.vfptr, 0, pCharacter, 1);
    CMonster::SetAttackTarget(v6, (CCharacter *)&pCharacter->vfptr);
    CMonsterAggroMgr::ShortRankDelay(&v6->m_AggroMgr, 0xBB8u);
  }
  return 1;
}
