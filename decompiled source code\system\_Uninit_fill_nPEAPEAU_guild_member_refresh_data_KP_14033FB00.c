/*
 * Function: ??$_Uninit_fill_n@PEAPEAU_guild_member_refresh_data@@_KPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@@std@@YAXPEAPEAU_guild_member_refresh_data@@_KAEBQEAU1@AEAV?$allocator@PEAU_guild_member_refresh_data@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14033FB00
 */

void __fastcall std::_Uninit_fill_n<_guild_member_refresh_data * *,unsigned __int64,_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>(_guild_member_refresh_data **_First, unsigned __int64 _Count, _guild_member_refresh_data *const *_Val, std::allocator<_guild_member_refresh_data *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  _guild_member_refresh_data **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  stdext::unchecked_fill_n<_guild_member_refresh_data * *,unsigned __int64,_guild_member_refresh_data *>(
    _Firsta,
    _Count,
    _Val);
}
