/**
 * @file CPlayerDataStructures.cpp
 * @brief Modern C++20 player data structures implementation
 * 
 * This file provides the implementation of modern, type-safe player data structures
 * that replace legacy C structures with modern C++20 equivalents.
 */

#include "../Headers/CPlayerDataStructures.h"
#include <algorithm>
#include <iostream>
#include <ctime>

namespace NexusProtection {
namespace Player {

// PlayerInventory Implementation
bool PlayerInventory::AddItem(const ItemInfo& item, size_t& slot) {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    if (!item.IsValid()) {
        return false;
    }
    
    // Find free slot
    slot = GetFreeSlot();
    if (slot >= INVENTORY_SIZE) {
        return false; // Inventory full
    }
    
    m_items[slot] = item;
    return true;
}

bool PlayerInventory::RemoveItem(size_t slot, uint16_t quantity) {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    if (slot >= INVENTORY_SIZE || m_items[slot].IsEmpty()) {
        return false;
    }
    
    if (quantity == 0 || quantity >= m_items[slot].quantity) {
        // Remove entire stack
        m_items[slot].Clear();
    } else {
        // Remove partial quantity
        m_items[slot].quantity -= quantity;
    }
    
    return true;
}

ItemInfo PlayerInventory::GetItem(size_t slot) const {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    if (slot >= INVENTORY_SIZE) {
        return ItemInfo{};
    }
    
    return m_items[slot];
}

bool PlayerInventory::SetItem(size_t slot, const ItemInfo& item) {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    if (slot >= INVENTORY_SIZE) {
        return false;
    }
    
    m_items[slot] = item;
    return true;
}

bool PlayerInventory::EquipItem(EquipmentSlot slot, const ItemInfo& item) {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    size_t slotIndex = static_cast<size_t>(slot);
    if (slotIndex >= EQUIPMENT_SLOTS) {
        return false;
    }
    
    m_equipment[slotIndex] = item;
    return true;
}

bool PlayerInventory::UnequipItem(EquipmentSlot slot) {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    size_t slotIndex = static_cast<size_t>(slot);
    if (slotIndex >= EQUIPMENT_SLOTS) {
        return false;
    }
    
    m_equipment[slotIndex].Clear();
    return true;
}

ItemInfo PlayerInventory::GetEquippedItem(EquipmentSlot slot) const {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    size_t slotIndex = static_cast<size_t>(slot);
    if (slotIndex >= EQUIPMENT_SLOTS) {
        return ItemInfo{};
    }
    
    return m_equipment[slotIndex];
}

size_t PlayerInventory::GetFreeSlot() const {
    for (size_t i = 0; i < INVENTORY_SIZE; ++i) {
        if (m_items[i].IsEmpty()) {
            return i;
        }
    }
    return INVENTORY_SIZE; // No free slot
}

size_t PlayerInventory::GetItemCount() const {
    size_t count = 0;
    for (const auto& item : m_items) {
        if (!item.IsEmpty()) {
            ++count;
        }
    }
    return count;
}

bool PlayerInventory::IsFull() const {
    return GetFreeSlot() >= INVENTORY_SIZE;
}

void PlayerInventory::Clear() {
    std::lock_guard<std::mutex> lock(m_inventoryMutex);
    
    m_items.fill(ItemInfo{});
    m_equipment.fill(ItemInfo{});
}

// PlayerSkillManager Implementation
bool PlayerSkillManager::LearnSkill(uint32_t skillCode, uint8_t level) {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    if (skillCode == 0 || level == 0) {
        return false;
    }
    
    if (m_skills.size() >= MAX_SKILLS) {
        return false; // Too many skills
    }
    
    m_skills[skillCode] = SkillInfo(skillCode, level);
    return true;
}

bool PlayerSkillManager::ForgetSkill(uint32_t skillCode) {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    auto it = m_skills.find(skillCode);
    if (it == m_skills.end()) {
        return false;
    }
    
    m_skills.erase(it);
    return true;
}

bool PlayerSkillManager::UpgradeSkill(uint32_t skillCode) {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    auto it = m_skills.find(skillCode);
    if (it == m_skills.end()) {
        return false;
    }
    
    if (it->second.level >= 255) {
        return false; // Max level reached
    }
    
    ++it->second.level;
    return true;
}

SkillInfo PlayerSkillManager::GetSkill(uint32_t skillCode) const {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    auto it = m_skills.find(skillCode);
    if (it == m_skills.end()) {
        return SkillInfo{};
    }
    
    return it->second;
}

bool PlayerSkillManager::CanUseSkill(uint32_t skillCode, uint32_t currentTime) const {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    auto it = m_skills.find(skillCode);
    if (it == m_skills.end()) {
        return false;
    }
    
    return !it->second.IsOnCooldown(currentTime);
}

bool PlayerSkillManager::UseSkill(uint32_t skillCode, uint32_t currentTime, uint32_t cooldown) {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    auto it = m_skills.find(skillCode);
    if (it == m_skills.end()) {
        return false;
    }
    
    if (it->second.IsOnCooldown(currentTime)) {
        return false;
    }
    
    it->second.SetCooldown(currentTime, cooldown);
    return true;
}

std::vector<SkillInfo> PlayerSkillManager::GetAllSkills() const {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    
    std::vector<SkillInfo> skills;
    skills.reserve(m_skills.size());
    
    for (const auto& pair : m_skills) {
        skills.push_back(pair.second);
    }
    
    return skills;
}

size_t PlayerSkillManager::GetSkillCount() const {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    return m_skills.size();
}

void PlayerSkillManager::Clear() {
    std::lock_guard<std::mutex> lock(m_skillMutex);
    m_skills.clear();
}

// PlayerQuestManager Implementation
bool PlayerQuestManager::StartQuest(uint32_t questCode) {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    if (questCode == 0) {
        return false;
    }
    
    if (GetActiveQuestCount() >= MAX_ACTIVE_QUESTS) {
        return false; // Too many active quests
    }
    
    QuestInfo quest(questCode);
    quest.SetActive();
    quest.startTime = static_cast<uint32_t>(std::time(nullptr));
    
    m_quests[questCode] = quest;
    return true;
}

bool PlayerQuestManager::CompleteQuest(uint32_t questCode) {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    auto it = m_quests.find(questCode);
    if (it == m_quests.end() || !it->second.IsActive()) {
        return false;
    }
    
    it->second.SetCompleted();
    return true;
}

bool PlayerQuestManager::AbandonQuest(uint32_t questCode) {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    auto it = m_quests.find(questCode);
    if (it == m_quests.end()) {
        return false;
    }
    
    m_quests.erase(it);
    return true;
}

QuestInfo PlayerQuestManager::GetQuest(uint32_t questCode) const {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    auto it = m_quests.find(questCode);
    if (it == m_quests.end()) {
        return QuestInfo{};
    }
    
    return it->second;
}

bool PlayerQuestManager::UpdateQuestProgress(uint32_t questCode, uint32_t progress) {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    auto it = m_quests.find(questCode);
    if (it == m_quests.end() || !it->second.IsActive()) {
        return false;
    }
    
    it->second.progress = progress;
    return true;
}

bool PlayerQuestManager::SetQuestVariable(uint32_t questCode, size_t index, uint32_t value) {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    auto it = m_quests.find(questCode);
    if (it == m_quests.end() || index >= it->second.variables.size()) {
        return false;
    }
    
    it->second.variables[index] = value;
    return true;
}

uint32_t PlayerQuestManager::GetQuestVariable(uint32_t questCode, size_t index) const {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    auto it = m_quests.find(questCode);
    if (it == m_quests.end() || index >= it->second.variables.size()) {
        return 0;
    }
    
    return it->second.variables[index];
}

std::vector<QuestInfo> PlayerQuestManager::GetActiveQuests() const {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    std::vector<QuestInfo> activeQuests;
    for (const auto& pair : m_quests) {
        if (pair.second.IsActive()) {
            activeQuests.push_back(pair.second);
        }
    }
    
    return activeQuests;
}

std::vector<QuestInfo> PlayerQuestManager::GetCompletedQuests() const {
    std::lock_guard<std::mutex> lock(m_questMutex);
    
    std::vector<QuestInfo> completedQuests;
    for (const auto& pair : m_quests) {
        if (pair.second.IsCompleted()) {
            completedQuests.push_back(pair.second);
        }
    }
    
    return completedQuests;
}

size_t PlayerQuestManager::GetActiveQuestCount() const {
    size_t count = 0;
    for (const auto& pair : m_quests) {
        if (pair.second.IsActive()) {
            ++count;
        }
    }
    return count;
}

void PlayerQuestManager::Clear() {
    std::lock_guard<std::mutex> lock(m_questMutex);
    m_quests.clear();
}

// SocialInfo Implementation
bool SocialInfo::IsFriend(const std::string& playerName) const {
    return std::find(friendList.begin(), friendList.end(), playerName) != friendList.end();
}

bool SocialInfo::IsBlocked(const std::string& playerName) const {
    return std::find(blockList.begin(), blockList.end(), playerName) != blockList.end();
}

void SocialInfo::AddFriend(const std::string& playerName) {
    if (!IsFriend(playerName) && !playerName.empty()) {
        friendList.push_back(playerName);
    }
}

void SocialInfo::RemoveFriend(const std::string& playerName) {
    auto it = std::find(friendList.begin(), friendList.end(), playerName);
    if (it != friendList.end()) {
        friendList.erase(it);
    }
}

void SocialInfo::BlockPlayer(const std::string& playerName) {
    if (!IsBlocked(playerName) && !playerName.empty()) {
        blockList.push_back(playerName);
        // Also remove from friends if present
        RemoveFriend(playerName);
    }
}

void SocialInfo::UnblockPlayer(const std::string& playerName) {
    auto it = std::find(blockList.begin(), blockList.end(), playerName);
    if (it != blockList.end()) {
        blockList.erase(it);
    }
}

} // namespace Player
} // namespace NexusProtection
