/*
 * Function: ?SendGoalMsg@CNormalGuildBattle@GUILD_BATTLE@@IEAAX_NPEADPEAVCPlayer@@H@Z
 * Address: 0x1403E71C0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::SendGoalMsg(GUILD_BATTLE::CNormalGuildBattle *this, bool b1P, char *wszGuildName, CPlayer *pkPlayer, int iPortalInx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@4
  GUILD_BATTLE::CGuildBattleSchedulePool *v8; // rax@8
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@9
  int v10; // eax@15
  unsigned __int16 v11; // ax@16
  char v12; // al@18
  int v13; // eax@19
  __int64 v14; // rax@20
  int v15; // eax@21
  __int64 v16; // [sp+0h] [bp-E8h]@1
  char pbyType; // [sp+34h] [bp-B4h]@4
  char v18; // [sp+35h] [bp-B3h]@4
  char Dst; // [sp+58h] [bp-90h]@4
  char Dest; // [sp+59h] [bp-8Fh]@4
  unsigned int v21; // [sp+6Ah] [bp-7Eh]@4
  char v22; // [sp+6Eh] [bp-7Ah]@4
  unsigned int v23; // [sp+7Fh] [bp-69h]@5
  unsigned int v24; // [sp+83h] [bp-65h]@7
  unsigned int v25; // [sp+87h] [bp-61h]@5
  unsigned int v26; // [sp+8Bh] [bp-5Dh]@7
  char byHour; // [sp+8Fh] [bp-59h]@10
  char byMin; // [sp+90h] [bp-58h]@10
  char bySec; // [sp+91h] [bp-57h]@10
  GUILD_BATTLE::CGuildBattleSchedule *v30; // [sp+A8h] [bp-40h]@8
  unsigned __int8 v31; // [sp+B0h] [bp-38h]@10
  int j; // [sp+B4h] [bp-34h]@10
  CPlayer *v33; // [sp+B8h] [bp-30h]@13
  int v34; // [sp+C8h] [bp-20h]@18
  int v35; // [sp+CCh] [bp-1Ch]@20
  unsigned __int64 v36; // [sp+D0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v37; // [sp+F0h] [bp+8h]@1
  bool v38; // [sp+F8h] [bp+10h]@1
  const char *Source; // [sp+100h] [bp+18h]@1
  CPlayer *v40; // [sp+108h] [bp+20h]@1

  v40 = pkPlayer;
  Source = wszGuildName;
  v38 = b1P;
  v37 = this;
  v5 = &v16;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v36 = (unsigned __int64)&v16 ^ _security_cookie;
  pbyType = 27;
  v18 = 74;
  memset_0(&Dst, 0, 0x3Aui64);
  Dst = 0;
  strcpy_0(&Dest, Source);
  v21 = v40->m_dwObjSerial;
  v7 = CPlayerDB::GetCharNameW(&v40->m_Param);
  strcpy_0(&v22, v7);
  if ( v37->m_pkRed )
  {
    v25 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v37->m_pkRed);
    v23 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v37->m_pkRed);
  }
  if ( v37->m_pkBlue )
  {
    v26 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v37->m_pkBlue);
    v24 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v37->m_pkBlue);
  }
  v8 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v30 = GUILD_BATTLE::CGuildBattleSchedulePool::GetRef(v8, v37->m_dwID);
  if ( v30 )
  {
    GUILD_BATTLE::CGuildBattleSchedule::GetLeftTime(v30, &byHour, &byMin, &bySec);
    v31 = CPlayerDB::GetRaceCode(&v40->m_Param);
    for ( j = 0; j < 2532; ++j )
    {
      v33 = &g_Player + j;
      if ( v33->m_bLive && !v33->m_bBlockGuildBattleMsg )
      {
        v10 = CPlayerDB::GetRaceCode(&v33->m_Param);
        if ( v10 == v31 )
        {
          v11 = _guild_battle_goal_result_zocl::size((_guild_battle_goal_result_zocl *)&Dst);
          CNetProcess::LoadSendMsg(unk_1414F2088, v33->m_ObjID.m_wIndex, &pbyType, &Dst, v11);
        }
      }
    }
    if ( v38
      && (v34 = (unsigned __int8)GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v37->m_k1P),
          v12 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v37->m_k2P),
          v34 != (unsigned __int8)v12) )
    {
      v13 = _guild_battle_goal_result_zocl::size((_guild_battle_goal_result_zocl *)&Dst);
      GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v37->m_k2P, &pbyType, &Dst, v13);
    }
    else
    {
      v35 = (unsigned __int8)GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v37->m_k1P);
      v14 = (unsigned __int8)GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v37->m_k2P);
      if ( v35 != (unsigned __int8)v14 )
      {
        v15 = _guild_battle_goal_result_zocl::size((_guild_battle_goal_result_zocl *)&Dst);
        GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v37->m_k1P, &pbyType, &Dst, v15);
      }
    }
  }
  else
  {
    v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v9,
      "CNormalGuildBattle::SendKillInform() : CGuildBattleSchedulePool::Instance()->GetRef(%u) NULL!",
      v37->m_dwID);
  }
}
