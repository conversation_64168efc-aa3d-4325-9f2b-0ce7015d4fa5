/*
 * Function: ?GetUseCashQueryStr@CCashDBWorkManager@@QEAAXAEAU_param_cash_update@@HPEAD_K@Z
 * Address: 0x1402F3430
 */

void __fastcall CCashDBWorkManager::GetUseCashQueryStr(CCashDBWorkManager *this, _param_cash_update *rParam, int nIdx, char *wszQuery, unsigned __int64 tBufferSize)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CashDbWorker *v7; // rcx@4
  WorkerVtbl *v8; // rax@4
  __int64 v9; // [sp+0h] [bp-38h]@1
  unsigned __int64 v10; // [sp+20h] [bp-18h]@4
  CCashDBWorkManager *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = v11->m_pWorker;
  v8 = v11->m_pWorker->vfptr;
  v10 = tBufferSize;
  ((void (__fastcall *)(CashDbWorker *, _param_cash_update *, _QWORD))v8[2].~Worker)(v7, rParam, (unsigned int)nIdx);
}
