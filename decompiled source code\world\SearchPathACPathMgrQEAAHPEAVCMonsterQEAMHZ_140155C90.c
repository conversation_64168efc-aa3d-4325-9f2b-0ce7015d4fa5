/*
 * Function: ?SearchPathA@CPathMgr@@QEAAHPEAVCMonster@@QEAMH@Z
 * Address: 0x140155C90
 */

signed __int64 __fastcall CPathMgr::SearchPathA(CPathMgr *this, CMonster *pMon, float *vTarPos, int bBackupRestore)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-88h]@1
  unsigned __int32 v8; // [sp+34h] [bp-54h]@8
  unsigned __int32 v9; // [sp+44h] [bp-44h]@8
  float v10[3]; // [sp+58h] [bp-30h]@8
  CLevel *v11; // [sp+78h] [bp-10h]@9
  CPathMgr *pDst; // [sp+90h] [bp+8h]@1
  CMonster *v13; // [sp+98h] [bp+10h]@1
  float *Src; // [sp+A0h] [bp+18h]@1
  int v15; // [sp+A8h] [bp+20h]@1

  v15 = bBackupRestore;
  Src = vTarPos;
  v13 = pMon;
  pDst = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pMon )
  {
    if ( bBackupRestore )
      CPathMgr::Copy(pDst, &CPathMgr::ms_BackupPath);
    v8 = 0;
    CPathMgr::Init(pDst);
    v9 = 0;
    if ( (unsigned int)CBsp::CanYouGoThere(v13->m_pCurMap->m_Level.mBsp, v13->m_fCurPos, Src, (float (*)[3])v10) )
    {
      pDst->m_Size = 1;
      pDst->m_StartPos = 0;
      memcpy_0(pDst->m_PosPool, Src, 0xCui64);
      result = pDst->m_Size;
    }
    else
    {
      v11 = &v13->m_pCurMap->m_Level;
      v9 = CLevel::GetPathFromDepth(v11, v13->m_fCurPos, Src, 16, pDst->m_PosPool, &v8);
      if ( v9 )
      {
        pDst->m_Size = v8;
        pDst->m_StartPos = 0;
        result = pDst->m_Size;
      }
      else
      {
        if ( v15 )
          CPathMgr::Copy(&CPathMgr::ms_BackupPath, pDst);
        result = 0xFFFFFFFFi64;
      }
    }
  }
  else
  {
    result = 4294967294i64;
  }
  return result;
}
