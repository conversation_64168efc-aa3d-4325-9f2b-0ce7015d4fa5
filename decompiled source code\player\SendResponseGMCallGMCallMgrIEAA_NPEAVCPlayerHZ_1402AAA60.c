/*
 * Function: ?SendResponseGMCall@GMCallMgr@@IEAA_NPEAVCPlayer@@H@Z
 * Address: 0x1402AAA60
 */

char __fastcall GMCallMgr::SendResponseGMCall(GMCallMgr *this, CPlayer *pOne, int bCallState)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@6
  char pbyType; // [sp+54h] [bp-24h]@6
  char v9; // [sp+55h] [bp-23h]@6

  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pOne )
  {
    *(_DWORD *)szMsg = bCallState;
    pbyType = 55;
    v9 = 4;
    CNetProcess::LoadSendMsg(unk_1414F2088, pOne->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
