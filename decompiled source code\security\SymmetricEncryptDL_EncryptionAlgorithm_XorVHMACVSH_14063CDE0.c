/*
 * Function: ?SymmetricEncrypt@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE1_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x14063CDE0
 */

void __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>::SymmetricEncrypt(unsigned __int8 *a1, CryptoPP *a2, CryptoPP::NameValuePairs *a3, unsigned __int8 *a4, unsigned __int8 *a5, CryptoPP *a6, CryptoPP::NameValuePairs *a7)
{
  unsigned __int8 *v7; // ST30_8@1
  const char *v8; // rax@1
  const unsigned __int8 *v9; // rax@1
  unsigned int v10; // eax@1
  char *xorBlock; // [sp+20h] [bp-178h]@0
  CryptoPP::ConstByteArrayParameter value; // [sp+38h] [bp-160h]@1
  CryptoPP::HMAC<CryptoPP::SHA1> v13; // [sp+68h] [bp-130h]@1
  char *key; // [sp+158h] [bp-40h]@1
  unsigned __int8 v15; // [sp+168h] [bp-30h]@1
  char v16; // [sp+169h] [bp-2Fh]@1
  char v17; // [sp+16Ah] [bp-2Eh]@1
  char v18; // [sp+16Bh] [bp-2Dh]@1
  char block; // [sp+16Ch] [bp-2Ch]@1
  __int64 v20; // [sp+170h] [bp-28h]@1
  unsigned __int64 v21; // [sp+178h] [bp-20h]@1
  unsigned __int8 *v22; // [sp+1B8h] [bp+20h]@1

  v22 = a4;
  v20 = -2i64;
  key = (char *)a3;
  v7 = (unsigned __int8 *)&a3[2];
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&value, 0i64, 0);
  v8 = CryptoPP::Name::EncodingParameters();
  CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>(a7, v8, &value);
  CryptoPP::xorbuf(a6, v22, v7, a5, (unsigned __int64)xorBlock);
  CryptoPP::HMAC<CryptoPP::SHA1>::HMAC<CryptoPP::SHA1>(&v13, key, 0x10ui64);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v13.vfptr, (const unsigned __int8 *)a6, (__int64)a5);
  v21 = CryptoPP::ConstByteArrayParameter::size(&value);
  v9 = (const unsigned __int8 *)CryptoPP::ConstByteArrayParameter::begin(&value);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v13.vfptr, v9, v21);
  v15 = 0;
  v16 = 0;
  v17 = 0;
  v18 = 0;
  memset(&block, 0, 4ui64);
  v10 = CryptoPP::ConstByteArrayParameter::size(&value);
  CryptoPP::PutWord<unsigned int>(0, BIG_ENDIAN_ORDER, &block, v10, 0i64);
  CryptoPP::HMAC_Base::Update((CryptoPP::HMAC_Base *)&v13.vfptr, &v15, 8i64);
  CryptoPP::HashTransformation::Final((CryptoPP::HashTransformation *)&v13.vfptr, (char *)a6 + (_QWORD)a5);
  CryptoPP::HMAC<CryptoPP::SHA1>::~HMAC<CryptoPP::SHA1>(&v13);
  CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter(&value);
}
