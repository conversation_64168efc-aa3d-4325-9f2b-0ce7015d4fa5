/*
 * Function: ?MakeBuddyPacket@CGuild@@QEAAXXZ
 * Address: 0x1402551E0
 */

void __fastcall CGuild::MakeBuddyPacket(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-1F8h]@1
  unsigned __int8 Src; // [sp+24h] [bp-1D4h]@4
  int v5; // [sp+34h] [bp-1C4h]@4
  void *v6; // [sp+38h] [bp-1C0h]@4
  int Dst; // [sp+50h] [bp-1A8h]@4
  __int16 v8; // [sp+54h] [bp-1A4h]@9
  char v9[398]; // [sp+56h] [bp-1A2h]@9
  int j; // [sp+1E4h] [bp-14h]@4
  _guild_member_info *v11; // [sp+1E8h] [bp-10h]@7
  CGuild *v12; // [sp+200h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 124i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12->m_Buddy_List->wDataSize = 0;
  Src = 0;
  v5 = 0;
  v6 = v12->m_Buddy_List->sData;
  memset_0(&Dst, 0, 0x190ui64);
  for ( j = 0; j < 50; ++j )
  {
    v11 = &v12->m_MemberData[j];
    if ( _guild_member_info::IsFill(v11) )
    {
      if ( v11->pPlayer )
      {
        *(&Dst + 2 * Src) = v11->dwSerial;
        *(&v8 + 4 * Src) = v11->pPlayer->m_wRegionMapIndex;
        v9[8 * (unsigned __int64)Src++] = v11->pPlayer->m_wRegionIndex;
      }
    }
  }
  memcpy_0(v6, &Src, 1ui64);
  v6 = (char *)v6 + 1;
  ++v5;
  for ( j = 0; j < Src; ++j )
  {
    memcpy_0(v6, &Dst + 2 * j, 4ui64);
    v6 = (char *)v6 + 4;
    v5 += 4;
    memcpy_0(v6, &v8 + 4 * j, 2ui64);
    v6 = (char *)v6 + 2;
    v5 += 2;
    memcpy_0(v6, &v9[8 * j], 1ui64);
    v6 = (char *)v6 + 1;
    ++v5;
  }
  v12->m_Buddy_List->wDataSize = v5;
}
