/*
 * Function: ?GetFireTol@CMonster@@UEAAHXZ
 * Address: 0x140145620
 */

__int64 __usercall CMonster::GetFireTol@<rax>(CMonster *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@4
  __int64 result; // rax@10
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@4
  _base_fld *v8; // [sp+28h] [bp-20h]@4
  float v9; // [sp+30h] [bp-18h]@4
  CMonster *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = v10->m_pRecordSet;
  _effect_parameter::GetEff_Plus(&v10->m_EP, 15);
  v4 = *(float *)&v8[5].m_strCode[20] + a2;
  v9 = v4;
  _effect_parameter::GetEff_Rate(&v10->m_EP, 25);
  v7 = (signed int)ffloor(v9 * v4);
  if ( v7 >= -200 )
  {
    if ( v7 > 200 )
      v7 = 200;
  }
  else
  {
    v7 = -200;
  }
  if ( _effect_parameter::GetEff_State(&v10->m_EP, 19) && v7 > 0 )
    result = (unsigned int)-v7;
  else
    result = (unsigned int)v7;
  return result;
}
