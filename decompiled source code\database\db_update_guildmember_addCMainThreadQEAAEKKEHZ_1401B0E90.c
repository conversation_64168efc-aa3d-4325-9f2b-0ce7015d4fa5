/*
 * Function: ?db_update_guildmember_add@CMainThread@@QEAAEKKEH@Z
 * Address: 0x1401B0E90
 */

char __fastcall CMainThread::db_update_guildmember_add(CMainThread *this, unsigned int dwAvatorSerial, unsigned int dwGuildSerial, char byGrade, int nMemberNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  CMainThread *v9; // [sp+30h] [bp+8h]@1
  unsigned int dwAvatorSeriala; // [sp+38h] [bp+10h]@1
  unsigned int dwGuildSeriala; // [sp+40h] [bp+18h]@1
  char v12; // [sp+48h] [bp+20h]@1

  v12 = byGrade;
  dwGuildSeriala = dwGuildSerial;
  dwAvatorSeriala = dwAvatorSerial;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( nMemberNum <= 50 )
  {
    if ( CRFWorldDatabase::Check_GuildMemberCount(v9->m_pWorldDB, dwGuildSerial) )
    {
      if ( CRFWorldDatabase::Update_UserGuildData(v9->m_pWorldDB, dwAvatorSeriala, dwGuildSeriala, v12) )
      {
        if ( CRFWorldDatabase::Update_GuildMemberCount(v9->m_pWorldDB, dwGuildSeriala, nMemberNum) )
          result = 0;
        else
          result = 24;
      }
      else
      {
        result = 24;
      }
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
