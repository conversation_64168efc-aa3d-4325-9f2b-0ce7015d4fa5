/*
 * Function: ??0?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VBase@DES@CryptoPP@@@CryptoPP@@VCBC_Encryption@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14061B400
 */

CryptoPP::BlockOrientedCipherModeBase *__fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>(CryptoPP::BlockOrientedCipherModeBase *a1)
{
  CryptoPP::BlockOrientedCipherModeBase *v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>>::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>>(&a1[1]);
  CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>>::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>>(v2);
  v2->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v2->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  v2->m_cipher = (CryptoPP::SimpleKeyedTransformation<CryptoPP::BlockTransformation> *)&v2[1];
  CryptoPP::BlockOrientedCipherModeBase::ResizeBuffers(v2);
  return v2;
}
