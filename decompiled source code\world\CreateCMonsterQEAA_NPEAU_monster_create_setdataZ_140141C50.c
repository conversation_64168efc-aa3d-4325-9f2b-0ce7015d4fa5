/*
 * Function: ?Create@CMonster@@QEAA_NPEAU_monster_create_setdata@@@Z
 * Address: 0x140141C50
 */

char __usercall CMonster::Create@<al>(CMonster *this@<rcx>, _monster_create_setdata *pData@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // xmm0_4@12
  int v6; // eax@18
  char result; // al@21
  __int64 v8; // [sp+0h] [bp-88h]@1
  float v; // [sp+28h] [bp-60h]@12
  float v10; // [sp+2Ch] [bp-5Ch]@12
  float v11; // [sp+30h] [bp-58h]@12
  float Src; // [sp+58h] [bp-30h]@12
  float v13; // [sp+5Ch] [bp-2Ch]@12
  float v14; // [sp+60h] [bp-28h]@12
  int v15; // [sp+74h] [bp-14h]@12
  int nUserNode; // [sp+78h] [bp-10h]@16
  bool v17; // [sp+7Ch] [bp-Ch]@7
  CMonster *v18; // [sp+90h] [bp+8h]@1
  _monster_create_setdata *pDataa; // [sp+98h] [bp+10h]@1

  pDataa = pData;
  v18 = this;
  v3 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CCharacter::Create((CCharacter *)&v18->vfptr, (_character_create_setdata *)&pData->m_pRecordSet) )
  {
    v18->m_pMonRec = (_monster_fld *)pDataa->m_pRecordSet;
    memcpy_0(v18->m_fCreatePos, pDataa->m_fStartPos, 0xCui64);
    memcpy_0(v18->m_fTarPos, pDataa->m_fStartPos, 0xCui64);
    memcpy_0(v18->m_fLookAtPos, pDataa->m_fStartPos, 0xCui64);
    v18->m_bRotateMonster = 0;
    EmotionPresentationChecker::ReSet(&v18->m_EmotionPresentationCheck);
    v18->m_bRobExp = pDataa->bRobExp;
    v18->m_bRewardExp = pDataa->bRewardExp;
    v18->m_bStdItemLoot = 1;
    v18->m_pEventRespawn = 0i64;
    v18->m_nEventItemNum = 0;
    v18->m_pActiveRec = 0i64;
    v18->m_pDumPosition = 0i64;
    v18->m_pEventSet = 0i64;
    if ( pDataa->pActiveRec )
    {
      v18->m_pActiveRec = pDataa->pActiveRec;
      _mon_active::SetCurMonNum(v18->m_pActiveRec, 1);
      if ( v18->m_pActiveRec->m_pBlk )
        v17 = v18->m_pActiveRec->m_pBlk->m_bRotate;
      else
        v17 = 0;
      v18->m_bRotateMonster = v17;
    }
    if ( pDataa->pDumPosition )
    {
      v18->m_pDumPosition = pDataa->pDumPosition;
      if ( v18->m_bRotateMonster )
      {
        v = v18->m_pDumPosition->m_fDirection[0] - v18->m_pDumPosition->m_fCenterPos[0];
        v10 = v18->m_pDumPosition->m_fDirection[1] - v18->m_pDumPosition->m_fCenterPos[1];
        v11 = v18->m_pDumPosition->m_fDirection[2] - v18->m_pDumPosition->m_fCenterPos[2];
        *(float *)&v5 = v11;
        Normalize(&v);
        CMonster::GetVisualField(v18);
        v15 = v5;
        v = *(float *)&v5 * v;
        v10 = *(float *)&v5 * v10;
        v11 = *(float *)&v5 * v11;
        Src = v18->m_fCurPos[0] + v;
        v13 = v18->m_fCurPos[1] + v10;
        a3 = v18->m_fCurPos[2] + v11;
        v14 = v18->m_fCurPos[2] + v11;
        memcpy_0(v18->m_fStartLookAtPos, &Src, 0xCui64);
        CMonster::UpdateLookAtPos(v18, &Src);
      }
      _dummy_position::SetActiveMonNum(v18->m_pDumPosition, 1);
    }
    else if ( v18->m_bRotateMonster )
    {
      memcpy_0(v18->m_fStartLookAtPos, v18->m_fCreatePos, 0xCui64);
      a3 = v18->m_fLookAtPos[2] - 10.0;
      v18->m_fLookAtPos[2] = a3;
      memcpy_0(v18->m_fStartLookAtPos, v18->m_fLookAtPos, 0xCui64);
      CMonster::UpdateLookAtPos(v18, v18->m_fLookAtPos);
    }
    v18->m_bDungeon = pDataa->bDungeon;
    v18->m_dwObjSerial = CMonster::GetNewMonSerial();
    nUserNode = 16;
    if ( v18->m_pMonRec->m_bMonsterCondition == 1 )
      nUserNode = 64;
    CLootingMgr::Init(&v18->m_LootMgr, nUserNode);
    CMonsterAggroMgr::Init(&v18->m_AggroMgr);
    v6 = CMonster::GetMonsterGrade(v18);
    MonsterSetInfoData::GetMaxToleranceProbMax(&g_MonsterSetInfoData, v6);
    MonsterSFContDamageToleracne::Init(&v18->m_SFContDamageTolerance, a3);
    CLuaSignalReActor::Init(&v18->m_LuaSignalReActor);
    v18->m_nHP = (signed int)ffloor(v18->m_pMonRec->m_fMaxHP);
    v18->m_dwDestroyNextTime = -1;
    v18->m_bApparition = 0;
    v18->m_dwLastRecoverTime = GetLoopTime();
    v18->m_LifeMax = 60000 * (rand() % 3) + 600000;
    v18->m_LifeCicle = GetLoopTime();
    if ( v18->m_pMonRec->m_bMonsterCondition == 1 && !pDataa->pParent )
    {
      v18->m_byCreateDate[0] = GetCurrentMonth();
      v18->m_byCreateDate[1] = GetCurrentDay();
      v18->m_byCreateDate[2] = GetCurrentHour();
      v18->m_byCreateDate[3] = GetCurrentMin();
      CMonster::_BossBirthWriteLog(v18);
    }
    CMonster::SetDefPart(v18, v18->m_pMonRec);
    CMonsterSkillPool::Set(&v18->m_MonsterSkillPool, v18);
    CMonster::CreateAI(v18, 0);
    v18->m_bOper = 1;
    CMonster::SetMoveType(v18, 0);
    CMonster::CheckMonsterStateData(v18);
    CMonster::SendMsg_Create(v18);
    CMonsterHierarchy::OnChildMonsterCreate(&v18->m_MonHierarcy, pDataa);
    ++CMonster::s_nLiveNum;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
