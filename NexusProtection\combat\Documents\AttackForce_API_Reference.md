# AttackForce System API Reference

## CAttackForce Class

### Constructor
```cpp
explicit CAttackForce(CCharacter* pAttacker);
```
Creates an AttackForce instance for the specified attacking character.

**Parameters:**
- `pAttacker`: Pointer to the attacking character (must not be null)

**Throws:**
- `std::invalid_argument`: If pAttacker is null

### Core Methods

#### ExecuteAttack
```cpp
AttackForceResult ExecuteAttack(_attack_param* pParam, bool bUseEffBullet = false, float fAccuracyBonus = 0.0f);
```
Executes a complete attack sequence including hit calculation and damage processing.

**Parameters:**
- `pParam`: Attack parameters structure
- `bUseEffBullet`: Whether to use effect bullet calculations
- `fAccuracyBonus`: Additional accuracy bonus percentage

**Returns:**
- `AttackForceResult`: Complete attack result with damage, hit status, and target information

#### CalculateHitChance
```cpp
float CalculateHitChance(CCharacter* pTarget, float fAccuracyBonus = 0.0f);
```
Calculates the hit chance percentage against a target.

**Parameters:**
- `pTarget`: Target character
- `fAccuracyBonus`: Additional accuracy bonus

**Returns:**
- `float`: Hit chance percentage (0-100)

#### CalculateDamage
```cpp
DamageCalculationParams CalculateDamage(_attack_param* pParam, bool bUseEffBullet = false);
```
Calculates damage parameters for an attack.

**Parameters:**
- `pParam`: Attack parameters
- `bUseEffBullet`: Use effect bullet calculations

**Returns:**
- `DamageCalculationParams`: Detailed damage calculation results

### Utility Methods

#### IsValidAttacker
```cpp
bool IsValidAttacker() const;
```
Checks if the attacker is in a valid state for attacking.

**Returns:**
- `bool`: True if attacker is valid

#### GetAttacker
```cpp
CCharacter* GetAttacker() const;
```
Gets the attacking character.

**Returns:**
- `CCharacter*`: Pointer to the attacking character

## CAttackForceDamageCalculator Class

### Constructor
```cpp
CAttackForceDamageCalculator();
```
Creates a damage calculator instance with default configuration.

### Core Methods

#### CalculateDamage
```cpp
DetailedDamageResult CalculateDamage(const DamageCalculationContext& context);
```
Performs comprehensive damage calculation with detailed breakdown.

**Parameters:**
- `context`: Damage calculation context with all required information

**Returns:**
- `DetailedDamageResult`: Detailed damage result with modifiers and calculation log

#### CalculateBaseAttackPower
```cpp
float CalculateBaseAttackPower(CCharacter* pAttacker, _attack_param* pParam, bool bUseEffBullet = false);
```
Calculates base attack power before modifiers.

**Parameters:**
- `pAttacker`: Attacking character
- `pParam`: Attack parameters
- `bUseEffBullet`: Use effect bullet calculations

**Returns:**
- `float`: Base attack power value

#### CalculateHitChance
```cpp
float CalculateHitChance(CCharacter* pAttacker, CCharacter* pTarget, float fAccuracyBonus = 0.0f);
```
Calculates hit chance between attacker and target.

**Parameters:**
- `pAttacker`: Attacking character
- `pTarget`: Target character
- `fAccuracyBonus`: Additional accuracy bonus

**Returns:**
- `float`: Hit chance percentage

#### CalculateCriticalChance
```cpp
float CalculateCriticalChance(CCharacter* pAttacker, CCharacter* pTarget);
```
Calculates critical hit chance.

**Parameters:**
- `pAttacker`: Attacking character
- `pTarget`: Target character

**Returns:**
- `float`: Critical hit chance percentage

### Modifier Methods

#### ApplyDamageModifiers
```cpp
void ApplyDamageModifiers(const DamageCalculationContext& context, DetailedDamageResult& result);
```
Applies all damage modifiers to the result.

#### ApplyDestroyerBonus
```cpp
void ApplyDestroyerBonus(CCharacter* pAttacker, DetailedDamageResult& result);
```
Applies destroyer character bonus.

#### ApplyPvPBonuses
```cpp
void ApplyPvPBonuses(CCharacter* pAttacker, DetailedDamageResult& result);
```
Applies PvP ranking bonuses.

## CAttackForceAreaProcessor Class

### Constructor
```cpp
explicit CAttackForceAreaProcessor(std::shared_ptr<CAttackForceDamageCalculator> pDamageCalculator);
```
Creates an area processor with the specified damage calculator.

**Parameters:**
- `pDamageCalculator`: Shared pointer to damage calculator

### Area Attack Methods

#### ProcessAreaDamage
```cpp
AreaDamageResult ProcessAreaDamage(CCharacter* pAttacker, const AreaDamageConfig& config, _attack_param* pParam);
```
Processes circular area damage attack.

**Parameters:**
- `pAttacker`: Attacking character
- `config`: Area damage configuration
- `pParam`: Attack parameters

**Returns:**
- `AreaDamageResult`: Results with all hit targets and damage values

#### ProcessFlashDamage
```cpp
AreaDamageResult ProcessFlashDamage(CCharacter* pAttacker, const FlashDamageConfig& config, _attack_param* pParam);
```
Processes cone-shaped flash damage attack.

**Parameters:**
- `pAttacker`: Attacking character
- `config`: Flash damage configuration
- `pParam`: Attack parameters

**Returns:**
- `AreaDamageResult`: Results with all hit targets and damage values

#### ProcessSectorDamage
```cpp
AreaDamageResult ProcessSectorDamage(CCharacter* pAttacker, const SectorDamageConfig& config, _attack_param* pParam);
```
Processes sector-shaped damage attack.

**Parameters:**
- `pAttacker`: Attacking character
- `config`: Sector damage configuration
- `pParam`: Attack parameters

**Returns:**
- `AreaDamageResult`: Results with all hit targets and damage values

### Target Finding Methods

#### FindTargetsInArea
```cpp
std::vector<CCharacter*> FindTargetsInArea(const AreaDamageConfig& config, CCharacter* pAttacker);
```
Finds all valid targets within an area.

#### FindTargetsInFlash
```cpp
std::vector<CCharacter*> FindTargetsInFlash(const FlashDamageConfig& config, CCharacter* pAttacker);
```
Finds all valid targets within a flash cone.

#### FindTargetsInSector
```cpp
std::vector<CCharacter*> FindTargetsInSector(const SectorDamageConfig& config, CCharacter* pAttacker);
```
Finds all valid targets within a sector.

## CAttackForceErrorHandler Class

### Constructor
```cpp
CAttackForceErrorHandler();
```
Creates an error handler with default configuration.

### Validation Methods

#### ValidateAttackParameters
```cpp
ValidationResult ValidateAttackParameters(_attack_param* pParam);
```
Validates attack parameters for correctness.

**Parameters:**
- `pParam`: Attack parameters to validate

**Returns:**
- `ValidationResult`: Validation result with errors and warnings

#### ValidateAttacker
```cpp
ValidationResult ValidateAttacker(CCharacter* pAttacker);
```
Validates attacking character state.

**Parameters:**
- `pAttacker`: Character to validate

**Returns:**
- `ValidationResult`: Validation result

#### ValidateTarget
```cpp
ValidationResult ValidateTarget(CCharacter* pTarget, CCharacter* pAttacker = nullptr);
```
Validates target character state.

**Parameters:**
- `pTarget`: Target character to validate
- `pAttacker`: Attacking character for context (optional)

**Returns:**
- `ValidationResult`: Validation result

#### ValidateDamageContext
```cpp
ValidationResult ValidateDamageContext(CCharacter* pAttacker, CCharacter* pTarget, _attack_param* pParam);
```
Validates complete damage calculation context.

**Parameters:**
- `pAttacker`: Attacking character
- `pTarget`: Target character
- `pParam`: Attack parameters

**Returns:**
- `ValidationResult`: Validation result

### Error Handling Methods

#### HandleError
```cpp
bool HandleError(const AttackForceError& error, ErrorRecoveryContext& context);
```
Handles an error with recovery strategy.

**Parameters:**
- `error`: Error to handle
- `context`: Recovery context

**Returns:**
- `bool`: True if error was handled successfully

#### CreateError
```cpp
AttackForceError CreateError(AttackForceErrorCategory category, AttackForceErrorSeverity severity,
                            const std::string& message, const std::string& function = "",
                            const std::string& file = "", int line = 0);
```
Creates a new error with context information.

**Parameters:**
- `category`: Error category
- `severity`: Error severity level
- `message`: Error message
- `function`: Function name (optional)
- `file`: File name (optional)
- `line`: Line number (optional)

**Returns:**
- `AttackForceError`: Created error object

### Configuration Methods

#### SetErrorCallback
```cpp
void SetErrorCallback(std::function<void(const AttackForceError&)> callback);
```
Sets a callback function for error notifications.

#### SetRecoveryStrategy
```cpp
void SetRecoveryStrategy(AttackForceErrorCategory category, ErrorRecoveryStrategy strategy);
```
Sets recovery strategy for an error category.

#### SetDetailedLogging
```cpp
void SetDetailedLogging(bool bEnable);
```
Enables or disables detailed logging.

## Data Structures

### AttackForceResult
```cpp
struct AttackForceResult {
    bool bSuccess{false};                    // Overall success
    bool bHit{false};                       // Whether attack hit
    int nDamage{0};                         // Total damage dealt
    int nTargetsHit{0};                     // Number of targets hit
    std::vector<CCharacter*> hitTargets;    // List of hit targets
    std::string errorMessage;               // Error message if failed
};
```

### DamageCalculationContext
```cpp
struct DamageCalculationContext {
    CCharacter* pAttacker{nullptr};         // Attacking character
    CCharacter* pTarget{nullptr};           // Target character
    _attack_param* pParam{nullptr};         // Attack parameters
    bool bUseEffBullet{false};             // Use effect bullet
    bool bBackAttack{false};               // Back attack flag
    bool bCriticalHit{false};              // Critical hit flag
    float fAccuracyBonus{0.0f};            // Accuracy bonus
};
```

### DetailedDamageResult
```cpp
struct DetailedDamageResult {
    int nFinalDamage{0};                    // Final damage value
    int nBaseDamage{0};                     // Base damage before modifiers
    float fTotalMultiplier{1.0f};           // Total damage multiplier
    bool bCriticalHit{false};               // Critical hit occurred
    bool bBackAttack{false};                // Back attack occurred
    std::vector<DamageModifier> appliedModifiers; // Applied modifiers
    std::string calculationLog;             // Calculation step log
};
```

### AreaDamageConfig
```cpp
struct AreaDamageConfig {
    float fCenterX{0.0f};                   // Center X coordinate
    float fCenterY{0.0f};                   // Center Y coordinate
    float fCenterZ{0.0f};                   // Center Z coordinate
    float fRadius{0.0f};                    // Damage radius
    float fInnerRadius{0.0f};               // Inner radius (no falloff)
    bool bUseFalloff{true};                 // Use damage falloff
    bool bIgnoreObstacles{false};           // Ignore line of sight
    int nMaxTargets{50};                    // Maximum targets
};
```

### ValidationResult
```cpp
struct ValidationResult {
    bool bValid{true};                      // Overall validity
    std::vector<AttackForceError> errors;   // Validation errors
    std::vector<AttackForceError> warnings; // Validation warnings
};
```

## Utility Functions

### AreaDamageUtils Namespace

#### CreateAreaConfig
```cpp
AreaDamageConfig CreateAreaConfig(_attack_param* pParam, int nLimitRadius);
```
Creates area damage configuration from legacy parameters.

#### CreateFlashConfig
```cpp
FlashDamageConfig CreateFlashConfig(_attack_param* pParam, int nLimitDistance, int nLimitAngle);
```
Creates flash damage configuration from legacy parameters.

#### CalculateDistance3D
```cpp
float CalculateDistance3D(float x1, float y1, float z1, float x2, float y2, float z2);
```
Calculates 3D distance between two points.

### DamageCalculationUtils Namespace

#### ApplyDamageVariance
```cpp
int ApplyDamageVariance(int nBaseDamage, float fVariancePercent = 0.1f);
```
Applies random variance to damage value.

#### CalculateElementalModifier
```cpp
float CalculateElementalModifier(int nAttackerElement, int nTargetElement);
```
Calculates elemental damage modifier.

#### IsCriticalHit
```cpp
bool IsCriticalHit(float fCriticalChance);
```
Determines if an attack is a critical hit based on chance.

#### ClampDamage
```cpp
int ClampDamage(int nDamage);
```
Clamps damage value to valid range.

## Error Handling Macros

```cpp
#define ATTACK_FORCE_ERROR(handler, category, message)
#define ATTACK_FORCE_WARNING(handler, category, message)
#define ATTACK_FORCE_CRITICAL(handler, category, message)
#define ATTACK_FORCE_VALIDATE_PARAM(handler, param, result)
#define ATTACK_FORCE_VALIDATE_ATTACKER(handler, attacker, result)
#define ATTACK_FORCE_VALIDATE_TARGET(handler, target, attacker, result)
```

These macros provide convenient error creation and validation with automatic context information.
