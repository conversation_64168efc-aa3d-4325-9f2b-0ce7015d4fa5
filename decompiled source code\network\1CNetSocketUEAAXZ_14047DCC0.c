/*
 * Function: ??1CNetSocket@@UEAA@XZ
 * Address: 0x14047DCC0
 */

void __fastcall CNetSocket::~CNetSocket(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CNetSocket *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CNetSocketVtbl *)&CNetSocket::`vftable';
  if ( v5->m_bSetSocket )
    CNetSocket::Release(v5);
  CNetIndexList::~CNetIndexList(&v5->m_listIPCheck_Empty);
  CNetIndexList::~CNetIndexList(&v5->m_listIPCheck);
}
