/*
 * Function: ?SendMsg_MineCompleteResult@CPlayer@@QEAAXEEGEG@Z
 * Address: 0x1400DC6A0
 */

void __fastcall CPlayer::SendMsg_MineCompleteResult(CPlayer *this, char byErrCode, char byNewOreIndex, unsigned __int16 dwOreSerial, char byOreDur, unsigned __int16 dwBatteryLeftDurPoint)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  __int16 v10; // [sp+39h] [bp-4Fh]@4
  unsigned __int16 v11; // [sp+3Bh] [bp-4Dh]@4
  char v12; // [sp+3Dh] [bp-4Bh]@4
  unsigned __int16 v13; // [sp+3Eh] [bp-4Ah]@4
  char v14; // [sp+40h] [bp-48h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v16; // [sp+65h] [bp-23h]@4
  CPlayer *v17; // [sp+90h] [bp+8h]@1

  v17 = this;
  v6 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  szMsg = byErrCode;
  v14 = byOreDur;
  v12 = byNewOreIndex;
  v13 = dwOreSerial;
  v10 = v17->m_Param.m_dbEquip.m_pStorageList[6].m_dwDur;
  v11 = dwBatteryLeftDurPoint;
  pbyType = 14;
  v16 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
