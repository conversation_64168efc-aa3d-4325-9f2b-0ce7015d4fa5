/*
 * Function: ?ExitWorldRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C9D20
 */

char __fastcall CNetworkEX::ExitWorldRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  char *v7; // [sp+20h] [bp-28h]@4
  CPlayer *v8; // [sp+28h] [bp-20h]@4
  _socket *v9; // [sp+30h] [bp-18h]@7
  CNetworkEX *v10; // [sp+50h] [bp+8h]@1
  int dwSocketIndex; // [sp+58h] [bp+10h]@1

  dwSocketIndex = n;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    if ( v10->m_bUseFG )
    {
      v9 = CNetWorking::GetSocket((CNetWorking *)&g_Network.vfptr, 0, n);
      _CcrFG_rs_CloseUserContext(&v9[dwSocketIndex].m_hFGContext);
    }
    CPlayer::pc_ExitWorldRequest(v8);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
