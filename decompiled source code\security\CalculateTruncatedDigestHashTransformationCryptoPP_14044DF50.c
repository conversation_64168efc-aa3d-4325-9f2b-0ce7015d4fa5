/*
 * Function: ?CalculateTruncatedDigest@HashTransformation@CryptoPP@@UEAAXPEAE_KPEBE1@Z
 * Address: 0x14044DF50
 */

void __fastcall CryptoPP::HashTransformation::CalculateTruncatedDigest(CryptoPP::HashTransformation *this, char *digest, unsigned __int64 digestSize, const char *input, unsigned __int64 length)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CryptoPP::HashTransformation *v8; // [sp+30h] [bp+8h]@1
  char *v9; // [sp+38h] [bp+10h]@1
  unsigned __int64 v10; // [sp+40h] [bp+18h]@1

  v10 = digestSize;
  v9 = digest;
  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  ((void (__fastcall *)(CryptoPP::HashTransformation *, const char *, unsigned __int64))v8->vfptr[1].Clone)(
    v8,
    input,
    length);
  ((void (__fastcall *)(CryptoPP::HashTransformation *, char *, unsigned __int64))v8->vfptr[7].__vecDelDtor)(
    v8,
    v9,
    v10);
}
