/*
 * Function: j_??$unchecked_uninitialized_copy@PEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@stdext@@YAPEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZ00AEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@Z
 * Address: 0x14000686B
 */

void (__cdecl **__fastcall stdext::unchecked_uninitialized_copy<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), void (__cdecl **_Last)(CUserRankingProcess *this), void (__cdecl **_Dest)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al))(CUserRankingProcess *this)
{
  return stdext::unchecked_uninitialized_copy<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
