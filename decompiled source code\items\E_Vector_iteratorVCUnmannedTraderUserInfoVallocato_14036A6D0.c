/*
 * Function: ??E?$_Vector_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x14036A6D0
 */

std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *__fastcall std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator++(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator++((std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v5->_Mycont);
  return v5;
}
