/*
 * Function: ?_GetAreaEffectMember@CCharacter@@QEAAHPEAV1@_NHPEAMPEADPEAPEAV1@@Z
 * Address: 0x140177440
 */

__int64 __fastcall CCharacter::_GetAreaEffectMember(CCharacter *this, CCharacter *pOriDst, bool bBenefit, int nLimitRadius, float *pTar, char *psActableDst, CCharacter **ppDsts)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  _sec_info *v10; // rax@11
  int v11; // eax@21
  int v12; // eax@26
  float v13; // xmm0_4@36
  float v14; // xmm1_4@37
  __int64 v15; // [sp+0h] [bp-C8h]@1
  int nSecNum; // [sp+20h] [bp-A8h]@4
  unsigned int v17; // [sp+24h] [bp-A4h]@6
  _pnt_rect pRect; // [sp+38h] [bp-90h]@6
  int j; // [sp+54h] [bp-74h]@6
  int k; // [sp+58h] [bp-70h]@8
  unsigned int dwSecIndex; // [sp+5Ch] [bp-6Ch]@11
  CObjectList *v22; // [sp+60h] [bp-68h]@11
  CObjectList *v23; // [sp+68h] [bp-60h]@12
  CCharacter *pDst; // [sp+70h] [bp-58h]@14
  CPlayer *v25; // [sp+78h] [bp-50h]@23
  CPlayer *v26; // [sp+80h] [bp-48h]@28
  float v27; // [sp+88h] [bp-40h]@36
  int v28; // [sp+8Ch] [bp-3Ch]@40
  int v29; // [sp+90h] [bp-38h]@21
  CGameObjectVtbl *v30; // [sp+98h] [bp-30h]@21
  int v31; // [sp+A0h] [bp-28h]@26
  CGameObjectVtbl *v32; // [sp+A8h] [bp-20h]@26
  float v33; // [sp+B0h] [bp-18h]@37
  float v34; // [sp+B4h] [bp-14h]@38
  float v35; // [sp+B8h] [bp-10h]@38
  CCharacter *v36; // [sp+D0h] [bp+8h]@1
  CCharacter *v37; // [sp+D8h] [bp+10h]@1
  bool v38; // [sp+E0h] [bp+18h]@1
  int v39; // [sp+E8h] [bp+20h]@1

  v39 = nLimitRadius;
  v38 = bBenefit;
  v37 = pOriDst;
  v36 = this;
  v7 = &v15;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  nSecNum = CMapData::GetSectorIndex(v36->m_pCurMap, pTar);
  if ( nSecNum == -1 )
    return 0i64;
  v17 = 0;
  CMapData::GetRectInRadius(v36->m_pCurMap, &pRect, 1, nSecNum);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v10 = CMapData::GetSecInfo(v36->m_pCurMap);
      dwSecIndex = v10->m_nSecNumW * j + k;
      v22 = CMapData::GetSectorListObj(v36->m_pCurMap, v36->m_wMapLayerIndex, dwSecIndex);
      if ( v22 )
      {
        v23 = (CObjectList *)v22->m_Head.m_pNext;
        while ( 1 )
        {
          while ( 1 )
          {
            while ( 1 )
            {
              if ( (_object_list_point *)v23 == &v22->m_Tail )
                goto LABEL_9;
              pDst = (CCharacter *)v23->vfptr;
              v23 = (CObjectList *)v23->m_Head.m_pItem;
              if ( v37 != pDst && !pDst->m_ObjID.m_byKind && CCharacter::IsEffectableDst(v36, psActableDst, pDst) )
              {
                if ( (signed int)v17 >= 29 )
                  return v17;
                if ( !pDst->m_bCorpse )
                  break;
              }
            }
            if ( v38 )
              break;
            v31 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
            v32 = v36->vfptr;
            v12 = ((int (__fastcall *)(CCharacter *))v32->GetObjRace)(v36);
            if ( v31 == v12 && !pDst->m_ObjID.m_byID )
            {
              v26 = (CPlayer *)pDst;
              if ( !CPlayer::IsPunished((CPlayer *)pDst, 1, 0) && !CPlayer::IsChaosMode(v26) )
                continue;
            }
            if ( ((unsigned __int8)((int (__fastcall *)(CCharacter *))v36->vfptr->IsAttackableInTown)(v36)
               || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst)
               || !(unsigned __int8)((int (__fastcall *)(CCharacter *))v36->vfptr->IsInTown)(v36)
               && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst))
              && (unsigned __int8)((int (__fastcall *)(CCharacter *, CCharacter *))pDst->vfptr->IsBeDamagedAble)(
                                    pDst,
                                    v36) )
            {
              goto LABEL_36;
            }
          }
          v29 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
          v30 = v36->vfptr;
          v11 = ((int (__fastcall *)(CCharacter *))v30->GetObjRace)(v36);
          if ( v29 == v11 )
          {
            if ( pDst->m_ObjID.m_byID
              || (v25 = (CPlayer *)pDst, !CPlayer::IsChaosMode((CPlayer *)pDst)) && !CPlayer::IsPunished(v25, 1, 0) )
            {
LABEL_36:
              v13 = v36->m_fCurPos[1] - pDst->m_fCurPos[1];
              abs(v13);
              v27 = v13;
              if ( v13 <= 350.0 )
              {
                GetSqrt(pTar, pDst->m_fCurPos);
                v33 = v13;
                ((void (__fastcall *)(CCharacter *))pDst->vfptr->GetWidth)(pDst);
                v14 = v33 - (float)(v13 / 2.0);
                if ( v14 <= 0.0 )
                {
                  v35 = 0.0;
                }
                else
                {
                  GetSqrt(pTar, pDst->m_fCurPos);
                  v34 = v14;
                  ((void (__fastcall *)(CCharacter *))pDst->vfptr->GetWidth)(pDst);
                  v35 = v34 - (float)(v14 / 2.0);
                }
                v28 = (signed int)ffloor(v35);
                if ( v28 <= v39 )
                  ppDsts[v17++] = pDst;
              }
            }
          }
        }
      }
LABEL_9:
      ;
    }
  }
  return v17;
}
