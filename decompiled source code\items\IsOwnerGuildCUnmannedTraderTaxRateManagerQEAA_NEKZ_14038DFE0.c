/*
 * Function: ?IsOwnerGuild@CUnmannedTraderTaxRateManager@@QEAA_NEK@Z
 * Address: 0x14038DFE0
 */

bool __fastcall CUnmannedTraderTaxRateManager::IsOwnerGuild(CUnmannedTraderTaxRateManager *this, char byRace, unsigned int dwGuildSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  TRC_AutoTrade **v6; // rax@7
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTaxRateManager *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1
  unsigned int nGuildSerial; // [sp+40h] [bp+18h]@1

  nGuildSerial = dwGuildSerial;
  v9 = byRace;
  v8 = this;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v8->m_vecTRC)
    || std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v8->m_vecTRC) <= (unsigned __int8)v9 )
  {
    result = 0;
  }
  else
  {
    v6 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v8->m_vecTRC, (unsigned __int8)v9);
    result = TRC_AutoTrade::IsOwnerGuild(*v6, nGuildSerial);
  }
  return result;
}
