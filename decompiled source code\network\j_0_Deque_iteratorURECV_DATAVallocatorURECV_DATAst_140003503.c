/*
 * Function: j_??0?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA@_KPEBV_Container_base@1@@Z
 * Address: 0x140003503
 */

void __fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, unsigned __int64 _Off, std::_Container_base *_Pdeque)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    this,
    _Off,
    _Pdeque);
}
