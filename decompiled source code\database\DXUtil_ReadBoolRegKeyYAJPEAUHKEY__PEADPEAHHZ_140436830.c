/*
 * Function: ?DXUtil_ReadBoolRegKey@@YAJPEAUHKEY__@@PEADPEAHH@Z
 * Address: 0x140436830
 */

__int64 __fastcall DXUtil_ReadBoolRegKey(HKEY__ *hKey, char *strRegName, int *pbValue, int bDefault)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-78h]@1
  unsigned int Type; // [sp+34h] [bp-44h]@4
  unsigned int cbData; // [sp+54h] [bp-24h]@4
  HKEY__ *hKeya; // [sp+80h] [bp+8h]@1
  int *v11; // [sp+90h] [bp+18h]@1
  int v12; // [sp+98h] [bp+20h]@1

  v12 = bDefault;
  v11 = pbValue;
  hKeya = hKey;
  v4 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  cbData = 4;
  if ( RegQueryValueExA(hKeya, strRegName, 0i64, &Type, (LPBYTE)pbValue, &cbData) )
    *v11 = v12;
  return 0i64;
}
