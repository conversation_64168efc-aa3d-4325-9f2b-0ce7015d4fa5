/*
 * Function: ?Init@CUnmannedTraderController@@QEAA_NXZ
 * Address: 0x14034CBE0
 */

bool __fastcall CUnmannedTraderController::Init(CUnmannedTraderController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CUnmannedTraderUserInfoTable *v4; // rax@6
  CUnmannedTraderGroupItemInfoTable *v5; // rax@8
  CUnmannedTraderScheduler *v6; // rax@10
  CUnmannedTraderTaxRateManager *v7; // rax@12
  __int64 v8; // [sp+0h] [bp-28h]@1
  CUnmannedTraderController *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v1 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CUnmannedTraderController::InitLogger(v9) )
  {
    v4 = CUnmannedTraderUserInfoTable::Instance();
    if ( CUnmannedTraderUserInfoTable::Init(v4) )
    {
      v5 = CUnmannedTraderGroupItemInfoTable::Instance();
      if ( CUnmannedTraderGroupItemInfoTable::Init(v5) )
      {
        v6 = CUnmannedTraderScheduler::Instance();
        if ( CUnmannedTraderScheduler::Init(v6) )
        {
          v7 = CUnmannedTraderTaxRateManager::Instance();
          if ( CUnmannedTraderTaxRateManager::Init(v7, v9->m_pkLogger) )
            result = CUnmannedTraderLazyCleaner::Init(&v9->m_kLazyCleaner) != 0;
          else
            result = 0;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
