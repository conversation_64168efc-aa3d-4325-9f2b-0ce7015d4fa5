/*
 * Function: ?PadLastBlock@?$IteratedHashBase@_KVHashTransformation@CryptoPP@@@CryptoPP@@IEAAXIE@Z
 * Address: 0x140570950
 */

__int64 __fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>::PadLastBlock(__int64 a1, unsigned int a2, char a3)
{
  unsigned int v3; // ST20_4@1
  __int64 v4; // rax@1
  __int64 result; // rax@2
  int v6; // [sp+20h] [bp-48h]@1
  void *v7; // [sp+28h] [bp-40h]@1
  unsigned int b; // [sp+38h] [bp-30h]@1
  unsigned __int64 v9; // [sp+40h] [bp-28h]@2
  unsigned __int64 v10; // [sp+48h] [bp-20h]@3
  __int64 v11; // [sp+70h] [bp+8h]@1
  unsigned int v12; // [sp+78h] [bp+10h]@1
  char v13; // [sp+80h] [bp+18h]@1

  v13 = a3;
  v12 = a2;
  v11 = a1;
  b = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  v3 = CryptoPP::ModPowerOf2<unsigned __int64,unsigned int>((const unsigned __int64 *)(v11 + 8), &b);
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v11 + 176i64))(v11);
  v7 = (void *)v4;
  *(_BYTE *)(v4 + v3) = v13;
  v6 = v3 + 1;
  if ( v6 > v12 )
  {
    v10 = b - v6;
    memset((void *)((unsigned int)v6 + v4), 0, v10);
    CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>::HashBlock(v11, v4);
    result = 0i64;
    memset(v7, 0, v12);
  }
  else
  {
    v9 = v12 - v6;
    result = 0i64;
    memset((char *)v7 + (unsigned int)v6, 0, v9);
  }
  return result;
}
