/*
 * Function: j_??1?$_Vector_const_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAA@XZ
 * Address: 0x140011C2F
 */

void __fastcall std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  std::_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_const_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(this);
}
