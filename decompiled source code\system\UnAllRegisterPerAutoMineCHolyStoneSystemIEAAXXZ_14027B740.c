/*
 * Function: ?UnAllRegisterPerAutoMine@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027B740
 */

void __fastcall CHolyStoneSystem::UnAllRegisterPerAutoMine(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v3; // ecx@10
  __int64 v4; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  CPlayer *v6; // [sp+28h] [bp-20h]@7
  AutominePersonal *v7; // [sp+30h] [bp-18h]@8
  CExtDummy *v8; // [sp+38h] [bp-10h]@10
  CHolyStoneSystem *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v6 = &g_Player + j;
    if ( v6->m_bLive )
    {
      v7 = v6->m_Param.m_pAPM;
      if ( v7 )
      {
        if ( AutominePersonal::is_installed(v7) )
        {
          v3 = v9->m_HolyKeeperData.ActiveDummy.m_wLineIndex;
          v8 = &v9->m_HolyKeeperData.pCreateMap->m_Dummy;
          if ( CExtDummy::IsInBBox(v8, v3, v7->m_fCurPos) )
            AutominePersonal::unregist_from_map(v7, 0);
        }
      }
    }
  }
}
