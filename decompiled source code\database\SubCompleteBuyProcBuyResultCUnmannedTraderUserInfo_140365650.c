/*
 * Function: ?SubCompleteBuyProcBuyResult@CUnmannedTraderUserInfoTable@@AEAA_NEPEAU__list@_qry_case_unmandtrader_buy_update_complete@@PEAE@Z
 * Address: 0x140365650
 */

char __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(CUnmannedTraderUserInfoTable *this, char byRet, _qry_case_unmandtrader_buy_update_complete::__list *pUpdateCompleteList, char *byCompleteUpdateNum)
{
  int *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  int v7; // [sp+0h] [bp-18h]@1

  v4 = &v7;
  for ( i = 4i64; i; --i )
  {
    *v4 = -858993460;
    ++v4;
  }
  v7 = (unsigned __int8)byRet - 34;
  switch ( byRet )
  {
    case 0x5A:
      pUpdateCompleteList->byUpdateState = 3;
      goto LABEL_10;
    case 0x51:
      pUpdateCompleteList->byUpdateState = 4;
      goto LABEL_10;
    case 0x25:
    case 0x63:
      pUpdateCompleteList->byUpdateState = 8;
      goto LABEL_10;
    case 0x22:
    case 0x2A:
    case 0x2B:
    case 0x96:
    case 0x97:
      pUpdateCompleteList->byUpdateState = 1;
LABEL_10:
      if ( pUpdateCompleteList->byProcUpdate == 255 )
        ++*byCompleteUpdateNum;
      pUpdateCompleteList->byProcUpdate = byRet;
      result = 1;
      break;
    default:
      result = 0;
      break;
  }
  return result;
}
