/*
 * Function: SQLGetAvailableDrivers
 * Address: 0x1404DA7DC
 */

int __fastcall SQLGetAvailableDrivers(const char *lpszInfFile, char *lpszBuf, unsigned __int16 cbBufMax, unsigned __int16 *pcbBufOut)
{
  const char *v4; // rbp@1
  unsigned __int16 *v5; // rbx@1
  unsigned __int16 v6; // di@1
  char *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszInfFile;
  v5 = pcbBufOut;
  v6 = cbBufMax;
  v7 = lpszBuf;
  v8 = ODBC___GetSetupProc("SQLGetAvailableDrivers");
  if ( v8 )
    result = ((int (__fastcall *)(const char *, char *, _QWORD, unsigned __int16 *))v8)(v4, v7, v6, v5);
  else
    result = 0;
  return result;
}
