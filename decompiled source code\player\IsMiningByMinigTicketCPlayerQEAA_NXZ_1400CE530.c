/*
 * Function: ?IsMiningByMinigTicket@CPlayer@@QEAA_NXZ
 * Address: 0x1400CE530
 */

bool __fastcall CPlayer::IsMiningByMinigTicket(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  unsigned __int16 v4; // ax@6
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+30h] [bp-18h]@6
  char v7; // [sp+31h] [bp-17h]@6
  char v8; // [sp+32h] [bp-16h]@6
  char v9; // [sp+33h] [bp-15h]@6
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CHolyStoneSystem::IsMinigeTicketCheck(&g_HolySys) )
  {
    v6 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
    v7 = CHolyStoneSystem::GetStartHour(&g_HolySys);
    v8 = CHolyStoneSystem::GetStartDay(&g_HolySys);
    v9 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
    v4 = CHolyStoneSystem::GetStartYear(&g_HolySys);
    result = MiningTicket::AuthLastCriTicket(&v10->m_MinigTicket, v4, v9, v8, v7, v6) != 0;
  }
  else
  {
    result = 1;
  }
  return result;
}
