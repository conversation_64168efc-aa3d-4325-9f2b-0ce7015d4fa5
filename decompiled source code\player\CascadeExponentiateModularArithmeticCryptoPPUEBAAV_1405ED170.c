/*
 * Function: ?CascadeExponentiate@ModularArithmetic@CryptoPP@@UEBA?AVInteger@2@AEBV32@000@Z
 * Address: 0x1405ED170
 */

struct CryptoPP::Integer *__fastcall CryptoPP::ModularArithmetic::CascadeExponentiate(CryptoPP::ModularArithmetic *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, const struct CryptoPP::Integer *a5, const struct CryptoPP::Integer *a6)
{
  struct CryptoPP::Integer *v6; // rax@2
  struct CryptoPP::Integer *v7; // rax@2
  struct CryptoPP::Integer *v8; // rax@2
  struct CryptoPP::Integer *result; // rax@2
  CryptoPP::MontgomeryRepresentation v10; // [sp+30h] [bp-198h]@2
  CryptoPP::Integer v11; // [sp+100h] [bp-C8h]@2
  CryptoPP::Integer v12; // [sp+128h] [bp-A0h]@2
  CryptoPP::Integer v13; // [sp+150h] [bp-78h]@2
  int v14; // [sp+178h] [bp-50h]@1
  __int64 v15; // [sp+180h] [bp-48h]@1
  struct CryptoPP::Integer *v16; // [sp+188h] [bp-40h]@2
  struct CryptoPP::Integer *v17; // [sp+190h] [bp-38h]@2
  struct CryptoPP::Integer *v18; // [sp+198h] [bp-30h]@2
  struct CryptoPP::Integer *v19; // [sp+1A0h] [bp-28h]@2
  struct CryptoPP::Integer *v20; // [sp+1A8h] [bp-20h]@2
  struct CryptoPP::Integer *v21; // [sp+1B0h] [bp-18h]@2
  CryptoPP::ModularArithmetic *v22; // [sp+1D0h] [bp+8h]@1
  struct CryptoPP::Integer *v23; // [sp+1D8h] [bp+10h]@1
  struct CryptoPP::Integer *v24; // [sp+1E0h] [bp+18h]@1
  struct CryptoPP::Integer *v25; // [sp+1E8h] [bp+20h]@1

  v25 = (struct CryptoPP::Integer *)a4;
  v24 = (struct CryptoPP::Integer *)a3;
  v23 = retstr;
  v22 = this;
  v15 = -2i64;
  v14 = 0;
  if ( CryptoPP::Integer::IsOdd(&this->m_modulus) )
  {
    CryptoPP::MontgomeryRepresentation::MontgomeryRepresentation(&v10, &v22->m_modulus);
    v6 = CryptoPP::MontgomeryRepresentation::ConvertIn(&v10, &v11, a5);
    v16 = v6;
    v17 = v6;
    v7 = CryptoPP::MontgomeryRepresentation::ConvertIn(&v10, &v12, v24);
    v18 = v7;
    v19 = v7;
    v8 = CryptoPP::MontgomeryRepresentation::CascadeExponentiate(&v10, &v13, v7, v25, v17, a6);
    v20 = v8;
    v21 = v8;
    CryptoPP::MontgomeryRepresentation::ConvertOut(&v10, v23, v8);
    v14 |= 1u;
    CryptoPP::Integer::~Integer(&v13);
    CryptoPP::Integer::~Integer(&v12);
    CryptoPP::Integer::~Integer(&v11);
    CryptoPP::MontgomeryRepresentation::~MontgomeryRepresentation(&v10);
    result = v23;
  }
  else
  {
    CryptoPP::AbstractRing<CryptoPP::Integer>::CascadeExponentiate(
      (__int64)v22,
      v23,
      (__int64)v24,
      v25,
      (__int64)a5,
      (CryptoPP::Integer *)a6);
    result = v23;
  }
  return result;
}
