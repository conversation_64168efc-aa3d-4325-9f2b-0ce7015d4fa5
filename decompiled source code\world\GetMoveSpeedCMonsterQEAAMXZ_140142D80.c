/*
 * Function: ?GetMoveSpeed@CMonster@@QEAAMXZ
 * Address: 0x140142D80
 */

float __fastcall CMonster::GetMoveSpeed(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  CMonster *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( _effect_parameter::GetEff_State(&v5->m_EP, 7) )
  {
    result = v5->m_pMonRec->m_fMovSpd;
  }
  else if ( CMonster::GetMoveType(v5) )
  {
    result = v5->m_pMonRec->m_fWarMovSpd;
  }
  else
  {
    result = v5->m_pMonRec->m_fMovSpd;
  }
  return result;
}
