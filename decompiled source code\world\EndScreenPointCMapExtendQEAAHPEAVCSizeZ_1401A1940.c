/*
 * Function: ?EndScreenPoint@CMapExtend@@QEAAHPEAVCSize@@@Z
 * Address: 0x1401A1940
 */

__int64 __fastcall CMapExtend::EndScreenPoint(CMapExtend *this, CSize *szMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@5
  __int64 v5; // [sp+0h] [bp-98h]@1
  CPoint *v6; // [sp+20h] [bp-78h]@8
  tagPOINT v7; // [sp+38h] [bp-60h]@8
  CPoint v8; // [sp+58h] [bp-40h]@8
  tagSIZE initSize; // [sp+64h] [bp-34h]@8
  tagPOINT point; // [sp+6Ch] [bp-2Ch]@8
  CSize result; // [sp+74h] [bp-24h]@8
  tagPOINT bottomRight; // [sp+7Ch] [bp-1Ch]@28
  tagPOINT topLeft; // [sp+84h] [bp-14h]@28
  CMapExtend *v14; // [sp+A0h] [bp+8h]@1
  CSize *szMapa; // [sp+A8h] [bp+10h]@1

  szMapa = szMap;
  v14 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !v14->m_bSetArea )
    return v14->m_bExtendMode;
  if ( !v14->m_bMove )
  {
    v14->m_bSetArea = 0;
    return v14->m_bExtendMode;
  }
  v6 = &v14->m_ptMoveScreen;
  v7 = (tagPOINT)v14->m_ptStartScreen;
  point = v7;
  initSize = CPoint::operator-(&v14->m_ptMoveScreen, &result, v7)->0;
  CPoint::CPoint(&v8, initSize);
  if ( v8.x >= 0 || v8.y >= 0 )
  {
    if ( v8.x <= 0 || v8.y >= 0 )
    {
      if ( v8.x >= 0 || v8.y <= 0 )
      {
        if ( v8.x <= 0 || v8.y <= 0 )
        {
          v14->m_bSetArea = 0;
          return v14->m_bExtendMode;
        }
        v14->m_ptEndScreen.x = v6->x;
        v14->m_ptEndScreen.y = v6->y;
      }
      else
      {
        v14->m_ptStartScreen.x = v6->x;
        v14->m_ptEndScreen.x = v7.x;
        v14->m_ptEndScreen.y = v6->y;
      }
    }
    else
    {
      v14->m_ptStartScreen.y = v6->y;
      v14->m_ptEndScreen.x = v6->x;
      v14->m_ptEndScreen.y = v7.y;
    }
  }
  else
  {
    v14->m_ptStartScreen.x = v6->x;
    v14->m_ptStartScreen.y = v6->y;
    v14->m_ptEndScreen.x = v7.x;
    v14->m_ptEndScreen.y = v7.y;
  }
  if ( v8.x < 0 )
    v8.x = -v8.x;
  if ( v8.y < 0 )
    v8.y = -v8.y;
  if ( v8.x >= 10 || v8.y >= 10 )
  {
    CMapExtend::ConvertToMap(v14, szMapa);
    v14->m_bSetArea = 0;
    v14->m_bExtendMode = 1;
    bottomRight = (tagPOINT)v14->m_ptEndMap;
    topLeft = (tagPOINT)v14->m_ptStartMap;
    CRect::SetRect(&v14->m_rcExtend, topLeft, bottomRight);
    v4 = v14->m_bExtendMode;
  }
  else
  {
    v14->m_bSetArea = 0;
    v4 = v14->m_bExtendMode;
  }
  return v4;
}
