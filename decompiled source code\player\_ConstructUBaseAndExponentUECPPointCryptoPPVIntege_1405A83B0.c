/*
 * Function: ??$_Construct@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@U12@@std@@YAXPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@AEBU12@@Z
 * Address: 0x1405A83B0
 */

CryptoPP::ECPPoint *__fastcall std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(void *a1, CryptoPP::ECPPoint *a2)
{
  CryptoPP::ECPPoint *v3; // [sp+30h] [bp-28h]@1
  CryptoPP::ECPPoint *v4; // [sp+40h] [bp-18h]@2
  CryptoPP::ECPPoint *v5; // [sp+68h] [bp+10h]@1

  v5 = a2;
  v3 = (CryptoPP::ECPPoint *)operator new(0x80ui64, a1);
  if ( v3 )
    v4 = CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(
           v3,
           v5);
  else
    v4 = 0i64;
  return v4;
}
