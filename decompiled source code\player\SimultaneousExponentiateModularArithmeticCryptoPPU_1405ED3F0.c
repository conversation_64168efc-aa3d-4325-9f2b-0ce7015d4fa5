/*
 * Function: ?SimultaneousExponentiate@ModularArithmetic@CryptoPP@@UEBAXPEAVInteger@2@AEBV32@PEBV32@I@Z
 * Address: 0x1405ED3F0
 */

void __fastcall CryptoPP::ModularArithmetic::SimultaneousExponentiate(CryptoPP::ModularArithmetic *this, struct CryptoPP::Integer *a2, const struct CryptoPP::Integer *a3, const struct CryptoPP::Integer *a4, unsigned int a5)
{
  CryptoPP::MontgomeryRepresentation v5; // [sp+30h] [bp-158h]@2
  unsigned int i; // [sp+100h] [bp-88h]@2
  CryptoPP::Integer v7; // [sp+108h] [bp-80h]@2
  CryptoPP::Integer v8; // [sp+130h] [bp-58h]@4
  __int64 v9; // [sp+158h] [bp-30h]@1
  struct CryptoPP::Integer *v10; // [sp+160h] [bp-28h]@2
  struct CryptoPP::Integer *v11; // [sp+168h] [bp-20h]@2
  CryptoPP::Integer *v12; // [sp+170h] [bp-18h]@4
  CryptoPP::Integer *v13; // [sp+178h] [bp-10h]@4
  CryptoPP::ModularArithmetic *v14; // [sp+190h] [bp+8h]@1
  struct CryptoPP::Integer *v15; // [sp+198h] [bp+10h]@1
  struct CryptoPP::Integer *v16; // [sp+1A0h] [bp+18h]@1
  struct CryptoPP::Integer *v17; // [sp+1A8h] [bp+20h]@1

  v17 = (struct CryptoPP::Integer *)a4;
  v16 = (struct CryptoPP::Integer *)a3;
  v15 = a2;
  v14 = this;
  v9 = -2i64;
  if ( CryptoPP::Integer::IsOdd(&this->m_modulus) )
  {
    CryptoPP::MontgomeryRepresentation::MontgomeryRepresentation(&v5, &v14->m_modulus);
    v10 = CryptoPP::MontgomeryRepresentation::ConvertIn(&v5, &v7, v16);
    v11 = v10;
    CryptoPP::MontgomeryRepresentation::SimultaneousExponentiate(&v5, v15, v10, v17, a5);
    CryptoPP::Integer::~Integer(&v7);
    for ( i = 0; i < a5; ++i )
    {
      v12 = CryptoPP::MontgomeryRepresentation::ConvertOut(&v5, &v8, &v15[i]);
      v13 = v12;
      CryptoPP::Integer::operator=(&v15[i], v12);
      CryptoPP::Integer::~Integer(&v8);
    }
    CryptoPP::MontgomeryRepresentation::~MontgomeryRepresentation(&v5);
  }
  else
  {
    CryptoPP::AbstractRing<CryptoPP::Integer>::SimultaneousExponentiate((__int64)v14, (__int64)v15, v16, v17, a5);
  }
}
