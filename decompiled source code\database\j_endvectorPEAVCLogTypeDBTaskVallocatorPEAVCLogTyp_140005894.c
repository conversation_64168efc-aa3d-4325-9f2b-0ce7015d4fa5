/*
 * Function: j_?end@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@2@XZ
 * Address: 0x140005894
 */

std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::end(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *result)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::end(this, result);
}
