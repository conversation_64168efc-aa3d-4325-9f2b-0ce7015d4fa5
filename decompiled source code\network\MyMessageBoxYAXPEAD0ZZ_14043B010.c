/*
 * Function: ?MyMessageBox@@YAXPEAD0ZZ
 * Address: 0x14043B010
 */

void MyMessageBox(char *szTitle, char *szMessage, ...)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@7
  DWORD v5; // eax@9
  __int64 v6; // [sp+0h] [bp-AD8h]@1
  va_list Args; // [sp+48h] [bp-A90h]@4
  char Dest; // [sp+70h] [bp-A68h]@4
  char Str; // [sp+590h] [bp-548h]@8
  HANDLE hFile; // [sp+A98h] [bp-40h]@8
  unsigned int NumberOfBytesWritten; // [sp+AA4h] [bp-34h]@9
  unsigned __int64 v12; // [sp+AC0h] [bp-18h]@4
  char *lpCaption; // [sp+AE0h] [bp+8h]@1
  va_list va; // [sp+AF0h] [bp+18h]@1

  va_start(va, szMessage);
  lpCaption = szTitle;
  v2 = &v6;
  for ( i = 692i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  Args = (va_list)va;
  vsprintf(&Dest, szMessage, (va_list)va);
  Args = 0i64;
  if ( CMainThread::IsExcuteService(&g_Main) )
  {
    if ( !g_szMessageFile )
    {
      v4 = GetKorLocalTime();
      sprintf(&g_szMessageFile, "..\\ZoneServerLog\\Systemlog\\MessageBox%d.log", v4);
    }
    sprintf(&Str, "%s - %s\n", lpCaption, &Dest);
    hFile = CreateFileA(&g_szMessageFile, 0x40000000u, 1u, 0i64, 4u, 0x80u, 0i64);
    if ( hFile != (HANDLE)-1 )
    {
      SetFilePointer(hFile, 0, 0i64, 2u);
      v5 = strlen_0(&Str);
      WriteFile(hFile, &Str, v5, &NumberOfBytesWritten, 0i64);
      CloseHandle(hFile);
    }
  }
  else
  {
    MessageBoxA(0i64, &Dest, lpCaption, 0);
  }
}
