/*
 * Function: ??1?$CHashMapPtrPool@HVCNationSettingFactory@@@@QEAA@XZ
 * Address: 0x140229A50
 */

void __fastcall CHashMapPtrPool<int,CNationSettingFactory>::~CHashMapPtrPool<int,CNationSettingFactory>(CHashMapPtrPool<int,CNationSettingFactory> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CHashMapPtrPool<int,CNationSettingFactory> *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  if ( v5->m_bCleanUp )
    CHashMapPtrPool<int,CNationSettingFactory>::cleanup(v5);
  stdext::hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::~hash_map<int,CNationSettingFactory *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(&v5->m_mapData);
}
