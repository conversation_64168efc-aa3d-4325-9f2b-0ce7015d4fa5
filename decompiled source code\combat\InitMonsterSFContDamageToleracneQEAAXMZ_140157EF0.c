/*
 * Function: ?Init@MonsterSFContDamageToleracne@@QEAAXM@Z
 * Address: 0x140157EF0
 */

void __fastcall MonsterSFContDamageToleracne::Init(MonsterSFContDamageToleracne *this, float fMaxTolValue)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  float v5; // [sp+20h] [bp-18h]@6
  MonsterSFContDamageToleracne *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_dwLastUpdateTime = GetLoopTime();
  if ( fMaxTolValue > 1.0 || fMaxTolValue < 0.0 )
    v5 = FLOAT_1_0;
  else
    v5 = fMaxTolValue;
  v6->m_fToleranceProbMax = v5;
  v6->m_fToleranceProb = v5;
}
