/*
 * Function: ?GetResBufferNum@CPlayerDB@@QEAAEXZ
 * Address: 0x14010BDD0
 */

char __fastcall CPlayerDB::GetResBufferNum(CPlayerDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  CPlayerDB *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  for ( j = 0; j < GetMaxResKind(); ++j )
  {
    if ( (signed int)v7->m_wCuttingResBuffer[j] > 0 )
      ++v5;
  }
  return v5;
}
