/*
 * Function: ?Emb_AlterDurPoint@CPlayer@@QEAAKEEH_N0@Z
 * Address: 0x140058A80
 */

__int64 __fastcall CPlayer::Emb_AlterDurPoint(CPlayer *this, char byStorageCode, char byStorageIndex, int nAlter, bool bUpdate, bool bSend)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  LendItemMng *v9; // rax@14
  char *v10; // rax@16
  TimeLimitJadeMng *v11; // rax@25
  char *v12; // rax@26
  CPlayer::CashChangeStateFlag *v13; // rax@42
  __int64 v14; // [sp+0h] [bp-98h]@1
  _DWORD v15[2]; // [sp+20h] [bp-78h]@16
  int v16; // [sp+28h] [bp-70h]@26
  int v17; // [sp+30h] [bp-68h]@26
  _STORAGE_LIST::_db_con *pkItem; // [sp+40h] [bp-58h]@4
  unsigned __int64 v19; // [sp+48h] [bp-50h]@11
  unsigned __int64 pdwLeftDur; // [sp+58h] [bp-40h]@11
  unsigned __int16 v21; // [sp+64h] [bp-34h]@11
  _ResourceItem_fld *pFld; // [sp+68h] [bp-30h]@19
  CPlayer::CashChangeStateFlag v23; // [sp+70h] [bp-28h]@42
  int v24; // [sp+74h] [bp-24h]@16
  unsigned int v25; // [sp+78h] [bp-20h]@16
  int v26; // [sp+7Ch] [bp-1Ch]@26
  int v27; // [sp+80h] [bp-18h]@26
  int v28; // [sp+84h] [bp-14h]@26
  char *v29; // [sp+88h] [bp-10h]@26
  CPlayer *v30; // [sp+A0h] [bp+8h]@1
  char v31; // [sp+A8h] [bp+10h]@1
  char v32; // [sp+B0h] [bp+18h]@1
  int nAltera; // [sp+B8h] [bp+20h]@1

  nAltera = nAlter;
  v32 = byStorageIndex;
  v31 = byStorageCode;
  v30 = this;
  v6 = &v14;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  pkItem = &v30->m_Param.m_pStoragePtr[(unsigned __int8)byStorageCode]->m_pStorageList[(unsigned __int8)byStorageIndex];
  if ( pkItem->m_bLoad )
  {
    if ( pkItem->m_byTableCode != 15
      || nAlter <= 0
      || pkItem->m_dwDur + nAlter <= 0xFFFFFF
      || (nAltera = 0xFFFFFF - LODWORD(pkItem->m_dwDur), 0xFFFFFF - LODWORD(pkItem->m_dwDur) > 0) )
    {
      v19 = pkItem->m_dwDur;
      pdwLeftDur = 0i64;
      v21 = pkItem->m_wSerial;
      if ( nAltera < 0 && !(pkItem->m_dwDur + nAltera) && pkItem->m_byCsMethod )
      {
        v9 = LendItemMng::Instance();
        LendItemMng::DeleteLink(v9, v30->m_ObjID.m_wIndex, v31, pkItem);
      }
      if ( _STORAGE_LIST::AlterCurDur(
             v30->m_Param.m_pStoragePtr[(unsigned __int8)v31],
             (unsigned __int8)v32,
             nAltera,
             &pdwLeftDur) )
      {
        if ( v31 )
          goto LABEL_58;
        if ( pkItem->m_byTableCode != 18 )
          goto LABEL_58;
        pFld = (_ResourceItem_fld *)CRecordData::GetRecord(
                                      (CRecordData *)&unk_1799C6AA0 + pkItem->m_byTableCode,
                                      pkItem->m_wItemIndex);
        if ( pFld->m_nEffectDataNum <= 0 )
          goto LABEL_58;
        CPlayer::SetHaveEffect(v30, 0);
        if ( !pFld->m_nEffType1 )
        {
          if ( v19 <= pdwLeftDur )
            CPlayer::SetMstHaveEffect(v30, pFld, pkItem, 1, nAltera);
          else
            CPlayer::SetMstHaveEffect(v30, pFld, pkItem, 0, nAltera);
        }
        if ( pFld->m_nStartTime == -1
          || (v11 = TimeLimitJadeMng::Instance(), TimeLimitJadeMng::DeleteList(v11, v30->m_ObjID.m_wIndex, pkItem)) )
        {
LABEL_58:
          if ( pdwLeftDur )
          {
            if ( v19 != pdwLeftDur )
            {
              if ( bSend )
                CPlayer::SendMsg_AlterItemDurInform(v30, v31, v21, pdwLeftDur);
              if ( v30->m_pUserDB )
              {
                LOBYTE(v15[0]) = bUpdate;
                CUserDB::Update_ItemDur(v30->m_pUserDB, v31, v32, pdwLeftDur, bUpdate);
              }
            }
          }
          else
          {
            if ( v30->m_pUserDB )
              CUserDB::Update_ItemDelete(v30->m_pUserDB, v31, v32, bUpdate);
            if ( v31 == 1 )
            {
              if ( CPlayer::GetEffectEquipCode(v30, v31, v32) == 1 )
                CPlayer::SetEquipEffect(v30, (_STORAGE_LIST::_storage_con *)&pkItem->m_bLoad, 0);
              CPlayer::SetEffectEquipCode(v30, v31, v32, 0);
            }
            if ( (v31 == 1 || v31 == 2)
              && (pkItem->m_byTableCode < 5
               || pkItem->m_byTableCode == 5
               || pkItem->m_byTableCode == 8
               || pkItem->m_byTableCode == 9) )
            {
              CPlayer::CalcDefTol(v30);
            }
            if ( v31 == 1 )
            {
              CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v23, 0);
              CPlayer::UpdateVisualVer(v30, (CPlayer::CashChangeStateFlag)v13->0);
              CPlayer::SendMsg_EquipPartChange(v30, pkItem->m_byTableCode);
              if ( CPlayer::IsSiegeMode(v30) )
                CPlayer::SetSiege(v30, 0i64);
              CPlayer::CalcEquipSpeed(v30);
              CPlayer::CalcEquipMaxDP(v30, 0);
            }
            else if ( !v31 && CPlayer::IsSiegeMode(v30) && pkItem == v30->m_pSiegeItem )
            {
              CPlayer::SetSiege(v30, 0i64);
            }
            CPlayer::SendMsg_DeleteStorageInform(v30, v31, v21);
          }
          result = (unsigned int)pdwLeftDur;
        }
        else
        {
          v26 = pkItem->m_wItemIndex;
          v27 = pkItem->m_byTableCode;
          v28 = 0;
          v29 = pFld->m_strCode;
          v12 = CPlayerDB::GetCharNameA(&v30->m_Param);
          v17 = v26;
          v16 = v27;
          v15[0] = v28;
          CLogFile::Write(
            &stru_1799C8E78,
            "%s: Emb_AlterDurPoint.. TimeLimitJadeMng::DeleteList() error storage: %d, item[%s]: %d-%d: ",
            v12,
            v29);
          result = LODWORD(pkItem->m_dwDur);
        }
      }
      else
      {
        v24 = (unsigned __int8)v32;
        v25 = (unsigned __int8)v31;
        v10 = CPlayerDB::GetCharNameA(&v30->m_Param);
        v15[0] = v24;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s: Emb_AlterDurPoint.. AlterCurDur() error storage: %d, slot: %d: ",
          v10,
          v25);
        result = LODWORD(pkItem->m_dwDur);
      }
    }
    else
    {
      result = LODWORD(pkItem->m_dwDur);
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
