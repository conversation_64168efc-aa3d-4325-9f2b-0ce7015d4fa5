/*
 * Function: ?UpdateGuildBattleWinLoseRankInfo@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F4290
 */

void __fastcall CMainThread::UpdateGuildBattleWinLoseRankInfo(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@5
  int v5; // eax@6
  int v6; // eax@7
  int v7; // eax@8
  int v8; // eax@8
  __int64 v9; // [sp+0h] [bp-98h]@1
  char *pQryData; // [sp+20h] [bp-78h]@5
  int nSize; // [sp+28h] [bp-70h]@5
  char *v12; // [sp+30h] [bp-68h]@4
  _qry_case_loadguildbattlerank v13; // [sp+44h] [bp-54h]@6
  _qry_case_load_guildbattle_totalrecord v14; // [sp+68h] [bp-30h]@8
  int v15; // [sp+84h] [bp-14h]@5
  unsigned int v16; // [sp+88h] [bp-10h]@5

  v2 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = pData->m_sData;
  if ( pData->m_byResult )
  {
    v15 = (unsigned __int8)v12[8];
    v16 = (unsigned __int8)*v12;
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    nSize = *((_DWORD *)v12 + 3);
    LODWORD(pQryData) = v15;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v4,
      "void CMainThread::UpdateGuildBattleDrawRankInfo(_DB_QRY_SYN_DATA* pData) : Win R(%u) Serial(%u) Lose R(%u) Serial(%u) Fail!",
      v16,
      *((_DWORD *)v12 + 1));
  }
  else
  {
    v13.byRace = *v12;
    v5 = _qry_case_loadguildbattlerank::size(&v13);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 33, &v13.byRace, v5);
    if ( (unsigned __int8)*v12 != (unsigned __int8)v12[8] )
    {
      v13.byRace = v12[8];
      v6 = _qry_case_loadguildbattlerank::size(&v13);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 33, &v13.byRace, v6);
    }
    v14.dwGuildSerial = *((_DWORD *)v12 + 1);
    v7 = _qry_case_load_guildbattle_totalrecord::size(&v14);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 57, (char *)&v14, v7);
    v14.dwGuildSerial = *((_DWORD *)v12 + 3);
    v8 = _qry_case_load_guildbattle_totalrecord::size(&v14);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 57, (char *)&v14, v8);
  }
}
