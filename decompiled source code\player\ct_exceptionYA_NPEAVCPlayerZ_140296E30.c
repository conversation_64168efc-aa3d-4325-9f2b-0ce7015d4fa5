/*
 * Function: ?ct_exception@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296E30
 */

char __fastcall ct_exception(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  char _Dest[2]; // [sp+24h] [bp-24h]@7
  CPlayer *v6; // [sp+50h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    _Dest[0] = 0;
    memset(&_Dest[1], 0, sizeof(_Dest[1]));
    sprintf_s<2>((char (*)[2])_Dest, "%s", "Test");
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
