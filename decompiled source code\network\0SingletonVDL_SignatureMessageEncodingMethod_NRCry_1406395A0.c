/*
 * Function: ??0?$Singleton@VDL_SignatureMessageEncodingMethod_NR@CryptoPP@@U?$NewObject@VDL_SignatureMessageEncodingMethod_NR@CryptoPP@@@2@$0A@@CryptoPP@@QEAA@U?$NewObject@VDL_SignatureMessageEncodingMethod_NR@CryptoPP@@@1@@Z
 * Address: 0x1406395A0
 */

__int64 __fastcall CryptoPP::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>,0>::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_NR,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_NR>,0>(__int64 a1)
{
  return a1;
}
