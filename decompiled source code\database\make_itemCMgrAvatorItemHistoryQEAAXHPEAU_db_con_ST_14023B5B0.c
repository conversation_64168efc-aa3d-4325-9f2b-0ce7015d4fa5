/*
 * Function: ?make_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAEEE_N0PEAD@Z
 * Address: 0x14023B5B0
 */

void __fastcall CMgrAvatorItemHistory::make_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pMaterial, char *pbyMtrNum, char byMaterialNum, char byRetCode, bool bInsert, _STORAGE_LIST::_db_con *pMakeItem, char *pszFileName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@6
  char *v12; // rax@7
  unsigned int v13; // ecx@12
  __int64 v14; // [sp+0h] [bp-98h]@1
  unsigned __int64 v15; // [sp+20h] [bp-78h]@6
  unsigned __int64 v16; // [sp+28h] [bp-70h]@6
  char *v17; // [sp+30h] [bp-68h]@6
  char *v18; // [sp+38h] [bp-60h]@6
  _base_fld *v19; // [sp+40h] [bp-58h]@5
  int j; // [sp+48h] [bp-50h]@10
  _base_fld *v21; // [sp+50h] [bp-48h]@12
  char *v22; // [sp+58h] [bp-40h]@6
  char *v23; // [sp+60h] [bp-38h]@6
  int nTableCode; // [sp+68h] [bp-30h]@6
  char *v25; // [sp+70h] [bp-28h]@7
  char *v26; // [sp+78h] [bp-20h]@7
  int v27; // [sp+80h] [bp-18h]@7
  CMgrAvatorItemHistory *v28; // [sp+A0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v29; // [sp+B0h] [bp+18h]@1
  char *v30; // [sp+B8h] [bp+20h]@1

  v30 = pbyMtrNum;
  v29 = pMaterial;
  v28 = this;
  v9 = &v14;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  sData[0] = 0;
  if ( byRetCode )
  {
    sprintf(sBuf, "MAKE(FAIL): [%s %s]\r\n", v28->m_szCurDate, v28->m_szCurTime);
    strcat_0(sData, sBuf);
  }
  else
  {
    v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pMakeItem->m_byTableCode, pMakeItem->m_wItemIndex);
    if ( bInsert )
    {
      v22 = v28->m_szCurTime;
      v23 = v28->m_szCurDate;
      nTableCode = pMakeItem->m_byTableCode;
      v11 = DisplayItemUpgInfo(nTableCode, pMakeItem->m_dwLv);
      v18 = v22;
      v17 = v23;
      v16 = pMakeItem->m_lnUID;
      v15 = (unsigned __int64)v11;
      sprintf(sBuf, "MAKE(SUCC): %s_%u_@%s[%I64u] [%s %s]\r\n", v19->m_strCode, pMakeItem->m_dwDur);
    }
    else
    {
      v25 = v28->m_szCurTime;
      v26 = v28->m_szCurDate;
      v27 = pMakeItem->m_byTableCode;
      v12 = DisplayItemUpgInfo(v27, pMakeItem->m_dwLv);
      v18 = v25;
      v17 = v26;
      v16 = pMakeItem->m_lnUID;
      v15 = (unsigned __int64)v12;
      sprintf(sBuf, "MAKE(QUEST): %s_%u_@%s[%I64u] [%s %s]\r\n", v19->m_strCode, pMakeItem->m_dwDur);
    }
    strcat_0(sData, sBuf);
  }
  for ( j = 0; j < (unsigned __int8)byMaterialNum; ++j )
  {
    v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v29[j].m_byTableCode, v29[j].m_wItemIndex);
    v13 = (unsigned __int8)v30[j];
    v15 = v29[j].m_lnUID;
    sprintf(sBuf, "\t- %s_%d[%I64]\r\n", v21->m_strCode, v13);
    strcat_0(sData, sBuf);
  }
  CMgrAvatorItemHistory::WriteFile(v28, pszFileName, sData);
}
