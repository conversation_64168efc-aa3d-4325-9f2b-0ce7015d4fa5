/*
 * Function: j_?size@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140003972
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(this);
}
