/*
 * Function: ?pc_BuyItemStore@CPlayer@@QEAAXPEAVCItemStore@@EPEAU_list@_buy_store_request_clzo@@H@Z
 * Address: 0x1400F1140
 */

void __usercall CPlayer::pc_BuyItemStore(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, char by<PERSON><PERSON><PERSON><PERSON>@<r8b>, _buy_store_request_clzo::_list *pList@<r9>, signed __int64 a5@<rax>, float a6@<xmm0>, int bUseNPCLinkIntem)
{
  void *v7; // rsp@1
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int v10; // eax@9
  int v11; // eax@20
  long double v12; // xmm1_8@26
  unsigned int v13; // eax@26
  CPvpUserAndGuildRankingSystem *v14; // rax@32
  unsigned int v15; // eax@32
  int v16; // eax@36
  CGoldenBoxItemMgr *v17; // rax@46
  int v18; // eax@95
  int v19; // eax@95
  int v20; // eax@95
  int v21; // eax@97
  int v22; // eax@101
  int v23; // eax@101
  int v24; // eax@101
  int v25; // eax@103
  unsigned int v26; // eax@108
  int v27; // eax@112
  int v28; // eax@114
  CMoneySupplyMgr *v29; // rax@120
  CMoneySupplyMgr *v30; // rax@122
  int v31; // eax@123
  __int64 v32; // [sp-20h] [bp-1888h]@1
  unsigned int dwHasGold[2]; // [sp+0h] [bp-1868h]@100
  char byGrade; // [sp+30h] [bp-1838h]@26
  int j; // [sp+40h] [bp-1828h]@15
  char __t; // [sp+60h] [bp-1808h]@4
  char v37; // [sp+61h] [bp-1807h]@25
  char v38; // [sp+62h] [bp-1806h]@25
  _STORAGE_LIST::_db_con pFixingItem; // [sp+63h] [bp-1805h]@29
  __int16 v40[2785]; // [sp+96h] [bp-17D2h]@100
  char v41; // [sp+1658h] [bp-210h]@4
  char v42; // [sp+1659h] [bp-20Fh]@4
  CTransportShip *v43; // [sp+1668h] [bp-200h]@4
  char v44; // [sp+1670h] [bp-1F8h]@4
  float v45; // [sp+1674h] [bp-1F4h]@26
  char v46; // [sp+1684h] [bp-1E4h]@26
  char v47; // [sp+1694h] [bp-1D4h]@26
  _base_fld *v48; // [sp+1698h] [bp-1D0h]@30
  char *v49; // [sp+16A0h] [bp-1C8h]@36
  _base_fld *v50; // [sp+16A8h] [bp-1C0h]@37
  _base_fld *v51; // [sp+16B0h] [bp-1B8h]@42
  char *v52; // [sp+16B8h] [bp-1B0h]@51
  int v53; // [sp+16C0h] [bp-1A8h]@60
  int k; // [sp+16C4h] [bp-1A4h]@60
  int l; // [sp+16C8h] [bp-1A0h]@71
  char *v56; // [sp+16D0h] [bp-198h]@74
  int m; // [sp+16D8h] [bp-190h]@79
  char *v58; // [sp+16E0h] [bp-188h]@82
  char v59; // [sp+16E8h] [bp-180h]@88
  char *v60; // [sp+16F0h] [bp-178h]@90
  int n; // [sp+16F8h] [bp-170h]@95
  int ii; // [sp+16FCh] [bp-16Ch]@101
  _base_fld *v63; // [sp+1700h] [bp-168h]@107
  int v64; // [sp+1708h] [bp-160h]@115
  _limit_amount_info pAmountInfo; // [sp+1720h] [bp-148h]@126
  int v66; // [sp+17C0h] [bp-A8h]@9
  float v67; // [sp+17C4h] [bp-A4h]@26
  int v68; // [sp+17C8h] [bp-A0h]@26
  unsigned int *v69; // [sp+17D0h] [bp-98h]@26
  long double v70; // [sp+17D8h] [bp-90h]@26
  unsigned int v71; // [sp+17E0h] [bp-88h]@26
  int v72; // [sp+17E4h] [bp-84h]@32
  unsigned int v73; // [sp+17E8h] [bp-80h]@32
  _STORAGE_LIST::_db_con *pItem; // [sp+17F0h] [bp-78h]@108
  char *szCharName; // [sp+17F8h] [bp-70h]@108
  int nAdd; // [sp+1800h] [bp-68h]@112
  int v77; // [sp+1804h] [bp-64h]@114
  unsigned int nAmount; // [sp+1808h] [bp-60h]@120
  char *szClass; // [sp+1810h] [bp-58h]@120
  int nLv; // [sp+1818h] [bp-50h]@120
  int v81; // [sp+181Ch] [bp-4Ch]@120
  unsigned int v82; // [sp+1820h] [bp-48h]@122
  char *v83; // [sp+1828h] [bp-40h]@122
  int v84; // [sp+1830h] [bp-38h]@122
  int v85; // [sp+1834h] [bp-34h]@122
  char *v86; // [sp+1838h] [bp-30h]@123
  unsigned int dwNewGold; // [sp+1840h] [bp-28h]@123
  unsigned int dwNewDalant; // [sp+1844h] [bp-24h]@123
  unsigned int dwCostGold; // [sp+1848h] [bp-20h]@123
  unsigned __int64 v90; // [sp+1850h] [bp-18h]@4
  CPlayer *pOne; // [sp+1870h] [bp+8h]@1
  CItemStore *pStorea; // [sp+1878h] [bp+10h]@1
  char v93; // [sp+1880h] [bp+18h]@1
  _buy_store_request_clzo::_list *v94; // [sp+1888h] [bp+20h]@1

  v94 = pList;
  v93 = byOfferNum;
  pStorea = pStore;
  pOne = this;
  v7 = alloca(a5);
  v8 = &v32;
  for ( i = 1568i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v90 = (unsigned __int64)&v32 ^ _security_cookie;
  `vector constructor iterator'(&__t, 0x38ui64, 100, (void *(__cdecl *)(void *))_buy_offer::_buy_offer);
  v41 = 0;
  memset(&v42, 0, 7ui64);
  v43 = (CTransportShip *)&g_TransportShip[10162 * CPlayerDB::GetRaceCode(&pOne->m_Param)];
  v44 = 0;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, pOne->m_id.wIndex) == 99 )
  {
    v44 = 23;
  }
  else if ( bUseNPCLinkIntem || pStorea->m_pExistMap == pOne->m_pCurMap )
  {
    v66 = pStorea->m_byNpcRaceCode;
    v10 = CPlayerDB::GetRaceCode(&pOne->m_Param);
    if ( v66 == v10 || pStorea->m_byNpcRaceCode == 255 )
    {
      if ( bUseNPCLinkIntem || (GetSqrt(pStorea->m_pDum->m_pDumPos->m_fCenterPos, pOne->m_fCurPos), a6 <= 100.0) )
      {
        for ( j = 0; j < (unsigned __int8)v93; ++j )
          ++*(&v41 + v94[j].byStorageCode);
        for ( j = 0; j < 8; ++j )
        {
          v11 = _STORAGE_LIST::GetNumEmptyCon(pOne->m_Param.m_pStoragePtr[j]);
          if ( v11 < (unsigned __int8)*(&v41 + j) )
          {
            v44 = 4;
            goto $RESULT_67;
          }
        }
        for ( j = 0; j < (unsigned __int8)v93; ++j )
        {
          *(&__t + 56 * j) = v94[j].dwGoodSerial;
          *(&v37 + 56 * j) = v94[j].byAmount;
          *(&v38 + 56 * j) = v94[j].byStorageCode;
        }
        _effect_parameter::GetEff_Have(&pOne->m_EP, 1);
        v67 = a6;
        _effect_parameter::GetEff_Have(&pOne->m_EP, 11);
        *(_QWORD *)&v12 = LODWORD(v67);
        *(float *)&v12 = v67 + a6;
        v45 = v67 + a6;
        v46 = 0;
        v68 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        v69 = CUserDB::GetPtrActPoint(pOne->m_pUserDB);
        CPvpOrderView::GetPvpCash(&pOne->m_kPvpOrderView);
        v70 = v12;
        v71 = CPlayerDB::GetGold(&pOne->m_Param);
        v13 = CPlayerDB::GetDalant(&pOne->m_Param);
        byGrade = pOne->m_Param.m_byPvPGrade;
        v44 = CItemStore::IsSell(pStorea, v93, (_buy_offer *)&__t, v13, v71, v70, v69, &v46, v45, v68, byGrade);
        v47 = -1;
        if ( !v44 )
        {
          CTransportShip::InitTicketReserver(v43);
          for ( j = 0; j < (unsigned __int8)v93; ++j )
          {
            if ( *(&pFixingItem.m_byTableCode + 56 * j) == 26 )
            {
              v48 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 26, *(&pFixingItem.m_wItemIndex + 28 * j));
              if ( v48 )
              {
                if ( *(_DWORD *)&v48[1].m_strCode[0] == 1 )
                {
                  v72 = CPlayerDB::GetRaceCode(&pOne->m_Param);
                  v14 = CPvpUserAndGuildRankingSystem::Instance();
                  v73 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v14, v72, 0);
                  v15 = CPlayerDB::GetCharSerial(&pOne->m_Param);
                  if ( v73 != v15 )
                  {
                    v44 = 6;
                    break;
                  }
                }
              }
            }
            if ( pStorea->m_pRec->m_nStore_trade == 18 && *(&pFixingItem.m_byTableCode + 56 * j) == 28 )
            {
              v49 = GetItemEquipCivil(28, *(&pFixingItem.m_wItemIndex + 28 * j));
              v16 = CPlayerDB::GetRaceSexCode(&pOne->m_Param);
              if ( v49[v16] != 49 )
              {
                v44 = 16;
                break;
              }
              v50 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 28, *(&pFixingItem.m_wItemIndex + 28 * j));
              if ( CTransportShip::GetLeftTicketIncludeReserNum(v43, &v50[4].m_strCode[8], 1) <= 0 )
              {
                v44 = 16;
                break;
              }
            }
            if ( *(&pFixingItem.m_byTableCode + 56 * j) == 31 )
            {
              v51 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 31, *(&pFixingItem.m_wItemIndex + 28 * j));
              if ( *(_DWORD *)&v51[4].m_strCode[4] == 11 && !strcmp_0(v51->m_strCode, "bxgol01") )
              {
                if ( (signed int)(unsigned __int8)*(&v37 + 56 * j) > 1 )
                  *(&v37 + 56 * j) = 1;
                v17 = CGoldenBoxItemMgr::Instance();
                v47 = CGoldenBoxItemMgr::IsBuyRaceBossGoldBox(v17, pOne);
                if ( v47 )
                {
                  v44 = v47;
                  break;
                }
              }
            }
            if ( !IsStorageCodeWithItemKind(*(&pFixingItem.m_byTableCode + 56 * j), v94[j].byStorageCode) )
            {
              v44 = 5;
              break;
            }
            if ( v94[j].byStorageCode == 1 )
            {
              v52 = &pOne->m_Param.m_dbEquip.m_pStorageList[*(&pFixingItem.m_byTableCode + 56 * j)].m_bLoad;
              if ( *v52 )
              {
                v44 = 5;
                break;
              }
              if ( !CPlayer::_check_equip_part(pOne, (_STORAGE_LIST::_db_con *)((char *)&pFixingItem + 56 * j)) )
              {
                v44 = 7;
                break;
              }
            }
            if ( v94[j].byStorageCode == 2 )
            {
              if ( *(&pFixingItem.m_byTableCode + 56 * j) != 8
                && *(&pFixingItem.m_byTableCode + 56 * j) != 9
                && *(&pFixingItem.m_byTableCode + 56 * j) != 10 )
              {
                v44 = 5;
                break;
              }
              v53 = 0;
              for ( k = 0; k < 7; ++k )
              {
                if ( pOne->m_Param.m_dbEmbellish.m_pStorageList[k].m_bLoad
                  && pOne->m_Param.m_dbEmbellish.m_pStorageList[k].m_byTableCode == *(&pFixingItem.m_byTableCode + 56 * j) )
                {
                  ++v53;
                }
              }
              if ( v53 > 1 )
              {
                v44 = 5;
                break;
              }
              if ( !CPlayer::_check_embel_part(pOne, (_STORAGE_LIST::_db_con *)((char *)&pFixingItem + 56 * j)) )
              {
                v44 = 7;
                break;
              }
            }
            if ( v94[j].byStorageCode == 3 )
            {
              for ( l = 0; l < 88; ++l )
              {
                v56 = &pOne->m_Param.m_dbForce.m_pStorageList[l].m_bLoad;
                if ( *v56
                  && *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + *(_WORD *)(v56 + 3)) == *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect
                                                                                              + *(&pFixingItem.m_wItemIndex
                                                                                                + 28 * j)) )
                {
                  v44 = 7;
                  goto $RESULT_67;
                }
              }
            }
            if ( v94[j].byStorageCode == 4 )
            {
              for ( m = 0; m < 4; ++m )
              {
                v58 = &pOne->m_Param.m_dbAnimus.m_pStorageList[m].m_bLoad;
                if ( *v58 && *(_WORD *)(v58 + 3) == *(&pFixingItem.m_wItemIndex + 28 * j) )
                {
                  v44 = 7;
                  goto $RESULT_67;
                }
              }
            }
          }
        }
      }
      else
      {
        v44 = 3;
      }
    }
    else
    {
      v44 = 2;
    }
  }
  else
  {
    v44 = 1;
  }
$RESULT_67:
  if ( !v44 )
  {
    CTransportShip::ApplyTicketReserver(v43);
    v59 = 0;
    for ( j = 0; j < (unsigned __int8)v93; ++j )
    {
      v60 = &pStorea->m_pStorageItem[(unsigned __int8)*(&__t + 56 * j)].byItemTableCode;
      if ( v60[40] == 1 )
      {
        CItemStore::SubLimitItemNum(pStorea, *((_DWORD *)v60 + 11), (unsigned __int8)*(&v37 + 56 * j));
        v59 = 1;
      }
    }
    if ( v59 )
      CItemStore::UpdateLimitItemNum(pStorea, 1);
    v18 = CItemStore::GetLastTradeDalant(pStorea);
    CPlayer::SubDalant(pOne, v18);
    v19 = CItemStore::GetLastTradeGold(pStorea);
    CPlayer::SubGold(pOne, v19);
    v20 = CItemStore::GetLastTradePoint(pStorea);
    CPlayer::SubPoint(pOne, v20);
    for ( n = 0; n < 3; ++n )
    {
      v21 = CItemStore::GetLastTradeActPoint(pStorea, n);
      CPlayer::SubActPoint(pOne, n, v21);
    }
    for ( n = 0; n < (unsigned __int8)v93; ++n )
    {
      v40[28 * n] = CPlayerDB::GetNewItemSerial(&pOne->m_Param);
      *(&pFixingItem.m_wSerial + 28 * n) = v40[28 * n];
      LOBYTE(dwHasGold[0]) = 1;
      if ( !CPlayer::Emb_AddStorage(
              pOne,
              v94[n].byStorageCode,
              (_STORAGE_LIST::_storage_con *)(&pFixingItem.m_bLoad + 56 * n),
              0,
              1) )
      {
        v22 = CItemStore::GetLastTradeDalant(pStorea);
        CPlayer::SubDalant(pOne, -v22);
        v23 = CItemStore::GetLastTradeGold(pStorea);
        CPlayer::SubGold(pOne, -v23);
        v24 = CItemStore::GetLastTradePoint(pStorea);
        CPlayer::SubPoint(pOne, -v24);
        for ( ii = 0; ii < 3; ++ii )
        {
          v25 = CItemStore::GetLastTradeActPoint(pStorea, ii);
          CPlayer::SubActPoint(pOne, ii, -v25);
        }
        CPlayer::SendMsg_BuyItemStoreResult(pOne, pStorea, (unsigned __int8)v93, (_buy_offer *)&__t, 17);
        CMgrAvatorItemHistory::add_storage_fail(
          &CPlayer::s_MgrItemHistory,
          pOne->m_ObjID.m_wIndex,
          (_STORAGE_LIST::_db_con *)((char *)&pFixingItem + 56 * ii),
          " CPlayer::pc_BuyItemStore - Emb_AddStorage() Fail",
          pOne->m_szItemHistoryFileName);
        return;
      }
      if ( !v47 && *(&pFixingItem.m_byTableCode + 56 * n) == 31 )
      {
        v63 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 31, *(&pFixingItem.m_wItemIndex + 28 * n));
        if ( !strcmp_0(v63->m_strCode, "bxgol01") )
        {
          CHolyStoneSystem::SetGoldBoxConsumable(&g_HolySys, 0);
          pItem = (_STORAGE_LIST::_db_con *)((char *)&pFixingItem + 56 * n);
          szCharName = CPlayerDB::GetCharNameA(&pOne->m_Param);
          v26 = CPlayerDB::GetCharSerial(&pOne->m_Param);
          CPlayer::SendMsg_Notify_Get_Golden_Box(pOne, 1, v26, szCharName, pItem, 0);
        }
      }
    }
    if ( !pOne->m_byUserDgr )
    {
      if ( CItemStore::GetLastTradeDalant(pStorea) > 0 )
      {
        nAdd = CItemStore::GetLastTradeDalant(pStorea);
        v27 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        eAddDalant(v27, nAdd);
      }
      if ( CItemStore::GetLastTradeGold(pStorea) > 0 )
      {
        v77 = CItemStore::GetLastTradeGold(pStorea);
        v28 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        eAddGold(v28, v77);
      }
    }
    v64 = CPlayerDB::GetLevel(&pOne->m_Param);
    if ( v64 == 30 || v64 == 40 || v64 == 50 || v64 == 60 )
    {
      if ( CItemStore::GetLastTradeDalant(pStorea) > 0 )
      {
        nAmount = CItemStore::GetLastTradeDalant(pStorea);
        szClass = CPlayerDB::GetPtrCurClass(&pOne->m_Param)->m_strCode;
        nLv = CPlayerDB::GetLevel(&pOne->m_Param);
        v81 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        v29 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateBuyData(v29, v81, nLv, szClass, nAmount);
      }
      if ( CItemStore::GetLastTradeGold(pStorea) > 0 )
      {
        v82 = 2000 * CItemStore::GetLastTradeGold(pStorea);
        v83 = CPlayerDB::GetPtrCurClass(&pOne->m_Param)->m_strCode;
        v84 = CPlayerDB::GetLevel(&pOne->m_Param);
        v85 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        v30 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateBuyData(v30, v85, v84, v83, v82);
      }
    }
    v86 = pOne->m_szItemHistoryFileName;
    dwNewGold = CPlayerDB::GetGold(&pOne->m_Param);
    dwNewDalant = CPlayerDB::GetDalant(&pOne->m_Param);
    dwCostGold = CItemStore::GetLastTradeGold(pStorea);
    v31 = CItemStore::GetLastTradeDalant(pStorea);
    CMgrAvatorItemHistory::buy_item(
      &CPlayer::s_MgrItemHistory,
      pOne->m_ObjID.m_wIndex,
      (_buy_offer *)&__t,
      v93,
      v31,
      dwCostGold,
      dwNewDalant,
      dwNewGold,
      v86);
  }
  if ( !v44 || v44 == 19 )
  {
    _limit_amount_info::_limit_amount_info(&pAmountInfo);
    CItemStore::GetLimitItemAmount(pStorea, &pAmountInfo);
    CPlayer::SendMsg_StoreLimitItemAmountInfo(pOne, pStorea->m_pRec->m_dwIndex, &pAmountInfo);
  }
  CPlayer::SendMsg_BuyItemStoreResult(pOne, pStorea, (unsigned __int8)v93, (_buy_offer *)&__t, v44);
}
