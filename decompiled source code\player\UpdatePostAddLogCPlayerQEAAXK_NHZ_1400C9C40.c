/*
 * Function: ?UpdatePostAddLog@CPlayer@@QEAAXK_NH@Z
 * Address: 0x1400C9C40
 */

void __fastcall CPlayer::UpdatePostAddLog(CPlayer *this, unsigned int dwIndex, bool bLog, int nItemKey)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CPostSystemManager *v6; // rax@5
  CPostSystemManager *v7; // rax@8
  int v8; // eax@9
  CPostSystemManager *v9; // rax@13
  CPostSystemManager *v10; // rax@17
  CPostSystemManager *v11; // rax@20
  CPostSystemManager *v12; // rax@22
  __int64 v13; // [sp+0h] [bp-D8h]@1
  char byState[4]; // [sp+20h] [bp-B8h]@8
  int nKey; // [sp+28h] [bp-B0h]@8
  unsigned __int64 dwDur; // [sp+30h] [bp-A8h]@9
  unsigned int dwUpt; // [sp+38h] [bp-A0h]@9
  unsigned int dwGold; // [sp+40h] [bp-98h]@9
  unsigned __int64 lnUID; // [sp+48h] [bp-90h]@9
  CPostData *v20; // [sp+50h] [bp-88h]@4
  _POSTDATA_DB_BASE *v21; // [sp+58h] [bp-80h]@4
  _POSTDATA_DB_BASE *v22; // [sp+60h] [bp-78h]@4
  char v23; // [sp+68h] [bp-70h]@14
  int v24; // [sp+6Ch] [bp-6Ch]@14
  int j; // [sp+70h] [bp-68h]@14
  __int64 v26; // [sp+78h] [bp-60h]@8
  char *v27; // [sp+80h] [bp-58h]@8
  __int64 v28; // [sp+88h] [bp-50h]@13
  __int64 v29; // [sp+90h] [bp-48h]@13
  __int64 v30; // [sp+98h] [bp-40h]@13
  __int64 v31; // [sp+A0h] [bp-38h]@13
  char *v32; // [sp+A8h] [bp-30h]@13
  __int64 v33; // [sp+B0h] [bp-28h]@17
  __int64 v34; // [sp+B8h] [bp-20h]@17
  char *v35; // [sp+C0h] [bp-18h]@17
  CPlayer *v36; // [sp+E0h] [bp+8h]@1
  unsigned int nIndex; // [sp+E8h] [bp+10h]@1
  bool v38; // [sp+F0h] [bp+18h]@1
  int v39; // [sp+F8h] [bp+20h]@1

  v39 = nItemKey;
  v38 = bLog;
  nIndex = dwIndex;
  v36 = this;
  v4 = &v13;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v20 = 0i64;
  v21 = &v36->m_pUserDB->m_AvatorData.dbPostData;
  v22 = &v36->m_pUserDB->m_AvatorData_bk.dbPostData;
  v20 = CPostStorage::GetPostDataFromInx(&v36->m_Param.m_PostStorage, dwIndex);
  if ( v20 )
  {
    if ( v20->m_dwPSSerial && v20->m_dwPSSerial != v21->dbPost.m_PostList[nIndex].dwPSSerial )
    {
      v26 = 297i64 * nIndex;
      v27 = CPlayerDB::GetCharNameW(&v36->m_Param);
      v7 = CPostSystemManager::Instace();
      nKey = v21->dbPost.m_PostList[(unsigned __int64)v26 / 0x129].dwPSSerial;
      *(_DWORD *)byState = v20->m_dwPSSerial;
      CPostSystemManager::Log(v7, "GetItem Wrong DB Data >> Name:%s >> Inx:%d >> Storage(S:%d) : DB(S:%d)", v27, nIndex);
    }
    v8 = _INVENKEY::CovDBKey(&v20->m_Key);
    lnUID = v20->m_lnUID;
    dwGold = v20->m_dwGold;
    dwUpt = v20->m_dwUpt;
    dwDur = v20->m_dwDur;
    nKey = v8;
    byState[0] = v20->m_byState;
    CUserDB::Update_Post(
      v36->m_pUserDB,
      nIndex,
      v20->m_dwPSSerial,
      v20->m_nNumber,
      byState[0],
      v8,
      dwDur,
      dwUpt,
      dwGold,
      lnUID);
    if ( v38
      && !v21->dbPost.m_PostList[nIndex].bNew
      && v21->dbPost.m_PostList[nIndex].dwPSSerial
      && v21->dbPost.m_PostList[nIndex].nKey == v22->dbPost.m_PostList[nIndex].nKey )
    {
      v28 = 297i64 * nIndex;
      v29 = 297i64 * nIndex;
      v30 = 297i64 * nIndex;
      v31 = 297i64 * nIndex;
      v32 = CPlayerDB::GetCharNameW(&v36->m_Param);
      v9 = CPostSystemManager::Instace();
      dwGold = v22->dbPost.m_PostList[(unsigned __int64)v28 / 0x129].dwPSSerial;
      dwUpt = v22->dbPost.m_PostList[(unsigned __int64)v29 / 0x129].nKey;
      LODWORD(dwDur) = v21->dbPost.m_PostList[(unsigned __int64)v30 / 0x129].dwPSSerial;
      nKey = v21->dbPost.m_PostList[(unsigned __int64)v31 / 0x129].nKey;
      *(_DWORD *)byState = v39;
      CPostSystemManager::Log(
        v9,
        "GetItem KeyError >> Name:%s >> INX(%d) >> ORIGIN(%d) : NEW(%d) PS(%u) : OLD(%d) PS(%u)",
        v32,
        nIndex);
      if ( v21->dbPost.m_PostList[nIndex].dwPSSerial == v22->dbPost.m_PostList[nIndex].dwPSSerial )
      {
        v12 = CPostSystemManager::Instace();
        CPostSystemManager::Log(v12, "And Post Serial Is Equal!");
      }
      else
      {
        v23 = 0;
        v24 = -1;
        for ( j = 0; j < 50; ++j )
        {
          if ( v21->dbPost.m_PostList[nIndex].dwPSSerial == v22->dbPost.m_PostList[j].dwPSSerial )
          {
            v33 = 297i64 * j;
            v34 = 297i64 * nIndex;
            v35 = CPlayerDB::GetCharNameW(&v36->m_Param);
            v10 = CPostSystemManager::Instace();
            LODWORD(dwDur) = v22->dbPost.m_PostList[(unsigned __int64)v33 / 0x129].dwPSSerial;
            nKey = j;
            *(_DWORD *)byState = v21->dbPost.m_PostList[(unsigned __int64)v34 / 0x129].dwPSSerial;
            CPostSystemManager::Log(
              v10,
              "And Post DB Base Index Wrong >> Name:%s >> NEW : INX(%d) PS(%u) >> OLD : INX(%d) PS(%u)",
              v35,
              nIndex);
            v23 = 1;
            break;
          }
        }
        if ( !v23 )
        {
          v11 = CPostSystemManager::Instace();
          CPostSystemManager::Log(v11, "And Matched Serial Is None!");
        }
      }
    }
  }
  else
  {
    v6 = CPostSystemManager::Instace();
    CPostSystemManager::Log(v6, "CPlayer::UpdatePostAddLog() : pPost Is Null : Index(%u)", nIndex);
  }
}
