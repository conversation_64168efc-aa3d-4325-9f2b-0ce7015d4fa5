/*
 * Function: _CUnmannedTraderUserInfo::CheatCancelRegistAll_::_1_::dtor$2
 * Address: 0x14035C8A0
 */

void __fastcall CUnmannedTraderUserInfo::CheatCancelRegistAll_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 248));
}
