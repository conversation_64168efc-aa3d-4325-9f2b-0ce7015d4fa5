#include "../Headers/CAsyncLogInfo.h"
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <cstdarg>

namespace NexusProtection::Authentication {

    // CLogFile implementation
    CLogFile::CLogFile() = default;

    CLogFile::~CLogFile() {
        Close();
    }

    bool CLogFile::SetWriteLogFile(const std::string& filePath, bool append, 
                                  bool autoFlush, bool timestamped, bool enabled) {
        std::lock_guard<std::mutex> lock(m_fileMutex);
        
        Close();
        
        m_filePath = filePath;
        m_autoFlush = autoFlush;
        m_timestamped = timestamped;
        m_enabled = enabled;
        
        if (!m_enabled) {
            return true;
        }

        try {
            // Create directory if it doesn't exist
            std::filesystem::path path(filePath);
            std::filesystem::create_directories(path.parent_path());
            
            // Open file
            auto mode = append ? (std::ios::out | std::ios::app) : std::ios::out;
            m_file.open(filePath, mode);
            
            if (m_file.is_open()) {
                m_isOpen = true;
                if (m_timestamped) {
                    Write("=== Log file opened at " + FormatTimestamp() + " ===");
                }
                return true;
            }
        }
        catch (const std::exception&) {
            // Failed to open file
        }
        
        m_isOpen = false;
        return false;
    }

    void CLogFile::Write(const std::string& message) {
        std::lock_guard<std::mutex> lock(m_fileMutex);
        
        if (!m_isOpen || !m_enabled) {
            return;
        }

        try {
            if (m_timestamped) {
                m_file << "[" << FormatTimestamp() << "] " << message << std::endl;
            } else {
                m_file << message << std::endl;
            }
            
            if (m_autoFlush) {
                m_file.flush();
            }
        }
        catch (const std::exception&) {
            // Error writing to file
        }
    }

    void CLogFile::Write(const char* format, ...) {
        if (!format) {
            return;
        }

        va_list args;
        va_start(args, format);
        
        // Calculate required buffer size
        va_list args_copy;
        va_copy(args_copy, args);
        int size = std::vsnprintf(nullptr, 0, format, args_copy);
        va_end(args_copy);
        
        if (size > 0) {
            std::vector<char> buffer(size + 1);
            std::vsnprintf(buffer.data(), buffer.size(), format, args);
            Write(std::string(buffer.data()));
        }
        
        va_end(args);
    }

    void CLogFile::Flush() {
        std::lock_guard<std::mutex> lock(m_fileMutex);
        
        if (m_isOpen) {
            m_file.flush();
        }
    }

    void CLogFile::Close() {
        std::lock_guard<std::mutex> lock(m_fileMutex);
        
        if (m_isOpen) {
            if (m_timestamped) {
                m_file << "=== Log file closed at " << FormatTimestamp() << " ===" << std::endl;
            }
            m_file.close();
            m_isOpen = false;
        }
    }

    std::string CLogFile::FormatTimestamp() const {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        ss << "." << std::setfill('0') << std::setw(3) << ms.count();
        
        return ss.str();
    }

    // CAsyncLogBufferList implementation
    CAsyncLogBufferList::CAsyncLogBufferList() = default;

    CAsyncLogBufferList::~CAsyncLogBufferList() {
        Clear();
    }

    void CAsyncLogBufferList::AddEntry(const LogEntry& entry) {
        std::lock_guard<std::mutex> lock(m_bufferMutex);
        
        // Prevent buffer overflow
        if (m_buffer.size() >= MAX_BUFFER_SIZE) {
            m_buffer.pop(); // Remove oldest entry
        }
        
        m_buffer.push(entry);
        m_bufferCondition.notify_one();
    }

    bool CAsyncLogBufferList::GetNextEntry(LogEntry& entry) {
        std::unique_lock<std::mutex> lock(m_bufferMutex);
        
        if (m_buffer.empty()) {
            return false;
        }
        
        entry = m_buffer.front();
        m_buffer.pop();
        return true;
    }

    void CAsyncLogBufferList::Clear() {
        std::lock_guard<std::mutex> lock(m_bufferMutex);
        
        while (!m_buffer.empty()) {
            m_buffer.pop();
        }
    }

    size_t CAsyncLogBufferList::GetSize() const {
        std::lock_guard<std::mutex> lock(m_bufferMutex);
        return m_buffer.size();
    }

    bool CAsyncLogBufferList::IsEmpty() const {
        std::lock_guard<std::mutex> lock(m_bufferMutex);
        return m_buffer.empty();
    }

    // CAsyncLogInfo implementation
    CAsyncLogInfo::CAsyncLogInfo() {
        m_lastFileNameUpdate = std::chrono::steady_clock::now();
    }

    CAsyncLogInfo::~CAsyncLogInfo() {
        Shutdown();
    }

    bool CAsyncLogInfo::Initialize(LogType logType, const std::string& dirPath, const std::string& typeName,
                                  bool addDateFileName, uint32_t updateFileNameDelay,
                                  CLogFile* loadingLog) {
        if (m_isInitialized) {
            return true;
        }

        try {
            m_logType = logType;
            m_dirPath = dirPath;
            m_typeName = typeName;
            m_addDateFileName = addDateFileName;
            m_updateFileNameDelay = updateFileNameDelay;
            m_loadingLog = loadingLog;

            // Create log directory
            if (!CreateLogDirectory()) {
                return false;
            }

            // Initialize log file
            m_logFile = std::make_unique<CLogFile>();
            
            // Generate initial file name
            UpdateLogFileName();

            // Initialize buffer list
            m_bufferList = std::make_unique<CAsyncLogBufferList>();

            // Start async processing
            StartAsyncProcessing();

            m_isInitialized = true;
            m_isActive = true;

            WriteLog("Async log info initialized for type: " + LogTypeToString(logType));
            return true;
        }
        catch (const std::exception& e) {
            if (m_loadingLog) {
                m_loadingLog->Write("Failed to initialize async log info: " + std::string(e.what()));
            }
            return false;
        }
    }

    void CAsyncLogInfo::Shutdown() {
        if (!m_isInitialized) {
            return;
        }

        m_isActive = false;
        StopAsyncProcessing();

        // Process remaining buffer entries
        ProcessLogBuffer();

        // Close log file
        if (m_logFile) {
            m_logFile->Close();
        }

        m_bufferList.reset();
        m_logFile.reset();

        m_isInitialized = false;
    }

    void CAsyncLogInfo::WriteLog(const std::string& message) {
        if (!m_isInitialized || !m_isActive) {
            return;
        }

        LogEntry entry(m_logType, message);
        m_bufferList->AddEntry(entry);
        ++m_logCount;
    }

    void CAsyncLogInfo::WriteLog(LogType type, const std::string& message) {
        if (!m_isInitialized || !m_isActive) {
            return;
        }

        LogEntry entry(type, message);
        m_bufferList->AddEntry(entry);
        ++m_logCount;
    }

    void CAsyncLogInfo::WriteLogFormatted(const char* format, ...) {
        if (!format || !m_isInitialized || !m_isActive) {
            return;
        }

        va_list args;
        va_start(args, format);
        
        // Calculate required buffer size
        va_list args_copy;
        va_copy(args_copy, args);
        int size = std::vsnprintf(nullptr, 0, format, args_copy);
        va_end(args_copy);
        
        if (size > 0) {
            std::vector<char> buffer(size + 1);
            std::vsnprintf(buffer.data(), buffer.size(), format, args);
            WriteLog(std::string(buffer.data()));
        }
        
        va_end(args);
    }

    void CAsyncLogInfo::FlushLogs() {
        ProcessLogBuffer();
        
        if (m_logFile) {
            m_logFile->Flush();
        }
    }

    void CAsyncLogInfo::UpdateLogFileName() {
        std::lock_guard<std::mutex> lock(m_fileNameMutex);
        
        m_fileName = GenerateFileName();
        
        if (m_logFile) {
            std::string fullPath = m_dirPath + "/" + m_fileName;
            m_logFile->SetWriteLogFile(fullPath, true, false, true, true);
        }
        
        m_lastFileNameUpdate = std::chrono::steady_clock::now();
    }

    bool CAsyncLogInfo::SetLogDirectory(const std::string& dirPath) {
        m_dirPath = dirPath;
        return CreateLogDirectory();
    }

    bool CAsyncLogInfo::SetTypeName(const std::string& typeName) {
        m_typeName = typeName;
        UpdateLogFileName();
        return true;
    }

    void CAsyncLogInfo::ProcessLogBuffer() {
        if (!m_bufferList || !m_logFile) {
            return;
        }

        LogEntry entry;
        while (m_bufferList->GetNextEntry(entry)) {
            std::string formattedMessage = FormatLogMessage(entry.message);
            m_logFile->Write(formattedMessage);
        }
    }

    size_t CAsyncLogInfo::GetBufferSize() const {
        if (!m_bufferList) {
            return 0;
        }
        return m_bufferList->GetSize();
    }

    void CAsyncLogInfo::ClearBuffer() {
        if (m_bufferList) {
            m_bufferList->Clear();
        }
    }

    bool CAsyncLogInfo::CreateLogDirectory() {
        try {
            std::filesystem::create_directories(m_dirPath);
            return true;
        }
        catch (const std::exception&) {
            return false;
        }
    }

    std::string CAsyncLogInfo::GenerateFileName() const {
        std::stringstream ss;
        ss << m_typeName;

        if (m_addDateFileName) {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);
            ss << "_" << std::put_time(std::localtime(&time_t), "%Y%m%d");
        }

        ss << ".log";
        return ss.str();
    }

    std::string CAsyncLogInfo::FormatLogMessage(const std::string& message) const {
        std::stringstream ss;
        ss << "[" << LogTypeToString(m_logType) << "] " << message;
        return ss.str();
    }

    void CAsyncLogInfo::StartAsyncProcessing() {
        m_shouldStop = false;
        m_processingThread = std::make_unique<std::thread>(&CAsyncLogInfo::AsyncProcessingLoop, this);
    }

    void CAsyncLogInfo::StopAsyncProcessing() {
        m_shouldStop = true;

        if (m_processingThread && m_processingThread->joinable()) {
            m_processingCondition.notify_all();
            m_processingThread->join();
        }

        m_processingThread.reset();
    }

    void CAsyncLogInfo::AsyncProcessingLoop() {
        while (!m_shouldStop) {
            try {
                // Process log buffer
                ProcessLogBuffer();

                // Check if file name needs updating
                if (m_updateFileNameDelay != 0xFFFFFFFF) {
                    auto now = std::chrono::steady_clock::now();
                    auto timeSinceUpdate = std::chrono::duration_cast<std::chrono::milliseconds>(
                        now - m_lastFileNameUpdate);

                    if (timeSinceUpdate.count() >= m_updateFileNameDelay) {
                        UpdateLogFileName();
                    }
                }

                // Wait for next processing cycle
                std::unique_lock<std::mutex> lock(m_processingMutex);
                m_processingCondition.wait_for(lock, std::chrono::milliseconds(100));
            }
            catch (const std::exception&) {
                // Continue processing even if there's an error
            }
        }
    }

    // CAsyncLogger implementation
    CAsyncLogger::CAsyncLogger() = default;

    CAsyncLogger::~CAsyncLogger() {
        Shutdown();
    }

    bool CAsyncLogger::Initialize() {
        if (m_isInitialized) {
            return true;
        }

        try {
            // Initialize loading log
            if (!m_loadingLog.SetWriteLogFile("./Logs/loading.log", true, true, true, true)) {
                return false;
            }

            // Initialize system log
            if (!InitializeSystemLog()) {
                return false;
            }

            // Initialize buffer lists
            for (size_t i = 0; i < m_bufferLists.size(); ++i) {
                m_bufferLists[i] = std::make_unique<CAsyncLogBufferList>();
            }

            m_isInitialized = true;
            WriteSystemLog("Async logger initialized successfully");
            return true;
        }
        catch (const std::exception& e) {
            m_loadingLog.Write("Failed to initialize async logger: " + std::string(e.what()));
            return false;
        }
    }

    void CAsyncLogger::Shutdown() {
        if (!m_isInitialized) {
            return;
        }

        WriteSystemLog("Async logger shutting down");
        CleanupLogs();

        m_loadingLog.Close();
        m_isInitialized = false;
    }

    bool CAsyncLogger::RegisterLog(LogType logType, const std::string& dirPath, const std::string& typeName,
                                  bool addDateFileName, uint32_t updateFileNameDelay) {
        std::lock_guard<std::mutex> lock(m_logMapMutex);

        auto logInfo = std::make_unique<CAsyncLogInfo>();
        if (!logInfo->Initialize(logType, dirPath, typeName, addDateFileName, updateFileNameDelay, &m_loadingLog)) {
            return false;
        }

        m_logInfoMap[logType] = std::move(logInfo);
        return true;
    }

    bool CAsyncLogger::UnregisterLog(LogType logType) {
        std::lock_guard<std::mutex> lock(m_logMapMutex);

        auto it = m_logInfoMap.find(logType);
        if (it != m_logInfoMap.end()) {
            it->second->Shutdown();
            m_logInfoMap.erase(it);
            return true;
        }

        return false;
    }

    CAsyncLogInfo* CAsyncLogger::GetLogInfo(LogType logType) {
        std::lock_guard<std::mutex> lock(m_logMapMutex);

        auto it = m_logInfoMap.find(logType);
        if (it != m_logInfoMap.end()) {
            return it->second.get();
        }

        return nullptr;
    }

    void CAsyncLogger::WriteLog(LogType logType, const std::string& message) {
        auto* logInfo = GetLogInfo(logType);
        if (logInfo) {
            logInfo->WriteLog(message);
        }
    }

    void CAsyncLogger::WriteSystemLog(const std::string& message) {
        WriteLog(LogType::System, message);
    }

    void CAsyncLogger::WriteAuthLog(const std::string& message) {
        WriteLog(LogType::Authentication, message);
    }

    void CAsyncLogger::WriteBillingLog(const std::string& message) {
        WriteLog(LogType::Billing, message);
    }

    void CAsyncLogger::WriteSecurityLog(const std::string& message) {
        WriteLog(LogType::Security, message);
    }

    size_t CAsyncLogger::GetLogCount() const {
        std::lock_guard<std::mutex> lock(m_logMapMutex);
        return m_logInfoMap.size();
    }

    bool CAsyncLogger::InitializeSystemLog() {
        return RegisterLog(LogType::System, "./Logs", "system", true, 0xFFFFFFFF);
    }

    void CAsyncLogger::CleanupLogs() {
        std::lock_guard<std::mutex> lock(m_logMapMutex);

        for (auto& pair : m_logInfoMap) {
            pair.second->Shutdown();
        }

        m_logInfoMap.clear();
        m_systemLogInfo = nullptr;

        for (auto& bufferList : m_bufferLists) {
            bufferList.reset();
        }
    }

    // Global instance
    static std::unique_ptr<CAsyncLogger> s_asyncLoggerInstance;
    static std::mutex s_asyncLoggerMutex;

    CAsyncLogger& GetAsyncLogger() {
        std::lock_guard<std::mutex> lock(s_asyncLoggerMutex);
        if (!s_asyncLoggerInstance) {
            s_asyncLoggerInstance = std::make_unique<CAsyncLogger>();
        }
        return *s_asyncLoggerInstance;
    }

} // namespace NexusProtection::Authentication

// Legacy C interface implementation
extern "C" {
    CAsyncLogInfo_Legacy* CAsyncLogInfo_Create() {
        static CAsyncLogInfo_Legacy s_legacyLogInfo;
        std::memset(&s_legacyLogInfo, 0, sizeof(CAsyncLogInfo_Legacy));
        return &s_legacyLogInfo;
    }

    void CAsyncLogInfo_Destroy(CAsyncLogInfo_Legacy* logInfo) {
        (void)logInfo; // Suppress unused parameter warning
        // Modern implementation handles cleanup automatically
    }

    bool CAsyncLogInfo_Init(CAsyncLogInfo_Legacy* logInfo, ASYNC_LOG_TYPE eType,
                           const char* szDirPath, const char* szTypeName,
                           bool bAddDateFileName, uint32_t dwUpdateFileNameDelay,
                           CLogFile* logLoading) {
        if (!logInfo || !szDirPath || !szTypeName) {
            return false;
        }

        // Convert legacy type to modern type
        NexusProtection::Authentication::LogType logType =
            static_cast<NexusProtection::Authentication::LogType>(eType);

        // Get async logger instance
        auto& asyncLogger = NexusProtection::Authentication::GetAsyncLogger();

        // Initialize if not already done
        if (!asyncLogger.IsInitialized()) {
            asyncLogger.Initialize();
        }

        // Register the log
        bool result = asyncLogger.RegisterLog(logType, szDirPath, szTypeName,
                                            bAddDateFileName, dwUpdateFileNameDelay);

        if (result) {
            // Update legacy structure
            logInfo->m_eType = eType;
            strncpy_s(logInfo->m_szDirPath, sizeof(logInfo->m_szDirPath), szDirPath, _TRUNCATE);
            strncpy_s(logInfo->m_szTypeName, sizeof(logInfo->m_szTypeName), szTypeName, _TRUNCATE);
            logInfo->m_bAddDateFileName = bAddDateFileName;
            logInfo->m_dwUpdateFileNameDelay = dwUpdateFileNameDelay;
            logInfo->m_dwCount = 0;
            logInfo->m_pLogFile = nullptr; // Modern implementation manages this internally
            logInfo->m_pBufferList = nullptr; // Modern implementation manages this internally
        }

        return result;
    }

    void CAsyncLogInfo_WriteLog(CAsyncLogInfo_Legacy* logInfo, const char* message) {
        if (!logInfo || !message) {
            return;
        }

        NexusProtection::Authentication::LogType logType =
            static_cast<NexusProtection::Authentication::LogType>(logInfo->m_eType);

        auto& asyncLogger = NexusProtection::Authentication::GetAsyncLogger();
        asyncLogger.WriteLog(logType, message);

        ++logInfo->m_dwCount;
    }

    uint32_t CAsyncLogInfo_GetCount(CAsyncLogInfo_Legacy* logInfo) {
        if (!logInfo) {
            return 0;
        }
        return logInfo->m_dwCount;
    }

    void CAsyncLogInfo_IncreaseCount(CAsyncLogInfo_Legacy* logInfo) {
        if (logInfo) {
            ++logInfo->m_dwCount;
        }
    }

    void CAsyncLogInfo_UpdateLogFileName(CAsyncLogInfo_Legacy* logInfo) {
        if (!logInfo) {
            return;
        }

        NexusProtection::Authentication::LogType logType =
            static_cast<NexusProtection::Authentication::LogType>(logInfo->m_eType);

        auto& asyncLogger = NexusProtection::Authentication::GetAsyncLogger();
        auto* logInfoPtr = asyncLogger.GetLogInfo(logType);

        if (logInfoPtr) {
            logInfoPtr->UpdateLogFileName();
        }
    }
}

// Global legacy compatibility
NexusProtection::Authentication::CAsyncLogger* g_pAsyncLogger = nullptr;
