/*
 * Function: ?GetMapData@CGuildRoomSystem@@QEAAPEAVCMapData@@EE@Z
 * Address: 0x1402EA9C0
 */

CMapData *__fastcall CGuildRoomSystem::GetMapData(CGuildRoomSystem *this, char byRace, char byMapType)
{
  CMapData *result; // rax@3

  if ( (signed int)(unsigned __int8)byRace < 3 && (signed int)(unsigned __int8)byMapType < 2 )
    result = this->m_pRoomMap[(unsigned __int8)byRace][(unsigned __int8)byMapType];
  else
    result = 0i64;
  return result;
}
