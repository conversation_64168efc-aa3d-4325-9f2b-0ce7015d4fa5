/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderRegistItemInfo@@_KV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008C1A
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderRegistItemInfo *,unsigned __int64,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val, std::allocator<CUnmannedTraderRegistItemInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderRegistItemInfo *,unsigned __int64,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
