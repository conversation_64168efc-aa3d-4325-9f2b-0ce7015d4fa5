/*
 * Function: ?SendInformChange@CHonorGuild@@QEAAXEG@Z
 * Address: 0x14025F850
 */

void __fastcall CHonorGuild::SendInformChange(CHonorGuild *this, char byRace, unsigned __int16 wIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@6
  unsigned int v6; // eax@6
  __int64 v7; // [sp+0h] [bp-88h]@1
  CPlayer *v8; // [sp+30h] [bp-58h]@4
  char pbyType; // [sp+44h] [bp-44h]@10
  char v10; // [sp+45h] [bp-43h]@10
  char szMsg; // [sp+64h] [bp-24h]@10
  CHonorGuild *v12; // [sp+90h] [bp+8h]@1
  char v13; // [sp+98h] [bp+10h]@1
  unsigned __int16 v14; // [sp+A0h] [bp+18h]@1

  v14 = wIndex;
  v13 = byRace;
  v12 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0i64;
  if ( v12->m_bSendInform[(unsigned __int8)byRace] )
  {
    if ( wIndex == 0xFFFF )
    {
      v5 = CPvpUserAndGuildRankingSystem::Instance();
      v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v13, 0);
      v8 = GetPtrPlayerFromSerial(&g_Player, 2532, v6);
      if ( !v8 || !v8->m_bOper )
        return;
      v14 = v8->m_ObjID.m_wIndex;
    }
    pbyType = 27;
    v10 = 117;
    szMsg = 0;
    CNetProcess::LoadSendMsg(unk_1414F2088, v14, &pbyType, &szMsg, 1u);
    v12->m_bSendInform[(unsigned __int8)v13] = 0;
  }
}
