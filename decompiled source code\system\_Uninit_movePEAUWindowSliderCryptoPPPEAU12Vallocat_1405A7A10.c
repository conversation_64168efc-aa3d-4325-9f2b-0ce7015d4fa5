/*
 * Function: ??$_Uninit_move@PEAUWindowSlider@CryptoPP@@PEAU12@V?$allocator@UWindowSlider@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAUWindowSlider@CryptoPP@@PEAU12@00AEAV?$allocator@UWindowSlider@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7A10
 */

int std::_Uninit_move<CryptoPP::WindowSlider *,CryptoPP::WindowSlider *,std::allocator<CryptoPP::WindowSlider>,std::_Undefined_move_tag>()
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::WindowSlider *,CryptoPP::WindowSlider *,std::allocator<CryptoPP::WindowSlider>>();
}
