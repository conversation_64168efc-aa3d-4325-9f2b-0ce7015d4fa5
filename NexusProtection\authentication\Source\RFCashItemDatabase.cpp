#include "../Headers/RFCashItemDatabase.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <stdexcept>
#include <fstream>
#include <cstdarg>

namespace NexusProtection::Authentication {

    // CashAuthParams implementation

    CashAuthParams::CashAuthParams(const std::string& accId, AuthRegion authRegion)
        : accountId(accId), region(authRegion) {
    }

    bool CashAuthParams::IsValid() const {
        return !accountId.empty() && 
               accountId.length() <= 64 && 
               region != AuthRegion::UNKNOWN &&
               timeout.count() > 0;
    }

    std::string CashAuthParams::ToString() const {
        std::ostringstream oss;
        oss << "CashAuthParams{";
        oss << "AccountId: \"" << accountId << "\", ";
        oss << "Region: " << CRFCashItemDatabase::AuthRegionToString(region) << ", ";
        oss << "CashAmount: " << cashAmount << ", ";
        oss << "Timeout: " << timeout.count() << "ms, ";
        oss << "Logging: " << (enableLogging ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    void CashAuthParams::Reset() {
        accountId.clear();
        cashAmount = 0;
        region = AuthRegion::STANDARD;
        timeout = std::chrono::milliseconds(30000);
        enableLogging = true;
    }

    // AuthResultInfo implementation

    std::string AuthResultInfo::ToString() const {
        std::ostringstream oss;
        oss << "AuthResultInfo{";
        oss << "Result: " << CRFCashItemDatabase::AuthResultToString(result) << ", ";
        oss << "CashAmount: " << cashAmount << ", ";
        oss << "ExecutionTime: " << executionTime.count() << "ms, ";
        oss << "ErrorMessage: \"" << errorMessage << "\", ";
        oss << "Query: \"" << sqlQuery << "\"";
        oss << "}";
        return oss.str();
    }

    // ModernSQLStatement implementation

    struct ModernSQLStatement::Impl {
        std::string lastError;
        std::string preparedQuery;
        std::chrono::steady_clock::time_point startTime;
        std::chrono::milliseconds executionTime{0};
        bool isValid{false};
        bool isPrepared{false};
        std::chrono::milliseconds timeout{30000};
        
        // Simulated SQL state for modern implementation
        bool hasData{false};
        uint32_t resultValue{0};
    };

    ModernSQLStatement::ModernSQLStatement() : m_pImpl(std::make_unique<Impl>()) {
    }

    ModernSQLStatement::~ModernSQLStatement() {
        CloseCursor();
    }

    bool ModernSQLStatement::Prepare(const std::string& query) {
        if (query.empty()) {
            m_pImpl->lastError = "Empty query string";
            return false;
        }

        m_pImpl->preparedQuery = query;
        m_pImpl->isPrepared = true;
        m_pImpl->isValid = true;
        m_pImpl->lastError.clear();
        return true;
    }

    bool ModernSQLStatement::Execute() {
        if (!m_pImpl->isPrepared) {
            m_pImpl->lastError = "Statement not prepared";
            return false;
        }

        m_pImpl->startTime = std::chrono::steady_clock::now();
        
        // Simulate SQL execution
        try {
            // In a real implementation, this would execute the SQL statement
            // For now, we simulate successful execution
            m_pImpl->hasData = true;
            m_pImpl->resultValue = 1000; // Simulated cash amount
            
            auto endTime = std::chrono::steady_clock::now();
            m_pImpl->executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - m_pImpl->startTime);
            
            return true;
        } catch (const std::exception& e) {
            m_pImpl->lastError = "SQL execution failed: " + std::string(e.what());
            return false;
        }
    }

    bool ModernSQLStatement::Fetch() {
        if (!m_pImpl->isValid) {
            m_pImpl->lastError = "Invalid statement";
            return false;
        }

        // Simulate fetching data
        return m_pImpl->hasData;
    }

    bool ModernSQLStatement::GetData(int column, uint32_t& value) {
        if (!m_pImpl->hasData) {
            m_pImpl->lastError = "No data available";
            return false;
        }

        if (column != 1) {
            m_pImpl->lastError = "Invalid column index";
            return false;
        }

        value = m_pImpl->resultValue;
        return true;
    }

    void ModernSQLStatement::CloseCursor() {
        m_pImpl->hasData = false;
        m_pImpl->isValid = false;
    }

    bool ModernSQLStatement::IsValid() const {
        return m_pImpl->isValid;
    }

    std::string ModernSQLStatement::GetLastError() const {
        return m_pImpl->lastError;
    }

    void ModernSQLStatement::SetTimeout(std::chrono::milliseconds timeout) {
        m_pImpl->timeout = timeout;
    }

    std::chrono::milliseconds ModernSQLStatement::GetExecutionTime() const {
        return m_pImpl->executionTime;
    }

    // CRFCashItemDatabase implementation

    CRFCashItemDatabase::CRFCashItemDatabase() {
        m_supportedRegions = {AuthRegion::STANDARD, AuthRegion::JAPANESE};
        m_lastActivity.store(std::chrono::system_clock::now());
    }

    CRFCashItemDatabase::CRFCashItemDatabase(std::unique_ptr<IDatabaseConnection> connection)
        : m_connection(std::move(connection)) {
        m_supportedRegions = {AuthRegion::STANDARD, AuthRegion::JAPANESE};
        m_lastActivity.store(std::chrono::system_clock::now());
    }

    CRFCashItemDatabase::~CRFCashItemDatabase() {
        Shutdown();
    }

    CRFCashItemDatabase::CRFCashItemDatabase(CRFCashItemDatabase&& other) noexcept {
        MoveFrom(std::move(other));
    }

    CRFCashItemDatabase& CRFCashItemDatabase::operator=(CRFCashItemDatabase&& other) noexcept {
        if (this != &other) {
            Shutdown();
            MoveFrom(std::move(other));
        }
        return *this;
    }

    AuthResultInfo CRFCashItemDatabase::CallProc_RFOnlineAuth_Jap(const CashAuthParams& params) {
        if (!ValidateParameters(params)) {
            AuthResultInfo result(AuthResult::INVALID_PARAMETER);
            result.errorMessage = "Invalid parameters for Japanese authentication";
            return result;
        }

        return ExecuteAuthProcedure("SP_RF_CHK_GEM_GAMEON", params);
    }

    AuthResultInfo CRFCashItemDatabase::CallProc_RFOnlineAuth(const CashAuthParams& params) {
        if (!ValidateParameters(params)) {
            AuthResultInfo result(AuthResult::INVALID_PARAMETER);
            result.errorMessage = "Invalid parameters for standard authentication";
            return result;
        }

        return ExecuteAuthProcedure("prc_rfonline_auth", params);
    }

    AuthResultInfo CRFCashItemDatabase::AuthenticateUser(const CashAuthParams& params) {
        switch (params.region) {
            case AuthRegion::JAPANESE:
                return CallProc_RFOnlineAuth_Jap(params);
            case AuthRegion::STANDARD:
            default:
                return CallProc_RFOnlineAuth(params);
        }
    }

    bool CRFCashItemDatabase::Initialize(const std::string& connectionString) {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        
        try {
            // Create statement
            m_statement = std::make_unique<ModernSQLStatement>();
            
            // In a real implementation, create database connection here
            // m_connection = CreateDatabaseConnection(connectionString);
            
            m_isInitialized = true;
            m_lastActivity.store(std::chrono::system_clock::now());
            
            if (m_logger && m_loggingEnabled) {
                m_logger->Log("CRFCashItemDatabase initialized successfully");
            }
            
            return true;
        } catch (const std::exception& e) {
            if (m_logger) {
                m_logger->ErrorLogFormat("Failed to initialize database: %s", e.what());
            }
            return false;
        }
    }

    bool CRFCashItemDatabase::IsConnected() const {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        if (m_connection) {
            return m_connection->IsConnected();
        } else {
            return m_isInitialized.load();
        }
    }

    bool CRFCashItemDatabase::Reconnect() {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        
        if (m_connection) {
            bool success = m_connection->Reconnect();
            if (success) {
                m_lastActivity.store(std::chrono::system_clock::now());
                if (m_logger && m_loggingEnabled) {
                    m_logger->Log("Database reconnection successful");
                }
            }
            return success;
        }
        
        return m_isInitialized;
    }

    void CRFCashItemDatabase::Disconnect() {
        std::lock_guard<std::mutex> lock(m_connectionMutex);
        
        CleanupStatement();
        
        if (m_connection) {
            m_connection->Disconnect();
        }
        
        if (m_logger && m_loggingEnabled) {
            m_logger->Log("Database disconnected");
        }
    }

    void CRFCashItemDatabase::Shutdown() {
        Disconnect();
        m_isInitialized = false;
        
        if (m_logger && m_loggingEnabled) {
            m_logger->Log("CRFCashItemDatabase shutdown complete");
        }
    }

    void CRFCashItemDatabase::SetDatabaseLogger(std::unique_ptr<IDatabaseLogger> logger) {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_logger = std::move(logger);
    }

    void CRFCashItemDatabase::SetLoggingEnabled(bool enabled) {
        m_loggingEnabled = enabled;
    }

    bool CRFCashItemDatabase::IsLoggingEnabled() const {
        return m_loggingEnabled;
    }

    void CRFCashItemDatabase::SetDefaultTimeout(std::chrono::milliseconds timeout) {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_defaultTimeout = timeout;
    }

    std::chrono::milliseconds CRFCashItemDatabase::GetDefaultTimeout() const {
        std::lock_guard<std::mutex> lock(m_configMutex);
        return m_defaultTimeout;
    }

    uint32_t CRFCashItemDatabase::GetTotalAuthAttempts() const {
        return m_totalAuthAttempts.load();
    }

    uint32_t CRFCashItemDatabase::GetSuccessfulAuths() const {
        return m_successfulAuths.load();
    }

    uint32_t CRFCashItemDatabase::GetFailedAuths() const {
        return m_failedAuths.load();
    }

    double CRFCashItemDatabase::GetSuccessRate() const {
        uint32_t total = GetTotalAuthAttempts();
        if (total == 0) return 0.0;
        return static_cast<double>(GetSuccessfulAuths()) / total * 100.0;
    }

    std::chrono::system_clock::time_point CRFCashItemDatabase::GetLastActivity() const {
        return m_lastActivity.load();
    }

    void CRFCashItemDatabase::SetSupportedRegions(const std::vector<AuthRegion>& regions) {
        std::lock_guard<std::mutex> lock(m_configMutex);
        m_supportedRegions = regions;
    }

    std::vector<AuthRegion> CRFCashItemDatabase::GetSupportedRegions() const {
        std::lock_guard<std::mutex> lock(m_configMutex);
        return m_supportedRegions;
    }

    bool CRFCashItemDatabase::IsRegionSupported(AuthRegion region) const {
        std::lock_guard<std::mutex> lock(m_configMutex);
        return std::find(m_supportedRegions.begin(), m_supportedRegions.end(), region) != m_supportedRegions.end();
    }

    std::string CRFCashItemDatabase::ToString() const {
        std::ostringstream oss;
        oss << "CRFCashItemDatabase{";
        oss << "Initialized: " << (m_isInitialized ? "true" : "false") << ", ";
        oss << "Connected: " << (IsConnected() ? "true" : "false") << ", ";
        oss << "LoggingEnabled: " << (m_loggingEnabled ? "true" : "false") << ", ";
        oss << "TotalAttempts: " << GetTotalAuthAttempts() << ", ";
        oss << "SuccessRate: " << std::fixed << std::setprecision(2) << GetSuccessRate() << "%, ";
        oss << "SupportedRegions: " << m_supportedRegions.size();
        oss << "}";
        return oss.str();
    }

    // Static utility methods

    std::string CRFCashItemDatabase::AuthResultToString(AuthResult result) {
        switch (result) {
            case AuthResult::SUCCESS: return "Success";
            case AuthResult::ERROR: return "Error";
            case AuthResult::NO_DATA: return "NoData";
            case AuthResult::CONNECTION_FAILED: return "ConnectionFailed";
            case AuthResult::INVALID_PARAMETER: return "InvalidParameter";
            case AuthResult::SQL_ERROR: return "SQLError";
            case AuthResult::TIMEOUT: return "Timeout";
            default: return "Unknown";
        }
    }

    std::string CRFCashItemDatabase::AuthRegionToString(AuthRegion region) {
        switch (region) {
            case AuthRegion::STANDARD: return "Standard";
            case AuthRegion::JAPANESE: return "Japanese";
            case AuthRegion::KOREAN: return "Korean";
            case AuthRegion::CHINESE: return "Chinese";
            case AuthRegion::EUROPEAN: return "European";
            default: return "Unknown";
        }
    }

    AuthRegion CRFCashItemDatabase::StringToAuthRegion(const std::string& regionStr) {
        if (regionStr == "Standard") return AuthRegion::STANDARD;
        if (regionStr == "Japanese") return AuthRegion::JAPANESE;
        if (regionStr == "Korean") return AuthRegion::KOREAN;
        if (regionStr == "Chinese") return AuthRegion::CHINESE;
        if (regionStr == "European") return AuthRegion::EUROPEAN;
        return AuthRegion::UNKNOWN;
    }

    bool CRFCashItemDatabase::IsValidAccountId(const std::string& accountId) {
        if (accountId.empty() || accountId.length() > 64) {
            return false;
        }

        // Check for valid characters (alphanumeric and some special chars)
        for (char c : accountId) {
            if (!std::isalnum(c) && c != '_' && c != '-' && c != '.') {
                return false;
            }
        }

        return true;
    }

    // Private helper methods

    AuthResultInfo CRFCashItemDatabase::ExecuteAuthProcedure(const std::string& procedureName,
                                                            const CashAuthParams& params) {
        auto startTime = std::chrono::steady_clock::now();
        AuthResultInfo result;

        try {
            // Update statistics
            UpdateStatistics(false); // Will be updated to true if successful

            // Check connection
            if (!IsConnected() && !Reconnect()) {
                result.result = AuthResult::CONNECTION_FAILED;
                result.errorMessage = "Database connection failed";
                LogAuthAttempt(params, result);
                return result;
            }

            // Build query based on procedure
            std::string query;
            if (procedureName == "SP_RF_CHK_GEM_GAMEON") {
                query = BuildJapaneseQuery(params.accountId);
            } else {
                query = BuildStandardQuery(params.accountId);
            }

            result.sqlQuery = query;

            // Log query if enabled
            if (m_logger && params.enableLogging && m_loggingEnabled) {
                m_logger->Log(query);
            }

            // Prepare and execute statement
            if (!m_statement || !m_statement->Prepare(query)) {
                result.result = AuthResult::SQL_ERROR;
                result.errorMessage = "Failed to prepare SQL statement";
                LogAuthAttempt(params, result);
                return result;
            }

            // Set timeout
            if (auto modernStmt = dynamic_cast<ModernSQLStatement*>(m_statement.get())) {
                modernStmt->SetTimeout(params.timeout);
            }

            // Execute query
            if (!m_statement->Execute()) {
                result.result = AuthResult::SQL_ERROR;
                result.errorMessage = "Failed to execute SQL statement: " + m_statement->GetLastError();
                LogAuthAttempt(params, result);
                return result;
            }

            // Fetch result
            if (!m_statement->Fetch()) {
                result.result = AuthResult::NO_DATA;
                result.errorMessage = "No data returned from query";
                CleanupStatement();
                LogAuthAttempt(params, result);
                return result;
            }

            // Get cash amount
            uint32_t cashAmount = 0;
            if (!m_statement->GetData(1, cashAmount)) {
                result.result = AuthResult::SQL_ERROR;
                result.errorMessage = "Failed to retrieve cash amount: " + m_statement->GetLastError();
                CleanupStatement();
                LogAuthAttempt(params, result);
                return result;
            }

            // Success
            result.result = AuthResult::SUCCESS;
            result.cashAmount = cashAmount;

            // Update statistics for success
            m_successfulAuths++;

            CleanupStatement();

            // Calculate execution time
            auto endTime = std::chrono::steady_clock::now();
            result.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

            // Update last activity
            m_lastActivity.store(std::chrono::system_clock::now());

            // Log success
            if (m_logger && params.enableLogging && m_loggingEnabled) {
                m_logger->LogFormat("%s Success - Cash Amount: %u", query.c_str(), cashAmount);
            }

            LogAuthAttempt(params, result);
            return result;

        } catch (const std::exception& e) {
            result.result = AuthResult::ERROR;
            result.errorMessage = "Exception during authentication: " + std::string(e.what());
            CleanupStatement();
            LogAuthAttempt(params, result);
            return result;
        }
    }

    std::string CRFCashItemDatabase::BuildJapaneseQuery(const std::string& accountId) const {
        // Original Japanese query: SP_RF_CHK_GEM_GAMEON
        std::ostringstream oss;
        oss << "declare @out_amount int exec dbo.SP_RF_CHK_GEM_GAMEON @uid = '"
            << accountId << "', @s_amount = @out_amount output select @out_amount";
        return oss.str();
    }

    std::string CRFCashItemDatabase::BuildStandardQuery(const std::string& accountId) const {
        // Original standard query: prc_rfonline_auth
        std::ostringstream oss;
        oss << "declare @out_amount int exec prc_rfonline_auth '"
            << accountId << "', @s_amount = @out_amount output select @out_amount";
        return oss.str();
    }

    bool CRFCashItemDatabase::ValidateParameters(const CashAuthParams& params) const {
        if (!params.IsValid()) {
            return false;
        }

        if (!IsValidAccountId(params.accountId)) {
            return false;
        }

        if (!IsRegionSupported(params.region)) {
            return false;
        }

        return true;
    }

    void CRFCashItemDatabase::UpdateStatistics(bool success) {
        m_totalAuthAttempts++;
        if (!success) {
            m_failedAuths++;
        }
    }

    void CRFCashItemDatabase::LogAuthAttempt(const CashAuthParams& params, const AuthResultInfo& result) {
        if (!m_logger || !params.enableLogging || !m_loggingEnabled) {
            return;
        }

        if (result.IsSuccess()) {
            m_logger->LogFormat("Auth Success: %s - Region: %s - Amount: %u - Time: %lldms",
                               params.accountId.c_str(),
                               AuthRegionToString(params.region).c_str(),
                               result.cashAmount,
                               result.executionTime.count());
        } else {
            m_logger->ErrorLogFormat("Auth Failed: %s - Region: %s - Error: %s - Time: %lldms",
                                    params.accountId.c_str(),
                                    AuthRegionToString(params.region).c_str(),
                                    result.errorMessage.c_str(),
                                    result.executionTime.count());
        }
    }

    AuthResult CRFCashItemDatabase::HandleSQLError(int16_t sqlResult, const std::string& query, const std::string& operation) {
        if (sqlResult == 0 || sqlResult == 1) {
            return AuthResult::SUCCESS;
        }

        if (sqlResult == 100) {
            return AuthResult::NO_DATA;
        }

        // Log error
        if (m_logger && m_loggingEnabled) {
            m_logger->ErrorLogFormat("SQL Error in %s: Code %d, Query: %s",
                                    operation.c_str(), sqlResult, query.c_str());
        }

        return AuthResult::SQL_ERROR;
    }

    void CRFCashItemDatabase::CleanupStatement() {
        if (m_statement) {
            m_statement->CloseCursor();
        }
    }

    void CRFCashItemDatabase::MoveFrom(CRFCashItemDatabase&& other) noexcept {
        m_connection = std::move(other.m_connection);
        m_statement = std::move(other.m_statement);
        m_logger = std::move(other.m_logger);
        m_defaultTimeout = other.m_defaultTimeout;
        m_supportedRegions = std::move(other.m_supportedRegions);
        m_totalAuthAttempts.store(other.m_totalAuthAttempts.load());
        m_successfulAuths.store(other.m_successfulAuths.load());
        m_failedAuths.store(other.m_failedAuths.load());
        m_lastActivity.store(other.m_lastActivity.load());
        m_isInitialized.store(other.m_isInitialized.load());
        m_loggingEnabled.store(other.m_loggingEnabled.load());

        // Reset other object
        other.m_isInitialized = false;
        other.m_loggingEnabled = true;
        other.m_totalAuthAttempts = 0;
        other.m_successfulAuths = 0;
        other.m_failedAuths = 0;
    }

    // CRFCashItemDatabaseManager implementation

    CRFCashItemDatabaseManager::CRFCashItemDatabaseManager() = default;

    CRFCashItemDatabaseManager::~CRFCashItemDatabaseManager() {
        ShutdownAll();
    }

    bool CRFCashItemDatabaseManager::RegisterDatabase(AuthRegion region, std::unique_ptr<CRFCashItemDatabase> database) {
        if (!database || region == AuthRegion::UNKNOWN) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_managerMutex);

        // Configure database with global settings
        database->SetLoggingEnabled(m_globalLoggingEnabled);
        database->SetDefaultTimeout(m_globalTimeout);

        m_databases[region] = std::move(database);
        return true;
    }

    CRFCashItemDatabase* CRFCashItemDatabaseManager::GetDatabase(AuthRegion region) const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        auto it = m_databases.find(region);
        return it != m_databases.end() ? it->second.get() : nullptr;
    }

    bool CRFCashItemDatabaseManager::RemoveDatabase(AuthRegion region) {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        auto it = m_databases.find(region);
        if (it != m_databases.end()) {
            it->second->Shutdown();
            m_databases.erase(it);
            return true;
        }
        return false;
    }

    AuthResultInfo CRFCashItemDatabaseManager::AuthenticateUser(const CashAuthParams& params) {
        CRFCashItemDatabase* database = GetDatabase(params.region);
        if (!database) {
            AuthResultInfo result(AuthResult::ERROR);
            result.errorMessage = "No database registered for region: " + CRFCashItemDatabase::AuthRegionToString(params.region);
            return result;
        }

        return database->AuthenticateUser(params);
    }

    AuthResultInfo CRFCashItemDatabaseManager::AuthenticateUserByRegion(AuthRegion region, const CashAuthParams& params) {
        CashAuthParams modifiedParams = params;
        modifiedParams.region = region;
        return AuthenticateUser(modifiedParams);
    }

    void CRFCashItemDatabaseManager::SetLoggingEnabled(bool enabled) {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        m_globalLoggingEnabled = enabled;

        for (auto& pair : m_databases) {
            if (pair.second) {
                pair.second->SetLoggingEnabled(enabled);
            }
        }
    }

    void CRFCashItemDatabaseManager::SetDefaultTimeout(std::chrono::milliseconds timeout) {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        m_globalTimeout = timeout;

        for (auto& pair : m_databases) {
            if (pair.second) {
                pair.second->SetDefaultTimeout(timeout);
            }
        }
    }

    bool CRFCashItemDatabaseManager::ReconnectAll() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        bool allSuccess = true;

        for (auto& pair : m_databases) {
            if (pair.second && !pair.second->Reconnect()) {
                allSuccess = false;
            }
        }

        return allSuccess;
    }

    void CRFCashItemDatabaseManager::ShutdownAll() {
        std::lock_guard<std::mutex> lock(m_managerMutex);

        for (auto& pair : m_databases) {
            if (pair.second) {
                pair.second->Shutdown();
            }
        }

        m_databases.clear();
    }

    uint32_t CRFCashItemDatabaseManager::GetTotalDatabases() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        return static_cast<uint32_t>(m_databases.size());
    }

    uint32_t CRFCashItemDatabaseManager::GetConnectedDatabases() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        uint32_t connected = 0;

        for (const auto& pair : m_databases) {
            if (pair.second && pair.second->IsConnected()) {
                connected++;
            }
        }

        return connected;
    }

    std::unordered_map<AuthRegion, uint32_t> CRFCashItemDatabaseManager::GetAuthStatsByRegion() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        std::unordered_map<AuthRegion, uint32_t> stats;

        for (const auto& pair : m_databases) {
            if (pair.second) {
                stats[pair.first] = pair.second->GetTotalAuthAttempts();
            }
        }

        return stats;
    }

    std::vector<AuthRegion> CRFCashItemDatabaseManager::GetRegisteredRegions() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        std::vector<AuthRegion> regions;
        regions.reserve(m_databases.size());

        for (const auto& pair : m_databases) {
            regions.push_back(pair.first);
        }

        return regions;
    }

    std::string CRFCashItemDatabaseManager::GetStatusReport() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        std::ostringstream oss;
        oss << "CRFCashItemDatabaseManager Status Report:\n";
        oss << "Total databases: " << m_databases.size() << "\n";
        oss << "Connected databases: " << GetConnectedDatabases() << "\n";
        oss << "Global timeout: " << m_globalTimeout.count() << "ms\n";
        oss << "Global logging: " << (m_globalLoggingEnabled ? "enabled" : "disabled") << "\n\n";

        for (const auto& pair : m_databases) {
            if (pair.second) {
                oss << "- " << CRFCashItemDatabase::AuthRegionToString(pair.first) << ": ";
                oss << "Connected=" << (pair.second->IsConnected() ? "Yes" : "No") << ", ";
                oss << "Attempts=" << pair.second->GetTotalAuthAttempts() << ", ";
                oss << "Success Rate=" << std::fixed << std::setprecision(1) << pair.second->GetSuccessRate() << "%\n";
            }
        }

        return oss.str();
    }

    // CRFCashItemDatabaseFactory implementation

    std::unique_ptr<CRFCashItemDatabase> CRFCashItemDatabaseFactory::CreateDatabase(AuthRegion region) {
        auto database = std::make_unique<CRFCashItemDatabase>();

        // Set supported regions based on the target region
        std::vector<AuthRegion> supportedRegions;
        switch (region) {
            case AuthRegion::JAPANESE:
                supportedRegions = {AuthRegion::JAPANESE};
                break;
            case AuthRegion::STANDARD:
            default:
                supportedRegions = {AuthRegion::STANDARD, AuthRegion::EUROPEAN, AuthRegion::KOREAN, AuthRegion::CHINESE};
                break;
        }

        database->SetSupportedRegions(supportedRegions);
        return database;
    }

    std::unique_ptr<CRFCashItemDatabase> CRFCashItemDatabaseFactory::CreateDatabase(const std::string& connectionString, AuthRegion region) {
        auto database = CreateDatabase(region);

        if (database->Initialize(connectionString)) {
            return database;
        }

        return nullptr;
    }

    std::unique_ptr<CRFCashItemDatabaseManager> CRFCashItemDatabaseFactory::CreateManager() {
        return std::make_unique<CRFCashItemDatabaseManager>();
    }

    std::unique_ptr<CRFCashItemDatabaseManager> CRFCashItemDatabaseFactory::CreateStandardManager() {
        auto manager = std::make_unique<CRFCashItemDatabaseManager>();

        // Register standard databases
        auto standardDb = CreateDatabase(AuthRegion::STANDARD);
        auto japaneseDb = CreateDatabase(AuthRegion::JAPANESE);

        if (standardDb) {
            manager->RegisterDatabase(AuthRegion::STANDARD, std::move(standardDb));
        }

        if (japaneseDb) {
            manager->RegisterDatabase(AuthRegion::JAPANESE, std::move(japaneseDb));
        }

        return manager;
    }

    std::unique_ptr<CRFCashItemDatabaseManager> CRFCashItemDatabaseFactory::CreateTestManager() {
        auto manager = std::make_unique<CRFCashItemDatabaseManager>();

        // Create test databases with shorter timeouts
        auto testDb = CreateDatabase(AuthRegion::STANDARD);
        if (testDb) {
            testDb->SetDefaultTimeout(std::chrono::milliseconds(5000)); // 5 second timeout for testing
            testDb->SetLoggingEnabled(true);
            manager->RegisterDatabase(AuthRegion::STANDARD, std::move(testDb));
        }

        return manager;
    }

    std::unique_ptr<CRFCashItemDatabaseManager> CRFCashItemDatabaseFactory::CreateProductionManager() {
        auto manager = std::make_unique<CRFCashItemDatabaseManager>();

        // Register all regional databases for production
        std::vector<AuthRegion> regions = {
            AuthRegion::STANDARD,
            AuthRegion::JAPANESE,
            AuthRegion::KOREAN,
            AuthRegion::CHINESE,
            AuthRegion::EUROPEAN
        };

        for (AuthRegion region : regions) {
            auto database = CreateDatabase(region);
            if (database) {
                database->SetDefaultTimeout(std::chrono::milliseconds(30000)); // 30 second timeout for production
                database->SetLoggingEnabled(true);
                manager->RegisterDatabase(region, std::move(database));
            }
        }

        return manager;
    }

} // namespace NexusProtection::Authentication
