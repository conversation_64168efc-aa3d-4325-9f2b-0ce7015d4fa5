/*
 * Function: j_??8?$_Vector_const_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x140012D69
 */

bool __fastcall std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator==(std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Right)
{
  return std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator==(
           this,
           _Right);
}
