/*
 * Function: ?AddComplete@CNormalGuildBattle@GUILD_BATTLE@@QEAAXE@Z
 * Address: 0x1403E3910
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::AddComplete(GUILD_BATTLE::CNormalGuildBattle *this, char byRet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CGuild *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  CGuild *pSrcGuild; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v7; // [sp+40h] [bp+8h]@1
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byRet;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pSrcGuild = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v7->m_k1P);
  v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v7->m_k2P);
  CGuild::AddScheduleComplete(v4, v8, pSrcGuild);
  GUILD_BATTLE::CGuildBattleStateList::SetWait((GUILD_BATTLE::CGuildBattleStateList *)&v7->m_pkStateList->vfptr);
  GUILD_BATTLE::CNormalGuildBattle::SendWebAddScheduleInfo(v7);
}
