/*
 * Function: ?dev_up_forcemastery@CPlayer@@QEAA_NH@Z
 * Address: 0x1400BBB40
 */

char __fastcall CPlayer::dev_up_forcemastery(CPlayer *this, int nCum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+40h] [bp+8h]@1
  int dwNewData; // [sp+48h] [bp+10h]@1

  dwNewData = nCum;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 24; ++j )
    CPlayer::Emb_UpdateStat(v7, j + 52, dwNewData, 0);
  return 1;
}
