/*
 * Function: ?FindItem@CUnmannedTraderScheduler@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@EK@Z
 * Address: 0x1403947E0
 */

std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__fastcall CUnmannedTraderScheduler::FindItem(CUnmannedTraderScheduler *this, std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *result, char byType, unsigned int dwRegistSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSchedule *v6; // rax@6
  char v7; // al@6
  CUnmannedTraderSchedule *v8; // rax@7
  __int64 v10; // [sp+0h] [bp-B8h]@1
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > resulta; // [sp+28h] [bp-90h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > v12; // [sp+48h] [bp-70h]@8
  bool v13; // [sp+60h] [bp-58h]@5
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > v14; // [sp+68h] [bp-50h]@5
  int v15; // [sp+80h] [bp-38h]@4
  __int64 v16; // [sp+88h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v17; // [sp+90h] [bp-28h]@5
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *_Right; // [sp+98h] [bp-20h]@5
  int v19; // [sp+A0h] [bp-18h]@6
  CUnmannedTraderScheduler *v20; // [sp+C0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v21; // [sp+C8h] [bp+10h]@1
  char v22; // [sp+D0h] [bp+18h]@1
  unsigned int v23; // [sp+D8h] [bp+20h]@1

  v23 = dwRegistSerial;
  v22 = byType;
  v21 = result;
  v20 = this;
  v4 = &v10;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  v15 = 0;
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::begin(&v20->m_veckSchdule, &resulta);
  while ( 1 )
  {
    v17 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::end(&v20->m_veckSchdule, &v14);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)v17;
    v13 = std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator!=(
            (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&resulta._Mycont,
            (std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v17->_Mycont);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v14);
    if ( !v13 )
      break;
    v19 = (unsigned __int8)v22;
    v6 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&resulta);
    v7 = CUnmannedTraderSchedule::GetType(v6);
    if ( v19 == (unsigned __int8)v7 )
    {
      v8 = std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator*(&resulta);
      if ( v23 == CUnmannedTraderSchedule::GetRegistSerial(v8) )
        break;
    }
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator++(
      &resulta,
      &v12,
      0);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&v12);
  }
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    v21,
    &resulta);
  v15 |= 1u;
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&resulta);
  return v21;
}
