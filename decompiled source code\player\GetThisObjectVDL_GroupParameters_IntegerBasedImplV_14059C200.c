/*
 * Function: ??$GetThisObject@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@@NameValuePairs@CryptoPP@@QEBA_NAEAV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@1@@Z
 * Address: 0x14059C200
 */

char __fastcall CryptoPP::NameValuePairs::GetThisObject<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rax@1
  __int64 v4; // rax@1
  char v5; // ST20_1@1
  char v7; // [sp+28h] [bp-A0h]@1
  unsigned __int8 v8; // [sp+58h] [bp-70h]@1
  char v9; // [sp+60h] [bp-68h]@1
  __int64 v10; // [sp+90h] [bp-38h]@1
  const char *v11; // [sp+98h] [bp-30h]@1
  __int64 v12; // [sp+A0h] [bp-28h]@1
  __int64 v13; // [sp+A8h] [bp-20h]@1
  __int64 v14; // [sp+B0h] [bp-18h]@1
  __int64 v15; // [sp+B8h] [bp-10h]@1
  __int64 v16; // [sp+D0h] [bp+8h]@1
  __int64 v17; // [sp+D8h] [bp+10h]@1

  v17 = a2;
  v16 = a1;
  v10 = -2i64;
  memset(&v8, 0, sizeof(v8));
  v11 = type_info::_name_internal_method(
          &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
          (struct __type_info_node *)&__type_info_root_node);
  LODWORD(v2) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
                  &v7,
                  "ThisObject:",
                  v8);
  v12 = v2;
  v13 = v2;
  LODWORD(v3) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(&v9, v2, v11);
  v14 = v3;
  v15 = v3;
  LODWORD(v4) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v3);
  v5 = CryptoPP::NameValuePairs::GetValue<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(
         v16,
         v4,
         v17);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v9);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v7);
  return v5;
}
