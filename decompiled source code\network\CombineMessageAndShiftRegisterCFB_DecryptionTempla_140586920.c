/*
 * Function: ?CombineMessageAndShiftRegister@?$CFB_DecryptionTemplate@V?$AbstractPolicyHolder@VCFB_CipherAbstractPolicy@CryptoPP@@V?$SimpleKeyedTransformation@VStreamTransformation@CryptoPP@@@2@@CryptoPP@@@CryptoPP@@EEAAXPEAE0PEBE_K@Z
 * Address: 0x140586920
 */

__int64 __fastcall CryptoPP::CFB_DecryptionTemplate<CryptoPP::AbstractPolicyHolder<CryptoPP::CFB_CipherAbstractPolicy,CryptoPP::SimpleKeyedTransformation<CryptoPP::StreamTransformation>>>::CombineMessageAndShiftRegister(__int64 a1, __int64 a2, __int64 a3, __int64 a4, unsigned __int64 a5)
{
  __int64 result; // rax@2
  char v6; // ST04_1@3
  unsigned int i; // [sp+0h] [bp-18h]@1

  for ( i = 0; ; ++i )
  {
    result = i;
    if ( i >= a5 )
      break;
    v6 = *(_BYTE *)(a4 + i);
    *(_BYTE *)(a2 + i) = v6 ^ *(_BYTE *)(a3 + i);
    *(_BYTE *)(a3 + i) = v6;
  }
  return result;
}
