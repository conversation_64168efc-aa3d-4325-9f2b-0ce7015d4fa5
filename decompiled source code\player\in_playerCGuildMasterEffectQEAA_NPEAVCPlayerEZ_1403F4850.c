/*
 * Function: ?in_player@CGuildMasterEffect@@QEAA_NPEAVCPlayer@@E@Z
 * Address: 0x1403F4850
 */

char __fastcall CGuildMasterEffect::in_player(CGuildMasterEffect *this, CPlayer *pP, char byGrade)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-38h]@1
  CGuildMasterEffect *v7; // [sp+40h] [bp+8h]@1
  CPlayer *pPa; // [sp+48h] [bp+10h]@1
  char v9; // [sp+50h] [bp+18h]@1

  v9 = byGrade;
  pPa = pP;
  v7 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pP
    && byGrade
    && (unsigned __int8)byGrade >= (signed int)v7->m_byAdjustableGrade
    && (signed int)(unsigned __int8)byGrade <= 8 )
  {
    if ( pP->m_Param.m_pGuild )
    {
      if ( pP->m_Param.m_byClassInGuild == 2 )
      {
        if ( (unsigned __int8)CGuild::GetGrade(pP->m_Param.m_pGuild) == (unsigned __int8)byGrade )
        {
          CGuildMasterEffect::adjust_effect(v7, pPa, v9, 1);
          CGuildMasterEffect::show_to_all(v7, pPa, 0, v9, 0);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
