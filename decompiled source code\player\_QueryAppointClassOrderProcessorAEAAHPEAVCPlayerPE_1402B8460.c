/*
 * Function: ?_QueryAppoint@ClassOrderProcessor@@AEAAHPEAVCPlayer@@PEAD@Z
 * Address: 0x1402B8460
 */

signed __int64 __fastcall ClassOrderProcessor::_QueryAppoint(ClassOrderProcessor *this, CPlayer *pOne, char *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@4
  unsigned int v6; // eax@4
  signed __int64 result; // rax@5
  __int64 v8; // rax@6
  char v9; // al@8
  __int64 v10; // [sp+0h] [bp-68h]@1
  char *pwszAvatorName; // [sp+20h] [bp-48h]@8
  char *v12; // [sp+30h] [bp-38h]@6
  char v13; // [sp+38h] [bp-30h]@8
  CPlayer *pUser; // [sp+40h] [bp-28h]@8
  int v15; // [sp+48h] [bp-20h]@8
  unsigned int v16; // [sp+4Ch] [bp-1Ch]@4
  int v17; // [sp+50h] [bp-18h]@4
  ClassOrderProcessor *v18; // [sp+70h] [bp+8h]@1
  CPlayer *v19; // [sp+78h] [bp+10h]@1
  char *v20; // [sp+80h] [bp+18h]@1

  v20 = pData;
  v19 = pOne;
  v18 = this;
  v3 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = CPlayerDB::GetCharSerial(&pOne->m_Param);
  v17 = CPlayerDB::GetRaceCode(&v19->m_Param);
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v17, 0);
  if ( v16 == v6 )
  {
    v12 = v20;
    v8 = (unsigned __int8)*v20;
    if ( (signed int)(unsigned __int8)*v20 < 4 )
    {
      v13 = *v12 + 5;
      pUser = (CPlayer *)CMainThread::GetCharW(&g_Main, v12 + 1);
      v9 = CPlayerDB::GetRaceCode(&v19->m_Param);
      v15 = ClassOrderProcessor::_CheckUserInfo(v18, v9, v13, pUser);
      pwszAvatorName = v12 + 1;
      ClassOrderProcessor::SendMsg_QueryAppointResult(v18, v19->m_id.wIndex, v15, *v12, v12 + 1);
      result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 20i64;
  }
  return result;
}
