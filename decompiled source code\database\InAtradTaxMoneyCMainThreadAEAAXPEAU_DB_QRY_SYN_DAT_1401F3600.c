/*
 * Function: ?InAtradTaxMoney@CMainThread@@AEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3600
 */

void __fastcall CMainThread::InAtradTaxMoney(CMainThread *this, _DB_QRY_SYN_DATA *p)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderTaxRateManager *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  char *pdata; // [sp+28h] [bp-10h]@4

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = p->m_sData;
  pdata = p->m_sData;
  v4 = CUnmannedTraderTaxRateManager::Instance();
  CUnmannedTraderTaxRateManager::DQSCompleteInAtradTaxMoney(v4, *v6, pdata);
}
