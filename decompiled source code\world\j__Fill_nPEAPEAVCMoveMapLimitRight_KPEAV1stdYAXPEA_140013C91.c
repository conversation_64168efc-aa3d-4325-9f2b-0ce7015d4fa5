/*
 * Function: j_??$_Fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013C91
 */

void __fastcall std::_Fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *>(_First, _Count, _Val, __formal);
}
