/*
 * Function: ?SetDamage@CMonster@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x1401462D0
 */

__int64 __fastcall CMonster::SetDamage(CMonster *this, __int64 nDamage, CCharacter *pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *v10; // rax@24
  __int64 v12; // [sp+0h] [bp-58h]@1
  int v13; // [sp+40h] [bp-18h]@20
  CMonster *v14; // [sp+60h] [bp+8h]@1
  signed int nDamagea; // [sp+68h] [bp+10h]@1
  CCharacter *pOri; // [sp+70h] [bp+18h]@1

  pOri = pDst;
  nDamagea = nDamage;
  v14 = this;
  v8 = &v12;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( pDst )
    CMonster::AttackObject(v14, nDamage, (CGameObject *)&pDst->vfptr);
  if ( pOri && nDamagea >= 0 )
  {
    if ( pOri->m_ObjID.m_byID )
    {
      if ( pOri->m_ObjID.m_byID == 3 )
      {
        if ( *(_QWORD *)&pOri[1].m_bLive )
        {
          CLootingMgr::PushDamage(&v14->m_LootMgr, *(CPlayer **)&pOri[1].m_bLive, nDamagea);
          CMonsterAggroMgr::SetAggro(&v14->m_AggroMgr, pOri, nDamagea, nAttackType, dwAttackSerial, 0, 0);
        }
      }
      else if ( pOri->m_ObjID.m_byID == 4 )
      {
        if ( pOri[1].m_pRecordSet )
        {
          CLootingMgr::PushDamage(&v14->m_LootMgr, (CPlayer *)pOri[1].m_pRecordSet, nDamagea);
          CMonsterAggroMgr::SetAggro(&v14->m_AggroMgr, pOri, nDamagea, nAttackType, dwAttackSerial, 0, 0);
        }
        else
        {
          CLootingMgr::PushDamage(&v14->m_LootMgr, &sPlayerDum, nDamagea);
        }
      }
    }
    else
    {
      CLootingMgr::PushDamage(&v14->m_LootMgr, (CPlayer *)pOri, nDamagea);
      CMonsterAggroMgr::SetAggro(&v14->m_AggroMgr, pOri, nDamagea, nAttackType, dwAttackSerial, 0, 0);
    }
  }
  if ( nDamagea >= 1 )
  {
    if ( v14->m_nHP - nDamagea <= 0 )
      v13 = 0;
    else
      v13 = v14->m_nHP - nDamagea;
    v14->m_nHP = v13;
  }
  if ( !v14->m_nHP )
  {
    CMonster::ClearEmotionPresentation(v14);
    CMonster::CheckEventEmotionPresentation(v14, 8, 0i64);
    v10 = CLootingMgr::GetLooter(&v14->m_LootMgr, v14->m_pCurMap, v14->m_fCurPos, &sPlayerDum);
    CMonster::Destroy(v14, 0, (CGameObject *)&v10->vfptr);
    if ( pOri )
      ((void (__fastcall *)(CCharacter *, CMonster *))pOri->vfptr->RecvKillMessage)(pOri, v14);
  }
  if ( bCrt && !v14->m_pMonRec->m_bMonsterCondition )
  {
    LOBYTE(nDamage) = 1;
    (*(void (__fastcall **)(CMonster *, __int64))&v14->vfptr->gap8[0])(v14, nDamage);
  }
  Us_HFSM::SendExternMsg((Us_HFSM *)&v14->m_AI.vfptr, 0, pOri, nDamagea);
  return v14->m_nHP;
}
