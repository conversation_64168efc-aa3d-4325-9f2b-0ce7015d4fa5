/*
 * Function: ?DropGravityStone@CNormalGuildBattle@GUILD_BATTLE@@QEAAEK@Z
 * Address: 0x1403E5830
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::DropGravityStone(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwCharacSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *v6; // [sp+20h] [bp-28h]@10
  CPlayer *pkPlayer; // [sp+30h] [bp-18h]@4
  char v8; // [sp+38h] [bp-10h]@8
  GUILD_BATTLE::CNormalGuildBattle *v9; // [sp+50h] [bp+8h]@1
  unsigned int dwSerial; // [sp+58h] [bp+10h]@1

  dwSerial = dwCharacSerial;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v9->m_k1P, dwCharacSerial);
  if ( !pkPlayer )
    pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v9->m_k2P, dwSerial);
  if ( pkPlayer )
  {
    v8 = 0;
    if ( !pkPlayer->m_bTakeGravityStone
      || (v8 = GUILD_BATTLE::CNormalGuildBattleField::DropBall(v9->m_pkField, pkPlayer)) != 0 )
    {
      result = v8;
    }
    else
    {
      GUILD_BATTLE::CNormalGuildBattle::NotifyDestoryBall(v9, pkPlayer->m_dwObjSerial);
      v6 = CPlayerDB::GetCharNameW(&pkPlayer->m_Param);
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        &v9->m_kLogger,
        "CNormalGuildBattle::DropGravityStone( %u ) : (%u) m_pkField->DropBall( %s )!",
        dwSerial,
        dwSerial);
      result = 0;
    }
  }
  else
  {
    result = -111;
  }
  return result;
}
