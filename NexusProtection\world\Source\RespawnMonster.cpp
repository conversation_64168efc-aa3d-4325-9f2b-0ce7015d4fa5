#include "../Headers/RespawnMonster.h"
#include "../Headers/AddMonster.h" // For ReactObj and ReactArea
#include <algorithm>
#include <sstream>
#include <cstring>
#include <stdexcept>

namespace NexusProtection::World {

    // RespawnMonster implementation

    RespawnMonster::RespawnMonster() {
        Initialize();
    }

    RespawnMonster::RespawnMonster(const std::string& defineCode, bool callEvent)
        : m_defineCode(defineCode)
        , m_callEvent(callEvent) {
        ValidateAndTruncate();
        InitializeComponents();
        m_isInitialized = true;
    }

    RespawnMonster::RespawnMonster(const RespawnMonster& other)
        : m_reactObj(other.m_reactObj)
        , m_reactArea(other.m_reactArea)
        , m_callEvent(other.m_callEvent)
        , m_defineCode(other.m_defineCode)
        , m_isInitialized(other.m_isInitialized)
        , m_isRespawnActive(other.m_isRespawnActive)
        , m_isRespawnPending(other.m_isRespawnPending)
        , m_respawnDelay(other.m_respawnDelay)
        , m_respawnCondition(other.m_respawnCondition)
        , m_eventHandlers(other.m_eventHandlers) {
    }

    RespawnMonster& RespawnMonster::operator=(const RespawnMonster& other) {
        if (this != &other) {
            m_reactObj = other.m_reactObj;
            m_reactArea = other.m_reactArea;
            m_callEvent = other.m_callEvent;
            m_defineCode = other.m_defineCode;
            m_isInitialized = other.m_isInitialized;
            m_isRespawnActive = other.m_isRespawnActive;
            m_isRespawnPending = other.m_isRespawnPending;
            m_respawnDelay = other.m_respawnDelay;
            m_respawnCondition = other.m_respawnCondition;
            m_eventHandlers = other.m_eventHandlers;
        }
        return *this;
    }

    RespawnMonster::RespawnMonster(RespawnMonster&& other) noexcept
        : m_reactObj(std::move(other.m_reactObj))
        , m_reactArea(std::move(other.m_reactArea))
        , m_callEvent(other.m_callEvent)
        , m_defineCode(std::move(other.m_defineCode))
        , m_isInitialized(other.m_isInitialized)
        , m_isRespawnActive(other.m_isRespawnActive)
        , m_isRespawnPending(other.m_isRespawnPending)
        , m_respawnDelay(other.m_respawnDelay)
        , m_respawnCondition(std::move(other.m_respawnCondition))
        , m_eventHandlers(std::move(other.m_eventHandlers)) {
        other.m_callEvent = false;
        other.m_isInitialized = false;
        other.m_isRespawnActive = false;
        other.m_isRespawnPending = false;
        other.m_respawnDelay = 0.0f;
    }

    RespawnMonster& RespawnMonster::operator=(RespawnMonster&& other) noexcept {
        if (this != &other) {
            m_reactObj = std::move(other.m_reactObj);
            m_reactArea = std::move(other.m_reactArea);
            m_callEvent = other.m_callEvent;
            m_defineCode = std::move(other.m_defineCode);
            m_isInitialized = other.m_isInitialized;
            m_isRespawnActive = other.m_isRespawnActive;
            m_isRespawnPending = other.m_isRespawnPending;
            m_respawnDelay = other.m_respawnDelay;
            m_respawnCondition = std::move(other.m_respawnCondition);
            m_eventHandlers = std::move(other.m_eventHandlers);
            
            other.m_callEvent = false;
            other.m_isInitialized = false;
            other.m_isRespawnActive = false;
            other.m_isRespawnPending = false;
            other.m_respawnDelay = 0.0f;
        }
        return *this;
    }

    void RespawnMonster::Initialize() {
        InitializeComponents();
        m_callEvent = false;
        m_defineCode.clear();
        m_isRespawnActive = false;
        m_isRespawnPending = false;
        m_respawnDelay = 0.0f;
        m_respawnCondition.clear();
        m_eventHandlers.clear();
        m_isInitialized = true;
    }

    void RespawnMonster::Reset() {
        ResetComponents();
        Initialize();
    }

    bool RespawnMonster::IsValid() const {
        return m_isInitialized && (HasDefineCode() || m_callEvent);
    }

    void RespawnMonster::SetDefineCode(const std::string& code) {
        m_defineCode = code;
        if (m_defineCode.length() > MAX_DEFINE_CODE_LENGTH) {
            m_defineCode.resize(MAX_DEFINE_CODE_LENGTH);
        }
        NotifyRespawnStateChange();
    }

    void RespawnMonster::ProcessMonsterRespawn(const std::string& monsterType, float x, float y, float z) {
        if (!IsValid() || !m_isRespawnActive) {
            return;
        }

        // Process through reactive area
        m_reactArea.ProcessAreaEvent("monster_respawn", x, y);
        
        // Process through reactive object
        m_reactObj.ProcessSignal("monster_respawn_" + monsterType);
        
        // Trigger event if enabled
        if (m_callEvent) {
            TriggerRespawnEvent("monster_respawned");
        }
        
        m_isRespawnPending = false;
    }

    void RespawnMonster::HandleRespawnEvent(const std::string& event, const std::string& data) {
        if (!IsValid()) {
            return;
        }

        // Process through reactive object
        m_reactObj.ProcessSignal(event + "_" + data);
        
        // Trigger event if enabled
        if (m_callEvent) {
            TriggerRespawnEvent(event);
        }
    }

    void RespawnMonster::TriggerRespawnSequence() {
        if (IsValid() && !m_isRespawnPending) {
            m_isRespawnPending = true;
            m_isRespawnActive = true;
            TriggerRespawnEvent("respawn_sequence_started");
        }
    }

    void RespawnMonster::CancelRespawnSequence() {
        if (m_isRespawnPending) {
            m_isRespawnPending = false;
            TriggerRespawnEvent("respawn_sequence_cancelled");
        }
    }

    void RespawnMonster::ConfigureReactiveComponents(bool enableObj, bool enableArea) {
        m_reactObj.SetActive(enableObj);
        m_reactArea.SetActive(enableArea);
    }

    bool RespawnMonster::AreReactiveComponentsActive() const {
        return m_reactObj.IsActive() || m_reactArea.IsActive();
    }

    void RespawnMonster::SetRespawnConfiguration(const std::string& defineCode, bool callEvent, 
                                                bool enableObj, bool enableArea) {
        SetDefineCode(defineCode);
        SetCallEvent(callEvent);
        ConfigureReactiveComponents(enableObj, enableArea);
    }

    void RespawnMonster::SetRespawnDelay(float delaySeconds) {
        m_respawnDelay = std::clamp(delaySeconds, 0.0f, MAX_RESPAWN_DELAY);
        NotifyRespawnStateChange();
    }

    void RespawnMonster::SetRespawnCondition(const std::string& condition) {
        m_respawnCondition = condition;
        if (m_respawnCondition.length() > MAX_RESPAWN_CONDITION_LENGTH) {
            m_respawnCondition.resize(MAX_RESPAWN_CONDITION_LENGTH);
        }
        NotifyRespawnStateChange();
    }

    std::string RespawnMonster::ToString() const {
        std::ostringstream oss;
        oss << "RespawnMonster{";
        oss << "DefineCode: \"" << m_defineCode << "\", ";
        oss << "CallEvent: " << (m_callEvent ? "true" : "false") << ", ";
        oss << "RespawnActive: " << (m_isRespawnActive ? "true" : "false") << ", ";
        oss << "RespawnPending: " << (m_isRespawnPending ? "true" : "false") << ", ";
        oss << "RespawnDelay: " << m_respawnDelay << "s, ";
        oss << "ReactObj Active: " << (m_reactObj.IsActive() ? "true" : "false") << ", ";
        oss << "ReactArea Active: " << (m_reactArea.IsActive() ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    size_t RespawnMonster::GetMemoryUsage() const {
        return sizeof(RespawnMonster) + 
               m_defineCode.capacity() + 
               m_respawnCondition.capacity() +
               sizeof(ReactObj) + 
               sizeof(ReactArea) +
               (m_eventHandlers.size() * (sizeof(std::string) + sizeof(std::function<void(const RespawnMonster&)>)));
    }

    const char* RespawnMonster::GetDefineCodeCStr() const {
        return m_defineCode.c_str();
    }

    void RespawnMonster::SetDefineCodeFromCStr(const char* code) {
        if (code) {
            SetDefineCode(std::string(code));
        } else {
            m_defineCode.clear();
        }
    }

    bool RespawnMonster::ValidateDefineCode() const {
        return IsValidString(m_defineCode, MAX_DEFINE_CODE_LENGTH);
    }

    bool RespawnMonster::ValidateRespawnConfiguration() const {
        return ValidateDefineCode() && 
               IsValidString(m_respawnCondition, MAX_RESPAWN_CONDITION_LENGTH) &&
               m_respawnDelay <= MAX_RESPAWN_DELAY &&
               IsValid();
    }

    void RespawnMonster::RegisterRespawnEventHandler(const std::string& event, std::function<void(const RespawnMonster&)> handler) {
        m_eventHandlers[event] = std::move(handler);
    }

    void RespawnMonster::TriggerRespawnEvent(const std::string& event) {
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end() && it->second) {
            it->second(*this);
        }
    }

    void RespawnMonster::InitializeComponents() {
        m_reactObj.Initialize();
        m_reactArea.Initialize();
    }

    void RespawnMonster::ResetComponents() {
        m_reactObj.Reset();
        m_reactArea.Reset();
    }

    void RespawnMonster::ValidateAndTruncate() {
        if (m_defineCode.length() > MAX_DEFINE_CODE_LENGTH) {
            m_defineCode.resize(MAX_DEFINE_CODE_LENGTH);
        }
        if (m_respawnCondition.length() > MAX_RESPAWN_CONDITION_LENGTH) {
            m_respawnCondition.resize(MAX_RESPAWN_CONDITION_LENGTH);
        }
        if (m_respawnDelay > MAX_RESPAWN_DELAY) {
            m_respawnDelay = MAX_RESPAWN_DELAY;
        }
    }

    bool RespawnMonster::IsValidString(const std::string& str, size_t maxLength) const {
        return str.length() <= maxLength;
    }

    void RespawnMonster::NotifyRespawnStateChange() {
        TriggerRespawnEvent("respawn_state_changed");
    }

    void RespawnMonster::ProcessRespawnLogic() {
        if (m_isRespawnActive && m_isRespawnPending) {
            // Respawn logic would be implemented here
            // This is a placeholder for the actual respawn processing
        }
    }

    // RespawnMonsterFactory implementation

    std::unique_ptr<RespawnMonster> RespawnMonsterFactory::CreateRespawnMonster() {
        return std::make_unique<RespawnMonster>();
    }

    std::unique_ptr<RespawnMonster> RespawnMonsterFactory::CreateRespawnMonster(const std::string& defineCode) {
        return std::make_unique<RespawnMonster>(defineCode);
    }

    std::unique_ptr<RespawnMonster> RespawnMonsterFactory::CreateRespawnMonster(const std::string& defineCode,
                                                                               bool callEvent) {
        return std::make_unique<RespawnMonster>(defineCode, callEvent);
    }

    std::unique_ptr<RespawnMonster> RespawnMonsterFactory::CreateTimedRespawn(const std::string& defineCode,
                                                                             float delaySeconds) {
        auto respawn = std::make_unique<RespawnMonster>(defineCode, true);
        respawn->SetRespawnDelay(delaySeconds);
        respawn->SetRespawnActive(true);
        return respawn;
    }

    std::unique_ptr<RespawnMonster> RespawnMonsterFactory::CreateEventRespawn(const std::string& defineCode,
                                                                             const std::string& condition) {
        auto respawn = std::make_unique<RespawnMonster>(defineCode, true);
        respawn->SetRespawnCondition(condition);
        respawn->SetRespawnActive(true);
        return respawn;
    }

    std::unique_ptr<RespawnMonster> RespawnMonsterFactory::CreateAreaRespawn(const std::string& defineCode,
                                                                           float x, float y, float width, float height) {
        auto respawn = std::make_unique<RespawnMonster>(defineCode, true);
        respawn->GetReactArea().SetArea(x, y, width, height);
        respawn->ConfigureReactiveComponents(false, true); // Only area reactive
        respawn->SetRespawnActive(true);
        return respawn;
    }

    std::vector<std::unique_ptr<RespawnMonster>> RespawnMonsterFactory::CreateRespawnMonsters(
        const std::vector<std::pair<std::string, bool>>& configurations) {
        std::vector<std::unique_ptr<RespawnMonster>> result;
        result.reserve(configurations.size());

        for (const auto& config : configurations) {
            result.push_back(CreateRespawnMonster(config.first, config.second));
        }

        return result;
    }

    // RespawnMonsterManager implementation

    RespawnMonsterManager::RespawnMonsterManager() {
        m_respawnMonsters.reserve(50); // Reserve space for typical respawn count
    }

    void RespawnMonsterManager::AddRespawnMonster(std::unique_ptr<RespawnMonster> respawnMonster) {
        if (respawnMonster && !respawnMonster->GetDefineCode().empty()) {
            std::string defineCode = respawnMonster->GetDefineCode();
            m_defineCodeToIndex[defineCode] = m_respawnMonsters.size();
            m_respawnMonsters.push_back(std::move(respawnMonster));
        }
    }

    void RespawnMonsterManager::RemoveRespawnMonster(const std::string& defineCode) {
        auto it = m_defineCodeToIndex.find(defineCode);
        if (it != m_defineCodeToIndex.end()) {
            size_t index = it->second;
            if (index < m_respawnMonsters.size()) {
                m_respawnMonsters.erase(m_respawnMonsters.begin() + index);
                m_defineCodeToIndex.erase(it);

                // Update indices for remaining respawn monsters
                for (auto& pair : m_defineCodeToIndex) {
                    if (pair.second > index) {
                        --pair.second;
                    }
                }
            }
        }
    }

    std::shared_ptr<RespawnMonster> RespawnMonsterManager::GetRespawnMonster(const std::string& defineCode) const {
        auto it = m_defineCodeToIndex.find(defineCode);
        if (it != m_defineCodeToIndex.end() && it->second < m_respawnMonsters.size()) {
            return m_respawnMonsters[it->second];
        }
        return nullptr;
    }

    void RespawnMonsterManager::ActivateAllRespawns() {
        for (auto& respawn : m_respawnMonsters) {
            if (respawn) {
                respawn->SetRespawnActive(true);
            }
        }
    }

    void RespawnMonsterManager::DeactivateAllRespawns() {
        for (auto& respawn : m_respawnMonsters) {
            if (respawn) {
                respawn->SetRespawnActive(false);
            }
        }
    }

    void RespawnMonsterManager::UpdateAllRespawns(float deltaTime) {
        for (auto& respawn : m_respawnMonsters) {
            if (respawn && respawn->IsRespawnActive()) {
                // Update respawn logic here
                // This would typically involve checking timers, conditions, etc.
            }
        }
    }

    void RespawnMonsterManager::ProcessAllRespawnEvents() {
        for (auto& respawn : m_respawnMonsters) {
            if (respawn && respawn->IsRespawnActive()) {
                // Process any pending respawn events
                if (respawn->IsRespawnPending()) {
                    // Trigger respawn logic
                }
            }
        }
    }

    size_t RespawnMonsterManager::GetActiveRespawnCount() const {
        return std::count_if(m_respawnMonsters.begin(), m_respawnMonsters.end(),
            [](const std::shared_ptr<RespawnMonster>& respawn) {
                return respawn && respawn->IsRespawnActive();
            });
    }

    size_t RespawnMonsterManager::GetPendingRespawnCount() const {
        return std::count_if(m_respawnMonsters.begin(), m_respawnMonsters.end(),
            [](const std::shared_ptr<RespawnMonster>& respawn) {
                return respawn && respawn->IsRespawnPending();
            });
    }

    void RespawnMonsterManager::SetGlobalRespawnDelay(float delaySeconds) {
        m_globalRespawnDelay = std::max(0.0f, delaySeconds);

        // Apply to all respawn monsters that don't have a custom delay
        for (auto& respawn : m_respawnMonsters) {
            if (respawn && respawn->GetRespawnDelay() == 0.0f) {
                respawn->SetRespawnDelay(m_globalRespawnDelay);
            }
        }
    }

    // RespawnMonsterUtils implementation

    namespace RespawnMonsterUtils {

        bool ValidateRespawnMonster(const RespawnMonster& respawnMonster) {
            return respawnMonster.IsValid() && respawnMonster.ValidateRespawnConfiguration();
        }

        bool ValidateDefineCode(const std::string& code) {
            return code.length() <= RespawnMonster::MAX_DEFINE_CODE_LENGTH && !code.empty();
        }

        bool ValidateRespawnCondition(const std::string& condition) {
            return condition.length() <= RespawnMonster::MAX_RESPAWN_CONDITION_LENGTH;
        }

        std::string SanitizeDefineCode(const std::string& code) {
            std::string sanitized = code;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > RespawnMonster::MAX_DEFINE_CODE_LENGTH) {
                sanitized.resize(RespawnMonster::MAX_DEFINE_CODE_LENGTH);
            }
            return sanitized;
        }

        std::string SanitizeRespawnCondition(const std::string& condition) {
            std::string sanitized = condition;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > RespawnMonster::MAX_RESPAWN_CONDITION_LENGTH) {
                sanitized.resize(RespawnMonster::MAX_RESPAWN_CONDITION_LENGTH);
            }
            return sanitized;
        }

        std::string GenerateUniqueDefineCode(const std::string& baseName) {
            static uint32_t counter = 0;
            std::ostringstream oss;
            oss << baseName << "_" << ++counter;
            return oss.str();
        }

        size_t CalculateMemoryFootprint(const RespawnMonster& respawnMonster) {
            return respawnMonster.GetMemoryUsage();
        }

        void ConfigureDefaultRespawn(RespawnMonster& respawnMonster) {
            if (!respawnMonster.HasDefineCode()) {
                respawnMonster.SetDefineCode("DefaultRespawn");
            }
            if (respawnMonster.GetRespawnDelay() == 0.0f) {
                respawnMonster.SetRespawnDelay(30.0f); // 30 seconds default
            }
            respawnMonster.ConfigureReactiveComponents(true, true);
            respawnMonster.SetCallEvent(true);
            respawnMonster.SetRespawnActive(true);
        }

        void OptimizeRespawnConfiguration(RespawnMonster& respawnMonster) {
            // Optimization logic could be added here
            // For now, just ensure respawn has valid configuration
            if (!ValidateRespawnMonster(respawnMonster)) {
                ConfigureDefaultRespawn(respawnMonster);
            }
        }

        std::string RespawnMonsterToJson(const RespawnMonster& respawnMonster) {
            std::ostringstream oss;
            oss << "{";
            oss << "\"defineCode\": \"" << respawnMonster.GetDefineCode() << "\",";
            oss << "\"callEvent\": " << (respawnMonster.GetCallEvent() ? "true" : "false") << ",";
            oss << "\"respawnActive\": " << (respawnMonster.IsRespawnActive() ? "true" : "false") << ",";
            oss << "\"respawnPending\": " << (respawnMonster.IsRespawnPending() ? "true" : "false") << ",";
            oss << "\"respawnDelay\": " << respawnMonster.GetRespawnDelay() << ",";
            oss << "\"respawnCondition\": \"" << respawnMonster.GetRespawnCondition() << "\"";
            oss << "}";
            return oss.str();
        }

        std::unique_ptr<RespawnMonster> RespawnMonsterFromJson(const std::string& json) {
            // Simple JSON parsing - in a real implementation, use a proper JSON library
            auto respawnMonster = std::make_unique<RespawnMonster>();

            // This is a simplified implementation
            // In production, use a proper JSON parser like nlohmann/json

            return respawnMonster;
        }

        std::vector<std::string> AnalyzeRespawnConfiguration(const RespawnMonster& respawnMonster) {
            std::vector<std::string> analysis;

            analysis.push_back("Define Code: " + respawnMonster.GetDefineCode());
            analysis.push_back("Call Event: " + (respawnMonster.GetCallEvent() ? std::string("Enabled") : std::string("Disabled")));
            analysis.push_back("Respawn Active: " + (respawnMonster.IsRespawnActive() ? std::string("Yes") : std::string("No")));
            analysis.push_back("Respawn Pending: " + (respawnMonster.IsRespawnPending() ? std::string("Yes") : std::string("No")));
            analysis.push_back("Respawn Delay: " + std::to_string(respawnMonster.GetRespawnDelay()) + " seconds");
            analysis.push_back("Respawn Condition: " + respawnMonster.GetRespawnCondition());
            analysis.push_back("Reactive Components: " + (respawnMonster.AreReactiveComponentsActive() ? std::string("Active") : std::string("Inactive")));

            return analysis;
        }

        float CalculateRespawnEfficiency(const RespawnMonster& respawnMonster) {
            float efficiency = 0.0f;

            // Base efficiency from configuration completeness
            if (respawnMonster.HasDefineCode()) efficiency += 0.3f;
            if (respawnMonster.GetCallEvent()) efficiency += 0.2f;
            if (respawnMonster.IsRespawnActive()) efficiency += 0.2f;
            if (respawnMonster.AreReactiveComponentsActive()) efficiency += 0.2f;
            if (respawnMonster.GetRespawnDelay() > 0.0f) efficiency += 0.1f;

            return std::clamp(efficiency, 0.0f, 1.0f);
        }

        std::string GetRespawnStatusSummary(const RespawnMonster& respawnMonster) {
            std::ostringstream oss;
            oss << "Respawn Status: ";

            if (!respawnMonster.IsValid()) {
                oss << "Invalid Configuration";
            } else if (respawnMonster.IsRespawnPending()) {
                oss << "Pending Respawn";
            } else if (respawnMonster.IsRespawnActive()) {
                oss << "Active and Ready";
            } else {
                oss << "Inactive";
            }

            oss << " (Efficiency: " << (CalculateRespawnEfficiency(respawnMonster) * 100.0f) << "%)";

            return oss.str();
        }

    } // namespace RespawnMonsterUtils

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {

    void __respawn_monster_Constructor(NexusProtection::World::_respawn_monster* this_ptr) {
        if (this_ptr) {
            // Initialize pointers and values to match original constructor behavior
            this_ptr->ReactObj = nullptr;
            this_ptr->ReactArea = nullptr;
            this_ptr->bCallEvent = false;
            this_ptr->pszDefineCode = nullptr;

            // Clear padding
            std::memset(this_ptr->padding, 0, sizeof(this_ptr->padding));

            // Allocate and initialize reactive components
            this_ptr->ReactObj = new NexusProtection::World::_react_obj();
            this_ptr->ReactArea = new NexusProtection::World::_react_area();

            // Initialize reactive components (from AddMonster.cpp)
            _react_obj_Constructor(static_cast<NexusProtection::World::_react_obj*>(this_ptr->ReactObj));
            _react_area_Constructor(static_cast<NexusProtection::World::_react_area*>(this_ptr->ReactArea));
        }
    }

    void __respawn_monster_Destructor(NexusProtection::World::_respawn_monster* this_ptr) {
        if (this_ptr) {
            // Clean up reactive components
            if (this_ptr->ReactObj) {
                _react_obj_Destructor(static_cast<NexusProtection::World::_react_obj*>(this_ptr->ReactObj));
                delete static_cast<NexusProtection::World::_react_obj*>(this_ptr->ReactObj);
                this_ptr->ReactObj = nullptr;
            }

            if (this_ptr->ReactArea) {
                _react_area_Destructor(static_cast<NexusProtection::World::_react_area*>(this_ptr->ReactArea));
                delete static_cast<NexusProtection::World::_react_area*>(this_ptr->ReactArea);
                this_ptr->ReactArea = nullptr;
            }

            // Clean up define code string
            if (this_ptr->pszDefineCode) {
                delete[] this_ptr->pszDefineCode;
                this_ptr->pszDefineCode = nullptr;
            }

            this_ptr->bCallEvent = false;
        }
    }

    void __respawn_monster_SetDefineCode(NexusProtection::World::_respawn_monster* this_ptr, const char* code) {
        if (!this_ptr) {
            return;
        }

        // Clean up existing memory
        if (this_ptr->pszDefineCode) {
            delete[] this_ptr->pszDefineCode;
            this_ptr->pszDefineCode = nullptr;
        }

        // Allocate and copy new string
        if (code) {
            size_t len = std::strlen(code) + 1;
            this_ptr->pszDefineCode = new char[len];
            std::strcpy(this_ptr->pszDefineCode, code);
        }
    }

    void __respawn_monster_SetCallEvent(NexusProtection::World::_respawn_monster* this_ptr, bool callEvent) {
        if (this_ptr) {
            this_ptr->bCallEvent = callEvent;
        }
    }

    const char* __respawn_monster_GetDefineCode(NexusProtection::World::_respawn_monster* this_ptr) {
        return (this_ptr && this_ptr->pszDefineCode) ? this_ptr->pszDefineCode : "";
    }

    bool __respawn_monster_GetCallEvent(NexusProtection::World::_respawn_monster* this_ptr) {
        return this_ptr ? this_ptr->bCallEvent : false;
    }

    void* __respawn_monster_GetReactObj(NexusProtection::World::_respawn_monster* this_ptr) {
        return (this_ptr) ? this_ptr->ReactObj : nullptr;
    }

    void* __respawn_monster_GetReactArea(NexusProtection::World::_respawn_monster* this_ptr) {
        return (this_ptr) ? this_ptr->ReactArea : nullptr;
    }

    void __respawn_monster_Initialize(NexusProtection::World::_respawn_monster* this_ptr) {
        if (this_ptr) {
            __respawn_monster_Constructor(this_ptr);
        }
    }

    void __respawn_monster_Reset(NexusProtection::World::_respawn_monster* this_ptr) {
        if (this_ptr) {
            __respawn_monster_Destructor(this_ptr);
            __respawn_monster_Constructor(this_ptr);
        }
    }

} // extern "C"

// Global legacy compatibility
NexusProtection::World::RespawnMonster* g_pRespawnMonster = nullptr;
