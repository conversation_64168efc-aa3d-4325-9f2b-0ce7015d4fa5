/*
 * Function: ?CreateGuardTower@@YAPEAVCGuardTower@@PEAVCMapData@@GPEAMPEAU_db_con@_STORAGE_LIST@@PEAVCPlayer@@E_N@Z
 * Address: 0x1401313F0
 */

CGuardTower *__fastcall CreateGuardTower(CMapData *pMap, unsigned __int16 wLayer, float *fPos, _STORAGE_LIST::_db_con *pItem, CPlayer *pMaster, char byRaceCode, bool bQuick)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  CGuardTower *result; // rax@10
  __int64 v10; // [sp+0h] [bp-A8h]@1
  CGuardTower *v11; // [sp+20h] [bp-88h]@4
  int j; // [sp+28h] [bp-80h]@4
  _tower_create_setdata Dst; // [sp+40h] [bp-68h]@11
  CMapData *v14; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v15; // [sp+B8h] [bp+10h]@1
  float *Src; // [sp+C0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v17; // [sp+C8h] [bp+20h]@1

  v17 = pItem;
  Src = fPos;
  v15 = wLayer;
  v14 = pMap;
  v7 = &v10;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v11 = 0i64;
  for ( j = 0; j < 500; ++j )
  {
    if ( !g_Tower[j].m_bLive )
    {
      v11 = &g_Tower[j];
      break;
    }
  }
  if ( v11 )
  {
    _tower_create_setdata::_tower_create_setdata(&Dst);
    Dst.m_pMap = v14;
    Dst.m_nLayerIndex = v15;
    Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, v17->m_wItemIndex);
    if ( Dst.m_pRecordSet )
    {
      memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
      Dst.pMaster = pMaster;
      Dst.byRaceCode = byRaceCode;
      Dst.pItem = v17;
      Dst.bQuick = bQuick;
      if ( CGuardTower::Create(v11, &Dst) )
        result = v11;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
