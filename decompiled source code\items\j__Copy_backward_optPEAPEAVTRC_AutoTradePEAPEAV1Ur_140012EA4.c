/*
 * Function: j_??$_Copy_backward_opt@PEAPEAVTRC_AutoTrade@@PEAPEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAPEAVTRC_AutoTrade@@PEAPEAV1@00Urandom_access_iterator_tag@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140012EA4
 */

TRC_AutoTrade **__fastcall std::_Copy_backward_opt<TRC_AutoTrade * *,TRC_AutoTrade * *,std::random_access_iterator_tag>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Dest, std::random_access_iterator_tag __formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_backward_opt<TRC_AutoTrade * *,TRC_AutoTrade * *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
