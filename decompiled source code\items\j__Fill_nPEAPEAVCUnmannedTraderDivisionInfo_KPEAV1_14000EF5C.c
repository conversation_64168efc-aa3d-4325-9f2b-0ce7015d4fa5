/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000EF5C
 */

void __fastcall std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *>(CUnmannedTraderDivisionInfo **_First, unsigned __int64 _Count, CUnmannedTraderDivisionInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *>(
    _First,
    _Count,
    _Val,
    __formal);
}
