/*
 * Function: ?_set_db_sf_effect@CPlayer@@QEAAXPEAU_SFCONT_DB_BASE@@@Z
 * Address: 0x14004E2C0
 */

void __fastcall CPlayer::_set_db_sf_effect(CPlayer *this, _SFCONT_DB_BASE *pDBBase)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@11
  char *v5; // rax@20
  __int64 v6; // [sp+0h] [bp-98h]@1
  char byLv[4]; // [sp+20h] [bp-78h]@20
  unsigned int dwStartSec; // [sp+28h] [bp-70h]@20
  unsigned int v9; // [sp+40h] [bp-58h]@5
  int j; // [sp+44h] [bp-54h]@5
  int k; // [sp+48h] [bp-50h]@7
  _SFCONT_DB_BASE::_LIST *v12; // [sp+50h] [bp-48h]@10
  _sf_continous *pCont; // [sp+58h] [bp-40h]@11
  unsigned __int8 v14; // [sp+60h] [bp-38h]@11
  unsigned __int16 v15; // [sp+64h] [bp-34h]@11
  char v16; // [sp+68h] [bp-30h]@11
  unsigned int v17; // [sp+6Ch] [bp-2Ch]@11
  unsigned __int16 v18; // [sp+70h] [bp-28h]@11
  char v19; // [sp+74h] [bp-24h]@11
  char v20; // [sp+75h] [bp-23h]@13
  _base_fld *v21; // [sp+78h] [bp-20h]@21
  unsigned int v22; // [sp+80h] [bp-18h]@20
  int v23; // [sp+84h] [bp-14h]@20
  unsigned int v24; // [sp+88h] [bp-10h]@20
  CPlayer *v25; // [sp+A0h] [bp+8h]@1
  _SFCONT_DB_BASE *v26; // [sp+A8h] [bp+10h]@1

  v26 = pDBBase;
  v25 = this;
  v2 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v25->m_pUserDB )
  {
    v9 = _sf_continous::GetSFContCurTime();
    for ( j = 0; j < 2; ++j )
    {
      for ( k = 0; k < 8; ++k )
      {
        v12 = (_SFCONT_DB_BASE::_LIST *)((char *)v26 + 32 * j + 4 * k);
        if ( _SFCONT_DB_BASE::_LIST::IsFilled(v12) )
        {
          pCont = &v25->m_SFCont[j][k];
          v14 = _SFCONT_DB_BASE::_LIST::GetEffectCode(v12);
          v15 = _SFCONT_DB_BASE::_LIST::GetEffectIndex(v12);
          v16 = _SFCONT_DB_BASE::_LIST::GetLv(v12) + 1;
          v17 = v9;
          v18 = _SFCONT_DB_BASE::_LIST::GetLeftTime(v12);
          v19 = _SFCONT_DB_BASE::_LIST::GetOrder(v12);
          v4 = _SFCONT_DB_BASE::_LIST::GetOrder(v12);
          if ( v9 > (unsigned __int8)v4 )
          {
            v17 = v9 - (unsigned __int8)v19;
            v18 += (unsigned __int8)v19;
          }
          v20 = 0;
          if ( (signed int)v14 < 4 )
          {
            if ( CRecordData::GetRecord(&stru_1799C8410 + v14, v15) )
            {
              if ( (signed int)(unsigned __int8)v16 > 7 )
                v20 = 1;
            }
            else
            {
              v20 = 1;
            }
          }
          else
          {
            v20 = 1;
          }
          if ( v20 )
          {
            CUserDB::Update_SFContDelete(v25->m_pUserDB, j, k);
            v22 = (unsigned __int8)v16;
            v23 = v15;
            v24 = v14;
            v5 = CPlayerDB::GetCharNameA(&v25->m_Param);
            dwStartSec = v22;
            *(_DWORD *)byLv = v23;
            CLogFile::Write(&stru_1799C8E78, "%s: error stored effect, code: %d, idx: %d: lv: %d", v5, v24);
          }
          else
          {
            CCharacter::_set_sf_cont((CCharacter *)&v25->vfptr, pCont, v14, v15, v16, v17, v18, 0);
            CEquipItemSFAgent::StartContSF(&v25->EquipItemSFAgent, pCont);
            v21 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
            if ( v21 && pCont->m_byEffectCode == 3 && pCont->m_wEffectIndex == v21->m_dwIndex )
              v25->m_bAfterEffect = 1;
            CPlayer::SendMsg_StartContSF(v25, pCont);
            v25->m_bLastContEffectUpdate = 1;
          }
        }
      }
    }
  }
}
