/*
 * Function: ?IsValidPlayer@CMonster@@QEAAHXZ
 * Address: 0x140142E70
 */

signed __int64 __fastcall CMonster::IsValidPlayer(CMonster *this, __int64 a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  CCharacter *v6; // [sp+20h] [bp-18h]@19
  CMonster *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_pTargetChar )
  {
    if ( v7->m_pTargetChar->m_bLive )
    {
      if ( v7->m_pTargetChar->m_bCorpse )
      {
        v7->m_pTargetChar = 0i64;
        result = 0i64;
      }
      else if ( v7->m_pTargetChar->m_pCurMap == v7->m_pCurMap )
      {
        if ( v7->m_pTargetChar->m_bMapLoading )
        {
          v7->m_pTargetChar = 0i64;
          result = 0i64;
        }
        else
        {
          LOBYTE(a2) = 1;
          if ( (unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v7->m_pTargetChar->vfptr->IsBeAttackedAble)(
                                  v7->m_pTargetChar,
                                  a2) )
          {
            if ( v7->m_pTargetChar->m_ObjID.m_byID || !BYTE2(v7->m_pTargetChar[1].m_fCurPos[2]) )
            {
              if ( v7->m_pTargetChar->m_ObjID.m_byID == 3
                && (v6 = v7->m_pTargetChar) != 0i64
                && *(_BYTE *)(*(_QWORD *)&v6[1].m_bLive + 1922i64) )
              {
                result = 0i64;
              }
              else if ( CCharacter::GetStealth(v7->m_pTargetChar, 1) || v7->m_pTargetChar->m_bObserver )
              {
                v7->m_pTargetChar = 0i64;
                result = 0i64;
              }
              else
              {
                result = 1i64;
              }
            }
            else
            {
              result = 0i64;
            }
          }
          else
          {
            v7->m_pTargetChar = 0i64;
            result = 0i64;
          }
        }
      }
      else
      {
        v7->m_pTargetChar = 0i64;
        result = 0i64;
      }
    }
    else
    {
      v7->m_pTargetChar = 0i64;
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
