/*
 * Function: ??$destroyer@VCMonster@@@lua_tinker@@YAHPEAUlua_State@@@Z
 * Address: 0x140408950
 */

__int64 __fastcall lua_tinker::destroyer<CMonster>(struct lua_State *L)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  void (__fastcall ***v3)(_QWORD, _QWORD); // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  void (__fastcall ***v6)(_QWORD, _QWORD); // [sp+20h] [bp-18h]@4
  struct lua_State *v7; // [sp+40h] [bp+8h]@1

  v7 = L;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  LODWORD(v3) = lua_touserdata(v7, 1i64);
  v6 = v3;
  (**v3)(v3, 0i64);
  return 0i64;
}
