/*
 * Function: _dynamic_initializer_for__CGuardTower::s_Temp__
 * Address: 0x1406DBA90
 */

void dynamic_initializer_for__CGuardTower::s_Temp__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `vector constructor iterator'(
    &CGuardTower::s_Temp,
    0x28ui64,
    2532,
    (void *(__cdecl *)(void *))__TEMP_WAIT_TOWER::__TEMP_WAIT_TOWER);
}
