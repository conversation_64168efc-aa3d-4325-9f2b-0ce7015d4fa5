/*
 * Function: _dynamic_initializer_for__CPlayer::s_SkillIndexPerMastery__
 * Address: 0x1406DA220
 */

void dynamic_initializer_for__CPlayer::s_SkillIndexPerMastery__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `vector constructor iterator'(
    CPlayer::s_SkillIndexPerMastery,
    0xC4ui64,
    8,
    (void *(__cdecl *)(void *))_SKILL_IDX_PER_MASTERY::_SKILL_IDX_PER_MASTERY);
}
