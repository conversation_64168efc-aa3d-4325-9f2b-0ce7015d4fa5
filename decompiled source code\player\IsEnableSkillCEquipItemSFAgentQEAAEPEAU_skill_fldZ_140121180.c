/*
 * Function: ?IsEnableSkill@CEquipItemSFAgent@@QEAAEPEAU_skill_fld@@@Z
 * Address: 0x140121180
 */

char __fastcall CEquipItemSFAgent::IsEnableSkill(CEquipItemSFAgent *this, _skill_fld *pSkill)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CEquipItemSFAgent *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  if ( pSkill->m_strFixWeapon[12] == 49 && (v6 = CEquipItemSFAgent::IsEnableSkill(v7, 7, pSkill)) != 0 )
    result = v6;
  else
    result = 0;
  return result;
}
