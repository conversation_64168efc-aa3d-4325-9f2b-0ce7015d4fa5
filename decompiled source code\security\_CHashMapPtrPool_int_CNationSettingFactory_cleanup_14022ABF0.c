/*
 * Function: _CHashMapPtrPool_int_CNationSettingFactory_::cleanup_::_1_::dtor$2
 * Address: 0x14022ABF0
 */

void __fastcall CHashMapPtrPool_int_CNationSettingFactory_::cleanup_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *)(a2 + 208));
}
