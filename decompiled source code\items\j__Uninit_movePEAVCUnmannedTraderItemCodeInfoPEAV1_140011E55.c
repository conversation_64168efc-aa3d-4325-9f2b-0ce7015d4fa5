/*
 * Function: j_??$_Uninit_move@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCUnmannedTraderItemCodeInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140011E55
 */

CUnmannedTraderItemCodeInfo *__fastcall std::_Uninit_move<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>,std::_Undefined_move_tag>(CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::allocator<CUnmannedTraderItemCodeInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
