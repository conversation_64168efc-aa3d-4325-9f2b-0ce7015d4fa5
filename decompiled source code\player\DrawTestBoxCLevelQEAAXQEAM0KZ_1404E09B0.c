/*
 * Function: ?DrawTestBox@CLevel@@QEAAXQEAM0K@Z
 * Address: 0x1404E09B0
 */

void __fastcall CLevel::DrawTestBox(CLevel *this, float *const a2, float *const a3, unsigned __int32 a4)
{
  int v4; // xmm1_4@1
  unsigned __int32 v5; // ebx@1
  float *v6; // rdi@1
  struct IDirect3DDevice8 *v7; // rax@1
  float v8; // [sp+20h] [bp-48h]@1
  int v9; // [sp+24h] [bp-44h]@1
  int v10; // [sp+28h] [bp-40h]@1
  int v11; // [sp+2Ch] [bp-3Ch]@1
  int v12; // [sp+30h] [bp-38h]@1
  float v13; // [sp+34h] [bp-34h]@1
  int v14; // [sp+38h] [bp-30h]@1
  int v15; // [sp+3Ch] [bp-2Ch]@1
  int v16; // [sp+40h] [bp-28h]@1
  int v17; // [sp+44h] [bp-24h]@1
  float v18; // [sp+48h] [bp-20h]@1
  int v19; // [sp+4Ch] [bp-1Ch]@1
  int v20; // [sp+50h] [bp-18h]@1
  int v21; // [sp+54h] [bp-14h]@1
  int v22; // [sp+58h] [bp-10h]@1
  float v23; // [sp+5Ch] [bp-Ch]@1

  v4 = *((_DWORD *)a3 + 1);
  v5 = a4;
  v6 = a2;
  v19 = 0;
  v17 = 0;
  v16 = 0;
  v15 = 0;
  v14 = 0;
  v12 = 0;
  v11 = 0;
  v10 = 0;
  v9 = 0;
  v21 = v4;
  v23 = FLOAT_1_0;
  v18 = FLOAT_1_0;
  v13 = FLOAT_1_0;
  v8 = FLOAT_1_0;
  v20 = *(_DWORD *)a3;
  v22 = *((_DWORD *)a3 + 2);
  v7 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, int *))v7->vfptr[12].AddRef)(v7, 256i64, (int *)&v8);
  DrawTestBox(*v6, v6[1], v6[2], v5);
}
