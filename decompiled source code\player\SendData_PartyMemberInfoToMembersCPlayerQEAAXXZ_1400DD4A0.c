/*
 * Function: ?SendData_PartyMemberInfoToMembers@CPlayer@@QEAAXXZ
 * Address: 0x1400DD4A0
 */

void __fastcall CPlayer::SendData_PartyMemberInfoToMembers(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@17
  __int64 v4; // [sp+0h] [bp-F8h]@1
  _party_member_info_upd v5; // [sp+40h] [bp-B8h]@4
  char v6; // [sp+94h] [bp-64h]@4
  int j; // [sp+98h] [bp-60h]@4
  int k; // [sp+9Ch] [bp-5Ch]@6
  bool *v9; // [sp+A0h] [bp-58h]@9
  CPartyPlayer **v10; // [sp+A8h] [bp-50h]@12
  int v11; // [sp+B0h] [bp-48h]@13
  char pbyType; // [sp+C4h] [bp-34h]@13
  char v13; // [sp+C5h] [bp-33h]@13
  int l; // [sp+D4h] [bp-24h]@13
  unsigned __int64 v15; // [sp+E0h] [bp-18h]@4
  CPlayer *v16; // [sp+100h] [bp+8h]@1

  v16 = this;
  v1 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v15 = (unsigned __int64)&v4 ^ _security_cookie;
  _party_member_info_upd::_party_member_info_upd(&v5);
  v5.dwMemSerial = v16->m_dwObjSerial;
  v5.wHPRate = (*(int (__fastcall **)(CPlayer *))&v16->vfptr->gap8[8])(v16);
  v5.wFPRate = CPlayer::CalcCurFPRate(v16);
  v5.wSPRate = CPlayer::CalcCurSPRate(v16);
  v5.byLv = CPlayerDB::GetLevel(&v16->m_Param);
  v5.byMapCode = CPlayerDB::GetMapCode(&v16->m_Param);
  v5.zPos[0] = (signed int)ffloor(v16->m_fCurPos[0]);
  v5.zPos[1] = (signed int)ffloor(v16->m_fCurPos[2]);
  v5.byContEffectNum = 0;
  v6 = 0;
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      v9 = &v16->m_SFCont[j][k].m_bExist;
      if ( *v9 )
      {
        v5.Effect[(unsigned __int8)v6].wEffectCode = CCharacter::CalcEffectBit(
                                                       (CCharacter *)&v16->vfptr,
                                                       v9[1],
                                                       *((_WORD *)v9 + 1));
        v5.Effect[(unsigned __int8)v6++].byEffectLv = v9[4];
      }
    }
  }
  v5.byContEffectNum = v6;
  v10 = CPartyPlayer::GetPtrPartyMember(v16->m_pPartyMgr);
  if ( v10 )
  {
    v11 = CPartyPlayer::GetPopPartyMember(v16->m_pPartyMgr);
    pbyType = 16;
    v13 = 19;
    for ( l = 0; l < v11; ++l )
    {
      if ( v10[l] != v16->m_pPartyMgr )
      {
        v3 = _party_member_info_upd::size(&v5);
        CNetProcess::LoadSendMsg(unk_1414F2088, v10[l]->m_wZoneIndex, &pbyType, (char *)&v5, v3);
      }
    }
  }
}
