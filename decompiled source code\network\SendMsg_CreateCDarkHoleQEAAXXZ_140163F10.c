/*
 * Function: ?SendMsg_Create@CDarkHole@@QEAAXXZ
 * Address: 0x140163F10
 */

void __fastcall CDarkHole::SendMsg_Create(CDarkHole *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-A8h]@1
  _darkhole_create_zocl v5; // [sp+38h] [bp-70h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v7; // [sp+75h] [bp-33h]@4
  unsigned __int64 v8; // [sp+90h] [bp-18h]@4
  CDarkHole *v9; // [sp+B0h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v4 ^ _security_cookie;
  v5.wQuestIndex = v9->m_pRecordSet->m_dwIndex;
  v5.wIndex = v9->m_ObjID.m_wIndex;
  v5.dwSerial = v9->m_dwObjSerial;
  FloatToShort(v9->m_fCurPos, v5.zPos, 3);
  v5.bHurry = v9->m_bHurry;
  strcpy_0(v5.wszOpenerName, v9->m_wszOpenerName);
  v5.dwOpenerSerial = v9->m_dwOpenerSerial;
  pbyType = 35;
  v7 = 101;
  v3 = _darkhole_create_zocl::size(&v5);
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, (char *)&v5, v3, 0);
}
