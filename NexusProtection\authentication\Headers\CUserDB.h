#pragma once

#include "AuthenticationTypes.h"
#include <memory>
#include <mutex>
#include <unordered_map>
#include <string>
#include <vector>

namespace NexusProtection::Authentication {

    /**
     * @brief Password security utilities
     */
    class PasswordSecurity {
    public:
        static std::string HashPassword(const std::string& password, const std::string& salt = "");
        static bool VerifyPassword(const std::string& password, const std::string& hashedPassword);
        static std::string GenerateSalt();
        static bool IsPasswordStrong(const std::string& password);
        static std::string GenerateRandomPassword(size_t length = 12);
    };

    /**
     * @brief User Database Management
     * 
     * Manages user account data, authentication, and password operations.
     * Refactored from decompiled C source to modern C++20.
     */
    class CUserDB {
    public:
        // Constructor and destructor
        CUserDB();
        ~CUserDB();

        // Core lifecycle
        bool Initialize();
        void Shutdown();
        bool LoadConfiguration();

        // Account management
        bool CreateAccount(const std::string& accountName, const std::string& password,
                          BillingType billingType = BillingType::Free);
        bool DeleteAccount(const std::string& accountName);
        bool UpdateAccount(const AccountInfo& accountInfo);
        AccountInfo GetAccount(const std::string& accountName) const;
        AccountInfo GetAccount(uint32_t accountSerial) const;
        bool AccountExists(const std::string& accountName) const;

        // Authentication
        AuthenticationResult AuthenticateUser(const std::string& accountName, const std::string& password);
        bool ValidateCredentials(const std::string& accountName, const std::string& password) const;
        bool IsAccountActive(const std::string& accountName) const;
        bool IsAccountLocked(const std::string& accountName) const;

        // Password management
        bool ChangePassword(const std::string& accountName, const std::string& oldPassword,
                          const std::string& newPassword);
        bool ResetPassword(const std::string& accountName, const std::string& newPassword);
        bool UpdateTrunkPassword(const std::string& accountName, const std::string& trunkPassword);
        std::string GetTrunkPassword(const std::string& accountName) const;

        // Account status management
        bool LockAccount(const std::string& accountName, const std::string& reason = "");
        bool UnlockAccount(const std::string& accountName);
        bool SuspendAccount(const std::string& accountName, const std::chrono::system_clock::time_point& until);
        bool ActivateAccount(const std::string& accountName);

        // Login tracking
        void RecordLogin(const std::string& accountName, const std::string& clientIP);
        void RecordLogout(const std::string& accountName);
        void UpdateLastActivity(const std::string& accountName);
        std::chrono::system_clock::time_point GetLastLogin(const std::string& accountName) const;
        uint32_t GetLoginCount(const std::string& accountName) const;

        // Billing integration
        bool UpdateBillingType(const std::string& accountName, BillingType billingType);
        BillingType GetBillingType(const std::string& accountName) const;
        bool SetBillingInfo(const std::string& accountName, const BillingInfo& billingInfo);
        BillingInfo GetBillingInfo(const std::string& accountName) const;

        // Database operations
        bool SaveToDatabase();
        bool LoadFromDatabase();
        bool BackupDatabase(const std::string& backupPath = "");
        bool RestoreDatabase(const std::string& backupPath);

        // Statistics and information
        size_t GetAccountCount() const;
        size_t GetActiveAccountCount() const;
        std::vector<std::string> GetAccountList() const;
        std::vector<AccountInfo> GetAccountsByBillingType(BillingType billingType) const;

        // Configuration and state
        bool IsInitialized() const noexcept { return m_isInitialized; }
        const std::string& GetDatabasePath() const noexcept { return m_databasePath; }
        void SetDatabasePath(const std::string& path) { m_databasePath = path; }

        // Legacy C interface compatibility
        bool Update_TrunkPassword(char* accountName, char* trunkPassword);
        char* Get_TrunkPassword(char* accountName);
        bool Authenticate_User(char* accountName, char* password);

    private:
        // Internal methods
        bool LoadAccountsFromFile();
        bool SaveAccountsToFile();
        bool ValidateAccountName(const std::string& accountName) const;
        bool ValidatePassword(const std::string& password) const;
        uint32_t GenerateAccountSerial();
        void CleanupExpiredAccounts();
        std::string GetAccountFilePath(const std::string& accountName) const;

        // Security helpers
        bool CheckPasswordPolicy(const std::string& password) const;
        void LogAccountEvent(const std::string& event, const std::string& accountName);
        bool IsAccountNameReserved(const std::string& accountName) const;

        // Member variables
        std::unordered_map<std::string, AccountInfo> m_accounts;
        std::unordered_map<uint32_t, std::string> m_serialToName;
        std::unordered_map<std::string, std::string> m_trunkPasswords;
        std::unordered_map<std::string, BillingInfo> m_billingInfo;
        
        bool m_isInitialized{false};
        std::string m_databasePath;
        std::string m_configPath;
        uint32_t m_nextAccountSerial{1};
        
        mutable std::mutex m_accountsMutex;
        mutable std::mutex m_billingMutex;
        mutable std::mutex m_trunkPasswordMutex;

        // Configuration
        size_t m_maxAccountNameLength{MAX_ACCOUNT_NAME_LENGTH};
        size_t m_maxPasswordLength{MAX_PASSWORD_LENGTH};
        bool m_requireStrongPasswords{true};
        bool m_enableAccountLocking{true};
        uint32_t m_maxLoginAttempts{3};

        // Statistics
        struct DatabaseStatistics {
            uint32_t totalAccounts{0};
            uint32_t activeAccounts{0};
            uint32_t lockedAccounts{0};
            uint32_t suspendedAccounts{0};
            std::chrono::steady_clock::time_point startTime;
        };
        
        DatabaseStatistics m_statistics;
        mutable std::mutex m_statisticsMutex;
    };

    /**
     * @brief User database factory
     */
    class CUserDBFactory {
    public:
        static std::unique_ptr<CUserDB> CreateUserDB();
        static std::unique_ptr<CUserDB> CreateUserDB(const std::string& databasePath);
        static bool ValidateDatabasePath(const std::string& path);
    };

    // Legacy C interface
    extern "C" {
        struct CUserDB_Legacy {
            char m_szDatabasePath[256];
            bool m_bInitialized;
            uint32_t m_dwAccountCount;
            uint32_t m_dwNextSerial;
        };

        struct USER_ACCOUNT_DATA {
            uint32_t dwAccountSerial;
            char szAccountName[MAX_ACCOUNT_NAME_LENGTH];
            char szPasswordHash[64];
            uint8_t byBillingType;
            bool bActive;
            bool bLocked;
            _SYSTEMTIME stLastLogin;
            _SYSTEMTIME stCreationDate;
            uint32_t dwLoginCount;
        };

        // Legacy function declarations
        CUserDB_Legacy* CUserDB_Create();
        void CUserDB_Destroy(CUserDB_Legacy* userDB);
        bool CUserDB_Initialize(CUserDB_Legacy* userDB);
        bool CUserDB_CreateAccount(CUserDB_Legacy* userDB, const char* accountName, 
                                  const char* password, uint8_t billingType);
        bool CUserDB_DeleteAccount(CUserDB_Legacy* userDB, const char* accountName);
        bool CUserDB_AuthenticateUser(CUserDB_Legacy* userDB, const char* accountName, 
                                     const char* password);
        bool CUserDB_ChangePassword(CUserDB_Legacy* userDB, const char* accountName,
                                   const char* oldPassword, const char* newPassword);
        bool CUserDB_Update_TrunkPassword(CUserDB_Legacy* userDB, char* accountName, 
                                         char* trunkPassword);
        char* CUserDB_Get_TrunkPassword(CUserDB_Legacy* userDB, char* accountName);
        bool CUserDB_LockAccount(CUserDB_Legacy* userDB, const char* accountName);
        bool CUserDB_UnlockAccount(CUserDB_Legacy* userDB, const char* accountName);
        uint32_t CUserDB_GetAccountCount(CUserDB_Legacy* userDB);
    }

    // Global instance access
    CUserDB& GetUserDB();

} // namespace NexusProtection::Authentication

// Global legacy compatibility
extern NexusProtection::Authentication::CUserDB* g_pUserDB;
