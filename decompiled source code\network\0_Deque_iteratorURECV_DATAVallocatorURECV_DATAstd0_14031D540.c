/*
 * Function: ??0?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA@AEBV01@@Z
 * Address: 0x14031D540
 */

void __fastcall std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v5->_Mycont,
    (std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&__that->_Mycont);
}
