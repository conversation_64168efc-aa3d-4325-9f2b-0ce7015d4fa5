/*
 * Function: ?CreateComplete@CMoveMapLimitRightPortal@@UEAAXPEAVCPlayer@@@Z
 * Address: 0x1403AC890
 */

void __fastcall CMoveMapLimitRightPortal::CreateComplete(CMoveMapLimitRightPortal *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char pbyType; // [sp+34h] [bp-44h]@5
  char v6; // [sp+35h] [bp-43h]@5
  _notice_move_limit_map_msg_zocl v7; // [sp+54h] [bp-24h]@5
  CMoveMapLimitRightPortal *v8; // [sp+80h] [bp+8h]@1
  CPlayer *v9; // [sp+88h] [bp+10h]@1

  v9 = pkPlayer;
  v8 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bNotifyForceMoveStartPosition )
  {
    pbyType = 59;
    v6 = 1;
    _notice_move_limit_map_msg_zocl::_notice_move_limit_map_msg_zocl(&v7);
    v7.byType = 2;
    CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &v7.byType, 1u);
    v8->m_bNotifyForceMoveStartPosition = 0;
  }
}
