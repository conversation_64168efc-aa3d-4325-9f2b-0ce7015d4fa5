/*
 * Function: ?get@?$CHashMapPtrPool@HVCNationCodeStr@@@@QEAA_NHAEAPEAVCNationCodeStr@@@Z
 * Address: 0x14020BC40
 */

char __fastcall CHashMapPtrPool<int,CNationCodeStr>::get(CHashMapPtrPool<int,CNationCodeStr> *this, int kKey, CNationCodeStr **pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@5
  std::pair<int const ,CNationCodeStr *> *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-88h]@1
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> result; // [sp+28h] [bp-60h]@4
  bool v9; // [sp+44h] [bp-44h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> v10; // [sp+48h] [bp-40h]@4
  char v11; // [sp+60h] [bp-28h]@5
  char v12; // [sp+61h] [bp-27h]@6
  __int64 v13; // [sp+68h] [bp-20h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> *v14; // [sp+70h] [bp-18h]@4
  std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *v15; // [sp+78h] [bp-10h]@4
  CHashMapPtrPool<int,CNationCodeStr> *v16; // [sp+90h] [bp+8h]@1
  int _Keyval; // [sp+98h] [bp+10h]@1
  CNationCodeStr **v18; // [sp+A0h] [bp+18h]@1

  v18 = pData;
  _Keyval = kKey;
  v16 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = -2i64;
  stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::find(
    (stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *)&v16->m_mapData._Myfirstiter,
    &result,
    &_Keyval);
  v14 = stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>,0>>::end(
          (stdext::_Hash<stdext::_Hmap_traits<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,CNationCodeStr *> >,0> > *)&v16->m_mapData._Myfirstiter,
          &v10);
  v15 = (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)v14;
  v9 = std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Const_iterator<0>::operator==(
         (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)&v14->_Mycont,
         (std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Const_iterator<0> *)&result._Mycont);
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&v10);
  if ( v9 )
  {
    v11 = 0;
    std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&result);
    v5 = v11;
  }
  else
  {
    v6 = std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::operator->(&result);
    *v18 = v6->second;
    v12 = 1;
    std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(&result);
    v5 = v12;
  }
  return v5;
}
