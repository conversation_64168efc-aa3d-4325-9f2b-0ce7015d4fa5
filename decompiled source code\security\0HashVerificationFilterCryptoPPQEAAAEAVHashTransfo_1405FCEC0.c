/*
 * Function: ??0HashVerificationFilter@CryptoPP@@QEAA@AEAVHashTransformation@1@PEAVBufferedTransformation@1@I@Z
 * Address: 0x1405FCEC0
 */

CryptoPP::HashVerificationFilter *__fastcall CryptoPP::HashVerificationFilter::HashVerificationFilter(CryptoPP::HashVerificationFilter *this, struct CryptoPP::HashTransformation *a2, struct CryptoPP::BufferedTransformation *a3, int a4)
{
  CryptoPP::Name *v4; // rcx@1
  const char *v5; // rax@1
  __int64 v6; // r9@1
  struct CryptoPP::NameValuePairs *v7; // rax@1
  char v9; // [sp+20h] [bp-48h]@1
  __int64 v10; // [sp+48h] [bp-20h]@1
  struct CryptoPP::NameValuePairs *v11; // [sp+50h] [bp-18h]@1
  struct CryptoPP::NameValuePairs *v12; // [sp+58h] [bp-10h]@1
  CryptoPP::HashVerificationFilter *v13; // [sp+70h] [bp+8h]@1
  struct CryptoPP::HashTransformation *v14; // [sp+78h] [bp+10h]@1
  int v15; // [sp+88h] [bp+20h]@1

  v15 = a4;
  v14 = a2;
  v13 = this;
  v10 = -2i64;
  CryptoPP::FilterWithBufferedInput::FilterWithBufferedInput((CryptoPP::FilterWithBufferedInput *)&this->vfptr, a3);
  v13->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::HashVerificationFilter::`vftable'{for `CryptoPP::Algorithm'};
  v13->vfptr = (CryptoPP::WaitableVtbl *)&CryptoPP::HashVerificationFilter::`vftable'{for `CryptoPP::Waitable'};
  v13->m_hashModule = v14;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v13->m_expectedHash,
    0i64);
  v5 = CryptoPP::Name::HashVerificationFilterFlags(v4);
  LOBYTE(v6) = 1;
  LODWORD(v7) = CryptoPP::MakeParameters<unsigned int>(&v9, v5, &v15, v6);
  v11 = v7;
  v12 = v7;
  CryptoPP::FilterWithBufferedInput::IsolatedInitialize((CryptoPP::FilterWithBufferedInput *)&v13->vfptr, v7);
  CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned int>::~AlgorithmParameters<CryptoPP::NullNameValuePairs,unsigned int>(&v9);
  return v13;
}
