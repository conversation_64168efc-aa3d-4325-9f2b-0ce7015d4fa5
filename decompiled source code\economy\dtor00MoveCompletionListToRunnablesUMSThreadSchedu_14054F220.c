/*
 * Function: ?dtor$0@?0??MoveCompletionListToRunnables@UMSThreadScheduler@details@Concurrency@@QEAA_NVlocation@3@@Z@4HA
 * Address: 0x14054F220
 */

void __fastcall `Concurrency::details::UMSThreadScheduler::MoveCompletionListToRunnables'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<unsigned int,std::allocator<unsigned int>>::~_Vector_iterator<unsigned int,std::allocator<unsigned int>>(*(std::_Vector_iterator<unsigned int,std::allocator<unsigned int> > **)(a2 + 152));
}
