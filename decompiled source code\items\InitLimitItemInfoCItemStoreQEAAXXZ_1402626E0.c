/*
 * Function: ?InitLimitItemInfo@CItemStore@@QEAAXXZ
 * Address: 0x1402626E0
 */

void __fastcall CItemStore::InitLimitItemInfo(CItemStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  int v5; // [sp+24h] [bp-14h]@4
  _base_fld *v6; // [sp+28h] [bp-10h]@9
  CItemStore *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -1;
  for ( j = 0; (signed int)j < 16; ++j )
  {
    v5 = GetItemTableCode(v7->m_pRec->m_sellLimitList[j].m_strItemCode);
    if ( v5 == -1 )
    {
      _limit_item_info::init(&v7->m_pLimitStorageItem[j]);
    }
    else
    {
      v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v5, v7->m_pRec->m_sellLimitList[j].m_strItemCode);
      if ( v6 )
      {
        v7->m_pLimitStorageItem[j].bLoad = 1;
        v7->m_pLimitStorageItem[j].dwStorageIndex = j;
        v7->m_pLimitStorageItem[j].Key.byTableCode = v5;
        v7->m_pLimitStorageItem[j].Key.wItemIndex = v6->m_dwIndex;
        v7->m_pLimitStorageItem[j].nLimitNum = v7->m_pRec->m_sellLimitList[j].m_nMaxCount;
      }
      else
      {
        _limit_item_info::init(&v7->m_pLimitStorageItem[j]);
      }
    }
  }
}
