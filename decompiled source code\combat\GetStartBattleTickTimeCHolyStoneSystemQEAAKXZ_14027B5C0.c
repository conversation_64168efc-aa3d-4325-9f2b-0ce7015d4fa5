/*
 * Function: ?GetStartBattleTickTime@CHolyStoneSystem@@QEAAKXZ
 * Address: 0x14027B5C0
 */

__int64 __fastcall CHolyStoneSystem::GetStartBattleTickTime(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  ATL::CTime *v4; // rax@4
  ATL::CTime *v5; // rax@4
  int v6; // eax@4
  int v7; // eax@4
  __int64 v9; // [sp+0h] [bp-98h]@1
  ATL::CTimeSpan v10; // [sp+48h] [bp-50h]@4
  int v11; // [sp+54h] [bp-44h]@4
  ATL::CTime result; // [sp+58h] [bp-40h]@4
  ATL::CTime v13; // [sp+60h] [bp-38h]@4
  int v14; // [sp+68h] [bp-30h]@4
  int v15; // [sp+6Ch] [bp-2Ch]@4
  int nDay; // [sp+70h] [bp-28h]@4
  int nMonth; // [sp+74h] [bp-24h]@4
  ATL::CTime *v18; // [sp+78h] [bp-20h]@4
  __int64 v19; // [sp+80h] [bp-18h]@4
  __int64 v20; // [sp+88h] [bp-10h]@4
  CHolyStoneSystem *v21; // [sp+A0h] [bp+8h]@1

  v21 = this;
  v1 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v14 = (unsigned __int8)CHolyStoneSystem::GetStartMin(v21);
  v15 = (unsigned __int8)CHolyStoneSystem::GetStartHour(v21);
  nDay = (unsigned __int8)CHolyStoneSystem::GetStartDay(v21);
  nMonth = (unsigned __int8)CHolyStoneSystem::GetStartMonth(v21);
  v3 = CHolyStoneSystem::GetStartYear(v21);
  ATL::CTime::CTime(&v13, v3, nMonth, nDay, v15, v14, 0, -1);
  v18 = v4;
  v5 = ATL::CTime::GetTickCount(&result);
  ATL::CTime::operator-(v5, &v10, (ATL::CTime)v18->m_time);
  v19 = 24 * ATL::CTimeSpan::GetDays(&v10);
  v6 = ATL::CTimeSpan::GetHours(&v10);
  v20 = 60 * (v6 + v19);
  v7 = ATL::CTimeSpan::GetMinutes(&v10);
  v11 = v7 + v20;
  return GetLoopTime() - 60000 * v11;
}
