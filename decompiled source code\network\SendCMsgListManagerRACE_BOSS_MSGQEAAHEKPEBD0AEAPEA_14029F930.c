/*
 * Function: ?Send@CMsgListManager@RACE_BOSS_MSG@@QEAAHEKPEBD0AEAPEAVCMsg@2@K@Z
 * Address: 0x14029F930
 */

signed __int64 __fastcall RACE_BOSS_MSG::CMsgListManager::Send(RACE_BOSS_MSG::CMsgListManager *this, char ucRace, unsigned int dwSerial, const char *pwszName, const char *pwszMsg, RACE_BOSS_MSG::CMsg **pkSend, unsigned int dbWebSendDBID)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v10; // [sp+0h] [bp-48h]@1
  RACE_BOSS_MSG::CMsg *pkMsg; // [sp+30h] [bp-18h]@11
  RACE_BOSS_MSG::CMsgListManager *v12; // [sp+50h] [bp+8h]@1
  char v13; // [sp+58h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+60h] [bp+18h]@1
  char *pwszNamea; // [sp+68h] [bp+20h]@1

  pwszNamea = (char *)pwszName;
  dwSeriala = dwSerial;
  v13 = ucRace;
  v12 = this;
  v7 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( v12->m_bEmpty )
  {
    result = 1i64;
  }
  else if ( (signed int)(unsigned __int8)ucRace < 3 && pwszName && pwszMsg && strlen_0(pwszMsg) )
  {
    pkMsg = RACE_BOSS_MSG::CMsgList::GetEmpty(v12->m_pkMsgList[(unsigned __int8)v13]);
    if ( pkMsg )
    {
      RACE_BOSS_MSG::CMsg::Set(pkMsg, dwSeriala, pwszNamea, pwszMsg, dbWebSendDBID);
      RACE_BOSS_MSG::CMsgList::AddUse(v12->m_pkMsgList[(unsigned __int8)v13], pkMsg);
      if ( RACE_BOSS_MSG::CMsgList::Save(v12->m_pkMsgList[(unsigned __int8)v13]) )
      {
        if ( WritePrivateProfileStringA("RaceBossSMSSave", "Flag", "TRUE", "..\\SystemSave\\ServerState.ini") )
        {
          *pkSend = pkMsg;
          result = 0i64;
        }
        else
        {
          RACE_BOSS_MSG::CMsgList::RollBack(v12->m_pkMsgList[(unsigned __int8)v13]);
          result = 1i64;
        }
      }
      else
      {
        RACE_BOSS_MSG::CMsgList::RollBack(v12->m_pkMsgList[(unsigned __int8)v13]);
        result = 1i64;
      }
    }
    else
    {
      result = 4i64;
    }
  }
  else
  {
    result = 1i64;
  }
  return result;
}
