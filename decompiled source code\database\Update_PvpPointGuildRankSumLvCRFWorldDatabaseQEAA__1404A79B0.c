/*
 * Function: ?Update_PvpPointGuildRankSumLv@CRFWorldDatabase@@QEAA_NPEADEEE@Z
 * Address: 0x1404A79B0
 */

bool __fastcall CRFWorldDatabase::Update_PvpPointGuildRankSumLv(CRFWorldDatabase *this, char *szDate, char byRace, char byLimitCnt, char byLimitGrade)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-488h]@1
  char *v9; // [sp+20h] [bp-468h]@4
  char *v10; // [sp+28h] [bp-460h]@4
  int v11; // [sp+30h] [bp-458h]@4
  int v12; // [sp+38h] [bp-450h]@4
  char *v13; // [sp+40h] [bp-448h]@4
  char Dst; // [sp+60h] [bp-428h]@4
  unsigned __int64 v15; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+490h] [bp+8h]@1
  char *v17; // [sp+498h] [bp+10h]@1
  char v18; // [sp+4A0h] [bp+18h]@1
  char v19; // [sp+4A8h] [bp+20h]@1

  v19 = byLimitCnt;
  v18 = byRace;
  v17 = szDate;
  v16 = this;
  v5 = &v8;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v15 = (unsigned __int64)&v8 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  v13 = v17;
  v12 = (unsigned __int8)byLimitGrade;
  v11 = (unsigned __int8)v18;
  v10 = v17;
  v9 = v17;
  sprintf(
    &Dst,
    "update [dbo].[tbl_PvpPointGuildRank%s] set sumlv = nsl.newsumlv from ( select top %u serial, ( select sum(lv) from t"
    "bl_base as b join tbl_general as g on g.guildserial = [dbo].[tbl_PvpPointGuildRank%s].serial and b.serial = g.serial"
    " and b.dck = 0 ) as newsumlv from [dbo].[tbl_PvpPointGuildRank%s] where serial in ( select top 5 r.serial from [dbo]"
    ".[tbl_GuildBattleRank] as r join [dbo].[tbl_Guild] as g on r.serial = g.Serial where g.dck = 0 and g.race = %u and g"
    ".grade >= %u and ( r.win > 0 or r.lose > 0 or r.draw > 0 ) order by score desc, win desc, draw desc, lose ) order by"
    " sumpvppoint desc, killpvppoint desc, newsumlv desc ) as nsl where [dbo].[tbl_PvpPointGuildRank%s].serial = nsl.serial",
    v17,
    (unsigned __int8)v19);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v16->vfptr, &Dst, 0);
}
