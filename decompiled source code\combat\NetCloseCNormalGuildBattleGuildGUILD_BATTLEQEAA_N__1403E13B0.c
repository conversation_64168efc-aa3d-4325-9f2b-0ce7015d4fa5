/*
 * Function: ?NetClose@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_N_NKAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E13B0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::NetClose(GUILD_BATTLE::CNormalGuildBattleGuild *this, bool bInGuildBattle, unsigned int dwSerial, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  int iMemberInx; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v9; // [sp+40h] [bp+8h]@1
  bool v10; // [sp+48h] [bp+10h]@1

  v10 = bInGuildBattle;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  iMemberInx = GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(v9, dwSerial);
  if ( iMemberInx >= 0 )
  {
    if ( v10 )
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::SendDeleteNotifyPositionMember(v9, iMemberInx);
      GUILD_BATTLE::CNormalGuildBattleGuildMember::NetClose(&v9->m_kMember[iMemberInx]);
    }
    if ( v9->m_dwCurJoinMember >= 1 )
      --v9->m_dwCurJoinMember;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
