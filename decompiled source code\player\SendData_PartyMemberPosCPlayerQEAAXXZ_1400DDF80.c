/*
 * Function: ?SendData_PartyMemberPos@CPlayer@@QEAAXXZ
 * Address: 0x1400DDF80
 */

void __fastcall CPlayer::SendData_PartyMemberPos(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  CPartyPlayer **v4; // [sp+30h] [bp-78h]@5
  char szMsg[4]; // [sp+48h] [bp-60h]@6
  char v6; // [sp+4Ch] [bp-5Ch]@6
  __int16 v7; // [sp+4Dh] [bp-5Bh]@6
  __int16 v8; // [sp+4Fh] [bp-59h]@6
  int v9; // [sp+64h] [bp-44h]@6
  char pbyType; // [sp+74h] [bp-34h]@6
  char v11; // [sp+75h] [bp-33h]@6
  int j; // [sp+84h] [bp-24h]@6
  unsigned __int64 v13; // [sp+90h] [bp-18h]@4
  CPlayer *v14; // [sp+B0h] [bp+8h]@1

  v14 = this;
  v1 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v3 ^ _security_cookie;
  if ( v14->m_pPartyMgr )
  {
    v4 = CPartyPlayer::GetPtrPartyMember(v14->m_pPartyMgr);
    if ( v4 )
    {
      *(_DWORD *)szMsg = v14->m_dwObjSerial;
      v6 = CPlayerDB::GetMapCode(&v14->m_Param);
      v7 = (signed int)ffloor(v14->m_fCurPos[0]);
      v8 = (signed int)ffloor(v14->m_fCurPos[2]);
      v9 = CPartyPlayer::GetPopPartyMember(v14->m_pPartyMgr);
      pbyType = 16;
      v11 = 24;
      for ( j = 0; j < v9; ++j )
      {
        if ( v4[j] != v14->m_pPartyMgr )
          CNetProcess::LoadSendMsg(unk_1414F2088, v4[j]->m_wZoneIndex, &pbyType, szMsg, 9u);
      }
    }
  }
}
