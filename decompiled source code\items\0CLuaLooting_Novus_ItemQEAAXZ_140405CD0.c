/*
 * Function: ??0CLuaLooting_Novus_Item@@QEAA@XZ
 * Address: 0x140405CD0
 */

void __fastcall CLuaLooting_Novus_Item::CLuaLooting_Novus_Item(CLuaLooting_Novus_Item *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CLuaLooting_Novus_Item *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _STORAGE_LIST::_db_con::_db_con(&v4->m_Item);
}
