/*
 * Function: ??1?$DL_GroupParametersImpl@V?$EcPrecomputation@VECP@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@2@V?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140449CD0
 */

void __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::~DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>(CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::~DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>((CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *)((char *)&v5[-1].m_gpc + 8));
  CryptoPP::EcPrecomputation<CryptoPP::ECP>::~EcPrecomputation<CryptoPP::ECP>((CryptoPP::EcPrecomputation<CryptoPP::ECP> *)&v5[-1].gap18[8]);
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::~DL_GroupParameters<CryptoPP::ECPPoint>((CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)v5[-1].m_groupPrecomputation);
}
