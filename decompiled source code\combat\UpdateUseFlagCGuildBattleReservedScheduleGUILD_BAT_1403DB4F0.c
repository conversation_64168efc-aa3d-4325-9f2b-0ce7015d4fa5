/*
 * Function: ?UpdateUseFlag@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@K@Z
 * Address: 0x1403DB4F0
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseFlag(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwID)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedule *result; // rax@5
  unsigned int v5; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = dwID % 0x17;
  if ( v6->m_pkSchedule[dwID % 0x17] )
  {
    v6->m_bUseField[v5] = 1;
    result = v6->m_pkSchedule[v5];
  }
  else
  {
    result = 0i64;
  }
  return result;
}
