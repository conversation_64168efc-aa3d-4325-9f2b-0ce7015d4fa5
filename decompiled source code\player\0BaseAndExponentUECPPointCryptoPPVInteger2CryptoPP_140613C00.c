/*
 * Function: ??0?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140613C00
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(CryptoPP::ECPPoint *a1)
{
  CryptoPP::ECPPoint *v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::ECPPoint::ECPPoint(a1);
  CryptoPP::Integer::Integer(&v2[1]);
  return v2;
}
