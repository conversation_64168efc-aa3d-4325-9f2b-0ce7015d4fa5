/*
 * Function: j_?Insert_Start_NpcQuest_History@CRFWorldDatabase@@QEAA_NKPEADE0_J@Z
 * Address: 0x14000BDBB
 */

bool __fastcall CRFWorldDatabase::Insert_Start_NpcQuest_History(CRFWorldDatabase *this, unsigned int dwSerial, char *szQuestCode, char byLevel, char *szTime, __int64 nEndTime)
{
  return CRFWorldDatabase::Insert_Start_NpcQuest_History(this, dwSerial, szQuestCode, byLevel, szTime, nEndTime);
}
