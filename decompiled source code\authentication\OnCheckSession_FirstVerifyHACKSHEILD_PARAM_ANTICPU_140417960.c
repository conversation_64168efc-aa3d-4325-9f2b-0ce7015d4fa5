/*
 * Function: ?OnCheckSession_FirstVerify@HACKSHEILD_PARAM_ANTICP@@UEAA_NH@Z
 * Address: 0x140417960
 */

char __fastcall HACKSHEILD_PARAM_ANTICP::OnCheckSession_FirstVerify(HACKSHEILD_PARAM_ANTICP *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  HACKSHEILD_PARAM_ANTICP *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = n;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (unsigned __int8)((int (__fastcall *)(HACKSHEILD_PARAM_ANTICP *))v6->vfptr->IsLogPass)(v6) )
  {
    result = 1;
  }
  else
  {
    v6->m_nSocketIndex = v7;
    HACKSHEILD_PARAM_ANTICP::Kick(v6, 4, 0);
    result = 0;
  }
  return result;
}
