/*
 * Function: j_?pc_<PERSON>A<PERSON><PERSON>_Skill@CPlayer@@QEAAXPEAVCCharacter@@PEAMEGGPEAGG@Z
 * Address: 0x14000908E
 */

void __fastcall CPlayer::pc_PlayAttack_Skill(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byEffectC<PERSON>, unsigned __int16 wSkillIndex, unsigned __int16 wBulletSerial, unsigned __int16 *pConsumeSerial, unsigned __int16 wEffBtSerial)
{
  CPlayer::pc_PlayAttack_Skill(
    this,
    pDst,
    pfAttackPos,
    byEffectCode,
    wSkillIndex,
    wBulletSerial,
    pConsumeSerial,
    wEffBtSerial);
}
