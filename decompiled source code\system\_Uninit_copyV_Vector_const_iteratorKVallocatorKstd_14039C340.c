/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@KV?$allocator@K@std@@@std@@PEAKV?$allocator@K@2@@std@@YAPEAKV?$_Vector_const_iterator@KV?$allocator@K@std@@@0@0PEAKAEAV?$allocator@K@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14039C340
 */

unsigned int *__fastcall std::_Uninit_copy<std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>,unsigned long *,std::allocator<unsigned long>>(std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_First, std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_Last, unsigned int *_Dest, std::allocator<unsigned long> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-58h]@1
  unsigned int *v10; // [sp+20h] [bp-38h]@4
  unsigned int *v11; // [sp+28h] [bp-30h]@7
  __int64 v12; // [sp+30h] [bp-28h]@4
  bool v13; // [sp+38h] [bp-20h]@5
  unsigned int *_Val; // [sp+40h] [bp-18h]@6
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *v15; // [sp+60h] [bp+8h]@1
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_Right; // [sp+68h] [bp+10h]@1
  unsigned int *_Ptr; // [sp+70h] [bp+18h]@1
  std::allocator<unsigned long> *v18; // [sp+78h] [bp+20h]@1

  v18 = _Al;
  _Ptr = _Dest;
  _Right = _Last;
  v15 = _First;
  v6 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v12 = -2i64;
  v10 = _Dest;
  while ( 1 )
  {
    v13 = std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::operator!=(v15, _Right);
    if ( !v13 )
      break;
    _Val = (unsigned int *)std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::operator*(v15);
    std::allocator<unsigned long>::construct(v18, _Ptr, _Val);
    ++_Ptr;
    std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::operator++(v15);
  }
  v11 = _Ptr;
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::~_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>(v15);
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>::~_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>(_Right);
  return v11;
}
