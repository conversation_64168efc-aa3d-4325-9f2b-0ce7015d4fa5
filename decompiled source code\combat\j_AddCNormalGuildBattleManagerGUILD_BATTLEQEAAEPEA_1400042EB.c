/*
 * Function: j_?Add@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEPEAVCGuild@@0KKEK@Z
 * Address: 0x1400042EB
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Add(GUILD_BATTLE::CNormalGuildBattleManager *this, CGuild *pSrcGuild, CGuild *pDestGuild, unsigned int dwStartTime, unsigned int dwElapseTimeCnt, char byNumber, unsigned int dwMapCode)
{
  return GUILD_BATTLE::CNormalGuildBattleManager::Add(
           this,
           pSrcGuild,
           pDestGuild,
           dwStartTime,
           dwElapseTimeCnt,
           byNumber,
           dwMapCode);
}
