/*
 * Function: _dynamic_initializer_for__g_TransportShip__
 * Address: 0x1406DFAC0
 */

__int64 dynamic_initializer_for__g_TransportShip__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v0 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector constructor iterator'(
    g_TransportShip,
    0x13D90ui64,
    3,
    (void (__cdecl *)(void *))CTransportShip::CTransportShip,
    (void (__cdecl *)(void *))CTransportShip::~CTransportShip);
  return atexit(dynamic_atexit_destructor_for__g_TransportShip__);
}
