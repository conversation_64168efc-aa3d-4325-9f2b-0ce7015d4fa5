/*
 * CPlayerEquipment.h - Modern Player Equipment System
 * Refactored from decompiled pc_EquipPart function (Address: 0x1400AD960)
 * Provides type-safe, modern C++ implementation of equipment operations
 */

#pragma once

#include <string>
#include <memory>
#include <optional>
#include <chrono>
#include <functional>
#include "../../items/Headers/ItemCodes.h"

// Forward declarations
class CPlayer;
struct _STORAGE_POS_INDIV;
struct _STORAGE_LIST;
struct _base_fld;

namespace NexusProtection {
namespace Player {

/**
 * Equipment operation error codes (from pc_EquipPart function analysis)
 * These match the exact error codes used in the original decompiled function
 */
enum class EquipmentErrorCode : int8_t {
    Success = 0,                    // No error, operation successful (v7 = 0)
    ItemNotFound = 2,              // Item not found in inventory (v7 = 2, line 90)
    InvalidTableCode = 3,          // Item table code >= 8, not equipment (v7 = 3, line 85)
    EquipmentRequirementFailed = 7, // Equipment requirement check failed (v7 = 7, line 74)
    PlayerStateError = 8,          // Player in invalid state (effect states 20/28) (v7 = 8, lines 41,45)
    GradeRequirementFailed = 9,    // Player grade insufficient (v7 = 9, line 78)
    ItemLocked = 10,               // Item is locked or slot occupied (v7 = 10, lines 59,66)
    SystemError = -1               // System/storage operation failed (SendMsg_EquipPartResult(-1))
};

// Use EquipmentSlot from ItemCodes.h
using EquipmentSlot = Items::EquipmentSlot;

/**
 * Equipment operation context
 */
struct EquipmentContext {
    CPlayer* pPlayer{nullptr};
    _STORAGE_POS_INDIV* pItemPosition{nullptr};
    EquipmentSlot targetSlot{EquipmentSlot::Invalid};
    std::string operationId;
    std::chrono::high_resolution_clock::time_point startTime;
    
    bool IsValid() const {
        return pPlayer != nullptr && pItemPosition != nullptr;
    }
};

/**
 * Equipment operation result
 */
struct EquipmentResult {
    EquipmentErrorCode errorCode{EquipmentErrorCode::Success};
    std::string errorMessage;
    std::optional<_STORAGE_LIST*> previousItem;
    std::chrono::milliseconds executionTime{0};
    
    bool IsSuccess() const {
        return errorCode == EquipmentErrorCode::Success;
    }
    
    std::string GetErrorString() const {
        switch (errorCode) {
            case EquipmentErrorCode::Success: return "Success";
            case EquipmentErrorCode::ItemNotFound: return "Item not found in inventory";
            case EquipmentErrorCode::InvalidTableCode: return "Item is not equipment";
            case EquipmentErrorCode::EquipmentRequirementFailed: return "Equipment requirements not met";
            case EquipmentErrorCode::PlayerStateError: return "Player in invalid state";
            case EquipmentErrorCode::GradeRequirementFailed: return "Insufficient grade requirement";
            case EquipmentErrorCode::ItemLocked: return "Item is locked or slot occupied";
            case EquipmentErrorCode::SystemError: return "System error during operation";
            default: return "Unknown error";
        }
    }
};

/**
 * Modern C++ Player Equipment System
 * Refactored from pc_EquipPart function with improved safety and maintainability
 */
class CPlayerEquipment {
public:
    /**
     * Constructor
     */
    CPlayerEquipment();
    
    /**
     * Destructor
     */
    ~CPlayerEquipment() = default;
    
    /**
     * Equip item to player (main refactored pc_EquipPart function)
     * @param context Equipment operation context
     * @return Equipment operation result
     */
    EquipmentResult EquipItem(const EquipmentContext& context);
    
    /**
     * Validate equipment requirements
     * @param pPlayer Player instance
     * @param pItem Item to validate
     * @return Error code or Success
     */
    EquipmentErrorCode ValidateEquipmentRequirements(CPlayer* pPlayer, _STORAGE_LIST* pItem);
    
    /**
     * Get equipment slot from table code
     * @param tableCode Item table code
     * @return Equipment slot
     */
    static EquipmentSlot GetEquipmentSlot(uint8_t tableCode);
    
    /**
     * Check if table code represents equipment
     * @param tableCode Item table code
     * @return true if equipment (0-7)
     */
    static bool IsEquipmentTableCode(uint8_t tableCode);
    
    /**
     * Set equipment operation callback
     * @param callback Callback function for operation notifications
     */
    void SetEquipmentCallback(std::function<void(const EquipmentResult&)> callback);
    
    /**
     * Enable/disable detailed logging
     * @param enable Enable detailed logging
     */
    void SetDetailedLogging(bool enable);

private:
    std::function<void(const EquipmentResult&)> m_equipmentCallback;
    bool m_bDetailedLogging{false};
    
    /**
     * Validate player state for equipment operations
     * @param pPlayer Player instance
     * @return Error code or Success
     */
    EquipmentErrorCode ValidatePlayerState(CPlayer* pPlayer);
    
    /**
     * Perform storage operations for equipment
     * @param context Equipment context
     * @param pCurrentItem Current equipped item (if any)
     * @param pNewItem Item to equip
     * @return true if successful
     */
    bool PerformStorageOperations(const EquipmentContext& context, 
                                 _STORAGE_LIST* pCurrentItem, 
                                 _STORAGE_LIST* pNewItem);
    
    /**
     * Rollback storage operations on failure
     * @param context Equipment context
     * @param pOriginalItem Original item to restore
     */
    void RollbackStorageOperations(const EquipmentContext& context, 
                                  _STORAGE_LIST* pOriginalItem);
    
    /**
     * Create equipment result with timing
     * @param errorCode Result error code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete equipment result
     */
    EquipmentResult CreateResult(EquipmentErrorCode errorCode,
                               std::chrono::high_resolution_clock::time_point startTime,
                               const std::string& errorMessage = "");
    
    /**
     * Send equipment result to player
     * @param pPlayer Player instance
     * @param errorCode Error code to send
     */
    void SendEquipmentResult(CPlayer* pPlayer, EquipmentErrorCode errorCode);
};

} // namespace Player
} // namespace NexusProtection
