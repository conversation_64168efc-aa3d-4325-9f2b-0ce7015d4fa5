/*
 * Function: ?CompleteUpdateCheatRegistTime@CUnmannedTraderController@@QEAAXPEAD@Z
 * Address: 0x14034FF60
 */

void __fastcall CUnmannedTraderController::CompleteUpdateCheatRegistTime(CUnmannedTraderController *this, char *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v4; // rax@7
  CUnmannedTraderScheduler *v5; // rax@7
  int v6; // ecx@12
  __int64 v7; // [sp+0h] [bp-188h]@1
  bool bFilter[4]; // [sp+20h] [bp-168h]@12
  char *pwszMessage; // [sp+28h] [bp-160h]@12
  char *v10; // [sp+40h] [bp-148h]@4
  CPlayer *v11; // [sp+48h] [bp-140h]@4
  char Dest; // [sp+60h] [bp-128h]@10
  char v13; // [sp+61h] [bp-127h]@10
  unsigned __int8 j; // [sp+164h] [bp-24h]@10
  unsigned __int64 v15; // [sp+170h] [bp-18h]@4
  char *pLoadDataa; // [sp+198h] [bp+10h]@1

  pLoadDataa = pLoadData;
  v2 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  v10 = pLoadData;
  v11 = 0i64;
  if ( (signed int)*((_WORD *)pLoadData + 1) < 2532 )
  {
    if ( v10[1] )
    {
      v4 = CUnmannedTraderUserInfoTable::Instance();
      CUnmannedTraderUserInfoTable::CompleteUpdateCheatRegistTime(v4, pLoadDataa);
      v5 = CUnmannedTraderScheduler::Instance();
      CUnmannedTraderScheduler::CheatPushLoad(v5);
      v11 = GetPtrPlayerFromSerial(&g_Player, 2532, *((_DWORD *)v10 + 1));
      if ( v11 )
      {
        if ( v11->m_bOper )
        {
          Dest = 0;
          memset(&v13, 0, 0xFEui64);
          sprintf(&Dest, "Cnt : %u", (unsigned __int8)v10[1]);
          CPlayer::SendData_ChatTrans(v11, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          sprintf(&Dest, "th  serial  state  ret");
          CPlayer::SendData_ChatTrans(v11, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          for ( j = 0; j < (signed int)(unsigned __int8)v10[1]; ++j )
          {
            v6 = (unsigned __int8)v10[8 * j + 9];
            LODWORD(pwszMessage) = (unsigned __int8)v10[8 * j + 8];
            *(_DWORD *)bFilter = v6;
            sprintf(&Dest, " %u    %u       %u     %u", j, *(_DWORD *)&v10[8 * j + 12]);
            CPlayer::SendData_ChatTrans(v11, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
          }
        }
      }
    }
  }
}
