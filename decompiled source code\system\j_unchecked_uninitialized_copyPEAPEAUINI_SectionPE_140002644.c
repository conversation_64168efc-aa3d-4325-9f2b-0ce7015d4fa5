/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAUINI_Section@@PEAPEAU1@V?$allocator@PEAUINI_Section@@@std@@@stdext@@YAPEAPEAUINI_Section@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Section@@@std@@@Z
 * Address: 0x140002644
 */

INI_Section **__fastcall stdext::unchecked_uninitialized_copy<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(INI_Section **_First, INI_Section **_Last, INI_Section **_Dest, std::allocator<INI_Section *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
