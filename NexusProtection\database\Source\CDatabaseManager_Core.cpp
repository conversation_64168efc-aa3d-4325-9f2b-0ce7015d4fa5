/**
 * @file CDatabaseManager_Core.cpp
 * @brief Modern C++20 Database Manager core implementation
 * 
 * Refactored from decompiled sources:
 * - _GameDataBaseInitCMainThreadAEAA_NXZ_1401ED4E0.c (270 lines)
 * - DatabaseInitCMainThreadAEAA_NPEAD0Z_1401ED230.c (94 lines)
 * 
 * This file provides the core implementation of the CDatabaseManager class
 * with modern C++20 patterns, proper error handling, and modular design.
 */

#include "../Headers/CDatabaseManager.h"
#include "../Headers/CRFWorldDatabase.h"
#include "../Headers/CUserDB.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <thread>

// Legacy includes (to be gradually replaced)
extern "C" {
    // Forward declarations for legacy functions
    class CLogTypeDBTaskManager;
    class CMoneySupplyMgr;
    class CPvpUserAndGuildRankingSystem;
    class CGuildBattleController;
    
    // Legacy function declarations
    extern CLogTypeDBTaskManager* GetLogTypeDBTaskManager();
    extern bool InitLogTypeDBTaskManager(const char* odbcName, const char* serverIP, 
                                       const char* accountName, const char* password);
    extern CMoneySupplyMgr* GetMoneySupplyMgr();
    extern bool InitMoneySupplyMgr();
    extern bool LoadMoneySupplyHistory();
    extern CPvpUserAndGuildRankingSystem* GetPvpUserAndGuildRankingSystem();
    extern bool InitPvpUserAndGuildRankingSystem();
    extern CGuildBattleController* GetGuildBattleController();
    extern bool InitGuildBattleController();
}

namespace NexusProtection {
namespace Database {

// Static member definitions
std::unique_ptr<CDatabaseManager> CDatabaseManager::s_instance = nullptr;
std::mutex CDatabaseManager::s_instanceMutex;

// Constructor
CDatabaseManager::CDatabaseManager() 
    : m_state(DatabaseManagerState::Uninitialized)
    , m_lastUpdate(std::chrono::steady_clock::now())
    , m_lastHealthCheck(std::chrono::steady_clock::now())
{
    std::cout << "[INFO] CDatabaseManager constructor called" << std::endl;
    
    // Register default managers
    RegisterDefaultManagers();
}

// Destructor
CDatabaseManager::~CDatabaseManager() {
    std::cout << "[INFO] CDatabaseManager destructor called" << std::endl;
    
    try {
        Shutdown();
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CDatabaseManager destructor: " << e.what() << std::endl;
    }
}

DatabaseInitResult CDatabaseManager::Initialize(const DatabaseConfig& config) {
    std::lock_guard<std::mutex> lock(m_managerMutex);
    
    try {
        std::cout << "[INFO] Starting CDatabaseManager::Initialize" << std::endl;
        
        // Validate configuration
        if (!ValidateConfig(config)) {
            SetLastError("Invalid database configuration");
            return DatabaseInitResult::ConfigurationError;
        }
        
        // Set state and configuration
        m_state = DatabaseManagerState::Initializing;
        m_config = config;
        
        // Initialize database connections
        if (!InitializeDatabaseConnections()) {
            SetLastError("Failed to initialize database connections");
            m_state = DatabaseManagerState::Error;
            return DatabaseInitResult::ConnectionFailed;
        }
        
        // Set connected state
        m_state = DatabaseManagerState::Connected;
        
        std::cout << "[INFO] CDatabaseManager::Initialize completed successfully" << std::endl;
        return DatabaseInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::string("Exception during initialization: ") + e.what());
        m_state = DatabaseManagerState::Error;
        std::cerr << "[ERROR] Exception in CDatabaseManager::Initialize: " << e.what() << std::endl;
        return DatabaseInitResult::SystemError;
    }
}

DatabaseInitResult CDatabaseManager::InitializeGameData() {
    std::lock_guard<std::mutex> lock(m_managerMutex);
    
    try {
        std::cout << "[INFO] Starting CDatabaseManager::InitializeGameData" << std::endl;
        
        // Check if database is connected
        if (m_state != DatabaseManagerState::Connected) {
            SetLastError("Database not connected");
            return DatabaseInitResult::ConnectionFailed;
        }
        
        // Set managers loading state
        m_state = DatabaseManagerState::ManagersLoading;
        
        // Calculate initialization order
        if (!CalculateInitializationOrder()) {
            SetLastError("Failed to calculate manager initialization order");
            m_state = DatabaseManagerState::Error;
            return DatabaseInitResult::ManagerInitFailed;
        }
        
        // Initialize managers in dependency order
        if (!InitializeManagersInOrder()) {
            SetLastError("Failed to initialize managers");
            m_state = DatabaseManagerState::Error;
            return DatabaseInitResult::ManagerInitFailed;
        }
        
        // Set ready state
        m_state = DatabaseManagerState::Ready;
        
        std::cout << "[INFO] CDatabaseManager::InitializeGameData completed successfully" << std::endl;
        return DatabaseInitResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::string("Exception during game data initialization: ") + e.what());
        m_state = DatabaseManagerState::Error;
        std::cerr << "[ERROR] Exception in CDatabaseManager::InitializeGameData: " << e.what() << std::endl;
        return DatabaseInitResult::SystemError;
    }
}

bool CDatabaseManager::InitializeDatabaseConnections() {
    try {
        std::cout << "[DEBUG] Initializing database connections" << std::endl;
        
        // Initialize world database
        if (!InitializeWorldDatabase()) {
            std::cerr << "[ERROR] Failed to initialize world database" << std::endl;
            return false;
        }
        
        // Initialize user database
        if (!InitializeUserDatabase()) {
            std::cerr << "[ERROR] Failed to initialize user database" << std::endl;
            return false;
        }
        
        // Initialize log database
        if (!InitializeLogDatabase()) {
            std::cerr << "[ERROR] Failed to initialize log database" << std::endl;
            return false;
        }
        
        std::cout << "[DEBUG] Database connections initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in InitializeDatabaseConnections: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseManager::InitializeWorldDatabase() {
    try {
        std::cout << "[DEBUG] Initializing world database" << std::endl;
        
        // Create world database instance
        m_worldDatabase = std::make_shared<CRFWorldDatabase>();
        
        // Initialize with configuration
        if (!m_worldDatabase->Initialize(m_config.odbcName, m_config.serverIP, 
                                       m_config.accountName, m_config.password)) {
            std::cerr << "[ERROR] Failed to initialize CRFWorldDatabase" << std::endl;
            return false;
        }
        
        std::cout << "[DEBUG] World database initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in InitializeWorldDatabase: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseManager::InitializeUserDatabase() {
    try {
        std::cout << "[DEBUG] Initializing user database" << std::endl;
        
        // Create user database instance
        m_userDatabase = std::make_shared<CUserDB>();
        
        // Initialize with configuration
        if (!m_userDatabase->Initialize(m_config.odbcName, m_config.serverIP, 
                                      m_config.accountName, m_config.password)) {
            std::cerr << "[ERROR] Failed to initialize CUserDB" << std::endl;
            return false;
        }
        
        std::cout << "[DEBUG] User database initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in InitializeUserDatabase: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseManager::InitializeLogDatabase() {
    try {
        std::cout << "[DEBUG] Initializing log database" << std::endl;
        
        // Initialize log database using legacy function
        if (!InitLogTypeDBTaskManager(m_config.odbcName.c_str(), m_config.serverIP.c_str(),
                                    m_config.accountName.c_str(), m_config.password.c_str())) {
            std::cerr << "[ERROR] Failed to initialize log database" << std::endl;
            return false;
        }
        
        // Get log database instance
        CLogTypeDBTaskManager* logDB = GetLogTypeDBTaskManager();
        if (!logDB) {
            std::cerr << "[ERROR] Failed to get log database instance" << std::endl;
            return false;
        }
        
        // Store as shared pointer (would need wrapper for legacy compatibility)
        // m_logDatabase = std::shared_ptr<CLogTypeDBTaskManager>(logDB, [](CLogTypeDBTaskManager*){});
        
        std::cout << "[DEBUG] Log database initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in InitializeLogDatabase: " << e.what() << std::endl;
        return false;
    }
}

void CDatabaseManager::RegisterDefaultManagers() {
    try {
        std::cout << "[DEBUG] Registering default managers" << std::endl;
        
        // Register Economy System
        RegisterManager("MoneySupplyMgr", 
            []() { return InitMoneySupplyMgr(); },
            []() { return LoadMoneySupplyHistory(); },
            {} // No dependencies
        );
        
        // Register PvP System
        RegisterManager("PvpUserAndGuildRankingSystem",
            []() { return InitPvpUserAndGuildRankingSystem(); },
            []() { return true; }, // No separate load function
            {} // No dependencies
        );
        
        // Register Guild Battle System
        RegisterManager("GuildBattleController",
            []() { return InitGuildBattleController(); },
            []() { return true; }, // No separate load function
            {"PvpUserAndGuildRankingSystem"} // Depends on PvP system
        );
        
        // Additional managers would be registered here...
        
        std::cout << "[DEBUG] Default managers registered successfully" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in RegisterDefaultManagers: " << e.what() << std::endl;
    }
}

void CDatabaseManager::RegisterManager(const std::string& name, std::function<bool()> initFunc, 
                                     std::function<bool()> loadFunc, const std::vector<std::string>& deps) {
    std::lock_guard<std::mutex> lock(m_managerMutex);
    
    try {
        ManagerInfo manager;
        manager.name = name;
        manager.initFunction = std::move(initFunc);
        manager.loadFunction = std::move(loadFunc);
        manager.dependencies = deps;
        manager.isRequired = true;
        manager.isInitialized = false;
        
        m_managers[name] = std::move(manager);
        
        std::cout << "[DEBUG] Registered manager: " << name << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in RegisterManager: " << e.what() << std::endl;
    }
}

bool CDatabaseManager::CalculateInitializationOrder() {
    try {
        std::cout << "[DEBUG] Calculating manager initialization order" << std::endl;
        
        m_initializationOrder.clear();
        std::unordered_set<std::string> visited;
        std::unordered_set<std::string> visiting;
        
        // Perform topological sort
        std::function<bool(const std::string&)> visit = [&](const std::string& name) -> bool {
            if (visiting.find(name) != visiting.end()) {
                std::cerr << "[ERROR] Circular dependency detected involving: " << name << std::endl;
                return false; // Circular dependency
            }
            
            if (visited.find(name) != visited.end()) {
                return true; // Already processed
            }
            
            visiting.insert(name);
            
            auto it = m_managers.find(name);
            if (it != m_managers.end()) {
                for (const auto& dep : it->second.dependencies) {
                    if (!visit(dep)) {
                        return false;
                    }
                }
            }
            
            visiting.erase(name);
            visited.insert(name);
            m_initializationOrder.push_back(name);
            
            return true;
        };
        
        // Visit all managers
        for (const auto& pair : m_managers) {
            if (!visit(pair.first)) {
                return false;
            }
        }
        
        std::cout << "[DEBUG] Manager initialization order calculated successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CalculateInitializationOrder: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseManager::InitializeManagersInOrder() {
    try {
        std::cout << "[DEBUG] Initializing managers in dependency order" << std::endl;
        
        for (const auto& managerName : m_initializationOrder) {
            auto it = m_managers.find(managerName);
            if (it == m_managers.end()) {
                continue;
            }
            
            auto& manager = it->second;
            
            std::cout << "[DEBUG] Initializing manager: " << managerName << std::endl;
            manager.initTime = std::chrono::steady_clock::now();
            
            // Initialize manager
            if (manager.initFunction && !manager.initFunction()) {
                manager.lastError = "Initialization function failed";
                std::cerr << "[ERROR] Failed to initialize manager: " << managerName << std::endl;
                
                if (manager.isRequired) {
                    return false;
                }
                continue;
            }
            
            // Load manager data
            if (manager.loadFunction && !manager.loadFunction()) {
                manager.lastError = "Load function failed";
                std::cerr << "[ERROR] Failed to load manager data: " << managerName << std::endl;
                
                if (manager.isRequired) {
                    return false;
                }
                continue;
            }
            
            manager.isInitialized = true;
            std::cout << "[DEBUG] Manager initialized successfully: " << managerName << std::endl;
        }
        
        std::cout << "[DEBUG] All managers initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in InitializeManagersInOrder: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseManager::ValidateConfig(const DatabaseConfig& config) const {
    if (!config.IsValid()) {
        return false;
    }
    
    if (config.port == 0 || config.port > 65535) {
        return false;
    }
    
    if (config.connectionTimeout == 0 || config.queryTimeout == 0) {
        return false;
    }
    
    return true;
}

void CDatabaseManager::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    
    m_lastError = error;
    m_errorHistory.push_back(error);
    
    // Limit error history size
    if (m_errorHistory.size() > MAX_ERROR_HISTORY) {
        m_errorHistory.erase(m_errorHistory.begin());
    }
    
    std::cerr << "[ERROR] CDatabaseManager: " << error << std::endl;
}

void CDatabaseManager::Shutdown() {
    std::lock_guard<std::mutex> lock(m_managerMutex);
    
    try {
        std::cout << "[INFO] CDatabaseManager shutdown initiated" << std::endl;
        
        // Set shutdown state
        m_state = DatabaseManagerState::Shutdown;
        
        // Clear database connections
        m_worldDatabase.reset();
        m_userDatabase.reset();
        m_logDatabase.reset();
        
        // Clear managers
        m_managers.clear();
        m_initializationOrder.clear();
        
        // Set final state
        m_state = DatabaseManagerState::Uninitialized;
        
        std::cout << "[INFO] CDatabaseManager shutdown completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CDatabaseManager::Shutdown: " << e.what() << std::endl;
    }
}

void CDatabaseManager::Update(float deltaTime) {
    // Update statistics and perform health checks
    UpdateStatistics();
    
    // Check for failed connections and attempt reconnection
    CheckAndReconnect();
    
    m_lastUpdate = std::chrono::steady_clock::now();
}

void CDatabaseManager::UpdateStatistics() {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    m_stats.lastActivity = std::chrono::steady_clock::now();
}

void CDatabaseManager::CheckAndReconnect() {
    // Placeholder for reconnection logic
    // This would check database connections and attempt reconnection if needed
}

// Singleton implementation
CDatabaseManager& CDatabaseManager::Instance() {
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    
    if (!s_instance) {
        s_instance = std::make_unique<CDatabaseManager>();
    }
    
    return *s_instance;
}

void CDatabaseManager::SetInstance(std::unique_ptr<CDatabaseManager> instance) {
    std::lock_guard<std::mutex> lock(s_instanceMutex);
    s_instance = std::move(instance);
}

// Factory implementation
std::unique_ptr<CDatabaseManager> CDatabaseManagerFactory::CreateDatabaseManager() {
    return std::make_unique<CDatabaseManager>();
}

std::unique_ptr<CDatabaseManager> CDatabaseManagerFactory::CreateDatabaseManager(const DatabaseConfig& config) {
    auto manager = std::make_unique<CDatabaseManager>();
    manager->SetConfig(config);
    return manager;
}

} // namespace Database
} // namespace NexusProtection
