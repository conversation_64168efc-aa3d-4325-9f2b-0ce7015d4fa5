/*
 * Function: j_?UpdateTomorrowSchedule@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@AEAA_NI@Z
 * Address: 0x140001E06
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int uiMapID)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowSchedule(this, uiMapID);
}
