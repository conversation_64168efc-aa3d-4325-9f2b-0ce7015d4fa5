/*
 * Function: ?_CalcGenAttPnt@CAttack@@IEAAH_N@Z
 * Address: 0x14016AA00
 */

__int64 __fastcall CAttack::_CalcGenAttPnt(CAttack *this, bool bUseEffBullet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@8
  float v5; // xmm0_4@15
  float v6; // xmm0_4@15
  int v7; // eax@17
  float v8; // xmm0_4@20
  float v9; // xmm1_4@20
  int v10; // eax@22
  __int64 v11; // [sp+0h] [bp-68h]@1
  int v12; // [sp+20h] [bp-48h]@4
  int v13; // [sp+24h] [bp-44h]@4
  int v14; // [sp+38h] [bp-30h]@15
  int v15; // [sp+3Ch] [bp-2Ch]@20
  int v16; // [sp+44h] [bp-24h]@15
  float v17; // [sp+48h] [bp-20h]@15
  float v18; // [sp+4Ch] [bp-1Ch]@20
  _attack_param *v19; // [sp+50h] [bp-18h]@28
  _attack_param *v20; // [sp+58h] [bp-10h]@31
  CAttack *v21; // [sp+70h] [bp+8h]@1
  bool v22; // [sp+78h] [bp+10h]@1

  v22 = bUseEffBullet;
  v21 = this;
  v2 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = 0;
  v13 = 0;
  if ( bUseEffBullet )
  {
    v12 = (signed int)ffloor((float)((float)((float)v21->m_pp->nMaxAFPlus * (float)((float)v21->m_pp->nMaxAFPlus + 125.0))
                                   / (float)((float)v21->m_pp->nMaxAFPlus + 50.0)) + 0.5);
    v13 = (signed int)ffloor((float)((float)(v21->m_pp->nMaxAFPlus + v21->m_pp->nMinAFPlus) / 2.0) + 0.5);
  }
  else
  {
    v12 = (signed int)ffloor((float)((float)((float)v21->m_pp->nMaxAF * (float)((float)v21->m_pp->nMaxAF + 125.0))
                                   / (float)((float)v21->m_pp->nMaxAF + 50.0)) + 0.5);
    v13 = (signed int)ffloor((float)((float)(v21->m_pp->nMaxAF + v21->m_pp->nMinAF) / 2.0) + 0.5);
  }
  if ( _effect_parameter::GetEff_State(&v21->m_pAttChar->m_EP, 9) )
  {
    result = (unsigned int)v12;
  }
  else if ( v21->m_pp->nMaxAttackPnt <= 0 )
  {
    if ( v21->m_pp->nMaxAttackPnt >= 0 )
    {
      v16 = _100_per_random_table::GetRand(&v21->m_pAttChar->m_rtPer100);
      v17 = (float)v21->m_pp->nMinSel;
      v5 = v17;
      _effect_parameter::GetEff_Plus(&v21->m_pAttChar->m_EP, 14);
      v6 = v17 - v5;
      v14 = (signed int)ffloor(v6);
      if ( v21->m_pp->pDst && v21->m_pp->pDst != v21->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v21->m_pp->pDst->m_EP, 37);
        v14 = (signed int)ffloor((float)v14 + v6);
        v7 = CAttack::MonsterCritical_Exception_Rate(v21, v21->m_pp->pDst, v21->m_pp->bBackAttack);
        v14 += v7;
      }
      if ( v14 < 0 )
        v14 = 0;
      v18 = (float)(v21->m_pp->nMaxSel + v21->m_pp->nMinSel);
      v8 = v18;
      _effect_parameter::GetEff_Plus(&v21->m_pAttChar->m_EP, 14);
      v9 = v18 - v8;
      v15 = (signed int)ffloor(v18 - v8);
      if ( v21->m_pp->pDst && v21->m_pp->pDst != v21->m_pAttChar )
      {
        _effect_parameter::GetEff_Plus(&v21->m_pp->pDst->m_EP, 37);
        v15 = (signed int)ffloor((float)v15 + v9);
        v10 = CAttack::MonsterCritical_Exception_Rate(v21, v21->m_pp->pDst, v21->m_pp->bBackAttack);
        v15 += v10;
      }
      if ( v15 < 0 )
        v15 = 0;
      if ( v16 > v14 )
      {
        if ( v16 > v15 )
        {
          v21->m_bIsCrtAtt = 1;
          result = (unsigned int)v12;
        }
        else if ( v22 )
        {
          if ( v21->m_pp->nMaxAFPlus - v13 <= 0 )
            result = (unsigned int)v13;
          else
            result = (unsigned int)(rand() % (v21->m_pp->nMaxAFPlus - v13) + v13);
        }
        else if ( v21->m_pp->nMaxAF - v13 <= 0 )
        {
          result = (unsigned int)v13;
        }
        else
        {
          result = (unsigned int)(rand() % (v21->m_pp->nMaxAF - v13) + v13);
        }
      }
      else if ( v22 )
      {
        if ( v13 - v21->m_pp->nMinAFPlus <= 0 )
        {
          result = v21->m_pp->nMinAFPlus;
        }
        else
        {
          v19 = v21->m_pp;
          result = (unsigned int)(v19->nMinAFPlus + rand() % (v13 - v21->m_pp->nMinAFPlus));
        }
      }
      else if ( v13 - v21->m_pp->nMinAF <= 0 )
      {
        result = v21->m_pp->nMinAF;
      }
      else
      {
        v20 = v21->m_pp;
        result = (unsigned int)(v20->nMinAF + rand() % (v13 - v21->m_pp->nMinAF));
      }
    }
    else if ( v22 )
    {
      result = v21->m_pp->nMinAFPlus;
    }
    else
    {
      result = v21->m_pp->nMinAF;
    }
  }
  else
  {
    result = (unsigned int)v12;
  }
  return result;
}
