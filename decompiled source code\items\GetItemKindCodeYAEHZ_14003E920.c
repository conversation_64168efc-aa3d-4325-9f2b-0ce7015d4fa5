/*
 * Function: ?GetItemKindCode@@YAEH@Z
 * Address: 0x14003E920
 */

char __fastcall GetItemKindCode(int nTableCode)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // [sp+0h] [bp-18h]@1
  int v5; // [sp+20h] [bp+8h]@1

  v5 = nTableCode;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = v5;
  if ( v5 == 19 )
    result = 2;
  else
    result = v4 == 24;
  return result;
}
