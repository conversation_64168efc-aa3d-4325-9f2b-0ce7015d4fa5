/*
 * Function: j_?Update_PostStorageSendToRecver@CRFWorldDatabase@@QEAA_NKKEPEAD000H_KKKEGPEAE_N1@Z
 * Address: 0x14000B866
 */

bool __fastcall CRFWorldDatabase::Update_PostStorageSendToRecver(CRFWorldDatabase *this, unsigned int dwOwner, unsigned int dwPostSerial, char byPostState, char *wszSendName, char *wszRecvName, char *wszTitle, char *wszContent, int nK, unsigned __int64 dwD, unsigned int dwU, unsigned int dwGold, char byErr, unsigned __int16 wStorageIndex, char *pbyNumber, bool bGetNumber, unsigned __int64 lnUID)
{
  return CRFWorldDatabase::Update_PostStorageSendToRecver(
           this,
           dwOwner,
           dwPostSerial,
           byPostState,
           wszSendName,
           wszRecvName,
           wszTitle,
           wszContent,
           nK,
           dwD,
           dwU,
           dwGold,
           byErr,
           wStorageIndex,
           pbyNumber,
           bGetNumber,
           lnUID);
}
