/*
 * Function: ??0CMoveMapLimitRightPortal@@QEAA@H@Z
 * Address: 0x1403AC7F0
 */

void __fastcall CMoveMapLimitRightPortal::CMoveMapLimitRightPortal(CMoveMapLimitRightPortal *this, int iType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightPortal *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMoveMapLimitRight::CMoveMapLimitRight((CMoveMapLimitRight *)&v5->vfptr, iType);
  v5->vfptr = (CMoveMapLimitRightVtbl *)&CMoveMapLimitRightPortal::`vftable';
  v5->m_pkRight = 0i64;
  v5->m_bNotifyForceMoveStartPosition = 0;
}
