/*
 * Function: ?_db_update_inven_AMP@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401AFA70
 */

char __usercall CMainThread::_db_update_inven_AMP@<al>(CMainThread *this@<rcx>, unsigned int dwAvatorSerial@<edx>, _AVATOR_DATA *pNewData@<r8>, _AVATOR_DATA *pOldData@<r9>, signed __int64 a5@<rax>, char *pszQuery)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@8
  int v10; // eax@11
  int v11; // eax@12
  int v12; // eax@16
  size_t v13; // rax@18
  __int64 v15; // [sp-20h] [bp-2888h]@1
  int v16; // [sp+0h] [bp-2868h]@16
  unsigned int v17; // [sp+8h] [bp-2860h]@16
  char Source; // [sp+20h] [bp-2848h]@4
  char v19; // [sp+21h] [bp-2847h]@4
  char *Dest; // [sp+2828h] [bp-40h]@4
  size_t Size; // [sp+2830h] [bp-38h]@4
  int v22; // [sp+2840h] [bp-28h]@11
  __int64 v23; // [sp+2848h] [bp-20h]@16
  unsigned __int64 v24; // [sp+2850h] [bp-18h]@4
  CMainThread *v25; // [sp+2870h] [bp+8h]@1
  unsigned int v26; // [sp+2878h] [bp+10h]@1
  _AVATOR_DATA *v27; // [sp+2880h] [bp+18h]@1
  _AVATOR_DATA *v28; // [sp+2888h] [bp+20h]@1

  v28 = pOldData;
  v27 = pNewData;
  v26 = dwAvatorSerial;
  v25 = this;
  v6 = alloca(a5);
  v7 = &v15;
  for ( i = 2592i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v24 = (unsigned __int64)&v15 ^ _security_cookie;
  Source = 0;
  memset(&v19, 0, 0x27FFui64);
  Dest = pszQuery;
  sprintf(pszQuery, "update [dbo].[tbl_aminepersonal_inven] set ");
  for ( Size = (unsigned int)strlen_0(Dest); SHIDWORD(Size) < 40; ++HIDWORD(Size) )
  {
    if ( _INVENKEY::IsFilled((_INVENKEY *)&v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)]) )
    {
      if ( _INVENKEY::IsFilled((_INVENKEY *)&v28->dbPersonalAmineInven.m_List[SHIDWORD(Size)]) )
      {
        v22 = _INVENKEY::CovDBKey((_INVENKEY *)&v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)]);
        v10 = _INVENKEY::CovDBKey((_INVENKEY *)&v28->dbPersonalAmineInven.m_List[SHIDWORD(Size)]);
        if ( v22 != v10 )
        {
          v11 = _INVENKEY::CovDBKey((_INVENKEY *)&v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)]);
          sprintf(&Source, "K%d=%d,", HIDWORD(Size), (unsigned int)v11);
          strcat_0(Dest, &Source);
        }
        if ( v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)].dwDur != v28->dbPersonalAmineInven.m_List[SHIDWORD(Size)].dwDur )
        {
          sprintf(&Source, "N%d=%d,", HIDWORD(Size), v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)].dwDur);
          strcat_0(Dest, &Source);
        }
      }
      else
      {
        v23 = SHIDWORD(Size);
        v12 = _INVENKEY::CovDBKey((_INVENKEY *)&v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)]);
        v17 = v27->dbPersonalAmineInven.m_List[v23].dwDur;
        v16 = HIDWORD(Size);
        sprintf(&Source, "K%d=%d,N%d=%d,", HIDWORD(Size), (unsigned int)v12);
        strcat_0(Dest, &Source);
      }
    }
    else if ( _INVENKEY::IsFilled((_INVENKEY *)&v28->dbPersonalAmineInven.m_List[SHIDWORD(Size)]) )
    {
      v9 = _INVENKEY::CovDBKey((_INVENKEY *)&v27->dbPersonalAmineInven.m_List[SHIDWORD(Size)]);
      sprintf(&Source, "K%d=%d,", HIDWORD(Size), (unsigned int)v9);
      strcat_0(Dest, &Source);
    }
  }
  v13 = strlen_0(Dest);
  if ( v13 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE avatorserial=%d", v26);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
