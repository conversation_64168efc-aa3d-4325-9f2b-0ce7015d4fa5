/*
 * Function: ?CheckGoal@CGuildBattleController@@QEAAXPEAVCPlayer@@H@Z
 * Address: 0x1403D61B0
 */

void __fastcall CGuildBattleController::CheckGoal(CGuildBattleController *this, CPlayer *pkPlayer, int iPortalInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  int n; // [sp+30h] [bp-18h]@4
  unsigned int dwGuildSerial; // [sp+34h] [bp-14h]@7
  unsigned int dwCharacSerial; // [sp+38h] [bp-10h]@7
  int v10; // [sp+3Ch] [bp-Ch]@5
  int v11; // [sp+60h] [bp+18h]@1

  v11 = iPortalInx;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  n = pkPlayer->m_ObjID.m_wIndex;
  if ( pkPlayer->m_Param.m_pGuild )
    v10 = pkPlayer->m_Param.m_pGuild->m_dwSerial;
  else
    v10 = -1;
  dwGuildSerial = v10;
  dwCharacSerial = pkPlayer->m_pUserDB->m_dwSerial;
  v5 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::CheckGoal(v5, n, dwGuildSerial, dwCharacSerial, v11);
}
