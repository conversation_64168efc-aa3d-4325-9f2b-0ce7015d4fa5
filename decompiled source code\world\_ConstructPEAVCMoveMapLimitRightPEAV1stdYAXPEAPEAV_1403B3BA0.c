/*
 * Function: ??$_Construct@PEAVCMoveMapLimitRight@@PEAV1@@std@@YAXPEAPEAVCMoveMapLimitRight@@AEBQEAV1@@Z
 * Address: 0x1403B3BA0
 */

void __fastcall std::_Construct<CMoveMapLimitRight *,CMoveMapLimitRight *>(CMoveMapLimitRight **_Ptr, CMoveMapLimitRight *const *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  void *_Where; // [sp+20h] [bp-28h]@4
  _QWORD *v6; // [sp+28h] [bp-20h]@4
  CMoveMapLimitRight **v7; // [sp+50h] [bp+8h]@1
  CMoveMapLimitRight *const *v8; // [sp+58h] [bp+10h]@1

  v8 = _Val;
  v7 = _Ptr;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _Where = v7;
  v6 = operator new(8ui64, v7);
  if ( v6 )
    *v6 = *v8;
}
