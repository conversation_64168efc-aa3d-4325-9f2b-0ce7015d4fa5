/*
 * ItemCodes.h - Complete Item Code System Constants
 * Based on decompiled GetItemTableCode function (Address: 0x1400362B0)
 * Contains all item prefixes and their corresponding table codes
 */

#pragma once

#include <string>
#include <unordered_map>
#include <cstdint>

namespace NexusProtection {
namespace Items {

/**
 * Item table codes based on GetItemTableCode function
 * Equipment items use table codes 0-7, others are non-equipment
 */
enum class ItemTableCode : int32_t {
    // Equipment Items (0-7) - Can be equipped
    UpperArmor = 0,     // "iu" - Upper body armor/clothing
    LowerArmor = 1,     // "il" - Lower body armor/clothing  
    Gloves = 2,         // "ig" - Gloves/hand equipment
    Shoes = 3,          // "is" - Shoes/foot equipment
    Helmet = 4,         // "ih" - Helmet/head equipment
    Shield = 5,         // "id" - Shield/defensive equipment
    Weapon = 6,         // "iw" - Weapon/offensive equipment
    Cloak = 7,          // "ik" - Cloak/cape equipment
    
    // Non-Equipment Items (8+) - Cannot be equipped
    Ring = 8,           // "ii" - Ring/finger accessories
    Amulet = 9,         // "ia" - Amulet/neck accessories
    Bullet = 10,        // "ib" - Bullet/ammunition
    Material = 11,      // "im" - Crafting materials/tools
    Elixir = 12,        // "ie" - Elixir/potions
    Potion = 13,        // "ip" - Potions/consumables
    Food = 14,          // "if" - Food items
    Charm = 15,         // "ic" - Charm items
    Ticket = 16,        // "it" - Ticket items
    Ore = 17,           // "io" - Ore/mining materials
    Rare = 18,          // "ir" - Rare items
    Natural = 19,       // "in" - Natural items (FORBIDDEN - see table code 19 check)
    Yggdrasil = 20,     // "iy" - Yggdrasil items
    Zone = 21,          // "iz" - Zone items
    Quest = 22,         // "iq" - Quest items
    Experience = 23,    // "ix" - Experience items
    Jewel = 24,         // "ij" - Jewel items
    Gate = 25,          // "gt" - Gate items
    Treasure = 26,      // "tr" - Treasure items
    Skill = 27,         // "sk" - Skill items
    Time = 28,          // "ti" - Time items
    Event = 29,         // "ev" - Event items
    Reward = 30,        // "re" - Reward items
    Box = 31,           // "bx" - Box items
    Fish = 32,          // "fi" - Fish items
    Union = 33,         // "un" - Union items
    Raid = 34,          // "rd" - Raid items
    Link = 35,          // "lk" - Link items
    Currency = 36,      // "cu" - Currency items
    
    Invalid = -1        // Invalid/unknown item code
};

/**
 * Item code prefix to table code mapping
 * Based on the complete GetItemTableCode function
 */
class ItemCodeMapper {
public:
    /**
     * Get table code from item code prefix
     * @param prefix Two-character item prefix (e.g., "iu", "il")
     * @return Table code or Invalid if not found
     */
    static ItemTableCode GetTableCode(const std::string& prefix);
    
    /**
     * Get table code from full item code
     * @param itemCode Full item code (extracts first 2 characters)
     * @return Table code or Invalid if not found
     */
    static ItemTableCode GetTableCodeFromItem(const std::string& itemCode);
    
    /**
     * Check if table code represents equipment
     * @param tableCode Table code to check
     * @return true if equipment (0-7)
     */
    static bool IsEquipmentTableCode(ItemTableCode tableCode);
    
    /**
     * Check if table code represents equipment (integer version)
     * @param tableCode Table code to check
     * @return true if equipment (0-7)
     */
    static bool IsEquipmentTableCode(int32_t tableCode);
    
    /**
     * Get item type description
     * @param tableCode Table code
     * @return Human-readable description
     */
    static std::string GetItemTypeDescription(ItemTableCode tableCode);
    
    /**
     * Get item prefix from table code
     * @param tableCode Table code
     * @return Two-character prefix or empty string if invalid
     */
    static std::string GetItemPrefix(ItemTableCode tableCode);

private:
    // Static mapping tables
    static const std::unordered_map<std::string, ItemTableCode> s_prefixToTableCode;
    static const std::unordered_map<ItemTableCode, std::string> s_tableCodeToPrefix;
    static const std::unordered_map<ItemTableCode, std::string> s_tableCodeToDescription;
};

/**
 * Equipment slot mapping based on table codes 0-7
 */
enum class EquipmentSlot : uint8_t {
    UpperArmor = 0,     // "iu" - Upper body armor/clothing
    LowerArmor = 1,     // "il" - Lower body armor/clothing
    Gloves = 2,         // "ig" - Gloves/hand equipment
    Shoes = 3,          // "is" - Shoes/foot equipment
    Helmet = 4,         // "ih" - Helmet/head equipment
    Shield = 5,         // "id" - Shield/defensive equipment
    Weapon = 6,         // "iw" - Weapon/offensive equipment
    Cloak = 7,          // "ik" - Cloak/cape equipment
    Invalid = 255
};

/**
 * Equipment slot utilities
 */
class EquipmentSlotMapper {
public:
    /**
     * Convert table code to equipment slot
     * @param tableCode Item table code
     * @return Equipment slot or Invalid if not equipment
     */
    static EquipmentSlot TableCodeToSlot(ItemTableCode tableCode);
    
    /**
     * Convert table code to equipment slot (integer version)
     * @param tableCode Item table code
     * @return Equipment slot or Invalid if not equipment
     */
    static EquipmentSlot TableCodeToSlot(int32_t tableCode);
    
    /**
     * Get equipment slot name
     * @param slot Equipment slot
     * @return Human-readable slot name
     */
    static std::string GetSlotName(EquipmentSlot slot);
    
    /**
     * Check if slot is valid equipment slot
     * @param slot Equipment slot to check
     * @return true if valid equipment slot
     */
    static bool IsValidEquipmentSlot(EquipmentSlot slot);
};

/**
 * Item validation constants
 */
namespace ItemValidation {
    constexpr int32_t MAX_TABLE_CODE = 36;
    constexpr int32_t MIN_EQUIPMENT_TABLE_CODE = 0;
    constexpr int32_t MAX_EQUIPMENT_TABLE_CODE = 7;
    constexpr int32_t FORBIDDEN_TABLE_CODE = 19;  // "in" - Natural items are forbidden
    constexpr size_t ITEM_PREFIX_LENGTH = 2;
    constexpr size_t MAX_ITEM_CODE_LENGTH = 32;
}

} // namespace Items
} // namespace NexusProtection
