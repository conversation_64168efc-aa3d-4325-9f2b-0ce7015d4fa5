/*
 * Function: ?SetGotoRegenState@CNormalGuildBattleStateInBattle@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403F3300
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleStateInBattle::SetGotoRegenState(GUILD_BATTLE::CNormalGuildBattleStateInBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateInBattle *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CNormalGuildBattleStateInBattle::IsInBattleRegenState(v5) )
    result = GUILD_BATTLE::CGuildBattleStateList::GotoState(
               (GUILD_BATTLE::CGuildBattleStateList *)&v5->m_kRountStateList.vfptr,
               0);
  else
    result = 0;
  return result;
}
