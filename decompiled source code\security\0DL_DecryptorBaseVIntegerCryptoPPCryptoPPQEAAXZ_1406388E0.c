/*
 * Function: ??0?$DL_DecryptorBase@VInteger@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x1406388E0
 */

__int64 __fastcall CryptoPP::DL_DecryptorBase<CryptoPP::Integer>::DL_DecryptorBase<CryptoPP::Integer>(__int64 a1)
{
  __int64 v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>();
  return v2;
}
