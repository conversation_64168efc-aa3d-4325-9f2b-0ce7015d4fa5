/*
 * Function: ?Alive_Char_Complete@CUserDB@@QEAAXEEKPEAU_REGED@@@Z
 * Address: 0x14011AFC0
 */

void __fastcall CUserDB::Alive_Char_Complete(CUserDB *this, char byRetCode, char byCase, unsigned int dwSerial, _REGED *pAliveAvator)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-E8h]@1
  char v8; // [sp+30h] [bp-B8h]@4
  int j; // [sp+34h] [bp-B4h]@9
  int k; // [sp+38h] [bp-B0h]@15
  _alive_char_result_zocl Dst; // [sp+50h] [bp-98h]@20
  char pbyType; // [sp+B4h] [bp-34h]@22
  char v13; // [sp+B5h] [bp-33h]@22
  unsigned __int64 v14; // [sp+D0h] [bp-18h]@4
  CUserDB *v15; // [sp+F0h] [bp+8h]@1
  char v16; // [sp+F8h] [bp+10h]@1
  char v17; // [sp+100h] [bp+18h]@1
  unsigned int v18; // [sp+108h] [bp+20h]@1

  v18 = dwSerial;
  v17 = byCase;
  v16 = byRetCode;
  v15 = this;
  v5 = &v7;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  v15->m_bDBWaitState = 0;
  v8 = -1;
  if ( !byRetCode )
  {
    v8 = pAliveAvator->m_bySlotIndex;
    if ( (signed int)(unsigned __int8)v8 < 3 )
    {
      if ( v15->m_RegedList[(unsigned __int8)v8].m_bySlotIndex == 255 )
      {
        memcpy_0(&v15->m_RegedList[(unsigned __int8)v8], pAliveAvator, 0x10Dui64);
        _REGED::UpdateEquipLv(&v15->m_RegedList[(unsigned __int8)v8]);
        for ( j = 0; j < 50; ++j )
        {
          if ( v15->m_NotArrangedChar[j].dwSerial == v18 )
          {
            v15->m_NotArrangedChar[j].dwSerial = -1;
            break;
          }
        }
      }
      else
      {
        v16 = 50;
      }
    }
    else
    {
      v16 = 50;
    }
  }
  if ( !v17 )
  {
    for ( k = 0; k < 50; ++k )
    {
      if ( v15->m_dwArrangePassCase0[k] == -1 )
      {
        v15->m_dwArrangePassCase0[k] = v18;
        break;
      }
    }
  }
  CMgrAccountLobbyHistory::recovery_char_complete(
    &CUserDB::s_MgrLobbyHistory,
    v16,
    pAliveAvator,
    v15->m_szLobbyHistoryFileName);
  _alive_char_result_zocl::_alive_char_result_zocl(&Dst);
  Dst.byRetCode = v16;
  if ( !v16 )
  {
    Dst.dwSerial = v18;
    memcpy_0(&Dst.AliveChar, &v15->m_RegedList[(unsigned __int8)v8], 0x45ui64);
  }
  pbyType = 1;
  v13 = 23;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_idWorld.wIndex, &pbyType, &Dst.byRetCode, 0x4Au);
}
