/*
 * Function: ?Init@CGuildBattleRankManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CA4B0
 */

char __fastcall GUILD_BATTLE::CGuildBattleRankManager::Init(GUILD_BATTLE::CGuildBattleRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@5
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@6
  _guild_battle_rank_list_result_zocl **v8; // [sp+28h] [bp-20h]@4
  _guild_battle_rank_list_result_zocl *v9; // [sp+30h] [bp-18h]@8
  GUILD_BATTLE::CGuildBattleRankManager *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (_guild_battle_rank_list_result_zocl **)operator new[](0x18ui64);
  v10->m_ppkList = v8;
  if ( v10->m_ppkList )
  {
    memset_0(v10->m_ppkList, 0, 0x18ui64);
    for ( j = 0; j < 3; ++j )
    {
      v9 = (_guild_battle_rank_list_result_zocl *)operator new[](0x320Aui64);
      v10->m_ppkList[j] = v9;
      if ( !v10->m_ppkList[j] )
      {
        v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v5,
          "CGuildBattleRankManager::Init() : NULL == new _guild_battle_rank_list_result_zocl[%u]",
          30i64);
        return 0;
      }
      memset_0(v10->m_ppkList[j], 0, 0x320Aui64);
    }
    memset_0(v10->m_dwGuildSerial, 0, 0xE10ui64);
    result = 1;
  }
  else
  {
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CGuildBattleRankManager::Init() : NULL == new _guild_battle_rank_list_result_zocl * [%u]",
      3i64);
    result = 0;
  }
  return result;
}
