/*
 * Function: j_?erase@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@2@V32@0@Z
 * Address: 0x14000C7E8
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::erase(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_First, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Last)
{
  return std::deque<RECV_DATA,std::allocator<RECV_DATA>>::erase(this, result, _First, _Last);
}
