/*
 * Function: ?MapChannel@EqualityComparisonFilter@CryptoPP@@AEBAIAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x140654950
 */

signed __int64 __fastcall CryptoPP::EqualityComparisonFilter::MapChannel(__int64 a1, __int64 a2)
{
  signed __int64 result; // rax@2
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  if ( (unsigned __int8)std::operator==<char,std::char_traits<char>,std::allocator<char>>(a2, a1 + 56) )
  {
    result = 0i64;
  }
  else if ( (unsigned __int8)std::operator==<char,std::char_traits<char>,std::allocator<char>>(v4, v3 + 104) )
  {
    result = 1i64;
  }
  else
  {
    result = 2i64;
  }
  return result;
}
