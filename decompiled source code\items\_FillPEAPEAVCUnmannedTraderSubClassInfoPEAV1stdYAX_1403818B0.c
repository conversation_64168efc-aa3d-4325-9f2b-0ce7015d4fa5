/*
 * Function: ??$_Fill@PEAPEAVCUnmannedTraderSubClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@0AEBQEAV1@@Z
 * Address: 0x1403818B0
 */

void __fastcall std::_Fill<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo *const *_Val)
{
  CUnmannedTraderSubClassInfo **i; // [sp+10h] [bp+8h]@1

  for ( i = _First; i != _Last; ++i )
    *i = *_Val;
}
