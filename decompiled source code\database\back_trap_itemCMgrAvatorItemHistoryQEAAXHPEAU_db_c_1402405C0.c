/*
 * Function: ?back_trap_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAD@Z
 * Address: 0x1402405C0
 */

void __fastcall CMgrAvatorItemHistory::back_trap_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  unsigned __int64 v7; // [sp+20h] [bp-38h]@4
  char *v8; // [sp+28h] [bp-30h]@4
  char *v9; // [sp+30h] [bp-28h]@4
  _base_fld *v10; // [sp+40h] [bp-18h]@4
  CMgrAvatorItemHistory *v11; // [sp+60h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v12; // [sp+70h] [bp+18h]@1
  char *pszFileNamea; // [sp+78h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v12 = pItem;
  v11 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v9 = v11->m_szCurTime;
  v8 = v11->m_szCurDate;
  v7 = v12->m_lnUID;
  sprintf(sData, "BACK TRAP ITEM : %s_%u_[%I64u] [%s %s]\r\n", v10->m_strCode, v12->m_dwDur);
  CMgrAvatorItemHistory::WriteFile(v11, pszFileNamea, sData);
}
