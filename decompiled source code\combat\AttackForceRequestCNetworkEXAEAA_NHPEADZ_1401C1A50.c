/*
 * Function: ?AttackForceRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C1A50
 */

char __fastcall CNetworkEX::AttackForceRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@11
  __int64 v7; // [sp+0h] [bp-88h]@1
  unsigned __int16 *pConsumeSerial; // [sp+20h] [bp-68h]@12
  unsigned __int16 wEffBtSerial; // [sp+28h] [bp-60h]@12
  char *v10; // [sp+30h] [bp-58h]@4
  CPlayer *v11; // [sp+38h] [bp-50h]@4
  CCharacter *pDst; // [sp+40h] [bp-48h]@8
  float pfAreaPos; // [sp+58h] [bp-30h]@12
  float v14; // [sp+5Ch] [bp-2Ch]@12
  int v15; // [sp+60h] [bp-28h]@12
  CNetworkEX *v16; // [sp+90h] [bp+8h]@1

  v16 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)*v10, *(_WORD *)(v10 + 1));
    if ( pDst || (unsigned __int8)*v10 == 255 && *(_WORD *)(v10 + 1) == 0xFFFF )
    {
      pfAreaPos = (float)*(_WORD *)(v10 + 3);
      v14 = (float)*(_WORD *)(v10 + 5);
      v15 = 0;
      wEffBtSerial = *(_WORD *)(v10 + 15);
      pConsumeSerial = (unsigned __int16 *)(v10 + 9);
      CPlayer::pc_PlayAttack_Force(
        v11,
        pDst,
        &pfAreaPos,
        *(_WORD *)(v10 + 7),
        (unsigned __int16 *)(v10 + 9),
        wEffBtSerial);
      result = 1;
    }
    else
    {
      v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v16->m_LogFile,
        "odd.. %s: AttackForceRequest()..  if(pRecv->byID != 0xFF || pRecv->wIndex != 0xFFFF)",
        v6);
      result = 0;
    }
  }
  return result;
}
