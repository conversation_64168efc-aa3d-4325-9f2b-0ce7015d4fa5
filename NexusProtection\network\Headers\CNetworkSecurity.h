#pragma once

/**
 * @file CNetworkSecurity.h
 * @brief Modern C++20 Network Security System
 * 
 * This file provides comprehensive network security including packet validation,
 * encryption support, anti-cheat measures, and intrusion detection.
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <optional>

namespace NexusProtection {
namespace Network {

/**
 * @brief Security threat level enumeration
 */
enum class ThreatLevel : uint8_t {
    None = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
};

/**
 * @brief Security violation type enumeration
 */
enum class ViolationType : uint8_t {
    None = 0,
    InvalidPacket = 1,
    SpeedHack = 2,
    PacketFlooding = 3,
    InvalidSequence = 4,
    SuspiciousPattern = 5,
    EncryptionViolation = 6,
    RateLimitExceeded = 7,
    UnauthorizedAccess = 8,
    MalformedData = 9,
    ReplayAttack = 10
};

/**
 * @brief Security action enumeration
 */
enum class SecurityAction : uint8_t {
    None = 0,
    Log = 1,
    Warn = 2,
    Throttle = 3,
    Disconnect = 4,
    Ban = 5,
    Alert = 6
};

/**
 * @brief Encryption algorithm enumeration
 */
enum class EncryptionAlgorithm : uint8_t {
    None = 0,
    XOR = 1,
    AES128 = 2,
    AES256 = 3,
    Custom = 4
};

/**
 * @brief Security violation record
 */
struct SecurityViolation {
    uint32_t clientId{0};
    ViolationType type{ViolationType::None};
    ThreatLevel level{ThreatLevel::None};
    std::string description;
    std::chrono::system_clock::time_point timestamp;
    std::string clientIP;
    std::vector<uint8_t> packetData;
    uint32_t violationCount{1};
    
    SecurityViolation() : timestamp(std::chrono::system_clock::now()) {}
    SecurityViolation(uint32_t id, ViolationType vType, ThreatLevel tLevel, const std::string& desc)
        : clientId(id), type(vType), level(tLevel), description(desc)
        , timestamp(std::chrono::system_clock::now()) {}
};

/**
 * @brief Client security profile
 */
struct ClientSecurityProfile {
    uint32_t clientId{0};
    std::string clientIP;
    std::chrono::system_clock::time_point firstSeen;
    std::chrono::system_clock::time_point lastActivity;
    
    // Violation tracking
    std::unordered_map<ViolationType, uint32_t> violationCounts;
    std::vector<SecurityViolation> recentViolations;
    ThreatLevel currentThreatLevel{ThreatLevel::None};
    
    // Rate limiting
    std::chrono::steady_clock::time_point lastPacketTime;
    uint32_t packetsInWindow{0};
    std::chrono::steady_clock::time_point windowStartTime;
    
    // Encryption state
    EncryptionAlgorithm encryptionType{EncryptionAlgorithm::None};
    std::vector<uint8_t> encryptionKey;
    uint32_t encryptionSequence{0};
    
    // Behavioral analysis
    double averagePacketInterval{0.0};
    uint32_t totalPackets{0};
    std::vector<std::chrono::milliseconds> packetIntervals;
    
    ClientSecurityProfile() {
        auto now = std::chrono::system_clock::now();
        firstSeen = now;
        lastActivity = now;
        windowStartTime = std::chrono::steady_clock::now();
        lastPacketTime = std::chrono::steady_clock::now();
    }
    
    void AddViolation(const SecurityViolation& violation);
    ThreatLevel CalculateThreatLevel() const;
    bool IsRateLimited() const;
    void UpdatePacketTiming();
};

/**
 * @brief Security configuration
 */
struct SecurityConfig {
    // Rate limiting
    uint32_t maxPacketsPerSecond{100};
    std::chrono::seconds rateLimitWindow{1};
    
    // Violation thresholds
    uint32_t maxViolationsPerMinute{10};
    uint32_t maxViolationsTotal{100};
    
    // Speed hack detection
    bool enableSpeedHackDetection{true};
    std::chrono::milliseconds speedHackThreshold{4500};
    uint32_t speedHackMaxMisses{5};
    
    // Packet validation
    bool enablePacketValidation{true};
    uint32_t maxPacketSize{65536};
    uint32_t minPacketSize{4};
    
    // Encryption
    bool requireEncryption{false};
    EncryptionAlgorithm defaultEncryption{EncryptionAlgorithm::XOR};
    std::chrono::seconds keyRotationInterval{300};
    
    // Behavioral analysis
    bool enableBehavioralAnalysis{true};
    double suspiciousPacketIntervalThreshold{0.1}; // 100ms
    uint32_t behavioralAnalysisWindow{100};
    
    // Actions
    SecurityAction defaultAction{SecurityAction::Log};
    std::unordered_map<ViolationType, SecurityAction> violationActions;
    
    SecurityConfig() {
        // Initialize default actions
        violationActions[ViolationType::SpeedHack] = SecurityAction::Disconnect;
        violationActions[ViolationType::PacketFlooding] = SecurityAction::Throttle;
        violationActions[ViolationType::InvalidPacket] = SecurityAction::Warn;
        violationActions[ViolationType::SuspiciousPattern] = SecurityAction::Log;
    }
};

/**
 * @brief Security statistics
 */
struct SecurityStatistics {
    std::atomic<uint64_t> totalPacketsProcessed{0};
    std::atomic<uint64_t> validPackets{0};
    std::atomic<uint64_t> invalidPackets{0};
    std::atomic<uint64_t> encryptedPackets{0};
    std::atomic<uint64_t> totalViolations{0};
    std::atomic<uint32_t> activeClients{0};
    std::atomic<uint32_t> bannedClients{0};
    std::atomic<uint32_t> throttledClients{0};
    std::chrono::steady_clock::time_point startTime;
    
    SecurityStatistics() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetValidPacketRate() const {
        uint64_t total = totalPacketsProcessed.load();
        return total > 0 ? (static_cast<double>(validPackets.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Security event callback function type
 */
using SecurityEventCallback = std::function<void(const SecurityViolation&)>;

/**
 * @brief Modern C++20 Network Security class
 * 
 * This class provides comprehensive network security including packet validation,
 * encryption support, anti-cheat measures, and intrusion detection.
 */
class CNetworkSecurity {
public:
    // Constructor and Destructor
    CNetworkSecurity();
    explicit CNetworkSecurity(const SecurityConfig& config);
    virtual ~CNetworkSecurity();

    // Disable copy constructor and assignment operator
    CNetworkSecurity(const CNetworkSecurity&) = delete;
    CNetworkSecurity& operator=(const CNetworkSecurity&) = delete;

    // Enable move constructor and assignment operator
    CNetworkSecurity(CNetworkSecurity&&) noexcept = default;
    CNetworkSecurity& operator=(CNetworkSecurity&&) noexcept = default;

    /**
     * @brief Initialize security system
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown security system
     */
    void Shutdown();

    /**
     * @brief Validate incoming packet
     * 
     * @param clientId Client identifier
     * @param packetData Packet data
     * @param packetSize Packet size
     * @return true if packet is valid, false otherwise
     */
    bool ValidatePacket(uint32_t clientId, const uint8_t* packetData, size_t packetSize);

    /**
     * @brief Check rate limiting for client
     * 
     * @param clientId Client identifier
     * @return true if client is within rate limits, false otherwise
     */
    bool CheckRateLimit(uint32_t clientId);

    /**
     * @brief Process speed hack detection
     * 
     * @param clientId Client identifier
     * @param responseTime Response time in milliseconds
     * @return true if timing is valid, false if speed hack detected
     */
    bool ProcessSpeedHackDetection(uint32_t clientId, uint32_t responseTime);

    /**
     * @brief Encrypt packet data
     * 
     * @param clientId Client identifier
     * @param data Input data
     * @param encryptedData Output encrypted data
     * @return true if encryption successful, false otherwise
     */
    bool EncryptPacket(uint32_t clientId, const std::vector<uint8_t>& data, 
                      std::vector<uint8_t>& encryptedData);

    /**
     * @brief Decrypt packet data
     * 
     * @param clientId Client identifier
     * @param encryptedData Input encrypted data
     * @param data Output decrypted data
     * @return true if decryption successful, false otherwise
     */
    bool DecryptPacket(uint32_t clientId, const std::vector<uint8_t>& encryptedData, 
                      std::vector<uint8_t>& data);

    /**
     * @brief Add client to security monitoring
     * 
     * @param clientId Client identifier
     * @param clientIP Client IP address
     * @return true if added successfully, false otherwise
     */
    bool AddClient(uint32_t clientId, const std::string& clientIP);

    /**
     * @brief Remove client from security monitoring
     * 
     * @param clientId Client identifier
     * @return true if removed successfully, false otherwise
     */
    bool RemoveClient(uint32_t clientId);

    /**
     * @brief Report security violation
     * 
     * @param violation Security violation details
     * @return Security action to take
     */
    SecurityAction ReportViolation(const SecurityViolation& violation);

    /**
     * @brief Get client security profile
     * 
     * @param clientId Client identifier
     * @return Client security profile or nullptr if not found
     */
    std::shared_ptr<ClientSecurityProfile> GetClientProfile(uint32_t clientId) const;

    /**
     * @brief Check if client is banned
     * 
     * @param clientId Client identifier
     * @return true if client is banned, false otherwise
     */
    bool IsClientBanned(uint32_t clientId) const;

    /**
     * @brief Ban client
     * 
     * @param clientId Client identifier
     * @param reason Ban reason
     * @param duration Ban duration (0 for permanent)
     * @return true if ban applied successfully, false otherwise
     */
    bool BanClient(uint32_t clientId, const std::string& reason, 
                  std::chrono::seconds duration = std::chrono::seconds(0));

    /**
     * @brief Unban client
     * 
     * @param clientId Client identifier
     * @return true if unban successful, false otherwise
     */
    bool UnbanClient(uint32_t clientId);

    /**
     * @brief Get security statistics
     * 
     * @return Current security statistics
     */
    const SecurityStatistics& GetStatistics() const { return m_statistics; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Set security configuration
     * 
     * @param config New security configuration
     */
    void SetConfiguration(const SecurityConfig& config);

    /**
     * @brief Get security configuration
     * 
     * @return Current security configuration
     */
    const SecurityConfig& GetConfiguration() const { return m_config; }

    /**
     * @brief Register security event callback
     * 
     * @param callback Callback function
     */
    void RegisterEventCallback(SecurityEventCallback callback);

    /**
     * @brief Get security report
     * 
     * @return Detailed security report string
     */
    std::string GetSecurityReport() const;

    /**
     * @brief Perform behavioral analysis
     * 
     * @param clientId Client identifier
     * @return Detected threat level
     */
    ThreatLevel PerformBehavioralAnalysis(uint32_t clientId);

protected:
    // Configuration
    SecurityConfig m_config;
    
    // Client profiles
    std::unordered_map<uint32_t, std::shared_ptr<ClientSecurityProfile>> m_clientProfiles;
    
    // Banned clients
    std::unordered_set<uint32_t> m_bannedClients;
    std::unordered_map<uint32_t, std::chrono::system_clock::time_point> m_banExpirations;
    
    // Statistics
    SecurityStatistics m_statistics;
    
    // Event callback
    SecurityEventCallback m_eventCallback;
    
    // Synchronization
    mutable std::mutex m_profilesMutex;
    mutable std::mutex m_bansMutex;
    mutable std::mutex m_statisticsMutex;
    mutable std::mutex m_callbackMutex;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};

private:
    /**
     * @brief Validate packet structure
     * 
     * @param packetData Packet data
     * @param packetSize Packet size
     * @return true if structure is valid, false otherwise
     */
    bool ValidatePacketStructure(const uint8_t* packetData, size_t packetSize) const;

    /**
     * @brief Validate packet content
     * 
     * @param packetData Packet data
     * @param packetSize Packet size
     * @return true if content is valid, false otherwise
     */
    bool ValidatePacketContent(const uint8_t* packetData, size_t packetSize) const;

    /**
     * @brief Update client statistics
     * 
     * @param clientId Client identifier
     * @param packetSize Packet size
     */
    void UpdateClientStatistics(uint32_t clientId, size_t packetSize);

    /**
     * @brief Execute security action
     * 
     * @param clientId Client identifier
     * @param action Security action to execute
     * @param violation Violation details
     */
    void ExecuteSecurityAction(uint32_t clientId, SecurityAction action, 
                              const SecurityViolation& violation);

    /**
     * @brief Generate encryption key
     * 
     * @param algorithm Encryption algorithm
     * @return Generated encryption key
     */
    std::vector<uint8_t> GenerateEncryptionKey(EncryptionAlgorithm algorithm) const;

    /**
     * @brief XOR encryption/decryption
     * 
     * @param data Input data
     * @param key Encryption key
     * @param output Output data
     */
    void XORCrypt(const std::vector<uint8_t>& data, const std::vector<uint8_t>& key, 
                  std::vector<uint8_t>& output) const;

    /**
     * @brief Cleanup expired bans
     */
    void CleanupExpiredBans();

    /**
     * @brief Notify security event
     * 
     * @param violation Security violation
     */
    void NotifySecurityEvent(const SecurityViolation& violation);
};

/**
 * @brief Network Security Factory
 */
class CNetworkSecurityFactory {
public:
    /**
     * @brief Create security system with default configuration
     * 
     * @return Unique pointer to security system
     */
    static std::unique_ptr<CNetworkSecurity> CreateDefaultSecurity();

    /**
     * @brief Create security system with custom configuration
     * 
     * @param config Security configuration
     * @return Unique pointer to security system
     */
    static std::unique_ptr<CNetworkSecurity> CreateSecurity(const SecurityConfig& config);
};

/**
 * @brief Security utility functions
 */
namespace SecurityUtils {
    std::string ThreatLevelToString(ThreatLevel level);
    std::string ViolationTypeToString(ViolationType type);
    std::string SecurityActionToString(SecurityAction action);
    std::string EncryptionAlgorithmToString(EncryptionAlgorithm algorithm);
    ThreatLevel StringToThreatLevel(const std::string& levelStr);
    ViolationType StringToViolationType(const std::string& typeStr);
    SecurityAction StringToSecurityAction(const std::string& actionStr);
    EncryptionAlgorithm StringToEncryptionAlgorithm(const std::string& algorithmStr);
}

} // namespace Network
} // namespace NexusProtection
