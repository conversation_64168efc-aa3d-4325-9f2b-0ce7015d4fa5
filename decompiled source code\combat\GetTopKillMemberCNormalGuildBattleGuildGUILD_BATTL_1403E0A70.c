/*
 * Function: ?GetTopKillMember@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleGuildMember@2@XZ
 * Address: 0x1403E0A70
 */

GUILD_BATTLE::CNormalGuildBattleGuildMember *__fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetTopKillMember(GUILD_BATTLE::CNormalGuildBattleGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v5; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate v6; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset(&v6, 0, sizeof(v6));
  v5 = std::max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate>(
         v7->m_kMember,
         (GUILD_BATTLE::CNormalGuildBattleGuildMember *)&v7[1],
         v6);
  if ( (GUILD_BATTLE::CNormalGuildBattleGuildMember *)&v7[1] == v5 )
    result = 0i64;
  else
    result = v5;
  return result;
}
