/*
 * Function: ?dtor$0@?0???0UMSSchedulerProxy@details@Concurrency@@QEAA@PEAUIScheduler@2@PEAVResourceManager@12@AEBVSchedulerPolicy@2@@Z@4HA
 * Address: 0x14057A6B0
 */

int __fastcall `Concurrency::details::UMSSchedulerProxy::UMSSchedulerProxy'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  return CryptoPP::clonable_ptr<CryptoPP::ModularArithmetic>::~clonable_ptr<CryptoPP::ModularArithmetic>(*(_QWORD *)(a2 + 160) + 8i64);
}
