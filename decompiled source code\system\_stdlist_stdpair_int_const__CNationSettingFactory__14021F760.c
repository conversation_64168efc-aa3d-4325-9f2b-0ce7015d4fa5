/*
 * Function: _std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::erase_::_1_::dtor$1_0
 * Address: 0x14021F760
 */

void __fastcall std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::erase_::_1_::dtor_1_0(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *)(a2 + 40));
}
