/*
 * Function: ?PrepareCascade@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@AEBAXAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEAV?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@AEBVInteger@2@@Z
 * Address: 0x140576730
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::PrepareCascade(__int64 a1, __int64 a2, __int64 a3, struct CryptoPP::Integer *a4)
{
  __int64 v4; // rax@1
  unsigned __int64 v5; // rax@6
  signed __int64 v6; // rax@9
  __int64 v7; // rax@9
  __int64 v8; // rax@9
  signed __int64 v9; // rax@10
  __int64 v10; // rax@10
  signed __int64 v11; // rax@12
  __int64 v12; // rax@12
  char v13; // [sp+20h] [bp-238h]@5
  unsigned int i; // [sp+24h] [bp-234h]@5
  __int64 v15; // [sp+28h] [bp-230h]@1
  CryptoPP::Integer v16; // [sp+30h] [bp-228h]@1
  CryptoPP::Integer b; // [sp+58h] [bp-200h]@1
  CryptoPP::Integer v18; // [sp+80h] [bp-1D8h]@1
  char v19; // [sp+A8h] [bp-1B0h]@9
  CryptoPP::Integer result; // [sp+108h] [bp-150h]@9
  char v21; // [sp+130h] [bp-128h]@10
  char v22; // [sp+190h] [bp-C8h]@12
  __int64 v23; // [sp+1F0h] [bp-68h]@1
  int v24; // [sp+1F8h] [bp-60h]@3
  unsigned __int64 v25; // [sp+200h] [bp-58h]@6
  CryptoPP::Integer *v26; // [sp+208h] [bp-50h]@9
  CryptoPP::Integer *v27; // [sp+210h] [bp-48h]@9
  __int64 v28; // [sp+218h] [bp-40h]@9
  __int64 v29; // [sp+220h] [bp-38h]@9
  __int64 v30; // [sp+228h] [bp-30h]@9
  __int64 v31; // [sp+230h] [bp-28h]@10
  __int64 v32; // [sp+238h] [bp-20h]@10
  __int64 v33; // [sp+240h] [bp-18h]@12
  __int64 v34; // [sp+248h] [bp-10h]@12
  __int64 v35; // [sp+260h] [bp+8h]@1
  __int64 v36; // [sp+270h] [bp+18h]@1
  struct CryptoPP::Integer *v37; // [sp+278h] [bp+20h]@1

  v37 = a4;
  v36 = a3;
  v35 = a1;
  v23 = -2i64;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)a2 + 24i64))(a2);
  v15 = v4;
  CryptoPP::Integer::Integer(&b);
  CryptoPP::Integer::Integer(&v16);
  CryptoPP::Integer::Integer(&v18, v37);
  v24 = (unsigned __int8)(*(int (__fastcall **)(__int64))(*(_QWORD *)v15 + 40i64))(v15) && *(_DWORD *)(v35 + 64) > 1u;
  v13 = v24;
  for ( i = 0; ; ++i )
  {
    v25 = i + 1;
    v5 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::size(v35 + 112);
    if ( v25 >= v5 )
      break;
    CryptoPP::Integer::DivideByPowerOf2(&b, &v16, &v18, *(_DWORD *)(v35 + 64));
    std::swap<CryptoPP::Integer>(&v16, &v18);
    if ( v13 && CryptoPP::Integer::GetBit(&b, (unsigned int)(*(_DWORD *)(v35 + 64) - 1)) )
    {
      CryptoPP::Integer::operator++(&v18);
      v26 = CryptoPP::operator-(&result, (CryptoPP::Integer *)(v35 + 72), &b);
      v27 = v26;
      v6 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v35 + 112, i);
      v28 = *(_QWORD *)v15;
      LODWORD(v7) = (*(int (__fastcall **)(__int64, signed __int64))(v28 + 32))(v15, v6);
      LODWORD(v8) = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
                      &v19,
                      v7,
                      v27);
      v29 = v8;
      v30 = v8;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::push_back(
        v36,
        v8);
      CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(&v19);
      CryptoPP::Integer::~Integer(&result);
    }
    else
    {
      v9 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v35 + 112, i);
      LODWORD(v10) = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
                       &v21,
                       v9,
                       &b);
      v31 = v10;
      v32 = v10;
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::push_back(
        v36,
        v10);
      CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(&v21);
    }
  }
  v11 = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v35 + 112, i);
  LODWORD(v12) = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
                   &v22,
                   v11,
                   &v18);
  v33 = v12;
  v34 = v12;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::push_back(
    v36,
    v12);
  CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(&v22);
  CryptoPP::Integer::~Integer(&v18);
  CryptoPP::Integer::~Integer(&v16);
  CryptoPP::Integer::~Integer(&b);
}
