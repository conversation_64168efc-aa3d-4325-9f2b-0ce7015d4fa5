/*
 * Function: SQLInstallerErrorW
 * Address: 0x1404DB2F8
 */

__int16 __fastcall SQLInstallerErrorW(unsigned __int16 iError, unsigned int *pfErrorCode, unsigned __int16 *lpszErrorMsg, unsigned __int16 cbErrorMsgMax, unsigned __int16 *pcbErrorMsg)
{
  unsigned __int16 v5; // bp@1
  unsigned __int16 v6; // bx@1
  unsigned __int16 *v7; // rdi@1
  unsigned int *v8; // rsi@1
  __int64 (__cdecl *v9)(); // rax@1
  __int16 result; // ax@2

  v5 = iError;
  v6 = cbErrorMsgMax;
  v7 = lpszErrorMsg;
  v8 = pfErrorCode;
  v9 = ODBC___GetSetupProc("SQLInstallerErrorW");
  if ( v9 )
    result = ((int (__fastcall *)(_QWORD, unsigned int *, unsigned __int16 *, _QWORD))v9)(v5, v8, v7, v6);
  else
    result = 0;
  return result;
}
