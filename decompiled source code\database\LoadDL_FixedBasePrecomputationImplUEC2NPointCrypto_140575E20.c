/*
 * Function: ?Load@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x140575E20
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::Load(__int64 a1, __int64 a2, struct CryptoPP::BufferedTransformation *a3)
{
  __int64 v3; // rax@3
  __int64 v4; // rax@6
  _BYTE *v5; // rax@6
  char v6; // [sp+30h] [bp-108h]@1
  CryptoPP::BERGeneralDecoder v7; // [sp+40h] [bp-F8h]@1
  CryptoPP::EC2NPoint v8; // [sp+80h] [bp-B8h]@3
  CryptoPP::EC2NPoint v9; // [sp+B8h] [bp-80h]@6
  __int64 v10; // [sp+F0h] [bp-48h]@1
  __int64 v11; // [sp+F8h] [bp-40h]@1
  __int64 v12; // [sp+100h] [bp-38h]@3
  __int64 v13; // [sp+108h] [bp-30h]@3
  __int64 v14; // [sp+110h] [bp-28h]@6
  _BYTE *v15; // [sp+118h] [bp-20h]@6
  _BYTE *v16; // [sp+120h] [bp-18h]@6
  __int64 v17; // [sp+140h] [bp+8h]@1
  __int64 v18; // [sp+148h] [bp+10h]@1

  v18 = a2;
  v17 = a1;
  v10 = -2i64;
  CryptoPP::BERSequenceDecoder::BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7, a3, 0x30u);
  CryptoPP::BERDecodeUnsigned<unsigned int>((CryptoPP *)&v7, (int *)&v6, 2u, 1u, 1u);
  v11 = *(_QWORD *)(v17 + 72);
  (*(void (__fastcall **)(signed __int64, CryptoPP::BERGeneralDecoder *))(v11 + 8))(v17 + 72, &v7);
  *(_DWORD *)(v17 + 64) = CryptoPP::Integer::BitCount((CryptoPP::Integer *)(v17 + 72)) - 1;
  std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::clear(v17 + 112);
  while ( !CryptoPP::BERGeneralDecoder::EndReached(&v7) )
  {
    LODWORD(v3) = (*(int (__fastcall **)(__int64, CryptoPP::EC2NPoint *, CryptoPP::BERGeneralDecoder *))(*(_QWORD *)v18 + 32i64))(
                    v18,
                    &v8,
                    &v7);
    v12 = v3;
    v13 = v3;
    std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::push_back(v17 + 112, v3);
    CryptoPP::EC2NPoint::~EC2NPoint(&v8);
  }
  if ( !std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::empty(v17 + 112)
    && (unsigned __int8)(**(int (__fastcall ***)(_QWORD))v18)(v18) )
  {
    LODWORD(v4) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](v17 + 112, 0i64);
    v14 = *(_QWORD *)v18;
    LODWORD(v5) = (*(int (__fastcall **)(__int64, CryptoPP::EC2NPoint *, __int64))(v14 + 16))(v18, &v9, v4);
    v15 = v5;
    v16 = v5;
    CryptoPP::EC2NPoint::operator=((_BYTE *)(v17 + 8), v5);
    CryptoPP::EC2NPoint::~EC2NPoint(&v9);
  }
  CryptoPP::BERGeneralDecoder::MessageEnd(&v7);
  CryptoPP::BERSequenceDecoder::~BERSequenceDecoder((CryptoPP::BERSequenceDecoder *)&v7);
}
