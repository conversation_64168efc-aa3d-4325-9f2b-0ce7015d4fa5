/*
 * Function: ?Recover@PK_Verifier@CryptoPP@@UEBA?AUDecodingResult@2@PEAEPEAVPK_MessageAccumulator@2@@Z
 * Address: 0x1405F6390
 */

struct CryptoPP::DecodingResult *__fastcall CryptoPP::PK_Verifier::Recover(CryptoPP::PK_Verifier *this, struct CryptoPP::DecodingResult *retstr, unsigned __int8 *a3, struct CryptoPP::PK_MessageAccumulator *a4)
{
  __int64 v4; // rax@1
  char v6; // [sp+20h] [bp-28h]@1
  __int64 v7; // [sp+28h] [bp-20h]@1
  CryptoPP::PK_SignatureSchemeVtbl *v8; // [sp+30h] [bp-18h]@1
  CryptoPP::PK_Verifier *v9; // [sp+50h] [bp+8h]@1
  struct CryptoPP::DecodingResult *v10; // [sp+58h] [bp+10h]@1
  unsigned __int8 *v11; // [sp+60h] [bp+18h]@1

  v11 = a3;
  v10 = retstr;
  v9 = this;
  v7 = -2i64;
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::auto_ptr<CryptoPP::PK_MessageAccumulator>(&v6, a4);
  LODWORD(v4) = std::auto_ptr<CryptoPP::PK_MessageAccumulator>::operator*(&v6);
  v8 = v9->vfptr;
  ((void (__fastcall *)(CryptoPP::PK_Verifier *, struct CryptoPP::DecodingResult *, unsigned __int8 *, __int64))v8[1].AllowNonrecoverablePart)(
    v9,
    v10,
    v11,
    v4);
  std::auto_ptr<CryptoPP::PK_MessageAccumulator>::~auto_ptr<CryptoPP::PK_MessageAccumulator>(&v6);
  return v10;
}
