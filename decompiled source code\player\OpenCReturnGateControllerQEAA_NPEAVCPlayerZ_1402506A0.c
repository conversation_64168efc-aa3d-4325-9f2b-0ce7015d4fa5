/*
 * Function: ?Open@CReturnGateController@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x1402506A0
 */

char __fastcall CReturnGateController::Open(CReturnGateController *this, CPlayer *pkOwner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v5; // rax@9
  int v6; // eax@11
  CReturnGateCreateParam *v7; // rax@12
  unsigned __int16 v8; // ax@14
  __int64 v9; // [sp+0h] [bp-88h]@1
  CReturnGate *v10; // [sp+20h] [bp-68h]@10
  char v11; // [sp+28h] [bp-60h]@12
  CReturnGateCreateParam v12; // [sp+30h] [bp-58h]@12
  __int64 v13; // [sp+58h] [bp-30h]@4
  int v14; // [sp+60h] [bp-28h]@11
  CReturnGateCreateParam *v15; // [sp+68h] [bp-20h]@12
  CReturnGateCreateParam *pParam; // [sp+70h] [bp-18h]@12
  int v17; // [sp+78h] [bp-10h]@12
  CReturnGateController *v18; // [sp+90h] [bp+8h]@1
  CPlayer *pkObj; // [sp+98h] [bp+10h]@1

  pkObj = pkOwner;
  v18 = this;
  v2 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  if ( pkOwner )
  {
    if ( pkOwner->m_bInGuildBattle )
    {
      result = 0;
    }
    else if ( CReturnGateController::IsExistOwner(v18, pkOwner) )
    {
      v5 = CPlayerDB::GetCharNameA(&pkObj->m_Param);
      CLogFile::Write(&stru_1799C97D0, "CReturnGateController::Open ExistOwner! CharName:%s", v5);
      result = 0;
    }
    else
    {
      v10 = CReturnGateController::GetEmpty(v18);
      if ( v10 )
      {
        CReturnGateCreateParam::CReturnGateCreateParam(&v12, pkObj);
        v15 = v7;
        pParam = v7;
        v17 = CReturnGate::Open(v10, v7) == 0;
        v11 = v17;
        CReturnGateCreateParam::~CReturnGateCreateParam(&v12);
        if ( v11 )
        {
          result = 0;
        }
        else
        {
          v8 = CReturnGate::GetIndex(v10);
          if ( CNetIndexList::PushNode_Back(v18->m_pkUseInxList, v8) )
          {
            result = 1;
          }
          else
          {
            CLogFile::Write(&stru_1799C97D0, "CReturnGateController::Open m_pkUseInxList->PushNode_Back Failed!");
            result = 0;
          }
        }
      }
      else
      {
        v14 = CNetIndexList::size(v18->m_pkEmptyInxList);
        v6 = CNetIndexList::size(v18->m_pkUseInxList);
        CLogFile::Write(
          &stru_1799C97D0,
          "CReturnGateController::Open GetEmpty Failed! Use : %d Empty : %d",
          (unsigned int)v6,
          (unsigned int)v14);
        result = 0;
      }
    }
  }
  else
  {
    CLogFile::Write(&stru_1799C97D0, "CReturnGateController::Open NULL == pkOwner");
    result = 0;
  }
  return result;
}
