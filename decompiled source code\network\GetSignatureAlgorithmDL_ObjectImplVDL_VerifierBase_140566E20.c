/*
 * Function: ?GetSignatureAlgorithm@?$DL_ObjectImpl@V?$DL_VerifierBase@UEC2NPoint@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@V?$DL_SS@U?$DL_Keys_ECDSA@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VEC2N@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@U?$DL_Keys_ECDSA@VEC2N@CryptoPP@@@2@V?$DL_Algorithm_ECDSA@VEC2N@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@2@V?$DL_PublicKey_EC@VEC2N@CryptoPP@@@2@@CryptoPP@@MEBAAEBV?$DL_ElgamalLikeSignatureAlgorithm@UEC2NPoint@CryptoPP@@@2@XZ
 * Address: 0x140566E20
 */

int CryptoPP::DL_ObjectImpl<CryptoPP::DL_VerifierBase<CryptoPP::EC2NPoint>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_Keys_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_EC<CryptoPP::EC2N>>::GetSignatureAlgorithm()
{
  __int64 v0; // rax@1
  char v2; // [sp+20h] [bp-18h]@1
  unsigned __int8 v3; // [sp+21h] [bp-17h]@1

  memset(&v3, 0, sizeof(v3));
  LODWORD(v0) = CryptoPP::Singleton<CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>>,0>::Singleton<CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>>,0>(
                  &v2,
                  v3);
  return CryptoPP::Singleton<CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>>,0>::Ref(v0);
}
