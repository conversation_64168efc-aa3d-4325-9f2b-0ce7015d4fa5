/*
 * Function: ?Find@CReservedGuildScheduleMapGroup@GUILD_BATTLE@@QEAAPEAVCReservedGuildSchedulePage@2@K@Z
 * Address: 0x1403CCA30
 */

GUILD_BATTLE::CReservedGuildSchedulePage *__fastcall GUILD_BATTLE::CReservedGuildScheduleMapGroup::Find(GUILD_BATTLE::CReservedGuildScheduleMapGroup *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v7; // [sp+40h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+48h] [bp+10h]@1

  dwGuildSeriala = dwGuildSerial;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < (signed int)v7->m_byMaxPage; ++j )
  {
    if ( GUILD_BATTLE::CReservedGuildSchedulePage::Find(&v7->m_kList[j], dwGuildSeriala) )
      return &v7->m_kList[j];
  }
  return 0i64;
}
