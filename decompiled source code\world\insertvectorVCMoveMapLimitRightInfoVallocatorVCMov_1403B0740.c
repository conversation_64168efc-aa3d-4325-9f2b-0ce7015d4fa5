/*
 * Function: ?insert@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAXV?$_Vector_iterator@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@2@_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403B0740
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::insert(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *_Where, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-58h]@1
  char v8; // [sp+20h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v9; // [sp+38h] [bp-20h]@4
  __int64 v10; // [sp+40h] [bp-18h]@4
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v11; // [sp+48h] [bp-10h]@4
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *v12; // [sp+60h] [bp+8h]@1
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *__that; // [sp+68h] [bp+10h]@1
  unsigned __int64 v14; // [sp+70h] [bp+18h]@1
  CMoveMapLimitRightInfo *v15; // [sp+78h] [bp+20h]@1

  v15 = _Val;
  v14 = _Count;
  __that = _Where;
  v12 = this;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = -2i64;
  v9 = (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v8;
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    (std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *)&v8,
    _Where);
  v11 = v6;
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Insert_n(v12, v6, v14, v15);
  std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(__that);
}
