/*
 * Function: ?SendMsgAccount_UILockRefresh_Update@CUserDB@@QEAAXXZ
 * Address: 0x140118720
 */

void __fastcall CUserDB::SendMsgAccount_UILockRefresh_Update(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@9
  char v5; // [sp+3Ch] [bp-4Ch]@9
  char v6; // [sp+3Dh] [bp-4Bh]@9
  _GLBID v7; // [sp+3Eh] [bp-4Ah]@9
  char pbyType; // [sp+64h] [bp-24h]@9
  char v9; // [sp+65h] [bp-23h]@9
  CUserDB *v10; // [sp+90h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v10->m_byUILock != 255
    && !v10->m_byUserDgr
    && (v10->m_byUILock_InitFailCnt != v10->m_byUILock_FailCnt
     || v10->m_byUILock_InitFindPassFailCount != v10->m_byUILockFindPassFailCount) )
  {
    *(_DWORD *)szMsg = v10->m_dwAccountSerial;
    v5 = v10->m_byUILock_FailCnt;
    v6 = v10->m_byUILockFindPassFailCount;
    memset(&v7, 0, sizeof(v7));
    v7 = v10->m_gidGlobal;
    pbyType = 1;
    v9 = 19;
    CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, szMsg, 0xEu);
  }
}
