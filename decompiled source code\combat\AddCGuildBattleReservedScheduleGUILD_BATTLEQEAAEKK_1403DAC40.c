/*
 * Function: ?Add@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAAEKKPEAPEAVCGuildBattleSchedule@2@@Z
 * Address: 0x1403DAC40
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Add(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, GUILD_BATTLE::CGuildBattleSchedule **ppkSchedule)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  unsigned int dwStartInx; // [sp+20h] [bp-28h]@4
  GUILD_BATTLE::CGuildBattleSchedulePool *v9; // [sp+28h] [bp-20h]@4
  GUILD_BATTLE::CGuildBattleSchedule *v10; // [sp+30h] [bp-18h]@4
  char v11; // [sp+38h] [bp-10h]@6
  GUILD_BATTLE::CGuildBattleReservedSchedule *v12; // [sp+50h] [bp+8h]@1
  unsigned int dwStartTimeInxa; // [sp+58h] [bp+10h]@1
  unsigned int dwElapseTimeCnta; // [sp+60h] [bp+18h]@1
  GUILD_BATTLE::CGuildBattleSchedule **v15; // [sp+68h] [bp+20h]@1

  v15 = ppkSchedule;
  dwElapseTimeCnta = dwElapseTimeCnt;
  dwStartTimeInxa = dwStartTimeInx;
  v12 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  dwStartInx = dwStartTimeInx;
  v9 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
  v10 = GUILD_BATTLE::CGuildBattleSchedulePool::Get(v9, v12->m_uiScheduleListID, dwStartInx);
  if ( v10 )
  {
    v11 = GUILD_BATTLE::CGuildBattleSchedule::Set(v10, dwStartTimeInxa, dwElapseTimeCnta);
    if ( v11 )
    {
      result = v11;
    }
    else
    {
      GUILD_BATTLE::CGuildBattleReservedSchedule::UpdateUseField(v12, dwStartTimeInxa, dwElapseTimeCnta);
      v12->m_pkSchedule[dwStartInx] = v10;
      *v15 = v10;
      result = 0;
    }
  }
  else
  {
    result = 110;
  }
  return result;
}
