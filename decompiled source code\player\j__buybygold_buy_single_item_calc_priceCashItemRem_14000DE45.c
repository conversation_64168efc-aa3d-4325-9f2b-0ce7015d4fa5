/*
 * Function: j_?_buybygold_buy_single_item_calc_price@CashItemRemoteStore@@AEAAKPEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU__item@3@PEAU_param_cashitem_dblog@@PEAU_CashShop_fld@@PEA_NAEAU_result_csi_buy_zocl@@AEAK@Z
 * Address: 0x14000DE45
 */

unsigned int __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, _CashShop_fld *pCsFld, bool *bCouponUseCheck, _result_csi_buy_zocl *Send, unsigned int *dwDiscount)
{
  return CashItemRemoteStore::_buybygold_buy_single_item_calc_price(
           this,
           pOne,
           pRecv,
           pSrc,
           pSheet,
           pCsFld,
           bCouponUseCheck,
           Send,
           dwDiscount);
}
