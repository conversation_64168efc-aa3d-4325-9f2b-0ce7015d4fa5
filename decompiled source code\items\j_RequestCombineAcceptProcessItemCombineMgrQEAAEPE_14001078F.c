/*
 * Function: j_?RequestCombineAcceptProcess@ItemCombineMgr@@QEAAEPEAU_combine_ex_item_accept_request_clzo@@PEAU_combine_ex_item_accept_result_zocl@@@Z
 * Address: 0x14001078F
 */

char __fastcall ItemCombineMgr::RequestCombineAcceptProcess(ItemCombineMgr *this, _combine_ex_item_accept_request_clzo *pRecv, _combine_ex_item_accept_result_zocl *pSend)
{
  return ItemCombineMgr::RequestCombineAcceptProcess(this, pRecv, pSend);
}
