/*
 * Function: ?InputSignature@TF_VerifierBase@CryptoPP@@UEBAXAEAVPK_MessageAccumulator@2@PEBE_K@Z
 * Address: 0x140623020
 */

void __fastcall CryptoPP::TF_VerifierBase::InputSignature(CryptoPP::TF_VerifierBase *this, struct CryptoPP::PK_MessageAccumulator *a2, const unsigned __int8 *a3, unsigned __int64 a4)
{
  __int64 *v4; // rax@1
  __int64 v5; // rax@1
  int v6; // eax@1
  unsigned __int64 v7; // rax@1
  unsigned __int64 v8; // rax@1
  unsigned __int64 v9; // rax@4
  __int64 v10; // rax@4
  unsigned __int64 v11; // rax@4
  CryptoPP::Integer *v12; // rax@5
  unsigned __int8 *v13; // rax@6
  char v14; // [sp+20h] [bp-128h]@1
  __int64 v15; // [sp+28h] [bp-120h]@1
  CryptoPP::Integer v16; // [sp+30h] [bp-118h]@4
  struct CryptoPP::PK_MessageAccumulator *v17; // [sp+58h] [bp-F0h]@1
  __int64 *v18; // [sp+60h] [bp-E8h]@1
  CryptoPP::PK_SignatureScheme::KeyTooShort v19; // [sp+68h] [bp-E0h]@2
  CryptoPP::Integer v20; // [sp+B8h] [bp-90h]@4
  __int64 v21; // [sp+E0h] [bp-68h]@1
  CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v22; // [sp+E8h] [bp-60h]@1
  __int64 v23; // [sp+F0h] [bp-58h]@1
  __int64 v24; // [sp+F8h] [bp-50h]@1
  unsigned __int64 v25; // [sp+100h] [bp-48h]@1
  CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v26; // [sp+108h] [bp-40h]@4
  __int64 v27; // [sp+110h] [bp-38h]@4
  CryptoPP::Integer *v28; // [sp+118h] [bp-30h]@4
  CryptoPP::Integer *v29; // [sp+120h] [bp-28h]@4
  unsigned __int64 v30; // [sp+128h] [bp-20h]@4
  unsigned __int64 v31; // [sp+130h] [bp-18h]@6
  CryptoPP::TF_VerifierBase *v32; // [sp+150h] [bp+8h]@1
  unsigned __int8 *v33; // [sp+160h] [bp+18h]@1
  unsigned __int64 v34; // [sp+168h] [bp+20h]@1

  v34 = a4;
  v33 = (unsigned __int8 *)a3;
  v32 = this;
  v21 = -2i64;
  v17 = a2;
  ((void (__fastcall *)(CryptoPP::TF_VerifierBase *, char *))this->vfptr[1].RecoverablePartFirst)(this, &v14);
  v22 = v32->vfptr;
  LODWORD(v4) = ((int (__fastcall *)(signed __int64))v22->GetMessageEncodingInterface)((signed __int64)&v32->vfptr);
  v18 = v4;
  LODWORD(v5) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v17->vfptr[9].__vecDelDtor)(v17);
  v23 = v5;
  v6 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 56i64))(v5);
  v24 = *v18;
  LODWORD(v7) = (*(int (__fastcall **)(__int64 *, __int64, _QWORD))(v24 + 8))(v18, v15, (unsigned int)v6);
  v25 = v7;
  LODWORD(v8) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v32);
  if ( v8 < v25 )
  {
    CryptoPP::PK_SignatureScheme::KeyTooShort::KeyTooShort(&v19);
    CxxThrowException_0((__int64)&v19, (__int64)&TI4_AVKeyTooShort_PK_SignatureScheme_CryptoPP__);
  }
  LODWORD(v9) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeLength(v32);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v17[4],
    v9);
  v26 = v32->vfptr;
  LODWORD(v10) = ((int (__fastcall *)(signed __int64))v26->GetTrapdoorFunctionInterface)((signed __int64)&v32->vfptr);
  v27 = v10;
  v28 = CryptoPP::Integer::Integer(&v20, v33, v34, 0);
  v29 = v28;
  (*(void (__fastcall **)(__int64, CryptoPP::Integer *, CryptoPP::Integer *))(*(_QWORD *)v27 + 56i64))(v27, &v16, v28);
  CryptoPP::Integer::~Integer(&v20);
  v30 = (unsigned int)CryptoPP::Integer::BitCount(&v16);
  LODWORD(v11) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::TF_Base<CryptoPP::TrapdoorFunction,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v32);
  if ( v30 > v11 )
  {
    LODWORD(v12) = CryptoPP::Integer::Zero();
    CryptoPP::Integer::operator=(&v16, v12);
  }
  v31 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v17[4]);
  v13 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v17[4]);
  CryptoPP::Integer::Encode(&v16, v13, v31, 0);
  CryptoPP::Integer::~Integer(&v16);
}
