/*
 * Function: ?SQLFetch_RetErrCode@CRFNewDatabase@@QEAAEPEAD@Z
 * Address: 0x140485020
 */

char __fastcall CRFNewDatabase::SQLFetch_RetErrCode(CRFNewDatabase *this, char *strQuery)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  __int64 v5; // [sp+0h] [bp-48h]@1
  void *SQLStmt; // [sp+20h] [bp-28h]@8
  __int16 v7; // [sp+30h] [bp-18h]@4
  char v8; // [sp+34h] [bp-14h]@6
  CRFNewDatabase *v9; // [sp+50h] [bp+8h]@1
  char *strQuerya; // [sp+58h] [bp+10h]@1

  strQuerya = strQuery;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = SQLFetch_0(v9->m_hStmtSelect);
  if ( v7 && v7 != 1 )
  {
    v8 = 0;
    if ( v7 == 100 )
    {
      v8 = 2;
    }
    else
    {
      SQLStmt = v9->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog(v9, v7, strQuerya, "SQLFetch", SQLStmt);
      CRFNewDatabase::ErrorAction(v9, v7, v9->m_hStmtSelect);
      v8 = 1;
    }
    if ( v9->m_hStmtSelect )
      SQLCloseCursor_0(v9->m_hStmtSelect);
    result = v8;
  }
  else
  {
    result = 0;
  }
  return result;
}
