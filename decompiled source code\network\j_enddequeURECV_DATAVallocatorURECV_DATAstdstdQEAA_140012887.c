/*
 * Function: j_?end@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@2@XZ
 * Address: 0x140012887
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::end(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result)
{
  return std::deque<RECV_DATA,std::allocator<RECV_DATA>>::end(this, result);
}
