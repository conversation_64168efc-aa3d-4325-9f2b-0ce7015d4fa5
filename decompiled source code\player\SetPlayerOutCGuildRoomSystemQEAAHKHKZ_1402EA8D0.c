/*
 * Function: ?SetPlayerOut@CGuildRoomSystem@@QEAAHKHK@Z
 * Address: 0x1402EA8D0
 */

signed __int64 __fastcall CGuildRoomSystem::SetPlayerOut(CGuildRoomSystem *this, unsigned int dwGuildSerial, int n, unsigned int dwCharSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v6; // rax@7
  CGuildRoomInfo *v7; // rax@8
  CGuildRoomInfo *v8; // rax@9
  signed __int64 result; // rax@10
  __int64 v10; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGuildRoomSystem *v12; // [sp+40h] [bp+8h]@1
  unsigned int v13; // [sp+48h] [bp+10h]@1
  int na; // [sp+50h] [bp+18h]@1
  unsigned int dwCharSeriala; // [sp+58h] [bp+20h]@1

  dwCharSeriala = dwCharSerial;
  na = n;
  v13 = dwGuildSerial;
  v12 = this;
  v4 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; ; ++j )
  {
    if ( j >= 90 )
      return 1i64;
    v6 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v12->m_vecGuildRoom, j);
    if ( CGuildRoomInfo::IsRent(v6) )
    {
      v7 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v12->m_vecGuildRoom, j);
      if ( CGuildRoomInfo::GetGuildSerial(v7) == v13 )
        break;
    }
  }
  v8 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v12->m_vecGuildRoom, j);
  if ( CGuildRoomInfo::SetPlayerOut(v8, na, dwCharSeriala, -1) )
    result = 0i64;
  else
    result = 2i64;
  return result;
}
