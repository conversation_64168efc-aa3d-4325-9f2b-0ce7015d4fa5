/*
 * Function: _std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0_::operator___::_1_::dtor$0
 * Address: 0x14031D9E0
 */

void __fastcall std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0_::operator___::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>((std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)(a2 + 40));
}
