/*
 * Function: _std::vector_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64___::_Insert_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64______::_1_::catch$0
 * Address: 0x140374730
 */

void __fastcall __noreturn std::vector_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64___::_Insert_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Destroy(
    *(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 736),
    *(CUnmannedTraderClassInfo ***)(a2 + 64),
    *(CUnmannedTraderClassInfo ***)(a2 + 72));
  std::allocator<CUnmannedTraderClassInfo *>::deallocate(
    (std::allocator<CUnmannedTraderClassInfo *> *)(*(_QWORD *)(v2 + 736) + 8i64),
    *(CUnmannedTraderClassInfo ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
