/*
 * Function: ?CompleteUpdateCheatRegistTime@CUnmannedTraderUserInfo@@QEAAXPEAD@Z
 * Address: 0x1403574B0
 */

void __fastcall CUnmannedTraderUserInfo::CompleteUpdateCheatRegistTime(CUnmannedTraderUserInfo *this, char *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v4; // rax@9
  __int64 v5; // [sp+0h] [bp-C8h]@1
  char *v6; // [sp+20h] [bp-A8h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-90h]@4
  unsigned __int8 j; // [sp+54h] [bp-74h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v9; // [sp+58h] [bp-70h]@8
  bool v10; // [sp+70h] [bp-58h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v11; // [sp+78h] [bp-50h]@8
  __int64 v12; // [sp+90h] [bp-38h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v13; // [sp+98h] [bp-30h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__that; // [sp+A0h] [bp-28h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v15; // [sp+A8h] [bp-20h]@8
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+B0h] [bp-18h]@8
  CUnmannedTraderUserInfo *v17; // [sp+D0h] [bp+8h]@1

  v17 = this;
  v2 = &v5;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v6 = pLoadData;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
    &v17->m_vecRegistItemInfo,
    &result);
  for ( j = 0; j < (signed int)(unsigned __int8)v6[1]; ++j )
  {
    if ( !v6[8 * j + 8] )
    {
      v13 = CUnmannedTraderUserInfo::Find(v17, &v9, *(_DWORD *)&v6[8 * j + 12]);
      __that = v13;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator=(
        &result,
        v13);
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v9);
      v15 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
              &v17->m_vecRegistItemInfo,
              &v11);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v15;
      v10 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
              (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v15->_Mycont);
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v11);
      if ( !v10 )
      {
        v4 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
        CUnmannedTraderRegistItemInfo::SetOverRegistTime(v4);
      }
    }
  }
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
}
