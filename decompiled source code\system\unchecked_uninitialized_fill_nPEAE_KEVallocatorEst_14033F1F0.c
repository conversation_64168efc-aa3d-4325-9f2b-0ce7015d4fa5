/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAE_KEV?$allocator@E@std@@@stdext@@YAXPEAE_KAEBEAEAV?$allocator@E@std@@@Z
 * Address: 0x14033F1F0
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<unsigned char *,unsigned __int64,unsigned char,std::allocator<unsigned char>>(char *_First, unsigned __int64 _Count, const char *_Val, std::allocator<unsigned char> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  char *__formal; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  char *_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<unsigned char> *v12; // [sp+68h] [bp+20h]@1

  v12 = _Al;
  _Vala = (char *)_Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<unsigned char *,unsigned char *>(&__formal, &__formal);
  std::_Uninit_fill_n<unsigned char *,unsigned __int64,unsigned char,std::allocator<unsigned char>>(
    __formal,
    _Counta,
    _Vala,
    v12,
    v8,
    v7);
}
