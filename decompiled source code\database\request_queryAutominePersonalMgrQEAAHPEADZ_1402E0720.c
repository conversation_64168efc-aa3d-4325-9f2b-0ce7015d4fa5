/*
 * Function: ?request_query@AutominePersonalMgr@@QEAAHPEAD@Z
 * Address: 0x1402E0720
 */

signed __int64 __usercall AutominePersonalMgr::request_query@<rax>(AutominePersonalMgr *this@<rcx>, char *pdata@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@13
  int v7; // ecx@13
  unsigned int v8; // edi@13
  signed __int64 result; // rax@6
  __int64 v10; // [sp-20h] [bp-2888h]@1
  int v11; // [sp+0h] [bp-2868h]@13
  int v12; // [sp+8h] [bp-2860h]@13
  char *v13; // [sp+10h] [bp-2858h]@7
  char *v14; // [sp+18h] [bp-2850h]@11
  char Dest; // [sp+30h] [bp-2838h]@11
  char v16; // [sp+31h] [bp-2837h]@11
  int v17; // [sp+2834h] [bp-34h]@11
  int j; // [sp+2838h] [bp-30h]@11
  char v19; // [sp+2848h] [bp-20h]@4
  char *v20; // [sp+2850h] [bp-18h]@13
  unsigned __int64 v21; // [sp+2858h] [bp-10h]@4
  AutominePersonalMgr *v22; // [sp+2870h] [bp+8h]@1
  char *v23; // [sp+2878h] [bp+10h]@1

  v23 = pdata;
  v22 = this;
  v3 = alloca(a3);
  v4 = &v10;
  for ( i = 2592i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v21 = (unsigned __int64)&v10 ^ _security_cookie;
  v19 = *pdata;
  if ( v19 )
  {
    if ( v19 == 1 )
    {
      v14 = pdata;
      Dest = 0;
      memset(&v16, 0, 0x27FFui64);
      v17 = 0;
      sprintf(&Dest, "update [dbo].[tbl_aminepersonal_inven] set ");
      v17 = strlen_0(&Dest);
      for ( j = 0; j < (unsigned __int8)v14[12]; ++j )
      {
        v6 = (unsigned __int8)v14[12 * j + 24];
        v7 = (unsigned __int8)v14[12 * j + 16];
        v8 = (unsigned __int8)v14[12 * j + 16];
        v20 = &Dest + v17;
        v12 = v6;
        v11 = v7;
        sprintf(&Dest + v17, ",K%d=%d,N%d=%d,", v8, *(_DWORD *)&v14[12 * j + 20]);
      }
      v17 = strlen_0(&Dest);
      sprintf(&Dest + v17 - 1, " where avatorserial = %d", *((_DWORD *)v14 + 2));
      if ( CRFWorldDatabase::update_amine_personal(pkDB, &Dest) )
        result = 0i64;
      else
        result = 24i64;
    }
    else
    {
      result = 24i64;
    }
  }
  else
  {
    v13 = pdata;
    if ( CRFWorldDatabase::select_amine_personal(pkDB, *((_DWORD *)pdata + 2)) != 2
      || CRFWorldDatabase::insert_amine_personal(pkDB, *((_DWORD *)v13 + 2)) )
    {
      result = 0i64;
    }
    else
    {
      result = 24i64;
    }
  }
  return result;
}
