/*
 * Function: ?SendMsg_StateChange@CDarkHole@@QEAAXXZ
 * Address: 0x140164240
 */

void __fastcall CDarkHole::SendMsg_StateChange(CDarkHole *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-78h]@1
  _darkhole_state_change_zocl v5; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CDarkHole *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5.wIndex = v8->m_ObjID.m_wIndex;
  v5.dwSerial = v8->m_dwObjSerial;
  v5.bHurry = v8->m_bHurry;
  pbyType = 35;
  v7 = 103;
  v3 = _darkhole_state_change_zocl::size(&v5);
  CGameObject::CircleReport((CGameObject *)&v8->vfptr, &pbyType, (char *)&v5, v3, 0);
}
