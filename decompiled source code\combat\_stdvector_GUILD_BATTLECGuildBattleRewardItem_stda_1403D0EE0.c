/*
 * Function: _std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::_Assign_n_::_1_::dtor$0
 * Address: 0x1403D0EE0
 */

void __fastcall std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::_Assign_n_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(*(std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > **)(a2 + 96));
}
