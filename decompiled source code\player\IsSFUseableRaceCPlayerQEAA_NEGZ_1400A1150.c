/*
 * Function: ?IsSFUseableRace@CPlayer@@QEAA_NEG@Z
 * Address: 0x1400A1150
 */

char __fastcall CPlayer::IsSFUseableRace(CPlayer *this, char byEffectCode, unsigned __int16 wEffectIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned __int8 v7; // [sp+20h] [bp-28h]@4
  _base_fld *v8; // [sp+28h] [bp-20h]@7
  _base_fld *v9; // [sp+30h] [bp-18h]@10
  CPlayer *v10; // [sp+50h] [bp+8h]@1
  char v11; // [sp+58h] [bp+10h]@1
  unsigned __int16 v12; // [sp+60h] [bp+18h]@1

  v12 = wEffectIndex;
  v11 = byEffectCode;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CPlayerDB::GetRaceSexCode(&v10->m_Param);
  if ( v11 && v11 != 2 && v11 != 3 )
  {
    v9 = CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)v11, v12);
    if ( v9[5].m_strCode[v7 + 4] == 49 )
      return 1;
  }
  else
  {
    v8 = CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)v11, v12);
    if ( v8[5].m_strCode[v7 + 4] == 49 )
      return 1;
  }
  return 0;
}
