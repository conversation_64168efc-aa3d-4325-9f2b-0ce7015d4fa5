/*
 * Function: ?CopyMessagesTo@Store@CryptoPP@@QEBAIAEAVBufferedTransformation@2@IAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x1405FE150
 */

signed __int64 __fastcall CryptoPP::Store::CopyMessagesTo(__int64 a1, __int64 a2, int a3, __int64 a4)
{
  signed __int64 result; // rax@3
  __int64 v5; // [sp+30h] [bp+8h]@1
  __int64 v6; // [sp+38h] [bp+10h]@1
  __int64 v7; // [sp+48h] [bp+20h]@1

  v7 = a4;
  v6 = a2;
  v5 = a1;
  if ( !*(_BYTE *)(a1 + 32) && a3 )
  {
    CryptoPP::BufferedTransformation::CopyTo(a1);
    if ( (*(int (__fastcall **)(__int64))(*(_QWORD *)v5 + 112i64))(v5) )
    {
      (*(void (__fastcall **)(__int64))(*(_QWORD *)v5 + 112i64))(v5);
      CryptoPP::BufferedTransformation::ChannelMessageEnd(v6, v7);
    }
    result = 1i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
