/*
 * Function: ?_LootItem_Rwp@CMonster@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140144D90
 */

char __fastcall CMonster::_LootItem_Rwp(CMonster *this, CPlayer *pOwner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-88h]@1
  CMapData *pMap; // [sp+30h] [bp-58h]@16
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-50h]@16
  float *pStdPos; // [sp+40h] [bp-48h]@16
  bool bHide; // [sp+48h] [bp-40h]@16
  bool v10; // [sp+50h] [bp-38h]@6
  CPlayer *pOwnera; // [sp+58h] [bp-30h]@6
  unsigned int dwPartyBossSerial; // [sp+60h] [bp-28h]@6
  char v13; // [sp+64h] [bp-24h]@9
  int j; // [sp+68h] [bp-20h]@9
  char *v15; // [sp+70h] [bp-18h]@12
  _STORAGE_LIST::_db_con *pItem; // [sp+78h] [bp-10h]@15
  CMonster *v17; // [sp+90h] [bp+8h]@1
  CPlayer *v18; // [sp+98h] [bp+10h]@1

  v18 = pOwner;
  v17 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v17->m_pEventRespawn )
  {
    v10 = 0;
    pOwnera = pOwner;
    dwPartyBossSerial = -1;
    if ( CPartyPlayer::IsPartyMode(pOwner->m_pPartyMgr) )
    {
      dwPartyBossSerial = v18->m_pPartyMgr->m_pPartyBoss->m_id.dwSerial;
      pOwnera = CPartyPlayer::GetLootAuthor(v18->m_pPartyMgr);
      if ( !pOwnera )
        v10 = 1;
    }
    v13 = 0;
    for ( j = 0; j < v17->m_pEventRespawn->nUseRewardItemNum; ++j )
    {
      v15 = &v17->m_pEventRespawn->RewardItem[j].byItemTableCode;
      if ( (!*((_QWORD *)v15 + 2) || (_monster_fld *)*((_QWORD *)v15 + 2) == v17->m_pMonRec)
        && *((_DWORD *)v15 + 6) > rand() % 100 )
      {
        pItem = MakeLoot(*v15, **((_WORD **)v15 + 1));
        if ( pItem )
        {
          bHide = 0;
          pStdPos = v17->m_fCurPos;
          wLayerIndex = v17->m_wMapLayerIndex;
          pMap = v17->m_pCurMap;
          if ( CreateItemBox(
                 pItem,
                 pOwnera,
                 dwPartyBossSerial,
                 v10,
                 (CCharacter *)&v17->vfptr,
                 0,
                 pMap,
                 wLayerIndex,
                 v17->m_fCurPos,
                 0) )
          {
            v13 = 1;
            if ( v17->m_pMonRec->m_bMonsterCondition == 1 )
              CLogFile::Write(&CMonster::s_logTrace_Boss_Looting, "\t LootItem : %s", *((_QWORD *)v15 + 1) + 4i64);
            v13 = 1;
          }
        }
      }
    }
    result = v13;
  }
  else
  {
    result = 0;
  }
  return result;
}
