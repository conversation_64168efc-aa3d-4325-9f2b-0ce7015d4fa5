/*
 * Function: ?CreateRepMonster@@YAPEAVCMonster@@PEAVCMapData@@GPEAMPEADPEAV1@_N4444@Z
 * Address: 0x140148D90
 */

CMonster *__fastcall CreateRepMonster(CMapData *pMap, unsigned __int16 wLayer, float *fPos, char *pszMonsterCode, CMonster *pParent, bool bRobExp, bool bRewardExp, bool bDungeon, bool bWithoutFail, bool bApplyRopExpField)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  CMonster *result; // rax@5
  __int64 v13; // [sp+0h] [bp-A8h]@1
  CMonster *v14; // [sp+20h] [bp-88h]@4
  _monster_create_setdata Dst; // [sp+40h] [bp-68h]@6
  _base_fld *v16; // [sp+88h] [bp-20h]@9
  bool v17; // [sp+90h] [bp-18h]@9
  CMapData *v18; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v19; // [sp+B8h] [bp+10h]@1
  float *Src; // [sp+C0h] [bp+18h]@1
  const char *szRecordCode; // [sp+C8h] [bp+20h]@1

  szRecordCode = pszMonsterCode;
  Src = fPos;
  v19 = wLayer;
  v18 = pMap;
  v10 = &v13;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v14 = SearchEmptyMonster(bWithoutFail);
  if ( v14 )
  {
    _monster_create_setdata::_monster_create_setdata(&Dst);
    Dst.m_pMap = v18;
    Dst.m_nLayerIndex = v19;
    Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C6210, szRecordCode);
    if ( Dst.m_pRecordSet )
    {
      memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
      Dst.pActiveRec = 0i64;
      Dst.pDumPosition = 0i64;
      Dst.pParent = pParent;
      Dst.bDungeon = bDungeon;
      if ( bApplyRopExpField )
      {
        v16 = Dst.m_pRecordSet;
        v17 = *(_DWORD *)&Dst.m_pRecordSet[4].m_strCode[4] != 0;
        Dst.bRobExp = v17;
      }
      else
      {
        Dst.bRobExp = bRobExp;
      }
      Dst.bRewardExp = bRewardExp;
      CMonster::Create(v14, &Dst);
      result = v14;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
