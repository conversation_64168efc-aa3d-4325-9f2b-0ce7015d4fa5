/*
 * Function: ?Select_NpcData@CRFWorldDatabase@@QEAAEKPEAK@Z
 * Address: 0x14049F050
 */

char __fastcall CRFWorldDatabase::Select_NpcData(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int *pNpcData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@24
  SQLLEN v9; // [sp+38h] [bp-150h]@24
  __int16 v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int j; // [sp+164h] [bp-24h]@4
  int v13; // [sp+168h] [bp-20h]@4
  char v14; // [sp+16Ch] [bp-1Ch]@16
  unsigned __int64 v15; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v16; // [sp+190h] [bp+8h]@1
  unsigned int *v17; // [sp+1A0h] [bp+18h]@1

  v17 = pNpcData;
  v16 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  j = 0;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_NpcData( %d ) }", dwSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v10 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v10, v16->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v14 = 0;
        if ( v10 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v10, v16->m_hStmtSelect);
          v14 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v14;
      }
      else
      {
        for ( j = 0; j < 6; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v16->m_hStmtSelect, v13, 4, &v17[j], 0i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        if ( v16->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
