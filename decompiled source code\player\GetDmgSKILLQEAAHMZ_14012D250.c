/*
 * Function: ?GetDmg@SKILL@@QEAAHM@Z
 * Address: 0x14012D250
 */

__int64 __fastcall SKILL::GetDmg(SKILL *this, float fDamRate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@9
  __int64 v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@4
  int v7; // [sp+24h] [bp-24h]@4
  int v8; // [sp+28h] [bp-20h]@4
  int v9; // [sp+2Ch] [bp-1Ch]@7
  int v10; // [sp+30h] [bp-18h]@4
  float v11; // [sp+34h] [bp-14h]@7
  float v12; // [sp+38h] [bp-10h]@10
  float v13; // [sp+3Ch] [bp-Ch]@10
  SKILL *v14; // [sp+50h] [bp+8h]@1

  v14 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = v14->m_StdDmg;
  v7 = v14->m_MinDmg;
  v8 = v14->m_MaxDmg;
  v6 = (signed int)ffloor((float)v6 * fDamRate);
  v10 = (signed int)ffloor((float)v6 * 0.89999998);
  if ( v6 - v10 <= 0 )
    v7 = 0;
  else
    v7 = rand() % (v6 - v10) + v10;
  v8 = 2 * v6 - v7;
  v11 = (float)(v8 + 125) / (float)(v8 + 50);
  v9 = (signed int)ffloor((float)((float)v8 * v11) + 0.5);
  v14->m_IsCritical = 0;
  if ( v6 != v7 && v8 != v6 )
  {
    v12 = (float)(rand() % 100);
    v13 = 0.0;
    if ( (float)v14->m_MinProb < v12 )
    {
      if ( (float)(v14->m_MinProb + v14->m_MaxProb) < v12 )
      {
        v14->m_IsCritical = 1;
        result = (unsigned int)v9;
      }
      else
      {
        v13 = (float)(rand() % (v8 - v6));
        result = (unsigned int)(signed int)ffloor((float)v6 + v13);
      }
    }
    else
    {
      v13 = (float)(rand() % (v6 - v7));
      result = (unsigned int)(signed int)ffloor((float)v7 + v13);
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
