/*
 * Function: ?IsNull@CGuildBattleRewardItem@GUILD_BATTLE@@QEBA_NXZ
 * Address: 0x1403EAEA0
 */

bool __fastcall GUILD_BATTLE::CGuildBattleRewardItem::IsNull(GUILD_BATTLE::CGuildBattleRewardItem *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5 == &GUILD_BATTLE::CGuildBattleRewardItem::ms_kNullObj;
}
