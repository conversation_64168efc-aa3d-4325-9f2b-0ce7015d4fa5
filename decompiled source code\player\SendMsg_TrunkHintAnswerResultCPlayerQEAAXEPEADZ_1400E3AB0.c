/*
 * Function: ?SendMsg_TrunkHintAnswerResult@CPlayer@@QEAAXEPEAD@Z
 * Address: 0x1400E3AB0
 */

void __fastcall CPlayer::SendMsg_TrunkHintAnswerResult(CPlayer *this, char byRetCode, char *pwszPassword)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  char Dest; // [sp+39h] [bp-5Fh]@5
  char pbyType; // [sp+64h] [bp-34h]@6
  char v9; // [sp+65h] [bp-33h]@6
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  CPlayer *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byRetCode;
  if ( !byRetCode )
    strcpy_0(&Dest, pwszPassword);
  pbyType = 34;
  v9 = 23;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xEu);
}
