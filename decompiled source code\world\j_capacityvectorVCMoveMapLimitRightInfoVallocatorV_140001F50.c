/*
 * Function: j_?capacity@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEBA_KXZ
 * Address: 0x140001F50
 */

unsigned __int64 __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::capacity(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this)
{
  return std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::capacity(this);
}
