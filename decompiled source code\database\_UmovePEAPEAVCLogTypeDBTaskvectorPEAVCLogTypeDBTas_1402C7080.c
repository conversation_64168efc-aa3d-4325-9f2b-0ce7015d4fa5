/*
 * Function: ??$_Umove@PEAPEAVCLogTypeDBTask@@@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAPEAPEAVCLogTypeDBTask@@PEAPEAV2@00@Z
 * Address: 0x1402C7080
 */

CLogTypeDBTask **__fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Umove<CLogTypeDBTask * *>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask **_First, CLogTypeDBTask **_Last, CLogTypeDBTask **_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CLogTypeDBTask * *,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
