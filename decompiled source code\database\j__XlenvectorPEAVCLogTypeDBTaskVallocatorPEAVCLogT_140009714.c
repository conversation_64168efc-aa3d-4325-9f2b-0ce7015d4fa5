/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@KAXXZ
 * Address: 0x140009714
 */

void __fastcall __noreturn std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_<PERSON>len(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_<PERSON><PERSON>(this);
}
