/*
 * Function: ?IsBeCirclePlayer@CGameObject@@QEAA_NH@Z
 * Address: 0x14017C440
 */

bool __fastcall CGameObject::IsBeCirclePlayer(CGameObject *this, int nRange)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-78h]@1
  _pnt_rect pRect; // [sp+28h] [bp-50h]@6
  _sec_info *v7; // [sp+48h] [bp-30h]@6
  int v8; // [sp+50h] [bp-28h]@6
  int j; // [sp+54h] [bp-24h]@6
  int k; // [sp+58h] [bp-20h]@8
  unsigned int dwSecIndex; // [sp+5Ch] [bp-1Ch]@11
  CObjectList *v12; // [sp+60h] [bp-18h]@11
  CGameObject *v13; // [sp+80h] [bp+8h]@1
  int nRadius; // [sp+88h] [bp+10h]@1

  nRadius = nRange;
  v13 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v13->m_bPlayerCircleList )
  {
    result = 1;
  }
  else
  {
    v7 = CMapData::GetSecInfo(v13->m_pCurMap);
    CMapData::GetRectInRadius(v13->m_pCurMap, &pRect, nRadius, v13->m_dwCurSec);
    v8 = 0;
    for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
    {
      for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
      {
        dwSecIndex = v7->m_nSecNumW * j + k;
        v12 = CMapData::GetSectorListPlayer(v13->m_pCurMap, v13->m_wMapLayerIndex, dwSecIndex);
        if ( v12 && v12->m_nSize > 0 )
          return 1;
      }
    }
    result = 0;
  }
  return result;
}
