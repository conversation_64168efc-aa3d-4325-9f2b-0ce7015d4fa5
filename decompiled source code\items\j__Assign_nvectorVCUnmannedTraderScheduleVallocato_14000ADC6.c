/*
 * Function: j_?_Assign_n@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderSchedule@@@Z
 * Address: 0x14000ADC6
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Assign_n(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Assign_n(this, _Count, _Val);
}
