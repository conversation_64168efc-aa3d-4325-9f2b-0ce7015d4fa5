/*
 * Function: j_?MakePage@CPossibleBattleGuildListManager@GUILD_BATTLE@@AEAA_NEEAEAG@Z
 * Address: 0x14000F9CF
 */

bool __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::MakePage(GUILD_BATTLE::CPossibleBattleGuildListManager *this, char byRace, char ucPage, unsigned __int16 *wLastGuildInx)
{
  return GUILD_BATTLE::CPossibleBattleGuildListManager::MakePage(this, byRace, ucPage, wLastGuildInx);
}
