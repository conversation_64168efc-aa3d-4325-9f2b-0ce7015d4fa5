/*
 * Function: ??0_remain_num_of_good@CashItemRemoteStore@@QEAA@XZ
 * Address: 0x1403047C0
 */

void __fastcall CashItemRemoteStore::_remain_num_of_good::_remain_num_of_good(CashItemRemoteStore::_remain_num_of_good *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore::_remain_num_of_good *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x10ui64);
}
