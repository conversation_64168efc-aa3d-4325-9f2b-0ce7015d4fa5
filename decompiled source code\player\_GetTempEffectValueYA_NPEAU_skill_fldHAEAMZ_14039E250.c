/*
 * Function: ?_GetTempEffectValue@@YA_NPEAU_skill_fld@@HAEAM@Z
 * Address: 0x14039E250
 */

char __fastcall _GetTempEffectValue(_skill_fld *pEffectFld, int nTempEffectType, float *fValue)
{
  char result; // al@2

  if ( pEffectFld )
  {
    if ( nTempEffectType == 43 )
    {
      *fValue = (float)pEffectFld->m_nContEffectSec[0];
      result = 1;
    }
    else
    {
      *fValue = pEffectFld->m_fTempValue[0];
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
