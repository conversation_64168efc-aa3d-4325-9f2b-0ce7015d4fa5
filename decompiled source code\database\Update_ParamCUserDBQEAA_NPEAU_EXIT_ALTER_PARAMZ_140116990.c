/*
 * Function: ?Update_Param@CUserDB@@QEAA_NPEAU_EXIT_ALTER_PARAM@@@Z
 * Address: 0x140116990
 */

char __fastcall CUserDB::Update_Param(CUserDB *this, _EXIT_ALTER_PARAM *pCon)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUserDB *v6; // [sp+30h] [bp+8h]@1
  _EXIT_ALTER_PARAM *v7; // [sp+38h] [bp+10h]@1

  v7 = pCon;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_AvatorData.dbAvator.m_dwHP = pCon->dwHP;
  v6->m_AvatorData.dbAvator.m_dwFP = pCon->dwFP;
  v6->m_AvatorData.dbAvator.m_dwSP = pCon->dwSP;
  v6->m_AvatorData.dbAvator.m_dwDP = pCon->dwDP;
  v6->m_AvatorData.dbAvator.m_dExp = pCon->dExp;
  v6->m_AvatorData.dbAvator.m_dwDalant = pCon->dwDalant;
  v6->m_AvatorData.dbAvator.m_dwGold = pCon->dwGold;
  if ( CMapOperation::IsExistStdMapID(&g_MapOper, pCon->byMapCode) )
  {
    v6->m_AvatorData.dbAvator.m_byMapCode = v7->byMapCode;
    v6->m_AvatorData.dbAvator.m_fStartPos[0] = v7->fStartPos[0];
    v6->m_AvatorData.dbAvator.m_fStartPos[1] = v7->fStartPos[1];
    v6->m_AvatorData.dbAvator.m_fStartPos[2] = v7->fStartPos[2];
  }
  return 1;
}
