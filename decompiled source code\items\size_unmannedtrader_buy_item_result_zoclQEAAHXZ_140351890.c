/*
 * Function: ?size@_unmannedtrader_buy_item_result_zocl@@QEAAHXZ
 * Address: 0x140351890
 */

signed __int64 __fastcall _unmannedtrader_buy_item_result_zocl::size(_unmannedtrader_buy_item_result_zocl *this)
{
  signed __int64 result; // rax@3

  if ( this->byRetCode && this->byRetCode != 255 )
  {
    result = 1i64;
  }
  else if ( this->byNum <= 10 )
  {
    result = 230 - 22i64 * (10 - this->byNum);
  }
  else
  {
    result = 0i64;
  }
  return result;
}
