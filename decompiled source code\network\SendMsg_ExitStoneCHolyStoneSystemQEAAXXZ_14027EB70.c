/*
 * Function: ?SendMsg_ExitStone@CHolyStoneSystem@@QEAAXXZ
 * Address: 0x14027EB70
 */

void __fastcall CHolyStoneSystem::SendMsg_ExitStone(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  unsigned int dwClientIndex; // [sp+64h] [bp-14h]@4
  CHolyStoneSystem *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  szMsg = CHolyStoneSystem::GetHolyMasterRace(v8);
  pbyType = 25;
  v6 = 2;
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 1u);
  }
}
