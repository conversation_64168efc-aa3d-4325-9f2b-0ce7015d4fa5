/*
 * Function: j_?end@?$_Hash@V?$_Hmap_traits@HPEBU_TimeItem_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA?AV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@XZ
 * Address: 0x14000ED72
 */

std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *__fastcall stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::end(stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *this, std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *result)
{
  return stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::end(
           this,
           result);
}
