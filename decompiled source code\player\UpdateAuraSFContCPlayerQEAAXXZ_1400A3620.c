/*
 * Function: ?UpdateAuraSFCont@CPlayer@@QEAAXXZ
 * Address: 0x1400A3620
 */

void __fastcall CPlayer::UpdateAuraSFCont(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@27
  __int64 v4; // [sp+0h] [bp-78h]@1
  unsigned int v5; // [sp+30h] [bp-48h]@4
  int j; // [sp+34h] [bp-44h]@4
  int k; // [sp+38h] [bp-40h]@6
  bool *v8; // [sp+40h] [bp-38h]@9
  unsigned int v9; // [sp+48h] [bp-30h]@10
  int v10; // [sp+4Ch] [bp-2Ch]@15
  int l; // [sp+50h] [bp-28h]@15
  int m; // [sp+54h] [bp-24h]@17
  bool *v13; // [sp+58h] [bp-20h]@20
  _skill_fld *pSkillFld; // [sp+60h] [bp-18h]@21
  CPlayer *v15; // [sp+80h] [bp+8h]@1

  v15 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = _sf_continous::GetSFContCurTime();
  for ( j = 0; j < 2; ++j )
  {
    for ( k = 0; k < 8; ++k )
    {
      v8 = &v15->m_SFContAura[j][k].m_bExist;
      if ( *v8 )
      {
        v9 = v5 - *((_DWORD *)v8 + 2);
        if ( v9 >= *((_WORD *)v8 + 6) )
          CCharacter::RemoveSFContEffect((CCharacter *)&v15->vfptr, j, k, 0, 1);
      }
    }
  }
  if ( CMyTimer::CountingTimer(&v15->m_tmrAuraSkill) )
  {
    v10 = 0;
    for ( l = 0; l < 2; ++l )
    {
      for ( m = 0; m < 8; ++m )
      {
        v13 = &v15->m_SFContAura[l][m].m_bExist;
        if ( *v13 )
        {
          pSkillFld = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410, *((_WORD *)v13 + 1));
          if ( pSkillFld )
          {
            if ( pSkillFld->m_nClass == 4 )
            {
              if ( CPlayer::IsChaosMode(v15) || CPlayer::IsPunished(v15, 1, 0) )
              {
                CCharacter::RemoveSFContEffect((CCharacter *)&v15->vfptr, l, m, 0, 0);
              }
              else
              {
                v3 = CPlayer::GetMaxFP(v15);
                if ( v3 >= pSkillFld->m_nNeedFP )
                {
                  if ( CPlayer::_pre_check_skill_gradelimit(v15, pSkillFld) )
                  {
                    v10 = 1;
                    CPlayer::skill_process_for_aura(v15, *((_WORD *)v13 + 1));
                  }
                  else
                  {
                    CCharacter::RemoveSFContEffect((CCharacter *)&v15->vfptr, l, m, 0, 1);
                  }
                }
                else
                {
                  CCharacter::RemoveSFContEffect((CCharacter *)&v15->vfptr, l, m, 0, 1);
                }
              }
            }
          }
        }
      }
    }
    if ( !v10 )
      CMyTimer::StopTimer(&v15->m_tmrAuraSkill);
  }
}
