/*
 * Function: ?ct_jump_to_pos@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028FD00
 */

bool __fastcall ct_jump_to_pos(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  float v4; // xmm0_4@7
  float v5; // xmm0_4@10
  float v6; // xmm0_4@11
  __int64 v7; // [sp+0h] [bp-D8h]@1
  char szTran; // [sp+38h] [bp-A0h]@7
  char pszMapCode; // [sp+78h] [bp-60h]@9
  float v10; // [sp+B0h] [bp-28h]@7
  float v11; // [sp+B4h] [bp-24h]@7
  float v12; // [sp+B8h] [bp-20h]@10
  float v13; // [sp+BCh] [bp-1Ch]@10
  float v14; // [sp+C0h] [bp-18h]@11
  float v15; // [sp+C4h] [bp-14h]@11
  unsigned __int64 v16; // [sp+C8h] [bp-10h]@4
  CPlayer *v17; // [sp+E0h] [bp+8h]@1

  v17 = pOne;
  v1 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( v17 )
  {
    if ( s_nWordCount == 4 )
    {
      W2M(s_pwszDstCheat[0], &szTran, 0x20u);
      v10 = (float)atoi(s_pwszDstCheat[3]);
      v11 = (float)atoi(s_pwszDstCheat[2]);
      v4 = (float)atoi(s_pwszDstCheat[1]);
      result = CPlayer::mgr_gotoCoordinates(v17, &szTran, v4, v11, v10);
    }
    else if ( s_nWordCount <= 4 )
    {
      result = 0;
    }
    else
    {
      W2M(s_pwszDstCheat[1], &pszMapCode, 0x20u);
      if ( !strcmp_0(s_pwszDstCheat[0], "#") )
      {
        v12 = (float)atoi(s_pwszDstCheat[4]);
        v13 = (float)atoi(s_pwszDstCheat[3]);
        v5 = (float)atoi(s_pwszDstCheat[2]);
        result = CPlayer::mgr_gotoDstCoordinates(v17, 0i64, &pszMapCode, v5, v13, v12);
      }
      else
      {
        v14 = (float)atoi(s_pwszDstCheat[4]);
        v15 = (float)atoi(s_pwszDstCheat[3]);
        v6 = (float)atoi(s_pwszDstCheat[2]);
        result = CPlayer::mgr_gotoDstCoordinates(v17, s_pwszDstCheat[0], &pszMapCode, v6, v15, v14);
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
