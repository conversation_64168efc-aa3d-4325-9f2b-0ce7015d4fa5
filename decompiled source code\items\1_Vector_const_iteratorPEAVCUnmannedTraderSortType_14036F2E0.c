/*
 * Function: ??1?$_Vector_const_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA@XZ
 * Address: 0x14036F2E0
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>::~_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &>((std::_Ranit<CUnmannedTraderSortType *,__int64,CUnmannedTraderSortType * const *,CUnmannedTraderSortType * const &> *)&v4->_Mycont);
}
