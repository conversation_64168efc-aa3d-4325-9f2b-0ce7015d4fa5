/*
 * Function: ??4?$_<PERSON>t@UMessageRange@MeterFilter@CryptoPP@@_JPEBU123@AEBU123@@std@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x140602480
 */

std::_Iterator_base *__fastcall std::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>::operator=(std::_Iterator_base *a1, std::_Iterator_base *a2)
{
  std::_Iterator_base *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  std::_Iterator_base::operator=(a1, a2);
  return v3;
}
