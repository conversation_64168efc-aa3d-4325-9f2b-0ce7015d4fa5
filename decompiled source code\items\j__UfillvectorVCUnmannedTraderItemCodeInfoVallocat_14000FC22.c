/*
 * Function: j_?_Ufill@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderItemCodeInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x14000FC22
 */

CUnmannedTraderItemCodeInfo *__fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Ufill(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, CUnmannedTraderItemCodeInfo *_Ptr, unsigned __int64 _Count, CUnmannedTraderItemCodeInfo *_Val)
{
  return std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_U<PERSON>(
           this,
           _Ptr,
           _Count,
           _<PERSON>);
}
