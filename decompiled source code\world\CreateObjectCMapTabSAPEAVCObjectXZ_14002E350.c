/*
 * Function: ?CreateObject@CMapTab@@SAPEAVCObject@@XZ
 * Address: 0x14002E350
 */

CObject *__cdecl CMapTab::CreateObject()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CMapTab *v5; // [sp+28h] [bp-20h]@4
  __int64 v6; // [sp+30h] [bp-18h]@4
  __int64 v7; // [sp+38h] [bp-10h]@5

  v0 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v6 = -2i64;
  v5 = (CMapTab *)CObject::operator new(0x398ui64, THIS_FILE_7, 21);
  if ( v5 )
  {
    CMapTab::CMapTab(v5);
    v7 = v2;
  }
  else
  {
    v7 = 0i64;
  }
  return (CObject *)v7;
}
