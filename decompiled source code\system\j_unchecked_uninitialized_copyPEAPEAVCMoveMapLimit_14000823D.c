/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@stdext@@YAPEAPEAVCMoveMapLimitRight@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitRight@@@std@@@Z
 * Address: 0x14000823D
 */

CMoveMapLimitRight **__fastcall stdext::unchecked_uninitialized_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, CMoveMapLimitRight **_Dest, std::allocator<CMoveMapLimitRight *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CMoveMapLimitRight * *,CMoveMapLimitRight * *,std::allocator<CMoveMapLimitRight *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
