/*
 * Function: ?Init@CDarkHole@@QEAAXPEAU_object_id@@@Z
 * Address: 0x140163910
 */

void __fastcall CDarkHole::Init(CDarkHole *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CDarkHole *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CGameObject::Init((CGameObject *)&v5->vfptr, pID);
  v5->m_dwOpenerSerial = 0;
  memset_0(v5->m_wszOpenerName, 0, 0x11ui64);
  memset_0(v5->m_aszOpenerName, 0, 0x11ui64);
}
