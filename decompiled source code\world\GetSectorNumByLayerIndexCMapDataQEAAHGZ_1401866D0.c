/*
 * Function: ?GetSectorNumByLayerIndex@CMapData@@QEAAHG@Z
 * Address: 0x1401866D0
 */

signed __int64 __fastcall CMapData::GetSectorNumByLayerIndex(CMapData *this, unsigned __int16 wLayerIndex)
{
  signed __int64 result; // rax@3

  if ( this->m_pMapSet && wLayerIndex <= this->m_pMapSet->m_nLayerNum )
    result = this->m_ls[wLayerIndex].m_nSecNum;
  else
    result = 0xFFFFFFFFi64;
  return result;
}
