/*
 * Function: ?dev_die@CPlayer@@QEAA_NXZ
 * Address: 0x1400BB330
 */

char __fastcall CPlayer::dev_die(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  _UNIT_DB_BASE::_LIST *v4; // rax@6
  __int64 v6; // [sp+0h] [bp-58h]@1
  char v7; // [sp+20h] [bp-38h]@5
  int v8; // [sp+28h] [bp-30h]@5
  int v9; // [sp+30h] [bp-28h]@5
  char v10; // [sp+38h] [bp-20h]@5
  CGameObjectVtbl *v11; // [sp+40h] [bp-18h]@5
  CGameObjectVtbl *v12; // [sp+48h] [bp-10h]@6
  CPlayer *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v1 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13->m_nLastBeatenPart = 0;
  if ( CPlayer::IsRidingUnit(v13) )
  {
    v4 = v13->m_pUsingUnit;
    v12 = v13->vfptr;
    v10 = 1;
    v9 = 0;
    v8 = -1;
    v7 = 0;
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD, _QWORD))v12->SetDamage)(v13, v4->dwGauge, 0i64, 0i64);
  }
  else
  {
    v3 = CPlayerDB::GetHP(&v13->m_Param);
    v11 = v13->vfptr;
    v10 = 1;
    v9 = 0;
    v8 = -1;
    v7 = 0;
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD, _QWORD))v11->SetDamage)(v13, (unsigned int)v3, 0i64, 0i64);
  }
  return 1;
}
