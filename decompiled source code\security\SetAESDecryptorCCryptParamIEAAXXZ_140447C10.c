/*
 * Function: ?SetAESDecryptor@CCryptParam@@IEAAXXZ
 * Address: 0x140447C10
 */

void __fastcall CCryptParam::SetAESDecryptor(CCryptParam *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CCryptParam *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::SimpleKeyingInterface::SetKeyWithIV(
    (CryptoPP::SimpleKeyingInterface *)&v4->m_AESDecryptor.vfptr,
    &g_key,
    0x10ui64,
    &g_iv);
}
