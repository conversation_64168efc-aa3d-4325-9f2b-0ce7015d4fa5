/*
 * Function: ?Emb_CreateNPCQuest@CPlayer@@QEAA_NPEADK@Z
 * Address: 0x1400CB890
 */

bool __fastcall CPlayer::Emb_CreateNPCQuest(CPlayer *this, char *pszEventCode, unsigned int dwNPCQuestIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@10
  char v6; // al@11
  __int64 v7; // [sp+0h] [bp-148h]@1
  unsigned int v8; // [sp+30h] [bp-118h]@7
  char v9; // [sp+34h] [bp-114h]@4
  int j; // [sp+38h] [bp-110h]@4
  void *Src; // [sp+40h] [bp-108h]@11
  char v12; // [sp+48h] [bp-100h]@13
  _base_fld *v13; // [sp+50h] [bp-F8h]@13
  unsigned int k; // [sp+58h] [bp-F0h]@14
  _happen_event_cont Dst; // [sp+68h] [bp-E0h]@21
  int l; // [sp+84h] [bp-C4h]@22
  _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY pHisData; // [sp+A0h] [bp-A8h]@31
  __int64 _Time; // [sp+118h] [bp-30h]@31
  unsigned __int64 v19; // [sp+130h] [bp-18h]@4
  CPlayer *v20; // [sp+150h] [bp+8h]@1
  char *pszEventCodea; // [sp+158h] [bp+10h]@1
  unsigned int dwQuestIndex; // [sp+160h] [bp+18h]@1

  dwQuestIndex = dwNPCQuestIndex;
  pszEventCodea = pszEventCode;
  v20 = this;
  v3 = &v7;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = (unsigned __int64)&v7 ^ _security_cookie;
  v9 = 0;
  for ( j = 0; j < 30; ++j )
  {
    if ( v20->m_NPCQuestIndexTempData.IndexData[j].dwQuestIndex == dwNPCQuestIndex )
    {
      v8 = v20->m_NPCQuestIndexTempData.IndexData[j].dwQuestHappenIndex;
      v9 = 1;
      break;
    }
  }
  if ( !v9 )
    return 0;
  v6 = CPlayerDB::GetRaceCode(&v20->m_Param);
  Src = CQuestMgr::CheckNPCQuestStartable(&v20->m_QuestMgr, pszEventCodea, v6, dwQuestIndex, v8);
  if ( !Src )
    return 0;
  v12 = 0;
  v13 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, dwQuestIndex);
  if ( v20->m_pUserDB->m_AvatorData.dbQuest.dwListCnt )
  {
    for ( k = 0; k < v20->m_pUserDB->m_AvatorData.dbQuest.dwListCnt; ++k )
    {
      if ( !strcmp_0(v20->m_pUserDB->m_AvatorData.dbQuest.m_StartHistory[k].szQuestCode, v13->m_strCode) )
      {
        if ( *(_DWORD *)&v13[1].m_strCode[4] )
          break;
        v12 = 1;
        return 0;
      }
    }
  }
  _happen_event_cont::_happen_event_cont(&Dst);
  memcpy_0(&Dst, Src, 0x18ui64);
  if ( CPlayer::Emb_StartQuest(v20, -1, &Dst) )
  {
    if ( v12 )
      goto LABEL_36;
    if ( *(_DWORD *)&v13[1].m_strCode[4] )
      return 1;
    _QUEST_DB_BASE::_START_NPC_QUEST_HISTORY::_START_NPC_QUEST_HISTORY(&pHisData);
    strcpy_0(pHisData.szQuestCode, v13->m_strCode);
    pHisData.byLevel = CPlayerDB::GetLevel(&v20->m_Param);
    _Time = 0i64;
    time_0(&_Time);
    pHisData.nEndTime = (unsigned int)(signed int)floor((double)(signed int)_Time + *(double *)&v13[1].m_strCode[8]);
    if ( CUserDB::Update_StartNPCQuestHistory(v20->m_pUserDB, v20->m_pUserDB->m_AvatorData.dbQuest.dwListCnt, &pHisData) )
LABEL_36:
      result = 1;
    else
      result = 0;
  }
  else
  {
    for ( l = 0; l < 3; ++l )
    {
      if ( !_happen_event_cont::isset(&v20->m_QuestMgr.m_pTempHappenEvent[l]) )
      {
        memcpy_0(&v20->m_QuestMgr.m_pTempHappenEvent[l], &Dst, 0x18ui64);
        break;
      }
    }
    result = 0;
  }
  return result;
}
