/*
 * Function: ?SendMsg_InformDropPos@CNuclearBomb@@QEAAXXZ
 * Address: 0x14013CB90
 */

void __fastcall CNuclearBomb::SendMsg_InformDropPos(CNuclearBomb *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  _nuclear_bomb_position_inform_zocl v4; // [sp+38h] [bp-50h]@4
  unsigned int dwClientIndex; // [sp+54h] [bp-34h]@4
  CPlayer *v6; // [sp+58h] [bp-30h]@7
  char pbyType; // [sp+64h] [bp-24h]@11
  char v8; // [sp+65h] [bp-23h]@11
  CNuclearBomb *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _nuclear_bomb_position_inform_zocl::_nuclear_bomb_position_inform_zocl(&v4);
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    v6 = &g_Player + (signed int)dwClientIndex;
    if ( v6->m_bOper && !v6->m_bCorpse && !strcmp_0(v6->m_pCurMap->m_pMapSet->m_strCode, "resources") )
    {
      v4.byRaceCode = CPlayerDB::GetRaceCode(&v9->m_pMaster->m_Param);
      v4.byUseClass = CNuclearBomb::GetMasterClass(v9);
      v4.zPos[0] = v9->m_fCurPos[0];
      v4.zPos[1] = v9->m_fCurPos[1];
      v4.zPos[2] = v9->m_fCurPos[2];
      pbyType = 60;
      v8 = 7;
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v4.byRaceCode, 0xEu);
    }
  }
}
