/*
 * Function: ??0?$DL_ObjectImplBase@V?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@UDSA@CryptoPP@@UDL_Keys_DSA@2@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@2@V?$DL_PublicKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140568160
 */

__int64 __fastcall CryptoPP::DL_ObjectImplBase<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>>::DL_ObjectImplBase<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>>(__int64 a1)
{
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DSA>::AlgorithmImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DSA>();
  CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>(
    v2 + 24,
    1i64);
  return v2;
}
