/*
 * Function: ?NotifyAllProcessEnd@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E57A0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyAllProcessEnd(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  char byType; // [sp+24h] [bp-24h]@4
  char v5; // [sp+25h] [bp-23h]@4
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  byType = 27;
  v5 = 87;
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v6->m_k1P, &byType, 0i64, 0);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v6->m_k2P, &byType, 0i64, 0);
}
