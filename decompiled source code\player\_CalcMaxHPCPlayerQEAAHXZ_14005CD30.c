/*
 * Function: ?_<PERSON>c<PERSON>axHP@CPlayer@@QEAAHXZ
 * Address: 0x14005CD30
 */

__int64 __fastcall CPlayer::_CalcMaxHP(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  float v4; // xmm0_4@4
  float v5; // xmm0_4@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  float v8; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = _MASTERY_PARAM::GetMasteryPerMast(&v9->m_pmMst, 1, 0);
  v8 = (float)v3;
  v8 = (float)v3 + (float)v9->m_nAddPointByClass[0];
  v4 = (float)CPlayerDB::GetLevel(&v9->m_Param);
  pow(v4, 2);
  v5 = v4 * v8;
  sqrt(v5);
  return (unsigned int)(signed int)ffloor((float)(v5 * 20.0) + 80.0);
}
