/*
 * Function: ?GetDefSkill@CPlayer@@UEAAH_N@Z
 * Address: 0x140060C00
 */

int __fastcall CPlayer::GetDefSkill(CPlayer *this, bool bBackAttackDamage)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@9
  __int64 v5; // [sp+0h] [bp-48h]@1
  int v6; // [sp+20h] [bp-28h]@5
  _base_fld *v7; // [sp+28h] [bp-20h]@5
  _base_fld *v8; // [sp+30h] [bp-18h]@7
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPlayer::IsRidingUnit(v9) )
  {
    v6 = 0;
    v7 = CRecordData::GetRecord(&stru_1799C86D0, v9->m_pUsingUnit->byPart[0]);
    if ( v7 )
      v6 += *(_DWORD *)&v7[5].m_strCode[12];
    v8 = CRecordData::GetRecord(&stru_1799C86D0 + 1, v9->m_pUsingUnit->byPart[1]);
    if ( v8 )
      v6 += *(_DWORD *)&v8[5].m_strCode[12];
    result = v6;
  }
  else
  {
    result = _MASTERY_PARAM::GetMasteryPerMast(&v9->m_pmMst, 1, 0);
  }
  return result;
}
