/*
 * Function: j_??$_Ptr_cat@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@PEAPEAVCLogTypeDBTask@@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@0@AEAPEAPEAVCLogTypeDBTask@@@Z
 * Address: 0x14000C8EC
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__formal, CLogTypeDBTask ***a2)
{
  return std::_Ptr_cat<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *>(
           __formal,
           a2);
}
