/*
 * Function: ?SendMsg_BillingTypeChangeInform@CPlayer@@QEAAXFJPEAU_SYSTEMTIME@@E@Z
 * Address: 0x1400E4640
 */

void __fastcall CPlayer::SendMsg_BillingTypeChangeInform(CPlayer *this, __int16 iType, int lRemainTime, _SYSTEMTIME *pstEndDate, char byReason)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  __int16 v9; // [sp+3Ah] [bp-5Eh]@4
  int v10; // [sp+3Ch] [bp-5Ch]@4
  char Dst; // [sp+40h] [bp-58h]@5
  char v12; // [sp+50h] [bp-48h]@6
  char pbyType; // [sp+74h] [bp-24h]@6
  char v14; // [sp+75h] [bp-23h]@6
  CPlayer *v15; // [sp+A0h] [bp+8h]@1

  v15 = this;
  v5 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *(_WORD *)szMsg = v15->m_pUserDB->m_BillingInfo.iType;
  v9 = iType;
  v10 = lRemainTime;
  if ( pstEndDate )
    memcpy_0(&Dst, pstEndDate, 0x10ui64);
  v12 = byReason;
  pbyType = 29;
  v14 = 3;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, szMsg, 0x19u);
}
