/*
 * Function: j_??A?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderSortType@@_K@Z
 * Address: 0x14000D265
 */

CUnmannedTraderSortType **__fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator[](std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator[](this, _Pos);
}
