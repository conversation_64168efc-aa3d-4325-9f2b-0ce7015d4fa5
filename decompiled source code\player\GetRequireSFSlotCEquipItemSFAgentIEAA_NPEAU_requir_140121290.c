/*
 * Function: ?GetRequireSFSlot@CEquipItemSFAgent@@IEAA_NPEAU_requireSlot@1@PEAU_skill_fld@@@Z
 * Address: 0x140121290
 */

char __fastcall CEquipItemSFAgent::GetRequireSFSlot(CEquipItemSFAgent *this, CEquipItemSFAgent::_requireSlot *pSlot, _skill_fld *pSkillFld)
{
  char result; // al@2

  if ( pSlot )
  {
    if ( pSkillFld )
    {
      if ( pSkillFld->m_strFixWeapon[12] == 49 )
        pSlot->m_SlotIndex[7] = 1;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
