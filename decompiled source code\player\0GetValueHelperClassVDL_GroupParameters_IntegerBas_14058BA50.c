/*
 * Function: ??0?$GetValueHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@PEBV?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@1@PEBDAEBVtype_info@@PEAXPEBVNameValuePairs@1@@Z
 * Address: 0x14058BA50
 */

_QWORD *__fastcall CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>(_QWORD *a1, __int64 a2, __int64 a3, struct type_info *a4, void *a5, __int64 a6)
{
  unsigned __int8 *v6; // rax@1
  char *v7; // rcx@1
  unsigned __int8 v8; // dl@2
  int v9; // eax@4
  const char *v10; // ST20_8@11
  __int64 v11; // rax@11
  __int64 v12; // rax@11
  __int64 v13; // rdx@11
  const char *v14; // rax@14
  char *v15; // rcx@14
  signed __int64 v16; // rax@14
  unsigned __int8 v17; // dl@15
  int v18; // eax@17
  _QWORD *v20; // [sp+40h] [bp+8h]@1
  __int64 v21; // [sp+48h] [bp+10h]@1
  struct type_info *v22; // [sp+58h] [bp+20h]@1

  v22 = a4;
  v21 = a2;
  v20 = a1;
  *a1 = a2;
  a1[1] = a3;
  a1[2] = a4;
  a1[3] = a5;
  *((_BYTE *)a1 + 32) = 0;
  *((_BYTE *)a1 + 33) = 0;
  v6 = (unsigned __int8 *)a1[1];
  v7 = (char *)("ValueNames" - (char *)v6);
  while ( 1 )
  {
    v8 = *v6;
    if ( *v6 != v7[(_QWORD)v6] )
      break;
    ++v6;
    if ( !v8 )
    {
      v9 = 0;
      goto LABEL_6;
    }
  }
  v9 = -(v8 < v7[(_QWORD)v6]) - ((*v6 < v7[(_QWORD)v6]) - 1);
LABEL_6:
  if ( !v9 )
  {
    *((_BYTE *)v20 + 33) = 1;
    *((_BYTE *)v20 + 32) = 1;
    CryptoPP::NameValuePairs::ThrowIfTypeMismatch(
      (const char *)v20[1],
      &std::basic_string<char,std::char_traits<char>,std::allocator<char>> `RTTI Type Descriptor',
      (type_info *)v20[2]);
    if ( a6 )
      (*(void (__fastcall **)(__int64, _QWORD, struct type_info *, void *))(*(_QWORD *)a6 + 8i64))(a6, v20[1], v22, a5);
    if ( (unsigned __int8)type_info::operator!=(
                            &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
                            &CryptoPP::DL_GroupParameters_IntegerBased `RTTI Type Descriptor') )
      CryptoPP::DL_GroupParameters_IntegerBased::GetVoidValue(
        (CryptoPP::DL_GroupParameters_IntegerBased *)(v21 + 80),
        (const char *)v20[1],
        v22,
        a5);
    v10 = type_info::_name_internal_method(
            &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
            (struct __type_info_node *)&__type_info_root_node);
    LODWORD(v11) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(
                     v20[3],
                     "ThisPointer:");
    LODWORD(v12) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v11, v10);
    LOBYTE(v13) = 59;
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v12, v13);
  }
  if ( *((_BYTE *)v20 + 32) || strncmp((const char *)v20[1], "ThisPointer:", 0xCui64) )
    goto LABEL_30;
  v14 = type_info::_name_internal_method(
          &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
          (struct __type_info_node *)&__type_info_root_node);
  v15 = (char *)(v20[1] + 12i64);
  v16 = v14 - v15;
  while ( 1 )
  {
    v17 = *v15;
    if ( *v15 != v15[v16] )
      break;
    ++v15;
    if ( !v17 )
    {
      v18 = 0;
      goto LABEL_19;
    }
  }
  v18 = -(v17 < (unsigned __int8)v15[v16]) - (((unsigned __int8)*v15 < (unsigned __int8)v15[v16]) - 1);
LABEL_19:
  if ( v18 )
  {
LABEL_30:
    if ( !*((_BYTE *)v20 + 32) && a6 )
      *((_BYTE *)v20 + 32) = (*(int (__fastcall **)(__int64, _QWORD, struct type_info *, void *))(*(_QWORD *)a6 + 8i64))(
                               a6,
                               v20[1],
                               v22,
                               a5);
    if ( !*((_BYTE *)v20 + 32)
      && (unsigned __int8)type_info::operator!=(
                            &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
                            &CryptoPP::DL_GroupParameters_IntegerBased `RTTI Type Descriptor') )
    {
      *((_BYTE *)v20 + 32) = CryptoPP::DL_GroupParameters_IntegerBased::GetVoidValue(
                               (CryptoPP::DL_GroupParameters_IntegerBased *)(v21 + 80),
                               (const char *)v20[1],
                               v22,
                               a5);
    }
  }
  else
  {
    CryptoPP::NameValuePairs::ThrowIfTypeMismatch(
      (const char *)v20[1],
      &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> * `RTTI Type Descriptor',
      (type_info *)v20[2]);
    *(_QWORD *)a5 = v21;
    *((_BYTE *)v20 + 32) = 1;
  }
  return v20;
}
