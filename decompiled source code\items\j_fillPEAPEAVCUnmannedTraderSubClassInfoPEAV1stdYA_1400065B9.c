/*
 * Function: j_??$fill@PEAPEAVCUnmannedTraderSubClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@0AEBQEAV1@@Z
 * Address: 0x1400065B9
 */

void __fastcall std::fill<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo *>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo *const *_Val)
{
  std::fill<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo *>(_First, _Last, _Val);
}
