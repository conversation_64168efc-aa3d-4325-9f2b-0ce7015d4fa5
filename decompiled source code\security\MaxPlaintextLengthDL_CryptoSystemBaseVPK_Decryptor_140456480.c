/*
 * Function: ?MaxPlaintextLength@?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x140456480
 */

__int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MaxPlaintextLength(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *this, unsigned __int64 ciphertextLength)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rdx@4
  __int64 *v5; // rax@6
  __int64 v6; // rax@6
  __int64 v8; // [sp+0h] [bp-58h]@1
  unsigned int v9; // [sp+20h] [bp-38h]@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v10; // [sp+28h] [bp-30h]@4
  __int64 v11; // [sp+30h] [bp-28h]@5
  __int64 *v12; // [sp+38h] [bp-20h]@6
  __int64 v13; // [sp+40h] [bp-18h]@6
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *v14; // [sp+60h] [bp+8h]@1
  unsigned __int64 v15; // [sp+68h] [bp+10h]@1

  v15 = ciphertextLength;
  v14 = this;
  v2 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *)&v14->vfptr);
  LOBYTE(v4) = 1;
  v9 = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, __int64))v10->vfptr[12].__vecDelDtor)(
         v10,
         v4);
  if ( v15 >= v9 )
  {
    LODWORD(v5) = ((int (__fastcall *)(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *))v14->vfptr[1].FixedCiphertextLength)(v14);
    v12 = v5;
    v13 = *v5;
    LODWORD(v6) = (*(int (__fastcall **)(__int64 *, unsigned __int64))(v13 + 24))(v5, v15 - v9);
    v11 = v6;
  }
  else
  {
    v11 = 0i64;
  }
  return v11;
}
