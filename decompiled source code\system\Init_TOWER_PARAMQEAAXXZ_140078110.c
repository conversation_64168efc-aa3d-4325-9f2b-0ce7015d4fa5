/*
 * Function: ?Init@_TOWER_PARAM@@QEAAXXZ
 * Address: 0x140078110
 */

void __fastcall _TOWER_PARAM::Init(_TOWER_PARAM *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _TOWER_PARAM *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 6; ++j )
    _TOWER_PARAM::_list::init(&v5->m_List[j]);
  v5->m_nCount = 0;
}
