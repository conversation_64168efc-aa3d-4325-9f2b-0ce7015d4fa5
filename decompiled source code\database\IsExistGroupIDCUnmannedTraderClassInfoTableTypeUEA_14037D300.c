/*
 * Function: ?IsExistGroupID@CUnmannedTraderClassInfoTableType@@UEAA_NEE@Z
 * Address: 0x14037D300
 */

char __fastcall CUnmannedTraderClassInfoTableType::IsExistGroupID(CUnmannedTraderClassInfoTableType *this, char byClass, char bySubClass)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@5
  CUnmannedTraderSubClassInfo **v6; // rax@10
  unsigned int v7; // eax@10
  __int64 v8; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > result; // [sp+28h] [bp-70h]@8
  bool v10; // [sp+44h] [bp-54h]@9
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > v11; // [sp+48h] [bp-50h]@9
  char v12; // [sp+60h] [bp-38h]@11
  char v13; // [sp+61h] [bp-37h]@13
  __int64 v14; // [sp+68h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v15; // [sp+70h] [bp-28h]@9
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Right; // [sp+78h] [bp-20h]@9
  int v17; // [sp+80h] [bp-18h]@10
  CUnmannedTraderClassInfoTableType *v18; // [sp+A0h] [bp+8h]@1
  char v19; // [sp+B0h] [bp+18h]@1

  v19 = bySubClass;
  v18 = this;
  v3 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  if ( (unsigned __int8)byClass == v18->m_dwID )
  {
    if ( (unsigned __int8)bySubClass == 255 )
    {
      v5 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&v18->m_vecSubClass);
    }
    else
    {
      std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::begin(
        &v18->m_vecSubClass,
        &result);
      while ( 1 )
      {
        v15 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(
                &v18->m_vecSubClass,
                &v11);
        _Right = (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)v15;
        v10 = std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator!=(
                (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&result._Mycont,
                (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v15->_Mycont);
        std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v11);
        if ( !v10 )
          break;
        v17 = (unsigned __int8)v19;
        v6 = std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator*(&result);
        v7 = CUnmannedTraderSubClassInfo::GetID(*v6);
        if ( v17 == v7 )
        {
          v12 = 1;
          std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
          return v12;
        }
        std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator++(&result);
      }
      v13 = 0;
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
      v5 = v13;
    }
  }
  else
  {
    v5 = 0;
  }
  return v5;
}
