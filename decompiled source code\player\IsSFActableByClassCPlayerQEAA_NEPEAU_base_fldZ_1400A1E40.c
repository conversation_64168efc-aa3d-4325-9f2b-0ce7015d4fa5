/*
 * Function: ?IsSFActableByClass@CPlayer@@QEAA_NEPEAU_base_fld@@@Z
 * Address: 0x1400A1E40
 */

char __fastcall CPlayer::IsSFActableByClass(CPlayer *this, char byEffectCode, _base_fld *pSFFld)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  int v7; // [sp+20h] [bp-38h]@8
  int nClassCode; // [sp+38h] [bp-20h]@8
  int v9; // [sp+3Ch] [bp-1Ch]@8
  int j; // [sp+44h] [bp-14h]@16
  CPlayer *v11; // [sp+60h] [bp+8h]@1
  char v12; // [sp+68h] [bp+10h]@1
  _base_fld *pSFFlda; // [sp+70h] [bp+18h]@1

  pSFFlda = pSFFld;
  v12 = byEffectCode;
  v11 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( byEffectCode == 2 )
    return 1;
  if ( v11->m_bFreeSFByClass )
    return 1;
  v7 = 1;
  nClassCode = v11->m_Param.m_pClassData->m_nClass;
  v9 = -1;
  if ( v11->m_Param.m_pClassHistory[0] )
  {
    if ( v11->m_Param.m_pClassHistory[0]->m_nClass != v11->m_Param.m_pClassData->m_nClass )
    {
      v9 = v11->m_Param.m_pClassHistory[0]->m_nClass;
      v7 = 2;
    }
  }
  else if ( v11->m_Param.m_pClassData->m_nClass == 3 && !byEffectCode && *(_DWORD *)&pSFFld[4].m_strCode[60] > 0 )
  {
    return 0;
  }
  for ( j = 0; j < v7; ++j )
  {
    if ( check_sf_class(*(&nClassCode + j), v12, pSFFlda) )
      return 1;
  }
  return 0;
}
