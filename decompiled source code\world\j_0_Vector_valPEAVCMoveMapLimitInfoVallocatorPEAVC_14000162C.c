/*
 * Function: j_??0?$_Vector_val@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@IEAA@V?$allocator@PEAVCMoveMapLimitInfo@@@1@@Z
 * Address: 0x14000162C
 */

void __fastcall std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, std::allocator<CMoveMapLimitInfo *> _Al)
{
  std::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_val<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    this,
    _Al);
}
