/*
 * Function: ?SendMsg_NoticeNextQuest@CHolyStoneSystem@@QEAAXHE@Z
 * Address: 0x140280150
 */

void __fastcall CHolyStoneSystem::SendMsg_NoticeNextQuest(CHolyStoneSystem *this, int n, char byStoneMapMoveInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  DWORD v6; // [sp+30h] [bp-58h]@5
  unsigned int v7; // [sp+34h] [bp-54h]@5
  __int16 v8; // [sp+38h] [bp-50h]@5
  char szMsg[2]; // [sp+44h] [bp-44h]@5
  char v10; // [sp+46h] [bp-42h]@5
  char pbyType; // [sp+64h] [bp-24h]@5
  char v12; // [sp+65h] [bp-23h]@5
  unsigned int dwClientIndex; // [sp+74h] [bp-14h]@6
  CHolyStoneSystem *v14; // [sp+90h] [bp+8h]@1
  int v15; // [sp+98h] [bp+10h]@1
  char v16; // [sp+A0h] [bp+18h]@1

  v16 = byStoneMapMoveInfo;
  v15 = n;
  v14 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v14->m_dwNextStartTime )
  {
    v6 = timeGetTime();
    v7 = v14->m_dwNextStartTime - v6;
    v8 = v7 / 0x3E8;
    *(_WORD *)szMsg = v7 / 0x3E8;
    v10 = v16;
    pbyType = 25;
    v12 = 16;
    if ( v15 == -1 )
    {
      for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
      {
        if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
        {
          if ( CPlayer::GetLevel(&g_Player + (signed int)dwClientIndex) < 25 )
          {
            *(&g_Player.m_byStoneMapMoveInfo + 50856 * (signed int)dwClientIndex) = 0;
          }
          else
          {
            *(&g_Player.m_byStoneMapMoveInfo + 50856 * (signed int)dwClientIndex) = v16;
            CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 3u);
          }
        }
      }
    }
    else
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v15, &pbyType, szMsg, 3u);
    }
  }
}
