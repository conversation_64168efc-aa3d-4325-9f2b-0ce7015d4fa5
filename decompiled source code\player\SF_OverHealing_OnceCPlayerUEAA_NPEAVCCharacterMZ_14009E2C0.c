/*
 * Function: ?SF_OverHealing_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009E2C0
 */

char __fastcall CPlayer::SF_OverHealing_Once(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float v5; // xmm0_4@8
  __int64 v6; // r8@8
  int v7; // eax@8
  char result; // al@9
  __int64 v9; // [sp+0h] [bp-48h]@1
  char v10; // [sp+20h] [bp-28h]@4
  int v11; // [sp+24h] [bp-24h]@8
  int v12; // [sp+28h] [bp-20h]@8
  int v13; // [sp+2Ch] [bp-1Ch]@8
  float v14; // [sp+30h] [bp-18h]@8
  CGameObjectVtbl *v15; // [sp+38h] [bp-10h]@8
  CCharacter *v16; // [sp+58h] [bp+10h]@1

  v16 = pDstObj;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = 0;
  if ( !pDstObj->m_ObjID.m_byID && !pDstObj->m_bCorpse )
    v10 = 1;
  if ( v10 )
  {
    v11 = ((int (__fastcall *)(CCharacter *))pDstObj->vfptr->GetHP)(pDstObj);
    v12 = ((int (__fastcall *)(CCharacter *))v16->vfptr->GetMaxHP)(v16);
    v5 = (float)v12 * fEffectValue;
    v14 = (float)v12 * fEffectValue;
    _effect_parameter::GetEff_Rate(&v16->m_EP, 18);
    v13 = (signed int)ffloor(v14 * v5);
    v15 = v16->vfptr;
    LOBYTE(v6) = 1;
    ((void (__fastcall *)(CCharacter *, _QWORD, __int64))v15->SetHP)(v16, (unsigned int)(v13 + v11), v6);
    v7 = ((int (__fastcall *)(CCharacter *))v16->vfptr->GetHP)(v16);
    if ( v11 == v7 )
    {
      result = 0;
    }
    else
    {
      (*(void (__fastcall **)(CCharacter *))&v16->vfptr->gap8[72])(v16);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
