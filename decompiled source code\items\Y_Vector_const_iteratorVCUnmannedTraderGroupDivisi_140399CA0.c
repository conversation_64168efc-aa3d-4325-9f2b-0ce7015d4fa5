/*
 * Function: ??Y?$_Vector_const_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x140399CA0
 */

std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *__fastcall std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator+=(std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, __int64 _Off)
{
  this->_Myptr += _Off;
  return this;
}
