/*
 * Function: luaK_exp2RK
 * Address: 0x140548310
 */

int __fastcall luaK_exp2RK(__int64 a1, __int64 a2)
{
  __int64 v2; // rbx@1
  __int64 v3; // rdi@1
  __int64 v4; // rdx@1
  signed int v5; // er11@1
  int result; // eax@5
  signed int v7; // eax@6
  int v8; // eax@10
  __int128 v9; // xmm1@12
  int v10; // eax@12
  int v11; // eax@13

  v2 = a2;
  v3 = a1;
  luaK_exp2val(a1, a2);
  v5 = *(_DWORD *)v2;
  if ( *(_DWORD *)v2 <= 0 )
    return luaK_exp2anyreg(v3, v2);
  if ( v5 <= 3 )
  {
LABEL_8:
    if ( *(_DWORD *)(v3 + 64) > 255 )
      return luaK_exp2anyreg(v3, v2);
    if ( v5 == 1 )
    {
      v8 = sub_1405478B0(v3);
      *(_DWORD *)v2 = 4;
      *(_DWORD *)(v2 + 8) = v8;
      result = v8 | 0x100;
    }
    else if ( v5 == 5 )
    {
      v9 = *(_QWORD *)(v2 + 8);
      v10 = luaK_numberK(v3, v4);
      *(_DWORD *)v2 = 4;
      *(_DWORD *)(v2 + 8) = v10;
      result = v10 | 0x100;
    }
    else
    {
      v11 = sub_140547880(v3, v5 == 2);
      *(_DWORD *)v2 = 4;
      *(_DWORD *)(v2 + 8) = v11;
      result = v11 | 0x100;
    }
    return result;
  }
  if ( v5 != 4 )
  {
    if ( v5 != 5 )
      return luaK_exp2anyreg(v3, v2);
    goto LABEL_8;
  }
  v7 = *(_DWORD *)(v2 + 8);
  if ( v7 > 255 )
    return luaK_exp2anyreg(v3, v2);
  return v7 | 0x100;
}
