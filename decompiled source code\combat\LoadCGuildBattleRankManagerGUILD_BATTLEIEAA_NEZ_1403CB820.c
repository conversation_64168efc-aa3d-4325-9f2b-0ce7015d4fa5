/*
 * Function: ?Load@CGuildBattleRankManager@GUILD_BATTLE@@IEAA_NE@Z
 * Address: 0x1403CB820
 */

char __usercall GUILD_BATTLE::CGuildBattleRankManager::Load@<al>(GUILD_BATTLE::CGuildBattleRankManager *this@<rcx>, char by<PERSON><PERSON>@<dl>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp-20h] [bp-5668h]@1
  unsigned __int16 v8; // [sp+0h] [bp-5648h]@6
  unsigned __int16 v9; // [sp+4h] [bp-5644h]@6
  int *v10; // [sp+8h] [bp-5640h]@6
  unsigned __int16 Dst; // [sp+20h] [bp-5628h]@6
  int v12; // [sp+24h] [bp-5624h]@12
  char v13; // [sp+28h] [bp-5620h]@12
  char Source[19]; // [sp+29h] [bp-561Fh]@12
  int v15; // [sp+3Ch] [bp-560Ch]@12
  int v16; // [sp+40h] [bp-5608h]@12
  int v17; // [sp+44h] [bp-5604h]@12
  int v18; // [sp+48h] [bp-5600h]@12
  int v19[5494]; // [sp+4Ch] [bp-55FCh]@12
  unsigned __int16 j; // [sp+5624h] [bp-24h]@8
  unsigned __int16 k; // [sp+5628h] [bp-20h]@13
  unsigned __int64 v22; // [sp+5638h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleRankManager *v23; // [sp+5650h] [bp+8h]@1
  char v24; // [sp+5658h] [bp+10h]@1

  v24 = byRace;
  v23 = this;
  v3 = alloca(a3);
  v4 = &v7;
  for ( i = 5528i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v22 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    v8 = 0;
    v9 = 0;
    v10 = 0i64;
    memset_0(&Dst, 0, 0x55F4ui64);
    if ( CRFWorldDatabase::SelectGuildBattleRankList(pkDB, v24, (_worlddb_guild_battle_rank_list *)&Dst) )
    {
      GUILD_BATTLE::CGuildBattleRankManager::Clear(v23, v24);
      ++v23->m_dwVer[(unsigned __int8)v24];
      for ( j = 0; j < (signed int)Dst; ++j )
      {
        if ( (signed int)v8 >= 10 )
        {
          v23->m_ppkList[(unsigned __int8)v24][v9].dwCurVer = v23->m_dwVer[(unsigned __int8)v24];
          v23->m_ppkList[(unsigned __int8)v24][v9].byCnt = 10;
          v8 = 0;
          ++v9;
        }
        v10 = &v23->m_ppkList[(unsigned __int8)v24][v9].list[v8].nRank;
        *v10 = *(&v12 + 11 * j);
        *((_BYTE *)v10 + 4) = *(&v13 + 44 * j);
        strcpy_0((char *)v10 + 5, &Source[44 * j]);
        *(int *)((char *)v10 + 22) = *(&v15 + 11 * j);
        *(int *)((char *)v10 + 26) = *(&v16 + 11 * j);
        *(int *)((char *)v10 + 30) = *(&v17 + 11 * j);
        *(int *)((char *)v10 + 34) = *(&v18 + 11 * j);
        v23->m_dwGuildSerial[(unsigned __int8)v24][v9][v8++] = v19[11 * (unsigned __int64)j];
      }
      v23->m_ppkList[(unsigned __int8)v24][v9].byCnt = v8;
      for ( k = 0; k <= (signed int)v9; ++k )
      {
        v23->m_ppkList[(unsigned __int8)v24][k].dwCurVer = v23->m_dwVer[(unsigned __int8)v24];
        v23->m_ppkList[(unsigned __int8)v24][k].byMaxPage = v9 + 1;
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
