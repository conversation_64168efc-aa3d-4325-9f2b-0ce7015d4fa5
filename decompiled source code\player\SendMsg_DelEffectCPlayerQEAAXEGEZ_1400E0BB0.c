/*
 * Function: ?SendMsg_DelEffect@CPlayer@@QEAAXEGE@Z
 * Address: 0x1400E0BB0
 */

void __fastcall CPlayer::SendMsg_DelEffect(CPlayer *this, char byEffectCode, unsigned __int16 wEffectIndex, char byLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  unsigned __int16 v7; // [sp+30h] [bp-58h]@4
  char szMsg[2]; // [sp+44h] [bp-44h]@4
  unsigned int v9; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v11; // [sp+65h] [bp-23h]@4
  CPlayer *v12; // [sp+90h] [bp+8h]@1
  char v13; // [sp+A8h] [bp+20h]@1

  v13 = byLv;
  v12 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = CCharacter::CalcEffectBit((CCharacter *)&v12->vfptr, (unsigned __int8)byEffectCode, wEffectIndex);
  *(_WORD *)szMsg = v7;
  v9 = v12->m_dwObjSerial;
  pbyType = 17;
  v11 = 11;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 6u);
  CPlayer::SendData_PartyMemberEffect(v12, 1, v7, v13);
}
