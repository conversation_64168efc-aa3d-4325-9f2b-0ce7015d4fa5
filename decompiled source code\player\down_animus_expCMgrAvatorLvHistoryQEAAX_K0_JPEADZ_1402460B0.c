/*
 * Function: ?down_animus_exp@CMgrAvatorLvHistory@@QEAAX_K0_JPEAD@Z
 * Address: 0x1402460B0
 */

void __fastcall CMgrAvatorLvHistory::down_animus_exp(CMgrAvatorLvHistory *this, unsigned __int64 dw64OldExp, unsigned __int64 dw64NewExp, __int64 i64Alter, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  __int64 v8; // [sp+20h] [bp-28h]@4
  char *v9; // [sp+28h] [bp-20h]@4
  char *v10; // [sp+30h] [bp-18h]@4
  CMgrAvatorLvHistory *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v5 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = v11->m_szCurTime;
  v9 = v11->m_szCurDate;
  v8 = i64Alter;
  sprintf(sData_0, "ANIMUS EXP DOWN %I64u -> %I64u : Alter(%I64d) [%s %s]\r\n\r\n", dw64OldExp, dw64NewExp);
  CMgrAvatorLvHistory::WriteFile(v11, pszFileName, sData_0);
}
