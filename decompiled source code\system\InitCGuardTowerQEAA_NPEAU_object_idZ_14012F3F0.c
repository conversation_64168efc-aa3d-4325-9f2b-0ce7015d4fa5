/*
 * Function: ?Init@CGuardTower@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x14012F3F0
 */

char __fastcall CGuardTower::Init(CGuardTower *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CGuardTower *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v6->vfptr, pID);
  v6->m_dwLastDestroyTime = 0;
  v6->m_bSystemStruct = 0;
  v6->m_nHP = 0;
  v6->m_pMasterTwr = 0i64;
  v6->m_dwMasterSerial = 0;
  v6->m_byRaceCode = -1;
  v6->m_pItem = 0i64;
  v6->m_wItemSerial = -1;
  v6->m_bSystemStruct = 0;
  v6->m_nIniIndex = -1;
  v6->m_dwStartMakeTime = 0;
  v6->m_bComplete = 0;
  v6->m_bQuick = 0;
  v6->m_pTarget = 0i64;
  v6->m_pMasterSetTarget = 0i64;
  return 1;
}
