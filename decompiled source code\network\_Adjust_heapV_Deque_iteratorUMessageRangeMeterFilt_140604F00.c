/*
 * Function: ??$_Adjust_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@_JUMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@_J1UMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x140604F00
 */

int __fastcall std::_Adjust_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(__int64 a1, __int64 a2, __int64 a3, const void *a4)
{
  __int64 v4; // rax@3
  __int64 v5; // rax@3
  bool v6; // ST30_1@3
  const void *v7; // rax@5
  char *v8; // rax@5
  const void *v9; // rax@7
  char *v10; // rax@7
  __int64 v12; // [sp+20h] [bp-1E8h]@1
  __int64 i; // [sp+28h] [bp-1E0h]@1
  char v14; // [sp+38h] [bp-1D0h]@3
  char v15; // [sp+58h] [bp-1B0h]@3
  char v16; // [sp+78h] [bp-190h]@5
  char v17; // [sp+98h] [bp-170h]@5
  char v18; // [sp+B8h] [bp-150h]@7
  char v19; // [sp+D8h] [bp-130h]@7
  char v20; // [sp+F8h] [bp-110h]@8
  char *v21; // [sp+118h] [bp-F0h]@8
  char v22; // [sp+120h] [bp-E8h]@8
  char v23; // [sp+140h] [bp-C8h]@7
  char v24; // [sp+158h] [bp-B0h]@5
  __int64 v25; // [sp+170h] [bp-98h]@1
  __int64 v26; // [sp+178h] [bp-90h]@3
  __int64 v27; // [sp+180h] [bp-88h]@3
  __int64 v28; // [sp+188h] [bp-80h]@3
  __int64 v29; // [sp+190h] [bp-78h]@3
  __int64 v30; // [sp+198h] [bp-70h]@3
  __int64 v31; // [sp+1A0h] [bp-68h]@5
  __int64 v32; // [sp+1A8h] [bp-60h]@5
  __int64 v33; // [sp+1B0h] [bp-58h]@5
  __int64 v34; // [sp+1B8h] [bp-50h]@5
  __int64 v35; // [sp+1C0h] [bp-48h]@7
  __int64 v36; // [sp+1C8h] [bp-40h]@7
  __int64 v37; // [sp+1D0h] [bp-38h]@7
  __int64 v38; // [sp+1D8h] [bp-30h]@7
  __int64 v39; // [sp+1E0h] [bp-28h]@8
  __int64 v40; // [sp+210h] [bp+8h]@1
  __int64 v41; // [sp+218h] [bp+10h]@1
  __int64 v42; // [sp+220h] [bp+18h]@1
  const void *v43; // [sp+228h] [bp+20h]@1

  v43 = a4;
  v42 = a3;
  v41 = a2;
  v40 = a1;
  v25 = -2i64;
  v12 = a2;
  for ( i = 2 * a2 + 2; i < v42; i = 2 * i + 2 )
  {
    v26 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v40,
            (__int64)&v15,
            i - 1);
    v27 = v26;
    LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    v28 = v4;
    v29 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v40,
            (__int64)&v14,
            i);
    v30 = v29;
    LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    v6 = CryptoPP::MeterFilter::MessageRange::operator<(v5, v28);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    if ( v6 )
      --i;
    v31 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v40,
            (__int64)&v17,
            i);
    v32 = v31;
    LODWORD(v7) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(&v24, v7, 0x18ui64);
    v33 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v40,
            (__int64)&v16,
            v41);
    v34 = v33;
    LODWORD(v8) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(v8, &v24, 0x18ui64);
    v41 = i;
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  }
  if ( i == v42 )
  {
    v35 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v40,
            (__int64)&v19,
            v42 - 1);
    v36 = v35;
    LODWORD(v9) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(&v23, v9, 0x18ui64);
    v37 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
            v40,
            (__int64)&v18,
            v41);
    v38 = v37;
    LODWORD(v10) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    qmemcpy(v10, &v23, 0x18ui64);
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    v41 = v42 - 1;
  }
  v21 = &v20;
  qmemcpy(&v22, v43, 0x18ui64);
  v39 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v20);
  std::_Push_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(
    v39,
    v41,
    v12,
    &v22);
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
