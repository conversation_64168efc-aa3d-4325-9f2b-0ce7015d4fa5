/*
 * Function: ?CalPvpTempCash@CPlayer@@QEAAXPEAV1@E@Z
 * Address: 0x14005AD50
 */

void __fastcall CPlayer::CalPvpTempCash(CPlayer *this, CPlayer *pDier, char by<PERSON><PERSON>rObjID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@5
  int v6; // eax@7
  int v7; // eax@10
  int v8; // eax@12
  int v9; // eax@17
  double v10; // xmm0_8@19
  char v11; // al@20
  double v12; // xmm0_8@21
  int v13; // eax@23
  char v14; // al@23
  char v15; // al@26
  int v16; // eax@26
  double v17; // xmm0_8@28
  char v18; // al@29
  double v19; // xmm0_8@32
  char v20; // al@34
  char v21; // al@34
  unsigned int v22; // eax@37
  __int64 v23; // [sp+0h] [bp-E8h]@1
  char v24; // [sp+30h] [bp-B8h]@17
  char v25; // [sp+31h] [bp-B7h]@17
  double v26; // [sp+38h] [bp-B0h]@17
  double v27; // [sp+40h] [bp-A8h]@17
  int v28; // [sp+48h] [bp-A0h]@17
  long double v29; // [sp+50h] [bp-98h]@17
  int v30; // [sp+58h] [bp-90h]@5
  CGameObjectVtbl *v31; // [sp+60h] [bp-88h]@5
  int v32; // [sp+68h] [bp-80h]@7
  CGameObjectVtbl *v33; // [sp+70h] [bp-78h]@7
  int v34; // [sp+78h] [bp-70h]@10
  CGameObjectVtbl *v35; // [sp+80h] [bp-68h]@10
  int v36; // [sp+88h] [bp-60h]@12
  CGameObjectVtbl *v37; // [sp+90h] [bp-58h]@12
  char *pSrcClass; // [sp+98h] [bp-50h]@17
  CGameObjectVtbl *v39; // [sp+A0h] [bp-48h]@17
  int nSrcLv; // [sp+A8h] [bp-40h]@17
  CGameObjectVtbl *v41; // [sp+B0h] [bp-38h]@17
  double v42; // [sp+B8h] [bp-30h]@21
  double v43; // [sp+C0h] [bp-28h]@26
  bool v44; // [sp+C8h] [bp-20h]@29
  double v45; // [sp+D0h] [bp-18h]@32
  CPlayer *pKiller; // [sp+F0h] [bp+8h]@1
  CPlayer *pDiera; // [sp+F8h] [bp+10h]@1

  pDiera = pDier;
  pKiller = this;
  v3 = &v23;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !CPlayer::IsChaosMode(pKiller)
    || (v30 = ((int (__fastcall *)(_QWORD))pKiller->vfptr->GetObjRace)(pKiller),
        v31 = pDiera->vfptr,
        v5 = ((int (__fastcall *)(CPlayer *))v31->GetObjRace)(pDiera),
        v30 != v5) )
  {
    if ( !CPlayer::IsChaosMode(pDiera)
      || (v32 = ((int (__fastcall *)(CPlayer *))pKiller->vfptr->GetObjRace)(pKiller),
          v33 = pDiera->vfptr,
          v6 = ((int (__fastcall *)(CPlayer *))v33->GetObjRace)(pDiera),
          v32 != v6) )
    {
      if ( !CPlayer::IsPunished(pDiera, 1, 0)
        || (v34 = ((int (__fastcall *)(CPlayer *))pKiller->vfptr->GetObjRace)(pKiller),
            v35 = pDiera->vfptr,
            v7 = ((int (__fastcall *)(CPlayer *))v35->GetObjRace)(pDiera),
            v34 != v7) )
      {
        if ( !CPlayer::IsPunished(pKiller, 1, 0)
          || (v36 = ((int (__fastcall *)(CPlayer *))pKiller->vfptr->GetObjRace)(pKiller),
              v37 = pDiera->vfptr,
              v8 = ((int (__fastcall *)(CPlayer *))v37->GetObjRace)(pDiera),
              v36 != v8) )
        {
          if ( !pDiera->m_bInGuildBattle && !pKiller->m_bInGuildBattle )
          {
            v24 = 0;
            v25 = 0;
            v26 = 0.0;
            v27 = 0.0;
            v28 = 0;
            CPvpOrderView::GetPvpTempCash(&pDiera->m_kPvpOrderView);
            v29 = 0.0;
            pSrcClass = pKiller->m_Param.m_pClassData->m_strCode;
            v39 = pKiller->vfptr;
            nSrcLv = ((int (__fastcall *)(CPlayer *))v39->GetLevel)(pKiller);
            v41 = pDiera->vfptr;
            v9 = ((int (__fastcall *)(CPlayer *))v41->GetLevel)(pDiera);
            CPvpCashPoint::CalPvpCashPoint(&pKiller->m_kPvpCashPoint, v9, nSrcLv, pSrcClass, 1);
            v26 = 0.0;
            if ( !CPvpCashPoint::CheckPvpLoseCondition(&pDiera->m_kPvpCashPoint, pKiller, pDiera) )
              v26 = 0.0;
            v10 = v26;
            if ( v26 != 0.0 )
            {
              v11 = CPlayerDB::GetLevel(&pDiera->m_Param);
              v28 = CPvpCashPoint::GetMinTempPoint(&pDiera->m_kPvpCashPoint, v11);
              CPvpOrderView::GetPvpTempCash(&pDiera->m_kPvpOrderView);
              if ( (double)v28 <= v10 - v26 )
              {
                v10 = v26 * -1.0;
                v27 = v26 * -1.0;
              }
              else
              {
                v12 = (double)v28;
                v42 = (double)v28;
                CPvpOrderView::GetPvpTempCash(&pDiera->m_kPvpOrderView);
                v10 = v42 - v12;
                v27 = v10;
              }
              CPvpOrderView::Update_PvpTempCash(&pDiera->m_kPvpOrderView, pDiera->m_ObjID.m_wIndex, v27);
              v13 = CPvpCashPoint::GetContPvpLose(&pDiera->m_kPvpCashPoint);
              CPvpCashPoint::SetContPvpLose(&pDiera->m_kPvpCashPoint, v13 + 1);
              v14 = CPvpCashPoint::GetContPvpLose(&pDiera->m_kPvpCashPoint);
              CPvpOrderView::Update_ContLoseCash(&pDiera->m_kPvpOrderView, v14);
              v25 = 1;
            }
            CPvpCashPoint::SetContPvpHave(&pDiera->m_kPvpCashPoint, 0);
            CPvpOrderView::Update_ContHaveCash(&pDiera->m_kPvpOrderView, 0);
            if ( !CPvpCashPoint::CheckPvpHaveCondition(&pKiller->m_kPvpCashPoint, pKiller, pDiera, v29) )
            {
              v10 = 0.0;
              v26 = 0.0;
            }
            CPvpOrderView::GetPvpCash(&pDiera->m_kPvpOrderView);
            v43 = v10;
            v15 = CPlayerDB::GetLevel(&pDiera->m_Param);
            v16 = CPvpCashPoint::GetMinTempPoint(&pDiera->m_kPvpCashPoint, v15);
            if ( (double)(10 * v16) > v43 )
              v26 = 0.0;
            v17 = v26;
            if ( v26 != 0.0 )
            {
              v44 = CPlayer::IsApplyPcbangPrimium(pKiller);
              v18 = CPlayerDB::GetLevel(&pKiller->m_Param);
              v28 = CPvpCashPoint::GetMaxTempPoint(&pKiller->m_kPvpCashPoint, v18, v44);
              if ( CPlayer::IsApplyPcbangPrimium(pKiller) )
              {
                v17 = v26 * PCBANG_PRIMIUM_FAVOR::PVP_RATE;
                v26 = v26 * PCBANG_PRIMIUM_FAVOR::PVP_RATE;
              }
              CPvpOrderView::GetPvpTempCash(&pKiller->m_kPvpOrderView);
              if ( v26 + v17 <= (double)v28 )
              {
                v27 = v26;
              }
              else
              {
                v19 = (double)v28;
                v45 = (double)v28;
                CPvpOrderView::GetPvpTempCash(&pKiller->m_kPvpOrderView);
                v27 = v45 - v19;
              }
              CPvpOrderView::Update_PvpTempCash(&pKiller->m_kPvpOrderView, pKiller->m_ObjID.m_wIndex, v27);
              v20 = CPvpCashPoint::GetContPvpHave(&pKiller->m_kPvpCashPoint);
              CPvpCashPoint::SetContPvpHave(&pKiller->m_kPvpCashPoint, v20 + 1);
              v21 = CPvpCashPoint::GetContPvpHave(&pKiller->m_kPvpCashPoint);
              CPvpOrderView::Update_ContHaveCash(&pKiller->m_kPvpOrderView, v21);
              v24 = 1;
            }
            CPvpCashPoint::SetContPvpLose(&pKiller->m_kPvpCashPoint, 0);
            CPvpOrderView::Update_ContLoseCash(&pKiller->m_kPvpOrderView, 0);
            if ( v24 || v25 )
            {
              v22 = CPlayerDB::GetCharSerial(&pKiller->m_Param);
              if ( CPvpCashPoint::SetKillerList(&pDiera->m_kPvpCashPoint, v22) )
                CPvpCashPoint::UpdateKillerList(
                  &pDiera->m_kPvpCashPoint,
                  &pDiera->m_pUserDB->m_AvatorData.dbPvpOrderView);
            }
          }
        }
      }
    }
  }
}
