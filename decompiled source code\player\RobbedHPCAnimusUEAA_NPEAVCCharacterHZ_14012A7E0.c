/*
 * Function: ?<PERSON><PERSON><PERSON>@CAnimus@@UEAA_NPEAVCCharacter@@H@Z
 * Address: 0x14012A7E0
 */

char __fastcall CAnimus::RobbedHP(CAnimus *this, CCharacter *pDst, int nDecHP)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v6; // eax@6
  __int64 v7; // [sp+0h] [bp-58h]@1
  char v8; // [sp+20h] [bp-38h]@6
  int v9; // [sp+28h] [bp-30h]@6
  int v10; // [sp+30h] [bp-28h]@6
  char v11; // [sp+38h] [bp-20h]@6
  CGameObjectVtbl *v12; // [sp+40h] [bp-18h]@6
  CAnimus *v13; // [sp+60h] [bp+8h]@1
  CCharacter *pkPerform; // [sp+68h] [bp+10h]@1
  int v15; // [sp+70h] [bp+18h]@1

  v15 = nDecHP;
  pkPerform = pDst;
  v13 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nDecHP < v13->m_nHP )
  {
    v6 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetLevel)(pDst);
    v12 = v13->vfptr;
    v11 = 1;
    v10 = 0;
    v9 = -1;
    v8 = 0;
    ((void (__fastcall *)(CAnimus *, _QWORD, CCharacter *, _QWORD))v12->SetDamage)(
      v13,
      (unsigned int)v15,
      pkPerform,
      (unsigned int)v6);
    CCharacter::SendMsg_RobedHP((CCharacter *)&v13->vfptr, pkPerform, v15);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
