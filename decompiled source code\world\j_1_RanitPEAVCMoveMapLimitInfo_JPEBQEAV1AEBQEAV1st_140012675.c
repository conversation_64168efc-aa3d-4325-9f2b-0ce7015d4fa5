/*
 * Function: j_??1?$_Ranit@PEAVCMoveMapLimitInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140012675
 */

void __fastcall std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>::~_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>(std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &> *this)
{
  std::_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>::~_Ranit<CMoveMapLimitInfo *,__int64,CMoveMapLimitInfo * const *,CMoveMapLimitInfo * const &>(this);
}
