/*
 * Function: j_??$_Uninit_fill_n@PEAH_KHV?$allocator@H@std@@@std@@YAXPEAH_KAEBHAEAV?$allocator@H@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400081BB
 */

void __fastcall std::_Uninit_fill_n<int *,unsigned __int64,int,std::allocator<int>>(int *_First, unsigned __int64 _Count, const int *_Val, std::allocator<int> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<int *,unsigned __int64,int,std::allocator<int>>(_First, _Count, _Val, __formal, a5, a6);
}
