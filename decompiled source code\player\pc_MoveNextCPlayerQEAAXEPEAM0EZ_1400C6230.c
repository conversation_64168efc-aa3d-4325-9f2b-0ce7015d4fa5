/*
 * Function: ?pc_MoveNext@CPlayer@@QEAAXEPEAM0E@Z
 * Address: 0x1400C6230
 */

void __fastcall CPlayer::pc_MoveNext(CPlayer *this, char byMoveType, float *pfCur, float *pfTar, char byDirect)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  float v7; // xmm0_4@25
  float v8; // xmm0_4@26
  int v9; // eax@28
  int v10; // eax@42
  float v11; // xmm0_4@51
  float v12; // xmm0_4@51
  float v13; // xmm0_4@52
  float v14; // xmm0_4@53
  bool v15; // al@64
  __int64 v16; // [sp+0h] [bp-48h]@1
  char v17; // [sp+20h] [bp-28h]@4
  int nSP; // [sp+24h] [bp-24h]@46
  bool v19; // [sp+28h] [bp-20h]@49
  int v20; // [sp+2Ch] [bp-1Ch]@28
  int v21; // [sp+30h] [bp-18h]@42
  float v22; // [sp+34h] [bp-14h]@51
  float v23; // [sp+38h] [bp-10h]@52
  CPlayer *v24; // [sp+50h] [bp+8h]@1
  char v25; // [sp+58h] [bp+10h]@1
  float *fPos; // [sp+60h] [bp+18h]@1
  float *Src; // [sp+68h] [bp+20h]@1

  Src = pfTar;
  fPos = pfCur;
  v25 = byMoveType;
  v24 = this;
  v5 = &v16;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = 0;
  if ( v24->m_pmTrd.bDTradeMode )
  {
    v17 = 7;
  }
  else if ( v24->m_bCorpse )
  {
    v17 = 8;
  }
  else if ( CPlayer::IsSiegeMode(v24) || CPlayer::IsActingSiegeMode(v24) )
  {
    v17 = 12;
  }
  else if ( v24->m_byStandType == 1 )
  {
    v17 = 10;
  }
  else if ( v24->m_bMapLoading )
  {
    v17 = 1;
  }
  else if ( _effect_parameter::GetEff_State(&v24->m_EP, 6) )
  {
    v17 = 2;
  }
  else if ( _effect_parameter::GetEff_State(&v24->m_EP, 7) && v25 == 1 )
  {
    v17 = 3;
  }
  else if ( _effect_parameter::GetEff_State(&v24->m_EP, 20) )
  {
    v17 = 13;
  }
  else if ( CMapData::IsMapIn(v24->m_pCurMap, fPos) )
  {
    if ( v24->m_byUserDgr
      || (v7 = v24->m_fCurPos[0] - *fPos, abs(v7), v7 <= 100.0)
      && (v8 = v24->m_fCurPos[2] - fPos[2], abs(v8), v8 <= 100.0) )
    {
      v20 = v24->m_byPosRaceTown;
      v9 = CPlayerDB::GetRaceCode(&v24->m_Param);
      if ( v20 != v9 && (v25 == 1 || v25 == 2) )
      {
        if ( CPlayer::IsRidingUnit(v24) )
        {
          if ( !v24->m_pUsingUnit->wBooster )
            v17 = 5;
        }
        else if ( v24->m_bMove )
        {
          if ( !CPlayer::GetSP(v24) )
            v17 = 5;
        }
        else if ( v24->m_fEquipSpeed >= (float)CPlayer::GetSP(v24) )
        {
          v17 = 5;
        }
      }
    }
    else
    {
      v17 = 11;
    }
  }
  else
  {
    v17 = 4;
  }
  if ( v17 )
  {
    CPlayer::SendMsg_MoveError(v24, v17);
    if ( v24->m_bMove )
    {
      v15 = CPlayer::IsOutExtraStopPos(v24, v24->m_fCurPos);
      CPlayer::SendMsg_Stop(v24, v15);
      CCharacter::Stop((CCharacter *)&v24->vfptr);
    }
  }
  else
  {
    v21 = v24->m_byPosRaceTown;
    v10 = CPlayerDB::GetRaceCode(&v24->m_Param);
    if ( v21 != v10 && !CPlayer::IsRidingUnit(v24) && !v24->m_bMove && v25 == 1 )
    {
      nSP = CPlayer::GetSP(v24);
      nSP = (signed int)ffloor((float)nSP - v24->m_fEquipSpeed);
      if ( nSP < 0 )
        nSP = 0;
      CPlayer::SetSP(v24, nSP, 0);
      CPlayer::SendMsg_AlterSPInform(v24);
    }
    v24->m_byMoveType = v25;
    v24->m_byMoveDirect = byDirect;
    memcpy_0(v24->m_fOldPos, v24->m_fCurPos, 0xCui64);
    memcpy_0(v24->m_fCurPos, fPos, 0xCui64);
    v19 = 1;
    if ( v24->m_bMove && v24->m_byLastDirect == (unsigned __int8)byDirect )
    {
      v11 = v24->m_fSendTarPos[0] - *Src;
      abs(v11);
      v22 = v11;
      v12 = v24->m_fSendTarPos[1] - Src[2];
      abs(v12);
      if ( v22 <= v12 )
      {
        v14 = v24->m_fSendTarPos[1] - Src[2];
        abs(v14);
        v23 = v14;
      }
      else
      {
        v13 = v24->m_fSendTarPos[0] - *Src;
        abs(v13);
        v23 = v13;
      }
      if ( v23 < 10.0 )
        v19 = 0;
    }
    if ( v19 )
    {
      v24->m_fSendTarPos[0] = *Src;
      v24->m_fSendTarPos[1] = Src[2];
      v24->m_byLastDirect = byDirect;
    }
    memcpy_0(v24->m_fTarPos, Src, 0xCui64);
    CCharacter::ResetSlot((CCharacter *)&v24->vfptr);
    CCharacter::Go((CCharacter *)&v24->vfptr);
    CPlayer::SendMsg_MoveNext(v24, v19);
    if ( _effect_parameter::GetEff_State(&v24->m_EP, 14) )
      CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v24->vfptr, 2, 14);
    v24->m_dwLastSetPointTime = GetLoopTime();
    if ( v24->m_bMineMode )
    {
      v24->m_bMineMode = 0;
      v24->m_dwMineNextTime = -1;
      CPlayer::SendMsg_MineCancle(v24);
    }
  }
}
