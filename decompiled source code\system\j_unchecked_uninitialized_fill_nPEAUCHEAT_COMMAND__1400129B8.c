/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAUCHEAT_COMMAND@@_KU1@V?$allocator@UCHEAT_COMMAND@@@std@@@stdext@@YAXPEAUCHEAT_COMMAND@@_KAEBU1@AEAV?$allocator@UCHEAT_COMMAND@@@std@@@Z
 * Address: 0x1400129B8
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CHEAT_COMMAND *,unsigned __int64,CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(CHEAT_COMMAND *_First, unsigned __int64 _Count, CHEAT_COMMAND *_Val, std::allocator<CHEAT_COMMAND> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CHEAT_COMMAND *,unsigned __int64,CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(
    _First,
    _<PERSON>,
    _<PERSON>,
    _<PERSON>);
}
