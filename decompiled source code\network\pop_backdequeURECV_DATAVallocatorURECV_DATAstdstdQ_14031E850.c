/*
 * Function: ?pop_back@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAAXXZ
 * Address: 0x14031E850
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::pop_back(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int64 v4; // [sp+20h] [bp-18h]@5
  unsigned __int64 v5; // [sp+28h] [bp-10h]@5
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !std::deque<RECV_DATA,std::allocator<RECV_DATA>>::empty(v6) )
  {
    v4 = v6->_Mysize + v6->_Myoff - 1;
    v5 = v4;
    if ( v6->_Mapsize <= v4 )
      v5 -= v6->_Mapsize;
    std::allocator<RECV_DATA>::destroy(&v6->_Alval, v6->_Map[v5]);
    if ( !--v6->_Mysize )
      v6->_Myoff = 0i64;
  }
}
