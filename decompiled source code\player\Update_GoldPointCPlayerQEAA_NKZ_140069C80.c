/*
 * Function: ?Update_GoldPoint@CPlayer@@QEAA_NK@Z
 * Address: 0x140069C80
 */

char __fastcall CPlayer::Update_GoldPoint(CPlayer *this, unsigned int dwPoint)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CActionPointSystemMgr *v4; // rax@4
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int dwPointa; // [sp+20h] [bp-18h]@5
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  unsigned int v9; // [sp+48h] [bp+10h]@1

  v9 = dwPoint;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CActionPointSystemMgr::Instance();
  if ( CActionPointSystemMgr::GetEventStatus(v4, 2) == 2 )
  {
    dwPointa = v9 + CUserDB::GetActPoint(v8->m_pUserDB, 2);
    CUserDB::Update_User_Action_Point(v8->m_pUserDB, 2, dwPointa);
    CPlayer::SendMsg_Alter_Action_Point(v8, 2, dwPointa);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
