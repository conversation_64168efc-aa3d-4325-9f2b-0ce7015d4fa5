/*
 * Function: ?LoadDB@CHonorGuild@@QEAA_NXZ
 * Address: 0x14025E710
 */

char __fastcall CHonorGuild::LoadDB(CHonorGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v3; // rax@8
  CAsyncLogger *v5; // rax@10
  CAsyncLogger *v6; // rax@13
  CAsyncLogger *v7; // rax@19
  __int64 v8; // [sp+0h] [bp-58h]@1
  char v9; // [sp+20h] [bp-38h]@6
  unsigned int j; // [sp+24h] [bp-34h]@4
  unsigned int dwSerial; // [sp+34h] [bp-24h]@12
  CHonorGuild *v12; // [sp+60h] [bp+8h]@1

  v12 = this;
  v1 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; (signed int)j < 3; ++j )
  {
    v9 = CRFWorldDatabase::Select_HonorGuild(pkDB, j, v12->m_pCurrHonorGuild[j], 0);
    if ( v9 != 2 && v9 )
    {
      v3 = CAsyncLogger::Instance();
      CAsyncLogger::FormatLog(v3, 8, "Faild Current CHonorGuild::LoadDB(RACE:%d)", j);
      return 0;
    }
    v9 = CRFWorldDatabase::Select_HonorGuild(pkDB, j, v12->m_pNextHonorGuild[j], 1);
    if ( v9 == 1 )
    {
      v5 = CAsyncLogger::Instance();
      CAsyncLogger::FormatLog(v5, 8, "Faild Next CHonorGuild::LoadDB(RACE:%d)", j);
      return 0;
    }
    if ( v12->m_pNextHonorGuild[j]->byListNum )
    {
      v12->m_bNext[j] = 1;
    }
    else
    {
      dwSerial = 0;
      v9 = CRFWorldDatabase::Select_ClearHonorGuild(pkDB, j, &dwSerial);
      if ( v9 == 1 )
      {
        v6 = CAsyncLogger::Instance();
        CAsyncLogger::FormatLog(v6, 8, "Faild Load ClearHonorGuild(RACE:%d)", j);
        return 0;
      }
      if ( dwSerial == -1 )
      {
        memset_0(v12->m_pNextHonorGuild[j], 0, 0xEDui64);
        v12->m_bNext[j] = 1;
      }
    }
  }
  v7 = CAsyncLogger::Instance();
  CAsyncLogger::FormatLog(v7, 8, "Success CHonorGuild::LoadDB()");
  return 1;
}
