/*
 * Function: ?ct_combine_ex_result@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293C30
 */

char __fastcall ct_combine_ex_result(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-188h]@1
  _combine_ex_item_result_zocl pSend; // [sp+30h] [bp-158h]@6
  CPlayer *v6; // [sp+190h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    _combine_ex_item_result_zocl::_combine_ex_item_result_zocl(&pSend);
    pSend.byErrCode = 0;
    pSend.byDlgType = 1;
    pSend.ItemBuff.byItemListNum = 1;
    pSend.ItemBuff.RewardItemList[0].Key.byRewardIndex = 0;
    pSend.ItemBuff.RewardItemList[0].Key.byTableCode = 6;
    pSend.ItemBuff.RewardItemList[0].Key.wItemIndex = 30;
    pSend.ItemBuff.RewardItemList[0].dwDur = 1;
    pSend.ItemBuff.RewardItemList[0].dwUpt = 2147483632;
    CPlayer::SendMsg_CombineItemExResult(v6, &pSend);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
