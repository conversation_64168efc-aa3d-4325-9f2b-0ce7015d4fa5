/*
 * Function: j_??$_Uninit_fill_n@PEAE_KEV?$allocator@E@std@@@std@@YAXPEAE_KAEBEAEAV?$allocator@E@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400131AB
 */

void __fastcall std::_Uninit_fill_n<unsigned char *,unsigned __int64,unsigned char,std::allocator<unsigned char>>(char *_First, unsigned __int64 _Count, const char *_Val, std::allocator<unsigned char> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<unsigned char *,unsigned __int64,unsigned char,std::allocator<unsigned char>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
