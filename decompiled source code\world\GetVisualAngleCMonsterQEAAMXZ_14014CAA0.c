/*
 * Function: ?GetVisualAngle@CMonster@@QEAAMXZ
 * Address: 0x14014CAA0
 */

float __fastcall CMonster::GetVisualAngle(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  CMonster *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return (float)*(signed int *)&v5->m_pRecordSet[25].m_strCode[32];
}
