/*
 * Function: ??$unchecked_fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@@stdext@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@@Z
 * Address: 0x140391A50
 */

void __fastcall stdext::unchecked_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+20h] [bp-28h]@4
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  TRC_AutoTrade **__formal; // [sp+31h] [bp-17h]@4
  TRC_AutoTrade **_Firsta; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  TRC_AutoTrade **_Vala; // [sp+60h] [bp+18h]@1

  _Vala = (TRC_AutoTrade **)_Val;
  _Counta = _Count;
  _Firsta = _First;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  LOBYTE(v5) = std::_Iter_cat<TRC_AutoTrade * *>(&__formal);
  v7 = v8;
  std::_Fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::random_access_iterator_tag>(
    _Firsta,
    _Counta,
    _Vala,
    (std::random_access_iterator_tag)v5->0,
    v8);
}
