/*
 * Function: ?CheckGuildBattleSuggestRequestToDestGuild@CGuild@@QEAAEKKKK@Z
 * Address: 0x1402579F0
 */

char __fastcall CGuild::CheckGuildBattleSuggestRequestToDestGuild(CGuild *this, unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  CGuild *v9; // [sp+30h] [bp+8h]@1
  unsigned int dwSerial; // [sp+38h] [bp+10h]@1

  dwSerial = dwSrcGuildSerial;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v9->m_bRankWait )
  {
    result = -92;
  }
  else if ( _guild_master_info::IsFill(&v9->m_MasterData) )
  {
    if ( v9->m_MasterData.pMember )
    {
      if ( v9->m_MasterData.pMember->pPlayer )
      {
        if ( v9->m_dTotalGold >= 5000.0 )
        {
          if ( GetGuildDataFromSerial(g_Guild, 500, dwSerial) )
          {
            if ( GetGuildDataFromSerial(g_Guild, 500, v9->m_dwSerial) )
              result = CGuild::DestGuildIsAvailableBattleRequestState(v9);
            else
              result = 111;
          }
          else
          {
            result = 111;
          }
        }
        else
        {
          result = -90;
        }
      }
      else
      {
        result = -77;
      }
    }
    else
    {
      result = -77;
    }
  }
  else
  {
    result = -83;
  }
  return result;
}
