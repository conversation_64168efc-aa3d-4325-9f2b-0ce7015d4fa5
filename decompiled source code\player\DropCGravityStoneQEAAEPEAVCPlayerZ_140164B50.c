/*
 * Function: ?Drop@CGravityStone@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x140164B50
 */

char __fastcall CGravityStone::Drop(CGravityStone *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-78h]@1
  _object_create_setdata pParam; // [sp+38h] [bp-40h]@6
  CGravityStone *v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CGravityStone::IsValidOwner(v7, pkPlayer) )
  {
    _object_create_setdata::_object_create_setdata(&pParam);
    pParam.m_pRecordSet = 0i64;
    pParam.m_nLayerIndex = v7->m_pkOwner->m_wMapLayerIndex;
    pParam.m_pMap = v7->m_pkOwner->m_pCurMap;
    if ( !CMapData::GetRandPosVirtualDumExcludeStdRange(
            pParam.m_pMap,
            v7->m_pkOwner->m_fCurPos,
            300,
            100,
            pParam.m_fStartPos) )
    {
      pParam.m_fStartPos[0] = v7->m_pkOwner->m_fCurPos[0];
      pParam.m_fStartPos[1] = v7->m_pkOwner->m_fCurPos[1];
      pParam.m_fStartPos[2] = v7->m_pkOwner->m_fCurPos[2];
    }
    if ( CGravityStone::Regen(v7, &pParam) )
    {
      CPlayer::ClearGravityStone(v7->m_pkOwner);
      v7->m_pkOwner = 0i64;
      result = 0;
    }
    else
    {
      result = -123;
    }
  }
  else
  {
    result = -125;
  }
  return result;
}
