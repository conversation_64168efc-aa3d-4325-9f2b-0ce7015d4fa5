/*
 * Function: ?CalcAttExp@CAnimus@@QEAAXPEAVCAttack@@@Z
 * Address: 0x140126F40
 */

void __fastcall CAnimus::CalcAttExp(CAnimus *this, CAttack *pAT)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  int v5; // eax@13
  int v6; // eax@14
  signed int v7; // eax@16
  signed int v8; // eax@21
  float v9; // xmm0_4@26
  signed int v10; // eax@30
  signed int v11; // eax@33
  __int64 v12; // [sp+0h] [bp-158h]@1
  bool bUseExpAdditionItem; // [sp+20h] [bp-138h]@32
  char *strErrorCodePos; // [sp+28h] [bp-130h]@33
  bool bPcbangPrimiumFavorReward; // [sp+30h] [bp-128h]@33
  int v16; // [sp+40h] [bp-118h]@4
  int j; // [sp+44h] [bp-114h]@8
  CMonster *v18; // [sp+48h] [bp-110h]@10
  int v19; // [sp+50h] [bp-108h]@10
  _base_fld *v20; // [sp+58h] [bp-100h]@14
  CMonster *v21; // [sp+60h] [bp-F8h]@14
  int v22; // [sp+68h] [bp-F0h]@14
  int v23; // [sp+6Ch] [bp-ECh]@14
  float v24; // [sp+70h] [bp-E8h]@16
  int v25; // [sp+74h] [bp-E4h]@16
  float v26; // [sp+78h] [bp-E0h]@17
  int v27; // [sp+7Ch] [bp-DCh]@21
  CPlayer *out_ppMember; // [sp+90h] [bp-C8h]@22
  int v29[11]; // [sp+E8h] [bp-70h]@26
  char v30; // [sp+114h] [bp-44h]@22
  float v31; // [sp+118h] [bp-40h]@24
  int k; // [sp+11Ch] [bp-3Ch]@24
  float v33; // [sp+120h] [bp-38h]@29
  int v34; // [sp+124h] [bp-34h]@30
  int v35; // [sp+128h] [bp-30h]@13
  CGameObjectVtbl *v36; // [sp+130h] [bp-28h]@13
  float v37; // [sp+138h] [bp-20h]@16
  float v38; // [sp+13Ch] [bp-1Ch]@21
  float v39; // [sp+140h] [bp-18h]@30
  float v40; // [sp+144h] [bp-14h]@33
  CAnimus *v41; // [sp+160h] [bp+8h]@1
  CAttack *v42; // [sp+168h] [bp+10h]@1

  v42 = pAT;
  v41 = this;
  v2 = &v12;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v16 = ((int (__fastcall *)(CAnimus *))v41->vfptr->GetLevel)(v41);
  if ( v41->m_pMaster )
  {
    if ( v16 - 1 < 50
      || (v4 = ((int (__fastcall *)(CPlayer *))v41->m_pMaster->vfptr->GetLevel)(v41->m_pMaster), v4 >= v16 - 1) )
    {
      for ( j = 0; j < v42->m_nDamagedObjNum; ++j )
      {
        v18 = (CMonster *)v42->m_DamList[j].m_pChar;
        v19 = v42->m_DamList[j].m_nDamage;
        if ( v18->m_ObjID.m_byID == 1
          && v19 > 1
          && !(unsigned __int8)((int (__fastcall *)(CAnimus *))v41->vfptr->IsInTown)(v41) )
        {
          v35 = ((int (__fastcall *)(CAnimus *))v41->vfptr->GetLevel)(v41);
          v36 = v18->vfptr;
          v5 = ((int (__fastcall *)(CMonster *))v36->GetLevel)(v18);
          if ( abs_0(v35 - v5) <= 10 )
          {
            v20 = v18->m_pRecordSet;
            v21 = v18;
            v6 = ((int (__fastcall *)(CMonster *))v18->vfptr->GetHP)(v18);
            v22 = v6 - v19;
            v23 = v19;
            if ( v6 - v19 < 0 )
            {
              v22 = 0;
              v23 = ((int (__fastcall *)(CMonster *))v21->vfptr->GetHP)(v21);
            }
            v24 = (float)(*(float *)&v20[4].m_strCode[16] * 0.69999999)
                * (float)((float)v23 / *(float *)&v20[25].m_strCode[4]);
            v37 = v24 / 500.0;
            v7 = ((int (__fastcall *)(CMonster *))v21->vfptr->GetLevel)(v21);
            v25 = (signed int)ffloor(v37 + (float)v7);
            CAnimus::AlterExp(v41, v25);
            if ( !v22 )
            {
              v26 = 0.0;
              if ( CMonster::GetEmotionState(v21) == 4 )
                v26 = *(float *)&v20[4].m_strCode[16] * 0.5;
              else
                v26 = *(float *)&v20[4].m_strCode[16] * 0.30000001;
              if ( CPartyPlayer::IsPartyMode(v41->m_pMaster->m_pPartyMgr) )
              {
                v30 = CPlayer::_GetPartyMemberInCircle(v41->m_pMaster, &out_ppMember, 8, 1);
                if ( (signed int)(unsigned __int8)v30 > 0 )
                  v26 = v26 * CPlayer::s_fExpDivUnderParty_Kill[(unsigned __int8)v30 - 1];
                v31 = 0.0;
                for ( k = 0; k < (unsigned __int8)v30; ++k )
                {
                  v9 = (float)((int (__fastcall *)(_QWORD))(*(&out_ppMember + k))->vfptr->GetLevel)(*(&out_ppMember + k));
                  pow(v9, 3);
                  *(float *)&v29[k] = v9;
                  v31 = v31 + *(float *)&v29[k];
                }
                for ( k = 0; k < (unsigned __int8)v30; ++k )
                {
                  v33 = (float)(v26 * *(float *)&v29[k]) / v31;
                  if ( *(&out_ppMember + k) == v41->m_pMaster )
                  {
                    v39 = v33 / 500.0;
                    v10 = ((int (__fastcall *)(CMonster *))v21->vfptr->GetLevel)(v21);
                    v34 = (signed int)ffloor(v39 + (float)v10);
                    CAnimus::AlterExp(v41, v34);
                  }
                  if ( CPlayer::IsRidingUnit(*(&out_ppMember + k)) )
                  {
                    v40 = v33 / 180.0;
                    v11 = ((int (__fastcall *)(_QWORD))v21->vfptr->GetLevel)(v21);
                    bPcbangPrimiumFavorReward = 1;
                    strErrorCodePos = 0i64;
                    bUseExpAdditionItem = 0;
                    CPlayer::Emb_AlterStat(*(&out_ppMember + k), 6, 0, (signed int)ffloor(v40 + (float)v11), 0, 0i64, 1);
                  }
                  else
                  {
                    bUseExpAdditionItem = 0;
                    CPlayer::AlterExp(*(&out_ppMember + k), v33, 0, 0, 0);
                  }
                }
              }
              else
              {
                v38 = v26 / 500.0;
                v8 = ((int (__fastcall *)(_QWORD))v21->vfptr->GetLevel)(v21);
                v27 = (signed int)ffloor(v38 + (float)v8);
                CAnimus::AlterExp(v41, v27);
              }
            }
          }
        }
      }
    }
  }
}
