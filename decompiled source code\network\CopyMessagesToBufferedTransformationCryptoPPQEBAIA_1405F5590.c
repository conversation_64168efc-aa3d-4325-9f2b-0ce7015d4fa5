/*
 * Function: ?CopyMessagesTo@BufferedTransformation@CryptoPP@@QEBAIAEAV12@IAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x1405F5590
 */

int __fastcall CryptoPP::BufferedTransformation::CopyMessagesTo(__int64 a1, __int64 a2, unsigned int a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@2
  int result; // eax@2
  __int64 v7; // [sp+30h] [bp+8h]@1
  __int64 v8; // [sp+38h] [bp+10h]@1
  unsigned int v9; // [sp+40h] [bp+18h]@1
  __int64 v10; // [sp+48h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  LODWORD(v4) = (*(int (**)(void))(*(_QWORD *)a1 + 320i64))();
  if ( v4 )
  {
    LODWORD(v5) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v7 + 320i64))(v7);
    result = CryptoPP::BufferedTransformation::CopyMessagesTo(v5, v8, v9, v10);
  }
  else
  {
    result = 0;
  }
  return result;
}
