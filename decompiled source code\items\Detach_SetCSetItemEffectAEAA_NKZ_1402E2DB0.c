/*
 * Function: ?Detach_Set@CSetItemEffect@@AEAA_NK@Z
 * Address: 0x1402E2DB0
 */

char __fastcall CSetItemEffect::Detach_Set(CSetItemEffect *this, unsigned int dwSetItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@8
  CSetItemEffect *v7; // [sp+40h] [bp+8h]@1
  unsigned int dwSetItema; // [sp+48h] [bp+10h]@1

  dwSetItema = dwSetItem;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_byTotalSetCount > 0 )
  {
    if ( CSetItemEffect::IsSetOn(v7, dwSetItem) )
    {
      for ( j = 0; j < 6; ++j )
      {
        if ( v7->m_setCount[j].m_bCheckSetEffect == 1 && v7->m_setCount[j].m_dwSetItem == dwSetItema )
        {
          CSetItemEffect::Init_Data(v7, j);
          --v7->m_byTotalSetCount;
          return 1;
        }
      }
      result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
