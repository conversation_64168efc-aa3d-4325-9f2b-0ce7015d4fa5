/*
 * Function: _CUnmannedTraderDivisionInfo::GetGroupID_::_1_::dtor$2
 * Address: 0x14036DBF0
 */

void __fastcall CUnmannedTraderDivisionInfo::GetGroupID_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>((std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)(a2 + 104));
}
