/*
 * Function: ?UpdateUseFlag@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@IIK@Z
 * Address: 0x1403DD120
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleScheduleManager::UpdateUseFlag(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiDayID, unsigned int uiMapID, unsigned int dwID)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleSchedule *result; // rax@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v8; // [sp+30h] [bp+8h]@1
  unsigned int v9; // [sp+38h] [bp+10h]@1
  unsigned int uiMapIDa; // [sp+40h] [bp+18h]@1
  unsigned int dwIDa; // [sp+48h] [bp+20h]@1

  dwIDa = dwID;
  uiMapIDa = uiMapID;
  v9 = uiDayID;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( uiDayID == GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetDayID(v8->m_pkTodaySchedule) )
  {
    result = GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::UpdateUseFlag(v8->m_pkTodaySchedule, uiMapIDa, dwIDa);
  }
  else if ( v9 == GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetDayID(v8->m_pkTomorrowSchedule) )
  {
    result = GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::UpdateUseFlag(
               v8->m_pkTomorrowSchedule,
               uiMapIDa,
               dwIDa);
  }
  else
  {
    result = 0i64;
  }
  return result;
}
