/*
 * Function: ?DE_Potion_Class_Refine@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017E960
 */

char __fastcall DE_Potion_Class_Refine(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-88h]@1
  CPlayer *v8; // [sp+30h] [bp-58h]@9
  char szMsg; // [sp+44h] [bp-44h]@11
  char pbyType; // [sp+64h] [bp-24h]@11
  char v11; // [sp+65h] [bp-23h]@11
  CCharacter *v12; // [sp+90h] [bp+8h]@1
  char *v13; // [sp+A8h] [bp+20h]@1

  v13 = byRet;
  v12 = pActChar;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pTargetChar )
  {
    if ( v12->m_ObjID.m_byID || pTargetChar->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v8 = (CPlayer *)pTargetChar;
      if ( v12 == pTargetChar )
      {
        szMsg = CPlayer::pc_InitClass(v8);
        pbyType = 11;
        v11 = 25;
        CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
        if ( szMsg )
        {
          *v13 = 18;
          result = 0;
        }
        else
        {
          result = 1;
        }
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
