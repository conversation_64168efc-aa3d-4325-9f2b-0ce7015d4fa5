/*
 * Function: ?OnInitDialog@LicensePopupDlg@@UEAAHXZ
 * Address: 0x140026960
 */

signed __int64 __fastcall LicensePopupDlg::OnInitDialog(LicensePopupDlg *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  HMODULE v3; // rax@4
  HMODULE v4; // rax@4
  HMODULE v5; // rax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  HGLOBAL hResData; // [sp+20h] [bp-28h]@4
  HRSRC hResInfo; // [sp+28h] [bp-20h]@4
  char *v10; // [sp+30h] [bp-18h]@4
  DWORD v11; // [sp+38h] [bp-10h]@4
  LicensePopupDlg *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v1 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CDialog::OnInitDialog((CDialog *)&v12->vfptr);
  v3 = AfxGetInstanceHandle();
  hResInfo = FindResourceA(v3, (LPCSTR)0x97, "Text");
  v4 = AfxGetInstanceHandle();
  hResData = LoadResource(v4, hResInfo);
  v10 = (char *)LockResource(hResData);
  v5 = AfxGetInstanceHandle();
  v11 = SizeofResource(v5, hResInfo);
  v12->m_bOk = 0;
  CWnd::SetWindowTextA((CWnd *)&v12->m_EditCtrl.vfptr, v10);
  return 1i64;
}
