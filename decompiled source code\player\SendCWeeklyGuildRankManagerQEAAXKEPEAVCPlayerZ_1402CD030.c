/*
 * Function: ?Send@CWeeklyGuildRankManager@@QEAAXKEPEAVCPlayer@@@Z
 * Address: 0x1402CD030
 */

void __fastcall CWeeklyGuildRankManager::Send(CWeeklyGuildRankManager *this, unsigned int dwVer, char byTabRace, CPlayer *pkPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@6
  int v7; // ecx@6
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned int v9; // [sp+30h] [bp-18h]@4
  CWeeklyGuildRankInfo *v10; // [sp+38h] [bp-10h]@6
  CWeeklyGuildRankManager *v11; // [sp+50h] [bp+8h]@1
  unsigned int dwVera; // [sp+58h] [bp+10h]@1
  char v13; // [sp+60h] [bp+18h]@1
  CPlayer *v14; // [sp+68h] [bp+20h]@1

  v14 = pkPlayer;
  v13 = byTabRace;
  dwVera = dwVer;
  v11 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = -1;
  if ( pkPlayer->m_Param.m_pGuild )
    v9 = pkPlayer->m_Param.m_pGuild->m_dwSerial;
  v6 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
  v7 = v14->m_ObjID.m_wIndex;
  v10 = &v11->m_kInfo;
  CWeeklyGuildRankInfo::Send(&v11->m_kInfo, dwVera, v7, v13, v6, v9);
}
