/*
 * Function: ?CompleteReRegist@CUnmannedTraderUserInfoTable@@QEAAXPEAD@Z
 * Address: 0x140364520
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteReRegist(CUnmannedTraderUserInfoTable *this, char *pLoadData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderGroupItemInfoTable *v4; // rax@11
  unsigned int v5; // ecx@12
  unsigned int v6; // ecx@18
  __int64 v7; // [sp+0h] [bp-68h]@1
  unsigned int dwU; // [sp+20h] [bp-48h]@12
  _DWORD byType[2]; // [sp+28h] [bp-40h]@18
  char *v10; // [sp+30h] [bp-38h]@4
  CUnmannedTraderUserInfo *v11; // [sp+38h] [bp-30h]@4
  CPlayer *v12; // [sp+40h] [bp-28h]@4
  unsigned int dwD; // [sp+48h] [bp-20h]@8
  unsigned __int8 j; // [sp+4Ch] [bp-1Ch]@8
  __int64 v15; // [sp+50h] [bp-18h]@11
  __int64 v16; // [sp+58h] [bp-10h]@11
  CUnmannedTraderUserInfoTable *v17; // [sp+70h] [bp+8h]@1
  char *pLoadDataa; // [sp+78h] [bp+10h]@1

  pLoadDataa = pLoadData;
  v17 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = pLoadData;
  v11 = CUnmannedTraderUserInfoTable::FindUser(v17, *((_WORD *)pLoadData + 1), *((_DWORD *)pLoadData + 2));
  v12 = 0i64;
  if ( !CUnmannedTraderUserInfo::IsNull(v11) && (v12 = CUnmannedTraderUserInfo::FindOwner(v11)) != 0i64 && v12->m_bOper )
  {
    CUnmannedTraderUserInfo::ClearRequest(v11);
    CUnmannedTraderUserInfo::CompleteReRegist(v11, pLoadDataa, v17->m_pkLogger);
  }
  else
  {
    dwD = 0;
    for ( j = 0; j < (signed int)(unsigned __int8)v10[4]; ++j )
    {
      if ( v10[28 * j + 12] )
      {
        if ( v10[28 * j + 13] && ((unsigned __int8)v10[28 * j + 12] == 202 || v10[28 * j + 12] == 24) )
          dwD += *(_DWORD *)&v10[28 * j + 16];
        v6 = (unsigned __int8)v10[28 * j + 13];
        byType[0] = (unsigned __int8)v10[28 * j + 12];
        dwU = v6;
        CUnmannedTraderUserInfoTable::Log(
          v17,
          "CUnmannedTraderUserInfoTable::CompleteReRegist( BYTE byRet, char * pLoadData )\r\n"
          "\t\tdwRegistSerial(%u) dwOwnerSerial(%u) bRegist(%d) byProcRet(%u)\r\n",
          *(_DWORD *)&v10[28 * j + 32],
          *((_DWORD *)v10 + 2));
      }
      else
      {
        v15 = 28i64 * j;
        v16 = 28i64 * j;
        v4 = CUnmannedTraderGroupItemInfoTable::Instance();
        if ( !CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v4, v10[v16 + 24], v10[v15 + 25]) )
        {
          v5 = (unsigned __int8)v10[28 * j + 24];
          dwU = (unsigned __int8)v10[28 * j + 25];
          CLogFile::Write(
            v17->m_pkLogger,
            "CUnmannedTraderController::CompleteRegist(...)\r\n"
            "\t\tOwner : (%u)\r\n"
            "\t\tCUnmannedTraderGroupItemInfoTable::Instance()->IncreaseVersion(\r\n"
            "\t\tpkQuery->byClass1(%u), pkQuery->byClass2(%u) ) Fail!\r\n",
            *((_DWORD *)v10 + 2),
            v5);
        }
      }
    }
    if ( dwD )
    {
      LOBYTE(byType[0]) = 1;
      dwU = 0xFFFFFFF;
      CMainThread::Push_ChargeItem(&g_Main, *((_DWORD *)v10 + 2), 0xFFFFFFFF, dwD, 0xFFFFFFFu, 1);
      CUnmannedTraderUserInfoTable::Log(
        v17,
        "CUnmannedTraderUserInfoTable::CompleteReRegist( BYTE byRet, char * pLoadData )\r\n"
        "\t\tdwOwnerSerial(%u) Push_ChargeItem( dwSumTax(%u) )!\r\n",
        *((_DWORD *)v10 + 2),
        dwD);
    }
  }
}
