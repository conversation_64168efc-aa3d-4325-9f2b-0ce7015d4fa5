/*
 * Function: ??0CMonsterEventRespawn@@QEAA@XZ
 * Address: 0x1402A5D40
 */

void __fastcall CMonsterEventRespawn::CMonsterEventRespawn(CMonsterEventRespawn *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMonsterEventRespawn *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->vfptr = (CMonsterEventRespawnVtbl *)&CMonsterEventRespawn::`vftable';
  `vector constructor iterator'(
    v4->m_EventRespawn,
    0x4D78ui64,
    32,
    (void *(__cdecl *)(void *))_event_respawn::_event_respawn);
  v4->m_nLoadEventRespawn = 0;
}
