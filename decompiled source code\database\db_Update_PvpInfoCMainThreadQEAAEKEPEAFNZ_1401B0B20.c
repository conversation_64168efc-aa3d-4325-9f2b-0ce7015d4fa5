/*
 * Function: ?db_Update_PvpInfo@CMainThread@@QEAAEKEPEAFN@Z
 * Address: 0x1401B0B20
 */

char __fastcall CMainThread::db_Update_PvpInfo(CMainThread *this, unsigned int dwSerial, char byLevel, __int16 *pzClassHistory, long double dPvpPoint)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-28h]@1
  CMainThread *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( CRFWorldDatabase::Update_PvpPointInfo(v9->m_pWorldDB, dwSerial, pzClassHistory, dPvpPoint) )
    result = 0;
  else
    result = 24;
  return result;
}
