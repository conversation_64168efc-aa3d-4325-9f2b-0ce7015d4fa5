/*
 * Function: ?CompleteSelectReservedSchedule@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x14034D4B0
 */

void __fastcall CUnmannedTraderController::CompleteSelectReservedSchedule(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderScheduler *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  _unmannedtrader_reserved_schedule_info *pkInfo; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pkInfo = (_unmannedtrader_reserved_schedule_info *)pLoadData;
  if ( !byRet )
  {
    if ( pkInfo->dwCnt )
    {
      v5 = CUnmannedTraderScheduler::Instance();
      CUnmannedTraderScheduler::Update(v5, pkInfo);
    }
  }
}
