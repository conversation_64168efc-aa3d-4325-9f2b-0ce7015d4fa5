/*
 * Function: ??0CMoveMapLimitRightInfoList@@QEAA@XZ
 * Address: 0x1403A1E10
 */

void __fastcall CMoveMapLimitRightInfoList::CMoveMapLimitRightInfoList(CMoveMapLimitRightInfoList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfoList *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(&v4->m_vecRight);
}
