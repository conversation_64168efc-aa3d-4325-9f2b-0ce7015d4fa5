/*
 * Function: ?GetBallOwner@CNormalGuildBattleField@GUILD_BATTLE@@QEAAPEAVCPlayer@@XZ
 * Address: 0x1403ED210
 */

CPlayer *__fastcall GUILD_BATTLE::CNormalGuildBattleField::GetBallOwner(GUILD_BATTLE::CNormalGuildBattleField *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPlayer *v5; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_bInit )
  {
    v5 = CGravityStone::GetOwner(v6->m_pkBall);
    if ( v5 )
      result = v5;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
