/*
 * Function: j_?FindItem@CUnmannedTraderScheduler@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@EK@Z
 * Address: 0x1400058AD
 */

std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__fastcall CUnmannedTraderScheduler::FindItem(CUnmannedTraderScheduler *this, std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *result, char byType, unsigned int dwRegistSerial)
{
  return CUnmannedTraderScheduler::FindItem(this, result, byType, dwRegistSerial);
}
