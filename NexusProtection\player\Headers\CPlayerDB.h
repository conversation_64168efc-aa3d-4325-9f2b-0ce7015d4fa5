#pragma once

/**
 * @file CPlayerDB.h
 * @brief Modern C++20 CPlayerDB class definition
 * 
 * Refactored from decompiled sources:
 * - 0CPlayerDBQEAAXZ_1401087E0.c (Constructor)
 * - InitPlayerDBCPlayerDBQEAAXPEAVCPlayerZ_140108B60.c (Initialization)
 * 
 * This file provides a modern, type-safe, and maintainable implementation
 * of the player database management system for NexusProtection.
 */

#include <memory>
#include <string>
#include <array>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <optional>

// Forward declarations
class CPlayer;
class AutominePersonalMgr;
class CGuild;
class CGuildMember;

// Legacy structure forward declarations
struct _STORAGE_LIST;
struct _QUEST_DB_BASE;
struct _UNIT_DB_BASE;
struct _ITEMCOMBINE_DB_BASE;
struct _quick_link;
struct CPostStorage;
struct CPostReturnStorage;

namespace NexusProtection {
namespace Player {

/**
 * @brief Database operation result enumeration
 */
enum class DatabaseResult : uint8_t {
    Success = 0,
    InvalidData,
    ConnectionError,
    QueryError,
    ConversionError,
    SystemError
};

/**
 * @brief Storage type enumeration
 */
enum class StorageType : uint8_t {
    Personal = 0,
    Guild,
    Warehouse,
    Premium,
    Event,
    Special,
    Temporary,
    Archive,
    MaxStorageTypes = 8
};

/**
 * @brief Resource buffer management
 */
class ResourceBuffer {
public:
    ResourceBuffer();
    ~ResourceBuffer();

    /**
     * @brief Initialize resource buffer
     * @param maxResources Maximum number of resources
     * @return true if successful, false otherwise
     */
    bool Initialize(size_t maxResources);

    /**
     * @brief Clear all resources
     */
    void Clear();

    /**
     * @brief Get resource value
     * @param index Resource index
     * @return Resource value or 0 if invalid
     */
    uint16_t GetResource(size_t index) const;

    /**
     * @brief Set resource value
     * @param index Resource index
     * @param value Resource value
     * @return true if successful, false otherwise
     */
    bool SetResource(size_t index, uint16_t value);

    /**
     * @brief Get maximum resource count
     * @return Maximum resource count
     */
    size_t GetMaxResources() const { return m_maxResources; }

private:
    std::unique_ptr<uint16_t[]> m_buffer;
    size_t m_maxResources{0};
    mutable std::mutex m_bufferMutex;
};

/**
 * @brief Storage management system
 */
class StorageManager {
public:
    StorageManager();
    ~StorageManager();

    /**
     * @brief Initialize all storage systems
     * @return true if successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown all storage systems
     */
    void Shutdown();

    /**
     * @brief Get storage by type
     * @param type Storage type
     * @return Pointer to storage or nullptr if invalid
     */
    _STORAGE_LIST* GetStorage(StorageType type);

    /**
     * @brief Set all storages to empty
     */
    void SetAllEmpty();

private:
    std::array<std::unique_ptr<_STORAGE_LIST>, static_cast<size_t>(StorageType::MaxStorageTypes)> m_storages;
    mutable std::mutex m_storageMutex;
};

/**
 * @brief Quick link management
 */
class QuickLinkManager {
public:
    static constexpr size_t MAX_QUICK_LINKS = 50;

    QuickLinkManager();
    ~QuickLinkManager();

    /**
     * @brief Initialize all quick links
     * @return true if successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Get quick link by index
     * @param index Quick link index
     * @return Pointer to quick link or nullptr if invalid
     */
    _quick_link* GetQuickLink(size_t index);

    /**
     * @brief Clear all quick links
     */
    void ClearAll();

private:
    std::array<std::unique_ptr<_quick_link>, MAX_QUICK_LINKS> m_quickLinks;
    mutable std::mutex m_linkMutex;
};

/**
 * @brief Guild management data
 */
struct GuildData {
    std::shared_ptr<CGuild> guild;
    std::shared_ptr<CGuildMember> memberPtr;
    std::shared_ptr<CGuild> applyGuild;
    uint8_t classInGuild{0xFF};
    bool guildLock{false};
};

/**
 * @brief Class history management
 */
class ClassHistoryManager {
public:
    static constexpr size_t MAX_CLASS_HISTORY = 3;

    ClassHistoryManager();
    ~ClassHistoryManager();

    /**
     * @brief Initialize class history
     * @return true if successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Get current class data
     * @return Pointer to current class data
     */
    void* GetCurrentClassData() const { return m_currentClassData; }

    /**
     * @brief Set current class data
     * @param classData Pointer to class data
     */
    void SetCurrentClassData(void* classData) { m_currentClassData = classData; }

    /**
     * @brief Get class history entry
     * @param index History index (0-2)
     * @return Pointer to class history or nullptr if invalid
     */
    void* GetClassHistory(size_t index) const;

    /**
     * @brief Set class history entry
     * @param index History index (0-2)
     * @param classData Pointer to class data
     * @return true if successful, false otherwise
     */
    bool SetClassHistory(size_t index, void* classData);

private:
    void* m_currentClassData{nullptr};
    std::array<void*, MAX_CLASS_HISTORY> m_classHistory{};
    std::array<void**, 4> m_historyEffects{};  // Effect pointers for legacy compatibility
    mutable std::mutex m_historyMutex;
};

/**
 * @brief Modern C++20 CPlayerDB class
 * 
 * This class manages all player database operations and data persistence.
 * It provides a modern, thread-safe interface for player data management.
 */
class CPlayerDB {
public:
    // Constructor and Destructor
    CPlayerDB();
    virtual ~CPlayerDB();

    // Disable copy constructor and assignment operator
    CPlayerDB(const CPlayerDB&) = delete;
    CPlayerDB& operator=(const CPlayerDB&) = delete;

    // Enable move constructor and assignment operator
    CPlayerDB(CPlayerDB&&) noexcept = default;
    CPlayerDB& operator=(CPlayerDB&&) noexcept = default;

    /**
     * @brief Initialize player database
     * 
     * Modern implementation of InitPlayerDB method.
     * Initializes all database subsystems and data structures.
     * 
     * @param player Shared pointer to player instance
     * @return DatabaseResult indicating success or failure
     */
    DatabaseResult InitializePlayerDB(std::shared_ptr<CPlayer> player);

    /**
     * @brief Shutdown player database
     * 
     * Safely shuts down all database subsystems.
     */
    void Shutdown();

    /**
     * @brief Initialize resource buffer
     * @return true if successful, false otherwise
     */
    bool InitializeResourceBuffer();

    /**
     * @brief Initialize alter mastery system
     * @return true if successful, false otherwise
     */
    bool InitializeAlterMastery();

    // Player reference
    std::shared_ptr<CPlayer> GetPlayer() const { return m_player; }
    void SetPlayer(std::shared_ptr<CPlayer> player) { m_player = player; }

    // Serial management
    uint16_t GetSerialCount() const { return m_serialCount; }
    void SetSerialCount(uint16_t count) { m_serialCount = count; }
    void IncrementSerialCount() { ++m_serialCount; }

    // Resource management
    ResourceBuffer& GetResourceBuffer() { return m_resourceBuffer; }
    const ResourceBuffer& GetResourceBuffer() const { return m_resourceBuffer; }

    // Storage management
    StorageManager& GetStorageManager() { return m_storageManager; }
    const StorageManager& GetStorageManager() const { return m_storageManager; }

    // Quick link management
    QuickLinkManager& GetQuickLinkManager() { return m_quickLinkManager; }
    const QuickLinkManager& GetQuickLinkManager() const { return m_quickLinkManager; }

    // Guild management
    const GuildData& GetGuildData() const { return m_guildData; }
    GuildData& GetGuildData() { return m_guildData; }

    // Class history management
    ClassHistoryManager& GetClassHistoryManager() { return m_classHistoryManager; }
    const ClassHistoryManager& GetClassHistoryManager() const { return m_classHistoryManager; }

    // PvP management
    uint8_t GetPvPGrade() const { return m_pvpGrade; }
    void SetPvPGrade(uint8_t grade) { m_pvpGrade = grade; }

    // Trap management
    int32_t GetMakeTrapMaxNum() const { return m_makeTrapMaxNum; }
    void SetMakeTrapMaxNum(int32_t maxNum) { m_makeTrapMaxNum = maxNum; }

    // Automine management
    AutominePersonalMgr* GetAutomineManager() const { return m_automineManager; }
    bool IsPersonalAmineInven() const { return m_personalAmineInven; }

    // PvP point management
    double GetPvpPointLeak() const { return m_pvpPointLeak; }
    void SetPvpPointLeak(double leak) { m_pvpPointLeak = leak; }

    /**
     * @brief Get last error message
     * @return Last error message
     */
    const std::string& GetLastError() const { return m_lastError; }

protected:
    // Core player reference
    std::shared_ptr<CPlayer> m_player;
    
    // Serial management
    std::atomic<uint16_t> m_serialCount{0};
    
    // Resource management
    ResourceBuffer m_resourceBuffer;
    
    // Storage systems
    StorageManager m_storageManager;
    
    // Database subsystems
    std::unique_ptr<_QUEST_DB_BASE> m_questDB;
    std::unique_ptr<_UNIT_DB_BASE> m_unitDB;
    std::unique_ptr<_ITEMCOMBINE_DB_BASE> m_itemCombineDB;
    
    // Quick link management
    QuickLinkManager m_quickLinkManager;
    
    // Guild management
    GuildData m_guildData;
    
    // Class history management
    ClassHistoryManager m_classHistoryManager;
    
    // PvP and combat
    std::atomic<uint8_t> m_pvpGrade{0};
    std::atomic<int32_t> m_makeTrapMaxNum{0};
    
    // Automine system
    AutominePersonalMgr* m_automineManager{nullptr};
    std::atomic<bool> m_personalAmineInven{false};
    
    // Post storage systems
    std::unique_ptr<CPostStorage> m_postStorage;
    std::unique_ptr<CPostReturnStorage> m_returnPostStorage;
    
    // PvP point management
    std::atomic<double> m_pvpPointLeak{0.0};
    
    // Synchronization
    mutable std::mutex m_dbMutex;
    
    // Error handling
    std::string m_lastError;

private:
    /**
     * @brief Set last error message
     * @param error Error message
     */
    void SetLastError(const std::string& error) { m_lastError = error; }

    /**
     * @brief Initialize database subsystems
     * @return true if successful, false otherwise
     */
    bool InitializeDatabaseSubsystems();

    /**
     * @brief Initialize post storage systems
     * @return true if successful, false otherwise
     */
    bool InitializePostStorage();

    /**
     * @brief Validate database state
     * @return true if valid, false otherwise
     */
    bool ValidateDatabaseState() const;
};

} // namespace Player
} // namespace NexusProtection
