/*
 * Function: ?SearchBuddyLogin@_BUDDY_LIST@@QEAA_NPEAVCPlayer@@KPEAD@Z
 * Address: 0x1400793A0
 */

char __fastcall _BUDDY_LIST::SearchBuddyLogin(_BUDDY_LIST *this, CPlayer *pLoger, unsigned int dwSerial, char *pwszName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _BUDDY_LIST *v9; // [sp+40h] [bp+8h]@1
  CPlayer *v10; // [sp+48h] [bp+10h]@1
  unsigned int v11; // [sp+50h] [bp+18h]@1
  const char *Source; // [sp+58h] [bp+20h]@1

  Source = pwszName;
  v11 = dwSerial;
  v10 = pLoger;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < 50; ++j )
  {
    if ( _BUDDY_LIST::__list::fill((_BUDDY_LIST::__list *)v9 + j) && v9->m_List[j].dwSerial == v11 )
    {
      v9->m_List[j].pPtr = v10;
      strcpy_0(v9->m_List[j].wszName, Source);
      return 1;
    }
  }
  return 0;
}
