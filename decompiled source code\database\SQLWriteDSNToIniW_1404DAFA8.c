/*
 * Function: SQLWriteDSNToIniW
 * Address: 0x1404DAFA8
 */

int __fastcall SQLWriteDSNToIniW(const unsigned __int16 *lpszDSN, const unsigned __int16 *lpszDriver)
{
  const unsigned __int16 *v2; // rdi@1
  const unsigned __int16 *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  int result; // eax@2

  v2 = lpszDSN;
  v3 = lpszDriver;
  v4 = ODBC___GetSetupProc("SQLWriteDSNToIniW");
  if ( v4 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
