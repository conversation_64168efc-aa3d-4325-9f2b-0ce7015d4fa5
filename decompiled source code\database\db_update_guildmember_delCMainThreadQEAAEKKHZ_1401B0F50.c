/*
 * Function: ?db_update_guildmember_del@CMainThread@@QEAAEKKH@Z
 * Address: 0x1401B0F50
 */

char __fastcall CMainThread::db_update_guildmember_del(CMainThread *this, unsigned int dwAvatorSerial, unsigned int dwGuildSerial, int nMemberNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CMainThread *v8; // [sp+30h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+40h] [bp+18h]@1
  int v10; // [sp+48h] [bp+20h]@1

  v10 = nMemberNum;
  dwGuildSeriala = dwGuildSerial;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( nMemberNum >= 0 )
  {
    if ( CRFWorldDatabase::Update_UserGuildData(v8->m_pWorldDB, dwAvatorSerial, 0xFFFFFFFF, -1) )
    {
      if ( CRFWorldDatabase::Update_GuildMemberCount(v8->m_pWorldDB, dwGuildSeriala, v10) )
        result = 0;
      else
        result = 24;
    }
    else
    {
      result = 24;
    }
  }
  else
  {
    result = 24;
  }
  return result;
}
