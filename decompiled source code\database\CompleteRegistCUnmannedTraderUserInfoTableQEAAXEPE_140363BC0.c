/*
 * Function: ?CompleteRegist@CUnmannedTraderUserInfoTable@@QEAAXEPEAD@Z
 * Address: 0x140363BC0
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteRegist(CUnmannedTraderUserInfoTable *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderGroupItemInfoTable *v5; // rax@10
  unsigned int v6; // ecx@11
  __int64 v7; // [sp+0h] [bp-58h]@1
  unsigned int dwU; // [sp+20h] [bp-38h]@9
  _DWORD byType[2]; // [sp+28h] [bp-30h]@9
  char *v10; // [sp+30h] [bp-28h]@4
  CUnmannedTraderUserInfo *v11; // [sp+38h] [bp-20h]@4
  CPlayer *v12; // [sp+40h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v13; // [sp+60h] [bp+8h]@1
  char v14; // [sp+68h] [bp+10h]@1
  char *pLoadDataa; // [sp+70h] [bp+18h]@1

  pLoadDataa = pLoadData;
  v14 = byRet;
  v13 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pLoadData;
  v11 = CUnmannedTraderUserInfoTable::FindUser(v13, *(_WORD *)pLoadData, *((_DWORD *)pLoadData + 7));
  v12 = 0i64;
  if ( !CUnmannedTraderUserInfo::IsNull(v11) && (v12 = CUnmannedTraderUserInfo::FindOwner(v11)) != 0i64 && v12->m_bOper )
  {
    CUnmannedTraderUserInfo::ClearRequest(v11);
    CUnmannedTraderUserInfo::CompleteRegist(v11, v14, pLoadDataa, v13->m_pkLogger);
  }
  else if ( v14 )
  {
    LOBYTE(byType[0]) = 1;
    dwU = 0xFFFFFFF;
    CMainThread::Push_ChargeItem(&g_Main, *((_DWORD *)v10 + 7), 0xFFFFFFFF, *((_DWORD *)v10 + 20), 0xFFFFFFFu, 1);
    byType[0] = *((_DWORD *)v10 + 20);
    dwU = *((_DWORD *)v10 + 7);
    CUnmannedTraderUserInfoTable::Log(
      v13,
      "CUnmannedTraderUserInfoTable::CompleteRegist( BYTE byRet, char * pLoadData )\r\n"
      "\t\tdwRegistSerial(%u) dwOwnerSerial(%u)\r\n"
      "\t\tPush_ChargeItem( dwOwnerSerial(%u), Tax(%u) )!\r\n",
      *((_DWORD *)v10 + 5),
      *((_DWORD *)v10 + 7));
  }
  else
  {
    v5 = CUnmannedTraderGroupItemInfoTable::Instance();
    if ( !CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v5, v10[62], v10[63]) )
    {
      v6 = (unsigned __int8)v10[62];
      dwU = (unsigned __int8)v10[63];
      CLogFile::Write(
        v13->m_pkLogger,
        "CUnmannedTraderController::CompleteRegist(...)\r\n"
        "\t\tOwner : (%u)\r\n"
        "\t\tCUnmannedTraderGroupItemInfoTable::Instance()->IncreaseVersion(\r\n"
        "\t\tpkQuery->byClass1(%u), pkQuery->byClass2(%u) ) Fail!\r\n",
        *((_DWORD *)v10 + 7),
        v6);
    }
  }
}
