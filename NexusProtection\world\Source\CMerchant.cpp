#include "../Headers/CMerchant.h"
#include "../Headers/CCharacter.h"
#include <cstring>
#include <ctime>
#include <algorithm>
#include <cassert>
#include <iostream>

// External function declarations (these would be properly linked in the real implementation)
extern "C" {
    DWORD timeGetTime();
}

// Static member variable definitions
int CMerchant::s_nLiveNum = 0;
uint32_t CMerchant::s_dwSerialCounter = 1000;

/**
 * CMerchant Implementation
 */

CMerchant::CMerchant() {
    InitializeDefaults();
    LogOperation("Constructor", "CMerchant created");
}

CMerchant::~CMerchant() {
    if (m_bLive) {
        Destroy();
    }
    CleanupResources();
    LogOperation("Destructor", "CMerchant destroyed");
}

void CMerchant::InitializeDefaults() {
    m_bLive = false;
    m_dwObjSerial = 0;
    m_byRaceCode = 0;
    m_pItemStore = nullptr;
    m_dwLastDestroyTime = 0;
    m_dwCreateTime = CMerchantUtils::GetCurrentTime();
    m_dwLastUpdateTime = m_dwCreateTime;
}

void CMerchant::CleanupResources() {
    // Clean up any allocated resources
    // Note: m_pItemStore is typically managed externally, so we don't delete it here
    m_pItemStore = nullptr;
}

bool CMerchant::Create(_npc_create_setdata* pData) {
    try {
        if (!ValidateCreationData(pData)) {
            LogError("Invalid creation data", "Create");
            return false;
        }

        // Call parent class Create method
        if (!CCharacter::Create(reinterpret_cast<_character_create_setdata*>(&pData->m_pRecordSet))) {
            LogError("Failed to create base character", "Create");
            return false;
        }

        // Set merchant-specific properties
        m_pItemStore = pData->m_pLinkItemStore;
        m_dwObjSerial = GetNewMonSerial();
        m_byRaceCode = pData->m_byRaceCode;
        m_bLive = true;
        m_dwCreateTime = CMerchantUtils::GetCurrentTime();
        m_dwLastUpdateTime = m_dwCreateTime;

        // Send creation message
        SendMsg_Create();

        // Increment live merchant count
        ++s_nLiveNum;

        LogOperation("Create", "Merchant created successfully");
        return true;
    }
    catch (...) {
        LogError("Exception in Create method", "Create");
        return false;
    }
}

bool CMerchant::Destroy() {
    try {
        if (!m_bLive) {
            LogError("Attempting to destroy non-live merchant", "Destroy");
            return false;
        }

        // Send destruction message
        SendMsg_Destroy();

        // Mark as not live
        m_bLive = false;
        m_dwLastDestroyTime = CMerchantUtils::GetCurrentTime();

        // Decrement live merchant count
        if (s_nLiveNum > 0) {
            --s_nLiveNum;
        }

        // Clean up resources
        CleanupResources();

        LogOperation("Destroy", "Merchant destroyed successfully");
        return true;
    }
    catch (...) {
        LogError("Exception in Destroy method", "Destroy");
        return false;
    }
}

void CMerchant::Update() {
    try {
        if (!m_bLive) {
            return;
        }

        uint32_t currentTime = CMerchantUtils::GetCurrentTime();
        
        // Update timestamp
        m_dwLastUpdateTime = currentTime;

        // Perform merchant-specific updates here
        // This could include inventory updates, price changes, etc.

        LogOperation("Update", "Merchant updated");
    }
    catch (...) {
        LogError("Exception in Update method", "Update");
    }
}

uint32_t CMerchant::GetNewMonSerial() {
    return ++s_dwSerialCounter;
}

void CMerchant::SendMsg_Create() {
    try {
        // This would send a message to the game system about merchant creation
        // Implementation would depend on the specific messaging system
        LogOperation("SendMsg_Create", "Creation message sent");
    }
    catch (...) {
        LogError("Exception in SendMsg_Create", "SendMsg_Create");
    }
}

void CMerchant::SendMsg_Destroy() {
    try {
        // This would send a message to the game system about merchant destruction
        // Implementation would depend on the specific messaging system
        LogOperation("SendMsg_Destroy", "Destruction message sent");
    }
    catch (...) {
        LogError("Exception in SendMsg_Destroy", "SendMsg_Destroy");
    }
}

bool CMerchant::OnPlayerInteract(void* pPlayer) {
    try {
        if (!m_bLive || !pPlayer) {
            LogError("Invalid interaction parameters", "OnPlayerInteract");
            return false;
        }

        // Handle player interaction logic
        // This would typically open the merchant's shop
        return OpenShop(pPlayer);
    }
    catch (...) {
        LogError("Exception in OnPlayerInteract", "OnPlayerInteract");
        return false;
    }
}

bool CMerchant::OpenShop(void* pPlayer) {
    try {
        if (!m_bLive || !pPlayer || !m_pItemStore) {
            LogError("Cannot open shop - invalid state", "OpenShop");
            return false;
        }

        // Implementation would open the shop interface for the player
        LogOperation("OpenShop", "Shop opened for player");
        return true;
    }
    catch (...) {
        LogError("Exception in OpenShop", "OpenShop");
        return false;
    }
}

void CMerchant::CloseShop(void* pPlayer) {
    try {
        if (!pPlayer) {
            LogError("Invalid player parameter", "CloseShop");
            return;
        }

        // Implementation would close the shop interface for the player
        LogOperation("CloseShop", "Shop closed for player");
    }
    catch (...) {
        LogError("Exception in CloseShop", "CloseShop");
    }
}

bool CMerchant::ValidateData() const {
    // Validate merchant data integrity
    if (m_bLive && m_dwObjSerial == 0) {
        return false;
    }

    if (m_bLive && !m_pItemStore) {
        return false;
    }

    return true;
}

void CMerchant::Reset() {
    try {
        if (m_bLive) {
            Destroy();
        }
        
        InitializeDefaults();
        LogOperation("Reset", "Merchant reset to default state");
    }
    catch (...) {
        LogError("Exception in Reset", "Reset");
    }
}

bool CMerchant::CanDestroy() const {
    // Check if merchant can be safely destroyed
    // This might check for active transactions, etc.
    return m_bLive;
}

bool CMerchant::ValidateCreationData(const _npc_create_setdata* pData) const {
    if (!pData) {
        return false;
    }

    // Validate that required fields are set
    if (!pData->m_pLinkItemStore) {
        return false;
    }

    return true;
}

void CMerchant::LogOperation(const char* operation, const char* details) const {
    if (!operation) return;

    std::cout << "[CMerchant:" << m_dwObjSerial << "] " << operation;
    if (details) {
        std::cout << ": " << details;
    }
    std::cout << std::endl;
}

void CMerchant::LogError(const char* errorMessage, const char* context) const {
    if (!errorMessage) return;

    std::cerr << "[CMerchant ERROR:" << m_dwObjSerial << "]";
    if (context) {
        std::cerr << " [" << context << "]";
    }
    std::cerr << ": " << errorMessage << std::endl;
}

/**
 * CMerchantUtils namespace implementation
 */

namespace CMerchantUtils {

CMerchant* FindEmptyNPC(CMerchant* pList, int nMax) {
    try {
        if (!pList || nMax <= 0) {
            LogError("Invalid parameters", "FindEmptyNPC");
            return nullptr;
        }

        uint32_t currentTime = GetCurrentTime();
        
        for (int i = 0; i < nMax; ++i) {
            if (!pList[i].IsLive()) {
                // Check if enough time has passed since last destroy (30 seconds = 30000ms)
                if (HasEnoughTimePassed(pList[i].GetLastDestroyTime(), 30000)) {
                    LogOperation("FindEmptyNPC", "Found empty NPC slot");
                    return &pList[i];
                }
            }
        }

        LogOperation("FindEmptyNPC", "No empty NPC slots available");
        return nullptr;
    }
    catch (...) {
        LogError("Exception in FindEmptyNPC", "FindEmptyNPC");
        return nullptr;
    }
}

uint32_t GetCurrentTime() {
    return timeGetTime();
}

bool HasEnoughTimePassed(uint32_t dwLastDestroyTime, uint32_t dwMinInterval) {
    if (dwLastDestroyTime == 0) {
        return true; // Never destroyed before
    }

    uint32_t currentTime = GetCurrentTime();
    uint32_t timeDiff = currentTime - dwLastDestroyTime;
    
    return timeDiff > dwMinInterval;
}

void LogOperation(const char* operation, const char* details) {
    if (!operation) return;

    std::cout << "[CMerchantUtils] " << operation;
    if (details) {
        std::cout << ": " << details;
    }
    std::cout << std::endl;
}

void LogError(const char* errorMessage, const char* context) {
    if (!errorMessage) return;

    std::cerr << "[CMerchantUtils ERROR]";
    if (context) {
        std::cerr << " [" << context << "]";
    }
    std::cerr << ": " << errorMessage << std::endl;
}

} // namespace CMerchantUtils
