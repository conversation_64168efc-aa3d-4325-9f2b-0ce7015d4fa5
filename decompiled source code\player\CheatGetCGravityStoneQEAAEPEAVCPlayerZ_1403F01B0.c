/*
 * Function: ?CheatGet@CGravityStone@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403F01B0
 */

char __fastcall CGravityStone::CheatGet(CGravityStone *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CGravityStone *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return CGravityStone::Get(v6, v6->m_ObjID.m_wIndex, v6->m_dwObjSerial, pkPlayer);
}
