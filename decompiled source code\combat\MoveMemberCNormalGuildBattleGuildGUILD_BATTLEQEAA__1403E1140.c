/*
 * Function: ?MoveMember@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NHIPEAVCNormalGuildBattleField@2@AEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E1140
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::MoveMember(GUILD_BATTLE::CNormalGuildBattleGuild *this, int iMember, unsigned int uiID, GUILD_BATTLE::CNormalGuildBattleField *pkField, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  unsigned __int16 v8; // ax@11
  __int64 v9; // [sp+0h] [bp-88h]@1
  unsigned __int16 nLen[4]; // [sp+20h] [bp-68h]@20
  unsigned int v11; // [sp+28h] [bp-60h]@20
  char pbyType; // [sp+34h] [bp-54h]@11
  char v13; // [sp+35h] [bp-53h]@11
  char szMsg; // [sp+54h] [bp-34h]@11
  CPlayer *pkPlayer; // [sp+68h] [bp-20h]@12
  unsigned int v16; // [sp+70h] [bp-18h]@15
  char *v17; // [sp+78h] [bp-10h]@18
  GUILD_BATTLE::CNormalGuildBattleGuild *v18; // [sp+90h] [bp+8h]@1
  int v19; // [sp+98h] [bp+10h]@1
  unsigned int v20; // [sp+A0h] [bp+18h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v21; // [sp+A8h] [bp+20h]@1

  v21 = pkField;
  v20 = uiID;
  v19 = iMember;
  v18 = this;
  v5 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v18->m_pkGuild && pkField && iMember >= 0 )
  {
    if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v18->m_kMember[iMember]) )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEnableStart(&v18->m_kMember[v19]) )
      {
        GUILD_BATTLE::CNormalGuildBattleGuildMember::StockOldInfo(&v18->m_kMember[v19]);
        GUILD_BATTLE::CNormalGuildBattleGuildMember::SetBattleState(&v18->m_kMember[v19], 1, v18->m_byColorInx);
        pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v18->m_kMember[v19]);
        if ( GUILD_BATTLE::CNormalGuildBattleField::Start(v21, v18->m_byColorInx, pkPlayer) )
        {
          if ( pkPlayer )
            v16 = pkPlayer->m_dwObjSerial;
          else
            v16 = 0;
          if ( pkPlayer )
            v17 = CPlayerDB::GetCharNameW(&pkPlayer->m_Param);
          else
            v17 = "NULL";
          v11 = v16;
          *(_QWORD *)nLen = v17;
          GUILD_BATTLE::CNormalGuildBattleLogger::Log(
            kLogger,
            "CNormalGuildBattleGuild::MoveMember( iMember(%d), uiID(%u), pkField ) : %s(%u) Move Start Position!",
            (unsigned int)v19,
            v20);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        pbyType = 27;
        v13 = 66;
        szMsg = -1;
        v8 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v18->m_kMember[v19]);
        CNetProcess::LoadSendMsg(unk_1414F2088, v8, &pbyType, &szMsg, 1u);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
