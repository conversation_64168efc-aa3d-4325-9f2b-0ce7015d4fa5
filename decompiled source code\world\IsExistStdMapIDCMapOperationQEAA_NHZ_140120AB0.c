/*
 * Function: ?IsExistStdMapID@CMapOperation@@QEAA_NH@Z
 * Address: 0x140120AB0
 */

char __fastcall CMapOperation::IsExistStdMapID(CMapOperation *this, int iMapID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  unsigned __int64 v8; // [sp+28h] [bp-10h]@5
  CMapOperation *v9; // [sp+40h] [bp+8h]@1
  int v10; // [sp+48h] [bp+10h]@1

  v10 = iMapID;
  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; ; ++j )
  {
    v8 = j;
    v4 = std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::size(&v9->m_vecStandardMapCodeTable);
    if ( v8 >= v4 )
      break;
    if ( v10 == std::vector<std::pair<int,int>,std::allocator<std::pair<int,int>>>::operator[](
                  &v9->m_vecStandardMapCodeTable,
                  j)->first )
      return 1;
  }
  return 0;
}
