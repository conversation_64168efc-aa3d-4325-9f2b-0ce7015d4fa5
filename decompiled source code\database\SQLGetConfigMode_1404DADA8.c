/*
 * Function: SQLGetConfigMode
 * Address: 0x1404DADA8
 */

int __fastcall SQLGetConfigMode(unsigned __int16 *pwConfigMode)
{
  unsigned __int16 *v1; // rbx@1
  __int64 (__cdecl *v2)(); // rax@1
  int result; // eax@2

  v1 = pwConfigMode;
  v2 = ODBC___GetSetupProc("SQLGetConfigMode");
  if ( v2 )
    result = ((int (__fastcall *)(unsigned __int16 *))v2)(v1);
  else
    result = 0;
  return result;
}
