/*
 * Function: ?GuildBattleResultLogNotifyWeb@CNormalGuildBattle@GUILD_BATTLE@@IEAAXAEBV_qry_case_guild_battel_result_log@@@Z
 * Address: 0x1403E83E0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::GuildBattleResultLogNotifyWeb(GUILD_BATTLE::CNormalGuildBattle *this, _qry_case_guild_battel_result_log *Sheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-138h]@1
  char Dst; // [sp+40h] [bp-F8h]@4
  char v6; // [sp+51h] [bp-E7h]@4
  unsigned int v7; // [sp+62h] [bp-D6h]@4
  char v8; // [sp+66h] [bp-D2h]@4
  unsigned int v9; // [sp+77h] [bp-C1h]@4
  char v10; // [sp+7Bh] [bp-BDh]@4
  unsigned int v11; // [sp+8Ch] [bp-ACh]@4
  unsigned int v12; // [sp+90h] [bp-A8h]@4
  unsigned int v13; // [sp+94h] [bp-A4h]@4
  unsigned int v14; // [sp+98h] [bp-A0h]@4
  unsigned int v15; // [sp+9Ch] [bp-9Ch]@4
  unsigned int v16; // [sp+A0h] [bp-98h]@4
  unsigned int v17; // [sp+A4h] [bp-94h]@4
  unsigned int v18; // [sp+A8h] [bp-90h]@4
  char v19; // [sp+ACh] [bp-8Ch]@4
  unsigned int v20; // [sp+ADh] [bp-8Bh]@4
  char v21; // [sp+B1h] [bp-87h]@4
  unsigned int v22; // [sp+C2h] [bp-76h]@4
  char v23; // [sp+C6h] [bp-72h]@4
  char v24; // [sp+D7h] [bp-61h]@4
  unsigned int v25; // [sp+D8h] [bp-60h]@4
  char v26; // [sp+DCh] [bp-5Ch]@4
  char pbyType; // [sp+104h] [bp-34h]@4
  char v28; // [sp+105h] [bp-33h]@4
  unsigned __int64 v29; // [sp+120h] [bp-18h]@4
  _qry_case_guild_battel_result_log *Src; // [sp+148h] [bp+10h]@1

  Src = Sheet;
  v2 = &v4;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v29 = (unsigned __int64)&v4 ^ _security_cookie;
  strcpy_s(&Dst, 0x11ui64, Sheet->szStartTime);
  strcpy_s(&v6, 0x11ui64, Src->szEndTime);
  v7 = Src->dwRedSerial;
  strcpy_s(&v8, 0x11ui64, Src->wszRedName);
  v9 = Src->dwBlueSerial;
  strcpy_s(&v10, 0x11ui64, Src->wszBlueName);
  v11 = Src->dwRedScore;
  v12 = Src->dwBlueScore;
  v13 = Src->dwRedMaxJoinCnt;
  v14 = Src->dwBlueMaxJoinCnt;
  v15 = Src->dwRedGoalCnt;
  v16 = Src->dwBlueGoalCnt;
  v17 = Src->dwRedKillCntSum;
  v18 = Src->dwBlueKillCntSum;
  v19 = Src->byBattleResult;
  v20 = Src->dwMaxGoalCharacSerial;
  strcpy_s(&v21, 0x11ui64, Src->wszMaxGoalCharacName);
  v22 = Src->dwMaxKillCharacSerial;
  strcpy_s(&v23, 0x11ui64, Src->wszMaxKillCharacName);
  v24 = Src->byJoinLimit;
  v25 = Src->dwGuildBattleCostGold;
  strcpy_s(&v26, 0xCui64, Src->szBattleMapCode);
  pbyType = 51;
  v28 = 18;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, &Dst, 0xA8u);
}
