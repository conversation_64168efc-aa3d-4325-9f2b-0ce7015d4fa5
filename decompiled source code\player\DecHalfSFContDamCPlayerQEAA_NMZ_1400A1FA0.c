/*
 * Function: ?DecHalfSFContDam@CPlayer@@QEAA_NM@Z
 * Address: 0x1400A1FA0
 */

bool __fastcall CPlayer::DecHalfSFContDam(CPlayer *this, float fEffVal)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned __int16 wNewDur; // [sp+20h] [bp-48h]@16
  unsigned int v7; // [sp+30h] [bp-38h]@4
  int v8; // [sp+34h] [bp-34h]@4
  int v9; // [sp+38h] [bp-30h]@4
  int j; // [sp+3Ch] [bp-2Ch]@4
  _sf_continous (*v11)[8]; // [sp+40h] [bp-28h]@7
  _base_fld *v12; // [sp+48h] [bp-20h]@8
  unsigned int v13; // [sp+50h] [bp-18h]@12
  unsigned int v14; // [sp+54h] [bp-14h]@13
  CPlayer *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = _sf_continous::GetSFContCurTime();
  v8 = 0;
  v9 = 0;
  for ( j = 0; j < 8; ++j )
  {
    v11 = (_sf_continous (*)[8])((char *)v15->m_SFCont + 48 * j);
    if ( v11 )
    {
      v12 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
      if ( !v12 || !v15->m_bAfterEffect || (*v11)[0].m_byEffectCode != 3 || (*v11)[0].m_wEffectIndex != v12->m_dwIndex )
      {
        v13 = v7 - (*v11)[0].m_dwStartSec;
        if ( fEffVal <= 0.0 )
          v14 = ((signed int)(*v11)[0].m_wDurSec >> 1) - v13;
        else
          v14 = (signed int)ffloor((float)(*v11)[0].m_wDurSec * (float)(1.0 - fEffVal)) - v13;
        if ( (signed int)v14 <= 0 )
        {
          CCharacter::RemoveSFContEffect((CCharacter *)&v15->vfptr, 0, j, 0, 0);
        }
        else
        {
          wNewDur = (signed int)(*v11)[0].m_wDurSec >> 1;
          CCharacter::AlterContDurSec((CCharacter *)&v15->vfptr, 0, j, (*v11)[0].m_dwStartSec, wNewDur);
          ++v9;
        }
        ++v8;
      }
    }
  }
  if ( v9 > 0 )
    CPlayer::SendMsg_AlterContEffectTime(v15, 0);
  return v8 > 0;
}
