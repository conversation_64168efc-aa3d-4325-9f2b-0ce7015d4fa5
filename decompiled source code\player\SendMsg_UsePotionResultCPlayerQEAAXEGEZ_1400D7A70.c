/*
 * Function: ?SendMsg_UsePotionResult@CPlayer@@QEAAXEGE@Z
 * Address: 0x1400D7A70
 */

void __fastcall CPlayer::SendMsg_UsePotionResult(CPlayer *this, char byErrCode, unsigned __int16 wSerial, char byLeftNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v8; // [sp+39h] [bp-4Fh]@4
  __int16 v9; // [sp+3Bh] [bp-4Dh]@4
  __int16 v10; // [sp+3Dh] [bp-4Bh]@4
  __int16 v11; // [sp+3Fh] [bp-49h]@4
  char v12; // [sp+41h] [bp-47h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v14; // [sp+65h] [bp-23h]@4
  CPlayer *v15; // [sp+90h] [bp+8h]@1
  char v16; // [sp+A8h] [bp+20h]@1

  v16 = byLeftNum;
  v15 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  v8 = wSerial;
  v9 = CPlayerDB::GetHP(&v15->m_Param);
  v10 = CPlayerDB::GetFP(&v15->m_Param);
  v11 = CPlayerDB::GetSP(&v15->m_Param);
  v12 = v16;
  pbyType = 7;
  v14 = 8;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xAu);
}
