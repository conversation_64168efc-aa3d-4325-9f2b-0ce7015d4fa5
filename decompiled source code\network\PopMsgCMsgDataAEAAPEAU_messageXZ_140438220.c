/*
 * Function: ?PopMsg@CMsgData@@AEAAPEAU_message@@XZ
 * Address: 0x140438220
 */

_message *__fastcall CMsgData::PopMsg(CMsgData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _message *result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  _message *v5; // [sp+20h] [bp-18h]@4
  CMsgData *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMyCriticalSection::Lock(&v6->m_csList);
  v5 = v6->m_gmListHead.pNext;
  if ( v5 == &v6->m_gmListTail )
  {
    CMyCriticalSection::Unlock(&v6->m_csList);
    result = 0i64;
  }
  else
  {
    v6->m_gmListHead.pNext = v5->pNext;
    v5->pNext->pPrev = &v6->m_gmListHead;
    CMyCriticalSection::Unlock(&v6->m_csList);
    result = v5;
  }
  return result;
}
