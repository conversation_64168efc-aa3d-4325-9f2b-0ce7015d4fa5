/*
 * Function: ?_LoadResource@CMapData@@AEAA_NPEAD@Z
 * Address: 0x1401835B0
 */

char __fastcall CMapData::_LoadResource(CMapData *this, char *pszMapCode)
{
  __int64 *v2; // rdi@1
  signed __int64 j; // rcx@1
  char result; // al@5
  int v5; // eax@16
  int v6; // eax@16
  int v7; // eax@22
  int v8; // eax@25
  int v9; // eax@28
  __int64 v10; // [sp+0h] [bp-128h]@1
  char Dest; // [sp+30h] [bp-F8h]@4
  int v12; // [sp+B4h] [bp-74h]@21
  int i; // [sp+B8h] [bp-70h]@21
  _dummy_position *pDumPos; // [sp+C0h] [bp-68h]@23
  _dummy_position *pPos; // [sp+C8h] [bp-60h]@26
  _dummy_position *v16; // [sp+D0h] [bp-58h]@29
  int __n[2]; // [sp+E0h] [bp-48h]@18
  void *v18; // [sp+E8h] [bp-40h]@21
  void *__t; // [sp+F0h] [bp-38h]@18
  __int64 v20; // [sp+F8h] [bp-30h]@4
  int v21; // [sp+100h] [bp-28h]@16
  int v22; // [sp+104h] [bp-24h]@16
  void *v23; // [sp+108h] [bp-20h]@19
  unsigned __int64 v24; // [sp+110h] [bp-18h]@4
  CMapData *v25; // [sp+130h] [bp+8h]@1

  v25 = this;
  v2 = &v10;
  for ( j = 72i64; j; --j )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = -2i64;
  v24 = (unsigned __int64)&v10 ^ _security_cookie;
  sprintf(&Dest, ".\\map\\%s\\%s.spt", pszMapCode, pszMapCode);
  if ( CDummyPosTable::LoadDummyPosition(&v25->m_tbResDumPosHigh, &Dest, "*0dr") )
  {
    if ( CMapData::ConvertLocalToWorldDummy(v25, &v25->m_tbResDumPosHigh, 0) )
    {
      if ( CDummyPosTable::LoadDummyPosition(&v25->m_tbResDumPosMiddle, &Dest, "*1dr") )
      {
        if ( CMapData::ConvertLocalToWorldDummy(v25, &v25->m_tbResDumPosMiddle, 0) )
        {
          if ( CDummyPosTable::LoadDummyPosition(&v25->m_tbResDumPosLow, &Dest, "*2dr") )
          {
            if ( CMapData::ConvertLocalToWorldDummy(v25, &v25->m_tbResDumPosLow, 0) )
            {
              v21 = CDummyPosTable::GetRecordNum(&v25->m_tbResDumPosHigh);
              v5 = CDummyPosTable::GetRecordNum(&v25->m_tbResDumPosMiddle);
              v22 = v5 + v21;
              v6 = CDummyPosTable::GetRecordNum(&v25->m_tbResDumPosLow);
              v25->m_nResDumNum = v6 + v22;
              if ( v25->m_nResDumNum )
              {
                *(_QWORD *)__n = v25->m_nResDumNum;
                __t = operator new[](saturated_mul(0x70ui64, *(unsigned __int64 *)__n));
                if ( __t )
                {
                  `vector constructor iterator'(
                    __t,
                    0x70ui64,
                    __n[0],
                    (void *(__cdecl *)(void *))_res_dummy::_res_dummy);
                  v23 = __t;
                }
                else
                {
                  v23 = 0i64;
                }
                v18 = v23;
                v25->m_pResDummy = (_res_dummy *)v23;
                v12 = 0;
                for ( i = 0; ; ++i )
                {
                  v7 = CDummyPosTable::GetRecordNum(&v25->m_tbResDumPosHigh);
                  if ( i >= v7 )
                    break;
                  pDumPos = CDummyPosTable::GetRecord(&v25->m_tbResDumPosHigh, i);
                  _res_dummy::SetDummy(&v25->m_pResDummy[v12++], pDumPos, 0);
                  CMapData::CheckCenterPosDummy(v25, pDumPos);
                }
                for ( i = 0; ; ++i )
                {
                  v8 = CDummyPosTable::GetRecordNum(&v25->m_tbResDumPosMiddle);
                  if ( i >= v8 )
                    break;
                  pPos = CDummyPosTable::GetRecord(&v25->m_tbResDumPosMiddle, i);
                  _res_dummy::SetDummy(&v25->m_pResDummy[v12++], pPos, 1);
                  CMapData::CheckCenterPosDummy(v25, pPos);
                }
                for ( i = 0; ; ++i )
                {
                  v9 = CDummyPosTable::GetRecordNum(&v25->m_tbResDumPosLow);
                  if ( i >= v9 )
                    break;
                  v16 = CDummyPosTable::GetRecord(&v25->m_tbResDumPosLow, i);
                  _res_dummy::SetDummy(&v25->m_pResDummy[v12++], v16, 2);
                  CMapData::CheckCenterPosDummy(v25, v16);
                }
                result = 1;
              }
              else
              {
                result = 1;
              }
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            MyMessageBox("CMapData Error", "m_tbResDumPosLow.LoadDummyPosition(%s) == false", &Dest);
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        MyMessageBox("CMapData Error", "m_tbResDumPosMiddle.LoadDummyPosition(%s) == false", &Dest);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("CMapData Error", "m_tbResDumPosHigh.LoadDummyPosition(%s) == false", &Dest);
    result = 0;
  }
  return result;
}
