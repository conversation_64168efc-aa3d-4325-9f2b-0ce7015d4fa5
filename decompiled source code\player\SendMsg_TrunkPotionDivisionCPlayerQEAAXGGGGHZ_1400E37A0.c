/*
 * Function: ?SendMsg_TrunkPotionDivision@CPlayer@@QEAAXGGGGH@Z
 * Address: 0x1400E37A0
 */

void __fastcall CPlayer::SendMsg_TrunkPotionDivision(CPlayer *this, unsigned __int16 wSerial, unsigned __int16 wParentAmount, unsigned __int16 wChildSerial, unsigned __int16 wChildAmount, int nRet)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v10; // [sp+39h] [bp-4Fh]@4
  unsigned __int16 v11; // [sp+3Bh] [bp-4Dh]@4
  unsigned __int16 v12; // [sp+3Dh] [bp-4Bh]@4
  unsigned __int16 v13; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v15; // [sp+65h] [bp-23h]@4
  CPlayer *v16; // [sp+90h] [bp+8h]@1

  v16 = this;
  v6 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = wSerial;
  v11 = wParentAmount;
  v12 = wChildSerial;
  v13 = wChildAmount;
  szMsg = nRet;
  pbyType = 34;
  v15 = 13;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
