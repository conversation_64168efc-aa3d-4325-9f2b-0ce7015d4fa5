/*
 * Function: ?Send_IP@CChiNetworkEX@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14040FFC0
 */

void __fastcall CChiNetworkEX::Send_IP(CChiNetworkEX *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _apex_id *v4; // rax@4
  char *v5; // rax@4
  CAsyncLogger *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-68h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-48h]@4
  _apex_send_ip v9; // [sp+34h] [bp-34h]@4
  _apex_id v10; // [sp+44h] [bp-24h]@4
  unsigned __int16 v11[2]; // [sp+48h] [bp-20h]@4
  CUserDB *v12; // [sp+50h] [bp-18h]@4
  CUserDB *v13; // [sp+58h] [bp-10h]@4
  CChiNetworkEX *v14; // [sp+70h] [bp+8h]@1
  CPlayer *v15; // [sp+78h] [bp+10h]@1

  v15 = pOne;
  v14 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _apex_send_ip::_apex_send_ip(&v9);
  v9.m_dwIp = v15->m_pUserDB->m_dwIP;
  *(_DWORD *)v11 = _apex_send_ip::size(&v9);
  v12 = v15->m_pUserDB;
  _apex_id::_apex_id(&v10, 83);
  v5 = _apex_id::operator&(v4);
  nLen = v11[0];
  CChiNetworkEX::Send(v14, v5, v12->m_dwAccountSerial, &v9.m_byOnce, v11[0]);
  v13 = v15->m_pUserDB;
  v6 = CAsyncLogger::Instance();
  CAsyncLogger::FormatLog(v6, 12, "Send_IP - %d", v13->m_dwAccountSerial);
}
