/*
 * Function: ??$_Construct@VCUnmannedTraderSchedule@@V1@@std@@YAXPEAVCUnmannedTraderSchedule@@AEBV1@@Z
 * Address: 0x1403972C0
 */

void __fastcall std::_Construct<CUnmannedTraderSchedule,CUnmannedTraderSchedule>(CUnmannedTraderSchedule *_Ptr, CUnmannedTraderSchedule *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  void *_Where; // [sp+20h] [bp-58h]@4
  char *v6; // [sp+28h] [bp-50h]@4
  char v7; // [sp+30h] [bp-48h]@5
  CUnmannedTraderSchedule *v8; // [sp+80h] [bp+8h]@1
  CUnmannedTraderSchedule *v9; // [sp+88h] [bp+10h]@1

  v9 = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 26i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _Where = v8;
  v6 = (char *)operator new(0x20ui64, v8);
  if ( v6 )
  {
    qmemcpy(&v7, v9, 0x20ui64);
    qmemcpy(v6, &v7, 0x20ui64);
  }
}
