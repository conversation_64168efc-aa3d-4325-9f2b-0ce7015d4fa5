/*
 * Function: ?Select_BossCryMsg@CRFWorldDatabase@@QEAAEKPEAU_worlddb_crymsg_info@@@Z
 * Address: 0x1404BB3E0
 */

char __fastcall CRFWorldDatabase::Select_BossCryMsg(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_crymsg_info *pCryMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@14
  __int64 v6; // [sp+0h] [bp-3A8h]@1
  void *SQLStmt; // [sp+20h] [bp-388h]@19
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-380h]@30
  SQLLEN v9; // [sp+38h] [bp-370h]@30
  __int16 v10; // [sp+44h] [bp-364h]@15
  char Dest; // [sp+60h] [bp-348h]@4
  char Source; // [sp+280h] [bp-128h]@7
  int j; // [sp+384h] [bp-24h]@4
  int v14; // [sp+388h] [bp-20h]@4
  char v15; // [sp+38Ch] [bp-1Ch]@22
  unsigned __int64 v16; // [sp+398h] [bp-10h]@4
  CRFWorldDatabase *v17; // [sp+3B0h] [bp+8h]@1
  unsigned int v18; // [sp+3B8h] [bp+10h]@1
  _worlddb_crymsg_info *v19; // [sp+3C0h] [bp+18h]@1

  v19 = pCryMsg;
  v18 = dwSerial;
  v17 = this;
  v3 = &v6;
  for ( i = 232i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v6 ^ _security_cookie;
  j = 0;
  v14 = 0;
  sprintf(&Dest, "select ");
  for ( j = 0; j < 10; ++j )
  {
    if ( j == 9 )
      sprintf(&Source, "CryMsg%d ", (unsigned int)(j + 1));
    else
      sprintf(&Source, "CryMsg%d, ", (unsigned int)(j + 1));
    strcat_0(&Dest, &Source);
  }
  sprintf(&Source, "from tbl_CryMsg where Serial = %d", v18);
  strcat_0(&Dest, &Source);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v10 = SQLExecDirect_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v15 = 0;
        if ( v10 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
          v15 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v15;
      }
      else
      {
        for ( j = 0; j < 10; ++j )
        {
          ++v14;
          StrLen_or_IndPtr = &v9;
          SQLStmt = (void *)65;
          v10 = SQLGetData_0(v17->m_hStmtSelect, v14, 1, (char *)v19 + 65 * j, 65i64, &v9);
          if ( v10 )
          {
            if ( v10 != 1 )
              break;
          }
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        if ( v17->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
