/*
 * Function: ?Update_Bind@CUserDB@@QEAA_NPEAD0_N@Z
 * Address: 0x1401162F0
 */

char __fastcall CUserDB::Update_Bind(CUserDB *this, char *pszMapCode, char *pDummyCode, bool bUpdate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1
  const char *Str; // [sp+38h] [bp+10h]@1
  const char *Source; // [sp+40h] [bp+18h]@1

  Source = pDummyCode;
  Str = pszMapCode;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( strlen_0(pszMapCode) <= 0xB )
  {
    if ( strlen_0(Source) <= 0xB )
    {
      strcpy_0(v8->m_AvatorData.dbAvator.m_szBindMapCode, Str);
      strcpy_0(v8->m_AvatorData.dbAvator.m_szBindDummy, Source);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
