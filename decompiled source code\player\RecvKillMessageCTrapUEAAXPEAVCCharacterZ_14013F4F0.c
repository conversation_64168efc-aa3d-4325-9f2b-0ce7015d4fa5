/*
 * Function: ?RecvKillMessage@CTrap@@UEAAXPEAVCCharacter@@@Z
 * Address: 0x14013F4F0
 */

void __usercall CTrap::RecvKillMessage(CTrap *this@<rcx>, CCharacter *pDier@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  double v5; // xmm0_8@12
  int v6; // eax@18
  __int64 v7; // [sp+0h] [bp-98h]@1
  char *pQryData; // [sp+20h] [bp-78h]@18
  int nSize[2]; // [sp+28h] [bp-70h]@18
  double v10; // [sp+30h] [bp-68h]@18
  double v11; // [sp+40h] [bp-58h]@12
  double v12; // [sp+48h] [bp-50h]@12
  double v13; // [sp+50h] [bp-48h]@12
  double v14; // [sp+58h] [bp-40h]@12
  _qry_case_addpvppoint v15; // [sp+68h] [bp-30h]@18
  CTrap *v16; // [sp+A0h] [bp+8h]@1
  CPlayer *v17; // [sp+A8h] [bp+10h]@1

  v17 = (CPlayer *)pDier;
  v16 = this;
  v3 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v16->m_pMaster )
  {
    if ( v16->m_pMaster->m_bLive && v16->m_pMaster->m_bOper && v16->m_pMaster->m_dwObjSerial == v16->m_dwMasterSerial )
      ((void (__fastcall *)(CPlayer *))v16->m_pMaster->vfptr->RecvKillMessage)(v16->m_pMaster);
  }
  else if ( !pDier->m_ObjID.m_byID && v16->m_dwMasterSerial != -1 )
  {
    CPlayerDB::GetPvPPoint((CPlayerDB *)&pDier[1].m_fOldPos[2]);
    v11 = a3 + 10000.0;
    v12 = v16->m_dMasterPvPPoint + 10000.0;
    v13 = (a3 + 10000.0) / v12;
    v5 = v13 * 500.0 + 0.5;
    v14 = v13 * 500.0 + 0.5;
    CPlayerDB::GetPvPPoint(&v17->m_Param);
    if ( v14 > v5 )
    {
      CPlayerDB::GetPvPPoint(&v17->m_Param);
      v14 = v5;
    }
    if ( v14 < 1.0 )
      v14 = DOUBLE_1_0;
    if ( v14 > 100000000.0 )
      v14 = DOUBLE_1_0e8;
    v15.dwSerial = v16->m_dwMasterSerial;
    v15.dwPoint = (signed int)floor(v14);
    v15.dwCashBag = (signed int)floor(v14);
    v6 = _qry_case_addpvppoint::size(&v15);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 13, (char *)&v15, v6);
    v10 = v16->m_dMasterPvPPoint + v14;
    *(double *)nSize = v14;
    LODWORD(pQryData) = v17->m_dwObjSerial;
    CLogFile::Write(
      &stru_1799C9940,
      "%s [ %d ] DST: [ %d ] type: logoff_inc  >> pvp : %.0f  last: %.0f",
      v16->m_aszMasterName,
      v16->m_dwMasterSerial);
    CPlayer::AlterPvPPoint(v17, -0.0 - v14, die_dec, v16->m_dwMasterSerial);
  }
}
