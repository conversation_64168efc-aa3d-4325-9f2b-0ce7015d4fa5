/*
 * Function: ?empty@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBA_NXZ
 * Address: 0x14038F1E0
 */

bool __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(v5) == 0;
}
