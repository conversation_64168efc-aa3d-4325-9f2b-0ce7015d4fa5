/*
 * Function: ?dev_up_mastery@CPlayer@@QEAA_NHHH@Z
 * Address: 0x1400BBBB0
 */

char __fastcall CPlayer::dev_up_mastery(CPlayer *this, int nMasteryCode, int nMasteryIndex, int nLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  float v7; // xmm0_4@15
  float v8; // xmm0_4@17
  float v9; // xmm0_4@19
  float v10; // xmm0_4@34
  float v11; // xmm0_4@36
  float v12; // xmm0_4@38
  __int64 v13; // [sp+0h] [bp-58h]@1
  unsigned int dwNewData; // [sp+20h] [bp-38h]@14
  unsigned int dwStatIndex; // [sp+24h] [bp-34h]@41
  unsigned int dwNewStat; // [sp+28h] [bp-30h]@42
  int j; // [sp+2Ch] [bp-2Ch]@42
  int v18; // [sp+30h] [bp-28h]@44
  unsigned int v19; // [sp+34h] [bp-24h]@45
  float v20; // [sp+38h] [bp-20h]@15
  float v21; // [sp+3Ch] [bp-1Ch]@17
  float v22; // [sp+40h] [bp-18h]@19
  float v23; // [sp+44h] [bp-14h]@38
  __int64 v24; // [sp+48h] [bp-10h]@42
  CPlayer *v25; // [sp+60h] [bp+8h]@1
  int v26; // [sp+68h] [bp+10h]@1
  int v27; // [sp+70h] [bp+18h]@1
  int v28; // [sp+78h] [bp+20h]@1

  v28 = nLv;
  v27 = nMasteryIndex;
  v26 = nMasteryCode;
  v25 = this;
  v4 = &v13;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( nMasteryCode == 3 )
  {
    if ( nMasteryIndex >= 8 )
      return 0;
  }
  else if ( !_STAT_DB_BASE::IsRangePerMastery(nMasteryCode, nMasteryIndex) )
  {
    return 0;
  }
  if ( v28 <= 99 )
  {
    if ( v28 >= 1 )
    {
      dwNewData = 0;
      if ( v26 )
      {
        switch ( v26 )
        {
          case 1:
            pow(1000.0, 2);
            v21 = FLOAT_1000_0;
            pow((float)v28, 2);
            v8 = v21 + (float)((float)(4.0 * (float)v28) * 1000.0);
            sqrt(v8);
            pow((float)(v8 + -1000.0) / 2.0, 2);
            dwNewData = (signed int)ffloor((float)(v8 + -1000.0) / 2.0);
            break;
          case 2:
            pow(1000.0, 2);
            v22 = FLOAT_1000_0;
            pow((float)v28, 2);
            v9 = v22 + (float)((float)(4.0 * (float)v28) * 1000.0);
            sqrt(v9);
            pow((float)(v9 + -1000.0) / 2.0, 2);
            dwNewData = (signed int)ffloor((float)(v9 + -1000.0) / 2.0);
            break;
          case 4:
            pow((float)v28, 2);
            pow((float)v28, 2);
            dwNewData = (signed int)ffloor((float)(CalcRoundUp((float)v28 / 14.0) - 1) + 0.0099999998);
            break;
          case 3:
            pow((float)v28, 2);
            pow((float)v28, 2);
            dwNewData = (signed int)ffloor((float)(CalcRoundUp((float)v28 / 10.0) - 1) + 0.0099999998);
            break;
          case 5:
            if ( v27 )
            {
              if ( v27 == 1 )
              {
                pow((float)v28, 2);
                dwNewData = (signed int)ffloor((float)((float)((float)((float)v28 - 1.0) / 3.0) * 1.1) + 0.89999998);
              }
              else if ( v27 == 2 )
              {
                pow((float)v28, 2);
                dwNewData = (signed int)ffloor((float)((float)((float)((float)v28 - 1.0) / 3.0) * 10.0) + 0.89999998);
              }
            }
            else
            {
              pow((float)v28, 2);
              dwNewData = (signed int)ffloor((float)((float)((float)((float)v28 - 1.0) / 3.0) * 1.1) + 0.89999998);
            }
            break;
          case 6:
            if ( CPlayerDB::GetRaceCode(&v25->m_Param) )
            {
              if ( CPlayerDB::GetRaceCode(&v25->m_Param) == 1 )
              {
                v11 = (float)v28 - 1.0;
                pow(v11, 2);
                dwNewData = (signed int)ffloor(v11 * 15000.0);
              }
              else if ( CPlayerDB::GetRaceCode(&v25->m_Param) == 2 )
              {
                pow(1000.0, 2);
                v23 = FLOAT_1000_0;
                pow((float)v28, 2);
                v12 = v23 + (float)((float)(4.0 * (float)v28) * 1000.0);
                sqrt(v12);
                pow((float)(v12 + -1000.0) / 2.0, 2);
                dwNewData = (signed int)ffloor((float)(v12 + -1000.0) / 2.0);
              }
            }
            else
            {
              v10 = (float)v28 - 1.0;
              pow(v10, 2);
              dwNewData = (signed int)ffloor(v10 * 15000.0);
            }
            break;
        }
      }
      else
      {
        pow(1000.0, 2);
        v20 = FLOAT_1000_0;
        pow((float)v28, 2);
        v7 = v20 + (float)((float)(4.0 * (float)v28) * 1000.0);
        sqrt(v7);
        pow((float)(v7 + -1000.0) / 2.0, 2);
        dwNewData = (signed int)ffloor((float)(v7 + -1000.0) / 2.0);
      }
      if ( dwNewData )
      {
        if ( v26 == 3 )
        {
          v24 = 196i64 * v27;
          dwNewStat = dwNewData / CPlayer::s_SkillIndexPerMastery[49 * v27] + 1;
          for ( j = 0; j < CPlayer::s_SkillIndexPerMastery[49 * v27]; ++j )
          {
            v18 = *(&CPlayer::s_SkillIndexPerMastery[49 * v27 + 1] + j);
            if ( dwNewStat )
            {
              v19 = _STAT_DB_BASE::GetStatIndex(3, v18);
              CPlayer::Emb_UpdateStat(v25, v19, dwNewStat, 0);
              CPlayer::SendMsg_StatInform(v25, v19, dwNewStat, 0);
            }
          }
        }
        else
        {
          dwStatIndex = _STAT_DB_BASE::GetStatIndex(v26, v27);
          CPlayer::Emb_UpdateStat(v25, dwStatIndex, dwNewData, 0);
          CPlayer::SendMsg_StatInform(v25, dwStatIndex, dwNewData, 0);
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
