/*
 * Function: ?GetRandPosInDummy@CMapData@@QEAA_NPEAU_dummy_position@@PEAM_N@Z
 * Address: 0x1401857A0
 */

bool __fastcall CMapData::GetRandPosInDummy(CMapData *this, _dummy_position *pPos, float *pNewPos, bool bRePos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // kr00_8@9
  __int64 v8; // [sp+0h] [bp-98h]@1
  float v9; // [sp+28h] [bp-70h]@9
  float v10; // [sp+2Ch] [bp-6Ch]@9
  float v11; // [sp+30h] [bp-68h]@9
  int j; // [sp+44h] [bp-54h]@6
  float v13[3]; // [sp+58h] [bp-40h]@12
  int v14; // [sp+74h] [bp-24h]@12
  CLevel *v15; // [sp+78h] [bp-20h]@11
  CLevel *v16; // [sp+80h] [bp-18h]@14
  CMapData *v17; // [sp+A0h] [bp+8h]@1
  _dummy_position *v18; // [sp+A8h] [bp+10h]@1
  float *Dst; // [sp+B0h] [bp+18h]@1
  bool v20; // [sp+B8h] [bp+20h]@1

  v20 = bRePos;
  Dst = pNewPos;
  v18 = pPos;
  v17 = this;
  v4 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pPos->m_bPosAble )
  {
    for ( j = 0; j < 3; ++j )
    {
      v9 = (float)(v18->m_zLocalMin[0] + rand() % (v18->m_zLocalMax[0] - v18->m_zLocalMin[0]));
      v11 = (float)(v18->m_zLocalMin[2] + rand() % (v18->m_zLocalMax[2] - v18->m_zLocalMin[2]));
      v7 = v18->m_zLocalMin[1] + (signed int)v18->m_zLocalMax[1];
      v10 = (float)(((signed int)v7 - HIDWORD(v7)) >> 1);
      if ( !CExtDummy::GetWorldFromLocal(&v17->m_Dummy, (float (*)[3])Dst, v18->m_wLineIndex, &v9) )
      {
        MyMessageBox("CMapData Error", "GetRandPosInDummy map:%s, dummy:%s", v17->m_pMapSet->m_strCode, v18);
        return 0;
      }
      v15 = &v17->m_Level;
      Dst[1] = CLevel::GetFirstYpos(&v17->m_Level, Dst, v18->m_fMin, v18->m_fMax);
      if ( Dst[1] != -65535.0 )
      {
        v14 = *((_DWORD *)Dst + 1);
        Dst[1] = v18->m_fCenterPos[1];
        if ( (unsigned int)CBsp::CanYouGoThere(v17->m_Level.mBsp, v18->m_fCenterPos, Dst, (float (*)[3])v13)
          || v20
          && (*Dst = v13[0],
              Dst[2] = v13[2],
              v16 = &v17->m_Level,
              Dst[1] = CLevel::GetFirstYpos(&v17->m_Level, Dst, v18->m_fMin, v18->m_fMax),
              Dst[1] != -65535.0) )
        {
          *((_DWORD *)Dst + 1) = v14;
          return 1;
        }
      }
    }
    memcpy_0(Dst, v18->m_fCenterPos, 0xCui64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
