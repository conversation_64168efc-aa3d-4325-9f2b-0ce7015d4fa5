/*
 * Function: ?CheckDiff@CCheckSumCharacAccountTrunkData@@QEAAHPEAVCRFWorldDatabase@@PEADAEAV1@@Z
 * Address: 0x1402C08E0
 */

signed __int64 __fastcall CCheckSumCharacAccountTrunkData::CheckDiff(CCheckSumCharacAccountTrunkData *this, CRFWorldDatabase *pkDB, char *wszName, CCheckSumCharacAccountTrunkData *kSrcValue)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  unsigned int v7; // eax@10
  long double v8; // xmm0_8@15
  __int64 v9; // [sp+0h] [bp-78h]@1
  unsigned int dwOrgValue[2]; // [sp+20h] [bp-58h]@10
  unsigned int dwChgValue[2]; // [sp+28h] [bp-50h]@10
  char v12; // [sp+30h] [bp-48h]@7
  char v13; // [sp+44h] [bp-34h]@10
  int j; // [sp+54h] [bp-24h]@7
  int k; // [sp+58h] [bp-20h]@13
  unsigned int v16; // [sp+5Ch] [bp-1Ch]@10
  long double v17; // [sp+60h] [bp-18h]@16
  CCheckSumCharacAccountTrunkData *v18; // [sp+80h] [bp+8h]@1
  CRFWorldDatabase *v19; // [sp+88h] [bp+10h]@1
  char *pwszName; // [sp+90h] [bp+18h]@1
  CCheckSumCharacAccountTrunkData *v21; // [sp+98h] [bp+20h]@1

  v21 = kSrcValue;
  pwszName = wszName;
  v19 = pkDB;
  v18 = this;
  v4 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkDB && wszName )
  {
    v12 = 1;
    for ( j = 0; j < 6; ++j )
    {
      if ( v18->m_dwValues[j] != v21->m_dwValues[j] )
      {
        v12 = 0;
        v16 = CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v13, j, v18->m_dwSerial, v21->m_dwValues[j]);
        v7 = CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v13, j, v18->m_dwSerial, v18->m_dwValues[j]);
        dwChgValue[0] = v16;
        dwOrgValue[0] = v7;
        if ( !CRFWorldDatabase::Insert_NpcLog(v19, v18->m_dwSerial, pwszName, j, v7, v16) )
          return 0xFFFFFFFFi64;
      }
    }
    for ( k = 0; k < 2; ++k )
    {
      v8 = v18->m_dValues[k];
      if ( v8 != v21->m_dValues[k] )
      {
        v12 = 0;
        CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v13, k, v18->m_dwAccountSerial, v21->m_dValues[k]);
        v17 = v8;
        CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v13, k, v18->m_dwAccountSerial, v18->m_dValues[k]);
        *(long double *)dwChgValue = v17;
        *(long double *)dwOrgValue = v8;
        if ( !CRFWorldDatabase::Insert_AnimusLog(v19, v18->m_dwAccountSerial, pwszName, k, v8, v17) )
          return 0xFFFFFFFFi64;
      }
    }
    result = v12 == 0;
  }
  else
  {
    result = 1i64;
  }
  return result;
}
