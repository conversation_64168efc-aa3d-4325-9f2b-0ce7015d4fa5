/*
 * Function: ?_DeleteUnit<PERSON><PERSON>@CPlayer@@QEAAGE@Z
 * Address: 0x1401067D0
 */

unsigned __int16 __fastcall CPlayer::_DeleteUnitKey(CPlayer *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 result; // ax@10
  __int64 v5; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  char *v7; // [sp+38h] [bp-10h]@7
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; ; ++j )
  {
    if ( j >= v8->m_Param.m_dbInven.m_nUsedNum )
      return -1;
    v7 = &v8->m_Param.m_dbInven.m_pStorageList[j].m_bLoad;
    if ( *v7 )
    {
      if ( *(_DWORD *)(v7 + 13) == (unsigned __int8)bySlotIndex )
        break;
    }
  }
  if ( CPlayer::Emb_DelStorage(v8, 0, j, 0, 1, "CPlayer::_DeleteUnitKey()") )
    result = *(_WORD *)(v7 + 17);
  else
    result = -1;
  return result;
}
