/*
 * Function: ?UpdateDayChangedWork@CGuildBattleScheduleManager@GUILD_BATTLE@@AEAAXXZ
 * Address: 0x1403DD650
 */

void __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::UpdateDayChangedWork(GUILD_BATTLE::CGuildBattleScheduleManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = GUILD_BATTLE::CGuildBattleScheduleManager::IsDayChanged(v5);
  if ( v4 )
  {
    if ( v4 == -1 )
    {
      GUILD_BATTLE::CGuildBattleScheduleManager::Clear(v5);
    }
    else if ( v4 == 1 )
    {
      GUILD_BATTLE::CGuildBattleScheduleManager::Flip(v5);
    }
  }
}
