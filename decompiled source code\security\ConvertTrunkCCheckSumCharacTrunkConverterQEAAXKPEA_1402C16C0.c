/*
 * Function: ?ConvertTrunk@CCheckSumCharacTrunkConverter@@QEAAXKPEAN@Z
 * Address: 0x1402C16C0
 */

void __usercall CCheckSumCharacTrunkConverter::ConvertTrunk(CCheckSumCharacTrunkConverter *this@<rcx>, unsigned int dwSerial@<edx>, long double *pVal@<r8>, long double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  CCheckSumCharacTrunkConverter *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+48h] [bp+10h]@1
  long double *v10; // [sp+50h] [bp+18h]@1

  v10 = pVal;
  dwSeriala = dwSerial;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pVal )
  {
    for ( j = 0; j < 6; ++j )
    {
      CCheckSumBaseConverter::ProcCode(&v8->0, j, dwSeriala, v10[j]);
      v10[j] = a4;
    }
  }
}
