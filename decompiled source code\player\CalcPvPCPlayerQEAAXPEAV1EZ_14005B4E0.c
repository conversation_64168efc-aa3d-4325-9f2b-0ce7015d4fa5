/*
 * Function: ?CalcPvP@CPlayer@@QEAAXPEAV1@E@Z
 * Address: 0x14005B4E0
 */

void __fastcall CPlayer::CalcPvP(CPlayer *this, CPlayer *pDier, char by<PERSON><PERSON>rObjID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@5
  int v6; // eax@7
  double v7; // xmm0_8@12
  CNationSettingManager *v8; // rax@14
  int v9; // eax@35
  signed int v10; // eax@38
  __int64 v11; // [sp+0h] [bp-108h]@1
  long double dPvpPoint; // [sp+28h] [bp-E0h]@8
  double v13; // [sp+38h] [bp-D0h]@10
  double v14; // [sp+40h] [bp-C8h]@10
  double v15; // [sp+48h] [bp-C0h]@10
  struct CHolyStone *v16; // [sp+50h] [bp-B8h]@17
  CPlayer *out_ppMember; // [sp+70h] [bp-98h]@30
  char v18; // [sp+B4h] [bp-54h]@30
  int v19; // [sp+B8h] [bp-50h]@33
  int j; // [sp+BCh] [bp-4Ch]@33
  float v21; // [sp+C0h] [bp-48h]@38
  long double v22; // [sp+C8h] [bp-40h]@38
  int v23; // [sp+D0h] [bp-38h]@5
  CGameObjectVtbl *v24; // [sp+D8h] [bp-30h]@5
  int v25; // [sp+E0h] [bp-28h]@7
  CGameObjectVtbl *v26; // [sp+E8h] [bp-20h]@7
  __int16 *pDest2; // [sp+F0h] [bp-18h]@14
  __int16 *pDest1; // [sp+F8h] [bp-10h]@14
  CPlayer *pkDest; // [sp+110h] [bp+8h]@1
  CPlayer *pkSelf; // [sp+118h] [bp+10h]@1

  pkSelf = pDier;
  pkDest = this;
  v3 = &v11;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CPlayer::IsChaosMode(pkDest) )
  {
    v23 = ((int (__fastcall *)(CPlayer *))pkDest->vfptr->GetObjRace)(pkDest);
    v24 = pkSelf->vfptr;
    v5 = ((int (__fastcall *)(CPlayer *))v24->GetObjRace)(pkSelf);
    if ( v23 == v5 )
      return;
  }
  if ( CPlayer::IsPunished(pkSelf, 1, 0) )
  {
    v25 = ((int (__fastcall *)(CPlayer *))pkDest->vfptr->GetObjRace)(pkDest);
    v26 = pkSelf->vfptr;
    v6 = ((int (__fastcall *)(CPlayer *))v26->GetObjRace)(pkSelf);
    if ( v25 == v6 )
      return;
  }
  dPvpPoint = DOUBLE_1_0;
  CPlayerDB::GetPvPPoint(&pkSelf->m_Param);
  if ( 1.0 <= 0.0 || pkDest->m_byUserDgr != pkSelf->m_byUserDgr )
    goto LABEL_24;
  CPlayerDB::GetPvPPoint(&pkSelf->m_Param);
  v13 = 1.0 + 10000.0;
  CPlayerDB::GetPvPPoint(&pkDest->m_Param);
  v14 = 1.0 + 10000.0 + 10000.0;
  v15 = v13 / v14;
  dPvpPoint = v13 / v14 * 500.0 + 0.5;
  if ( dPvpPoint < 1.0 )
    dPvpPoint = DOUBLE_1_0;
  v7 = dPvpPoint;
  if ( dPvpPoint > 100000000.0 )
  {
    v7 = DOUBLE_1_0e8;
    dPvpPoint = DOUBLE_1_0e8;
  }
  pDest2 = &pkSelf->m_pUserDB->m_BillingInfo.iType;
  pDest1 = &pkDest->m_pUserDB->m_BillingInfo.iType;
  v8 = CTSingleton<CNationSettingManager>::Instance();
  if ( !CNationSettingManager::IsPersonalFreeFixedAmountBillingType(v8, pDest1, pDest2) )
  {
    CPlayerDB::GetPvPPoint(&pkSelf->m_Param);
    if ( dPvpPoint > v7 )
    {
      CPlayerDB::GetPvPPoint(&pkSelf->m_Param);
      dPvpPoint = v7;
    }
    v16 = &g_Stone[CPlayerDB::GetRaceCode(&pkDest->m_Param)];
    if ( v16->m_pCurMap == pkDest->m_pCurMap && CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 )
      dPvpPoint = dPvpPoint * 3.0;
    if ( pkSelf->m_bInGuildBattle
      || CPvpPointLimiter::TakePvpPoint(&pkSelf->m_kPvpPointLimiter, &dPvpPoint, pkSelf, pkDest) )
    {
      if ( !pkSelf->m_bInGuildBattle )
        CPlayer::AlterPvPPoint(pkSelf, -0.0 - dPvpPoint, die_dec, pkDest->m_dwObjSerial);
LABEL_24:
      if ( !pkSelf->m_bInGuildBattle )
      {
        dPvpPoint = dPvpPoint * 50.0 / 100.0;
        if ( dPvpPoint < 1.0 )
          dPvpPoint = DOUBLE_1_0;
        if ( CPartyPlayer::IsPartyMode(pkDest->m_pPartyMgr) && dPvpPoint != 1.0 )
        {
          v18 = CPlayer::_GetPartyMemberInCircle(pkDest, &out_ppMember, 8, 0);
          if ( (double)(unsigned __int8)v18 <= dPvpPoint && (signed int)(unsigned __int8)v18 > 0 )
          {
            CPlayer::IncPvPPoint(pkDest, dPvpPoint * 0.699999988079071, 0, pkSelf->m_dwObjSerial);
            v19 = 0;
            for ( j = 0; j < (unsigned __int8)v18; ++j )
            {
              v9 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetLevel)(*(&out_ppMember + j));
              v19 += v9;
            }
            for ( j = 0; j < (unsigned __int8)v18; ++j )
            {
              v10 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetLevel)(*(&out_ppMember + j));
              v21 = (float)v10 / (float)v19;
              v22 = dPvpPoint * 0.300000011920929 * v21;
              if ( v22 >= 1.0 )
                CPlayer::IncPvPPoint(*(&out_ppMember + j), v22, kill_p_inc, pkSelf->m_dwObjSerial);
            }
          }
          else
          {
            CPlayer::IncPvPPoint(pkDest, dPvpPoint, 0, pkSelf->m_dwObjSerial);
          }
        }
        else
        {
          CPlayer::IncPvPPoint(pkDest, dPvpPoint, 0, pkSelf->m_dwObjSerial);
        }
      }
      return;
    }
  }
}
