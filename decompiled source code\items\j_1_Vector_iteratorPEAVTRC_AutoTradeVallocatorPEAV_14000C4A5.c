/*
 * Function: j_??1?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@XZ
 * Address: 0x14000C4A5
 */

void __fastcall std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(this);
}
