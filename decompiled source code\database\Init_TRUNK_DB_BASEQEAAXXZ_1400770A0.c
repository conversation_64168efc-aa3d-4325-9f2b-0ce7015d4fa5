/*
 * Function: ?Init@_TRUNK_DB_BASE@@QEAAXXZ
 * Address: 0x1400770A0
 */

void __fastcall _TRUNK_DB_BASE::Init(_TRUNK_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@7
  _TRUNK_DB_BASE *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->bySlotNum = 0;
  for ( j = 0; j < 100; ++j )
    _TRUNK_DB_BASE::_LIST::Init(&v6->m_List[j]);
  v6->byExtSlotNum = 0;
  for ( k = 0; k < 40; ++k )
    _TRUNK_DB_BASE::_LIST::Init(&v6->m_ExtList[k]);
}
