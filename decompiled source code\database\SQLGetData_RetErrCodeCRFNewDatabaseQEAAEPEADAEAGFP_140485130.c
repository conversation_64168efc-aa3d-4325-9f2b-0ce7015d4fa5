/*
 * Function: ?SQLGetData_RetErrCode@CRFNewDatabase@@QEAAEPEADAEAGFPEAX@Z
 * Address: 0x140485130
 */

char __fastcall CRFNewDatabase::SQLGetData_RetErrCode(CRFNewDatabase *this, char *strQuery, unsigned __int16 *ColumnNumber, __int16 TargetType, void *TargetValue)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  __int64 v8; // [sp+0h] [bp-58h]@1
  SQLLE<PERSON> BufferLength; // [sp+20h] [bp-38h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-30h]@4
  SQLLEN v11; // [sp+38h] [bp-20h]@4
  __int16 v12; // [sp+44h] [bp-14h]@4
  char v13; // [sp+48h] [bp-10h]@6
  SQLUSMALLINT v14; // [sp+4Ah] [bp-Eh]@4
  CRFNewDatabase *v15; // [sp+60h] [bp+8h]@1
  char *strQuerya; // [sp+68h] [bp+10h]@1

  strQuerya = strQuery;
  v15 = this;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = (*ColumnNumber)++;
  StrLen_or_IndPtr = &v11;
  BufferLength = 0i64;
  v12 = SQLGetData_0(v15->m_hStmtSelect, v14, TargetType, TargetValue, 0i64, &v11);
  if ( v12 && v12 != 1 )
  {
    v13 = 0;
    if ( v12 == 100 )
    {
      v13 = 2;
    }
    else
    {
      BufferLength = (SQLLEN)v15->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog(v15, v12, strQuerya, "SQLGetData_RetErrCode", (void *)BufferLength);
      CRFNewDatabase::ErrorAction(v15, v12, v15->m_hStmtSelect);
      v13 = 1;
    }
    if ( v15->m_hStmtSelect )
      SQLCloseCursor_0(v15->m_hStmtSelect);
    result = v13;
  }
  else
  {
    if ( v12 == 1 )
    {
      BufferLength = (SQLLEN)v15->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog(v15, v12, strQuerya, "SQLGetData_RetErrCode", (void *)BufferLength);
    }
    result = 0;
  }
  return result;
}
