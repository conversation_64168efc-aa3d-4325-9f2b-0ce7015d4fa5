/*
 * Function: ?pc_ChangeModeType@CPlayer@@QEAAXHH@Z
 * Address: 0x1400FD840
 */

void __fastcall CPlayer::pc_ChangeModeType(CPlayer *this, int nModeType, int nStandType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6->m_byModeType = nModeType;
  v6->m_byStandType = nStandType;
  CPlayer::SenseState(v6);
}
