/*
 * Function: ?IsolatedInitialize@?$SourceTemplate@VFileStore@CryptoPP@@@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x140454040
 */

void __fastcall CryptoPP::SourceTemplate<CryptoPP::FileStore>::IsolatedInitialize(CryptoPP::SourceTemplate<CryptoPP::FileStore> *this, CryptoPP::NameValuePairs *parameters)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CryptoPP::ClonableVtbl *v5; // [sp+20h] [bp-18h]@4
  CryptoPP::SourceTemplate<CryptoPP::FileStore> *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = v6->m_store.vfptr;
  ((void (__fastcall *)(signed __int64))v5[3].Clone)((signed __int64)&v6->m_store);
}
