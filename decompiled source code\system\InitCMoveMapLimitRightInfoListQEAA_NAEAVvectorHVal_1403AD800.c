/*
 * Function: ?Init@CMoveMapLimitRightInfoList@@QEAA_NAEAV?$vector@HV?$allocator@H@std@@@std@@@Z
 * Address: 0x1403AD800
 */

char __fastcall CMoveMapLimitRightInfoList::Init(CMoveMapLimitRightInfoList *this, std::vector<int,std::allocator<int> > *vecRightTypeList)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@7
  unsigned __int64 v5; // rax@12
  CMoveMapLimitRightInfo *v6; // rax@13
  __int64 v7; // [sp+0h] [bp-168h]@1
  CMoveMapLimitRightInfo rhs; // [sp+28h] [bp-140h]@4
  std::vector<int,std::allocator<int> > *v9; // [sp+58h] [bp-110h]@4
  std::_Vector_const_iterator<int,std::allocator<int> > _Right; // [sp+68h] [bp-100h]@4
  std::_Vector_const_iterator<int,std::allocator<int> > v11; // [sp+98h] [bp-D0h]@4
  int iType; // [sp+B4h] [bp-B4h]@6
  CMoveMapLimitRightInfo _Val; // [sp+C8h] [bp-A0h]@9
  int j; // [sp+F4h] [bp-74h]@11
  std::_Vector_iterator<int,std::allocator<int> > result; // [sp+F8h] [bp-70h]@4
  std::_Vector_iterator<int,std::allocator<int> > v16; // [sp+110h] [bp-58h]@4
  char v17; // [sp+128h] [bp-40h]@7
  char v18; // [sp+129h] [bp-3Fh]@10
  char v19; // [sp+12Ah] [bp-3Eh]@14
  __int64 v20; // [sp+130h] [bp-38h]@4
  std::_Vector_iterator<int,std::allocator<int> > *v21; // [sp+138h] [bp-30h]@4
  std::_Vector_const_iterator<int,std::allocator<int> > *__that; // [sp+140h] [bp-28h]@4
  std::_Vector_iterator<int,std::allocator<int> > *v23; // [sp+148h] [bp-20h]@4
  std::_Vector_const_iterator<int,std::allocator<int> > *v24; // [sp+150h] [bp-18h]@4
  unsigned __int64 v25; // [sp+158h] [bp-10h]@12
  CMoveMapLimitRightInfoList *v26; // [sp+170h] [bp+8h]@1
  std::vector<int,std::allocator<int> > *v27; // [sp+178h] [bp+10h]@1

  v27 = vecRightTypeList;
  v26 = this;
  v2 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = -2i64;
  CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(&rhs);
  v9 = v27;
  v21 = std::vector<int,std::allocator<int>>::end(v27, &result);
  __that = (std::_Vector_const_iterator<int,std::allocator<int> > *)v21;
  std::_Vector_const_iterator<int,std::allocator<int>>::_Vector_const_iterator<int,std::allocator<int>>(
    &_Right,
    (std::_Vector_const_iterator<int,std::allocator<int> > *)&v21->_Mycont);
  std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&result);
  v23 = std::vector<int,std::allocator<int>>::begin(v9, &v16);
  v24 = (std::_Vector_const_iterator<int,std::allocator<int> > *)v23;
  std::_Vector_const_iterator<int,std::allocator<int>>::_Vector_const_iterator<int,std::allocator<int>>(
    &v11,
    (std::_Vector_const_iterator<int,std::allocator<int> > *)&v23->_Mycont);
  std::_Vector_iterator<int,std::allocator<int>>::~_Vector_iterator<int,std::allocator<int>>(&v16);
  while ( std::_Vector_const_iterator<int,std::allocator<int>>::operator!=(&v11, &_Right) )
  {
    iType = *std::_Vector_const_iterator<int,std::allocator<int>>::operator*(&v11);
    if ( !CMoveMapLimitRightInfo::Regist(&rhs, iType) )
    {
      CLogFile::Write(
        &stru_1799C8F30,
        "CMoveMapLimitRightInfoList::Init(...) : kVal.Regist( iType(%d) ) ) Fail!",
        (unsigned int)iType);
      v17 = 0;
      std::_Vector_const_iterator<int,std::allocator<int>>::~_Vector_const_iterator<int,std::allocator<int>>(&v11);
      std::_Vector_const_iterator<int,std::allocator<int>>::~_Vector_const_iterator<int,std::allocator<int>>(&_Right);
      CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&rhs);
      return v17;
    }
    std::_Vector_const_iterator<int,std::allocator<int>>::operator++(&v11);
  }
  std::_Vector_const_iterator<int,std::allocator<int>>::~_Vector_const_iterator<int,std::allocator<int>>(&v11);
  std::_Vector_const_iterator<int,std::allocator<int>>::~_Vector_const_iterator<int,std::allocator<int>>(&_Right);
  CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(&_Val);
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::assign(&v26->m_vecRight, 0x9E4ui64, &_Val);
  if ( std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(&v26->m_vecRight) == 2532 )
  {
    for ( j = 0; ; ++j )
    {
      v25 = j;
      v5 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(&v26->m_vecRight);
      if ( v25 >= v5 )
        break;
      v6 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](&v26->m_vecRight, j);
      CMoveMapLimitRightInfo::operator=(v6, &rhs);
    }
    v19 = 1;
    CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&_Val);
    CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&rhs);
    v4 = v19;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8F30,
      "CMoveMapLimitRightInfoList::Init(...) : m_vecRight.assign( MAX_PLARYER(%u) ) Fail!",
      2532i64);
    v18 = 0;
    CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&_Val);
    CMoveMapLimitRightInfo::~CMoveMapLimitRightInfo(&rhs);
    v4 = v18;
  }
  return v4;
}
