/*
 * Function: ?SendMsg_Move@CMonster@@QEAAXXZ
 * Address: 0x140148A70
 */

void __fastcall CMonster::SendMsg_Move(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@7
  __int16 pShort; // [sp+3Ch] [bp-5Ch]@7
  __int16 v6; // [sp+42h] [bp-56h]@7
  __int16 v7; // [sp+44h] [bp-54h]@7
  char pbyType; // [sp+64h] [bp-34h]@7
  char v9; // [sp+65h] [bp-33h]@7
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  CMonster *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v3 ^ _security_cookie;
  if ( v11->m_bMove )
  {
    if ( v11->m_bOper )
    {
      *(_DWORD *)szMsg = v11->m_dwObjSerial;
      FloatToShort(v11->m_fCurPos, &pShort, 3);
      v6 = (signed int)ffloor(v11->m_fTarPos[0]);
      v7 = (signed int)ffloor(v11->m_fTarPos[2]);
      pbyType = 4;
      v9 = 5;
      CGameObject::CircleReport((CGameObject *)&v11->vfptr, &pbyType, szMsg, 14, 0);
    }
  }
}
