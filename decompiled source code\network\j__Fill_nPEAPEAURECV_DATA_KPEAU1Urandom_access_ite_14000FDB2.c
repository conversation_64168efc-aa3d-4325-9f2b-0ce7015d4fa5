/*
 * Function: j_??$_Fill_n@PEAPEAURECV_DATA@@_KPEAU1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000FDB2
 */

void __fastcall std::_Fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::random_access_iterator_tag>(RECV_DATA **_First, unsigned __int64 _Count, RECV_DATA *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _<PERSON>,
    __formal,
    a5);
}
