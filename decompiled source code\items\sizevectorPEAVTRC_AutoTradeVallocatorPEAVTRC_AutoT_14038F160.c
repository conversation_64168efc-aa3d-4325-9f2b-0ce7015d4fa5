/*
 * Function: ?size@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBA_KXZ
 * Address: 0x14038F160
 */

__int64 __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->_Myfirst )
    v4 = v5->_Mylast - v5->_Myfirst;
  else
    v4 = 0i64;
  return v4;
}
