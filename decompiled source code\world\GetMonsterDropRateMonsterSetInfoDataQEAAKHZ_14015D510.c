/*
 * Function: ?GetMonsterDropRate@MonsterSetInfoData@@QEAAKH@Z
 * Address: 0x14015D510
 */

__int64 __fastcall MonsterSetInfoData::GetMonsterDropRate(MonsterSetInfoData *this, int iDiffLevel)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int v6; // [sp+20h] [bp-18h]@4
  int v7; // [sp+24h] [bp-14h]@4
  MonsterSetInfoData *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  v7 = 0;
  if ( iDiffLevel <= 0 )
  {
    if ( iDiffLevel >= 0 )
    {
      if ( !iDiffLevel )
        v6 = v8->m_iMonsterLootRateSame;
    }
    else
    {
      v7 = abs_0(iDiffLevel);
      if ( v7 > 10 )
        v6 = v8->m_iMonsterLootingRateDown[10];
      else
        v6 = v8->m_iMonsterLootingRateDown[v7 - 1];
    }
  }
  else if ( iDiffLevel > 10 )
  {
    v6 = v8->m_iMonsterLootingRateUp[10];
  }
  else
  {
    v6 = v8->m_iMonsterLootingRateUp[iDiffLevel - 1];
  }
  return v6;
}
