/*
 * Function: ?Init@_REGED_AVATOR_DB@@QEAAXXZ
 * Address: 0x1400754D0
 */

void __fastcall _REGED_AVATOR_DB::Init(_REGED_AVATOR_DB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _REGED_AVATOR_DB *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x45ui64);
  Dst->m_bySlotIndex = -1;
  Dst->m_dwRecordNum = -1;
  for ( j = 0; j < 8; ++j )
    _EQUIPKEY::SetRelease(&Dst->m_EquipKey[j]);
}
