/*
 * Function: ?SendMsg_RealMsgInform@CDarkHoleChannel@@QEAAXPEAD@Z
 * Address: 0x14026CA60
 */

void __fastcall CDarkHoleChannel::SendMsg_RealMsgInform(CDarkHoleChannel *this, char *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@7
  __int64 v5; // [sp+0h] [bp-98h]@1
  char Dest; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v8; // [sp+65h] [bp-33h]@4
  int j; // [sp+74h] [bp-24h]@4
  _dh_player_mgr *v10; // [sp+78h] [bp-20h]@6
  unsigned __int64 v11; // [sp+88h] [bp-10h]@4
  CDarkHoleChannel *v12; // [sp+A0h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  strcpy_0(&Dest, pMsg);
  pbyType = 35;
  v8 = 18;
  for ( j = 0; j < 32; ++j )
  {
    v10 = &v12->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v10) )
    {
      v4 = _darkhole_real_msg_inform_zocl::size((_darkhole_real_msg_inform_zocl *)&Dest);
      CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, &Dest, v4);
    }
  }
}
