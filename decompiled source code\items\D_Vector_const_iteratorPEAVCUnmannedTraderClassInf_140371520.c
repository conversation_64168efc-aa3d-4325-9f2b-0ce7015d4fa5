/*
 * Function: ??D?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEBAAEBQEAVCUnmannedTraderClassInfo@@XZ
 * Address: 0x140371520
 */

CUnmannedTraderClassInfo **__fastcall std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  return this->_Myptr;
}
