/*
 * Function: ?Notify@CExpInfo@CPartyModeKillMonsterExpNotify@@QEAAXXZ
 * Address: 0x1401693B0
 */

void __fastcall CPartyModeKillMonsterExpNotify::CExpInfo::Notify(CPartyModeKillMonsterExpNotify::CExpInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@8
  char pbyType; // [sp+54h] [bp-24h]@8
  char v6; // [sp+55h] [bp-23h]@8
  CPartyModeKillMonsterExpNotify::CExpInfo *v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_pkMember && v7->m_pkMember->m_bOper && v7->m_fExp != 0.0 )
  {
    *(float *)szMsg = v7->m_fExp;
    pbyType = 11;
    v6 = 30;
    CNetProcess::LoadSendMsg(unk_1414F2088, v7->m_pkMember->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
  }
}
