/*
 * Function: ?pc_RequestChangeTaxRate@CPlayer@@QEAAXE@Z
 * Address: 0x140100A00
 */

void __fastcall CPlayer::pc_RequestChangeTaxRate(CPlayer *this, char byTaxRate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  CUnmannedTraderTaxRateManager *v5; // rax@9
  CUnmannedTraderTaxRateManager *v6; // rax@12
  __int64 v7; // [sp+0h] [bp-68h]@1
  char v8; // [sp+30h] [bp-38h]@4
  unsigned int v9; // [sp+34h] [bp-34h]@9
  unsigned int v10; // [sp+38h] [bp-30h]@9
  int v11; // [sp+3Ch] [bp-2Ch]@4
  int v12; // [sp+40h] [bp-28h]@9
  unsigned int v13; // [sp+44h] [bp-24h]@12
  char *v14; // [sp+48h] [bp-20h]@12
  unsigned int dwMatterDst; // [sp+50h] [bp-18h]@12
  int v16; // [sp+54h] [bp-14h]@12
  CPlayer *v17; // [sp+70h] [bp+8h]@1
  char v18; // [sp+78h] [bp+10h]@1

  v18 = byTaxRate;
  v17 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  v11 = CPlayerDB::GetRaceCode(&v17->m_Param);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  if ( CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v11, 5) == v17->m_dwObjSerial )
  {
    if ( (signed int)(unsigned __int8)v18 >= 5 && (signed int)(unsigned __int8)v18 <= 20 )
    {
      v12 = CPlayerDB::GetRaceCode(&v17->m_Param);
      v5 = CUnmannedTraderTaxRateManager::Instance();
      v9 = CUnmannedTraderTaxRateManager::GetSuggestedTime(v5, v12);
      v10 = GetKorLocalTime();
      if ( v10 - v9 < 0x3C )
        v8 = 2;
    }
    else
    {
      v8 = 3;
    }
  }
  else
  {
    v8 = 1;
  }
  if ( v8 )
  {
    CPlayer::SendMsg_ResultChangeTaxRate(v17, v8, -1);
  }
  else
  {
    v13 = (unsigned __int8)v18;
    v14 = CPlayerDB::GetCharNameW(&v17->m_Param);
    dwMatterDst = CPlayerDB::GetCharSerial(&v17->m_Param);
    v16 = CPlayerDB::GetRaceCode(&v17->m_Param);
    v6 = CUnmannedTraderTaxRateManager::Instance();
    CUnmannedTraderTaxRateManager::SetSuggested(v6, v16, 0, dwMatterDst, v14, v13);
    CPlayer::SendMsg_ResultChangeTaxRate(v17, v8, v18);
  }
}
