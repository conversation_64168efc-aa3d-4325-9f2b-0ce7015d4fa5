/*
 * Function: ?<PERSON><PERSON>@CPlayer@@QEAA_NH_N@Z
 * Address: 0x14005F140
 */

bool __fastcall CPlayer::SetFP(CPlayer *this, int nFP, bool bOver)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v6; // eax@8
  int v7; // eax@11
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@6
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  int dwFP; // [sp+48h] [bp+10h]@1
  bool v12; // [sp+50h] [bp+18h]@1

  v12 = bOver;
  dwFP = nFP;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v10->m_bNeverDie )
    return 1;
  v9 = CPlayer::GetFP(v10);
  if ( !v12 && dwFP > v9 )
  {
    v6 = CPlayer::GetMaxFP(v10);
    if ( v9 >= v6 || dwFP <= CPlayer::GetMaxFP(v10) )
    {
      v7 = CPlayer::GetMaxFP(v10);
      if ( v9 >= v7 && dwFP >= v9 )
        return 0;
    }
    else
    {
      dwFP = CPlayer::GetMaxFP(v10);
    }
  }
  if ( dwFP < 0 )
    dwFP = 0;
  if ( v9 == dwFP )
  {
    result = 0;
  }
  else
  {
    CPlayerDB::SetFP(&v10->m_Param, dwFP);
    result = 1;
  }
  return result;
}
