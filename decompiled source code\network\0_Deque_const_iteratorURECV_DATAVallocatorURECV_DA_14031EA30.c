/*
 * Function: ??0?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAA@_KPEBV_Container_base@1@@Z
 * Address: 0x14031EA30
 */

void __fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, unsigned __int64 _Off, std::_Container_base *_Pdeque)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+30h] [bp+8h]@1
  unsigned __int64 v7; // [sp+38h] [bp+10h]@1
  std::_Container_base *v8; // [sp+40h] [bp+18h]@1

  v8 = _Pdeque;
  v7 = _Off;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Ranit<RECV_DATA,__int64,RECV_DATA const *,RECV_DATA const &>::_Ranit<RECV_DATA,__int64,RECV_DATA const *,RECV_DATA const &>((std::_Ranit<RECV_DATA,__int64,RECV_DATA const *,RECV_DATA const &> *)&v6->_Mycont);
  v6->_Mycont = (std::deque<RECV_DATA,std::allocator<RECV_DATA> > *)v8;
  v6->_Myoff = v7;
}
