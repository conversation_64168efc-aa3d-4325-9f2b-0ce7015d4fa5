# Economy Module

## Overview

The Economy Module provides comprehensive economic management for the NexusProtection game server. This module has been refactored from decompiled C source code to modern C++20 standards compatible with Visual Studio 2022.

## Refactored Files

### Core Economy Classes
- **Original**: `decompiled source ode/economy/0CMoneySupplyMgrQEAAXZ_14042B630.c` and related files
- **Refactored Header**: `NexusProtection/economy/Headers/CMoneySupplyMgr.h`
- **Refactored Source**: `NexusProtection/economy/Source/CMoneySupplyMgr.cpp`

### Economy System
- **Original**: `decompiled source ode/economy/_ReadEconomyIniFileYA_NXZ_1402A5040.c` and related files
- **Refactored Header**: `NexusProtection/economy/Headers/EconomySystem.h`
- **Refactored Source**: `NexusProtection/economy/Source/EconomySystem.cpp`

### Network Handlers
- **Original**: `decompiled source ode/economy/ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.c` and related files
- **Refactored Header**: `NexusProtection/economy/Headers/EconomyNetworkHandlers.h`
- **Refactored Source**: `NexusProtection/economy/Source/EconomyNetworkHandlers.cpp`

## Module Structure

```
NexusProtection/economy/
├── Headers/
│   ├── Economy.h                    # Main module header
│   ├── EconomyTypes.h              # Core types and enumerations
│   ├── MoneySupplyData.h           # Money supply data structures
│   ├── CMoneySupplyMgr.h           # Money supply manager
│   ├── EconomySystem.h             # Global economy system
│   └── EconomyNetworkHandlers.h    # Network request handlers
├── Source/
│   ├── Economy.cpp                 # Main module implementation
│   ├── MoneySupplyData.cpp         # Data structure implementations
│   ├── CMoneySupplyMgr.cpp         # Money supply manager implementation
│   ├── EconomySystem.cpp           # Economy system implementation
│   └── EconomyNetworkHandlers.cpp  # Network handlers implementation
└── Documents/
    └── README.md                   # This file
```

## Key Features

### Modern C++ Implementation
- **Type Safety**: Strong typing with enums and type-safe interfaces
- **Memory Management**: RAII and smart pointers for automatic resource management
- **Thread Safety**: Mutex protection for concurrent access
- **Exception Safety**: Proper exception handling throughout
- **STL Integration**: Modern STL containers and algorithms

### Economy Management
- **Money Supply Tracking**: Comprehensive tracking of all economic transactions
- **Currency Exchange**: Dalant ↔ Gold exchange system
- **Guild Money Operations**: Guild financial management and history
- **Statistical Analysis**: Detailed economic statistics by race, level, and transaction type
- **Web Service Integration**: Automatic data transmission to web services

### Network Protocol Support
- **Currency Exchange Requests**: Handle player currency exchange operations
- **Guild Money Requests**: Process guild financial operations
- **Trunk Operations**: Player storage money management
- **Auto-mine Cost Management**: Automatic mining machine cost handling

## Core Classes

### CMoneySupplyMgr
Singleton class managing economic statistics and money supply data.

**Key Methods:**
- `UpdateSellData()` - Track item sales
- `UpdateBuyData()` - Track item purchases
- `UpdateFeeMoneyData()` - Track fee collections
- `UpdateHonorGuildMoneyData()` - Track guild money operations
- `LoopMoneySupply()` - Periodic data transmission

### EconomySystem
Global economy state management including currency rates and trade data.

**Key Methods:**
- `AddDalant()` - Add Dalant currency to race pool
- `GetDalant()` - Get current Dalant amount for race
- `UpdateEconomySystem()` - Process economic updates
- `ReadEconomyIniFile()` - Load configuration

### EconomyNetworkHandlers
Network request handlers for economy-related operations.

**Key Methods:**
- `HandleExchangeDalantForGoldRequest()` - Process currency exchange
- `HandleGuildPushMoneyRequest()` - Process guild money operations
- `HandleTrunkIoMoneyRequest()` - Process trunk money operations

## Data Structures

### MoneySupplyData
Comprehensive economic statistics tracking:
- Transaction amounts by type (sell, buy, fees, rewards)
- Level-based statistics (30, 40, 50, 60)
- Race-based statistics (Bellato, Cora, Accretia)
- Guild money operations
- Unit purchase tracking

### EconomyCalculationData
Economic calculation data for trade rates:
- Currency exchange rates
- Ore mining statistics
- Trade volume tracking

## Legacy Compatibility

The module maintains full backward compatibility with existing C code through:
- **C Interface Wrappers**: All original function signatures preserved
- **Global Variables**: Legacy global variables maintained
- **Data Structure Compatibility**: Automatic conversion between legacy and modern formats
- **Function Macros**: Convenience macros for easy migration

## Usage Examples

### Modern C++ Interface
```cpp
// Initialize economy module
NexusProtection::Economy::InitializeEconomyModule();

// Track a sell transaction
auto& moneyMgr = NexusProtection::Economy::GetMoneySupplyManager();
moneyMgr.UpdateSellData(NexusProtection::Economy::RaceType::Bellato, 50, "Warrior", 1000);

// Add currency
auto& economySystem = NexusProtection::Economy::GetEconomySystem();
economySystem.AddDalant(NexusProtection::Economy::RaceType::Cora, 500);
```

### Legacy C Interface
```c
// Initialize economy (legacy)
InitializeEconomy();

// Track transactions (legacy)
CMoneySupplyMgr* mgr = CMoneySupplyMgr_Instance();
CMoneySupplyMgr_UpdateSellData(mgr, 0, 50, "Warrior", 1000);

// Add currency (legacy)
eAddDalant(1, 500);
```

## Configuration

The economy system reads configuration from `.\Initialize\WorldSystem.ini`:

```ini
[Economy]
Default_OreVal=1000
```

## Integration Points

### Database System
- Economic data persistence
- Transaction history storage
- Guild money tracking

### Network System
- Currency exchange requests
- Guild money operations
- Web service data transmission

### Player System
- Player currency management
- Transaction validation
- Permission checking

## Performance Optimizations

### Memory Management
- Pre-allocated data structures
- RAII for automatic cleanup
- Minimal dynamic allocations

### Thread Safety
- Mutex protection for shared data
- Lock-free operations where possible
- Efficient concurrent access patterns

### Network Efficiency
- Batched data transmission
- Compressed data formats
- Periodic rather than real-time updates

## Testing Recommendations

1. **Unit Tests**: Test individual methods with mock data
2. **Integration Tests**: Test with actual game scenarios
3. **Performance Tests**: Verify no performance regression
4. **Compatibility Tests**: Ensure legacy interface compatibility
5. **Stress Tests**: Test under high transaction volumes

## Compilation Requirements

- **Compiler**: Visual Studio 2022 (Platform Toolset v143)
- **Standard**: C++17 or C++20
- **Architecture**: x64
- **Runtime**: Multi-threaded DLL (/MD)

## Future Enhancements

1. **Advanced Analytics**: More detailed economic analysis
2. **Real-time Monitoring**: Live economic dashboard
3. **Automated Balancing**: Dynamic economic adjustments
4. **Fraud Detection**: Suspicious transaction detection
5. **Performance Monitoring**: Built-in performance metrics
