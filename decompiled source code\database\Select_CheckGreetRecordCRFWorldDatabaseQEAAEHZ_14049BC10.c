/*
 * Function: ?Select_CheckGreetRecord@CRFWorldDatabase@@QEAAEH@Z
 * Address: 0x14049BC10
 */

char __fastcall CRFWorldDatabase::Select_CheckGreetRecord(CRFWorldDatabase *this, int nUseType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-128h]@1
  void *SQLStmt; // [sp+20h] [bp-108h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-100h]@22
  char Dest; // [sp+40h] [bp-E8h]@4
  char v9; // [sp+41h] [bp-E7h]@4
  SQLLEN v10; // [sp+D8h] [bp-50h]@22
  __int16 v11; // [sp+E4h] [bp-44h]@9
  int TargetValue; // [sp+F4h] [bp-34h]@4
  char v13; // [sp+104h] [bp-24h]@16
  unsigned __int64 v14; // [sp+110h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+130h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v9, 0, 0x7Fui64);
  TargetValue = 0;
  sprintf(&Dest, "select count(useType) from tbl_GreetMsg where useType = %d", (unsigned int)nUseType);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v11 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v15->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
          v13 = 1;
        }
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, -6, &TargetValue, 0i64, &v10);
        if ( v11 != 100 && TargetValue )
        {
          if ( v15->m_hStmtSelect )
            SQLCloseCursor_0(v15->m_hStmtSelect);
          if ( v15->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
          result = 0;
        }
        else
        {
          SQLCloseCursor_0(v15->m_hStmtSelect);
          result = 2;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
