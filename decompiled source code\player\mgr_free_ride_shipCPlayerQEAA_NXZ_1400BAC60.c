/*
 * Function: ?mgr_free_ride_ship@CPlayer@@QEAA_NXZ
 * Address: 0x1400BAC60
 */

char __fastcall CPlayer::mgr_free_ride_ship(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v4; // ax@8
  char v5; // al@8
  __int64 v6; // [sp+0h] [bp-48h]@1
  CMapData *pIntoMap; // [sp+30h] [bp-18h]@4
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pIntoMap = CMapOperation::GetMap(&g_MapOper, "Transport01");
  if ( pIntoMap )
  {
    if ( CMapData::IsMapIn(pIntoMap, fPos) )
    {
      v4 = CPlayerDB::GetRaceCode(&v8->m_Param);
      CPlayer::OutOfMap(v8, pIntoMap, v4, 4, fPos);
      v5 = CPlayerDB::GetMapCode(&v8->m_Param);
      CPlayer::SendMsg_GotoRecallResult(v8, 0, v5, fPos, 4);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
