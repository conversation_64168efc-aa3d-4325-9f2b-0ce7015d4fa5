/*
 * Function: ?MakePage@CPossibleBattleGuildListManager@GUILD_BATTLE@@AEAA_NEEAEAG@Z
 * Address: 0x1403C9D70
 */

bool __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::MakePage(GUILD_BATTLE::CPossibleBattleGuildListManager *this, char byRace, char ucPage, unsigned __int16 *wLastGuildInx)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v7; // [sp+0h] [bp-58h]@1
  _possible_battle_guild_list_result_zocl::__list *v8; // [sp+20h] [bp-38h]@7
  int v9; // [sp+28h] [bp-30h]@7
  int v10; // [sp+2Ch] [bp-2Ch]@7
  char *v11; // [sp+30h] [bp-28h]@7
  char v12; // [sp+38h] [bp-20h]@7
  int j; // [sp+3Ch] [bp-1Ch]@7
  int k; // [sp+40h] [bp-18h]@9
  int v15; // [sp+44h] [bp-14h]@19
  GUILD_BATTLE::CPossibleBattleGuildListManager *v16; // [sp+60h] [bp+8h]@1
  char v17; // [sp+68h] [bp+10h]@1
  char v18; // [sp+70h] [bp+18h]@1
  unsigned __int16 *v19; // [sp+78h] [bp+20h]@1

  v19 = wLastGuildInx;
  v18 = ucPage;
  v17 = byRace;
  v16 = this;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v16->m_bInit && (signed int)(unsigned __int8)byRace < 3 )
  {
    v8 = v16->m_ppkList[(unsigned __int8)byRace][(unsigned __int8)ucPage].DestGuild;
    v9 = 4 * (unsigned __int8)ucPage;
    v10 = 4 * (unsigned __int8)ucPage + 4;
    v16->m_ppkList[(unsigned __int8)byRace][(unsigned __int8)ucPage].byCount = 0;
    memset_0(v16->m_ppkList[(unsigned __int8)byRace][(unsigned __int8)ucPage].DestGuild, 0, 0x5Cui64);
    v16->m_ppkList[(unsigned __int8)v17][(unsigned __int8)v18].byPage = v18;
    v16->m_ppkList[(unsigned __int8)v17][(unsigned __int8)v18].dwBattleCost = 5000;
    v11 = &v16->m_ppkList[(unsigned __int8)v17][(unsigned __int8)v18].byCount;
    v12 = 0;
    for ( j = v9; j < v10; ++j )
    {
      v12 = 0;
      for ( k = *v19; k < 500; ++k )
      {
        if ( CGuild::IsFill(&g_Guild[k])
          && GUILD_BATTLE::LIMIT_DEST_GRADE <= g_Guild[k].m_byGrade
          && !_guild_battle_suggest_matter::IsCompleteBattle(&g_Guild[k].m_GuildBattleSugestMatter)
          && g_Guild[k].m_byRace == (unsigned __int8)v17 )
        {
          *v19 = k;
          v12 = 1;
          break;
        }
      }
      if ( !v12 )
        break;
      strcpy_0(v8[(unsigned __int8)*v11].wszName, g_Guild[*v19].m_wszName);
      v8[(unsigned __int8)*v11].byGrade = g_Guild[*v19].m_byGrade;
      v8[(unsigned __int8)*v11].byRace = g_Guild[*v19].m_byRace;
      v8[(unsigned __int8)(*v11)++].dwGuildSerial = g_Guild[(*v19)++].m_dwSerial;
    }
    v15 = (signed int)(unsigned __int8)*v11 > 0;
    result = v15;
  }
  else
  {
    result = 0;
  }
  return result;
}
