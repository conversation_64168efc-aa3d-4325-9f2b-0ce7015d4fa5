/*
 * Function: ?SendMsg_ItemStorageRefresh@CPlayer@@QEAAXE@Z
 * Address: 0x1400D6E20
 */

void __fastcall CPlayer::SendMsg_ItemStorageRefresh(CPlayer *this, char byStorageCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-178h]@1
  _STORAGE_LIST *v5; // [sp+30h] [bp-148h]@4
  _storage_refresh_inform_zocl v6; // [sp+50h] [bp-128h]@4
  int v7; // [sp+124h] [bp-54h]@4
  int j; // [sp+128h] [bp-50h]@4
  char *v9; // [sp+130h] [bp-48h]@6
  char pbyType; // [sp+144h] [bp-34h]@9
  char v11; // [sp+145h] [bp-33h]@9
  unsigned __int64 v12; // [sp+160h] [bp-18h]@4
  CPlayer *v13; // [sp+180h] [bp+8h]@1
  char v14; // [sp+188h] [bp+10h]@1

  v14 = byStorageCode;
  v13 = this;
  v2 = &v4;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  v5 = v13->m_Param.m_pStoragePtr[(unsigned __int8)byStorageCode];
  _storage_refresh_inform_zocl::_storage_refresh_inform_zocl(&v6);
  v6.byStorageCode = v14;
  v6.byItemNum = _STORAGE_LIST::GetNumUseCon(v5);
  v7 = 0;
  for ( j = 0; j < (unsigned __int8)v6.byItemNum; ++j )
  {
    v9 = &v5->m_pStorageList[j].m_bLoad;
    if ( *v9 )
      v6.wSerial[v7++] = *(_WORD *)(v9 + 17);
  }
  pbyType = 3;
  v11 = 24;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &v6.byStorageCode, 0xCAu);
}
