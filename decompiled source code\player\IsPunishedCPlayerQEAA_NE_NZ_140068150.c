/*
 * Function: ?IsPunished@CPlayer@@QEAA_NE_N@Z
 * Address: 0x140068150
 */

char __fastcall CPlayer::IsPunished(CPlayer *this, char byType, bool bSend)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  PatriarchElectProcessor *v6; // rax@12
  unsigned int v7; // eax@12
  __int128 v8; // tt@13
  unsigned __int16 v9; // ax@15
  __int64 v10; // [sp+0h] [bp-A8h]@1
  __int64 _Time; // [sp+38h] [bp-70h]@13
  int v12; // [sp+44h] [bp-64h]@13
  _pt_inform_punishment_zocl v13; // [sp+54h] [bp-54h]@15
  char pbyType; // [sp+74h] [bp-34h]@15
  char v15; // [sp+75h] [bp-33h]@15
  CUserDB *v16; // [sp+88h] [bp-20h]@12
  __int64 v17; // [sp+90h] [bp-18h]@12
  CPlayer *v18; // [sp+B0h] [bp+8h]@1
  char v19; // [sp+B8h] [bp+10h]@1
  bool v20; // [sp+C0h] [bp+18h]@1

  v20 = bSend;
  v19 = byType;
  v18 = this;
  v3 = &v10;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byType >= 3 )
    return 0;
  if ( CMainThread::IsReleaseServiceMode(&g_Main) && v18->m_byUserDgr == 2 )
    return 0;
  if ( !v18->m_pUserDB )
    return 0;
  if ( v18->m_pUserDB->m_AvatorData.dbAvator.m_dwPunishment[(unsigned __int8)v19] == -1 )
    return 0;
  v16 = v18->m_pUserDB;
  v17 = (unsigned __int8)v19;
  v6 = PatriarchElectProcessor::Instance();
  v7 = PatriarchElectProcessor::GetCurrPatriarchElectSerial(v6);
  if ( v16->m_AvatorData.dbAvator.m_dwElectSerial[v17] != v7 )
    return 0;
  time(&_Time);
  *(_QWORD *)&v8 = _Time;
  *((_QWORD *)&v8 + 1) = (unsigned __int128)_Time >> 64;
  v12 = v8 / 60;
  if ( v12 - v18->m_pUserDB->m_AvatorData.dbAvator.m_dwPunishment[(unsigned __int8)v19] >= 0x10E0 )
  {
    v18->m_pUserDB->m_AvatorData.dbAvator.m_dwPunishment[(unsigned __int8)v19] = -1;
    return 0;
  }
  if ( v20 )
  {
    v13.byType = v19;
    v13.nRemainMin = 4320 - (v12 - v18->m_pUserDB->m_AvatorData.dbAvator.m_dwPunishment[(unsigned __int8)v19]);
    pbyType = 13;
    v15 = 117;
    v9 = _pt_inform_punishment_zocl::size(&v13);
    CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &v13.byType, v9);
  }
  return 1;
}
