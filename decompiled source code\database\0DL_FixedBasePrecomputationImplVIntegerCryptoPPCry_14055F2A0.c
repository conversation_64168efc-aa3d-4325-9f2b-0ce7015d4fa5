/*
 * Function: ??0?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14055F2A0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(__int64 a1)
{
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::Integer>::DL_FixedBasePrecomputation<CryptoPP::Integer>();
  *(_QWORD *)v2 = &CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::`vftable';
  CryptoPP::Integer::Integer((void *)(v2 + 8));
  CryptoPP::Integer::Integer((void *)(v2 + 56));
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(v2 + 96);
  return v2;
}
