/*
 * Function: j_?_Destroy@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAXPEAVCUnmannedTraderItemCodeInfo@@0@Z
 * Address: 0x140013309
 */

void __fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, CUnmannedTraderItemCodeInfo *_First, CUnmannedTraderItemCodeInfo *_Last)
{
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(this, _First, _Last);
}
