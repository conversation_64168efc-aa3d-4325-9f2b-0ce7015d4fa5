/*
 * Function: ?CallFunc_Item_Buy@CRusiaBillingMgr@@QEAAHAEAU_param_cash_update@@H@Z
 * Address: 0x140321190
 */

signed __int64 __usercall CRusiaBillingMgr::CallFunc_Item_Buy@<rax>(CRusiaBillingMgr *this@<rcx>, _param_cash_update *rParam@<rdx>, int nIdx@<r8d>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float v6; // xmm1_4@4
  unsigned int v7; // edx@4
  signed __int64 result; // rax@5
  __int64 v9; // [sp+0h] [bp-58h]@1
  char *v10; // [sp+20h] [bp-38h]@4
  unsigned __int64 v11; // [sp+28h] [bp-30h]@4
  float v12; // [sp+30h] [bp-28h]@4
  int v13; // [sp+38h] [bp-20h]@4
  int v14; // [sp+40h] [bp-18h]@4
  char *v15; // [sp+48h] [bp-10h]@4
  CRusiaBillingMgr *v16; // [sp+60h] [bp+8h]@1
  _param_cash_update *v17; // [sp+68h] [bp+10h]@1
  int v18; // [sp+70h] [bp+18h]@1

  v18 = nIdx;
  v17 = rParam;
  v16 = this;
  v4 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CoInitialize(0i64);
  *(float *)&a4 = (float)v17->in_item[(signed __int64)v18].in_nDiscount;
  v6 = (float)v17->in_item[(signed __int64)v18].in_nPrice;
  v7 = v17->in_item[(signed __int64)v18].in_byOverlapNum;
  v15 = v17->in_szAvatorName;
  v13 = LODWORD(a4);
  v12 = v6;
  v11 = v17->in_item[(signed __int64)v18].in_lnUID;
  v10 = v17->in_szSvrName;
  v14 = RFACC_ChargeBalance(v17->in_szAcc, v17->in_szAvatorName, v17->in_item[(signed __int64)v18].in_strItemCode, v7);
  if ( v14 )
  {
    RFACC_CheckBalance(v17->in_szAcc);
    v17->in_item[(signed __int64)v18].out_nCashAmount = (signed int)floor(a4);
    v17->out_nCashAmount = v17->in_item[(signed __int64)v18].out_nCashAmount;
    CoUninitialize();
    result = 0i64;
  }
  else
  {
    CLogFile::Write(&v16->m_logBill, "Item Buy Fail.");
    CoUninitialize();
    result = 1i64;
  }
  return result;
}
