/*
 * Function: j_??$unchecked_uninitialized_copy@PEAURoomCharInfo@@PEAU1@V?$allocator@URoomCharInfo@@@std@@@stdext@@YAPEAURoomCharInfo@@PEAU1@00AEAV?$allocator@URoomCharInfo@@@std@@@Z
 * Address: 0x140010050
 */

RoomCharInfo *__fastcall stdext::unchecked_uninitialized_copy<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(RoomCharInfo *_First, RoomCharInfo *_Last, RoomCharInfo *_Dest, std::allocator<RoomCharInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<RoomCharInfo *,RoomCharInfo *,std::allocator<RoomCharInfo>>(
           _First,
           _Last,
           _Dest,
           _<PERSON>);
}
