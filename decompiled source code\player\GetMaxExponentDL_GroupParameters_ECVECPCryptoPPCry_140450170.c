/*
 * Function: ?GetMaxExponent@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@XZ
 * Address: 0x140450170
 */

CryptoPP::Integer *__fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetMaxExponent(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *this, CryptoPP::Integer *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CryptoPP::Integer *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-68h]@1
  CryptoPP::Integer b; // [sp+20h] [bp-48h]@4
  int v8; // [sp+48h] [bp-20h]@4
  __int64 v9; // [sp+50h] [bp-18h]@4
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *v10; // [sp+70h] [bp+8h]@1
  CryptoPP::Integer *resulta; // [sp+78h] [bp+10h]@1

  resulta = result;
  v10 = this;
  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = -2i64;
  v8 = 0;
  CryptoPP::Integer::Integer(&b, 1);
  LODWORD(v4) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *))v10->vfptr[8].__vecDelDtor)(v10);
  CryptoPP::operator-(resulta, v4, &b);
  v8 |= 1u;
  CryptoPP::Integer::~Integer(&b);
  return resulta;
}
