/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAVCUnmannedTraderRegistItemInfo@@PEAV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@stdext@@YAPEAVCUnmannedTraderRegistItemInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@Z
 * Address: 0x14000A173
 */

CUnmannedTraderRegistItemInfo *__fastcall stdext::_Unchecked_uninitialized_move<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Dest, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo *,std::allocator<CUnmannedTraderRegistItemInfo>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
