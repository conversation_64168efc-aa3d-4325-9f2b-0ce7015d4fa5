/*
 * Function: ?GetDummyPostion@CMapData@@QEAAPEAU_dummy_position@@PEAD@Z
 * Address: 0x140186440
 */

_dummy_position *__fastcall CMapData::GetDummyPostion(CMapData *this, char *pszDummyCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _dummy_position *result; // rax@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMapData *v6; // [sp+30h] [bp+8h]@1
  char *Str1; // [sp+38h] [bp+10h]@1

  Str1 = pszDummyCode;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !strncmp(pszDummyCode, "dm", 2ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbMonDumPos, Str1);
  }
  else if ( !strncmp(Str1, "dp", 2ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbPortalDumPos, Str1);
  }
  else if ( !strncmp(Str1, "sd", 2ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbStoreDumPos, Str1);
  }
  else if ( !strncmp(Str1, "ds", 2ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbStartDumPos, Str1);
  }
  else if ( !strncmp(Str1, "bd", 2ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbBindDumPos, Str1);
  }
  else if ( !strncmp(Str1, "0dr", 3ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbResDumPosHigh, Str1);
  }
  else if ( !strncmp(Str1, "1dr", 3ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbResDumPosMiddle, Str1);
  }
  else if ( !strncmp(Str1, "2dr", 3ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbResDumPosLow, Str1);
  }
  else if ( !strncmp(Str1, "dq", 2ui64) )
  {
    result = CDummyPosTable::GetRecord(&v6->m_tbQuestDumPos, Str1);
  }
  else
  {
    result = 0i64;
  }
  return result;
}
