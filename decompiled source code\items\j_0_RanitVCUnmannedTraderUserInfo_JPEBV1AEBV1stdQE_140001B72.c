/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderUserInfo@@_JPEBV1@AEBV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140001B72
 */

void __fastcall std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::_<PERSON>t<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>(std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *this, std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *__that)
{
  std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>(
    this,
    __that);
}
