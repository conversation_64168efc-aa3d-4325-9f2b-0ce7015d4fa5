/*
 * Function: ?IsCashDBDSNSetted@CNationSettingData@@QEAA_NXZ
 * Address: 0x1402F2D00
 */

bool __fastcall CNationSettingData::IsCashDBDSNSetted(CNationSettingData *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  CNationSettingData *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5->m_eCashDBFlag == 1;
}
