/*
 * Function: ?erase@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@2@V32@@Z
 * Address: 0x14031D700
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::erase(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Where)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v5; // rax@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-A8h]@1
  char v9; // [sp+20h] [bp-88h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *resulta; // [sp+40h] [bp-68h]@4
  char v11; // [sp+48h] [bp-60h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v12; // [sp+68h] [bp-40h]@4
  int v13; // [sp+70h] [bp-38h]@4
  __int64 v14; // [sp+78h] [bp-30h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v15; // [sp+80h] [bp-28h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v16; // [sp+88h] [bp-20h]@4
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v17; // [sp+90h] [bp-18h]@4
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v18; // [sp+B0h] [bp+8h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v19; // [sp+B8h] [bp+10h]@1
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__that; // [sp+C0h] [bp+18h]@1

  __that = _Where;
  v19 = result;
  v18 = this;
  v3 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = -2i64;
  v13 = 0;
  resulta = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v9;
  v12 = (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v11;
  v5 = std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator+(
         _Where,
         (std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *)&v9,
         1i64);
  v15 = v5;
  v16 = v5;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(
    v12,
    __that);
  v17 = v6;
  std::deque<RECV_DATA,std::allocator<RECV_DATA>>::erase(v18, v19, v6, v16);
  v13 |= 1u;
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(__that);
  return v19;
}
