/*
 * Function: ?RemoveSlot@CCharacter@@QEAAHPEAV1@@Z
 * Address: 0x140173AA0
 */

signed __int64 __fastcall CCharacter::RemoveSlot(CCharacter *this, CCharacter *p)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CCharacter *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 5; ++j )
  {
    if ( v6->m_AroundSlot[j] == p )
    {
      v6->m_AroundSlot[j] = 0i64;
      --v6->m_AroundNum;
      return 1i64;
    }
  }
  return 0i64;
}
