/**
 * @file CGuildRoomSystemManager.h
 * @brief Modern C++20 Guild Room System Manager
 * 
 * This file provides comprehensive guild room system management with modern C++20 patterns,
 * proper initialization, map management, and modular design for handling all guild room operations.
 * 
 * Refactored from decompiled sources:
 * - InitCGuildRoomSystemQEAA_NXZ_1402E9A00.c (316 lines)
 * - 0CGuildRoomSystemAEAAXZ_1402E9610.c (22 lines)
 * - 1CGuildRoomSystemQEAAXZ_1402E9650.c (21 lines)
 * - RoomInCGuildRoomSystemQEAAHKHKZ_1402EA5F0.c (complex room entry logic)
 * - RoomOutCGuildRoomSystemQEAAHKHKZ_1402EA6C0.c (complex room exit logic)
 * - RentRoomCGuildRoomSystemQEAAEEEHKPEAUtagTIMESTAMP__1402EA230.c (room rental logic)
 */

#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <array>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>
#include <optional>
#include <string>
#include <string_view>

// Forward declarations for legacy compatibility
extern "C" {
    class CMapData;
    class CMapOperation;
    class CGuildRoomInfo;
    struct _portal_dummy;
    struct _dummy_position;
    struct tagTIMESTAMP_STRUCT;
    
    extern CMapOperation g_MapOper;
    extern CGuildRoomInfo* sm_neutal_map;
    extern _dummy_position* sm_neutral_hq_dummy;
    extern uint64_t qword_184A6F640;
    extern uint64_t qword_184A6F628;
    extern uint64_t qword_184A6F648;
    extern uint64_t qword_184A6F630;
}

namespace NexusProtection::Guild {

/**
 * @brief Guild room system operation result enumeration
 */
enum class GuildRoomResult : uint8_t {
    Success = 0,
    InitializationFailed,
    MapLoadingFailed,
    InvalidParameters,
    RoomNotAvailable,
    RoomAlreadyRented,
    InsufficientPermissions,
    DatabaseError,
    SystemError
};

/**
 * @brief Guild room system state enumeration
 */
enum class GuildRoomSystemState : uint8_t {
    Uninitialized = 0,
    Initializing,
    LoadingMaps,
    ConfiguringRooms,
    Running,
    Error,
    Shutdown
};

/**
 * @brief Race enumeration for guild rooms
 */
enum class GuildRace : uint8_t {
    Bellato = 0,
    Cora = 1,
    Accretia = 2,
    MaxRaces = 3
};

/**
 * @brief Room type enumeration
 */
enum class GuildRoomType : uint8_t {
    Standard = 0,
    Premium = 1,
    MaxTypes = 2
};

/**
 * @brief Guild room configuration structure
 */
struct GuildRoomConfig {
    uint32_t maxRoomsPerRace = 30;           // Maximum rooms per race
    uint32_t standardRoomsPerRace = 20;      // Standard rooms per race
    uint32_t premiumRoomsPerRace = 10;       // Premium rooms per race
    uint32_t totalRooms = 90;                // Total rooms (3 races * 30 rooms)
    
    // Room pricing and duration
    uint32_t standardRoomPrice = 5000000;    // Standard room price
    uint32_t standardRoomDuration = 604800;  // Standard room duration (7 days)
    uint32_t premiumRoomPrice = 20000000;    // Premium room price
    uint32_t premiumRoomDuration = 2592000;  // Premium room duration (30 days)
    
    // Room limits
    uint8_t standardRoomMaxMembers = 30;     // Standard room member limit
    uint8_t premiumRoomMaxMembers = 30;      // Premium room member limit
    
    // Validation
    bool IsValid() const {
        return maxRoomsPerRace > 0 && 
               standardRoomsPerRace + premiumRoomsPerRace <= maxRoomsPerRace &&
               totalRooms == static_cast<uint32_t>(GuildRace::MaxRaces) * maxRoomsPerRace;
    }
};

/**
 * @brief Guild room statistics
 */
struct GuildRoomStats {
    std::atomic<uint64_t> totalRoomsInitialized{0};
    std::atomic<uint64_t> totalRoomsRented{0};
    std::atomic<uint64_t> totalRoomEntries{0};
    std::atomic<uint64_t> totalRoomExits{0};
    std::atomic<uint64_t> activeRentals{0};
    std::atomic<uint64_t> mapLoadingErrors{0};
    std::atomic<uint64_t> roomOperationErrors{0};
    std::chrono::steady_clock::time_point startTime;
    
    GuildRoomStats() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetAverageOccupancy() const {
        uint64_t total = totalRoomsInitialized.load();
        return total > 0 ? static_cast<double>(activeRentals.load()) / total : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Map information structure
 */
struct MapInfo {
    std::string mapName;
    CMapData* mapData = nullptr;
    bool isLoaded = false;
    std::chrono::steady_clock::time_point loadTime;
    
    MapInfo() = default;
    MapInfo(std::string_view name) : mapName(name) {}
};

/**
 * @brief Room entry/exit information
 */
struct RoomAccessInfo {
    uint32_t guildSerial;
    uint32_t characterSerial;
    int roomIndex;
    std::chrono::steady_clock::time_point accessTime;
    bool isEntry;
    
    RoomAccessInfo(uint32_t guild, uint32_t character, int room, bool entry)
        : guildSerial(guild), characterSerial(character), roomIndex(room)
        , accessTime(std::chrono::steady_clock::now()), isEntry(entry) {}
};

/**
 * @brief Guild room event callback types
 */
using RoomInitializedCallback = std::function<void(int roomIndex, GuildRace race, GuildRoomType type)>;
using RoomRentedCallback = std::function<void(uint32_t guildSerial, int roomIndex, GuildRoomType type)>;
using RoomAccessCallback = std::function<void(const RoomAccessInfo& accessInfo)>;
using RoomErrorCallback = std::function<void(int roomIndex, const std::string& error)>;
using MapLoadedCallback = std::function<void(const std::string& mapName, bool success)>;

/**
 * @brief Modern C++20 Guild Room System Manager
 * 
 * Provides comprehensive guild room system management with proper error handling,
 * map loading, room configuration, and event callbacks.
 */
class CGuildRoomSystemManager {
public:
    /**
     * @brief Get singleton instance
     * 
     * @return Reference to the singleton instance
     */
    static CGuildRoomSystemManager& GetInstance();

    /**
     * @brief Initialize guild room system
     * 
     * @param config Room system configuration
     * @return GuildRoomResult indicating success or failure
     */
    GuildRoomResult Initialize(const GuildRoomConfig& config = {});

    /**
     * @brief Shutdown guild room system
     * 
     * @return GuildRoomResult indicating success or failure
     */
    GuildRoomResult Shutdown();

    /**
     * @brief Initialize guild room system (legacy compatibility)
     * 
     * Modern implementation of CGuildRoomSystem::Init function.
     * Loads all required maps and configures room system.
     * 
     * @return true if successful, false otherwise
     */
    bool Init();

    /**
     * @brief Get current system state
     * 
     * @return Current guild room system state
     */
    GuildRoomSystemState GetState() const { return m_state.load(); }

    /**
     * @brief Get system statistics
     * 
     * @return Copy of current statistics
     */
    GuildRoomStats GetStatistics() const;

    /**
     * @brief Get system configuration
     * 
     * @return Copy of current configuration
     */
    GuildRoomConfig GetConfiguration() const;

    /**
     * @brief Update system configuration
     * 
     * @param config New configuration
     * @return true if successful, false otherwise
     */
    bool UpdateConfiguration(const GuildRoomConfig& config);

    /**
     * @brief Room entry management
     * 
     * @param guildSerial Guild serial number
     * @param roomIndex Room index
     * @param characterSerial Character serial number
     * @return Room entry result code
     */
    int RoomIn(uint32_t guildSerial, int roomIndex, uint32_t characterSerial);

    /**
     * @brief Room exit management
     * 
     * @param guildSerial Guild serial number
     * @param roomIndex Room index
     * @param characterSerial Character serial number
     * @return Room exit result code
     */
    int RoomOut(uint32_t guildSerial, int roomIndex, uint32_t characterSerial);

    /**
     * @brief Rent a guild room
     * 
     * @param race Guild race
     * @param roomType Room type (standard/premium)
     * @param guildIndex Guild index
     * @param guildSerial Guild serial number
     * @param timestamp Rental timestamp
     * @param isRestore Whether this is a restoration from database
     * @return Rental result code
     */
    uint8_t RentRoom(GuildRace race, GuildRoomType roomType, int guildIndex, 
                     uint32_t guildSerial, tagTIMESTAMP_STRUCT* timestamp, bool isRestore = false);

    /**
     * @brief Get map data for race and room type
     * 
     * @param race Guild race
     * @param roomType Room type
     * @return Map data pointer or nullptr
     */
    CMapData* GetMapData(GuildRace race, GuildRoomType roomType) const;

    /**
     * @brief Get map position for guild
     * 
     * @param guildSerial Guild serial number
     * @param position Output position array
     * @param mapData Output map data pointer
     * @param mapLayer Output map layer
     * @param roomType Output room type
     * @return true if successful, false otherwise
     */
    bool GetMapPos(uint32_t guildSerial, float* position, CMapData** mapData, 
                   uint16_t* mapLayer, uint8_t* roomType) const;

    /**
     * @brief Set event callbacks
     */
    void SetRoomInitializedCallback(RoomInitializedCallback callback);
    void SetRoomRentedCallback(RoomRentedCallback callback);
    void SetRoomAccessCallback(RoomAccessCallback callback);
    void SetRoomErrorCallback(RoomErrorCallback callback);
    void SetMapLoadedCallback(MapLoadedCallback callback);

    /**
     * @brief Check if system is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized.load(); }

    /**
     * @brief Get active room count
     * 
     * @return Number of currently rented rooms
     */
    uint32_t GetActiveRoomCount() const;

    /**
     * @brief Reset system statistics
     */
    void ResetStatistics();

private:
    // Singleton pattern
    CGuildRoomSystemManager() = default;
    ~CGuildRoomSystemManager() = default;
    CGuildRoomSystemManager(const CGuildRoomSystemManager&) = delete;
    CGuildRoomSystemManager& operator=(const CGuildRoomSystemManager&) = delete;
    CGuildRoomSystemManager(CGuildRoomSystemManager&&) = delete;
    CGuildRoomSystemManager& operator=(CGuildRoomSystemManager&&) = delete;

    /**
     * @brief Load all required maps
     * 
     * @return true if successful, false otherwise
     */
    bool LoadAllMaps();

    /**
     * @brief Load map by name
     * 
     * @param mapName Map name to load
     * @return Map data pointer or nullptr
     */
    CMapData* LoadMap(std::string_view mapName);

    /**
     * @brief Configure room system
     * 
     * @return true if successful, false otherwise
     */
    bool ConfigureRoomSystem();

    /**
     * @brief Initialize room configurations
     *
     * @return true if successful, false otherwise
     */
    bool InitializeRoomConfigurations();

    /**
     * @brief Load portal dummy positions
     *
     * @return true if successful, false otherwise
     */
    bool LoadPortalDummies();

    /**
     * @brief Set room configuration parameters
     */
    void SetRoomConfiguration();

    /**
     * @brief Configure individual room
     *
     * @param roomIndex Room index
     * @param race Guild race
     * @param roomType Room type
     * @param layerIndex Map layer index
     */
    void ConfigureRoom(uint32_t roomIndex, GuildRace race, GuildRoomType roomType, uint32_t layerIndex);

    /**
     * @brief Get portal dummy from map
     *
     * @param mapData Map data pointer
     * @param portalName Portal name
     * @return Portal dummy pointer or nullptr
     */
    _portal_dummy* GetPortal(CMapData* mapData, const char* portalName);

    /**
     * @brief Validate map loading
     *
     * @return true if all maps loaded successfully, false otherwise
     */
    bool ValidateMapLoading() const;

    /**
     * @brief Log error message
     *
     * @param context Error context
     * @param message Error message
     */
    void LogError(std::string_view context, std::string_view message) const;

    /**
     * @brief Notify event callbacks
     */
    void NotifyRoomInitialized(int roomIndex, GuildRace race, GuildRoomType type);
    void NotifyRoomRented(uint32_t guildSerial, int roomIndex, GuildRoomType type);
    void NotifyRoomAccess(const RoomAccessInfo& accessInfo);
    void NotifyRoomError(int roomIndex, const std::string& error);
    void NotifyMapLoaded(const std::string& mapName, bool success);

    // Member variables
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    std::atomic<GuildRoomSystemState> m_state{GuildRoomSystemState::Uninitialized};
    
    GuildRoomConfig m_config;
    GuildRoomStats m_stats;
    
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    mutable std::mutex m_mapMutex;
    
    // Map storage: [race][roomType] = MapData*
    std::array<std::array<CMapData*, static_cast<size_t>(GuildRoomType::MaxTypes)>, 
               static_cast<size_t>(GuildRace::MaxRaces)> m_roomMaps{};
    
    // Map information storage
    std::unordered_map<std::string, MapInfo> m_mapInfo;
    
    // Neutral map data
    CMapData* m_neutralMapBellato = nullptr;
    CMapData* m_neutralMapCora = nullptr;
    CMapData* m_neutralMapAccretia = nullptr;
    
    // Neutral HQ dummy positions
    _dummy_position* m_neutralHQDummyBellato = nullptr;
    _dummy_position* m_neutralHQDummyCora = nullptr;
    _dummy_position* m_neutralHQDummyAccretia = nullptr;
    
    // Event callbacks
    RoomInitializedCallback m_roomInitializedCallback;
    RoomRentedCallback m_roomRentedCallback;
    RoomAccessCallback m_roomAccessCallback;
    RoomErrorCallback m_roomErrorCallback;
    MapLoadedCallback m_mapLoadedCallback;
    
    // Error tracking
    std::chrono::steady_clock::time_point m_lastErrorTime;
    std::atomic<uint32_t> m_consecutiveErrors{0};
};

} // namespace NexusProtection::Guild
