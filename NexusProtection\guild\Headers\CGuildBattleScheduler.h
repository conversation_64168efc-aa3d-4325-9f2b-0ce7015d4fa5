#pragma once

/**
 * @file CGuildBattleScheduler.h
 * @brief Modern C++20 Guild Battle Scheduler System
 * 
 * This file provides comprehensive guild battle scheduling system with
 * time management, conflict resolution, and automated coordination.
 * 
 * Refactored from decompiled sources related to guild battle scheduling,
 * time management, and battle coordination systems.
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <queue>
#include <optional>

// Forward declarations
class CGuild;

namespace NexusProtection {
namespace Guild {

// Forward declarations
class CGuildBattleManager;

/**
 * @brief Schedule status enumeration
 */
enum class ScheduleStatus : uint8_t {
    Pending = 0,
    Confirmed = 1,
    Active = 2,
    Completed = 3,
    Cancelled = 4,
    Expired = 5
};

/**
 * @brief Schedule priority enumeration
 */
enum class SchedulePriority : uint8_t {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
    Championship = 4
};

/**
 * @brief Time slot availability
 */
enum class TimeSlotAvailability : uint8_t {
    Available = 0,
    Reserved = 1,
    Occupied = 2,
    Maintenance = 3
};

/**
 * @brief Guild battle schedule entry
 */
struct GuildBattleSchedule {
    uint32_t scheduleId{0};
    uint32_t battleId{0};
    
    // Participating guilds
    uint32_t sourceGuildId{0};
    uint32_t targetGuildId{0};
    std::string sourceGuildName;
    std::string targetGuildName;
    
    // Timing
    std::chrono::system_clock::time_point scheduledTime;
    std::chrono::system_clock::time_point createdTime;
    std::chrono::minutes duration{60};
    std::chrono::minutes preparationTime{10};
    
    // Battle configuration
    uint32_t mapId{0};
    uint8_t battleNumber{0};
    SchedulePriority priority{SchedulePriority::Normal};
    ScheduleStatus status{ScheduleStatus::Pending};
    
    // Metadata
    std::string description;
    std::unordered_map<std::string, std::string> metadata;
    
    GuildBattleSchedule() {
        auto now = std::chrono::system_clock::now();
        createdTime = now;
        scheduledTime = now + std::chrono::hours(1); // Default to 1 hour from now
    }
    
    GuildBattleSchedule(uint32_t srcGuild, uint32_t tgtGuild, 
                       std::chrono::system_clock::time_point time)
        : sourceGuildId(srcGuild), targetGuildId(tgtGuild), scheduledTime(time) {
        createdTime = std::chrono::system_clock::now();
    }
    
    bool IsValid() const {
        return scheduleId > 0 && sourceGuildId > 0 && targetGuildId > 0 &&
               sourceGuildId != targetGuildId && mapId > 0 &&
               scheduledTime > createdTime;
    }
    
    std::chrono::system_clock::time_point GetEndTime() const {
        return scheduledTime + duration;
    }
    
    std::chrono::system_clock::time_point GetPreparationStartTime() const {
        return scheduledTime - preparationTime;
    }
    
    bool IsActive() const {
        auto now = std::chrono::system_clock::now();
        return now >= GetPreparationStartTime() && now <= GetEndTime();
    }
    
    bool IsExpired() const {
        return std::chrono::system_clock::now() > GetEndTime();
    }
};

/**
 * @brief Time slot information
 */
struct TimeSlot {
    std::chrono::system_clock::time_point startTime;
    std::chrono::system_clock::time_point endTime;
    TimeSlotAvailability availability{TimeSlotAvailability::Available};
    uint32_t scheduleId{0};
    uint32_t mapId{0};
    
    TimeSlot() = default;
    TimeSlot(std::chrono::system_clock::time_point start, std::chrono::system_clock::time_point end)
        : startTime(start), endTime(end) {}
    
    bool OverlapsWith(const TimeSlot& other) const {
        return startTime < other.endTime && endTime > other.startTime;
    }
    
    bool Contains(std::chrono::system_clock::time_point time) const {
        return time >= startTime && time < endTime;
    }
    
    std::chrono::minutes GetDuration() const {
        return std::chrono::duration_cast<std::chrono::minutes>(endTime - startTime);
    }
};

/**
 * @brief Schedule conflict information
 */
struct ScheduleConflict {
    uint32_t scheduleId1{0};
    uint32_t scheduleId2{0};
    std::string conflictType;
    std::string description;
    std::chrono::system_clock::time_point detectedTime;
    
    ScheduleConflict() : detectedTime(std::chrono::system_clock::now()) {}
    ScheduleConflict(uint32_t id1, uint32_t id2, const std::string& type, const std::string& desc)
        : scheduleId1(id1), scheduleId2(id2), conflictType(type), description(desc)
        , detectedTime(std::chrono::system_clock::now()) {}
};

/**
 * @brief Scheduler statistics
 */
struct SchedulerStatistics {
    std::atomic<uint64_t> totalSchedules{0};
    std::atomic<uint64_t> activeSchedules{0};
    std::atomic<uint64_t> completedSchedules{0};
    std::atomic<uint64_t> cancelledSchedules{0};
    std::atomic<uint64_t> conflictsDetected{0};
    std::atomic<uint64_t> conflictsResolved{0};
    std::chrono::steady_clock::time_point startTime;
    
    SchedulerStatistics() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetCompletionRate() const {
        uint64_t total = totalSchedules.load();
        return total > 0 ? (static_cast<double>(completedSchedules.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Scheduler configuration
 */
struct SchedulerConfig {
    // Time settings
    std::chrono::minutes defaultBattleDuration{60};
    std::chrono::minutes defaultPreparationTime{10};
    std::chrono::minutes minAdvanceBooking{30};
    std::chrono::hours maxAdvanceBooking{168}; // 1 week
    
    // Capacity settings
    uint32_t maxConcurrentBattles{10};
    uint32_t maxSchedulesPerGuild{5};
    uint32_t maxDailyBattles{20};
    
    // Conflict resolution
    bool autoResolveConflicts{true};
    bool allowOverlappingSchedules{false};
    SchedulePriority minPriorityForOverride{SchedulePriority::High};
    
    // Maintenance windows
    std::vector<TimeSlot> maintenanceWindows;
    
    SchedulerConfig() = default;
    
    bool IsValid() const {
        return defaultBattleDuration.count() > 0 &&
               defaultPreparationTime.count() > 0 &&
               minAdvanceBooking.count() > 0 &&
               maxAdvanceBooking > minAdvanceBooking &&
               maxConcurrentBattles > 0;
    }
};

/**
 * @brief Schedule event callback function type
 */
using ScheduleEventCallback = std::function<void(uint32_t scheduleId, ScheduleStatus status)>;

/**
 * @brief Modern C++20 Guild Battle Scheduler class
 * 
 * This class provides comprehensive guild battle scheduling system with
 * time management, conflict resolution, and automated coordination.
 */
class CGuildBattleScheduler {
public:
    // Constructor and Destructor
    CGuildBattleScheduler();
    explicit CGuildBattleScheduler(const SchedulerConfig& config);
    virtual ~CGuildBattleScheduler();

    // Disable copy constructor and assignment operator
    CGuildBattleScheduler(const CGuildBattleScheduler&) = delete;
    CGuildBattleScheduler& operator=(const CGuildBattleScheduler&) = delete;

    // Enable move constructor and assignment operator
    CGuildBattleScheduler(CGuildBattleScheduler&&) noexcept = default;
    CGuildBattleScheduler& operator=(CGuildBattleScheduler&&) noexcept = default;

    /**
     * @brief Initialize scheduler
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown scheduler
     */
    void Shutdown();

    /**
     * @brief Load scheduler data
     * 
     * @param currentDay Current day
     * @param mapCount Map count
     * @param today Today value
     * @param todayDayId Today day ID
     * @param tomorrow Tomorrow value
     * @param tomorrowDayId Tomorrow day ID
     * @return true if loading successful, false otherwise
     */
    bool LoadData(int currentDay, uint32_t mapCount, int today, int todayDayId, 
                 int tomorrow, int tomorrowDayId);

    /**
     * @brief Update scheduler
     * 
     * @param deltaTime Time elapsed since last update
     */
    void Update(float deltaTime);

    /**
     * @brief Create new schedule
     * 
     * @param sourceGuildId Source guild ID
     * @param targetGuildId Target guild ID
     * @param scheduledTime Scheduled time
     * @param mapId Map ID
     * @param priority Schedule priority
     * @return Schedule ID if successful, 0 if failed
     */
    uint32_t CreateSchedule(uint32_t sourceGuildId, uint32_t targetGuildId,
                           std::chrono::system_clock::time_point scheduledTime,
                           uint32_t mapId, SchedulePriority priority = SchedulePriority::Normal);

    /**
     * @brief Cancel schedule
     * 
     * @param scheduleId Schedule ID
     * @param reason Cancellation reason
     * @return true if cancelled successfully, false otherwise
     */
    bool CancelSchedule(uint32_t scheduleId, const std::string& reason);

    /**
     * @brief Confirm schedule
     * 
     * @param scheduleId Schedule ID
     * @return true if confirmed successfully, false otherwise
     */
    bool ConfirmSchedule(uint32_t scheduleId);

    /**
     * @brief Get schedule
     * 
     * @param scheduleId Schedule ID
     * @return Schedule if found, nullopt otherwise
     */
    std::optional<GuildBattleSchedule> GetSchedule(uint32_t scheduleId) const;

    /**
     * @brief Get schedules for guild
     * 
     * @param guildId Guild ID
     * @return Vector of schedules for the guild
     */
    std::vector<GuildBattleSchedule> GetSchedulesForGuild(uint32_t guildId) const;

    /**
     * @brief Get active schedules
     * 
     * @return Vector of currently active schedules
     */
    std::vector<GuildBattleSchedule> GetActiveSchedules() const;

    /**
     * @brief Check time slot availability
     * 
     * @param startTime Start time
     * @param duration Duration
     * @param mapId Map ID (0 for any map)
     * @return Time slot availability
     */
    TimeSlotAvailability CheckTimeSlotAvailability(std::chrono::system_clock::time_point startTime,
                                                  std::chrono::minutes duration, uint32_t mapId = 0) const;

    /**
     * @brief Find available time slots
     * 
     * @param duration Required duration
     * @param startSearch Start of search window
     * @param endSearch End of search window
     * @param mapId Map ID (0 for any map)
     * @return Vector of available time slots
     */
    std::vector<TimeSlot> FindAvailableTimeSlots(std::chrono::minutes duration,
                                               std::chrono::system_clock::time_point startSearch,
                                               std::chrono::system_clock::time_point endSearch,
                                               uint32_t mapId = 0) const;

    /**
     * @brief Detect schedule conflicts
     * 
     * @return Vector of detected conflicts
     */
    std::vector<ScheduleConflict> DetectConflicts() const;

    /**
     * @brief Resolve schedule conflicts
     * 
     * @param conflicts Conflicts to resolve
     * @return Number of conflicts resolved
     */
    size_t ResolveConflicts(const std::vector<ScheduleConflict>& conflicts);

    /**
     * @brief Add maintenance window
     * 
     * @param startTime Start time
     * @param endTime End time
     * @param description Description
     * @return true if added successfully, false otherwise
     */
    bool AddMaintenanceWindow(std::chrono::system_clock::time_point startTime,
                             std::chrono::system_clock::time_point endTime,
                             const std::string& description);

    /**
     * @brief Remove maintenance window
     * 
     * @param startTime Start time
     * @return true if removed successfully, false otherwise
     */
    bool RemoveMaintenanceWindow(std::chrono::system_clock::time_point startTime);

    /**
     * @brief Get scheduler statistics
     * 
     * @return Current scheduler statistics
     */
    const SchedulerStatistics& GetStatistics() const { return m_stats; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Get scheduler configuration
     * 
     * @return Current configuration
     */
    const SchedulerConfig& GetConfig() const { return m_config; }

    /**
     * @brief Set scheduler configuration
     * 
     * @param config New configuration
     */
    void SetConfig(const SchedulerConfig& config);

    /**
     * @brief Register schedule event callback
     * 
     * @param callback Callback function
     */
    void RegisterEventCallback(ScheduleEventCallback callback);

    /**
     * @brief Get scheduler report
     * 
     * @return Detailed scheduler status report
     */
    std::string GetSchedulerReport() const;

    /**
     * @brief Check if scheduler is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized; }

    // Singleton access (for legacy compatibility)
    static CGuildBattleScheduler& Instance();
    static void SetInstance(std::unique_ptr<CGuildBattleScheduler> instance);

protected:
    // Configuration
    SchedulerConfig m_config;
    
    // Schedule management
    std::unordered_map<uint32_t, GuildBattleSchedule> m_schedules;
    std::priority_queue<std::pair<std::chrono::system_clock::time_point, uint32_t>,
                       std::vector<std::pair<std::chrono::system_clock::time_point, uint32_t>>,
                       std::greater<>> m_schedulePriorityQueue;
    
    // Time slot management
    std::vector<TimeSlot> m_timeSlots;
    std::vector<TimeSlot> m_maintenanceWindows;
    
    // Conflict tracking
    std::vector<ScheduleConflict> m_conflicts;
    
    // Statistics
    SchedulerStatistics m_stats;
    
    // Event callback
    ScheduleEventCallback m_eventCallback;
    
    // Synchronization
    mutable std::mutex m_schedulesMutex;
    mutable std::mutex m_timeslotsMutex;
    mutable std::mutex m_conflictsMutex;
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    std::atomic<uint32_t> m_nextScheduleId{1};
    
    // Timing
    std::chrono::steady_clock::time_point m_lastUpdate;
    
    // Singleton instance
    static std::unique_ptr<CGuildBattleScheduler> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Process pending schedules
     */
    void ProcessPendingSchedules();

    /**
     * @brief Update active schedules
     */
    void UpdateActiveSchedules();

    /**
     * @brief Cleanup expired schedules
     */
    void CleanupExpiredSchedules();

    /**
     * @brief Validate schedule
     * 
     * @param schedule Schedule to validate
     * @return true if valid, false otherwise
     */
    bool ValidateSchedule(const GuildBattleSchedule& schedule) const;

    /**
     * @brief Check for schedule conflicts
     * 
     * @param schedule Schedule to check
     * @return Vector of conflicting schedule IDs
     */
    std::vector<uint32_t> CheckForConflicts(const GuildBattleSchedule& schedule) const;

    /**
     * @brief Generate unique schedule ID
     * 
     * @return Unique schedule identifier
     */
    uint32_t GenerateScheduleId();

    /**
     * @brief Notify schedule event
     * 
     * @param scheduleId Schedule ID
     * @param status New status
     */
    void NotifyScheduleEvent(uint32_t scheduleId, ScheduleStatus status);

    /**
     * @brief Update time slots
     */
    void UpdateTimeSlots();

    /**
     * @brief Check maintenance window overlap
     * 
     * @param startTime Start time
     * @param endTime End time
     * @return true if overlaps with maintenance, false otherwise
     */
    bool CheckMaintenanceOverlap(std::chrono::system_clock::time_point startTime,
                                std::chrono::system_clock::time_point endTime) const;
};

/**
 * @brief Guild Battle Scheduler Factory
 */
class CGuildBattleSchedulerFactory {
public:
    /**
     * @brief Create scheduler with default configuration
     * 
     * @return Unique pointer to scheduler
     */
    static std::unique_ptr<CGuildBattleScheduler> CreateDefaultScheduler();

    /**
     * @brief Create scheduler with custom configuration
     * 
     * @param config Scheduler configuration
     * @return Unique pointer to scheduler
     */
    static std::unique_ptr<CGuildBattleScheduler> CreateScheduler(const SchedulerConfig& config);
};

/**
 * @brief Guild battle scheduler utility functions
 */
namespace SchedulerUtils {
    std::string ScheduleStatusToString(ScheduleStatus status);
    std::string SchedulePriorityToString(SchedulePriority priority);
    std::string TimeSlotAvailabilityToString(TimeSlotAvailability availability);
    ScheduleStatus StringToScheduleStatus(const std::string& statusStr);
    SchedulePriority StringToSchedulePriority(const std::string& priorityStr);
    TimeSlotAvailability StringToTimeSlotAvailability(const std::string& availabilityStr);
    std::string FormatSchedule(const GuildBattleSchedule& schedule);
    std::string FormatTimeSlot(const TimeSlot& timeSlot);
}

} // namespace Guild
} // namespace NexusProtection
