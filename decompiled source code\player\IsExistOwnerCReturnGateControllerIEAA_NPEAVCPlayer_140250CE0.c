/*
 * Function: ?IsExistOwner@CReturnGateController@@IEAA_NPEAVCPlayer@@@Z
 * Address: 0x140250CE0
 */

char __fastcall CReturnGateController::IsExistOwner(CReturnGateController *this, CPlayer *pkObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  CNetIndexList *j; // [sp+20h] [bp-18h]@6
  CReturnGateController *v7; // [sp+40h] [bp+8h]@1
  CPlayer *v8; // [sp+48h] [bp+10h]@1

  v8 = pkObj;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CNetIndexList::size(v7->m_pkUseInxList) > 0 )
  {
    CNetCriticalSection::Lock(&v7->m_pkUseInxList->m_csList);
    for ( j = (CNetIndexList *)v7->m_pkUseInxList->m_Head.m_pNext;
          j != (CNetIndexList *)((char *)v7->m_pkUseInxList + 24);
          j = (CNetIndexList *)j->m_Head.m_pNext )
    {
      if ( CReturnGate::GetOwner(v7->m_ppkGatePool[j->m_Head.m_dwIndex]) == v8 )
      {
        CNetCriticalSection::Unlock(&v7->m_pkUseInxList->m_csList);
        return 1;
      }
    }
    CNetCriticalSection::Unlock(&v7->m_pkUseInxList->m_csList);
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
