/*
 * Function: ?GetCRecordData_SetItem@CSUItemSystem@@QEAAPEAVCRecordData@@XZ
 * Address: 0x1402E4680
 */

CSUItemSystem *__fastcall CSUItemSystem::GetCRecordData_SetItem(CSUItemSystem *this)
{
  CSUItemSystem *result; // rax@2

  if ( this->m_bySUItemCount )
  {
    if ( this->m_bLoadData[0] )
    {
      if ( this->m_bChangeData[0] )
        result = this;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
