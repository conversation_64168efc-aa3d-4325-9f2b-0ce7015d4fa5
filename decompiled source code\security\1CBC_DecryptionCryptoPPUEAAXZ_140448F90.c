/*
 * Function: ??1CBC_Decryption@CryptoPP@@UEAA@XZ
 * Address: 0x140448F90
 */

void __fastcall CryptoPP::CBC_Decryption::~CBC_Decryption(CryptoPP::CBC_Decryption *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::CBC_Decryption *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v5->m_temp);
  CryptoPP::CBC_ModeBase::~CBC_ModeBase((CryptoPP::CBC_ModeBase *)&v5->vfptr);
}
