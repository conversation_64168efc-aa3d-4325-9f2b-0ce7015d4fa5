/*
 * Function: _CMoveMapLimitInfoList::Loop_::_1_::dtor$2
 * Address: 0x1403A5690
 */

void __fastcall CMoveMapLimitInfoList::Loop_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>((std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)(a2 + 168));
}
