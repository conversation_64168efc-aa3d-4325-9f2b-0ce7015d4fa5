/*
 * Function: ?CreateCharacterSelectLogTable@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404A0BE0
 */

bool __fastcall CRFWorldDatabase::CreateCharacterSelectLogTable(CRFWorldDatabase *this, char *szTableName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-CA8h]@1
  char *v6; // [sp+20h] [bp-C88h]@4
  char *v7; // [sp+28h] [bp-C80h]@4
  char *v8; // [sp+30h] [bp-C78h]@4
  char *v9; // [sp+38h] [bp-C70h]@4
  char *v10; // [sp+40h] [bp-C68h]@4
  char *v11; // [sp+48h] [bp-C60h]@4
  char *v12; // [sp+50h] [bp-C58h]@4
  char *v13; // [sp+58h] [bp-C50h]@4
  char *v14; // [sp+60h] [bp-C48h]@4
  char *v15; // [sp+68h] [bp-C40h]@4
  char Dst; // [sp+80h] [bp-C28h]@4
  unsigned __int64 v17; // [sp+C90h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+CB0h] [bp+8h]@1
  char *v19; // [sp+CB8h] [bp+10h]@1

  v19 = szTableName;
  v18 = this;
  v2 = &v5;
  for ( i = 808i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = (unsigned __int64)&v5 ^ _security_cookie;
  memset_0(&Dst, 0, 0xC00ui64);
  v15 = v19;
  v14 = v19;
  v13 = v19;
  v12 = v19;
  v11 = v19;
  v10 = v19;
  v9 = v19;
  v8 = v19;
  v7 = v19;
  v6 = v19;
  sprintf(
    &Dst,
    "CREATE TABLE [dbo].[%s] ([ID] [int] IDENTITY (1, 1) NOT NULL ,[AccountSerial] [int] NOT NULL ,[Account] [varchar] (1"
    "7) NOT NULL ,[CharacSerial] [int] NOT NULL ,[CharacName] [varchar] (17) NOT NULL ,[LogDate] [datetime] NOT NULL ) ON"
    " [PRIMARY] ALTER TABLE [dbo].[%s] WITH NOCHECK ADD CONSTRAINT [PK_%s] PRIMARY KEY  CLUSTERED ([ID]) ON [PRIMARY] ALT"
    "ER TABLE [dbo].[%s] WITH NOCHECK ADD CONSTRAINT [DF_%s_AccountSerial] DEFAULT (0) FOR [AccountSerial], CONSTRAINT [D"
    "F_%s_Account] DEFAULT ('*') FOR [Account], CONSTRAINT [DF_%s_CharacName] DEFAULT ('*') FOR [CharacName], CONSTRAINT "
    "[DF_%s_LogDate] DEFAULT (0) FOR [LogDate] CREATE  INDEX [IX_%s_account_logdate] ON [dbo].[%s]([Account], [LogDate]) "
    "ON [PRIMARY] CREATE  INDEX [IX_%s_characname_logdate] ON [dbo].[%s]([CharacName], [LogDate]) ON [PRIMARY] ",
    v19,
    v19);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v18->vfptr, &Dst, 1);
}
