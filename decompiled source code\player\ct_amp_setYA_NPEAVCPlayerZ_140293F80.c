/*
 * Function: ?ct_amp_set@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293F80
 */

char __fastcall ct_amp_set(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int dwDelay; // [sp+20h] [bp-18h]@10
  unsigned int dwDS; // [sp+24h] [bp-14h]@10
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( v7->m_bOper )
    {
      if ( AutominePersonal::is_installed(v7->m_Param.m_pAPM) )
      {
        dwDelay = atoi(s_pwszDstCheat[0]);
        dwDS = atoi(s_pwszDstCheat[1]);
        if ( dwDelay )
          AutominePersonal::set_delay(v7->m_Param.m_pAPM, dwDelay);
        if ( dwDS )
          AutominePersonal::set_delaysec(v7->m_Param.m_pAPM, dwDS);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
