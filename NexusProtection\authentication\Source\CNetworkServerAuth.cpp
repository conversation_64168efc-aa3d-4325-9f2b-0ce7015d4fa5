/**
 * @file CNetworkServerAuth.cpp
 * @brief Network Server Login Authentication System Implementation
 * 
 * Provides secure authentication for control server and web agent server login operations.
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "CNetworkServerAuth.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cassert>

namespace NexusProtection::Authentication {

    // Global instance
    static std::unique_ptr<CNetworkServerAuth> g_pNetworkServerAuth = nullptr;
    static std::mutex g_instanceMutex;

    // External network process handle (equivalent to unk_1414F2098)
    extern void* unk_1414F2098;

    // ServerConnectionInfo implementation

    bool ServerConnectionInfo::IsValid() const {
        return sessionId > 0 && serverType != ServerType::Unknown;
    }

    std::string ServerConnectionInfo::ToString() const {
        std::ostringstream oss;
        oss << "ServerConnection{";
        oss << "SessionId: " << sessionId << ", ";
        oss << "Type: " << ServerTypeToString(serverType) << ", ";
        oss << "State: " << ServerAuthStateToString(authState) << ", ";
        oss << "Address: " << serverAddress << ":" << serverPort << ", ";
        oss << "Authenticated: " << (isAuthenticated ? "Yes" : "No");
        oss << "}";
        return oss.str();
    }

    void ServerConnectionInfo::UpdateActivity() {
        lastActivity = std::chrono::steady_clock::now();
    }

    // NetworkAuthMessage implementation

    bool NetworkAuthMessage::IsValid() const {
        return version == 1 && (messageType == static_cast<uint8_t>(NetworkMessageType::ControlServerLogin) ||
                               messageType == static_cast<uint8_t>(NetworkMessageType::WebAgentServerLogin) ||
                               messageType == static_cast<uint8_t>(NetworkMessageType::AuthenticationResponse) ||
                               messageType == static_cast<uint8_t>(NetworkMessageType::AuthenticationFailure));
    }

    std::vector<uint8_t> NetworkAuthMessage::Serialize() const {
        return {messageType, version, status, reserved};
    }

    bool NetworkAuthMessage::Deserialize(const std::vector<uint8_t>& data) {
        if (data.size() < 4) {
            return false;
        }
        messageType = data[0];
        version = data[1];
        status = data[2];
        reserved = data[3];
        return IsValid();
    }

    // CNetProcess implementation
    void* CNetProcess::LoadSendMsg(void* process, int sessionId, const void* messageType, 
                                 const void* messageData, uint32_t messageSize) {
        // This is a placeholder implementation
        // In the actual system, this would interface with the real network process
        std::cout << "[DEBUG] CNetProcess::LoadSendMsg called for session " << sessionId
                  << ", size: " << messageSize << std::endl;
        return reinterpret_cast<void*>(1); // Return non-null to indicate success
    }

    // CNetworkServerAuth implementation

    CNetworkServerAuth::CNetworkServerAuth() {
        m_statistics.startTime = std::chrono::steady_clock::now();
        m_networkProcessHandle = &unk_1414F2098;
    }

    CNetworkServerAuth::~CNetworkServerAuth() {
        Shutdown();
    }

    bool CNetworkServerAuth::Initialize() {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        if (m_isInitialized) {
            return true;
        }

        try {
            // Initialize network process handle
            if (!m_networkProcessHandle) {
                m_networkProcessHandle = &unk_1414F2098;
            }

            // Reset server states
            SetControlServerState(false);
            SetWebAgentServerState(false);

            // Clear any existing connections
            m_serverConnections.clear();

            m_isOperational = true;
            m_isInitialized = true;

            std::cout << "[INFO] CNetworkServerAuth initialized successfully" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to initialize CNetworkServerAuth: " << e.what() << std::endl;
            return false;
        }
    }

    void CNetworkServerAuth::Shutdown() {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        if (!m_isInitialized) {
            return;
        }

        // Disconnect all servers
        SetControlServerState(false);
        SetWebAgentServerState(false);

        // Clear all connections
        m_serverConnections.clear();

        m_isOperational = false;
        m_isInitialized = false;
        
        std::cout << "[INFO] CNetworkServerAuth shutdown completed" << std::endl;
    }

    bool CNetworkServerAuth::LoadConfiguration() {
        // Load configuration from file or database
        // For now, use default configuration
        return true;
    }

    NetworkServerAuthResult CNetworkServerAuth::AuthenticateControlServer(CNetworkEX* networkEX, int sessionId, char* buffer) {
        try {
            std::cout << "[INFO] Authenticating control server for session: " << sessionId << std::endl;
            
            if (!ValidateParameters(networkEX, sessionId, buffer)) {
                UpdateStatistics(false, ServerType::ControlServer);
                return NetworkServerAuthResult::InvalidParameters;
            }

            // Register server connection
            RegisterServerConnection(sessionId, ServerType::ControlServer);

            // Process authentication based on original logic
            bool authResult = ProcessControlServerAuthentication(sessionId, buffer);
            
            UpdateStatistics(authResult, ServerType::ControlServer);
            LogAuthenticationEvent("Control server authentication", sessionId, ServerType::ControlServer, authResult);
            
            return authResult ? NetworkServerAuthResult::Success : NetworkServerAuthResult::AuthenticationFailed;
            
        } catch (const std::exception& e) {
            UpdateStatistics(false, ServerType::ControlServer);
            LogAuthenticationEvent("Control server authentication error: " + std::string(e.what()), 
                                 sessionId, ServerType::ControlServer, false);
            return NetworkServerAuthResult::SystemError;
        }
    }

    NetworkServerAuthResult CNetworkServerAuth::AuthenticateWebAgentServer(CNetworkEX* networkEX, int sessionId, char* buffer) {
        try {
            std::cout << "[INFO] Authenticating web agent server for session: " << sessionId << std::endl;
            
            if (!ValidateParameters(networkEX, sessionId, buffer)) {
                UpdateStatistics(false, ServerType::WebAgentServer);
                return NetworkServerAuthResult::InvalidParameters;
            }

            // Register server connection
            RegisterServerConnection(sessionId, ServerType::WebAgentServer);

            // Process authentication based on original logic
            bool authResult = ProcessWebAgentServerAuthentication(sessionId, buffer);
            
            UpdateStatistics(authResult, ServerType::WebAgentServer);
            LogAuthenticationEvent("Web agent server authentication", sessionId, ServerType::WebAgentServer, authResult);
            
            return authResult ? NetworkServerAuthResult::Success : NetworkServerAuthResult::AuthenticationFailed;
            
        } catch (const std::exception& e) {
            UpdateStatistics(false, ServerType::WebAgentServer);
            LogAuthenticationEvent("Web agent server authentication error: " + std::string(e.what()), 
                                 sessionId, ServerType::WebAgentServer, false);
            return NetworkServerAuthResult::SystemError;
        }
    }

    bool CNetworkServerAuth::RegisterServerConnection(int sessionId, ServerType serverType, const std::string& address) {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        auto connection = std::make_unique<ServerConnectionInfo>(sessionId, serverType);
        connection->serverAddress = address;
        connection->authState = ServerAuthState::Connecting;
        
        m_serverConnections[sessionId] = std::move(connection);
        
        std::cout << "[INFO] Registered server connection: " << sessionId 
                  << " Type: " << ServerTypeToString(serverType) << std::endl;
        return true;
    }

    bool CNetworkServerAuth::UnregisterServerConnection(int sessionId) {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        auto it = m_serverConnections.find(sessionId);
        if (it != m_serverConnections.end()) {
            ServerType serverType = it->second->serverType;
            
            // Update global server state
            if (serverType == ServerType::ControlServer) {
                SetControlServerState(false);
            } else if (serverType == ServerType::WebAgentServer) {
                SetWebAgentServerState(false);
            }
            
            m_serverConnections.erase(it);
            
            std::cout << "[INFO] Unregistered server connection: " << sessionId 
                      << " Type: " << ServerTypeToString(serverType) << std::endl;
            return true;
        }
        
        return false;
    }

    ServerConnectionInfo* CNetworkServerAuth::GetServerConnection(int sessionId) {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        auto it = m_serverConnections.find(sessionId);
        return (it != m_serverConnections.end()) ? it->second.get() : nullptr;
    }

    std::vector<ServerConnectionInfo> CNetworkServerAuth::GetActiveConnections() const {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        
        std::vector<ServerConnectionInfo> connections;
        for (const auto& pair : m_serverConnections) {
            connections.push_back(*pair.second);
        }
        
        return connections;
    }

    bool CNetworkServerAuth::ValidateAuthenticationBuffer(const char* buffer, size_t bufferSize) const {
        return buffer != nullptr && bufferSize > 0;
    }

    bool CNetworkServerAuth::ValidateServerCredentials(ServerType serverType, const char* buffer) const {
        if (!buffer) {
            return false;
        }

        switch (serverType) {
            case ServerType::ControlServer:
                return ValidateControlServerBuffer(buffer);
            case ServerType::WebAgentServer:
                return ValidateWebAgentServerBuffer(buffer);
            default:
                return false;
        }
    }

    bool CNetworkServerAuth::SendAuthenticationResponse(CNetworkEX* networkEX, int sessionId, 
                                                       NetworkMessageType messageType, uint8_t status) {
        return SendNetworkMessage(sessionId, messageType, status);
    }

    bool CNetworkServerAuth::ProcessAuthenticationMessage(const NetworkAuthMessage& message, int sessionId) {
        if (!message.IsValid()) {
            return false;
        }

        auto* connection = GetServerConnection(sessionId);
        if (!connection) {
            return false;
        }

        connection->UpdateActivity();
        
        // Process based on message type
        switch (static_cast<NetworkMessageType>(message.messageType)) {
            case NetworkMessageType::ControlServerLogin:
                connection->authState = ServerAuthState::Authenticated;
                SetControlServerState(true, sessionId);
                return true;
                
            case NetworkMessageType::WebAgentServerLogin:
                connection->authState = ServerAuthState::Authenticated;
                SetWebAgentServerState(true, sessionId);
                return true;
                
            default:
                return false;
        }
    }

    size_t CNetworkServerAuth::GetActiveConnectionCount() const {
        std::lock_guard<std::mutex> lock(m_connectionsMutex);
        return m_serverConnections.size();
    }

    void CNetworkServerAuth::ResetStatistics() {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics = Statistics{};
        m_statistics.startTime = std::chrono::steady_clock::now();
    }

    bool CNetworkServerAuth::LogInControllServer_Legacy(CNetworkEX* networkEX, int sessionId, char* buffer) {
        NetworkServerAuthResult result = AuthenticateControlServer(networkEX, sessionId, buffer);
        return result == NetworkServerAuthResult::Success;
    }

    bool CNetworkServerAuth::LogInWebAgentServer_Legacy(CNetworkEX* networkEX, int sessionId, char* buffer) {
        NetworkServerAuthResult result = AuthenticateWebAgentServer(networkEX, sessionId, buffer);
        return result == NetworkServerAuthResult::Success;
    }

    // Private method implementations

    bool CNetworkServerAuth::ValidateParameters(CNetworkEX* networkEX, int sessionId, const char* buffer) const {
        return networkEX != nullptr && sessionId > 0 && buffer != nullptr;
    }

    bool CNetworkServerAuth::ProcessControlServerAuthentication(int sessionId, const char* buffer) {
        // Implement original logic from LogInControllServer
        // Original: pbyType = 54, checks for buffer[0] == 239

        uint8_t firstByte = GetBufferFirstByte(buffer);

        if (IsControlServerConnected()) {
            // Server already connected - send status 1 and return false (original returns 0)
            SendNetworkMessage(sessionId, NetworkMessageType::ControlServerLogin, 1);
            return false;
        } else if (firstByte == 239) {
            // Valid authentication byte - connect server
            SetControlServerState(true, sessionId);
            SendNetworkMessage(sessionId, NetworkMessageType::ControlServerLogin, 0);
            return true;
        } else {
            // Invalid authentication - send status 1 and return false
            SendNetworkMessage(sessionId, NetworkMessageType::ControlServerLogin, 1);
            return false;
        }
    }

    bool CNetworkServerAuth::ProcessWebAgentServerAuthentication(int sessionId, const char* buffer) {
        // Implement original logic from LogInWebAgentServer
        // Original: pbyType = 51, checks for buffer[0] == 237

        uint8_t firstByte = GetBufferFirstByte(buffer);

        if (IsWebAgentServerConnected()) {
            // Server already connected - send status 1 and return true (original returns 1)
            SendNetworkMessage(sessionId, NetworkMessageType::WebAgentServerLogin, 1);
            return true;
        } else if (firstByte == 237) {
            // Valid authentication byte - connect server
            SetWebAgentServerState(true, sessionId);
            SendNetworkMessage(sessionId, NetworkMessageType::WebAgentServerLogin, 0);
            return true;
        } else {
            // Invalid authentication - send status 2 and return true (original returns 1)
            SendNetworkMessage(sessionId, NetworkMessageType::WebAgentServerLogin, 2);
            return true;
        }
    }

    void CNetworkServerAuth::UpdateStatistics(bool success, ServerType serverType) {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics.totalAuthenticationAttempts++;

        if (success) {
            m_statistics.successfulAuthentications++;
            if (serverType == ServerType::ControlServer) {
                m_statistics.controlServerConnections++;
            } else if (serverType == ServerType::WebAgentServer) {
                m_statistics.webAgentServerConnections++;
            }
        } else {
            m_statistics.failedAuthentications++;
        }
    }

    void CNetworkServerAuth::LogAuthenticationEvent(const std::string& event, int sessionId,
                                                   ServerType serverType, bool success) {
        std::cout << "[" << (success ? "INFO" : "WARN") << "] " << event
                  << " - Session: " << sessionId
                  << " - Type: " << ServerTypeToString(serverType)
                  << " - Result: " << (success ? "SUCCESS" : "FAILED") << std::endl;
    }

    bool CNetworkServerAuth::SendNetworkMessage(int sessionId, NetworkMessageType messageType, uint8_t status) {
        try {
            NetworkAuthMessage message(messageType, status);

            // Use CNetProcess::LoadSendMsg equivalent to original
            void* result = CNetProcess::LoadSendMsg(
                m_networkProcessHandle,
                sessionId,
                &message.messageType,
                &message,
                static_cast<uint32_t>(message.Serialize().size())
            );

            return result != nullptr;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to send network message: " << e.what() << std::endl;
            return false;
        }
    }

    bool CNetworkServerAuth::ValidateControlServerBuffer(const char* buffer) const {
        // Control server expects specific authentication byte (239)
        return buffer != nullptr && GetBufferFirstByte(buffer) == 239;
    }

    bool CNetworkServerAuth::ValidateWebAgentServerBuffer(const char* buffer) const {
        // Web agent server expects specific authentication byte (237)
        return buffer != nullptr && GetBufferFirstByte(buffer) == 237;
    }

    uint8_t CNetworkServerAuth::GetBufferFirstByte(const char* buffer) const {
        return buffer ? static_cast<uint8_t>(*buffer) : 0;
    }

    void CNetworkServerAuth::SetControlServerState(bool connected, int sessionId) {
        std::lock_guard<std::mutex> lock(m_serverStateMutex);
        m_controlServerConnected = connected;
        m_controlServerSessionId = connected ? sessionId : 0;
    }

    void CNetworkServerAuth::SetWebAgentServerState(bool connected, int sessionId) {
        std::lock_guard<std::mutex> lock(m_serverStateMutex);
        m_webAgentServerConnected = connected;
        m_webAgentServerSessionId = connected ? sessionId : 0;
    }

    bool CNetworkServerAuth::IsControlServerConnected() const {
        std::lock_guard<std::mutex> lock(m_serverStateMutex);
        return m_controlServerConnected;
    }

    bool CNetworkServerAuth::IsWebAgentServerConnected() const {
        std::lock_guard<std::mutex> lock(m_serverStateMutex);
        return m_webAgentServerConnected;
    }

    // Global instance access
    CNetworkServerAuth& GetNetworkServerAuth() {
        std::lock_guard<std::mutex> lock(g_instanceMutex);
        if (!g_pNetworkServerAuth) {
            g_pNetworkServerAuth = std::make_unique<CNetworkServerAuth>();
        }
        return *g_pNetworkServerAuth;
    }

    // Utility functions
    std::string NetworkServerAuthResultToString(NetworkServerAuthResult result) {
        switch (result) {
            case NetworkServerAuthResult::Success: return "Success";
            case NetworkServerAuthResult::InvalidParameters: return "Invalid Parameters";
            case NetworkServerAuthResult::InvalidBuffer: return "Invalid Buffer";
            case NetworkServerAuthResult::ServerAlreadyConnected: return "Server Already Connected";
            case NetworkServerAuthResult::AuthenticationFailed: return "Authentication Failed";
            case NetworkServerAuthResult::NetworkError: return "Network Error";
            case NetworkServerAuthResult::SystemError: return "System Error";
            case NetworkServerAuthResult::NotInitialized: return "Not Initialized";
            default: return "Unknown";
        }
    }

    std::string ServerTypeToString(ServerType serverType) {
        switch (serverType) {
            case ServerType::Unknown: return "Unknown";
            case ServerType::ControlServer: return "Control Server";
            case ServerType::WebAgentServer: return "Web Agent Server";
            default: return "Unknown";
        }
    }

    std::string ServerAuthStateToString(ServerAuthState state) {
        switch (state) {
            case ServerAuthState::Disconnected: return "Disconnected";
            case ServerAuthState::Connecting: return "Connecting";
            case ServerAuthState::Connected: return "Connected";
            case ServerAuthState::Authenticated: return "Authenticated";
            case ServerAuthState::Error: return "Error";
            default: return "Unknown";
        }
    }

    std::string NetworkMessageTypeToString(NetworkMessageType messageType) {
        switch (messageType) {
            case NetworkMessageType::ControlServerLogin: return "Control Server Login";
            case NetworkMessageType::WebAgentServerLogin: return "Web Agent Server Login";
            case NetworkMessageType::AuthenticationResponse: return "Authentication Response";
            case NetworkMessageType::AuthenticationFailure: return "Authentication Failure";
            default: return "Unknown";
        }
    }

    // Legacy C interface implementation
    extern "C" {
        char CNetworkEX_LogInControllServer(CNetworkEX_Legacy* networkEX, int sessionId, char* buffer) {
            auto& auth = GetNetworkServerAuth();
            bool result = auth.LogInControllServer_Legacy(reinterpret_cast<CNetworkEX*>(networkEX), sessionId, buffer);
            return result ? 1 : 0;
        }

        char CNetworkEX_LogInWebAgentServer(CNetworkEX_Legacy* networkEX, int sessionId, char* buffer) {
            auto& auth = GetNetworkServerAuth();
            bool result = auth.LogInWebAgentServer_Legacy(reinterpret_cast<CNetworkEX*>(networkEX), sessionId, buffer);
            return result ? 1 : 0;
        }

        void* CNetProcess_LoadSendMsg(void* process, int sessionId, const void* messageType,
                                    const void* messageData, uint32_t messageSize) {
            return CNetProcess::LoadSendMsg(process, sessionId, messageType, messageData, messageSize);
        }

        bool CNetworkServerAuth_IsControlServerConnected() {
            auto& auth = GetNetworkServerAuth();
            return auth.IsControlServerConnected();
        }

        bool CNetworkServerAuth_IsWebAgentServerConnected() {
            auto& auth = GetNetworkServerAuth();
            return auth.IsWebAgentServerConnected();
        }

        int CNetworkServerAuth_GetControlServerSessionId() {
            auto& auth = GetNetworkServerAuth();
            return auth.IsControlServerConnected() ? 1 : 0; // Simplified implementation
        }

        int CNetworkServerAuth_GetWebAgentServerSessionId() {
            auto& auth = GetNetworkServerAuth();
            return auth.IsWebAgentServerConnected() ? 1 : 0; // Simplified implementation
        }
    }

} // namespace NexusProtection::Authentication
