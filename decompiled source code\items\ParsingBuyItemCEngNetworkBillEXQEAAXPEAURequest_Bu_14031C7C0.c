/*
 * Function: ?ParsingBuyItem@CEngNetworkBillEX@@QEAAXPEAURequest_Buy_Item@@PEAD@Z
 * Address: 0x14031C7C0
 */

void __fastcall CEngNetworkBillEX::ParsingBuyItem(CEngNetworkBillEX *this, Request_Buy_Item *data, char *pRecvData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-298h]@1
  char *szFull; // [sp+20h] [bp-278h]@4
  char szSub; // [sp+40h] [bp-258h]@4
  char v8; // [sp+41h] [bp-257h]@4
  char Src; // [sp+42h] [bp-256h]@4
  char Dst; // [sp+258h] [bp-40h]@4
  unsigned __int64 v11; // [sp+280h] [bp-18h]@4
  CEngNetworkBillEX *v12; // [sp+2A0h] [bp+8h]@1
  Request_Buy_Item *v13; // [sp+2A8h] [bp+10h]@1

  v13 = data;
  v12 = this;
  v3 = &v5;
  for ( i = 164i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  szFull = pRecvData;
  szSub = 0;
  memset(&v8, 0, 0x1FFui64);
  szFull = CEngNetworkBillEX::dhExtractSubString(v12, &szSub, pRecvData, 124);
  memset_0(&Dst, 0, 0xAui64);
  memcpy_0(&Dst, &szSub, 2ui64);
  v13->wRet = atoi(&Dst);
  memset_0(&Dst, 0, 0xAui64);
  memcpy_0(&Dst, &Src, 5ui64);
  v13->dwDataLength = atoi(&Dst);
  memset_0(&szSub, 0, 0x200ui64);
  szFull = CEngNetworkBillEX::dhExtractSubString(v12, &szSub, szFull, 124);
  v13->dwSeq = atoi(&szSub);
  memset_0(&szSub, 0, 0x200ui64);
  szFull = CEngNetworkBillEX::dhExtractSubString(v12, &szSub, szFull, 124);
  v13->nBuyID = atoi(&szSub);
  memset_0(&szSub, 0, 0x200ui64);
  szFull = CEngNetworkBillEX::dhExtractSubString(v12, &szSub, szFull, 10);
  v13->nRemainCash = atoi(&szSub);
  memset_0(&szSub, 0, 0x200ui64);
}
