/*
 * Function: ?ErrFmtLog@CRFNewDatabase@@IEAAXPEADZZ
 * Address: 0x140484E30
 */

void CRFNewDatabase::ErrFmtLog(CRFNewDatabase *this, char *fmt, ...)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char *arg; // [sp+28h] [bp-20h]@4
  CRFNewDatabase *v6; // [sp+50h] [bp+8h]@1
  va_list va; // [sp+60h] [bp+18h]@1

  va_start(va, fmt);
  v6 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  arg = (char *)va;
  CLogFile::WriteFromArg(&v6->m_ErrorLogA, fmt, (char *)va);
}
