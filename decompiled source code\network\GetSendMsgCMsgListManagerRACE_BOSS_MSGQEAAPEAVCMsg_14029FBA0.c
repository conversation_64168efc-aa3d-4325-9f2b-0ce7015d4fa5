/*
 * Function: ?GetSendMsg@CMsgListManager@RACE_BOSS_MSG@@QEAAPEAVCMsg@2@AEAE@Z
 * Address: 0x14029FBA0
 */

RACE_BOSS_MSG::CMsg *__fastcall RACE_BOSS_MSG::CMsgListManager::GetSendMsg(RACE_BOSS_MSG::CMsgListManager *this, char *ucRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  RACE_BOSS_MSG::CMsg *result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  RACE_BOSS_MSG::CMsg *v6; // [sp+20h] [bp-18h]@6
  int j; // [sp+28h] [bp-10h]@6
  RACE_BOSS_MSG::CMsgListManager *v8; // [sp+40h] [bp+8h]@1
  char *v9; // [sp+48h] [bp+10h]@1

  v9 = ucRace;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bEmpty )
  {
    result = 0i64;
  }
  else
  {
    v6 = 0i64;
    for ( j = 0; j < 3; ++j )
    {
      v6 = RACE_BOSS_MSG::CMsgList::GetSendMsg(v8->m_pkMsgList[j]);
      if ( v6 )
      {
        *v9 = j;
        return v6;
      }
    }
    result = 0i64;
  }
  return result;
}
