/*
 * Function: ?DrawDummy@CMapDisplay@@AEAAXXZ
 * Address: 0x1401A0010
 */

void __fastcall CMapDisplay::DrawDummy(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-C8h]@1
  CRect v4; // [sp+28h] [bp-A0h]@4
  CRect *prcArea; // [sp+48h] [bp-80h]@4
  CDummyDraw *v6; // [sp+50h] [bp-78h]@8
  int v7; // [sp+58h] [bp-70h]@6
  int j; // [sp+5Ch] [bp-6Ch]@6
  _DDSURFACEDESC2 *v9; // [sp+60h] [bp-68h]@10
  HDC hdc; // [sp+78h] [bp-50h]@10
  int v11; // [sp+84h] [bp-44h]@10
  char v12; // [sp+88h] [bp-40h]@5
  IDirectDrawSurface7 *v13; // [sp+98h] [bp-30h]@10
  IDirectDrawSurface7 *v14; // [sp+A0h] [bp-28h]@11
  CMapDisplay *v15; // [sp+D0h] [bp+8h]@1

  v15 = this;
  v1 = &v3;
  for ( i = 46i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CRect::CRect(&v4);
  prcArea = 0i64;
  if ( v15->m_MapExtend.m_bExtendMode )
  {
    qmemcpy(&v12, &v15->m_MapExtend.m_rcExtend, 0x10ui64);
    qmemcpy(&v4, &v12, sizeof(v4));
    prcArea = &v4;
  }
  v7 = CMapOperation::GetMap(&g_MapOper, v15->m_pActMap);
  for ( j = 0; j < v15->m_nDummyDrawNum[v7]; ++j )
  {
    v6 = v15->m_DummyDraw[v7];
    CDummyDraw::Draw(&v6[j], v15->m_pSFMap, prcArea);
  }
  if ( v15->m_MapExtend.m_bExtendMode )
  {
    v9 = CSurface::GetDDSurfaceDesc(v15->m_pSFMap);
    v13 = CSurface::GetDDrawSurface(v15->m_pSFMap);
    v11 = ((int (__fastcall *)(IDirectDrawSurface7 *, HDC *))v13->vfptr[5].Release)(v13, &hdc);
    if ( !v11 )
    {
      SelectObject(hdc, v15->m_hPenBorder);
      MoveToEx(hdc, 0, 0, 0i64);
      LineTo(hdc, v9->dwWidth, 0);
      LineTo(hdc, v9->dwWidth, v9->dwHeight);
      LineTo(hdc, 0, v9->dwHeight);
      LineTo(hdc, 0, 0);
      v14 = CSurface::GetDDrawSurface(v15->m_pSFMap);
      ((void (__fastcall *)(IDirectDrawSurface7 *, HDC))v14->vfptr[8].Release)(v14, hdc);
    }
  }
}
