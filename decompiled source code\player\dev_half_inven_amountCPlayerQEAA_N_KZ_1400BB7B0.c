/*
 * Function: ?dev_half_inven_amount@CPlayer@@QEAA_N_K@Z
 * Address: 0x1400BB7B0
 */

char __fastcall CPlayer::dev_half_inven_amount(CPlayer *this, unsigned __int64 dwAmount)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@6
  __int64 v6; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@14
  int j; // [sp+30h] [bp-18h]@4
  char *v9; // [sp+38h] [bp-10h]@8
  CPlayer *v10; // [sp+50h] [bp+8h]@1
  unsigned __int64 dwDur; // [sp+58h] [bp+10h]@1

  dwDur = dwAmount;
  v10 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; ; ++j )
  {
    v4 = CPlayerDB::GetBagNum(&v10->m_Param);
    if ( j >= 20 * (unsigned __int8)v4 )
      break;
    if ( v10->m_Param.m_dbInven.m_pStorageList[j].m_bLoad )
    {
      v9 = &v10->m_Param.m_dbInven.m_pStorageList[j].m_bLoad;
      if ( !v9[19] )
      {
        if ( IsAbrItem((unsigned __int8)v9[1], *(_WORD *)(v9 + 3)) )
        {
          if ( IsOverLapItem((unsigned __int8)v9[1]) && dwDur > 0x63 )
            dwDur = 99i64;
          *(_QWORD *)(v9 + 5) = dwDur;
          CPlayer::SendMsg_AlterItemDurInform(v10, 0, *(_WORD *)(v9 + 17), dwDur);
          if ( v10->m_pUserDB )
          {
            bUpdate = 1;
            CUserDB::Update_ItemDur(v10->m_pUserDB, 0, j, dwDur, 1);
          }
        }
      }
    }
  }
  return 1;
}
