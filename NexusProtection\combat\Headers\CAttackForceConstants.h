/*
 * CAttackForceConstants.h - Attack Force System Constants and Lookup Tables
 * Refactored from original decompiled constants
 */

#pragma once

#include <array>
#include <cstdint>

namespace NexusProtection {
namespace Combat {
namespace AttackForceConstants {

/**
 * Attack force lookup tables (reconstructed from original decompiled code)
 * These arrays correspond to s_nLimitDist, s_nLimitAngle, s_nLimitRadius
 */

/**
 * Limit distance array for flash attacks
 * Original: s_nLimitDist
 */
constexpr std::array<int, 10> LIMIT_DISTANCE = {
    100, 150, 200, 250, 300, 350, 400, 450, 500, 550
};

/**
 * Limit angle array for directional attacks
 * Original: s_nLimitAngle[2][10]
 * [0] = horizontal angles, [1] = vertical angles
 */
constexpr std::array<std::array<int, 10>, 2> LIMIT_ANGLE = {{
    // Horizontal angles
    {15, 30, 45, 60, 75, 90, 105, 120, 135, 150},
    // Vertical angles  
    {10, 20, 30, 40, 50, 60, 70, 80, 90, 100}
}};

/**
 * Limit radius array for area attacks
 * Original: s_nLimitRadius
 */
constexpr std::array<int, 10> LIMIT_RADIUS = {
    50, 75, 100, 125, 150, 175, 200, 225, 250, 275
};

/**
 * Attack type enumeration
 */
enum class AttackType : int {
    SingleTarget0 = 0,
    SingleTarget1 = 1,
    SingleTarget2 = 2,
    Reserved3 = 3,
    AreaDamage1 = 4,
    FlashDamage = 5,
    AreaDamage2 = 6
};

/**
 * Effect type enumeration
 */
enum class EffectType : int {
    DamageRate = 4,
    Invulnerability = 8,
    AccuracyBonus = 31,
    PlayerAccuracy = 40
};

/**
 * PvP boss type enumeration
 */
enum class PvPBossType : int {
    NoBoss = 0,
    Reserved1 = 1,
    BossType2 = 2,
    Reserved3 = 3,
    Reserved4 = 4,
    Reserved5 = 5,
    BossType6 = 6
};

/**
 * Damage multiplier constants
 */
namespace DamageMultipliers {
    constexpr float DESTROYER_BONUS = 1.3f;
    constexpr float LAST_ATTACK_BUFF_BONUS = 1.3f;
    constexpr float BOSS_TYPE_2_6_BONUS = 1.2f;
    constexpr float BOSS_TYPE_0_BONUS = 1.3f;
    constexpr float CRITICAL_HIT_MULTIPLIER = 1.5f;
}

/**
 * Hit chance constants
 */
namespace HitChance {
    constexpr float BASE_HIT_CHANCE = 100.0f;
    constexpr int MAX_HIT_CHANCE = 100;
    constexpr int MIN_HIT_CHANCE = 0;
    constexpr float ACCURACY_BONUS_BASE = 100.0f;
}

/**
 * Attack range constants
 */
namespace AttackRange {
    constexpr int DEFAULT_EXTENT_RANGE = 20;
    constexpr int MAX_AREA_RADIUS = 500;
    constexpr int MAX_FLASH_DISTANCE = 1000;
    constexpr int MAX_ANGLE = 360;
}

/**
 * Damage calculation constants
 */
namespace DamageCalculation {
    constexpr int MIN_DAMAGE = 0;
    constexpr int MAX_DAMAGE = 999999;
    constexpr float DAMAGE_VARIANCE = 0.1f;  // 10% variance
    constexpr int CRITICAL_HIT_CHANCE = 5;   // 5% base critical chance
}

/**
 * Attack validation constants
 */
namespace Validation {
    constexpr int MAX_TARGETS_PER_ATTACK = 50;
    constexpr float MIN_DISTANCE_THRESHOLD = 1.0f;
    constexpr float MAX_DISTANCE_THRESHOLD = 2000.0f;
    constexpr int MAX_ATTACK_LEVEL = 99;
    constexpr int MAX_MASTERY_LEVEL = 99;
}

/**
 * Timing constants
 */
namespace Timing {
    constexpr int ATTACK_COOLDOWN_MS = 100;
    constexpr int EFFECT_DURATION_MS = 5000;
    constexpr int STEALTH_BREAK_DELAY_MS = 50;
}

/**
 * Error codes for attack force system
 */
enum class AttackForceError : int {
    Success = 0,
    InvalidAttacker = 1,
    InvalidTarget = 2,
    InvalidParameters = 3,
    TargetInvulnerable = 4,
    AttackMissed = 5,
    InsufficientRange = 6,
    CooldownActive = 7,
    SystemError = 8
};

/**
 * Attack force flags
 */
enum class AttackForceFlags : uint32_t {
    None = 0,
    UseEffBullet = 1 << 0,
    IgnoreDefense = 1 << 1,
    CriticalHit = 1 << 2,
    BackAttack = 1 << 3,
    AreaAttack = 1 << 4,
    FlashAttack = 1 << 5,
    PassCount = 1 << 6,
    ApplyBonuses = 1 << 7
};

// Bitwise operators for flags
constexpr AttackForceFlags operator|(AttackForceFlags a, AttackForceFlags b) {
    return static_cast<AttackForceFlags>(static_cast<uint32_t>(a) | static_cast<uint32_t>(b));
}

constexpr AttackForceFlags operator&(AttackForceFlags a, AttackForceFlags b) {
    return static_cast<AttackForceFlags>(static_cast<uint32_t>(a) & static_cast<uint32_t>(b));
}

constexpr AttackForceFlags operator^(AttackForceFlags a, AttackForceFlags b) {
    return static_cast<AttackForceFlags>(static_cast<uint32_t>(a) ^ static_cast<uint32_t>(b));
}

constexpr AttackForceFlags operator~(AttackForceFlags a) {
    return static_cast<AttackForceFlags>(~static_cast<uint32_t>(a));
}

/**
 * Utility functions for constants
 */
namespace Utils {
    /**
     * Get limit distance value safely
     * @param nIndex Array index
     * @return Limit distance value or default
     */
    constexpr int GetLimitDistance(int nIndex) {
        return (nIndex >= 0 && nIndex < static_cast<int>(LIMIT_DISTANCE.size())) 
               ? LIMIT_DISTANCE[nIndex] 
               : LIMIT_DISTANCE[0];
    }
    
    /**
     * Get limit angle value safely
     * @param nType Angle type (0 or 1)
     * @param nIndex Array index
     * @return Limit angle value or default
     */
    constexpr int GetLimitAngle(int nType, int nIndex) {
        if (nType < 0 || nType >= static_cast<int>(LIMIT_ANGLE.size())) {
            nType = 0;
        }
        if (nIndex < 0 || nIndex >= static_cast<int>(LIMIT_ANGLE[nType].size())) {
            nIndex = 0;
        }
        return LIMIT_ANGLE[nType][nIndex];
    }
    
    /**
     * Get limit radius value safely
     * @param nIndex Array index
     * @return Limit radius value or default
     */
    constexpr int GetLimitRadius(int nIndex) {
        return (nIndex >= 0 && nIndex < static_cast<int>(LIMIT_RADIUS.size())) 
               ? LIMIT_RADIUS[nIndex] 
               : LIMIT_RADIUS[0];
    }
    
    /**
     * Check if attack type is valid
     * @param nType Attack type
     * @return true if valid
     */
    constexpr bool IsValidAttackType(int nType) {
        return nType >= 0 && nType <= 6;
    }
    
    /**
     * Check if effect type is valid
     * @param nType Effect type
     * @return true if valid
     */
    constexpr bool IsValidEffectType(int nType) {
        return nType == static_cast<int>(EffectType::DamageRate) ||
               nType == static_cast<int>(EffectType::Invulnerability) ||
               nType == static_cast<int>(EffectType::AccuracyBonus) ||
               nType == static_cast<int>(EffectType::PlayerAccuracy);
    }
    
    /**
     * Clamp value to valid range
     * @param value Value to clamp
     * @param min Minimum value
     * @param max Maximum value
     * @return Clamped value
     */
    template<typename T>
    constexpr T Clamp(T value, T min, T max) {
        return (value < min) ? min : (value > max) ? max : value;
    }
}

} // namespace AttackForceConstants
} // namespace Combat
} // namespace NexusProtection
