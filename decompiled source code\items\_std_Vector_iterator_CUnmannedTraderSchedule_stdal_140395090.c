/*
 * Function: _std::_Vector_iterator_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::operator___::_1_::dtor$1
 * Address: 0x140395090
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::operator___::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(*(std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > **)(a2 + 104));
  }
}
