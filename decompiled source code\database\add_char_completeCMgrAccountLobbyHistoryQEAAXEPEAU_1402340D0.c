/*
 * Function: ?add_char_complete@CMgrAccountLobbyHistory@@QEAAXEPEAU_REGED_AVATOR_DB@@PEAD@Z
 * Address: 0x1402340D0
 */

void __fastcall CMgrAccountLobbyHistory::add_char_complete(CMgrAccountLobbyHistory *this, char byRetCode, _REGED_AVATOR_DB *pInsertData, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@8
  unsigned int v7; // ecx@8
  __int64 v8; // [sp+0h] [bp-58h]@1
  unsigned int v9; // [sp+20h] [bp-38h]@8
  int v10; // [sp+28h] [bp-30h]@8
  unsigned int v11; // [sp+30h] [bp-28h]@8
  unsigned int v12; // [sp+38h] [bp-20h]@8
  bool v13; // [sp+40h] [bp-18h]@4
  bool v14; // [sp+41h] [bp-17h]@4
  const char *v15; // [sp+48h] [bp-10h]@5
  CMgrAccountLobbyHistory *v16; // [sp+60h] [bp+8h]@1
  _REGED_AVATOR_DB *v17; // [sp+70h] [bp+18h]@1
  char *pszFileNamea; // [sp+78h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v17 = pInsertData;
  v16 = this;
  v4 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  sLData[0] = 0;
  v14 = byRetCode == 0;
  v13 = byRetCode == 0;
  if ( byRetCode )
    v15 = "ERROR";
  else
    v15 = "SUCCESS";
  sprintf_s<10240>((char (*)[10240])sLBuf, "Add Result: %d (%s)\r\n", (unsigned __int8)byRetCode, v15);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  if ( v13 )
  {
    v6 = v17->m_byLevel;
    v7 = v17->m_bySlotIndex;
    v12 = v17->m_dwGold;
    v11 = v17->m_dwDalant;
    v10 = v6;
    v9 = v17->m_dwRecordNum;
    sprintf_s<10240>(
      (char (*)[10240])sLBuf,
      "[Slot%d]\r\nNAME: %s\r\nCharSR: %d\r\nLV: %d\r\n$D: %d\r\n$G: %d\r\n\r\n",
      v7,
      v17);
    strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  }
  sprintf_s<10240>((char (*)[10240])sLBuf, "Add Character Complete [%s %s]\r\n", v16->m_szCurDate, v16->m_szCurTime);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v16, pszFileNamea, sLData);
}
