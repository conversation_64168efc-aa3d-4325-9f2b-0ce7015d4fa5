/*
 * Function: ?insert_iteminfo@CRFDBItemLog@@QEAA_NPEAU_LTD_ITEMINFO@@E@Z
 * Address: 0x1404859A0
 */

bool __fastcall CRFDBItemLog::insert_iteminfo(CRFDBItemLog *this, _LTD_ITEMINFO *pi, char byIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // ecx@4
  int v6; // er8@4
  int v7; // er9@4
  int v8; // er10@4
  int v9; // er11@4
  int v10; // ebx@4
  int v11; // esi@4
  int v12; // ebp@4
  unsigned int v13; // er12@4
  __int64 v15; // [sp+0h] [bp-1D8h]@1
  int v16; // [sp+20h] [bp-1B8h]@4
  int v17; // [sp+28h] [bp-1B0h]@4
  int v18; // [sp+30h] [bp-1A8h]@4
  int v19; // [sp+38h] [bp-1A0h]@4
  int v20; // [sp+40h] [bp-198h]@4
  int v21; // [sp+48h] [bp-190h]@4
  int v22; // [sp+50h] [bp-188h]@4
  char *v23; // [sp+58h] [bp-180h]@4
  char *v24; // [sp+60h] [bp-178h]@4
  unsigned int v25; // [sp+68h] [bp-170h]@4
  int v26; // [sp+70h] [bp-168h]@4
  char *v27; // [sp+78h] [bp-160h]@4
  char Dest; // [sp+90h] [bp-148h]@4
  char v29; // [sp+91h] [bp-147h]@4
  _LTD_ITEMINFO::_iteminfo *v30; // [sp+198h] [bp-40h]@4
  unsigned __int64 v31; // [sp+1A8h] [bp-30h]@4
  CRFDBItemLog *v32; // [sp+1E0h] [bp+8h]@1
  char v33; // [sp+1F0h] [bp+18h]@1

  v33 = byIndex;
  v32 = this;
  v3 = &v15;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v31 = (unsigned __int64)&v15 ^ _security_cookie;
  Dest = 0;
  memset(&v29, 0, 0xFFui64);
  v30 = &pi->m_ItemInfo[(unsigned __int8)byIndex];
  v5 = pi->m_ItemInfo[(unsigned __int8)byIndex].m_byOverlapNum;
  v6 = pi->m_bySubLogType;
  v7 = pi->m_timeLocal.wMilliseconds;
  v8 = pi->m_timeLocal.wSecond;
  v9 = pi->m_timeLocal.wMinute;
  v10 = pi->m_timeLocal.wHour;
  v11 = pi->m_timeLocal.wDay;
  v12 = pi->m_timeLocal.wMonth;
  v13 = pi->m_timeLocal.wYear;
  v27 = pi->m_ItemInfo[(unsigned __int8)v33].m_szItemName;
  v26 = v5;
  v25 = pi->m_ItemInfo[(unsigned __int8)v33].m_dwDur;
  v24 = pi->m_ItemInfo[(unsigned __int8)v33].m_szUpgradeCode;
  v23 = pi->m_ItemInfo[(unsigned __int8)v33].m_szItemCode;
  v22 = v6;
  v21 = v7;
  v20 = v8;
  v19 = v9;
  v18 = v10;
  v17 = v11;
  v16 = v12;
  sprintf(
    &Dest,
    "insert into tbl_ltd_iteminfo_%d (LogSerial, SubType, ItemC, ItemU, ItemD, ItemO, Name) values ('%04d-%02d-%02d %02d:"
    "%02d:%02d.%03d', %d, '%s', '%s', %d, %d, '%s')",
    v32->m_dwKorTime,
    v13);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v32->vfptr, &Dest, 1);
}
