/*
 * Function: ?SendMsg_UsPotionResultOther@CPlayer@@QEAAXEGPEAV1@_N@Z
 * Address: 0x1400D7B80
 */

void __fastcall CPlayer::SendMsg_UsPotionResultOther(CPlayer *this, char byRetcode, unsigned __int16 wPotionIndex, CPlayer *pUsePlayer, bool bCircle)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-A8h]@1
  char szMsg; // [sp+38h] [bp-70h]@4
  unsigned __int16 v9; // [sp+39h] [bp-6Fh]@4
  char Dst; // [sp+3Bh] [bp-6Dh]@4
  char v11; // [sp+42h] [bp-66h]@4
  unsigned __int16 v12; // [sp+43h] [bp-65h]@4
  unsigned int v13; // [sp+45h] [bp-63h]@4
  __int16 v14; // [sp+49h] [bp-5Fh]@4
  __int16 v15; // [sp+4Bh] [bp-5Dh]@4
  __int16 v16; // [sp+4Dh] [bp-5Bh]@4
  char Src; // [sp+64h] [bp-44h]@4
  __int16 v18; // [sp+65h] [bp-43h]@4
  int v19; // [sp+67h] [bp-41h]@4
  char pbyType; // [sp+84h] [bp-24h]@4
  char v21; // [sp+85h] [bp-23h]@4
  CPlayer *v22; // [sp+B0h] [bp+8h]@1
  unsigned __int16 v23; // [sp+C0h] [bp+18h]@1

  v23 = wPotionIndex;
  v22 = this;
  v5 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  Src = pUsePlayer->m_ObjID.m_byID;
  v19 = pUsePlayer->m_ObjID.m_wIndex;
  v18 = pUsePlayer->m_dwObjSerial;
  szMsg = byRetcode;
  memcpy_0(&Dst, &Src, 7ui64);
  v11 = v22->m_ObjID.m_byID;
  v12 = v22->m_ObjID.m_wIndex;
  v13 = v22->m_dwObjSerial;
  v9 = v23;
  v14 = CPlayerDB::GetHP(&v22->m_Param);
  v15 = CPlayerDB::GetFP(&v22->m_Param);
  v16 = CPlayerDB::GetSP(&v22->m_Param);
  pbyType = 7;
  v21 = 108;
  if ( bCircle )
    CGameObject::CircleReport((CGameObject *)&v22->vfptr, &pbyType, &szMsg, 23, 1);
  else
    CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x17u);
}
