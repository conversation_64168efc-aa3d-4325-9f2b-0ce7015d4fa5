/*
 * Function: ?ChatTradeRequestMsg@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C61F0
 */

char __fastcall CNetworkEX::ChatTradeRequestMsg(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-168h]@1
  char *v8; // [sp+20h] [bp-148h]@4
  CPlayer *v9; // [sp+28h] [bp-140h]@4
  char Dst[272]; // [sp+40h] [bp-128h]@8
  unsigned __int64 v11; // [sp+150h] [bp-18h]@4
  CNetworkEX *v12; // [sp+170h] [bp+8h]@1

  v12 = this;
  v3 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper )
  {
    if ( (signed int)(unsigned __int8)v8[1] < 255 )
    {
      memcpy_0(Dst, v8 + 2, (unsigned __int8)v8[1]);
      Dst[(unsigned __int8)v8[1]] = 0;
      CPlayer::pc_ChatTradeRequestMsg(v9, *v8, Dst);
      result = 1;
    }
    else
    {
      v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
      CLogFile::Write(&v12->m_LogFile, "odd.. %s: ChatTradeMsg()..  if(pRecv->bySize > max_message_size)", v6);
      result = 1;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
