/*
 * Function: j_??$_Destroy_range@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@0AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@@Z
 * Address: 0x140008323
 */

void __fastcall std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last, std::allocator<CMoveMapLimitRight *> *_Al)
{
  std::_Destroy_range<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(_First, _Last, _Al);
}
