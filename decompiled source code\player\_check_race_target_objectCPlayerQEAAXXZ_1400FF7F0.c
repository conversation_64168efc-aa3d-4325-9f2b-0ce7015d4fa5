/*
 * Function: ?_check_race_target_object@CPlayer@@QEAAXXZ
 * Address: 0x1400FF7F0
 */

void __fastcall CPlayer::_check_race_target_object(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@4
  unsigned int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  CGameObject *pObject; // [sp+20h] [bp-18h]@4
  int v7; // [sp+28h] [bp-10h]@4
  unsigned int v8; // [sp+2Ch] [bp-Ch]@4
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pObject = 0i64;
  v7 = CPlayerDB::GetRaceCode(&v9->m_Param);
  v3 = CPvpUserAndGuildRankingSystem::Instance();
  v8 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v3, v7, 0);
  v4 = CPlayerDB::GetCharSerial(&v9->m_Param);
  if ( v8 == v4 )
  {
    pObject = v9->m_GroupTargetObject[2].pObject;
    if ( pObject )
    {
      if ( pObject->m_bLive )
      {
        if ( v9->m_GroupTargetObject[2].byKind == pObject->m_ObjID.m_byKind
          && v9->m_GroupTargetObject[2].byID == pObject->m_ObjID.m_byID
          && v9->m_GroupTargetObject[2].dwSerial == pObject->m_dwObjSerial )
        {
          CPlayer::pc_RefreshGroupTargetPosition(v9, 2, pObject);
        }
        else
        {
          CPlayer::pc_ReleaseGroupTargetObjectRequest(v9, 2);
        }
      }
      else
      {
        CPlayer::pc_ReleaseGroupTargetObjectRequest(v9, 2);
      }
    }
  }
}
