/*
 * Function: j_?empty@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBA_NXZ
 * Address: 0x14000AC63
 */

bool __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::empty(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  return std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::empty(this);
}
