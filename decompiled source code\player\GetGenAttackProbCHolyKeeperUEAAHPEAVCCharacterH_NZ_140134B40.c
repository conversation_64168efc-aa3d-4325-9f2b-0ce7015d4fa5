/*
 * Function: ?GetGenAttackProb@CHoly<PERSON>eeper@@UEAAHPEAVCCharacter@@H_N@Z
 * Address: 0x140134B40
 */

__int64 __fastcall CHolyKeeper::GetGenAttackProb(CH<PERSON><PERSON><PERSON><PERSON> *this, <PERSON>haracter *pDst, int nPart, bool bBackAttack)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  __int64 v8; // [sp+0h] [bp-38h]@1
  float v9; // [sp+20h] [bp-18h]@4
  float v10; // [sp+24h] [bp-14h]@4
  float v11; // [sp+28h] [bp-10h]@4
  int v12; // [sp+2Ch] [bp-Ch]@4
  CHolyKeeper *v13; // [sp+40h] [bp+8h]@1
  CCharacter *v14; // [sp+48h] [bp+10h]@1
  bool v15; // [sp+58h] [bp+20h]@1

  v15 = bBackAttack;
  v14 = pDst;
  v13 = this;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = v13->m_pRec->m_fAttSklUnit;
  v10 = (float)((int (__fastcall *)(CCharacter *))pDst->vfptr->GetLevel)(pDst);
  v11 = (float)((int (__fastcall *)(CCharacter *, _QWORD))v14->vfptr->GetDefSkill)(v14, v15);
  v12 = (signed int)floor((float)(v9 - (float)(v10 + v11)) / 4.0 + 95.0);
  v6 = ((int (__fastcall *)(CCharacter *))v14->vfptr->GetAvoidRate)(v14);
  v12 -= v6;
  if ( v14->m_bMove )
    v12 = (signed int)ffloor((float)v12 * 0.5);
  if ( v12 >= 5 )
  {
    if ( v12 > 95 )
      v12 = 95;
  }
  else
  {
    v12 = 5;
  }
  return (unsigned int)v12;
}
