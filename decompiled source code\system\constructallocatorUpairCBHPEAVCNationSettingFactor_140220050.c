/*
 * Function: ?construct@?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@QEAAXPEAU?$pair@$$CBHPEAVCNationSettingFactory@@@2@AEBU32@@Z
 * Address: 0x140220050
 */

void __fastcall std::allocator<std::pair<int const,CNationSettingFactory *>>::construct(std::allocator<std::pair<int const ,CNationSettingFactory *> > *this, std::pair<int const ,CNationSettingFactory *> *_Ptr, std::pair<int const ,CNationSettingFactory *> *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<std::pair<int const,CNationSettingFactory *>,std::pair<int const,CNationSettingFactory *>>(_Ptr, _Val);
}
