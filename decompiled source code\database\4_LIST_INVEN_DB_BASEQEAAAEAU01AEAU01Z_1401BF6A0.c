/*
 * Function: ??4_LIST@_INVEN_DB_BASE@@QEAAAEAU01@AEAU01@@Z
 * Address: 0x1401BF6A0
 */

_INVEN_DB_BASE::_LIST *__fastcall _INVEN_DB_BASE::_LIST::operator=(_INVEN_DB_BASE::_LIST *this, _INVEN_DB_BASE::_LIST *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  _INVEN_DB_BASE::_LIST *v6; // [sp+30h] [bp+8h]@1
  _INVEN_DB_BASE::_LIST *rhs; // [sp+38h] [bp+10h]@1

  rhs = __that;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _INVENKEY::operator=(&v6->Key, &__that->Key);
  v6->dwDur = rhs->dwDur;
  v6->dwUpt = rhs->dwUpt;
  v6->dwItemETSerial = rhs->dwItemETSerial;
  v6->lnUID = rhs->lnUID;
  v6->byCsMethod = rhs->byCsMethod;
  v6->dwT = rhs->dwT;
  v6->dwLendRegdTime = rhs->dwLendRegdTime;
  return v6;
}
