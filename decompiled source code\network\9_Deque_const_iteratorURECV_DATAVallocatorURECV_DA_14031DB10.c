/*
 * Function: ??9?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBA_NAEBV01@@Z
 * Address: 0x14031DB10
 */

bool __fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator!=(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this, std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Right)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator==(v6, _Right) == 0;
}
