/*
 * Function: ?C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@CHolyStoneSystem@@IEAAXH@Z
 * Address: 0x14027D8B0
 */

void __fastcall CHolyStoneSystem::CreateHolyKeeper(CHolyStoneSystem *this, int nCreateType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed int v4; // eax@7
  signed int v5; // eax@9
  float v6; // xmm0_4@9
  float v7; // xmm0_4@10
  __int64 v8; // [sp+0h] [bp-A8h]@1
  _keeper_create_setdata Dst; // [sp+30h] [bp-78h]@4
  float v10; // [sp+74h] [bp-34h]@7
  float v11; // [sp+78h] [bp-30h]@7
  CGameObjectVtbl *v12; // [sp+80h] [bp-28h]@7
  CGameObjectVtbl *v13; // [sp+88h] [bp-20h]@9
  CGameObjectVtbl *v14; // [sp+90h] [bp-18h]@10
  CHolyStoneSystem *v15; // [sp+B0h] [bp+8h]@1
  int nCreateTypea; // [sp+B8h] [bp+10h]@1

  nCreateTypea = nCreateType;
  v15 = this;
  v2 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _keeper_create_setdata::_keeper_create_setdata(&Dst);
  Dst.m_pMap = v15->m_HolyKeeperData.pCreateMap;
  Dst.m_nLayerIndex = 0;
  Dst.m_pRecordSet = (_base_fld *)&v15->m_HolyKeeperData.pRec->m_dwIndex;
  memcpy_0(Dst.m_fStartPos, v15->m_HolyKeeperData.CreateDummy.m_fCenterPos, 0xCui64);
  Dst.pPosCreate = &v15->m_HolyKeeperData.CreateDummy;
  Dst.pPosActive = &v15->m_HolyKeeperData.ActiveDummy;
  Dst.pPosCenter = &v15->m_HolyKeeperData.CenterDummy;
  Dst.nMasterRace = CHolyStoneSystem::GetHolyMasterRace(v15);
  if ( g_Keeper->m_bLive )
    CHolyKeeper::Destroy(g_Keeper, 1, 0i64);
  CHolyKeeper::Create(g_Keeper, &Dst, nCreateTypea);
  if ( nCreateTypea )
  {
    (*(void (__fastcall **)(CHolyKeeper *, _QWORD))&g_Keeper->vfptr[1].gap8[16])(
      g_Keeper,
      v15->m_SaveData.m_nStartStoneHP);
    (*(void (__fastcall **)(CHolyKeeper *, _QWORD))&g_Keeper->vfptr[1].gap8[8])(
      g_Keeper,
      v15->m_SaveData.m_nStartStoneHP);
  }
  else
  {
    v10 = v15->m_fKeeperHPRate;
    v11 = v15->m_fFirstKeeperHPRate;
    v4 = (signed int)ffloor((float)v15->m_SaveData.m_nStartStoneHP * v10);
    v12 = g_Keeper->vfptr;
    (*(void (__fastcall **)(CHolyKeeper *, _QWORD))&v12[1].gap8[16])(g_Keeper, (unsigned int)v4);
    if ( v15->m_SaveData.m_nHolyMasterRace < 0 || v15->m_SaveData.m_nHolyMasterRace >= 3 )
    {
      v7 = (float)v15->m_SaveData.m_nStartStoneHP * v10;
      v14 = g_Keeper->vfptr;
      (*(void (__fastcall **)(CHolyKeeper *, _QWORD))&v14[1].gap8[8])(g_Keeper, (unsigned int)(signed int)ffloor(v7));
    }
    else
    {
      v5 = (signed int)ffloor((float)v15->m_SaveData.m_nStartStoneHP * v11);
      v6 = (float)v15->m_SaveData.m_nStoneHP_Buffer[v15->m_SaveData.m_nHolyMasterRace] * v10;
      v13 = g_Keeper->vfptr;
      (*(void (__fastcall **)(CHolyKeeper *, _QWORD))&v13[1].gap8[8])(
        g_Keeper,
        (unsigned int)((signed int)ffloor(v6) + v5));
    }
  }
}
