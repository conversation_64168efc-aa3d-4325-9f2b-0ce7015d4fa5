/*
 * Function: ?Initialize@?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@QEAAXAEBVOID@2@@Z
 * Address: 0x1405809F0
 */

int __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::Initialize(__int64 a1, CryptoPP::OID *a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rax@5
  CryptoPP::EC2N *v4; // rax@8
  unsigned __int64 v5; // rax@13
  __int64 v7; // [sp+20h] [bp-228h]@1
  CryptoPP::StringSource v8; // [sp+30h] [bp-218h]@8
  __int64 v9; // [sp+A0h] [bp-1A8h]@1
  char v10; // [sp+A8h] [bp-1A0h]@5
  __int64 v11; // [sp+B0h] [bp-198h]@1
  bool v12; // [sp+B8h] [bp-190h]@8
  CryptoPP::EC2NPoint v13; // [sp+C0h] [bp-188h]@8
  __int64 v14; // [sp+F8h] [bp-150h]@5
  CryptoPP::StringSource v15; // [sp+100h] [bp-148h]@13
  unsigned __int8 v16; // [sp+170h] [bp-D8h]@1
  CryptoPP::UnknownOID v17; // [sp+178h] [bp-D0h]@3
  struct CryptoPP::BufferedTransformation *v18; // [sp+1C8h] [bp-80h]@8
  CryptoPP::HexDecoder *v19; // [sp+1D0h] [bp-78h]@5
  struct CryptoPP::BufferedTransformation *v20; // [sp+1D8h] [bp-70h]@13
  CryptoPP::HexDecoder *v21; // [sp+1E0h] [bp-68h]@10
  CryptoPP::Integer v22; // [sp+1E8h] [bp-60h]@13
  __int64 v23; // [sp+210h] [bp-38h]@1
  struct CryptoPP::BufferedTransformation *v24; // [sp+218h] [bp-30h]@6
  unsigned __int64 v25; // [sp+220h] [bp-28h]@8
  struct CryptoPP::BufferedTransformation *v26; // [sp+228h] [bp-20h]@11
  __int64 v27; // [sp+250h] [bp+8h]@1
  CryptoPP::OID *__that; // [sp+258h] [bp+10h]@1

  __that = a2;
  v27 = a1;
  v23 = -2i64;
  sub_14057B830(&v9, (struct CryptoPP::OID *)&v11);
  memset(&v16, 0, sizeof(v16));
  LODWORD(v2) = std::lower_bound<CryptoPP::EcRecommendedParameters<CryptoPP::EC2N> const *,CryptoPP::OID,CryptoPP::OIDLessThan>(
                  v9,
                  v11,
                  __that,
                  v16);
  v7 = v2;
  if ( v2 == v11 || (unsigned __int8)CryptoPP::operator!=() )
  {
    CryptoPP::UnknownOID::UnknownOID(&v17);
    CxxThrowException_0((__int64)&v17, (__int64)&TI5_AVUnknownOID_CryptoPP__);
  }
  v14 = v7;
  CryptoPP::OID::operator=((CryptoPP::OID *)(v27 + 304), __that);
  LODWORD(v3) = CryptoPP::EcRecommendedParameters<CryptoPP::EC2N>::NewEC(v14);
  std::auto_ptr<CryptoPP::EC2N>::auto_ptr<CryptoPP::EC2N>(&v10, v3);
  std::auto_ptr<CryptoPP::EC2N>::operator*(&v10);
  CryptoPP::EcPrecomputation<CryptoPP::EC2N>::SetCurve(v27 + 24);
  v19 = (CryptoPP::HexDecoder *)operator new(0x68ui64);
  if ( v19 )
    v24 = (struct CryptoPP::BufferedTransformation *)CryptoPP::HexDecoder::HexDecoder(v19, 0i64);
  else
    v24 = 0i64;
  v18 = v24;
  CryptoPP::StringSource::StringSource(&v8, *(const char **)(v14 + 80), 1, v24);
  CryptoPP::EC2NPoint::EC2NPoint(&v13);
  v25 = CryptoPP::BufferedTransformation::MaxRetrievable((CryptoPP::BufferedTransformation *)&v8.vfptr);
  LODWORD(v4) = CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::GetCurve(v27);
  v12 = CryptoPP::EC2N::DecodePoint(v4, &v13, (struct CryptoPP::BufferedTransformation *)&v8.vfptr, v25);
  (*(void (__fastcall **)(__int64, CryptoPP::EC2NPoint *))(*(_QWORD *)v27 + 16i64))(v27, &v13);
  if ( !v12 )
    _wassert(L"result", L"d:\\rf project\\rf_server64\\28 crypto++\\eccrypto.cpp", 0x189u);
  v21 = (CryptoPP::HexDecoder *)operator new(0x68ui64);
  if ( v21 )
    v26 = (struct CryptoPP::BufferedTransformation *)CryptoPP::HexDecoder::HexDecoder(v21, 0i64);
  else
    v26 = 0i64;
  v20 = v26;
  CryptoPP::StringSource::StringSource(&v15, *(const char **)(v14 + 88), 1, v26);
  v5 = CryptoPP::BufferedTransformation::MaxRetrievable((CryptoPP::BufferedTransformation *)&v15.vfptr);
  CryptoPP::Integer::Decode(
    (CryptoPP::Integer *)(v27 + 344),
    (struct CryptoPP::BufferedTransformation *)&v15.vfptr,
    v5,
    0);
  CryptoPP::Integer::Integer(&v22, *(_DWORD *)(v14 + 96));
  CryptoPP::Integer::operator=(v27 + 392);
  CryptoPP::Integer::~Integer(&v22);
  CryptoPP::StringSource::~StringSource(&v15);
  CryptoPP::EC2NPoint::~EC2NPoint(&v13);
  CryptoPP::StringSource::~StringSource(&v8);
  return std::auto_ptr<CryptoPP::EC2N>::~auto_ptr<CryptoPP::EC2N>(&v10);
}
