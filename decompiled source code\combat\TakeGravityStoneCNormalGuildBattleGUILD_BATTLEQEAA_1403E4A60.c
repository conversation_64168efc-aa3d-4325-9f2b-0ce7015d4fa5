/*
 * Function: ?TakeGravityStone@CNormalGuildBattle@GUILD_BATTLE@@QEAAEHK@Z
 * Address: 0x1403E4A60
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::TakeGravityStone(GUILD_BATTLE::CNormalGuildBattle *this, int iPortalInx, unsigned int dwCharacSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *pkTakeGuild; // [sp+20h] [bp-28h]@4
  CPlayer *pkPlayer; // [sp+28h] [bp-20h]@4
  char v9; // [sp+30h] [bp-18h]@8
  GUILD_BATTLE::CNormalGuildBattle *v10; // [sp+50h] [bp+8h]@1
  int iPortalInxa; // [sp+58h] [bp+10h]@1
  unsigned int dwSerial; // [sp+60h] [bp+18h]@1

  dwSerial = dwCharacSerial;
  iPortalInxa = iPortalInx;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pkTakeGuild = &v10->m_k1P;
  pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v10->m_k1P, dwCharacSerial);
  if ( !pkPlayer )
  {
    pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v10->m_k2P, dwSerial);
    pkTakeGuild = &v10->m_k2P;
  }
  if ( pkPlayer )
  {
    v9 = GUILD_BATTLE::CNormalGuildBattleField::TakeBall(v10->m_pkField, iPortalInxa, pkPlayer);
    if ( v9 )
    {
      result = v9;
    }
    else
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(&v10->m_k1P, pkTakeGuild, pkPlayer, iPortalInxa);
      GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(&v10->m_k2P, pkTakeGuild, pkPlayer, iPortalInxa);
      result = 0;
    }
  }
  else
  {
    result = -111;
  }
  return result;
}
