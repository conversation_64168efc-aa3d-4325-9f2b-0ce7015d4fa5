/*
 * Function: ??1?$_Ranit@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14058A6D0
 */

void __fastcall std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>::~_Ranit<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base::~_Iterator_base(a1);
}
