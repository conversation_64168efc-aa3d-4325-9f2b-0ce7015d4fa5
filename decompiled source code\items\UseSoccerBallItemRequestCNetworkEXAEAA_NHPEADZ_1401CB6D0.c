/*
 * Function: ?UseSoccerBallItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB6D0
 */

char __fastcall CNetworkEX::UseSoccerBallItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-E8h]@1
  unsigned __int16 *v7; // [sp+30h] [bp-B8h]@4
  CPlayer *v8; // [sp+38h] [bp-B0h]@4
  unsigned __int16 wItemIndex; // [sp+44h] [bp-A4h]@6
  char szMsg; // [sp+64h] [bp-84h]@6
  unsigned __int16 v11; // [sp+65h] [bp-83h]@6
  char pbyType; // [sp+84h] [bp-64h]@6
  char v13; // [sp+85h] [bp-63h]@6
  char v14[4]; // [sp+A4h] [bp-44h]@7
  unsigned __int16 v15; // [sp+A8h] [bp-40h]@7
  bool v16; // [sp+AAh] [bp-3Eh]@7
  char v17; // [sp+C4h] [bp-24h]@7
  char v18; // [sp+C5h] [bp-23h]@7

  v3 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned __int16 *)pBuf;
  v8 = &g_Player + n;
  if ( v8->m_bOper )
  {
    wItemIndex = -1;
    v11 = *v7;
    szMsg = CPlayer::pc_UserSoccerBall(v8, *v7, &wItemIndex);
    pbyType = 7;
    v13 = 47;
    CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 3u);
    if ( !szMsg )
    {
      *(_DWORD *)v14 = v8->m_dwObjSerial;
      v15 = wItemIndex;
      v16 = v8->m_bTakeSoccerBall;
      v17 = 7;
      v18 = 48;
      CGameObject::CircleReport((CGameObject *)&v8->vfptr, &v17, v14, 7, 0);
      CPlayer::SenseState(v8);
    }
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
