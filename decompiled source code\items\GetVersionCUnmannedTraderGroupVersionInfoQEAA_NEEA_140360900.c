/*
 * Function: ?GetVersion@CUnmannedTraderGroupVersionInfo@@QEAA_NEEAEAK@Z
 * Address: 0x140360900
 */

bool __fastcall CUnmannedTraderGroupVersionInfo::GetVersion(CUnmannedTraderGroupVersionInfo *this, char byDivision, char byClass, unsigned int *dwVer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CUnmannedTraderGroupDivisionVersionInfo *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupVersionInfo *v9; // [sp+30h] [bp+8h]@1
  char v10; // [sp+38h] [bp+10h]@1
  char v11; // [sp+40h] [bp+18h]@1
  unsigned int *dwVera; // [sp+48h] [bp+20h]@1

  dwVera = dwVer;
  v11 = byClass;
  v10 = byDivision;
  v9 = this;
  v4 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::size(&v9->m_vecVerInfo) > (unsigned __int8)byDivision )
  {
    v7 = std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator[](
           &v9->m_vecVerInfo,
           (unsigned __int8)v10);
    result = CUnmannedTraderGroupDivisionVersionInfo::GetVersion(v7, v11, dwVera);
  }
  else
  {
    result = 0;
  }
  return result;
}
