/*
 * Function: ?apply_case_equip_upgrade_effect@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@_N@Z
 * Address: 0x1400623C0
 */

void __fastcall CPlayer::apply_case_equip_upgrade_effect(CPlayer *this, _STORAGE_LIST::_db_con *pItem, bool bEquip)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@16
  float v6; // xmm0_4@34
  __int64 v7; // rax@55
  __int64 v8; // [sp+0h] [bp-A8h]@1
  int v9; // [sp+20h] [bp-88h]@4
  unsigned int Dst; // [sp+34h] [bp-74h]@4
  char v11; // [sp+44h] [bp-64h]@4
  float v12; // [sp+48h] [bp-60h]@6
  _ItemUpgrade_fld *v13; // [sp+50h] [bp-58h]@10
  int nParamIndex; // [sp+58h] [bp-50h]@10
  _ItemUpgrade_fld *v15; // [sp+60h] [bp-48h]@17
  int j; // [sp+68h] [bp-40h]@21
  char v17; // [sp+6Ch] [bp-3Ch]@24
  int v18; // [sp+70h] [bp-38h]@25
  int k; // [sp+74h] [bp-34h]@25
  char v20; // [sp+78h] [bp-30h]@27
  _ItemUpgrade_fld *v21; // [sp+80h] [bp-28h]@30
  float v22; // [sp+88h] [bp-20h]@34
  int v23; // [sp+8Ch] [bp-1Ch]@34
  int v24; // [sp+90h] [bp-18h]@28
  int v25; // [sp+94h] [bp-14h]@34
  CPlayer *v26; // [sp+B0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v27; // [sp+B8h] [bp+10h]@1
  bool v28; // [sp+C0h] [bp+18h]@1

  v28 = bEquip;
  v27 = pItem;
  v26 = this;
  v3 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  memcpy_0(&Dst, &pItem->m_dwLv, 4ui64);
  v11 = GetItemUpgedLv(Dst);
  if ( !v11 || !GetDefItemUpgSocketNum(v27->m_byTableCode, v27->m_wItemIndex) )
  {
    v12 = 0.0;
    _effect_parameter::GetEff_Have(&v26->m_EP, 79);
    if ( 0.0 <= 0.0
      || (_effect_parameter::GetEff_Have(&v26->m_EP, 79), 0.0 >= 6.0)
      || v27->m_byTableCode != 6
      || (signed int)(unsigned __int8)GetItemGrade(v27->m_byTableCode, v27->m_wItemIndex) >= 3 )
    {
      _effect_parameter::GetEff_Have(&v26->m_EP, 80);
      if ( 0.0 > 0.0 )
      {
        _effect_parameter::GetEff_Have(&v26->m_EP, 80);
        if ( 0.0 < 5.0 )
        {
          v5 = v27->m_byTableCode;
          if ( v27->m_byTableCode <= 5 )
          {
            v15 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, 5u);
            _effect_parameter::GetEff_Have(&v26->m_EP, 80);
            v12 = *(&v15->m_fUp1 + (signed int)ffloor(0.0) - 1);
            _effect_parameter::SetEff_Rate(&v26->m_EP, 6, v12, v28);
            if ( v28 )
              v26->m_fTalik_DefencePoint = v26->m_fTalik_DefencePoint + v12;
            else
              v26->m_fTalik_DefencePoint = v26->m_fTalik_DefencePoint - v12;
          }
        }
      }
    }
    else
    {
      v13 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, 0);
      _effect_parameter::GetEff_Have(&v26->m_EP, 79);
      v12 = *(&v13->m_fUp1 + (signed int)ffloor(0.0) - 1);
      for ( nParamIndex = 0; nParamIndex < 2; ++nParamIndex )
      {
        _effect_parameter::SetEff_Rate(&v26->m_EP, nParamIndex, v12, v28);
        _effect_parameter::SetEff_Rate(&v26->m_EP, nParamIndex + 2, v12, v28);
      }
      _effect_parameter::SetEff_Rate(&v26->m_EP, 4, v12, v28);
      _effect_parameter::SetEff_Rate(&v26->m_EP, 29, v12, v28);
    }
  }
  if ( (signed int)(unsigned __int8)GetDefItemUpgSocketNum(v27->m_byTableCode, v27->m_wItemIndex) > 0 )
  {
    for ( j = 0; j < (unsigned __int8)v11; ++j )
    {
      v17 = GetTalikFromSocket(Dst, j);
      if ( v17 != 15 )
      {
        v18 = 1;
        for ( k = j + 1; k < (unsigned __int8)v11; ++k )
        {
          v20 = GetTalikFromSocket(Dst, k);
          if ( (unsigned __int8)v17 == (unsigned __int8)v20 )
          {
            ++v18;
            v24 = 15;
            Dst |= 15 << 4 * k;
          }
        }
        v21 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, (unsigned __int8)v17);
        if ( v21 )
        {
          if ( v18 >= 1 && v18 <= 7 )
          {
            v6 = *(&v21->m_fUp1 + v18 - 1);
            v22 = *(&v21->m_fUp1 + v18 - 1);
            v23 = 0;
            v25 = (unsigned __int8)v17;
            switch ( v17 )
            {
              case 0:
                _effect_parameter::GetEff_Have(&v26->m_EP, 79);
                v23 = (signed int)ffloor(v6);
                if ( v23 > 5 )
                  v23 = 5;
                if ( v23 > v18
                  && (signed int)(unsigned __int8)GetItemGrade(v27->m_byTableCode, v27->m_wItemIndex) < 3
                  && v27->m_byTableCode == 6 )
                {
                  v22 = *(&v21->m_fUp1 + v23 - 1);
                }
                for ( k = 0; k < 2; ++k )
                {
                  _effect_parameter::SetEff_Rate(&v26->m_EP, k, v22, v28);
                  _effect_parameter::SetEff_Rate(&v26->m_EP, k + 2, v22, v28);
                }
                _effect_parameter::SetEff_Rate(&v26->m_EP, 4, v22, v28);
                _effect_parameter::SetEff_Rate(&v26->m_EP, 29, v22, v28);
                break;
              case 1:
                _effect_parameter::SetEff_Rate(&v26->m_EP, 12, v22, v28);
                break;
              case 2:
                _effect_parameter::SetEff_Plus(&v26->m_EP, 28, v22, v28);
                break;
              case 3:
                if ( v27->m_byTableCode == 7 )
                  _effect_parameter::SetEff_Plus(&v26->m_EP, 37, v22, v28);
                else
                  _effect_parameter::SetEff_Plus(&v26->m_EP, 14, v22, v28);
                break;
              case 4:
                _effect_parameter::SetEff_Plus(&v26->m_EP, 5, v22, v28);
                break;
              case 5:
                _effect_parameter::GetEff_Have(&v26->m_EP, 80);
                v23 = (signed int)ffloor(v6);
                if ( v23 > 4 )
                  v23 = 4;
                if ( v18 < v23 )
                {
                  v7 = v27->m_byTableCode;
                  if ( v27->m_byTableCode <= 5 )
                    v22 = *(&v21->m_fUp1 + v23 - 1);
                }
                _effect_parameter::SetEff_Rate(&v26->m_EP, 6, v22, v28);
                if ( v28 )
                  v26->m_fTalik_DefencePoint = v26->m_fTalik_DefencePoint + v22;
                else
                  v26->m_fTalik_DefencePoint = v26->m_fTalik_DefencePoint - v22;
                break;
              case 6:
                _effect_parameter::SetEff_Plus(&v26->m_EP, 38, v22, v28);
                break;
              case 7:
                if ( v27->m_byTableCode != 6 )
                  _effect_parameter::SetEff_Plus(&v26->m_EP, 15, v22, v28);
                break;
              case 8:
                if ( v27->m_byTableCode != 6 )
                  _effect_parameter::SetEff_Plus(&v26->m_EP, 16, v22, v28);
                break;
              case 9:
                if ( v27->m_byTableCode != 6 )
                  _effect_parameter::SetEff_Plus(&v26->m_EP, 17, v22, v28);
                break;
              case 0xA:
                if ( v27->m_byTableCode != 6 )
                  _effect_parameter::SetEff_Plus(&v26->m_EP, 18, v22, v28);
                break;
              case 0xB:
                for ( k = 0; k < 2; ++k )
                  _effect_parameter::SetEff_Plus(&v26->m_EP, k, v22, v28);
                _effect_parameter::SetEff_Plus(&v26->m_EP, 2, v22, v28);
                _effect_parameter::SetEff_Plus(&v26->m_EP, 31, v22, v28);
                _effect_parameter::SetEff_Plus(&v26->m_EP, 30, v22, v28);
                break;
              case 0xC:
                _effect_parameter::SetEff_Plus(&v26->m_EP, 3, v22 / 2.0, v28);
                if ( v28 )
                  v26->m_fTalik_AvoidPoint = v26->m_fTalik_AvoidPoint + (float)(v22 / 2.0);
                else
                  v26->m_fTalik_AvoidPoint = v26->m_fTalik_AvoidPoint - (float)(v22 / 2.0);
                break;
              default:
                continue;
            }
          }
        }
      }
    }
  }
}
