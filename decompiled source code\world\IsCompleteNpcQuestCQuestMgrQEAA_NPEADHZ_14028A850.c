/*
 * Function: ?IsCompleteNpcQuest@CQuestMgr@@QEAA_NPEADH@Z
 * Address: 0x14028A850
 */

char __fastcall CQuestMgr::IsCompleteNpcQuest(CQuestMgr *this, char *pszCode, int bQuestRepeat)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  char *Str1; // [sp+28h] [bp-10h]@7
  CQuestMgr *v9; // [sp+40h] [bp+8h]@1
  const char *Str2; // [sp+48h] [bp+10h]@1
  int v11; // [sp+50h] [bp+18h]@1

  v11 = bQuestRepeat;
  Str2 = pszCode;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 70; ++j )
  {
    Str1 = v9->m_pQuestData->m_History[j].szQuestCode;
    if ( (unsigned __int8)Str1[12] != 255 && !strncmp(Str1, Str2, 7ui64) && v11 != 1 )
      return 1;
  }
  return 0;
}
