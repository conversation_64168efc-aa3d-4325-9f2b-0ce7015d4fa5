/*
 * Function: ?RecoverAndRestart@?$DL_VerifierBase@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA?AUDecodingResult@2@PEAEAEAVPK_MessageAccumulator@2@@Z
 * Address: 0x1405674D0
 */

__int64 __fastcall CryptoPP::DL_VerifierBase<CryptoPP::EC2NPoint>::RecoverAndRestart(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  CryptoPP::CryptoMaterial *v4; // rax@1
  __int64 *v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  unsigned __int64 v8; // rax@1
  __int64 *v9; // rax@1
  __int64 v10; // rax@1
  const void *v11; // rax@1
  __int64 v12; // rax@1
  CryptoPP *v13; // rcx@1
  struct CryptoPP::RandomNumberGenerator *v14; // rax@1
  char v15; // ST30_1@1
  char *v16; // rax@1
  int v17; // eax@1
  char *v18; // rax@1
  CryptoPP::Integer *v19; // rax@1
  __int64 *v20; // rax@1
  const void *v21; // rax@1
  __int64 v22; // rax@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v24; // [sp+50h] [bp-1D8h]@1
  __int64 v25; // [sp+68h] [bp-1C0h]@1
  __int64 v26; // [sp+70h] [bp-1B8h]@1
  __int64 v27; // [sp+78h] [bp-1B0h]@1
  __int64 *v28; // [sp+80h] [bp-1A8h]@1
  CryptoPP::Integer v29; // [sp+88h] [bp-1A0h]@1
  CryptoPP::Integer v30; // [sp+B0h] [bp-178h]@1
  char v31; // [sp+D8h] [bp-150h]@1
  CryptoPP::Integer v32; // [sp+E8h] [bp-140h]@1
  char v33; // [sp+110h] [bp-118h]@1
  char v34; // [sp+120h] [bp-108h]@1
  char v35; // [sp+130h] [bp-F8h]@1
  __int64 v36; // [sp+140h] [bp-E8h]@1
  __int64 v37; // [sp+148h] [bp-E0h]@1
  __int64 v38; // [sp+150h] [bp-D8h]@1
  __int64 *v39; // [sp+158h] [bp-D0h]@1
  __int64 v40; // [sp+160h] [bp-C8h]@1
  char *v41; // [sp+168h] [bp-C0h]@1
  __int64 v42; // [sp+170h] [bp-B8h]@1
  __int64 v43; // [sp+178h] [bp-B0h]@1
  unsigned __int64 v44; // [sp+180h] [bp-A8h]@1
  char *v45; // [sp+188h] [bp-A0h]@1
  __int64 v46; // [sp+190h] [bp-98h]@1
  unsigned __int64 v47; // [sp+198h] [bp-90h]@1
  unsigned __int64 v48; // [sp+1A0h] [bp-88h]@1
  unsigned __int64 v49; // [sp+1A8h] [bp-80h]@1
  unsigned __int8 *v50; // [sp+1B0h] [bp-78h]@1
  __int64 v51; // [sp+1B8h] [bp-70h]@1
  CryptoPP::Integer *v52; // [sp+1C0h] [bp-68h]@1
  CryptoPP::Integer *v53; // [sp+1C8h] [bp-60h]@1
  __int64 *v54; // [sp+1D0h] [bp-58h]@1
  unsigned __int64 v55; // [sp+1D8h] [bp-50h]@1
  char *v56; // [sp+1E0h] [bp-48h]@1
  unsigned __int64 v57; // [sp+1E8h] [bp-40h]@1
  char *v58; // [sp+1F0h] [bp-38h]@1
  __int64 v59; // [sp+1F8h] [bp-30h]@1
  __int64 v60; // [sp+200h] [bp-28h]@1
  __int64 v61; // [sp+230h] [bp+8h]@1
  __int64 v62; // [sp+238h] [bp+10h]@1
  __int64 v63; // [sp+248h] [bp+20h]@1

  v63 = a4;
  v62 = a2;
  v61 = a1;
  v36 = -2i64;
  v37 = *(_QWORD *)(a1 + 8);
  LODWORD(v4) = (*(int (__fastcall **)(signed __int64))(v37 + 32))(a1 + 8);
  CryptoPP::CryptoMaterial::DoQuickSanityCheck(v4);
  v25 = v63;
  LODWORD(v5) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v61 + 136i64))(v61);
  v28 = v5;
  LODWORD(v6) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::GetAbstractGroupParameters(v61 + 16);
  v26 = v6;
  v38 = *(_QWORD *)(v61 + 16);
  LODWORD(v7) = (*(int (__fastcall **)(signed __int64))(v38 + 8))(v61 + 16);
  v27 = v7;
  LODWORD(v8) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::MessageRepresentativeLength(v61);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v24,
    v8);
  LODWORD(v9) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v61 + 144i64))(v61);
  v39 = v9;
  LODWORD(v10) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::MessageRepresentativeBitLength(v61);
  v40 = v10;
  v41 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v24);
  v42 = *(_QWORD *)v61;
  LODWORD(v11) = (*(int (__fastcall **)(__int64, char *))(v42 + 152))(v61, &v31);
  qmemcpy(&v34, v11, 0x10ui64);
  LODWORD(v12) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v25 + 144i64))(v25);
  v43 = v12;
  v44 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 8));
  v45 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 8));
  v14 = CryptoPP::NullRNG(v13);
  v46 = *v39;
  v15 = *(_BYTE *)(v25 + 184);
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v46 + 48))(
    v39,
    v14,
    v45,
    v44);
  *(_BYTE *)(v25 + 184) = 1;
  v47 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v24);
  v16 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v24);
  CryptoPP::Integer::Integer(&v30, (const unsigned __int8 *)v16, v47, 0);
  v17 = (*(int (__fastcall **)(__int64, _QWORD))(*(_QWORD *)v26 + 96i64))(v26, 0i64);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(
    (CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 56),
    (unsigned int)v17);
  v48 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 80));
  v18 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 80));
  CryptoPP::Integer::Integer(&v29, (const unsigned __int8 *)v18, v48, 0);
  v49 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 56));
  v50 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 56));
  v51 = *v28;
  LODWORD(v19) = (*(int (__fastcall **)(__int64 *, CryptoPP::Integer *, __int64, __int64))(v51 + 16))(
                   v28,
                   &v32,
                   v26,
                   v27);
  v52 = v19;
  v53 = v19;
  CryptoPP::Integer::Encode(v19, v50, v49, 0);
  CryptoPP::Integer::~Integer(&v32);
  LODWORD(v20) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v61 + 144i64))(v61);
  v54 = v20;
  v55 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 80));
  v56 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 80));
  v57 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 56));
  v58 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v25 + 56));
  v59 = *(_QWORD *)v61;
  LODWORD(v21) = (*(int (__fastcall **)(__int64, char *))(v59 + 152))(v61, &v33);
  qmemcpy(&v35, v21, 0x10ui64);
  LODWORD(v22) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v25 + 144i64))(v25);
  v60 = *v54;
  (*(void (__fastcall **)(__int64 *, __int64, __int64, char *))(v60 + 72))(v54, v62, v22, &v35);
  CryptoPP::Integer::~Integer(&v29);
  CryptoPP::Integer::~Integer(&v30);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v24);
  return v62;
}
