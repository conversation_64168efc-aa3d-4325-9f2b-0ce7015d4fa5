/*
 * Function: _std::_Vector_iterator_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::operator__::_1_::dtor$0
 * Address: 0x140372EC0
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::operator__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>((std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)(a2 + 40));
}
