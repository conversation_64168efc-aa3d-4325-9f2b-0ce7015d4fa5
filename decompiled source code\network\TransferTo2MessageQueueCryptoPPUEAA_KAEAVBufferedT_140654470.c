/*
 * Function: ?TransferTo2@MessageQueue@CryptoPP@@UEAA_KAEAVBufferedTransformation@2@AEA_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z
 * Address: 0x140654470
 */

__int64 __fastcall CryptoPP::MessageQueue::TransferTo2(__int64 a1, __int64 a2, unsigned __int64 *a3, __int64 a4)
{
  unsigned __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // ST30_8@1
  _QWORD *v7; // rax@1
  unsigned __int64 a; // [sp+38h] [bp-20h]@1
  __int64 v10; // [sp+40h] [bp-18h]@1
  _QWORD *v11; // [sp+48h] [bp-10h]@1
  __int64 v12; // [sp+60h] [bp+8h]@1
  __int64 v13; // [sp+68h] [bp+10h]@1
  unsigned __int64 *b; // [sp+70h] [bp+18h]@1
  __int64 v15; // [sp+78h] [bp+20h]@1

  v15 = a4;
  b = a3;
  v13 = a2;
  v12 = a1;
  LODWORD(v4) = (*(int (**)(void))(*(_QWORD *)a1 + 120i64))();
  a = v4;
  *b = *CryptoPP::STDMIN<unsigned __int64>(&a, b);
  v10 = *(_QWORD *)(v12 + 32);
  LODWORD(v5) = (*(int (__fastcall **)(signed __int64, __int64, unsigned __int64 *, __int64))(v10 + 248))(
                  v12 + 32,
                  v13,
                  b,
                  v15);
  v6 = v5;
  LODWORD(v7) = std::deque<unsigned __int64,std::allocator<unsigned __int64>>::front(v12 + 112);
  v11 = v7;
  *v7 -= *b;
  return v6;
}
