/*
 * Function: ?construct@?$allocator@VCUnmannedTraderSchedule@@@std@@QEAAXPEAVCUnmannedTraderSchedule@@AEBV3@@Z
 * Address: 0x1403970E0
 */

void __fastcall std::allocator<CUnmannedTraderSchedule>::construct(std::allocator<CUnmannedTraderSchedule> *this, CUnmannedTraderSchedule *_Ptr, CUnmannedTraderSchedule *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<CUnmannedTraderSchedule,CUnmannedTraderSchedule>(_Ptr, _Val);
}
