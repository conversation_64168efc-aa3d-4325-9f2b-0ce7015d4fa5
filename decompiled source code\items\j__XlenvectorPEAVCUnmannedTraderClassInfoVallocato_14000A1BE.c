/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@KAXXZ
 * Address: 0x14000A1BE
 */

void __fastcall __noreturn std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_<PERSON><PERSON>(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_<PERSON><PERSON>(this);
}
