/**
 * @file CGameObject.cpp
 * @brief Base Game Object Class Implementation
 * 
 * Provides the fundamental base class for all game objects in the NexusProtection
 * engine. This class serves as the root of the object hierarchy.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "../Headers/CGameObject.h"
#include <iostream>
#include <sstream>
#include <cmath>
#include <algorithm>
#include <cstring>
#include <chrono>

namespace NexusProtection::World {

    // Static member initialization
    int CGameObject::s_nTotalObjectNum = 0;
    uint32_t CGameObject::s_dwNextSerial = 1;
    std::mutex CGameObject::s_staticMutex;

    // CGameObjectVtbl implementation
    CGameObjectVtbl::CGameObjectVtbl() {
        // Initialize virtual function table with null pointers
        std::fill(std::begin(functions), std::end(functions), nullptr);
    }

    // _object_id implementation
    bool _object_id::IsValid() const {
        return dwSerial != 0 && wIndex != 0;
    }

    std::string _object_id::ToString() const {
        std::ostringstream oss;
        oss << "ObjectID{Serial:" << dwSerial << ", Index:" << wIndex 
            << ", Type:" << static_cast<int>(byType) << ", Flags:" << static_cast<int>(byFlags) << "}";
        return oss.str();
    }

    void _object_id::Reset() {
        dwSerial = 0;
        wIndex = 0;
        byType = 0;
        byFlags = 0;
    }

    // _object_create_setdata implementation
    bool _object_create_setdata::IsValid() const {
        return pObjectId != nullptr && pObjectId->IsValid() && 
               objectType != GameObjectType::Unknown;
    }

    // CGameObject implementation
    CGameObject::CGameObject() {
        InitializeDefaults();
        m_dwObjSerial = GenerateSerial();
        IncrementObjectCount();
        
        std::cout << "[DEBUG] CGameObject created with serial: " << m_dwObjSerial 
                  << " (Total objects: " << s_nTotalObjectNum << ")" << std::endl;
    }

    CGameObject::~CGameObject() {
        DecrementObjectCount();
        std::cout << "[DEBUG] CGameObject destroyed: " << m_dwObjSerial 
                  << " (Total objects: " << s_nTotalObjectNum << ")" << std::endl;
    }

    bool CGameObject::Create(const _object_create_setdata* pData) {
        std::lock_guard<std::mutex> lock(m_objectMutex);
        
        try {
            if (!ValidateCreateData(pData)) {
                LogError("Invalid creation data", "Create");
                return false;
            }

            // Set basic properties from creation data
            if (pData->pObjectId) {
                m_dwObjSerial = pData->pObjectId->dwSerial;
                m_wIndex = pData->pObjectId->wIndex;
            }
            
            m_objectType = pData->objectType;
            SetPosition(pData->position);
            
            // Set creation time
            m_dwCreationTime = GetCurrentTime();
            m_dwLastUpdateTime = m_dwCreationTime;
            
            // Set active state
            m_state = GameObjectState::Active;

            std::cout << "[INFO] Game object created successfully: " << GetDebugInfo() << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            LogError("Exception during creation: " + std::string(e.what()), "Create");
            return false;
        }
    }

    bool CGameObject::Init(_object_id* pID) {
        std::lock_guard<std::mutex> lock(m_objectMutex);
        
        try {
            if (!pID || !pID->IsValid()) {
                LogError("Invalid object ID", "Init");
                return false;
            }

            // Set object identification
            m_dwObjSerial = pID->dwSerial;
            m_wIndex = pID->wIndex;
            
            // Initialize default values based on original decompiled logic
            // Original: pItem->m_nScreenPos[0] = 0; pItem->m_nScreenPos[1] = 0;
            m_nScreenPos[0] = 0;
            m_nScreenPos[1] = 0;
            
            // Original: LODWORD(pItem->m_fOldPos[0]) = 0; etc.
            m_fOldPos[0] = 0.0f;
            m_fOldPos[1] = 0.0f;
            m_fOldPos[2] = 0.0f;
            
            // Original: pItem->m_pCurMap = 0i64;
            m_pCurMap = nullptr;
            
            // Original: pItem->m_wMapLayerIndex = 0;
            m_wMapLayerIndex = 0;
            
            // Original: pItem->m_dwNextFreeStunTime = -1;
            m_dwNextFreeStunTime = static_cast<uint32_t>(-1);
            
            // Original: pItem->m_dwOldTickBreakTranspar = -1;
            m_dwOldTickBreakTranspar = static_cast<uint32_t>(-1);
            
            // Original: pItem->m_bBreakTranspar = 0;
            m_bBreakTranspar = false;
            
            // Original: pItem->m_bPlayerCircleList = 0i64;
            m_bPlayerCircleList = nullptr;
            
            // Original: pItem->m_bObserver = 0;
            m_bObserver = false;
            
            // Original: pItem->m_dwCurSec = 0;
            m_dwCurSec = 0;
            
            // Set initialization time
            m_dwCreationTime = GetCurrentTime();
            m_dwLastUpdateTime = m_dwCreationTime;
            
            // Set active state
            m_state = GameObjectState::Active;
            
            // Check total object limit (original: if ( ++CGameObject::s_nTotalObjectNum > 42642 ))
            if (s_nTotalObjectNum > 42642) {
                LogError("Object limit exceeded (42642)", "Init");
                // In original: MyMessageBox("error", "CGameObject::Init : Lack Object Num");
                // In original: ServerProgramExit("CGameObject::Init()", 0);
                return false;
            }
            
            std::cout << "[INFO] Game object initialized: " << pID->ToString() << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            LogError("Exception during initialization: " + std::string(e.what()), "Init");
            return false;
        }
    }

    bool CGameObject::Destroy() {
        std::lock_guard<std::mutex> lock(m_objectMutex);
        
        try {
            // Set destroying state
            m_state = GameObjectState::Destroying;
            
            // Reset object data
            m_dwObjSerial = INVALID_SERIAL;
            m_wIndex = INVALID_INDEX;
            m_objectType = GameObjectType::Unknown;
            
            // Clear position data
            m_fCurPos.fill(0.0f);
            m_fOldPos.fill(0.0f);
            m_nScreenPos.fill(0);
            
            // Clear map data
            m_pCurMap = nullptr;
            m_wMapLayerIndex = 0;
            
            // Reset timing
            m_dwNextFreeStunTime = static_cast<uint32_t>(-1);
            m_dwOldTickBreakTranspar = static_cast<uint32_t>(-1);
            m_dwCurSec = 0;
            
            // Reset flags
            m_bBreakTranspar = false;
            m_bObserver = false;
            
            // Clear circle list
            m_bPlayerCircleList = nullptr;
            
            // Set destroyed state
            m_state = GameObjectState::Destroyed;
            
            std::cout << "[INFO] Game object destroyed successfully" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            LogError("Exception during destruction: " + std::string(e.what()), "Destroy");
            return false;
        }
    }

    void CGameObject::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_objectMutex);
        
        if (m_state != GameObjectState::Active) {
            return;
        }
        
        try {
            UpdateTimestamp();
            UpdateInternalState(deltaTime);
            
        } catch (const std::exception& e) {
            LogError("Exception during update: " + std::string(e.what()), "Update");
        }
    }

    void CGameObject::SetPosition(float x, float y, float z) {
        std::lock_guard<std::mutex> lock(m_objectMutex);
        
        // Store old position
        m_fOldPos[0] = m_fCurPos[0];
        m_fOldPos[1] = m_fCurPos[1];
        m_fOldPos[2] = m_fCurPos[2];
        
        // Set new position
        m_fCurPos[0] = x;
        m_fCurPos[1] = y;
        m_fCurPos[2] = z;
    }

    void CGameObject::SetPosition(const float* pos) {
        if (!pos) return;
        SetPosition(pos[0], pos[1], pos[2]);
    }

    void CGameObject::GetPosition(float* pos) const {
        if (!pos) return;
        std::lock_guard<std::mutex> lock(m_objectMutex);
        pos[0] = m_fCurPos[0];
        pos[1] = m_fCurPos[1];
        pos[2] = m_fCurPos[2];
    }

    void CGameObject::SetOldPosition(const float* pos) {
        if (!pos) return;
        std::lock_guard<std::mutex> lock(m_objectMutex);
        m_fOldPos[0] = pos[0];
        m_fOldPos[1] = pos[1];
        m_fOldPos[2] = pos[2];
    }

    void CGameObject::GetOldPosition(float* pos) const {
        if (!pos) return;
        std::lock_guard<std::mutex> lock(m_objectMutex);
        pos[0] = m_fOldPos[0];
        pos[1] = m_fOldPos[1];
        pos[2] = m_fOldPos[2];
    }

    void CGameObject::SetScreenPosition(int x, int y) {
        std::lock_guard<std::mutex> lock(m_objectMutex);
        m_nScreenPos[0] = x;
        m_nScreenPos[1] = y;
    }

    void CGameObject::GetScreenPosition(int* pos) const {
        if (!pos) return;
        std::lock_guard<std::mutex> lock(m_objectMutex);
        pos[0] = m_nScreenPos[0];
        pos[1] = m_nScreenPos[1];
    }

    void CGameObject::UpdateTimestamp() {
        m_dwLastUpdateTime = GetCurrentTime();
    }

    void CGameObject::CircleReport(void* type, void* msg, int size, int flags) {
        // Placeholder implementation for circle reporting
        // In the original, this would handle area effect notifications
        std::cout << "[DEBUG] CircleReport called - Type: " << type 
                  << ", Size: " << size << ", Flags: " << flags << std::endl;
    }

    bool CGameObject::ValidateState() const {
        std::lock_guard<std::mutex> lock(m_objectMutex);

        // Basic validation checks
        if (m_dwObjSerial == INVALID_SERIAL) {
            return false;
        }

        if (m_state == GameObjectState::Uninitialized || m_state == GameObjectState::Destroyed) {
            return false;
        }

        return true;
    }

    void CGameObject::LogError(const std::string& message, const std::string& function) const {
        std::cout << "[ERROR] CGameObject::" << function << " - " << message
                  << " (Serial: " << m_dwObjSerial << ")" << std::endl;
    }

    std::string CGameObject::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_objectMutex);

        std::ostringstream oss;
        oss << "CGameObject{";
        oss << "Serial:" << m_dwObjSerial;
        oss << ", Index:" << m_wIndex;
        oss << ", Type:" << GameObjectUtils::GameObjectTypeToString(m_objectType);
        oss << ", State:" << GameObjectUtils::GameObjectStateToString(m_state);
        oss << ", Pos:[" << m_fCurPos[0] << "," << m_fCurPos[1] << "," << m_fCurPos[2] << "]";
        oss << ", MapLayer:" << m_wMapLayerIndex;
        oss << "}";
        return oss.str();
    }

    uint32_t CGameObject::GetNextSerial() {
        std::lock_guard<std::mutex> lock(s_staticMutex);
        return s_dwNextSerial++;
    }

    // Protected methods
    void CGameObject::InitializeDefaults() {
        // Initialize virtual function table
        static CGameObjectVtbl defaultVtbl;
        vfptr = &defaultVtbl;

        m_dwObjSerial = INVALID_SERIAL;
        m_wIndex = INVALID_INDEX;
        m_objectType = GameObjectType::Unknown;
        m_state = GameObjectState::Uninitialized;

        // Initialize position arrays
        m_fCurPos.fill(0.0f);
        m_fOldPos.fill(0.0f);
        m_nScreenPos.fill(0);

        // Initialize map data
        m_pCurMap = nullptr;
        m_wMapLayerIndex = 0;

        // Initialize timing
        m_dwCreationTime = GetCurrentTime();
        m_dwLastUpdateTime = m_dwCreationTime;
        m_dwNextFreeStunTime = static_cast<uint32_t>(-1);
        m_dwOldTickBreakTranspar = static_cast<uint32_t>(-1);
        m_dwCurSec = 0;

        // Initialize flags
        m_bBreakTranspar = false;
        m_bObserver = false;

        // Initialize circle list
        m_bPlayerCircleList = nullptr;
    }

    bool CGameObject::ValidateCreateData(const _object_create_setdata* pData) const {
        if (!pData) {
            return false;
        }

        if (!pData->IsValid()) {
            return false;
        }

        if (!GameObjectUtils::IsValidPosition(pData->position)) {
            return false;
        }

        return true;
    }

    void CGameObject::UpdateInternalState(float deltaTime) {
        // Update current second based on elapsed time
        uint32_t currentTime = GetCurrentTime();
        m_dwCurSec = currentTime / 1000; // Convert milliseconds to seconds

        // Additional internal state updates can be added here
    }

    // Private methods
    uint32_t CGameObject::GenerateSerial() {
        std::lock_guard<std::mutex> lock(s_staticMutex);
        return s_dwNextSerial++;
    }

    uint32_t CGameObject::GetCurrentTime() const {
        auto now = std::chrono::steady_clock::now();
        auto duration = now.time_since_epoch();
        auto millis = std::chrono::duration_cast<std::chrono::milliseconds>(duration).count();
        return static_cast<uint32_t>(millis);
    }

    void CGameObject::IncrementObjectCount() {
        std::lock_guard<std::mutex> lock(s_staticMutex);
        ++s_nTotalObjectNum;
    }

    void CGameObject::DecrementObjectCount() {
        std::lock_guard<std::mutex> lock(s_staticMutex);
        --s_nTotalObjectNum;
    }

    // Utility functions
    namespace GameObjectUtils {
        std::string GameObjectTypeToString(GameObjectType type) {
            switch (type) {
                case GameObjectType::Unknown: return "Unknown";
                case GameObjectType::Character: return "Character";
                case GameObjectType::Monster: return "Monster";
                case GameObjectType::Player: return "Player";
                case GameObjectType::NPC: return "NPC";
                case GameObjectType::Item: return "Item";
                case GameObjectType::Projectile: return "Projectile";
                case GameObjectType::Effect: return "Effect";
                case GameObjectType::Trigger: return "Trigger";
                case GameObjectType::Portal: return "Portal";
                case GameObjectType::Building: return "Building";
                case GameObjectType::Vehicle: return "Vehicle";
                case GameObjectType::Trap: return "Trap";
                case GameObjectType::Zone: return "Zone";
                default: return "Invalid";
            }
        }

        std::string GameObjectStateToString(GameObjectState state) {
            switch (state) {
                case GameObjectState::Uninitialized: return "Uninitialized";
                case GameObjectState::Initializing: return "Initializing";
                case GameObjectState::Active: return "Active";
                case GameObjectState::Inactive: return "Inactive";
                case GameObjectState::Destroying: return "Destroying";
                case GameObjectState::Destroyed: return "Destroyed";
                default: return "Invalid";
            }
        }

        bool IsValidSerial(uint32_t serial) {
            return serial != CGameObject::INVALID_SERIAL;
        }

        bool IsValidPosition(const float* pos) {
            if (!pos) return false;

            // Check for NaN or infinite values
            for (int i = 0; i < 3; ++i) {
                if (std::isnan(pos[i]) || std::isinf(pos[i])) {
                    return false;
                }
            }

            // Check for reasonable bounds
            const float MAX_COORD = 100000.0f;
            for (int i = 0; i < 3; ++i) {
                if (std::abs(pos[i]) > MAX_COORD) {
                    return false;
                }
            }

            return true;
        }

        float CalculateDistance(const float* pos1, const float* pos2) {
            if (!pos1 || !pos2) return 0.0f;

            float dx = pos1[0] - pos2[0];
            float dy = pos1[1] - pos2[1];
            float dz = pos1[2] - pos2[2];

            return std::sqrt(dx * dx + dy * dy + dz * dz);
        }
    }

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {
    void CGameObject_Constructor(CGameObject_Legacy* object) {
        if (object) {
            // Initialize legacy structure
            object->vfptr = nullptr;
            object->m_dwObjSerial = 0;
            std::cout << "[DEBUG] Legacy CGameObject constructed" << std::endl;
        }
    }

    void CGameObject_Destructor(CGameObject_Legacy* object) {
        if (object) {
            std::cout << "[DEBUG] Legacy CGameObject destructed" << std::endl;
        }
    }

    bool CGameObject_Create(CGameObject_Legacy* object, _object_create_setdata* pData) {
        if (!object || !pData) {
            return false;
        }

        std::cout << "[DEBUG] Legacy CGameObject create called" << std::endl;
        return true;
    }

    void CGameObject_Init(CGameObject_Legacy* object, _object_id* pID) {
        if (object && pID) {
            object->m_dwObjSerial = pID->dwSerial;
            std::cout << "[DEBUG] Legacy CGameObject init called with ID: " << pID->ToString() << std::endl;
        }
    }

    bool CGameObject_Destroy(CGameObject_Legacy* object) {
        if (object) {
            object->m_dwObjSerial = 0;
            std::cout << "[DEBUG] Legacy CGameObject destroy called" << std::endl;
            return true;
        }
        return false;
    }

    void CGameObject_CircleReport(CGameObject_Legacy* object, void* type, void* msg, int size, int flags) {
        if (object) {
            std::cout << "[DEBUG] Legacy CGameObject CircleReport called" << std::endl;
        }
    }
}
