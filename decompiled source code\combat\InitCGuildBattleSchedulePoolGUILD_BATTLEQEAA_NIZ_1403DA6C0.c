/*
 * Function: ?Init@CGuildBattleSchedulePool@GUILD_BATTLE@@QEAA_NI@Z
 * Address: 0x1403DA6C0
 */

char __fastcall GUILD_BATTLE::CGuildBattleSchedulePool::Init(GUILD_BATTLE::CGuildBattleSchedulePool *this, unsigned int uiMapCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@7
  __int64 v6; // rax@11
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@14
  __int64 v8; // [sp+0h] [bp-68h]@1
  unsigned int dwScheduleID; // [sp+20h] [bp-48h]@8
  GUILD_BATTLE::CGuildBattleSchedule **v10; // [sp+28h] [bp-40h]@6
  GUILD_BATTLE::CGuildBattleSchedule *v11; // [sp+30h] [bp-38h]@13
  GUILD_BATTLE::CGuildBattleSchedule *v12; // [sp+38h] [bp-30h]@10
  __int64 v13; // [sp+40h] [bp-28h]@4
  unsigned __int64 v14; // [sp+48h] [bp-20h]@6
  GUILD_BATTLE::CGuildBattleSchedule *v15; // [sp+50h] [bp-18h]@11
  GUILD_BATTLE::CGuildBattleSchedulePool *v16; // [sp+70h] [bp+8h]@1
  unsigned int v17; // [sp+78h] [bp+10h]@1

  v17 = uiMapCnt;
  v16 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  if ( uiMapCnt )
  {
    v16->m_dwMaxScheduleCnt = 46 * uiMapCnt;
    v14 = v16->m_dwMaxScheduleCnt;
    v10 = (GUILD_BATTLE::CGuildBattleSchedule **)operator new[](saturated_mul(8ui64, v14));
    v16->m_ppkSchedule = v10;
    if ( v16->m_ppkSchedule )
    {
      memset_0(v16->m_ppkSchedule, 0, 8i64 * v16->m_dwMaxScheduleCnt);
      for ( dwScheduleID = 0; dwScheduleID < v16->m_dwMaxScheduleCnt; ++dwScheduleID )
      {
        v12 = (GUILD_BATTLE::CGuildBattleSchedule *)operator new(0x28ui64);
        if ( v12 )
        {
          GUILD_BATTLE::CGuildBattleSchedule::CGuildBattleSchedule(v12, dwScheduleID);
          v15 = (GUILD_BATTLE::CGuildBattleSchedule *)v6;
        }
        else
        {
          v15 = 0i64;
        }
        v11 = v15;
        v16->m_ppkSchedule[dwScheduleID] = v15;
        if ( !v16->m_ppkSchedule[dwScheduleID] )
        {
          v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v7,
            "CGuildBattleSchedulePool() : new CGuildBattleSchedule( %u ) Fail!",
            dwScheduleID);
          return 0;
        }
      }
      v16->m_uiMapCnt = v17;
      result = 1;
    }
    else
    {
      v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v5,
        "CGuildBattleSchedulePool() : new CGuildBattleSchedule * [%u] Fail!",
        23i64);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
