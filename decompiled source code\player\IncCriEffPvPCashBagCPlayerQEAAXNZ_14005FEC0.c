/*
 * Function: ?IncCriEffPvPCashBag@CPlayer@@QEAAXN@Z
 * Address: 0x14005FEC0
 */

void __fastcall CPlayer::IncCriEffPvPCashBag(CPlayer *this, long double dAlter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp-20h] [bp-38h]@1
  struct CHolyStone *v5; // [sp+0h] [bp-18h]@6
  CPlayer *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dAlter > 0.0 && v6->m_byHSKQuestCode != 100 )
  {
    v5 = &g_Stone[CPlayerDB::GetRaceCode(&v6->m_Param)];
    if ( v5->m_bOper )
    {
      if ( v5->m_pCurMap == v6->m_pCurMap )
      {
        v6->m_nHSKPvpPoint = (signed int)floor((double)v6->m_nHSKPvpPoint + dAlter);
        CPlayer::SendMsg_HSKQuestActCum(v6);
        v6->m_pUserDB->m_AvatorData.m_iPvpPoint = v6->m_nHSKPvpPoint;
      }
    }
  }
}
