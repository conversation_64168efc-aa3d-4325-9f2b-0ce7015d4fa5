/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderSortType@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSortType@@@std@@@Z
 * Address: 0x14000E71E
 */

CUnmannedTraderSortType **__fastcall stdext::_Unchecked_uninitialized_move<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Dest, std::allocator<CUnmannedTraderSortType *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
