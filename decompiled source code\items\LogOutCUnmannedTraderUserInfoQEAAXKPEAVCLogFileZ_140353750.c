/*
 * Function: ?LogOut@CUnmannedTraderUserInfo@@QEAAXKPEAVCLogFile@@@Z
 * Address: 0x140353750
 */

void __fastcall CUnmannedTraderUserInfo::LogOut(CUnmannedTraderUserInfo *this, unsigned int dwSerial, CLogFile *pkLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@6
  CUnmannedTraderUserInfo *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_dwUserSerial != dwSerial && pkLogger )
  {
    v5 = v8->m_wInx;
    v7 = dwSerial;
    CLogFile::Write(
      pkLogger,
      "CUnmannedTraderUserInfo::LogOut( DWORD dwSerial )\r\n\t\tm_wInx(%u) m_dwSerial(%u) != dwSerial(%u)\r\n",
      v5,
      v8->m_dwUserSerial);
  }
  CUnmannedTraderUserInfo::Clear(v8);
  v8->m_eState = 0;
}
