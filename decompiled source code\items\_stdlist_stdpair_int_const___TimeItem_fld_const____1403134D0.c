/*
 * Function: _std::list_std::pair_int_const___TimeItem_fld_const_____ptr64__std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64_____::erase_::_1_::dtor$3
 * Address: 0x1403134D0
 */

void __fastcall std::list_std::pair_int_const___TimeItem_fld_const_____ptr64__std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64_____::erase_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 144) & 2 )
  {
    *(_DWORD *)(a2 + 144) &= 0xFFFFFFFD;
    std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *)(a2 + 64));
  }
}
