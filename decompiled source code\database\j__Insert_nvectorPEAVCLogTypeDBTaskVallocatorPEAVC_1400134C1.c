/*
 * Function: j_?_Insert_n@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@2@_KAEBQEAVCLogTypeDBTask@@@Z
 * Address: 0x1400134C1
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Insert_n(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Where, unsigned __int64 _Count, CLogTypeDBTask *const *_Val)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Insert_n(this, _Where, _Count, _Val);
}
