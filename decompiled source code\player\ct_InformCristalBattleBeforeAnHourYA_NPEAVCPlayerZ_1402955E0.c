/*
 * Function: ?ct_InformCristalBattleBeforeAnHour@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402955E0
 */

char __fastcall ct_InformCristalBattleBeforeAnHour(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@8
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v5 = atoi(s_pwszDstCheat[1]);
      CHolyStoneSystem::AlterSchedule(&g_HolySys, 0, v5);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
