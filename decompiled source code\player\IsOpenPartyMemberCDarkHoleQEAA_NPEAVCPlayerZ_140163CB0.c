/*
 * Function: ?IsOpenPartyMember@CDarkHole@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140163CB0
 */

bool __fastcall CDarkHole::IsOpenPartyMember(CDarkHole *this, CPlayer *pOpener)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CDarkHole *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_pChannel )
    result = CDarkHoleChannel::IsOpenPartyMember(v6->m_pChannel, pOpener);
  else
    result = 0;
  return result;
}
