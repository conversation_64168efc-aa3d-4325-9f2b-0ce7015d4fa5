/*
 * Function: ?CheckKeeperPlusTime@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027DCB0
 */

void __fastcall CHolyStoneSystem::CheckKeeperPlusTime(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  CHolyScheduleData::__HolyScheduleNode *v5; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CHolyStoneSystem::GetNumOfTime(v6);
  v5 = CHolyScheduleData::GetIndex(&v6->m_ScheculeData, (unsigned __int8)v3);
  v6->m_SaveData.m_dwTerm[0] = v5->m_nSceneTime[5]
                             - (v5->m_nSceneTime[2]
                              + v5->m_nSceneTime[0]
                              + GetLoopTime()
                              - v6->m_dwCheckTime[0]);
}
