/*
 * Function: _std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::insert_::_1_::dtor$4
 * Address: 0x140388C70
 */

void __fastcall std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::insert_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 2 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(*(std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > **)(a2 + 216));
  }
}
