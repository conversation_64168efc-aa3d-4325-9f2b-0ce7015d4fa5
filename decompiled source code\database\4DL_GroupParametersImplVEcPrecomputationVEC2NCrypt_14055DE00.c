/*
 * Function: ??4?$DL_GroupParametersImpl@V?$EcPrecomputation@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@2@V?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055DE00
 */

__int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::operator=(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>::operator=();
  CryptoPP::EcPrecomputation<CryptoPP::EC2N>::operator=(v3 + 24, v4 + 24);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::operator=(v3 + 152, v4 + 152);
  return v3;
}
