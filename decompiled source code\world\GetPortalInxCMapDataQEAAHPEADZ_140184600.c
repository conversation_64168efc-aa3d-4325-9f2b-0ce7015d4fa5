/*
 * Function: ?GetPortalInx@CMapData@@QEAAHPEAD@Z
 * Address: 0x140184600
 */

signed __int64 __fastcall CMapData::GetPortalInx(CMapData *this, char *pPortalCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  CMapData *v7; // [sp+40h] [bp+8h]@1
  const char *Str1; // [sp+48h] [bp+10h]@1

  Str1 = pPortalCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; (signed int)j < v7->m_nPortalNum; ++j )
  {
    if ( !strcmp_0(Str1, v7->m_pPortal[j].m_pPortalRec->m_strCode) )
      return j;
  }
  return 0xFFFFFFFFi64;
}
