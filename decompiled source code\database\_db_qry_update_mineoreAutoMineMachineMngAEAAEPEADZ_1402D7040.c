/*
 * Function: ?_db_qry_update_mineore@AutoMineMachineMng@@AEAAEPEAD@Z
 * Address: 0x1402D7040
 */

char __fastcall AutoMineMachineMng::_db_qry_update_mineore(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  char bySlot; // [sp+20h] [bp-38h]@4
  unsigned int dwK; // [sp+28h] [bp-30h]@4
  char byNum; // [sp+30h] [bp-28h]@4
  unsigned int dwGage; // [sp+38h] [bp-20h]@4
  char *v10; // [sp+40h] [bp-18h]@4

  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = pdata;
  dwGage = *(_DWORD *)(pdata + 13);
  byNum = pdata[12];
  dwK = *((_DWORD *)pdata + 2);
  bySlot = pdata[7];
  if ( CRFWorldDatabase::update_amine_mineore(
         pkDB,
         pdata[1],
         pdata[2],
         *(_DWORD *)(pdata + 3),
         bySlot,
         dwK,
         byNum,
         dwGage) )
  {
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
