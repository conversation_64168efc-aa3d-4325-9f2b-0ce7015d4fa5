/*
 * Function: ?IsolatedMessageSeriesEnd@?$InputRejecting@VFilter@CryptoPP@@@CryptoPP@@UEAA_N_N@Z
 * Address: 0x14044E910
 */

void __fastcall __noreturn CryptoPP::InputRejecting<CryptoPP::Filter>::IsolatedMessageSeriesEnd(CryptoPP::InputRejecting<CryptoPP::Filter> *this, bool __formal)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  CryptoPP::InputRejecting<CryptoPP::Filter>::InputRejected v5; // [sp+20h] [bp-58h]@4

  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CryptoPP::InputRejecting<CryptoPP::Filter>::InputRejected::InputRejected(&v5);
  CxxThrowException_0(&v5, &TI4_AUInputRejected___InputRejecting_VFilter_CryptoPP___CryptoPP__);
}
