/*
 * Function: ?Kill@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAHPEAVCNormalGuildBattleGuildMember@2@0@Z
 * Address: 0x1403E1560
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Kill(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkSrcMember, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkDestMember)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+30h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v8; // [sp+38h] [bp+10h]@1

  v8 = pkSrcMember;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_dwKillPoint += 10;
  ++v7->m_dwKillCountSum;
  if ( pkSrcMember && !GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(pkSrcMember) )
    GUILD_BATTLE::CNormalGuildBattleGuildMember::AddKillCnt(v8);
  GUILD_BATTLE::CNormalGuildBattleGuild::UpdateScore(v7);
  return 10i64;
}
