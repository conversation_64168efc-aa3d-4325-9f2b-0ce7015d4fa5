/*
 * Function: ?dec_ref@table_obj@lua_tinker@@QEAAXXZ
 * Address: 0x140446260
 */

void __fastcall lua_tinker::table_obj::dec_ref(lua_tinker::table_obj *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  lua_tinker::table_obj *v4; // [sp+20h] [bp-28h]@5
  lua_tinker::table_obj *v5; // [sp+28h] [bp-20h]@5
  lua_tinker::table_obj *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !--v6->m_ref )
  {
    v5 = v6;
    v4 = v6;
    if ( v6 )
      lua_tinker::table_obj::`scalar deleting destructor'(v4, 1u);
  }
}
