/*
 * Function: ?CreateEncryptionFilter@PK_Encryptor@CryptoPP@@UEBAPEAVBufferedTransformation@2@AEAVRandomNumberGenerator@2@PEAV32@AEBVNameValuePairs@2@@Z
 * Address: 0x1405F5E10
 */

struct CryptoPP::BufferedTransformation *__fastcall CryptoPP::PK_Encryptor::CreateEncryptionFilter(CryptoPP::PK_Encryptor *this, struct CryptoPP::RandomNumberGenerator *a2, struct CryptoPP::BufferedTransformation *a3, const struct CryptoPP::NameValuePairs *a4)
{
  CryptoPP::PK_DefaultEncryptionFilter *v5; // [sp+38h] [bp-20h]@1
  __int64 v6; // [sp+48h] [bp-10h]@2
  const struct CryptoPP::PK_Encryptor *v7; // [sp+60h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v8; // [sp+68h] [bp+10h]@1
  struct CryptoPP::BufferedTransformation *v9; // [sp+70h] [bp+18h]@1
  struct CryptoPP::NameValuePairs *v10; // [sp+78h] [bp+20h]@1

  v10 = (struct CryptoPP::NameValuePairs *)a4;
  v9 = a3;
  v8 = a2;
  v7 = this;
  v5 = (CryptoPP::PK_DefaultEncryptionFilter *)operator new(0xB0ui64);
  if ( v5 )
    v6 = CryptoPP::PK_DefaultEncryptionFilter::PK_DefaultEncryptionFilter(v5, v8, v7, v9, v10);
  else
    v6 = 0i64;
  return (struct CryptoPP::BufferedTransformation *)v6;
}
