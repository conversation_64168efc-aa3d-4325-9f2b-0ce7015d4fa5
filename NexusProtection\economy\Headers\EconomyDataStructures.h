#pragma once

#include <cstdint>
#include <array>
#include <string>
#include <chrono>

namespace NexusProtection::Economy {

    /**
     * @brief Economy log sheet structure for tracking economic events
     * 
     * This structure represents a log entry for economic activities.
     * Size: 208 bytes (as per original decompiled code)
     */
    struct LogSheetEconomy {
        char eventType[32];           // Type of economic event
        char playerName[64];          // Player involved in the event
        char guildName[64];           // Guild involved (if applicable)
        uint32_t timestamp;           // Event timestamp
        double dalantAmount;          // Dalant amount involved
        double goldAmount;            // Gold amount involved
        char description[32];         // Event description
        uint8_t reserved[8];          // Reserved for future use
        
        // Total size: 208 bytes
        static constexpr size_t Size() { return 208; }
        
        void Initialize() {
            std::memset(this, 0, sizeof(LogSheetEconomy));
        }
        
        void SetEvent(const char* type, const char* player, const char* guild,
                     double dalant, double gold, const char* desc) {
            if (type) strncpy_s(eventType, sizeof(eventType), type, _TRUNCATE);
            if (player) strncpy_s(playerName, sizeof(playerName), player, _TRUNCATE);
            if (guild) strncpy_s(guildName, sizeof(guildName), guild, _TRUNCATE);
            if (desc) strncpy_s(description, sizeof(description), desc, _TRUNCATE);
            dalantAmount = dalant;
            goldAmount = gold;
            timestamp = static_cast<uint32_t>(std::time(nullptr));
        }
    };

    /**
     * @brief Query case input guild money structure
     * 
     * Structure for guild money input operations.
     * Size: 64 bytes (as per original decompiled code)
     */
    struct QueryCaseInputGuildMoney {
        uint32_t guildId;             // Guild identifier
        char operatorName[32];        // Name of the operator
        uint32_t operatorSerial;      // Operator serial number
        double dalantAmount;          // Dalant amount to input
        double goldAmount;            // Gold amount to input
        uint32_t timestamp;           // Operation timestamp
        uint8_t reserved[8];          // Reserved for future use
        
        // Total size: 64 bytes
        static constexpr size_t Size() { return 64; }
        
        void Initialize() {
            std::memset(this, 0, sizeof(QueryCaseInputGuildMoney));
        }
        
        void SetData(uint32_t guild, const char* opName, uint32_t opSerial,
                    double dalant, double gold) {
            guildId = guild;
            if (opName) strncpy_s(operatorName, sizeof(operatorName), opName, _TRUNCATE);
            operatorSerial = opSerial;
            dalantAmount = dalant;
            goldAmount = gold;
            timestamp = static_cast<uint32_t>(std::time(nullptr));
        }
    };

    /**
     * @brief Query case output guild money structure
     * 
     * Structure for guild money output operations.
     * Size: 72 bytes (as per original decompiled code)
     */
    struct QueryCaseOutputGuildMoney {
        uint32_t guildId;             // Guild identifier
        char operatorName[32];        // Name of the operator
        uint32_t operatorSerial;      // Operator serial number
        double dalantAmount;          // Dalant amount to output
        double goldAmount;            // Gold amount to output
        uint32_t timestamp;           // Operation timestamp
        uint8_t operationType;        // Type of operation
        uint8_t reserved[15];         // Reserved for future use
        
        // Total size: 72 bytes
        static constexpr size_t Size() { return 72; }
        
        void Initialize() {
            std::memset(this, 0, sizeof(QueryCaseOutputGuildMoney));
        }
        
        void SetData(uint32_t guild, const char* opName, uint32_t opSerial,
                    double dalant, double gold, uint8_t opType) {
            guildId = guild;
            if (opName) strncpy_s(operatorName, sizeof(operatorName), opName, _TRUNCATE);
            operatorSerial = opSerial;
            dalantAmount = dalant;
            goldAmount = gold;
            operationType = opType;
            timestamp = static_cast<uint32_t>(std::time(nullptr));
        }
    };

    /**
     * @brief Guild money I/O download structure
     * 
     * Structure for downloading guild money I/O data.
     * Size determined from original decompiled code
     */
    struct GuildMoneyIODownload {
        uint32_t guildId;             // Guild identifier
        char guildName[64];           // Guild name
        double totalDalant;           // Total dalant in guild
        double totalGold;             // Total gold in guild
        uint32_t lastUpdateTime;      // Last update timestamp
        uint32_t memberCount;         // Number of guild members
        uint8_t downloadStatus;       // Download status
        uint8_t reserved[7];          // Reserved for future use
        
        static constexpr size_t Size() { return 96; }
        
        void Initialize() {
            std::memset(this, 0, sizeof(GuildMoneyIODownload));
        }
        
        void SetData(uint32_t guild, const char* name, double dalant, double gold, uint32_t members) {
            guildId = guild;
            if (name) strncpy_s(guildName, sizeof(guildName), name, _TRUNCATE);
            totalDalant = dalant;
            totalGold = gold;
            memberCount = members;
            lastUpdateTime = static_cast<uint32_t>(std::time(nullptr));
            downloadStatus = 1; // Downloaded
        }
    };

    /**
     * @brief Economy history data structure
     * 
     * Structure for tracking economy history over time.
     */
    struct EconomyHistoryData {
        std::array<double, 3> oldDalant{};      // Previous dalant values by race
        std::array<double, 3> currentDalant{};  // Current dalant values by race
        std::array<double, 3> bufferTradeDalant{}; // Buffer trade dalant values
        
        static constexpr size_t Size() { return sizeof(EconomyHistoryData); }
        
        void Initialize() {
            oldDalant.fill(0.0);
            currentDalant.fill(0.0);
            bufferTradeDalant.fill(0.0);
        }
        
        void UpdateDalant(size_t raceIndex, double newValue) {
            if (raceIndex < 3) {
                oldDalant[raceIndex] = currentDalant[raceIndex];
                currentDalant[raceIndex] = newValue;
            }
        }
    };

    /**
     * @brief Economy calculation data structure
     * 
     * Structure for economy calculations and trade data.
     */
    struct EconomyCalculationData {
        std::array<double, 3> tradeDalant{};    // Trade dalant by race
        std::array<double, 3> tradeGold{};      // Trade gold by race
        std::array<std::array<double, 3>, 3> oreMineCount{}; // Ore mine count by race
        std::array<std::array<double, 3>, 3> oreCutCount{};  // Ore cut count by race
        
        static constexpr size_t Size() { return sizeof(EconomyCalculationData); }
        
        void Initialize() {
            tradeDalant.fill(1.0);
            tradeGold.fill(1.0);
            
            for (auto& raceArray : oreMineCount) {
                raceArray.fill(1.0);
            }
            
            for (auto& raceArray : oreCutCount) {
                raceArray.fill(1.0);
            }
        }
    };

    // Legacy C interface compatibility structures
    extern "C" {
        struct _log_sheet_economy {
            char data[208];
            
            static signed __int64 size() { return 208; }
        };

        struct _qry_case_inputgmoney {
            char data[64];
            
            static signed __int64 size() { return 64; }
        };

        struct _qry_case_outputgmoney {
            char data[72];
            
            static signed __int64 size() { return 72; }
        };

        struct _guild_money_io_download_zocl {
            char data[96];
            
            static signed __int64 size() { return 96; }
        };

        struct _economy_history_data {
            double dOldDalant[3];
            double dCurDalant[3];
            double dBufTradeDalant[3];
        };

        struct _economy_calc_data {
            double dTradeDalant[3];
            double dTradeGold[3];
            double dOreMineCount[3][3];
            double dOreCutCount[3][3];
        };

        // Legacy function declarations
        signed __int64 _log_sheet_economy_size(_log_sheet_economy* obj);
        signed __int64 _qry_case_inputgmoney_size(_qry_case_inputgmoney* obj);
        signed __int64 _qry_case_outputgmoney_size(_qry_case_outputgmoney* obj);
        signed __int64 _guild_money_io_download_zocl_size(_guild_money_io_download_zocl* obj);
        void _economy_history_data_constructor(_economy_history_data* obj);
    }

    // Conversion functions between modern and legacy structures
    void ConvertToLegacy(const LogSheetEconomy& modern, _log_sheet_economy& legacy);
    void ConvertFromLegacy(const _log_sheet_economy& legacy, LogSheetEconomy& modern);
    
    void ConvertToLegacy(const QueryCaseInputGuildMoney& modern, _qry_case_inputgmoney& legacy);
    void ConvertFromLegacy(const _qry_case_inputgmoney& legacy, QueryCaseInputGuildMoney& modern);
    
    void ConvertToLegacy(const QueryCaseOutputGuildMoney& modern, _qry_case_outputgmoney& legacy);
    void ConvertFromLegacy(const _qry_case_outputgmoney& legacy, QueryCaseOutputGuildMoney& modern);
    
    void ConvertToLegacy(const EconomyHistoryData& modern, _economy_history_data& legacy);
    void ConvertFromLegacy(const _economy_history_data& legacy, EconomyHistoryData& modern);
    
    void ConvertToLegacy(const EconomyCalculationData& modern, _economy_calc_data& legacy);
    void ConvertFromLegacy(const _economy_calc_data& legacy, EconomyCalculationData& modern);

} // namespace NexusProtection::Economy
