#include "../Headers/EventRespawn.h"
#include <algorithm>
#include <sstream>
#include <cstring>
#include <stdexcept>
#include <fstream>

namespace NexusProtection::World {

    // EventRespawn::State implementation

    EventRespawn::State::State() {
        Initialize();
    }

    EventRespawn::State::State(const State& other)
        : m_isActive(other.m_isActive)
        , m_isPending(other.m_isPending)
        , m_isCompleted(other.m_isCompleted)
        , m_isPaused(other.m_isPaused)
        , m_startTime(other.m_startTime)
        , m_duration(other.m_duration)
        , m_eventId(other.m_eventId)
        , m_eventType(other.m_eventType)
        , m_progress(other.m_progress)
        , m_internalState(other.m_internalState) {
    }

    EventRespawn::State& EventRespawn::State::operator=(const State& other) {
        if (this != &other) {
            m_isActive = other.m_isActive;
            m_isPending = other.m_isPending;
            m_isCompleted = other.m_isCompleted;
            m_isPaused = other.m_isPaused;
            m_startTime = other.m_startTime;
            m_duration = other.m_duration;
            m_eventId = other.m_eventId;
            m_eventType = other.m_eventType;
            m_progress = other.m_progress;
            m_internalState = other.m_internalState;
        }
        return *this;
    }

    EventRespawn::State::State(State&& other) noexcept
        : m_isActive(other.m_isActive)
        , m_isPending(other.m_isPending)
        , m_isCompleted(other.m_isCompleted)
        , m_isPaused(other.m_isPaused)
        , m_startTime(other.m_startTime)
        , m_duration(other.m_duration)
        , m_eventId(other.m_eventId)
        , m_eventType(std::move(other.m_eventType))
        , m_progress(other.m_progress)
        , m_internalState(other.m_internalState) {
        other.m_isActive = false;
        other.m_isPending = false;
        other.m_isCompleted = false;
        other.m_isPaused = false;
        other.m_eventId = 0;
        other.m_progress = 0.0f;
    }

    EventRespawn::State& EventRespawn::State::operator=(State&& other) noexcept {
        if (this != &other) {
            m_isActive = other.m_isActive;
            m_isPending = other.m_isPending;
            m_isCompleted = other.m_isCompleted;
            m_isPaused = other.m_isPaused;
            m_startTime = other.m_startTime;
            m_duration = other.m_duration;
            m_eventId = other.m_eventId;
            m_eventType = std::move(other.m_eventType);
            m_progress = other.m_progress;
            m_internalState = other.m_internalState;
            
            other.m_isActive = false;
            other.m_isPending = false;
            other.m_isCompleted = false;
            other.m_isPaused = false;
            other.m_eventId = 0;
            other.m_progress = 0.0f;
        }
        return *this;
    }

    void EventRespawn::State::Initialize() {
        m_isActive = false;
        m_isPending = false;
        m_isCompleted = false;
        m_isPaused = false;
        m_startTime = std::chrono::steady_clock::time_point{};
        m_duration = std::chrono::milliseconds{0};
        m_eventId = 0;
        m_eventType.clear();
        m_progress = 0.0f;
        InitializeInternalState();
    }

    void EventRespawn::State::Reset() {
        Initialize();
    }

    bool EventRespawn::State::IsValid() const {
        return m_eventId > 0 || !m_eventType.empty();
    }

    std::chrono::milliseconds EventRespawn::State::GetElapsedTime() const {
        if (m_startTime == std::chrono::steady_clock::time_point{}) {
            return std::chrono::milliseconds{0};
        }
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
    }

    std::chrono::milliseconds EventRespawn::State::GetRemainingTime() const {
        auto elapsed = GetElapsedTime();
        if (elapsed >= m_duration) {
            return std::chrono::milliseconds{0};
        }
        return m_duration - elapsed;
    }

    void EventRespawn::State::StartEvent() {
        m_isActive = true;
        m_isPending = false;
        m_isCompleted = false;
        m_isPaused = false;
        m_startTime = std::chrono::steady_clock::now();
        m_progress = 0.0f;
        UpdateInternalState();
    }

    void EventRespawn::State::PauseEvent() {
        if (m_isActive && !m_isCompleted) {
            m_isPaused = true;
            UpdateInternalState();
        }
    }

    void EventRespawn::State::ResumeEvent() {
        if (m_isActive && m_isPaused && !m_isCompleted) {
            m_isPaused = false;
            UpdateInternalState();
        }
    }

    void EventRespawn::State::CompleteEvent() {
        m_isCompleted = true;
        m_isActive = false;
        m_isPending = false;
        m_isPaused = false;
        m_progress = 1.0f;
        UpdateInternalState();
    }

    void EventRespawn::State::CancelEvent() {
        m_isActive = false;
        m_isPending = false;
        m_isCompleted = false;
        m_isPaused = false;
        m_progress = 0.0f;
        UpdateInternalState();
    }

    std::string EventRespawn::State::ToString() const {
        std::ostringstream oss;
        oss << "EventRespawn::State{";
        oss << "EventId: " << m_eventId << ", ";
        oss << "EventType: \"" << m_eventType << "\", ";
        oss << "Active: " << (m_isActive ? "true" : "false") << ", ";
        oss << "Pending: " << (m_isPending ? "true" : "false") << ", ";
        oss << "Completed: " << (m_isCompleted ? "true" : "false") << ", ";
        oss << "Paused: " << (m_isPaused ? "true" : "false") << ", ";
        oss << "Progress: " << (m_progress * 100.0f) << "%";
        oss << "}";
        return oss.str();
    }

    size_t EventRespawn::State::GetMemoryUsage() const {
        return sizeof(State) + m_eventType.capacity();
    }

    void EventRespawn::State::InitializeInternalState() {
        // Initialize internal state array to match original behavior (-858993460 = 0xCCCCCCCC)
        m_internalState.fill(0xCCCCCCCC);
    }

    void EventRespawn::State::UpdateInternalState() {
        // Update internal state based on current state
        m_internalState[0] = m_eventId;
        m_internalState[1] = static_cast<uint32_t>(m_isActive) | 
                            (static_cast<uint32_t>(m_isPending) << 1) |
                            (static_cast<uint32_t>(m_isCompleted) << 2) |
                            (static_cast<uint32_t>(m_isPaused) << 3);
        m_internalState[2] = static_cast<uint32_t>(m_duration.count());
        m_internalState[3] = static_cast<uint32_t>(m_progress * 1000.0f); // Store as fixed point
        // Remaining slots for future use
    }

    // EventRespawn implementation

    EventRespawn::EventRespawn() {
        Initialize();
    }

    EventRespawn::EventRespawn(const EventRespawn& other)
        : m_state(other.m_state)
        , m_isLoaded(other.m_isLoaded)
        , m_isActive(other.m_isActive)
        , m_useRewardItemNum(other.m_useRewardItemNum)
        , m_eventName(other.m_eventName)
        , m_eventDescription(other.m_eventDescription)
        , m_triggerCondition(other.m_triggerCondition)
        , m_completionCondition(other.m_completionCondition)
        , m_cooldownDuration(other.m_cooldownDuration)
        , m_rewardItems(other.m_rewardItems)
        , m_participants(other.m_participants)
        , m_eventHandlers(other.m_eventHandlers) {
    }

    EventRespawn& EventRespawn::operator=(const EventRespawn& other) {
        if (this != &other) {
            m_state = other.m_state;
            m_isLoaded = other.m_isLoaded;
            m_isActive = other.m_isActive;
            m_useRewardItemNum = other.m_useRewardItemNum;
            m_eventName = other.m_eventName;
            m_eventDescription = other.m_eventDescription;
            m_triggerCondition = other.m_triggerCondition;
            m_completionCondition = other.m_completionCondition;
            m_cooldownDuration = other.m_cooldownDuration;
            m_rewardItems = other.m_rewardItems;
            m_participants = other.m_participants;
            m_eventHandlers = other.m_eventHandlers;
        }
        return *this;
    }

    EventRespawn::EventRespawn(EventRespawn&& other) noexcept
        : m_state(std::move(other.m_state))
        , m_isLoaded(other.m_isLoaded)
        , m_isActive(other.m_isActive)
        , m_useRewardItemNum(other.m_useRewardItemNum)
        , m_eventName(std::move(other.m_eventName))
        , m_eventDescription(std::move(other.m_eventDescription))
        , m_triggerCondition(std::move(other.m_triggerCondition))
        , m_completionCondition(std::move(other.m_completionCondition))
        , m_cooldownDuration(other.m_cooldownDuration)
        , m_rewardItems(std::move(other.m_rewardItems))
        , m_participants(std::move(other.m_participants))
        , m_eventHandlers(std::move(other.m_eventHandlers)) {
        other.m_isLoaded = false;
        other.m_isActive = false;
        other.m_useRewardItemNum = 0;
        other.m_cooldownDuration = std::chrono::milliseconds{0};
    }

    EventRespawn& EventRespawn::operator=(EventRespawn&& other) noexcept {
        if (this != &other) {
            m_state = std::move(other.m_state);
            m_isLoaded = other.m_isLoaded;
            m_isActive = other.m_isActive;
            m_useRewardItemNum = other.m_useRewardItemNum;
            m_eventName = std::move(other.m_eventName);
            m_eventDescription = std::move(other.m_eventDescription);
            m_triggerCondition = std::move(other.m_triggerCondition);
            m_completionCondition = std::move(other.m_completionCondition);
            m_cooldownDuration = other.m_cooldownDuration;
            m_rewardItems = std::move(other.m_rewardItems);
            m_participants = std::move(other.m_participants);
            m_eventHandlers = std::move(other.m_eventHandlers);
            
            other.m_isLoaded = false;
            other.m_isActive = false;
            other.m_useRewardItemNum = 0;
            other.m_cooldownDuration = std::chrono::milliseconds{0};
        }
        return *this;
    }

    void EventRespawn::Initialize() {
        InitializeComponents();
        m_isLoaded = false;
        m_isActive = false;
        m_useRewardItemNum = 0;
        m_eventName.clear();
        m_eventDescription.clear();
        m_triggerCondition.clear();
        m_completionCondition.clear();
        m_cooldownDuration = std::chrono::milliseconds{0};
        m_rewardItems.clear();
        m_participants.clear();
        m_eventHandlers.clear();
    }

    void EventRespawn::Reset() {
        ResetComponents();
        Initialize();
    }

    bool EventRespawn::IsValid() const {
        return !m_eventName.empty() && m_state.IsValid();
    }

    void EventRespawn::SetActive(bool active) {
        m_isActive = active;
        if (active) {
            m_state.SetActive(true);
        } else {
            m_state.SetActive(false);
        }
        NotifyEventStateChange();
    }

    void EventRespawn::ActivateEvent() {
        SetActive(true);
        TriggerEvent("event_activated");
    }

    void EventRespawn::DeactivateEvent() {
        SetActive(false);
        TriggerEvent("event_deactivated");
    }

    bool EventRespawn::LoadEventConfiguration(const std::string& configPath) {
        try {
            std::ifstream file(configPath);
            if (!file.is_open()) {
                return false;
            }

            // Simple configuration loading - in production, use proper JSON/XML parser
            std::string line;
            while (std::getline(file, line)) {
                // Parse configuration lines
                // This is a simplified implementation
            }

            m_isLoaded = true;
            TriggerEvent("config_loaded");
            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    bool EventRespawn::SaveEventConfiguration(const std::string& configPath) const {
        try {
            std::ofstream file(configPath);
            if (!file.is_open()) {
                return false;
            }

            // Simple configuration saving - in production, use proper JSON/XML serialization
            file << "EventName=" << m_eventName << std::endl;
            file << "EventDescription=" << m_eventDescription << std::endl;
            file << "TriggerCondition=" << m_triggerCondition << std::endl;
            file << "CompletionCondition=" << m_completionCondition << std::endl;
            file << "UseRewardItemNum=" << m_useRewardItemNum << std::endl;

            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    void EventRespawn::StartEventRespawn() {
        if (!IsValid()) {
            return;
        }

        m_state.StartEvent();
        SetActive(true);
        TriggerEvent("event_respawn_started");
    }

    void EventRespawn::StopEventRespawn() {
        m_state.CompleteEvent();
        SetActive(false);
        TriggerEvent("event_respawn_stopped");
    }

    void EventRespawn::PauseEventRespawn() {
        m_state.PauseEvent();
        TriggerEvent("event_respawn_paused");
    }

    void EventRespawn::ResumeEventRespawn() {
        m_state.ResumeEvent();
        TriggerEvent("event_respawn_resumed");
    }

    void EventRespawn::ProcessEventRespawn(float deltaTime) {
        if (!IsValid() || !m_isActive) {
            return;
        }

        ProcessEventLogic(deltaTime);

        // Check if event should complete
        if (m_state.GetRemainingTime() <= std::chrono::milliseconds{0}) {
            StopEventRespawn();
        }
    }

    void EventRespawn::AddRewardItem(uint32_t itemId, uint32_t quantity) {
        if (m_rewardItems.size() < MAX_REWARD_ITEMS) {
            m_rewardItems[itemId] = quantity;
        }
    }

    void EventRespawn::RemoveRewardItem(uint32_t itemId) {
        m_rewardItems.erase(itemId);
    }

    void EventRespawn::ClearRewardItems() {
        m_rewardItems.clear();
        m_useRewardItemNum = 0;
    }

    void EventRespawn::SetEventDuration(std::chrono::milliseconds duration) {
        auto clampedDuration = std::min(duration, MAX_EVENT_DURATION);
        m_state.SetDuration(clampedDuration);
    }

    std::chrono::milliseconds EventRespawn::GetEventDuration() const {
        return m_state.GetDuration();
    }

    void EventRespawn::AddParticipant(uint32_t playerId) {
        if (m_participants.size() < MAX_PARTICIPANTS) {
            auto it = std::find(m_participants.begin(), m_participants.end(), playerId);
            if (it == m_participants.end()) {
                m_participants.push_back(playerId);
                TriggerEvent("participant_added");
            }
        }
    }

    void EventRespawn::RemoveParticipant(uint32_t playerId) {
        auto it = std::find(m_participants.begin(), m_participants.end(), playerId);
        if (it != m_participants.end()) {
            m_participants.erase(it);
            TriggerEvent("participant_removed");
        }
    }

    void EventRespawn::ClearParticipants() {
        m_participants.clear();
        TriggerEvent("participants_cleared");
    }

    void EventRespawn::RegisterEventHandler(const std::string& event, std::function<void(const EventRespawn&)> handler) {
        m_eventHandlers[event] = std::move(handler);
    }

    void EventRespawn::TriggerEvent(const std::string& event) {
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end() && it->second) {
            it->second(*this);
        }
    }

    bool EventRespawn::ValidateEventConfiguration() const {
        return IsValidString(m_eventName, MAX_EVENT_NAME_LENGTH) &&
               IsValidString(m_eventDescription, MAX_EVENT_DESCRIPTION_LENGTH) &&
               IsValidString(m_triggerCondition, MAX_CONDITION_LENGTH) &&
               IsValidString(m_completionCondition, MAX_CONDITION_LENGTH);
    }

    bool EventRespawn::ValidateRewardConfiguration() const {
        return m_rewardItems.size() <= MAX_REWARD_ITEMS &&
               m_useRewardItemNum <= static_cast<uint32_t>(m_rewardItems.size());
    }

    bool EventRespawn::ValidateStateConfiguration() const {
        return m_state.IsValid() && ValidateEventConfiguration() && ValidateRewardConfiguration();
    }

    std::string EventRespawn::ToString() const {
        std::ostringstream oss;
        oss << "EventRespawn{";
        oss << "EventName: \"" << m_eventName << "\", ";
        oss << "Loaded: " << (m_isLoaded ? "true" : "false") << ", ";
        oss << "Active: " << (m_isActive ? "true" : "false") << ", ";
        oss << "RewardItemNum: " << m_useRewardItemNum << ", ";
        oss << "Participants: " << m_participants.size() << ", ";
        oss << "State: " << m_state.ToString();
        oss << "}";
        return oss.str();
    }

    size_t EventRespawn::GetMemoryUsage() const {
        return sizeof(EventRespawn) +
               m_eventName.capacity() +
               m_eventDescription.capacity() +
               m_triggerCondition.capacity() +
               m_completionCondition.capacity() +
               (m_rewardItems.size() * (sizeof(uint32_t) * 2)) +
               (m_participants.size() * sizeof(uint32_t)) +
               (m_eventHandlers.size() * (sizeof(std::string) + sizeof(std::function<void(const EventRespawn&)>))) +
               m_state.GetMemoryUsage();
    }

    void EventRespawn::InitializeComponents() {
        m_state.Initialize();
    }

    void EventRespawn::ResetComponents() {
        m_state.Reset();
    }

    void EventRespawn::ValidateAndTruncate() {
        if (m_eventName.length() > MAX_EVENT_NAME_LENGTH) {
            m_eventName.resize(MAX_EVENT_NAME_LENGTH);
        }
        if (m_eventDescription.length() > MAX_EVENT_DESCRIPTION_LENGTH) {
            m_eventDescription.resize(MAX_EVENT_DESCRIPTION_LENGTH);
        }
        if (m_triggerCondition.length() > MAX_CONDITION_LENGTH) {
            m_triggerCondition.resize(MAX_CONDITION_LENGTH);
        }
        if (m_completionCondition.length() > MAX_CONDITION_LENGTH) {
            m_completionCondition.resize(MAX_CONDITION_LENGTH);
        }

        // Limit collections
        if (m_rewardItems.size() > MAX_REWARD_ITEMS) {
            auto it = m_rewardItems.begin();
            std::advance(it, MAX_REWARD_ITEMS);
            m_rewardItems.erase(it, m_rewardItems.end());
        }

        if (m_participants.size() > MAX_PARTICIPANTS) {
            m_participants.resize(MAX_PARTICIPANTS);
        }
    }

    bool EventRespawn::IsValidString(const std::string& str, size_t maxLength) const {
        return str.length() <= maxLength;
    }

    void EventRespawn::NotifyEventStateChange() {
        TriggerEvent("state_changed");
    }

    void EventRespawn::ProcessEventLogic(float deltaTime) {
        if (!m_isActive || !m_state.IsActive()) {
            return;
        }

        // Update participants
        UpdateParticipants();

        // Check completion conditions
        // This would contain game-specific logic

        // Update progress based on time
        auto elapsed = m_state.GetElapsedTime();
        auto duration = m_state.GetDuration();
        if (duration.count() > 0) {
            float progress = static_cast<float>(elapsed.count()) / static_cast<float>(duration.count());
            m_state.SetProgress(std::clamp(progress, 0.0f, 1.0f));
        }
    }

    void EventRespawn::UpdateParticipants() {
        // Update participant states
        // This would contain game-specific logic for participant management
    }

    void EventRespawn::DistributeRewards() {
        if (m_rewardItems.empty() || m_participants.empty()) {
            return;
        }

        // Distribute rewards to participants
        // This would contain game-specific logic for reward distribution

        TriggerEvent("rewards_distributed");
    }

    // EventRespawnFactory implementation

    std::unique_ptr<EventRespawn> EventRespawnFactory::CreateEventRespawn() {
        return std::make_unique<EventRespawn>();
    }

    std::unique_ptr<EventRespawn> EventRespawnFactory::CreateEventRespawn(const std::string& eventName) {
        auto eventRespawn = std::make_unique<EventRespawn>();
        eventRespawn->SetEventName(eventName);
        return eventRespawn;
    }

    std::unique_ptr<EventRespawn> EventRespawnFactory::CreateEventRespawn(const std::string& eventName,
                                                                         std::chrono::milliseconds duration) {
        auto eventRespawn = std::make_unique<EventRespawn>();
        eventRespawn->SetEventName(eventName);
        eventRespawn->SetEventDuration(duration);
        return eventRespawn;
    }

    std::unique_ptr<EventRespawn> EventRespawnFactory::CreateTimedEvent(const std::string& eventName,
                                                                       std::chrono::milliseconds duration) {
        auto eventRespawn = CreateEventRespawn(eventName, duration);
        eventRespawn->SetTriggerCondition("time_based");
        eventRespawn->SetCompletionCondition("duration_elapsed");
        return eventRespawn;
    }

    std::unique_ptr<EventRespawn> EventRespawnFactory::CreateConditionalEvent(const std::string& eventName,
                                                                             const std::string& condition) {
        auto eventRespawn = CreateEventRespawn(eventName);
        eventRespawn->SetTriggerCondition(condition);
        eventRespawn->SetCompletionCondition("condition_met");
        return eventRespawn;
    }

    std::unique_ptr<EventRespawn> EventRespawnFactory::CreateRewardEvent(const std::string& eventName,
                                                                        const std::unordered_map<uint32_t, uint32_t>& rewards) {
        auto eventRespawn = CreateEventRespawn(eventName);
        for (const auto& reward : rewards) {
            eventRespawn->AddRewardItem(reward.first, reward.second);
        }
        eventRespawn->SetUseRewardItemNum(static_cast<uint32_t>(rewards.size()));
        return eventRespawn;
    }

    std::vector<std::unique_ptr<EventRespawn>> EventRespawnFactory::CreateEventRespawns(
        const std::vector<std::string>& eventNames) {
        std::vector<std::unique_ptr<EventRespawn>> result;
        result.reserve(eventNames.size());

        for (const auto& eventName : eventNames) {
            result.push_back(CreateEventRespawn(eventName));
        }

        return result;
    }

    // EventRespawnManager implementation

    EventRespawnManager::EventRespawnManager() {
        m_eventRespawns.reserve(50); // Reserve space for typical event count
    }

    void EventRespawnManager::AddEventRespawn(std::unique_ptr<EventRespawn> eventRespawn) {
        if (eventRespawn && !eventRespawn->GetEventName().empty()) {
            std::string eventName = eventRespawn->GetEventName();
            m_eventNameToIndex[eventName] = m_eventRespawns.size();
            m_eventRespawns.push_back(std::move(eventRespawn));
        }
    }

    void EventRespawnManager::RemoveEventRespawn(const std::string& eventName) {
        auto it = m_eventNameToIndex.find(eventName);
        if (it != m_eventNameToIndex.end()) {
            size_t index = it->second;
            if (index < m_eventRespawns.size()) {
                m_eventRespawns.erase(m_eventRespawns.begin() + index);
                m_eventNameToIndex.erase(it);

                // Update indices for remaining events
                for (auto& pair : m_eventNameToIndex) {
                    if (pair.second > index) {
                        --pair.second;
                    }
                }
            }
        }
    }

    std::shared_ptr<EventRespawn> EventRespawnManager::GetEventRespawn(const std::string& eventName) const {
        auto it = m_eventNameToIndex.find(eventName);
        if (it != m_eventNameToIndex.end() && it->second < m_eventRespawns.size()) {
            return m_eventRespawns[it->second];
        }
        return nullptr;
    }

    void EventRespawnManager::StartAllEvents() {
        for (auto& event : m_eventRespawns) {
            if (event) {
                event->StartEventRespawn();
            }
        }
    }

    void EventRespawnManager::StopAllEvents() {
        for (auto& event : m_eventRespawns) {
            if (event) {
                event->StopEventRespawn();
            }
        }
    }

    void EventRespawnManager::UpdateAllEvents(float deltaTime) {
        for (auto& event : m_eventRespawns) {
            if (event && event->IsActive()) {
                event->ProcessEventRespawn(deltaTime);
            }
        }
    }

    void EventRespawnManager::ProcessAllEventRespawns() {
        for (auto& event : m_eventRespawns) {
            if (event && event->IsActive()) {
                // Process any pending event operations
                if (event->GetState().IsPending()) {
                    // Trigger event processing logic
                }
            }
        }
    }

    size_t EventRespawnManager::GetActiveEventCount() const {
        return std::count_if(m_eventRespawns.begin(), m_eventRespawns.end(),
            [](const std::shared_ptr<EventRespawn>& event) {
                return event && event->IsActive();
            });
    }

    size_t EventRespawnManager::GetLoadedEventCount() const {
        return std::count_if(m_eventRespawns.begin(), m_eventRespawns.end(),
            [](const std::shared_ptr<EventRespawn>& event) {
                return event && event->IsLoaded();
            });
    }

    void EventRespawnManager::SetGlobalEventDuration(std::chrono::milliseconds duration) {
        m_globalEventDuration = std::max(std::chrono::milliseconds{1000}, duration); // Minimum 1 second

        // Apply to all events that don't have a custom duration
        for (auto& event : m_eventRespawns) {
            if (event && event->GetEventDuration() == std::chrono::milliseconds{0}) {
                event->SetEventDuration(m_globalEventDuration);
            }
        }
    }

    // EventRespawnUtils implementation

    namespace EventRespawnUtils {

        bool ValidateEventRespawn(const EventRespawn& eventRespawn) {
            return eventRespawn.IsValid() && eventRespawn.ValidateStateConfiguration();
        }

        bool ValidateEventName(const std::string& name) {
            return name.length() <= EventRespawn::MAX_EVENT_NAME_LENGTH && !name.empty();
        }

        bool ValidateEventCondition(const std::string& condition) {
            return condition.length() <= EventRespawn::MAX_CONDITION_LENGTH;
        }

        std::string SanitizeEventName(const std::string& name) {
            std::string sanitized = name;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > EventRespawn::MAX_EVENT_NAME_LENGTH) {
                sanitized.resize(EventRespawn::MAX_EVENT_NAME_LENGTH);
            }
            return sanitized;
        }

        std::string SanitizeEventCondition(const std::string& condition) {
            std::string sanitized = condition;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > EventRespawn::MAX_CONDITION_LENGTH) {
                sanitized.resize(EventRespawn::MAX_CONDITION_LENGTH);
            }
            return sanitized;
        }

        std::string GenerateUniqueEventName(const std::string& baseName) {
            static uint32_t counter = 0;
            std::ostringstream oss;
            oss << baseName << "_" << ++counter;
            return oss.str();
        }

        size_t CalculateMemoryFootprint(const EventRespawn& eventRespawn) {
            return eventRespawn.GetMemoryUsage();
        }

        void ConfigureDefaultEvent(EventRespawn& eventRespawn) {
            if (eventRespawn.GetEventName().empty()) {
                eventRespawn.SetEventName("DefaultEvent");
            }
            if (eventRespawn.GetEventDuration() == std::chrono::milliseconds{0}) {
                eventRespawn.SetEventDuration(std::chrono::milliseconds{300000}); // 5 minutes default
            }
            eventRespawn.SetActive(true);
            eventRespawn.SetLoaded(true);
        }

        void OptimizeEventConfiguration(EventRespawn& eventRespawn) {
            // Optimization logic could be added here
            // For now, just ensure event has valid configuration
            if (!ValidateEventRespawn(eventRespawn)) {
                ConfigureDefaultEvent(eventRespawn);
            }
        }

        std::string EventRespawnToJson(const EventRespawn& eventRespawn) {
            std::ostringstream oss;
            oss << "{";
            oss << "\"eventName\": \"" << eventRespawn.GetEventName() << "\",";
            oss << "\"eventDescription\": \"" << eventRespawn.GetEventDescription() << "\",";
            oss << "\"loaded\": " << (eventRespawn.IsLoaded() ? "true" : "false") << ",";
            oss << "\"active\": " << (eventRespawn.IsActive() ? "true" : "false") << ",";
            oss << "\"useRewardItemNum\": " << eventRespawn.GetUseRewardItemNum() << ",";
            oss << "\"triggerCondition\": \"" << eventRespawn.GetTriggerCondition() << "\",";
            oss << "\"completionCondition\": \"" << eventRespawn.GetCompletionCondition() << "\",";
            oss << "\"participantCount\": " << eventRespawn.GetParticipantCount() << ",";
            oss << "\"eventDuration\": " << eventRespawn.GetEventDuration().count();
            oss << "}";
            return oss.str();
        }

        std::unique_ptr<EventRespawn> EventRespawnFromJson(const std::string& json) {
            // Simple JSON parsing - in a real implementation, use a proper JSON library
            auto eventRespawn = std::make_unique<EventRespawn>();

            // This is a simplified implementation
            // In production, use a proper JSON parser like nlohmann/json

            return eventRespawn;
        }

        std::vector<std::string> AnalyzeEventConfiguration(const EventRespawn& eventRespawn) {
            std::vector<std::string> analysis;

            analysis.push_back("Event Name: " + eventRespawn.GetEventName());
            analysis.push_back("Event Description: " + eventRespawn.GetEventDescription());
            analysis.push_back("Loaded: " + (eventRespawn.IsLoaded() ? std::string("Yes") : std::string("No")));
            analysis.push_back("Active: " + (eventRespawn.IsActive() ? std::string("Yes") : std::string("No")));
            analysis.push_back("Reward Item Count: " + std::to_string(eventRespawn.GetUseRewardItemNum()));
            analysis.push_back("Participant Count: " + std::to_string(eventRespawn.GetParticipantCount()));
            analysis.push_back("Event Duration: " + std::to_string(eventRespawn.GetEventDuration().count()) + " ms");
            analysis.push_back("Trigger Condition: " + eventRespawn.GetTriggerCondition());
            analysis.push_back("Completion Condition: " + eventRespawn.GetCompletionCondition());
            analysis.push_back("State: " + eventRespawn.GetState().ToString());

            return analysis;
        }

        float CalculateEventEfficiency(const EventRespawn& eventRespawn) {
            float efficiency = 0.0f;

            // Base efficiency from configuration completeness
            if (!eventRespawn.GetEventName().empty()) efficiency += 0.2f;
            if (!eventRespawn.GetEventDescription().empty()) efficiency += 0.1f;
            if (eventRespawn.IsLoaded()) efficiency += 0.2f;
            if (eventRespawn.IsActive()) efficiency += 0.2f;
            if (eventRespawn.GetUseRewardItemNum() > 0) efficiency += 0.1f;
            if (eventRespawn.GetParticipantCount() > 0) efficiency += 0.1f;
            if (eventRespawn.GetEventDuration().count() > 0) efficiency += 0.1f;

            return std::clamp(efficiency, 0.0f, 1.0f);
        }

        std::string GetEventStatusSummary(const EventRespawn& eventRespawn) {
            std::ostringstream oss;
            oss << "Event Status: ";

            if (!eventRespawn.IsValid()) {
                oss << "Invalid Configuration";
            } else if (!eventRespawn.IsLoaded()) {
                oss << "Not Loaded";
            } else if (eventRespawn.GetState().IsCompleted()) {
                oss << "Completed";
            } else if (eventRespawn.GetState().IsPaused()) {
                oss << "Paused";
            } else if (eventRespawn.IsActive()) {
                oss << "Active and Running";
            } else {
                oss << "Inactive";
            }

            oss << " (Efficiency: " << (CalculateEventEfficiency(eventRespawn) * 100.0f) << "%)";

            return oss.str();
        }

    } // namespace EventRespawnUtils

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {

    void _event_respawn_Constructor(NexusProtection::World::_event_respawn* this_ptr) {
        if (this_ptr) {
            // Initialize state structure
            _event_respawn_state_Constructor(&this_ptr->State);

            // Initialize flags and values to match original constructor behavior
            this_ptr->bLoad = false;
            this_ptr->bActive = false;
            this_ptr->nUseRewardItemNum = 0;

            // Clear padding
            std::memset(this_ptr->padding, 0, sizeof(this_ptr->padding));
        }
    }

    void _event_respawn_Destructor(NexusProtection::World::_event_respawn* this_ptr) {
        if (this_ptr) {
            // Clean up state structure
            _event_respawn_state_Destructor(&this_ptr->State);

            // Reset values
            this_ptr->bLoad = false;
            this_ptr->bActive = false;
            this_ptr->nUseRewardItemNum = 0;
        }
    }

    void _event_respawn_state_Constructor(NexusProtection::World::_event_respawn::_state* this_ptr) {
        if (this_ptr) {
            // Initialize internal state array to match original behavior (-858993460 = 0xCCCCCCCC)
            for (int i = 0; i < 8; ++i) {
                this_ptr->internalState[i] = 0xCCCCCCCC;
            }

            // Clear padding
            std::memset(this_ptr->padding, 0, sizeof(this_ptr->padding));
        }
    }

    void _event_respawn_state_Destructor(NexusProtection::World::_event_respawn::_state* this_ptr) {
        if (this_ptr) {
            // Reset internal state
            for (int i = 0; i < 8; ++i) {
                this_ptr->internalState[i] = 0;
            }
        }
    }

    void _event_respawn_SetLoad(NexusProtection::World::_event_respawn* this_ptr, bool load) {
        if (this_ptr) {
            this_ptr->bLoad = load;
        }
    }

    void _event_respawn_SetActive(NexusProtection::World::_event_respawn* this_ptr, bool active) {
        if (this_ptr) {
            this_ptr->bActive = active;
        }
    }

    void _event_respawn_SetUseRewardItemNum(NexusProtection::World::_event_respawn* this_ptr, uint32_t num) {
        if (this_ptr) {
            this_ptr->nUseRewardItemNum = num;
        }
    }

    bool _event_respawn_GetLoad(NexusProtection::World::_event_respawn* this_ptr) {
        return this_ptr ? this_ptr->bLoad : false;
    }

    bool _event_respawn_GetActive(NexusProtection::World::_event_respawn* this_ptr) {
        return this_ptr ? this_ptr->bActive : false;
    }

    uint32_t _event_respawn_GetUseRewardItemNum(NexusProtection::World::_event_respawn* this_ptr) {
        return this_ptr ? this_ptr->nUseRewardItemNum : 0;
    }

    void* _event_respawn_GetState(NexusProtection::World::_event_respawn* this_ptr) {
        return this_ptr ? &this_ptr->State : nullptr;
    }

    void _event_respawn_Initialize(NexusProtection::World::_event_respawn* this_ptr) {
        if (this_ptr) {
            _event_respawn_Constructor(this_ptr);
        }
    }

    void _event_respawn_Reset(NexusProtection::World::_event_respawn* this_ptr) {
        if (this_ptr) {
            _event_respawn_Destructor(this_ptr);
            _event_respawn_Constructor(this_ptr);
        }
    }

    void _event_respawn_StartEvent(NexusProtection::World::_event_respawn* this_ptr) {
        if (this_ptr) {
            this_ptr->bActive = true;
            // Update state to reflect active event
            this_ptr->State.internalState[1] |= 0x01; // Set active bit
        }
    }

    void _event_respawn_StopEvent(NexusProtection::World::_event_respawn* this_ptr) {
        if (this_ptr) {
            this_ptr->bActive = false;
            // Update state to reflect inactive event
            this_ptr->State.internalState[1] &= ~0x01; // Clear active bit
        }
    }

} // extern "C"

// Global legacy compatibility
NexusProtection::World::EventRespawn* g_pEventRespawn = nullptr;
