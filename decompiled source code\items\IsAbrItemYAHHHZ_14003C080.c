/*
 * Function: ?IsAbrItem@@YAHHH@Z
 * Address: 0x14003C080
 */

signed __int64 __fastcall IsAbrItem(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CRecordData *v5; // [sp+0h] [bp-18h]@1
  int v6; // [sp+8h] [bp-10h]@4
  int v7; // [sp+20h] [bp+8h]@1

  v7 = nTableCode;
  v2 = (__int64 *)&v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = &s_ptblItemData[v7];
  v6 = v7 - 6;
  switch ( v7 )
  {
    case 6:
      result = 0i64;
      break;
    case 11:
      result = 1i64;
      break;
    case 19:
      result = 1i64;
      break;
    case 10:
      result = 1i64;
      break;
    case 16:
      result = 1i64;
      break;
    case 25:
      result = 1i64;
      break;
    case 27:
      result = 1i64;
      break;
    default:
      result = 0i64;
      break;
  }
  return result;
}
