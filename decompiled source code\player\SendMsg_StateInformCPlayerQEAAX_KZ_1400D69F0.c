/*
 * Function: ?SendMsg_StateInform@CPlayer@@QEAAX_K@Z
 * Address: 0x1400D69F0
 */

void __fastcall CPlayer::SendMsg_StateInform(CPlayer *this, unsigned __int64 dwStateFlag)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  unsigned __int64 v6; // [sp+3Ch] [bp-4Ch]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v8; // [sp+65h] [bp-23h]@4
  CPlayer *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = v9->m_dwObjSerial;
  v6 = dwStateFlag;
  pbyType = 4;
  v8 = 25;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 12, 1);
}
