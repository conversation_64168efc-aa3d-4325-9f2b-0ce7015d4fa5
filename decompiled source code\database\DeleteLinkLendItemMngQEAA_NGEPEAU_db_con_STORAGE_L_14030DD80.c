/*
 * Function: ?DeleteLink@LendItemMng@@QEAA_NGEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14030DD80
 */

char __fastcall LendItemMng::DeleteLink(LendItemMng *this, unsigned __int16 wIdx, char byStorageCode, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _TimeItem_fld *v8; // [sp+20h] [bp-18h]@6
  CLogFile *v9; // [sp+28h] [bp-10h]@5
  LendItemMng *v10; // [sp+40h] [bp+8h]@1
  unsigned __int16 v11; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1
  _STORAGE_LIST::_db_con *pItem; // [sp+58h] [bp+20h]@1

  pItem = pkItem;
  v12 = byStorageCode;
  v11 = wIdx;
  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)wIdx < 2532 )
  {
    v8 = TimeItem::FindTimeRec(pkItem->m_byTableCode, pkItem->m_wItemIndex);
    if ( v8 )
    {
      if ( v8->m_nCheckType )
      {
        if ( LendItemSheet::DeleteLink(v10->_ppkLendItem[v11], v12, pItem) )
        {
          result = 1;
        }
        else
        {
          CLogFile::Write(
            &v10->_kLogSys,
            "DeleteLink() >> StorageCode:%d - %I64u",
            (unsigned __int8)v12,
            pItem->m_lnUID);
          result = 0;
        }
      }
      else
      {
        CLogFile::Write(
          &v10->_kLogSys,
          "[NO_NELD]DeleteLink() >> StorageCode:%d - %I64u",
          (unsigned __int8)v12,
          pItem->m_lnUID);
        result = 0;
      }
    }
    else
    {
      CLogFile::Write(
        &v10->_kLogSys,
        "[NO_CASH]DeleteLink() >> StorageCode:%d - %I64u",
        (unsigned __int8)v12,
        pItem->m_lnUID);
      result = 0;
    }
  }
  else
  {
    v9 = &v10->_kLogSys;
    CLogFile::Write(
      &v10->_kLogSys,
      "DeleteLink() >> (wIdx >= MAXPLAYER) - [%d, byStorageCode:%d]",
      wIdx,
      (unsigned __int8)byStorageCode);
    result = 0;
  }
  return result;
}
