/*
 * Function: ??E?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEAAAEAV01@XZ
 * Address: 0x14031EAB0
 */

std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator++(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this)
{
  ++this->_Myoff;
  return this;
}
