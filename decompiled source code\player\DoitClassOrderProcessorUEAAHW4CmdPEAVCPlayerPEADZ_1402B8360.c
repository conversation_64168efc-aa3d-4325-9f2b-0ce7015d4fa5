/*
 * Function: ?Doit@ClassOrderProcessor@@UEAAHW4Cmd@@PEAVCPlayer@@PEAD@Z
 * Address: 0x1402B8360
 */

__int64 __fastcall ClassOrderProcessor::Doit(ClassOrderProcessor *this, Cmd eCmd, CPlayer *pOne, char *pdata)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  Cmd v9; // [sp+24h] [bp-14h]@4
  ClassOrderProcessor *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = eCmd;
  if ( eCmd == 14 )
  {
    v8 = ClassOrderProcessor::_QueryAppoint(v10, pOne, pdata);
  }
  else
  {
    switch ( v9 )
    {
      case 15:
        v8 = ClassOrderProcessor::_RequestAppoint(v10, pOne, pdata);
        break;
      case 16:
        v8 = ClassOrderProcessor::_ResponseAppoint(v10, pOne, pdata);
        break;
      case 17:
        v8 = ClassOrderProcessor::_RequestDischarge(v10, pOne, pdata);
        break;
      case 18:
        ClassOrderProcessor::SendMsg_PatriarchInform(v10, pOne);
        break;
      default:
        v8 = 255;
        break;
    }
  }
  return (unsigned int)v8;
}
