/*
 * Function: ?SetScene@CHolyStoneSystem@@QEAA_NEHKH@Z
 * Address: 0x14027B840
 */

char __fastcall CHolyStoneSystem::SetScene(CHolyStoneSystem *this, char byNumOfTime, int nSceneCode, unsigned int nPassTime, int nChangeReason)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CRaceBuffManager *v8; // rax@7
  CRaceBuffManager *v9; // rax@10
  unsigned int v10; // eax@11
  unsigned int v11; // eax@15
  CRaceBuffManager *v12; // rax@18
  CRaceBuffManager *v13; // rax@22
  unsigned int v14; // eax@26
  unsigned int v15; // eax@29
  unsigned int v16; // eax@30
  __int64 v17; // [sp+0h] [bp-88h]@1
  CHolyScheduleData::__HolyScheduleNode *v18; // [sp+20h] [bp-68h]@4
  int v19; // [sp+28h] [bp-60h]@6
  int v20; // [sp+2Ch] [bp-5Ch]@6
  unsigned int v21; // [sp+30h] [bp-58h]@11
  CHolyScheduleData *v22; // [sp+38h] [bp-50h]@11
  unsigned int v23; // [sp+40h] [bp-48h]@15
  CHolyScheduleData *v24; // [sp+48h] [bp-40h]@15
  unsigned int v25; // [sp+50h] [bp-38h]@26
  CHolyScheduleData *v26; // [sp+58h] [bp-30h]@26
  unsigned int v27; // [sp+60h] [bp-28h]@29
  CHolyScheduleData *v28; // [sp+68h] [bp-20h]@29
  unsigned int v29; // [sp+70h] [bp-18h]@30
  CHolyScheduleData *v30; // [sp+78h] [bp-10h]@30
  CHolyStoneSystem *v31; // [sp+90h] [bp+8h]@1
  char v32; // [sp+98h] [bp+10h]@1
  int v33; // [sp+A0h] [bp+18h]@1
  unsigned int v34; // [sp+A8h] [bp+20h]@1

  v34 = nPassTime;
  v33 = nSceneCode;
  v32 = byNumOfTime;
  v31 = this;
  v5 = &v17;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v18 = CHolyScheduleData::GetIndex(&v31->m_ScheculeData, (unsigned __int8)byNumOfTime);
  if ( v18 )
  {
    v31->m_SaveData.m_byNumOfTime = v32;
    v31->m_SaveData.m_nSceneCode = v33;
    v19 = -1;
    v20 = v33 - 1;
    switch ( v33 )
    {
      case 1:
        v8 = CRaceBuffManager::Instance();
        CRaceBuffManager::RequestHolyQuestRaceBuff(v8, 1);
        v31->m_dwCheckTime[0] = GetLoopTime() - v34;
        v31->m_dwCheckTime[1] = v18->m_nSceneTime[1] + v31->m_dwCheckTime[0];
        CHolyStoneSystem::SetTermTimeDefault(v31, v32);
        v31->m_dwCheckTime[2] = v18->m_nSceneTime[0] + v31->m_dwCheckTime[1];
        v31->m_dwCheckTime[3] = v18->m_nSceneTime[2] + v31->m_dwCheckTime[2];
        v31->m_dwCheckTime[5] = v31->m_SaveData.m_dwTerm[1] + v31->m_SaveData.m_dwTerm[0] + v31->m_dwCheckTime[3];
        v31->m_dwCheckTime[6] = v31->m_dwCheckTime[0]
                              + CHolyScheduleData::GetTotalSceduleTerm(&v31->m_ScheculeData, (unsigned __int8)v32);
        CHolyStoneSystem::On_HS_SCENE_BATTLE_TIME(v31);
        CLogFile::Write(&v31->m_logQuest, ">> HS_SCENE_BATTLE_TIME");
        break;
      case 2:
        v31->m_dwCheckTime[1] = GetLoopTime() - v34;
        v31->m_dwCheckTime[2] = v18->m_nSceneTime[0] + v31->m_dwCheckTime[1];
        if ( CHolyStoneSystem::GetHolyMasterRace(v31) == -1 )
        {
          v31->m_dwCheckTime[3] = v31->m_dwCheckTime[2];
          v31->m_dwCheckTime[5] = v31->m_dwCheckTime[3];
          CHolyStoneSystem::CheckKeeperPlusTime(v31);
          v9 = CRaceBuffManager::Instance();
          CRaceBuffManager::RequestHolyQuestRaceBuff(v9, 2);
        }
        else
        {
          v31->m_dwCheckTime[3] = v18->m_nSceneTime[2] + v31->m_dwCheckTime[2];
          v31->m_dwCheckTime[5] = v31->m_SaveData.m_dwTerm[1] + v31->m_SaveData.m_dwTerm[0] + v31->m_dwCheckTime[3];
        }
        v21 = CHolyStoneSystem::GetStartBattleTickTime(v31);
        v22 = &v31->m_ScheculeData;
        v10 = CHolyScheduleData::GetTotalSceduleTerm(&v31->m_ScheculeData, (unsigned __int8)v32);
        v31->m_dwCheckTime[6] = v10 + v21;
        CHolyStoneSystem::On_HS_SCENE_BATTLE_END_WAIT_TIME(v31);
        CLogFile::Write(&v31->m_logQuest, ">> HS_SCENE_BATTLE_END_WAIT_TIME");
        break;
      case 3:
        v31->m_dwCheckTime[2] = GetLoopTime() - v34;
        v31->m_dwCheckTime[3] = v18->m_nSceneTime[2] + v31->m_dwCheckTime[2];
        if ( CHolyStoneSystem::GetHolyMasterRace(v31) == -1 )
        {
          v31->m_dwCheckTime[3] = v31->m_dwCheckTime[2];
          v31->m_dwCheckTime[5] = v31->m_dwCheckTime[3];
        }
        else
        {
          v31->m_dwCheckTime[5] = v31->m_SaveData.m_dwTerm[1] + v31->m_SaveData.m_dwTerm[0] + v31->m_dwCheckTime[3];
        }
        v23 = CHolyStoneSystem::GetStartBattleTickTime(v31);
        v24 = &v31->m_ScheculeData;
        v11 = CHolyScheduleData::GetTotalSceduleTerm(&v31->m_ScheculeData, (unsigned __int8)v32);
        v31->m_dwCheckTime[6] = v11 + v23;
        CHolyStoneSystem::On_HS_SCENE_KEEPER_ATTACKABLE_TIME(v31);
        CLogFile::Write(&v31->m_logQuest, ">> HS_SCENE_KEEPER_ATTACKABLE_TIME");
        break;
      case 4:
        if ( CMainThread::IsReleaseServiceMode(&g_Main) )
        {
          if ( nChangeReason == 4 )
          {
            CHolyStoneSystem::SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeper(v31, 1);
            v12 = CRaceBuffManager::Instance();
            CRaceBuffManager::RequestHolyQuestRaceBuff(v12, 3);
          }
        }
        else if ( nChangeReason == 4 || nChangeReason == 1 )
        {
          CHolyStoneSystem::SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeper(v31, 1);
          v13 = CRaceBuffManager::Instance();
          CRaceBuffManager::RequestHolyQuestRaceBuff(v13, 3);
        }
        v31->m_dwCheckTime[3] = GetLoopTime() - v34;
        if ( CHolyStoneSystem::GetHolyMasterRace(v31) == -1 )
          v31->m_dwCheckTime[5] = v31->m_dwCheckTime[3];
        else
          v31->m_dwCheckTime[5] = v31->m_SaveData.m_dwTerm[1] + v31->m_SaveData.m_dwTerm[0] + v31->m_dwCheckTime[3];
        v25 = CHolyStoneSystem::GetStartBattleTickTime(v31);
        v26 = &v31->m_ScheculeData;
        v14 = CHolyScheduleData::GetTotalSceduleTerm(&v31->m_ScheculeData, (unsigned __int8)v32);
        v31->m_dwCheckTime[6] = v14 + v25;
        CHolyStoneSystem::On_HS_SCENE_KEEPER_DEATTACKABLE_TIME(v31);
        CLogFile::Write(&v31->m_logQuest, ">> HS_SCENE_KEEPER_DEATTACKABLE_TIME");
        break;
      case 5:
        if ( nChangeReason == 3 )
          CHolyStoneSystem::SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeper(v31, 0);
        v31->m_dwCheckTime[4] = GetLoopTime() - v34;
        v27 = CHolyStoneSystem::GetStartBattleTickTime(v31);
        v28 = &v31->m_ScheculeData;
        v15 = CHolyScheduleData::GetTotalSceduleTerm(&v31->m_ScheculeData, (unsigned __int8)v32);
        v31->m_dwCheckTime[6] = v15 + v27;
        v31->m_dwCheckTime[5] = v31->m_dwCheckTime[6] - v18->m_nSceneTime[4];
        CHolyStoneSystem::On_HS_SCENE_KEEPER_DIE_TIME(v31);
        CLogFile::Write(&v31->m_logQuest, ">> HS_SCENE_KEEPER_DIE_TIME");
        break;
      case 6:
        v31->m_dwCheckTime[5] = GetLoopTime() - v34;
        v29 = CHolyStoneSystem::GetStartBattleTickTime(v31);
        v30 = &v31->m_ScheculeData;
        v16 = CHolyScheduleData::GetTotalSceduleTerm(&v31->m_ScheculeData, (unsigned __int8)v32);
        v31->m_dwCheckTime[6] = v16 + v29;
        CHolyStoneSystem::On_HS_SCENE_KEEPER_CHAOS_TIME(v31);
        CLogFile::Write(&v31->m_logQuest, ">> HS_SCENE_KEEPER_CHAOS_TIME");
        break;
      default:
        break;
    }
    v31->m_SaveData.m_dwPassTimeInScene = v34;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
