/*
 * Function: ?send_installed@AutominePersonal@@QEAAXXZ
 * Address: 0x1402DC9F0
 */

void __fastcall AutominePersonal::send_installed(AutominePersonal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  _personal_automine_install_zocl Dst; // [sp+38h] [bp-60h]@4
  char pbyType; // [sp+74h] [bp-24h]@4
  char v8; // [sp+75h] [bp-23h]@4
  unsigned int v9; // [sp+84h] [bp-14h]@4
  AutominePersonal *v10; // [sp+A0h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _personal_automine_install_zocl::_personal_automine_install_zocl(&Dst);
  Dst.dwObjSerial = v10->m_dwObjSerial;
  Dst.wObjIndex = v10->m_ObjID.m_wIndex;
  Dst.dwOwnerSerial = AutominePersonal::get_ownerserial(v10);
  Dst.wItemSerial = v10->m_pItem->m_wSerial;
  Dst.wItemTblIndex = v10->m_pItem->m_wItemIndex;
  memcpy_0(Dst.fPos, v10->m_fCurPos, 0xCui64);
  Dst.byFilledSlotCnt = v10->m_byFilledSlotCnt;
  pbyType = 14;
  v8 = 45;
  v9 = AutominePersonal::get_ownerserial(v10);
  v3 = _personal_automine_install_zocl::size(&Dst);
  CGameObject::CircleReport((CGameObject *)&v10->vfptr, &pbyType, (char *)&Dst, v3, v9, 0);
  v4 = _personal_automine_install_zocl::size(&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_pOwner->m_id.wIndex, &pbyType, (char *)&Dst, v4);
}
