/*
 * Function: ??D?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEBAAEBQEAVCUnmannedTraderSubClassInfo@@XZ
 * Address: 0x14037F370
 */

CUnmannedTraderSubClassInfo **__fastcall std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator*(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this)
{
  return this->_Myptr;
}
