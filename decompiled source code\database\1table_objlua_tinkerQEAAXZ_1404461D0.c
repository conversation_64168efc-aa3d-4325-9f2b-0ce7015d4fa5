/*
 * Function: ??1table_obj@lua_tinker@@QEAA@XZ
 * Address: 0x1404461D0
 */

void __fastcall lua_tinker::table_obj::~table_obj(lua_tinker::table_obj *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  lua_tinker::table_obj *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( lua_tinker::table_obj::validate(v4) )
    lua_remove(v4->m_L, v4->m_index);
}
