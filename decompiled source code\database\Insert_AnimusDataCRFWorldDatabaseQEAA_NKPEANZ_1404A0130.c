/*
 * Function: ?Insert_AnimusData@CRFWorldDatabase@@QEAA_NKPEAN@Z
 * Address: 0x1404A0130
 */

bool __fastcall CRFWorldDatabase::Insert_AnimusData(CRFWorldDatabase *this, unsigned int dwSerial, long double *pVal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-488h]@1
  __int64 v7; // [sp+20h] [bp-468h]@6
  __int64 v8; // [sp+28h] [bp-460h]@6
  __int64 v9; // [sp+30h] [bp-458h]@6
  __int64 v10; // [sp+38h] [bp-450h]@6
  __int64 v11; // [sp+40h] [bp-448h]@6
  char Dest; // [sp+60h] [bp-428h]@6
  unsigned __int64 v13; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+490h] [bp+8h]@1

  v14 = this;
  v3 = &v6;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( pVal )
  {
    v11 = *((_QWORD *)pVal + 5);
    v10 = *((_QWORD *)pVal + 4);
    v9 = *((_QWORD *)pVal + 3);
    v8 = *((_QWORD *)pVal + 2);
    v7 = *((_QWORD *)pVal + 1);
    sprintf(&Dest, "{ CALL pInsert_AnimusData( %u, %.0f, %.0f, %.0f, %.0f, %.0f, %.0f ) }", dwSerial, *(_QWORD *)pVal);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v14->vfptr, &Dest, 1);
  }
  else
  {
    result = 0;
  }
  return result;
}
