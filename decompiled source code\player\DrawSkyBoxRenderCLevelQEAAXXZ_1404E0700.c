/*
 * Function: ?DrawSkyBoxRender@CLevel@@QEAAXXZ
 * Address: 0x1404E0700
 */

void __fastcall CLevel::DrawSkyBoxRender(CLevel *this)
{
  CLevel *v1; // rbx@1
  struct IDirect3DDevice8 *v2; // rax@1
  struct IDirect3DDevice8 *v3; // rax@1

  v1 = this;
  D3DXMatrixPerspectiveFovLH_0(dword_184A79B2C);
  v2 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, float *))v2->vfptr[12].AddRef)(
    v2,
    3i64,
    dword_184A79B2C);
  CSkyBox::DrawSkyBox(v1->mSkyBox, &v1->mMatView);
  DrawFadeSKy();
  D3DXMatrixPerspectiveFovLH_0(dword_184A79B2C);
  v3 = GetD3dDevice();
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, float *))v3->vfptr[12].AddRef)(
    v3,
    3i64,
    dword_184A79B2C);
}
