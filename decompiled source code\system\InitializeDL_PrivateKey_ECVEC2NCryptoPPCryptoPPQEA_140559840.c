/*
 * Function: ?Initialize@?$DL_PrivateKey_EC@VEC2N@CryptoPP@@@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@AEBVEC2N@2@AEBUEC2NPoint@2@AEBVInteger@2@@Z
 * Address: 0x140559840
 */

int __fastcall CryptoPP::DL_PrivateKey_EC<CryptoPP::EC2N>::Initialize(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5)
{
  const struct CryptoPP::Integer *v5; // rax@1
  char v7; // [sp+38h] [bp-1F0h]@1
  __int64 v8; // [sp+1F8h] [bp-30h]@1
  __int64 v9; // [sp+200h] [bp-28h]@1
  __int64 v10; // [sp+208h] [bp-20h]@1
  __int64 v11; // [sp+210h] [bp-18h]@2
  void (__fastcall **v12)(_QWORD, _QWORD, _QWORD); // [sp+218h] [bp-10h]@4
  __int64 v13; // [sp+230h] [bp+8h]@1
  __int64 v14; // [sp+238h] [bp+10h]@1
  __int64 v15; // [sp+240h] [bp+18h]@1
  __int64 v16; // [sp+248h] [bp+20h]@1

  v16 = a4;
  v15 = a3;
  v14 = a2;
  v13 = a1;
  v8 = -2i64;
  v5 = CryptoPP::Integer::Zero();
  v9 = CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::DL_GroupParameters_EC<CryptoPP::EC2N>(
         (__int64)&v7,
         v15,
         v16,
         a5,
         (__int64)v5,
         1);
  v10 = v9;
  if ( v9 )
    v11 = v9 + *(_DWORD *)(*(_QWORD *)(v9 + 8) + 4i64) + 8;
  else
    v11 = 0i64;
  v12 = *(void (__fastcall ***)(_QWORD, _QWORD, _QWORD))(v13 + 16);
  (*v12)(v13 + 16, v14, v11);
  return CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>::`vbase destructor(&v7);
}
