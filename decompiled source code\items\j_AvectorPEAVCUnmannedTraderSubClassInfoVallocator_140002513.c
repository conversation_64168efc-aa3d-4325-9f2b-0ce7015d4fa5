/*
 * Function: j_??A?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderSubClassInfo@@_K@Z
 * Address: 0x140002513
 */

CUnmannedTraderSubClassInfo **__fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator[](std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator[](
           this,
           _Pos);
}
