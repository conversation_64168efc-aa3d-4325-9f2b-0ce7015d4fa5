/*
 * Function: ?UpdateLookAtPos@CMonster@@QEAAXQEAM@Z
 * Address: 0x140148220
 */

void __fastcall CMonster::UpdateLookAtPos(CMonster *this, float *vLookAt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // xmm0_4@4
  float v5; // xmm0_4@4
  __int64 v6; // [sp+0h] [bp-58h]@1
  float v; // [sp+28h] [bp-30h]@4
  float v8; // [sp+2Ch] [bp-2Ch]@4
  float v9; // [sp+30h] [bp-28h]@4
  int v10; // [sp+44h] [bp-14h]@4
  CMonster *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v = *vLookAt - v11->m_fCurPos[0];
  v8 = vLookAt[1] - v11->m_fCurPos[1];
  v9 = vLookAt[2] - v11->m_fCurPos[2];
  *(float *)&v4 = v9;
  Normalize(&v);
  CMonster::GetVisualField(v11);
  v10 = v4;
  v = *(float *)&v4 * v;
  v8 = *(float *)&v4 * v8;
  v9 = *(float *)&v4 * v9;
  v11->m_fLookAtPos[0] = v11->m_fCurPos[0] + v;
  v11->m_fLookAtPos[1] = v11->m_fCurPos[1] + v8;
  v5 = v11->m_fCurPos[2] + v9;
  v11->m_fLookAtPos[2] = v5;
  GetYAngle(v11->m_fCurPos, v11->m_fLookAtPos);
  v11->m_fYAngle = v5;
}
