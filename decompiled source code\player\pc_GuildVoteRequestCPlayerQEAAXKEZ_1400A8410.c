/*
 * Function: ?pc_GuildVoteRequest@CPlayer@@QEAAXKE@Z
 * Address: 0x1400A8410
 */

void __fastcall CPlayer::pc_GuildVoteRequest(CPlayer *this, unsigned int dwMatterVoteSynKey, char byVoteCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  CGuild *v7; // [sp+28h] [bp-10h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  unsigned int dwMatterVoteSynKeya; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = byVoteCode;
  dwMatterVoteSynKeya = dwMatterVoteSynKey;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = 0;
  v7 = v8->m_Param.m_pGuild;
  if ( v7 )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v8->m_id.wIndex) == 99 )
    {
      v6 = 106;
    }
    else if ( v7->m_bNowProcessSgtMter )
    {
      if ( v7->m_SuggestedMatter.dwMatterVoteSynKey == dwMatterVoteSynKeya )
      {
        if ( _suggested_matter::IsVotable(&v7->m_SuggestedMatter, v8->m_dwObjSerial) )
        {
          if ( v8->m_Param.m_pGuildMemPtr->bVote )
            v6 = 66;
        }
        else
        {
          v6 = -53;
        }
      }
      else
      {
        v6 = 63;
      }
    }
    else
    {
      v6 = 62;
    }
  }
  else
  {
    v6 = -54;
  }
  if ( !v6 )
    CGuild::ActVote(v7, v8->m_Param.m_pGuildMemPtr, v10);
  CPlayer::SendMsg_VoteResult(v8, dwMatterVoteSynKeya, v6);
}
