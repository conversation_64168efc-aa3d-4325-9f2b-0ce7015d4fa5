/*
 * Function: ?consume_del_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAD@Z
 * Address: 0x14023C5D0
 */

void __fastcall CMgrAvatorItemHistory::consume_del_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@4
  __int64 v7; // [sp+0h] [bp-68h]@1
  unsigned __int64 v8; // [sp+20h] [bp-48h]@4
  char *v9; // [sp+28h] [bp-40h]@4
  char *v10; // [sp+30h] [bp-38h]@4
  _base_fld *v11; // [sp+40h] [bp-28h]@4
  char *v12; // [sp+48h] [bp-20h]@4
  char *v13; // [sp+50h] [bp-18h]@4
  int nTableCode; // [sp+58h] [bp-10h]@4
  CMgrAvatorItemHistory *v15; // [sp+70h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v16; // [sp+80h] [bp+18h]@1
  char *pszFileNamea; // [sp+88h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v16 = pItem;
  v15 = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v12 = v15->m_szCurTime;
  v13 = v15->m_szCurDate;
  nTableCode = v16->m_byTableCode;
  v6 = DisplayItemUpgInfo(nTableCode, v16->m_dwLv);
  v10 = v12;
  v9 = v13;
  v8 = v16->m_lnUID;
  sprintf(sData, "CONSUM: %s_@%s[%I64u] [%s %s]\r\n", v11->m_strCode, v6);
  CMgrAvatorItemHistory::WriteFile(v15, pszFileNamea, sData);
}
