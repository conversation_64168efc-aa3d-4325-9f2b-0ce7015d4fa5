/*
 * Function: ?GetPlayerInfo@CDarkHoleChannel@@QEAAPEAV_dh_player_mgr@@K@Z
 * Address: 0x14026ADB0
 */

_dh_player_mgr *__fastcall CDarkHoleChannel::GetPlayerInfo(CDarkHoleChannel *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CDarkHoleChannel *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = dwSerial;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 32; ++j )
  {
    if ( _dh_player_mgr::IsFill(&v7->m_Quester[j]) && v7->m_Quester[j].dwSerial == v8 )
      return &v7->m_Quester[j];
  }
  return 0i64;
}
