/*
 * Function: _dynamic_initializer_for__CGuardTower::s_dwOldTick_CheckTemp__
 * Address: 0x1406DBAE0
 */

DWORD dynamic_initializer_for__CGuardTower::s_dwOldTick_CheckTemp__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  DWORD result; // eax@4
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  result = timeGetTime();
  CGuardTower::s_dwOldTick_CheckTemp = result;
  return result;
}
