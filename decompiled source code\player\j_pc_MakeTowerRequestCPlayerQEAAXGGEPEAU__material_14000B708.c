/*
 * Function: j_?pc_MakeTowerRequest@CPlayer@@QEAAXGGEPEAU__material@_make_tower_request_clzo@@PEAMPEAG@Z
 * Address: 0x14000B708
 */

void __fastcall CPlayer::pc_MakeTowerRequest(CPlayer *this, unsigned __int16 wSkillIndex, unsigned __int16 wTowerItemSerial, char byMaterial<PERSON>um, _make_tower_request_clzo::__material *pMaterial, float *pfPos, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_MakeTowerRequest(this, wSkillIndex, wTowerItemSerial, byMaterialNum, pMaterial, pfPos, pConsumeSerial);
}
