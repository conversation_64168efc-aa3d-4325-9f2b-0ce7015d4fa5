/*
 * Function: j_?GetGroupID@CUnmannedTraderDivisionInfo@@QEAA_NEGAEAE00AEAK@Z
 * Address: 0x14000EA2A
 */

bool __fastcall CUnmannedTraderDivisionInfo::GetGroupID(CUnmannedTraderDivisionInfo *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex)
{
  return CUnmannedTraderDivisionInfo::GetGroupID(
           this,
           byTableCode,
           wItemTableIndex,
           byDivision,
           byClass,
           bySubClass,
           dwListIndex);
}
