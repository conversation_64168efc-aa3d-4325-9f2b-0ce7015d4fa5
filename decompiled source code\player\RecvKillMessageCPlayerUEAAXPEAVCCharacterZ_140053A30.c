/*
 * Function: ?RecvKillMessage@CPlayer@@UEAAXPEAVCCharacter@@@Z
 * Address: 0x140053A30
 */

void __fastcall CPlayer::RecvKillMessage(CPlayer *this, CCharacter *pDier)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@5
  char *v5; // rax@5
  int v6; // eax@6
  char *v7; // rax@6
  CActionPointSystemMgr *v8; // rax@9
  int v9; // eax@10
  unsigned int v10; // eax@11
  int *v11; // rax@13
  __int64 v12; // [sp+0h] [bp-68h]@1
  bool bParty[4]; // [sp+20h] [bp-48h]@13
  CGameObject *pObj; // [sp+28h] [bp-40h]@13
  CPlayer *pDiera; // [sp+30h] [bp-38h]@5
  bool v16; // [sp+38h] [bp-30h]@5
  CCharacter *v17; // [sp+40h] [bp-28h]@9
  unsigned int dwPoint; // [sp+48h] [bp-20h]@11
  bool v19; // [sp+4Ch] [bp-1Ch]@12
  int v20; // [sp+50h] [bp-18h]@10
  CGameObjectVtbl *v21; // [sp+58h] [bp-10h]@10
  CPlayer *v22; // [sp+70h] [bp+8h]@1
  CCharacter *v23; // [sp+78h] [bp+10h]@1

  v23 = pDier;
  v22 = this;
  v2 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pDier->m_ObjID.m_byID )
  {
    if ( pDier->m_ObjID.m_byID == 1 )
    {
      v17 = pDier;
      CQuestMgr::CheckFailLoop(&v22->m_QuestMgr, 3, pDier->m_pRecordSet->m_strCode);
      v8 = CActionPointSystemMgr::Instance();
      if ( CActionPointSystemMgr::GetEventStatus(v8, 1) == 2 )
      {
        v20 = CPlayerDB::GetLevel(&v22->m_Param);
        v21 = v17->vfptr;
        v9 = ((int (__fastcall *)(CCharacter *))v21->GetLevel)(v17);
        if ( abs_0(v20 - v9) < 10 )
        {
          v10 = CUserDB::GetActPoint(v22->m_pUserDB, 1);
          dwPoint = *(_DWORD *)(*(_QWORD *)&v17[1].m_fAbsPos[1] + 208i64) + v10;
          CUserDB::Update_User_Action_Point(v22->m_pUserDB, 1, dwPoint);
          CPlayer::SendMsg_Alter_Action_Point(v22, 1, dwPoint);
        }
      }
      v19 = 0;
      if ( v22->m_pDHChannel )
      {
        v11 = *(int **)&v17[1].m_fAbsPos[1];
        pObj = (CGameObject *)v23;
        *(_DWORD *)bParty = 1;
        v19 = CDarkHoleChannel::CheckEvent(v22->m_pDHChannel, dh_event_hunt, 255, *v11, 1, (CGameObject *)&v23->vfptr);
      }
      if ( !v19 )
      {
        CPlayer::Emb_CheckActForQuest(v22, 3, v17->m_pRecordSet->m_strCode, 1u, 0);
        if ( CPartyPlayer::IsPartyMode(v22->m_pPartyMgr) )
          CPlayer::Emb_CheckActForQuestParty(v22, 3, v17->m_pRecordSet->m_strCode, 1u);
      }
    }
  }
  else
  {
    pDiera = (CPlayer *)pDier;
    v4 = CPlayerDB::GetRaceCode((CPlayerDB *)&pDier[1].m_fOldPos[2]);
    v5 = cvt_string(v4);
    v16 = CPlayer::Emb_CreateQuestEvent(v22, quest_happen_type_pk, v5);
    if ( !v16 )
    {
      v6 = CPlayerDB::GetRaceCode(&pDiera->m_Param);
      v7 = cvt_string(v6);
      CPlayer::Emb_CheckActForQuest(v22, 2, v7, 1u, 0);
    }
    CPlayer::CalcPvP(v22, pDiera, 0);
    CPlayer::CalPvpTempCash(v22, pDiera, 0);
    CPlayer::IncCriEffKillPoint(v22);
  }
}
