/*
 * Function: j_?InitUseField@CNormalGuildBattleFieldList@GUILD_BATTLE@@AEAA_NEPEAD0QEAPEAD@Z
 * Address: 0x14000AD80
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::InitUseField(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char by<PERSON><PERSON>, char *sz<PERSON><PERSON><PERSON><PERSON>, char *szStr<PERSON><PERSON>, char **szParseBuff)
{
  return GUILD_BATTLE::CNormalGuildBattleFieldList::InitUseField(this, by<PERSON><PERSON>, sz<PERSON><PERSON><PERSON><PERSON>, szStr<PERSON><PERSON>, szParseBuff);
}
