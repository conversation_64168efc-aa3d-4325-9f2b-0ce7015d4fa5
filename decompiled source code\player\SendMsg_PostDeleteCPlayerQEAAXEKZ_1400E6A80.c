/*
 * Function: ?SendMsg_PostDelete@CPlayer@@QEAAXEK@Z
 * Address: 0x1400E6A80
 */

void __fastcall CPlayer::SendMsg_PostDelete(CPlayer *this, char byErrCode, unsigned int dwPostSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v7; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = byErrCode;
  *(_DWORD *)szMsg = dwPostSerial;
  pbyType = 58;
  v9 = 12;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 5u);
}
