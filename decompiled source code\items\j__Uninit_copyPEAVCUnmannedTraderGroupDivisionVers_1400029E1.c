/*
 * Function: j_??$_Uninit_copy@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400029E1
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::_Uninit_copy<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, CUnmannedTraderGroupDivisionVersionInfo *_Last, CUnmannedTraderGroupDivisionVersionInfo *_Dest, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
