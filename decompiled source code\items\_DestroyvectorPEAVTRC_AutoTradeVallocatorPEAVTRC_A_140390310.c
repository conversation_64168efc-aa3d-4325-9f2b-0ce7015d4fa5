/*
 * Function: ?_Destroy@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAXPEAPEAVTRC_AutoTrade@@0@Z
 * Address: 0x140390310
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Destroy(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_First, TRC_AutoTrade **_Last)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Destroy_range<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(_First, _Last, &v6->_Alval);
}
