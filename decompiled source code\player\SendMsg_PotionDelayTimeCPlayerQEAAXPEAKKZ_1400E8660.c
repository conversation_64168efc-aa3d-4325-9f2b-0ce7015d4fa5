/*
 * Function: ?SendMsg_PotionDelayTime@CPlayer@@QEAAXPEAKK@Z
 * Address: 0x1400E8660
 */

void __fastcall CPlayer::SendMsg_PotionDelayTime(CPlayer *this, unsigned int *pdwPotionNextUseTime, unsigned int dwCurTime)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-118h]@1
  char szMsg[4]; // [sp+40h] [bp-D8h]@4
  int v7[40]; // [sp+44h] [bp-D4h]@7
  int j; // [sp+E4h] [bp-34h]@4
  unsigned int v9; // [sp+E8h] [bp-30h]@6
  char pbyType; // [sp+F4h] [bp-24h]@10
  char v11; // [sp+F5h] [bp-23h]@10
  CPlayer *v12; // [sp+120h] [bp+8h]@1

  v12 = this;
  v3 = &v5;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(_DWORD *)szMsg = 38;
  for ( j = 0; j < 38; ++j )
  {
    v9 = pdwPotionNextUseTime[j] - dwCurTime;
    if ( (signed int)v9 <= 0 )
      v7[j] = 0;
    else
      v7[j] = v9;
  }
  pbyType = 3;
  v11 = 60;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 0x9Cu);
}
