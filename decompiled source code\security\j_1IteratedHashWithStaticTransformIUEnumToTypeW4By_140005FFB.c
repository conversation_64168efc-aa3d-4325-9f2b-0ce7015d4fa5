/*
 * Function: j_??1?$IteratedHashWithStaticTransform@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@$0BE@VSHA1@2@$0A@@CryptoPP@@UEAA@XZ
 * Address: 0x140005FFB
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>::~IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>(CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0> *this)
{
  CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>::~IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>(this);
}
