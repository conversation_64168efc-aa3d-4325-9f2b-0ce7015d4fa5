/*
 * Function: ?SetFlag@CMoveMapLimitRightPortal@@UEAAXH_N@Z
 * Address: 0x1403AC790
 */

void __fastcall CMoveMapLimitRightPortal::SetFlag(CMoveMapLimitRightPortal *this, int iType, bool bFlag)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CMoveMapLimitRightPortal *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  v5 = iType;
  if ( !iType )
    v6->m_bNotifyForceMoveStartPosition = bFlag;
}
