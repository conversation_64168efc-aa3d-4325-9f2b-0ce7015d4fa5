/*
 * Function: _CUnmannedTraderUserInfo::CompleteRepriceItem_::_1_::dtor$1
 * Address: 0x14035B310
 */

void __fastcall CUnmannedTraderUserInfo::CompleteRepriceItem_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 100) & 1 )
  {
    *(_DWORD *)(a2 + 100) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 72));
  }
}
