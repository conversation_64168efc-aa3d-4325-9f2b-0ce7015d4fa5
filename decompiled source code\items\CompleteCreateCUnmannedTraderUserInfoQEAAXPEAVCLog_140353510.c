/*
 * Function: ?CompleteCreate@CUnmannedTraderUserInfo@@QEAAXPEAVCLogFile@@@Z
 * Address: 0x140353510
 */

void __fastcall CUnmannedTraderUserInfo::CompleteCreate(CUnmannedTraderUserInfo *this, CLogFile *pkLogger)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-1B8h]@1
  unsigned __int16 Dst; // [sp+40h] [bp-178h]@4
  unsigned int v7; // [sp+44h] [bp-174h]@4
  char v8; // [sp+49h] [bp-16Fh]@4
  unsigned __int16 v9; // [sp+4Ah] [bp-16Eh]@4
  CPlayer *v10; // [sp+198h] [bp-20h]@4
  unsigned int v11; // [sp+1A0h] [bp-18h]@7
  CUnmannedTraderUserInfo *v12; // [sp+1C0h] [bp+8h]@1
  CLogFile *pkLoggera; // [sp+1C8h] [bp+10h]@1

  pkLoggera = pkLogger;
  v12 = this;
  v2 = &v5;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memset_0(&Dst, 0, 0x14Cui64);
  Dst = v12->m_wInx;
  v7 = v12->m_dwUserSerial;
  v8 = 0;
  v9 = 0;
  v10 = CUnmannedTraderUserInfo::FindOwner(v12);
  if ( v10 && v10->m_bOper )
  {
    CMgrAvatorItemHistory::ClearLogBuffer(&CPlayer::s_MgrItemHistory);
    CUnmannedTraderUserInfo::PrcoSellUpdateWaitItem(
      v12,
      (_qry_case_unmandtrader_log_in_proc_update_complete *)&Dst,
      0,
      pkLoggera);
    CUnmannedTraderUserInfo::SetCompleteInfo(v12, pkLoggera);
    CUnmannedTraderUserInfo::NotifyRegistItem(v12);
    CUnmannedTraderUserInfo::ProcSellWaitItem(
      v12,
      (_qry_case_unmandtrader_log_in_proc_update_complete *)&Dst,
      0,
      pkLoggera);
    CUnmannedTraderUserInfo::NotifyCloseItem(v12, (_qry_case_unmandtrader_log_in_proc_update_complete *)&Dst, pkLoggera);
    CUnmannedTraderUserInfo::ClearLoadItemInfo(v12);
    v4 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
           &v12->m_vecRegistItemInfo,
           0i64);
    CMgrAvatorItemHistory::have_auto_item(&CPlayer::s_MgrItemHistory, v10->m_ObjID.m_wIndex, v4, 10);
    CMgrAvatorItemHistory::WriteLog(&CPlayer::s_MgrItemHistory, v10->m_szItemHistoryFileName);
    if ( (signed int)v9 > 0 )
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 111, (char *)&Dst, 332);
    v12->m_eState = 1;
  }
  else
  {
    if ( v10 )
      v11 = v10->m_bOper;
    else
      v11 = -1;
    CLogFile::Write(
      pkLoggera,
      "CUnmannedTraderUserInfo::CompleteCreate()\r\n\t\t( 0 == pkOwner(%p) || !pkOwner->m_bOper(%d) )\r\n",
      v10,
      v11);
  }
}
