/*
 * Function: j_??$fill@PEAPEAVCUnmannedTraderClassInfo@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@0AEBQEAV1@@Z
 * Address: 0x14000FB41
 */

void __fastcall std::fill<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo *const *_Val)
{
  std::fill<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo *>(_First, _Last, _Val);
}
