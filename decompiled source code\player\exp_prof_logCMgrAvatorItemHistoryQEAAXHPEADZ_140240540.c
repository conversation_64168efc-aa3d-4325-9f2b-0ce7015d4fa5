/*
 * Function: ?exp_prof_log@CMgrAvatorItemHistory@@QEAAXHPEAD@Z
 * Address: 0x140240540
 */

void __fastcall CMgrAvatorItemHistory::exp_prof_log(CMgrAvatorItemHistory *this, int count, char *szFile)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMgrAvatorItemHistory *v6; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1
  char *pszFileName; // [sp+40h] [bp+18h]@1

  pszFileName = szFile;
  v7 = count;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(sData, 0, 0x4E20ui64);
  sprintf_s(sData, 0x4E20ui64, "[Exp_Prof_Using] :  %d \r\n", (unsigned int)v7);
  CMgrAvatorItemHistory::WriteFile(v6, pszFileName, sData);
}
