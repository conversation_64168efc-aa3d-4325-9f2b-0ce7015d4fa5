/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@XZ
 * Address: 0x14055E9F0
 */

signed __int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::AccessBasePrecomputation(__int64 a1)
{
  return a1 + 80;
}
