/*
 * Function: j_?IncreaseVersion@CUnmannedTraderGroupItemInfoTable@@QEAA_NEGEE@Z
 * Address: 0x140009CE1
 */

bool __fastcall CUnmannedTraderGroupItemInfoTable::IncreaseVersion(CUnmannedTraderGroupItemInfoTable *this, char byTableCode, unsigned __int16 wItemTableIndex, char byRegistDivision, char byRegistClass)
{
  return CUnmannedTraderGroupItemInfoTable::IncreaseVersion(
           this,
           byTableCode,
           wItemTableIndex,
           byRegistDivision,
           byRegistClass);
}
