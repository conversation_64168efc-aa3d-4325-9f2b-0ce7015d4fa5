/*
 * Function: ?Alloc@?$CArray@VCLuaLooting_Novus_Item@@@US@@QEAAXK@Z
 * Address: 0x140405B30
 */

void __fastcall US::CArray<CLuaLooting_Novus_Item>::Alloc(US::CArray<CLuaLooting_Novus_Item> *this, unsigned int dwCount)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v4; // rax@6
  unsigned __int8 v5; // cf@8
  unsigned __int64 v6; // rax@8
  __int64 v7; // [sp+0h] [bp-68h]@1
  int count[2]; // [sp+30h] [bp-38h]@6
  void *v9; // [sp+40h] [bp-28h]@10
  __int64 v10; // [sp+48h] [bp-20h]@4
  CLuaLooting_Novus_Item *v11; // [sp+50h] [bp-18h]@11
  US::CArray<CLuaLooting_Novus_Item> *v12; // [sp+70h] [bp+8h]@1
  unsigned int v13; // [sp+78h] [bp+10h]@1

  v13 = dwCount;
  v12 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  if ( !v12->m_bAlloc && dwCount )
  {
    *(_QWORD *)count = dwCount;
    v4 = 88i64 * dwCount;
    if ( !is_mul_ok(0x58ui64, dwCount) )
      v4 = -1i64;
    v5 = __CFADD__(v4, 8i64);
    v6 = v4 + 8;
    if ( v5 )
      v6 = -1i64;
    v9 = operator new[](v6);
    if ( v9 )
    {
      *(_DWORD *)v9 = count[0];
      `eh vector constructor iterator'(
        (char *)v9 + 8,
        0x58ui64,
        count[0],
        (void (__cdecl *)(void *))CLuaLooting_Novus_Item::CLuaLooting_Novus_Item,
        (void (__cdecl *)(void *))CLuaLooting_Novus_Item::~CLuaLooting_Novus_Item);
      v11 = (CLuaLooting_Novus_Item *)((char *)v9 + 8);
    }
    else
    {
      v11 = 0i64;
    }
    v12->m_pBuffer = v11;
    v12->m_dwCount = v13;
    v12->m_bAlloc = 1;
  }
}
