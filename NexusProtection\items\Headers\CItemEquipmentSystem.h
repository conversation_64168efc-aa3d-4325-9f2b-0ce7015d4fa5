/*
 * CItemEquipmentSystem.h - Modern Item Equipment and Store System
 * Refactored from decompiled C item equipment, store, and special item functions
 * Provides comprehensive equipment management, item stores, and special item handling
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <optional>
#include <array>

// Forward declarations
class CPlayer;
class CMapData;
class CLuaLootingMgr;
struct _STORAGE_LIST;
struct _golden_box_item;
struct _itembox_create_setdata;

namespace NexusProtection {
namespace Items {

/**
 * Equipment operation result codes
 */
enum class EquipmentResult : int {
    Success = 1,
    Failure = 0,
    InvalidItem = -1,
    InvalidSlot = -2,
    RequirementNotMet = -3,
    AlreadyEquipped = -4,
    CannotUnequip = -5,
    SystemError = -6
};

/**
 * Store operation result codes
 */
enum class StoreResult : int {
    Success = 1,
    Failure = 0,
    InvalidStore = -1,
    ItemNotAvailable = -2,
    InsufficientFunds = -3,
    InventoryFull = -4,
    SystemError = -5
};

/**
 * Special item result codes
 */
enum class SpecialItemResult : int {
    Success = 1,
    Failure = 0,
    InvalidBox = -1,
    BoxEmpty = -2,
    CannotOpen = -3,
    RewardFailed = -4,
    SystemError = -5
};

/**
 * Equipment slot types (based on actual game table codes)
 * From GetItemTableCode function and pc_EquipPart validation (table codes 0-7 are equipment)
 */
enum class EquipmentSlot : uint8_t {
    // Equipment slots (table codes 0-7)
    Weapon = 0,      // "iu" - Weapon (table code 0)
    LeftHand = 1,    // "il" - Left hand weapon/shield (table code 1)
    Gloves = 2,      // "ig" - Gloves (table code 2)
    Shoes = 3,       // "is" - Shoes/Boots (table code 3)
    Helmet = 4,      // "ih" - Helmet (table code 4)
    Armor = 5,       // "id" - Armor/Dress (table code 5)
    Wings = 6,       // "iw" - Wings (table code 6)
    Ring = 7,        // "ik" - Ring (table code 7)
    MaxEquipSlots = 8,

    // Non-equipment items (for reference)
    Accessory = 8,   // "ii" - Accessory items (table code 8)
    Consumable = 9,  // "ia" - Consumable items (table code 9)
    Misc = 10,       // "ib" - Miscellaneous items (table code 10)
    Material = 11,   // "im" - Material items (table code 11)

    None = 255
};

/**
 * Item store types
 */
enum class StoreType : uint8_t {
    General = 0,
    Weapon = 1,
    Armor = 2,
    Accessory = 3,
    Consumable = 4,
    Special = 5,
    Event = 6,
    Cash = 7
};

/**
 * Special item types
 */
enum class SpecialItemType : uint8_t {
    GoldenBox = 0,
    EventBox = 1,
    RewardBox = 2,
    LootBox = 3,
    QuestBox = 4,
    CashBox = 5
};

/**
 * Equipment context
 */
struct EquipmentContext {
    CPlayer* pPlayer{nullptr};
    _STORAGE_LIST* pItem{nullptr};
    EquipmentSlot slot{EquipmentSlot::None};
    bool bForceEquip{false};
    bool bCheckRequirements{true};
    
    EquipmentContext() = default;
    
    bool IsValid() const {
        return pPlayer != nullptr && pItem != nullptr && slot != EquipmentSlot::None;
    }
};

/**
 * Store context
 */
struct StoreContext {
    CPlayer* pPlayer{nullptr};
    StoreType storeType{StoreType::General};
    std::string itemCode;
    uint32_t quantity{1};
    uint32_t price{0};
    bool bBuyOperation{true};
    
    StoreContext() = default;
    
    bool IsValid() const {
        return pPlayer != nullptr && !itemCode.empty() && quantity > 0;
    }
};

/**
 * Special item context
 */
struct SpecialItemContext {
    CPlayer* pPlayer{nullptr};
    SpecialItemType itemType{SpecialItemType::GoldenBox};
    std::string itemCode;
    CMapData* pMap{nullptr};
    std::array<float, 3> position{0.0f, 0.0f, 0.0f};
    uint16_t layerIndex{0};
    uint32_t overlapCount{1};
    uint32_t itemCount{1};
    uint8_t createType{0};
    
    SpecialItemContext() = default;
    
    bool IsValid() const {
        return pPlayer != nullptr && !itemCode.empty();
    }
};

/**
 * Equipment operation details
 */
struct EquipmentOperationDetails {
    EquipmentResult result{EquipmentResult::Success};
    std::string errorMessage;
    EquipmentContext context;
    _STORAGE_LIST* pPreviousItem{nullptr};
    std::chrono::milliseconds executionTime{0};
    
    EquipmentOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == EquipmentResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case EquipmentResult::Success: return "Success";
            case EquipmentResult::Failure: return "General failure";
            case EquipmentResult::InvalidItem: return "Invalid item";
            case EquipmentResult::InvalidSlot: return "Invalid slot";
            case EquipmentResult::RequirementNotMet: return "Requirement not met";
            case EquipmentResult::AlreadyEquipped: return "Already equipped";
            case EquipmentResult::CannotUnequip: return "Cannot unequip";
            case EquipmentResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Store operation details
 */
struct StoreOperationDetails {
    StoreResult result{StoreResult::Success};
    std::string errorMessage;
    StoreContext context;
    uint32_t totalCost{0};
    std::chrono::milliseconds executionTime{0};
    
    StoreOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == StoreResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case StoreResult::Success: return "Success";
            case StoreResult::Failure: return "General failure";
            case StoreResult::InvalidStore: return "Invalid store";
            case StoreResult::ItemNotAvailable: return "Item not available";
            case StoreResult::InsufficientFunds: return "Insufficient funds";
            case StoreResult::InventoryFull: return "Inventory full";
            case StoreResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Special item operation details
 */
struct SpecialItemOperationDetails {
    SpecialItemResult result{SpecialItemResult::Success};
    std::string errorMessage;
    SpecialItemContext context;
    std::vector<_STORAGE_LIST*> rewardItems;
    std::chrono::milliseconds executionTime{0};
    
    SpecialItemOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == SpecialItemResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case SpecialItemResult::Success: return "Success";
            case SpecialItemResult::Failure: return "General failure";
            case SpecialItemResult::InvalidBox: return "Invalid box";
            case SpecialItemResult::BoxEmpty: return "Box empty";
            case SpecialItemResult::CannotOpen: return "Cannot open";
            case SpecialItemResult::RewardFailed: return "Reward failed";
            case SpecialItemResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Equipment statistics
 */
struct EquipmentStats {
    std::atomic<uint64_t> totalEquipOperations{0};
    std::atomic<uint64_t> successfulEquips{0};
    std::atomic<uint64_t> failedEquips{0};
    std::atomic<uint64_t> storeTransactions{0};
    std::atomic<uint64_t> specialItemsOpened{0};
    std::atomic<uint64_t> totalRevenue{0};
    std::chrono::system_clock::time_point lastOperation;
    
    EquipmentStats() : lastOperation(std::chrono::system_clock::now()) {}
    
    void RecordEquipment(bool success) {
        totalEquipOperations++;
        if (success) {
            successfulEquips++;
        } else {
            failedEquips++;
        }
        lastOperation = std::chrono::system_clock::now();
    }
    
    void RecordStoreTransaction(uint32_t amount) {
        storeTransactions++;
        totalRevenue += amount;
        lastOperation = std::chrono::system_clock::now();
    }
    
    double GetEquipSuccessRate() const {
        uint64_t total = totalEquipOperations.load();
        return total > 0 ? static_cast<double>(successfulEquips.load()) / total * 100.0 : 0.0;
    }
};

/**
 * Modern Item Equipment and Store System
 * Refactored from legacy decompiled C functions
 */
class CItemEquipmentSystem {
public:
    /**
     * Constructor
     */
    CItemEquipmentSystem();
    
    /**
     * Destructor
     */
    virtual ~CItemEquipmentSystem();
    
    /**
     * Initialize equipment system
     * @return true if successful
     */
    bool Initialize();
    
    /**
     * Shutdown equipment system
     */
    void Shutdown();
    
    /**
     * Equip item to player
     * Refactored from: CEquipItemSFAgent functions
     * @param context Equipment context
     * @return Equipment operation result
     */
    EquipmentOperationDetails EquipItem(const EquipmentContext& context);
    
    /**
     * Unequip item from player
     * @param context Equipment context
     * @return Equipment operation result
     */
    EquipmentOperationDetails UnequipItem(const EquipmentContext& context);
    
    /**
     * Process store transaction
     * Refactored from: CItemStore functions
     * @param context Store context
     * @return Store operation result
     */
    StoreOperationDetails ProcessStoreTransaction(const StoreContext& context);
    
    /**
     * Open special item box
     * Refactored from: _golden_box_item and related functions
     * @param context Special item context
     * @return Special item operation result
     */
    SpecialItemOperationDetails OpenSpecialItem(const SpecialItemContext& context);
    
    /**
     * Add Novus item to world
     * Refactored from: AddNovusItemCLuaLootingMgrQEAA_NPEBDPEAVCMapDataGP_140404EE0.c
     * @param pLootingMgr Looting manager
     * @param itemCode Item code
     * @param pMap Map data
     * @param layerIndex Layer index
     * @param position Position
     * @param lootRange Loot range
     * @param overlapCount Overlap count
     * @param itemCount Item count
     * @param createType Create type
     * @return true if successful
     */
    bool AddNovusItem(CLuaLootingMgr* pLootingMgr, const std::string& itemCode, CMapData* pMap,
                     uint16_t layerIndex, const std::array<float, 3>& position, uint16_t lootRange,
                     uint32_t overlapCount, uint32_t itemCount, uint8_t createType);
    
    /**
     * Create item box with data
     * Refactored from: _itembox_create_setdata functions
     * @param pItem Item to create box for
     * @param pOwner Owner player
     * @param pThrower Thrower (optional)
     * @param createCode Create code
     * @param bParty Party flag
     * @param partyBossSerial Party boss serial
     * @return Created item box data or nullptr
     */
    _itembox_create_setdata* CreateItemBoxData(_STORAGE_LIST* pItem, CPlayer* pOwner, CPlayer* pThrower,
                                              uint8_t createCode, bool bParty, uint32_t partyBossSerial);
    
    /**
     * Validate equipment context
     * @param context Context to validate
     * @return true if valid
     */
    bool ValidateEquipmentContext(const EquipmentContext& context);
    
    /**
     * Validate store context
     * @param context Context to validate
     * @return true if valid
     */
    bool ValidateStoreContext(const StoreContext& context);
    
    /**
     * Validate special item context
     * @param context Context to validate
     * @return true if valid
     */
    bool ValidateSpecialItemContext(const SpecialItemContext& context);
    
    /**
     * Get equipment statistics
     * @return Current statistics
     */
    const EquipmentStats& GetStatistics() const { return m_stats; }
    
    /**
     * Reset statistics
     */
    void ResetStatistics();
    
    /**
     * Set equipment callback
     * @param callback Equipment callback function
     */
    void SetEquipmentCallback(std::function<void(const EquipmentOperationDetails&)> callback);
    
    /**
     * Enable/disable detailed logging
     * @param bEnable Enable flag
     */
    void SetDetailedLogging(bool bEnable) { m_bDetailedLogging = bEnable; }

protected:
    /**
     * Check equipment requirements
     * @param pPlayer Player
     * @param pItem Item to check
     * @param slot Equipment slot
     * @return true if requirements are met
     */
    virtual bool CheckEquipmentRequirements(CPlayer* pPlayer, _STORAGE_LIST* pItem, EquipmentSlot slot);
    
    /**
     * Get equipment slot for item
     * @param pItem Item to check
     * @return Equipment slot
     */
    virtual EquipmentSlot GetEquipmentSlot(_STORAGE_LIST* pItem);
    
    /**
     * Process store buy operation
     * @param context Store context
     * @return true if successful
     */
    virtual bool ProcessStoreBuy(const StoreContext& context);
    
    /**
     * Process store sell operation
     * @param context Store context
     * @return true if successful
     */
    virtual bool ProcessStoreSell(const StoreContext& context);
    
    /**
     * Generate special item rewards
     * @param context Special item context
     * @return List of reward items
     */
    virtual std::vector<_STORAGE_LIST*> GenerateSpecialItemRewards(const SpecialItemContext& context);
    
    /**
     * Log equipment operation
     * @param details Operation details
     */
    virtual void LogEquipmentOperation(const EquipmentOperationDetails& details);

private:
    EquipmentStats m_stats;
    std::function<void(const EquipmentOperationDetails&)> m_equipmentCallback;
    bool m_bDetailedLogging{false};
    bool m_bInitialized{false};
    mutable std::mutex m_statsMutex;
    
    /**
     * Create equipment result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete equipment result
     */
    EquipmentOperationDetails CreateEquipmentResult(EquipmentResult result, 
                                                   std::chrono::high_resolution_clock::time_point startTime,
                                                   const std::string& errorMessage = "");
    
    /**
     * Create store result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete store result
     */
    StoreOperationDetails CreateStoreResult(StoreResult result, 
                                           std::chrono::high_resolution_clock::time_point startTime,
                                           const std::string& errorMessage = "");
    
    /**
     * Create special item result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete special item result
     */
    SpecialItemOperationDetails CreateSpecialItemResult(SpecialItemResult result, 
                                                       std::chrono::high_resolution_clock::time_point startTime,
                                                       const std::string& errorMessage = "");
    
    // Disable copy constructor and assignment operator
    CItemEquipmentSystem(const CItemEquipmentSystem&) = delete;
    CItemEquipmentSystem& operator=(const CItemEquipmentSystem&) = delete;
};

/**
 * Legacy compatibility functions
 * Maintain exact signatures for backward compatibility
 */
namespace LegacyCompatibility {
    /**
     * Legacy equipment agent constructor wrapper
     * @param pAgent Equipment agent
     */
    void CEquipItemSFAgent_Constructor_Legacy(void* pAgent);
    
    /**
     * Legacy item store constructor wrapper
     * @param pStore Item store
     */
    void CItemStore_Constructor_Legacy(void* pStore);
    
    /**
     * Legacy golden box item constructor wrapper
     * @param pGoldenBox Golden box item
     */
    void GoldenBoxItem_Constructor_Legacy(void* pGoldenBox);
    
    /**
     * Legacy item box create setdata constructor wrapper
     * @param pItemBoxData Item box create setdata
     */
    void ItemBoxCreateSetData_Constructor_Legacy(void* pItemBoxData);
    
    /**
     * Legacy add Novus item wrapper
     * @param pLootingMgr Looting manager
     * @param itemCode Item code
     * @param pMap Map data
     * @param layerIndex Layer index
     * @param position Position
     * @param lootRange Loot range
     * @param overlapCount Overlap count
     * @param itemCount Item count
     * @param createType Create type
     * @return 1 if successful, 0 if failed
     */
    char AddNovusItem_Legacy(CLuaLootingMgr* pLootingMgr, const char* itemCode, CMapData* pMap,
                            uint16_t layerIndex, float* position, uint16_t lootRange,
                            uint32_t overlapCount, uint32_t itemCount, uint8_t createType);
}

} // namespace Items
} // namespace NexusProtection
