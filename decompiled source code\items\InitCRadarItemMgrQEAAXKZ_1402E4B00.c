/*
 * Function: ?Init@CRadarItemMgr@@QEAAXK@Z
 * Address: 0x1402E4B00
 */

void __fastcall CRadarItemMgr::Init(CRadarItemMgr *this, unsigned int dwDelayTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRadarItemMgr *v5; // [sp+30h] [bp+8h]@1
  unsigned int v6; // [sp+38h] [bp+10h]@1

  v6 = dwDelayTime;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwDelayTime )
  {
    v5->m_bUse = 1;
    v5->m_bPlayerEnd = 1;
    v5->m_bMonEnd = 1;
    v5->m_dwStartTime = timeGetTime();
    v5->m_dwDelayTime = v6;
  }
  else
  {
    v5->m_bUse = 0;
    v5->m_bPlayerEnd = 0;
    v5->m_bMonEnd = 0;
    v5->m_dwStartTime = 0;
    v5->m_dwDelayTime = 0;
  }
  v5->m_pMaster = 0i64;
  memset_0(v5->m_strRadarCode, 0, 0x40ui64);
  v5->m_pDestMap = 0i64;
  v5->m_dwDurTime = 0;
  v5->m_nPlayerNum = 0;
  v5->m_nMonNum = 0;
  CRadarItemMgr::ResetFlags(v5);
  CRadarItemMgr::ResetUpdate(v5);
  _detected_char_list::init(&v5->m_RadarResult);
}
