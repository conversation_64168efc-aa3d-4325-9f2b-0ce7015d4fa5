/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAUCHEAT_COMMAND@@PEAU1@V?$allocator@UCHEAT_COMMAND@@@std@@@stdext@@YAPEAUCHEAT_COMMAND@@PEAU1@00AEAV?$allocator@UCHEAT_COMMAND@@@std@@@Z
 * Address: 0x140009D31
 */

CHEAT_COMMAND *__fastcall stdext::_Unchecked_uninitialized_move<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(CHEAT_COMMAND *_First, CHEAT_COMMAND *_Last, CHEAT_COMMAND *_Dest, std::allocator<CHEAT_COMMAND> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(
           _First,
           _Last,
           _Dest,
           _<PERSON>);
}
