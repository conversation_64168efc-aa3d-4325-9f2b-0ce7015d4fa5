/*
 * Function: ?SF_TransDestHP@CPlayer@@UEAA_NPEAVCCharacter@@MAEAE@Z
 * Address: 0x14009FD50
 */

char __fastcall CPlayer::SF_TransDestHP(CPlayer *this, CCharacter *pDstObj, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@14
  int v8; // eax@25
  __int64 v9; // [sp+0h] [bp-58h]@1
  CCharacter *v10; // [sp+20h] [bp-38h]@12
  CCharacter *v11; // [sp+28h] [bp-30h]@18
  int v12; // [sp+30h] [bp-28h]@22
  int v13; // [sp+34h] [bp-24h]@22
  int v14; // [sp+38h] [bp-20h]@22
  int v15; // [sp+3Ch] [bp-1Ch]@22
  int v16; // [sp+40h] [bp-18h]@25
  char v17; // [sp+44h] [bp-14h]@7
  int v18; // [sp+48h] [bp-10h]@14
  CPlayer *v19; // [sp+60h] [bp+8h]@1
  CCharacter *v20; // [sp+68h] [bp+10h]@1
  char *v21; // [sp+78h] [bp+20h]@1

  v21 = byRet;
  v20 = pDstObj;
  v19 = this;
  v4 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (CPlayer *)pDstObj == v19 || pDstObj->m_ObjID.m_byKind )
    return 0;
  v17 = pDstObj->m_ObjID.m_byID;
  if ( v17 )
  {
    if ( v17 == 1 )
    {
      v11 = pDstObj;
      if ( *(_DWORD *)(*(_QWORD *)&pDstObj[1].m_fAbsPos[1] + 272i64) == 1 )
        return 0;
    }
    else if ( (unsigned __int8)v17 <= 2u || (unsigned __int8)v17 > 4u )
    {
      return 0;
    }
  }
  else
  {
    v10 = pDstObj;
    if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))pDstObj->vfptr->IsInTown)(pDstObj) )
    {
      *v21 = 18;
      return 0;
    }
    v18 = CPlayerDB::GetRaceCode(&v19->m_Param);
    v7 = CPlayerDB::GetRaceCode((CPlayerDB *)&v10[1].m_fOldPos[2]);
    if ( v18 == v7 )
      return 0;
  }
  v12 = ((int (__fastcall *)(CCharacter *))v20->vfptr->GetHP)(v20);
  v13 = ((int (__fastcall *)(CCharacter *))v20->vfptr->GetMaxHP)(v20);
  v14 = v13 / 10;
  v15 = 0;
  if ( v12 > v13 / 10 )
  {
    v15 = v12 - v14;
    if ( (float)(v12 - v14) > fEffectValue )
      v15 = (signed int)ffloor(fEffectValue);
  }
  v8 = ((int (__fastcall *)(CPlayer *, unsigned __int64))v19->vfptr->GetHP)(v19, (unsigned __int64)v13 >> 32);
  v16 = v15 + v8;
  ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v19->vfptr->SetHP)(v19, (unsigned int)(v15 + v8), 0i64);
  (*(void (__fastcall **)(CPlayer *))&v19->vfptr->gap8[72])(v19);
  ((void (__fastcall *)(CCharacter *, CPlayer *, _QWORD))v20->vfptr->RobbedHP)(v20, v19, (unsigned int)v15);
  return 1;
}
