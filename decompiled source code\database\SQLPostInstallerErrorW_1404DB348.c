/*
 * Function: SQLPostInstallerErrorW
 * Address: 0x1404DB348
 */

__int16 __fastcall SQLPostInstallerErrorW(unsigned int dwErrorCode, const unsigned __int16 *lpszErrorMsg)
{
  unsigned int v2; // edi@1
  const unsigned __int16 *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  __int16 result; // ax@2

  v2 = dwErrorCode;
  v3 = lpszErrorMsg;
  v4 = ODBC___GetSetupProc("SQLPostInstallerErrorW");
  if ( v4 )
    result = ((int (__fastcall *)(_QWORD, const unsigned __int16 *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
