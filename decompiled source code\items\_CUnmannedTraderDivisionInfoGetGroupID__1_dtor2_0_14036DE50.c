/*
 * Function: _CUnmannedTraderDivisionInfo::GetGroupID_::_1_::dtor$2_0
 * Address: 0x14036DE50
 */

void __fastcall CUnmannedTraderDivisionInfo::GetGroupID_::_1_::dtor_2_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>((std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)(a2 + 120));
}
