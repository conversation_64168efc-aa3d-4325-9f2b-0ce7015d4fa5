/*
 * Function: ??0_equip_up_item_lv_limit_zocl@@QEAA@XZ
 * Address: 0x1400F06F0
 */

void __fastcall _equip_up_item_lv_limit_zocl::_equip_up_item_lv_limit_zocl(_equip_up_item_lv_limit_zocl *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _equip_up_item_lv_limit_zocl *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 8ui64);
}
