/*
 * Function: ?CiphertextLength@?$DL_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x140456570
 */

__int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::CiphertextLength(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *this, unsigned __int64 plaintextLength)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@4
  __int64 v5; // rax@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v6; // rax@6
  __int64 v7; // rdx@6
  int v8; // eax@6
  __int64 v10; // [sp+0h] [bp-48h]@1
  __int64 v11; // [sp+20h] [bp-28h]@4
  __int64 v12; // [sp+28h] [bp-20h]@4
  __int64 v13; // [sp+30h] [bp-18h]@5
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v14; // [sp+38h] [bp-10h]@6
  CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *v15; // [sp+50h] [bp+8h]@1
  unsigned __int64 v16; // [sp+58h] [bp+10h]@1

  v16 = plaintextLength;
  v15 = this;
  v2 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  LODWORD(v4) = ((int (__fastcall *)(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *))v15->vfptr[1].FixedCiphertextLength)(v15);
  v12 = v4;
  LODWORD(v5) = (*(int (__fastcall **)(__int64, unsigned __int64))(*(_QWORD *)v4 + 16i64))(v4, v16);
  v11 = v5;
  if ( v5 )
  {
    v6 = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *)&v15->vfptr);
    v14 = v6;
    LOBYTE(v7) = 1;
    v8 = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *, __int64))v6->vfptr[12].__vecDelDtor)(
           v6,
           v7);
    v13 = v11 + (unsigned int)v8;
  }
  else
  {
    v13 = 0i64;
  }
  return v13;
}
