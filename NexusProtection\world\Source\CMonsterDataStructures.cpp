/**
 * @file CMonsterDataStructures.cpp
 * @brief Monster Data Structures Implementation
 * 
 * Provides essential data structures for monster state management,
 * emotion presentation, damage tolerance, and Lua signal reaction systems.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "../Headers/CMonsterDataStructures.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <fstream>
#include <cmath>
#include <queue>

namespace NexusProtection::World {

    // MonsterStateData implementation
    MonsterStateData::MonsterStateData() {
        Initialize();
    }

    void MonsterStateData::Initialize() {
        currentState = MonsterState::Idle;
        previousState = MonsterState::Idle;
        nextState = MonsterState::Idle;
        
        stateStartTime = std::chrono::steady_clock::now();
        lastStateChange = stateStartTime;
        stateDuration = 0;
        stateTimeout = 0;
        
        isTransitioning = false;
        canInterrupt = true;
        isLooping = false;
        hasTimeout = false;
        
        std::fill(std::begin(stateData), std::end(stateData), 0);
        std::fill(std::begin(stateFloatData), std::end(stateFloatData), 0.0f);
        statePointer = nullptr;
        
        animationId = 0;
        effectId = 0;
        soundId = 0;
    }

    void MonsterStateData::Reset() {
        Initialize();
    }

    bool MonsterStateData::Update(float deltaTime) {
        if (!IsValid()) {
            return false;
        }
        
        // Check for timeout
        if (hasTimeout && HasExpired()) {
            if (nextState != currentState) {
                return TransitionTo(nextState);
            }
        }
        
        // Update timing
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - stateStartTime).count();
        
        if (stateDuration > 0 && elapsed >= stateDuration) {
            if (isLooping) {
                stateStartTime = now; // Reset for looping
            } else if (nextState != currentState) {
                return TransitionTo(nextState);
            }
        }
        
        return true;
    }

    bool MonsterStateData::CanTransitionTo(MonsterState newState) const {
        if (isTransitioning && !canInterrupt) {
            return false;
        }
        
        // Add state-specific transition rules here
        switch (currentState) {
            case MonsterState::Dead:
                return newState == MonsterState::Spawning; // Only allow respawn
            case MonsterState::Stunned:
                return canInterrupt; // Can only transition if interruptible
            default:
                return true; // Most states allow transitions
        }
    }

    bool MonsterStateData::TransitionTo(MonsterState newState) {
        if (!CanTransitionTo(newState)) {
            return false;
        }
        
        previousState = currentState;
        currentState = newState;
        lastStateChange = std::chrono::steady_clock::now();
        stateStartTime = lastStateChange;
        
        isTransitioning = false;
        
        return true;
    }

    void MonsterStateData::ForceState(MonsterState newState) {
        previousState = currentState;
        currentState = newState;
        lastStateChange = std::chrono::steady_clock::now();
        stateStartTime = lastStateChange;
        
        isTransitioning = false;
    }

    uint32_t MonsterStateData::GetElapsedTime() const {
        auto now = std::chrono::steady_clock::now();
        return static_cast<uint32_t>(
            std::chrono::duration_cast<std::chrono::milliseconds>(now - stateStartTime).count()
        );
    }

    uint32_t MonsterStateData::GetRemainingTime() const {
        if (stateDuration == 0) {
            return 0;
        }
        
        uint32_t elapsed = GetElapsedTime();
        return (elapsed < stateDuration) ? (stateDuration - elapsed) : 0;
    }

    bool MonsterStateData::HasExpired() const {
        if (!hasTimeout || stateTimeout == 0) {
            return false;
        }
        
        return GetElapsedTime() >= stateTimeout;
    }

    bool MonsterStateData::IsValid() const {
        return currentState != MonsterState::Dead || previousState == MonsterState::Dead;
    }

    std::string MonsterStateData::ToString() const {
        std::ostringstream oss;
        oss << "MonsterStateData{";
        oss << "Current:" << GetStateString();
        oss << ", Previous:" << MonsterDataUtils::MonsterStateToString(previousState);
        oss << ", Elapsed:" << GetElapsedTime() << "ms";
        oss << ", Duration:" << stateDuration << "ms";
        oss << ", Transitioning:" << (isTransitioning ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    std::string MonsterStateData::GetStateString() const {
        return MonsterDataUtils::MonsterStateToString(currentState);
    }

    // EmotionPresentationChecker implementation
    EmotionPresentationChecker::EmotionPresentationChecker() {
        std::cout << "[DEBUG] EmotionPresentationChecker created" << std::endl;
    }

    EmotionPresentationChecker::~EmotionPresentationChecker() {
        Shutdown();
        std::cout << "[DEBUG] EmotionPresentationChecker destroyed" << std::endl;
    }

    bool EmotionPresentationChecker::Initialize() {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        try {
            m_emotions.clear();
            m_defaultDuration = EMOTION_DURATION_MS;
            m_intensityThreshold = 0.1f;
            
            std::cout << "[INFO] EmotionPresentationChecker initialized successfully" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[ERROR] EmotionPresentationChecker initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void EmotionPresentationChecker::Shutdown() {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        m_emotions.clear();
        
        std::cout << "[INFO] EmotionPresentationChecker shutdown complete" << std::endl;
    }

    void EmotionPresentationChecker::ReSet(EmotionPresentationChecker* pChecker) {
        if (pChecker) {
            std::lock_guard<std::mutex> lock(pChecker->m_emotionMutex);

            // Reset legacy compatibility members
            pChecker->m_bIsSet = false;
            pChecker->m_pTarget = nullptr;
            pChecker->m_byType = 0;
            pChecker->m_wIndex = 0;
            pChecker->m_wRandIndex = 0;

            // Clear all emotions
            pChecker->ClearEmotions();

            std::cout << "[DEBUG] EmotionPresentationChecker reset" << std::endl;
        }
    }

    void EmotionPresentationChecker::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        UpdateEmotions(deltaTime);
        CleanupExpiredEmotions();
    }

    bool EmotionPresentationChecker::SetEmotion(MonsterEmotion emotion, float intensity) {
        return AddEmotion(emotion, intensity, m_defaultDuration);
    }

    bool EmotionPresentationChecker::AddEmotion(MonsterEmotion emotion, float intensity, uint32_t duration) {
        if (intensity < 0.0f || intensity > EMOTION_INTENSITY_MAX) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        if (m_emotions.size() >= MAX_EMOTIONS) {
            return false;
        }
        
        // Check if emotion already exists
        EmotionEntry* existing = FindEmotion(emotion);
        if (existing) {
            existing->intensity = std::max(existing->intensity, intensity);
            existing->duration = duration;
            existing->startTime = std::chrono::steady_clock::now();
            existing->isActive = true;
        } else {
            EmotionEntry entry;
            entry.emotion = emotion;
            entry.intensity = intensity;
            entry.duration = duration;
            entry.startTime = std::chrono::steady_clock::now();
            entry.isActive = true;
            m_emotions.push_back(entry);
        }
        
        return true;
    }

    bool EmotionPresentationChecker::RemoveEmotion(MonsterEmotion emotion) {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        auto it = std::remove_if(m_emotions.begin(), m_emotions.end(),
            [emotion](const EmotionEntry& entry) { return entry.emotion == emotion; });
        
        if (it != m_emotions.end()) {
            m_emotions.erase(it, m_emotions.end());
            return true;
        }
        
        return false;
    }

    void EmotionPresentationChecker::ClearEmotions() {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        m_emotions.clear();
    }

    MonsterEmotion EmotionPresentationChecker::GetPrimaryEmotion() const {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        if (m_emotions.empty()) {
            return MonsterEmotion::Neutral;
        }
        
        // Find emotion with highest priority (intensity * time factor)
        const EmotionEntry* primary = nullptr;
        float highestPriority = 0.0f;
        
        for (const auto& entry : m_emotions) {
            if (entry.isActive && !entry.IsExpired()) {
                float priority = CalculateEmotionPriority(entry);
                if (priority > highestPriority) {
                    highestPriority = priority;
                    primary = &entry;
                }
            }
        }
        
        return primary ? primary->emotion : MonsterEmotion::Neutral;
    }

    float EmotionPresentationChecker::GetEmotionIntensity(MonsterEmotion emotion) const {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        for (const auto& entry : m_emotions) {
            if (entry.emotion == emotion && entry.isActive && !entry.IsExpired()) {
                return entry.GetDecayedIntensity();
            }
        }
        
        return 0.0f;
    }

    bool EmotionPresentationChecker::HasEmotion(MonsterEmotion emotion) const {
        return GetEmotionIntensity(emotion) > m_intensityThreshold;
    }

    std::vector<MonsterEmotion> EmotionPresentationChecker::GetActiveEmotions() const {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        std::vector<MonsterEmotion> activeEmotions;
        for (const auto& entry : m_emotions) {
            if (entry.isActive && !entry.IsExpired() && entry.GetDecayedIntensity() > m_intensityThreshold) {
                activeEmotions.push_back(entry.emotion);
            }
        }
        
        return activeEmotions;
    }

    bool EmotionPresentationChecker::ShouldPresentEmotion(MonsterEmotion emotion) const {
        float intensity = GetEmotionIntensity(emotion);
        return intensity > m_intensityThreshold;
    }

    uint32_t EmotionPresentationChecker::GetEmotionAnimationId(MonsterEmotion emotion) const {
        // Return animation ID based on emotion type
        switch (emotion) {
            case MonsterEmotion::Angry: return 1001;
            case MonsterEmotion::Happy: return 1002;
            case MonsterEmotion::Sad: return 1003;
            case MonsterEmotion::Excited: return 1004;
            case MonsterEmotion::Fearful: return 1005;
            case MonsterEmotion::Confused: return 1006;
            case MonsterEmotion::Aggressive: return 1007;
            case MonsterEmotion::Defensive: return 1008;
            case MonsterEmotion::Curious: return 1009;
            default: return 1000; // Neutral
        }
    }

    uint32_t EmotionPresentationChecker::GetEmotionEffectId(MonsterEmotion emotion) const {
        // Return effect ID based on emotion type
        switch (emotion) {
            case MonsterEmotion::Angry: return 2001;
            case MonsterEmotion::Happy: return 2002;
            case MonsterEmotion::Excited: return 2004;
            case MonsterEmotion::Fearful: return 2005;
            case MonsterEmotion::Aggressive: return 2007;
            default: return 0; // No effect
        }
    }

    uint32_t EmotionPresentationChecker::GetEmotionSoundId(MonsterEmotion emotion) const {
        // Return sound ID based on emotion type
        switch (emotion) {
            case MonsterEmotion::Angry: return 3001;
            case MonsterEmotion::Happy: return 3002;
            case MonsterEmotion::Sad: return 3003;
            case MonsterEmotion::Fearful: return 3005;
            case MonsterEmotion::Aggressive: return 3007;
            default: return 0; // No sound
        }
    }

    float EmotionPresentationChecker::GetDamageModifier() const {
        MonsterEmotion primary = GetPrimaryEmotion();
        float intensity = GetEmotionIntensity(primary);
        
        switch (primary) {
            case MonsterEmotion::Angry:
            case MonsterEmotion::Aggressive:
                return 1.0f + (intensity * 0.5f); // Up to 50% more damage
            case MonsterEmotion::Fearful:
            case MonsterEmotion::Sad:
                return 1.0f - (intensity * 0.3f); // Up to 30% less damage
            default:
                return 1.0f; // No modifier
        }
    }

    float EmotionPresentationChecker::GetSpeedModifier() const {
        MonsterEmotion primary = GetPrimaryEmotion();
        float intensity = GetEmotionIntensity(primary);
        
        switch (primary) {
            case MonsterEmotion::Excited:
            case MonsterEmotion::Fearful:
                return 1.0f + (intensity * 0.4f); // Up to 40% faster
            case MonsterEmotion::Sad:
            case MonsterEmotion::Confused:
                return 1.0f - (intensity * 0.3f); // Up to 30% slower
            default:
                return 1.0f; // No modifier
        }
    }

    float EmotionPresentationChecker::GetAggroModifier() const {
        MonsterEmotion primary = GetPrimaryEmotion();
        float intensity = GetEmotionIntensity(primary);
        
        switch (primary) {
            case MonsterEmotion::Angry:
            case MonsterEmotion::Aggressive:
                return 1.0f + (intensity * 0.8f); // Up to 80% more aggro
            case MonsterEmotion::Happy:
            case MonsterEmotion::Curious:
                return 1.0f - (intensity * 0.5f); // Up to 50% less aggro
            default:
                return 1.0f; // No modifier
        }
    }

    std::string EmotionPresentationChecker::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_emotionMutex);
        
        std::ostringstream oss;
        oss << "EmotionPresentationChecker{";
        oss << "Emotions:" << m_emotions.size();
        oss << ", Primary:" << MonsterDataUtils::MonsterEmotionToString(GetPrimaryEmotion());
        oss << ", Threshold:" << m_intensityThreshold;
        oss << "}";
        return oss.str();
    }

    // EmotionEntry implementation
    bool EmotionPresentationChecker::EmotionEntry::IsExpired() const {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime).count();
        return elapsed >= duration;
    }

    float EmotionPresentationChecker::EmotionEntry::GetDecayedIntensity() const {
        if (!isActive || IsExpired()) {
            return 0.0f;
        }
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - startTime).count();
        
        // Linear decay over time
        float decayFactor = 1.0f - (static_cast<float>(elapsed) / static_cast<float>(duration));
        return intensity * std::max(0.0f, decayFactor);
    }

    void EmotionPresentationChecker::UpdateEmotions(float deltaTime) {
        // Emotions are updated through their GetDecayedIntensity() method
        // This method could be used for additional emotion processing
    }

    void EmotionPresentationChecker::CleanupExpiredEmotions() {
        m_emotions.erase(
            std::remove_if(m_emotions.begin(), m_emotions.end(),
                [](const EmotionEntry& entry) { return entry.IsExpired(); }),
            m_emotions.end());
    }

    EmotionPresentationChecker::EmotionEntry* EmotionPresentationChecker::FindEmotion(MonsterEmotion emotion) {
        for (auto& entry : m_emotions) {
            if (entry.emotion == emotion) {
                return &entry;
            }
        }
        return nullptr;
    }

    float EmotionPresentationChecker::CalculateEmotionPriority(const EmotionEntry& entry) const {
        float intensity = entry.GetDecayedIntensity();
        
        // Add emotion-specific priority modifiers
        float priorityModifier = 1.0f;
        switch (entry.emotion) {
            case MonsterEmotion::Angry:
            case MonsterEmotion::Aggressive:
                priorityModifier = 1.5f; // High priority
                break;
            case MonsterEmotion::Fearful:
                priorityModifier = 1.3f; // High priority
                break;
            case MonsterEmotion::Happy:
            case MonsterEmotion::Curious:
                priorityModifier = 0.8f; // Lower priority
                break;
            default:
                priorityModifier = 1.0f;
                break;
        }
        
        return intensity * priorityModifier;
    }

    // MonsterSFContDamageTolerance implementation
    MonsterSFContDamageTolerance::MonsterSFContDamageTolerance() {
        InitializeDefaults();
        std::cout << "[DEBUG] MonsterSFContDamageTolerance created" << std::endl;
    }

    MonsterSFContDamageTolerance::~MonsterSFContDamageTolerance() {
        Shutdown();
        std::cout << "[DEBUG] MonsterSFContDamageTolerance destroyed" << std::endl;
    }

    bool MonsterSFContDamageTolerance::Initialize() {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        try {
            InitializeDefaults();

            std::cout << "[INFO] MonsterSFContDamageTolerance initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] MonsterSFContDamageTolerance initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void MonsterSFContDamageTolerance::Shutdown() {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        // Reset all tolerances
        m_damageTolerances.fill(DEFAULT_TOLERANCE);
        m_sfContainerTolerances.fill(DEFAULT_TOLERANCE);

        std::cout << "[INFO] MonsterSFContDamageTolerance shutdown complete" << std::endl;
    }

    void MonsterSFContDamageTolerance::Update(float deltaTime) {
        // Tolerance values are static, no update needed
        // This method could be used for dynamic tolerance changes
    }

    void MonsterSFContDamageTolerance::SetDamageTolerance(uint32_t damageType, float tolerance) {
        if (damageType >= MAX_DAMAGE_TYPES) {
            return;
        }

        std::lock_guard<std::mutex> lock(m_toleranceMutex);
        m_damageTolerances[damageType] = std::max(0.0f, tolerance);
    }

    float MonsterSFContDamageTolerance::GetDamageTolerance(uint32_t damageType) const {
        if (damageType >= MAX_DAMAGE_TYPES) {
            return DEFAULT_TOLERANCE;
        }

        std::lock_guard<std::mutex> lock(m_toleranceMutex);
        return m_damageTolerances[damageType];
    }

    void MonsterSFContDamageTolerance::SetToleranceLevel(uint32_t damageType, DamageToleranceLevel level) {
        float tolerance = ToleranceLevelToFloat(level);
        SetDamageTolerance(damageType, tolerance);
    }

    DamageToleranceLevel MonsterSFContDamageTolerance::GetToleranceLevel(uint32_t damageType) const {
        float tolerance = GetDamageTolerance(damageType);
        return FloatToToleranceLevel(tolerance);
    }

    void MonsterSFContDamageTolerance::SetSFContainerTolerance(uint32_t containerId, float tolerance) {
        if (containerId >= MAX_SF_CONTAINERS) {
            return;
        }

        std::lock_guard<std::mutex> lock(m_toleranceMutex);
        m_sfContainerTolerances[containerId] = std::max(0.0f, tolerance);
    }

    float MonsterSFContDamageTolerance::GetSFContainerTolerance(uint32_t containerId) const {
        if (containerId >= MAX_SF_CONTAINERS) {
            return DEFAULT_TOLERANCE;
        }

        std::lock_guard<std::mutex> lock(m_toleranceMutex);
        return m_sfContainerTolerances[containerId];
    }

    bool MonsterSFContDamageTolerance::IsSFContainerImmune(uint32_t containerId) const {
        float tolerance = GetSFContainerTolerance(containerId);
        return tolerance >= 999.0f; // Consider 999+ as immune
    }

    float MonsterSFContDamageTolerance::CalculateDamageReduction(uint32_t damageType, float baseDamage) const {
        float tolerance = GetDamageTolerance(damageType);

        if (tolerance >= 999.0f) {
            return 0.0f; // Immune
        }

        // Calculate reduction: higher tolerance = less damage taken
        float reductionFactor = 1.0f / tolerance;
        return baseDamage * std::min(1.0f, reductionFactor);
    }

    float MonsterSFContDamageTolerance::CalculateSFReduction(uint32_t containerId, float baseEffect) const {
        float tolerance = GetSFContainerTolerance(containerId);

        if (tolerance >= 999.0f) {
            return 0.0f; // Immune
        }

        // Calculate reduction: higher tolerance = less effect
        float reductionFactor = 1.0f / tolerance;
        return baseEffect * std::min(1.0f, reductionFactor);
    }

    bool MonsterSFContDamageTolerance::ShouldBlockDamage(uint32_t damageType) const {
        float tolerance = GetDamageTolerance(damageType);
        return tolerance >= 999.0f; // Block if immune
    }

    void MonsterSFContDamageTolerance::SetAllTolerances(float tolerance) {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        m_damageTolerances.fill(tolerance);
        m_sfContainerTolerances.fill(tolerance);
    }

    void MonsterSFContDamageTolerance::ResetToDefaults() {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);
        InitializeDefaults();
    }

    void MonsterSFContDamageTolerance::ApplyToleranceModifier(float modifier) {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        for (auto& tolerance : m_damageTolerances) {
            tolerance *= modifier;
        }

        for (auto& tolerance : m_sfContainerTolerances) {
            tolerance *= modifier;
        }
    }

    void MonsterSFContDamageTolerance::LoadToleranceData(const std::string& configFile) {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        std::ifstream file(configFile);
        if (!file.is_open()) {
            std::cout << "[WARNING] Could not open tolerance config file: " << configFile << std::endl;
            return;
        }

        // Simple format: type tolerance (one per line)
        std::string line;
        while (std::getline(file, line)) {
            std::istringstream iss(line);
            uint32_t type;
            float tolerance;

            if (iss >> type >> tolerance) {
                if (type < MAX_DAMAGE_TYPES) {
                    m_damageTolerances[type] = tolerance;
                }
            }
        }

        std::cout << "[INFO] Loaded tolerance data from: " << configFile << std::endl;
    }

    void MonsterSFContDamageTolerance::SaveToleranceData(const std::string& configFile) const {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        std::ofstream file(configFile);
        if (!file.is_open()) {
            std::cout << "[ERROR] Could not create tolerance config file: " << configFile << std::endl;
            return;
        }

        // Save damage tolerances
        for (size_t i = 0; i < MAX_DAMAGE_TYPES; ++i) {
            if (m_damageTolerances[i] != DEFAULT_TOLERANCE) {
                file << i << " " << m_damageTolerances[i] << std::endl;
            }
        }

        std::cout << "[INFO] Saved tolerance data to: " << configFile << std::endl;
    }

    std::string MonsterSFContDamageTolerance::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_toleranceMutex);

        std::ostringstream oss;
        oss << "MonsterSFContDamageTolerance{";

        // Count non-default tolerances
        size_t customDamageTolerances = 0;
        size_t customSFTolerances = 0;

        for (const auto& tolerance : m_damageTolerances) {
            if (tolerance != DEFAULT_TOLERANCE) {
                customDamageTolerances++;
            }
        }

        for (const auto& tolerance : m_sfContainerTolerances) {
            if (tolerance != DEFAULT_TOLERANCE) {
                customSFTolerances++;
            }
        }

        oss << "CustomDamage:" << customDamageTolerances;
        oss << ", CustomSF:" << customSFTolerances;
        oss << "}";
        return oss.str();
    }

    float MonsterSFContDamageTolerance::ToleranceLevelToFloat(DamageToleranceLevel level) const {
        switch (level) {
            case DamageToleranceLevel::VeryLow: return 0.5f;
            case DamageToleranceLevel::Low: return 0.75f;
            case DamageToleranceLevel::Normal: return 1.0f;
            case DamageToleranceLevel::High: return 1.5f;
            case DamageToleranceLevel::VeryHigh: return 2.0f;
            case DamageToleranceLevel::Immune: return 999.0f;
            default: return 1.0f;
        }
    }

    DamageToleranceLevel MonsterSFContDamageTolerance::FloatToToleranceLevel(float tolerance) const {
        if (tolerance >= 999.0f) return DamageToleranceLevel::Immune;
        if (tolerance >= 2.0f) return DamageToleranceLevel::VeryHigh;
        if (tolerance >= 1.5f) return DamageToleranceLevel::High;
        if (tolerance >= 1.0f) return DamageToleranceLevel::Normal;
        if (tolerance >= 0.75f) return DamageToleranceLevel::Low;
        return DamageToleranceLevel::VeryLow;
    }

    void MonsterSFContDamageTolerance::InitializeDefaults() {
        m_damageTolerances.fill(DEFAULT_TOLERANCE);
        m_sfContainerTolerances.fill(DEFAULT_TOLERANCE);

        // Set some example default tolerances
        // Physical damage types
        m_damageTolerances[0] = 1.0f;  // Physical
        m_damageTolerances[1] = 1.0f;  // Slash
        m_damageTolerances[2] = 1.0f;  // Pierce
        m_damageTolerances[3] = 1.0f;  // Blunt

        // Elemental damage types
        m_damageTolerances[4] = 1.0f;  // Fire
        m_damageTolerances[5] = 1.0f;  // Ice
        m_damageTolerances[6] = 1.0f;  // Lightning
        m_damageTolerances[7] = 1.0f;  // Earth

        // Special damage types
        m_damageTolerances[8] = 1.0f;  // Holy
        m_damageTolerances[9] = 1.0f;  // Dark
        m_damageTolerances[10] = 1.0f; // Poison
        m_damageTolerances[11] = 1.0f; // Disease
    }

    // CLuaSignalReActor implementation
    CLuaSignalReActor::CLuaSignalReActor() {
        std::cout << "[DEBUG] CLuaSignalReActor created" << std::endl;
    }

    CLuaSignalReActor::~CLuaSignalReActor() {
        Shutdown();
        std::cout << "[DEBUG] CLuaSignalReActor destroyed" << std::endl;
    }

    bool CLuaSignalReActor::Initialize() {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        try {
            m_signalMap.clear();
            while (!m_signalQueue.empty()) {
                m_signalQueue.pop();
            }

            m_scriptTimeoutMs = 5000;
            m_maxSignalsPerFrame = 10;

            std::cout << "[INFO] CLuaSignalReActor initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] CLuaSignalReActor initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void CLuaSignalReActor::Shutdown() {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        m_signalMap.clear();
        while (!m_signalQueue.empty()) {
            m_signalQueue.pop();
        }

        m_signalHandler = nullptr;
        m_luaCallback = nullptr;

        std::cout << "[INFO] CLuaSignalReActor shutdown complete" << std::endl;
    }

    void CLuaSignalReActor::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_signalMutex);
        ProcessSignalQueue();
    }

    bool CLuaSignalReActor::RegisterSignal(LuaSignalType signalType, const std::string& scriptFunction) {
        if (scriptFunction.empty()) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_signalMutex);

        SignalEntry entry;
        entry.signalType = signalType;
        entry.scriptFunction = scriptFunction;
        entry.isActive = true;
        entry.callCount = 0;
        entry.lastCall = std::chrono::steady_clock::now();

        m_signalMap[signalType] = entry;

        std::cout << "[INFO] Registered signal: " << SignalTypeToString(signalType)
                  << " -> " << scriptFunction << std::endl;
        return true;
    }

    bool CLuaSignalReActor::UnregisterSignal(LuaSignalType signalType) {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        auto it = m_signalMap.find(signalType);
        if (it != m_signalMap.end()) {
            std::cout << "[INFO] Unregistered signal: " << SignalTypeToString(signalType) << std::endl;
            m_signalMap.erase(it);
            return true;
        }

        return false;
    }

    void CLuaSignalReActor::ClearSignals() {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        m_signalMap.clear();
        while (!m_signalQueue.empty()) {
            m_signalQueue.pop();
        }

        std::cout << "[INFO] All signals cleared" << std::endl;
    }

    bool CLuaSignalReActor::EmitSignal(LuaSignalType signalType, const std::vector<std::string>& parameters) {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        // Check if signal is registered
        auto it = m_signalMap.find(signalType);
        if (it == m_signalMap.end() || !it->second.isActive) {
            return false;
        }

        // Add to queue for processing
        if (m_signalQueue.size() < m_maxSignalsPerFrame * 2) { // Allow some buffer
            m_signalQueue.push(std::make_pair(signalType, parameters));
            return true;
        }

        std::cout << "[WARNING] Signal queue full, dropping signal: " << SignalTypeToString(signalType) << std::endl;
        return false;
    }

    bool CLuaSignalReActor::EmitCustomSignal(const std::string& signalName, const std::vector<std::string>& parameters) {
        // For custom signals, we use OnCustomEvent type and pass the signal name as first parameter
        std::vector<std::string> customParams = {signalName};
        customParams.insert(customParams.end(), parameters.begin(), parameters.end());

        return EmitSignal(LuaSignalType::OnCustomEvent, customParams);
    }

    bool CLuaSignalReActor::LoadScript(const std::string& scriptFile) {
        std::ifstream file(scriptFile);
        if (!file.is_open()) {
            std::cout << "[ERROR] Could not open script file: " << scriptFile << std::endl;
            return false;
        }

        std::string script((std::istreambuf_iterator<char>(file)),
                          std::istreambuf_iterator<char>());

        return ExecuteScript(script);
    }

    bool CLuaSignalReActor::ExecuteScript(const std::string& script) {
        if (script.empty()) {
            return false;
        }

        // In a real implementation, this would execute Lua script
        // For now, just log the action
        std::cout << "[DEBUG] Executing Lua script (" << script.length() << " characters)" << std::endl;

        return true;
    }

    bool CLuaSignalReActor::CallLuaFunction(const std::string& functionName, const std::vector<std::string>& parameters) {
        if (functionName.empty()) {
            return false;
        }

        // In a real implementation, this would call the Lua function
        // For now, just log the action
        std::cout << "[DEBUG] Calling Lua function: " << functionName
                  << " with " << parameters.size() << " parameters" << std::endl;

        if (m_luaCallback) {
            m_luaCallback(functionName, parameters);
        }

        return true;
    }

    bool CLuaSignalReActor::IsSignalRegistered(LuaSignalType signalType) const {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        auto it = m_signalMap.find(signalType);
        return it != m_signalMap.end() && it->second.isActive;
    }

    std::vector<LuaSignalType> CLuaSignalReActor::GetRegisteredSignals() const {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        std::vector<LuaSignalType> signals;
        for (const auto& pair : m_signalMap) {
            if (pair.second.isActive) {
                signals.push_back(pair.first);
            }
        }

        return signals;
    }

    std::string CLuaSignalReActor::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_signalMutex);

        std::ostringstream oss;
        oss << "CLuaSignalReActor{";
        oss << "Signals:" << m_signalMap.size();
        oss << ", Queue:" << m_signalQueue.size();
        oss << ", Timeout:" << m_scriptTimeoutMs << "ms";
        oss << ", MaxPerFrame:" << m_maxSignalsPerFrame;
        oss << "}";
        return oss.str();
    }

    void CLuaSignalReActor::ProcessSignalQueue() {
        uint32_t processedCount = 0;

        while (!m_signalQueue.empty() && processedCount < m_maxSignalsPerFrame) {
            auto signalPair = m_signalQueue.front();
            m_signalQueue.pop();

            LuaSignalType signalType = signalPair.first;
            const std::vector<std::string>& parameters = signalPair.second;

            auto it = m_signalMap.find(signalType);
            if (it != m_signalMap.end() && it->second.isActive) {
                ExecuteSignalScript(it->second, parameters);
                it->second.callCount++;
                it->second.lastCall = std::chrono::steady_clock::now();
            }

            processedCount++;
        }
    }

    bool CLuaSignalReActor::ExecuteSignalScript(const SignalEntry& entry, const std::vector<std::string>& parameters) {
        try {
            // Call the signal handler if available
            if (m_signalHandler) {
                bool handled = m_signalHandler(entry.signalType, parameters);
                if (handled) {
                    return true;
                }
            }

            // Call the Lua function
            return CallLuaFunction(entry.scriptFunction, parameters);

        } catch (const std::exception& e) {
            std::cout << "[ERROR] Exception executing signal script: " << e.what() << std::endl;
            return false;
        }
    }

    std::string CLuaSignalReActor::SignalTypeToString(LuaSignalType signalType) const {
        return MonsterDataUtils::LuaSignalTypeToString(signalType);
    }

    // Utility functions
    namespace MonsterDataUtils {
        std::string MonsterStateToString(MonsterState state) {
            switch (state) {
                case MonsterState::Idle: return "Idle";
                case MonsterState::Moving: return "Moving";
                case MonsterState::Attacking: return "Attacking";
                case MonsterState::Defending: return "Defending";
                case MonsterState::Casting: return "Casting";
                case MonsterState::Stunned: return "Stunned";
                case MonsterState::Dead: return "Dead";
                case MonsterState::Spawning: return "Spawning";
                case MonsterState::Despawning: return "Despawning";
                case MonsterState::Sleeping: return "Sleeping";
                case MonsterState::Enraged: return "Enraged";
                case MonsterState::Fleeing: return "Fleeing";
                default: return "Unknown";
            }
        }

        std::string MonsterEmotionToString(MonsterEmotion emotion) {
            switch (emotion) {
                case MonsterEmotion::Neutral: return "Neutral";
                case MonsterEmotion::Angry: return "Angry";
                case MonsterEmotion::Happy: return "Happy";
                case MonsterEmotion::Sad: return "Sad";
                case MonsterEmotion::Excited: return "Excited";
                case MonsterEmotion::Fearful: return "Fearful";
                case MonsterEmotion::Confused: return "Confused";
                case MonsterEmotion::Aggressive: return "Aggressive";
                case MonsterEmotion::Defensive: return "Defensive";
                case MonsterEmotion::Curious: return "Curious";
                default: return "Unknown";
            }
        }

        std::string DamageToleranceLevelToString(DamageToleranceLevel level) {
            switch (level) {
                case DamageToleranceLevel::VeryLow: return "VeryLow";
                case DamageToleranceLevel::Low: return "Low";
                case DamageToleranceLevel::Normal: return "Normal";
                case DamageToleranceLevel::High: return "High";
                case DamageToleranceLevel::VeryHigh: return "VeryHigh";
                case DamageToleranceLevel::Immune: return "Immune";
                default: return "Unknown";
            }
        }

        std::string LuaSignalTypeToString(LuaSignalType signalType) {
            switch (signalType) {
                case LuaSignalType::OnSpawn: return "OnSpawn";
                case LuaSignalType::OnDeath: return "OnDeath";
                case LuaSignalType::OnDamage: return "OnDamage";
                case LuaSignalType::OnHeal: return "OnHeal";
                case LuaSignalType::OnAttack: return "OnAttack";
                case LuaSignalType::OnSkillUse: return "OnSkillUse";
                case LuaSignalType::OnStateChange: return "OnStateChange";
                case LuaSignalType::OnTargetChange: return "OnTargetChange";
                case LuaSignalType::OnEmotionChange: return "OnEmotionChange";
                case LuaSignalType::OnCustomEvent: return "OnCustomEvent";
                default: return "Unknown";
            }
        }

        MonsterState StringToMonsterState(const std::string& str) {
            if (str == "Idle") return MonsterState::Idle;
            if (str == "Moving") return MonsterState::Moving;
            if (str == "Attacking") return MonsterState::Attacking;
            if (str == "Defending") return MonsterState::Defending;
            if (str == "Casting") return MonsterState::Casting;
            if (str == "Stunned") return MonsterState::Stunned;
            if (str == "Dead") return MonsterState::Dead;
            if (str == "Spawning") return MonsterState::Spawning;
            if (str == "Despawning") return MonsterState::Despawning;
            if (str == "Sleeping") return MonsterState::Sleeping;
            if (str == "Enraged") return MonsterState::Enraged;
            if (str == "Fleeing") return MonsterState::Fleeing;
            return MonsterState::Idle;
        }

        MonsterEmotion StringToMonsterEmotion(const std::string& str) {
            if (str == "Neutral") return MonsterEmotion::Neutral;
            if (str == "Angry") return MonsterEmotion::Angry;
            if (str == "Happy") return MonsterEmotion::Happy;
            if (str == "Sad") return MonsterEmotion::Sad;
            if (str == "Excited") return MonsterEmotion::Excited;
            if (str == "Fearful") return MonsterEmotion::Fearful;
            if (str == "Confused") return MonsterEmotion::Confused;
            if (str == "Aggressive") return MonsterEmotion::Aggressive;
            if (str == "Defensive") return MonsterEmotion::Defensive;
            if (str == "Curious") return MonsterEmotion::Curious;
            return MonsterEmotion::Neutral;
        }

        DamageToleranceLevel StringToDamageToleranceLevel(const std::string& str) {
            if (str == "VeryLow") return DamageToleranceLevel::VeryLow;
            if (str == "Low") return DamageToleranceLevel::Low;
            if (str == "Normal") return DamageToleranceLevel::Normal;
            if (str == "High") return DamageToleranceLevel::High;
            if (str == "VeryHigh") return DamageToleranceLevel::VeryHigh;
            if (str == "Immune") return DamageToleranceLevel::Immune;
            return DamageToleranceLevel::Normal;
        }

        LuaSignalType StringToLuaSignalType(const std::string& str) {
            if (str == "OnSpawn") return LuaSignalType::OnSpawn;
            if (str == "OnDeath") return LuaSignalType::OnDeath;
            if (str == "OnDamage") return LuaSignalType::OnDamage;
            if (str == "OnHeal") return LuaSignalType::OnHeal;
            if (str == "OnAttack") return LuaSignalType::OnAttack;
            if (str == "OnSkillUse") return LuaSignalType::OnSkillUse;
            if (str == "OnStateChange") return LuaSignalType::OnStateChange;
            if (str == "OnTargetChange") return LuaSignalType::OnTargetChange;
            if (str == "OnEmotionChange") return LuaSignalType::OnEmotionChange;
            if (str == "OnCustomEvent") return LuaSignalType::OnCustomEvent;
            return LuaSignalType::OnSpawn;
        }

        bool IsValidState(MonsterState state) {
            return state >= MonsterState::Idle && state <= MonsterState::Fleeing;
        }

        bool IsValidEmotion(MonsterEmotion emotion) {
            return emotion >= MonsterEmotion::Neutral && emotion <= MonsterEmotion::Curious;
        }

        bool IsValidToleranceLevel(DamageToleranceLevel level) {
            return level >= DamageToleranceLevel::VeryLow && level <= DamageToleranceLevel::Immune;
        }

        bool IsValidSignalType(LuaSignalType signalType) {
            return signalType >= LuaSignalType::OnSpawn && signalType <= LuaSignalType::OnCustomEvent;
        }
    }

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {
    void MonsterStateData_Initialize(MonsterStateData_Legacy* data) {
        if (data) {
            data->currentState = 0; // Idle
            data->previousState = 0;
            std::fill(std::begin(data->stateData), std::end(data->stateData), 0);
            std::fill(std::begin(data->stateFloatData), std::end(data->stateFloatData), 0.0f);
            data->statePointer = nullptr;
            std::cout << "[DEBUG] Legacy MonsterStateData initialized" << std::endl;
        }
    }

    void MonsterStateData_Reset(MonsterStateData_Legacy* data) {
        if (data) {
            MonsterStateData_Initialize(data);
            std::cout << "[DEBUG] Legacy MonsterStateData reset" << std::endl;
        }
    }

    bool MonsterStateData_TransitionTo(MonsterStateData_Legacy* data, uint32_t newState) {
        if (data) {
            data->previousState = data->currentState;
            data->currentState = newState;
            std::cout << "[DEBUG] Legacy MonsterStateData transition to state: " << newState << std::endl;
            return true;
        }
        return false;
    }

    void EmotionPresentationChecker_Constructor(EmotionPresentationChecker_Legacy* checker) {
        if (checker) {
            checker->vfptr = nullptr;
            std::cout << "[DEBUG] Legacy EmotionPresentationChecker constructed" << std::endl;
        }
    }

    void EmotionPresentationChecker_Destructor(EmotionPresentationChecker_Legacy* checker) {
        if (checker) {
            std::cout << "[DEBUG] Legacy EmotionPresentationChecker destructed" << std::endl;
        }
    }

    bool EmotionPresentationChecker_SetEmotion(EmotionPresentationChecker_Legacy* checker, uint32_t emotion, float intensity) {
        if (checker) {
            std::cout << "[DEBUG] Legacy EmotionPresentationChecker set emotion: "
                      << emotion << " intensity: " << intensity << std::endl;
            return true;
        }
        return false;
    }

    void MonsterSFContDamageTolerance_Constructor(MonsterSFContDamageTolerance_Legacy* tolerance) {
        if (tolerance) {
            tolerance->vfptr = nullptr;
            std::fill(std::begin(tolerance->damageTolerances), std::end(tolerance->damageTolerances), 1.0f);
            std::fill(std::begin(tolerance->sfContainerTolerances), std::end(tolerance->sfContainerTolerances), 1.0f);
            std::cout << "[DEBUG] Legacy MonsterSFContDamageTolerance constructed" << std::endl;
        }
    }

    void MonsterSFContDamageTolerance_Destructor(MonsterSFContDamageTolerance_Legacy* tolerance) {
        if (tolerance) {
            std::cout << "[DEBUG] Legacy MonsterSFContDamageTolerance destructed" << std::endl;
        }
    }

    void MonsterSFContDamageTolerance_SetTolerance(MonsterSFContDamageTolerance_Legacy* tolerance, uint32_t type, float value) {
        if (tolerance && type < 32) {
            tolerance->damageTolerances[type] = value;
            std::cout << "[DEBUG] Legacy MonsterSFContDamageTolerance set tolerance: "
                      << type << " = " << value << std::endl;
        }
    }

    void CLuaSignalReActor_Constructor(CLuaSignalReActor_Legacy* reactor) {
        if (reactor) {
            reactor->vfptr = nullptr;
            std::cout << "[DEBUG] Legacy CLuaSignalReActor constructed" << std::endl;
        }
    }

    void CLuaSignalReActor_Destructor(CLuaSignalReActor_Legacy* reactor) {
        if (reactor) {
            std::cout << "[DEBUG] Legacy CLuaSignalReActor destructed" << std::endl;
        }
    }

    bool CLuaSignalReActor_EmitSignal(CLuaSignalReActor_Legacy* reactor, uint32_t signalType) {
        if (reactor) {
            std::cout << "[DEBUG] Legacy CLuaSignalReActor emit signal: " << signalType << std::endl;
            return true;
        }
        return false;
    }
}
