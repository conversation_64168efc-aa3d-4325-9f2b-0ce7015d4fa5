/*
 * CAuthenticationManager.cpp - Modern Authentication Management Implementation
 * Refactored from decompiled C authentication functions
 * Provides comprehensive user authentication, login management, and security
 */

#include "../Headers/CAuthenticationManager.h"
#include "../../common/Headers/Logger.h"
#include "../../system/Headers/CMainThread.h"
#include "../../database/Headers/CUserDB.h"
#include "../../billing/Headers/CBilling.h"
#include "../../billing/Headers/CBillingManager.h"

#include <algorithm>
#include <chrono>
#include <stdexcept>
#include <random>
#include <sstream>
#include <iomanip>

// External references to legacy systems
extern "C" {
    // Legacy function declarations
    class CNationSettingManager;
    struct _open_world_request_wrac;
    
    extern CNationSettingManager* CTSingleton_CNationSettingManager_Instance();
    extern void CNationSettingManager_SendCashDBDSNRequest(CNationSettingManager* pManager);
    extern uint32_t GetIPAddress();
    extern uint32_t inet_addr(const char* cp);
    extern char* inet_ntoa(struct in_addr in);
    extern int GetPrivateProfileStringA(const char* lpAppName, const char* lpKeyName, const char* lpDefault,
                                       char* lpReturnedString, uint32_t nSize, const char* lpFileName);
    extern int strcmp_0(const char* str1, const char* str2);
    extern void strcpy_0(char* dest, const char* src);
    extern void memcpy_s(void* dest, size_t destSize, const void* src, size_t count);
    extern void CNetProcess_LoadSendMsg(void* pNetProcess, int param1, void* pType, void* pData, uint16_t size);
    extern uint16_t _open_world_request_wrac_size(_open_world_request_wrac* pRequest);
    extern uint8_t g_cbHashVerify[32];
    extern void* unk_1414F2090;
}

namespace NexusProtection {
namespace Authentication {

/**
 * Constructor
 */
CAuthenticationManager::CAuthenticationManager() 
    : m_bDetailedLogging(false), m_bInitialized(false) {
    
    Logger::Debug("CAuthenticationManager::CAuthenticationManager - Authentication manager created");
}

/**
 * Destructor
 */
CAuthenticationManager::~CAuthenticationManager() {
    try {
        Shutdown();
        Logger::Debug("CAuthenticationManager::~CAuthenticationManager - Authentication manager destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Initialize authentication system
 */
bool CAuthenticationManager::Initialize() {
    try {
        if (m_bInitialized) {
            Logger::Warning("CAuthenticationManager::Initialize - Already initialized");
            return true;
        }
        
        // Initialize session management
        m_activeSessions.clear();
        m_bannedAccounts.clear();
        
        // Reset statistics
        ResetStatistics();
        
        m_bInitialized = true;
        Logger::Info("CAuthenticationManager::Initialize - Authentication system initialized");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::Initialize - Exception: %s", e.what());
        return false;
    }
}

/**
 * Shutdown authentication system
 */
void CAuthenticationManager::Shutdown() {
    try {
        if (!m_bInitialized) {
            return;
        }
        
        // Clear all active sessions
        {
            std::lock_guard<std::mutex> lock(m_sessionMutex);
            m_activeSessions.clear();
        }
        
        // Clear banned accounts
        {
            std::lock_guard<std::mutex> lock(m_banMutex);
            m_bannedAccounts.clear();
        }
        
        m_bInitialized = false;
        Logger::Info("CAuthenticationManager::Shutdown - Authentication system shutdown");
        
    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::Shutdown - Exception: %s", e.what());
    }
}

/**
 * Account server login
 * Refactored from: AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c
 */
AuthenticationDetails CAuthenticationManager::AccountServerLogin(CMainThread* pMainThread) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    if (!pMainThread) {
        return CreateResult(AuthenticationResult::SystemError, startTime, "Main thread is null");
    }
    
    try {
        // Load world configuration (original lines 30-35)
        std::string worldName = pMainThread->m_szWorldName;
        if (!LoadWorldConfiguration(worldName)) {
            return CreateResult(AuthenticationResult::SystemError, startTime, "Failed to load world configuration");
        }
        
        // Get gate IP address (original lines 31-35)
        uint32_t gateIP = GetGateIPAddress();
        if (gateIP == 0) {
            return CreateResult(AuthenticationResult::NetworkError, startTime, "Failed to get gate IP address");
        }
        
        // Prepare hash verification data (original line 36)
        std::vector<uint8_t> hashVerify(32);
        memcpy_s(hashVerify.data(), 32, g_cbHashVerify, 32);
        
        // Send open world request (original lines 37-40)
        if (!SendOpenWorldRequest(worldName, gateIP, hashVerify)) {
            return CreateResult(AuthenticationResult::NetworkError, startTime, "Failed to send open world request");
        }
        
        // Send cash DB DSN request (original lines 41-42)
        CNationSettingManager* pNationMgr = CTSingleton_CNationSettingManager_Instance();
        if (pNationMgr) {
            CNationSettingManager_SendCashDBDSNRequest(pNationMgr);
        }
        
        // Update statistics
        m_stats.RecordLogin(true);
        
        AuthenticationDetails result = CreateResult(AuthenticationResult::Success, startTime);
        result.accountID = "SERVER";
        result.sessionState = LoginSessionState::Authenticated;
        
        LogAuthenticationEvent(result);
        
        if (m_authenticationCallback) {
            m_authenticationCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordLogin(false);
        Logger::Error("CAuthenticationManager::AccountServerLogin - Exception: %s", e.what());
        return CreateResult(AuthenticationResult::SystemError, startTime, 
                          std::string("Exception: ") + e.what());
    }
}

/**
 * User login through billing manager
 * Refactored from: LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c
 */
AuthenticationDetails CAuthenticationManager::LoginUser(CUserDB* pUserDB) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    if (!pUserDB) {
        return CreateResult(AuthenticationResult::SystemError, startTime, "User database is null");
    }
    
    try {
        // Check if account is banned
        std::string accountID = pUserDB->m_szAccountID;
        if (IsAccountBanned(accountID)) {
            m_stats.bannedAttempts++;
            return CreateResult(AuthenticationResult::AccountBanned, startTime, "Account is banned");
        }
        
        // Call billing system login (original line 20)
        // In real implementation: this would call the billing manager's login method
        // For now, simulate the billing login process
        
        // Create authentication context
        AuthenticationContext context;
        context.accountID = accountID;
        context.clientIP = AuthenticationUtils::IPAddressToString(pUserDB->m_dwIP);
        context.loginTime = std::chrono::system_clock::now();
        
        // Validate context
        if (!ValidateAuthenticationContext(context)) {
            return CreateResult(AuthenticationResult::InvalidCredentials, startTime, "Invalid authentication context");
        }
        
        // Create session
        std::string sessionToken = CreateSession(accountID, context.clientIP);
        
        // Update statistics
        m_stats.RecordLogin(true);
        
        AuthenticationDetails result = CreateResult(AuthenticationResult::Success, startTime);
        result.accountID = accountID;
        result.sessionToken = sessionToken;
        result.sessionState = LoginSessionState::LoggedIn;
        result.sessionId = GenerateSessionId();
        
        // Copy billing information
        result.billingInfo.billingType = pUserDB->m_BillingInfo.iType;
        result.billingInfo.remainingTime = pUserDB->m_BillingInfo.lRemainTime;
        result.billingInfo.cmsInfo = pUserDB->m_BillingInfo.szCMS;
        result.billingInfo.isActive = true;
        
        LogAuthenticationEvent(result);
        
        if (m_authenticationCallback) {
            m_authenticationCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordLogin(false);
        Logger::Error("CAuthenticationManager::LoginUser - Exception: %s", e.what());
        return CreateResult(AuthenticationResult::SystemError, startTime, 
                          std::string("Exception: ") + e.what());
    }
}

/**
 * Billing system login
 * Refactored from: LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.c
 */
AuthenticationDetails CAuthenticationManager::BillingLogin(CUserDB* pUserDB, CBilling* pBilling) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    if (!pUserDB || !pBilling) {
        return CreateResult(AuthenticationResult::SystemError, startTime, "Invalid parameters");
    }
    
    try {
        // Process billing login (original lines 29-41)
        if (!ProcessBillingLogin(pUserDB, pBilling)) {
            m_stats.billingErrors++;
            return CreateResult(AuthenticationResult::BillingError, startTime, "Billing login failed");
        }
        
        // Set billing no logout flag (original line 41)
        // In real implementation: CUserDB::SetBillingNoLogout(pUserDB, 0);
        
        // Update billing information
        BillingInfo billingInfo;
        billingInfo.billingType = pUserDB->m_BillingInfo.iType;
        billingInfo.remainingTime = pUserDB->m_BillingInfo.lRemainTime;
        billingInfo.cmsInfo = pUserDB->m_BillingInfo.szCMS;
        billingInfo.isActive = true;
        billingInfo.noLogout = false;
        
        std::string accountID = pUserDB->m_szAccountID;
        UpdateBillingInfo(accountID, billingInfo);
        
        // Update statistics
        m_stats.RecordLogin(true);
        
        AuthenticationDetails result = CreateResult(AuthenticationResult::Success, startTime);
        result.accountID = accountID;
        result.billingInfo = billingInfo;
        result.sessionState = LoginSessionState::Authenticated;
        result.sessionId = GenerateSessionId();
        
        LogAuthenticationEvent(result);
        
        if (m_authenticationCallback) {
            m_authenticationCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordLogin(false);
        m_stats.billingErrors++;
        Logger::Error("CAuthenticationManager::BillingLogin - Exception: %s", e.what());
        return CreateResult(AuthenticationResult::SystemError, startTime,
                          std::string("Exception: ") + e.what());
    }
}

/**
 * Authenticate user credentials
 */
AuthenticationDetails CAuthenticationManager::AuthenticateUser(const AuthenticationContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        // Validate context
        if (!ValidateAuthenticationContext(context)) {
            return CreateResult(AuthenticationResult::InvalidCredentials, startTime, "Invalid authentication context");
        }

        // Check if account is banned
        if (IsAccountBanned(context.accountID)) {
            m_stats.bannedAttempts++;
            return CreateResult(AuthenticationResult::AccountBanned, startTime, "Account is banned");
        }

        // Validate credentials (in real implementation, this would check against database)
        // For now, simulate credential validation

        // Create session
        std::string sessionToken = CreateSession(context.accountID, context.clientIP);

        // Update statistics
        m_stats.RecordLogin(true);

        AuthenticationDetails result = CreateResult(AuthenticationResult::Success, startTime);
        result.accountID = context.accountID;
        result.sessionToken = sessionToken;
        result.sessionState = LoginSessionState::Authenticated;
        result.sessionId = GenerateSessionId();

        LogAuthenticationEvent(result);

        if (m_authenticationCallback) {
            m_authenticationCallback(result);
        }

        return result;

    } catch (const std::exception& e) {
        m_stats.RecordLogin(false);
        Logger::Error("CAuthenticationManager::AuthenticateUser - Exception: %s", e.what());
        return CreateResult(AuthenticationResult::SystemError, startTime,
                          std::string("Exception: ") + e.what());
    }
}

/**
 * Validate session token
 */
bool CAuthenticationManager::ValidateSessionToken(const std::string& sessionToken) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionMutex);

        auto it = m_activeSessions.find(sessionToken);
        if (it == m_activeSessions.end()) {
            return false;
        }

        // Check if session is still valid (not expired)
        auto now = std::chrono::system_clock::now();
        auto sessionAge = now - it->second.billingInfo.endDate;
        if (sessionAge > std::chrono::hours(24)) { // 24 hour session timeout
            m_activeSessions.erase(it);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::ValidateSessionToken - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create new session
 */
std::string CAuthenticationManager::CreateSession(const std::string& accountID, const std::string& clientIP) {
    try {
        std::string sessionToken = GenerateSessionToken(accountID, clientIP);

        std::lock_guard<std::mutex> lock(m_sessionMutex);

        AuthenticationDetails sessionInfo;
        sessionInfo.accountID = accountID;
        sessionInfo.sessionToken = sessionToken;
        sessionInfo.sessionState = LoginSessionState::Authenticated;
        sessionInfo.sessionId = GenerateSessionId();
        sessionInfo.billingInfo.endDate = std::chrono::system_clock::now() + std::chrono::hours(24);

        m_activeSessions[sessionToken] = sessionInfo;

        Logger::Debug("CAuthenticationManager::CreateSession - Created session for account: %s", accountID.c_str());
        return sessionToken;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::CreateSession - Exception: %s", e.what());
        return "";
    }
}

/**
 * Destroy session
 */
bool CAuthenticationManager::DestroySession(const std::string& sessionToken) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionMutex);

        auto it = m_activeSessions.find(sessionToken);
        if (it != m_activeSessions.end()) {
            Logger::Debug("CAuthenticationManager::DestroySession - Destroyed session for account: %s",
                         it->second.accountID.c_str());
            m_activeSessions.erase(it);
            m_stats.RecordLogout();
            return true;
        }

        return false;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::DestroySession - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get session information
 */
std::optional<AuthenticationDetails> CAuthenticationManager::GetSessionInfo(const std::string& sessionToken) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionMutex);

        auto it = m_activeSessions.find(sessionToken);
        if (it != m_activeSessions.end()) {
            return it->second;
        }

        return std::nullopt;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::GetSessionInfo - Exception: %s", e.what());
        return std::nullopt;
    }
}

/**
 * Update billing information
 */
bool CAuthenticationManager::UpdateBillingInfo(const std::string& accountID, const BillingInfo& billingInfo) {
    try {
        std::lock_guard<std::mutex> lock(m_sessionMutex);

        // Find session by account ID and update billing info
        for (auto& [token, session] : m_activeSessions) {
            if (session.accountID == accountID) {
                session.billingInfo = billingInfo;
                Logger::Debug("CAuthenticationManager::UpdateBillingInfo - Updated billing info for account: %s",
                             accountID.c_str());
                return true;
            }
        }

        return false;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::UpdateBillingInfo - Exception: %s", e.what());
        return false;
    }
}

/**
 * Check if account is banned
 */
bool CAuthenticationManager::IsAccountBanned(const std::string& accountID) {
    try {
        std::lock_guard<std::mutex> lock(m_banMutex);

        auto it = m_bannedAccounts.find(accountID);
        if (it == m_bannedAccounts.end()) {
            return false;
        }

        // Check if ban has expired (0 time means permanent ban)
        if (it->second == std::chrono::system_clock::time_point{}) {
            return true; // Permanent ban
        }

        if (std::chrono::system_clock::now() < it->second) {
            return true; // Still banned
        }

        // Ban has expired, remove it
        m_bannedAccounts.erase(it);
        return false;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::IsAccountBanned - Exception: %s", e.what());
        return false;
    }
}

/**
 * Ban account
 */
bool CAuthenticationManager::BanAccount(const std::string& accountID, const std::string& reason,
                                       std::chrono::seconds duration) {
    try {
        std::lock_guard<std::mutex> lock(m_banMutex);

        auto banEndTime = std::chrono::system_clock::time_point{};
        if (duration.count() > 0) {
            banEndTime = std::chrono::system_clock::now() + duration;
        }

        m_bannedAccounts[accountID] = banEndTime;

        // Destroy any active sessions for this account
        {
            std::lock_guard<std::mutex> sessionLock(m_sessionMutex);
            auto it = m_activeSessions.begin();
            while (it != m_activeSessions.end()) {
                if (it->second.accountID == accountID) {
                    it = m_activeSessions.erase(it);
                    m_stats.RecordLogout();
                } else {
                    ++it;
                }
            }
        }

        Logger::Warning("CAuthenticationManager::BanAccount - Banned account: %s, reason: %s",
                       accountID.c_str(), reason.c_str());
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::BanAccount - Exception: %s", e.what());
        return false;
    }
}

/**
 * Unban account
 */
bool CAuthenticationManager::UnbanAccount(const std::string& accountID) {
    try {
        std::lock_guard<std::mutex> lock(m_banMutex);

        auto it = m_bannedAccounts.find(accountID);
        if (it != m_bannedAccounts.end()) {
            m_bannedAccounts.erase(it);
            Logger::Info("CAuthenticationManager::UnbanAccount - Unbanned account: %s", accountID.c_str());
            return true;
        }

        return false;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::UnbanAccount - Exception: %s", e.what());
        return false;
    }
}

/**
 * Reset statistics
 */
void CAuthenticationManager::ResetStatistics() {
    try {
        m_stats.totalLoginAttempts = 0;
        m_stats.successfulLogins = 0;
        m_stats.failedLogins = 0;
        m_stats.bannedAttempts = 0;
        m_stats.activeSessions = 0;
        m_stats.billingErrors = 0;
        m_stats.lastLogin = std::chrono::system_clock::now();

        Logger::Debug("CAuthenticationManager::ResetStatistics - Statistics reset");

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::ResetStatistics - Exception: %s", e.what());
    }
}

/**
 * Set authentication callback
 */
void CAuthenticationManager::SetAuthenticationCallback(std::function<void(const AuthenticationDetails&)> callback) {
    m_authenticationCallback = callback;
}

/**
 * Validate authentication context
 */
bool CAuthenticationManager::ValidateAuthenticationContext(const AuthenticationContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CAuthenticationManager::ValidateAuthenticationContext - Invalid context");
            return false;
        }

        if (context.accountID.length() > 32 || context.accountID.length() < 3) {
            Logger::Warning("CAuthenticationManager::ValidateAuthenticationContext - Invalid account ID length");
            return false;
        }

        if (context.clientIP.empty()) {
            Logger::Warning("CAuthenticationManager::ValidateAuthenticationContext - Empty client IP");
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::ValidateAuthenticationContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Load world configuration
 */
bool CAuthenticationManager::LoadWorldConfiguration(const std::string& worldName) {
    try {
        if (worldName.empty()) {
            Logger::Error("CAuthenticationManager::LoadWorldConfiguration - Empty world name");
            return false;
        }

        // In real implementation, this would load world-specific configuration
        Logger::Debug("CAuthenticationManager::LoadWorldConfiguration - Loaded configuration for world: %s",
                     worldName.c_str());
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::LoadWorldConfiguration - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get gate IP address
 */
uint32_t CAuthenticationManager::GetGateIPAddress() {
    try {
        char returnedString[128] = {0};

        // Get gate IP from configuration (original lines 31-35)
        GetPrivateProfileStringA("System", "GateIP", "X", returnedString, 128, "..\\WorldInfo\\WorldInfo.ini");

        if (strcmp_0(returnedString, "X") != 0) {
            // Use configured IP
            return inet_addr(returnedString);
        } else {
            // Use local IP
            return GetIPAddress();
        }

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::GetGateIPAddress - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Send open world request
 */
bool CAuthenticationManager::SendOpenWorldRequest(const std::string& worldName, uint32_t gateIP,
                                                 const std::vector<uint8_t>& hashVerify) {
    try {
        // Prepare request data (original lines 37-40)
        struct RequestData {
            char worldName[32];
            uint32_t gateIP;
            uint8_t hashVerify[32];
            uint8_t type1;
            uint8_t type2;
        } requestData = {0};

        strcpy_0(requestData.worldName, worldName.c_str());
        requestData.gateIP = gateIP;
        memcpy_s(requestData.hashVerify, 32, hashVerify.data(), std::min(hashVerify.size(), size_t(32)));
        requestData.type1 = 1;
        requestData.type2 = 1;

        // Calculate size and send (original lines 39-40)
        uint16_t size = sizeof(RequestData);
        CNetProcess_LoadSendMsg(unk_1414F2090, 0, &requestData.type1, &requestData, size);

        Logger::Debug("CAuthenticationManager::SendOpenWorldRequest - Sent open world request for: %s",
                     worldName.c_str());
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::SendOpenWorldRequest - Exception: %s", e.what());
        return false;
    }
}

/**
 * Process billing login
 */
bool CAuthenticationManager::ProcessBillingLogin(CUserDB* pUserDB, CBilling* pBilling) {
    try {
        if (!pUserDB || !pBilling) {
            return false;
        }

        // Get client IP string (original line 31)
        char* clientIPStr = inet_ntoa((struct in_addr)pUserDB->m_dwIP);
        if (!clientIPStr) {
            return false;
        }

        // Prepare billing data (original lines 29-35)
        std::string accountID = pUserDB->m_szAccountID;
        std::string cmsInfo = pUserDB->m_BillingInfo.szCMS;

        // Call billing system (original lines 36-40)
        // In real implementation: this would call the billing system's SendMsg_Login method
        // For now, simulate successful billing login

        Logger::Debug("CAuthenticationManager::ProcessBillingLogin - Processed billing login for account: %s",
                     accountID.c_str());
        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::ProcessBillingLogin - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate session token
 */
std::string CAuthenticationManager::GenerateSessionToken(const std::string& accountID, const std::string& clientIP) {
    try {
        // Generate random session token
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);

        std::stringstream ss;
        ss << accountID << "_" << clientIP << "_";

        // Add timestamp
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::seconds>(now.time_since_epoch()).count();
        ss << timestamp << "_";

        // Add random component
        for (int i = 0; i < 16; ++i) {
            ss << std::hex << dis(gen);
        }

        return ss.str();

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::GenerateSessionToken - Exception: %s", e.what());
        return "";
    }
}

/**
 * Log authentication event
 */
void CAuthenticationManager::LogAuthenticationEvent(const AuthenticationDetails& details) {
    try {
        if (m_bDetailedLogging) {
            Logger::Info("Authentication Event - Result: %s, Account: %s, Session: %s, Time: %lldms",
                        details.GetResultString().c_str(),
                        details.accountID.c_str(),
                        details.sessionToken.c_str(),
                        details.authenticationTime.count());
        }

        if (!details.IsSuccess()) {
            Logger::Warning("Authentication Failed - %s: %s",
                           details.GetResultString().c_str(),
                           details.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        // Don't log errors in logging function to avoid recursion
    }
}

/**
 * Create authentication result with timing
 */
AuthenticationDetails CAuthenticationManager::CreateResult(AuthenticationResult result,
                                                          std::chrono::high_resolution_clock::time_point startTime,
                                                          const std::string& errorMessage) {
    AuthenticationDetails authResult;
    authResult.result = result;
    authResult.errorMessage = errorMessage;
    authResult.sessionId = GenerateSessionId();
    authResult.authenticationTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return authResult;
}

/**
 * Cleanup expired sessions
 */
void CAuthenticationManager::CleanupExpiredSessions() {
    try {
        std::lock_guard<std::mutex> lock(m_sessionMutex);

        auto now = std::chrono::system_clock::now();
        auto it = m_activeSessions.begin();

        while (it != m_activeSessions.end()) {
            if (it->second.billingInfo.IsExpired() ||
                (now - it->second.billingInfo.endDate) > std::chrono::hours(24)) {
                it = m_activeSessions.erase(it);
                m_stats.RecordLogout();
            } else {
                ++it;
            }
        }

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::CleanupExpiredSessions - Exception: %s", e.what());
    }
}

/**
 * Cleanup expired bans
 */
void CAuthenticationManager::CleanupExpiredBans() {
    try {
        std::lock_guard<std::mutex> lock(m_banMutex);

        auto now = std::chrono::system_clock::now();
        auto it = m_bannedAccounts.begin();

        while (it != m_bannedAccounts.end()) {
            // Skip permanent bans (time_point{})
            if (it->second != std::chrono::system_clock::time_point{} && now >= it->second) {
                it = m_bannedAccounts.erase(it);
            } else {
                ++it;
            }
        }

    } catch (const std::exception& e) {
        Logger::Error("CAuthenticationManager::CleanupExpiredBans - Exception: %s", e.what());
    }
}

/**
 * Legacy compatibility functions
 */
namespace LegacyCompatibility {

/**
 * Legacy account server login wrapper
 */
void AccountServerLogin_Legacy(CMainThread* pMainThread) {
    try {
        static CAuthenticationManager authManager;

        if (!authManager.Initialize()) {
            Logger::Error("LegacyCompatibility::AccountServerLogin_Legacy - Failed to initialize auth manager");
            return;
        }

        AuthenticationDetails result = authManager.AccountServerLogin(pMainThread);
        if (!result.IsSuccess()) {
            Logger::Error("LegacyCompatibility::AccountServerLogin_Legacy - Authentication failed: %s",
                         result.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::AccountServerLogin_Legacy - Exception: %s", e.what());
    }
}

/**
 * Legacy billing manager login wrapper
 */
void Login_BillingManager_Legacy(CBillingManager* pBillingManager, CUserDB* pUserDB) {
    try {
        static CAuthenticationManager authManager;

        if (!pBillingManager || !pUserDB) {
            return;
        }

        AuthenticationDetails result = authManager.LoginUser(pUserDB);
        if (!result.IsSuccess()) {
            Logger::Error("LegacyCompatibility::Login_BillingManager_Legacy - Login failed: %s",
                         result.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::Login_BillingManager_Legacy - Exception: %s", e.what());
    }
}

/**
 * Legacy billing login wrapper
 */
void Login_Billing_Legacy(CBilling* pBilling, CUserDB* pUserDB) {
    try {
        static CAuthenticationManager authManager;

        if (!pBilling || !pUserDB) {
            return;
        }

        AuthenticationDetails result = authManager.BillingLogin(pUserDB, pBilling);
        if (!result.IsSuccess()) {
            Logger::Error("LegacyCompatibility::Login_Billing_Legacy - Billing login failed: %s",
                         result.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::Login_Billing_Legacy - Exception: %s", e.what());
    }
}

} // namespace LegacyCompatibility

/**
 * Utility functions for authentication
 */
namespace AuthenticationUtils {

/**
 * Convert IP address to string
 */
std::string IPAddressToString(uint32_t ipAddress) {
    try {
        struct in_addr addr;
        addr.s_addr = ipAddress;
        char* ipStr = inet_ntoa(addr);
        return ipStr ? std::string(ipStr) : "";

    } catch (const std::exception& e) {
        Logger::Error("AuthenticationUtils::IPAddressToString - Exception: %s", e.what());
        return "";
    }
}

/**
 * Convert string to IP address
 */
uint32_t StringToIPAddress(const std::string& ipString) {
    try {
        return inet_addr(ipString.c_str());

    } catch (const std::exception& e) {
        Logger::Error("AuthenticationUtils::StringToIPAddress - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Generate hash verification data
 */
std::vector<uint8_t> GenerateHashVerification(const std::string& data) {
    try {
        // Simple hash generation (in real implementation, use proper cryptographic hash)
        std::vector<uint8_t> hash(32, 0);

        std::hash<std::string> hasher;
        size_t hashValue = hasher(data);

        // Distribute hash value across the array
        for (size_t i = 0; i < 32; ++i) {
            hash[i] = static_cast<uint8_t>((hashValue >> (i % 8)) & 0xFF);
        }

        return hash;

    } catch (const std::exception& e) {
        Logger::Error("AuthenticationUtils::GenerateHashVerification - Exception: %s", e.what());
        return std::vector<uint8_t>(32, 0);
    }
}

/**
 * Validate hash verification
 */
bool ValidateHashVerification(const std::string& data, const std::vector<uint8_t>& hash) {
    try {
        auto expectedHash = GenerateHashVerification(data);
        return expectedHash == hash;

    } catch (const std::exception& e) {
        Logger::Error("AuthenticationUtils::ValidateHashVerification - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get current system time
 */
std::chrono::system_clock::time_point GetCurrentTime() {
    return std::chrono::system_clock::now();
}

/**
 * Format time for logging
 */
std::string FormatTime(const std::chrono::system_clock::time_point& timePoint) {
    try {
        auto time_t = std::chrono::system_clock::to_time_t(timePoint);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();

    } catch (const std::exception& e) {
        Logger::Error("AuthenticationUtils::FormatTime - Exception: %s", e.what());
        return "Invalid Time";
    }
}

} // namespace AuthenticationUtils

} // namespace Authentication
} // namespace NexusProtection
