/*
 * Function: ?Create@CGravityStoneRegener@@QEAA_NPEAVCMapData@@@Z
 * Address: 0x14012E950
 */

char __fastcall CGravityStoneRegener::Create(CGravityStoneRegener *this, CMapData *pkMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-68h]@1
  _object_create_setdata Dst; // [sp+28h] [bp-40h]@7
  CGravityStoneRegener *v7; // [sp+70h] [bp+8h]@1
  CMapData *v8; // [sp+78h] [bp+10h]@1

  v8 = pkMap;
  v7 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_eState != -1 && pkMap )
  {
    _object_create_setdata::_object_create_setdata(&Dst);
    Dst.m_nLayerIndex = 0;
    Dst.m_pMap = v8;
    memcpy_0(Dst.m_fStartPos, v7->m_pkRegenPos->m_fCenterPos, 0xCui64);
    Dst.m_pRecordSet = 0i64;
    if ( CGameObject::Create((CGameObject *)&v7->vfptr, &Dst) )
    {
      v7->m_dwObjSerial = CGravityStoneRegener::ms_dwSerialCnt++;
      v7->m_eState = 1;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
