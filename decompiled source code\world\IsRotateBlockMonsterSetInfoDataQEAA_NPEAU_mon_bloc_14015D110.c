/*
 * Function: ?IsRotateBlock@MonsterSetInfoData@@QEAA_NPEAU_mon_block@@@Z
 * Address: 0x14015D110
 */

char __fastcall MonsterSetInfoData::IsRotateBlock(MonsterSetInfoData *this, _mon_block *pBlock)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *v6; // [sp+20h] [bp-28h]@13
  char *Str1; // [sp+28h] [bp-20h]@13
  int j; // [sp+30h] [bp-18h]@13
  MonsterSetInfoData *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_nMonBlkCount > 0 )
  {
    if ( v9->m_strRotMonBlk_Ar )
    {
      if ( pBlock && pBlock->m_pBlkRec && pBlock->m_pMap && pBlock->m_pMap->m_pMapSet )
      {
        v6 = pBlock->m_pBlkRec->m_strCode;
        Str1 = pBlock->m_pMap->m_pMapSet->m_strCode;
        for ( j = 0; j < v9->m_nMonBlkCount; ++j )
        {
          if ( !strcmp_0(Str1, v9->m_strRotMonBlk_Ar[(signed __int64)(2 * j)])
            && !strcmp_0(v6, v9->m_strRotMonBlk_Ar[(signed __int64)(2 * j + 1)]) )
          {
            return 1;
          }
        }
        result = 0;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
