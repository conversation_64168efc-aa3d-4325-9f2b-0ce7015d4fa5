/*
 * Function: ?pc_CombineItemExAccept@CPlayer@@QEAAXPEAU_combine_ex_item_accept_request_clzo@@@Z
 * Address: 0x1400B0600
 */

void __fastcall CPlayer::pc_CombineItemExAccept(CPlayer *this, _combine_ex_item_accept_request_clzo *pRecv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  char v5; // [sp+20h] [bp-38h]@4
  _combine_ex_item_accept_result_zocl pSend; // [sp+34h] [bp-24h]@4
  CPlayer *v7; // [sp+60h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  v5 = ItemCombineMgr::RequestCombineAcceptProcess(&v7->m_ItemCombineMgr, pRecv, &pSend);
  if ( v5 )
    pSend.byErrCode = v5;
  CPlayer::SendMsg_CombineItemExAcceptResult(v7, &pSend);
}
