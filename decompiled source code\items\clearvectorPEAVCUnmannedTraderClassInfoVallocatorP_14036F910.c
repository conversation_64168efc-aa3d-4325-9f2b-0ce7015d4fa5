/*
 * Function: ?clear@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAXXZ
 * Address: 0x14036F910
 */

void __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::clear(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-A8h]@1
  char v4; // [sp+20h] [bp-88h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *result; // [sp+38h] [bp-70h]@4
  char v6; // [sp+40h] [bp-68h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v7; // [sp+58h] [bp-50h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v8; // [sp+60h] [bp-48h]@4
  __int64 v9; // [sp+78h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v10; // [sp+80h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v11; // [sp+88h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v12; // [sp+90h] [bp-18h]@4
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v13; // [sp+B0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = -2i64;
  result = (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v4;
  v7 = (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v6;
  v10 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
          v13,
          (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v4);
  v11 = v10;
  v12 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(v13, v7);
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::erase(v13, &v8, v12, v11);
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v8);
}
