/*
 * Function: ?pc_PotionSeparation@CPlayer@@QEAAXGE@Z
 * Address: 0x1400FD060
 */

void __fastcall CPlayer::pc_PotionSeparation(CPlayer *this, unsigned __int16 wSerial, char byAmount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayer::SendMsg_PotionSeparation(v6, wSerial, 0, 0xFFu, byAmount, -1);
}
