/*
 * Function: j_??$_Ptr_cat@PEAVCUnmannedTraderItemCodeInfo@@PEAV1@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCUnmannedTraderItemCodeInfo@@0@Z
 * Address: 0x14000F53D
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(CUnmannedTraderItemCodeInfo **__formal, CUnmannedTraderItemCodeInfo **a2)
{
  return std::_Ptr_cat<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(__formal, a2);
}
