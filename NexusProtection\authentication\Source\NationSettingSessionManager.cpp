/**
 * @file NationSettingSessionManager.cpp
 * @brief Implementation of nation setting session management functionality
 * 
 * Refactored from decompiled source: OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.c
 * Original function: CNationSettingManager::OnConnectSession
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "NationSettingSessionManager.h"
#include <iostream>
#include <format>
#include <chrono>
#include <cassert>
#include <mutex>

/**
 * @brief SessionInfo constructor
 */
SessionInfo::SessionInfo() {
    Reset();
}

/**
 * @brief Reset session info to default values
 */
void SessionInfo::Reset() {
    sessionID = -1;
    state = SessionState::Disconnected;
    clientIP.clear();
    connectionTime = 0;
    isGameGuardEnabled = false;
}

/**
 * @brief Check if session info is valid
 * @return true if valid, false otherwise
 */
bool SessionInfo::IsValid() const {
    return sessionID >= 0 && state != SessionState::Error;
}

/**
 * @brief Constructor
 */
NationSettingSessionManager::NationSettingSessionManager() 
    : m_securityCookie(0) {
}

/**
 * @brief Destructor
 */
NationSettingSessionManager::~NationSettingSessionManager() = default;

/**
 * @brief Handle session connection
 * 
 * Processes a session connection request with enhanced error handling
 * and logging capabilities.
 * 
 * @param manager Pointer to CNationSettingManager instance
 * @param sessionID Session identifier
 * @return SessionConnectionResult indicating success or failure
 */
SessionConnectionResult NationSettingSessionManager::HandleSessionConnection(CNationSettingManager* manager, int sessionID) {
    try {
        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;
        
        std::cout << std::format("[INFO] Handling session connection for session ID: {}", sessionID) << std::endl;
        
        // Validate input parameters
        if (!ValidateParameters(manager, sessionID)) {
            return SessionConnectionResult::InvalidManager;
        }
        
        // Update session state to connecting
        UpdateSessionState(sessionID, SessionState::Connecting);
        
        // Get game guard system
        INationGameGuardSystem* gameGuard = GetGameGuardSystem(manager);
        
        if (gameGuard) {
            // Execute game guard connection
            if (!ExecuteGameGuardConnection(gameGuard, sessionID)) {
                UpdateSessionState(sessionID, SessionState::Error);
                return SessionConnectionResult::InvalidGameGuard;
            }
            
            // Update session info
            {
                std::lock_guard<std::mutex> lock(m_sessionsMutex);
                auto& sessionInfo = m_sessions[sessionID];
                sessionInfo.sessionID = sessionID;
                sessionInfo.isGameGuardEnabled = true;
                sessionInfo.connectionTime = static_cast<uint32_t>(
                    std::chrono::duration_cast<std::chrono::seconds>(
                        std::chrono::system_clock::now().time_since_epoch()
                    ).count()
                );
            }
            
            std::cout << std::format("[INFO] Game guard connection established for session: {}", sessionID) << std::endl;
        } else {
            std::cout << std::format("[INFO] No game guard system available for session: {}", sessionID) << std::endl;
        }
        
        // Update session state to connected
        UpdateSessionState(sessionID, SessionState::Connected);
        
        // Verify security cookie (equivalent to original stack protection check)
        if (!ValidateSecurityCookie()) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return SessionConnectionResult::SecurityError;
        }
        
        std::cout << std::format("[INFO] Session connection completed successfully for session: {}", sessionID) << std::endl;
        return SessionConnectionResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during session connection: {}", e.what()));
        UpdateSessionState(sessionID, SessionState::Error);
        return SessionConnectionResult::SystemError;
    }
}

/**
 * @brief Legacy OnConnectSession function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * 
 * @param manager Pointer to CNationSettingManager instance
 * @param sessionID Session identifier
 */
void NationSettingSessionManager::OnConnectSession_Legacy(CNationSettingManager* manager, int sessionID) {
    try {
        if (!manager) {
            std::cerr << "[ERROR] Invalid CNationSettingManager pointer in OnConnectSession_Legacy" << std::endl;
            return;
        }
        
        NationSettingSessionManager sessionMgr;
        SessionConnectionResult result = sessionMgr.HandleSessionConnection(manager, sessionID);
        
        if (result != SessionConnectionResult::Success) {
            std::cerr << "[ERROR] Session connection failed: " 
                      << sessionMgr.GetLastError() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in OnConnectSession_Legacy: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in OnConnectSession_Legacy" << std::endl;
    }
}

/**
 * @brief Handle session disconnection
 * 
 * Processes a session disconnection request.
 * 
 * @param manager Pointer to CNationSettingManager instance
 * @param sessionID Session identifier
 * @return SessionConnectionResult indicating success or failure
 */
SessionConnectionResult NationSettingSessionManager::HandleSessionDisconnection(CNationSettingManager* manager, int sessionID) {
    try {
        std::cout << std::format("[INFO] Handling session disconnection for session ID: {}", sessionID) << std::endl;
        
        // Validate input parameters
        if (!ValidateParameters(manager, sessionID)) {
            return SessionConnectionResult::InvalidManager;
        }
        
        // Update session state to disconnecting
        UpdateSessionState(sessionID, SessionState::Disconnecting);
        
        // Get game guard system and handle disconnection
        INationGameGuardSystem* gameGuard = GetGameGuardSystem(manager);
        if (gameGuard && gameGuard->vfptr && gameGuard->vfptr->OnDisconnectSession) {
            gameGuard->vfptr->OnDisconnectSession(gameGuard, static_cast<unsigned int>(sessionID));
        }
        
        // Remove session from tracking
        {
            std::lock_guard<std::mutex> lock(m_sessionsMutex);
            m_sessions.erase(sessionID);
        }
        
        // Update session state to disconnected
        UpdateSessionState(sessionID, SessionState::Disconnected);
        
        std::cout << std::format("[INFO] Session disconnection completed for session: {}", sessionID) << std::endl;
        return SessionConnectionResult::Success;
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Exception during session disconnection: {}", e.what()));
        return SessionConnectionResult::SystemError;
    }
}

/**
 * @brief Handle first session verification
 * @param manager Pointer to CNationSettingManager instance
 * @param sessionID Session identifier
 * @return true if verification successful, false otherwise
 */
bool NationSettingSessionManager::HandleSessionFirstVerification(CNationSettingManager* manager, int sessionID) {
    try {
        std::cout << "[INFO] Handling session first verification for session ID: " << sessionID << std::endl;

        // Validate input parameters
        if (!ValidateParameters(manager, sessionID)) {
            return false;
        }

        // Get the game guard system
        INationGameGuardSystem* gameGuard = GetGameGuardSystem(manager);
        if (!gameGuard) {
            // No game guard system available - return true (success) as per original behavior
            std::cout << "[INFO] No game guard system available, verification passed for session: " << sessionID << std::endl;
            UpdateSessionState(sessionID, SessionState::Authenticated);
            return true;
        }

        // Call the game guard first verification method
        int result = 1; // Default to success
        if (gameGuard->vfptr && gameGuard->vfptr->OnCheckSession_FirstVerify) {
            result = gameGuard->vfptr->OnCheckSession_FirstVerify(gameGuard, static_cast<unsigned int>(sessionID));
            std::cout << "[INFO] Game guard first verification result: " << result << " for session: " << sessionID << std::endl;
        } else {
            std::cout << "[INFO] Game guard first verification function not available, defaulting to success for session: " << sessionID << std::endl;
        }

        // Update session state based on verification result
        if (result == 1) {
            UpdateSessionState(sessionID, SessionState::Authenticated);
            return true;
        } else {
            UpdateSessionState(sessionID, SessionState::Error);
            SetLastError("Game guard first verification failed for session " + std::to_string(sessionID));
            return false;
        }

    } catch (const std::exception& e) {
        SetLastError(std::string("Exception during session first verification: ") + e.what());
        UpdateSessionState(sessionID, SessionState::Error);
        return false;
    }
}

/**
 * @brief Legacy OnCheckSession_FirstVerify function for backward compatibility
 * @param manager Pointer to CNationSettingManager instance
 * @param sessionID Session identifier
 * @return 1 if verification successful, 0 otherwise
 */
int NationSettingSessionManager::OnCheckSession_FirstVerify_Legacy(CNationSettingManager* manager, int sessionID) {
    NationSettingSessionManager sessionMgr;
    bool result = sessionMgr.HandleSessionFirstVerification(manager, sessionID);
    return result ? 1 : 0;
}

/**
 * @brief Validate input parameters
 * @param manager Pointer to CNationSettingManager instance
 * @param sessionID Session identifier
 * @return true if valid, false otherwise
 */
bool NationSettingSessionManager::ValidateParameters(CNationSettingManager* manager, int sessionID) {
    if (!manager) {
        SetLastError("CNationSettingManager pointer is null");
        return false;
    }
    
    if (sessionID < 0) {
        SetLastError("Invalid session ID");
        return false;
    }
    
    return true;
}

/**
 * @brief Get game guard system
 * @param manager Pointer to CNationSettingManager instance
 * @return Pointer to INationGameGuardSystem, or nullptr if not available
 */
INationGameGuardSystem* NationSettingSessionManager::GetGameGuardSystem(CNationSettingManager* manager) {
    if (!manager || !manager->m_pData) {
        return nullptr;
    }
    
    return CNationSettingData::GetGameGuardSystem(manager->m_pData);
}

/**
 * @brief Execute game guard connection
 * @param gameGuard Pointer to INationGameGuardSystem instance
 * @param sessionID Session identifier
 * @return true if successful, false otherwise
 */
bool NationSettingSessionManager::ExecuteGameGuardConnection(INationGameGuardSystem* gameGuard, int sessionID) {
    try {
        if (!gameGuard || !gameGuard->vfptr) {
            SetLastError("Invalid game guard system or virtual function table");
            return false;
        }
        
        // Call the game guard connection function (equivalent to original virtual call)
        // (*(void (__fastcall **)(INationGameGuardSystem *, _QWORD))&v5->vfptr->gap8[0])(v5, (unsigned int)v7);
        if (gameGuard->vfptr->OnConnectSession) {
            gameGuard->vfptr->OnConnectSession(gameGuard, static_cast<unsigned int>(sessionID));
            return true;
        } else {
            SetLastError("Game guard OnConnectSession function is null");
            return false;
        }
        
    } catch (const std::exception& e) {
        SetLastError(std::format("Failed to execute game guard connection: {}", e.what()));
        return false;
    }
}

/**
 * @brief Get session information
 * 
 * Retrieves information about a specific session.
 * 
 * @param sessionID Session identifier
 * @return SessionInfo structure with session details
 */
SessionInfo NationSettingSessionManager::GetSessionInfo(int sessionID) const {
    std::lock_guard<std::mutex> lock(m_sessionsMutex);
    
    auto it = m_sessions.find(sessionID);
    if (it != m_sessions.end()) {
        return it->second;
    }
    
    SessionInfo info;
    info.sessionID = sessionID;
    info.state = SessionState::Disconnected;
    return info;
}

/**
 * @brief Get active session count
 * @return Number of active sessions
 */
uint32_t NationSettingSessionManager::GetActiveSessionCount() const {
    std::lock_guard<std::mutex> lock(m_sessionsMutex);
    
    uint32_t activeCount = 0;
    for (const auto& [sessionID, sessionInfo] : m_sessions) {
        if (sessionInfo.state == SessionState::Connected || 
            sessionInfo.state == SessionState::Authenticated || 
            sessionInfo.state == SessionState::Active) {
            activeCount++;
        }
    }
    
    return activeCount;
}

/**
 * @brief Update session state
 * @param sessionID Session identifier
 * @param newState New session state
 */
void NationSettingSessionManager::UpdateSessionState(int sessionID, SessionState newState) {
    SessionState oldState = SessionState::Disconnected;
    
    // Update state
    {
        std::lock_guard<std::mutex> lock(m_sessionsMutex);
        auto& sessionInfo = m_sessions[sessionID];
        oldState = sessionInfo.state;
        sessionInfo.state = newState;
        sessionInfo.sessionID = sessionID;
    }
    
    // Call registered callback
    if (m_stateChangeCallback && oldState != newState) {
        try {
            m_stateChangeCallback(sessionID, oldState, newState);
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Exception in session state callback: " << e.what() << std::endl;
        }
    }
}

/**
 * @brief Set session state change callback
 * @param callback Function to call when session state changes
 */
void NationSettingSessionManager::SetSessionStateChangeCallback(std::function<void(int, SessionState, SessionState)> callback) {
    m_stateChangeCallback = std::move(callback);
}

/**
 * @brief Get the last error message
 * @return string containing the last error message
 */
std::string NationSettingSessionManager::GetLastError() const {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    return m_lastError;
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void NationSettingSessionManager::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
}

/**
 * @brief Validate security cookie
 * @return true if valid, false if corrupted
 */
bool NationSettingSessionManager::ValidateSecurityCookie() const {
    return (reinterpret_cast<uint64_t>(this) ^ _security_cookie) == m_securityCookie;
}

/**
 * @brief Convert SessionConnectionResult enum to string for logging
 * @param result The connection result
 * @return String representation of the result
 */
std::string SessionConnectionResultToString(SessionConnectionResult result) {
    switch (result) {
        case SessionConnectionResult::Success: return "Success";
        case SessionConnectionResult::Failure: return "Failure";
        case SessionConnectionResult::InvalidManager: return "InvalidManager";
        case SessionConnectionResult::InvalidSession: return "InvalidSession";
        case SessionConnectionResult::InvalidGameGuard: return "InvalidGameGuard";
        case SessionConnectionResult::SystemError: return "SystemError";
        case SessionConnectionResult::SecurityError: return "SecurityError";
        default: return "Unknown";
    }
}

/**
 * @brief Convert SessionState enum to string for logging
 * @param state The session state
 * @return String representation of the state
 */
std::string SessionStateToString(SessionState state) {
    switch (state) {
        case SessionState::Disconnected: return "Disconnected";
        case SessionState::Connecting: return "Connecting";
        case SessionState::Connected: return "Connected";
        case SessionState::Authenticated: return "Authenticated";
        case SessionState::Active: return "Active";
        case SessionState::Disconnecting: return "Disconnecting";
        case SessionState::Error: return "Error";
        default: return "Unknown";
    }
}

/**
 * @brief CNationSettingManager::OnConnectSession implementation for compatibility
 * 
 * This provides the original method implementation that can be called
 * directly on CNationSettingManager instances.
 */
void CNationSettingManager::OnConnectSession(int sessionID) {
    NationSettingSessionManager::OnConnectSession_Legacy(this, sessionID);
}

/**
 * @brief CNationSettingManager::OnDisConnectSession implementation for compatibility
 *
 * This provides the original method implementation that can be called
 * directly on CNationSettingManager instances.
 */
void CNationSettingManager::OnDisConnectSession(int sessionID) {
    NationSettingSessionManager sessionMgr;
    sessionMgr.HandleSessionDisconnection(this, sessionID);
}

/**
 * @brief CNationSettingManager::OnCheckSession_FirstVerify implementation for compatibility
 *
 * This provides the original method implementation that can be called
 * directly on CNationSettingManager instances.
 *
 * @param sessionID Session identifier
 * @return true if verification successful, false otherwise
 */
bool CNationSettingManager::OnCheckSession_FirstVerify(int sessionID) {
    return NationSettingSessionManager::OnCheckSession_FirstVerify_Legacy(this, sessionID) == 1;
}
