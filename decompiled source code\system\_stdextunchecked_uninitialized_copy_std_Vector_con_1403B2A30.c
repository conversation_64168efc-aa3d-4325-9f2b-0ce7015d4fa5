/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____::_1_::dtor$0
 * Address: 0x1403B2A30
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____CMoveMapLimitRight_____ptr64_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 184));
}
