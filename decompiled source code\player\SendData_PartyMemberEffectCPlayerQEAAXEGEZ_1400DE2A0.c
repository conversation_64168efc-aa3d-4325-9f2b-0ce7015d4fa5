/*
 * Function: ?SendData_PartyMemberEffect@CPlayer@@QEAAXEGE@Z
 * Address: 0x1400DE2A0
 */

void __fastcall CPlayer::SendData_PartyMemberEffect(CPlayer *this, char byAlterCode, unsigned __int16 wEffectCode, char byLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  CPartyPlayer **v7; // [sp+30h] [bp-58h]@5
  char szMsg[4]; // [sp+48h] [bp-40h]@6
  char v9; // [sp+4Ch] [bp-3Ch]@6
  unsigned __int16 v10; // [sp+4Dh] [bp-3Bh]@6
  char v11; // [sp+4Fh] [bp-39h]@6
  int v12; // [sp+54h] [bp-34h]@6
  char pbyType; // [sp+64h] [bp-24h]@6
  char v14; // [sp+65h] [bp-23h]@6
  int j; // [sp+74h] [bp-14h]@6
  CPlayer *v16; // [sp+90h] [bp+8h]@1
  char v17; // [sp+98h] [bp+10h]@1
  unsigned __int16 v18; // [sp+A0h] [bp+18h]@1
  char v19; // [sp+A8h] [bp+20h]@1

  v19 = byLv;
  v18 = wEffectCode;
  v17 = byAlterCode;
  v16 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v16->m_pPartyMgr )
  {
    v7 = CPartyPlayer::GetPtrPartyMember(v16->m_pPartyMgr);
    if ( v7 )
    {
      *(_DWORD *)szMsg = v16->m_dwObjSerial;
      v9 = v17;
      v10 = v18;
      v11 = v19;
      v12 = CPartyPlayer::GetPopPartyMember(v16->m_pPartyMgr);
      pbyType = 16;
      v14 = 26;
      for ( j = 0; j < v12; ++j )
      {
        if ( v7[j] != v16->m_pPartyMgr )
          CNetProcess::LoadSendMsg(unk_1414F2088, v7[j]->m_wZoneIndex, &pbyType, szMsg, 8u);
      }
    }
  }
}
