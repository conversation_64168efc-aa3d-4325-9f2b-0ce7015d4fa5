/*
 * Function: ?Init@CMapOperation@@QEAA_NXZ
 * Address: 0x140196300
 */

bool __fastcall CMapOperation::Init(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  signed __int64 v4; // rax@6
  unsigned __int8 v5; // cf@8
  unsigned __int64 v6; // rax@8
  CItemStoreManager *v7; // rax@13
  __int64 v8; // [sp+0h] [bp-A8h]@1
  void (__cdecl *pDtor)(void *); // [sp+20h] [bp-88h]@34
  unsigned int v10; // [sp+28h] [bp-80h]@34
  char *v11; // [sp+30h] [bp-78h]@34
  int nInstanceListNum; // [sp+40h] [bp-68h]@13
  CMapData *pLinkShipMap; // [sp+48h] [bp-60h]@19
  CMapData *pLinkPlatformMap; // [sp+50h] [bp-58h]@19
  int j; // [sp+58h] [bp-50h]@22
  CMapData *pLinkMainbaseMap; // [sp+60h] [bp-48h]@24
  unsigned int k; // [sp+68h] [bp-40h]@29
  unsigned int l; // [sp+6Ch] [bp-3Ch]@31
  int count[2]; // [sp+70h] [bp-38h]@6
  CMapData *v20; // [sp+78h] [bp-30h]@13
  void *v21; // [sp+80h] [bp-28h]@10
  __int64 v22; // [sp+88h] [bp-20h]@4
  CMapData *v23; // [sp+90h] [bp-18h]@11
  CMapOperation *v24; // [sp+B0h] [bp+8h]@1

  v24 = this;
  v1 = &v8;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = -2i64;
  if ( CMapDataTable::ReadScript(&v24->m_tblMapData, ".\\Map\\Map_Data.spt") )
  {
    v24->m_nMapNum = CMapDataTable::GetRecordNum(&v24->m_tblMapData);
    *(_QWORD *)count = v24->m_nMapNum;
    v4 = 1504i64 * *(_QWORD *)count;
    if ( !is_mul_ok(0x5E0ui64, *(unsigned __int64 *)count) )
      v4 = -1i64;
    v5 = __CFADD__(v4, 8i64);
    v6 = v4 + 8;
    if ( v5 )
      v6 = -1i64;
    v21 = operator new[](v6);
    if ( v21 )
    {
      *(_DWORD *)v21 = count[0];
      `eh vector constructor iterator'(
        (char *)v21 + 8,
        0x5E0ui64,
        count[0],
        (void (__cdecl *)(void *))CMapData::CMapData,
        (void (__cdecl *)(void *))CMapData::~CMapData);
      v23 = (CMapData *)((char *)v21 + 8);
    }
    else
    {
      v23 = 0i64;
    }
    v20 = v23;
    v24->m_Map = v23;
    nInstanceListNum = 90;
    v7 = CItemStoreManager::Instance();
    if ( CItemStoreManager::Init(v7, v24->m_nMapNum, nInstanceListNum) )
    {
      if ( CMapOperation::LoadMaps(v24) )
      {
        if ( CMapOperation::LoadRegion(v24) )
        {
          pLinkShipMap = CMapOperation::GetMap(v24, "Transport01");
          pLinkPlatformMap = CMapOperation::GetMap(v24, "Platform01");
          if ( pLinkShipMap && pLinkPlatformMap )
          {
            for ( j = 0; j < 3; ++j )
            {
              pLinkMainbaseMap = CMapOperation::GetStartMap(v24, j);
              if ( !pLinkMainbaseMap )
              {
                MyMessageBox("CMapOperation::Init() Error", "if(!pMainBaseMap)");
                return 0;
              }
              if ( !CTransportShip::InitShip(
                      (CTransportShip *)&g_TransportShip[10162 * j],
                      pLinkShipMap,
                      pLinkMainbaseMap,
                      pLinkPlatformMap,
                      j) )
                return 0;
            }
            for ( k = 0; (signed int)k < 3; ++k )
            {
              for ( l = 0; (signed int)l < 2; ++l )
              {
                v24->m_SettlementMapData[k][l] = CMapOperation::GetMap(
                                                   v24,
                                                   (&CMapOperation::ms_szSettlementMapName[2 * (signed int)k])[8 * (signed int)l]);
                if ( !v24->m_SettlementMapData[k][l] )
                {
                  v11 = (&CMapOperation::ms_szSettlementMapName[2 * (signed int)k])[8 * (signed int)l];
                  v10 = l;
                  LODWORD(pDtor) = k;
                  MyMessageBox(
                    "Error",
                    "CMapOperation::Init() : m_SettlementMapData[%d][%d] = GetMap( ms_szSettlementMapName[%d][%d](%s) ) Fail!",
                    k,
                    l);
                  return 0;
                }
              }
            }
            if ( CHolyStoneSystem::InitHolySystem(&g_HolySys) )
              result = CWorldSchedule::Init(&g_WorldSch) != 0;
            else
              result = 0;
          }
          else
          {
            MyMessageBox("CMapOperation::Init() Error", "if(!pShipMap || !pPlatformMap)");
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    MyMessageBox("DatafileInit", "MapSptfile Load Error");
    result = 0;
  }
  return result;
}
