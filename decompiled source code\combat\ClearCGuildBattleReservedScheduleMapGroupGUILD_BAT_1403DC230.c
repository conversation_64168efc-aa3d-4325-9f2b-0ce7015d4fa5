/*
 * Function: ?Clear@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DC230
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  GUILD_BATTLE::CGuildBattleSchedulePool *v4; // rax@11
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_ppkReservedSchedule )
  {
    v7->m_bDone = 0;
    for ( j = 0; j < v7->m_uiMapCnt; ++j )
    {
      if ( v7->m_ppkReservedSchedule[j] )
        GUILD_BATTLE::CGuildBattleReservedSchedule::Clear(v7->m_ppkReservedSchedule[j]);
    }
    v4 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
    GUILD_BATTLE::CGuildBattleSchedulePool::ClearByDayID(v4, v7->m_uiDayInx);
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
