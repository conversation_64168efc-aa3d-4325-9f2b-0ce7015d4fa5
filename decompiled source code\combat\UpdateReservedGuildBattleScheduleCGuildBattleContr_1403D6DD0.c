/*
 * Function: ?UpdateReservedGuildBattleSchedule@CGuildBattleController@@QEAA_NKPEAE@Z
 * Address: 0x1403D6DD0
 */

bool __fastcall CGuildBattleController::UpdateReservedGuildBattleSchedule(CGuildBattleController *this, unsigned int dwSLID, char *byOutData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  unsigned int dwSLIDa; // [sp+38h] [bp+10h]@1
  char *byOutDataa; // [sp+40h] [bp+18h]@1

  byOutDataa = byOutData;
  dwSLIDa = dwSLID;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
  return GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateReservedShedule(v5, dwSLIDa, byOutDataa);
}
