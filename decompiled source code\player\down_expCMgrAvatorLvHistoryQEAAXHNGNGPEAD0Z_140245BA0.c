/*
 * Function: ?down_exp@CMgrAvatorLvHistory@@QEAAXHNGNGPEAD0@Z
 * Address: 0x140245BA0
 */

void __fastcall CMgrAvatorLvHistory::down_exp(CMgrAvatorLvHistory *this, int n, long double dOldExp, unsigned __int16 wOldExpRate, long double dNewExp, unsigned __int16 wNewExpRate, char *pCause, char *pszFileName)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp-20h] [bp-58h]@1
  long double v11; // [sp+0h] [bp-38h]@4
  int v12; // [sp+8h] [bp-30h]@4
  char *v13; // [sp+10h] [bp-28h]@4
  char *v14; // [sp+18h] [bp-20h]@4
  char *v15; // [sp+20h] [bp-18h]@4
  CMgrAvatorLvHistory *v16; // [sp+40h] [bp+8h]@1

  v16 = this;
  v8 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v15 = v16->m_szCurTime;
  v14 = v16->m_szCurDate;
  v13 = pCause;
  v12 = wNewExpRate;
  v11 = dNewExp;
  sprintf(sData_0, "EXP DOWN %.0f(%d) -> %.0f(%d) : %s [%s %s]\r\n\r\n", dOldExp, wOldExpRate);
  CMgrAvatorLvHistory::WriteFile(v16, pszFileName, sData_0);
}
