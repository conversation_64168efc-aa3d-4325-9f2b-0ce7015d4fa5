/*
 * Function: ?GetGold@CCheckSumGuildData@@QEAANXZ
 * Address: 0x1402C12B0
 */

long double __usercall CCheckSumGuildData::GetGold@<xmm0>(CCheckSumGuildData *this@<rcx>, __m128i a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  long double result; // xmm0_8@4
  __int64 v5; // [sp+0h] [bp-58h]@1
  __int128 v6; // [sp+20h] [bp-38h]@4
  char v7; // [sp+34h] [bp-24h]@4
  CCheckSumGuildData *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCheckSumBaseConverter::ProcCode((CCheckSumBaseConverter *)&v7, 7, v8->m_dwGuildSerial, v8->m_dValues[1]);
  _mm_storeu_si128((__m128i *)&v6, a2);
  *(_QWORD *)&result = (unsigned __int128)_mm_loadu_si128((const __m128i *)&v6);
  return result;
}
