/*
 * Function: ?Update_Post@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B4790
 */

bool __fastcall CRFWorldDatabase::Update_Post(CRFWorldDatabase *this, char *szPostQuery)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v6->vfptr, szPostQuery, 1);
}
