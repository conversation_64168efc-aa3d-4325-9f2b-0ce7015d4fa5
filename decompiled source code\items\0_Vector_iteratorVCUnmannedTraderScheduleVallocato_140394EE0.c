/*
 * Function: ??0?$_Vector_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA@XZ
 * Address: 0x140394EE0
 */

void __fastcall std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>((std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)&v4->_Mycont);
}
