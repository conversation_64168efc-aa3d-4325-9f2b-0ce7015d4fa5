/*
 * Function: ?_AddToPacket@CandidateRegister@@AEAA_NPEAVCPlayer@@K@Z
 * Address: 0x1402B6E40
 */

char __usercall CandidateRegister::_AddToPacket@<al>(CandidateRegister *this@<rcx>, CPlayer *pOne@<rdx>, unsigned int dwWinCnt@<r8d>, long double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CandidateMgr *v6; // rax@4
  int v7; // eax@4
  char result; // al@5
  char *v9; // rax@6
  char v10; // al@8
  __int64 v11; // [sp+0h] [bp-38h]@1
  unsigned __int8 v12; // [sp+20h] [bp-18h]@4
  int v13; // [sp+24h] [bp-14h]@4
  CandidateRegister *v14; // [sp+40h] [bp+8h]@1
  CPlayer *v15; // [sp+48h] [bp+10h]@1
  unsigned int v16; // [sp+50h] [bp+18h]@1

  v16 = dwWinCnt;
  v15 = pOne;
  v14 = this;
  v4 = &v11;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  v13 = v14->_kSend[v12].byCnt;
  v6 = CandidateMgr::Instance();
  v7 = CandidateMgr::GetMaxNum(v6);
  if ( v13 < v7 )
  {
    v14->_kSend[v12].Candidacy[v14->_kSend[v12].byCnt].byGrade = v15->m_Param.m_byPvPGrade;
    CPlayerDB::GetPvPPoint(&v15->m_Param);
    v14->_kSend[v12].Candidacy[v14->_kSend[v12].byCnt].dPvpPoint = a4;
    v14->_kSend[v12].Candidacy[v14->_kSend[v12].byCnt].dwWinCnt = v16;
    v9 = CPlayerDB::GetCharNameW(&v15->m_Param);
    strcpy_s(v14->_kSend[v12].Candidacy[v14->_kSend[v12].byCnt].wszAvatorName, 0x11ui64, v9);
    if ( v15->m_Param.m_pGuild )
      strcpy_s(
        v14->_kSend[v12].Candidacy[v14->_kSend[v12].byCnt].wszGuildName,
        0x11ui64,
        v15->m_Param.m_pGuild->m_wszName);
    ++v14->_kSend[v12].byCnt;
    v10 = CPlayerDB::GetRaceCode(&v15->m_Param);
    CandidateRegister::_SortCandidacyByPvpPoint(v14, v10);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
