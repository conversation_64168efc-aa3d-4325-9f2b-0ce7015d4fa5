/*
 * Function: ?CreateTaskPool@CBossMonsterScheduleSystem@@IEAA_NXZ
 * Address: 0x140419A80
 */

char __fastcall CBossMonsterScheduleSystem::CreateTaskPool(CBossMonsterScheduleSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CBossMonsterScheduleSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ((void (__fastcall *)(signed __int64, signed __int64))v5->m_MSG_POOL.vfptr->ReAllocPool)(
    (signed __int64)&v5->m_MSG_POOL,
    2000i64);
  return 1;
}
