/*
 * Function: j_??$_Copy_opt@PEAVCMoveMapLimitRightInfo@@PEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F128
 */

CMoveMapLimitRightInfo *__fastcall std::_Copy_opt<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::random_access_iterator_tag>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
