/*
 * Function: ?destroy@?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@QEAAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x1405A5260
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::destroy(__int64 a1, __int64 a2)
{
  return std::_Destroy<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(a2);
}
