/*
 * Function: ?SendMsg_FcitemInform@CPlayer@@QEAAXGK@Z
 * Address: 0x1400D6D80
 */

void __fastcall CPlayer::SendMsg_FcitemInform(CPlayer *this, unsigned __int16 wItemSerial, unsigned int dwNewStat)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  unsigned int v7; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(_WORD *)szMsg = wItemSerial;
  v7 = dwNewStat;
  pbyType = 3;
  v9 = 44;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 6u);
}
