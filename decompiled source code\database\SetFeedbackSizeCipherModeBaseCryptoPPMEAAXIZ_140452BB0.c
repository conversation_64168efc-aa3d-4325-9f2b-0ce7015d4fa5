/*
 * Function: ?SetFeedbackSize@CipherModeBase@CryptoPP@@MEAAXI@Z
 * Address: 0x140452BB0
 */

void __fastcall CryptoPP::CipherModeBase::SetFeedbackSize(CryptoPP::CipherModeBase *this, unsigned int feedbackSize)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-B8h]@1
  CryptoPP::InvalidArgument v5; // [sp+20h] [bp-98h]@6
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@6
  unsigned __int8 v7; // [sp+A0h] [bp-18h]@6
  __int64 v8; // [sp+A8h] [bp-10h]@4
  CryptoPP::CipherModeBase *v9; // [sp+C0h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = -2i64;
  if ( feedbackSize && feedbackSize != CryptoPP::CipherModeBase::BlockSize(v9) )
  {
    memset(&v7, 0, sizeof(v7));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "CipherModeBase: feedback size cannot be specified for this cipher mode",
      v7);
    CryptoPP::InvalidArgument::InvalidArgument(&v5, &s);
    CxxThrowException_0(&v5, &TI3_AVInvalidArgument_CryptoPP__);
  }
}
