/*
 * Function: ?UncheckedSet<PERSON><PERSON>@CBC_CTS_Encryption@CryptoPP@@MEAAXPEBEIAEBVNameValuePairs@2@@Z
 * Address: 0x14055B720
 */

void __fastcall CryptoPP::CBC_CTS_Encryption::UncheckedSetKey(CryptoPP::CBC_CTS_Encryption *this, const unsigned __int8 *a2, unsigned int a3, const struct CryptoPP::NameValuePairs *a4)
{
  CryptoPP::Name *v4; // rcx@1
  const char *v5; // rax@1
  char *v6; // rax@1
  CryptoPP::CBC_CTS_Encryption *v7; // [sp+30h] [bp+8h]@1
  struct CryptoPP::NameValuePairs *v8; // [sp+48h] [bp+20h]@1

  v8 = (struct CryptoPP::NameValuePairs *)a4;
  v7 = this;
  CryptoPP::BlockOrientedCipherModeBase::UncheckedSetKey(
    (CryptoPP::BlockOrientedCipherModeBase *)&this->vfptr,
    a2,
    a3,
    a4);
  v5 = CryptoPP::Name::StolenIV(v4);
  LODWORD(v6) = CryptoPP::NameValuePairs::GetValueWithDefault<unsigned char *>(v8, v5, 0i64);
  v7->m_buffer.m_ptr = v6;
}
