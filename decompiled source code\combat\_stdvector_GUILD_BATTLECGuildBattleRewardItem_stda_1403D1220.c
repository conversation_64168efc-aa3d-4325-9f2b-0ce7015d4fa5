/*
 * Function: _std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::erase_::_1_::dtor$1
 * Address: 0x1403D1220
 */

void __fastcall std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::erase_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(*(std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > **)(a2 + 96));
}
