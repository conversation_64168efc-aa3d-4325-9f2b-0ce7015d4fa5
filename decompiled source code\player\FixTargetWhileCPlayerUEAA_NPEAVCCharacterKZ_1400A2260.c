/*
 * Function: ?FixTarget<PERSON>hile@CPlayer@@UEAA_NPEAVCCharacter@@K@Z
 * Address: 0x1400A2260
 */

char __fastcall CPlayer::FixTargetWhile(CPlayer *this, CCharacter *pkTarget, unsigned int dwMiliSecond)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayer::pc_SetTargetObjectRequest(v7, (CGameObject *)&pkTarget->vfptr, pkTarget->m_dwObjSerial, 1);
  return 1;
}
