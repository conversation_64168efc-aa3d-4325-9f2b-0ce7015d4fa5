/*
 * Function: ?dev_loot_fullitem@CPlayer@@QEAA_NE@Z
 * Address: 0x1400BE510
 */

char __fastcall CPlayer::dev_loot_fullitem(CPlayer *this, char byLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-88h]@1
  int nUpNum; // [sp+20h] [bp-68h]@10
  char Dest; // [sp+38h] [bp-50h]@4
  char v8; // [sp+39h] [bp-4Fh]@4
  _base_fld *v9; // [sp+58h] [bp-30h]@4
  int j; // [sp+60h] [bp-28h]@6
  unsigned __int64 v11; // [sp+70h] [bp-18h]@4
  CPlayer *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v8, 0, 8ui64);
  sprintf(&Dest, "%s_%d", v12->m_Param.m_pClassData->m_strCode, (unsigned __int8)byLv);
  v9 = CRecordData::GetRecord(&stru_1799C8C50, &Dest);
  if ( v9 )
  {
    for ( j = 0; j < 30; ++j )
    {
      if ( *(_DWORD *)&v9[j + 1].m_strCode[60] > 0 )
      {
        nUpNum = 0;
        CPlayer::dev_loot_item(v12, (char *)&v9[j + 1], *(_DWORD *)&v9[j + 1].m_strCode[60], 0i64, 0);
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
