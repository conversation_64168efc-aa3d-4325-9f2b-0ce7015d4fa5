/*
 * Function: ?Des<PERSON>y@CCharacter@@QEAA_NXZ
 * Address: 0x140172700
 */

bool __fastcall CCharacter::Des<PERSON>y(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CCharacter *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _effect_parameter::InitEffParam(&v5->m_EP);
  memset_0(v5->m_SFCont, 0, 0x300ui64);
  memset_0(v5->m_SFContAura, 0, 0x300ui64);
  v5->m_bLastContEffectUpdate = 0;
  v5->m_wLastContEffect = -1;
  return CGameObject::Destroy((CGameObject *)&v5->vfptr);
}
