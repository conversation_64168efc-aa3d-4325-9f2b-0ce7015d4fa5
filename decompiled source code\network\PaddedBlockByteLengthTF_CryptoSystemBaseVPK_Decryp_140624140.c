/*
 * Function: ?PaddedBlockByteLength@?$TF_CryptoSystemBase@VPK_Decryptor@CryptoPP@@V?$TF_Base@VTrapdoorFunctionInverse@CryptoPP@@VPK_EncryptionMessageEncodingMethod@2@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x140624140
 */

unsigned __int64 __fastcall CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::TF_Base<CryptoPP::TrapdoorFunctionInverse,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockByteLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Decryptor,CryptoPP::TF_Base<CryptoPP::TrapdoorFunctionInverse,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
