/*
 * Function: ?DE_Potion_CharReName@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017EE00
 */

char __fastcall DE_Potion_CharReName(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v7; // rax@8
  int v8; // eax@8
  __int64 v9; // [sp+0h] [bp-A8h]@1
  CCharacter *v10; // [sp+30h] [bp-78h]@8
  _qry_case_character_rename Dst; // [sp+48h] [bp-60h]@8
  unsigned __int64 v12; // [sp+90h] [bp-18h]@4
  CCharacter *v13; // [sp+B0h] [bp+8h]@1

  v13 = pActChar;
  v4 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v13 )
  {
    if ( v13->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v10 = v13;
      _qry_case_character_rename::_qry_case_character_rename(&Dst);
      Dst.dwCharSerial = *(_DWORD *)(*(_QWORD *)&v10[1].m_nScreenPos[0] + 92i64);
      Dst.dwAlreadySerial = -1;
      memcpy_0(&Dst.ItemInfo, (char *)&v10[25].m_SFContAura[0][5].m_wDurSec + 3, 4ui64);
      strcpy_s(Dst.wszCharName, 0x11ui64, (const char *)&v10[25].m_SFContAura[0][5].m_dwEffSerial + 3);
      v7 = CPlayerDB::GetCharNameW((CPlayerDB *)&v10[1].m_fOldPos[2]);
      strcpy_s(Dst.wszOldName, 0x11ui64, v7);
      v8 = _qry_case_character_rename::size(&Dst);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -110, (char *)&Dst, v8);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
