/*
 * Function: j_?_Prevnode@?$list@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@KAAEAPEAU_Node@?$_List_nod@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@2@PEAU342@@Z
 * Address: 0x1400055BF
 */

std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node **__fastcall std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Prevnode(std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this, std::_List_nod<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Node *_Pnode)
{
  return std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Prevnode(
           this,
           _Pnode);
}
