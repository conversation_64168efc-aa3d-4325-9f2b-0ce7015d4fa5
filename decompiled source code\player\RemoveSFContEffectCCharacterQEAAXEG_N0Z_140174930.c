/*
 * Function: ?RemoveSFContEffect@CCharacter@@QEAAXEG_N0@Z
 * Address: 0x140174930
 */

void __fastcall CCharacter::RemoveSFContEffect(CCharacter *this, char byContCode, unsigned __int16 wListIndex, bool bInit, bool bAura)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CGameObjectVtbl *v7; // rax@23
  __int64 v8; // [sp+0h] [bp-68h]@1
  bool v9; // [sp+20h] [bp-48h]@23
  bool *v10; // [sp+30h] [bp-38h]@4
  _base_fld *v11; // [sp+38h] [bp-30h]@8
  char *v12; // [sp+40h] [bp-28h]@8
  int j; // [sp+48h] [bp-20h]@12
  char *v14; // [sp+50h] [bp-18h]@14
  int v15; // [sp+58h] [bp-10h]@15
  bool v16; // [sp+5Ch] [bp-Ch]@23
  CCharacter *v17; // [sp+70h] [bp+8h]@1
  char v18; // [sp+78h] [bp+10h]@1
  unsigned __int16 v19; // [sp+80h] [bp+18h]@1
  bool v20; // [sp+88h] [bp+20h]@1

  v20 = bInit;
  v19 = wListIndex;
  v18 = byContCode;
  v17 = this;
  v5 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = 0i64;
  if ( bAura )
    v10 = &v17->m_SFContAura[(unsigned __int8)byContCode][wListIndex].m_bExist;
  else
    v10 = &v17->m_SFCont[(unsigned __int8)byContCode][wListIndex].m_bExist;
  if ( *v10 )
  {
    v11 = CRecordData::GetRecord(&stru_1799C8410 + v10[1], *((_WORD *)v10 + 1));
    v12 = 0i64;
    if ( v10[1] == 1 )
      v12 = &v11[12].m_strCode[56];
    else
      v12 = &v11[13].m_strCode[48];
    if ( v12 )
    {
      for ( j = 0; j < 5; ++j )
      {
        v14 = &v12[36 * j];
        if ( *(_DWORD *)v14 == -1 )
          break;
        v15 = *(_DWORD *)v14;
        if ( v15 )
        {
          if ( v15 == 1 )
          {
            _effect_parameter::SetEff_Plus(&v17->m_EP, *((_DWORD *)v14 + 1), *(float *)&v14[4 * (v10[4] - 1) + 8], 0);
          }
          else if ( v15 == 2 )
          {
            _effect_parameter::SetEff_State(&v17->m_EP, *((_DWORD *)v14 + 1), 0);
          }
        }
        else
        {
          _effect_parameter::SetEff_Rate(&v17->m_EP, *((_DWORD *)v14 + 1), *(float *)&v14[4 * (v10[4] - 1) + 8], 0);
        }
      }
    }
    *v10 = 0;
    v16 = v20 == 0;
    v7 = v17->vfptr;
    v9 = bAura;
    ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD, _QWORD))v7->SFContDelMessage)(
      v17,
      (unsigned __int8)v18,
      (unsigned __int8)v19,
      v20 == 0);
    v17->m_bLastContEffectUpdate = 1;
  }
}
