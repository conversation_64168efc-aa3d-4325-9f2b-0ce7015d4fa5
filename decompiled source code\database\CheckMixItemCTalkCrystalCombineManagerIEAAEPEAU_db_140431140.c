/*
 * Function: ?CheckMixItem@CTalkCrystalCombineManager@@IEAAEPEAU_db_con@_STORAGE_LIST@@PEAHPEAEPEAG1@Z
 * Address: 0x140431140
 */

char __fastcall CTalkCrystalCombineManager::CheckMixItem(CTalkCrystalCombineManager *this, _STORAGE_LIST::_db_con *pItem, int *pMixIndex, char *pbyTableCode, unsigned __int16 *pwItemIndex, int *pnNeedItemCount)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@10
  __int64 v9; // [sp+0h] [bp-48h]@1
  _base_fld *v10; // [sp+20h] [bp-28h]@12
  int v11; // [sp+28h] [bp-20h]@16
  unsigned __int16 *v12; // [sp+30h] [bp-18h]@18
  CTalkCrystalCombineManager *v13; // [sp+50h] [bp+8h]@1
  int *v14; // [sp+60h] [bp+18h]@1
  char *v15; // [sp+68h] [bp+20h]@1

  v15 = pbyTableCode;
  v14 = pMixIndex;
  v13 = this;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v13->m_pCurrentPlayer && pItem && pMixIndex && pnNeedItemCount && pwItemIndex && pbyTableCode )
  {
    if ( pItem->m_byTableCode == 18 )
    {
      v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, pItem->m_wItemIndex);
      if ( v10 )
      {
        if ( *(_DWORD *)&v10[4].m_strCode[4] >= 0 )
        {
          *v14 = *(_DWORD *)&v10[4].m_strCode[4];
          v11 = GetItemTableCode(&v10[4].m_strCode[12]);
          if ( v11 == -1 )
          {
            result = 8;
          }
          else
          {
            v12 = (unsigned __int16 *)CRecordData::GetRecordByHash(
                                        (CRecordData *)&unk_1799C6AA0 + v11,
                                        &v10[4].m_strCode[12],
                                        2,
                                        5);
            if ( v12 )
            {
              *v15 = v11;
              *pwItemIndex = *v12;
              *pnNeedItemCount = (signed int)ffloor(*(float *)&v10[4].m_strCode[8]);
              if ( *pnNeedItemCount >= 0 )
                result = 0;
              else
                result = 16;
            }
            else
            {
              result = 8;
            }
          }
        }
        else
        {
          result = 16;
        }
      }
      else
      {
        result = 4;
      }
    }
    else
    {
      result = 12;
    }
  }
  else
  {
    result = 4;
  }
  return result;
}
