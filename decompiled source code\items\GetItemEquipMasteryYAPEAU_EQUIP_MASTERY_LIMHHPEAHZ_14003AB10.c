/*
 * Function: ?GetItemEquipMastery@@YAPEAU_EQUIP_MASTERY_LIM@@HHPEAH@Z
 * Address: 0x14003AB10
 */

_EQUIP_MASTERY_LIM *__fastcall GetItemEquipMastery(int nTableCode, int nItemIndex, int *pnLimNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  CRecordData *v7; // [sp+20h] [bp-28h]@4
  _base_fld *v8; // [sp+28h] [bp-20h]@8
  _base_fld *v9; // [sp+30h] [bp-18h]@11
  int v10; // [sp+38h] [bp-10h]@4
  int v11; // [sp+50h] [bp+8h]@1
  int *v12; // [sp+60h] [bp+18h]@1

  v12 = pnLimNum;
  v11 = nTableCode;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = &s_ptblItemData[v11];
  v10 = v11;
  if ( v11 >= 0 )
  {
    if ( v10 <= 5 )
      goto LABEL_8;
    if ( v10 == 6 )
    {
      v9 = CRecordData::GetRecord(v7, nItemIndex);
      if ( v9 )
      {
        *v12 = 2;
        return (_EQUIP_MASTERY_LIM *)&v9[8].m_strCode[20];
      }
    }
    else if ( v10 == 7 )
    {
LABEL_8:
      v8 = CRecordData::GetRecord(v7, nItemIndex);
      if ( v8 )
      {
        *v12 = 2;
        return (_EQUIP_MASTERY_LIM *)&v8[4].m_strCode[20];
      }
      return 0i64;
    }
  }
  return 0i64;
}
