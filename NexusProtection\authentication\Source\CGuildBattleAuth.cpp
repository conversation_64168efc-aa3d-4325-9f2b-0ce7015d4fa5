/**
 * @file CGuildBattleAuth.cpp
 * @brief Guild Battle Authentication System Implementation
 * 
 * Provides secure authentication for guild battle login operations and management.
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "CGuildBattleAuth.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cstdarg>

namespace NexusProtection::Authentication {

    // Global instance
    static std::unique_ptr<CGuildBattleAuth> g_pGuildBattleAuth = nullptr;
    static std::mutex g_instanceMutex;

    // GuildBattleMember implementation

    bool GuildBattleMember::IsValid() const {
        return characterSerial > 0 && guildSerial > 0 && sessionId > 0;
    }

    std::string GuildBattleMember::ToString() const {
        std::ostringstream oss;
        oss << "GuildBattleMember{";
        oss << "CharSerial: " << characterSerial << ", ";
        oss << "GuildSerial: " << guildSerial << ", ";
        oss << "SessionId: " << sessionId << ", ";
        oss << "PvpPoint: " << pvpPoint << ", ";
        oss << "LoggedIn: " << (isLoggedIn ? "Yes" : "No");
        oss << "}";
        return oss.str();
    }

    void GuildBattleMember::UpdateActivity() {
        lastActivity = std::chrono::steady_clock::now();
    }

    // GuildBattleField implementation

    bool GuildBattleField::IsValid() const {
        return fieldId > 0 && !fieldName.empty() && maxParticipants > 0;
    }

    std::string GuildBattleField::ToString() const {
        std::ostringstream oss;
        oss << "GuildBattleField{";
        oss << "Id: " << fieldId << ", ";
        oss << "Name: " << fieldName << ", ";
        oss << "Participants: " << currentParticipants << "/" << maxParticipants << ", ";
        oss << "Active: " << (isActive ? "Yes" : "No");
        oss << "}";
        return oss.str();
    }

    // CGuildBattleGuild implementation

    CGuildBattleGuild::CGuildBattleGuild() {
        m_members.fill(GuildBattleMember{});
    }

    bool CGuildBattleGuild::Initialize(uint32_t guildSerial, const std::string& guildName) {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        m_guildSerial = guildSerial;
        m_guildName = guildName;
        m_currentMemberCount = 0;
        m_members.fill(GuildBattleMember{});
        
        return true;
    }

    void CGuildBattleGuild::Shutdown() {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        m_guildSerial = 0;
        m_guildName.clear();
        m_currentMemberCount = 0;
        m_members.fill(GuildBattleMember{});
    }

    int CGuildBattleGuild::GetMember(uint32_t characterSerial) const {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        for (size_t i = 0; i < m_currentMemberCount; ++i) {
            if (m_members[i].characterSerial == characterSerial) {
                return static_cast<int>(i);
            }
        }
        
        return -1;
    }

    bool CGuildBattleGuild::AddMember(const GuildBattleMember& member) {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        if (m_currentMemberCount >= MAX_GUILD_MEMBERS) {
            return false;
        }
        
        // Check if member already exists
        for (size_t i = 0; i < m_currentMemberCount; ++i) {
            if (m_members[i].characterSerial == member.characterSerial) {
                return false; // Member already exists
            }
        }
        
        m_members[m_currentMemberCount] = member;
        m_currentMemberCount++;
        
        return true;
    }

    bool CGuildBattleGuild::RemoveMember(uint32_t characterSerial) {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        for (size_t i = 0; i < m_currentMemberCount; ++i) {
            if (m_members[i].characterSerial == characterSerial) {
                // Shift remaining members
                for (size_t j = i; j < m_currentMemberCount - 1; ++j) {
                    m_members[j] = m_members[j + 1];
                }
                m_currentMemberCount--;
                m_members[m_currentMemberCount] = GuildBattleMember{};
                return true;
            }
        }
        
        return false;
    }

    bool CGuildBattleGuild::LoginMember(int sessionId, uint32_t characterSerial, uint8_t guildBattleNumber,
                                       const std::string& destGuildName, uint32_t battleId) {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        int memberIndex = -1;
        for (size_t i = 0; i < m_currentMemberCount; ++i) {
            if (m_members[i].characterSerial == characterSerial) {
                memberIndex = static_cast<int>(i);
                break;
            }
        }
        
        if (memberIndex >= 0) {
            // Existing member login
            m_members[memberIndex].sessionId = sessionId;
            m_members[memberIndex].isLoggedIn = true;
            m_members[memberIndex].UpdateActivity();
            
            // Send notifications and move member (equivalent to original logic)
            SendNotifications(memberIndex);
            MoveMember(memberIndex, battleId);
            
            std::cout << "[INFO] Guild member logged in: " << characterSerial 
                      << " to battle: " << battleId << std::endl;
            return true;
        } else if (guildBattleNumber > m_currentMemberCount) {
            // New member join request
            return AskJoin(sessionId, destGuildName);
        }
        
        return false;
    }

    size_t CGuildBattleGuild::GetMemberCount() const {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        return m_currentMemberCount;
    }

    size_t CGuildBattleGuild::GetLoggedInMemberCount() const {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        size_t count = 0;
        for (size_t i = 0; i < m_currentMemberCount; ++i) {
            if (m_members[i].isLoggedIn) {
                count++;
            }
        }
        
        return count;
    }

    bool CGuildBattleGuild::AskJoin(int sessionId, const std::string& destGuildName) {
        // Implementation for join request (equivalent to original AskJoin)
        std::cout << "[INFO] Guild join request from session: " << sessionId 
                  << " to guild: " << destGuildName << std::endl;
        return true;
    }

    bool CGuildBattleGuild::MoveMember(int memberIndex, uint32_t battleId) {
        if (memberIndex < 0 || memberIndex >= static_cast<int>(m_currentMemberCount)) {
            return false;
        }
        
        // Implementation for moving member to battle field
        std::cout << "[INFO] Moving member " << memberIndex 
                  << " to battle field: " << battleId << std::endl;
        return true;
    }

    bool CGuildBattleGuild::SendNotifications(int memberIndex) {
        if (memberIndex < 0 || memberIndex >= static_cast<int>(m_currentMemberCount)) {
            return false;
        }
        
        // Implementation for sending notifications (equivalent to original)
        // SendOhterNotifyCommitteeMemberPosition and SendSelfNotifyCommitteeMemberPositionList
        std::cout << "[INFO] Sending notifications for member: " << memberIndex << std::endl;
        return true;
    }

    bool CGuildBattleGuild::IsValid() const {
        return m_guildSerial > 0 && !m_guildName.empty();
    }

    std::string CGuildBattleGuild::ToString() const {
        std::ostringstream oss;
        oss << "CGuildBattleGuild{";
        oss << "Serial: " << m_guildSerial << ", ";
        oss << "Name: " << m_guildName << ", ";
        oss << "Members: " << GetMemberCount() << "/" << MAX_GUILD_MEMBERS << ", ";
        oss << "LoggedIn: " << GetLoggedInMemberCount();
        oss << "}";
        return oss.str();
    }

    int CGuildBattleGuild::FindFreeMemberSlot() const {
        if (m_currentMemberCount >= MAX_GUILD_MEMBERS) {
            return -1;
        }
        return static_cast<int>(m_currentMemberCount);
    }

    bool CGuildBattleGuild::ValidateMemberLogin(uint32_t characterSerial) const {
        return characterSerial > 0;
    }

    // CGuildBattleLogger implementation

    CGuildBattleLogger::CGuildBattleLogger() = default;

    void CGuildBattleLogger::Log(const char* format, ...) {
        std::lock_guard<std::mutex> lock(m_logMutex);

        va_list args;
        va_start(args, format);

        char buffer[1024];
        vsnprintf(buffer, sizeof(buffer), format, args);

        va_end(args);

        WriteLog(std::string(buffer));
    }

    void CGuildBattleLogger::LogLogin(int sessionId, uint32_t characterSerial, uint32_t guildSerial,
                                     const std::string& guildName, uint32_t battleId) {
        std::ostringstream oss;
        oss << "Guild Battle Login - Session: " << sessionId 
            << ", Character: " << characterSerial 
            << ", Guild: " << guildSerial << " (" << guildName << ")"
            << ", Battle: " << battleId;
        WriteLog(oss.str());
    }

    void CGuildBattleLogger::LogJoinRequest(int sessionId, const std::string& guildName) {
        std::ostringstream oss;
        oss << "Guild Battle Join Request - Session: " << sessionId 
            << ", Target Guild: " << guildName;
        WriteLog(oss.str());
    }

    void CGuildBattleLogger::LogError(const std::string& error, int sessionId) {
        std::ostringstream oss;
        oss << "Guild Battle Error";
        if (sessionId > 0) {
            oss << " (Session: " << sessionId << ")";
        }
        oss << " - " << error;
        WriteLog(oss.str());
    }

    void CGuildBattleLogger::WriteLog(const std::string& message) {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::tm tm_buf;
        #ifdef _WIN32
            localtime_s(&tm_buf, &time_t);
            auto* tm = &tm_buf;
        #else
            auto* tm = std::localtime(&time_t);
        #endif
        
        std::cout << "[" << std::put_time(tm, "%Y-%m-%d %H:%M:%S") << "] " 
                  << "[GUILD_BATTLE] " << message << std::endl;
    }

    // CGuildBattleAuth implementation

    CGuildBattleAuth::CGuildBattleAuth() {
        m_statistics.startTime = std::chrono::steady_clock::now();
        m_logger = std::make_unique<CGuildBattleLogger>();
    }

    CGuildBattleAuth::~CGuildBattleAuth() {
        Shutdown();
    }

    bool CGuildBattleAuth::Initialize() {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        if (m_isInitialized) {
            return true;
        }

        try {
            // Clear any existing data
            m_battles.clear();
            m_guilds.clear();
            m_nextBattleId = 1;

            m_isOperational = true;
            m_isInitialized = true;

            std::cout << "[INFO] CGuildBattleAuth initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to initialize CGuildBattleAuth: " << e.what() << std::endl;
            return false;
        }
    }

    void CGuildBattleAuth::Shutdown() {
        std::lock_guard<std::mutex> battleLock(m_battlesMutex);
        std::lock_guard<std::mutex> guildLock(m_guildsMutex);

        if (!m_isInitialized) {
            return;
        }

        // Shutdown all guilds
        for (auto& pair : m_guilds) {
            if (pair.second) {
                pair.second->Shutdown();
            }
        }

        // Clear all data
        m_battles.clear();
        m_guilds.clear();

        m_isOperational = false;
        m_isInitialized = false;

        std::cout << "[INFO] CGuildBattleAuth shutdown completed" << std::endl;
    }

    bool CGuildBattleAuth::LoadConfiguration() {
        // Load configuration from file or database
        // For now, use default configuration
        return true;
    }

    uint32_t CGuildBattleAuth::CreateBattle(uint32_t guild1Serial, uint32_t guild2Serial) {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        uint32_t battleId = m_nextBattleId++;
        auto battle = std::make_unique<GuildBattle>(battleId, guild1Serial, guild2Serial);
        battle->field = std::make_unique<GuildBattleField>(battleId, "Battle Field " + std::to_string(battleId), 100);
        battle->state = GuildBattleState::Ready;
        battle->isActive = true;

        m_battles[battleId] = std::move(battle);

        UpdateStatistics(true);
        std::cout << "[INFO] Created guild battle: " << battleId
                  << " between guilds: " << guild1Serial << " and " << guild2Serial << std::endl;

        return battleId;
    }

    bool CGuildBattleAuth::DestroyBattle(uint32_t battleId) {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        auto it = m_battles.find(battleId);
        if (it != m_battles.end()) {
            m_battles.erase(it);
            std::cout << "[INFO] Destroyed guild battle: " << battleId << std::endl;
            return true;
        }

        return false;
    }

    bool CGuildBattleAuth::GetBattleByGuildSerial(uint32_t guildSerial, uint32_t& battleId) const {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        for (const auto& pair : m_battles) {
            const auto& battle = pair.second;
            if (battle->guild1Serial == guildSerial || battle->guild2Serial == guildSerial) {
                battleId = battle->battleId;
                return true;
            }
        }

        return false;
    }

    GuildBattleAuthResult CGuildBattleAuth::AuthenticateGuildBattleLogin(int sessionId, uint32_t guildSerial, uint32_t characterSerial) {
        try {
            if (!ValidateParameters(sessionId, guildSerial, characterSerial)) {
                UpdateStatistics(false);
                return GuildBattleAuthResult::InvalidParameters;
            }

            // Find battle for this guild (equivalent to GetBattleByGuildSerial)
            uint32_t battleId = 0;
            if (!GetBattleByGuildSerial(guildSerial, battleId)) {
                UpdateStatistics(false);
                LogAuthenticationEvent("Guild battle not found", sessionId, guildSerial, false);
                return GuildBattleAuthResult::BattleNotFound;
            }

            GuildBattle* battle = GetBattle(battleId);
            if (!battle) {
                UpdateStatistics(false);
                return GuildBattleAuthResult::BattleNotFound;
            }

            // Process based on battle state (equivalent to original logic)
            bool success = false;
            if (IsBattleReady(battleId)) {
                // Battle is ready - process join request
                success = ProcessJoinRequest(battle, sessionId, guildSerial, characterSerial);
            } else if (IsBattleActive(battleId)) {
                // Battle is active - process login
                success = ProcessBattleLogin(battle, sessionId, guildSerial, characterSerial);
            } else {
                UpdateStatistics(false);
                return GuildBattleAuthResult::BattleNotReady;
            }

            UpdateStatistics(success);
            LogAuthenticationEvent("Guild battle authentication", sessionId, guildSerial, success);

            return success ? GuildBattleAuthResult::Success : GuildBattleAuthResult::SystemError;

        } catch (const std::exception& e) {
            UpdateStatistics(false);
            LogAuthenticationEvent("Guild battle authentication error: " + std::string(e.what()),
                                 sessionId, guildSerial, false);
            return GuildBattleAuthResult::SystemError;
        }
    }

    GuildBattleAuthResult CGuildBattleAuth::AuthenticateGuildMemberLogin(int sessionId, uint32_t characterSerial,
                                                                        uint8_t guildBattleNumber, const std::string& destGuildName,
                                                                        uint32_t battleId) {
        try {
            if (!ValidateParameters(sessionId, 0, characterSerial)) {
                UpdateStatistics(false);
                return GuildBattleAuthResult::InvalidParameters;
            }

            GuildBattle* battle = GetBattle(battleId);
            if (!battle) {
                UpdateStatistics(false);
                return GuildBattleAuthResult::BattleNotFound;
            }

            // Find the appropriate guild for this member
            CGuildBattleGuild* guild = nullptr;
            if (auto guild1 = GetGuild(battle->guild1Serial)) {
                if (guild1->GetMember(characterSerial) >= 0) {
                    guild = guild1;
                }
            }
            if (!guild) {
                auto guild2 = GetGuild(battle->guild2Serial);
                if (guild2 && guild2->GetMember(characterSerial) >= 0) {
                    guild = guild2;
                }
            }

            if (!guild) {
                UpdateStatistics(false);
                return GuildBattleAuthResult::MemberNotFound;
            }

            // Process member login
            bool success = guild->LoginMember(sessionId, characterSerial, guildBattleNumber, destGuildName, battleId);

            if (success && m_logger) {
                m_logger->LogLogin(sessionId, characterSerial, guild->GetGuildSerial(), guild->GetGuildName(), battleId);
            }

            UpdateStatistics(success);

            return success ? GuildBattleAuthResult::Success : GuildBattleAuthResult::SystemError;

        } catch (const std::exception& e) {
            UpdateStatistics(false);
            if (m_logger) {
                m_logger->LogError("Guild member authentication error: " + std::string(e.what()), sessionId);
            }
            return GuildBattleAuthResult::SystemError;
        }
    }

    bool CGuildBattleAuth::SetBattleState(uint32_t battleId, GuildBattleState state) {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        auto* battle = GetBattle(battleId);
        if (battle) {
            battle->state = state;
            return true;
        }

        return false;
    }

    GuildBattleState CGuildBattleAuth::GetBattleState(uint32_t battleId) const {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        const auto* battle = GetBattle(battleId);
        return battle ? battle->state : GuildBattleState::None;
    }

    bool CGuildBattleAuth::IsBattleReady(uint32_t battleId) const {
        GuildBattleState state = GetBattleState(battleId);
        return state == GuildBattleState::Ready || state == GuildBattleState::Count;
    }

    bool CGuildBattleAuth::IsBattleActive(uint32_t battleId) const {
        GuildBattleState state = GetBattleState(battleId);
        return state == GuildBattleState::InBattle;
    }

    bool CGuildBattleAuth::RegisterGuild(uint32_t guildSerial, const std::string& guildName) {
        std::lock_guard<std::mutex> lock(m_guildsMutex);

        auto guild = std::make_unique<CGuildBattleGuild>();
        if (guild->Initialize(guildSerial, guildName)) {
            m_guilds[guildSerial] = std::move(guild);
            std::cout << "[INFO] Registered guild: " << guildSerial << " (" << guildName << ")" << std::endl;
            return true;
        }

        return false;
    }

    bool CGuildBattleAuth::UnregisterGuild(uint32_t guildSerial) {
        std::lock_guard<std::mutex> lock(m_guildsMutex);

        auto it = m_guilds.find(guildSerial);
        if (it != m_guilds.end()) {
            it->second->Shutdown();
            m_guilds.erase(it);
            std::cout << "[INFO] Unregistered guild: " << guildSerial << std::endl;
            return true;
        }

        return false;
    }

    CGuildBattleGuild* CGuildBattleAuth::GetGuild(uint32_t guildSerial) {
        std::lock_guard<std::mutex> lock(m_guildsMutex);

        auto it = m_guilds.find(guildSerial);
        return (it != m_guilds.end()) ? it->second.get() : nullptr;
    }

    size_t CGuildBattleAuth::GetActiveBattleCount() const {
        std::lock_guard<std::mutex> lock(m_battlesMutex);

        size_t count = 0;
        for (const auto& pair : m_battles) {
            if (pair.second->isActive) {
                count++;
            }
        }

        return count;
    }

    void CGuildBattleAuth::ResetStatistics() {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics = Statistics{};
        m_statistics.startTime = std::chrono::steady_clock::now();
    }

    bool CGuildBattleAuth::LogInCNormalGuildBattle_Legacy(int sessionId, uint32_t guildSerial, uint32_t characterSerial) {
        GuildBattleAuthResult result = AuthenticateGuildBattleLogin(sessionId, guildSerial, characterSerial);
        return result == GuildBattleAuthResult::Success;
    }

    bool CGuildBattleAuth::LogInCNormalGuildBattleGuild_Legacy(int sessionId, uint32_t characterSerial,
                                                             uint8_t guildBattleNumber, const std::string& destGuildName,
                                                             uint32_t battleId) {
        GuildBattleAuthResult result = AuthenticateGuildMemberLogin(sessionId, characterSerial, guildBattleNumber, destGuildName, battleId);
        return result == GuildBattleAuthResult::Success;
    }

    bool CGuildBattleAuth::LogInCNormalGuildBattleManager_Legacy(int sessionId, uint32_t guildSerial, uint32_t characterSerial) {
        // Equivalent to original CNormalGuildBattleManager::LogIn logic
        if (guildSerial == static_cast<uint32_t>(-1)) {
            return false; // Invalid guild serial
        }

        return LogInCNormalGuildBattle_Legacy(sessionId, guildSerial, characterSerial);
    }

    // Private method implementations

    bool CGuildBattleAuth::ValidateParameters(int sessionId, uint32_t guildSerial, uint32_t characterSerial) const {
        return sessionId > 0 && characterSerial > 0;
    }

    CGuildBattleAuth::GuildBattle* CGuildBattleAuth::GetBattle(uint32_t battleId) {
        auto it = m_battles.find(battleId);
        return (it != m_battles.end()) ? it->second.get() : nullptr;
    }

    const CGuildBattleAuth::GuildBattle* CGuildBattleAuth::GetBattle(uint32_t battleId) const {
        auto it = m_battles.find(battleId);
        return (it != m_battles.end()) ? it->second.get() : nullptr;
    }

    void CGuildBattleAuth::UpdateStatistics(bool success) {
        std::lock_guard<std::mutex> lock(m_statisticsMutex);
        m_statistics.totalLoginAttempts++;

        if (success) {
            m_statistics.successfulLogins++;
        } else {
            m_statistics.failedLogins++;
        }

        m_statistics.activeBattles = static_cast<uint32_t>(GetActiveBattleCount());
    }

    void CGuildBattleAuth::LogAuthenticationEvent(const std::string& event, int sessionId, uint32_t guildSerial, bool success) {
        if (m_logger) {
            std::ostringstream oss;
            oss << event << " - Session: " << sessionId << ", Guild: " << guildSerial
                << " - Result: " << (success ? "SUCCESS" : "FAILED");
            m_logger->WriteLog(oss.str());
        }
    }

    bool CGuildBattleAuth::ProcessBattleLogin(GuildBattle* battle, int sessionId, uint32_t guildSerial, uint32_t characterSerial) {
        if (!battle) {
            return false;
        }

        // Determine which guild this character belongs to
        CGuildBattleGuild* targetGuild = nullptr;
        std::string destGuildName;

        if (battle->guild1Serial == guildSerial) {
            targetGuild = GetGuild(battle->guild1Serial);
            auto* guild2 = GetGuild(battle->guild2Serial);
            destGuildName = guild2 ? guild2->GetGuildName() : "Unknown";
        } else if (battle->guild2Serial == guildSerial) {
            targetGuild = GetGuild(battle->guild2Serial);
            auto* guild1 = GetGuild(battle->guild1Serial);
            destGuildName = guild1 ? guild1->GetGuildName() : "Unknown";
        }

        if (!targetGuild) {
            return false;
        }

        // Process the login (equivalent to original CNormalGuildBattle::LogIn)
        return targetGuild->LoginMember(sessionId, characterSerial, 0, destGuildName, battle->battleId);
    }

    bool CGuildBattleAuth::ProcessJoinRequest(GuildBattle* battle, int sessionId, uint32_t guildSerial, uint32_t characterSerial) {
        if (!battle) {
            return false;
        }

        // Process join request (equivalent to original CNormalGuildBattle::AskJoin)
        CGuildBattleGuild* targetGuild = nullptr;

        if (battle->guild1Serial == guildSerial) {
            targetGuild = GetGuild(battle->guild1Serial);
        } else if (battle->guild2Serial == guildSerial) {
            targetGuild = GetGuild(battle->guild2Serial);
        }

        if (!targetGuild) {
            return false;
        }

        // Add member to guild if not already present
        int memberIndex = targetGuild->GetMember(characterSerial);
        if (memberIndex < 0) {
            GuildBattleMember newMember(characterSerial, guildSerial, sessionId);
            if (!targetGuild->AddMember(newMember)) {
                return false;
            }
        }

        return targetGuild->AskJoin(sessionId, "Battle Request");
    }

    // Global instance access
    CGuildBattleAuth& GetGuildBattleAuth() {
        std::lock_guard<std::mutex> lock(g_instanceMutex);
        if (!g_pGuildBattleAuth) {
            g_pGuildBattleAuth = std::make_unique<CGuildBattleAuth>();
        }
        return *g_pGuildBattleAuth;
    }

    // Utility functions
    std::string GuildBattleAuthResultToString(GuildBattleAuthResult result) {
        switch (result) {
            case GuildBattleAuthResult::Success: return "Success";
            case GuildBattleAuthResult::InvalidParameters: return "Invalid Parameters";
            case GuildBattleAuthResult::GuildNotFound: return "Guild Not Found";
            case GuildBattleAuthResult::BattleNotFound: return "Battle Not Found";
            case GuildBattleAuthResult::MemberNotFound: return "Member Not Found";
            case GuildBattleAuthResult::BattleNotReady: return "Battle Not Ready";
            case GuildBattleAuthResult::BattleNotActive: return "Battle Not Active";
            case GuildBattleAuthResult::MemberAlreadyLoggedIn: return "Member Already Logged In";
            case GuildBattleAuthResult::MaxMembersReached: return "Max Members Reached";
            case GuildBattleAuthResult::SystemError: return "System Error";
            case GuildBattleAuthResult::NotInitialized: return "Not Initialized";
            default: return "Unknown";
        }
    }

    std::string GuildBattleStateToString(GuildBattleState state) {
        switch (state) {
            case GuildBattleState::None: return "None";
            case GuildBattleState::Ready: return "Ready";
            case GuildBattleState::Count: return "Count";
            case GuildBattleState::InBattle: return "In Battle";
            case GuildBattleState::Finished: return "Finished";
            default: return "Unknown";
        }
    }

    // Legacy C interface implementation
    extern "C" {
        void CNormalGuildBattle_LogIn(CNormalGuildBattle_Legacy* battle, int sessionId,
                                    uint32_t guildSerial, uint32_t characterSerial) {
            if (!battle) {
                return;
            }

            auto& auth = GetGuildBattleAuth();
            auth.LogInCNormalGuildBattle_Legacy(sessionId, guildSerial, characterSerial);
        }

        void CNormalGuildBattleGuild_LogIn(CNormalGuildBattleGuild_Legacy* guild, int sessionId,
                                         uint32_t characterSerial, uint8_t guildBattleNumber,
                                         const char* destGuildName, uint32_t battleId,
                                         void* field, void* logger) {
            if (!guild || !destGuildName) {
                return;
            }

            auto& auth = GetGuildBattleAuth();
            std::string destName(destGuildName);
            auth.LogInCNormalGuildBattleGuild_Legacy(sessionId, characterSerial, guildBattleNumber, destName, battleId);
        }

        void CNormalGuildBattleManager_LogIn(CNormalGuildBattleManager_Legacy* manager, int sessionId,
                                           uint32_t guildSerial, uint32_t characterSerial) {
            if (!manager) {
                return;
            }

            auto& auth = GetGuildBattleAuth();
            auth.LogInCNormalGuildBattleManager_Legacy(sessionId, guildSerial, characterSerial);
        }

        void CNormalGuildBattleGuildMember_Login(void* member) {
            // Implementation for guild member login (equivalent to original)
            // This would update PvP points and member status
            if (member) {
                std::cout << "[INFO] Guild battle member login processed" << std::endl;
            }
        }

        bool CNormalGuildBattle_IsReadyOrCountState(void* battle) {
            if (!battle) {
                return false;
            }

            // For legacy compatibility, assume battle is ready
            // In a real implementation, this would check the actual battle state
            return true;
        }

        bool CNormalGuildBattle_IsInBattle(void* battle) {
            if (!battle) {
                return false;
            }

            // For legacy compatibility, assume battle is active
            // In a real implementation, this would check the actual battle state
            return true;
        }

        void* CNormalGuildBattleManager_GetBattleByGuildSerial(void* manager, uint32_t guildSerial) {
            if (!manager) {
                return nullptr;
            }

            auto& auth = GetGuildBattleAuth();
            uint32_t battleId = 0;
            if (auth.GetBattleByGuildSerial(guildSerial, battleId)) {
                // Return a non-null pointer to indicate battle found
                // In a real implementation, this would return the actual battle object
                return reinterpret_cast<void*>(static_cast<uintptr_t>(battleId));
            }

            return nullptr;
        }
    }

} // namespace NexusProtection::Authentication
