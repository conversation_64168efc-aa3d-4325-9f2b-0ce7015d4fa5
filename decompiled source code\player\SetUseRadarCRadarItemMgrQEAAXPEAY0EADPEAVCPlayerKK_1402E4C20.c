/*
 * Function: ?SetUseRadar@CRadarItemMgr@@QEAAXPEAY0EA@DPEAVCPlayer@@KK@Z
 * Address: 0x1402E4C20
 */

void __fastcall CRadarItemMgr::SetUseRadar(CRadarItemMgr *this, char (*strRadarCode)[64], CPlayer *pMaster, unsigned int dwDurTime, unsigned int dwDelayTime)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CRadarItemMgr *v8; // [sp+30h] [bp+8h]@1
  CPlayer *v9; // [sp+40h] [bp+18h]@1
  unsigned int v10; // [sp+48h] [bp+20h]@1

  v10 = dwDurTime;
  v9 = pMaster;
  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8->m_bUse = 1;
  v8->m_bPlayerEnd = 0;
  v8->m_bMonEnd = 0;
  memcpy_0(v8->m_strRadarCode, strRadarCode, 0x40ui64);
  v8->m_dwStartTime = timeGetTime();
  v8->m_dwDurTime = 1000 * v10;
  v8->m_dwDelayTime = 1000 * dwDelayTime;
  v8->m_pMaster = v9;
  v8->m_pDestMap = v9->m_pCurMap;
  v8->m_nPlayerNum = 0;
  v8->m_nMonNum = 0;
}
