/*
 * Function: ?SetL<PERSON>lD@CPlayer@@QEAAXE@Z
 * Address: 0x140055930
 */

void __usercall CPlayer::SetLevelD(CPlayer *this@<rcx>, char byDownLevel@<dl>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@7
  int v6; // eax@7
  __int64 v7; // r8@7
  int v8; // eax@7
  int v9; // eax@7
  int v10; // edx@7
  __int64 v11; // [sp+0h] [bp-58h]@1
  unsigned __int8 v12; // [sp+30h] [bp-28h]@4
  CGameObjectVtbl *v13; // [sp+38h] [bp-20h]@7
  int n; // [sp+40h] [bp-18h]@7
  CPlayer *v15; // [sp+60h] [bp+8h]@1
  char v16; // [sp+68h] [bp+10h]@1

  v16 = byDownLevel;
  v15 = this;
  v3 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = CPlayerDB::GetLevel(&v15->m_Param);
  if ( v12 > (signed int)(unsigned __int8)v16 )
  {
    if ( v15->m_pUserDB )
    {
      CPlayerDB::GetExp(&v15->m_Param);
      CUserDB::Update_Level(v15->m_pUserDB, v16, a3);
    }
    CPlayerDB::SetLevel(&v15->m_Param, (unsigned __int8)v16);
    v5 = CPlayerDB::CalcCharGrade(v16, v15->m_Param.m_dbChar.m_wRankRate);
    CPlayer::SetGrade(v15, v5);
    CPlayer::ReCalcMaxHFSP(v15, 1, 0);
    v6 = ((int (__fastcall *)(CPlayer *))v15->vfptr->GetMaxHP)(v15);
    v13 = v15->vfptr;
    LOBYTE(v7) = 1;
    ((void (__fastcall *)(CPlayer *, _QWORD, __int64))v13->SetHP)(v15, (unsigned int)v6, v7);
    v8 = CPlayer::GetMaxFP(v15);
    CPlayer::SetFP(v15, v8, 1);
    v9 = CPlayer::GetMaxSP(v15);
    CPlayer::SetSP(v15, v9, 1);
    CPlayer::SendMsg_Level(v15, (unsigned __int8)v16);
    CPlayer::SendData_PartyMemberLv(v15);
    v15->m_bDownCheckEquipEffect = 1;
    v10 = v15->m_Param.m_byPvPGrade;
    n = v15->m_ObjID.m_wIndex;
    CMgrAvatorLvHistory::downgrade_lv(
      &CPlayer::s_MgrLvHistory,
      n,
      (unsigned __int8)v16,
      v10,
      v15->m_nMaxPoint,
      v15->m_szLvHistoryFileName);
  }
}
