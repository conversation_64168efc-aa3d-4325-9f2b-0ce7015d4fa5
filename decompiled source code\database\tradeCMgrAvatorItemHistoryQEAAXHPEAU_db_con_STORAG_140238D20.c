/*
 * Function: ?trade@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@HKK0HKKPEADK1KK1PEAM1@Z
 * Address: 0x140238D20
 */

void __fastcall CMgrAvatorItemHistory::trade(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pOutItem, int nOutItemNum, unsigned int dwOutDalant, unsigned int dwOutGold, _STORAGE_LIST::_db_con *pInItem, int nInItemNum, unsigned int dwInDalant, unsigned int dwInGold, char *pszDstName, unsigned int dwDstSerial, char *pszDstID, unsigned int dwSumDalant, unsigned int dwSumGold, char *pMapCode, float *pfPos, char *pszFileName)
{
  __int64 *v18; // rdi@1
  signed __int64 i; // rcx@1
  signed int v20; // edx@4
  signed int v21; // er8@4
  signed int v22; // er9@4
  char *v23; // rax@6
  char *v24; // rax@9
  __int64 v25; // [sp+0h] [bp-D8h]@1
  char *v26; // [sp+20h] [bp-B8h]@4
  unsigned __int64 v27; // [sp+28h] [bp-B0h]@4
  unsigned int v28; // [sp+30h] [bp-A8h]@4
  unsigned int v29; // [sp+38h] [bp-A0h]@4
  unsigned int v30; // [sp+40h] [bp-98h]@4
  unsigned int v31; // [sp+48h] [bp-90h]@4
  unsigned int v32; // [sp+50h] [bp-88h]@4
  char *v33; // [sp+58h] [bp-80h]@4
  int v34; // [sp+60h] [bp-78h]@4
  int v35; // [sp+68h] [bp-70h]@4
  int v36; // [sp+70h] [bp-68h]@4
  char *v37; // [sp+78h] [bp-60h]@4
  char *v38; // [sp+80h] [bp-58h]@4
  int j; // [sp+90h] [bp-48h]@4
  _base_fld *v40; // [sp+98h] [bp-40h]@6
  _base_fld *v41; // [sp+A0h] [bp-38h]@9
  __int64 v42; // [sp+A8h] [bp-30h]@6
  int nTableCode; // [sp+B0h] [bp-28h]@6
  __int64 v44; // [sp+B8h] [bp-20h]@9
  int v45; // [sp+C0h] [bp-18h]@9
  CMgrAvatorItemHistory *v46; // [sp+E0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v47; // [sp+F0h] [bp+18h]@1
  int v48; // [sp+F8h] [bp+20h]@1

  v48 = nOutItemNum;
  v47 = pOutItem;
  v46 = this;
  v18 = &v25;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v18 = -858993460;
    v18 = (__int64 *)((char *)v18 + 4);
  }
  sData[0] = 0;
  v20 = (signed int)ffloor(pfPos[2]);
  v21 = (signed int)ffloor(pfPos[1]);
  v22 = (signed int)ffloor(*pfPos);
  v38 = v46->m_szCurTime;
  v37 = v46->m_szCurDate;
  v36 = v20;
  v35 = v21;
  v34 = v22;
  v33 = pMapCode;
  v32 = dwSumGold;
  v31 = dwSumDalant;
  v30 = dwInGold;
  v29 = dwInDalant;
  v28 = dwOutGold;
  LODWORD(v27) = dwOutDalant;
  v26 = pszDstID;
  sprintf(
    sBuf,
    "TRADE: dst(%s:%d id:%s) pay(D:%u G:%u) rev(D:%u G:%u) $D:%u $G:%u \t{POS:%s (%d, %d, %d)} [%s %s]\r\n",
    pszDstName,
    dwDstSerial);
  strcat_0(sData, sBuf);
  for ( j = 0; j < v48; ++j )
  {
    v40 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v47[j].m_byTableCode, v47[j].m_wItemIndex);
    v42 = 50i64 * j;
    nTableCode = v47[j].m_byTableCode;
    v23 = DisplayItemUpgInfo(nTableCode, v47[j].m_dwLv);
    v27 = v47[(unsigned __int64)v42 / 0x32].m_lnUID;
    v26 = v23;
    sprintf(sBuf, "\t- %s_%u_@%s[%I64u]\r\n", v40->m_strCode, v47[j].m_dwDur);
    strcat_0(sData, sBuf);
  }
  for ( j = 0; j < nInItemNum; ++j )
  {
    v41 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pInItem[j].m_byTableCode, pInItem[j].m_wItemIndex);
    v44 = 50i64 * j;
    v45 = pInItem[j].m_byTableCode;
    v24 = DisplayItemUpgInfo(v45, pInItem[j].m_dwLv);
    v27 = pInItem[(unsigned __int64)v44 / 0x32].m_lnUID;
    v26 = v24;
    sprintf(sBuf, "\t+ %s_%u_@%s[%I64u]\r\n", v41->m_strCode, pInItem[j].m_dwDur);
    strcat_0(sData, sBuf);
  }
  CMgrAvatorItemHistory::WriteFile(v46, pszFileName, sData);
}
