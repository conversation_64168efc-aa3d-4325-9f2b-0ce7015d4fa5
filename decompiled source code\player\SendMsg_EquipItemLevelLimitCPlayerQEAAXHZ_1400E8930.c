/*
 * Function: ?SendMsg_EquipItemLevelLimit@CPlayer@@QEAAXH@Z
 * Address: 0x1400E8930
 */

void __fastcall CPlayer::SendMsg_EquipItemLevelLimit(CPlayer *this, int nCurPlayerLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  _equip_up_item_lv_limit_zocl v5; // [sp+38h] [bp-60h]@4
  int j; // [sp+44h] [bp-54h]@4
  char *v7; // [sp+48h] [bp-50h]@7
  int v8; // [sp+50h] [bp-48h]@8
  char pbyType; // [sp+64h] [bp-34h]@20
  char v10; // [sp+65h] [bp-33h]@20
  int v11; // [sp+80h] [bp-18h]@10
  unsigned __int64 v12; // [sp+88h] [bp-10h]@4
  CPlayer *v13; // [sp+A0h] [bp+8h]@1
  int v14; // [sp+A8h] [bp+10h]@1

  v14 = nCurPlayerLv;
  v13 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  _equip_up_item_lv_limit_zocl::_equip_up_item_lv_limit_zocl(&v5);
  for ( j = 0; j < 8; ++j )
  {
    v7 = &v13->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    if ( *v7 )
    {
      v8 = GetItemEquipUpLevel((unsigned __int8)v7[1], *(_WORD *)(v7 + 3));
      if ( v8 > 0 && v14 > v8 )
      {
        v11 = (unsigned __int8)v7[1];
        switch ( v11 )
        {
          case 0:
            v5.byEquipItemNum[1] = 1;
            break;
          case 1:
            v5.byEquipItemNum[2] = 1;
            break;
          case 2:
            v5.byEquipItemNum[7] = 1;
            break;
          case 3:
            v5.byEquipItemNum[4] = 1;
            break;
          case 4:
            v5.byEquipItemNum[0] = 1;
            break;
          case 5:
            v5.byEquipItemNum[6] = 1;
            break;
          case 7:
            v5.byEquipItemNum[5] = 1;
            break;
          case 6:
            v5.byEquipItemNum[3] = 1;
            break;
          default:
            continue;
        }
      }
    }
  }
  pbyType = 11;
  v10 = 35;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, (char *)&v5, 8u);
}
