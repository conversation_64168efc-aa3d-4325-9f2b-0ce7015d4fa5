/*
 * Function: j_?pc_WPActiveAttack_Skill@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAH1PEAU_skill_fld@@EG@Z
 * Address: 0x14000BFEB
 */

bool __fastcall CPlayer::pc_WPActiveAttack_Skill(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObjNum, int *nShotNum, _skill_fld *pSkillFld, char byEffect<PERSON><PERSON>, unsigned __int16 wBulletSerial)
{
  return CPlayer::pc_WPActiveAttack_Skill(
           this,
           pDamList,
           nDamagedObjNum,
           nShotNum,
           pSkillFld,
           byEffectCode,
           wBulletSerial);
}
