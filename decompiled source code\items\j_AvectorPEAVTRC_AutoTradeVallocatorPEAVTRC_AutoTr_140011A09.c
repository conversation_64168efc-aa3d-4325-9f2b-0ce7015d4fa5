/*
 * Function: j_??A?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAAEAPEAVTRC_AutoTrade@@_K@Z
 * Address: 0x140011A09
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, unsigned __int64 _Pos)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](this, _Pos);
}
