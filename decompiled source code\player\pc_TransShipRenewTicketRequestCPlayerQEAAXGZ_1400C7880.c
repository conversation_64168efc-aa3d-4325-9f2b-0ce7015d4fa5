/*
 * Function: ?pc_TransShipRenewTicketRequest@CPlayer@@QEAAXG@Z
 * Address: 0x1400C7880
 */

void __fastcall CPlayer::pc_TransShipRenewTicketRequest(CPlayer *this, unsigned __int16 wTicketItemSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@10
  int v5; // eax@13
  _TicketItem_fld *v6; // rax@19
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+20h] [bp-28h]@4
  CTransportShip *v9; // [sp+28h] [bp-20h]@4
  _STORAGE_LIST::_db_con *v10; // [sp+30h] [bp-18h]@4
  int v11; // [sp+38h] [bp-10h]@10
  int v12; // [sp+3Ch] [bp-Ch]@13
  CPlayer *pMember; // [sp+50h] [bp+8h]@1
  unsigned __int16 v14; // [sp+58h] [bp+10h]@1

  v14 = wTicketItemSerial;
  pMember = this;
  v2 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  v9 = (CTransportShip *)&g_TransportShip[10162 * CPlayerDB::GetRaceCode(&pMember->m_Param)];
  v10 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&pMember->m_Param.m_dbInven.m_nListNum, v14);
  if ( pMember->m_pCurMap == v9->m_pLinkShipMap )
  {
    if ( CTransportShip::IsOldMember(v9, pMember) )
    {
      if ( v9->m_bAnchor )
      {
        v11 = ((int (__fastcall *)(CPlayer *))pMember->vfptr->GetLevel)(pMember);
        v4 = CTransportShip::GetRideLimLevel(v9);
        if ( v11 >= v4 )
        {
          if ( CTransportShip::GetRideUpLimLevel(v9) == -1
            || (v12 = ((int (__fastcall *)(CPlayer *))pMember->vfptr->GetLevel)(pMember),
                v5 = CTransportShip::GetRideUpLimLevel(v9),
                v12 <= v5) )
          {
            if ( v10 )
            {
              if ( v10->m_byTableCode == 28 )
              {
                v6 = (_TicketItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 28, v10->m_wItemIndex);
                if ( !CTransportShip::GetCurRideShipThisTicket(v9, v6) )
                  v8 = 1;
              }
              else
              {
                v8 = 1;
              }
            }
            else
            {
              v8 = 1;
            }
          }
          else
          {
            v8 = 14;
          }
        }
        else
        {
          v8 = 14;
        }
      }
      else
      {
        v8 = 1;
      }
    }
    else
    {
      v8 = 1;
    }
  }
  else
  {
    v8 = 1;
  }
  if ( !v8 && !CTransportShip::RenewOldMember(v9, pMember) )
    v8 = 1;
  CPlayer::SendMsg_TransShipRenewTicketResult(pMember, v8);
}
