/*
 * Function: ?UsePotion@CPotionMgr@@QEAAHPEAVCPlayer@@PEAVCCharacter@@PEBU_PotionItem_fld@@K@Z
 * Address: 0x14039DDC0
 */

signed __int64 __fastcall CPotionMgr::UsePotion(CPotionMgr *this, CPlayer *pUsePlayer, CCharacter *pTargetCharacter, _PotionItem_fld *pfB, unsigned int nCurTime)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  __int64 v8; // [sp+0h] [bp-228h]@1
  CCharacter *pOriDst; // [sp+20h] [bp-208h]@12
  _skill_fld *pFld; // [sp+28h] [bp-200h]@12
  bool bCheckDist[8]; // [sp+30h] [bp-1F8h]@12
  bool *pbPath; // [sp+38h] [bp-1F0h]@12
  _skill_fld *pEffecFld; // [sp+40h] [bp-1E8h]@8
  bool v14; // [sp+48h] [bp-1E0h]@8
  int v15; // [sp+4Ch] [bp-1DCh]@10
  _CheckPotion_fld *v16; // [sp+50h] [bp-1D8h]@12
  char Dst[72]; // [sp+68h] [bp-1C0h]@12
  CCharacter *ppDsts; // [sp+B0h] [bp-178h]@12
  char v19[44]; // [sp+1B8h] [bp-70h]@12
  int v20; // [sp+1E4h] [bp-44h]@12
  char v21; // [sp+1E8h] [bp-40h]@12
  int j; // [sp+1ECh] [bp-3Ch]@12
  CPlayer *pApplyPlayer; // [sp+1F0h] [bp-38h]@16
  char v24; // [sp+1F8h] [bp-30h]@35
  _CheckPotion_fld *pCheckFld; // [sp+208h] [bp-20h]@18
  unsigned __int64 v26; // [sp+210h] [bp-18h]@4
  CPotionMgr *v27; // [sp+230h] [bp+8h]@1
  CPlayer *pUsePlayera; // [sp+238h] [bp+10h]@1
  CCharacter *pTargetCharactera; // [sp+240h] [bp+18h]@1
  _PotionItem_fld *pfBa; // [sp+248h] [bp+20h]@1

  pfBa = pfB;
  pTargetCharactera = pTargetCharacter;
  pUsePlayera = pUsePlayer;
  v27 = this;
  v5 = &v8;
  for ( i = 136i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v26 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( pfB && pUsePlayer && pTargetCharacter )
  {
    pEffecFld = (_skill_fld *)CRecordData::GetRecord(&v27->m_tblPotionEffectData, pfB->m_strEffCode);
    v14 = 1;
    if ( pfBa->m_nUseRange < 0 )
      v14 = 0;
    v15 = CPotionMgr::PreCheckPotion(v27, pUsePlayera, &pTargetCharactera, pfBa, nCurTime, pEffecFld, v14);
    if ( v15 )
    {
      result = (unsigned int)v15;
    }
    else
    {
      v16 = (_CheckPotion_fld *)CRecordData::GetRecord(&v27->m_tblPotionCheckData, pfBa->m_strTargetEff);
      memset_0(Dst, 0, 0x1Eui64);
      pbPath = (bool *)Dst;
      *(_QWORD *)bCheckDist = &ppDsts;
      pFld = (_skill_fld *)((char *)pEffecFld + 412);
      pOriDst = pTargetCharactera;
      v20 = CCharacter::FindPotionEffectDst(
              (CCharacter *)&pUsePlayera->vfptr,
              pfBa->m_nEffArea,
              pfBa->m_nEffAreaVal,
              1,
              pTargetCharactera,
              pEffecFld->m_strActableDst,
              &ppDsts,
              (bool *)Dst);
      memset_0(v19, 0, 0x1Eui64);
      v21 = 0;
      for ( j = 0; j < v20; ++j )
      {
        if ( !(*(&ppDsts + j))->m_ObjID.m_byID )
        {
          pApplyPlayer = (CPlayer *)*(&ppDsts + j);
          if ( Dst[j] && v21 )
            pCheckFld = 0i64;
          else
            pCheckFld = v16;
          v19[j] = CPotionMgr::ApplyPotion(v27, pUsePlayera, pApplyPlayer, pEffecFld, pCheckFld, pfBa, 1);
          if ( (CCharacter *)pApplyPlayer == pTargetCharactera )
          {
            if ( v19[j] )
            {
              if ( pEffecFld->m_nTempEffectType == 45 )
                result = 18i64;
              else
                result = 25i64;
              return result;
            }
            v21 = 1;
          }
          if ( !v19[j]
            && (!strcmp_0(pfBa->m_strEffCode, "F0")
             || pApplyPlayer != pUsePlayera
             && pEffecFld->m_nTempEffectType != 48
             && pEffecFld->m_nTempEffectType != 49
             && pEffecFld->m_nTempEffectType != 70
             && pEffecFld->m_nTempEffectType != 71) )
          {
            LOBYTE(pOriDst) = 0;
            CPlayer::SendMsg_UsPotionResultOther(pApplyPlayer, 0, pfBa->m_dwIndex, pUsePlayera, 0);
          }
        }
      }
      v24 = 0;
      for ( j = 0; j < v20; ++j )
      {
        if ( !v19[j] )
          v24 = 1;
      }
      v15 = 0;
      if ( !v24 )
        v15 = (unsigned __int8)v19[0];
      if ( v15 == 19 && pfBa->m_nDelayType == 12 )
        v15 = 21;
      result = (unsigned int)v15;
    }
  }
  else
  {
    result = 25i64;
  }
  return result;
}
