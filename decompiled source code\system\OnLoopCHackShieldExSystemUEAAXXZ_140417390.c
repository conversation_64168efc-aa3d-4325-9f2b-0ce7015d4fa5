/*
 * Function: ?OnLoop@CHackShieldExSystem@@UEAAXXZ
 * Address: 0x140417390
 */

void __fastcall CHackShieldExSystem::OnLoop(CHackShieldExSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int v4; // [sp+20h] [bp-18h]@5
  CUserDB *v5; // [sp+28h] [bp-10h]@9
  CHackShieldExSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v6->m_tmLoopTime) )
  {
    v4 = 0;
    while ( v4 < 0xA )
    {
      if ( v6->m_dwCurrentCheckIndex >= 0x9E4 )
        v6->m_dwCurrentCheckIndex = 0;
      v5 = &g_UserDB[v6->m_dwCurrentCheckIndex];
      if ( v5->m_bActive )
        ((void (__fastcall *)(CHackShieldExSystem *, _QWORD))v6->vfptr->OnLoopSession)(v6, v6->m_dwCurrentCheckIndex);
      ++v4;
      ++v6->m_dwCurrentCheckIndex;
    }
  }
}
