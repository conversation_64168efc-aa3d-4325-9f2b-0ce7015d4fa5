/*
 * Function: ?Create@CUnmannedTraderSubClassInfoLevel@@UEAAPEAVCUnmannedTraderSubClassInfo@@K@Z
 * Address: 0x1403844F0
 */

CUnmannedTraderSubClassInfo *__fastcall CUnmannedTraderSubClassInfoLevel::Create(CUnmannedTraderSubClassInfoLevel *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@5
  __int64 v6; // [sp+0h] [bp-48h]@1
  CUnmannedTraderSubClassInfoLevel *v7; // [sp+28h] [bp-20h]@4
  __int64 v8; // [sp+30h] [bp-18h]@4
  __int64 v9; // [sp+38h] [bp-10h]@5
  unsigned int dwIDa; // [sp+58h] [bp+10h]@1

  dwIDa = dwID;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = -2i64;
  v7 = (CUnmannedTraderSubClassInfoLevel *)operator new(0x30ui64);
  if ( v7 )
  {
    CUnmannedTraderSubClassInfoLevel::CUnmannedTraderSubClassInfoLevel(v7, dwIDa);
    v9 = v4;
  }
  else
  {
    v9 = 0i64;
  }
  return (CUnmannedTraderSubClassInfo *)v9;
}
