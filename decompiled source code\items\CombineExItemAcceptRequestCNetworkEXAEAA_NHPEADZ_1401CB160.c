/*
 * Function: ?CombineExItemAcceptRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB160
 */

char __fastcall CNetworkEX::CombineExItemAcceptRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  char *v7; // rax@13
  __int64 v8; // [sp+0h] [bp-38h]@1
  _combine_ex_item_accept_request_clzo *pRecv; // [sp+20h] [bp-18h]@4
  CPlayer *v10; // [sp+28h] [bp-10h]@4
  CNetworkEX *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pRecv = (_combine_ex_item_accept_request_clzo *)pBuf;
  v10 = &g_Player + n;
  if ( !v10->m_bOper || v10->m_pmTrd.bDTradeMode || v10->m_bCorpse )
  {
    result = 1;
  }
  else if ( pRecv->SelectItemBuff.bySelectNum < 24 )
  {
    if ( pRecv->byDlgType != 255 && pRecv->byDlgType != 1 && pRecv->byDlgType )
    {
      v7 = CPlayerDB::GetCharNameA(&v10->m_Param);
      CLogFile::Write(&v11->m_LogFile, "odd.. %s: CombineExItemAcceptRequest().. \tIsNotDlgType  ", v7);
      result = 0;
    }
    else
    {
      CPlayer::pc_CombineItemExAccept(v10, pRecv);
      result = 1;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v10->m_Param);
    CLogFile::Write(
      &v11->m_LogFile,
      "odd.. %s: CombineExItemAcceptRequest().. \tif(\tpRecv->SelectItemBuff.bySelectNum >= _combine_ex_item_accept_reque"
      "st_clzo::eMaxSelectItemNum ) ",
      v6);
    result = 0;
  }
  return result;
}
