/*
 * Function: ?RegistCheat@CNationSettingFactory@@IEAA_NPEAVCNationSettingData@@PEBDP6A_NPEAVCPlayer@@@ZHH@Z
 * Address: 0x1402171C0
 */

char __fastcall CNationSettingFactory::RegistCheat(CNationSettingFactory *this, CNationSettingData *pkData, const char *szCheat, bool (__cdecl *pCheatCommandFn)(CPlayer *), int iUseDegree, int iMgrDegree)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@8
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v9; // rax@11
  __int64 v10; // [sp+0h] [bp-138h]@1
  bool (__cdecl *v11)(CPlayer *); // [sp+20h] [bp-118h]@8
  int v12; // [sp+28h] [bp-110h]@8
  int v13; // [sp+30h] [bp-108h]@8
  CHEAT_COMMAND _Val; // [sp+48h] [bp-F0h]@11
  char v15; // [sp+78h] [bp-C0h]@11
  char v16; // [sp+98h] [bp-A0h]@11
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *result; // [sp+B0h] [bp-88h]@11
  char v18; // [sp+B8h] [bp-80h]@11
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v19; // [sp+D0h] [bp-68h]@11
  char v20; // [sp+E0h] [bp-58h]@11
  __int64 v21; // [sp+100h] [bp-38h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v22; // [sp+108h] [bp-30h]@11
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v23; // [sp+110h] [bp-28h]@11
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v24; // [sp+118h] [bp-20h]@11
  CNationSettingFactory *v25; // [sp+140h] [bp+8h]@1
  CNationSettingData *pkDataa; // [sp+148h] [bp+10h]@1
  char *szCheata; // [sp+150h] [bp+18h]@1
  bool (__cdecl *v28)(CPlayer *); // [sp+158h] [bp+20h]@1

  v28 = pCheatCommandFn;
  szCheata = (char *)szCheat;
  pkDataa = pkData;
  v25 = this;
  v6 = &v10;
  for ( i = 74i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v21 = -2i64;
  if ( pkData && szCheat && pCheatCommandFn && iUseDegree )
  {
    if ( CNationSettingFactory::IsExistCheat(v25, szCheat, pkData) )
    {
      MyMessageBox(
        "CNationSettingFactory::RegistChest : ",
        "Nation(%s) Cheat(%s) Already Exist!",
        pkDataa->m_szNationCodeStr,
        szCheata);
      v8 = 0;
    }
    else
    {
      _Val.pwszCommand = szCheata;
      _Val.uiCmdLen = strlen_0(szCheata);
      _Val.fn = v28;
      _Val.nUseDegree = iUseDegree;
      _Val.nMgrDegree = iMgrDegree;
      std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::push_back(&pkDataa->m_vecCheatData, &_Val);
      memset(&v15, 0, 0x20ui64);
      result = (std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v16;
      v19 = (std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v18;
      qmemcpy(&v20, &v15, 0x20ui64);
      v9 = std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::end(
             &pkDataa->m_vecCheatData,
             (std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v16);
      v22 = v9;
      v23 = v9;
      v24 = std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::begin(&pkDataa->m_vecCheatData, v19);
      std::sort<std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>,CHEAT_COMMAND>(
        v24,
        v23,
        (CHEAT_COMMAND *)&v20);
      v8 = 1;
    }
  }
  else
  {
    v13 = iMgrDegree;
    v12 = iUseDegree;
    v11 = pCheatCommandFn;
    MyMessageBox(
      "CNationSettingFactory::RegistChest : ",
      "0 == pkData(%p) || 0 == szCheat(%s) || 0 == pCheatCommandFn(%p) Invalid!",
      pkData,
      szCheat);
    v8 = 0;
  }
  return v8;
}
