/*
 * Function: ?db_Load_PostStorage@CMainThread@@QEAAEPEAD@Z
 * Address: 0x1401B2D80
 */

char __usercall CMainThread::db_Load_PostStorage@<al>(CMainThread *this@<rcx>, char *pData@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-1018h]@1
  char *v8; // [sp+0h] [bp-FF8h]@4
  unsigned int Dst; // [sp+20h] [bp-FD8h]@4
  int v10; // [sp+28h] [bp-FD0h]@7
  char v11; // [sp+2Ch] [bp-FCCh]@7
  char v12; // [sp+2Dh] [bp-FCBh]@7
  char Src[17]; // [sp+2Eh] [bp-FCAh]@7
  char v14[21]; // [sp+3Fh] [bp-FB9h]@7
  int pl_nKey; // [sp+54h] [bp-FA4h]@7
  __int64 v16; // [sp+58h] [bp-FA0h]@7
  int v17[2]; // [sp+60h] [bp-F98h]@7
  __int64 v18; // [sp+68h] [bp-F90h]@7
  int v19; // [sp+70h] [bp-F88h]@7
  __int16 v20[1968]; // [sp+74h] [bp-F84h]@7
  unsigned int j; // [sp+FD4h] [bp-24h]@5
  unsigned __int64 v22; // [sp+FE0h] [bp-18h]@4
  CMainThread *v23; // [sp+1000h] [bp+8h]@1
  char *v24; // [sp+1008h] [bp+10h]@1

  v24 = pData;
  v23 = this;
  v3 = alloca(a3);
  v4 = &v7;
  for ( i = 1028i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v22 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = pData;
  memset_0(&Dst, 0, 0xFA8ui64);
  v8[4] = CRFWorldDatabase::Select_PostStorageList(v23->m_pWorldDB, *(_DWORD *)v8, (_post_storage_list *)&Dst);
  if ( v8[4] != 1 )
  {
    for ( j = 0; j < Dst; ++j )
    {
      *(_DWORD *)&v8[80 * j + 16] = *(&v10 + 20 * (signed int)j);
      v8[80 * j + 20] = *(&v11 + 80 * (signed int)j);
      v8[80 * j + 21] = *(&v12 + 80 * (signed int)j);
      strcpy_s(&v8[80 * j + 22], 0x11ui64, &Src[80 * j]);
      strcpy_s(&v8[80 * j + 39], 0x15ui64, &v14[80 * j]);
      _INVENKEY::LoadDBKey((_INVENKEY *)&v8[80 * j + 60], *(&pl_nKey + 20 * (signed int)j));
      *(_QWORD *)&v8[80 * j + 64] = *(&v16 + 10 * (signed int)j);
      *(_DWORD *)&v8[80 * j + 72] = v17[20 * j];
      *(_DWORD *)&v8[80 * j + 88] = *(&v19 + 20 * (signed int)j);
      *(_QWORD *)&v8[80 * j + 80] = *(&v18 + 10 * (signed int)j);
      *(_WORD *)&v8[80 * j + 92] = v20[40 * j];
    }
    *((_DWORD *)v8 + 2) = Dst;
  }
  return 0;
}
