/*
 * Function: ?Init@CCircleZone@@QEAA_NIHHGPEAVCMapData@@@Z
 * Address: 0x14012D740
 */

char __fastcall CCircleZone::Init(CCircleZone *this, unsigned int uiMapInx, int iPlayerInx, int iNth, unsigned __int16 wInx, CMapData *pkMap)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // rax@5
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@8
  char result; // al@8
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@10
  GUILD_BATTLE::CGuildBattleLogger *v12; // rax@12
  GUILD_BATTLE::CGuildBattleLogger *v13; // rax@14
  __int64 v14; // [sp+0h] [bp-3E8h]@1
  DWORD nSize[2]; // [sp+20h] [bp-3C8h]@10
  LPCSTR lpFileName; // [sp+28h] [bp-3C0h]@10
  _object_id pID; // [sp+34h] [bp-3B4h]@4
  char Dst; // [sp+60h] [bp-388h]@4
  char KeyName; // [sp+180h] [bp-268h]@4
  char Dest; // [sp+2A0h] [bp-148h]@4
  _dummy_position *v21; // [sp+3B0h] [bp-38h]@7
  _dummy_position *v22; // [sp+3B8h] [bp-30h]@4
  __int64 v23; // [sp+3C0h] [bp-28h]@4
  _dummy_position *v24; // [sp+3C8h] [bp-20h]@5
  unsigned __int64 v25; // [sp+3D0h] [bp-18h]@4
  CCircleZone *v26; // [sp+3F0h] [bp+8h]@1
  unsigned int v27; // [sp+3F8h] [bp+10h]@1
  int v28; // [sp+400h] [bp+18h]@1
  int v29; // [sp+408h] [bp+20h]@1

  v29 = iNth;
  v28 = iPlayerInx;
  v27 = uiMapInx;
  v26 = this;
  v6 = &v14;
  for ( i = 248i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v23 = -2i64;
  v25 = (unsigned __int64)&v14 ^ _security_cookie;
  _object_id::_object_id(&pID, 0, 9, wInx);
  CGameObject::Init((CGameObject *)&v26->vfptr, &pID);
  memset_0(&Dst, 0, 0xFFui64);
  memset_0(&KeyName, 0, 0xFFui64);
  sprintf(&Dest, "Map%d", v27);
  v22 = (_dummy_position *)operator new(0x9Cui64);
  if ( v22 )
  {
    _dummy_position::_dummy_position(v22);
    v24 = (_dummy_position *)v8;
  }
  else
  {
    v24 = 0i64;
  }
  v21 = v24;
  v26->m_pkGoalPos = v24;
  if ( v26->m_pkGoalPos )
  {
    Dst = 0;
    sprintf(&KeyName, "%dPGoalPosDummyName%d", (unsigned int)v28, (unsigned int)v29);
    GetPrivateProfileStringA(&Dest, &KeyName, "X", &Dst, 0xFFu, "./Initialize/NormalGuildBattle.ini");
    if ( Dst == 88 )
    {
      v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      lpFileName = "./Initialize/NormalGuildBattle.ini";
      *(_QWORD *)nSize = &Dst;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v11,
        "CCircleZone::Init() : GetPrivateProfileString( %s, %s, X, %s, 255, %s ) == 'X'!",
        &Dest,
        &KeyName);
      result = 0;
    }
    else if ( CMapData::LoadDummy(pkMap, &Dst, v26->m_pkGoalPos) )
    {
      v26->m_iPortalInx = CMapData::GetPortalInx(pkMap, v26->m_pkGoalPos->m_szCode);
      if ( v26->m_iPortalInx >= 0 )
      {
        v26->m_eState = 0;
        result = 1;
      }
      else
      {
        v13 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v13,
          "CGravityStoneRegener::Init()pkMap->GetPortalInx( %s ) Fail!",
          v26->m_pkGoalPos);
        result = 0;
      }
    }
    else
    {
      v12 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v12,
        "CCircleZone::Init() : pkMap->LoadDummy( %s, m_pkGoalPos ) Fail!",
        &Dst);
      result = 0;
    }
  }
  else
  {
    v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(v9, "CCircleZone::Init() : new _dummy_position NULL!");
    result = 0;
  }
  return result;
}
