/*
 * Function: j_??$_Allocate@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@YAPEAVCUnmannedTraderGroupDivisionVersionInfo@@_KPEAV1@@Z
 * Address: 0x140011E46
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::_Allocate<CUnmannedTraderGroupDivisionVersionInfo>(unsigned __int64 _Count, CUnmannedTraderGroupDivisionVersionInfo *__formal)
{
  return std::_Allocate<CUnmannedTraderGroupDivisionVersionInfo>(_Count, __formal);
}
