/*
 * Function: ??$fill@PEAPEAVTRC_AutoTrade@@PEAV1@@std@@YAXPEAPEAVTRC_AutoTrade@@0AEBQEAV1@@Z
 * Address: 0x140391470
 */

void __fastcall std::fill<TRC_AutoTrade * *,TRC_AutoTrade *>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade *const *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade **_Firsta; // [sp+30h] [bp+8h]@1

  _Firsta = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Fill<TRC_AutoTrade * *,TRC_AutoTrade *>(_<PERSON>a, _Last, _Val);
}
