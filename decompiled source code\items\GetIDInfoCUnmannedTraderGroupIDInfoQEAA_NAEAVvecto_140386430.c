/*
 * Function: ?GetIDInfo@CUnmannedTraderGroupIDInfo@@QEAA_NAEAV?$vector@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@@Z
 * Address: 0x140386430
 */

bool __fastcall CUnmannedTraderGroupIDInfo::GetIDInfo(CUnmannedTraderGroupIDInfo *this, std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *vecInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool v4; // al@5
  unsigned __int64 v5; // rax@6
  CUnmannedTraderDivisionInfo **v6; // rax@8
  CUnmannedTraderDivisionInfo **v7; // rax@8
  std::pair<unsigned long,unsigned long> *v8; // rax@8
  unsigned __int64 v9; // rax@9
  __int64 v10; // [sp+0h] [bp-C8h]@1
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > result; // [sp+28h] [bp-A0h]@6
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v12; // [sp+48h] [bp-80h]@8
  bool v13; // [sp+60h] [bp-68h]@7
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v14; // [sp+68h] [bp-60h]@7
  std::pair<unsigned long,unsigned long> v15; // [sp+80h] [bp-48h]@8
  unsigned int _Val2; // [sp+88h] [bp-40h]@8
  unsigned int _Val1; // [sp+8Ch] [bp-3Ch]@8
  bool v18; // [sp+90h] [bp-38h]@9
  __int64 v19; // [sp+98h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v20; // [sp+A0h] [bp-28h]@7
  std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *_Right; // [sp+A8h] [bp-20h]@7
  unsigned __int64 v22; // [sp+B0h] [bp-18h]@9
  int v23; // [sp+B8h] [bp-10h]@9
  CUnmannedTraderGroupIDInfo *v24; // [sp+D0h] [bp+8h]@1
  std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *v25; // [sp+D8h] [bp+10h]@1

  v25 = vecInfo;
  v24 = this;
  v2 = &v10;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = -2i64;
  std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::clear(vecInfo);
  if ( std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::empty(&v24->m_vecDivisionInfo) )
  {
    CUnmannedTraderGroupIDInfo::Log(
      v24,
      "CUnmannedTraderGroupIDInfo::GetIDAndMaxCntInfo( std::vector< std::pair<DWORD,DWORD> > & vecInfo )\r\n"
      "\t\tm_vecClassInfo.empty()!\r\n");
    v4 = 0;
  }
  else
  {
    v5 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::size(&v24->m_vecDivisionInfo);
    std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::reserve(
      v25,
      v5);
    std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::begin(
      &v24->m_vecDivisionInfo,
      &result);
    while ( 1 )
    {
      v20 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::end(
              &v24->m_vecDivisionInfo,
              &v14);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)v20;
      v13 = std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v20->_Mycont);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v14);
      if ( !v13 )
        break;
      v6 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      _Val2 = CUnmannedTraderDivisionInfo::GetSize(*v6);
      v7 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      _Val1 = CUnmannedTraderDivisionInfo::GetID(*v7);
      std::pair<unsigned long,unsigned long>::pair<unsigned long,unsigned long>(&v15, &_Val1, &_Val2);
      std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::push_back(
        v25,
        v8);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator++(
        &result,
        &v12,
        0);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v12);
    }
    v22 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::size(&v24->m_vecDivisionInfo);
    v9 = std::vector<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>::size(v25);
    v23 = v22 == v9;
    v18 = v22 == v9;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
    v4 = v18;
  }
  return v4;
}
