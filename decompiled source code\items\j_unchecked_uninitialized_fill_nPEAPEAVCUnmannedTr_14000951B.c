/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@stdext@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@Z
 * Address: 0x14000951B
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val, std::allocator<CUnmannedTraderClassInfo *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
