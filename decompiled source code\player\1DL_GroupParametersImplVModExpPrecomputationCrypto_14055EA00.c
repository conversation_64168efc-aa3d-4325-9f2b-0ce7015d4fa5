/*
 * Function: ??1?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@UEAA@XZ
 * Address: 0x14055EA00
 */

__int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::~DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(CryptoPP::DL_GroupParameters_IntegerBased *a1)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::~DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(&a1[1]);
  CryptoPP::ModExpPrecomputation::~ModExpPrecomputation((CryptoPP::ModExpPrecomputation *)v2->gap48);
  return CryptoPP::DL_GroupParameters_IntegerBased::~DL_GroupParameters_IntegerBased(v2);
}
