/*
 * Function: ??1CMapDataTable@@UEAA@XZ
 * Address: 0x140198790
 */

void __fastcall CMapDataTable::~CMapDataTable(CMapDataTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  void *v4; // [sp+20h] [bp-18h]@5
  CMapDataTable *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->vfptr = (CMapDataTableVtbl *)&CMapDataTable::`vftable';
  if ( v5->m_pRecord )
  {
    v4 = v5->m_pRecord;
    operator delete[](v4);
    v5->m_pRecord = 0i64;
  }
}
