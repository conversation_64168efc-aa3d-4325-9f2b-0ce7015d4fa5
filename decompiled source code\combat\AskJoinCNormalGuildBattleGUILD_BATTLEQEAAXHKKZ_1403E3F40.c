/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CNormalGuildBattle@GUILD_BATTLE@@QEAAXHKK@Z
 * Address: 0x1403E3F40
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::Ask<PERSON>oin(GUILD_BATTLE::CNormalGuildBattle *this, int n, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@5
  char *v7; // rax@7
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *wszDestGuild; // [sp+20h] [bp-28h]@5
  GUILD_BATTLE::CNormalGuildBattleLogger *kLogger; // [sp+28h] [bp-20h]@5
  GUILD_BATTLE::CNormalGuildBattleLogger *v11; // [sp+30h] [bp-18h]@5
  GUILD_BATTLE::CNormalGuildBattleLogger *v12; // [sp+38h] [bp-10h]@7
  GUILD_BATTLE::CNormalGuildBattle *v13; // [sp+50h] [bp+8h]@1
  int na; // [sp+58h] [bp+10h]@1
  unsigned int v15; // [sp+60h] [bp+18h]@1
  unsigned int dwSerial; // [sp+68h] [bp+20h]@1

  dwSerial = dwCharacSerial;
  v15 = dwGuildSerial;
  na = n;
  v13 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v13->m_k1P) )
  {
    v11 = &v13->m_kLogger;
    v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v13->m_k2P);
    kLogger = v11;
    wszDestGuild = v6;
    GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(&v13->m_k1P, na, dwSerial, v13->m_byGuildBattleNumber, v6, v11);
  }
  else if ( v15 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v13->m_k2P) )
  {
    v12 = &v13->m_kLogger;
    v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v13->m_k1P);
    kLogger = v12;
    wszDestGuild = v7;
    GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(&v13->m_k2P, na, dwSerial, v13->m_byGuildBattleNumber, v7, v12);
  }
}
