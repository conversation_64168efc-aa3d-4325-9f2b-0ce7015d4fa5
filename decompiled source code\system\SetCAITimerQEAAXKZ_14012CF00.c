/*
 * Function: ?Set@CAITimer@@QEAAXK@Z
 * Address: 0x14012CF00
 */

void __fastcall CAITimer::Set(CAITimer *this, unsigned int delay)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CAITimer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( delay )
    v5->m_Delay = delay;
  else
    v5->m_Delay = v5->m_DDelay;
  v5->m_BefTime = timeGetTime();
}
