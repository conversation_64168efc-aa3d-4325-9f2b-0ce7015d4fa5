/*
 * Function: j_??1?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA@XZ
 * Address: 0x1400019B5
 */

void __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(this);
}
