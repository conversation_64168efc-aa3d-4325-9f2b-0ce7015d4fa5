/*
 * Function: ?Assignable@?$GetValueHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAAAEAV12@XZ
 * Address: 0x1405616C0
 */

__int64 __fastcall CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::Assignable(__int64 a1)
{
  const char *v1; // ST20_8@2
  __int64 v2; // rax@2
  __int64 v3; // rax@2
  __int64 v4; // rdx@2
  const char *v5; // rax@5
  char *v6; // rcx@5
  signed __int64 v7; // rax@5
  unsigned __int8 v8; // dl@6
  int v9; // eax@8
  __int64 v10; // rdx@11
  __int64 v12; // [sp+40h] [bp+8h]@1

  v12 = a1;
  if ( *(_BYTE *)(a1 + 33) )
  {
    v1 = type_info::_name_internal_method(
           &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
           (struct __type_info_node *)&__type_info_root_node);
    LODWORD(v2) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(
                    *(_QWORD *)(v12 + 24),
                    "ThisObject:");
    LODWORD(v3) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v2, v1);
    LOBYTE(v4) = 59;
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::operator+=(v3, v4);
  }
  if ( !*(_BYTE *)(v12 + 32) && !strncmp(*(const char **)(v12 + 8), "ThisObject:", 0xBui64) )
  {
    v5 = type_info::_name_internal_method(
           &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
           (struct __type_info_node *)&__type_info_root_node);
    v6 = (char *)(*(_QWORD *)(v12 + 8) + 11i64);
    v7 = v5 - v6;
    while ( 1 )
    {
      v8 = *v6;
      if ( *v6 != v6[v7] )
        break;
      ++v6;
      if ( !v8 )
      {
        v9 = 0;
        goto LABEL_10;
      }
    }
    v9 = -(v8 < (unsigned __int8)v6[v7]) - (((unsigned __int8)*v6 < (unsigned __int8)v6[v7]) - 1);
LABEL_10:
    if ( !v9 )
    {
      CryptoPP::NameValuePairs::ThrowIfTypeMismatch(
        *(const char **)(v12 + 8),
        &CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>> `RTTI Type Descriptor',
        *(type_info **)(v12 + 16));
      v10 = *(_QWORD *)v12;
      CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::operator=(*(_QWORD *)(v12 + 24));
      *(_BYTE *)(v12 + 32) = 1;
    }
  }
  return v12;
}
