/*
 * Function: ??1CashItemRemoteStore@@QEAA@XZ
 * Address: 0x1402F3A90
 */

void __fastcall CashItemRemoteStore::~CashItemRemoteStore(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CashItemRemoteStore *ptr; // [sp+40h] [bp+8h]@1

  ptr = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CLogFile::~CLogFile(&ptr->_kSysLog);
  CMyTimer::~CMyTimer(&ptr->m_TotalEventTimer);
  _con_event_::~_con_event_(&ptr->m_con_event);
  `eh vector destructor iterator'(ptr->m_cash_event, 0x668ui64, 3, (void (__cdecl *)(void *))_cash_event::~_cash_event);
  _cash_discount_::~_cash_discount_(&ptr->m_cde);
  CRecordData::~CRecordData(&ptr->_kRecConEventMSG);
  CRecordData::~CRecordData(&ptr->_kRecGoods);
  `eh vector destructor iterator'(ptr, 0xB8ui64, 2, (void (__cdecl *)(void *))CLogFile::~CLogFile);
}
