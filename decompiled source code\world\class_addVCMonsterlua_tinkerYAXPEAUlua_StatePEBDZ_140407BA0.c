/*
 * Function: ??$class_add@VCMonster@@@lua_tinker@@YAXPEAUlua_State@@PEBD@Z
 * Address: 0x140407BA0
 */

void __fastcall lua_tinker::class_add<CMonster>(struct lua_State *L, const char *name)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  struct lua_State *v5; // [sp+30h] [bp+8h]@1
  char *namea; // [sp+38h] [bp+10h]@1

  namea = (char *)name;
  v5 = L;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  lua_tinker::class_name<CMonster>::name(name);
  lua_pushstring(v5, namea);
  lua_createtable(v5, 0i64, 0i64);
  lua_pushstring(v5, "__name");
  lua_pushstring(v5, namea);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__index");
  lua_pushcclosure(v5, lua_tinker::meta_get, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__newindex");
  lua_pushcclosure(v5, lua_tinker::meta_set, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__gc");
  lua_pushcclosure(v5, lua_tinker::destroyer<CMonster>, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_settable(v5, 4294957294i64);
}
