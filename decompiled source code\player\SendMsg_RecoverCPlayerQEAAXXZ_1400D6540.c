/*
 * Function: ?SendMsg_Recover@CPlayer@@QEAAXXZ
 * Address: 0x1400D6540
 */

void __fastcall CPlayer::SendMsg_Recover(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+38h] [bp-40h]@4
  __int16 v5; // [sp+3Ah] [bp-3Eh]@4
  __int16 v6; // [sp+3Ch] [bp-3Ch]@4
  __int16 v7; // [sp+3Eh] [bp-3Ah]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_WORD *)szMsg = CPlayerDB::GetHP(&v10->m_Param);
  v5 = CPlayerDB::GetFP(&v10->m_Param);
  v6 = CPlayerDB::GetSP(&v10->m_Param);
  v7 = CPlayerDB::GetDP(&v10->m_Param);
  pbyType = 11;
  v9 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 8u);
}
