/*
 * Function: ?LoadDummys@CNormalGuildBattleField@GUILD_BATTLE@@AEAA_NPEAD00AEAIAEAPEAPEAU_dummy_position@@@Z
 * Address: 0x1403ED930
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::LoadDummys(GUILD_BATTLE::CNormalGuildBattleField *this, char *szSectionName, char *szKeyName, char *szItemName, unsigned int *uiCnt, _dummy_position ***ppDummy)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@9
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@11
  _dummy_position *v11; // rax@15
  GUILD_BATTLE::CGuildBattleLogger *v12; // rax@20
  GUILD_BATTLE::CGuildBattleLogger *v13; // rax@22
  __int64 v14; // [sp+0h] [bp-2E8h]@1
  DWORD nSize[2]; // [sp+20h] [bp-2C8h]@9
  LPCSTR lpFileName; // [sp+28h] [bp-2C0h]@9
  char *v17; // [sp+30h] [bp-2B8h]@9
  char *v18; // [sp+38h] [bp-2B0h]@9
  const char *v19; // [sp+40h] [bp-2A8h]@20
  char ReturnedString; // [sp+60h] [bp-288h]@19
  char Dest; // [sp+180h] [bp-168h]@19
  GUILD_BATTLE::CGuildBattleLogger *v22; // [sp+288h] [bp-60h]@8
  unsigned int j; // [sp+290h] [bp-58h]@12
  _dummy_position **v24; // [sp+2A0h] [bp-48h]@10
  _dummy_position *v25; // [sp+2A8h] [bp-40h]@17
  _dummy_position *v26; // [sp+2B0h] [bp-38h]@14
  __int64 v27; // [sp+2B8h] [bp-30h]@4
  unsigned __int64 v28; // [sp+2C0h] [bp-28h]@10
  _dummy_position *v29; // [sp+2C8h] [bp-20h]@15
  unsigned __int64 v30; // [sp+2D0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v31; // [sp+2F0h] [bp+8h]@1
  char *lpAppName; // [sp+2F8h] [bp+10h]@1
  char *lpKeyName; // [sp+300h] [bp+18h]@1
  char *v34; // [sp+308h] [bp+20h]@1

  v34 = szItemName;
  lpKeyName = szKeyName;
  lpAppName = szSectionName;
  v31 = this;
  v6 = &v14;
  for ( i = 184i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v27 = -2i64;
  v30 = (unsigned __int64)&v14 ^ _security_cookie;
  if ( szSectionName && szKeyName && szItemName )
  {
    v22 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    *uiCnt = GetPrivateProfileIntA(lpAppName, lpKeyName, 0, "./Initialize/NormalGuildBattle.ini");
    if ( *uiCnt )
    {
      v28 = *uiCnt;
      v24 = (_dummy_position **)operator new[](saturated_mul(8ui64, v28));
      *ppDummy = v24;
      if ( *ppDummy )
      {
        for ( j = 0; j < *uiCnt; ++j )
        {
          v26 = (_dummy_position *)operator new(0x9Cui64);
          if ( v26 )
          {
            _dummy_position::_dummy_position(v26);
            v29 = v11;
          }
          else
          {
            v29 = 0i64;
          }
          v25 = v29;
          (*ppDummy)[j] = v29;
          if ( !(*ppDummy)[j] )
            return 0;
          ReturnedString = 0;
          sprintf(&Dest, "%s%d", v34, j);
          GetPrivateProfileStringA(lpAppName, &Dest, "X", &ReturnedString, 0xFFu, "./Initialize/NormalGuildBattle.ini");
          if ( ReturnedString == 88 )
          {
            v12 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            v19 = "./Initialize/NormalGuildBattle.ini";
            v18 = &ReturnedString;
            v17 = &Dest;
            lpFileName = lpAppName;
            *(_QWORD *)nSize = v34;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v12,
              "CNormalGuildBattleField::LoadDummys( %s, %s, %s ) : GetPrivateProfileInt( %s, %s, X, %s, 255, %s ) Fail!",
              lpAppName,
              lpKeyName);
            return 0;
          }
          if ( !CMapData::LoadDummy(v31->m_pkMap, &ReturnedString, (*ppDummy)[j]) )
          {
            v13 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            lpFileName = &ReturnedString;
            *(_QWORD *)nSize = v34;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v13,
              "CNormalGuildBattleField::LoadDummys( %s, %s, %s ) : LoadDummy( %s, ... ) Fail!",
              lpAppName,
              lpKeyName);
            return 0;
          }
        }
        result = 1;
      }
      else
      {
        v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        LODWORD(lpFileName) = *uiCnt;
        *(_QWORD *)nSize = v34;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v10,
          "CNormalGuildBattleField::LoadDummys( %s, %s, %s ) : ppDummy = new _dummy_position * [%u] Fail!",
          lpAppName,
          lpKeyName);
        result = 0;
      }
    }
    else
    {
      v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v18 = "./Initialize/NormalGuildBattle.ini";
      v17 = lpKeyName;
      lpFileName = lpAppName;
      *(_QWORD *)nSize = v34;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v9,
        "CNormalGuildBattleField::LoadDummys( %s, %s, %s ) : uiCnt = GetPrivateProfileInt( %s, %s, 0, %s ) Fail!",
        lpAppName,
        lpKeyName);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
