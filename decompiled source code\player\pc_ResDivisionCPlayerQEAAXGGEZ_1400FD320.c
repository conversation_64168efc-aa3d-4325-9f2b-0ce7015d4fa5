/*
 * Function: ?pc_ResDivision@CPlayer@@QEAAXGGE@Z
 * Address: 0x1400FD320
 */

void __fastcall CPlayer::pc_ResDivision(CPlayer *this, unsigned __int16 wStartSerial, unsigned __int16 wTarSerial, char byMoveAmount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  bool bUpdate; // [sp+20h] [bp-38h]@28
  bool bSend; // [sp+28h] [bp-30h]@28
  char v9; // [sp+30h] [bp-28h]@4
  _STORAGE_LIST::_db_con *pStartOre; // [sp+38h] [bp-20h]@4
  _STORAGE_LIST::_db_con *pTargetOre; // [sp+40h] [bp-18h]@4
  CPlayer *v12; // [sp+60h] [bp+8h]@1
  unsigned __int16 v13; // [sp+70h] [bp+18h]@1
  char v14; // [sp+78h] [bp+20h]@1

  v14 = byMoveAmount;
  v13 = wTarSerial;
  v12 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  pStartOre = 0i64;
  pTargetOre = 0i64;
  pStartOre = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v12->m_Param.m_dbInven.m_nListNum, wStartSerial);
  if ( pStartOre )
  {
    if ( pStartOre->m_bLock )
    {
      v9 = 11;
    }
    else
    {
      pTargetOre = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v12->m_Param.m_dbInven.m_nListNum, v13);
      if ( pTargetOre )
      {
        if ( pTargetOre->m_bLock )
        {
          v9 = 11;
        }
        else if ( IsOverLapItem(pStartOre->m_byTableCode) && IsOverLapItem(pTargetOre->m_byTableCode) )
        {
          if ( pStartOre->m_byCsMethod && pTargetOre->m_byCsMethod && pStartOre->m_dwT != pTargetOre->m_dwT )
          {
            v9 = 23;
          }
          else if ( pStartOre->m_byTableCode == pTargetOre->m_byTableCode )
          {
            if ( pStartOre->m_wItemIndex == pTargetOre->m_wItemIndex )
            {
              if ( pStartOre->m_dwDur >= (unsigned __int8)v14 )
              {
                if ( pTargetOre->m_dwDur + (unsigned __int8)v14 > 0x63 )
                  v9 = 9;
              }
              else
              {
                v9 = 8;
              }
            }
            else
            {
              v9 = 6;
            }
          }
          else
          {
            v9 = 6;
          }
        }
        else
        {
          v9 = 3;
        }
      }
      else
      {
        v9 = 5;
      }
    }
  }
  else
  {
    v9 = 5;
  }
  if ( !v9 )
  {
    bSend = 0;
    bUpdate = 0;
    CPlayer::Emb_AlterDurPoint(v12, 0, pStartOre->m_byStorageIndex, -(unsigned __int8)v14, 0, 0);
    bSend = 0;
    bUpdate = 0;
    CPlayer::Emb_AlterDurPoint(v12, 0, pTargetOre->m_byStorageIndex, (unsigned __int8)v14, 0, 0);
  }
  CPlayer::SendMsg_ResDivision(v12, v9, pStartOre, pTargetOre);
}
