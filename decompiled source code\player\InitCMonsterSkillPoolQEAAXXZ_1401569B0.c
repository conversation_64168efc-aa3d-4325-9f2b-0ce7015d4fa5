/*
 * Function: ?Init@CMonsterSkillPool@@QEAAXXZ
 * Address: 0x1401569B0
 */

void __fastcall CMonsterSkillPool::Init(CMonsterSkillPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CMonsterSkillPool *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5->m_pMyMon = 0i64;
  v5->m_nSize = 0;
  for ( j = 0; j < 16; ++j )
    CMonsterSkill::Init(&v5->m_MonSkill[j]);
}
