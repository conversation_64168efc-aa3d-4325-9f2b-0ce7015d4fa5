/*
 * Function: ?_Destroy@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@IEAAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@0@Z
 * Address: 0x140593200
 */

int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Destroy(__int64 a1, __int64 a2, __int64 a3)
{
  return std::_Destroy_range<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(
           a2,
           a3,
           a1 + 8);
}
