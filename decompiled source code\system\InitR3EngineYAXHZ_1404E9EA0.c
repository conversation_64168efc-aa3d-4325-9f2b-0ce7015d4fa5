/*
 * Function: ?InitR3Engine@@YAXH@Z
 * Address: 0x1404E9EA0
 */

void __fastcall InitR3Engine(int a1)
{
  signed int v1; // eax@1

  v1 = dword_184A77F38;
  dword_184A77F3C = 1;
  if ( a1 == 1 )
    v1 = 1;
  dword_184A77F38 = v1;
  InitCore();
  SetPlayWaveState(dword_184A797E0);
  SetPlayMusicState(dword_184A797E4);
  if ( !dword_184A77F38 )
  {
    InitFunctionKey(0i64);
    InitSpriteManager();
    InitR3Text();
  }
}
