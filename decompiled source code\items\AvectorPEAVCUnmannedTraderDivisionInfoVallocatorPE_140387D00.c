/*
 * Function: ??A?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderDivisionInfo@@_K@Z
 * Address: 0x140387D00
 */

CUnmannedTraderDivisionInfo **__fastcall std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator[](std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
