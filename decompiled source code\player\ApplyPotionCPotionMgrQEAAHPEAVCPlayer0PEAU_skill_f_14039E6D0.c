/*
 * Function: ?ApplyPotion@CPotionMgr@@QEAAHPEAVCPlayer@@0PEAU_skill_fld@@PEAU_CheckPotion_fld@@PEBU_PotionItem_fld@@_N@Z
 * Address: 0x14039E6D0
 */

signed __int64 __fastcall CPotionMgr::ApplyPotion(CPotionMgr *this, CPlayer *pUsePlayer, CPlayer *pApplyPlayer, _skill_fld *pEffecFld, _CheckPotion_fld *pCheckFld, _PotionItem_fld *pfB, bool bCommonPotion)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  __int64 v10; // r8@55
  __int64 v11; // [sp+0h] [bp-B8h]@1
  unsigned int dwDurTime; // [sp+20h] [bp-98h]@52
  int j; // [sp+30h] [bp-88h]@9
  int v14; // [sp+34h] [bp-84h]@14
  int v15; // [sp+38h] [bp-80h]@14
  int v16; // [sp+3Ch] [bp-7Ch]@16
  int v17; // [sp+40h] [bp-78h]@16
  char v18; // [sp+44h] [bp-74h]@16
  int v19; // [sp+48h] [bp-70h]@16
  int k; // [sp+4Ch] [bp-6Ch]@16
  int n; // [sp+50h] [bp-68h]@18
  _base_fld *v22; // [sp+58h] [bp-60h]@18
  _base_fld *v23; // [sp+60h] [bp-58h]@18
  unsigned __int8 v24; // [sp+74h] [bp-44h]@53
  float fValue; // [sp+94h] [bp-24h]@55
  int (__fastcall *v26)(CPlayer *, CPlayer *, __int64, unsigned __int8 *); // [sp+A8h] [bp-10h]@56
  CPotionMgr *v27; // [sp+C0h] [bp+8h]@1
  CPlayer *v28; // [sp+C8h] [bp+10h]@1
  CPlayer *pApplyPlayera; // [sp+D0h] [bp+18h]@1
  _skill_fld *pEffecFlda; // [sp+D8h] [bp+20h]@1

  pEffecFlda = pEffecFld;
  pApplyPlayera = pApplyPlayer;
  v28 = pUsePlayer;
  v27 = this;
  v7 = &v11;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( !pUsePlayer || !pApplyPlayer || !pEffecFld )
    return 0xFFFFFFFFi64;
  if ( pCheckFld )
  {
    for ( j = 0; j < 5; ++j )
    {
      if ( !_CheckPotionData(&pCheckFld->m_CheckEffectCode[j], pApplyPlayera) )
        return 19i64;
    }
  }
  v14 = -1;
  v15 = -1;
  if ( pEffecFlda->m_nContEffectType != -1 && bCommonPotion )
  {
    v16 = 0;
    v17 = 0;
    v18 = 0;
    v19 = 0;
    for ( k = 0; k < 2; ++k )
    {
      n = _ContPotionData::GetEffectIndex((_ContPotionData *)&v28->m_PotionParam + k);
      v22 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 13, n);
      v23 = CRecordData::GetRecord(&v27->m_tblPotionEffectData, n);
      if ( v23
        && *(_DWORD *)&v23[13].m_strCode[36] == pEffecFlda->m_nEffLimType
        && *(_DWORD *)&v23[13].m_strCode[36] != -1
        && pEffecFlda->m_nEffLimType != -1
        || v23
        && *(_DWORD *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType
        && *(_DWORD *)&v23[13].m_strCode[36] == pEffecFlda->m_nEffLimType2
        && *(_DWORD *)&v23[13].m_strCode[36] != -1
        && pEffecFlda->m_nEffLimType2 != -1
        || v23
        && *(_DWORD *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType
        && *(_DWORD *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType2
        && *(_DWORD *)&v23[13].m_strCode[40] == pEffecFlda->m_nEffLimType
        && *(_DWORD *)&v23[13].m_strCode[40] != -1
        && pEffecFlda->m_nEffLimType != -1
        || v23
        && *(_DWORD *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType
        && *(_DWORD *)&v23[13].m_strCode[36] != pEffecFlda->m_nEffLimType2
        && *(_DWORD *)&v23[13].m_strCode[40] != pEffecFlda->m_nEffLimType
        && *(_DWORD *)&v23[13].m_strCode[40] == pEffecFlda->m_nEffLimType2
        && *(_DWORD *)&v23[13].m_strCode[40] != -1
        && pEffecFlda->m_nEffLimType2 != -1 )
      {
        if ( v22 )
        {
          if ( (signed int)v22[5].m_dwIndex <= pfB->m_nPotionCheck )
          {
            v18 = 1;
            v19 = k;
          }
        }
      }
    }
    if ( CExtPotionBuf::IsExtPotionUse(&pApplyPlayera->m_PotionBufUse) )
    {
      if ( v18 )
        v16 = v19;
      else
        v16 = CPotionMgr::SelectDeleteBuf(v27, pApplyPlayera, 1, 0);
    }
    else
    {
      v16 = CPotionMgr::SelectDeleteBuf(v27, pApplyPlayera, 0, 0);
    }
    if ( v16 > 2 )
      return 25i64;
    dwDurTime = pEffecFlda->m_nContEffectSec[0];
    v14 = CPotionMgr::InsertPotionContEffect(
            v27,
            pApplyPlayera,
            (_ContPotionData *)&pApplyPlayera->m_PotionParam + v16,
            pEffecFlda,
            dwDurTime);
  }
  v24 = -1;
  if ( pEffecFlda->m_nTempEffectType != -1 )
  {
    if ( pEffecFlda->m_nTempEffectType >= 150 )
    {
      v15 = -1;
    }
    else
    {
      fValue = 0.0;
      if ( _GetTempEffectValue(pEffecFlda, pEffecFlda->m_nTempEffectType, &fValue) )
      {
        v26 = (int (__fastcall *)(CPlayer *, CPlayer *, __int64, unsigned __int8 *))g_TempEffectFunc[pEffecFlda->m_nTempEffectType];
        if ( v26 && (unsigned __int8)v26(v28, pApplyPlayera, v10, &v24) )
          v15 = 0;
        else
          v15 = v24;
      }
      else
      {
        v15 = -1;
      }
    }
  }
  if ( v14 && v15 )
    result = (unsigned int)v14;
  else
    result = 0i64;
  return result;
}
