/*
 * Function: _dynamic_initializer_for__CPathMgr::ms_BackupPath__
 * Address: 0x1406DC190
 */

__int64 dynamic_initializer_for__CPathMgr::ms_BackupPath__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CPathMgr::CPathMgr(&CPathMgr::ms_BackupPath);
  return atexit(dynamic_atexit_destructor_for__CPathMgr::ms_BackupPath__);
}
