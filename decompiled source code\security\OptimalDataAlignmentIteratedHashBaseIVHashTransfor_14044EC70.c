/*
 * Function: ?OptimalDataAlignment@?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@UEBAIXZ
 * Address: 0x14044EC70
 */

unsigned int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::OptimalDataAlignment(CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1

  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CryptoPP::GetAlignmentOf<unsigned int>(0i64);
}
