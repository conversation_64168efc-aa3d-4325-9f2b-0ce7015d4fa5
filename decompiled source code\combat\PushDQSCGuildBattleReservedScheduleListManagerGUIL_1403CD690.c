/*
 * Function: ?PushDQS@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXKK@Z
 * Address: 0x1403CD690
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::PushDQS(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int dwMapID, unsigned int dwSLID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@4
  __int64 v6; // [sp+0h] [bp-68h]@1
  _qry_case_updatereservedschedule v7; // [sp+38h] [bp-30h]@4

  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.dwMapID = dwMapID;
  v7.dwSLID = dwSLID;
  v7.byLoadDataStartPosition = 0;
  v5 = _qry_case_updatereservedschedule::size(&v7);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 41, (char *)&v7, v5);
}
