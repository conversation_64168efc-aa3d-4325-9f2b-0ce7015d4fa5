/*
 * Function: ??$_Uninit_move@PEAVInteger@CryptoPP@@PEAV12@V?$allocator@VInteger@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAVInteger@CryptoPP@@PEAV12@00AEAV?$allocator@VInteger@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7790
 */

int __fastcall std::_Uninit_move<CryptoPP::Integer *,CryptoPP::Integer *,std::allocator<CryptoPP::Integer>,std::_Undefined_move_tag>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::Integer *,CryptoPP::Integer *,std::allocator<CryptoPP::Integer>>(
           a1,
           a2,
           a3,
           a4);
}
