/*
 * Function: SQLGetInstalledDriversW
 * Address: 0x1404DAEEC
 */

int __fastcall SQLGetInstalledDriversW(unsigned __int16 *lpszBuf, unsigned __int16 cbBufMax, unsigned __int16 *pcbBufOut)
{
  unsigned __int16 *v3; // rsi@1
  unsigned __int16 *v4; // rbx@1
  unsigned __int16 v5; // di@1
  __int64 (__cdecl *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszBuf;
  v4 = pcbBufOut;
  v5 = cbBufMax;
  v6 = ODBC___GetSetupProc("SQLGetInstalledDriversW");
  if ( v6 )
    result = ((int (__fastcall *)(unsigned __int16 *, _QWORD, unsigned __int16 *))v6)(v3, v5, v4);
  else
    result = 0;
  return result;
}
