/*
 * Function: ?Select_Avator_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3890
 */

void __fastcall CMainThread::Select_Avator_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  unsigned int dwAddDalant; // [sp+20h] [bp-78h]@7
  unsigned int dwAddGold; // [sp+28h] [bp-70h]@7
  unsigned int dwCheckSum; // [sp+30h] [bp-68h]@7
  bool *pbTrunkAddItem; // [sp+38h] [bp-60h]@7
  char byTrunkOldSlot; // [sp+40h] [bp-58h]@7
  long double v10; // [sp+48h] [bp-50h]@7
  long double v11; // [sp+50h] [bp-48h]@7
  bool bCreateTrunkFree; // [sp+58h] [bp-40h]@7
  bool *pbExtTrunkAddItem; // [sp+60h] [bp-38h]@7
  char byExtTrunkOldSlot; // [sp+68h] [bp-30h]@7
  char v15; // [sp+70h] [bp-28h]@4
  CUserDB *v16; // [sp+78h] [bp-20h]@4
  char *v17; // [sp+80h] [bp-18h]@7
  CMainThread *v18; // [sp+A0h] [bp+8h]@1
  _DB_QRY_SYN_DATA *v19; // [sp+A8h] [bp+10h]@1

  v19 = pData;
  v18 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = 0;
  v16 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v16->m_bActive )
  {
    if ( v16->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
    {
      v17 = pData->m_sData;
      byExtTrunkOldSlot = pData->m_sData[37505];
      pbExtTrunkAddItem = (bool *)&pData->m_sData[37465];
      bCreateTrunkFree = pData->m_sData[37456];
      v11 = *(double *)&pData->m_sData[37448];
      v10 = *(double *)&pData->m_sData[37440];
      byTrunkOldSlot = pData->m_sData[37432];
      pbTrunkAddItem = (bool *)&pData->m_sData[37332];
      dwCheckSum = *(_DWORD *)&pData->m_sData[37328];
      dwAddGold = *(_DWORD *)&pData->m_sData[37324];
      dwAddDalant = *(_DWORD *)&pData->m_sData[37320];
      CUserDB::Select_Char_Complete(
        v16,
        pData->m_byResult,
        (_AVATOR_DATA *)&pData->m_sData[4],
        (bool *)&pData->m_sData[37219],
        dwAddDalant,
        dwAddGold,
        dwCheckSum,
        (bool *)&pData->m_sData[37332],
        byTrunkOldSlot,
        v10,
        v11,
        bCreateTrunkFree,
        (bool *)&pData->m_sData[37465],
        byExtTrunkOldSlot);
      dwAddDalant = *((_DWORD *)v17 + 9365);
      CMainThread::_db_complete_event_classrefine(
        v18,
        v19->m_idWorld.wIndex,
        v19->m_idWorld.dwSerial,
        v17[37464],
        dwAddDalant);
    }
  }
}
