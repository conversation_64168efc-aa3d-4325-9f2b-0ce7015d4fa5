/*
 * Function: ?Destroy@CUnmannedTraderUserInfoTable@@SAXXZ
 * Address: 0x140363780
 */

void CUnmannedTraderUserInfoTable::Destroy(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-48h]@1
  CUnmannedTraderUserInfoTable *v3; // [sp+20h] [bp-28h]@5
  struct CUnmannedTraderUserInfoTable *v4; // [sp+28h] [bp-20h]@5
  void *v5; // [sp+30h] [bp-18h]@6

  v0 = &v2;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  if ( CUnmannedTraderUserInfoTable::ms_Instance )
  {
    v4 = CUnmannedTraderUserInfoTable::ms_Instance;
    v3 = CUnmannedTraderUserInfoTable::ms_Instance;
    if ( CUnmannedTraderUserInfoTable::ms_Instance )
      v5 = CUnmannedTraderUserInfoTable::`scalar deleting destructor'(v3, 1u);
    else
      v5 = 0i64;
    CUnmannedTraderUserInfoTable::ms_Instance = 0i64;
  }
}
