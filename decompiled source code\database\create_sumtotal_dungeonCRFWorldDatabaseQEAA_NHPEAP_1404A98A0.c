/*
 * Function: ?create_sumtotal_dungeon@CRFWorldDatabase@@QEAA_NHPEAPEAD@Z
 * Address: 0x1404A98A0
 */

bool __usercall CRFWorldDatabase::create_sumtotal_dungeon@<al>(CRFWorldDatabase *this@<rcx>, int nRecodeNum@<edx>, char **ppKey@<r8>, signed __int64 a4@<rax>)
{
  void *v4; // rsp@1
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@7
  __int64 v9; // [sp-20h] [bp-27A8h]@1
  unsigned int v10; // [sp+0h] [bp-2788h]@7
  unsigned int v11; // [sp+8h] [bp-2780h]@7
  unsigned int v12; // [sp+10h] [bp-2778h]@7
  unsigned int v13; // [sp+18h] [bp-2770h]@7
  unsigned int v14; // [sp+20h] [bp-2768h]@7
  unsigned int v15; // [sp+28h] [bp-2760h]@7
  char Dest; // [sp+40h] [bp-2748h]@4
  char v17; // [sp+41h] [bp-2747h]@4
  int v18; // [sp+2754h] [bp-34h]@4
  unsigned int v19; // [sp+2758h] [bp-30h]@4
  int j; // [sp+275Ch] [bp-2Ch]@4
  int v21; // [sp+2760h] [bp-28h]@7
  unsigned __int64 v22; // [sp+2770h] [bp-18h]@4
  CRFWorldDatabase *v23; // [sp+2790h] [bp+8h]@1
  int v24; // [sp+2798h] [bp+10h]@1
  char **v25; // [sp+27A0h] [bp+18h]@1

  v25 = ppKey;
  v24 = nRecodeNum;
  v23 = this;
  v4 = alloca(a4);
  v5 = &v9;
  for ( i = 2536i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v22 = (unsigned __int64)&v9 ^ _security_cookie;
  Dest = 0;
  memset(&v17, 0, 0x270Fui64);
  v18 = 0;
  v19 = GetLocalDate();
  sprintf(
    &Dest,
    "CREATE TABLE [dbo].[tbl_sumtotal_dungeon_%d] ( [Serial] [int] IDENTITY(1, 1) NOT NULL, [tm] [datetime] NOT NULL , [R"
    "ace] [tinyint] NOT NULL",
    v19);
  for ( j = 0; j < v24; ++j )
  {
    v18 = strlen_0(&Dest);
    sprintf(&Dest + v18, ",[Sum_%s] [int] DEFAULT (1) NOT NULL", v25[j]);
  }
  v7 = strlen_0(&Dest);
  v18 = v7;
  v21 = v7;
  v15 = v19;
  v14 = v19;
  v13 = v19;
  v12 = v19;
  v11 = v19;
  v10 = v19;
  sprintf(
    &Dest + v7,
    ") ON [PRIMARY] ALTER TABLE [dbo].[tbl_sumtotal_dungeon_%d] WITH NOCHECK ADD  CONSTRAINT [PK_tbl_sumtotal_dungeon_%d]"
    " PRIMARY KEY CLUSTERED ([Serial]) ON [PRIMARY] ALTER TABLE [dbo].[tbl_sumtotal_dungeon_%d] WITH NOCHECK ADD CONSTRAI"
    "NT [DF_tbl_sumtotal_dungeon_%d_tm] DEFAULT (getdate()) FOR [tm] CREATE INDEX [IX_tbl_sumtotal_dungeon_%d_tm] ON [dbo"
    "].[tbl_sumtotal_dungeon_%d]([tm]) ON [PRIMARY] CREATE INDEX [IX_tbl_sumtotal_dungeon_%d_race] ON [dbo].[tbl_sumtotal"
    "_dungeon_%d] ([Race]) ON [PRIMARY]",
    v19,
    v19);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v23->vfptr, &Dest, 1);
}
