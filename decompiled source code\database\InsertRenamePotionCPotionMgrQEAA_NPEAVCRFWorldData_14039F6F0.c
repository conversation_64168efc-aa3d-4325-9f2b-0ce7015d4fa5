/*
 * Function: ?InsertRenamePotion@CPotionMgr@@QEAA_NPEAVCRFWorldDatabase@@PEAD@Z
 * Address: 0x14039F6F0
 */

char __fastcall CPotionMgr::InsertRenamePotion(CPotionMgr *this, CRFWorldDatabase *pkWorldDB, char *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  char *v8; // [sp+28h] [bp-10h]@4

  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 1;
  v8 = pData;
  if ( !CRFWorldDatabase::Insert_RenamePotionLog(pkWorldDB, *(_DWORD *)pData, pData + 4, pData + 21) )
    v7 = 0;
  return v7;
}
