/*
 * Function: ?swap@MessageQueue@CryptoPP@@QEAAXAEAV12@@Z
 * Address: 0x140654890
 */

void __fastcall CryptoPP::MessageQueue::swap(CryptoPP::MessageQueue *this, struct CryptoPP::MessageQueue *a2)
{
  CryptoPP::MessageQueue *v2; // [sp+30h] [bp+8h]@1
  struct CryptoPP::MessageQueue *v3; // [sp+38h] [bp+10h]@1

  v3 = a2;
  v2 = this;
  CryptoPP::ByteQueue::swap((CryptoPP::ByteQueue *)((char *)this + 32), (struct CryptoPP::ByteQueue *)((char *)a2 + 32));
  std::deque<unsigned __int64,std::allocator<unsigned __int64>>::swap((char *)v2 + 112, (char *)v3 + 112);
}
