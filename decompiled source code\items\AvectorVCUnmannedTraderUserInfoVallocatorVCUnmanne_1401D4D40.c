/*
 * Function: ??A?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAAAEAVCUnmannedTraderUserInfo@@_K@Z
 * Address: 0x1401D4D40
 */

CUnmannedTraderUserInfo *__fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
