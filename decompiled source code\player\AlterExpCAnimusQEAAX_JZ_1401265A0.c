/*
 * Function: ?AlterExp@CAnimus@@QEAAX_J@Z
 * Address: 0x1401265A0
 */

void __fastcall CAnimus::AlterExp(CAnimus *this, __int64 nAddExp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@7
  signed __int64 v5; // rax@15
  int v6; // eax@34
  __int64 v7; // [sp+0h] [bp-68h]@1
  unsigned __int64 v8; // [sp+20h] [bp-48h]@23
  double v9; // [sp+30h] [bp-38h]@8
  __int64 v10; // [sp+38h] [bp-30h]@11
  unsigned __int64 v11; // [sp+40h] [bp-28h]@21
  unsigned __int64 dwExp; // [sp+48h] [bp-20h]@29
  _animus_fld *v13; // [sp+50h] [bp-18h]@29
  int v14; // [sp+58h] [bp-10h]@7
  CAnimus *v15; // [sp+70h] [bp+8h]@1
  __int64 nAlterExp; // [sp+78h] [bp+10h]@1
  __int64 nAlterExpa; // [sp+78h] [bp+10h]@13
  unsigned __int64 nAlterExpb; // [sp+78h] [bp+10h]@15

  nAlterExp = nAddExp;
  v15 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v15->m_pMaster )
  {
    if ( v15->m_pMaster->m_bOper )
    {
      v14 = ((int (__fastcall *)(CAnimus *))v15->vfptr->GetLevel)(v15);
      v4 = (unsigned __int8)CAnimus::GetMaxLevel(v15);
      if ( v14 < (unsigned __int8)v4 )
      {
        v9 = DOUBLE_1_0;
        if ( v15->m_pMaster && v15->m_pMaster->m_bOper )
        {
          TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v15->m_pMaster->m_id.wIndex);
          v9 = DOUBLE_1_0;
        }
        v10 = nAlterExp;
        if ( v15->m_pMaster && CPlayer::IsApplyPcbangPrimium(v15->m_pMaster) )
          nAlterExpa = (unsigned int)(signed int)ffloor((float)(signed int)nAlterExp + (float)((float)(signed int)v10
                                                                                             * (float)(PCBANG_PRIMIUM_FAVOR::ANIMUS_EXP - 1.0)));
        else
          nAlterExpa = (unsigned int)(signed int)ffloor((float)(signed int)nAlterExp + (float)((float)(signed int)v10
                                                                                             * (float)(ANIMUS_EXP_RATE - 1.0)));
        v5 = (unsigned int)(signed int)floor((double)(signed int)nAlterExpa * v9);
        nAlterExpb = v5;
        if ( v5 <= 0 )
        {
          if ( v5 < 0 && v15->m_dwExp < -v5 )
            nAlterExpb = -v15->m_dwExp;
        }
        else if ( v15->m_pRecord->m_nForLvUpExp - v15->m_dwExp < v5 )
        {
          if ( v15->m_pRecord->m_nForLvUpExp - v15->m_dwExp <= 0x7FFFFFFFFFFFFFFFi64 )
          {
            nAlterExpb = v15->m_pRecord->m_nForLvUpExp - v15->m_dwExp;
          }
          else
          {
            nAlterExpb = 0x7FFFFFFFFFFFFFFFi64;
            CLogFile::Write(
              &stru_1799C8E78,
              "CAnimus::AlterExp(__int64 nAddExp(%I64d) ) : m_pRecord->m_nForLvUpExp - m_dwExp(%I64u) Invalid!",
              0x7FFFFFFFFFFFFFFFi64,
              v15->m_pRecord->m_nForLvUpExp - v15->m_dwExp);
          }
          if ( -1i64 - v15->m_dwExp < nAlterExpb )
          {
            v11 = nAlterExpb;
            if ( -1i64 - v15->m_dwExp <= 0x7FFFFFFFFFFFFFFFi64 )
            {
              nAlterExpb = -1i64 - v15->m_dwExp;
              v8 = -1i64 - v15->m_dwExp;
              CLogFile::Write(
                &stru_1799C8E78,
                "CAnimus::AlterExp( __int64 nAddExp ) : _UI64_MAX < m_dwExp(%I64u), nOldExp(%I64u) : nAddExp(%I64d) Invalid!",
                v15->m_dwExp,
                v11);
            }
            else
            {
              nAlterExpb = 0x7FFFFFFFFFFFFFFFi64;
              CLogFile::Write(
                &stru_1799C8E78,
                "CAnimus::AlterExp( __int64 nAddExp ) : ( _UI64_MAX < m_dwExp + nAddExp ) && ( _I64_MAX < _UI64_MAX - m_d"
                "wExp )\r\n"
                "nOldExp(%I64u) : nAddExp(%I64d) Invalid!",
                v11,
                0x7FFFFFFFFFFFFFFFi64);
            }
          }
        }
        if ( nAlterExpb )
        {
          dwExp = nAlterExpb + v15->m_dwExp;
          v13 = GetAnimusFldFromExp(v15->m_byClassCode, dwExp);
          if ( v13 == v15->m_pRecord )
          {
            v15->m_dwExp = dwExp;
            CAnimus::AlterExp_MasterReport(v15, nAlterExpb);
          }
          else if ( v15->m_pMaster )
          {
            if ( (((int (__fastcall *)(CPlayer *))v15->m_pMaster->vfptr->GetLevel)(v15->m_pMaster) >= 50
               || v13->m_nLevel > 50)
              && (((int (__fastcall *)(CPlayer *))v15->m_pMaster->vfptr->GetLevel)(v15->m_pMaster) < 50
               || (v6 = ((int (__fastcall *)(_QWORD))v15->m_pMaster->vfptr->GetLevel)(v15->m_pMaster),
                   v13->m_nLevel > v6 + 1)) )
            {
              v15->m_dwExp = dwExp - 1;
              CAnimus::AlterExp_MasterReport(v15, nAlterExpb - 1);
            }
            else
            {
              v15->m_dwExp = dwExp;
              CAnimus::AlterExp_MasterReport(v15, nAlterExpb);
              v15->m_pRecord = v13;
              CAnimus::SendMsg_LevelUp(v15);
              v15->m_nMaxHP = v15->m_pRecord->m_nMaxHP;
              v15->m_nMaxFP = v15->m_pRecord->m_nMaxFP;
            }
          }
        }
      }
    }
  }
}
