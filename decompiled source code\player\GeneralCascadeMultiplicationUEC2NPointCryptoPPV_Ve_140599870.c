/*
 * Function: ??$GeneralCascadeMultiplication@UEC2NPoint@CryptoPP@@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@@CryptoPP@@YA?AUEC2NPoint@0@AEBV?$AbstractGroup@UEC2NPoint@CryptoPP@@@0@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@1@Z
 * Address: 0x140599870
 */

__int64 __fastcall CryptoPP::GeneralCascadeMultiplication<CryptoPP::EC2NPoint,std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v3; // rax@1
  __int64 v4; // rax@2
  __int64 v5; // rax@2
  __int64 result; // rax@2
  __int64 v7; // rax@3
  __int64 v8; // rax@4
  __int64 v9; // rax@4
  __int64 v10; // rax@4
  __int64 v11; // rax@4
  __int64 v12; // rax@4
  __int64 v13; // rax@4
  __int64 v14; // rax@6
  __int64 v15; // rax@7
  __int64 v16; // rax@7
  CryptoPP::Integer *v17; // rax@7
  __int64 v18; // rax@8
  __int64 v19; // rax@8
  __int64 v20; // rax@9
  __int64 v21; // rax@9
  __int64 v22; // rax@9
  __int64 v23; // rax@11
  __int64 v24; // rax@11
  char v25; // [sp+30h] [bp-2E8h]@5
  CryptoPP::Integer a; // [sp+48h] [bp-2D0h]@5
  CryptoPP::Integer v27; // [sp+70h] [bp-2A8h]@5
  char v28; // [sp+98h] [bp-280h]@4
  char v29; // [sp+B0h] [bp-268h]@4
  char v30; // [sp+C8h] [bp-250h]@5
  char *v31; // [sp+E0h] [bp-238h]@5
  char v32; // [sp+E8h] [bp-230h]@5
  char *v33; // [sp+100h] [bp-218h]@5
  char v34; // [sp+108h] [bp-210h]@5
  char *v35; // [sp+120h] [bp-1F8h]@5
  char v36; // [sp+128h] [bp-1F0h]@5
  char *v37; // [sp+140h] [bp-1D8h]@5
  CryptoPP::EC2NPoint v38; // [sp+148h] [bp-1D0h]@9
  char v39; // [sp+180h] [bp-198h]@10
  char *v40; // [sp+198h] [bp-180h]@10
  char v41; // [sp+1A0h] [bp-178h]@10
  char *v42; // [sp+1B8h] [bp-160h]@10
  char v43; // [sp+1C0h] [bp-158h]@10
  char *v44; // [sp+1D8h] [bp-140h]@10
  char v45; // [sp+1E0h] [bp-138h]@10
  char *v46; // [sp+1F8h] [bp-120h]@10
  int v47; // [sp+200h] [bp-118h]@1
  __int64 v48; // [sp+208h] [bp-110h]@1
  __int64 v49; // [sp+210h] [bp-108h]@2
  __int64 v50; // [sp+220h] [bp-F8h]@4
  __int64 v51; // [sp+228h] [bp-F0h]@4
  __int64 v52; // [sp+230h] [bp-E8h]@4
  __int64 v53; // [sp+238h] [bp-E0h]@4
  __int64 v54; // [sp+240h] [bp-D8h]@4
  __int64 v55; // [sp+248h] [bp-D0h]@4
  __int64 v56; // [sp+250h] [bp-C8h]@4
  __int64 v57; // [sp+258h] [bp-C0h]@4
  __int64 v58; // [sp+260h] [bp-B8h]@5
  __int64 v59; // [sp+268h] [bp-B0h]@5
  __int64 v60; // [sp+270h] [bp-A8h]@5
  __int64 v61; // [sp+278h] [bp-A0h]@5
  __int64 v62; // [sp+280h] [bp-98h]@5
  __int64 v63; // [sp+288h] [bp-90h]@5
  struct CryptoPP::Integer *v64; // [sp+290h] [bp-88h]@7
  __int64 v65; // [sp+298h] [bp-80h]@8
  __int64 v66; // [sp+2A0h] [bp-78h]@8
  __int64 v67; // [sp+2A8h] [bp-70h]@9
  __int64 v68; // [sp+2B0h] [bp-68h]@9
  __int64 v69; // [sp+2B8h] [bp-60h]@9
  __int64 v70; // [sp+2C0h] [bp-58h]@9
  __int64 v71; // [sp+2C8h] [bp-50h]@10
  __int64 v72; // [sp+2D0h] [bp-48h]@10
  __int64 v73; // [sp+2D8h] [bp-40h]@10
  __int64 v74; // [sp+2E0h] [bp-38h]@10
  __int64 v75; // [sp+2E8h] [bp-30h]@10
  __int64 v76; // [sp+2F0h] [bp-28h]@10
  __int64 v77; // [sp+2F8h] [bp-20h]@11
  __int64 v78; // [sp+300h] [bp-18h]@11
  __int64 v79; // [sp+320h] [bp+8h]@1
  __int64 v80; // [sp+328h] [bp+10h]@1
  __int64 v81; // [sp+330h] [bp+18h]@1

  v81 = a3;
  v80 = a2;
  v79 = a1;
  v48 = -2i64;
  v47 = 0;
  LODWORD(v3) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-();
  if ( v3 == 1 )
  {
    LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
    v49 = v4 + 56;
    LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
    (*(void (__fastcall **)(__int64, __int64, __int64, __int64))(*(_QWORD *)v80 + 80i64))(v80, v79, v5, v49);
    v47 |= 1u;
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
    result = v79;
  }
  else
  {
    LODWORD(v7) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-();
    if ( v7 == 2 )
    {
      v8 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator+(
             v81,
             (__int64)&v28,
             1i64);
      v50 = v8;
      v51 = v8;
      LODWORD(v9) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v8);
      v52 = v9 + 56;
      v10 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator+(
              v81,
              (__int64)&v29,
              1i64);
      v53 = v10;
      v54 = v10;
      LODWORD(v11) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v10);
      v55 = v11;
      LODWORD(v12) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
      v56 = v12 + 56;
      LODWORD(v13) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
      v57 = *(_QWORD *)v80;
      (*(void (__fastcall **)(__int64, __int64, __int64, __int64))(v57 + 88))(v80, v79, v13, v56);
      v47 |= 1u;
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      result = v79;
    }
    else
    {
      CryptoPP::Integer::Integer(&a);
      CryptoPP::Integer::Integer(&v27);
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v25);
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator--(&v25);
      v31 = &v30;
      v33 = &v32;
      v58 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v30);
      v59 = v58;
      v60 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v33);
      std::make_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(
        v60,
        v59);
      v35 = &v34;
      v37 = &v36;
      v61 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v34);
      v62 = v61;
      v63 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v37);
      std::pop_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(
        v63,
        v62);
      while ( 1 )
      {
        LODWORD(v14) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
        if ( (unsigned __int8)CryptoPP::Integer::operator!(v14 + 56) )
          break;
        std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(&v25);
        CryptoPP::Integer::operator=(&v27);
        LODWORD(v15) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
        v64 = (struct CryptoPP::Integer *)(v15 + 56);
        LODWORD(v16) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(&v25);
        CryptoPP::Integer::Divide((struct CryptoPP::Integer *)(v16 + 56), &a, &v27, v64);
        v17 = (CryptoPP::Integer *)CryptoPP::Integer::One();
        if ( CryptoPP::operator==(&a, v17) )
        {
          LODWORD(v18) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(&v25);
          v65 = v18;
          LODWORD(v19) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
          v66 = *(_QWORD *)v80;
          (*(void (__fastcall **)(__int64, __int64, __int64))(v66 + 64))(v80, v19, v65);
        }
        else
        {
          LODWORD(v20) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(&v25);
          v67 = *(_QWORD *)v80;
          LODWORD(v21) = (*(int (__fastcall **)(__int64, CryptoPP::EC2NPoint *, __int64, CryptoPP::Integer *))(v67 + 80))(
                           v80,
                           &v38,
                           v20,
                           &a);
          v68 = v21;
          v69 = v21;
          LODWORD(v22) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(v81);
          v70 = *(_QWORD *)v80;
          (*(void (__fastcall **)(__int64, __int64, __int64))(v70 + 64))(v80, v22, v69);
          CryptoPP::EC2NPoint::~EC2NPoint(&v38);
        }
        v40 = &v39;
        v42 = &v41;
        v71 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v39);
        v72 = v71;
        v73 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v42);
        std::push_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(
          v73,
          v72);
        v44 = &v43;
        v46 = &v45;
        v74 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v43);
        v75 = v74;
        v76 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v46);
        std::pop_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(
          v76,
          v75);
      }
      LODWORD(v23) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(&v25);
      v77 = v23 + 56;
      LODWORD(v24) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator->(&v25);
      v78 = *(_QWORD *)v80;
      (*(void (__fastcall **)(__int64, __int64, __int64, __int64))(v78 + 80))(v80, v79, v24, v77);
      v47 |= 1u;
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      CryptoPP::Integer::~Integer(&v27);
      CryptoPP::Integer::~Integer(&a);
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
      result = v79;
    }
  }
  return result;
}
