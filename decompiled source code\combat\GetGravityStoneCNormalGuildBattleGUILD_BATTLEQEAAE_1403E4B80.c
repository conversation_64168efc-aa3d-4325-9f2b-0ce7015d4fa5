/*
 * Function: ?GetGravityStone@CNormalGuildBattle@GUILD_BATTLE@@QEAAEGKK@Z
 * Address: 0x1403E4B80
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::GetGravityStone(GUILD_BATTLE::CNormalGuildBattle *this, unsigned __int16 wIndex, unsigned int dwObjSerial, unsigned int dwCharacSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v7; // [sp+0h] [bp-48h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *pkTakeGuild; // [sp+20h] [bp-28h]@4
  CPlayer *pkPlayer; // [sp+28h] [bp-20h]@4
  char v10; // [sp+30h] [bp-18h]@8
  GUILD_BATTLE::CNormalGuildBattle *v11; // [sp+50h] [bp+8h]@1
  unsigned __int16 v12; // [sp+58h] [bp+10h]@1
  unsigned int dwObjSeriala; // [sp+60h] [bp+18h]@1
  unsigned int dwSerial; // [sp+68h] [bp+20h]@1

  dwSerial = dwCharacSerial;
  dwObjSeriala = dwObjSerial;
  v12 = wIndex;
  v11 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  pkTakeGuild = &v11->m_k1P;
  pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v11->m_k1P, dwCharacSerial);
  if ( !pkPlayer )
  {
    pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(&v11->m_k2P, dwSerial);
    pkTakeGuild = &v11->m_k2P;
  }
  if ( pkPlayer )
  {
    v10 = GUILD_BATTLE::CNormalGuildBattleField::GetBall(v11->m_pkField, v12, dwObjSeriala, pkPlayer);
    if ( v10 )
    {
      result = v10;
    }
    else
    {
      GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(&v11->m_k1P, pkTakeGuild, pkPlayer, -1);
      GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(&v11->m_k2P, pkTakeGuild, pkPlayer, -1);
      result = 0;
    }
  }
  else
  {
    result = -111;
  }
  return result;
}
