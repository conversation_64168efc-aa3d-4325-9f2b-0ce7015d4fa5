/*
 * Function: ?pc_RequestPatriarchPunishment@CPlayer@@QEAAXEPEAD0@Z
 * Address: 0x140100720
 */

void __fastcall CPlayer::pc_RequestPatriarchPunishment(CPlayer *this, char byType, char *pwszName, char *pwszCont)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v6; // rax@5
  char *v7; // rax@12
  int v8; // eax@14
  __int64 v9; // [sp+0h] [bp-598h]@1
  char *pQryData; // [sp+20h] [bp-578h]@12
  char v11; // [sp+30h] [bp-568h]@4
  _qry_case_select_charserial v12; // [sp+50h] [bp-548h]@14
  int v13; // [sp+580h] [bp-18h]@5
  unsigned __int64 v14; // [sp+588h] [bp-10h]@4
  CPlayer *v15; // [sp+5A0h] [bp+8h]@1
  char v16; // [sp+5A8h] [bp+10h]@1
  char *wszStr; // [sp+5B0h] [bp+18h]@1
  const char *Source; // [sp+5B8h] [bp+20h]@1

  Source = pwszCont;
  wszStr = pwszName;
  v16 = byType;
  v15 = this;
  v4 = &v9;
  for ( i = 356i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  v11 = 0;
  if ( CMainThread::IsReleaseServiceMode(&g_Main)
    && (v13 = CPlayerDB::GetRaceCode(&v15->m_Param),
        v6 = CPvpUserAndGuildRankingSystem::Instance(),
        CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v6, v13, 0) != v15->m_dwObjSerial) )
  {
    v11 = 1;
  }
  else if ( LOBYTE(g_VoteSys[760 * CPlayerDB::GetRaceCode(&v15->m_Param)]) )
  {
    v11 = 2;
  }
  else if ( v16 == 2 )
  {
    v11 = 9;
  }
  else if ( !IsSQLValidString(wszStr) )
  {
    v7 = CPlayerDB::GetCharNameA(&v15->m_Param);
    pQryData = wszStr;
    CLogFile::Write(
      &stru_1799C8E78,
      "CPlayer::pc_RequestPatriarchPunishment() : %u(%s) ::IsSQLValidString(pwszName(%s)) Invalid!",
      v15->m_dwObjSerial,
      v7);
    v11 = 7;
  }
  if ( v11 )
  {
    CPlayer::SendMsg_ProposeVoteResult(v15, v11);
  }
  else
  {
    v12.byRace = CPlayerDB::GetRaceCode(&v15->m_Param);
    v12.byType = v16;
    strcpy_0(v12.wszCharName, wszStr);
    strcpy_0(v12.wszContent, Source);
    v12.dwAvatorSerial = -1;
    v8 = _qry_case_select_charserial::size(&v12);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -128, &v12.byRace, v8);
  }
}
