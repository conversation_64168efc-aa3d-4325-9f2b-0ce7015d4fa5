/*
 * Function: ?dtor$0@?0??FoundAvailableVirtualProcessor@SchedulerBase@details@Concurrency@@QEAA_NAEAVClaimTicket@VirtualProcessor@23@Vlocation@3@K@Z@4HA_8
 * Address: 0x14060D1F0
 */

void __fastcall `Concurrency::details::SchedulerBase::FoundAvailableVirtualProcessor'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  __int64 v2; // rcx@1

  v2 = *(_QWORD *)(a2 + 240);
  CryptoPP::AbstractGroup<CryptoPP::ECPPoint>::~AbstractGroup<CryptoPP::ECPPoint>();
}
