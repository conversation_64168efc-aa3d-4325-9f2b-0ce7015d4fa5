/*
 * Function: ?IsolatedInitialize@FileSink@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x14061DE50
 */

void __fastcall CryptoPP::FileSink::IsolatedInitialize(CryptoPP::FileSink *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::Name *v2; // rcx@4
  const char *v3; // rax@4
  CryptoPP::Name *v4; // rcx@4
  const char *v5; // rax@5
  __int64 v6; // rax@8
  __int64 v7; // rax@8
  std::basic_ostream<char,std::char_traits<char> > *v8; // rax@11
  const char *v9; // rax@12
  __int64 v10; // [sp+20h] [bp-D8h]@4
  int v11; // [sp+28h] [bp-D0h]@8
  void *v12; // [sp+30h] [bp-C8h]@4
  _QWORD *v13; // [sp+38h] [bp-C0h]@1
  __int64 v14; // [sp+40h] [bp-B8h]@8
  char v15; // [sp+48h] [bp-B0h]@9
  char v16; // [sp+98h] [bp-60h]@9
  unsigned __int8 v17; // [sp+C8h] [bp-30h]@9
  __int64 v18; // [sp+D0h] [bp-28h]@1
  void *v19; // [sp+D8h] [bp-20h]@2
  int v20; // [sp+E0h] [bp-18h]@6
  int v21; // [sp+E4h] [bp-14h]@8
  std::basic_ostream<char,std::char_traits<char> > **v22; // [sp+E8h] [bp-10h]@12
  CryptoPP::FileSink *v23; // [sp+100h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v24; // [sp+108h] [bp+10h]@1

  v24 = a2;
  v23 = this;
  v18 = -2i64;
  v13 = operator new(0xF8ui64);
  if ( v13 )
  {
    std::basic_ofstream<char,std::char_traits<char>>::basic_ofstream<char,std::char_traits<char>>(v13, 1i64);
    v13[19] = &std::basic_ofstream<char,std::char_traits<char>>::`local vftable';
    v19 = v13;
  }
  else
  {
    v19 = 0i64;
  }
  v12 = v19;
  CryptoPP::member_ptr<std::basic_ofstream<char,std::char_traits<char>>>::reset(&v23->m_file, v19);
  v3 = CryptoPP::Name::OutputFileName(v2);
  if ( (unsigned __int8)CryptoPP::NameValuePairs::GetValue<char const *>(v24, v3, &v10) )
  {
    v5 = CryptoPP::Name::OutputBinaryMode(v4);
    if ( CryptoPP::NameValuePairs::GetValueWithDefault<bool>((__int64)v24, (__int64)v5, 1) )
      v20 = 32;
    else
      v20 = 0;
    v11 = v20;
    v21 = v20 | 0x12;
    LODWORD(v6) = CryptoPP::member_ptr<std::basic_ofstream<char,std::char_traits<char>>>::operator->(&v23->m_file);
    std::basic_ofstream<char,std::char_traits<char>>::open(v6, v10, (unsigned int)v21, 64i64);
    LODWORD(v7) = CryptoPP::member_ptr<std::basic_ofstream<char,std::char_traits<char>>>::operator*(&v23->m_file);
    v14 = v7;
    if ( (unsigned __int8)std::ios_base::operator!(*(_DWORD *)(*(_QWORD *)v7 + 4i64) + v7) )
    {
      memset(&v17, 0, sizeof(v17));
      std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
        &v16,
        v10,
        v17);
      CryptoPP::FileSink::OpenErr::OpenErr(&v15, &v16);
      CxxThrowException_0((__int64)&v15, (__int64)&TI4_AVOpenErr_FileSink_CryptoPP__);
    }
    LODWORD(v8) = CryptoPP::member_ptr<std::basic_ofstream<char,std::char_traits<char>>>::get(&v23->m_file);
    v23->m_stream = v8;
  }
  else
  {
    v23->m_stream = 0i64;
    v22 = &v23->m_stream;
    v9 = CryptoPP::Name::OutputStreamPointer(v4);
    CryptoPP::NameValuePairs::GetValue<std::basic_ostream<char,std::char_traits<char>> *>(v24, v9, v22);
  }
}
