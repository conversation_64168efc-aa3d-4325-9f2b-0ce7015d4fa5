/*
 * Function: ??0?$_<PERSON>t@PEAVCLogTypeDBTask@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x1402C6AD0
 */

void __fastcall std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &>(std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Ranit<CLogTypeDBTask *,__int64,CLogTypeDBTask * const *,CLogTypeDBTask * const &> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Iterator_base::_Iterator_base((std::_Iterator_base *)&v4->_Mycont);
}
