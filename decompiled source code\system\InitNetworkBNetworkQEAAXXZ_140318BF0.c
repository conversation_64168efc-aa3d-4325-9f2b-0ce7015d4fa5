/*
 * Function: ?InitNetwork@BNetwork@@QEAAXXZ
 * Address: 0x140318BF0
 */

void __fastcall BNetwork::InitNetwork(BNetwork *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-148h]@1
  _NET_TYPE_PARAM v4; // [sp+30h] [bp-118h]@4
  unsigned __int64 v5; // [sp+130h] [bp-18h]@4
  BNetwork *v6; // [sp+150h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = (unsigned __int64)&v3 ^ _security_cookie;
  _NET_TYPE_PARAM::_NET_TYPE_PARAM(&v4);
  v4.m_bRealSockCheck = 0;
  v4.m_bSystemLogFile = 1;
  v4.m_bServer = 0;
  v4.m_byRecvThreadNum = 1;
  v4.m_byRecvSleepTime = 1;
  v4.m_wSocketMaxNum = 1;
  v4.m_bSvrToS = 1;
  v4.m_bOddMsgWriteLog = 1;
  v4.m_dwSendBufferSize = 1000000;
  v4.m_dwRecvBufferSize = 1000000;
  v4.m_bAnSyncConnect = 1;
  sprintf_s(v4.m_szModuleName, 0x80ui64, "BillingNetwork");
  ((void (__fastcall *)(signed __int64, _NET_TYPE_PARAM *, const char *, const char *))v6->SetNetSystem)(
    1i64,
    &v4,
    "BillingNetwork",
    "..\\ZoneServerLog\\NetLog");
}
