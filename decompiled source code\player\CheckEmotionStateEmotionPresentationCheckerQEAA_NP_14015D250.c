/*
 * Function: ?CheckEmotionState@EmotionPresentationChecker@@QEAA_NPEAVCMonster@@EPEAVCCharacter@@@Z
 * Address: 0x14015D250
 */

bool __fastcall EmotionPresentationChecker::CheckEmotionState(EmotionPresentationChecker *this, C<PERSON>onster *pThis, char byCheckType, CCharacter *pTarget)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@9
  unsigned __int16 v9; // [sp+24h] [bp-14h]@18
  unsigned __int8 v10; // [sp+28h] [bp-10h]@15
  EmotionPresentationChecker *v11; // [sp+40h] [bp+8h]@1
  CMonster *v12; // [sp+48h] [bp+10h]@1
  char v13; // [sp+50h] [bp+18h]@1
  CCharacter *v14; // [sp+58h] [bp+20h]@1

  v14 = pTarget;
  v13 = byCheckType;
  v12 = pThis;
  v11 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( !v11->m_bIsSet && pThis )
  {
    if ( pThis->m_pMonRec )
    {
      for ( j = 0; j < 5 && !v11->m_bIsSet; ++j )
      {
        if ( v12->m_pMonRec->m_EmotionChecker[j].m_nEmotionCon > 0
          && v12->m_pMonRec->m_EmotionChecker[j].m_nEmotionCon == (unsigned __int8)v13 )
        {
          v10 = v13;
          if ( v13 )
          {
            if ( v10 <= 0xAu )
            {
              v11->m_bIsSet = 1;
              v11->m_pTarget = 0i64;
              v11->m_byType = v12->m_pMonRec->m_EmotionChecker[j].m_nEmotionClass;
              if ( v11->m_byType )
              {
                v11->m_wIndex = atoi(v12->m_pMonRec->m_EmotionChecker[j].m_strEmotionCode);
              }
              else
              {
                v11->m_wIndex = atoi(v12->m_pMonRec->m_EmotionChecker[j].m_strEmotionCode);
                v9 = *(_BYTE *)(unk_1799CA300 + 4i64 * v11->m_wIndex + 2);
                if ( !v9 )
                {
                  CLogFile::Write(
                    &stru_1799C8E78,
                    "g_Main.m_tblMobMessage.GetRecord(m_wIndex(%u)->m_nUsingNum == 0 !!",
                    v11->m_wIndex);
                  v9 = 1;
                }
                v11->m_wRandIndex = GetTickCount() % v9;
              }
              if ( v13 == 10 || v13 == 7 )
                v11->m_pTarget = v14;
            }
          }
        }
      }
      result = v11->m_bIsSet;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
