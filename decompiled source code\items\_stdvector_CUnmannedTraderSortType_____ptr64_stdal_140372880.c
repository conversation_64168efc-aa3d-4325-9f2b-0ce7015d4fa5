/*
 * Function: _std::vector_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x140372880
 */

void __fastcall __noreturn std::vector_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(
    *(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > **)(a2 + 160),
    *(CUnmannedTraderSortType ***)(a2 + 64),
    *(CUnmannedTraderSortType ***)(a2 + 72));
  std::allocator<CUnmannedTraderSortType *>::deallocate(
    (std::allocator<CUnmannedTraderSortType *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(CUnmannedTraderSortType ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
