/*
 * Function: ?CalcExp@CPlayer@@QEAAXPEAVCCharacter@@HAEAVCPartyModeKillMonsterExpNotify@@@Z
 * Address: 0x14005A600
 */

void __fastcall CPlayer::CalcExp(CPlayer *this, CCharacter *pDst, int nDam, CPartyModeKillMonsterExpNotify *kPartyExpNotify)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@8
  signed int v7; // eax@16
  signed int v8; // eax@24
  signed int v9; // eax@38
  float v10; // xmm1_4@38
  float v11; // xmm0_4@38
  int v12; // eax@38
  signed int v13; // eax@41
  __int64 v14; // [sp+0h] [bp-118h]@1
  bool bUseExpAdditionItem; // [sp+20h] [bp-F8h]@40
  char *strErrorCodePos; // [sp+28h] [bp-F0h]@41
  bool bPcbangPrimiumFavorReward; // [sp+30h] [bp-E8h]@41
  bool bGetAttackExp; // [sp+44h] [bp-D4h]@8
  CMonster *v19; // [sp+58h] [bp-C0h]@9
  _base_fld *v20; // [sp+60h] [bp-B8h]@9
  int v21; // [sp+68h] [bp-B0h]@9
  int v22; // [sp+6Ch] [bp-ACh]@12
  float v23; // [sp+70h] [bp-A8h]@14
  float v24; // [sp+74h] [bp-A4h]@18
  int v25; // [sp+78h] [bp-A0h]@18
  CPlayer *out_ppMember; // [sp+90h] [bp-88h]@26
  char v27; // [sp+D4h] [bp-44h]@26
  int iMaxLevel; // [sp+E8h] [bp-30h]@28
  int i2ndLevel; // [sp+ECh] [bp-2Ch]@28
  int v30; // [sp+F4h] [bp-24h]@28
  float v31; // [sp+F8h] [bp-20h]@28
  int j; // [sp+FCh] [bp-1Ch]@28
  float v33; // [sp+100h] [bp-18h]@38
  float v34; // [sp+104h] [bp-14h]@16
  float v35; // [sp+108h] [bp-10h]@24
  float v36; // [sp+10Ch] [bp-Ch]@41
  CPlayer *v37; // [sp+120h] [bp+8h]@1
  CMonster *v38; // [sp+128h] [bp+10h]@1
  int v39; // [sp+130h] [bp+18h]@1
  CPartyModeKillMonsterExpNotify *v40; // [sp+138h] [bp+20h]@1

  v40 = kPartyExpNotify;
  v39 = nDam;
  v38 = (CMonster *)pDst;
  v37 = this;
  v4 = &v14;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pDst->m_ObjID.m_byID == 1
    && nDam > 0
    && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v37->vfptr->IsInTown)(v37) )
  {
    bGetAttackExp = 1;
    v6 = ((int (__fastcall *)(CMonster *))v38->vfptr->GetLevel)(v38);
    if ( CPlayer::IsPassExpLimitLvDiff(v37, v6, &bGetAttackExp) )
    {
      v19 = v38;
      v20 = v38->m_pRecordSet;
      v21 = ((int (__fastcall *)(CMonster *))v38->vfptr->GetHP)(v38) - v39;
      if ( CMonster::IsBossMonster(v19) )
        bGetAttackExp = 0;
      if ( bGetAttackExp )
      {
        v22 = v39;
        if ( v21 < 0 )
        {
          v21 = 0;
          v22 = ((int (__fastcall *)(CMonster *))v38->vfptr->GetHP)(v38);
        }
        v23 = (float)(*(float *)&v20[4].m_strCode[16] * 0.69999999)
            * (float)((float)v22 / *(float *)&v20[25].m_strCode[4]);
        if ( CPlayer::IsRidingUnit(v37) )
        {
          v34 = v23 / 180.0;
          v7 = ((int (__fastcall *)(CMonster *))v19->vfptr->GetLevel)(v19);
          CPlayer::Emb_AlterStat(v37, 6, 0, (signed int)ffloor(v34 + (float)v7), 0, "CPlayer::CalcExp()--0", 1);
        }
        else
        {
          CPlayer::AlterExp(v37, v23, 0, 0, 0);
        }
      }
      if ( !v21 )
      {
        v24 = 0.0;
        v25 = (unsigned __int8)CMonster::GetEmotionState(v19);
        if ( v25 == 4 )
          v24 = *(float *)&v20[4].m_strCode[16] * 0.5;
        else
          v24 = *(float *)&v20[4].m_strCode[16] * 0.30000001;
        if ( CPartyPlayer::IsPartyMode(v37->m_pPartyMgr) )
        {
          v27 = CPlayer::_GetPartyMemberInCircle(v37, &out_ppMember, 8, 1);
          if ( (signed int)(unsigned __int8)v27 > 0 )
            v24 = v24 * CPlayer::s_fExpDivUnderParty_Kill[(unsigned __int8)v27 - 1];
          iMaxLevel = 0;
          i2ndLevel = 0;
          v30 = 0;
          v31 = 0.0;
          for ( j = 0; j < (unsigned __int8)v27; ++j )
          {
            v30 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetLevel)(*(&out_ppMember + j));
            v31 = v31 + (float)v30;
            if ( v30 <= iMaxLevel )
            {
              if ( v30 > i2ndLevel )
                i2ndLevel = v30;
            }
            else
            {
              i2ndLevel = iMaxLevel;
              iMaxLevel = v30;
            }
          }
          if ( v31 > 1.0 )
          {
            CPartyModeKillMonsterExpNotify::SetKillMonsterFlag(v40);
            for ( j = 0; j < (unsigned __int8)v27; ++j )
            {
              v9 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetLevel)(*(&out_ppMember + j));
              v10 = v24 * (float)v9;
              v11 = v10 / v31;
              v33 = v10 / v31;
              v12 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetLevel)(*(&out_ppMember + j));
              CPlayer::GetPartyExpDistributionRate(v37, v12, iMaxLevel, i2ndLevel);
              v33 = v33 + (float)(v11 * v33);
              if ( v33 >= 1.0 )
              {
                if ( CPlayer::IsRidingUnit(*(&out_ppMember + j)) )
                {
                  v36 = v33 / 180.0;
                  v13 = ((int (__fastcall *)(CMonster *))v19->vfptr->GetLevel)(v19);
                  bPcbangPrimiumFavorReward = 1;
                  strErrorCodePos = "CPlayer::CalcExp()--2";
                  bUseExpAdditionItem = 0;
                  CPlayer::Emb_AlterStat(
                    *(&out_ppMember + j),
                    (unsigned __int64)"astery(%d, %d) == false",
                    0,
                    (signed int)ffloor(v36 + (float)v13),
                    0,
                    "CPlayer::CalcExp()--2",
                    1);
                }
                else
                {
                  bUseExpAdditionItem = 0;
                  CPlayer::AlterExp(*(&out_ppMember + j), v33, 0, 0, 0);
                  CPartyModeKillMonsterExpNotify::Add(v40, *(&out_ppMember + j), v33);
                }
              }
            }
          }
        }
        else if ( CPlayer::IsRidingUnit(v37) )
        {
          v35 = v24 / 180.0;
          v8 = ((int (__fastcall *)(_QWORD))v19->vfptr->GetLevel)(v19);
          CPlayer::Emb_AlterStat(v37, 6, 0, (signed int)ffloor(v35 + (float)v8), 0, "CPlayer::CalcExp()--1", 1);
        }
        else
        {
          CPlayer::AlterExp(v37, v24, 0, 0, 0);
        }
      }
    }
  }
}
