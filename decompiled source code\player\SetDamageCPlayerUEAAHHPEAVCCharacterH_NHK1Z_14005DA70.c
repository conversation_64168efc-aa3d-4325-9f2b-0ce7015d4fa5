/*
 * Function: ?SetDamage@CPlayer@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x14005DA70
 */

int __usercall CPlayer::SetDamage@<eax>(CPlayer *this@<rcx>, int nDamage@<edx>, CCharacter *pDst@<r8>, int nDstLv@<r9d>, float a5@<xmm0>, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  int v12; // eax@13
  int v13; // eax@18
  int v14; // eax@24
  int v15; // eax@28
  int v16; // eax@31
  int v17; // eax@33
  int v18; // eax@38
  int v19; // eax@42
  int v20; // eax@43
  int v21; // eax@45
  int v22; // eax@51
  int v23; // eax@52
  int v24; // eax@52
  int v25; // eax@53
  int v26; // eax@54
  signed int v27; // eax@58
  signed int v28; // eax@60
  int v29; // eax@63
  char *v30; // rax@81
  signed int v31; // eax@82
  int v32; // eax@82
  __int64 v33; // rax@95
  signed int v34; // eax@119
  int v35; // eax@121
  __int64 v36; // [sp+0h] [bp-1C8h]@1
  char byReason[8]; // [sp+20h] [bp-1A8h]@13
  char *strErrorCodePos; // [sp+28h] [bp-1A0h]@13
  bool bPcbangPrimiumFavorReward[4]; // [sp+30h] [bp-198h]@13
  char v40; // [sp+38h] [bp-190h]@13
  int v41; // [sp+40h] [bp-188h]@13
  int v42; // [sp+44h] [bp-184h]@24
  int v43; // [sp+48h] [bp-180h]@28
  int v44; // [sp+4Ch] [bp-17Ch]@28
  CPlayer *v45; // [sp+50h] [bp-178h]@28
  int v46; // [sp+58h] [bp-170h]@33
  int v47; // [sp+5Ch] [bp-16Ch]@38
  int v48; // [sp+60h] [bp-168h]@42
  int v49; // [sp+64h] [bp-164h]@42
  int v50; // [sp+68h] [bp-160h]@45
  int v51; // [sp+6Ch] [bp-15Ch]@51
  int v52; // [sp+70h] [bp-158h]@52
  int v53; // [sp+74h] [bp-154h]@61
  CPlayer *v54; // [sp+78h] [bp-150h]@64
  int v55; // [sp+80h] [bp-148h]@71
  int v56; // [sp+84h] [bp-144h]@74
  CPlayer *out_ppMember; // [sp+A0h] [bp-128h]@74
  char v58; // [sp+E4h] [bp-E4h]@74
  int j; // [sp+E8h] [bp-E0h]@74
  float v60; // [sp+ECh] [bp-DCh]@82
  float v61; // [sp+F0h] [bp-D8h]@82
  unsigned int v62; // [sp+F4h] [bp-D4h]@82
  int v63; // [sp+F8h] [bp-D0h]@88
  unsigned int pdwAfterCum; // [sp+104h] [bp-C4h]@89
  bool v65; // [sp+114h] [bp-B4h]@89
  int v66; // [sp+118h] [bp-B0h]@111
  CPlayer *v67; // [sp+120h] [bp-A8h]@122
  unsigned int dwAlter; // [sp+128h] [bp-A0h]@124
  unsigned int v69; // [sp+12Ch] [bp-9Ch]@126
  float v70; // [sp+130h] [bp-98h]@13
  CGameObjectVtbl *v71; // [sp+138h] [bp-90h]@13
  CGameObjectVtbl *v72; // [sp+140h] [bp-88h]@18
  int v73; // [sp+148h] [bp-80h]@24
  int v74; // [sp+14Ch] [bp-7Ch]@25
  float v75; // [sp+150h] [bp-78h]@28
  int v76; // [sp+154h] [bp-74h]@28
  float v77; // [sp+158h] [bp-70h]@31
  int v78; // [sp+15Ch] [bp-6Ch]@31
  int v79; // [sp+160h] [bp-68h]@38
  int v80; // [sp+164h] [bp-64h]@39
  float v81; // [sp+168h] [bp-60h]@42
  int v82; // [sp+16Ch] [bp-5Ch]@42
  float v83; // [sp+170h] [bp-58h]@43
  int v84; // [sp+174h] [bp-54h]@43
  CGameObjectVtbl *v85; // [sp+178h] [bp-50h]@52
  CGameObjectVtbl *v86; // [sp+180h] [bp-48h]@54
  float v87; // [sp+188h] [bp-40h]@58
  int v88; // [sp+18Ch] [bp-3Ch]@63
  CGameObjectVtbl *v89; // [sp+190h] [bp-38h]@63
  int v90; // [sp+198h] [bp-30h]@72
  int v91; // [sp+19Ch] [bp-2Ch]@82
  CGameObjectVtbl *v92; // [sp+1A0h] [bp-28h]@82
  unsigned int v93; // [sp+1A8h] [bp-20h]@82
  int v94; // [sp+1ACh] [bp-1Ch]@121
  CGameObjectVtbl *v95; // [sp+1B0h] [bp-18h]@121
  CPlayer *v96; // [sp+1D0h] [bp+8h]@1
  int v97; // [sp+1D8h] [bp+10h]@1
  CPlayer *pAtter; // [sp+1E0h] [bp+18h]@1
  int iDstLevel; // [sp+1E8h] [bp+20h]@1

  iDstLevel = nDstLv;
  pAtter = (CPlayer *)pDst;
  v97 = nDamage;
  v96 = this;
  v9 = &v36;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  if ( v96->m_bCorpse )
  {
    result = CPlayerDB::GetHP(&v96->m_Param);
  }
  else if ( v96->m_bMapLoading || CGameObject::GetCurSecNum((CGameObject *)&v96->vfptr) == -1 )
  {
    result = CPlayerDB::GetHP(&v96->m_Param);
  }
  else
  {
    if ( pAtter )
    {
      _effect_parameter::GetEff_Have(&v96->m_EP, 54);
      if ( a5 > 0.0 && bJadeReturn && !pAtter->m_ObjID.m_byID )
      {
        v70 = (float)v97;
        _effect_parameter::GetEff_Have(&v96->m_EP, 54);
        a5 = v70 * (float)v97;
        v41 = (signed int)ffloor(a5);
        v12 = ((int (__fastcall *)(CPlayer *))v96->vfptr->GetLevel)(v96);
        v71 = pAtter->vfptr;
        v40 = 0;
        *(_DWORD *)bPcbangPrimiumFavorReward = 0;
        LODWORD(strErrorCodePos) = -1;
        byReason[0] = 1;
        ((void (__fastcall *)(CPlayer *, _QWORD, CPlayer *, _QWORD))v71->SetDamage)(
          pAtter,
          (unsigned int)v41,
          v96,
          (unsigned int)v12);
      }
    }
    if ( pAtter && (_effect_parameter::GetEff_Have(&v96->m_EP, 42), a5 != 0.0) && bJadeReturn && !pAtter->m_ObjID.m_byID )
    {
      v13 = ((int (__fastcall *)(CPlayer *))v96->vfptr->GetLevel)(v96);
      v72 = pAtter->vfptr;
      v40 = 0;
      *(_DWORD *)bPcbangPrimiumFavorReward = 0;
      LODWORD(strErrorCodePos) = -1;
      byReason[0] = 1;
      ((void (__fastcall *)(CPlayer *, _QWORD, CPlayer *, _QWORD))v72->SetDamage)(
        pAtter,
        (unsigned int)v97,
        v96,
        (unsigned int)v13);
      result = CPlayerDB::GetHP(&v96->m_Param);
    }
    else
    {
      CCharacter::BreakStealth((CCharacter *)&v96->vfptr);
      CPlayer::BreakCloakBooster(v96);
      if ( _effect_parameter::GetEff_State(&v96->m_EP, 14) )
        CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v96->vfptr, 2, 14);
      if ( pAtter && !CPlayer::IsRidingUnit(v96) && v97 > 0 )
      {
        v73 = ((int (__fastcall *)(CPlayer *))pAtter->vfptr->GetAttackLevel)(pAtter);
        v14 = CPlayer::GetDamageLevel(v96, v96->m_nLastBeatenPart);
        v42 = (signed int)ffloor((float)((float)(v73 - v14 + 23) / 33.0) + 1.0);
        if ( v42 <= 0 )
          v74 = 0;
        else
          v74 = v42;
        v42 = v74;
        if ( pAtter->m_ObjID.m_byID )
        {
          v44 = s_nMonDefPoint;
          v77 = (float)s_nMonDefPoint;
          v78 = ((int (__fastcall *)(CPlayer *))pAtter->vfptr->GetAttackDP)(pAtter);
          v16 = CPlayer::GetDamageDP(v96, v96->m_nLastBeatenPart);
          v43 = (signed int)ffloor((float)(v77 + (float)((float)(v16 * v78) / 2.0)) * (float)v42);
        }
        else
        {
          v44 = s_nStdDefPoint;
          v75 = (float)s_nStdDefPoint;
          v76 = ((int (__fastcall *)(CPlayer *))pAtter->vfptr->GetAttackDP)(pAtter);
          v15 = CPlayer::GetDamageDP(v96, v96->m_nLastBeatenPart);
          v43 = (signed int)floor((float)((float)(v75 + (float)((float)(v15 * v76) / 2.0)) * (float)v42) * 1.25);
          v45 = pAtter;
          if ( !CPlayer::IsChaosMode(pAtter) )
          {
            CPvpOrderView::Notify_OrderView(&v96->m_kPvpOrderView, v96->m_ObjID.m_wIndex);
            CPvpOrderView::SetOrderViewDamagedState(&v96->m_kPvpOrderView);
            CPvpOrderView::Notify_OrderView(&v45->m_kPvpOrderView, v45->m_ObjID.m_wIndex);
            CPvpOrderView::SetOrderViewAttackState(&v45->m_kPvpOrderView);
          }
        }
        if ( v43 > 0 )
        {
          v46 = CPlayer::GetDP(v96);
          CPlayer::SetDP(v96, v46 - v43, 0);
          v17 = CPlayer::GetDP(v96);
          if ( v46 != v17 )
            CPlayer::SendMsg_SetDPInform(v96);
        }
      }
      if ( pAtter && !CPlayer::IsRidingUnit(v96) && v97 <= 0 )
      {
        v79 = ((int (__fastcall *)(CPlayer *))pAtter->vfptr->GetAttackLevel)(pAtter);
        v18 = CPlayer::GetDamageLevel(v96, v96->m_nLastBeatenPart);
        v47 = (signed int)ffloor((float)((float)(v79 - v18 + 23) / 33.0) + 1.0);
        v80 = v47 <= 0 ? 0 : v47;
        v47 = v80;
        if ( pAtter->m_ObjID.m_byID )
        {
          v49 = s_nMonDefPoint;
          v83 = (float)s_nMonDefPoint;
          v84 = ((int (__fastcall *)(CPlayer *))pAtter->vfptr->GetAttackDP)(pAtter);
          v20 = CPlayer::GetDamageDP(v96, v96->m_nLastBeatenPart);
          v48 = (signed int)ffloor((float)((float)(v83 + (float)((float)(v20 * v84) / 2.0)) * (float)v47) / 3.0);
        }
        else
        {
          v49 = s_nStdDefPoint;
          v81 = (float)s_nStdDefPoint;
          v82 = ((int (__fastcall *)(_QWORD))pAtter->vfptr->GetAttackDP)(pAtter);
          v19 = CPlayer::GetDamageDP(v96, v96->m_nLastBeatenPart);
          v48 = (signed int)floor((float)((float)(v81 + (float)((float)(v19 * v82) / 2.0)) * (float)v47) * 1.25 / 3.0);
        }
        if ( v48 > 0 )
        {
          v50 = CPlayer::GetDP(v96);
          CPlayer::SetDP(v96, v50 - v48, 0);
          v21 = CPlayer::GetDP(v96);
          if ( v50 != v21 )
            CPlayer::SendMsg_SetDPInform(v96);
        }
      }
      if ( v97 <= 0 )
      {
        if ( v97 == -1 )
        {
          if ( pAtter )
            v66 = CCharacter::GetAttackRandomPart((CCharacter *)&pAtter->vfptr);
          else
            v66 = CCharacter::GetAttackRandomPart((CCharacter *)&v96->vfptr);
          CPlayer::pc_PlayAttack_Gen(v96, (CCharacter *)&pAtter->vfptr, v66, 0xFFFFu, 0xFFFFu, 1);
        }
        else if ( v97 == -2 && v96->m_nLastBeatenPart == 5 )
        {
          if ( pAtter )
          {
            if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v96->vfptr->IsInTown)(v96)
              && CPlayer::IsPassMasteryLimitLvDiff(v96, iDstLevel) )
            {
              v34 = v96->m_byDefMatCount++;
              if ( v34 < 2 )
              {
                if ( pAtter->m_ObjID.m_byID
                  || (v94 = ((int (__fastcall *)(CPlayer *))v96->vfptr->GetObjRace)(v96),
                      v95 = pAtter->vfptr,
                      v35 = ((int (__fastcall *)(CPlayer *))v95->GetObjRace)(pAtter),
                      v94 != v35) )
                {
                  v69 = CPlayer::GetMasteryCumAfterAttack(v96, iDstLevel);
                  CPlayer::Emb_AlterStat(v96, 2, 0, v69, 0, "CPlayer::SetDamage()---3", 1);
                }
                else
                {
                  v67 = pAtter;
                  if ( !CPlayer::IsChaosMode(pAtter) && !CPlayer::IsPunished(v96, 1, 0) )
                  {
                    dwAlter = CPlayer::GetMasteryCumAfterAttack(v96, iDstLevel);
                    CPlayer::Emb_AlterStat(v96, 2, 0, dwAlter, 0, "CPlayer::SetDamage()---2", 1);
                  }
                }
              }
            }
          }
        }
      }
      else
      {
        if ( CPlayer::IsRidingUnit(v96) )
        {
          if ( !v96->m_bNeverDie )
          {
            if ( v96->m_pUsingUnit->dwGauge <= v97 )
              v96->m_pUsingUnit->dwGauge = 0;
            else
              v96->m_pUsingUnit->dwGauge -= v97;
          }
          if ( v96->m_pUsingUnit->dwGauge )
          {
            CPlayer::SendMsg_AlterUnitHPInform(v96, v96->m_pUsingUnit->bySlotIndex, v96->m_pUsingUnit->dwGauge);
          }
          else
          {
            ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v96->vfptr->SetHP)(v96, 0i64, 0i64);
            (*(void (__fastcall **)(CPlayer *))&v96->vfptr->gap8[72])(v96);
            CPlayer::Emb_RidindUnit(v96, 0, 0i64);
          }
        }
        else
        {
          if ( CPlayerDB::GetFP(&v96->m_Param) > 0 && _effect_parameter::GetEff_State(&v96->m_EP, 22) )
          {
            v51 = 2 * v97;
            v22 = CPlayerDB::GetFP(&v96->m_Param);
            if ( v51 > v22 )
            {
              v23 = CPlayerDB::GetFP(&v96->m_Param);
              v52 = (v51 - v23) / 2;
              v24 = CPlayerDB::GetHP(&v96->m_Param);
              v85 = v96->vfptr;
              ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v85->SetHP)(v96, (unsigned int)(v24 - v52), 0i64);
              v51 = CPlayerDB::GetFP(&v96->m_Param);
            }
            v25 = CPlayerDB::GetFP(&v96->m_Param);
            CPlayer::SetFP(v96, v25 - v51, 0);
            CPlayer::SendMsg_SetFPInform(v96);
          }
          else
          {
            v26 = CPlayerDB::GetHP(&v96->m_Param);
            v86 = v96->vfptr;
            ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v86->SetHP)(v96, (unsigned int)(v26 - v97), 0i64);
            (*(void (__fastcall **)(CPlayer *))&v96->vfptr->gap8[72])(v96);
          }
          if ( pAtter )
          {
            if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v96->vfptr->IsInTown)(v96)
              && CPlayer::IsPassMasteryLimitLvDiff(v96, iDstLevel) )
            {
              v87 = (float)v97;
              v27 = ((int (__fastcall *)(CPlayer *))v96->vfptr->GetMaxHP)(v96);
              if ( (float)(v87 / (float)v27) >= 0.02 && ((int (__fastcall *)(CPlayer *))v96->vfptr->GetHP)(v96) > 0 )
              {
                v28 = v96->m_byDefMatCount++;
                if ( v28 < 2 )
                {
                  v53 = CPlayer::GetMasteryCumAfterAttack(v96, iDstLevel);
                  if ( v96->m_nLastBeatenPart != 5 )
                  {
                    if ( pAtter->m_ObjID.m_byID
                      || (v88 = ((int (__fastcall *)(CPlayer *))v96->vfptr->GetObjRace)(v96),
                          v89 = pAtter->vfptr,
                          v29 = ((int (__fastcall *)(CPlayer *))v89->GetObjRace)(pAtter),
                          v88 != v29) )
                    {
                      CPlayer::Emb_AlterStat(
                        v96,
                        1,
                        0,
                        v96->m_nAddDfnMstByClass * v53,
                        0,
                        "CPlayer::SetDamage()---1",
                        1);
                    }
                    else
                    {
                      v54 = pAtter;
                      if ( !CPlayer::IsChaosMode(pAtter) && !CPlayer::IsPunished(v96, 1, 0) )
                        CPlayer::Emb_AlterStat(
                          v96,
                          1,
                          0,
                          v96->m_nAddDfnMstByClass * v53,
                          0,
                          "CPlayer::SetDamage()---0",
                          1);
                    }
                    if ( v96->m_pPartyMgr && CPartyPlayer::IsPartyMode(v96->m_pPartyMgr) )
                    {
                      *(float *)&v55 = (float)(v96->m_nAddDfnMstByClass * v53) * 0.1;
                      if ( *(float *)&v55 >= 0.0 )
                        v90 = v55;
                      else
                        v90 = 0;
                      v55 = v90;
                      v56 = 0;
                      v58 = CPlayer::_GetPartyMemberInCircle(v96, &out_ppMember, 8, 1);
                      for ( j = 0; j < (unsigned __int8)v58; ++j )
                      {
                        if ( !(*(&out_ppMember + j))->m_bCorpse && *(&out_ppMember + j) != v96 )
                        {
                          if ( _STAT_DB_BASE::IsRangePerMastery(1, 0) )
                          {
                            v91 = v96->m_nAddDfnMstByClass * v53;
                            v92 = v96->vfptr;
                            v31 = ((int (__fastcall *)(CPlayer *))v92->GetLevel)(v96);
                            v60 = (float)((v31 / 10 + 1) * v91);
                            v61 = v60 * 0.1;
                            v93 = CPlayer::_check_mastery_cum_lim(*(&out_ppMember + j), 1, 0);
                            v32 = _MASTERY_PARAM::GetCumPerMast(&(*(&out_ppMember + j))->m_pmMst, 1, 0);
                            v62 = v93 - v32;
                            if ( ((v93 - v32) & 0x80000000) != 0 )
                              v62 = 0;
                            if ( v61 > (float)(signed int)v62 )
                              v61 = (float)(signed int)v62;
                            if ( v61 > 0.0 )
                            {
                              v61 = v61 + 0.5;
                              if ( v61 >= 1.0 )
                              {
                                v63 = _STAT_DB_BASE::GetStatIndex(1, 0);
                                if ( (*(&out_ppMember + j))->m_pmMst.m_BaseCum.m_dwDamWpCnt[v63] <= 0xEE6B2800 )
                                {
                                  pdwAfterCum = 0;
                                  v65 = _MASTERY_PARAM::AlterCumPerMast(
                                          &(*(&out_ppMember + j))->m_pmMst,
                                          1,
                                          0,
                                          (signed int)ffloor(v61),
                                          &pdwAfterCum);
                                  if ( (*(&out_ppMember + j))->m_pmMst.m_bUpdateEquipMast )
                                    (*(&out_ppMember + j))->m_bUpCheckEquipEffect = 1;
                                  CPlayer::SendMsg_StatInform(*(&out_ppMember + j), v63, pdwAfterCum, 0);
                                  if ( (*(&out_ppMember + j))->m_pmMst.m_MastUpData.bUpdate )
                                    CPlayer::ReCalcMaxHFSP(*(&out_ppMember + j), 1, 0);
                                  if ( (*(&out_ppMember + j))->m_pUserDB )
                                    CUserDB::Update_Stat((*(&out_ppMember + j))->m_pUserDB, v63, pdwAfterCum, v65);
                                  (*(&out_ppMember + j))->m_Param.m_dwAlterMastery[v63] += (signed int)ffloor(v61);
                                  v33 = (*(&out_ppMember + j))->m_pmMst.m_MastUpData.bUpdate;
                                }
                              }
                            }
                          }
                          else
                          {
                            v30 = CPlayerDB::GetCharNameA(&v96->m_Param);
                            CLogFile::Write(&stru_1799C8E78, "%s: PartyPlayer Div Defence Error", v30);
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        if ( !((int (__fastcall *)(CPlayer *))v96->vfptr->GetHP)(v96) )
        {
          if ( pAtter )
            ((void (__fastcall *)(CPlayer *, CPlayer *))pAtter->vfptr->RecvKillMessage)(pAtter, v96);
          CPlayer::Corpse(v96, (CCharacter *)&pAtter->vfptr);
        }
      }
      if ( pAtter && v96->m_pRecalledAnimusChar && !CCharacter::GetStealth((CCharacter *)&pAtter->vfptr, 1) )
        CAnimus::MasterBeAttacked_MasterInform(v96->m_pRecalledAnimusChar, (CCharacter *)&pAtter->vfptr);
      v96->m_nLastBeatenPart = 0;
      CPlayer::SetBattleMode(v96, 0);
      result = CPlayerDB::GetHP(&v96->m_Param);
    }
  }
  return result;
}
