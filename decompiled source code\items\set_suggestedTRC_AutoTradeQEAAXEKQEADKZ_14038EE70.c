/*
 * Function: ?set_suggested@TRC_AutoTrade@@QEAAXEKQEADK@Z
 * Address: 0x14038EE70
 */

void __fastcall TRC_AutoTrade::set_suggested(TRC_AutoTrade *this, char byMatterType, unsigned int dwMatterDst, char *wszMatterDst, unsigned int dwNext)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1
  unsigned int v10; // [sp+40h] [bp+18h]@1
  const char *Source; // [sp+48h] [bp+20h]@1

  Source = wszMatterDst;
  v10 = dwMatterDst;
  v9 = byMatterType;
  v8 = this;
  v5 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  _suggested_matter_change_taxrate::init(&v8->m_suggested);
  v8->m_suggested.byMatterType = v9;
  v8->m_suggested.dwMatterDst = v10;
  strcpy_0(v8->m_suggested.wszMatterDst, Source);
  if ( dwNext < 5 || dwNext > 0x14 )
    dwNext = 5;
  v8->m_suggested.dwNext = dwNext;
  v8->m_bChangeTaxRate = 1;
  v8->m_suggested.dwSuggestedTime = GetKorLocalTime();
  CLogFile::Write(&v8->m_serviceLog, "[Suggest Change Tax Rate]:[SUBPATRIARCH:%s] - %d(%%)", Source, dwNext);
  TRC_AutoTrade::PushDQSData(v8);
}
