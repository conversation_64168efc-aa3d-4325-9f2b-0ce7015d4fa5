/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo______::_1_::catch$1
 * Address: 0x14037B000
 */

void __fastcall __noreturn std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_std::_Vector_const_iterator_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo______::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 736),
    (CUnmannedTraderItemCodeInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 744) + 16i64) + 72i64 * *(_QWORD *)(a2 + 40)),
    (CUnmannedTraderItemCodeInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 736) + 24i64) + 72i64 * *(_QWORD *)(a2 + 40)));
  CxxThrowException_0(0i64, 0i64);
}
