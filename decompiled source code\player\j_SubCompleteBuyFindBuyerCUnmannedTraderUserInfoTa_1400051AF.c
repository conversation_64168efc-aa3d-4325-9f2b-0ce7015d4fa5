/*
 * Function: j_?SubCompleteBuyFindBuyer@CUnmannedTraderUserInfoTable@@AEAA_NPEAU_qry_case_unmandtrader_buy_update_wait@@PEAPEAVCUnmannedTraderUserInfo@@PEAPEAVCPlayer@@@Z
 * Address: 0x1400051AF
 */

bool __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyFindBuyer(CUnmannedTraderUserInfoTable *this, _qry_case_unmandtrader_buy_update_wait *pkQuery, CUnmannedTraderUserInfo **ppkBuyUser, CPlayer **ppkBuyPlayer)
{
  return CUnmannedTraderUserInfoTable::SubCompleteBuyFindBuyer(this, pkQuery, ppkBuyUser, ppkBuyPlayer);
}
