/*
 * Function: ?GetOreIndexFromRate@COreCuttingTable@@QEAAKKK@Z
 * Address: 0x1400D3F50
 */

signed __int64 __fastcall COreCuttingTable::GetOreIndexFromRate(COreCuttingTable *this, unsigned int dwOreIndex, unsigned int dwRate)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  unsigned int j; // [sp+0h] [bp-18h]@1
  COreCuttingTable *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < v7->pOreList[dwOreIndex].nResNum; ++j )
  {
    if ( dwRate < v7->pOreList[dwOreIndex].ResList[j].dwCumRate )
      return v7->pOreList[dwOreIndex].ResList[j].wResIndex;
  }
  if ( v7->pOreList[dwOreIndex].nResNum )
    result = v7->pOreList[dwOreIndex].ResList[v7->pOreList[dwOreIndex].nResNum - 1].wResIndex;
  else
    result = 0xFFFFFFFFi64;
  return result;
}
