/*
 * Function: ?DXUtil_Timer@@YAMW4TIMER_COMMAND@@@Z
 * Address: 0x140436AC0
 */

float __usercall DXUtil_Timer@<xmm0>(TIMER_COMMAND command@<ecx>, __m128i a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int128 v4; // tt@24
  DWORD v5; // eax@32
  float result; // xmm0_4@48
  __int64 v7; // [sp+0h] [bp-98h]@1
  __int128 v8; // [sp+20h] [bp-78h]@48
  LARGE_INTEGER Frequency; // [sp+38h] [bp-60h]@5
  double v10; // [sp+48h] [bp-50h]@26
  double v11; // [sp+50h] [bp-48h]@14
  LARGE_INTEGER PerformanceCount; // [sp+68h] [bp-30h]@11
  double v13; // [sp+78h] [bp-20h]@16
  double v14; // [sp+80h] [bp-18h]@31
  double v15; // [sp+88h] [bp-10h]@34
  TIMER_COMMAND v16; // [sp+A0h] [bp+8h]@1

  v16 = command;
  v2 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !m_bTimerInitialized )
  {
    m_bTimerInitialized = 1;
    m_bUsingQPF = QueryPerformanceFrequency(&Frequency);
    if ( m_bUsingQPF )
      m_llQPFTicksPerSec = Frequency.QuadPart;
  }
  if ( m_bUsingQPF )
  {
    if ( m_llStopTime && v16 != 1 && v16 != 4 )
      PerformanceCount.QuadPart = m_llStopTime;
    else
      QueryPerformanceCounter(&PerformanceCount);
    if ( v16 == 6 )
    {
      *(double *)&a2.m128i_i64[0] = (double)(PerformanceCount.LowPart - (signed int)m_llLastElapsedTime)
                                  / (double)(signed int)m_llQPFTicksPerSec;
      v11 = (double)(PerformanceCount.LowPart - (signed int)m_llLastElapsedTime)
          / (double)(signed int)m_llQPFTicksPerSec;
      m_llLastElapsedTime = PerformanceCount.QuadPart;
      *(float *)&a2.m128i_i32[0] = v11;
    }
    else if ( v16 == 5 )
    {
      *(double *)&a2.m128i_i64[0] = (double)(PerformanceCount.LowPart - (signed int)m_llBaseTime)
                                  / (double)(signed int)m_llQPFTicksPerSec;
      v13 = (double)(PerformanceCount.LowPart - (signed int)m_llBaseTime) / (double)(signed int)m_llQPFTicksPerSec;
      *(float *)&a2.m128i_i32[0] = v13;
    }
    else if ( v16 )
    {
      switch ( v16 )
      {
        case 1:
          m_llBaseTime += PerformanceCount.QuadPart - m_llStopTime;
          m_llStopTime = 0i64;
          m_llLastElapsedTime = PerformanceCount.QuadPart;
          a2 = 0i64;
          break;
        case 2:
          m_llStopTime = PerformanceCount.QuadPart;
          m_llLastElapsedTime = PerformanceCount.QuadPart;
          a2 = 0i64;
          break;
        case 3:
          *(_QWORD *)&v4 = m_llQPFTicksPerSec;
          *((_QWORD *)&v4 + 1) = (unsigned __int128)m_llQPFTicksPerSec >> 64;
          m_llStopTime += v4 / 10;
          a2 = 0i64;
          break;
        case 4:
          *(double *)&a2.m128i_i64[0] = (double)(signed int)PerformanceCount.LowPart
                                      / (double)(signed int)m_llQPFTicksPerSec;
          v10 = (double)(signed int)PerformanceCount.LowPart / (double)(signed int)m_llQPFTicksPerSec;
          *(float *)&a2.m128i_i32[0] = v10;
          break;
        default:
          a2 = (__m128i)LODWORD(FLOAT_N1_0);
          break;
      }
    }
    else
    {
      m_llBaseTime = PerformanceCount.QuadPart;
      m_llLastElapsedTime = PerformanceCount.QuadPart;
      a2 = 0i64;
    }
  }
  else
  {
    a2 = (__m128i)*(unsigned __int64 *)&m_fStopTime;
    if ( m_fStopTime == 0.0 || v16 == 1 || v16 == 4 )
    {
      v5 = timeGetTime();
      *(double *)&a2.m128i_i64[0] = (double)(signed int)v5 * 0.001;
      v14 = (double)(signed int)v5 * 0.001;
    }
    else
    {
      a2 = (__m128i)*(unsigned __int64 *)&m_fStopTime;
      v14 = m_fStopTime;
    }
    if ( v16 == 6 )
    {
      v15 = v14 - m_fLastElapsedTime;
      a2 = (__m128i)*(unsigned __int64 *)&v14;
      m_fLastElapsedTime = v14;
      *(float *)&a2.m128i_i32[0] = v15;
    }
    else if ( v16 == 5 )
    {
      a2 = (__m128i)*(unsigned __int64 *)&v14;
      *(double *)&a2.m128i_i64[0] = v14 - m_fBaseTime;
      *(float *)&a2.m128i_i32[0] = v14 - m_fBaseTime;
    }
    else if ( v16 )
    {
      switch ( v16 )
      {
        case 1:
          m_fBaseTime = m_fBaseTime + v14 - m_fStopTime;
          *(_QWORD *)&m_fStopTime = 0i64;
          m_fLastElapsedTime = v14;
          a2 = 0i64;
          break;
        case 2:
          m_fStopTime = v14;
          a2 = 0i64;
          break;
        case 3:
          m_fStopTime = m_fStopTime + 0.1000000014901161;
          a2 = 0i64;
          break;
        case 4:
          *(float *)&a2.m128i_i32[0] = v14;
          break;
        default:
          a2 = (__m128i)LODWORD(FLOAT_N1_0);
          break;
      }
    }
    else
    {
      m_fBaseTime = v14;
      m_fLastElapsedTime = v14;
      a2 = 0i64;
    }
  }
  _mm_storeu_si128((__m128i *)&v8, a2);
  LODWORD(result) = (unsigned __int128)_mm_loadu_si128((const __m128i *)&v8);
  return result;
}
