/*
 * Function: ?pc_BuddyDelRequest@CPlayer@@QEAAXK@Z
 * Address: 0x14008FBC0
 */

void __fastcall CPlayer::pc_BuddyDelRequest(CPlayer *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  CPlayer *ppPoper; // [sp+28h] [bp-20h]@4
  char v6; // [sp+34h] [bp-14h]@4
  int v7; // [sp+38h] [bp-10h]@4
  CPlayer *v8; // [sp+50h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+58h] [bp+10h]@1

  dwSeriala = dwSerial;
  v8 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ppPoper = 0i64;
  v6 = 6;
  v7 = _BUDDY_LIST::PopBuddy(&v8->m_pmBuddy, dwSerial, &ppPoper);
  if ( v7 != -1 )
  {
    if ( ppPoper )
      CPlayer::SendMsg_BuddyLogoffInform(ppPoper, v8->m_dwObjSerial);
    CUserDB::Update_DelBuddy(v8->m_pUserDB, v7);
    v6 = 0;
  }
  CPlayer::SendMsg_BuddyDelResult(v8, v6, dwSeriala);
}
