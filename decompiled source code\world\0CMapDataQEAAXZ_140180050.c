/*
 * Function: ??0CMapData@@QEAA@XZ
 * Address: 0x140180050
 */

void __fastcall CMapData::CMapData(CMapData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CMapData *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CMapDataVtbl *)&CMapData::`vftable';
  CLevel::CLevel(&v5->m_Level);
  CExtDummy::CExtDummy(&v5->m_Dummy);
  CDummyPosTable::CDummyPosTable(&v5->m_tbSafeDumPos);
  CRecordData::CRecordData(&v5->m_tbMonBlk);
  CRecordData::CRecordData(&v5->m_tbPortal);
  CDummyPosTable::CDummyPosTable(&v5->m_tbMonDumPos);
  CDummyPosTable::CDummyPosTable(&v5->m_tbPortalDumPos);
  CDummyPosTable::CDummyPosTable(&v5->m_tbStoreDumPos);
  CDummyPosTable::CDummyPosTable(&v5->m_tbStartDumPos);
  CDummyPosTable::CDummyPosTable(&v5->m_tbBindDumPos);
  CDummyPosTable::CDummyPosTable(&v5->m_tbResDumPosHigh);
  CDummyPosTable::CDummyPosTable(&v5->m_tbResDumPosMiddle);
  CDummyPosTable::CDummyPosTable(&v5->m_tbResDumPosLow);
  CDummyPosTable::CDummyPosTable(&v5->m_tbQuestDumPos);
  CMyTimer::CMyTimer(&v5->m_tmrMineGradeReSet);
  v5->m_pMapSet = 0i64;
  v5->m_pPortal = 0i64;
  v5->m_pItemStoreDummy = 0i64;
  v5->m_pStartDummy = 0i64;
  v5->m_pBindDummy = 0i64;
  v5->m_pResDummy = 0i64;
  v5->m_pMonBlock = 0i64;
  v5->m_pExtDummy_Town = 0i64;
  v5->m_ls = 0i64;
  v5->m_mb = 0i64;
  v5->m_nMonBlockNum = 0;
  v5->m_nMonDumNum = 0;
  v5->m_nPortalNum = 0;
  v5->m_nStartDumNum = 0;
  v5->m_nBindDumNum = 0;
  v5->m_nItemStoreDumNum = 0;
  v5->m_nMapInPlayerNum = 0;
  v5->m_nMapInMonsterNum = 0;
  v5->m_bUse = 0;
  v5->m_nMonTotalCount = 0;
}
