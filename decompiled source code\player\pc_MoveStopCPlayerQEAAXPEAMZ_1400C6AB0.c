/*
 * Function: ?pc_MoveStop@CPlayer@@QEAAXPEAM@Z
 * Address: 0x1400C6AB0
 */

void __fastcall CPlayer::pc_MoveStop(CPlayer *this, float *pfCur)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@15
  float v5; // xmm0_4@16
  bool v6; // al@21
  bool v7; // al@23
  __int64 v8; // [sp+0h] [bp-38h]@1
  char v9; // [sp+20h] [bp-18h]@4
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  float *fPos; // [sp+48h] [bp+10h]@1

  fPos = pfCur;
  v10 = this;
  v2 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = 0;
  if ( v10->m_bMove )
  {
    if ( v10->m_pmTrd.bDTradeMode )
    {
      v9 = 7;
    }
    else if ( v10->m_bCorpse )
    {
      v9 = 8;
    }
    else if ( CPlayer::IsSiegeMode(v10) )
    {
      v9 = 12;
    }
    else if ( v10->m_byStandType == 1 )
    {
      v9 = 10;
    }
    else if ( v10->m_byUserDgr
           || (v4 = v10->m_fCurPos[0] - *fPos, abs(v4), v4 <= 200.0)
           && (v5 = v10->m_fCurPos[2] - fPos[2], abs(v5), v5 <= 200.0) )
    {
      if ( !CMapData::IsMapIn(v10->m_pCurMap, fPos) )
        v9 = 4;
    }
    else
    {
      v9 = 11;
    }
  }
  else
  {
    v9 = 6;
  }
  CRealMoveRequestDelayChecker::Reset(&v10->m_kMoveDelayChecker);
  if ( v9 )
  {
    CPlayer::SendMsg_MoveError(v10, v9);
    if ( v10->m_bMove )
    {
      v7 = CPlayer::IsOutExtraStopPos(v10, v10->m_fCurPos);
      CPlayer::SendMsg_Stop(v10, v7);
      CCharacter::Stop((CCharacter *)&v10->vfptr);
    }
  }
  else
  {
    memcpy_0(v10->m_fOldPos, v10->m_fCurPos, 0xCui64);
    memcpy_0(v10->m_fCurPos, fPos, 0xCui64);
    ++v10->m_nCheckMovePacket;
    v6 = CPlayer::IsOutExtraStopPos(v10, v10->m_fCurPos);
    CPlayer::SendMsg_Stop(v10, v6);
    CCharacter::Stop((CCharacter *)&v10->vfptr);
  }
}
