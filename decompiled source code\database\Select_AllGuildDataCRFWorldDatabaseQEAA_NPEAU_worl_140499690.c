/*
 * Function: ?Select_AllGuildData@CRFWorldDatabase@@QEAA_NPEAU_worlddb_guild_info@@@Z
 * Address: 0x140499690
 */

char __usercall CRFWorldDatabase::Select_AllGuildData@<al>(CRFWorldDatabase *this@<rcx>, _worlddb_guild_info *pGuildInfo@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp-20h] [bp-2888h]@1
  void *SQLStmt; // [sp+0h] [bp-2868h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+8h] [bp-2860h]@16
  SQLLEN v10; // [sp+18h] [bp-2850h]@16
  __int16 v11; // [sp+24h] [bp-2844h]@9
  char Dest; // [sp+40h] [bp-2828h]@4
  int v13; // [sp+2844h] [bp-24h]@4
  unsigned __int64 v14; // [sp+2850h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+2870h] [bp+8h]@1
  _worlddb_guild_info *v16; // [sp+2878h] [bp+10h]@1

  v16 = pGuildInfo;
  v15 = this;
  v3 = alloca(a3);
  v4 = &v7;
  for ( i = 2592i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  v13 = 0;
  sprintf(
    &Dest,
    "select top %d g.serial, g.grade, g.race, id, emblemBack, emblemMark, dalant, gold, masterserial, MasterBeforeGrade, "
    "r.totwin, r.totdraw, r.totlose, GMsg from tbl_guild as g left join tbl_GuildBattleRank as r on g.serial = r.serial w"
    "here g.dck=0 order by g.serial",
    500i64);
  if ( v15->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
  if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
  {
    v11 = SQLExecDirectA_0(v15->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        v16->wGuildCount = v13;
        result = 1;
      }
      else
      {
        SQLStmt = v15->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      while ( 1 )
      {
        v11 = SQLFetch_0(v15->m_hStmtSelect);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, 4, &v16->GuildData[v13], 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 2u, 5, &v16->GuildData[v13].byGuildGrade, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 3u, 5, &v16->GuildData[v13].byRace, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 4u, 1, v16->GuildData[v13].wszGuildName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 5u, 4, &v16->GuildData[v13].dwEmblemBack, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 6u, 4, &v16->GuildData[v13].dwEmblemMark, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 7u, 8, &v16->GuildData[v13].dDalant, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 8u, 8, &v16->GuildData[v13].dGold, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 9u, 4, &v16->GuildData[v13].dwMasterSerial, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 0xAu, 5, &v16->GuildData[v13].byMasterPrevGrade, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 0xBu, -18, &v16->GuildData[v13].dwTotWin, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 0xCu, -18, &v16->GuildData[v13].dwTotDraw, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 0xDu, -18, &v16->GuildData[v13].dwTotLose, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)256;
        v11 = SQLGetData_0(v15->m_hStmtSelect, 0xEu, 1, v16->GuildData[v13++].wszGreetingMsg, 256i64, &v10);
      }
      v16->wGuildCount = v13;
      if ( v15->m_hStmtSelect )
        SQLCloseCursor_0(v15->m_hStmtSelect);
      if ( v15->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
