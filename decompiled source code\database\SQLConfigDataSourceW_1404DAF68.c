/*
 * Function: SQLConfigDataSourceW
 * Address: 0x1404DAF68
 */

int __fastcall SQLConfigDataSourceW(HWND__ *hwndParent, unsigned __int16 fRequest, const unsigned __int16 *lpszDriver, const unsigned __int16 *lpszAttributes)
{
  HWND__ *v4; // rbp@1
  const unsigned __int16 *v5; // rbx@1
  const unsigned __int16 *v6; // rdi@1
  unsigned __int16 v7; // si@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = hwndParent;
  v5 = lpszAttributes;
  v6 = lpszDriver;
  v7 = fRequest;
  v8 = ODBC___GetSetupProc("SQLConfigDataSourceW");
  if ( v8 )
    result = ((int (__fastcall *)(HWND__ *, _QWORD, const unsigned __int16 *, const unsigned __int16 *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
