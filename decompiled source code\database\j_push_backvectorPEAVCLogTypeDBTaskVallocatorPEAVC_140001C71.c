/*
 * Function: j_?push_back@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAXAEBQEAVCLogTypeDBTask@@@Z
 * Address: 0x140001C71
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::push_back(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, CLogTypeDBTask *const *_Val)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::push_back(this, _Val);
}
