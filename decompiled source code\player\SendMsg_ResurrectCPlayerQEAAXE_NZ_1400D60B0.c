/*
 * Function: ?SendMsg_Resurrect@CPlayer@@QEAAXE_N@Z
 * Address: 0x1400D60B0
 */

void __fastcall CPlayer::SendMsg_Resurrect(CPlayer *this, char byRet, bool bQuickPotion)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v7; // [sp+39h] [bp-4Fh]@4
  __int16 v8; // [sp+3Ah] [bp-4Eh]@4
  __int16 v9; // [sp+3Ch] [bp-4Ch]@4
  __int16 v10; // [sp+3Eh] [bp-4Ah]@4
  bool v11; // [sp+40h] [bp-48h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v13; // [sp+65h] [bp-23h]@4
  CPlayer *v14; // [sp+90h] [bp+8h]@1
  bool v15; // [sp+A0h] [bp+18h]@1

  v15 = bQuickPotion;
  v14 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byRet;
  v7 = CPlayerDB::GetLevel(&v14->m_Param);
  v9 = CPlayerDB::GetFP(&v14->m_Param);
  v8 = CPlayerDB::GetHP(&v14->m_Param);
  v10 = CPlayerDB::GetSP(&v14->m_Param);
  v11 = v15;
  pbyType = 3;
  v13 = 40;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
