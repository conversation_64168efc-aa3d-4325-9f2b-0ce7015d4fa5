/*
 * Function: j_?_buybygold_buy_single_item_calc_price_one_n_one@CashItemRemoteStore@@AEAAKEHE@Z
 * Address: 0x14000FA74
 */

unsigned int __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price_one_n_one(CashItemRemoteStore *this, char bySet<PERSON><PERSON>, int nCsPrice, char byOverlapNum)
{
  return CashItemRemoteStore::_buybygold_buy_single_item_calc_price_one_n_one(this, bySetKind, nCsPrice, byOverlapNum);
}
