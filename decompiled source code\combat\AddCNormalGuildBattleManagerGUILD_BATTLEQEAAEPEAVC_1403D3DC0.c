/*
 * Function: ?Add@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEPEAVCGuild@@0KKEK@Z
 * Address: 0x1403D3DC0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Add(GUILD_BATTLE::CNormalGuildBattleManager *this, CGuild *pSrcGuild, CGuild *pDestGuild, unsigned int dwStartTime, unsigned int dwElapseTimeCnt, char byNumber, unsigned int dwMapCode)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char v9; // al@6
  GUILD_BATTLE::CNormalGuildBattleFieldList *v10; // rax@9
  unsigned int v11; // eax@14
  ATL::CTimeSpan *v12; // rax@16
  unsigned int v13; // eax@18
  __int64 v14; // [sp+0h] [bp-D8h]@1
  unsigned int dwMapInx; // [sp+34h] [bp-A4h]@9
  unsigned int v16; // [sp+54h] [bp-84h]@11
  GUILD_BATTLE::CGuildBattleSchedule *pkSchedule; // [sp+78h] [bp-60h]@11
  GUILD_BATTLE::CGuildBattleScheduleManager *v18; // [sp+88h] [bp-50h]@11
  char v19; // [sp+90h] [bp-48h]@11
  GUILD_BATTLE::CNormalGuildBattleStateListPool *v20; // [sp+98h] [bp-40h]@14
  GUILD_BATTLE::CNormalGuildBattleStateList *pkStateList; // [sp+A0h] [bp-38h]@14
  GUILD_BATTLE::CNormalGuildBattleFieldList *v22; // [sp+A8h] [bp-30h]@16
  GUILD_BATTLE::CNormalGuildBattleField *pkField; // [sp+B0h] [bp-28h]@16
  GUILD_BATTLE::CNormalGuildBattle *pkBattle; // [sp+B8h] [bp-20h]@18
  ATL::CTimeSpan result; // [sp+C0h] [bp-18h]@16
  GUILD_BATTLE::CNormalGuildBattleManager *v26; // [sp+E0h] [bp+8h]@1
  CGuild *pk1P; // [sp+E8h] [bp+10h]@1
  CGuild *pk2P; // [sp+F0h] [bp+18h]@1
  unsigned int dwStartTimeInx; // [sp+F8h] [bp+20h]@1

  dwStartTimeInx = dwStartTime;
  pk2P = pDestGuild;
  pk1P = pSrcGuild;
  v26 = this;
  v7 = &v14;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( pSrcGuild && pDestGuild )
  {
    if ( (signed int)(unsigned __int8)byNumber <= 50 )
    {
      dwMapInx = 0;
      v10 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
      if ( GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInx(v10, pk1P->m_byRace, dwMapCode, &dwMapInx) )
      {
        v16 = 0;
        pkSchedule = 0i64;
        v18 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
        v19 = GUILD_BATTLE::CGuildBattleScheduleManager::Add(
                v18,
                dwMapInx,
                dwStartTimeInx,
                dwElapseTimeCnt,
                &pkSchedule,
                &v16);
        if ( !v19 && pkSchedule )
        {
          v20 = GUILD_BATTLE::CNormalGuildBattleStateListPool::Instance();
          v11 = GUILD_BATTLE::CGuildBattleSchedule::GetSID(pkSchedule);
          pkStateList = GUILD_BATTLE::CNormalGuildBattleStateListPool::Get(v20, v11);
          if ( pkStateList )
          {
            v12 = GUILD_BATTLE::CGuildBattleSchedule::GetBattleTime(pkSchedule, &result);
            GUILD_BATTLE::CNormalGuildBattleStateList::SetBattleTime(pkStateList, (ATL::CTimeSpan)v12->m_timeSpan);
            GUILD_BATTLE::CGuildBattleSchedule::SetStateList(
              pkSchedule,
              (GUILD_BATTLE::CGuildBattleStateList *)&pkStateList->vfptr);
            v22 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
            pkField = GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(v22, dwMapInx);
            if ( pkField )
            {
              v13 = GUILD_BATTLE::CGuildBattleSchedule::GetSID(pkSchedule);
              pkBattle = GUILD_BATTLE::CNormalGuildBattleManager::GetBattle(v26, v13);
              if ( pkBattle )
              {
                GUILD_BATTLE::CNormalGuildBattle::Init(pkBattle, pk1P, pk2P, pkField, byNumber, pkStateList);
                if ( GUILD_BATTLE::CNormalGuildBattleManager::PushDQSData(v26, dwMapInx, v16, pkBattle, pkSchedule) )
                  v9 = 0;
                else
                  v9 = 110;
              }
              else
              {
                v9 = 110;
              }
            }
            else
            {
              v9 = 110;
            }
          }
          else
          {
            v9 = 110;
          }
        }
        else
        {
          v9 = v19;
        }
      }
      else
      {
        v9 = 120;
      }
    }
    else
    {
      v9 = 126;
    }
  }
  else
  {
    v9 = 110;
  }
  return v9;
}
