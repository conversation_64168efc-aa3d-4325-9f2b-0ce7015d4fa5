/**
 * @file CGuildDatabaseManager.h
 * @brief Modern C++20 Guild Database Manager
 * 
 * This file provides comprehensive guild database operations with modern C++20 patterns,
 * proper transaction management, error handling, and data integrity validation.
 * 
 * Refactored from decompiled sources:
 * - db_Insert_guildCMainThreadQEAAEPEAKPEADE0Z_1401B0C80.c (81 lines)
 * - db_disjoint_guildCMainThreadQEAAEKZ_1401B2390.c (26 lines)
 * - Delete_GuildCRFWorldDatabaseQEAA_NKZ_14049C2D0.c (28 lines)
 * - Various guild database operations
 */

#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>
#include <optional>
#include <array>

// Forward declarations for legacy compatibility
extern "C" {
    class CMainThread;
    class CRFWorldDatabase;
    class CCheckSumGuildData;
    
    extern CMainThread* g_pMainThread;
}

namespace NexusProtection::Guild {

/**
 * @brief Guild database operation result enumeration
 */
enum class GuildDatabaseResult : uint8_t {
    Success = 0,
    DatabaseError = 24,           // Legacy error code
    InvalidParameters,
    TransactionFailed,
    ChecksumError,
    UserUpdateFailed,
    GuildNotFound,
    DuplicateGuild,
    SystemError
};

/**
 * @brief Guild database operation type enumeration
 */
enum class GuildDatabaseOperation : uint8_t {
    Insert = 0,
    Update,
    Delete,
    Select,
    Disjoint
};

/**
 * @brief Guild creation data structure
 */
struct GuildCreationData {
    std::string guildName;
    uint8_t race;
    std::vector<uint32_t> memberSerials;  // Up to 8 founding members
    uint32_t masterSerial;
    
    // Validation
    bool IsValid() const {
        return !guildName.empty() && 
               guildName.length() <= 50 &&
               race < 3 &&
               memberSerials.size() <= 8 &&
               masterSerial != 0;
    }
};

/**
 * @brief Guild database statistics (non-atomic for copying)
 */
struct GuildDatabaseStats {
    uint64_t totalInsertions = 0;
    uint64_t totalDeletions = 0;
    uint64_t totalUpdates = 0;
    uint64_t totalSelections = 0;
    uint64_t successfulOperations = 0;
    uint64_t failedOperations = 0;
    uint64_t transactionRollbacks = 0;
    uint64_t checksumErrors = 0;
    std::chrono::steady_clock::time_point startTime;

    GuildDatabaseStats() : startTime(std::chrono::steady_clock::now()) {}

    double GetSuccessRate() const {
        uint64_t total = successfulOperations + failedOperations;
        return total > 0 ? static_cast<double>(successfulOperations) / total * 100.0 : 0.0;
    }

    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Internal atomic statistics for thread-safe updates
 */
struct AtomicGuildDatabaseStats {
    std::atomic<uint64_t> totalInsertions{0};
    std::atomic<uint64_t> totalDeletions{0};
    std::atomic<uint64_t> totalUpdates{0};
    std::atomic<uint64_t> totalSelections{0};
    std::atomic<uint64_t> successfulOperations{0};
    std::atomic<uint64_t> failedOperations{0};
    std::atomic<uint64_t> transactionRollbacks{0};
    std::atomic<uint64_t> checksumErrors{0};
    std::chrono::steady_clock::time_point startTime;

    AtomicGuildDatabaseStats() : startTime(std::chrono::steady_clock::now()) {}
};

/**
 * @brief Guild database configuration
 */
struct GuildDatabaseConfig {
    bool enableTransactions = true;          // Enable database transactions
    bool enableChecksumValidation = true;    // Enable checksum validation
    bool enableStatistics = true;            // Enable statistics collection
    bool enableErrorLogging = true;          // Enable error logging
    uint32_t maxRetryAttempts = 3;          // Maximum retry attempts for failed operations
    std::chrono::milliseconds retryDelay{100}; // Delay between retry attempts
    
    // Validation
    bool IsValid() const {
        return maxRetryAttempts > 0 && maxRetryAttempts <= 10;
    }
};

/**
 * @brief Guild database event callback types
 */
using GuildCreatedCallback = std::function<void(uint32_t guildSerial, const std::string& guildName)>;
using GuildDeletedCallback = std::function<void(uint32_t guildSerial)>;
using GuildUpdatedCallback = std::function<void(uint32_t guildSerial, const std::string& operation)>;
using DatabaseErrorCallback = std::function<void(GuildDatabaseOperation operation, const std::string& error)>;
using TransactionCallback = std::function<void(bool success, const std::string& operation)>;

/**
 * @brief Modern C++20 Guild Database Manager
 * 
 * Provides comprehensive guild database operations with proper error handling,
 * transaction management, and data integrity validation.
 */
class CGuildDatabaseManager {
public:
    /**
     * @brief Get singleton instance
     * 
     * @return Reference to the singleton instance
     */
    static CGuildDatabaseManager& GetInstance();

    /**
     * @brief Initialize guild database manager
     * 
     * @param config Database configuration
     * @return GuildDatabaseResult indicating success or failure
     */
    GuildDatabaseResult Initialize(const GuildDatabaseConfig& config = {});

    /**
     * @brief Shutdown guild database manager
     * 
     * @return GuildDatabaseResult indicating success or failure
     */
    GuildDatabaseResult Shutdown();

    /**
     * @brief Insert new guild into database
     * 
     * Modern implementation of db_Insert_guild function.
     * Creates guild with all associated data and member updates.
     * 
     * @param creationData Guild creation data
     * @param guildSerial Output guild serial number
     * @return GuildDatabaseResult indicating success or failure
     */
    GuildDatabaseResult InsertGuild(const GuildCreationData& creationData, uint32_t& guildSerial);

    /**
     * @brief Delete guild from database
     * 
     * Modern implementation of db_disjoint_guild function.
     * Removes guild and all associated data.
     * 
     * @param guildSerial Guild serial number to delete
     * @return GuildDatabaseResult indicating success or failure
     */
    GuildDatabaseResult DeleteGuild(uint32_t guildSerial);

    /**
     * @brief Insert guild (legacy compatibility)
     * 
     * @param memberSerials Array of member serials
     * @param guildName Guild name
     * @param race Guild race
     * @param guildSerial Output guild serial
     * @return Legacy result code (0 = success, 24 = error)
     */
    uint8_t db_Insert_guild(uint32_t* memberSerials, const char* guildName, uint8_t race, uint32_t* guildSerial);

    /**
     * @brief Delete guild (legacy compatibility)
     * 
     * @param guildSerial Guild serial number
     * @return Legacy result code (0 = success, 24 = error)
     */
    uint8_t db_disjoint_guild(uint32_t guildSerial);

    /**
     * @brief Get database statistics
     * 
     * @return Copy of current statistics
     */
    GuildDatabaseStats GetStatistics() const;

    /**
     * @brief Get database configuration
     * 
     * @return Copy of current configuration
     */
    GuildDatabaseConfig GetConfiguration() const;

    /**
     * @brief Update database configuration
     * 
     * @param config New configuration
     * @return true if successful, false otherwise
     */
    bool UpdateConfiguration(const GuildDatabaseConfig& config);

    /**
     * @brief Set event callbacks
     */
    void SetGuildCreatedCallback(GuildCreatedCallback callback);
    void SetGuildDeletedCallback(GuildDeletedCallback callback);
    void SetGuildUpdatedCallback(GuildUpdatedCallback callback);
    void SetDatabaseErrorCallback(DatabaseErrorCallback callback);
    void SetTransactionCallback(TransactionCallback callback);

    /**
     * @brief Check if manager is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized.load(); }

    /**
     * @brief Reset database statistics
     */
    void ResetStatistics();

    /**
     * @brief Validate guild name
     * 
     * @param guildName Guild name to validate
     * @return true if valid, false otherwise
     */
    bool ValidateGuildName(const std::string& guildName) const;

    /**
     * @brief Check if guild exists
     * 
     * @param guildName Guild name to check
     * @return true if exists, false otherwise
     */
    bool GuildExists(const std::string& guildName) const;

    /**
     * @brief Get guild serial by name
     * 
     * @param guildName Guild name
     * @param guildSerial Output guild serial
     * @return true if found, false otherwise
     */
    bool GetGuildSerial(const std::string& guildName, uint32_t& guildSerial) const;

private:
    // Singleton pattern
    CGuildDatabaseManager() = default;
    ~CGuildDatabaseManager() = default;
    CGuildDatabaseManager(const CGuildDatabaseManager&) = delete;
    CGuildDatabaseManager& operator=(const CGuildDatabaseManager&) = delete;
    CGuildDatabaseManager(CGuildDatabaseManager&&) = delete;
    CGuildDatabaseManager& operator=(CGuildDatabaseManager&&) = delete;

    /**
     * @brief Execute database operation with retry logic
     * 
     * @param operation Operation to execute
     * @param operationType Type of operation for logging
     * @return true if successful, false otherwise
     */
    bool ExecuteWithRetry(std::function<bool()> operation, GuildDatabaseOperation operationType);

    /**
     * @brief Create and validate checksum data
     * 
     * @param guildSerial Guild serial number
     * @return true if successful, false otherwise
     */
    bool CreateChecksumData(uint32_t guildSerial);

    /**
     * @brief Update member guild data
     * 
     * @param memberSerials Array of member serials
     * @param guildSerial Guild serial number
     * @return true if successful, false otherwise
     */
    bool UpdateMemberGuildData(const std::vector<uint32_t>& memberSerials, uint32_t guildSerial);

    /**
     * @brief Validate database connection
     * 
     * @return true if valid, false otherwise
     */
    bool ValidateDatabaseConnection() const;

    /**
     * @brief Log database error
     * 
     * @param operation Operation that failed
     * @param error Error message
     */
    void LogDatabaseError(GuildDatabaseOperation operation, const std::string& error);

    /**
     * @brief Update statistics
     * 
     * @param operation Operation type
     * @param success Whether operation was successful
     */
    void UpdateStatistics(GuildDatabaseOperation operation, bool success);

    /**
     * @brief Notify event callbacks
     */
    void NotifyGuildCreated(uint32_t guildSerial, const std::string& guildName);
    void NotifyGuildDeleted(uint32_t guildSerial);
    void NotifyGuildUpdated(uint32_t guildSerial, const std::string& operation);
    void NotifyDatabaseError(GuildDatabaseOperation operation, const std::string& error);
    void NotifyTransaction(bool success, const std::string& operation);

    // Member variables
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    
    GuildDatabaseConfig m_config;
    AtomicGuildDatabaseStats m_stats;
    
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    mutable std::mutex m_databaseMutex;
    
    // Event callbacks
    GuildCreatedCallback m_guildCreatedCallback;
    GuildDeletedCallback m_guildDeletedCallback;
    GuildUpdatedCallback m_guildUpdatedCallback;
    DatabaseErrorCallback m_databaseErrorCallback;
    TransactionCallback m_transactionCallback;
    
    // Error tracking
    std::chrono::steady_clock::time_point m_lastErrorTime;
    std::atomic<uint32_t> m_consecutiveErrors{0};
};

} // namespace NexusProtection::Guild
