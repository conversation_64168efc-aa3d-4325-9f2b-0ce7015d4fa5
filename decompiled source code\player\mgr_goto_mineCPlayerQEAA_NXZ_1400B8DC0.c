/*
 * Function: ?mgr_goto_mine@CPlayer@@QEAA_NXZ
 * Address: 0x1400B8DC0
 */

char __fastcall CPlayer::mgr_goto_mine(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-48h]@1
  float *pfStartPos; // [sp+20h] [bp-28h]@6
  CMapData *pIntoMap; // [sp+30h] [bp-18h]@4
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pIntoMap = CMapOperation::GetMap(&g_MapOper, "resources");
  if ( pIntoMap )
  {
    CPlayer::OutOfMap(v8, pIntoMap, 0, 4, fTarPos);
    v4 = (char *)pIntoMap->m_pMapSet;
    LOBYTE(pfStartPos) = 4;
    CPlayer::SendMsg_GotoRecallResult(v8, 0, *v4, fTarPos, 4);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
