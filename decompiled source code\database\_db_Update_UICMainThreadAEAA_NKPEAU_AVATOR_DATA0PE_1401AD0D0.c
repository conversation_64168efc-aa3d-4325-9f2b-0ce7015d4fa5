/*
 * Function: ?_db_Update_UI@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401AD0D0
 */

char __fastcall CMainThread::_db_Update_UI(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pSzQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int16 v7; // ax@6
  __int16 v8; // ax@7
  size_t v9; // rax@48
  __int64 v11; // [sp+0h] [bp-E8h]@1
  char Source; // [sp+30h] [bp-B8h]@4
  char v13; // [sp+31h] [bp-B7h]@4
  char *Dest; // [sp+B8h] [bp-30h]@4
  size_t Size; // [sp+C0h] [bp-28h]@4
  int v16; // [sp+D0h] [bp-18h]@6
  unsigned __int64 v17; // [sp+D8h] [bp-10h]@4
  unsigned int v18; // [sp+F8h] [bp+10h]@1
  _AVATOR_DATA *v19; // [sp+100h] [bp+18h]@1
  _AVATOR_DATA *v20; // [sp+108h] [bp+20h]@1

  v20 = pOldData;
  v19 = pNewData;
  v18 = dwSerial;
  v5 = &v11;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = (unsigned __int64)&v11 ^ _security_cookie;
  Source = 0;
  memset(&v13, 0, 0x7Fui64);
  Dest = pSzQuery;
  sprintf(pSzQuery, "UPDATE tbl_userinterface SET ");
  for ( Size = (unsigned int)strlen_0(Dest); SHIDWORD(Size) < 50; ++HIDWORD(Size) )
  {
    v16 = _LINKKEY::CovDBKey((_LINKKEY *)&v20->dbLink + SHIDWORD(Size));
    v7 = _LINKKEY::CovDBKey((_LINKKEY *)&v19->dbLink + SHIDWORD(Size));
    if ( v16 != v7 )
    {
      v8 = _LINKKEY::CovDBKey((_LINKKEY *)&v19->dbLink + SHIDWORD(Size));
      sprintf(&Source, "LB%d=%d,", HIDWORD(Size), (unsigned int)v8);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 8; ++HIDWORD(Size) )
  {
    if ( v20->dbSfcont.m_List[0][SHIDWORD(Size)].dwKey != v19->dbSfcont.m_List[0][SHIDWORD(Size)].dwKey )
    {
      sprintf(&Source, "DCF%d=%d,", HIDWORD(Size), v19->dbSfcont.m_List[0][SHIDWORD(Size)].dwKey);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 8; ++HIDWORD(Size) )
  {
    if ( v20->dbSfcont.m_List[1][SHIDWORD(Size)].dwKey != v19->dbSfcont.m_List[1][SHIDWORD(Size)].dwKey )
    {
      sprintf(&Source, "HCF%d=%d,", HIDWORD(Size), v19->dbSfcont.m_List[1][SHIDWORD(Size)].dwKey);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 2; ++HIDWORD(Size) )
  {
    if ( v20->dbLink.m_dwSkill[SHIDWORD(Size)] != v19->dbLink.m_dwSkill[SHIDWORD(Size)] )
    {
      sprintf(&Source, "SkillWin%d=%d,", HIDWORD(Size), v19->dbLink.m_dwSkill[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 2; ++HIDWORD(Size) )
  {
    if ( v20->dbLink.m_dwForce[SHIDWORD(Size)] != v19->dbLink.m_dwForce[SHIDWORD(Size)] )
    {
      sprintf(&Source, "ForceWin%d=%d,", HIDWORD(Size), v19->dbLink.m_dwForce[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 2; ++HIDWORD(Size) )
  {
    if ( v20->dbLink.m_dwCharacter[SHIDWORD(Size)] != v19->dbLink.m_dwCharacter[SHIDWORD(Size)] )
    {
      sprintf(&Source, "CharWin%d=%d,", HIDWORD(Size), v19->dbLink.m_dwCharacter[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 2; ++HIDWORD(Size) )
  {
    if ( v20->dbLink.m_dwAnimus[SHIDWORD(Size)] != v19->dbLink.m_dwAnimus[SHIDWORD(Size)] )
    {
      sprintf(&Source, "AnimusWin%d=%d,", HIDWORD(Size), v19->dbLink.m_dwAnimus[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  if ( v20->dbLink.m_dwInven != v19->dbLink.m_dwInven )
  {
    sprintf(&Source, "InvenWin=%d,", v19->dbLink.m_dwInven);
    strcat_0(Dest, &Source);
  }
  for ( HIDWORD(Size) = 0; SHIDWORD(Size) < 5; ++HIDWORD(Size) )
  {
    if ( v20->dbLink.m_dwInvenBag[SHIDWORD(Size)] != v19->dbLink.m_dwInvenBag[SHIDWORD(Size)] )
    {
      sprintf(&Source, "InvenBag%d=%d,", HIDWORD(Size), v19->dbLink.m_dwInvenBag[SHIDWORD(Size)]);
      strcat_0(Dest, &Source);
    }
  }
  if ( v20->dbLink.m_byLinkBoardLock != v19->dbLink.m_byLinkBoardLock )
  {
    sprintf(&Source, "LBLock=%d,", v19->dbLink.m_byLinkBoardLock);
    strcat_0(Dest, &Source);
  }
  v9 = strlen_0(Dest);
  if ( v9 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE Serial=%d", v18);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
