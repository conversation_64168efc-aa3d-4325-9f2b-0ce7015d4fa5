/*
 * Function: ?ct_pcbangitemget@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298B70
 */

bool __fastcall ct_pcbangitemget(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CPcBangFavor *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-A8h]@1
  int v6; // [sp+30h] [bp-78h]@8
  char v7; // [sp+44h] [bp-64h]@8
  char v8; // [sp+45h] [bp-63h]@8
  char v9; // [sp+46h] [bp-62h]@8
  char v10; // [sp+47h] [bp-61h]@8
  char v11; // [sp+48h] [bp-60h]@8
  unsigned int dwRecIndex; // [sp+68h] [bp-40h]@8
  char bySeletItemIndex; // [sp+6Ch] [bp-3Ch]@8
  char v14; // [sp+6Dh] [bp-3Bh]@8
  char v15; // [sp+6Eh] [bp-3Ah]@8
  char v16; // [sp+6Fh] [bp-39h]@8
  char v17; // [sp+70h] [bp-38h]@8
  unsigned __int64 v18; // [sp+90h] [bp-18h]@4
  CPlayer *pOnea; // [sp+B0h] [bp+8h]@1

  pOnea = pOne;
  v1 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v18 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( pOnea )
  {
    if ( s_nWordCount == 2 )
    {
      v6 = atoi(s_pwszDstCheat[0]);
      v7 = *s_pwszDstCheat[1];
      v8 = s_pwszDstCheat[1][1];
      v9 = s_pwszDstCheat[1][2];
      v10 = s_pwszDstCheat[1][3];
      v11 = s_pwszDstCheat[1][4];
      dwRecIndex = v6;
      bySeletItemIndex = v7 - 48;
      v14 = v8 - 48;
      v15 = v9 - 48;
      v16 = v10 - 48;
      v17 = v11 - 48;
      v4 = CPcBangFavor::Instance();
      result = CPcBangFavor::PcBangGiveItem(v4, pOnea, dwRecIndex, &bySeletItemIndex, 5) != 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
