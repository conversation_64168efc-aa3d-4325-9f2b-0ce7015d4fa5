/*
 * Function: ?DE_Potion_Race_Debuff_Clear_One@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017F3E0
 */

char __fastcall DE_Potion_Race_Debuff_Clear_One(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CRaceBuffManager *v7; // rax@12
  __int64 v8; // [sp+0h] [bp-88h]@1
  CPlayer *pkPlayer; // [sp+30h] [bp-58h]@10
  int v10; // [sp+38h] [bp-50h]@12
  char szMsg[4]; // [sp+44h] [bp-44h]@13
  char pbyType; // [sp+64h] [bp-24h]@13
  char v13; // [sp+65h] [bp-23h]@13
  unsigned int uiContinueCnt; // [sp+74h] [bp-14h]@12
  CPlayer *v15; // [sp+90h] [bp+8h]@1

  v15 = (CPlayer *)pActChar;
  v4 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v15 )
  {
    if ( v15->m_ObjID.m_byID )
    {
      result = 0;
    }
    else if ( (CCharacter *)v15 == pTargetChar )
    {
      pkPlayer = v15;
      if ( CPlayer::IsUseReleaseRaceBuffPotion(v15) )
      {
        result = 0;
      }
      else
      {
        uiContinueCnt = (signed int)ffloor(fEffectValue);
        v7 = CRaceBuffManager::Instance();
        v10 = CRaceBuffManager::CancelPlayerRaceBuff(v7, pkPlayer, RT_FAIL, uiContinueCnt);
        if ( v10 <= 0 )
        {
          *(_DWORD *)szMsg = v10;
          pbyType = 17;
          v13 = 37;
          CNetProcess::LoadSendMsg(unk_1414F2088, pkPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
        }
        CPlayer::SetUseReleaseRaceBuffPotion(pkPlayer);
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
