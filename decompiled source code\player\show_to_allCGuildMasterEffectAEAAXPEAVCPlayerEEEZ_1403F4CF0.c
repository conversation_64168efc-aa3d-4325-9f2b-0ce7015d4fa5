/*
 * Function: ?show_to_all@CGuildMasterEffect@@AEAAXPEAVCPlayer@@EEE@Z
 * Address: 0x1403F4CF0
 */

void __fastcall CGuildMasterEffect::show_to_all(CGuildMasterEffect *this, CPlayer *pP, char byBeforeGrade, char byGrade, char byState)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@5
  __int64 v8; // [sp+0h] [bp-58h]@1
  char v9; // [sp+40h] [bp-18h]@5
  char v10; // [sp+41h] [bp-17h]@5
  char v11; // [sp+42h] [bp-16h]@5
  CGuildMasterEffect *v12; // [sp+60h] [bp+8h]@1
  CPlayer *v13; // [sp+68h] [bp+10h]@1
  char v14; // [sp+70h] [bp+18h]@1
  char v15; // [sp+78h] [bp+20h]@1

  v15 = byGrade;
  v14 = byBeforeGrade;
  v13 = pP;
  v12 = this;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pP )
  {
    v9 = CGuildMasterEffect::get_DefenceValueByGrade(v12, byGrade);
    v10 = CGuildMasterEffect::get_AttactValueByGrade(v12, v15);
    v11 = CGuildMasterEffect::get_DefenceValueByGrade(v12, v14);
    v7 = CGuildMasterEffect::get_AttactValueByGrade(v12, v14);
    CPlayer::SendMsg_GuildMasterEffect(v13, byState, v15, v7, v11, v10, v9);
    CPlayer::SenseState(v13);
  }
}
