/*
 * Function: ??1?$CHashMapPtrPool@HVCNationCodeStr@@@@QEAA@XZ
 * Address: 0x1402083D0
 */

void __fastcall CHashMapPtrPool<int,CNationCodeStr>::~CHashMapPtrPool<int,CNationCodeStr>(CHashMapPtrPool<int,CNationCodeStr> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CHashMapPtrPool<int,CNationCodeStr> *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  if ( v5->m_bCleanUp )
    CHashMapPtrPool<int,CNationCodeStr>::cleanup(v5);
  stdext::hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>>::~hash_map<int,CNationCodeStr *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,CNationCodeStr *>>>(&v5->m_mapData);
}
