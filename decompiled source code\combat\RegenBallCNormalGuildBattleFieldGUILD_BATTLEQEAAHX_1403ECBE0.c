/*
 * Function: ?<PERSON><PERSON>Ball@CNormalGuildBattleField@GUILD_BATTLE@@QEAAHXZ
 * Address: 0x1403ECBE0
 */

int __fastcall GUILD_BATTLE::CNormalGuildBattleField::RegenBall(GUILD_BATTLE::CNormalGuildBattleField *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_bInit )
  {
    v5 = rand() % v6->m_uiRegenPosCnt;
    result = CGravityStoneRegener::Regen(&v6->m_pkRegenPos[v5]);
  }
  else
  {
    result = -1;
  }
  return result;
}
