/*
 * Function: j_?_Insert_n@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@2@_KAEBVCUnmannedTraderItemCodeInfo@@@Z
 * Address: 0x1400040A2
 */

void __fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Insert_n(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Where, unsigned __int64 _Count, CUnmannedTraderItemCodeInfo *_Val)
{
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Insert_n(
    this,
    _Where,
    _Count,
    _Val);
}
