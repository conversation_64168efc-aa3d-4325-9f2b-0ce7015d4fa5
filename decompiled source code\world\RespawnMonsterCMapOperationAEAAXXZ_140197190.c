/*
 * Function: ?RespawnMonster@CMapOperation@@AEAAXXZ
 * Address: 0x140197190
 */

void __fastcall CMapOperation::RespawnMonster(CMapOperation *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CBossMonsterScheduleSystem *v3; // rax@4
  ATL::CTime *v4; // rax@21
  ATL::CTime *v5; // rax@22
  ATL::CTimeSpan *v6; // rax@22
  ATL::CTime *v7; // rax@23
  int v8; // eax@48
  signed int v9; // ecx@48
  int v10; // ecx@61
  __int64 v11; // [sp+0h] [bp-148h]@1
  _dummy_position *pDumPosition; // [sp+20h] [bp-128h]@61
  bool bRobExp; // [sp+28h] [bp-120h]@61
  bool bRewardExp; // [sp+30h] [bp-118h]@61
  bool bDungeon; // [sp+38h] [bp-110h]@61
  bool bWithoutFail; // [sp+40h] [bp-108h]@61
  bool bApplyRopExpField; // [sp+48h] [bp-100h]@61
  unsigned int v18; // [sp+50h] [bp-F8h]@5
  int j; // [sp+54h] [bp-F4h]@5
  CMapData *pMap; // [sp+58h] [bp-F0h]@8
  int *v21; // [sp+60h] [bp-E8h]@11
  int v22; // [sp+68h] [bp-E0h]@11
  _mon_block *v23; // [sp+70h] [bp-D8h]@13
  _mon_block_fld *v24; // [sp+78h] [bp-D0h]@13
  int v25; // [sp+80h] [bp-C8h]@13
  int k; // [sp+84h] [bp-C4h]@13
  _mon_active *pActiveRec; // [sp+88h] [bp-C0h]@16
  _mon_active_fld *v28; // [sp+90h] [bp-B8h]@17
  int v29; // [sp+98h] [bp-B0h]@19
  BossSchedule *v30; // [sp+A0h] [bp-A8h]@20
  ATL::CTimeSpan v31; // [sp+B8h] [bp-90h]@22
  unsigned int v32; // [sp+C4h] [bp-84h]@25
  unsigned int v33; // [sp+C8h] [bp-80h]@29
  float v34; // [sp+CCh] [bp-7Ch]@30
  unsigned int v35; // [sp+D0h] [bp-78h]@30
  unsigned int v36; // [sp+D4h] [bp-74h]@30
  bool v37; // [sp+D8h] [bp-70h]@30
  unsigned int l; // [sp+DCh] [bp-6Ch]@30
  unsigned int v39; // [sp+E0h] [bp-68h]@34
  unsigned int v40; // [sp+E4h] [bp-64h]@35
  int v41; // [sp+E8h] [bp-60h]@35
  unsigned int m; // [sp+ECh] [bp-5Ch]@35
  unsigned int v43; // [sp+F0h] [bp-58h]@52
  CMonster *v44; // [sp+F8h] [bp-50h]@61
  char v45; // [sp+100h] [bp-48h]@21
  ATL::CTime *v46; // [sp+108h] [bp-40h]@21
  ATL::CTime result; // [sp+110h] [bp-38h]@22
  char v48; // [sp+118h] [bp-30h]@22
  ATL::CTimeSpan *v49; // [sp+120h] [bp-28h]@22
  ATL::CTime v50; // [sp+128h] [bp-20h]@23
  _mon_block_fld *v51; // [sp+130h] [bp-18h]@30
  bool v52; // [sp+138h] [bp-10h]@30
  CMapOperation *v53; // [sp+150h] [bp+8h]@1

  v53 = this;
  v1 = &v11;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CBossMonsterScheduleSystem::Instance();
  CBossMonsterScheduleSystem::RespawnMonster(v3);
  if ( CMonster::s_nLiveNum < 30000 )
  {
    v18 = timeGetTime();
    for ( j = 0; ; ++j )
    {
      if ( j >= v53->m_nMapNum )
        return;
      pMap = &v53->m_Map[j];
      if ( pMap->m_bUse )
      {
        if ( !pMap->m_pMapSet->m_nMapType && _LAYER_SET::IsActiveLayer(pMap->m_ls) )
          break;
      }
LABEL_6:
      ;
    }
    v21 = &pMap->m_ls->m_pMB->m_nBlockNum;
    v22 = 0;
LABEL_12:
    if ( v22 >= pMap->m_nMonBlockNum )
      goto LABEL_6;
    v23 = &pMap->m_pMonBlock[v22];
    v24 = v23->m_pBlkRec;
    v25 = CRecordData::GetRecordNum((CRecordData *)(*((_QWORD *)v21 + 1) + 176i64 * v22));
    for ( k = 0; ; ++k )
    {
      if ( k >= v25 )
      {
        ++v22;
        goto LABEL_12;
      }
      pActiveRec = &pMap->m_ls->m_MonAct[v22][k];
      if ( pActiveRec->m_nLimRegenNum > 0 )
      {
        v28 = pActiveRec->m_pActRec;
        if ( v23->m_bBossBlock && pActiveRec->m_nLimRegenNum > 1 )
          v29 = 0;
        v30 = _mon_active::GetBossSchedule(pActiveRec);
        if ( v30 )
        {
          v46 = (ATL::CTime *)&v45;
          ATL::CTime::CTime((ATL::CTime *)&v45, 0i64);
          if ( ATL::CTime::operator!=(&v30->m_LastRespawnSystemTime, (ATL::CTime)v4->m_time) )
          {
            v5 = ATL::CTime::GetTickCount(&result);
            ATL::CTime::operator-(v5, &v31, v30->m_LastRespawnSystemTime);
            v49 = (ATL::CTimeSpan *)&v48;
            ATL::CTimeSpan::CTimeSpan((ATL::CTimeSpan *)&v48, v28->m_dwRegenTime / 0x3E8);
            if ( ATL::CTimeSpan::operator<(&v31, (ATL::CTimeSpan)v6->m_timeSpan) )
              continue;
          }
          v7 = ATL::CTime::GetTickCount(&v50);
          BossSchedule::Save_LastRespawnSystemTime(v30, v7);
        }
        else
        {
          if ( pActiveRec->m_dwLastRespawnTime )
          {
            v32 = v18 - pActiveRec->m_dwLastRespawnTime;
            if ( v32 <= v28->m_dwRegenTime )
              continue;
          }
          pActiveRec->m_dwLastRespawnTime = v18;
        }
        if ( pActiveRec->m_wMonRecIndex != 0xFFFF && v28->m_dwRegenProp > rand() % 100 )
        {
          v33 = pActiveRec->m_nLimRegenNum - pActiveRec->m_zCurMonNum;
          if ( (signed int)v33 > 0 )
          {
            v34 = (float)(signed int)pActiveRec->m_pBlk->m_pBlkRec->m_dwDummyNum / (float)(signed int)v33;
            v51 = pActiveRec->m_pBlk->m_pBlkRec;
            v35 = v33 / v51->m_dwDummyNum;
            v36 = 0;
            v52 = pActiveRec->m_zCurMonNum == 0;
            v37 = v52;
            for ( l = 0; (signed int)l < (signed int)v33 && pActiveRec->m_zCurMonNum < pActiveRec->m_nLimRegenNum; ++l )
            {
              v39 = -1;
              if ( v37 )
              {
                if ( v34 <= 1.0 )
                {
                  if ( v34 >= 1.0 )
                  {
                    v39 = l;
                  }
                  else
                  {
                    v43 = pActiveRec->m_pBlk->m_pBlkRec->m_dwDummyNum * v35;
                    if ( (signed int)l <= (signed int)v43 )
                      v39 = (signed int)l / (signed int)v35;
                    else
                      v39 = _mon_block::SelectDummyIndex(v23);
                  }
                }
                else if ( l )
                {
                  v39 = (signed int)ffloor((float)(signed int)v36 + v34);
                  v36 = v39;
                }
                else
                {
                  v8 = rand();
                  v9 = (signed int)ffloor(v34);
                  v39 = v8 % v9;
                  v36 = v8 % v9;
                }
              }
              else
              {
                v40 = 0;
                v41 = v23->m_pDumPos[0]->m_wActiveMon;
                for ( m = 0; m < pActiveRec->m_pBlk->m_pBlkRec->m_dwDummyNum; ++m )
                {
                  if ( v23->m_pDumPos[m]->m_bPosAble && !v23->m_pDumPos[m]->m_wActiveMon )
                  {
                    v39 = m;
                    break;
                  }
                  if ( v41 > v23->m_pDumPos[m]->m_wActiveMon )
                  {
                    v41 = v23->m_pDumPos[m]->m_wActiveMon;
                    v40 = m;
                  }
                }
                if ( v39 == -1 )
                  v39 = v40;
              }
              if ( (v39 & 0x80000000) == 0 && v39 < pActiveRec->m_pBlk->m_pBlkRec->m_dwDummyNum )
              {
                if ( v23->m_pDumPos[v39]->m_bPosAble )
                {
                  v10 = pActiveRec->m_wMonRecIndex;
                  bApplyRopExpField = 1;
                  bWithoutFail = 0;
                  bDungeon = 0;
                  bRewardExp = 1;
                  bRobExp = 1;
                  pDumPosition = v23->m_pDumPos[v39];
                  v44 = CreateRespawnMonster(pMap, 0, v10, pActiveRec, pDumPosition, 1, 1, 0, 0, 1);
                  if ( !v44 )
                    return;
                }
              }
            }
          }
        }
      }
    }
  }
}
