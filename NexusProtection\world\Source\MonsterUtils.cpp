/**
 * @file MonsterUtils.cpp
 * @brief Implementation of monster utility functions
 * @details Provides utility functions for monster management, searching, and operations
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include "MonsterUtils.h"
#include <algorithm>
#include <stdexcept>
#include <cstring>

// Forward declaration includes (these would normally be in separate headers)
// For now, we'll use placeholder classes until we refactor the dependencies
class CMonster {
public:
    bool m_bLive;                    ///< Whether the monster is alive
    class CMapData* m_pCurMap;       ///< Current map the monster is on
    class CMonsterHierarchy m_MonHierarcy; ///< Monster hierarchy data
    struct MonsterRecord* m_pMonRec; ///< Monster record data
    class CGameObject* m_pTargetChar; ///< Target character
    
    CMonster() : m_bLive(false), m_pCurMap(nullptr), m_pMonRec(nullptr), m_pTargetChar(nullptr) {}
    
    /**
     * @brief Destroy the monster
     * @param flag Destruction flag
     * @param param Additional parameter
     */
    static void Destroy(CMonster* monster, int flag, void* param) {
        if (monster) {
            monster->m_bLive = false;
            monster->m_pTargetChar = nullptr;
            // Additional cleanup would be done here
        }
    }
    
    /**
     * @brief Get the emotion state of the monster
     * @param monster Pointer to the monster
     * @return Emotion state value
     */
    static int GetEmotionState(const CMonster* monster) {
        // Placeholder implementation
        return 0;
    }
};

class CMapData {
public:
    struct MapSet* m_pMapSet;
    
    CMapData() : m_pMapSet(nullptr) {}
};

struct MapSet {
    int m_nMapType;
    
    MapSet() : m_nMapType(0) {}
};

struct MonsterRecord {
    uint8_t m_bMonsterCondition;
    
    MonsterRecord() : m_bMonsterCondition(0) {}
};

class CMonsterHierarchy {
public:
    /**
     * @brief Get the number of child monsters
     * @param hierarchy Pointer to the hierarchy
     * @return Number of children
     */
    static uint8_t ChildKindCount(const CMonsterHierarchy* hierarchy) {
        // Placeholder implementation
        return 0;
    }
    
    /**
     * @brief Get the parent monster
     * @param hierarchy Pointer to the hierarchy
     * @return Pointer to parent monster, or nullptr if no parent
     */
    static CMonster* GetParent(const CMonsterHierarchy* hierarchy) {
        // Placeholder implementation
        return nullptr;
    }
};

// Global monster array (would be defined elsewhere in the real implementation)
char* g_Monster = nullptr;

namespace NexusProtection {
namespace World {
namespace MonsterUtils {

// Search for an empty monster slot
CMonster* SearchEmptyMonster(bool withoutFail) {
    try {
        if (!g_Monster) {
            return nullptr;
        }
        
        constexpr std::size_t maxMonsters = GetMaxMonsterSlots();
        constexpr std::size_t slotSize = GetMonsterSlotSize();
        
        // First pass: Look for completely empty slots
        for (std::size_t i = 0; i < maxMonsters; ++i) {
            const char* slotPtr = g_Monster + (slotSize * i);
            // Check if the slot is marked as empty (byte at offset 24)
            if (*(slotPtr + 24) == 0) {
                return reinterpret_cast<CMonster*>(const_cast<char*>(slotPtr));
            }
        }
        
        // If withoutFail is false, return nullptr if no empty slots found
        if (!withoutFail) {
            return nullptr;
        }
        
        // Second pass: Look for non-live monsters
        for (std::size_t i = 0; i < maxMonsters; ++i) {
            CMonster* monster = reinterpret_cast<CMonster*>(g_Monster + (slotSize * i));
            if (!monster->m_bLive) {
                return monster;
            }
        }
        
        // Third pass: Look for destroyable monsters (more restrictive criteria)
        for (std::size_t i = 0; i < maxMonsters; ++i) {
            CMonster* monster = reinterpret_cast<CMonster*>(g_Monster + (slotSize * i));
            
            if (CanDestroyMonster(monster)) {
                CMonster::Destroy(monster, 1, nullptr);
                return monster;
            }
        }
        
        // Fourth pass: Look for monsters with targets (less restrictive)
        for (std::size_t i = 0; i < maxMonsters; ++i) {
            CMonster* monster = reinterpret_cast<CMonster*>(g_Monster + (slotSize * i));
            
            if (!monster->m_bLive) {
                return monster;
            }
            
            // Check if monster can be destroyed (less restrictive criteria)
            if (monster->m_pCurMap && 
                monster->m_pCurMap->m_pMapSet && 
                monster->m_pCurMap->m_pMapSet->m_nMapType == 0 &&
                CMonsterHierarchy::ChildKindCount(&monster->m_MonHierarcy) <= 0 &&
                !CMonsterHierarchy::GetParent(&monster->m_MonHierarcy) &&
                monster->m_pMonRec &&
                monster->m_pMonRec->m_bMonsterCondition != 1) {
                
                CMonster::Destroy(monster, 1, nullptr);
                return monster;
            }
        }
        
        return nullptr;
    }
    catch (const std::exception&) {
        return nullptr;
    }
}

// Search for an empty monster slot with options
CMonster* SearchEmptyMonster(const MonsterSearchOptions& options) {
    return SearchEmptyMonster(options.withoutFail);
}

// Find monsters matching specific criteria
std::vector<CMonster*> FindMonsters(MonsterSearchCriteria criteria, std::size_t maxResults) {
    std::vector<CMonster*> results;
    results.reserve(std::min(maxResults, GetMaxMonsterSlots()));
    
    if (!g_Monster) {
        return results;
    }
    
    constexpr std::size_t maxMonsters = GetMaxMonsterSlots();
    constexpr std::size_t slotSize = GetMonsterSlotSize();
    
    for (std::size_t i = 0; i < maxMonsters && results.size() < maxResults; ++i) {
        CMonster* monster = reinterpret_cast<CMonster*>(g_Monster + (slotSize * i));
        
        bool matches = true;
        
        if (HasCriteria(criteria, MonsterSearchCriteria::EmptySlot)) {
            matches &= IsEmptySlot(monster);
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::NotLive)) {
            matches &= !monster->m_bLive;
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::NoChildren)) {
            matches &= (CMonsterHierarchy::ChildKindCount(&monster->m_MonHierarcy) <= 0);
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::NoParent)) {
            matches &= !CMonsterHierarchy::GetParent(&monster->m_MonHierarcy);
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::NoTarget)) {
            matches &= !monster->m_pTargetChar;
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::NormalCondition)) {
            matches &= (monster->m_pMonRec && monster->m_pMonRec->m_bMonsterCondition != 1);
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::NoEmotion)) {
            matches &= (CMonster::GetEmotionState(monster) == 0);
        }
        
        if (HasCriteria(criteria, MonsterSearchCriteria::InNormalMap)) {
            matches &= (monster->m_pCurMap && 
                       monster->m_pCurMap->m_pMapSet && 
                       monster->m_pCurMap->m_pMapSet->m_nMapType == 0);
        }
        
        if (matches) {
            results.push_back(monster);
        }
    }
    
    return results;
}

// Check if a monster slot is empty
bool IsEmptySlot(const CMonster* monster) {
    if (!monster || !g_Monster) {
        return false;
    }
    
    // Calculate the offset from the base of the monster array
    const char* monsterPtr = reinterpret_cast<const char*>(monster);
    const char* basePtr = g_Monster;
    
    if (monsterPtr < basePtr) {
        return false;
    }
    
    std::size_t offset = monsterPtr - basePtr;
    constexpr std::size_t slotSize = GetMonsterSlotSize();
    
    // Check if it's aligned to a slot boundary
    if (offset % slotSize != 0) {
        return false;
    }
    
    // Check the empty flag at offset 24
    return *(monsterPtr + 24) == 0;
}

// Check if a monster can be safely destroyed
bool CanDestroyMonster(const CMonster* monster) {
    if (!monster || !monster->m_bLive) {
        return false;
    }
    
    // Check various conditions that make a monster safe to destroy
    return (monster->m_pCurMap && 
            monster->m_pCurMap->m_pMapSet && 
            monster->m_pCurMap->m_pMapSet->m_nMapType == 0 &&
            CMonsterHierarchy::ChildKindCount(&monster->m_MonHierarcy) <= 0 &&
            !CMonsterHierarchy::GetParent(&monster->m_MonHierarcy) &&
            monster->m_pMonRec &&
            monster->m_pMonRec->m_bMonsterCondition != 1 &&
            CMonster::GetEmotionState(monster) == 0);
}

// Get monster by index
CMonster* GetMonsterByIndex(std::size_t index) {
    if (!g_Monster || index >= GetMaxMonsterSlots()) {
        return nullptr;
    }
    
    constexpr std::size_t slotSize = GetMonsterSlotSize();
    return reinterpret_cast<CMonster*>(g_Monster + (slotSize * index));
}

// Get the index of a monster in the global array
std::optional<std::size_t> GetMonsterIndex(const CMonster* monster) {
    if (!monster || !g_Monster) {
        return std::nullopt;
    }
    
    const char* monsterPtr = reinterpret_cast<const char*>(monster);
    const char* basePtr = g_Monster;
    
    if (monsterPtr < basePtr) {
        return std::nullopt;
    }
    
    std::size_t offset = monsterPtr - basePtr;
    constexpr std::size_t slotSize = GetMonsterSlotSize();
    
    // Check if it's aligned to a slot boundary
    if (offset % slotSize != 0) {
        return std::nullopt;
    }
    
    std::size_t index = offset / slotSize;
    if (index >= GetMaxMonsterSlots()) {
        return std::nullopt;
    }
    
    return index;
}

// Count empty monster slots
std::size_t CountEmptySlots() {
    if (!g_Monster) {
        return 0;
    }
    
    std::size_t count = 0;
    constexpr std::size_t maxMonsters = GetMaxMonsterSlots();
    
    for (std::size_t i = 0; i < maxMonsters; ++i) {
        CMonster* monster = GetMonsterByIndex(i);
        if (IsEmptySlot(monster)) {
            ++count;
        }
    }
    
    return count;
}

// Count live monsters
std::size_t CountLiveMonsters() {
    if (!g_Monster) {
        return 0;
    }
    
    std::size_t count = 0;
    constexpr std::size_t maxMonsters = GetMaxMonsterSlots();
    
    for (std::size_t i = 0; i < maxMonsters; ++i) {
        CMonster* monster = GetMonsterByIndex(i);
        if (monster && monster->m_bLive) {
            ++count;
        }
    }
    
    return count;
}

// Validate monster pointer
bool ValidateMonsterPointer(const CMonster* monster) {
    return GetMonsterIndex(monster).has_value();
}

// Apply a function to all monsters matching criteria
std::size_t ForEachMonster(MonsterSearchCriteria criteria, std::function<void(CMonster*)> func) {
    auto monsters = FindMonsters(criteria, GetMaxMonsterSlots());
    
    for (CMonster* monster : monsters) {
        func(monster);
    }
    
    return monsters.size();
}

// Get comprehensive monster statistics
MonsterStats GetMonsterStatistics() {
    MonsterStats stats{};
    stats.totalSlots = GetMaxMonsterSlots();
    
    if (!g_Monster) {
        return stats;
    }
    
    for (std::size_t i = 0; i < GetMaxMonsterSlots(); ++i) {
        CMonster* monster = GetMonsterByIndex(i);
        
        if (IsEmptySlot(monster)) {
            ++stats.emptySlots;
        } else if (monster) {
            if (monster->m_bLive) {
                ++stats.liveMonsters;
            }
            
            if (CanDestroyMonster(monster)) {
                ++stats.destroyableMonsters;
            }
            
            if (CMonsterHierarchy::ChildKindCount(&monster->m_MonHierarcy) > 0) {
                ++stats.monstersWithChildren;
            }
            
            if (CMonsterHierarchy::GetParent(&monster->m_MonHierarcy)) {
                ++stats.monstersWithParents;
            }
            
            if (monster->m_pTargetChar) {
                ++stats.monstersWithTargets;
            }
        }
    }
    
    return stats;
}

// Cleanup destroyed monsters
std::size_t CleanupDestroyedMonsters() {
    std::size_t cleanedUp = 0;
    
    auto destroyedMonsters = FindMonsters(MonsterSearchCriteria::NotLive, GetMaxMonsterSlots());
    
    for (CMonster* monster : destroyedMonsters) {
        // Perform additional cleanup operations here
        // For now, just count them
        ++cleanedUp;
    }
    
    return cleanedUp;
}

} // namespace MonsterUtils
} // namespace World
} // namespace NexusProtection

// Legacy C-style interface implementation
extern "C" {
    CMonster* SearchEmptyMonster(bool bWithoutFail) {
        return NexusProtection::World::MonsterUtils::SearchEmptyMonster(bWithoutFail);
    }
}
