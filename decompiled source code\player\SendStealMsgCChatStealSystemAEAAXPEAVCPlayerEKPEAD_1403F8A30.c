/*
 * Function: ?SendStealMsg@CChatStealSystem@@AEAAXPEAVCPlayer@@EKPEADE1@Z
 * Address: 0x1403F8A30
 */

void __fastcall CChatStealSystem::SendStealMsg(CChatStealSystem *this, CPlayer *pPlayer, char byChatType, unsigned int dwSenderSerial, char *pwszSender, char byRaceCode, char *pwszMessage)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v9; // ax@9
  __int64 v10; // [sp+0h] [bp-1A8h]@1
  _chat_steal_message_gm_zocl Dst; // [sp+40h] [bp-168h]@7
  char pbyType; // [sp+174h] [bp-34h]@9
  char v13; // [sp+175h] [bp-33h]@9
  unsigned __int64 v14; // [sp+190h] [bp-18h]@4
  CPlayer *v15; // [sp+1B8h] [bp+10h]@1
  char v16; // [sp+1C0h] [bp+18h]@1
  unsigned int v17; // [sp+1C8h] [bp+20h]@1

  v17 = dwSenderSerial;
  v16 = byChatType;
  v15 = pPlayer;
  v7 = &v10;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v14 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( pPlayer && pPlayer->m_bLive )
  {
    _chat_steal_message_gm_zocl::_chat_steal_message_gm_zocl(&Dst);
    Dst.byMessageType = v16;
    Dst.dwSenderSerial = v17;
    strcpy_0(Dst.wszSenderName, pwszSender);
    Dst.byRaceCode = byRaceCode;
    Dst.bFiltering = 0;
    Dst.bySize = strlen_0(pwszMessage);
    if ( (signed int)(unsigned __int8)Dst.bySize > 255 )
      Dst.bySize = -1;
    memcpy_0(Dst.wszChatData, pwszMessage, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    Dst.byPvpGrade = v15->m_Param.m_byPvPGrade;
    pbyType = 2;
    v13 = 15;
    v9 = _chat_steal_message_gm_zocl::size(&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v9);
  }
}
