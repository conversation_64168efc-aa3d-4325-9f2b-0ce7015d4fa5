/*
 * Function: j_?IsEmptyTime@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAEIKK@Z
 * Address: 0x14000A0A6
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsEmptyTime(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::IsEmptyTime(
           this,
           uiFieldInx,
           dwStartTimeInx,
           dwElapseTimeCnt);
}
