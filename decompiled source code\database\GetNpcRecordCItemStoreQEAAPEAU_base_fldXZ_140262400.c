/*
 * Function: ?GetNpcRecord@CItemStore@@QEAAPEAU_base_fld@@XZ
 * Address: 0x140262400
 */

_base_fld *__fastcall CItemStore::GetNpcRecord(CItemStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CItemStore *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CRecordData::GetRecord(&stru_1799C62C0, v5->m_pRec->m_strStore_NPCcode);
}
