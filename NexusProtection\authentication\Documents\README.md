# Authentication Module

## Overview

This module contains the refactored authentication system for the NexusProtection game server, starting with the AccountServerLogin functionality. The code has been refactored from decompiled C source to modern C++20 standards compatible with Visual Studio 2022.

## Refactored Files

### AccountServerLogin
- **Original**: `decompiled source ode/authentication/AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c`
- **Refactored Header**: `NexusProtection/authentication/Headers/AccountServerLogin.h`
- **Refactored Source**: `NexusProtection/authentication/Source/AccountServerLogin.cpp`

### BillingManagerLogin
- **Original**: `decompiled source ode/authentication/LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c`
- **Refactored Header**: `NexusProtection/authentication/Headers/BillingManagerLogin.h`
- **Refactored Source**: `NexusProtection/authentication/Source/BillingManagerLogin.cpp`

### NationSettingSessionManager
- **Original**: `decompiled source ode/authentication/OnConnectSessionCNationSettingManagerQEAAXHZ_140229400.c`
- **Refactored Header**: `NexusProtection/authentication/Headers/NationSettingSessionManager.h`
- **Refactored Source**: `NexusProtection/authentication/Source/NationSettingSessionManager.cpp`

## File Structure

```
NexusProtection/authentication/
├── Headers/
│   ├── AccountServerLogin.h
│   ├── BillingManagerLogin.h
│   └── NationSettingSessionManager.h
├── Source/
│   ├── AccountServerLogin.cpp
│   ├── BillingManagerLogin.cpp
│   └── NationSettingSessionManager.cpp
└── Documents/
    └── README.md
```

## Original Function Analysis

### AccountServerLogin
The original `CMainThread::AccountServerLogin` function performed the following operations:

1. **Stack Protection Setup**: Used security cookies for buffer overflow protection
2. **World Name Copying**: Copied world name from CMainThread instance
3. **Configuration Reading**: Read GateIP from WorldInfo.ini file
4. **IP Address Resolution**: Resolved IP address or used local IP if default
5. **Hash Verification**: Prepared hash verification data for security
6. **Message Sending**: Sent login request to account server
7. **Cash DB Request**: Sent cash database DSN request via nation manager

### BillingManagerLogin
The original `CBillingManager::Login` function performed the following operations:

1. **Stack Protection Setup**: Used security cookies for buffer overflow protection
2. **Parameter Handling**: Received CUserDB pointer as input
3. **Virtual Function Call**: Called the Login method on the billing system through virtual function table
4. **Billing System Integration**: Integrated with the underlying billing implementation

### NationSettingSessionManager
The original `CNationSettingManager::OnConnectSession` function performed the following operations:

1. **Stack Protection Setup**: Used security cookies for buffer overflow protection
2. **Parameter Validation**: Received session ID as input parameter
3. **Game Guard System Access**: Retrieved game guard system from nation setting data
4. **Virtual Function Call**: Called OnConnectSession on the game guard system through virtual function table
5. **Session Management**: Managed session connection state and tracking

1. **Stack Protection Setup**: Used security cookies for buffer overflow protection
2. **World Name Copying**: Copied world name from CMainThread instance
3. **Configuration Reading**: Read GateIP from WorldInfo.ini file
4. **IP Address Resolution**: Resolved IP address or used local IP if default
5. **Hash Verification**: Prepared hash verification data for security
6. **Message Sending**: Sent login request to account server
7. **Cash DB Request**: Sent cash database DSN request via nation manager

## Refactoring Improvements

### Modern C++20 Features
- **RAII Principles**: Automatic resource management
- **Smart Pointers**: Safe memory management where applicable
- **std::string**: Modern string handling instead of C-style strings
- **std::format**: Modern string formatting
- **Exception Handling**: Comprehensive error handling
- **Enum Classes**: Type-safe enumerations

### Security Enhancements
- **Stack Protection**: Maintained original security cookie validation
- **Input Validation**: Added parameter validation
- **Error Handling**: Detailed error messages and exception handling
- **Buffer Safety**: Safe string operations and bounds checking

### Code Organization
- **Class-based Design**: Encapsulated functionality in AccountServerLogin class
- **Separation of Concerns**: Clear separation between data and operations
- **Legacy Compatibility**: Maintained backward compatibility with original interface

## Class Structure

### AccountServerLogin Class

#### Public Methods
- `ExecuteLogin(CMainThread* mainThread)` - Main login execution
- `AccountServerLogin_Legacy(CMainThread* mainThread)` - Legacy compatibility wrapper
- `GetLastError()` - Retrieve last error message
- `GetConnectionInfo()` - Get connection information

#### Private Methods
- `InitializeConnectionInfo()` - Initialize world connection data
- `ReadConfiguration()` - Read WorldInfo.ini configuration
- `ResolveIPAddress()` - Resolve IP address from configuration
- `PrepareHashVerification()` - Prepare security hash data
- `SendLoginRequest()` - Send login request to server
- `SendCashDBRequest()` - Send cash database request
- `ValidateSecurityCookie()` - Validate stack protection

### WorldConnectionInfo Structure
- `worldName` - Name of the world server
- `gateIP` - Gateway IP address string
- `ipAddress` - Resolved IP address
- `hashVerify[32]` - Hash verification data
- `connectionType` - Connection type identifier
- `protocolVersion` - Protocol version

### Result Enumerations
- `AccountServerLoginResult` - Login operation results
- Success, Failure, InvalidWorldName, NetworkError, ConfigurationError, SecurityError, SystemError

## Configuration

The system reads configuration from `..\\WorldInfo\\WorldInfo.ini`:

```ini
[System]
GateIP=X  ; Use "X" for local IP, or specify IP address
```

## Usage Examples

### Modern Interface
```cpp
AccountServerLogin loginHandler;
AccountServerLoginResult result = loginHandler.ExecuteLogin(mainThread);

if (result == AccountServerLoginResult::Success) {
    std::cout << "Login successful!" << std::endl;
    const auto& info = loginHandler.GetConnectionInfo();
    std::cout << "Connected to: " << info.worldName << std::endl;
} else {
    std::cout << "Login failed: " << loginHandler.GetLastError() << std::endl;
}
```

### Legacy Compatibility
```cpp
// Original function signature maintained
AccountServerLogin::AccountServerLogin_Legacy(mainThread);
```

## Dependencies

### External Functions
- `GetIPAddress()` - Get local IP address
- `inet_addr()` - Convert IP string to address
- `GetPrivateProfileStringA()` - Read INI file configuration
- `CNetProcess_LoadSendMsg()` - Send network message
- `CTSingleton_CNationSettingManager_Instance()` - Get nation manager
- `CNationSettingManager_SendCashDBDSNRequest()` - Send cash DB request

### External Variables
- `g_cbHashVerify[32]` - Global hash verification data
- `unk_1414F2090` - Network process instance
- `_security_cookie` - Security cookie for stack protection

## Error Handling

The refactored code provides comprehensive error handling:

1. **Parameter Validation**: Null pointer checks and parameter validation
2. **Configuration Errors**: INI file reading and parsing errors
3. **Network Errors**: Connection and message sending failures
4. **Security Errors**: Stack corruption detection and hash verification
5. **System Errors**: General system and exception handling

## Security Features

### Stack Protection
- Maintains original security cookie mechanism
- Detects stack buffer overflows
- Validates stack integrity before function return

### Input Validation
- Validates all input parameters
- Checks configuration file data
- Verifies network message integrity

### Error Reporting
- Detailed error messages for debugging
- Secure error handling without information leakage
- Comprehensive logging for security monitoring

## Compilation Requirements

- **Compiler**: Visual Studio 2022 (Platform Toolset v143)
- **Standard**: C++17 or C++20
- **Architecture**: x64
- **Runtime**: Multi-threaded DLL (/MD)

## Testing Recommendations

1. **Unit Tests**: Test individual methods with mock data
2. **Integration Tests**: Test with actual configuration files
3. **Security Tests**: Verify stack protection and error handling
4. **Performance Tests**: Ensure no performance regression
5. **Compatibility Tests**: Verify legacy interface compatibility

## Future Enhancements

1. **Async Operations**: Consider async/await patterns for network operations
2. **Configuration Management**: Enhanced configuration validation and management
3. **Logging Framework**: Integration with structured logging system
4. **Metrics Collection**: Performance and usage metrics
5. **Security Hardening**: Additional security measures and validation

## Notes

- The refactored code maintains full backward compatibility with the original function
- All original functionality has been preserved while adding modern C++ safety features
- The code follows RAII principles and modern C++ best practices
- Security features from the original code have been maintained and enhanced
