/*
 * Function: j_?post_senditem@CMgrAvatorItemHistory@@QEAAXPEADPEAU_db_con@_STORAGE_LIST@@_KK0@Z
 * Address: 0x1400036AC
 */

void __fastcall CMgrAvatorItemHistory::post_senditem(CMgrAvatorItemHistory *this, char *wszRecvName, _STORAGE_LIST::_db_con *Item, unsigned __int64 dwDur, unsigned int dwGold, char *pFileName)
{
  CMgrAvatorItemHistory::post_senditem(this, wszRecvName, Item, dwDur, dwGold, pFileName);
}
