/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@stdext@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@AEAV?$allocator@PEAVCMoveMapLimitRight@@@std@@@Z
 * Address: 0x140003873
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val, std::allocator<CMoveMapLimitRight *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
