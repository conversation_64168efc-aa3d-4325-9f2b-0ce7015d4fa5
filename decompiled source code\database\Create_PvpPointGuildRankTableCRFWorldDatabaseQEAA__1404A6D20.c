/*
 * Function: ?Create_PvpPointGuildRankTable@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404A6D20
 */

bool __fastcall CRFWorldDatabase::Create_PvpPointGuildRankTable(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-468h]@1
  char *v6; // [sp+20h] [bp-448h]@4
  char Dst; // [sp+40h] [bp-428h]@4
  unsigned __int64 v8; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+470h] [bp+8h]@1
  char *v10; // [sp+478h] [bp+10h]@1

  v10 = szDate;
  v9 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  v6 = v10;
  sprintf(
    &Dst,
    "create table [dbo].[tbl_PvpPointGuildRank%s] ( [serial] [int] not null, [rank] [int] not null, [id] [varchar](17) no"
    "t null, [race]  [tinyint] not null, [grade] [smallint] not null, [killpvppoint] [float] not null, [guildbattlepvppoi"
    "nt] [float] not null, [sumpvppoint] [float] not null, [sumlv] [int] null constraint [DF_tbl_PvpPointGuildRank%s_suml"
    "v] default (0) constraint [tbl_PvpPointGuildRank%s_serial] primary key clustered ([serial]) on [Primary] ) on [PRIMARY]",
    v10,
    v10);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dst, 1);
}
