/*
 * Function: j_?MakeNewItems@ItemCombineMgr@@IEAAEPEAU_ITEMCOMBINE_DB_BASE@@PEAU_combine_ex_item_accept_request_clzo@@PEAU_combine_ex_item_accept_result_zocl@@@Z
 * Address: 0x14000A8DF
 */

char __fastcall ItemCombineMgr::MakeNewItems(ItemCombineMgr *this, _ITEMCOMBINE_DB_BASE *pPlayerItemDB, _combine_ex_item_accept_request_clzo *pRecv, _combine_ex_item_accept_result_zocl *pSend)
{
  return ItemCombineMgr::MakeNewItems(this, pPlayerItemDB, pRecv, pSend);
}
