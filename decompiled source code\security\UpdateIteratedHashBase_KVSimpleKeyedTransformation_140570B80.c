/*
 * Function: ?Update@?$IteratedHashBase@_KV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXPEBE_K@Z
 * Address: 0x140570B80
 */

int __fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::Update(__int64 a1, char *a2, unsigned __int64 a3)
{
  __int64 v3; // rax@3
  __int64 v4; // rax@4
  __int64 v5; // rax@5
  char *v6; // rax@7
  __int64 v7; // rax@10
  unsigned int v9; // [sp+20h] [bp-E8h]@7
  unsigned __int64 v10; // [sp+28h] [bp-E0h]@1
  unsigned __int64 a; // [sp+30h] [bp-D8h]@1
  char *v12; // [sp+38h] [bp-D0h]@7
  __int64 v13; // [sp+40h] [bp-C8h]@7
  unsigned int b; // [sp+48h] [bp-C0h]@7
  __int64 v15; // [sp+50h] [bp-B8h]@17
  char v16; // [sp+58h] [bp-B0h]@5
  char v17; // [sp+A8h] [bp-60h]@5
  __int64 v18; // [sp+D8h] [bp-30h]@1
  __int64 v19; // [sp+E0h] [bp-28h]@5
  __int64 v20; // [sp+E8h] [bp-20h]@5
  __int64 v21; // [sp+110h] [bp+8h]@1
  char *v22; // [sp+118h] [bp+10h]@1
  unsigned __int64 v23; // [sp+120h] [bp+18h]@1

  v23 = a3;
  v22 = a2;
  v21 = a1;
  v18 = -2i64;
  a = *(_QWORD *)(a1 + 16);
  v10 = *(_QWORD *)(a1 + 24);
  *(_QWORD *)(a1 + 16) = a3 + a;
  if ( *(_QWORD *)(a1 + 16) < a )
    ++*(_QWORD *)(a1 + 24);
  LODWORD(v3) = CryptoPP::SafeRightShift<64,unsigned __int64>(a3);
  *(_QWORD *)(v21 + 24) += v3;
  if ( *(_QWORD *)(v21 + 24) < v10 || (LODWORD(v4) = CryptoPP::SafeRightShift<128,unsigned __int64>(v23), v4) )
  {
    LODWORD(v5) = (*(int (__fastcall **)(__int64, char *))(*(_QWORD *)v21 + 16i64))(v21, &v17);
    v19 = v5;
    v20 = v5;
    CryptoPP::HashInputTooLong::HashInputTooLong((CryptoPP::InvalidDataFormat *)&v16, v5);
    CxxThrowException_0((__int64)&v16, (__int64)&TI4_AVHashInputTooLong_CryptoPP__);
  }
  b = (*(int (__fastcall **)(__int64))(*(_QWORD *)v21 + 64i64))(v21);
  v9 = CryptoPP::ModPowerOf2<unsigned __int64,unsigned int>(&a, &b);
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v21 + 176i64))(v21);
  v13 = (__int64)v6;
  v12 = v6;
  if ( v9 )
  {
    if ( v23 + v9 < b )
    {
      LODWORD(v7) = v9 + (_DWORD)v12;
      qmemcpy(&v12[v9], v22, v23);
      return v7;
    }
    qmemcpy(&v12[v9], v22, b - v9);
    CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::HashBlock(
      v21,
      v13);
    v22 += b - v9;
    v23 -= b - v9;
  }
  LODWORD(v7) = b;
  if ( v23 < b )
  {
LABEL_19:
    qmemcpy(v12, v22, v23);
    return v7;
  }
  if ( v22 != v12 )
  {
    if ( (unsigned __int8)CryptoPP::IsAligned<unsigned __int64>(v22, 0i64) )
    {
      LODWORD(v7) = (*(int (__fastcall **)(__int64, const void *, unsigned __int64))(*(_QWORD *)v21 + 168i64))(
                      v21,
                      v22,
                      v23);
      v15 = v7;
      v22 += v23 - v7;
      v23 = v7;
    }
    else
    {
      do
      {
        qmemcpy(v12, v22, b);
        CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::HashBlock(
          v21,
          v13);
        v22 += b;
        v23 -= b;
        LODWORD(v7) = b;
      }
      while ( v23 >= b );
    }
    goto LABEL_19;
  }
  if ( v23 != b )
    _wassert(L"len == blockSize", L"d:\\rf project\\rf_server64\\28 crypto++\\iterhash.cpp", 0x32u);
  LODWORD(v7) = CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::HashBlock(
                  v21,
                  v13);
  return v7;
}
