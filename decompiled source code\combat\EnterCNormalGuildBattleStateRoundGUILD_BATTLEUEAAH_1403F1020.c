/*
 * Function: ?Enter@CNormalGuildBattleStateRound@GUILD_BATTLE@@UEAAHPEAVCGuildBattle@2@@Z
 * Address: 0x1403F1020
 */

int __fastcall GUILD_BATTLE::CNormalGuildBattleStateRound::Enter(GUILD_BATTLE::CNormalGuildBattleStateRound *this, GUILD_BATTLE::CGuildBattle *pkBattle)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CGuildBattle *v6; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleStateRound *v7; // [sp+40h] [bp+8h]@1
  GUILD_BATTLE::CGuildBattle *v8; // [sp+48h] [bp+10h]@1

  v8 = pkBattle;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( ((int (__fastcall *)(GUILD_BATTLE::CGuildBattle *))pkBattle->vfptr->GetObjType)(pkBattle) )
  {
    result = 0;
  }
  else
  {
    v6 = v8;
    result = ((int (__fastcall *)(GUILD_BATTLE::CNormalGuildBattleStateRound *, GUILD_BATTLE::CGuildBattle *))v7->vfptr[1].Enter)(
               v7,
               v8);
  }
  return result;
}
