/*
 * Function: j_??0?$_Ranit@PEAVCUnmannedTraderClassInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140004246
 */

void __fastcall std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>(std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &> *this, std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &> *__that)
{
  std::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>::_Ranit<CUnmannedTraderClassInfo *,__int64,CUnmannedTraderClassInfo * const *,CUnmannedTraderClassInfo * const &>(
    this,
    __that);
}
