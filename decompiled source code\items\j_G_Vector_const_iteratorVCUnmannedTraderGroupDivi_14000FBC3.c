/*
 * Function: j_??G?$_Vector_const_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x14000FBC3
 */

__int64 __fastcall std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator-(std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *_Right)
{
  return std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator-(
           this,
           _Right);
}
