/*
 * Function: ??1?$_Bidit@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14021B490
 */

void __fastcall std::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>::~_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>(std::_Bidit<std::pair<int const ,CNationSettingFactory *>,__int64,std::pair<int const ,CNationSettingFactory *> const *,std::pair<int const ,CNationSettingFactory *> const &> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Bidit<std::pair<int const ,CNationSettingFactory *>,__int64,std::pair<int const ,CNationSettingFactory *> const *,std::pair<int const ,CNationSettingFactory *> const &> *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Iterator_base::~_Iterator_base((std::_Iterator_base *)&v4->_Mycont);
}
