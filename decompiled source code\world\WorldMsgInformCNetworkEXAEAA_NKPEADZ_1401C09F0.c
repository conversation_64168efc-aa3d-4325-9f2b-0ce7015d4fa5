/*
 * Function: ?WorldMsgInform@CNetworkEX@@AEAA_NKPEAD@Z
 * Address: 0x1401C09F0
 */

char __fastcall CNetworkEX::WorldMsgInform(CNetworkEX *this, unsigned int n, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-578h]@1
  char *v7; // [sp+20h] [bp-558h]@4
  char Dst[1312]; // [sp+40h] [bp-538h]@6
  unsigned __int64 v9; // [sp+560h] [bp-18h]@4

  v3 = &v6;
  for ( i = 348i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  v7 = pMsg;
  if ( (signed int)*(_WORD *)pMsg < 1280 )
  {
    memcpy_0(Dst, v7 + 2, *(_WORD *)v7);
    Dst[*(_WORD *)v7] = 0;
    CMainThread::pc_AllUserMsgInform(&g_Main, Dst);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
