/*
 * Function: ?GetMasteryCumAfterAttack@CPlayer@@QEAAHH@Z
 * Address: 0x140063800
 */

__int64 __fastcall CPlayer::GetMasteryCumAfterAttack(CPlayer *this, int nDstLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  int v7; // [sp+24h] [bp-14h]@8
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1

  v9 = nDstLv;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = nDstLv - ((int (__fastcall *)(CPlayer *))v8->vfptr->GetLevel)(v8);
  if ( v6 <= 5 )
  {
    if ( v6 <= 0 )
      v6 = 1;
  }
  else
  {
    v6 = 5;
  }
  v7 = v9 / 10;
  if ( v9 % 10 )
    ++v7;
  if ( v6 > v7 )
    v6 = v7;
  return (unsigned int)v6;
}
