/*
 * Function: ??0HashTransformation@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x1404582E0
 */

void __fastcall CryptoPP::HashTransformation::HashTransformation(CryptoPP::HashTransformation *this, CryptoPP::HashTransformation *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CryptoPP::HashTransformation *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CryptoPP::Algorithm::Algorithm((CryptoPP::Algorithm *)&v5->vfptr, (CryptoPP::Algorithm *)&__that->vfptr);
}
