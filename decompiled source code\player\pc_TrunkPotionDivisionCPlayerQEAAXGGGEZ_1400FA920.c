/*
 * Function: ?pc_TrunkPotionDivision@CPlayer@@QEAAXGGGE@Z
 * Address: 0x1400FA920
 */

void __fastcall CPlayer::pc_TrunkPotionDivision(CPlayer *this, unsigned __int16 wStartSerial, unsigned __int16 wTarSerial, unsigned __int16 wMoveAmount, char byStorageIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-58h]@1
  bool bUpdate[2]; // [sp+20h] [bp-38h]@26
  bool bSend[4]; // [sp+28h] [bp-30h]@26
  char v10; // [sp+30h] [bp-28h]@4
  _STORAGE_LIST::_db_con *v11; // [sp+38h] [bp-20h]@4
  _STORAGE_LIST::_db_con *v12; // [sp+40h] [bp-18h]@4
  unsigned __int16 v13; // [sp+48h] [bp-10h]@26
  unsigned __int16 v14; // [sp+4Ch] [bp-Ch]@26
  CPlayer *p; // [sp+60h] [bp+8h]@1
  unsigned __int16 v16; // [sp+68h] [bp+10h]@1
  unsigned __int16 v17; // [sp+70h] [bp+18h]@1
  unsigned __int16 v18; // [sp+78h] [bp+20h]@1

  v18 = wMoveAmount;
  v17 = wTarSerial;
  v16 = wStartSerial;
  p = this;
  v5 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = 0;
  v11 = 0i64;
  v12 = 0i64;
  if ( IsBeNearStore(p, 10) )
  {
    if ( p->m_Param.m_bTrunkOpen )
    {
      v11 = _STORAGE_LIST::GetPtrFromSerial(p->m_Param.m_pStoragePtr[(unsigned __int8)byStorageIndex], v16);
      if ( v11 )
      {
        if ( v11->m_bLock )
        {
          v10 = -11;
        }
        else
        {
          v12 = _STORAGE_LIST::GetPtrFromSerial(p->m_Param.m_pStoragePtr[(unsigned __int8)byStorageIndex], v17);
          if ( v12 )
          {
            if ( v12->m_bLock )
            {
              v10 = -11;
            }
            else if ( v11->m_byTableCode == 13 && v12->m_byTableCode == 13 )
            {
              if ( v11->m_wItemIndex == v12->m_wItemIndex )
              {
                if ( v11->m_dwDur >= v18 )
                {
                  if ( v12->m_dwDur + v18 > 0x63 )
                    v10 = -4;
                }
                else
                {
                  v10 = -4;
                }
              }
              else
              {
                v10 = -6;
              }
            }
            else
            {
              v10 = -3;
            }
          }
          else
          {
            v10 = -5;
          }
        }
      }
      else
      {
        v10 = -5;
      }
    }
    else
    {
      v10 = 14;
    }
  }
  else
  {
    v10 = 13;
  }
  if ( !v10 )
  {
    bSend[0] = 0;
    bUpdate[0] = 0;
    v13 = CPlayer::Emb_AlterDurPoint(p, byStorageIndex, v11->m_byStorageIndex, -v18, 0, 0);
    bSend[0] = 0;
    bUpdate[0] = 0;
    v14 = CPlayer::Emb_AlterDurPoint(p, byStorageIndex, v12->m_byStorageIndex, v18, 0, 0);
  }
  CPlayer::SendMsg_TrunkPotionDivision(p, v16, v13, v17, v14, v10);
}
