/*
 * Function: ?GetMeleeSkillIndex@CAttack@@SAHH@Z
 * Address: 0x14016D880
 */

signed __int64 __fastcall CAttack::GetMeleeSkillIndex(int nMeleeTechCode)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int n; // [sp+24h] [bp-14h]@4
  _base_fld *v7; // [sp+28h] [bp-10h]@7
  int v8; // [sp+40h] [bp+8h]@1

  v8 = nMeleeTechCode;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CRecordData::GetRecordNum(CAttack::s_pSkillData);
  for ( n = 0; n < v5; ++n )
  {
    v7 = CRecordData::GetRecord(CAttack::s_pSkillData, n);
    if ( !v7[1].m_dwIndex && *(_DWORD *)&v7[16].m_strCode[52] == v8 )
      return (unsigned int)n;
  }
  return 0xFFFFFFFFi64;
}
