/*
 * Function: ?Doit@CandidateRegister@@UEAAHW4Cmd@@PEAVCPlayer@@PEAD@Z
 * Address: 0x1402B68F0
 */

__int64 __fastcall CandidateRegister::Doit(CandidateRegister *this, Cmd eCmd, CPlayer *pOne, char *pdata)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@17
  __int64 v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+20h] [bp-28h]@4
  char *v10; // [sp+28h] [bp-20h]@10
  Cmd v11; // [sp+30h] [bp-18h]@4
  CandidateRegister *v12; // [sp+50h] [bp+8h]@1
  CPlayer *pOnea; // [sp+60h] [bp+18h]@1

  pOnea = pOne;
  v12 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  v11 = eCmd;
  if ( eCmd )
  {
    switch ( v11 )
    {
      case 1:
        v10 = pdata;
        CandidateRegister::_UpdatePacketWin(v12, *pdata, pdata + 8, *((_DWORD *)pdata + 1));
        break;
      case 2:
        if ( v12->_bInitCandidate )
        {
          v6 = CPlayerDB::GetRaceCode(&pOne->m_Param);
          v9 = CandidateRegister::_SendList(v12, pOnea->m_ObjID.m_wIndex, v6);
        }
        else
        {
          v9 = 2;
        }
        break;
      case 3:
        if ( v12->_bInitCandidate )
          v9 = CandidateRegister::_Regist(v12, pOne, pdata);
        else
          v9 = 2;
        break;
      default:
        v9 = 255;
        break;
    }
  }
  else
  {
    CandidateRegister::_InitCandidate(v12);
  }
  return (unsigned int)v9;
}
