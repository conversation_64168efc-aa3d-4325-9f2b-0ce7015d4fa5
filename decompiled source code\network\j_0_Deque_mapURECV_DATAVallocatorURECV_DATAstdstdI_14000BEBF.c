/*
 * Function: j_??0?$_Deque_map@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@IEAA@V?$allocator@URECV_DATA@@@1@@Z
 * Address: 0x14000BEBF
 */

void __fastcall std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>(std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA> > *this, std::allocator<RECV_DATA> _Al)
{
  std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>(this, _Al);
}
