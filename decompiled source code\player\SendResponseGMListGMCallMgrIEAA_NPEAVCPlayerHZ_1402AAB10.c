/*
 * Function: ?SendResponseGMList@GMCallMgr@@IEAA_NPEAVCPlayer@@H@Z
 * Address: 0x1402AAB10
 */

char __usercall GMCallMgr::SendResponseGMList@<al>(GMCallMgr *this@<rcx>, CPlayer *pOne@<rdx>, int nCurrPageIndex@<r8d>, signed __int64 a4@<rax>)
{
  void *v4; // rsp@1
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp-20h] [bp-2958h]@1
  GMRequestData *v9; // [sp+10h] [bp-2928h]@6
  unsigned int pdwList[2533]; // [sp+30h] [bp-2908h]@6
  int v11; // [sp+27C4h] [bp-174h]@6
  int v12; // [sp+27C8h] [bp-170h]@6
  int v13; // [sp+27CCh] [bp-16Ch]@10
  int v14; // [sp+27D0h] [bp-168h]@10
  int v15; // [sp+27D4h] [bp-164h]@10
  int v16; // [sp+27D8h] [bp-160h]@10
  _gm_msg_gmcall_list_response_zocl v17; // [sp+27F0h] [bp-148h]@10
  _gm_msg_gmcall_list_response_zocl::_call_node *v18; // [sp+28E8h] [bp-50h]@14
  char pbyType; // [sp+28F4h] [bp-44h]@24
  char v20; // [sp+28F5h] [bp-43h]@24
  int v21; // [sp+2910h] [bp-28h]@16
  int v22; // [sp+2914h] [bp-24h]@19
  int v23; // [sp+2918h] [bp-20h]@22
  unsigned __int64 v24; // [sp+2920h] [bp-18h]@4
  GMCallMgr *v25; // [sp+2940h] [bp+8h]@1
  CPlayer *v26; // [sp+2948h] [bp+10h]@1
  int v27; // [sp+2950h] [bp+18h]@1

  v27 = nCurrPageIndex;
  v26 = pOne;
  v25 = this;
  v4 = alloca(a4);
  v5 = &v8;
  for ( i = 2644i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v24 = (unsigned __int64)&v8 ^ _security_cookie;
  if ( pOne )
  {
    v9 = 0i64;
    v11 = CNetIndexList::CopyIndexList(&v25->m_listGMRequestDataTask, pdwList, 2532);
    v12 = (v11 - 1) / 10 + 1;
    if ( v27 <= 0 )
      v27 = 0;
    if ( v27 >= v12 )
      v27 = v12 - 1;
    v13 = 10 * v27;
    v14 = 10 * v27 + 9;
    v16 = 0;
    v15 = 0;
    _gm_msg_gmcall_list_response_zocl::Init(&v17);
    if ( v11 > 0 )
    {
      v16 = v13;
      while ( v16 <= v14 && v16 < v11 )
      {
        v9 = &v25->m_buffGMRequestData[pdwList[v16]];
        v18 = &v17.m_arCallNode[v15];
        v17.m_arCallNode[v15].dwSerial = v9->m_dwPlayerSerial;
        sprintf(v18->m_wszUserName, "%s", v9->m_wszUserName);
        ++v16;
        ++v15;
      }
    }
    if ( v11 > 0 )
      v21 = v15;
    else
      v21 = 0;
    v17.m_nCurrPageUserNum = v21;
    if ( v11 > 0 )
      v22 = v27;
    else
      v22 = 0;
    v17.m_nCurrPageIndex = v22;
    if ( v11 > 0 )
      v23 = v12;
    else
      v23 = 0;
    v17.m_MaxPage = v23;
    v17.m_nMaxUser = v11;
    pbyType = 55;
    v20 = 5;
    CNetProcess::LoadSendMsg(unk_1414F2088, v26->m_ObjID.m_wIndex, &pbyType, (char *)&v17, 0xE2u);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
