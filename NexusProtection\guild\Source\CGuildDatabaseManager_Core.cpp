/**
 * @file CGuildDatabaseManager_Core.cpp
 * @brief Modern C++20 Guild Database Manager core implementation
 * 
 * This file provides the core implementation of the CGuildDatabaseManager class
 * with comprehensive database operations, transaction management, and error handling.
 */

#include "../Headers/CGuildDatabaseManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>
#include <thread>

// Legacy includes for compatibility
extern "C" {
    class CMainThread {
    public:
        CRFWorldDatabase* m_pWorldDB;
    };
    
    class CRFWorldDatabase {
    public:
        static bool Insert_Guild(CRFWorldDatabase* db, const char* guildName, uint8_t race);
        static bool Select_GuildSerial(CRFWorldDatabase* db, const char* guildName, uint32_t* guildSerial);
        static bool Insert_WeeklyGuildPvpPointSum(CRFWorldDatabase* db, uint32_t guildSerial);
        static bool Update_UserGuildData(CRFWorldDatabase* db, uint32_t userSerial, uint32_t guildSerial, uint8_t grade);
        static bool Delete_Guild(CRFWorldDatabase* db, uint32_t guildSerial);
    };
    
    class CCheckSumGuildData {
    public:
        CCheckSumGuildData(uint32_t guildSerial);
        ~CCheckSumGuildData();
        void Encode(double param1, double param2);
        bool Insert(CRFWorldDatabase* db);
    };
    
    extern CMainThread* g_pMainThread;
}

namespace NexusProtection::Guild {

CGuildDatabaseManager& CGuildDatabaseManager::GetInstance() {
    static CGuildDatabaseManager instance;
    return instance;
}

GuildDatabaseResult CGuildDatabaseManager::Initialize(const GuildDatabaseConfig& config) {
    try {
        if (m_isInitialized.load()) {
            return GuildDatabaseResult::Success;
        }
        
        // Validate configuration
        if (!config.IsValid()) {
            LogDatabaseError(GuildDatabaseOperation::Insert, "Invalid configuration provided");
            return GuildDatabaseResult::InvalidParameters;
        }
        
        // Store configuration
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }
        
        // Validate database connection
        if (!ValidateDatabaseConnection()) {
            LogDatabaseError(GuildDatabaseOperation::Insert, "Database connection validation failed");
            return GuildDatabaseResult::DatabaseError;
        }
        
        // Reset statistics
        ResetStatistics();
        
        m_isInitialized.store(true);
        m_isShutdown.store(false);
        
        std::cout << "[INFO] Guild Database Manager initialized successfully" << std::endl;
        std::cout << "[INFO] Configuration: transactions=" << (config.enableTransactions ? "enabled" : "disabled")
                  << ", checksums=" << (config.enableChecksumValidation ? "enabled" : "disabled")
                  << ", maxRetries=" << config.maxRetryAttempts << std::endl;
        
        return GuildDatabaseResult::Success;
        
    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Insert, std::string("Exception in Initialize: ") + e.what());
        return GuildDatabaseResult::SystemError;
    }
}

GuildDatabaseResult CGuildDatabaseManager::Shutdown() {
    try {
        if (!m_isInitialized.load() || m_isShutdown.load()) {
            return GuildDatabaseResult::Success;
        }
        
        // Clear callbacks
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            m_guildCreatedCallback = nullptr;
            m_guildDeletedCallback = nullptr;
            m_guildUpdatedCallback = nullptr;
            m_databaseErrorCallback = nullptr;
            m_transactionCallback = nullptr;
        }
        
        m_isShutdown.store(true);
        m_isInitialized.store(false);
        
        std::cout << "[INFO] Guild Database Manager shutdown completed" << std::endl;
        
        return GuildDatabaseResult::Success;
        
    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Insert, std::string("Exception in Shutdown: ") + e.what());
        return GuildDatabaseResult::SystemError;
    }
}

GuildDatabaseResult CGuildDatabaseManager::InsertGuild(const GuildCreationData& creationData, uint32_t& guildSerial) {
    try {
        if (!m_isInitialized.load()) {
            return GuildDatabaseResult::SystemError;
        }
        
        // Validate input data
        if (!creationData.IsValid()) {
            LogDatabaseError(GuildDatabaseOperation::Insert, "Invalid guild creation data");
            UpdateStatistics(GuildDatabaseOperation::Insert, false);
            return GuildDatabaseResult::InvalidParameters;
        }
        
        // Check if guild name already exists
        if (GuildExists(creationData.guildName)) {
            LogDatabaseError(GuildDatabaseOperation::Insert, "Guild name already exists: " + creationData.guildName);
            UpdateStatistics(GuildDatabaseOperation::Insert, false);
            return GuildDatabaseResult::DuplicateGuild;
        }
        
        std::lock_guard<std::mutex> lock(m_databaseMutex);
        
        // Execute guild insertion with retry logic
        bool success = ExecuteWithRetry([&]() -> bool {
            // Step 1: Insert guild into database
            if (!CRFWorldDatabase::Insert_Guild(g_pMainThread->m_pWorldDB, creationData.guildName.c_str(), creationData.race)) {
                LogDatabaseError(GuildDatabaseOperation::Insert, "Failed to insert guild: " + creationData.guildName);
                return false;
            }
            
            // Step 2: Get the generated guild serial
            if (!CRFWorldDatabase::Select_GuildSerial(g_pMainThread->m_pWorldDB, creationData.guildName.c_str(), &guildSerial)) {
                LogDatabaseError(GuildDatabaseOperation::Select, "Failed to retrieve guild serial for: " + creationData.guildName);
                return false;
            }
            
            // Step 3: Create and insert checksum data
            if (m_config.enableChecksumValidation) {
                if (!CreateChecksumData(guildSerial)) {
                    LogDatabaseError(GuildDatabaseOperation::Insert, "Failed to create checksum data for guild: " + std::to_string(guildSerial));
                    return false;
                }
            }
            
            // Step 4: Insert weekly PvP point sum
            if (!CRFWorldDatabase::Insert_WeeklyGuildPvpPointSum(g_pMainThread->m_pWorldDB, guildSerial)) {
                LogDatabaseError(GuildDatabaseOperation::Insert, "Failed to insert weekly PvP points for guild: " + std::to_string(guildSerial));
                return false;
            }
            
            // Step 5: Update member guild data
            if (!UpdateMemberGuildData(creationData.memberSerials, guildSerial)) {
                LogDatabaseError(GuildDatabaseOperation::Update, "Failed to update member guild data for guild: " + std::to_string(guildSerial));
                return false;
            }
            
            return true;
        }, GuildDatabaseOperation::Insert);
        
        if (success) {
            UpdateStatistics(GuildDatabaseOperation::Insert, true);
            NotifyGuildCreated(guildSerial, creationData.guildName);
            NotifyTransaction(true, "Guild Creation");
            
            std::cout << "[INFO] Successfully created guild: " << creationData.guildName 
                      << " (Serial: " << guildSerial << ")" << std::endl;
            
            return GuildDatabaseResult::Success;
        } else {
            UpdateStatistics(GuildDatabaseOperation::Insert, false);
            NotifyTransaction(false, "Guild Creation");
            return GuildDatabaseResult::DatabaseError;
        }
        
    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Insert, std::string("Exception in InsertGuild: ") + e.what());
        UpdateStatistics(GuildDatabaseOperation::Insert, false);
        return GuildDatabaseResult::SystemError;
    }
}

GuildDatabaseResult CGuildDatabaseManager::DeleteGuild(uint32_t guildSerial) {
    try {
        if (!m_isInitialized.load()) {
            return GuildDatabaseResult::SystemError;
        }
        
        if (guildSerial == 0) {
            LogDatabaseError(GuildDatabaseOperation::Delete, "Invalid guild serial: 0");
            UpdateStatistics(GuildDatabaseOperation::Delete, false);
            return GuildDatabaseResult::InvalidParameters;
        }
        
        std::lock_guard<std::mutex> lock(m_databaseMutex);
        
        // Execute guild deletion with retry logic
        bool success = ExecuteWithRetry([&]() -> bool {
            return CRFWorldDatabase::Delete_Guild(g_pMainThread->m_pWorldDB, guildSerial);
        }, GuildDatabaseOperation::Delete);
        
        if (success) {
            UpdateStatistics(GuildDatabaseOperation::Delete, true);
            NotifyGuildDeleted(guildSerial);
            NotifyTransaction(true, "Guild Deletion");
            
            std::cout << "[INFO] Successfully deleted guild: " << guildSerial << std::endl;
            
            return GuildDatabaseResult::Success;
        } else {
            UpdateStatistics(GuildDatabaseOperation::Delete, false);
            NotifyTransaction(false, "Guild Deletion");
            LogDatabaseError(GuildDatabaseOperation::Delete, "Failed to delete guild: " + std::to_string(guildSerial));
            return GuildDatabaseResult::DatabaseError;
        }
        
    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Delete, std::string("Exception in DeleteGuild: ") + e.what());
        UpdateStatistics(GuildDatabaseOperation::Delete, false);
        return GuildDatabaseResult::SystemError;
    }
}

uint8_t CGuildDatabaseManager::db_Insert_guild(uint32_t* memberSerials, const char* guildName, uint8_t race, uint32_t* guildSerial) {
    try {
        // Convert to modern data structure
        GuildCreationData creationData;
        creationData.guildName = guildName ? guildName : "";
        creationData.race = race;
        
        // Convert member serials array to vector
        if (memberSerials) {
            for (int i = 0; i < 8 && memberSerials[i] != static_cast<uint32_t>(-1); ++i) {
                creationData.memberSerials.push_back(memberSerials[i]);
            }
            if (!creationData.memberSerials.empty()) {
                creationData.masterSerial = creationData.memberSerials[0]; // First member is master
            }
        }
        
        uint32_t newGuildSerial = 0;
        auto result = InsertGuild(creationData, newGuildSerial);
        
        if (guildSerial) {
            *guildSerial = newGuildSerial;
        }
        
        // Convert to legacy result code
        return (result == GuildDatabaseResult::Success) ? 0 : static_cast<uint8_t>(result);
        
    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Insert, std::string("Exception in db_Insert_guild: ") + e.what());
        return static_cast<uint8_t>(GuildDatabaseResult::DatabaseError);
    }
}

uint8_t CGuildDatabaseManager::db_disjoint_guild(uint32_t guildSerial) {
    try {
        auto result = DeleteGuild(guildSerial);
        
        // Convert to legacy result code
        return (result == GuildDatabaseResult::Success) ? 0 : static_cast<uint8_t>(result);
        
    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Delete, std::string("Exception in db_disjoint_guild: ") + e.what());
        return static_cast<uint8_t>(GuildDatabaseResult::DatabaseError);
    }
}

bool CGuildDatabaseManager::ExecuteWithRetry(std::function<bool()> operation, GuildDatabaseOperation operationType) {
    try {
        uint32_t attempts = 0;

        while (attempts < m_config.maxRetryAttempts) {
            if (operation()) {
                if (attempts > 0) {
                    std::cout << "[INFO] Operation succeeded after " << attempts << " retries" << std::endl;
                }
                return true;
            }

            attempts++;
            if (attempts < m_config.maxRetryAttempts) {
                std::cout << "[WARN] Operation failed, retrying in " << m_config.retryDelay.count() << "ms (attempt " << attempts << "/" << m_config.maxRetryAttempts << ")" << std::endl;
                std::this_thread::sleep_for(m_config.retryDelay);
            }
        }

        LogDatabaseError(operationType, "Operation failed after " + std::to_string(m_config.maxRetryAttempts) + " attempts");
        return false;

    } catch (const std::exception& e) {
        LogDatabaseError(operationType, std::string("Exception in ExecuteWithRetry: ") + e.what());
        return false;
    }
}

bool CGuildDatabaseManager::CreateChecksumData(uint32_t guildSerial) {
    try {
        CCheckSumGuildData checksumData(guildSerial);
        checksumData.Encode(0.0, 0.0);

        bool success = checksumData.Insert(g_pMainThread->m_pWorldDB);
        if (!success) {
            m_stats.checksumErrors.fetch_add(1);
            LogDatabaseError(GuildDatabaseOperation::Insert, "Failed to insert checksum data for guild: " + std::to_string(guildSerial));
        }

        return success;

    } catch (const std::exception& e) {
        m_stats.checksumErrors.fetch_add(1);
        LogDatabaseError(GuildDatabaseOperation::Insert, std::string("Exception in CreateChecksumData: ") + e.what());
        return false;
    }
}

bool CGuildDatabaseManager::UpdateMemberGuildData(const std::vector<uint32_t>& memberSerials, uint32_t guildSerial) {
    try {
        for (uint32_t memberSerial : memberSerials) {
            if (memberSerial == static_cast<uint32_t>(-1)) {
                break; // End of valid serials
            }

            if (!CRFWorldDatabase::Update_UserGuildData(g_pMainThread->m_pWorldDB, memberSerial, guildSerial, 0)) {
                LogDatabaseError(GuildDatabaseOperation::Update, "Failed to update guild data for member: " + std::to_string(memberSerial));
                return false;
            }
        }

        return true;

    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Update, std::string("Exception in UpdateMemberGuildData: ") + e.what());
        return false;
    }
}

bool CGuildDatabaseManager::ValidateDatabaseConnection() const {
    try {
        if (!g_pMainThread || !g_pMainThread->m_pWorldDB) {
            return false;
        }

        // Additional validation could be added here
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ValidateDatabaseConnection: " << e.what() << std::endl;
        return false;
    }
}

void CGuildDatabaseManager::LogDatabaseError(GuildDatabaseOperation operation, const std::string& error) {
    try {
        if (!m_config.enableErrorLogging) {
            return;
        }

        std::string operationName;
        switch (operation) {
            case GuildDatabaseOperation::Insert: operationName = "INSERT"; break;
            case GuildDatabaseOperation::Update: operationName = "UPDATE"; break;
            case GuildDatabaseOperation::Delete: operationName = "DELETE"; break;
            case GuildDatabaseOperation::Select: operationName = "SELECT"; break;
            case GuildDatabaseOperation::Disjoint: operationName = "DISJOINT"; break;
            default: operationName = "UNKNOWN"; break;
        }

        std::cerr << "[ERROR] Guild Database " << operationName << ": " << error << std::endl;

        // Track consecutive errors
        m_consecutiveErrors.fetch_add(1);
        m_lastErrorTime = std::chrono::steady_clock::now();

        // Notify error callback
        NotifyDatabaseError(operation, error);

    } catch (const std::exception& e) {
        std::cerr << "[CRITICAL] Exception in LogDatabaseError: " << e.what() << std::endl;
    }
}

void CGuildDatabaseManager::UpdateStatistics(GuildDatabaseOperation operation, bool success) {
    try {
        if (!m_config.enableStatistics) {
            return;
        }

        switch (operation) {
            case GuildDatabaseOperation::Insert:
                m_stats.totalInsertions.fetch_add(1);
                break;
            case GuildDatabaseOperation::Update:
                m_stats.totalUpdates.fetch_add(1);
                break;
            case GuildDatabaseOperation::Delete:
            case GuildDatabaseOperation::Disjoint:
                m_stats.totalDeletions.fetch_add(1);
                break;
            case GuildDatabaseOperation::Select:
                m_stats.totalSelections.fetch_add(1);
                break;
        }

        if (success) {
            m_stats.successfulOperations.fetch_add(1);
            m_consecutiveErrors.store(0); // Reset consecutive errors on success
        } else {
            m_stats.failedOperations.fetch_add(1);
        }

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UpdateStatistics: " << e.what() << std::endl;
    }
}

GuildDatabaseStats CGuildDatabaseManager::GetStatistics() const {
    // Convert atomic stats to copyable stats
    GuildDatabaseStats stats;
    stats.totalInsertions = m_stats.totalInsertions.load();
    stats.totalDeletions = m_stats.totalDeletions.load();
    stats.totalUpdates = m_stats.totalUpdates.load();
    stats.totalSelections = m_stats.totalSelections.load();
    stats.successfulOperations = m_stats.successfulOperations.load();
    stats.failedOperations = m_stats.failedOperations.load();
    stats.transactionRollbacks = m_stats.transactionRollbacks.load();
    stats.checksumErrors = m_stats.checksumErrors.load();
    stats.startTime = m_stats.startTime;
    return stats;
}

GuildDatabaseConfig CGuildDatabaseManager::GetConfiguration() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_config;
}

bool CGuildDatabaseManager::UpdateConfiguration(const GuildDatabaseConfig& config) {
    try {
        if (!config.IsValid()) {
            LogDatabaseError(GuildDatabaseOperation::Update, "Invalid configuration provided");
            return false;
        }

        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }

        std::cout << "[INFO] Guild database configuration updated" << std::endl;
        return true;

    } catch (const std::exception& e) {
        LogDatabaseError(GuildDatabaseOperation::Update, std::string("Exception in UpdateConfiguration: ") + e.what());
        return false;
    }
}

void CGuildDatabaseManager::ResetStatistics() {
    try {
        m_stats.totalInsertions.store(0);
        m_stats.totalDeletions.store(0);
        m_stats.totalUpdates.store(0);
        m_stats.totalSelections.store(0);
        m_stats.successfulOperations.store(0);
        m_stats.failedOperations.store(0);
        m_stats.transactionRollbacks.store(0);
        m_stats.checksumErrors.store(0);
        m_stats.startTime = std::chrono::steady_clock::now();

        m_consecutiveErrors.store(0);
        m_lastErrorTime = std::chrono::steady_clock::now();

        std::cout << "[INFO] Guild database statistics reset" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ResetStatistics: " << e.what() << std::endl;
    }
}

bool CGuildDatabaseManager::ValidateGuildName(const std::string& guildName) const {
    try {
        // Check length
        if (guildName.empty() || guildName.length() > 50) {
            return false;
        }

        // Check for invalid characters (basic validation)
        for (char c : guildName) {
            if (c < 32 || c > 126) { // Printable ASCII range
                return false;
            }
        }

        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ValidateGuildName: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildDatabaseManager::GuildExists(const std::string& guildName) const {
    try {
        uint32_t guildSerial = 0;
        return GetGuildSerial(guildName, guildSerial);

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in GuildExists: " << e.what() << std::endl;
        return false;
    }
}

bool CGuildDatabaseManager::GetGuildSerial(const std::string& guildName, uint32_t& guildSerial) const {
    try {
        if (!ValidateDatabaseConnection()) {
            return false;
        }

        return CRFWorldDatabase::Select_GuildSerial(g_pMainThread->m_pWorldDB, guildName.c_str(), &guildSerial);

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in GetGuildSerial: " << e.what() << std::endl;
        return false;
    }
}

} // namespace NexusProtection::Guild
