/*
 * Function: ?Check_Base_EquipItem@CSetItemEffect@@AEAAEPEAU_AVATOR_DATA@@PEAU_SetItemEff_fld@@@Z
 * Address: 0x1402E25B0
 */

char __fastcall CSetItemEffect::Check_Base_EquipItem(CSetItemEffect *this, _AVATOR_DATA *pData, _SetItemEff_fld *pSetFld)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char v7; // [sp+20h] [bp-68h]@4
  int j; // [sp+24h] [bp-64h]@4
  int n; // [sp+28h] [bp-60h]@4
  char Dest; // [sp+38h] [bp-50h]@4
  char v11; // [sp+39h] [bp-4Fh]@4
  _base_fld *v12; // [sp+58h] [bp-30h]@4
  int v13; // [sp+68h] [bp-20h]@8
  unsigned __int64 v14; // [sp+70h] [bp-18h]@4
  _AVATOR_DATA *v15; // [sp+98h] [bp+10h]@1
  _SetItemEff_fld *v16; // [sp+A0h] [bp+18h]@1

  v16 = pSetFld;
  v15 = pData;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v6 ^ _security_cookie;
  v7 = 0;
  n = 0;
  Dest = 0;
  memset(&v11, 0, 9ui64);
  v12 = 0i64;
  for ( j = 0; j < 8; ++j )
  {
    n = v15->dbAvator.m_EquipKey[j].zItemIndex;
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, n);
    if ( v12 )
    {
      strcpy_0(&Dest, v12->m_strCode);
      v13 = j;
      switch ( j )
      {
        case 0:
          if ( !strcmp_0(&Dest, v16->m_strset_upper) )
            ++v7;
          break;
        case 1:
          if ( !strcmp_0(&Dest, v16->m_strset_lower) )
            ++v7;
          break;
        case 2:
          if ( !strcmp_0(&Dest, v16->m_strset_gauntlet) )
            ++v7;
          break;
        case 3:
          if ( !strcmp_0(&Dest, v16->m_strset_shoes) )
            ++v7;
          break;
        case 4:
          if ( !strcmp_0(&Dest, v16->m_strset_head) )
            ++v7;
          break;
        case 5:
          if ( !strcmp_0(&Dest, v16->m_strset_shield) )
            ++v7;
          break;
        case 6:
          if ( !strcmp_0(&Dest, v16->m_strset_weapon) )
            ++v7;
          break;
        case 7:
          if ( !strcmp_0(&Dest, v16->m_strset_cloak) )
            ++v7;
          break;
        default:
          continue;
      }
    }
  }
  return v7;
}
