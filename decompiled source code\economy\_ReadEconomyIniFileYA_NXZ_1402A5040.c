/*
 * Function: ?_ReadEconomyIniFile@@YA_NXZ
 * Address: 0x1402A5040
 */

char __cdecl _ReadEconomyIniFile()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  LODWORD(EqSukList[15].pwszEpSuk) = GetPrivateProfileIntA(
                                       "Economy",
                                       "Default_OreVal",
                                       0,
                                       ".\\Initialize\\WorldSystem.ini");
  if ( LODWORD(EqSukList[15].pwszEpSuk) )
  {
    result = 1;
  }
  else
  {
    MyMessageBox("Economy Error", "Nothing Default MgrValue");
    result = 0;
  }
  return result;
}
