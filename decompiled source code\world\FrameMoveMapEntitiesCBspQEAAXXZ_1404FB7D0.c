/*
 * Function: ?FrameMoveMapEntities@CBsp@@QEAAXXZ
 * Address: 0x1404FB7D0
 */

void __fastcall CBsp::FrameMoveMapEntities(CBsp *this)
{
  float v1; // xmm11_4@1
  CBsp *v2; // rsi@1
  char *v3; // rcx@1
  float v4; // xmm12_4@1
  int v5; // edx@1
  float v6; // xmm13_4@1
  signed __int64 v7; // r8@2
  int v8; // er9@2
  char *v9; // rax@2
  int v10; // er14@4
  char v11; // r12@4
  signed __int64 v12; // rbp@4
  _MAP_ENTITIES_LIST *v13; // rax@6
  int v14; // edi@6
  signed __int64 v15; // r13@6
  signed __int64 v16; // r15@6
  float v17; // xmm0_4@6
  _ENTITY_LIST *v18; // rax@6
  signed __int64 v19; // r15@6
  float v20; // xmm6_4@6
  float v21; // xmm8_4@6
  float v22; // xmm7_4@8
  CParticle *v23; // rcx@13
  int v24; // [sp+F0h] [bp+8h]@2
  int v25; // [sp+F8h] [bp+10h]@1
  char *v26; // [sp+100h] [bp+18h]@2
  signed __int64 v27; // [sp+108h] [bp+20h]@2

  v1 = unk_184A79B1C;
  v2 = this;
  v3 = this->mEntityCache;
  v4 = *(float *)&qword_184A79B20;
  v5 = v2->mEntityCacheSize - 1;
  v6 = *((float *)&qword_184A79B20 + 1);
  v25 = v5;
  if ( v5 >= 0 )
  {
    v7 = 8i64 * v5;
    v8 = 8 * v5;
    v9 = &v3[v5];
    v27 = 8i64 * v5;
    v24 = 8 * v5;
    v26 = &v3[v5];
    do
    {
      if ( *v9 )
      {
        v10 = 0;
        v11 = 1;
        v12 = 0i64;
        do
        {
          if ( (unsigned __int8)v11 & (unsigned __int8)*v9 )
          {
            v13 = v2->mMapEntitiesList;
            v14 = v8 + v10;
            v15 = v7 + v12;
            v16 = v13[v15].ID;
            v17 = sqrtf_0((float)((float)((float)(v4 - v13[v15].Pos[1]) * (float)(v4 - v13[v15].Pos[1]))
                                + (float)((float)(v1 - v13[v15].Pos[0]) * (float)(v1 - v13[v15].Pos[0]))) + (float)((float)(v6 - v13[v15].Pos[2]) * (float)(v6 - v13[v15].Pos[2])));
            v18 = v2->mEntityList;
            v19 = v16;
            v20 = v17;
            v21 = v18[v19].FadeEnd;
            if ( v17 <= v21 )
            {
              v22 = v18[v19].FadeStart;
              if ( (unsigned int)IsBBoxInFrustum(v2->mMapEntitiesList[v14].BBMin, v2->mMapEntitiesList[v14].BBMax) )
              {
                if ( v17 <= v22 )
                  v20 = v22;
                v2->mMapEntitiesList[v15].Color = ((signed int)ffloor((float)(1.0
                                                                            - (float)((float)(v20 - v22)
                                                                                    / (float)(v21 - v22))) * 255.0) << 24) | 0xFFFFFF;
                if ( v2->mEntityList[v19].IsParticle )
                {
                  v23 = v2->mMapEntitiesList[v15].Particle;
                  if ( v23 )
                  {
                    if ( CParticle::Loop(v23) )
                    {
                      v9 = v26;
                      v7 = v27;
                      v8 = v24;
                    }
                    else
                    {
                      v7 = v27;
                      v8 = v24;
                      v2->mEntityCache[(signed __int64)v14 >> 3] &= ~(1 << (v14 & 7));
                      v9 = v26;
                    }
                  }
                  else
                  {
                    v7 = v27;
                    v8 = v24;
                    v2->mEntityCache[(signed __int64)v14 >> 3] &= ~(1 << (v14 & 7));
                    v9 = v26;
                  }
                }
                else
                {
                  v9 = v26;
                  v7 = v27;
                  v8 = v24;
                }
              }
              else
              {
                v7 = v27;
                v8 = v24;
                v2->mEntityCache[(signed __int64)v14 >> 3] &= ~(1 << (v14 & 7));
                v9 = v26;
              }
            }
            else
            {
              v8 = v24;
              v2->mEntityCache[(signed __int64)v14 >> 3] &= ~(1 << (v14 & 7));
              v9 = v26;
              v7 = v27;
            }
          }
          ++v12;
          ++v10;
          v11 *= 2;
        }
        while ( v12 < 8 );
        v5 = v25;
      }
      --v5;
      v8 -= 8;
      --v9;
      v7 -= 8i64;
      v25 = v5;
      v24 = v8;
      v26 = v9;
      v27 = v7;
    }
    while ( v5 >= 0 );
  }
}
