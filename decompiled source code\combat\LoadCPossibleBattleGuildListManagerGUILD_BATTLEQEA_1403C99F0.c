/*
 * Function: ?Load@CPossibleBattleGuildListManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403C99F0
 */

char __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::Load(GUILD_BATTLE::CPossibleBattleGuildListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v4; // al@10
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v6; // [sp+20h] [bp-18h]@6
  int j; // [sp+28h] [bp-10h]@6
  int k; // [sp+2Ch] [bp-Ch]@8
  GUILD_BATTLE::CPossibleBattleGuildListManager *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_bInit )
  {
    GUILD_BATTLE::CPossibleBattleGuildListManager::UpdateGuildList(v9);
    v6 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
    for ( j = 0; j < 3; ++j )
    {
      for ( k = 0; k < 75; ++k )
      {
        v4 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInxList(v6, j, v9->m_ppkList[j][k].byMapInx);
        v9->m_ppkList[j][k].byMapCnt = v4;
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
