/*
 * Function: ?SignAndRestart@TF_SignerBase@CryptoPP@@UEBA_KAEAVRandomNumberGenerator@2@AEAVPK_MessageAccumulator@2@PEAE_N@Z
 * Address: 0x140622BF0
 */

__int64 __fastcall CryptoPP::TF_SignerBase::SignAndRestart(CryptoPP::TF_SignerBase *this, struct CryptoPP::RandomNumberGenerator *a2, struct CryptoPP::PK_MessageAccumulator *a3, unsigned __int8 *a4, bool a5)
{
  __int64 *v5; // rax@1
  __int64 v6; // rax@1
  int v7; // eax@1
  unsigned __int64 v8; // rax@1
  unsigned __int64 v9; // rax@1
  unsigned __int64 v10; // rax@4
  __int64 v11; // rax@4
  __int64 v12; // rax@4
  char *v13; // rax@4
  char v14; // ST30_1@4
  char *v15; // rax@4
  __int64 v16; // rax@4
  __int64 v17; // ST50_8@4
  __int64 v18; // rax@4
  CryptoPP::Integer *v19; // rax@4
  char v21; // [sp+58h] [bp-190h]@1
  __int64 v22; // [sp+60h] [bp-188h]@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v23; // [sp+68h] [bp-180h]@4
  struct CryptoPP::PK_MessageAccumulator *v24; // [sp+80h] [bp-168h]@1
  __int64 *v25; // [sp+88h] [bp-160h]@1
  CryptoPP::Integer v26; // [sp+90h] [bp-158h]@4
  CryptoPP::PK_SignatureScheme::KeyTooShort v27; // [sp+B8h] [bp-130h]@2
  CryptoPP::Integer v28; // [sp+108h] [bp-E0h]@4
  __int64 v29; // [sp+130h] [bp-B8h]@4
  char v30; // [sp+140h] [bp-A8h]@4
  __int64 v31; // [sp+150h] [bp-98h]@1
  CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v32; // [sp+158h] [bp-90h]@1
  __int64 v33; // [sp+160h] [bp-88h]@1
  __int64 v34; // [sp+168h] [bp-80h]@1
  unsigned __int64 v35; // [sp+170h] [bp-78h]@1
  __int64 v36; // [sp+178h] [bp-70h]@4
  char *v37; // [sp+180h] [bp-68h]@4
  CryptoPP::ClonableVtbl *v38; // [sp+188h] [bp-60h]@4
  __int64 v39; // [sp+190h] [bp-58h]@4
  unsigned __int64 v40; // [sp+198h] [bp-50h]@4
  __int64 v41; // [sp+1A0h] [bp-48h]@4
  unsigned __int64 v42; // [sp+1A8h] [bp-40h]@4
  CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>Vtbl *v43; // [sp+1B0h] [bp-38h]@4
  __int64 v44; // [sp+1B8h] [bp-30h]@4
  CryptoPP::Integer *v45; // [sp+1C0h] [bp-28h]@4
  CryptoPP::Integer *v46; // [sp+1C8h] [bp-20h]@4
  CryptoPP::TF_SignerBase *v47; // [sp+1F0h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v48; // [sp+1F8h] [bp+10h]@1
  unsigned __int8 *v49; // [sp+208h] [bp+20h]@1

  v49 = a4;
  v48 = a2;
  v47 = this;
  v31 = -2i64;
  v24 = a3;
  ((void (__fastcall *)(CryptoPP::TF_SignerBase *, char *))this->vfptr[1].AllowNonrecoverablePart)(this, &v21);
  v32 = v47->vfptr;
  LODWORD(v5) = ((int (__fastcall *)(signed __int64))v32->GetMessageEncodingInterface)((signed __int64)&v47->vfptr);
  v25 = v5;
  LODWORD(v6) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v24->vfptr[9].__vecDelDtor)(v24);
  v33 = v6;
  v7 = (*(int (__fastcall **)(__int64))(*(_QWORD *)v6 + 56i64))(v6);
  v34 = *v25;
  LODWORD(v8) = (*(int (__fastcall **)(__int64 *, __int64, _QWORD))(v34 + 8))(v25, v22, (unsigned int)v7);
  v35 = v8;
  LODWORD(v9) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v47);
  if ( v9 < v35 )
  {
    CryptoPP::PK_SignatureScheme::KeyTooShort::KeyTooShort(&v27);
    CxxThrowException_0((__int64)&v27, (__int64)&TI4_AVKeyTooShort_PK_SignatureScheme_CryptoPP__);
  }
  LODWORD(v10) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeLength(v47);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v23,
    v10);
  LODWORD(v11) = CryptoPP::TF_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunctionInverse,CryptoPP::PK_SignatureMessageEncodingMethod>>::MessageRepresentativeBitLength(v47);
  v36 = v11;
  v37 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v23);
  qmemcpy(&v30, &v21, 0x10ui64);
  v38 = v24->vfptr;
  LODWORD(v12) = ((int (__fastcall *)(struct CryptoPP::PK_MessageAccumulator *))v38[9].__vecDelDtor)(v24);
  v39 = v12;
  v40 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v24[1]);
  v13 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)&v24[1]);
  v41 = *v25;
  v14 = (char)v24[23].vfptr;
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v41 + 48))(
    v25,
    v48,
    v13,
    v40);
  LOBYTE(v24[23].vfptr) = 1;
  v42 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v23);
  v15 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v23);
  CryptoPP::Integer::Integer(&v26, (const unsigned __int8 *)v15, v42, 0);
  LODWORD(v16) = (*(int (__fastcall **)(CryptoPP::TF_SignerBase *))&v47->vfptr->gap8[0])(v47);
  v17 = v16;
  v43 = v47->vfptr;
  LODWORD(v18) = ((int (__fastcall *)(signed __int64))v43->GetTrapdoorFunctionInterface)((signed __int64)&v47->vfptr);
  v44 = v18;
  LODWORD(v19) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, struct CryptoPP::RandomNumberGenerator *, CryptoPP::Integer *))(*(_QWORD *)v18 + 8i64))(
                   v18,
                   &v28,
                   v48,
                   &v26);
  v45 = v19;
  v46 = v19;
  CryptoPP::Integer::Encode(v19, v49, v17, 0);
  CryptoPP::Integer::~Integer(&v28);
  v29 = v17;
  CryptoPP::Integer::~Integer(&v26);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v23);
  return v29;
}
