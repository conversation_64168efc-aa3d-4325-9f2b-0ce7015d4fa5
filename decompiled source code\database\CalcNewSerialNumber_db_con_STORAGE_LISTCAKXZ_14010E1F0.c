/*
 * Function: ?CalcNewSerialNumber@_db_con@_STORAGE_LIST@@CAKXZ
 * Address: 0x14010E1F0
 */

__int64 __cdecl _STORAGE_LIST::_db_con::CalcNewSerialNumber()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@11
  __int64 v3; // [sp+0h] [bp-48h]@1
  int v4; // [sp+20h] [bp-28h]@4
  int v5; // [sp+24h] [bp-24h]@7
  int v6; // [sp+28h] [bp-20h]@7
  int v7; // [sp+2Ch] [bp-1Ch]@7
  int v8; // [sp+30h] [bp-18h]@10
  int v9; // [sp+34h] [bp-14h]@10
  int v10; // [sp+38h] [bp-10h]@5
  int v11; // [sp+3Ch] [bp-Ch]@8

  v0 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v4 = rand();
  if ( rand() % 2 )
    v10 = 0;
  else
    v10 = 0x8000;
  v5 = v10 | v4;
  v6 = (v10 | v4) << 16;
  v7 = rand();
  if ( rand() % 2 )
    v11 = 0;
  else
    v11 = 0x8000;
  v8 = v11 | v7;
  v9 = v11 | v7 | v6;
  if ( v9 == 1 )
    result = 0i64;
  else
    result = (unsigned int)v9;
  return result;
}
