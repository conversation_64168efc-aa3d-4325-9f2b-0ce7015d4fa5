/*
 * Function: ?CalcEquipAttackDelay@CPlayer@@QEAAHXZ
 * Address: 0x1400575C0
 */

__int64 __fastcall CPlayer::CalcEquipAttackDelay(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  unsigned int v5; // [sp+20h] [bp-48h]@4
  int j; // [sp+24h] [bp-44h]@4
  char *v7; // [sp+28h] [bp-40h]@6
  _base_fld *v8; // [sp+30h] [bp-38h]@7
  char *v9; // [sp+38h] [bp-30h]@9
  _base_fld *v10; // [sp+40h] [bp-28h]@10
  char *v11; // [sp+48h] [bp-20h]@11
  _base_fld *v12; // [sp+50h] [bp-18h]@12
  _base_fld *v13; // [sp+58h] [bp-10h]@14
  CPlayer *v14; // [sp+70h] [bp+8h]@1

  v14 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  for ( j = 0; j < 5; ++j )
  {
    v7 = &v14->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
    if ( *v7 )
    {
      v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(_WORD *)(v7 + 3));
      v5 += *(_DWORD *)&v8[5].m_strCode[20];
    }
  }
  v9 = &v14->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
  if ( *v9 )
  {
    v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v9 + 3));
    v5 += *(_DWORD *)&v10[9].m_strCode[52];
  }
  v11 = &v14->m_Param.m_dbEquip.m_pStorageList[5].m_bLoad;
  if ( *v11 )
  {
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 5, *(_WORD *)(v11 + 3));
    v5 += *(_DWORD *)&v12[5].m_strCode[20];
  }
  if ( CPlayer::IsSiegeMode(v14) )
  {
    v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v14->m_pSiegeItem->m_wItemIndex);
    v5 += *(_DWORD *)&v13[5].m_strCode[20];
  }
  return v5;
}
