/*
 * Function: ??$_Make_heap@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@0PEA_JPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405A5DC0
 */

int __fastcall std::_Make_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(__int64 a1)
{
  signed __int64 v1; // rax@1
  const struct CryptoPP::EC2NPoint *v2; // rax@3
  signed __int64 v4; // [sp+20h] [bp-E8h]@1
  __int64 v5; // [sp+28h] [bp-E0h]@1
  char v6; // [sp+30h] [bp-D8h]@3
  CryptoPP::EC2NPoint *v7; // [sp+90h] [bp-78h]@3
  char v8; // [sp+98h] [bp-70h]@3
  char v9; // [sp+B0h] [bp-58h]@3
  char *v10; // [sp+C8h] [bp-40h]@3
  __int64 v11; // [sp+D0h] [bp-38h]@1
  __int64 v12; // [sp+D8h] [bp-30h]@3
  __int64 v13; // [sp+E0h] [bp-28h]@3
  CryptoPP::EC2NPoint *v14; // [sp+E8h] [bp-20h]@3
  CryptoPP::EC2NPoint *v15; // [sp+F0h] [bp-18h]@3
  __int64 v16; // [sp+F8h] [bp-10h]@3
  __int64 v17; // [sp+110h] [bp+8h]@1

  v17 = a1;
  v11 = -2i64;
  LODWORD(v1) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-();
  v4 = v1;
  v5 = v1 / 2;
  while ( v5 > 0 )
  {
    --v5;
    v7 = (CryptoPP::EC2NPoint *)&v6;
    v10 = &v9;
    v12 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator+(
            v17,
            (__int64)&v8,
            v5);
    v13 = v12;
    LODWORD(v2) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator*();
    v14 = CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(
            v7,
            v2);
    v15 = v14;
    v16 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v10);
    std::_Adjust_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
      v16,
      v5,
      v4,
      v15);
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  }
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
}
