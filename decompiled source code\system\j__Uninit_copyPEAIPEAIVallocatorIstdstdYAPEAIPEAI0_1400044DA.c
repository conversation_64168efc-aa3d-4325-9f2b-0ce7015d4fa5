/*
 * Function: j_??$_Uninit_copy@PEAIPEAIV?$allocator@I@std@@@std@@YAPEAIPEAI00AEAV?$allocator@I@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400044DA
 */

unsigned int *__fastcall std::_Uninit_copy<unsigned int *,unsigned int *,std::allocator<unsigned int>>(unsigned int *_First, unsigned int *_Last, unsigned int *_Dest, std::allocator<unsigned int> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<unsigned int *,unsigned int *,std::allocator<unsigned int>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
