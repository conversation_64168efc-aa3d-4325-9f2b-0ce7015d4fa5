/*
 * Function: ?StartContSF@CEquipItemSFAgent@@QEAAXPEAU_sf_continous@@@Z
 * Address: 0x140121490
 */

void __fastcall CEquipItemSFAgent::StartContSF(CEquipItemSFAgent *this, _sf_continous *pSF_Cont)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char v5; // [sp+20h] [bp-48h]@5
  int n; // [sp+24h] [bp-44h]@5
  _skill_fld *pSkillFld; // [sp+28h] [bp-40h]@6
  CEquipItemSFAgent::_requireSlot pSlot; // [sp+38h] [bp-30h]@8
  int nEquipTblIndex; // [sp+44h] [bp-24h]@9
  unsigned __int64 v10; // [sp+50h] [bp-18h]@4
  CEquipItemSFAgent *v11; // [sp+70h] [bp+8h]@1
  _sf_continous *pSF; // [sp+78h] [bp+10h]@1

  pSF = pSF_Cont;
  v11 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( pSF_Cont )
  {
    v5 = pSF_Cont->m_byEffectCode;
    n = pSF_Cont->m_wEffectIndex;
    if ( !v5 )
    {
      pSkillFld = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410, n);
      if ( pSkillFld )
      {
        if ( pSkillFld->m_nClass == 2 )
        {
          CEquipItemSFAgent::_requireSlot::_requireSlot(&pSlot);
          if ( CEquipItemSFAgent::GetRequireSFSlot(v11, &pSlot, pSkillFld) )
          {
            for ( nEquipTblIndex = 0; nEquipTblIndex < 8; ++nEquipTblIndex )
            {
              if ( pSlot.m_SlotIndex[nEquipTblIndex] )
              {
                if ( !v11->m_pContSF[nEquipTblIndex] )
                  CEquipItemSFAgent::SetSFCont(v11, nEquipTblIndex, pSF);
              }
            }
          }
        }
      }
    }
  }
}
