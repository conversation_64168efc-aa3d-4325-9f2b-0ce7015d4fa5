/*
 * Function: ?GetSortType@CUnmannedTraderGroupIDInfo@@QEAAPEBVCUnmannedTraderSortType@@EE@Z
 * Address: 0x140386D80
 */

CUnmannedTraderSortType *__fastcall CUnmannedTraderGroupIDInfo::GetSortType(CUnmannedTraderGroupIDInfo *this, char byDivision, char bySortType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSortType *v5; // rax@5
  CUnmannedTraderDivisionInfo **v6; // rax@10
  __int64 v7; // [sp+0h] [bp-C8h]@1
  CUnmannedTraderSortType *v8; // [sp+20h] [bp-A8h]@8
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > result; // [sp+38h] [bp-90h]@8
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v10; // [sp+58h] [bp-70h]@12
  bool v11; // [sp+70h] [bp-58h]@9
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > v12; // [sp+78h] [bp-50h]@9
  CUnmannedTraderSortType *v13; // [sp+90h] [bp-38h]@11
  CUnmannedTraderSortType *v14; // [sp+98h] [bp-30h]@13
  __int64 v15; // [sp+A0h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *v16; // [sp+A8h] [bp-20h]@9
  std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *_Right; // [sp+B0h] [bp-18h]@9
  CUnmannedTraderGroupIDInfo *v18; // [sp+D0h] [bp+8h]@1
  char v19; // [sp+E0h] [bp+18h]@1

  v19 = bySortType;
  v18 = this;
  v3 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  if ( (unsigned __int8)byDivision == 255 )
  {
    v5 = 0i64;
  }
  else if ( (unsigned __int8)bySortType == 255 )
  {
    v5 = 0i64;
  }
  else
  {
    v8 = 0i64;
    std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::begin(
      &v18->m_vecDivisionInfo,
      &result);
    while ( 1 )
    {
      v16 = std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::end(
              &v18->m_vecDivisionInfo,
              &v12);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)v16;
      v11 = std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator!=(
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)&v16->_Mycont);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v12);
      if ( !v11 )
        break;
      v6 = std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator*(&result);
      v8 = CUnmannedTraderDivisionInfo::GetSortType(*v6, v19);
      if ( v8 )
      {
        v13 = v8;
        std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
        return v13;
      }
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::operator++(
        &result,
        &v10,
        0);
      std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&v10);
    }
    v14 = 0i64;
    std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(&result);
    v5 = v14;
  }
  return v5;
}
