/*
 * Function: ?pc_RequestUILockUpdate@CPlayer@@QEAAXPEAD00E0@Z
 * Address: 0x140100FD0
 */

void __fastcall CPlayer::pc_RequestUILockUpdate(CPlayer *this, char *uszUILockPWOld, char *uszUILockPW, char *uszUILockPW_Confirm, char byUILock_HintIndex, char *uszUILock_HintAnswer)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@27
  signed __int64 v9; // rax@35
  CUserDB *v10; // rcx@35
  CUserDB *v11; // rax@37
  CUserDB *v12; // rcx@37
  __int64 v13; // [sp+0h] [bp-48h]@1
  _DWORD byHintIndex[2]; // [sp+20h] [bp-28h]@35
  char *uszHintAnswer; // [sp+28h] [bp-20h]@35
  char v16; // [sp+30h] [bp-18h]@5
  CPlayer *v17; // [sp+50h] [bp+8h]@1
  const char *Str1; // [sp+58h] [bp+10h]@1
  char *Str; // [sp+60h] [bp+18h]@1
  const char *Str2; // [sp+68h] [bp+20h]@1

  Str2 = uszUILockPW_Confirm;
  Str = uszUILockPW;
  Str1 = uszUILockPWOld;
  v17 = this;
  v6 = &v13;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !v17->m_pUserDB->m_byUserDgr )
  {
    v16 = 0;
    if ( uszUILockPW
      && uszUILockPW_Confirm
      && uszUILock_HintAnswer
      && strlen_0(uszUILockPW)
      && strlen_0(Str2)
      && strlen_0(uszUILock_HintAnswer) )
    {
      if ( strlen_0(Str) <= 0xC && strlen_0(Str2) <= 0xC && strlen_0(uszUILock_HintAnswer) <= 0x10 )
      {
        if ( v17->m_pUserDB->m_byUILock == 2 )
        {
          if ( !strcmp_0(Str1, v17->m_pUserDB->m_szUILock_PW) )
          {
            if ( !strcmp_0(Str, v17->m_pUserDB->m_szUILock_PW) )
            {
              v16 = 8;
            }
            else if ( !strcmp_0(Str, Str2) )
            {
              if ( !strcmp_0(Str, v17->m_pUserDB->m_szAccount_PW) )
              {
                v16 = 2;
              }
              else if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&v17->m_Param) > 0
                     && (v8 = CPlayerDB::GetTrunkPasswdW(&v17->m_Param), !strcmp_0(Str, v8)) )
              {
                v16 = 2;
              }
              else if ( isalphastr(Str) || isdigitstr(Str) )
              {
                v16 = 5;
              }
              else if ( isalnumstr(Str) )
              {
                if ( !IsSQLValidString(uszUILock_HintAnswer) )
                {
                  v9 = (signed __int64)v17->m_pUserDB->m_szAccountID;
                  v10 = v17->m_pUserDB;
                  uszHintAnswer = uszUILock_HintAnswer;
                  byHintIndex[0] = v17->m_dwObjSerial;
                  CLogFile::Write(
                    &stru_1799C8E78,
                    "CPlayer::pc_RequestUILockUpdate() : Account : %u(%s) Character : %u !::IsSQLValidString( uszUILock_H"
                    "intAnswer(%s) ) Invalid!",
                    v10->m_dwAccountSerial,
                    v9);
                  v16 = 6;
                }
              }
              else
              {
                v16 = 5;
              }
            }
            else
            {
              v16 = 1;
            }
          }
          else
          {
            v16 = 9;
          }
        }
        else
        {
          v16 = 13;
        }
      }
      else
      {
        v16 = 6;
      }
    }
    else
    {
      v16 = 6;
    }
    if ( v16 )
    {
      CPlayer::SendMsg_UILock_Update_Result(v17, v16);
    }
    else
    {
      v11 = v17->m_pUserDB;
      v12 = v17->m_pUserDB;
      uszHintAnswer = uszUILock_HintAnswer;
      LOBYTE(byHintIndex[0]) = byUILock_HintIndex;
      CPlayer::SendMsg_UILock_Update_Request_ToAccount(
        v17,
        v12->m_dwAccountSerial,
        Str,
        v11->m_idWorld.wIndex,
        byUILock_HintIndex,
        uszUILock_HintAnswer);
    }
  }
}
