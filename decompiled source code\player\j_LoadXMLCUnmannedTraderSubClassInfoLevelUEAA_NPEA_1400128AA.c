/*
 * Function: j_?LoadXML@CUnmannedTraderSubClassInfoLevel@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x1400128AA
 */

bool __fastcall CUnmannedTraderSubClassInfoLevel::LoadXML(CUnmannedTraderSubClassInfoLevel *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  return CUnmannedTraderSubClassInfoLevel::LoadXML(this, elemSubClass, kLogger, dwDivisionID, dwClassID);
}
