/*
 * Function: ?CreateDataResetToken@CMainThread@@QEAAKPEAU_SYSTEMTIME@@@Z
 * Address: 0x1401FB490
 */

__int64 __fastcall CMainThread::CreateDataResetToken(CMainThread *this, _SYSTEMTIME *tm)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // ecx@4
  unsigned int v5; // edx@4
  __int64 v7; // [sp+0h] [bp-88h]@1
  int v8; // [sp+20h] [bp-68h]@4
  int v9; // [sp+28h] [bp-60h]@4
  int v10; // [sp+30h] [bp-58h]@4
  char DstBuf; // [sp+48h] [bp-40h]@4
  char v12; // [sp+49h] [bp-3Fh]@4
  unsigned __int64 v13; // [sp+70h] [bp-18h]@4
  _SYSTEMTIME *v14; // [sp+98h] [bp+10h]@1

  v14 = tm;
  v2 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  v10 = 0;
  DstBuf = 0;
  memset(&v12, 0, 0x13ui64);
  v4 = tm->wMonth;
  v5 = tm->wYear;
  v9 = v14->wDay;
  v8 = v4;
  sprintf_s(&DstBuf, 0x14ui64, "%04d%02d%0d", v5);
  if ( &v7 != (__int64 *)-72 )
    v10 = atoi(&DstBuf);
  return (unsigned int)v10;
}
