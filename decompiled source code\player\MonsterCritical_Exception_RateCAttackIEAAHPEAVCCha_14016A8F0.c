/*
 * Function: ?MonsterCritical_Exception_Rate@CAttack@@IEAAHPEAVCCharacter@@_N@Z
 * Address: 0x14016A8F0
 */

__int64 __fastcall CAttack::MonsterCritical_Exception_Rate(CAttack *this, CCharacter *p<PERSON><PERSON><PERSON>, bool bBackAttack)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@10
  __int64 v6; // [sp+0h] [bp-58h]@1
  CMonster *v7; // [sp+20h] [bp-38h]@6
  float v8; // [sp+28h] [bp-30h]@6
  int nOutValue; // [sp+34h] [bp-24h]@6
  bool v10; // [sp+70h] [bp+18h]@1

  v10 = bBackAttack;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pCharacter && pCharacter->m_ObjID.m_byID == 1 )
  {
    v7 = (CMonster *)pCharacter;
    v8 = (float)(*(int (__fastcall **)(CCharacter *))&pCharacter->vfptr[1].gap8[8])(pCharacter);
    nOutValue = -1;
    if ( v10 && CMonster::GetViewAngleCap(v7, 2, &nOutValue) )
    {
      v8 = v8 * (float)((float)(100 - nOutValue) / 100.0);
      if ( v8 < 0.0 )
        v8 = 0.0;
    }
    result = (unsigned int)(signed int)ffloor(v8);
  }
  else
  {
    result = 0i64;
  }
  return result;
}
