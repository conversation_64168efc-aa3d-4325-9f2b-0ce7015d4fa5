/*
 * Function: _std::deque_RECV_DATA_std::allocator_RECV_DATA___::erase_::_1_::dtor$5
 * Address: 0x14031E100
 */

void __fastcall std::deque_RECV_DATA_std::allocator_RECV_DATA___::erase_::_1_::dtor_5(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 192));
}
