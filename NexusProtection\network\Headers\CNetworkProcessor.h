#pragma once

/**
 * @file CNetworkProcessor.h
 * @brief Modern C++20 Network Processing System
 * 
 * This file provides comprehensive network processing including CNetProcess
 * refactoring, packet handling, and internal message processing with modern
 * C++20 patterns.
 * 
 * Refactored from decompiled sources:
 * - _InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0.c (145 lines)
 * - LoadSendMsgCNetProcessQEAAHKGPEADGZ_140479680.c (26 lines)
 * - _PopRecvMsgCNetProcessAEAAXGZ_140478680.c
 * - _CheckSendCNetProcessAEAAXGZ_140479A30.c
 * - SendThreadCNetProcessCAXPEAXZ_140001AE6.c
 * - RecvThreadCNetProcessCAXPEAXZ_140007450.c
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <thread>
#include <queue>
#include <condition_variable>

// Forward declarations
class CNetSocket;
class CLogFile;
struct _MSG_HEADER;

namespace NexusProtection {
namespace Network {

// Forward declarations
class CNetworkMessageSystem;
class NetworkMessage;

/**
 * @brief Network processing result enumeration
 */
enum class NetworkProcessResult : uint8_t {
    Success = 0,
    InvalidSocket,
    InvalidMessage,
    ProcessingError,
    SecurityViolation,
    RateLimited,
    SystemError,
    ThreadError
};

/**
 * @brief Network thread type enumeration
 */
enum class NetworkThreadType : uint8_t {
    Send = 0,
    Receive = 1,
    Process = 2
};

/**
 * @brief Speed hack detection state
 */
enum class SpeedHackState : uint8_t {
    Normal = 0,
    Suspicious = 1,
    Detected = 2,
    Blocked = 3
};

/**
 * @brief Client connection information
 */
struct ClientConnectionInfo {
    uint32_t clientId{0};
    uint32_t socketIndex{0};
    std::string clientIP;
    std::chrono::steady_clock::time_point connectTime;
    std::chrono::steady_clock::time_point lastActivity;
    
    // Speed hack detection
    SpeedHackState speedHackState{SpeedHackState::Normal};
    uint32_t speedHackKey[4]{0};
    std::chrono::steady_clock::time_point lastSpeedHackTime;
    std::chrono::steady_clock::time_point lastSpeedHackResponse;
    uint8_t speedHackContCount{0};
    uint8_t speedHackMissCount{0};
    
    // Statistics
    std::atomic<uint64_t> messagesReceived{0};
    std::atomic<uint64_t> messagesSent{0};
    std::atomic<uint64_t> bytesReceived{0};
    std::atomic<uint64_t> bytesSent{0};
    
    ClientConnectionInfo() {
        auto now = std::chrono::steady_clock::now();
        connectTime = now;
        lastActivity = now;
        lastSpeedHackTime = now;
    }
    
    bool IsActive() const {
        auto now = std::chrono::steady_clock::now();
        auto idleTime = std::chrono::duration_cast<std::chrono::seconds>(now - lastActivity);
        return idleTime.count() < 300; // 5 minutes timeout
    }
    
    std::chrono::seconds GetConnectionDuration() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - connectTime);
    }
};

/**
 * @brief Network processing statistics
 */
struct NetworkProcessingStats {
    std::atomic<uint64_t> totalPacketsProcessed{0};
    std::atomic<uint64_t> successfulPackets{0};
    std::atomic<uint64_t> failedPackets{0};
    std::atomic<uint64_t> droppedPackets{0};
    std::atomic<uint64_t> speedHackDetections{0};
    std::atomic<uint64_t> securityViolations{0};
    std::atomic<uint32_t> activeConnections{0};
    std::chrono::steady_clock::time_point startTime;
    
    NetworkProcessingStats() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetSuccessRate() const {
        uint64_t total = totalPacketsProcessed.load();
        return total > 0 ? (static_cast<double>(successfulPackets.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Network packet queue entry
 */
struct NetworkPacketEntry {
    uint32_t clientId{0};
    uint32_t socketIndex{0};
    std::vector<uint8_t> data;
    std::chrono::steady_clock::time_point timestamp;
    uint32_t priority{0};
    
    NetworkPacketEntry() : timestamp(std::chrono::steady_clock::now()) {}
    NetworkPacketEntry(uint32_t id, uint32_t socket, const std::vector<uint8_t>& packetData)
        : clientId(id), socketIndex(socket), data(packetData), timestamp(std::chrono::steady_clock::now()) {}
    
    bool operator<(const NetworkPacketEntry& other) const {
        // Higher priority packets come first
        if (priority != other.priority) {
            return priority < other.priority;
        }
        // Earlier timestamps come first for same priority
        return timestamp > other.timestamp;
    }
};

/**
 * @brief Modern C++20 Network Processor class
 * 
 * This class provides comprehensive network processing including packet
 * handling, message processing, and thread management with modern C++20 patterns.
 */
class CNetworkProcessor {
public:
    // Constructor and Destructor
    CNetworkProcessor();
    virtual ~CNetworkProcessor();

    // Disable copy constructor and assignment operator
    CNetworkProcessor(const CNetworkProcessor&) = delete;
    CNetworkProcessor& operator=(const CNetworkProcessor&) = delete;

    // Enable move constructor and assignment operator
    CNetworkProcessor(CNetworkProcessor&&) noexcept = default;
    CNetworkProcessor& operator=(CNetworkProcessor&&) noexcept = default;

    /**
     * @brief Initialize network processor
     * 
     * @param netSocket Network socket system
     * @param messageSystem Message system
     * @return true if initialization successful, false otherwise
     */
    bool Initialize(std::shared_ptr<CNetSocket> netSocket, 
                   std::shared_ptr<CNetworkMessageSystem> messageSystem);

    /**
     * @brief Shutdown network processor
     */
    void Shutdown();

    /**
     * @brief Start processing threads
     * 
     * @return true if threads started successfully, false otherwise
     */
    bool StartThreads();

    /**
     * @brief Stop processing threads
     */
    void StopThreads();

    /**
     * @brief Process internal packet
     * 
     * Modern implementation of _InternalPacketProcess method.
     * 
     * @param socketIndex Socket index
     * @param msgHeader Message header
     * @param msgData Message data
     * @return Network processing result
     */
    NetworkProcessResult ProcessInternalPacket(uint32_t socketIndex, 
                                              const _MSG_HEADER* msgHeader, 
                                              const char* msgData);

    /**
     * @brief Load and send message
     * 
     * Modern implementation of LoadSendMsg method.
     * 
     * @param clientIndex Client index
     * @param messageType Message type
     * @param messageData Message data
     * @param messageLength Message length
     * @return true if message loaded successfully, false otherwise
     */
    bool LoadSendMessage(uint32_t clientIndex, uint16_t messageType, 
                        const char* messageData, uint16_t messageLength);

    /**
     * @brief Pop received message
     * 
     * Modern implementation of _PopRecvMsg method.
     * 
     * @param socketIndex Socket index
     * @return Network processing result
     */
    NetworkProcessResult PopReceivedMessage(uint32_t socketIndex);

    /**
     * @brief Check send queue
     * 
     * Modern implementation of _CheckSend method.
     * 
     * @param socketIndex Socket index
     * @return Network processing result
     */
    NetworkProcessResult CheckSendQueue(uint32_t socketIndex);

    /**
     * @brief Add client connection
     * 
     * @param clientId Client identifier
     * @param socketIndex Socket index
     * @param clientIP Client IP address
     * @return true if added successfully, false otherwise
     */
    bool AddClientConnection(uint32_t clientId, uint32_t socketIndex, const std::string& clientIP);

    /**
     * @brief Remove client connection
     * 
     * @param clientId Client identifier
     * @return true if removed successfully, false otherwise
     */
    bool RemoveClientConnection(uint32_t clientId);

    /**
     * @brief Get client connection info
     * 
     * @param clientId Client identifier
     * @return Client connection info or nullptr if not found
     */
    std::shared_ptr<ClientConnectionInfo> GetClientConnection(uint32_t clientId) const;

    /**
     * @brief Process speed hack detection
     * 
     * @param clientId Client identifier
     * @param messageType Speed hack message type
     * @param messageData Message data
     * @return Network processing result
     */
    NetworkProcessResult ProcessSpeedHackDetection(uint32_t clientId, uint8_t messageType, 
                                                  const char* messageData);

    /**
     * @brief Push close node
     * 
     * @param clientId Client identifier to disconnect
     */
    void PushCloseNode(uint32_t clientId);

    /**
     * @brief Get processing statistics
     * 
     * @return Current processing statistics
     */
    const NetworkProcessingStats& GetStatistics() const { return m_stats; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Get active connection count
     * 
     * @return Number of active connections
     */
    size_t GetActiveConnectionCount() const;

    /**
     * @brief Update client activity
     * 
     * @param clientId Client identifier
     */
    void UpdateClientActivity(uint32_t clientId);

    /**
     * @brief Check if processor is running
     * 
     * @return true if running, false otherwise
     */
    bool IsRunning() const { return m_isRunning; }

protected:
    // Core systems
    std::shared_ptr<CNetSocket> m_netSocket;
    std::shared_ptr<CNetworkMessageSystem> m_messageSystem;
    
    // Client connections
    std::unordered_map<uint32_t, std::shared_ptr<ClientConnectionInfo>> m_clientConnections;
    
    // Packet queues
    std::priority_queue<NetworkPacketEntry> m_sendQueue;
    std::queue<NetworkPacketEntry> m_receiveQueue;
    
    // Processing threads
    std::unique_ptr<std::thread> m_sendThread;
    std::unique_ptr<std::thread> m_receiveThread;
    std::unique_ptr<std::thread> m_processThread;
    
    // Statistics
    NetworkProcessingStats m_stats;
    
    // Synchronization
    mutable std::mutex m_connectionsMutex;
    mutable std::mutex m_sendQueueMutex;
    mutable std::mutex m_receiveQueueMutex;
    mutable std::mutex m_statsMutex;
    std::condition_variable m_sendCondition;
    std::condition_variable m_receiveCondition;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isRunning{false};
    std::atomic<bool> m_shutdownRequested{false};
    
    // Logging
    std::unique_ptr<CLogFile> m_logHack;
    std::unique_ptr<CLogFile> m_logNetwork;

private:
    /**
     * @brief Send thread function
     */
    void SendThreadFunction();

    /**
     * @brief Receive thread function
     */
    void ReceiveThreadFunction();

    /**
     * @brief Process thread function
     */
    void ProcessThreadFunction();

    /**
     * @brief Process speed hack challenge
     * 
     * @param clientInfo Client connection info
     * @param messageData Message data
     * @return Network processing result
     */
    NetworkProcessResult ProcessSpeedHackChallenge(std::shared_ptr<ClientConnectionInfo> clientInfo, 
                                                  const char* messageData);

    /**
     * @brief Process speed hack response
     * 
     * @param clientInfo Client connection info
     * @param messageData Message data
     * @return Network processing result
     */
    NetworkProcessResult ProcessSpeedHackResponse(std::shared_ptr<ClientConnectionInfo> clientInfo, 
                                                 const char* messageData);

    /**
     * @brief Generate speed hack key
     * 
     * @param key Output key array
     */
    void GenerateSpeedHackKey(uint32_t* key);

    /**
     * @brief Validate speed hack timing
     * 
     * @param clientInfo Client connection info
     * @param responseTime Response time in milliseconds
     * @return true if timing is valid, false otherwise
     */
    bool ValidateSpeedHackTiming(std::shared_ptr<ClientConnectionInfo> clientInfo, uint32_t responseTime);

    /**
     * @brief Update processing statistics
     * 
     * @param result Processing result
     */
    void UpdateStatistics(NetworkProcessResult result);

    /**
     * @brief Cleanup inactive connections
     */
    void CleanupInactiveConnections();

    /**
     * @brief Initialize logging systems
     * 
     * @return true if successful, false otherwise
     */
    bool InitializeLogging();
};

/**
 * @brief Network Processor Factory
 */
class CNetworkProcessorFactory {
public:
    /**
     * @brief Create network processor
     * 
     * @return Unique pointer to network processor
     */
    static std::unique_ptr<CNetworkProcessor> CreateNetworkProcessor();

    /**
     * @brief Create network processor with dependencies
     *
     * @param netSocket Network socket system
     * @param messageSystem Message system
     * @return Unique pointer to network processor
     */
    static std::unique_ptr<CNetworkProcessor> CreateNetworkProcessor(
        std::shared_ptr<CNetSocket> netSocket,
        std::shared_ptr<CNetworkMessageSystem> messageSystem);
};

/**
 * @brief Network processing utility functions
 */
namespace NetworkProcessingUtils {
    std::string NetworkProcessResultToString(NetworkProcessResult result);
    std::string SpeedHackStateToString(SpeedHackState state);
    std::string NetworkThreadTypeToString(NetworkThreadType type);
    uint32_t GetCurrentTimeMs();
    bool IsValidMessageType(uint8_t messageType);
}

} // namespace Network
} // namespace NexusProtection
