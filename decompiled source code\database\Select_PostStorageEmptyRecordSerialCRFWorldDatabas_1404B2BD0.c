/*
 * Function: ?Select_PostStorageEmptyRecordSerial@CRFWorldDatabase@@QEAA_NPEAK@Z
 * Address: 0x1404B2BD0
 */

char __fastcall CRFWorldDatabase::Select_PostStorageEmptyRecordSerial(CRFWorldDatabase *this, unsigned int *pdwStorageSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@21
  SQLLEN v8; // [sp+38h] [bp-170h]@21
  __int16 v9; // [sp+44h] [bp-164h]@9
  unsigned int TargetValue; // [sp+54h] [bp-154h]@4
  char DstBuf; // [sp+80h] [bp-128h]@4
  char v12; // [sp+81h] [bp-127h]@4
  unsigned __int64 v13; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+1B0h] [bp+8h]@1
  unsigned int *v15; // [sp+1B8h] [bp+10h]@1

  v15 = pdwStorageSerial;
  v14 = this;
  v2 = &v5;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  TargetValue = 0;
  DstBuf = 0;
  memset(&v12, 0, 0xFFui64);
  sprintf_s(&DstBuf, 0x100ui64, "select top 1 Serial from tbl_PostStorage where dck=1");
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &DstBuf);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v9 = SQLExecDirect_0(v14->m_hStmtSelect, &DstBuf, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v9 = SQLFetch_0(v14->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        if ( v9 != 100 )
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
        }
        if ( v14->m_hStmtSelect )
          SQLCloseCursor_0(v14->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v14->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v8);
        if ( v9 && v9 != 1 )
        {
          SQLStmt = v14->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v9, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v9, v14->m_hStmtSelect);
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          result = 0;
        }
        else
        {
          *v15 = TargetValue;
          if ( v14->m_hStmtSelect )
            SQLCloseCursor_0(v14->m_hStmtSelect);
          if ( v14->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &DstBuf);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 0;
  }
  return result;
}
