/*
 * Function: ?ChangeMap@CMapDisplay@@QEAA_NPEAVCMapData@@@Z
 * Address: 0x14019F130
 */

char __fastcall CMapDisplay::ChangeMap(CMapDisplay *this, CMapData *pMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMapDisplay *v6; // [sp+30h] [bp+8h]@1
  CMapData *pMapa; // [sp+38h] [bp+10h]@1

  pMapa = pMap;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bDisplayMode )
  {
    if ( v6->m_pActMap == pMap )
    {
      result = 0;
    }
    else
    {
      CMapDisplay::InitSurface(v6, pMap);
      v6->m_pActMap = pMapa;
      v6->m_wLayerIndex = 0;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
