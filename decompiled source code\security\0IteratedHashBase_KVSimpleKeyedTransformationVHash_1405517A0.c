/*
 * Function: ??0?$IteratedHashBase@_KV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x1405517A0
 */

CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *__fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>(CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *a1)
{
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>::SimpleKeyedTransformation<CryptoPP::HashTransformation>(a1);
  v2[1].vfptr = 0i64;
  v2[1].vfptr = 0i64;
  return v2;
}
