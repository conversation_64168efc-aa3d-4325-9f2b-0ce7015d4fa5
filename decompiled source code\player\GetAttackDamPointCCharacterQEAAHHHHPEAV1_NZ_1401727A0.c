/*
 * Function: ?GetAttackDamPoint@CCharacter@@QEAAHHHHPEAV1@_N@Z
 * Address: 0x1401727A0
 */

signed __int64 __fastcall CCharacter::GetAttackDamPoint(CCharacter *this, int nAttPnt, int nAttPart, int nTolType, CCharacter *pDst, bool bBackAttack)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@13
  float v9; // xmm0_4@19
  __int64 v10; // [sp+0h] [bp-A8h]@1
  float v11; // [sp+20h] [bp-88h]@4
  int v12; // [sp+24h] [bp-84h]@29
  float v13; // [sp+28h] [bp-80h]@22
  float v14; // [sp+2Ch] [bp-7Ch]@25
  int v15; // [sp+30h] [bp-78h]@4
  unsigned int v16; // [sp+44h] [bp-64h]@4
  CMonster *v17; // [sp+58h] [bp-50h]@6
  int nOutValue; // [sp+64h] [bp-44h]@6
  float v19; // [sp+74h] [bp-34h]@7
  float v20; // [sp+78h] [bp-30h]@14
  float v21; // [sp+7Ch] [bp-2Ch]@14
  float v22; // [sp+80h] [bp-28h]@21
  float v23; // [sp+84h] [bp-24h]@21
  float v24; // [sp+88h] [bp-20h]@24
  float v25; // [sp+8Ch] [bp-1Ch]@27
  CCharacter *v26; // [sp+90h] [bp-18h]@34
  CCharacter *v27; // [sp+B0h] [bp+8h]@1
  signed int nDamPoint; // [sp+B8h] [bp+10h]@1
  int v29; // [sp+C0h] [bp+18h]@1
  int v30; // [sp+C8h] [bp+20h]@1

  v30 = nTolType;
  v29 = nAttPart;
  nDamPoint = nAttPnt;
  v27 = this;
  v6 = &v10;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = 0.0;
  v15 = CCharacter::GetTotalTol(pDst, nTolType, nAttPnt);
  v16 = v29;
  if ( _effect_parameter::GetEff_State(&pDst->m_EP, 12) )
  {
    ((void (__fastcall *)(CCharacter *, _QWORD))pDst->vfptr->SetAttackPart)(pDst, (unsigned int)v29);
  }
  else
  {
    v11 = (float)((int (__fastcall *)(CCharacter *, _QWORD, CCharacter *, unsigned int *))pDst->vfptr->GetDefFC)(
                   pDst,
                   (unsigned int)v29,
                   v27,
                   &v16);
    if ( pDst->m_ObjID.m_byID == 1 )
    {
      v17 = (CMonster *)pDst;
      nOutValue = -1;
      if ( CMonster::GetViewAngleCap((CMonster *)pDst, 1, &nOutValue) )
      {
        v19 = (float)nOutValue;
        if ( (float)nOutValue > 0.0 )
        {
          v19 = v19 / 100.0;
          if ( v19 <= 1.0 )
            v11 = v11 * (float)(1.0 - v19);
        }
      }
    }
  }
  if ( -2.0 == v11 )
  {
    result = 4294967294i64;
  }
  else
  {
    ((void (__fastcall *)(CCharacter *, _QWORD))pDst->vfptr->GetDefFacing)(pDst, v16);
    v20 = FLOAT_N2_0;
    ((void (__fastcall *)(CCharacter *, _QWORD))pDst->vfptr->GetDefGap)(pDst, v16);
    v21 = FLOAT_N2_0;
    if ( v16 == 5 )
    {
      ((void (__fastcall *)(CCharacter *, _QWORD))pDst->vfptr->GetDefFacing)(pDst, (unsigned int)v29);
      if ( v20 > -2.0 )
      {
        ((void (__fastcall *)(_QWORD, _QWORD))pDst->vfptr->GetDefFacing)(pDst, (unsigned int)v29);
        v20 = FLOAT_N2_0;
      }
      ((void (__fastcall *)(CCharacter *, _QWORD))pDst->vfptr->GetDefGap)(pDst, (unsigned int)v29);
      if ( v21 < -2.0 )
      {
        ((void (__fastcall *)(_QWORD, _QWORD))pDst->vfptr->GetDefGap)(pDst, (unsigned int)v29);
        v21 = FLOAT_N2_0;
      }
    }
    v9 = FLOAT_1_0;
    if ( v11 < 1.0 )
    {
      v9 = FLOAT_1_0;
      v11 = FLOAT_1_0;
    }
    ((void (__fastcall *)(CCharacter *))v27->vfptr->GetWeaponAdjust)(v27);
    v22 = (float)(v9 + v21) / 2.0;
    v23 = v20 - 1.0;
    if ( (float)(v20 - 1.0) == 0.0 )
      v13 = 0.0;
    else
      v13 = (float)((float)((float)(v20 * v11) * v21) - v11) / v23;
    v24 = v13 - (float)(v11 * v22);
    if ( v24 == 0.0 )
      v14 = 0.0;
    else
      v14 = (float)(v13 - v11) / v24;
    v25 = FLOAT_1_2;
    if ( v30 == -1 )
      v25 = FLOAT_1_0;
    v12 = (signed int)ffloor((float)((float)((float)((float)nDamPoint * v25) + (float)v15) - (float)(v11 * v22)) * v14);
    if ( v12 < 1 )
      v12 = 1;
    if ( !pDst->m_ObjID.m_byID && v12 >= 1 && v12 <= 300 )
    {
      v26 = pDst;
      if ( ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetLevel)(pDst) >= 30 )
        v12 = rand() % 300 + 1;
    }
    result = (unsigned int)v12;
  }
  return result;
}
