/*
 * Function: ?AlterExp_Potion@CPlayer@@QEAAXN@Z
 * Address: 0x14005C550
 */

void __fastcall CPlayer::AlterExp_Potion(CPlayer *this, long double dAlterExp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  int v5; // eax@7
  int v6; // xmm0_4@8
  double v7; // xmm0_8@9
  cStaticMember_Player *v8; // rax@9
  int v9; // eax@10
  cStaticMember_Player *v10; // rax@12
  long double v11; // xmm0_8@12
  cStaticMember_Player *v12; // rax@12
  cStaticMember_Player *v13; // rax@14
  __int64 v14; // [sp-20h] [bp-88h]@1
  long double v15; // [sp+0h] [bp-68h]@4
  int v16; // [sp+8h] [bp-60h]@8
  long double v17; // [sp+10h] [bp-58h]@9
  double v18; // [sp+18h] [bp-50h]@9
  unsigned __int8 v19; // [sp+20h] [bp-48h]@10
  unsigned __int8 v20; // [sp+21h] [bp-47h]@10
  int v21; // [sp+24h] [bp-44h]@10
  long double v22; // [sp+28h] [bp-40h]@12
  int v23; // [sp+30h] [bp-38h]@6
  int v24; // [sp+34h] [bp-34h]@7
  int lv; // [sp+38h] [bp-30h]@9
  int v26; // [sp+3Ch] [bp-2Ch]@12
  double v27; // [sp+40h] [bp-28h]@12
  int v28; // [sp+48h] [bp-20h]@12
  double v29; // [sp+50h] [bp-18h]@14
  int v30; // [sp+58h] [bp-10h]@14
  CPlayer *v31; // [sp+70h] [bp+8h]@1
  long double v32; // [sp+78h] [bp+10h]@8

  v31 = this;
  v2 = &v14;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = dAlterExp;
  if ( v31->m_byUserDgr && v31->m_byUserDgr != 1
    || (v23 = CPlayerDB::GetMaxLevel(&v31->m_Param), v4 = CPlayerDB::GetLevel(&v31->m_Param), v23 != v4) )
  {
    v24 = CPlayerDB::GetMaxLevel(&v31->m_Param);
    v5 = CPlayerDB::GetLevel(&v31->m_Param);
    if ( v24 != v5 )
    {
      TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v31->m_id.wIndex);
      *(float *)&v6 = dAlterExp;
      v16 = v6;
      v32 = dAlterExp * *(float *)&v6;
      if ( v32 > 0.0 )
      {
        CPlayerDB::GetExp(&v31->m_Param);
        v17 = 0.0;
        CPlayerDB::GetExp(&v31->m_Param);
        v7 = v32 + 0.0;
        v18 = v32 + 0.0;
        CPlayer::SendMsg_NotifyGetExpInfo(v31, v17, v32, v32 + 0.0);
        lv = CPlayerDB::GetLevel(&v31->m_Param);
        v8 = cStaticMember_Player::Instance();
        cStaticMember_Player::GetLimitExp(v8, lv);
        if ( v18 < v32 + 0.0 )
        {
          CPlayerDB::SetExp(&v31->m_Param, v18);
          CPlayerDB::GetExp(&v31->m_Param);
          v29 = v32 + 0.0;
          v30 = CPlayerDB::GetLevel(&v31->m_Param);
          v13 = cStaticMember_Player::Instance();
          cStaticMember_Player::GetLimitExp(v13, v30);
          v7 = v29 / v7 * 1000000.0;
          v31->m_dwExpRate = (signed int)floor(v7);
          CPlayer::SendMsg_AlterExpInform(v31);
        }
        else
        {
          v19 = ((int (__fastcall *)(CPlayer *))v31->vfptr->GetLevel)(v31);
          v20 = v19 + 1;
          CPlayer::SetLevel(v31, v19 + 1);
          v21 = CPlayerDB::GetMaxLevel(&v31->m_Param);
          v9 = CPlayerDB::GetLevel(&v31->m_Param);
          if ( v21 > v9 )
          {
            v26 = v19;
            v10 = cStaticMember_Player::Instance();
            cStaticMember_Player::GetLimitExp(v10, v26);
            v11 = v18 - v7;
            v22 = v11;
            CPlayerDB::SetExp(&v31->m_Param, v11);
            CPlayerDB::GetExp(&v31->m_Param);
            v27 = v11;
            v28 = v20;
            v12 = cStaticMember_Player::Instance();
            cStaticMember_Player::GetLimitExp(v12, v28);
            v7 = v27 / v11 * 1000000.0;
            v31->m_dwExpRate = (signed int)floor(v7);
          }
          else
          {
            CPlayerDB::SetExp(&v31->m_Param, 0.0);
            v31->m_dwExpRate = 0;
          }
          CPlayer::SendMsg_AlterExpInform(v31);
          v31->m_bDownCheckEquipEffect = 1;
          CPlayer::SendMsg_EquipItemLevelLimit(v31, v20);
        }
        if ( v31->m_pUserDB )
        {
          CPlayerDB::GetExp(&v31->m_Param);
          CUserDB::Update_Exp(v31->m_pUserDB, v7);
        }
      }
    }
  }
}
