/*
 * Function: ?pc_SkillRequest@CPlayer@@QEAAXEPEAU_CHRID@@PEAG@Z
 * Address: 0x14009A4B0
 */

void __fastcall CPlayer::pc_SkillRequest(CPlayer *this, char bySkillIndex, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  int nSFLv; // [sp+34h] [bp-24h]@4
  bool v8; // [sp+44h] [bp-14h]@4
  char v9; // [sp+45h] [bp-13h]@4
  CPlayer *v10; // [sp+60h] [bp+8h]@1
  char v11; // [sp+68h] [bp+10h]@1
  _CHRID *pidDsta; // [sp+70h] [bp+18h]@1
  unsigned __int16 *v13; // [sp+78h] [bp+20h]@1

  v13 = pConsumeSerial;
  pidDsta = pidDst;
  v11 = bySkillIndex;
  v10 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  nSFLv = 1;
  v8 = CCharacter::GetStealth((CCharacter *)&v10->vfptr, 1);
  v9 = CPlayer::skill_process(v10, 0, (unsigned __int8)v11, pidDsta, v13, &nSFLv);
  if ( (!v9 || v9 == 100) && v8 )
    CCharacter::BreakStealth((CCharacter *)&v10->vfptr);
  CPlayer::SendMsg_SkillResult(v10, v9, pidDsta, v11, nSFLv);
}
