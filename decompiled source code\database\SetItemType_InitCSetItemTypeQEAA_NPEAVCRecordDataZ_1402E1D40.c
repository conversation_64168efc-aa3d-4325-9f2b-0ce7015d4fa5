/*
 * Function: ?SetItemType_Init@CSetItemType@@QEAA_NPEAVCRecordData@@@Z
 * Address: 0x1402E1D40
 */

char __fastcall CSetItemType::SetItemType_Init(CSetItemType *this, CRecordData *prd)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // rax@15
  __int64 v6; // [sp+0h] [bp-68h]@1
  _SetItemEff_fld *pFld; // [sp+20h] [bp-48h]@10
  int v8; // [sp+28h] [bp-40h]@10
  int n; // [sp+2Ch] [bp-3Ch]@10
  si_interpret **v10; // [sp+30h] [bp-38h]@10
  si_interpret *v11; // [sp+38h] [bp-30h]@17
  si_interpret *v12; // [sp+40h] [bp-28h]@14
  __int64 v13; // [sp+48h] [bp-20h]@4
  unsigned __int64 v14; // [sp+50h] [bp-18h]@10
  si_interpret *v15; // [sp+58h] [bp-10h]@15
  CSetItemType *v16; // [sp+70h] [bp+8h]@1
  CRecordData *v17; // [sp+78h] [bp+10h]@1

  v17 = prd;
  v16 = this;
  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  if ( prd )
  {
    if ( v16->m_iEffectTypeCount )
    {
      result = 0;
    }
    else if ( CRecordData::GetRecordNum(prd) > 0 )
    {
      v14 = CRecordData::GetRecordNum(v17);
      v10 = (si_interpret **)operator new[](saturated_mul(8ui64, v14));
      v16->m_pEffectCountInfo = v10;
      pFld = 0i64;
      v8 = 0;
      n = 0;
      v8 = CRecordData::GetRecordNum(v17);
      while ( n < v8 )
      {
        pFld = (_SetItemEff_fld *)CRecordData::GetRecord(v17, n);
        if ( !pFld )
        {
          CSetItemType::Class_Init(v16);
          return 0;
        }
        v12 = (si_interpret *)operator new(0x54ui64);
        if ( v12 )
        {
          si_interpret::si_interpret(v12);
          v15 = (si_interpret *)v5;
        }
        else
        {
          v15 = 0i64;
        }
        v11 = v15;
        v16->m_pEffectCountInfo[n] = v15;
        if ( !si_interpret::set_effect_interpret(v16->m_pEffectCountInfo[n], pFld) )
        {
          CSetItemType::Class_Init(v16);
          return 0;
        }
        ++n;
      }
      v16->m_iEffectTypeCount = n;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
