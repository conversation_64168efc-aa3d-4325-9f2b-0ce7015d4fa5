/*
 * Function: j_??0?$_Vector_const_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEAA@PEAPEAVCUnmannedTraderSortType@@@Z
 * Address: 0x14000738D
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_Ptr)
{
  std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(
    this,
    _Ptr);
}
