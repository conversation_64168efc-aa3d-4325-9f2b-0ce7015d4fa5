/*
 * Function: j_??0?$IteratedHash@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@VHashTransformation@2@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14000356C
 */

void __fastcall CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>(CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *this, CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation> *__that)
{
  CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>(
    this,
    __that);
}
