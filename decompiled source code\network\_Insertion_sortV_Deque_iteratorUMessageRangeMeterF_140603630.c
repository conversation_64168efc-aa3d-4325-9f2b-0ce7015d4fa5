/*
 * Function: ??$_Insertion_sort@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x140603630
 */

int std::_Insertion_sort<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>()
{
  __int64 v0; // rax@1
  __int64 v1; // rax@1
  __int64 v2; // rax@1
  char v4; // [sp+20h] [bp-B8h]@1
  char *v5; // [sp+40h] [bp-98h]@1
  char v6; // [sp+48h] [bp-90h]@1
  char *v7; // [sp+68h] [bp-70h]@1
  char v8; // [sp+70h] [bp-68h]@1
  char *v9; // [sp+90h] [bp-48h]@1
  __int64 v10; // [sp+98h] [bp-40h]@1
  __int64 v11; // [sp+A0h] [bp-38h]@1
  __int64 v12; // [sp+A8h] [bp-30h]@1
  __int64 v13; // [sp+B0h] [bp-28h]@1
  __int64 v14; // [sp+B8h] [bp-20h]@1
  __int64 v15; // [sp+C0h] [bp-18h]@1

  v10 = -2i64;
  v5 = &v4;
  v7 = &v6;
  v9 = &v8;
  v0 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v4);
  v11 = v0;
  LODWORD(v1) = std::_Val_type<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(v0);
  v12 = v1;
  v2 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v7);
  v13 = v2;
  v14 = v2;
  v15 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v9);
  std::_Insertion_sort1<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,CryptoPP::MeterFilter::MessageRange>(
    v15,
    v14,
    v12);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
