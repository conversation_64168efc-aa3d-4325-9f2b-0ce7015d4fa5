/*
 * Function: ?Create@?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@UEAA_NPEAU?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@2@P6AKPEAX@Z@Z
 * Address: 0x14041D890
 */

char __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Create(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *this, US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *pThreadParam, unsigned int (__cdecl *pDefaultThread)(void *))
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *lpParameter; // [sp+40h] [bp+8h]@1
  US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *Src; // [sp+48h] [bp+10h]@1
  unsigned int (__cdecl *lpStartAddress)(void *); // [sp+50h] [bp+18h]@1

  lpStartAddress = pDefaultThread;
  Src = pThreadParam;
  lpParameter = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  lpParameter->m_hStartupEvent = CreateEventA(0i64, 0, 0, 0i64);
  if ( lpParameter->m_hStartupEvent )
  {
    lpParameter->m_hDestroyEvent = CreateEventA(0i64, 0, 0, 0i64);
    if ( lpParameter->m_hDestroyEvent )
    {
      memcpy_s(&lpParameter->m_ThreadParam, 0x28ui64, Src, 0x28ui64);
      lpParameter->m_hThread = CreateThread(0i64, 0i64, lpStartAddress, lpParameter, 0, &lpParameter->m_dwThreadID);
      if ( lpParameter->m_hThread )
      {
        WaitForSingleObject(lpParameter->m_hStartupEvent, 0xFFFFFFFF);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
