/*
 * Function: j_??D?$_Deque_const_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@QEBAAEBURECV_DATA@@XZ
 * Address: 0x14000C2C5
 */

RECV_DATA *__fastcall std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*(std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *this)
{
  return std::_Deque_const_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::operator*(this);
}
