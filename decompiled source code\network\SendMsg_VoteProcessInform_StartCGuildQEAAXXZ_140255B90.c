/*
 * Function: ?SendMsg_VoteProcessInform_Start@CGuild@@QEAAXXZ
 * Address: 0x140255B90
 */

void __fastcall CGuild::SendMsg_VoteProcessInform_Start(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@21
  __int64 v4; // [sp+0h] [bp-118h]@1
  int v5; // [sp+30h] [bp-E8h]@5
  int j; // [sp+34h] [bp-E4h]@5
  _guild_vote_process_inform_zocl Dst; // [sp+50h] [bp-C8h]@10
  char pbyType; // [sp+E4h] [bp-34h]@14
  char v9; // [sp+E5h] [bp-33h]@14
  _guild_member_info *v10; // [sp+F8h] [bp-20h]@17
  unsigned __int64 v11; // [sp+108h] [bp-10h]@4
  CGuild *v12; // [sp+120h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v12->m_bNowProcessSgtMter )
  {
    v5 = 0;
    for ( j = 0; j < v12->m_SuggestedMatter.nTotal_VotableMemNum; ++j )
    {
      if ( v12->m_SuggestedMatter.VotableMem[j]->pPlayer )
        ++v5;
    }
    _guild_vote_process_inform_zocl::_guild_vote_process_inform_zocl(&Dst);
    memset_0(&Dst, 0, 0x74ui64);
    Dst.bStart = 1;
    Dst.byMatterType = v12->m_SuggestedMatter.byMatterType;
    Dst.dwMatterDst = v12->m_SuggestedMatter.dwMatterDst;
    Dst.dwMatterObj1 = v12->m_SuggestedMatter.dwMatterObj1;
    Dst.dwMatterObj2 = v12->m_SuggestedMatter.dwMatterObj2;
    Dst.dwMatterObj3 = v12->m_SuggestedMatter.dwMatterObj3;
    Dst.dwMatterVoteSynKey = v12->m_SuggestedMatter.dwMatterVoteSynKey;
    Dst.dwSuggesterSerial = v12->m_dwSuggesterSerial;
    Dst.byTotalSeniorNum = v12->m_SuggestedMatter.nTotal_VotableMemNum;
    Dst.byLoginSeniorNum = v5;
    Dst.bActed = 0;
    Dst.byApprPoint = v12->m_SuggestedMatter.byVoteState[0];
    Dst.byOppoPoint = v12->m_SuggestedMatter.byVoteState[1];
    Dst.byCommentLen = strlen_0(v12->m_SuggestedMatter.wszComment);
    strcpy_0(Dst.wszComment, v12->m_SuggestedMatter.wszComment);
    if ( Dst.byMatterType == 4 )
    {
      strcpy_0(Dst.wszDestGuildName, v12->m_GuildBattleSugestMatter.pkDest->m_wszName);
      Dst.byDestGuildGrade = v12->m_GuildBattleSugestMatter.pkDest->m_byGrade;
      Dst.byDestGuildRace = v12->m_GuildBattleSugestMatter.pkDest->m_byRace;
    }
    else if ( Dst.byMatterType == 5 )
    {
      strcpy_0(Dst.wszDestGuildName, v12->m_GuildBattleSugestMatter.pkSrc->m_wszName);
      Dst.byDestGuildGrade = v12->m_GuildBattleSugestMatter.pkSrc->m_byGrade;
      Dst.byDestGuildRace = v12->m_GuildBattleSugestMatter.pkSrc->m_byRace;
    }
    pbyType = 27;
    v9 = 24;
    for ( j = 0; j < 50; ++j )
    {
      v10 = &v12->m_MemberData[j];
      if ( _guild_member_info::IsFill(v10) && v10->pPlayer && (Dst.byMatterType != 5 || v10->byClassInGuild == 2) )
      {
        v3 = _guild_vote_process_inform_zocl::size(&Dst);
        CNetProcess::LoadSendMsg(unk_1414F2088, v10->pPlayer->m_ObjID.m_wIndex, &pbyType, (char *)&Dst.bStart, v3);
      }
    }
  }
}
