/*
 * Function: j_??$_Uninit_copy@PEAVCMoveMapLimitRightInfo@@PEAV1@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAPEAVCMoveMapLimitRightInfo@@PEAV1@00AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140009624
 */

CMoveMapLimitRightInfo *__fastcall std::_Uninit_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, CMoveMapLimitRightInfo *_Dest, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CMoveMapLimitRightInfo *,CMoveMapLimitRightInfo *,std::allocator<CMoveMapLimitRightInfo>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
