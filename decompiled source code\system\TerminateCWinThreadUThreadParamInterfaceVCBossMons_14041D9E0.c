/*
 * Function: ?Terminate@?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@UEAAXK@Z
 * Address: 0x14041D9E0
 */

void __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Terminate(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *this, unsigned int dwWaitTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  DWORD v5; // [sp+20h] [bp-18h]@6
  US::AbstractThreadPool *v6; // [sp+28h] [bp-10h]@8
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *v7; // [sp+40h] [bp+8h]@1
  unsigned int dwMilliseconds; // [sp+48h] [bp+10h]@1

  dwMilliseconds = dwWaitTime;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_bRunning )
  {
    if ( v7->m_hThread )
    {
      v7->m_bRunning = 0;
      SetEvent(v7->m_hDestroyEvent);
      v5 = WaitForSingleObject(v7->m_hThread, dwMilliseconds);
      if ( v5 == 258 )
        TerminateThread(v7->m_hThread, 0);
    }
  }
  CloseHandle(v7->m_hThread);
  v7->m_hThread = 0i64;
  v7->m_dwThreadID = -1;
  CloseHandle(v7->m_hStartupEvent);
  v7->m_hStartupEvent = 0i64;
  CloseHandle(v7->m_hDestroyEvent);
  v7->m_hDestroyEvent = 0i64;
  v6 = US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::GetMyThreadPool(&v7->m_ThreadParam);
  if ( v6 )
    ((void (__fastcall *)(US::AbstractThreadPool *, US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *))v6->vfptr->NotifyTerminate)(
      v6,
      v7);
}
