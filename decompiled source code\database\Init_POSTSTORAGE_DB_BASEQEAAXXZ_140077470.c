/*
 * Function: ?Init@_POSTSTORAGE_DB_BASE@@QEAAXXZ
 * Address: 0x140077470
 */

void __fastcall _POSTSTORAGE_DB_BASE::Init(_POSTSTORAGE_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _POSTSTORAGE_DB_BASE *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x3A03ui64);
  for ( j = 0; j < 50; ++j )
  {
    Dst->m_PostList[j].byState = -1;
    Dst->m_PostList[j].bRetProc = 1;
  }
}
