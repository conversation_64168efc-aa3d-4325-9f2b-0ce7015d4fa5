/*
 * Function: SQLReadFileDSNW
 * Address: 0x1404DB378
 */

int __fastcall SQLReadFileDSNW(const unsigned __int16 *lpszFileName, const unsigned __int16 *lpszAppName, const unsigned __int16 *lpszKeyName, unsigned __int16 *lpszString, unsigned __int16 cbString, unsigned __int16 *pcbString)
{
  const unsigned __int16 *v6; // rbp@1
  unsigned __int16 *v7; // rbx@1
  const unsigned __int16 *v8; // rdi@1
  const unsigned __int16 *v9; // rsi@1
  __int64 (__cdecl *v10)(); // rax@1
  int result; // eax@2

  v6 = lpszFileName;
  v7 = lpszString;
  v8 = lpszKeyName;
  v9 = lpszAppName;
  v10 = ODBC___GetSetupProc("SQLReadFileDSNW");
  if ( v10 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *, unsigned __int16 *))v10)(
               v6,
               v9,
               v8,
               v7);
  else
    result = 0;
  return result;
}
