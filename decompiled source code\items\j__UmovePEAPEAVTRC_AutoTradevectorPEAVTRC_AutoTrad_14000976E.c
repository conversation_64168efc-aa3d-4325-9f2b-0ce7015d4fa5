/*
 * Function: j_??$_Umove@PEAPEAVTRC_AutoTrade@@@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAPEAPEAVTRC_AutoTrade@@PEAPEAV2@00@Z
 * Address: 0x14000976E
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Umove<TRC_AutoTrade * *>(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, TRC_AutoTrade **_First, TRC_AutoTrade **_Last, TRC_AutoTrade **_Ptr)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Umove<TRC_AutoTrade * *>(
           this,
           _First,
           _Last,
           _Ptr);
}
