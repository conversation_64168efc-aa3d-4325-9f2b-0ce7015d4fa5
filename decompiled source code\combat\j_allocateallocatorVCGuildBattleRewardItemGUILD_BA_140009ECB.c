/*
 * Function: j_?allocate@?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@_K@Z
 * Address: 0x140009ECB
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocate(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, unsigned __int64 _Count)
{
  return std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocate(this, _Count);
}
