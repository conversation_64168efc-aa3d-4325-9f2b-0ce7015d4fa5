/*
 * Function: ??0_equip_db_load@@QEAA@XZ
 * Address: 0x14010DB40
 */

void __fastcall _equip_db_load::_equip_db_load(_equip_db_load *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  _equip_db_load *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _STORAGE_LIST::_STORAGE_LIST((_STORAGE_LIST *)&v4->m_nListNum);
  `vector constructor iterator'(v4->m_List, 0x32ui64, 8, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  _STORAGE_LIST::SetMemory((_STORAGE_LIST *)&v4->m_nListNum, v4->m_List, 1, 8, 0);
}
