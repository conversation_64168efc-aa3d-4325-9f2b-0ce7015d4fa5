/*
 * Function: ?IsolatedInitialize@<PERSON>@ByteQueue@CryptoPP@@UEAAXAEBVNameValuePairs@3@@Z
 * Address: 0x14054A5C0
 */

void __fastcall CryptoPP::ByteQueue::Walker::IsolatedInitialize(CryptoPP::ByteQueue::Walker *this, const struct CryptoPP::NameValuePairs *a2)
{
  this->m_node = this->m_queue->m_head;
  this->m_position = 0i64;
  this->m_offset = 0i64;
  this->m_lazyString = this->m_queue->m_lazyString;
  this->m_lazyLength = this->m_queue->m_lazyLength;
}
