/*
 * Function: _CUnmannedTraderGroupIDInfo::GetGroupID_::_1_::dtor$2
 * Address: 0x140386880
 */

void __fastcall CUnmannedTraderGroupIDInfo::GetGroupID_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>((std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *)(a2 + 120));
}
