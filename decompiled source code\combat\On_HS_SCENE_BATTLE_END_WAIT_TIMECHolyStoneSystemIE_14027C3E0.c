/*
 * Function: ?On_HS_SCENE_BATTLE_END_WAIT_TIME@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027C3E0
 */

void __fastcall CHolyStoneSystem::On_HS_SCENE_BATTLE_END_WAIT_TIME(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CRaceBossWinRate *v3; // rax@5
  CPvpUserAndGuildRankingSystem *v4; // rax@6
  CPvpUserAndGuildRankingSystem *v5; // rax@6
  CPvpUserAndGuildRankingSystem *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-68h]@1
  char v8; // [sp+38h] [bp-30h]@6
  unsigned int v9; // [sp+3Ch] [bp-2Ch]@6
  char v10; // [sp+40h] [bp-28h]@6
  char v11; // [sp+41h] [bp-27h]@6
  unsigned int v12; // [sp+44h] [bp-24h]@6
  unsigned int v13; // [sp+48h] [bp-20h]@6
  unsigned int v14; // [sp+4Ch] [bp-1Ch]@6
  int j; // [sp+54h] [bp-14h]@6
  CHolyStoneSystem *v16; // [sp+70h] [bp+8h]@1

  v16 = this;
  v1 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CHolyStoneSystem::DestroyHolyStone(v16);
  if ( CHolyStoneSystem::GetHolyMasterRace(v16) == -1 )
  {
    CHolyStoneSystem::SendMsg_ExitStone(v16);
    CHolyStoneSystem::SendMsg_EndBattle(v16, -1);
    v3 = CRaceBossWinRate::Instance();
    CRaceBossWinRate::UpdateTotalCnt(v3);
    if ( unk_1799C608D )
    {
      v8 = v16->m_SaveData.m_byNumOfTime;
      v9 = GetKorLocalTime();
      v10 = -1;
      v11 = -1;
      v4 = CPvpUserAndGuildRankingSystem::Instance();
      v12 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, 0, 0);
      v5 = CPvpUserAndGuildRankingSystem::Instance();
      v13 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, 1, 0);
      v6 = CPvpUserAndGuildRankingSystem::Instance();
      v14 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v6, 2, 0);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -119, &v8, 24);
      for ( j = 0; j < 3; ++j )
        CHolyStoneSystem::PeneltyFailRace(v16, j);
    }
  }
  CHolyStoneSystem::SendNotifyHolyStoneDestroyedToRaceBoss(v16);
}
