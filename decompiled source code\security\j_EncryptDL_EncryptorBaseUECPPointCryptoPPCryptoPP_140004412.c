/*
 * Function: j_?Encrypt@?$DL_EncryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x140004412
 */

void __fastcall CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>::Encrypt(CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *this, CryptoPP::RandomNumberGenerator *rng, const char *plaintext, unsigned __int64 plaintextLength, char *ciphertext, CryptoPP::NameValuePairs *parameters)
{
  CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>::Encrypt(this, rng, plaintext, plaintextLength, ciphertext, parameters);
}
