/*
 * Function: ?SendMsg@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAEPEADIH@Z
 * Address: 0x1403E2800
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(GUILD_BATTLE::CNormalGuildBattleGuild *this, char *byType, char *pMsg, unsigned int uiSize, int iExeceptMemberInx)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@10
  __int64 v8; // [sp+0h] [bp-48h]@1
  int j; // [sp+30h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+50h] [bp+8h]@1
  char *pbyType; // [sp+58h] [bp+10h]@1
  char *szMsg; // [sp+60h] [bp+18h]@1
  unsigned int v13; // [sp+68h] [bp+20h]@1

  v13 = uiSize;
  szMsg = pMsg;
  pbyType = byType;
  v10 = this;
  v5 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  for ( j = 0; j < 50; ++j )
  {
    if ( iExeceptMemberInx != j && GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v10->m_kMember[j]) )
    {
      v7 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v10->m_kMember[j]);
      CNetProcess::LoadSendMsg(unk_1414F2088, v7, pbyType, szMsg, v13);
    }
  }
}
