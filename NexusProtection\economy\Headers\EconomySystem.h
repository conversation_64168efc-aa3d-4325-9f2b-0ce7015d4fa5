#pragma once

#include "EconomyTypes.h"
#include <array>
#include <chrono>
#include <string>
#include <mutex>

namespace NexusProtection::Economy {

    /**
     * @brief Economy calculation data structure
     */
    struct EconomyCalculationData {
        std::array<double, static_cast<size_t>(RaceType::Count)> tradeDalant{};
        std::array<double, static_cast<size_t>(RaceType::Count)> tradeGold{};
        std::array<std::array<double, static_cast<size_t>(RaceType::Count)>, 
                   static_cast<size_t>(RaceType::Count)> oreMineCount{};
        std::array<std::array<double, static_cast<size_t>(RaceType::Count)>, 
                   static_cast<size_t>(RaceType::Count)> oreCutCount{};

        void Initialize() noexcept {
            for (auto& dalant : tradeDalant) dalant = 1.0;
            for (auto& gold : tradeGold) gold = 1.0;
            
            for (auto& raceArray : oreMineCount) {
                for (auto& count : raceArray) count = 1.0;
            }
            
            for (auto& raceArray : oreCutCount) {
                for (auto& count : raceArray) count = 1.0;
            }
        }
    };

    /**
     * @brief Economy history data structure
     */
    struct EconomyHistoryData {
        std::array<double, static_cast<size_t>(RaceType::Count)> oldDalant{};
        std::array<double, static_cast<size_t>(RaceType::Count)> currentDalant{};
        std::array<double, static_cast<size_t>(RaceType::Count)> bufferTradeDalant{};
        
        void Initialize() noexcept {
            oldDalant.fill(0.0);
            currentDalant.fill(0.0);
            bufferTradeDalant.fill(0.0);
        }
    };

    /**
     * @brief Main Economy System class
     * 
     * Manages global economy state including:
     * - Currency exchange rates
     * - Trade money calculations
     * - Ore mining and cutting statistics
     * - Economic history tracking
     */
    class EconomySystem {
    public:
        // Constructor and initialization
        EconomySystem();
        ~EconomySystem() = default;

        void Initialize();
        void InitializeTradeMoneySystem();

        // Currency operations
        void AddDalant(RaceType race, int32_t amount);
        double GetDalant(RaceType race) const;
        double GetOldDalant(RaceType race) const;

        // System state
        bool IsLoaded() const noexcept { return m_isLoaded; }
        void SetLoaded(bool loaded) noexcept { m_isLoaded = loaded; }

        // Timing
        std::chrono::steady_clock::time_point GetLastUpdateTime() const noexcept { return m_lastUpdateTime; }
        std::chrono::steady_clock::time_point GetSystemStartTime() const noexcept { return m_systemStartTime; }

        // Data access
        const EconomyCalculationData& GetCurrentTradeData() const noexcept { return m_currentTradeData; }
        const EconomyCalculationData& GetBufferTradeData() const noexcept { return m_bufferTradeData; }
        const EconomyHistoryData& GetHistoryData() const noexcept { return m_historyData; }

        // Update operations
        void UpdateEconomySystem(bool* changeDay);
        void UpdateNewEconomy(const EconomyCalculationData& data);

        // Configuration
        bool ReadEconomyIniFile();

        // Legacy C interface compatibility
        void AddDalant(int32_t raceCode, int32_t amount);
        double GetDalant(int32_t raceCode) const;
        double GetOldDalant(int32_t raceCode) const;

    private:
        // Internal methods
        void UpdateTradeRates();
        void ProcessDailyUpdate();
        RaceType ConvertLegacyRace(int32_t raceCode) const noexcept;

        // Member variables
        EconomyCalculationData m_currentTradeData;
        EconomyCalculationData m_bufferTradeData;
        EconomyHistoryData m_historyData;

        bool m_isLoaded{false};
        std::chrono::steady_clock::time_point m_lastUpdateTime;
        std::chrono::steady_clock::time_point m_systemStartTime;

        mutable std::mutex m_dataMutex;

        // Configuration values
        int32_t m_defaultOreValue{0};
    };

    // Legacy C interface
    extern "C" {
        struct _ECONOMY_SYSTEM {
            bool m_bLoad;
            double m_dCurTradeGold[3];
            double m_dCurTradeDalant[3];
            double m_dCurOreMineCount[3][3];
            double m_dCurOreCutCount[3][3];
            double m_dBufTradeGold[3];
            double m_dBufTradeDalant[3];
            double m_dBufOreMineCount[3][3];
            double m_dBufOreCutCount[3][3];
            uint32_t m_dwLastUpdateTime;
            uint32_t m_dwSystemOperStartTime;
        };

        struct _economy_calc_data {
            double dTradeDalant[3];
            double dTradeGold[3];
            double dOreMineCount[3][3];
            double dOreCutCount[3][3];
        };

        struct _economy_history_data {
            double dOldDalant[3];
            double dCurDalant[3];
            double dBufTradeDalant[3];
        };

        // Legacy function declarations
        void _ECONOMY_SYSTEM_Init(_ECONOMY_SYSTEM* system);
        void _ECONOMY_SYSTEM_CurTradeMoneyInit(_ECONOMY_SYSTEM* system);
        char _ReadEconomyIniFile();
        void _UpdateNewEconomy(_economy_calc_data* data);
        void eUpdateEconomySystem(bool* changeDay);
        void eAddDalant(int nRaceCode, int nAdd);
        double eGetDalant(int nRaceCode);
        double eGetOldDalant(int nRaceCode);
        _economy_history_data* eGetGuideHistory();
    }

    // Global instance
    extern EconomySystem g_EconomySystem;

} // namespace NexusProtection::Economy

// Legacy global compatibility
extern NexusProtection::Economy::_ECONOMY_SYSTEM e_EconomySystem;
