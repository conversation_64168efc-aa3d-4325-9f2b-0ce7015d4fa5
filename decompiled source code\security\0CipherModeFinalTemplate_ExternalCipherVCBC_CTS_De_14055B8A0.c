/*
 * Function: ??0?$CipherModeFinalTemplate_ExternalCipher@VCBC_CTS_Decryption@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14055B8A0
 */

CryptoPP::CBC_CTS_Decryption *__fastcall CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>(CryptoPP::CBC_CTS_Decryption *a1)
{
  CryptoPP::CBC_CTS_Decryption *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::CBC_CTS_Decryption::CBC_CTS_Decryption(a1);
  v2->vfptr = (CryptoPP::ClonableVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::`vftable'{for `CryptoPP::StreamTransformation'};
  v2->vfptr = (CryptoPP::SimpleKeyingInterfaceVtbl *)&CryptoPP::CipherModeFinalTemplate_ExternalCipher<CryptoPP::CBC_CTS_Decryption>::`vftable'{for `CryptoPP::SimpleKeyingInterface'};
  return v2;
}
