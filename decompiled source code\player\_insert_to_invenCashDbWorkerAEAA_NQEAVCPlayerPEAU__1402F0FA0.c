/*
 * Function: ?_insert_to_inven@CashDbWorker@@AEAA_NQEAVCPlayer@@PEAU__item@_param_cash_update@@@Z
 * Address: 0x1402F0FA0
 */

char __fastcall CashDbWorker::_insert_to_inven(CashDbWorker *this, CPlayer *const pOne, _param_cash_update::__item *pGII)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@7
  int v6; // ecx@10
  char *v7; // rax@10
  char result; // al@10
  int v9; // ecx@14
  int v10; // edx@14
  __int64 v11; // [sp+0h] [bp-F8h]@1
  bool bAdd[4]; // [sp+20h] [bp-D8h]@10
  int nNum; // [sp+28h] [bp-D0h]@14
  int nBuyPrice; // [sp+30h] [bp-C8h]@14
  int nAmount; // [sp+38h] [bp-C0h]@14
  char *pFileName; // [sp+40h] [bp-B8h]@14
  unsigned __int64 lnUID; // [sp+48h] [bp-B0h]@14
  char byEventType; // [sp+50h] [bp-A8h]@14
  _base_fld *v19; // [sp+60h] [bp-98h]@4
  _STORAGE_LIST::_db_con v20; // [sp+78h] [bp-80h]@4
  int v21; // [sp+B4h] [bp-44h]@4
  _TimeItem_fld *v22; // [sp+B8h] [bp-40h]@7
  __time32_t Time; // [sp+C4h] [bp-34h]@8
  _STORAGE_LIST::_db_con *v24; // [sp+D8h] [bp-20h]@9
  int nCashType; // [sp+E0h] [bp-18h]@11
  int v26; // [sp+E4h] [bp-14h]@10
  int nTableCode; // [sp+E8h] [bp-10h]@10
  CashDbWorker *v28; // [sp+100h] [bp+8h]@1
  CPlayer *v29; // [sp+108h] [bp+10h]@1
  _param_cash_update::__item *v30; // [sp+110h] [bp+18h]@1

  v30 = pGII;
  v29 = pOne;
  v28 = this;
  v3 = &v11;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pGII->in_byTblCode, pGII->in_wItemIdx);
  _STORAGE_LIST::_db_con::_db_con(&v20);
  v21 = 0;
  v20.m_byTableCode = v30->in_byTblCode;
  v20.m_wItemIndex = v30->in_wItemIdx;
  if ( IsOverLapItem(v30->in_byTblCode) )
    v20.m_dwDur = v30->in_byOverlapNum;
  else
    v20.m_dwDur = GetItemDurPoint(v30->in_byTblCode, v30->in_wItemIdx);
  v5 = GetDefItemUpgSocketNum(v30->in_byTblCode, v30->in_wItemIdx);
  v20.m_dwLv = (unsigned __int8)v5;
  v20.m_dwLv = GetBitAfterSetLimSocket(v5);
  v20.m_wSerial = CPlayerDB::GetNewItemSerial(&v29->m_Param);
  v30->out_wItemSerial = v20.m_wSerial;
  v22 = TimeItem::FindTimeRec((unsigned __int8)v20.m_byTableCode, v20.m_wItemIndex);
  if ( v30->in_nLendType )
  {
    v20.m_byCsMethod = v22->m_nCheckType;
    _time32(&Time);
    v20.m_dwT = v30->in_dwLendTime + Time;
    v20.m_dwLendRegdTime = Time;
  }
  v24 = CPlayer::Emb_AddStorage(v29, 0, (_STORAGE_LIST::_storage_con *)&v20.m_bLoad, 0, 1);
  if ( v24 )
  {
    nCashType = GetUsePcCashType(v24->m_byTableCode, v24->m_wItemIndex);
    if ( v24->m_byTableCode == 18 && !CPlayer::IsUsableAccountType(v29, nCashType) )
      CPlayer::SendMsg_PremiumCashItemUse(v29, v24->m_wSerial);
    v30->out_dwT = v24->m_dwT;
    v9 = v30->in_byOverlapNum;
    v10 = v30->in_nDiscount;
    byEventType = v30->in_nEventType;
    lnUID = v30->in_lnUID;
    pFileName = v29->m_szItemHistoryFileName;
    nAmount = v30->out_nCashAmount;
    nBuyPrice = v30->out_nBuyPrice;
    nNum = v9;
    *(_DWORD *)bAdd = v10;
    CMgrAvatorItemHistory::buy_to_inven_cashitem(
      &CPlayer::s_MgrItemHistory,
      v30->in_byTblCode,
      v30->in_wItemIdx,
      v30->in_nPrice,
      v10,
      v9,
      nBuyPrice,
      nAmount,
      v29->m_szItemHistoryFileName,
      lnUID,
      byEventType);
    result = 1;
  }
  else
  {
    v26 = v30->in_nDiscount;
    v6 = v30->in_wItemIdx;
    nTableCode = v30->in_byTblCode;
    v7 = GetItemKorName(nTableCode, v6);
    *(_DWORD *)bAdd = v26;
    CLogFile::Write(v28->_kLogger, "Failed _insert_to_inven() >> %s [price:%d discount:%d]", v7, v30->in_nPrice);
    result = 0;
  }
  return result;
}
