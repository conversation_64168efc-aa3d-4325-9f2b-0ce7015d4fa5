/*
 * Function: ?SendMsg_Enter<PERSON>eeper@CHolyStoneSystem@@QEAAXH@Z
 * Address: 0x14027F510
 */

void __fastcall CHolyStoneSystem::SendMsg_EnterKeeper(CHolyStoneSystem *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  CHolyScheduleData::__HolyScheduleNode *v6; // [sp+30h] [bp-68h]@4
  unsigned int v7; // [sp+38h] [bp-60h]@5
  char szMsg; // [sp+48h] [bp-50h]@5
  __int16 v9; // [sp+49h] [bp-4Fh]@5
  __int16 v10; // [sp+4Bh] [bp-4Dh]@6
  __int16 v11; // [sp+4Dh] [bp-4Bh]@9
  char v12; // [sp+4Fh] [bp-49h]@11
  bool v13; // [sp+50h] [bp-48h]@11
  bool v14; // [sp+51h] [bp-47h]@11
  unsigned int v15; // [sp+64h] [bp-34h]@5
  char pbyType; // [sp+74h] [bp-24h]@11
  char v17; // [sp+75h] [bp-23h]@11
  unsigned int dwClientIndex; // [sp+84h] [bp-14h]@12
  bool v19; // [sp+88h] [bp-10h]@11
  bool v20; // [sp+89h] [bp-Fh]@11
  CHolyStoneSystem *v21; // [sp+A0h] [bp+8h]@1
  int v22; // [sp+A8h] [bp+10h]@1

  v22 = n;
  v21 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CHolyStoneSystem::GetNumOfTime(v21);
  v6 = CHolyScheduleData::GetIndex(&v21->m_ScheculeData, (unsigned __int8)v4);
  if ( v6 )
  {
    v7 = GetLoopTime();
    szMsg = v22 == -1;
    v9 = (v21->m_dwCheckTime[6] - v21->m_dwCheckTime[2]) / 0x3E8;
    v15 = v21->m_dwCheckTime[6] - v6->m_nSceneTime[0];
    if ( v15 <= v7 )
      v10 = -(v7 - v15) / 0x3E8;
    else
      v10 = (v15 - v7) / 0x3E8;
    if ( v21->m_dwCheckTime[6] <= v7 )
      v11 = -(v7 - v21->m_dwCheckTime[6]) / 0x3E8;
    else
      v11 = (v21->m_dwCheckTime[6] - v7) / 0x3E8;
    v12 = CHolyStoneSystem::GetHolyMasterRace(v21);
    v19 = CHolyStoneSystem::GetSceneCode(v21) == 6;
    v13 = v19;
    v20 = CHolyStoneSystem::GetSceneCode(v21) == 3;
    v14 = v20;
    pbyType = 25;
    v17 = 3;
    if ( v22 == -1 )
    {
      for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
      {
        if ( *(&g_Player.m_bLive + 50856 * (signed int)dwClientIndex) )
          CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 0xAu);
      }
    }
    else
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v22, &pbyType, &szMsg, 0xAu);
    }
  }
}
