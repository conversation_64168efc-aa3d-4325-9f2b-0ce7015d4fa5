/*
 * Function: ?Set@CGuildBattleSchedule@GUILD_BATTLE@@QEAAEKK@Z
 * Address: 0x1403D9B90
 */

char __fastcall GUILD_BATTLE::CGuildBattleSchedule::Set(GUILD_BATTLE::CGuildBattleSchedule *this, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@6
  ATL::CTimeSpan *v6; // rax@7
  ATL::CTime *v7; // rax@7
  int v8; // eax@7
  ATL::CTimeSpan *v9; // rax@7
  ATL::CTimeSpan *v10; // rax@7
  ATL::CTimeSpan *v11; // rax@7
  ATL::CTimeSpan *v12; // rax@7
  ATL::CTimeSpan *v13; // rax@7
  __int64 *v14; // rax@7
  __int64 v15; // [sp+0h] [bp-E8h]@1
  ATL::CTime v16; // [sp+48h] [bp-A0h]@7
  ATL::CTime v17; // [sp+68h] [bp-80h]@7
  ATL::CTime result; // [sp+78h] [bp-70h]@7
  ATL::CTimeSpan v19; // [sp+80h] [bp-68h]@7
  ATL::CTimeSpan v20; // [sp+88h] [bp-60h]@7
  ATL::CTimeSpan v21; // [sp+90h] [bp-58h]@7
  ATL::CTimeSpan v22; // [sp+98h] [bp-50h]@7
  ATL::CTimeSpan v23; // [sp+A0h] [bp-48h]@7
  ATL::CTimeSpan v24; // [sp+A8h] [bp-40h]@7
  ATL::CTimeSpan v25; // [sp+B0h] [bp-38h]@7
  ATL::CTimeSpan *v26; // [sp+B8h] [bp-30h]@7
  int nDay; // [sp+C0h] [bp-28h]@7
  int nMonth; // [sp+C4h] [bp-24h]@7
  ATL::CTimeSpan *v29; // [sp+C8h] [bp-20h]@7
  ATL::CTimeSpan *v30; // [sp+D0h] [bp-18h]@7
  GUILD_BATTLE::CGuildBattleSchedule *v31; // [sp+F0h] [bp+8h]@1
  unsigned int nHour; // [sp+F8h] [bp+10h]@1
  unsigned int v33; // [sp+100h] [bp+18h]@1

  v33 = dwElapseTimeCnt;
  nHour = dwStartTimeInx;
  v31 = this;
  v3 = &v15;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dwStartTimeInx && dwStartTimeInx < 0x17 )
  {
    ATL::CTimeSpan::CTimeSpan(&v19, 1, 0, 0, 0);
    v26 = v6;
    v7 = ATL::CTime::GetTickCount(&result);
    ATL::CTime::operator+(v7, &v16, (ATL::CTimeSpan)v26->m_timeSpan);
    nDay = ATL::CTime::GetDay(&v16);
    nMonth = ATL::CTime::GetMonth(&v16);
    v8 = ATL::CTime::GetYear(&v16);
    ATL::CTime::CTime(&v17, v8, nMonth, nDay, nHour, 0, 0, -1);
    v31->m_kBattleStartTime = v17;
    ATL::CTimeSpan::CTimeSpan(&v23, 0, 0, 1, 0);
    v29 = v9;
    ATL::CTimeSpan::CTimeSpan(&v21, 0, 0, 4, 0);
    v30 = v10;
    ATL::CTimeSpan::CTimeSpan(&v20, 0, 0, 5, 0);
    v12 = ATL::CTimeSpan::operator+(v11, &v22, (ATL::CTimeSpan)v30->m_timeSpan);
    v13 = ATL::CTimeSpan::operator+(v12, &v24, (ATL::CTimeSpan)v29->m_timeSpan);
    ATL::CTime::operator-=(&v17, (ATL::CTimeSpan)v13->m_timeSpan);
    v31->m_kNextStartTime = v17;
    ATL::CTimeSpan::CTimeSpan(&v25, 0, 0, 30 * v33, 0);
    v31->m_kBattleTime.m_timeSpan = *v14;
    v31->m_eState = 1;
    v5 = 0;
  }
  else
  {
    v5 = 122;
  }
  return v5;
}
