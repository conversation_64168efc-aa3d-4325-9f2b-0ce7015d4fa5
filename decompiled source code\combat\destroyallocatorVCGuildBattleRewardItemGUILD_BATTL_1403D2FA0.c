/*
 * Function: ?destroy@?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@@Z
 * Address: 0x1403D2FA0
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::destroy(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1

  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Destroy<GUILD_BATTLE::CGuildBattleRewardItem>(_Ptr);
}
