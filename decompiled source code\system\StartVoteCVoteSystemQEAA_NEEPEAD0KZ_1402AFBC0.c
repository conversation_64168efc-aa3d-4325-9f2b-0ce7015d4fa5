/*
 * Function: ?StartVote@CVoteSystem@@QEAA_NEEPEAD0K@Z
 * Address: 0x1402AFBC0
 */

char __fastcall CVoteSystem::StartVote(CVoteSystem *this, char byRaceCode, char byPunishType, char *pwszContent, char *pwszName, unsigned int dwSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPvpUserAndGuildRankingSystem *v9; // rax@13
  unsigned __int16 v10; // ax@14
  unsigned int v11; // eax@15
  __int64 v12; // [sp+0h] [bp-5A8h]@1
  _starting_vote_inform_zocl v13; // [sp+40h] [bp-568h]@6
  char pbyType; // [sp+564h] [bp-44h]@6
  char v15; // [sp+565h] [bp-43h]@6
  int j; // [sp+574h] [bp-34h]@6
  CPlayer *v17; // [sp+578h] [bp-30h]@9
  unsigned int v18; // [sp+588h] [bp-20h]@13
  int v19; // [sp+58Ch] [bp-1Ch]@13
  unsigned __int64 v20; // [sp+590h] [bp-18h]@4
  CVoteSystem *v21; // [sp+5B0h] [bp+8h]@1
  char v22; // [sp+5C0h] [bp+18h]@1
  const char *Source; // [sp+5C8h] [bp+20h]@1

  Source = pwszContent;
  v22 = byPunishType;
  v21 = this;
  v6 = &v12;
  for ( i = 360i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v20 = (unsigned __int64)&v12 ^ _security_cookie;
  if ( v21->m_bActive )
  {
    result = 0;
  }
  else
  {
    ++v21->m_nSerial;
    v21->m_byRaceCode = byRaceCode;
    memset_0(v21->m_dwPoint, 0, 0xCui64);
    CNetIndexList::ResetList(&v21->m_listVote);
    v21->m_byLimGrade = 0;
    v21->m_dwLastBroadcastTime = GetLoopTime();
    v21->m_dwStartVoteTime = GetLoopTime();
    v21->m_bActive = 1;
    v21->m_bHurry = 0;
    v21->m_bPunishment = 1;
    v21->m_byPunishType = v22;
    v21->m_dwAvatorSerial = dwSerial;
    strcpy_0(v21->m_wszCharName, pwszName);
    _starting_vote_inform_zocl::_starting_vote_inform_zocl(&v13);
    v13.nVoteSerial = v21->m_nSerial;
    v13.byLimGrade = v21->m_byLimGrade;
    v13.wLeftSec = 300;
    strcpy_0(v13.wszContent, Source);
    v13.wContentSize = strlen_0(Source) + 1;
    pbyType = 26;
    v15 = 3;
    v21->m_SendStarted.nVoteSerial = v21->m_nSerial;
    v21->m_SendStarted.byLimGrade = v21->m_byLimGrade;
    strcpy_0(v21->m_SendStarted.wszContent, Source);
    v21->m_SendStarted.wContentSize = strlen_0(Source) + 1;
    v21->m_SendStarted.wLeftSec = 300;
    for ( j = 0; j < 2532; ++j )
    {
      v17 = &g_Player + j;
      if ( v17->m_bLive && v17->m_bOper && CPlayerDB::GetRaceCode(&v17->m_Param) == v21->m_byRaceCode )
      {
        v18 = CPlayerDB::GetCharSerial(&v17->m_Param);
        v19 = CPlayerDB::GetRaceCode(&v17->m_Param);
        v9 = CPvpUserAndGuildRankingSystem::Instance();
        if ( (unsigned __int8)CPvpUserAndGuildRankingSystem::GetBossType(v9, v19, v18) == 255 )
        {
          v11 = CPlayerDB::GetCharSerial(&v17->m_Param);
          CVoteSystem::SendMsg_StartedVoteInform(v21, v17->m_ObjID.m_wIndex, v11, 1);
        }
        else
        {
          v10 = _starting_vote_inform_zocl::size(&v13);
          CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, (char *)&v13, v10);
        }
      }
    }
    result = 1;
  }
  return result;
}
