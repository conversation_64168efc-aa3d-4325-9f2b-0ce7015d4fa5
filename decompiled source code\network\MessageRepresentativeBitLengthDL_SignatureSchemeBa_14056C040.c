/*
 * Function: ?MessageRepresentativeBitLength@?$DL_SignatureSchemeBase@VPK_Signer@CryptoPP@@V?$DL_PrivateKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C040
 */

__int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::MessageRepresentativeBitLength(__int64 a1)
{
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v1; // rax@1
  CryptoPP::Integer *v2; // rax@1

  v1 = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *)(a1 + 16));
  LODWORD(v2) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *))v1->vfptr[8].__vecDelDtor)(v1);
  return CryptoPP::Integer::BitCount(v2);
}
