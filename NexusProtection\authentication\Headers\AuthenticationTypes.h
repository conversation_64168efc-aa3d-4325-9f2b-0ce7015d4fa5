#pragma once

#include <cstdint>
#include <array>
#include <string>
#include <chrono>
#include <memory>

namespace NexusProtection::Authentication {

    // Forward declarations
    class CBillingManager;
    class CAsyncLogInfo;
    class CHackShieldExSystem;
    class CUserDB;
    class CBilling;

    // Constants
    constexpr size_t MAX_ACCOUNT_NAME_LENGTH = 64;
    constexpr size_t MAX_PASSWORD_LENGTH = 32;
    constexpr size_t MAX_LOG_TYPES = 15;
    constexpr size_t MAX_BILLING_TYPES = 8;
    constexpr size_t MAX_SECURITY_PARAMS = 16;

    // Enumerations
    enum class AuthenticationResult : int32_t {
        Success = 1,
        Failure = 0,
        InvalidCredentials = -1,
        AccountLocked = -2,
        SystemError = -3,
        SecurityViolation = -4,
        BillingError = -5,
        SessionExpired = -6
    };

    enum class BillingType : uint8_t {
        Free = 0,
        Premium = 1,
        VIP = 2,
        Trial = 3,
        Suspended = 4,
        Count = 5
    };

    enum class LogType : uint8_t {
        System = 0,
        Authentication = 1,
        Billing = 2,
        Security = 3,
        Error = 4,
        Debug = 5,
        Count = 6
    };

    enum class SecurityLevel : uint8_t {
        None = 0,
        Basic = 1,
        Enhanced = 2,
        Maximum = 3
    };

    enum class SessionState : uint8_t {
        Disconnected = 0,
        Connecting = 1,
        Connected = 2,
        Authenticated = 3,
        Active = 4,
        Suspended = 5,
        Terminating = 6
    };

    // Data structures
    struct AccountInfo {
        uint32_t accountSerial{0};
        std::string accountName;
        std::string hashedPassword;
        BillingType billingType{BillingType::Free};
        bool isActive{false};
        std::chrono::system_clock::time_point lastLogin;
        std::chrono::system_clock::time_point creationDate;
        uint32_t loginCount{0};
        
        void Reset() noexcept {
            accountSerial = 0;
            accountName.clear();
            hashedPassword.clear();
            billingType = BillingType::Free;
            isActive = false;
            lastLogin = std::chrono::system_clock::time_point{};
            creationDate = std::chrono::system_clock::time_point{};
            loginCount = 0;
        }
        
        bool IsValid() const noexcept {
            return accountSerial > 0 && !accountName.empty() && !hashedPassword.empty();
        }
    };

    struct SessionInfo {
        uint32_t sessionId{0};
        uint32_t accountSerial{0};
        SessionState state{SessionState::Disconnected};
        std::chrono::steady_clock::time_point startTime;
        std::chrono::steady_clock::time_point lastActivity;
        std::string clientIP;
        uint16_t clientPort{0};
        SecurityLevel securityLevel{SecurityLevel::Basic};
        
        void Reset() noexcept {
            sessionId = 0;
            accountSerial = 0;
            state = SessionState::Disconnected;
            startTime = std::chrono::steady_clock::now();
            lastActivity = startTime;
            clientIP.clear();
            clientPort = 0;
            securityLevel = SecurityLevel::Basic;
        }
        
        bool IsActive() const noexcept {
            return state == SessionState::Active || state == SessionState::Authenticated;
        }
        
        std::chrono::milliseconds GetSessionDuration() const noexcept {
            return std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - startTime);
        }
    };

    struct BillingInfo {
        BillingType type{BillingType::Free};
        std::chrono::system_clock::time_point startDate;
        std::chrono::system_clock::time_point endDate;
        uint32_t remainingTime{0}; // in minutes
        bool isActive{false};
        std::string billingCode;
        uint8_t reason{0};
        
        void Reset() noexcept {
            type = BillingType::Free;
            startDate = std::chrono::system_clock::time_point{};
            endDate = std::chrono::system_clock::time_point{};
            remainingTime = 0;
            isActive = false;
            billingCode.clear();
            reason = 0;
        }
        
        bool IsExpired() const noexcept {
            return std::chrono::system_clock::now() > endDate;
        }
        
        bool HasTimeRemaining() const noexcept {
            return remainingTime > 0 && !IsExpired();
        }
    };

    struct SecurityParams {
        bool antiCheatEnabled{true};
        bool integrityCheckEnabled{true};
        bool sessionVerificationEnabled{true};
        uint32_t maxLoginAttempts{3};
        std::chrono::minutes sessionTimeout{30};
        std::chrono::minutes inactivityTimeout{15};
        SecurityLevel requiredLevel{SecurityLevel::Basic};
        
        void SetDefaults() noexcept {
            antiCheatEnabled = true;
            integrityCheckEnabled = true;
            sessionVerificationEnabled = true;
            maxLoginAttempts = 3;
            sessionTimeout = std::chrono::minutes{30};
            inactivityTimeout = std::chrono::minutes{15};
            requiredLevel = SecurityLevel::Basic;
        }
    };

    struct LogEntry {
        LogType type{LogType::System};
        std::chrono::system_clock::time_point timestamp;
        std::string message;
        uint32_t sessionId{0};
        uint32_t accountSerial{0};
        std::string sourceFunction;
        
        LogEntry() : timestamp(std::chrono::system_clock::now()) {}
        
        LogEntry(LogType logType, const std::string& msg) 
            : type(logType), timestamp(std::chrono::system_clock::now()), message(msg) {}
    };

    // Legacy compatibility structures
    extern "C" {
        struct _SYSTEMTIME {
            uint16_t wYear;
            uint16_t wMonth;
            uint16_t wDayOfWeek;
            uint16_t wDay;
            uint16_t wHour;
            uint16_t wMinute;
            uint16_t wSecond;
            uint16_t wMilliseconds;
        };

        struct _ACCOUNT_DATA {
            uint32_t dwAccountSerial;
            char szAccountName[MAX_ACCOUNT_NAME_LENGTH];
            char szPassword[MAX_PASSWORD_LENGTH];
            uint8_t byBillingType;
            bool bActive;
            _SYSTEMTIME stLastLogin;
            uint32_t dwLoginCount;
        };

        struct _SESSION_DATA {
            uint32_t dwSessionId;
            uint32_t dwAccountSerial;
            uint8_t byState;
            uint32_t dwStartTime;
            uint32_t dwLastActivity;
            char szClientIP[16];
            uint16_t wClientPort;
            uint8_t bySecurityLevel;
        };
    }

    // Conversion functions
    void ConvertToLegacy(const AccountInfo& modern, _ACCOUNT_DATA& legacy) noexcept;
    void ConvertFromLegacy(const _ACCOUNT_DATA& legacy, AccountInfo& modern) noexcept;
    void ConvertToLegacy(const SessionInfo& modern, _SESSION_DATA& legacy) noexcept;
    void ConvertFromLegacy(const _SESSION_DATA& legacy, SessionInfo& modern) noexcept;

    // Utility functions
    std::string AuthenticationResultToString(AuthenticationResult result);
    std::string BillingTypeToString(BillingType type);
    std::string LogTypeToString(LogType type);
    std::string SecurityLevelToString(SecurityLevel level);
    std::string SessionStateToString(SessionState state);

} // namespace NexusProtection::Authentication
