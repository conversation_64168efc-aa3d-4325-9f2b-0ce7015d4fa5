/*
 * Function: ?Update_RaceRank@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404956E0
 */

char __fastcall CRFWorldDatabase::Update_RaceRank(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-278h]@1
  void *SQLStmt; // [sp+20h] [bp-258h]@11
  __int16 v7; // [sp+30h] [bp-248h]@7
  char Dest; // [sp+50h] [bp-228h]@6
  unsigned int j; // [sp+254h] [bp-24h]@102
  unsigned __int64 v10; // [sp+260h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+280h] [bp+8h]@1
  char *v12; // [sp+288h] [bp+10h]@1

  v12 = szDate;
  v11 = this;
  v2 = &v5;
  for ( i = 156i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Update_RaceRank Start!",
    szDate);
  if ( !v11->m_hStmtUpdate && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v11->vfptr) )
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v11->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    return 0;
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Create #tbl_PvpRankB,C,A Table",
    v12);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 0);
  sprintf(&Dest, "select IDENTITY(int, 1, 1) AS Rank, -1 as Rate, tbl_base.serial as Serial, 0 as Race, ");
  strcat_0(
    &Dest,
    "tbl_base.name as Name, tbl_base.lv as Lv, tbl_general.PvpPoint as PvpPoint, tbl_general.GuildSerial as GuildSerial i"
    "nto #tbl_PvpRankB ");
  strcat_0(&Dest, "from tbl_general, tbl_base ");
  strcat_0(&Dest, "where tbl_base.serial=tbl_general.serial and tbl_base.dck=0 ");
  strcat_0(&Dest, "and tbl_base.AccountSerial<********* ");
  strcat_0(&Dest, "and tbl_general.class0 <> -1 ");
  strcat_0(&Dest, "and tbl_base.race in (0, 1) order by tbl_general.PvpPoint desc");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create #tbl_PvpRankB Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create #tbl_PvpRankB Table Fail NO_DATA!",
      v12);
  }
  sprintf(&Dest, "select IDENTITY(int, 1, 1) AS Rank, -1 as Rate, tbl_base.serial as Serial, 1 as Race, ");
  strcat_0(
    &Dest,
    "tbl_base.name as Name, tbl_base.lv as Lv, tbl_general.PvpPoint as PvpPoint, tbl_general.GuildSerial as GuildSerial i"
    "nto #tbl_PvpRankC ");
  strcat_0(&Dest, "from tbl_general, tbl_base ");
  strcat_0(&Dest, "where tbl_base.serial=tbl_general.serial and tbl_base.dck=0 ");
  strcat_0(&Dest, "and tbl_base.AccountSerial<********* ");
  strcat_0(&Dest, "and tbl_general.class0 <> -1 ");
  strcat_0(&Dest, "and tbl_base.race in (2, 3) order by tbl_general.PvpPoint desc");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create #tbl_PvpRankC Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create #tbl_PvpRankC Table Fail NO_DATA!",
      v12);
  }
  sprintf(&Dest, "select IDENTITY(int, 1, 1) AS Rank, -1 as Rate, tbl_base.serial as Serial, 2 as Race, ");
  strcat_0(
    &Dest,
    "tbl_base.name as Name, tbl_base.lv as Lv, tbl_general.PvpPoint as PvpPoint, tbl_general.GuildSerial as GuildSerial i"
    "nto #tbl_PvpRankA ");
  strcat_0(&Dest, "from tbl_general, tbl_base ");
  strcat_0(&Dest, "where tbl_base.serial=tbl_general.serial and tbl_base.dck=0 ");
  strcat_0(&Dest, "and tbl_base.AccountSerial<********* ");
  strcat_0(&Dest, "and tbl_general.class0 <> -1 ");
  strcat_0(&Dest, "and tbl_base.race in (4) order by tbl_general.PvpPoint desc");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create #tbl_PvpRankA Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create #tbl_PvpRankA Table Fail NO_DATA!",
      v12);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Create #tbl_PvpRankB,C,A Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Set Rate #tbl_PvpRankB,C,A Table",
    v12);
  sprintf(&Dest, "update #tbl_PvpRankB set Rate = ( (Rank*10000)/(select count(*) from #tbl_PvpRankB) )");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Rate #tbl_PvpRankB Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Rate #tbl_PvpRankB Table Fail NO_DATA!",
      v12);
  }
  sprintf(&Dest, "update #tbl_PvpRankC set Rate = ( (Rank*10000)/(select count(*) from #tbl_PvpRankC) )");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Rate #tbl_PvpRankC Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Rate #tbl_PvpRankC Table Fail NO_DATA!",
      v12);
  }
  sprintf(&Dest, "update #tbl_PvpRankA set Rate = ( (Rank*10000)/(select count(*) from #tbl_PvpRankA) )");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Rate #tbl_PvpRankA Table Fail SQL_ERROR!",
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Rate #tbl_PvpRankA Table Fail NO_DATA!",
      v12);
  }
  CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v11->vfptr);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Set Rate #tbl_PvpRankB,C,A Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Create tbl_PvpRank%s Table",
    v12,
    v12);
  sprintf(&Dest, "CREATE TABLE [dbo].[tbl_PvpRank%s] ", v12);
  strcat_0(&Dest, "( [Rank] [int] NOT NULL, [Rate] [int] NOT NULL, [serial] [int] NOT NULL, ");
  strcat_0(&Dest, "[name] [varchar] (17) NOT NULL , ");
  strcat_0(&Dest, "[lv] [int] NOT NULL, [Race] [int] NOT NULL, [Grade] [smallint] NOT NULL, ");
  strcat_0(&Dest, "[PvpPoint] [float] NOT NULL, [GuildSerial] [int] NOT NULL, ");
  strcat_0(&Dest, "[GuildName] [varchar] (17) NOT NULL ) ON [PRIMARY] ");
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Create tbl_PvpRank%s Table Fail!",
      v12,
      v12);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
    return 0;
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Create tbl_PvpRank%s Table",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Set Primary Key tbl_PvpRank%s",
    v12,
    v12);
  sprintf(
    &Dest,
    "ALTER TABLE [tbl_PvpRank%s] WITH NOCHECK ADD CONSTRAINT [PK_tbl_PvpRank%s] PRIMARY KEY CLUSTERED ( [serial] ) ON [PRIMARY] ",
    v12,
    v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 1) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Set Primary Key tbl_PvpRank%s Fail!",
      v12,
      v12);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
    return 0;
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Set Primary Key tbl_PvpRank%s",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Union #tbl_PvpRankB,C,A To tbl_PvpRank%s",
    v12,
    v12);
  sprintf(
    &Dest,
    "insert tbl_PvpRank%s(Rank, Rate, Serial, Name, Lv, Race, PvpPoint, Grade, GuildSerial, GuildName) ",
    v12);
  strcat_0(
    &Dest,
    "select 0 as Rank, Rate, Serial, Name, Lv, Race, PvpPoint, 0 as Grade, GuildSerial, '*' as GuildName from #tbl_PvpRankB union ");
  strcat_0(
    &Dest,
    "select 0 as Rank, Rate, Serial, Name, Lv, Race, PvpPoint, 0 as Grade, GuildSerial, '*' as GuildName from #tbl_PvpRankC union ");
  strcat_0(
    &Dest,
    "select 0 as Rank, Rate, Serial, Name, Lv, Race, PvpPoint, 0 as Grade, GuildSerial, '*' as GuildName from #tbl_PvpRankA ");
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &Dest, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Union #tbl_PvpRankB,C,A To tbl_PvpRank%s Fail SQL_ERROR!",
        v12,
        v12);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Union #tbl_PvpRankB,C,A To tbl_PvpRank%s Fail NO_DATA!",
      v12,
      v12);
  }
  CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v11->vfptr);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Union #tbl_PvpRankB,C,A To tbl_PvpRank%s",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Set Grade tbl_PvpRank%s",
    v12,
    v12);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=0", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=3 where lv >= 30 and lv <= 34 and rate <= 6500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=2 where lv >= 30 and lv <= 34 and rate > 6500 and rate <= 8500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=1 where lv >= 30 and lv <= 34 and rate > 8500 and rate <= 9500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=4 where lv >= 35 and lv <= 39 and rate <= 3500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=3 where lv >= 35 and lv <= 39 and rate > 3500 and rate <= 6500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=2 where lv >= 35 and lv <= 39 and rate > 6500 and rate <= 8500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=1 where lv >= 35 and lv <= 39 and rate > 8500 and rate <= 9500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=5 where lv >= 40 and lv <= 44 and rate <= 1500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=4 where lv >= 40 and lv <= 44 and rate > 1500 and rate <= 3500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=3 where lv >= 40 and lv <= 44 and rate > 3500 and rate <= 6500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=2 where lv >= 40 and lv <= 44 and rate > 6500 and rate <= 8500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=1 where lv >= 40 and lv <= 44 and rate > 8500 and rate <= 9500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=6 where lv >= 45 and lv <= 49 and rate <= 500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=5 where lv >= 45 and lv <= 49 and rate > 500 and rate <= 1500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=4 where lv >= 45 and lv <= 49 and rate > 1500 and rate <= 3500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=3 where lv >= 45 and lv <= 49 and rate > 3500 and rate <= 6500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=2 where lv >= 45 and lv <= 49 and rate > 6500 and rate <= 8500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=1 where lv >= 45 and lv <= 49 and rate > 8500 and rate <= 9500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=7 where lv >= 50 and rate <= 100", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=6 where lv >= 50 and rate > 100 and rate <= 500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=5 where lv >= 50 and rate > 500 and rate <= 1500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=4 where lv >= 50 and rate > 1500 and rate <= 3500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=3 where lv >= 50 and rate > 3500 and rate <= 6500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=2 where lv >= 50 and rate > 6500 and rate <= 8500", v12);
  CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0);
  sprintf(&Dest, "update tbl_PvpRank%s set grade=1 where lv >= 50 and rate > 8500 and rate <= 9500", v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Set Grade tbl_PvpRank%s",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start drop #tbl_PvpRankB,C,A Table",
    v12);
  sprintf(&Dest, "drop table #tbl_PvpRankB");
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "drop table #tbl_PvpRankC");
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  sprintf(&Dest, "drop table #tbl_PvpRankA");
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End drop #tbl_PvpRankB,C,A Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Update Rank tbl_PvpRank%s Table",
    v12,
    v12);
  for ( j = 0; (signed int)j < 3; ++j )
  {
    sprintf(
      &Dest,
      "select IDENTITY(int, 1, 1) AS Rank, Serial into #tbl_PvpRank from tbl_PvpRank%s where race=%d order by Grade desc, Rate",
      v12,
      j);
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
        v12,
        &Dest);
    sprintf(
      &Dest,
      "update tbl_PvpRank%s set rank = t1.rank from(select serial, rank from #tbl_PvpRank) as t1 where tbl_PvpRank%s.serial = t1.serial",
      v12,
      v12);
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
        v12,
        &Dest);
    sprintf(&Dest, "drop table #tbl_PvpRank");
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
        v12,
        &Dest);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Update Rank tbl_PvpRank%s Table",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Set GuildName tbl_PvpRank%s Table",
    v12,
    v12);
  SQLStmt = v12;
  sprintf(
    &Dest,
    "update tbl_PvpRank%s set GuildName = g.id from(select serial, id from tbl_Guild) as g where tbl_PvpRank%s.GuildSeria"
    "l = g.serial and tbl_PvpRank%s.GuildSerial > 0",
    v12,
    v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Set GuildName tbl_PvpRank%s Table",
    v12,
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Drop tbl_PvpRankToday Table",
    v12);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->vfptr, "tbl_PvpRankToday") )
  {
    sprintf(&Dest, "Drop Table tbl_PvpRankToday");
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
        v12,
        &Dest);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Drop tbl_PvpRankToday Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Start Create tbl_PvpRankToday Table",
    v12);
  sprintf(
    &Dest,
    "select Rank, Rate, serial, name, lv, race, PvpPoint, Grade, GuildSerial, GuildName into [dbo].[tbl_PvpRankToday] from tbl_PvpRank%s",
    v12);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dest, 0) )
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : %s Fail!",
      v12,
      &Dest);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : End Create tbl_PvpRankToday Table",
    v12);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(char* szDate(%s)) : Update_RaceRank Success",
    v12);
  return 1;
}
