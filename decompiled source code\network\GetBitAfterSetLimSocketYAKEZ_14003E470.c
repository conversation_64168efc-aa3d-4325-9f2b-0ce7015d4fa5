/*
 * Function: ?GetBitAfterSetLimSocket@@YAKE@Z
 * Address: 0x14003E470
 */

__int64 __fastcall GetBitAfterSetLimSocket(char byLimSocketNum)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  char v5; // [sp+20h] [bp+8h]@1

  v5 = byLimSocketNum;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return ((unsigned __int8)v5 << 28) | 0xFFFFFFFu;
}
