/*
 * Function: ?CreateHolyStone@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027D4A0
 */

void __fastcall CHolyStoneSystem::CreateHolyStone(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@13
  __int64 v4; // [sp+0h] [bp-198h]@1
  float v5; // [sp+30h] [bp-168h]@7
  float v6; // [sp+34h] [bp-164h]@7
  char ReturnedString; // [sp+50h] [bp-148h]@11
  float v8; // [sp+D4h] [bp-C4h]@11
  char Dest; // [sp+F0h] [bp-A8h]@11
  int j; // [sp+174h] [bp-24h]@15
  float v11; // [sp+180h] [bp-18h]@5
  unsigned __int64 v12; // [sp+188h] [bp-10h]@4
  CHolyStoneSystem *v13; // [sp+1A0h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v13->m_SaveData.m_dwCumCount )
    v11 = (float)(signed int)v13->m_SaveData.m_dwCumPlayerNum / (float)(signed int)v13->m_SaveData.m_dwCumCount;
  else
    v11 = 0.0;
  v5 = v11;
  v6 = v11 / 1500.0;
  if ( (float)(v11 / 1500.0) >= 0.5 )
  {
    if ( v6 > 2.0 )
      v6 = FLOAT_2_0;
  }
  else
  {
    v6 = FLOAT_0_5;
  }
  v8 = FLOAT_1_0;
  sprintf(&Dest, "CrystalHPMultiple_%d", v13->m_SaveData.m_byNumOfTime);
  GetPrivateProfileStringA("Rule", &Dest, "0", &ReturnedString, 0x80u, "..\\WorldInfo\\WorldInfo.ini");
  if ( !strcmp_0(&ReturnedString, "0") )
    GetPrivateProfileStringA("Rule", "CrystalHPMultiple", "1", &ReturnedString, 0x80u, "..\\WorldInfo\\WorldInfo.ini");
  v3 = atof(&ReturnedString);
  v8 = v3;
  if ( v3 == 0.0 )
  {
    v8 = FLOAT_1_0;
    WritePrivateProfileStringA("Rule", "CrystalHPMultiple", "1.0", "..\\WorldInfo\\WorldInfo.ini");
  }
  for ( j = 0; j < v13->m_nHolyStoneNum; ++j )
    CHolyStone::SetOper(&g_Stone[j], 1, v6 * v8);
  v13->m_SaveData.m_nStartStoneHP = ((int (__fastcall *)(struct CHolyStone *))g_Stone->vfptr->GetHP)(g_Stone);
  v13->m_SaveData.m_dwCumPlayerNum = 0;
  v13->m_SaveData.m_dwCumCount = 0;
}
