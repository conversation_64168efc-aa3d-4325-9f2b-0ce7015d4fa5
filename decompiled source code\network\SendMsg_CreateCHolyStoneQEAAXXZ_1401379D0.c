/*
 * Function: ?SendMsg_Create@CHolyStone@@QEAAXXZ
 * Address: 0x1401379D0
 */

void __fastcall CHolyStone::SendMsg_Create(CHolyStone *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v5; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v6; // [sp+3Ch] [bp-5Ch]@4
  __int16 pShort; // [sp+40h] [bp-58h]@4
  char v8; // [sp+46h] [bp-52h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v10; // [sp+65h] [bp-33h]@4
  unsigned __int64 v11; // [sp+80h] [bp-18h]@4
  CHolyStone *v12; // [sp+A0h] [bp+8h]@1

  v12 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = (unsigned __int64)&v3 ^ _security_cookie;
  v5 = v12->m_ObjID.m_wIndex;
  *(_WORD *)szMsg = v12->m_pRecordSet->m_dwIndex;
  v6 = v12->m_dwObjSerial;
  FloatToShort(v12->m_fCurPos, &pShort, 3);
  v8 = v12->m_byMasterRaceCode;
  pbyType = 3;
  v10 = -45;
  CGameObject::CircleReport((CGameObject *)&v12->vfptr, &pbyType, szMsg, 15, 0);
}
