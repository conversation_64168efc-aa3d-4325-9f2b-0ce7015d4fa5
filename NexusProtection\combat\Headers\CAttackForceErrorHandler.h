/*
 * CAttackForceErrorHandler.h - Comprehensive Error Handling for AttackForce System
 * Provides robust error handling, validation, and recovery mechanisms
 */

#pragma once

#include "CAttackForceConstants.h"
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <exception>
#include <chrono>

// Forward declarations
class CCharacter;
struct _attack_param;

namespace NexusProtection {
namespace Combat {

/**
 * Attack force error categories
 */
enum class AttackForceErrorCategory : int {
    None = 0,
    InvalidInput = 1,
    InvalidCharacter = 2,
    InvalidTarget = 3,
    InvalidParameters = 4,
    CalculationError = 5,
    SystemError = 6,
    NetworkError = 7,
    DatabaseError = 8,
    SecurityError = 9,
    PerformanceError = 10
};

/**
 * Attack force error severity levels
 */
enum class AttackForceErrorSeverity : int {
    Info = 0,
    Warning = 1,
    Error = 2,
    Critical = 3,
    Fatal = 4
};

/**
 * Detailed error information
 */
struct AttackForceError {
    AttackForceErrorCategory category{AttackForceErrorCategory::None};
    AttackForceErrorSeverity severity{AttackForceErrorSeverity::Info};
    int errorCode{0};
    std::string message;
    std::string details;
    std::string function;
    std::string file;
    int line{0};
    std::chrono::system_clock::time_point timestamp;
    
    // Context information
    CCharacter* pAttacker{nullptr};
    CCharacter* pTarget{nullptr};
    _attack_param* pParam{nullptr};
    
    AttackForceError() : timestamp(std::chrono::system_clock::now()) {}
    
    AttackForceError(AttackForceErrorCategory cat, AttackForceErrorSeverity sev, int code, 
                    const std::string& msg, const std::string& func = "", 
                    const std::string& f = "", int l = 0)
        : category(cat), severity(sev), errorCode(code), message(msg), 
          function(func), file(f), line(l), timestamp(std::chrono::system_clock::now()) {}
    
    std::string ToString() const {
        std::string result = "[" + GetCategoryString() + "][" + GetSeverityString() + "] ";
        result += message;
        if (!details.empty()) {
            result += " - " + details;
        }
        if (!function.empty()) {
            result += " (" + function + ")";
        }
        return result;
    }
    
    std::string GetCategoryString() const {
        switch (category) {
            case AttackForceErrorCategory::InvalidInput: return "INVALID_INPUT";
            case AttackForceErrorCategory::InvalidCharacter: return "INVALID_CHARACTER";
            case AttackForceErrorCategory::InvalidTarget: return "INVALID_TARGET";
            case AttackForceErrorCategory::InvalidParameters: return "INVALID_PARAMETERS";
            case AttackForceErrorCategory::CalculationError: return "CALCULATION_ERROR";
            case AttackForceErrorCategory::SystemError: return "SYSTEM_ERROR";
            case AttackForceErrorCategory::NetworkError: return "NETWORK_ERROR";
            case AttackForceErrorCategory::DatabaseError: return "DATABASE_ERROR";
            case AttackForceErrorCategory::SecurityError: return "SECURITY_ERROR";
            case AttackForceErrorCategory::PerformanceError: return "PERFORMANCE_ERROR";
            default: return "UNKNOWN";
        }
    }
    
    std::string GetSeverityString() const {
        switch (severity) {
            case AttackForceErrorSeverity::Info: return "INFO";
            case AttackForceErrorSeverity::Warning: return "WARNING";
            case AttackForceErrorSeverity::Error: return "ERROR";
            case AttackForceErrorSeverity::Critical: return "CRITICAL";
            case AttackForceErrorSeverity::Fatal: return "FATAL";
            default: return "UNKNOWN";
        }
    }
    
    bool IsCritical() const {
        return severity >= AttackForceErrorSeverity::Critical;
    }
    
    bool ShouldAbort() const {
        return severity >= AttackForceErrorSeverity::Error;
    }
};

/**
 * Error recovery strategy
 */
enum class ErrorRecoveryStrategy : int {
    None = 0,
    Retry = 1,
    UseDefault = 2,
    Skip = 3,
    Abort = 4,
    Fallback = 5
};

/**
 * Error recovery context
 */
struct ErrorRecoveryContext {
    AttackForceError error;
    ErrorRecoveryStrategy strategy{ErrorRecoveryStrategy::None};
    int retryCount{0};
    int maxRetries{3};
    std::chrono::milliseconds retryDelay{100};
    std::function<bool()> recoveryAction;
    
    ErrorRecoveryContext() = default;
    
    bool CanRetry() const {
        return strategy == ErrorRecoveryStrategy::Retry && retryCount < maxRetries;
    }
    
    void IncrementRetry() {
        retryCount++;
    }
    
    bool ShouldAbort() const {
        return strategy == ErrorRecoveryStrategy::Abort || error.IsCritical();
    }
};

/**
 * Validation result
 */
struct ValidationResult {
    bool bValid{true};
    std::vector<AttackForceError> errors;
    std::vector<AttackForceError> warnings;
    
    ValidationResult() = default;
    
    void AddError(const AttackForceError& error) {
        if (error.severity >= AttackForceErrorSeverity::Error) {
            errors.push_back(error);
            bValid = false;
        } else {
            warnings.push_back(error);
        }
    }
    
    void AddError(AttackForceErrorCategory category, const std::string& message, 
                  AttackForceErrorSeverity severity = AttackForceErrorSeverity::Error) {
        AttackForceError error(category, severity, 0, message);
        AddError(error);
    }
    
    bool HasErrors() const {
        return !errors.empty();
    }
    
    bool HasWarnings() const {
        return !warnings.empty();
    }
    
    size_t GetErrorCount() const {
        return errors.size();
    }
    
    size_t GetWarningCount() const {
        return warnings.size();
    }
    
    std::string GetSummary() const {
        if (bValid && warnings.empty()) {
            return "Validation passed";
        }
        
        std::string summary = "Validation ";
        if (!bValid) {
            summary += "failed with " + std::to_string(errors.size()) + " error(s)";
        } else {
            summary += "passed";
        }
        
        if (!warnings.empty()) {
            summary += " and " + std::to_string(warnings.size()) + " warning(s)";
        }
        
        return summary;
    }
};

/**
 * Comprehensive error handler for AttackForce system
 */
class CAttackForceErrorHandler {
public:
    /**
     * Constructor
     */
    CAttackForceErrorHandler();
    
    /**
     * Destructor
     */
    virtual ~CAttackForceErrorHandler();
    
    /**
     * Validate attack parameters
     * @param pParam Attack parameters
     * @return Validation result
     */
    ValidationResult ValidateAttackParameters(_attack_param* pParam);
    
    /**
     * Validate attacking character
     * @param pAttacker Attacking character
     * @return Validation result
     */
    ValidationResult ValidateAttacker(CCharacter* pAttacker);
    
    /**
     * Validate target character
     * @param pTarget Target character
     * @param pAttacker Attacking character (for context)
     * @return Validation result
     */
    ValidationResult ValidateTarget(CCharacter* pTarget, CCharacter* pAttacker = nullptr);
    
    /**
     * Validate damage calculation context
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @return Validation result
     */
    ValidationResult ValidateDamageContext(CCharacter* pAttacker, CCharacter* pTarget, _attack_param* pParam);
    
    /**
     * Handle error with recovery strategy
     * @param error Error to handle
     * @param context Recovery context
     * @return true if error was handled successfully
     */
    bool HandleError(const AttackForceError& error, ErrorRecoveryContext& context);
    
    /**
     * Log error
     * @param error Error to log
     */
    void LogError(const AttackForceError& error);
    
    /**
     * Create error with context
     * @param category Error category
     * @param severity Error severity
     * @param message Error message
     * @param function Function name
     * @param file File name
     * @param line Line number
     * @return Created error
     */
    AttackForceError CreateError(AttackForceErrorCategory category, AttackForceErrorSeverity severity,
                                const std::string& message, const std::string& function = "",
                                const std::string& file = "", int line = 0);
    
    /**
     * Set error callback
     * @param callback Error callback function
     */
    void SetErrorCallback(std::function<void(const AttackForceError&)> callback);
    
    /**
     * Set recovery strategy for error category
     * @param category Error category
     * @param strategy Recovery strategy
     */
    void SetRecoveryStrategy(AttackForceErrorCategory category, ErrorRecoveryStrategy strategy);
    
    /**
     * Get error statistics
     * @return Error count by category
     */
    std::vector<std::pair<AttackForceErrorCategory, int>> GetErrorStatistics() const;
    
    /**
     * Clear error statistics
     */
    void ClearStatistics();
    
    /**
     * Enable/disable detailed logging
     * @param bEnable Enable flag
     */
    void SetDetailedLogging(bool bEnable) { m_bDetailedLogging = bEnable; }
    
    /**
     * Check if detailed logging is enabled
     * @return true if enabled
     */
    bool IsDetailedLoggingEnabled() const { return m_bDetailedLogging; }

protected:
    /**
     * Validate character state
     * @param pCharacter Character to validate
     * @param context Context description
     * @return Validation result
     */
    virtual ValidationResult ValidateCharacterState(CCharacter* pCharacter, const std::string& context);
    
    /**
     * Validate numeric range
     * @param value Value to validate
     * @param min Minimum value
     * @param max Maximum value
     * @param name Value name
     * @return Validation result
     */
    virtual ValidationResult ValidateRange(int value, int min, int max, const std::string& name);
    
    /**
     * Validate float range
     * @param value Value to validate
     * @param min Minimum value
     * @param max Maximum value
     * @param name Value name
     * @return Validation result
     */
    virtual ValidationResult ValidateRange(float value, float min, float max, const std::string& name);

private:
    std::function<void(const AttackForceError&)> m_errorCallback;
    std::vector<std::pair<AttackForceErrorCategory, ErrorRecoveryStrategy>> m_recoveryStrategies;
    std::vector<std::pair<AttackForceErrorCategory, int>> m_errorStatistics;
    bool m_bDetailedLogging{false};
    
    /**
     * Initialize default recovery strategies
     */
    void InitializeDefaultStrategies();
    
    /**
     * Update error statistics
     * @param category Error category
     */
    void UpdateStatistics(AttackForceErrorCategory category);
    
    // Disable copy constructor and assignment operator
    CAttackForceErrorHandler(const CAttackForceErrorHandler&) = delete;
    CAttackForceErrorHandler& operator=(const CAttackForceErrorHandler&) = delete;
};

/**
 * Error handling macros for convenience
 */
#define ATTACK_FORCE_ERROR(handler, category, message) \
    handler.CreateError(category, AttackForceErrorSeverity::Error, message, __FUNCTION__, __FILE__, __LINE__)

#define ATTACK_FORCE_WARNING(handler, category, message) \
    handler.CreateError(category, AttackForceErrorSeverity::Warning, message, __FUNCTION__, __FILE__, __LINE__)

#define ATTACK_FORCE_CRITICAL(handler, category, message) \
    handler.CreateError(category, AttackForceErrorSeverity::Critical, message, __FUNCTION__, __FILE__, __LINE__)

#define ATTACK_FORCE_VALIDATE_PARAM(handler, param, result) \
    do { \
        ValidationResult validation = handler.ValidateAttackParameters(param); \
        if (!validation.bValid) { \
            result.errorMessage = validation.GetSummary(); \
            return result; \
        } \
    } while(0)

#define ATTACK_FORCE_VALIDATE_ATTACKER(handler, attacker, result) \
    do { \
        ValidationResult validation = handler.ValidateAttacker(attacker); \
        if (!validation.bValid) { \
            result.errorMessage = validation.GetSummary(); \
            return result; \
        } \
    } while(0)

#define ATTACK_FORCE_VALIDATE_TARGET(handler, target, attacker, result) \
    do { \
        ValidationResult validation = handler.ValidateTarget(target, attacker); \
        if (!validation.bValid) { \
            result.errorMessage = validation.GetSummary(); \
            return result; \
        } \
    } while(0)

} // namespace Combat
} // namespace NexusProtection
