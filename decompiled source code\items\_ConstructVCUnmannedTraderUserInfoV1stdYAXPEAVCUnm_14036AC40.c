/*
 * Function: ??$_Construct@VCUnmannedTraderUserInfo@@V1@@std@@YAXPEAVCUnmannedTraderUserInfo@@AEBV1@@Z
 * Address: 0x14036AC40
 */

void __fastcall std::_Construct<CUnmannedTraderUserInfo,CUnmannedTraderUserInfo>(CUnmannedTraderUserInfo *_Ptr, CUnmannedTraderUserInfo *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  void *_Where; // [sp+20h] [bp-38h]@4
  CUnmannedTraderUserInfo *v6; // [sp+30h] [bp-28h]@4
  __int64 v7; // [sp+38h] [bp-20h]@4
  CUnmannedTraderUserInfo *v8; // [sp+60h] [bp+8h]@1
  CUnmannedTraderUserInfo *__that; // [sp+68h] [bp+10h]@1

  __that = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = -2i64;
  _Where = v8;
  v6 = (CUnmannedTraderUserInfo *)operator new(0x68ui64, v8);
  if ( v6 )
    CUnmannedTraderUserInfo::CUnmannedTraderUserInfo(v6, __that);
}
