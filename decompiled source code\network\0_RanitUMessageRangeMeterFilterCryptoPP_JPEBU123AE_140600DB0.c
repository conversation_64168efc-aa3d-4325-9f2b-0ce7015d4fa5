/*
 * Function: ??0?$_<PERSON><PERSON>@UMessageRange@MeterFilter@CryptoPP@@_JPEBU123@AEBU123@@std@@QEAA@XZ
 * Address: 0x140600DB0
 */

std::_Iterator_base *__fastcall std::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>::_Ranit<CryptoPP::MeterFilter::MessageRange,__int64,CryptoPP::MeterFilter::MessageRange const *,CryptoPP::MeterFilter::MessageRange const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Iterator_base::_Iterator_base(a1);
  return v2;
}
