/*
 * Function: ?Update_NPCQuestHistory@CUserDB@@QEAA_NEPEAU_NPC_QUEST_HISTORY@_QUEST_DB_BASE@@@Z
 * Address: 0x14011B880
 */

char __fastcall CUserDB::Update_NPCQuestHistory(CUserDB *this, char byIndex, _QUEST_DB_BASE::_NPC_QUEST_HISTORY *pHisData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byIndex < 70 )
  {
    memcpy_0(&v7->m_AvatorData.dbQuest.m_History[(unsigned __int8)byIndex], pHisData, 0x11ui64);
    result = 1;
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_NPCQuestHistory(Index OVER) : %d",
      v7->m_aszAvatorName,
      (unsigned __int8)byIndex);
    result = 0;
  }
  return result;
}
