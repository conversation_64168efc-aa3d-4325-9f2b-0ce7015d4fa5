/*
 * Function: ?IsMemberGuild@CNormalGuildBattle@GUILD_BATTLE@@QEAA_NK@Z
 * Address: 0x1403D9690
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattle::IsMemberGuild(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v7; // [sp+40h] [bp+8h]@1
  unsigned int v8; // [sp+48h] [bp+10h]@1

  v8 = dwGuildSerial;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return dwGuildSerial == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v7->m_k1P)
      || v8 == GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v7->m_k2P);
}
