/*
 * Function: ??1CLogTypeDBTaskPool@@QEAA@XZ
 * Address: 0x1402C1FC0
 */

void __fastcall CLogTypeDBTaskPool::~CLogTypeDBTaskPool(CLogTypeDBTaskPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CLogTypeDBTaskPool *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CLogTypeDBTaskPool::Destroy(v5);
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(&v5->m_vecDat);
  CNetIndexList::~CNetIndexList(&v5->m_kInxComplete);
  CNetIndexList::~CNetIndexList(&v5->m_kInxEmpty);
  CNetIndexList::~CNetIndexList(&v5->m_kInxProc);
}
