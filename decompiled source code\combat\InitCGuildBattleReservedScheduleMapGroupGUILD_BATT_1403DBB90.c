/*
 * Function: ?Init@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NII@Z
 * Address: 0x1403DBB90
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Init(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiDayInx, unsigned int uiMapCnt)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@7
  __int64 v7; // rax@11
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@14
  __int64 v9; // [sp+0h] [bp-78h]@1
  unsigned int v10; // [sp+20h] [bp-58h]@7
  unsigned int v11; // [sp+30h] [bp-48h]@8
  unsigned int j; // [sp+34h] [bp-44h]@8
  GUILD_BATTLE::CGuildBattleReservedSchedule **v13; // [sp+38h] [bp-40h]@6
  GUILD_BATTLE::CGuildBattleReservedSchedule *v14; // [sp+40h] [bp-38h]@13
  GUILD_BATTLE::CGuildBattleReservedSchedule *v15; // [sp+48h] [bp-30h]@10
  __int64 v16; // [sp+50h] [bp-28h]@4
  __int64 v17; // [sp+58h] [bp-20h]@6
  GUILD_BATTLE::CGuildBattleReservedSchedule *v18; // [sp+60h] [bp-18h]@11
  unsigned int v19; // [sp+68h] [bp-10h]@14
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v20; // [sp+80h] [bp+8h]@1
  unsigned int v21; // [sp+88h] [bp+10h]@1
  unsigned int v22; // [sp+90h] [bp+18h]@1

  v22 = uiMapCnt;
  v21 = uiDayInx;
  v20 = this;
  v3 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = -2i64;
  if ( uiMapCnt )
  {
    v17 = uiMapCnt;
    v13 = (GUILD_BATTLE::CGuildBattleReservedSchedule **)operator new[](saturated_mul(8ui64, uiMapCnt));
    v20->m_ppkReservedSchedule = v13;
    if ( v20->m_ppkReservedSchedule )
    {
      v11 = v22 * v21;
      for ( j = 0; j < v22; ++j )
      {
        v15 = (GUILD_BATTLE::CGuildBattleReservedSchedule *)operator new(0xE0ui64);
        if ( v15 )
        {
          GUILD_BATTLE::CGuildBattleReservedSchedule::CGuildBattleReservedSchedule(v15, j + v11);
          v18 = (GUILD_BATTLE::CGuildBattleReservedSchedule *)v7;
        }
        else
        {
          v18 = 0i64;
        }
        v14 = v18;
        v20->m_ppkReservedSchedule[j] = v18;
        if ( !v20->m_ppkReservedSchedule[j] )
        {
          v19 = j + v11;
          v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          v10 = v19;
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v8,
            "CGuildBattleReservedScheduleMapGroup::Init( %u, %u ) : new CGuildBattleReservedSchedule(%u) Fail!",
            v21,
            v22);
          return 0;
        }
      }
      v20->m_uiDayInx = v21;
      v20->m_uiMapCnt = v22;
      result = 1;
    }
    else
    {
      v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v10 = v22;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v6,
        "CGuildBattleReservedScheduleMapGroup::Init( %u, %u ) : new CGuildBattleReservedSchedule * [%u] Fail!",
        v21,
        v22);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
