/*
 * Function: ?BuyByCash@CashItemRemoteStore@@AEAA_NGPEAD@Z
 * Address: 0x1402FE0D0
 */

char __fastcall CashItemRemoteStore::BuyByCash(CashItemRemoteStore *this, unsigned __int16 wSock, char *pPacket)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@9
  int v7; // eax@25
  unsigned int v8; // eax@27
  unsigned int v9; // ecx@31
  unsigned int v10; // edx@31
  char v11; // al@34
  char v12; // al@34
  char v13; // al@68
  char v14; // al@71
  char *v15; // rax@84
  CCashDBWorkManager *v16; // rax@84
  __int64 v17; // [sp+0h] [bp-658h]@1
  int v18; // [sp+20h] [bp-638h]@31
  char *v19; // [sp+30h] [bp-628h]@4
  CPlayer *pOne; // [sp+38h] [bp-620h]@4
  int j; // [sp+40h] [bp-618h]@15
  int v22; // [sp+44h] [bp-614h]@20
  int k; // [sp+48h] [bp-610h]@20
  _param_cash_update pBuyList; // [sp+60h] [bp-5F8h]@27
  bool v25; // [sp+5D4h] [bp-84h]@27
  bool v26; // [sp+5D5h] [bp-83h]@27
  bool v27; // [sp+5D6h] [bp-82h]@27
  bool v28; // [sp+5D7h] [bp-81h]@27
  _request_csi_buy_clzo::__item *pCsItem; // [sp+5D8h] [bp-80h]@27
  char *v30; // [sp+5E0h] [bp-78h]@27
  int l; // [sp+5E8h] [bp-70h]@27
  _CashShop_fld *pFld; // [sp+5F0h] [bp-68h]@29
  _TimeItem_fld *v33; // [sp+5F8h] [bp-60h]@29
  unsigned __int8 v34; // [sp+600h] [bp-58h]@48
  char v35; // [sp+601h] [bp-57h]@68
  int v36; // [sp+604h] [bp-54h]@77
  int m; // [sp+608h] [bp-50h]@79
  char v38; // [sp+618h] [bp-40h]@33
  char v39; // [sp+619h] [bp-3Fh]@35
  char v40; // [sp+61Ah] [bp-3Eh]@38
  char v41; // [sp+61Bh] [bp-3Dh]@41
  char v42; // [sp+61Ch] [bp-3Ch]@44
  char v43; // [sp+61Dh] [bp-3Bh]@47
  char v44; // [sp+61Eh] [bp-3Ah]@52
  char v45; // [sp+61Fh] [bp-39h]@55
  char v46; // [sp+620h] [bp-38h]@76
  char v47; // [sp+621h] [bp-37h]@78
  char v48; // [sp+622h] [bp-36h]@82
  char v49; // [sp+623h] [bp-35h]@84
  __int64 v50; // [sp+628h] [bp-30h]@4
  int v51; // [sp+630h] [bp-28h]@9
  int v52; // [sp+634h] [bp-24h]@25
  char v53; // [sp+638h] [bp-20h]@48
  unsigned __int64 size; // [sp+640h] [bp-18h]@84
  unsigned __int64 v55; // [sp+648h] [bp-10h]@4
  CashItemRemoteStore *v56; // [sp+660h] [bp+8h]@1
  unsigned __int16 v57; // [sp+668h] [bp+10h]@1

  v57 = wSock;
  v56 = this;
  v3 = &v17;
  for ( i = 404i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v50 = -2i64;
  v55 = (unsigned __int64)&v17 ^ _security_cookie;
  v19 = pPacket;
  pOne = &g_Player + wSock;
  if ( !pOne->m_bOper || !pOne->m_bLive )
    return 1;
  if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum) == 255 )
  {
    ICsSendInterface::SendMsg_Error(v57, 16);
    return 1;
  }
  v51 = (unsigned __int8)*v19;
  v6 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum);
  if ( v51 > v6 )
  {
    ICsSendInterface::SendMsg_Error(v57, 16);
    return 1;
  }
  if ( (signed int)(unsigned __int8)*v19 > 20 )
  {
    ICsSendInterface::SendMsg_Error(v57, 4);
    CLogFile::Write(v56->_kLoggers, "CashItemRemoteStore::Buy() Buy Item Number Error, Num(%d)", (unsigned __int8)*v19);
    return 1;
  }
  if ( (signed int)(unsigned __int8)v19[2] >= 3 )
  {
    ICsSendInterface::SendMsg_Error(v57, 19);
    return 1;
  }
  for ( j = 0; j < (unsigned __int8)v19[2]; ++j )
  {
    if ( (signed int)(unsigned __int8)v19[4 * j + 3] >= 8 )
    {
      ICsSendInterface::SendMsg_Error(v57, 19);
      return 1;
    }
  }
  v22 = 0;
  for ( k = 0; k < (unsigned __int8)*v19; ++k )
  {
    if ( v19[44 * k + 58] == 3 )
      ++v22;
  }
  v52 = v22 + (unsigned __int8)*v19;
  v7 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum);
  if ( v52 > v7 )
  {
    ICsSendInterface::SendMsg_Error(v57, 16);
    return 1;
  }
  v8 = CPlayerDB::GetCharSerial(&pOne->m_Param);
  _param_cash_update::_param_cash_update(&pBuyList, pOne->m_pUserDB->m_dwAccountSerial, v8, v57);
  v25 = CashItemRemoteStore::is_cde_time(v56);
  pBuyList.in_bAdjustDiscount = v25;
  v26 = CashItemRemoteStore::IsEventTime(v56, 1);
  pBuyList.in_bOneN_One = v26;
  v27 = CashItemRemoteStore::IsEventTime(v56, 0);
  pBuyList.in_bSetDiscount = v27;
  v28 = CashItemRemoteStore::IsEventTime(v56, 2);
  pBuyList.in_bLimited_Sale = v28;
  pCsItem = 0i64;
  v30 = 0i64;
  for ( l = 0; l < (unsigned __int8)*v19; ++l )
  {
    pCsItem = (_request_csi_buy_clzo::__item *)&v19[44 * l + 15];
    v30 = &pBuyList.in_item[(signed __int64)l].byRet;
    pFld = (_CashShop_fld *)CRecordData::GetRecord(&v56->_kRecGoods, *(_WORD *)&v19[44 * l + 23]);
    v33 = TimeItem::FindTimeRec(pCsItem->byTblCode, pCsItem->wItemIdx);
    if ( !v33 && pCsItem->byTblCode >= 37 )
    {
      v9 = pCsItem->wItemIdx;
      v10 = pCsItem->byTblCode;
      v18 = pCsItem->wStoreIdx;
      CLogFile::Write(
        v56->_kLoggers,
        "CashItemRemoteStore::Buy() Can not find _TimeItem_fld Data, TableCode(%d), ItemIndex(%d), StoreIndex(%d)",
        v10,
        v9);
    }
    if ( pCsItem->byTblCode >= 37 )
    {
      ICsSendInterface::SendMsg_Error(v57, 11);
      v38 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v38;
    }
    v11 = CPlayerDB::GetRaceSexCode(&pOne->m_Param);
    v12 = CashItemRemoteStore::_check_buyitem(v56, v11, pCsItem, pFld);
    *v30 = v12;
    if ( *v30 )
    {
      ICsSendInterface::SendMsg_Error(v57, (unsigned __int8)*v30);
      v39 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v39;
    }
    if ( pCsItem->byEventType == 1 && !v25 )
    {
      ICsSendInterface::SendMsg_Error(v57, 18);
      v40 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v40;
    }
    if ( pCsItem->byEventType == 2 && !v27 )
    {
      ICsSendInterface::SendMsg_Error(v57, 18);
      v41 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v41;
    }
    if ( pCsItem->byEventType == 3 && !v26 )
    {
      ICsSendInterface::SendMsg_Error(v57, 18);
      v42 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v42;
    }
    if ( pCsItem->byEventType == 5 && !v28 )
    {
      ICsSendInterface::SendMsg_Error(v57, 22);
      v43 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v43;
    }
    v34 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&pOne->m_Param.m_dbInven.m_nListNum);
    v53 = pCsItem->byEventType;
    if ( v53 == 2 )
    {
      if ( (signed int)v34 < 3 )
      {
        ICsSendInterface::SendMsg_Error(v57, 16);
        v44 = 1;
        _param_cash_update::~_param_cash_update(&pBuyList);
        return v44;
      }
    }
    else if ( v53 == 3 && (signed int)v34 < 2 )
    {
      ICsSendInterface::SendMsg_Error(v57, 16);
      v45 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v45;
    }
    v30[10] = pCsItem->byTblCode;
    *((_WORD *)v30 + 6) = pCsItem->wItemIdx;
    v30[9] = pCsItem->byOverlapNum;
    *((_DWORD *)v30 + 4) = pCsItem->nPrice;
    v30[28] = pCsItem->byEventType;
    if ( v25 && pCsItem->byEventType == 1 )
    {
      if ( pCsItem->byDiscount == 255 )
        *((_WORD *)v30 + 7) = v56->m_cde.m_ini.m_wCsDiscount;
      else
        *((_WORD *)v30 + 7) = pCsItem->byDiscount;
    }
    else
    {
      *((_WORD *)v30 + 7) = 0;
    }
    *((_QWORD *)v30 + 4) = UIDGenerator::getuid(unk_1799C608C);
    if ( v33 )
    {
      v30[20] = v33->m_nCheckType;
      *((_DWORD *)v30 + 6) = v33->m_nUseTime;
    }
    strcpy_s(v30 + 1, 8ui64, pFld->m_strCsItemCode);
    if ( v27 && (signed int)(unsigned __int8)v19[1] > 0 && (signed int)(unsigned __int8)v19[1] <= 4 )
    {
      v35 = v19[1];
      v13 = CashItemRemoteStore::GetSetDiscout(v56, v35 - 1);
      *((_WORD *)v30 + 7) = (unsigned __int8)v13;
    }
    if ( v28 && pCsItem->byEventType == 5 )
    {
      v14 = CashItemRemoteStore::GetLimDiscout(v56);
      *((_WORD *)v30 + 7) = (unsigned __int8)v14;
    }
    ++pBuyList.in_nNum10;
  }
  if ( (signed int)(unsigned __int8)v19[2] > 0 )
  {
    if ( v25 || v26 )
    {
      ICsSendInterface::SendMsg_Error(v57, 20);
      v46 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v46;
    }
    v36 = CashItemRemoteStore::CheckCouponType(v56, (_STORAGE_POS_INDIV *)(v19 + 3), pOne, v19[2]);
    if ( v36 <= 0 )
    {
      ICsSendInterface::SendMsg_Error(v57, 19);
      v47 = 1;
      _param_cash_update::~_param_cash_update(&pBuyList);
      return v47;
    }
    for ( m = 0; m < (unsigned __int8)v19[2]; ++m )
    {
      if ( !CashItemRemoteStore::UseDiscountCoupon(v56, &pBuyList, *(_STORAGE_POS_INDIV *)&v19[4 * m + 3], pOne) )
      {
        ICsSendInterface::SendMsg_Error(v57, 19);
        v48 = 1;
        _param_cash_update::~_param_cash_update(&pBuyList);
        return v48;
      }
      *(_DWORD *)&pBuyList.in_CouponItem[m].byStorageCode = *(_DWORD *)&v19[4 * m + 3];
    }
  }
  pBuyList.in_nCouponCnt = v19[2];
  strcpy_s(pBuyList.in_szAcc, 0xDui64, pOne->m_pUserDB->m_szAccountID);
  strcpy_s(pBuyList.in_szSvrName, 0x21ui64, byte_1799C5B78);
  v15 = CPlayerDB::GetCharNameW(&pOne->m_Param);
  strcpy_s(pBuyList.in_szAvatorName, 0x11ui64, v15);
  pBuyList.in_nCashAmount = CPlayer::GetCashAmount(pOne);
  pBuyList.in_dwIP = pOne->m_pUserDB->m_dwIP;
  size = _param_cash_update::size(&pBuyList);
  v16 = CTSingleton<CCashDBWorkManager>::Instance();
  CCashDBWorkManager::PushTask(v16, 1, (char *)&pBuyList, size);
  v49 = 1;
  _param_cash_update::~_param_cash_update(&pBuyList);
  return v49;
}
