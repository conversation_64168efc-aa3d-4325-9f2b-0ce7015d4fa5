/*
 * Function: ?Select_CharNumInWorld@CRFWorldDatabase@@QEAAEKAEAE@Z
 * Address: 0x14048E200
 */

char __fastcall CRFWorldDatabase::Select_CharNumInWorld(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *byCharNum)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v9; // [sp+38h] [bp-150h]@22
  __int16 v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v12; // [sp+164h] [bp-24h]@4
  int v13; // [sp+168h] [bp-20h]@4
  char v14; // [sp+16Ch] [bp-1Ch]@16
  char v15; // [sp+16Dh] [bp-1Bh]@24
  unsigned __int64 v16; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v17; // [sp+190h] [bp+8h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1

  TargetValue = byCharNum;
  v17 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  v13 = 0;
  sprintf(&Dest, "{ CALL pSelect_CharNumInWorld( %d ) }", dwAccountSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v10 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v14 = 0;
        if ( v10 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
          v14 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v14;
      }
      else
      {
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v17->m_hStmtSelect, 1u, -6, TargetValue, 0i64, &v9);
        if ( v10 && v10 != 1 )
        {
          v15 = 0;
          if ( v10 == 100 )
          {
            v15 = 2;
          }
          else
          {
            SQLStmt = v17->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v10, v17->m_hStmtSelect);
            v15 = 1;
          }
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = v15;
        }
        else
        {
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          if ( v17->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
