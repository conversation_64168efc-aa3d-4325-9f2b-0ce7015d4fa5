/*
 * Function: ?dev_change_class@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400BB930
 */

bool __fastcall CPlayer::dev_change_class(CPlayer *this, char *pszClassCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_pUserDB )
    result = CUserDB::Setting_Class(v6->m_pUserDB, pszClassCode) != 0;
  else
    result = 0;
  return result;
}
