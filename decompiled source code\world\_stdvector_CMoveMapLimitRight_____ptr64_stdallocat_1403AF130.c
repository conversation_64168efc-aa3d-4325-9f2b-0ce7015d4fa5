/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor$4
 * Address: 0x1403AF130
 */

void __fastcall std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 2 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 216));
  }
}
