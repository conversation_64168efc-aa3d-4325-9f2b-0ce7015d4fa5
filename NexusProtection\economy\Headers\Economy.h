#pragma once

/**
 * @file Economy.h
 * @brief Main header for the NexusProtection Economy Module
 * 
 * This header provides access to all economy-related functionality including:
 * - Money supply management and statistics
 * - Currency exchange systems (Dalant/Gold)
 * - Guild money operations
 * - Economic data tracking and analysis
 * - Network handlers for economy operations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

// Core economy types and enumerations
#include "EconomyTypes.h"

// Data structures
#include "MoneySupplyData.h"

// Main economy classes
#include "CMoneySupplyMgr.h"
#include "EconomySystem.h"

// Network handlers
#include "EconomyNetworkHandlers.h"

namespace NexusProtection::Economy {

    /**
     * @brief Initialize the entire economy module
     * 
     * This function initializes all economy subsystems including:
     * - Money supply manager
     * - Economy system
     * - Configuration loading
     * 
     * @return true if initialization successful, false otherwise
     */
    bool InitializeEconomyModule();

    /**
     * @brief Shutdown the economy module
     * 
     * Properly shuts down all economy subsystems and saves any pending data.
     */
    void ShutdownEconomyModule();

    /**
     * @brief Update the economy module
     * 
     * Called periodically to update economy systems, send data to web services,
     * and process any pending operations.
     * 
     * @param deltaTime Time elapsed since last update
     */
    void UpdateEconomyModule(std::chrono::milliseconds deltaTime);

    /**
     * @brief Get module version information
     * 
     * @return Version string in format "Major.Minor.Patch"
     */
    const char* GetEconomyModuleVersion();

    /**
     * @brief Get module build information
     * 
     * @return Build information string
     */
    const char* GetEconomyModuleBuildInfo();

    // Convenience access to main systems
    inline CMoneySupplyMgr& GetMoneySupplyManager() {
        return CMoneySupplyMgr::Instance();
    }

    inline EconomySystem& GetEconomySystem() {
        return g_EconomySystem;
    }

} // namespace NexusProtection::Economy

// Legacy C interface for backward compatibility
extern "C" {
    /**
     * @brief Initialize economy module (C interface)
     * @return 1 if successful, 0 if failed
     */
    int InitializeEconomy();

    /**
     * @brief Shutdown economy module (C interface)
     */
    void ShutdownEconomy();

    /**
     * @brief Update economy module (C interface)
     * @param deltaTimeMs Delta time in milliseconds
     */
    void UpdateEconomy(unsigned int deltaTimeMs);

    /**
     * @brief Get economy module version (C interface)
     * @return Version string
     */
    const char* GetEconomyVersion();
}

// Global legacy compatibility macros
#define ECONOMY_MODULE_VERSION "1.0.0"
#define ECONOMY_MODULE_BUILD_DATE __DATE__ " " __TIME__

// Legacy global variables for compatibility
extern NexusProtection::Economy::_ECONOMY_SYSTEM e_EconomySystem;
extern NexusProtection::Economy::CMoneySupplyMgr* g_pMoneySupplyMgr;

// Legacy function macros for easy migration
#define eAddDalant(race, amount) NexusProtection::Economy::g_EconomySystem.AddDalant(race, amount)
#define eGetDalant(race) NexusProtection::Economy::g_EconomySystem.GetDalant(race)
#define eGetOldDalant(race) NexusProtection::Economy::g_EconomySystem.GetOldDalant(race)

#define MoneySupplyMgr_Instance() (&NexusProtection::Economy::CMoneySupplyMgr::Instance())
#define MoneySupplyMgr_UpdateSellData(mgr, race, level, className, amount) \
    (mgr)->UpdateSellData(race, level, className, amount)
#define MoneySupplyMgr_UpdateBuyData(mgr, race, level, className, amount) \
    (mgr)->UpdateBuyData(race, level, className, amount)

// Documentation for module usage
/**
 * @page EconomyModuleUsage Economy Module Usage Guide
 * 
 * @section overview Overview
 * The Economy Module provides comprehensive economic management for the NexusProtection game server.
 * 
 * @section initialization Initialization
 * @code{.cpp}
 * // Initialize the economy module
 * if (!NexusProtection::Economy::InitializeEconomyModule()) {
 *     // Handle initialization failure
 *     return false;
 * }
 * @endcode
 * 
 * @section money_tracking Money Tracking
 * @code{.cpp}
 * // Track a sell transaction
 * auto& moneyMgr = NexusProtection::Economy::GetMoneySupplyManager();
 * moneyMgr.UpdateSellData(NexusProtection::Economy::RaceType::Bellato, 50, "Warrior", 1000);
 * 
 * // Track currency addition
 * auto& economySystem = NexusProtection::Economy::GetEconomySystem();
 * economySystem.AddDalant(NexusProtection::Economy::RaceType::Cora, 500);
 * @endcode
 * 
 * @section network_handling Network Handling
 * @code{.cpp}
 * // Handle currency exchange request
 * bool success = NexusProtection::Economy::EconomyNetworkHandlers::HandleExchangeDalantForGoldRequest(
 *     networkInstance, sessionId, requestBuffer);
 * @endcode
 * 
 * @section legacy_compatibility Legacy Compatibility
 * The module maintains full compatibility with existing C code through wrapper functions
 * and global variables. Existing code should continue to work without modification.
 */
