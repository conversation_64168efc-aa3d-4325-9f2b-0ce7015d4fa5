/*
 * Function: ?Update_UnmannedTraderReRegist@CRFWorldDatabase@@QEAAEEKEKKAEBU_SYSTEMTIME@@@Z
 * Address: 0x1404AF990
 */

char __fastcall CRFWorldDatabase::Update_UnmannedTraderReRegist(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, char byState, unsigned int dwPrice, unsigned int dwTax, _SYSTEMTIME *kCurTime)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@4
  int v10; // ecx@4
  int v11; // edx@4
  int v12; // edi@4
  int v13; // er8@4
  unsigned int v14; // er9@4
  unsigned int v15; // er10@4
  char result; // al@5
  __int64 v17; // [sp+0h] [bp-4A8h]@1
  unsigned int dwBuyer; // [sp+20h] [bp-488h]@4
  unsigned int v19; // [sp+28h] [bp-480h]@4
  _SYSTEMTIME *v20; // [sp+30h] [bp-478h]@4
  int v21; // [sp+38h] [bp-470h]@4
  int v22; // [sp+40h] [bp-468h]@4
  int v23; // [sp+48h] [bp-460h]@4
  int v24; // [sp+50h] [bp-458h]@4
  unsigned int v25; // [sp+58h] [bp-450h]@4
  char Dest; // [sp+70h] [bp-438h]@4
  char v27; // [sp+71h] [bp-437h]@4
  unsigned __int64 v28; // [sp+480h] [bp-28h]@4
  CRFWorldDatabase *v29; // [sp+4B0h] [bp+8h]@1
  char v30; // [sp+4B8h] [bp+10h]@1
  unsigned int dwRegistSeriala; // [sp+4C0h] [bp+18h]@1
  char v32; // [sp+4C8h] [bp+20h]@1

  v32 = byState;
  dwRegistSeriala = dwRegistSerial;
  v30 = byType;
  v29 = this;
  v7 = &v17;
  for ( i = 294i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v28 = (unsigned __int64)&v17 ^ _security_cookie;
  Dest = 0;
  memset(&v27, 0, 0x3FFui64);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 0);
  v9 = kCurTime->wMilliseconds;
  v10 = kCurTime->wSecond;
  v11 = kCurTime->wMinute;
  v12 = kCurTime->wHour;
  v13 = kCurTime->wDay;
  v14 = kCurTime->wMonth;
  v15 = kCurTime->wYear;
  v25 = dwPrice;
  v24 = v9;
  v23 = v10;
  v22 = v11;
  v21 = v12;
  LODWORD(v20) = v13;
  v19 = v14;
  dwBuyer = v15;
  sprintf(
    &Dest,
    "{ CALL pUpdate_utsellinfo_reregist( %u, %u, '%04d-%02d-%02d %02d:%02d:%02d.%03d', %u ) }",
    (unsigned __int8)v30,
    dwRegistSeriala);
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v29->vfptr, &Dest, 1) )
  {
    if ( CRFWorldDatabase::Update_UnmannedTraderResutlInfo(v29, v30, dwRegistSeriala, v32, 0, 0, kCurTime) )
    {
      CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v29->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
      result = 1;
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v29->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v29->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
    result = 0;
  }
  return result;
}
