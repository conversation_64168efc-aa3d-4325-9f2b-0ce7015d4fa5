/*
 * Function: j_?PrcoSellUpdateWaitItem@CUnmannedTraderUserInfo@@AEAAXPEAU_qry_case_unmandtrader_log_in_proc_update_complete@@EPEAVCLogFile@@@Z
 * Address: 0x140003DF5
 */

void __fastcall CUnmannedTraderUserInfo::PrcoSellUpdateWaitItem(CUnmannedTraderUserInfo *this, _qry_case_unmandtrader_log_in_proc_update_complete *pkResult, char byGroupType, CLogFile *pkLogger)
{
  CUnmannedTraderUserInfo::PrcoSellUpdateWaitItem(this, pkResult, byGroupType, pkLogger);
}
