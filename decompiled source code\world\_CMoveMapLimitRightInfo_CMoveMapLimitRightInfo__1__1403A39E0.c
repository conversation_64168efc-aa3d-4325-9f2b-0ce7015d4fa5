/*
 * Function: _CMoveMapLimitRightInfo::_CMoveMapLimitRightInfo_::_1_::dtor$0
 * Address: 0x1403A39E0
 */

void __fastcall CMoveMapLimitRightInfo::_CMoveMapLimitRightInfo_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 64));
}
