/*
 * Function: ??0?$<PERSON>ton@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@U?$NewObject@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@@2@$0A@@CryptoPP@@QEAA@U?$NewObject@VDL_SignatureMessageEncodingMethod_DSA@CryptoPP@@@1@@Z
 * Address: 0x14056C710
 */

__int64 __fastcall CryptoPP::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>,0>::Singleton<CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::NewObject<CryptoPP::DL_SignatureMessageEncodingMethod_DSA>,0>(__int64 a1)
{
  return a1;
}
