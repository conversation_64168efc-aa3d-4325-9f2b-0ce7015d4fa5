/*
 * Function: ??1CHackShieldExSystem@@UEAA@XZ
 * Address: 0x140416D70
 */

void __fastcall CHackShieldExSystem::~CHackShieldExSystem(CHackShieldExSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@5
  void *v5; // [sp+28h] [bp-20h]@8
  void *v6; // [sp+30h] [bp-18h]@10
  __int64 v7; // [sp+38h] [bp-10h]@4
  CHackShieldExSystem *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  v8->vfptr = (INationGameGuardSystemVtbl *)&CHackShieldExSystem::`vftable';
  if ( v8->m_ppNodeArray )
  {
    for ( j = 0; j < 2532; ++j )
    {
      if ( v8->m_ppNodeArray[j] )
      {
        v5 = v8->m_ppNodeArray[j];
        operator delete(v5);
        v8->m_ppNodeArray[j] = 0i64;
      }
    }
    v6 = v8->m_ppNodeArray;
    operator delete[](v6);
    v8->m_ppNodeArray = 0i64;
  }
  if ( v8->m_bInit )
    _AntiCpSvr_Finalize();
  CMyTimer::~CMyTimer(&v8->m_tmLoopTime);
  INationGameGuardSystem::~INationGameGuardSystem((INationGameGuardSystem *)&v8->vfptr);
}
