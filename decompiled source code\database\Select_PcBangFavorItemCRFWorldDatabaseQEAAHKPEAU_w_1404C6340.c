/*
 * Function: ?Select_PcBangFavorItem@CRFWorldDatabase@@QEAAHKPEAU_worlddb_pcbang_favor_item@@@Z
 * Address: 0x1404C6340
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PcBangFavorItem(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_pcbang_favor_item *pPcBangFavorItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-498h]@1
  void *SQLStmt; // [sp+20h] [bp-478h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-470h]@24
  char DstBuf; // [sp+40h] [bp-458h]@4
  SQLLEN v10; // [sp+458h] [bp-40h]@24
  __int16 v11; // [sp+464h] [bp-34h]@9
  unsigned __int8 v12; // [sp+468h] [bp-30h]@16
  int v13; // [sp+46Ch] [bp-2Ch]@22
  int j; // [sp+470h] [bp-28h]@22
  unsigned __int64 v15; // [sp+480h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+4A0h] [bp+8h]@1
  _worlddb_pcbang_favor_item *v17; // [sp+4B0h] [bp+18h]@1

  v17 = pPcBangFavorItem;
  v16 = this;
  v3 = &v6;
  for ( i = 292i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s(&DstBuf, 0x400ui64, "{ CALL pSelect_PcbangItem( %d ) }", dwSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &DstBuf);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirect_0(v16->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v12 = 0;
        if ( v11 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
          v12 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v12;
      }
      else
      {
        v13 = 0;
        for ( j = 0; j < 50; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v16->m_hStmtSelect, v13, -25, (char *)v17 + 8 * j, 0i64, &v10);
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        if ( v16->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &DstBuf);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
