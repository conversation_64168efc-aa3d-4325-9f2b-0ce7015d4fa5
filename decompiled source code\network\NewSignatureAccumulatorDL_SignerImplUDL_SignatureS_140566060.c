/*
 * Function: ?NewSignatureAccumulator@?$DL_SignerImpl@U?$DL_SignatureSchemeOptions@V?$DL_SS@U?$DL_Keys_ECDSA@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VEC2N@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@U?$DL_Keys_ECDSA@VEC2N@CryptoPP@@@2@V?$DL_Algorithm_ECDSA@VEC2N@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@CryptoPP@@@CryptoPP@@UEBAPEAVPK_MessageAccumulator@2@AEAVRandomNumberGenerator@2@@Z
 * Address: 0x140566060
 */

__int64 __fastcall CryptoPP::DL_SignerImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_Keys_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::EC2N>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>::NewSignatureAccumulator(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@4
  __int64 v3; // rax@4
  char v5; // [sp+20h] [bp-38h]@4
  CryptoPP::PK_MessageAccumulatorBase *v6; // [sp+28h] [bp-30h]@4
  CryptoPP::PK_MessageAccumulatorBase *v7; // [sp+30h] [bp-28h]@1
  __int64 v8; // [sp+38h] [bp-20h]@4
  __int64 v9; // [sp+40h] [bp-18h]@1
  CryptoPP::PK_MessageAccumulatorBase *v10; // [sp+48h] [bp-10h]@2
  __int64 v11; // [sp+60h] [bp+8h]@1
  __int64 v12; // [sp+68h] [bp+10h]@1

  v12 = a2;
  v11 = a1;
  v9 = -2i64;
  v7 = (CryptoPP::PK_MessageAccumulatorBase *)operator new(0x180ui64);
  if ( v7 )
    v10 = CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::PK_MessageAccumulatorImpl<CryptoPP::SHA1>(v7);
  else
    v10 = 0i64;
  v6 = v10;
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>(&v5, v10);
  LODWORD(v2) = std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::operator*(&v5);
  CryptoPP::DL_SignerBase<CryptoPP::EC2NPoint>::RestartMessageAccumulator(v11, v12, v2);
  LODWORD(v3) = std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::release(&v5);
  v8 = v3;
  std::auto_ptr<CryptoPP::PK_MessageAccumulatorBase>::~auto_ptr<CryptoPP::PK_MessageAccumulatorBase>(&v5);
  return v8;
}
