/*
 * Function: ?ConvertGeneralDB@CPlayerDB@@QEAA_NPEAU_AVATOR_DATA@@0@Z
 * Address: 0x140109450
 */

char __fastcall CPlayerDB::ConvertGeneralDB(CPlayerDB *this, _AVATOR_DATA *pData, _AVATOR_DATA *pOutData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  LendItemMng *v5; // rax@17
  LendItemMng *v6; // rax@33
  LendItemMng *v7; // rax@49
  char v8; // al@56
  unsigned int v9; // eax@56
  LendItemMng *v10; // rax@65
  LendItemMng *v11; // rax@86
  LendItemMng *v12; // rax@102
  LendItemMng *v13; // rax@118
  __int64 v15; // [sp+0h] [bp-148h]@1
  __time32_t Time; // [sp+24h] [bp-124h]@4
  unsigned int v17; // [sp+34h] [bp-114h]@4
  int nLinkIndex; // [sp+38h] [bp-110h]@4
  _INVENKEY *v19; // [sp+40h] [bp-108h]@7
  _TimeItem_fld *v20; // [sp+48h] [bp-100h]@11
  _TimeItem_fld *v21; // [sp+50h] [bp-F8h]@27
  _EMBELLKEY *v22; // [sp+58h] [bp-F0h]@39
  _TimeItem_fld *v23; // [sp+60h] [bp-E8h]@43
  _FORCEKEY *v24; // [sp+68h] [bp-E0h]@55
  _TimeItem_fld *v25; // [sp+70h] [bp-D8h]@59
  _ANIMUSKEY *v26; // [sp+78h] [bp-D0h]@76
  _TimeItem_fld *v27; // [sp+80h] [bp-C8h]@80
  _INVENKEY *v28; // [sp+88h] [bp-C0h]@92
  _TimeItem_fld *v29; // [sp+90h] [bp-B8h]@96
  _INVENKEY *v30; // [sp+98h] [bp-B0h]@108
  _TimeItem_fld *v31; // [sp+A0h] [bp-A8h]@112
  _INVENKEY *v32; // [sp+A8h] [bp-A0h]@124
  _LINKKEY *v33; // [sp+B0h] [bp-98h]@129
  unsigned __int16 v34; // [sp+B8h] [bp-90h]@131
  int v35; // [sp+BCh] [bp-8Ch]@131
  int v36; // [sp+C0h] [bp-88h]@131
  char *v37; // [sp+C8h] [bp-80h]@133
  _STORAGE_LIST::_db_con *pkItem; // [sp+D0h] [bp-78h]@17
  CPlayer *v39; // [sp+D8h] [bp-70h]@17
  _STORAGE_LIST::_db_con *v40; // [sp+E0h] [bp-68h]@33
  CPlayer *v41; // [sp+E8h] [bp-60h]@33
  _STORAGE_LIST::_db_con *v42; // [sp+F0h] [bp-58h]@49
  CPlayer *v43; // [sp+F8h] [bp-50h]@49
  _STORAGE_LIST::_db_con *v44; // [sp+100h] [bp-48h]@65
  CPlayer *v45; // [sp+108h] [bp-40h]@65
  _STORAGE_LIST::_db_con *v46; // [sp+110h] [bp-38h]@86
  CPlayer *v47; // [sp+118h] [bp-30h]@86
  _STORAGE_LIST::_db_con *v48; // [sp+120h] [bp-28h]@102
  CPlayer *v49; // [sp+128h] [bp-20h]@102
  _STORAGE_LIST::_db_con *v50; // [sp+130h] [bp-18h]@118
  CPlayer *v51; // [sp+138h] [bp-10h]@118
  CPlayerDB *v52; // [sp+150h] [bp+8h]@1
  _AVATOR_DATA *v53; // [sp+158h] [bp+10h]@1
  _AVATOR_DATA *v54; // [sp+160h] [bp+18h]@1

  v54 = pOutData;
  v53 = pData;
  v52 = this;
  v3 = &v15;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _time32(&Time);
  v17 = Time;
  for ( nLinkIndex = 0; nLinkIndex < 20 * v53->dbAvator.m_byBagNum; ++nLinkIndex )
  {
    v19 = (_INVENKEY *)((char *)&v53->dbInven + 37 * nLinkIndex);
    if ( _INVENKEY::IsFilled(v19) )
    {
      v52->m_dbInven.m_pStorageList[nLinkIndex].m_byTableCode = v19->byTableCode;
      v52->m_dbInven.m_pStorageList[nLinkIndex].m_byClientIndex = v19->bySlotIndex;
      v52->m_dbInven.m_pStorageList[nLinkIndex].m_wItemIndex = v19->wItemIndex;
      v52->m_dbInven.m_pStorageList[nLinkIndex].m_dwDur = *(_QWORD *)&v19[1].bySlotIndex;
      v52->m_dbInven.m_pStorageList[nLinkIndex].m_dwLv = (unsigned int)v19[3];
      v52->m_dbInven.m_pStorageList[nLinkIndex].m_bLoad = 1;
      _STORAGE_LIST::_db_con::SetSerialNumber(&v52->m_dbInven.m_pStorageList[nLinkIndex], *(_DWORD *)&v19[4]);
      v54->dbInven.m_List[nLinkIndex].dwItemETSerial = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbInven.m_pStorageList[nLinkIndex]);
      if ( *(_QWORD *)&v19[5].bySlotIndex )
      {
        v52->m_dbInven.m_pStorageList[nLinkIndex].m_lnUID = *(_QWORD *)&v19[5].bySlotIndex;
      }
      else
      {
        v52->m_dbInven.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbInven.m_List[nLinkIndex].lnUID = v52->m_dbInven.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v20 = TimeItem::FindTimeRec(
              v52->m_dbInven.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbInven.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v20 && v20->m_nCheckType )
      {
        if ( v20->m_nCheckType == 1 )
        {
          v52->m_dbInven.m_pStorageList[nLinkIndex].m_dwT = *(_DWORD *)&v19[7].byTableCode + v17;
        }
        else if ( v20->m_nCheckType == 2 )
        {
          v52->m_dbInven.m_pStorageList[nLinkIndex].m_dwT = *(unsigned int *)((char *)v19 + 29);
        }
        v19[7].bySlotIndex = v20->m_nCheckType;
        v52->m_dbInven.m_pStorageList[nLinkIndex].m_byCsMethod = v19[7].bySlotIndex;
        *(_DWORD *)&v19[8].byTableCode = v17;
        v52->m_dbInven.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v54->dbInven.m_List[nLinkIndex].byCsMethod = v52->m_dbInven.m_pStorageList[nLinkIndex].m_byCsMethod;
        v54->dbInven.m_List[nLinkIndex].dwT = v52->m_dbInven.m_pStorageList[nLinkIndex].m_dwT;
        pkItem = &v52->m_dbInven.m_pStorageList[nLinkIndex];
        v39 = v52->m_pThis;
        v5 = LendItemMng::Instance();
        LendItemMng::InsertLink(v5, v39->m_ObjID.m_wIndex, 0, pkItem);
      }
      else
      {
        v19[7].bySlotIndex = 0;
        v52->m_dbInven.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
    }
  }
  for ( nLinkIndex = 0; nLinkIndex < 8; ++nLinkIndex )
  {
    if ( _EQUIPKEY::IsFilled(&v53->dbAvator.m_EquipKey[nLinkIndex]) )
    {
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_byTableCode = nLinkIndex;
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_byClientIndex = nLinkIndex;
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_wItemIndex = v53->dbAvator.m_EquipKey[nLinkIndex].zItemIndex;
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_dwDur = 0i64;
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_dwLv = v53->dbAvator.m_dwFixEquipLv[nLinkIndex];
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbEquip.m_pStorageList[nLinkIndex].m_bLock = 0;
      _STORAGE_LIST::_db_con::SetSerialNumber(
        &v52->m_dbEquip.m_pStorageList[nLinkIndex],
        v53->dbAvator.m_dwItemETSerial[nLinkIndex]);
      v54->dbAvator.m_dwItemETSerial[nLinkIndex] = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbEquip.m_pStorageList[nLinkIndex]);
      if ( v53->dbAvator.m_lnUID[nLinkIndex] )
      {
        v52->m_dbEquip.m_pStorageList[nLinkIndex].m_lnUID = v53->dbAvator.m_lnUID[nLinkIndex];
      }
      else
      {
        v52->m_dbEquip.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbAvator.m_lnUID[nLinkIndex] = v52->m_dbEquip.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v21 = TimeItem::FindTimeRec(
              v52->m_dbEquip.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbEquip.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v21 && v21->m_nCheckType )
      {
        if ( v21->m_nCheckType == 1 )
        {
          v52->m_dbEquip.m_pStorageList[nLinkIndex].m_dwT = v53->dbAvator.m_dwET[nLinkIndex] + v17;
        }
        else if ( v21->m_nCheckType == 2 )
        {
          v52->m_dbEquip.m_pStorageList[nLinkIndex].m_dwT = v53->dbAvator.m_dwET[nLinkIndex];
        }
        v53->dbAvator.m_byCsMethod[nLinkIndex] = v21->m_nCheckType;
        v52->m_dbEquip.m_pStorageList[nLinkIndex].m_byCsMethod = v53->dbAvator.m_byCsMethod[nLinkIndex];
        v53->dbAvator.m_dwLendRegdTime[nLinkIndex] = v17;
        v52->m_dbEquip.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v40 = &v52->m_dbEquip.m_pStorageList[nLinkIndex];
        v41 = v52->m_pThis;
        v6 = LendItemMng::Instance();
        LendItemMng::InsertLink(v6, v41->m_ObjID.m_wIndex, 1, v40);
      }
      else
      {
        v53->dbAvator.m_byCsMethod[nLinkIndex] = 0;
        v52->m_dbEquip.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
      CPlayer::SetEffectEquipCode(v52->m_pThis, 1, nLinkIndex, 2);
    }
  }
  for ( nLinkIndex = 0; nLinkIndex < 7; ++nLinkIndex )
  {
    v22 = (_EMBELLKEY *)((char *)&v53->dbEquip + 27 * nLinkIndex);
    if ( _EMBELLKEY::IsFilled(v22) )
    {
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_byTableCode = v22->byTableCode;
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_byClientIndex = v22->bySlotIndex;
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_wItemIndex = v22->wItemIndex;
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_dwDur = *(_WORD *)&v22[1].bySlotIndex;
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_dwLv = 0xFFFFFFF;
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_bLock = 0;
      _STORAGE_LIST::_db_con::SetSerialNumber(
        &v52->m_dbEmbellish.m_pStorageList[nLinkIndex],
        *(_DWORD *)&v22[1].wItemIndex);
      v54->dbEquip.m_EmbellishList[nLinkIndex].dwItemETSerial = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbEmbellish.m_pStorageList[nLinkIndex]);
      if ( *(_QWORD *)&v22[2].wItemIndex )
      {
        v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_lnUID = *(_QWORD *)&v22[2].wItemIndex;
      }
      else
      {
        v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbEquip.m_EmbellishList[nLinkIndex].lnUID = v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v23 = TimeItem::FindTimeRec(
              v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v23 && v23->m_nCheckType )
      {
        if ( v23->m_nCheckType == 1 )
        {
          v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_dwT = *(_DWORD *)((char *)&v22[4].wItemIndex + 1) + v17;
        }
        else if ( v23->m_nCheckType == 2 )
        {
          v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_dwT = *(unsigned int *)((char *)v22 + 19);
        }
        LOBYTE(v22[4].wItemIndex) = v23->m_nCheckType;
        v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_byCsMethod = v22[4].wItemIndex;
        *(_DWORD *)((char *)&v22[5].wItemIndex + 1) = v17;
        v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v42 = &v52->m_dbEmbellish.m_pStorageList[nLinkIndex];
        v43 = v52->m_pThis;
        v7 = LendItemMng::Instance();
        LendItemMng::InsertLink(v7, v43->m_ObjID.m_wIndex, 2, v42);
      }
      else
      {
        LOBYTE(v22[4].wItemIndex) = 0;
        v52->m_dbEmbellish.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
      CPlayer::SetEffectEquipCode(v52->m_pThis, 2, nLinkIndex, 2);
    }
  }
  for ( nLinkIndex = 0; nLinkIndex < 88; ++nLinkIndex )
  {
    v24 = (_FORCEKEY *)((char *)&v53->dbForce + 25 * nLinkIndex);
    if ( _FORCEKEY::IsFilled(v24) )
    {
      v52->m_dbForce.m_pStorageList[nLinkIndex].m_byTableCode = 15;
      v8 = _FORCEKEY::GetIndex(v24);
      v52->m_dbForce.m_pStorageList[nLinkIndex].m_wItemIndex = (unsigned __int8)v8;
      v9 = _FORCEKEY::GetStat(v24);
      v52->m_dbForce.m_pStorageList[nLinkIndex].m_dwDur = v9;
      v52->m_dbForce.m_pStorageList[nLinkIndex].m_dwLv = 0xFFFFFFF;
      v52->m_dbForce.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbForce.m_pStorageList[nLinkIndex].m_bLock = 0;
      _STORAGE_LIST::_db_con::SetSerialNumber(&v52->m_dbForce.m_pStorageList[nLinkIndex], v24[1].dwKey);
      v54->dbForce.m_List[nLinkIndex].dwItemETSerial = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbForce.m_pStorageList[nLinkIndex]);
      if ( *(_QWORD *)&v24[2].dwKey )
      {
        v52->m_dbForce.m_pStorageList[nLinkIndex].m_lnUID = *(_QWORD *)&v24[2].dwKey;
      }
      else
      {
        v52->m_dbForce.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbForce.m_List[nLinkIndex].lnUID = v52->m_dbForce.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v25 = TimeItem::FindTimeRec(
              v52->m_dbForce.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbForce.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v25 && v25->m_nCheckType )
      {
        if ( v25->m_nCheckType == 1 )
        {
          v52->m_dbForce.m_pStorageList[nLinkIndex].m_dwT = *(unsigned int *)((char *)&v24[4].dwKey + 1) + v17;
        }
        else if ( v25->m_nCheckType == 2 )
        {
          v52->m_dbForce.m_pStorageList[nLinkIndex].m_dwT = *(unsigned int *)((char *)v24 + 17);
        }
        LOBYTE(v24[4].dwKey) = v25->m_nCheckType;
        v52->m_dbForce.m_pStorageList[nLinkIndex].m_byCsMethod = v24[4].dwKey;
        *(unsigned int *)((char *)&v24[5].dwKey + 1) = v17;
        v52->m_dbForce.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v44 = &v52->m_dbForce.m_pStorageList[nLinkIndex];
        v45 = v52->m_pThis;
        v10 = LendItemMng::Instance();
        LendItemMng::InsertLink(v10, v45->m_ObjID.m_wIndex, 3, v44);
      }
      else
      {
        LOBYTE(v24[4].dwKey) = 0;
        v52->m_dbForce.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
    }
  }
  for ( nLinkIndex = 0; nLinkIndex < 20; ++nLinkIndex )
  {
    if ( _INVENKEY::IsFilled((_INVENKEY *)&v53->dbCutting.m_List[nLinkIndex]) )
      v52->m_wCuttingResBuffer[v53->dbCutting.m_List[nLinkIndex].Key.wItemIndex] = v53->dbCutting.m_List[nLinkIndex].dwDur;
  }
  memcpy_0(&v52->m_QuestDB, &v53->dbQuest, 0x638ui64);
  memcpy_0(&v52->m_UnitDB, &v53->dbUnit, 0xF8ui64);
  memcpy_0(&v52->m_ItemCombineDB, &v53->dbItemCombineEx, 0x134ui64);
  for ( nLinkIndex = 0; nLinkIndex < 4; ++nLinkIndex )
  {
    v26 = (_ANIMUSKEY *)&v53->dbAnimus + 34 * nLinkIndex;
    if ( _ANIMUSKEY::IsFilled(v26) )
    {
      v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_byTableCode = 24;
      v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_wItemIndex = v26->byItemIndex;
      v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_dwDur = *(_QWORD *)&v26[1].byItemIndex;
      v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_dwLv = *(_DWORD *)&v26[9].byItemIndex;
      v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_bLock = 0;
      _STORAGE_LIST::_db_con::SetSerialNumber(
        &v52->m_dbAnimus.m_pStorageList[nLinkIndex],
        *(_DWORD *)&v26[13].byItemIndex);
      v54->dbAnimus.m_List[nLinkIndex].dwItemETSerial = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbAnimus.m_pStorageList[nLinkIndex]);
      if ( *(_QWORD *)&v26[17].byItemIndex )
      {
        v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_lnUID = *(_QWORD *)&v26[17].byItemIndex;
      }
      else
      {
        v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbAnimus.m_List[nLinkIndex].lnUID = v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v27 = TimeItem::FindTimeRec(
              v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v27 && v27->m_nCheckType )
      {
        if ( v27->m_nCheckType == 1 )
        {
          v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_dwT = *(_DWORD *)&v26[26].byItemIndex + v17;
        }
        else if ( v27->m_nCheckType == 2 )
        {
          v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_dwT = *(_DWORD *)&v26[26].byItemIndex;
        }
        v26[25].byItemIndex = v27->m_nCheckType;
        v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_byCsMethod = v26[25].byItemIndex;
        *(_DWORD *)&v26[30].byItemIndex = v17;
        v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v46 = &v52->m_dbAnimus.m_pStorageList[nLinkIndex];
        v47 = v52->m_pThis;
        v11 = LendItemMng::Instance();
        LendItemMng::InsertLink(v11, v47->m_ObjID.m_wIndex, 4, v46);
      }
      else
      {
        v26[25].byItemIndex = 0;
        v52->m_dbAnimus.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
    }
  }
  strcpy_0(v52->m_wszTrunkPasswd, v53->dbTrunk.wszPasswd);
  v52->m_dTrunkDalant = v53->dbTrunk.dDalant;
  v52->m_dTrunkGold = v53->dbTrunk.dGold;
  v52->m_byTrunkHintIndex = v53->dbTrunk.byHintIndex;
  strcpy_0(v52->m_wszTrunkHintAnswer, v53->dbTrunk.wszHintAnswer);
  v52->m_byTrunkSlotNum = v53->dbTrunk.bySlotNum;
  for ( nLinkIndex = 0; nLinkIndex < v52->m_byTrunkSlotNum; ++nLinkIndex )
  {
    v28 = &v53->dbTrunk.m_List[nLinkIndex].Key;
    if ( _INVENKEY::IsFilled(v28) )
    {
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_byTableCode = v28->byTableCode;
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_byClientIndex = v28->bySlotIndex;
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_wItemIndex = v28->wItemIndex;
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_dwDur = *(_QWORD *)&v28[1].bySlotIndex;
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_dwLv = (unsigned int)v28[3];
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_bLock = 0;
      _STORAGE_LIST::_db_con::SetSerialNumber(
        &v52->m_dbTrunk.m_pStorageList[nLinkIndex],
        *(_DWORD *)&v28[4].byTableCode);
      v54->dbTrunk.m_List[nLinkIndex].dwItemETSerial = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbTrunk.m_pStorageList[nLinkIndex]);
      v52->m_dbTrunk.m_byItemSlotRace[nLinkIndex] = v28[4].bySlotIndex;
      if ( *(_QWORD *)&v28[5].byTableCode )
      {
        v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_lnUID = *(_QWORD *)&v28[5].byTableCode;
      }
      else
      {
        v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbTrunk.m_List[nLinkIndex].lnUID = v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v29 = TimeItem::FindTimeRec(
              v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v29 && v29->m_nCheckType )
      {
        if ( v29->m_nCheckType == 1 )
        {
          v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_dwT = *(_DWORD *)&v28[7].wItemIndex + v17;
        }
        else if ( v29->m_nCheckType == 2 )
        {
          v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_dwT = *(unsigned int *)((char *)v28 + 30);
        }
        v28[7].byTableCode = v29->m_nCheckType;
        v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_byCsMethod = v28[7].byTableCode;
        *(_DWORD *)&v28[8].wItemIndex = v17;
        v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v48 = &v52->m_dbTrunk.m_pStorageList[nLinkIndex];
        v49 = v52->m_pThis;
        v12 = LendItemMng::Instance();
        LendItemMng::InsertLink(v12, v49->m_ObjID.m_wIndex, 5, v48);
      }
      else
      {
        v28[7].byTableCode = 0;
        v52->m_dbTrunk.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
    }
  }
  v52->m_byExtTrunkSlotNum = v53->dbTrunk.byExtSlotNum;
  for ( nLinkIndex = 0; nLinkIndex < v52->m_byExtTrunkSlotNum; ++nLinkIndex )
  {
    v30 = &v53->dbTrunk.m_ExtList[nLinkIndex].Key;
    if ( _INVENKEY::IsFilled(v30) )
    {
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_byTableCode = v30->byTableCode;
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_byClientIndex = v30->bySlotIndex;
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_wItemIndex = v30->wItemIndex;
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_dwDur = *(_QWORD *)&v30[1].bySlotIndex;
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_dwLv = (unsigned int)v30[3];
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_bLock = 0;
      _STORAGE_LIST::_db_con::SetSerialNumber(
        &v52->m_dbExtTrunk.m_pStorageList[nLinkIndex],
        *(_DWORD *)&v30[4].byTableCode);
      v54->dbTrunk.m_ExtList[nLinkIndex].dwItemETSerial = _STORAGE_LIST::_db_con::GetSerialNumber(&v52->m_dbExtTrunk.m_pStorageList[nLinkIndex]);
      v52->m_dbExtTrunk.m_byItemSlotRace[nLinkIndex] = v30[4].bySlotIndex;
      if ( *(_QWORD *)&v30[5].byTableCode )
      {
        v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_lnUID = *(_QWORD *)&v30[5].byTableCode;
      }
      else
      {
        v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        v54->dbTrunk.m_ExtList[nLinkIndex].lnUID = v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_lnUID;
      }
      v31 = TimeItem::FindTimeRec(
              v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_byTableCode,
              v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_wItemIndex);
      if ( v31 && v31->m_nCheckType )
      {
        if ( v31->m_nCheckType == 1 )
        {
          v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_dwT = *(_DWORD *)&v30[7].wItemIndex + v17;
        }
        else if ( v31->m_nCheckType == 2 )
        {
          v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_dwT = *(unsigned int *)((char *)v30 + 30);
        }
        v30[7].byTableCode = v31->m_nCheckType;
        v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_byCsMethod = v30[7].byTableCode;
        *(_DWORD *)&v30[8].wItemIndex = v17;
        v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_dwLendRegdTime = v17;
        v50 = &v52->m_dbExtTrunk.m_pStorageList[nLinkIndex];
        v51 = v52->m_pThis;
        v13 = LendItemMng::Instance();
        LendItemMng::InsertLink(v13, v51->m_ObjID.m_wIndex, 7, v50);
      }
      else
      {
        v30[7].byTableCode = 0;
        v52->m_dbExtTrunk.m_pStorageList[nLinkIndex].m_byCsMethod = 0;
      }
    }
  }
  v52->m_bTrunkOpen = 0;
  _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v52->m_dbTrunk.m_nListNum, v52->m_byTrunkSlotNum);
  _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&v52->m_dbExtTrunk.m_nListNum, v52->m_byExtTrunkSlotNum);
  v52->m_bPersonalAmineInven = v53->dbPersonalAmineInven.bUsable;
  for ( nLinkIndex = 0; nLinkIndex < 40; ++nLinkIndex )
  {
    v32 = (_INVENKEY *)&v53->dbPersonalAmineInven.m_List[nLinkIndex];
    if ( _INVENKEY::IsFilled(v32) )
    {
      v52->m_dbPersonalAmineInven.m_pStorageList[nLinkIndex].m_byTableCode = v32->byTableCode;
      v52->m_dbPersonalAmineInven.m_pStorageList[nLinkIndex].m_byClientIndex = v32->bySlotIndex;
      v52->m_dbPersonalAmineInven.m_pStorageList[nLinkIndex].m_wItemIndex = v32->wItemIndex;
      v52->m_dbPersonalAmineInven.m_pStorageList[nLinkIndex].m_dwDur = (unsigned __int64)v32[1];
      v52->m_dbPersonalAmineInven.m_pStorageList[nLinkIndex].m_bLoad = 1;
      v52->m_dbPersonalAmineInven.m_pStorageList[nLinkIndex].m_bLock = 0;
    }
  }
  AutominePersonal::LoadDBComplete(v52->m_pAPM);
  CPlayerDB::AppointSerialStorageItem(v52);
  for ( nLinkIndex = 0; nLinkIndex < 50; ++nLinkIndex )
  {
    v33 = (_LINKKEY *)((char *)&v53->dbLink + 2 * nLinkIndex);
    if ( _LINKKEY::IsFilled(v33) && _LINKKEY::GetCode(v33) == 4 )
    {
      v34 = _LINKKEY::GetIndex(v33);
      v35 = (signed int)v34 >> 8;
      v36 = (unsigned __int8)v34;
      if ( IsStorageRange(SHIBYTE(v34), v34) )
      {
        v37 = &v52->m_pStoragePtr[v35]->m_pStorageList[v36].m_bLoad;
        if ( *v37 )
        {
          if ( !CPlayerDB::PushLink(v52, nLinkIndex, *(_WORD *)(v37 + 17), 1) )
            _LINK_DB_BASE::_LIST::Init((_LINK_DB_BASE::_LIST *)v33);
        }
        else
        {
          _LINK_DB_BASE::_LIST::Init((_LINK_DB_BASE::_LIST *)v33);
        }
      }
      else
      {
        _LINK_DB_BASE::_LIST::Init((_LINK_DB_BASE::_LIST *)v33);
      }
    }
  }
  v52->m_dPvpPointLeak = v53->dbSupplement.dPvpPointLeak;
  return 1;
}
