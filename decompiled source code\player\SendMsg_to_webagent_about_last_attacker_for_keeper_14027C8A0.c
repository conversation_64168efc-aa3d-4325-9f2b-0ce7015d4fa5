/*
 * Function: ?SendMsg_to_webagent_about_last_attacker_for_keeper@CHolyStoneSystem@@QEAAXPEAVCPlayer@@H@Z
 * Address: 0x14027C8A0
 */

void __fastcall CHolyStoneSystem::SendMsg_to_webagent_about_last_attacker_for_keeper(CHolyStoneSystem *this, CPlayer *pPlayer, int bByAnimus)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@5
  __int64 v6; // [sp+0h] [bp-A8h]@1
  unsigned __int16 nLen[2]; // [sp+20h] [bp-88h]@6
  int v8; // [sp+28h] [bp-80h]@6
  int Dst; // [sp+38h] [bp-70h]@4
  char v10; // [sp+3Ch] [bp-6Ch]@4
  unsigned __int16 v11; // [sp+3Dh] [bp-6Bh]@4
  char v12; // [sp+3Fh] [bp-69h]@4
  char v13; // [sp+40h] [bp-68h]@4
  char Dest; // [sp+41h] [bp-67h]@5
  int v15; // [sp+52h] [bp-56h]@7
  char pbyType; // [sp+74h] [bp-34h]@7
  char v17; // [sp+75h] [bp-33h]@7
  CLogFile *v18; // [sp+90h] [bp-18h]@6
  unsigned __int64 v19; // [sp+98h] [bp-10h]@4
  CHolyStoneSystem *v20; // [sp+B0h] [bp+8h]@1
  CPlayer *v21; // [sp+B8h] [bp+10h]@1
  int v22; // [sp+C0h] [bp+18h]@1

  v22 = bByAnimus;
  v21 = pPlayer;
  v20 = this;
  v3 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = (unsigned __int64)&v6 ^ _security_cookie;
  memset_0(&Dst, 0, 0x1Eui64);
  Dst = unk_1799C608C;
  v10 = CHolyStoneSystem::GetNumOfTime(v20);
  v11 = CHolyStoneSystem::GetStartYear(v20);
  v12 = CHolyStoneSystem::GetStartMonth(v20);
  v13 = CHolyStoneSystem::GetStartDay(v20);
  if ( v21 )
  {
    v5 = CPlayerDB::GetCharNameW(&v21->m_Param);
    strcpy_0(&Dest, v5);
    v15 = v22;
    pbyType = 51;
    v17 = 19;
    if ( unk_1799C9ADE )
      CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, (char *)&Dst, 0x1Eu);
  }
  else
  {
    v18 = &v20->m_logQuestDestroy;
    v8 = (unsigned __int8)v10;
    *(_DWORD *)nLen = (unsigned __int8)v13;
    CLogFile::Write(
      &v20->m_logQuestDestroy,
      ">> Laster Attacker for Keeper[%04d%02d%02d][Cnt:%d] - pPlayer Error",
      v11,
      (unsigned __int8)v12);
  }
}
