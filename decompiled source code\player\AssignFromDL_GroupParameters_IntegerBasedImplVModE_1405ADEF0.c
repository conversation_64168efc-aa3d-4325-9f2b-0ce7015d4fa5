/*
 * Function: ?AssignFrom@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@$4PPPPPPPM@A@EAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405ADEF0
 */

int __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AssignFrom(__int64 a1, __int64 a2)
{
  return CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::Assign<PERSON>rom(
           a1 - *(_DWORD *)(a1 - 4),
           a2);
}
