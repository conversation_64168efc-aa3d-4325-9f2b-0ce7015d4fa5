/*
 * Function: ?AnalysisMsg@CBossMonsterScheduleSystem@@IEAAXPEAUScheduleMSG@@@Z
 * Address: 0x140419CB0
 */

void __fastcall CBossMonsterScheduleSystem::AnalysisMsg(CBossMonsterScheduleSystem *this, ScheduleMSG *pMSG)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char *strKey; // [sp+20h] [bp-18h]@7
  int *v6; // [sp+28h] [bp-10h]@7
  CBossMonsterScheduleSystem *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pMSG->m_byKey < 3 && v7->m_pCurTBL && pMSG->m_wIniFileIndex < v7->m_pCurTBL->m_nCount )
  {
    strKey = (&BossSchedule::ms_KeyPair)[8 * pMSG->m_byKey];
    v6 = &v7->m_pCurTBL->m_MapScheduleList[pMSG->m_wIniFileIndex]->m_nIndex;
    if ( v6 )
      CIniFile::WriteString((CIniFile *)(v6 + 18), pMSG->m_strSection, strKey, pMSG->m_strValue);
  }
}
