/*
 * Function: ?Update_UnmannedTraderSellInfoPrice@CRFWorldDatabase@@QEAA_NEKKK@Z
 * Address: 0x1404AD140
 */

bool __fastcall CRFWorldDatabase::Update_UnmannedTraderSellInfoPrice(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, unsigned int dwOwner, unsigned int dwNewPrice)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-468h]@1
  unsigned int v9; // [sp+20h] [bp-448h]@4
  unsigned int v10; // [sp+28h] [bp-440h]@4
  char Dest; // [sp+40h] [bp-428h]@4
  unsigned __int64 v12; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+470h] [bp+8h]@1

  v13 = this;
  v5 = &v8;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = (unsigned __int64)&v8 ^ _security_cookie;
  v10 = dwNewPrice;
  v9 = dwOwner;
  sprintf(&Dest, "{ CALL pUpdate_utsellinfoprice( %u, %u, %u, %u ) }", (unsigned __int8)byType, dwRegistSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v13->vfptr, &Dest, 0);
}
