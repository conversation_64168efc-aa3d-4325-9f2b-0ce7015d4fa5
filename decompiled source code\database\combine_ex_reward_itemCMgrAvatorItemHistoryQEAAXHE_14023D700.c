/*
 * Function: ?combine_ex_reward_item@CMgrAvatorItemHistory@@QEAAXHEPEAU_ITEMCOMBINE_DB_BASE@@PEAEPEA_KPEAD@Z
 * Address: 0x14023D700
 */

void __fastcall CMgrAvatorItemHistory::combine_ex_reward_item(CMgrAvatorItemHistory *this, int n, char by<PERSON><PERSON><PERSON><PERSON>, _ITEMCOMBINE_DB_BASE *pCombineDB, char *pbyRewardTypeList, unsigned __int64 *lnUIDs, char *strFileName)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // er8@5
  char *v10; // rax@15
  __int64 v11; // [sp+0h] [bp-88h]@1
  char *v12; // [sp+20h] [bp-68h]@5
  unsigned __int64 v13; // [sp+28h] [bp-60h]@5
  unsigned int v14; // [sp+30h] [bp-58h]@5
  char *v15; // [sp+38h] [bp-50h]@5
  char *v16; // [sp+40h] [bp-48h]@5
  unsigned __int16 v17; // [sp+50h] [bp-38h]@5
  int j; // [sp+54h] [bp-34h]@5
  _base_fld *v19; // [sp+58h] [bp-30h]@10
  char *v20; // [sp+60h] [bp-28h]@10
  int v21; // [sp+68h] [bp-20h]@5
  int v22; // [sp+6Ch] [bp-1Ch]@7
  __int64 v23; // [sp+70h] [bp-18h]@15
  CMgrAvatorItemHistory *v24; // [sp+90h] [bp+8h]@1
  char v25; // [sp+A0h] [bp+18h]@1
  _ITEMCOMBINE_DB_BASE *v26; // [sp+A8h] [bp+20h]@1

  v26 = pCombineDB;
  v25 = byMakeNum;
  v24 = this;
  v7 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( pCombineDB )
  {
    v17 = GetExcelIndexFromCombineExCheckKey(pCombineDB->m_dwCheckKey);
    v9 = v26->m_byDlgType;
    v21 = v17 + 1;
    v16 = v24->m_szCurTime;
    v15 = v24->m_szCurDate;
    v14 = v26->m_dwDalant;
    LODWORD(v13) = (unsigned __int8)v25;
    LODWORD(v12) = v9;
    sprintf(
      sData,
      "\r\nCOMBINE_EX[REWARD]\r\n\tCombine%d@%d, type:%d, num:%d, (D:%u) [%s %s]\r\n",
      (unsigned int)v21,
      v26->m_dwCheckKey);
    for ( j = 0; ; ++j )
    {
      v22 = v26->m_byItemListNum >= 24 ? 24 : v26->m_byItemListNum;
      if ( j >= v22 )
        break;
      v19 = 0i64;
      v20 = &v26->m_List[j].Key.byRewardIndex;
      if ( (_ITEMCOMBINE_DB_BASE *)((char *)v26 + 12 * j) != (_ITEMCOMBINE_DB_BASE *)-12 )
      {
        v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v20[1], *((_WORD *)v20 + 1));
        if ( v19 )
        {
          if ( pbyRewardTypeList[j] == 2 )
          {
            v12 = DisplayItemUpgInfo((unsigned __int8)v20[1], *((_DWORD *)v20 + 2));
            sprintf(sBuf, "\t%s_%u_@%s World \r\n", v19->m_strCode, *((_DWORD *)v20 + 1));
            strcat_0(sData, sBuf);
          }
          else if ( pbyRewardTypeList[j] == 1 )
          {
            v23 = j;
            v10 = DisplayItemUpgInfo((unsigned __int8)v20[1], *((_DWORD *)v20 + 2));
            v13 = lnUIDs[v23];
            v12 = v10;
            sprintf(sBuf, "\t%s_%u_@%s[%I64u] Inven \r\n", v19->m_strCode, *((_DWORD *)v20 + 1));
            strcat_0(sData, sBuf);
          }
        }
      }
    }
    CMgrAvatorItemHistory::WriteFile(v24, strFileName, sData);
  }
}
