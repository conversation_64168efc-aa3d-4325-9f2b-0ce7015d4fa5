/*
 * Function: ?ct_lua_command@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297D80
 */

char __fastcall ct_lua_command(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CLuaScriptMgr *v4; // rax@10
  CLuaScriptMgr *v5; // rax@14
  CLuaScriptMgr *v6; // rax@19
  CLuaScriptMgr *v7; // rax@20
  CLuaScriptMgr *v8; // rax@25
  CLuaScriptMgr *v9; // rax@34
  __int64 v10; // [sp+0h] [bp-918h]@1
  char szTran; // [sp+28h] [bp-8F0h]@10
  char strName; // [sp+68h] [bp-8B0h]@10
  CLuaCommand pAttachCommand; // [sp+B0h] [bp-868h]@7
  CLuaScript *pScript; // [sp+8C8h] [bp-50h]@10
  CLuaScript *v15; // [sp+8D0h] [bp-48h]@19
  CLuaScript *v16; // [sp+8D8h] [bp-40h]@25
  CLuaScript *v17; // [sp+8E0h] [bp-38h]@34
  bool v18; // [sp+8F0h] [bp-28h]@14
  char v19; // [sp+8F1h] [bp-27h]@15
  bool v20; // [sp+8F2h] [bp-26h]@20
  char v21; // [sp+8F3h] [bp-25h]@21
  char v22; // [sp+8F4h] [bp-24h]@29
  char v23; // [sp+8F5h] [bp-23h]@30
  char v24; // [sp+8F6h] [bp-22h]@38
  bool v25; // [sp+8F7h] [bp-21h]@39
  char v26; // [sp+8F8h] [bp-20h]@40
  __int64 v27; // [sp+900h] [bp-18h]@4
  unsigned __int64 v28; // [sp+908h] [bp-10h]@4
  CPlayer *v29; // [sp+920h] [bp+8h]@1

  v29 = pOne;
  v1 = &v10;
  for ( i = 580i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v27 = -2i64;
  v28 = (unsigned __int64)&v10 ^ _security_cookie;
  if ( !v29 || !v29->m_bOper )
    return 0;
  CLuaCommand::CLuaCommand(&pAttachCommand);
  CLuaCommand::Init(&pAttachCommand);
  if ( s_nWordCount < 2 )
    goto LABEL_40;
  if ( !strcmp_0("attach", s_pwszDstCheat[0]) )
  {
    if ( s_nWordCount >= 2 )
    {
      W2M(s_pwszDstCheat[1], &szTran, 0x20u);
      W2M(s_pwszDstCheat[2], &strName, 0x20u);
      v4 = CLuaScriptMgr::Instance();
      pScript = CLuaScriptMgr::NewScript(v4);
      if ( pScript )
      {
        ((void (__fastcall *)(CLuaScript *, char *))pScript->vfptr->SetName)(pScript, &szTran);
        if ( s_nWordCount == 3 )
          CLuaCommand::SetCmd(&pAttachCommand, 2, &strName);
        else
          CLuaCommand::Init(&pAttachCommand);
        v5 = CLuaScriptMgr::Instance();
        v18 = CLuaScriptMgr::AttachLuaScript(v5, pScript, &pAttachCommand);
        CLuaCommand::~CLuaCommand(&pAttachCommand);
        result = v18;
      }
      else
      {
        v19 = 0;
        CLuaCommand::~CLuaCommand(&pAttachCommand);
        result = v19;
      }
      return result;
    }
LABEL_40:
    v26 = 0;
    CLuaCommand::~CLuaCommand(&pAttachCommand);
    return v26;
  }
  if ( !strcmp_0("dettach", s_pwszDstCheat[0]) )
  {
    if ( s_nWordCount >= 2 )
    {
      W2M(s_pwszDstCheat[1], &szTran, 0x20u);
      v6 = CLuaScriptMgr::Instance();
      v15 = CLuaScriptMgr::SearchScript(v6, &szTran);
      if ( v15 )
      {
        v7 = CLuaScriptMgr::Instance();
        v20 = CLuaScriptMgr::DetackLuaScript(v7, v15);
        CLuaCommand::~CLuaCommand(&pAttachCommand);
        result = v20;
      }
      else
      {
        v21 = 0;
        CLuaCommand::~CLuaCommand(&pAttachCommand);
        result = v21;
      }
      return result;
    }
    goto LABEL_40;
  }
  if ( !strcmp_0("cmdfile", s_pwszDstCheat[0]) )
  {
    if ( s_nWordCount < 2 )
      goto LABEL_40;
    W2M(s_pwszDstCheat[1], &szTran, 0x20u);
    W2M(s_pwszDstCheat[2], &strName, 0x20u);
    v8 = CLuaScriptMgr::Instance();
    v16 = CLuaScriptMgr::SearchScript(v8, &szTran);
    if ( v16 )
    {
      if ( s_nWordCount == 3 )
        CLuaCommand::SetCmd(&pAttachCommand, 2, &strName);
      else
        CLuaCommand::Init(&pAttachCommand);
      v22 = ((int (__fastcall *)(CLuaScript *, CLuaCommand *))v16->vfptr->RunCommand)(v16, &pAttachCommand);
      CLuaCommand::~CLuaCommand(&pAttachCommand);
      result = v22;
    }
    else
    {
      v23 = 0;
      CLuaCommand::~CLuaCommand(&pAttachCommand);
      result = v23;
    }
  }
  else
  {
    if ( strcmp_0("cmdstr", s_pwszDstCheat[0]) || s_nWordCount < 2 )
      goto LABEL_40;
    W2M(s_pwszDstCheat[1], &szTran, 0x20u);
    W2M(s_pwszDstCheat[2], &strName, 0x20u);
    v9 = CLuaScriptMgr::Instance();
    v17 = CLuaScriptMgr::SearchScript(v9, &szTran);
    if ( v17 )
    {
      if ( s_nWordCount == 3 )
        CLuaCommand::SetCmd(&pAttachCommand, 1, &strName);
      else
        CLuaCommand::Init(&pAttachCommand);
      v24 = ((int (__fastcall *)(CLuaScript *, CLuaCommand *))v17->vfptr->RunCommand)(v17, &pAttachCommand);
      CLuaCommand::~CLuaCommand(&pAttachCommand);
      result = v24;
    }
    else
    {
      v25 = 0;
      CLuaCommand::~CLuaCommand(&pAttachCommand);
      result = v25;
    }
  }
  return result;
}
