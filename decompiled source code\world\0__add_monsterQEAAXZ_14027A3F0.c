/*
 * Function: ??0__add_monster@@QEAA@XZ
 * Address: 0x14027A3F0
 */

void __fastcall __add_monster::__add_monster(__add_monster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  __add_monster *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _react_obj::_react_obj(&v4->ReactObj);
  _react_area::_react_area(&v4->ReactArea);
}
