/*
 * Function: j_??$_Uninit_fill_n@PEAPEAU_PVP_RANK_REFRESH_USER@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@std@@@std@@YAXPEAPEAU_PVP_RANK_REFRESH_USER@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_REFRESH_USER@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400130E3
 */

void __fastcall std::_Uninit_fill_n<_PVP_RANK_REFRESH_USER * *,unsigned __int64,_PVP_RANK_REFRESH_USER *,std::allocator<_PVP_RANK_REFRESH_USER *>>(_PVP_RANK_REFRESH_USER **_First, unsigned __int64 _Count, _PVP_RANK_REFRESH_USER *const *_Val, std::allocator<_PVP_RANK_REFRESH_USER *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<_PVP_RANK_REFRESH_USER * *,unsigned __int64,_PVP_RANK_REFRESH_USER *,std::allocator<_PVP_RANK_REFRESH_USER *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
