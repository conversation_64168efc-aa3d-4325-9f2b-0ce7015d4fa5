/*
 * Function: ?ChannelMessageSeriesEnd@EqualityComparisonFilter@CryptoPP@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x140654D40
 */

bool __fastcall CryptoPP::EqualityComparisonFilter::ChannelMessageSeriesEnd(__int64 a1, __int64 a2, int a3, char a4)
{
  bool result; // al@2
  __int64 v5; // r8@8
  unsigned int v6; // [sp+40h] [bp-28h]@1
  signed __int64 v7; // [sp+48h] [bp-20h]@5
  CryptoPP::EqualityComparisonFilter *v8; // [sp+70h] [bp+8h]@1
  __int64 v9; // [sp+78h] [bp+10h]@1
  int v10; // [sp+80h] [bp+18h]@1
  char v11; // [sp+88h] [bp+20h]@1

  v11 = a4;
  v10 = a3;
  v9 = a2;
  v8 = (CryptoPP::EqualityComparisonFilter *)a1;
  v6 = CryptoPP::EqualityComparisonFilter::MapChannel(a1, a2);
  if ( v6 == 2 )
  {
    CryptoPP::Filter::OutputMessageSeriesEnd((__int64)v8, 4, v10, v11, v9);
    result = 0;
  }
  else if ( *((_BYTE *)v8 + 49) )
  {
    result = 0;
  }
  else
  {
    v7 = (signed __int64)v8 + 224 * (1 - v6) + 152;
    if ( (unsigned __int8)(*(int (__fastcall **)(signed __int64))(*(_QWORD *)v7 + 128i64))(v7)
      || (unsigned __int8)(*(int (__fastcall **)(signed __int64))(*(_QWORD *)v7 + 192i64))(v7) )
    {
      result = CryptoPP::EqualityComparisonFilter::HandleMismatchDetected(v8, v11);
    }
    else if ( (*(int (__fastcall **)(signed __int64))(*(_QWORD *)v7 + 240i64))(v7) )
    {
      result = CryptoPP::Filter::Output((__int64)v8, 2, (__int64)&unk_1408A976C, 1i64, 0) != 0;
    }
    else
    {
      LOBYTE(v5) = 1;
      (*(void (__fastcall **)(signed __int64, signed __int64, __int64))(*((_QWORD *)v8 + 28 * v6 + 19) + 96i64))(
        (signed __int64)v8 + 224 * v6 + 152,
        0xFFFFFFFFi64,
        v5);
      result = 0;
    }
  }
  return result;
}
