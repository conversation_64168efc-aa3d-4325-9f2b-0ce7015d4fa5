/*
 * Function: ?GetDefItemUpgSocketNum@@YAEHH@Z
 * Address: 0x14003BB50
 */

unsigned __int8 __fastcall GetDefItemUpgSocketNum(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-108h]@1
  int v6; // [sp+20h] [bp-E8h]@14
  CRecordData *v7; // [sp+30h] [bp-D8h]@4
  unsigned __int8 v8; // [sp+38h] [bp-D0h]@4
  _base_fld *v9; // [sp+40h] [bp-C8h]@8
  _base_fld *v10; // [sp+48h] [bp-C0h]@11
  char Dest; // [sp+60h] [bp-A8h]@14
  int v12; // [sp+F0h] [bp-18h]@4
  unsigned __int64 v13; // [sp+F8h] [bp-10h]@4
  int v14; // [sp+110h] [bp+8h]@1
  int n; // [sp+118h] [bp+10h]@1

  n = nItemIndex;
  v14 = nTableCode;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  v7 = &s_ptblItemData[v14];
  v8 = 0;
  v12 = v14;
  if ( v14 < 0 )
    goto LABEL_13;
  if ( v12 <= 5 )
    goto LABEL_8;
  if ( v12 != 6 )
  {
    if ( v12 != 7 )
      goto LABEL_13;
LABEL_8:
    v9 = CRecordData::GetRecord(v7, nItemIndex);
    if ( v9 )
      v8 = v9[6].m_strCode[52];
    goto LABEL_13;
  }
  v10 = CRecordData::GetRecord(v7, nItemIndex);
  if ( v10 )
    v8 = v10[11].m_strCode[16];
LABEL_13:
  if ( (signed int)v8 > 7 )
  {
    v6 = v8;
    sprintf(&Dest, "tbl:%d, idx:%d => slot: %d\n", (unsigned int)v14, (unsigned int)n);
    OutputDebugStringA(&Dest);
    v8 = 0;
  }
  return v8;
}
