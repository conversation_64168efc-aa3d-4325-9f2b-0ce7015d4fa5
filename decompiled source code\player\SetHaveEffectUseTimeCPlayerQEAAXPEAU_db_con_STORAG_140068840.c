/*
 * Function: ?SetHaveEffectUseTime@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@_N@Z
 * Address: 0x140068840
 */

void __fastcall CPlayer::SetHaveEffectUseTime(CPlayer *this, _STORAGE_LIST::_db_con *pItem, bool bAdd)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v5; // r8@12
  float v6; // xmm0_4@12
  signed __int64 v7; // rcx@29
  float v8; // xmm0_4@29
  signed __int64 v9; // r8@33
  float v10; // xmm0_4@33
  __int64 v11; // [sp+0h] [bp-48h]@1
  _base_fld *v12; // [sp+30h] [bp-18h]@5
  int j; // [sp+38h] [bp-10h]@7
  int k; // [sp+3Ch] [bp-Ch]@24
  CPlayer *v15; // [sp+50h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItema; // [sp+58h] [bp+10h]@1
  bool v17; // [sp+60h] [bp+18h]@1

  v17 = bAdd;
  pItema = pItem;
  v15 = this;
  v3 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pItem->m_byTableCode == 18 )
  {
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
    if ( v12 )
    {
      if ( v17 )
      {
        for ( j = 0; j < (signed int)v12[6].m_dwIndex; ++j )
        {
          if ( *(_DWORD *)&v12[6].m_strCode[12 * j] > -1 && *(_DWORD *)&v12[6].m_strCode[12 * j] < 83 )
          {
            v5 = pItema->m_dwDur;
            v6 = (float)(signed int)v5;
            if ( v5 < 0 )
              v6 = v6 + 1.8446744e19;
            v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * j]] = v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * j]]
                                                                                      + (float)(*(float *)&v12[6].m_strCode[12 * j + 4]
                                                                                              * v6);
            if ( v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * j]] > *(float *)&v12[6].m_strCode[12 * j + 8] )
            {
              if ( (v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * j]] = *(float *)&v12[6].m_strCode[12 * j + 8],
                    *(_DWORD *)&v12[6].m_strCode[12 * j] >= 71)
                && *(_DWORD *)&v12[6].m_strCode[12 * j] <= 75
                || *(_DWORD *)&v12[6].m_strCode[12 * j] >= 5 && *(_DWORD *)&v12[6].m_strCode[12 * j] <= 9
                || !*(_DWORD *)&v12[6].m_strCode[12 * j]
                || *(_DWORD *)&v12[6].m_strCode[12 * j] == 2 )
              {
                v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * j]] = v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * j]]
                                                                                          + 1.0;
              }
            }
          }
        }
        CMgrAvatorItemHistory::time_jade_effect_log(
          &CPlayer::s_MgrItemHistory,
          v12->m_strCode,
          pItema,
          1,
          v15->m_szItemHistoryFileName);
      }
      else
      {
        for ( k = 0; k < (signed int)v12[6].m_dwIndex; ++k )
        {
          if ( *(_DWORD *)&v12[6].m_strCode[12 * k] > -1 )
          {
            if ( *(_DWORD *)&v12[6].m_strCode[12 * k] < 83 )
            {
              v7 = pItema->m_dwDur;
              v8 = (float)(signed int)v7;
              if ( v7 < 0 )
                v8 = v8 + 1.8446744e19;
              if ( *(float *)&v12[6].m_strCode[12 * k + 8] <= (float)(*(float *)&v12[6].m_strCode[12 * k + 4] * v8) )
              {
                v9 = pItema->m_dwDur;
                v10 = (float)(signed int)v9;
                if ( v9 < 0 )
                  v10 = v10 + 1.8446744e19;
                v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]] = v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]]
                                                                                          - (float)(*(float *)&v12[6].m_strCode[12 * k + 4]
                                                                                                  * v10);
              }
              else
              {
                v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]] = v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]]
                                                                                          - *(float *)&v12[6].m_strCode[12 * k + 8];
              }
              if ( (*(_DWORD *)&v12[6].m_strCode[12 * k] < 71 || *(_DWORD *)&v12[6].m_strCode[12 * k] > 75)
                && (*(_DWORD *)&v12[6].m_strCode[12 * k] < 5 || *(_DWORD *)&v12[6].m_strCode[12 * k] > 9)
                && *(_DWORD *)&v12[6].m_strCode[12 * k]
                && *(_DWORD *)&v12[6].m_strCode[12 * k] != 2 )
              {
                if ( v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]] < 0.0 )
                  LODWORD(v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]]) = 0;
              }
              else if ( v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]] < 1.0 )
              {
                v15->m_EP.m_pDataParam->m_fEff_Have[*(_DWORD *)&v12[6].m_strCode[12 * k]] = FLOAT_1_0;
              }
            }
            CMgrAvatorItemHistory::time_jade_effect_log(
              &CPlayer::s_MgrItemHistory,
              v12->m_strCode,
              pItema,
              0,
              v15->m_szItemHistoryFileName);
          }
        }
      }
    }
  }
}
