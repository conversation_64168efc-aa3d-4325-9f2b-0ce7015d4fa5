/*
 * Function: ?GetStartMap@CMapOperation@@QEAAPEAVCMapData@@E@Z
 * Address: 0x140197AE0
 */

CMapData *__fastcall CMapOperation::GetStartMap(CMapOperation *this, char byRaceCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  unsigned int dwIndex; // [sp+24h] [bp-14h]@4
  _map_fld *v8; // [sp+28h] [bp-10h]@6
  CMapOperation *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1

  v10 = byRaceCode;
  v9 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CMapDataTable::GetRecordNum(&v9->m_tblMapData);
  for ( dwIndex = 0; (signed int)dwIndex < v6; ++dwIndex )
  {
    v8 = CMapDataTable::GetRecord(&v9->m_tblMapData, dwIndex);
    if ( v8->m_nRaceVillageCode == (unsigned __int8)v10 )
      return &v9->m_Map[dwIndex];
  }
  return 0i64;
}
