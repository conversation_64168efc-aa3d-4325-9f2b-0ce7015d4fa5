/*
 * Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Assign_n_::_1_::dtor$0
 * Address: 0x1403A8620
 */

void __fastcall std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Assign_n_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(*(std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > **)(a2 + 80));
}
