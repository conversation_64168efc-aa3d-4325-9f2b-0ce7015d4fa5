/*
 * Function: ?IsEffectableEquip@CPlayer@@QEAA_NPEAU_storage_con@_STORAGE_LIST@@@Z
 * Address: 0x140057870
 */

char __fastcall CPlayer::IsEffectableEquip(CPlayer *this, _STORAGE_LIST::_storage_con *pCon)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  float v5; // xmm0_4@4
  char result; // al@5
  int v7; // eax@7
  float v8; // xmm0_4@7
  int v9; // eax@15
  __int64 v10; // [sp+0h] [bp-78h]@1
  unsigned __int8 v11; // [sp+20h] [bp-58h]@4
  unsigned __int8 v12; // [sp+21h] [bp-57h]@4
  int pnLimNum; // [sp+34h] [bp-44h]@9
  _EQUIP_MASTERY_LIM *v14; // [sp+48h] [bp-30h]@9
  int j; // [sp+50h] [bp-28h]@11
  int nEquipMasteryCode; // [sp+54h] [bp-24h]@14
  float v17; // [sp+58h] [bp-20h]@4
  float v18; // [sp+5Ch] [bp-1Ch]@4
  float v19; // [sp+60h] [bp-18h]@7
  float v20; // [sp+64h] [bp-14h]@7
  int v21; // [sp+68h] [bp-10h]@15
  int v22; // [sp+6Ch] [bp-Ch]@16
  CPlayer *v23; // [sp+80h] [bp+8h]@1
  _STORAGE_LIST::_storage_con *v24; // [sp+88h] [bp+10h]@1

  v24 = pCon;
  v23 = this;
  v2 = &v10;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = GetItemEquipLevel(pCon->m_byTableCode, pCon->m_wItemIndex);
  v12 = GetItemEquipUpLevel(v24->m_byTableCode, v24->m_wItemIndex);
  v17 = (float)v11;
  v4 = CPlayerDB::GetLevel(&v23->m_Param);
  v5 = (float)v4;
  v18 = (float)v4;
  _effect_parameter::GetEff_Have(&v23->m_EP, 4);
  if ( v17 <= (float)(v18 + v5) )
  {
    if ( v12 == -1
      || (v19 = (float)v12,
          v7 = CPlayerDB::GetLevel(&v23->m_Param),
          v8 = (float)v7,
          v20 = (float)v7,
          _effect_parameter::GetEff_Have(&v23->m_EP, 4),
          (float)(v20 - v8) <= v19) )
    {
      pnLimNum = 0;
      v14 = GetItemEquipMastery(v24->m_byTableCode, v24->m_wItemIndex, &pnLimNum);
      if ( v14 )
      {
        for ( j = 0; j < pnLimNum; ++j )
        {
          nEquipMasteryCode = v14[j].nMasteryCode;
          if ( nEquipMasteryCode != -1 )
          {
            v21 = (unsigned __int8)_MASTERY_PARAM::GetEquipMastery(&v23->m_pmMst, nEquipMasteryCode);
            v9 = CPlayer::_check_equipmastery_lim(v23, nEquipMasteryCode);
            v22 = v21 >= v9 ? CPlayer::_check_equipmastery_lim(v23, nEquipMasteryCode) : (unsigned __int8)_MASTERY_PARAM::GetEquipMastery(&v23->m_pmMst, nEquipMasteryCode);
            if ( v22 < v14[j].nLimMastery )
              return 0;
          }
        }
        result = 1;
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
