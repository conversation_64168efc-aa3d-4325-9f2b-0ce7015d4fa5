/*
 * Function: ?GetMaxRegistCnt@CUnmannedTraderController@@QEAAEGK@Z
 * Address: 0x14007A1B0
 */

char __fastcall CUnmannedTraderController::GetMaxRegistCnt(CUnmannedTraderController *this, unsigned __int16 wInx, unsigned int dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  unsigned __int16 v8; // [sp+38h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+40h] [bp+18h]@1

  dwSeriala = dwSerial;
  v8 = wInx;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CUnmannedTraderUserInfoTable::Instance();
  return CUnmannedTraderUserInfoTable::GetMaxRegistCnt(v5, v8, dwSeriala);
}
