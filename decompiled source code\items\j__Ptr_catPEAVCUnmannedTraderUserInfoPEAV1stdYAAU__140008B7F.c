/*
 * Function: j_??$_Ptr_cat@PEAVCUnmannedTraderUserInfo@@PEAV1@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAVCUnmannedTraderUserInfo@@0@Z
 * Address: 0x140008B7F
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *>(CUnmannedTraderUserInfo **__formal, CUnmannedTraderUserInfo **a2)
{
  return std::_Ptr_cat<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *>(__formal, a2);
}
