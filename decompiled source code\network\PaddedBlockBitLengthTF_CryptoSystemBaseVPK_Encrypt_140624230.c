/*
 * Function: ?PaddedBlockBitLength@?$TF_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$TF_Base@VRandomizedTrapdoorFunction@CryptoPP@@VPK_EncryptionMessageEncodingMethod@2@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x140624230
 */

__int64 __fastcall CryptoPP::TF_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::TF_Base<CryptoPP::RandomizedTrapdoorFunction,CryptoPP::PK_EncryptionMessageEncodingMethod>>::PaddedBlockBitLength(__int64 a1)
{
  __int64 v1; // rax@1
  CryptoPP::Integer *v2; // rax@1
  __int64 v3; // ST20_8@1
  CryptoPP::Integer v5; // [sp+28h] [bp-60h]@1
  __int64 v6; // [sp+50h] [bp-38h]@1
  int (__fastcall **v7)(_QWORD); // [sp+58h] [bp-30h]@1
  __int64 v8; // [sp+60h] [bp-28h]@1
  CryptoPP::Integer *v9; // [sp+68h] [bp-20h]@1
  CryptoPP::Integer *v10; // [sp+70h] [bp-18h]@1

  v6 = -2i64;
  v7 = *(int (__fastcall ***)(_QWORD))(a1 + 16);
  LODWORD(v1) = (*v7)(a1 + 16);
  v8 = v1;
  LODWORD(v2) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *))(*(_QWORD *)v1 + 8i64))(v1, &v5);
  v9 = v2;
  v10 = v2;
  v3 = (unsigned int)CryptoPP::Integer::BitCount(v2) - 1;
  CryptoPP::Integer::~Integer(&v5);
  return v3;
}
