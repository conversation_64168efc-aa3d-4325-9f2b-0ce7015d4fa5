/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::_Ucopy_std::_Vector_const_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64______::_1_::dtor$2
 * Address: 0x1403B1ED0
 */

void __fastcall std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::_Ucopy_std::_Vector_const_iterator_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64______::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(*(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > **)(a2 + 64));
}
