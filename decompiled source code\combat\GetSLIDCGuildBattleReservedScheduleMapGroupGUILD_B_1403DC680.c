/*
 * Function: ?GetSLID@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NIAEAI@Z
 * Address: 0x1403DC680
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetSLID(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiMapID, unsigned int *uiSLID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_uiMapCnt && v7->m_uiMapCnt > uiMapID && v7->m_ppkReservedSchedule[uiMapID] )
  {
    *uiSLID = GUILD_BATTLE::CGuildBattleReservedSchedule::GetID(v7->m_ppkReservedSchedule[uiMapID]);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
