/*
 * Function: ?CopyUseTimeField@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA_NPEA_N@Z
 * Address: 0x1403DAD20
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::CopyUseTimeField(GUILD_BATTLE::CGuildBattleReservedSchedule *this, bool *pbField)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedSchedule *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pbField )
  {
    memcpy_0(pb<PERSON><PERSON>, v6->m_bU<PERSON><PERSON><PERSON>, 0x17ui64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
