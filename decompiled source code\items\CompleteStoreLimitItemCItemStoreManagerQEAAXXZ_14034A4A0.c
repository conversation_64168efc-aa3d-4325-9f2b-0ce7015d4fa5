/*
 * Function: ?CompleteStoreLimitItem@CItemStoreManager@@QEAAXXZ
 * Address: 0x14034A4A0
 */

void __fastcall CItemStoreManager::CompleteStoreLimitItem(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  unsigned int j; // [sp+20h] [bp-28h]@4
  _qry_case_all_store_limit_item::__list *v5; // [sp+28h] [bp-20h]@7
  CItemStore *v6; // [sp+30h] [bp-18h]@10
  CMapItemStoreList *v7; // [sp+38h] [bp-10h]@10
  CItemStoreManager *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v8->m_Sheet.dwCount; ++j )
  {
    v5 = &v8->m_Sheet.pStoreList[j];
    if ( v5->byRet )
    {
      CItemStoreManager::Log(
        v8,
        "CItemStoreManager::CompleteStoreLimitItem\r\n\t\tStore Limit Item DBUpdate Proc(ErrorCode:%d) Fail!\r\n",
        v5->byRet);
    }
    else if ( v5->bNewSerial )
    {
      v6 = 0i64;
      v7 = 0i64;
      if ( v5->byType )
      {
        if ( v5->byType == 1 )
          v7 = CItemStoreManager::GetInstanceStoreListBySerial(v8, v5->nTypeSerial);
      }
      else
      {
        v7 = CItemStoreManager::GetMapItemStoreListBySerial(v8, v5->nTypeSerial);
      }
      if ( v7 )
      {
        v6 = CMapItemStoreList::GetItemStoreFromRecIndex(v7, v5->dwStoreIndex);
        if ( v6 )
          v6->m_dwDBSerial = v5->dwDBSerial;
        else
          CItemStoreManager::Log(
            v8,
            "CItemStoreManager::CompleteStoreLimitItem\r\n"
            "\t\t Store Limit Item NewSerial Set Fail! (StoreType:%d)(Serial:%d)\r\n",
            v5->byType,
            v5->nTypeSerial);
      }
      else
      {
        CItemStoreManager::Log(
          v8,
          "CItemStoreManager::CompleteStoreLimitItem\r\n"
          "\t\t Store Limit Item NewSerial Set Fail! (StoreType:%d)(Serial:%d)\r\n",
          v5->byType,
          v5->nTypeSerial);
      }
    }
  }
  _qry_case_all_store_limit_item::DataInit(&v8->m_Sheet);
}
