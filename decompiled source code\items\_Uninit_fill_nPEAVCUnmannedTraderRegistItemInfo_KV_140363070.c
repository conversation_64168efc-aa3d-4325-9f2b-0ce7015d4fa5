/*
 * Function: ??$_Uninit_fill_n@PEAVCUnmannedTraderRegistItemInfo@@_KV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140363070
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderRegistItemInfo *,unsigned __int64,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val, std::allocator<CUnmannedTraderRegistItemInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  CUnmannedTraderRegistItemInfo *v9; // [sp+20h] [bp-18h]@4
  __int64 v10; // [sp+28h] [bp-10h]@4
  CUnmannedTraderRegistItemInfo *_Ptr; // [sp+40h] [bp+8h]@1
  unsigned __int64 v12; // [sp+48h] [bp+10h]@1
  CUnmannedTraderRegistItemInfo *_Vala; // [sp+50h] [bp+18h]@1
  std::allocator<CUnmannedTraderRegistItemInfo> *v14; // [sp+58h] [bp+20h]@1

  v14 = _Al;
  _Vala = _Val;
  v12 = _Count;
  _Ptr = _First;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = -2i64;
  v9 = _Ptr;
  while ( v12 )
  {
    std::allocator<CUnmannedTraderRegistItemInfo>::construct(v14, _Ptr, _Vala);
    --v12;
    ++_Ptr;
  }
}
