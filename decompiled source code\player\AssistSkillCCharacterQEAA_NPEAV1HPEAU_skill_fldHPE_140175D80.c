/*
 * Function: ?AssistSkill@<PERSON>haracter@@QEAA_NPEAV1@HPEAU_skill_fld@@HPEAEPEA_N@Z
 * Address: 0x140175D80
 */

char __fastcall CCharacter::AssistSkill(<PERSON>haracter *this, CCharacter *pDstChar, int nEffectCode, _skill_fld *pSkillFld, int nSkillLv, char *pbyErrorCode, bool *pbUpMty)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // r8@4
  CMonsterAggroMgr *v10; // rax@11
  CCharacter *v11; // rax@19
  __int128 v12; // xmm2@27
  CCharacter *v13; // rax@40
  CMonsterAggroMgr *v14; // rax@44
  __int64 v16; // [sp+0h] [bp-258h]@1
  unsigned int bBenefit[2]; // [sp+20h] [bp-238h]@4
  CCharacter *pOriDst; // [sp+28h] [bp-230h]@4
  char *psActableDst; // [sp+30h] [bp-228h]@4
  CCharacter **ppDsts; // [sp+38h] [bp-220h]@4
  int j; // [sp+40h] [bp-218h]@5
  CCharacter *v22; // [sp+60h] [bp-1F8h]@4
  char Dst[64]; // [sp+168h] [bp-F0h]@4
  char v24[48]; // [sp+1A8h] [bp-B0h]@4
  CMonster *v25; // [sp+1D8h] [bp-80h]@15
  int (__fastcall *v26)(CCharacter *, _QWORD, __int64, char *); // [sp+1E0h] [bp-78h]@23
  char v27; // [sp+1F4h] [bp-64h]@23
  CMonster *v28; // [sp+208h] [bp-50h]@33
  int v29; // [sp+210h] [bp-48h]@38
  char v30; // [sp+214h] [bp-44h]@46
  bool v31; // [sp+220h] [bp-38h]@4
  __int64 *v32; // [sp+228h] [bp-30h]@7
  __int64 v33; // [sp+230h] [bp-28h]@7
  int v34; // [sp+238h] [bp-20h]@36
  unsigned __int64 v35; // [sp+240h] [bp-18h]@4
  CCharacter *pCharacter; // [sp+260h] [bp+8h]@1
  int nEffectCodea; // [sp+270h] [bp+18h]@1
  _skill_fld *v38; // [sp+278h] [bp+20h]@1

  v38 = pSkillFld;
  nEffectCodea = nEffectCode;
  pCharacter = this;
  v7 = &v16;
  for ( i = 148i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v35 = (unsigned __int64)&v16 ^ _security_cookie;
  v31 = pSkillFld->m_nContEffectType == 1;
  ppDsts = &v22;
  psActableDst = pSkillFld->m_strActableDst;
  pOriDst = pDstChar;
  LOBYTE(bBenefit[0]) = v31;
  g_tmpEffectedNum = CCharacter::FindEffectDst(
                       pCharacter,
                       nEffectCode,
                       pSkillFld->m_nContAreaType,
                       pSkillFld->m_nLv,
                       v31,
                       pDstChar,
                       pSkillFld->m_strActableDst,
                       &v22);
  memset_0(Dst, 0, 0x1Eui64);
  memset_0(v24, 0, 0x1Eui64);
  if ( v38->m_nContEffectType != -1 )
  {
    for ( j = 0; j < g_tmpEffectedNum; ++j )
    {
      v32 = (__int64 *)*(&v22 + j);
      v33 = *v32;
      ppDsts = (CCharacter **)pCharacter;
      psActableDst = &Dst[j];
      LOBYTE(pOriDst) = nSkillLv;
      LOWORD(bBenefit[0]) = v38->m_nContEffectSec[nSkillLv - 1];
      v24[j] = (*(int (__fastcall **)(__int64 *, _QWORD, _QWORD, _QWORD))(v33 + 664))(
                 v32,
                 LOBYTE(v38->m_nContEffectType),
                 (unsigned __int8)nEffectCodea,
                 v38->m_dwIndex);
      if ( !pCharacter->m_ObjID.m_byID && !v24[j] )
      {
        if ( v38->m_nContEffectType )
        {
          if ( v38->m_nContEffectType == 1 && !(*(&v22 + j))->m_ObjID.m_byID )
          {
            v25 = (CMonster *)CPlayer::GetTargetObj((CPlayer *)*(&v22 + j));
            if ( v25 )
            {
              if ( !v25->m_ObjID.m_byKind && v25->m_ObjID.m_byID == 1 )
              {
                if ( CMonsterAggroMgr::SearchAggroNode(&v25->m_AggroMgr, *(&v22 + j))
                  || (v11 = CMonster::GetAttackTarget(v25), v11 == *(&v22 + j)) )
                {
                  LODWORD(psActableDst) = 0;
                  LODWORD(pOriDst) = 1;
                  bBenefit[0] = v38->m_dwIndex;
                  CMonsterAggroMgr::SetAggro(&v25->m_AggroMgr, pCharacter, 0, nEffectCodea, bBenefit[0], 1, 0);
                }
              }
            }
          }
        }
        else if ( (*(&v22 + j))->m_ObjID.m_byID == 1 )
        {
          v10 = (CMonsterAggroMgr *)(*(&v22 + j))[1].m_SFContAura[1][4].m_wszPlayerName;
          LODWORD(psActableDst) = 0;
          LODWORD(pOriDst) = 0;
          bBenefit[0] = v38->m_dwIndex;
          CMonsterAggroMgr::SetAggro(v10, pCharacter, 0, nEffectCodea, bBenefit[0], 0, 0);
        }
      }
    }
  }
  if ( v38->m_nTempEffectType != -1 )
  {
    v26 = (int (__fastcall *)(CCharacter *, _QWORD, __int64, char *))g_TempEffectFunc[v38->m_nTempEffectType];
    v27 = 0;
    for ( j = 0; j < g_tmpEffectedNum; ++j )
    {
      if ( !v24[j] )
      {
        v27 = 0;
        v12 = LODWORD(v38->m_fTempValue[nSkillLv - 1]);
        if ( (unsigned __int8)v26(pCharacter, *(&v22 + j), v9, &v27) )
          Dst[j] = 1;
        if ( v27 )
        {
          v24[j] = v27;
        }
        else if ( !pCharacter->m_ObjID.m_byID )
        {
          if ( (*(&v22 + j))->m_ObjID.m_byID )
          {
            if ( (*(&v22 + j))->m_ObjID.m_byID == 1 )
            {
              v14 = (CMonsterAggroMgr *)(*(&v22 + j))[1].m_SFContAura[1][4].m_wszPlayerName;
              LODWORD(psActableDst) = 1;
              LODWORD(pOriDst) = 0;
              bBenefit[0] = v38->m_dwIndex;
              CMonsterAggroMgr::SetAggro(v14, pCharacter, 0, nEffectCodea, bBenefit[0], 0, 1);
            }
          }
          else
          {
            v28 = (CMonster *)CPlayer::GetTargetObj((CPlayer *)*(&v22 + j));
            v34 = v28 && !v28->m_ObjID.m_byKind && v28->m_ObjID.m_byID == 1;
            v29 = v34;
            if ( v34 )
            {
              if ( CMonsterAggroMgr::SearchAggroNode(&v28->m_AggroMgr, *(&v22 + j))
                || (v13 = CMonster::GetAttackTarget(v28), v13 == *(&v22 + j)) )
              {
                LODWORD(psActableDst) = 1;
                LODWORD(pOriDst) = 1;
                bBenefit[0] = v38->m_dwIndex;
                CMonsterAggroMgr::SetAggro(&v28->m_AggroMgr, pCharacter, 0, nEffectCodea, bBenefit[0], 1, 1);
              }
            }
          }
        }
      }
    }
  }
  *pbyErrorCode = 0;
  *pbUpMty = 0;
  v30 = 0;
  for ( j = 0; j < g_tmpEffectedNum; ++j )
  {
    *((_QWORD *)&g_tmpEffectedList + 2 * j) = *(&v22 + j);
    *((_BYTE *)&g_tmpEffectedList + 16 * j + 8) = v24[j];
    *((_WORD *)&g_tmpEffectedList + 8 * j + 5) = (*(&v22 + j))->m_wEffectTempValue;
    if ( !v24[j] )
      v30 = 1;
    if ( Dst[j] )
      *pbUpMty = 1;
  }
  if ( v30 )
  {
    *pbyErrorCode = 0;
  }
  else
  {
    *pbyErrorCode = v24[0];
    g_tmpEffectedNum = 0;
  }
  return v30;
}
