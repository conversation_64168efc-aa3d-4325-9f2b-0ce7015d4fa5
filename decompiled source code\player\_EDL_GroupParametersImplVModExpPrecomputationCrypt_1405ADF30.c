/*
 * Function: ??_E?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@$4PPPPPPPM@OI@EAAPEAXI@Z
 * Address: 0x1405ADF30
 */

void *__fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vector deleting destructor'(__int64 a1, int a2)
{
  return CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`scalar deleting destructor'(
           (void *)(a1 - *(_DWORD *)(a1 - 4) - 232),
           a2);
}
