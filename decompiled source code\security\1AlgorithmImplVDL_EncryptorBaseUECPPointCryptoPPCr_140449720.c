/*
 * Function: ??1?$AlgorithmImpl@V?$DL_EncryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$ECIES@VECP@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@$0A@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140449720
 */

void __fastcall CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::~AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>(CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>::~DL_EncryptorBase<CryptoPP::ECPPoint>((CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint> *)&v4->vfptr);
}
