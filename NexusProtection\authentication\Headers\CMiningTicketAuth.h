#pragma once

/**
 * @file CMiningTicketAuth.h
 * @brief Mining Ticket Authentication System
 * 
 * Provides secure authentication for mining tickets and holy stone system operations.
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * Original files:
 * - AuthMiningTicketCHolyStoneSystemQEAA_NIZ_14027DBD0.c
 * - Set_AuthKeyTicketMiningTicketQEAAXGEEEEZ_1400A6BA0.c
 * - Init_AuthKeyTicketMiningTicketQEAAXXZ_140073BC0.c
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <unordered_map>
#include <vector>

namespace NexusProtection::Authentication {

    /**
     * @brief Mining ticket authentication result
     */
    enum class MiningTicketResult : uint8_t {
        Success = 0,
        InvalidTicket = 1,
        ExpiredTicket = 2,
        InvalidTimeData = 3,
        SystemError = 4,
        NotInitialized = 5
    };

    /**
     * @brief Holy stone system scene codes
     */
    enum class HolyStoneScene : uint8_t {
        None = 0,
        HolyStone = 1,
        Mining = 2,
        MiningTicket1 = 3,
        MiningTicket2 = 4,
        MiningTicket3 = 5,
        MiningTicket4 = 6
    };

    /**
     * @brief Time data structure for mining tickets
     */
    struct MiningTimeData {
        uint16_t year{0};
        uint8_t month{0};
        uint8_t day{0};
        uint8_t hour{0};
        uint8_t numOfTime{0};

        MiningTimeData() = default;
        MiningTimeData(uint16_t y, uint8_t m, uint8_t d, uint8_t h, uint8_t n)
            : year(y), month(m), day(d), hour(h), numOfTime(n) {}

        bool IsValid() const;
        std::string ToString() const;
    };

    /**
     * @brief Authentication key ticket for mining operations
     */
    class AuthKeyTicket {
    public:
        AuthKeyTicket();
        ~AuthKeyTicket() = default;

        // Core operations
        void Init();
        void Set(uint16_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t numOfTime);
        void Set(const MiningTimeData& timeData);
        uint32_t GetKey() const { return m_uiData; }

        // Validation
        bool IsValid() const;
        bool IsExpired() const;
        MiningTimeData GetTimeData() const;

        // Operators
        bool operator==(const AuthKeyTicket& other) const { return m_uiData == other.m_uiData; }
        bool operator!=(const AuthKeyTicket& other) const { return !(*this == other); }
        bool operator==(uint32_t key) const { return m_uiData == key; }

    private:
        uint32_t m_uiData{0};

        // Bit manipulation helpers
        void SetYear(uint16_t year);
        void SetMonth(uint8_t month);
        void SetDay(uint8_t day);
        void SetHour(uint8_t hour);
        void SetNumOfTime(uint8_t numOfTime);

        uint16_t GetYear() const;
        uint8_t GetMonth() const;
        uint8_t GetDay() const;
        uint8_t GetHour() const;
        uint8_t GetNumOfTime() const;
    };

    /**
     * @brief Mining ticket management system
     */
    class MiningTicket {
    public:
        MiningTicket();
        ~MiningTicket() = default;

        // Core operations
        void Init();
        bool AuthLastCriTicket(uint16_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t numOfTime);
        bool AuthLastMentalTicket(uint16_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t numOfTime);

        // Ticket management
        const AuthKeyTicket& GetAuthKeyTicket() const { return m_authKeyTicket; }
        const AuthKeyTicket& GetLastCriTicket() const { return m_lastCriTicket; }
        void SetAuthKeyTicket(const AuthKeyTicket& ticket) { m_authKeyTicket = ticket; }
        void SetLastCriTicket(const AuthKeyTicket& ticket) { m_lastCriTicket = ticket; }

        // Validation
        bool IsValid() const;
        std::string ToString() const;

    private:
        AuthKeyTicket m_authKeyTicket;
        AuthKeyTicket m_lastCriTicket;
    };

    /**
     * @brief Holy Stone System - Mining ticket authentication and management
     */
    class CHolyStoneSystem {
    public:
        CHolyStoneSystem();
        ~CHolyStoneSystem();

        // Core lifecycle
        bool Initialize();
        void Shutdown();
        bool LoadConfiguration();

        // Mining ticket authentication
        bool AuthMiningTicket(uint32_t dwKey);
        MiningTicketResult ValidateMiningTicket(uint32_t dwKey, const MiningTimeData& timeData);

        // Time management
        MiningTimeData GetCurrentTimeData() const;
        void SetTimeData(const MiningTimeData& timeData);

        // Scene management
        HolyStoneScene GetSceneCode() const { return m_currentScene; }
        void SetSceneCode(HolyStoneScene scene) { m_currentScene = scene; }
        bool IsMiningTicketScene() const;

        // Configuration
        bool IsInitialized() const { return m_isInitialized; }
        bool IsOperational() const { return m_isOperational; }

        // Statistics
        struct Statistics {
            uint32_t totalAuthentications{0};
            uint32_t successfulAuthentications{0};
            uint32_t failedAuthentications{0};
            uint32_t expiredTickets{0};
            std::chrono::steady_clock::time_point startTime;
        };

        const Statistics& GetStatistics() const { return m_statistics; }
        void ResetStatistics();

        // Legacy compatibility
        uint8_t GetNumOfTime() const { return m_timeData.numOfTime; }
        uint8_t GetStartHour() const { return m_timeData.hour; }
        uint8_t GetStartDay() const { return m_timeData.day; }
        uint8_t GetStartMonth() const { return m_timeData.month; }
        uint16_t GetStartYear() const { return m_timeData.year; }

    private:
        // Configuration
        bool m_isInitialized{false};
        bool m_isOperational{false};
        MiningTimeData m_timeData;
        HolyStoneScene m_currentScene{HolyStoneScene::None};

        // Thread safety
        mutable std::mutex m_mutex;

        // Statistics
        Statistics m_statistics;
        mutable std::mutex m_statisticsMutex;

        // Internal methods
        bool ValidateTimeData(const MiningTimeData& timeData) const;
        void UpdateStatistics(bool success, bool expired = false);
        void LogAuthenticationEvent(const std::string& event, uint32_t key, bool success);
    };

    /**
     * @brief Mining Ticket Authentication Manager
     */
    class CMiningTicketAuth {
    public:
        CMiningTicketAuth();
        ~CMiningTicketAuth();

        // Core lifecycle
        bool Initialize();
        void Shutdown();

        // Authentication operations
        MiningTicketResult AuthenticateTicket(uint32_t ticketKey, const MiningTimeData& timeData);
        bool ValidateTicketFormat(uint32_t ticketKey) const;

        // Ticket management
        AuthKeyTicket CreateTicket(const MiningTimeData& timeData);
        bool IsTicketValid(const AuthKeyTicket& ticket) const;

        // System access
        CHolyStoneSystem& GetHolyStoneSystem() { return m_holyStoneSystem; }
        const CHolyStoneSystem& GetHolyStoneSystem() const { return m_holyStoneSystem; }

        // Configuration
        bool IsInitialized() const { return m_isInitialized; }

    private:
        bool m_isInitialized{false};
        CHolyStoneSystem m_holyStoneSystem;
        mutable std::mutex m_mutex;

        // Internal validation
        bool ValidateTicketIntegrity(uint32_t ticketKey) const;
    };

    // Global instance access
    CMiningTicketAuth& GetMiningTicketAuth();

    // Utility functions
    std::string MiningTicketResultToString(MiningTicketResult result);
    std::string HolyStoneSceneToString(HolyStoneScene scene);

    // Legacy C interface compatibility
    extern "C" {
        struct CHolyStoneSystem_Legacy {
            bool m_bInitialized;
            uint8_t m_bySceneCode;
            uint16_t m_wYear;
            uint8_t m_byMonth;
            uint8_t m_byDay;
            uint8_t m_byHour;
            uint8_t m_byNumOfTime;
        };

        struct MiningTicket_Legacy {
            uint32_t m_uiAuthKeyData;
            uint32_t m_uiLastCriTicketData;
        };

        // Legacy function declarations
        CHolyStoneSystem_Legacy* CHolyStoneSystem_Create();
        void CHolyStoneSystem_Destroy(CHolyStoneSystem_Legacy* system);
        bool CHolyStoneSystem_AuthMiningTicket(CHolyStoneSystem_Legacy* system, uint32_t dwKey);
        bool CHolyStoneSystem_Initialize(CHolyStoneSystem_Legacy* system);

        MiningTicket_Legacy* MiningTicket_Create();
        void MiningTicket_Destroy(MiningTicket_Legacy* ticket);
        void MiningTicket_Init(MiningTicket_Legacy* ticket);
        void MiningTicket_AuthKeyTicket_Set(MiningTicket_Legacy* ticket, 
                                          uint16_t year, uint8_t month, uint8_t day, 
                                          uint8_t hour, uint8_t numOfTime);
        void MiningTicket_AuthKeyTicket_Init(MiningTicket_Legacy* ticket);
    }

} // namespace NexusProtection::Authentication
