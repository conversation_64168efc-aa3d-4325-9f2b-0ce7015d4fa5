/*
 * Function: j_??$_Copy_opt@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@Urandom_access_iterator_tag@std@@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C6AD
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Copy_opt<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::random_access_iterator_tag>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
