/*
 * Function: j_?_Kfn@?$_Hmap_traits@HPEBU_TimeItem_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@$0A@@stdext@@SAAEBHAEBU?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@Z
 * Address: 0x14000DB52
 */

const int *__fastcall stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>::_Kfn(stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> *this, std::pair<int const ,_TimeItem_fld const *> *_Val)
{
  return stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>::_Kfn(
           this,
           _Val);
}
