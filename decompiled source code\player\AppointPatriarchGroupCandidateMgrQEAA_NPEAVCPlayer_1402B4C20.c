/*
 * Function: ?AppointPatriarchGroup@CandidateMgr@@QEAA_NPEAVCPlayer@@W4ClassType@_candidate_info@@@Z
 * Address: 0x1402B4C20
 */

char __usercall CandidateMgr::AppointPatriarchGroup@<al>(CandidateMgr *this@<rcx>, CPlayer *pOne@<rdx>, _candidate_info::ClassType eClassType@<r8d>, long double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@4
  char result; // al@5
  char v8; // al@6
  char v9; // al@6
  char *v10; // rax@6
  PatriarchElectProcessor *v11; // rax@9
  unsigned int v12; // eax@9
  int v13; // eax@9
  __int64 v14; // [sp+0h] [bp-78h]@1
  unsigned int dwS[2]; // [sp+20h] [bp-58h]@9
  _candidate_info *v16; // [sp+30h] [bp-48h]@4
  _qry_case_insert_candidate v17; // [sp+48h] [bp-30h]@9
  CandidateMgr *v18; // [sp+80h] [bp+8h]@1
  CPlayer *v19; // [sp+88h] [bp+10h]@1
  _candidate_info::ClassType v20; // [sp+90h] [bp+18h]@1

  v20 = eClassType;
  v19 = pOne;
  v18 = this;
  v4 = &v14;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  v16 = CandidateMgr::GetEmptyPatriarchGroup(v18, v6);
  if ( v16 )
  {
    v16->bLoad = 1;
    v8 = CPlayerDB::GetRaceCode(&v19->m_Param);
    v16->byRace = v8;
    v9 = CPlayerDB::GetLevel(&v19->m_Param);
    v16->byLevel = v9;
    v16->dwRank = CPlayerDB::GetPvpRank(&v19->m_Param);
    CPlayerDB::GetPvPPoint(&v19->m_Param);
    v16->dPvpPoint = a4;
    v16->byGrade = v19->m_Param.m_byPvPGrade;
    v16->dwAvatorSerial = CPlayerDB::GetCharSerial(&v19->m_Param);
    v16->eClassType = v20;
    v16->eStatus = 3;
    v10 = CPlayerDB::GetCharNameW(&v19->m_Param);
    strcpy_s(v16->wszName, 0x11ui64, v10);
    if ( v19->m_Param.m_pGuild )
    {
      v16->dwGuildSerial = v19->m_Param.m_pGuild->m_dwSerial;
      strcpy_s(v16->wszGuildName, 0x11ui64, v19->m_Param.m_pGuild->m_wszName);
    }
    else
    {
      v16->dwGuildSerial = -1;
      memset_0(v16->wszGuildName, 0, 0x11ui64);
    }
    v11 = PatriarchElectProcessor::Instance();
    v12 = PatriarchElectProcessor::GetElectSerial(v11);
    dwS[0] = v16->dwAvatorSerial;
    _qry_case_insert_candidate::_qry_case_insert_candidate(&v17, v16->byRace, v19->m_id.wIndex, v12, dwS[0]);
    v13 = _qry_case_insert_candidate::size(&v17);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 125, &v17.byRace, v13);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
