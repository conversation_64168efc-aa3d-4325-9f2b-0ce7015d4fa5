/*
 * Function: ?SendMsg_GotoBasePortalResult@CPlayer@@QEAAXE@Z
 * Address: 0x1400D6A90
 */

void __fastcall CPlayer::SendMsg_GotoBasePortalResult(CPlayer *this, char byErrCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v6; // [sp+39h] [bp-4Fh]@4
  __int16 pShort; // [sp+3Ah] [bp-4Eh]@4
  char pbyType; // [sp+54h] [bp-34h]@4
  char v9; // [sp+55h] [bp-33h]@4
  unsigned __int64 v10; // [sp+70h] [bp-18h]@4
  CPlayer *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  szMsg = byErrCode;
  v6 = v11->m_pCurMap->m_pMapSet->m_dwIndex;
  FloatToShort(v11->m_fCurPos, &pShort, 3);
  pbyType = 4;
  v9 = 27;
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &szMsg, 8u);
}
