/*
 * Function: ?allocate@?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@QEAAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@_K@Z
 * Address: 0x140593460
 */

int __fastcall std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>::allocate(__int64 a1, __int64 a2)
{
  return std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(a2, 0i64);
}
