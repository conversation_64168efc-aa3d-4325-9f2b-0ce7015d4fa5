/*
 * Function: ?LogIn@CMoveMapLimitInfoList@@QEAAXPEAVCPlayer@@PEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A5B20
 */

void __fastcall CMoveMapLimitInfoList::LogIn(CMoveMapLimitInfoList *this, CPlayer *pkPlayer, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-F8h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v6; // [sp+20h] [bp-D8h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > _Right; // [sp+38h] [bp-C0h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v8; // [sp+68h] [bp-90h]@4
  CMoveMapLimitInfo *v9; // [sp+88h] [bp-70h]@6
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > result; // [sp+90h] [bp-68h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v11; // [sp+A8h] [bp-50h]@4
  __int64 v12; // [sp+C0h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v13; // [sp+C8h] [bp-30h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *__that; // [sp+D0h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v15; // [sp+D8h] [bp-20h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v16; // [sp+E0h] [bp-18h]@4
  CMoveMapLimitInfoList *v17; // [sp+100h] [bp+8h]@1
  CPlayer *v18; // [sp+108h] [bp+10h]@1
  CMoveMapLimitRightInfo *v19; // [sp+110h] [bp+18h]@1

  v19 = pkRight;
  v18 = pkPlayer;
  v17 = this;
  v3 = &v5;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = -2i64;
  v6 = (std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v17;
  v13 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::end(&v17->m_vecLimitInfo, &result);
  __that = (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v13;
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    &_Right,
    (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v13->_Mycont);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&result);
  v15 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::begin(v6, &v11);
  v16 = (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v15;
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    &v8,
    (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v15->_Mycont);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v11);
  while ( std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator!=(&v8, &_Right) )
  {
    v9 = *std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator*(&v8);
    ((void (__fastcall *)(CMoveMapLimitInfo *, CPlayer *, CMoveMapLimitRightInfo *))v9->vfptr->LogIn)(v9, v18, v19);
    std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator++(&v8);
  }
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v8);
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&_Right);
}
