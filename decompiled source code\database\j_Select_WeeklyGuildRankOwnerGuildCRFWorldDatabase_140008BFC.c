/*
 * Function: j_?Select_WeeklyGuildRankOwnerGuild@CRFWorldDatabase@@QEAAEPEADEEPEAU_weeklyguildrank_owner_info@@@Z
 * Address: 0x140008BFC
 */

char __fastcall CRFWorldDatabase::Select_WeeklyGuildRankOwnerGuild(CRFWorldDatabase *this, char *szDate, char byRace, char byLimitCnt, _weeklyguildrank_owner_info *pkInfo)
{
  return CRFWorldDatabase::Select_WeeklyGuildRankOwnerGuild(this, szDate, byRace, byLimitCnt, pkInfo);
}
