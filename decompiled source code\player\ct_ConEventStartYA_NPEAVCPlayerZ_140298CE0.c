/*
 * Function: ?ct_ConEventStart@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298CE0
 */

bool __fastcall ct_ConEventStart(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CashItemRemoteStore *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-48h]@1
  int iBegin_TT; // [sp+28h] [bp-20h]@6
  int iEnd_TT; // [sp+2Ch] [bp-1Ch]@6
  char v8; // [sp+34h] [bp-14h]@6
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9 )
  {
    iBegin_TT = 0;
    memset(&iEnd_TT, 0, sizeof(iEnd_TT));
    iBegin_TT = atoi(s_pwszDstCheat[0]);
    iEnd_TT = atoi(s_pwszDstCheat[1]);
    v8 = atoi(s_pwszDstCheat[2]);
    if ( iBegin_TT > 0 && iEnd_TT > 0 && (signed int)(unsigned __int8)v8 <= 3 )
    {
      v4 = CashItemRemoteStore::Instance();
      result = CashItemRemoteStore::start_conevent(v4, iBegin_TT, iEnd_TT, v8);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
