/*
 * Function: ?have_item@CMgrAvatorItemHistory@@QEAAXHPEADPEAU_AVATOR_DATA@@10KEKK_N0@Z
 * Address: 0x1402362B0
 */

void __fastcall CMgrAvatorItemHistory::have_item(CMgrAvatorItemHistory *this, int n, char *pszName, _AVATOR_DATA *pLoadData, _AVATOR_DATA *pBackupData, char *pszID, unsigned int dwIDSerial, char byDgr, unsigned int dwIP, unsigned int dwExpRate, bool bStart, char *pszFileName)
{
  __int64 *v12; // rdi@1
  signed __int64 i; // rcx@1
  int v14; // ecx@14
  int v15; // er8@14
  signed int v16; // eax@14
  int v17; // edx@14
  unsigned int v18; // eax@15
  char *v19; // rax@20
  char *v20; // rax@32
  char *v21; // rax@33
  char *v22; // rax@35
  char v23; // al@41
  unsigned int v24; // eax@41
  unsigned int v25; // eax@47
  int v26; // ecx@54
  int v27; // edx@54
  int v28; // ebx@54
  int v29; // edi@54
  int v30; // er8@54
  __int64 v31; // r9@54
  char *v32; // xmm1_8@63
  unsigned int v33; // eax@63
  unsigned int v34; // eax@64
  char *v35; // rax@71
  char *v36; // rax@72
  char *v37; // rax@74
  __int64 v38; // [sp+0h] [bp-218h]@1
  long double v39; // [sp+20h] [bp-1F8h]@14
  char *v40; // [sp+28h] [bp-1F0h]@14
  double v41; // [sp+30h] [bp-1E8h]@14
  char *v42; // [sp+38h] [bp-1E0h]@14
  long double v43; // [sp+40h] [bp-1D8h]@14
  unsigned int v44; // [sp+48h] [bp-1D0h]@14
  int v45; // [sp+50h] [bp-1C8h]@14
  long double v46; // [sp+58h] [bp-1C0h]@14
  unsigned int v47; // [sp+60h] [bp-1B8h]@14
  int v48; // [sp+68h] [bp-1B0h]@14
  int v49; // [sp+70h] [bp-1A8h]@14
  char *v50; // [sp+78h] [bp-1A0h]@14
  const char *v51; // [sp+80h] [bp-198h]@14
  const char *v52; // [sp+88h] [bp-190h]@14
  const char *v53; // [sp+90h] [bp-188h]@14
  int v54; // [sp+98h] [bp-180h]@14
  unsigned int v55; // [sp+A0h] [bp-178h]@14
  unsigned int v56; // [sp+A8h] [bp-170h]@14
  unsigned int v57; // [sp+B0h] [bp-168h]@14
  unsigned int v58; // [sp+B8h] [bp-160h]@14
  long double v59; // [sp+C0h] [bp-158h]@14
  long double v60; // [sp+C8h] [bp-150h]@14
  int v61; // [sp+D0h] [bp-148h]@14
  char *v62; // [sp+D8h] [bp-140h]@14
  unsigned int v63; // [sp+E0h] [bp-138h]@14
  _base_fld *v64; // [sp+F0h] [bp-128h]@4
  _base_fld *v65; // [sp+F8h] [bp-120h]@4
  _base_fld *v66; // [sp+100h] [bp-118h]@4
  int nTableCode; // [sp+108h] [bp-110h]@16
  _EQUIPKEY *v68; // [sp+110h] [bp-108h]@19
  _base_fld *v69; // [sp+118h] [bp-100h]@20
  _EMBELLKEY *v70; // [sp+120h] [bp-F8h]@24
  _base_fld *v71; // [sp+128h] [bp-F0h]@25
  _INVENKEY *v72; // [sp+130h] [bp-E8h]@29
  _base_fld *v73; // [sp+138h] [bp-E0h]@30
  _FORCEKEY *v74; // [sp+140h] [bp-D8h]@40
  _base_fld *v75; // [sp+148h] [bp-D0h]@41
  _INVENKEY *v76; // [sp+150h] [bp-C8h]@45
  _base_fld *v77; // [sp+158h] [bp-C0h]@46
  _UNIT_DB_BASE *v78; // [sp+160h] [bp-B8h]@53
  _ANIMUSKEY *v79; // [sp+168h] [bp-B0h]@60
  _base_fld *v80; // [sp+170h] [bp-A8h]@61
  _INVENKEY *v81; // [sp+178h] [bp-A0h]@68
  _base_fld *v82; // [sp+180h] [bp-98h]@69
  const char *v83; // [sp+188h] [bp-90h]@6
  const char *v84; // [sp+190h] [bp-88h]@9
  const char *v85; // [sp+198h] [bp-80h]@12
  char *v86; // [sp+1A0h] [bp-78h]@14
  unsigned int v87; // [sp+1A8h] [bp-70h]@14
  int v88; // [sp+1ACh] [bp-6Ch]@14
  __int64 v89; // [sp+1B0h] [bp-68h]@20
  __int64 v90; // [sp+1B8h] [bp-60h]@71
  char **v91; // [sp+1C0h] [bp-58h]@71
  int v92; // [sp+1C8h] [bp-50h]@71
  __int64 v93; // [sp+1D0h] [bp-48h]@72
  char **v94; // [sp+1D8h] [bp-40h]@72
  int v95; // [sp+1E0h] [bp-38h]@72
  __int64 v96; // [sp+1E8h] [bp-30h]@74
  char **v97; // [sp+1F0h] [bp-28h]@74
  int v98; // [sp+1F8h] [bp-20h]@74
  CMgrAvatorItemHistory *v99; // [sp+220h] [bp+8h]@1
  char *v100; // [sp+230h] [bp+18h]@1
  _AVATOR_DATA *v101; // [sp+238h] [bp+20h]@1

  v101 = pLoadData;
  v100 = pszName;
  v99 = this;
  v12 = &v38;
  for ( i = 130i64; i; --i )
  {
    *(_DWORD *)v12 = -858993460;
    v12 = (__int64 *)((char *)v12 + 4);
  }
  sData[0] = 0;
  v64 = CRecordData::GetRecord(&stru_1799C6420, pLoadData->dbAvator.m_zClassHistory[0]);
  v65 = CRecordData::GetRecord(&stru_1799C6420, v101->dbAvator.m_zClassHistory[1]);
  v66 = CRecordData::GetRecord(&stru_1799C6420, v101->dbAvator.m_zClassHistory[2]);
  if ( bStart )
  {
    if ( v66 )
      v83 = v66->m_strCode;
    else
      v83 = "-1";
    if ( v65 )
      v84 = v65->m_strCode;
    else
      v84 = "-1";
    if ( v64 )
      v85 = v64->m_strCode;
    else
      v85 = "-1";
    v86 = inet_ntoa((struct in_addr)dwIP);
    v14 = v101->dbAvator.m_byBagNum;
    v87 = v101->dbAvator.m_dwDalant - pBackupData->dbAvator.m_dwDalant;
    v15 = v101->dbAvator.m_byLastClassGrade;
    v88 = v101->dbAvator.m_byRaceSexCode & 1;
    v16 = v101->dbAvator.m_byRaceSexCode >> 1;
    v17 = v101->dbAvator.m_byLevel;
    v63 = v101->dbAvator.m_dwTotalPlayMin;
    v62 = v86;
    v61 = v14;
    v60 = v101->dbAvator.m_dPvPCashBag;
    v59 = v101->dbAvator.m_dPvPPoint;
    v58 = v101->dbAvator.m_dwGold;
    v57 = v87;
    v56 = v101->dbAvator.m_dwDalant;
    v55 = v101->dbAvator.m_dwClassInitCnt;
    v54 = v15;
    v53 = v83;
    v52 = v84;
    v51 = v85;
    v50 = v101->dbAvator.m_szClassCode;
    v49 = v88;
    v48 = v16;
    v47 = dwExpRate;
    v46 = v101->dbAvator.m_dExp;
    v45 = v17;
    v44 = (unsigned __int8)byDgr;
    LODWORD(v43) = dwIDSerial;
    v42 = pszID;
    LODWORD(v41) = v101->dbAvator.m_dwRecordNum;
    v40 = byte_1799C5B78;
    *(_QWORD *)&v39 = (char *)v99 + 192;
    sprintf(
      sBuf,
      "NAME: %s [%s %s]\r\n"
      "WORLD: %s\r\n"
      "SR: %d\r\n"
      "ID: %s (%d)\r\n"
      "DGR: %d\r\n"
      "LV: %d\r\n"
      "XP: %.0f (%d)\r\n"
      "RACE: %d\r\n"
      "SEX: %d\r\n"
      "CurClass:%s\r\n"
      "OldClass 0:%s, 1:%s, 2:%s\r\n"
      "LastClassGrade: %d\r\n"
      "InitClassCnt: %d\r\n"
      "$D: %u  ( %u push )\r\n"
      "$G: %u\r\n"
      "PvP: %.0f\r\n"
      "CB: %.0f\r\n"
      "BAG: %d\r\n"
      "IP: %s\r\n"
      "TIME: %d\r\n"
      "\r\n",
      v100,
      v99->m_szCurDate);
    strcat_0(sData, sBuf);
  }
  else
  {
    strcat_0(sData, "\r\n\t============\r\n\r\n");
    v18 = v101->dbAvator.m_byLevel;
    v44 = v101->dbAvator.m_dwTotalPlayMin;
    v43 = v101->dbAvator.m_dPvPCashBag;
    v42 = *(char **)&v101->dbAvator.m_dPvPPoint;
    LODWORD(v41) = v101->dbAvator.m_dwGold;
    LODWORD(v40) = v101->dbAvator.m_dwDalant;
    LODWORD(v39) = dwExpRate;
    sprintf(
      sBuf,
      "LV: %d\r\nXP: %.0f (%d)\r\n$D: %u\r\n$G: %u\r\nPvP: %.0f\r\nCB: %.0f\r\nTIME: %d\r\n\r\n",
      v18,
      v101->dbAvator.m_dExp);
    strcat_0(sData, sBuf);
  }
  sprintf(sBuf, "EQUIP\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 8; ++nTableCode )
  {
    v68 = &v101->dbAvator.m_EquipKey[nTableCode];
    if ( _EQUIPKEY::IsFilled(v68) )
    {
      v69 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, v68->zItemIndex);
      v89 = nTableCode;
      v19 = DisplayItemUpgInfo(nTableCode, v101->dbAvator.m_dwFixEquipLv[nTableCode]);
      v39 = *(double *)&v101->dbAvator.m_lnUID[v89];
      sprintf(sBuf, "\t%s_@%s[%I64u]\r\n", v69->m_strCode, v19);
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "EMBELL\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 7; ++nTableCode )
  {
    v70 = (_EMBELLKEY *)((char *)&v101->dbEquip + 27 * nTableCode);
    if ( _EMBELLKEY::IsFilled(v70) )
    {
      v71 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v70->byTableCode, v70->wItemIndex);
      sprintf(sBuf, "\t%s[%I64u]\r\n", v71->m_strCode, *(_QWORD *)&v70[2].wItemIndex);
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "INVEN\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 20 * v101->dbAvator.m_byBagNum; ++nTableCode )
  {
    v72 = (_INVENKEY *)((char *)&v101->dbInven + 37 * nTableCode);
    if ( _INVENKEY::IsFilled(v72) )
    {
      v73 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v72->byTableCode, v72->wItemIndex);
      if ( bStart )
      {
        if ( _INVENKEY::IsFilled((_INVENKEY *)((char *)&pBackupData->dbInven + 37 * nTableCode)) )
        {
          v21 = DisplayItemUpgInfo(v72->byTableCode, *(_DWORD *)&v72[3]);
          v40 = *(char **)&v72[5].bySlotIndex;
          v39 = *(double *)&v21;
          sprintf(sBuf, "\t%s_%u_@%s[%I64u]\r\n", v73->m_strCode, *(_QWORD *)&v72[1].bySlotIndex);
        }
        else
        {
          v20 = DisplayItemUpgInfo(v72->byTableCode, *(_DWORD *)&v72[3]);
          v40 = *(char **)&v72[5].bySlotIndex;
          v39 = *(double *)&v20;
          sprintf(sBuf, "\t%s_%u_@%s[%I64u] \t#push\r\n", v73->m_strCode, *(_QWORD *)&v72[1].bySlotIndex);
        }
      }
      else
      {
        v22 = DisplayItemUpgInfo(v72->byTableCode, *(_DWORD *)&v72[3]);
        v40 = *(char **)&v72[5].bySlotIndex;
        v39 = *(double *)&v22;
        sprintf(sBuf, "\t%s_%u_@%s[%I64u]\r\n", v73->m_strCode, *(_QWORD *)&v72[1].bySlotIndex);
      }
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "FORCE\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 88; ++nTableCode )
  {
    v74 = (_FORCEKEY *)((char *)&v101->dbForce + 25 * nTableCode);
    if ( _FORCEKEY::IsFilled(v74) )
    {
      v23 = _FORCEKEY::GetIndex(v74);
      v75 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 15, (unsigned __int8)v23);
      v24 = _FORCEKEY::GetStat(v74);
      v39 = *(double *)&v74[2].dwKey;
      sprintf(sBuf, "\t%s_%u[%I64u]\r\n", v75->m_strCode, v24);
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "RES\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 20; ++nTableCode )
  {
    v76 = (_INVENKEY *)&v101->dbCutting.m_List[nTableCode];
    if ( _INVENKEY::IsFilled(v76) )
    {
      v77 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, v76->wItemIndex);
      if ( v77 )
      {
        sprintf(sBuf, "\t%s_%u\r\n", v77->m_strCode, *(_DWORD *)&v76[1]);
        strcat_0(sData, sBuf);
      }
      else
      {
        v25 = v76->wItemIndex;
        LODWORD(v39) = dwIDSerial;
        CLogFile::Write(
          &stru_1799C8E78,
          "CMgrAvatorItemHistory::have_item() : _CUTTING_DB_BASE::_LIST* pList->Key.wItemIndex(%u) i(%d) Serial(%u)",
          v25,
          (unsigned int)nTableCode);
      }
    }
  }
  if ( v101->dbAvator.m_byRaceSexCode >> 1 )
  {
    if ( v101->dbAvator.m_byRaceSexCode >> 1 == 1 )
    {
      sprintf(sBuf, "ANIMUS\r\n");
      strcat_0(sData, sBuf);
      for ( nTableCode = 0; nTableCode < 4; ++nTableCode )
      {
        v79 = (_ANIMUSKEY *)&v101->dbAnimus + 34 * nTableCode;
        if ( _ANIMUSKEY::IsFilled(v79) )
        {
          v80 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 24, v79->byItemIndex);
          v39 = *(double *)&v79[17].byItemIndex;
          sprintf(sBuf, "\t%s_%I64u[%I64u]\r\n", v80->m_strCode, *(_QWORD *)&v79[1].byItemIndex);
          strcat_0(sData, sBuf);
        }
      }
    }
  }
  else
  {
    sprintf(sBuf, "UNIT\r\n");
    strcat_0(sData, sBuf);
    for ( nTableCode = 0; nTableCode < 4; ++nTableCode )
    {
      v78 = (_UNIT_DB_BASE *)((char *)&v101->dbUnit + 62 * nTableCode);
      if ( v101->dbUnit.m_List[nTableCode].byFrame != 255 )
      {
        v26 = v78->m_List[0].byPart[4];
        v27 = v78->m_List[0].byPart[3];
        v28 = v78->m_List[0].byPart[2];
        v29 = v78->m_List[0].byPart[1];
        v30 = v78->m_List[0].byPart[0];
        v31 = v78->m_List[0].byFrame;
        v44 = v78->m_List[0].byPart[5];
        LODWORD(v43) = v26;
        LODWORD(v42) = v27;
        LODWORD(v41) = v28;
        LODWORD(v40) = v29;
        LODWORD(v39) = v30;
        sprintf(sBuf, "\t%d>fr:%d %d/%d/%d/%d/%d/%d\r\n", (unsigned int)nTableCode, v31);
        strcat_0(sData, sBuf);
      }
    }
  }
  if ( bStart )
  {
    *(double *)&v32 = v101->dbTrunk.dDalant - pBackupData->dbTrunk.dDalant;
    v33 = v101->dbTrunk.bySlotNum;
    v41 = v101->dbTrunk.dGold - pBackupData->dbTrunk.dGold;
    v40 = v32;
    v39 = v101->dbTrunk.dGold;
    sprintf(sBuf, "TRUNK (slot:%d, ^D:%.0f, ^G:%.0f) ( ^D:%.0f, ^G:%.0f push )\r\n", v33, v101->dbTrunk.dDalant);
  }
  else
  {
    v34 = v101->dbTrunk.bySlotNum;
    v39 = v101->dbTrunk.dGold;
    sprintf(sBuf, "TRUNK (slot:%d, ^D:%.0f, ^G:%.0f)\r\n", v34, v101->dbTrunk.dDalant);
  }
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < v101->dbTrunk.bySlotNum; ++nTableCode )
  {
    v81 = &v101->dbTrunk.m_List[nTableCode].Key;
    if ( _INVENKEY::IsFilled(v81) )
    {
      v82 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v81->byTableCode, v81->wItemIndex);
      if ( bStart )
      {
        if ( _INVENKEY::IsFilled(&pBackupData->dbTrunk.m_List[nTableCode].Key) )
        {
          v93 = v81[4].bySlotIndex;
          v94 = pRace;
          v95 = v81->byTableCode;
          v36 = DisplayItemUpgInfo(v95, *(_DWORD *)&v81[3]);
          v41 = *(double *)&v94[v93];
          v40 = *(char **)&v81[5].byTableCode;
          v39 = *(double *)&v36;
          sprintf(sBuf, "\t%s_%u_@%s[%I64u] %s\r\n", v82->m_strCode, *(_QWORD *)&v81[1].bySlotIndex);
        }
        else
        {
          v90 = v81[4].bySlotIndex;
          v91 = pRace;
          v92 = v81->byTableCode;
          v35 = DisplayItemUpgInfo(v92, *(_DWORD *)&v81[3]);
          v41 = *(double *)&v91[v90];
          v40 = *(char **)&v81[5].byTableCode;
          v39 = *(double *)&v35;
          sprintf(sBuf, "\t%s_%u_@%s[%I64u] %s\t#push\r\n", v82->m_strCode, *(_QWORD *)&v81[1].bySlotIndex);
        }
      }
      else
      {
        v96 = v81[4].bySlotIndex;
        v97 = pRace;
        v98 = v81->byTableCode;
        v37 = DisplayItemUpgInfo(v98, *(_DWORD *)&v81[3]);
        v41 = *(double *)&v97[v96];
        v40 = *(char **)&v81[5].byTableCode;
        v39 = *(double *)&v37;
        sprintf(sBuf, "\t%s_%u_@%s[%I64u] %s\r\n", v82->m_strCode, *(_QWORD *)&v81[1].bySlotIndex);
      }
      strcat_0(sData, sBuf);
    }
  }
  if ( bStart )
    strcat_0(sData, "\r\n\t============\r\n\r\n");
  CMgrAvatorItemHistory::WriteFile(v99, pszFileName, sData);
}
