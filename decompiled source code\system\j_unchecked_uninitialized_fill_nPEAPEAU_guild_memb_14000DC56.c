/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAU_guild_member_refresh_data@@_KPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@@stdext@@YAXPEAPEAU_guild_member_refresh_data@@_KAEBQEAU1@AEAV?$allocator@PEAU_guild_member_refresh_data@@@std@@@Z
 * Address: 0x14000DC56
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<_guild_member_refresh_data * *,unsigned __int64,_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>(_guild_member_refresh_data **_First, unsigned __int64 _Count, _guild_member_refresh_data *const *_Val, std::allocator<_guild_member_refresh_data *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<_guild_member_refresh_data * *,unsigned __int64,_guild_member_refresh_data *,std::allocator<_guild_member_refresh_data *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
