/*
 * Function: j_??$_Iter_random@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderClassInfo@@@std@@YA?AUrandom_access_iterator_tag@0@AEBV?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@AEBQEAPEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x140008EF4
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__formal, CUnmannedTraderClassInfo **const *a2)
{
  return std::_Iter_random<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(
           __formal,
           a2);
}
