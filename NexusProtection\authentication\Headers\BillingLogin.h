#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <cstdint>
#include <functional>
#include <vector>
#include <array>
#include <unordered_map>
#include <mutex>
#include <atomic>

namespace NexusProtection::Authentication {

    /**
     * @brief Billing Login System
     * 
     * Represents a billing authentication system that handles user login
     * operations for different billing providers including standard billing,
     * ID-based billing, Japanese billing, and NULL billing systems.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - LoginCBillingUEAAXPEAVCUserDBZ_14028CAC0.c (CBilling::Login)
     * - LoginCBillingIDUEAAXPEAVCUserDBZ_14028E0F0.c (CBillingID::Login)
     * - LoginCBillingJPUEAAXPEAVCUserDBZ_14028E910.c (CBillingJP::Login)
     * - LoginCBillingNULLUEAAXPEAVCUserDBZ_14028DBD0.c (CBillingNULL::Login)
     * - LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c (CBillingManager::Login)
     */

    /**
     * @brief Forward declarations
     */
    class CUserDB;

    /**
     * @brief Billing information structure
     */
    struct BillingInfo {
        int16_t iType{0};                           // Billing type
        int32_t lRemainTime{0};                     // Remaining time in seconds
        std::chrono::system_clock::time_point stEndDate; // End date
        std::string szCMS;                          // CMS identifier
        bool bIsPcBang{false};                      // PC Bang flag
        bool bIsActive{true};                       // Active status
        
        BillingInfo();
        void Reset();
        bool IsValid() const;
        std::string ToString() const;
    };

    /**
     * @brief User database information for billing
     */
    struct UserBillingData {
        std::string szAccountID;                    // Account ID
        uint32_t dwIP{0};                          // User IP address
        BillingInfo billingInfo;                    // Billing information
        bool bBillingNoLogout{false};              // No logout flag
        
        UserBillingData();
        void Reset();
        bool IsValid() const;
        std::string GetIPAddressString() const;
        void SetIPAddress(uint32_t ip) { dwIP = ip; }
        void SetBillingNoLogout(bool noLogout) { bBillingNoLogout = noLogout; }
    };

    /**
     * @brief Base Billing Login Class
     * 
     * Abstract base class for all billing login implementations.
     */
    class IBillingLogin {
    public:
        IBillingLogin() = default;
        virtual ~IBillingLogin() = default;

        // Explicitly delete copy operations due to atomic members
        IBillingLogin(const IBillingLogin&) = delete;
        IBillingLogin& operator=(const IBillingLogin&) = delete;

        // Default move operations
        IBillingLogin(IBillingLogin&&) = default;
        IBillingLogin& operator=(IBillingLogin&&) = default;

        // Core login operations
        virtual bool Login(const UserBillingData& userData) = 0;
        virtual bool Logout(const std::string& accountID) = 0;
        virtual bool IsLoggedIn(const std::string& accountID) const = 0;

        // Configuration
        virtual void SetEnabled(bool enabled) { m_isEnabled = enabled; }
        virtual bool IsEnabled() const { return m_isEnabled; }

        // Statistics
        virtual uint32_t GetActiveUserCount() const = 0;
        virtual uint32_t GetTotalLoginAttempts() const { return m_totalLoginAttempts; }
        virtual uint32_t GetSuccessfulLogins() const { return m_successfulLogins; }
        virtual uint32_t GetFailedLogins() const { return m_failedLogins; }

        // Utility
        virtual std::string GetBillingTypeName() const = 0;
        virtual std::string ToString() const = 0;

    protected:
        bool m_isEnabled{true};
        std::atomic<uint32_t> m_totalLoginAttempts{0};
        std::atomic<uint32_t> m_successfulLogins{0};
        std::atomic<uint32_t> m_failedLogins{0};

        void UpdateStatistics(bool success);
    };

    /**
     * @brief Standard Billing Login Implementation
     * 
     * Handles standard billing login operations.
     */
    class CBillingLogin : public IBillingLogin {
    public:
        CBillingLogin();
        ~CBillingLogin() override;

        // Copy and move semantics
        CBillingLogin(const CBillingLogin& other) = delete;
        CBillingLogin& operator=(const CBillingLogin& other) = delete;
        CBillingLogin(CBillingLogin&& other) noexcept;
        CBillingLogin& operator=(CBillingLogin&& other) noexcept;

        // IBillingLogin implementation
        bool Login(const UserBillingData& userData) override;
        bool Logout(const std::string& accountID) override;
        bool IsLoggedIn(const std::string& accountID) const override;
        uint32_t GetActiveUserCount() const override;
        std::string GetBillingTypeName() const override { return "Standard"; }
        std::string ToString() const override;

        // Standard billing specific methods
        bool SendLoginMessage(const std::string& accountID, const std::string& ipAddress, const std::string& cms);
        bool ValidateBillingInfo(const BillingInfo& billingInfo) const;

    private:
        std::unordered_map<std::string, UserBillingData> m_activeUsers;
        mutable std::mutex m_usersMutex;

        bool ProcessLogin(const UserBillingData& userData);
        void CleanupExpiredUsers();
    };

    /**
     * @brief ID-based Billing Login Implementation
     * 
     * Handles ID-based billing login operations with PC Bang support.
     */
    class CBillingIDLogin : public IBillingLogin {
    public:
        CBillingIDLogin();
        ~CBillingIDLogin() override;

        // Copy and move semantics
        CBillingIDLogin(const CBillingIDLogin& other) = delete;
        CBillingIDLogin& operator=(const CBillingIDLogin& other) = delete;
        CBillingIDLogin(CBillingIDLogin&& other) noexcept;
        CBillingIDLogin& operator=(CBillingIDLogin&& other) noexcept;

        // IBillingLogin implementation
        bool Login(const UserBillingData& userData) override;
        bool Logout(const std::string& accountID) override;
        bool IsLoggedIn(const std::string& accountID) const override;
        uint32_t GetActiveUserCount() const override;
        std::string GetBillingTypeName() const override { return "ID-based"; }
        std::string ToString() const override;

        // ID billing specific methods
        bool IsPcBangUser(const std::string& accountID) const;
        void SetPcBangMode(bool enabled) { m_pcBangMode = enabled; }
        bool GetPcBangMode() const { return m_pcBangMode; }

    private:
        std::unordered_map<std::string, UserBillingData> m_activeUsers;
        bool m_pcBangMode{false};
        mutable std::mutex m_usersMutex;

        bool ProcessPcBangLogin(const UserBillingData& userData);
        bool ValidatePcBangUser(const UserBillingData& userData) const;
    };

    /**
     * @brief Japanese Billing Login Implementation
     * 
     * Handles Japanese-specific billing login operations.
     */
    class CBillingJPLogin : public IBillingLogin {
    public:
        CBillingJPLogin();
        ~CBillingJPLogin() override;

        // IBillingLogin implementation
        bool Login(const UserBillingData& userData) override;
        bool Logout(const std::string& accountID) override;
        bool IsLoggedIn(const std::string& accountID) const override;
        uint32_t GetActiveUserCount() const override;
        std::string GetBillingTypeName() const override { return "Japanese"; }
        std::string ToString() const override;

        // Japanese billing specific methods
        void SetJapaneseRegion(const std::string& region) { m_japaneseRegion = region; }
        const std::string& GetJapaneseRegion() const { return m_japaneseRegion; }

    private:
        std::unordered_map<std::string, UserBillingData> m_activeUsers;
        std::string m_japaneseRegion{"JP"};
        mutable std::mutex m_usersMutex;

        bool ProcessJapaneseLogin(const UserBillingData& userData);
        bool ValidateJapaneseUser(const UserBillingData& userData) const;
    };

    /**
     * @brief NULL Billing Login Implementation
     * 
     * Handles NULL billing (free/test) login operations.
     */
    class CBillingNULLLogin : public IBillingLogin {
    public:
        CBillingNULLLogin();
        ~CBillingNULLLogin() override;

        // IBillingLogin implementation
        bool Login(const UserBillingData& userData) override;
        bool Logout(const std::string& accountID) override;
        bool IsLoggedIn(const std::string& accountID) const override;
        uint32_t GetActiveUserCount() const override;
        std::string GetBillingTypeName() const override { return "NULL/Free"; }
        std::string ToString() const override;

        // NULL billing specific methods
        void SetFreeMode(bool enabled) { m_freeMode = enabled; }
        bool GetFreeMode() const { return m_freeMode; }
        void SetMaxFreeUsers(uint32_t maxUsers) { m_maxFreeUsers = maxUsers; }
        uint32_t GetMaxFreeUsers() const { return m_maxFreeUsers; }

    private:
        std::unordered_map<std::string, UserBillingData> m_activeUsers;
        bool m_freeMode{true};
        uint32_t m_maxFreeUsers{1000};
        mutable std::mutex m_usersMutex;

        bool CanAcceptNewUser() const;
    };

    /**
     * @brief Billing Manager
     * 
     * Manages multiple billing providers and routes login requests.
     */
    class CBillingManager {
    public:
        enum class BillingType {
            STANDARD = 0,
            ID_BASED = 1,
            JAPANESE = 2,
            NULL_BILLING = 3,
            UNKNOWN = -1
        };

        CBillingManager();
        ~CBillingManager();

        // Core operations
        bool Initialize();
        void Shutdown();
        bool Login(const UserBillingData& userData);
        bool Logout(const std::string& accountID);

        // Provider management
        void SetBillingProvider(BillingType type, std::unique_ptr<IBillingLogin> provider);
        IBillingLogin* GetBillingProvider(BillingType type) const;
        void SetDefaultBillingType(BillingType type) { m_defaultBillingType = type; }
        BillingType GetDefaultBillingType() const { return m_defaultBillingType; }

        // Statistics
        uint32_t GetTotalActiveUsers() const;
        uint32_t GetActiveUsersByType(BillingType type) const;
        std::vector<std::string> GetActiveUserList() const;

        // Configuration
        bool LoadConfiguration(const std::string& configPath);
        bool SaveConfiguration(const std::string& configPath) const;

        // Utility
        static std::string BillingTypeToString(BillingType type);
        static BillingType StringToBillingType(const std::string& typeStr);
        std::string ToString() const;

    private:
        std::array<std::unique_ptr<IBillingLogin>, 4> m_billingProviders;
        BillingType m_defaultBillingType{BillingType::STANDARD};
        bool m_isInitialized{false};
        mutable std::mutex m_managerMutex;

        BillingType DetermineBillingType(const UserBillingData& userData) const;
        void InitializeDefaultProviders();
        void CleanupProviders();
    };

    /**
     * @brief Billing Login Factory
     * 
     * Factory class for creating billing login instances.
     */
    class BillingLoginFactory {
    public:
        static std::unique_ptr<IBillingLogin> CreateBillingLogin(CBillingManager::BillingType type);
        static std::unique_ptr<CBillingManager> CreateBillingManager();
        
        // Predefined configurations
        static std::unique_ptr<CBillingManager> CreateStandardBillingManager();
        static std::unique_ptr<CBillingManager> CreateTestBillingManager();
        static std::unique_ptr<CBillingManager> CreateProductionBillingManager();
    };

} // namespace NexusProtection::Authentication
