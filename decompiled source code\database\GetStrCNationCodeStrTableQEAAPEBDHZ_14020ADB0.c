/*
 * Function: ?GetStr@CNationCodeStrTable@@QEAAPEBDH@Z
 * Address: 0x14020ADB0
 */

const char *__fastcall CNationCodeStrTable::GetStr(CNationCodeStrTable *this, int iType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  const char *result; // rax@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  CNationCodeStr *pData; // [sp+28h] [bp-20h]@4
  CNationCodeStrTable *v7; // [sp+50h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pData = 0i64;
  if ( CHashMapPtrPool<int,CNationCodeStr>::get(&v7->m_kTable, iType, &pData) )
    result = CNationCodeStr::GetStr(pData);
  else
    result = 0i64;
  return result;
}
