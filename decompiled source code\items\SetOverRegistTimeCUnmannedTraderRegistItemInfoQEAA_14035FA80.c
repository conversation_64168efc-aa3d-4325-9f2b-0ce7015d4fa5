/*
 * Function: ?SetOverRegistTime@CUnmannedTraderRegistItemInfo@@QEAAXXZ
 * Address: 0x14035FA80
 */

void __fastcall CUnmannedTraderRegistItemInfo::SetOverRegistTime(CUnmannedTraderRegistItemInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __int64 _Time; // [sp+28h] [bp-20h]@4
  CUnmannedTraderRegistItemInfo *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _Time = 0i64;
  time_18(&_Time);
  v5->m_tStartTime = _Time - 3600 * v5->m_bySellTurm;
}
