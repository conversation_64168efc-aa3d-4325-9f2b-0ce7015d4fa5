/*
 * Function: ?InitEffParam@_effect_parameter@@QEAAXXZ
 * Address: 0x140074960
 */

void __fastcall _effect_parameter::InitEffParam(_effect_parameter *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _effect_parameter *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4->m_bLock = 0;
  if ( v4->m_pDataParam )
  {
    for ( j = 0; j < 62; ++j )
      v4->m_pDataParam->m_fEff_Rate[j] = FLOAT_1_0;
    for ( j = 0; j < 42; ++j )
      LODWORD(v4->m_pDataParam->m_fEff_Plus[j]) = 0;
    for ( j = 0; j < 29; ++j )
      v4->m_pDataParam->m_bEff_State[j] = 0;
    for ( j = 0; j < 83; ++j )
      LODWORD(v4->m_pDataParam->m_fEff_Have[j]) = 0;
  }
}
