/*
 * Function: j_?ExponentiatePublicElement@?$DL_PublicKey@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@@Z
 * Address: 0x14000D38C
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::ExponentiatePublicElement(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *exponent)
{
  return CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::ExponentiatePublicElement(this, result, exponent);
}
