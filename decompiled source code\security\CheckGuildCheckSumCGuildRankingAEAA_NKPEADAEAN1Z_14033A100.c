/*
 * Function: ?CheckGuildCheckSum@CGuildRanking@@AEAA_NKPEADAEAN1@Z
 * Address: 0x14033A100
 */

char __usercall CGuildRanking::CheckGuildCheckSum@<al>(CGuildRanking *this@<rcx>, unsigned int dwSerial@<edx>, char *wszGuildName@<r8>, long double *dDalant@<r9>, __int64 a5@<xmm0>, long double *dGold)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v9; // [sp+0h] [bp-B8h]@1
  __int64 v10; // [sp+20h] [bp-98h]@11
  long double v11; // [sp+28h] [bp-90h]@11
  __int64 v12; // [sp+30h] [bp-88h]@11
  __int64 v13; // [sp+38h] [bp-80h]@11
  CCheckSumGuildData v14; // [sp+48h] [bp-70h]@4
  CCheckSumGuildData kSrcValue; // [sp+78h] [bp-40h]@4
  int v16; // [sp+94h] [bp-24h]@4
  char v17; // [sp+98h] [bp-20h]@6
  char v18; // [sp+99h] [bp-1Fh]@9
  char v19; // [sp+9Ah] [bp-1Eh]@12
  char v20; // [sp+9Bh] [bp-1Dh]@13
  __int64 v21; // [sp+A0h] [bp-18h]@4
  __int64 v22; // [sp+A8h] [bp-10h]@11
  unsigned int dwSeriala; // [sp+C8h] [bp+10h]@1
  char *wszName; // [sp+D0h] [bp+18h]@1
  long double *v25; // [sp+D8h] [bp+20h]@1

  v25 = dDalant;
  wszName = wszGuildName;
  dwSeriala = dwSerial;
  v6 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v21 = -2i64;
  CCheckSumGuildData::CCheckSumGuildData(&v14, dwSerial);
  CCheckSumGuildData::CCheckSumGuildData(&kSrcValue, dwSeriala);
  v16 = 0;
  if ( !unk_1799C9ADC )
    goto LABEL_16;
  CCheckSumGuildData::Encode(&kSrcValue, *v25, *dGold);
  v16 = CCheckSumGuildData::Load(&v14, pkDB, &kSrcValue);
  if ( v16 < 0 )
  {
    CLogFile::Write(&stru_1799C8F30, "Guild(%u, %s) CheckSum Load Fail!", dwSeriala, wszName);
    v17 = 0;
    CCheckSumGuildData::~CCheckSumGuildData(&kSrcValue);
    CCheckSumGuildData::~CCheckSumGuildData(&v14);
    return v17;
  }
  if ( v16 )
    goto LABEL_16;
  v16 = CCheckSumGuildData::CheckDiff(&v14, pkDB, wszName, &kSrcValue);
  if ( v16 < 0 )
  {
    CLogFile::Write(&stru_1799C8F30, "Guild(%u, %s) CheckSum Insert Fail!", dwSeriala, wszName);
    v18 = 0;
    CCheckSumGuildData::~CCheckSumGuildData(&kSrcValue);
    CCheckSumGuildData::~CCheckSumGuildData(&v14);
    return v18;
  }
  if ( v16 <= 0
    || (CCheckSumGuildData::GetGold(&v14),
        v22 = a5,
        CCheckSumGuildData::GetDalant(&v14),
        v13 = *(_QWORD *)dGold,
        v12 = v22,
        v11 = *v25,
        v10 = a5,
        CLogFile::Write(
          &stru_1799C8F30,
          "Guild(%u, %s) Diff Value  dalant(%f) -> %f, Gold(%f) -> %f",
          dwSeriala,
          wszName),
        CCheckSumGuildData::GetDalant(&v14),
        *(_QWORD *)v25 = a5,
        CCheckSumGuildData::GetGold(&v14),
        *(_QWORD *)dGold = a5,
        CRFWorldDatabase::UpdateGuildMoney(pkDB, dwSeriala, *v25, *dGold)) )
  {
LABEL_16:
    v20 = 1;
    CCheckSumGuildData::~CCheckSumGuildData(&kSrcValue);
    CCheckSumGuildData::~CCheckSumGuildData(&v14);
    result = v20;
  }
  else
  {
    v19 = 0;
    CCheckSumGuildData::~CCheckSumGuildData(&kSrcValue);
    CCheckSumGuildData::~CCheckSumGuildData(&v14);
    result = v19;
  }
  return result;
}
