/*
 * Function: ?GetRecord@CDummyPosTable@@QEAAPEAU_dummy_position@@PEAD@Z
 * Address: 0x14018A600
 */

_dummy_position *__fastcall CDummyPosTable::GetRecord(CDummyPosTable *this, char *szCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-A8h]@1
  char Dest; // [sp+30h] [bp-78h]@4
  char *String; // [sp+78h] [bp-30h]@4
  char *Source; // [sp+80h] [bp-28h]@4
  int j; // [sp+88h] [bp-20h]@4
  unsigned __int64 v10; // [sp+98h] [bp-10h]@4
  CDummyPosTable *v11; // [sp+B0h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  String = _strdup(szCode);
  Source = _strlwr(String);
  strcpy_0(&Dest, Source);
  free(String);
  for ( j = 0; j < v11->m_nDumPosDataNum; ++j )
  {
    if ( !strcmp_0(v11->m_pDumPos[j].m_szCode, &Dest) )
      return &v11->m_pDumPos[j];
  }
  return 0i64;
}
