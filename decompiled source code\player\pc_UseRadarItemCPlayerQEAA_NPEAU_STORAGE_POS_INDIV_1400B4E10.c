/*
 * Function: ?pc_UseRadarItem@CPlayer@@QEAA_NPEAU_STORAGE_POS_INDIV@@PEAG@Z
 * Address: 0x1400B4E10
 */

char __fastcall CPlayer::pc_UseRadarItem(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 *pConsumeSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-E8h]@1
  int *pnConsume; // [sp+20h] [bp-C8h]@27
  bool *pbOverLap; // [sp+28h] [bp-C0h]@27
  char v9; // [sp+30h] [bp-B8h]@4
  CRadarItemMgr *v10; // [sp+38h] [bp-B0h]@4
  _STORAGE_LIST *v11; // [sp+40h] [bp-A8h]@4
  _STORAGE_LIST::_db_con *pItema; // [sp+48h] [bp-A0h]@4
  _RadarItem_fld *pRadarFld; // [sp+50h] [bp-98h]@4
  unsigned int dwDelay; // [sp+58h] [bp-90h]@4
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+68h] [bp-80h]@24
  char v16; // [sp+70h] [bp-78h]@24
  int v17; // [sp+98h] [bp-50h]@24
  char v18; // [sp+9Ch] [bp-4Ch]@24
  bool v19; // [sp+C4h] [bp-24h]@24
  char v20; // [sp+C5h] [bp-23h]@24
  CPlayer *pMaster; // [sp+F0h] [bp+8h]@1
  _STORAGE_POS_INDIV *v22; // [sp+F8h] [bp+10h]@1
  unsigned __int16 *pItemSerials; // [sp+100h] [bp+18h]@1

  pItemSerials = pConsumeSerial;
  v22 = pItem;
  pMaster = this;
  v3 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  v10 = &pMaster->m_pUserDB->m_RadarItemMgr;
  v11 = 0i64;
  pItema = 0i64;
  pRadarFld = 0i64;
  dwDelay = 0;
  if ( CPlayerDB::GetHP(&pMaster->m_Param) )
  {
    if ( CPlayer::IsRidingUnit(pMaster) )
    {
      v9 = 6;
    }
    else if ( CPlayer::IsSiegeMode(pMaster) )
    {
      v9 = 7;
    }
    else if ( v22->byStorageCode < 8 )
    {
      v11 = pMaster->m_Param.m_pStoragePtr[v22->byStorageCode];
      pItema = _STORAGE_LIST::GetPtrFromSerial(v11, v22->wItemSerial);
      if ( pItema )
      {
        if ( pItema->m_byTableCode == 34 )
        {
          pRadarFld = (_RadarItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 34, pItema->m_wItemIndex);
          if ( pRadarFld )
          {
            dwDelay = (signed int)ffloor(pRadarFld->m_fActDelay);
            if ( pRadarFld->m_nDuration <= dwDelay )
            {
              if ( pItema->m_bLock )
              {
                v9 = 4;
              }
              else
              {
                CUserDB::CalcRadarDelay(pMaster->m_pUserDB);
                if ( CRadarItemMgr::IsUse(v10) )
                {
                  v9 = 8;
                }
                else
                {
                  ppConsumeItems = 0i64;
                  memset(&v16, 0, 0x10ui64);
                  v17 = 0;
                  memset(&v18, 0, 8ui64);
                  v19 = 0;
                  memset(&v20, 0, 2ui64);
                  if ( CPlayer::GetUseConsumeItem(
                         pMaster,
                         pRadarFld->m_ConsumeItemList,
                         pItemSerials,
                         &ppConsumeItems,
                         &v17,
                         &v19) )
                  {
                    if ( rand() % 100 < pRadarFld->m_nSucPro )
                    {
                      CPlayer::DeleteUseConsumeItem(pMaster, &ppConsumeItems, &v17, &v19);
                    }
                    else
                    {
                      v9 = 9;
                      LOBYTE(pbOverLap) = 0;
                      LOBYTE(pnConsume) = 0;
                      CPlayer::Emb_AlterDurPoint(pMaster, v11->m_nListCode, pItema->m_byStorageIndex, -1, 0, 0);
                      CMgrAvatorItemHistory::consume_del_item(
                        &CPlayer::s_MgrItemHistory,
                        pMaster->m_ObjID.m_wIndex,
                        pItema,
                        pMaster->m_szItemHistoryFileName);
                    }
                  }
                  else
                  {
                    v9 = 11;
                  }
                }
              }
            }
            else
            {
              v9 = 10;
            }
          }
          else
          {
            v9 = 2;
          }
        }
        else
        {
          v9 = 2;
        }
      }
      else
      {
        v9 = 1;
      }
    }
    else
    {
      v9 = 1;
    }
  }
  else
  {
    v9 = 3;
  }
  CPlayer::SendMsg_UseRadarResult(pMaster, v9, v22->wItemSerial, dwDelay);
  if ( !v9 )
  {
    LOBYTE(pbOverLap) = 0;
    LOBYTE(pnConsume) = 0;
    CPlayer::Emb_AlterDurPoint(pMaster, v11->m_nListCode, pItema->m_byStorageIndex, -1, 0, 0);
    LODWORD(pnConsume) = dwDelay;
    CRadarItemMgr::SetUseRadar(v10, (char (*)[64])pRadarFld->m_strCode, pMaster, pRadarFld->m_nDuration, dwDelay);
    CUserDB::SetRadarDelay(pMaster->m_pUserDB, dwDelay);
    CRadarItemMgr::RadarProc(v10, pRadarFld);
    CPlayer::SendMsg_RadarCharSearchResult(pMaster);
  }
  return 1;
}
