/**
 * @file CGuildMemberManager_Methods.cpp
 * @brief Additional methods for CGuildMemberManager
 * 
 * This file contains the utility methods, callbacks, and helper functions for the guild member manager.
 */

#include "../Headers/CGuildMemberManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <iomanip>

namespace NexusProtection::Guild {

void CGuildMemberManager::SetMemberLoginCallback(MemberLoginCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_memberLoginCallback = std::move(callback);
}

void CGuildMemberManager::SetMemberLogoutCallback(MemberLogoutCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_memberLogoutCallback = std::move(callback);
}

void CGuildMemberManager::SetMemberJoinCallback(MemberJoinCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_memberJoinCallback = std::move(callback);
}

void CGuildMemberManager::SetMemberLeaveCallback(MemberLeaveCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_memberLeaveCallback = std::move(callback);
}

void CGuildMemberManager::SetMasterChangeCallback(MasterChangeCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_masterChangeCallback = std::move(callback);
}

void CGuildMemberManager::SetPermissionChangeCallback(PermissionChangeCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_permissionChangeCallback = std::move(callback);
}

void CGuildMemberManager::SetMemberMessageCallback(MemberMessageCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_memberMessageCallback = std::move(callback);
}

GuildMemberStats CGuildMemberManager::GetStatistics() const {
    // Convert atomic stats to copyable stats
    GuildMemberStats stats;
    stats.totalMemberLogins = m_stats.totalMemberLogins.load();
    stats.totalMemberLogouts = m_stats.totalMemberLogouts.load();
    stats.totalMemberJoins = m_stats.totalMemberJoins.load();
    stats.totalMemberLeaves = m_stats.totalMemberLeaves.load();
    stats.totalMasterChanges = m_stats.totalMasterChanges.load();
    stats.totalPermissionChanges = m_stats.totalPermissionChanges.load();
    stats.activeMembersOnline = m_stats.activeMembersOnline.load();
    stats.messagesSent = m_stats.messagesSent.load();
    stats.startTime = m_stats.startTime;
    return stats;
}

GuildMemberConfig CGuildMemberManager::GetConfiguration() const {
    std::lock_guard<std::mutex> lock(m_configMutex);
    return m_config;
}

bool CGuildMemberManager::UpdateConfiguration(const GuildMemberConfig& config) {
    try {
        if (!config.IsValid()) {
            LogMemberActivity(0, "UpdateConfiguration: Invalid configuration provided");
            return false;
        }
        
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }
        
        std::cout << "[INFO] Guild member configuration updated" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("UpdateConfiguration exception: ") + e.what());
        return false;
    }
}

void CGuildMemberManager::ResetStatistics() {
    try {
        m_stats.totalMemberLogins.store(0);
        m_stats.totalMemberLogouts.store(0);
        m_stats.totalMemberJoins.store(0);
        m_stats.totalMemberLeaves.store(0);
        m_stats.totalMasterChanges.store(0);
        m_stats.totalPermissionChanges.store(0);
        m_stats.activeMembersOnline.store(0);
        m_stats.messagesSent.store(0);
        m_stats.startTime = std::chrono::steady_clock::now();
        
        m_consecutiveErrors.store(0);
        m_lastErrorTime = std::chrono::steady_clock::now();
        
        std::cout << "[INFO] Guild member statistics reset" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ResetStatistics: " << e.what() << std::endl;
    }
}

std::vector<GuildMemberInfo> CGuildMemberManager::InitializeMemberData(const _guild_member_info* legacyMembers, int memberCount) {
    try {
        std::vector<GuildMemberInfo> members;
        members.reserve(memberCount);
        
        for (int i = 0; i < memberCount; ++i) {
            if (legacyMembers[i].IsFill()) {
                GuildMemberInfo member;
                member.memberSerial = legacyMembers[i].dwSerial;
                member.memberName = legacyMembers[i].wszName;
                member.level = legacyMembers[i].byLv;
                member.pvpPoints = legacyMembers[i].dwPvpPoint;
                member.memberClass = static_cast<GuildMemberClass>(legacyMembers[i].byClassInGuild);
                member.playerPtr = legacyMembers[i].pPlayer;
                member.status = (member.playerPtr != nullptr) ? GuildMemberStatus::Online : GuildMemberStatus::Offline;
                
                members.push_back(std::move(member));
            }
        }
        
        return members;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("InitializeMemberData exception: ") + e.what());
        return {};
    }
}

bool CGuildMemberManager::SetupCommitteeMembers(const std::vector<GuildMemberInfo>& members) {
    try {
        m_committeeMembers.fill(nullptr);
        
        int committeeIndex = 0;
        for (const auto& member : members) {
            if (member.memberClass == GuildMemberClass::Committee && committeeIndex < 3) {
                auto memberIter = m_members.find(member.memberSerial);
                if (memberIter != m_members.end()) {
                    m_committeeMembers[committeeIndex] = memberIter->second;
                    committeeIndex++;
                }
            }
        }
        
        std::cout << "[INFO] Setup " << committeeIndex << " committee members" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("SetupCommitteeMembers exception: ") + e.what());
        return false;
    }
}

bool CGuildMemberManager::CheckMasterElectionEligibility(uint32_t masterSerial) {
    try {
        if (masterSerial == 0 || masterSerial == static_cast<uint32_t>(-1)) {
            return true; // No master, election possible
        }
        
        // Check master last connection time (21 days threshold)
        uint32_t lastConnTime = static_cast<uint32_t>(-1);
        uint32_t thresholdTime = GetConnectTime_AddBySec(-1814400); // 21 days ago
        
        if (CRFWorldDatabase::Select_GuildMasterLastConn(pkDB, masterSerial, thresholdTime, &lastConnTime)) {
            return lastConnTime != static_cast<uint32_t>(-1);
        }
        
        return false;
        
    } catch (const std::exception& e) {
        LogMemberActivity(masterSerial, std::string("CheckMasterElectionEligibility exception: ") + e.what());
        return false;
    }
}

bool CGuildMemberManager::GenerateMemberPackets() {
    try {
        // This would call the legacy packet generation functions
        // CGuild::MakeDownMemberPacket(guild);
        // CGuild::MakeDownApplierPacket(guild);
        // CGuild::MakeQueryInfoPacket(guild);
        // CGuild::MakeMoneyIOPacket(guild);
        // CGuild::MakeBuddyPacket(guild);
        
        std::cout << "[INFO] Generated member packets for guild " << m_guildSerial << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("GenerateMemberPackets exception: ") + e.what());
        return false;
    }
}

bool CGuildMemberManager::ValidateMemberPermissions(uint32_t memberSerial, const std::string& action) {
    try {
        auto member = GetMember(memberSerial);
        if (!member) {
            LogMemberActivity(memberSerial, "ValidateMemberPermissions: Member not found for action: " + action);
            return false;
        }
        
        // Basic permission validation based on action
        if (action == "kick_member" || action == "change_permissions") {
            return member->memberClass >= GuildMemberClass::Committee;
        } else if (action == "change_master" || action == "disband_guild") {
            return member->memberClass == GuildMemberClass::Master;
        }
        
        return true; // Default allow for basic actions
        
    } catch (const std::exception& e) {
        LogMemberActivity(memberSerial, std::string("ValidateMemberPermissions exception: ") + e.what());
        return false;
    }
}

void CGuildMemberManager::LogMemberActivity(uint32_t memberSerial, const std::string& activity) {
    try {
        if (!m_config.enableMemberTracking) {
            return;
        }
        
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        
        std::cout << "[ACTIVITY] " << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S")
                  << " Member " << memberSerial << ": " << activity << std::endl;
        
        // Track consecutive errors
        if (activity.find("exception") != std::string::npos || activity.find("error") != std::string::npos) {
            m_consecutiveErrors.fetch_add(1);
            m_lastErrorTime = std::chrono::steady_clock::now();
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[CRITICAL] Exception in LogMemberActivity: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::UpdateStatistics(const std::string& operation, bool success) {
    try {
        if (!m_config.enableStatistics) {
            return;
        }
        
        if (success) {
            m_consecutiveErrors.store(0); // Reset consecutive errors on success
        }
        
        // Operation-specific statistics are updated in the calling methods
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UpdateStatistics: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyMemberLogin(uint32_t memberSerial, uint16_t mapCode, uint16_t regionIndex) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_memberLoginCallback) {
            try {
                m_memberLoginCallback(memberSerial, mapCode, regionIndex);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in member login callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyMemberLogin: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyMemberLogout(uint32_t memberSerial) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_memberLogoutCallback) {
            try {
                m_memberLogoutCallback(memberSerial);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in member logout callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyMemberLogout: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyMemberJoin(const GuildMemberInfo& member) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_memberJoinCallback) {
            try {
                m_memberJoinCallback(member);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in member join callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyMemberJoin: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyMemberLeave(uint32_t memberSerial, bool selfLeave, bool punishment) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_memberLeaveCallback) {
            try {
                m_memberLeaveCallback(memberSerial, selfLeave, punishment);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in member leave callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyMemberLeave: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyMasterChange(uint32_t oldMaster, uint32_t newMaster) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_masterChangeCallback) {
            try {
                m_masterChangeCallback(oldMaster, newMaster);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in master change callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyMasterChange: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyPermissionChange(uint32_t memberSerial, GuildMemberClass oldClass, GuildMemberClass newClass) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_permissionChangeCallback) {
            try {
                m_permissionChangeCallback(memberSerial, oldClass, newClass);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in permission change callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyPermissionChange: " << e.what() << std::endl;
    }
}

void CGuildMemberManager::NotifyMemberMessage(uint32_t fromMember, const std::string& message) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_memberMessageCallback) {
            try {
                m_memberMessageCallback(fromMember, message);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in member message callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyMemberMessage: " << e.what() << std::endl;
    }
}

} // namespace NexusProtection::Guild

// Legacy C-style wrapper functions for compatibility
extern "C" {
    /**
     * @brief Legacy wrapper for guild setup
     *
     * @param guild Guild pointer
     * @param dwSerial Guild serial
     * @param byGrade Guild grade
     * @param byRace Guild race
     * @param pwszName Guild name
     * @param pwszGreetingMsg Greeting message
     * @param dwEmblemBack Emblem background
     * @param dwEmblemMark Emblem mark
     * @param nNum Number of members
     * @param pEstMember Member array
     * @param dTotalDalant Total dalant
     * @param dTotalGold Total gold
     * @param dwMasterSerial Master serial
     * @param byMasterPrevGrade Master previous grade
     * @param nIOMoneyHisNum Money history count
     * @param pIOMonHisList Money history list
     * @param dwGuildBattleTotalWinCnt Battle wins
     * @param dwGuildBattleTotalDrawCnt Battle draws
     * @param dwGuildBattleTotalLoseCnt Battle losses
     */
    void CGuild_SetGuild(void* guild, uint32_t dwSerial, uint8_t byGrade, uint8_t byRace,
                         const char* pwszName, const char* pwszGreetingMsg,
                         uint32_t dwEmblemBack, uint32_t dwEmblemMark, int nNum,
                         _guild_member_info* pEstMember, double dTotalDalant, double dTotalGold,
                         uint32_t dwMasterSerial, uint8_t byMasterPrevGrade,
                         int nIOMoneyHisNum, _io_money_data* pIOMonHisList,
                         uint32_t dwGuildBattleTotalWinCnt, uint32_t dwGuildBattleTotalDrawCnt,
                         uint32_t dwGuildBattleTotalLoseCnt) {

        using namespace NexusProtection::Guild;

        // Convert to modern setup data
        GuildSetupData setupData;
        setupData.guildSerial = dwSerial;
        setupData.guildGrade = byGrade;
        setupData.guildRace = byRace;
        setupData.guildName = pwszName ? pwszName : "";
        setupData.greetingMessage = pwszGreetingMsg ? pwszGreetingMsg : "";
        setupData.emblemBack = dwEmblemBack;
        setupData.emblemMark = dwEmblemMark;
        setupData.totalDalant = dTotalDalant;
        setupData.totalGold = dTotalGold;
        setupData.battleWins = dwGuildBattleTotalWinCnt;
        setupData.battleDraws = dwGuildBattleTotalDrawCnt;
        setupData.battleLosses = dwGuildBattleTotalLoseCnt;

        // Convert member data
        if (pEstMember && nNum > 0) {
            auto& manager = CGuildMemberManager::GetInstance();
            setupData.members = manager.InitializeMemberData(pEstMember, nNum);
        }

        // Setup master info
        setupData.masterInfo.masterSerial = dwMasterSerial;
        setupData.masterInfo.previousGrade = byMasterPrevGrade;

        // Convert money history
        if (pIOMonHisList && nIOMoneyHisNum > 0) {
            int historyCount = std::min(nIOMoneyHisNum, 100);
            for (int i = 0; i < historyCount; ++i) {
                GuildMoneyTransaction transaction(
                    0.0, // Amount would be extracted from _io_money_data
                    0,   // Type would be extracted from _io_money_data
                    "", // Description would be extracted from _io_money_data
                    0   // Member serial
                );
                setupData.moneyHistory.push_back(transaction);
            }
        }

        // Setup guild
        auto result = CGuildMemberManager::GetInstance().SetupGuild(setupData);
        if (result != GuildMemberResult::Success) {
            std::cerr << "[ERROR] Legacy CGuild_SetGuild failed with code: " << static_cast<int>(result) << std::endl;
        }
    }

    /**
     * @brief Legacy wrapper for member login
     *
     * @param guild Guild pointer
     * @param dwMemberSerial Member serial
     * @param pPtr Player pointer
     * @return Member info pointer or nullptr
     */
    _guild_member_info* CGuild_LoginMember(void* guild, uint32_t dwMemberSerial, CPlayer* pPtr) {
        auto member = NexusProtection::Guild::CGuildMemberManager::GetInstance().LoginMember(dwMemberSerial, pPtr);

        // Note: This would need to return a legacy structure pointer
        // For now, return nullptr as the modern system uses shared_ptr
        return nullptr;
    }

    /**
     * @brief Legacy wrapper for member login notification
     *
     * @param guild Guild pointer
     * @param dwSerial Member serial
     * @param wMapCode Map code
     * @param wRegionIndex Region index
     */
    void CGuild_SendMsg_GuildMemberLogin(void* guild, uint32_t dwSerial, uint16_t wMapCode, uint16_t wRegionIndex) {
        auto result = NexusProtection::Guild::CGuildMemberManager::GetInstance().SendMemberLoginNotification(dwSerial, wMapCode, wRegionIndex);
        if (result != NexusProtection::Guild::GuildMemberResult::Success) {
            std::cerr << "[ERROR] Legacy CGuild_SendMsg_GuildMemberLogin failed with code: " << static_cast<int>(result) << std::endl;
        }
    }

    /**
     * @brief Legacy wrapper for member logout notification
     *
     * @param guild Guild pointer
     * @param dwSerial Member serial
     */
    void CGuild_SendMsg_GuildMemberLogoff(void* guild, uint32_t dwSerial) {
        auto result = NexusProtection::Guild::CGuildMemberManager::GetInstance().SendMemberLogoutNotification(dwSerial);
        if (result != NexusProtection::Guild::GuildMemberResult::Success) {
            std::cerr << "[ERROR] Legacy CGuild_SendMsg_GuildMemberLogoff failed with code: " << static_cast<int>(result) << std::endl;
        }
    }

    /**
     * @brief Legacy wrapper for member leave notification
     *
     * @param guild Guild pointer
     * @param dwMemberSerial Member serial
     * @param bSelf Self leave flag
     * @param bPunish Punishment flag
     */
    void CGuild_SendMsg_LeaveMember(void* guild, uint32_t dwMemberSerial, bool bSelf, bool bPunish) {
        auto result = NexusProtection::Guild::CGuildMemberManager::GetInstance().SendMemberLeaveNotification(dwMemberSerial, bSelf, bPunish);
        if (result != NexusProtection::Guild::GuildMemberResult::Success) {
            std::cerr << "[ERROR] Legacy CGuild_SendMsg_LeaveMember failed with code: " << static_cast<int>(result) << std::endl;
        }
    }

    /**
     * @brief Legacy wrapper for guild master update
     *
     * @param guild Guild pointer
     * @param pNewguildMaster New master info
     * @return true if successful, false otherwise
     */
    bool CGuild_DB_Update_GuildMaster(void* guild, _guild_member_info* pNewguildMaster) {
        if (!pNewguildMaster) {
            return false;
        }

        auto result = NexusProtection::Guild::CGuildMemberManager::GetInstance().UpdateGuildMaster(pNewguildMaster->dwSerial);
        return result == NexusProtection::Guild::GuildMemberResult::Success;
    }

    /**
     * @brief Initialize guild member manager
     *
     * @return true if successful, false otherwise
     */
    bool CGuildMemberManager_Initialize() {
        auto result = NexusProtection::Guild::CGuildMemberManager::GetInstance().Initialize();
        return result == NexusProtection::Guild::GuildMemberResult::Success;
    }

    /**
     * @brief Shutdown guild member manager
     *
     * @return true if successful, false otherwise
     */
    bool CGuildMemberManager_Shutdown() {
        auto result = NexusProtection::Guild::CGuildMemberManager::GetInstance().Shutdown();
        return result == NexusProtection::Guild::GuildMemberResult::Success;
    }

    /**
     * @brief Get member count
     *
     * @return Total number of guild members
     */
    uint32_t CGuildMemberManager_GetMemberCount() {
        return NexusProtection::Guild::CGuildMemberManager::GetInstance().GetMemberCount();
    }

    /**
     * @brief Get online member count
     *
     * @return Number of online guild members
     */
    uint32_t CGuildMemberManager_GetOnlineMemberCount() {
        return NexusProtection::Guild::CGuildMemberManager::GetInstance().GetOnlineMemberCount();
    }

    /**
     * @brief Check member permission
     *
     * @param memberSerial Member serial
     * @param requiredClass Required class (0=Member, 1=Committee, 2=Master)
     * @return true if has permission, false otherwise
     */
    bool CGuildMemberManager_HasPermission(uint32_t memberSerial, uint8_t requiredClass) {
        return NexusProtection::Guild::CGuildMemberManager::GetInstance().HasPermission(
            memberSerial, static_cast<NexusProtection::Guild::GuildMemberClass>(requiredClass));
    }

    /**
     * @brief Reset member statistics
     */
    void CGuildMemberManager_ResetStatistics() {
        NexusProtection::Guild::CGuildMemberManager::GetInstance().ResetStatistics();
    }
}
