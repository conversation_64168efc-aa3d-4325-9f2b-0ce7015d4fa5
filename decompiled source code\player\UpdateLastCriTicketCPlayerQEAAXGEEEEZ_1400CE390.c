/*
 * Function: ?UpdateLastCriTicket@CPlayer@@QEAAXGEEEE@Z
 * Address: 0x1400CE390
 */

void __fastcall CPlayer::UpdateLastCriTicket(CPlayer *this, unsigned __int16 byCurrentYear, char byCurrent<PERSON><PERSON><PERSON>, char byCurrentDay, char byCurrentH<PERSON>, char byNumOfTime)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v8; // eax@5
  __int64 v9; // [sp+0h] [bp-38h]@1
  CPlayer *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  MiningTicket::SetLastCriTicket(
    &v10->m_MinigTicket,
    byCurrentYear,
    byCurrentMonth,
    byCurrentDay,
    byCurrentHour,
    byNumOfTime);
  if ( v10->m_pUserDB )
  {
    v8 = MiningTicket::GetLastCriTicket(&v10->m_MinigTicket);
    CUserDB::Update_TakeLastCriTicket(v10->m_pUserDB, v8);
  }
}
