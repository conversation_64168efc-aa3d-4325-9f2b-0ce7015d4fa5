/*
 * Function: ?Init@_LIST@_UNIT_DB_BASE@@QEAAXE@Z
 * Address: 0x1400764B0
 */

void __fastcall _UNIT_DB_BASE::_LIST::Init(_UNIT_DB_BASE::_LIST *this, char byIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  _UNIT_DB_BASE::_LIST *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (unsigned __int8)byIndex != 255 )
    v5->bySlotIndex = byIndex;
  v5->byFrame = -1;
  memset_0(v5->byPart, 255, 6ui64);
  memset_0(v5->dwBullet, -1, 8ui64);
  memset_0(v5->dwSpare, -1, 0x20ui64);
  v5->dwGauge = 0;
  v5->nPullingFee = 0;
  v5->dwCutTime = 0;
  v5->wBooster = 0;
}
