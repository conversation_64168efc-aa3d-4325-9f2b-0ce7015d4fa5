/*
 * Function: ?Init@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NI@Z
 * Address: 0x1403EB870
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::Init(GUILD_BATTLE::CNormalGuildBattleField *this, unsigned int uiMapInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@5
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@9
  __int64 v8; // rax@11
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@16
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@18
  __int64 v12; // rax@20
  GUILD_BATTLE::CGuildBattleLogger *v13; // rax@23
  GUILD_BATTLE::CGuildBattleLogger *v14; // rax@25
  GUILD_BATTLE::CGuildBattleLogger *v15; // rax@27
  signed __int64 v16; // rax@28
  unsigned __int8 v17; // cf@30
  unsigned __int64 v18; // rax@30
  GUILD_BATTLE::CGuildBattleLogger *v19; // rax@36
  CCircleZone *v20; // rax@39
  GUILD_BATTLE::CGuildBattleLogger *v21; // rax@40
  GUILD_BATTLE::CGuildBattleLogger *v22; // rax@43
  signed __int64 v23; // rax@44
  unsigned __int64 v24; // rax@46
  GUILD_BATTLE::CGuildBattleLogger *v25; // rax@52
  int v26; // eax@55
  CCircleZone *v27; // rcx@55
  GUILD_BATTLE::CGuildBattleLogger *v28; // rax@56
  GUILD_BATTLE::CGuildBattleLogger *v29; // rax@59
  signed __int64 v30; // rax@60
  unsigned __int64 v31; // rax@62
  GUILD_BATTLE::CGuildBattleLogger *v32; // rax@68
  GUILD_BATTLE::CGuildBattleLogger *v33; // rax@72
  __int64 v34; // rax@75
  GUILD_BATTLE::CGuildBattleLogger *v35; // rax@78
  __int64 v36; // [sp+0h] [bp-488h]@1
  DWORD nSize[2]; // [sp+20h] [bp-468h]@5
  LPCSTR lpFileName; // [sp+28h] [bp-460h]@5
  int v39; // [sp+30h] [bp-458h]@40
  char ReturnedString; // [sp+50h] [bp-438h]@4
  char Dst; // [sp+170h] [bp-318h]@4
  char v42; // [sp+290h] [bp-1F8h]@4
  int v43; // [sp+394h] [bp-F4h]@4
  int iNth; // [sp+398h] [bp-F0h]@37
  _dummy_position *v45; // [sp+3A8h] [bp-E0h]@13
  _dummy_position *v46; // [sp+3B0h] [bp-D8h]@10
  _dummy_position *v47; // [sp+3B8h] [bp-D0h]@22
  _dummy_position *v48; // [sp+3C0h] [bp-C8h]@19
  int count[2]; // [sp+3C8h] [bp-C0h]@28
  CCircleZone *v50; // [sp+3D0h] [bp-B8h]@35
  void *v51; // [sp+3D8h] [bp-B0h]@32
  int v52[2]; // [sp+3E0h] [bp-A8h]@44
  CCircleZone *v53; // [sp+3E8h] [bp-A0h]@51
  void *v54; // [sp+3F0h] [bp-98h]@48
  int v55[2]; // [sp+3F8h] [bp-90h]@60
  CGravityStoneRegener *v56; // [sp+400h] [bp-88h]@67
  void *v57; // [sp+408h] [bp-80h]@64
  CGravityStone *v58; // [sp+410h] [bp-78h]@77
  CGravityStone *v59; // [sp+418h] [bp-70h]@74
  __int64 v60; // [sp+420h] [bp-68h]@4
  _dummy_position *v61; // [sp+428h] [bp-60h]@11
  _dummy_position *v62; // [sp+430h] [bp-58h]@20
  CCircleZone *v63; // [sp+438h] [bp-50h]@33
  int v64; // [sp+440h] [bp-48h]@40
  CCircleZone *v65; // [sp+448h] [bp-40h]@49
  int v66; // [sp+450h] [bp-38h]@56
  CGravityStoneRegener *v67; // [sp+458h] [bp-30h]@65
  int v68; // [sp+460h] [bp-28h]@72
  CGravityStone *v69; // [sp+468h] [bp-20h]@75
  unsigned __int64 v70; // [sp+470h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v71; // [sp+490h] [bp+8h]@1
  DWORD uiMapInxa; // [sp+498h] [bp+10h]@1

  uiMapInxa = uiMapInx;
  v71 = this;
  v2 = &v36;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v60 = -2i64;
  v70 = (unsigned __int64)&v36 ^ _security_cookie;
  memset_0(&Dst, 0, 0xFFui64);
  memset_0(&ReturnedString, 0, 0xFFui64);
  memset_0(&v42, 0, 0xFFui64);
  v43 = 0;
  sprintf(&Dst, "Map%d", uiMapInxa);
  ReturnedString = 0;
  GetPrivateProfileStringA(&Dst, "Name", "X", &ReturnedString, 0xFFu, "./Initialize/NormalGuildBattle.ini");
  if ( ReturnedString == 88 )
  {
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    lpFileName = "./Initialize/NormalGuildBattle.ini";
    *(_QWORD *)nSize = &ReturnedString;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v4,
      "CNormalGuildBattleField::Init( %u )GetPrivateProfileString( %s, Name, X, %s, 255, %s ) Fail",
      uiMapInxa,
      &Dst);
    result = 0;
  }
  else
  {
    v71->m_pkMap = CMapOperation::GetMap(&g_MapOper, &ReturnedString);
    if ( v71->m_pkMap )
    {
      ReturnedString = 0;
      GetPrivateProfileStringA(
        &Dst,
        "1PStartPosDummyName",
        "X",
        &ReturnedString,
        0xFFu,
        "./Initialize/NormalGuildBattle.ini");
      if ( ReturnedString == 88 )
      {
        v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        lpFileName = "./Initialize/NormalGuildBattle.ini";
        *(_QWORD *)nSize = &ReturnedString;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v7,
          "CNormalGuildBattleField::Init( %u )GetPrivateProfileString( %s, 1PStartPosDummyName, X, %s, 255, %s ) Fail!",
          uiMapInxa,
          &Dst);
        result = 0;
      }
      else
      {
        v46 = (_dummy_position *)operator new(0x9Cui64);
        if ( v46 )
        {
          _dummy_position::_dummy_position(v46);
          v61 = (_dummy_position *)v8;
        }
        else
        {
          v61 = 0i64;
        }
        v45 = v61;
        v71->m_pkStartPos[0] = v61;
        if ( v71->m_pkStartPos[0] )
        {
          if ( CMapData::LoadDummy(v71->m_pkMap, &ReturnedString, v71->m_pkStartPos[0]) )
          {
            ReturnedString = 0;
            GetPrivateProfileStringA(
              &Dst,
              "2PStartPosDummyName",
              "X",
              &ReturnedString,
              0xFFu,
              "./Initialize/NormalGuildBattle.ini");
            if ( ReturnedString == 88 )
            {
              v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
              lpFileName = "./Initialize/NormalGuildBattle.ini";
              *(_QWORD *)nSize = &ReturnedString;
              GUILD_BATTLE::CGuildBattleLogger::Log(
                v11,
                "CNormalGuildBattleField::Init( %u )GetPrivateProfileString( %s, 2PStartPosDummyName, X, %s, 255, %s ) Fail!",
                uiMapInxa,
                &Dst);
              result = 0;
            }
            else
            {
              v48 = (_dummy_position *)operator new(0x9Cui64);
              if ( v48 )
              {
                _dummy_position::_dummy_position(v48);
                v62 = (_dummy_position *)v12;
              }
              else
              {
                v62 = 0i64;
              }
              v47 = v62;
              v71->m_pkStartPos[1] = v62;
              if ( v71->m_pkStartPos[1] )
              {
                if ( CMapData::LoadDummy(v71->m_pkMap, &ReturnedString, v71->m_pkStartPos[1]) )
                {
                  v71->m_ui1PGoalPosCnt = GetPrivateProfileIntA(
                                            &Dst,
                                            "1PGoalPosCnt",
                                            0,
                                            "./Initialize/NormalGuildBattle.ini");
                  if ( v71->m_ui1PGoalPosCnt )
                  {
                    *(_QWORD *)count = v71->m_ui1PGoalPosCnt;
                    v16 = 1896i64 * *(_QWORD *)count;
                    if ( !is_mul_ok(0x768ui64, *(unsigned __int64 *)count) )
                      v16 = -1i64;
                    v17 = __CFADD__(v16, 8i64);
                    v18 = v16 + 8;
                    if ( v17 )
                      v18 = -1i64;
                    v51 = operator new[](v18);
                    if ( v51 )
                    {
                      *(_DWORD *)v51 = count[0];
                      `eh vector constructor iterator'(
                        (char *)v51 + 8,
                        0x768ui64,
                        count[0],
                        (void (__cdecl *)(void *))CCircleZone::CCircleZone,
                        (void (__cdecl *)(void *))CCircleZone::~CCircleZone);
                      v63 = (CCircleZone *)((char *)v51 + 8);
                    }
                    else
                    {
                      v63 = 0i64;
                    }
                    v50 = v63;
                    v71->m_pk1PGoalZone = v63;
                    if ( !v71->m_pk1PGoalZone )
                    {
                      v19 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                      GUILD_BATTLE::CGuildBattleLogger::Log(
                        v19,
                        "CNormalGuildBattleField::Init( %u )new CCircleZone[%u]",
                        uiMapInxa,
                        v71->m_ui1PGoalPosCnt);
                    }
                    for ( iNth = 0; iNth < v71->m_ui1PGoalPosCnt; ++iNth )
                    {
                      v20 = &v71->m_pk1PGoalZone[iNth];
                      lpFileName = (LPCSTR)v71->m_pkMap;
                      if ( !CCircleZone::Init(v20, uiMapInxa, 1, iNth, iNth, (CMapData *)lpFileName) )
                      {
                        v64 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v71);
                        v21 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                        v39 = v64;
                        LODWORD(lpFileName) = iNth;
                        nSize[0] = uiMapInxa;
                        GUILD_BATTLE::CGuildBattleLogger::Log(
                          v21,
                          "CNormalGuildBattleField::Init( %u )m_pk1PGoalZone[%d].Init( %u, %u, %d )",
                          uiMapInxa,
                          (unsigned int)iNth);
                      }
                    }
                    v71->m_ui2PGoalPosCnt = GetPrivateProfileIntA(
                                              &Dst,
                                              "2PGoalPosCnt",
                                              0,
                                              "./Initialize/NormalGuildBattle.ini");
                    if ( v71->m_ui2PGoalPosCnt )
                    {
                      *(_QWORD *)v52 = v71->m_ui2PGoalPosCnt;
                      v23 = 1896i64 * *(_QWORD *)v52;
                      if ( !is_mul_ok(0x768ui64, *(unsigned __int64 *)v52) )
                        v23 = -1i64;
                      v17 = __CFADD__(v23, 8i64);
                      v24 = v23 + 8;
                      if ( v17 )
                        v24 = -1i64;
                      v54 = operator new[](v24);
                      if ( v54 )
                      {
                        *(_DWORD *)v54 = v52[0];
                        `eh vector constructor iterator'(
                          (char *)v54 + 8,
                          0x768ui64,
                          v52[0],
                          (void (__cdecl *)(void *))CCircleZone::CCircleZone,
                          (void (__cdecl *)(void *))CCircleZone::~CCircleZone);
                        v65 = (CCircleZone *)((char *)v54 + 8);
                      }
                      else
                      {
                        v65 = 0i64;
                      }
                      v53 = v65;
                      v71->m_pk2PGoalZone = v65;
                      if ( !v71->m_pk2PGoalZone )
                      {
                        v25 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                        GUILD_BATTLE::CGuildBattleLogger::Log(
                          v25,
                          "CNormalGuildBattleField::Init( %u )new CCircleZone[%u]",
                          uiMapInxa,
                          v71->m_ui2PGoalPosCnt);
                      }
                      for ( iNth = 0; iNth < v71->m_ui2PGoalPosCnt; ++iNth )
                      {
                        v26 = v71->m_ui1PGoalPosCnt + iNth;
                        v27 = &v71->m_pk2PGoalZone[iNth];
                        lpFileName = (LPCSTR)v71->m_pkMap;
                        if ( !CCircleZone::Init(v27, uiMapInxa, 2, iNth, v26, (CMapData *)lpFileName) )
                        {
                          v66 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v71);
                          v28 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                          v39 = v66;
                          LODWORD(lpFileName) = iNth;
                          nSize[0] = uiMapInxa;
                          GUILD_BATTLE::CGuildBattleLogger::Log(
                            v28,
                            "CNormalGuildBattleField::Init( %u )m_pk2PGoalZone[%d].Init( %u, %u, %d )",
                            uiMapInxa,
                            (unsigned int)iNth);
                        }
                      }
                      v71->m_uiRegenPosCnt = GetPrivateProfileIntA(
                                               &Dst,
                                               "BallRegenPosCnt",
                                               0,
                                               "./Initialize/NormalGuildBattle.ini");
                      if ( v71->m_uiRegenPosCnt )
                      {
                        *(_QWORD *)v55 = v71->m_uiRegenPosCnt;
                        v30 = 1888i64 * *(_QWORD *)v55;
                        if ( !is_mul_ok(0x760ui64, *(unsigned __int64 *)v55) )
                          v30 = -1i64;
                        v17 = __CFADD__(v30, 8i64);
                        v31 = v30 + 8;
                        if ( v17 )
                          v31 = -1i64;
                        v57 = operator new[](v31);
                        if ( v57 )
                        {
                          *(_DWORD *)v57 = v55[0];
                          `eh vector constructor iterator'(
                            (char *)v57 + 8,
                            0x760ui64,
                            v55[0],
                            (void (__cdecl *)(void *))CGravityStoneRegener::CGravityStoneRegener,
                            (void (__cdecl *)(void *))CGravityStoneRegener::~CGravityStoneRegener);
                          v67 = (CGravityStoneRegener *)((char *)v57 + 8);
                        }
                        else
                        {
                          v67 = 0i64;
                        }
                        v56 = v67;
                        v71->m_pkRegenPos = v67;
                        if ( !v71->m_pkRegenPos )
                        {
                          v32 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                          GUILD_BATTLE::CGuildBattleLogger::Log(
                            v32,
                            "CNormalGuildBattleField::Init( %u )new GravityStoneRegener[%u]",
                            uiMapInxa,
                            v71->m_uiRegenPosCnt);
                        }
                        for ( iNth = 0; iNth < v71->m_uiRegenPosCnt; ++iNth )
                        {
                          if ( !CGravityStoneRegener::Init(&v71->m_pkRegenPos[iNth], uiMapInxa, iNth, v71->m_pkMap) )
                          {
                            v68 = GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v71);
                            v33 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                            LODWORD(lpFileName) = v68;
                            nSize[0] = iNth;
                            GUILD_BATTLE::CGuildBattleLogger::Log(
                              v33,
                              "CNormalGuildBattleField::Init( %u )m_pkRegenPos[%d].Init( %u, %d )",
                              uiMapInxa,
                              (unsigned int)iNth);
                          }
                        }
                        v59 = (CGravityStone *)operator new(0xD0ui64);
                        if ( v59 )
                        {
                          CGravityStone::CGravityStone(v59, uiMapInxa);
                          v69 = (CGravityStone *)v34;
                        }
                        else
                        {
                          v69 = 0i64;
                        }
                        v58 = v69;
                        v71->m_pkBall = v69;
                        if ( v71->m_pkBall )
                        {
                          v71->m_uiMapInx = uiMapInxa;
                          v71->m_bInit = 1;
                          result = 1;
                        }
                        else
                        {
                          v35 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                          GUILD_BATTLE::CGuildBattleLogger::Log(
                            v35,
                            "CNormalGuildBattleField::Init( %u )NULL == new CGravityStone",
                            uiMapInxa);
                          result = 0;
                        }
                      }
                      else
                      {
                        v29 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                        *(_QWORD *)nSize = "./Initialize/NormalGuildBattle.ini";
                        GUILD_BATTLE::CGuildBattleLogger::Log(
                          v29,
                          "CNormalGuildBattleField::Init( %u ) : GetPrivateProfileInt( %s, BallRegenPosCnt, 0, %s ) is 0 !",
                          uiMapInxa,
                          &Dst);
                        result = 0;
                      }
                    }
                    else
                    {
                      v22 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                      *(_QWORD *)nSize = "./Initialize/NormalGuildBattle.ini";
                      GUILD_BATTLE::CGuildBattleLogger::Log(
                        v22,
                        "CNormalGuildBattleField::Init( %u ) : GetPrivateProfileInt( %s, 2PGoalPosCnt, 0, %s ) is 0 !",
                        uiMapInxa,
                        &Dst);
                      result = 0;
                    }
                  }
                  else
                  {
                    v15 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                    *(_QWORD *)nSize = "./Initialize/NormalGuildBattle.ini";
                    GUILD_BATTLE::CGuildBattleLogger::Log(
                      v15,
                      "CNormalGuildBattleField::Init( %u ) : GetPrivateProfileInt( %s, 1PGoalPosCnt, 0, %s ) is 0 !",
                      uiMapInxa,
                      &Dst);
                    result = 0;
                  }
                }
                else
                {
                  v14 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                  GUILD_BATTLE::CGuildBattleLogger::Log(
                    v14,
                    "CNormalGuildBattleField::Init( %u )m_pkMap->LoadDummy()",
                    uiMapInxa);
                  result = 0;
                }
              }
              else
              {
                v13 = GUILD_BATTLE::CGuildBattleLogger::Instance();
                GUILD_BATTLE::CGuildBattleLogger::Log(
                  v13,
                  "CNormalGuildBattleField::Init( %u )new _dummy_position",
                  uiMapInxa);
                result = 0;
              }
            }
          }
          else
          {
            v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v10,
              "CNormalGuildBattleField::Init( %u )m_pkMap->LoadDummy( %s, ... )",
              uiMapInxa,
              &ReturnedString);
            result = 0;
          }
        }
        else
        {
          v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v9,
            "CNormalGuildBattleField::Init( %u )NULL == g_MapOper.GetMap( %s )",
            uiMapInxa,
            &ReturnedString);
          result = 0;
        }
      }
    }
    else
    {
      v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v6,
        "CNormalGuildBattleField::Init( %u )NULL == g_MapOper.GetMap( %s )",
        uiMapInxa,
        &ReturnedString);
      result = 0;
    }
  }
  return result;
}
