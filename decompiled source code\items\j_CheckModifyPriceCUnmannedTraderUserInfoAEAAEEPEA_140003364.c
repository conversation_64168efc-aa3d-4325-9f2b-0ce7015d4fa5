/*
 * Function: j_?CheckModifyPrice@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_adjust_price_request_clzo@@AEAKPEAVCLogFile@@PEAK@Z
 * Address: 0x140003364
 */

char __fastcall CUnmannedTraderUserInfo::CheckModifyPrice(CUnmannedTraderUserInfo *this, char byType, _a_trade_adjust_price_request_clzo *pRequest, unsigned int *dwOldPrice, CLogFile *pkLogger, unsigned int *pdwTax)
{
  return CUnmannedTraderUserInfo::CheckModifyPrice(this, byType, pRequest, dwOldPrice, pkLogger, pdwTax);
}
