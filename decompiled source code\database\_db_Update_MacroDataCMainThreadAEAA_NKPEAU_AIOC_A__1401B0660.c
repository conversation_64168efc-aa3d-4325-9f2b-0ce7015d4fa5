/*
 * Function: ?_db_Update_MacroData@CMainThread@@AEAA_NKPEAU_AIOC_A_MACRODATA@@0@Z
 * Address: 0x1401B0660
 */

bool __fastcall CMainThread::_db_Update_MacroData(CMainThread *this, unsigned int dwSerial, _AIOC_A_MACRODATA *pMacro, _AIOC_A_MACRODATA *pOldMacro)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@7
  int j; // [sp+24h] [bp-14h]@7
  int k; // [sp+28h] [bp-10h]@7
  CMainThread *v11; // [sp+40h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+48h] [bp+10h]@1
  _AIOC_A_MACRODATA *pMacroa; // [sp+50h] [bp+18h]@1
  _AIOC_A_MACRODATA *v14; // [sp+58h] [bp+20h]@1

  v14 = pOldMacro;
  pMacroa = pMacro;
  dwSeriala = dwSerial;
  v11 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pMacro )
  {
    if ( !pOldMacro )
      goto LABEL_39;
    v8 = 1;
    k = 0;
    for ( j = 0; j < 1; ++j )
    {
      for ( k = 0; k < 3; ++k )
      {
        if ( pMacro->mcr_Potion[j].Potion[k] != pOldMacro->mcr_Potion[j].Potion[k]
          || pMacro->mcr_Potion[j].PotionValue[k] != pOldMacro->mcr_Potion[j].PotionValue[k] )
        {
          v8 = 0;
          break;
        }
      }
    }
    for ( j = 0; j < 3 && v8 == 1; ++j )
    {
      for ( k = 0; k < 10; ++k )
      {
        if ( pMacro->mcr_Action[j].Action[k] != pOldMacro->mcr_Action[j].Action[k] )
        {
          v8 = 0;
          break;
        }
      }
    }
    for ( j = 0; j < 2 && v8 == 1; ++j )
    {
      for ( k = 0; k < 5; ++k )
      {
        if ( strcmp_0(
               (const char *)&pMacroa->mcr_Chat[j] + 256 * (signed __int64)k,
               (const char *)&v14->mcr_Chat[j] + 256 * (signed __int64)k) )
        {
          v8 = 0;
          break;
        }
      }
    }
    if ( v8 == 1 )
      result = 1;
    else
LABEL_39:
      result = CRFWorldDatabase::Update_MacroData(v11->m_pWorldDB, dwSeriala, pMacroa) != 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
