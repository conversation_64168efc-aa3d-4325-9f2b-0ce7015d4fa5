/*
 * Function: ?ct_expire_pcbang@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298DC0
 */

char __fastcall ct_expire_pcbang(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5 && v5->m_bOper )
  {
    CPlayer::Billing_Logout(v5);
    v5->m_pUserDB->m_BillingInfo.iType = 1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
