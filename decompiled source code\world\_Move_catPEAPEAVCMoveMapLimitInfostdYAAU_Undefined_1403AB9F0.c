/*
 * Function: ??$_Move_cat@PEAPEAVCMoveMapLimitInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@@Z
 * Address: 0x1403AB9F0
 */

char __fastcall std::_Move_cat<CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+24h] [bp-24h]@4

  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return v5;
}
