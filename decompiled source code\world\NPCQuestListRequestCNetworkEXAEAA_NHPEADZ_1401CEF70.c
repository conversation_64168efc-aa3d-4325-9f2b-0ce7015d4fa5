/*
 * Function: ?NPCQuestListRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CEF70
 */

char __fastcall CNetworkEX::NPCQuestListRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  CItemStoreManager *v6; // rax@8
  int v7; // eax@8
  CItemStoreManager *v8; // rax@10
  __int64 v9; // [sp+0h] [bp-58h]@1
  unsigned int *v10; // [sp+20h] [bp-38h]@4
  CPlayer *v11; // [sp+28h] [bp-30h]@4
  CItemStore *pStore; // [sp+30h] [bp-28h]@10
  CMapItemStoreList *v13; // [sp+38h] [bp-20h]@10
  int nSerial; // [sp+40h] [bp-18h]@10

  v3 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned int *)pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    v6 = CItemStoreManager::Instance();
    v7 = CRecordData::GetRecordNum(&v6->m_tblItemStore);
    if ( *v10 < v7 )
    {
      pStore = 0i64;
      nSerial = (unsigned __int8)CMapData::GetMapCode(v11->m_pCurMap);
      v8 = CItemStoreManager::Instance();
      v13 = CItemStoreManager::GetMapItemStoreListBySerial(v8, nSerial);
      if ( v13 )
      {
        pStore = CMapItemStoreList::GetItemStoreFromRecIndex(v13, *v10);
        if ( pStore )
        {
          CPlayer::pc_RequestQuestListFromNPC(v11, pStore);
          result = 1;
        }
        else
        {
          result = 1;
        }
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 1;
    }
  }
  return result;
}
