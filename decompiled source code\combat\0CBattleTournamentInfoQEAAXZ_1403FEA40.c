/*
 * Function: ??0CBattleTournamentInfo@@QEAA@XZ
 * Address: 0x1403FEA40
 */

void __fastcall CBattleTournamentInfo::CBattleTournamentInfo(CBattleTournamentInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CBattleTournamentInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(
    v4->m_WinnerInfo,
    0x18ui64,
    48,
    (void *(__cdecl *)(void *))TournamentWinner::TournamentWinner);
  CBattleTournamentInfo::Init(v4);
}
