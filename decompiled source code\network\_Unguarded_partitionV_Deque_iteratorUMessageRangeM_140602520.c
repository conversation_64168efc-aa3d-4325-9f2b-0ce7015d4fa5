/*
 * Function: ??$_Unguarded_partition@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@@std@@YA?AU?$pair@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@V12@@0@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0@Z
 * Address: 0x140602520
 */

__int64 __fastcall std::_Unguarded_partition<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(__int64 a1, __int64 a2, __int64 a3)
{
  signed __int64 v3; // rax@1
  __int64 v4; // rax@1
  __int64 v5; // rax@3
  __int64 v6; // rax@3
  __int64 v7; // rax@3
  __int64 v8; // rax@4
  __int64 v9; // rax@4
  __int64 v10; // rax@4
  __int64 v11; // rax@14
  __int64 v12; // rax@14
  __int64 v13; // rax@15
  __int64 v14; // rax@15
  __int64 v15; // rax@19
  __int64 v16; // rax@19
  __int64 v17; // rax@20
  __int64 v18; // rax@20
  __int64 v19; // rax@21
  __int64 v20; // rax@24
  __int64 v21; // rax@24
  __int64 v22; // rax@24
  __int64 v23; // rax@25
  __int64 v24; // rax@25
  __int64 v25; // rax@25
  __int64 v26; // rax@26
  __int64 v28; // rax@34
  __int64 v29; // rax@34
  __int64 v30; // rax@36
  __int64 v31; // rax@36
  __int64 v32; // rax@39
  char v33; // [sp+20h] [bp-558h]@1
  char v34; // [sp+40h] [bp-538h]@17
  char v35; // [sp+60h] [bp-518h]@1
  char v36; // [sp+80h] [bp-4F8h]@17
  char v37; // [sp+A0h] [bp-4D8h]@1
  char v38; // [sp+C0h] [bp-4B8h]@1
  char *v39; // [sp+E0h] [bp-498h]@1
  char v40; // [sp+E8h] [bp-490h]@1
  char *v41; // [sp+108h] [bp-470h]@1
  char v42; // [sp+110h] [bp-468h]@1
  char *v43; // [sp+130h] [bp-448h]@1
  char v44; // [sp+138h] [bp-440h]@7
  char v45; // [sp+140h] [bp-438h]@3
  char v46; // [sp+160h] [bp-418h]@4
  char v47; // [sp+180h] [bp-3F8h]@21
  char *v48; // [sp+1A0h] [bp-3D8h]@21
  char v49; // [sp+1A8h] [bp-3D0h]@21
  char *v50; // [sp+1C8h] [bp-3B0h]@21
  char v51; // [sp+1D0h] [bp-3A8h]@24
  char v52; // [sp+1D8h] [bp-3A0h]@24
  char v53; // [sp+1F8h] [bp-380h]@25
  char v54; // [sp+200h] [bp-378h]@25
  char v55; // [sp+220h] [bp-358h]@26
  char *v56; // [sp+240h] [bp-338h]@26
  char v57; // [sp+248h] [bp-330h]@26
  char *v58; // [sp+268h] [bp-310h]@26
  char v59; // [sp+270h] [bp-308h]@33
  char *v60; // [sp+290h] [bp-2E8h]@33
  char v61; // [sp+298h] [bp-2E0h]@33
  char *v62; // [sp+2B8h] [bp-2C0h]@33
  char v63; // [sp+2C0h] [bp-2B8h]@34
  char *v64; // [sp+2E0h] [bp-298h]@34
  char v65; // [sp+2E8h] [bp-290h]@34
  char *v66; // [sp+308h] [bp-270h]@34
  char v67; // [sp+310h] [bp-268h]@37
  char *v68; // [sp+330h] [bp-248h]@37
  char v69; // [sp+338h] [bp-240h]@37
  char *v70; // [sp+358h] [bp-220h]@37
  char v71; // [sp+360h] [bp-218h]@38
  char *v72; // [sp+380h] [bp-1F8h]@38
  char v73; // [sp+388h] [bp-1F0h]@38
  char *v74; // [sp+3A8h] [bp-1D0h]@38
  char v75; // [sp+3B0h] [bp-1C8h]@39
  char *v76; // [sp+3D0h] [bp-1A8h]@39
  char v77; // [sp+3D8h] [bp-1A0h]@39
  char *v78; // [sp+3F8h] [bp-180h]@39
  int v79; // [sp+400h] [bp-178h]@1
  __int64 v80; // [sp+408h] [bp-170h]@1
  __int64 v81; // [sp+410h] [bp-168h]@1
  __int64 v82; // [sp+418h] [bp-160h]@1
  __int64 v83; // [sp+420h] [bp-158h]@1
  __int64 v84; // [sp+428h] [bp-150h]@1
  __int64 v85; // [sp+430h] [bp-148h]@1
  __int64 v86; // [sp+438h] [bp-140h]@3
  __int64 v87; // [sp+440h] [bp-138h]@3
  __int64 v88; // [sp+448h] [bp-130h]@3
  __int64 v89; // [sp+450h] [bp-128h]@4
  __int64 v90; // [sp+458h] [bp-120h]@4
  __int64 v91; // [sp+460h] [bp-118h]@4
  int v92; // [sp+468h] [bp-110h]@5
  __int64 v93; // [sp+470h] [bp-108h]@14
  __int64 v94; // [sp+478h] [bp-100h]@15
  __int64 v95; // [sp+480h] [bp-F8h]@19
  __int64 v96; // [sp+488h] [bp-F0h]@20
  __int64 v97; // [sp+490h] [bp-E8h]@21
  __int64 v98; // [sp+498h] [bp-E0h]@21
  __int64 v99; // [sp+4A0h] [bp-D8h]@21
  __int64 v100; // [sp+4A8h] [bp-D0h]@24
  __int64 v101; // [sp+4B0h] [bp-C8h]@24
  __int64 v102; // [sp+4B8h] [bp-C0h]@24
  __int64 v103; // [sp+4C0h] [bp-B8h]@25
  __int64 v104; // [sp+4C8h] [bp-B0h]@25
  __int64 v105; // [sp+4D0h] [bp-A8h]@25
  __int64 v106; // [sp+4D8h] [bp-A0h]@26
  __int64 v107; // [sp+4E0h] [bp-98h]@26
  __int64 v108; // [sp+4E8h] [bp-90h]@26
  __int64 v109; // [sp+4F0h] [bp-88h]@33
  __int64 v110; // [sp+4F8h] [bp-80h]@33
  __int64 v111; // [sp+500h] [bp-78h]@33
  __int64 v112; // [sp+508h] [bp-70h]@34
  __int64 v113; // [sp+510h] [bp-68h]@34
  __int64 v114; // [sp+518h] [bp-60h]@34
  __int64 v115; // [sp+520h] [bp-58h]@36
  __int64 v116; // [sp+528h] [bp-50h]@37
  __int64 v117; // [sp+530h] [bp-48h]@37
  __int64 v118; // [sp+538h] [bp-40h]@37
  __int64 v119; // [sp+540h] [bp-38h]@38
  __int64 v120; // [sp+548h] [bp-30h]@38
  __int64 v121; // [sp+550h] [bp-28h]@38
  __int64 v122; // [sp+558h] [bp-20h]@39
  __int64 v123; // [sp+560h] [bp-18h]@39
  __int64 v124; // [sp+568h] [bp-10h]@39
  __int64 v125; // [sp+580h] [bp+8h]@1
  __int64 v126; // [sp+588h] [bp+10h]@1
  __int64 v127; // [sp+590h] [bp+18h]@1

  v127 = a3;
  v126 = a2;
  v125 = a1;
  v80 = -2i64;
  v79 = 0;
  LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
    v126,
    &v33,
    v3 / 2);
  v39 = &v38;
  v41 = &v40;
  v43 = &v42;
  LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                  v127,
                  &v38,
                  1i64);
  v81 = v4;
  v82 = v4;
  v83 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v41);
  v84 = v83;
  v85 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v43);
  std::_Median<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
    v85,
    v84,
    v82);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v35);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator+(
    &v35,
    &v37,
    1i64);
  while ( 1 )
  {
    v92 = (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator<(
                             v126,
                             &v35)
       && (LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(),
           v86 = v5,
           LODWORD(v6) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                           &v35,
                           &v45,
                           1i64),
           v87 = v6,
           v88 = v6,
           v79 |= 1u,
           LODWORD(v7) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(),
           !(unsigned __int8)CryptoPP::MeterFilter::MessageRange::operator<(v7, v86))
       && (LODWORD(v8) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                           &v35,
                           &v46,
                           1i64),
           v89 = v8,
           v90 = v8,
           v79 |= 2u,
           LODWORD(v9) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(),
           v91 = v9,
           LODWORD(v10) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(),
           !(unsigned __int8)CryptoPP::MeterFilter::MessageRange::operator<(v10, v91));
    v44 = v92;
    if ( v79 & 2 )
    {
      v79 &= 0xFFFFFFFD;
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    }
    if ( v79 & 1 )
    {
      v79 &= 0xFFFFFFFE;
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    }
    if ( !v44 )
      break;
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v35);
  }
  while ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator<(
                             &v37,
                             v127) )
  {
    LODWORD(v11) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    v93 = v11;
    LODWORD(v12) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    if ( (unsigned __int8)CryptoPP::MeterFilter::MessageRange::operator<(v12, v93) )
      break;
    LODWORD(v13) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    v94 = v13;
    LODWORD(v14) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    if ( (unsigned __int8)CryptoPP::MeterFilter::MessageRange::operator<(v14, v94) )
      break;
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(&v37);
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v34);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v36);
  while ( 1 )
  {
    while ( 1 )
    {
      if ( !(unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator<(
                               &v34,
                               v127) )
        goto LABEL_23;
      LODWORD(v15) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
      v95 = v15;
      LODWORD(v16) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
      if ( !(unsigned __int8)CryptoPP::MeterFilter::MessageRange::operator<(v16, v95) )
        break;
LABEL_22:
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(&v34);
    }
    LODWORD(v17) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    v96 = v17;
    LODWORD(v18) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
    if ( !(unsigned __int8)CryptoPP::MeterFilter::MessageRange::operator<(v18, v96) )
    {
      v48 = &v47;
      v50 = &v49;
      v97 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v47);
      v98 = v97;
      LODWORD(v19) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(
                       &v37,
                       v50,
                       0i64);
      v99 = v19;
      std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
        v19,
        v98);
      goto LABEL_22;
    }
LABEL_23:
    while ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator<(
                               v126,
                               &v36) )
    {
      LODWORD(v20) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
      v100 = v20;
      LODWORD(v21) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                       &v36,
                       &v52,
                       1i64);
      v101 = v21;
      v102 = v21;
      LODWORD(v22) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
      v51 = CryptoPP::MeterFilter::MessageRange::operator<(v22, v100);
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
      if ( !v51 )
      {
        LODWORD(v23) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                         &v36,
                         &v54,
                         1i64);
        v103 = v23;
        v104 = v23;
        LODWORD(v24) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
        v105 = v24;
        LODWORD(v25) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
        v53 = CryptoPP::MeterFilter::MessageRange::operator<(v25, v105);
        std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
        if ( v53 )
          break;
        v56 = &v55;
        v58 = &v57;
        LODWORD(v26) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
                         &v36,
                         &v55,
                         1i64);
        v106 = v26;
        v107 = v26;
        std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v35);
        v108 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v58);
        std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
          v108,
          v107);
      }
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v36);
    }
    if ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator==(
                            &v36,
                            v126) )
    {
      if ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator==(
                              &v34,
                              v127) )
        break;
    }
    if ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator==(
                            &v36,
                            v126) )
    {
      if ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator!=(
                              &v37,
                              &v34) )
      {
        v60 = &v59;
        v62 = &v61;
        v109 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v59);
        v110 = v109;
        v111 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v62);
        std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
          v111,
          v110);
      }
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(&v37);
      v64 = &v63;
      v66 = &v65;
      LODWORD(v28) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(
                       &v34,
                       &v63,
                       0i64);
      v112 = v28;
      v113 = v28;
      LODWORD(v29) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(
                       &v35,
                       v66,
                       0i64);
      v114 = v29;
      std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
        v29,
        v113);
    }
    else if ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator==(
                                 &v34,
                                 v127) )
    {
      LODWORD(v30) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v35);
      v115 = v30;
      LODWORD(v31) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v36);
      if ( (unsigned __int8)std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator!=(
                              v31,
                              v115) )
      {
        v68 = &v67;
        v70 = &v69;
        v116 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v67);
        v117 = v116;
        v118 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v70);
        std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
          v118,
          v117);
      }
      v72 = &v71;
      v74 = &v73;
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v37);
      v119 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v72);
      v120 = v119;
      v121 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v74);
      std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
        v121,
        v120);
    }
    else
    {
      v76 = &v75;
      v78 = &v77;
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--(&v36);
      v122 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v76);
      v123 = v122;
      LODWORD(v32) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++(
                       &v34,
                       v78,
                       0i64);
      v124 = v32;
      std::iter_swap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
        v32,
        v123);
    }
  }
  std::pair<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>::pair<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
    v125,
    &v35,
    &v37);
  v79 |= 4u;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return v125;
}
