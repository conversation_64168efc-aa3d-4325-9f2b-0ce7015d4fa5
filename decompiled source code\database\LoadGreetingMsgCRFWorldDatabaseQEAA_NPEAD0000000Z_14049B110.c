/*
 * Function: ?LoadGreetingMsg@CRFWorldDatabase@@QEAA_NPEAD0000000@Z
 * Address: 0x14049B110
 */

char __fastcall CRFWorldDatabase::LoadGreetingMsg(CRFWorldDatabase *this, char *pwszGMGreetingmsg, char *pwszRaceGreetingmsgA, char *pwszRaceGreetingmsgB, char *pwszRaceGreetingmsgC, char *pwszGMName, char *pwszNameA, char *pwszNameB, char *pwszNameC)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v12; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@21
  char Dest; // [sp+40h] [bp-148h]@4
  char v16; // [sp+41h] [bp-147h]@4
  SQLLEN v17; // [sp+158h] [bp-30h]@21
  __int16 v18; // [sp+164h] [bp-24h]@9
  int v19; // [sp+168h] [bp-20h]@4
  unsigned __int64 v20; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v21; // [sp+190h] [bp+8h]@1
  char *v22; // [sp+198h] [bp+10h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1
  char *v24; // [sp+1A8h] [bp+20h]@1

  v24 = pwszRaceGreetingmsgB;
  TargetValue = pwszRaceGreetingmsgA;
  v22 = pwszGMGreetingmsg;
  v21 = this;
  v9 = &v12;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v20 = (unsigned __int64)&v12 ^ _security_cookie;
  Dest = 0;
  memset(&v16, 0, 0xFFui64);
  v19 = 0;
  sprintf(&Dest, "select GMsg,Name from tbl_GreetMsg where usetype = %d", 0i64);
  if ( v21->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v21->vfptr, &Dest);
  if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
  {
    v18 = SQLExecDirect_0(v21->m_hStmtSelect, &Dest, -3);
    if ( v18 && v18 != 1 )
    {
      if ( v18 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v21->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v18 = SQLFetch_0(v21->m_hStmtSelect);
      if ( v18 && v18 != 1 )
      {
        if ( v18 != 100 )
        {
          SQLStmt = v21->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
        }
        if ( v21->m_hStmtSelect )
          SQLCloseCursor_0(v21->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v17;
        SQLStmt = (void *)256;
        v18 = SQLGetData_0(v21->m_hStmtSelect, 1u, 1, TargetValue, 256i64, &v17);
        StrLen_or_IndPtr = &v17;
        SQLStmt = (void *)17;
        v18 = SQLGetData_0(v21->m_hStmtSelect, 2u, 1, pwszNameA, 17i64, &v17);
        if ( v21->m_hStmtSelect )
          SQLCloseCursor_0(v21->m_hStmtSelect);
        sprintf(&Dest, "select GMsg,Name from tbl_GreetMsg where usetype = %d", 1i64);
        if ( v21->m_bSaveDBLog )
          CRFNewDatabase::Log((CRFNewDatabase *)&v21->vfptr, &Dest);
        if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
        {
          v18 = SQLExecDirect_0(v21->m_hStmtSelect, &Dest, -3);
          if ( v18 && v18 != 1 )
          {
            if ( v18 == 100 )
            {
              result = 0;
            }
            else
            {
              SQLStmt = v21->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLExecDirect", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
              result = 0;
            }
          }
          else
          {
            v18 = SQLFetch_0(v21->m_hStmtSelect);
            if ( v18 && v18 != 1 )
            {
              if ( v18 != 100 )
              {
                SQLStmt = v21->m_hStmtSelect;
                CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLFetch", SQLStmt);
                CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
              }
              if ( v21->m_hStmtSelect )
                SQLCloseCursor_0(v21->m_hStmtSelect);
              result = 0;
            }
            else
            {
              StrLen_or_IndPtr = &v17;
              SQLStmt = (void *)256;
              v18 = SQLGetData_0(v21->m_hStmtSelect, 1u, 1, v24, 256i64, &v17);
              StrLen_or_IndPtr = &v17;
              SQLStmt = (void *)17;
              v18 = SQLGetData_0(v21->m_hStmtSelect, 2u, 1, pwszNameB, 17i64, &v17);
              if ( v21->m_hStmtSelect )
                SQLCloseCursor_0(v21->m_hStmtSelect);
              sprintf(&Dest, "select GMsg,Name from tbl_GreetMsg where usetype = %d", 2i64);
              if ( v21->m_bSaveDBLog )
                CRFNewDatabase::Log((CRFNewDatabase *)&v21->vfptr, &Dest);
              if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
              {
                v18 = SQLExecDirect_0(v21->m_hStmtSelect, &Dest, -3);
                if ( v18 && v18 != 1 )
                {
                  if ( v18 == 100 )
                  {
                    result = 0;
                  }
                  else
                  {
                    SQLStmt = v21->m_hStmtSelect;
                    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLExecDirect", SQLStmt);
                    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
                    result = 0;
                  }
                }
                else
                {
                  v18 = SQLFetch_0(v21->m_hStmtSelect);
                  if ( v18 && v18 != 1 )
                  {
                    if ( v18 != 100 )
                    {
                      SQLStmt = v21->m_hStmtSelect;
                      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLFetch", SQLStmt);
                      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
                    }
                    if ( v21->m_hStmtSelect )
                      SQLCloseCursor_0(v21->m_hStmtSelect);
                    result = 0;
                  }
                  else
                  {
                    StrLen_or_IndPtr = &v17;
                    SQLStmt = (void *)256;
                    v18 = SQLGetData_0(v21->m_hStmtSelect, 1u, 1, pwszRaceGreetingmsgC, 256i64, &v17);
                    StrLen_or_IndPtr = &v17;
                    SQLStmt = (void *)17;
                    v18 = SQLGetData_0(v21->m_hStmtSelect, 2u, 1, pwszNameC, 17i64, &v17);
                    if ( v21->m_hStmtSelect )
                      SQLCloseCursor_0(v21->m_hStmtSelect);
                    sprintf(&Dest, "select GMsg,Name from tbl_GreetMsg where usetype = %d", 255i64);
                    if ( v21->m_bSaveDBLog )
                      CRFNewDatabase::Log((CRFNewDatabase *)&v21->vfptr, &Dest);
                    if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
                    {
                      v18 = SQLExecDirect_0(v21->m_hStmtSelect, &Dest, -3);
                      if ( v18 && v18 != 1 )
                      {
                        if ( v18 == 100 )
                        {
                          result = 0;
                        }
                        else
                        {
                          SQLStmt = v21->m_hStmtSelect;
                          CRFNewDatabase::ErrorMsgLog(
                            (CRFNewDatabase *)&v21->vfptr,
                            v18,
                            &Dest,
                            "SQLExecDirect",
                            SQLStmt);
                          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
                          result = 0;
                        }
                      }
                      else
                      {
                        v18 = SQLFetch_0(v21->m_hStmtSelect);
                        if ( v18 && v18 != 1 )
                        {
                          if ( v18 != 100 )
                          {
                            SQLStmt = v21->m_hStmtSelect;
                            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v18, &Dest, "SQLFetch", SQLStmt);
                            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v18, v21->m_hStmtSelect);
                          }
                          if ( v21->m_hStmtSelect )
                            SQLCloseCursor_0(v21->m_hStmtSelect);
                          result = 0;
                        }
                        else
                        {
                          StrLen_or_IndPtr = &v17;
                          SQLStmt = (void *)256;
                          v18 = SQLGetData_0(v21->m_hStmtSelect, 1u, 1, v22, 256i64, &v17);
                          StrLen_or_IndPtr = &v17;
                          SQLStmt = (void *)17;
                          v18 = SQLGetData_0(v21->m_hStmtSelect, 2u, 1, pwszGMName, 17i64, &v17);
                          if ( v21->m_hStmtSelect )
                            SQLCloseCursor_0(v21->m_hStmtSelect);
                          if ( v21->m_bSaveDBLog )
                            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v21->vfptr, "%s Success", &Dest);
                          result = 1;
                        }
                      }
                    }
                    else
                    {
                      CRFNewDatabase::ErrFmtLog(
                        (CRFNewDatabase *)&v21->vfptr,
                        "ReConnectDataBase Fail. Query : %s",
                        &Dest);
                      result = 0;
                    }
                  }
                }
              }
              else
              {
                CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
                result = 0;
              }
            }
          }
        }
        else
        {
          CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
