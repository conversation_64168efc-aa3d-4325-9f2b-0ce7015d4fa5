/*
 * Function: _std::_Uninit_move_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::allocator_std::basic_string_char_std::char_traits_char__std::allocator_char________::_1_::catch$0
 * Address: 0x140429C20
 */

void __fastcall __noreturn std::_Uninit_move_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::basic_string_char_std::char_traits_char__std::allocator_char________ptr64_std::allocator_std::basic_string_char_std::char_traits_char__std::allocator_char________::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 192); *(_QWORD *)(i + 32) += 48i64 )
    std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>::destroy(
      *(std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > **)(i + 200),
      *(std::basic_string<char,std::char_traits<char>,std::allocator<char> > **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
