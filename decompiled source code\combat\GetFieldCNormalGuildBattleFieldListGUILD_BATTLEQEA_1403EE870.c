/*
 * Function: ?GetField@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleField@2@EK@Z
 * Address: 0x1403EE870
 */

GUILD_BATTLE::CNormalGuildBattleField *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetField(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace, unsigned int dwMapCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleField *result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@9
  GUILD_BATTLE::CNormalGuildBattleFieldList *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  unsigned int v10; // [sp+50h] [bp+18h]@1

  v10 = dwMapCode;
  v9 = byRace;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    if ( v8->m_byUseFieldCnt[(unsigned __int8)byRace] && v8->m_ppkUseFieldByRace[(unsigned __int8)byRace] )
    {
      for ( j = 0; j < (signed int)v8->m_byUseFieldCnt[(unsigned __int8)v9]; ++j )
      {
        if ( v10 == GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v8->m_ppkUseFieldByRace[(unsigned __int8)v9][j]) )
          return v8->m_ppkUseFieldByRace[(unsigned __int8)v9][j];
      }
      result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
