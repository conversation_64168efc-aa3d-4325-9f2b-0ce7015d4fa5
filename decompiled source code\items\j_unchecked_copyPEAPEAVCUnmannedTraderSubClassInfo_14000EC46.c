/*
 * Function: j_??$unchecked_copy@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@@stdext@@YAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@00@Z
 * Address: 0x14000EC46
 */

CUnmannedTraderSubClassInfo **__fastcall stdext::unchecked_copy<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo **_Dest)
{
  return stdext::unchecked_copy<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(_First, _Last, _Dest);
}
