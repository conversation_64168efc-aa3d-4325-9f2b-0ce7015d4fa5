/*
 * Function: ?CompleteUpdateReservedSchedule@CGuildBattleController@@QEAAXKPEAE@Z
 * Address: 0x1403D6F60
 */

void __fastcall CGuildBattleController::CompleteUpdateReservedSchedule(CGuildBattleController *this, unsigned int dwMapID, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  unsigned int dwMapIDa; // [sp+38h] [bp+10h]@1
  char *pLoadDataa; // [sp+40h] [bp+18h]@1

  pLoadDataa = pLoadData;
  dwMapIDa = dwMapID;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Instance();
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowComplete(v5, dwMapIDa, pLoadDataa);
}
