/*
 * CItemManagementSystem.h - Modern Item Management System
 * Refactored from decompiled C item management functions
 * Provides comprehensive item creation, looting, and inventory management
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <optional>

// Forward declarations
class CPlayer;
class CMapData;
class CItemBox;
class CMgrAvatorItemHistory;
struct _STORAGE_LIST;
struct _base_fld;
struct _ItemUpgrade_fld;
struct _TimeItem_fld;

namespace NexusProtection {
namespace Items {

/**
 * Item operation result codes
 */
enum class ItemOperationResult : int {
    Success = 1,
    Failure = 0,
    InvalidItem = -1,
    InvalidPlayer = -2,
    InventoryFull = -3,
    InsufficientQuantity = -4,
    ItemNotFound = -5,
    InvalidUpgrade = -6,
    SystemError = -7
};

/**
 * Item creation context
 */
struct ItemCreationContext {
    std::string itemCode;
    int quantity{1};
    std::string upgradeCode;
    int upgradeLevel{0};
    CPlayer* pOwner{nullptr};
    CMapData* pMap{nullptr};
    uint16_t layerIndex{0};
    float position[3]{0.0f, 0.0f, 0.0f};
    bool hideFromOthers{false};
    
    ItemCreationContext() = default;
    explicit ItemCreationContext(const std::string& code) : itemCode(code) {}
    
    bool IsValid() const {
        return !itemCode.empty() && quantity > 0 && pOwner != nullptr;
    }
};

/**
 * Item information structure
 */
struct ItemInfo {
    uint8_t tableCode{0};
    uint16_t itemIndex{0};
    uint32_t durability{0};
    uint32_t level{0};
    uint64_t uniqueId{0};
    std::string itemCode;
    std::string description;
    std::chrono::system_clock::time_point creationTime;
    std::chrono::system_clock::time_point expirationTime;
    bool isTimeItem{false};
    bool isUpgraded{false};
    
    ItemInfo() : creationTime(std::chrono::system_clock::now()) {}
    
    bool IsValid() const {
        return tableCode > 0 && itemIndex > 0 && !itemCode.empty();
    }
    
    bool IsExpired() const {
        if (!isTimeItem) return false;
        return std::chrono::system_clock::now() > expirationTime;
    }
};

/**
 * Item upgrade information
 */
struct ItemUpgradeInfo {
    std::string upgradeCode;
    uint32_t upgradeIndex{0};
    uint8_t maxUpgradeLevel{0};
    uint8_t currentLevel{0};
    std::vector<uint32_t> upgradeBits;
    
    ItemUpgradeInfo() = default;
    
    bool IsValid() const {
        return !upgradeCode.empty() && upgradeIndex > 0;
    }
    
    bool CanUpgrade() const {
        return currentLevel < maxUpgradeLevel;
    }
};

/**
 * Item operation result with detailed information
 */
struct ItemOperationDetails {
    ItemOperationResult result{ItemOperationResult::Success};
    std::string errorMessage;
    ItemInfo itemInfo;
    CItemBox* pCreatedItemBox{nullptr};
    uint64_t operationId{0};
    std::chrono::milliseconds executionTime{0};
    
    ItemOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == ItemOperationResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case ItemOperationResult::Success: return "Success";
            case ItemOperationResult::Failure: return "General failure";
            case ItemOperationResult::InvalidItem: return "Invalid item";
            case ItemOperationResult::InvalidPlayer: return "Invalid player";
            case ItemOperationResult::InventoryFull: return "Inventory full";
            case ItemOperationResult::InsufficientQuantity: return "Insufficient quantity";
            case ItemOperationResult::ItemNotFound: return "Item not found";
            case ItemOperationResult::InvalidUpgrade: return "Invalid upgrade";
            case ItemOperationResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Item management statistics
 */
struct ItemManagementStats {
    std::atomic<uint64_t> totalItemsCreated{0};
    std::atomic<uint64_t> totalItemsLooted{0};
    std::atomic<uint64_t> totalItemsUpgraded{0};
    std::atomic<uint64_t> totalItemsExpired{0};
    std::atomic<uint64_t> successfulOperations{0};
    std::atomic<uint64_t> failedOperations{0};
    std::chrono::system_clock::time_point lastOperation;
    
    ItemManagementStats() : lastOperation(std::chrono::system_clock::now()) {}
    
    void RecordOperation(bool success) {
        if (success) {
            successfulOperations++;
        } else {
            failedOperations++;
        }
        lastOperation = std::chrono::system_clock::now();
    }
    
    double GetSuccessRate() const {
        uint64_t total = successfulOperations + failedOperations;
        return total > 0 ? static_cast<double>(successfulOperations) / total * 100.0 : 0.0;
    }
};

/**
 * Modern Item Management System
 * Refactored from legacy decompiled C functions
 */
class CItemManagementSystem {
public:
    /**
     * Constructor
     */
    CItemManagementSystem();
    
    /**
     * Destructor
     */
    virtual ~CItemManagementSystem();
    
    /**
     * Create and loot item for player
     * Refactored from: _loot_item_1400BEFC0.c
     * @param context Item creation context
     * @return Detailed operation result
     */
    ItemOperationDetails CreateLootItem(const ItemCreationContext& context);
    
    /**
     * Create item box in world
     * Refactored from: CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166AD0.c
     * @param itemInfo Item information
     * @param context Creation context
     * @return Created item box or nullptr
     */
    CItemBox* CreateItemBox(const ItemInfo& itemInfo, const ItemCreationContext& context);
    
    /**
     * Initialize time item system
     * Refactored from: InitTimeItemQEAA_NXZ_14030E160.c
     * @return true if successful
     */
    bool InitializeTimeItemSystem();
    
    /**
     * Read time item goods data
     * Refactored from: ReadGoodsTimeItemQEAA_NXZ_14030E6B0.c
     * @return true if successful
     */
    bool ReadTimeItemGoods();
    
    /**
     * Validate item code and get table information
     * @param itemCode Item code to validate
     * @return Item table code or -1 if invalid
     */
    int GetItemTableCode(const std::string& itemCode);
    
    /**
     * Get item record data
     * @param tableCode Item table code
     * @param itemCode Item code
     * @return Item record or nullptr
     */
    _base_fld* GetItemRecord(int tableCode, const std::string& itemCode);
    
    /**
     * Calculate item durability
     * @param tableCode Item table code
     * @param itemIndex Item index
     * @return Item durability points
     */
    uint32_t CalculateItemDurability(int tableCode, uint32_t itemIndex);
    
    /**
     * Process item upgrade
     * @param itemInfo Item to upgrade
     * @param upgradeInfo Upgrade information
     * @return Operation result
     */
    ItemOperationDetails ProcessItemUpgrade(ItemInfo& itemInfo, const ItemUpgradeInfo& upgradeInfo);
    
    /**
     * Check if item is overlap type
     * @param tableCode Item table code
     * @return true if overlap item
     */
    bool IsOverlapItem(int tableCode);
    
    /**
     * Get item kind code
     * @param tableCode Item table code
     * @return Item kind code
     */
    uint8_t GetItemKindCode(int tableCode);
    
    /**
     * Get default upgrade socket number
     * @param tableCode Item table code
     * @param itemIndex Item index
     * @return Number of upgrade sockets
     */
    uint8_t GetDefaultUpgradeSocketCount(int tableCode, uint32_t itemIndex);
    
    /**
     * Find time item record
     * @param tableCode Item table code
     * @param itemIndex Item index
     * @return Time item record or nullptr
     */
    _TimeItem_fld* FindTimeItemRecord(int tableCode, uint32_t itemIndex);
    
    /**
     * Get item management statistics
     * @return Current statistics
     */
    const ItemManagementStats& GetStatistics() const { return m_stats; }
    
    /**
     * Reset statistics
     */
    void ResetStatistics();
    
    /**
     * Set operation callback for monitoring
     * @param callback Operation callback function
     */
    void SetOperationCallback(std::function<void(const ItemOperationDetails&)> callback);
    
    /**
     * Enable/disable detailed logging
     * @param bEnable Enable flag
     */
    void SetDetailedLogging(bool bEnable) { m_bDetailedLogging = bEnable; }

protected:
    /**
     * Validate item creation context
     * @param context Context to validate
     * @return true if valid
     */
    virtual bool ValidateCreationContext(const ItemCreationContext& context);
    
    /**
     * Create storage list item
     * @param context Creation context
     * @param itemInfo Item information
     * @return Storage list item
     */
    virtual _STORAGE_LIST* CreateStorageListItem(const ItemCreationContext& context, const ItemInfo& itemInfo);
    
    /**
     * Apply item upgrades
     * @param itemInfo Item information
     * @param upgradeInfo Upgrade information
     * @param upgradeLevel Number of upgrades to apply
     * @return Updated item level
     */
    virtual uint32_t ApplyItemUpgrades(const ItemInfo& itemInfo, const ItemUpgradeInfo& upgradeInfo, int upgradeLevel);
    
    /**
     * Set time item properties
     * @param itemInfo Item information
     * @param timeRecord Time item record
     */
    virtual void SetTimeItemProperties(ItemInfo& itemInfo, const _TimeItem_fld* timeRecord);
    
    /**
     * Log operation details
     * @param details Operation details
     */
    virtual void LogOperation(const ItemOperationDetails& details);

private:
    ItemManagementStats m_stats;
    std::function<void(const ItemOperationDetails&)> m_operationCallback;
    bool m_bDetailedLogging{false};
    mutable std::mutex m_statsMutex;
    std::atomic<uint64_t> m_nextOperationId{1};
    
    /**
     * Create operation result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete operation result
     */
    ItemOperationDetails CreateResult(ItemOperationResult result, 
                                    std::chrono::high_resolution_clock::time_point startTime,
                                    const std::string& errorMessage = "");
    
    /**
     * Generate unique operation ID
     * @return Unique operation ID
     */
    uint64_t GenerateOperationId() { return m_nextOperationId++; }
    
    // Disable copy constructor and assignment operator
    CItemManagementSystem(const CItemManagementSystem&) = delete;
    CItemManagementSystem& operator=(const CItemManagementSystem&) = delete;
};

/**
 * Item history management system
 * Refactored from CMgrAvatorItemHistory functions
 */
class CItemHistoryManager {
public:
    /**
     * Constructor
     */
    CItemHistoryManager();
    
    /**
     * Destructor
     */
    virtual ~CItemHistoryManager();
    
    /**
     * Log lend item deletion from inventory
     * Refactored from: lenditem_del_from_invenCMgrAvatorItemHistoryQEAAXE_140240BD0.c
     * @param tableCode Item table code
     * @param itemIndex Item index
     * @param uniqueId Item unique ID
     * @param fileName Log file name
     */
    void LogLendItemDeletion(uint8_t tableCode, uint16_t itemIndex, uint64_t uniqueId, const std::string& fileName);
    
    /**
     * Log item serial full event
     * Refactored from: item_serial_fullCMgrAvatorItemHistoryQEAA_140237500.c
     * @param serialNumber Serial number
     * @param fileName Log file name
     */
    void LogItemSerialFull(int serialNumber, const std::string& fileName);
    
    /**
     * Log item close event with detailed inventory
     * Refactored from: have_item_closeCMgrAvatorItemHistoryQEAAXHPEADPEAU_140237500.c
     * @param playerName Player name
     * @param fileName Log file name
     */
    void LogItemClose(const std::string& playerName, const std::string& fileName);
    
    /**
     * Write log entry to file
     * @param fileName Log file name
     * @param logData Log data to write
     */
    void WriteLogFile(const std::string& fileName, const std::string& logData);

private:
    std::mutex m_logMutex;
    std::string m_currentDate;
    std::string m_currentTime;
    
    /**
     * Update current date and time
     */
    void UpdateDateTime();
    
    /**
     * Get Korean local time
     * @return Korean local time
     */
    uint32_t GetKoreanLocalTime();
};

/**
 * Legacy compatibility functions
 * Maintain exact signatures for backward compatibility
 */
namespace LegacyCompatibility {
    /**
     * Legacy loot item function wrapper
     * @param pOwner Player owner
     * @param pszItemCode Item code
     * @param nNum Quantity
     * @param pszUpTalCode Upgrade code
     * @param nUpNum Upgrade level
     * @return 1 if successful, 0 if failed
     */
    char loot_item_Legacy(CPlayer* pOwner, char* pszItemCode, int nNum, char* pszUpTalCode, int nUpNum);
    
    /**
     * Legacy create item box function wrapper
     * @param pItem Storage list item
     * @param pOwner Player owner
     * @param dwPartyBossSerial Party boss serial
     * @param bPartyShare Party share flag
     * @param pThrower Thrower character
     * @param byCreateCode Create code
     * @param pMap Map data
     * @param wLayerIndex Layer index
     * @param pStdPos Position
     * @param bHide Hide flag
     * @return Created item box or nullptr
     */
    CItemBox* CreateItemBox_Legacy(_STORAGE_LIST* pItem, CPlayer* pOwner, uint32_t dwPartyBossSerial, 
                                  bool bPartyShare, void* pThrower, uint8_t byCreateCode, 
                                  CMapData* pMap, uint16_t wLayerIndex, float* pStdPos, bool bHide);
    
    /**
     * Legacy time item initialization wrapper
     * @return true if successful, false if failed
     */
    bool InitTimeItem_Legacy();
}

/**
 * Utility functions for item management
 */
namespace ItemManagementUtils {
    /**
     * Convert legacy item code to modern string
     * @param pszItemCode Legacy item code
     * @return Modern string
     */
    std::string ConvertItemCode(const char* pszItemCode);
    
    /**
     * Validate item code format
     * @param itemCode Item code to validate
     * @return true if valid
     */
    bool ValidateItemCode(const std::string& itemCode);
    
    /**
     * Calculate upgrade bit mask
     * @param currentBits Current bit mask
     * @param upgradeIndex Upgrade index
     * @param socketIndex Socket index
     * @return Updated bit mask
     */
    uint32_t CalculateUpgradeBits(uint32_t currentBits, uint32_t upgradeIndex, int socketIndex);
    
    /**
     * Get current time as 32-bit timestamp
     * @return Current timestamp
     */
    uint32_t GetCurrentTime32();
}

} // namespace Items
} // namespace NexusProtection
