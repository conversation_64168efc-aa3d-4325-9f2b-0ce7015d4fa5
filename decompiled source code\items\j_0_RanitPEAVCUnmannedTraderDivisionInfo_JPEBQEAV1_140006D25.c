/*
 * Function: j_??0?$_Ranit@PEAVCUnmannedTraderDivisionInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140006D25
 */

void __fastcall std::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &>::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &>(std::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &> *this, std::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &> *__that)
{
  std::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &>::_Ranit<CUnmannedTraderDivisionInfo *,__int64,CUnmannedTraderDivisionInfo * const *,CUnmannedTraderDivisionInfo * const &>(
    this,
    __that);
}
