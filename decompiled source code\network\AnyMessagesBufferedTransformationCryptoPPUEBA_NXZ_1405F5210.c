/*
 * Function: ?AnyMessages@BufferedTransformation@CryptoPP@@UEBA_NXZ
 * Address: 0x1405F5210
 */

bool __fastcall CryptoPP::BufferedTransformation::AnyMessages(CryptoPP::BufferedTransformation *this)
{
  __int64 v1; // rax@1
  __int64 v2; // rax@2
  bool result; // al@2
  CryptoPP::BufferedTransformation *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  LODWORD(v1) = ((int (*)(void))this->vfptr[20].__vecDelDtor)();
  if ( v1 )
  {
    LODWORD(v2) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))v4->vfptr[20].__vecDelDtor)(v4);
    result = (*(int (__fastcall **)(__int64))(*(_QWORD *)v2 + 192i64))(v2);
  }
  else
  {
    result = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))v4->vfptr[11].Clone)(v4) != 0;
  }
  return result;
}
