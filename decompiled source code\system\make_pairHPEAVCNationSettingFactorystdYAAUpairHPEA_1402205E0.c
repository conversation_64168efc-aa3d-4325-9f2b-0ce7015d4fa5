/*
 * Function: ??$make_pair@HPEAVCNationSettingFactory@@@std@@YA?AU?$pair@HPEAVCNationSettingFactory@@@0@HPEAVCNationSettingFactory@@@Z
 * Address: 0x1402205E0
 */

std::pair<int,CNationSettingFactory *> *__fastcall std::make_pair<int,CNationSettingFactory *>(std::pair<int,CNationSettingFactory *> *result, int _Val1, CNationSettingFactory *_Val2)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  std::pair<int,CNationSettingFactory *> *v7; // [sp+30h] [bp+8h]@1
  int _Val1a; // [sp+38h] [bp+10h]@1
  CNationSettingFactory *_Val2a; // [sp+40h] [bp+18h]@1

  _Val2a = _Val2;
  _Val1a = _Val1;
  v7 = result;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::pair<int,CNationSettingFactory *>::pair<int,CNationSettingFactory *>(v7, &_Val1a, &_Val2a);
  return v7;
}
