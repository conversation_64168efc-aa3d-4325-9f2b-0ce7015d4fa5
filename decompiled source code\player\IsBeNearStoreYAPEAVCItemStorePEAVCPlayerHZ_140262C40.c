/*
 * Function: ?IsBeNearStore@@YAPEAVCItemStore@@PEAVCPlayer@@H@Z
 * Address: 0x140262C40
 */

CItemStore *__usercall IsBeNearStore@<rax>(CPlayer *p@<rcx>, int nStoreCode@<edx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CItemStoreManager *v5; // rax@4
  CItemStore *result; // rax@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  bool *v8; // [sp+20h] [bp-28h]@4
  CMapItemStoreList *v9; // [sp+28h] [bp-20h]@4
  int j; // [sp+30h] [bp-18h]@6
  int nSerial; // [sp+34h] [bp-14h]@4
  CPlayer *v12; // [sp+50h] [bp+8h]@1
  int v13; // [sp+58h] [bp+10h]@1

  v13 = nStoreCode;
  v12 = p;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0i64;
  nSerial = (unsigned __int8)CMapData::GetMapCode(v12->m_pCurMap);
  v5 = CItemStoreManager::Instance();
  v9 = CItemStoreManager::GetMapItemStoreListBySerial(v5, nSerial);
  if ( v9 )
  {
    for ( j = 0; j < v9->m_nItemStoreNum; ++j )
    {
      v8 = &v9->m_ItemStore[j].m_bLive;
      if ( v13 == -1 || *(_DWORD *)(*((_QWORD *)v8 + 4) + 324i64) == v13 )
      {
        GetSqrt((float *)(*(_QWORD *)(*((_QWORD *)v8 + 3) + 16i64) + 128i64), v12->m_fCurPos);
        if ( a3 < 100.0 )
          return (CItemStore *)v8;
      }
    }
    result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
