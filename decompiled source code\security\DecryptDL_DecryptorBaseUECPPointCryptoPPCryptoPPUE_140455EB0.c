/*
 * Function: ?Decrypt@?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUDecodingResult@2@AEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x140455EB0
 */

CryptoPP::DecodingResult *__fastcall CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>::Decrypt(CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *this, CryptoPP::DecodingResult *result, CryptoPP::RandomNumberGenerator *rng, const char *ciphertext, unsigned __int64 ciphertextLength, char *plaintext, CryptoPP::NameValuePairs *parameters)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  _QWORD *v9; // rax@4
  _QWORD *v10; // rax@4
  __int64 v11; // rax@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v12; // rax@4
  __int64 v13; // rax@4
  __int64 v14; // r9@4
  __int64 v15; // rax@4
  __int64 v16; // rdx@4
  int v17; // eax@4
  __int64 v18; // rax@4
  __int64 v19; // rax@4
  __int64 v20; // rax@4
  __int64 v21; // rax@4
  unsigned __int64 v22; // rax@4
  __int64 v23; // rax@4
  __int64 v24; // rax@4
  __int64 v25; // rax@4
  __int64 v27; // [sp+0h] [bp-228h]@1
  CryptoPP::ECPPoint *v28; // [sp+20h] [bp-208h]@4
  void *v29; // [sp+28h] [bp-200h]@4
  CryptoPP::NameValuePairs *v30; // [sp+30h] [bp-1F8h]@4
  __int64 *v31; // [sp+40h] [bp-1E8h]@4
  __int64 *v32; // [sp+48h] [bp-1E0h]@4
  __int64 v33; // [sp+50h] [bp-1D8h]@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v34; // [sp+58h] [bp-1D0h]@4
  __int64 v35; // [sp+60h] [bp-1C8h]@4
  CryptoPP::ECPPoint v36; // [sp+80h] [bp-1A8h]@4
  __int64 v37; // [sp+E8h] [bp-140h]@4
  CryptoPP::ECPPoint v38; // [sp+100h] [bp-128h]@4
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v39; // [sp+178h] [bp-B0h]@4
  __int64 v40; // [sp+198h] [bp-90h]@4
  _QWORD *v41; // [sp+1A0h] [bp-88h]@4
  _QWORD *v42; // [sp+1A8h] [bp-80h]@4
  __int64 v43; // [sp+1B0h] [bp-78h]@4
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *v44; // [sp+1B8h] [bp-70h]@4
  CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> >Vtbl *v45; // [sp+1C0h] [bp-68h]@4
  __int64 v46; // [sp+1C8h] [bp-60h]@4
  __int64 v47; // [sp+1D0h] [bp-58h]@4
  int v48; // [sp+1D8h] [bp-50h]@4
  void *v49; // [sp+1E0h] [bp-48h]@4
  __int64 v50; // [sp+1E8h] [bp-40h]@4
  __int64 v51; // [sp+1F0h] [bp-38h]@4
  unsigned __int64 size; // [sp+1F8h] [bp-30h]@4
  __int64 v53; // [sp+200h] [bp-28h]@4
  unsigned __int64 v54; // [sp+208h] [bp-20h]@4
  char *v55; // [sp+210h] [bp-18h]@4
  char *v56; // [sp+218h] [bp-10h]@4
  CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *v57; // [sp+230h] [bp+8h]@1
  CryptoPP::DecodingResult *v58; // [sp+238h] [bp+10h]@1
  const char *v59; // [sp+248h] [bp+20h]@1
  const char *v60; // [sp+248h] [bp+20h]@4
  CryptoPP::ECPPoint *ciphertextLengtha; // [sp+250h] [bp+28h]@4

  v59 = ciphertext;
  v58 = result;
  v57 = this;
  v7 = &v27;
  for ( i = 136i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v40 = -2i64;
  LODWORD(v9) = ((int (__fastcall *)(CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *))v57->vfptr[1].CiphertextLength)(v57);
  v41 = v9;
  v31 = v9;
  LODWORD(v10) = ((int (__fastcall *)(CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *))v57->vfptr[1].ParameterSupported)(v57);
  v42 = v10;
  v32 = v10;
  LODWORD(v11) = ((int (__fastcall *)(CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint> *))v57->vfptr[1].FixedCiphertextLength)(v57);
  v43 = v11;
  v33 = v11;
  v12 = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint>>::GetAbstractGroupParameters((CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::ECPPoint> > *)&v57->vfptr);
  v44 = v12;
  v34 = v12;
  v45 = v57->vfptr;
  LODWORD(v13) = ((int (__fastcall *)(signed __int64))v45->GetKeyInterface)((signed __int64)&v57->vfptr);
  v46 = v13;
  v35 = v13;
  LOBYTE(v14) = 1;
  LODWORD(v15) = ((int (__fastcall *)(__int64, CryptoPP::ECPPoint *, const char *, __int64))v34->vfptr[14].__vecDelDtor)(
                   (__int64)v34,
                   &v36,
                   v59,
                   v14);
  v47 = v15;
  LOBYTE(v16) = 1;
  v17 = ((int (__fastcall *)(__int64, __int64))v34->vfptr[12].__vecDelDtor)((__int64)v34, v16);
  v48 = v17;
  v37 = (unsigned int)v17;
  v60 = &v59[v17];
  ciphertextLengtha = (CryptoPP::ECPPoint *)(ciphertextLength - (unsigned int)v17);
  LODWORD(v18) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v35 + 16i64))(v35);
  v49 = (void *)v18;
  v19 = *v31;
  v29 = v49;
  LOBYTE(v28) = 1;
  LODWORD(v20) = (*(int (__fastcall **)(__int64 *, CryptoPP::ECPPoint *, __int64, CryptoPP::ECPPoint *))(v19 + 8))(
                   v31,
                   &v38,
                   (__int64)v34,
                   &v36);
  v50 = v20;
  LODWORD(v21) = (*(int (__fastcall **)(__int64, CryptoPP::ECPPoint *))(*(_QWORD *)v33 + 24i64))(v33, ciphertextLengtha);
  v51 = v21;
  LODWORD(v22) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v33 + 8i64))(v33, v21);
  size = v22;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v39,
    v22);
  v53 = v23;
  v54 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v39);
  v55 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v39);
  v24 = *v32;
  v30 = parameters;
  v29 = &v36;
  v28 = &v38;
  (*(void (__fastcall **)(__int64 *, __int64, char *, unsigned __int64))(v24 + 8))(v32, (__int64)v34, v55, v54);
  v56 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v39);
  v25 = *(_QWORD *)v33;
  v30 = parameters;
  v29 = plaintext;
  v28 = ciphertextLengtha;
  (*(void (__fastcall **)(__int64, CryptoPP::DecodingResult *, char *, const char *))(v25 + 40))(v33, v58, v56, v60);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v39);
  CryptoPP::ECPPoint::~ECPPoint(&v38);
  CryptoPP::ECPPoint::~ECPPoint(&v36);
  return v58;
}
