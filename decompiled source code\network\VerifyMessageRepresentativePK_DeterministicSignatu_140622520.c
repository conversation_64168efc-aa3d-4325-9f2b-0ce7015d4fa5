/*
 * Function: ?VerifyMessageRepresentative@PK_DeterministicSignatureMessageEncodingMethod@CryptoPP@@UEBA_NAEAVHashTransformation@2@U?$pair@PEBE_K@std@@_NPEAE_K@Z
 * Address: 0x140622520
 */

char __fastcall CryptoPP::PK_DeterministicSignatureMessageEncodingMethod::VerifyMessageRepresentative(void *Buf1, CryptoPP *a2, const void *a3, __int64 a4, void *Buf1a, CryptoPP *a6)
{
  unsigned __int64 v6; // rax@1
  struct CryptoPP::RandomNumberGenerator *v7; // rax@1
  void *v8; // rax@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v10; // [sp+50h] [bp-78h]@1
  char v11; // [sp+68h] [bp-60h]@1
  char v12; // [sp+70h] [bp-58h]@1
  __int64 v13; // [sp+80h] [bp-48h]@1
  char *v14; // [sp+88h] [bp-40h]@1
  __int64 v15; // [sp+90h] [bp-38h]@1
  size_t Size; // [sp+98h] [bp-30h]@1
  int v17; // [sp+A0h] [bp-28h]@1
  __int64 *v18; // [sp+D0h] [bp+8h]@1
  const void *v19; // [sp+E0h] [bp+18h]@1

  v19 = a3;
  v18 = (__int64 *)Buf1;
  v13 = -2i64;
  v6 = CryptoPP::BitsToBytes(a6);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v10,
    v6);
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v10);
  qmemcpy(&v12, v19, 0x10ui64);
  v7 = CryptoPP::NullRNG(0i64);
  v15 = *v18;
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, _QWORD, _QWORD))(v15 + 48))(
    v18,
    v7,
    0i64,
    0i64);
  Size = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v10);
  v8 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v10);
  v17 = memcmp_0(Buf1a, v8, Size) == 0;
  v11 = v17;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v10);
  return v11;
}
