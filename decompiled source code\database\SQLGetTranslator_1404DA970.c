/*
 * Function: SQLGetTranslator
 * Address: 0x1404DA970
 */

int __fastcall SQLGetTranslator(HWND__ *hwnd, char *lpszName, unsigned __int16 cbNameMax, unsigned __int16 *pcbNameOut, char *lpszPath, unsigned __int16 cbPathMax, unsigned __int16 *pcbPathOut, unsigned int *pvOption)
{
  HWND__ *v8; // rbp@1
  unsigned __int16 *v9; // rbx@1
  unsigned __int16 v10; // di@1
  char *v11; // rsi@1
  __int64 (__cdecl *v12)(); // rax@1
  int result; // eax@2

  v8 = hwnd;
  v9 = pcbNameOut;
  v10 = cbNameMax;
  v11 = lpszName;
  v12 = ODBC___GetSetupProc("SQLGetTranslator");
  if ( v12 )
    result = ((int (__fastcall *)(HWND__ *, char *, _QWORD, unsigned __int16 *))v12)(v8, v11, v10, v9);
  else
    result = 0;
  return result;
}
