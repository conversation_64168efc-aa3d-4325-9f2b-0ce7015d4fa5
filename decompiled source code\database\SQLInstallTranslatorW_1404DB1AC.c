/*
 * Function: SQLInstallTranslatorW
 * Address: 0x1404DB1AC
 */

int __fastcall SQLInstallTranslatorW(const unsigned __int16 *lpszInfFile, const unsigned __int16 *lpszTranslator, const unsigned __int16 *lpszPathIn, unsigned __int16 *lpszPathOut, unsigned __int16 cbPathOutMax, unsigned __int16 *pcbPathOut, unsigned __int16 fRequest, unsigned int *lpdwUsageCount)
{
  const unsigned __int16 *v8; // rbp@1
  unsigned __int16 *v9; // rbx@1
  const unsigned __int16 *v10; // rdi@1
  const unsigned __int16 *v11; // rsi@1
  __int64 (__cdecl *v12)(); // rax@1
  int result; // eax@2

  v8 = lpszInfFile;
  v9 = lpszPathOut;
  v10 = lpszPathIn;
  v11 = lpszTranslator;
  v12 = ODBC___GetSetupProc("SQLInstallTranslatorW");
  if ( v12 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *, unsigned __int16 *))v12)(
               v8,
               v11,
               v10,
               v9);
  else
    result = 0;
  return result;
}
