/*
 * Function: ?SendMsg_FixPosition@CPlayer@@UEAAXH@Z
 * Address: 0x1400D5A80
 */

void __fastcall CPlayer::SendMsg_FixPosition(CPlayer *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@6
  unsigned int v6; // [sp+3Ah] [bp-6Eh]@6
  __int16 v7; // [sp+3Eh] [bp-6Ah]@6
  char v8; // [sp+40h] [bp-68h]@6
  __int16 pShort; // [sp+41h] [bp-67h]@6
  unsigned __int16 v10; // [sp+47h] [bp-61h]@6
  unsigned __int64 v11; // [sp+49h] [bp-5Fh]@6
  char v12; // [sp+51h] [bp-57h]@6
  char pbyType; // [sp+74h] [bp-34h]@6
  char v14; // [sp+75h] [bp-33h]@6
  unsigned __int64 v15; // [sp+90h] [bp-18h]@4
  CPlayer *v16; // [sp+B0h] [bp+8h]@1
  int dwClientIndex; // [sp+B8h] [bp+10h]@1

  dwClientIndex = n;
  v16 = this;
  v2 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( !v16->m_bObserver || *(&g_Player.m_byUserDgr + 50856 * n) )
  {
    *(_WORD *)szMsg = v16->m_ObjID.m_wIndex;
    v6 = v16->m_dwObjSerial;
    v7 = CPlayer::GetVisualVer(v16);
    FloatToShort(v16->m_fCurPos, &pShort, 3);
    v8 = CPlayerDB::GetRaceSexCode(&v16->m_Param);
    v11 = CPlayer::GetStateFlag(v16);
    v10 = v16->m_wLastContEffect;
    v12 = v16->m_byGuildBattleColorInx;
    pbyType = 4;
    v14 = 9;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x1Au);
  }
}
