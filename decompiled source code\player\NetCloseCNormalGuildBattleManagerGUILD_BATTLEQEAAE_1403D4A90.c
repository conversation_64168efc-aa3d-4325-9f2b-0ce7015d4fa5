/*
 * Function: ?NetClose@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEKKPEAVCPlayer@@@Z
 * Address: 0x1403D4A90
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::NetClose(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwGuildSerial, unsigned int dwCharacSerial, CPlayer *pkPlayer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+20h] [bp-18h]@6
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwCharacSeriala; // [sp+50h] [bp+18h]@1
  CPlayer *pkPlayera; // [sp+58h] [bp+20h]@1

  pkPlayera = pkPlayer;
  dwCharacSeriala = dwCharacSerial;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( dwGuildSerial == -1 )
  {
    result = -115;
  }
  else
  {
    v8 = 0i64;
    v8 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v9, dwGuildSerial);
    if ( v8 )
      result = GUILD_BATTLE::CNormalGuildBattle::NetClose(v8, dwCharacSeriala, pkPlayera);
    else
      result = -114;
  }
  return result;
}
