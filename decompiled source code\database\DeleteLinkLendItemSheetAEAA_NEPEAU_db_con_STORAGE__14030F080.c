/*
 * Function: ?DeleteLink@LendItemSheet@@AEAA_NEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14030F080
 */

bool __fastcall LendItemSheet::DeleteLink(LendItemSheet *this, char byStorageCode, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  LendItemSheet::Cell *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  LendItemSheet::Cell *v8; // [sp+20h] [bp-28h]@4
  LendItemSheet::Cell v9; // [sp+28h] [bp-20h]@9
  LendItemSheet *v10; // [sp+50h] [bp+8h]@1
  char v11; // [sp+58h] [bp+10h]@1
  _STORAGE_LIST::_db_con *pItema; // [sp+60h] [bp+18h]@1

  pItema = pItem;
  v11 = byStorageCode;
  v10 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = ListHeap<LendItemSheet::Cell>::top(&v10->_heapFixRow);
  if ( v8 )
  {
    if ( v8->_nStorageCode != (unsigned __int8)v11 || v8->_pkItem != pItema )
    {
      LendItemSheet::Cell::Cell(&v9, v11, pItema);
      result = ListHeap<LendItemSheet::Cell>::pop(&v10->_heapFixRow, v6);
    }
    else
    {
      result = ListHeap<LendItemSheet::Cell>::pop(&v10->_heapFixRow);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
