/*
 * Function: ?Alive_Char_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3FF0
 */

void __fastcall CMainThread::Alive_Char_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  _REGED *pAliveAvator; // [sp+20h] [bp-38h]@7
  char v6; // [sp+30h] [bp-28h]@4
  CUserDB *v7; // [sp+38h] [bp-20h]@4
  char *v8; // [sp+40h] [bp-18h]@7

  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  v7 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v7->m_bActive )
  {
    if ( v7->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
    {
      v8 = pData->m_sData;
      pAliveAvator = (_REGED *)&pData->m_sData[26];
      CUserDB::Alive_Char_Complete(
        v7,
        pData->m_byResult,
        pData->m_sData[0],
        *(_DWORD *)&pData->m_sData[4],
        (_REGED *)&pData->m_sData[26]);
    }
  }
}
