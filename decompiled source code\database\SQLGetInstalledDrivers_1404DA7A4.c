/*
 * Function: SQLGetInstalledDrivers
 * Address: 0x1404DA7A4
 */

int __fastcall SQLGetInstalledDrivers(char *lpszBuf, unsigned __int16 cbBufMax, unsigned __int16 *pcbBufOut)
{
  char *v3; // rsi@1
  unsigned __int16 *v4; // rbx@1
  unsigned __int16 v5; // di@1
  __int64 (__cdecl *v6)(); // rax@1
  int result; // eax@2

  v3 = lpszBuf;
  v4 = pcbBufOut;
  v5 = cbBufMax;
  v6 = ODBC___GetSetupProc("SQLGetInstalledDrivers");
  if ( v6 )
    result = ((int (__fastcall *)(char *, _QWORD, unsigned __int16 *))v6)(v3, v5, v4);
  else
    result = 0;
  return result;
}
