/*
 * Function: ?CallFunc_Item_Cancel@CRusiaBillingMgr@@QEAAHAEAU__list@_param_cash_rollback@@PEAD@Z
 * Address: 0x140321300
 */

signed __int64 __usercall CRusiaBillingMgr::CallFunc_Item_Cancel@<rax>(CRusiaBillingMgr *this@<rcx>, _param_cash_rollback::__list *list@<rdx>, char *szUserID@<r8>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@4
  CRusiaBillingMgr *v9; // [sp+40h] [bp+8h]@1
  _param_cash_rollback::__list *v10; // [sp+48h] [bp+10h]@1
  char *v11; // [sp+50h] [bp+18h]@1

  v11 = szUserID;
  v10 = list;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CoInitialize(0i64);
  v8 = RFACC_Cancel(v10->in_lnUID);
  if ( v8 )
  {
    RFACC_CheckBalance(v11);
    v10->out_nCashAmount = (signed int)floor(a4);
    CoUninitialize();
    result = 0i64;
  }
  else
  {
    CLogFile::Write(&v9->m_logBill, "Item Buy Cancel Fail.");
    CoUninitialize();
    result = 1i64;
  }
  return result;
}
