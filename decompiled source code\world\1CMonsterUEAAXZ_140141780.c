/*
 * Function: ??1<PERSON><PERSON><PERSON>@@UEAA@XZ
 * Address: 0x140141780
 */

void __fastcall CMonster::~CMonster(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CMonster *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (CGameObjectVtbl *)&CMonster::`vftable';
  --CMonster::s_nAllocNum;
  CMonster::_DestroySDM();
  CLuaSignalReActor::~CLuaSignalReActor(&v5->m_LuaSignalReActor);
  CMonsterAI::~CMonsterAI(&v5->m_AI);
  CMonsterSkillPool::~CMonsterSkillPool(&v5->m_MonsterSkillPool);
  CMonsterHierarchy::~CMonsterHierarchy(&v5->m_MonHierarcy);
  CMonsterAggroMgr::~CMonsterAggroMgr(&v5->m_AggroMgr);
  CCharacter::~CCharacter((CCharacter *)&v5->vfptr);
}
