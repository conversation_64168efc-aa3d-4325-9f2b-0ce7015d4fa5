/*
 * Function: ??1BossSchedule_Map@@QEAA@XZ
 * Address: 0x14041B430
 */

void __fastcall BossSchedule_Map::~BossSchedule_Map(BossSchedule_Map *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  BossSchedule_Map *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  BossSchedule_Map::Clear(v5);
  CIniFile::~CIniFile(&v5->m_INIFile);
}
