/*
 * Function: j_?push_back@?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEAAXAEBVCUnmannedTraderGroupDivisionVersionInfo@@@Z
 * Address: 0x140010627
 */

void __fastcall std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::push_back(std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, CUnmannedTraderGroupDivisionVersionInfo *_Val)
{
  std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::push_back(
    this,
    _<PERSON>);
}
