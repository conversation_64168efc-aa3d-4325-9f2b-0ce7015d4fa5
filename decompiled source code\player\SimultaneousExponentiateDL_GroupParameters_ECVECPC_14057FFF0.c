/*
 * Function: ?SimultaneousExponentiate@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEBAXPEAUECPPoint@2@AEBU32@PEBVInteger@2@I@Z
 * Address: 0x14057FFF0
 */

int __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::SimultaneousExponentiate(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *a1, __int64 a2, __int64 a3, __int64 a4)
{
  CryptoPP::ECP *v4; // rax@1
  __int64 v6; // [sp+58h] [bp+10h]@1
  __int64 v7; // [sp+60h] [bp+18h]@1
  __int64 v8; // [sp+68h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a2;
  v4 = CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetCurve(a1);
  return ((int (__fastcall *)(CryptoPP::ECP *, __int64, __int64, __int64))v4->vfptr->SimultaneousMultiply)(
           v4,
           v6,
           v7,
           v8);
}
