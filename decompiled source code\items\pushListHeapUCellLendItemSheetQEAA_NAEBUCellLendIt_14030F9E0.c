/*
 * Function: ?push@?$ListHeap@UCell@LendItemSheet@@@@QEAA_NAEBUCell@LendItemSheet@@@Z
 * Address: 0x14030F9E0
 */

bool __fastcall ListHeap<LendItemSheet::Cell>::push(ListHeap<LendItemSheet::Cell> *this, LendItemSheet::Cell *data)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-58h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-34h]@4
  CNetIndexList::_index_node *pos; // [sp+38h] [bp-20h]@6
  LendItemSheet::Cell *v8; // [sp+40h] [bp-18h]@6
  LendItemSheet::Cell *v9; // [sp+48h] [bp-10h]@17
  ListHeap<LendItemSheet::Cell> *v10; // [sp+60h] [bp+8h]@1
  LendItemSheet::Cell *Src; // [sp+68h] [bp+10h]@1

  Src = data;
  v10 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !CNetIndexList::PopNode_Front((CNetIndexList *)&v10->_listEmpty.m_Head, &pdwOutIndex) )
    return 0;
  memcpy_0(&v10->_pBuf[pdwOutIndex], Src, 0x18ui64);
  pos = v10->_listData.m_Head.m_pNext;
  v8 = &v10->_pBuf[pos->m_dwIndex];
  if ( !CNetIndexList::size((CNetIndexList *)&v10->_listData.m_Head) )
  {
    if ( !CNetIndexList::PushNode_Front((CNetIndexList *)&v10->_listData.m_Head, pdwOutIndex) )
    {
      CNetIndexList::PushNode_Back((CNetIndexList *)&v10->_listEmpty.m_Head, pdwOutIndex);
      return 0;
    }
    return 1;
  }
  if ( CNetIndexList::size((CNetIndexList *)&v10->_listData.m_Head) && LendItemSheet::Cell::operator>(v8, Src) )
  {
    if ( !CNetIndexList::PushNode_Front((CNetIndexList *)&v10->_listData.m_Head, pdwOutIndex) )
    {
      CNetIndexList::PushNode_Back((CNetIndexList *)&v10->_listEmpty.m_Head, pdwOutIndex);
      return 0;
    }
    return 1;
  }
  while ( pos != &v10->_listData.m_Tail )
  {
    pos = pos->m_pNext;
    if ( pos == &v10->_listData.m_Tail )
      break;
    v9 = &v10->_pBuf[pos->m_dwIndex];
    if ( LendItemSheet::Cell::operator>(v9, Src) )
    {
      if ( ListHeap<LendItemSheet::Cell>::CIndexListEx::Push(&v10->_listData, pos, pdwOutIndex) )
      {
        result = 1;
      }
      else
      {
        CNetIndexList::PushNode_Back((CNetIndexList *)&v10->_listEmpty.m_Head, pdwOutIndex);
        result = 0;
      }
      return result;
    }
  }
  if ( CNetIndexList::PushNode_Back((CNetIndexList *)&v10->_listData.m_Head, pdwOutIndex) )
    return 1;
  CNetIndexList::PushNode_Back((CNetIndexList *)&v10->_listEmpty.m_Head, pdwOutIndex);
  return 0;
}
