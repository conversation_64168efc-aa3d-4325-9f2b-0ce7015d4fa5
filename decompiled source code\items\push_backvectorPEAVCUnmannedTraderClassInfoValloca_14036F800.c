/*
 * Function: ?push_back@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAXAEBQEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x14036F800
 */

void __fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::push_back(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, CUnmannedTraderClassInfo *const *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  char v6; // [sp+20h] [bp-58h]@6
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *result; // [sp+38h] [bp-40h]@6
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v8; // [sp+40h] [bp-38h]@6
  unsigned __int64 v9; // [sp+58h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v10; // [sp+60h] [bp-18h]@6
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v11; // [sp+80h] [bp+8h]@1
  CUnmannedTraderClassInfo **_Vala; // [sp+88h] [bp+10h]@1

  _Vala = (CUnmannedTraderClassInfo **)_Val;
  v11 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::size(v11);
  v4 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::capacity(v11);
  if ( v9 >= v4 )
  {
    result = (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v6;
    v10 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
            v11,
            (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v6);
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::insert(v11, &v8, v10, _Vala);
    std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v8);
  }
  else
  {
    v11->_Mylast = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Ufill(
                     v11,
                     v11->_Mylast,
                     1ui64,
                     _Vala);
  }
}
