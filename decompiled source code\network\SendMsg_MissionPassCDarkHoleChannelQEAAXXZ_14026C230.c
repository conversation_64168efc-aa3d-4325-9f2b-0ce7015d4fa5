/*
 * Function: ?SendMsg_MissionPass@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x14026C230
 */

void __fastcall CDarkHoleChannel::SendMsg_MissionPass(CDarkHoleChannel *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@10
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char Dest; // [sp+38h] [bp-70h]@7
  char *Source; // [sp+58h] [bp-50h]@4
  _dh_mission_mgr::_if_change *v7; // [sp+60h] [bp-48h]@4
  char pbyType; // [sp+74h] [bp-34h]@7
  char v9; // [sp+75h] [bp-33h]@7
  int j; // [sp+84h] [bp-24h]@7
  _dh_player_mgr *v11; // [sp+88h] [bp-20h]@9
  unsigned __int64 v12; // [sp+98h] [bp-10h]@4
  CDarkHoleChannel *v13; // [sp+B0h] [bp+8h]@1

  v13 = this;
  v1 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v4 ^ _security_cookie;
  Source = v13->m_MissionMgr.pCurMssionPtr->szCompleteMsg;
  v7 = _dh_mission_mgr::SearchCurMissionCont(&v13->m_MissionMgr);
  if ( v7 && v7->pszComMsg )
    Source = v7->pszComMsg;
  strcpy_0(&Dest, Source);
  pbyType = 35;
  v9 = 10;
  for ( j = 0; j < 32; ++j )
  {
    v11 = &v13->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v11) )
    {
      v3 = _darkhole_mission_pass_inform_zocl::size((_darkhole_mission_pass_inform_zocl *)&Dest);
      CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, &Dest, v3);
    }
  }
}
