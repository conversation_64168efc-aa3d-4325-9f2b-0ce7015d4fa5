/*
 * Function: ?GetNextMessage@BufferedTransformation@CryptoPP@@UEAA_NXZ
 * Address: 0x1405F52A0
 */

bool __fastcall CryptoPP::BufferedTransformation::GetNextMessage(CryptoPP::BufferedTransformation *this)
{
  __int64 v1; // rax@1
  __int64 v2; // rax@2
  bool result; // al@2
  CryptoPP::BufferedTransformation *v4; // [sp+40h] [bp+8h]@1

  v4 = this;
  LODWORD(v1) = ((int (*)(void))this->vfptr[20].Clone)();
  if ( v1 )
  {
    LODWORD(v2) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))v4->vfptr[20].Clone)(v4);
    result = (*(int (__fastcall **)(__int64))(*(_QWORD *)v2 + 200i64))(v2);
  }
  else
  {
    if ( (unsigned __int8)((int (__fastcall *)(CryptoPP::BufferedTransformation *))v4->vfptr[12].__vecDelDtor)(v4) )
      _wassert(L"!AnyMessages()", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\cryptlib.cpp", 0x176u);
    result = 0;
  }
  return result;
}
