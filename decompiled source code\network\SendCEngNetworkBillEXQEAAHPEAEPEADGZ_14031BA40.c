/*
 * Function: ?Send@CEngNetworkBillEX@@QEAAHPEAEPEADG@Z
 * Address: 0x14031BA40
 */

int __fastcall CEngNetworkBillEX::Send(CEngNetworkBillEX *this, char *pbyType, char *szMsg, unsigned __int16 nLen)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned __int16 v8; // [sp+20h] [bp-18h]@4
  CEngNetworkBillEX *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = nLen;
  return ((int (__fastcall *)(_QWORD, _QWORD, char *, char *))v9->LoadSendMsg)(0i64, 0i64, pbyType, szMsg);
}
