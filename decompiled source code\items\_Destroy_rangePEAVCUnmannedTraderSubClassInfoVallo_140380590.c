/*
 * Function: ??$_Destroy_range@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@0AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@@Z
 * Address: 0x140380590
 */

void __fastcall std::_Destroy_range<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Scalar_ptr_iterator_tag v6; // [sp+20h] [bp-18h]@4
  CUnmannedTraderSubClassInfo **__formal; // [sp+40h] [bp+8h]@1
  CUnmannedTraderSubClassInfo **_Lasta; // [sp+48h] [bp+10h]@1
  std::allocator<CUnmannedTraderSubClassInfo *> *_Ala; // [sp+50h] [bp+18h]@1

  _Ala = _Al;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = std::_Ptr_cat<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *>(&__formal, &_Lasta);
  std::_Destroy_range<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    __formal,
    _Lasta,
    _Ala,
    v6);
}
