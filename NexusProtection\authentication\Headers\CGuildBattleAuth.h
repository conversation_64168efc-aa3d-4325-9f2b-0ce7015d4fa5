#pragma once

/**
 * @file CGuildBattleAuth.h
 * @brief Guild Battle Authentication System
 * 
 * Provides secure authentication for guild battle login operations and management.
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * Original files:
 * - LogInCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E4050.c
 * - LogInCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKEPE_1403E0DD0.c
 * - LogInCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKK_1403D4360.c
 * - LoginCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DFA80.c
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <unordered_map>
#include <vector>
#include <array>

namespace NexusProtection::Authentication {

    /**
     * @brief Guild battle authentication result
     */
    enum class GuildBattleAuthResult : uint8_t {
        Success = 0,
        InvalidParameters = 1,
        GuildNotFound = 2,
        BattleNotFound = 3,
        MemberNotFound = 4,
        BattleNotReady = 5,
        BattleNotActive = 6,
        MemberAlreadyLoggedIn = 7,
        MaxMembersReached = 8,
        SystemError = 9,
        NotInitialized = 10
    };

    /**
     * @brief Guild battle state enumeration
     */
    enum class GuildBattleState : uint8_t {
        None = 0,
        Ready = 1,
        Count = 2,
        InBattle = 3,
        Finished = 4
    };

    /**
     * @brief Guild battle member information
     */
    struct GuildBattleMember {
        uint32_t characterSerial{0};
        uint32_t guildSerial{0};
        int sessionId{0};
        double pvpPoint{0.0};
        bool isLoggedIn{false};
        std::chrono::steady_clock::time_point loginTime;
        std::chrono::steady_clock::time_point lastActivity;

        GuildBattleMember() = default;
        GuildBattleMember(uint32_t charSerial, uint32_t guildSerial, int session)
            : characterSerial(charSerial), guildSerial(guildSerial), sessionId(session),
              loginTime(std::chrono::steady_clock::now()) {}

        bool IsValid() const;
        std::string ToString() const;
        void UpdateActivity();
    };

    /**
     * @brief Guild battle guild information
     */
    class CGuildBattleGuild {
    public:
        static constexpr size_t MAX_GUILD_MEMBERS = 50;

        CGuildBattleGuild();
        ~CGuildBattleGuild() = default;

        // Core operations
        bool Initialize(uint32_t guildSerial, const std::string& guildName);
        void Shutdown();

        // Member management
        int GetMember(uint32_t characterSerial) const;
        bool AddMember(const GuildBattleMember& member);
        bool RemoveMember(uint32_t characterSerial);
        bool LoginMember(int sessionId, uint32_t characterSerial, uint8_t guildBattleNumber,
                        const std::string& destGuildName, uint32_t battleId);

        // Guild information
        uint32_t GetGuildSerial() const { return m_guildSerial; }
        const std::string& GetGuildName() const { return m_guildName; }
        size_t GetMemberCount() const;
        size_t GetLoggedInMemberCount() const;

        // Battle operations
        bool AskJoin(int sessionId, const std::string& destGuildName);
        bool MoveMember(int memberIndex, uint32_t battleId);
        bool SendNotifications(int memberIndex);

        // Validation
        bool IsValid() const;
        std::string ToString() const;

    private:
        uint32_t m_guildSerial{0};
        std::string m_guildName;
        std::array<GuildBattleMember, MAX_GUILD_MEMBERS> m_members;
        size_t m_currentMemberCount{0};
        mutable std::mutex m_membersMutex;

        // Internal methods
        int FindFreeMemberSlot() const;
        bool ValidateMemberLogin(uint32_t characterSerial) const;
    };

    /**
     * @brief Guild battle field information
     */
    struct GuildBattleField {
        uint32_t fieldId{0};
        std::string fieldName;
        uint32_t maxParticipants{100};
        uint32_t currentParticipants{0};
        bool isActive{false};

        GuildBattleField() = default;
        GuildBattleField(uint32_t id, const std::string& name, uint32_t maxPart)
            : fieldId(id), fieldName(name), maxParticipants(maxPart) {}

        bool IsValid() const;
        std::string ToString() const;
    };

    /**
     * @brief Guild battle logger for event tracking
     */
    class CGuildBattleLogger {
    public:
        CGuildBattleLogger();
        ~CGuildBattleLogger() = default;

        // Logging operations
        void Log(const char* format, ...);
        void LogLogin(int sessionId, uint32_t characterSerial, uint32_t guildSerial,
                     const std::string& guildName, uint32_t battleId);
        void LogJoinRequest(int sessionId, const std::string& guildName);
        void LogError(const std::string& error, int sessionId = 0);

        // Configuration
        void SetLogLevel(int level) { m_logLevel = level; }
        int GetLogLevel() const { return m_logLevel; }

        // Public for access from CGuildBattleAuth
        void WriteLog(const std::string& message);

    private:
        int m_logLevel{1};
        mutable std::mutex m_logMutex;
    };

    /**
     * @brief Main guild battle authentication and management
     */
    class CGuildBattleAuth {
    public:
        static constexpr size_t MAX_GUILD_BATTLES = 100;

        CGuildBattleAuth();
        ~CGuildBattleAuth();

        // Core lifecycle
        bool Initialize();
        void Shutdown();
        bool LoadConfiguration();

        // Battle management
        uint32_t CreateBattle(uint32_t guild1Serial, uint32_t guild2Serial);
        bool DestroyBattle(uint32_t battleId);
        bool GetBattleByGuildSerial(uint32_t guildSerial, uint32_t& battleId) const;

        // Authentication operations
        GuildBattleAuthResult AuthenticateGuildBattleLogin(int sessionId, uint32_t guildSerial, uint32_t characterSerial);
        GuildBattleAuthResult AuthenticateGuildMemberLogin(int sessionId, uint32_t characterSerial,
                                                          uint8_t guildBattleNumber, const std::string& destGuildName,
                                                          uint32_t battleId);

        // Battle state management
        bool SetBattleState(uint32_t battleId, GuildBattleState state);
        GuildBattleState GetBattleState(uint32_t battleId) const;
        bool IsBattleReady(uint32_t battleId) const;
        bool IsBattleActive(uint32_t battleId) const;

        // Guild operations
        bool RegisterGuild(uint32_t guildSerial, const std::string& guildName);
        bool UnregisterGuild(uint32_t guildSerial);
        CGuildBattleGuild* GetGuild(uint32_t guildSerial);

        // Configuration and status
        bool IsInitialized() const { return m_isInitialized; }
        bool IsOperational() const { return m_isOperational; }
        size_t GetActiveBattleCount() const;

        // Statistics
        struct Statistics {
            uint32_t totalLoginAttempts{0};
            uint32_t successfulLogins{0};
            uint32_t failedLogins{0};
            uint32_t activeBattles{0};
            uint32_t totalBattlesCreated{0};
            std::chrono::steady_clock::time_point startTime;
        };

        const Statistics& GetStatistics() const { return m_statistics; }
        void ResetStatistics();

        // Legacy compatibility
        bool LogInCNormalGuildBattle_Legacy(int sessionId, uint32_t guildSerial, uint32_t characterSerial);
        bool LogInCNormalGuildBattleGuild_Legacy(int sessionId, uint32_t characterSerial,
                                               uint8_t guildBattleNumber, const std::string& destGuildName,
                                               uint32_t battleId);
        bool LogInCNormalGuildBattleManager_Legacy(int sessionId, uint32_t guildSerial, uint32_t characterSerial);

    private:
        // Configuration
        bool m_isInitialized{false};
        bool m_isOperational{false};

        // Battle management
        struct GuildBattle {
            uint32_t battleId{0};
            uint32_t guild1Serial{0};
            uint32_t guild2Serial{0};
            GuildBattleState state{GuildBattleState::None};
            std::unique_ptr<GuildBattleField> field;
            std::chrono::steady_clock::time_point createTime;
            bool isActive{false};

            GuildBattle() = default;
            GuildBattle(uint32_t id, uint32_t g1, uint32_t g2)
                : battleId(id), guild1Serial(g1), guild2Serial(g2),
                  createTime(std::chrono::steady_clock::now()) {}
        };

        std::unordered_map<uint32_t, std::unique_ptr<GuildBattle>> m_battles;
        std::unordered_map<uint32_t, std::unique_ptr<CGuildBattleGuild>> m_guilds;
        uint32_t m_nextBattleId{1};
        mutable std::mutex m_battlesMutex;
        mutable std::mutex m_guildsMutex;

        // Logging
        std::unique_ptr<CGuildBattleLogger> m_logger;

        // Statistics
        Statistics m_statistics;
        mutable std::mutex m_statisticsMutex;

        // Internal methods
        bool ValidateParameters(int sessionId, uint32_t guildSerial, uint32_t characterSerial) const;
        GuildBattle* GetBattle(uint32_t battleId);
        const GuildBattle* GetBattle(uint32_t battleId) const;
        void UpdateStatistics(bool success);
        void LogAuthenticationEvent(const std::string& event, int sessionId, uint32_t guildSerial, bool success);

        // Battle operations
        bool ProcessBattleLogin(GuildBattle* battle, int sessionId, uint32_t guildSerial, uint32_t characterSerial);
        bool ProcessJoinRequest(GuildBattle* battle, int sessionId, uint32_t guildSerial, uint32_t characterSerial);
    };

    /**
     * @brief Global instance access
     */
    CGuildBattleAuth& GetGuildBattleAuth();

    /**
     * @brief Utility functions
     */
    std::string GuildBattleAuthResultToString(GuildBattleAuthResult result);
    std::string GuildBattleStateToString(GuildBattleState state);

    /**
     * @brief Legacy C interface compatibility
     */
    extern "C" {
        struct CNormalGuildBattle_Legacy {
            void* vfptr;
            uint32_t m_dwID;
            uint8_t m_byGuildBattleNumber;
            void* m_pkField;
            void* m_k1P;  // Guild 1
            void* m_k2P;  // Guild 2
            void* m_kLogger;
        };

        struct CNormalGuildBattleGuild_Legacy {
            void* vfptr;
            uint32_t m_guildSerial;
            char m_guildName[64];
            uint32_t m_dwCurJoinMember;
            void* m_kMember;
        };

        struct CNormalGuildBattleManager_Legacy {
            void* vfptr;
            void* m_battles;
        };

        // Legacy function declarations
        void CNormalGuildBattle_LogIn(CNormalGuildBattle_Legacy* battle, int sessionId,
                                    uint32_t guildSerial, uint32_t characterSerial);
        void CNormalGuildBattleGuild_LogIn(CNormalGuildBattleGuild_Legacy* guild, int sessionId,
                                         uint32_t characterSerial, uint8_t guildBattleNumber,
                                         const char* destGuildName, uint32_t battleId,
                                         void* field, void* logger);
        void CNormalGuildBattleManager_LogIn(CNormalGuildBattleManager_Legacy* manager, int sessionId,
                                           uint32_t guildSerial, uint32_t characterSerial);
        void CNormalGuildBattleGuildMember_Login(void* member);

        // Battle state queries
        bool CNormalGuildBattle_IsReadyOrCountState(void* battle);
        bool CNormalGuildBattle_IsInBattle(void* battle);
        void* CNormalGuildBattleManager_GetBattleByGuildSerial(void* manager, uint32_t guildSerial);
    }

} // namespace NexusProtection::Authentication
