/*
 * Function: ?CheckGoods@TimeItem@@QEAA_NXZ
 * Address: 0x14030E7D0
 */

char __fastcall TimeItem::CheckGoods(TimeItem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  int v6; // [sp+20h] [bp-48h]@10
  unsigned int v7; // [sp+28h] [bp-40h]@10
  int n; // [sp+30h] [bp-38h]@4
  _base_fld *v9; // [sp+38h] [bp-30h]@6
  char v10; // [sp+40h] [bp-28h]@6
  _base_fld *v11; // [sp+48h] [bp-20h]@6
  char *v12; // [sp+50h] [bp-18h]@10
  TimeItem *v13; // [sp+70h] [bp+8h]@1

  v13 = this;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( n = 0; ; ++n )
  {
    v3 = CRecordData::GetRecordNum(&v13->_kRecTimeItem);
    if ( n >= v3 )
      break;
    v9 = CRecordData::GetRecord(&v13->_kRecTimeItem, n);
    v10 = GetItemTableCode((char *)&v9[1]);
    v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v10, (const char *)&v9[1], 7);
    if ( !v11 )
    {
      MyMessageBox("TimeItem", "Wrong Code : %s", &v9[1]);
      return 0;
    }
    if ( !IsTimeItem(v10, v11->m_dwIndex) )
    {
      MyMessageBox("TimeItem", "Is not Lend item : %s", &v9[1]);
      return 0;
    }
    v12 = GetItemKorName((unsigned __int8)v10, v11->m_dwIndex);
    v7 = v9[2].m_dwIndex;
    v6 = *(_DWORD *)&v9[1].m_strCode[60];
    CLogFile::Write(&v13->_kLogger, "[%s(%s)], method:%d, lentime:%d", v12, v11->m_strCode);
  }
  return 1;
}
