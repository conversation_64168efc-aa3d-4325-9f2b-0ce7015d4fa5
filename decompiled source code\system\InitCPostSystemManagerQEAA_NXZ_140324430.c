/*
 * Function: ?Init@CPostSystemManager@@QEAA_NXZ
 * Address: 0x140324430
 */

char __fastcall CPostSystemManager::Init(CPostSystemManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-68h]@1
  unsigned int dwIndex; // [sp+30h] [bp-38h]@11
  CPostData *v6; // [sp+38h] [bp-30h]@9
  void *v7; // [sp+40h] [bp-28h]@6
  __int64 v8; // [sp+48h] [bp-20h]@4
  CPostData *v9; // [sp+50h] [bp-18h]@7
  CPostSystemManager *v10; // [sp+70h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( CPostSystemManager::InitLogger(v10) )
  {
    v7 = operator new[](0x26168ui64);
    if ( v7 )
    {
      *(_DWORD *)v7 = 500;
      `eh vector constructor iterator'(
        (char *)v7 + 8,
        0x138ui64,
        500,
        (void (__cdecl *)(void *))CPostData::CPostData,
        (void (__cdecl *)(void *))CPostData::~CPostData);
      v9 = (CPostData *)((char *)v7 + 8);
    }
    else
    {
      v9 = 0i64;
    }
    v6 = v9;
    v10->m_PostData = v9;
    if ( v10->m_PostData )
    {
      CMyTimer::BeginTimer(&v10->m_tmrRegiTime, 0x3E8u);
      CMyTimer::BeginTimer(&v10->m_tmrProcTime, 0x7530u);
      CNetIndexList::SetList(&v10->m_listEmpty, 0x1F4u);
      CNetIndexList::SetList(&v10->m_listRegist, 0x1F4u);
      CNetIndexList::SetList(&v10->m_listProc, 0x1F4u);
      for ( dwIndex = 0; (signed int)dwIndex < 500; ++dwIndex )
        CNetIndexList::PushNode_Back(&v10->m_listEmpty, dwIndex);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
