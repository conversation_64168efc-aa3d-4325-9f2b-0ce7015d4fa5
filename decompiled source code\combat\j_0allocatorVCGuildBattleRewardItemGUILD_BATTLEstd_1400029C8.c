/*
 * Function: j_??0?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAA@XZ
 * Address: 0x1400029C8
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocator<GUILD_BATTLE::CGuildBattleRewardItem>(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this)
{
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocator<GUILD_BATTLE::CGuildBattleRewardItem>(this);
}
