/*
 * Function: ?reset@_100_per_random_table@@QEAAXXZ
 * Address: 0x1400727E0
 */

void __fastcall _100_per_random_table::reset(_100_per_random_table *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  int k; // [sp+24h] [bp-24h]@6
  unsigned __int16 v6; // [sp+28h] [bp-20h]@8
  unsigned __int16 v7; // [sp+2Ch] [bp-1Ch]@8
  __int16 v8; // [sp+30h] [bp-18h]@8

  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
  {
    for ( k = 0; k < 100; ++k )
    {
      v6 = rand() % 100;
      v7 = rand() % 100;
      v8 = *(&_100_per_random_table::s_wRecord[100 * j] + v7);
      *(&_100_per_random_table::s_wRecord[100 * j] + v7) = *(&_100_per_random_table::s_wRecord[100 * j] + v6);
      *(&_100_per_random_table::s_wRecord[100 * j] + v6) = v8;
    }
  }
}
