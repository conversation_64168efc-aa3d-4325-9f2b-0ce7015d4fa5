/*
 * Function: ?Load@CMoveMapLimitInfoPortal@@UEAAXPEAVCPlayer@@PEAVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1403A42B0
 */

void __fastcall CMoveMapLimitInfoPortal::Load(CMoveMapLimitInfoPortal *this, CPlayer *pkPlayer, CMoveMapLimitRightInfo *pkRight)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@7
  int v6; // eax@9
  __int64 v7; // [sp+0h] [bp-68h]@1
  CMapData *v8; // [sp+20h] [bp-48h]@4
  float pNewPos; // [sp+38h] [bp-30h]@8
  char v10; // [sp+3Ch] [bp-2Ch]@8
  CMoveMapLimitInfoPortal *v11; // [sp+70h] [bp+8h]@1
  CPlayer *v12; // [sp+78h] [bp+10h]@1
  CMoveMapLimitRightInfo *v13; // [sp+80h] [bp+18h]@1

  v13 = pkRight;
  v12 = pkPlayer;
  v11 = this;
  v3 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = CMapOperation::GetMap(&g_MapOper, pkPlayer->m_Param.m_dbChar.m_sStartMapCode);
  if ( v8 )
  {
    if ( v11->m_iMapInx == v8->m_nMapCode )
    {
      v5 = CMoveMapLimitInfo::GetType((CMoveMapLimitInfo *)&v11->vfptr);
      if ( !CMoveMapLimitRightInfo::IsHaveRight(v13, v5) )
      {
        pNewPos = 0.0;
        memset(&v10, 0, 8ui64);
        if ( CMapData::GetRandPosInDummy(v8, v11->m_pkRegenDummy, &pNewPos, 1) )
        {
          CPlayerDB::SetCurPos(&v12->m_Param, &pNewPos);
          v6 = CMoveMapLimitInfo::GetType((CMoveMapLimitInfo *)&v11->vfptr);
          CMoveMapLimitRightInfo::SetFlag(v13, v6, 0, 1);
        }
      }
    }
  }
}
