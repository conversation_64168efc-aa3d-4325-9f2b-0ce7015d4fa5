/*
 * Function: ?Expire_PCBang@CBilling@@QEAAXPEAD@Z
 * Address: 0x14028D2B0
 */

void __fastcall CBilling::Expire_PCBang(CBilling *this, char *szCMS)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+28h] [bp-10h]@10
  CBilling *v7; // [sp+40h] [bp+8h]@1
  const char *Str; // [sp+48h] [bp+10h]@1

  Str = szCMS;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_bOper && szCMS && strlen_0(szCMS) >= 1 )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v6 = &g_Player + j;
      if ( v6 && v6->m_bLive && !strcmp_0(v6->m_pUserDB->m_BillingInfo.szCMS, Str) )
      {
        CPlayer::Billing_Logout(v6);
        v6->m_pUserDB->m_BillingInfo.iType = 1;
      }
    }
  }
}
