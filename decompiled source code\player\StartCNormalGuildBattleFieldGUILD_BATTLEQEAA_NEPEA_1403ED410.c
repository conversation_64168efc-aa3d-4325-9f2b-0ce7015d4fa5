/*
 * Function: ?Start@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NEPEAVCPlayer@@@Z
 * Address: 0x1403ED410
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleField::Start(GUILD_BATTLE::CNormalGuildBattleField *this, char byStartPos, CPlayer *pkPlayer)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  CPlayer *pkPlayera; // [sp+40h] [bp+18h]@1

  pkPlayera = pkPlayer;
  v8 = byStartPos;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( GUILD_BATTLE::CNormalGuildBattleField::MoveStartPos(v7, byStartPos, 6, pkPlayer) )
    result = CPlayer::SetBindPosition(pkPlayera, v7->m_pkMap, v7->m_pkStartPos[(unsigned __int8)v8]);
  else
    result = 0;
  return result;
}
