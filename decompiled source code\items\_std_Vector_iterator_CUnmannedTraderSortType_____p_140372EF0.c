/*
 * Function: _std::_Vector_iterator_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::operator__::_1_::dtor$1
 * Address: 0x140372EF0
 */

void __fastcall std::_Vector_iterator_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(*(std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > **)(a2 + 104));
  }
}
