/*
 * Function: ?SendRe<PERSON>Web@CRaceBossMsgController@@IEAAXEPEAVCMsg@RACE_BOSS_MSG@@@Z
 * Address: 0x1402A1480
 */

void __fastcall CRaceBossMsgController::SendRequestWeb(CRaceBossMsgController *this, char ucRace, RACE_BOSS_MSG::CMsg *pkMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char pbyType; // [sp+34h] [bp-A4h]@8
  char v7; // [sp+35h] [bp-A3h]@8
  char szMsg[4]; // [sp+58h] [bp-80h]@8
  unsigned int v9; // [sp+5Ch] [bp-7Ch]@8
  int v10; // [sp+60h] [bp-78h]@8
  char v11; // [sp+64h] [bp-74h]@8
  char v12; // [sp+84h] [bp-54h]@11
  char v13; // [sp+85h] [bp-53h]@11
  unsigned int Dst; // [sp+A8h] [bp-30h]@11
  int v15; // [sp+ACh] [bp-2Ch]@11
  char v16; // [sp+B0h] [bp-28h]@11
  char v17; // [sp+E8h] [bp+10h]@1
  RACE_BOSS_MSG::CMsg *v18; // [sp+F0h] [bp+18h]@1

  v18 = pkMsg;
  v17 = ucRace;
  v3 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( unk_1799C608D && unk_1799C608E )
  {
    if ( RACE_BOSS_MSG::CMsg::IsSendFromWeb(pkMsg) )
    {
      pbyType = 51;
      v7 = 14;
      *(_DWORD *)szMsg = RACE_BOSS_MSG::CMsg::GetWebDBID(v18);
      v9 = RACE_BOSS_MSG::CMsg::GetID(v18);
      v10 = unk_1799C608C;
      v11 = v17;
      if ( unk_1799C9ADE )
        CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, szMsg, 0xDu);
    }
    else
    {
      v12 = 51;
      v13 = 10;
      memset_0(&Dst, 0, 9ui64);
      Dst = RACE_BOSS_MSG::CMsg::GetID(v18);
      v15 = unk_1799C608C;
      v16 = v17;
      if ( unk_1799C9ADE )
        CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &v12, (char *)&Dst, 9u);
    }
  }
}
