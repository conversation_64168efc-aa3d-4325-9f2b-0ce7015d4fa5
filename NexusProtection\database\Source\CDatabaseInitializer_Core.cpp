/**
 * @file CDatabaseInitializer_Core.cpp
 * @brief Modern C++20 Database Initializer core implementation
 * 
 * This file provides the core implementation of the CDatabaseInitializer class
 * with comprehensive initialization coordination, dependency management, and
 * progress reporting.
 */

#include "../Headers/CDatabaseInitializer.h"
#include "../Headers/CDatabaseManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <thread>
#include <future>

namespace NexusProtection {
namespace Database {

// Constructor
CDatabaseInitializer::CDatabaseInitializer() 
    : m_parallelInitialization(false)
    , m_maxParallelTasks(4)
    , m_isInitializing(false)
    , m_initializationComplete(false)
    , m_initializationFailed(false)
{
    std::cout << "[INFO] CDatabaseInitializer constructor called" << std::endl;
    
    // Register default initialization tasks
    RegisterDefaultTasks();
}

// Destructor
CDatabaseInitializer::~CDatabaseInitializer() {
    std::cout << "[INFO] CDatabaseInitializer destructor called" << std::endl;
}

bool CDatabaseInitializer::InitializeAll(CDatabaseManager& databaseManager, 
                                        ProgressCallback progressCallback) {
    std::lock_guard<std::mutex> lock(m_taskMutex);
    
    try {
        std::cout << "[INFO] Starting complete database initialization" << std::endl;
        
        // Set callback and reset state
        m_progressCallback = std::move(progressCallback);
        m_isInitializing = true;
        m_initializationComplete = false;
        m_initializationFailed = false;
        
        // Reset progress
        m_progress = InitializationProgress{};
        m_progress.totalTasks = m_tasks.size();
        
        // Calculate execution order
        if (!CalculateExecutionOrder()) {
            m_initializationFailed = true;
            std::cerr << "[ERROR] Failed to calculate execution order" << std::endl;
            return false;
        }
        
        // Initialize each phase in order
        std::vector<InitializationPhase> phases = {
            InitializationPhase::PreInit,
            InitializationPhase::DatabaseConnections,
            InitializationPhase::CoreManagers,
            InitializationPhase::EconomySystem,
            InitializationPhase::PvPSystem,
            InitializationPhase::GuildSystem,
            InitializationPhase::AutomineSystem,
            InitializationPhase::PostSystem,
            InitializationPhase::ItemSystem,
            InitializationPhase::LogSystem,
            InitializationPhase::Finalization
        };
        
        for (auto phase : phases) {
            UpdateProgress("", phase);
            
            if (!InitializePhase(phase, databaseManager)) {
                m_initializationFailed = true;
                std::cerr << "[ERROR] Failed to initialize phase: " << static_cast<int>(phase) << std::endl;
                return false;
            }
        }
        
        // Mark as complete
        m_progress.currentPhase = InitializationPhase::Complete;
        m_initializationComplete = true;
        m_isInitializing = false;
        
        NotifyProgress();
        
        std::cout << "[INFO] Complete database initialization successful" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        m_initializationFailed = true;
        m_isInitializing = false;
        std::cerr << "[ERROR] Exception in InitializeAll: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseInitializer::InitializePhase(InitializationPhase phase, CDatabaseManager& databaseManager) {
    try {
        std::cout << "[DEBUG] Initializing phase: " << static_cast<int>(phase) << std::endl;
        
        // Get tasks for this phase
        auto phaseTasks = GetTasksForPhase(phase);
        if (phaseTasks.empty()) {
            std::cout << "[DEBUG] No tasks for phase: " << static_cast<int>(phase) << std::endl;
            return true;
        }
        
        // Update progress
        m_progress.currentPhase = phase;
        m_progress.currentPhaseStartTime = std::chrono::steady_clock::now();
        
        // Execute tasks (parallel or sequential)
        if (m_parallelInitialization && phaseTasks.size() > 1) {
            size_t successCount = ExecuteTasksParallel(phaseTasks, databaseManager);
            return successCount == phaseTasks.size();
        } else {
            // Sequential execution
            for (const auto& taskName : phaseTasks) {
                if (!ExecuteTask(taskName, databaseManager)) {
                    auto& task = m_tasks[taskName];
                    if (task.priority == InitializationPriority::Critical) {
                        return false; // Critical task failed
                    }
                    // Non-critical task failed, continue
                    m_progress.skippedTasks++;
                    m_progress.skippedTaskNames.push_back(taskName);
                }
            }
        }
        
        std::cout << "[DEBUG] Phase initialization completed: " << static_cast<int>(phase) << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in InitializePhase: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseInitializer::ExecuteTask(const std::string& taskName, CDatabaseManager& databaseManager) {
    try {
        auto it = m_tasks.find(taskName);
        if (it == m_tasks.end()) {
            std::cerr << "[ERROR] Task not found: " << taskName << std::endl;
            return false;
        }
        
        auto& task = it->second;
        
        // Check dependencies
        if (!AreDependenciesSatisfied(taskName)) {
            std::cerr << "[ERROR] Dependencies not satisfied for task: " << taskName << std::endl;
            return false;
        }
        
        std::cout << "[DEBUG] Executing task: " << taskName << std::endl;
        
        // Update progress
        UpdateProgress(taskName, task.phase);
        
        // Record start time
        task.startTime = std::chrono::steady_clock::now();
        
        // Execute initialization function with retry
        bool success = false;
        for (uint32_t attempt = 0; attempt <= task.retryCount; ++attempt) {
            try {
                if (task.initFunction && task.initFunction()) {
                    // Execute load function if present
                    if (!task.loadFunction || task.loadFunction()) {
                        success = true;
                        break;
                    }
                } else if (!task.initFunction) {
                    // No init function, just run load function
                    if (!task.loadFunction || task.loadFunction()) {
                        success = true;
                        break;
                    }
                }
                
                if (attempt < task.retryCount) {
                    std::cout << "[WARNING] Task failed, retrying: " << taskName 
                             << " (attempt " << (attempt + 1) << "/" << (task.retryCount + 1) << ")" << std::endl;
                    std::this_thread::sleep_for(std::chrono::milliseconds(1000 * (attempt + 1)));
                }
                
            } catch (const std::exception& e) {
                task.lastError = std::string("Exception: ") + e.what();
                std::cerr << "[ERROR] Exception in task " << taskName << ": " << e.what() << std::endl;
            }
        }
        
        // Record end time
        task.endTime = std::chrono::steady_clock::now();
        
        if (success) {
            task.isInitialized = true;
            m_progress.completedTasks++;
            std::cout << "[DEBUG] Task completed successfully: " << taskName << std::endl;
        } else {
            task.hasFailed = true;
            m_progress.failedTasks++;
            m_progress.failedTaskNames.push_back(taskName);
            std::cerr << "[ERROR] Task failed: " << taskName << std::endl;
        }
        
        // Notify progress
        NotifyProgress();
        
        return success;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ExecuteTask: " << e.what() << std::endl;
        return false;
    }
}

size_t CDatabaseInitializer::ExecuteTasksParallel(const std::vector<std::string>& taskNames, 
                                                 CDatabaseManager& databaseManager) {
    try {
        std::cout << "[DEBUG] Executing " << taskNames.size() << " tasks in parallel" << std::endl;
        
        std::vector<std::future<bool>> futures;
        futures.reserve(taskNames.size());
        
        // Launch tasks
        for (const auto& taskName : taskNames) {
            futures.emplace_back(std::async(std::launch::async, [this, taskName, &databaseManager]() {
                return ExecuteTask(taskName, databaseManager);
            }));
        }
        
        // Wait for completion and count successes
        size_t successCount = 0;
        for (auto& future : futures) {
            if (future.get()) {
                successCount++;
            }
        }
        
        std::cout << "[DEBUG] Parallel execution completed: " << successCount 
                 << "/" << taskNames.size() << " tasks successful" << std::endl;
        
        return successCount;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ExecuteTasksParallel: " << e.what() << std::endl;
        return 0;
    }
}

bool CDatabaseInitializer::AreDependenciesSatisfied(const std::string& taskName) const {
    auto it = m_tasks.find(taskName);
    if (it == m_tasks.end()) {
        return false;
    }
    
    const auto& task = it->second;
    
    // Check all dependencies
    for (const auto& dep : task.dependencies) {
        auto depIt = m_tasks.find(dep);
        if (depIt == m_tasks.end() || !depIt->second.isInitialized) {
            return false;
        }
    }
    
    return true;
}

void CDatabaseInitializer::RegisterDefaultTasks() {
    try {
        std::cout << "[DEBUG] Registering default initialization tasks" << std::endl;
        
        // Pre-initialization tasks
        InitializationTask preInit("PreInit", "Pre-initialization setup", 
            []() { 
                std::cout << "[DEBUG] Pre-initialization setup" << std::endl;
                return true; 
            }, 
            nullptr);
        preInit.phase = InitializationPhase::PreInit;
        preInit.priority = InitializationPriority::Critical;
        RegisterTask(preInit);
        
        // Database connection tasks
        InitializationTask dbConnections("DatabaseConnections", "Initialize database connections",
            []() {
                std::cout << "[DEBUG] Database connections initialization" << std::endl;
                return true;
            },
            nullptr);
        dbConnections.phase = InitializationPhase::DatabaseConnections;
        dbConnections.priority = InitializationPriority::Critical;
        dbConnections.dependencies = {"PreInit"};
        RegisterTask(dbConnections);
        
        // Core managers
        InitializationTask coreManagers("CoreManagers", "Initialize core managers",
            []() {
                std::cout << "[DEBUG] Core managers initialization" << std::endl;
                return true;
            },
            nullptr);
        coreManagers.phase = InitializationPhase::CoreManagers;
        coreManagers.priority = InitializationPriority::Critical;
        coreManagers.dependencies = {"DatabaseConnections"};
        RegisterTask(coreManagers);
        
        // Economy system
        InitializationTask economySystem("EconomySystem", "Initialize economy system",
            []() {
                std::cout << "[DEBUG] Economy system initialization" << std::endl;
                return true;
            },
            []() {
                std::cout << "[DEBUG] Economy system data loading" << std::endl;
                return true;
            });
        economySystem.phase = InitializationPhase::EconomySystem;
        economySystem.priority = InitializationPriority::High;
        economySystem.dependencies = {"CoreManagers"};
        RegisterTask(economySystem);
        
        // Additional default tasks would be registered here...
        
        std::cout << "[DEBUG] Default initialization tasks registered" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in RegisterDefaultTasks: " << e.what() << std::endl;
    }
}

bool CDatabaseInitializer::RegisterTask(const InitializationTask& task) {
    std::lock_guard<std::mutex> lock(m_taskMutex);
    
    try {
        if (!ValidateTask(task)) {
            std::cerr << "[ERROR] Invalid task: " << task.name << std::endl;
            return false;
        }
        
        m_tasks[task.name] = task;
        
        // Add to phase map
        m_phaseTaskMap[task.phase].push_back(task.name);
        
        std::cout << "[DEBUG] Registered task: " << task.name << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in RegisterTask: " << e.what() << std::endl;
        return false;
    }
}

bool CDatabaseInitializer::ValidateTask(const InitializationTask& task) const {
    if (task.name.empty()) {
        return false;
    }
    
    if (!task.initFunction && !task.loadFunction) {
        return false;
    }
    
    return true;
}

std::vector<std::string> CDatabaseInitializer::GetTasksForPhase(InitializationPhase phase) const {
    auto it = m_phaseTaskMap.find(phase);
    if (it != m_phaseTaskMap.end()) {
        return it->second;
    }
    return {};
}

bool CDatabaseInitializer::CalculateExecutionOrder() {
    try {
        std::cout << "[DEBUG] Calculating task execution order" << std::endl;
        
        m_initializationOrder.clear();
        
        // Simple dependency-based ordering
        // In a full implementation, this would use topological sorting
        
        // For now, use phase-based ordering
        std::vector<InitializationPhase> phaseOrder = {
            InitializationPhase::PreInit,
            InitializationPhase::DatabaseConnections,
            InitializationPhase::CoreManagers,
            InitializationPhase::EconomySystem,
            InitializationPhase::PvPSystem,
            InitializationPhase::GuildSystem,
            InitializationPhase::AutomineSystem,
            InitializationPhase::PostSystem,
            InitializationPhase::ItemSystem,
            InitializationPhase::LogSystem,
            InitializationPhase::Finalization
        };
        
        for (auto phase : phaseOrder) {
            auto phaseTasks = GetTasksForPhase(phase);
            m_initializationOrder.insert(m_initializationOrder.end(), 
                                       phaseTasks.begin(), phaseTasks.end());
        }
        
        std::cout << "[DEBUG] Task execution order calculated: " << m_initializationOrder.size() << " tasks" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CalculateExecutionOrder: " << e.what() << std::endl;
        return false;
    }
}

void CDatabaseInitializer::UpdateProgress(const std::string& taskName, InitializationPhase phase) {
    std::lock_guard<std::mutex> lock(m_progressMutex);
    
    m_progress.currentTaskName = taskName;
    if (phase != InitializationPhase::Complete) {
        m_progress.currentPhase = phase;
    }
}

void CDatabaseInitializer::NotifyProgress() {
    if (m_progressCallback) {
        try {
            m_progressCallback(m_progress);
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Exception in progress callback: " << e.what() << std::endl;
        }
    }
}

InitializationProgress CDatabaseInitializer::GetProgress() const {
    std::lock_guard<std::mutex> lock(m_progressMutex);
    return m_progress;
}

bool CDatabaseInitializer::IsInitializationComplete() const {
    return m_initializationComplete.load();
}

bool CDatabaseInitializer::HasInitializationFailed() const {
    return m_initializationFailed.load();
}

std::vector<std::string> CDatabaseInitializer::GetFailedTasks() const {
    std::lock_guard<std::mutex> lock(m_progressMutex);
    return m_progress.failedTaskNames;
}

void CDatabaseInitializer::Reset() {
    std::lock_guard<std::mutex> lock(m_taskMutex);
    
    // Reset all task states
    for (auto& pair : m_tasks) {
        auto& task = pair.second;
        task.isInitialized = false;
        task.hasFailed = false;
        task.lastError.clear();
        task.startTime = std::chrono::steady_clock::time_point{};
        task.endTime = std::chrono::steady_clock::time_point{};
    }
    
    // Reset progress
    m_progress = InitializationProgress{};
    m_progress.totalTasks = m_tasks.size();
    
    // Reset state
    m_isInitializing = false;
    m_initializationComplete = false;
    m_initializationFailed = false;
    
    std::cout << "[DEBUG] Database initializer reset" << std::endl;
}

// Factory implementation
std::unique_ptr<CDatabaseInitializer> CDatabaseInitializerFactory::CreateStandardInitializer() {
    return std::make_unique<CDatabaseInitializer>();
}

std::unique_ptr<CDatabaseInitializer> CDatabaseInitializerFactory::CreateCustomInitializer(
    const std::vector<InitializationTask>& tasks) {
    auto initializer = std::make_unique<CDatabaseInitializer>();
    
    for (const auto& task : tasks) {
        initializer->RegisterTask(task);
    }
    
    return initializer;
}

// Utility functions
namespace InitializationUtils {
    std::string PhaseToString(InitializationPhase phase) {
        switch (phase) {
            case InitializationPhase::PreInit: return "PreInit";
            case InitializationPhase::DatabaseConnections: return "DatabaseConnections";
            case InitializationPhase::CoreManagers: return "CoreManagers";
            case InitializationPhase::EconomySystem: return "EconomySystem";
            case InitializationPhase::PvPSystem: return "PvPSystem";
            case InitializationPhase::GuildSystem: return "GuildSystem";
            case InitializationPhase::AutomineSystem: return "AutomineSystem";
            case InitializationPhase::PostSystem: return "PostSystem";
            case InitializationPhase::ItemSystem: return "ItemSystem";
            case InitializationPhase::LogSystem: return "LogSystem";
            case InitializationPhase::Finalization: return "Finalization";
            case InitializationPhase::Complete: return "Complete";
            default: return "Unknown";
        }
    }
    
    std::string PriorityToString(InitializationPriority priority) {
        switch (priority) {
            case InitializationPriority::Critical: return "Critical";
            case InitializationPriority::High: return "High";
            case InitializationPriority::Medium: return "Medium";
            case InitializationPriority::Low: return "Low";
            case InitializationPriority::Optional: return "Optional";
            default: return "Unknown";
        }
    }
}

} // namespace Database
} // namespace NexusProtection
