/*
 * Function: ?SendWebBattleEndInfo@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E6D50
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::SendWebBattleEndInfo(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v5; // [sp+35h] [bp-43h]@4
  char v6; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  GUILD_BATTLE::CNormalGuildBattle *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  szMsg = v9->m_dwID % 0x17;
  v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v9->m_k1P);
  v6 = 3;
  pbyType = 51;
  v8 = 17;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, &szMsg, 3u);
}
