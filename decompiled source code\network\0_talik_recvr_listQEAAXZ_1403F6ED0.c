/*
 * Function: ??0_talik_recvr_list@@QEAA@XZ
 * Address: 0x1403F6ED0
 */

void __fastcall _talik_recvr_list::_talik_recvr_list(_talik_recvr_list *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-28h]@1
  _talik_recvr_list *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = _talik_recvr_list::size(v5);
  memset_0(v5, 0, v3);
}
