/*
 * Function: ??_G?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x14055C050
 */

void *__fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::`scalar deleting destructor'(void *a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::~IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>();
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
