/*
 * Function: ?MessageRepresentativeLength@?$DL_SignatureSchemeBase@VPK_Verifier@CryptoPP@@V?$DL_PublicKey@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C3A0
 */

unsigned __int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::MessageRepresentativeLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Verifier,CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>>::MessageRepresentativeBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
