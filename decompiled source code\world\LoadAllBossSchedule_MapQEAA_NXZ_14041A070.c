/*
 * Function: ?LoadAll@BossSchedule_Map@@QEAA_NXZ
 * Address: 0x14041A070
 */

char __fastcall BossSchedule_Map::LoadAll(BossSchedule_Map *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int dwIndex; // [sp+20h] [bp-28h]@9
  INI_Section *pSection; // [sp+28h] [bp-20h]@11
  BossSchedule **v7; // [sp+30h] [bp-18h]@9
  unsigned __int64 v8; // [sp+38h] [bp-10h]@9
  BossSchedule_Map *pMapSchedule; // [sp+50h] [bp+8h]@1

  pMapSchedule = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  BossSchedule_Map::Clear(pMapSchedule);
  pMapSchedule->m_nCount = CIniFile::GetSectionSize(&pMapSchedule->m_INIFile);
  if ( pMapSchedule->m_nCount > 0 )
  {
    if ( pMapSchedule->m_pSystem && pMapSchedule->m_pSystem->m_pMapOper )
    {
      v8 = pMapSchedule->m_nCount;
      v7 = (BossSchedule **)operator new[](saturated_mul(8ui64, v8));
      pMapSchedule->m_ScheduleList = v7;
      memset_0(pMapSchedule->m_ScheduleList, 0, 8i64 * pMapSchedule->m_nCount);
      for ( dwIndex = 0; dwIndex < pMapSchedule->m_nCount; ++dwIndex )
      {
        pSection = CIniFile::GetSection(&pMapSchedule->m_INIFile, dwIndex);
        if ( !pSection )
          return 0;
        pMapSchedule->m_ScheduleList[dwIndex] = CBossMonsterScheduleSystem::LoadSchedule(
                                                  pMapSchedule->m_pSystem,
                                                  pMapSchedule,
                                                  pSection);
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
