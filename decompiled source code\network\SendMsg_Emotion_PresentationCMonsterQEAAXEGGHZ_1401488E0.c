/*
 * Function: ?SendMsg_Emotion_Presentation@CMonster@@QEAAXEGGH@Z
 * Address: 0x1401488E0
 */

void __fastcall CMonster::SendMsg_Emotion_Presentation(CMonster *this, char bylhw, unsigned __int16 wSubIndex, unsigned __int16 wRandIndex, int nSendTargetIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  char v9; // [sp+3Ch] [bp-4Ch]@4
  unsigned __int16 v10; // [sp+3Dh] [bp-4Bh]@4
  unsigned __int16 v11; // [sp+3Fh] [bp-49h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v13; // [sp+65h] [bp-23h]@4
  CMonster *v14; // [sp+90h] [bp+8h]@1

  v14 = this;
  v5 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  *(_DWORD *)szMsg = v14->m_dwObjSerial;
  v9 = bylhw;
  v10 = wSubIndex;
  v11 = wRandIndex;
  pbyType = 11;
  v13 = -102;
  if ( nSendTargetIndex == -1 )
    CGameObject::CircleReport((CGameObject *)&v14->vfptr, &pbyType, szMsg, 9, 0);
  else
    CNetProcess::LoadSendMsg(unk_1414F2088, nSendTargetIndex, &pbyType, szMsg, 9u);
}
