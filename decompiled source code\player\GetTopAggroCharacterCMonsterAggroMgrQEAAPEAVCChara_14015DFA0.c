/*
 * Function: ?GetTopAggroCharacter@CMonsterAggroMgr@@QEAAPEAVCCharacter@@XZ
 * Address: 0x14015DFA0
 */

CCharacter *__fastcall CMonsterAggroMgr::GetTopAggroCharacter(CMonsterAggroMgr *this)
{
  CCharacter *result; // rax@4

  if ( this->m_pTopAggroCharacter && this->m_pTopAggroCharacter->m_bLive && !this->m_pTopAggroCharacter->m_bCorpse )
  {
    result = this->m_pTopAggroCharacter;
  }
  else
  {
    this->m_pTopAggroCharacter = 0i64;
    result = 0i64;
  }
  return result;
}
