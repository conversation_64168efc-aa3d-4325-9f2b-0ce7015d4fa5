/*
 * Function: ?Create@CCircleZone@@QEAA_NPEAVCMapData@@E@Z
 * Address: 0x14012DA60
 */

char __fastcall CCircleZone::Create(CCircleZone *this, CMapData *pkMap, char byColor)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-68h]@1
  _object_create_setdata Dst; // [sp+28h] [bp-40h]@7
  CCircleZone *v8; // [sp+70h] [bp+8h]@1
  CMapData *v9; // [sp+78h] [bp+10h]@1
  char v10; // [sp+80h] [bp+18h]@1

  v10 = byColor;
  v9 = pkMap;
  v8 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v8->m_eState != -1 && pkMap )
  {
    _object_create_setdata::_object_create_setdata(&Dst);
    Dst.m_nLayerIndex = 0;
    Dst.m_pMap = v9;
    memcpy_0(Dst.m_fStartPos, v8->m_pkGoalPos->m_fCenterPos, 0xCui64);
    Dst.m_pRecordSet = 0i64;
    if ( CGameObject::Create((CGameObject *)&v8->vfptr, &Dst) )
    {
      v8->m_dwObjSerial = CCircleZone::ms_dwSerialCnt++;
      v8->m_byColor = v10;
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
