/*
 * Function: ?SendRegistItemSuccessResult@CUnmannedTraderUserInfo@@QEAAXKGPEAD@Z
 * Address: 0x140357BE0
 */

void __fastcall CUnmannedTraderUserInfo::SendRegistItemSuccessResult(CUnmannedTraderUserInfo *this, unsigned int dwLeftDalant, unsigned __int16 wInx, char *pLoadData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-A8h]@1
  char *v8; // [sp+30h] [bp-78h]@4
  _unmannedtrader_regist_item_success_result_zocl v9; // [sp+48h] [bp-60h]@4
  char pbyType; // [sp+84h] [bp-24h]@4
  char v11; // [sp+85h] [bp-23h]@4
  unsigned __int16 v12; // [sp+C0h] [bp+18h]@1

  v12 = wInx;
  v4 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = pLoadData;
  v9.wItemSerial = *((_WORD *)pLoadData + 1);
  v9.dwPrice = *((_DWORD *)pLoadData + 8);
  v9.dwRegedSerial = *((_DWORD *)pLoadData + 5);
  v9.dwListIndex = *((_DWORD *)pLoadData + 3);
  v9.dwTax = *((_DWORD *)pLoadData + 20);
  v9.dwLeftDalant = dwLeftDalant;
  v9.wSepaSerial = *((_WORD *)pLoadData + 4);
  v9.bySepaAmount = pLoadData[10];
  pbyType = 30;
  v11 = 28;
  v6 = _unmannedtrader_regist_item_success_result_zocl::size(&v9);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12, &pbyType, (char *)&v9, v6);
}
