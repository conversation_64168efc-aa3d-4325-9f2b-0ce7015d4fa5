/*
 * Function: ?ct_fullset@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140291C50
 */

char __fastcall ct_fullset(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int nLv; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      nLv = atoi(s_pwszDstCheat[0]);
      if ( nLv >= 2 && nLv <= 35 )
      {
        CPlayer::dev_lv(v6, nLv);
        CPlayer::dev_up_all(v6, 400000000);
        if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
          CPlayer::dev_dalant(v6, 0xFFFFFFFF);
        CPlayer::dev_loot_fullitem(v6, nLv);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
