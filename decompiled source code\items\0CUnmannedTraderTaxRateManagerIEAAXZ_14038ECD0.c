/*
 * Function: ??0CUnmannedTraderTaxRateManager@@IEAA@XZ
 * Address: 0x14038ECD0
 */

void __fastcall CUnmannedTraderTaxRateManager::CUnmannedTraderTaxRateManager(CUnmannedTraderTaxRateManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTaxRateManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_pkTimer = 0i64;
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(&v4->m_vecTRC);
}
