/*
 * Function: ?GetSignatureAlgorithm@?$DL_ObjectImpl@V?$DL_VerifierBase@VInteger@CryptoPP@@@CryptoPP@@U?$DL_SignatureSchemeOptions@UDSA@CryptoPP@@UDL_Keys_DSA@2@V?$DL_Algorithm_GDSA@VInteger@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@2@V?$DL_PublicKey_GFP@VDL_GroupParameters_DSA@CryptoPP@@@2@@CryptoPP@@MEBAAEBV?$DL_ElgamalLikeSignatureAlgorithm@VInteger@CryptoPP@@@2@XZ
 * Address: 0x1405633C0
 */

int CryptoPP::DL_ObjectImpl<CryptoPP::DL_VerifierBase<CryptoPP::Integer>,CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DSA,CryptoPP::DL_Keys_DSA,CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>,CryptoPP::DL_PublicKey_GFP<CryptoPP::DL_GroupParameters_DSA>>::GetSignatureAlgorithm()
{
  __int64 v0; // rax@1
  char v2; // [sp+20h] [bp-18h]@1
  unsigned __int8 v3; // [sp+21h] [bp-17h]@1

  memset(&v3, 0, sizeof(v3));
  LODWORD(v0) = CryptoPP::Singleton<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>>,0>::Singleton<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>>,0>(
                  &v2,
                  v3);
  return CryptoPP::Singleton<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>,CryptoPP::NewObject<CryptoPP::DL_Algorithm_GDSA<CryptoPP::Integer>>,0>::Ref(v0);
}
