/*
 * Function: ?SendMsg_AnimusFPInform@CPlayer@@QEAAXXZ
 * Address: 0x1400DBBF0
 */

void __fastcall CPlayer::SendMsg_AnimusFPInform(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@7
  __int16 v5; // [sp+36h] [bp-42h]@7
  char pbyType; // [sp+54h] [bp-24h]@7
  char v7; // [sp+55h] [bp-23h]@7
  CPlayer *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_pRecalledAnimusChar )
  {
    if ( v8->m_pRecalledAnimusItem )
    {
      *(_WORD *)szMsg = v8->m_pRecalledAnimusItem->m_wSerial;
      v5 = v8->m_pRecalledAnimusChar->m_nFP;
      pbyType = 22;
      v7 = 10;
      CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
    }
  }
}
