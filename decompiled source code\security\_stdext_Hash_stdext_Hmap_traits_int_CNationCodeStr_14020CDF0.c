/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int_CNationCodeStr_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationCodeStr_____ptr64____0___::insert_::_1_::dtor$14
 * Address: 0x14020CDF0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CNationCodeStr_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationCodeStr_____ptr64____0___::insert_::_1_::dtor_14(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 612) & 1 )
  {
    *(_DWORD *)(a2 + 612) &= 0xFFFFFFFE;
    std::pair<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>,bool>(*(std::pair<std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0>,bool> **)(a2 + 856));
  }
}
