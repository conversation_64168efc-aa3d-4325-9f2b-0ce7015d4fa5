/*
 * Function: ??1CBossMonsterScheduleSystem@@UEAA@XZ
 * Address: 0x1404186D0
 */

void __fastcall CBossMonsterScheduleSystem::~CBossMonsterScheduleSystem(CBossMonsterScheduleSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  BossSchedule_TBL *v4; // [sp+20h] [bp-28h]@5
  BossSchedule_TBL *v5; // [sp+28h] [bp-20h]@5
  __int64 v6; // [sp+30h] [bp-18h]@4
  void *v7; // [sp+38h] [bp-10h]@6
  CBossMonsterScheduleSystem *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -2i64;
  v8->vfptr = (US::AbstractThreadVtbl *)&CBossMonsterScheduleSystem::`vftable';
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::Terminate(
    (US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *)&v8->vfptr,
    0xFFFFFFFF);
  if ( v8->m_pCurTBL )
  {
    v5 = v8->m_pCurTBL;
    v4 = v5;
    if ( v5 )
      v7 = BossSchedule_TBL::`scalar deleting destructor'(v4, 1u);
    else
      v7 = 0i64;
  }
  US::CDynamicTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>::~CDynamicTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>(&v8->m_MSG_POOL);
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::~CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>((US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *)&v8->vfptr);
}
