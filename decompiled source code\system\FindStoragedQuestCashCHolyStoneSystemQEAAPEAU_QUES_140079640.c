/*
 * Function: ?FindStoragedQuestCash@CHolyStoneSystem@@QEAAPEAU_QUEST_CASH@@K@Z
 * Address: 0x140079640
 */

_QUEST_CASH *__fastcall CHolyStoneSystem::FindStoragedQuestCash(CHolyStoneSystem *this, unsigned int dwAvatorSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 5064; ++j )
  {
    if ( v7->m_cashQuest[j].dwAvatorSerial == dwAvatorSerial )
    {
      _QUEST_CASH::init(&v7->m_cashQuest[j]);
      return &v7->m_cashQuest[j];
    }
  }
  return 0i64;
}
