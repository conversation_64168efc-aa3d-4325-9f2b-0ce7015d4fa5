/*
 * Function: ?DiagRecWLog@CRFNewDatabase@@IEAAXFFPEAX@Z
 * Address: 0x140486420
 */

void __fastcall CRFNewDatabase::DiagRecWLog(CRFNewDatabase *this, __int16 sqlRet, __int16 HandleType, void *Handle)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-4E8h]@1
  SQLINTEGER *pfNativeError; // [sp+20h] [bp-4C8h]@9
  SQLWCHAR szSqlState; // [sp+48h] [bp-4A0h]@8
  SQLWCHAR v9; // [sp+80h] [bp-468h]@8
  SQLINTEGER v10; // [sp+494h] [bp-54h]@8
  SQLSMALLINT v11; // [sp+4B4h] [bp-34h]@6
  int j; // [sp+4C4h] [bp-24h]@6
  unsigned __int64 v13; // [sp+4D0h] [bp-18h]@4
  CRFNewDatabase *v14; // [sp+4F0h] [bp+8h]@1
  __int16 v15; // [sp+500h] [bp+18h]@1
  SQLHANDLE handle; // [sp+508h] [bp+20h]@1

  handle = Handle;
  v15 = HandleType;
  v14 = this;
  v4 = &v6;
  for ( i = 312i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( sqlRet == 1 || sqlRet == -1 )
  {
    v11 = 0;
    for ( j = 1; j < 11 && !SQLGetDiagRecW_0(v15, handle, j, &szSqlState, &v10, &v9, 1024, &v11); ++j )
    {
      pfNativeError = (SQLINTEGER *)&v9;
      CRFNewDatabase::ErrFmtLog(v14, L"SqlState:%s, NativeError:%d  Msg:%s ", &szSqlState, (unsigned int)v10);
    }
  }
}
