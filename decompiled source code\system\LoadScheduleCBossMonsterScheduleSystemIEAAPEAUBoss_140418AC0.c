/*
 * Function: ?LoadSchedule@CBossMonsterScheduleSystem@@IEAAPEAUBossSchedule@@PEAUBossSchedule_Map@@PEAUINI_Section@@@Z
 * Address: 0x140418AC0
 */

BossSchedule *__fastcall CBossMonsterScheduleSystem::LoadSchedule(CBossMonsterScheduleSystem *this, BossSchedule_Map *pMapSchedule, INI_Section *pSection)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  BossSchedule *v5; // rax@5
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v6; // rax@16
  const char *v7; // rax@16
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v8; // rax@16
  const char *v9; // rax@16
  __int64 v10; // rax@27
  __int64 v11; // [sp+0h] [bp-138h]@1
  CMapData *v12; // [sp+20h] [bp-118h]@8
  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > > stringlist; // [sp+38h] [bp-100h]@14
  int v14; // [sp+64h] [bp-D4h]@16
  int v15; // [sp+68h] [bp-D0h]@16
  INI_Key *v16; // [sp+70h] [bp-C8h]@16
  INI_Key *v17; // [sp+78h] [bp-C0h]@18
  int v18; // [sp+80h] [bp-B8h]@20
  ATL::CTime result; // [sp+98h] [bp-A0h]@20
  _mon_block *v20; // [sp+A8h] [bp-90h]@20
  int *v21; // [sp+B0h] [bp-88h]@22
  int v22; // [sp+B8h] [bp-80h]@22
  _mon_active *v23; // [sp+C0h] [bp-78h]@24
  char (*_Dest)[64]; // [sp+C8h] [bp-70h]@29
  BossSchedule *v25; // [sp+D0h] [bp-68h]@15
  BossSchedule *v26; // [sp+D8h] [bp-60h]@17
  BossSchedule *v27; // [sp+E0h] [bp-58h]@19
  BossSchedule *v28; // [sp+E8h] [bp-50h]@21
  BossSchedule *v29; // [sp+F0h] [bp-48h]@23
  BossSchedule *v30; // [sp+F8h] [bp-40h]@25
  __int64 v31; // [sp+100h] [bp-38h]@29
  BossSchedule *v32; // [sp+108h] [bp-30h]@26
  BossSchedule *v33; // [sp+110h] [bp-28h]@29
  __int64 v34; // [sp+118h] [bp-20h]@4
  __int64 v35; // [sp+120h] [bp-18h]@27
  CBossMonsterScheduleSystem *v36; // [sp+140h] [bp+8h]@1
  BossSchedule_Map *v37; // [sp+148h] [bp+10h]@1
  INI_Section *strSrc; // [sp+150h] [bp+18h]@1

  strSrc = pSection;
  v37 = pMapSchedule;
  v36 = this;
  v3 = &v11;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v34 = -2i64;
  if ( pSection )
  {
    if ( pMapSchedule )
    {
      v12 = CMapOperation::GetMap(v36->m_pMapOper, pMapSchedule->m_strMap);
      if ( v12 )
      {
        if ( v12->m_pMapSet->m_nMapType )
        {
          v5 = 0i64;
        }
        else if ( _LAYER_SET::IsActiveLayer(v12->m_ls) )
        {
          std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
          if ( SplitString(strSrc->m_strSection, "_", &stringlist) == 3 )
          {
            v6 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
                   &stringlist,
                   1ui64);
            LODWORD(v7) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v6);
            v14 = atoi(v7);
            v8 = std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::operator[](
                   &stringlist,
                   2ui64);
            LODWORD(v9) = std::basic_string<char,std::char_traits<char>,std::allocator<char>>::c_str(v8);
            v15 = atoi(v9);
            v16 = INI_Section::GetKey(strSrc, strKey);
            if ( v16 )
            {
              v17 = INI_Section::GetKey(strSrc, off_140973118);
              if ( v17 )
              {
                v18 = BossSchedule::Make_LiveCount(v17->m_strValue);
                BossSchedule::Make_LastTimeRespawnSystemTime(&result, v16->m_strValue);
                v20 = 0i64;
                if ( v14 < v12->m_nMonBlockNum )
                {
                  v20 = &v12->m_pMonBlock[v14];
                  v21 = &v12->m_ls->m_pMB->m_nBlockNum;
                  v22 = CRecordData::GetRecordNum((CRecordData *)(*((_QWORD *)v21 + 1) + 176i64 * v14));
                  if ( v15 < v22 )
                  {
                    v23 = &v12->m_ls->m_MonAct[v14][v15];
                    if ( v23 )
                    {
                      v32 = (BossSchedule *)operator new(0xB0ui64);
                      if ( v32 )
                      {
                        BossSchedule::BossSchedule(v32);
                        v35 = v10;
                      }
                      else
                      {
                        v35 = 0i64;
                      }
                      v31 = v35;
                      _Dest = (char (*)[64])v35;
                      strcpy_s<64>((char (*)[64])(v35 + 64), v23->m_pActRec->m_strCode);
                      *(_QWORD *)&(*_Dest)[128] = v20;
                      *(_QWORD *)&(*_Dest)[136] = v23;
                      *(_DWORD *)&(*_Dest)[144] = v14;
                      *(_DWORD *)&(*_Dest)[148] = v15;
                      *(_WORD *)&(*_Dest)[160] = v18;
                      *(ATL::CTime *)&(*_Dest)[152] = result;
                      *(_QWORD *)&(*_Dest)[168] = v37;
                      sprintf_s<64>(_Dest, "BL_%d_%d", *(_DWORD *)&(*_Dest)[144], *(_DWORD *)&(*_Dest)[148]);
                      _mon_active::SetBossSchedule(v23, (BossSchedule *)_Dest);
                      v33 = (BossSchedule *)_Dest;
                      std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
                      v5 = v33;
                    }
                    else
                    {
                      v30 = 0i64;
                      std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
                      v5 = v30;
                    }
                  }
                  else
                  {
                    v29 = 0i64;
                    std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
                    v5 = v29;
                  }
                }
                else
                {
                  v28 = 0i64;
                  std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
                  v5 = v28;
                }
              }
              else
              {
                v27 = 0i64;
                std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
                v5 = v27;
              }
            }
            else
            {
              v26 = 0i64;
              std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
              v5 = v26;
            }
          }
          else
          {
            v25 = 0i64;
            std::vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>::~vector<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(&stringlist);
            v5 = v25;
          }
        }
        else
        {
          v5 = 0i64;
        }
      }
      else
      {
        v5 = 0i64;
      }
    }
    else
    {
      v5 = 0i64;
    }
  }
  else
  {
    v5 = 0i64;
  }
  return v5;
}
