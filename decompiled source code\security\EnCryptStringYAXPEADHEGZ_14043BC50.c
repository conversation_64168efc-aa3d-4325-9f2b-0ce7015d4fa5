/*
 * Function: ?EnCryptString@@YAXPEADHEG@Z
 * Address: 0x14043BC50
 */

void __fastcall EnCryptString(char *pStr, int nSize, char byPlus, unsigned __int16 wCryptKey)
{
  int *v4; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  char *v7; // [sp+20h] [bp+8h]@1

  v7 = pStr;
  v4 = &j;
  for ( i = 4i64; i; --i )
  {
    *v4 = -858993460;
    ++v4;
  }
  for ( j = 0; j < nSize; ++j )
  {
    *v7 ^= wCryptKey;
    *v7++ += byPlus;
  }
}
