/*
 * Function: ?SendMsg_StoneAlterOper@CHolyStone@@QEAAXXZ
 * Address: 0x140137CD0
 */

void __fastcall CHolyStone::SendMsg_StoneAlterOper(CHolyStone *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  bool v5; // [sp+38h] [bp-40h]@4
  __int16 v6; // [sp+39h] [bp-3Fh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CHolyStone *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = v9->m_bOper;
  *(_DWORD *)szMsg = v9->m_dwObjSerial;
  v6 = (*(int (__fastcall **)(CHolyStone *))&v9->vfptr->gap8[8])(v9);
  pbyType = 25;
  v8 = 14;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 7, 0);
}
