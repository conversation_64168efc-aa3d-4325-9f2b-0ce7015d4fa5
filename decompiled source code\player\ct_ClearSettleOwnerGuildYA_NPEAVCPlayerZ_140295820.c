/*
 * Function: ?ct_ClearSettleOwnerGuild@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140295820
 */

char __fastcall ct_ClearSettleOwnerGuild(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  AutoMineMachineMng *v3; // rax@9
  char result; // al@11
  AutoMineMachineMng *v5; // rax@19
  AutoMineMachineMng *v6; // rax@20
  AutoMineMachine *v7; // rax@20
  AutoMineMachineMng *v8; // rax@24
  AutoMineMachine *v9; // rax@24
  __int64 v10; // [sp+0h] [bp-58h]@1
  int nRaceCode; // [sp+20h] [bp-38h]@5
  int j; // [sp+24h] [bp-34h]@7
  int v13; // [sp+28h] [bp-30h]@13
  int v14; // [sp+2Ch] [bp-2Ch]@13
  CGuild *v15; // [sp+30h] [bp-28h]@20
  CGuild *v16; // [sp+38h] [bp-20h]@24
  unsigned int dw2ThGuildSerial; // [sp+40h] [bp-18h]@21
  unsigned int dw1ThGuildSerial; // [sp+44h] [bp-14h]@25

  v1 = &v10;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( s_nWordCount )
  {
    if ( s_nWordCount == 2 )
    {
      v13 = atoi(s_pwszDstCheat[0]);
      v14 = atoi(s_pwszDstCheat[0]);
      if ( v13 >= 0 && v13 < 3 )
      {
        if ( v14 >= 0 && v14 < 2 )
        {
          v5 = AutoMineMachineMng::Instance();
          AutoMineMachineMng::ChangeOwner(v5, v13, 0i64, v14);
          if ( v14 )
          {
            v8 = AutoMineMachineMng::Instance();
            v9 = AutoMineMachineMng::GetMachine(v8, v13, 0);
            v16 = AutoMineMachine::GetOwnerGuild(v9);
            if ( v16 )
              dw1ThGuildSerial = v16->m_dwSerial;
            else
              dw1ThGuildSerial = 0;
            CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateSettlementOwner(&stru_1799C9AF8, v13, dw1ThGuildSerial, 0);
          }
          else
          {
            v6 = AutoMineMachineMng::Instance();
            v7 = AutoMineMachineMng::GetMachine(v6, v13, 1);
            v15 = AutoMineMachine::GetOwnerGuild(v7);
            if ( v15 )
              dw2ThGuildSerial = v15->m_dwSerial;
            else
              dw2ThGuildSerial = 0;
            CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateSettlementOwner(&stru_1799C9AF8, v13, 0, dw2ThGuildSerial);
          }
          CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, v13);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
    {
      for ( j = 0; j < 2; ++j )
      {
        v3 = AutoMineMachineMng::Instance();
        AutoMineMachineMng::ChangeOwner(v3, nRaceCode, 0i64, j);
      }
      CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateSettlementOwner(&stru_1799C9AF8, nRaceCode, 0, 0);
    }
    CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, -1);
    result = 1;
  }
  return result;
}
