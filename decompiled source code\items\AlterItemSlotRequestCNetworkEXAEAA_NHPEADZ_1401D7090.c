/*
 * Function: ?AlterItemSlotRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D7090
 */

char __fastcall CNetworkEX::AlterItemSlotRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v6; // rax@8
  char *v7; // rax@13
  char *v8; // rax@17
  char *v9; // rax@19
  __int64 v10; // [sp+0h] [bp-78h]@1
  int v11; // [sp+20h] [bp-58h]@13
  unsigned int v12; // [sp+28h] [bp-50h]@13
  int v13; // [sp+30h] [bp-48h]@13
  char *v14; // [sp+40h] [bp-38h]@4
  CPlayer *v15; // [sp+48h] [bp-30h]@4
  unsigned int j; // [sp+50h] [bp-28h]@9
  unsigned int v17; // [sp+54h] [bp-24h]@8
  int v18; // [sp+58h] [bp-20h]@13
  int v19; // [sp+5Ch] [bp-1Ch]@13
  int v20; // [sp+60h] [bp-18h]@17
  int v21; // [sp+64h] [bp-14h]@17
  int v22; // [sp+68h] [bp-10h]@19
  CNetworkEX *v23; // [sp+80h] [bp+8h]@1

  v23 = this;
  v3 = &v10;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = pBuf;
  v15 = &g_Player + n;
  if ( v15->m_bOper )
  {
    if ( *v14 && (signed int)(unsigned __int8)*v14 <= 100 )
    {
      for ( j = 0; (signed int)j < (unsigned __int8)*v14; ++j )
      {
        if ( v14[6 * j + 1] )
        {
          if ( v14[6 * j + 1] != 2 )
          {
            v22 = (unsigned __int8)v14[6 * j + 1];
            v9 = CPlayerDB::GetCharNameA(&v15->m_Param);
            v11 = v22;
            CLogFile::Write(
              &v23->m_LogFile,
              "odd.. %s: AlterItemSlotRequest() : pRecv->list[%d].byStorageIndex(%d)",
              v9,
              j);
            return 0;
          }
          if ( (signed int)(unsigned __int8)v14[6 * j + 6] >= 7 )
          {
            v20 = (unsigned __int8)v14[6 * j + 6];
            v21 = (unsigned __int8)v14[6 * j + 1];
            v8 = CPlayerDB::GetCharNameA(&v15->m_Param);
            v13 = v20;
            v12 = j;
            v11 = v21;
            CLogFile::Write(
              &v23->m_LogFile,
              "odd.. %s: AlterItemSlotRequest() : pRecv->list[%d].byStorageIndex(%d), pRecv->list[%d].byClientSlotIndex(%d)",
              v8,
              j);
            return 0;
          }
        }
        else if ( (signed int)(unsigned __int8)v14[6 * j + 6] >= 100 )
        {
          v18 = (unsigned __int8)v14[6 * j + 6];
          v19 = (unsigned __int8)v14[6 * j + 1];
          v7 = CPlayerDB::GetCharNameA(&v15->m_Param);
          v13 = v18;
          v12 = j;
          v11 = v19;
          CLogFile::Write(
            &v23->m_LogFile,
            "odd.. %s: AlterItemSlotRequest() : pRecv->list[%d].byStorageIndex(%d), pRecv->list[%d].byClientSlotIndex(%d)",
            v7,
            j);
          return 0;
        }
      }
      CPlayer::pc_AlterItemSlotRequest(v15, *v14, (_alter_item_slot_request_clzo::__list *)(v14 + 1));
      result = 1;
    }
    else
    {
      v17 = (unsigned __int8)*v14;
      v6 = CPlayerDB::GetCharNameA(&v15->m_Param);
      CLogFile::Write(&v23->m_LogFile, "odd.. %s: AlterItemSlotRequest() : pRecv->byNum(%d)", v6, v17);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
