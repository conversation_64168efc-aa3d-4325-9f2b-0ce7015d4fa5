/*
 * Function: _CUnmannedTraderSubClassFactory::Destroy_::_1_::dtor$1
 * Address: 0x140385A50
 */

void __fastcall CUnmannedTraderSubClassFactory::Destroy_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(a2 + 72));
}
