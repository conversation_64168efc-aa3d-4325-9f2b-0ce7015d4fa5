/*
 * Function: ?SetMemory@_STORAGE_LIST@@QEAAXPEAU_db_con@1@HHH@Z
 * Address: 0x14010EA40
 */

void __fastcall _STORAGE_LIST::SetMemory(_STORAGE_LIST *this, _STORAGE_LIST::_db_con *pList, int nListName, int nListNum, int nUsedNum)
{
  int *v5; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _STORAGE_LIST *v8; // [sp+20h] [bp+8h]@1

  v8 = this;
  v5 = &j;
  for ( i = 4i64; i; --i )
  {
    *v5 = -858993460;
    ++v5;
  }
  v8->m_pStorageList = pList;
  v8->m_nListCode = nListName;
  v8->m_nListNum = nListNum;
  if ( nUsedNum )
    v8->m_nUsedNum = nUsedNum;
  else
    v8->m_nUsedNum = nListNum;
  for ( j = 0; j < v8->m_nListNum; ++j )
  {
    v8->m_pStorageList[j].m_pInList = v8;
    v8->m_pStorageList[j].m_byStorageIndex = j;
  }
}
