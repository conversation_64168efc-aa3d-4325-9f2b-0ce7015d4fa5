/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@stdext@@YAPEAPEAVCUnmannedTraderSubClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@Z
 * Address: 0x14000C031
 */

CUnmannedTraderSubClassInfo **__fastcall stdext::_Unchecked_uninitialized_move<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, CUnmannedTraderSubClassInfo **_Dest, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderSubClassInfo * *,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
