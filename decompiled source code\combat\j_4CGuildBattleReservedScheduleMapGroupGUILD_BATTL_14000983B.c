/*
 * Function: j_??4CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAAEBV01@AEBV01@@Z
 * Address: 0x14000983B
 */

GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *__fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::operator=(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *kObj)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::operator=(this, kObj);
}
