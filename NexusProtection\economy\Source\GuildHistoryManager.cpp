#include "../Headers/GuildHistoryManager.h"
#include <filesystem>
#include <sstream>
#include <iomanip>
#include <cstdio>
#include <cstring>

namespace NexusProtection::Economy {

    // Global instance
    static std::unique_ptr<GuildHistoryManager> s_guildHistoryInstance;
    static std::mutex s_guildHistoryMutex;

    GuildHistoryManager::GuildHistoryManager() {
        m_currentTime = FormatTimestamp(std::chrono::system_clock::now());
    }

    GuildHistoryManager::~GuildHistoryManager() {
        Shutdown();
    }

    void GuildHistoryManager::Initialize() {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        
        // Create log directory
        try {
            std::filesystem::create_directories(m_logDirectory);
        }
        catch (const std::exception&) {
            // Directory creation failed, but continue
        }

        // Clear history
        m_transactionHistory.clear();
        m_transactionCount = 0;
        m_totalDalantFlow = 0.0;
        m_totalGoldFlow = 0.0;
    }

    void GuildHistoryManager::Shutdown() {
        std::lock_guard<std::mutex> historyLock(m_historyMutex);
        std::lock_guard<std::mutex> fileLock(m_fileMutex);
        
        // Clear transaction history
        m_transactionHistory.clear();
    }

    void GuildHistoryManager::PushMoney(const std::string& operatorName, uint32_t operatorSerial,
                                       int32_t pushDalant, int32_t pushGold,
                                       double totalDalant, double totalGold,
                                       const std::string& fileName) {
        GuildMoneyTransaction transaction;
        transaction.SetData(operatorName, operatorSerial, pushDalant, pushGold,
                          totalDalant, totalGold, true, fileName);

        // Add to history
        AddTransaction(transaction);

        // Write to file
        if (m_enableFileLogging) {
            std::string logMessage = FormatTransactionLog(transaction);
            WriteFile(fileName, logMessage);
        }
    }

    void GuildHistoryManager::PopMoney(const std::string& operatorName, uint32_t operatorSerial,
                                      int32_t popDalant, int32_t popGold,
                                      double totalDalant, double totalGold,
                                      const std::string& fileName) {
        GuildMoneyTransaction transaction;
        transaction.SetData(operatorName, operatorSerial, -popDalant, -popGold,
                          totalDalant, totalGold, false, fileName);

        // Add to history
        AddTransaction(transaction);

        // Write to file
        if (m_enableFileLogging) {
            std::string logMessage = FormatTransactionLog(transaction);
            WriteFile(fileName, logMessage);
        }
    }

    void GuildHistoryManager::WriteFile(const std::string& fileName, const std::string& data) {
        std::lock_guard<std::mutex> lock(m_fileMutex);

        try {
            // Ensure log directory exists
            EnsureLogDirectory(fileName);

            // Construct full file path
            std::string fullPath = m_logDirectory + fileName;

            // Open file in append mode
            std::ofstream file(fullPath, std::ios::app);
            if (file.is_open()) {
                file << data;
                file.close();
            }
        }
        catch (const std::exception&) {
            // File writing failed, but continue
        }
    }

    void GuildHistoryManager::SetCurrentTime(const std::string& timeStr) {
        m_currentTime = timeStr;
    }

    void GuildHistoryManager::AddTransaction(const GuildMoneyTransaction& transaction) {
        std::lock_guard<std::mutex> lock(m_historyMutex);

        // Add transaction to history
        m_transactionHistory.push_back(transaction);

        // Update statistics
        ++m_transactionCount;
        m_totalDalantFlow += std::abs(transaction.dalantAmount);
        m_totalGoldFlow += std::abs(transaction.goldAmount);

        // Limit history size
        if (m_transactionHistory.size() > m_maxHistorySize) {
            m_transactionHistory.erase(m_transactionHistory.begin());
        }
    }

    std::vector<GuildMoneyTransaction> GuildHistoryManager::GetTransactionHistory(size_t maxCount) const {
        std::lock_guard<std::mutex> lock(m_historyMutex);

        if (maxCount >= m_transactionHistory.size()) {
            return m_transactionHistory;
        }

        // Return the most recent transactions
        auto start = m_transactionHistory.end() - maxCount;
        return std::vector<GuildMoneyTransaction>(start, m_transactionHistory.end());
    }

    void GuildHistoryManager::ClearHistory() {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        
        m_transactionHistory.clear();
        m_transactionCount = 0;
        m_totalDalantFlow = 0.0;
        m_totalGoldFlow = 0.0;
    }

    size_t GuildHistoryManager::GetTransactionCount() const {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        return m_transactionCount;
    }

    double GuildHistoryManager::GetTotalDalantFlow() const {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        return m_totalDalantFlow;
    }

    double GuildHistoryManager::GetTotalGoldFlow() const {
        std::lock_guard<std::mutex> lock(m_historyMutex);
        return m_totalGoldFlow;
    }

    std::string GuildHistoryManager::FormatTransactionLog(const GuildMoneyTransaction& transaction) const {
        std::stringstream ss;
        
        // Format similar to original Korean log format
        if (transaction.isInput) {
            ss << "입금: ( " << transaction.operatorName << " , " << transaction.operatorSerial 
               << " ) push( D:" << transaction.dalantAmount << " , G:" << transaction.goldAmount 
               << " ) $D:" << std::fixed << std::setprecision(0) << transaction.totalDalantAfter
               << " $G:" << std::fixed << std::setprecision(0) << transaction.totalGoldAfter
               << " [" << m_currentTime << "]\r\n";
        } else {
            ss << "출금: ( " << transaction.operatorName << " , " << transaction.operatorSerial 
               << " ) pop( D:" << (-transaction.dalantAmount) << " , G:" << (-transaction.goldAmount)
               << " ) $D:" << std::fixed << std::setprecision(0) << transaction.totalDalantAfter
               << " $G:" << std::fixed << std::setprecision(0) << transaction.totalGoldAfter
               << " [" << m_currentTime << "]\r\n";
        }

        return ss.str();
    }

    std::string GuildHistoryManager::FormatTimestamp(const std::chrono::system_clock::time_point& time) const {
        auto timeT = std::chrono::system_clock::to_time_t(time);
        auto tm = *std::localtime(&timeT);
        
        std::stringstream ss;
        ss << std::put_time(&tm, "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }

    void GuildHistoryManager::EnsureLogDirectory(const std::string& fileName) {
        try {
            std::filesystem::path filePath(m_logDirectory + fileName);
            std::filesystem::create_directories(filePath.parent_path());
        }
        catch (const std::exception&) {
            // Directory creation failed, but continue
        }
    }

    // Legacy C interface implementations
    void GuildHistoryManager::PushMoney(char* operatorName, uint32_t operatorSerial,
                                       int32_t pushDalant, int32_t pushGold,
                                       long double totalDalant, long double totalGold,
                                       char* fileName) {
        if (!operatorName || !fileName) {
            return;
        }

        PushMoney(std::string(operatorName), operatorSerial, pushDalant, pushGold,
                 static_cast<double>(totalDalant), static_cast<double>(totalGold),
                 std::string(fileName));
    }

    void GuildHistoryManager::PopMoney(char* operatorName, uint32_t operatorSerial,
                                      int32_t popDalant, int32_t popGold,
                                      long double totalDalant, long double totalGold,
                                      char* fileName) {
        if (!operatorName || !fileName) {
            return;
        }

        PopMoney(std::string(operatorName), operatorSerial, popDalant, popGold,
                static_cast<double>(totalDalant), static_cast<double>(totalGold),
                std::string(fileName));
    }

    // Factory implementation
    std::unique_ptr<GuildHistoryManager> GuildHistoryManagerFactory::CreateManager() {
        return std::make_unique<GuildHistoryManager>();
    }

    std::unique_ptr<GuildHistoryManager> GuildHistoryManagerFactory::CreateManager(const std::string& logDirectory) {
        auto manager = std::make_unique<GuildHistoryManager>();
        manager->m_logDirectory = logDirectory;
        return manager;
    }

    GuildHistoryManager& GetGuildHistoryManager() {
        std::lock_guard<std::mutex> lock(s_guildHistoryMutex);
        if (!s_guildHistoryInstance) {
            s_guildHistoryInstance = std::make_unique<GuildHistoryManager>();
            s_guildHistoryInstance->Initialize();
        }
        return *s_guildHistoryInstance;
    }

} // namespace NexusProtection::Economy

// Legacy C interface implementation
extern "C" {
    // Global buffer for formatting log messages (as per original code)
    char sData_2[10000];

    void CMgrGuildHistory_push_money(CMgrGuildHistory* mgr, char* operatorName,
                                    unsigned int operatorSerial, int pushDalant, int pushGold,
                                    long double totalDalant, long double totalGold,
                                    char* fileName) {
        (void)mgr; // Suppress unused parameter warning

        auto& manager = NexusProtection::Economy::GetGuildHistoryManager();
        manager.PushMoney(operatorName, operatorSerial, pushDalant, pushGold,
                         totalDalant, totalGold, fileName);
    }

    void CMgrGuildHistory_pop_money(CMgrGuildHistory* mgr, char* operatorName,
                                   unsigned int operatorSerial, int popDalant, int popGold,
                                   long double totalDalant, long double totalGold,
                                   char* fileName) {
        (void)mgr; // Suppress unused parameter warning

        auto& manager = NexusProtection::Economy::GetGuildHistoryManager();
        manager.PopMoney(operatorName, operatorSerial, popDalant, popGold,
                        totalDalant, totalGold, fileName);
    }

    void CMgrGuildHistory_WriteFile(CMgrGuildHistory* mgr, char* fileName, char* data) {
        (void)mgr; // Suppress unused parameter warning

        if (!fileName || !data) {
            return;
        }

        auto& manager = NexusProtection::Economy::GetGuildHistoryManager();
        manager.WriteFile(std::string(fileName), std::string(data));
    }

    void CMgrGuildHistory_SetCurrentTime(CMgrGuildHistory* mgr, char* timeStr) {
        (void)mgr; // Suppress unused parameter warning

        if (!timeStr) {
            return;
        }

        auto& manager = NexusProtection::Economy::GetGuildHistoryManager();
        manager.SetCurrentTime(std::string(timeStr));
    }

    // Legacy function implementations that match the original decompiled code
    void __fastcall push_money_CMgrGuildHistory(void* this_ptr, char* operatorName,
                                               unsigned int operatorSerial, int pushDalant, int pushGold,
                                               long double totalDalant, long double totalGold,
                                               char* fileName) {
        (void)this_ptr; // Suppress unused parameter warning

        if (!operatorName || !fileName) {
            return;
        }

        // Clear the global buffer
        sData_2[0] = 0;

        // Format the log message (similar to original sprintf)
        std::snprintf(sData_2, sizeof(sData_2),
                     "입금: ( %s , %u ) push( D:%d , G:%d ) $D:%.0f $G:%.0f [%s]\r\n",
                     operatorName, operatorSerial, pushDalant, pushGold,
                     static_cast<double>(totalDalant), static_cast<double>(totalGold),
                     NexusProtection::Economy::GetGuildHistoryManager().GetCurrentTime().c_str());

        // Ensure null termination
        sData_2[sizeof(sData_2) - 1] = 0;

        // Write to file
        auto& manager = NexusProtection::Economy::GetGuildHistoryManager();
        manager.WriteFile(std::string(fileName), std::string(sData_2));
    }

    void __fastcall pop_money_CMgrGuildHistory(void* this_ptr, char* operatorName,
                                              unsigned int operatorSerial, int popDalant, int popGold,
                                              long double totalDalant, long double totalGold,
                                              char* fileName) {
        (void)this_ptr; // Suppress unused parameter warning

        if (!operatorName || !fileName) {
            return;
        }

        // Clear the global buffer
        sData_2[0] = 0;

        // Format the log message (similar to original sprintf)
        std::snprintf(sData_2, sizeof(sData_2),
                     "출금: ( %s , %u ) pop( D:%d , G:%d ) $D:%.0f $G:%.0f [%s]\r\n",
                     operatorName, operatorSerial, popDalant, popGold,
                     static_cast<double>(totalDalant), static_cast<double>(totalGold),
                     NexusProtection::Economy::GetGuildHistoryManager().GetCurrentTime().c_str());

        // Ensure null termination
        sData_2[sizeof(sData_2) - 1] = 0;

        // Write to file
        auto& manager = NexusProtection::Economy::GetGuildHistoryManager();
        manager.WriteFile(std::string(fileName), std::string(sData_2));
    }
}

// Global legacy compatibility
NexusProtection::Economy::GuildHistoryManager* g_pGuildHistoryManager = nullptr;
