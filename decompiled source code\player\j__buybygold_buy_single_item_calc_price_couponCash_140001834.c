/*
 * Function: j_?_buybygold_buy_single_item_calc_price_coupon@CashItemRemoteStore@@AEAAKPEAVCPlayer@@PEAU_request_csi_buy_clzo@@EHPEA_NAEAK@Z
 * Address: 0x140001834
 */

unsigned int __fastcall CashItemRemoteStore::_buybygold_buy_single_item_calc_price_coupon(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, char byOver<PERSON><PERSON>um, int nCsPrice, bool *bCouponUseCheck, unsigned int *dwDiscount)
{
  return CashItemRemoteStore::_buybygold_buy_single_item_calc_price_coupon(
           this,
           pOne,
           pRecv,
           byOverlapNum,
           nCsPrice,
           bCouponUseCheck,
           dwDiscount);
}
