/*
 * Function: ?GetSocket@CNetWorking@@QEAAPEAU_socket@@KK@Z
 * Address: 0x140481BD0
 */

_socket *__fastcall CNetWorking::GetSocket(CNetWorking *this, unsigned int dwProID, unsigned int dwSocketIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _socket *result; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CNetWorking *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( dwProID < v7->m_dwUseProcessNum )
    result = CNetSocket::GetSocket(&v7->m_Process[dwProID].m_NetSocket, dwSocketIndex);
  else
    result = 0i64;
  return result;
}
