/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCUnmannedTraderSchedule@@PEAV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@stdext@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00AEAV?$allocator@VCUnmannedTraderSchedule@@@std@@@Z
 * Address: 0x1400118BA
 */

CUnmannedTraderSchedule *__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::allocator<CUnmannedTraderSchedule> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
