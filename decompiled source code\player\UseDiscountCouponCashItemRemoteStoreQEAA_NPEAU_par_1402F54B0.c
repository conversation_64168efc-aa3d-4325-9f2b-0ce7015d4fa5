/*
 * Function: ?UseDiscountCoupon@CashItemRemoteStore@@QEAA_NPEAU_param_cash_update@@U_STORAGE_POS_INDIV@@PEAVCPlayer@@@Z
 * Address: 0x1402F54B0
 */

bool __fastcall CashItemRemoteStore::UseDiscountCoupon(CashItemRemoteStore *this, _param_cash_update *pBuyList, _STORAGE_POS_INDIV pCoupon, CPlayer *pOne)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-58h]@1
  _STORAGE_LIST *v8; // [sp+20h] [bp-38h]@4
  _STORAGE_LIST::_db_con *pCouponItem; // [sp+28h] [bp-30h]@6
  _base_fld *v10; // [sp+30h] [bp-28h]@8
  int v11; // [sp+38h] [bp-20h]@10
  int j; // [sp+3Ch] [bp-1Ch]@10
  int v13; // [sp+40h] [bp-18h]@13
  int l; // [sp+44h] [bp-14h]@15
  int v15; // [sp+48h] [bp-10h]@17
  int k; // [sp+4Ch] [bp-Ch]@23
  _param_cash_update *v17; // [sp+68h] [bp+10h]@1
  _STORAGE_POS_INDIV v18; // [sp+70h] [bp+18h]@1
  CPlayer *v19; // [sp+78h] [bp+20h]@1

  v19 = pOne;
  v18 = pCoupon;
  v17 = pBuyList;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = pOne->m_Param.m_pStoragePtr[(unsigned __int8)pCoupon.byStorageCode];
  if ( v8 )
  {
    pCouponItem = _STORAGE_LIST::GetPtrFromSerial(v8, v18.wItemSerial);
    if ( pCouponItem )
    {
      v10 = CRecordData::GetRecord(
              (CRecordData *)&unk_1799C6AA0 + pCouponItem->m_byTableCode,
              pCouponItem->m_wItemIndex);
      if ( v10 )
      {
        v11 = 0;
        for ( j = 0; j < v17->in_nNum10; ++j )
          v11 += v17->in_item[(signed __int64)j].in_byOverlapNum * v17->in_item[(signed __int64)j].in_nPrice;
        v13 = 10 * *(_DWORD *)&v10[4].m_strCode[8];
        if ( *(_DWORD *)&v10[4].m_strCode[4] )
        {
          for ( k = 0; k < v17->in_nNum10; ++k )
          {
            v17->in_item[(signed __int64)k].in_nDiscount += *(_WORD *)&v10[4].m_strCode[8];
            v17->in_item[(signed __int64)k].in_nEventType = 4;
          }
          result = 1;
        }
        else if ( v13 >= v11 )
        {
          result = 0;
        }
        else
        {
          for ( l = 0; l < v17->in_nNum10; ++l )
          {
            v15 = v17->in_item[(signed __int64)l].in_byOverlapNum * v17->in_item[(signed __int64)l].in_nPrice;
            if ( v15 > v13 && !v17->in_item[(signed __int64)l].in_bIsApplyCoupon )
            {
              v17->in_item[(signed __int64)l].in_nDiscount = *(_WORD *)&v10[4].m_strCode[8];
              v17->in_item[(signed __int64)l].in_bIsApplyCoupon = 1;
              v17->in_item[(signed __int64)l].in_nEventType = 4;
              CMgrAvatorItemHistory::coupon_use_buy_item(
                &CPlayer::s_MgrItemHistory,
                pCouponItem,
                v17->in_item[(signed __int64)l].in_strItemCode,
                v19->m_szItemHistoryFileName);
              return 1;
            }
          }
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
