/*
 * Function: j_??$_Ptr_cat@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderSubClassInfo@@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@0@AEAPEAPEAVCUnmannedTraderSubClassInfo@@@Z
 * Address: 0x14000CBD0
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *>(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *__formal, CUnmannedTraderSubClassInfo ***a2)
{
  return std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *>(
           __formal,
           a2);
}
