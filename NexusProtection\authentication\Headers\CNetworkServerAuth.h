#pragma once

/**
 * @file CNetworkServerAuth.h
 * @brief Network Server Login Authentication System
 * 
 * Provides secure authentication for control server and web agent server login operations.
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * Original files:
 * - LogInControllServerCNetworkEXAEAA_NHPEADZ_1401C7250.c
 * - LogInWebAgentServerCNetworkEXAEAA_NHPEADZ_1401DA860.c
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <unordered_map>
#include <vector>
#include <functional>

namespace NexusProtection::Authentication {

    /**
     * @brief Network server authentication result
     */
    enum class NetworkServerAuthResult : uint8_t {
        Success = 0,
        InvalidParameters = 1,
        InvalidBuffer = 2,
        ServerAlreadyConnected = 3,
        AuthenticationFailed = 4,
        NetworkError = 5,
        SystemError = 6,
        NotInitialized = 7
    };

    /**
     * @brief Server type enumeration
     */
    enum class ServerType : uint8_t {
        Unknown = 0,
        ControlServer = 1,
        WebAgentServer = 2
    };

    /**
     * @brief Server authentication state
     */
    enum class ServerAuthState : uint8_t {
        Disconnected = 0,
        Connecting = 1,
        Connected = 2,
        Authenticated = 3,
        Error = 4
    };

    /**
     * @brief Network message types for server authentication
     */
    enum class NetworkMessageType : uint8_t {
        ControlServerLogin = 54,    // pbyType = 54 for control server
        WebAgentServerLogin = 51,   // pbyType = 51 for web agent server
        AuthenticationResponse = 1,
        AuthenticationFailure = 2
    };

    /**
     * @brief Server connection information
     */
    struct ServerConnectionInfo {
        int sessionId{0};
        ServerType serverType{ServerType::Unknown};
        ServerAuthState authState{ServerAuthState::Disconnected};
        std::chrono::steady_clock::time_point connectTime;
        std::chrono::steady_clock::time_point lastActivity;
        std::string serverAddress;
        uint16_t serverPort{0};
        uint32_t authenticationKey{0};
        bool isAuthenticated{false};

        ServerConnectionInfo() = default;
        ServerConnectionInfo(int id, ServerType type) 
            : sessionId(id), serverType(type), connectTime(std::chrono::steady_clock::now()) {}

        bool IsValid() const;
        std::string ToString() const;
        void UpdateActivity();
    };

    /**
     * @brief Network authentication message structure
     */
    struct NetworkAuthMessage {
        uint8_t messageType{0};
        uint8_t version{1};
        uint8_t status{0};
        uint8_t reserved{0};
        
        NetworkAuthMessage() = default;
        NetworkAuthMessage(NetworkMessageType type, uint8_t stat = 0) 
            : messageType(static_cast<uint8_t>(type)), status(stat) {}

        bool IsValid() const;
        std::vector<uint8_t> Serialize() const;
        bool Deserialize(const std::vector<uint8_t>& data);
    };

    /**
     * @brief Forward declaration for CNetworkEX
     */
    class CNetworkEX;

    /**
     * @brief Forward declaration for CNetProcess
     */
    class CNetProcess {
    public:
        static void* LoadSendMsg(void* process, int sessionId, const void* messageType, 
                               const void* messageData, uint32_t messageSize);
    };

    /**
     * @brief Network Server Authentication Manager
     */
    class CNetworkServerAuth {
    public:
        CNetworkServerAuth();
        ~CNetworkServerAuth();

        // Core lifecycle
        bool Initialize();
        void Shutdown();
        bool LoadConfiguration();

        // Server authentication operations
        NetworkServerAuthResult AuthenticateControlServer(CNetworkEX* networkEX, int sessionId, char* buffer);
        NetworkServerAuthResult AuthenticateWebAgentServer(CNetworkEX* networkEX, int sessionId, char* buffer);

        // Connection management
        bool RegisterServerConnection(int sessionId, ServerType serverType, const std::string& address = "");
        bool UnregisterServerConnection(int sessionId);
        ServerConnectionInfo* GetServerConnection(int sessionId);
        std::vector<ServerConnectionInfo> GetActiveConnections() const;

        // Authentication validation
        bool ValidateAuthenticationBuffer(const char* buffer, size_t bufferSize) const;
        bool ValidateServerCredentials(ServerType serverType, const char* buffer) const;

        // Message handling
        bool SendAuthenticationResponse(CNetworkEX* networkEX, int sessionId, 
                                      NetworkMessageType messageType, uint8_t status);
        bool ProcessAuthenticationMessage(const NetworkAuthMessage& message, int sessionId);

        // Configuration and status
        bool IsInitialized() const { return m_isInitialized; }
        bool IsOperational() const { return m_isOperational; }
        size_t GetActiveConnectionCount() const;

        // Server state queries
        bool IsControlServerConnected() const;
        bool IsWebAgentServerConnected() const;

        // Statistics
        struct Statistics {
            uint32_t totalAuthenticationAttempts{0};
            uint32_t successfulAuthentications{0};
            uint32_t failedAuthentications{0};
            uint32_t controlServerConnections{0};
            uint32_t webAgentServerConnections{0};
            std::chrono::steady_clock::time_point startTime;
        };

        const Statistics& GetStatistics() const { return m_statistics; }
        void ResetStatistics();

        // Legacy compatibility
        bool LogInControllServer_Legacy(CNetworkEX* networkEX, int sessionId, char* buffer);
        bool LogInWebAgentServer_Legacy(CNetworkEX* networkEX, int sessionId, char* buffer);

    private:
        // Configuration
        bool m_isInitialized{false};
        bool m_isOperational{false};

        // Connection management
        std::unordered_map<int, std::unique_ptr<ServerConnectionInfo>> m_serverConnections;
        mutable std::mutex m_connectionsMutex;

        // Global server state (equivalent to original unk_* variables)
        bool m_controlServerConnected{false};    // unk_1799C9AE0
        int m_controlServerSessionId{0};         // unk_1799C9ADF
        bool m_webAgentServerConnected{false};   // unk_1799C9ADE
        int m_webAgentServerSessionId{0};        // unk_1799C9ADD
        mutable std::mutex m_serverStateMutex;

        // Network process handle (equivalent to unk_1414F2098)
        void* m_networkProcessHandle{nullptr};

        // Statistics
        Statistics m_statistics;
        mutable std::mutex m_statisticsMutex;

        // Internal methods
        bool ValidateParameters(CNetworkEX* networkEX, int sessionId, const char* buffer) const;
        bool ProcessControlServerAuthentication(int sessionId, const char* buffer);
        bool ProcessWebAgentServerAuthentication(int sessionId, const char* buffer);
        void UpdateStatistics(bool success, ServerType serverType);
        void LogAuthenticationEvent(const std::string& event, int sessionId, ServerType serverType, bool success);
        bool SendNetworkMessage(int sessionId, NetworkMessageType messageType, uint8_t status);

        // Buffer validation helpers
        bool ValidateControlServerBuffer(const char* buffer) const;
        bool ValidateWebAgentServerBuffer(const char* buffer) const;
        uint8_t GetBufferFirstByte(const char* buffer) const;

        // Connection state management
        void SetControlServerState(bool connected, int sessionId = 0);
        void SetWebAgentServerState(bool connected, int sessionId = 0);
    };

    /**
     * @brief Global instance access
     */
    CNetworkServerAuth& GetNetworkServerAuth();

    /**
     * @brief Utility functions
     */
    std::string NetworkServerAuthResultToString(NetworkServerAuthResult result);
    std::string ServerTypeToString(ServerType serverType);
    std::string ServerAuthStateToString(ServerAuthState state);
    std::string NetworkMessageTypeToString(NetworkMessageType messageType);

    /**
     * @brief Legacy C interface compatibility
     */
    extern "C" {
        struct CNetworkEX_Legacy {
            void* vfptr;
            // Additional fields would be defined based on actual structure
        };

        // Legacy function declarations
        char CNetworkEX_LogInControllServer(CNetworkEX_Legacy* networkEX, int sessionId, char* buffer);
        char CNetworkEX_LogInWebAgentServer(CNetworkEX_Legacy* networkEX, int sessionId, char* buffer);
        
        // Network process functions
        void* CNetProcess_LoadSendMsg(void* process, int sessionId, const void* messageType, 
                                    const void* messageData, uint32_t messageSize);
        
        // Server state management
        bool CNetworkServerAuth_IsControlServerConnected();
        bool CNetworkServerAuth_IsWebAgentServerConnected();
        int CNetworkServerAuth_GetControlServerSessionId();
        int CNetworkServerAuth_GetWebAgentServerSessionId();
    }

} // namespace NexusProtection::Authentication
