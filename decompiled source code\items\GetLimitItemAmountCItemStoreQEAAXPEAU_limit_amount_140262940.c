/*
 * Function: ?GetLimitItemAmount@CItemStore@@QEAAXPEAU_limit_amount_info@@@Z
 * Address: 0x140262940
 */

void __fastcall CItemStore::GetLimitItemAmount(CItemStore *this, _limit_amount_info *pAmountInfo)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CItemStore *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( v5->m_nLimitStorageItemNum > 0 )
  {
    for ( j = 0; j < v5->m_nLimitStorageItemNum; ++j )
    {
      if ( v5->m_pLimitStorageItem[j].bLoad )
      {
        pAmountInfo->ItemInfo[pAmountInfo->byItemNum].dwLimitItemIndex = v5->m_pLimitStorageItem[j].dwStorageIndex;
        pAmountInfo->ItemInfo[pAmountInfo->byItemNum++].wLimitNum = v5->m_pLimitStorageItem[j].nLimitNum;
      }
    }
  }
}
