/*
 * Function: ?OnCheckSession_FirstVerify@CHackShieldExSystem@@UEAA_NH@Z
 * Address: 0x140417250
 */

bool __fastcall CHackShieldExSystem::OnCheckSession_FirstVerify(CHackShieldExSystem *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  BASE_HACKSHEILD_PARAM *v6; // [sp+20h] [bp-18h]@4
  CHackShieldExSystem *v7; // [sp+40h] [bp+8h]@1
  int na; // [sp+48h] [bp+10h]@1

  na = n;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CHackShieldExSystem::GetParam(v7, n);
  if ( v6 )
    result = ((int (__fastcall *)(BASE_HACKSHEILD_PARAM *, _QWORD))v6->vfptr->OnCheckSession_FirstVerify)(
               v6,
               (unsigned int)na);
  else
    result = 0;
  return result;
}
