/*
 * Function: ?SendDQS_RoomUpdate@CGuildRoomInfo@@AEAAXXZ
 * Address: 0x1402E6790
 */

void __fastcall CGuildRoomInfo::SendDQS_RoomUpdate(CGuildRoomInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  unsigned int Dst; // [sp+34h] [bp-24h]@4
  CGuildRoomInfo *v5; // [sp+60h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(&Dst, 0, 4ui64);
  Dst = v5->m_dwGuildSerial;
  CMainThread::PushDQSData(&g_<PERSON>, 0xFFFFFFFF, 0i64, 72, (char *)&Dst, 4);
}
