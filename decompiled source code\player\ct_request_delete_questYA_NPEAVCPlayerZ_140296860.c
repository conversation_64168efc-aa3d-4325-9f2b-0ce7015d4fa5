/*
 * Function: ?ct_request_delete_quest@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296860
 */

char __fastcall ct_request_delete_quest(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@9
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    if ( s_nWordCount == 1 )
    {
      v5 = atoi(s_pwszDstCheat[0]);
      if ( (signed int)(unsigned __int8)v5 < 30 )
      {
        CPlayer::pc_QuestGiveupRequest(v6, v5);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
