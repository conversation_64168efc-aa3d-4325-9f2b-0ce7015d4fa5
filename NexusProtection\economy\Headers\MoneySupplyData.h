#pragma once

#include "EconomyTypes.h"
#include <array>
#include <cstring>

namespace NexusProtection::Economy {

    /**
     * @brief Money supply data structure for tracking economic statistics
     * 
     * This structure tracks various economic metrics including:
     * - Transaction amounts by type
     * - Level-based statistics for different transaction types
     * - Race-based statistics for fees and honor guild operations
     * - Unit buying statistics by level
     */
    struct MoneySupplyData {
        // Amount tracking for different money types
        std::array<uint32_t, static_cast<size_t>(MoneyType::Count)> amounts{};
        
        // Statistics for different money supply types
        std::array<MoneySupplyStatistics, static_cast<size_t>(MAX_TRADE_TYPES)> tradeStats{};
        
        // Fee statistics by race
        RaceStatistics feeRaceStats;
        
        // Fee statistics by level
        LevelStatistics feeLevelStats;
        
        // Honor guild statistics by trade type and race
        std::array<std::array<int32_t, static_cast<size_t>(RaceType::Count)>, 
                   static_cast<size_t>(MAX_HONOR_GUILD_TYPES)> honorGuildRaceStats{};
        
        // Unit buying statistics by level
        LevelStatistics buyUnitLevelStats;

        /**
         * @brief Initialize all data to zero
         */
        void Initialize() noexcept {
            amounts.fill(0);
            
            for (auto& stats : tradeStats) {
                stats.Reset();
            }
            
            feeRaceStats.Reset();
            feeLevelStats.Reset();
            buyUnitLevelStats.Reset();
            
            for (auto& raceArray : honorGuildRaceStats) {
                raceArray.fill(0);
            }
        }

        /**
         * @brief Get the size of this structure for serialization
         * @return Size in bytes
         */
        constexpr size_t GetSize() const noexcept {
            return sizeof(MoneySupplyData);
        }

        /**
         * @brief Get amount for specific money type
         * @param type The money type
         * @return Amount for the specified type
         */
        uint32_t GetAmount(MoneyType type) const noexcept {
            if (type < MoneyType::Count) {
                return amounts[static_cast<size_t>(type)];
            }
            return 0;
        }

        /**
         * @brief Add amount to specific money type
         * @param type The money type
         * @param amount Amount to add
         */
        void AddAmount(MoneyType type, uint32_t amount) noexcept {
            if (type < MoneyType::Count) {
                amounts[static_cast<size_t>(type)] += amount;
            }
        }

        /**
         * @brief Get trade statistics for specific type
         * @param tradeType The trade type index
         * @return Reference to trade statistics
         */
        const MoneySupplyStatistics& GetTradeStats(size_t tradeType) const noexcept {
            if (tradeType < tradeStats.size()) {
                return tradeStats[tradeType];
            }
            static const MoneySupplyStatistics empty{};
            return empty;
        }

        /**
         * @brief Get mutable trade statistics for specific type
         * @param tradeType The trade type index
         * @return Reference to trade statistics
         */
        MoneySupplyStatistics& GetTradeStats(size_t tradeType) noexcept {
            if (tradeType < tradeStats.size()) {
                return tradeStats[tradeType];
            }
            static MoneySupplyStatistics empty{};
            return empty;
        }

        /**
         * @brief Update honor guild race statistics
         * @param tradeType Trade type (0 or 1)
         * @param race Race type
         */
        void IncrementHonorGuildRace(uint8_t tradeType, RaceType race) noexcept {
            if (tradeType < MAX_HONOR_GUILD_TYPES && race < RaceType::Count) {
                ++honorGuildRaceStats[tradeType][static_cast<size_t>(race)];
            }
        }

        /**
         * @brief Get honor guild race count
         * @param tradeType Trade type (0 or 1)
         * @param race Race type
         * @return Count for the specified trade type and race
         */
        int32_t GetHonorGuildRaceCount(uint8_t tradeType, RaceType race) const noexcept {
            if (tradeType < MAX_HONOR_GUILD_TYPES && race < RaceType::Count) {
                return honorGuildRaceStats[tradeType][static_cast<size_t>(race)];
            }
            return 0;
        }
    };

    // Legacy C interface compatibility
    extern "C" {
        struct _MONEY_SUPPLY_DATA {
            uint32_t dwAmount[8];
            struct {
                int32_t nLv[4];
                int32_t nRace[3];
            } ms_data[4];
            int32_t nFeeRace[3];
            int32_t nFeeLv[4];
            int32_t nHonorGuildRace[2][3];
            int32_t nBuyUnitLv[4];
        };

        // Legacy function declarations
        void _MONEY_SUPPLY_DATA_init(_MONEY_SUPPLY_DATA* data);
        int _MONEY_SUPPLY_DATA_size(const _MONEY_SUPPLY_DATA* data);
    }

    /**
     * @brief Convert modern MoneySupplyData to legacy format
     * @param modern Modern data structure
     * @param legacy Legacy data structure to fill
     */
    void ConvertToLegacy(const MoneySupplyData& modern, _MONEY_SUPPLY_DATA& legacy) noexcept;

    /**
     * @brief Convert legacy format to modern MoneySupplyData
     * @param legacy Legacy data structure
     * @param modern Modern data structure to fill
     */
    void ConvertFromLegacy(const _MONEY_SUPPLY_DATA& legacy, MoneySupplyData& modern) noexcept;

} // namespace NexusProtection::Economy
