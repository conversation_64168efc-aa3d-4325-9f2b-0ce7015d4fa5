/*
 * Function: ?Select_AccountItemCharge_Extend@CRFWorldDatabase@@QEAA_NKPEAEPEAKPEA_K101PEAH@Z
 * Address: 0x1404A43E0
 */

char __fastcall CRFWorldDatabase::Select_AccountItemCharge_Extend(CRFWorldDatabase *this, unsigned int dwAccountSerial, char *pbyType, unsigned int *pdwItemCode_K, unsigned __int64 *pdwItemCode_D, unsigned int *pdwItemCode_U, char *pbyRace, unsigned int *pdwDBID, int *piTime)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v11; // ecx@5
  char result; // al@11
  __int64 v13; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@5
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@24
  SQLLEN v16; // [sp+38h] [bp-150h]@24
  __int16 v17; // [sp+44h] [bp-144h]@12
  char Dest; // [sp+60h] [bp-128h]@5
  int v19; // [sp+164h] [bp-24h]@4
  unsigned __int64 v20; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v21; // [sp+190h] [bp+8h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1
  unsigned int *v23; // [sp+1A8h] [bp+20h]@1

  v23 = pdwItemCode_K;
  TargetValue = pbyType;
  v21 = this;
  v9 = &v13;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v9 = -*********;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v20 = (unsigned __int64)&v13 ^ _security_cookie;
  v19 = 0;
  if ( (unsigned __int8)*pbyRace == 255 )
  {
    sprintf(
      &Dest,
      "{ CALL pSelect_TrunkItemChargeByType_Extend( %u, %d ) }",
      dwAccountSerial,
      (unsigned __int8)*pbyType);
  }
  else
  {
    v11 = (unsigned __int8)*pbyType;
    LODWORD(SQLStmt) = (unsigned __int8)*pbyRace;
    sprintf(&Dest, "{ CALL pSelect_TrunkItemChargeByTypeRace_Extend( %u, %u, %u ) }", dwAccountSerial, v11);
  }
  if ( v21->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v21->vfptr, &Dest);
  if ( v21->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v21->vfptr) )
  {
    v17 = SQLExecDirectA_0(v21->m_hStmtSelect, &Dest, -3);
    if ( v17 && v17 != 1 )
    {
      if ( v17 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v21->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v17, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v17, v21->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v17 = SQLFetch_0(v21->m_hStmtSelect);
      if ( v17 && v17 != 1 )
      {
        if ( v17 != 100 )
        {
          SQLStmt = v21->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v21->vfptr, v17, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v21->vfptr, v17, v21->m_hStmtSelect);
        }
        if ( v21->m_hStmtSelect )
          SQLCloseCursor_0(v21->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 1u, 4, pdwDBID, 0i64, &v16);
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 2u, -28, TargetValue, 0i64, &v16);
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 3u, 4, v23, 0i64, &v16);
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 4u, -25, pdwItemCode_D, 0i64, &v16);
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 5u, 4, pdwItemCode_U, 0i64, &v16);
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 6u, -28, pbyRace, 0i64, &v16);
        StrLen_or_IndPtr = &v16;
        SQLStmt = 0i64;
        v17 = SQLGetData_0(v21->m_hStmtSelect, 7u, 4, piTime, 0i64, &v16);
        if ( v17 == 100 )
        {
          if ( v21->m_hStmtSelect )
            SQLCloseCursor_0(v21->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v21->m_hStmtSelect )
            SQLCloseCursor_0(v21->m_hStmtSelect);
          if ( v21->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v21->vfptr, "%s Success", &Dest);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v21->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
