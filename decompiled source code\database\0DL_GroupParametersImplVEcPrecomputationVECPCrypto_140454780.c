/*
 * Function: ??0?$DL_GroupParametersImpl@V?$EcPrecomputation@VECP@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@2@V?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140454780
 */

void __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>(CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  __int64 v6; // [sp+28h] [bp-10h]@4
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = -2i64;
  v5 = 0;
  if ( a2 )
  {
    *(_QWORD *)&v7->gap8[0] = &CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::`vbtable';
    CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)&v7->gapE8[8]);
    v5 |= 1u;
  }
  CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::DL_GroupParameters<CryptoPP::ECPPoint>((CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *)&v7->vfptr);
  v7->vfptr = (CryptoPP::GeneratableCryptoMaterialVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::`vftable'{for `CryptoPP::GeneratableCryptoMaterial'};
  *(_QWORD *)&v7->gap8[*(_DWORD *)(*(_QWORD *)&v7->gap8[0] + 4i64)] = &CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::`vftable'{for `CryptoPP::CryptoMaterial'};
  *(_DWORD *)((char *)&v7->vfptr + *(_DWORD *)(*(_QWORD *)&v7->gap8[0] + 4i64) + 4) = 0;
  CryptoPP::EcPrecomputation<CryptoPP::ECP>::EcPrecomputation<CryptoPP::ECP>((CryptoPP::EcPrecomputation<CryptoPP::ECP> *)v7->gap18);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>(&v7->m_gpc);
}
