/*
 * Function: j_?IsExistGroupID@CUnmannedTraderGroupItemInfoTable@@QEAA_NEEEEAEAK@Z
 * Address: 0x14000451B
 */

bool __fastcall CUnmannedTraderGroupItemInfoTable::IsExistGroupID(CUnmannedTraderGroupItemInfoTable *this, char byDivision, char byClass, char bySubClass, char bySortType, unsigned int *dwListIndex)
{
  return CUnmannedTraderGroupItemInfoTable::IsExistGroupID(
           this,
           byDivision,
           byClass,
           bySubClass,
           bySortType,
           dwListIndex);
}
