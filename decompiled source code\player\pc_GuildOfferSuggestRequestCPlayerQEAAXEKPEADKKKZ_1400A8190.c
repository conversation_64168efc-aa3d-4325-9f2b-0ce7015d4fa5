/*
 * Function: ?pc_GuildOfferSuggestRequest@CPlayer@@QEAAXEKPEADKKK@Z
 * Address: 0x1400A8190
 */

void __fastcall CPlayer::pc_GuildOfferSuggestRequest(CPlayer *this, char byMatterType, unsigned int dwMatterDst, char *pwszComment, unsigned int dwMatterObj1, unsigned int dwMatterObj2, unsigned int dwMatterObj3)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-68h]@1
  char *pwszCommenta; // [sp+20h] [bp-48h]@20
  unsigned int v11; // [sp+28h] [bp-40h]@20
  unsigned int v12; // [sp+30h] [bp-38h]@20
  unsigned int v13; // [sp+38h] [bp-30h]@20
  int v14; // [sp+40h] [bp-28h]@4
  char v15; // [sp+44h] [bp-24h]@4
  CGuild *v16; // [sp+48h] [bp-20h]@4
  _guild_member_info *v17; // [sp+50h] [bp-18h]@12
  CPlayer *v18; // [sp+70h] [bp+8h]@1
  char v19; // [sp+78h] [bp+10h]@1
  unsigned int dwMemberSerial; // [sp+80h] [bp+18h]@1
  char *v21; // [sp+88h] [bp+20h]@1

  v21 = pwszComment;
  dwMemberSerial = dwMatterDst;
  v19 = byMatterType;
  v18 = this;
  v7 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v14 = 0;
  v15 = 0;
  v16 = v18->m_Param.m_pGuild;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v18->m_id.wIndex) == 99 )
  {
    v15 = 106;
  }
  else if ( v16 )
  {
    if ( v16->m_bNowProcessSgtMter )
    {
      v15 = 61;
    }
    else if ( v16->m_bRankWait )
    {
      v15 = 71;
    }
    else
    {
      v17 = CGuild::GetMemberFromSerial(v16, dwMemberSerial);
      if ( v17 )
      {
        if ( _guild_master_info::IsFill(&v16->m_MasterData) && v16->m_MasterData.dwSerial == dwMemberSerial )
        {
          v15 = 67;
        }
        else if ( !v16->m_bPossibleElectMaster )
        {
          v15 = -78;
        }
      }
      else
      {
        v15 = 63;
      }
    }
  }
  else
  {
    v15 = -54;
  }
  if ( !v15 )
  {
    v13 = dwMatterObj3;
    v12 = dwMatterObj2;
    v11 = dwMatterObj1;
    pwszCommenta = v21;
    if ( !CGuild::RegSuggestedMatter(
            v16,
            v18->m_dwObjSerial,
            v19,
            dwMemberSerial,
            v21,
            dwMatterObj1,
            dwMatterObj2,
            dwMatterObj3) )
      v15 = 64;
  }
  CPlayer::SendMsg_OfferSuggestResult(v18, v15);
}
