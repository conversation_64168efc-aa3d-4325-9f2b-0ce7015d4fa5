/*
 * Function: j_??1?$_Ranit@VCMoveMapLimitRightInfo@@_JPEBV1@AEBV1@@std@@QEAA@XZ
 * Address: 0x140011F4F
 */

void __fastcall std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>::~_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>(std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &> *this)
{
  std::_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>::~_Ranit<CMoveMapLimitRightInfo,__int64,CMoveMapLimitRightInfo const *,CMoveMapLimitRightInfo const &>(this);
}
