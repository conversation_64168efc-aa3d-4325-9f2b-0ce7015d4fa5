/*
 * Function: j_?SendMsg_TrunkPotionDivision@CPlayer@@QEAAXGGGGH@Z
 * Address: 0x140011BA3
 */

void __fastcall CPlayer::SendMsg_TrunkPotionDivision(CPlayer *this, unsigned __int16 wSerial, unsigned __int16 wParentAmount, unsigned __int16 wChildSerial, unsigned __int16 wChildAmount, int nRet)
{
  CPlayer::SendMsg_TrunkPotionDivision(this, wSerial, wParentAmount, wChildSerial, wChildAmount, nRet);
}
