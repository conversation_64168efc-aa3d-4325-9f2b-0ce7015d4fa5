/*
 * Function: ??0CNationSettingData@@QEAA@XZ
 * Address: 0x1402119C0
 */

void __fastcall CNationSettingData::CNationSettingData(CNationSettingData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CNationSettingData *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->vfptr = (CNationSettingDataVtbl *)&CNationSettingData::`vftable';
  std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>(&v4->m_vecCheatData);
  v4->m_bServiceMode = 0;
  v4->m_iNationCode = -1;
  v4->m_szNationCodeStr[0] = 0;
  v4->m_iANSICodePage = 0;
  v4->m_szCashDBName[0] = 0;
  v4->m_szCashDBIP[0] = 0;
  v4->m_szCashDBID[0] = 0;
  v4->m_szCashDBPW[0] = 0;
  v4->m_wCashDBPort = -4103;
  v4->m_eCashDBFlag = -1;
  v4->m_szWorldDBID[0] = 0;
  v4->m_szWorldDBPW[0] = 0;
  v4->m_wBillingForceCloseDelay = 300;
  strcpy_s(v4->m_szNoneString, 8ui64, "None");
  v4->m_pGameGuardSystem = 0i64;
  memset_0(v4->m_szVaildKey, 0, 0x11ui64);
}
