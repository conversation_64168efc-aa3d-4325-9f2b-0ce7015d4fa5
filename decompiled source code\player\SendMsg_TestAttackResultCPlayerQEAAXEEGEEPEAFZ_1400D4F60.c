/*
 * Function: ?SendMsg_TestAttackResult@CPlayer@@QEAAXEEGEEPEAF@Z
 * Address: 0x1400D4F60
 */

void __fastcall CPlayer::SendMsg_TestAttackResult(CPlayer *this, char byEffectCode, char byEffectIndex, unsigned __int16 wBulletItemIndex, char byEffectLv, char by<PERSON><PERSON>pon<PERSON><PERSON>, __int16 *pzTar)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v11; // [sp+3Ch] [bp-5Ch]@4
  char v12; // [sp+3Eh] [bp-5Ah]@4
  char v13; // [sp+3Fh] [bp-59h]@4
  char v14; // [sp+40h] [bp-58h]@4
  char v15; // [sp+41h] [bp-57h]@4
  __int16 v16; // [sp+42h] [bp-56h]@4
  __int16 v17; // [sp+44h] [bp-54h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v19; // [sp+65h] [bp-33h]@4
  unsigned __int64 v20; // [sp+80h] [bp-18h]@4
  CPlayer *v21; // [sp+A0h] [bp+8h]@1

  v21 = this;
  v7 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v20 = (unsigned __int64)&v9 ^ _security_cookie;
  *(_DWORD *)szMsg = v21->m_dwObjSerial;
  v12 = byEffectCode;
  v14 = byEffectLv;
  v13 = byEffectIndex;
  v11 = wBulletItemIndex;
  v15 = byWeaponPart;
  v16 = *pzTar;
  v17 = pzTar[1];
  pbyType = 5;
  v19 = 12;
  CGameObject::CircleReport((CGameObject *)&v21->vfptr, &pbyType, szMsg, 14, 1);
}
