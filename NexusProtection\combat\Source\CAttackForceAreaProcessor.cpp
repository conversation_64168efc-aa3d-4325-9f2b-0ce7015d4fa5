/*
 * CAttackForceAreaProcessor.cpp - Area and Flash Damage Processing Implementation
 * Handles area damage, flash damage, and sector damage calculations
 */

#include "../Headers/CAttackForceAreaProcessor.h"
#include "../Headers/CAttackForceIntegration.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CMonsterAttack.h"

#include <cmath>
#include <algorithm>
#include <stdexcept>

using namespace NexusProtection::Combat::AttackForceConstants;

namespace NexusProtection {
namespace Combat {

/**
 * Constructor
 */
CAttackForceAreaProcessor::CAttackForceAreaProcessor(std::shared_ptr<CAttackForceDamageCalculator> pDamageCalculator)
    : m_pDamageCalculator(pDamageCalculator)
    , m_nMaxTargets(50) {
    
    if (!pDamageCalculator) {
        throw std::invalid_argument("CAttackForceAreaProcessor: Damage calculator cannot be null");
    }
    
    Logger::Debug("CAttackForceAreaProcessor::CAttackForceAreaProcessor - Area processor initialized");
}

/**
 * Destructor
 */
CAttackForceAreaProcessor::~CAttackForceAreaProcessor() {
    Logger::Debug("CAttackForceAreaProcessor::~CAttackForceAreaProcessor - Area processor destroyed");
}

/**
 * Process area damage attack
 */
AreaDamageResult CAttackForceAreaProcessor::ProcessAreaDamage(CCharacter* pAttacker, const AreaDamageConfig& config, _attack_param* pParam) {
    AreaDamageResult result;
    
    if (!pAttacker || !pParam || !config.IsValid()) {
        result.errorMessage = "Invalid parameters for area damage";
        Logger::Error("CAttackForceAreaProcessor::ProcessAreaDamage - Invalid parameters");
        return result;
    }
    
    try {
        // Find targets in area (original lines 214-220)
        std::vector<CCharacter*> potentialTargets = FindTargetsInArea(config, pAttacker);
        
        // Limit number of targets
        int nMaxTargets = std::min(static_cast<int>(potentialTargets.size()), 
                                  std::min(config.nMaxTargets, m_nMaxTargets));
        
        // Calculate damage for each target
        for (int i = 0; i < nMaxTargets; ++i) {
            CCharacter* pTarget = potentialTargets[i];
            if (!IsValidTarget(pAttacker, pTarget)) {
                continue;
            }
            
            // Get character position for falloff calculation
            float fTargetX = 0.0f, fTargetY = 0.0f, fTargetZ = 0.0f;
            // In real implementation: pTarget->GetPosition(&fTargetX, &fTargetY, &fTargetZ);
            
            // Calculate damage falloff
            float fFalloff = config.GetDamageFalloff(fTargetX, fTargetY, fTargetZ);
            if (fFalloff <= 0.0f) {
                continue; // Target too far for damage
            }
            
            // Calculate damage for this target
            int nDamage = CalculateTargetDamage(pAttacker, pTarget, pParam, fFalloff);
            if (nDamage > 0) {
                result.AddTarget(pTarget, nDamage, fFalloff);
            }
        }
        
        result.bSuccess = true;
        Logger::Debug("CAttackForceAreaProcessor::ProcessAreaDamage - Hit %d targets, total damage: %d", 
                     result.nTargetsHit, result.nTotalDamage);
        
    } catch (const std::exception& e) {
        result.errorMessage = std::string("Area damage processing failed: ") + e.what();
        result.bSuccess = false;
        Logger::Error("CAttackForceAreaProcessor::ProcessAreaDamage - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Process flash damage attack
 */
AreaDamageResult CAttackForceAreaProcessor::ProcessFlashDamage(CCharacter* pAttacker, const FlashDamageConfig& config, _attack_param* pParam) {
    AreaDamageResult result;
    
    if (!pAttacker || !pParam || !config.IsValid()) {
        result.errorMessage = "Invalid parameters for flash damage";
        Logger::Error("CAttackForceAreaProcessor::ProcessFlashDamage - Invalid parameters");
        return result;
    }
    
    try {
        // Find targets in flash cone (original lines 196-207)
        std::vector<CCharacter*> potentialTargets = FindTargetsInFlash(config, pAttacker);
        
        // Limit number of targets
        int nMaxTargets = std::min(static_cast<int>(potentialTargets.size()), 
                                  std::min(config.nMaxTargets, m_nMaxTargets));
        
        // Calculate damage for each target
        for (int i = 0; i < nMaxTargets; ++i) {
            CCharacter* pTarget = potentialTargets[i];
            if (!IsValidTarget(pAttacker, pTarget)) {
                continue;
            }
            
            // Get character position for falloff calculation
            float fTargetX = 0.0f, fTargetY = 0.0f, fTargetZ = 0.0f;
            // In real implementation: pTarget->GetPosition(&fTargetX, &fTargetY, &fTargetZ);
            
            // Calculate damage falloff
            float fFalloff = config.GetDamageFalloff(fTargetX, fTargetY, fTargetZ);
            if (fFalloff <= 0.0f) {
                continue; // Target too far for damage
            }
            
            // Calculate damage for this target
            int nDamage = CalculateTargetDamage(pAttacker, pTarget, pParam, fFalloff);
            if (nDamage > 0) {
                result.AddTarget(pTarget, nDamage, fFalloff);
            }
        }
        
        result.bSuccess = true;
        Logger::Debug("CAttackForceAreaProcessor::ProcessFlashDamage - Hit %d targets, total damage: %d", 
                     result.nTargetsHit, result.nTotalDamage);
        
    } catch (const std::exception& e) {
        result.errorMessage = std::string("Flash damage processing failed: ") + e.what();
        result.bSuccess = false;
        Logger::Error("CAttackForceAreaProcessor::ProcessFlashDamage - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Process sector damage attack
 */
AreaDamageResult CAttackForceAreaProcessor::ProcessSectorDamage(CCharacter* pAttacker, const SectorDamageConfig& config, _attack_param* pParam) {
    AreaDamageResult result;
    
    if (!pAttacker || !pParam || !config.IsValid()) {
        result.errorMessage = "Invalid parameters for sector damage";
        Logger::Error("CAttackForceAreaProcessor::ProcessSectorDamage - Invalid parameters");
        return result;
    }
    
    try {
        // Find targets in sector
        std::vector<CCharacter*> potentialTargets = FindTargetsInSector(config, pAttacker);
        
        // Limit number of targets
        int nMaxTargets = std::min(static_cast<int>(potentialTargets.size()), 
                                  std::min(config.nMaxTargets, m_nMaxTargets));
        
        // Calculate damage for each target
        for (int i = 0; i < nMaxTargets; ++i) {
            CCharacter* pTarget = potentialTargets[i];
            if (!IsValidTarget(pAttacker, pTarget)) {
                continue;
            }
            
            // Get character position for falloff calculation
            float fTargetX = 0.0f, fTargetY = 0.0f, fTargetZ = 0.0f;
            // In real implementation: pTarget->GetPosition(&fTargetX, &fTargetY, &fTargetZ);
            
            // Calculate damage falloff
            float fFalloff = config.GetDamageFalloff(fTargetX, fTargetY, fTargetZ);
            if (fFalloff <= 0.0f) {
                continue; // Target too far for damage
            }
            
            // Calculate damage for this target
            int nDamage = CalculateTargetDamage(pAttacker, pTarget, pParam, fFalloff);
            if (nDamage > 0) {
                result.AddTarget(pTarget, nDamage, fFalloff);
            }
        }
        
        result.bSuccess = true;
        Logger::Debug("CAttackForceAreaProcessor::ProcessSectorDamage - Hit %d targets, total damage: %d", 
                     result.nTargetsHit, result.nTotalDamage);
        
    } catch (const std::exception& e) {
        result.errorMessage = std::string("Sector damage processing failed: ") + e.what();
        result.bSuccess = false;
        Logger::Error("CAttackForceAreaProcessor::ProcessSectorDamage - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Find targets in area
 */
std::vector<CCharacter*> CAttackForceAreaProcessor::FindTargetsInArea(const AreaDamageConfig& config, CCharacter* pAttacker) {
    std::vector<CCharacter*> targets;
    
    if (!pAttacker) {
        return targets;
    }
    
    try {
        // Get all characters in range
        std::vector<CCharacter*> allCharacters = GetCharactersInRange(
            config.fCenterX, config.fCenterY, config.fCenterZ, config.fRadius);
        
        // Filter targets that are in the area
        for (CCharacter* pCharacter : allCharacters) {
            if (pCharacter == pAttacker) {
                continue; // Don't hit self
            }
            
            // Get character position
            float fCharX = 0.0f, fCharY = 0.0f, fCharZ = 0.0f;
            // In real implementation: pCharacter->GetPosition(&fCharX, &fCharY, &fCharZ);
            
            // Check if character is in area
            if (config.IsInRange(fCharX, fCharY, fCharZ)) {
                // Check line of sight if obstacles matter
                if (!config.bIgnoreObstacles) {
                    if (!CheckLineOfSight(config.fCenterX, config.fCenterY, config.fCenterZ,
                                         fCharX, fCharY, fCharZ)) {
                        continue; // Blocked by obstacle
                    }
                }
                
                targets.push_back(pCharacter);
            }
        }
        
        // Sort by distance (closest first)
        std::sort(targets.begin(), targets.end(), [&config](CCharacter* a, CCharacter* b) {
            float fDistA = AreaDamageUtils::CalculateDistance3D(
                config.fCenterX, config.fCenterY, config.fCenterZ, 0.0f, 0.0f, 0.0f); // Would use actual positions
            float fDistB = AreaDamageUtils::CalculateDistance3D(
                config.fCenterX, config.fCenterY, config.fCenterZ, 0.0f, 0.0f, 0.0f); // Would use actual positions
            return fDistA < fDistB;
        });
        
        Logger::Debug("CAttackForceAreaProcessor::FindTargetsInArea - Found %zu targets in area", targets.size());
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::FindTargetsInArea - Exception: %s", e.what());
        targets.clear();
    }
    
    return targets;
}

/**
 * Find targets in flash cone
 */
std::vector<CCharacter*> CAttackForceAreaProcessor::FindTargetsInFlash(const FlashDamageConfig& config, CCharacter* pAttacker) {
    std::vector<CCharacter*> targets;
    
    if (!pAttacker) {
        return targets;
    }
    
    try {
        // Get all characters in range
        std::vector<CCharacter*> allCharacters = GetCharactersInRange(
            config.fOriginX, config.fOriginY, config.fOriginZ, config.fMaxDistance);
        
        // Filter targets that are in the flash cone
        for (CCharacter* pCharacter : allCharacters) {
            if (pCharacter == pAttacker) {
                continue; // Don't hit self
            }
            
            // Get character position
            float fCharX = 0.0f, fCharY = 0.0f, fCharZ = 0.0f;
            // In real implementation: pCharacter->GetPosition(&fCharX, &fCharY, &fCharZ);
            
            // Check if character is in flash cone
            if (config.IsInCone(fCharX, fCharY, fCharZ)) {
                // Check line of sight
                if (!CheckLineOfSight(config.fOriginX, config.fOriginY, config.fOriginZ,
                                     fCharX, fCharY, fCharZ)) {
                    continue; // Blocked by obstacle
                }
                
                targets.push_back(pCharacter);
            }
        }
        
        // Sort by distance (closest first)
        std::sort(targets.begin(), targets.end(), [&config](CCharacter* a, CCharacter* b) {
            float fDistA = AreaDamageUtils::CalculateDistance3D(
                config.fOriginX, config.fOriginY, config.fOriginZ, 0.0f, 0.0f, 0.0f); // Would use actual positions
            float fDistB = AreaDamageUtils::CalculateDistance3D(
                config.fOriginX, config.fOriginY, config.fOriginZ, 0.0f, 0.0f, 0.0f); // Would use actual positions
            return fDistA < fDistB;
        });
        
        Logger::Debug("CAttackForceAreaProcessor::FindTargetsInFlash - Found %zu targets in flash cone", targets.size());
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::FindTargetsInFlash - Exception: %s", e.what());
        targets.clear();
    }

    return targets;
}

/**
 * Find targets in sector
 */
std::vector<CCharacter*> CAttackForceAreaProcessor::FindTargetsInSector(const SectorDamageConfig& config, CCharacter* pAttacker) {
    std::vector<CCharacter*> targets;

    if (!pAttacker) {
        return targets;
    }

    try {
        // Get all characters in range
        std::vector<CCharacter*> allCharacters = GetCharactersInRange(
            config.fOriginX, config.fOriginY, config.fOriginZ, config.fRadius);

        // Filter targets that are in the sector
        for (CCharacter* pCharacter : allCharacters) {
            if (pCharacter == pAttacker) {
                continue; // Don't hit self
            }

            // Get character position
            float fCharX = 0.0f, fCharY = 0.0f, fCharZ = 0.0f;
            // In real implementation: pCharacter->GetPosition(&fCharX, &fCharY, &fCharZ);

            // Check if character is in sector
            if (config.IsInSector(fCharX, fCharY, fCharZ)) {
                // Check line of sight
                if (!CheckLineOfSight(config.fOriginX, config.fOriginY, config.fOriginZ,
                                     fCharX, fCharY, fCharZ)) {
                    continue; // Blocked by obstacle
                }

                targets.push_back(pCharacter);
            }
        }

        // Sort by distance (closest first)
        std::sort(targets.begin(), targets.end(), [&config](CCharacter* a, CCharacter* b) {
            float fDistA = AreaDamageUtils::CalculateDistance3D(
                config.fOriginX, config.fOriginY, config.fOriginZ, 0.0f, 0.0f, 0.0f); // Would use actual positions
            float fDistB = AreaDamageUtils::CalculateDistance3D(
                config.fOriginX, config.fOriginY, config.fOriginZ, 0.0f, 0.0f, 0.0f); // Would use actual positions
            return fDistA < fDistB;
        });

        Logger::Debug("CAttackForceAreaProcessor::FindTargetsInSector - Found %zu targets in sector", targets.size());

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::FindTargetsInSector - Exception: %s", e.what());
        targets.clear();
    }

    return targets;
}

/**
 * Calculate damage for target with falloff
 */
int CAttackForceAreaProcessor::CalculateTargetDamage(CCharacter* pAttacker, CCharacter* pTarget, _attack_param* pParam, float fFalloff) {
    if (!pAttacker || !pTarget || !pParam || !m_pDamageCalculator) {
        return 0;
    }

    try {
        // Set up damage calculation context
        DamageCalculationContext context;
        context.pAttacker = pAttacker;
        context.pTarget = pTarget;
        context.pParam = pParam;
        context.bUseEffBullet = false; // Would be determined from pParam
        context.bBackAttack = false;   // Would be determined from positions
        context.bCriticalHit = false;  // Would be determined by chance

        // Calculate base damage
        DetailedDamageResult damageResult = m_pDamageCalculator->CalculateDamage(context);

        // Apply falloff
        int nFinalDamage = static_cast<int>(damageResult.nFinalDamage * fFalloff);

        // Ensure minimum damage
        if (nFinalDamage < 1 && damageResult.nFinalDamage > 0) {
            nFinalDamage = 1;
        }

        Logger::Debug("CAttackForceAreaProcessor::CalculateTargetDamage - Target damage: %d (falloff: %.2f)",
                     nFinalDamage, fFalloff);

        return nFinalDamage;

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::CalculateTargetDamage - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Check if target is valid for area damage
 */
bool CAttackForceAreaProcessor::IsValidTarget(CCharacter* pAttacker, CCharacter* pTarget) {
    if (!pAttacker || !pTarget || pAttacker == pTarget) {
        return false;
    }

    try {
        // Check if target is invulnerable
        if (CAttackForceIntegration::GetEffectState(pTarget, static_cast<int>(EffectType::Invulnerability))) {
            return false;
        }

        // Check if target is alive
        // In real implementation: if (pTarget->IsDead()) return false;

        // Check if target is in same faction (for PvP rules)
        // In real implementation: if (!CanAttackTarget(pAttacker, pTarget)) return false;

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::IsValidTarget - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get all characters in range
 */
std::vector<CCharacter*> CAttackForceAreaProcessor::GetCharactersInRange(float fCenterX, float fCenterY, float fCenterZ, float fRadius) {
    std::vector<CCharacter*> characters;

    try {
        // In a real implementation, this would query the world/map system
        // to get all characters within the specified radius
        // For now, return an empty list as a placeholder

        // Example implementation:
        // CMapManager* pMapManager = CMapManager::Instance();
        // characters = pMapManager->GetCharactersInRadius(fCenterX, fCenterY, fCenterZ, fRadius);

        Logger::Debug("CAttackForceAreaProcessor::GetCharactersInRange - Found %zu characters in range %.1f",
                     characters.size(), fRadius);

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::GetCharactersInRange - Exception: %s", e.what());
        characters.clear();
    }

    return characters;
}

/**
 * Check line of sight between two points
 */
bool CAttackForceAreaProcessor::CheckLineOfSight(float fFromX, float fFromY, float fFromZ, float fToX, float fToY, float fToZ) {
    try {
        // In a real implementation, this would perform collision detection
        // against the world geometry to check if there are obstacles
        // For now, assume line of sight is always clear

        // Example implementation:
        // CCollisionManager* pCollisionManager = CCollisionManager::Instance();
        // return pCollisionManager->CheckLineOfSight(fFromX, fFromY, fFromZ, fToX, fToY, fToZ);

        return true; // Placeholder - assume clear line of sight

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceAreaProcessor::CheckLineOfSight - Exception: %s", e.what());
        return false;
    }
}

/**
 * Area damage utility functions
 */
namespace AreaDamageUtils {

/**
 * Create area damage config from legacy parameters
 */
AreaDamageConfig CreateAreaConfig(_attack_param* pParam, int nLimitRadius) {
    AreaDamageConfig config;

    if (!pParam) {
        return config;
    }

    try {
        // Set center from attack parameters
        if (pParam->fArea.size() >= 3) {
            config.fCenterX = pParam->fArea[0];
            config.fCenterY = pParam->fArea[1];
            config.fCenterZ = pParam->fArea[2];
        }

        // Set radius from limit
        config.fRadius = static_cast<float>(nLimitRadius);
        config.fInnerRadius = config.fRadius * 0.3f; // 30% inner radius for falloff

        // Configure area damage settings
        config.bUseFalloff = true;
        config.bIgnoreObstacles = false;
        config.nMaxTargets = 20; // Reasonable default for area attacks

        Logger::Debug("AreaDamageUtils::CreateAreaConfig - Created area config: center(%.1f,%.1f,%.1f) radius:%.1f",
                     config.fCenterX, config.fCenterY, config.fCenterZ, config.fRadius);

    } catch (const std::exception& e) {
        Logger::Error("AreaDamageUtils::CreateAreaConfig - Exception: %s", e.what());
        config = AreaDamageConfig(); // Reset to default
    }

    return config;
}

/**
 * Create flash damage config from legacy parameters
 */
FlashDamageConfig CreateFlashConfig(_attack_param* pParam, int nLimitDistance, int nLimitAngle) {
    FlashDamageConfig config;

    if (!pParam) {
        return config;
    }

    try {
        // Set origin from attack parameters
        if (pParam->fArea.size() >= 3) {
            config.fOriginX = pParam->fArea[0];
            config.fOriginY = pParam->fArea[1];
            config.fOriginZ = pParam->fArea[2];
        }

        // Set direction (would be calculated from attacker facing or target direction)
        config.fDirectionX = 1.0f; // Default forward direction
        config.fDirectionY = 0.0f;
        config.fDirectionZ = 0.0f;

        // Set limits
        config.fMaxDistance = static_cast<float>(nLimitDistance);
        config.fAngle = static_cast<float>(nLimitAngle);

        // Configure flash damage settings
        config.bUseFalloff = true;
        config.nMaxTargets = 15; // Reasonable default for flash attacks

        Logger::Debug("AreaDamageUtils::CreateFlashConfig - Created flash config: origin(%.1f,%.1f,%.1f) distance:%.1f angle:%.1f",
                     config.fOriginX, config.fOriginY, config.fOriginZ, config.fMaxDistance, config.fAngle);

    } catch (const std::exception& e) {
        Logger::Error("AreaDamageUtils::CreateFlashConfig - Exception: %s", e.what());
        config = FlashDamageConfig(); // Reset to default
    }

    return config;
}

/**
 * Create sector damage config from legacy parameters
 */
SectorDamageConfig CreateSectorConfig(_attack_param* pParam, int nLimitRadius, int nLimitAngle) {
    SectorDamageConfig config;

    if (!pParam) {
        return config;
    }

    try {
        // Set origin from attack parameters
        if (pParam->fArea.size() >= 3) {
            config.fOriginX = pParam->fArea[0];
            config.fOriginY = pParam->fArea[1];
            config.fOriginZ = pParam->fArea[2];
        }

        // Set direction (would be calculated from attacker facing)
        config.fDirectionX = 1.0f; // Default forward direction
        config.fDirectionY = 0.0f;
        config.fDirectionZ = 0.0f;

        // Set limits
        config.fRadius = static_cast<float>(nLimitRadius);
        config.fAngle = static_cast<float>(nLimitAngle);

        // Configure sector damage settings
        config.bUseFalloff = true;
        config.nMaxTargets = 25; // Reasonable default for sector attacks

        Logger::Debug("AreaDamageUtils::CreateSectorConfig - Created sector config: origin(%.1f,%.1f,%.1f) radius:%.1f angle:%.1f",
                     config.fOriginX, config.fOriginY, config.fOriginZ, config.fRadius, config.fAngle);

    } catch (const std::exception& e) {
        Logger::Error("AreaDamageUtils::CreateSectorConfig - Exception: %s", e.what());
        config = SectorDamageConfig(); // Reset to default
    }

    return config;
}

/**
 * Calculate 3D distance between two points
 */
float CalculateDistance3D(float x1, float y1, float z1, float x2, float y2, float z2) {
    float dx = x2 - x1;
    float dy = y2 - y1;
    float dz = z2 - z1;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

/**
 * Normalize a 3D vector
 */
float NormalizeVector3D(float& x, float& y, float& z) {
    float length = std::sqrt(x * x + y * y + z * z);
    if (length > 0.001f) {
        x /= length;
        y /= length;
        z /= length;
    } else {
        x = y = z = 0.0f;
    }
    return length;
}

} // namespace AreaDamageUtils

} // namespace Combat
} // namespace NexusProtection
