/*
 * Function: ?ClearRegen@CNormalGuildBattleField@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403ED190
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::ClearRegen(GUILD_BATTLE::CNormalGuildBattleField *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v6->m_uiRegenPosCnt; ++j )
  {
    if ( !CGravityStoneRegener::ClearRegen(&v6->m_pkRegenPos[j]) )
      return 0;
  }
  return 1;
}
