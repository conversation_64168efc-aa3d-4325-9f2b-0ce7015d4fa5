/*
 * Function: ??$unchecked_uninitialized_copy@PEAVCUnmannedTraderSchedule@@PEAV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@stdext@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00AEAV?$allocator@VCUnmannedTraderSchedule@@@std@@@Z
 * Address: 0x140397450
 */

CUnmannedTraderSchedule *__fastcall stdext::unchecked_uninitialized_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::allocator<CUnmannedTraderSchedule> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  CUnmannedTraderSchedule *__formal; // [sp+50h] [bp+8h]@1
  CUnmannedTraderSchedule *_Lasta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderSchedule *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderSchedule> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *>(&__formal, &_Desta);
  return std::_Uninit_copy<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::allocator<CUnmannedTraderSchedule>>(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}
