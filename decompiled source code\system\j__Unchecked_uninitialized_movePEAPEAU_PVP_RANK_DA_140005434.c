/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAU_PVP_RANK_DATA@@PEAPEAU1@V?$allocator@PEAU_PVP_RANK_DATA@@@std@@@stdext@@YAPEAPEAU_PVP_RANK_DATA@@PEAPEAU1@00AEAV?$allocator@PEAU_PVP_RANK_DATA@@@std@@@Z
 * Address: 0x140005434
 */

_PVP_RANK_DATA **__fastcall stdext::_Unchecked_uninitialized_move<_PVP_RANK_DATA * *,_PVP_RANK_DATA * *,std::allocator<_PVP_RANK_DATA *>>(_PVP_RANK_DATA **_First, _PVP_RANK_DATA **_Last, _PVP_RANK_DATA **_Dest, std::allocator<_PVP_RANK_DATA *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<_PVP_RANK_DATA * *,_PVP_RANK_DATA * *,std::allocator<_PVP_RANK_DATA *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
