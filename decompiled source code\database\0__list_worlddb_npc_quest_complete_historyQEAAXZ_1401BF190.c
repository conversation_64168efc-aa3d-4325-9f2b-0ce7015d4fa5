/*
 * Function: ??0__list@_worlddb_npc_quest_complete_history@@QEAA@XZ
 * Address: 0x1401BF190
 */

void __fastcall _worlddb_npc_quest_complete_history::__list::__list(_worlddb_npc_quest_complete_history::__list *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _worlddb_npc_quest_complete_history::__list *Dest; // [sp+30h] [bp+8h]@1

  Dest = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  strcpy_0(Dest->szQuestCode, "*");
  Dest->byLevel = -1;
  Dest->dwEventEndTime = 0;
}
