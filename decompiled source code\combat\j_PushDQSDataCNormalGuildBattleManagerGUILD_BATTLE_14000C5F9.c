/*
 * Function: j_?PushDQSData@CNormalGuildBattleManager@GUILD_BATTLE@@IEAA_NIIPEAVCNormalGuildBattle@2@PEAVCGuildBattleSchedule@2@@Z
 * Address: 0x14000C5F9
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleManager::PushDQSData(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int uiFieldInx, unsigned int uiSLID, GUILD_BATTLE::CNormalGuildBattle *pkBattle, GUILD_BATTLE::CGuildBattleSchedule *pkSchedule)
{
  return GUILD_BATTLE::CNormalGuildBattleManager::PushDQSData(this, uiFieldInx, uiSLID, pkBattle, pkSchedule);
}
