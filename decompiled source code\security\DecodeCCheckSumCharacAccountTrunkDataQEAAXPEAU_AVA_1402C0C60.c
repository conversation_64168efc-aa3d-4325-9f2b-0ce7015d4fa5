/*
 * Function: ?Decode@CCheckSumCharacAccountTrunkData@@QEAAXPEAU_AVATOR_DATA@@@Z
 * Address: 0x1402C0C60
 */

void __fastcall CCheckSumCharacAccountTrunkData::Decode(CCheckSumCharacAccountTrunkData *this, _AVATOR_DATA *pAvator)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+24h] [bp-24h]@4
  CCheckSumCharacAccountTrunkData *pkCheckSum; // [sp+50h] [bp+8h]@1

  pkCheckSum = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCheckSumCharacTrunkConverter::Convert((CCheckSumCharacTrunkConverter *)&v5, pAvator, pkCheckSum);
}
