/*
 * Function: j_??1?$_Hash@V?$_Hmap_traits@HPEBU_TimeItem_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@$0A@@stdext@@@stdext@@QEAA@XZ
 * Address: 0x14000349A
 */

void __fastcall stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::~_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>(stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *this)
{
  stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::~_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>(this);
}
