/*
 * Function: ?Check@CGuildBattleSchedule@GUILD_BATTLE@@QEAAHXZ
 * Address: 0x1403D9DE0
 */

signed __int64 __fastcall GUILD_BATTLE::CGuildBattleSchedule::Check(GUILD_BATTLE::CGuildBattleSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v3; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  ATL::CTime result; // [sp+28h] [bp-20h]@7
  GUILD_BATTLE::CGuildBattleSchedule *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_eState && v6->m_eState != 3 )
  {
    ATL::CTime::GetTickCount(&result);
    if ( ATL::CTime::operator<(&result, v6->m_kNextStartTime) )
    {
      v3 = 1i64;
    }
    else if ( GUILD_BATTLE::CGuildBattleSchedule::Process(v6) )
    {
      v3 = 2i64;
    }
    else
    {
      v6->m_eState = 3;
      v3 = 0i64;
    }
  }
  else
  {
    v3 = 0xFFFFFFFFi64;
  }
  return v3;
}
