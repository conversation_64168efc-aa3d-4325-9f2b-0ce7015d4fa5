/*
 * Function: ?Init@_AVATOR_DB_BASE@@QEAAXXZ
 * Address: 0x1400756A0
 */

void __fastcall _AVATOR_DB_BASE::Init(_AVATOR_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@7
  int l; // [sp+28h] [bp-10h]@10
  _AVATOR_DB_BASE *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _REGED_AVATOR_DB::Init((_REGED_AVATOR_DB *)v7->m_wszAvatorName);
  for ( j = 0; j < 3; ++j )
    v7->m_zClassHistory[j] = -1;
  v7->m_dwPvpRank = 0;
  v7->m_wRankRate = -1;
  v7->m_dwGuildSerial = -1;
  v7->m_dwGuildExplusDate = -1;
  v7->m_bOverlapVote = 1;
  v7->m_dwGivebackCount = 0;
  v7->m_dwRadarDelayTime = 0;
  v7->m_dwTakeLastMentalTicket = 0;
  v7->m_dwTakeLastCriTicket = 0;
  v7->m_byMaxLevel = 0;
  for ( k = 0; k < 3; ++k )
  {
    v7->m_dwPunishment[k] = -1;
    v7->m_dwElectSerial[k] = 0;
  }
  for ( l = 0; l < 3; ++l )
    v7->m_dwRaceBattleRecord[l] = 0;
}
