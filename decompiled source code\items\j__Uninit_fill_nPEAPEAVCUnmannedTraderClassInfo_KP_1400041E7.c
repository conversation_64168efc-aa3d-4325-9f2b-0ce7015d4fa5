/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400041E7
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(CUnmannedTraderClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val, std::allocator<CUnmannedTraderClassInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
