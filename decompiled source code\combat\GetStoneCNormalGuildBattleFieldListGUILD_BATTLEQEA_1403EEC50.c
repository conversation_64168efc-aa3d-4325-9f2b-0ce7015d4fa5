/*
 * Function: ?GetStone@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCGravityStone@@H@Z
 * Address: 0x1403EEC50
 */

CGravityStone *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetStone(GUILD_BATTLE::CNormalGuildBattleFieldList *this, int iInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int j; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleFieldList *v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1

  v8 = iInx;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < v7->m_dwCnt; ++j )
  {
    if ( v8 == GUILD_BATTLE::CNormalGuildBattleField::GetStone(&v7->m_pkField[j])->m_ObjID.m_wIndex )
      return GUILD_BATTLE::CNormalGuildBattleField::GetStone(&v7->m_pkField[j]);
  }
  return 0i64;
}
