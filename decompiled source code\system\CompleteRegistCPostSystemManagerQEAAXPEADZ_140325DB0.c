/*
 * Function: ?CompleteRegist@CPostSystemManager@@QEAAXPEAD@Z
 * Address: 0x140325DB0
 */

void __fastcall CPostSystemManager::CompleteRegist(CPostSystemManager *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-68h]@1
  int v6; // [sp+20h] [bp-48h]@7
  __int64 v7; // [sp+28h] [bp-40h]@7
  int v8; // [sp+30h] [bp-38h]@7
  int v9; // [sp+38h] [bp-30h]@7
  char *v10; // [sp+40h] [bp-28h]@4
  unsigned int j; // [sp+48h] [bp-20h]@4
  char *v12; // [sp+50h] [bp-18h]@6
  CPostSystemManager *v13; // [sp+70h] [bp+8h]@1

  v13 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = pData;
  for ( j = 0; j < *(_DWORD *)v10; ++j )
  {
    v12 = &v10[304 * j + 8];
    if ( v10[304 * j + 12] )
    {
      CNetIndexList::PushNode_Back(&v13->m_listProc, *(_DWORD *)v12);
    }
    else
    {
      v4 = _INVENKEY::CovDBKey((_INVENKEY *)v12 + 67);
      v9 = *((_DWORD *)v12 + 74);
      v8 = *((_DWORD *)v12 + 70);
      v7 = *((_QWORD *)v12 + 34);
      v6 = v4;
      CPostSystemManager::Log(
        v13,
        "CPostSystemManager::CompleteRegist() Serial(%d),PostInx(%d),K(%d),D(%d),U(%d),Gold(%d)",
        *((_DWORD *)v12 + 2),
        *(_DWORD *)v12);
      CNetIndexList::PushNode_Back(&v13->m_listEmpty, *(_DWORD *)v12);
    }
  }
}
