/*
 * Function: ?SendMsg_FixPosition@CHolyKeeper@@UEAAXH@Z
 * Address: 0x1401351D0
 */

void __fastcall CHolyKeeper::SendMsg_FixPosition(<PERSON><PERSON><PERSON>eeper *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v6; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v7; // [sp+3Ch] [bp-5Ch]@4
  __int16 pShort; // [sp+40h] [bp-58h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v10; // [sp+65h] [bp-33h]@4
  unsigned __int64 v11; // [sp+80h] [bp-18h]@4
  CHolyKeeper *v12; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v12 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v12->m_pRec->m_dwIndex;
  v6 = v12->m_ObjID.m_wIndex;
  v7 = v12->m_dwObjSerial;
  FloatToShort(v12->m_fCurPos, &pShort, 3);
  pbyType = 4;
  v10 = -89;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0xEu);
}
