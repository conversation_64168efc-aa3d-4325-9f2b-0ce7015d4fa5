/*
 * Function: ??0CUnmannedTraderSubClassInfoDefault@@QEAA@K@Z
 * Address: 0x140383920
 */

void __fastcall CUnmannedTraderSubClassInfoDefault::CUnmannedTraderSubClassInfoDefault(CUnmannedTraderSubClassInfoDefault *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoDefault *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CUnmannedTraderSubClassInfo::CUnmannedTraderSubClassInfo((CUnmannedTraderSubClassInfo *)&v5->vfptr, 0);
  v5->vfptr = (CUnmannedTraderSubClassInfoVtbl *)&CUnmannedTraderSubClassInfoDefault::`vftable';
  strcpy_0(v5->m_szName, "default");
}
