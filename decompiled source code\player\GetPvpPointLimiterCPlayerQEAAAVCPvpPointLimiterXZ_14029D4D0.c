/*
 * Function: ?GetPvpPointLimiter@CPlayer@@QEAA?AVCPvpPointLimiter@@XZ
 * Address: 0x14029D4D0
 */

CPvpPointLimiter *__fastcall CPlayer::GetPvpPointLimiter(CPlayer *this, CPvpPointLimiter *result)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CPlayer *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = 0;
  result->m_pkInfo = (_PVPPOINT_LIMIT_DB_BASE *)v6->m_kPvpPointLimiter;
  return result;
}
