/*
 * Function: ?AddTrunkDalant@CPlayerDB@@QEAAXK@Z
 * Address: 0x14010C200
 */

void __fastcall CPlayerDB::AddTrunkDalant(CPlayerDB *this, unsigned int dwPush)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = v5->m_dTrunkDalant + (double)(signed int)dwPush;
  if ( v4 > 1000000000.0 || v5->m_dTrunkDalant > v4 )
    v4 = DOUBLE_1_0e9;
  v5->m_dTrunkDalant = v4;
}
