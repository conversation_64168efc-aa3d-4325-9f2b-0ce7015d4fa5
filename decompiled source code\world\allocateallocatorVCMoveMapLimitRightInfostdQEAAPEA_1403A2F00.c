/*
 * Function: ?allocate@?$allocator@VCMoveMapLimitRightInfo@@@std@@QEAAPEAVCMoveMapLimitRightInfo@@_K@Z
 * Address: 0x1403A2F00
 */

CMoveMapLimitRightInfo *__fastcall std::allocator<CMoveMapLimitRightInfo>::allocate(std::allocator<CMoveMapLimitRightInfo> *this, unsigned __int64 _Count)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return std::_Allocate<CMoveMapLimitRightInfo>(_Count, 0i64);
}
