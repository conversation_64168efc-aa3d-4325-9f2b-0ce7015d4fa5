/*
 * Function: ?InitTicketReserver@CTransportShip@@QEAAXXZ
 * Address: 0x140264F10
 */

void __fastcall CTransportShip::InitTicketReserver(CTransportShip *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CTransportShip *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 2; ++j )
    v4->m_MgrTicket[j].nReserTicketNum = 0;
}
