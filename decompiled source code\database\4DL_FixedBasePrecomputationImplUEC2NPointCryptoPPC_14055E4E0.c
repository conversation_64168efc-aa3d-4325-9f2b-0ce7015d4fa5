/*
 * Function: ??4?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@QEAAAEAV01@AEBV01@@Z
 * Address: 0x14055E4E0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::operator=(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::EC2NPoint>::operator=();
  CryptoPP::EC2NPoint::operator=((_BYTE *)(v3 + 8), (_BYTE *)(v4 + 8));
  *(_DWORD *)(v3 + 64) = *(_DWORD *)(v4 + 64);
  CryptoPP::Integer::operator=(v3 + 72);
  std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator=(v3 + 112, v4 + 112);
  return v3;
}
