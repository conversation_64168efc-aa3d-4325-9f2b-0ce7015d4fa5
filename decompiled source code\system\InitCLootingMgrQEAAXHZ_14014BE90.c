/*
 * Function: ?Init@CLootingMgr@@QEAAXH@Z
 * Address: 0x14014BE90
 */

void __fastcall CLootingMgr::Init(CLootingMgr *this, int nUserNode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CLootingMgr *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_byUserNode = nUserNode;
  for ( j = 0; j < v6->m_byUserNode; ++j )
    CLootingMgr::_list::Init(&v6->m_AtterList[j]);
  v6->m_bFirst = 1;
}
