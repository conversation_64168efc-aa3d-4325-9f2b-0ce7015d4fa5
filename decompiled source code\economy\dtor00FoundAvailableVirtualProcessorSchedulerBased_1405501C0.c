/*
 * Function: ?dtor$0@?0??FoundAvailableVirtualProcessor@SchedulerBase@details@Concurrency@@QEAA_NAEAVClaimTicket@VirtualProcessor@23@Vlocation@3@K@Z@4HA
 * Address: 0x1405501C0
 */

void __fastcall `Concurrency::details::SchedulerBase::FoundAvailableVirtualProcessor'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::~_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>(*(std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > **)(a2 + 240));
}
