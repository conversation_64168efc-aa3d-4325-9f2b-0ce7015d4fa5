/*
 * Function: ?make_unit_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAU_UnitPart_fld@@MPEAU_attack_param@@@Z
 * Address: 0x140089230
 */

void __fastcall CPlayer::make_unit_attack_param(CPlayer *this, CCharacter *pDst, _UnitPart_fld *pWeaponFld, float fAddBulletFc, _attack_param *pAP)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@7
  __int64 v8; // [sp+0h] [bp-38h]@1
  float v9; // [sp+20h] [bp-18h]@7
  CPlayer *pTarget; // [sp+40h] [bp+8h]@1
  CMonster *v11; // [sp+48h] [bp+10h]@1
  _UnitPart_fld *v12; // [sp+50h] [bp+18h]@1

  v12 = pWeaponFld;
  v11 = (CMonster *)pDst;
  pTarget = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  pAP->pDst = pDst;
  if ( pDst )
    pAP->nPart = CCharacter::GetAttackRandomPart(pDst);
  else
    pAP->nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pTarget->vfptr);
  pAP->nTol = -1;
  pAP->nClass = v12->m_nWPType;
  pAP->nMinAF = (signed int)ffloor((float)((float)v12->m_nGAMinAF * fAddBulletFc) * pTarget->m_fUnitPv_AttFc);
  v9 = (float)v12->m_nGAMaxAF * fAddBulletFc;
  v7 = _MASTERY_PARAM::GetMasteryPerMast(&pTarget->m_pmMst, 6, 0);
  pAP->nMaxAF = (signed int)ffloor((float)(v9 + (float)v7) * pTarget->m_fUnitPv_AttFc);
  pAP->nMinSel = v12->m_nGAMinSelProb;
  pAP->nMaxSel = v12->m_nGAMaxSelProb;
  pAP->nExtentRange = (signed int)ffloor(v12->m_fAttackRange);
  pAP->pFld = (_base_fld *)v12;
  pAP->nMaxAttackPnt = pTarget->m_nMaxAttackPnt;
  if ( v11 && v11->m_ObjID.m_byKind == 1 && !CMonster::IsViewArea(v11, (CCharacter *)&pTarget->vfptr) )
    pAP->bBackAttack = 1;
}
