/*
 * Function: ?CheatCancelRegistAll@CUnmannedTraderUserInfo@@AEAA_NXZ
 * Address: 0x14035C5F0
 */

char __fastcall CUnmannedTraderUserInfo::CheatCancelRegistAll(CUnmannedTraderUserInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@5
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v4; // rax@6
  CUnmannedTraderRegistItemInfo *v5; // rax@7
  int v6; // eax@9
  __int64 v8; // [sp+0h] [bp-178h]@1
  char Dst; // [sp+40h] [bp-138h]@4
  unsigned __int8 v10; // [sp+41h] [bp-137h]@7
  unsigned __int16 v11; // [sp+42h] [bp-136h]@4
  unsigned int v12; // [sp+44h] [bp-134h]@4
  int v13[27]; // [sp+4Ch] [bp-12Ch]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+B8h] [bp-C0h]@4
  unsigned __int8 j; // [sp+D4h] [bp-A4h]@4
  char v16; // [sp+D8h] [bp-A0h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v17; // [sp+F0h] [bp-88h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v18; // [sp+F8h] [bp-80h]@6
  bool v19; // [sp+110h] [bp-68h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v20; // [sp+118h] [bp-60h]@6
  char v21; // [sp+130h] [bp-48h]@10
  __int64 v22; // [sp+138h] [bp-40h]@4
  unsigned __int64 v23; // [sp+140h] [bp-38h]@5
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v24; // [sp+148h] [bp-30h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v25; // [sp+150h] [bp-28h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__that; // [sp+158h] [bp-20h]@6
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v27; // [sp+160h] [bp-18h]@6
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+168h] [bp-10h]@6
  CUnmannedTraderUserInfo *v29; // [sp+180h] [bp+8h]@1

  v29 = this;
  v1 = &v8;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = -2i64;
  memset_0(&Dst, 0, 0x58ui64);
  v11 = v29->m_wInx;
  v12 = v29->m_dwUserSerial;
  Dst = 0;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(
    &v29->m_vecRegistItemInfo,
    &result);
  for ( j = 0; ; ++j )
  {
    v23 = j;
    v3 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v29->m_vecRegistItemInfo);
    if ( v23 >= v3 )
      break;
    v17 = (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v16;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
      (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v16,
      &result);
    v24 = v4;
    v25 = CUnmannedTraderUserInfo::FindRegist(v29, &v18, v4);
    __that = v25;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator=(
      &result,
      v25);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v18);
    v27 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
            &v29->m_vecRegistItemInfo,
            &v20);
    _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v27;
    v19 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
            (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v27->_Mycont);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v20);
    if ( v19 )
      break;
    v5 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    v13[2 * (unsigned __int64)v10] = CUnmannedTraderRegistItemInfo::GetRegistSerial(v5);
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator++(&result);
    ++v10;
  }
  if ( (signed int)v10 > 0 )
  {
    v6 = _qry_case_unmandtrader_cheat_updateregisttime::size((_qry_case_unmandtrader_cheat_updateregisttime *)&Dst);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -115, &Dst, v6);
  }
  v21 = 1;
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
  return v21;
}
