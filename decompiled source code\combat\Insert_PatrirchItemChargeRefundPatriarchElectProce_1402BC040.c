/*
 * Function: ?Insert_PatrirchItemChargeRefund@PatriarchElectProcessor@@QEAAHPEAD@Z
 * Address: 0x1402BC040
 */

signed __int64 __fastcall PatriarchElectProcessor::Insert_PatrirchItemChargeRefund(PatriarchElectProcessor *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-168h]@1
  char *v6; // [sp+20h] [bp-148h]@4
  char Dest; // [sp+40h] [bp-128h]@4
  char v8; // [sp+41h] [bp-127h]@4
  unsigned __int64 v9; // [sp+150h] [bp-18h]@4

  v2 = &v5;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = pData;
  Dest = 0;
  memset(&v8, 0, 0xFFui64);
  sprintf(
    &Dest,
    "insert [dbo].[tbl_itemcharge] (nAvatorSerial, nItemCode_K, nItemCode_D, nItemCode_U, dtGiveDate, dtTakeDate, Type) v"
    "alues (%d, 0, %d, default, default, default, 1)",
    *((_DWORD *)v6 + 1),
    *((_QWORD *)v6 + 1));
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&pkDB->vfptr, &Dest, 1) )
    result = 0i64;
  else
    result = 24i64;
  return result;
}
