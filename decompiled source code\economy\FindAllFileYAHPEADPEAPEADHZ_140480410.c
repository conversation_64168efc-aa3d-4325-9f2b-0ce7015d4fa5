/*
 * Function: ?FindAllFile@@YAHPEADPEAPEADH@Z
 * Address: 0x140480410
 */

__int64 __fastcall FindAllFile(char *pszDirectory, char **ppszFileName, int nMax)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-1A8h]@1
  _WIN32_FIND_DATAA FindFileData; // [sp+30h] [bp-178h]@4
  HANDLE hFindFile; // [sp+178h] [bp-30h]@4
  int v9; // [sp+180h] [bp-28h]@4
  unsigned int v10; // [sp+184h] [bp-24h]@4
  char *v11; // [sp+190h] [bp-18h]@7
  unsigned __int64 v12; // [sp+198h] [bp-10h]@4
  char *lpFileName; // [sp+1B0h] [bp+8h]@1
  char **v14; // [sp+1B8h] [bp+10h]@1
  int v15; // [sp+1C0h] [bp+18h]@1

  v15 = nMax;
  v14 = ppszFileName;
  lpFileName = pszDirectory;
  v3 = &v6;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  hFindFile = FindFirstFileA(lpFileName, &FindFileData);
  v9 = 1;
  v10 = 0;
  while ( v9 )
  {
    if ( hFindFile != (HANDLE)-1 )
    {
      v11 = (char *)operator new(0x80ui64);
      v14[v10] = v11;
      strcpy_s(v14[v10++], 0x80ui64, FindFileData.cFileName);
      if ( (signed int)v10 >= v15 )
        break;
    }
    v9 = FindNextFileA(hFindFile, &FindFileData);
  }
  FindClose(hFindFile);
  return v10;
}
