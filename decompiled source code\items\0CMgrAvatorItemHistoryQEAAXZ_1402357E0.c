/*
 * Function: ??0CMgrAvatorItemHistory@@QEAA@XZ
 * Address: 0x1402357E0
 */

void __fastcall CMgrAvatorItemHistory::CMgrAvatorItemHistory(CMgrAvatorItemHistory *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-E8h]@1
  char ReturnedString; // [sp+40h] [bp-A8h]@4
  unsigned int dwIndex; // [sp+C4h] [bp-24h]@4
  __int64 v6; // [sp+D0h] [bp-18h]@4
  unsigned __int64 v7; // [sp+D8h] [bp-10h]@4
  CMgrAvatorItemHistory *Dest; // [sp+F0h] [bp+8h]@1

  Dest = this;
  v1 = &v3;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = -2i64;
  v7 = (unsigned __int64)&v3 ^ _security_cookie;
  CMyTimer::CMyTimer(&Dest->m_tmrUpdateTime);
  CNetIndexList::CNetIndexList(&Dest->m_listLogData_10K);
  CNetIndexList::CNetIndexList(&Dest->m_listLogDataEmpty_10K);
  CNetIndexList::CNetIndexList(&Dest->m_listLogData_1K);
  CNetIndexList::CNetIndexList(&Dest->m_listLogDataEmpty_1K);
  CNetIndexList::CNetIndexList(&Dest->m_listLogData_200);
  CNetIndexList::CNetIndexList(&Dest->m_listLogDataEmpty_200);
  CFrameRate::CFrameRate(&Dest->m_FrameRate);
  Dest->m_dwLastLocalDate = 0;
  Dest->m_dwLastLocalHour = 0;
  GetPrivateProfileStringA(
    "System",
    "HistoryPath",
    "C:\\History",
    &ReturnedString,
    0x80u,
    "..\\WorldInfo\\WorldInfo.ini");
  CreateDirectoryA(&ReturnedString, 0i64);
  sprintf(Dest->m_szStdPath, "%s\\Item", &ReturnedString);
  CreateDirectoryA(Dest->m_szStdPath, 0i64);
  _strdate(Dest->m_szCurDate);
  Dest->m_szCurDate[5] = 0;
  _strtime(Dest->m_szCurTime);
  Dest->m_szCurTime[5] = 0;
  CMyTimer::BeginTimer(&Dest->m_tmrUpdateTime, 0xEA60u);
  CNetIndexList::SetList(&Dest->m_listLogData_10K, 0xFEu);
  CNetIndexList::SetList(&Dest->m_listLogDataEmpty_10K, 0xFEu);
  for ( dwIndex = 0; (signed int)dwIndex < 254; ++dwIndex )
    CNetIndexList::PushNode_Back(&Dest->m_listLogDataEmpty_10K, dwIndex);
  CNetIndexList::SetList(&Dest->m_listLogData_1K, 0xFEu);
  CNetIndexList::SetList(&Dest->m_listLogDataEmpty_1K, 0xFEu);
  for ( dwIndex = 0; (signed int)dwIndex < 254; ++dwIndex )
    CNetIndexList::PushNode_Back(&Dest->m_listLogDataEmpty_1K, dwIndex);
  CNetIndexList::SetList(&Dest->m_listLogData_200, 0x9E4u);
  CNetIndexList::SetList(&Dest->m_listLogDataEmpty_200, 0x9E4u);
  for ( dwIndex = 0; (signed int)dwIndex < 2532; ++dwIndex )
    CNetIndexList::PushNode_Back(&Dest->m_listLogDataEmpty_200, dwIndex);
  Dest->m_bIOThread = 1;
  _beginthread((void (__cdecl *)(void *))CMgrAvatorItemHistory::IOThread, 0, Dest);
}
