/*
 * Function: ?PushClearGuildBattleRank@CGuildBattleRankManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403CB380
 */

void __fastcall GUILD_BATTLE::CGuildBattleRankManager::PushClearGuildBattleRank(GUILD_BATTLE::CGuildBattleRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-38h]@1

  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 35, 0i64, 0);
  v3 = CPvpUserAndGuildRankingSystem::Instance();
  CPvpUserAndGuildRankingSystem::Log(v3, "CGuildBattleRankManager::PushClearGuildBattleRank()");
}
