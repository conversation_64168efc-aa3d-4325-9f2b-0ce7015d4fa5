/*
 * CItemManagementSystem.cpp - Modern Item Management System Implementation
 * Refactored from decompiled C item management functions
 * Provides comprehensive item creation, looting, and inventory management
 */

#include "../Headers/CItemManagementSystem.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CPlayer.h"
#include "../../world/Headers/CMapData.h"
#include "../../world/Headers/CItemBox.h"

#include <algorithm>
#include <chrono>
#include <stdexcept>
#include <ctime>

// External references to legacy systems
extern "C" {
    // Legacy function declarations
    struct _base_fld;
    struct _ItemUpgrade_fld;
    struct _TimeItem_fld;
    struct _STORAGE_LIST;
    
    extern int GetItemTableCode(const char* pszItemCode);
    extern _base_fld* CRecordData_GetRecord(void* pRecordData, const char* itemCode);
    extern bool IsOverLapItem(int tableCode);
    extern uint32_t GetItemDurPoint(int tableCode, uint32_t itemIndex);
    extern uint8_t GetItemKindCode(int tableCode);
    extern uint8_t GetDefItemUpgSocketNum(int tableCode, uint32_t itemIndex);
    extern uint32_t GetBitAfterSetLimSocket(uint8_t socketNum);
    extern uint32_t GetBitAfterUpgrade(uint32_t currentBits, uint32_t upgradeIndex, int socketIndex);
    extern _TimeItem_fld* TimeItem_FindTimeRec(int tableCode, uint32_t itemIndex);
    extern CItemBox* CreateItemBox(_STORAGE_LIST* pItem, CPlayer* pOwner, uint32_t dwPartyBossSerial, 
                                  bool bPartyShare, void* pThrower, uint8_t byCreateCode, 
                                  CMapData* pMap, uint16_t wLayerIndex, float* pStdPos, bool bHide);
    extern void _time32(uint32_t* pTime);
    extern char* GetItemKorName(uint8_t tableCode, uint16_t itemIndex);
    extern uint32_t GetKorLocalTime();
}

namespace NexusProtection {
namespace Items {

/**
 * Constructor
 */
CItemManagementSystem::CItemManagementSystem() 
    : m_bDetailedLogging(false) {
    
    Logger::Debug("CItemManagementSystem::CItemManagementSystem - Item management system initialized");
}

/**
 * Destructor
 */
CItemManagementSystem::~CItemManagementSystem() {
    try {
        Logger::Debug("CItemManagementSystem::~CItemManagementSystem - Item management system destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Create and loot item for player
 * Refactored from: _loot_item_1400BEFC0.c
 */
ItemOperationDetails CItemManagementSystem::CreateLootItem(const ItemCreationContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    // Validate context
    if (!ValidateCreationContext(context)) {
        return CreateResult(ItemOperationResult::InvalidItem, startTime, "Invalid creation context");
    }
    
    try {
        // Get item table code (original line 44)
        int tableCode = GetItemTableCode(context.itemCode.c_str());
        if (tableCode == -1) {
            return CreateResult(ItemOperationResult::InvalidItem, startTime, "Invalid item code: " + context.itemCode);
        }
        
        // Check for invalid table codes (original lines 47-48)
        if (tableCode == 19) {
            return CreateResult(ItemOperationResult::InvalidItem, startTime, "Table code 19 not allowed");
        }
        
        // Get item record (original line 49)
        _base_fld* pItemRecord = GetItemRecord(tableCode, context.itemCode);
        if (!pItemRecord) {
            return CreateResult(ItemOperationResult::ItemNotFound, startTime, "Item record not found");
        }
        
        // Create item information
        ItemInfo itemInfo;
        itemInfo.tableCode = static_cast<uint8_t>(tableCode);
        itemInfo.itemIndex = static_cast<uint16_t>(pItemRecord->m_dwIndex);
        itemInfo.itemCode = context.itemCode;
        
        // Calculate durability (original lines 52-56)
        if (IsOverlapItem(tableCode)) {
            itemInfo.durability = 99; // Overlap items have fixed durability
        } else {
            itemInfo.durability = CalculateItemDurability(tableCode, pItemRecord->m_dwIndex);
        }
        
        // Validate quantity (original lines 57-58)
        int quantity = context.quantity;
        if (quantity > 100) {
            quantity = 100;
        }
        
        // Get item kind code (original line 59)
        uint8_t itemKind = GetItemKindCode(tableCode);
        
        // Process item upgrades if specified (original lines 71-82)
        ItemUpgradeInfo upgradeInfo;
        if (!context.upgradeCode.empty()) {
            int upgradeTableCode = GetItemTableCode(context.upgradeCode.c_str());
            if (upgradeTableCode != 18) {
                return CreateResult(ItemOperationResult::InvalidUpgrade, startTime, "Invalid upgrade table code");
            }
            
            _base_fld* pUpgradeRecord = GetItemRecord(18, context.upgradeCode);
            if (!pUpgradeRecord) {
                return CreateResult(ItemOperationResult::InvalidUpgrade, startTime, "Upgrade record not found");
            }
            
            upgradeInfo.upgradeCode = context.upgradeCode;
            upgradeInfo.upgradeIndex = pUpgradeRecord->m_dwIndex;
            upgradeInfo.currentLevel = context.upgradeLevel;
        }
        
        // Create items (original lines 84-112)
        ItemOperationDetails result;
        result.result = ItemOperationResult::Success;
        result.operationId = GenerateOperationId();
        
        for (int i = 0; i < quantity; ++i) {
            // Create storage list item
            _STORAGE_LIST* pStorageItem = CreateStorageListItem(context, itemInfo);
            if (!pStorageItem) {
                return CreateResult(ItemOperationResult::SystemError, startTime, "Failed to create storage item");
            }
            
            // Set time item properties if applicable (original lines 90-97)
            _TimeItem_fld* pTimeRecord = FindTimeItemRecord(tableCode, pItemRecord->m_dwIndex);
            if (pTimeRecord) {
                SetTimeItemProperties(itemInfo, pTimeRecord);
            }
            
            // Apply upgrades if specified (original lines 98-104)
            if (upgradeInfo.IsValid()) {
                uint8_t socketCount = GetDefaultUpgradeSocketCount(tableCode, pItemRecord->m_dwIndex);
                int upgradeLevel = std::min(context.upgradeLevel, static_cast<int>(socketCount));
                itemInfo.level = ApplyItemUpgrades(itemInfo, upgradeInfo, upgradeLevel);
            }
            
            // Create item box in world (original line 110)
            CItemBox* pItemBox = CreateItemBox(itemInfo, context);
            if (!pItemBox) {
                return CreateResult(ItemOperationResult::SystemError, startTime, "Failed to create item box");
            }
            
            result.pCreatedItemBox = pItemBox;
        }
        
        // Update statistics
        m_stats.totalItemsCreated += quantity;
        m_stats.totalItemsLooted += quantity;
        m_stats.RecordOperation(true);
        
        result.itemInfo = itemInfo;
        result.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::high_resolution_clock::now() - startTime);
        
        LogOperation(result);
        
        if (m_operationCallback) {
            m_operationCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordOperation(false);
        Logger::Error("CItemManagementSystem::CreateLootItem - Exception: %s", e.what());
        return CreateResult(ItemOperationResult::SystemError, startTime, 
                          std::string("Exception: ") + e.what());
    }
}

/**
 * Create item box in world
 * Refactored from: CreateItemBoxYAPEAVCItemBoxPEAU_db_con_STORAGE_LIS_140166AD0.c
 */
CItemBox* CItemManagementSystem::CreateItemBox(const ItemInfo& itemInfo, const ItemCreationContext& context) {
    try {
        if (!context.pOwner || !context.pMap) {
            Logger::Error("CItemManagementSystem::CreateItemBox - Invalid context parameters");
            return nullptr;
        }
        
        // Create storage list item
        _STORAGE_LIST* pStorageItem = CreateStorageListItem(context, itemInfo);
        if (!pStorageItem) {
            Logger::Error("CItemManagementSystem::CreateItemBox - Failed to create storage item");
            return nullptr;
        }
        
        // Call legacy CreateItemBox function (original line 110 in _loot_item_1400BEFC0.c)
        CItemBox* pItemBox = ::CreateItemBox(
            pStorageItem,
            context.pOwner,
            0xFFFFFFFF,  // dwPartyBossSerial
            false,       // bPartyShare
            nullptr,     // pThrower
            2,           // byCreateCode
            context.pMap,
            context.layerIndex,
            const_cast<float*>(context.position),
            context.hideFromOthers
        );
        
        if (pItemBox) {
            Logger::Debug("CItemManagementSystem::CreateItemBox - Item box created successfully for item: %s", 
                         itemInfo.itemCode.c_str());
        } else {
            Logger::Error("CItemManagementSystem::CreateItemBox - Failed to create item box for item: %s", 
                         itemInfo.itemCode.c_str());
        }
        
        return pItemBox;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::CreateItemBox - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Initialize time item system
 * Refactored from: InitTimeItemQEAA_NXZ_14030E160.c
 */
bool CItemManagementSystem::InitializeTimeItemSystem() {
    try {
        Logger::Info("CItemManagementSystem::InitializeTimeItemSystem - Initializing time item system");
        
        // Create log directory (original lines 26-27)
        std::string logPath = "..\\ZoneServerLog\\SystemLog";
        // In real implementation: CreateDirectoryA(logPath.c_str(), nullptr);
        
        // Set up log file with current date (original lines 30-32)
        uint32_t currentTime = ItemManagementUtils::GetCurrentTime32();
        std::string logFileName = logPath + "\\TimeItem_" + std::to_string(currentTime) + ".log";
        
        // Initialize time item logger (original line 32)
        // In real implementation: CLogFile::SetWriteLogFile(&timeItemLogger, logFileName.c_str(), 1, 0, 1, 1);
        
        // Read time item goods data (original line 33)
        if (!ReadTimeItemGoods()) {
            Logger::Error("CItemManagementSystem::InitializeTimeItemSystem - Failed to read time item goods");
            return false;
        }
        
        Logger::Info("CItemManagementSystem::InitializeTimeItemSystem - Time item system initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::InitializeTimeItemSystem - Exception: %s", e.what());
        return false;
    }
}

/**
 * Read time item goods data
 * Refactored from: ReadGoodsTimeItemQEAA_NXZ_14030E6B0.c
 */
bool CItemManagementSystem::ReadTimeItemGoods() {
    try {
        Logger::Debug("CItemManagementSystem::ReadTimeItemGoods - Reading time item goods data");
        
        // Read time item record data (original line 24)
        // In real implementation: CRecordData::ReadRecord(&timeItemRecord, ".\\Script\\TimerItem.dat", 0x8C, &errorMsg);
        
        // Make link table (original line 26)
        // In real implementation: TimeItem::MakeLinkTable(&timeItemRecord, &errorMsg, 128);
        
        // Check goods validity (original line 28)
        // In real implementation: TimeItem::CheckGoods(&timeItemRecord);
        
        Logger::Debug("CItemManagementSystem::ReadTimeItemGoods - Time item goods data loaded successfully");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::ReadTimeItemGoods - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate item code and get table information
 */
int CItemManagementSystem::GetItemTableCode(const std::string& itemCode) {
    try {
        return ::GetItemTableCode(itemCode.c_str());
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::GetItemTableCode - Exception: %s", e.what());
        return -1;
    }
}

/**
 * Get item record data
 */
_base_fld* CItemManagementSystem::GetItemRecord(int tableCode, const std::string& itemCode) {
    try {
        // In real implementation, this would access the appropriate record data structure
        // based on the table code and retrieve the item record
        return nullptr; // Placeholder
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::GetItemRecord - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Calculate item durability
 */
uint32_t CItemManagementSystem::CalculateItemDurability(int tableCode, uint32_t itemIndex) {
    try {
        return ::GetItemDurPoint(tableCode, itemIndex);
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::CalculateItemDurability - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Process item upgrade
 */
ItemOperationDetails CItemManagementSystem::ProcessItemUpgrade(ItemInfo& itemInfo, const ItemUpgradeInfo& upgradeInfo) {
    auto startTime = std::chrono::high_resolution_clock::now();

    try {
        if (!upgradeInfo.IsValid()) {
            return CreateResult(ItemOperationResult::InvalidUpgrade, startTime, "Invalid upgrade information");
        }

        if (!upgradeInfo.CanUpgrade()) {
            return CreateResult(ItemOperationResult::InvalidUpgrade, startTime, "Item cannot be upgraded further");
        }

        // Apply upgrade
        uint32_t newLevel = ApplyItemUpgrades(itemInfo, upgradeInfo, 1);
        itemInfo.level = newLevel;
        itemInfo.isUpgraded = true;

        // Update statistics
        m_stats.totalItemsUpgraded++;
        m_stats.RecordOperation(true);

        ItemOperationDetails result = CreateResult(ItemOperationResult::Success, startTime);
        result.itemInfo = itemInfo;
        result.operationId = GenerateOperationId();

        LogOperation(result);

        if (m_operationCallback) {
            m_operationCallback(result);
        }

        return result;

    } catch (const std::exception& e) {
        m_stats.RecordOperation(false);
        Logger::Error("CItemManagementSystem::ProcessItemUpgrade - Exception: %s", e.what());
        return CreateResult(ItemOperationResult::SystemError, startTime,
                          std::string("Exception: ") + e.what());
    }
}

/**
 * Check if item is overlap type
 */
bool CItemManagementSystem::IsOverlapItem(int tableCode) {
    try {
        return ::IsOverLapItem(tableCode);
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::IsOverlapItem - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get item kind code
 */
uint8_t CItemManagementSystem::GetItemKindCode(int tableCode) {
    try {
        return ::GetItemKindCode(tableCode);
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::GetItemKindCode - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Get default upgrade socket number
 */
uint8_t CItemManagementSystem::GetDefaultUpgradeSocketCount(int tableCode, uint32_t itemIndex) {
    try {
        return ::GetDefItemUpgSocketNum(tableCode, itemIndex);
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::GetDefaultUpgradeSocketCount - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Find time item record
 */
_TimeItem_fld* CItemManagementSystem::FindTimeItemRecord(int tableCode, uint32_t itemIndex) {
    try {
        return ::TimeItem_FindTimeRec(tableCode, itemIndex);
    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::FindTimeItemRecord - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Reset statistics
 */
void CItemManagementSystem::ResetStatistics() {
    try {
        std::lock_guard<std::mutex> lock(m_statsMutex);

        m_stats.totalItemsCreated = 0;
        m_stats.totalItemsLooted = 0;
        m_stats.totalItemsUpgraded = 0;
        m_stats.totalItemsExpired = 0;
        m_stats.successfulOperations = 0;
        m_stats.failedOperations = 0;
        m_stats.lastOperation = std::chrono::system_clock::now();

        Logger::Debug("CItemManagementSystem::ResetStatistics - Statistics reset");

    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::ResetStatistics - Exception: %s", e.what());
    }
}

/**
 * Set operation callback for monitoring
 */
void CItemManagementSystem::SetOperationCallback(std::function<void(const ItemOperationDetails&)> callback) {
    m_operationCallback = callback;
}

/**
 * Validate item creation context
 */
bool CItemManagementSystem::ValidateCreationContext(const ItemCreationContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemManagementSystem::ValidateCreationContext - Invalid context");
            return false;
        }

        if (!ItemManagementUtils::ValidateItemCode(context.itemCode)) {
            Logger::Warning("CItemManagementSystem::ValidateCreationContext - Invalid item code: %s",
                           context.itemCode.c_str());
            return false;
        }

        if (context.quantity <= 0 || context.quantity > 100) {
            Logger::Warning("CItemManagementSystem::ValidateCreationContext - Invalid quantity: %d",
                           context.quantity);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::ValidateCreationContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create storage list item
 */
_STORAGE_LIST* CItemManagementSystem::CreateStorageListItem(const ItemCreationContext& context, const ItemInfo& itemInfo) {
    try {
        // In real implementation, this would create a _STORAGE_LIST structure
        // with the item information and return it
        // For now, return nullptr as placeholder
        return nullptr;

    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::CreateStorageListItem - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Apply item upgrades
 */
uint32_t CItemManagementSystem::ApplyItemUpgrades(const ItemInfo& itemInfo, const ItemUpgradeInfo& upgradeInfo, int upgradeLevel) {
    try {
        uint32_t currentLevel = itemInfo.level;

        // Get socket count for the item
        uint8_t socketCount = GetDefaultUpgradeSocketCount(itemInfo.tableCode, itemInfo.itemIndex);

        // Apply upgrades up to the specified level
        for (int i = 0; i < upgradeLevel && i < socketCount; ++i) {
            // Calculate upgrade bits (original logic from _loot_item_1400BEFC0.c lines 98-104)
            uint32_t upgradeBits = ItemManagementUtils::CalculateUpgradeBits(
                currentLevel, upgradeInfo.upgradeIndex, i);
            currentLevel = upgradeBits;
        }

        return currentLevel;

    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::ApplyItemUpgrades - Exception: %s", e.what());
        return itemInfo.level;
    }
}

/**
 * Set time item properties
 */
void CItemManagementSystem::SetTimeItemProperties(ItemInfo& itemInfo, const _TimeItem_fld* timeRecord) {
    try {
        if (!timeRecord) {
            return;
        }

        itemInfo.isTimeItem = true;

        // Set expiration time based on time record
        // In real implementation, this would use the time record data
        // to calculate the expiration time
        auto now = std::chrono::system_clock::now();
        itemInfo.expirationTime = now + std::chrono::hours(24); // Default 24 hours

        Logger::Debug("CItemManagementSystem::SetTimeItemProperties - Set time item properties for: %s",
                     itemInfo.itemCode.c_str());

    } catch (const std::exception& e) {
        Logger::Error("CItemManagementSystem::SetTimeItemProperties - Exception: %s", e.what());
    }
}

/**
 * Log operation details
 */
void CItemManagementSystem::LogOperation(const ItemOperationDetails& details) {
    try {
        if (m_bDetailedLogging) {
            Logger::Info("ItemManagement Operation - Result: %s, Item: %s, Time: %lldms",
                        details.GetResultString().c_str(),
                        details.itemInfo.itemCode.c_str(),
                        details.executionTime.count());
        }

        if (!details.IsSuccess()) {
            Logger::Warning("ItemManagement Operation Failed - %s: %s",
                           details.GetResultString().c_str(),
                           details.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        // Don't log errors in logging function to avoid recursion
    }
}

/**
 * Create operation result with timing
 */
ItemOperationDetails CItemManagementSystem::CreateResult(ItemOperationResult result,
                                                        std::chrono::high_resolution_clock::time_point startTime,
                                                        const std::string& errorMessage) {
    ItemOperationDetails operationResult;
    operationResult.result = result;
    operationResult.errorMessage = errorMessage;
    operationResult.operationId = GenerateOperationId();
    operationResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return operationResult;
}

/**
 * Item History Manager Implementation
 */

/**
 * Constructor
 */
CItemHistoryManager::CItemHistoryManager() {
    UpdateDateTime();
    Logger::Debug("CItemHistoryManager::CItemHistoryManager - Item history manager initialized");
}

/**
 * Destructor
 */
CItemHistoryManager::~CItemHistoryManager() {
    try {
        Logger::Debug("CItemHistoryManager::~CItemHistoryManager - Item history manager destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Log lend item deletion from inventory
 * Refactored from: lenditem_del_from_invenCMgrAvatorItemHistoryQEAAXE_140240BD0.c
 */
void CItemHistoryManager::LogLendItemDeletion(uint8_t tableCode, uint16_t itemIndex, uint64_t uniqueId, const std::string& fileName) {
    try {
        std::lock_guard<std::mutex> lock(m_logMutex);

        UpdateDateTime();

        // Create log entry (original logic from lenditem_del_from_invenCMgrAvatorItemHistoryQEAAXE_140240BD0.c)
        std::string logData = "\r\nLEND ITEM DELETE FROM INVEN [" + m_currentDate + " " + m_currentTime + "]\r\n";
        logData += "TableCode: " + std::to_string(tableCode) + "\r\n";
        logData += "ItemIndex: " + std::to_string(itemIndex) + "\r\n";
        logData += "UniqueId: " + std::to_string(uniqueId) + "\r\n";

        WriteLogFile(fileName, logData);

        Logger::Debug("CItemHistoryManager::LogLendItemDeletion - Logged lend item deletion: %d/%d",
                     tableCode, itemIndex);

    } catch (const std::exception& e) {
        Logger::Error("CItemHistoryManager::LogLendItemDeletion - Exception: %s", e.what());
    }
}

/**
 * Log item serial full event
 * Refactored from: item_serial_fullCMgrAvatorItemHistoryQEAA_140237500.c
 */
void CItemHistoryManager::LogItemSerialFull(int serialNumber, const std::string& fileName) {
    try {
        std::lock_guard<std::mutex> lock(m_logMutex);

        UpdateDateTime();

        // Create log entry (original logic from item_serial_fullCMgrAvatorItemHistoryQEAA_140237500.c line 22)
        std::string logData = "\r\nITEM SERIAL FULL [" + m_currentDate + " " + m_currentTime + "]\r\n";
        logData += "Serial Number: " + std::to_string(serialNumber) + "\r\n";

        WriteLogFile(fileName, logData);

        Logger::Warning("CItemHistoryManager::LogItemSerialFull - Item serial full: %d", serialNumber);

    } catch (const std::exception& e) {
        Logger::Error("CItemHistoryManager::LogItemSerialFull - Exception: %s", e.what());
    }
}

/**
 * Log item close event with detailed inventory
 * Refactored from: have_item_closeCMgrAvatorItemHistoryQEAAXHPEADPEAU_140237500.c
 */
void CItemHistoryManager::LogItemClose(const std::string& playerName, const std::string& fileName) {
    try {
        std::lock_guard<std::mutex> lock(m_logMutex);

        UpdateDateTime();

        // Create log entry (original logic from have_item_closeCMgrAvatorItemHistoryQEAAXHPEADPEAU_140237500.c)
        std::string logData = "\r\nITEM CLOSE [" + m_currentDate + " " + m_currentTime + "]\r\n";
        logData += "Player: " + playerName + "\r\n";
        logData += "Detailed inventory information would be logged here\r\n";

        WriteLogFile(fileName, logData);

        Logger::Debug("CItemHistoryManager::LogItemClose - Logged item close for player: %s", playerName.c_str());

    } catch (const std::exception& e) {
        Logger::Error("CItemHistoryManager::LogItemClose - Exception: %s", e.what());
    }
}

/**
 * Write log entry to file
 */
void CItemHistoryManager::WriteLogFile(const std::string& fileName, const std::string& logData) {
    try {
        // In real implementation, this would write to the actual log file
        // For now, just log to the system logger
        Logger::Info("ItemHistory Log [%s]: %s", fileName.c_str(), logData.c_str());

    } catch (const std::exception& e) {
        Logger::Error("CItemHistoryManager::WriteLogFile - Exception: %s", e.what());
    }
}

/**
 * Update current date and time
 */
void CItemHistoryManager::UpdateDateTime() {
    try {
        uint32_t koreanTime = GetKoreanLocalTime();

        // Convert to date and time strings
        // In real implementation, this would format the Korean time properly
        m_currentDate = "2024-01-01"; // Placeholder
        m_currentTime = "12:00:00";   // Placeholder

    } catch (const std::exception& e) {
        Logger::Error("CItemHistoryManager::UpdateDateTime - Exception: %s", e.what());
    }
}

/**
 * Get Korean local time
 */
uint32_t CItemHistoryManager::GetKoreanLocalTime() {
    try {
        return ::GetKorLocalTime();
    } catch (const std::exception& e) {
        Logger::Error("CItemHistoryManager::GetKoreanLocalTime - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Legacy compatibility functions
 */
namespace LegacyCompatibility {

/**
 * Legacy loot item function wrapper
 */
char loot_item_Legacy(CPlayer* pOwner, char* pszItemCode, int nNum, char* pszUpTalCode, int nUpNum) {
    try {
        static CItemManagementSystem itemManager;

        if (!pOwner || !pszItemCode) {
            return 0;
        }

        ItemCreationContext context;
        context.pOwner = pOwner;
        context.itemCode = ItemManagementUtils::ConvertItemCode(pszItemCode);
        context.quantity = nNum;

        if (pszUpTalCode) {
            context.upgradeCode = ItemManagementUtils::ConvertItemCode(pszUpTalCode);
            context.upgradeLevel = nUpNum;
        }

        // Set default position and map from player
        // In real implementation: get from player's current position and map
        context.position[0] = 0.0f;
        context.position[1] = 0.0f;
        context.position[2] = 0.0f;

        ItemOperationDetails result = itemManager.CreateLootItem(context);
        return result.IsSuccess() ? 1 : 0;

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::loot_item_Legacy - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Legacy create item box function wrapper
 */
CItemBox* CreateItemBox_Legacy(_STORAGE_LIST* pItem, CPlayer* pOwner, uint32_t dwPartyBossSerial,
                              bool bPartyShare, void* pThrower, uint8_t byCreateCode,
                              CMapData* pMap, uint16_t wLayerIndex, float* pStdPos, bool bHide) {
    try {
        if (!pItem || !pOwner || !pMap || !pStdPos) {
            return nullptr;
        }

        // Call legacy CreateItemBox function
        return ::CreateItemBox(pItem, pOwner, dwPartyBossSerial, bPartyShare, pThrower,
                              byCreateCode, pMap, wLayerIndex, pStdPos, bHide);

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::CreateItemBox_Legacy - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Legacy time item initialization wrapper
 */
bool InitTimeItem_Legacy() {
    try {
        static CItemManagementSystem itemManager;
        return itemManager.InitializeTimeItemSystem();

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::InitTimeItem_Legacy - Exception: %s", e.what());
        return false;
    }
}

} // namespace LegacyCompatibility

/**
 * Utility functions for item management
 */
namespace ItemManagementUtils {

/**
 * Convert legacy item code to modern string
 */
std::string ConvertItemCode(const char* pszItemCode) {
    if (!pszItemCode) {
        return "";
    }

    try {
        std::string result(pszItemCode);

        // Trim whitespace
        result.erase(0, result.find_first_not_of(" \t\n\r"));
        result.erase(result.find_last_not_of(" \t\n\r") + 1);

        // Convert to uppercase for consistency
        std::transform(result.begin(), result.end(), result.begin(), ::toupper);

        return result;

    } catch (const std::exception& e) {
        Logger::Error("ItemManagementUtils::ConvertItemCode - Exception: %s", e.what());
        return "";
    }
}

/**
 * Validate item code format
 */
bool ValidateItemCode(const std::string& itemCode) {
    if (itemCode.empty() || itemCode.length() > 32) {
        return false;
    }

    // Check for valid characters (alphanumeric and underscore)
    for (char c : itemCode) {
        if (!std::isalnum(c) && c != '_') {
            return false;
        }
    }

    return true;
}

/**
 * Calculate upgrade bit mask
 */
uint32_t CalculateUpgradeBits(uint32_t currentBits, uint32_t upgradeIndex, int socketIndex) {
    try {
        // Call legacy upgrade bit calculation function
        return ::GetBitAfterUpgrade(currentBits, upgradeIndex, socketIndex);

    } catch (const std::exception& e) {
        Logger::Error("ItemManagementUtils::CalculateUpgradeBits - Exception: %s", e.what());
        return currentBits;
    }
}

/**
 * Get current time as 32-bit timestamp
 */
uint32_t GetCurrentTime32() {
    try {
        uint32_t currentTime = 0;
        ::_time32(&currentTime);
        return currentTime;

    } catch (const std::exception& e) {
        Logger::Error("ItemManagementUtils::GetCurrentTime32 - Exception: %s", e.what());
        return 0;
    }
}

} // namespace ItemManagementUtils

} // namespace Items
} // namespace NexusProtection
