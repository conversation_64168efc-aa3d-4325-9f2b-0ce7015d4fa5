/*
 * Function: ??0CReturnGateCreateParam@@QEAA@PEAVCPlayer@@@Z
 * Address: 0x1401684E0
 */

void __fastcall CReturnGateCreateParam::CReturnGateCreateParam(CReturnGateCreateParam *this, CPlayer *pkOwner)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CReturnGateCreateParam *v5; // [sp+30h] [bp+8h]@1
  CPlayer *v6; // [sp+38h] [bp+10h]@1

  v6 = pkOwner;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _object_create_setdata::_object_create_setdata((_object_create_setdata *)&v5->m_pRecordSet);
  v5->m_pkOwner = 0i64;
  if ( v6 )
  {
    if ( v6->m_pCurMap )
    {
      v5->m_pkOwner = v6;
      v5->m_nLayerIndex = v5->m_pkOwner->m_wMapLayerIndex;
      v5->m_pMap = v5->m_pkOwner->m_pCurMap;
      CMapData::GetRandPosInRange(v5->m_pMap, v5->m_pkOwner->m_fCurPos, 10, v5->m_fStartPos);
      v5->m_pRecordSet = 0i64;
    }
  }
}
