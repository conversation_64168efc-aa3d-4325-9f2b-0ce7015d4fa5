/*
 * Function: ?GetRemainNumOfGood@CashItemRemoteStore@@QEAAHQEAD@Z
 * Address: 0x1402F5D60
 */

__int64 __fastcall CashItemRemoteStore::GetRemainNumOfGood(CashItemRemoteStore *this, char *strCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@6
  CashItemRemoteStore *v8; // [sp+40h] [bp+8h]@1
  const char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = strCode;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->_pkRemainInfo )
  {
    for ( j = 0; ; ++j )
    {
      v5 = CRecordData::GetRecordNum(&v8->_kRecGoods);
      if ( j >= v5 )
        break;
      if ( !strncmp(v8->_pkRemainInfo[j].strCode, Str2, 8ui64) )
        return v8->_pkRemainInfo[j].nRemainNum;
    }
    result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
