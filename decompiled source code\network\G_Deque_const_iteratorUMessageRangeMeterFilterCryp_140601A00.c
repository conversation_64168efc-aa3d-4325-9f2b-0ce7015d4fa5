/*
 * Function: ??G?$_Deque_const_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEBA_JAEBV01@@Z
 * Address: 0x140601A00
 */

__int64 __fastcall std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+0h] [bp-18h]@2

  if ( *(_QWORD *)(a2 + 24) > *(_QWORD *)(a1 + 24) )
    v3 = -(*(_QWORD *)(a2 + 24) - *(_QWORD *)(a1 + 24));
  else
    v3 = *(_QWORD *)(a1 + 24) - *(_QWORD *)(a2 + 24);
  return v3;
}
