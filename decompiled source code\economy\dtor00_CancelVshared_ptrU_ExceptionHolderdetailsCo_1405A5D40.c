/*
 * Function: ?dtor$0@?0???$_Cancel@V?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@@?$task_completion_event@E@Concurrency@@QEBA_NV?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@PEAX@Z@4HA_5
 * Address: 0x1405A5D40
 */

void __fastcall `Concurrency::task_completion_event<unsigned char>::_Cancel<std::shared_ptr<Concurrency::details::_ExceptionHolder>>'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(*(_QWORD *)(a2 + 120));
}
