/*
 * Function: ?Init@CRaceBuffInfoByHolyQuestfGroup@@QEAA_NXZ
 * Address: 0x1403B4A30
 */

char __fastcall CRaceBuffInfoByHolyQuestfGroup::Init(CRaceBuffInfoByHolyQuestfGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@5
  char result; // al@5
  CRaceBuffInfoByHolyQuest **v5; // rax@11
  __int64 v6; // [sp+0h] [bp-78h]@1
  __int64 v7; // [sp+28h] [bp-50h]@6
  char v8; // [sp+30h] [bp-48h]@6
  char _Pos[12]; // [sp+54h] [bp-24h]@4
  __int64 v10; // [sp+60h] [bp-18h]@11
  std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *> > *v11; // [sp+68h] [bp-10h]@11
  CRaceBuffInfoByHolyQuestfGroup *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v1 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_QWORD *)&_Pos[4] = 0i64;
  std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::assign(
    &v12->m_vecInfo,
    4ui64,
    (CRaceBuffInfoByHolyQuest *const *)&_Pos[4]);
  if ( std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::size(&v12->m_vecInfo) == 4 )
  {
    v7 = 0i64;
    memset(&v8, 0, 0x18ui64);
    for ( *(_DWORD *)_Pos = 0; *(_DWORD *)_Pos < 4u; ++*(_DWORD *)_Pos )
    {
      *(&v7 + *(unsigned int *)_Pos) = (__int64)CRaceBuffInfoByHolyQuest::Create(
                                                  v12->m_uiNTh,
                                                  CRaceBuffInfoByHolyQuestfGroup::TYPE_NAME[(unsigned __int64)*(unsigned int *)_Pos]);
      if ( *(&v7 + *(unsigned int *)_Pos) )
      {
        v10 = *(unsigned int *)_Pos;
        v11 = &v12->m_vecInfo;
        v5 = std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::operator[](
               &v12->m_vecInfo,
               *(unsigned int *)_Pos);
        *v5 = (CRaceBuffInfoByHolyQuest *)*(&v7 + v10);
      }
      else
      {
        CLogFile::Write(
          &stru_1799C8F30,
          "CRaceBuffInfoByHolyQuestfGroup::Init() : CRaceBuffInfoByHolyQuest::Create( m_uiNTh(%u), TYPE_NAME[i](%s) ) NULL!",
          *(unsigned int *)_Pos,
          CRaceBuffInfoByHolyQuestfGroup::TYPE_NAME[(unsigned __int64)*(unsigned int *)_Pos]);
      }
    }
    result = 1;
  }
  else
  {
    v3 = std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::size(&v12->m_vecInfo);
    CLogFile::Write(
      &stru_1799C8F30,
      "CRaceBuffInfoByHolyQuestfGroup::Init() : MAX_TYPE_NUM(%u) != m_vecInfo.size()(%d)",
      4i64,
      v3);
    result = 0;
  }
  return result;
}
