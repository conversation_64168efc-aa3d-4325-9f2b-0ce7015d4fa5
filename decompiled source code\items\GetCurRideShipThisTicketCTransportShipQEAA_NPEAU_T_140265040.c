/*
 * Function: ?GetCurRideShipThisTicket@CTransportShip@@QEAA_NPEAU_TicketItem_fld@@@Z
 * Address: 0x140265040
 */

bool __fastcall CTransportShip::GetCurRideShipThisTicket(CTransportShip *this, _TicketItem_fld *pTicketFld)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned __int8 v6; // [sp+20h] [bp-18h]@6
  int v7; // [sp+24h] [bp-14h]@7
  CTransportShip *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pTicketFld->m_strCivil[2 * v8->m_byRaceCode_Layer] == 49 )
  {
    v6 = v8->m_byDirect;
    if ( v8->m_bAnchor )
    {
      v7 = v8->m_byDirect == 0;
      v6 = v7;
    }
    result = strcmp_0(v8->m_pLinkPortMap[v6]->m_pMapSet->m_strCode, pTicketFld->m_strMapCode) == 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
