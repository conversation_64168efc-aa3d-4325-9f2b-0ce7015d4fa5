/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@PEAVCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@2@@stdext@@YAPEAVCUnmannedTraderItemCodeInfo@@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@0PEAV1@AEAV?$allocator@VCUnmannedTraderItemCodeInfo@@@3@@Z
 * Address: 0x14037C3C0
 */

CUnmannedTraderItemCodeInfo *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_First, std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::allocator<CUnmannedTraderItemCodeInfo> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v6; // rax@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-A8h]@1
  CUnmannedTraderItemCodeInfo *v10; // [sp+30h] [bp-78h]@4
  std::_Range_checked_iterator_tag v11; // [sp+38h] [bp-70h]@4
  std::_Nonscalar_ptr_iterator_tag v12; // [sp+39h] [bp-6Fh]@4
  char v13; // [sp+40h] [bp-68h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v14; // [sp+58h] [bp-50h]@4
  char v15; // [sp+60h] [bp-48h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v16; // [sp+78h] [bp-30h]@4
  __int64 v17; // [sp+80h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v18; // [sp+88h] [bp-20h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v19; // [sp+90h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v20; // [sp+98h] [bp-10h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__formal; // [sp+B0h] [bp+8h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__that; // [sp+B8h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *v23; // [sp+C0h] [bp+18h]@1
  std::allocator<CUnmannedTraderItemCodeInfo> *v24; // [sp+C8h] [bp+20h]@1

  v24 = _Al;
  v23 = _Dest;
  __that = _Last;
  __formal = _First;
  v4 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = -2i64;
  memset(&v11, 0, sizeof(v11));
  v12 = std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(
          __formal,
          &v23);
  v14 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v13;
  v16 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v15;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v13,
    __that);
  v18 = v6;
  v19 = v6;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    v16,
    __formal);
  v20 = v7;
  v10 = std::_Uninit_copy<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *,std::allocator<CUnmannedTraderItemCodeInfo>>(
          v7,
          v19,
          v23,
          v24,
          v12,
          v11);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(__formal);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(__that);
  return v10;
}
