/*
 * Function: ?SelectObject@CMapDisplay@@QEAAPEAVCGameObject@@PEAVCPoint@@@Z
 * Address: 0x14019F340
 */

CGameObject *__fastcall CMapDisplay::SelectObject(CMapDisplay *this, CPoint *pt)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  __int64 v6; // [sp+8h] [bp-10h]@7
  CMapDisplay *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < CGameObject::s_nTotalObjectNum; ++j )
  {
    v6 = CGameObject::s_pTotalObject[j];
    if ( *(_BYTE *)(v6 + 24)
      && *(CMapData **)(v6 + 88) == v7->m_pActMap
      && *(_WORD *)(v6 + 104) == v7->m_wLayerIndex
      && pt->x >= *(_DWORD *)(v6 + 64)
      && pt->x <= *(_DWORD *)(v6 + 64) + 6
      && pt->y >= *(_DWORD *)(v6 + 68)
      && pt->y <= *(_DWORD *)(v6 + 68) + 6 )
    {
      return (CGameObject *)v6;
    }
  }
  return 0i64;
}
