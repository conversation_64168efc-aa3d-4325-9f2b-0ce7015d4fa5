/*
 * Function: ?GetObjectExpand@CMainThread@@QEAAPEAVCGameObject@@PEAU_object_id@@PEADG@Z
 * Address: 0x1401EC900
 */

CGameObject *__fastcall CMainThread::GetObjectExpand(CMainThread *this, _object_id *pObjID, char *szCharName, unsigned __int16 wSearchIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CGameObject *result; // rax@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  CGameObject *v8; // [sp+20h] [bp-18h]@4
  int j; // [sp+28h] [bp-10h]@4
  unsigned __int16 v10; // [sp+2Ch] [bp-Ch]@4
  CMainThread *v11; // [sp+40h] [bp+8h]@1
  _object_id *pObjIDa; // [sp+48h] [bp+10h]@1
  char *Str; // [sp+50h] [bp+18h]@1
  unsigned __int16 v14; // [sp+58h] [bp+20h]@1

  v14 = wSearchIndex;
  Str = szCharName;
  pObjIDa = pObjID;
  v11 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0i64;
  j = 0;
  v10 = 0;
  if ( !szCharName || !strlen_0(szCharName) )
    goto LABEL_38;
  if ( !pObjIDa->m_byKind && !pObjIDa->m_byID )
    return CMainThread::GetChar(v11, Str);
  if ( !pObjIDa->m_byKind && pObjIDa->m_byID == 1 )
  {
    for ( j = 0; j < 30000; ++j )
    {
      if ( *((_BYTE *)g_Monster + 6424 * j + 24)
        && *((_QWORD *)g_Monster + 803 * j + 241)
        && !strcmp_0((const char *)(*((_QWORD *)g_Monster + 803 * j + 241) + 4i64), Str) )
      {
        if ( v14 == v10 )
          return (CGameObject *)((char *)g_Monster + 6424 * j);
        ++v10;
      }
    }
    return 0i64;
  }
  if ( pObjIDa->m_byKind || pObjIDa->m_byID != 2 )
  {
LABEL_38:
    if ( v8 )
      result = v8;
    else
      result = CMainThread::GetObjectA(v11, pObjIDa);
    return result;
  }
  for ( j = 0; j < 500; ++j )
  {
    if ( g_NPC[j].m_bLive && g_NPC[j].m_pRecordSet && !strcmp_0(g_NPC[j].m_pRecordSet->m_strCode, Str) )
    {
      if ( v14 == v10 )
        return (CGameObject *)((char *)g_Monster + 6424 * j);
      ++v10;
    }
  }
  return 0i64;
}
