/*
 * Function: ??0?$_List_nod@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@2@@std@@IEAA@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@1@@Z
 * Address: 0x140316030
 */

void __fastcall std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>(std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_List_nod<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<std::pair<int const ,_TimeItem_fld const *> > *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<std::pair<int const ,_TimeItem_fld const *> > *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node>::allocator<std::_List_nod<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Node>(
    &v5->_Alnod,
    __formal);
}
