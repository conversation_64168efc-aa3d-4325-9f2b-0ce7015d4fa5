/*
 * Function: j_?GetMyThreadPool@?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@QEAAPEAVAbstractThreadPool@2@XZ
 * Address: 0x14000944E
 */

US::AbstractThreadPool *__fastcall US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::GetMyThreadPool(US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *this)
{
  return US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::GetMyThreadPool(this);
}
