/*
 * Function: ?Set<PERSON>@CPlayer@@QEAA_NH_N@Z
 * Address: 0x14005F240
 */

bool __fastcall CPlayer::SetSP(CPlayer *this, int nSP, bool bOver)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v6; // eax@8
  int v7; // eax@11
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@6
  CPlayer *v10; // [sp+40h] [bp+8h]@1
  int dwSP; // [sp+48h] [bp+10h]@1
  bool v12; // [sp+50h] [bp+18h]@1

  v12 = bOver;
  dwSP = nSP;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v10->m_bNeverDie )
    return 1;
  v9 = CPlayerDB::GetSP(&v10->m_Param);
  if ( !v12 && dwSP > v9 )
  {
    v6 = CPlayer::GetMaxSP(v10);
    if ( v9 >= v6 || dwSP <= CPlayer::GetMaxSP(v10) )
    {
      v7 = CPlayer::GetMaxSP(v10);
      if ( v9 >= v7 && dwSP >= v9 )
        return 0;
    }
    else
    {
      dwSP = CPlayer::GetMaxSP(v10);
    }
  }
  if ( dwSP < 0 )
    dwSP = 0;
  if ( v9 == dwSP )
  {
    result = 0;
  }
  else
  {
    CPlayerDB::SetSP(&v10->m_Param, dwSP);
    result = 1;
  }
  return result;
}
