/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderDivisionInfo@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140013BBA
 */

void __fastcall std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *,std::random_access_iterator_tag>(CUnmannedTraderDivisionInfo **_First, unsigned __int64 _Count, CUnmannedTraderDivisionInfo *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CUnmannedTraderDivisionInfo * *,unsigned __int64,CUnmannedTraderDivisionInfo *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
