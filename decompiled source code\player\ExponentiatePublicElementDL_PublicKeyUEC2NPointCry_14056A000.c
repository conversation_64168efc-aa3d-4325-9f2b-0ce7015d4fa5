/*
 * Function: ?ExponentiatePublicElement@?$DL_PublicKey@UEC2NPoint@CryptoPP@@@CryptoPP@@UEBA?AUEC2NPoint@2@AEBVInteger@2@@Z
 * Address: 0x14056A000
 */

__int64 __fastcall CryptoPP::DL_PublicKey<CryptoPP::EC2NPoint>::ExponentiatePublicElement(int (***a1)(void), __int64 a2, __int64 a3)
{
  __int64 v3; // rax@1
  __int64 v4; // ST20_8@1
  __int64 v5; // rax@1
  __int64 v6; // ST30_8@1
  __int64 v7; // rax@1
  int (***v9)(void); // [sp+50h] [bp+8h]@1
  __int64 v10; // [sp+58h] [bp+10h]@1
  __int64 v11; // [sp+60h] [bp+18h]@1

  v11 = a3;
  v10 = a2;
  v9 = a1;
  LODWORD(v3) = (**a1)();
  v4 = v3;
  LODWORD(v5) = ((int (__fastcall *)(int (***)(void)))(*v9)[6])(v9);
  v6 = v5;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v4 + 40i64))(v4);
  (*(void (__fastcall **)(__int64, __int64, __int64, __int64))(*(_QWORD *)v6 + 48i64))(v6, v10, v7, v11);
  return v10;
}
