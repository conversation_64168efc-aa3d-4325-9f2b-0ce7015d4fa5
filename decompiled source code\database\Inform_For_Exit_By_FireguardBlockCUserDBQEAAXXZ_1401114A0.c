/*
 * Function: ?Inform_For_Exit_By_FireguardBlock@CUserDB@@QEAAXXZ
 * Address: 0x1401114A0
 */

void __fastcall CUserDB::Inform_For_Exit_By_FireguardBlock(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  __int64 v4; // [sp+0h] [bp-98h]@1
  char Dst; // [sp+38h] [bp-60h]@4
  unsigned int v6; // [sp+45h] [bp-53h]@4
  unsigned int v7; // [sp+49h] [bp-4Fh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v9; // [sp+65h] [bp-33h]@4
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  CUserDB *v11; // [sp+A0h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  strcpy_s(&Dst, 0xDui64, v11->m_szAccountID);
  v6 = v11->m_dwAccountSerial;
  v7 = v11->m_dwIP;
  pbyType = 1;
  v9 = 22;
  v3 = _fireguard_block_request_wrac::size((_fireguard_block_request_wrac *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &Dst, v3);
}
