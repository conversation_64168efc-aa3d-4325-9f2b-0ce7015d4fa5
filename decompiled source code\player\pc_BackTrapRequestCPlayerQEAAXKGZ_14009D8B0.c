/*
 * Function: ?pc_BackTrapRequest@CPlayer@@QEAAXKG@Z
 * Address: 0x14009D8B0
 */

void __usercall CPlayer::pc_BackTrapRequest(CPlayer *this@<rcx>, unsigned int dwTrapObjSerial@<edx>, unsigned __int16 wAddSerial@<r8w>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@22
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+20h] [bp-28h]@4
  CTrap *pTrap; // [sp+28h] [bp-20h]@4
  int j; // [sp+30h] [bp-18h]@8
  CPlayer *v11; // [sp+50h] [bp+8h]@1
  unsigned int v12; // [sp+58h] [bp+10h]@1
  unsigned __int16 v13; // [sp+60h] [bp+18h]@1

  v13 = wAddSerial;
  v12 = dwTrapObjSerial;
  v11 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 2;
  pTrap = 0i64;
  if ( v11->m_pmTrp.m_nCount > 0 )
  {
    for ( j = 0; j < 20; ++j )
    {
      if ( _TRAP_PARAM::_param::isLoad((_TRAP_PARAM::_param *)&v11->m_pmTrp + j)
        && v11->m_pmTrp.m_Item[j].dwSerial == v12 )
      {
        pTrap = v11->m_pmTrp.m_Item[j].pItem;
        if ( !pTrap )
        {
          v8 = 2;
          goto $RESULT_16;
        }
        if ( pTrap->m_dwMasterSerial != v11->m_dwObjSerial )
        {
          v8 = 21;
          goto $RESULT_16;
        }
        GetSqrt(v11->m_fCurPos, pTrap->m_fCurPos);
        if ( a4 > 150.0 )
        {
          v8 = 22;
          goto $RESULT_16;
        }
        v8 = 0;
        break;
      }
    }
    if ( v8 )
    {
      v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &stru_1799C8E78,
        "CPlayer::pc_BackTrapRequest() : Can't find trap (Player:%s Count:%d)",
        v6,
        v11->m_pmTrp.m_nCount);
    }
  }
  else
  {
    if ( v11->m_pmTrp.m_nCount < 0 )
      CLogFile::Write(
        &stru_1799C8E78,
        "CPlayer::pc_BackTrapRequest() : m_pmTrp.m_nCount ZERO and less ( %d )",
        v11->m_pmTrp.m_nCount);
    v8 = 2;
  }
$RESULT_16:
  if ( !v8 )
  {
    CPlayer::_TrapReturn(v11, pTrap, v13);
    CTrap::Destroy(pTrap, 2);
  }
  CPlayer::SendMsg_BackTrapResult(v11, v8);
}
