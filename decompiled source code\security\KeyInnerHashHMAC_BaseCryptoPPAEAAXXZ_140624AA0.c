/*
 * Function: ?KeyInnerHash@HMAC_Base@CryptoPP@@AEAAXXZ
 * Address: 0x140624AA0
 */

void __fastcall CryptoPP::HMAC_Base::KeyInnerHash(CryptoPP::HMAC_Base *this)
{
  __int64 v1; // rax@3
  __int64 v2; // ST20_8@3
  __int64 v3; // ST28_8@3
  unsigned __int8 *v4; // rax@3
  CryptoPP::HMAC_Base *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  if ( this->m_innerHashKeyed )
    _wassert(L"!m_innerHashKeyed", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\hmac.cpp", 0x2Du);
  LODWORD(v1) = ((int (*)(void))this->vfptr[9].__vecDelDtor)();
  v2 = v1;
  v3 = (unsigned int)(*(int (__fastcall **)(__int64))(*(_QWORD *)v1 + 64i64))(v1);
  v4 = CryptoPP::HMAC_Base::AccessIpad(v5);
  (*(void (__fastcall **)(__int64, unsigned __int8 *, __int64))(*(_QWORD *)v2 + 24i64))(v2, v4, v3);
  v5->m_innerHashKeyed = 1;
}
