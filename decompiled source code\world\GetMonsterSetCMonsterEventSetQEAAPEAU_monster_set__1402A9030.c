/*
 * Function: ?GetMonsterSet@CMonsterEventSet@@QEAAPEAU_monster_set@_event_set@@PEAU3@@Z
 * Address: 0x1402A9030
 */

_event_set::_monster_set *__fastcall CMonsterEventSet::GetMonsterSet(CMonsterEventSet *this, _event_set *pEventSet)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1

  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 10; ++j )
  {
    if ( !pEventSet->m_MonSet[j].bIsSet )
      return &pEventSet->m_MonSet[j];
  }
  return 0i64;
}
