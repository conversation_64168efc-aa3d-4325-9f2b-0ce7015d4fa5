/*
 * Function: ?NetClose@CGuildBattleController@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403D64D0
 */

void __fastcall CGuildBattleController::NetClose(CGuildBattleController *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleManager *v4; // rax@8
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-58h]@1
  unsigned int v7; // [sp+20h] [bp-38h]@10
  int v8; // [sp+28h] [bp-30h]@10
  int v9; // [sp+30h] [bp-28h]@5
  unsigned int dwGuildSerial; // [sp+34h] [bp-24h]@8
  unsigned int dwCharacSerial; // [sp+38h] [bp-20h]@8
  char v12; // [sp+3Ch] [bp-1Ch]@8
  unsigned int v13; // [sp+40h] [bp-18h]@6
  int v14; // [sp+44h] [bp-14h]@10
  char *v15; // [sp+48h] [bp-10h]@10
  CPlayer *pkPlayera; // [sp+68h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer )
  {
    v9 = CPlayerDB::GetRaceCode(&pkPlayer->m_Param);
    if ( pkPlayera->m_Param.m_pGuild )
      v13 = pkPlayera->m_Param.m_pGuild->m_dwSerial;
    else
      v13 = -1;
    dwGuildSerial = v13;
    dwCharacSerial = pkPlayera->m_pUserDB->m_dwSerial;
    v4 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
    v12 = GUILD_BATTLE::CNormalGuildBattleManager::NetClose(v4, dwGuildSerial, dwCharacSerial, pkPlayera);
    if ( pkPlayera->m_bInGuildBattle )
    {
      if ( v12 )
      {
        v14 = (unsigned __int8)v12;
        v15 = CPlayerDB::GetCharNameW(&pkPlayera->m_Param);
        v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        v8 = v14;
        v7 = dwCharacSerial;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v5,
          "CGuildBattleController::NetClose( %s ) : CNormalGuildBattleManager::Instance()->NetClose( %u, %u ) Return(%u) Fail!",
          v15,
          dwGuildSerial);
      }
    }
  }
}
