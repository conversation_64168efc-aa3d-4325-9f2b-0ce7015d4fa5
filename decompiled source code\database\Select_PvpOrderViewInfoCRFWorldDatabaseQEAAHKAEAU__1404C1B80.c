/*
 * Function: ?Select_PvpOrderViewInfo@CRFWorldDatabase@@QEAAHKAEAU_pvporderview_info@@@Z
 * Address: 0x1404C1B80
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PvpOrderViewInfo(CRFWorldDatabase *this, unsigned int dwSerial, _pvporderview_info *kInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-208h]@1
  void *SQLStmt; // [sp+20h] [bp-1E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-1E0h]@22
  char Dest; // [sp+40h] [bp-1C8h]@4
  SQLLEN v10; // [sp+158h] [bp-B0h]@22
  __int16 v11; // [sp+164h] [bp-A4h]@9
  int v12; // [sp+168h] [bp-A0h]@4
  __int16 TargetValue; // [sp+178h] [bp-90h]@22
  unsigned __int16 v14; // [sp+17Ah] [bp-8Eh]@25
  unsigned __int16 v15; // [sp+17Ch] [bp-8Ch]@25
  unsigned __int16 v16; // [sp+17Eh] [bp-8Ah]@25
  unsigned __int16 v17; // [sp+180h] [bp-88h]@25
  unsigned __int16 v18; // [sp+182h] [bp-86h]@25
  tm _Tm; // [sp+1A8h] [bp-60h]@25
  __int64 v20; // [sp+1D8h] [bp-30h]@25
  unsigned __int8 v21; // [sp+1E0h] [bp-28h]@16
  int j; // [sp+1E4h] [bp-24h]@22
  unsigned __int64 v23; // [sp+1F0h] [bp-18h]@4
  CRFWorldDatabase *v24; // [sp+210h] [bp+8h]@1
  _pvporderview_info *v25; // [sp+220h] [bp+18h]@1

  v25 = kInfo;
  v24 = this;
  v3 = &v6;
  for ( i = 128i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v23 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  sprintf(&Dest, "{ CALL pSelect_PvpOrderView( %u ) }", dwSerial);
  if ( v24->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v24->vfptr, &Dest);
  if ( v24->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v24->vfptr) )
  {
    v11 = SQLExecDirect_0(v24->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v24->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v11, v24->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v24->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v21 = 0;
        if ( v11 == 100 )
        {
          v21 = 2;
        }
        else
        {
          SQLStmt = v24->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v24->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v24->vfptr, v11, v24->m_hStmtSelect);
          v21 = 1;
        }
        if ( v24->m_hStmtSelect )
          SQLCloseCursor_0(v24->m_hStmtSelect);
        result = v21;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 1u, 93, &TargetValue, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 2u, 4, &v25->nDeath, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 3u, 4, &v25->nKill, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 4u, 8, &v25->dTodayStacked, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 5u, 8, &v25->dPvpPoint, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 6u, 8, &v25->dPvpTempCash, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 7u, 8, &v25->dPvpCash, 0i64, &v10);
        for ( j = 0; j < 10; ++j )
        {
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v24->m_hStmtSelect, j + 8, 4, &v25->dwKillerSerial[j], 0i64, &v10);
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 0x12u, -6, &v25->byContHaveCash, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 0x13u, -6, &v25->byContLoseCash, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v24->m_hStmtSelect, 0x14u, -7, &v25->bRaceWarRecvr, 0i64, &v10);
        _Tm.tm_year = TargetValue - 1900;
        _Tm.tm_mon = v14 - 1;
        _Tm.tm_mday = v15;
        _Tm.tm_hour = v16;
        _Tm.tm_min = v17;
        _Tm.tm_sec = v18;
        _Tm.tm_isdst = -1;
        v20 = mktime_3(&_Tm);
        if ( v20 == -1 )
          v20 = 0i64;
        v25->tUpdatedate = v20;
        if ( v24->m_hStmtSelect )
          SQLCloseCursor_0(v24->m_hStmtSelect);
        if ( v24->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v24->vfptr, "%s Success", &Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v24->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
