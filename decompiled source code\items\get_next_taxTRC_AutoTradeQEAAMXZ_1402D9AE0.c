/*
 * Function: ?get_next_tax@TRC_AutoTrade@@QEAAMXZ
 * Address: 0x1402D9AE0
 */

float __fastcall TRC_AutoTrade::get_next_tax(TRC_AutoTrade *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  TRC_AutoTrade *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return (float)(signed int)v5->m_suggested.dwNext;
}
