/*
 * Function: ?insert@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA?AV?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@2@V32@AEBU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x140592F20
 */

__int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::insert(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@3
  __int64 v5; // rax@6
  __int64 v6; // rax@6
  __int64 v8; // [sp+20h] [bp-A8h]@4
  char v9; // [sp+28h] [bp-A0h]@3
  char v10; // [sp+40h] [bp-88h]@6
  char *v11; // [sp+58h] [bp-70h]@6
  char v12; // [sp+60h] [bp-68h]@6
  int v13; // [sp+78h] [bp-50h]@1
  __int64 v14; // [sp+80h] [bp-48h]@1
  __int64 v15; // [sp+88h] [bp-40h]@2
  __int64 v16; // [sp+90h] [bp-38h]@3
  __int64 v17; // [sp+98h] [bp-30h]@3
  __int64 v18; // [sp+A0h] [bp-28h]@6
  __int64 v19; // [sp+A8h] [bp-20h]@6
  __int64 v20; // [sp+B0h] [bp-18h]@6
  __int64 v21; // [sp+D0h] [bp+8h]@1
  __int64 v22; // [sp+D8h] [bp+10h]@1
  __int64 v23; // [sp+E0h] [bp+18h]@1
  __int64 v24; // [sp+E8h] [bp+20h]@1

  v24 = a4;
  v23 = a3;
  v22 = a2;
  v21 = a1;
  v14 = -2i64;
  v13 = 0;
  if ( std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::size(a1) )
  {
    v16 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::begin(
            v21,
            (__int64)&v9);
    v17 = v16;
    v13 |= 1u;
    LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator-(
                    v23,
                    v16);
    v15 = v4;
  }
  else
  {
    v15 = 0i64;
  }
  v8 = v15;
  if ( v13 & 1 )
  {
    v13 &= 0xFFFFFFFE;
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  }
  v11 = &v10;
  LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(
                  &v10,
                  v23);
  v18 = v5;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Insert_n(
    v21,
    v5,
    1i64,
    v24);
  v6 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::begin(
         v21,
         (__int64)&v12);
  v19 = v6;
  v20 = v6;
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator+(
    v6,
    v22,
    v8);
  v13 |= 2u;
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  return v22;
}
