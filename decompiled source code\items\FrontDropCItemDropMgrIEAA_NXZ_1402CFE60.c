/*
 * Function: ?FrontDrop@CItemDropMgr@@IEAA_NXZ
 * Address: 0x1402CFE60
 */

char __fastcall CItemDropMgr::FrontDrop(CItemDropMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-108h]@1
  float *pStdPos; // [sp+20h] [bp-E8h]@14
  bool bHide; // [sp+28h] [bp-E0h]@14
  CPlayer *pAttacker; // [sp+30h] [bp-D8h]@14
  int bHolyScanner; // [sp+38h] [bp-D0h]@14
  char byEventItemLootAuth; // [sp+40h] [bp-C8h]@14
  _DropItemGroupInfo *v10; // [sp+50h] [bp-B8h]@4
  _DropItemGroupInfo *v11; // [sp+58h] [bp-B0h]@9
  unsigned int v12; // [sp+60h] [bp-A8h]@9
  unsigned __int8 v13; // [sp+64h] [bp-A4h]@9
  int nIndex; // [sp+68h] [bp-A0h]@9
  char v15; // [sp+6Ch] [bp-9Ch]@12
  char v16; // [sp+6Dh] [bp-9Bh]@12
  unsigned int v17; // [sp+70h] [bp-98h]@14
  _STORAGE_LIST::_db_con pItem; // [sp+88h] [bp-80h]@14
  int Dst; // [sp+D8h] [bp-30h]@14
  int v20; // [sp+DCh] [bp-2Ch]@14
  int v21; // [sp+E0h] [bp-28h]@14
  CItemBox *v22; // [sp+F8h] [bp-10h]@14
  CItemDropMgr *v23; // [sp+110h] [bp+8h]@1

  v23 = this;
  v1 = &v4;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = CItemDropMgr::GetFrontPtr(v23);
  if ( v10 )
  {
    if ( v10->m_dwDropCount || (CItemDropMgr::PopFront(v23), (v10 = CItemDropMgr::GetFrontPtr(v23)) != 0i64) )
    {
      v11 = v10;
      v12 = 0;
      v13 = v10->m_DropItem.byTableCode;
      nIndex = v10->m_DropItem.pFld->m_dwIndex;
      if ( IsOverLapItem(v13) )
        v12 = v11->m_DropItem.dwDur;
      else
        v12 = GetItemDurPoint(v13, nIndex);
      v15 = GetDefItemUpgSocketNum(v13, nIndex);
      v16 = 0;
      if ( (signed int)(unsigned __int8)v15 > 0 )
        v16 = rand() % (unsigned __int8)v15 + 1;
      v17 = GetBitAfterSetLimSocket(v16);
      _STORAGE_LIST::_db_con::_db_con(&pItem);
      pItem.m_byTableCode = v13;
      pItem.m_wItemIndex = nIndex;
      pItem.m_dwDur = v12;
      pItem.m_dwLv = v17;
      Dst = 0;
      v20 = 0;
      v21 = 0;
      memcpy_0(&Dst, v11->m_DropItem.fDropPos, 0xCui64);
      CMapData::GetRandPosInRange(
        v11->m_DropItem.pMap,
        v11->m_DropItem.fDropPos,
        v11->m_DropItem.nDropRange,
        (float *)&Dst);
      v22 = 0i64;
      byEventItemLootAuth = 3;
      bHolyScanner = 0;
      pAttacker = 0i64;
      bHide = 0;
      pStdPos = (float *)&Dst;
      v22 = CreateItemBox(
              &pItem,
              v11->m_DropItem.byCreateCode,
              v11->m_DropItem.pMap,
              v11->m_DropItem.wLayerIndex,
              (float *)&Dst,
              0,
              0i64,
              0,
              3);
      --v10->m_dwDropCount;
      --v23->m_dwTotalDropCount;
      if ( !v10->m_dwDropCount )
        CItemDropMgr::PopFront(v23);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
