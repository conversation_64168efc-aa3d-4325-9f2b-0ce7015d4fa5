/*
 * Function: ?CheatDropStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x1403ED7C0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleField::CheatDropStone(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-88h]@1
  char v6; // [sp+30h] [bp-58h]@6
  char szMsg[4]; // [sp+44h] [bp-44h]@8
  char pbyType; // [sp+64h] [bp-24h]@8
  char v9; // [sp+65h] [bp-23h]@8
  GUILD_BATTLE::CNormalGuildBattleField *v10; // [sp+90h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+98h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v10 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_bInit )
  {
    GUILD_BATTLE::CNormalGuildBattleField::ClearRegen(v10);
    CGravityStone::Destroy(v10->m_pkBall);
    v6 = CGravityStone::Drop(v10->m_pkBall, pkPlayera);
    if ( v6 )
    {
      result = v6;
    }
    else
    {
      *(_DWORD *)szMsg = pkPlayera->m_dwObjSerial;
      pbyType = 27;
      v9 = 81;
      CNetProcess::LoadSendMsg(unk_1414F2088, pkPlayera->m_ObjID.m_wIndex, &pbyType, szMsg, 4u);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
