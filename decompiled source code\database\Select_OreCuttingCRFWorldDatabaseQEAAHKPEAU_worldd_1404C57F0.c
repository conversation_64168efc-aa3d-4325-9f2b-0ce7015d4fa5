/*
 * Function: ?Select_OreCutting@CRFWorldDatabase@@QEAAHKPEAU_worlddb_ore_cutting@@@Z
 * Address: 0x1404C57F0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_OreCutting(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_ore_cutting *pOreCutting)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v6; // [sp+0h] [bp-298h]@1
  void *SQLStmt; // [sp+20h] [bp-278h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-270h]@24
  char DstBuf; // [sp+40h] [bp-258h]@4
  SQLLEN v10; // [sp+258h] [bp-40h]@24
  __int16 v11; // [sp+264h] [bp-34h]@9
  unsigned __int8 v12; // [sp+268h] [bp-30h]@16
  int j; // [sp+26Ch] [bp-2Ch]@22
  int v14; // [sp+270h] [bp-28h]@22
  unsigned __int64 v15; // [sp+280h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+2A0h] [bp+8h]@1
  _worlddb_ore_cutting *v17; // [sp+2B0h] [bp+18h]@1

  v17 = pOreCutting;
  v16 = this;
  v3 = &v6;
  for ( i = 164i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s(
    &DstBuf,
    0x200ui64,
    "select K0,D0,K1,D1,K2,D2,K3,D3,K4,D4,K5,D5,K6,D6,K7,D7,K8,D8,K9,D9,K10,D10,K11,D11,K12,D12,K13,D13,K14,D14,K15,D15,K"
    "16,D16,K17,D17,K18,D18,K19,D19 from tbl_OreCutting where serial=%u",
    dwSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &DstBuf);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirect_0(v16->m_hStmtSelect, &DstBuf, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v16->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v12 = 0;
        if ( v11 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v16->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
          v12 = 1;
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        result = v12;
      }
      else
      {
        v14 = 0;
        for ( j = 0; j < 20; ++j )
        {
          ++v14;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v16->m_hStmtSelect, v14, 4, (char *)v17 + 8 * j, 0i64, &v10);
          if ( v11 && v11 != 1 )
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
            if ( v16->m_hStmtSelect )
              SQLCloseCursor_0(v16->m_hStmtSelect);
            return 1i64;
          }
          ++v14;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v16->m_hStmtSelect, v14, 4, &v17->List[j].dwDur, 0i64, &v10);
          if ( v11 && v11 != 1 )
          {
            SQLStmt = v16->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &DstBuf, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
            if ( v16->m_hStmtSelect )
              SQLCloseCursor_0(v16->m_hStmtSelect);
            return 1i64;
          }
        }
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        if ( v16->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &DstBuf);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
