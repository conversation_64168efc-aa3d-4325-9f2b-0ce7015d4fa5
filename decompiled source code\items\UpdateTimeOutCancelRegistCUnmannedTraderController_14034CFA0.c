/*
 * Function: ?UpdateTimeOutCancelRegist@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034CFA0
 */

char __fastcall CUnmannedTraderController::UpdateTimeOutCancelRegist(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char *byProcRet; // [sp+20h] [bp-B8h]@4
  char *v7; // [sp+30h] [bp-A8h]@4
  char byState; // [sp+44h] [bp-94h]@4
  char v9; // [sp+54h] [bp-84h]@4
  char Dst; // [sp+68h] [bp-70h]@6
  _unmannedtrader_buy_item_info kData; // [sp+98h] [bp-40h]@6
  CUnmannedTraderController *v12; // [sp+E0h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData;
  pData[50] = 0;
  byState = -1;
  byProcRet = v7 + 50;
  v9 = CUnmannedTraderController::CheckDBItemState(v12, *v7, *((_DWORD *)v7 + 1), &byState, v7 + 50);
  if ( v7[50] )
  {
    result = v9;
  }
  else
  {
    memset_0(&Dst, 0, 0x10ui64);
    GetLocalTime((LPSYSTEMTIME)&Dst);
    memset_0(&kData, 0, 0x28ui64);
    v9 = CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(pkDB, *v7, *((_DWORD *)v7 + 1), &kData);
    if ( v9 == 1 )
    {
      v7[50] = 47;
      byProcRet = &Dst;
      CRFWorldDatabase::Update_UnmannedTraderItemState(pkDB, *v7, *((_DWORD *)v7 + 1), 9, (_SYSTEMTIME *)&Dst);
      result = 24;
    }
    else if ( v9 == 2 )
    {
      v7[50] = 48;
      result = 0;
    }
    else if ( kData.dwSeller == *((_DWORD *)v7 + 3) )
    {
      *((_DWORD *)v7 + 13) = kData.dwK;
      *((_DWORD *)v7 + 14) = kData.dwD;
      *((_DWORD *)v7 + 15) = kData.dwU;
      *((_DWORD *)v7 + 16) = kData.dwT;
      if ( kData.lnUID )
        *((_QWORD *)v7 + 9) = kData.lnUID;
      else
        *((_QWORD *)v7 + 9) = UIDGenerator::getuid(unk_1799C608C);
      if ( !v7[20] || !v7[33] )
        CRFWorldDatabase::Select_CharacterName(pkDB, *((_DWORD *)v7 + 3), v7 + 33, v7 + 20);
      byProcRet = &Dst;
      if ( CRFWorldDatabase::Update_UnmannedTraderItemState(pkDB, *v7, *((_DWORD *)v7 + 1), v7[8], (_SYSTEMTIME *)&Dst) )
      {
        result = 0;
      }
      else
      {
        v7[50] = 46;
        result = 24;
      }
    }
    else
    {
      v7[50] = 93;
      result = 0;
    }
  }
  return result;
}
