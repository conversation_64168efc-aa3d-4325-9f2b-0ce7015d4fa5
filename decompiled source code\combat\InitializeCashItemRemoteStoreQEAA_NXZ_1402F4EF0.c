/*
 * Function: ?Initialize@CashItemRemoteStore@@QEAA_NXZ
 * Address: 0x1402F4EF0
 */

bool __fastcall CashItemRemoteStore::Initialize(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CashItemRemoteStore::_InitLoggers(v5) )
  {
    if ( CashItemRemoteStore::_ReadGoods(v5) )
    {
      CMyTimer::BeginTimer(&v5->m_TotalEventTimer, 0x3E8u);
      CashItemRemoteStore::Load_Conditional_Event(v5);
      CashItemRemoteStore::load_cash_discount_event(v5);
      CashItemRemoteStore::Load_Cash_Event(v5);
      result = CashItemRemoteStore::LoadBuyCashMode(v5) != 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
