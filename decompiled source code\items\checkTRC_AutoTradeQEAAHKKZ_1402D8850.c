/*
 * Function: ?check@TRC_AutoTrade@@QEAAHKK@Z
 * Address: 0x1402D8850
 */

signed __int64 __fastcall TRC_AutoTrade::check(TRC_AutoTrade *this, unsigned int dwAvatorSerial, unsigned int dwGuildSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  TRC_AutoTrade *v7; // [sp+30h] [bp+8h]@1
  unsigned int dwSerial; // [sp+38h] [bp+10h]@1

  dwSerial = dwAvatorSerial;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( TRC_AutoTrade::IsOwnerGuild(v7, dwGuildSerial) )
  {
    if ( TRC_AutoTrade::IsMaster(v7, dwSerial) )
      result = 0i64;
    else
      result = 2i64;
  }
  else
  {
    result = 1i64;
  }
  return result;
}
