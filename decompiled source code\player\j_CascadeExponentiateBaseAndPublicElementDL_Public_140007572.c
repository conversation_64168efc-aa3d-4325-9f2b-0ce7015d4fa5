/*
 * Function: j_?CascadeExponentiateBaseAndPublicElement@?$DL_PublicKey@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@0@Z
 * Address: 0x140007572
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::CascadeExponentiateBaseAndPublicElement(CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *baseExp, CryptoPP::Integer *publicExp)
{
  return CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>::CascadeExponentiateBaseAndPublicElement(
           this,
           result,
           baseExp,
           publicExp);
}
