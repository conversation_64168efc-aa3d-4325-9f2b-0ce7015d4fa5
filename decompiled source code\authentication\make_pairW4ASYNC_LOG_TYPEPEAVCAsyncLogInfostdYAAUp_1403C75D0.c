/*
 * Function: ??$make_pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@std@@YA?AU?$pair@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@0@W4ASYNC_LOG_TYPE@@PEAVCAsyncLogInfo@@@Z
 * Address: 0x1403C75D0
 */

std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *__fastcall std::make_pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *result, ASYNC_LOG_TYPE _Val1, CAsyncLogInfo *_Val2)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  std::pair<enum ASYNC_LOG_TYPE,CAsyncLogInfo *> *v7; // [sp+30h] [bp+8h]@1
  ASYNC_LOG_TYPE _Val1a; // [sp+38h] [bp+10h]@1
  CAsyncLogInfo *_Val2a; // [sp+40h] [bp+18h]@1

  _Val2a = _Val2;
  _Val1a = _Val1;
  v7 = result;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>::pair<enum  ASYNC_LOG_TYPE,CAsyncLogInfo *>(v7, &_Val1a, &_Val2a);
  return v7;
}
