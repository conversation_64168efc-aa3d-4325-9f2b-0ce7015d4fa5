/*
 * Function: _CMoveMapLimitRightInfo::Load_::_1_::dtor$2
 * Address: 0x1403ACED0
 */

void __fastcall CMoveMapLimitRightInfo::Load_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 168));
}
