/*
 * Function: ?IsDbUpdate@RFEvent_ClassRefine@@UEAA_NK@Z
 * Address: 0x140329600
 */

bool __fastcall RFEvent_ClassRefine::IsDbUpdate(RFEvent_ClassRefine *this, unsigned int nIdx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  RFEvent_ClassRefine *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nIdx < 0x9E4 )
    result = _event_participant_classrefine::IsChanged(&v6->_pkParticipant[nIdx]);
  else
    result = 0;
  return result;
}
