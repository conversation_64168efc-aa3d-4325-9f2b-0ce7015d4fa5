/*
 * Function: j_?pc_RequestUILockUpdate@CPlayer@@QEAAXPEAD00E0@Z
 * Address: 0x140011CFC
 */

void __fastcall CPlayer::pc_RequestUILockUpdate(CPlayer *this, char *uszUILockPWOld, char *usz<PERSON>LockPW, char *usz<PERSON><PERSON>ockPW_Confirm, char byUILock_HintIndex, char *uszUILock_HintAnswer)
{
  CPlayer::pc_RequestUILockUpdate(
    this,
    uszUILockPWOld,
    uszUILockPW,
    uszUILockPW_Confirm,
    byUILock_HintIndex,
    uszUILock_HintAnswer);
}
