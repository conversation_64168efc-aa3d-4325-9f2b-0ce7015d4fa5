/*
 * Function: ?id_characteristic_two_basis@ASN1@CryptoPP@@YA?AVOID@2@XZ
 * Address: 0x14062C760
 */

struct CryptoPP::OID *__fastcall CryptoPP::ASN1::id_characteristic_two_basis(CryptoPP::ASN1 *this, struct CryptoPP::OID *retstr)
{
  const struct CryptoPP::OID *v2; // rax@1
  CryptoPP::OID v4; // [sp+20h] [bp-58h]@1
  int v5; // [sp+48h] [bp-30h]@1
  __int64 v6; // [sp+50h] [bp-28h]@1
  const struct CryptoPP::OID *v7; // [sp+58h] [bp-20h]@1
  const struct CryptoPP::OID *v8; // [sp+60h] [bp-18h]@1
  CryptoPP::OID *v9; // [sp+80h] [bp+8h]@1

  v9 = (CryptoPP::OID *)this;
  v6 = -2i64;
  v5 = 0;
  v2 = CryptoPP::ASN1::characteristic_two_field((CryptoPP::ASN1 *)&v4, retstr);
  v7 = v2;
  v8 = v2;
  CryptoPP::operator+(v9, v2, 3u);
  v5 |= 1u;
  CryptoPP::OID::~OID(&v4);
  return v9;
}
