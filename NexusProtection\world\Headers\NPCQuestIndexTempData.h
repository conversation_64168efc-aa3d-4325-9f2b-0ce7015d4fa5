#pragma once

/**
 * @file NPCQuestIndexTempData.h
 * @brief Temporary data structure for NPC quest indexing in NexusProtection
 * @details Provides temporary storage and indexing for NPC quest data during processing
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include <cstdint>
#include <array>
#include <memory>
#include <vector>
#include <string>

namespace NexusProtection {
namespace World {

/**
 * @struct NPCQuestInfo
 * @brief Information about an NPC quest
 */
struct NPCQuestInfo {
    uint32_t questId;           ///< Unique quest identifier
    uint32_t npcId;             ///< NPC associated with this quest
    uint32_t questType;         ///< Type of quest (kill, collect, etc.)
    uint32_t questStatus;       ///< Current status of the quest
    uint32_t requiredLevel;     ///< Minimum level required for quest
    uint32_t rewardExp;         ///< Experience reward
    uint32_t rewardGold;        ///< Gold reward
    std::string questName;      ///< Name of the quest
    std::string description;    ///< Quest description
    
    NPCQuestInfo() 
        : questId(0), npcId(0), questType(0), questStatus(0)
        , requiredLevel(0), rewardExp(0), rewardGold(0) {}
};

/**
 * @enum QuestStatus
 * @brief Status values for quests
 */
enum class QuestStatus : uint32_t {
    NotStarted = 0,     ///< Quest not yet started
    InProgress = 1,     ///< Quest is currently active
    Completed = 2,      ///< Quest has been completed
    Failed = 3,         ///< Quest has failed
    Abandoned = 4,      ///< Quest was abandoned by player
    Repeatable = 5      ///< Quest can be repeated
};

/**
 * @enum QuestType
 * @brief Types of quests available
 */
enum class QuestType : uint32_t {
    Kill = 0,           ///< Kill specific monsters
    Collect = 1,        ///< Collect specific items
    Deliver = 2,        ///< Deliver items to NPCs
    Escort = 3,         ///< Escort NPCs safely
    Explore = 4,        ///< Explore specific areas
    Talk = 5,           ///< Talk to specific NPCs
    Craft = 6,          ///< Craft specific items
    Custom = 99         ///< Custom quest type
};

/**
 * @class NPCQuestIndexTempData
 * @brief Temporary data structure for indexing and managing NPC quest information
 * 
 * This class provides temporary storage for NPC quest data during processing operations.
 * It maintains indices and temporary data structures to efficiently manage quest information
 * for NPCs in the game world.
 * 
 * The class is designed to be lightweight and temporary, used during quest processing
 * operations and then discarded.
 */
class NPCQuestIndexTempData {
public:
    /**
     * @brief Default constructor
     * Initializes the temporary data structure with default values
     */
    NPCQuestIndexTempData();

    /**
     * @brief Destructor
     * Cleans up any allocated resources
     */
    ~NPCQuestIndexTempData();

    /**
     * @brief Copy constructor (deleted)
     * Temporary data should not be copied
     */
    NPCQuestIndexTempData(const NPCQuestIndexTempData&) = delete;

    /**
     * @brief Copy assignment operator (deleted)
     * Temporary data should not be copied
     */
    NPCQuestIndexTempData& operator=(const NPCQuestIndexTempData&) = delete;

    /**
     * @brief Move constructor
     * @param other The NPCQuestIndexTempData to move from
     */
    NPCQuestIndexTempData(NPCQuestIndexTempData&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The NPCQuestIndexTempData to move from
     * @return Reference to this object
     */
    NPCQuestIndexTempData& operator=(NPCQuestIndexTempData&& other) noexcept;

    /**
     * @brief Initialize the temporary data structure
     * Sets all values to their default state
     */
    void Init();

    /**
     * @brief Clear all temporary data
     * Resets the structure to an empty state
     */
    void Clear();

    /**
     * @brief Add a quest to the temporary index
     * @param questInfo Quest information to add
     * @return true if successfully added, false otherwise
     */
    bool AddQuest(const NPCQuestInfo& questInfo);

    /**
     * @brief Remove a quest from the temporary index
     * @param questId ID of the quest to remove
     * @return true if successfully removed, false if not found
     */
    bool RemoveQuest(uint32_t questId);

    /**
     * @brief Find a quest by ID
     * @param questId ID of the quest to find
     * @return Pointer to quest info if found, nullptr otherwise
     */
    const NPCQuestInfo* FindQuest(uint32_t questId) const;

    /**
     * @brief Get all quests for a specific NPC
     * @param npcId ID of the NPC
     * @return Vector of quest pointers for the NPC
     */
    std::vector<const NPCQuestInfo*> GetQuestsForNPC(uint32_t npcId) const;

    /**
     * @brief Get quests by status
     * @param status Quest status to filter by
     * @return Vector of quest pointers with the specified status
     */
    std::vector<const NPCQuestInfo*> GetQuestsByStatus(QuestStatus status) const;

    /**
     * @brief Get quests by type
     * @param type Quest type to filter by
     * @return Vector of quest pointers with the specified type
     */
    std::vector<const NPCQuestInfo*> GetQuestsByType(QuestType type) const;

    /**
     * @brief Update quest status
     * @param questId ID of the quest to update
     * @param newStatus New status for the quest
     * @return true if successfully updated, false if quest not found
     */
    bool UpdateQuestStatus(uint32_t questId, QuestStatus newStatus);

    /**
     * @brief Get the number of quests in the temporary data
     * @return Number of quests currently stored
     */
    [[nodiscard]] std::size_t GetQuestCount() const noexcept;

    /**
     * @brief Check if the temporary data is empty
     * @return true if no quests are stored, false otherwise
     */
    [[nodiscard]] bool IsEmpty() const noexcept;

    /**
     * @brief Get the maximum number of quests that can be stored
     * @return Maximum quest capacity
     */
    [[nodiscard]] static constexpr std::size_t GetMaxCapacity() noexcept {
        return 1000; // Reasonable limit for temporary data
    }

    /**
     * @brief Validate the internal data structure
     * @return true if all data is consistent, false otherwise
     */
    [[nodiscard]] bool Validate() const;

    /**
     * @brief Get all quest IDs currently stored
     * @return Vector of all quest IDs
     */
    std::vector<uint32_t> GetAllQuestIds() const;

    /**
     * @brief Get statistics about the temporary data
     * @return Structure containing various statistics
     */
    struct Statistics {
        std::size_t totalQuests;
        std::size_t questsByStatus[6];  // One for each QuestStatus value
        std::size_t questsByType[8];    // One for each QuestType value
        std::size_t uniqueNPCs;
    };

    /**
     * @brief Get comprehensive statistics
     * @return Statistics structure with current data
     */
    Statistics GetStatistics() const;

    /**
     * @brief Sort quests by a specific criteria
     * @param sortByLevel If true, sort by required level; otherwise by quest ID
     */
    void SortQuests(bool sortByLevel = false);

    /**
     * @brief Export quest data to a vector
     * @return Vector containing all quest information
     */
    std::vector<NPCQuestInfo> ExportQuests() const;

    /**
     * @brief Import quest data from a vector
     * @param quests Vector of quest information to import
     * @return Number of quests successfully imported
     */
    std::size_t ImportQuests(const std::vector<NPCQuestInfo>& quests);

private:
    /// Vector storing quest information
    std::vector<NPCQuestInfo> m_quests;
    
    /// Index mapping quest ID to vector position for fast lookup
    std::vector<std::pair<uint32_t, std::size_t>> m_questIdIndex;
    
    /// Index mapping NPC ID to quest positions
    std::vector<std::pair<uint32_t, std::vector<std::size_t>>> m_npcIndex;
    
    /// Flag indicating if indices need rebuilding
    mutable bool m_indicesNeedRebuild;

    /**
     * @brief Rebuild internal indices for fast lookup
     * Called automatically when needed
     */
    void RebuildIndices() const;

    /**
     * @brief Find quest position in vector by ID
     * @param questId ID of the quest to find
     * @return Index in vector if found, SIZE_MAX otherwise
     */
    std::size_t FindQuestIndex(uint32_t questId) const;

    /**
     * @brief Mark indices as needing rebuild
     */
    void InvalidateIndices();

    /**
     * @brief Validate a quest info structure
     * @param questInfo Quest information to validate
     * @return true if valid, false otherwise
     */
    static bool ValidateQuestInfo(const NPCQuestInfo& questInfo);

    /**
     * @brief Get the size of the temporary data structure in bytes
     * @return Approximate memory usage
     */
    [[nodiscard]] std::size_t GetMemoryUsage() const;
};

} // namespace World
} // namespace NexusProtection
