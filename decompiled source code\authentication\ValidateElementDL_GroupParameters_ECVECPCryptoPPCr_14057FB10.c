/*
 * Function: ?ValidateElement@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEBA_NIAEBUECPPoint@2@PEBV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@@Z
 * Address: 0x14057FB10
 */

char __fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::ValidateElement(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *a1, unsigned int a2, struct CryptoPP::ECPPoint *a3, __int64 *a4)
{
  CryptoPP::ECP *v4; // rax@2
  __int64 v5; // rax@8
  __int64 v6; // rax@8
  __int64 v7; // rax@15
  __int64 v8; // rax@16
  CryptoPP::ECPPoint *v9; // rax@16
  CryptoPP::ECPPoint *v10; // rax@17
  char v12; // [sp+20h] [bp-218h]@5
  CryptoPP::ECPPoint v13; // [sp+30h] [bp-208h]@18
  __int64 v14; // [sp+90h] [bp-1A8h]@15
  CryptoPP::ECPPoint v15; // [sp+98h] [bp-1A0h]@8
  CryptoPP::ECPPoint *__that; // [sp+F0h] [bp-148h]@18
  CryptoPP::ECPPoint v17; // [sp+F8h] [bp-140h]@16
  CryptoPP::ECPPoint v18; // [sp+150h] [bp-E8h]@17
  int v19; // [sp+1A8h] [bp-90h]@1
  __int64 v20; // [sp+1B0h] [bp-88h]@1
  int v21; // [sp+1B8h] [bp-80h]@3
  const struct CryptoPP::Integer *v22; // [sp+1C0h] [bp-78h]@8
  CryptoPP::GeneratableCryptoMaterialVtbl *v23; // [sp+1C8h] [bp-70h]@8
  __int64 v24; // [sp+1D0h] [bp-68h]@8
  __int64 v25; // [sp+1D8h] [bp-60h]@8
  __int64 v26; // [sp+1E0h] [bp-58h]@8
  int v27; // [sp+1E8h] [bp-50h]@9
  __int64 v28; // [sp+1F0h] [bp-48h]@16
  CryptoPP::ECPPoint *v29; // [sp+1F8h] [bp-40h]@16
  CryptoPP::ECPPoint *v30; // [sp+200h] [bp-38h]@16
  CryptoPP::ECPPoint *v31; // [sp+208h] [bp-30h]@16
  CryptoPP::ECPPoint *v32; // [sp+210h] [bp-28h]@17
  CryptoPP::ECPPoint *v33; // [sp+218h] [bp-20h]@17
  int v34; // [sp+220h] [bp-18h]@24
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *v35; // [sp+240h] [bp+8h]@1
  unsigned int v36; // [sp+248h] [bp+10h]@1
  struct CryptoPP::ECPPoint *v37; // [sp+250h] [bp+18h]@1
  __int64 *v38; // [sp+258h] [bp+20h]@1

  v38 = a4;
  v37 = a3;
  v36 = a2;
  v35 = a1;
  v20 = -2i64;
  v19 = 0;
  v21 = !(unsigned __int8)((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *, struct CryptoPP::ECPPoint *))a1->vfptr[19].__vecDelDtor)(
                            a1,
                            a3)
     && (v4 = CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetCurve(v35), CryptoPP::ECP::VerifyPoint(v4, v37));
  v12 = v21;
  if ( v36 >= 1 && v38 )
  {
    v27 = (_BYTE)v21
       && (v22 = CryptoPP::Integer::One(),
           v23 = v35->vfptr,
           LODWORD(v5) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *))v23[5].__vecDelDtor)(v35),
           v24 = *v38,
           LODWORD(v6) = (*(int (__fastcall **)(__int64 *, CryptoPP::ECPPoint *, __int64, const struct CryptoPP::Integer *))(v24 + 48))(
                           v38,
                           &v15,
                           v5,
                           v22),
           v25 = v6,
           v26 = v6,
           v19 |= 1u,
           CryptoPP::ECPPoint::operator==(v6, (__int64)v37));
    v12 = v27;
    if ( v19 & 1 )
    {
      v19 &= 0xFFFFFFFE;
      CryptoPP::ECPPoint::~ECPPoint(&v15);
    }
  }
  if ( v36 >= 2 && v12 )
  {
    LODWORD(v7) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *))v35->vfptr[8].__vecDelDtor)(v35);
    v14 = v7;
    if ( v38 )
    {
      LODWORD(v8) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *))v35->vfptr[5].__vecDelDtor)(v35);
      v28 = *v38;
      LODWORD(v9) = (*(int (__fastcall **)(__int64 *, CryptoPP::ECPPoint *, __int64, __int64))(v28 + 48))(
                      v38,
                      &v17,
                      v8,
                      v14);
      v29 = v9;
      v30 = v9;
      v19 |= 2u;
      v31 = v9;
    }
    else
    {
      LODWORD(v10) = ((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *, CryptoPP::ECPPoint *, struct CryptoPP::ECPPoint *, __int64))v35->vfptr[4].__vecDelDtor)(
                       v35,
                       &v18,
                       v37,
                       v14);
      v32 = v10;
      v33 = v10;
      v19 |= 4u;
      v31 = v10;
    }
    __that = v31;
    CryptoPP::ECPPoint::ECPPoint(&v13, v31);
    if ( v19 & 4 )
    {
      v19 &= 0xFFFFFFFB;
      CryptoPP::ECPPoint::~ECPPoint(&v18);
    }
    if ( v19 & 2 )
    {
      v19 &= 0xFFFFFFFD;
      CryptoPP::ECPPoint::~ECPPoint(&v17);
    }
    v34 = v12
       && (unsigned __int8)((int (__fastcall *)(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *, CryptoPP::ECPPoint *))v35->vfptr[19].__vecDelDtor)(
                             v35,
                             &v13);
    v12 = v34;
    CryptoPP::ECPPoint::~ECPPoint(&v13);
  }
  return v12;
}
