/*
 * Function: ?LoadPrevTable@CWeeklyGuildRankManager@@AEAA_NPEADAEAU_pvppoint_guild_rank_info@@@Z
 * Address: 0x1402CE140
 */

bool __fastcall CWeeklyGuildRankManager::LoadPrevTable(CWeeklyGuildRankManager *this, char *szDate, _pvppoint_guild_rank_info *kInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-158h]@1
  char DstBuf; // [sp+30h] [bp-128h]@4
  int v8; // [sp+140h] [bp-18h]@6
  unsigned __int64 v9; // [sp+148h] [bp-10h]@4
  char *szDatea; // [sp+168h] [bp+10h]@1
  _pvppoint_guild_rank_info *Dst; // [sp+170h] [bp+18h]@1

  Dst = kInfo;
  szDatea = szDate;
  v3 = &v6;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s(&DstBuf, 0xFFui64, "tbl_PvpPointGuildRank%s", szDate);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&pkDB->vfptr, &DstBuf) )
  {
    memset_0(Dst, 0, 0x6D70ui64);
    v8 = CRFWorldDatabase::Select_PvpPointGuildRank(pkDB, szDatea, Dst) == 0;
    result = v8;
  }
  else
  {
    result = 0;
  }
  return result;
}
