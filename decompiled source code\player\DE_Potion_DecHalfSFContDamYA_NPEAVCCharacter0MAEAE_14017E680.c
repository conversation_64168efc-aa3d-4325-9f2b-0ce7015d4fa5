/*
 * Function: ?DE_Potion_DecHalfSFContDam@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017E680
 */

bool __fastcall DE_Potion_DecHalfSFContDam(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPlayer *v8; // [sp+20h] [bp-18h]@9
  CCharacter *v9; // [sp+40h] [bp+8h]@1

  v9 = pActChar;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pTargetChar )
  {
    if ( v9->m_ObjID.m_byID || pTargetChar->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v8 = (CPlayer *)pTargetChar;
      result = CPlayer::DecHalfSFContDam((CPlayer *)pTargetChar, 0.0);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
