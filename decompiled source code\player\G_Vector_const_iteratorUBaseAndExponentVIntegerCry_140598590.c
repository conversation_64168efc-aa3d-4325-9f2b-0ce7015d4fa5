/*
 * Function: ??G?$_Vector_const_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x140598590
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator-(__int64 a1, __int64 a2)
{
  __int128 v2; // ax@1
  __int128 v4; // tt@1

  v2 = *(_QWORD *)(a1 + 16) - *(_QWORD *)(a2 + 16);
  *(_QWORD *)&v4 = v2;
  *((_QWORD *)&v4 + 1) = *((_QWORD *)&v2 + 1);
  return v4 / 80;
}
