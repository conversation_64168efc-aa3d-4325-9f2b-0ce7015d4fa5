/*
 * Function: ?IsMapIn@CMapData@@QEAA_NPEAM@Z
 * Address: 0x140184B40
 */

char __fastcall CMapData::IsMapIn(CMapData *this, float *fPos)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CMapData *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( fPos[j] > (float)v6->m_BspInfo.m_nMapMaxSize[j] || (float)v6->m_BspInfo.m_nMapMinSize[j] > fPos[j] )
      return 0;
  }
  return 1;
}
