/*
 * Function: ??0CUnmannedTraderSubClassInfoLevel@@QEAA@K@Z
 * Address: 0x140384070
 */

void __fastcall CUnmannedTraderSubClassInfoLevel::CUnmannedTraderSubClassInfoLevel(CUnmannedTraderSubClassInfoLevel *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoLevel *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CUnmannedTraderSubClassInfo::CUnmannedTraderSubClassInfo((CUnmannedTraderSubClassInfo *)&v5->vfptr, dwID);
  v5->vfptr = (CUnmannedTraderSubClassInfoVtbl *)&CUnmannedTraderSubClassInfoLevel::`vftable';
  v5->m_byMin = 0;
  v5->m_byMax = 0;
  strcpy_0(v5->m_szName, "level");
}
