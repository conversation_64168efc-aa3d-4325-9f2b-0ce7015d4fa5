/*
 * Function: ?pc_AwayPartyJoinInvitationAnswer@CPlayer@@QEAAXPEAU_CLID@@E@Z
 * Address: 0x1400C4130
 */

void __usercall CPlayer::pc_AwayPartyJoinInvitationAnswer(CPlayer *this@<rcx>, _CLID *pidBoss@<rdx>, char byRetCode@<r8b>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@9
  int v7; // eax@22
  CMoneySupplyMgr *v8; // rax@43
  __int64 v9; // [sp+0h] [bp-68h]@1
  bool bUpdate; // [sp+20h] [bp-48h]@35
  bool bSend; // [sp+28h] [bp-40h]@35
  char v12; // [sp+30h] [bp-38h]@4
  CPlayer *v13; // [sp+38h] [bp-30h]@5
  _STORAGE_LIST::_db_con *pItem; // [sp+40h] [bp-28h]@24
  unsigned __int16 v15; // [sp+48h] [bp-20h]@35
  int v16; // [sp+4Ch] [bp-1Ch]@39
  int v17; // [sp+50h] [bp-18h]@9
  float v18; // [sp+54h] [bp-14h]@22
  int nLv; // [sp+58h] [bp-10h]@43
  int v20; // [sp+5Ch] [bp-Ch]@43
  CPlayer *v21; // [sp+70h] [bp+8h]@1
  _CLID *v22; // [sp+78h] [bp+10h]@1
  char v23; // [sp+80h] [bp+18h]@1

  v23 = byRetCode;
  v22 = pidBoss;
  v21 = this;
  v4 = &v9;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = 0;
  if ( !CPlayer::IsPunished(v21, 2, 1) )
  {
    v13 = &g_Player + v22->wIndex;
    if ( v13->m_dwObjSerial == v22->dwSerial )
    {
      if ( v13->m_bLive )
      {
        if ( v13->m_bOper )
        {
          v17 = CPlayerDB::GetRaceCode(&v21->m_Param);
          v6 = CPlayerDB::GetRaceCode(&v13->m_Param);
          if ( v17 == v6 && !CPlayer::IsPunished(v13, 2, 0) )
          {
            if ( v13->m_byUserDgr )
            {
              if ( !v21->m_byUserDgr )
                return;
            }
            else if ( v21->m_byUserDgr )
            {
              return;
            }
            if ( v13->m_pPartyMgr->m_bLock )
            {
              v12 = 7;
              CPlayer::SendMsg_AwayPartyRequestResult(v21, 7);
              CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
            }
            else if ( !CPartyPlayer::IsPartyMode(v13->m_pPartyMgr) || CPartyPlayer::IsPartyBoss(v13->m_pPartyMgr) )
            {
              if ( CPartyPlayer::IsPartyMode(v21->m_pPartyMgr) )
              {
                v12 = 4;
                CPlayer::SendMsg_AwayPartyRequestResult(v21, 4);
                CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
              }
              else
              {
                _effect_parameter::GetEff_Have(&v21->m_EP, 53);
                v18 = a4;
                v7 = ((int (__fastcall *)(CPlayer *))v21->vfptr->GetLevel)(v21);
                if ( CPartyPlayer::IsJoinPartyLevel(v13->m_pPartyMgr, v7, v18) )
                {
                  pItem = 0i64;
                  if ( !unk_1799C5FFE
                    || (pItem = _STORAGE_LIST::GetPtrFromItemCode(
                                  (_STORAGE_LIST *)&v21->m_Param.m_dbInven.m_nListNum,
                                  byte_1799C5FFF)) != 0i64 )
                  {
                    if ( unk_1799C603F && nAmount > CPlayer::GetMoney(v21, 0) )
                    {
                      v12 = 11;
                      CPlayer::SendMsg_AwayPartyRequestResult(v21, 11);
                      CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
                    }
                    else if ( v23 )
                    {
                      v12 = 10;
                      CPlayer::SendMsg_AwayPartyRequestResult(v13, 10);
                    }
                    else
                    {
                      wa_PartyJoin(&v13->m_id, &v21->m_id);
                      if ( CPartyPlayer::IsPartyMode(v21->m_pPartyMgr) )
                      {
                        if ( unk_1799C5FFE && pItem )
                        {
                          bSend = 1;
                          bUpdate = 0;
                          v15 = CPlayer::Emb_AlterDurPoint(v21, 0, pItem->m_byStorageIndex, -1, 0, 1);
                          if ( v15 )
                            CPlayer::SendMsg_AdjustAmountInform(v21, 0, pItem->m_wSerial, v15);
                          else
                            CMgrAvatorItemHistory::consume_del_item(
                              &CPlayer::s_MgrItemHistory,
                              v21->m_ObjID.m_wIndex,
                              pItem,
                              v21->m_szItemHistoryFileName);
                        }
                        if ( unk_1799C603F )
                        {
                          CPlayer::SubDalant(v21, nAmount);
                          CPlayer::SendMsg_AlterMoneyInform(v21, 0);
                          v16 = CPlayerDB::GetLevel(&v21->m_Param);
                          if ( v16 == 30 || v16 == 40 || v16 == 50 || v16 == 60 )
                          {
                            nLv = CPlayerDB::GetLevel(&v21->m_Param);
                            v20 = CPlayerDB::GetRaceCode(&v21->m_Param);
                            v8 = CMoneySupplyMgr::Instance();
                            CMoneySupplyMgr::UpdateFeeMoneyData(v8, v20, nLv, nAmount);
                          }
                        }
                      }
                      else
                      {
                        v12 = 7;
                        CPlayer::SendMsg_AwayPartyRequestResult(v21, 7);
                        CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
                      }
                    }
                  }
                  else
                  {
                    v12 = 9;
                    CPlayer::SendMsg_AwayPartyRequestResult(v21, 9);
                    CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
                  }
                }
                else
                {
                  v12 = 5;
                  CPlayer::SendMsg_AwayPartyRequestResult(v21, 5);
                  CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
                }
              }
            }
            else
            {
              v12 = 8;
              CPlayer::SendMsg_AwayPartyRequestResult(v21, 8);
              CPlayer::SendMsg_AwayPartyRequestResult(v13, v12);
            }
          }
        }
      }
    }
  }
}
