/*
 * Function: ??$_Destroy_range@PEAUMessageRange@MeterFilter@CryptoPP@@V?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@0AEAV?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@0@@Z
 * Address: 0x140601600
 */

int __fastcall std::_Destroy_range<CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(__int64 a1, __int64 a2, __int64 a3)
{
  unsigned __int8 v3; // ST20_1@1
  __int64 v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1

  v7 = a3;
  v6 = a2;
  v5 = a1;
  v3 = std::_Ptr_cat<CryptoPP::MeterFilter::MessageRange * *,CryptoPP::MeterFilter::MessageRange * *>(&v5, &v6);
  return std::_Destroy_range<CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>(
           v5,
           v6,
           v7,
           v3);
}
