/*
 * Function: ?BERDecodeElement@ModExpPrecomputation@CryptoPP@@UEBA?A<PERSON><PERSON><PERSON>@2@AEAVBufferedTransformation@2@@Z
 * Address: 0x14055F210
 */

struct CryptoPP::Integer *__fastcall CryptoPP::ModExpPrecomputation::BERDecodeElement(CryptoPP::ModExpPrecomputation *this, struct CryptoPP::Integer *retstr, struct CryptoPP::BufferedTransformation *a3)
{
  struct CryptoPP::Integer *v4; // [sp+48h] [bp+10h]@1

  v4 = retstr;
  CryptoPP::Integer::Integer(retstr, a3);
  return v4;
}
