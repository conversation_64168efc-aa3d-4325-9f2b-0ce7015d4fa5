/*
 * Function: j_?DigestSize@?$IteratedHashWithStaticTransform@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@$0BE@VSHA1@2@$0A@@CryptoPP@@UEBAIXZ
 * Address: 0x14000CC93
 */

unsigned int __fastcall CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>::DigestSize(CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0> *this)
{
  return CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::Byte<PERSON><PERSON>r,1>,64,20,CryptoPP::SHA1,0>::DigestSize(this);
}
