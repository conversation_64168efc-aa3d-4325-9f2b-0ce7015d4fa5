/*
 * Function: ?Init@CRusiaBillingMgr@@QEAAHXZ
 * Address: 0x140320E40
 */

signed __int64 __fastcall CRusiaBillingMgr::Init(CRusiaBillingMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRusiaBillingMgr *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CoInitialize(0i64);
  if ( RFACC_Init() )
  {
    CoUninitialize();
    result = 1i64;
  }
  else
  {
    CLogFile::Write(&v5->m_logBill, "CRusiaBillingMgr::RFACC_Init() Fail");
    CoUninitialize();
    result = 0i64;
  }
  return result;
}
