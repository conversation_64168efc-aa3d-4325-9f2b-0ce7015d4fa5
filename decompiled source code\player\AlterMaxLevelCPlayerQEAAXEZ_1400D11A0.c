/*
 * Function: ?AlterMaxLevel@CPlayer@@QEAAXE@Z
 * Address: 0x1400D11A0
 */

void __fastcall CPlayer::AlterMaxLevel(CPlayer *this, char byMaxLevel)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  int v5; // eax@5
  cStaticMember_Player *v6; // rax@6
  int v7; // eax@6
  __int64 v8; // [sp+0h] [bp-48h]@1
  int v9; // [sp+20h] [bp-28h]@4
  CGameObjectVtbl *v10; // [sp+28h] [bp-20h]@4
  int v11; // [sp+30h] [bp-18h]@5
  int v12; // [sp+34h] [bp-14h]@6
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  char v14; // [sp+58h] [bp+10h]@1

  v14 = byMaxLevel;
  v13 = this;
  v2 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int8)byMaxLevel;
  v10 = v13->vfptr;
  v4 = ((int (__fastcall *)(CPlayer *))v10->GetLevel)(v13);
  if ( v9 > v4 )
  {
    v11 = (unsigned __int8)v14;
    v5 = CPlayerDB::GetMaxLevel(&v13->m_Param);
    if ( v11 > v5 )
    {
      v12 = (unsigned __int8)v14;
      v6 = cStaticMember_Player::Instance();
      v7 = cStaticMember_Player::GetMaxLv(v6);
      if ( v12 <= v7 )
      {
        CPlayerDB::SetMaxLevel(&v13->m_Param, (unsigned __int8)v14);
        if ( v13->m_pUserDB )
          CUserDB::Update_MaxLevel(v13->m_pUserDB, v14);
      }
    }
  }
}
