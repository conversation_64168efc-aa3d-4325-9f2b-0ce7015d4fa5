/*
 * Function: j_??0?$_Vector_val@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@1@@Z
 * Address: 0x140010A64
 */

void __fastcall std::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(std::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> _Al)
{
  std::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::_Vector_val<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    this,
    _Al);
}
