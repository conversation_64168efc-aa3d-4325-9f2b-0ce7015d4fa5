/*
 * Function: j_??1?$_Bidit@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x14000936D
 */

void __fastcall std::_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>::~_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>(std::_Bidit<std::pair<int const ,_TimeItem_fld const *>,__int64,std::pair<int const ,_TimeItem_fld const *> const *,std::pair<int const ,_TimeItem_fld const *> const &> *this)
{
  std::_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>::~_Bidit<std::pair<int const,_TimeItem_fld const *>,__int64,std::pair<int const,_TimeItem_fld const *> const *,std::pair<int const,_TimeItem_fld const *> const &>(this);
}
