/*
 * Function: j_??Y?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x14000B0DC
 */

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator+=(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, __int64 _Off)
{
  return std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator+=(this, _Off);
}
