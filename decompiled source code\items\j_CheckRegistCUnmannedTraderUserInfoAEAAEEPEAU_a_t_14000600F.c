/*
 * Function: j_?CheckRegist@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_reg_item_request_clzo@@PEAVCLogFile@@AEAE222AEAK3@Z
 * Address: 0x14000600F
 */

char __fastcall CUnmannedTraderUserInfo::CheckRegist(CUnmannedTraderUserInfo *this, char byType, _a_trade_reg_item_request_clzo *pRequest, CLogFile *pkLogger, char *byTempSlotIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex, unsigned int *dwTax)
{
  return CUnmannedTraderUserInfo::CheckRegist(
           this,
           byType,
           pRequest,
           pkLogger,
           byTempSlotIndex,
           byDivision,
           byClass,
           bySubClass,
           dwListIndex,
           dwTax);
}
