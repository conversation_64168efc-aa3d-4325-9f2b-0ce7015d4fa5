/*
 * Function: ??$_Destroy_range@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@@Z
 * Address: 0x1403A3310
 */

void __fastcall std::_Destroy_range<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, std::allocator<CMoveMapLimitInfo *> *_Al)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  std::_Scalar_ptr_iterator_tag v6; // [sp+20h] [bp-18h]@4
  CMoveMapLimitInfo **__formal; // [sp+40h] [bp+8h]@1
  CMoveMapLimitInfo **_Lasta; // [sp+48h] [bp+10h]@1
  std::allocator<CMoveMapLimitInfo *> *_Ala; // [sp+50h] [bp+18h]@1

  _Ala = _Al;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = std::_Ptr_cat<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(&__formal, &_Lasta);
  std::_Destroy_range<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(__formal, _Lasta, _Ala, v6);
}
