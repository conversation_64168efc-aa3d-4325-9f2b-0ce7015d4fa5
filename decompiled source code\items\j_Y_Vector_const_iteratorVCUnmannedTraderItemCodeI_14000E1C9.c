/*
 * Function: j_??Y?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@QEAAAEAV01@_J@Z
 * Address: 0x14000E1C9
 */

std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__fastcall std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator+=(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, __int64 _Off)
{
  return std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator+=(
           this,
           _Off);
}
