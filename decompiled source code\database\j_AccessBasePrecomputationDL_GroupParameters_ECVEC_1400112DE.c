/*
 * Function: j_?AccessBasePrecomputation@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x1400112DE
 */

CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::AccessBasePrecomputation(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *this)
{
  return CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::AccessBasePrecomputation(this);
}
