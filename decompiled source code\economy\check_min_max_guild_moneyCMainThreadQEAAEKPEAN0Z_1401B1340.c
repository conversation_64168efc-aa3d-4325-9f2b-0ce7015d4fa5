/*
 * Function: ?check_min_max_guild_money@CMainThread@@QEAAEKPEAN0@Z
 * Address: 0x1401B1340
 */

char __fastcall CMainThread::check_min_max_guild_money(CMainThread *this, unsigned int dwGuildSerial, long double *pdDalant, long double *pdGold)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-1E8h]@1
  _worlddb_guild_info::__guild_info pGuildData; // [sp+30h] [bp-1B8h]@4
  char v9; // [sp+184h] [bp-64h]@6
  CCheckSumGuildData v10; // [sp+198h] [bp-50h]@23
  char v11; // [sp+1C0h] [bp-28h]@24
  __int64 v12; // [sp+1C8h] [bp-20h]@4
  unsigned __int64 v13; // [sp+1D0h] [bp-18h]@4
  CMainThread *v14; // [sp+1F0h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+1F8h] [bp+10h]@1
  long double *v16; // [sp+200h] [bp+18h]@1
  long double *v17; // [sp+208h] [bp+20h]@1

  v17 = pdGold;
  v16 = pdDalant;
  dwGuildSeriala = dwGuildSerial;
  v14 = this;
  v4 = &v7;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = -2i64;
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  pGuildData.dwGuildSerial = 0;
  memset(&pGuildData.byGuildGrade, 0, 0x144ui64);
  if ( !CRFWorldDatabase::Select_GuildData(v14->m_pWorldDB, dwGuildSerial, &pGuildData) )
    return 24;
  v9 = 0;
  if ( pGuildData.dDalant >= 0.0 || pGuildData.dGold >= 0.0 )
  {
    if ( pGuildData.dDalant >= 0.0 )
    {
      if ( pGuildData.dGold < 0.0 )
      {
        *(_QWORD *)&pGuildData.dGold = 0i64;
        v9 = 1;
      }
    }
    else
    {
      *(_QWORD *)&pGuildData.dDalant = 0i64;
      v9 = 1;
    }
  }
  else
  {
    *(_QWORD *)&pGuildData.dDalant = 0i64;
    *(_QWORD *)&pGuildData.dGold = 0i64;
    v9 = 1;
  }
  if ( pGuildData.dDalant <= 1000000000.0 || pGuildData.dGold <= 500000.0 )
  {
    if ( pGuildData.dDalant <= 1000000000.0 )
    {
      if ( pGuildData.dGold > 500000.0 )
      {
        pGuildData.dGold = DOUBLE_500000_0;
        v9 = 1;
      }
    }
    else
    {
      pGuildData.dDalant = DOUBLE_1_0e9;
      v9 = 1;
    }
  }
  else
  {
    pGuildData.dDalant = DOUBLE_1_0e9;
    pGuildData.dGold = DOUBLE_500000_0;
    v9 = 1;
  }
  if ( v9 )
  {
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v14->m_pWorldDB->vfptr, 0);
    if ( !CRFWorldDatabase::Update_SetGuildMoney(v14->m_pWorldDB, dwGuildSeriala, pGuildData.dDalant, pGuildData.dGold) )
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v14->m_pWorldDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v14->m_pWorldDB->vfptr, 1);
      return 24;
    }
    CCheckSumGuildData::CCheckSumGuildData(&v10, dwGuildSeriala);
    CCheckSumGuildData::Encode(&v10, pGuildData.dDalant, 0.0);
    if ( !CCheckSumGuildData::Update(&v10, v14->m_pWorldDB) )
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v14->m_pWorldDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v14->m_pWorldDB->vfptr, 1);
      v11 = 24;
      CCheckSumGuildData::~CCheckSumGuildData(&v10);
      return v11;
    }
    CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v14->m_pWorldDB->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v14->m_pWorldDB->vfptr, 1);
    CCheckSumGuildData::~CCheckSumGuildData(&v10);
  }
  if ( v16 )
    *v16 = pGuildData.dDalant;
  if ( v17 )
    *v17 = pGuildData.dGold;
  return 0;
}
