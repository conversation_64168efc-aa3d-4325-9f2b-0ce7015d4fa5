/*
 * Function: j_??$_Uninit_copy@PEAPEAUScheduleMSG@@PEAPEAU1@V?$allocator@PEAUScheduleMSG@@@std@@@std@@YAPEAPEAUScheduleMSG@@PEAPEAU1@00AEAV?$allocator@PEAUSchedule<PERSON><PERSON>@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000B096
 */

ScheduleMSG **__fastcall std::_Uninit_copy<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>>(ScheduleMSG **_First, ScheduleMSG **_Last, ScheduleMSG **_Dest, std::allocator<ScheduleMSG *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<ScheduleMSG * *,ScheduleMSG * *,std::allocator<ScheduleMSG *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
