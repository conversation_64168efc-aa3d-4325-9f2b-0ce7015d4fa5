/*
 * Function: ?Change_Conditional_Event_Status@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FBF00
 */

void __fastcall CashItemRemoteStore::Change_Conditional_Event_Status(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@4
  CashItemRemoteStore *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _time32(&Time);
  if ( !v5->m_con_event.m_ini.m_bUseConEvent )
    CashItemRemoteStore::Set_Conditional_Evnet_Status(v5, 0);
  if ( v5->m_con_event.m_eventtime.m_EventTime[1] >= Time )
  {
    if ( v5->m_con_event.m_eventtime.m_EventTime[0] > Time || v5->m_con_event.m_eventtime.m_EventTime[1] < Time )
    {
      if ( v5->m_con_event.m_eventtime.m_EventTime[0] > Time )
        CashItemRemoteStore::Set_Conditional_Evnet_Status(v5, 1);
    }
    else if ( v5->m_con_event.m_eventtime.m_EventTime[1] > Time )
    {
      if ( Time - v5->m_con_event.m_eventtime.m_EventTime[0] <= 5 || v5->m_con_event.m_eventtime.m_EventTime[1] <= Time )
      {
        if ( Time - v5->m_con_event.m_eventtime.m_EventTime[0] <= 5 )
          CashItemRemoteStore::Set_Conditional_Evnet_Status(v5, 2);
      }
      else
      {
        CashItemRemoteStore::Set_Conditional_Evnet_Status(v5, 3);
      }
    }
    else
    {
      CashItemRemoteStore::Set_Conditional_Evnet_Status(v5, 4);
      v5->m_con_event.m_bConEvent = 0;
    }
  }
  else
  {
    CashItemRemoteStore::Set_Conditional_Evnet_Status(v5, 0);
  }
}
