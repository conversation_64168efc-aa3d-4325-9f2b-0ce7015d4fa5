/*
 * Function: ?Select_GodenBoxItem@CRFWorldDatabase@@QEAAHPEAU_worlddb_golden_box_item@@PEAH@Z
 * Address: 0x1404C9160
 */

signed __int64 __fastcall CRFWorldDatabase::Select_GodenBoxItem(CRFWorldDatabase *this, _worlddb_golden_box_item *goldenboxitem, int *pnSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  CGoldenBoxItemMgr *v6; // rax@23
  char v7; // al@23
  CGoldenBoxItemMgr *v8; // rax@26
  char v9; // al@26
  __int64 v10; // [sp+0h] [bp-4B8h]@1
  void *SQLStmt; // [sp+20h] [bp-498h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-490h]@22
  char _Dest[1024]; // [sp+40h] [bp-478h]@4
  SQLLEN v14; // [sp+458h] [bp-60h]@22
  __int16 v15; // [sp+464h] [bp-54h]@9
  unsigned __int8 v16; // [sp+468h] [bp-50h]@16
  int v17; // [sp+46Ch] [bp-4Ch]@22
  int j; // [sp+470h] [bp-48h]@22
  int v19; // [sp+484h] [bp-34h]@24
  int k; // [sp+494h] [bp-24h]@25
  int l; // [sp+498h] [bp-20h]@27
  unsigned __int64 v22; // [sp+4A8h] [bp-10h]@4
  CRFWorldDatabase *v23; // [sp+4C0h] [bp+8h]@1
  _worlddb_golden_box_item *v24; // [sp+4C8h] [bp+10h]@1
  int *TargetValue; // [sp+4D0h] [bp+18h]@1

  TargetValue = pnSerial;
  v24 = goldenboxitem;
  v23 = this;
  v3 = &v10;
  for ( i = 300i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = (unsigned __int64)&v10 ^ _security_cookie;
  sprintf_s<1024>((char (*)[1024])_Dest, "{ CALL pSelect_GoldBoxItem }");
  if ( v23->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, _Dest);
  if ( v23->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
  {
    v15 = SQLExecDirect_0(v23->m_hStmtSelect, _Dest, -3);
    if ( v15 && v15 != 1 )
    {
      if ( v15 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v23->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v15, _Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v15, v23->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v15 = SQLFetch_0(v23->m_hStmtSelect);
      if ( v15 && v15 != 1 )
      {
        v16 = 0;
        if ( v15 == 100 )
        {
          v16 = 2;
        }
        else
        {
          SQLStmt = v23->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v15, _Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v15, v23->m_hStmtSelect);
          v16 = 1;
        }
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        result = v16;
      }
      else
      {
        v17 = 1;
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v23->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v14);
        ++v17;
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v23->m_hStmtSelect, v17++, -6, &v24->bydck, 0i64, &v14);
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v23->m_hStmtSelect, v17, 4, &v24->dwStarterBoxCnt, 0i64, &v14);
        for ( j = 0; ; ++j )
        {
          v6 = CGoldenBoxItemMgr::Instance();
          v7 = CGoldenBoxItemMgr::GetLoopCount(v6);
          if ( j >= (unsigned __int8)v7 )
            break;
          v19 = -1;
          ++v17;
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v23->m_hStmtSelect, v17++, 4, &v24->nBox_item_code[j], 0i64, &v14);
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v23->m_hStmtSelect, v17, 4, &v19, 0i64, &v14);
          v24->wBox_item_max[j] = v19;
          ++v17;
          StrLen_or_IndPtr = &v14;
          SQLStmt = 0i64;
          v15 = SQLGetData_0(v23->m_hStmtSelect, v17, -6, &v24->bygolden_item_num[j], 0i64, &v14);
        }
        for ( k = 0; ; ++k )
        {
          v8 = CGoldenBoxItemMgr::Instance();
          v9 = CGoldenBoxItemMgr::GetLoopCount(v8);
          if ( k >= (unsigned __int8)v9 )
            break;
          for ( l = 0; l < 100; ++l )
          {
            ++v17;
            StrLen_or_IndPtr = &v14;
            SQLStmt = 0i64;
            v15 = SQLGetData_0(v23->m_hStmtSelect, v17++, 4, &v24->List[k][l], 0i64, &v14);
            StrLen_or_IndPtr = &v14;
            SQLStmt = 0i64;
            v15 = SQLGetData_0(v23->m_hStmtSelect, v17, 4, &v24->List[k][l].wGoldencount, 0i64, &v14);
          }
        }
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        if ( v23->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", _Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
