/*
 * Function: ?ReInitParticle@CParticle@@QEAAXH@Z
 * Address: 0x14051AE00
 */

void __fastcall CParticle::ReInitParticle(CParticle *this, signed int a2)
{
  signed int v2; // esi@1
  int v3; // edx@1
  CParticle *v4; // rdi@1
  _PARTICLE_ELEMENT *v5; // rax@2
  int v6; // ebx@2
  float v7; // xmm1_4@5

  v2 = a2;
  v3 = this->mNum;
  v4 = this;
  if ( v3 != v2 )
  {
    LODWORD(this->mTotalTime) = 0;
    v5 = (_PARTICLE_ELEMENT *)ReAlloc(this->mElement, 104 * v3, 104 * v2);
    v6 = v4->mNum;
    v4->mElement = v5;
    if ( v2 > v6 && v6 < v2 )
    {
      do
        CParticle::InitElement(v4, v6++, 0.0);
      while ( v6 < v2 );
    }
    v7 = v4->mLiveTime;
    v4->mNum = v2;
    v4->mOnePerTime = (float)(v7 / v4->mTimeSpeed) / (float)v2;
  }
}
