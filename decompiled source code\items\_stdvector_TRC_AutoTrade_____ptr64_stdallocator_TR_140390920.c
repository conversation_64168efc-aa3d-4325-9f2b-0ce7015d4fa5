/*
 * Function: _std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::_Insert_n_::_1_::catch$1
 * Address: 0x140390920
 */

void __fastcall __noreturn std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Destroy(
    *(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > **)(a2 + 160),
    (TRC_AutoTrade **)(*(_QWORD *)(*(_QWORD *)(a2 + 168) + 16i64) + 8i64 * *(_QWORD *)(a2 + 176)),
    (TRC_AutoTrade **)(*(_QWORD *)(*(_QWORD *)(a2 + 160) + 24i64) + 8i64 * *(_QWORD *)(a2 + 176)));
  CxxThrowException_0(0i64, 0i64);
}
