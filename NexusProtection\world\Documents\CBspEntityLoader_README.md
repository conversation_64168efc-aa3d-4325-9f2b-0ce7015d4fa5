# CBspEntityLoader - Modern BSP Entity Loading System

## Overview

The `CBspEntityLoader` class provides a modern, maintainable C++20 implementation of the BSP (Binary Space Partitioning) entity loading system. This refactored version replaces the original decompiled C code with proper error handling, memory management, and modern C++ patterns.

## Original Source

**Refactored from:** `decompiled source code/world/LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c`  
**Original Function:** `CBsp::LoadEntities`  
**Original Address:** `0x1404F96C0`  
**Lines of Code:** 201 lines → Modern C++ implementation

## Key Features

### 🔧 **Modern C++20 Implementation**
- **RAII Memory Management**: Automatic resource cleanup
- **Smart Pointers**: Safe memory handling with `std::unique_ptr`
- **STL Containers**: Modern `std::vector`, `std::unordered_map`, `std::optional`
- **Exception Safety**: Proper error handling and resource cleanup
- **Move Semantics**: Efficient resource transfer

### 🎯 **Entity Loading Capabilities**
- **Static Entity Loading**: 3D models, meshes, and static objects
- **Particle System Loading**: Particle effects and animations
- **Shader Entity Support**: Entities with custom shaders
- **Memory Management**: Efficient allocation and deallocation
- **File Path Processing**: Robust file path handling

### 📊 **Statistics and Monitoring**
- **Loading Statistics**: Success rates, timing, memory usage
- **Error Tracking**: Detailed error reporting and logging
- **Performance Metrics**: Load times and resource usage
- **Verbose Logging**: Configurable logging levels

### 🛡️ **Error Handling**
- **Comprehensive Error Codes**: Specific failure reasons
- **Exception Safety**: No resource leaks on failure
- **Validation**: Input data validation and sanitization
- **Graceful Degradation**: Partial loading support

## Class Structure

### Core Classes

#### `CBspEntityLoader`
Main entity loading class with the following key methods:

```cpp
// Primary loading methods
EntityLoadResult LoadEntities(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList);
EntityLoadResult LoadEntitiesWithFlags(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList, EntityLoadFlags loadFlags);

// Individual entity loading
bool LoadSingleEntity(EntityInfo& entityInfo, const std::string& basePath, EntityLoadFlags loadFlags);
bool LoadParticleEntity(EntityInfo& entityInfo, const std::string& basePath);

// Management and queries
bool CleanupEntities();
std::optional<EntityInfo> GetEntityById(uint16_t entityId) const;
std::vector<EntityInfo> GetEntitiesByType(EntityType type) const;
const EntityLoadingStats& GetStatistics() const;
```

#### `EntityInfo`
Modern entity information structure:

```cpp
struct EntityInfo {
    uint16_t id;
    EntityType type;
    std::string filename;
    EntityTransform transform;
    bool isFileExist;
    bool isParticle;
    uint32_t shaderID;
    uint32_t flags;
    std::unique_ptr<CParticle> particle;
};
```

#### `EntityTransform`
3D transformation data:

```cpp
struct EntityTransform {
    std::array<float, 3> position;
    float rotationX, rotationY, scale;
    std::array<int16_t, 3> boundingBoxMin, boundingBoxMax;
    float additionalFrame;
};
```

### Enumerations

#### `EntityLoadResult`
```cpp
enum class EntityLoadResult : uint8_t {
    Success,
    MemoryAllocationFailed,
    EntityFileNotFound,
    ParticleFileNotFound,
    InvalidEntityData,
    ShaderLoadFailed,
    ParticleInitFailed,
    EntityInitFailed,
    UnknownError
};
```

#### `EntityType`
```cpp
enum class EntityType : uint8_t {
    Unknown,
    StaticEntity,
    ParticleEntity,
    ShaderEntity,
    AnimatedEntity
};
```

#### `EntityLoadFlags`
```cpp
enum class EntityLoadFlags : uint32_t {
    None = 0x00,
    UseShader = 0x02,
    RestoreTexture = 0x20,
    EnableFlag = 0x40
};
```

## Usage Examples

### Basic Entity Loading

```cpp
#include "CBspEntityLoader.h"

using namespace NexusProtection::World;

// Create loader
auto loader = CBspEntityLoaderFactory::CreateDefaultLoader();

// Load entities
CBsp* bsp = GetBspInstance();
_READ_MAP_ENTITIES_LIST* entityList = GetEntityListData();

EntityLoadResult result = loader->LoadEntities(bsp, entityList);

if (result == EntityLoadResult::Success) {
    std::cout << "Entities loaded successfully!" << std::endl;
    std::cout << "Total entities: " << loader->GetEntityCount() << std::endl;
    std::cout << "Success rate: " << loader->GetStatistics().GetSuccessRate() << "%" << std::endl;
} else {
    std::cerr << "Failed to load entities: " << 
                 CBspEntityLoader::EntityLoadResultToString(result) << std::endl;
    std::cerr << "Error: " << loader->GetLastError() << std::endl;
}
```

### Advanced Loading with Custom Flags

```cpp
// Load with custom flags
EntityLoadFlags flags = static_cast<EntityLoadFlags>(
    static_cast<uint32_t>(EntityLoadFlags::UseShader) |
    static_cast<uint32_t>(EntityLoadFlags::RestoreTexture)
);

EntityLoadResult result = loader->LoadEntitiesWithFlags(bsp, entityList, flags);
```

### Querying Loaded Entities

```cpp
// Get entity by ID
auto entityOpt = loader->GetEntityById(42);
if (entityOpt.has_value()) {
    const EntityInfo& entity = entityOpt.value();
    std::cout << "Entity " << entity.id << ": " << entity.filename << std::endl;
}

// Get all particle entities
auto particleEntities = loader->GetEntitiesByType(EntityType::ParticleEntity);
std::cout << "Found " << particleEntities.size() << " particle entities" << std::endl;
```

### Statistics and Monitoring

```cpp
const EntityLoadingStats& stats = loader->GetStatistics();

std::cout << "Loading Statistics:" << std::endl;
std::cout << "  Total entities: " << stats.totalEntities << std::endl;
std::cout << "  Successfully loaded: " << stats.successfullyLoaded << std::endl;
std::cout << "  Failed to load: " << stats.failedToLoad << std::endl;
std::cout << "  Particle entities: " << stats.particleEntities << std::endl;
std::cout << "  Static entities: " << stats.staticEntities << std::endl;
std::cout << "  Memory allocated: " << stats.totalMemoryAllocated << " bytes" << std::endl;
std::cout << "  Load time: " << stats.GetDuration().count() << " ms" << std::endl;
std::cout << "  Success rate: " << stats.GetSuccessRate() << "%" << std::endl;

// Check for failed files
if (!stats.failedFiles.empty()) {
    std::cout << "Failed files:" << std::endl;
    for (const auto& file : stats.failedFiles) {
        std::cout << "  - " << file << std::endl;
    }
}
```

## Integration with Legacy Code

The refactored system maintains compatibility with the original BSP system while providing modern interfaces:

### Legacy Structure Support
- Maintains compatibility with `CBsp`, `CEntity`, `CParticle` classes
- Supports original `_READ_MAP_ENTITIES_LIST` and `_ENTITY_LIST` structures
- Preserves memory layout for existing systems

### Migration Path
1. **Phase 1**: Use new loader with existing BSP structures
2. **Phase 2**: Gradually modernize dependent systems
3. **Phase 3**: Full migration to modern C++ interfaces

## Performance Improvements

### Memory Management
- **Reduced Fragmentation**: Smart pointer usage reduces memory fragmentation
- **RAII**: Automatic cleanup prevents memory leaks
- **Efficient Allocation**: Batch allocation for entity arrays

### Error Handling
- **Early Validation**: Input validation prevents unnecessary processing
- **Graceful Failure**: Partial loading continues on individual entity failures
- **Resource Cleanup**: Automatic cleanup on exceptions

### Monitoring
- **Real-time Statistics**: Track loading progress and performance
- **Detailed Logging**: Configurable logging for debugging
- **Performance Metrics**: Timing and memory usage tracking

## Error Handling

### Common Error Scenarios

1. **Memory Allocation Failures**
   - Automatic cleanup of partially allocated resources
   - Clear error reporting with specific failure points

2. **File Not Found**
   - Individual entity failures don't stop overall loading
   - Detailed logging of missing files

3. **Invalid Data**
   - Input validation prevents crashes
   - Graceful handling of corrupted data

### Error Recovery

```cpp
EntityLoadResult result = loader->LoadEntities(bsp, entityList);

switch (result) {
    case EntityLoadResult::MemoryAllocationFailed:
        // Handle memory issues
        FreeUnusedMemory();
        RetryWithReducedSettings();
        break;
        
    case EntityLoadResult::EntityFileNotFound:
        // Handle missing files
        LogMissingFiles(loader->GetStatistics().failedFiles);
        ContinueWithAvailableEntities();
        break;
        
    case EntityLoadResult::InvalidEntityData:
        // Handle data corruption
        ValidateAndRepairData();
        break;
        
    default:
        // Handle other errors
        break;
}
```

## Testing and Validation

### Unit Testing
- Individual method testing with mock data
- Error condition testing
- Memory leak detection

### Integration Testing
- Full BSP loading scenarios
- Performance benchmarking
- Compatibility testing with legacy systems

### Validation
- Memory usage validation
- Loading time benchmarks
- Success rate monitoring

## Future Enhancements

### Planned Improvements
1. **Async Loading**: Background entity loading
2. **Streaming**: Progressive entity loading for large maps
3. **Caching**: Entity data caching for faster subsequent loads
4. **Compression**: Compressed entity data support

### Modernization Opportunities
1. **Coroutines**: C++20 coroutine-based async loading
2. **Modules**: C++20 module support
3. **Concepts**: Template constraints for type safety
4. **Ranges**: STL ranges for data processing

## Dependencies

### Required Headers
- `<memory>` - Smart pointers
- `<string>` - String handling
- `<vector>` - Dynamic arrays
- `<unordered_map>` - Hash maps
- `<optional>` - Optional values
- `<chrono>` - Timing
- `<functional>` - Function objects

### Legacy Dependencies
- Legacy BSP system classes (`CBsp`, `CEntity`, `CParticle`)
- Legacy memory allocation functions (`Dmalloc`, `memset_0`, `memcpy_0`)
- Legacy file system functions

## Compilation Requirements

- **C++20 Standard**: Required for modern features
- **VS2022 v143 Toolset**: As specified in project requirements
- **Windows Platform**: Current implementation targets Windows

## Conclusion

The `CBspEntityLoader` represents a significant modernization of the BSP entity loading system, providing:

- **Improved Maintainability**: Clean, readable modern C++ code
- **Better Error Handling**: Comprehensive error reporting and recovery
- **Enhanced Performance**: Efficient memory management and processing
- **Future-Proof Design**: Extensible architecture for future enhancements

This refactoring maintains full compatibility with existing systems while providing a foundation for future modernization efforts.
