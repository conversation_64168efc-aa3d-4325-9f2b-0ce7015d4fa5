/*
 * Function: ?IsEmpty@CUnmannedTraderGroupDivisionVersionInfo@@QEAA_NXZ
 * Address: 0x140397E20
 */

bool __fastcall CUnmannedTraderGroupDivisionVersionInfo::IsEmpty(CUnmannedTraderGroupDivisionVersionInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CUnmannedTraderGroupDivisionVersionInfo *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return v6->m_iType < 0 || std::vector<unsigned long,std::allocator<unsigned long>>::empty(&v6->m_vecuiVersion);
}
