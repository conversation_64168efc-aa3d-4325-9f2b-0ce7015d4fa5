/*
 * Function: ??$_Iter_cat@PEAPEAVCLogTypeDBTask@@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCLogTypeDBTask@@@Z
 * Address: 0x1402C7E00
 */

CLogTypeDBTask **const *__fastcall std::_Iter_cat<CLogTypeDBTask * *>(CLogTypeDBTask **const *__formal)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  CLogTypeDBTask **const *v5; // [sp+50h] [bp+8h]@1

  v5 = __formal;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return v5;
}
