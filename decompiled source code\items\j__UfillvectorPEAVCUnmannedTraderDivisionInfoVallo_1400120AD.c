/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x1400120AD
 */

CUnmannedTraderDivisionInfo **__fastcall std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Ufill(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this, CUnmannedTraderDivisionInfo **_Ptr, unsigned __int64 _Count, CUnmannedTraderDivisionInfo *const *_Val)
{
  return std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _<PERSON>);
}
