/*
 * Function: ?Init@_MASTERY_PARAM@@QEAA_NPEAU_STAT_DB_BASE@@E@Z
 * Address: 0x1400781D0
 */

char __fastcall _MASTERY_PARAM::Init(_MASTERY_PARAM *this, _STAT_DB_BASE *pStatBase, char byRaceCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@6
  char v7; // al@20
  char v8; // al@23
  __int64 v9; // [sp+0h] [bp-48h]@1
  int nMasteryIndex; // [sp+20h] [bp-28h]@4
  _base_fld *v11; // [sp+28h] [bp-20h]@9
  unsigned int v12; // [sp+30h] [bp-18h]@23
  _MASTERY_PARAM *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13->m_byRaceCode = byRaceCode;
  memcpy_0(&v13->m_BaseCum, pStatBase, 0x140ui64);
  memset_0(v13->m_dwForceLvCum, 0, 0x10ui64);
  _mastery_up_data::init(&v13->m_MastUpData);
  _skill_lv_up_data::init(&v13->m_SkillUpData);
  v13->m_bUpdateEquipMast = 0;
  for ( nMasteryIndex = 0; nMasteryIndex < 2; ++nMasteryIndex )
  {
    v5 = CalcMastery(0, nMasteryIndex, v13->m_BaseCum.m_dwDamWpCnt[nMasteryIndex], v13->m_byRaceCode);
    v13->m_mtyWp[nMasteryIndex] = v5;
  }
  v13->m_mtySuffer = CalcMastery(1, 0, v13->m_BaseCum.m_dwDefenceCnt, v13->m_byRaceCode);
  v13->m_mtyShield = CalcMastery(2, 0, v13->m_BaseCum.m_dwShieldCnt, v13->m_byRaceCode);
  memset_0(v13->m_dwSkillMasteryCum, 0, 0x20ui64);
  for ( nMasteryIndex = 0; nMasteryIndex < 48; ++nMasteryIndex )
  {
    v11 = CRecordData::GetRecord(_MASTERY_PARAM::s_pSkillData, nMasteryIndex);
    if ( !v11 )
      return 0;
    if ( *(_DWORD *)&v11[4].m_strCode[60] > 3 )
      return 0;
    if ( *(_DWORD *)&v11[4].m_strCode[60] >= 0 )
      v13->m_lvSkill[nMasteryIndex] = GetSFLevel(
                                        *(_DWORD *)&v11[4].m_strCode[60],
                                        v13->m_BaseCum.m_dwSkillCum[nMasteryIndex]);
    if ( *(_DWORD *)&v11[1].m_strCode[4] < 8u )
      v13->m_dwSkillMasteryCum[*(_DWORD *)&v11[1].m_strCode[4]] += v13->m_BaseCum.m_dwSkillCum[nMasteryIndex];
  }
  for ( nMasteryIndex = 0; nMasteryIndex < 8; ++nMasteryIndex )
  {
    v7 = CalcMastery(3, nMasteryIndex, v13->m_dwSkillMasteryCum[nMasteryIndex], v13->m_byRaceCode);
    v13->m_mtySkill[nMasteryIndex] = v7;
  }
  for ( nMasteryIndex = 0; nMasteryIndex < 24; ++nMasteryIndex )
  {
    v8 = CalcMastery(4, nMasteryIndex, v13->m_BaseCum.m_dwForceCum[nMasteryIndex], v13->m_byRaceCode);
    v13->m_mtyForce[nMasteryIndex] = v8;
    v12 = v13->m_BaseCum.m_dwForceCum[nMasteryIndex] + v13->m_dwForceLvCum[nMasteryIndex % -4];
    v13->m_dwForceLvCum[nMasteryIndex % -4] = v12;
  }
  v13->m_mtyStaff = GetStaffMastery(v13->m_dwForceLvCum);
  v13->m_mtyMakeItem[0] = CalcMastery(5, 0, v13->m_BaseCum.m_dwMakeCum[0], v13->m_byRaceCode);
  v13->m_mtyMakeItem[1] = CalcMastery(5, 1, v13->m_BaseCum.m_dwMakeCum[1], v13->m_byRaceCode);
  v13->m_mtyMakeItem[2] = CalcMastery(5, 2, v13->m_BaseCum.m_dwMakeCum[2], v13->m_byRaceCode);
  v13->m_mtySpecial = CalcMastery(6, 0, v13->m_BaseCum.m_dwSpecialCum, v13->m_byRaceCode);
  v13->m_ppdwMasteryCumPtr[0] = v13->m_BaseCum.m_dwDamWpCnt;
  v13->m_ppbyMasteryPtr[0] = v13->m_mtyWp;
  v13->m_ppdwMasteryCumPtr[1] = &v13->m_BaseCum.m_dwDefenceCnt;
  v13->m_ppbyMasteryPtr[1] = &v13->m_mtySuffer;
  v13->m_ppdwMasteryCumPtr[2] = &v13->m_BaseCum.m_dwShieldCnt;
  v13->m_ppbyMasteryPtr[2] = &v13->m_mtyShield;
  v13->m_ppdwMasteryCumPtr[3] = v13->m_dwSkillMasteryCum;
  v13->m_ppbyMasteryPtr[3] = v13->m_mtySkill;
  v13->m_ppdwMasteryCumPtr[4] = v13->m_BaseCum.m_dwForceCum;
  v13->m_ppbyMasteryPtr[4] = v13->m_mtyForce;
  v13->m_ppdwMasteryCumPtr[5] = v13->m_BaseCum.m_dwMakeCum;
  v13->m_ppbyMasteryPtr[5] = v13->m_mtyMakeItem;
  v13->m_ppdwMasteryCumPtr[6] = &v13->m_BaseCum.m_dwSpecialCum;
  v13->m_ppbyMasteryPtr[6] = &v13->m_mtySpecial;
  v13->m_ppbyEquipMasteryPrt[0] = v13->m_mtyWp;
  v13->m_ppbyEquipMasteryPrt[1] = &v13->m_mtyWp[1];
  v13->m_ppbyEquipMasteryPrt[2] = &v13->m_mtySpecial;
  v13->m_ppbyEquipMasteryPrt[3] = &v13->m_mtyStaff;
  v13->m_ppbyEquipMasteryPrt[4] = &v13->m_mtyShield;
  v13->m_ppbyEquipMasteryPrt[5] = &v13->m_mtySuffer;
  return 1;
}
