/*
 * Function: ?InitDB@CLogTypeDBTaskManager@@QEAA_NPEBD0@Z
 * Address: 0x1402C2E50
 */

char __fastcall CLogTypeDBTaskManager::InitDB(CLogTypeDBTaskManager *this, const char *szDBName, const char *szDBIP)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  CNationSettingManager *v6; // rax@8
  CNationSettingManager *v7; // rax@8
  const char *v8; // rax@8
  __int64 v9; // [sp+0h] [bp-38h]@1
  char *passWord; // [sp+20h] [bp-18h]@8
  CLogTypeDBTaskManager *v11; // [sp+40h] [bp+8h]@1
  char *szOdbcName; // [sp+48h] [bp+10h]@1
  const char *v13; // [sp+50h] [bp+18h]@1

  v13 = szDBIP;
  szOdbcName = (char *)szDBName;
  v11 = this;
  v3 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CRFNewDatabase::SetLogFile((CRFNewDatabase *)&v11->m_pkWorldDB->vfptr, "..\\ZoneServerLog\\", szDBName);
  if ( v11->m_eState == LTDTM_INIT && szOdbcName && v13 )
  {
    v6 = CTSingleton<CNationSettingManager>::Instance();
    passWord = (char *)CNationSettingManager::GetWorldDBPW(v6);
    v7 = CTSingleton<CNationSettingManager>::Instance();
    v8 = CNationSettingManager::GetWorldDBID(v7);
    if ( CRFNewDatabase::StartDataBase((CRFNewDatabase *)&v11->m_pkWorldDB->vfptr, odbcName, v8, passWord) )
    {
      CLogFile::Write(&stru_1799C8F30, "Log Type Start World DataBase Complete!!");
      v11->m_eState = 1;
      result = 1;
    }
    else
    {
      MyMessageBox("CLogTypeDBTaskManager::InitDB()", "Connect World DB Failed!");
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
