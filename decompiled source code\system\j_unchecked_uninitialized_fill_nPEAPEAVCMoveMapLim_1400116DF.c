/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@stdext@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCMoveMapLimitInfo@@@std@@@Z
 * Address: 0x1400116DF
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val, std::allocator<CMoveMapLimitInfo *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    _First,
    _Count,
    _Val,
    _Al);
}
