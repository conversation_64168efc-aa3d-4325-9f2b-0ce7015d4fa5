# CMerchant Class Documentation

## Overview

The `CMerchant` class represents NPC merchants in the NexusProtection game engine. This class was refactored from decompiled C source files to modern C++17/C++20 standards compatible with Visual Studio 2022. It inherits from `CCharacter` and provides merchant-specific functionality for shop management and player interactions.

## Features

### Core Functionality
- **NPC Creation and Destruction**: Complete lifecycle management for merchant NPCs
- **Item Store Management**: Integration with item store systems for shop functionality
- **Player Interaction**: Handle player interactions and shop operations
- **Merchant Pool Management**: Efficient management of merchant arrays with empty slot detection
- **Messaging System**: Communication with game systems for merchant events
- **State Tracking**: Live merchant counting and status management

### Design Principles
- **Object-Oriented Design**: Clean inheritance from CCharacter base class
- **Resource Management**: Proper RAII and resource cleanup
- **Error Handling**: Comprehensive parameter validation and exception handling
- **Modern C++**: Uses C++17/C++20 features and STL containers
- **Logging**: Built-in operation and error logging
- **Thread Safety**: Safe for single-threaded game loop operations

## Class Structure

### Public Methods

#### Constructor and Destructor
- `CMerchant()`: Default constructor with proper initialization
- `~CMerchant()`: Virtual destructor with automatic cleanup

#### Core Merchant Operations
- `bool Create(_npc_create_setdata* pData)`: Create merchant with specified data
- `bool Destroy()`: Destroy merchant and clean up resources
- `void Update()`: Update merchant state and behavior

#### Property Access
- `bool IsLive() const`: Check if merchant is currently active
- `void SetLive(bool bLive)`: Set merchant live state
- `uint32_t GetObjSerial() const`: Get unique object serial number
- `uint8_t GetRaceCode() const`: Get merchant race code
- `CItemStore* GetItemStore() const`: Get associated item store
- `uint32_t GetLastDestroyTime() const`: Get last destruction timestamp
- `void SetLastDestroyTime(uint32_t dwTime)`: Set last destruction timestamp

#### Static Utility Functions
- `static uint32_t GetNewMonSerial()`: Generate new unique serial number
- `static int GetLiveNum()`: Get current number of live merchants

#### Player Interaction
- `bool OnPlayerInteract(void* pPlayer)`: Handle player interaction
- `bool OpenShop(void* pPlayer)`: Open merchant shop for player
- `void CloseShop(void* pPlayer)`: Close merchant shop for player

#### Validation and Utility
- `bool ValidateData() const`: Validate merchant data integrity
- `void Reset()`: Reset merchant to default state
- `bool CanDestroy() const`: Check if merchant can be destroyed

### Protected Members

#### Member Variables
- `bool m_bLive`: Whether the merchant is currently alive
- `uint32_t m_dwObjSerial`: Unique object serial number
- `uint8_t m_byRaceCode`: Race code for the merchant type
- `CItemStore* m_pItemStore`: Pointer to the merchant's item store
- `uint32_t m_dwLastDestroyTime`: Timestamp of last destruction
- `uint32_t m_dwCreateTime`: Timestamp when merchant was created
- `uint32_t m_dwLastUpdateTime`: Timestamp of last update

#### Static Members
- `static int s_nLiveNum`: Global count of live merchants
- `static uint32_t s_dwSerialCounter`: Counter for generating unique serials

#### Protected Methods
- `void InitializeDefaults()`: Initialize merchant with default values
- `void CleanupResources()`: Clean up merchant resources
- `bool ValidateCreationData(const _npc_create_setdata* pData) const`: Validate creation data
- `void LogOperation(const char* operation, const char* details = nullptr) const`: Log operations
- `void LogError(const char* errorMessage, const char* context = nullptr) const`: Log errors

## CMerchantUtils Namespace

Provides utility functions for merchant management:

### Functions
- `CMerchant* FindEmptyNPC(CMerchant* pList, int nMax)`: Find empty merchant slot in array
- `uint32_t GetCurrentTime()`: Get current system time in milliseconds
- `bool HasEnoughTimePassed(uint32_t dwLastDestroyTime, uint32_t dwMinInterval = 30000)`: Check time intervals
- `void LogOperation(const char* operation, const char* details = nullptr)`: Log utility operations
- `void LogError(const char* errorMessage, const char* context = nullptr)`: Log utility errors

## Data Structures

### _npc_create_setdata
Structure containing NPC creation parameters:
- `_character_create_setdata m_pRecordSet`: Base character creation data
- `CItemStore* m_pLinkItemStore`: Associated item store
- `uint8_t m_byRaceCode`: Race code for the merchant
- `uint32_t m_dwFlags`: Creation flags

### _character_create_setdata
Base character creation structure:
- `uint32_t characterType`: Type of character
- `float position[3]`: 3D position coordinates
- `uint32_t flags`: Character creation flags

## Usage Examples

### Creating a Merchant
```cpp
// Prepare creation data
_npc_create_setdata createData;
createData.m_pLinkItemStore = pItemStore;
createData.m_byRaceCode = MERCHANT_RACE_HUMAN;
// Set other fields...

// Create merchant
CMerchant merchant;
if (merchant.Create(&createData)) {
    std::cout << "Merchant created with serial: " << merchant.GetObjSerial() << std::endl;
}
```

### Finding Empty Merchant Slot
```cpp
// Array of merchants
CMerchant merchantArray[MAX_MERCHANTS];

// Find empty slot
CMerchant* pEmptyMerchant = CMerchantUtils::FindEmptyNPC(merchantArray, MAX_MERCHANTS);
if (pEmptyMerchant) {
    // Use the empty slot
    pEmptyMerchant->Create(&createData);
}
```

### Player Interaction
```cpp
// Handle player clicking on merchant
if (merchant.IsLive()) {
    if (merchant.OnPlayerInteract(pPlayer)) {
        std::cout << "Shop opened for player" << std::endl;
    }
}
```

### Merchant Management
```cpp
// Update all merchants
for (int i = 0; i < MAX_MERCHANTS; ++i) {
    if (merchantArray[i].IsLive()) {
        merchantArray[i].Update();
    }
}

// Check live merchant count
std::cout << "Live merchants: " << CMerchant::GetLiveNum() << std::endl;
```

## Error Handling

The class includes comprehensive error handling:
- Parameter validation for all public methods
- Exception handling with logging
- Graceful degradation for invalid inputs
- Detailed error messages with context information

## Constants and Configuration

- **Empty Slot Timeout**: 30 seconds (30000ms) minimum time before reusing destroyed merchant slot
- **Serial Number Range**: Starts from 1000 and increments
- **Default State**: New merchants start in non-live state until Create() is called

## Thread Safety

- All methods are designed for single-threaded game loop execution
- Static member access is not thread-safe and should be synchronized if used in multi-threaded context
- External dependencies (like CItemStore) may require additional synchronization

## Dependencies

- Standard C++ libraries: `<cstdint>`, `<memory>`, `<string>`, `<chrono>`
- Windows API: `<windows.h>` for timeGetTime()
- Game engine headers: `CCharacter.h`, `CItemStore.h`
- Platform: Visual Studio 2022, C++17/C++20 standard

## Refactoring Notes

This class was refactored from the following decompiled source files:
- `FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140139CD0.c`
- `CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140139140.c`

The refactoring process involved:
1. Converting C-style functions to C++ class methods
2. Adding proper inheritance from CCharacter
3. Implementing modern C++ memory management
4. Adding comprehensive error handling and logging
5. Creating a clean, maintainable class structure
6. Adding utility namespace for helper functions

## Performance Considerations

- Efficient empty slot detection with time-based validation
- Minimal memory allocation during normal operations
- Fast serial number generation
- Optimized for frequent Update() calls in game loop
