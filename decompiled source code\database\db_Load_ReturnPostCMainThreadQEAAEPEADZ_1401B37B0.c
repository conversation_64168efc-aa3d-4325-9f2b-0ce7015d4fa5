/*
 * Function: ?db_Load_ReturnPost@CMainThread@@QEAAEPEAD@Z
 * Address: 0x1401B37B0
 */

char __fastcall CMainThread::db_Load_ReturnPost(CMainThread *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B68h]@1
  char *v6; // [sp+20h] [bp-B48h]@4
  char Dst; // [sp+40h] [bp-B28h]@4
  unsigned int v8; // [sp+44h] [bp-B24h]@6
  int v9; // [sp+48h] [bp-B20h]@7
  char v10; // [sp+4Ch] [bp-B1Ch]@7
  char Src[17]; // [sp+4Dh] [bp-B1Bh]@7
  char v12[21]; // [sp+5Eh] [bp-B0Ah]@7
  char v13[201]; // [sp+73h] [bp-AF5h]@7
  int pl_nKey; // [sp+13Ch] [bp-A2Ch]@7
  __int64 v15; // [sp+140h] [bp-A28h]@7
  int v16[2]; // [sp+148h] [bp-A20h]@7
  __int64 v17; // [sp+150h] [bp-A18h]@7
  int v18; // [sp+158h] [bp-A10h]@7
  char v19[2536]; // [sp+15Ch] [bp-A0Ch]@7
  unsigned int j; // [sp+B44h] [bp-24h]@5
  unsigned __int64 v21; // [sp+B50h] [bp-18h]@4
  CMainThread *v22; // [sp+B70h] [bp+8h]@1

  v22 = this;
  v2 = &v5;
  for ( i = 728i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v21 = (unsigned __int64)&v5 ^ _security_cookie;
  v6 = pData;
  memset_0(&Dst, 0, 0xAF8ui64);
  v6[8] = CRFWorldDatabase::Select_ReturnPost(
            v22->m_pWorldDB,
            *((_DWORD *)v6 + 1),
            *(_DWORD *)v6,
            (_return_post_list *)&Dst);
  if ( !v6[8] )
  {
    for ( j = 0; j < v8; ++j )
    {
      *(_DWORD *)&v6[280 * j + 16] = *(&v9 + 70 * (signed int)j);
      v6[280 * j + 20] = *(&v10 + 280 * (signed int)j);
      strcpy_s(&v6[280 * j + 21], 0x11ui64, &Src[280 * j]);
      strcpy_s(&v6[280 * j + 38], 0x15ui64, &v12[280 * j]);
      strcpy_s(&v6[280 * j + 59], 0xC9ui64, &v13[280 * j]);
      _INVENKEY::LoadDBKey((_INVENKEY *)&v6[280 * j + 260], *(&pl_nKey + 70 * (signed int)j));
      *(_QWORD *)&v6[280 * j + 264] = *(&v15 + 35 * (signed int)j);
      *(_DWORD *)&v6[280 * j + 272] = v16[70 * j];
      *(_DWORD *)&v6[280 * j + 288] = *(&v18 + 70 * (signed int)j);
      v6[280 * j + 292] = v19[280 * j];
      *(_QWORD *)&v6[280 * j + 280] = *(&v17 + 35 * (signed int)j);
    }
    *((_DWORD *)v6 + 3) = v8;
  }
  return 0;
}
