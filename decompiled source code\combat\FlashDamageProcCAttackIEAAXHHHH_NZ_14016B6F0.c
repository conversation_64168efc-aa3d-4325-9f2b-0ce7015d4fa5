/*
 * Function: ?FlashDamageProc@CAttack@@IEAAXHHHH_N@Z
 * Address: 0x14016B6F0
 */

void __fastcall CAttack::FlashDamageProc(CAttack *this, int nLimDist, int nAttPower, int nAngle, int nEffAttPower, bool bUseEffBullet)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CCharacter **v8; // rcx@7
  _attack_param *v9; // rdx@7
  _attack_param *v10; // r8@7
  CCharacter **v11; // rcx@8
  _attack_param *v12; // rdx@8
  _attack_param *v13; // r8@8
  CCharacter *v14; // rdx@13
  CCharacter *v15; // rax@13
  CCharacter *v16; // rdx@13
  __int64 v17; // rax@13
  int v18; // eax@14
  _sec_info *v19; // rax@19
  CCharacter *v20; // rdx@33
  int v21; // eax@33
  CPlayer *v22; // rax@39
  CCharacter *v23; // rdx@51
  int v24; // eax@51
  float v25; // xmm0_4@58
  float v26; // xmm1_4@59
  float *v27; // rax@63
  float *v28; // rcx@63
  int v29; // eax@65
  __int64 v30; // [sp+0h] [bp-158h]@1
  CCharacter *pDst; // [sp+20h] [bp-138h]@7
  bool bBackAttack[8]; // [sp+28h] [bp-130h]@7
  CMapData *v33; // [sp+30h] [bp-128h]@14
  _pnt_rect pRect; // [sp+48h] [bp-110h]@14
  bool pbInGuildBattle; // [sp+74h] [bp-E4h]@14
  int j; // [sp+84h] [bp-D4h]@14
  int k; // [sp+88h] [bp-D0h]@16
  unsigned int dwSecIndex; // [sp+8Ch] [bp-CCh]@19
  CObjectList *v39; // [sp+90h] [bp-C8h]@19
  CObjectList *v40; // [sp+98h] [bp-C0h]@20
  CGameObject *pObject; // [sp+A0h] [bp-B8h]@22
  CPlayer *v42; // [sp+A8h] [bp-B0h]@35
  CPlayer *v43; // [sp+B0h] [bp-A8h]@36
  AutominePersonal *v44; // [sp+B8h] [bp-A0h]@39
  CAnimus *v45; // [sp+C0h] [bp-98h]@43
  CPlayer *v46; // [sp+C8h] [bp-90h]@46
  CCharacter *v47; // [sp+D0h] [bp-88h]@51
  CPlayer *v48; // [sp+D8h] [bp-80h]@51
  float v49; // [sp+E0h] [bp-78h]@58
  int v50; // [sp+E4h] [bp-74h]@62
  _bsp_info *v51; // [sp+E8h] [bp-70h]@13
  CGameObjectVtbl *v52; // [sp+F0h] [bp-68h]@13
  CCharacter *v53; // [sp+F8h] [bp-60h]@13
  _bsp_info *v54; // [sp+100h] [bp-58h]@13
  CGameObjectVtbl *v55; // [sp+108h] [bp-50h]@13
  int v56; // [sp+110h] [bp-48h]@33
  CGameObjectVtbl *v57; // [sp+118h] [bp-40h]@33
  int v58; // [sp+120h] [bp-38h]@51
  CGameObjectVtbl *v59; // [sp+128h] [bp-30h]@51
  float v60; // [sp+130h] [bp-28h]@59
  float v61; // [sp+134h] [bp-24h]@60
  float v62; // [sp+138h] [bp-20h]@60
  float *chkpos; // [sp+140h] [bp-18h]@63
  CGameObject **v64; // [sp+148h] [bp-10h]@65
  CAttack *v65; // [sp+160h] [bp+8h]@1
  int v66; // [sp+168h] [bp+10h]@1
  int nAttPnt; // [sp+170h] [bp+18h]@1
  int v68; // [sp+178h] [bp+20h]@1

  v68 = nAngle;
  nAttPnt = nAttPower;
  v66 = nLimDist;
  v65 = this;
  v6 = &v30;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v65->m_pp->pDst )
  {
    v65->m_DamList[0].m_pChar = v65->m_pp->pDst;
    if ( v65->m_pp->bMatchless )
    {
      v65->m_DamList[0].m_nDamage = ((int (__fastcall *)(CCharacter *))v65->m_pp->pDst->vfptr->GetHP)(v65->m_pp->pDst);
    }
    else if ( bUseEffBullet )
    {
      v8 = &v65->m_pp->pDst;
      v9 = v65->m_pp;
      v10 = v65->m_pp;
      bBackAttack[0] = v65->m_pp->bBackAttack;
      pDst = *v8;
      v65->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                      v65->m_pAttChar,
                                      nEffAttPower,
                                      v10->nPart,
                                      v9->nTol,
                                      pDst,
                                      bBackAttack[0]);
    }
    else
    {
      v11 = &v65->m_pp->pDst;
      v12 = v65->m_pp;
      v13 = v65->m_pp;
      bBackAttack[0] = v65->m_pp->bBackAttack;
      pDst = *v11;
      v65->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                      v65->m_pAttChar,
                                      nAttPnt,
                                      v13->nPart,
                                      v12->nTol,
                                      pDst,
                                      bBackAttack[0]);
    }
    v65->m_nDamagedObjNum = 1;
    if ( CGameObject::GetCurSecNum((CGameObject *)v65->m_pp->pDst) != -1 )
    {
      if ( v65->m_pAttChar->m_pCurMap != v65->m_pp->pDst->m_pCurMap )
      {
        v51 = CMapData::GetBspInfo(v65->m_pp->pDst->m_pCurMap);
        v14 = v65->m_pp->pDst;
        v52 = v65->m_pp->pDst->vfptr;
        LODWORD(v15) = ((int (__fastcall *)(CCharacter *))v52->GetObjName)(v14);
        v53 = v15;
        v54 = CMapData::GetBspInfo(v65->m_pAttChar->m_pCurMap);
        v16 = v65->m_pAttChar;
        v55 = v65->m_pAttChar->vfptr;
        LODWORD(v17) = ((int (__fastcall *)(CCharacter *))v55->GetObjName)(v16);
        *(_QWORD *)bBackAttack = v51;
        pDst = v53;
        CLogFile::Write(
          &stru_1799C8E78,
          "FlashDamage Error AttackTarget Map : Attack Obj( %s : %s ) Dst Obj( %s : %s )",
          v17,
          v54);
        return;
      }
      v33 = v65->m_pp->pDst->m_pCurMap;
      v18 = CGameObject::GetCurSecNum((CGameObject *)v65->m_pp->pDst);
      CMapData::GetRectInRadius(v33, &pRect, 1, v18);
      pbInGuildBattle = 0;
      for ( j = pRect.nStarty; ; ++j )
      {
        if ( j > pRect.nEndy )
          return;
        for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
        {
          v19 = CMapData::GetSecInfo(v33);
          dwSecIndex = v19->m_nSecNumW * j + k;
          v39 = CMapData::GetSectorListObj(v65->m_pAttChar->m_pCurMap, v65->m_pAttChar->m_wMapLayerIndex, dwSecIndex);
          if ( v39 )
          {
            v40 = (CObjectList *)v39->m_Head.m_pNext;
            while ( 1 )
            {
              while ( 1 )
              {
                if ( (_object_list_point *)v40 == &v39->m_Tail )
                  goto LABEL_17;
                pObject = (CGameObject *)v40->vfptr;
                v40 = (CObjectList *)v40->m_Head.m_pItem;
                if ( v65->m_nDamagedObjNum >= 30 )
                  return;
                if ( !pObject->m_ObjID.m_byKind
                  && pObject != (CGameObject *)v65->m_pAttChar
                  && (CCharacter *)pObject != v65->m_pp->pDst )
                {
                  if ( pObject->m_bLive )
                  {
                    if ( !pObject->m_bCorpse )
                    {
                      if ( (unsigned __int8)((int (__fastcall *)(CGameObject *, _QWORD))pObject->vfptr->IsBeAttackedAble)(
                                              pObject,
                                              0i64) )
                      {
                        pbInGuildBattle = 0;
                        if ( !CAttack::CheckGuildBattleLimit(v65, pObject, &pbInGuildBattle) )
                        {
                          if ( pbInGuildBattle
                            || (v56 = ((int (__fastcall *)(CGameObject *))pObject->vfptr->GetObjRace)(pObject),
                                v20 = v65->m_pAttChar,
                                v57 = v65->m_pAttChar->vfptr,
                                v21 = ((int (__fastcall *)(CCharacter *))v57->GetObjRace)(v20),
                                v56 != v21)
                            || v65->m_pAttChar->m_ObjID.m_byID
                            || ((v42 = (CPlayer *)v65->m_pAttChar, pObject->m_ObjID.m_byID)
                             || (v43 = (CPlayer *)pObject, CPlayer::IsPunished((CPlayer *)pObject, 1, 0))
                             || CPlayer::IsChaosMode(v42))
                            && (pObject->m_ObjID.m_byID != 11
                             || (v44 = (AutominePersonal *)pObject,
                                 v22 = AutominePersonal::GetOwner((AutominePersonal *)pObject),
                                 v22 != v42)
                             && CPlayer::IsChaosMode(v42)) )
                          {
                            if ( (unsigned __int8)((int (__fastcall *)(CGameObject *, CCharacter *))pObject->vfptr->IsBeDamagedAble)(
                                                    pObject,
                                                    v65->m_pAttChar) )
                            {
                              if ( v65->m_pAttChar->m_ObjID.m_byID
                                || (v45 = CPlayer::GetRecallAnimus((CPlayer *)v65->m_pAttChar)) == 0i64
                                || (CGameObject *)v45 != pObject )
                              {
                                if ( v65->m_pAttChar->m_ObjID.m_byID
                                  || (v46 = (CPlayer *)v65->m_pAttChar,
                                      ((int (__fastcall *)(CPlayer *))v46->vfptr->GetObjRace)(v46) != 1)
                                  || CPlayer::IsChaosMode(v46)
                                  || pObject->m_ObjID.m_byID != 3 )
                                {
                                  if ( pObject->m_ObjID.m_byID != 7 )
                                  {
                                    if ( pObject->m_ObjID.m_byID != 4
                                      || (v47 = v65->m_pAttChar,
                                          v48 = (CPlayer *)v65->m_pAttChar,
                                          v58 = ((int (__fastcall *)(__int64))v47->vfptr->GetObjRace)((__int64)v47),
                                          v23 = v65->m_pAttChar,
                                          v59 = v65->m_pAttChar->vfptr,
                                          v24 = ((int (__fastcall *)(CCharacter *))v59->GetObjRace)(v23),
                                          v58 != v24)
                                      || CPlayer::IsChaosMode(v48) )
                                    {
                                      if ( (unsigned __int8)((int (__fastcall *)(CCharacter *))v65->m_pAttChar->vfptr->IsAttackableInTown)(v65->m_pAttChar)
                                        || (unsigned __int8)((int (__fastcall *)(CGameObject *))pObject->vfptr->IsAttackableInTown)(pObject)
                                        || !(unsigned __int8)((int (__fastcall *)(CCharacter *))v65->m_pAttChar->vfptr->IsInTown)(v65->m_pAttChar)
                                        && !(unsigned __int8)((int (__fastcall *)(CGameObject *))pObject->vfptr->IsInTown)(pObject) )
                                      {
                                        v25 = v65->m_pAttChar->m_fCurPos[1] - pObject->m_fCurPos[1];
                                        abs(v25);
                                        v49 = v25;
                                        if ( v25 <= 350.0 )
                                        {
                                          GetSqrt(v65->m_pp->pDst->m_fCurPos, pObject->m_fCurPos);
                                          v60 = v25;
                                          ((void (__fastcall *)(CGameObject *))pObject->vfptr->GetWidth)(pObject);
                                          v26 = v60 - (float)(v25 / 2.0);
                                          if ( v26 <= 0.0 )
                                          {
                                            v62 = 0.0;
                                          }
                                          else
                                          {
                                            GetSqrt(v65->m_pp->pDst->m_fCurPos, pObject->m_fCurPos);
                                            v61 = v26;
                                            ((void (__fastcall *)(CGameObject *))pObject->vfptr->GetWidth)(pObject);
                                            v62 = v61 - (float)(v26 / 2.0);
                                          }
                                          v50 = (signed int)ffloor(v62);
                                          if ( v50 < v66 )
                                          {
                                            v27 = v65->m_pp->pDst->m_fCurPos;
                                            v28 = v65->m_pAttChar->m_fCurPos;
                                            chkpos = pObject->m_fCurPos;
                                            if ( CAttack::IsCharInSector(
                                                   pObject->m_fCurPos,
                                                   v28,
                                                   v27,
                                                   (float)v68,
                                                   (float)v66) )
                                            {
                                              break;
                                            }
                                          }
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
              v65->m_DamList[v65->m_nDamagedObjNum].m_pChar = (CCharacter *)pObject;
              if ( v65->m_pp->bMatchless )
                break;
              v64 = (CGameObject **)v65->m_pp;
              v29 = CCharacter::GetAttackRandomPart((CCharacter *)pObject);
              bBackAttack[0] = 0;
              pDst = (CCharacter *)pObject;
              v65->m_DamList[v65->m_nDamagedObjNum].m_nDamage = CCharacter::GetAttackDamPoint(
                                                                  v65->m_pAttChar,
                                                                  nAttPnt,
                                                                  v29,
                                                                  *((_DWORD *)v64 + 3),
                                                                  (CCharacter *)pObject,
                                                                  0);
              if ( v65->m_DamList[v65->m_nDamagedObjNum].m_nDamage != -2 )
              {
                v65->m_DamList[v65->m_nDamagedObjNum].m_nDamage = (signed int)ffloor((float)v65->m_DamList[v65->m_nDamagedObjNum].m_nDamage * (float)((float)(v66 - v50) / (float)v66));
                if ( v65->m_DamList[v65->m_nDamagedObjNum].m_nDamage < 1 )
                  continue;
              }
LABEL_69:
              ++v65->m_nDamagedObjNum;
            }
            v65->m_DamList[v65->m_nDamagedObjNum].m_nDamage = ((int (__fastcall *)(CGameObject *))pObject->vfptr->GetHP)(pObject);
            goto LABEL_69;
          }
LABEL_17:
          ;
        }
      }
    }
  }
}
