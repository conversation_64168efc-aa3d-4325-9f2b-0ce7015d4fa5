/*
 * Function: j_?_Insert_n@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@2@_KAEBQEAVTRC_AutoTrade@@@Z
 * Address: 0x14000D440
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Insert_n(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *_Where, unsigned __int64 _Count, TRC_AutoTrade *const *_Val)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Insert_n(this, _Where, _Count, _<PERSON>);
}
