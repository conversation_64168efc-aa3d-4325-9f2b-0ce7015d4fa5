/*
 * Function: ?throw_ground_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEADPEAM1@Z
 * Address: 0x140238770
 */

void __fastcall CMgrAvatorItemHistory::throw_ground_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, char *pMapCode, float *pfPos, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@4
  __int64 v9; // [sp+0h] [bp-98h]@1
  char *v10; // [sp+20h] [bp-78h]@4
  unsigned __int64 v11; // [sp+28h] [bp-70h]@4
  char *v12; // [sp+30h] [bp-68h]@4
  int v13; // [sp+38h] [bp-60h]@4
  int v14; // [sp+40h] [bp-58h]@4
  int v15; // [sp+48h] [bp-50h]@4
  char *v16; // [sp+50h] [bp-48h]@4
  char *v17; // [sp+58h] [bp-40h]@4
  _base_fld *v18; // [sp+60h] [bp-38h]@4
  char *v19; // [sp+68h] [bp-30h]@4
  char *v20; // [sp+70h] [bp-28h]@4
  int v21; // [sp+78h] [bp-20h]@4
  int v22; // [sp+7Ch] [bp-1Ch]@4
  int v23; // [sp+80h] [bp-18h]@4
  CMgrAvatorItemHistory *v24; // [sp+A0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v25; // [sp+B0h] [bp+18h]@1
  char *v26; // [sp+B8h] [bp+20h]@1

  v26 = pMapCode;
  v25 = pItem;
  v24 = this;
  v6 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v18 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  v19 = v24->m_szCurTime;
  v20 = v24->m_szCurDate;
  v21 = (signed int)ffloor(pfPos[2]);
  v22 = (signed int)ffloor(pfPos[1]);
  v23 = (signed int)ffloor(*pfPos);
  v8 = DisplayItemUpgInfo(v25->m_byTableCode, v25->m_dwLv);
  v17 = v19;
  v16 = v20;
  v15 = v21;
  v14 = v22;
  v13 = v23;
  v12 = v26;
  v11 = v25->m_lnUID;
  v10 = v8;
  sprintf(sData, "DUMP: %s_%u_@%s[%I64u] \t{POS:%s (%d, %d, %d)} [%s %s]\r\n", v18->m_strCode, v25->m_dwDur);
  CMgrAvatorItemHistory::WriteFile(v24, pszFileName, sData);
}
