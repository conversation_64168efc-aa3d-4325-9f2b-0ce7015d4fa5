/*
 * Function: _CUnmannedTraderDivisionInfo::CUnmannedTraderDivisionInfo_::_1_::dtor$0
 * Address: 0x14036D2F0
 */

void __fastcall CUnmannedTraderDivisionInfo::CUnmannedTraderDivisionInfo_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>((std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)(*(_QWORD *)(a2 + 64) + 136i64));
}
