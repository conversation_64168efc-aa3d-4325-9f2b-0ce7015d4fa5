/*
 * Function: ??0CNormalGuildBattleStateRoundList@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403F2150
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleStateRoundList::CNormalGuildBattleStateRoundList(GUILD_BATTLE::CNormalGuildBattleStateRoundList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleStateRoundList *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  GUILD_BATTLE::CGuildBattleStateList::CGuildBattleStateList(
    (GUILD_BATTLE::CGuildBattleStateList *)&v5->vfptr,
    3,
    1,
    1u);
  v5->vfptr = (GUILD_BATTLE::CGuildBattleStateListVtbl *)&GUILD_BATTLE::CNormalGuildBattleStateRoundList::`vftable';
  GUILD_BATTLE::CNormalGuildBattleStateRoundStart::CNormalGuildBattleStateRoundStart(&v5->START);
  GUILD_BATTLE::CNormalGuildBattleStateRoundProcess::CNormalGuildBattleStateRoundProcess(&v5->PROCESS);
  GUILD_BATTLE::CNormalGuildBattleStateRoundReturnStartPos::CNormalGuildBattleStateRoundReturnStartPos(&v5->ROUND_END);
  v5->m_pStateList[0] = (GUILD_BATTLE::CNormalGuildBattleStateRound *)&v5->START.vfptr;
  v5->m_pStateList[1] = (GUILD_BATTLE::CNormalGuildBattleStateRound *)&v5->PROCESS.vfptr;
  v5->m_pStateList[2] = (GUILD_BATTLE::CNormalGuildBattleStateRound *)&v5->ROUND_END.vfptr;
}
