/*
 * Function: ??1Hash<PERSON>ilter@CryptoPP@@UEAA@XZ
 * Address: 0x140623F20
 */

void __fastcall CryptoPP::HashFilter::~HashFilter(CryptoPP::HashFilter *this)
{
  CryptoPP::HashFilter *v1; // [sp+40h] [bp+8h]@1

  v1 = this;
  if ( this )
    CryptoPP::FilterPutSpaceHelper::~FilterPutSpaceHelper((CryptoPP::FilterPutSpaceHelper *)&this->m_tempSpace);
  else
    CryptoPP::FilterPutSpaceHelper::~FilterPutSpaceHelper(0i64);
  CryptoPP::Bufferless<CryptoPP::Filter>::~Bufferless<CryptoPP::Filter>((CryptoPP::Filter *)&v1->vfptr);
}
