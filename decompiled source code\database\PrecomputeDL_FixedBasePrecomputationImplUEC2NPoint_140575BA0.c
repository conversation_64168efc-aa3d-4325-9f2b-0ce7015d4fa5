/*
 * Function: ?Precompute@?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@II@Z
 * Address: 0x140575BA0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>::Precompute(__int64 a1, __int64 a2, unsigned int a3, unsigned int a4)
{
  __int64 result; // rax@8
  __int64 *v5; // rax@9
  __int64 v6; // rax@9
  _BYTE *v7; // rax@9
  _BYTE *v8; // rax@9
  signed int i; // [sp+20h] [bp-B8h]@7
  CryptoPP::Integer v10; // [sp+28h] [bp-B0h]@6
  CryptoPP::EC2NPoint v11; // [sp+50h] [bp-88h]@9
  __int64 v12; // [sp+88h] [bp-50h]@1
  struct CryptoPP::Integer *v13; // [sp+90h] [bp-48h]@6
  struct CryptoPP::Integer *v14; // [sp+98h] [bp-40h]@6
  __int64 *v15; // [sp+A0h] [bp-38h]@9
  __int64 v16; // [sp+A8h] [bp-30h]@9
  __int64 v17; // [sp+B0h] [bp-28h]@9
  __int64 v18; // [sp+B8h] [bp-20h]@9
  _BYTE *v19; // [sp+C0h] [bp-18h]@9
  _BYTE *v20; // [sp+C8h] [bp-10h]@9
  __int64 v21; // [sp+E0h] [bp+8h]@1
  __int64 v22; // [sp+E8h] [bp+10h]@1
  unsigned int v23; // [sp+F0h] [bp+18h]@1
  unsigned int v24; // [sp+F8h] [bp+20h]@1

  v24 = a4;
  v23 = a3;
  v22 = a2;
  v21 = a1;
  v12 = -2i64;
  if ( !std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::size(a1 + 112) )
    _wassert(L"m_bases.size() > 0", L"d:\\rf project\\rf_server64\\28 crypto++\\eprecomp.cpp", 0x1Fu);
  if ( v24 > v23 )
    _wassert(L"storage <= maxExpBits", L"d:\\rf project\\rf_server64\\28 crypto++\\eprecomp.cpp", 0x20u);
  if ( v24 > 1 )
  {
    *(_DWORD *)(v21 + 64) = (v23 + v24 - 1) / v24;
    v13 = CryptoPP::Integer::Power2(&v10, *(_DWORD *)(v21 + 64));
    v14 = v13;
    CryptoPP::Integer::operator=(v21 + 72);
    CryptoPP::Integer::~Integer(&v10);
  }
  std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::resize(v21 + 112, v24);
  for ( i = 1; ; ++i )
  {
    result = v24;
    if ( i >= v24 )
      break;
    LODWORD(v5) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v22 + 24i64))(v22);
    v15 = v5;
    v16 = v21 + 72;
    v17 = v21 + 112;
    LODWORD(v6) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](
                    v21 + 112,
                    (unsigned int)(i - 1));
    v18 = *v15;
    LODWORD(v7) = (*(int (__fastcall **)(__int64 *, CryptoPP::EC2NPoint *, __int64, __int64))(v18 + 80))(
                    v15,
                    &v11,
                    v6,
                    v16);
    v19 = v7;
    v20 = v7;
    LODWORD(v8) = std::vector<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator[](
                    v21 + 112,
                    (unsigned int)i);
    CryptoPP::EC2NPoint::operator=(v8, v20);
    CryptoPP::EC2NPoint::~EC2NPoint(&v11);
  }
  return result;
}
