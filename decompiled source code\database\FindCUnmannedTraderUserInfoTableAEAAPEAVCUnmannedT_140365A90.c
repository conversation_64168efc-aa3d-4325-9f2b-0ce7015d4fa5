/*
 * Function: ?Find@CUnmannedTraderUserInfoTable@@AEAAPEAVCUnmannedTraderUserInfo@@K@Z
 * Address: 0x140365A90
 */

CUnmannedTraderUserInfo *__fastcall CUnmannedTraderUserInfoTable::Find(CUnmannedTraderUserInfoTable *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *v4; // rax@5
  __int64 v5; // [sp+0h] [bp-F8h]@1
  std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > _Right; // [sp+28h] [bp-D0h]@4
  char v7; // [sp+48h] [bp-B0h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *result; // [sp+60h] [bp-98h]@4
  char v9; // [sp+68h] [bp-90h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v10; // [sp+80h] [bp-78h]@4
  bool v11; // [sp+88h] [bp-70h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > v12; // [sp+90h] [bp-68h]@4
  CUnmannedTraderUserInfo *v13; // [sp+A8h] [bp-50h]@5
  CUnmannedTraderUserInfo *v14; // [sp+B0h] [bp-48h]@6
  __int64 v15; // [sp+B8h] [bp-40h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v16; // [sp+C0h] [bp-38h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v17; // [sp+C8h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v18; // [sp+D0h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v19; // [sp+D8h] [bp-20h]@4
  std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v20; // [sp+E0h] [bp-18h]@4
  CUnmannedTraderUserInfoTable *v21; // [sp+100h] [bp+8h]@1
  unsigned int v22; // [sp+108h] [bp+10h]@1

  v22 = dwSerial;
  v21 = this;
  v2 = &v5;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = -2i64;
  result = (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v7;
  v10 = (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v9;
  v16 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::end(
          &v21->m_veckInfo,
          (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v7);
  v17 = v16;
  v18 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::begin(&v21->m_veckInfo, v10);
  std::find<std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>,unsigned long>(
    (std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&_Right,
    v18,
    v17,
    &v22);
  v19 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::end(&v21->m_veckInfo, &v12);
  v20 = (std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)v19;
  v11 = std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v19->_Mycont,
          &_Right);
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(&v12);
  if ( v11 )
  {
    v13 = &CUnmannedTraderUserInfo::ms_kNull;
    std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>((std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&_Right);
    v4 = v13;
  }
  else
  {
    v14 = std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator*((std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&_Right);
    std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>((std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&_Right);
    v4 = v14;
  }
  return v4;
}
