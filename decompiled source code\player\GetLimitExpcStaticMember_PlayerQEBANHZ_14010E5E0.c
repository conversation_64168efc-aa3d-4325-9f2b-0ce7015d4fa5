/*
 * Function: ?GetLimitExp@cStaticMember_Player@@QEBANH@Z
 * Address: 0x14010E5E0
 */

double __fastcall cStaticMember_Player::GetLimitExp(cStaticMember_Player *this, int lv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double result; // xmm0_8@5
  __int64 v5; // [sp+0h] [bp-18h]@1
  cStaticMember_Player *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( lv < v6->_nMaxLv )
    result = v6->_pLimExp[lv];
  else
    result = 0.0;
  return result;
}
