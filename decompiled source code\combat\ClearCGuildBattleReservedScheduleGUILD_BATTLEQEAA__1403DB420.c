/*
 * Function: ?Clear@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA_NK@Z
 * Address: 0x1403DB420
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Clear(GUILD_BATTLE::CGuildBattleReservedSchedule *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int v6; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleReservedSchedule *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = dwID % 0x17;
  if ( v7->m_bUseField[dwID % 0x17] && v7->m_pkSchedule[v6] )
  {
    v7->m_bUseField[v6] = 0;
    GUILD_BATTLE::CGuildBattleSchedule::Clear(v7->m_pkSchedule[v6]);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
