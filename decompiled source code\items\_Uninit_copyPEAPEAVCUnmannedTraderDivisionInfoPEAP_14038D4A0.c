/*
 * Function: ??$_Uninit_copy@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@YAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14038D4A0
 */

CUnmannedTraderDivisionInfo **__fastcall std::_Uninit_copy<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Dest, std::allocator<CUnmannedTraderDivisionInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  __int64 v10; // [sp+20h] [bp-18h]@4
  CUnmannedTraderDivisionInfo **v11; // [sp+28h] [bp-10h]@4
  CUnmannedTraderDivisionInfo **Src; // [sp+40h] [bp+8h]@1

  Src = _First;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = _Last - Src;
  v11 = &_Dest[v10];
  if ( v10 )
    memmove_s(_Dest, 8 * v10, Src, 8 * v10);
  return v11;
}
