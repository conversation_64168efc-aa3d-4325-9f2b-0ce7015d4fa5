/*
 * Function: ??0TRC_AutoTrade@@QEAA@E@Z
 * Address: 0x1402D7B70
 */

void __fastcall TRC_AutoTrade::TRC_AutoTrade(TRC_AutoTrade *this, char byRace)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  _SYSTEMTIME SystemTime; // [sp+28h] [bp-30h]@4
  __int64 v6; // [sp+48h] [bp-10h]@4
  TRC_AutoTrade *v7; // [sp+60h] [bp+8h]@1
  char v8; // [sp+68h] [bp+10h]@1

  v8 = byRace;
  v7 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = -2i64;
  v7->m_bInit = 0;
  v7->m_bChangeTaxRate = 0;
  v7->m_fCommonTaxRate = FLOAT_0_050000001;
  v7->m_pOwnerGuild = 0i64;
  ControllerTaxRate::ControllerTaxRate(&v7->m_Controller);
  CLogFile::CLogFile(&v7->m_sysLog);
  CLogFile::CLogFile(&v7->m_serviceLog);
  _suggested_matter_change_taxrate::_suggested_matter_change_taxrate(&v7->m_suggested);
  GetLocalTime(&SystemTime);
  v7->m_byCurDay = SystemTime.wDay;
  v7->m_wCurMonth = SystemTime.wMonth;
  v7->m_wCurYear = SystemTime.wYear;
  *(_QWORD *)&v7->m_dIncomeMoney = 0i64;
  v7->m_dwTrade = 0;
  v7->m_byRace = v8;
}
