/*
 * Function: ?Push_ChargeItem@CMainThread@@QEAA_NKKKKE@Z
 * Address: 0x1401B83C0
 */

bool __fastcall CMainThread::Push_ChargeItem(CMainThread *this, unsigned int dwSerial, unsigned int dwK, unsigned int dwD, unsigned int dwU, char byType)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@8
  __int64 v10; // [sp+0h] [bp-88h]@1
  _qry_case_insertitem v11; // [sp+38h] [bp-50h]@4
  int *v12; // [sp+68h] [bp-20h]@5
  _TimeItem_fld *v13; // [sp+70h] [bp-18h]@5
  unsigned int v15; // [sp+A0h] [bp+18h]@1
  unsigned int v16; // [sp+A8h] [bp+20h]@1

  v16 = dwD;
  v15 = dwK;
  v6 = &v10;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11.byType = byType;
  v11.dwItemCodeK = dwK;
  v11.dwItemCodeD = dwD;
  v11.dwItemCodeU = dwU;
  v11.dwSerial = dwSerial;
  if ( !byType )
  {
    v12 = (int *)&v15;
    v13 = TimeItem::FindTimeRec(BYTE1(v15), HIWORD(dwK));
    if ( v13 )
      v11.dwRemainTime = v13->m_nUseTime;
    v11.lnUID = UIDGenerator::getuid(unk_1799C608C);
  }
  v8 = _qry_case_insertitem::size(&v11);
  return CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 14, (char *)&v11, v8) != 0i64;
}
