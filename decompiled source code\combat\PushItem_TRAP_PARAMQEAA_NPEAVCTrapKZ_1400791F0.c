/*
 * Function: ?PushItem@_TRAP_PARAM@@QEAA_NPEAVCTrap@@K@Z
 * Address: 0x1400791F0
 */

char __fastcall _TRAP_PARAM::PushItem(_TRAP_PARAM *this, CTrap *pTrap, unsigned int dwTrapSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _TRAP_PARAM *v8; // [sp+40h] [bp+8h]@1
  CTrap *v9; // [sp+48h] [bp+10h]@1
  unsigned int v10; // [sp+50h] [bp+18h]@1

  v10 = dwTrapSerial;
  v9 = pTrap;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 20; ++j )
  {
    if ( !_TRAP_PARAM::_param::isLoad((_TRAP_PARAM::_param *)v8 + j) )
    {
      v8->m_Item[j].pItem = v9;
      v8->m_Item[j].dwSerial = v10;
      ++v8->m_nCount;
      return 1;
    }
  }
  return 0;
}
