/*
 * Function: ?InitClass@CPlayerDB@@QEAAXXZ
 * Address: 0x14010B7B0
 */

void __fastcall CPlayerDB::InitClass(CPlayerDB *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CPlayerDB *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4->m_pClassData = v4->m_pClassHistory[0];
  for ( j = 0; j < 3; ++j )
    v4->m_pClassHistory[j] = 0i64;
}
