/*
 * Function: j_??$_Destroy_range@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@@Z
 * Address: 0x140005E98
 */

void __fastcall std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, std::allocator<CMoveMapLimitRightInfo> *_Al)
{
  std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(_First, _Last, _Al);
}
