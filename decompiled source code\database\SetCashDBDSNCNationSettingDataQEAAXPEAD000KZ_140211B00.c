/*
 * Function: ?SetCashDBDSN@CNationSettingData@@QEAAXPEAD000K@Z
 * Address: 0x140211B00
 */

void __fastcall CNationSettingData::SetCashDBDSN(CNationSettingData *this, char *szIP, char *szDBName, char *szAccount, char *szPassword, unsigned int dwPort)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CNationSettingData *v9; // [sp+30h] [bp+8h]@1
  const char *v10; // [sp+38h] [bp+10h]@1
  const char *v11; // [sp+48h] [bp+20h]@1

  v11 = szAccount;
  v10 = szIP;
  v9 = this;
  v6 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  strcpy_s<64>((char (*)[64])v9->m_szCashDBName, szDBName);
  strcpy_s<16>((char (*)[16])v9->m_szCashDBIP, v10);
  strcpy_s<64>((char (*)[64])v9->m_szCashDBID, v11);
  strcpy_s<64>((char (*)[64])v9->m_szCashDBPW, szPassword);
  v9->m_wCashDBPort = dwPort;
  CNationSettingData::SetCashDBDSNSetFlag(v9);
}
