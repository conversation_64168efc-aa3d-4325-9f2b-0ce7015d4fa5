/*
 * Function: ??$unchecked_uninitialized_copy@PEAPEAU_guild_member_refresh_data@@PEAPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@@stdext@@YAPEAPEAU_guild_member_refresh_data@@PEAPEAU1@00AEAV?$allocator@PEAU_guild_member_refresh_data@@@std@@@Z
 * Address: 0x14033FFB0
 */

_guild_member_refresh_data **__fastcall stdext::unchecked_uninitialized_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(_guild_member_refresh_data **_First, _guild_member_refresh_data **_Last, _guild_member_refresh_data **_Dest, std::allocator<_guild_member_refresh_data *> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  _guild_member_refresh_data **__formal; // [sp+50h] [bp+8h]@1
  _guild_member_refresh_data **_Lasta; // [sp+58h] [bp+10h]@1
  _guild_member_refresh_data **_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<_guild_member_refresh_data *> *v13; // [sp+68h] [bp+20h]@1

  v13 = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<_guild_member_refresh_data * *,_guild_member_refresh_data * *>(&__formal, &_Desta);
  return std::_Uninit_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(
           __formal,
           _Lasta,
           _Desta,
           v13,
           v9,
           v8);
}
