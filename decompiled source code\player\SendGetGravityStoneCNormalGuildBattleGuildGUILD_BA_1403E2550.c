/*
 * Function: ?SendGetGravityStone@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAV12@PEAVCPlayer@@H@Z
 * Address: 0x1403E2550
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleGuild *pkTakeGuild, CPlayer *pkPlayer, int iTakePortalInx)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char *v6; // rax@8
  char *v7; // rax@8
  unsigned __int16 v8; // ax@12
  __int64 v9; // [sp+0h] [bp-A8h]@1
  char pbyType; // [sp+34h] [bp-74h]@8
  char v11; // [sp+35h] [bp-73h]@8
  char Dst; // [sp+58h] [bp-50h]@8
  char Dest; // [sp+59h] [bp-4Fh]@8
  char v14; // [sp+6Ah] [bp-3Eh]@8
  int v15; // [sp+7Bh] [bp-2Dh]@8
  char v16; // [sp+7Fh] [bp-29h]@8
  int j; // [sp+84h] [bp-24h]@8
  unsigned __int16 v18[2]; // [sp+90h] [bp-18h]@12
  unsigned __int64 v19; // [sp+98h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v20; // [sp+B0h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v21; // [sp+B8h] [bp+10h]@1
  CPlayer *v22; // [sp+C0h] [bp+18h]@1
  int v23; // [sp+C8h] [bp+20h]@1

  v23 = iTakePortalInx;
  v22 = pkPlayer;
  v21 = pkTakeGuild;
  v20 = this;
  v4 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v20->m_pkGuild && pkPlayer && pkTakeGuild )
  {
    pbyType = 27;
    v11 = 72;
    memset_0(&Dst, 0, 0x28ui64);
    Dst = 0;
    v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v21);
    strcpy_0(&Dest, v6);
    v7 = CPlayerDB::GetCharNameW(&v22->m_Param);
    strcpy_0(&v14, v7);
    v15 = v23;
    v16 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(v21);
    for ( j = 0; j < 50; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v20->m_kMember[j]) )
      {
        *(_DWORD *)v18 = _guild_battle_get_gravity_stone_result_zocl::size((_guild_battle_get_gravity_stone_result_zocl *)&Dst);
        v8 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v20->m_kMember[j]);
        CNetProcess::LoadSendMsg(unk_1414F2088, v8, &pbyType, &Dst, v18[0]);
      }
    }
  }
}
