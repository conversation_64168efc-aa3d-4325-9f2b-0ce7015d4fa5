/*
 * Function: ?_Insert_n@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAXV?$_Vector_iterator@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@2@_KAEBQEAVCUnmannedTraderSortType@@@Z
 * Address: 0x140372340
 */

void __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Insert_n(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Where, unsigned __int64 _Count, CUnmannedTraderSortType *const *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-98h]@1
  CUnmannedTraderSortType *_Vala; // [sp+28h] [bp-70h]@4
  unsigned __int64 _Counta; // [sp+38h] [bp-60h]@4
  CUnmannedTraderSortType **_Ptr; // [sp+40h] [bp-58h]@13
  CUnmannedTraderSortType **v14; // [sp+48h] [bp-50h]@13
  CUnmannedTraderSortType **_Last; // [sp+50h] [bp-48h]@18
  __int64 v16; // [sp+58h] [bp-40h]@4
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v17; // [sp+60h] [bp-38h]@5
  unsigned __int64 v18; // [sp+68h] [bp-30h]@8
  unsigned __int64 v19; // [sp+70h] [bp-28h]@9
  CUnmannedTraderSortType **v20; // [sp+78h] [bp-20h]@13
  CUnmannedTraderSortType **v21; // [sp+80h] [bp-18h]@13
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v22; // [sp+A0h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v23; // [sp+A8h] [bp+10h]@1
  unsigned __int64 v24; // [sp+B0h] [bp+18h]@1
  unsigned __int64 v25; // [sp+B0h] [bp+18h]@13

  v24 = _Count;
  v23 = _Where;
  v22 = this;
  v4 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  _Vala = *_Val;
  _Counta = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::capacity(v22);
  if ( v24 )
  {
    v17 = (std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(v22);
    v6 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::max_size(v22);
    if ( v6 - (unsigned __int64)v17 < v24 )
      std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Xlen(v17);
    v7 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(v22);
    if ( _Counta >= v24 + v7 )
    {
      if ( v22->_Mylast - v23->_Myptr >= v24 )
      {
        _Last = v22->_Mylast;
        v22->_Mylast = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(
                         v22,
                         &_Last[-v24],
                         _Last,
                         v22->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(
          v23->_Myptr,
          &_Last[-v24],
          _Last);
        std::fill<CUnmannedTraderSortType * *,CUnmannedTraderSortType *>(v23->_Myptr, &v23->_Myptr[v24], &_Vala);
      }
      else
      {
        std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(
          v22,
          v23->_Myptr,
          v22->_Mylast,
          &v23->_Myptr[v24]);
        std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Ufill(
          v22,
          v22->_Mylast,
          v24 - (v22->_Mylast - v23->_Myptr),
          &_Vala);
        v22->_Mylast += v24;
        std::fill<CUnmannedTraderSortType * *,CUnmannedTraderSortType *>(v23->_Myptr, &v22->_Mylast[-v24], &_Vala);
      }
    }
    else
    {
      v18 = _Counta / 2;
      v8 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::max_size(v22);
      if ( v8 - v18 >= _Counta )
        v19 = _Counta / 2 + _Counta;
      else
        v19 = 0i64;
      _Counta = v19;
      v9 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(v22);
      if ( _Counta < v24 + v9 )
        _Counta = v24 + std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(v22);
      _Ptr = std::allocator<CUnmannedTraderSortType *>::allocate(&v22->_Alval, _Counta);
      v14 = _Ptr;
      v20 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(
              v22,
              v22->_Myfirst,
              v23->_Myptr,
              _Ptr);
      v14 = v20;
      v21 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Ufill(
              v22,
              v20,
              v24,
              &_Vala);
      v14 = v21;
      std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Umove<CUnmannedTraderSortType * *>(
        v22,
        v23->_Myptr,
        v22->_Mylast,
        v21);
      v25 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::size(v22) + v24;
      if ( v22->_Myfirst )
      {
        std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(
          v22,
          v22->_Myfirst,
          v22->_Mylast);
        std::allocator<CUnmannedTraderSortType *>::deallocate(&v22->_Alval, v22->_Myfirst, v22->_Myend - v22->_Myfirst);
      }
      v22->_Myend = &_Ptr[_Counta];
      v22->_Mylast = &_Ptr[v25];
      v22->_Myfirst = _Ptr;
    }
  }
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(v23);
}
