/*
 * Function: ?SetEquipJadeEffect@CPlayer@@QEAAXHM_N@Z
 * Address: 0x140052280
 */

void __fastcall CPlayer::SetEquipJadeEffect(CPlayer *this, int nParam, float fCurVal, bool bAdd)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // rax@22
  __int64 v7; // [sp+0h] [bp-A8h]@1
  int j; // [sp+20h] [bp-88h]@4
  char *v9; // [sp+28h] [bp-80h]@7
  unsigned int Dst; // [sp+34h] [bp-74h]@10
  char v11; // [sp+44h] [bp-64h]@10
  float v12; // [sp+48h] [bp-60h]@12
  _ItemUpgrade_fld *v13; // [sp+50h] [bp-58h]@16
  int nParamIndex; // [sp+58h] [bp-50h]@16
  _ItemUpgrade_fld *v15; // [sp+60h] [bp-48h]@23
  int k; // [sp+68h] [bp-40h]@27
  char v17; // [sp+6Ch] [bp-3Ch]@30
  int v18; // [sp+70h] [bp-38h]@31
  int l; // [sp+74h] [bp-34h]@31
  char v20; // [sp+78h] [bp-30h]@33
  _ItemUpgrade_fld *v21; // [sp+80h] [bp-28h]@36
  float v22; // [sp+88h] [bp-20h]@40
  float v23; // [sp+8Ch] [bp-1Ch]@40
  int m; // [sp+90h] [bp-18h]@47
  int v25; // [sp+94h] [bp-14h]@34
  char v26; // [sp+98h] [bp-10h]@40
  CPlayer *v27; // [sp+B0h] [bp+8h]@1
  bool v28; // [sp+C8h] [bp+20h]@1

  v28 = bAdd;
  v27 = this;
  v4 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < 7; ++j )
  {
    v9 = &v27->m_Param.m_pStoragePtr[1]->m_pStorageList[j].m_bLoad;
    if ( v9 && *v9 )
    {
      memcpy_0(&Dst, v9 + 13, 4ui64);
      v11 = GetItemUpgedLv(Dst);
      if ( v11 && GetDefItemUpgSocketNum((unsigned __int8)v9[1], *(_WORD *)(v9 + 3)) )
      {
        for ( k = 0; k < (unsigned __int8)v11; ++k )
        {
          v17 = GetTalikFromSocket(Dst, k);
          if ( v17 != 15 )
          {
            v18 = 1;
            for ( l = k + 1; l < (unsigned __int8)v11; ++l )
            {
              v20 = GetTalikFromSocket(Dst, l);
              if ( (unsigned __int8)v17 == (unsigned __int8)v20 )
              {
                ++v18;
                v25 = 15;
                Dst |= 15 << 4 * l;
              }
            }
            v21 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, (unsigned __int8)v17);
            if ( v21 )
            {
              if ( v18 >= 1 && v18 <= 7 )
              {
                v22 = *(&v21->m_fUp1 + v18 - 1);
                v23 = 0.0;
                v26 = v17;
                if ( v17 )
                {
                  if ( v26 == 5 && v9[1] != 6 )
                  {
                    if ( fCurVal > 0.0 && v18 < (signed int)ffloor(fCurVal) )
                      v23 = *(&v21->m_fUp1 + (signed int)ffloor(fCurVal) - 1) - v22;
                    _effect_parameter::SetEff_Rate(&v27->m_EP, 6, v23, v28);
                    v27->m_fTalik_DefencePoint = v27->m_fTalik_DefencePoint + v23;
                  }
                }
                else if ( v9[1] == 6 )
                {
                  if ( fCurVal > 0.0 && v18 < (signed int)ffloor(fCurVal) )
                    v23 = *(&v21->m_fUp1 + (signed int)ffloor(fCurVal) - 1) - v22;
                  for ( m = 0; m < 2; ++m )
                  {
                    _effect_parameter::SetEff_Rate(&v27->m_EP, m, v23, v28);
                    _effect_parameter::SetEff_Rate(&v27->m_EP, m + 2, v23, v28);
                  }
                  _effect_parameter::SetEff_Rate(&v27->m_EP, 4, v23, v28);
                  _effect_parameter::SetEff_Rate(&v27->m_EP, 29, v23, v28);
                }
              }
            }
          }
        }
      }
      else
      {
        v12 = 0.0;
        if ( fCurVal <= 0.0
          || fCurVal >= 6.0
          || v9[1] != 6
          || (signed int)(unsigned __int8)GetItemGrade((unsigned __int8)v9[1], *(_WORD *)(v9 + 3)) >= 3 )
        {
          if ( fCurVal > 0.0 && fCurVal < 5.0 )
          {
            v6 = (unsigned __int8)v9[1];
            if ( (signed int)(unsigned __int8)v9[1] <= 5 )
            {
              v15 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, 5u);
              v12 = *(&v15->m_fUp1 + (signed int)ffloor(fCurVal) - 1);
              _effect_parameter::SetEff_Rate(&v27->m_EP, 6, v12, v28);
              if ( v28 )
                v27->m_fTalik_DefencePoint = v27->m_fTalik_DefencePoint + v12;
              else
                v27->m_fTalik_DefencePoint = v27->m_fTalik_DefencePoint - v12;
            }
          }
        }
        else
        {
          v13 = CItemUpgradeTable::GetRecord(&stru_1799C69D8, 0);
          v12 = *(&v13->m_fUp1 + (signed int)ffloor(fCurVal) - 1);
          for ( nParamIndex = 0; nParamIndex < 2; ++nParamIndex )
          {
            _effect_parameter::SetEff_Rate(&v27->m_EP, nParamIndex, v12, v28);
            _effect_parameter::SetEff_Rate(&v27->m_EP, nParamIndex + 2, v12, v28);
          }
          _effect_parameter::SetEff_Rate(&v27->m_EP, 4, v12, v28);
          _effect_parameter::SetEff_Rate(&v27->m_EP, 29, v12, v28);
        }
      }
    }
  }
}
