/*
 * Function: ??0?$_<PERSON>t@PEAVTRC_AutoTrade@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140390260
 */

void __fastcall std::_<PERSON>t<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *this, std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_<PERSON>t<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Iterator_base::_Iterator_base((std::_Iterator_base *)&v5->_Mycont, (std::_Iterator_base *)&__that->_Mycont);
}
