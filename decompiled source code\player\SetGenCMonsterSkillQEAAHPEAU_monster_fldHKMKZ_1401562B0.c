/*
 * Function: ?SetGen@CMonsterSkill@@QEAAHPEAU_monster_fld@@HKMK@Z
 * Address: 0x1401562B0
 */

signed __int64 __fastcall CMonsterSkill::SetGen(CMonsterSkill *this, _monster_fld *pMonsterFld, int nSFLv, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v9; // [sp+0h] [bp-38h]@1
  int v10; // [sp+20h] [bp-18h]@4
  float v11; // [sp+24h] [bp-14h]@7
  CMonsterSkill *v12; // [sp+40h] [bp+8h]@1
  _monster_fld *v13; // [sp+48h] [bp+10h]@1
  int v14; // [sp+50h] [bp+18h]@1
  unsigned int v15; // [sp+58h] [bp+20h]@1

  v15 = dwDelayTime;
  v14 = nSFLv;
  v13 = pMonsterFld;
  v12 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  CMonsterSkill::Init(v12);
  v12->m_nSFCode = 0;
  v12->m_wSFIndex = 0;
  v12->m_pSF_Fld = 0i64;
  v12->m_dwDelayTime = v15;
  v12->m_fAttackDist = fAttackDist;
  v12->m_dwCastDelay = dwCastDelay;
  v12->m_nSFLv = v14;
  v12->m_Element = v13->m_nProperty;
  v12->m_StdDmg = (signed int)ffloor(v13->m_fAttFcStd);
  v10 = (signed int)ffloor((float)v12->m_StdDmg * 0.89999998);
  if ( v12->m_StdDmg - v10 <= 0 )
    v12->m_MinDmg = 0;
  else
    v12->m_MinDmg = rand() % (v12->m_StdDmg - v10) + v10;
  v12->m_MaxDmg = 2 * v12->m_StdDmg - v12->m_MinDmg;
  v11 = (float)(v12->m_MaxDmg + 125) / (float)(v12->m_MaxDmg + 50);
  v12->m_MinProb = (signed int)ffloor(v13->m_fMinAFSelProb);
  v12->m_MaxProb = (signed int)ffloor(v13->m_fMaxAFSelProb);
  v12->m_UseType = 0;
  v12->m_nMotive = 0;
  v12->m_nMotivevalue = -1;
  v12->m_nCaseType = 0;
  v12->m_bExit = 1;
  if ( v12->m_bExit )
  {
    result = 1i64;
  }
  else
  {
    CMonsterSkill::Init(v12);
    result = 0i64;
  }
  return result;
}
