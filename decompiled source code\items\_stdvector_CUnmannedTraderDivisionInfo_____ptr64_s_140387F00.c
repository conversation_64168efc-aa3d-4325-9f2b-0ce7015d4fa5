/*
 * Function: _std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::clear_::_1_::dtor$0
 * Address: 0x140387F00
 */

void __fastcall std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::clear_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::~_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(*(std::_Vector_iterator<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > **)(a2 + 56));
}
