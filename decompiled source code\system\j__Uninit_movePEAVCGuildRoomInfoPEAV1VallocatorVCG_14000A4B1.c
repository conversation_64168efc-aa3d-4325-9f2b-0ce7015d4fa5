/*
 * Function: j_??$_Uninit_move@PEAVCGuildRoomInfo@@PEAV1@V?$allocator@VCGuildRoomInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCGuildRoomInfo@@PEAV1@00AEAV?$allocator@VCGuildRoomInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000A4B1
 */

CGuildRoomInfo *__fastcall std::_Uninit_move<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>,std::_Undefined_move_tag>(CGuildRoomInfo *_First, CGuildRoomInfo *_Last, CGuildRoomInfo *_Dest, std::allocator<CGuildRoomInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
