/*
 * Function: ?IsCashItem@@YAHEK@Z
 * Address: 0x14003F600
 */

__int64 __fastcall IsCashItem(char byTblCode, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-A8h]@1
  CRecordData *v6; // [sp+20h] [bp-88h]@4
  _base_fld *v7; // [sp+28h] [bp-80h]@6
  _base_fld *v8; // [sp+30h] [bp-78h]@9
  _base_fld *v9; // [sp+38h] [bp-70h]@12
  _base_fld *v10; // [sp+40h] [bp-68h]@15
  _base_fld *v11; // [sp+48h] [bp-60h]@18
  _base_fld *v12; // [sp+50h] [bp-58h]@21
  _base_fld *v13; // [sp+58h] [bp-50h]@24
  _base_fld *v14; // [sp+60h] [bp-48h]@27
  _base_fld *v15; // [sp+68h] [bp-40h]@30
  _base_fld *v16; // [sp+70h] [bp-38h]@33
  _base_fld *v17; // [sp+78h] [bp-30h]@36
  _base_fld *v18; // [sp+80h] [bp-28h]@39
  _base_fld *v19; // [sp+88h] [bp-20h]@42
  int v20; // [sp+90h] [bp-18h]@4
  char v21; // [sp+B0h] [bp+8h]@1

  v21 = byTblCode;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[(unsigned __int8)v21];
  v20 = (unsigned __int8)v21;
  switch ( v21 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
      result = 0i64;
      break;
    case 0x14:
      v7 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v7 )
        goto LABEL_44;
      result = *(_DWORD *)&v7[5].m_strCode[44];
      break;
    case 0x1F:
      v8 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v8 )
        goto LABEL_44;
      result = *(_DWORD *)&v8[5].m_strCode[56];
      break;
    case 0x21:
      v9 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v9 )
        goto LABEL_44;
      result = *(_DWORD *)&v9[7].m_strCode[32];
      break;
    case 0xA:
      v10 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v10 )
        goto LABEL_44;
      result = *(_DWORD *)&v10[9].m_strCode[12];
      break;
    case 8:
      v11 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v11 )
        goto LABEL_44;
      result = *(_DWORD *)&v11[6].m_strCode[48];
      break;
    case 9:
      v12 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v12 )
        goto LABEL_44;
      result = *(_DWORD *)&v12[6].m_strCode[48];
      break;
    case 0xD:
      v13 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v13 )
        goto LABEL_44;
      result = *(_DWORD *)&v13[9].m_strCode[36];
      break;
    case 0x10:
      v14 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v14 )
        goto LABEL_44;
      result = *(_DWORD *)&v14[5].m_strCode[48];
      break;
    case 0x12:
      v15 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v15 )
        goto LABEL_44;
      result = *(_DWORD *)&v15[8].m_strCode[8];
      break;
    case 0x16:
      v16 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v16 )
        goto LABEL_44;
      result = *(_DWORD *)&v16[7].m_strCode[44];
      break;
    case 0x1E:
      v17 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v17 )
        goto LABEL_44;
      result = *(_DWORD *)&v17[5].m_strCode[60];
      break;
    case 0x23:
      v18 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v18 )
        goto LABEL_44;
      result = *(_DWORD *)&v18[7].m_strCode[8];
      break;
    case 0x24:
      v19 = CRecordData::GetRecord(v6, dwIndex);
      if ( !v19 )
        goto LABEL_44;
      result = *(_DWORD *)&v19[6].m_strCode[0];
      break;
    default:
LABEL_44:
      result = 0i64;
      break;
  }
  return result;
}
