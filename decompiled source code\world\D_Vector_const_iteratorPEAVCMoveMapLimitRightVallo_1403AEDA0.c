/*
 * Function: ??D?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBAAEBQEAVCMoveMapLimitRight@@XZ
 * Address: 0x1403AEDA0
 */

CMoveMapLimitRight **__fastcall std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator*(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  return this->_Myptr;
}
