/*
 * Function: ?GetUseConsumeItem@CPlayer@@QEAA_NPEAU_consume_item_list@@PEAGPEAPEAU_db_con@_STORAGE_LIST@@PEAHPEA_N@Z
 * Address: 0x140067D50
 */

char __fastcall CPlayer::GetUseConsumeItem(CPlayer *this, _consume_item_list *pConsumeList, unsigned __int16 *pItemSerials, _STORAGE_LIST::_db_con **ppConsumeItems, int *pnConsume, bool *pbOverLap)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v9; // [sp+0h] [bp-48h]@1
  _STORAGE_LIST::_db_con *v10; // [sp+20h] [bp-28h]@10
  int j; // [sp+28h] [bp-20h]@10
  int v12; // [sp+2Ch] [bp-1Ch]@12
  _base_fld *v13; // [sp+30h] [bp-18h]@18
  CPlayer *v14; // [sp+50h] [bp+8h]@1
  _consume_item_list *v15; // [sp+58h] [bp+10h]@1
  unsigned __int16 *v16; // [sp+60h] [bp+18h]@1
  _STORAGE_LIST::_db_con **v17; // [sp+68h] [bp+20h]@1

  v17 = ppConsumeItems;
  v16 = pItemSerials;
  v15 = pConsumeList;
  v14 = this;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( pConsumeList && pItemSerials && ppConsumeItems && pnConsume && pbOverLap )
  {
    v10 = 0i64;
    for ( j = 0; j < 3; ++j )
    {
      v12 = v15[j].m_nNeedItemCount;
      if ( strcmp_0(v15[j].m_itmNeedItemCode, "-1") && v12 >= 0 )
      {
        if ( v16[j] == 255 )
          return 0;
        v10 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v14->m_Param.m_dbInven.m_nListNum, v16[j]);
        if ( !v10 )
          return 0;
        v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v10->m_byTableCode, v10->m_wItemIndex);
        if ( strcmp_0(v15[j].m_itmNeedItemCode, v13->m_strCode) )
          return 0;
        if ( v12 > 0 && IsOverLapItem(v10->m_byTableCode) )
        {
          if ( v10->m_dwDur < v12 )
            return 0;
          pbOverLap[j] = 1;
        }
        v17[j] = v10;
        pnConsume[j] = v12;
      }
      v10 = 0i64;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
