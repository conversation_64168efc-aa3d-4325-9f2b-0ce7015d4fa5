#pragma once

#include <string>
#include <memory>
#include <vector>
#include <cstdint>
#include <unordered_map>
#include <functional>

namespace NexusProtection::World {

    // Forward declarations
    class ReactObj;
    class ReactArea;

    /**
     * @brief Respawn Monster Configuration
     * 
     * Represents configuration and reactive components for monster respawn operations.
     * This class manages reactive objects, areas, event flags, and define codes for
     * coordinated monster respawning in the game world.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - 0__respawn_monsterQEAAXZ_14027A450.c (constructor)
     * - j_0__respawn_monsterQEAAXZ_14000A097.c (jump table)
     */
    class RespawnMonster {
    public:
        // Constructor and destructor
        RespawnMonster();
        explicit RespawnMonster(const std::string& defineCode, bool callEvent = false);
        ~RespawnMonster() = default;

        // Copy and move semantics
        RespawnMonster(const RespawnMonster& other);
        RespawnMonster& operator=(const RespawnMonster& other);
        RespawnMonster(RespawnMonster&& other) noexcept;
        RespawnMonster& operator=(RespawnMonster&& other) noexcept;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsValid() const;

        // React object management
        ReactObj& GetReactObj() { return m_reactObj; }
        const ReactObj& GetReactObj() const { return m_reactObj; }
        void SetReactObj(const ReactObj& reactObj) { m_reactObj = reactObj; }

        // React area management
        ReactArea& GetReactArea() { return m_reactArea; }
        const ReactArea& GetReactArea() const { return m_reactArea; }
        void SetReactArea(const ReactArea& reactArea) { m_reactArea = reactArea; }

        // Event flag management
        bool GetCallEvent() const { return m_callEvent; }
        void SetCallEvent(bool callEvent) { m_callEvent = callEvent; }
        void EnableEvents() { m_callEvent = true; }
        void DisableEvents() { m_callEvent = false; }

        // Define code management
        void SetDefineCode(const std::string& code);
        const std::string& GetDefineCode() const { return m_defineCode; }
        bool HasDefineCode() const { return !m_defineCode.empty(); }

        // Respawn operations
        void ProcessMonsterRespawn(const std::string& monsterType, float x, float y, float z = 0.0f);
        void HandleRespawnEvent(const std::string& event, const std::string& data);
        void TriggerRespawnSequence();
        void CancelRespawnSequence();

        // Configuration management
        void ConfigureReactiveComponents(bool enableObj, bool enableArea);
        bool AreReactiveComponentsActive() const;
        void SetRespawnConfiguration(const std::string& defineCode, bool callEvent, 
                                   bool enableObj = true, bool enableArea = true);

        // Respawn timing and conditions
        void SetRespawnDelay(float delaySeconds);
        float GetRespawnDelay() const { return m_respawnDelay; }
        void SetRespawnCondition(const std::string& condition);
        const std::string& GetRespawnCondition() const { return m_respawnCondition; }

        // State management
        bool IsRespawnActive() const { return m_isRespawnActive; }
        void SetRespawnActive(bool active) { m_isRespawnActive = active; }
        bool IsRespawnPending() const { return m_isRespawnPending; }

        // Utility methods
        std::string ToString() const;
        size_t GetMemoryUsage() const;

        // Legacy C interface compatibility
        const char* GetDefineCodeCStr() const;
        void SetDefineCodeFromCStr(const char* code);
        bool GetCallEventLegacy() const { return m_callEvent; }
        void SetCallEventLegacy(bool callEvent) { m_callEvent = callEvent; }
        void* GetReactObjPtr() { return &m_reactObj; }
        void* GetReactAreaPtr() { return &m_reactArea; }

        // Validation
        bool ValidateDefineCode() const;
        bool ValidateRespawnConfiguration() const;

        // Event handling
        void RegisterRespawnEventHandler(const std::string& event, std::function<void(const RespawnMonster&)> handler);
        void TriggerRespawnEvent(const std::string& event);

    private:
        // Member variables
        ReactObj m_reactObj;                    // Reactive object component (originally ReactObj)
        ReactArea m_reactArea;                  // Reactive area component (originally ReactArea)
        bool m_callEvent{false};                // Event call flag (originally bCallEvent)
        std::string m_defineCode;               // Define code (originally pszDefineCode)
        
        // Extended functionality
        bool m_isInitialized{false};
        bool m_isRespawnActive{false};
        bool m_isRespawnPending{false};
        float m_respawnDelay{0.0f};
        std::string m_respawnCondition;
        
        // Event handling
        std::unordered_map<std::string, std::function<void(const RespawnMonster&)>> m_eventHandlers;
        
        // Configuration limits
        static constexpr size_t MAX_DEFINE_CODE_LENGTH = 256;
        static constexpr size_t MAX_RESPAWN_CONDITION_LENGTH = 512;
        static constexpr float MAX_RESPAWN_DELAY = 3600.0f; // 1 hour max
        
        // Internal methods
        void InitializeComponents();
        void ResetComponents();
        void ValidateAndTruncate();
        bool IsValidString(const std::string& str, size_t maxLength) const;
        void NotifyRespawnStateChange();
        void ProcessRespawnLogic();
    };

    /**
     * @brief Respawn Monster Factory
     * 
     * Factory class for creating RespawnMonster instances with proper configuration.
     */
    class RespawnMonsterFactory {
    public:
        static std::unique_ptr<RespawnMonster> CreateRespawnMonster();
        static std::unique_ptr<RespawnMonster> CreateRespawnMonster(const std::string& defineCode);
        static std::unique_ptr<RespawnMonster> CreateRespawnMonster(const std::string& defineCode, 
                                                                   bool callEvent);
        
        // Predefined respawn types
        static std::unique_ptr<RespawnMonster> CreateTimedRespawn(const std::string& defineCode, 
                                                                 float delaySeconds);
        static std::unique_ptr<RespawnMonster> CreateEventRespawn(const std::string& defineCode, 
                                                                 const std::string& condition);
        static std::unique_ptr<RespawnMonster> CreateAreaRespawn(const std::string& defineCode, 
                                                               float x, float y, float width, float height);
        
        // Batch creation
        static std::vector<std::unique_ptr<RespawnMonster>> CreateRespawnMonsters(
            const std::vector<std::pair<std::string, bool>>& configurations);
    };

    /**
     * @brief Respawn Monster Manager
     * 
     * Manages multiple respawn monster configurations and their interactions.
     */
    class RespawnMonsterManager {
    public:
        RespawnMonsterManager();
        ~RespawnMonsterManager() = default;

        // Respawn management
        void AddRespawnMonster(std::unique_ptr<RespawnMonster> respawnMonster);
        void RemoveRespawnMonster(const std::string& defineCode);
        std::shared_ptr<RespawnMonster> GetRespawnMonster(const std::string& defineCode) const;
        const std::vector<std::shared_ptr<RespawnMonster>>& GetAllRespawnMonsters() const { return m_respawnMonsters; }

        // Batch operations
        void ActivateAllRespawns();
        void DeactivateAllRespawns();
        void UpdateAllRespawns(float deltaTime);
        void ProcessAllRespawnEvents();

        // Statistics
        size_t GetTotalRespawnCount() const { return m_respawnMonsters.size(); }
        size_t GetActiveRespawnCount() const;
        size_t GetPendingRespawnCount() const;

        // Configuration
        void SetGlobalRespawnDelay(float delaySeconds);
        float GetGlobalRespawnDelay() const { return m_globalRespawnDelay; }

    private:
        std::vector<std::shared_ptr<RespawnMonster>> m_respawnMonsters;
        std::unordered_map<std::string, size_t> m_defineCodeToIndex;
        float m_globalRespawnDelay{30.0f}; // Default 30 seconds
    };

    /**
     * @brief Respawn Monster Utilities
     * 
     * Utility functions for RespawnMonster management and operations.
     */
    namespace RespawnMonsterUtils {
        // Validation utilities
        bool ValidateRespawnMonster(const RespawnMonster& respawnMonster);
        bool ValidateDefineCode(const std::string& code);
        bool ValidateRespawnCondition(const std::string& condition);
        
        // String utilities
        std::string SanitizeDefineCode(const std::string& code);
        std::string SanitizeRespawnCondition(const std::string& condition);
        std::string GenerateUniqueDefineCode(const std::string& baseName);
        
        // Memory utilities
        size_t CalculateMemoryFootprint(const RespawnMonster& respawnMonster);
        
        // Configuration utilities
        void ConfigureDefaultRespawn(RespawnMonster& respawnMonster);
        void OptimizeRespawnConfiguration(RespawnMonster& respawnMonster);
        
        // Conversion utilities
        std::string RespawnMonsterToJson(const RespawnMonster& respawnMonster);
        std::unique_ptr<RespawnMonster> RespawnMonsterFromJson(const std::string& json);
        
        // Respawn analysis
        std::vector<std::string> AnalyzeRespawnConfiguration(const RespawnMonster& respawnMonster);
        float CalculateRespawnEfficiency(const RespawnMonster& respawnMonster);
        std::string GetRespawnStatusSummary(const RespawnMonster& respawnMonster);
    }

    // Legacy C interface
    extern "C" {
        // Legacy structure for compatibility
        struct _respawn_monster {
            void* ReactObj;               // Pointer to reactive object
            void* ReactArea;              // Pointer to reactive area
            bool bCallEvent;              // Event call flag
            char* pszDefineCode;          // Define code pointer
            char padding[32];             // Additional padding for original structure size
        };

        // Legacy function declarations
        void __respawn_monster_Constructor(_respawn_monster* this_ptr);
        void __respawn_monster_Destructor(_respawn_monster* this_ptr);
        
        // Legacy utility functions
        void __respawn_monster_SetDefineCode(_respawn_monster* this_ptr, const char* code);
        void __respawn_monster_SetCallEvent(_respawn_monster* this_ptr, bool callEvent);
        const char* __respawn_monster_GetDefineCode(_respawn_monster* this_ptr);
        bool __respawn_monster_GetCallEvent(_respawn_monster* this_ptr);
        void* __respawn_monster_GetReactObj(_respawn_monster* this_ptr);
        void* __respawn_monster_GetReactArea(_respawn_monster* this_ptr);
        void __respawn_monster_Initialize(_respawn_monster* this_ptr);
        void __respawn_monster_Reset(_respawn_monster* this_ptr);
    }

} // namespace NexusProtection::World

// Legacy global compatibility
extern NexusProtection::World::RespawnMonster* g_pRespawnMonster;
