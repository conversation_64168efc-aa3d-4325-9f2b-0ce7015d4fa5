/*
 * Function: ??$_Destroy_range@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@YAXPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@0AEAV?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@0@@Z
 * Address: 0x14059E260
 */

int __fastcall std::_Destroy_range<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(__int64 a1, __int64 a2, __int64 a3)
{
  unsigned __int8 v3; // ST20_1@1
  __int64 v5; // [sp+40h] [bp+8h]@1
  __int64 v6; // [sp+48h] [bp+10h]@1
  __int64 v7; // [sp+50h] [bp+18h]@1

  v7 = a3;
  v6 = a2;
  v5 = a1;
  v3 = std::_Ptr_cat<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(
         &v5,
         &v6);
  return std::_Destroy_range<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(
           v5,
           v6,
           v7,
           v3);
}
