/*
 * Function: j_??D?$_Vector_const_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEBAAEBVCUnmannedTraderGroupDivisionVersionInfo@@XZ
 * Address: 0x14000810C
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator*(std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this)
{
  return std::_Vector_const_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator*(this);
}
