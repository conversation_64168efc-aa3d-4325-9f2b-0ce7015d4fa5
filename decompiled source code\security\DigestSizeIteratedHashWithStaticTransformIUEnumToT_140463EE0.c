/*
 * Function: ?DigestSize@?$IteratedHashWithStaticTransform@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@$0BE@VSHA1@2@$0A@@CryptoPP@@UEBAIXZ
 * Address: 0x140463EE0
 */

signed __int64 __fastcall CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>::DigestSize(CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0> *this)
{
  return 20i64;
}
