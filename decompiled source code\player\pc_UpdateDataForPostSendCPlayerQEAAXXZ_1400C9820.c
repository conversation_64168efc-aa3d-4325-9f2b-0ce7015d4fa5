/*
 * Function: ?pc_UpdateDataForPostSend@CPlayer@@QEAAXXZ
 * Address: 0x1400C9820
 */

void __fastcall CPlayer::pc_UpdateDataForPostSend(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-68h]@1
  _qry_case_update_data_for_post_send v5; // [sp+38h] [bp-30h]@4
  CPlayer *v6; // [sp+70h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _qry_case_update_data_for_post_send::_qry_case_update_data_for_post_send(&v5);
  v5.dwSerial = v6->m_pUserDB->m_dwSerial;
  v5.dwGlod = CPlayerDB::GetGold(&v6->m_Param);
  v5.pNewData = &v6->m_pUserDB->m_AvatorData;
  v5.pOldData = &v6->m_pUserDB->m_AvatorData_bk;
  v3 = _qry_case_update_data_for_post_send::size(&v5);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -81, (char *)&v5, v3);
}
