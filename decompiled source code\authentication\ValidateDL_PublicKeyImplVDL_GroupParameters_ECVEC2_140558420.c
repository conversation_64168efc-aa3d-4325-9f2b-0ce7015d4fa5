/*
 * Function: ?Validate@?$DL_PublicKeyImpl@V?$DL_GroupParameters_EC@VEC2N@CryptoPP@@@CryptoPP@@@CryptoPP@@UEBA_NAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x140558420
 */

__int64 __fastcall CryptoPP::DL_PublicKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::EC2N>>::Validate(__int64 a1, __int64 a2, unsigned int a3)
{
  __int64 v3; // rax@1
  __int64 v4; // rax@2
  __int64 v5; // ST40_8@2
  __int64 v6; // rax@2
  __int64 v7; // ST50_8@2
  __int64 v8; // rax@2
  bool v10; // [sp+60h] [bp-18h]@3
  __int64 v11; // [sp+80h] [bp+8h]@1
  __int64 v12; // [sp+88h] [bp+10h]@1
  unsigned int v13; // [sp+90h] [bp+18h]@1

  v13 = a3;
  v12 = a2;
  v11 = a1;
  LODWORD(v3) = (**(int (__fastcall ***)(_QWORD))(a1 - 624))(a1 - 624);
  v10 = (unsigned __int8)(*(int (__fastcall **)(signed __int64, __int64, _QWORD))(*(_QWORD *)(v3
                                                                                            + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64)
                                                                                            + 8)
                                                                                + 24i64))(
                           v3 + *(_DWORD *)(*(_QWORD *)(v3 + 8) + 4i64) + 8,
                           v12,
                           v13)
     && (LODWORD(v4) = (**(int (__fastcall ***)(_QWORD))(v11 - 624))(v11 - 624),
         v5 = v4,
         LODWORD(v6) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v11 - 624) + 48i64))(v11 - 624),
         v7 = v6,
         LODWORD(v8) = (*(int (__fastcall **)(signed __int64))(*(_QWORD *)(v11 - 624) + 16i64))(v11 - 624),
         (unsigned __int8)(*(int (__fastcall **)(__int64, _QWORD, __int64, __int64))(*(_QWORD *)v5 + 136i64))(
                            v5,
                            v13,
                            v8,
                            v7));
  return v10;
}
