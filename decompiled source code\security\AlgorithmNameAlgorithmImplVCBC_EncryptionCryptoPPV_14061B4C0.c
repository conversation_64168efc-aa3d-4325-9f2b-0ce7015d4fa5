/*
 * Function: ?AlgorithmName@?$AlgorithmImpl@VCBC_Encryption@CryptoPP@@V?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VBase@DES@CryptoPP@@@CryptoPP@@VCBC_Encryption@2@@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x14061B4C0
 */

__int64 __fastcall CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>>::AlgorithmName(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+48h] [bp+10h]@1

  v3 = a2;
  CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::DES::Base>,CryptoPP::CBC_Encryption>::StaticAlgorithmName(a2);
  return v3;
}
