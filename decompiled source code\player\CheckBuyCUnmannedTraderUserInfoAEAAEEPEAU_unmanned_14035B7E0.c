/*
 * Function: ?CheckBuy@CUnmannedTraderUserInfo@@AEAAEEPEAU_unmannedtrader_buy_item_request_clzo@@AEAPEAVCPlayer@@PEAVCLogFile@@@Z
 * Address: 0x14035B7E0
 */

char __fastcall CUnmannedTraderUserInfo::CheckBuy(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_buy_item_request_clzo *pRequest, CPlayer **pkBuyer, CLogFile *pkLogger)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CUnmannedTraderGroupItemInfoTable *v8; // rax@13
  __int64 v9; // [sp+0h] [bp-48h]@1
  unsigned int dwVer; // [sp+24h] [bp-24h]@13
  CUnmannedTraderUserInfo *v11; // [sp+50h] [bp+8h]@1
  _unmannedtrader_buy_item_request_clzo *v12; // [sp+60h] [bp+18h]@1
  CPlayer **v13; // [sp+68h] [bp+20h]@1

  v13 = pkBuyer;
  v12 = pRequest;
  v11 = this;
  v5 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( (signed int)v11->m_wInx < 2532 )
  {
    *pkBuyer = &g_Player + v11->m_wInx;
    if ( (*pkBuyer)->m_dwObjSerial == v11->m_dwUserSerial )
    {
      if ( CUnmannedTraderRequestLimiter::IsEmpty(&v11->m_kRequestState) )
      {
        if ( v12->bUseNpcLink || IsBeNearStore(*v13, -1) )
        {
          dwVer = 0;
          v8 = CUnmannedTraderGroupItemInfoTable::Instance();
          if ( CUnmannedTraderGroupItemInfoTable::GetVersion(v8, v12->byDivision, v12->byClass, &dwVer) )
          {
            if ( dwVer == v12->dwVer )
              result = 0;
            else
              result = 52;
          }
          else
          {
            result = 51;
          }
        }
        else
        {
          result = 2;
        }
      }
      else
      {
        result = 95;
      }
    }
    else
    {
      result = 99;
    }
  }
  else
  {
    result = 99;
  }
  return result;
}
