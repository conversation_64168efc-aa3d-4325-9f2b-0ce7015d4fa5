/*
 * Function: ?Init@CBossMonsterScheduleSystem@@QEAA_NPEAVCMapOperation@@@Z
 * Address: 0x140419B60
 */

bool __fastcall CBossMonsterScheduleSystem::Init(CBossMonsterScheduleSystem *this, CMapOperation *pMapOper)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  __int64 v6; // [sp+20h] [bp-18h]@6
  CBossMonsterScheduleSystem *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_pCurTBL )
  {
    result = 0;
  }
  else
  {
    v7->m_pMapOper = pMapOper;
    v7->m_bRespawnMonster = 0;
    v6 = 0i64;
    v7->m_pCurTBL = CBossMonsterScheduleSystem::MakeTBL(v7, v7->m_pMapOper);
    if ( CBossMonsterScheduleSystem::CreateTaskPool(v7) )
      result = CBossMonsterScheduleSystem::CreateWorkerThread(v7) != 0;
    else
      result = 0;
  }
  return result;
}
