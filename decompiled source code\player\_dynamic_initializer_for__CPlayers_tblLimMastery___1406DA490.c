/*
 * Function: _dynamic_initializer_for__CPlayer::s_tblLimMastery__
 * Address: 0x1406DA490
 */

__int64 dynamic_initializer_for__CPlayer::s_tblLimMastery__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v0 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector constructor iterator'(
    &CPlayer::s_tblLimMastery,
    0xB0ui64,
    12,
    (void (__cdecl *)(void *))CRecordData::CRecordData,
    (void (__cdecl *)(void *))CRecordData::~CRecordData);
  return atexit(dynamic_atexit_destructor_for__CPlayer::s_tblLimMastery__);
}
