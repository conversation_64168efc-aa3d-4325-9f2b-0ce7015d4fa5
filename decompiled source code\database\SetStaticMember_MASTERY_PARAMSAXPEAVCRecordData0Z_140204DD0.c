/*
 * Function: ?SetStaticMember@_MASTERY_PARAM@@SAXPEAVCRecordData@@0@Z
 * Address: 0x140204DD0
 */

void __fastcall _MASTERY_PARAM::SetStaticMember(CRecordData *pSkillData, CRecordData *pForceData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRecordData *pSkillDataa; // [sp+30h] [bp+8h]@1

  pSkillDataa = pSkillData;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _MASTERY_PARAM::s_pSkillData = pSkillDataa;
  _MASTERY_PARAM::s_pForceData = pForceData;
  InitMasteryFormula(pSkillDataa, pForceData);
}
