# Complete Player Equipment System Refactoring

## Overview

This document describes the complete refactoring of the `pc_EquipPart` function from the decompiled source code into a modern, type-safe C++ implementation. The refactoring includes comprehensive item code mapping, error handling, and equipment validation.

## Source Analysis

### Original Function: pc_EquipPart
- **Address**: 0x1400AD960
- **Complexity**: HIGH
- **Lines of Code**: 158 lines
- **Key Features**: Equipment validation, storage management, error handling

### Key Dependencies Analyzed
1. **GetItemTableCode** (0x1400362B0) - Complete item prefix mapping
2. **SendMsg_EquipPartResult** (0x1400D7D30) - Error code communication
3. **Storage management functions** - Inventory and equipment operations

## Complete Item Code System

### Equipment Items (Table Codes 0-7)
Based on `GetItemTableCode` function analysis:

| Table Code | Prefix | Equipment Slot | Description |
|------------|--------|----------------|-------------|
| 0 | "iu" | UpperArmor | Upper body armor/clothing |
| 1 | "il" | LowerArmor | Lower body armor/clothing |
| 2 | "ig" | Gloves | Gloves/hand equipment |
| 3 | "is" | Shoes | Shoes/foot equipment |
| 4 | "ih" | Helmet | Helmet/head equipment |
| 5 | "id" | Shield | Shield/defensive equipment |
| 6 | "iw" | Weapon | Weapon/offensive equipment |
| 7 | "ik" | Cloak | Cloak/cape equipment |

### Non-Equipment Items (Table Codes 8+)

| Table Code | Prefix | Item Type | Description |
|------------|--------|-----------|-------------|
| 8 | "ii" | Ring | Ring/finger accessories |
| 9 | "ia" | Amulet | Amulet/neck accessories |
| 10 | "ib" | Bullet | Bullet/ammunition |
| 11 | "im" | Material | Crafting materials/tools |
| 12 | "ie" | Elixir | Elixir/potions |
| 13 | "ip" | Potion | Potions/consumables |
| 14 | "if" | Food | Food items |
| 15 | "ic" | Charm | Charm items |
| 16 | "it" | Ticket | Ticket items |
| 17 | "io" | Ore | Ore/mining materials |
| 18 | "ir" | Rare | Rare items |
| 19 | "in" | Natural | Natural items (FORBIDDEN) |
| 20 | "iy" | Yggdrasil | Yggdrasil items |
| 21 | "iz" | Zone | Zone items |
| 22 | "iq" | Quest | Quest items |
| 23 | "ix" | Experience | Experience items |
| 24 | "ij" | Jewel | Jewel items |
| 25 | "gt" | Gate | Gate items |
| 26 | "tr" | Treasure | Treasure items |
| 27 | "sk" | Skill | Skill items |
| 28 | "ti" | Time | Time items |
| 29 | "ev" | Event | Event items |
| 30 | "re" | Reward | Reward items |
| 31 | "bx" | Box | Box items |
| 32 | "fi" | Fish | Fish items |
| 33 | "un" | Union | Union items |
| 34 | "rd" | Raid | Raid items |
| 35 | "lk" | Link | Link items |
| 36 | "cu" | Currency | Currency items |

## Error Code System

### Equipment Error Codes (from pc_EquipPart analysis)

| Error Code | Constant | Description | Original Line |
|------------|----------|-------------|---------------|
| 0 | Success | Operation successful | v7 = 0 |
| 2 | ItemNotFound | Item not found in inventory | v7 = 2 (line 90) |
| 3 | InvalidTableCode | Item table code >= 8, not equipment | v7 = 3 (line 85) |
| 7 | EquipmentRequirementFailed | Equipment requirement check failed | v7 = 7 (line 74) |
| 8 | PlayerStateError | Player in invalid state (effects 20/28) | v7 = 8 (lines 41,45) |
| 9 | GradeRequirementFailed | Player grade insufficient | v7 = 9 (line 78) |
| 10 | ItemLocked | Item locked or slot occupied | v7 = 10 (lines 59,66) |
| -1 | SystemError | Storage operation failed | SendMsg(-1) |

## Refactored Architecture

### Core Components

1. **ItemCodes.h/cpp** - Complete item code mapping system
2. **CPlayerEquipment.h/cpp** - Modern equipment operation system
3. **Type-safe enums** - Equipment slots, error codes, item types
4. **RAII patterns** - Automatic resource management
5. **Exception safety** - Comprehensive error handling

### Key Improvements

1. **Type Safety**: Strong typing prevents invalid operations
2. **Error Handling**: Comprehensive exception safety and rollback
3. **Maintainability**: Clear separation of concerns
4. **Performance**: Optimized storage operations
5. **Documentation**: Extensive inline documentation
6. **Testing**: Designed for unit testing

### Modern C++ Features Used

- `enum class` for type-safe enumerations
- `std::optional` for nullable values
- `std::chrono` for timing operations
- `std::function` for callbacks
- RAII for resource management
- Exception safety guarantees

## Usage Example

```cpp
#include "Headers/CPlayerEquipment.h"
#include "../items/Headers/ItemCodes.h"

// Create equipment system
CPlayerEquipment equipmentSystem;
equipmentSystem.SetDetailedLogging(true);

// Set up equipment context
EquipmentContext context;
context.pPlayer = pPlayer;
context.pItemPosition = pItemPosition;
context.targetSlot = EquipmentSlot::Weapon;
context.operationId = "equip_weapon_001";

// Perform equipment operation
EquipmentResult result = equipmentSystem.EquipItem(context);

if (result.IsSuccess()) {
    Logger::Info("Equipment operation successful in %lld ms", 
                result.executionTime.count());
} else {
    Logger::Error("Equipment failed: %s", result.GetErrorString().c_str());
}
```

## Validation Logic

### Equipment Validation Steps (from original pc_EquipPart)

1. **Player State Check** (lines 39-46)
   - Effect state 20 (transformation/special state)
   - Effect state 28 (another special state)

2. **Item Existence Check** (lines 49-50)
   - Item must exist in inventory
   - Serial number validation

3. **Equipment Type Check** (line 55)
   - Table code must be < 8 (equipment items only)

4. **Lock Status Check** (lines 57-60)
   - Item must not be locked
   - Target slot must not be locked

5. **Grade Requirement Check** (lines 70-78)
   - Player grade must meet item requirements
   - Uses GetItemEquipGrade and IsEquipAbleGrade

6. **Equipment Part Check** (lines 73-74)
   - Custom equipment requirements
   - Uses _check_equip_part function

## Storage Operations

### Original Storage Flow (lines 95-156)

1. **Backup Creation**: Create backup of item being equipped
2. **Inventory Removal**: Remove item from inventory
3. **Current Item Handling**: Move currently equipped item to inventory
4. **Equipment Addition**: Add new item to equipment slot
5. **Rollback Logic**: Restore state on any failure

### Modern Implementation

- **RAII patterns** for automatic cleanup
- **Exception safety** with guaranteed rollback
- **Type-safe operations** with compile-time validation
- **Performance optimization** with minimal memory operations

## Testing Strategy

1. **Unit Tests**: Individual function validation
2. **Integration Tests**: Full equipment flow testing
3. **Error Condition Tests**: All error paths covered
4. **Performance Tests**: Storage operation timing
5. **Memory Tests**: No memory leaks or corruption

## Compilation Integration

The refactored system integrates with the existing build system:

1. Add to `NexusProtection.vcxproj`
2. Update `NexusProtection.vcxproj.filters`
3. Link with existing player and item systems
4. Maintain compatibility with legacy code

This refactoring provides a solid foundation for modern equipment operations while maintaining full compatibility with the original game logic.
