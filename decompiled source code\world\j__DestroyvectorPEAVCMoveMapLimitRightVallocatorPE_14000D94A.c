/*
 * Function: j_?_Destroy@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAXPEAPEAVCMoveMapLimitRight@@0@Z
 * Address: 0x14000D94A
 */

void __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Destroy(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_First, CMoveMapLimitRight **_Last)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Destroy(this, _First, _Last);
}
