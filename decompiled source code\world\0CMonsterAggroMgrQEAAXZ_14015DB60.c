/*
 * Function: ??0CMonsterAggroMgr@@QEAA@XZ
 * Address: 0x14015DB60
 */

void __fastcall CMonsterAggroMgr::CMonsterAggroMgr(CMonsterAggroMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMonsterAggroMgr *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_pTopAggroCharacter = 0i64;
  v4->m_pTopDamageCharacter = 0i64;
  v4->m_pKingPowerDamageCharacter = 0i64;
  `vector constructor iterator'(v4->m_AggroPool, 0x18ui64, 10, (void *(__cdecl *)(void *))CAggroNode::CAggroNode);
  v4->m_dwAggroCount = 0;
  v4->m_dwAllResetLastTime = 0;
  v4->m_dwShortRankLastTime = 0;
  v4->m_dwAllResetTimer = 30000;
  v4->m_dwShortRankTimer = 5000;
  v4->m_pMonster = 0i64;
}
