/*
 * Function: ?Select_GuildMasterLastConn@CRFWorldDatabase@@QEAAEKKPEAK@Z
 * Address: 0x1404C3F80
 */

char __fastcall CRFWorldDatabase::Select_GuildMasterLastConn(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwLimitConnTime, unsigned int *pdwLastConnTime)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v10; // [sp+38h] [bp-150h]@22
  __int16 v11; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v13; // [sp+164h] [bp-24h]@4
  int v14; // [sp+168h] [bp-20h]@4
  char v15; // [sp+16Ch] [bp-1Ch]@16
  char v16; // [sp+16Dh] [bp-1Bh]@24
  unsigned __int64 v17; // [sp+178h] [bp-10h]@4
  CRFWorldDatabase *v18; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = pdwLastConnTime;
  v18 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v17 = (unsigned __int64)&v7 ^ _security_cookie;
  v13 = 0;
  v14 = 0;
  sprintf(&Dest, "{ CALL pSelect_GuildMasterLastConn( %d, %d ) }", dwSerial, dwLimitConnTime);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v11 = SQLExecDirectA_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v11, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v11 = SQLFetch_0(v18->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v15 = 0;
        if ( v11 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v18->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v11, v18->m_hStmtSelect);
          v15 = 1;
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        result = v15;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v18->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v16 = 0;
          if ( v11 == 100 )
          {
            v16 = 2;
          }
          else
          {
            SQLStmt = v18->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v11, &Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v11, v18->m_hStmtSelect);
            v16 = 1;
          }
          if ( v18->m_hStmtSelect )
            SQLCloseCursor_0(v18->m_hStmtSelect);
          result = v16;
        }
        else
        {
          if ( v18->m_hStmtSelect )
            SQLCloseCursor_0(v18->m_hStmtSelect);
          if ( v18->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
          result = 0;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
