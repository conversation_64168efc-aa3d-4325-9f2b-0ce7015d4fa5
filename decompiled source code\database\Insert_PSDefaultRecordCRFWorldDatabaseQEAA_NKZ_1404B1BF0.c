/*
 * Function: ?Insert_PSDefaultRecord@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B1BF0
 */

char __fastcall CRFWorldDatabase::Insert_PSDefaultRecord(CRFWorldDatabase *this, unsigned int dwCum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char DstBuf; // [sp+30h] [bp-68h]@4
  char v7; // [sp+31h] [bp-67h]@4
  unsigned int j; // [sp+74h] [bp-24h]@4
  unsigned __int64 v9; // [sp+80h] [bp-18h]@4
  CRFWorldDatabase *v10; // [sp+A0h] [bp+8h]@1
  unsigned int v11; // [sp+A8h] [bp+10h]@1

  v11 = dwCum;
  v10 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0x3Fui64);
  sprintf_s(&DstBuf, 0x40ui64, "{ CALL pInsert_PostStorageRecord }");
  for ( j = 0; j < v11; ++j )
  {
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v10->vfptr, &DstBuf, 1) )
      return 0;
  }
  return 1;
}
