/*
 * Function: ?pc_AnimusCommandRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400D0AE0
 */

void __fastcall CPlayer::pc_AnimusCommandRequest(CPlayer *this, char byCommandCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  if ( !v6->m_pRecalledAnimusItem || !v6->m_pRecalledAnimusChar )
    v5 = 7;
  if ( !v5 )
    CAnimus::ChangeMode_MasterCommand(v6->m_pRecalledAnimusChar, (unsigned __int8)byCommandCode);
}
