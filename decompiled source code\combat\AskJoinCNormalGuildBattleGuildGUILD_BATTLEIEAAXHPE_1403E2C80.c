/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CNormalGuildBattleGuild@GUILD_BATTLE@@IEAAXHPEADAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E2C80
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(GUILD_BATTLE::CNormalGuildBattleGuild *this, int n, char *wszDestGuildName, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  char Dest; // [sp+38h] [bp-60h]@7
  char pbyType; // [sp+64h] [bp-34h]@7
  char v9; // [sp+65h] [bp-33h]@7
  unsigned __int64 v10; // [sp+80h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v11; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1
  char *Source; // [sp+B0h] [bp+18h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v14; // [sp+B8h] [bp+20h]@1

  v14 = kLogger;
  Source = wszDestGuildName;
  dwClientIndex = n;
  v11 = this;
  v4 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( v11->m_pkGuild )
  {
    if ( wszDestGuildName )
    {
      strcpy_0(&Dest, wszDestGuildName);
      pbyType = 27;
      v9 = 55;
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &Dest, 0x11u);
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        v14,
        "CNormalGuildBattleGuild::AskJoin( n(%d), %s )",
        (unsigned int)dwClientIndex,
        Source);
    }
  }
}
