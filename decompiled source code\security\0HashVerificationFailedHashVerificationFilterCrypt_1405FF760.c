/*
 * Function: ??0HashVerificationFailed@HashVerificationFilter@CryptoPP@@QEAA@XZ
 * Address: 0x1405FF760
 */

CryptoPP::HashVerificationFilter::HashVerificationFailed *__fastcall CryptoPP::HashVerificationFilter::HashVerificationFailed::HashVerificationFailed(CryptoPP::HashVerificationFilter::HashVerificationFailed *this)
{
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+20h] [bp-48h]@1
  unsigned __int8 v3; // [sp+50h] [bp-18h]@1
  __int64 v4; // [sp+58h] [bp-10h]@1
  CryptoPP::HashVerificationFilter::HashVerificationFailed *v5; // [sp+70h] [bp+8h]@1

  v5 = this;
  v4 = -2i64;
  memset(&v3, 0, sizeof(v3));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &s,
    "HashVerifier: message hash not valid",
    v3);
  CryptoPP::Exception::Exception((CryptoPP::Exception *)&v5->vfptr, DATA_INTEGRITY_CHECK_FAILED, &s);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&s);
  v5->vfptr = (std::exceptionVtbl *)&CryptoPP::HashVerificationFilter::HashVerificationFailed::`vftable';
  return v5;
}
