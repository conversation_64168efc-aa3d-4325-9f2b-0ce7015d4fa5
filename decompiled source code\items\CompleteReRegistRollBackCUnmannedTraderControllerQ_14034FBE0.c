/*
 * Function: ?CompleteReRegistRollBack@CUnmannedTraderController@@QEAAXPEAD@Z
 * Address: 0x14034FBE0
 */

void __fastcall CUnmannedTraderController::CompleteReRegistRollBack(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  char *pDataa; // [sp+48h] [bp+10h]@1

  pDataa = pData;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pData;
  v4 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CompleteReRegistRollBack(v4, *((_WORD *)v6 + 1), *((_DWORD *)v6 + 2), pDataa);
}
