/*
 * Function: ?Init@HACKSHEILD_PARAM_ANTICP@@QEAAXXZ
 * Address: 0x140417890
 */

void __fastcall HACKSHEILD_PARAM_ANTICP::Init(HACKSHEILD_PARAM_ANTICP *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  HACKSHEILD_PARAM_ANTICP *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_nSocketIndex = -1;
  v4->m_dwLastSyncQryTime = 0;
  v4->m_byVerifyState = 0;
  memset_0(&v4->m_CrcInfo, 0, 0x18ui64);
  memset_0(v4->m_byGUIDClientInfo, 0, 0x14ui64);
}
