/*
 * Function: ?SendMsg_QueryAppointResult@ClassOrderProcessor@@QEAAXGEEPEAD@Z
 * Address: 0x1402B8E60
 */

void __fastcall ClassOrderProcessor::SendMsg_QueryAppointResult(ClassOrderProcessor *this, unsigned __int16 wIndex, char byRet, char byClassType, char *pwszAvatorName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v7; // ax@4
  __int64 v8; // [sp+0h] [bp-98h]@1
  char pbyType; // [sp+34h] [bp-64h]@4
  char v10; // [sp+35h] [bp-63h]@4
  _pt_query_appoint_zocl v11; // [sp+58h] [bp-40h]@4
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  unsigned __int16 v13; // [sp+A8h] [bp+10h]@1
  char v14; // [sp+B0h] [bp+18h]@1
  char v15; // [sp+B8h] [bp+20h]@1

  v15 = byClassType;
  v14 = byRet;
  v13 = wIndex;
  v5 = &v8;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = (unsigned __int64)&v8 ^ _security_cookie;
  pbyType = 56;
  v10 = 9;
  _pt_query_appoint_zocl::_pt_query_appoint_zocl(&v11);
  v11.byRet = v14;
  v11.byClassType = v15;
  strcpy_0(v11.wszAvatorName, pwszAvatorName);
  v7 = _pt_query_appoint_zocl::size(&v11);
  CNetProcess::LoadSendMsg(unk_1414F2088, v13, &pbyType, &v11.byClassType, v7);
}
