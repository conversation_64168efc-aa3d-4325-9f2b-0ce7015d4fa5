/*
 * Function: ?ct_StopBattle@CHolyStoneSystem@@QEAA_NXZ
 * Address: 0x1402815B0
 */

char __fastcall CHolyStoneSystem::ct_StopBattle(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CHolyStoneSystem *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5->m_SaveData.m_nSceneCode == 1 )
  {
    v5->m_dwCheckTime[1] = GetLoopTime();
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
