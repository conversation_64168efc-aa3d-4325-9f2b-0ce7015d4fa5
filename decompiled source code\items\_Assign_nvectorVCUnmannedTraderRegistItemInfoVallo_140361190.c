/*
 * Function: ?_Assign_n@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAX_KAEBVCUnmannedTraderRegistItemInfo@@@Z
 * Address: 0x140361190
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Assign_n(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-1D8h]@1
  CUnmannedTraderRegistItemInfo v6; // [sp+30h] [bp-1A8h]@4
  char v7; // [sp+B0h] [bp-128h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result; // [sp+C8h] [bp-110h]@4
  char v9; // [sp+D0h] [bp-108h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v10; // [sp+E8h] [bp-F0h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v11; // [sp+F0h] [bp-E8h]@4
  char v12; // [sp+108h] [bp-D0h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v13; // [sp+120h] [bp-B8h]@4
  char v14; // [sp+128h] [bp-B0h]@4
  __int64 v15; // [sp+190h] [bp-48h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v16; // [sp+198h] [bp-40h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v17; // [sp+1A0h] [bp-38h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v18; // [sp+1A8h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v19; // [sp+1B0h] [bp-28h]@4
  unsigned __int64 v20; // [sp+1B8h] [bp-20h]@4
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v21; // [sp+1E0h] [bp+8h]@1
  unsigned __int64 v22; // [sp+1E8h] [bp+10h]@1

  v22 = _Count;
  v21 = this;
  v3 = &v5;
  for ( i = 114i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  v20 = (unsigned __int64)&v5 ^ _security_cookie;
  qmemcpy(&v14, _Val, 0x68ui64);
  qmemcpy(&v6, &v14, sizeof(v6));
  result = (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v7;
  v10 = (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v9;
  v16 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          v21,
          (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v7);
  v17 = v16;
  v18 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(v21, v10);
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::erase(v21, &v11, v18, v17);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v11);
  v13 = (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v12;
  v19 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(
          v21,
          (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v12);
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::insert(v21, v19, v22, &v6);
  CUnmannedTraderRegistItemInfo::~CUnmannedTraderRegistItemInfo(&v6);
}
