#pragma once

#include "EconomyTypes.h"
#include <cstdint>

// Forward declarations
class CNetworkEX;
class CPlayer;
class CGuild;

namespace NexusProtection::Economy {

    /**
     * @brief Network handlers for economy-related operations
     * 
     * This class provides handlers for various economy network requests including:
     * - Currency exchange (Dalant <-> Gold)
     * - Guild money operations
     * - Trunk money operations
     * - Auto-mine cost management
     */
    class EconomyNetworkHandlers {
    public:
        // Currency exchange handlers
        static bool HandleExchangeDalantForGoldRequest(CNetworkEX* network, int32_t sessionId, char* buffer);
        static bool HandleExchangeGoldForDalantRequest(CNetworkEX* network, int32_t sessionId, char* buffer);

        // Guild money handlers
        static bool HandleGuildPushMoneyRequest(CNetworkEX* network, int32_t sessionId, char* buffer);

        // Trunk operations
        static bool HandleTrunkIoMoneyRequest(CNetworkEX* network, int32_t sessionId, char* buffer);
        static bool HandleTrunkCreateCostIsFreeRequest(CNetworkEX* network, int32_t sessionId, char* buffer);

        // Auto-mine operations
        static void HandleSubChargeCostAutoMineMachine(uint8_t machineType, char* playerData);

        // Validation helpers
        static bool ValidatePlayer(CPlayer* player);
        static bool ValidateCurrencyAmount(uint32_t amount);
        static bool ValidateGuildOperation(CGuild* guild);

    private:
        // Internal helper methods
        static bool ProcessCurrencyExchange(CPlayer* player, CurrencyType fromType, CurrencyType toType, uint32_t amount);
        static bool ProcessGuildMoneyOperation(CGuild* guild, const char* operatorName, uint32_t operatorSerial, 
                                             double dalantAmount, double goldAmount, bool isInput);

        // Constants
        static constexpr uint32_t MAX_CURRENCY_EXCHANGE_AMOUNT = 1000000000; // 1 billion
        static constexpr uint32_t MIN_CURRENCY_EXCHANGE_AMOUNT = 1;
    };

    /**
     * @brief Guild money management operations
     */
    class GuildMoneyManager {
    public:
        // Guild money I/O operations
        static void ProcessGuildMoneyIO(CGuild* guild, const char* operatorName, uint32_t operatorSerial,
                                      double dalantIO, double goldIO, double totalDalant, double totalGold,
                                      const char* date, bool isInput);

        // Guild money history
        static void PushMoneyHistory(CGuild* guild, bool isInput, const char* operatorName, uint32_t operatorSerial,
                                   double dalantAmount, double goldAmount, double totalDalant, double totalGold,
                                   const char* date);

        // Guild money validation
        static bool CheckMaxGuildMoney(uint32_t guildId, char* playerData, bool* isValid, double newAmount);
        static bool CheckMinMaxGuildMoney(uint32_t guildId, bool* minValid, bool* maxValid);

        // Guild money management
        static uint8_t ManagePopGuildMoney(CGuild* guild, uint32_t amount1, uint32_t amount2, uint32_t amount3);
        static void SetGuildMaintainMoney(uint8_t honorType, uint32_t guildId, uint32_t amount);

        // Honor guild operations
        static void CompleteHonorGuildTaxMoney(const char* playerData);

    private:
        // Internal validation
        static bool ValidateGuildMoneyOperation(CGuild* guild, double amount);
        static bool ValidateOperatorPermissions(CGuild* guild, uint32_t operatorSerial);

        // Constants
        static constexpr double MAX_GUILD_MONEY = *********.0; // 999 million
        static constexpr double MIN_GUILD_MONEY = 0.0;
    };

    /**
     * @brief Quest and economy condition checking
     */
    class EconomyQuestManager {
    public:
        // Quest condition checking
        static bool CheckDalantCondition(uint8_t raceCode, int32_t requiredAmount);
        static bool CheckQuestDalantCondition(int32_t questId, uint8_t raceCode, int32_t amount);

        // Quest dalant operations for dark hole dungeons
        static bool ProcessQuestDalant(void* questFile, void* dungeonQuest);

    private:
        // Internal quest validation
        static bool ValidateQuestDalantRequirement(int32_t questId, int32_t amount);
    };

    // Legacy C interface compatibility
    extern "C" {
        // Network handler function pointers
        typedef char (*ExchangeDalantForGoldRequestFunc)(CNetworkEX*, int, char*);
        typedef char (*ExchangeGoldForDalantRequestFunc)(CNetworkEX*, int, char*);
        typedef char (*GuildPushMoneyRequestFunc)(CNetworkEX*, int, char*);
        typedef char (*TrunkIoMoneyRequestFunc)(CNetworkEX*, int, char*);
        typedef char (*TrunkCreateCostIsFreeRequestFunc)(CNetworkEX*, int, char*);

        // Legacy function declarations
        char CNetworkEX_ExchangeDalantForGoldRequest(CNetworkEX* network, int n, char* pBuf);
        char CNetworkEX_ExchangeGoldForDalantRequest(CNetworkEX* network, int n, char* pBuf);
        char CNetworkEX_GuildPushMoneyRequest(CNetworkEX* network, int n, char* pBuf);
        char CNetworkEX_TrunkIoMoneyRequest(CNetworkEX* network, int n, char* pBuf);
        char CNetworkEX_TrunkCreateCostIsFreeRequest(CNetworkEX* network, int n, char* pBuf);

        // Guild operations
        void CGuild_IOMoney(CGuild* guild, char* operatorName, unsigned int operatorSerial,
                           long double dalantIO, long double goldIO, long double totalDalant,
                           long double totalGold, char* date, bool isInput);
        void CGuild_PushHistory_IOMoney(CGuild* guild, bool isInput, char* operatorName, unsigned int operatorSerial,
                                       long double dalantAmount, long double goldAmount, long double totalDalant,
                                       long double totalGold, char* date);
        void CGuild_CheckMaxGuildMoney(void* guildRanking, unsigned int guildId, char* playerData, bool* isValid, long double amount);
        unsigned char CGuild_ManagePopGuildMoney(CGuild* guild, unsigned int amount1, unsigned int amount2, unsigned int amount3);

        // Auto-mine operations
        void SubChargeCostAutoMineMachine(unsigned char machineType, char* playerData);

        // Quest operations
        char CQuestMgr_CheckCond_Dalant(unsigned char raceCode, int amount);
        char qc_Dalant(void* questFile, void* dungeonQuest);

        // Utility functions
        char CanAddMoneyForMaxLimGold(unsigned int amount);
        char CanAddMoneyForMaxLimMoney(unsigned int amount);
    }

} // namespace NexusProtection::Economy
