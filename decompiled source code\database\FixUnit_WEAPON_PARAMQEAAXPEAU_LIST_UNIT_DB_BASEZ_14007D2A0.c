/*
 * Function: ?FixUnit@_WEAPON_PARAM@@QEAAXPEAU_LIST@_UNIT_DB_BASE@@@Z
 * Address: 0x14007D2A0
 */

void __fastcall _WEAPON_PARAM::FixUnit(_WEAPON_PARAM *this, _UNIT_DB_BASE::_LIST *pUnit)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  _WEAPON_PARAM *v5; // [sp+30h] [bp+8h]@1
  _UNIT_DB_BASE::_LIST *v6; // [sp+38h] [bp+10h]@1

  v6 = pUnit;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _WEAPON_PARAM::Init(v5);
  v5->byWpClass = 0;
  v5->byWpType = 0;
  v5->pFixWp = 0i64;
  v5->pFixUnit = v6;
}
