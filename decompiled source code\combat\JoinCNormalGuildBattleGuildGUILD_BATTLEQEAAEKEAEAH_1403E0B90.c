/*
 * Function: ?Join@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAEKEAEAHAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E0B90
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Join(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial, char GuildBattleNumber, int *iMemberInx, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-58h]@1
  int v9; // [sp+20h] [bp-38h]@17
  unsigned int v10; // [sp+28h] [bp-30h]@17
  char *v11; // [sp+30h] [bp-28h]@17
  unsigned int v12; // [sp+38h] [bp-20h]@17
  _guild_member_info *pkMember; // [sp+40h] [bp-18h]@8
  unsigned int v14; // [sp+48h] [bp-10h]@15
  GUILD_BATTLE::CNormalGuildBattleGuild *v15; // [sp+60h] [bp+8h]@1
  unsigned int dwMemberSerial; // [sp+68h] [bp+10h]@1
  char v17; // [sp+70h] [bp+18h]@1
  int *v18; // [sp+78h] [bp+20h]@1

  v18 = iMemberInx;
  v17 = GuildBattleNumber;
  dwMemberSerial = dwSerial;
  v15 = this;
  v5 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v15->m_pkGuild )
  {
    if ( (unsigned __int8)GuildBattleNumber > v15->m_dwCurJoinMember )
    {
      pkMember = CGuild::GetMemberFromSerial(v15->m_pkGuild, dwSerial);
      if ( pkMember )
      {
        if ( GUILD_BATTLE::CNormalGuildBattleGuild::IsJoinMember(v15, dwMemberSerial) )
        {
          result = -110;
        }
        else
        {
          *v18 = GUILD_BATTLE::CNormalGuildBattleGuild::GetEmptyMember(v15);
          if ( *v18 >= 0 )
          {
            GUILD_BATTLE::CNormalGuildBattleGuildMember::Join(&v15->m_kMember[*v18], pkMember);
            if ( v15->m_dwMaxJoinMemberCnt <= ++v15->m_dwCurJoinMember )
              v14 = v15->m_dwCurJoinMember;
            else
              v14 = v15->m_dwMaxJoinMemberCnt;
            v15->m_dwMaxJoinMemberCnt = v14;
            v12 = pkMember->dwSerial;
            v11 = pkMember->wszName;
            v10 = v15->m_dwCurJoinMember;
            v9 = *v18;
            GUILD_BATTLE::CNormalGuildBattleLogger::Log(
              kLogger,
              "CNormalGuildBattleGuild::Join( dwSerial(%u), GuildBattleNumber(%u) ) : iMemberInx(%d) Cnt(%u) %s(%u) Join!",
              dwMemberSerial,
              (unsigned __int8)v17);
            result = 0;
          }
          else
          {
            result = 110;
          }
        }
      }
      else
      {
        result = -116;
      }
    }
    else
    {
      result = 127;
    }
  }
  else
  {
    result = 110;
  }
  return result;
}
