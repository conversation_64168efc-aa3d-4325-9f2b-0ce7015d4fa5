/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long____0___::insert_::_1_::dtor$14
 * Address: 0x140422780
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long____0___::insert_::_1_::dtor_14(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 612) & 1 )
  {
    *(_DWORD *)(a2 + 612) &= 0xFFFFFFFE;
    std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>::~pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::_Iterator<0>,bool>(*(std::pair<std::list<std::pair<ScheduleMSG * const,unsigned long>,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > >::_Iterator<0>,bool> **)(a2 + 856));
  }
}
