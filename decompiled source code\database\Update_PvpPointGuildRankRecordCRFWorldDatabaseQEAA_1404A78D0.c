/*
 * Function: ?Update_PvpPointGuildRankRecord@CRFWorldDatabase@@QEAA_NPEADKG@Z
 * Address: 0x1404A78D0
 */

bool __fastcall CRFWorldDatabase::Update_PvpPointGuildRankRecord(CRFWorldDatabase *this, char *szDate, unsigned int dwSerial, unsigned __int16 wRank)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-168h]@1
  unsigned int v8; // [sp+20h] [bp-148h]@4
  char Dst; // [sp+40h] [bp-128h]@4
  unsigned __int64 v10; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+170h] [bp+8h]@1
  char *v12; // [sp+178h] [bp+10h]@1
  unsigned int v13; // [sp+180h] [bp+18h]@1
  unsigned __int16 v14; // [sp+188h] [bp+20h]@1

  v14 = wRank;
  v13 = dwSerial;
  v12 = szDate;
  v11 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  memset_0(&Dst, 0, 0x100ui64);
  v8 = v13;
  sprintf(&Dst, "update [dbo].[tbl_PvpPointGuildRank%s] set rank=%u where serial=%u", v12, v14);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &Dst, 1);
}
