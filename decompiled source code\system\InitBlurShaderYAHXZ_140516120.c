/*
 * Function: ?InitBlurShader@@YAHXZ
 * Address: 0x140516120
 */

__int64 InitBlurShader(void)
{
  struct IDirect3DDevice8 *v1; // rax@10
  unsigned int v2; // ebx@10
  __int64 v3; // rcx@14
  float v4; // xmm2_4@16
  float v5; // xmm1_4@20
  float v6; // xmm2_4@23
  float v7; // xmm1_4@27
  __int64 v8; // [sp+50h] [bp+8h]@12
  unsigned __int64 v9; // [sp+58h] [bp+10h]@29

  if ( *(float *)&dword_184A797B0 < 1.1
    || *(float *)&dword_184A797B4 < 1.1
    || (unsigned int)dword_184A797C0 < 4
    || !dword_184A797AC
    || (unsigned int)IsExistFile(".\\System\\shader\\FilterBlit.vso")
    && (unsigned int)LoadCreateShader(".\\System\\shader\\FilterBlit.vso", &stBlurVertexDecl, 0i64, 1, &dword_184A893E4)
    || (unsigned int)IsExistFile(".\\System\\shader\\BlurBlit.pso")
    && (unsigned int)LoadCreateShader(".\\System\\shader\\BlurBlit.pso", 0i64, 0i64, 2, &dword_184A893E8) )
  {
    return 0i64;
  }
  v1 = GetD3dDevice();
  v2 = 0;
  if ( ((int (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64, _QWORD))v1->vfptr[7].Release)(
         v1,
         80i64,
         520i64,
         0i64) < 0 )
    return 0i64;
  if ( m_pVertexBuffer )
  {
    if ( ((int (__fastcall *)(struct IDirect3DVertexBuffer8 *, _QWORD, signed __int64, __int64 *))m_pVertexBuffer->vfptr[3].Release)(
           m_pVertexBuffer,
           0i64,
           80i64,
           &v8) < 0 )
      return 0i64;
    v3 = v8;
    do
    {
      if ( v2 >= 2 )
        v4 = FLOAT_1_0;
      else
        v4 = FLOAT_N1_0;
      if ( v2 && v2 != 3 )
        v5 = FLOAT_1_0;
      else
        v5 = FLOAT_N1_0;
      *(float *)v3 = v5;
      *(float *)(v3 + 4) = v4;
      *(_DWORD *)(v3 + 8) = 0;
      if ( v2 >= 2 )
        LODWORD(v6) = 0;
      else
        v6 = FLOAT_1_0;
      if ( v2 && v2 != 3 )
        v7 = FLOAT_1_0;
      else
        LODWORD(v7) = 0;
      v9 = __PAIR__(LODWORD(v6), LODWORD(v7));
      ++v2;
      *(_QWORD *)(v8 + 12) = __PAIR__(LODWORD(v6), LODWORD(v7));
      v3 = v8 + 20;
      v8 += 20i64;
    }
    while ( v2 < 4 );
    ((void (*)(void))m_pVertexBuffer->vfptr[4].QueryInterface)();
  }
  return 1i64;
}
