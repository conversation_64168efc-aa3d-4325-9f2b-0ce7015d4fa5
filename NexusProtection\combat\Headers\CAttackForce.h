/*
 * CAttackForce.h - Modern C++ Attack Force System
 * Refactored from AttackForceCAttackQEAAXPEAU_attack_param_NZ_14016A210.c
 * Original Address: 0x14016A210
 */

#pragma once

#include <memory>
#include <vector>
#include <array>
#include <functional>
#include <optional>
#include <chrono>

// Forward declarations
class CCharacter;
class CPlayer;
class CMonster;
class CPvpUserAndGuildRankingSystem;
class CHolyStoneSystem;
struct _attack_param;
struct _base_fld;
struct _effect_parameter;

namespace NexusProtection {
namespace Combat {
    class CAttackForceErrorHandler;
}
}

namespace NexusProtection {
namespace Combat {

/**
 * Attack force calculation constants
 */
namespace AttackForceConstants {
    constexpr float BASE_HIT_CHANCE = 100.0f;
    constexpr float DESTROYER_DAMAGE_MULTIPLIER = 1.3f;
    constexpr float BOSS_TYPE_2_6_MULTIPLIER = 1.2f;
    constexpr float BOSS_TYPE_0_MULTIPLIER = 1.3f;
    constexpr int MAX_HIT_CHANCE = 100;
    constexpr int MIN_HIT_CHANCE = 0;
    
    // Attack type constants
    constexpr int ATTACK_TYPE_SINGLE_MIN = 0;
    constexpr int ATTACK_TYPE_SINGLE_MAX = 2;
    constexpr int ATTACK_TYPE_AREA_1 = 4;
    constexpr int ATTACK_TYPE_FLASH = 5;
    constexpr int ATTACK_TYPE_AREA_2 = 6;
    
    // Effect constants
    constexpr int EFFECT_INVULNERABILITY = 8;
    constexpr int EFFECT_ACCURACY_BONUS = 31;
    constexpr int EFFECT_PLAYER_ACCURACY = 40;
    constexpr int EFFECT_DAMAGE_RATE = 4;
}

/**
 * Attack force calculation result
 */
struct AttackForceResult {
    bool bSuccess{false};
    bool bHit{false};
    bool bCritical{false};
    int nDamage{0};
    int nTargetsHit{0};
    std::vector<CCharacter*> hitTargets;
    std::string errorMessage;
    
    AttackForceResult() = default;
    
    void Reset() {
        bSuccess = false;
        bHit = false;
        bCritical = false;
        nDamage = 0;
        nTargetsHit = 0;
        hitTargets.clear();
        errorMessage.clear();
    }
    
    bool IsValid() const {
        return bSuccess && (bHit || nTargetsHit > 0);
    }
};

/**
 * Damage calculation parameters
 */
struct DamageCalculationParams {
    float fBaseDamage{0.0f};
    float fEffectiveDamage{0.0f};
    float fHitChance{100.0f};
    bool bUseEffBullet{false};
    bool bApplyBonuses{true};
    bool bIgnoreDefense{false};
    
    DamageCalculationParams() = default;
    
    void Reset() {
        fBaseDamage = 0.0f;
        fEffectiveDamage = 0.0f;
        fHitChance = 100.0f;
        bUseEffBullet = false;
        bApplyBonuses = true;
        bIgnoreDefense = false;
    }
};

/**
 * Attack type configuration
 */
struct AttackTypeConfig {
    int nType{0};
    int nLimitRadius{0};
    int nLimitDistance{0};
    int nLimitAngle{0};
    std::array<float, 3> targetArea{0.0f, 0.0f, 0.0f};
    
    AttackTypeConfig() = default;
    
    bool IsAreaAttack() const {
        return nType == AttackForceConstants::ATTACK_TYPE_AREA_1 || 
               nType == AttackForceConstants::ATTACK_TYPE_AREA_2;
    }
    
    bool IsFlashAttack() const {
        return nType == AttackForceConstants::ATTACK_TYPE_FLASH;
    }
    
    bool IsSingleTarget() const {
        return nType >= AttackForceConstants::ATTACK_TYPE_SINGLE_MIN && 
               nType <= AttackForceConstants::ATTACK_TYPE_SINGLE_MAX;
    }
};

/**
 * Modern C++ Attack Force System
 */
class CAttackForce {
public:
    /**
     * Constructor
     * @param pAttacker The attacking character
     */
    explicit CAttackForce(CCharacter* pAttacker);
    
    /**
     * Destructor
     */
    virtual ~CAttackForce();
    
    /**
     * Execute force attack
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @param fAccuracyBonus Additional accuracy bonus
     * @return Attack result
     */
    AttackForceResult ExecuteAttack(_attack_param* pParam, bool bUseEffBullet = false, float fAccuracyBonus = 0.0f);
    
    /**
     * Calculate hit chance for target
     * @param pTarget Target character
     * @param fAccuracyBonus Additional accuracy bonus
     * @return Hit chance percentage (0-100)
     */
    float CalculateHitChance(CCharacter* pTarget, float fAccuracyBonus = 0.0f) const;
    
    /**
     * Calculate damage values
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @return Damage calculation parameters
     */
    DamageCalculationParams CalculateDamage(_attack_param* pParam, bool bUseEffBullet = false) const;
    
    /**
     * Get attack type configuration from field data
     * @param pField Field data pointer
     * @param pParam Attack parameters
     * @return Attack type configuration
     */
    AttackTypeConfig GetAttackTypeConfig(_base_fld* pField, _attack_param* pParam) const;
    
    /**
     * Check if attacker is valid
     * @return true if attacker is valid
     */
    bool IsValidAttacker() const;
    
    /**
     * Check if target is valid for attack
     * @param pTarget Target character
     * @return true if target is valid
     */
    bool IsValidTarget(CCharacter* pTarget) const;
    
    /**
     * Reset attack state
     */
    void Reset();

protected:
    /**
     * Calculate base force attack points
     * @param bUseEffBullet Use effect bullet flag
     * @return Base attack points
     */
    virtual int CalculateForceAttackPoints(bool bUseEffBullet = false) const;
    
    /**
     * Apply damage multipliers (destroyer, PvP bonuses, etc.)
     * @param fBaseDamage Base damage value
     * @param fEffectiveDamage Effective damage value
     * @return Modified damage values
     */
    virtual std::pair<float, float> ApplyDamageMultipliers(float fBaseDamage, float fEffectiveDamage) const;
    
    /**
     * Process single target attack
     * @param pParam Attack parameters
     * @param damageParams Damage calculation parameters
     * @return Attack result
     */
    virtual AttackForceResult ProcessSingleTargetAttack(_attack_param* pParam, const DamageCalculationParams& damageParams);
    
    /**
     * Process area damage attack
     * @param pParam Attack parameters
     * @param damageParams Damage calculation parameters
     * @param config Attack type configuration
     * @return Attack result
     */
    virtual AttackForceResult ProcessAreaAttack(_attack_param* pParam, const DamageCalculationParams& damageParams, const AttackTypeConfig& config);
    
    /**
     * Process flash damage attack
     * @param pParam Attack parameters
     * @param damageParams Damage calculation parameters
     * @param config Attack type configuration
     * @return Attack result
     */
    virtual AttackForceResult ProcessFlashAttack(_attack_param* pParam, const DamageCalculationParams& damageParams, const AttackTypeConfig& config);

private:
    CCharacter* m_pAttacker;                    // Attacking character
    mutable std::optional<AttackForceResult> m_lastResult;  // Last attack result (cached)
    std::unique_ptr<CAttackForceErrorHandler> m_pErrorHandler;  // Error handler

    // Disable copy constructor and assignment operator
    CAttackForce(const CAttackForce&) = delete;
    CAttackForce& operator=(const CAttackForce&) = delete;
};

/**
 * Attack force utility functions
 */
namespace AttackForceUtils {
    /**
     * Get limit distance array value
     * @param nIndex Array index
     * @return Limit distance value
     */
    int GetLimitDistance(int nIndex);

    /**
     * Get limit angle array value
     * @param nType Angle type
     * @param nIndex Array index
     * @return Limit angle value
     */
    int GetLimitAngle(int nType, int nIndex);

    /**
     * Get limit radius array value
     * @param nIndex Array index
     * @return Limit radius value
     */
    int GetLimitRadius(int nIndex);

    /**
     * Check if character has destroyer status
     * @param pCharacter Character to check
     * @return true if character is destroyer
     */
    bool IsDestroyer(CCharacter* pCharacter);

    /**
     * Check if character has last attack buff
     * @param pCharacter Character to check
     * @return true if character has last attack buff
     */
    bool HasLastAttackBuff(CCharacter* pCharacter);

    /**
     * Get PvP boss type for character
     * @param pCharacter Character to check
     * @return Boss type (0 = no boss, 2/6 = boss types)
     */
    int GetPvPBossType(CCharacter* pCharacter);

    /**
     * Break stealth for character
     * @param pCharacter Character to break stealth for
     */
    void BreakStealth(CCharacter* pCharacter);

    /**
     * Get effect state for character
     * @param pCharacter Character to check
     * @param nEffectType Effect type to check
     * @return true if character has the effect
     */
    bool GetEffectState(CCharacter* pCharacter, int nEffectType);

    /**
     * Get effect plus value for character
     * @param pCharacter Character to check
     * @param nEffectType Effect type to get
     * @return Effect plus value
     */
    float GetEffectPlus(CCharacter* pCharacter, int nEffectType);

    /**
     * Get effect rate for character
     * @param pCharacter Character to check
     * @param nEffectType Effect type to get
     * @return Effect rate multiplier
     */
    float GetEffectRate(CCharacter* pCharacter, int nEffectType);

    /**
     * Calculate attack damage point
     * @param pAttacker Attacking character
     * @param nAttPower Attack power
     * @param nPart Attack part
     * @param nTol Tolerance/element type
     * @param pTarget Target character
     * @param bBackAttack Back attack flag
     * @return Calculated damage
     */
    int CalculateAttackDamagePoint(CCharacter* pAttacker, int nAttPower, int nPart, int nTol, CCharacter* pTarget, bool bBackAttack);
}

/**
 * Legacy C-style interface for backward compatibility
 */
extern "C" {
    /**
     * Legacy AttackForce function interface
     * @param pThis CAttack instance
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @param fAccuracyBonus Accuracy bonus
     */
    void CAttack_AttackForce(void* pThis, _attack_param* pParam, bool bUseEffBullet, float fAccuracyBonus);
}

} // namespace Combat
} // namespace NexusProtection
