/**
 * @file CGuildRoomSystemManager_Methods.cpp
 * @brief Additional methods for CGuildRoomSystemManager
 * 
 * This file contains the remaining method implementations for the guild room system manager.
 */

#include "../Headers/CGuildRoomSystemManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>

namespace NexusProtection::Guild {

void CGuildRoomSystemManager::SetRoomInitializedCallback(RoomInitializedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_roomInitializedCallback = std::move(callback);
}

void CGuildRoomSystemManager::SetRoomRentedCallback(RoomRentedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_roomRentedCallback = std::move(callback);
}

void CGuildRoomSystemManager::SetRoomAccessCallback(RoomAccessCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_roomAccessCallback = std::move(callback);
}

void CGuildRoomSystemManager::SetRoomErrorCallback(RoomErrorCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_roomErrorCallback = std::move(callback);
}

void CGuildRoomSystemManager::SetMapLoadedCallback(MapLoadedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_mapLoadedCallback = std::move(callback);
}

uint32_t CGuildRoomSystemManager::GetActiveRoomCount() const {
    try {
        if (!m_isInitialized.load()) {
            return 0;
        }
        
        return m_stats.activeRentals.load();
        
    } catch (const std::exception& e) {
        LogError("GetActiveRoomCount", std::string("Exception: ") + e.what());
        return 0;
    }
}

void CGuildRoomSystemManager::ResetStatistics() {
    try {
        m_stats.totalRoomsInitialized.store(0);
        m_stats.totalRoomsRented.store(0);
        m_stats.totalRoomEntries.store(0);
        m_stats.totalRoomExits.store(0);
        m_stats.activeRentals.store(0);
        m_stats.mapLoadingErrors.store(0);
        m_stats.roomOperationErrors.store(0);
        m_stats.startTime = std::chrono::steady_clock::now();
        
        m_consecutiveErrors.store(0);
        m_lastErrorTime = std::chrono::steady_clock::now();
        
        std::cout << "[INFO] Guild room system statistics reset" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ResetStatistics: " << e.what() << std::endl;
    }
}

void CGuildRoomSystemManager::NotifyRoomInitialized(int roomIndex, GuildRace race, GuildRoomType type) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_roomInitializedCallback) {
            try {
                m_roomInitializedCallback(roomIndex, race, type);
            } catch (const std::exception& e) {
                LogError("NotifyRoomInitialized", std::string("Callback exception: ") + e.what());
            }
        }
    } catch (const std::exception& e) {
        LogError("NotifyRoomInitialized", std::string("Exception: ") + e.what());
    }
}

void CGuildRoomSystemManager::NotifyRoomRented(uint32_t guildSerial, int roomIndex, GuildRoomType type) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_roomRentedCallback) {
            try {
                m_roomRentedCallback(guildSerial, roomIndex, type);
            } catch (const std::exception& e) {
                LogError("NotifyRoomRented", std::string("Callback exception: ") + e.what());
            }
        }
    } catch (const std::exception& e) {
        LogError("NotifyRoomRented", std::string("Exception: ") + e.what());
    }
}

void CGuildRoomSystemManager::NotifyRoomAccess(const RoomAccessInfo& accessInfo) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_roomAccessCallback) {
            try {
                m_roomAccessCallback(accessInfo);
            } catch (const std::exception& e) {
                LogError("NotifyRoomAccess", std::string("Callback exception: ") + e.what());
            }
        }
    } catch (const std::exception& e) {
        LogError("NotifyRoomAccess", std::string("Exception: ") + e.what());
    }
}

void CGuildRoomSystemManager::NotifyRoomError(int roomIndex, const std::string& error) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_roomErrorCallback) {
            try {
                m_roomErrorCallback(roomIndex, error);
            } catch (const std::exception& e) {
                LogError("NotifyRoomError", std::string("Callback exception: ") + e.what());
            }
        }
    } catch (const std::exception& e) {
        LogError("NotifyRoomError", std::string("Exception: ") + e.what());
    }
}

void CGuildRoomSystemManager::NotifyMapLoaded(const std::string& mapName, bool success) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_mapLoadedCallback) {
            try {
                m_mapLoadedCallback(mapName, success);
            } catch (const std::exception& e) {
                LogError("NotifyMapLoaded", std::string("Callback exception: ") + e.what());
            }
        }
    } catch (const std::exception& e) {
        LogError("NotifyMapLoaded", std::string("Exception: ") + e.what());
    }
}

} // namespace NexusProtection::Guild

// Legacy C-style wrapper functions for compatibility
extern "C" {
    /**
     * @brief Legacy wrapper for CGuildRoomSystem::Init
     * 
     * @return true if successful, false otherwise
     */
    bool CGuildRoomSystem_Init() {
        return NexusProtection::Guild::CGuildRoomSystemManager::GetInstance().Init();
    }
    
    /**
     * @brief Legacy wrapper for room entry
     * 
     * @param guildSerial Guild serial number
     * @param roomIndex Room index
     * @param characterSerial Character serial number
     * @return Room entry result code
     */
    int CGuildRoomSystem_RoomIn(uint32_t guildSerial, int roomIndex, uint32_t characterSerial) {
        return NexusProtection::Guild::CGuildRoomSystemManager::GetInstance().RoomIn(guildSerial, roomIndex, characterSerial);
    }
    
    /**
     * @brief Legacy wrapper for room exit
     * 
     * @param guildSerial Guild serial number
     * @param roomIndex Room index
     * @param characterSerial Character serial number
     * @return Room exit result code
     */
    int CGuildRoomSystem_RoomOut(uint32_t guildSerial, int roomIndex, uint32_t characterSerial) {
        return NexusProtection::Guild::CGuildRoomSystemManager::GetInstance().RoomOut(guildSerial, roomIndex, characterSerial);
    }
    
    /**
     * @brief Legacy wrapper for room rental
     * 
     * @param race Guild race
     * @param roomType Room type
     * @param guildIndex Guild index
     * @param guildSerial Guild serial number
     * @param timestamp Rental timestamp
     * @return Rental result code
     */
    uint8_t CGuildRoomSystem_RentRoom(uint8_t race, uint8_t roomType, int guildIndex, 
                                      uint32_t guildSerial, void* timestamp) {
        return NexusProtection::Guild::CGuildRoomSystemManager::GetInstance().RentRoom(
            static_cast<NexusProtection::Guild::GuildRace>(race),
            static_cast<NexusProtection::Guild::GuildRoomType>(roomType),
            guildIndex, guildSerial, 
            static_cast<tagTIMESTAMP_STRUCT*>(timestamp));
    }
    
    /**
     * @brief Legacy wrapper for getting map position
     * 
     * @param guildSerial Guild serial number
     * @param position Output position array
     * @param mapData Output map data pointer
     * @param mapLayer Output map layer
     * @param roomType Output room type
     * @return true if successful, false otherwise
     */
    bool CGuildRoomSystem_GetMapPos(uint32_t guildSerial, float* position, void** mapData, 
                                    uint16_t* mapLayer, uint8_t* roomType) {
        return NexusProtection::Guild::CGuildRoomSystemManager::GetInstance().GetMapPos(
            guildSerial, position, reinterpret_cast<CMapData**>(mapData), mapLayer, roomType);
    }
}
