/*
 * Function: ?PushQuestCash_Other@CHolyStoneSystem@@QEAAXKE@Z
 * Address: 0x140281F70
 */

void __fastcall CHolyStoneSystem::PushQuestCash_Other(CHolyStoneSystem *this, unsigned int dwAvatorSerial, char byStoneMapMoveInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  _QUEST_CASH_OTHER *v7; // [sp+28h] [bp-20h]@7
  _QUEST_CASH_OTHER *v8; // [sp+30h] [bp-18h]@14
  CHolyStoneSystem *v9; // [sp+50h] [bp+8h]@1
  unsigned int v10; // [sp+58h] [bp+10h]@1
  char v11; // [sp+60h] [bp+18h]@1

  v11 = byStoneMapMoveInfo;
  v10 = dwAvatorSerial;
  v9 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 5064; ++j )
  {
    v7 = &v9->m_cashQuestOther[j];
    if ( _QUEST_CASH_OTHER::isLoaded(v7) && v7->dwAvatorSerial == v10 )
    {
      v7->byStoneMapMoveInfo = v11;
      return;
    }
  }
  for ( j = 0; j < 5064; ++j )
  {
    v8 = &v9->m_cashQuestOther[j];
    if ( !_QUEST_CASH_OTHER::isLoaded(v8) )
    {
      v8->dwAvatorSerial = v10;
      v8->byStoneMapMoveInfo = v11;
      return;
    }
  }
}
