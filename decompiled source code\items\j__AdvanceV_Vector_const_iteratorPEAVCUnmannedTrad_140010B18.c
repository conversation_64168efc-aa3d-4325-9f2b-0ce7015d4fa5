/*
 * Function: j_??$_Advance@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@_J@std@@YAXAEAV?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@_JUrandom_access_iterator_tag@0@@Z
 * Address: 0x140010B18
 */

void __fastcall std::_Advance<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,__int64>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Where, __int64 _Off, std::random_access_iterator_tag __formal)
{
  std::_Advance<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,__int64>(
    _Where,
    _Off,
    __formal);
}
