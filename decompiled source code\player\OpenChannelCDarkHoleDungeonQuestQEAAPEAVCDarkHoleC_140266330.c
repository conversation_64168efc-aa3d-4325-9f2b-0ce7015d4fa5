/*
 * Function: ?OpenChannel@CDarkHoleDungeonQuest@@QEAAPEAVCDarkHoleChannel@@HPEAVCPlayer@@PEAVCDarkHole@@@Z
 * Address: 0x140266330
 */

CDarkHoleChannel *__fastcall CDarkHoleDungeonQuest::OpenChannel(CDarkHoleDungeonQuest *this, int nQuestIndex, CPlayer *pOpener, CDarkHole *pHoleObj)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CDarkHoleChannel *result; // rax@6
  __int64 v7; // [sp+0h] [bp-58h]@1
  CDarkHole *pHoleObja; // [sp+20h] [bp-38h]@11
  _dh_quest_setup *v9; // [sp+30h] [bp-28h]@4
  int nLayerIndex; // [sp+38h] [bp-20h]@7
  int v11; // [sp+3Ch] [bp-1Ch]@9
  CDarkHoleChannel *v12; // [sp+40h] [bp-18h]@11
  CDarkHoleDungeonQuest *v13; // [sp+60h] [bp+8h]@1
  int nQuestIndexa; // [sp+68h] [bp+10h]@1
  CPlayer *pOpenera; // [sp+70h] [bp+18h]@1
  CDarkHole *v16; // [sp+78h] [bp+20h]@1

  v16 = pHoleObj;
  pOpenera = pOpener;
  nQuestIndexa = nQuestIndex;
  v13 = this;
  v4 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = v13->m_QuestSetup[nQuestIndex];
  if ( !v9->bPartyOnly || CPartyPlayer::IsPartyMode(pOpener->m_pPartyMgr) )
  {
    nLayerIndex = CDarkHoleDungeonQuest::SearchEmptyDarkHoleLayer(v13, nQuestIndexa);
    if ( nLayerIndex == -1 )
    {
      result = 0i64;
    }
    else
    {
      v11 = CDarkHoleDungeonQuest::SearchEmptyDarkHoleChannel(v13);
      if ( v11 == -1 )
      {
        result = 0i64;
      }
      else
      {
        v12 = &v13->m_Channel[v11];
        _dh_quest_setup::SetRealBoss(v9, 1);
        pHoleObja = v16;
        CDarkHoleChannel::OpenDungeon(v12, v13->m_QuestSetup[nQuestIndexa], nLayerIndex, pOpenera, v16);
        result = v12;
      }
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
