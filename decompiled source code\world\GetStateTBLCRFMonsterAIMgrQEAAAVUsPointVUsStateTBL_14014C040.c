/*
 * Function: ?GetStateTBL@CRFMonsterAIMgr@@QEAA?AV?$UsPoint@VUsStateTBL@@@@H@Z
 * Address: 0x14014C040
 */

UsPoint<UsStateTBL> *__fastcall CRFMonsterAIMgr::GetStateTBL(CRFMonsterAIMgr *this, UsPoint<UsStateTBL> *result, int nIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  UsPoint<UsStateTBL> *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  CRFMonsterAIMgr *v8; // [sp+40h] [bp+8h]@1
  UsPoint<UsStateTBL> *v9; // [sp+48h] [bp+10h]@1

  v9 = result;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = 0;
  if ( nIndex >= 1 || nIndex < 0 )
  {
    UsPoint<UsStateTBL>::UsPoint<UsStateTBL>(result, 0i64);
    v5 = v9;
  }
  else
  {
    UsPoint<UsStateTBL>::UsPoint<UsStateTBL>(result, v8[nIndex].m_spStateTBLPoolPtr);
    v7 |= 1u;
    v5 = v9;
  }
  return v5;
}
