/*
 * Function: ?pc_NuclearAfterEffect@CPlayer@@QEAAXXZ
 * Address: 0x1400C5160
 */

void __fastcall CPlayer::pc_NuclearAfterEffect(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@13
  char v4; // al@14
  char v5; // al@14
  char *v6; // rax@23
  __int64 v7; // [sp+0h] [bp-78h]@1
  int v8; // [sp+20h] [bp-58h]@23
  int v9; // [sp+28h] [bp-50h]@23
  _SFCONT_DB_BASE *v10; // [sp+30h] [bp-48h]@4
  _base_fld *v11; // [sp+38h] [bp-40h]@4
  unsigned int v12; // [sp+40h] [bp-38h]@6
  int j; // [sp+44h] [bp-34h]@6
  int k; // [sp+48h] [bp-30h]@8
  _SFCONT_DB_BASE::_LIST *v15; // [sp+50h] [bp-28h]@11
  _sf_continous *pCont; // [sp+58h] [bp-20h]@14
  char v17; // [sp+60h] [bp-18h]@14
  char v18; // [sp+61h] [bp-17h]@16
  int v19; // [sp+64h] [bp-14h]@23
  int v20; // [sp+68h] [bp-10h]@23
  unsigned int v21; // [sp+6Ch] [bp-Ch]@23
  CPlayer *v22; // [sp+80h] [bp+8h]@1

  v22 = this;
  v1 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = &v22->m_pUserDB->m_AvatorData.dbSfcont;
  v11 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
  if ( v10 && v22->m_pUserDB )
  {
    v12 = _sf_continous::GetSFContCurTime();
    for ( j = 0; j < 2; ++j )
    {
      for ( k = 0; k < 8; ++k )
      {
        v15 = (_SFCONT_DB_BASE::_LIST *)((char *)v10 + 32 * j + 4 * k);
        if ( _SFCONT_DB_BASE::_LIST::IsFilled(v15) )
        {
          if ( _SFCONT_DB_BASE::_LIST::GetEffectCode(v15) == 3
            || (v3 = _SFCONT_DB_BASE::_LIST::GetEffectCode(v15), (unsigned __int8)v3 == v11->m_dwIndex) )
          {
            pCont = &v22->m_SFCont[j][k];
            pCont->m_byEffectCode = _SFCONT_DB_BASE::_LIST::GetEffectCode(v15);
            pCont->m_wEffectIndex = _SFCONT_DB_BASE::_LIST::GetEffectIndex(v15);
            v4 = _SFCONT_DB_BASE::_LIST::GetLv(v15);
            pCont->m_byLv = v4 + 1;
            pCont->m_dwStartSec = v12;
            pCont->m_wDurSec = _SFCONT_DB_BASE::_LIST::GetLeftTime(v15);
            v17 = _SFCONT_DB_BASE::_LIST::GetOrder(v15);
            v5 = _SFCONT_DB_BASE::_LIST::GetOrder(v15);
            if ( v12 > (unsigned __int8)v5 )
            {
              pCont->m_dwStartSec = v12 - (unsigned __int8)v17;
              pCont->m_wDurSec += (unsigned __int8)v17;
            }
            v18 = 0;
            if ( pCont->m_byEffectCode < 4 )
            {
              if ( CRecordData::GetRecord(&stru_1799C8410 + pCont->m_byEffectCode, pCont->m_wEffectIndex) )
              {
                if ( pCont->m_byLv > 7 )
                  v18 = 1;
              }
              else
              {
                v18 = 1;
              }
            }
            else
            {
              v18 = 1;
            }
            if ( v18 )
            {
              CUserDB::Update_SFContDelete(v22->m_pUserDB, j, k);
              v19 = pCont->m_byLv;
              v20 = pCont->m_wEffectIndex;
              v21 = pCont->m_byEffectCode;
              v6 = CPlayerDB::GetCharNameW(&v22->m_Param);
              v9 = v19;
              v8 = v20;
              CLogFile::Write(&stru_1799C8E78, "%s: error stored effect, code: %d, idx: %d: lv: %d", v6, v21);
            }
            else
            {
              CPlayer::SendMsg_StartContSF(v22, pCont);
              v22->m_bLastContEffectUpdate = 1;
            }
          }
        }
      }
    }
  }
}
