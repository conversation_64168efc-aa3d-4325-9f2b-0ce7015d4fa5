/*
 * Function: ?MissileInit@CNuclearBombMgr@@QEAA_NXZ
 * Address: 0x14013A4F0
 */

bool __fastcall CNuclearBombMgr::MissileInit(CNuclearBombMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  _object_id pID; // [sp+34h] [bp-24h]@6
  int k; // [sp+44h] [bp-14h]@6
  CNuclearBombMgr *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    _object_id::_object_id(&pID, 0, 12, j);
    for ( k = 0; k < 3; ++k )
    {
      if ( !CNuclearBomb::Init(&v8->m_Missile[j][k], &pID) )
        return 0;
    }
  }
  return CNuclearBombMgr::LoadIni(v8) != 0;
}
