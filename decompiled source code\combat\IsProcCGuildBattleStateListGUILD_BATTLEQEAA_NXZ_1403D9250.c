/*
 * Function: ?IsProc@CGuildBattleStateList@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403D9250
 */

bool __fastcall GUILD_BATTLE::CGuildBattleStateList::IsProc(GUILD_BATTLE::CGuildBattleStateList *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleStateList *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return GUILD_BATTLE::CGuildBattleStateList::STATE_NONE != v5->m_iState
      && v5->STATE_MAX >= v5->m_iState
      && GUILD_BATTLE::CGuildBattleStateList::STATE_WAIT != v5->m_iState
      && GUILD_BATTLE::CGuildBattleStateList::STATE_READY != v5->m_iState;
}
