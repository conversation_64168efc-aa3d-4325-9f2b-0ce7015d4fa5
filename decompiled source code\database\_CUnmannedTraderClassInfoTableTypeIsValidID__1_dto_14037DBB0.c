/*
 * Function: _CUnmannedTraderClassInfoTableType::IsValidID_::_1_::dtor$2
 * Address: 0x14037DBB0
 */

void __fastcall CUnmannedTraderClassInfoTableType::IsValidID_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(a2 + 104));
}
