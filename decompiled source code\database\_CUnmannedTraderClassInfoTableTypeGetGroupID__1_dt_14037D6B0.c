/*
 * Function: _CUnmannedTraderClassInfoTableType::GetGroupID_::_1_::dtor$0
 * Address: 0x14037D6B0
 */

void __fastcall CUnmannedTraderClassInfoTableType::GetGroupID_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>((std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)(a2 + 40));
}
