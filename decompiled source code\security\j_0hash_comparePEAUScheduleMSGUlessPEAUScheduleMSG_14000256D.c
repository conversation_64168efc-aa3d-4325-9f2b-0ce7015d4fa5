/*
 * Function: j_??0?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@QEAA@XZ
 * Address: 0x14000256D
 */

void __fastcall stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>(stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *this)
{
  stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>(this);
}
