/*
 * Function: ?Init@CPossibleBattleGuildListManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403C9790
 */

char __fastcall GUILD_BATTLE::CPossibleBattleGuildListManager::Init(GUILD_BATTLE::CPossibleBattleGuildListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@5
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@9
  GUILD_BATTLE::CGuildBattleLogger *v7; // rax@16
  __int64 v8; // [sp+0h] [bp-68h]@1
  unsigned int j; // [sp+20h] [bp-48h]@10
  unsigned int *v10; // [sp+28h] [bp-40h]@4
  char *v11; // [sp+30h] [bp-38h]@6
  _possible_battle_guild_list_result_zocl **v12; // [sp+38h] [bp-30h]@8
  void *v13; // [sp+40h] [bp-28h]@15
  void *__t; // [sp+48h] [bp-20h]@12
  __int64 v15; // [sp+50h] [bp-18h]@4
  void *v16; // [sp+58h] [bp-10h]@13
  GUILD_BATTLE::CPossibleBattleGuildListManager *v17; // [sp+70h] [bp+8h]@1

  v17 = this;
  v1 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v15 = -2i64;
  v10 = (unsigned int *)operator new[](0xCui64);
  v17->m_pdwVer = v10;
  if ( v17->m_pdwVer )
  {
    memset_0(v17->m_pdwVer, 0, 0xCui64);
    v11 = (char *)operator new[](3ui64);
    v17->m_pMaxPage = v11;
    if ( v17->m_pdwVer )
    {
      memset_0(v17->m_pMaxPage, 0, 3ui64);
      v12 = (_possible_battle_guild_list_result_zocl **)operator new[](0x18ui64);
      v17->m_ppkList = v12;
      if ( v17->m_ppkList )
      {
        for ( j = 0; (signed int)j < 3; ++j )
        {
          __t = operator new[](0x3BC4ui64);
          if ( __t )
          {
            `vector constructor iterator'(
              __t,
              0xCCui64,
              75,
              (void *(__cdecl *)(void *))_possible_battle_guild_list_result_zocl::_possible_battle_guild_list_result_zocl);
            v16 = __t;
          }
          else
          {
            v16 = 0i64;
          }
          v13 = v16;
          v17->m_ppkList[j] = (_possible_battle_guild_list_result_zocl *)v16;
          if ( !v17->m_ppkList[j] )
          {
            v7 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v7,
              "CPossibleBattleGuildListManager::Init()m_ppkList[%d] = new _possible_battle_guild_list_result_zocl[%u] Fail",
              j,
              75i64);
            return 0;
          }
          memset_0(v17->m_ppkList[j], 0, 0x3BC4ui64);
        }
        v17->m_bInit = 1;
        result = 1;
      }
      else
      {
        v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v6,
          "CPossibleBattleGuildListManager::Init()m_ppkList = new _possible_battle_guild_list_result_zocl * [%u] Fail",
          3i64);
        result = 0;
      }
    }
    else
    {
      v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v5,
        "CPossibleBattleGuildListManager::Init()m_pMaxPage == new BYTE[%u] NULL!",
        3i64);
      result = 0;
    }
  }
  else
  {
    v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v3,
      "CPossibleBattleGuildListManager::Init()m_pdwVer == new DWORD[%u] NULL!",
      3i64);
    result = 0;
  }
  return result;
}
