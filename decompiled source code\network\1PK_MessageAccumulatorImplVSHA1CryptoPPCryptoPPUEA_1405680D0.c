/*
 * Function: ??1?$PK_MessageAccumulatorImpl@VSHA1@CryptoPP@@@CryptoPP@@UEAA@XZ
 * Address: 0x1405680D0
 */

void __fastcall CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::~PK_MessageAccumulatorImpl<CryptoPP::SHA1>(CryptoPP::PK_MessageAccumulatorBase *a1)
{
  CryptoPP::PK_MessageAccumulatorBase *v1; // [sp+40h] [bp+8h]@1

  v1 = a1;
  if ( a1 )
    CryptoPP::ObjectHolder<CryptoPP::SHA1>::~ObjectHolder<CryptoPP::SHA1>(&a1[1]);
  else
    CryptoPP::ObjectHolder<CryptoPP::SHA1>::~ObjectHolder<CryptoPP::SHA1>(0i64);
  CryptoPP::PK_MessageAccumulatorBase::~PK_MessageAccumulatorBase(v1);
}
