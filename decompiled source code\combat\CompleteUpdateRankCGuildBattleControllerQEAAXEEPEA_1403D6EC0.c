/*
 * Function: ?CompleteUpdateRank@CGuildBattleController@@QEAAXEEPEAE@Z
 * Address: 0x1403D6EC0
 */

void __fastcall CGuildBattleController::CompleteUpdateRank(CGuildBattleController *this, char byR<PERSON><PERSON>, char byRace, char *pLoadData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@5
  GUILD_BATTLE::CGuildBattleRankManager *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned int v9; // [sp+20h] [bp-18h]@5
  unsigned int v10; // [sp+24h] [bp-14h]@5
  char v11; // [sp+50h] [bp+18h]@1
  char *pLoadDataa; // [sp+58h] [bp+20h]@1

  pLoadDataa = pLoadData;
  v11 = byRace;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( byResult )
  {
    v9 = (unsigned __int8)byRace;
    v10 = (unsigned __int8)byResult;
    v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v6,
      "CGuildBattleController::CompleteUpdateRank( BYTE byResult(%u), BYTE byRace(%u), BYTE * pLoadData ) : Fail!",
      v10,
      v9);
  }
  else
  {
    v7 = GUILD_BATTLE::CGuildBattleRankManager::Instance();
    GUILD_BATTLE::CGuildBattleRankManager::Update(v7, v11, pLoadDataa);
  }
}
