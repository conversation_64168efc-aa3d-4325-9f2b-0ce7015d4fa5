/*
 * Function: ?SearchCharacterPath@DfAIMgr@@SA_NPEAVCMonsterAI@@PEAVCMonster@@PEAVCCharacter@@@Z
 * Address: 0x140153070
 */

char __fastcall DfAIMgr::SearchCharacterPath(CMonsterAI *pAI, CMonster *pMon, CCharacter *pTarget)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPathMgr *v5; // rax@4
  CPathMgr *v6; // rax@6
  char result; // al@9
  CPathMgr *v8; // rax@12
  CPathMgr *v9; // rax@14
  CPathMgr *v10; // rax@15
  CPathMgr *v11; // rax@16
  CPathMgr *v12; // rax@18
  CPathMgr *v13; // rax@19
  __int64 v14; // [sp+0h] [bp-C8h]@1
  char v15; // [sp+20h] [bp-A8h]@7
  float v16[3]; // [sp+38h] [bp-90h]@13
  int v17; // [sp+54h] [bp-74h]@15
  float pPos; // [sp+68h] [bp-60h]@16
  float pTarPos; // [sp+98h] [bp-30h]@19
  float *vTarPos; // [sp+B8h] [bp-10h]@18
  CMonsterAI *v21; // [sp+D0h] [bp+8h]@1
  CMonster *pMona; // [sp+D8h] [bp+10h]@1
  CCharacter *pTargetCharacter; // [sp+E0h] [bp+18h]@1

  pTargetCharacter = pTarget;
  pMona = pMon;
  v21 = pAI;
  v3 = &v14;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CMonsterAI::GetPathFinder(v21);
  if ( (signed int)(unsigned __int8)CPathMgr::GetPathSize(v5) > 0
    && (pMona->m_bMove
     || (v6 = CMonsterAI::GetPathFinder(v21), (signed int)(unsigned __int8)CPathMgr::GetPathSize(v6) <= 0)) )
  {
    result = 1;
  }
  else
  {
    v15 = 0;
    if ( pTargetCharacter->m_bMove )
    {
      if ( !CMonsterHelper::SearchTargetMovePos_MovingTarget(pMona, pTargetCharacter, (float (*)[3])vTargetPos) )
        return 0;
    }
    else if ( !CMonsterHelper::SearchTargetMovePos_StopTarget(pMona, pTargetCharacter, (float (*)[3])vTargetPos) )
    {
      v8 = CMonsterAI::GetPathFinder(v21);
      CPathMgr::Init(v8);
      return 0;
    }
    if ( (unsigned int)CBsp::CanYouGoThere(
                         pMona->m_pCurMap->m_Level.mBsp,
                         pMona->m_fCurPos,
                         vTargetPos,
                         (float (*)[3])v16) )
    {
      DfAIMgr::ChangeTargetPos(pMona, vTargetPos);
      v9 = CMonsterAI::GetPathFinder(v21);
      CPathMgr::Init(v9);
      result = 1;
    }
    else
    {
      v10 = CMonsterAI::GetPathFinder(v21);
      v17 = CPathMgr::SearchPathA(v10, pMona, vTargetPos, 0);
      if ( v17 >= 2 && (v11 = CMonsterAI::GetPathFinder(v21), CPathMgr::PopNextPath(v11, &pPos)) )
      {
        DfAIMgr::ChangeTargetPos(pMona, &pPos);
        result = 1;
      }
      else
      {
        vTarPos = pTargetCharacter->m_fCurPos;
        v12 = CMonsterAI::GetPathFinder(v21);
        v17 = CPathMgr::SearchPathA(v12, pMona, vTarPos, 0);
        if ( v17 >= 1 && (v13 = CMonsterAI::GetPathFinder(v21), CPathMgr::PopNextPath(v13, &pTarPos)) )
        {
          DfAIMgr::ChangeTargetPos(pMona, &pTarPos);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
    }
  }
  return result;
}
