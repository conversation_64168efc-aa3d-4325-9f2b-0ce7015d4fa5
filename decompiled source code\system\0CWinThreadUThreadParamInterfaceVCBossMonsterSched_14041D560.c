/*
 * Function: ??0?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@QEAA@XZ
 * Address: 0x14041D560
 */

void __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  US::AbstractThread::AbstractThread((US::AbstractThread *)&v5->vfptr);
  v5->vfptr = (US::AbstractThreadVtbl *)&US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::`vftable';
  v5->m_bRunning = 0;
  US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>(&v5->m_ThreadParam);
  v5->m_hThread = 0i64;
  v5->m_hStartupEvent = 0i64;
  v5->m_hDestroyEvent = 0i64;
  v5->m_dwThreadID = -1;
}
