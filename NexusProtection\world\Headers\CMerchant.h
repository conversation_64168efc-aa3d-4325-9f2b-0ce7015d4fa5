#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <chrono>
#include <windows.h>

// Forward declarations
class CCharacter;
class CItemStore;

// Structure definitions for NPC creation data
struct _character_create_setdata {
    // Character creation data fields
    uint32_t characterType;
    float position[3];
    uint32_t flags;
    // Additional fields would be defined based on actual structure
};

struct _npc_create_setdata {
    _character_create_setdata m_pRecordSet;
    CItemStore* m_pLinkItemStore;
    uint8_t m_byRaceCode;
    uint32_t m_dwFlags;
    // Additional fields would be defined based on actual structure
};

/**
 * @brief CMerchant - NPC merchant class for handling shop functionality
 * 
 * This class represents a merchant NPC that can sell items to players.
 * It inherits from CCharacter and adds merchant-specific functionality
 * including item store management, NPC lifecycle, and messaging.
 * 
 * Key features:
 * - NPC creation and destruction
 * - Item store management
 * - Merchant-specific messaging
 * - Live merchant tracking
 * - Empty slot management for merchant pools
 */
class CMerchant : public CCharacter {
public:
    // Constructor and Destructor
    CMerchant();
    virtual ~CMerchant();

    // Core merchant functionality
    
    /**
     * @brief Create a merchant NPC with specified data
     * @param pData NPC creation data containing position, race, and store info
     * @return true if creation was successful, false otherwise
     */
    bool Create(_npc_create_setdata* pData);

    /**
     * @brief Destroy the merchant and clean up resources
     * @return true if destruction was successful
     */
    bool Destroy();

    /**
     * @brief Update merchant state and behavior
     * Called periodically to handle merchant logic
     */
    void Update();

    // Merchant properties and state management

    /**
     * @brief Check if the merchant is currently alive/active
     * @return true if merchant is alive and functional
     */
    bool IsLive() const { return m_bLive; }

    /**
     * @brief Set the live state of the merchant
     * @param bLive New live state
     */
    void SetLive(bool bLive) { m_bLive = bLive; }

    /**
     * @brief Get the merchant's object serial number
     * @return Unique serial number for this merchant
     */
    uint32_t GetObjSerial() const { return m_dwObjSerial; }

    /**
     * @brief Get the merchant's race code
     * @return Race code identifying the merchant type
     */
    uint8_t GetRaceCode() const { return m_byRaceCode; }

    /**
     * @brief Get the associated item store
     * @return Pointer to the merchant's item store
     */
    CItemStore* GetItemStore() const { return m_pItemStore; }

    /**
     * @brief Get the last destroy time
     * @return Timestamp when the merchant was last destroyed
     */
    uint32_t GetLastDestroyTime() const { return m_dwLastDestroyTime; }

    /**
     * @brief Set the last destroy time
     * @param dwTime Timestamp to set
     */
    void SetLastDestroyTime(uint32_t dwTime) { m_dwLastDestroyTime = dwTime; }

    // Static utility functions

    /**
     * @brief Get a new unique serial number for merchants
     * @return New unique serial number
     */
    static uint32_t GetNewMonSerial();

    /**
     * @brief Get the current number of live merchants
     * @return Number of currently active merchants
     */
    static int GetLiveNum() { return s_nLiveNum; }

    /**
     * @brief Send creation message for the merchant
     * Notifies the game system that a new merchant has been created
     */
    void SendMsg_Create();

    /**
     * @brief Send destruction message for the merchant
     * Notifies the game system that a merchant has been destroyed
     */
    void SendMsg_Destroy();

    // Merchant interaction methods

    /**
     * @brief Handle player interaction with the merchant
     * @param pPlayer Pointer to the interacting player
     * @return true if interaction was successful
     */
    bool OnPlayerInteract(void* pPlayer);

    /**
     * @brief Open the merchant's shop for a player
     * @param pPlayer Pointer to the player opening the shop
     * @return true if shop was opened successfully
     */
    bool OpenShop(void* pPlayer);

    /**
     * @brief Close the merchant's shop for a player
     * @param pPlayer Pointer to the player closing the shop
     */
    void CloseShop(void* pPlayer);

    // Validation and utility methods

    /**
     * @brief Validate merchant data integrity
     * @return true if merchant data is valid
     */
    bool ValidateData() const;

    /**
     * @brief Reset merchant to default state
     */
    void Reset();

    /**
     * @brief Check if merchant can be destroyed
     * @return true if merchant can be safely destroyed
     */
    bool CanDestroy() const;

protected:
    // Protected member variables
    bool m_bLive;                    ///< Whether the merchant is currently alive
    uint32_t m_dwObjSerial;          ///< Unique object serial number
    uint8_t m_byRaceCode;            ///< Race code for the merchant type
    CItemStore* m_pItemStore;        ///< Pointer to the merchant's item store
    uint32_t m_dwLastDestroyTime;    ///< Timestamp of last destruction
    uint32_t m_dwCreateTime;         ///< Timestamp when merchant was created
    uint32_t m_dwLastUpdateTime;     ///< Timestamp of last update
    
    // Static member variables
    static int s_nLiveNum;           ///< Global count of live merchants
    static uint32_t s_dwSerialCounter; ///< Counter for generating unique serials

    // Protected utility methods

    /**
     * @brief Initialize merchant with default values
     */
    void InitializeDefaults();

    /**
     * @brief Clean up merchant resources
     */
    void CleanupResources();

    /**
     * @brief Validate creation data
     * @param pData Creation data to validate
     * @return true if data is valid
     */
    bool ValidateCreationData(const _npc_create_setdata* pData) const;

    /**
     * @brief Log merchant operation
     * @param operation Name of the operation
     * @param details Additional details
     */
    void LogOperation(const char* operation, const char* details = nullptr) const;

    /**
     * @brief Log merchant error
     * @param errorMessage Error message
     * @param context Context where error occurred
     */
    void LogError(const char* errorMessage, const char* context = nullptr) const;

private:
    // Private copy constructor and assignment operator to prevent copying
    CMerchant(const CMerchant&) = delete;
    CMerchant& operator=(const CMerchant&) = delete;
};

/**
 * @brief Utility functions for merchant management
 */
namespace CMerchantUtils {
    /**
     * @brief Find an empty merchant slot in an array
     * @param pList Array of merchants to search
     * @param nMax Maximum number of merchants in the array
     * @return Pointer to empty merchant slot, or nullptr if none found
     */
    CMerchant* FindEmptyNPC(CMerchant* pList, int nMax);

    /**
     * @brief Get current system time in milliseconds
     * @return Current time in milliseconds
     */
    uint32_t GetCurrentTime();

    /**
     * @brief Check if enough time has passed since last destroy
     * @param dwLastDestroyTime Last destroy timestamp
     * @param dwMinInterval Minimum interval in milliseconds
     * @return true if enough time has passed
     */
    bool HasEnoughTimePassed(uint32_t dwLastDestroyTime, uint32_t dwMinInterval = 30000);

    /**
     * @brief Log merchant utility operation
     * @param operation Name of the operation
     * @param details Additional details
     */
    void LogOperation(const char* operation, const char* details = nullptr);

    /**
     * @brief Log merchant utility error
     * @param errorMessage Error message
     * @param context Context where error occurred
     */
    void LogError(const char* errorMessage, const char* context = nullptr);
}
