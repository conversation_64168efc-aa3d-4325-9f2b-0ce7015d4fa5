/*
 * Function: ?Clear@BossSchedule_Map@@QEAAXXZ
 * Address: 0x14041B4D0
 */

void __fastcall BossSchedule_Map::Clear(BossSchedule_Map *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@5
  BossSchedule *v5; // [sp+28h] [bp-30h]@8
  BossSchedule *v6; // [sp+30h] [bp-28h]@8
  void *v7; // [sp+38h] [bp-20h]@12
  void *v8; // [sp+40h] [bp-18h]@9
  BossSchedule_Map *v9; // [sp+60h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v9->m_ScheduleList )
  {
    for ( j = 0; j < v9->m_nCount; ++j )
    {
      if ( v9->m_ScheduleList[j] )
      {
        v6 = v9->m_ScheduleList[j];
        v5 = v6;
        if ( v6 )
          v8 = BossSchedule::`scalar deleting destructor'(v5, 1u);
        else
          v8 = 0i64;
      }
    }
    v7 = v9->m_ScheduleList;
    operator delete[](v7);
    v9->m_ScheduleList = 0i64;
  }
}
