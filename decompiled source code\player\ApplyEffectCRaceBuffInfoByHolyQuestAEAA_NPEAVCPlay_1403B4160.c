/*
 * Function: ?ApplyEffect@CRaceBuffInfoByHolyQuest@@AEAA_NPEAVCPlayer@@_N@Z
 * Address: 0x1403B4160
 */

char __fastcall CRaceBuffInfoByHolyQuest::ApplyEffect(CRaceBuffInfoByHolyQuest *this, CPlayer *pkDest, bool bAdd)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@8
  int *v8; // [sp+28h] [bp-20h]@10
  int v9; // [sp+30h] [bp-18h]@11
  CRaceBuffInfoByHolyQuest *v10; // [sp+50h] [bp+8h]@1
  CPlayer *v11; // [sp+58h] [bp+10h]@1
  bool v12; // [sp+60h] [bp+18h]@1

  v12 = bAdd;
  v11 = pkDest;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v10->m_pData && v10->m_byLv < 7 && pkDest )
  {
    for ( j = 0; j < 5; ++j )
    {
      v8 = &v10->m_pData->m_ContParamList[j].m_nContParamCode;
      if ( *v8 == -1 )
        break;
      v9 = *v8;
      if ( v9 )
      {
        if ( v9 == 1 )
        {
          _effect_parameter::SetEff_Plus(&v11->m_EP, v8[1], *(float *)&v8[v10->m_byLv + 1], v12);
        }
        else if ( v9 == 2 )
        {
          _effect_parameter::SetEff_State(&v11->m_EP, v8[1], v12);
        }
      }
      else
      {
        _effect_parameter::SetEff_Rate(&v11->m_EP, v8[1], *(float *)&v8[v10->m_byLv + 1], v12);
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
