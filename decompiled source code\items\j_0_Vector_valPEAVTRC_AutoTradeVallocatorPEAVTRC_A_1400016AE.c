/*
 * Function: j_??0?$_Vector_val@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@IEAA@V?$allocator@PEAVTRC_AutoTrade@@@1@@Z
 * Address: 0x1400016AE
 */

void __fastcall std::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::allocator<TRC_AutoTrade *> _Al)
{
  std::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    this,
    _<PERSON>);
}
