/*
 * Function: ??0_npc_create_setdata@@QEAA@XZ
 * Address: 0x140199140
 */

void __fastcall _npc_create_setdata::_npc_create_setdata(_npc_create_setdata *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _npc_create_setdata *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _character_create_setdata::_character_create_setdata((_character_create_setdata *)&v4->m_pRecordSet);
  v4->m_pLinkItemStore = 0i64;
  v4->m_byRaceCode = -1;
}
