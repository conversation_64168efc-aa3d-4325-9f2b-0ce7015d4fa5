/*
 * Function: ?IsBuy@CItemStore@@QEAAEEPEAU_sell_offer@@ME@Z
 * Address: 0x140261C50
 */

char __usercall CItemStore::IsBuy@<al>(CItemStore *this@<rcx>, char by<PERSON><PERSON><PERSON><PERSON>@<dl>, _sell_offer *pOffer@<r8>, float fDiscountRate@<xmm3>, float a5@<xmm0>, char byRace)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  float v9; // xmm1_4@6
  double v10; // xmm0_8@6
  int v11; // ecx@13
  signed __int64 v12; // rax@14
  float v13; // xmm0_4@14
  int v14; // ecx@19
  bool v15; // zf@38
  __int64 v16; // [sp+0h] [bp-B8h]@1
  double v17; // [sp+20h] [bp-98h]@6
  double v18; // [sp+28h] [bp-90h]@6
  int v19; // [sp+30h] [bp-88h]@6
  unsigned int v20; // [sp+34h] [bp-84h]@6
  int j; // [sp+38h] [bp-80h]@6
  char pbyMoneyKind; // [sp+44h] [bp-74h]@13
  int v23; // [sp+54h] [bp-64h]@13
  unsigned int v24; // [sp+58h] [bp-60h]@13
  int v25; // [sp+5Ch] [bp-5Ch]@14
  double v26; // [sp+60h] [bp-58h]@16
  int v27; // [sp+68h] [bp-50h]@18
  unsigned __int64 v28; // [sp+70h] [bp-48h]@18
  unsigned __int64 v29; // [sp+78h] [bp-40h]@20
  unsigned int v30; // [sp+80h] [bp-38h]@21
  __int64 v31; // [sp+88h] [bp-30h]@22
  int v32; // [sp+90h] [bp-28h]@37
  unsigned __int64 v33; // [sp+98h] [bp-20h]@37
  int nTableCode; // [sp+A0h] [bp-18h]@13
  int v35; // [sp+A4h] [bp-14h]@19
  __int64 v36; // [sp+A8h] [bp-10h]@37
  CItemStore *v37; // [sp+C0h] [bp+8h]@1
  char v38; // [sp+C8h] [bp+10h]@1
  _sell_offer *v39; // [sp+D0h] [bp+18h]@1
  float v40; // [sp+D8h] [bp+20h]@1

  v40 = fDiscountRate;
  v39 = pOffer;
  v38 = byOfferNum;
  v37 = this;
  v6 = &v16;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v37->m_pStorageItem )
  {
    CItemStore::SetZeroTradeMoney(v37);
    eGetTex((unsigned __int8)byRace);
    v9 = 1.0 - a5;
    v10 = (float)(1.0 - a5);
    v17 = v9;
    eGetOreRate((unsigned __int8)byRace);
    v18 = *(float *)&v10;
    v19 = (signed int)floor(*(float *)&v10 * 10000.0);
    v20 = 10000 - eGetTexRate((unsigned __int8)byRace);
    for ( j = 0; j < (unsigned __int8)v38; ++j )
    {
      if ( v39[j].pItem->m_byTableCode == 19 )
        return 15;
    }
    for ( j = 0; j < (unsigned __int8)v38; ++j )
    {
      v11 = v39[j].pItem->m_wItemIndex;
      nTableCode = v39[j].pItem->m_byTableCode;
      v23 = GetItemStdPrice(nTableCode, v11, (unsigned __int8)byRace, &pbyMoneyKind);
      v24 = (unsigned int)v39[j].byAmount * v23 / 2;
      if ( IsAbrItem(v39[j].pItem->m_byTableCode, v39[j].pItem->m_wItemIndex) )
      {
        v25 = GetItemDurPoint(v39[j].pItem->m_byTableCode, v39[j].pItem->m_wItemIndex);
        v12 = v39[j].pItem->m_dwDur;
        v13 = (float)(signed int)v12;
        if ( v12 < 0 )
          v13 = v13 + 1.8446744e19;
        v26 = (float)(v13 / (float)v25);
        if ( v26 > 1.0 )
          v26 = DOUBLE_1_0;
        v27 = (signed int)floor(v26 * 10000.0);
        v28 = (unsigned int)v27 * (unsigned __int64)v24;
        v24 = v28 / 0x2710;
      }
      v14 = v39[j].pItem->m_wItemIndex;
      v35 = v39[j].pItem->m_byTableCode;
      if ( IsItemEquipCivil(v35, v14, 2 * byRace) )
      {
        v29 = v20 * (unsigned __int64)v24;
        v24 = v29 / 0x2710;
      }
      v30 = v24;
      if ( v39[j].pItem->m_byTableCode == 17 )
      {
        v31 = v19 * v30;
        v30 = v19 * v30 / 0x2710;
      }
      if ( pbyMoneyKind && pbyMoneyKind != 2 && pbyMoneyKind != 4 && pbyMoneyKind != 5 && pbyMoneyKind != 6 )
      {
        if ( pbyMoneyKind == 1 || pbyMoneyKind == 3 )
          v37->m_dwLastTradeGold += v30;
      }
      else
      {
        v37->m_dwLastTradeDalant += v30;
      }
    }
    if ( fDiscountRate > 0.0 && v37->m_dwLastTradeDalant > 1 )
    {
      if ( fDiscountRate > 1.0 )
        v40 = FLOAT_1_0;
      v32 = (signed int)ffloor(v40 * 10000.0);
      v33 = v32 * v37->m_dwLastTradeDalant;
      v36 = v37->m_dwLastTradeDalant;
      v37->m_dwLastTradeDalant = v33 / 0x2710 + v36;
    }
    v15 = v37->m_dwLastTradeDalant == 0;
    v15 = v37->m_dwLastTradeGold == 0;
    result = 0;
  }
  else
  {
    result = 100;
  }
  return result;
}
