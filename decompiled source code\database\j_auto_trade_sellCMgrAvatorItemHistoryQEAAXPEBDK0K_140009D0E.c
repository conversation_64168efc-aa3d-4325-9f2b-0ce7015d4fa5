/*
 * Function: j_?auto_trade_sell@CMgrAvatorItemHistory@@QEAAXPEBDK0KPEAU_db_con@_STORAGE_LIST@@KKKKPEAD@Z
 * Address: 0x140009D0E
 */

void __fastcall CMgrAvatorItemHistory::auto_trade_sell(CMgrAvatorItemHistory *this, const char *szBuyerName, unsigned int dwBuyerSerial, const char *szBuyerID, unsigned int dwRegistSerial, _STORAGE_LIST::_db_con *pItem, unsigned int dwPrice, unsigned int dwTax, unsigned int dwLeftDalant, unsigned int dwLeftGold, char *pszFileName)
{
  CMgrAvatorItemHistory::auto_trade_sell(
    this,
    szBuyerName,
    dwBuyerSerial,
    szBuyerID,
    dwRegistSerial,
    pItem,
    dwPrice,
    dwTax,
    dwLeftDalant,
    dwLeftGold,
    pszFileName);
}
