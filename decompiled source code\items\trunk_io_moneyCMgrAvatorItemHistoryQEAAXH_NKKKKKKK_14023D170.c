/*
 * Function: ?trunk_io_money@CMgrAvatorItemHistory@@QEAAXH_NKKKKKKKPEAD@Z
 * Address: 0x14023D170
 */

void __fastcall CMgrAvatorItemHistory::trunk_io_money(CMgrAvatorItemHistory *this, int n, bool bInput, unsigned int dwIODalant, unsigned int dwIOGold, unsigned int dwPayDalant, unsigned int dwInvenDalant, unsigned int dwInvenGold, unsigned int dwTrkDalant, unsigned int dwTrkGold, char *pszFileName)
{
  __int64 *v11; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v13; // [sp+0h] [bp-98h]@1
  unsigned int v14; // [sp+20h] [bp-78h]@4
  unsigned int v15; // [sp+28h] [bp-70h]@4
  unsigned int v16; // [sp+30h] [bp-68h]@4
  unsigned int v17; // [sp+38h] [bp-60h]@4
  unsigned int v18; // [sp+40h] [bp-58h]@4
  unsigned int v19; // [sp+48h] [bp-50h]@4
  char *v20; // [sp+50h] [bp-48h]@4
  char *v21; // [sp+58h] [bp-40h]@4
  const char *v22; // [sp+68h] [bp-30h]@4
  const char *v23; // [sp+70h] [bp-28h]@4
  int v24; // [sp+84h] [bp-14h]@4
  CMgrAvatorItemHistory *v25; // [sp+A0h] [bp+8h]@1

  v25 = this;
  v11 = &v13;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  v22 = "OUT";
  v23 = "IN";
  v24 = bInput != 0;
  v21 = v25->m_szCurTime;
  v20 = v25->m_szCurDate;
  v19 = dwTrkGold;
  v18 = dwTrkDalant;
  v17 = dwInvenGold;
  v16 = dwInvenDalant;
  v15 = dwPayDalant;
  v14 = dwIOGold;
  sprintf(
    sData,
    "TRUNK MONEY %s: io(D:%u, G:%u) pay(%u) $D:%u $G:%u \t^D%u ^G:%u [%s %s]\r\n",
    (&v22)[8 * (bInput != 0)],
    dwIODalant);
  CMgrAvatorItemHistory::WriteFile(v25, pszFileName, sData);
}
