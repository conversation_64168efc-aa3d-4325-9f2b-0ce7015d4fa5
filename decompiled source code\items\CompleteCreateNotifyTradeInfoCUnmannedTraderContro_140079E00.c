/*
 * Function: ?CompleteCreateNotifyTradeInfo@CUnmannedTraderController@@QEAAXEG@Z
 * Address: 0x140079E00
 */

void __fastcall CUnmannedTraderController::CompleteCreateNotifyTradeInfo(CUnmannedTraderController *this, char by<PERSON><PERSON>, unsigned __int16 wInx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderController *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CUnmannedTraderTradeInfo::NotifyIncome(&v6->m_kTradeInfo, byRace, wInx);
}
