/*
 * Function: ?CompleteItemChargeRefund@PatriarchElectProcessor@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1402BBBA0
 */

void __fastcall PatriarchElectProcessor::CompleteItemChargeRefund(PatriarchElectProcessor *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@5
  __int64 v6; // [sp+28h] [bp-20h]@5
  char *v7; // [sp+30h] [bp-18h]@4
  PatriarchElectProcessor *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = pData->m_sData;
  if ( pData->m_byResult )
  {
    v6 = *((_QWORD *)v7 + 1);
    v5 = *((_DWORD *)v7 + 1);
    CLogFile::Write(&v8->_kSysLog, "FAILED DB_RET(%s_%d):%d-Cost:%d", "Item Cahrge for Refund", v8->_dwElectSerial);
  }
}
