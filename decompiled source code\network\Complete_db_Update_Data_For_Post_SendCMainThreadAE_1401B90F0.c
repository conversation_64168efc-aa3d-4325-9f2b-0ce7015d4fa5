/*
 * Function: ?Complete_db_Update_Data_For_Post_Send@CMainThread@@AEAAXPEAD@Z
 * Address: 0x1401B90F0
 */

void __fastcall CMainThread::Complete_db_Update_Data_For_Post_Send(CMainThread *this, char *pSheet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char *v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pSheet;
  v6 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)pSheet);
  if ( v6 )
  {
    if ( v6->m_bOper )
    {
      v6->m_pUserDB->m_AvatorData_bk.dbAvator.m_dwGold = v6->m_pUserDB->m_AvatorData.dbAvator.m_dwGold;
      _INVEN_DB_BASE::operator=(&v6->m_pUserDB->m_AvatorData_bk.dbInven, &v6->m_pUserDB->m_AvatorData.dbInven);
      CPlayer::SendMsg_PostSendReply(v6, 0);
    }
  }
}
