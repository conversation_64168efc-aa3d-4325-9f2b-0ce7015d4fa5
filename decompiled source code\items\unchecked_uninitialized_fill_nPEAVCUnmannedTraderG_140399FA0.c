/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderGroupDivisionVersionInfo@@_KV1@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderGroupDivisionVersionInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@Z
 * Address: 0x140399FA0
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned __int64,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(CUnmannedTraderGroupDivisionVersionInfo *_First, unsigned __int64 _Count, CUnmannedTraderGroupDivisionVersionInfo *_Val, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  CUnmannedTraderGroupDivisionVersionInfo *__formal; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  CUnmannedTraderGroupDivisionVersionInfo *_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Vala = _Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(
         &__formal,
         &__formal);
  std::_Uninit_fill_n<CUnmannedTraderGroupDivisionVersionInfo *,unsigned __int64,CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>(
    __formal,
    _Counta,
    _Vala,
    _Ala,
    v8,
    v7);
}
