#pragma once

/**
 * @file BossSchedule_Map.h
 * @brief Boss Schedule Map management class for NexusProtection
 * @details Manages boss monster scheduling and timing for game maps
 * <AUTHOR> Development Team
 * @date 2025
 * @version 1.0
 */

#include <memory>
#include <vector>
#include <cstdint>

// Forward declarations
class CIniFile;
class BossSchedule;
class CBossMonsterScheduleSystem;
struct INI_Section;

namespace NexusProtection {
namespace World {

/**
 * @class BossSchedule_Map
 * @brief Manages boss monster schedules for a specific map
 * 
 * This class handles the loading, saving, and management of boss monster
 * schedules for individual game maps. It provides functionality to:
 * - Load schedule data from configuration files
 * - Save schedule data back to configuration files
 * - Manage multiple boss schedules per map
 * - Clear and reset schedule data
 */
class BossSchedule_Map {
public:
    /**
     * @brief Default constructor
     * Initializes the BossSchedule_Map with default values
     */
    BossSchedule_Map();

    /**
     * @brief Destructor
     * Cleans up all allocated resources and schedules
     */
    ~BossSchedule_Map();

    /**
     * @brief Copy constructor (deleted)
     * BossSchedule_Map objects should not be copied
     */
    BossSchedule_Map(const BossSchedule_Map&) = delete;

    /**
     * @brief Copy assignment operator (deleted)
     * BossSchedule_Map objects should not be copied
     */
    BossSchedule_Map& operator=(const BossSchedule_Map&) = delete;

    /**
     * @brief Move constructor
     * @param other The BossSchedule_Map to move from
     */
    BossSchedule_Map(BossSchedule_Map&& other) noexcept;

    /**
     * @brief Move assignment operator
     * @param other The BossSchedule_Map to move from
     * @return Reference to this object
     */
    BossSchedule_Map& operator=(BossSchedule_Map&& other) noexcept;

    /**
     * @brief Load all boss schedules from configuration
     * @return true if loading was successful, false otherwise
     * @details Reads schedule data from the associated INI file and
     *          creates BossSchedule objects for each section
     */
    bool LoadAll();

    /**
     * @brief Save all boss schedules to configuration
     * @return true if saving was successful, false otherwise
     * @details Writes all current schedule data back to the INI file
     */
    bool SaveAll();

    /**
     * @brief Clear all boss schedules
     * @details Removes all loaded schedules and frees associated memory
     */
    void Clear();

    /**
     * @brief Get the number of loaded schedules
     * @return The count of currently loaded boss schedules
     */
    [[nodiscard]] std::size_t GetScheduleCount() const noexcept;

    /**
     * @brief Get a specific boss schedule by index
     * @param index The index of the schedule to retrieve
     * @return Pointer to the BossSchedule object, or nullptr if index is invalid
     */
    [[nodiscard]] BossSchedule* GetSchedule(std::size_t index) const noexcept;

    /**
     * @brief Check if the schedule map is empty
     * @return true if no schedules are loaded, false otherwise
     */
    [[nodiscard]] bool IsEmpty() const noexcept;

private:
    /// Configuration file handler for schedule data
    std::unique_ptr<CIniFile> m_iniFile;
    
    /// Array of boss schedule pointers
    std::vector<std::unique_ptr<BossSchedule>> m_scheduleList;
    
    /// Number of loaded schedules
    std::size_t m_scheduleCount;
    
    /// Pointer to the boss monster schedule system
    CBossMonsterScheduleSystem* m_system;

    /**
     * @brief Initialize internal data structures
     * @details Sets up default values for all member variables
     */
    void Initialize();

    /**
     * @brief Cleanup internal resources
     * @details Safely releases all allocated memory and resets state
     */
    void Cleanup();
};

} // namespace World
} // namespace NexusProtection
