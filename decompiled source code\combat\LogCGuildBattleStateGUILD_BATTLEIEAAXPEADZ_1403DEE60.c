/*
 * Function: ?Log@CGuildBattleState@GUILD_BATTLE@@IEAAXPEAD@Z
 * Address: 0x1403DEE60
 */

void __fastcall GUILD_BATTLE::CGuildBattleState::Log(GUILD_BATTLE::CGuildBattleState *this, char *szMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  const char *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-1A8h]@1
  char *v6; // [sp+20h] [bp-188h]@4
  char Buf; // [sp+40h] [bp-168h]@4
  char v8; // [sp+E0h] [bp-C8h]@4
  char v9; // [sp+178h] [bp-30h]@4
  __int64 v10; // [sp+190h] [bp-18h]@4
  unsigned __int64 v11; // [sp+198h] [bp-10h]@4
  char *v12; // [sp+1B8h] [bp+10h]@1

  v12 = szMsg;
  v2 = &v5;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  _strdate_s(&Buf, 0x80ui64);
  _strtime_s(&v8, 0x80ui64);
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>(&v9);
  v6 = v12;
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::Format(&v9, "%s %s : %s\n", &Buf);
  LODWORD(v4) = ATL::CSimpleStringT<char,1>::operator char const *(&v9);
  OutputDebugStringA(v4);
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>::~CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char>>>(&v9);
}
