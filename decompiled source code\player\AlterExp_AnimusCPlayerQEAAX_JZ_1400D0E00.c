/*
 * Function: ?AlterExp_Animus@CPlayer@@QEAAX_J@Z
 * Address: 0x1400D0E00
 */

void __fastcall CPlayer::AlterExp_Animus(CPlayer *this, __int64 nAlterExp)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v4; // rcx@10
  _STORAGE_LIST::_db_con *v5; // rax@12
  _STORAGE_LIST::_db_con *v6; // rcx@12
  __int64 v7; // [sp+0h] [bp-58h]@1
  char byReason[8]; // [sp+20h] [bp-38h]@10
  unsigned __int64 dw64OldExp; // [sp+40h] [bp-18h]@7
  CPlayer *v10; // [sp+60h] [bp+8h]@1
  __int64 dwAlter; // [sp+68h] [bp+10h]@1

  dwAlter = nAlterExp;
  v10 = this;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v10->m_pRecalledAnimusItem )
  {
    if ( nAlterExp <= 0 )
    {
      dw64OldExp = v10->m_pRecalledAnimusItem->m_dwDur;
      if ( v10->m_pRecalledAnimusItem->m_dwDur < -nAlterExp )
      {
        v10->m_pRecalledAnimusItem->m_dwDur = 0i64;
        CLogFile::Write(
          &stru_1799C8E78,
          "CPlayer::AlterExp_Animus( __int64 nAlterExp ) : m_pRecalledAnimusItem->m_dwDur(%I64u) < -nAlterExp(%I64d) Invalid!",
          v10->m_pRecalledAnimusItem->m_dwDur,
          -nAlterExp);
      }
      else
      {
        v10->m_pRecalledAnimusItem->m_dwDur += nAlterExp;
      }
      v4 = v10->m_pRecalledAnimusItem;
      *(_QWORD *)byReason = (char *)v10 + 50672;
      CMgrAvatorLvHistory::down_animus_exp(
        &CPlayer::s_MgrLvHistory,
        dw64OldExp,
        v4->m_dwDur,
        dwAlter,
        v10->m_szLvHistoryFileName);
    }
    else
    {
      v10->m_pRecalledAnimusItem->m_dwDur += nAlterExp;
      CPlayer::Emb_AlterStat(v10, 6, 0, nAlterExp, 0, "CPlayer::AlterExp_Animus()---0", 1);
    }
    CPlayer::SendMsg_AnimusExpInform(v10);
    if ( v10->m_pUserDB )
    {
      v5 = v10->m_pRecalledAnimusItem;
      v6 = v10->m_pRecalledAnimusItem;
      byReason[0] = 0;
      CUserDB::Update_ItemDur(v10->m_pUserDB, 4, v6->m_byStorageIndex, v5->m_dwDur, 0);
    }
  }
}
