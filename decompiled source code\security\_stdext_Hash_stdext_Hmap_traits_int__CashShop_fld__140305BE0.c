/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0___::insert_::_1_::dtor$12
 * Address: 0x140305BE0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0___::insert_::_1_::dtor_12(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *)(a2 + 496));
}
