/*
 * Function: ?pc_AnimusReturnRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400D0A50
 */

void __fastcall CPlayer::pc_AnimusReturnRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  char v4; // [sp+20h] [bp-18h]@4
  CPlayer *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = 0;
  if ( !v5->m_pRecalledAnimusItem || !v5->m_pRecalledAnimusChar )
    v4 = 7;
  if ( v4 )
    CPlayer::SendMsg_AnimusReturnResult(v5, v4, 0xFFFFu, 0);
  else
    CPlayer::_AnimusReturn(v5, 0);
}
