/*
 * Function: ?GetFrontPtr@CItemDropMgr@@IEAAPEAU_DropItemGroupInfo@@XZ
 * Address: 0x1402D0290
 */

_DropItemGroupInfo *__fastcall CItemDropMgr::GetFrontPtr(CItemDropMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _DropItemGroupInfo *result; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@6
  CItemDropMgr *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNetIndexList::size(&v6->m_listTask) > 0 )
  {
    pdwOutIndex = -1;
    CNetIndexList::CopyFront(&v6->m_listTask, &pdwOutIndex);
    result = &v6->m_Pool[(unsigned __int64)pdwOutIndex];
  }
  else
  {
    result = 0i64;
  }
  return result;
}
