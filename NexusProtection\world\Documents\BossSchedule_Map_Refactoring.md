# BossSchedule_Map Refactoring Documentation

## Overview
This document describes the refactoring of the BossSchedule_Map class from decompiled C source files to modern C++20 compatible code for Visual Studio 2022.

## Original Files Refactored
The following decompiled source files were consolidated into the new BossSchedule_Map class:

### Constructor and Destructor
- `0BossSchedule_MapQEAAXZ_14041B720.c` - Constructor implementation
- `1BossSchedule_MapQEAAXZ_14041B430.c` - Destructor implementation
- `_GBossSchedule_MapQEAAPEAXIZ_14041B3C0.c` - Scalar deleting destructor

### Core Methods
- `ClearBossSchedule_MapQEAAXXZ_14041B4D0.c` - Clear method implementation
- `LoadAllBossSchedule_MapQEAA_NXZ_14041A070.c` - LoadAll method implementation
- `SaveAllBossSchedule_MapQEAA_NXZ_140419FB0.c` - SaveAll method implementation

### Jump Table Functions (j_ prefix)
Multiple jump table functions were analyzed but not directly ported as they represent compiler-generated optimizations.

## Refactoring Changes

### Modern C++ Features Applied
1. **RAII (Resource Acquisition Is Initialization)**
   - Used `std::unique_ptr` for automatic memory management
   - Eliminated manual `operator new[]` and `operator delete[]` calls

2. **STL Containers**
   - Replaced raw pointer arrays with `std::vector<std::unique_ptr<BossSchedule>>`
   - Used `std::vector::reserve()` for performance optimization

3. **Exception Safety**
   - Added try-catch blocks for exception handling
   - Implemented strong exception safety guarantee

4. **Move Semantics**
   - Added move constructor and move assignment operator
   - Deleted copy constructor and copy assignment to prevent accidental copying

5. **Modern Function Attributes**
   - Used `[[nodiscard]]` for functions that return important values
   - Used `noexcept` for functions that don't throw exceptions

6. **Const Correctness**
   - Made getter methods const
   - Used const references where appropriate

### Code Organization
1. **Namespace Structure**
   - Placed class in `NexusProtection::World` namespace
   - Proper header guards with `#pragma once`

2. **Documentation**
   - Added comprehensive Doxygen-style documentation
   - Documented all public methods and member variables

3. **Error Handling**
   - Replaced C-style error codes with boolean returns
   - Added proper bounds checking

### Memory Management Improvements
1. **Automatic Cleanup**
   - RAII ensures automatic cleanup of resources
   - No memory leaks possible with smart pointers

2. **Exception Safety**
   - Strong exception safety guarantee
   - Automatic rollback on failure

## Dependencies
The refactored class has dependencies on the following classes that will need to be refactored in future iterations:

- `CIniFile` - Configuration file handling
- `BossSchedule` - Individual boss schedule data
- `CBossMonsterScheduleSystem` - Boss monster scheduling system
- `INI_Section` - Configuration section structure

## Compilation Notes
- Compatible with Visual Studio 2022 (v143 toolset)
- Requires C++20 standard
- Uses modern STL features

## Testing Recommendations
1. Unit tests for constructor/destructor behavior
2. Tests for move semantics
3. Exception safety tests
4. Memory leak detection tests
5. Integration tests with dependent classes

## Future Improvements
1. Consider using `std::span` for array-like interfaces when C++20 is fully adopted
2. Add logging for debugging purposes
3. Consider async loading for large schedule files
4. Add validation for schedule data integrity

## Performance Considerations
- `std::vector::reserve()` used to minimize reallocations
- Move semantics reduce unnecessary copying
- Smart pointers have minimal overhead compared to raw pointers
- Exception handling adds minimal overhead when no exceptions are thrown

## Backward Compatibility
This refactored version maintains the same public interface semantics as the original decompiled code while providing modern C++ safety and performance benefits.
