/*
 * Function: ?load_con_event_ini@CashItemRemoteStore@@QEAAXPEAU_con_event_ini@@PEAU_FILETIME@@@Z
 * Address: 0x1402FBC10
 */

void __fastcall CashItemRemoteStore::load_con_event_ini(CashItemRemoteStore *this, _con_event_ini *pIni, _FILETIME *pft)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  UINT v6; // [sp+30h] [bp-38h]@7
  _FILETIME ftWrite; // [sp+48h] [bp-20h]@7
  _con_event_ini *v8; // [sp+78h] [bp+10h]@1
  _FILETIME *v9; // [sp+80h] [bp+18h]@1

  v9 = pft;
  v8 = pIni;
  v3 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pIni )
  {
    if ( pft )
    {
      v6 = 0;
      pIni->m_bUseConEvent = 0;
      if ( GetLastWriteFileTime("./initialize/condistional_event.ini", &ftWrite) )
      {
        v9->dwHighDateTime = ftWrite.dwHighDateTime;
        v9->dwLowDateTime = ftWrite.dwLowDateTime;
        v6 = GetPrivateProfileIntA("CONDITIONAL_EVENT", "USE", 1, "./initialize/condistional_event.ini");
        if ( v6 == 1 )
        {
          v8->m_bUseConEvent = 0;
        }
        else
        {
          v8->m_bUseConEvent = 1;
          v6 = GetPrivateProfileIntA("CONDITIONAL_EVENT", "CHSH_MIN", 0, "./initialize/condistional_event.ini");
          if ( v6 )
          {
            v8->m_dwCashMin = 10000 * v6;
            v6 = GetPrivateProfileIntA("CONDITIONAL_EVENT", "EVENT_TIME", 0, "./initialize/condistional_event.ini");
            if ( v6 )
            {
              v8->m_iEventTime = v6;
              v6 = GetPrivateProfileIntA("CONDITIONAL_EVENT", "EVENT_KIND", -1, "./initialize/condistional_event.ini");
              v8->m_byEventKind = v6;
              GetPrivateProfileStringA(
                "CONDITIONAL_EVENT",
                "EVENT_START_MSG",
                "FALSE",
                v8->m_szStartMsg,
                8u,
                "./initialize/condistional_event.ini");
              if ( !strcmp_0(v8->m_szStartMsg, "FALSE") )
              {
                v8->m_bUseConEvent = 0;
              }
              else
              {
                GetPrivateProfileStringA(
                  "CONDITIONAL_EVENT",
                  "EVENT_MIDDLE_MSG",
                  "FALSE",
                  v8->m_szMiddletMsg,
                  8u,
                  "./initialize/condistional_event.ini");
                if ( !strcmp_0(v8->m_szMiddletMsg, "FALSE") )
                {
                  v8->m_bUseConEvent = 0;
                }
                else
                {
                  GetPrivateProfileStringA(
                    "CONDITIONAL_EVENT",
                    "EVENT_END_MSG",
                    "FALSE",
                    v8->m_szEndMsg,
                    8u,
                    "./initialize/condistional_event.ini");
                  if ( !strcmp_0(v8->m_szEndMsg, "FALSE") )
                    v8->m_bUseConEvent = 0;
                }
              }
            }
            else
            {
              v8->m_bUseConEvent = 0;
            }
          }
          else
          {
            v8->m_bUseConEvent = 0;
          }
        }
      }
    }
  }
}
