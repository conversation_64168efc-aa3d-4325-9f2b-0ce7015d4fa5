/*
 * Function: j_??$_Uninit_move@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCUnmannedTraderSortType@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderSortType@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140001096
 */

CUnmannedTraderSortType **__fastcall std::_Uninit_move<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>,std::_Undefined_move_tag>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType **_Dest, std::allocator<CUnmannedTraderSortType *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *,std::allocator<CUnmannedTraderSortType *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
