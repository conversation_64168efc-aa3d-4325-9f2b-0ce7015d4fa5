/*
 * Function: ??$unchecked_copy@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@PEAVCUnmannedTraderItemCodeInfo@@@stdext@@YAPEAVCUnmannedTraderItemCodeInfo@@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@0PEAV1@@Z
 * Address: 0x14037BDA0
 */

CUnmannedTraderItemCodeInfo *__fastcall stdext::unchecked_copy<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_First, std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Last, CUnmannedTraderItemCodeInfo *_Dest)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag *v5; // rax@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v6; // rax@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v7; // rax@4
  __int64 v9; // [sp+0h] [bp-B8h]@1
  std::_Nonscalar_ptr_iterator_tag v10; // [sp+20h] [bp-98h]@4
  std::_Range_checked_iterator_tag v11; // [sp+28h] [bp-90h]@4
  CUnmannedTraderItemCodeInfo *v12; // [sp+30h] [bp-88h]@4
  std::_Range_checked_iterator_tag v13; // [sp+38h] [bp-80h]@4
  std::_Nonscalar_ptr_iterator_tag v14; // [sp+39h] [bp-7Fh]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > v15; // [sp+3Ah] [bp-7Eh]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v16; // [sp+58h] [bp-60h]@4
  char v17; // [sp+60h] [bp-58h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v18; // [sp+78h] [bp-40h]@4
  __int64 v19; // [sp+80h] [bp-38h]@4
  std::random_access_iterator_tag *v20; // [sp+88h] [bp-30h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v21; // [sp+90h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v22; // [sp+98h] [bp-20h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v23; // [sp+A0h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__formal; // [sp+C0h] [bp+8h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__that; // [sp+C8h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *v26; // [sp+D0h] [bp+18h]@1

  v26 = _Dest;
  __that = _Last;
  __formal = _First;
  v3 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = -2i64;
  memset(&v13, 0, sizeof(v13));
  v14 = std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(
          __formal,
          &v26);
  v16 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)((char *)&v15 + 6);
  v18 = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v17;
  LOBYTE(v5) = std::_Iter_random<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(
                 &v15,
                 (CUnmannedTraderItemCodeInfo *const *)__formal);
  v20 = v5;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    v16,
    __that);
  v21 = v6;
  v22 = v6;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(
    v18,
    __formal);
  v23 = v7;
  v11 = v13;
  v10 = v14;
  v12 = std::_Copy_opt<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *,std::random_access_iterator_tag>(
          v7,
          v22,
          v26,
          (std::random_access_iterator_tag)v20->0,
          v14,
          v13);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(__formal);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(__that);
  return v12;
}
