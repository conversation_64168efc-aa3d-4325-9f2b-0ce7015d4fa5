/*
 * Function: ?SF_ReleaseMonsterTarget@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009F610
 */

bool __usercall CPlayer::SF_ReleaseMonsterTarget@<al>(CPlayer *this@<rcx>, CCharacter *pDstObj@<rdx>, float fEffectValue@<xmm2>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  _sec_info *v7; // rax@9
  __int64 v9; // [sp+0h] [bp-88h]@1
  int v10; // [sp+20h] [bp-68h]@4
  _pnt_rect pRect; // [sp+38h] [bp-50h]@4
  int j; // [sp+54h] [bp-34h]@4
  int k; // [sp+58h] [bp-30h]@6
  unsigned int dwSecIndex; // [sp+5Ch] [bp-2Ch]@9
  _object_list_point *v15; // [sp+60h] [bp-28h]@10
  CMonster *v16; // [sp+68h] [bp-20h]@12
  CObjectList *v17; // [sp+70h] [bp-18h]@9
  int v18; // [sp+78h] [bp-10h]@15
  CPlayer *v19; // [sp+90h] [bp+8h]@1
  CCharacter *v20; // [sp+98h] [bp+10h]@1

  v20 = pDstObj;
  v19 = this;
  v4 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = 0;
  v6 = CGameObject::GetCurSecNum((CGameObject *)&v19->vfptr);
  CMapData::GetRectInRadius(v20->m_pCurMap, &pRect, 1, v6);
  for ( j = pRect.nStarty; j < pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k < pRect.nEndx; ++k )
    {
      v7 = CMapData::GetSecInfo(v19->m_pCurMap);
      dwSecIndex = v7->m_nSecNumW * j + k;
      v17 = CMapData::GetSectorListObj(v19->m_pCurMap, v19->m_wMapLayerIndex, dwSecIndex);
      if ( v17 )
      {
        v15 = v17->m_Head.m_pNext;
        while ( v15 != &v17->m_Tail )
        {
          v16 = (CMonster *)v15->m_pItem;
          v15 = v15->m_pNext;
          if ( (CPlayer *)v16 != v19 && !v16->m_ObjID.m_byKind && v16->m_ObjID.m_byID == 1 )
          {
            GetSqrt(v19->m_fCurPos, v16->m_fCurPos);
            v18 = (signed int)ffloor(a4);
            if ( v18 <= 200 && CMonster::ConvertTargetPlayer(v16, 0i64) )
              ++v10;
          }
        }
      }
    }
  }
  return v10 > 0;
}
