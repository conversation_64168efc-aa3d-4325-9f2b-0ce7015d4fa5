/*
 * Function: ?GetDamageDP@CPlayer@@QEAAHH@Z
 * Address: 0x1400636F0
 */

__int64 __fastcall CPlayer::GetDamageDP(CPlayer *this, int nAttackPart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  _base_fld *v6; // [sp+20h] [bp-18h]@8
  char *v7; // [sp+28h] [bp-10h]@8
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1

  v9 = nAttackPart;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPlayer::IsRidingUnit(v8) )
  {
    result = 0i64;
  }
  else if ( v9 <= 5 )
  {
    v6 = 0i64;
    v7 = &v8->m_Param.m_dbEquip.m_pStorageList[v9].m_bLoad;
    if ( *v7 )
    {
      v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v9, *(_WORD *)(v7 + 3));
    }
    else if ( v9 < 5 )
    {
      v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v9, v8->m_Param.m_dbChar.m_byDftPart[v9]);
    }
    if ( v6 )
      result = *(_DWORD *)&v6[5].m_strCode[48];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
