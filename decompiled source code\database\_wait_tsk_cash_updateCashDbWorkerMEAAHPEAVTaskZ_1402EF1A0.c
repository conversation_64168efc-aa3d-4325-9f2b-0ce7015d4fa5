/*
 * Function: ?_wait_tsk_cash_update@CashDbWorker@@MEAAHPEAVTask@@@Z
 * Address: 0x1402EF1A0
 */

__int64 __fastcall CashDbWorker::_wait_tsk_cash_update(CashDbWorker *this, Task *pkTsk)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  _param_cash_update *rParam; // [sp+20h] [bp-28h]@4
  unsigned int v7; // [sp+28h] [bp-20h]@4
  int v8; // [sp+2Ch] [bp-1Ch]@4
  int v9; // [sp+30h] [bp-18h]@4
  int nIdx; // [sp+34h] [bp-14h]@4
  char *v11; // [sp+38h] [bp-10h]@6
  CashDbWorker *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  rParam = (_param_cash_update *)Task::GetTaskBuf(pkTsk);
  v7 = 0;
  v8 = -1;
  v9 = 0;
  for ( nIdx = 0; nIdx < rParam->in_nNum10; ++nIdx )
  {
    v11 = &rParam->in_item[(signed __int64)nIdx].byRet;
    if ( CRFCashItemDatabase::CallProc_RFOnlineUse(v12->_pkDb, rParam, nIdx) )
      return 1;
    v11[40] = ((int (__fastcall *)(CashDbWorker *, _QWORD))v12->vfptr[5].~Worker)(v12, (unsigned __int8)v11[40]);
  }
  return v7;
}
