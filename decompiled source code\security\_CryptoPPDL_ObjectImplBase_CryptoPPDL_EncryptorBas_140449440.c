/*
 * Function: _CryptoPP::DL_ObjectImplBase_CryptoPP::DL_EncryptorBase_CryptoPP::ECPPoint__CryptoPP::DL_CryptoSchemeOptions_CryptoPP::ECIES_CryptoPP::ECP_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0__0__CryptoPP::DL_Keys_EC_CryptoPP::ECP__CryptoPP::DL_KeyAgreementAlgorithm_DH_CryptoPP::ECPPoint_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0____CryptoPP::DL_KeyDerivationAlgorithm_P1363_CryptoPP::ECPPoint_0_CryptoPP::P1363_KDF2_CryptoPP::SHA1____CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____CryptoPP::DL_PublicKey_EC_CryptoPP::ECP___::_DL_ObjectImplBase_CryptoPP::DL_EncryptorBase_CryptoPP::ECPPoint__CryptoPP::DL_CryptoSchemeOptions_CryptoPP::ECIES_CryptoPP::ECP_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0__0__CryptoPP::DL_Keys_EC_CryptoPP::ECP__CryptoPP::DL_KeyAgreementAlgorithm_DH_CryptoPP::ECPPoint_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0____CryptoPP::DL_KeyDerivationAlgorithm_P1363_CryptoPP::ECPPoint_0_Crypt
 * Address: 0x140449440
 */

void __fastcall CryptoPP::DL_ObjectImplBase_CryptoPP::DL_EncryptorBase_CryptoPP::ECPPoint__CryptoPP::DL_CryptoSchemeOptions_CryptoPP::ECIES_CryptoPP::ECP_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0__0__CryptoPP::DL_Keys_EC_CryptoPP::ECP__CryptoPP::DL_KeyAgreementAlgorithm_DH_CryptoPP::ECPPoint_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0____CryptoPP::DL_KeyDerivationAlgorithm_P1363_CryptoPP::ECPPoint_0_CryptoPP::P1363_KDF2_CryptoPP::SHA1____CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____CryptoPP::DL_PublicKey_EC_CryptoPP::ECP___::_DL_ObjectImplBase_CryptoPP::DL_EncryptorBase_CryptoPP::ECPPoint__CryptoPP::DL_CryptoSchemeOptions_CryptoPP::ECIES_CryptoPP::ECP_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0__0__CryptoPP::DL_Keys_EC_CryptoPP::ECP__CryptoPP::DL_KeyAgreementAlgorithm_DH_CryptoPP::ECPPoint_CryptoPP::EnumToType_enum_CryptoPP::CofactorMultiplicationOption_0____CryptoPP::DL_KeyDerivationAlgorithm_P1363_CryptoPP::ECPPoint_0_CryptoPP::P1363_KDF2_CryptoPP::SHA1____CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____CryptoPP::DL_PublicKey_EC_CryptoPP::ECP____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::~AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>(*(CryptoPP::AlgorithmImpl<CryptoPP::DL_EncryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0> > **)(a2 + 64));
}
