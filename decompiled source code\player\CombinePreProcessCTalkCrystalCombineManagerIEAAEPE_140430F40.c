/*
 * Function: ?CombinePreProcess@CTalkCrystalCombineManager@@IEAAEPEAVCPlayer@@EPEAU_list@_talik_crystal_exchange_clzo@@@Z
 * Address: 0x140430F40
 */

char __fastcall CTalkCrystalCombineManager::CombinePreProcess(CTalkCrystalCombineManager *this, CPlayer *pPlayer, char byExchange<PERSON>um, _talik_crystal_exchange_clzo::_list *pList)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@4
  unsigned __int8 v9; // [sp+21h] [bp-17h]@4
  char j; // [sp+22h] [bp-16h]@4
  unsigned __int8 k; // [sp+23h] [bp-15h]@8
  _STORAGE_LIST::_db_con *pItem; // [sp+28h] [bp-10h]@14
  CTalkCrystalCombineManager *v13; // [sp+40h] [bp+8h]@1
  CPlayer *v14; // [sp+48h] [bp+10h]@1
  _talik_crystal_exchange_clzo::_list *v15; // [sp+58h] [bp+20h]@1

  v15 = pList;
  v14 = pPlayer;
  v13 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  CTalkCrystalCombineManager::Init(v13);
  v9 = 24;
  v13->m_pCurrentPlayer = v14;
  for ( j = 0; (unsigned __int8)j < (signed int)v9; ++j )
  {
    if ( v15[(unsigned __int8)j].byItemCount )
    {
      for ( k = j + 1; k < (signed int)v9; ++k )
      {
        if ( v15[k].byItemCount && v15[k].wSerial == v15[(unsigned __int8)j].wSerial )
          return 5;
      }
      pItem = _STORAGE_LIST::GetPtrFromSerial(
                (_STORAGE_LIST *)&v14->m_Param.m_dbInven.m_nListNum,
                v15[(unsigned __int8)j].wSerial);
      if ( !pItem )
        return 4;
      if ( pItem->m_byTableCode != 18 )
        return 4;
      if ( IsOverLapItem(pItem->m_byTableCode) )
      {
        if ( pItem->m_dwDur < v15[(unsigned __int8)j].byItemCount )
          return 5;
      }
      else if ( v15[(unsigned __int8)j].byItemCount != 1 )
      {
        return 5;
      }
      v8 = CTalkCrystalCombineManager::Push(v13, pItem, v15[(unsigned __int8)j].byItemCount, j);
      if ( v8 )
        return v8;
    }
  }
  return 0;
}
