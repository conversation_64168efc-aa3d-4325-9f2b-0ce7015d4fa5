/*
 * Function: ?ct_SetPatriarchGroup@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402952F0
 */

bool __fastcall ct_SetPatriarchGroup(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  PatriarchElectProcessor *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  _candidate_info::ClassType eClass; // [sp+20h] [bp-18h]@7
  CPlayer *pOnea; // [sp+40h] [bp+8h]@1

  pOnea = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pOnea && pOnea->m_bOper )
  {
    eClass = atoi(s_pwszDstCheat[0]);
    v4 = PatriarchElectProcessor::Instance();
    result = PatriarchElectProcessor::CheatSetPatriarch(v4, pOnea, eClass);
  }
  else
  {
    result = 0;
  }
  return result;
}
