/*
 * Function: ?IsItemCombineExKind@@YAHH@Z
 * Address: 0x14003E0A0
 */

signed __int64 __fastcall IsItemCombineExKind(int nTableCode)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  int v4; // [sp+0h] [bp-18h]@1
  int v5; // [sp+20h] [bp+8h]@1

  v5 = nTableCode;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  v4 = v5;
  switch ( v5 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      result = 1i64;
      break;
    case 6:
      result = 1i64;
      break;
    case 8:
      result = 1i64;
      break;
    case 9:
      result = 1i64;
      break;
    default:
      result = 0i64;
      break;
  }
  return result;
}
