/*
 * Function: ?Put2@MessageQueue@CryptoPP@@UEAA_KPEBE_KH_N@Z
 * Address: 0x140655040
 */

unsigned __int64 __fastcall CryptoPP::MessageQueue::Put2(CryptoPP::MessageQueue *this, const unsigned __int8 *a2, __int64 a3, int a4, bool a5)
{
  _QWORD *v5; // rax@1
  _DWORD *v6; // rax@2
  __int64 v8; // [sp+20h] [bp-28h]@2
  _QWORD *v9; // [sp+28h] [bp-20h]@1
  _DWORD *v10; // [sp+30h] [bp-18h]@2
  CryptoPP::MessageQueue *v11; // [sp+50h] [bp+8h]@1
  unsigned __int64 v12; // [sp+60h] [bp+18h]@1
  int v13; // [sp+68h] [bp+20h]@1

  v13 = a4;
  v12 = a3;
  v11 = this;
  CryptoPP::BufferedTransformation::Put((CryptoPP::BufferedTransformation *)((char *)this + 32), a2, a3);
  LODWORD(v5) = std::deque<unsigned __int64,std::allocator<unsigned __int64>>::back((char *)v11 + 112);
  v9 = v5;
  *v5 += v12;
  if ( v13 )
  {
    v8 = 0i64;
    std::deque<unsigned __int64,std::allocator<unsigned __int64>>::push_back((char *)v11 + 112, &v8);
    LODWORD(v6) = std::deque<unsigned int,std::allocator<unsigned int>>::back((char *)v11 + 168);
    v10 = v6;
    ++*v6;
  }
  return 0i64;
}
