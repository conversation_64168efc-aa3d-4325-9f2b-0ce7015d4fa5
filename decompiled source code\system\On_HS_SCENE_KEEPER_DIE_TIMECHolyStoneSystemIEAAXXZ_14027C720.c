/*
 * Function: ?On_HS_SCENE_KEEPER_DIE_TIME@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027C720
 */

void __fastcall CHolyStoneSystem::On_HS_SCENE_KEEPER_DIE_TIME(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CHolyStoneSystem *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CHolyStoneSystem::DestroyHolyKeeper(v4);
  CHolyStoneSystem::SendMsg_WaitKeeper(v4, -1, 1);
}
