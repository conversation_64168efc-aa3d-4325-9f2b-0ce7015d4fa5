/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@PEAPEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@2@@stdext@@YAPEAPEAVCLogTypeDBTask@@V?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@0PEAPEAV1@AEAV?$allocator@PEAVCLogTypeDBTask@@@3@@Z
 * Address: 0x140007BB7
 */

CLogTypeDBTask **__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_First, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *_Last, CLogTypeDBTask **_Dest, std::allocator<CLogTypeDBTask *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>,CLogTypeDBTask * *,std::allocator<CLogTypeDBTask *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
