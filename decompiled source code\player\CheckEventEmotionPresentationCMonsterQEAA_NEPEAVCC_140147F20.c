/*
 * Function: ?CheckEventEmotionPresentation@CMonster@@QEAA_NEPEAVCCharacter@@@Z
 * Address: 0x140147F20
 */

bool __fastcall CMonster::CheckEventEmotionPresentation(CMonster *this, char byCheckType, CCharacter *pTarget)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMonster *pThis; // [sp+30h] [bp+8h]@1

  pThis = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return EmotionPresentationChecker::CheckEmotionState(&pThis->m_EmotionPresentationCheck, pThis, byCheckType, pTarget);
}
