/**
 * @file CGuildSystemManager.h
 * @brief Modern C++20 Guild System Manager
 * 
 * This file provides comprehensive guild system management with modern C++20 patterns,
 * proper initialization, and modular design for handling all guild operations.
 * 
 * Refactored from decompiled sources:
 * - OnLoop_GuildSystemYAX_NZ_1402589E0.c (27 lines)
 * - j_OnLoop_GuildSystemYAX_NZ_140013732.c (9 lines)
 */

#pragma once

#include <memory>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <mutex>
#include <functional>
#include <optional>
#include <string>
#include <array>

// Forward declarations for legacy compatibility
extern "C" {
    class CGuild;
    extern CGuild* g_Guild;
    
    bool CGuild_IsFill(CGuild* guild);
    void CGuild_Loop(CGuild* guild, bool changeDay);
    int GetCurDay();
}

namespace NexusProtection::Guild {

/**
 * @brief Guild system operation result enumeration
 */
enum class GuildSystemResult : uint8_t {
    Success = 0,
    InitializationFailed,
    InvalidGuildArray,
    ProcessingError,
    SystemError
};

/**
 * @brief Guild system state enumeration
 */
enum class GuildSystemState : uint8_t {
    Uninitialized = 0,
    Initializing,
    Running,
    Paused,
    Error,
    Shutdown
};

/**
 * @brief Guild processing statistics
 */
struct GuildSystemStats {
    std::atomic<uint64_t> totalLoopCycles{0};
    std::atomic<uint64_t> totalGuildsProcessed{0};
    std::atomic<uint64_t> activeGuilds{0};
    std::atomic<uint64_t> dayChangeEvents{0};
    std::atomic<uint64_t> processingErrors{0};
    std::chrono::steady_clock::time_point startTime;
    
    GuildSystemStats() : startTime(std::chrono::steady_clock::now()) {}
    
    double GetAverageGuildsPerCycle() const {
        uint64_t cycles = totalLoopCycles.load();
        return cycles > 0 ? static_cast<double>(totalGuildsProcessed.load()) / cycles : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Guild processing configuration
 */
struct GuildSystemConfig {
    uint32_t maxGuilds = 500;                    // Maximum number of guilds to process
    bool enableDayChangeProcessing = true;       // Enable day change event processing
    bool enableStatistics = true;                // Enable statistics collection
    bool enableErrorLogging = true;              // Enable error logging
    std::chrono::milliseconds loopInterval{100}; // Loop processing interval
    
    // Validation
    bool IsValid() const {
        return maxGuilds > 0 && maxGuilds <= 10000;
    }
};

/**
 * @brief Guild event callback types
 */
using GuildProcessedCallback = std::function<void(uint32_t guildIndex, bool dayChanged)>;
using GuildErrorCallback = std::function<void(uint32_t guildIndex, const std::string& error)>;
using DayChangeCallback = std::function<void(int currentDay, int previousDay)>;

/**
 * @brief Modern C++20 Guild System Manager
 * 
 * Provides comprehensive guild system management with proper error handling,
 * statistics collection, and event callbacks.
 */
class CGuildSystemManager {
public:
    /**
     * @brief Get singleton instance
     * 
     * @return Reference to the singleton instance
     */
    static CGuildSystemManager& GetInstance();

    /**
     * @brief Initialize guild system manager
     * 
     * @param config System configuration
     * @return GuildSystemResult indicating success or failure
     */
    GuildSystemResult Initialize(const GuildSystemConfig& config = {});

    /**
     * @brief Shutdown guild system manager
     * 
     * @return GuildSystemResult indicating success or failure
     */
    GuildSystemResult Shutdown();

    /**
     * @brief Process guild system loop
     * 
     * Modern implementation of OnLoop_GuildSystem function.
     * Processes all active guilds and handles day change events.
     * 
     * @param dayChanged Whether a day change event occurred
     * @return GuildSystemResult indicating success or failure
     */
    GuildSystemResult ProcessGuildLoop(bool dayChanged = false);

    /**
     * @brief Process guild system loop (legacy compatibility)
     * 
     * @param dayChanged Whether a day change event occurred
     */
    void OnLoop_GuildSystem(bool dayChanged);

    /**
     * @brief Get current system state
     * 
     * @return Current guild system state
     */
    GuildSystemState GetState() const { return m_state.load(); }

    /**
     * @brief Get system statistics
     * 
     * @return Copy of current statistics
     */
    GuildSystemStats GetStatistics() const;

    /**
     * @brief Get system configuration
     * 
     * @return Copy of current configuration
     */
    GuildSystemConfig GetConfiguration() const;

    /**
     * @brief Update system configuration
     * 
     * @param config New configuration
     * @return true if successful, false otherwise
     */
    bool UpdateConfiguration(const GuildSystemConfig& config);

    /**
     * @brief Set guild processed callback
     * 
     * @param callback Callback function for guild processing events
     */
    void SetGuildProcessedCallback(GuildProcessedCallback callback);

    /**
     * @brief Set guild error callback
     * 
     * @param callback Callback function for guild processing errors
     */
    void SetGuildErrorCallback(GuildErrorCallback callback);

    /**
     * @brief Set day change callback
     * 
     * @param callback Callback function for day change events
     */
    void SetDayChangeCallback(DayChangeCallback callback);

    /**
     * @brief Check if guild system is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized.load(); }

    /**
     * @brief Get active guild count
     * 
     * @return Number of currently active guilds
     */
    uint32_t GetActiveGuildCount() const;

    /**
     * @brief Force day change processing
     * 
     * @return GuildSystemResult indicating success or failure
     */
    GuildSystemResult ForceDayChange();

    /**
     * @brief Reset system statistics
     */
    void ResetStatistics();

private:
    // Singleton pattern
    CGuildSystemManager() = default;
    ~CGuildSystemManager() = default;
    CGuildSystemManager(const CGuildSystemManager&) = delete;
    CGuildSystemManager& operator=(const CGuildSystemManager&) = delete;
    CGuildSystemManager(CGuildSystemManager&&) = delete;
    CGuildSystemManager& operator=(CGuildSystemManager&&) = delete;

    /**
     * @brief Process individual guild
     * 
     * @param guildIndex Guild array index
     * @param dayChanged Whether a day change event occurred
     * @return true if successful, false otherwise
     */
    bool ProcessGuild(uint32_t guildIndex, bool dayChanged);

    /**
     * @brief Validate guild array
     * 
     * @return true if valid, false otherwise
     */
    bool ValidateGuildArray() const;

    /**
     * @brief Update statistics
     * 
     * @param guildIndex Guild index that was processed
     * @param success Whether processing was successful
     * @param dayChanged Whether a day change event occurred
     */
    void UpdateStatistics(uint32_t guildIndex, bool success, bool dayChanged);

    /**
     * @brief Log error message
     * 
     * @param guildIndex Guild index where error occurred
     * @param message Error message
     */
    void LogError(uint32_t guildIndex, const std::string& message);

    /**
     * @brief Handle day change event
     * 
     * @param currentDay Current day value
     */
    void HandleDayChange(int currentDay);

    // Member variables
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    std::atomic<GuildSystemState> m_state{GuildSystemState::Uninitialized};
    
    GuildSystemConfig m_config;
    GuildSystemStats m_stats;
    
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    
    // Event callbacks
    GuildProcessedCallback m_guildProcessedCallback;
    GuildErrorCallback m_guildErrorCallback;
    DayChangeCallback m_dayChangeCallback;
    
    // Day tracking
    std::atomic<int> m_lastDay{-1};
    
    // Error tracking
    std::chrono::steady_clock::time_point m_lastErrorTime;
    std::atomic<uint32_t> m_consecutiveErrors{0};
};

} // namespace NexusProtection::Guild
