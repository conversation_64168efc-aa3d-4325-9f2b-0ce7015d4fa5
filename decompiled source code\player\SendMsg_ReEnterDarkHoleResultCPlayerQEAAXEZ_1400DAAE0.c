/*
 * Function: ?SendMsg_ReEnterDarkHoleResult@CPlayer@@QEAAXE@Z
 * Address: 0x1400DAAE0
 */

void __fastcall CPlayer::SendMsg_ReEnterDarkHoleResult(CPlayer *this, char byRetCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  _darkhole_answer_reenter_result_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6.byRetCode = byRetCode;
  pbyType = 35;
  v8 = -45;
  v4 = _darkhole_answer_reenter_result_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &v6.byRetCode, v4);
}
