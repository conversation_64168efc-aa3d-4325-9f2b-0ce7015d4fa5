/*
 * Function: ?DeleteItemCountFromCode@CPlayerDB@@QEAA_NPEADH@Z
 * Address: 0x14010C760
 */

char __fastcall CPlayerDB::DeleteItemCountFromCode(CPlayerDB *this, char *pszItemCode, int nCount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-68h]@1
  bool bUpdate; // [sp+20h] [bp-48h]@11
  bool bSend; // [sp+28h] [bp-40h]@11
  unsigned __int16 v9; // [sp+30h] [bp-38h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+38h] [bp-30h]@4
  _base_fld *v11; // [sp+40h] [bp-28h]@4
  int v12; // [sp+48h] [bp-20h]@4
  int j; // [sp+4Ch] [bp-1Ch]@4
  int v14; // [sp+50h] [bp-18h]@15
  CPlayerDB *v15; // [sp+70h] [bp+8h]@1
  const char *Str2; // [sp+78h] [bp+10h]@1

  Str2 = pszItemCode;
  v15 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = 0;
  pItem = 0i64;
  v11 = 0i64;
  v12 = nCount;
  for ( j = 0; ; ++j )
  {
    if ( j >= v15->m_dbInven.m_nUsedNum )
      return 0;
    if ( v15->m_dbInven.m_pStorageList[j].m_bLoad )
    {
      v11 = CRecordData::GetRecord(
              (CRecordData *)&unk_1799C6AA0 + v15->m_dbInven.m_pStorageList[j].m_byTableCode,
              v15->m_dbInven.m_pStorageList[j].m_wItemIndex);
      if ( v11 )
      {
        if ( !strcmp_0(v11->m_strCode, Str2) )
          break;
      }
    }
LABEL_5:
    ;
  }
  pItem = &v15->m_dbInven.m_pStorageList[j];
  if ( pItem->m_dwDur < v12 )
  {
    v14 = pItem->m_dwDur;
    bSend = 0;
    bUpdate = 0;
    v9 = CPlayer::Emb_AlterDurPoint(v15->m_pThis, 0, pItem->m_byStorageIndex, -v14, 0, 0);
    if ( v9 )
      CPlayer::SendMsg_AdjustAmountInform(v15->m_pThis, 0, pItem->m_wSerial, v9);
    else
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v15->m_pThis->m_ObjID.m_wIndex,
        pItem,
        v15->m_pThis->m_szItemHistoryFileName);
    v12 -= v14;
    goto LABEL_5;
  }
  bSend = 0;
  bUpdate = 0;
  v9 = CPlayer::Emb_AlterDurPoint(v15->m_pThis, 0, pItem->m_byStorageIndex, -v12, 0, 0);
  if ( v9 )
    CPlayer::SendMsg_AdjustAmountInform(v15->m_pThis, 0, pItem->m_wSerial, v9);
  else
    CMgrAvatorItemHistory::consume_del_item(
      &CPlayer::s_MgrItemHistory,
      v15->m_pThis->m_ObjID.m_wIndex,
      pItem,
      v15->m_pThis->m_szItemHistoryFileName);
  return 1;
}
