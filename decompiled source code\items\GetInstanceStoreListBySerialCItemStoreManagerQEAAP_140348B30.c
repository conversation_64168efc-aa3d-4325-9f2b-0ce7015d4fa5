/*
 * Function: ?GetInstanceStoreListBySerial@CItemStoreManager@@QEAAPEAVCMapItemStoreList@@H@Z
 * Address: 0x140348B30
 */

CMapItemStoreList *__fastcall CItemStoreManager::GetInstanceStoreListBySerial(CItemStoreManager *this, int nSerial)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CItemStoreManager *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < v6->m_nInstanceItemStoreListNum; ++j )
  {
    if ( v6->m_InstanceItemStoreList[j].m_bUse && v6->m_InstanceItemStoreList[j].m_nSerial == nSerial )
      return &v6->m_InstanceItemStoreList[j];
  }
  return 0i64;
}
