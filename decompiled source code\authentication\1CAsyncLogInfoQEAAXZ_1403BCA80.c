/*
 * Function: ??1CAsyncLogInfo@@QEAA@XZ
 * Address: 0x1403BCA80
 */

void __fastcall CAsyncLogInfo::~CAsyncLogInfo(CAsyncLogInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-68h]@1
  void *v5; // [sp+20h] [bp-48h]@4
  void *v6; // [sp+28h] [bp-40h]@4
  void *v7; // [sp+30h] [bp-38h]@4
  CMyTimer *v8; // [sp+38h] [bp-30h]@4
  CMyTimer *v9; // [sp+40h] [bp-28h]@4
  __int64 v10; // [sp+48h] [bp-20h]@4
  __int64 v11; // [sp+50h] [bp-18h]@5
  CAsyncLogInfo *v12; // [sp+70h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  v5 = v12->m_szLogDirPath;
  operator delete[](v5);
  v6 = v12->m_szLogFileName;
  operator delete[](v6);
  v7 = v12->m_szTypeName;
  operator delete[](v7);
  v9 = v12->m_pkTimer;
  v8 = v9;
  if ( v9 )
  {
    LODWORD(v3) = ((int (__fastcall *)(CMyTimer *, signed __int64))v8->vfptr->__vecDelDtor)(v8, 1i64);
    v11 = v3;
  }
  else
  {
    v11 = 0i64;
  }
  CNetCriticalSection::~CNetCriticalSection(&v12->m_csLock);
}
