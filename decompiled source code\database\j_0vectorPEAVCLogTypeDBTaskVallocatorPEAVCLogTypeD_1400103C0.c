/*
 * Function: j_??0?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@XZ
 * Address: 0x1400103C0
 */

void __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(this);
}
