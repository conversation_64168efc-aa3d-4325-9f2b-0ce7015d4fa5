/*
 * Function: ?Set_LimitedSale_Event_Ini@CashItemRemoteStore@@QEAAXPEAU_cash_event_ini@@@Z
 * Address: 0x1402FD780
 */

void __fastcall CashItemRemoteStore::Set_LimitedSale_Event_Ini(CashItemRemoteStore *this, _cash_event_ini *pIni)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@5
  unsigned __int8 v6; // [sp+24h] [bp-14h]@8
  _base_fld *v7; // [sp+28h] [bp-10h]@9
  CashItemRemoteStore *v8; // [sp+40h] [bp+8h]@1
  _cash_event_ini *v9; // [sp+48h] [bp+10h]@1

  v9 = pIni;
  v8 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pIni )
  {
    v8->m_lim_event.DCK = pIni->m_byDCK;
    v8->m_lim_event.m_byEventNum = pIni->m_byLimited_sale_num;
    for ( j = 0; j < v8->m_lim_event.m_byEventNum; ++j )
    {
      v8->m_lim_event.m_EventItemInfo[j].byTableCode = -1;
      v8->m_lim_event.m_EventItemInfo[j].dwIndex = 255;
      v8->m_lim_event.m_EventItemInfo[j].wCount = v9->m_Limited_sale[j].m_wLimcount;
      v6 = GetItemTableCode(v9->m_Limited_sale[j].m_szLimcode);
      if ( v6 != 255 )
      {
        v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v6, v9->m_Limited_sale[j].m_szLimcode);
        if ( v7 )
        {
          v8->m_lim_event.m_EventItemInfo[j].byTableCode = v6;
          v8->m_lim_event.m_EventItemInfo[j].dwIndex = v7->m_dwIndex;
        }
      }
    }
  }
}
