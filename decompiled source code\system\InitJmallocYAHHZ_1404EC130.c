/*
 * Function: ?InitJmalloc@@YAHH@Z
 * Address: 0x1404EC130
 */

void *__fastcall InitJmalloc(int a1)
{
  int v1; // ebx@1
  void *result; // rax@1

  v1 = a1;
  result = malloc(a1);
  Allmemory = result;
  Totalmemory = (unsigned __int8 *)((signed int)result + v1);
  if ( !result )
  {
    Error("can't alloc momory!", byte_140883769);
    result = (void *)(unsigned int)Allmemory;
  }
  return result;
}
