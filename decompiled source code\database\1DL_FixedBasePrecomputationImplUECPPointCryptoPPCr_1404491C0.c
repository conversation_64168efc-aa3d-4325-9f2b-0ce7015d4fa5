/*
 * Function: ??1?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x1404491C0
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::~DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>(CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::~vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(&v5->m_bases);
  CryptoPP::Integer::~Integer(&v5->m_exponentBase);
  CryptoPP::ECPPoint::~ECPPoint(&v5->m_base);
}
