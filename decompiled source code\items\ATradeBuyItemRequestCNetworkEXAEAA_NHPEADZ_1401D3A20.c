/*
 * Function: ?ATradeBuyItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D3A20
 */

char __fastcall CNetworkEX::ATradeBuyItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  CUnmannedTraderController *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-38h]@1
  _unmannedtrader_buy_item_request_clzo *pRequest; // [sp+20h] [bp-18h]@4
  CPlayer *v10; // [sp+28h] [bp-10h]@4
  CNetworkEX *v11; // [sp+40h] [bp+8h]@1
  int v12; // [sp+48h] [bp+10h]@1

  v12 = n;
  v11 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pRequest = (_unmannedtrader_buy_item_request_clzo *)pBuf;
  v10 = &g_Player + n;
  if ( !v10->m_bOper || v10->m_pmTrd.bDTradeMode || v10->m_bCorpse )
  {
    result = 1;
  }
  else if ( pRequest->byNum <= 10 )
  {
    v7 = CUnmannedTraderController::Instance();
    CUnmannedTraderController::Buy(v7, v12, pRequest);
    result = 1;
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v10->m_Param);
    CLogFile::Write(
      &v11->m_LogFile,
      "odd.. %s: ATradeBuyItemRequest().. if(pRecv->byNum > _unmannedtrader_buy_item_request_clzo::max_list)",
      v6);
    result = 0;
  }
  return result;
}
