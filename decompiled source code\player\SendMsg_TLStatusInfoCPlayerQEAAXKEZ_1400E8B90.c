/*
 * Function: ?SendMsg_TLStatusInfo@CPlayer@@QEAAXKE@Z
 * Address: 0x1400E8B90
 */

void __fastcall CPlayer::SendMsg_TLStatusInfo(CPlayer *this, unsigned int dwFatigue, char wStatus)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@4
  unsigned __int16 v6; // ax@4
  unsigned __int16 v7; // ax@6
  __int64 v8; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+38h] [bp-50h]@4
  __int16 v10; // [sp+3Ch] [bp-4Ch]@4
  unsigned int v11; // [sp+3Eh] [bp-4Ah]@5
  unsigned int v12; // [sp+42h] [bp-46h]@4
  char pbyType; // [sp+64h] [bp-24h]@7
  char v14; // [sp+65h] [bp-23h]@7
  CPlayer *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v3 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(_DWORD *)szMsg = v15->m_pUserDB->m_AvatorData.dbTimeLimitInfo.dwFatigue;
  v10 = v15->m_pUserDB->m_AvatorData.dbTimeLimitInfo.byTLStatus;
  v5 = TimeLimitMgr::GetPlayFDegree(qword_1799CA2D0);
  v12 = v5 / 0x3E8 * *(_DWORD *)szMsg;
  v6 = TimeLimitMgr::GetEndPlayTime(qword_1799CA2D0);
  if ( v12 < 60 * (unsigned int)v6 )
  {
    v7 = TimeLimitMgr::GetEndPlayTime(qword_1799CA2D0);
    v11 = 60 * v7 - v12;
  }
  else
  {
    v11 = 0;
  }
  pbyType = 13;
  v14 = -113;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, szMsg, 0xEu);
}
