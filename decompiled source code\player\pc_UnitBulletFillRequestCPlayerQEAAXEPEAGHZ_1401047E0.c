/*
 * Function: ?pc_UnitBulletFillRequest@CPlayer@@QEAAXEPEAGH@Z
 * Address: 0x1401047E0
 */

void __usercall CPlayer::pc_UnitBulletFillRequest(CPlayer *this@<rcx>, char bySlotIndex@<dl>, unsigned __int16 *pwBulletIndex@<r8>, int bUseNPCLinkIntem@<r9d>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v7; // eax@32
  unsigned int v8; // eax@43
  CMoneySupplyMgr *v9; // rax@51
  CMoneySupplyMgr *v10; // rax@53
  __int64 v11; // [sp+0h] [bp-128h]@1
  char v12; // [sp+40h] [bp-E8h]@4
  _UNIT_DB_BASE::_LIST *pData; // [sp+48h] [bp-E0h]@4
  unsigned __int8 v14; // [sp+50h] [bp-D8h]@4
  _base_fld *v15; // [sp+68h] [bp-C0h]@4
  _base_fld *v16; // [sp+70h] [bp-B8h]@4
  unsigned int dwSub; // [sp+98h] [bp-90h]@4
  unsigned int v18; // [sp+9Ch] [bp-8Ch]@4
  unsigned int v19; // [sp+C4h] [bp-64h]@4
  float v20; // [sp+C8h] [bp-60h]@4
  int j; // [sp+CCh] [bp-5Ch]@16
  unsigned __int8 v22; // [sp+D0h] [bp-58h]@20
  _base_fld *v23; // [sp+D8h] [bp-50h]@20
  unsigned __int64 v24; // [sp+E0h] [bp-48h]@32
  unsigned int *v25; // [sp+E8h] [bp-40h]@39
  int v26; // [sp+F0h] [bp-38h]@46
  __int64 v27; // [sp+F8h] [bp-30h]@32
  char *v28; // [sp+100h] [bp-28h]@43
  unsigned int v29; // [sp+108h] [bp-20h]@43
  unsigned int nAmount; // [sp+10Ch] [bp-1Ch]@51
  int nLv; // [sp+110h] [bp-18h]@51
  int v32; // [sp+114h] [bp-14h]@53
  CPlayer *p; // [sp+130h] [bp+8h]@1
  char v34; // [sp+138h] [bp+10h]@1
  unsigned __int16 *pwBulletIndexa; // [sp+140h] [bp+18h]@1
  int v36; // [sp+148h] [bp+20h]@1

  v36 = bUseNPCLinkIntem;
  pwBulletIndexa = pwBulletIndex;
  v34 = bySlotIndex;
  p = this;
  v5 = &v11;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = 0;
  pData = &p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex];
  v14 = p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byFrame;
  v15 = CRecordData::GetRecord(&stru_1799C8AF0, *pwBulletIndex);
  v16 = CRecordData::GetRecord(&stru_1799C8AF0, pwBulletIndexa[1]);
  dwSub = 0;
  memset(&v18, 0, 0x18ui64);
  v19 = eGetTexRate(0) + 10000;
  eGetTex(0);
  v20 = a5 + 1.0;
  if ( p->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      v12 = 34;
    }
    else if ( v36 || IsBeNearStore(p, 4) )
    {
      if ( CPlayerDB::GetRaceCode(&p->m_Param) )
      {
        v12 = 1;
      }
      else if ( p->m_pUsingUnit )
      {
        v12 = 2;
      }
      else if ( v14 == 255 )
      {
        v12 = 5;
      }
      else
      {
        for ( j = 0; j < 2; ++j )
        {
          if ( *(&v15 + j) )
          {
            v22 = gbyWeaponPerBullet[(signed __int64)j];
            v23 = CRecordData::GetRecord(&stru_1799C86D0 + v22, pData->byPart[v22]);
            if ( !v23 )
            {
              v12 = 26;
              goto LABEL_35;
            }
            if ( *(_DWORD *)&v23[3].m_strCode[60] != *(_DWORD *)&(*(&v15 + j))[2].m_strCode[60] )
            {
              v12 = 26;
              goto LABEL_35;
            }
          }
        }
        for ( j = 0; j < 2; ++j )
        {
          if ( *(&v15 + j) )
            *(&dwSub + *(_DWORD *)&(*(&v15 + j))[3].m_strCode[4]) += *(_DWORD *)&(*(&v15 + j))[3].m_strCode[8];
        }
        for ( j = 0; j < 7; ++j )
        {
          v24 = *(&dwSub + j);
          v24 *= v19;
          *(&dwSub + j) = v24 / 0x2710;
          v27 = j;
          v7 = CPlayer::GetMoney(p, j);
          if ( *(&dwSub + v27) > v7 )
          {
            v12 = 7;
            break;
          }
        }
      }
    }
    else
    {
      v12 = 21;
    }
LABEL_35:
    if ( !v12 )
    {
      for ( j = 0; j < 2; ++j )
      {
        if ( *(&v15 + j) )
        {
          v25 = &pData->dwBullet[j];
          *(_WORD *)v25 = pwBulletIndexa[j];
          *((_WORD *)v25 + 1) = *(_WORD *)&(*(&v15 + j))[3].m_strCode[0];
        }
      }
      CPlayer::SubDalant(p, dwSub);
      CPlayer::SubGold(p, v18);
      CUserDB::Update_UnitData(p->m_pUserDB, v34, pData);
      if ( dwSub || v18 )
      {
        v28 = p->m_szItemHistoryFileName;
        v29 = CPlayerDB::GetDalant(&p->m_Param);
        v8 = CPlayerDB::GetGold(&p->m_Param);
        CMgrAvatorItemHistory::pay_money(
          &CPlayer::s_MgrItemHistory,
          p->m_ObjID.m_wIndex,
          "Unit_Bullet_Charge",
          dwSub,
          v18,
          v8,
          v29,
          v28);
      }
      if ( !p->m_byUserDgr )
      {
        eAddGold(0, v18);
        eAddDalant(0, dwSub);
      }
      v26 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v26 == 30 || v26 == 40 || v26 == 50 || v26 == 60 )
      {
        if ( v18 )
        {
          nAmount = 2000 * v18;
          nLv = CPlayerDB::GetLevel(&p->m_Param);
          v9 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v9, nLv, nAmount);
        }
        if ( dwSub )
        {
          v32 = CPlayerDB::GetLevel(&p->m_Param);
          v10 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v10, v32, dwSub);
        }
      }
    }
    CPlayer::SendMsg_UnitBulletFillResult(p, v12, v34, pwBulletIndexa, &dwSub);
  }
}
