/*
 * Function: ?GetSetItemTableInfo@CSUItemSystem@@QEAAHKPEADH@Z
 * Address: 0x1402E4280
 */

signed __int64 __fastcall CSUItemSystem::GetSetItemTableInfo(CSUItemSystem *this, unsigned int dwSetItemEff, char *pStrCode, int nBufSize)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _base_fld *v8; // [sp+20h] [bp-18h]@4
  CSUItemSystem *v9; // [sp+40h] [bp+8h]@1
  char *Dst; // [sp+50h] [bp+18h]@1
  int v11; // [sp+58h] [bp+20h]@1

  v11 = nBufSize;
  Dst = pStrCode;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = CRecordData::GetRecord(v9->m_SUOrigin, dwSetItemEff);
  if ( v8 )
  {
    if ( !strcmp_0(&v8[1].m_strCode[60], "-1") )
    {
      if ( !strcmp_0(&v8[2].m_strCode[56], "-1") )
      {
        if ( !strcmp_0(&v8[3].m_strCode[52], "-1") )
        {
          if ( !strcmp_0(&v8[4].m_strCode[48], "-1") )
          {
            if ( !strcmp_0(&v8[5].m_strCode[44], "-1") )
            {
              if ( !strcmp_0(&v8[6].m_strCode[40], "-1") )
              {
                if ( !strcmp_0(&v8[7].m_strCode[36], "-1") )
                {
                  if ( !strcmp_0(&v8[8].m_strCode[32], "-1") )
                  {
                    if ( !strcmp_0(&v8[8].m_strCode[32], "-1") )
                    {
                      if ( !strcmp_0(&v8[10].m_strCode[24], "-1") )
                      {
                        if ( !strcmp_0(&v8[11].m_strCode[20], "-1") )
                        {
                          if ( !strcmp_0(&v8[12].m_strCode[16], "-1") )
                          {
                            result = 0xFFFFFFFFi64;
                          }
                          else
                          {
                            strcpy_s(Dst, v11, &v8[12].m_strCode[16]);
                            result = 7i64;
                          }
                        }
                        else
                        {
                          strcpy_s(Dst, v11, &v8[11].m_strCode[20]);
                          result = 8i64;
                        }
                      }
                      else
                      {
                        strcpy_s(Dst, v11, &v8[10].m_strCode[24]);
                        result = 8i64;
                      }
                    }
                    else
                    {
                      strcpy_s(Dst, v11, &v8[8].m_strCode[32]);
                      result = 9i64;
                    }
                  }
                  else
                  {
                    strcpy_s(Dst, v11, &v8[8].m_strCode[32]);
                    result = 9i64;
                  }
                }
                else
                {
                  strcpy_s(Dst, v11, &v8[7].m_strCode[36]);
                  result = 5i64;
                }
              }
              else
              {
                strcpy_s(Dst, v11, &v8[6].m_strCode[40]);
                result = 6i64;
              }
            }
            else
            {
              strcpy_s(Dst, v11, &v8[5].m_strCode[44]);
              result = 2i64;
            }
          }
          else
          {
            strcpy_s(Dst, v11, &v8[4].m_strCode[48]);
            result = 3i64;
          }
        }
        else
        {
          strcpy_s(Dst, v11, &v8[3].m_strCode[52]);
          result = 1i64;
        }
      }
      else
      {
        strcpy_s(Dst, v11, &v8[2].m_strCode[56]);
        result = 0i64;
      }
    }
    else
    {
      strcpy_s(Dst, v11, &v8[1].m_strCode[60]);
      result = 4i64;
    }
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
