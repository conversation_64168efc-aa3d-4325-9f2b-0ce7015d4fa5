/*
 * Function: ?IsForwardTransformation@?$CFB_DecryptionTemplate@V?$AbstractPolicyHolder@VCFB_CipherAbstractPolicy@CryptoPP@@V?$SimpleKeyedTransformation@VStreamTransformation@CryptoPP@@@2@@CryptoPP@@@CryptoPP@@EEBA_NXZ
 * Address: 0x14055AD90
 */

char CryptoPP::CFB_DecryptionTemplate<CryptoPP::AbstractPolicyHolder<CryptoPP::CFB_CipherAbstractPolicy,CryptoPP::SimpleKeyedTransformation<CryptoPP::StreamTransformation>>>::IsForwardTransformation()
{
  return 0;
}
