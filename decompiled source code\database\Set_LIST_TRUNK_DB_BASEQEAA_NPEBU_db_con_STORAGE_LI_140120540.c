/*
 * Function: ?Set@_LIST@_TRUNK_DB_BASE@@QEAA_NPEBU_db_con@_STORAGE_LIST@@E@Z
 * Address: 0x140120540
 */

char __fastcall _TRUNK_DB_BASE::_LIST::Set(_TRUNK_DB_BASE::_LIST *this, _STORAGE_LIST::_db_con *pItem, char byRaceCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  _TRUNK_DB_BASE::_LIST *v7; // [sp+30h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v8; // [sp+38h] [bp+10h]@1
  char v9; // [sp+40h] [bp+18h]@1

  v9 = byRaceCode;
  v8 = pItem;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( _INVENKEY::IsFilled(&v7->Key) )
  {
    result = 0;
  }
  else
  {
    v7->Key.byTableCode = v8->m_byTableCode;
    v7->Key.wItemIndex = v8->m_wItemIndex;
    v7->dwDur = v8->m_dwDur;
    v7->dwUpt = v8->m_dwLv;
    v7->byRace = v9;
    v7->lnUID = v8->m_lnUID;
    v7->dwT = v8->m_dwT;
    v7->byCsMethod = v8->m_byCsMethod;
    v7->dwLendRegdTime = v8->m_dwLendRegdTime;
    result = 1;
  }
  return result;
}
