/*
 * Function: ??$_Allocate@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_KPEAU12@@Z
 * Address: 0x14059E320
 */

void *__fastcall std::_Allocate<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(unsigned __int64 a1)
{
  std::bad_alloc v2; // [sp+20h] [bp-28h]@4
  unsigned __int64 v3; // [sp+50h] [bp+8h]@1

  v3 = a1;
  if ( a1 )
  {
    if ( 0xFFFFFFFFFFFFFFFFui64 / a1 < 0x80 )
    {
      std::bad_alloc::bad_alloc(&v2, 0i64);
      CxxThrowException_0((__int64)&v2, (__int64)&TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v3 = 0i64;
  }
  return operator new(v3 << 7);
}
