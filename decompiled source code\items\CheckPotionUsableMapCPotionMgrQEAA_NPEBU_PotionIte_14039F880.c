/*
 * Function: ?CheckPotionUsableMap@CPotionMgr@@QEAA_NPEBU_PotionItem_fld@@PEAVCMapData@@@Z
 * Address: 0x14039F880
 */

char __fastcall CPotionMgr::CheckPotionUsableMap(CPotionMgr *this, _PotionItem_fld *pPotionFld, CMapData *pMap)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  int v7; // [sp+4h] [bp-14h]@7

  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  if ( !pPotionFld || !pMap )
    return 0;
  j = 0;
  v7 = pPotionFld->m_nPotionLim;
  if ( !v7 )
  {
    for ( j = 0; j < 5; ++j )
    {
      if ( pPotionFld->m_nMapCode[j] == pMap->m_pMapSet->m_nPotionLim )
        return 0;
    }
    return 1;
  }
  if ( v7 != 1 )
  {
    if ( v7 == 2 )
    {
      if ( pMap->m_pMapSet->m_nMapType == 1 )
        return 0;
    }
    else if ( v7 == 3 && pMap->m_pMapSet->m_nMapType != 1 )
    {
      return 0;
    }
    return 1;
  }
  for ( j = 0; j < 5; ++j )
  {
    if ( pPotionFld->m_nMapCode[j] == pMap->m_pMapSet->m_nPotionLim )
      return 1;
  }
  return 0;
}
