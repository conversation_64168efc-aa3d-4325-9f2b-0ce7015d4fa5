/*
 * Function: ?AlgorithmName@?$AlgorithmImpl@VCBC_Encryption@CryptoPP@@V?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VEnc@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Encryption@2@@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x1404534A0
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption>>::AlgorithmName(CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption> > *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *resulta; // [sp+48h] [bp+10h]@1

  resulta = result;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption>::StaticAlgorithmName(result);
  return resulta;
}
