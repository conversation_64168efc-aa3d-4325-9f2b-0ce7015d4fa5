/*
 * Function: ?CompleteWork@CashDbWorker@@UEAAXXZ
 * Address: 0x1402EED20
 */

void __fastcall CashDbWorker::CompleteWork(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-58h]@1
  unsigned int nIdx; // [sp+24h] [bp-34h]@5
  Task *pkTsk; // [sp+38h] [bp-20h]@5
  int v7; // [sp+40h] [bp-18h]@6
  CashDbWorker *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  if ( CNationSettingManager::IsCashDBInit(v3) )
  {
    nIdx = 0;
    pkTsk = TaskPool::PopCompleteTsk(v8->_pkPool, &nIdx);
    if ( pkTsk )
    {
      v7 = Task::GetTaskCode(pkTsk);
      if ( v7 )
      {
        switch ( v7 )
        {
          case 1:
            CashDbWorker::_complete_tsk_cash_update(v8, pkTsk);
            break;
          case 2:
            CashDbWorker::_complete_tsk_cash_rollback(v8, pkTsk);
            break;
          case 3:
            CashDbWorker::_complete_tsk_cashitem_buy_dblog(v8, pkTsk);
            break;
          case 4:
            CashDbWorker::_complete_tsk_cash_total_selling_select(v8, pkTsk);
            break;
        }
      }
      else
      {
        CashDbWorker::_complete_tsk_cash_select(v8, pkTsk);
      }
      TaskPool::PushEmptyTsk(v8->_pkPool, nIdx);
    }
  }
}
