/*
 * Function: ??1CGuildBattleRewardItemManager@GUILD_BATTLE@@IEAA@XZ
 * Address: 0x1403C93E0
 */

void __fastcall GUILD_BATTLE::CGuildBattleRewardItemManager::~CGuildBattleRewardItemManager(GUILD_BATTLE::CGuildBattleRewardItemManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleRewardItemManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(&v4->m_kItem);
}
