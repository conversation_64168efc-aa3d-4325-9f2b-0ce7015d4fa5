/*
 * Function: ?Destroy@CPvpUserAndGuildRankingSystem@@SAXXZ
 * Address: 0x14032B080
 */

void CPvpUserAndGuildRankingSystem::Destroy(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-48h]@1
  CPvpUserAndGuildRankingSystem *v3; // [sp+20h] [bp-28h]@4
  struct CPvpUserAndGuildRankingSystem *v4; // [sp+28h] [bp-20h]@4

  v0 = &v2;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v4 = CPvpUserAndGuildRankingSystem::ms_Instance;
  v3 = CPvpUserAndGuildRankingSystem::ms_Instance;
  if ( CPvpUserAndGuildRankingSystem::ms_Instance )
    CPvpUserAndGuildRankingSystem::`scalar deleting destructor'(v3, 1u);
}
