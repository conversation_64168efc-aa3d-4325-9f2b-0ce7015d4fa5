/*
 * Function: ?SendMsg_DamageResult@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400D5290
 */

void __fastcall CPlayer::SendMsg_DamageResult(CPlayer *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v6; // [sp+35h] [bp-43h]@4
  __int16 v7; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = v10->m_nLastBeatenPart;
  v7 = pItem->m_dwDur;
  v6 = pItem->m_wSerial;
  pbyType = 5;
  v9 = 20;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &szMsg, 5u);
}
