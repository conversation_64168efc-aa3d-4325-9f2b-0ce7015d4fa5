/*
 * Function: j_?pc_GuildOfferSuggestRequest@CPlayer@@QEAAXEKPEADKKK@Z
 * Address: 0x140007AA4
 */

void __fastcall CPlayer::pc_GuildOfferSuggestRequest(CPlayer *this, char byMatterType, unsigned int dwMatterDst, char *pwszComment, unsigned int dwMatterObj1, unsigned int dwMatterObj2, unsigned int dwMatterObj3)
{
  CPlayer::pc_GuildOfferSuggestRequest(
    this,
    byMatterType,
    dwMatterDst,
    pwszComment,
    dwMatterObj1,
    dwMatterObj2,
    dwMatterObj3);
}
