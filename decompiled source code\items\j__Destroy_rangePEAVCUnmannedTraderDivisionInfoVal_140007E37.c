/*
 * Function: j_??$_Destroy_range@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderDivisionInfo@@0AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@0@@Z
 * Address: 0x140007E37
 */

void __fastcall std::_Destroy_range<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, std::allocator<CUnmannedTraderDivisionInfo *> *_Al)
{
  std::_Destroy_range<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>(_First, _Last, _Al);
}
