/*
 * Function: ?GetTax@CUnmannedTraderTaxRateManager@@QEAAKEKK@Z
 * Address: 0x14038E110
 */

unsigned int __fastcall CUnmannedTraderTaxRateManager::GetTax(CUnmannedTraderTaxRateManager *this, char byRace, unsigned int dwGuildSerial, unsigned int dwPrice)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int result; // eax@6
  TRC_AutoTrade **v7; // rax@7
  __int64 v8; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTaxRateManager *v9; // [sp+30h] [bp+8h]@1
  char v10; // [sp+38h] [bp+10h]@1
  unsigned int nGuildSerial; // [sp+40h] [bp+18h]@1
  unsigned int nPrice; // [sp+48h] [bp+20h]@1

  nPrice = dwPrice;
  nGuildSerial = dwGuildSerial;
  v10 = byRace;
  v9 = this;
  v4 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::empty(&v9->m_vecTRC)
    || std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::size(&v9->m_vecTRC) <= (unsigned __int8)v10 )
  {
    result = nPrice;
  }
  else
  {
    v7 = std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator[](&v9->m_vecTRC, (unsigned __int8)v10);
    result = TRC_AutoTrade::CalcPrice(*v7, nGuildSerial, nPrice);
  }
  return result;
}
