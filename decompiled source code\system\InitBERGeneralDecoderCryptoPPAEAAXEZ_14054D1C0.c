/*
 * Function: ?Init@BERGeneralDecoder@CryptoPP@@AEAAXE@Z
 * Address: 0x14054D1C0
 */

void __fastcall CryptoPP::BERGeneralDecoder::Init(CryptoPP::BERGeneralDecoder *this, unsigned __int8 a2)
{
  __int64 v2; // rax@1
  CryptoPP *v3; // rcx@1
  bool *v4; // r9@1
  CryptoPP *v5; // rcx@4
  unsigned __int8 v6; // [sp+20h] [bp-18h]@1
  CryptoPP::BERGeneralDecoder *v7; // [sp+40h] [bp+8h]@1
  unsigned __int8 v8; // [sp+48h] [bp+10h]@1

  v8 = a2;
  v7 = this;
  LODWORD(v2) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *, unsigned __int8 *))this->m_inQueue->vfptr[9].__vecDelDtor)(
                  this->m_inQueue,
                  &v6);
  if ( !v2 || (v3 = (CryptoPP *)v8, v6 != v8) )
    CryptoPP::BERDecodeError(v3);
  if ( !CryptoPP::BERLengthDecode(
          (CryptoPP *)v7->m_inQueue,
          (struct CryptoPP::BufferedTransformation *)&v7->m_length,
          (unsigned __int64 *)&v7->m_definiteLength,
          v4) )
    CryptoPP::BERDecodeError(v5);
  if ( !v7->m_definiteLength && !(v8 & 0x20) )
    CryptoPP::BERDecodeError(v5);
}
