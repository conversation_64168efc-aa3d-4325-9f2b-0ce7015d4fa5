/*
 * TestFramework.h - Simple Test Framework for NexusProtection
 * Provides basic unit testing capabilities
 */

#pragma once

#include <string>
#include <vector>
#include <functional>
#include <iostream>
#include <chrono>
#include <exception>

namespace NexusProtection {
namespace TestFramework {

/**
 * Test assertion macros
 */
#define ASSERT_TRUE(condition, message) \
    do { \
        if (!(condition)) { \
            throw TestAssertionException(std::string("ASSERT_TRUE failed: ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

#define ASSERT_FALSE(condition, message) \
    do { \
        if (condition) { \
            throw TestAssertionException(std::string("ASSERT_FALSE failed: ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

#define ASSERT_EQUAL(expected, actual, message) \
    do { \
        if ((expected) != (actual)) { \
            throw TestAssertionException(std::string("ASSERT_EQUAL failed: ") + message + " (expected: " + std::to_string(expected) + ", actual: " + std::to_string(actual) + ") at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

#define ASSERT_NOT_EQUAL(expected, actual, message) \
    do { \
        if ((expected) == (actual)) { \
            throw TestAssertionException(std::string("ASSERT_NOT_EQUAL failed: ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

#define ASSERT_NULL(pointer, message) \
    do { \
        if ((pointer) != nullptr) { \
            throw TestAssertionException(std::string("ASSERT_NULL failed: ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

#define ASSERT_NOT_NULL(pointer, message) \
    do { \
        if ((pointer) == nullptr) { \
            throw TestAssertionException(std::string("ASSERT_NOT_NULL failed: ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

#define ASSERT_THROWS(expression, exception_type, message) \
    do { \
        bool threw = false; \
        try { \
            expression; \
        } catch (const exception_type&) { \
            threw = true; \
        } catch (...) { \
            throw TestAssertionException(std::string("ASSERT_THROWS failed: wrong exception type for ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
        if (!threw) { \
            throw TestAssertionException(std::string("ASSERT_THROWS failed: no exception thrown for ") + message + " at " + __FILE__ + ":" + std::to_string(__LINE__)); \
        } \
    } while(0)

/**
 * Test assertion exception
 */
class TestAssertionException : public std::exception {
public:
    explicit TestAssertionException(const std::string& message) : m_message(message) {}
    
    const char* what() const noexcept override {
        return m_message.c_str();
    }
    
private:
    std::string m_message;
};

/**
 * Test result
 */
struct TestResult {
    std::string testName;
    bool passed{false};
    std::string errorMessage;
    std::chrono::milliseconds duration{0};
    
    TestResult() = default;
    TestResult(const std::string& name, bool success, const std::string& error = "", std::chrono::milliseconds dur = std::chrono::milliseconds(0))
        : testName(name), passed(success), errorMessage(error), duration(dur) {}
};

/**
 * Test case
 */
struct TestCase {
    std::string name;
    std::function<bool()> testFunction;
    
    TestCase() = default;
    TestCase(const std::string& n, std::function<bool()> func) : name(n), testFunction(func) {}
};

/**
 * Test suite base class
 */
class TestSuite {
public:
    explicit TestSuite(const std::string& suiteName) : m_suiteName(suiteName) {}
    virtual ~TestSuite() = default;
    
    /**
     * Add a test to the suite
     * @param testName Name of the test
     * @param testFunction Test function
     */
    void AddTest(const std::string& testName, std::function<bool()> testFunction) {
        m_tests.emplace_back(testName, testFunction);
    }
    
    /**
     * Run all tests in the suite
     * @return Vector of test results
     */
    std::vector<TestResult> RunAllTests() {
        std::vector<TestResult> results;
        
        std::cout << "Running test suite: " << m_suiteName << std::endl;
        std::cout << "===========================================" << std::endl;
        
        for (const auto& test : m_tests) {
            TestResult result = RunSingleTest(test);
            results.push_back(result);
            
            std::cout << "[" << (result.passed ? "PASS" : "FAIL") << "] " 
                      << result.testName << " (" << result.duration.count() << "ms)";
            
            if (!result.passed) {
                std::cout << " - " << result.errorMessage;
            }
            std::cout << std::endl;
        }
        
        // Print summary
        int passed = 0;
        int failed = 0;
        std::chrono::milliseconds totalTime(0);
        
        for (const auto& result : results) {
            if (result.passed) {
                passed++;
            } else {
                failed++;
            }
            totalTime += result.duration;
        }
        
        std::cout << "===========================================" << std::endl;
        std::cout << "Test suite completed: " << m_suiteName << std::endl;
        std::cout << "Passed: " << passed << ", Failed: " << failed 
                  << ", Total: " << (passed + failed) << std::endl;
        std::cout << "Total time: " << totalTime.count() << "ms" << std::endl;
        std::cout << std::endl;
        
        return results;
    }
    
    /**
     * Run a specific test by name
     * @param testName Name of the test to run
     * @return Test result
     */
    TestResult RunTest(const std::string& testName) {
        for (const auto& test : m_tests) {
            if (test.name == testName) {
                return RunSingleTest(test);
            }
        }
        
        return TestResult(testName, false, "Test not found");
    }
    
    /**
     * Get the suite name
     * @return Suite name
     */
    const std::string& GetSuiteName() const {
        return m_suiteName;
    }
    
    /**
     * Get the number of tests
     * @return Number of tests
     */
    size_t GetTestCount() const {
        return m_tests.size();
    }
    
    /**
     * Get test names
     * @return Vector of test names
     */
    std::vector<std::string> GetTestNames() const {
        std::vector<std::string> names;
        for (const auto& test : m_tests) {
            names.push_back(test.name);
        }
        return names;
    }

protected:
    /**
     * Setup method called before each test
     * Override in derived classes if needed
     */
    virtual void SetUp() {}
    
    /**
     * Teardown method called after each test
     * Override in derived classes if needed
     */
    virtual void TearDown() {}

private:
    std::string m_suiteName;
    std::vector<TestCase> m_tests;
    
    /**
     * Run a single test
     * @param test Test case to run
     * @return Test result
     */
    TestResult RunSingleTest(const TestCase& test) {
        auto startTime = std::chrono::high_resolution_clock::now();
        
        try {
            SetUp();
            
            bool result = test.testFunction();
            
            TearDown();
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            return TestResult(test.name, result, "", duration);
            
        } catch (const TestAssertionException& e) {
            TearDown();
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            return TestResult(test.name, false, e.what(), duration);
            
        } catch (const std::exception& e) {
            TearDown();
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            return TestResult(test.name, false, std::string("Unexpected exception: ") + e.what(), duration);
            
        } catch (...) {
            TearDown();
            
            auto endTime = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
            
            return TestResult(test.name, false, "Unknown exception", duration);
        }
    }
};

/**
 * Test runner utility
 */
class TestRunner {
public:
    /**
     * Run multiple test suites
     * @param suites Vector of test suites
     * @return Overall success status
     */
    static bool RunTestSuites(const std::vector<TestSuite*>& suites) {
        bool allPassed = true;
        int totalPassed = 0;
        int totalFailed = 0;
        std::chrono::milliseconds totalTime(0);
        
        for (TestSuite* suite : suites) {
            if (suite) {
                std::vector<TestResult> results = suite->RunAllTests();
                
                for (const auto& result : results) {
                    if (result.passed) {
                        totalPassed++;
                    } else {
                        totalFailed++;
                        allPassed = false;
                    }
                    totalTime += result.duration;
                }
            }
        }
        
        std::cout << "========================================" << std::endl;
        std::cout << "ALL TESTS COMPLETED" << std::endl;
        std::cout << "Total Passed: " << totalPassed << std::endl;
        std::cout << "Total Failed: " << totalFailed << std::endl;
        std::cout << "Total Time: " << totalTime.count() << "ms" << std::endl;
        std::cout << "Overall Result: " << (allPassed ? "PASS" : "FAIL") << std::endl;
        std::cout << "========================================" << std::endl;
        
        return allPassed;
    }
};

} // namespace TestFramework
} // namespace NexusProtection
