/*
 * Function: ?Update_CharacterData@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_update_char_query@@@Z
 * Address: 0x1404916B0
 */

char __fastcall CRFWorldDatabase::Update_CharacterData(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_update_char_query *pUpdateQuery)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-48h]@1
  void *SQLStmt; // [sp+20h] [bp-28h]@14
  __int16 v8; // [sp+30h] [bp-18h]@12
  CRFWorldDatabase *v9; // [sp+50h] [bp+8h]@1
  unsigned int v10; // [sp+58h] [bp+10h]@1
  _worlddb_update_char_query *v11; // [sp+60h] [bp+18h]@1

  v11 = pUpdateQuery;
  v10 = dwSerial;
  v9 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 0);
  if ( v9->m_bSaveDBLog )
    CRFNewDatabase::FmtLog((CRFNewDatabase *)&v9->vfptr, "Update_CharacterData : %d", v10);
  if ( !v9->m_hStmtUpdate && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr) )
  {
    CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "ReConnectDataBase Fail. Query : Update_CharacterData");
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
    return 0;
  }
  if ( *v11->szBaseQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szBaseQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szBaseQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szGeneralQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szGeneralQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szGeneralQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szSupplementQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szSupplementQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szSupplementQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szInvenQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szInvenQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szInvenQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szUnitQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szUnitQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szUnitQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szUIQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szUIQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szUIQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szQuestQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szQuestQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szQuestQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->wszBuddyQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->wszBuddyQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->wszBuddyQuery, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->wszTrunkQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->wszTrunkQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->wszTrunkQuery, "SQLExecDirectW", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szItemCombineExQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szItemCombineExQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog(
          (CRFNewDatabase *)&v9->vfptr,
          v8,
          v11->szItemCombineExQuery,
          "SQLExecDirectA",
          SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szAMPInvenQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szAMPInvenQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szAMPInvenQuery, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szPvpPointLimitQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szPvpPointLimitQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog(
          (CRFNewDatabase *)&v9->vfptr,
          v8,
          v11->szPvpPointLimitQuery,
          "SQLExecDirectA",
          SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->wszBossCryMsgQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->wszBossCryMsgQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog(
          (CRFNewDatabase *)&v9->vfptr,
          v8,
          v11->wszBossCryMsgQuery,
          "_SQLExecDirect",
          SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szPvpOrderViewQurey )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szPvpOrderViewQurey, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog(
          (CRFNewDatabase *)&v9->vfptr,
          v8,
          v11->szPvpOrderViewQurey,
          "_SQLExecDirect",
          SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szNPCQuestQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirect_0(v9->m_hStmtUpdate, v11->szNPCQuestQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szNPCQuestQuery, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szPcBangPlayTimeQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULLHSTMT : Update_ChaaterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirect_0(v9->m_hStmtUpdate, v11->szPcBangPlayTimeQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog(
          (CRFNewDatabase *)&v9->vfptr,
          v8,
          v11->szPcBangPlayTimeQuery,
          "_SQLExecDirect",
          SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szPotionDelayQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULLHSTMT : Update_ChaaterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirect_0(v9->m_hStmtUpdate, v11->szPotionDelayQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szPotionDelayQuery, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szOreCuttingQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULLHSTMT : Update_ChaaterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirect_0(v9->m_hStmtUpdate, v11->szOreCuttingQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szOreCuttingQuery, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->szPcBangFavorQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULLHSTMT : Update_ChaaterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirect_0(v9->m_hStmtUpdate, v11->szPcBangFavorQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szPcBangFavorQuery, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( *v11->wszExtTrunkQuery )
  {
    if ( !v9->m_hStmtUpdate )
    {
      CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
      CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
      return 0;
    }
    v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->wszExtTrunkQuery, -3);
    if ( v8 )
    {
      if ( v8 != 1 )
      {
        SQLStmt = v9->m_hStmtUpdate;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->wszExtTrunkQuery, "SQLExecDirectW", SQLStmt);
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
        return 0;
      }
    }
  }
  if ( !*v11->szTimeLimitInfoQuery )
    goto LABEL_140;
  if ( !v9->m_hStmtUpdate )
  {
    CRFNewDatabase::ErrLog((CRFNewDatabase *)&v9->vfptr, "m_hStmtUpdate == SQL_NULL_HSTMT : Update_CharacterData");
    CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v9->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
    return 0;
  }
  v8 = SQLExecDirectA_0(v9->m_hStmtUpdate, v11->szTimeLimitInfoQuery, -3);
  if ( v8 && v8 != 1 )
  {
    SQLStmt = v9->m_hStmtUpdate;
    CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v9->vfptr, v8, v11->szTimeLimitInfoQuery, "SQLExecDirectA", SQLStmt);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v9->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
    CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v9->vfptr, v8, v9->m_hStmtUpdate);
    result = 0;
  }
  else
  {
LABEL_140:
    CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v9->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v9->vfptr, 1);
    if ( v9->m_bSaveDBLog )
      CLogFile::Write(&v9->m_ProcessLogA, "Update_CharacterData Success : %d", v10);
    result = 1;
  }
  return result;
}
