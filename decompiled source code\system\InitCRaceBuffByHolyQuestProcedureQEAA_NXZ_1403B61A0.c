/*
 * Function: ?Init@CRaceBuffByHolyQuestProcedure@@QEAA_NXZ
 * Address: 0x1403B61A0
 */

bool __fastcall CRaceBuffByHolyQuestProcedure::Init(CRaceBuffByHolyQuestProcedure *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRaceBuffByHolyQuestProcedure *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRaceBuffHolyQuestResultInfo::Load(&v5->m_kBuffHolyQestResultInfo) )
    result = CRaceBuffInfoByHolyQuestList::Init(&v5->m_kBuffInfo) != 0;
  else
    result = 0;
  return result;
}
