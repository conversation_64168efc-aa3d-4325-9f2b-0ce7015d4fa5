/*
 * Function: ??A?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAAAEAVCUnmannedTraderSchedule@@_K@Z
 * Address: 0x140351C40
 */

CUnmannedTraderSchedule *__fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
