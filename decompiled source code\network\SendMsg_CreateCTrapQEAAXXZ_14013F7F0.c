/*
 * Function: ?SendMsg_Create@CTrap@@QEAAXXZ
 * Address: 0x14013F7F0
 */

void __fastcall CTrap::SendMsg_Create(CTrap *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v5; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v6; // [sp+3Ch] [bp-5Ch]@4
  __int16 pShort; // [sp+40h] [bp-58h]@4
  unsigned int v8; // [sp+46h] [bp-52h]@4
  char v9; // [sp+4Ah] [bp-4Eh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v11; // [sp+65h] [bp-33h]@4
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CTrap *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = (unsigned __int64)&v3 ^ _security_cookie;
  v5 = v13->m_ObjID.m_wIndex;
  *(_WORD *)szMsg = v13->m_pRecordSet->m_dwIndex;
  v6 = v13->m_dwObjSerial;
  FloatToShort(v13->m_fCurPos, &pShort, 3);
  v8 = v13->m_dwMasterSerial;
  v9 = v13->m_byRaceCode;
  pbyType = 3;
  v11 = -43;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 19, 0);
}
