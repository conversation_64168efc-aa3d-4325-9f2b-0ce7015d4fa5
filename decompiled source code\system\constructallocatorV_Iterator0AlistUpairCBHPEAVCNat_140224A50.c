/*
 * Function: ?construct@?$allocator@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@@std@@QEAAXPEAV?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@2@AEBV342@@Z
 * Address: 0x140224A50
 */

void __fastcall std::allocator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>>::construct(std::allocator<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> > *this, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Ptr, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>>(
    _Ptr,
    _Val);
}
