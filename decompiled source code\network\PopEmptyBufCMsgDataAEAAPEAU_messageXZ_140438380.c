/*
 * Function: ?PopEmptyBuf@CMsgData@@AEAAPEAU_message@@XZ
 * Address: 0x140438380
 */

_message *__fastcall CMsgData::PopEmptyBuf(CMsgData *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _message *result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  _message *v5; // [sp+20h] [bp-18h]@4
  CMsgData *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMyCriticalSection::Lock(&v6->m_csEmpty);
  v5 = v6->m_gmListEmptyHead.pNext;
  if ( v5 == &v6->m_gmListEmptyTail )
  {
    CMyCriticalSection::Unlock(&v6->m_csEmpty);
    result = 0i64;
  }
  else
  {
    v6->m_gmListEmptyHead.pNext = v5->pNext;
    v5->pNext->pPrev = &v6->m_gmListEmptyHead;
    CMyCriticalSection::Unlock(&v6->m_csEmpty);
    result = v5;
  }
  return result;
}
