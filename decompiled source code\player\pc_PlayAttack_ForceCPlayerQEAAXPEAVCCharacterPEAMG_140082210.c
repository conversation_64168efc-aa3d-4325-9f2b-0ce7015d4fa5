/*
 * Function: ?pc_PlayAttack_Force@CPlayer@@QEAAXPEAVCCharacter@@PEAMGPEAGG@Z
 * Address: 0x140082210
 */

void __fastcall CPlayer::pc_PlayAttack_Force(CPlayer *this, CCharacter *pDst, float *pfAreaPos, unsigned __int16 wForceSerial, unsigned __int16 *pConsumeSerial, unsigned __int16 wEffBtSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  float v8; // xmm0_4@22
  int v9; // eax@40
  int v10; // eax@41
  unsigned __int64 v11; // rdx@48
  int v12; // eax@51
  CCharacter *v13; // rdx@52
  int v14; // eax@52
  CCharacter *v15; // rdx@53
  int v16; // eax@53
  int v17; // eax@58
  signed int v18; // eax@59
  int v19; // eax@60
  int v20; // eax@60
  signed int v21; // eax@65
  float v22; // xmm0_4@66
  __int64 v23; // [sp+0h] [bp-698h]@1
  _force_fld **ppForceFld; // [sp+20h] [bp-678h]@51
  _STORAGE_LIST::_db_con **ppForceItem; // [sp+28h] [bp-670h]@51
  unsigned __int16 *pdwDecPoint; // [sp+30h] [bp-668h]@51
  float fAddEffBtFc; // [sp+38h] [bp-660h]@51
  _STORAGE_LIST::_db_con *pForceItem; // [sp+58h] [bp-640h]@4
  _force_fld *pForceFld; // [sp+78h] [bp-620h]@4
  float pfTarPos; // [sp+98h] [bp-600h]@4
  float v31; // [sp+9Ch] [bp-5FCh]@4
  int v32; // [sp+A0h] [bp-5F8h]@4
  CCharacter *pDsta; // [sp+B8h] [bp-5E0h]@4
  unsigned __int16 v34; // [sp+C4h] [bp-5D4h]@4
  char v35; // [sp+C6h] [bp-5D2h]@4
  _STORAGE_LIST::_db_con *pEffBulletItem; // [sp+E8h] [bp-5B0h]@6
  _BulletItem_fld *v37; // [sp+108h] [bp-590h]@6
  char v38; // [sp+114h] [bp-584h]@6
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+128h] [bp-570h]@6
  char v40; // [sp+130h] [bp-568h]@6
  int pnConsume; // [sp+158h] [bp-540h]@6
  char v42; // [sp+15Ch] [bp-53Ch]@6
  bool pbOverLap; // [sp+184h] [bp-514h]@6
  char v44; // [sp+185h] [bp-513h]@6
  CAttack pAt; // [sp+1B0h] [bp-4E8h]@18
  _attack_param pAP; // [sp+4C0h] [bp-1D8h]@20
  unsigned __int16 v47; // [sp+544h] [bp-154h]@20
  float v48; // [sp+548h] [bp-150h]@20
  bool v49; // [sp+54Ch] [bp-14Ch]@26
  int nParamCode; // [sp+550h] [bp-148h]@37
  int nValue; // [sp+554h] [bp-144h]@43
  CPartyModeKillMonsterExpNotify kPartyExpNotify; // [sp+570h] [bp-128h]@45
  int nTotalDam; // [sp+604h] [bp-94h]@46
  int v54; // [sp+608h] [bp-90h]@48
  int v55; // [sp+60Ch] [bp-8Ch]@48
  int j; // [sp+610h] [bp-88h]@48
  int v57; // [sp+614h] [bp-84h]@51
  CPlayer *v58; // [sp+618h] [bp-80h]@55
  unsigned int dwAlter; // [sp+620h] [bp-78h]@64
  int nAlter; // [sp+624h] [bp-74h]@65
  unsigned int v61; // [sp+628h] [bp-70h]@65
  float v62; // [sp+62Ch] [bp-6Ch]@68
  unsigned int dwNewStat; // [sp+630h] [bp-68h]@70
  unsigned __int16 v64; // [sp+634h] [bp-64h]@80
  __int64 v65; // [sp+640h] [bp-58h]@4
  int v66; // [sp+648h] [bp-50h]@41
  CCharacter *v67; // [sp+650h] [bp-48h]@51
  CGameObjectVtbl *v68; // [sp+658h] [bp-40h]@51
  int v69; // [sp+660h] [bp-38h]@52
  CGameObjectVtbl *v70; // [sp+668h] [bp-30h]@52
  int v71; // [sp+670h] [bp-28h]@53
  CGameObjectVtbl *v72; // [sp+678h] [bp-20h]@53
  float v73; // [sp+680h] [bp-18h]@59
  unsigned __int64 v74; // [sp+688h] [bp-10h]@4
  CPlayer *v75; // [sp+6A0h] [bp+8h]@1
  unsigned __int16 v76; // [sp+6B8h] [bp+20h]@1

  v76 = wForceSerial;
  v75 = this;
  v6 = &v23;
  for ( i = 420i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v65 = -2i64;
  v74 = (unsigned __int64)&v23 ^ _security_cookie;
  pForceItem = 0i64;
  pForceFld = 0i64;
  pfTarPos = *pfAreaPos;
  v31 = v75->m_fCurPos[1];
  v32 = *((_DWORD *)pfAreaPos + 1);
  pDsta = pDst;
  v34 = 0;
  memset(&v35, 0, 4ui64);
  if ( pDst )
    memcpy_0(pfAreaPos, pDst->m_fCurPos, 0xCui64);
  pEffBulletItem = 0i64;
  v37 = 0i64;
  v38 = CPlayer::_pre_check_force_attack(
          v75,
          pDsta,
          &pfTarPos,
          v76,
          &pForceFld,
          &pForceItem,
          &v34,
          wEffBtSerial,
          &pEffBulletItem,
          &v37);
  ppConsumeItems = 0i64;
  memset(&v40, 0, 0x10ui64);
  pnConsume = 0;
  memset(&v42, 0, 8ui64);
  pbOverLap = 0;
  memset(&v44, 0, 2ui64);
  if ( !v38
    && !CPlayer::GetUseConsumeItem(
          v75,
          pForceFld->m_ConsumeItemList,
          pConsumeSerial,
          &ppConsumeItems,
          &pnConsume,
          &pbOverLap) )
  {
    v38 = -61;
  }
  if ( v38 )
  {
    CPlayer::SendMsg_AttackResult_Error(v75, v38);
    if ( v75->m_bMove )
    {
      CCharacter::Stop((CCharacter *)&v75->vfptr);
      CGameObject::SendMsg_BreakStop((CGameObject *)&v75->vfptr);
    }
    return;
  }
  if ( pForceFld->m_nEffectGroup == 6 || pForceFld->m_nEffectGroup == 4 )
    pDsta = 0i64;
  if ( pForceFld->m_nEffectGroup == 4 )
    memcpy_0(&pfTarPos, v75->m_fCurPos, 0xCui64);
  CAttack::CAttack(&pAt, (CCharacter *)&v75->vfptr);
  if ( _effect_parameter::GetEff_State(&v75->m_EP, 14) )
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v75->vfptr, 2, 14);
  _attack_param::_attack_param(&pAP);
  v47 = -1;
  v48 = FLOAT_1_0;
  if ( pEffBulletItem )
  {
    v48 = v37->m_fGAAF;
    v47 = pEffBulletItem->m_wItemIndex;
  }
  v8 = v48;
  CPlayer::make_force_attack_param(v75, pDsta, pForceFld, pForceItem, &pfTarPos, &pAP, pEffBulletItem, v48);
  if ( pEffBulletItem && wEffBtSerial != 0xFFFF )
    CAttack::AttackForce(&pAt, &pAP, 1);
  else
    CAttack::AttackForce(&pAt, &pAP, 0);
  v49 = 0;
  if ( pAt.m_DamList[0].m_nDamage > 0 && v75->m_pmWpn.nActiveType > -1 && rand() % 100 < v75->m_pmWpn.nActiveProb )
    v49 = CPlayer::WeaponSFActive(v75, pAt.m_DamList, &pAt.m_nDamagedObjNum, &pAP.nShotNum, 0xFFFFu);
  CAttack::SetActiveSucc(&pAt, v49);
  _effect_parameter::GetEff_Plus(&v75->m_EP, 13);
  _ATTACK_DELAY_CHECKER::SetDelay(&v75->m_AttDelayChker, (signed int)ffloor(pForceFld->m_fActDelay + v8));
  if ( _effect_parameter::GetEff_State(&v75->m_EP, 21) )
  {
    if ( pAP.nAttactType != 4 && pAP.nAttactType != 5 && pAP.nAttactType != 6 && pAP.nAttactType != 7 )
      return;
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v75->vfptr, 2, 21);
  }
  for ( nParamCode = 0; nParamCode < 3; ++nParamCode )
  {
    if ( (signed int)*(&v34 + nParamCode) > 0 )
    {
      v9 = CPlayer::GetGauge(v75, nParamCode);
      if ( v9 - *(&v34 + nParamCode) <= 0 )
      {
        v66 = 0;
      }
      else
      {
        v10 = CPlayer::GetGauge(v75, nParamCode);
        v66 = v10 - *(&v34 + nParamCode);
      }
      nValue = v66;
      CPlayer::SetGauge(v75, nParamCode, v66, 1);
    }
    CPlayer::SendMsg_Recover(v75);
  }
  CPlayer::DeleteUseConsumeItem(v75, &ppConsumeItems, &pnConsume, &pbOverLap);
  CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
  if ( !pAt.m_bFailure )
  {
    nTotalDam = CPlayer::_check_exp_after_attack(v75, pAt.m_nDamagedObjNum, pAt.m_DamList, &kPartyExpNotify);
    if ( nTotalDam > 0 )
      CPlayer::_check_dst_param_after_attack(v75, nTotalDam, pDsta);
  }
  CPlayer::SendMsg_AttackResult_Force(v75, &pAt);
  CPartyModeKillMonsterExpNotify::Notify(&kPartyExpNotify);
  v54 = 0;
  v55 = 0;
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v57 = pAt.m_DamList[j].m_nActiveDamage + pAt.m_DamList[j].m_nDamage;
    v12 = CPlayerDB::GetLevel(&v75->m_Param);
    v67 = pAt.m_DamList[j].m_pChar;
    v68 = v67->vfptr;
    LOBYTE(fAddEffBtFc) = 1;
    LODWORD(pdwDecPoint) = pForceFld->m_dwIndex;
    LODWORD(ppForceItem) = 1;
    LOBYTE(ppForceFld) = pAt.m_bIsCrtAtt;
    ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v68->SetDamage)(
      v67,
      (unsigned int)v57,
      v75,
      (unsigned int)v12);
    if ( CPlayer::IsChaosMode(v75) )
    {
      v69 = ((int (__fastcall *)(CPlayer *))v75->vfptr->GetObjRace)(v75);
      v13 = pAt.m_DamList[j].m_pChar;
      v70 = pAt.m_DamList[j].m_pChar->vfptr;
      v14 = ((int (__fastcall *)(CCharacter *))v70->GetObjRace)(v13);
      if ( v69 == v14 )
        continue;
    }
    v71 = ((int (__fastcall *)(CPlayer *))v75->vfptr->GetObjRace)(v75);
    v15 = pAt.m_DamList[j].m_pChar;
    v72 = pAt.m_DamList[j].m_pChar->vfptr;
    v16 = ((int (__fastcall *)(CCharacter *))v72->GetObjRace)(v15);
    if ( v71 == v16 && !pAt.m_DamList[j].m_pChar->m_ObjID.m_byID )
    {
      v58 = (CPlayer *)pAt.m_DamList[j].m_pChar;
      if ( CPlayer::IsPunished(v58, 1, 0) )
        continue;
    }
    if ( !pAt.m_bFailure && pForceFld->m_nMastIndex < 0x18u )
    {
      v17 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
      if ( CPlayer::IsPassMasteryLimitLvDiff(v75, v17) )
      {
        v73 = (float)pAt.m_DamList[j].m_nDamage;
        v18 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetMaxHP)(pAt.m_DamList[j].m_pChar);
        if ( (float)(v73 / (float)v18) >= 0.0099999998 )
        {
          v19 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
          v20 = CPlayer::GetMasteryCumAfterAttack(v75, v19);
          v54 += v20;
          ++v55;
        }
      }
    }
  }
  if ( v55 > 0 && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v75->vfptr->IsInTown)(v75) )
  {
    v11 = (unsigned __int64)v54 >> 32;
    LODWORD(v11) = v54 % v55;
    dwAlter = v54 / v55;
    if ( v54 / v55 > 0 )
    {
      LOBYTE(pdwDecPoint) = 1;
      ppForceItem = (_STORAGE_LIST::_db_con **)"CPlayer::pc_PlayAttack_Force()---0";
      LOBYTE(ppForceFld) = 0;
      CPlayer::Emb_AlterStat(v75, 4, pForceFld->m_nMastIndex, dwAlter, 0, "CPlayer::pc_PlayAttack_Force()---0", 1);
      v21 = ((int (__fastcall *)(CPlayer *))v75->vfptr->GetLevel)(v75);
      nAlter = 5 * (v21 / 10 + 1) * dwAlter;
      v61 = 5 * (v21 / 10 + 1) * dwAlter;
      if ( CPlayer::IsApplyPcbangPrimium(v75) )
      {
        v22 = (float)nAlter + (float)((float)(signed int)v61 * (float)(PCBANG_PRIMIUM_FAVOR::SKILL_FORCE_MASTERY - 1.0));
        nAlter = (signed int)ffloor(v22);
      }
      else
      {
        v22 = (float)nAlter + (float)((float)(signed int)v61 * (float)(FORCE_LIVER_ACCUM_RATE - 1.0));
        nAlter = (signed int)ffloor(v22);
      }
      _effect_parameter::GetEff_Have(&v75->m_EP, 6);
      v62 = v22;
      if ( v22 > 1.0 )
        nAlter = (signed int)ffloor((float)nAlter + (float)((float)(signed int)v61 * (float)(v62 - 1.0)));
      LOBYTE(ppForceItem) = 1;
      LOBYTE(ppForceFld) = 0;
      dwNewStat = CPlayer::Emb_AlterDurPoint(v75, 3, pForceItem->m_byStorageIndex, nAlter, 0, 1);
      CPlayer::SendMsg_FcitemInform(v75, v76, dwNewStat);
    }
  }
  if ( pDsta
    && v75->m_pRecalledAnimusChar
    && ((int (__fastcall *)(CCharacter *, unsigned __int64))pDsta->vfptr->GetHP)(pDsta, v11) > 0 )
  {
    CAnimus::MasterAttack_MasterInform(v75->m_pRecalledAnimusChar, pDsta);
  }
  if ( pDsta && ((int (__fastcall *)(CCharacter *, unsigned __int64))pDsta->vfptr->GetHP)(pDsta, v11) > 0 )
    _TOWER_PARAM::NotifyOwnerAttackInform(&v75->m_pmTwr, pDsta);
  CPlayer::SetBattleMode(v75, 1);
  if ( pEffBulletItem && pAP.nEffShotNum > 0 )
  {
    LOBYTE(ppForceItem) = 1;
    LOBYTE(ppForceFld) = 0;
    v64 = CPlayer::Emb_AlterDurPoint(v75, 2, pEffBulletItem->m_byStorageIndex, -pAP.nEffShotNum, 0, 1);
    if ( v64 )
      CPlayer::SendMsg_AlterWeaponBulletInform(v75, pEffBulletItem->m_wSerial, v64);
    else
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v75->m_ObjID.m_wIndex,
        pEffBulletItem,
        v75->m_szItemHistoryFileName);
  }
  CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
}
