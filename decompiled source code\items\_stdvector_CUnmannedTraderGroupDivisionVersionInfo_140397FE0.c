/*
 * Function: _std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::back_::_1_::dtor$0
 * Address: 0x140397FE0
 */

void __fastcall std::vector_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo___::back_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::~_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>((std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *)(a2 + 40));
}
