/*
 * Function: ?init@CGuildMasterEffect@@QEAA_NXZ
 * Address: 0x1403F4720
 */

char __fastcall CGuildMasterEffect::init(CGuildMasterEffect *this)
{
  LODWORD(this->m_EffectData[0].attack_value) = 0;
  LODWORD(this->m_EffectData[0].defence_value) = 0;
  LODWORD(this->m_EffectData[1].attack_value) = 0;
  LODWORD(this->m_EffectData[1].defence_value) = 0;
  this->m_EffectData[2].attack_value = FLOAT_0_050000001;
  this->m_EffectData[2].defence_value = FLOAT_0_050000001;
  this->m_EffectData[3].attack_value = FLOAT_0_1;
  this->m_EffectData[3].defence_value = FLOAT_0_1;
  this->m_EffectData[4].attack_value = FLOAT_0_15000001;
  this->m_EffectData[4].defence_value = FLOAT_0_15000001;
  this->m_EffectData[5].attack_value = FLOAT_0_2;
  this->m_EffectData[5].defence_value = FLOAT_0_2;
  this->m_EffectData[6].attack_value = FLOAT_0_25;
  this->m_EffectData[6].defence_value = FLOAT_0_25;
  this->m_EffectData[7].attack_value = FLOAT_0_30000001;
  this->m_EffectData[7].defence_value = FLOAT_0_30000001;
  this->m_byAdjustableGrade = 3;
  return 1;
}
