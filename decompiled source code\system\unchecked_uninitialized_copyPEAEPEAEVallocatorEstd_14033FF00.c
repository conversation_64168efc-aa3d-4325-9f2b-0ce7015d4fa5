/*
 * Function: ??$unchecked_uninitialized_copy@PEAEPEAEV?$allocator@E@std@@@stdext@@YAPEAEPEAE00AEAV?$allocator@E@std@@@Z
 * Address: 0x14033FF00
 */

char *__fastcall stdext::unchecked_uninitialized_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(char *_First, char *_Last, char *_Dest, std::allocator<unsigned char> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  char *__formal; // [sp+50h] [bp+8h]@1
  char *_Lasta; // [sp+58h] [bp+10h]@1
  char *_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<unsigned char> *v13; // [sp+68h] [bp+20h]@1

  v13 = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<unsigned char *,unsigned char *>(&__formal, &_Desta);
  return std::_Uninit_copy<unsigned char *,unsigned char *,std::allocator<unsigned char>>(
           __formal,
           _Lasta,
           _Desta,
           v13,
           v9,
           v8);
}
