/*
 * Function: ?Get<PERSON>efFC@CPlayer@@UEAAHHPEAVCCharacter@@PEAH@Z
 * Address: 0x140060260
 */

signed __int64 __fastcall CPlayer::GetDefFC(CPlayer *this, int nAttactPart, CCharacter *pAttChar, int *pnConvertPart)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float v6; // xmm0_4@28
  float v8; // xmm0_4@46
  float v9; // xmm0_4@46
  float v10; // xmm0_4@53
  CPvpUserAndGuildRankingSystem *v11; // rax@58
  float v12; // xmm0_4@73
  __int64 v13; // [sp+0h] [bp-E8h]@1
  float v14; // [sp+20h] [bp-C8h]@4
  bool v15; // [sp+24h] [bp-C4h]@5
  CCharacter *v16; // [sp+28h] [bp-C0h]@8
  float v17; // [sp+30h] [bp-B8h]@12
  char *v18; // [sp+38h] [bp-B0h]@18
  char v19; // [sp+40h] [bp-A8h]@18
  char *v20; // [sp+48h] [bp-A0h]@22
  _base_fld *v21; // [sp+50h] [bp-98h]@23
  int v22; // [sp+58h] [bp-90h]@28
  int v23; // [sp+5Ch] [bp-8Ch]@28
  int v24; // [sp+68h] [bp-80h]@33
  char v25; // [sp+6Ch] [bp-7Ch]@33
  int j; // [sp+84h] [bp-64h]@33
  char *v27; // [sp+88h] [bp-60h]@35
  _base_fld *v28; // [sp+90h] [bp-58h]@37
  _base_fld *v29; // [sp+98h] [bp-50h]@40
  _base_fld *v30; // [sp+A0h] [bp-48h]@43
  int k; // [sp+A8h] [bp-40h]@43
  char *v32; // [sp+B0h] [bp-38h]@47
  _base_fld *v33; // [sp+B8h] [bp-30h]@49
  _base_fld *v34; // [sp+C0h] [bp-28h]@50
  char v35; // [sp+C8h] [bp-20h]@58
  float v36; // [sp+CCh] [bp-1Ch]@73
  unsigned int dwSerial; // [sp+D0h] [bp-18h]@58
  int v38; // [sp+D4h] [bp-14h]@58
  char v39; // [sp+D8h] [bp-10h]@58
  CPlayer *v40; // [sp+F0h] [bp+8h]@1
  CCharacter *v41; // [sp+100h] [bp+18h]@1
  int *v42; // [sp+108h] [bp+20h]@1

  v42 = pnConvertPart;
  v41 = pAttChar;
  v40 = this;
  v4 = &v13;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v40->m_nLastBeatenPart = nAttactPart;
  v14 = 0.0;
  if ( CPlayer::IsRidingUnit(v40) )
  {
    v14 = (float)v40->m_nUnitDefFc * v40->m_fUnitPv_DefFc;
  }
  else
  {
    v15 = 0;
    if ( v41 )
    {
      if ( !v41->m_ObjID.m_byID )
      {
        v15 = _effect_parameter::GetEff_State(&v41->m_EP, 11);
        if ( !v15 )
        {
          v16 = 0i64;
          if ( !v41->m_ObjID.m_byID )
            v16 = v41;
          _effect_parameter::GetEff_Plus(&v41->m_EP, 28);
          if ( 0.0 > 0.0 || v16 )
          {
            _effect_parameter::GetEff_Plus(&v41->m_EP, 28);
            v17 = 0.0;
            if ( v16 )
            {
              _effect_parameter::GetEff_Plus(&v41->m_EP, 41);
              v17 = v17 + 0.0;
            }
            if ( rand() % 100 <= (signed int)ffloor(v17) )
              v15 = 1;
          }
        }
      }
    }
    if ( v15 )
      CCharacter::SendMsg_AttackActEffect(v41, 1, (CCharacter *)&v40->vfptr);
    v18 = &v40->m_Param.m_dbEquip.m_pStorageList[5].m_bLoad;
    v19 = 0;
    if ( *v18 && CPlayer::GetEffectEquipCode(v40, 1, 5) == 1 )
      v19 = 1;
    if ( v19 )
    {
      v20 = &v40->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
      if ( *v20 )
      {
        v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v20 + 3));
        if ( *(_DWORD *)&v21[4].m_strCode[12] == 100 )
          v19 = 0;
      }
    }
    if ( v41 && v19 && !v15 )
    {
      v22 = _MASTERY_PARAM::GetMasteryPerMast(&v40->m_pmMst, 2, 0);
      v6 = (float)((float)((float)v22 / 99.0) * 20.0) + 5.0;
      v23 = (signed int)ffloor(v6);
      _effect_parameter::GetEff_Plus(&v40->m_EP, 29);
      v23 = (signed int)ffloor((float)v23 + v6);
      if ( v23 > 100 )
        v23 = 100;
      if ( rand() % 100 < v23 )
      {
        v40->m_nLastBeatenPart = 5;
        return 4294967294i64;
      }
    }
    if ( v19 )
    {
      v24 = 0;
      memset(&v25, 0, 0x10ui64);
      for ( j = 0; j < 5; ++j )
      {
        v27 = &v40->m_Param.m_dbEquip.m_pStorageList[j].m_bLoad;
        if ( *v27 && CPlayer::GetEffectEquipCode(v40, 1, j) == 1 )
        {
          v28 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, *(_WORD *)(v27 + 3));
          if ( v28 )
            *(&v24 + j) = *(_DWORD *)&v28[5].m_strCode[44];
        }
        else
        {
          v29 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + j, v40->m_Param.m_dbChar.m_byDftPart[j]);
          if ( v29 )
            *(&v24 + j) = *(_DWORD *)&v29[5].m_strCode[44];
        }
      }
      v30 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 5, *(_WORD *)(v18 + 3));
      for ( k = 0; k < 5; ++k )
        v14 = v14 + (float)(*((float *)&v24 + k) * CPlayer::s_fPartGravity[k]);
      v14 = v14 + *(float *)&v30[5].m_strCode[44];
      v8 = v14;
      _effect_parameter::GetEff_Rate(&v40->m_EP, 17);
      v9 = v14 * v8;
      v14 = v9;
    }
    else
    {
      v32 = &v40->m_Param.m_dbEquip.m_pStorageList[v40->m_nLastBeatenPart].m_bLoad;
      if ( *v32 && CPlayer::GetEffectEquipCode(v40, 1, v40->m_nLastBeatenPart) == 1 )
      {
        v33 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v40->m_nLastBeatenPart, *(_WORD *)(v32 + 3));
        v9 = *(float *)&v33[5].m_strCode[44];
        v14 = *(float *)&v33[5].m_strCode[44];
      }
      else
      {
        v34 = CRecordData::GetRecord(
                (CRecordData *)&unk_1799C6AA0 + v40->m_nLastBeatenPart,
                v40->m_Param.m_dbChar.m_byDftPart[v40->m_nLastBeatenPart]);
        v9 = *(float *)&v34[5].m_strCode[44];
        v14 = *(float *)&v34[5].m_strCode[44];
      }
    }
    if ( v42 )
      *v42 = v40->m_nLastBeatenPart;
    _effect_parameter::GetEff_Rate(&v40->m_EP, 33);
    v10 = v14 * v9;
    v14 = v10;
    if ( CPlayer::IsSiegeMode(v40) )
    {
      _effect_parameter::GetEff_Rate(&v40->m_EP, 24);
      v14 = v14 * v10;
    }
  }
  if ( !v40->m_bInGuildBattle )
  {
    dwSerial = CPlayerDB::GetCharSerial(&v40->m_Param);
    v38 = CPlayerDB::GetRaceCode(&v40->m_Param);
    v11 = CPvpUserAndGuildRankingSystem::Instance();
    v35 = CPvpUserAndGuildRankingSystem::GetBossType(v11, v38, dwSerial);
    v39 = v35;
    if ( !v35 )
    {
      v14 = v14 * 1.3;
      goto LABEL_67;
    }
    if ( v39 != 1 )
    {
      if ( v39 == 3 )
        goto LABEL_66;
      if ( v39 != 5 )
      {
        if ( v39 != 7 )
          goto LABEL_67;
LABEL_66:
        v14 = v14 * 1.2;
        goto LABEL_67;
      }
    }
    v14 = v14 * 1.5;
  }
LABEL_67:
  if ( CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == v40->m_dwObjSerial || CPlayer::IsLastAttBuff(v40) )
    v14 = v14 * 1.3;
  if ( !CPlayer::IsRidingUnit(v40) )
  {
    if ( v40->m_fTalik_DefencePoint > 0.0 )
    {
      CPlayer::CalcDPRate(v40);
      v36 = v40->m_fTalik_DefencePoint * (float)(1.0 - 0.0);
      v12 = v36;
      _effect_parameter::GetEff_Rate(&v40->m_EP, 6);
      v14 = v14 * (float)(v12 - v36);
    }
    else
    {
      _effect_parameter::GetEff_Rate(&v40->m_EP, 6);
      v14 = v14 * 0.0;
    }
  }
  return (unsigned int)(signed int)ffloor(v14);
}
