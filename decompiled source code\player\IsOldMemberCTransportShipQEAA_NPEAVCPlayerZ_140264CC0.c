/*
 * Function: ?IsOldMember@CTransportShip@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140264CC0
 */

char __fastcall CTransportShip::IsOldMember(CTransportShip *this, CPlayer *pMember)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CTransportShip::__mgr_member *v7; // [sp+28h] [bp-10h]@7
  CTransportShip *v8; // [sp+40h] [bp+8h]@1
  CPlayer *v9; // [sp+48h] [bp+10h]@1

  v9 = pMember;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v7 = &v8->m_OldMember[j];
    if ( CTransportShip::__mgr_member::is_fill(v7) && v7->pPtr == v9 && v7->dwSerial == v9->m_dwObjSerial )
      return 1;
  }
  return 0;
}
