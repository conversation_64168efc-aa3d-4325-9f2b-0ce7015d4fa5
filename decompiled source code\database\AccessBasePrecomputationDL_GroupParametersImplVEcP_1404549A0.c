/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParametersImpl@V?$EcPrecomputation@VECP@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@2@V?$DL_GroupParameters@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x1404549A0
 */

CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>>::AccessBasePrecomputation(CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::ECP>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>,CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> > *this)
{
  return &this->m_gpc;
}
