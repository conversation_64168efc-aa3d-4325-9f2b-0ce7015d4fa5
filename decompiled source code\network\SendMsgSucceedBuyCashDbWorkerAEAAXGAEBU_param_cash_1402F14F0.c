/*
 * Function: ?SendMsgSucceedBuy@CashDbWorker@@AEAAXGAEBU_param_cash_update@@@Z
 * Address: 0x1402F14F0
 */

void __fastcall CashDbWorker::SendMsgSucceedBuy(CashDbWorker *this, unsigned __int16 wSock, _param_cash_update *sheet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@7
  __int64 v6; // [sp+0h] [bp-3A8h]@1
  _result_csi_buy_zocl v7; // [sp+40h] [bp-368h]@4
  int j; // [sp+374h] [bp-34h]@4
  char pbyType; // [sp+384h] [bp-24h]@7
  char v10; // [sp+385h] [bp-23h]@7
  unsigned __int16 v11; // [sp+3B8h] [bp+10h]@1
  _param_cash_update *v12; // [sp+3C0h] [bp+18h]@1

  v12 = sheet;
  v11 = wSock;
  v3 = &v6;
  for ( i = 232i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _result_csi_buy_zocl::_result_csi_buy_zocl(&v7);
  v7.nNum = v12->in_nNum10;
  v7.nCashAmount = v12->out_nCashAmount;
  for ( j = 0; j < v12->in_nNum10; ++j )
  {
    v7.item[j].byTblCode = v12->in_item[(signed __int64)j].in_byTblCode;
    v7.item[j].wItemIdx = v12->in_item[(signed __int64)j].in_wItemIdx;
    v7.item[j].dwDur = v12->in_item[(signed __int64)j].in_byOverlapNum;
    v7.item[j].dwUp = (unsigned __int8)GetDefItemUpgSocketNum(
                                         v12->in_item[(signed __int64)j].in_byTblCode,
                                         v12->in_item[(signed __int64)j].in_wItemIdx);
    v7.item[j].dwItemSerial = v12->in_item[(signed __int64)j].out_wItemSerial;
  }
  pbyType = 57;
  v10 = 4;
  v5 = _result_csi_buy_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11, &pbyType, (char *)&v7, v5);
}
