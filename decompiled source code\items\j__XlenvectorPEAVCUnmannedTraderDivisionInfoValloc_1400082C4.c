/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@KAXXZ
 * Address: 0x1400082C4
 */

void __fastcall __noreturn std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_<PERSON>len(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this)
{
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_<PERSON><PERSON>(this);
}
