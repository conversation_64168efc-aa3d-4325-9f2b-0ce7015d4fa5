/*
 * Function: ?ChannelMessageSeriesEnd@OutputProxy@CryptoPP@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x1405FF170
 */

char __fastcall CryptoPP::OutputProxy::ChannelMessageSeriesEnd(__int64 a1, __int64 a2, unsigned int a3, unsigned __int8 a4)
{
  __int64 v4; // rax@2
  char v6; // [sp+28h] [bp-10h]@2
  __int64 v7; // [sp+48h] [bp+10h]@1
  unsigned int v8; // [sp+50h] [bp+18h]@1
  unsigned __int8 v9; // [sp+58h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  if ( *(_BYTE *)(a1 + 32) )
  {
    LODWORD(v4) = (*(int (__fastcall **)(_QWORD))(**(_QWORD **)(a1 + 24) + 328i64))(*(_QWORD *)(a1 + 24));
    v6 = (*(int (__fastcall **)(__int64, __int64, _QWORD, _QWORD))(*(_QWORD *)v4 + 296i64))(v4, v7, v8, v9);
  }
  else
  {
    v6 = 0;
  }
  return v6;
}
