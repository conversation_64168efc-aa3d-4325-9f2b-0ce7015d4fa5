/*
 * Function: j_?Load@CGuildBattleSchedule@GUILD_BATTLE@@QEAA_N_NKE_JG@Z
 * Address: 0x14000A713
 */

bool __fastcall GUILD_BATTLE::CGuildBattleSchedule::Load(GUILD_BATTLE::CGuildBattleSchedule *this, bool bToday, unsigned int dwScheduleID, char ucState, __int64 tTime, unsigned __int16 wTumeMin)
{
  return GUILD_BATTLE::CGuildBattleSchedule::Load(this, bToday, dwScheduleID, ucState, tTime, wTumeMin);
}
