/*
 * Function: ?BillingExpirePersonal@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C3BC0
 */

char __fastcall CNetworkEX::BillingExpirePersonal(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CBillingManager *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *szID; // [sp+20h] [bp-18h]@4

  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szID = pBuf;
  v5 = CTSingleton<CBillingManager>::Instance();
  CBillingManager::Expire_Personal(v5, szID);
  return 1;
}
