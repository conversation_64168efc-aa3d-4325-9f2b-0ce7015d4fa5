/*
 * Function: ?FixWeapon@_WEAPON_PARAM@@QEAAXPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14007BA30
 */

void __fastcall _WEAPON_PARAM::FixWeapon(_WEAPON_PARAM *this, _STORAGE_LIST::_db_con *pWeapon)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  _base_fld *v5; // [sp+20h] [bp-18h]@7
  _WEAPON_PARAM *v6; // [sp+40h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItem; // [sp+48h] [bp+10h]@1

  pItem = pWeapon;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pWeapon )
  {
    if ( pWeapon->m_byTableCode == 6 )
    {
      v5 = CRecordData::GetRecord(_WEAPON_PARAM::s_pWeaponData, pWeapon->m_wItemIndex);
      if ( *(_DWORD *)&v5[6].m_strCode[8] == 10 )
      {
        _WEAPON_PARAM::Init(v6);
      }
      else
      {
        v6->byAttTolType = _WEAPON_PARAM::GetWeaponTolType(v6, pItem);
        v6->byWpClass = GetWeaponClass(pItem->m_wItemIndex);
        v6->nGaMaxAF = (signed int)ffloor(*(float *)&v5[10].m_strCode[0]);
        v6->nGaMinAF = (signed int)ffloor(*(float *)&v5[9].m_strCode[60]);
        v6->nMaMaxAF = (signed int)ffloor(*(float *)&v5[10].m_strCode[24]);
        v6->nMaMinAF = (signed int)ffloor(*(float *)&v5[10].m_strCode[16]);
        v6->byGaMinSel = v5[9].m_strCode[56];
        v6->byGaMaxSel = v5[10].m_dwIndex;
        v6->byMaMinSel = v5[10].m_strCode[12];
        v6->byMaMaxSel = v5[10].m_strCode[20];
        v6->byWpType = v5[6].m_strCode[8];
        v6->wGaAttRange = (signed int)ffloor(*(float *)&v5[9].m_strCode[48] + 40.0);
        v6->nActiveType = *(_DWORD *)&v5[3].m_strCode[4];
        v6->nActiveEffLvl = *(_DWORD *)&v5[4].m_strCode[4];
        v6->nActiveProb = (signed int)ffloor(*(float *)&v5[4].m_strCode[8] * 100.0);
        strncpy(v6->strActiveCode_key, &v5[3].m_strCode[8], 0x40ui64);
        strncpy(v6->strEffBulletType, &v5[5].m_strCode[12], 0x40ui64);
        v6->pFixWp = pItem;
        v6->pFixUnit = 0i64;
      }
    }
  }
  else
  {
    _WEAPON_PARAM::Init(v6);
  }
}
