/*
 * Function: ?SendMsg_ThrowSkillResult@CPlayer@@QEAAXEPEAU_CHRID@@E@Z
 * Address: 0x1400DFCA0
 */

void __fastcall CPlayer::SendMsg_ThrowSkillResult(CPlayer *this, char byErrCode, _CHRID *pidDst, char bySkillIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-2C8h]@1
  _throw_skill_result_one_zocl Dst; // [sp+40h] [bp-288h]@4
  int j; // [sp+144h] [bp-184h]@4
  char pbyType; // [sp+154h] [bp-174h]@7
  char v10; // [sp+155h] [bp-173h]@7
  _throw_skill_result_other_zocl v11; // [sp+180h] [bp-148h]@8
  char v12; // [sp+2A4h] [bp-24h]@8
  char v13; // [sp+2A5h] [bp-23h]@8
  CPlayer *v14; // [sp+2D0h] [bp+8h]@1
  char v15; // [sp+2D8h] [bp+10h]@1
  _CHRID *Src; // [sp+2E0h] [bp+18h]@1
  char v17; // [sp+2E8h] [bp+20h]@1

  v17 = bySkillIndex;
  Src = pidDst;
  v15 = byErrCode;
  v14 = this;
  v4 = &v6;
  for ( i = 176i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  _throw_skill_result_one_zocl::_throw_skill_result_one_zocl(&Dst);
  Dst.byErrCode = v15;
  memcpy_0(&Dst.idDster, Src, 7ui64);
  Dst.byEffectedNum = g_tmpEffectedNum;
  for ( j = 0; j < g_tmpEffectedNum; ++j )
  {
    Dst.list[j].byObjID = *(_BYTE *)(*((_QWORD *)&g_tmpEffectedList + 2 * j) + 17i64);
    Dst.list[j].dwObjSerial = *(_DWORD *)(*((_QWORD *)&g_tmpEffectedList + 2 * j) + 20i64);
    Dst.list[j].byRetCode = *((_BYTE *)&g_tmpEffectedList + 16 * j + 8);
    Dst.list[j].wEffectValue = *((_WORD *)&g_tmpEffectedList + 8 * j + 5);
  }
  pbyType = 17;
  v10 = 100;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &Dst.byErrCode, 0xFAu);
  if ( !v15 )
  {
    _throw_skill_result_other_zocl::_throw_skill_result_other_zocl(&v11);
    v11.byRetCode = 0;
    v11.idPerformer.byID = v14->m_ObjID.m_byID;
    v11.idPerformer.wIndex = v14->m_ObjID.m_wIndex;
    v11.idPerformer.dwSerial = v14->m_dwObjSerial;
    v11.bySkillIndex = v17;
    memcpy_0(&v11.idDster, Src, 7ui64);
    v11.byEffectedNum = g_tmpEffectedNum;
    memcpy_0(v11.list, Dst.list, 8i64 * g_tmpEffectedNum);
    v12 = 17;
    v13 = 101;
    CGameObject::CircleReport((CGameObject *)&v14->vfptr, &v12, &v11.byRetCode, 258, 0);
  }
}
