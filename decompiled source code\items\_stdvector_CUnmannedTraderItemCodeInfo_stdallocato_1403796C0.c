/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_n_::_1_::dtor$1
 * Address: 0x1403796C0
 */

void __fastcall std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_n_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CUnmannedTraderItemCodeInfo::~CUnmannedTraderItemCodeInfo((CUnmannedTraderItemCodeInfo *)(a2 + 48));
}
