/*
 * Function: _dynamic_initializer_for__CGuild::s_MgrHistory__
 * Address: 0x1406DF720
 */

__int64 dynamic_initializer_for__CGuild::s_MgrHistory__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CMgrGuildHistory::CMgrGuildHistory(&CGuild::s_MgrHistory);
  return atexit(dynamic_atexit_destructor_for__CGuild::s_MgrHistory__);
}
