/*
 * Function: ?DE_Potion_Exp_Increase_Percentage@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017EF70
 */

char __usercall DE_Potion_Exp_Increase_Percentage@<al>(CCharacter *pActChar@<rcx>, CCharacter *pTargetChar@<rdx>, float fEffectValue@<xmm2>, char *byRet@<r9>, double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  cStaticMember_Player *v8; // rax@10
  __int64 v9; // [sp+0h] [bp-48h]@1
  CPlayer *v10; // [sp+20h] [bp-28h]@10
  int lv; // [sp+28h] [bp-20h]@10
  double v12; // [sp+30h] [bp-18h]@10
  double v13; // [sp+38h] [bp-10h]@10
  CPlayer *v14; // [sp+50h] [bp+8h]@1

  v14 = (CPlayer *)pActChar;
  v5 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( v14 )
  {
    if ( v14->m_ObjID.m_byID )
    {
      result = 0;
    }
    else if ( (CCharacter *)v14 == pTargetChar )
    {
      v10 = v14;
      lv = CPlayerDB::GetLevel(&v14->m_Param);
      v8 = cStaticMember_Player::Instance();
      cStaticMember_Player::GetLimitExp(v8, lv);
      v12 = a5;
      v13 = a5 * (float)(fEffectValue / 100.0);
      CPlayer::AlterExp_Potion(v10, v13);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
