/*
 * Function: ?LeaveGuild@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXK_NAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E1460
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::LeaveGuild(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial, bool bInGuildBattle, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-58h]@1
  int v7; // [sp+20h] [bp-38h]@4
  char byType; // [sp+34h] [bp-24h]@6
  char v9; // [sp+35h] [bp-23h]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v10; // [sp+60h] [bp+8h]@1
  bool v11; // [sp+70h] [bp+18h]@1

  v11 = bInGuildBattle;
  v10 = this;
  v4 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(v10, dwSerial);
  if ( v7 >= 0 )
  {
    if ( v11 )
    {
      GUILD_BATTLE::CNormalGuildBattleGuildMember::ReturnBindPos(&v10->m_kMember[v7]);
      byType = 27;
      v9 = 91;
      GUILD_BATTLE::CNormalGuildBattleGuildMember::Send(&v10->m_kMember[v7], &byType, 0i64, 0);
    }
    GUILD_BATTLE::CNormalGuildBattleGuildMember::CleanUpBattle(&v10->m_kMember[v7]);
    if ( v10->m_dwCurJoinMember >= 1 )
      --v10->m_dwCurJoinMember;
  }
}
