/*
 * Function: ?GetLocalHour@CRFNewDatabase@@IEAAEXZ
 * Address: 0x140487000
 */

char __fastcall CRFNewDatabase::GetLocalHour(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  _SYSTEMTIME SystemTime; // [sp+28h] [bp-30h]@4

  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  GetLocalTime(&SystemTime);
  return SystemTime.wHour;
}
