/*
 * Function: ?ExtractStringToTime@CPlayer@@QEAAXKPEAU_SYSTEMTIME@@@Z
 * Address: 0x140069290
 */

void __fastcall CPlayer::ExtractStringToTime(CPlayer *this, unsigned int dwTemp, _SYSTEMTIME *tm)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char Dest; // [sp+28h] [bp-60h]@4
  char v7; // [sp+29h] [bp-5Fh]@4
  char Src; // [sp+2Ch] [bp-5Ch]@8
  char v9; // [sp+2Eh] [bp-5Ah]@8
  char v10; // [sp+30h] [bp-58h]@8
  char Dst; // [sp+54h] [bp-34h]@8
  char v12; // [sp+55h] [bp-33h]@8
  unsigned __int64 v13; // [sp+70h] [bp-18h]@4
  unsigned int v14; // [sp+98h] [bp+10h]@1
  _SYSTEMTIME *v15; // [sp+A0h] [bp+18h]@1

  v15 = tm;
  v14 = dwTemp;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v7, 0, 9ui64);
  sprintf(&Dest, "%d", dwTemp);
  if ( &v5 != (__int64 *)-40 && v14 && strlen_0(&Dest) >= 9 )
  {
    Dst = 0;
    memset(&v12, 0, 3ui64);
    memcpy_0(&Dst, &Dest, 4ui64);
    v15->wYear = atoi(&Dst);
    memset_0(&Dst, 0, 4ui64);
    memcpy_0(&Dst, &Src, 2ui64);
    v15->wMonth = atoi(&Dst);
    memset_0(&Dst, 0, 4ui64);
    memcpy_0(&Dst, &v9, 2ui64);
    v15->wDay = atoi(&Dst);
    memset_0(&Dst, 0, 4ui64);
    memcpy_0(&Dst, &v10, 1ui64);
    v15->wHour = atoi(&Dst);
    v15->wMinute = 0;
    v15->wSecond = 0;
  }
}
