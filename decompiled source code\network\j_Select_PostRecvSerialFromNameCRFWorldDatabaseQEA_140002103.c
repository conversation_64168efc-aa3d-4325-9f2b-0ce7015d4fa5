/*
 * Function: j_?Select_PostRecvSerialFromName@CRFWorldDatabase@@QEAAEPEADPEAK11@Z
 * Address: 0x140002103
 */

char __fastcall CRFWorldDatabase::Select_PostRecvSerialFromName(CRFWorldDatabase *this, char *wszRecvName, unsigned int *pdwOutSerial, unsigned int *pdwAccSerial, unsigned int *pdwRace)
{
  return CRFWorldDatabase::Select_PostRecvSerialFromName(this, wszRecvName, pdwOutSerial, pdwAccSerial, pdwRace);
}
