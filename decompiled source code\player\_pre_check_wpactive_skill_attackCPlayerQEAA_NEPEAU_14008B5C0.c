/*
 * Function: ?_pre_check_wpactive_skill_attack@CPlayer@@QEAA_NEPEAU_skill_fld@@GPEAPEAU_db_con@_STORAGE_LIST@@PEAPEAU_BulletItem_fld@@@Z
 * Address: 0x14008B5C0
 */

char __fastcall CPlayer::_pre_check_wpactive_skill_attack(CPlayer *this, char byEffectCode, _skill_fld *pSkillFld, unsigned __int16 wBulletSerial, _STORAGE_LIST::_db_con **ppBulletProp, _BulletItem_fld **ppfldBullet)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-38h]@1
  _STORAGE_LIST::_db_con *v10; // [sp+20h] [bp-18h]@4
  _BulletItem_fld *v11; // [sp+28h] [bp-10h]@4
  CPlayer *v12; // [sp+40h] [bp+8h]@1
  char v13; // [sp+48h] [bp+10h]@1
  _skill_fld *v14; // [sp+50h] [bp+18h]@1
  unsigned __int16 v15; // [sp+58h] [bp+20h]@1

  v15 = wBulletSerial;
  v14 = pSkillFld;
  v13 = byEffectCode;
  v12 = this;
  v6 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = 0i64;
  v11 = 0i64;
  if ( CPlayer::IsSiegeMode(v12) )
    return 0;
  if ( CPlayer::IsRidingUnit(v12) )
    return 0;
  if ( v15 != 0xFFFF )
  {
    v10 = CPlayer::IsBulletValidity(v12, v15);
    if ( !v10 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v12, 2, v15, 0);
      return 0;
    }
    v11 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v10->m_wItemIndex);
    if ( v10->m_bLock )
      return 0;
  }
  if ( (v12->m_pmWpn.byWpType == 5
     || v12->m_pmWpn.byWpType == 6
     || v12->m_pmWpn.byWpType == 7
     || v12->m_pmWpn.byWpType == 11)
    && !v11 )
  {
    return 0;
  }
  if ( v13 )
  {
    if ( v13 == 3 && strcmp_0(v11->m_strEffectIndex, v14->m_strCode) )
      return 0;
  }
  else if ( v14->m_nMastIndex > 8u )
  {
    return 0;
  }
  *ppBulletProp = v10;
  *ppfldBullet = v11;
  return 1;
}
