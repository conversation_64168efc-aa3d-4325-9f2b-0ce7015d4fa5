#pragma once

/**
 * @file CDatabaseConfiguration.h
 * @brief Modern C++20 Database Configuration System
 * 
 * This file provides comprehensive configuration management for database
 * connections, ODBC settings, and environment-specific parameters.
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <optional>
#include <variant>

namespace NexusProtection {
namespace Database {

/**
 * @brief Configuration value types
 */
using ConfigValue = std::variant<std::string, int32_t, uint32_t, bool, double>;

/**
 * @brief Environment types
 */
enum class Environment : uint8_t {
    Development = 0,
    Testing,
    Staging,
    Production
};

/**
 * @brief Database types
 */
enum class DatabaseType : uint8_t {
    SQLServer = 0,
    MySQL,
    PostgreSQL,
    Oracle,
    SQLite
};

/**
 * @brief Configuration source types
 */
enum class ConfigSource : uint8_t {
    File = 0,
    Registry,
    Environment,
    CommandLine,
    Database,
    Memory
};

/**
 * @brief Configuration validation result
 */
struct ConfigValidationResult {
    bool isValid{true};
    std::vector<std::string> errors;
    std::vector<std::string> warnings;
    
    void AddError(const std::string& error) {
        errors.push_back(error);
        isValid = false;
    }
    
    void AddWarning(const std::string& warning) {
        warnings.push_back(warning);
    }
    
    bool HasErrors() const { return !errors.empty(); }
    bool HasWarnings() const { return !warnings.empty(); }
};

/**
 * @brief Database connection configuration
 */
struct DatabaseConnectionConfig {
    std::string name;
    DatabaseType type{DatabaseType::SQLServer};
    std::string odbcName;
    std::string serverAddress;
    uint16_t port{1433};
    std::string databaseName;
    std::string username;
    std::string password;
    std::string connectionString;
    
    // Connection settings
    std::chrono::seconds connectionTimeout{30};
    std::chrono::seconds commandTimeout{60};
    std::chrono::seconds loginTimeout{15};
    bool enableConnectionPooling{true};
    uint32_t maxPoolSize{100};
    uint32_t minPoolSize{5};
    
    // SSL/TLS settings
    bool enableSSL{false};
    std::string sslCertificatePath;
    std::string sslKeyPath;
    std::string sslCACertificatePath;
    bool sslVerifyServerCert{true};
    
    // Advanced settings
    bool enableAutoReconnect{true};
    uint32_t maxRetryAttempts{3};
    std::chrono::seconds retryDelay{5};
    bool enableQueryLogging{false};
    bool enablePerformanceCounters{true};
    
    DatabaseConnectionConfig() = default;
    explicit DatabaseConnectionConfig(const std::string& configName) : name(configName) {}
    
    bool IsValid() const;
    std::string BuildConnectionString() const;
    ConfigValidationResult Validate() const;
};

/**
 * @brief ODBC configuration
 */
struct ODBCConfig {
    std::string driverName;
    std::string driverVersion;
    std::string driverPath;
    std::unordered_map<std::string, std::string> attributes;
    
    // Common ODBC attributes
    std::string server;
    std::string database;
    std::string uid;
    std::string pwd;
    bool trustedConnection{false};
    std::string applicationIntent;
    std::string multiSubnetFailover;
    
    ODBCConfig() = default;
    
    std::string BuildODBCString() const;
    ConfigValidationResult Validate() const;
};

/**
 * @brief Environment-specific configuration
 */
struct EnvironmentConfig {
    Environment environment{Environment::Development};
    std::string environmentName;
    std::unordered_map<std::string, DatabaseConnectionConfig> databases;
    std::unordered_map<std::string, ConfigValue> settings;
    
    // Environment-specific paths
    std::string logPath;
    std::string dataPath;
    std::string configPath;
    std::string tempPath;
    
    // Environment-specific flags
    bool enableDebugLogging{false};
    bool enablePerformanceMonitoring{false};
    bool enableDetailedErrorReporting{false};
    
    EnvironmentConfig() = default;
    explicit EnvironmentConfig(Environment env) : environment(env) {}
    
    ConfigValidationResult Validate() const;
};

/**
 * @brief Configuration change notification
 */
struct ConfigChangeNotification {
    std::string section;
    std::string key;
    ConfigValue oldValue;
    ConfigValue newValue;
    std::chrono::system_clock::time_point timestamp;
    
    ConfigChangeNotification(const std::string& sec, const std::string& k, 
                           const ConfigValue& oldVal, const ConfigValue& newVal)
        : section(sec), key(k), oldValue(oldVal), newValue(newVal)
        , timestamp(std::chrono::system_clock::now()) {}
};

/**
 * @brief Configuration change callback
 */
using ConfigChangeCallback = std::function<void(const ConfigChangeNotification&)>;

/**
 * @brief Modern C++20 Database Configuration class
 * 
 * This class provides comprehensive configuration management for database
 * connections, ODBC settings, and environment-specific parameters.
 */
class CDatabaseConfiguration {
public:
    // Constructor and Destructor
    CDatabaseConfiguration();
    explicit CDatabaseConfiguration(Environment environment);
    virtual ~CDatabaseConfiguration();

    // Disable copy constructor and assignment operator
    CDatabaseConfiguration(const CDatabaseConfiguration&) = delete;
    CDatabaseConfiguration& operator=(const CDatabaseConfiguration&) = delete;

    // Enable move constructor and assignment operator
    CDatabaseConfiguration(CDatabaseConfiguration&&) noexcept = default;
    CDatabaseConfiguration& operator=(CDatabaseConfiguration&&) noexcept = default;

    /**
     * @brief Load configuration from file
     * 
     * @param filePath Path to configuration file
     * @return true if loaded successfully, false otherwise
     */
    bool LoadFromFile(const std::string& filePath);

    /**
     * @brief Load configuration from registry
     * 
     * @param registryPath Registry path
     * @return true if loaded successfully, false otherwise
     */
    bool LoadFromRegistry(const std::string& registryPath);

    /**
     * @brief Load configuration from environment variables
     * 
     * @param prefix Environment variable prefix
     * @return true if loaded successfully, false otherwise
     */
    bool LoadFromEnvironment(const std::string& prefix = "NEXUS_DB_");

    /**
     * @brief Save configuration to file
     * 
     * @param filePath Path to save configuration
     * @return true if saved successfully, false otherwise
     */
    bool SaveToFile(const std::string& filePath) const;

    /**
     * @brief Validate entire configuration
     * 
     * @return Validation result
     */
    ConfigValidationResult ValidateConfiguration() const;

    // Database connection management
    bool AddDatabaseConnection(const std::string& name, const DatabaseConnectionConfig& config);
    bool RemoveDatabaseConnection(const std::string& name);
    std::optional<DatabaseConnectionConfig> GetDatabaseConnection(const std::string& name) const;
    std::vector<std::string> GetDatabaseConnectionNames() const;

    // ODBC configuration management
    bool SetODBCConfig(const ODBCConfig& config);
    std::optional<ODBCConfig> GetODBCConfig() const;

    // Environment configuration
    void SetEnvironment(Environment environment);
    Environment GetEnvironment() const { return m_currentEnvironment; }
    std::string GetEnvironmentName() const;

    // Generic configuration values
    template<typename T>
    bool SetValue(const std::string& section, const std::string& key, const T& value);
    
    template<typename T>
    std::optional<T> GetValue(const std::string& section, const std::string& key) const;
    
    bool HasValue(const std::string& section, const std::string& key) const;
    bool RemoveValue(const std::string& section, const std::string& key);

    // Configuration sections
    std::vector<std::string> GetSections() const;
    std::vector<std::string> GetKeys(const std::string& section) const;
    bool RemoveSection(const std::string& section);

    // Change notifications
    void RegisterChangeCallback(const std::string& section, ConfigChangeCallback callback);
    void UnregisterChangeCallback(const std::string& section);

    // Utility methods
    std::string GetConfigurationSummary() const;
    void ResetToDefaults();
    bool MergeConfiguration(const CDatabaseConfiguration& other);

    // Singleton access
    static CDatabaseConfiguration& Instance();
    static void SetInstance(std::unique_ptr<CDatabaseConfiguration> instance);

protected:
    // Core configuration data
    Environment m_currentEnvironment{Environment::Development};
    std::unordered_map<std::string, EnvironmentConfig> m_environmentConfigs;
    std::unordered_map<std::string, std::unordered_map<std::string, ConfigValue>> m_configSections;
    std::optional<ODBCConfig> m_odbcConfig;
    
    // Change notification
    std::unordered_map<std::string, ConfigChangeCallback> m_changeCallbacks;
    
    // Synchronization
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    
    // Singleton instance
    static std::unique_ptr<CDatabaseConfiguration> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Initialize default configuration
     */
    void InitializeDefaults();

    /**
     * @brief Load configuration from JSON file
     * 
     * @param filePath Path to JSON file
     * @return true if loaded successfully, false otherwise
     */
    bool LoadFromJSON(const std::string& filePath);

    /**
     * @brief Load configuration from INI file
     * 
     * @param filePath Path to INI file
     * @return true if loaded successfully, false otherwise
     */
    bool LoadFromINI(const std::string& filePath);

    /**
     * @brief Save configuration to JSON file
     * 
     * @param filePath Path to save JSON file
     * @return true if saved successfully, false otherwise
     */
    bool SaveToJSON(const std::string& filePath) const;

    /**
     * @brief Notify configuration change
     * 
     * @param section Configuration section
     * @param key Configuration key
     * @param oldValue Old value
     * @param newValue New value
     */
    void NotifyConfigChange(const std::string& section, const std::string& key,
                          const ConfigValue& oldValue, const ConfigValue& newValue);

    /**
     * @brief Get current environment configuration
     * 
     * @return Reference to current environment configuration
     */
    EnvironmentConfig& GetCurrentEnvironmentConfig();
    const EnvironmentConfig& GetCurrentEnvironmentConfig() const;

    /**
     * @brief Convert ConfigValue to specific type
     * 
     * @tparam T Target type
     * @param value ConfigValue to convert
     * @return Optional converted value
     */
    template<typename T>
    std::optional<T> ConvertConfigValue(const ConfigValue& value) const;
};

/**
 * @brief Database Configuration Factory
 */
class CDatabaseConfigurationFactory {
public:
    /**
     * @brief Create configuration for environment
     * 
     * @param environment Target environment
     * @return Unique pointer to configuration
     */
    static std::unique_ptr<CDatabaseConfiguration> CreateForEnvironment(Environment environment);

    /**
     * @brief Create configuration from file
     * 
     * @param filePath Configuration file path
     * @return Unique pointer to configuration
     */
    static std::unique_ptr<CDatabaseConfiguration> CreateFromFile(const std::string& filePath);

    /**
     * @brief Create default configuration
     * 
     * @return Unique pointer to configuration
     */
    static std::unique_ptr<CDatabaseConfiguration> CreateDefault();
};

/**
 * @brief Configuration utility functions
 */
namespace ConfigUtils {
    std::string EnvironmentToString(Environment env);
    Environment StringToEnvironment(const std::string& envStr);
    std::string DatabaseTypeToString(DatabaseType type);
    DatabaseType StringToDatabaseType(const std::string& typeStr);
    std::string ConfigValueToString(const ConfigValue& value);
    ConfigValue StringToConfigValue(const std::string& str, const std::string& type);
}

} // namespace Database
} // namespace NexusProtection
