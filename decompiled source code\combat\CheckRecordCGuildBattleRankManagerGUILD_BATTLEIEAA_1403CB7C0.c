/*
 * Function: ?CheckRecord@CGuildBattleRankManager@GUILD_BATTLE@@IEAA_NK@Z
 * Address: 0x1403CB7C0
 */

bool __fastcall GUILD_BATTLE::CGuildBattleRankManager::CheckRecord(GUILD_BATTLE::CGuildBattleRankManager *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  unsigned int dwGuildSeriala; // [sp+38h] [bp+10h]@1

  dwGuildSeriala = dwGuildSerial;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CRFWorldDatabase::SelectGuildBattleRankRecord(pkDB, dwGuildSerial) )
    result = 1;
  else
    result = CRFWorldDatabase::InsertGuildBattleRankRecord(pkDB, dwGuildSeriala);
  return result;
}
