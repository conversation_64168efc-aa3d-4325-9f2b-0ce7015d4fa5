/*
 * Function: ?check_cash_discount_status@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F6C30
 */

void __fastcall CashItemRemoteStore::check_cash_discount_status(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  char v4; // [sp+20h] [bp-38h]@4
  __time32_t Time; // [sp+34h] [bp-24h]@7
  bool v6; // [sp+44h] [bp-14h]@11
  int v7; // [sp+48h] [bp-10h]@7
  CashItemRemoteStore *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = CashItemRemoteStore::get_cde_status(v8);
  v8->m_cde.m_ini.m_bCoEvent = 0;
  if ( v8->m_con_event.m_ini.m_byEventKind == 2 && v8->m_con_event.m_bConEvent )
    v8->m_cde.m_ini.m_bCoEvent = 1;
  _time32(&Time);
  v7 = (unsigned __int8)v4;
  switch ( v4 )
  {
    case 0:
      if ( v8->m_cde.m_ini.m_bUseCashDiscount && v8->m_cde.m_ini.m_bRepeat )
      {
        v6 = CashItemRemoteStore::ChangeDiscountEventTime(v8);
        if ( v8->m_cde.m_ini.m_NextEventTime[1] > Time )
        {
          if ( v6 )
            CashItemRemoteStore::set_cde_status(v8, 1);
          else
            CashItemRemoteStore::set_cde_status(v8, 5);
        }
        else
        {
          CashItemRemoteStore::set_cde_status(v8, 5);
        }
      }
      else
      {
        CashItemRemoteStore::set_cde_status(v8, 5);
      }
      break;
    case 1:
      if ( v8->m_cde.m_ini.m_cdeTime[2] > Time )
      {
        if ( Time >= v8->m_cde.m_ini.m_cdeTime[0] )
        {
          CashItemRemoteStore::set_cde_status(v8, 2);
          CashItemRemoteStore::inform_cashdiscount_status_all(v8, 2, &v8->m_cde.m_ini);
        }
      }
      else
      {
        CashItemRemoteStore::set_cde_status(v8, 5);
      }
      break;
    case 2:
      if ( v8->m_cde.m_ini.m_cdeTime[1] - Time <= v8->m_cde.m_cde_inform_before[0] )
      {
        CashItemRemoteStore::set_cde_status(v8, 3);
        CashItemRemoteStore::inform_cashdiscount_status_all(v8, 3, &v8->m_cde.m_ini);
      }
      break;
    case 3:
      if ( v8->m_cde.m_ini.m_cdeTime[1] - Time <= v8->m_cde.m_cde_inform_before[1] )
      {
        CashItemRemoteStore::set_cde_status(v8, 4);
        CashItemRemoteStore::inform_cashdiscount_status_all(v8, 4, &v8->m_cde.m_ini);
      }
      break;
    case 4:
      if ( Time >= v8->m_cde.m_ini.m_cdeTime[1] )
      {
        CashItemRemoteStore::set_cde_status(v8, 5);
        CashItemRemoteStore::inform_cashdiscount_status_all(v8, 5, &v8->m_cde.m_ini);
      }
      break;
    case 5:
      if ( v8->m_cde.m_ini.m_bUseCashDiscount )
      {
        if ( v8->m_cde.m_ini.m_bRepeat )
        {
          if ( v8->m_cde.m_ini.m_cdeTime[2] > Time )
          {
            if ( CashItemRemoteStore::SetNextDiscountEventTime(v8) )
              CashItemRemoteStore::set_cde_status(v8, 0);
            else
              CashItemRemoteStore::set_cde_status(v8, 7);
          }
          else
          {
            v8->m_cde.m_ini.m_bUseCashDiscount = 0;
            v8->m_cde.m_ini.m_bRepeat = 0;
            CashItemRemoteStore::set_cde_status(v8, 7);
          }
        }
        else
        {
          CashItemRemoteStore::set_cde_status(v8, 7);
        }
      }
      else
      {
        CashItemRemoteStore::set_cde_status(v8, 7);
      }
      break;
    default:
      return;
  }
}
