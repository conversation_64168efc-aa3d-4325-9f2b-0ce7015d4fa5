/*
 * Function: ?Change<PERSON><PERSON><PERSON>@CDarkHoleChannel@@QEAAXXZ
 * Address: 0x140268570
 */

void __usercall CDarkHoleChannel::ChangeMonster(CDarkHoleChannel *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v5; // ecx@14
  __int64 v6; // [sp-20h] [bp-28E8h]@1
  _dh_mission_setup *pMsSetup; // [sp+0h] [bp-28C8h]@4
  int v8; // [sp+8h] [bp-28C0h]@6
  __int64 v9[1280]; // [sp+20h] [bp-28A8h]@15
  int j; // [sp+2824h] [bp-A4h]@6
  char *v11; // [sp+2828h] [bp-A0h]@9
  int k; // [sp+2830h] [bp-98h]@16
  __change_monster *v13; // [sp+2838h] [bp-90h]@19
  _dh_mission_mgr::_if_change *v14; // [sp+2840h] [bp-88h]@20
  int l; // [sp+2848h] [bp-80h]@29
  CMonster *v16; // [sp+2850h] [bp-78h]@31
  _monster_create_setdata Dst; // [sp+2870h] [bp-58h]@31
  CExtDummy *v18; // [sp+28B8h] [bp-10h]@14
  CDarkHoleChannel *v19; // [sp+28D0h] [bp+8h]@1

  v19 = this;
  v2 = alloca(a2);
  v3 = &v6;
  for ( i = 2616i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pMsSetup = v19->m_MissionMgr.pCurMssionPtr;
  if ( pMsSetup && pMsSetup->nChangeMonsterNum > 0 )
  {
    v8 = 0;
    for ( j = 0; j < 30000; ++j )
    {
      v11 = (char *)g_Monster + 6424 * j;
      if ( v11[24] )
      {
        if ( (CMapData *)*((_QWORD *)v11 + 11) == v19->m_pQuestSetup->pUseMap
          && *((_WORD *)v11 + 52) == v19->m_wLayerIndex )
        {
          if ( !pMsSetup->pAreaDummy
            || (v5 = pMsSetup->pAreaDummy->m_wLineIndex,
                v18 = &v19->m_pQuestSetup->pUseMap->m_Dummy,
                CExtDummy::IsInBBox(v18, v5, (float *const )v11 + 10)) )
          {
            v9[v8++] = (__int64)v11;
          }
        }
      }
    }
    for ( k = 0; k < pMsSetup->nChangeMonsterNum; ++k )
    {
      v13 = pMsSetup->pChangeMonster[k];
      if ( rand() % 100 <= v13->nProb )
      {
        v14 = _dh_mission_mgr::GetMissionCont(&v19->m_MissionMgr, pMsSetup);
        if ( v14 )
        {
          _dh_quest_setup::SetRealBoss(v19->m_pQuestSetup, 0);
          if ( v13->pszIfMissionDescirptCode )
          {
            if ( !v14->pMissionPtr )
              v14->pMissionPtr = pMsSetup;
            v14->pszDespt = v13->pszIfMissionDescirptCode;
          }
          if ( v13->pszifCompleteMsg )
          {
            if ( !v14->pMissionPtr )
              v14->pMissionPtr = pMsSetup;
            v14->pszComMsg = v13->pszifCompleteMsg;
          }
        }
        for ( l = 0; l < v8; ++l )
        {
          v16 = (CMonster *)v9[l];
          _monster_create_setdata::_monster_create_setdata(&Dst);
          memcpy_0(Dst.m_fStartPos, v16->m_fCreatePos, 0xCui64);
          Dst.m_nLayerIndex = v16->m_wMapLayerIndex;
          Dst.m_pMap = v16->m_pCurMap;
          Dst.m_pRecordSet = (_base_fld *)&v13->pMonsterFldB->m_dwIndex;
          Dst.pActiveRec = v16->m_pActiveRec;
          Dst.bDungeon = v16->m_bDungeon;
          Dst.pDumPosition = v16->m_pDumPosition;
          Dst.pParent = 0i64;
          CMonster::Destroy(v16, 1, 0i64);
          CMonster::Create(v16, &Dst);
        }
      }
    }
  }
}
