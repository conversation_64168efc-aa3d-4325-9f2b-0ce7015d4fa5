/*
 * Function: ?dtor$1@?0??OnGetItemText@CMFCShellListCtrl@@UEAA?AV?$CStringT@DV?$StrTraitMFC@DV?$ChTraitsCRT@D@ATL@@@@@ATL@@HHPEAU_AFX_SHELLITEMINF<PERSON>@@@Z@4HA_0
 * Address: 0x14065AA80
 */

void __fastcall `CMFCShellListCtrl::OnGetItemText'::`1'::dtor$1(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>::~_Deque_iterator<unsigned int,std::allocator<unsigned int>,0>((std::_Ranit<unsigned int,__int64,unsigned int const *,unsigned int const &> *)(a2 + 1024));
}
