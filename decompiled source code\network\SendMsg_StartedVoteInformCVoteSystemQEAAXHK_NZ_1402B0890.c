/*
 * Function: ?SendMsg_StartedVoteInform@CVoteSystem@@QEAAXHK_N@Z
 * Address: 0x1402B0890
 */

void __fastcall CVoteSystem::SendMsg_StartedVoteInform(CVoteSystem *this, int n, unsigned int dwAvatorSerial, bool bPunish)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@11
  __int64 v7; // [sp+0h] [bp-68h]@1
  int j; // [sp+30h] [bp-38h]@8
  char pbyType; // [sp+44h] [bp-24h]@11
  char v10; // [sp+45h] [bp-23h]@11
  CVoteSystem *v11; // [sp+70h] [bp+8h]@1
  int dwClientIndex; // [sp+78h] [bp+10h]@1
  bool v13; // [sp+88h] [bp+20h]@1

  v13 = bPunish;
  dwClientIndex = n;
  v11 = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11->m_SendStarted.bActed = CNetIndexList::IsInList(&v11->m_listVote, dwAvatorSerial) || v13;
  for ( j = 0; j < 3; ++j )
    v11->m_SendStarted.wPoint[j] = v11->m_dwPoint[j];
  v11->m_SendStarted.bHurry = v11->m_bHurry;
  pbyType = 26;
  v10 = 33;
  v6 = _started_vote_inform_zocl::size(&v11->m_SendStarted);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, (char *)&v11->m_SendStarted, v6);
}
