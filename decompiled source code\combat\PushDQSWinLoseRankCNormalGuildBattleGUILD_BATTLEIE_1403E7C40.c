/*
 * Function: ?PushDQSWinLoseRank@CNormalGuildBattle@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403E7C40
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::PushDQSWinLoseRank(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@7
  __int64 v4; // [sp+0h] [bp-68h]@1
  _qry_case_updatewinloseguildbattlerank v5; // [sp+38h] [bp-30h]@7
  GUILD_BATTLE::CNormalGuildBattle *v6; // [sp+70h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_pkWin )
  {
    if ( v6->m_pkLose )
    {
      v5.byWinRace = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(v6->m_pkWin)->m_byRace;
      v5.dwWinGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v6->m_pkWin);
      v5.byLoseRace = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(v6->m_pkLose)->m_byRace;
      v5.dwLoseGuildSerial = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(v6->m_pkLose);
      v3 = _qry_case_updatewinloseguildbattlerank::size(&v5);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 31, &v5.byWinRace, v3);
    }
  }
}
