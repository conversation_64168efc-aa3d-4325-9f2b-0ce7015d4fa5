/*
 * Function: ?CleanUp@CMoveMapLimitInfoList@@AEAAXXZ
 * Address: 0x1403A6290
 */

void __fastcall CMoveMapLimitInfoList::CleanUp(CMoveMapLimitInfoList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-108h]@1
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v4; // [sp+20h] [bp-E8h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > _Right; // [sp+38h] [bp-D0h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v6; // [sp+68h] [bp-A0h]@4
  CMoveMapLimitInfo *v7; // [sp+88h] [bp-80h]@6
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > result; // [sp+90h] [bp-78h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > v9; // [sp+A8h] [bp-60h]@4
  CMoveMapLimitInfo *v10; // [sp+C0h] [bp-48h]@6
  CMoveMapLimitInfo *v11; // [sp+C8h] [bp-40h]@6
  __int64 v12; // [sp+D0h] [bp-38h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v13; // [sp+D8h] [bp-30h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *__that; // [sp+E0h] [bp-28h]@4
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v15; // [sp+E8h] [bp-20h]@4
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *v16; // [sp+F0h] [bp-18h]@4
  void *v17; // [sp+F8h] [bp-10h]@7
  CMoveMapLimitInfoList *v18; // [sp+110h] [bp+8h]@1

  v18 = this;
  v1 = &v3;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v12 = -2i64;
  v4 = (std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v18;
  v13 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::end(&v18->m_vecLimitInfo, &result);
  __that = (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v13;
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    &_Right,
    (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v13->_Mycont);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&result);
  v15 = std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::begin(v4, &v9);
  v16 = (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)v15;
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    &v6,
    (std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *)&v15->_Mycont);
  std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v9);
  while ( std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator!=(&v6, &_Right) )
  {
    v7 = *std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator*(&v6);
    v11 = v7;
    v10 = v7;
    if ( v7 )
      v17 = CMoveMapLimitInfo::`scalar deleting destructor'(v10, 1u);
    else
      v17 = 0i64;
    std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator++(&v6);
  }
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&v6);
  std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::~_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(&_Right);
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::clear(&v18->m_vecLimitInfo);
}
