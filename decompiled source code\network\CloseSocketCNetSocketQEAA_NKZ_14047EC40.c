/*
 * Function: ?CloseSocket@CNetSocket@@QEAA_NK@Z
 * Address: 0x14047EC40
 */

char __fastcall CNetSocket::CloseSocket(CNetSocket *this, unsigned int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNetSocket *v6; // [sp+30h] [bp+8h]@1
  unsigned int na; // [sp+38h] [bp+10h]@1

  na = n;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  shutdown(v6->m_Socket[n].m_Socket, 1);
  CNetSocket::EmptySocketBuffer(v6, na);
  --v6->m_TotalCount.m_dwAcceptNum;
  closesocket(v6->m_Socket[na].m_Socket);
  _socket::InitParam(&v6->m_Socket[na]);
  v6->m_Socket[na].m_dwLastCloseTime = timeGetTime();
  return 1;
}
