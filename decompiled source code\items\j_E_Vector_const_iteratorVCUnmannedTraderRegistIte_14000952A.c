/*
 * Function: j_??E?$_Vector_const_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x14000952A
 */

std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator++(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this)
{
  return std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator++(this);
}
