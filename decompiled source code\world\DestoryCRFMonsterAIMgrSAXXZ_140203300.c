/*
 * Function: ?Destory@CRFMonsterAIMgr@@SAXXZ
 * Address: 0x140203300
 */

void CRFMonsterAIMgr::Destory(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-48h]@1
  CRFMonsterAIMgr *v3; // [sp+20h] [bp-28h]@5
  struct CRFMonsterAIMgr *v4; // [sp+28h] [bp-20h]@5
  void *v5; // [sp+30h] [bp-18h]@6

  v0 = &v2;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  if ( CRFMonsterAIMgr::ms_Instance )
  {
    v4 = CRFMonsterAIMgr::ms_Instance;
    v3 = CRFMonsterAIMgr::ms_Instance;
    if ( CRFMonsterAIMgr::ms_Instance )
      v5 = CRFMonsterAIMgr::`scalar deleting destructor'(v3, 1u);
    else
      v5 = 0i64;
    CRFMonsterAIMgr::ms_Instance = 0i64;
  }
}
