/*
 * Function: ??D?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@QEBAAEBVCUnmannedTraderItemCodeInfo@@XZ
 * Address: 0x140378F20
 */

CUnmannedTraderItemCodeInfo *__fastcall std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator*(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this)
{
  return this->_Myptr;
}
