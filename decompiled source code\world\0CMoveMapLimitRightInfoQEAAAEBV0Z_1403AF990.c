/*
 * Function: ??0CMoveMapLimitRightInfo@@QEAA@AEBV0@@Z
 * Address: 0x1403AF990
 */

void __fastcall CMoveMapLimitRightInfo::CMoveMapLimitRightInfo(CMoveMapLimitRightInfo *this, CMoveMapLimitRightInfo *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    &v5->m_vecRight,
    &__that->m_vecRight);
}
