/*
 * Function: ?CompleteUpdateClear@CUnmannedTraderLazyCleaner@@QEAAXPEAD@Z
 * Address: 0x140392CF0
 */

void __fastcall CUnmannedTraderLazyCleaner::CompleteUpdateClear(CUnmannedTraderLazyCleaner *this, char *p)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char *v5; // [sp+30h] [bp-38h]@4
  char v6; // [sp+44h] [bp-24h]@10
  char v7; // [sp+45h] [bp-23h]@10
  char v8; // [sp+46h] [bp-22h]@10
  CUnmannedTraderLazyCleaner *v9; // [sp+70h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = p;
  if ( CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADER_LAZYCLEANER_UPDATE_MAX_RETRY_CNT > v9->m_uiRetryCnt
    && (*v5 || v5[1] || v5[2] || v5[3]) )
  {
    v6 = 0;
    v7 = 0;
    memset(&v8, 0, 2ui64);
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 67, &v6, 4);
    ++v9->m_uiRetryCnt;
    v9->m_bClearProcess = 1;
  }
  else
  {
    v9->m_bClearProcess = 0;
    v9->m_uiRetryCnt = 0;
  }
}
