/*
 * Function: ?SendMsg_AttackResult_Gen@CPlayer@@QEAAXPEAVCAttack@@G@Z
 * Address: 0x1400D42A0
 */

void __fastcall CPlayer::SendMsg_AttackResult_Gen(CPlayer *this, CAttack *pAt, unsigned __int16 wBulletIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-1C8h]@1
  _attack_gen_result_zocl v7; // [sp+40h] [bp-188h]@4
  int j; // [sp+194h] [bp-34h]@4
  char pbyType; // [sp+1A4h] [bp-24h]@7
  char v10; // [sp+1A5h] [bp-23h]@7
  CPlayer *v11; // [sp+1D0h] [bp+8h]@1
  CAttack *v12; // [sp+1D8h] [bp+10h]@1
  unsigned __int16 v13; // [sp+1E0h] [bp+18h]@1

  v13 = wBulletIndex;
  v12 = pAt;
  v11 = this;
  v3 = &v6;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _attack_gen_result_zocl::_attack_gen_result_zocl(&v7);
  v7.byAtterID = v11->m_ObjID.m_byID;
  v7.dwAtterSerial = v11->m_dwObjSerial;
  v7.byAttackPart = v12->m_pp->nPart;
  v7.bCritical = v12->m_bIsCrtAtt;
  v7.wBulletIndex = v13;
  v7.bWPActive = v12->m_bActiveSucc;
  v7.byListNum = v12->m_nDamagedObjNum;
  for ( j = 0; j < v12->m_nDamagedObjNum; ++j )
  {
    v7.DamList[j].byDstID = v12->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v7.DamList[j].dwDstSerial = v12->m_DamList[j].m_pChar->m_dwObjSerial;
    v7.DamList[j].wDamage = v12->m_DamList[j].m_nDamage;
    v7.DamList[j].bActive = v12->m_DamList[j].m_bActiveSucc;
    v7.DamList[j].wActiveDamage = v12->m_DamList[j].m_nActiveDamage;
  }
  pbyType = 5;
  v10 = 7;
  v5 = _attack_gen_result_zocl::size(&v7);
  CGameObject::CircleReport((CGameObject *)&v11->vfptr, &pbyType, &v7.byAtterID, v5, 1);
}
