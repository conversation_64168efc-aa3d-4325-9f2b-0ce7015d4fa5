/*
 * Function: ?GetItemStoreFromRecIndex@CMapItemStoreList@@QEAAPEAVCItemStore@@K@Z
 * Address: 0x14034C0A0
 */

CItemStore *__fastcall CMapItemStoreList::GetItemStoreFromRecIndex(CMapItemStoreList *this, unsigned int dwRecIndex)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  bool *v6; // [sp+8h] [bp-10h]@6
  CMapItemStoreList *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; j < v7->m_nItemStoreNum; ++j )
  {
    v6 = &v7->m_ItemStore[j].m_bLive;
    if ( **((_DWORD **)v6 + 4) == dwRecIndex )
      return (CItemStore *)v6;
  }
  return 0i64;
}
