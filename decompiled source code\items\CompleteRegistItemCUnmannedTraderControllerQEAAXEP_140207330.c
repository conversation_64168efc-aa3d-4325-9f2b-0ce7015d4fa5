/*
 * Function: ?CompleteRegistItem@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x140207330
 */

void __fastcall CUnmannedTraderController::CompleteRegistItem(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  char v7; // [sp+38h] [bp+10h]@1
  char *pLoadDataa; // [sp+40h] [bp+18h]@1

  pLoadDataa = pLoadData;
  v7 = byRet;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CUnmannedTraderUserInfoTable::Instance();
  CUnmannedTraderUserInfoTable::CompleteRegist(v5, v7, pLoadDataa);
}
