/*
 * Function: j_?SellComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@0KKKK_JPEAVCLogFile@@@Z
 * Address: 0x140004E5D
 */

char __fastcall CUnmannedTraderUserInfo::SellComplete(CUnmannedTraderUserInfo *this, CPlayer *pkSellPlayer, CPlayer *pkBuyer, unsigned int dwOriPrice, unsigned int dwRealPrice, unsigned int dwTax, unsigned int dwRegistSerial, __int64 tResultTime, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::SellComplete(
           this,
           pkSellPlayer,
           pkBuyer,
           dwOriPrice,
           dwRealPrice,
           dwTax,
           dwRegistSerial,
           tResultTime,
           pkLogger);
}
