#include "../Headers/MonsterGroup.h"
#include <algorithm>
#include <sstream>
#include <cstring>
#include <stdexcept>

namespace NexusProtection::World {

    // MonsterGroup implementation

    MonsterGroup::MonsterGroup() {
        Initialize();
    }

    MonsterGroup::MonsterGroup(const std::string& groupName, uint32_t subMonsterNum)
        : m_groupName(groupName)
        , m_subMonsterCount(subMonsterNum) {
        ValidateAndTruncate();
        m_isInitialized = true;
    }

    MonsterGroup::MonsterGroup(const MonsterGroup& other)
        : m_groupName(other.m_groupName)
        , m_subMonsterCount(other.m_subMonsterCount)
        , m_monsters(other.m_monsters)
        , m_groupBehavior(other.m_groupBehavior)
        , m_isActive(other.m_isActive)
        , m_isSpawned(other.m_isSpawned)
        , m_isInitialized(other.m_isInitialized)
        , m_eventHandlers(other.m_eventHandlers) {
    }

    MonsterGroup& MonsterGroup::operator=(const MonsterGroup& other) {
        if (this != &other) {
            m_groupName = other.m_groupName;
            m_subMonsterCount = other.m_subMonsterCount;
            m_monsters = other.m_monsters;
            m_groupBehavior = other.m_groupBehavior;
            m_isActive = other.m_isActive;
            m_isSpawned = other.m_isSpawned;
            m_isInitialized = other.m_isInitialized;
            m_eventHandlers = other.m_eventHandlers;
        }
        return *this;
    }

    MonsterGroup::MonsterGroup(MonsterGroup&& other) noexcept
        : m_groupName(std::move(other.m_groupName))
        , m_subMonsterCount(other.m_subMonsterCount)
        , m_monsters(std::move(other.m_monsters))
        , m_groupBehavior(std::move(other.m_groupBehavior))
        , m_isActive(other.m_isActive)
        , m_isSpawned(other.m_isSpawned)
        , m_isInitialized(other.m_isInitialized)
        , m_eventHandlers(std::move(other.m_eventHandlers)) {
        other.m_subMonsterCount = 0;
        other.m_isActive = false;
        other.m_isSpawned = false;
        other.m_isInitialized = false;
    }

    MonsterGroup& MonsterGroup::operator=(MonsterGroup&& other) noexcept {
        if (this != &other) {
            m_groupName = std::move(other.m_groupName);
            m_subMonsterCount = other.m_subMonsterCount;
            m_monsters = std::move(other.m_monsters);
            m_groupBehavior = std::move(other.m_groupBehavior);
            m_isActive = other.m_isActive;
            m_isSpawned = other.m_isSpawned;
            m_isInitialized = other.m_isInitialized;
            m_eventHandlers = std::move(other.m_eventHandlers);
            
            other.m_subMonsterCount = 0;
            other.m_isActive = false;
            other.m_isSpawned = false;
            other.m_isInitialized = false;
        }
        return *this;
    }

    void MonsterGroup::Initialize() {
        m_groupName.clear();
        m_subMonsterCount = 0;
        m_monsters.clear();
        m_groupBehavior.clear();
        m_isActive = false;
        m_isSpawned = false;
        m_eventHandlers.clear();
        m_isInitialized = true;
    }

    void MonsterGroup::Reset() {
        DespawnGroup();
        Initialize();
    }

    bool MonsterGroup::IsValid() const {
        return m_isInitialized && (HasGroupName() || m_subMonsterCount > 0);
    }

    void MonsterGroup::SetGroupName(const std::string& name) {
        m_groupName = name;
        if (m_groupName.length() > MAX_GROUP_NAME_LENGTH) {
            m_groupName.resize(MAX_GROUP_NAME_LENGTH);
        }
        NotifyGroupStateChange();
    }

    void MonsterGroup::SetSubMonsterCount(uint32_t count) {
        m_subMonsterCount = std::min(count, MAX_SUB_MONSTER_COUNT);
        NotifyGroupStateChange();
    }

    void MonsterGroup::AddMonster(std::shared_ptr<Monster> monster) {
        if (monster && m_monsters.size() < MAX_MONSTERS_PER_GROUP) {
            m_monsters.push_back(monster);
            NotifyGroupStateChange();
        }
    }

    void MonsterGroup::RemoveMonster(const std::string& monsterId) {
        auto it = std::remove_if(m_monsters.begin(), m_monsters.end(),
            [&monsterId](const std::weak_ptr<Monster>& monster) {
                // This is a simplified check - in real implementation,
                // Monster class would have an ID getter
                return false; // Placeholder
            });
        
        if (it != m_monsters.end()) {
            m_monsters.erase(it, m_monsters.end());
            NotifyGroupStateChange();
        }
    }

    std::shared_ptr<Monster> MonsterGroup::GetMonster(const std::string& monsterId) const {
        // Simplified implementation - in real code, Monster would have ID comparison
        return nullptr; // Placeholder
    }

    void MonsterGroup::SpawnGroup(float x, float y, float z) {
        if (!m_isSpawned && m_isActive) {
            // Spawn logic would go here
            m_isSpawned = true;
            TriggerGroupEvent("group_spawned");
        }
    }

    void MonsterGroup::DespawnGroup() {
        if (m_isSpawned) {
            // Despawn logic would go here
            m_monsters.clear();
            m_isSpawned = false;
            TriggerGroupEvent("group_despawned");
        }
    }

    void MonsterGroup::UpdateGroup(float deltaTime) {
        if (m_isActive && m_isSpawned) {
            CleanupInactiveMonsters();
            // Update logic for all monsters in group
            TriggerGroupEvent("group_updated");
        }
    }

    void MonsterGroup::SetGroupBehavior(const std::string& behavior) {
        m_groupBehavior = behavior;
        NotifyGroupStateChange();
    }

    void MonsterGroup::SetGroupConfiguration(const std::string& name, uint32_t count, const std::string& behavior) {
        SetGroupName(name);
        SetSubMonsterCount(count);
        SetGroupBehavior(behavior);
    }

    void MonsterGroup::ClearGroup() {
        DespawnGroup();
        m_groupName.clear();
        m_subMonsterCount = 0;
        m_groupBehavior.clear();
        m_isActive = false;
    }

    std::string MonsterGroup::ToString() const {
        std::ostringstream oss;
        oss << "MonsterGroup{";
        oss << "Name: \"" << m_groupName << "\", ";
        oss << "SubMonsterCount: " << m_subMonsterCount << ", ";
        oss << "ActiveMonsters: " << m_monsters.size() << ", ";
        oss << "Behavior: \"" << m_groupBehavior << "\", ";
        oss << "Active: " << (m_isActive ? "true" : "false") << ", ";
        oss << "Spawned: " << (m_isSpawned ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    size_t MonsterGroup::GetMemoryUsage() const {
        return sizeof(MonsterGroup) + 
               m_groupName.capacity() + 
               m_groupBehavior.capacity() +
               (m_monsters.size() * sizeof(std::shared_ptr<Monster>)) +
               (m_eventHandlers.size() * (sizeof(std::string) + sizeof(std::function<void(const MonsterGroup&)>)));
    }

    const char* MonsterGroup::GetGroupNameCStr() const {
        return m_groupName.c_str();
    }

    void MonsterGroup::SetGroupNameFromCStr(const char* name) {
        if (name) {
            SetGroupName(std::string(name));
        } else {
            m_groupName.clear();
        }
    }

    bool MonsterGroup::ValidateGroupName() const {
        return IsValidString(m_groupName, MAX_GROUP_NAME_LENGTH);
    }

    bool MonsterGroup::ValidateSubMonsterCount() const {
        return m_subMonsterCount <= MAX_SUB_MONSTER_COUNT;
    }

    bool MonsterGroup::ValidateGroupConfiguration() const {
        return ValidateGroupName() && ValidateSubMonsterCount() && IsValid();
    }

    void MonsterGroup::RegisterGroupEventHandler(const std::string& event, std::function<void(const MonsterGroup&)> handler) {
        m_eventHandlers[event] = std::move(handler);
    }

    void MonsterGroup::TriggerGroupEvent(const std::string& event) {
        auto it = m_eventHandlers.find(event);
        if (it != m_eventHandlers.end() && it->second) {
            it->second(*this);
        }
    }

    void MonsterGroup::ValidateAndTruncate() {
        if (m_groupName.length() > MAX_GROUP_NAME_LENGTH) {
            m_groupName.resize(MAX_GROUP_NAME_LENGTH);
        }
        if (m_subMonsterCount > MAX_SUB_MONSTER_COUNT) {
            m_subMonsterCount = MAX_SUB_MONSTER_COUNT;
        }
    }

    bool MonsterGroup::IsValidString(const std::string& str, size_t maxLength) const {
        return str.length() <= maxLength;
    }

    void MonsterGroup::NotifyGroupStateChange() {
        TriggerGroupEvent("group_state_changed");
    }

    void MonsterGroup::CleanupInactiveMonsters() {
        // Remove expired weak_ptr references
        auto it = std::remove_if(m_monsters.begin(), m_monsters.end(),
            [](const std::shared_ptr<Monster>& monster) {
                return !monster; // Remove null pointers
            });
        
        if (it != m_monsters.end()) {
            m_monsters.erase(it, m_monsters.end());
        }
    }

    // MonsterGroupFactory implementation

    std::unique_ptr<MonsterGroup> MonsterGroupFactory::CreateMonsterGroup() {
        return std::make_unique<MonsterGroup>();
    }

    std::unique_ptr<MonsterGroup> MonsterGroupFactory::CreateMonsterGroup(const std::string& groupName) {
        return std::make_unique<MonsterGroup>(groupName);
    }

    std::unique_ptr<MonsterGroup> MonsterGroupFactory::CreateMonsterGroup(const std::string& groupName, uint32_t subMonsterCount) {
        return std::make_unique<MonsterGroup>(groupName, subMonsterCount);
    }

    std::unique_ptr<MonsterGroup> MonsterGroupFactory::CreateOrcGroup(const std::string& name) {
        auto group = std::make_unique<MonsterGroup>(name, 5);
        group->SetGroupBehavior("aggressive");
        group->SetGroupActive(true);
        return group;
    }

    std::unique_ptr<MonsterGroup> MonsterGroupFactory::CreateGoblinGroup(const std::string& name) {
        auto group = std::make_unique<MonsterGroup>(name, 8);
        group->SetGroupBehavior("swarm");
        group->SetGroupActive(true);
        return group;
    }

    std::unique_ptr<MonsterGroup> MonsterGroupFactory::CreateEliteGroup(const std::string& name) {
        auto group = std::make_unique<MonsterGroup>(name, 3);
        group->SetGroupBehavior("tactical");
        group->SetGroupActive(true);
        return group;
    }

    std::vector<std::unique_ptr<MonsterGroup>> MonsterGroupFactory::CreateMonsterGroups(
        const std::vector<std::pair<std::string, uint32_t>>& groupConfigs) {
        std::vector<std::unique_ptr<MonsterGroup>> result;
        result.reserve(groupConfigs.size());

        for (const auto& config : groupConfigs) {
            result.push_back(CreateMonsterGroup(config.first, config.second));
        }

        return result;
    }

    // MonsterGroupManager implementation

    MonsterGroupManager::MonsterGroupManager() {
        m_groups.reserve(50); // Reserve space for typical group count
    }

    void MonsterGroupManager::AddGroup(std::unique_ptr<MonsterGroup> group) {
        if (group && !group->GetGroupName().empty()) {
            std::string groupName = group->GetGroupName();
            m_groupNameToIndex[groupName] = m_groups.size();
            m_groups.push_back(std::move(group));
        }
    }

    void MonsterGroupManager::RemoveGroup(const std::string& groupName) {
        auto it = m_groupNameToIndex.find(groupName);
        if (it != m_groupNameToIndex.end()) {
            size_t index = it->second;
            if (index < m_groups.size()) {
                m_groups.erase(m_groups.begin() + index);
                m_groupNameToIndex.erase(it);

                // Update indices for remaining groups
                for (auto& pair : m_groupNameToIndex) {
                    if (pair.second > index) {
                        --pair.second;
                    }
                }
            }
        }
    }

    std::shared_ptr<MonsterGroup> MonsterGroupManager::GetGroup(const std::string& groupName) const {
        auto it = m_groupNameToIndex.find(groupName);
        if (it != m_groupNameToIndex.end() && it->second < m_groups.size()) {
            return m_groups[it->second];
        }
        return nullptr;
    }

    void MonsterGroupManager::SpawnAllGroups() {
        for (auto& group : m_groups) {
            if (group && group->IsGroupActive()) {
                group->SpawnGroup(0.0f, 0.0f, 0.0f); // Default spawn position
            }
        }
    }

    void MonsterGroupManager::DespawnAllGroups() {
        for (auto& group : m_groups) {
            if (group) {
                group->DespawnGroup();
            }
        }
    }

    void MonsterGroupManager::UpdateAllGroups(float deltaTime) {
        for (auto& group : m_groups) {
            if (group) {
                group->UpdateGroup(deltaTime);
            }
        }
    }

    size_t MonsterGroupManager::GetActiveGroupCount() const {
        return std::count_if(m_groups.begin(), m_groups.end(),
            [](const std::shared_ptr<MonsterGroup>& group) {
                return group && group->IsGroupActive();
            });
    }

    size_t MonsterGroupManager::GetTotalMonsterCount() const {
        size_t total = 0;
        for (const auto& group : m_groups) {
            if (group) {
                total += group->GetActiveMonsterCount();
            }
        }
        return total;
    }

    // MonsterGroupUtils implementation

    namespace MonsterGroupUtils {

        bool ValidateMonsterGroup(const MonsterGroup& group) {
            return group.IsValid() && group.ValidateGroupConfiguration();
        }

        bool ValidateGroupName(const std::string& name) {
            return name.length() <= MonsterGroup::MAX_GROUP_NAME_LENGTH && !name.empty();
        }

        bool ValidateSubMonsterCount(uint32_t count) {
            return count <= MonsterGroup::MAX_SUB_MONSTER_COUNT;
        }

        std::string SanitizeGroupName(const std::string& name) {
            std::string sanitized = name;
            // Remove any null characters
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), '\0'), sanitized.end());
            // Truncate if too long
            if (sanitized.length() > MonsterGroup::MAX_GROUP_NAME_LENGTH) {
                sanitized.resize(MonsterGroup::MAX_GROUP_NAME_LENGTH);
            }
            return sanitized;
        }

        std::string GenerateUniqueGroupName(const std::string& baseName) {
            static uint32_t counter = 0;
            std::ostringstream oss;
            oss << baseName << "_" << ++counter;
            return oss.str();
        }

        size_t CalculateMemoryFootprint(const MonsterGroup& group) {
            return group.GetMemoryUsage();
        }

        void ConfigureDefaultGroup(MonsterGroup& group) {
            if (!group.HasGroupName()) {
                group.SetGroupName("DefaultGroup");
            }
            if (group.GetSubMonsterCount() == 0) {
                group.SetSubMonsterCount(5);
            }
            group.SetGroupBehavior("normal");
            group.SetGroupActive(true);
        }

        void OptimizeGroupConfiguration(MonsterGroup& group) {
            // Optimization logic could be added here
            // For now, just ensure group has valid configuration
            if (!ValidateMonsterGroup(group)) {
                ConfigureDefaultGroup(group);
            }
        }

        std::string MonsterGroupToJson(const MonsterGroup& group) {
            std::ostringstream oss;
            oss << "{";
            oss << "\"groupName\": \"" << group.GetGroupName() << "\",";
            oss << "\"subMonsterCount\": " << group.GetSubMonsterCount() << ",";
            oss << "\"groupBehavior\": \"" << group.GetGroupBehavior() << "\",";
            oss << "\"isActive\": " << (group.IsGroupActive() ? "true" : "false") << ",";
            oss << "\"isSpawned\": " << (group.IsGroupSpawned() ? "true" : "false");
            oss << "}";
            return oss.str();
        }

        std::unique_ptr<MonsterGroup> MonsterGroupFromJson(const std::string& json) {
            // Simple JSON parsing - in a real implementation, use a proper JSON library
            auto group = std::make_unique<MonsterGroup>();

            // This is a simplified implementation
            // In production, use a proper JSON parser like nlohmann/json

            return group;
        }

        std::vector<std::string> AnalyzeGroupComposition(const MonsterGroup& group) {
            std::vector<std::string> analysis;

            analysis.push_back("Group Name: " + group.GetGroupName());
            analysis.push_back("Sub-Monster Count: " + std::to_string(group.GetSubMonsterCount()));
            analysis.push_back("Active Monsters: " + std::to_string(group.GetActiveMonsterCount()));
            analysis.push_back("Group Behavior: " + group.GetGroupBehavior());
            analysis.push_back("Group Status: " + (group.IsGroupActive() ? "Active" : "Inactive"));

            return analysis;
        }

        float CalculateGroupThreatLevel(const MonsterGroup& group) {
            float threatLevel = 0.0f;

            // Base threat from sub-monster count
            threatLevel += group.GetSubMonsterCount() * 1.0f;

            // Behavior modifier
            const std::string& behavior = group.GetGroupBehavior();
            if (behavior == "aggressive") {
                threatLevel *= 1.5f;
            } else if (behavior == "tactical") {
                threatLevel *= 2.0f;
            } else if (behavior == "swarm") {
                threatLevel *= 1.2f;
            }

            // Active status modifier
            if (group.IsGroupActive() && group.IsGroupSpawned()) {
                threatLevel *= 1.3f;
            }

            return threatLevel;
        }

    } // namespace MonsterGroupUtils

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {

    void __monster_group_Constructor(NexusProtection::World::_monster_group* this_ptr) {
        if (this_ptr) {
            // Initialize pointers to null (matching original constructor behavior)
            this_ptr->pszGroupName = nullptr;
            this_ptr->nSubMonsterNum = 0;

            // Clear padding
            std::memset(this_ptr->padding, 0, sizeof(this_ptr->padding));
        }
    }

    void __monster_group_Destructor(NexusProtection::World::_monster_group* this_ptr) {
        if (this_ptr) {
            // Clean up allocated memory (matching original destructor behavior)
            if (this_ptr->pszGroupName) {
                delete[] this_ptr->pszGroupName;
                this_ptr->pszGroupName = nullptr;
            }
            this_ptr->nSubMonsterNum = 0;
        }
    }

    void __monster_group_SetGroupName(NexusProtection::World::_monster_group* this_ptr, const char* name) {
        if (!this_ptr) {
            return;
        }

        // Clean up existing memory
        if (this_ptr->pszGroupName) {
            delete[] this_ptr->pszGroupName;
            this_ptr->pszGroupName = nullptr;
        }

        // Allocate and copy new string
        if (name) {
            size_t len = std::strlen(name) + 1;
            this_ptr->pszGroupName = new char[len];
            std::strcpy(this_ptr->pszGroupName, name);
        }
    }

    void __monster_group_SetSubMonsterCount(NexusProtection::World::_monster_group* this_ptr, uint32_t count) {
        if (this_ptr) {
            this_ptr->nSubMonsterNum = count;
        }
    }

    const char* __monster_group_GetGroupName(NexusProtection::World::_monster_group* this_ptr) {
        return (this_ptr && this_ptr->pszGroupName) ? this_ptr->pszGroupName : "";
    }

    uint32_t __monster_group_GetSubMonsterCount(NexusProtection::World::_monster_group* this_ptr) {
        return this_ptr ? this_ptr->nSubMonsterNum : 0;
    }

    void __monster_group_Initialize(NexusProtection::World::_monster_group* this_ptr) {
        if (this_ptr) {
            __monster_group_Constructor(this_ptr);
        }
    }

    void __monster_group_Reset(NexusProtection::World::_monster_group* this_ptr) {
        if (this_ptr) {
            __monster_group_Destructor(this_ptr);
            __monster_group_Constructor(this_ptr);
        }
    }

} // extern "C"

// Global legacy compatibility
NexusProtection::World::MonsterGroup* g_pMonsterGroup = nullptr;
