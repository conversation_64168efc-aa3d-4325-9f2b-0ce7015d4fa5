/*
 * Function: ?UpdatePvpPointLimiter@CPlayer@@QEAAX_J@Z
 * Address: 0x1402081C0
 */

void __usercall CPlayer::UpdatePvpPointLimiter(CPlayer *this@<rcx>, __int64 tCurTime@<rdx>, long double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *pkSelf; // [sp+30h] [bp+8h]@1
  __int64 tUpdateTime; // [sp+38h] [bp+10h]@1

  tUpdateTime = tCurTime;
  pkSelf = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPlayerDB::GetPvPPoint(&pkSelf->m_Param);
  CPvpPointLimiter::Clear(&pkSelf->m_kPvpPointLimiter, tUpdateTime, a3, pkSelf);
}
