/*
 * Function: ?CheckPvpHaveCondition@CPvpCashPoint@@QEAA_NPEAVCPlayer@@0N@Z
 * Address: 0x1403F54F0
 */

bool __fastcall CPvpCashPoint::CheckPvpHaveCondition(CPvpCashPoint *this, CPlayer *pKiller, CPlayer *pDier, long double dOldTempPoint)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  unsigned int v7; // eax@9
  int v8; // eax@11
  char v9; // al@13
  __int64 v10; // [sp-20h] [bp-38h]@1
  int v11; // [sp+0h] [bp-18h]@11
  CPvpCashPoint *v12; // [sp+20h] [bp+8h]@1
  CPlayer *pOne; // [sp+28h] [bp+10h]@1
  CPlayer *v14; // [sp+30h] [bp+18h]@1

  v14 = pDier;
  pOne = pKiller;
  v12 = this;
  v4 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pKiller && pDier )
  {
    if ( CPvpCashPoint::IsPvpMap(v12, pKiller) )
    {
      v7 = CPlayerDB::GetCharSerial(&pOne->m_Param);
      if ( CIndexList::IsInList(&v14->m_kPvpCashPoint.m_KillerList, v7, 0i64) )
      {
        CPvpCashPoint::SendMsg_PvpCashInform(v12, pOne->m_ObjID.m_wIndex, 4);
        result = 0;
      }
      else
      {
        v11 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        v8 = CPlayerDB::GetRaceCode(&v14->m_Param);
        if ( v11 == v8 )
        {
          result = 0;
        }
        else
        {
          v9 = ((int (__fastcall *)(CPlayer *))v14->vfptr->GetLevel)(v14);
          result = (double)CPvpCashPoint::GetMinTempPoint(&v14->m_kPvpCashPoint, v9) < dOldTempPoint;
        }
      }
    }
    else
    {
      CPvpCashPoint::SendMsg_PvpCashInform(v12, pOne->m_ObjID.m_wIndex, 9);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
