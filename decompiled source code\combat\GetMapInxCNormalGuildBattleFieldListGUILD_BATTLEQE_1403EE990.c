/*
 * Function: ?GetMapInx@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAA_NEKAEAK@Z
 * Address: 0x1403EE990
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapInx(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace, unsigned int dwMapCode, unsigned int *dwMapInx)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned __int8 j; // [sp+20h] [bp-18h]@9
  GUILD_BATTLE::CNormalGuildBattleFieldList *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1
  unsigned int v11; // [sp+50h] [bp+18h]@1
  unsigned int *v12; // [sp+58h] [bp+20h]@1

  v12 = dwMapInx;
  v11 = dwMapCode;
  v10 = byRace;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    if ( v9->m_byUseFieldCnt[(unsigned __int8)byRace] && v9->m_ppkUseFieldByRace[(unsigned __int8)byRace] )
    {
      for ( j = 0; j < (signed int)v9->m_byUseFieldCnt[(unsigned __int8)v10]; ++j )
      {
        if ( v11 == GUILD_BATTLE::CNormalGuildBattleField::GetMapCode(v9->m_ppkUseFieldByRace[(unsigned __int8)v10][j]) )
        {
          *v12 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v9->m_ppkUseFieldByRace[(unsigned __int8)v10][j]);
          return 1;
        }
      }
      result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
