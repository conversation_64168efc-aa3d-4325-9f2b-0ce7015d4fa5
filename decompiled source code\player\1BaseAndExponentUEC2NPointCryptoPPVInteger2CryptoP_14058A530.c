/*
 * Function: ??1?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14058A530
 */

void __fastcall CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(__int64 a1)
{
  CryptoPP::EC2NPoint *v1; // [sp+40h] [bp+8h]@1

  v1 = (CryptoPP::EC2NPoint *)a1;
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a1 + 56));
  CryptoPP::EC2NPoint::~EC2NPoint(v1);
}
