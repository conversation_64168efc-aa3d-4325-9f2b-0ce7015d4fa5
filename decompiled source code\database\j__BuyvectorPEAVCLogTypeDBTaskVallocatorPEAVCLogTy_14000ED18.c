/*
 * Function: j_?_Buy@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x14000ED18
 */

bool __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Buy(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, unsigned __int64 _Capacity)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Buy(this, _Capacity);
}
