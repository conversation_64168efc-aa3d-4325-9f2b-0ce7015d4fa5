/*
 * Function: ?CombineExItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CB030
 */

char __fastcall CNetworkEX::CombineExItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  int v7; // eax@10
  char *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-38h]@1
  _combine_ex_item_request_clzo *pRecv; // [sp+20h] [bp-18h]@4
  CPlayer *v11; // [sp+28h] [bp-10h]@4
  CNetworkEX *v12; // [sp+40h] [bp+8h]@1

  v12 = this;
  v3 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pRecv = (_combine_ex_item_request_clzo *)pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( pRecv->byCombineSlotNum <= 5 )
  {
    v7 = CRecordData::GetRecordNum(&ItemCombineMgr::ms_tbl_ItemCombine);
    if ( pRecv->wManualIndex < v7 )
    {
      CPlayer::pc_CombineItemEx(v11, pRecv);
      result = 1;
    }
    else
    {
      v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v12->m_LogFile,
        "odd.. %s: CombineItemRequest()..  if(pRecv->wManualIndex >= ItemCombineMgr::ms_tbl_ItemCombine[eCOMMON].GetRecordNum()))",
        v8);
      result = 0;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v12->m_LogFile,
      "odd.. %s: CombineExItemRequest()..  if(pRecv->byCombineSlotNum > _combine_ex_item_request_clzo::combineslot_max ))",
      v6);
    result = 0;
  }
  return result;
}
