/*
 * Function: ?GetTopDamageCharacter@CMonsterAggroMgr@@QEAAPEAVCCharacter@@XZ
 * Address: 0x14015E000
 */

CCharacter *__fastcall CMonsterAggroMgr::GetTopDamageCharacter(CMonsterAggroMgr *this)
{
  CCharacter *result; // rax@4

  if ( this->m_pTopDamageCharacter && this->m_pTopDamageCharacter->m_bLive && !this->m_pTopDamageCharacter->m_bCorpse )
  {
    result = this->m_pTopDamageCharacter;
  }
  else
  {
    this->m_pTopDamageCharacter = 0i64;
    result = 0i64;
  }
  return result;
}
