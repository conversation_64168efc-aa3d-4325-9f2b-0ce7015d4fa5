/*
 * Function: ?extract@AP_BatterySlot@@QEAA_NPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402D9E20
 */

char __fastcall AP_BatterySlot::extract(AP_BatterySlot *this, _STORAGE_LIST::_db_con *pout_item)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  AP_BatterySlot *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_bFill )
  {
    memcpy_0(pout_item, &v6->battery_, 0x32ui64);
    v6->m_bFill = 0;
    v6->battery_.m_bLoad = 0;
    v6->battery_.m_dwDur = 0i64;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
