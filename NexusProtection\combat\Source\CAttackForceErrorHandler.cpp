/*
 * CAttackForceErrorHandler.cpp - Comprehensive Error Handling Implementation
 * Provides robust error handling, validation, and recovery mechanisms
 */

#include "../Headers/CAttackForceErrorHandler.h"
#include "../Headers/CAttackForceIntegration.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CMonsterAttack.h"

#include <algorithm>
#include <thread>
#include <stdexcept>

using namespace NexusProtection::Combat::AttackForceConstants;

namespace NexusProtection {
namespace Combat {

/**
 * Constructor
 */
CAttackForceErrorHandler::CAttackForceErrorHandler() 
    : m_bDetailedLogging(false) {
    
    InitializeDefaultStrategies();
    Logger::Debug("CAttackForceErrorHandler::CAttackForceErrorHandler - Error handler initialized");
}

/**
 * Destructor
 */
CAttackForceErrorHandler::~CAttackForceErrorHandler() {
    try {
        ClearStatistics();
        Logger::Debug("CAttackForceErrorHandler::~CAttackForceErrorHandler - Error handler destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Validate attack parameters
 */
ValidationResult CAttackForceErrorHandler::ValidateAttackParameters(_attack_param* pParam) {
    ValidationResult result;
    
    if (!pParam) {
        result.AddError(AttackForceErrorCategory::InvalidParameters, "Attack parameters pointer is null");
        return result;
    }
    
    try {
        // Validate attack force values
        ValidationResult afValidation = ValidateRange(pParam->nMinAF, 0, 999999, "MinAF");
        if (!afValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid MinAF value");
        }
        
        afValidation = ValidateRange(pParam->nMaxAF, pParam->nMinAF, 999999, "MaxAF");
        if (!afValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid MaxAF value or MaxAF < MinAF");
        }
        
        // Validate selection values
        ValidationResult selValidation = ValidateRange(pParam->nMinSel, 0, 100, "MinSel");
        if (!selValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid MinSel value (must be 0-100)");
        }
        
        selValidation = ValidateRange(pParam->nMaxSel, pParam->nMinSel, 100, "MaxSel");
        if (!selValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid MaxSel value or MaxSel < MinSel");
        }
        
        // Validate extent range
        ValidationResult rangeValidation = ValidateRange(pParam->nExtentRange, 0, 2000, "ExtentRange");
        if (!rangeValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid ExtentRange value (must be 0-2000)");
        }
        
        // Validate additional attack points
        ValidationResult addValidation = ValidateRange(pParam->nAddAttPnt, -999999, 999999, "AddAttPnt");
        if (!addValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid AddAttPnt value");
        }
        
        // Validate part and tolerance values
        ValidationResult partValidation = ValidateRange(pParam->nPart, 0, 10, "Part");
        if (!partValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid Part value (must be 0-10)");
        }
        
        ValidationResult tolValidation = ValidateRange(pParam->nTol, 0, 10, "Tol");
        if (!tolValidation.bValid) {
            result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid Tol value (must be 0-10)");
        }
        
        // Validate area coordinates if present
        if (pParam->fArea.size() >= 3) {
            for (size_t i = 0; i < 3; ++i) {
                ValidationResult areaValidation = ValidateRange(pParam->fArea[i], -10000.0f, 10000.0f, "Area[" + std::to_string(i) + "]");
                if (!areaValidation.bValid) {
                    result.AddError(AttackForceErrorCategory::InvalidParameters, "Invalid area coordinate value");
                    break;
                }
            }
        }
        
        // Log validation result
        if (m_bDetailedLogging) {
            Logger::Debug("CAttackForceErrorHandler::ValidateAttackParameters - %s", result.GetSummary().c_str());
        }
        
    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError, std::string("Exception during parameter validation: ") + e.what());
        Logger::Error("CAttackForceErrorHandler::ValidateAttackParameters - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Validate attacking character
 */
ValidationResult CAttackForceErrorHandler::ValidateAttacker(CCharacter* pAttacker) {
    ValidationResult result;
    
    if (!pAttacker) {
        result.AddError(AttackForceErrorCategory::InvalidCharacter, "Attacker pointer is null");
        return result;
    }
    
    try {
        // Validate character state
        ValidationResult stateValidation = ValidateCharacterState(pAttacker, "Attacker");
        if (!stateValidation.bValid) {
            for (const auto& error : stateValidation.errors) {
                result.AddError(error);
            }
        }
        
        // Check if character is alive
        // In real implementation: if (pAttacker->IsDead()) { result.AddError(...); }
        
        // Check if character can attack
        // In real implementation: if (!pAttacker->CanAttack()) { result.AddError(...); }
        
        // Check attack cooldown
        // In real implementation: if (pAttacker->IsInAttackCooldown()) { result.AddError(...); }
        
        // Log validation result
        if (m_bDetailedLogging) {
            Logger::Debug("CAttackForceErrorHandler::ValidateAttacker - %s", result.GetSummary().c_str());
        }
        
    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError, std::string("Exception during attacker validation: ") + e.what());
        Logger::Error("CAttackForceErrorHandler::ValidateAttacker - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Validate target character
 */
ValidationResult CAttackForceErrorHandler::ValidateTarget(CCharacter* pTarget, CCharacter* pAttacker) {
    ValidationResult result;
    
    // Target can be null for area attacks, so only validate if present
    if (!pTarget) {
        return result; // Valid for area attacks
    }
    
    try {
        // Validate character state
        ValidationResult stateValidation = ValidateCharacterState(pTarget, "Target");
        if (!stateValidation.bValid) {
            for (const auto& error : stateValidation.errors) {
                result.AddError(error);
            }
        }
        
        // Check if target is same as attacker
        if (pAttacker && pTarget == pAttacker) {
            result.AddError(AttackForceErrorCategory::InvalidTarget, "Target cannot be the same as attacker");
        }
        
        // Check if target is invulnerable
        if (CAttackForceIntegration::GetEffectState(pTarget, static_cast<int>(EffectType::Invulnerability))) {
            result.AddError(AttackForceErrorCategory::InvalidTarget, "Target is invulnerable", AttackForceErrorSeverity::Warning);
        }
        
        // Check if target is alive
        // In real implementation: if (pTarget->IsDead()) { result.AddError(...); }
        
        // Check if target can be attacked (PvP rules, faction, etc.)
        // In real implementation: if (!CanAttackTarget(pAttacker, pTarget)) { result.AddError(...); }
        
        // Log validation result
        if (m_bDetailedLogging) {
            Logger::Debug("CAttackForceErrorHandler::ValidateTarget - %s", result.GetSummary().c_str());
        }
        
    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError, std::string("Exception during target validation: ") + e.what());
        Logger::Error("CAttackForceErrorHandler::ValidateTarget - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Validate damage calculation context
 */
ValidationResult CAttackForceErrorHandler::ValidateDamageContext(CCharacter* pAttacker, CCharacter* pTarget, _attack_param* pParam) {
    ValidationResult result;
    
    try {
        // Validate attacker
        ValidationResult attackerValidation = ValidateAttacker(pAttacker);
        if (!attackerValidation.bValid) {
            for (const auto& error : attackerValidation.errors) {
                result.AddError(error);
            }
        }
        
        // Validate target (if present)
        if (pTarget) {
            ValidationResult targetValidation = ValidateTarget(pTarget, pAttacker);
            if (!targetValidation.bValid) {
                for (const auto& error : targetValidation.errors) {
                    result.AddError(error);
                }
            }
        }
        
        // Validate parameters
        ValidationResult paramValidation = ValidateAttackParameters(pParam);
        if (!paramValidation.bValid) {
            for (const auto& error : paramValidation.errors) {
                result.AddError(error);
            }
        }
        
        // Additional context-specific validations
        if (pAttacker && pTarget) {
            // Check distance between attacker and target
            // In real implementation: float distance = CalculateDistance(pAttacker, pTarget);
            // if (distance > MAX_ATTACK_RANGE) { result.AddError(...); }
            
            // Check line of sight
            // In real implementation: if (!HasLineOfSight(pAttacker, pTarget)) { result.AddError(...); }
        }
        
        // Log validation result
        if (m_bDetailedLogging) {
            Logger::Debug("CAttackForceErrorHandler::ValidateDamageContext - %s", result.GetSummary().c_str());
        }
        
    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError, std::string("Exception during context validation: ") + e.what());
        Logger::Error("CAttackForceErrorHandler::ValidateDamageContext - Exception: %s", e.what());
    }
    
    return result;
}

/**
 * Handle error with recovery strategy
 */
bool CAttackForceErrorHandler::HandleError(const AttackForceError& error, ErrorRecoveryContext& context) {
    try {
        // Log the error
        LogError(error);
        
        // Update statistics
        UpdateStatistics(error.category);
        
        // Determine recovery strategy
        ErrorRecoveryStrategy strategy = ErrorRecoveryStrategy::None;
        for (const auto& [category, strategyType] : m_recoveryStrategies) {
            if (category == error.category) {
                strategy = strategyType;
                break;
            }
        }
        
        context.strategy = strategy;
        context.error = error;
        
        // Execute recovery strategy
        switch (strategy) {
            case ErrorRecoveryStrategy::Retry:
                if (context.CanRetry()) {
                    context.IncrementRetry();
                    Logger::Info("CAttackForceErrorHandler::HandleError - Retrying operation (attempt %d/%d)", 
                               context.retryCount, context.maxRetries);
                    
                    // Wait before retry
                    std::this_thread::sleep_for(context.retryDelay);
                    return true;
                }
                break;
                
            case ErrorRecoveryStrategy::UseDefault:
                Logger::Info("CAttackForceErrorHandler::HandleError - Using default values for recovery");
                return true;
                
            case ErrorRecoveryStrategy::Skip:
                Logger::Info("CAttackForceErrorHandler::HandleError - Skipping operation due to error");
                return true;
                
            case ErrorRecoveryStrategy::Fallback:
                if (context.recoveryAction) {
                    Logger::Info("CAttackForceErrorHandler::HandleError - Executing fallback action");
                    return context.recoveryAction();
                }
                break;
                
            case ErrorRecoveryStrategy::Abort:
            default:
                Logger::Error("CAttackForceErrorHandler::HandleError - Aborting operation due to error");
                return false;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceErrorHandler::HandleError - Exception during error handling: %s", e.what());
        return false;
    }
}

/**
 * Log error
 */
void CAttackForceErrorHandler::LogError(const AttackForceError& error) {
    try {
        std::string logMessage = error.ToString();
        
        switch (error.severity) {
            case AttackForceErrorSeverity::Info:
                Logger::Info(logMessage.c_str());
                break;
            case AttackForceErrorSeverity::Warning:
                Logger::Warning(logMessage.c_str());
                break;
            case AttackForceErrorSeverity::Error:
                Logger::Error(logMessage.c_str());
                break;
            case AttackForceErrorSeverity::Critical:
            case AttackForceErrorSeverity::Fatal:
                Logger::Critical(logMessage.c_str());
                break;
        }
        
        // Call error callback if set
        if (m_errorCallback) {
            m_errorCallback(error);
        }
        
    } catch (const std::exception& e) {
        // Fallback logging - can't use Logger here to avoid recursion
    }
}

/**
 * Create error with context
 */
AttackForceError CAttackForceErrorHandler::CreateError(AttackForceErrorCategory category, AttackForceErrorSeverity severity,
                                                      const std::string& message, const std::string& function,
                                                      const std::string& file, int line) {
    try {
        AttackForceError error(category, severity, 0, message, function, file, line);

        // Update statistics
        UpdateStatistics(category);

        return error;

    } catch (const std::exception& e) {
        // Return a basic error if creation fails
        return AttackForceError(AttackForceErrorCategory::SystemError, AttackForceErrorSeverity::Error, 0,
                               "Failed to create error: " + std::string(e.what()));
    }
}

/**
 * Set error callback
 */
void CAttackForceErrorHandler::SetErrorCallback(std::function<void(const AttackForceError&)> callback) {
    m_errorCallback = callback;
}

/**
 * Set recovery strategy for error category
 */
void CAttackForceErrorHandler::SetRecoveryStrategy(AttackForceErrorCategory category, ErrorRecoveryStrategy strategy) {
    try {
        // Find existing strategy and update, or add new one
        auto it = std::find_if(m_recoveryStrategies.begin(), m_recoveryStrategies.end(),
                              [category](const auto& pair) { return pair.first == category; });

        if (it != m_recoveryStrategies.end()) {
            it->second = strategy;
        } else {
            m_recoveryStrategies.emplace_back(category, strategy);
        }

        Logger::Debug("CAttackForceErrorHandler::SetRecoveryStrategy - Set strategy for category %d", static_cast<int>(category));

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceErrorHandler::SetRecoveryStrategy - Exception: %s", e.what());
    }
}

/**
 * Get error statistics
 */
std::vector<std::pair<AttackForceErrorCategory, int>> CAttackForceErrorHandler::GetErrorStatistics() const {
    return m_errorStatistics;
}

/**
 * Clear error statistics
 */
void CAttackForceErrorHandler::ClearStatistics() {
    try {
        m_errorStatistics.clear();
        Logger::Debug("CAttackForceErrorHandler::ClearStatistics - Statistics cleared");

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceErrorHandler::ClearStatistics - Exception: %s", e.what());
    }
}

/**
 * Validate character state
 */
ValidationResult CAttackForceErrorHandler::ValidateCharacterState(CCharacter* pCharacter, const std::string& context) {
    ValidationResult result;

    if (!pCharacter) {
        result.AddError(AttackForceErrorCategory::InvalidCharacter, context + " character pointer is null");
        return result;
    }

    try {
        // Check character object ID
        if (pCharacter->m_ObjID.m_byID < 0 || pCharacter->m_ObjID.m_byID > 10) {
            result.AddError(AttackForceErrorCategory::InvalidCharacter, context + " has invalid object ID");
        }

        // Check character serial
        if (pCharacter->m_dwObjSerial == 0) {
            result.AddError(AttackForceErrorCategory::InvalidCharacter, context + " has invalid serial number");
        }

        // Additional character state validations would go here
        // In real implementation: check health, mana, status effects, etc.

    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError,
                       context + " state validation failed: " + std::string(e.what()));
    }

    return result;
}

/**
 * Validate numeric range
 */
ValidationResult CAttackForceErrorHandler::ValidateRange(int value, int min, int max, const std::string& name) {
    ValidationResult result;

    try {
        if (value < min || value > max) {
            result.AddError(AttackForceErrorCategory::InvalidParameters,
                           name + " value " + std::to_string(value) + " is out of range [" +
                           std::to_string(min) + ", " + std::to_string(max) + "]");
        }

    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError,
                       "Range validation failed for " + name + ": " + std::string(e.what()));
    }

    return result;
}

/**
 * Validate float range
 */
ValidationResult CAttackForceErrorHandler::ValidateRange(float value, float min, float max, const std::string& name) {
    ValidationResult result;

    try {
        if (value < min || value > max || std::isnan(value) || std::isinf(value)) {
            result.AddError(AttackForceErrorCategory::InvalidParameters,
                           name + " value " + std::to_string(value) + " is out of range [" +
                           std::to_string(min) + ", " + std::to_string(max) + "] or invalid");
        }

    } catch (const std::exception& e) {
        result.AddError(AttackForceErrorCategory::SystemError,
                       "Range validation failed for " + name + ": " + std::string(e.what()));
    }

    return result;
}

/**
 * Initialize default recovery strategies
 */
void CAttackForceErrorHandler::InitializeDefaultStrategies() {
    try {
        // Set default recovery strategies for each error category
        m_recoveryStrategies.clear();

        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::InvalidInput, ErrorRecoveryStrategy::UseDefault);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::InvalidCharacter, ErrorRecoveryStrategy::Abort);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::InvalidTarget, ErrorRecoveryStrategy::Skip);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::InvalidParameters, ErrorRecoveryStrategy::UseDefault);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::CalculationError, ErrorRecoveryStrategy::Retry);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::SystemError, ErrorRecoveryStrategy::Abort);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::NetworkError, ErrorRecoveryStrategy::Retry);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::DatabaseError, ErrorRecoveryStrategy::Retry);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::SecurityError, ErrorRecoveryStrategy::Abort);
        m_recoveryStrategies.emplace_back(AttackForceErrorCategory::PerformanceError, ErrorRecoveryStrategy::Fallback);

        Logger::Debug("CAttackForceErrorHandler::InitializeDefaultStrategies - Initialized %zu recovery strategies",
                     m_recoveryStrategies.size());

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceErrorHandler::InitializeDefaultStrategies - Exception: %s", e.what());
    }
}

/**
 * Update error statistics
 */
void CAttackForceErrorHandler::UpdateStatistics(AttackForceErrorCategory category) {
    try {
        // Find existing category and increment, or add new one
        auto it = std::find_if(m_errorStatistics.begin(), m_errorStatistics.end(),
                              [category](const auto& pair) { return pair.first == category; });

        if (it != m_errorStatistics.end()) {
            it->second++;
        } else {
            m_errorStatistics.emplace_back(category, 1);
        }

    } catch (const std::exception& e) {
        // Don't log here to avoid potential recursion
    }
}

} // namespace Combat
} // namespace NexusProtection
