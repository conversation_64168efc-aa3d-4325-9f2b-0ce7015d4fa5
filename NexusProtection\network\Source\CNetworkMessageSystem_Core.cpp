/**
 * @file CNetworkMessageSystem_Core.cpp
 * @brief Modern C++20 Network Message System core implementation
 * 
 * This file provides the core implementation of the CNetworkMessageSystem class
 * with type-safe message handling, serialization, and protocol management.
 */

#include "../Headers/CNetworkMessageSystem.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <cstring>
#include <crc32.h> // Assuming CRC32 implementation available

namespace NexusProtection {
namespace Network {

// MessageHeader implementation
bool MessageHeader::IsValid() const {
    return size >= HEADER_SIZE && 
           type != MessageType::Unknown &&
           CalculateChecksum() == checksum;
}

uint32_t MessageHeader::CalculateChecksum() const {
    // Calculate CRC32 of header excluding checksum field
    const uint8_t* data = reinterpret_cast<const uint8_t*>(this);
    size_t checksumOffset = offsetof(MessageHeader, checksum);
    
    uint32_t crc = 0;
    // CRC of data before checksum field
    if (checksumOffset > 0) {
        crc = crc32(crc, data, checksumOffset);
    }
    // CRC of data after checksum field
    size_t remainingOffset = checksumOffset + sizeof(checksum);
    if (remainingOffset < sizeof(MessageHeader)) {
        crc = crc32(crc, data + remainingOffset, sizeof(MessageHeader) - remainingOffset);
    }
    
    return crc;
}

void MessageHeader::UpdateChecksum() {
    checksum = 0; // Reset before calculation
    checksum = CalculateChecksum();
}

// NetworkMessage implementation
NetworkMessage::NetworkMessage(MessageType type, uint8_t subType, MessageFlags flags)
    : m_header(type, subType, flags) {
}

NetworkMessage::NetworkMessage(const MessageHeader& header, const MessageData& data)
    : m_header(header), m_data(data) {
}

bool NetworkMessage::HasBinaryData() const {
    return std::holds_alternative<std::vector<uint8_t>>(m_data);
}

bool NetworkMessage::HasTextData() const {
    return std::holds_alternative<std::string>(m_data);
}

bool NetworkMessage::IsEmpty() const {
    return std::holds_alternative<std::monostate>(m_data);
}

std::optional<std::vector<uint8_t>> NetworkMessage::GetBinaryData() const {
    if (HasBinaryData()) {
        return std::get<std::vector<uint8_t>>(m_data);
    }
    return std::nullopt;
}

std::optional<std::string> NetworkMessage::GetTextData() const {
    if (HasTextData()) {
        return std::get<std::string>(m_data);
    }
    return std::nullopt;
}

std::vector<uint8_t> NetworkMessage::Serialize() const {
    std::vector<uint8_t> buffer;
    
    // Calculate total size
    size_t dataSize = 0;
    if (HasBinaryData()) {
        dataSize = std::get<std::vector<uint8_t>>(m_data).size();
    } else if (HasTextData()) {
        dataSize = std::get<std::string>(m_data).size();
    }
    
    size_t totalSize = MessageHeader::HEADER_SIZE + dataSize;
    buffer.reserve(totalSize);
    
    // Update header size
    MessageHeader header = m_header;
    header.size = static_cast<uint16_t>(totalSize);
    header.UpdateChecksum();
    
    // Serialize header
    const uint8_t* headerData = reinterpret_cast<const uint8_t*>(&header);
    buffer.insert(buffer.end(), headerData, headerData + MessageHeader::HEADER_SIZE);
    
    // Serialize data
    if (HasBinaryData()) {
        const auto& binaryData = std::get<std::vector<uint8_t>>(m_data);
        buffer.insert(buffer.end(), binaryData.begin(), binaryData.end());
    } else if (HasTextData()) {
        const auto& textData = std::get<std::string>(m_data);
        buffer.insert(buffer.end(), textData.begin(), textData.end());
    }
    
    return buffer;
}

bool NetworkMessage::Deserialize(const std::vector<uint8_t>& buffer) {
    return Deserialize(buffer.data(), buffer.size());
}

bool NetworkMessage::Deserialize(const uint8_t* buffer, size_t size) {
    try {
        if (!buffer || size < MessageHeader::HEADER_SIZE) {
            return false;
        }
        
        // Deserialize header
        std::memcpy(&m_header, buffer, MessageHeader::HEADER_SIZE);
        
        // Validate header
        if (!m_header.IsValid() || m_header.size > size) {
            return false;
        }
        
        // Deserialize data
        size_t dataSize = m_header.size - MessageHeader::HEADER_SIZE;
        if (dataSize > 0) {
            std::vector<uint8_t> data(buffer + MessageHeader::HEADER_SIZE, 
                                    buffer + MessageHeader::HEADER_SIZE + dataSize);
            m_data = std::move(data);
        } else {
            m_data = std::monostate{};
        }
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] NetworkMessage::Deserialize failed: " << e.what() << std::endl;
        return false;
    }
}

bool NetworkMessage::IsValid() const {
    return m_header.IsValid();
}

MessageProcessResult NetworkMessage::Validate() const {
    if (!m_header.IsValid()) {
        return MessageProcessResult::InvalidHeader;
    }
    
    // Additional validation logic can be added here
    
    return MessageProcessResult::Success;
}

void NetworkMessage::UpdateSize() {
    size_t dataSize = 0;
    if (HasBinaryData()) {
        dataSize = std::get<std::vector<uint8_t>>(m_data).size();
    } else if (HasTextData()) {
        dataSize = std::get<std::string>(m_data).size();
    }
    
    m_header.size = static_cast<uint16_t>(MessageHeader::HEADER_SIZE + dataSize);
}

void NetworkMessage::UpdateTimestamp() {
    m_header.timestamp = static_cast<uint32_t>(std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count());
}

void NetworkMessage::UpdateChecksum() {
    m_header.UpdateChecksum();
}

// CNetworkMessageSystem implementation
CNetworkMessageSystem::CNetworkMessageSystem() {
    std::cout << "[INFO] CNetworkMessageSystem constructor called" << std::endl;
}

CNetworkMessageSystem::~CNetworkMessageSystem() {
    std::cout << "[INFO] CNetworkMessageSystem destructor called" << std::endl;
    Shutdown();
}

bool CNetworkMessageSystem::Initialize() {
    std::lock_guard<std::mutex> lock(m_handlersMutex);
    
    try {
        std::cout << "[INFO] Initializing CNetworkMessageSystem" << std::endl;
        
        if (m_isInitialized) {
            std::cout << "[WARNING] Message system already initialized" << std::endl;
            return true;
        }
        
        // Register default handlers
        RegisterDefaultHandlers();
        
        // Reset statistics
        m_statistics = MessageStatistics{};
        
        m_isInitialized = true;
        m_isShutdown = false;
        
        std::cout << "[INFO] CNetworkMessageSystem initialized successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CNetworkMessageSystem::Initialize: " << e.what() << std::endl;
        return false;
    }
}

void CNetworkMessageSystem::Shutdown() {
    std::lock_guard<std::mutex> handlerLock(m_handlersMutex);
    std::lock_guard<std::mutex> queueLock(m_queueMutex);
    
    try {
        std::cout << "[INFO] Shutting down CNetworkMessageSystem" << std::endl;
        
        if (m_isShutdown) {
            return;
        }
        
        // Clear handlers
        m_handlers.clear();
        
        // Clear message queue
        while (!m_messageQueue.empty()) {
            m_messageQueue.pop();
        }
        
        m_isInitialized = false;
        m_isShutdown = true;
        
        std::cout << "[INFO] CNetworkMessageSystem shutdown completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CNetworkMessageSystem::Shutdown: " << e.what() << std::endl;
    }
}

bool CNetworkMessageSystem::RegisterHandler(MessageType type, MessageHandler handler) {
    std::lock_guard<std::mutex> lock(m_handlersMutex);
    
    try {
        if (!handler) {
            std::cerr << "[ERROR] Cannot register null handler" << std::endl;
            return false;
        }
        
        m_handlers[type] = std::move(handler);
        
        std::cout << "[DEBUG] Registered handler for message type: " << static_cast<int>(type) << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in RegisterHandler: " << e.what() << std::endl;
        return false;
    }
}

bool CNetworkMessageSystem::UnregisterHandler(MessageType type) {
    std::lock_guard<std::mutex> lock(m_handlersMutex);
    
    try {
        auto it = m_handlers.find(type);
        if (it != m_handlers.end()) {
            m_handlers.erase(it);
            std::cout << "[DEBUG] Unregistered handler for message type: " << static_cast<int>(type) << std::endl;
            return true;
        }
        
        return false;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in UnregisterHandler: " << e.what() << std::endl;
        return false;
    }
}

MessageProcessResult CNetworkMessageSystem::ProcessMessage(uint32_t clientId, const uint8_t* buffer, size_t size) {
    try {
        if (!m_isInitialized || m_isShutdown) {
            return MessageProcessResult::SystemError;
        }
        
        // Deserialize message
        NetworkMessage message;
        if (!message.Deserialize(buffer, size)) {
            UpdateStatistics(MessageProcessResult::InvalidMessage, size);
            return MessageProcessResult::InvalidMessage;
        }
        
        return ProcessMessage(clientId, message);
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ProcessMessage: " << e.what() << std::endl;
        UpdateStatistics(MessageProcessResult::SystemError, size);
        return MessageProcessResult::SystemError;
    }
}

MessageProcessResult CNetworkMessageSystem::ProcessMessage(uint32_t clientId, const NetworkMessage& message) {
    try {
        if (!m_isInitialized || m_isShutdown) {
            return MessageProcessResult::SystemError;
        }
        
        // Validate message
        MessageProcessResult validationResult = message.Validate();
        if (validationResult != MessageProcessResult::Success) {
            UpdateStatistics(validationResult, message.GetSize());
            return validationResult;
        }
        
        // Find handler
        MessageType type = message.GetType();
        MessageHandler handler;
        
        {
            std::lock_guard<std::mutex> lock(m_handlersMutex);
            auto it = m_handlers.find(type);
            if (it == m_handlers.end()) {
                std::cerr << "[WARNING] No handler registered for message type: " << static_cast<int>(type) << std::endl;
                UpdateStatistics(MessageProcessResult::ProcessingError, message.GetSize());
                return MessageProcessResult::ProcessingError;
            }
            handler = it->second;
        }
        
        // Process message
        MessageProcessResult result = handler(clientId, message);
        UpdateStatistics(result, message.GetSize());
        
        return result;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ProcessMessage: " << e.what() << std::endl;
        UpdateStatistics(MessageProcessResult::SystemError, message.GetSize());
        return MessageProcessResult::SystemError;
    }
}

bool CNetworkMessageSystem::QueueMessage(uint32_t clientId, const NetworkMessage& message, MessagePriority priority) {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    
    try {
        if (!m_isInitialized || m_isShutdown) {
            return false;
        }
        
        MessageQueueEntry entry(message, clientId, priority);
        m_messageQueue.push(entry);
        
        std::cout << "[DEBUG] Queued message for client " << clientId 
                 << ", type: " << static_cast<int>(message.GetType()) << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in QueueMessage: " << e.what() << std::endl;
        return false;
    }
}

bool CNetworkMessageSystem::SendMessage(uint32_t clientId, const NetworkMessage& message) {
    try {
        if (!m_isInitialized || m_isShutdown) {
            return false;
        }
        
        // Serialize message
        auto buffer = message.Serialize();
        
        // TODO: Implement actual network sending
        // This would interface with the socket system
        std::cout << "[DEBUG] Sending message to client " << clientId 
                 << ", size: " << buffer.size() << " bytes" << std::endl;
        
        // Update statistics
        m_statistics.bytesSent += buffer.size();
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in SendMessage: " << e.what() << std::endl;
        return false;
    }
}

size_t CNetworkMessageSystem::ProcessMessageQueue(size_t maxMessages) {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    
    try {
        size_t processed = 0;
        
        while (!m_messageQueue.empty() && processed < maxMessages) {
            MessageQueueEntry entry = m_messageQueue.top();
            m_messageQueue.pop();
            
            if (SendMessage(entry.clientId, entry.message)) {
                processed++;
            }
        }
        
        return processed;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in ProcessMessageQueue: " << e.what() << std::endl;
        return 0;
    }
}

void CNetworkMessageSystem::ResetStatistics() {
    std::lock_guard<std::mutex> lock(m_statisticsMutex);
    m_statistics = MessageStatistics{};
}

bool CNetworkMessageSystem::IsHandlerRegistered(MessageType type) const {
    std::lock_guard<std::mutex> lock(m_handlersMutex);
    return m_handlers.find(type) != m_handlers.end();
}

size_t CNetworkMessageSystem::GetQueueSize() const {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_messageQueue.size();
}

void CNetworkMessageSystem::ClearQueue() {
    std::lock_guard<std::mutex> lock(m_queueMutex);
    while (!m_messageQueue.empty()) {
        m_messageQueue.pop();
    }
}

bool CNetworkMessageSystem::ValidateMessageHeader(const MessageHeader& header) const {
    return header.IsValid() && 
           header.size >= MessageHeader::HEADER_SIZE &&
           header.type != MessageType::Unknown;
}

void CNetworkMessageSystem::UpdateStatistics(MessageProcessResult result, size_t messageSize) {
    std::lock_guard<std::mutex> lock(m_statisticsMutex);
    
    m_statistics.totalMessages++;
    m_statistics.bytesReceived += messageSize;
    
    switch (result) {
        case MessageProcessResult::Success:
            m_statistics.processedMessages++;
            break;
        default:
            m_statistics.failedMessages++;
            break;
    }
}

void CNetworkMessageSystem::RegisterDefaultHandlers() {
    // Register system message handler
    RegisterHandler(MessageType::SystemHeartbeat, 
        [this](uint32_t clientId, const NetworkMessage& message) {
            return HandleSystemMessage(clientId, message);
        });
    
    RegisterHandler(MessageType::SystemPing, 
        [this](uint32_t clientId, const NetworkMessage& message) {
            return HandleSystemMessage(clientId, message);
        });
    
    // Register speed hack handlers
    RegisterHandler(MessageType::SpeedHackChallenge, 
        [this](uint32_t clientId, const NetworkMessage& message) {
            return HandleSpeedHackMessage(clientId, message);
        });
    
    RegisterHandler(MessageType::SpeedHackResponse, 
        [this](uint32_t clientId, const NetworkMessage& message) {
            return HandleSpeedHackMessage(clientId, message);
        });
}

MessageProcessResult CNetworkMessageSystem::HandleSystemMessage(uint32_t clientId, const NetworkMessage& message) {
    std::cout << "[DEBUG] Handling system message from client " << clientId 
             << ", type: " << static_cast<int>(message.GetType()) << std::endl;
    
    // TODO: Implement system message handling
    return MessageProcessResult::Success;
}

MessageProcessResult CNetworkMessageSystem::HandleSpeedHackMessage(uint32_t clientId, const NetworkMessage& message) {
    std::cout << "[DEBUG] Handling speed hack message from client " << clientId 
             << ", type: " << static_cast<int>(message.GetType()) << std::endl;
    
    // TODO: Implement speed hack detection logic
    return MessageProcessResult::Success;
}

// Factory implementation
std::unique_ptr<CNetworkMessageSystem> CNetworkMessageSystemFactory::CreateMessageSystem() {
    return std::make_unique<CNetworkMessageSystem>();
}

// Utility functions
namespace MessageUtils {
    std::string MessageTypeToString(MessageType type) {
        switch (type) {
            case MessageType::Unknown: return "Unknown";
            case MessageType::SystemHeartbeat: return "SystemHeartbeat";
            case MessageType::SystemPing: return "SystemPing";
            case MessageType::SystemPong: return "SystemPong";
            case MessageType::AuthLogin: return "AuthLogin";
            case MessageType::GameEnterWorld: return "GameEnterWorld";
            case MessageType::SpeedHackChallenge: return "SpeedHackChallenge";
            default: return "Custom_" + std::to_string(static_cast<int>(type));
        }
    }
    
    std::string MessagePriorityToString(MessagePriority priority) {
        switch (priority) {
            case MessagePriority::Low: return "Low";
            case MessagePriority::Normal: return "Normal";
            case MessagePriority::High: return "High";
            case MessagePriority::Critical: return "Critical";
            case MessagePriority::System: return "System";
            default: return "Unknown";
        }
    }
}

} // namespace Network
} // namespace NexusProtection
