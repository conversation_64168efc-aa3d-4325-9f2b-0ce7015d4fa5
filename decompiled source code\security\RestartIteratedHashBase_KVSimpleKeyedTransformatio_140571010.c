/*
 * Function: ?Restart@?$IteratedHashBase@_KV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXXZ
 * Address: 0x140571010
 */

int __fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::Restart(__int64 a1)
{
  *(_QWORD *)(a1 + 24) = 0i64;
  *(_QWORD *)(a1 + 16) = 0i64;
  return (*(int (**)(void))(*(_QWORD *)a1 + 144i64))();
}
