/*
 * Function: ?pc_RenameItemNConditionCheck@CPlayer@@QEAAEPEAU_STORAGE_POS_INDIV@@PEAPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400B5D60
 */

char __fastcall CPlayer::pc_RenameItemNConditionCheck(CPlayer *this, _STORAGE_POS_INDIV *pItemInfo, _STORAGE_LIST::_db_con **ppItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int v6; // eax@14
  CPvpUserAndGuildRankingSystem *v7; // rax@16
  __int64 v8; // [sp+0h] [bp-48h]@1
  char v9; // [sp+20h] [bp-28h]@7
  _STORAGE_LIST *v10; // [sp+28h] [bp-20h]@7
  _STORAGE_LIST::_db_con *v11; // [sp+30h] [bp-18h]@7
  unsigned int v12; // [sp+38h] [bp-10h]@14
  int v13; // [sp+3Ch] [bp-Ch]@16
  CPlayer *v14; // [sp+50h] [bp+8h]@1
  _STORAGE_LIST::_db_con **v15; // [sp+60h] [bp+18h]@1

  v15 = ppItem;
  v14 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pItemInfo && !*ppItem )
  {
    v9 = 0;
    v10 = 0i64;
    v11 = 0i64;
    v10 = v14->m_Param.m_pStoragePtr[pItemInfo->byStorageCode];
    v11 = _STORAGE_LIST::GetPtrFromSerial(v10, pItemInfo->wItemSerial);
    if ( v11 )
    {
      if ( CPlayerDB::GetHP(&v14->m_Param) )
      {
        if ( v11->m_byTableCode == 13 )
        {
          if ( v14->m_Param.m_pGuild
            && (v12 = CPlayerDB::GetCharSerial(&v14->m_Param),
                v6 = CGuild::GetGuildMasterSerial(v14->m_Param.m_pGuild),
                v12 == v6) )
          {
            v9 = 7;
          }
          else
          {
            v13 = CPlayerDB::GetRaceCode(&v14->m_Param);
            v7 = CPvpUserAndGuildRankingSystem::Instance();
            if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v7, v13, v14->m_dwObjSerial) )
              v9 = 8;
            else
              *v15 = v11;
          }
        }
        else
        {
          v9 = 9;
        }
      }
      else
      {
        v9 = 2;
      }
    }
    else
    {
      v9 = 1;
    }
    result = v9;
  }
  else
  {
    result = 1;
  }
  return result;
}
