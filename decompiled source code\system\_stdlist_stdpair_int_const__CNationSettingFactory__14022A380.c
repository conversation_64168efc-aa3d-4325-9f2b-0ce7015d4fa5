/*
 * Function: _std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Buynode_::_1_::catch$0_0
 * Address: 0x14022A380
 */

void __fastcall __noreturn std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Buynode_::_1_::catch_0_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1
  std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node **v3; // rax@2

  v2 = a2;
  if ( *(_DWORD *)(a2 + 52) > 0 )
  {
    v3 = std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Nextnode(
           *(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > **)(a2 + 40),
           (std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node *)a2);
    std::allocator<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Node *>::destroy(
      (std::allocator<std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node *> *)(*(_QWORD *)(v2 + 96) + 16i64),
      v3);
  }
  std::allocator<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Node>::deallocate(
    (std::allocator<std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node> *)(*(_QWORD *)(v2 + 96) + 8i64),
    *(std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Node **)(v2 + 40),
    1ui64);
  CxxThrowException_0(0i64, 0i64);
}
