/*
 * Function: ?SendMsg_AnimusRecallResult@CPlayer@@QEAAXEGPEAVCAnimus@@@Z
 * Address: 0x1400DB8D0
 */

void __fastcall CPlayer::SendMsg_AnimusRecallResult(CPlayer *this, char byResultCode, unsigned __int16 wLeftFP, CAnimus *pNewAnimus)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned int v8; // [sp+39h] [bp-4Fh]@6
  __int16 v9; // [sp+3Dh] [bp-4Bh]@6
  __int16 v10; // [sp+3Fh] [bp-49h]@6
  unsigned __int64 v11; // [sp+41h] [bp-47h]@6
  unsigned __int16 v12; // [sp+49h] [bp-3Fh]@4
  char pbyType; // [sp+64h] [bp-24h]@7
  char v14; // [sp+65h] [bp-23h]@7
  CPlayer *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byResultCode;
  v12 = wLeftFP;
  if ( !byResultCode && pNewAnimus )
  {
    v8 = pNewAnimus->m_dwObjSerial;
    v9 = pNewAnimus->m_nHP;
    v10 = pNewAnimus->m_nFP;
    v11 = pNewAnimus->m_dwExp;
  }
  pbyType = 22;
  v14 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x13u);
}
