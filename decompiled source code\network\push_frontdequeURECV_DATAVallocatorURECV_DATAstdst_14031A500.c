/*
 * Function: ?push_front@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAAXAEBURECV_DATA@@@Z
 * Address: 0x14031A500
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::push_front(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, RECV_DATA *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned __int64 v5; // [sp+20h] [bp-28h]@9
  unsigned __int64 v6; // [sp+28h] [bp-20h]@9
  unsigned __int64 v7; // [sp+30h] [bp-18h]@7
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v8; // [sp+50h] [bp+8h]@1
  RECV_DATA *_Vala; // [sp+58h] [bp+10h]@1

  _Vala = _Val;
  v8 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->_Mapsize <= v8->_Mysize + 1 )
    std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Growmap(v8, 1ui64);
  if ( v8->_Myoff )
    v7 = v8->_Myoff;
  else
    v7 = v8->_Mapsize;
  v5 = v7 - 1;
  v6 = v7 - 1;
  if ( !v8->_Map[v7 - 1] )
    v8->_Map[v6] = std::allocator<RECV_DATA>::allocate(&v8->_Alval, 1ui64);
  std::allocator<RECV_DATA>::construct(&v8->_Alval, v8->_Map[v6], _Vala);
  v8->_Myoff = v5;
  ++v8->_Mysize;
}
