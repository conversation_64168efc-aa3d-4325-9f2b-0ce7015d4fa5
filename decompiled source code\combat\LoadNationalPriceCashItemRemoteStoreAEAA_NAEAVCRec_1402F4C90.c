/*
 * Function: ?LoadNationalPrice@CashItemRemoteStore@@AEAA_NAEAVCRecordData@@@Z
 * Address: 0x1402F4C90
 */

char __fastcall CashItemRemoteStore::LoadNationalPrice(CashItemRemoteStore *this, CRecordData *krecPrice)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char pszErrMsg; // [sp+30h] [bp-A8h]@4
  char v7; // [sp+31h] [bp-A7h]@4
  unsigned __int64 v8; // [sp+C0h] [bp-18h]@4

  v2 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  pszErrMsg = 0;
  memset(&v7, 0, 0x7Fui64);
  if ( CRecordData::ReadRecord(krecPrice, ".\\script\\CashShop_str.dat", 0x70u, &pszErrMsg) )
  {
    result = 1;
  }
  else
  {
    MyMessageBox(
      "CashDbWorker::LoadNationalPrice()",
      "ReadRecord( %s ) ErrorMsg(%s)",
      ".\\script\\CashShop_str.dat",
      &pszErrMsg);
    CLogFile::Write(
      &stru_1799C8F30,
      "CashDbWorker::LoadNationalPrice() ReadRecord(%s) ErrorMsg(%s) Failed!",
      ".\\script\\CashShop_str.dat",
      &pszErrMsg);
    result = 0;
  }
  return result;
}
