/*
 * Function: j_?AlgorithmName@?$AlgorithmImpl@VCBC_Decryption@CryptoPP@@V?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$00VDec@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Decryption@2@@2@@CryptoPP@@UEBA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x1400081A7
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>::AlgorithmName(CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption> > *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  return CryptoPP::AlgorithmImpl<CryptoPP::CBC_Decryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>,CryptoPP::CBC_Decryption>>::AlgorithmName(
           this,
           result);
}
