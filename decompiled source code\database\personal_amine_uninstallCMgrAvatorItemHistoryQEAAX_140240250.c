/*
 * Function: ?personal_amine_uninstall@CMgrAvatorItemHistory@@QEAAXEPEBKHPEAU_db_con@_STORAGE_LIST@@PEAD@Z
 * Address: 0x140240250
 */

void __fastcall CMgrAvatorItemHistory::personal_amine_uninstall(CMgrAvatorItemHistory *this, char byType, const unsigned int *pdwMineCnt, int nMaxOreNum, _STORAGE_LIST::_db_con *pItem, char *szFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@4
  char *v9; // rax@8
  size_t v10; // rax@8
  __int64 v11; // [sp+0h] [bp-58h]@1
  char *v12; // [sp+20h] [bp-38h]@4
  unsigned __int64 v13; // [sp+28h] [bp-30h]@4
  char *DstBuf; // [sp+30h] [bp-28h]@4
  int nItemIndex; // [sp+38h] [bp-20h]@4
  __int64 v16; // [sp+40h] [bp-18h]@8
  CMgrAvatorItemHistory *v17; // [sp+60h] [bp+8h]@1
  char v18; // [sp+68h] [bp+10h]@1
  const unsigned int *v19; // [sp+70h] [bp+18h]@1
  int v20; // [sp+78h] [bp+20h]@1

  v20 = nMaxOreNum;
  v19 = pdwMineCnt;
  v18 = byType;
  v17 = this;
  v6 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  sData[0] = 0;
  memset_0(sData, 0, 0x4E20ui64);
  v8 = GetItemKorName(pItem->m_byTableCode, pItem->m_wItemIndex);
  v13 = pItem->m_lnUID;
  v12 = v8;
  sprintf_s(
    sData,
    0x4E20ui64,
    "[PERSONAL_AMINE_UNINSTALL][%s] - %s[%I64u]\r\n",
    *(_QWORD *)&szUninstallDesc[8 * (unsigned __int8)v18]);
  DstBuf = &sData[strlen_0(sData)];
  for ( nItemIndex = 0; nItemIndex < v20; ++nItemIndex )
  {
    if ( v19[nItemIndex] )
    {
      v16 = nItemIndex;
      v9 = GetItemKorName(17, nItemIndex);
      LODWORD(v12) = v19[v16];
      sprintf_s(DstBuf, 20000 - (DstBuf - sData), "%s >> num:%d\r\n", v9);
      v10 = strlen_0(DstBuf);
      DstBuf += v10;
    }
  }
  CMgrAvatorItemHistory::WriteFile(v17, szFileName, sData);
}
