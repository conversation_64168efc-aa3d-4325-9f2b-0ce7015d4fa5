/*
 * Function: ?IsExchangeItem@@YAHHH@Z
 * Address: 0x140040120
 */

__int64 __fastcall IsExchangeItem(int nTableCode, int nItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-128h]@1
  CRecordData *v6; // [sp+20h] [bp-108h]@4
  _base_fld *v7; // [sp+28h] [bp-100h]@5
  _base_fld *v8; // [sp+30h] [bp-F8h]@8
  _base_fld *v9; // [sp+38h] [bp-F0h]@11
  _base_fld *v10; // [sp+40h] [bp-E8h]@14
  _base_fld *v11; // [sp+48h] [bp-E0h]@17
  _base_fld *v12; // [sp+50h] [bp-D8h]@20
  _base_fld *v13; // [sp+58h] [bp-D0h]@23
  _base_fld *v14; // [sp+60h] [bp-C8h]@26
  _base_fld *v15; // [sp+68h] [bp-C0h]@29
  _base_fld *v16; // [sp+70h] [bp-B8h]@32
  _base_fld *v17; // [sp+78h] [bp-B0h]@35
  _base_fld *v18; // [sp+80h] [bp-A8h]@38
  _base_fld *v19; // [sp+88h] [bp-A0h]@41
  _base_fld *v20; // [sp+90h] [bp-98h]@44
  _base_fld *v21; // [sp+98h] [bp-90h]@47
  _base_fld *v22; // [sp+A0h] [bp-88h]@50
  _base_fld *v23; // [sp+A8h] [bp-80h]@53
  _base_fld *v24; // [sp+B0h] [bp-78h]@56
  _base_fld *v25; // [sp+B8h] [bp-70h]@59
  _base_fld *v26; // [sp+C0h] [bp-68h]@62
  _base_fld *v27; // [sp+C8h] [bp-60h]@65
  _base_fld *v28; // [sp+D0h] [bp-58h]@68
  _base_fld *v29; // [sp+D8h] [bp-50h]@71
  _base_fld *v30; // [sp+E0h] [bp-48h]@74
  _base_fld *v31; // [sp+E8h] [bp-40h]@77
  _base_fld *v32; // [sp+F0h] [bp-38h]@80
  _base_fld *v33; // [sp+F8h] [bp-30h]@83
  _base_fld *v34; // [sp+100h] [bp-28h]@86
  _base_fld *v35; // [sp+108h] [bp-20h]@89
  int v36; // [sp+110h] [bp-18h]@4
  int v37; // [sp+130h] [bp+8h]@1

  v37 = nTableCode;
  v2 = &v5;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &s_ptblItemData[v37];
  v36 = v37;
  switch ( v37 )
  {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 7:
      v7 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v7 )
        goto LABEL_91;
      result = *(_DWORD *)&v7[6].m_strCode[36];
      break;
    case 6:
      v8 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v8 )
        goto LABEL_91;
      result = *(_DWORD *)&v8[11].m_strCode[0];
      break;
    case 8:
      v9 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v9 )
        goto LABEL_91;
      result = *(_DWORD *)&v9[5].m_strCode[36];
      break;
    case 9:
      v10 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v10 )
        goto LABEL_91;
      result = *(_DWORD *)&v10[5].m_strCode[36];
      break;
    case 10:
      v11 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v11 )
        goto LABEL_91;
      result = v11[8].m_dwIndex;
      break;
    case 11:
      v12 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v12 )
        goto LABEL_91;
      result = *(_DWORD *)&v12[4].m_strCode[60];
      break;
    case 12:
      v13 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v13 )
        goto LABEL_91;
      result = *(_DWORD *)&v13[4].m_strCode[32];
      break;
    case 13:
      v14 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v14 )
        goto LABEL_91;
      result = *(_DWORD *)&v14[8].m_strCode[20];
      break;
    case 15:
      v15 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v15 )
        goto LABEL_91;
      result = *(_DWORD *)&v15[5].m_strCode[28];
      break;
    case 16:
      v16 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v16 )
        goto LABEL_91;
      result = *(_DWORD *)&v16[4].m_strCode[36];
      break;
    case 17:
      v17 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v17 )
        goto LABEL_91;
      result = *(_DWORD *)&v17[3].m_strCode[48];
      break;
    case 18:
      v18 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v18 )
        goto LABEL_91;
      result = v18[7].m_dwIndex;
      break;
    case 19:
      v19 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v19 )
        goto LABEL_91;
      result = *(_DWORD *)&v19[4].m_strCode[0];
      break;
    case 20:
      v20 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v20 )
        goto LABEL_91;
      result = *(_DWORD *)&v20[4].m_strCode[32];
      break;
    case 21:
      v21 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v21 )
        goto LABEL_91;
      result = *(_DWORD *)&v21[5].m_strCode[28];
      break;
    case 22:
      v22 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v22 )
        goto LABEL_91;
      result = *(_DWORD *)&v22[6].m_strCode[32];
      break;
    case 23:
      v23 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v23 )
        goto LABEL_91;
      result = *(_DWORD *)&v23[7].m_strCode[60];
      break;
    case 24:
      v24 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v24 )
        goto LABEL_91;
      result = *(_DWORD *)&v24[4].m_strCode[36];
      break;
    case 25:
      v25 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v25 )
        goto LABEL_91;
      result = *(_DWORD *)&v25[10].m_strCode[8];
      break;
    case 26:
      v26 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v26 )
        goto LABEL_91;
      result = *(_DWORD *)&v26[8].m_strCode[16];
      break;
    case 27:
      v27 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v27 )
        goto LABEL_91;
      result = *(_DWORD *)&v27[6].m_strCode[0];
      break;
    case 28:
      v28 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v28 )
        goto LABEL_91;
      result = *(_DWORD *)&v28[6].m_strCode[32];
      break;
    case 30:
      v29 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v29 )
        goto LABEL_91;
      result = *(_DWORD *)&v29[4].m_strCode[48];
      break;
    case 31:
      v30 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v30 )
        goto LABEL_91;
      result = *(_DWORD *)&v30[4].m_strCode[44];
      break;
    case 32:
      v31 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v31 )
        goto LABEL_91;
      result = *(_DWORD *)&v31[4].m_strCode[52];
      break;
    case 33:
      v32 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v32 )
        goto LABEL_91;
      result = *(_DWORD *)&v32[6].m_strCode[12];
      break;
    case 34:
      v33 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v33 )
        goto LABEL_91;
      result = *(_DWORD *)&v33[6].m_strCode[24];
      break;
    case 35:
      v34 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v34 )
        goto LABEL_91;
      result = v34[6].m_dwIndex;
      break;
    case 36:
      v35 = CRecordData::GetRecord(v6, nItemIndex);
      if ( !v35 )
        goto LABEL_91;
      result = *(_DWORD *)&v35[4].m_strCode[56];
      break;
    default:
LABEL_91:
      result = 0i64;
      break;
  }
  return result;
}
