/*
 * Function: ??R?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@QEBA_NAEBQEAUScheduleMSG@@0@Z
 * Address: 0x140424240
 */

bool __fastcall stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *this, ScheduleMSG *const *_Keyval1, ScheduleMSG *const *_Keyval2)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return std::less<ScheduleMSG *>::operator()(&v7->comp, _Keyval1, _Keyval2);
}
