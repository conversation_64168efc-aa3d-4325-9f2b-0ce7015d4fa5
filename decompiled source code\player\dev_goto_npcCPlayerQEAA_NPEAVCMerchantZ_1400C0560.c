/*
 * Function: ?dev_goto_npc@CPlayer@@QEAA_NPEAVCMerchant@@@Z
 * Address: 0x1400C0560
 */

char __fastcall CPlayer::dev_goto_npc(CPlayer *this, CMerchant *pNpc)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char v5; // al@8
  __int64 v6; // [sp+0h] [bp-48h]@1
  float *pfStartPos; // [sp+20h] [bp-28h]@8
  float *v8; // [sp+30h] [bp-18h]@8
  CPlayer *v9; // [sp+50h] [bp+8h]@1
  CMerchant *v10; // [sp+58h] [bp+10h]@1

  v10 = pNpc;
  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pNpc && pNpc->m_bLive && !pNpc->m_bCorpse )
  {
    pfStartPos = pNpc->m_fCurPos;
    CPlayer::OutOfMap(v9, pNpc->m_pCurMap, pNpc->m_wMapLayerIndex, 3, pNpc->m_fCurPos);
    v8 = v10->m_fCurPos;
    v5 = CPlayerDB::GetMapCode(&v9->m_Param);
    CPlayer::SendMsg_GotoRecallResult(v9, 0, v5, v8, 4);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
