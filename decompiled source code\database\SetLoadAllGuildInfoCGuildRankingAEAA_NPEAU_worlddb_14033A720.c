/*
 * Function: ?SetLoadAllGuildInfo@CGuildRanking@@AEAA_NPEAU_worlddb_guild_info@@@Z
 * Address: 0x14033A720
 */

char __usercall CGuildRanking::SetLoadAllGuildInfo@<al>(CGuildRanking *this@<rcx>, _worlddb_guild_info *pkInfo@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-23E8h]@1
  unsigned __int16 *pwMemberCnt; // [sp+0h] [bp-23C8h]@7
  char *pwszGreetingMsg; // [sp+8h] [bp-23C0h]@11
  unsigned int dwEmblemBack; // [sp+10h] [bp-23B8h]@11
  unsigned int dwEmblemMark; // [sp+18h] [bp-23B0h]@11
  int nNum; // [sp+20h] [bp-23A8h]@11
  _guild_member_info *pEstMember; // [sp+28h] [bp-23A0h]@11
  long double v14; // [sp+30h] [bp-2398h]@11
  long double v15; // [sp+38h] [bp-2390h]@11
  unsigned int dwMasterSerial; // [sp+40h] [bp-2388h]@11
  char byMasterPrevGrade; // [sp+48h] [bp-2380h]@11
  int nIOMoneyHisNum; // [sp+50h] [bp-2378h]@11
  _io_money_data *pIOMonHisList; // [sp+58h] [bp-2370h]@11
  unsigned int dwGuildBattleTotalWinCnt; // [sp+60h] [bp-2368h]@11
  unsigned int dwGuildBattleTotalDrawCnt; // [sp+68h] [bp-2360h]@11
  unsigned int dwGuildBattleTotalLoseCnt; // [sp+70h] [bp-2358h]@11
  unsigned __int16 v23; // [sp+84h] [bp-2344h]@4
  char __t; // [sp+B0h] [bp-2318h]@4
  int pnIOMonHisNum; // [sp+A24h] [bp-19A4h]@4
  char Dst; // [sp+A50h] [bp-1978h]@8
  unsigned __int16 j; // [sp+2354h] [bp-74h]@4
  __int64 v28; // [sp+2360h] [bp-68h]@11
  __int64 v29; // [sp+2368h] [bp-60h]@11
  __int64 v30; // [sp+2370h] [bp-58h]@11
  unsigned __int64 v31; // [sp+2378h] [bp-50h]@4
  CGuildRanking *v32; // [sp+23D0h] [bp+8h]@1
  _worlddb_guild_info *v33; // [sp+23D8h] [bp+10h]@1

  v33 = pkInfo;
  v32 = this;
  v3 = alloca(a3);
  v4 = &v7;
  for ( i = 2282i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v31 = (unsigned __int64)&v7 ^ _security_cookie;
  v23 = 0;
  `vector constructor iterator'(&__t, 0x30ui64, 50, (void *(__cdecl *)(void *))_guild_member_info::_guild_member_info);
  pnIOMonHisNum = 0;
  for ( j = 0; j < (signed int)v33->wGuildCount; ++j )
  {
    v23 = 0;
    memset_0(&__t, 0, 0x30ui64);
    pwMemberCnt = &v23;
    if ( CGuildRanking::LoadMemberInfo(
           v32,
           v33->GuildData[j].dwMasterSerial,
           v33->GuildData[j].dwGuildSerial,
           (_guild_member_info *)&__t,
           &v23) )
    {
      pnIOMonHisNum = 0;
      memset_0(&Dst, 0, 0x40ui64);
      if ( CGuildRanking::LoadGuildMoneyIOInfo(
             v32,
             v33->GuildData[j].dwGuildSerial,
             (_io_money_data *)&Dst,
             &pnIOMonHisNum) )
      {
        if ( CMainThread::check_min_max_guild_money(&g_Main, v33->GuildData[j].dwGuildSerial, 0i64, 0i64) )
        {
          CLogFile::Write(
            &stru_1799C8F30,
            "CGuildRanking::SetLoadAllGuildInfo(...) : %d > g_Main.check_min_max_guild_money(...) Fail!",
            v33->GuildData[j].dwGuildSerial);
          MyMessageBox(
            "Error",
            "CGuildRanking::SetLoadAllGuildInfo(...) : \r\n%d > g_Main.check_min_max_guild_money(...) Fail!",
            v33->GuildData[j].dwGuildSerial);
          return 0;
        }
        v28 = 328i64 * j;
        pwMemberCnt = (unsigned __int16 *)&v33->GuildData[j].dGold;
        CGuildRanking::CheckMaxGuildMoney(
          v32,
          v33->GuildData[j].dwGuildSerial,
          v33->GuildData[j].wszGuildName,
          &v33->GuildData[j].dDalant,
          (long double *)pwMemberCnt);
        v29 = 328i64 * j;
        v30 = 328i64 * j;
        dwGuildBattleTotalLoseCnt = v33->GuildData[j].dwTotLose;
        dwGuildBattleTotalDrawCnt = v33->GuildData[j].dwTotDraw;
        dwGuildBattleTotalWinCnt = v33->GuildData[j].dwTotWin;
        pIOMonHisList = (_io_money_data *)&Dst;
        nIOMoneyHisNum = pnIOMonHisNum;
        byMasterPrevGrade = v33->GuildData[j].byMasterPrevGrade;
        dwMasterSerial = v33->GuildData[j].dwMasterSerial;
        v15 = v33->GuildData[j].dGold;
        v14 = v33->GuildData[j].dDalant;
        pEstMember = (_guild_member_info *)&__t;
        nNum = v23;
        dwEmblemMark = v33->GuildData[j].dwEmblemMark;
        dwEmblemBack = v33->GuildData[j].dwEmblemBack;
        pwszGreetingMsg = v33->GuildData[j].wszGreetingMsg;
        pwMemberCnt = (unsigned __int16 *)v33->GuildData[j].wszGuildName;
        CGuild::SetGuild(
          &g_Guild[j],
          v33->GuildData[j].dwGuildSerial,
          v33->GuildData[j].byGuildGrade,
          v33->GuildData[j].byRace,
          (char *)pwMemberCnt,
          pwszGreetingMsg,
          dwEmblemBack,
          dwEmblemMark,
          v23,
          (_guild_member_info *)&__t,
          v14,
          v15,
          dwMasterSerial,
          byMasterPrevGrade,
          pnIOMonHisNum,
          (_io_money_data *)&Dst,
          dwGuildBattleTotalWinCnt,
          dwGuildBattleTotalDrawCnt,
          dwGuildBattleTotalLoseCnt);
      }
    }
  }
  CLogFile::Write(&stru_1799C8F30, "Guild List(%d) Setting Complete!!", v33->wGuildCount);
  return 1;
}
