/*
 * Function: ?DBProcess@CLogTypeDBTaskManager@@QEAAXXZ
 * Address: 0x1402C3130
 */

void __fastcall CLogTypeDBTaskManager::DBProcess(CLogTypeDBTaskManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CWeeklyGuildRankManager *v3; // rax@10
  CUnmannedTraderController *v4; // rax@13
  char *v5; // rax@14
  int v6; // eax@23
  __int64 v7; // [sp+0h] [bp-98h]@1
  CLogTypeDBTask *pTask; // [sp+20h] [bp-78h]@4
  unsigned __int8 v9; // [sp+28h] [bp-70h]@4
  char byProcRet; // [sp+34h] [bp-64h]@4
  char v11; // [sp+44h] [bp-54h]@4
  const char *v12; // [sp+58h] [bp-40h]@20
  const char *v13; // [sp+60h] [bp-38h]@20
  const char *v14; // [sp+68h] [bp-30h]@20
  int v15; // [sp+74h] [bp-24h]@6
  char *pData; // [sp+78h] [bp-20h]@10
  char *v17; // [sp+80h] [bp-18h]@13
  const char *v18; // [sp+88h] [bp-10h]@21
  CLogTypeDBTaskManager *v19; // [sp+A0h] [bp+8h]@1

  v19 = this;
  v1 = &v7;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pTask = 0i64;
  v9 = 0;
  byProcRet = 0;
  v11 = 0;
  while ( 1 )
  {
    pTask = CLogTypeDBTaskPool::GetProc(&v19->m_kPool);
    if ( !pTask )
      break;
    v15 = CLogTypeDBTask::GetQueryType(pTask);
    if ( v15 )
    {
      if ( v15 == 1 )
      {
        v17 = CLogTypeDBTask::GetData(pTask);
        v4 = CUnmannedTraderController::Instance();
        v9 = CUnmannedTraderController::SelectSearchList(v4, v17, v19->m_pkWorldDB, &byProcRet);
      }
      else if ( v15 == 2 )
      {
        v5 = CLogTypeDBTask::GetData(pTask);
        if ( !CPotionMgr::InsertRenamePotion(&g_PotionMgr, v19->m_pkWorldDB, v5) )
          v9 = 1;
      }
    }
    else
    {
      pData = CLogTypeDBTask::GetData(pTask);
      v3 = CWeeklyGuildRankManager::Instance();
      if ( !CWeeklyGuildRankManager::InsertSettlementOwner(v3, v19->m_pkWorldDB, pData) )
        v9 = 1;
    }
    if ( CLogTypeDBTask::GetQueryType(pTask) == -1 || v9 == 1 || v11 && v9 == 2 )
    {
      v12 = "db_result_sql_error";
      v13 = "db_result_sql_success";
      v14 = "db_result_no_data";
      if ( CLogTypeDBTask::GetQueryType(pTask) == -1 )
        v18 = "lt_qry_case_none";
      else
        v18 = (&v12)[8 * v9];
      v6 = CLogTypeDBTask::GetQueryType(pTask);
      CLogTypeDBTaskManager::Log(
        v19,
        "CLogTypeDBTaskManager::DBProcess() : case: %d,  ret: %s Fail!",
        (unsigned int)v6,
        v18);
    }
    CLogTypeDBTask::SetRet(pTask, v9, byProcRet);
    CLogTypeDBTaskPool::SetComplete(&v19->m_kPool, pTask, v19->m_pkLogger);
  }
}
