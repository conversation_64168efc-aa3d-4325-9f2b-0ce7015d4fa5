/*
 * Function: ?add_storage_fail@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEBDPEAD@Z
 * Address: 0x140240B40
 */

void __fastcall CMgrAvatorItemHistory::add_storage_fail(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, const char *strErrorCodePos, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v7; // eax@4
  unsigned int v8; // ecx@4
  __int64 v9; // [sp+0h] [bp-38h]@1
  const char *v10; // [sp+20h] [bp-18h]@4
  CMgrAvatorItemHistory *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v5 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = pItem->m_wItemIndex;
  v8 = pItem->m_byTableCode;
  v10 = strErrorCodePos;
  sprintf_s<20000>((char (*)[20000])sData, "Amb_AddStorage ERR - item:[%d-%d], CodePos:(%s) \r\n", v8, v7);
  CMgrAvatorItemHistory::WriteFile(v11, pszFileName, sData);
}
