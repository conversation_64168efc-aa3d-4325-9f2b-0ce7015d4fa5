/*
 * Function: ?IsValidID@CUnmannedTraderClassInfoTableType@@IEAA_NK@Z
 * Address: 0x14037DA00
 */

char __fastcall CUnmannedTraderClassInfoTableType::IsValidID(CUnmannedTraderClassInfoTableType *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  CUnmannedTraderSubClassInfo **v5; // rax@10
  __int64 v6; // [sp+0h] [bp-A8h]@1
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > result; // [sp+28h] [bp-80h]@8
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > v8; // [sp+48h] [bp-60h]@12
  bool v9; // [sp+60h] [bp-48h]@9
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > v10; // [sp+68h] [bp-40h]@9
  char v11; // [sp+80h] [bp-28h]@11
  char v12; // [sp+81h] [bp-27h]@13
  __int64 v13; // [sp+88h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v14; // [sp+90h] [bp-18h]@9
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Right; // [sp+98h] [bp-10h]@9
  CUnmannedTraderClassInfoTableType *v16; // [sp+B0h] [bp+8h]@1
  unsigned int v17; // [sp+B8h] [bp+10h]@1

  v17 = dwID;
  v16 = this;
  v2 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = -2i64;
  if ( dwID == -1 )
  {
    v4 = 0;
  }
  else if ( std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&v16->m_vecSubClass) )
  {
    v4 = 1;
  }
  else
  {
    std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::begin(
      &v16->m_vecSubClass,
      &result);
    while ( 1 )
    {
      v14 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(
              &v16->m_vecSubClass,
              &v10);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)v14;
      v9 = std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator!=(
             (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&result._Mycont,
             (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v14->_Mycont);
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v10);
      if ( !v9 )
        break;
      v5 = std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator*(&result);
      if ( v17 == CUnmannedTraderSubClassInfo::GetID(*v5) )
      {
        v11 = 0;
        std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
        return v11;
      }
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator++(
        &result,
        &v8,
        0);
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v8);
    }
    v12 = 1;
    std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
    v4 = v12;
  }
  return v4;
}
