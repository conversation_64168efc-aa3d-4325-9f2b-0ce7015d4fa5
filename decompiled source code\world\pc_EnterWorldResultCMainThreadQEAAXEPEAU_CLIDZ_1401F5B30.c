/*
 * Function: ?pc_EnterWorldResult@CMainThread@@QEAAXEPEAU_CLID@@@Z
 * Address: 0x1401F5B30
 */

void __fastcall CMainThread::pc_EnterWorldResult(CMainThread *this, char byRetCode, _CLID *pidWorld)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  CUserDB *v6; // [sp+30h] [bp-18h]@5

  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( byRetCode )
  {
    v6 = &g_UserDB[pidWorld->wIndex];
    if ( v6->m_bActive )
    {
      if ( v6->m_idWorld.dwSerial == pidWorld->dwSerial )
        CUserDB::ForceCloseCommand(v6, 1, 0xFFFFFFFF, 0, "Enter world False");
    }
  }
}
