/*
 * Function: ?ATradeAdjustPriceRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D38E0
 */

char __fastcall CNetworkEX::ATradeAdjustPriceRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CUnmannedTraderController *v6; // rax@7
  __int64 v7; // [sp+0h] [bp-38h]@1
  _a_trade_adjust_price_request_clzo *pRequest; // [sp+20h] [bp-18h]@4
  CPlayer *v9; // [sp+28h] [bp-10h]@4
  int v10; // [sp+48h] [bp+10h]@1

  v10 = n;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pRequest = (_a_trade_adjust_price_request_clzo *)pBuf;
  v9 = &g_Player + n;
  if ( v9->m_bOper && !v9->m_pmTrd.bDTradeMode )
  {
    v6 = CUnmannedTraderController::Instance();
    CUnmannedTraderController::ModifyPrice(v6, v10, pRequest);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
