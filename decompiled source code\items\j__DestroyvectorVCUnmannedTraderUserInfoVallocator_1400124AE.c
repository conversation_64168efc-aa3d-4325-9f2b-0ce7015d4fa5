/*
 * Function: j_?_Destroy@?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@IEAAXPEAVCUnmannedTraderUserInfo@@0@Z
 * Address: 0x1400124AE
 */

void __fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Destroy(std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last)
{
  std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Destroy(this, _First, _Last);
}
