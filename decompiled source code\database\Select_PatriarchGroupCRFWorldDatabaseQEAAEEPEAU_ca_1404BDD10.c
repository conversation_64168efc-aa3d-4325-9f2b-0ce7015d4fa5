/*
 * Function: ?Select_PatriarchGroup@CRFWorldDatabase@@QEAAEEPEAU_candidate_info@@@Z
 * Address: 0x1404BDD10
 */

char __fastcall CRFWorldDatabase::Select_PatriarchGroup(CRFWorldDatabase *this, char byRace, _candidate_info *p)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  PatriarchElectProcessor *v6; // rax@25
  __int64 v7; // [sp+0h] [bp-4A8h]@1
  void *SQLStmt; // [sp+20h] [bp-488h]@4
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-480h]@4
  SQLLEN v10; // [sp+38h] [bp-470h]@19
  __int16 v11; // [sp+44h] [bp-464h]@9
  char _Dest[1024]; // [sp+60h] [bp-448h]@4
  int j; // [sp+464h] [bp-44h]@14
  unsigned int TargetValue; // [sp+474h] [bp-34h]@19
  unsigned __int64 v15; // [sp+490h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+4B0h] [bp+8h]@1
  _candidate_info *v17; // [sp+4C0h] [bp+18h]@1

  v17 = p;
  v16 = this;
  v3 = &v7;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  LODWORD(StrLen_or_IndPtr) = 4;
  SQLStmt = byte_1799C5B78;
  sprintf_s<1024>(
    (char (*)[1024])_Dest,
    "SELECT ESerial, Race, Rank, Lv, PvpPoint, ASerial, AName, GSerial, GName, WinCnt, Score, ClassType, State FROM [dbo]"
    ".[tbl_patriarch_candidate] WHERE race = %d AND ClassType < %d AND eSerial = (Select TOP 1 Serial From tbl_patriarch_"
    "elect Where WorldName = '%s' AND ProcType >= %d Order by Serial DESC) ORDER BY ClassType ASC",
    (unsigned __int8)byRace,
    9i64);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, _Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirectA_0(v16->m_hStmtSelect, _Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, _Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      for ( j = 0; ; ++j )
      {
        if ( j >= 9 )
          goto LABEL_33;
        v11 = SQLFetch_0(v16->m_hStmtSelect);
        if ( v11 )
        {
          if ( v11 != 1 )
            goto LABEL_33;
        }
        if ( !j )
        {
          TargetValue = 0;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, 4, &TargetValue, 0i64, &v10);
          if ( v11 && v11 != 1 )
          {
            if ( v11 != 100 )
            {
              SQLStmt = v16->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, _Dest, "SQLExecDirectA", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
              if ( v16->m_hStmtSelect )
                SQLCloseCursor_0(v16->m_hStmtSelect);
              return 1;
            }
            goto LABEL_33;
          }
          v6 = PatriarchElectProcessor::Instance();
          PatriarchElectProcessor::SetCurrPatriarchElectSerial(v6, TargetValue);
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 2u, -6, &v17[j].byRace, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 3u, 4, &v17[j].dwRank, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 4u, -6, &v17[j].byLevel, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 5u, 8, &v17[j].dPvpPoint, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 6u, 4, &v17[j].dwAvatorSerial, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 7u, 1, v17[j].wszName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 8u, 4, &v17[j].dwGuildSerial, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 9u, 1, v17[j].wszGuildName, 17i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 0xAu, 4, &v17[j].dwWinCnt, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 0xBu, 4, &v17[j].dwScore, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 0xCu, -6, &v17[j].eClassType, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 0xDu, -6, &v17[j].eStatus, 0i64, &v10);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
        v17[j].bLoad = 1;
      }
      if ( v11 == 100 )
      {
LABEL_33:
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        if ( v16->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", _Dest);
        if ( v11 != 100 || j )
          result = 0;
        else
          result = 2;
        return result;
      }
      SQLStmt = v16->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, _Dest, "SQLExecDirectA", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
      if ( v16->m_hStmtSelect )
        SQLCloseCursor_0(v16->m_hStmtSelect);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1;
  }
  return result;
}
