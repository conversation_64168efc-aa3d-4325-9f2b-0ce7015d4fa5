/*
 * Function: _std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::erase_::_1_::dtor$2
 * Address: 0x14038FC90
 */

void __fastcall std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(*(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > **)(a2 + 88));
  }
}
