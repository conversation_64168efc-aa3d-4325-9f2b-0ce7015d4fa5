/*
 * Function: ?SearchNearPlayer@CMonster@@QEAAPEAVCCharacter@@XZ
 * Address: 0x14026F340
 */

CPlayer *__fastcall CMonster::SearchNearPlayer(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonster *pMon; // [sp+30h] [bp+8h]@1

  pMon = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return CMonsterHelper::SearchNearPlayer(pMon, 0);
}
