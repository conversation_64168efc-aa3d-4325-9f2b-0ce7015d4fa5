#pragma once

/**
 * @file Authentication.h
 * @brief Main header for the NexusProtection Authentication Module
 * 
 * This header provides access to all authentication-related functionality including:
 * - User authentication and session management
 * - Billing system integration
 * - Asynchronous logging operations
 * - Anti-cheat and security verification
 * - User database management
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

// Core authentication types and enumerations
#include "AuthenticationTypes.h"

// Main authentication classes
#include "CBillingManager.h"
#include "CAsyncLogInfo.h"
#include "CHackShieldExSystem.h"
#include "CUserDB.h"

namespace NexusProtection::Authentication {

    /**
     * @brief Initialize the entire authentication module
     * 
     * This function initializes all authentication subsystems including:
     * - Billing manager
     * - User database
     * - Async logging system
     * - Security verification system
     * 
     * @return true if initialization successful, false otherwise
     */
    bool InitializeAuthenticationModule();

    /**
     * @brief Shutdown the authentication module
     * 
     * Properly shuts down all authentication subsystems and saves any pending data.
     */
    void ShutdownAuthenticationModule();

    /**
     * @brief Update the authentication module
     * 
     * Called periodically to update authentication systems, process logs,
     * verify sessions, and handle any pending operations.
     * 
     * @param deltaTime Time elapsed since last update
     */
    void UpdateAuthenticationModule(std::chrono::milliseconds deltaTime);

    /**
     * @brief Authenticate a user with credentials
     * 
     * @param accountName User account name
     * @param password User password
     * @param clientIP Client IP address
     * @param clientPort Client port
     * @return Authentication result and session ID if successful
     */
    std::pair<AuthenticationResult, uint32_t> AuthenticateUser(
        const std::string& accountName, 
        const std::string& password,
        const std::string& clientIP = "",
        uint16_t clientPort = 0);

    /**
     * @brief Create a new user session
     * 
     * @param accountInfo Account information
     * @param clientIP Client IP address
     * @param clientPort Client port
     * @return Session ID if successful, 0 if failed
     */
    uint32_t CreateUserSession(const AccountInfo& accountInfo, 
                              const std::string& clientIP, 
                              uint16_t clientPort);

    /**
     * @brief Destroy a user session
     * 
     * @param sessionId Session ID to destroy
     * @return true if successful, false otherwise
     */
    bool DestroyUserSession(uint32_t sessionId);

    /**
     * @brief Validate a user session
     * 
     * @param sessionId Session ID to validate
     * @return true if session is valid and active, false otherwise
     */
    bool ValidateUserSession(uint32_t sessionId);

    /**
     * @brief Get module version information
     * 
     * @return Version string in format "Major.Minor.Patch"
     */
    const char* GetAuthenticationModuleVersion();

    /**
     * @brief Get module build information
     * 
     * @return Build information string
     */
    const char* GetAuthenticationModuleBuildInfo();

    // Convenience access to main systems
    inline CBillingManager& GetBillingManager() {
        return NexusProtection::Authentication::GetBillingManager();
    }

    inline CAsyncLogger& GetAsyncLogger() {
        return NexusProtection::Authentication::GetAsyncLogger();
    }

    inline CHackShieldExSystem& GetHackShieldExSystem() {
        return NexusProtection::Authentication::GetHackShieldExSystem();
    }

    inline CUserDB& GetUserDB() {
        return NexusProtection::Authentication::GetUserDB();
    }

    /**
     * @brief Authentication module configuration
     */
    struct AuthenticationConfig {
        std::string databasePath{"./Database/"};
        std::string logPath{"./Logs/"};
        std::string configPath{"./Config/"};
        bool enableBilling{true};
        bool enableLogging{true};
        bool enableSecurity{true};
        bool enableAntiCheat{true};
        SecurityLevel defaultSecurityLevel{SecurityLevel::Basic};
        std::chrono::minutes sessionTimeout{30};
        uint32_t maxLoginAttempts{3};
        
        void SetDefaults() {
            databasePath = "./Database/";
            logPath = "./Logs/";
            configPath = "./Config/";
            enableBilling = true;
            enableLogging = true;
            enableSecurity = true;
            enableAntiCheat = true;
            defaultSecurityLevel = SecurityLevel::Basic;
            sessionTimeout = std::chrono::minutes{30};
            maxLoginAttempts = 3;
        }
    };

    /**
     * @brief Get current authentication configuration
     * 
     * @return Reference to current configuration
     */
    const AuthenticationConfig& GetAuthenticationConfig();

    /**
     * @brief Set authentication configuration
     * 
     * @param config New configuration to apply
     * @return true if configuration applied successfully, false otherwise
     */
    bool SetAuthenticationConfig(const AuthenticationConfig& config);

    /**
     * @brief Load authentication configuration from file
     * 
     * @param configPath Path to configuration file
     * @return true if loaded successfully, false otherwise
     */
    bool LoadAuthenticationConfig(const std::string& configPath = "");

    /**
     * @brief Save authentication configuration to file
     * 
     * @param configPath Path to configuration file
     * @return true if saved successfully, false otherwise
     */
    bool SaveAuthenticationConfig(const std::string& configPath = "");

} // namespace NexusProtection::Authentication

// Legacy C interface for backward compatibility
extern "C" {
    /**
     * @brief Initialize authentication module (C interface)
     * @return 1 if successful, 0 if failed
     */
    int InitializeAuthentication();

    /**
     * @brief Shutdown authentication module (C interface)
     */
    void ShutdownAuthentication();

    /**
     * @brief Update authentication module (C interface)
     * @param deltaTimeMs Delta time in milliseconds
     */
    void UpdateAuthentication(unsigned int deltaTimeMs);

    /**
     * @brief Authenticate user (C interface)
     * @param accountName Account name
     * @param password Password
     * @param sessionId Output session ID
     * @return Authentication result code
     */
    int AuthenticateUser_C(const char* accountName, const char* password, unsigned int* sessionId);

    /**
     * @brief Validate session (C interface)
     * @param sessionId Session ID to validate
     * @return 1 if valid, 0 if invalid
     */
    int ValidateSession_C(unsigned int sessionId);

    /**
     * @brief Destroy session (C interface)
     * @param sessionId Session ID to destroy
     * @return 1 if successful, 0 if failed
     */
    int DestroySession_C(unsigned int sessionId);

    /**
     * @brief Get authentication module version (C interface)
     * @return Version string
     */
    const char* GetAuthenticationVersion();
}

// Global legacy compatibility macros
#define AUTHENTICATION_MODULE_VERSION "1.0.0"
#define AUTHENTICATION_MODULE_BUILD_DATE __DATE__ " " __TIME__

// Legacy global variables for compatibility
extern NexusProtection::Authentication::CBillingManager* g_pBillingManager;
extern NexusProtection::Authentication::CAsyncLogger* g_pAsyncLogger;
extern NexusProtection::Authentication::CHackShieldExSystem* g_pHackShieldExSystem;
extern NexusProtection::Authentication::CUserDB* g_pUserDB;

// Legacy function macros for easy migration
#define BillingManager_Instance() (&NexusProtection::Authentication::GetBillingManager())
#define AsyncLogger_Instance() (&NexusProtection::Authentication::GetAsyncLogger())
#define HackShieldExSystem_Instance() (&NexusProtection::Authentication::GetHackShieldExSystem())
#define UserDB_Instance() (&NexusProtection::Authentication::GetUserDB())

// Documentation for module usage
/**
 * @page AuthenticationModuleUsage Authentication Module Usage Guide
 * 
 * @section overview Overview
 * The Authentication Module provides comprehensive user authentication and security management 
 * for the NexusProtection game server.
 * 
 * @section initialization Initialization
 * @code{.cpp}
 * // Initialize the authentication module
 * if (!NexusProtection::Authentication::InitializeAuthenticationModule()) {
 *     // Handle initialization failure
 *     return false;
 * }
 * @endcode
 * 
 * @section user_auth User Authentication
 * @code{.cpp}
 * // Authenticate a user
 * auto [result, sessionId] = NexusProtection::Authentication::AuthenticateUser(
 *     "username", "password", "*************", 12345);
 * 
 * if (result == NexusProtection::Authentication::AuthenticationResult::Success) {
 *     // User authenticated successfully
 *     std::cout << "Session ID: " << sessionId << std::endl;
 * }
 * @endcode
 * 
 * @section logging Logging Operations
 * @code{.cpp}
 * // Write to authentication log
 * auto& logger = NexusProtection::Authentication::GetAsyncLogger();
 * logger.WriteAuthLog("User login attempt from IP: *************");
 * @endcode
 * 
 * @section security Security Verification
 * @code{.cpp}
 * // Verify client security
 * auto& security = NexusProtection::Authentication::GetHackShieldExSystem();
 * auto result = security.OnCheckSession_FirstVerify(sessionId, clientData);
 * 
 * if (result.isValid) {
 *     // Client verification successful
 * }
 * @endcode
 * 
 * @section legacy_compatibility Legacy Compatibility
 * The module maintains full compatibility with existing C code through wrapper functions
 * and global variables. Existing code should continue to work without modification.
 */
