/*
 * Function: ?GetLooter@CLootingMgr@@QEAAPEAVCPlayer@@PEAVCMapData@@PEAMPEAV2@@Z
 * Address: 0x14014C830
 */

CPlayer *__fastcall CLootingMgr::GetLooter(CLootingMgr *this, CMapData *pMap, float *pPos, CPlayer *pSkipObject)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  double v6; // xmm0_8@6
  __int64 v8; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@4
  double v10; // [sp+28h] [bp-40h]@6
  CLootingMgr::_list *v11; // [sp+30h] [bp-38h]@6
  int k; // [sp+38h] [bp-30h]@6
  CLootingMgr::_list *v13; // [sp+40h] [bp-28h]@9
  double v14; // [sp+48h] [bp-20h]@13
  bool v15; // [sp+50h] [bp-18h]@11
  CLootingMgr *v16; // [sp+70h] [bp+8h]@1
  CPlayer *v17; // [sp+88h] [bp+20h]@1

  v17 = pSkipObject;
  v16 = this;
  v4 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  for ( j = 0; j < v16->m_byUserNode; ++j )
  {
    v6 = 0.0;
    v10 = 0.0;
    v11 = 0i64;
    for ( k = 0; k < v16->m_byUserNode; ++k )
    {
      v13 = &v16->m_AtterList[k];
      if ( v13->pAtter )
      {
        if ( k )
          v15 = 0;
        else
          v15 = v16->m_bFirst;
        CLootingMgr::_list::GetScore(v13, v15);
        v14 = v6;
        if ( v6 > v10 )
        {
          v6 = v14;
          v10 = v14;
          v11 = v13;
        }
      }
    }
    if ( !v11 )
      return 0i64;
    if ( v11->pAtter == v17 )
      return 0i64;
    if ( v11->pAtter->m_bLive && v11->pAtter->m_dwObjSerial == v11->dwAtterSerial )
      return v11->pAtter;
    v11->pAtter = 0i64;
  }
  return 0i64;
}
