/*
 * Function: ?Insert_OreReset_Log@CRFWorldDatabase@@QEAA_NEHKK@Z
 * Address: 0x1404C9900
 */

bool __fastcall CRFWorldDatabase::Insert_OreReset_Log(CRFWorldDatabase *this, char byType, int nLiveUsercnt, unsigned int dwOreRemain, unsigned int dwTAmount)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-1A8h]@1
  int v9; // [sp+20h] [bp-188h]@4
  unsigned int v10; // [sp+28h] [bp-180h]@4
  unsigned int v11; // [sp+30h] [bp-178h]@4
  char Dest; // [sp+50h] [bp-158h]@4
  char szDateTime; // [sp+168h] [bp-40h]@4
  unsigned __int64 v14; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+1B0h] [bp+8h]@1
  char v16; // [sp+1B8h] [bp+10h]@1
  int v17; // [sp+1C0h] [bp+18h]@1
  unsigned int v18; // [sp+1C8h] [bp+20h]@1

  v18 = dwOreRemain;
  v17 = nLiveUsercnt;
  v16 = byType;
  v15 = this;
  v5 = &v8;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v14 = (unsigned __int64)&v8 ^ _security_cookie;
  GetNowDateTime(&szDateTime);
  v11 = dwTAmount;
  v10 = v18;
  v9 = v17;
  sprintf(&Dest, "{CALL pInsert_RemainOre_Log (%d,'%s',%d,%d,%d)}", (unsigned __int8)v16, &szDateTime);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v15->vfptr, &Dest, 1);
}
