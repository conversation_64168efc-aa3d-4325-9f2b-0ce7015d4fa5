/*
 * Function: ?_check_buyitem@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@EPEBU__item@_request_csi_buy_clzo@@PEBU_CashShop_fld@@@Z
 * Address: 0x1402F4FA0
 */

signed __int64 __fastcall CashItemRemoteStore::_check_buyitem(CashItemRemoteStore *this, char byRaceSex, _request_csi_buy_clzo::__item *pCsItem, _CashShop_fld *pFld)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  char v7; // al@25
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *v9; // [sp+20h] [bp-28h]@8
  _base_fld *v10; // [sp+28h] [bp-20h]@10
  int j; // [sp+30h] [bp-18h]@30
  int v12; // [sp+34h] [bp-14h]@25
  CashItemRemoteStore *v13; // [sp+50h] [bp+8h]@1
  char v14; // [sp+58h] [bp+10h]@1
  _request_csi_buy_clzo::__item *v15; // [sp+60h] [bp+18h]@1
  _CashShop_fld *v16; // [sp+68h] [bp+20h]@1

  v16 = pFld;
  v15 = pCsItem;
  v14 = byRaceSex;
  v13 = this;
  v4 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( !pCsItem || !pFld || pCsItem->byTblCode >= 37 )
    return 4i64;
  v9 = GetItemEquipCivil(pCsItem->byTblCode, pCsItem->wItemIdx);
  if ( v9[(unsigned __int8)v14] != 49 )
    return 12i64;
  v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v15->byTblCode, v15->wItemIdx);
  if ( !v10 || !v16 )
    return 11i64;
  if ( !v16->m_bView )
    return 11i64;
  if ( strncmp(v16->m_strCsItemCode, v10->m_strCode, 7ui64) )
    return 11i64;
  if ( v15->byOverlapNum > 99 )
    return 6i64;
  if ( v15->byOverlapNum > 1 && !IsOverLapItem(v15->byTblCode) )
    return 6i64;
  if ( v16->m_nCsPrice != v15->nPrice )
    return 5i64;
  if ( v15->byEventType == 5 )
  {
    v12 = v15->byDiscount;
    v7 = CashItemRemoteStore::GetLimDiscout(v13);
    if ( v12 != (unsigned __int8)v7 )
      return 7i64;
  }
  else if ( LOBYTE(v16->m_nCsDiscount) != v15->byDiscount )
  {
    return 7i64;
  }
  for ( j = 0; j < 8; ++j )
  {
    if ( v16->m_nCsEvent[j] != v15->nEvent[j] )
      return 18i64;
  }
  if ( v15->byEventType != 5 || CashItemRemoteStore::BuyLimSale(v13, v15->byTblCode, v15->wItemIdx) )
    result = 0i64;
  else
    result = 21i64;
  return result;
}
