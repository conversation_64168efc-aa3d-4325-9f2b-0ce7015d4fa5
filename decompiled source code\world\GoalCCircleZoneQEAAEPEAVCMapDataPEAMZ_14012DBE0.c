/*
 * Function: ?Goal@CCircleZone@@QEAAEPEAVCMapData@@PEAM@Z
 * Address: 0x14012DBE0
 */

char __fastcall CCircleZone::Goal(CCircleZone *this, CMapData *pkMap, float *pfCurPos)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v6; // [sp+0h] [bp-28h]@1
  CCircleZone *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkMap && v7->m_eState != 1 )
  {
    if ( CCircleZone::IsNearPosition(v7, pfCurPos) )
    {
      CCircleZone::SendMsgGoal(v7);
      result = 0;
    }
    else
    {
      result = -122;
    }
  }
  else
  {
    result = 110;
  }
  return result;
}
