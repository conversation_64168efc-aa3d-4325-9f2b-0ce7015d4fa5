/*
 * Function: ?GetRecord@CEventLootTable@@QEAAPEAU_event_drop@1@PEBD@Z
 * Address: 0x140136C80
 */

CEventLootTable::_event_drop *__fastcall CEventLootTable::GetRecord(CEventLootTable *this, const char *szRecordCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *Str1; // [sp+20h] [bp-18h]@4
  CEventLootTable *v7; // [sp+40h] [bp+8h]@1
  char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = (char *)szRecordCode;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( Str1 = v7->m_pEventDropList->strCode; Str1; Str1 = (char *)*((_QWORD *)Str1 + 9) )
  {
    if ( !strcmp_0(Str1, Str2) )
      return (CEventLootTable::_event_drop *)Str1;
  }
  return 0i64;
}
