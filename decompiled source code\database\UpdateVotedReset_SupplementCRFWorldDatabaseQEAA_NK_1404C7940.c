/*
 * Function: ?UpdateVotedReset_Supplement@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404C7940
 */

bool __fastcall CRFWorldDatabase::UpdateVotedReset_Supplement(CRFWorldDatabase *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-458h]@1
  char DstBuf; // [sp+30h] [bp-428h]@4
  char v7; // [sp+31h] [bp-427h]@4
  unsigned __int64 v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0x3FFui64);
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "Update [dbo].[tbl_Supplement] Set IsVoted = 0, VoteEnable = 1 From [dbo].[tbl_Supplement] as s join [dbo].[tbl_base]"
    " as b on s.serial = b.serial Where b.DCK = 0 AND b.accountserial = (select accountserial from tbl_base where serial =%d)",
    dwSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 1);
}
