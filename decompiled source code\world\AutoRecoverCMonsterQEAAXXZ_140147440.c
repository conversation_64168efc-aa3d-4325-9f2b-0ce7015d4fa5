/*
 * Function: ?AutoRecover@CMonster@@QEAAXXZ
 * Address: 0x140147440
 */

void __usercall CMonster::AutoRecover(CMonster *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@4
  int v6; // [sp+24h] [bp-24h]@4
  int v7; // [sp+28h] [bp-20h]@8
  CGameObjectVtbl *v8; // [sp+30h] [bp-18h]@11
  CMonster *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = ((int (__fastcall *)(CMonster *))v9->vfptr->GetHP)(v9);
  v6 = 0;
  _effect_parameter::GetEff_Plus(&v9->m_EP, 32);
  if ( a2 != 0.0 )
  {
    _effect_parameter::GetEff_Plus(&v9->m_EP, 32);
    v6 = (signed int)ffloor((float)v6 + a2);
  }
  if ( v6 )
  {
    if ( v6 < 0 )
    {
      v7 = ((int (__fastcall *)(CMonster *))v9->vfptr->GetMaxHP)(v9) / 10;
      if ( v6 + v5 <= v7 )
        v6 = 0;
    }
    if ( v6 )
    {
      v8 = v9->vfptr;
      ((void (__fastcall *)(CMonster *, _QWORD, _QWORD))v8->SetHP)(v9, (unsigned int)(v6 + v5), 0i64);
    }
  }
}
