/*
 * Function: ?dtor$0@?0??OnDrawMenuShadow@CMFCVisualManagerOfficeXP@@MEAAXPEAVCDC@@AEBVCRect@@1HHHPEAVCBitmap@@2H@Z@4HA_1
 * Address: 0x1405657D0
 */

// Microsoft VisualC v7/11 64bit runtime
void __fastcall `CMFCVisualManagerOfficeXP::OnDrawMenuShadow'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>((CryptoPP::Sec<PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(a2 + 80));
}
