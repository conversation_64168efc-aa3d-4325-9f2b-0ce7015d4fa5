/*
 * Function: j_?CreateRespawnMonster@@YAPEAVCMonster@@PEAVCMapData@@GHPEAU_mon_active@@PEAU_dummy_position@@_N3333@Z
 * Address: 0x14000BFDC
 */

CMonster *__fastcall CreateRespawnMonster(CMapData *pMap, unsigned __int16 wLayer, int nMonsterIndex, _mon_active *pActiveRec, _dummy_position *pDumPosition, bool bRobExp, bool bRewardExp, bool bDungeon, bool bWithoutFail, bool bApplyRopExpField)
{
  return CreateRespawnMonster(
           pMap,
           wLayer,
           nMonsterIndex,
           pActiveRec,
           pDumPosition,
           bRobExp,
           bRewardExp,
           bDungeon,
           bWithoutFail,
           bApplyRopExpField);
}
