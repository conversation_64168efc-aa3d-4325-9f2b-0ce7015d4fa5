/*
 * Function: _CUnmannedTraderUserInfo::CUnmannedTraderUserInfo_::_1_::dtor$0
 * Address: 0x1403530D0
 */

void __fastcall CUnmannedTraderUserInfo::CUnmannedTraderUserInfo_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CUnmannedTraderRequestLimiter::~CUnmannedTraderRequestLimiter((CUnmannedTraderRequestLimiter *)(*(_QWORD *)(a2 + 64)
                                                                                                + 16i64));
}
