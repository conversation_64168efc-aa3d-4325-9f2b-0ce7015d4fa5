/*
 * Function: j_??$_Uninit_fill_n@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_KV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@std@@YAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@0@_KAEBV10@AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000ECBE
 */

void __fastcall std::_Uninit_fill_n<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_First, unsigned __int64 _Count, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Val, std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
