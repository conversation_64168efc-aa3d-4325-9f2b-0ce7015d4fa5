/*
 * Function: ?GetPopPartyMember@CPartyPlayer@@QEAAHXZ
 * Address: 0x140044EB0
 */

__int64 __fastcall CPartyPlayer::GetPopPartyMember(CPartyPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@6
  int j; // [sp+24h] [bp-14h]@6
  CPartyPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v7) )
  {
    v5 = 0;
    for ( j = 0; j < 8 && v7->m_pPartyBoss->m_pPartyMember[j]; ++j )
      ++v5;
    result = v5;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
