/*
 * Function: _std::_Uninit_fill_n_void_(__cdecl_CUserRankingProcess::_____ptr64)(void)___ptr64_unsigned___int64_void_(__cdecl_CUserRankingProcess::_)(void)___ptr64_std::allocator_void_(__cdecl_CUserRankingProcess::_)(void)___ptr64____::_1_::catch$0
 * Address: 0x140347A20
 */

void __fastcall __noreturn std::_Uninit_fill_n_void____cdecl_CUserRankingProcess::_____ptr64__void____ptr64_unsigned___int64_void____cdecl_CUserRankingProcess::___void____ptr64_std::allocator_void____cdecl_CUserRankingProcess::___void____ptr64____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 8i64 )
    std::allocator<void (CUserRankingProcess::*)(void)>::destroy(
      *(std::allocator<void (__cdecl CUserRankingProcess::*)(void)> **)(i + 88),
      *(void (__cdecl ***)(CUserRankingProcess *))(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
