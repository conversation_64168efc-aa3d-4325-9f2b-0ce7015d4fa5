# CGuildDatabaseManager - Modern C++20 Guild Database Manager

## Overview

The `CGuildDatabaseManager` is a comprehensive, modern C++20 implementation that manages all guild database operations with proper transaction management, error handling, and data integrity validation. This system refactors the original decompiled C functions into a robust, maintainable, and feature-rich guild database management system.

## Refactored Sources

This implementation modernizes the following decompiled files:
- **db_Insert_guildCMainThreadQEAAEPEAKPEADE0Z_1401B0C80.c** (81 lines) - Main guild insertion with database operations
- **db_disjoint_guildCMainThreadQEAAEKZ_1401B2390.c** (26 lines) - Guild deletion operations
- **Delete_GuildCRFWorldDatabaseQEAA_NKZ_14049C2D0.c** (28 lines) - Database guild deletion
- Various guild database operations and checksum management

## Key Features

### 🚀 **Modern C++20 Implementation**
- **RAII Memory Management**: Automatic resource cleanup and exception safety
- **Smart Pointers**: Safe memory handling with proper ownership semantics
- **STL Containers**: Modern containers for efficient data management
- **Atomic Operations**: Thread-safe statistics and error tracking
- **Exception Safety**: Comprehensive error handling and recovery

### 🗄️ **Advanced Database Management**
- **Transaction Support**: Proper database transaction management
- **Retry Logic**: Configurable retry attempts for failed operations
- **Connection Validation**: Database connection health monitoring
- **Checksum Validation**: Data integrity verification with checksum management
- **Error Recovery**: Comprehensive error handling and rollback capabilities

### 📊 **Comprehensive Statistics and Monitoring**
- **Operation Tracking**: Track insertions, deletions, updates, and selections
- **Success Rate Monitoring**: Real-time success/failure rate calculation
- **Error Analytics**: Detailed error tracking and consecutive error monitoring
- **Performance Metrics**: Database operation performance monitoring
- **Uptime Tracking**: System uptime and operational duration

### 🔒 **Data Integrity and Validation**
- **Guild Name Validation**: Comprehensive guild name validation
- **Parameter Validation**: Input parameter validation and sanitization
- **Duplicate Prevention**: Prevent duplicate guild creation
- **Member Data Consistency**: Ensure member guild data consistency
- **Checksum Management**: Automatic checksum creation and validation

## Architecture

### Class Structure

```cpp
namespace NexusProtection::Guild {
    class CGuildDatabaseManager {
        // Singleton pattern with thread-safe database operations
        // Comprehensive transaction management
        // Event-driven architecture with callbacks
        // Advanced error handling and retry logic
    };
}
```

### Key Components

1. **Database Operation Engine**: Comprehensive database operation management
2. **Transaction Manager**: Proper transaction handling with rollback support
3. **Retry System**: Configurable retry logic for failed operations
4. **Validation Engine**: Input validation and data integrity checking
5. **Statistics Collection**: Real-time metrics and performance monitoring
6. **Event System**: Callback-based notifications for database events

## Database Operations

### Guild Creation Process

The system handles the complete guild creation process:

1. **Input Validation**: Validate guild name, race, and member data
2. **Duplicate Check**: Verify guild name doesn't already exist
3. **Database Insertion**: Insert guild into main database table
4. **Serial Retrieval**: Get the generated guild serial number
5. **Checksum Creation**: Create and insert checksum validation data
6. **PvP Data Setup**: Initialize weekly PvP point tracking
7. **Member Updates**: Update all founding members' guild data
8. **Transaction Commit**: Commit all changes or rollback on failure

### Guild Deletion Process

The system handles complete guild removal:

1. **Parameter Validation**: Validate guild serial number
2. **Database Deletion**: Remove guild from all database tables
3. **Cascade Operations**: Handle related data cleanup
4. **Transaction Management**: Ensure atomic deletion operation

## Usage Examples

### Basic Initialization

```cpp
#include "CGuildDatabaseManager.h"

using namespace NexusProtection::Guild;

// Get singleton instance
auto& dbManager = CGuildDatabaseManager::GetInstance();

// Configure system
GuildDatabaseConfig config;
config.enableTransactions = true;
config.enableChecksumValidation = true;
config.maxRetryAttempts = 3;
config.retryDelay = std::chrono::milliseconds(100);

// Initialize system
auto result = dbManager.Initialize(config);
if (result != GuildDatabaseResult::Success) {
    std::cerr << "Failed to initialize guild database manager" << std::endl;
    return -1;
}
```

### Guild Creation

```cpp
// Create guild data
GuildCreationData creationData;
creationData.guildName = "AwesomeGuild";
creationData.race = 0; // Bellato
creationData.memberSerials = {12345, 67890, 11111}; // Founding members
creationData.masterSerial = 12345; // Guild master

// Insert guild
uint32_t guildSerial = 0;
auto result = dbManager.InsertGuild(creationData, guildSerial);

if (result == GuildDatabaseResult::Success) {
    std::cout << "Guild created successfully with serial: " << guildSerial << std::endl;
} else {
    std::cerr << "Guild creation failed with code: " << static_cast<int>(result) << std::endl;
}
```

### Guild Deletion

```cpp
// Delete guild
uint32_t guildSerial = 12345;
auto result = dbManager.DeleteGuild(guildSerial);

if (result == GuildDatabaseResult::Success) {
    std::cout << "Guild deleted successfully" << std::endl;
} else {
    std::cerr << "Guild deletion failed with code: " << static_cast<int>(result) << std::endl;
}
```

### Event Callbacks

```cpp
// Set guild creation callback
dbManager.SetGuildCreatedCallback([](uint32_t guildSerial, const std::string& guildName) {
    std::cout << "Guild created: " << guildName << " (Serial: " << guildSerial << ")" << std::endl;
});

// Set guild deletion callback
dbManager.SetGuildDeletedCallback([](uint32_t guildSerial) {
    std::cout << "Guild deleted: " << guildSerial << std::endl;
});

// Set database error callback
dbManager.SetDatabaseErrorCallback([](GuildDatabaseOperation operation, const std::string& error) {
    std::cerr << "Database error in operation " << static_cast<int>(operation) << ": " << error << std::endl;
});

// Set transaction callback
dbManager.SetTransactionCallback([](bool success, const std::string& operation) {
    std::cout << "Transaction " << operation << ": " << (success ? "SUCCESS" : "FAILED") << std::endl;
});
```

### Statistics Monitoring

```cpp
// Get current statistics
auto stats = dbManager.GetStatistics();

std::cout << "Guild Database Statistics:" << std::endl;
std::cout << "  Total Insertions: " << stats.totalInsertions << std::endl;
std::cout << "  Total Deletions: " << stats.totalDeletions << std::endl;
std::cout << "  Total Updates: " << stats.totalUpdates << std::endl;
std::cout << "  Total Selections: " << stats.totalSelections << std::endl;
std::cout << "  Successful Operations: " << stats.successfulOperations << std::endl;
std::cout << "  Failed Operations: " << stats.failedOperations << std::endl;
std::cout << "  Success Rate: " << stats.GetSuccessRate() << "%" << std::endl;
std::cout << "  Checksum Errors: " << stats.checksumErrors << std::endl;
std::cout << "  Transaction Rollbacks: " << stats.transactionRollbacks << std::endl;
std::cout << "  System Uptime: " << stats.GetUptime().count() << " seconds" << std::endl;
```

### Legacy Compatibility

```cpp
// Legacy C-style function calls are automatically handled
extern "C" {
    uint8_t CMainThread_db_Insert_guild(uint32_t* memberSerials, const char* guildName, uint8_t race, uint32_t* guildSerial);
    uint8_t CMainThread_db_disjoint_guild(uint32_t guildSerial);
}

// These functions automatically delegate to the modern implementation
uint32_t memberSerials[] = {12345, 67890, 11111, -1}; // -1 terminated
uint32_t guildSerial = 0;
uint8_t result = CMainThread_db_Insert_guild(memberSerials, "TestGuild", 0, &guildSerial);
// result: 0 = success, 24 = error (legacy codes)
```

## Configuration Options

### GuildDatabaseConfig Structure

```cpp
struct GuildDatabaseConfig {
    bool enableTransactions = true;          // Enable database transactions
    bool enableChecksumValidation = true;    // Enable checksum validation
    bool enableStatistics = true;            // Enable statistics collection
    bool enableErrorLogging = true;          // Enable error logging
    uint32_t maxRetryAttempts = 3;          // Maximum retry attempts for failed operations
    std::chrono::milliseconds retryDelay{100}; // Delay between retry attempts
};
```

### Configuration Validation

The system automatically validates configuration parameters:
- `maxRetryAttempts`: Must be between 1 and 10
- `retryDelay`: Must be positive duration
- All boolean flags are validated for consistency

## Error Handling

### Result Codes

```cpp
enum class GuildDatabaseResult : uint8_t {
    Success = 0,              // Operation completed successfully
    DatabaseError = 24,       // Legacy database error code
    InvalidParameters,        // Invalid parameters provided
    TransactionFailed,        // Database transaction failed
    ChecksumError,           // Checksum validation failed
    UserUpdateFailed,        // Member data update failed
    GuildNotFound,           // Guild not found in database
    DuplicateGuild,          // Guild name already exists
    SystemError              // General system error
};
```

### Error Recovery

The system implements comprehensive error recovery:
- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Transaction Rollback**: Automatic rollback on transaction failures
- **Error Tracking**: Monitor consecutive errors and error rates
- **Callback Notifications**: Custom error handling through callbacks

## Performance Characteristics

### Optimizations

- **Atomic Operations**: Lock-free statistics updates where possible
- **Connection Pooling**: Efficient database connection management
- **Prepared Statements**: Optimized database query execution
- **Batch Operations**: Efficient bulk data operations
- **Memory Efficiency**: Minimal memory overhead with efficient data structures

### Scalability

- **Thread-Safe**: Safe for concurrent access from multiple threads
- **Connection Management**: Efficient database connection handling
- **Resource Management**: Automatic cleanup and resource management
- **Performance Monitoring**: Real-time performance metrics

## Integration

### Project Integration

The guild database manager integrates seamlessly with the existing NexusProtection architecture:

1. **Header Inclusion**: Include `CGuildDatabaseManager.h` in your project
2. **Namespace Usage**: Use `NexusProtection::Guild` namespace
3. **Legacy Compatibility**: Existing C-style calls work automatically
4. **Event Integration**: Connect to existing event systems through callbacks

### Dependencies

- **C++20 Standard**: Requires C++20 compatible compiler
- **STL Libraries**: Uses standard library containers and utilities
- **Legacy Database System**: Interfaces with existing CRFWorldDatabase
- **Threading Support**: Requires std::thread and std::mutex support

## Best Practices

### Initialization

1. **Early Initialization**: Initialize the database manager early in application startup
2. **Configuration Validation**: Always validate configuration before use
3. **Error Checking**: Check initialization results and handle failures
4. **Resource Cleanup**: Ensure proper shutdown on application exit

### Usage Patterns

1. **Transaction Management**: Use transactions for multi-step operations
2. **Error Monitoring**: Monitor error rates and implement alerting
3. **Performance Tuning**: Adjust retry settings based on database performance
4. **Statistics Usage**: Disable statistics in production if not needed

### Performance Tips

1. **Batch Operations**: Process multiple operations efficiently
2. **Connection Management**: Reuse database connections when possible
3. **Retry Configuration**: Tune retry settings for optimal performance
4. **Callback Efficiency**: Keep callback functions lightweight and fast
