/*
 * Function: ?_Growmap@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@IEAAX_K@Z
 * Address: 0x14031A690
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Growmap(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this, unsigned __int64 _Count)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@9
  RECV_DATA **v5; // rcx@11
  RECV_DATA **v6; // rcx@13
  RECV_DATA **v7; // rcx@14
  __int64 v8; // [sp+0h] [bp-78h]@1
  unsigned __int64 v9; // [sp+20h] [bp-58h]@6
  unsigned __int64 _Counta; // [sp+28h] [bp-50h]@11
  RECV_DATA **v11; // [sp+30h] [bp-48h]@11
  RECV_DATA **_Dest; // [sp+38h] [bp-40h]@11
  RECV_DATA *_Val; // [sp+40h] [bp-38h]@12
  RECV_DATA *v14; // [sp+48h] [bp-30h]@12
  RECV_DATA *v15; // [sp+50h] [bp-28h]@13
  RECV_DATA **_First; // [sp+58h] [bp-20h]@11
  RECV_DATA **v17; // [sp+60h] [bp-18h]@13
  RECV_DATA **v18; // [sp+68h] [bp-10h]@14
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v19; // [sp+80h] [bp+8h]@1
  unsigned __int64 v20; // [sp+88h] [bp+10h]@1

  v20 = _Count;
  v19 = this;
  v2 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( std::deque<RECV_DATA,std::allocator<RECV_DATA>>::max_size(v19) - v19->_Mapsize < _Count )
    std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Xlen();
  v9 = v19->_Mapsize / 2;
  if ( v9 < 8 )
    v9 = 8i64;
  if ( v20 < v9 )
  {
    v4 = std::deque<RECV_DATA,std::allocator<RECV_DATA>>::max_size(v19);
    if ( v19->_Mapsize <= v4 - v9 )
      v20 = v9;
  }
  _Counta = v19->_Myoff;
  v11 = std::allocator<RECV_DATA *>::allocate(&v19->_Almap, v20 + v19->_Mapsize);
  _Dest = &v11[_Counta];
  v5 = &v19->_Map[v19->_Mapsize];
  _First = &v19->_Map[_Counta];
  _Dest = stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
            _First,
            v5,
            &v11[_Counta],
            &v19->_Almap);
  if ( _Counta > v20 )
  {
    stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
      v19->_Map,
      &v19->_Map[v20],
      _Dest,
      &v19->_Almap);
    v6 = &v19->_Map[_Counta];
    v17 = &v19->_Map[v20];
    _Dest = stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
              v17,
              v6,
              v11,
              &v19->_Almap);
    v15 = 0i64;
    stdext::unchecked_uninitialized_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(
      _Dest,
      v20,
      &v15,
      &v19->_Almap);
  }
  else
  {
    _Dest = stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
              v19->_Map,
              &v19->_Map[_Counta],
              _Dest,
              &v19->_Almap);
    _Val = 0i64;
    stdext::unchecked_uninitialized_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(
      _Dest,
      v20 - _Counta,
      &_Val,
      &v19->_Almap);
    v14 = 0i64;
    stdext::unchecked_uninitialized_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(
      v11,
      _Counta,
      &v14,
      &v19->_Almap);
  }
  v7 = &v19->_Map[v19->_Mapsize];
  v18 = &v19->_Map[_Counta];
  std::_Destroy_range<RECV_DATA *,std::allocator<RECV_DATA *>>(v18, v7, &v19->_Almap);
  if ( v19->_Map )
    std::allocator<RECV_DATA *>::deallocate(&v19->_Almap, v19->_Map, v19->_Mapsize);
  v19->_Map = v11;
  v19->_Mapsize += v20;
}
