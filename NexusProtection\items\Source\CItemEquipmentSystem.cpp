/*
 * CItemEquipmentSystem.cpp - Modern Item Equipment and Store System Implementation
 * Refactored from decompiled C item equipment, store, and special item functions
 * Provides comprehensive equipment management, item stores, and special item handling
 */

#include "../Headers/CItemEquipmentSystem.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CPlayer.h"

#include <algorithm>
#include <chrono>
#include <stdexcept>
#include <cstring>

// External references to legacy systems
extern "C" {
    // Legacy function declarations
    class CLuaLootingMgr;
    class CMapData;
    struct _STORAGE_LIST;
    struct _golden_box_item;
    struct _itembox_create_setdata;
    struct _object_create_setdata;
    
    extern void memset_0(void* dest, int value, size_t count);
    extern void _object_create_setdata_Constructor(_object_create_setdata* pData);
    extern void _STORAGE_LIST_db_con_Constructor(_STORAGE_LIST* pItem);
    extern bool CEquipItemSFAgent_AllEndContSF(void* pAgent);
    extern uint32_t CLuaLootingMgr_SearchSlotIndex(CLuaLootingMgr* pMgr, void* pState);
    extern void* CLuaLootingMgr_GetStateAtPtr(CLuaLootingMgr* pMgr, uint32_t index);
    extern bool IsValidEquipmentSlot(int tableCode, uint8_t slot);
    extern bool CheckPlayerLevel(CPlayer* pPlayer, int requiredLevel);
    extern bool CheckPlayerClass(CPlayer* pPlayer, int requiredClass);
    extern uint32_t GetItemPrice(const char* itemCode, int storeType);
    extern bool DeductPlayerMoney(CPlayer* pPlayer, uint32_t amount);
    extern bool AddPlayerMoney(CPlayer* pPlayer, uint32_t amount);
}

namespace NexusProtection {
namespace Items {

/**
 * Constructor
 */
CItemEquipmentSystem::CItemEquipmentSystem() 
    : m_bDetailedLogging(false), m_bInitialized(false) {
    
    Logger::Debug("CItemEquipmentSystem::CItemEquipmentSystem - Item equipment system created");
}

/**
 * Destructor
 */
CItemEquipmentSystem::~CItemEquipmentSystem() {
    try {
        Shutdown();
        Logger::Debug("CItemEquipmentSystem::~CItemEquipmentSystem - Item equipment system destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Initialize equipment system
 */
bool CItemEquipmentSystem::Initialize() {
    try {
        if (m_bInitialized) {
            Logger::Warning("CItemEquipmentSystem::Initialize - Already initialized");
            return true;
        }
        
        // Reset statistics
        ResetStatistics();
        
        m_bInitialized = true;
        Logger::Info("CItemEquipmentSystem::Initialize - Item equipment system initialized");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::Initialize - Exception: %s", e.what());
        return false;
    }
}

/**
 * Shutdown equipment system
 */
void CItemEquipmentSystem::Shutdown() {
    try {
        if (!m_bInitialized) {
            return;
        }
        
        m_bInitialized = false;
        Logger::Info("CItemEquipmentSystem::Shutdown - Item equipment system shutdown");
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::Shutdown - Exception: %s", e.what());
    }
}

/**
 * Equip item to player
 * Refactored from: CEquipItemSFAgent functions
 */
EquipmentOperationDetails CItemEquipmentSystem::EquipItem(const EquipmentContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        if (!ValidateEquipmentContext(context)) {
            return CreateEquipmentResult(EquipmentResult::InvalidItem, startTime, "Invalid equipment context");
        }
        
        // Initialize stack variables (original pattern from CEquipItemSFAgent constructor)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Check equipment requirements
        if (context.bCheckRequirements && !CheckEquipmentRequirements(context.pPlayer, context.pItem, context.slot)) {
            return CreateEquipmentResult(EquipmentResult::RequirementNotMet, startTime, "Equipment requirements not met");
        }
        
        // Get current equipped item in slot
        _STORAGE_LIST* pCurrentItem = nullptr; // In real implementation, get from player equipment
        
        // Check if slot is already occupied
        if (pCurrentItem && !context.bForceEquip) {
            return CreateEquipmentResult(EquipmentResult::AlreadyEquipped, startTime, "Slot already occupied");
        }
        
        // Perform equipment operation
        // In real implementation, this would:
        // 1. Remove item from inventory
        // 2. Place item in equipment slot
        // 3. Apply item effects
        // 4. Update player stats
        
        // Update statistics
        m_stats.RecordEquipment(true);
        
        EquipmentOperationDetails result = CreateEquipmentResult(EquipmentResult::Success, startTime);
        result.context = context;
        result.pPreviousItem = pCurrentItem;
        
        LogEquipmentOperation(result);
        
        if (m_equipmentCallback) {
            m_equipmentCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordEquipment(false);
        Logger::Error("CItemEquipmentSystem::EquipItem - Exception: %s", e.what());
        return CreateEquipmentResult(EquipmentResult::SystemError, startTime, 
                                   std::string("Exception: ") + e.what());
    }
}

/**
 * Unequip item from player
 */
EquipmentOperationDetails CItemEquipmentSystem::UnequipItem(const EquipmentContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        if (!ValidateEquipmentContext(context)) {
            return CreateEquipmentResult(EquipmentResult::InvalidItem, startTime, "Invalid equipment context");
        }
        
        // Get current equipped item in slot
        _STORAGE_LIST* pCurrentItem = nullptr; // In real implementation, get from player equipment
        
        if (!pCurrentItem) {
            return CreateEquipmentResult(EquipmentResult::InvalidItem, startTime, "No item equipped in slot");
        }
        
        // Check if item can be unequipped
        // In real implementation, check for cursed items, quest items, etc.
        
        // Perform unequip operation
        // In real implementation, this would:
        // 1. Remove item from equipment slot
        // 2. Add item to inventory
        // 3. Remove item effects
        // 4. Update player stats
        
        // Update statistics
        m_stats.RecordEquipment(true);
        
        EquipmentOperationDetails result = CreateEquipmentResult(EquipmentResult::Success, startTime);
        result.context = context;
        result.pPreviousItem = pCurrentItem;
        
        LogEquipmentOperation(result);
        
        if (m_equipmentCallback) {
            m_equipmentCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        m_stats.RecordEquipment(false);
        Logger::Error("CItemEquipmentSystem::UnequipItem - Exception: %s", e.what());
        return CreateEquipmentResult(EquipmentResult::SystemError, startTime, 
                                   std::string("Exception: ") + e.what());
    }
}

/**
 * Process store transaction
 * Refactored from: CItemStore functions
 */
StoreOperationDetails CItemEquipmentSystem::ProcessStoreTransaction(const StoreContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        if (!ValidateStoreContext(context)) {
            return CreateStoreResult(StoreResult::InvalidStore, startTime, "Invalid store context");
        }
        
        // Initialize stack variables (original pattern from CItemStore constructor)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Calculate total cost
        uint32_t itemPrice = GetItemPrice(context.itemCode.c_str(), static_cast<int>(context.storeType));
        uint32_t totalCost = itemPrice * context.quantity;
        
        bool success = false;
        if (context.bBuyOperation) {
            success = ProcessStoreBuy(context);
        } else {
            success = ProcessStoreSell(context);
        }
        
        if (!success) {
            return CreateStoreResult(StoreResult::Failure, startTime, "Store transaction failed");
        }
        
        // Update statistics
        m_stats.RecordStoreTransaction(totalCost);
        
        StoreOperationDetails result = CreateStoreResult(StoreResult::Success, startTime);
        result.context = context;
        result.totalCost = totalCost;
        
        Logger::Debug("CItemEquipmentSystem::ProcessStoreTransaction - %s %d x %s for %d", 
                     context.bBuyOperation ? "Bought" : "Sold",
                     context.quantity, context.itemCode.c_str(), totalCost);
        
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ProcessStoreTransaction - Exception: %s", e.what());
        return CreateStoreResult(StoreResult::SystemError, startTime, 
                               std::string("Exception: ") + e.what());
    }
}

/**
 * Open special item box
 * Refactored from: _golden_box_item and related functions
 */
SpecialItemOperationDetails CItemEquipmentSystem::OpenSpecialItem(const SpecialItemContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        if (!ValidateSpecialItemContext(context)) {
            return CreateSpecialItemResult(SpecialItemResult::InvalidBox, startTime, "Invalid special item context");
        }
        
        // Initialize stack variables (original pattern from _golden_box_item constructor)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Generate rewards based on item type
        std::vector<_STORAGE_LIST*> rewards = GenerateSpecialItemRewards(context);
        
        if (rewards.empty()) {
            return CreateSpecialItemResult(SpecialItemResult::BoxEmpty, startTime, "No rewards generated");
        }
        
        // Add rewards to player inventory
        // In real implementation, this would add each reward item to the player's inventory
        
        // Update statistics
        m_stats.specialItemsOpened++;
        
        SpecialItemOperationDetails result = CreateSpecialItemResult(SpecialItemResult::Success, startTime);
        result.context = context;
        result.rewardItems = rewards;
        
        Logger::Debug("CItemEquipmentSystem::OpenSpecialItem - Opened %s, got %zu rewards", 
                     context.itemCode.c_str(), rewards.size());
        
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::OpenSpecialItem - Exception: %s", e.what());
        return CreateSpecialItemResult(SpecialItemResult::SystemError, startTime,
                                     std::string("Exception: ") + e.what());
    }
}

/**
 * Add Novus item to world
 * Refactored from: AddNovusItemCLuaLootingMgrQEAA_NPEBDPEAVCMapDataGP_140404EE0.c
 */
bool CItemEquipmentSystem::AddNovusItem(CLuaLootingMgr* pLootingMgr, const std::string& itemCode, CMapData* pMap,
                                       uint16_t layerIndex, const std::array<float, 3>& position, uint16_t lootRange,
                                       uint32_t overlapCount, uint32_t itemCount, uint8_t createType) {
    try {
        if (!pLootingMgr || !pMap || itemCode.empty()) {
            Logger::Error("CItemEquipmentSystem::AddNovusItem - Invalid parameters");
            return false;
        }

        // Initialize stack variables (original lines 31-36)
        int stackInit[24];
        for (int i = 0; i < 24; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }

        // Search for available slot (original lines 39-42)
        uint32_t slotIndex = CLuaLootingMgr_SearchSlotIndex(pLootingMgr, nullptr);
        if (slotIndex == static_cast<uint32_t>(-1)) {
            Logger::Warning("CItemEquipmentSystem::AddNovusItem - No available slot");
            return false;
        }

        // Get state pointer (original lines 48-50)
        void* pState = CLuaLootingMgr_GetStateAtPtr(pLootingMgr, slotIndex);
        if (!pState) {
            Logger::Error("CItemEquipmentSystem::AddNovusItem - Failed to get state pointer");
            return false;
        }

        // In real implementation, this would:
        // 1. Create the Novus item with specified parameters
        // 2. Set item position and properties
        // 3. Add to looting manager
        // 4. Configure loot range and overlap settings

        Logger::Debug("CItemEquipmentSystem::AddNovusItem - Added Novus item %s at slot %u",
                     itemCode.c_str(), slotIndex);

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::AddNovusItem - Exception: %s", e.what());
        return false;
    }
}

/**
 * Create item box with data
 * Refactored from: _itembox_create_setdata functions
 */
_itembox_create_setdata* CItemEquipmentSystem::CreateItemBoxData(_STORAGE_LIST* pItem, CPlayer* pOwner, CPlayer* pThrower,
                                                               uint8_t createCode, bool bParty, uint32_t partyBossSerial) {
    try {
        if (!pItem || !pOwner) {
            Logger::Error("CItemEquipmentSystem::CreateItemBoxData - Invalid parameters");
            return nullptr;
        }

        // Initialize stack variables (original lines 14-19)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }

        // Allocate item box data
        _itembox_create_setdata* pItemBoxData = new _itembox_create_setdata();

        // Initialize object create setdata (original line 20)
        _object_create_setdata_Constructor(reinterpret_cast<_object_create_setdata*>(&pItemBoxData->m_pRecordSet));

        // Initialize storage list (original line 21)
        _STORAGE_LIST_db_con_Constructor(&pItemBoxData->Item);

        // Set item box properties (original lines 22-29)
        pItemBoxData->pOwner = pOwner;
        pItemBoxData->pThrower = pThrower;
        pItemBoxData->byCreateCode = createCode;
        pItemBoxData->bParty = bParty;
        pItemBoxData->dwPartyBossSerial = partyBossSerial;
        pItemBoxData->pAttacker = nullptr;
        pItemBoxData->bHolyScanner = false;
        pItemBoxData->byEventItemLootAuth = 3;

        // Copy item data
        memcpy(&pItemBoxData->Item, pItem, sizeof(_STORAGE_LIST));

        Logger::Debug("CItemEquipmentSystem::CreateItemBoxData - Created item box data");

        return pItemBoxData;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::CreateItemBoxData - Exception: %s", e.what());
        return nullptr;
    }
}

/**
 * Validate equipment context
 */
bool CItemEquipmentSystem::ValidateEquipmentContext(const EquipmentContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemEquipmentSystem::ValidateEquipmentContext - Invalid context");
            return false;
        }

        if (context.slot >= EquipmentSlot::MaxEquipSlots && context.slot != EquipmentSlot::None) {
            Logger::Warning("CItemEquipmentSystem::ValidateEquipmentContext - Invalid equipment slot: %d",
                           static_cast<int>(context.slot));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ValidateEquipmentContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate store context
 */
bool CItemEquipmentSystem::ValidateStoreContext(const StoreContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemEquipmentSystem::ValidateStoreContext - Invalid context");
            return false;
        }

        if (context.quantity > 100) {
            Logger::Warning("CItemEquipmentSystem::ValidateStoreContext - Invalid quantity: %d", context.quantity);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ValidateStoreContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate special item context
 */
bool CItemEquipmentSystem::ValidateSpecialItemContext(const SpecialItemContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemEquipmentSystem::ValidateSpecialItemContext - Invalid context");
            return false;
        }

        if (context.itemCount == 0 || context.itemCount > 1000) {
            Logger::Warning("CItemEquipmentSystem::ValidateSpecialItemContext - Invalid item count: %d",
                           context.itemCount);
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ValidateSpecialItemContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Reset statistics
 */
void CItemEquipmentSystem::ResetStatistics() {
    try {
        std::lock_guard<std::mutex> lock(m_statsMutex);

        m_stats.totalEquipOperations = 0;
        m_stats.successfulEquips = 0;
        m_stats.failedEquips = 0;
        m_stats.storeTransactions = 0;
        m_stats.specialItemsOpened = 0;
        m_stats.totalRevenue = 0;
        m_stats.lastOperation = std::chrono::system_clock::now();

        Logger::Debug("CItemEquipmentSystem::ResetStatistics - Statistics reset");

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ResetStatistics - Exception: %s", e.what());
    }
}

/**
 * Set equipment callback
 */
void CItemEquipmentSystem::SetEquipmentCallback(std::function<void(const EquipmentOperationDetails&)> callback) {
    m_equipmentCallback = callback;
}

/**
 * Check equipment requirements
 * Based on actual game logic from pc_EquipPart function
 */
bool CItemEquipmentSystem::CheckEquipmentRequirements(CPlayer* pPlayer, _STORAGE_LIST* pItem, EquipmentSlot slot) {
    try {
        if (!pPlayer || !pItem) {
            return false;
        }

        // Check if item is actually equipment (table codes 0-7 from pc_EquipPart line 55)
        if (pItem->m_byTableCode >= 8) {
            Logger::Warning("CItemEquipmentSystem::CheckEquipmentRequirements - Item is not equipment, table code: %d",
                           pItem->m_byTableCode);
            return false;
        }

        // Check if item is locked (from pc_EquipPart line 57-60)
        if (pItem->m_bLock) {
            Logger::Warning("CItemEquipmentSystem::CheckEquipmentRequirements - Item is locked");
            return false;
        }

        // Check equipment grade requirements (from pc_EquipPart line 70-76)
        // In real implementation, this would call GetItemEquipGrade and IsEquipAbleGrade
        // For now, assume grade check passes

        // Check if slot matches item type
        EquipmentSlot itemSlot = GetEquipmentSlot(pItem);
        if (itemSlot != slot && slot != EquipmentSlot::None) {
            Logger::Warning("CItemEquipmentSystem::CheckEquipmentRequirements - Item slot mismatch: expected %d, got %d",
                           static_cast<int>(slot), static_cast<int>(itemSlot));
            return false;
        }

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::CheckEquipmentRequirements - Exception: %s", e.what());
        return false;
    }
}

/**
 * Get equipment slot for item
 * Based on actual game table codes from GetItemTableCode function
 */
EquipmentSlot CItemEquipmentSystem::GetEquipmentSlot(_STORAGE_LIST* pItem) {
    try {
        if (!pItem) {
            return EquipmentSlot::None;
        }

        // Map table codes to equipment slots based on actual game data
        // Equipment items have table codes 0-7 (validated in pc_EquipPart)
        switch (pItem->m_byTableCode) {
            case 0: return EquipmentSlot::Weapon;      // "iu" - Weapon
            case 1: return EquipmentSlot::LeftHand;    // "il" - Left hand weapon/shield
            case 2: return EquipmentSlot::Gloves;      // "ig" - Gloves
            case 3: return EquipmentSlot::Shoes;       // "is" - Shoes/Boots
            case 4: return EquipmentSlot::Helmet;      // "ih" - Helmet
            case 5: return EquipmentSlot::Armor;       // "id" - Armor/Dress
            case 6: return EquipmentSlot::Wings;       // "iw" - Wings
            case 7: return EquipmentSlot::Ring;        // "ik" - Ring

            // Non-equipment items
            case 8: return EquipmentSlot::Accessory;   // "ii" - Accessory items
            case 9: return EquipmentSlot::Consumable;  // "ia" - Consumable items
            case 10: return EquipmentSlot::Misc;       // "ib" - Miscellaneous items
            case 11: return EquipmentSlot::Material;   // "im" - Material items

            default: return EquipmentSlot::None;
        }

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::GetEquipmentSlot - Exception: %s", e.what());
        return EquipmentSlot::None;
    }
}

/**
 * Process store buy operation
 */
bool CItemEquipmentSystem::ProcessStoreBuy(const StoreContext& context) {
    try {
        // Calculate total cost
        uint32_t itemPrice = GetItemPrice(context.itemCode.c_str(), static_cast<int>(context.storeType));
        uint32_t totalCost = itemPrice * context.quantity;

        // Check if player has enough money
        // In real implementation, check player's actual money
        if (!DeductPlayerMoney(context.pPlayer, totalCost)) {
            Logger::Warning("CItemEquipmentSystem::ProcessStoreBuy - Insufficient funds");
            return false;
        }

        // Add items to player inventory
        // In real implementation, create and add items to player's inventory

        Logger::Debug("CItemEquipmentSystem::ProcessStoreBuy - Player bought %d x %s for %d",
                     context.quantity, context.itemCode.c_str(), totalCost);

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ProcessStoreBuy - Exception: %s", e.what());
        return false;
    }
}

/**
 * Process store sell operation
 */
bool CItemEquipmentSystem::ProcessStoreSell(const StoreContext& context) {
    try {
        // Calculate sell price (usually lower than buy price)
        uint32_t itemPrice = GetItemPrice(context.itemCode.c_str(), static_cast<int>(context.storeType));
        uint32_t sellPrice = itemPrice / 2; // 50% of buy price
        uint32_t totalValue = sellPrice * context.quantity;

        // Remove items from player inventory
        // In real implementation, remove items from player's inventory

        // Add money to player
        if (!AddPlayerMoney(context.pPlayer, totalValue)) {
            Logger::Error("CItemEquipmentSystem::ProcessStoreSell - Failed to add money to player");
            return false;
        }

        Logger::Debug("CItemEquipmentSystem::ProcessStoreSell - Player sold %d x %s for %d",
                     context.quantity, context.itemCode.c_str(), totalValue);

        return true;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ProcessStoreSell - Exception: %s", e.what());
        return false;
    }
}

/**
 * Generate special item rewards
 */
std::vector<_STORAGE_LIST*> CItemEquipmentSystem::GenerateSpecialItemRewards(const SpecialItemContext& context) {
    try {
        std::vector<_STORAGE_LIST*> rewards;

        // In real implementation, this would:
        // 1. Look up reward table for the special item
        // 2. Generate random rewards based on probabilities
        // 3. Create storage list items for each reward
        // 4. Apply any special modifiers

        // For now, return empty vector as placeholder
        Logger::Debug("CItemEquipmentSystem::GenerateSpecialItemRewards - Generated %zu rewards for %s",
                     rewards.size(), context.itemCode.c_str());

        return rewards;

    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::GenerateSpecialItemRewards - Exception: %s", e.what());
        return std::vector<_STORAGE_LIST*>();
    }
}

/**
 * Log equipment operation
 */
void CItemEquipmentSystem::LogEquipmentOperation(const EquipmentOperationDetails& details) {
    try {
        if (m_bDetailedLogging) {
            Logger::Info("Equipment Operation - Result: %s, Slot: %d, Time: %lldms",
                        details.GetResultString().c_str(),
                        static_cast<int>(details.context.slot),
                        details.executionTime.count());
        }

        if (!details.IsSuccess()) {
            Logger::Warning("Equipment Operation Failed - %s: %s",
                           details.GetResultString().c_str(),
                           details.errorMessage.c_str());
        }

    } catch (const std::exception& e) {
        // Don't log errors in logging function to avoid recursion
    }
}

/**
 * Create equipment result with timing
 */
EquipmentOperationDetails CItemEquipmentSystem::CreateEquipmentResult(EquipmentResult result,
                                                                     std::chrono::high_resolution_clock::time_point startTime,
                                                                     const std::string& errorMessage) {
    EquipmentOperationDetails equipmentResult;
    equipmentResult.result = result;
    equipmentResult.errorMessage = errorMessage;
    equipmentResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return equipmentResult;
}

/**
 * Create store result with timing
 */
StoreOperationDetails CItemEquipmentSystem::CreateStoreResult(StoreResult result,
                                                             std::chrono::high_resolution_clock::time_point startTime,
                                                             const std::string& errorMessage) {
    StoreOperationDetails storeResult;
    storeResult.result = result;
    storeResult.errorMessage = errorMessage;
    storeResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return storeResult;
}

/**
 * Create special item result with timing
 */
SpecialItemOperationDetails CItemEquipmentSystem::CreateSpecialItemResult(SpecialItemResult result,
                                                                         std::chrono::high_resolution_clock::time_point startTime,
                                                                         const std::string& errorMessage) {
    SpecialItemOperationDetails specialResult;
    specialResult.result = result;
    specialResult.errorMessage = errorMessage;
    specialResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);

    return specialResult;
}

/**
 * Legacy compatibility functions
 */
namespace LegacyCompatibility {

/**
 * Legacy equipment agent constructor wrapper
 */
void CEquipItemSFAgent_Constructor_Legacy(void* pAgent) {
    try {
        if (!pAgent) {
            Logger::Error("LegacyCompatibility::CEquipItemSFAgent_Constructor_Legacy - Invalid agent pointer");
            return;
        }

        // Initialize stack variables (original lines 14-19)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }

        // Initialize agent (original lines 20-21)
        *reinterpret_cast<void**>(pAgent) = nullptr; // m_pMaster = 0
        CEquipItemSFAgent_AllEndContSF(pAgent);

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::CEquipItemSFAgent_Constructor_Legacy - Exception: %s", e.what());
    }
}

/**
 * Legacy item store constructor wrapper
 */
void CItemStore_Constructor_Legacy(void* pStore) {
    try {
        if (!pStore) {
            Logger::Error("LegacyCompatibility::CItemStore_Constructor_Legacy - Invalid store pointer");
            return;
        }

        // Initialize stack variables (original lines 14-19)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }

        // Initialize store (original line 20)
        memset_0(pStore, 0, 0x78); // Original size

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::CItemStore_Constructor_Legacy - Exception: %s", e.what());
    }
}

/**
 * Legacy golden box item constructor wrapper
 */
void GoldenBoxItem_Constructor_Legacy(void* pGoldenBox) {
    try {
        if (!pGoldenBox) {
            Logger::Error("LegacyCompatibility::GoldenBoxItem_Constructor_Legacy - Invalid golden box pointer");
            return;
        }

        // Initialize stack variables (original lines 14-19)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }

        // Initialize golden box (original line 20)
        memset_0(pGoldenBox, 0, 0x97C); // Original size

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::GoldenBoxItem_Constructor_Legacy - Exception: %s", e.what());
    }
}

/**
 * Legacy item box create setdata constructor wrapper
 */
void ItemBoxCreateSetData_Constructor_Legacy(void* pItemBoxData) {
    try {
        static CItemEquipmentSystem equipmentSystem;

        if (!equipmentSystem.Initialize()) {
            Logger::Error("LegacyCompatibility::ItemBoxCreateSetData_Constructor_Legacy - Failed to initialize equipment system");
            return;
        }

        // In real implementation, this would properly initialize the item box create setdata
        // For now, just log the operation
        Logger::Debug("LegacyCompatibility::ItemBoxCreateSetData_Constructor_Legacy - Item box create setdata initialized");

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::ItemBoxCreateSetData_Constructor_Legacy - Exception: %s", e.what());
    }
}

/**
 * Legacy add Novus item wrapper
 */
char AddNovusItem_Legacy(CLuaLootingMgr* pLootingMgr, const char* itemCode, CMapData* pMap,
                        uint16_t layerIndex, float* position, uint16_t lootRange,
                        uint32_t overlapCount, uint32_t itemCount, uint8_t createType) {
    try {
        static CItemEquipmentSystem equipmentSystem;

        if (!equipmentSystem.Initialize()) {
            Logger::Error("LegacyCompatibility::AddNovusItem_Legacy - Failed to initialize equipment system");
            return 0;
        }

        std::array<float, 3> posArray = {position[0], position[1], position[2]};

        bool result = equipmentSystem.AddNovusItem(pLootingMgr, std::string(itemCode), pMap,
                                                  layerIndex, posArray, lootRange,
                                                  overlapCount, itemCount, createType);
        return result ? 1 : 0;

    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::AddNovusItem_Legacy - Exception: %s", e.what());
        return 0;
    }
}

} // namespace LegacyCompatibility

} // namespace Items
} // namespace NexusProtection
