/*
 * CItemTradingSystem.h - Modern Item Trading and Storage System
 * Refactored from decompiled C item trading and storage functions
 * Provides comprehensive item trading, storage management, and item creation
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <optional>

// Forward declarations
class CPlayer;
class CNetworkEX;
class CUnmannedTraderController;
struct _STORAGE_LIST;
struct _STORAGE_POS_INDIV;
struct _unmannedtrader_buy_item_request_clzo;
struct _a_trade_clear_item_request_clzo;

namespace NexusProtection {
namespace Items {

/**
 * Trading operation result codes
 */
enum class TradingResult : int {
    Success = 1,
    Failure = 0,
    InvalidPlayer = -1,
    InvalidItem = -2,
    InsufficientFunds = -3,
    InventoryFull = -4,
    TradeNotAllowed = -5,
    ItemNotFound = -6,
    SystemError = -7
};

/**
 * Storage operation result codes
 */
enum class StorageResult : int {
    Success = 1,
    Failure = 0,
    InvalidStorage = -1,
    StorageFull = -2,
    ItemNotFound = -3,
    InvalidPosition = -4,
    SystemError = -5
};

/**
 * Item creation result codes
 */
enum class ItemCreationResult : int {
    Success = 1,
    Failure = 0,
    InvalidMaterials = -1,
    InsufficientMaterials = -2,
    InvalidRecipe = -3,
    CreationFailed = -4,
    SystemError = -5
};

/**
 * Trading request context
 */
struct TradingContext {
    CPlayer* pBuyer{nullptr};
    CPlayer* pSeller{nullptr};
    uint32_t itemSerial{0};
    uint8_t quantity{1};
    uint32_t price{0};
    std::string itemCode;
    bool isUnmannedTrade{false};
    
    TradingContext() = default;
    
    bool IsValid() const {
        return pBuyer != nullptr && !itemCode.empty() && quantity > 0;
    }
};

/**
 * Storage operation context
 */
struct StorageContext {
    CPlayer* pPlayer{nullptr};
    uint8_t storageCode{0};
    uint8_t position{0};
    _STORAGE_LIST* pItem{nullptr};
    bool bEquipChange{false};
    bool bAdd{true};
    
    StorageContext() = default;
    
    bool IsValid() const {
        return pPlayer != nullptr && storageCode < 8;
    }
};

/**
 * Item creation context
 */
struct ItemCreationContext {
    CPlayer* pPlayer{nullptr};
    _STORAGE_POS_INDIV* pMakeTool{nullptr};
    uint16_t manualIndex{0};
    uint8_t materialNum{0};
    std::vector<_STORAGE_POS_INDIV*> materials;
    std::string recipeCode;
    
    ItemCreationContext() = default;
    
    bool IsValid() const {
        return pPlayer != nullptr && pMakeTool != nullptr && materialNum > 0;
    }
};

/**
 * Trading operation details
 */
struct TradingOperationDetails {
    TradingResult result{TradingResult::Success};
    std::string errorMessage;
    TradingContext context;
    uint32_t transactionId{0};
    std::chrono::milliseconds executionTime{0};
    
    TradingOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == TradingResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case TradingResult::Success: return "Success";
            case TradingResult::Failure: return "General failure";
            case TradingResult::InvalidPlayer: return "Invalid player";
            case TradingResult::InvalidItem: return "Invalid item";
            case TradingResult::InsufficientFunds: return "Insufficient funds";
            case TradingResult::InventoryFull: return "Inventory full";
            case TradingResult::TradeNotAllowed: return "Trade not allowed";
            case TradingResult::ItemNotFound: return "Item not found";
            case TradingResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Storage operation details
 */
struct StorageOperationDetails {
    StorageResult result{StorageResult::Success};
    std::string errorMessage;
    StorageContext context;
    _STORAGE_LIST* pResultItem{nullptr};
    std::chrono::milliseconds executionTime{0};
    
    StorageOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == StorageResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case StorageResult::Success: return "Success";
            case StorageResult::Failure: return "General failure";
            case StorageResult::InvalidStorage: return "Invalid storage";
            case StorageResult::StorageFull: return "Storage full";
            case StorageResult::ItemNotFound: return "Item not found";
            case StorageResult::InvalidPosition: return "Invalid position";
            case StorageResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Item creation operation details
 */
struct ItemCreationOperationDetails {
    ItemCreationResult result{ItemCreationResult::Success};
    std::string errorMessage;
    ItemCreationContext context;
    _STORAGE_LIST* pCreatedItem{nullptr};
    std::chrono::milliseconds executionTime{0};
    
    ItemCreationOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == ItemCreationResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case ItemCreationResult::Success: return "Success";
            case ItemCreationResult::Failure: return "General failure";
            case ItemCreationResult::InvalidMaterials: return "Invalid materials";
            case ItemCreationResult::InsufficientMaterials: return "Insufficient materials";
            case ItemCreationResult::InvalidRecipe: return "Invalid recipe";
            case ItemCreationResult::CreationFailed: return "Creation failed";
            case ItemCreationResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Trading statistics
 */
struct TradingStats {
    std::atomic<uint64_t> totalTrades{0};
    std::atomic<uint64_t> successfulTrades{0};
    std::atomic<uint64_t> failedTrades{0};
    std::atomic<uint64_t> totalValue{0};
    std::atomic<uint64_t> itemsCreated{0};
    std::atomic<uint64_t> storageOperations{0};
    std::chrono::system_clock::time_point lastTrade;
    
    TradingStats() : lastTrade(std::chrono::system_clock::now()) {}
    
    void RecordTrade(bool success, uint32_t value = 0) {
        totalTrades++;
        if (success) {
            successfulTrades++;
            totalValue += value;
        } else {
            failedTrades++;
        }
        lastTrade = std::chrono::system_clock::now();
    }
    
    double GetSuccessRate() const {
        uint64_t total = totalTrades.load();
        return total > 0 ? static_cast<double>(successfulTrades.load()) / total * 100.0 : 0.0;
    }
};

/**
 * Modern Item Trading and Storage System
 * Refactored from legacy decompiled C functions
 */
class CItemTradingSystem {
public:
    /**
     * Constructor
     */
    CItemTradingSystem();
    
    /**
     * Destructor
     */
    virtual ~CItemTradingSystem();
    
    /**
     * Initialize trading system
     * @return true if successful
     */
    bool Initialize();
    
    /**
     * Shutdown trading system
     */
    void Shutdown();
    
    /**
     * Process trade buy item request
     * Refactored from: ATradeBuyItemRequestCNetworkEXAEAA_NHPEADZ_1401D3A20.c
     * @param pNetwork Network instance
     * @param playerIndex Player index
     * @param pBuffer Request buffer
     * @return Trading operation result
     */
    TradingOperationDetails ProcessTradeBuyRequest(CNetworkEX* pNetwork, int playerIndex, char* pBuffer);
    
    /**
     * Process trade clear item request
     * Refactored from: ATradeClearItemRequestCNetworkEXAEAA_NHPEADZ_1401D3980.c
     * @param pNetwork Network instance
     * @param playerIndex Player index
     * @param pBuffer Request buffer
     * @return Trading operation result
     */
    TradingOperationDetails ProcessTradeClearRequest(CNetworkEX* pNetwork, int playerIndex, char* pBuffer);
    
    /**
     * Add item to storage
     * Refactored from: Emb_AddStorageCPlayerQEAAPEAU_db_con_STORAGE_LISTE_140057D90.c
     * @param context Storage operation context
     * @return Storage operation result
     */
    StorageOperationDetails AddItemToStorage(const StorageContext& context);
    
    /**
     * Create item for player
     * Refactored from: pc_MakeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIVGE0Z_1400AE750.c
     * @param context Item creation context
     * @return Item creation result
     */
    ItemCreationOperationDetails CreateItemForPlayer(const ItemCreationContext& context);
    
    /**
     * Process direct trade add request
     * Refactored from: pc_DTradeAddRequestCPlayerQEAAXEEKEZ_1400F4080.c
     * @param pPlayer Player instance
     * @param storageCode Storage code
     * @param position Item position
     * @param serial Item serial
     * @param amount Amount to trade
     * @return Trading operation result
     */
    TradingOperationDetails ProcessDirectTradeAdd(CPlayer* pPlayer, uint8_t storageCode, 
                                                 uint8_t position, uint32_t serial, uint8_t amount);
    
    /**
     * Validate trading context
     * @param context Trading context to validate
     * @return true if valid
     */
    bool ValidateTradingContext(const TradingContext& context);
    
    /**
     * Validate storage context
     * @param context Storage context to validate
     * @return true if valid
     */
    bool ValidateStorageContext(const StorageContext& context);
    
    /**
     * Validate item creation context
     * @param context Item creation context to validate
     * @return true if valid
     */
    bool ValidateItemCreationContext(const ItemCreationContext& context);
    
    /**
     * Get trading statistics
     * @return Current statistics
     */
    const TradingStats& GetStatistics() const { return m_stats; }
    
    /**
     * Reset statistics
     */
    void ResetStatistics();
    
    /**
     * Set trading callback
     * @param callback Trading callback function
     */
    void SetTradingCallback(std::function<void(const TradingOperationDetails&)> callback);
    
    /**
     * Enable/disable detailed logging
     * @param bEnable Enable flag
     */
    void SetDetailedLogging(bool bEnable) { m_bDetailedLogging = bEnable; }

protected:
    /**
     * Process unmanned trader buy request
     * @param playerIndex Player index
     * @param pRequest Buy request
     * @return true if successful
     */
    virtual bool ProcessUnmannedTraderBuy(int playerIndex, _unmannedtrader_buy_item_request_clzo* pRequest);
    
    /**
     * Process unmanned trader clear request
     * @param playerIndex Player index
     * @param pRequest Clear request
     * @return true if successful
     */
    virtual bool ProcessUnmannedTraderClear(int playerIndex, _a_trade_clear_item_request_clzo* pRequest);
    
    /**
     * Transfer item to storage
     * @param pPlayer Player instance
     * @param storageCode Storage code
     * @param pItem Item to transfer
     * @return Storage position or 255 if failed
     */
    virtual uint32_t TransferItemToStorage(CPlayer* pPlayer, uint8_t storageCode, _STORAGE_LIST* pItem);
    
    /**
     * Create storage item
     * @param context Item creation context
     * @return Created storage item or nullptr
     */
    virtual _STORAGE_LIST* CreateStorageItem(const ItemCreationContext& context);
    
    /**
     * Log trading operation
     * @param details Operation details
     */
    virtual void LogTradingOperation(const TradingOperationDetails& details);

private:
    TradingStats m_stats;
    std::function<void(const TradingOperationDetails&)> m_tradingCallback;
    bool m_bDetailedLogging{false};
    bool m_bInitialized{false};
    mutable std::mutex m_statsMutex;
    std::atomic<uint32_t> m_nextTransactionId{1};
    
    /**
     * Create trading result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete trading result
     */
    TradingOperationDetails CreateTradingResult(TradingResult result, 
                                               std::chrono::high_resolution_clock::time_point startTime,
                                               const std::string& errorMessage = "");
    
    /**
     * Create storage result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete storage result
     */
    StorageOperationDetails CreateStorageResult(StorageResult result, 
                                               std::chrono::high_resolution_clock::time_point startTime,
                                               const std::string& errorMessage = "");
    
    /**
     * Create item creation result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete item creation result
     */
    ItemCreationOperationDetails CreateItemCreationResult(ItemCreationResult result, 
                                                         std::chrono::high_resolution_clock::time_point startTime,
                                                         const std::string& errorMessage = "");
    
    /**
     * Generate unique transaction ID
     * @return Unique transaction ID
     */
    uint32_t GenerateTransactionId() { return m_nextTransactionId++; }
    
    // Disable copy constructor and assignment operator
    CItemTradingSystem(const CItemTradingSystem&) = delete;
    CItemTradingSystem& operator=(const CItemTradingSystem&) = delete;
};

/**
 * Legacy compatibility functions
 * Maintain exact signatures for backward compatibility
 */
namespace LegacyCompatibility {
    /**
     * Legacy trade buy item request wrapper
     * @param pNetwork Network instance
     * @param playerIndex Player index
     * @param pBuffer Request buffer
     * @return 1 if successful, 0 if failed
     */
    char ATradeBuyItemRequest_Legacy(CNetworkEX* pNetwork, int playerIndex, char* pBuffer);
    
    /**
     * Legacy trade clear item request wrapper
     * @param pNetwork Network instance
     * @param playerIndex Player index
     * @param pBuffer Request buffer
     * @return 1 if successful, 0 if failed
     */
    char ATradeClearItemRequest_Legacy(CNetworkEX* pNetwork, int playerIndex, char* pBuffer);
    
    /**
     * Legacy add storage wrapper
     * @param pPlayer Player instance
     * @param storageCode Storage code
     * @param pItem Item to add
     * @param bEquipChange Equipment change flag
     * @param bAdd Add flag
     * @return Storage item or nullptr
     */
    _STORAGE_LIST* Emb_AddStorage_Legacy(CPlayer* pPlayer, char storageCode, _STORAGE_LIST* pItem, 
                                        bool bEquipChange, bool bAdd);
    
    /**
     * Legacy make item wrapper
     * @param pPlayer Player instance
     * @param pMakeTool Make tool
     * @param manualIndex Manual index
     * @param materialNum Material number
     * @param pMaterials Materials array
     */
    void pc_MakeItem_Legacy(CPlayer* pPlayer, _STORAGE_POS_INDIV* pMakeTool, uint16_t manualIndex, 
                           uint8_t materialNum, _STORAGE_POS_INDIV* pMaterials);
}

} // namespace Items
} // namespace NexusProtection
