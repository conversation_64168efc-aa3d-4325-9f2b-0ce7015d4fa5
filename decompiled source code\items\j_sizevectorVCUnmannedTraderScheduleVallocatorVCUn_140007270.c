/*
 * Function: j_?size@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEBA_KXZ
 * Address: 0x140007270
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(this);
}
