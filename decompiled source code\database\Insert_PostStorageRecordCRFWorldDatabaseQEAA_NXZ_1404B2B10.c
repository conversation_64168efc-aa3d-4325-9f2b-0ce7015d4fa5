/*
 * Function: ?Insert_PostStorageRecord@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404B2B10
 */

bool __fastcall CRFWorldDatabase::Insert_PostStorageRecord(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char DstBuf; // [sp+30h] [bp-68h]@4
  char v6; // [sp+31h] [bp-67h]@4
  unsigned __int64 v7; // [sp+80h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+A0h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  DstBuf = 0;
  memset(&v6, 0, 0x3Fui64);
  sprintf_s(&DstBuf, 0x40ui64, "{ CALL pInsert_PostStorageRecord }");
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &DstBuf, 1);
}
