/*
 * Function: j_??0?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@QEAA@AEBV01@@Z
 * Address: 0x14000E86D
 */

void __fastcall std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocator<CUnmannedTraderGroupDivisionVersionInfo>(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *this, std::allocator<CUnmannedTraderGroupDivisionVersionInfo> *__formal)
{
  std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::allocator<CUnmannedTraderGroupDivisionVersionInfo>(
    this,
    __formal);
}
