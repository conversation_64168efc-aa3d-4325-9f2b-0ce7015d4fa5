/*
 * Function: j_?assign@?$vector@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@QEAAX_KAEBVCMoveMapLimitRightInfo@@@Z
 * Address: 0x1400059B6
 */

void __fastcall std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::assign(std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, unsigned __int64 _Count, CMoveMapLimitRightInfo *_Val)
{
  std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::assign(this, _Count, _Val);
}
