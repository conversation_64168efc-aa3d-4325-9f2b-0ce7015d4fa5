/*
 * Function: ?ExponentiateBase@?$DL_GroupParameters@VInteger@CryptoPP@@@CryptoPP@@UEBA?AVInteger@2@AEBV32@@Z
 * Address: 0x140551FF0
 */

__int64 __fastcall CryptoPP::DL_GroupParameters<CryptoPP::Integer>::ExponentiateBase(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v3; // rax@1
  __int64 v4; // ST28_8@1
  __int64 v5; // rax@1
  __int64 v7; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1

  v9 = a3;
  v8 = a2;
  v7 = a1;
  LODWORD(v3) = (*(int (**)(void))(*(_QWORD *)a1 + 48i64))();
  v4 = v3;
  LODWORD(v5) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v7 + 40i64))(v7);
  (*(void (__fastcall **)(__int64, __int64, __int64, __int64))(*(_QWORD *)v4 + 48i64))(v4, v8, v5, v9);
  return v8;
}
