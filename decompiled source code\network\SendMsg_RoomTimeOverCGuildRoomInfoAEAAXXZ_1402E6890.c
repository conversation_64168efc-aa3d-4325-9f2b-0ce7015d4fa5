/*
 * Function: ?SendMsg_RoomTimeOver@CGuildRoomInfo@@AEAAXXZ
 * Address: 0x1402E6890
 */

void __fastcall CGuildRoomInfo::SendMsg_RoomTimeOver(CGuildRoomInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CGuild *v4; // [sp+20h] [bp-18h]@4
  CGuildRoomInfo *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = 0i64;
  v4 = &g_Guild[v5->m_iGuildIdx];
  if ( v4 )
  {
    if ( v4->m_dwSerial == v5->m_dwGuildSerial )
      CGuild::SendMsg_GuildRoomRented(v4, 100);
  }
}
