/*
 * Function: ??$_Fill_n@PEAPEAVCUnmannedTraderSortType@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderSortType@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140376940
 */

void __fastcall std::_Fill_n<CUnmannedTraderSortType * *,unsigned __int64,CUnmannedTraderSortType *>(CUnmannedTraderSortType **_First, unsigned __int64 _Count, CUnmannedTraderSortType *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  memset64(_First, (unsigned __int64)*_Val, _Count);
}
