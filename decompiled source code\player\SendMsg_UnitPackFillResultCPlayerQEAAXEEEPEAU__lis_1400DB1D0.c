/*
 * Function: ?SendMsg_UnitPackFillResult@CPlayer@@QEAAXEEEPEAU__list@_unit_pack_fill_request_clzo@@PEAK@Z
 * Address: 0x1400DB1D0
 */

void __fastcall CPlayer::SendMsg_UnitPackFillResult(CPlayer *this, char byRetCode, char bySlotIndex, char byFill<PERSON>um, _unit_pack_fill_request_clzo::__list *pList, unsigned int *pdwConsumMoney)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-D8h]@1
  _unit_pack_fill_result_zocl Dst; // [sp+40h] [bp-98h]@4
  char pbyType; // [sp+B4h] [bp-24h]@4
  char v11; // [sp+B5h] [bp-23h]@4
  CPlayer *v12; // [sp+E0h] [bp+8h]@1
  char v13; // [sp+E8h] [bp+10h]@1
  char v14; // [sp+F0h] [bp+18h]@1
  char v15; // [sp+F8h] [bp+20h]@1

  v15 = byFillNum;
  v14 = bySlotIndex;
  v13 = byRetCode;
  v12 = this;
  v6 = &v8;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  _unit_pack_fill_result_zocl::_unit_pack_fill_result_zocl(&Dst);
  Dst.byRetCode = v13;
  Dst.bySlotIndex = v14;
  Dst.byFillNum = v15;
  memcpy_0(Dst.List, pList, 3i64 * (unsigned __int8)v15);
  Dst.dwComsumMoney[0] = *pdwConsumMoney;
  Dst.dwComsumMoney[1] = pdwConsumMoney[1];
  Dst.dwLeftMoney[0] = CPlayerDB::GetDalant(&v12->m_Param);
  Dst.dwLeftMoney[1] = CPlayerDB::GetGold(&v12->m_Param);
  pbyType = 23;
  v11 = 12;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &Dst.byRetCode, 0x53u);
}
