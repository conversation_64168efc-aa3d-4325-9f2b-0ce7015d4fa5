/*
 * Function: ?VerifyPoint@EC2N@CryptoPP@@QEBA_NAEBUEC2NPoint@2@@Z
 * Address: 0x14062E370
 */

char __fastcall CryptoPP::EC2N::VerifyPoint(CryptoPP::EC2N *this, const struct CryptoPP::EC2NPoint *a2)
{
  CryptoPP::GF2NP *v2; // rax@2
  CryptoPP::GF2NP *v3; // rax@3
  __int64 v4; // rax@4
  CryptoPP::PolynomialMod2 *v6; // [sp+20h] [bp-198h]@1
  CryptoPP::PolynomialMod2 *v7; // [sp+28h] [bp-190h]@1
  char v8; // [sp+30h] [bp-188h]@7
  char v9; // [sp+38h] [bp-180h]@4
  char v10; // [sp+50h] [bp-168h]@4
  char v11; // [sp+68h] [bp-150h]@4
  char v12; // [sp+80h] [bp-138h]@4
  char v13; // [sp+98h] [bp-120h]@4
  char v14; // [sp+B0h] [bp-108h]@4
  char v15; // [sp+C8h] [bp-F0h]@4
  char v16; // [sp+E0h] [bp-D8h]@4
  int v17; // [sp+F8h] [bp-C0h]@1
  __int64 v18; // [sp+100h] [bp-B8h]@1
  unsigned int v19; // [sp+108h] [bp-B0h]@2
  unsigned int v20; // [sp+10Ch] [bp-ACh]@3
  const struct CryptoPP::PolynomialMod2 *v21; // [sp+110h] [bp-A8h]@4
  CryptoPP::PolynomialMod2 *v22; // [sp+118h] [bp-A0h]@4
  CryptoPP::PolynomialMod2 *v23; // [sp+120h] [bp-98h]@4
  struct CryptoPP::PolynomialMod2 *v24; // [sp+128h] [bp-90h]@4
  const struct CryptoPP::PolynomialMod2 *v25; // [sp+130h] [bp-88h]@4
  const struct CryptoPP::PolynomialMod2 *v26; // [sp+138h] [bp-80h]@4
  CryptoPP::PolynomialMod2 *v27; // [sp+140h] [bp-78h]@4
  CryptoPP::PolynomialMod2 *v28; // [sp+148h] [bp-70h]@4
  CryptoPP::PolynomialMod2 *v29; // [sp+150h] [bp-68h]@4
  CryptoPP::PolynomialMod2 *v30; // [sp+158h] [bp-60h]@4
  CryptoPP::PolynomialMod2 *v31; // [sp+160h] [bp-58h]@4
  CryptoPP::PolynomialMod2 *v32; // [sp+168h] [bp-50h]@4
  CryptoPP::PolynomialMod2 *v33; // [sp+170h] [bp-48h]@4
  CryptoPP::PolynomialMod2 *v34; // [sp+178h] [bp-40h]@4
  CryptoPP::PolynomialMod2 *v35; // [sp+180h] [bp-38h]@4
  CryptoPP::PolynomialMod2 *v36; // [sp+188h] [bp-30h]@4
  struct CryptoPP::PolynomialMod2 *v37; // [sp+190h] [bp-28h]@4
  struct CryptoPP::PolynomialMod2 *v38; // [sp+198h] [bp-20h]@4
  int v39; // [sp+1A0h] [bp-18h]@5
  CryptoPP::EC2N *v40; // [sp+1C0h] [bp+8h]@1

  v40 = this;
  v18 = -2i64;
  v17 = 0;
  v7 = &a2->x;
  v6 = &a2->y;
  v39 = a2->identity
     || (v19 = CryptoPP::PolynomialMod2::CoefficientCount(v7),
         v2 = (CryptoPP::GF2NP *)CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->((__int64)&v40->m_field),
         v19 <= (unsigned int)CryptoPP::GF2NP::MaxElementBitLength(v2))
     && (v20 = CryptoPP::PolynomialMod2::CoefficientCount(v6),
         v3 = (CryptoPP::GF2NP *)CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->((__int64)&v40->m_field),
         v20 <= (unsigned int)CryptoPP::GF2NP::MaxElementBitLength(v3))
     && (v4 = CryptoPP::member_ptr<CryptoPP::GF2NP>::operator->((__int64)&v40->m_field),
         v21 = (const struct CryptoPP::PolynomialMod2 *)CryptoPP::QuotientRing<CryptoPP::EuclideanDomainOf<CryptoPP::PolynomialMod2>>::GetModulus(v4),
         v22 = CryptoPP::operator+((struct CryptoPP::PolynomialMod2 *)&v9, v7, v6),
         v23 = v22,
         v17 |= 1u,
         v24 = CryptoPP::operator*((struct CryptoPP::PolynomialMod2 *)&v10, v22, v6),
         v25 = v24,
         v17 |= 2u,
         v26 = &v40->m_b,
         v27 = CryptoPP::operator+((struct CryptoPP::PolynomialMod2 *)&v11, v7, &v40->m_a),
         v28 = v27,
         v17 |= 4u,
         v29 = CryptoPP::operator*((struct CryptoPP::PolynomialMod2 *)&v12, v27, v7),
         v30 = v29,
         v17 |= 8u,
         v31 = CryptoPP::operator*((struct CryptoPP::PolynomialMod2 *)&v13, v29, v7),
         v32 = v31,
         v17 |= 0x10u,
         v33 = CryptoPP::operator+((struct CryptoPP::PolynomialMod2 *)&v14, v31, v26),
         v34 = v33,
         v17 |= 0x20u,
         v35 = CryptoPP::operator-((struct CryptoPP::PolynomialMod2 *)&v15, v33, v25),
         v36 = v35,
         v17 |= 0x40u,
         v37 = CryptoPP::operator%((struct CryptoPP::PolynomialMod2 *)&v16, v35, v21),
         v38 = v37,
         v17 |= 0x80u,
         CryptoPP::PolynomialMod2::operator!(&v37->reg));
  v8 = v39;
  if ( v17 & 0x80 )
  {
    v17 &= 0xFFFFFF7F;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v16);
  }
  if ( v17 & 0x40 )
  {
    v17 &= 0xFFFFFFBF;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v15);
  }
  if ( v17 & 0x20 )
  {
    v17 &= 0xFFFFFFDF;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v14);
  }
  if ( v17 & 0x10 )
  {
    v17 &= 0xFFFFFFEF;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v13);
  }
  if ( v17 & 8 )
  {
    v17 &= 0xFFFFFFF7;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v12);
  }
  if ( v17 & 4 )
  {
    v17 &= 0xFFFFFFFB;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v11);
  }
  if ( v17 & 2 )
  {
    v17 &= 0xFFFFFFFD;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v10);
  }
  if ( v17 & 1 )
  {
    v17 &= 0xFFFFFFFE;
    CryptoPP::PolynomialMod2::~PolynomialMod2(&v9);
  }
  return v8;
}
