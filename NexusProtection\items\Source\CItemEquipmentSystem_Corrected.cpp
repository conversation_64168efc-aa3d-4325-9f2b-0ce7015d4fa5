/*
 * CItemEquipmentSystem.cpp - Modern Item Equipment and Store System Implementation (CORRECTED)
 * Refactored from decompiled C item equipment, store, and special item functions
 * CORRECTED to use accurate equipment slot interpretations based on actual game data
 */

#include "../Headers/CItemEquipmentSystem.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CPlayer.h"

#include <algorithm>
#include <chrono>
#include <stdexcept>
#include <cstring>

// External references to legacy systems
extern "C" {
    // Legacy function declarations
    class CLuaLootingMgr;
    class CMapData;
    struct _STORAGE_LIST;
    struct _golden_box_item;
    struct _itembox_create_setdata;
    struct _object_create_setdata;
    
    extern void memset_0(void* dest, int value, size_t count);
    extern void _object_create_setdata_Constructor(_object_create_setdata* pData);
    extern void _STORAGE_LIST_db_con_Constructor(_STORAGE_LIST* pItem);
    extern bool CEquipItemSFAgent_AllEndContSF(void* pAgent);
    extern uint32_t CLuaLootingMgr_SearchSlotIndex(CLuaLootingMgr* pMgr, void* pState);
    extern void* CLuaLootingMgr_GetStateAtPtr(CLuaLootingMgr* pMgr, uint32_t index);
    extern bool IsValidEquipmentSlot(int tableCode, uint8_t slot);
    extern bool CheckPlayerLevel(CPlayer* pPlayer, int requiredLevel);
    extern bool CheckPlayerClass(CPlayer* pPlayer, int requiredClass);
    extern uint32_t GetItemPrice(const char* itemCode, int storeType);
    extern bool DeductPlayerMoney(CPlayer* pPlayer, uint32_t amount);
    extern bool AddPlayerMoney(CPlayer* pPlayer, uint32_t amount);
}

namespace NexusProtection {
namespace Items {

/**
 * Constructor
 */
CItemEquipmentSystem::CItemEquipmentSystem() 
    : m_bDetailedLogging(false), m_bInitialized(false) {
    
    Logger::Debug("CItemEquipmentSystem::CItemEquipmentSystem - Item equipment system created");
}

/**
 * Destructor
 */
CItemEquipmentSystem::~CItemEquipmentSystem() {
    try {
        Shutdown();
        Logger::Debug("CItemEquipmentSystem::~CItemEquipmentSystem - Item equipment system destroyed");
    } catch (const std::exception& e) {
        // Can't log safely during destruction
    }
}

/**
 * Initialize equipment system
 */
bool CItemEquipmentSystem::Initialize() {
    try {
        if (m_bInitialized) {
            Logger::Warning("CItemEquipmentSystem::Initialize - Already initialized");
            return true;
        }
        
        // Reset statistics
        ResetStatistics();
        
        m_bInitialized = true;
        Logger::Info("CItemEquipmentSystem::Initialize - Item equipment system initialized");
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::Initialize - Exception: %s", e.what());
        return false;
    }
}

/**
 * Shutdown equipment system
 */
void CItemEquipmentSystem::Shutdown() {
    try {
        if (!m_bInitialized) {
            return;
        }
        
        m_bInitialized = false;
        Logger::Info("CItemEquipmentSystem::Shutdown - Item equipment system shutdown");
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::Shutdown - Exception: %s", e.what());
    }
}

/**
 * Get equipment slot for item
 * CORRECTED based on proper understanding of item code prefixes
 */
EquipmentSlot CItemEquipmentSystem::GetEquipmentSlot(_STORAGE_LIST* pItem) {
    try {
        if (!pItem) {
            return EquipmentSlot::None;
        }
        
        // Map table codes to equipment slots based on CORRECTED interpretation
        // Equipment items have table codes 0-7 (validated in pc_EquipPart)
        switch (pItem->m_byTableCode) {
            case 0: return EquipmentSlot::UpperArmor;  // "iu" - Upper body armor/clothing
            case 1: return EquipmentSlot::LowerArmor;  // "il" - Lower body armor/clothing
            case 2: return EquipmentSlot::Gloves;      // "ig" - Gloves/hand equipment
            case 3: return EquipmentSlot::Shoes;       // "is" - Shoes/foot equipment
            case 4: return EquipmentSlot::Helmet;      // "ih" - Helmet/head equipment
            case 5: return EquipmentSlot::Shield;      // "id" - Shield/defensive equipment
            case 6: return EquipmentSlot::Weapon;      // "iw" - Weapon/offensive equipment
            case 7: return EquipmentSlot::Cloak;       // "ik" - Cloak/cape equipment
            
            // Non-equipment items
            case 8: return EquipmentSlot::Ring;        // "ii" - Ring/finger equipment
            case 9: return EquipmentSlot::Amulet;      // "ia" - Amulet/neck equipment
            case 10: return EquipmentSlot::Bullet;     // "ib" - Bullet/ammunition
            case 11: return EquipmentSlot::Material;   // "im" - Crafting tools/materials
            
            default: return EquipmentSlot::None;
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::GetEquipmentSlot - Exception: %s", e.what());
        return EquipmentSlot::None;
    }
}

/**
 * Check equipment requirements
 * Based on actual game logic from pc_EquipPart function
 */
bool CItemEquipmentSystem::CheckEquipmentRequirements(CPlayer* pPlayer, _STORAGE_LIST* pItem, EquipmentSlot slot) {
    try {
        if (!pPlayer || !pItem) {
            return false;
        }
        
        // Check if item is actually equipment (table codes 0-7 from pc_EquipPart line 55)
        if (pItem->m_byTableCode >= 8) {
            Logger::Warning("CItemEquipmentSystem::CheckEquipmentRequirements - Item is not equipment, table code: %d", 
                           pItem->m_byTableCode);
            return false;
        }
        
        // Check if item is locked (from pc_EquipPart line 57-60)
        if (pItem->m_bLock) {
            Logger::Warning("CItemEquipmentSystem::CheckEquipmentRequirements - Item is locked");
            return false;
        }
        
        // Check equipment grade requirements (from pc_EquipPart line 70-76)
        // In real implementation, this would call GetItemEquipGrade and IsEquipAbleGrade
        // For now, assume grade check passes
        
        // Check if slot matches item type
        EquipmentSlot itemSlot = GetEquipmentSlot(pItem);
        if (itemSlot != slot && slot != EquipmentSlot::None) {
            Logger::Warning("CItemEquipmentSystem::CheckEquipmentRequirements - Item slot mismatch: expected %d, got %d", 
                           static_cast<int>(slot), static_cast<int>(itemSlot));
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::CheckEquipmentRequirements - Exception: %s", e.what());
        return false;
    }
}

/**
 * Validate equipment context
 */
bool CItemEquipmentSystem::ValidateEquipmentContext(const EquipmentContext& context) {
    try {
        if (!context.IsValid()) {
            Logger::Warning("CItemEquipmentSystem::ValidateEquipmentContext - Invalid context");
            return false;
        }
        
        if (context.slot >= EquipmentSlot::MaxEquipSlots && context.slot != EquipmentSlot::None) {
            Logger::Warning("CItemEquipmentSystem::ValidateEquipmentContext - Invalid equipment slot: %d", 
                           static_cast<int>(context.slot));
            return false;
        }
        
        return true;
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ValidateEquipmentContext - Exception: %s", e.what());
        return false;
    }
}

/**
 * Reset statistics
 */
void CItemEquipmentSystem::ResetStatistics() {
    try {
        std::lock_guard<std::mutex> lock(m_statsMutex);
        
        m_stats.totalEquipOperations = 0;
        m_stats.successfulEquips = 0;
        m_stats.failedEquips = 0;
        m_stats.storeTransactions = 0;
        m_stats.specialItemsOpened = 0;
        m_stats.totalRevenue = 0;
        m_stats.lastOperation = std::chrono::system_clock::now();
        
        Logger::Debug("CItemEquipmentSystem::ResetStatistics - Statistics reset");
        
    } catch (const std::exception& e) {
        Logger::Error("CItemEquipmentSystem::ResetStatistics - Exception: %s", e.what());
    }
}

/**
 * Set equipment callback
 */
void CItemEquipmentSystem::SetEquipmentCallback(std::function<void(const EquipmentOperationDetails&)> callback) {
    m_equipmentCallback = callback;
}

/**
 * Create equipment result with timing
 */
EquipmentOperationDetails CItemEquipmentSystem::CreateEquipmentResult(EquipmentResult result, 
                                                                     std::chrono::high_resolution_clock::time_point startTime,
                                                                     const std::string& errorMessage) {
    EquipmentOperationDetails equipmentResult;
    equipmentResult.result = result;
    equipmentResult.errorMessage = errorMessage;
    equipmentResult.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);
    
    return equipmentResult;
}

/**
 * Legacy compatibility functions
 */
namespace LegacyCompatibility {

/**
 * Legacy equipment agent constructor wrapper
 */
void CEquipItemSFAgent_Constructor_Legacy(void* pAgent) {
    try {
        if (!pAgent) {
            Logger::Error("LegacyCompatibility::CEquipItemSFAgent_Constructor_Legacy - Invalid agent pointer");
            return;
        }
        
        // Initialize stack variables (original lines 14-19)
        int stackInit[8];
        for (int i = 0; i < 8; ++i) {
            stackInit[i] = -858993460; // Original magic number
        }
        
        // Initialize agent (original lines 20-21)
        *reinterpret_cast<void**>(pAgent) = nullptr; // m_pMaster = 0
        CEquipItemSFAgent_AllEndContSF(pAgent);
        
    } catch (const std::exception& e) {
        Logger::Error("LegacyCompatibility::CEquipItemSFAgent_Constructor_Legacy - Exception: %s", e.what());
    }
}

} // namespace LegacyCompatibility

} // namespace Items
} // namespace NexusProtection
