/*
 * Function: ??$_Uninit_fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@V?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@AEAV?$allocator@PEAUMessageRange@MeterFilter@CryptoPP@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140602340
 */

int std::_Uninit_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *,std::allocator<CryptoPP::MeterFilter::MessageRange *>>()
{
  return stdext::unchecked_fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *>();
}
