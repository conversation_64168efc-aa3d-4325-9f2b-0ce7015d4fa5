/*
 * Function: ?pc_ProposeVoteRequest@CPlayer@@QEAAXEPEAD@Z
 * Address: 0x140108450
 */

void __fastcall CPlayer::pc_ProposeVoteRequest(CPlayer *this, char byLimGrade, char *pwszCont)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@5
  int v6; // eax@10
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@4
  int v9; // [sp+24h] [bp-14h]@5
  int v10; // [sp+28h] [bp-10h]@10
  CPlayer *v11; // [sp+40h] [bp+8h]@1
  char v12; // [sp+48h] [bp+10h]@1
  char *pwszContent; // [sp+50h] [bp+18h]@1

  pwszContent = pwszCont;
  v12 = byLimGrade;
  v11 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = 0;
  if ( CMainThread::IsReleaseServiceMode(&g_Main)
    && (v9 = CPlayerDB::GetRaceCode(&v11->m_Param),
        v5 = CPvpUserAndGuildRankingSystem::Instance(),
        CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v9, 0) != v11->m_dwObjSerial) )
  {
    v8 = 1;
  }
  else if ( LOBYTE(g_VoteSys[760 * CPlayerDB::GetRaceCode(&v11->m_Param)]) )
  {
    v8 = 2;
  }
  if ( !v8 )
  {
    v10 = CPlayerDB::GetRaceCode(&v11->m_Param);
    v6 = CPlayerDB::GetRaceCode(&v11->m_Param);
    if ( !CVoteSystem::StartVote((CVoteSystem *)&g_VoteSys[760 * v6], pwszContent, v12, v10) )
      v8 = 7;
  }
  CPlayer::SendMsg_ProposeVoteResult(v11, v8);
}
