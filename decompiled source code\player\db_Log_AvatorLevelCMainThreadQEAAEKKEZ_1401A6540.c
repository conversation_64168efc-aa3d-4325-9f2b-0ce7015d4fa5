/*
 * Function: ?db_Log_AvatorLevel@CMainThread@@QEAAEKKE@Z
 * Address: 0x1401A6540
 */

char __fastcall CMainThread::db_Log_AvatorLevel(CMainThread *this, unsigned int dwTotalPlayMin, unsigned int dwSerial, char byLv)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CMainThread *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CRFWorldDatabase::Insert_Level_Log(v8->m_pWorldDB, dwSerial, byLv, dwTotalPlayMin) )
    result = 0;
  else
    result = 24;
  return result;
}
