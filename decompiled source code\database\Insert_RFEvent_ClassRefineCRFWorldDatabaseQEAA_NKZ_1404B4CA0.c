/*
 * Function: ?Insert_RFEvent_ClassRefine@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404B4CA0
 */

bool __fastcall CRFWorldDatabase::Insert_RFEvent_ClassRefine(CRFWorldDatabase *this, unsigned int dwAvatorSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-158h]@1
  char DstBuf; // [sp+30h] [bp-128h]@4
  char v7; // [sp+31h] [bp-127h]@4
  unsigned __int64 v8; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+160h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v7, 0, 0xFFui64);
  sprintf_s(&DstBuf, 0x100ui64, "insert [dbo].[tbl_event] (avatorserial) values (%u)", dwAvatorSerial);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &DstBuf, 1);
}
