/*
 * Function: ?pc_TrunkCreateCostIsFreeRequest@CPlayer@@QEAAEXZ
 * Address: 0x1400FB2A0
 */

char __fastcall CPlayer::pc_TrunkCreateCostIsFreeRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&v5->m_Param) <= 0 )
    result = v5->m_pUserDB->m_bCreateTrunkFree == 0;
  else
    result = 2;
  return result;
}
