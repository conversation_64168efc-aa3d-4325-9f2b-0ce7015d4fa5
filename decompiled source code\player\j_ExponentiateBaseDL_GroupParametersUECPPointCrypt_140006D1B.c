/*
 * Function: j_?ExponentiateBase@?$DL_GroupParameters@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBVInteger@2@@Z
 * Address: 0x140006D1B
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::ExponentiateBase(CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint> *this, CryptoPP::ECPPoint *result, CryptoPP::Integer *exponent)
{
  return CryptoPP::DL_GroupParameters<CryptoPP::ECPPoint>::ExponentiateBase(this, result, exponent);
}
