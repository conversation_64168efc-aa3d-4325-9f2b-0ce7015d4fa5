/*
 * Function: j_??$_Uninit_fill_n@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KAEBV12@AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000B6C2
 */

void __fastcall std::_Uninit_fill_n<GUILD_BATTLE::CGuildBattleRewardItem *,unsigned __int64,GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(GUILD_BATTLE::CGuildBattleRewardItem *_First, unsigned __int64 _Count, GUILD_BATTLE::CGuildBattleRewardItem *_Val, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<GUILD_BATTLE::CGuildBattleRewardItem *,unsigned __int64,GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
