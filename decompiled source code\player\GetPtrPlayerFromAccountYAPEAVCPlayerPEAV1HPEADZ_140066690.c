/*
 * Function: ?GetPtrPlayerFromAccount@@YAPEAVCPlayer@@PEAV1@HPEAD@Z
 * Address: 0x140066690
 */

CPlayer *__fastcall GetPtrPlayerFromAccount(CPlayer *pData, int nNum, char *szAccount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  int v9; // [sp+48h] [bp+10h]@1
  const char *Str2; // [sp+50h] [bp+18h]@1

  Str2 = szAccount;
  v9 = nNum;
  v8 = pData;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < v9; ++j )
  {
    if ( v8[j].m_bLive && !strcmp_0(v8[j].m_pUserDB->m_szAccountID, Str2) )
      return &v8[j];
  }
  return 0i64;
}
