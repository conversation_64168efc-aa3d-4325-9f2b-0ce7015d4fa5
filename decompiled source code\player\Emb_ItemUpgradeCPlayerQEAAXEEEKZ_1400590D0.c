/*
 * Function: ?Emb_ItemUpgrade@CPlayer@@QEAAXEEEK@Z
 * Address: 0x1400590D0
 */

void __fastcall CPlayer::Emb_ItemUpgrade(CPlayer *this, char byUpgradeType, char byStorageCode, char byStorageIndex, unsigned int dwGradeInfo)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@14
  CPlayer::CashChangeStateFlag *v8; // rax@22
  __int64 v9; // [sp+0h] [bp-58h]@1
  bool bUpdate; // [sp+20h] [bp-38h]@24
  _STORAGE_LIST::_storage_con *pItem; // [sp+30h] [bp-28h]@4
  unsigned int dwUptInfo; // [sp+38h] [bp-20h]@14
  CPlayer::CashChangeStateFlag v13; // [sp+3Ch] [bp-1Ch]@22
  char v14; // [sp+40h] [bp-18h]@8
  CPlayer *v15; // [sp+60h] [bp+8h]@1
  char v16; // [sp+68h] [bp+10h]@1
  char v17; // [sp+70h] [bp+18h]@1
  char v18; // [sp+78h] [bp+20h]@1

  v18 = byStorageIndex;
  v17 = byStorageCode;
  v16 = byUpgradeType;
  v15 = this;
  v5 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  pItem = (_STORAGE_LIST::_storage_con *)&v15->m_Param.m_pStoragePtr[(unsigned __int8)byStorageCode]->m_pStorageList[(unsigned __int8)byStorageIndex].m_bLoad;
  if ( byStorageCode == 1 )
  {
    if ( CPlayer::GetEffectEquipCode(v15, byStorageCode, byStorageIndex) == 1 )
      CPlayer::SetEquipEffect(v15, pItem, 0);
    CPlayer::SetEffectEquipCode(v15, v17, v18, 0);
  }
  v14 = v16;
  if ( v16 )
  {
    if ( v14 == 1 )
    {
      _STORAGE_LIST::GradeDown(v15->m_Param.m_pStoragePtr[(unsigned __int8)v17], (unsigned __int8)v18, dwGradeInfo);
    }
    else if ( v14 == 2 )
    {
      v7 = GetItemUpgLimSocket(pItem->m_dwLv);
      dwUptInfo = GetBitAfterSetLimSocket(v7);
      _STORAGE_LIST::SetGrade(v15->m_Param.m_pStoragePtr[(unsigned __int8)v17], (unsigned __int8)v18, 0, dwUptInfo);
    }
  }
  else
  {
    _STORAGE_LIST::GradeUp(v15->m_Param.m_pStoragePtr[(unsigned __int8)v17], (unsigned __int8)v18, dwGradeInfo);
  }
  if ( v17 == 1 )
  {
    if ( CPlayer::IsEffectableEquip(v15, pItem) )
    {
      CPlayer::SetEquipEffect(v15, pItem, 1);
      CPlayer::SetEffectEquipCode(v15, v17, v18, 1);
    }
    else
    {
      CPlayer::SetEffectEquipCode(v15, v17, v18, 2);
    }
    if ( pItem->m_byTableCode == 6 )
      _WEAPON_PARAM::FixWeapon(&v15->m_pmWpn, (_STORAGE_LIST::_db_con *)pItem);
  }
  if ( v17 == 1 )
  {
    CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v13, 0);
    CPlayer::UpdateVisualVer(v15, (CPlayer::CashChangeStateFlag)v8->0);
    CPlayer::SendMsg_EquipPartChange(v15, pItem->m_byTableCode);
  }
  if ( v15->m_pUserDB )
  {
    bUpdate = 1;
    CUserDB::Update_ItemUpgrade(v15->m_pUserDB, v17, v18, dwGradeInfo, 1);
  }
}
