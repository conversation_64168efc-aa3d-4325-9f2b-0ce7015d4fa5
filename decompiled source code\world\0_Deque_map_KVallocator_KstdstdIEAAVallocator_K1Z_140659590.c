/*
 * Function: ??0?$_Deque_map@_KV?$allocator@_K@std@@@std@@IEAA@V?$allocator@_K@1@@Z
 * Address: 0x140659590
 */

std::_Container_base *__fastcall std::_Deque_map<unsigned __int64,std::allocator<unsigned __int64>>::_Deque_map<unsigned __int64,std::allocator<unsigned __int64>>(std::_Container_base *a1, __int64 a2)
{
  std::_Container_base *v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Container_base::_Container_base(a1);
  std::allocator<unsigned __int64 *>::allocator<unsigned __int64 *>(&v3[1], v4);
  return v3;
}
