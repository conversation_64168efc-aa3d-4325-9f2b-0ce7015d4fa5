/*
 * Function: _CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_1_::dtor$0
 * Address: 0x14045FAC0
 */

__int64 CryptoPP::Singleton_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0__CryptoPP::NewObject_CryptoPP::DL_EncryptionAlgorithm_Xor_CryptoPP::HMAC_CryptoPP::SHA1__0____0_::Ref_::_1_::dtor_0()
{
  __int64 result; // rax@1

  result = _S6 & 0xFFFFFFFE;
  _S6 &= 0xFFFFFFFE;
  return result;
}
