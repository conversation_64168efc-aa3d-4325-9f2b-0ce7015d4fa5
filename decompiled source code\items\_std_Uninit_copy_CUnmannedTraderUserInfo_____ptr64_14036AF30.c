/*
 * Function: _std::_Uninit_copy_CUnmannedTraderUserInfo_____ptr64_CUnmannedTraderUserInfo_____ptr64_std::allocator_CUnmannedTraderUserInfo____::_1_::catch$0
 * Address: 0x14036AF30
 */

void __fastcall __noreturn std::_Uninit_copy_CUnmannedTraderUserInfo_____ptr64_CUnmannedTraderUserInfo_____ptr64_std::allocator_CUnmannedTraderUserInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 80); *(_QWORD *)(i + 32) += 104i64 )
    std::allocator<CUnmannedTraderUserInfo>::destroy(
      *(std::allocator<CUnmannedTraderUserInfo> **)(i + 88),
      *(CUnmannedTraderUserInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
