/*
 * Function: ?_Insert_n@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAAXV?$_Vector_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@2@_KAEBVCUnmannedTraderItemCodeInfo@@@Z
 * Address: 0x1403790C0
 */

void __fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Insert_n(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Where, unsigned __int64 _Count, CUnmannedTraderItemCodeInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v6; // rax@5
  unsigned __int64 v7; // rax@7
  unsigned __int64 v8; // rax@8
  unsigned __int64 v9; // rax@11
  __int64 v10; // [sp+0h] [bp-F8h]@1
  CUnmannedTraderItemCodeInfo _Vala; // [sp+30h] [bp-C8h]@4
  unsigned __int64 _Counta; // [sp+88h] [bp-70h]@4
  CUnmannedTraderItemCodeInfo *_Ptr; // [sp+90h] [bp-68h]@13
  CUnmannedTraderItemCodeInfo *v14; // [sp+98h] [bp-60h]@13
  CUnmannedTraderItemCodeInfo *_Last; // [sp+A0h] [bp-58h]@18
  __int64 v16; // [sp+B0h] [bp-48h]@4
  unsigned __int64 v17; // [sp+B8h] [bp-40h]@5
  unsigned __int64 v18; // [sp+C0h] [bp-38h]@8
  unsigned __int64 v19; // [sp+C8h] [bp-30h]@9
  CUnmannedTraderItemCodeInfo *v20; // [sp+D0h] [bp-28h]@13
  CUnmannedTraderItemCodeInfo *v21; // [sp+D8h] [bp-20h]@13
  unsigned __int64 v22; // [sp+E0h] [bp-18h]@4
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v23; // [sp+100h] [bp+8h]@1
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v24; // [sp+108h] [bp+10h]@1
  unsigned __int64 v25; // [sp+110h] [bp+18h]@1
  unsigned __int64 v26; // [sp+110h] [bp+18h]@13

  v25 = _Count;
  v24 = _Where;
  v23 = this;
  v4 = &v10;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  v22 = (unsigned __int64)&v10 ^ _security_cookie;
  CUnmannedTraderItemCodeInfo::CUnmannedTraderItemCodeInfo(&_Vala, _Val);
  _Counta = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::capacity(v23);
  if ( v25 )
  {
    v17 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(v23);
    v6 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::max_size(v23);
    if ( v6 - v17 < v25 )
      std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Xlen();
    v7 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(v23);
    if ( _Counta >= v25 + v7 )
    {
      if ( (unsigned int)((char *)v23->_Mylast - (char *)v24->_Myptr) / 72i64 >= v25 )
      {
        _Last = v23->_Mylast;
        v23->_Mylast = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Umove<CUnmannedTraderItemCodeInfo *>(
                         v23,
                         &_Last[-v25],
                         _Last,
                         v23->_Mylast);
        stdext::_Unchecked_move_backward<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo *>(
          v24->_Myptr,
          &_Last[-v25],
          _Last);
        std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(v24->_Myptr, &v24->_Myptr[v25], &_Vala);
      }
      else
      {
        std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Umove<CUnmannedTraderItemCodeInfo *>(
          v23,
          v24->_Myptr,
          v23->_Mylast,
          &v24->_Myptr[v25]);
        std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Ufill(
          v23,
          v23->_Mylast,
          v25 - (unsigned int)((char *)v23->_Mylast - (char *)v24->_Myptr) / 72i64,
          &_Vala);
        v23->_Mylast += v25;
        std::fill<CUnmannedTraderItemCodeInfo *,CUnmannedTraderItemCodeInfo>(v24->_Myptr, &v23->_Mylast[-v25], &_Vala);
      }
    }
    else
    {
      v18 = _Counta / 2;
      v8 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::max_size(v23);
      if ( v8 - v18 >= _Counta )
        v19 = _Counta / 2 + _Counta;
      else
        v19 = 0i64;
      _Counta = v19;
      v9 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(v23);
      if ( _Counta < v25 + v9 )
        _Counta = v25 + std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(v23);
      _Ptr = std::allocator<CUnmannedTraderItemCodeInfo>::allocate(&v23->_Alval, _Counta);
      v14 = _Ptr;
      v20 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Umove<CUnmannedTraderItemCodeInfo *>(
              v23,
              v23->_Myfirst,
              v24->_Myptr,
              _Ptr);
      v14 = v20;
      v21 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Ufill(
              v23,
              v20,
              v25,
              &_Vala);
      v14 = v21;
      std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Umove<CUnmannedTraderItemCodeInfo *>(
        v23,
        v24->_Myptr,
        v23->_Mylast,
        v21);
      v26 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::size(v23) + v25;
      if ( v23->_Myfirst )
      {
        std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(
          v23,
          v23->_Myfirst,
          v23->_Mylast);
        std::allocator<CUnmannedTraderItemCodeInfo>::deallocate(
          &v23->_Alval,
          v23->_Myfirst,
          (unsigned int)((char *)v23->_Myend - (char *)v23->_Myfirst) / 72i64);
      }
      v23->_Myend = &_Ptr[_Counta];
      v23->_Mylast = &_Ptr[v26];
      v23->_Myfirst = _Ptr;
    }
  }
  CUnmannedTraderItemCodeInfo::~CUnmannedTraderItemCodeInfo(&_Vala);
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(v24);
}
