/*
 * Function: ?GetNewItemSerial@CPlayerDB@@QEAAGXZ
 * Address: 0x14010C100
 */

unsigned __int16 __fastcall CPlayerDB::GetNewItemSerial(CPlayerDB *this)
{
  __int16 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int16 v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 2;
  }
  return v5->m_wSerialCount++;
}
