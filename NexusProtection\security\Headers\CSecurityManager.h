/*
 * CSecurityManager.h - Modern Security Management System
 * Refactored from decompiled C security and cryptography functions
 * Provides comprehensive encryption, decryption, checksum validation, and anti-cheat features
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>
#include <optional>
#include <array>

namespace NexusProtection {
namespace Security {

/**
 * Security operation result codes
 */
enum class SecurityResult : int {
    Success = 1,
    Failure = 0,
    InvalidInput = -1,
    InvalidKey = -2,
    ChecksumMismatch = -3,
    EncryptionFailed = -4,
    DecryptionFailed = -5,
    ValidationFailed = -6,
    SystemError = -7
};

/**
 * Encryption algorithm types
 */
enum class EncryptionType : int {
    SimpleXOR = 0,
    AdvancedXOR = 1,
    AES128 = 2,
    AES256 = 3,
    Custom = 4
};

/**
 * Checksum types
 */
enum class ChecksumType : int {
    Character = 0,
    Account = 1,
    Guild = 2,
    Trunk = 3,
    Item = 4,
    System = 5
};

/**
 * Encryption context
 */
struct EncryptionContext {
    EncryptionType type{EncryptionType::SimpleXOR};
    std::vector<uint8_t> data;
    uint16_t cryptKey{0};
    uint8_t plusValue{0};
    std::string keyString;
    
    EncryptionContext() = default;
    
    bool IsValid() const {
        return !data.empty() && (cryptKey != 0 || !keyString.empty());
    }
};

/**
 * Checksum data structure
 */
struct ChecksumData {
    ChecksumType type{ChecksumType::Character};
    uint32_t serial{0};
    uint32_t accountSerial{0};
    uint8_t race{0};
    std::array<uint32_t, 6> values{};
    std::array<double, 2> dValues{};
    std::chrono::system_clock::time_point timestamp;
    
    ChecksumData() : timestamp(std::chrono::system_clock::now()) {}
    
    bool IsValid() const {
        return serial != 0 && type >= ChecksumType::Character && type <= ChecksumType::System;
    }
};

/**
 * Security operation details
 */
struct SecurityOperationDetails {
    SecurityResult result{SecurityResult::Success};
    std::string errorMessage;
    std::vector<uint8_t> processedData;
    ChecksumData checksumData;
    std::chrono::milliseconds executionTime{0};
    uint64_t operationId{0};
    
    SecurityOperationDetails() = default;
    
    bool IsSuccess() const {
        return result == SecurityResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case SecurityResult::Success: return "Success";
            case SecurityResult::Failure: return "General failure";
            case SecurityResult::InvalidInput: return "Invalid input";
            case SecurityResult::InvalidKey: return "Invalid key";
            case SecurityResult::ChecksumMismatch: return "Checksum mismatch";
            case SecurityResult::EncryptionFailed: return "Encryption failed";
            case SecurityResult::DecryptionFailed: return "Decryption failed";
            case SecurityResult::ValidationFailed: return "Validation failed";
            case SecurityResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Security statistics
 */
struct SecurityStats {
    std::atomic<uint64_t> totalEncryptions{0};
    std::atomic<uint64_t> totalDecryptions{0};
    std::atomic<uint64_t> totalChecksums{0};
    std::atomic<uint64_t> successfulOperations{0};
    std::atomic<uint64_t> failedOperations{0};
    std::atomic<uint64_t> securityViolations{0};
    std::chrono::system_clock::time_point lastOperation;
    
    SecurityStats() : lastOperation(std::chrono::system_clock::now()) {}
    
    void RecordOperation(bool success) {
        if (success) {
            successfulOperations++;
        } else {
            failedOperations++;
        }
        lastOperation = std::chrono::system_clock::now();
    }
    
    void RecordViolation() {
        securityViolations++;
    }
    
    double GetSuccessRate() const {
        uint64_t total = successfulOperations + failedOperations;
        return total > 0 ? static_cast<double>(successfulOperations) / total * 100.0 : 0.0;
    }
};

/**
 * Modern Security Manager
 * Refactored from legacy decompiled C security functions
 */
class CSecurityManager {
public:
    /**
     * Constructor
     */
    CSecurityManager();
    
    /**
     * Destructor
     */
    virtual ~CSecurityManager();
    
    /**
     * Initialize security system
     * @return true if successful
     */
    bool Initialize();
    
    /**
     * Shutdown security system
     */
    void Shutdown();
    
    /**
     * Encrypt string data
     * Refactored from: EnCryptStringYAXPEADHEGZ_14043BC50.c
     * @param data Data to encrypt
     * @param size Data size
     * @param plusValue Plus value for encryption
     * @param cryptKey Encryption key
     * @return Security operation result
     */
    SecurityOperationDetails EncryptString(char* data, int size, uint8_t plusValue, uint16_t cryptKey);
    
    /**
     * Decrypt string data
     * Refactored from: DeCryptStringYAXPEADHEGZ_14043BCF0.c
     * @param data Data to decrypt
     * @param size Data size
     * @param plusValue Plus value for decryption
     * @param cryptKey Decryption key
     * @return Security operation result
     */
    SecurityOperationDetails DecryptString(char* data, int size, uint8_t plusValue, uint16_t cryptKey);
    
    /**
     * Encrypt move data
     * Refactored from: EnCrypt_MoveYAXPEADHEGZ_14043BD90.c
     * @param data Data to encrypt
     * @param size Data size
     * @param plusValue Plus value for encryption
     * @param cryptKey Encryption key
     * @return Security operation result
     */
    SecurityOperationDetails EncryptMove(char* data, int size, uint8_t plusValue, uint16_t cryptKey);
    
    /**
     * Decrypt move data
     * Refactored from: DeCrypt_MoveYAXPEADHEGZ_14043BE30.c
     * @param data Data to decrypt
     * @param size Data size
     * @param plusValue Plus value for decryption
     * @param cryptKey Decryption key
     * @return Security operation result
     */
    SecurityOperationDetails DecryptMove(char* data, int size, uint8_t plusValue, uint16_t cryptKey);
    
    /**
     * Create character account trunk checksum
     * Refactored from: 0CCheckSumCharacAccountTrunkDataQEAAKKEZ_1402C06A0.c
     * @param serial Character serial
     * @param accountSerial Account serial
     * @param race Character race
     * @return Checksum data
     */
    ChecksumData CreateCharacterChecksum(uint32_t serial, uint32_t accountSerial, uint8_t race);
    
    /**
     * Create guild checksum
     * Refactored from: 0CCheckSumGuildDataQEAAKZ_1401BF340.c
     * @param guildId Guild ID
     * @return Checksum data
     */
    ChecksumData CreateGuildChecksum(uint32_t guildId);
    
    /**
     * Validate checksum
     * @param checksumData Checksum data to validate
     * @param expectedChecksum Expected checksum value
     * @return true if valid
     */
    bool ValidateChecksum(const ChecksumData& checksumData, uint32_t expectedChecksum);
    
    /**
     * Encode checksum value
     * Refactored from: EncodeValueCCheckSumQEAAKEKKZ_1402C05A0.c
     * @param value Value to encode
     * @param key1 First key
     * @param key2 Second key
     * @return Encoded value
     */
    uint32_t EncodeChecksumValue(uint8_t value, uint32_t key1, uint32_t key2);
    
    /**
     * Decode checksum value
     * Refactored from: DecodeValueCCheckSumQEAAKEKKZ_1402C0620.c
     * @param encodedValue Encoded value
     * @param key1 First key
     * @param key2 Second key
     * @return Decoded value
     */
    uint32_t DecodeChecksumValue(uint32_t encodedValue, uint32_t key1, uint32_t key2);
    
    /**
     * Advanced encryption with context
     * @param context Encryption context
     * @return Security operation result
     */
    SecurityOperationDetails EncryptData(const EncryptionContext& context);
    
    /**
     * Advanced decryption with context
     * @param context Decryption context
     * @return Security operation result
     */
    SecurityOperationDetails DecryptData(const EncryptionContext& context);
    
    /**
     * Generate secure random key
     * @param keySize Key size in bytes
     * @return Generated key
     */
    std::vector<uint8_t> GenerateSecureKey(size_t keySize);
    
    /**
     * Hash data using secure algorithm
     * @param data Data to hash
     * @return Hash result
     */
    std::vector<uint8_t> HashData(const std::vector<uint8_t>& data);
    
    /**
     * Verify data integrity
     * @param data Original data
     * @param hash Expected hash
     * @return true if integrity is valid
     */
    bool VerifyDataIntegrity(const std::vector<uint8_t>& data, const std::vector<uint8_t>& hash);
    
    /**
     * Get security statistics
     * @return Current statistics
     */
    const SecurityStats& GetStatistics() const { return m_stats; }
    
    /**
     * Reset statistics
     */
    void ResetStatistics();
    
    /**
     * Set security callback
     * @param callback Security callback function
     */
    void SetSecurityCallback(std::function<void(const SecurityOperationDetails&)> callback);
    
    /**
     * Enable/disable detailed logging
     * @param bEnable Enable flag
     */
    void SetDetailedLogging(bool bEnable) { m_bDetailedLogging = bEnable; }

protected:
    /**
     * Validate encryption context
     * @param context Context to validate
     * @return true if valid
     */
    virtual bool ValidateEncryptionContext(const EncryptionContext& context);
    
    /**
     * Perform XOR encryption
     * @param data Data to encrypt
     * @param key Encryption key
     * @param plusValue Plus value
     * @return Encrypted data
     */
    virtual std::vector<uint8_t> PerformXOREncryption(const std::vector<uint8_t>& data, uint16_t key, uint8_t plusValue);
    
    /**
     * Perform XOR decryption
     * @param data Data to decrypt
     * @param key Decryption key
     * @param plusValue Plus value
     * @return Decrypted data
     */
    virtual std::vector<uint8_t> PerformXORDecryption(const std::vector<uint8_t>& data, uint16_t key, uint8_t plusValue);
    
    /**
     * Calculate checksum hash
     * @param checksumData Checksum data
     * @return Calculated hash
     */
    virtual uint32_t CalculateChecksumHash(const ChecksumData& checksumData);
    
    /**
     * Log security operation
     * @param details Operation details
     */
    virtual void LogSecurityOperation(const SecurityOperationDetails& details);

private:
    SecurityStats m_stats;
    std::function<void(const SecurityOperationDetails&)> m_securityCallback;
    bool m_bDetailedLogging{false};
    bool m_bInitialized{false};
    mutable std::mutex m_statsMutex;
    std::atomic<uint64_t> m_nextOperationId{1};
    
    /**
     * Create security result with timing
     * @param result Result code
     * @param startTime Operation start time
     * @param errorMessage Error message (optional)
     * @return Complete security result
     */
    SecurityOperationDetails CreateResult(SecurityResult result, 
                                        std::chrono::high_resolution_clock::time_point startTime,
                                        const std::string& errorMessage = "");
    
    /**
     * Generate unique operation ID
     * @return Unique operation ID
     */
    uint64_t GenerateOperationId() { return m_nextOperationId++; }
    
    // Disable copy constructor and assignment operator
    CSecurityManager(const CSecurityManager&) = delete;
    CSecurityManager& operator=(const CSecurityManager&) = delete;
};

/**
 * Legacy compatibility functions
 * Maintain exact signatures for backward compatibility
 */
namespace LegacyCompatibility {
    /**
     * Legacy encrypt string function wrapper
     * @param pStr String to encrypt
     * @param nSize String size
     * @param byPlus Plus value
     * @param wCryptKey Encryption key
     */
    void EnCryptString_Legacy(char* pStr, int nSize, char byPlus, unsigned short wCryptKey);
    
    /**
     * Legacy decrypt string function wrapper
     * @param pStr String to decrypt
     * @param nSize String size
     * @param byPlus Plus value
     * @param wCryptKey Decryption key
     */
    void DeCryptString_Legacy(char* pStr, int nSize, char byPlus, unsigned short wCryptKey);
    
    /**
     * Legacy encrypt move function wrapper
     * @param pStr Data to encrypt
     * @param nSize Data size
     * @param byPlus Plus value
     * @param wCryptKey Encryption key
     */
    void EnCrypt_Move_Legacy(char* pStr, int nSize, char byPlus, unsigned short wCryptKey);
    
    /**
     * Legacy decrypt move function wrapper
     * @param pStr Data to decrypt
     * @param nSize Data size
     * @param byPlus Plus value
     * @param wCryptKey Decryption key
     */
    void DeCrypt_Move_Legacy(char* pStr, int nSize, char byPlus, unsigned short wCryptKey);
}

/**
 * Utility functions for security operations
 */
namespace SecurityUtils {
    /**
     * Generate random bytes
     * @param size Number of bytes to generate
     * @return Random bytes
     */
    std::vector<uint8_t> GenerateRandomBytes(size_t size);
    
    /**
     * Convert string to bytes
     * @param str String to convert
     * @return Byte vector
     */
    std::vector<uint8_t> StringToBytes(const std::string& str);
    
    /**
     * Convert bytes to string
     * @param bytes Bytes to convert
     * @return String
     */
    std::string BytesToString(const std::vector<uint8_t>& bytes);
    
    /**
     * Calculate CRC32 checksum
     * @param data Data to checksum
     * @return CRC32 value
     */
    uint32_t CalculateCRC32(const std::vector<uint8_t>& data);
    
    /**
     * Validate data format
     * @param data Data to validate
     * @return true if valid
     */
    bool ValidateDataFormat(const std::vector<uint8_t>& data);
    
    /**
     * Get current timestamp
     * @return Current timestamp
     */
    std::chrono::system_clock::time_point GetCurrentTime();
    
    /**
     * Format time for logging
     * @param timePoint Time point to format
     * @return Formatted time string
     */
    std::string FormatTime(const std::chrono::system_clock::time_point& timePoint);
}

} // namespace Security
} // namespace NexusProtection
