/*
 * Function: _std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::insert_::_1_::dtor$4
 * Address: 0x1402C5520
 */

void __fastcall std::vector_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::insert_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 2 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(*(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 216));
  }
}
