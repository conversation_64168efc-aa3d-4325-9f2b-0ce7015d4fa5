/*
 * Function: ?SendMsg_RACE_Greeting@CPlayer@@QEAAXPEAD0@Z
 * Address: 0x1400E6260
 */

void __fastcall CPlayer::SendMsg_RACE_Greeting(CPlayer *this, char *wszBossName, char *wszMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-1A8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-168h]@7
  char pbyType; // [sp+174h] [bp-34h]@7
  char v8; // [sp+175h] [bp-33h]@7
  int v9; // [sp+184h] [bp-24h]@7
  unsigned __int64 v10; // [sp+190h] [bp-18h]@4
  CPlayer *v11; // [sp+1B0h] [bp+8h]@1
  const char *_Source; // [sp+1B8h] [bp+10h]@1
  const char *Str; // [sp+1C0h] [bp+18h]@1

  Str = wszMsg;
  _Source = wszBossName;
  v11 = this;
  v3 = &v5;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( v11->m_pUserDB )
  {
    if ( !v11->m_pUserDB->m_bChatLock )
    {
      _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
      Dst.byMessageType = 16;
      Dst.bySenderRace = CPlayerDB::GetRaceCode(&v11->m_Param);
      Dst.dwSenderSerial = v11->m_dwObjSerial;
      strcpy_s<17>((char (*)[17])Dst.wszSenderName, _Source);
      Dst.bySize = strlen_0(Str);
      memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
      Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
      pbyType = 2;
      v8 = 11;
      v9 = _announ_message_receipt_udp::size(&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v9);
    }
  }
}
