/*
 * Function: ?CreateAni<PERSON>@@YA_NPEAVCMapData@@GPEAMEHHKPEAVCPlayer@@@Z
 * Address: 0x14012ADE0
 */

char __fastcall CreateAnimus(CMapData *pMap, unsigned __int16 wLayer, float *fPos, char byClass, int nHP, int nFP, unsigned int dwExp, CPlayer *pMaster)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v11; // [sp+0h] [bp-88h]@1
  _animus_create_setdata Dst; // [sp+30h] [bp-58h]@4
  CAnimus *v13; // [sp+78h] [bp-10h]@6
  CMapData *v14; // [sp+90h] [bp+8h]@1
  unsigned __int16 v15; // [sp+98h] [bp+10h]@1
  float *Src; // [sp+A0h] [bp+18h]@1
  char v17; // [sp+A8h] [bp+20h]@1

  v17 = byClass;
  Src = fPos;
  v15 = wLayer;
  v14 = pMap;
  v8 = &v11;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  _animus_create_setdata::_animus_create_setdata(&Dst);
  Dst.m_pMap = v14;
  Dst.m_nLayerIndex = v15;
  Dst.m_pRecordSet = CRecordData::GetRecord(&stru_1799C6370, (unsigned __int8)v17);
  if ( Dst.m_pRecordSet )
  {
    memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
    Dst.nHP = nHP;
    Dst.nFP = nFP;
    Dst.dwExp = dwExp;
    Dst.pMaster = pMaster;
    v13 = FindEmptyAnimus(g_Animus, 500);
    if ( v13 )
    {
      CAnimus::Create(v13, &Dst);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
