/*
 * Function: ?sell_item@CMgrAvatorItemHistory@@QEAAXHPEAU_sell_offer@@EKKKKPEAD@Z
 * Address: 0x140238AE0
 */

void __fastcall CMgrAvatorItemHistory::sell_item(CMgrAvatorItemHistory *this, int n, _sell_offer *pOffer, char byOfferNum, unsigned int dwIncomeDalant, unsigned int dwIncomeGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@7
  unsigned int v12; // eax@8
  __int64 v13; // [sp+0h] [bp-78h]@1
  char *v14; // [sp+20h] [bp-58h]@4
  __int64 v15; // [sp+28h] [bp-50h]@4
  unsigned int v16; // [sp+30h] [bp-48h]@4
  char *v17; // [sp+38h] [bp-40h]@4
  char *v18; // [sp+40h] [bp-38h]@4
  int j; // [sp+50h] [bp-28h]@4
  char *v20; // [sp+58h] [bp-20h]@6
  _base_fld *v21; // [sp+60h] [bp-18h]@6
  CMgrAvatorItemHistory *v22; // [sp+80h] [bp+8h]@1
  _sell_offer *v23; // [sp+90h] [bp+18h]@1
  char v24; // [sp+98h] [bp+20h]@1

  v24 = byOfferNum;
  v23 = pOffer;
  v22 = this;
  v9 = &v13;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  sData[0] = 0;
  v18 = v22->m_szCurTime;
  v17 = v22->m_szCurDate;
  v16 = dwNewGold;
  LODWORD(v15) = dwNewDalant;
  LODWORD(v14) = dwIncomeGold;
  sprintf(sBuf, "SELL: num:%u rev(D:%u G:%u) $D:%u $G:%u [%s %s]\r\n", (unsigned __int8)byOfferNum, dwIncomeDalant);
  strcat_0(sData, sBuf);
  for ( j = 0; j < (unsigned __int8)v24; ++j )
  {
    v20 = &v23[j].pItem->m_bLoad;
    v21 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v20[1], *(_WORD *)(v20 + 3));
    if ( IsOverLapItem((unsigned __int8)v20[1]) )
    {
      v12 = v23[j].byAmount;
      v14 = (char *)*((_QWORD *)v20 + 3);
      sprintf(sBuf, "\t- %s_%u[%I64u]\r\n", v21->m_strCode, v12);
      strcat_0(sData, sBuf);
    }
    else
    {
      v11 = DisplayItemUpgInfo((unsigned __int8)v20[1], *(_DWORD *)(v20 + 13));
      v15 = *((_QWORD *)v20 + 3);
      v14 = v11;
      sprintf(sBuf, "\t- %s_%u_@%s[%I64u]\r\n", v21->m_strCode, *(_QWORD *)(v20 + 5));
      strcat_0(sData, sBuf);
    }
  }
  CMgrAvatorItemHistory::WriteFile(v22, pszFileName, sData);
}
