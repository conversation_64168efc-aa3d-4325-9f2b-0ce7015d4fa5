/*
 * Function: ??$_Pop_heap_0@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@UMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0PEAUMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x140605B00
 */

int __fastcall std::_Pop_heap_0<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,CryptoPP::MeterFilter::MessageRange>(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1
  const void *v3; // rax@1
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  char v7; // [sp+30h] [bp-188h]@1
  char *v8; // [sp+50h] [bp-168h]@1
  char v9; // [sp+58h] [bp-160h]@1
  char v10; // [sp+70h] [bp-148h]@1
  char v11; // [sp+90h] [bp-128h]@1
  char *v12; // [sp+B0h] [bp-108h]@1
  char v13; // [sp+B8h] [bp-100h]@1
  char *v14; // [sp+D8h] [bp-E0h]@1
  char v15; // [sp+E0h] [bp-D8h]@1
  char *v16; // [sp+100h] [bp-B8h]@1
  char v17; // [sp+110h] [bp-A8h]@1
  char v18; // [sp+130h] [bp-88h]@1
  __int64 v19; // [sp+148h] [bp-70h]@1
  __int64 v20; // [sp+150h] [bp-68h]@1
  __int64 v21; // [sp+158h] [bp-60h]@1
  __int64 v22; // [sp+160h] [bp-58h]@1
  __int64 v23; // [sp+168h] [bp-50h]@1
  __int64 v24; // [sp+170h] [bp-48h]@1
  __int64 v25; // [sp+178h] [bp-40h]@1
  __int64 v26; // [sp+180h] [bp-38h]@1
  __int64 v27; // [sp+188h] [bp-30h]@1
  __int64 v28; // [sp+190h] [bp-28h]@1
  __int64 v29; // [sp+1C8h] [bp+10h]@1

  v29 = a2;
  v19 = -2i64;
  v8 = &v7;
  v12 = &v11;
  v14 = &v13;
  v16 = &v15;
  v2 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
         a2,
         (__int64)&v10,
         1i64);
  v20 = v2;
  v21 = v2;
  LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  qmemcpy(&v18, v3, 0x18ui64);
  qmemcpy(&v9, &v18, 0x18ui64);
  v22 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v8);
  v23 = std::_Dist_type<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>();
  qmemcpy(&v17, &v9, 0x18ui64);
  v4 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
         v29,
         (__int64)v12,
         1i64);
  v24 = v4;
  v25 = v4;
  v5 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-(
         v29,
         (__int64)v14,
         1i64);
  v26 = v5;
  v27 = v5;
  v28 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v16);
  std::_Pop_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(
    v28,
    v27,
    v25,
    &v17);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
