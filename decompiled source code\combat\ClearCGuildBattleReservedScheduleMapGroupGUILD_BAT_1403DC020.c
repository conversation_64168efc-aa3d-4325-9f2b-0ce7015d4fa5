/*
 * Function: ?Clear@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NIK@Z
 * Address: 0x1403DC020
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiMapID, unsigned int dwID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@9
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v9; // [sp+40h] [bp+8h]@1
  unsigned int v10; // [sp+48h] [bp+10h]@1
  int dwIDa; // [sp+50h] [bp+18h]@1

  dwIDa = dwID;
  v10 = uiMapID;
  v9 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v9->m_uiMapCnt && v9->m_uiMapCnt > uiMapID && v9->m_ppkReservedSchedule )
  {
    if ( GUILD_BATTLE::CGuildBattleReservedSchedule::Clear(v9->m_ppkReservedSchedule[uiMapID], dwID) )
    {
      result = 1;
    }
    else
    {
      v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v8 = dwIDa;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v6,
        "CGuildBattleReservedScheduleMapGroup::Clear(%u,%u) m_ppkReservedSchedule[%u]->Clear(%u) Fail!",
        v10,
        (unsigned int)dwIDa);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
