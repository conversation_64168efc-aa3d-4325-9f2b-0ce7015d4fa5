/*
 * Function: j_?CheckGuildBattleSuggestRequestToDestGuild@CGuild@@QEAAEKKKK@Z
 * Address: 0x14000927D
 */

char __fastcall CGuild::CheckGuildBattleSuggestRequestToDestGuild(CGuild *this, unsigned int dwSrcGuildSerial, unsigned int dwStartTimeInx, unsigned int dwMemberCntInx, unsigned int dwMapInx)
{
  return CGuild::CheckGuildBattleSuggestRequestToDestGuild(
           this,
           dwSrcGuildSerial,
           dwStartTimeInx,
           dwMemberCntInx,
           dwMapInx);
}
