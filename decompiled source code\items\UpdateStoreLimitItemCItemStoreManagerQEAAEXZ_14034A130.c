/*
 * Function: ?UpdateStoreLimitItem@CItemStoreManager@@QEAAEXZ
 * Address: 0x14034A130
 */

char __fastcall CItemStoreManager::UpdateStoreLimitItem(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-8B8h]@1
  unsigned int *pdwSerial; // [sp+20h] [bp-898h]@8
  _limit_item_db_data *pItemData; // [sp+28h] [bp-890h]@16
  unsigned __int64 dwLimitInitTime; // [sp+30h] [bp-888h]@16
  char *pszQuery; // [sp+38h] [bp-880h]@16
  int nBufSize; // [sp+40h] [bp-878h]@16
  char v10; // [sp+60h] [bp-858h]@4
  char v11; // [sp+61h] [bp-857h]@4
  unsigned int j; // [sp+864h] [bp-54h]@4
  _qry_case_all_store_limit_item::__list *v13; // [sp+868h] [bp-50h]@7
  char v14; // [sp+870h] [bp-48h]@8
  unsigned int dwSerial; // [sp+884h] [bp-34h]@8
  unsigned __int64 v16; // [sp+8A0h] [bp-18h]@4
  CItemStoreManager *v17; // [sp+8C0h] [bp+8h]@1

  v17 = this;
  v1 = &v4;
  for ( i = 556i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v16 = (unsigned __int64)&v4 ^ _security_cookie;
  v10 = 0;
  memset(&v11, 0, 0x7FFui64);
  for ( j = 0; j < v17->m_Sheet.dwCount; ++j )
  {
    v13 = &v17->m_Sheet.pStoreList[j];
    if ( v13->dwDBSerial )
    {
      nBufSize = 2048;
      pszQuery = &v10;
      dwLimitInitTime = v13->dwLimitInitTime;
      pItemData = v13->ItemData;
      LODWORD(pdwSerial) = v13->dwStoreIndex;
      CItemStoreManager::MakeLimitItemUpdateQuery(
        v17,
        v13->dwDBSerial,
        v13->byType,
        v13->nTypeSerial,
        (unsigned int)pdwSerial,
        v13->ItemData,
        dwLimitInitTime,
        &v10,
        2048);
      if ( !CRFWorldDatabase::Update_LimitItemNum(pkDB, &v10) )
        v13->byRet = 3;
    }
    else
    {
      dwSerial = 0;
      pdwSerial = &dwSerial;
      v14 = CRFWorldDatabase::Select_LimitItemUsedRecord(
              pkDB,
              v13->byType,
              v13->nTypeSerial,
              v13->dwStoreIndex,
              &dwSerial);
      if ( v14 == 1 )
      {
        v13->byRet = 4;
        continue;
      }
      if ( dwSerial )
        goto LABEL_25;
      v14 = CRFWorldDatabase::Select_LimitItemEmptyRecord(pkDB, &dwSerial);
      if ( v14 == 1 )
      {
        v13->byRet = 1;
        continue;
      }
      if ( dwSerial || CRFWorldDatabase::Insert_LimitItemRecord(pkDB, &dwSerial) )
      {
LABEL_25:
        nBufSize = 2048;
        pszQuery = &v10;
        dwLimitInitTime = v13->dwLimitInitTime;
        pItemData = v13->ItemData;
        LODWORD(pdwSerial) = v13->dwStoreIndex;
        CItemStoreManager::MakeLimitItemUpdateQuery(
          v17,
          dwSerial,
          v13->byType,
          v13->nTypeSerial,
          (unsigned int)pdwSerial,
          v13->ItemData,
          dwLimitInitTime,
          &v10,
          2048);
        if ( CRFWorldDatabase::Update_LimitItemNum(pkDB, &v10) )
        {
          v17->m_Sheet.pStoreList[j].bNewSerial = 1;
          v17->m_Sheet.pStoreList[j].dwDBSerial = dwSerial;
        }
        else
        {
          v13->byRet = 3;
        }
      }
      else
      {
        v13->byRet = 2;
      }
    }
  }
  return 0;
}
