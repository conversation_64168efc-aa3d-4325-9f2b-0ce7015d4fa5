/*
 * Function: ??1?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14055F350
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::~DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(__int64 a1)
{
  __int64 v1; // [sp+40h] [bp+8h]@1

  v1 = a1;
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::~vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(a1 + 96);
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(v1 + 56));
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(v1 + 8));
}
