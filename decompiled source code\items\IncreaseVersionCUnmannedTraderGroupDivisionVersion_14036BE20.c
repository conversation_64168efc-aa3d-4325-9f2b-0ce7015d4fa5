/*
 * Function: ?IncreaseVersion@CUnmannedTraderGroupDivisionVersionInfo@@QEAA_NE@Z
 * Address: 0x14036BE20
 */

char __fastcall CUnmannedTraderGroupDivisionVersionInfo::IncreaseVersion(CUnmannedTraderGroupDivisionVersionInfo *this, char byClass)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  unsigned int *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int *v7; // [sp+20h] [bp-18h]@7
  CUnmannedTraderGroupDivisionVersionInfo *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1

  v9 = byClass;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( std::vector<unsigned long,std::allocator<unsigned long>>::size(&v8->m_vecuiVersion) <= (unsigned __int8)byClass
    || std::vector<unsigned long,std::allocator<unsigned long>>::empty(&v8->m_vecuiVersion) )
  {
    result = 0;
  }
  else
  {
    v5 = std::vector<unsigned long,std::allocator<unsigned long>>::operator[](&v8->m_vecuiVersion, (unsigned __int8)v9);
    v7 = v5;
    ++*v5;
    result = 1;
  }
  return result;
}
