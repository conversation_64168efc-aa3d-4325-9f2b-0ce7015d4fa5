/*
 * Function: j_?CompleteRegistItem@CUnmannedTraderUserInfo@@AEAA_NKGKKEEGE_KK_N@Z
 * Address: 0x1400083EB
 */

bool __fastcall CUnmannedTraderUserInfo::CompleteRegistItem(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, unsigned __int16 dwItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, unsigned __int16 wItemIndex, char byStorageIndex, unsigned __int64 dwD, unsigned int dwU, bool bInserted)
{
  return CUnmannedTraderUserInfo::CompleteRegistItem(
           this,
           dwRegistSerial,
           dwItemSerial,
           dwETSerialNumber,
           dwPrice,
           bySellTurm,
           byTableCode,
           wItemIndex,
           byStorageIndex,
           dwD,
           dwU,
           bInserted);
}
