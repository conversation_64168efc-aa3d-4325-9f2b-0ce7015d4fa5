/*
 * Function: ??G?$_Vector_const_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x1403910E0
 */

__int64 __fastcall std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator-(std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *_Right)
{
  return this->_Myptr - _Right->_Myptr;
}
