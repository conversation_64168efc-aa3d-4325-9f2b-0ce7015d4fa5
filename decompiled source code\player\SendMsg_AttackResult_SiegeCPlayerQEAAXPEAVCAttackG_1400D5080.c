/*
 * Function: ?SendMsg_AttackResult_Siege@CPlayer@@QEAAXPEAVCAttack@@G@Z
 * Address: 0x1400D5080
 */

void __fastcall CPlayer::SendMsg_AttackResult_Siege(CPlayer *this, CAttack *pAt, unsigned __int16 wBulletIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-1D8h]@1
  _attack_siege_result_zocl v7; // [sp+40h] [bp-198h]@4
  int j; // [sp+194h] [bp-44h]@4
  char pbyType; // [sp+1A4h] [bp-34h]@7
  char v10; // [sp+1A5h] [bp-33h]@7
  unsigned __int64 v11; // [sp+1C0h] [bp-18h]@4
  CPlayer *v12; // [sp+1E0h] [bp+8h]@1
  CAttack *v13; // [sp+1E8h] [bp+10h]@1
  unsigned __int16 v14; // [sp+1F0h] [bp+18h]@1

  v14 = wBulletIndex;
  v13 = pAt;
  v12 = this;
  v3 = &v6;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v6 ^ _security_cookie;
  _attack_siege_result_zocl::_attack_siege_result_zocl(&v7);
  v7.byAtterID = v12->m_ObjID.m_byID;
  v7.dwAtterSerial = v12->m_dwObjSerial;
  v7.byAttackPart = v13->m_pp->nPart;
  v7.bCritical = v13->m_bIsCrtAtt;
  v7.wBulletIndex = v14;
  v7.zTar[0] = (signed int)ffloor(v13->m_pp->fArea[0]);
  v7.zTar[1] = (signed int)ffloor(v13->m_pp->fArea[2]);
  v7.byListNum = v13->m_nDamagedObjNum;
  for ( j = 0; j < v13->m_nDamagedObjNum; ++j )
  {
    v7.DamList[j].byDstID = v13->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v7.DamList[j].dwDstSerial = v13->m_DamList[j].m_pChar->m_dwObjSerial;
    v7.DamList[j].wDamage = v13->m_DamList[j].m_nDamage;
  }
  pbyType = 5;
  v10 = 122;
  v5 = _attack_siege_result_zocl::size(&v7);
  CGameObject::CircleReport((CGameObject *)&v12->vfptr, &pbyType, &v7.byAtterID, v5, 1);
}
