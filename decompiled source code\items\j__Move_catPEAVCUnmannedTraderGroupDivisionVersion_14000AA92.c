/*
 * Function: j_??$_Move_cat@PEAVCUnmannedTraderGroupDivisionVersionInfo@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCUnmannedTraderGroupDivisionVersionInfo@@@Z
 * Address: 0x14000AA92
 */

std::_Undefined_move_tag __fastcall std::_Move_cat<CUnmannedTraderGroupDivisionVersionInfo *>(CUnmannedTraderGroupDivisionVersionInfo *const *__formal)
{
  return std::_Move_cat<CUnmannedTraderGroupDivisionVersionInfo *>(__formal);
}
