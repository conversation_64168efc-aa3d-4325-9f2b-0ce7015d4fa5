/*
 * Function: ?GuildBattlePossibleGuildBattleList@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C84A0
 */

char __fastcall CNetworkEX::GuildBattlePossibleGuildBattleList(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-58h]@1
  unsigned int dwVer; // [sp+20h] [bp-38h]@6
  char *v9; // [sp+30h] [bp-28h]@4
  CPlayer *v10; // [sp+38h] [bp-20h]@4
  int v11; // [sp+40h] [bp-18h]@6
  int na; // [sp+68h] [bp+10h]@1

  na = n;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = pBuf;
  v10 = &g_Player + n;
  if ( v10->m_bOper )
  {
    v11 = CPlayerDB::GetRaceCode(&v10->m_Param);
    v6 = CGuildBattleController::Instance();
    dwVer = *(_DWORD *)(v9 + 1);
    CGuildBattleController::SendPossibleBattleGuildList(v6, na, v11, *v9, dwVer);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
