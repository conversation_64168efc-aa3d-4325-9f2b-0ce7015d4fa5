/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int__TimeItem_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64____0___::_Hash_stdext::_Hmap_traits_int__TimeItem_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64____0____::_1_::dtor$2
 * Address: 0x1403119F0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int__TimeItem_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64____0___::_Hash_stdext::_Hmap_traits_int__TimeItem_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___TimeItem_fld_const_____ptr64____0____::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::vector<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>>::~vector<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>>>((std::vector<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0>,std::allocator<std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> > > *)(*(_QWORD *)(a2 + 112) + 64i64));
}
