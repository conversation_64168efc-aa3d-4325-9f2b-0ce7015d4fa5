/*
 * Function: ?IsHaveRight@CMoveMapLimitRightInfo@@QEAA_NH@Z
 * Address: 0x1403ACBA0
 */

char __fastcall CMoveMapLimitRightInfo::IsHaveRight(CMoveMapLimitRightInfo *this, int iType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  CMoveMapLimitRight *v6; // [sp+20h] [bp-18h]@9
  CMoveMapLimitRightInfo *v7; // [sp+40h] [bp+8h]@1
  int v8; // [sp+48h] [bp+10h]@1

  v8 = iType;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( iType >= 0
    && !std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::empty(&v7->m_vecRight)
    && std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::size(&v7->m_vecRight) > v8
    && *std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](&v7->m_vecRight, v8) )
  {
    v6 = *std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator[](&v7->m_vecRight, v8);
    result = ((int (__fastcall *)(CMoveMapLimitRight *))v6->vfptr->IsHaveRight)(v6);
  }
  else
  {
    result = 1;
  }
  return result;
}
