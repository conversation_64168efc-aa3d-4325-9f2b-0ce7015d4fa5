/*
 * Function: j_?_Destroy@?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@IEAAXPEAPEAVCUnmannedTraderSubClassInfo@@0@Z
 * Address: 0x140013AA2
 */

void __fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Destroy(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last)
{
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Destroy(
    this,
    _First,
    _Last);
}
