/*
 * Function: ?_SetItemInfo@LtdWriter@@AEAAXEPEAU_db_con@_STORAGE_LIST@@EPEAU_LTD_ITEMINFO@@H@Z
 * Address: 0x14024B760
 */

void __fastcall LtdWriter::_SetItemInfo(LtdWriter *this, char byIndex, _STORAGE_LIST::_db_con *pItem, char byOverlapNum, _LTD_ITEMINFO *pi, int nMoveType)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-48h]@1
  _base_fld *v9; // [sp+20h] [bp-28h]@4
  char *Source; // [sp+28h] [bp-20h]@4
  char *v11; // [sp+30h] [bp-18h]@4
  char *v12; // [sp+38h] [bp-10h]@4
  char v13; // [sp+58h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v14; // [sp+60h] [bp+18h]@1
  char v15; // [sp+68h] [bp+20h]@1

  v15 = byOverlapNum;
  v14 = pItem;
  v13 = byIndex;
  v6 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
  Source = v9->m_strCode;
  v11 = GetItemKorName(v14->m_byTableCode, v14->m_wItemIndex);
  v12 = DisplayItemUpgInfo(v14->m_byTableCode, v14->m_dwLv);
  if ( nMoveType )
  {
    if ( nMoveType == 1 )
      sprintf(pi->m_ItemInfo[(unsigned __int8)v13].m_szItemCode, "GIVE:%s", Source);
    else
      sprintf(pi->m_ItemInfo[(unsigned __int8)v13].m_szItemCode, "TAKE:%s", Source);
  }
  else
  {
    strcpy_0(pi->m_ItemInfo[(unsigned __int8)v13].m_szItemCode, Source);
  }
  strcpy_0(pi->m_ItemInfo[(unsigned __int8)v13].m_szUpgradeCode, v12);
  strcpy_0(pi->m_ItemInfo[(unsigned __int8)v13].m_szItemName, v11);
  pi->m_ItemInfo[(unsigned __int8)v13].m_dwItemSerial = 0;
  pi->m_ItemInfo[(unsigned __int8)v13].m_byOverlapNum = v15;
  ++pi->m_byCnt;
}
