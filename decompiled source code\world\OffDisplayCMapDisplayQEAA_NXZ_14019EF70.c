/*
 * Function: ?OffDisplay@CMapDisplay@@QEAA_NXZ
 * Address: 0x14019EF70
 */

char __fastcall CMapDisplay::OffDisplay(CMapDisplay *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // rax@13
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+20h] [bp-38h]@6
  int j; // [sp+24h] [bp-34h]@8
  CDummyDraw *v8; // [sp+28h] [bp-30h]@11
  CDummyDraw *v9; // [sp+30h] [bp-28h]@11
  CDummyDraw *v10; // [sp+38h] [bp-20h]@11
  __int64 v11; // [sp+40h] [bp-18h]@13
  __int64 v12; // [sp+48h] [bp-10h]@15
  CMapDisplay *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v13->m_bDisplayMode )
  {
    v6 = CMapDisplay::ReleaseDisplay(v13);
    if ( v6 )
    {
      result = 0;
    }
    else
    {
      memset_0(v13->m_nDummyDrawNum, 0, 0xF0ui64);
      for ( j = 0; j < 60; ++j )
      {
        if ( v13->m_DummyDraw[j] )
        {
          v10 = v13->m_DummyDraw[j];
          v9 = v10;
          v8 = v10;
          if ( v10 )
          {
            if ( LODWORD(v8[-1].m_fScrExt[7]) )
            {
              LODWORD(v4) = ((int (__fastcall *)(CDummyDraw *, signed __int64))v9->vfptr->__vecDelDtor)(v9, 3i64);
              v11 = v4;
            }
            else
            {
              operator delete[](&v8[-1].m_fScrExt[7]);
              v11 = 0i64;
            }
            v12 = v11;
          }
          else
          {
            v12 = 0i64;
          }
          v13->m_DummyDraw[j] = 0i64;
        }
      }
      v13->m_bDisplayMode = 0;
      v13->m_pOldActMap = v13->m_pActMap;
      v13->m_wOldLayerIndex = v13->m_wLayerIndex;
      v13->m_pActMap = 0i64;
      v13->m_wLayerIndex = 0;
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
