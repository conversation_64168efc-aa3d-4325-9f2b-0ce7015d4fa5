/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCGuildRoomInfo@@PEAV1@V?$allocator@VCGuildRoomInfo@@@std@@@stdext@@YAPEAVCGuildRoomInfo@@PEAV1@00AEAV?$allocator@VCGuildRoomInfo@@@std@@@Z
 * Address: 0x140001C03
 */

CGuildRoomInfo *__fastcall stdext::unchecked_uninitialized_copy<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>>(CGuildRoomInfo *_First, CGuildRoomInfo *_Last, CGuildRoomInfo *_Dest, std::allocator<CGuildRoomInfo> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CGuildRoomInfo *,CGuildRoomInfo *,std::allocator<CGuildRoomInfo>>(
           _First,
           _Last,
           _Dest,
           _<PERSON>);
}
