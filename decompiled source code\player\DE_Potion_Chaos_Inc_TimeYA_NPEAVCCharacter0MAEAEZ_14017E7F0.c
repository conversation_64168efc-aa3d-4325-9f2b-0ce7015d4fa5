/*
 * Function: ?DE_Potion_Chaos_Inc_Time@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017E7F0
 */

char __fastcall DE_Potion_Chaos_Inc_Time(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  CCharacter *v8; // [sp+20h] [bp-18h]@9
  CCharacter *v9; // [sp+40h] [bp+8h]@1

  v9 = pActChar;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pTargetChar )
  {
    if ( v9->m_ObjID.m_byID || pTargetChar->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v8 = pTargetChar;
      *(_DWORD *)&pTargetChar[26].m_SFContAura[0][5].m_wszPlayerName[16] = 1;
      *(_DWORD *)&v8[26].m_SFContAura[0][6].m_byLv = (signed int)ffloor((float)(signed int)GetLoopTime() + (float)(fEffectValue * 1000.0));
      *(_DWORD *)&v8[26].m_SFContAura[0][6].m_bExist = (signed int)ffloor(fEffectValue * 100.0);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
