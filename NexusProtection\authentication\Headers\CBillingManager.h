#pragma once

#include "AuthenticationTypes.h"
#include <memory>
#include <mutex>
#include <unordered_map>
#include <functional>

namespace NexusProtection::Authentication {

    /**
     * @brief Billing system interface
     * 
     * Abstract interface for different billing implementations
     */
    class IBilling {
    public:
        virtual ~IBilling() = default;
        virtual void Login() = 0;
        virtual void Logout() = 0;
        virtual bool ChangeBillingType(const std::string& accountId, const std::string& cmsCode, 
                                     BillingType type, uint32_t remainTime, 
                                     const std::chrono::system_clock::time_point& endDate, 
                                     uint8_t reason) = 0;
        virtual bool IsOperational() const = 0;
        virtual BillingType GetBillingType(const std::string& accountId) const = 0;
    };

    /**
     * @brief Billing Manager - Main billing and authentication coordinator
     * 
     * This class manages billing operations, user authentication, and coordinates
     * with various billing systems. Refactored from decompiled C source to modern C++20.
     */
    class CBillingManager {
    public:
        // Constructor and destructor
        CBillingManager();
        ~CBillingManager();

        // Core lifecycle
        bool Initialize();
        void Shutdown();
        bool LoadConfiguration();

        // Authentication operations
        AuthenticationResult Login(CUserDB* userDB);
        AuthenticationResult Logout(uint32_t accountSerial);
        bool ValidateSession(uint32_t sessionId);

        // Billing operations
        bool ChangeBillingType(const std::string& accountId, const std::string& cmsCode,
                             BillingType type, uint32_t remainTime,
                             const std::chrono::system_clock::time_point& endDate,
                             uint8_t reason);
        BillingType GetBillingType(const std::string& accountId) const;
        bool IsBillingActive(const std::string& accountId) const;

        // Session management
        uint32_t CreateSession(const AccountInfo& account, const std::string& clientIP, uint16_t clientPort);
        bool DestroySession(uint32_t sessionId);
        SessionInfo GetSessionInfo(uint32_t sessionId) const;
        void UpdateSessionActivity(uint32_t sessionId);

        // Configuration and state
        bool IsInitialized() const noexcept { return m_isInitialized; }
        bool IsOperational() const noexcept { return m_isOperational; }
        size_t GetActiveSessionCount() const;

        // Statistics
        struct Statistics {
            uint32_t totalLogins{0};
            uint32_t successfulLogins{0};
            uint32_t failedLogins{0};
            uint32_t activeSessions{0};
            uint32_t totalSessions{0};
            std::chrono::steady_clock::time_point startTime;
        };
        
        const Statistics& GetStatistics() const noexcept { return m_statistics; }
        void ResetStatistics();

        // Legacy C interface compatibility
        void Change_BillingType(char* szID, char* szCMSCode, int16_t iType,
                              int32_t lRemainTime, _SYSTEMTIME* pstEndDate, uint8_t byReason);

    private:
        // Internal methods
        bool LoadINI();
        bool InitializeBillingSystem();
        void CleanupExpiredSessions();
        uint32_t GenerateSessionId();
        bool ValidateAccount(const AccountInfo& account) const;
        void LogAuthenticationEvent(LogType type, const std::string& message, uint32_t accountSerial = 0);

        // Member variables
        std::unique_ptr<IBilling> m_pBilling;
        std::unordered_map<uint32_t, SessionInfo> m_sessions;
        std::unordered_map<std::string, BillingInfo> m_billingCache;
        
        bool m_isInitialized{false};
        bool m_isOperational{false};
        uint32_t m_nextSessionId{1};
        
        Statistics m_statistics;
        SecurityParams m_securityParams;
        
        mutable std::mutex m_sessionMutex;
        mutable std::mutex m_billingMutex;
        mutable std::mutex m_statisticsMutex;

        // Configuration
        std::string m_configPath;
        std::chrono::minutes m_sessionCleanupInterval{5};
        std::chrono::steady_clock::time_point m_lastCleanup;

        // Callbacks
        std::function<void(AuthenticationResult, const AccountInfo&)> m_loginCallback;
        std::function<void(uint32_t, const SessionInfo&)> m_sessionCallback;
    };

    /**
     * @brief Concrete billing implementation
     */
    class CBilling : public IBilling {
    public:
        CBilling();
        virtual ~CBilling() = default;

        // IBilling implementation
        void Login() override;
        void Logout() override;
        bool ChangeBillingType(const std::string& accountId, const std::string& cmsCode,
                             BillingType type, uint32_t remainTime,
                             const std::chrono::system_clock::time_point& endDate,
                             uint8_t reason) override;
        bool IsOperational() const override { return m_isOperational; }
        BillingType GetBillingType(const std::string& accountId) const override;

        // Configuration
        void SetOperational(bool operational) { m_isOperational = operational; }

    private:
        bool m_isOperational{false};
        std::unordered_map<std::string, BillingInfo> m_accountBilling;
        mutable std::mutex m_billingMutex;
    };

    // Legacy C interface
    extern "C" {
        struct CBillingVtbl {
            void (__fastcall *Login)(CBilling* pThis);
            void (__fastcall *Logout)(CBilling* pThis);
            bool (__fastcall *ChangeBillingType)(CBilling* pThis, const char* accountId,
                                               const char* cmsCode, int16_t type,
                                               int32_t remainTime, _SYSTEMTIME* endDate,
                                               uint8_t reason);
        };

        struct CBillingManager_Legacy {
            CBillingVtbl* vfptr;
            CBilling* m_pBill;
            bool m_bOperational;
        };

        struct CBilling_Legacy {
            CBillingVtbl* vfptr;
            bool m_bOper;
        };

        // Legacy function declarations
        CBillingManager_Legacy* CBillingManager_Create();
        void CBillingManager_Destroy(CBillingManager_Legacy* mgr);
        bool CBillingManager_Init(CBillingManager_Legacy* mgr);
        void CBillingManager_Login(CBillingManager_Legacy* mgr, CUserDB* userDB);
        void CBillingManager_Change_BillingType(CBillingManager_Legacy* mgr, char* szID, 
                                               char* szCMSCode, int16_t iType, 
                                               int32_t lRemainTime, _SYSTEMTIME* pstEndDate, 
                                               uint8_t byReason);
        bool CBillingManager_LoadINI(CBillingManager_Legacy* mgr);
    }

    // Global instance access
    CBillingManager& GetBillingManager();

} // namespace NexusProtection::Authentication

// Global legacy compatibility
extern NexusProtection::Authentication::CBillingManager* g_pBillingManager;
