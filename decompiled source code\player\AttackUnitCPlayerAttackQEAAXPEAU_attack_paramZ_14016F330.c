/*
 * Function: ?AttackUnit@CPlayerAttack@@QEAAXPEAU_attack_param@@@Z
 * Address: 0x14016F330
 */

void __usercall CPlayerAttack::AttackUnit(CPlayerAttack *this@<rcx>, _attack_param *pParam@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@10
  float v6; // xmm0_4@16
  float v7; // xmm0_4@16
  CCharacter *v8; // r10@22
  int v9; // eax@22
  CPvpUserAndGuildRankingSystem *v10; // rax@27
  float v11; // xmm0_4@36
  float v12; // xmm0_4@36
  float v13; // xmm0_4@36
  float v14; // xmm0_4@36
  float v15; // xmm0_4@36
  float v16; // xmm0_4@36
  CCharacter **v17; // rax@44
  CCharacter **v18; // rcx@44
  CCharacter **v19; // rcx@46
  CCharacter **v20; // rdx@46
  CCharacter **v21; // r8@46
  __int64 v22; // [sp+0h] [bp-D8h]@1
  bool bUnit[8]; // [sp+20h] [bp-B8h]@36
  int bUseEffBullet[2]; // [sp+28h] [bp-B0h]@44
  int nEffAttPower; // [sp+30h] [bp-A8h]@44
  bool v26; // [sp+38h] [bp-A0h]@44
  _base_fld *v27; // [sp+40h] [bp-98h]@4
  char v28; // [sp+48h] [bp-90h]@4
  unsigned int v29; // [sp+4Ch] [bp-8Ch]@5
  char v30; // [sp+50h] [bp-88h]@7
  float v31; // [sp+54h] [bp-84h]@16
  float v32; // [sp+58h] [bp-80h]@16
  int nAttPower; // [sp+5Ch] [bp-7Ch]@26
  char v34; // [sp+60h] [bp-78h]@27
  float v35; // [sp+64h] [bp-74h]@33
  float v36; // [sp+68h] [bp-70h]@36
  float v37; // [sp+6Ch] [bp-6Ch]@36
  float v38; // [sp+70h] [bp-68h]@36
  float v39; // [sp+74h] [bp-64h]@36
  unsigned int j; // [sp+78h] [bp-60h]@33
  float v41; // [sp+7Ch] [bp-5Ch]@10
  float v42; // [sp+80h] [bp-58h]@16
  float v43; // [sp+84h] [bp-54h]@16
  int v44; // [sp+88h] [bp-50h]@22
  CCharacter **v45; // [sp+90h] [bp-48h]@22
  CGameObjectVtbl *v46; // [sp+98h] [bp-40h]@22
  unsigned int dwSerial; // [sp+A0h] [bp-38h]@27
  int v48; // [sp+A4h] [bp-34h]@27
  char v49; // [sp+A8h] [bp-30h]@27
  float v50; // [sp+ACh] [bp-2Ch]@36
  float v51; // [sp+B0h] [bp-28h]@36
  float v52; // [sp+B4h] [bp-24h]@36
  float v53; // [sp+B8h] [bp-20h]@36
  float v54; // [sp+BCh] [bp-1Ch]@36
  float v55; // [sp+C0h] [bp-18h]@36
  unsigned int v56; // [sp+C4h] [bp-14h]@36
  CPlayerAttack *v57; // [sp+E0h] [bp+8h]@1

  v57 = this;
  v3 = &v22;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v57->m_nDamagedObjNum = 0;
  v57->m_bIsCrtAtt = 0;
  v57->m_pp = pParam;
  v27 = v57->m_pp->pFld;
  v28 = 1;
  CCharacter::BreakStealth(v57->m_pAttChar);
  if ( v57->m_pp->pDst )
  {
    v29 = v27[4].m_dwIndex;
    if ( v29 != 4 )
    {
      if ( v57->m_pp->pDst )
      {
        v30 = 0;
        if ( _effect_parameter::GetEff_State(&v57->m_pp->pDst->m_EP, 14) )
        {
          v30 = 1;
        }
        else
        {
          _effect_parameter::GetEff_Plus(&v57->m_pp->pDst->m_EP, 27);
          if ( a3 > 0.0 )
          {
            v5 = rand();
            a3 = (float)(v5 % 100);
            v41 = (float)(v5 % 100);
            _effect_parameter::GetEff_Plus(&v57->m_pp->pDst->m_EP, 27);
            if ( a3 > v41 )
              v30 = 1;
          }
        }
        if ( v30 )
        {
          if ( !v57->m_pp->bPassCount
            && !v57->m_pp->nClass
            && !((int (__fastcall *)(CCharacter *))v57->m_pp->pDst->vfptr->GetWeaponClass)(v57->m_pp->pDst) )
          {
            ((void (__fastcall *)(CCharacter *))v57->m_pp->pDst->vfptr->GetAttackRange)(v57->m_pp->pDst);
            v42 = a3;
            ((void (__fastcall *)(CCharacter *))v57->m_pAttChar->vfptr->GetWidth)(v57->m_pAttChar);
            v6 = v42 + (float)(a3 / 2.0);
            v43 = v6;
            _effect_parameter::GetEff_Plus(&v57->m_pp->pDst->m_EP, 4);
            v7 = v43 + v6;
            v31 = v7;
            GetSqrt(v57->m_pp->pDst->m_fCurPos, v57->m_pAttChar->m_fCurPos);
            v32 = v7;
            a3 = v31;
            if ( v31 >= v32 )
            {
              v57->m_DamList[0].m_pChar = v57->m_pp->pDst;
              v57->m_DamList[0].m_nDamage = -1;
              v57->m_nDamagedObjNum = 1;
              CCharacter::SendMsg_AttackActEffect(v57->m_pAttChar, 0, v57->m_pp->pDst);
              return;
            }
          }
          _effect_parameter::GetEff_Plus(&v57->m_pp->pDst->m_EP, 27);
          if ( a3 > 0.0 )
          {
            v57->m_DamList[0].m_pChar = v57->m_pp->pDst;
            v57->m_DamList[0].m_nDamage = 0;
            v57->m_nDamagedObjNum = 1;
            return;
          }
        }
        if ( _effect_parameter::GetEff_State(&v57->m_pp->pDst->m_EP, 8) )
        {
          v28 = 0;
        }
        else
        {
          v44 = rand() % 100;
          v45 = &v57->m_pp->pDst;
          v8 = v57->m_pAttChar;
          v46 = v57->m_pAttChar->vfptr;
          v9 = ((int (__fastcall *)(CCharacter *, CCharacter *, _QWORD, _QWORD))v46->GetGenAttackProb)(
                 v8,
                 *v45,
                 *((_DWORD *)v45 + 2),
                 *((_BYTE *)v45 + 100));
          if ( v44 >= v9 )
            v28 = 0;
        }
        if ( !v28 )
        {
          v57->m_DamList[0].m_pChar = v57->m_pp->pDst;
          v57->m_DamList[0].m_nDamage = 0;
          v57->m_nDamagedObjNum = 1;
          return;
        }
      }
      nAttPower = v57->m_pp->nAddAttPnt + CAttack::_CalcGenAttPnt((CAttack *)&v57->m_pp, 0);
      _effect_parameter::GetEff_Rate(&v57->m_pAttChar->m_EP, 21);
      nAttPower = (signed int)ffloor((float)nAttPower * a3);
      if ( !v57->m_pAttPlayer->m_bInGuildBattle )
      {
        dwSerial = CPlayerDB::GetCharSerial(&v57->m_pAttPlayer->m_Param);
        v48 = CPlayerDB::GetRaceCode(&v57->m_pAttPlayer->m_Param);
        v10 = CPvpUserAndGuildRankingSystem::Instance();
        v34 = CPvpUserAndGuildRankingSystem::GetBossType(v10, v48, dwSerial);
        v49 = v34;
        if ( v34 )
        {
          if ( v49 == 2 || v49 == 6 )
            nAttPower = (signed int)ffloor((float)nAttPower * 1.2);
        }
        else
        {
          nAttPower = (signed int)ffloor((float)nAttPower * 1.3);
        }
      }
      v35 = 0.0;
      for ( j = 0; (signed int)j < 5; ++j )
        v35 = (float)((int (__fastcall *)(CPlayer *, _QWORD, CPlayer *, _QWORD))v57->m_pAttPlayer->vfptr->GetDefFC)(
                       v57->m_pAttPlayer,
                       j,
                       v57->m_pAttPlayer,
                       0i64);
      v11 = v35 / 5.0;
      v50 = v35 / 5.0;
      _effect_parameter::GetEff_Rate(&v57->m_pAttPlayer->m_EP, 44);
      v12 = v50 * (float)(v11 - 1.0);
      v36 = v12;
      bUnit[0] = 1;
      CAttack::GetAttackFC((CAttack *)&v57->m_pp, v57->m_pAttPlayer, 2, 1, 1);
      v51 = v12;
      _effect_parameter::GetEff_Rate(&v57->m_pAttPlayer->m_EP, 43);
      v13 = v51 * (float)(v12 - 1.0);
      v37 = v13;
      bUnit[0] = 1;
      CAttack::GetAttackFC((CAttack *)&v57->m_pp, v57->m_pAttPlayer, 0, 1, 1);
      v52 = v13;
      bUnit[0] = 1;
      CAttack::GetAttackFC((CAttack *)&v57->m_pp, v57->m_pAttPlayer, 1, 1, 1);
      v14 = v52 + v13;
      v53 = v14;
      _effect_parameter::GetEff_Rate(&v57->m_pAttPlayer->m_EP, 41);
      v15 = v53 * (float)(v14 - 1.0);
      v38 = v15;
      bUnit[0] = 1;
      CAttack::GetAttackFC((CAttack *)&v57->m_pp, v57->m_pAttPlayer, 0, 0, 1);
      v54 = v15;
      bUnit[0] = 1;
      CAttack::GetAttackFC((CAttack *)&v57->m_pp, v57->m_pAttPlayer, 1, 0, 1);
      v16 = v54 + v15;
      v55 = v16;
      _effect_parameter::GetEff_Rate(&v57->m_pAttPlayer->m_EP, 42);
      v39 = v55 * (float)(v16 - 1.0);
      nAttPower = (signed int)ffloor((float)((float)((float)((float)nAttPower + v37) + v36) + v38) + v39);
      v56 = v29;
      if ( (v29 & 0x80000000) == 0 )
      {
        if ( (signed int)v56 <= 3 )
        {
          if ( v57->m_pp->pDst )
          {
            v57->m_DamList[0].m_pChar = v57->m_pp->pDst;
            v19 = &v57->m_pp->pDst;
            v20 = &v57->m_pp->pDst;
            v21 = &v57->m_pp->pDst;
            LOBYTE(bUseEffBullet[0]) = v57->m_pp->bBackAttack;
            *(_QWORD *)bUnit = *v19;
            v57->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                            v57->m_pAttChar,
                                            nAttPower,
                                            *((_DWORD *)v21 + 2),
                                            *((_DWORD *)v20 + 3),
                                            *(CCharacter **)bUnit,
                                            bUseEffBullet[0]);
            v57->m_nDamagedObjNum = 1;
          }
        }
        else if ( v56 == 5 )
        {
          CAttack::FlashDamageProc((CAttack *)&v57->m_pp, s_nLimitDist[3], nAttPower, s_nLimitAngle[0][3], 0, 0);
        }
        else if ( v56 == 6 )
        {
          CAttack::AreaDamageProc((CAttack *)&v57->m_pp, s_nLimitRadius[3], nAttPower, v57->m_pp->fArea, 0, 0);
        }
        else
        {
          if ( v56 != 7 )
            return;
          v17 = &v57->m_pp->pDst;
          v18 = &v57->m_pp->pDst;
          v26 = 0;
          nEffAttPower = 0;
          bUseEffBullet[0] = *((_DWORD *)v17 + 9);
          *(_DWORD *)bUnit = *((_DWORD *)v18 + 10);
          CAttack::SectorDamageProc(
            (CAttack *)&v57->m_pp,
            3,
            nAttPower,
            s_nLimitAngle[0][3],
            *(int *)bUnit,
            bUseEffBullet[0],
            0,
            0);
        }
        CAttack::CalcAvgDamage((CAttack *)&v57->m_pp);
        return;
      }
    }
  }
}
