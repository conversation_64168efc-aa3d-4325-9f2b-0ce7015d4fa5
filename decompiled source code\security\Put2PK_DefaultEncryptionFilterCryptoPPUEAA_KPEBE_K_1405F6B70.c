/*
 * Function: ?Put2@PK_DefaultEncryptionFilter@CryptoPP@@UEAA_KPEBE_KH_N@Z
 * Address: 0x1405F6B70
 */

unsigned __int64 __fastcall CryptoPP::PK_DefaultEncryptionFilter::Put2(CryptoPP::PK_DefaultEncryptionFilter *this, const unsigned __int8 *a2, __int64 a3, int a4, bool a5)
{
  __int64 v5; // rax@5
  unsigned __int64 v6; // rax@8
  char *v7; // rax@8
  char *v8; // rax@8
  __int64 v9; // ST28_8@8
  char v10; // al@9
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v12; // [sp+40h] [bp-108h]@8
  unsigned __int64 size; // [sp+58h] [bp-F0h]@5
  unsigned __int64 v14; // [sp+60h] [bp-E8h]@8
  CryptoPP::InvalidArgument v15; // [sp+68h] [bp-E0h]@6
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+B8h] [bp-90h]@6
  unsigned __int8 v17; // [sp+E8h] [bp-60h]@6
  unsigned __int64 v18; // [sp+F0h] [bp-58h]@10
  __int64 v19; // [sp+F8h] [bp-50h]@10
  __int64 v20; // [sp+100h] [bp-48h]@1
  int v21; // [sp+108h] [bp-40h]@1
  __int64 v22; // [sp+110h] [bp-38h]@8
  char *v23; // [sp+118h] [bp-30h]@8
  __int64 *v24; // [sp+120h] [bp-28h]@8
  __int64 v25; // [sp+128h] [bp-20h]@8
  unsigned __int64 v26; // [sp+130h] [bp-18h]@9
  CryptoPP::PK_DefaultEncryptionFilter *v27; // [sp+150h] [bp+8h]@1
  int v28; // [sp+168h] [bp+20h]@1

  v28 = a4;
  v27 = this;
  v20 = -2i64;
  v21 = *((_DWORD *)this + 10);
  if ( v21 )
  {
    if ( v21 != 1 )
      _wassert(L"false", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\cryptlib.cpp", 0x24Au);
  }
  else
  {
    *((_QWORD *)this + 4) = 0i64;
    CryptoPP::BufferedTransformation::Put((CryptoPP::BufferedTransformation *)this + 3, a2, a3);
    if ( !v28 )
      return 0i64;
    v5 = CryptoPP::ByteQueue::CurrentSize((CryptoPP::ByteQueue *)((char *)v27 + 72));
    if ( !CryptoPP::SafeConvert<unsigned __int64,unsigned __int64>(v5, &size) )
    {
      memset(&v17, 0, sizeof(v17));
      std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
        &s,
        "PK_DefaultEncryptionFilter: plaintext too long",
        v17);
      CryptoPP::InvalidArgument::InvalidArgument(&v15, &s);
      CxxThrowException_0((__int64)&v15, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
    }
    LODWORD(v6) = (*(int (__fastcall **)(_QWORD, unsigned __int64))(**((_QWORD **)v27 + 7) + 16i64))(
                    *((_QWORD *)v27 + 7),
                    size);
    v14 = v6;
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
      &v12,
      size);
    v7 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v12);
    v22 = *((_QWORD *)v27 + 9);
    (*(void (__fastcall **)(signed __int64, char *, unsigned __int64))(v22 + 136))((signed __int64)v27 + 72, v7, size);
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::resize((__int64)v27 + 152, v14);
    v23 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v27 + 152));
    v8 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v12);
    v24 = (__int64 *)*((_QWORD *)v27 + 7);
    v25 = *v24;
    v9 = *((_QWORD *)v27 + 8);
    (*(void (__fastcall **)(__int64 *, _QWORD, char *, unsigned __int64))(v25 + 48))(
      v24,
      *((_QWORD *)v27 + 6),
      v8,
      size);
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v12);
  }
  v26 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v27 + 152));
  v10 = (unsigned __int64)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v27 + 152));
  if ( CryptoPP::Filter::Output((_DWORD)v27, 1, v10) )
  {
    v18 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)((char *)v27 + 152))
        - *((_QWORD *)v27 + 4);
    v19 = 1i64;
    return *CryptoPP::STDMAX<unsigned __int64>(&v19, &v18);
  }
  return 0i64;
}
