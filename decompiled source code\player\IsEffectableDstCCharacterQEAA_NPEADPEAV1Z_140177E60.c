/*
 * Function: ?IsEffectableDst@CCharacter@@QEAA_NPEADPEAV1@@Z
 * Address: 0x140177E60
 */

char __fastcall CCharacter::IsEffectableDst(CCharacter *this, char *psActableDst, CCharacter *pDst)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@18
  int v7; // eax@21
  int v8; // eax@38
  int v9; // eax@41
  __int64 v10; // [sp+0h] [bp-98h]@1
  CCharacter *v11; // [sp+20h] [bp-78h]@10
  CCharacter *v12; // [sp+28h] [bp-70h]@10
  CPlayer *v13; // [sp+30h] [bp-68h]@26
  CPlayer *v14; // [sp+38h] [bp-60h]@26
  CCharacter *v15; // [sp+40h] [bp-58h]@46
  int v16; // [sp+48h] [bp-50h]@18
  CGameObjectVtbl *v17; // [sp+50h] [bp-48h]@18
  int v18; // [sp+58h] [bp-40h]@21
  CGameObjectVtbl *v19; // [sp+60h] [bp-38h]@21
  int v20; // [sp+68h] [bp-30h]@38
  CGameObjectVtbl *v21; // [sp+70h] [bp-28h]@38
  int v22; // [sp+78h] [bp-20h]@41
  CGameObjectVtbl *v23; // [sp+80h] [bp-18h]@41
  CCharacter *v24; // [sp+A0h] [bp+8h]@1
  char *v25; // [sp+A8h] [bp+10h]@1
  CPlayer *v26; // [sp+B0h] [bp+18h]@1

  v26 = (CPlayer *)pDst;
  v25 = psActableDst;
  v24 = this;
  v3 = &v10;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( *psActableDst == 49 && pDst == v24 )
    return 1;
  if ( psActableDst[1] == 49 )
  {
    if ( pDst->m_ObjID.m_byID || v24->m_ObjID.m_byID )
    {
      v18 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
      v19 = v24->vfptr;
      v7 = ((int (__fastcall *)(CCharacter *))v19->GetObjRace)(v24);
      if ( v18 == v7 )
        return 1;
    }
    else
    {
      v11 = pDst;
      v12 = v24;
      if ( BYTE2(pDst[1].m_fCurPos[2]) && BYTE2(v12[1].m_fCurPos[2]) )
      {
        if ( !LOBYTE(v11[1].m_fCurPos[2]) && LOBYTE(v12[1].m_fAbsPos[0]) == LOBYTE(v11[1].m_fAbsPos[0]) )
          return 1;
      }
      else if ( !BYTE2(v11[1].m_fCurPos[2]) && !BYTE2(v12[1].m_fCurPos[2]) )
      {
        v16 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
        v17 = v24->vfptr;
        v6 = ((int (__fastcall *)(CCharacter *))v17->GetObjRace)(v24);
        if ( v16 == v6 )
          return 1;
      }
    }
  }
  if ( v25[2] == 49 )
  {
    if ( v26->m_ObjID.m_byID || v24->m_ObjID.m_byID )
    {
      v22 = ((int (__fastcall *)(CPlayer *))v26->vfptr->GetObjRace)(v26);
      v23 = v24->vfptr;
      v9 = ((int (__fastcall *)(CCharacter *))v23->GetObjRace)(v24);
      if ( v22 != v9 && v26->m_ObjID.m_byID != 1 )
        return 1;
    }
    else
    {
      v13 = v26;
      v14 = (CPlayer *)v24;
      if ( v26->m_bInGuildBattle && v14->m_bInGuildBattle )
      {
        if ( !v13->m_bTakeGravityStone && v14->m_byGuildBattleColorInx != v13->m_byGuildBattleColorInx )
          return 1;
      }
      else if ( !v13->m_bInGuildBattle && !v14->m_bInGuildBattle )
      {
        if ( CPlayer::IsChaosMode(v14) )
          return 1;
        if ( CPlayer::IsPunished(v13, 1, 0) )
          return 1;
        v20 = ((int (__fastcall *)(CPlayer *))v26->vfptr->GetObjRace)(v26);
        v21 = v24->vfptr;
        v8 = ((int (__fastcall *)(CCharacter *))v21->GetObjRace)(v24);
        if ( v20 != v8 )
          return 1;
      }
    }
  }
  if ( v25[3] == 49 )
  {
    if ( v24->m_ObjID.m_byID )
    {
      if ( v26->m_ObjID.m_byID == 1 )
        return 1;
    }
    else
    {
      v15 = v24;
      if ( !BYTE2(v24[1].m_fCurPos[2]) && v26->m_ObjID.m_byID == 1 )
        return 1;
    }
  }
  return 0;
}
