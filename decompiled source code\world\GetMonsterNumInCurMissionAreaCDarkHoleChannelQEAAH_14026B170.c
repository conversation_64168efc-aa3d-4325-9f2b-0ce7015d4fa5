/*
 * Function: ?GetMonsterNumInCurMissionArea@CDarkHoleChannel@@QEAAHH@Z
 * Address: 0x14026B170
 */

__int64 __fastcall CDarkHoleChannel::GetMonsterNumInCurMissionArea(CDarkHoleChannel *this, int nMonsterRecIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int32 v4; // ecx@15
  __int64 v6; // [sp+0h] [bp-48h]@1
  unsigned int v7; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@4
  CMonster *v9; // [sp+28h] [bp-20h]@7
  CExtDummy *v10; // [sp+30h] [bp-18h]@15
  CDarkHoleChannel *v11; // [sp+50h] [bp+8h]@1
  int v12; // [sp+58h] [bp+10h]@1

  v12 = nMonsterRecIndex;
  v11 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = 0;
  for ( j = 0; j < 30000; ++j )
  {
    v9 = (CMonster *)((char *)g_Monster + 6424 * j);
    if ( v9->m_bLive && v9->m_pCurMap == v11->m_pQuestSetup->pUseMap && v9->m_wMapLayerIndex == v11->m_wLayerIndex )
    {
      if ( v12 == -1 )
      {
        if ( !CMonster::IsMovable(v9) )
          continue;
      }
      else if ( v9->m_pMonRec->m_dwIndex != v12 )
      {
        continue;
      }
      if ( v11->m_MissionMgr.pCurMssionPtr->pAreaDummy )
      {
        v4 = v11->m_MissionMgr.pCurMssionPtr->pAreaDummy->m_wLineIndex;
        v10 = &v11->m_pQuestSetup->pUseMap->m_Dummy;
        if ( CExtDummy::IsInBBox(v10, v4, v9->m_fCurPos) )
          ++v7;
      }
      else
      {
        ++v7;
      }
    }
  }
  return v7;
}
