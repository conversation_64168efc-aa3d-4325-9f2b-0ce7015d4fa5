/*
 * Function: ?Buy@CUnmannedTraderUserInfo@@QEAAXEPEAU_unmannedtrader_buy_item_request_clzo@@PEAVCLogFile@@@Z
 * Address: 0x140353E80
 */

void __fastcall CUnmannedTraderUserInfo::Buy(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_buy_item_request_clzo *pRequest, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-368h]@1
  CPlayer *pkBuyer; // [sp+38h] [bp-330h]@5
  char v8; // [sp+44h] [bp-324h]@5
  unsigned __int16 Dst; // [sp+60h] [bp-308h]@7
  unsigned int v10; // [sp+64h] [bp-304h]@7
  char v11; // [sp+68h] [bp-300h]@7
  char v12; // [sp+69h] [bp-2FFh]@7
  char v13; // [sp+6Ah] [bp-2FEh]@7
  char v14; // [sp+6Bh] [bp-2FDh]@7
  char v15; // [sp+6Ch] [bp-2FCh]@7
  char v16; // [sp+6Dh] [bp-2FBh]@7
  unsigned __int8 v17; // [sp+6Eh] [bp-2FAh]@7
  int v18; // [sp+70h] [bp-2F8h]@9
  int v19[180]; // [sp+74h] [bp-2F4h]@9
  int j; // [sp+344h] [bp-24h]@7
  unsigned __int64 v21; // [sp+350h] [bp-18h]@4
  CUnmannedTraderUserInfo *v22; // [sp+370h] [bp+8h]@1
  char v23; // [sp+378h] [bp+10h]@1
  _unmannedtrader_buy_item_request_clzo *pRequesta; // [sp+380h] [bp+18h]@1

  pRequesta = pRequest;
  v23 = byType;
  v22 = this;
  v4 = &v6;
  for ( i = 216i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v21 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( (signed int)(unsigned __int8)byType < 2 )
  {
    pkBuyer = 0i64;
    v8 = CUnmannedTraderUserInfo::CheckBuy(v22, byType, pRequest, &pkBuyer, pkLogger);
    if ( v8 )
    {
      CUnmannedTraderUserInfo::SendBuyErrorResult(v22, v22->m_wInx, v8);
    }
    else
    {
      memset_0(&Dst, 0, 0x2E0ui64);
      Dst = v22->m_wInx;
      v10 = v22->m_dwUserSerial;
      v11 = CPlayerDB::GetRaceCode(&pkBuyer->m_Param);
      v12 = pkBuyer->m_byUserDgr;
      v13 = pRequesta->byDivision;
      v14 = pRequesta->byClass;
      v15 = pRequesta->bySubClass;
      v16 = v23;
      v17 = pRequesta->byNum;
      for ( j = 0; j < v17; ++j )
      {
        v19[18 * j] = pRequesta->List[j].dwPrice;
        *(&v18 + 18 * j) = pRequesta->List[j].dwRegistSerial;
      }
      CUnmannedTraderRequestLimiter::SetRequest(&v22->m_kRequestState, 3);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 64, (char *)&Dst, 736);
    }
  }
}
