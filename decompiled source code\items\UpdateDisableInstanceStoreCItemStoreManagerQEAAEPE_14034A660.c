/*
 * Function: ?UpdateDisableInstanceStore@CItemStoreManager@@QEAAEPEAD@Z
 * Address: 0x14034A660
 */

char __fastcall CItemStoreManager::UpdateDisableInstanceStore(CItemStoreManager *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *v6; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@4
  __int64 v8; // [sp+30h] [bp-18h]@6

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pData;
  for ( j = 0; j < *(_DWORD *)v6; ++j )
  {
    v8 = *((_QWORD *)v6 + 1) + 8i64 * j;
    if ( *(_DWORD *)(v8 + 4) )
      *(_BYTE *)v8 = CRFWorldDatabase::Update_DisableInstanceStore(pkDB, *(_DWORD *)(v8 + 4));
  }
  return 0;
}
