/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAURoomCharInfo@@_KU1@V?$allocator@URoomCharInfo@@@std@@@stdext@@YAXPEAURoomCharInfo@@_KAEBU1@AEAV?$allocator@URoomCharInfo@@@std@@@Z
 * Address: 0x140005952
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<RoomCharInfo *,unsigned __int64,RoomCharInfo,std::allocator<RoomCharInfo>>(RoomCharInfo *_First, unsigned __int64 _Count, RoomCharInfo *_Val, std::allocator<RoomCharInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<RoomCharInfo *,unsigned __int64,RoomCharInfo,std::allocator<RoomCharInfo>>(
    _First,
    _<PERSON>,
    _<PERSON>,
    _<PERSON>);
}
