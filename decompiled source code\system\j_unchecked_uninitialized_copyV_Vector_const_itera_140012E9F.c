/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@KV?$allocator@K@std@@@std@@PEAKV?$allocator@K@2@@stdext@@YAPEAKV?$_Vector_const_iterator@KV?$allocator@K@std@@@std@@0PEAKAEAV?$allocator@K@2@@Z
 * Address: 0x140012E9F
 */

unsigned int *__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>,unsigned long *,std::allocator<unsigned long>>(std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_First, std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_Last, unsigned int *_Dest, std::allocator<unsigned long> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>,unsigned long *,std::allocator<unsigned long>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
