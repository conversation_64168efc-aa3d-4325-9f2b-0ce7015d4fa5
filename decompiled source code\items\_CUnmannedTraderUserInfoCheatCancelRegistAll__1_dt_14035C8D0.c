/*
 * Function: _CUnmannedTraderUserInfo::CheatCancelRegistAll_::_1_::dtor$3
 * Address: 0x14035C8D0
 */

void __fastcall CUnmannedTraderUserInfo::CheatCancelRegistAll_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 280));
}
