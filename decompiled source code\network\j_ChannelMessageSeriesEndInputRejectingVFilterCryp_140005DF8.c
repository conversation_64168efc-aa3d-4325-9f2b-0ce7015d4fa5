/*
 * Function: j_?ChannelMessageSeriesEnd@?$InputRejecting@VFilter@CryptoPP@@@CryptoPP@@UEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x140005DF8
 */

void __fastcall __noreturn CryptoPP::InputRejecting<CryptoPP::Filter>::ChannelMessageSeriesEnd(CryptoPP::InputRejecting<CryptoPP::Filter> *this, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__formal, int a3, bool a4)
{
  CryptoPP::InputRejecting<CryptoPP::Filter>::ChannelMessageSeriesEnd(this, __formal, a3, a4);
}
