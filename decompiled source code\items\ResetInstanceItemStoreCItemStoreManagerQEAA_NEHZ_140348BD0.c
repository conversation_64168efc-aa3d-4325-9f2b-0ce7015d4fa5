/*
 * Function: ?ResetInstanceItemStore@CItemStoreManager@@QEAA_NEH@Z
 * Address: 0x140348BD0
 */

char __fastcall CItemStoreManager::ResetInstanceItemStore(CItemStoreManager *this, char byStoreType, int nSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-B8h]@1
  int j; // [sp+30h] [bp-88h]@6
  int k; // [sp+34h] [bp-84h]@13
  char v9[4]; // [sp+48h] [bp-70h]@6
  void *Dst; // [sp+50h] [bp-68h]@6
  bool *v11; // [sp+68h] [bp-50h]@8
  int v12; // [sp+70h] [bp-48h]@13
  void *v13; // [sp+78h] [bp-40h]@12
  void *v14; // [sp+80h] [bp-38h]@15
  void *v15; // [sp+88h] [bp-30h]@15
  CItemStore *v16; // [sp+90h] [bp-28h]@17
  CItemStore *v17; // [sp+98h] [bp-20h]@17
  unsigned __int64 v18; // [sp+A0h] [bp-18h]@12
  void *v19; // [sp+A8h] [bp-10h]@18
  CItemStoreManager *v20; // [sp+C0h] [bp+8h]@1

  v20 = this;
  v3 = &v6;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)byStoreType < 2 )
  {
    *(_DWORD *)v9 = 0;
    Dst = 0i64;
    for ( j = 0; j < v20->m_nInstanceItemStoreListNum; ++j )
    {
      v11 = &v20->m_InstanceItemStoreList[j].m_bUse;
      if ( *v11 && v11[1] == byStoreType && *((_DWORD *)v11 + 1) == nSerial )
      {
        *v11 = 0;
        if ( *((_DWORD *)v11 + 2) > 0 )
        {
          *(_DWORD *)v9 = *((_DWORD *)v11 + 2);
          v18 = *((_DWORD *)v11 + 2);
          v13 = operator new[](saturated_mul(8ui64, v18));
          Dst = v13;
          memset_0(v13, 0, 8i64 * *((_DWORD *)v11 + 2));
        }
        v12 = 0;
        for ( k = 0; k < *((_DWORD *)v11 + 2); ++k )
        {
          v14 = *(void **)(*((_QWORD *)v11 + 2) + 120i64 * k + 48);
          operator delete[](v14);
          v15 = *(void **)(*((_QWORD *)v11 + 2) + 120i64 * k + 80);
          operator delete[](v15);
          *(_QWORD *)(*((_QWORD *)v11 + 2) + 120i64 * k + 48) = 0i64;
          *(_QWORD *)(*((_QWORD *)v11 + 2) + 120i64 * k + 80) = 0i64;
          *((_DWORD *)Dst + 2 * k + 1) = *(_DWORD *)(*((_QWORD *)v11 + 2) + 68i64);
          ++v12;
        }
        if ( *((_DWORD *)v11 + 2) > 0 )
        {
          v17 = (CItemStore *)*((_QWORD *)v11 + 2);
          v16 = v17;
          if ( v17 )
            v19 = CItemStore::`vector deleting destructor'(v16, 3u);
          else
            v19 = 0i64;
          *((_DWORD *)v11 + 2) -= v12;
          if ( *((_DWORD *)v11 + 2) < 0 )
            *((_DWORD *)v11 + 2) = 0;
        }
        *((_QWORD *)v11 + 2) = 0i64;
        break;
      }
    }
    if ( *(_DWORD *)v9 <= 0 )
    {
      result = 0;
    }
    else
    {
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 113, v9, 16);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
