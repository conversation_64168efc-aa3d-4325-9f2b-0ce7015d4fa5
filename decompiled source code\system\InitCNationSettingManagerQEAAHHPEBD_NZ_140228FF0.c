/*
 * Function: ?Init@CNationSettingManager@@QEAAHHPEBD_N@Z
 * Address: 0x140228FF0
 */

__int64 __fastcall CNationSettingManager::Init(CNationSettingManager *this, int iNationCode, const char *szNationCodeStr, bool bServiceMode)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-D8h]@1
  CNationSettingFactoryGroup v8; // [sp+30h] [bp-A8h]@4
  int v9; // [sp+B4h] [bp-24h]@4
  unsigned int v10; // [sp+B8h] [bp-20h]@5
  unsigned int v11; // [sp+BCh] [bp-1Ch]@7
  unsigned int v12; // [sp+C0h] [bp-18h]@9
  unsigned int v13; // [sp+C4h] [bp-14h]@10
  __int64 v14; // [sp+C8h] [bp-10h]@4
  CNationSettingManager *v15; // [sp+E0h] [bp+8h]@1
  int iNationCodea; // [sp+E8h] [bp+10h]@1
  char *szNationCodeStra; // [sp+F0h] [bp+18h]@1
  bool v18; // [sp+F8h] [bp+20h]@1

  v18 = bServiceMode;
  szNationCodeStra = (char *)szNationCodeStr;
  iNationCodea = iNationCode;
  v15 = this;
  v4 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = -2i64;
  CNationSettingFactoryGroup::CNationSettingFactoryGroup(&v8);
  v9 = CNationSettingFactoryGroup::Init(&v8);
  if ( v9 )
  {
    v10 = -1;
    CNationSettingFactoryGroup::~CNationSettingFactoryGroup(&v8);
    result = v10;
  }
  else
  {
    v15->m_pData = CNationSettingFactoryGroup::Create(&v8, iNationCodea, szNationCodeStra, v18);
    if ( v15->m_pData )
    {
      v9 = ((int (__fastcall *)(CNationSettingData *))v15->m_pData->vfptr->Init)(v15->m_pData);
      if ( v9 )
      {
        v12 = -3;
        CNationSettingFactoryGroup::~CNationSettingFactoryGroup(&v8);
        result = v12;
      }
      else
      {
        v13 = 0;
        CNationSettingFactoryGroup::~CNationSettingFactoryGroup(&v8);
        result = v13;
      }
    }
    else
    {
      v11 = -2;
      CNationSettingFactoryGroup::~CNationSettingFactoryGroup(&v8);
      result = v11;
    }
  }
  return result;
}
