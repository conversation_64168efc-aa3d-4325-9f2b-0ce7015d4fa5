/*
 * Function: j_?StaticAlgorithmName@?$CipherModeFinalTemplate_CipherHolder@V?$BlockCipherFinal@$0A@VEnc@Rijndael@CryptoPP@@@CryptoPP@@VCBC_Encryption@2@@CryptoPP@@SA?AV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@XZ
 * Address: 0x140008058
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption>::StaticAlgorithmName(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *result)
{
  return CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption>::StaticAlgorithmName(result);
}
