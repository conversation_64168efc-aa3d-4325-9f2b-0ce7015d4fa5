/*
 * Function: ?IsHaveMentalTicket@CPlayer@@QEAA_NXZ
 * Address: 0x1400CE610
 */

bool __fastcall CPlayer::IsHaveMentalTicket(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+30h] [bp-18h]@4
  char v7; // [sp+31h] [bp-17h]@4
  char v8; // [sp+32h] [bp-16h]@4
  char v9; // [sp+33h] [bp-15h]@4
  CPlayer *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
  v7 = CHolyStoneSystem::GetStartHour(&g_HolySys);
  v8 = CHolyStoneSystem::GetStartDay(&g_HolySys);
  v9 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
  v3 = CHolyStoneSystem::GetStartYear(&g_HolySys);
  return MiningTicket::AuthLastCriTicket(&v10->m_MinigTicket, v3, v9, v8, v7, v6) != 0;
}
