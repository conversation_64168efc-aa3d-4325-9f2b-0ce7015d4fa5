/*
 * Function: ?Select_TakeItem@CRFWorldDatabase@@QEAAEKPEAU_worlddb_item_list@@@Z
 * Address: 0x140494930
 */

char __fastcall CRFWorldDatabase::Select_TakeItem(CRFWorldDatabase *this, unsigned int dwAvatorSerial, _worlddb_item_list *itemList)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@16
  SQLLEN v9; // [sp+38h] [bp-150h]@16
  __int16 v10; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v12; // [sp+164h] [bp-24h]@4
  unsigned __int64 v13; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v14; // [sp+190h] [bp+8h]@1
  _worlddb_item_list *v15; // [sp+1A0h] [bp+18h]@1

  v15 = itemList;
  v14 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  v12 = 0;
  sprintf(&Dest, "{ CALL pSelect_TakeItem_20061115( %d ) }", dwAvatorSerial);
  if ( v14->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v14->vfptr, &Dest);
  if ( v14->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v14->vfptr) )
  {
    v10 = SQLExecDirectA_0(v14->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v14->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v14->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v14->vfptr, v10, v14->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      while ( 1 )
      {
        v10 = SQLFetch_0(v14->m_hStmtSelect);
        if ( v10 && v10 != 1 )
          break;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 1u, 4, &v15->itemList[(signed __int64)v12].dwItemCode_K, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 2u, -25, &v15->itemList[(signed __int64)v12].dwItemCode_D, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 3u, 4, &v15->itemList[(signed __int64)v12].dwItemCode_U, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)32;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 4u, 1, v15->itemList[(signed __int64)v12].szDate, 32i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 5u, -25, &v15->itemList[(signed __int64)v12].lnUID, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v14->m_hStmtSelect, 6u, 4, &v15->itemList[(signed __int64)v12++].dwT, 0i64, &v9);
        if ( v10 )
        {
          if ( v10 != 1 )
            break;
        }
      }
      v15->byItemCount = v12;
      if ( v14->m_hStmtSelect )
        SQLCloseCursor_0(v14->m_hStmtSelect);
      if ( v14->m_bSaveDBLog )
        CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "%s Success", &Dest);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v14->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1;
  }
  return result;
}
