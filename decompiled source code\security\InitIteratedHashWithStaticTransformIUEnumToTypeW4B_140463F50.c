/*
 * Function: ?Init@?$IteratedHashWithStaticTransform@IU?$EnumToType@W4ByteOrder@CryptoPP@@$00@CryptoPP@@$0EA@$0BE@VSHA1@2@$0A@@CryptoPP@@MEAAXXZ
 * Address: 0x140463F50
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0>::Init(CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-28h]@1
  CryptoPP::IteratedHashWithStaticTransform<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,20,CryptoPP::SHA1,0> *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CryptoPP::SecBlock<unsigned int,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0>>::operator unsigned int *((CryptoPP::SecBlock<unsigned int,CryptoPP::FixedSizeAllocatorWithCleanup<unsigned int,16,CryptoPP::NullAllocator<unsigned int>,0> > *)&v5->m_state.m_alloc);
  CryptoPP::SHA1::InitState(v3);
}
