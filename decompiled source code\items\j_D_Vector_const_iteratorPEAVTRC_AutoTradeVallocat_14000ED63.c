/*
 * Function: j_??D?$_Vector_const_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBAAEBQEAVTRC_AutoTrade@@XZ
 * Address: 0x14000ED63
 */

TRC_AutoTrade *const *__fastcall std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator*(std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  return std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator*(this);
}
