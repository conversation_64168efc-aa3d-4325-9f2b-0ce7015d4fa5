/*
 * Function: j_?Init@CNormalGuildBattle@GUILD_BATTLE@@QEAAXPEAVCGuild@@0PEAVCNormalGuildBattleField@2@EPEAVCNormalGuildBattleStateList@2@@Z
 * Address: 0x140010A37
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::Init(GUILD_BATTLE::CNormalGuildBattle *this, CGuild *pk1P, CGuild *pk2P, GUILD_BATTLE::CNormalGuildBattleField *pkField, char by<PERSON>um<PERSON>, GUILD_BATTLE::CNormalGuildBattleStateList *pkStateList)
{
  GUILD_BATTLE::CNormalGuildBattle::Init(this, pk1P, pk2P, pkField, byNumber, pkStateList);
}
