/*
 * Function: j_?_Ufill@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderSortType@@PEAPEAV3@_KAEBQEAV3@@Z
 * Address: 0x1400114BE
 */

CUnmannedTraderSortType **__fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Ufill(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this, CUnmannedTraderSortType **_Ptr, unsigned __int64 _Count, CUnmannedTraderSortType *const *_Val)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _<PERSON>);
}
