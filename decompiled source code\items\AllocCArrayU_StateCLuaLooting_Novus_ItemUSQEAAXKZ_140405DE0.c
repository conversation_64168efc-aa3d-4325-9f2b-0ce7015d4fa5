/*
 * Function: ?Alloc@?$CArray@U_State@CLuaLooting_Novus_Item@@@US@@QEAAXK@Z
 * Address: 0x140405DE0
 */

void __fastcall US::CArray<CLuaLooting_Novus_Item::_State>::Alloc(US::CArray<CLuaLooting_Novus_Item::_State> *this, unsigned int dwCount)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  unsigned __int64 count; // [sp+20h] [bp-38h]@6
  void *__t; // [sp+30h] [bp-28h]@6
  __int64 v7; // [sp+38h] [bp-20h]@4
  CLuaLooting_Novus_Item::_State *v8; // [sp+40h] [bp-18h]@7
  US::CArray<CLuaLooting_Novus_Item::_State> *v9; // [sp+60h] [bp+8h]@1
  unsigned int v10; // [sp+68h] [bp+10h]@1

  v10 = dwCount;
  v9 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = -2i64;
  if ( !v9->m_bAlloc && dwCount )
  {
    count = dwCount;
    __t = operator new[](dwCount);
    if ( __t )
    {
      `vector constructor iterator'(
        __t,
        1ui64,
        count,
        (void *(__cdecl *)(void *))CLuaLooting_Novus_Item::_State::_State);
      v8 = (CLuaLooting_Novus_Item::_State *)__t;
    }
    else
    {
      v8 = 0i64;
    }
    v9->m_pBuffer = v8;
    v9->m_dwCount = v10;
    v9->m_bAlloc = 1;
  }
}
