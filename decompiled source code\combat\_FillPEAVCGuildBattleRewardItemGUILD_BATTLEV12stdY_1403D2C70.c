/*
 * Function: ??$_Fill@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@V12@@std@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@0AEBV12@@Z
 * Address: 0x1403D2C70
 */

void __fastcall std::_Fill<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  char *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *v6; // [sp+30h] [bp+8h]@1

  v6 = _First;
  v3 = &v5;
  for ( i = 6i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 += 4;
  }
  while ( v6 != _Last )
  {
    qmemcpy(&v5, _Val, 0x10ui64);
    qmemcpy(v6, &v5, sizeof(GUILD_BATTLE::CGuildBattleRewardItem));
    ++v6;
  }
}
