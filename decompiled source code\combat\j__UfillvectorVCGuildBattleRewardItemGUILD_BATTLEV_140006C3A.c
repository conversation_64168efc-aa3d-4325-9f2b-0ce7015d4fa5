/*
 * Function: j_?_Ufill@?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@IEAAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV34@_KAEBV34@@Z
 * Address: 0x140006C3A
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Ufill(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr, unsigned __int64 _Count, GUILD_BATTLE::CG<PERSON>BattleRewardItem *_Val)
{
  return std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Ufill(
           this,
           _Ptr,
           _Count,
           _Val);
}
