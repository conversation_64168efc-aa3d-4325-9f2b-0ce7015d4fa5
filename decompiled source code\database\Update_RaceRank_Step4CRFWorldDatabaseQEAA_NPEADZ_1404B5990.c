/*
 * Function: ?Update_RaceRank_Step4@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B5990
 */

char __fastcall CRFWorldDatabase::Update_RaceRank_Step4(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-478h]@1
  void *SQLStmt; // [sp+20h] [bp-458h]@7
  __int16 v7; // [sp+30h] [bp-448h]@9
  char DstBuf; // [sp+50h] [bp-428h]@4
  char v9; // [sp+51h] [bp-427h]@4
  unsigned __int64 v10; // [sp+460h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+480h] [bp+8h]@1
  char *szDatea; // [sp+488h] [bp+10h]@1

  szDatea = szDate;
  v11 = this;
  v2 = &v5;
  for ( i = 284i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v9, 0, 0x3FFui64);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step4(szDate(%s)) : Start Create tbl_PvpRank%s Table",
    szDate,
    szDate);
  sprintf_s(&DstBuf, 0x400ui64, "tbl_PvpRank%s", szDatea);
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v11->vfptr, &DstBuf) )
  {
    sprintf_s(&DstBuf, 0x400ui64, "drop table [dbo].[tbl_PvpRank%s]", szDatea);
    if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &DstBuf, 1) )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank_Step4(szDate(%s)) : Drop tbl_PvpRank%s Table Fail!",
        szDatea,
        szDatea);
      return 0;
    }
  }
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 0);
  SQLStmt = szDatea;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "CREATE TABLE [dbo].[tbl_PvpRank%s] ( [Rank] [int] NOT NULL, [Rate] [int] NOT NULL, [serial] [int] NOT NULL ,[name] ["
    "varchar] (17) NOT NULL ,[lv] [int] NOT NULL, [Race] [int] NOT NULL, [Grade] [smallint] NOT NULL ,[PvpPoint] [float] "
    "NOT NULL, [GuildSerial] [int] NOT NULL ,[GuildName] [varchar] (17) NOT NULL ,CONSTRAINT PK_tbl_PvpRank%s PRIMARY KEY"
    "  CLUSTERED ( [serial] ) on [PRIMARY] ) ON [PRIMARY]",
    szDatea);
  if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &DstBuf, 1) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step4(szDate(%s)) : Create tbl_PvpRank%s Table Fail!",
      szDatea,
      szDatea);
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
    return 0;
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step4(szDate(%s)) : End Create tbl_PvpRank%s Table",
    szDatea,
    szDatea);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(szDate(%s)) : Start Union #tbl_PvpRankB,C,A To tbl_PvpRank%s",
    szDatea,
    szDatea);
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "insert tbl_PvpRank%s(Rank, Rate, Serial, Name, lv, race, PvpPoint, Grade, GuildSerial, GuildName) select 0, rate, se"
    "rial, name, lv, race, PvpPoint, 0, GuildSerial, '*' from #tbl_PvpRankB union select 0, rate, serial, name, lv, race,"
    " PvpPoint, 0, GuildSerial, '*' from #tbl_PvpRankC union select 0, rate, serial, name, lv, race, PvpPoint, 0, GuildSe"
    "rial, '*' from #tbl_PvpRankA ",
    szDatea);
  v7 = SQLExecDirectA_0(v11->m_hStmtUpdate, &DstBuf, -3);
  if ( v7 && v7 != 1 )
  {
    if ( v7 != 100 )
    {
      CRFNewDatabase::FmtLog(
        (CRFNewDatabase *)&v11->vfptr,
        "CRFWorldDatabase::Update_RaceRank(szDate(%s)) : Union #tbl_PvpRankB,C,A To tbl_PvpRank%s Fail SQL_ERROR!",
        szDatea,
        szDatea);
      SQLStmt = v11->m_hStmtUpdate;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &DstBuf, "SQLExecDirect", SQLStmt);
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
      CRFWorldDatabase::Update_RaceRank_Step6(v11, szDatea);
      return 0;
    }
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank(szDate(%s)) : Union #tbl_PvpRankB,C,A To tbl_PvpRank%s Fail NO_DATA!",
      szDatea,
      szDatea);
  }
  CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v11->vfptr);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank(szDate(%s)) : End Union #tbl_PvpRankB,C,A To tbl_PvpRank%s",
    szDatea,
    szDatea);
  return 1;
}
