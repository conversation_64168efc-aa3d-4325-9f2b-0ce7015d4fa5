/**
 * @file CGuildMemberManager_Core.cpp
 * @brief Modern C++20 Guild Member Manager core implementation
 * 
 * This file provides the core implementation of the CGuildMemberManager class
 * with comprehensive member management, authentication, and communication.
 */

#include "../Headers/CGuildMemberManager.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <sstream>
#include <iomanip>

// Legacy includes for compatibility
extern "C" {
    class CGuild {
    public:
        uint32_t m_dwSerial;
        uint8_t m_byGrade;
        uint8_t m_byRace;
        char m_wszName[50];
        char m_aszName[17];
        char m_wszGreetingMsg[256];
        char m_szHistoryFileName[260];
        int m_nApplierNum;
        int m_nMemberNum;
        _guild_member_info m_MemberData[50];
        _guild_master_info m_MasterData;
        _guild_member_info* m_pGuildCommittee[3];
        double m_dTotalDalant;
        double m_dTotalGold;
        uint32_t m_dwEmblemBack;
        uint32_t m_dwEmblemMark;
        uint32_t m_dwGuildBattleTotWin;
        uint32_t m_dwGuildBattleTotDraw;
        uint32_t m_dwGuildBattleTotLose;
        bool m_bPossibleElectMaster;
        _io_money_data m_IOMoneyHistory[100];
        int m_nIOMoneyHistoryNum;
        
        static void MakeDownMemberPacket(CGuild* guild);
        static void MakeDownApplierPacket(CGuild* guild);
        static void MakeQueryInfoPacket(CGuild* guild);
        static void MakeMoneyIOPacket(CGuild* guild);
        static void MakeBuddyPacket(CGuild* guild);
    };
    
    class CRFWorldDatabase {
    public:
        static bool Select_GuildMasterLastConn(CRFWorldDatabase* db, uint32_t masterSerial, uint32_t timeThreshold, uint32_t* lastConnTime);
    };
    
    class CNetProcess {
    public:
        static void LoadSendMsg(void* netObj, uint16_t playerIndex, void* msgType, void* msgData, uint32_t msgSize);
    };
    
    extern void* unk_1414F2088;
    extern uint32_t GetConnectTime_AddBySec(int seconds);
    extern uint32_t GetLoopTime();
    extern void strcpy_0(char* dest, const char* src);
    extern void strcpy_s(char* dest, size_t destSize, const char* src);
    extern void W2M(const char* wide, char* multi, uint32_t size);
    extern void memcpy_0(void* dest, const void* src, size_t size);
}

namespace NexusProtection::Guild {

std::string GuildMemberInfo::ToString() const {
    std::ostringstream oss;
    oss << "Member{Serial:" << memberSerial 
        << ", Name:'" << memberName 
        << "', Level:" << static_cast<int>(level)
        << ", PvP:" << pvpPoints
        << ", Class:" << static_cast<int>(memberClass)
        << ", Status:" << static_cast<int>(status)
        << ", Online:" << (IsOnline() ? "Yes" : "No") << "}";
    return oss.str();
}

void GuildMemberInfo::UpdateActivity() {
    lastActivityTime = std::chrono::steady_clock::now();
}

CGuildMemberManager& CGuildMemberManager::GetInstance() {
    static CGuildMemberManager instance;
    return instance;
}

GuildMemberResult CGuildMemberManager::Initialize(const GuildMemberConfig& config) {
    try {
        if (m_isInitialized.load()) {
            return GuildMemberResult::Success;
        }
        
        // Validate configuration
        if (!config.IsValid()) {
            LogMemberActivity(0, "Initialize: Invalid configuration provided");
            return GuildMemberResult::InvalidParameters;
        }
        
        // Store configuration
        {
            std::lock_guard<std::mutex> lock(m_configMutex);
            m_config = config;
        }
        
        // Reset statistics
        ResetStatistics();
        
        // Initialize member containers
        {
            std::lock_guard<std::mutex> lock(m_membersMutex);
            m_members.clear();
            m_committeeMembers.fill(nullptr);
            m_masterInfo = GuildMasterInfo{};
        }
        
        m_isInitialized.store(true);
        m_isShutdown.store(false);
        
        std::cout << "[INFO] Guild Member Manager initialized successfully" << std::endl;
        std::cout << "[INFO] Configuration: maxMembers=" << config.maxMembers 
                  << ", maxCommittee=" << config.maxCommitteeMembers
                  << ", tracking=" << (config.enableMemberTracking ? "enabled" : "disabled") << std::endl;
        
        return GuildMemberResult::Success;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("Initialize exception: ") + e.what());
        return GuildMemberResult::SystemError;
    }
}

GuildMemberResult CGuildMemberManager::Shutdown() {
    try {
        if (!m_isInitialized.load() || m_isShutdown.load()) {
            return GuildMemberResult::Success;
        }
        
        // Clear callbacks
        {
            std::lock_guard<std::mutex> lock(m_callbackMutex);
            m_memberLoginCallback = nullptr;
            m_memberLogoutCallback = nullptr;
            m_memberJoinCallback = nullptr;
            m_memberLeaveCallback = nullptr;
            m_masterChangeCallback = nullptr;
            m_permissionChangeCallback = nullptr;
            m_memberMessageCallback = nullptr;
        }
        
        // Clear member data
        {
            std::lock_guard<std::mutex> lock(m_membersMutex);
            m_members.clear();
            m_committeeMembers.fill(nullptr);
            m_masterInfo = GuildMasterInfo{};
        }
        
        m_isShutdown.store(true);
        m_isInitialized.store(false);
        
        std::cout << "[INFO] Guild Member Manager shutdown completed" << std::endl;
        
        return GuildMemberResult::Success;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("Shutdown exception: ") + e.what());
        return GuildMemberResult::SystemError;
    }
}

GuildMemberResult CGuildMemberManager::SetupGuild(const GuildSetupData& setupData) {
    try {
        if (!m_isInitialized.load()) {
            return GuildMemberResult::SystemError;
        }
        
        // Validate setup data
        if (!setupData.IsValid()) {
            LogMemberActivity(0, "SetupGuild: Invalid setup data provided");
            UpdateStatistics("SetupGuild", false);
            return GuildMemberResult::InvalidParameters;
        }
        
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        // Store guild basic information
        m_guildSerial = setupData.guildSerial;
        m_guildName = setupData.guildName;
        m_guildRace = setupData.guildRace;
        m_guildGrade = setupData.guildGrade;
        
        // Store financial data
        m_totalDalant = setupData.totalDalant;
        m_totalGold = setupData.totalGold;
        m_moneyHistory = setupData.moneyHistory;
        
        // Store battle statistics
        m_battleWins = setupData.battleWins;
        m_battleDraws = setupData.battleDraws;
        m_battleLosses = setupData.battleLosses;
        
        // Initialize members
        m_members.clear();
        for (const auto& member : setupData.members) {
            auto memberPtr = std::make_shared<GuildMemberInfo>(member);
            m_members[member.memberSerial] = memberPtr;
        }
        
        // Setup master information
        m_masterInfo = setupData.masterInfo;
        if (m_masterInfo.IsValid()) {
            auto masterIter = m_members.find(m_masterInfo.masterSerial);
            if (masterIter != m_members.end()) {
                m_masterInfo.memberInfo = masterIter->second;
                masterIter->second->memberClass = GuildMemberClass::Master;
            }
        }
        
        // Setup committee members
        if (!SetupCommitteeMembers(setupData.members)) {
            LogMemberActivity(0, "SetupGuild: Failed to setup committee members");
            UpdateStatistics("SetupGuild", false);
            return GuildMemberResult::SystemError;
        }
        
        // Check master election eligibility
        if (m_masterInfo.IsValid()) {
            m_masterInfo.canElectNewMaster = CheckMasterElectionEligibility(m_masterInfo.masterSerial);
        } else {
            m_masterInfo.canElectNewMaster = true;
        }
        
        // Generate member packets
        if (!GenerateMemberPackets()) {
            LogMemberActivity(0, "SetupGuild: Failed to generate member packets");
            UpdateStatistics("SetupGuild", false);
            return GuildMemberResult::SystemError;
        }
        
        UpdateStatistics("SetupGuild", true);
        
        std::cout << "[INFO] Guild setup completed: " << m_guildName 
                  << " (Serial: " << m_guildSerial << ")" << std::endl;
        std::cout << "[INFO] Members: " << m_members.size() 
                  << ", Master: " << m_masterInfo.masterSerial << std::endl;
        
        return GuildMemberResult::Success;
        
    } catch (const std::exception& e) {
        LogMemberActivity(0, std::string("SetupGuild exception: ") + e.what());
        UpdateStatistics("SetupGuild", false);
        return GuildMemberResult::SystemError;
    }
}

std::shared_ptr<GuildMemberInfo> CGuildMemberManager::LoginMember(uint32_t memberSerial, CPlayer* playerPtr) {
    try {
        if (!m_isInitialized.load()) {
            return nullptr;
        }
        
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        auto memberIter = m_members.find(memberSerial);
        if (memberIter == m_members.end()) {
            LogMemberActivity(memberSerial, "LoginMember: Member not found");
            UpdateStatistics("LoginMember", false);
            return nullptr;
        }
        
        auto& member = memberIter->second;
        
        // Update member login information
        member->playerPtr = playerPtr;
        member->status = GuildMemberStatus::Online;
        member->lastLoginTime = std::chrono::steady_clock::now();
        member->UpdateActivity();
        member->totalLogins++;
        
        // Update statistics
        m_stats.totalMemberLogins.fetch_add(1);
        m_stats.activeMembersOnline.fetch_add(1);
        
        UpdateStatistics("LoginMember", true);
        
        std::cout << "[INFO] Member logged in: " << member->memberName 
                  << " (Serial: " << memberSerial << ")" << std::endl;
        
        return member;
        
    } catch (const std::exception& e) {
        LogMemberActivity(memberSerial, std::string("LoginMember exception: ") + e.what());
        UpdateStatistics("LoginMember", false);
        return nullptr;
    }
}

GuildMemberResult CGuildMemberManager::SendMemberLoginNotification(uint32_t memberSerial, uint16_t mapCode, uint16_t regionIndex) {
    try {
        if (!m_isInitialized.load()) {
            return GuildMemberResult::SystemError;
        }
        
        std::lock_guard<std::mutex> lock(m_membersMutex);
        
        // Find the member who logged in
        auto memberIter = m_members.find(memberSerial);
        if (memberIter == m_members.end()) {
            LogMemberActivity(memberSerial, "SendMemberLoginNotification: Member not found");
            return GuildMemberResult::MemberNotFound;
        }
        
        auto& loginMember = memberIter->second;
        loginMember->mapCode = mapCode;
        loginMember->regionIndex = regionIndex;
        
        // Prepare login message
        struct LoginMessage {
            uint32_t memberSerial;
            uint16_t mapCode;
            uint8_t padding;
        } loginMsg = {memberSerial, mapCode, 0};
        
        uint8_t msgType[2] = {27, 44}; // Login message type
        
        // Send notification to all online members
        uint32_t notificationsSent = 0;
        for (const auto& [serial, member] : m_members) {
            if (member->IsOnline() && member->playerPtr && serial != memberSerial) {
                // This would call CNetProcess::LoadSendMsg in the actual implementation
                // CNetProcess::LoadSendMsg(unk_1414F2088, member->playerPtr->m_ObjID.m_wIndex, msgType, &loginMsg, sizeof(loginMsg));
                notificationsSent++;
            }
        }
        
        m_stats.messagesSent.fetch_add(notificationsSent);
        NotifyMemberLogin(memberSerial, mapCode, regionIndex);
        
        std::cout << "[INFO] Login notification sent for member " << memberSerial 
                  << " to " << notificationsSent << " online members" << std::endl;
        
        return GuildMemberResult::Success;
        
    } catch (const std::exception& e) {
        LogMemberActivity(memberSerial, std::string("SendMemberLoginNotification exception: ") + e.what());
        return GuildMemberResult::SystemError;
    }
}

GuildMemberResult CGuildMemberManager::SendMemberLogoutNotification(uint32_t memberSerial) {
    try {
        if (!m_isInitialized.load()) {
            return GuildMemberResult::SystemError;
        }

        std::lock_guard<std::mutex> lock(m_membersMutex);

        // Find the member who logged out
        auto memberIter = m_members.find(memberSerial);
        if (memberIter == m_members.end()) {
            LogMemberActivity(memberSerial, "SendMemberLogoutNotification: Member not found");
            return GuildMemberResult::MemberNotFound;
        }

        auto& logoutMember = memberIter->second;
        logoutMember->status = GuildMemberStatus::Offline;
        logoutMember->playerPtr = nullptr;

        // Prepare logout message
        uint32_t logoutMsg = memberSerial;
        uint8_t msgType[2] = {27, 45}; // Logout message type

        // Send notification to all online members
        uint32_t notificationsSent = 0;
        for (const auto& [serial, member] : m_members) {
            if (member->IsOnline() && member->playerPtr && serial != memberSerial) {
                // This would call CNetProcess::LoadSendMsg in the actual implementation
                // CNetProcess::LoadSendMsg(unk_1414F2088, member->playerPtr->m_ObjID.m_wIndex, msgType, &logoutMsg, sizeof(logoutMsg));
                notificationsSent++;
            }
        }

        // Update statistics
        m_stats.totalMemberLogouts.fetch_add(1);
        m_stats.activeMembersOnline.fetch_sub(1);
        m_stats.messagesSent.fetch_add(notificationsSent);

        NotifyMemberLogout(memberSerial);

        std::cout << "[INFO] Logout notification sent for member " << memberSerial
                  << " to " << notificationsSent << " online members" << std::endl;

        return GuildMemberResult::Success;

    } catch (const std::exception& e) {
        LogMemberActivity(memberSerial, std::string("SendMemberLogoutNotification exception: ") + e.what());
        return GuildMemberResult::SystemError;
    }
}

GuildMemberResult CGuildMemberManager::SendMemberLeaveNotification(uint32_t memberSerial, bool selfLeave, bool punishment) {
    try {
        if (!m_isInitialized.load()) {
            return GuildMemberResult::SystemError;
        }

        std::lock_guard<std::mutex> lock(m_membersMutex);

        // Find the member who is leaving
        auto memberIter = m_members.find(memberSerial);
        if (memberIter == m_members.end()) {
            LogMemberActivity(memberSerial, "SendMemberLeaveNotification: Member not found");
            return GuildMemberResult::MemberNotFound;
        }

        // Prepare leave message
        struct LeaveMessage {
            uint8_t selfLeave;
            bool punishment;
            uint32_t memberSerial;
        } leaveMsg = {selfLeave ? 1u : 0u, punishment, memberSerial};

        uint8_t msgType[2] = {27, 46}; // Leave message type

        // Send notification to all online members
        uint32_t notificationsSent = 0;
        for (const auto& [serial, member] : m_members) {
            if (member->IsOnline() && member->playerPtr) {
                // This would call CNetProcess::LoadSendMsg in the actual implementation
                // CNetProcess::LoadSendMsg(unk_1414F2088, member->playerPtr->m_ObjID.m_wIndex, msgType, &leaveMsg, sizeof(leaveMsg));
                notificationsSent++;
            }
        }

        // Remove member from guild
        m_members.erase(memberIter);

        // Update statistics
        m_stats.totalMemberLeaves.fetch_add(1);
        m_stats.messagesSent.fetch_add(notificationsSent);

        NotifyMemberLeave(memberSerial, selfLeave, punishment);

        std::cout << "[INFO] Leave notification sent for member " << memberSerial
                  << " (self=" << selfLeave << ", punishment=" << punishment
                  << ") to " << notificationsSent << " online members" << std::endl;

        return GuildMemberResult::Success;

    } catch (const std::exception& e) {
        LogMemberActivity(memberSerial, std::string("SendMemberLeaveNotification exception: ") + e.what());
        return GuildMemberResult::SystemError;
    }
}

GuildMemberResult CGuildMemberManager::UpdateGuildMaster(uint32_t newMasterSerial) {
    try {
        if (!m_isInitialized.load()) {
            return GuildMemberResult::SystemError;
        }

        std::lock_guard<std::mutex> lock(m_membersMutex);

        // Find the new master
        auto newMasterIter = m_members.find(newMasterSerial);
        if (newMasterIter == m_members.end()) {
            LogMemberActivity(newMasterSerial, "UpdateGuildMaster: New master not found");
            return GuildMemberResult::MemberNotFound;
        }

        uint32_t oldMasterSerial = m_masterInfo.masterSerial;

        // Update old master to regular member
        if (m_masterInfo.IsValid() && m_masterInfo.memberInfo) {
            m_masterInfo.memberInfo->memberClass = GuildMemberClass::Member;
        }

        // Set new master
        m_masterInfo.masterSerial = newMasterSerial;
        m_masterInfo.memberInfo = newMasterIter->second;
        m_masterInfo.lastConnectionCheck = std::chrono::steady_clock::now();
        m_masterInfo.canElectNewMaster = false;

        // Update new master class
        newMasterIter->second->memberClass = GuildMemberClass::Master;

        // Update statistics
        m_stats.totalMasterChanges.fetch_add(1);

        NotifyMasterChange(oldMasterSerial, newMasterSerial);

        std::cout << "[INFO] Guild master updated from " << oldMasterSerial
                  << " to " << newMasterSerial << std::endl;

        return GuildMemberResult::Success;

    } catch (const std::exception& e) {
        LogMemberActivity(newMasterSerial, std::string("UpdateGuildMaster exception: ") + e.what());
        return GuildMemberResult::SystemError;
    }
}

std::shared_ptr<GuildMemberInfo> CGuildMemberManager::GetMember(uint32_t memberSerial) const {
    try {
        std::lock_guard<std::mutex> lock(m_membersMutex);

        auto memberIter = m_members.find(memberSerial);
        if (memberIter != m_members.end()) {
            return memberIter->second;
        }

        return nullptr;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] GetMember exception: " << e.what() << std::endl;
        return nullptr;
    }
}

std::vector<std::shared_ptr<GuildMemberInfo>> CGuildMemberManager::GetOnlineMembers() const {
    try {
        std::lock_guard<std::mutex> lock(m_membersMutex);

        std::vector<std::shared_ptr<GuildMemberInfo>> onlineMembers;
        for (const auto& [serial, member] : m_members) {
            if (member->IsOnline()) {
                onlineMembers.push_back(member);
            }
        }

        return onlineMembers;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] GetOnlineMembers exception: " << e.what() << std::endl;
        return {};
    }
}

uint32_t CGuildMemberManager::GetMemberCount() const {
    try {
        std::lock_guard<std::mutex> lock(m_membersMutex);
        return static_cast<uint32_t>(m_members.size());

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] GetMemberCount exception: " << e.what() << std::endl;
        return 0;
    }
}

uint32_t CGuildMemberManager::GetOnlineMemberCount() const {
    try {
        std::lock_guard<std::mutex> lock(m_membersMutex);

        uint32_t count = 0;
        for (const auto& [serial, member] : m_members) {
            if (member->IsOnline()) {
                count++;
            }
        }

        return count;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] GetOnlineMemberCount exception: " << e.what() << std::endl;
        return 0;
    }
}

bool CGuildMemberManager::HasPermission(uint32_t memberSerial, GuildMemberClass requiredClass) const {
    try {
        std::lock_guard<std::mutex> lock(m_membersMutex);

        auto memberIter = m_members.find(memberSerial);
        if (memberIter == m_members.end()) {
            return false;
        }

        auto memberClass = memberIter->second->memberClass;

        // Master has all permissions
        if (memberClass == GuildMemberClass::Master) {
            return true;
        }

        // Committee has committee and member permissions
        if (memberClass == GuildMemberClass::Committee &&
            (requiredClass == GuildMemberClass::Committee || requiredClass == GuildMemberClass::Member)) {
            return true;
        }

        // Member has only member permissions
        if (memberClass == GuildMemberClass::Member && requiredClass == GuildMemberClass::Member) {
            return true;
        }

        return false;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] HasPermission exception: " << e.what() << std::endl;
        return false;
    }
}

} // namespace NexusProtection::Guild
