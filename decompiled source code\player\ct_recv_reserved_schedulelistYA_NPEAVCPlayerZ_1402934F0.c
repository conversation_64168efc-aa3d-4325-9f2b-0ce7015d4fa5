/*
 * Function: ?ct_recv_reserved_schedulelist@@YA_NPEAVCPlayer@@@Z
 * Address: 0x1402934F0
 */

char __fastcall ct_recv_reserved_schedulelist(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-68h]@1
  int v6; // [sp+40h] [bp-28h]@8
  int v7; // [sp+44h] [bp-24h]@8
  unsigned int v8; // [sp+48h] [bp-20h]@8
  unsigned int uiMapID; // [sp+4Ch] [bp-1Ch]@10
  int n; // [sp+50h] [bp-18h]@10
  CPlayer *v11; // [sp+70h] [bp+8h]@1

  v11 = pOne;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11 )
  {
    if ( s_nWordCount == 2 )
    {
      v6 = atoi(s_pwszDstCheat[0]);
      v7 = atoi(s_pwszDstCheat[1]);
      v8 = -1;
      if ( v11->m_Param.m_pGuild )
        v8 = v11->m_Param.m_pGuild->m_dwSerial;
      uiMapID = CPlayerDB::GetRaceCode(&v11->m_Param);
      n = v11->m_ObjID.m_wIndex;
      v4 = CGuildBattleController::Instance();
      CGuildBattleController::SendReservedScheduleList(v4, n, uiMapID, 0, v6, v7, v8);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
