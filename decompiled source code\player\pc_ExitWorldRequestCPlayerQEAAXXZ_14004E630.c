/*
 * Function: ?pc_ExitWorldRequest@CPlayer@@QEAAXXZ
 * Address: 0x14004E630
 */

void __fastcall CPlayer::pc_ExitWorldRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  CPlayer *p_pDst; // [sp+28h] [bp-20h]@9
  CPlayer *lp_pOne; // [sp+50h] [bp+8h]@1

  lp_pOne = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( lp_pOne->m_bOper )
  {
    if ( lp_pOne->m_bLoad && lp_pOne->m_bOper )
      TimeLimitMgr::Pop_Data(qword_1799CA2D0, lp_pOne->m_pUserDB->m_dwAccountSerial, lp_pOne->m_id.wIndex);
    lp_pOne->m_bOper = 0;
    if ( lp_pOne->m_pmTrd.bDTradeMode )
    {
      p_pDst = 0i64;
      if ( DTradeEqualPerson(lp_pOne, &p_pDst) )
      {
        _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
        CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
      }
      _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    }
    CPlayer::SendMsg_ExitWorldResult(lp_pOne, 0);
  }
}
