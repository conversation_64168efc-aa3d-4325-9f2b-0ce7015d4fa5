/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Insert_n_::_1_::catch$1
 * Address: 0x140362460
 */

void __fastcall __noreturn std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 400),
    (CUnmannedTraderRegistItemInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 408) + 16i64) + 104i64 * *(_QWORD *)(a2 + 416)),
    (CUnmannedTraderRegistItemInfo *)(*(_QWORD *)(*(_QWORD *)(a2 + 400) + 24i64) + 104i64 * *(_QWORD *)(a2 + 416)));
  CxxThrowException_0(0i64, 0i64);
}
