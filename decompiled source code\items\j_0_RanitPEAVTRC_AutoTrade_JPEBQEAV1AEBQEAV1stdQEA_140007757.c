/*
 * Function: j_??0?$_<PERSON>t@PEAVTRC_AutoTrade@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x140007757
 */

void __fastcall std::_<PERSON><PERSON><TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::_<PERSON>t<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(std::_<PERSON><PERSON><TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *this, std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *__that)
{
  std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::_<PERSON><PERSON><TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(
    this,
    __that);
}
