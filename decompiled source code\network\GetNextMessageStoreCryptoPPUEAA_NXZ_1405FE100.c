/*
 * Function: ?GetNextMessage@Store@CryptoPP@@UEAA_NXZ
 * Address: 0x1405FE100
 */

char __fastcall CryptoPP::Store::GetNextMessage(CryptoPP::Store *this)
{
  char result; // al@3
  CryptoPP::Store *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  if ( this->m_messageEnd || (unsigned __int8)((int (*)(void))this->vfptr[8].__vecDelDtor)() )
  {
    result = 0;
  }
  else
  {
    v2->m_messageEnd = 1;
    result = 1;
  }
  return result;
}
