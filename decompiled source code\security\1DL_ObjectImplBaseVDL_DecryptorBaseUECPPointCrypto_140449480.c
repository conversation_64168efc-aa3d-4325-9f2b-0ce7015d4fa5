/*
 * Function: ??1?$DL_ObjectImplBase@V?$DL_DecryptorBase@UECPPoint@CryptoPP@@@CryptoPP@@U?$DL_CryptoSchemeOptions@U?$ECIES@VECP@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@$0A@@CryptoPP@@U?$DL_Keys_EC@VECP@CryptoPP@@@2@V?$DL_KeyAgreementAlgorithm_DH@UECPPoint@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@2@V?$DL_KeyDerivationAlgorithm_P1363@UECPPoint@CryptoPP@@$0A@V?$P1363_KDF2@VSHA1@CryptoPP@@@2@@2@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@2@@2@V?$DL_PrivateKey_EC@VECP@CryptoPP@@@2@@CryptoPP@@UEAA@XZ
 * Address: 0x140449480
 */

void __fastcall CryptoPP::DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>>::~DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1>>,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>>,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>>(CryptoPP::DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0> >,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1> >,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::DL_ObjectImplBase<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::DL_CryptoSchemeOptions<CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0>,CryptoPP::DL_Keys_EC<CryptoPP::ECP>,CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::ECPPoint,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0> >,CryptoPP::DL_KeyDerivationAlgorithm_P1363<CryptoPP::ECPPoint,0,CryptoPP::P1363_KDF2<CryptoPP::SHA1> >,CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> >,CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CryptoPP::DL_PrivateKey_EC<CryptoPP::ECP>::`vbase destructor(&v5->m_key);
  CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>::~AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>,0>>((CryptoPP::AlgorithmImpl<CryptoPP::DL_DecryptorBase<CryptoPP::ECPPoint>,CryptoPP::ECIES<CryptoPP::ECP,CryptoPP::EnumToType<enum CryptoPP::CofactorMultiplicationOption,0>,0> > *)&v5->vfptr);
}
