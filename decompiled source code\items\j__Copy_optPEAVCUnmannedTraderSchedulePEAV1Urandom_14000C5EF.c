/*
 * Function: j_??$_Copy_opt@PEAVCUnmannedTraderSchedule@@PEAV1@Urandom_access_iterator_tag@std@@@std@@YAPEAVCUnmannedTraderSchedule@@PEAV1@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C5EF
 */

CUnmannedTraderSchedule *__fastcall std::_Copy_opt<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::random_access_iterator_tag>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Copy_opt<CUnmannedTraderSchedule *,CUnmannedTraderSchedule *,std::random_access_iterator_tag>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
