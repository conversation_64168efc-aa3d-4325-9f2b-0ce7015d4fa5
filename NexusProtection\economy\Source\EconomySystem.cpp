#include "../Headers/EconomySystem.h"
#include <algorithm>
#include <fstream>
#include <cstring>

// External dependencies (these would be defined elsewhere in the project)
extern "C" {
    unsigned int timeGetTime();
    int GetPrivateProfileIntA(const char* section, const char* key, int defaultValue, const char* filename);
    void MyMessageBox(const char* title, const char* message);
}

namespace NexusProtection::Economy {

    // Global instance
    EconomySystem g_EconomySystem;

    EconomySystem::EconomySystem() {
        Initialize();
    }

    void EconomySystem::Initialize() {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        m_isLoaded = false;
        InitializeTradeMoneySystem();
        
        auto now = std::chrono::steady_clock::now();
        m_lastUpdateTime = now;
        m_systemStartTime = now;
        
        m_defaultOreValue = 0;
    }

    void EconomySystem::InitializeTradeMoneySystem() {
        m_currentTradeData.Initialize();
        m_bufferTradeData.Initialize();
        m_historyData.Initialize();
    }

    void EconomySystem::AddDalant(RaceType race, int32_t amount) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        if (race >= RaceType::Count) {
            return;
        }

        size_t raceIndex = static_cast<size_t>(race);
        m_bufferTradeData.tradeDalant[raceIndex] += static_cast<double>(amount);
        
        // Ensure non-negative values
        if (m_bufferTradeData.tradeDalant[raceIndex] < 0.0) {
            m_bufferTradeData.tradeDalant[raceIndex] = 0.0;
        }
    }

    double EconomySystem::GetDalant(RaceType race) const {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        if (race >= RaceType::Count) {
            return 0.0;
        }

        size_t raceIndex = static_cast<size_t>(race);
        return m_historyData.currentDalant[raceIndex];
    }

    double EconomySystem::GetOldDalant(RaceType race) const {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        if (race >= RaceType::Count) {
            return 0.0;
        }

        size_t raceIndex = static_cast<size_t>(race);
        return m_historyData.oldDalant[raceIndex];
    }

    void EconomySystem::UpdateEconomySystem(bool* changeDay) {
        if (!changeDay) {
            return;
        }

        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        auto currentTime = std::chrono::steady_clock::now();
        auto timeSinceLastUpdate = std::chrono::duration_cast<std::chrono::milliseconds>(currentTime - m_lastUpdateTime);
        
        // Update trade rates periodically
        UpdateTradeRates();
        
        // Process daily update if needed
        if (*changeDay) {
            ProcessDailyUpdate();
            *changeDay = false; // Reset the flag
        }
        
        m_lastUpdateTime = currentTime;
    }

    void EconomySystem::UpdateNewEconomy(const EconomyCalculationData& data) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        
        // Update current trade data with new calculations
        m_currentTradeData = data;
        
        // Update history data
        for (size_t race = 0; race < static_cast<size_t>(RaceType::Count); ++race) {
            m_historyData.oldDalant[race] = m_historyData.currentDalant[race];
            m_historyData.currentDalant[race] = data.tradeDalant[race];
            m_historyData.bufferTradeDalant[race] = m_bufferTradeData.tradeDalant[race];
        }
    }

    bool EconomySystem::ReadEconomyIniFile() {
        // Read default ore value from configuration file
        m_defaultOreValue = GetPrivateProfileIntA("Economy", "Default_OreVal", 0, ".\\Initialize\\WorldSystem.ini");
        
        if (m_defaultOreValue == 0) {
            MyMessageBox("Economy Error", "Nothing Default MgrValue");
            return false;
        }
        
        m_isLoaded = true;
        return true;
    }

    void EconomySystem::UpdateTradeRates() {
        // Update trade rates based on current economic conditions
        // This is a simplified implementation - the actual logic would be more complex
        
        for (size_t race = 0; race < static_cast<size_t>(RaceType::Count); ++race) {
            // Apply buffer changes to current trade data
            m_currentTradeData.tradeDalant[race] = std::max(1.0, m_bufferTradeData.tradeDalant[race]);
            m_currentTradeData.tradeGold[race] = std::max(1.0, m_bufferTradeData.tradeGold[race]);
            
            // Update ore mining and cutting rates
            for (size_t oreType = 0; oreType < static_cast<size_t>(RaceType::Count); ++oreType) {
                m_currentTradeData.oreMineCount[race][oreType] = std::max(1.0, m_bufferTradeData.oreMineCount[race][oreType]);
                m_currentTradeData.oreCutCount[race][oreType] = std::max(1.0, m_bufferTradeData.oreCutCount[race][oreType]);
            }
        }
    }

    void EconomySystem::ProcessDailyUpdate() {
        // Process daily economic updates
        // This could include resetting certain values, calculating daily statistics, etc.
        
        // Reset buffer data for new day
        m_bufferTradeData.Initialize();
        
        // Update history
        for (size_t race = 0; race < static_cast<size_t>(RaceType::Count); ++race) {
            m_historyData.oldDalant[race] = m_historyData.currentDalant[race];
        }
    }

    RaceType EconomySystem::ConvertLegacyRace(int32_t raceCode) const noexcept {
        switch (raceCode) {
            case 0: return RaceType::Bellato;
            case 1: return RaceType::Cora;
            case 2: return RaceType::Accretia;
            default: return RaceType::Bellato; // Default fallback
        }
    }

    // Legacy C interface implementations
    void EconomySystem::AddDalant(int32_t raceCode, int32_t amount) {
        AddDalant(ConvertLegacyRace(raceCode), amount);
    }

    double EconomySystem::GetDalant(int32_t raceCode) const {
        return GetDalant(ConvertLegacyRace(raceCode));
    }

    double EconomySystem::GetOldDalant(int32_t raceCode) const {
        return GetOldDalant(ConvertLegacyRace(raceCode));
    }

} // namespace NexusProtection::Economy

// Legacy global compatibility
NexusProtection::Economy::_ECONOMY_SYSTEM e_EconomySystem;

// Legacy C interface implementation
extern "C" {
    void _ECONOMY_SYSTEM_Init(_ECONOMY_SYSTEM* system) {
        if (!system) {
            return;
        }
        
        // Initialize legacy structure
        std::memset(system, 0, sizeof(_ECONOMY_SYSTEM));
        system->m_bLoad = false;
        
        // Initialize trade data to 1.0
        for (int race = 0; race < 3; ++race) {
            system->m_dCurTradeGold[race] = 1.0;
            system->m_dCurTradeDalant[race] = 1.0;
            system->m_dBufTradeGold[race] = 1.0;
            system->m_dBufTradeDalant[race] = 1.0;
            
            for (int oreType = 0; oreType < 3; ++oreType) {
                system->m_dCurOreMineCount[race][oreType] = 1.0;
                system->m_dCurOreCutCount[race][oreType] = 1.0;
                system->m_dBufOreMineCount[race][oreType] = 1.0;
                system->m_dBufOreCutCount[race][oreType] = 1.0;
            }
        }
        
        system->m_dwLastUpdateTime = timeGetTime();
        system->m_dwSystemOperStartTime = timeGetTime();
        
        // Initialize modern system as well
        NexusProtection::Economy::g_EconomySystem.Initialize();
    }

    void _ECONOMY_SYSTEM_CurTradeMoneyInit(_ECONOMY_SYSTEM* system) {
        if (!system) {
            return;
        }
        
        // Initialize current trade money data
        for (int race = 0; race < 3; ++race) {
            system->m_dCurTradeGold[race] = 1.0;
            system->m_dCurTradeDalant[race] = 1.0;
            
            for (int oreType = 0; oreType < 3; ++oreType) {
                system->m_dCurOreMineCount[race][oreType] = 1.0;
                system->m_dCurOreCutCount[race][oreType] = 1.0;
            }
        }
        
        // Initialize modern system as well
        NexusProtection::Economy::g_EconomySystem.InitializeTradeMoneySystem();
    }

    char _ReadEconomyIniFile() {
        return NexusProtection::Economy::g_EconomySystem.ReadEconomyIniFile() ? 1 : 0;
    }

    void _UpdateNewEconomy(_economy_calc_data* data) {
        if (!data) {
            return;
        }
        
        // Convert legacy data to modern format
        NexusProtection::Economy::EconomyCalculationData modernData;
        
        for (int race = 0; race < 3; ++race) {
            modernData.tradeDalant[race] = data->dTradeDalant[race];
            modernData.tradeGold[race] = data->dTradeGold[race];
            
            for (int oreType = 0; oreType < 3; ++oreType) {
                modernData.oreMineCount[race][oreType] = data->dOreMineCount[race][oreType];
                modernData.oreCutCount[race][oreType] = data->dOreCutCount[race][oreType];
            }
        }
        
        NexusProtection::Economy::g_EconomySystem.UpdateNewEconomy(modernData);
    }

    void eUpdateEconomySystem(bool* changeDay) {
        NexusProtection::Economy::g_EconomySystem.UpdateEconomySystem(changeDay);
    }

    void eAddDalant(int nRaceCode, int nAdd) {
        NexusProtection::Economy::g_EconomySystem.AddDalant(nRaceCode, nAdd);
    }

    double eGetDalant(int nRaceCode) {
        return NexusProtection::Economy::g_EconomySystem.GetDalant(nRaceCode);
    }

    double eGetOldDalant(int nRaceCode) {
        return NexusProtection::Economy::g_EconomySystem.GetOldDalant(nRaceCode);
    }

    _economy_history_data* eGetGuideHistory() {
        static _economy_history_data historyData;
        
        const auto& modernHistory = NexusProtection::Economy::g_EconomySystem.GetHistoryData();
        
        for (int race = 0; race < 3; ++race) {
            historyData.dOldDalant[race] = modernHistory.oldDalant[race];
            historyData.dCurDalant[race] = modernHistory.currentDalant[race];
            historyData.dBufTradeDalant[race] = modernHistory.bufferTradeDalant[race];
        }
        
        return &historyData;
    }
}
