/*
 * Function: ?SendMsg_RecvHSKQuest@CPlayer@@QEAAXXZ
 * Address: 0x1400E24F0
 */

void __fastcall CPlayer::SendMsg_RecvHSKQuest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  unsigned int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  char byCurrentHour[2]; // [sp+20h] [bp-58h]@4
  char szMsg; // [sp+38h] [bp-40h]@4
  int v8; // [sp+39h] [bp-3Fh]@4
  unsigned __int16 v9; // [sp+3Dh] [bp-3Bh]@4
  bool v10; // [sp+3Fh] [bp-39h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v12; // [sp+55h] [bp-23h]@4
  char v13; // [sp+64h] [bp-14h]@4
  char v14; // [sp+65h] [bp-13h]@4
  char v15; // [sp+66h] [bp-12h]@4
  char v16; // [sp+67h] [bp-11h]@4
  CPlayer *v17; // [sp+80h] [bp+8h]@1

  v17 = this;
  v1 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  szMsg = v17->m_byHSKQuestCode;
  v8 = v17->m_nHSKPvpPoint;
  v9 = v17->m_wKillPoint;
  v13 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
  v14 = CHolyStoneSystem::GetStartHour(&g_HolySys);
  v15 = CHolyStoneSystem::GetStartDay(&g_HolySys);
  v16 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
  v3 = CHolyStoneSystem::GetStartYear(&g_HolySys);
  v10 = MiningTicket::AuthLastMentalTicket(&v17->m_MinigTicket, v3, v16, v15, v14, v13) != 0;
  pbyType = 25;
  v12 = 10;
  v4 = v17->m_ObjID.m_wIndex;
  strcpy(byCurrentHour, "\b");
  CNetProcess::LoadSendMsg(unk_1414F2088, v4, &pbyType, &szMsg, *(unsigned __int16 *)byCurrentHour);
}
