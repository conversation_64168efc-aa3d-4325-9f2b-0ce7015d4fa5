#pragma once

/**
 * @file CGameObject.h
 * @brief Base Game Object Class for All Game Entities
 * 
 * Provides the fundamental base class for all game objects in the NexusProtection
 * engine. This class serves as the root of the object hierarchy and provides
 * basic functionality for object management, identification, and lifecycle.
 * 
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <array>

namespace NexusProtection::World {

    // Forward declarations
    struct _object_id;
    struct _object_create_setdata;
    class CMapData;

    /**
     * @brief Game object type enumeration
     */
    enum class GameObjectType : uint8_t {
        Unknown = 0,
        Character = 1,
        Monster = 2,
        Player = 3,
        NPC = 4,
        Item = 5,
        Projectile = 6,
        Effect = 7,
        Trigger = 8,
        Portal = 9,
        Building = 10,
        Vehicle = 11,
        Trap = 12,
        Zone = 13
    };

    /**
     * @brief Game object state enumeration
     */
    enum class GameObjectState : uint8_t {
        Uninitialized = 0,
        Initializing = 1,
        Active = 2,
        Inactive = 3,
        Destroying = 4,
        Destroyed = 5
    };

    /**
     * @brief Virtual function table structure
     */
    struct CGameObjectVtbl {
        void* functions[64]; // Virtual function pointers
        
        CGameObjectVtbl();
        ~CGameObjectVtbl() = default;
    };

    /**
     * @brief Object identification structure
     */
    struct _object_id {
        uint32_t dwSerial{0};        ///< Unique object serial number
        uint16_t wIndex{0};          ///< Object index
        uint8_t byType{0};           ///< Object type
        uint8_t byFlags{0};          ///< Object flags
        
        bool IsValid() const;
        std::string ToString() const;
        void Reset();
    };

    /**
     * @brief Object creation setup data
     */
    struct _object_create_setdata {
        _object_id* pObjectId{nullptr};      ///< Object identification
        GameObjectType objectType{GameObjectType::Unknown};  ///< Type of object to create
        float position[3]{0.0f, 0.0f, 0.0f}; ///< Initial position
        float rotation[3]{0.0f, 0.0f, 0.0f}; ///< Initial rotation
        uint32_t flags{0};                   ///< Creation flags
        void* pCustomData{nullptr};          ///< Custom creation data
        
        bool IsValid() const;
    };

    /**
     * @brief Base Game Object Class
     * 
     * This class provides the fundamental functionality for all game objects
     * in the world. It handles object identification, lifecycle management,
     * basic properties, and serves as the root of the object hierarchy.
     */
    class CGameObject {
    public:
        // Constants
        static constexpr uint32_t INVALID_SERIAL = 0;
        static constexpr uint16_t INVALID_INDEX = 0;
        static constexpr int MAX_SCREEN_POS = 2;
        static constexpr int MAX_POSITION_COORDS = 3;

        // Constructor and Destructor
        CGameObject();
        virtual ~CGameObject();

        // Core lifecycle methods
        virtual bool Create(const _object_create_setdata* pData);
        virtual bool Init(_object_id* pID);
        virtual bool Destroy();
        virtual void Update(float deltaTime);

        // Object identification
        virtual uint32_t GetSerial() const { return m_dwObjSerial; }
        virtual void SetSerial(uint32_t serial) { m_dwObjSerial = serial; }
        virtual uint16_t GetIndex() const { return m_wIndex; }
        virtual void SetIndex(uint16_t index) { m_wIndex = index; }
        virtual GameObjectType GetObjectType() const { return m_objectType; }
        virtual void SetObjectType(GameObjectType type) { m_objectType = type; }

        // State management
        virtual GameObjectState GetState() const { return m_state; }
        virtual void SetState(GameObjectState state) { m_state = state; }
        virtual bool IsActive() const { return m_state == GameObjectState::Active; }
        virtual bool IsDestroyed() const { return m_state == GameObjectState::Destroyed; }

        // Position and map management
        virtual void SetPosition(float x, float y, float z);
        virtual void SetPosition(const float* pos);
        virtual void GetPosition(float* pos) const;
        virtual void SetOldPosition(const float* pos);
        virtual void GetOldPosition(float* pos) const;
        virtual CMapData* GetCurrentMap() const { return m_pCurMap; }
        virtual void SetCurrentMap(CMapData* pMap) { m_pCurMap = pMap; }
        virtual uint16_t GetMapLayerIndex() const { return m_wMapLayerIndex; }
        virtual void SetMapLayerIndex(uint16_t index) { m_wMapLayerIndex = index; }

        // Screen position management
        virtual void SetScreenPosition(int x, int y);
        virtual void GetScreenPosition(int* pos) const;

        // Timing and lifecycle
        virtual uint32_t GetCreationTime() const { return m_dwCreationTime; }
        virtual uint32_t GetLastUpdateTime() const { return m_dwLastUpdateTime; }
        virtual void UpdateTimestamp();

        // Transparency and visibility
        virtual bool IsTransparent() const { return m_bBreakTranspar; }
        virtual void SetTransparent(bool transparent) { m_bBreakTranspar = transparent; }
        virtual uint32_t GetTransparencyBreakTime() const { return m_dwOldTickBreakTranspar; }
        virtual void SetTransparencyBreakTime(uint32_t time) { m_dwOldTickBreakTranspar = time; }

        // Observer and visibility
        virtual bool IsObserver() const { return m_bObserver; }
        virtual void SetObserver(bool observer) { m_bObserver = observer; }

        // Stun management
        virtual uint32_t GetNextFreeStunTime() const { return m_dwNextFreeStunTime; }
        virtual void SetNextFreeStunTime(uint32_t time) { m_dwNextFreeStunTime = time; }

        // Current second tracking
        virtual uint32_t GetCurrentSecond() const { return m_dwCurSec; }
        virtual void SetCurrentSecond(uint32_t sec) { m_dwCurSec = sec; }

        // Circle reporting (for area effects and notifications)
        virtual void CircleReport(void* type, void* msg, int size, int flags);

        // Utility methods
        virtual bool ValidateState() const;
        virtual void LogError(const std::string& message, const std::string& function = "") const;
        virtual std::string GetDebugInfo() const;

        // Static object management
        static int GetTotalObjectCount() { return s_nTotalObjectNum; }
        static uint32_t GetNextSerial();

    protected:
        // Virtual function table
        CGameObjectVtbl* vfptr{nullptr};                    ///< Virtual function table pointer

        // Object identification
        uint32_t m_dwObjSerial{INVALID_SERIAL};             ///< Object serial number
        uint16_t m_wIndex{INVALID_INDEX};                   ///< Object index
        GameObjectType m_objectType{GameObjectType::Unknown}; ///< Object type
        GameObjectState m_state{GameObjectState::Uninitialized}; ///< Object state

        // Position data
        std::array<float, MAX_POSITION_COORDS> m_fCurPos{0.0f, 0.0f, 0.0f};  ///< Current position
        std::array<float, MAX_POSITION_COORDS> m_fOldPos{0.0f, 0.0f, 0.0f};  ///< Previous position
        std::array<int, MAX_SCREEN_POS> m_nScreenPos{0, 0}; ///< Screen position

        // Map and layer information
        CMapData* m_pCurMap{nullptr};                       ///< Current map
        uint16_t m_wMapLayerIndex{0};                       ///< Map layer index

        // Timing information
        uint32_t m_dwCreationTime{0};                       ///< Object creation time
        uint32_t m_dwLastUpdateTime{0};                     ///< Last update time
        uint32_t m_dwNextFreeStunTime{static_cast<uint32_t>(-1)}; ///< Next free stun time
        uint32_t m_dwOldTickBreakTranspar{static_cast<uint32_t>(-1)}; ///< Transparency break time
        uint32_t m_dwCurSec{0};                             ///< Current second

        // State flags
        bool m_bBreakTranspar{false};                       ///< Transparency break flag
        bool m_bObserver{false};                            ///< Observer flag

        // Player circle list (for area effects)
        void* m_bPlayerCircleList{nullptr};                 ///< Player circle list

        // Thread safety
        mutable std::mutex m_objectMutex;                   ///< Mutex for thread safety

        // Internal methods
        virtual void InitializeDefaults();
        virtual bool ValidateCreateData(const _object_create_setdata* pData) const;
        virtual void UpdateInternalState(float deltaTime);

    private:
        // Static counters
        static int s_nTotalObjectNum;                       ///< Total object count
        static uint32_t s_dwNextSerial;                     ///< Next available serial
        static std::mutex s_staticMutex;                    ///< Mutex for static data

        // Internal utility methods
        uint32_t GenerateSerial();
        uint32_t GetCurrentTime() const;
        void IncrementObjectCount();
        void DecrementObjectCount();
    };

    /**
     * @brief Utility functions for game object management
     */
    namespace GameObjectUtils {
        std::string GameObjectTypeToString(GameObjectType type);
        std::string GameObjectStateToString(GameObjectState state);
        bool IsValidSerial(uint32_t serial);
        bool IsValidPosition(const float* pos);
        float CalculateDistance(const float* pos1, const float* pos2);
    }

} // namespace NexusProtection::World

// Legacy C interface for compatibility
extern "C" {
    struct CGameObject_Legacy {
        void* vfptr;
        uint32_t m_dwObjSerial;
        // Additional legacy fields would be defined based on actual structure
    };

    // Legacy function declarations
    void CGameObject_Constructor(CGameObject_Legacy* object);
    void CGameObject_Destructor(CGameObject_Legacy* object);
    bool CGameObject_Create(CGameObject_Legacy* object, _object_create_setdata* pData);
    void CGameObject_Init(CGameObject_Legacy* object, _object_id* pID);
    bool CGameObject_Destroy(CGameObject_Legacy* object);
    void CGameObject_CircleReport(CGameObject_Legacy* object, void* type, void* msg, int size, int flags);
}
