/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption__::_1_::dtor$1
 * Address: 0x140453440
 */

void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption>>::~AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption>>(*(CryptoPP::AlgorithmImpl<CryptoPP::CBC_Encryption,CryptoPP::CipherModeFinalTemplate_CipherHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>,CryptoPP::CBC_Encryption> > **)(a2 + 64));
}
