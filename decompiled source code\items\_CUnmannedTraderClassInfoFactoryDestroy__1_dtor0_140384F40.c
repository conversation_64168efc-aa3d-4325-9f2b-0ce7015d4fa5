/*
 * Function: _CUnmannedTraderClassInfoFactory::Destroy_::_1_::dtor$0
 * Address: 0x140384F40
 */

void __fastcall CUnmannedTraderClassInfoFactory::Destroy_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>((std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)(a2 + 40));
}
