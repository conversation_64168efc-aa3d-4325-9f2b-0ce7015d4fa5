/*
 * Function: j_?construct@?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@AEBV34@@Z
 * Address: 0x140011EA0
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::construct(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, GUILD_BATTLE::CGuildBattleRewardItem *_Ptr, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::construct(this, _Ptr, _Val);
}
