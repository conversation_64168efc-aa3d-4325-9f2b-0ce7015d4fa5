/*
 * Function: ?CharacterRenameCash@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CBBE0
 */

char __fastcall CNetworkEX::CharacterRenameCash(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-78h]@1
  char *v8; // [sp+20h] [bp-58h]@4
  CPlayer *v9; // [sp+28h] [bp-50h]@4
  char _Dest[17]; // [sp+38h] [bp-40h]@10
  unsigned __int64 v11; // [sp+60h] [bp-18h]@4
  CNetworkEX *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  v8 = pBuf;
  v9 = &g_Player + n;
  if ( !v9->m_bOper || v9->m_pmTrd.bDTradeMode || v9->m_bCorpse )
  {
    result = 1;
  }
  else if ( v8[1] )
  {
    v6 = CPlayerDB::GetCharNameA(&v9->m_Param);
    CLogFile::Write(
      &v12->m_LogFile,
      "odd.. %s: CharacterRenameCash()..  if(pRecv->Item.byStorageCode != _STORAGE_POS::INVEN)",
      v6);
    result = 0;
  }
  else
  {
    _Dest[0] = 0;
    memset(&_Dest[1], 0, 0x10ui64);
    strncpy_s<17>((char (*)[17])_Dest, v8 + 5, 0x10ui64);
    CPlayer::pc_CharacterRenameCash(v9, *v8, (_STORAGE_POS_INDIV *)(v8 + 1), _Dest);
    result = 1;
  }
  return result;
}
