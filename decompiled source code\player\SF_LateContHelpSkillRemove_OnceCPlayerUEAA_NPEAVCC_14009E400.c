/*
 * Function: ?SF_LateContHelpSkillRemove_Once@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x14009E400
 */

char __fastcall CPlayer::SF_LateContHelpSkillRemove_Once(CPlayer *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  float v5; // xmm0_4@4
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-68h]@1
  int v8; // [sp+30h] [bp-38h]@6
  bool *v9; // [sp+38h] [bp-30h]@6
  int j; // [sp+40h] [bp-28h]@6
  bool *v11; // [sp+48h] [bp-20h]@9
  float v12; // [sp+50h] [bp-18h]@4
  CCharacter *v13; // [sp+78h] [bp+10h]@1

  v13 = pDstObj;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = rand();
  v5 = (float)(v4 % 100);
  v12 = (float)(v4 % 100);
  _effect_parameter::GetEff_Plus(&v13->m_EP, 38);
  if ( v5 <= v12 )
  {
    v8 = -1;
    v9 = 0i64;
    for ( j = 0; j < 8; ++j )
    {
      v11 = &v13->m_SFCont[1][j].m_bExist;
      if ( *v11 && !v11[1] )
      {
        if ( v8 == -1 )
        {
          v8 = j;
          v9 = v11;
        }
        else if ( *((_DWORD *)v11 + 2) > *((_DWORD *)v9 + 2) )
        {
          v8 = j;
          v9 = v11;
        }
      }
    }
    if ( v8 == -1 )
    {
      result = 0;
    }
    else
    {
      CCharacter::RemoveSFContEffect(v13, 1, v8, 0, 0);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
