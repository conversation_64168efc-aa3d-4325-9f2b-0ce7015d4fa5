/*
 * Function: ?IsConvertableToLong@Integer@CryptoPP@@QEBA_NXZ
 * Address: 0x1405E4760
 */

bool __fastcall CryptoPP::Integer::IsConvertableToLong(CryptoPP::Integer *this)
{
  bool result; // al@2
  int *v2; // rax@3
  int v3; // ST20_4@3
  __int64 v4; // rax@3
  int v5; // [sp+20h] [bp-18h]@3
  bool v6; // [sp+28h] [bp-10h]@6
  CryptoPP::Integer *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  if ( CryptoPP::Integer::ByteCount(this) <= 4ui64 )
  {
    LODWORD(v2) = CryptoPP::SecBlock<unsigned __int64,CryptoPP::AllocatorWithCleanup<unsigned __int64,0>>::operator unsigned __int64 const *(&v7->reg);
    v3 = *v2;
    LODWORD(v4) = CryptoPP::SecBlock<unsigned __int64,CryptoPP::AllocatorWithCleanup<unsigned __int64,0>>::operator unsigned __int64 const *(&v7->reg);
    v5 = CryptoPP::SafeLeftShift<64,unsigned long>(*(_DWORD *)(v4 + 8)) + v3;
    if ( v7->sign )
    {
      v6 = -v5 < 0;
      result = v6;
    }
    else
    {
      result = v5 >= 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
