/*
 * Function: ?UpdatePvpOrderView@CPlayer@@QEAAX_J@Z
 * Address: 0x140068390
 */

void __usercall CPlayer::UpdatePvpOrderView(CPlayer *this@<rcx>, __int64 tCurTime@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  long double v5; // xmm0_8@4
  __int64 v6; // [sp+0h] [bp-78h]@1
  long double v7; // [sp+40h] [bp-38h]@4
  long double v8; // [sp+48h] [bp-30h]@4
  char *v9; // [sp+50h] [bp-28h]@4
  char *v10; // [sp+58h] [bp-20h]@4
  long double v11; // [sp+60h] [bp-18h]@4
  CPlayer *v12; // [sp+80h] [bp+8h]@1
  __int64 tUpdateDate; // [sp+88h] [bp+10h]@1

  tUpdateDate = tCurTime;
  v12 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  CPvpOrderView::GetPvpTempCash(&v12->m_kPvpOrderView);
  v7 = a3;
  CPvpOrderView::GetPvpCash(&v12->m_kPvpOrderView);
  v5 = a3 + v7;
  v8 = v5;
  v9 = v12->m_szLvHistoryFileName;
  CPvpOrderView::GetPvpCash(&v12->m_kPvpOrderView);
  CMgrAvatorLvHistory::adjust_pvpcash(&CPlayer::s_MgrLvHistory, 0, v5, v7, v9);
  CPlayer::AlterPvPCashBag(v12, v7, 0);
  CPvpOrderView::Update_RaceWarRecvr(&v12->m_kPvpOrderView, 0);
  CPlayerDB::GetPvPPoint(&v12->m_Param);
  CPvpOrderView::Update(&v12->m_kPvpOrderView, tUpdateDate, 0, 0, 0.0, v5, 0.0);
  CPvpOrderView::ResetPvPOrderView(&v12->m_kPvpOrderView);
  CPvpOrderView::Notify_PvpTempCash(&v12->m_kPvpOrderView, v12->m_ObjID.m_wIndex);
  CPvpOrderView::Notify_OrderView(&v12->m_kPvpOrderView, v12->m_ObjID.m_wIndex);
  CPlayer::SendMsg_AlterPvPCash(v12, 0);
  v10 = v12->m_szLvHistoryFileName;
  CPvpOrderView::GetPvpTempCash(&v12->m_kPvpOrderView);
  v11 = 0.0;
  CPvpOrderView::GetPvpCash(&v12->m_kPvpOrderView);
  CMgrAvatorLvHistory::adjust_pvpcash(&CPlayer::s_MgrLvHistory, 1, 0.0, v11, v10);
}
