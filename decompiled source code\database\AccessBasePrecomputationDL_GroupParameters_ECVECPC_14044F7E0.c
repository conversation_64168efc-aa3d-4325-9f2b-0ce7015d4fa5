/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x14044F7E0
 */

CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::AccessBasePrecomputation(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *this)
{
  return &this->m_gpc;
}
