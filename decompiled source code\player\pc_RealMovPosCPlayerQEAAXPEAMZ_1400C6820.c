/*
 * Function: ?pc_RealMovPos@CPlayer@@QEAAXPEAM@Z
 * Address: 0x1400C6820
 */

void __fastcall CPlayer::pc_RealMovPos(CPlayer *this, float *pfCur)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@21
  float v5; // xmm0_4@22
  bool v6; // al@29
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@4
  CPlayer *pkUser; // [sp+40h] [bp+8h]@1
  float *Buf1; // [sp+48h] [bp+10h]@1

  Buf1 = pfCur;
  pkUser = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  if ( pkUser->m_bMove )
  {
    if ( CRealMoveRequestDelayChecker::Check(&pkUser->m_kMoveDelayChecker, pkUser) )
    {
      if ( pkUser->m_pmTrd.bDTradeMode )
      {
        v8 = 7;
      }
      else if ( pkUser->m_bCorpse )
      {
        v8 = 8;
      }
      else if ( CPlayer::IsSiegeMode(pkUser) )
      {
        v8 = 12;
      }
      else if ( !memcmp_0(Buf1, pkUser->m_fCurPos, 0xCui64) )
      {
        v8 = 6;
      }
      else if ( pkUser->m_byStandType == 1 )
      {
        v8 = 10;
      }
      else if ( _effect_parameter::GetEff_State(&pkUser->m_EP, 20) )
      {
        v8 = 13;
      }
      else if ( pkUser->m_byUserDgr
             || (v4 = pkUser->m_fCurPos[0] - *Buf1, abs(v4), v4 <= 200.0)
             && (v5 = pkUser->m_fCurPos[2] - Buf1[2], abs(v5), v5 <= 200.0) )
      {
        if ( !CMapData::IsMapIn(pkUser->m_pCurMap, Buf1) )
          v8 = 4;
      }
      else
      {
        v8 = 11;
      }
    }
    else
    {
      v8 = 6;
    }
  }
  else
  {
    v8 = 6;
  }
  if ( v8 )
  {
    CPlayer::SendMsg_MoveError(pkUser, v8);
    if ( pkUser->m_bMove )
    {
      v6 = CPlayer::IsOutExtraStopPos(pkUser, pkUser->m_fCurPos);
      CPlayer::SendMsg_Stop(pkUser, v6);
      CCharacter::Stop((CCharacter *)&pkUser->vfptr);
    }
  }
  else
  {
    memcpy_0(pkUser->m_fOldPos, pkUser->m_fCurPos, 0xCui64);
    memcpy_0(pkUser->m_fCurPos, Buf1, 0xCui64);
    ++pkUser->m_nCheckMovePacket;
    pkUser->m_dwLastSetPointTime = GetLoopTime();
  }
}
