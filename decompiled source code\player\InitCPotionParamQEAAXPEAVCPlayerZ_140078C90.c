/*
 * Function: ?Init@CPotionParam@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x140078C90
 */

void __fastcall CPotionParam::Init(CPotionParam *this, CPlayer *pMaster)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CPotionParam *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->m_pMaster = pMaster;
  for ( j = 0; j < 2; ++j )
    _ContPotionData::Init((_ContPotionData *)v6 + j);
  _ContPotionData::Init(&v6->m_StoneOfMovePotionData);
}
