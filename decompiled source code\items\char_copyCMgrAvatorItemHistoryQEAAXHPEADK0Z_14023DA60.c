/*
 * Function: ?char_copy@CMgrAvatorItemHistory@@QEAAXHPEADK0@Z
 * Address: 0x14023DA60
 */

void __fastcall CMgrAvatorItemHistory::char_copy(CMgrAvatorItemHistory *this, int n, char *pszDstName, unsigned int dwDstSerial, char *pszFileName)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *v8; // [sp+20h] [bp-18h]@4
  char *v9; // [sp+28h] [bp-10h]@4
  CMgrAvatorItemHistory *v10; // [sp+40h] [bp+8h]@1

  v10 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = v10->m_szCurTime;
  v8 = v10->m_szCurDate;
  sprintf(sData, "CHAR COPY: dst(%s:%d) [%s %s]\r\n", pszDstName);
  CMgrAvatorItemHistory::WriteFile(v10, pszFileName, sData);
}
