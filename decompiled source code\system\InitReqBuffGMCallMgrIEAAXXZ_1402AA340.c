/*
 * Function: ?InitReqBuff@GMCallMgr@@IEAAXXZ
 * Address: 0x1402AA340
 */

void __fastcall GMCallMgr::InitReqBuff(GMCallMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned int dwIndex; // [sp+20h] [bp-18h]@4
  GMCallMgr *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNetIndexList::SetList(&v5->m_listGMRequestDataEmpty, 0x9E4u);
  CNetIndexList::SetList(&v5->m_listGMRequestDataTask, 0x9E4u);
  for ( dwIndex = 0; dwIndex < 0x9E4; ++dwIndex )
    CNetIndexList::PushNode_Back(&v5->m_listGMRequestDataEmpty, dwIndex);
}
