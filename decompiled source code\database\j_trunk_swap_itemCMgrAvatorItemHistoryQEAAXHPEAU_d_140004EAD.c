/*
 * Function: j_?trunk_swap_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@0KKPEAD@Z
 * Address: 0x140004EAD
 */

void __fastcall CMgrAvatorItemHistory::trunk_swap_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pInputItem, _STORAGE_LIST::_db_con *pOutputItem, unsigned int dwFeeDalant, unsigned int dwNewDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::trunk_swap_item(this, n, pInputItem, pOutputItem, dwFeeDalant, dwNewDalant, pszFileName);
}
