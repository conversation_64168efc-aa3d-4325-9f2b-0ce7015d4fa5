/*
 * Function: ?Update_ReturnPost@CUserDB@@QEAAXK@Z
 * Address: 0x140117A80
 */

void __fastcall CUserDB::Update_ReturnPost(CUserDB *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool *v4; // [sp+0h] [bp-18h]@1
  CUserDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = &v5->m_AvatorData.dbPostData.dbRetPost.m_bUpdate;
  v5->m_AvatorData.dbPostData.dbRetPost.m_RetSerials[v5->m_AvatorData.dbPostData.dbRetPost.m_nCum] = dwSerial;
  ++*(_DWORD *)(v4 + 5);
  *v4 = 1;
  v5->m_bDataUpdate = 1;
}
