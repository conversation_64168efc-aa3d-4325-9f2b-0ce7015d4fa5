/*
 * Function: ?Set_CashEvent_Status@CashItemRemoteStore@@QEAAXEE@Z
 * Address: 0x1402FAB90
 */

void __fastcall CashItemRemoteStore::Set_CashEvent_Status(CashItemRemoteStore *this, char byEventType, char byStatus)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // ecx@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  CLogFile *v7; // [sp+20h] [bp-18h]@4
  CashItemRemoteStore *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char v10; // [sp+50h] [bp+18h]@1

  v10 = byStatus;
  v9 = byEventType;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = v8->m_cash_event[(unsigned __int8)byEventType].m_event_status;
  v7 = &v8->m_cash_event[(unsigned __int8)byEventType].m_event_log;
  CLogFile::Write(v7, "[EventStateChange : %d -> %d ]", v5, (unsigned __int8)byStatus);
  if ( v10 == 7 )
    CLogFile::Write(&v8->m_cash_event[(unsigned __int8)v9].m_event_log, "[Event Expired]");
  v8->m_cash_event[(unsigned __int8)v9].m_event_status = v10;
}
