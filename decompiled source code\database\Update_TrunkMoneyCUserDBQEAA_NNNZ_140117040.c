/*
 * Function: ?Update_TrunkMoney@CUserDB@@QEAA_NNN@Z
 * Address: 0x140117040
 */

char __fastcall CUserDB::Update_TrunkMoney(CUserDB *this, long double dGold, long double dDalant)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _TRUNK_DB_BASE *v6; // [sp+0h] [bp-18h]@1
  CUserDB *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = (__int64 *)&v6;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = &v7->m_AvatorData.dbTrunk;
  v7->m_AvatorData.dbTrunk.dGold = dGold;
  v6->dDalant = dDalant;
  v7->m_bDataUpdate = 1;
  return 1;
}
