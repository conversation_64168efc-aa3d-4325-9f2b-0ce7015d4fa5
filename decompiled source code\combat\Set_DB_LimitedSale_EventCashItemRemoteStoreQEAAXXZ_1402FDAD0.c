/*
 * Function: ?Set_DB_LimitedSale_Event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FDAD0
 */

void __fastcall CashItemRemoteStore::Set_DB_LimitedSale_Event(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _INVENKEY *v3; // rax@6
  _INVENKEY *v4; // rax@6
  int v5; // eax@7
  __int64 v6; // [sp+0h] [bp-3B8h]@1
  qry_case_cash_limsale v7; // [sp+40h] [bp-378h]@4
  int j; // [sp+194h] [bp-224h]@4
  _DB_QRY_SYN_DATA *v9; // [sp+198h] [bp-220h]@7
  _INVENKEY v10; // [sp+1A0h] [bp-218h]@6
  _INVENKEY v11; // [sp+1A4h] [bp-214h]@6
  char v12; // [sp+1A8h] [bp-210h]@8
  char v13; // [sp+2A0h] [bp-118h]@4
  CashItemRemoteStore *v14; // [sp+3C0h] [bp+8h]@1

  v14 = this;
  v1 = &v6;
  for ( i = 234i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  qmemcpy(&v13, &v14->m_lim_event, 0xF4ui64);
  qmemcpy(&v14->m_lim_event_New, &v13, sizeof(v14->m_lim_event_New));
  qry_case_cash_limsale::qry_case_cash_limsale(&v7);
  v7.NewSale.byDck = v14->m_lim_event_New.DCK;
  v7.OldSale.byDck = v14->m_lim_event_Old.DCK;
  v7.NewSale.byLimited_sale_num = v14->m_lim_event_New.m_byEventNum;
  v7.OldSale.byLimited_sale_num = v14->m_lim_event_Old.m_byEventNum;
  for ( j = 0; j < v14->m_lim_event_New.m_byEventNum; ++j )
  {
    _INVENKEY::_INVENKEY(
      &v10,
      0,
      v14->m_lim_event_New.m_EventItemInfo[j].byTableCode,
      v14->m_lim_event_New.m_EventItemInfo[j].dwIndex);
    v7.NewSale.List[j].nLimcode = _INVENKEY::CovDBKey(v3);
    v7.NewSale.List[j].nLimcount = v14->m_lim_event_New.m_EventItemInfo[j].wCount;
    _INVENKEY::_INVENKEY(
      &v11,
      0,
      v14->m_lim_event_Old.m_EventItemInfo[j].byTableCode,
      v14->m_lim_event_Old.m_EventItemInfo[j].dwIndex);
    v7.OldSale.List[j].nLimcode = _INVENKEY::CovDBKey(v4);
    v7.OldSale.List[j].nLimcount = v14->m_lim_event_Old.m_EventItemInfo[j].wCount;
  }
  v5 = qry_case_cash_limsale::size(&v7);
  v9 = CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -98, &v7.NewSale.byDck, v5);
  if ( v9 )
  {
    qmemcpy(&v12, &v14->m_lim_event_New, 0xF4ui64);
    qmemcpy(&v14->m_lim_event_Old, &v12, sizeof(v14->m_lim_event_Old));
  }
}
