/*
 * Function: ?DoWork@CCashDbWorkerGB@@MEAAXXZ
 * Address: 0x1403193D0
 */

void __fastcall CCashDbWorkerGB::DoWork(CCashDbWorkerGB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  unsigned int nIdx; // [sp+24h] [bp-34h]@4
  Task *pkTsk; // [sp+38h] [bp-20h]@4
  int v6; // [sp+40h] [bp-18h]@5
  int v7; // [sp+44h] [bp-14h]@5
  CCashDbWorkerGB *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  nIdx = 0;
  pkTsk = TaskPool::PopWaitTsk(v8->_pkPool, &nIdx);
  if ( pkTsk )
  {
    v6 = -1;
    v7 = Task::GetTaskCode(pkTsk);
    if ( v7 )
    {
      switch ( v7 )
      {
        case 1:
          v6 = CCashDbWorkerGB::_wait_tsk_cash_update(v8, pkTsk, nIdx);
          break;
        case 2:
          v6 = ((int (__fastcall *)(CCashDbWorkerGB *, Task *))v8->vfptr[4].DoWork)(v8, pkTsk);
          break;
        case 3:
          v6 = CashDbWorker::_wait_tsk_cash_buy_dblog((CashDbWorker *)&v8->vfptr, pkTsk);
          break;
        case 4:
          CashDbWorker::_wait_tst_cash_total_selling_select((CashDbWorker *)&v8->vfptr, pkTsk);
          break;
      }
    }
    else
    {
      v6 = CCashDbWorkerGB::_wait_tsk_cash_select(v8, pkTsk, nIdx);
    }
  }
}
