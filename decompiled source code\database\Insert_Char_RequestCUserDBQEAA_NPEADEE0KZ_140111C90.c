/*
 * Function: ?Insert_Char_Request@CUserDB@@QEAA_NPEADEE0K@Z
 * Address: 0x140111C90
 */

bool __fastcall CUserDB::Insert_Char_Request(CUserDB *this, char *pwszCharName, char bySlotIndex, char byRaceSexCode, char *pszClassCode, unsigned int dwBaseShape)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CNationSettingManager *v9; // rax@30
  __int64 v10; // [sp+0h] [bp-E8h]@1
  char *pQryData; // [sp+20h] [bp-C8h]@33
  int nSize; // [sp+28h] [bp-C0h]@40
  char v13; // [sp+30h] [bp-B8h]@4
  char *j; // [sp+38h] [bp-B0h]@13
  int k; // [sp+40h] [bp-A8h]@21
  _base_fld *v16; // [sp+48h] [bp-A0h]@34
  _qry_sheet_insert v17; // [sp+60h] [bp-88h]@40
  unsigned __int64 v18; // [sp+D0h] [bp-18h]@4
  CUserDB *v19; // [sp+F0h] [bp+8h]@1
  char *Str1; // [sp+F8h] [bp+10h]@1
  char v21; // [sp+100h] [bp+18h]@1
  char v22; // [sp+108h] [bp+20h]@1

  v22 = byRaceSexCode;
  v21 = bySlotIndex;
  Str1 = pwszCharName;
  v19 = this;
  v6 = &v10;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v18 = (unsigned __int64)&v10 ^ _security_cookie;
  v13 = 0;
  if ( v19->m_bActive )
  {
    if ( v19->m_ss.bReged && !v19->m_ss.bSelect )
    {
      if ( v19->m_bDBWaitState )
      {
        result = 0;
      }
      else if ( v19->m_RegedList[(unsigned __int8)bySlotIndex].m_bySlotIndex == 255 )
      {
        for ( j = pwszCharName; ; ++j )
        {
          if ( *j == 32 || *j == 39 )
          {
            CUserDB::Insert_Char_Complete(v19, 47, 0i64);
            return 1;
          }
          if ( !*j )
            break;
        }
        if ( *pwszCharName == 42 )
        {
          CUserDB::Insert_Char_Complete(v19, 47, 0i64);
          result = 1;
        }
        else
        {
          for ( k = 0; k < 3; ++k )
          {
            if ( !strcmp_0(Str1, *(const char **)&wszNonMakeName_0[8 * k]) )
            {
              CUserDB::Insert_Char_Complete(v19, 47, 0i64);
              return 1;
            }
          }
          if ( v19->m_byUserDgr != 2 && strlen_0(Str1) >= nGMCmpLen && !strncmp(Str1, wszGMCmp, nGMCmpLen) )
          {
            CUserDB::Insert_Char_Complete(v19, 47, 0i64);
            result = 1;
          }
          else
          {
            v9 = CTSingleton<CNationSettingManager>::Instance();
            if ( CNationSettingManager::IsNormalString(v9, Str1) )
            {
              if ( IsSQLValidString(Str1) )
              {
                v16 = CRecordData::GetRecord(&stru_1799C6420, pszClassCode);
                if ( v16 )
                {
                  if ( *(_DWORD *)&v16[1].m_strCode[8] )
                  {
                    result = 0;
                  }
                  else if ( v16[1].m_dwIndex == (signed int)(unsigned __int8)v22 >> 1 )
                  {
                    CMgrAccountLobbyHistory::add_char_request(
                      &CUserDB::s_MgrLobbyHistory,
                      v19->m_szLobbyHistoryFileName);
                    _qry_sheet_insert::_qry_sheet_insert(&v17);
                    v17.dwAccountSerial = v19->m_dwAccountSerial;
                    strcpy_0(v17.szAccountID, v19->m_szAccountID);
                    strcpy_0(v17.InsertData.m_wszAvatorName, Str1);
                    v17.InsertData.m_byRaceSexCode = v22;
                    v17.InsertData.m_bySlotIndex = v21;
                    v17.InsertData.m_dwBaseShape = dwBaseShape;
                    strcpy_0(v17.InsertData.m_szClassCode, v16->m_strCode);
                    nSize = _qry_sheet_insert::size(&v17);
                    pQryData = (char *)&v17;
                    if ( CMainThread::PushDQSData(
                           &g_Main,
                           v19->m_dwAccountSerial,
                           &v19->m_idWorld,
                           1,
                           (char *)&v17,
                           nSize) )
                    {
                      v19->m_bDBWaitState = 1;
                      result = 1;
                    }
                    else
                    {
                      result = 0;
                    }
                  }
                  else
                  {
                    result = 0;
                  }
                }
                else
                {
                  result = 0;
                }
              }
              else
              {
                pQryData = Str1;
                CLogFile::Write(
                  &stru_1799C8E78,
                  "CUserDB::Insert_Char_Request() : Account : %s(%u) ::IsSQLValidString(pwszCharName(%s)) Invalid!",
                  v19->m_szAccountID,
                  v19->m_dwAccountSerial);
                CUserDB::Insert_Char_Complete(v19, 47, 0i64);
                result = 1;
              }
            }
            else
            {
              CUserDB::Insert_Char_Complete(v19, 47, 0i64);
              result = 1;
            }
          }
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
