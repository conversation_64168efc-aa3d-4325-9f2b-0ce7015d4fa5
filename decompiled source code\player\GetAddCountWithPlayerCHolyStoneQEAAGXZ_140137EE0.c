/*
 * Function: ?GetAddCountWithPlayer@CHolyStone@@QEAAGXZ
 * Address: 0x140137EE0
 */

unsigned __int16 __fastcall CHolyStone::GetAddCountWithPlayer(CHolyStone *this)
{
  __int16 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@12
  unsigned __int16 v5; // [sp+0h] [bp-18h]@1
  int j; // [sp+4h] [bp-14h]@4
  CPlayer *v7; // [sp+8h] [bp-10h]@7

  v1 = (__int16 *)&v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 2;
  }
  v5 = 0;
  for ( j = 0; j < 2532; ++j )
  {
    v7 = &g_Player + j;
    if ( v7->m_bLive )
    {
      if ( v7->m_byHSKQuestCode != 100 )
        ++v5;
    }
  }
  if ( (signed int)v5 >= 3 )
    v3 = v5 / 3;
  else
    LOWORD(v3) = v5;
  return v3;
}
