/*
 * Function: ?Loop@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DC0D0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Loop(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@7
  unsigned int j; // [sp+24h] [bp-14h]@7
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v7->m_bDone && v7->m_ppkReservedSchedule )
  {
    v7->m_bDone = 1;
    v5 = 0;
    for ( j = 0; j < v7->m_uiMapCnt; ++j )
    {
      if ( !GUILD_BATTLE::CGuildBattleReservedSchedule::IsDone(v7->m_ppkReservedSchedule[j]) )
      {
        if ( GUILD_BATTLE::CGuildBattleReservedSchedule::Loop(v7->m_ppkReservedSchedule[j]) )
          v5 = 1;
        v7->m_bDone = 0;
      }
    }
    result = v5;
  }
  else
  {
    result = 0;
  }
  return result;
}
