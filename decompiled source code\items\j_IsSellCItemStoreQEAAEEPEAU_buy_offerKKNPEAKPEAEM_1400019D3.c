/*
 * Function: j_?IsSell@CItemStore@@QEAAEEPEAU_buy_offer@@KKNPEAKPEAEMEE@Z
 * Address: 0x1400019D3
 */

char __fastcall CItemStore::IsSell(CItemStore *this, char byOfferNum, _buy_offer *pOffer, unsigned int dwHasDalant, unsigned int dwHasGold, long double dHasPoint, unsigned int *dwHasActPoint, char *pbyActCode, float fDiscountRate, char byRace, char byGrade)
{
  return CItemStore::IsSell(
           this,
           byOfferNum,
           pOffer,
           dwHasDalant,
           dwHasGold,
           dHasPoint,
           dwHasActPoint,
           pbyActCode,
           fDiscountRate,
           byRace,
           byGrade);
}
