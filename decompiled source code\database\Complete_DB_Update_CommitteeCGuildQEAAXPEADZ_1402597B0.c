/*
 * Function: ?Complete_DB_Update_Committee@CGuild@@QEAAXPEAD@Z
 * Address: 0x1402597B0
 */

void __fastcall CGuild::Complete_DB_Update_Committee(CGuild *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@29
  __int64 v5; // [sp+0h] [bp-1C8h]@1
  char *v6; // [sp+30h] [bp-198h]@4
  _guild_member_info *v7; // [sp+38h] [bp-190h]@4
  int v8; // [sp+40h] [bp-188h]@8
  int j; // [sp+44h] [bp-184h]@8
  int k; // [sp+48h] [bp-180h]@17
  _guild_alter_member_grade_inform_zocl v11; // [sp+60h] [bp-168h]@24
  int v12; // [sp+194h] [bp-34h]@24
  char pbyType; // [sp+1A4h] [bp-24h]@24
  char v14; // [sp+1A5h] [bp-23h]@24
  int l; // [sp+1B4h] [bp-14h]@24
  bool v16; // [sp+1B8h] [bp-10h]@30
  CGuild *v17; // [sp+1D0h] [bp+8h]@1

  v17 = this;
  v2 = &v5;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pData;
  v7 = CGuild::GetMemberFromSerial(v17, *(_DWORD *)pData);
  if ( v7 )
  {
    v7->byClassInGuild = v6[12];
    if ( v7->pPlayer )
    {
      v7->pPlayer->m_Param.m_byClassInGuild = v6[12];
      v7->pPlayer->m_Param.m_bGuildLock = 0;
    }
    if ( v6[12] == 1 )
    {
      v8 = -1;
      for ( j = 0; j < 3; ++j )
      {
        if ( !v17->m_pGuildCommittee[j] )
        {
          v8 = j;
          break;
        }
      }
      if ( v8 != -1 )
        v17->m_pGuildCommittee[v8] = v7;
    }
    else if ( !v6[12] )
    {
      for ( k = 0; k < 3; ++k )
      {
        if ( v17->m_pGuildCommittee[k] && v17->m_pGuildCommittee[k]->dwSerial == *(_DWORD *)v6 )
        {
          v17->m_pGuildCommittee[k] = 0i64;
          break;
        }
      }
    }
    CGuild::MakeDownMemberPacket(v17);
    _guild_alter_member_grade_inform_zocl::_guild_alter_member_grade_inform_zocl(&v11);
    v12 = 0;
    v11.MemberList[0].dwMemberSerial = v7->dwSerial;
    v11.MemberList[0].byRank = v7->byRank;
    v11.MemberList[0].byGrade = v7->byClassInGuild;
    v11.byAlterMemberNum = 1;
    pbyType = 27;
    v14 = 32;
    for ( l = 0; l < 50; ++l )
    {
      if ( _guild_member_info::IsFill(&v17->m_MemberData[l]) )
      {
        if ( v17->m_MemberData[l].pPlayer )
        {
          v4 = _guild_alter_member_grade_inform_zocl::size(&v11);
          CNetProcess::LoadSendMsg(
            unk_1414F2088,
            v17->m_MemberData[l].pPlayer->m_ObjID.m_wIndex,
            &pbyType,
            &v11.byAlterMemberNum,
            v4);
        }
      }
    }
    v16 = v6[12] != 0;
    CGuild::SendMsg_ManageGuildCommitteeResult(v17, v16, v7->wszName);
  }
}
