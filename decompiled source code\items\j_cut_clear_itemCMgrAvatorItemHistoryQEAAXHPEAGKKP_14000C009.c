/*
 * Function: j_?cut_clear_item@CMgrAvatorItemHistory@@QEAAXHPEAGKKPEAD@Z
 * Address: 0x14000C009
 */

void __fastcall CMgrAvatorItemHistory::cut_clear_item(CMgrAvatorItemHistory *this, int n, unsigned __int16 *pwCuttingResBuffer, unsigned int dwAddGold, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::cut_clear_item(this, n, pwCuttingResBuffer, dwAddGold, dwNewGold, pszFileName);
}
