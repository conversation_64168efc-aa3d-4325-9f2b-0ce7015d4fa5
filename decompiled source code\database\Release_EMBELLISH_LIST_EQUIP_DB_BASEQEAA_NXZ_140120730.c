/*
 * Function: ?Release@_EMBELLISH_LIST@_EQUIP_DB_BASE@@QEAA_NXZ
 * Address: 0x140120730
 */

char __fastcall _EQUIP_DB_BASE::_EMBELLISH_LIST::Release(_EQUIP_DB_BASE::_EMBELLISH_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  _EQUIP_DB_BASE::_EMBELLISH_LIST *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( _EMBELLKEY::IsFilled(&v5->Key) )
  {
    _EMBELLKEY::SetRelease(&v5->Key);
    v5->lnUID = 0i64;
    v5->dwT = -1;
    v5->byCsMethod = 0;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
