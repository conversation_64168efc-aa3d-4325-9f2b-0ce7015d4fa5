/*
 * Function: ??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@UECPPoint@CryptoPP@@V?$allocator@UECPPoint@CryptoPP@@@std@@@std@@PEAUECPPoint@CryptoPP@@V?$allocator@UECPPoint@CryptoPP@@@2@@stdext@@YAPEAUECPPoint@CryptoPP@@V?$_Vector_const_iterator@UECPPoint@CryptoPP@@V?$allocator@UECPPoint@CryptoPP@@@std@@@std@@0PEAU12@AEAV?$allocator@UECPPoint@CryptoPP@@@4@@Z
 * Address: 0x1405AC870
 */

__int64 __fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *a1, std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // ST30_8@1
  char v9; // [sp+38h] [bp-70h]@1
  char v10; // [sp+39h] [bp-6Fh]@1
  char v11; // [sp+40h] [bp-68h]@1
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *v12; // [sp+58h] [bp-50h]@1
  char v13; // [sp+60h] [bp-48h]@1
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *v14; // [sp+78h] [bp-30h]@1
  __int64 v15; // [sp+80h] [bp-28h]@1
  __int64 v16; // [sp+88h] [bp-20h]@1
  __int64 v17; // [sp+90h] [bp-18h]@1
  __int64 v18; // [sp+98h] [bp-10h]@1
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *v19; // [sp+B0h] [bp+8h]@1
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *__that; // [sp+B8h] [bp+10h]@1
  __int64 v21; // [sp+C0h] [bp+18h]@1
  __int64 v22; // [sp+C8h] [bp+20h]@1

  v22 = a4;
  v21 = a3;
  __that = a2;
  v19 = a1;
  v15 = -2i64;
  memset(&v9, 0, sizeof(v9));
  v10 = std::_Ptr_cat<std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>,CryptoPP::ECPPoint *>(
          a1,
          &v21);
  v12 = (std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)&v11;
  v14 = (std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)&v13;
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(
    (std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)&v11,
    __that);
  v16 = v4;
  v17 = v4;
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(
    v14,
    v19);
  v18 = v5;
  LODWORD(v6) = std::_Uninit_copy<std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>,CryptoPP::ECPPoint *,std::allocator<CryptoPP::ECPPoint>>(
                  v5,
                  v17,
                  v21,
                  v22);
  v7 = v6;
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::~_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(v19);
  std::_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::~_Vector_const_iterator<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>(__that);
  return v7;
}
