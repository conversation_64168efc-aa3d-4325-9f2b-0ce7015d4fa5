/*
 * Function: ?DB_Update_GuildMaster@CGuild@@QEAA_NPEAU_guild_member_info@@@Z
 * Address: 0x140252BD0
 */

char __fastcall CGuild::DB_Update_GuildMaster(CGuild *this, _guild_member_info *pNewguildMaster)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@10
  __int64 v6; // [sp+0h] [bp-78h]@1
  char v7; // [sp+30h] [bp-48h]@4
  _qry_case_update_guildmaster v8; // [sp+48h] [bp-30h]@4
  CGuild *v9; // [sp+80h] [bp+8h]@1
  _guild_member_info *v10; // [sp+88h] [bp+10h]@1

  v10 = pNewguildMaster;
  v9 = this;
  v2 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = 1;
  v8.tmp_guildindex = v9->m_nIndex;
  v8.in_guildserial = v9->m_dwSerial;
  if ( _guild_master_info::IsFill(&v9->m_MasterData) )
  {
    v8.in_guild_prev_masterSerial = v9->m_MasterData.dwSerial;
    v8.in_guild_prev_masterPrevGrade = v9->m_MasterData.byPrevGrade;
  }
  else
  {
    v8.in_guild_prev_masterSerial = -1;
    v8.in_guild_prev_masterPrevGrade = -1;
  }
  if ( v10 )
  {
    v8.in_guild_new_masterSerial = v10->dwSerial;
    v8.in_guild_new_masterPrevGrade = v10->byClassInGuild;
  }
  else
  {
    v8.in_guild_new_masterSerial = -1;
    v8.in_guild_new_masterPrevGrade = -1;
  }
  v4 = _qry_case_update_guildmaster::size(&v8);
  if ( !CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 49, (char *)&v8, v4) )
    v7 = 0;
  return v7;
}
