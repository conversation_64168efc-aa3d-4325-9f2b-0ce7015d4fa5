/*
 * Function: j_?InitClass@CMgrAvatorItemHistory@@QEAAXHKEPEAD0PEAH10@Z
 * Address: 0x140005A5B
 */

void __fastcall CMgrAvatorItemHistory::InitClass(CMgrAvatorItemHistory *this, int iCostGold, unsigned int dwInitClassCnt, char byLastClassGrade, char *szOldClass, char *szCurClass, int *piOldMaxPoint, int *piAlterMaxPoint, char *pszFileName)
{
  CMgrAvatorItemHistory::InitClass(
    this,
    iCostGold,
    dwInitClassCnt,
    byLastClassGrade,
    szOldClass,
    szCurClass,
    piOldMaxPoint,
    piAlterMaxPoint,
    pszFileName);
}
