/*
 * Function: j_?PreCheckPotion@CPotionMgr@@QEAAHPEAVCPlayer@@AEAPEAVCCharacter@@PEBU_PotionItem_fld@@KPEAU_skill_fld@@_N@Z
 * Address: 0x1400014A1
 */

int __fastcall CPotionMgr::PreCheckPotion(CPotionMgr *this, CPlayer *pUsePlayer, CCharacter **pTargetCharacter, _PotionItem_fld *pfB, unsigned int nCurTime, _skill_fld *pFld, bool bCheckDist)
{
  return CPotionMgr::PreCheckPotion(this, pUsePlayer, pTargetCharacter, pfB, nCurTime, pFld, bCheckDist);
}
