/*
 * Function: j_?InsertSFContEffect@CMonster@@UEAAEEEKGEPEA_NPEAVCCharacter@@@Z
 * Address: 0x140011C43
 */

char __fastcall CMonster::InsertSFContEffect(CMonster *this, char byContCode, char byEffectC<PERSON>, unsigned int dwEffectIndex, unsigned __int16 wDurSec, char byLv, bool *pbUpMty, CCharacter *pActChar)
{
  return CMonster::InsertSFContEffect(this, byContCode, byEffectCode, dwEffectIndex, wDurSec, byLv, pbUpMty, pActChar);
}
