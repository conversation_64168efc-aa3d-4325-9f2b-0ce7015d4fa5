/*
 * Function: ?IsSFUsableSFMastery@CPlayer@@QEAA_NEH@Z
 * Address: 0x1400A0FE0
 */

char __fastcall CPlayer::IsSFUsableSFMastery(CPlayer *this, char byMasteryCode, int nMasteryIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@24
  __int64 v7; // [sp+0h] [bp-48h]@1
  int *v8; // [sp+20h] [bp-28h]@15
  unsigned __int8 v9; // [sp+28h] [bp-20h]@22
  int v10; // [sp+2Ch] [bp-1Ch]@22
  int j; // [sp+30h] [bp-18h]@22
  CPlayer *v12; // [sp+50h] [bp+8h]@1
  char v13; // [sp+58h] [bp+10h]@1

  v13 = byMasteryCode;
  v12 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nMasteryIndex < 0 )
    return 0;
  if ( byMasteryCode == 4 )
  {
    if ( nMasteryIndex >= 24 )
      return 0;
  }
  else
  {
    if ( byMasteryCode != 3 )
      return 0;
    if ( nMasteryIndex >= 8 )
      return 0;
  }
  v8 = 0i64;
  if ( v12->m_Param.m_pClassHistory[0] )
  {
    if ( v12->m_Param.m_pClassHistory[0]->m_nClass != 3 || v12->m_Param.m_pClassData->m_nClass != 3 )
      v8 = nMPL;
    else
      v8 = nSpMPL;
  }
  else
  {
    v8 = nMPL;
  }
  v9 = (char)nMasteryIndex % -4;
  v10 = nMasteryIndex - (unsigned __int8)((char)nMasteryIndex % -4);
  for ( j = 0; j < v9; ++j )
  {
    v6 = _MASTERY_PARAM::GetMasteryPerMast(&v12->m_pmMst, v13, j + v10);
    if ( v6 < v8[j] )
      return 0;
  }
  return 1;
}
