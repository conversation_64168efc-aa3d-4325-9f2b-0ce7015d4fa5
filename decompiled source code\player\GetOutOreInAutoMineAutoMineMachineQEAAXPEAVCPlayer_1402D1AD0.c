/*
 * Function: ?GetOutOreInAutoMine@AutoMineMachine@@QEAAXPEAVCPlayer@@PEAD@Z
 * Address: 0x1402D1AD0
 */

void __fastcall AutoMineMachine::GetOutOreInAutoMine(AutoMineMachine *this, CPlayer *pUser, char *pMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@6
  int v6; // ecx@6
  int v7; // edx@6
  int v8; // eax@7
  unsigned int v9; // ecx@7
  unsigned int v10; // edx@7
  int v11; // eax@9
  int v12; // ecx@9
  int v13; // edx@9
  int v14; // eax@10
  int v15; // ecx@10
  int v16; // edx@10
  int v17; // er8@10
  __int64 v18; // r9@10
  unsigned int v19; // er10@10
  unsigned __int16 v20; // ax@12
  int v21; // eax@12
  int v22; // ecx@12
  char v23; // al@13
  unsigned int v24; // eax@14
  unsigned int v25; // ecx@14
  int v26; // ecx@15
  char *v27; // rax@15
  int v28; // ecx@15
  int v29; // edx@15
  __int64 v30; // [sp+0h] [bp-148h]@1
  int nNum; // [sp+20h] [bp-128h]@7
  int v32; // [sp+28h] [bp-120h]@7
  char *v33; // [sp+30h] [bp-118h]@10
  int v34; // [sp+38h] [bp-110h]@10
  int v35; // [sp+40h] [bp-108h]@10
  char *v36; // [sp+50h] [bp-F8h]@4
  _INVENKEY *pItem; // [sp+58h] [bp-F0h]@6
  int v38; // [sp+60h] [bp-E8h]@6
  _STORAGE_LIST::_storage_con pCon; // [sp+78h] [bp-D0h]@8
  _STORAGE_LIST::_db_con *v40; // [sp+B8h] [bp-90h]@8
  _pt_automine_getoutore_zocl v41; // [sp+C4h] [bp-84h]@12
  char pbyType; // [sp+E4h] [bp-64h]@12
  char v43; // [sp+E5h] [bp-63h]@12
  TInvenSlot<_INVENKEY> *v44; // [sp+F8h] [bp-50h]@12
  TInventory<_INVENKEY> *v45; // [sp+100h] [bp-48h]@6
  CLogFile *v46; // [sp+108h] [bp-40h]@7
  TInventory<_INVENKEY> *v47; // [sp+110h] [bp-38h]@9
  TInventory<_INVENKEY> *v48; // [sp+118h] [bp-30h]@12
  CLogFile *v49; // [sp+120h] [bp-28h]@14
  int v50; // [sp+128h] [bp-20h]@15
  int nTableCode; // [sp+12Ch] [bp-1Ch]@15
  CGuild *v52; // [sp+130h] [bp-18h]@15
  AutoMineMachine *v53; // [sp+150h] [bp+8h]@1
  CPlayer *v54; // [sp+158h] [bp+10h]@1

  v54 = pUser;
  v53 = this;
  v3 = &v30;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v36 = pMsg;
  if ( v53->m_pOwnerGuild )
  {
    pItem = (_INVENKEY *)(v36 + 1);
    v5 = (unsigned __int8)v36[10];
    v6 = (unsigned __int8)v36[1];
    v7 = (unsigned __int8)*v36;
    v45 = &v53->m_Inven;
    v38 = TInventory<_INVENKEY>::pop(&v53->m_Inven, v7, v6, (_INVENKEY *)(v36 + 1), v5);
    if ( v38 == -1 )
    {
      AutoMineMachine::SendMsg_ResultCode(v53, v54->m_id.wIndex, 21, 12);
      v8 = (unsigned __int8)v36[10];
      v9 = pItem->bySlotIndex;
      v10 = (unsigned __int8)*v36;
      v46 = &v53->m_sysLog;
      v32 = v8;
      nNum = (int)*pItem;
      CLogFile::Write(&v53->m_sysLog, "[ERR-GetOutOreInAutoMine]:automine_invalid_values(%d,%d,%d,%d)", v10, v9);
    }
    else
    {
      _STORAGE_LIST::_storage_con::_storage_con(&pCon);
      pCon.m_byClientIndex = pItem->bySlotIndex;
      pCon.m_byTableCode = pItem->byTableCode;
      pCon.m_wItemIndex = pItem->wItemIndex;
      pCon.m_dwDur += (unsigned __int8)v36[10];
      pCon.m_wSerial = CPlayerDB::GetNewItemSerial(&v54->m_Param);
      v40 = CPlayer::Emb_AddStorage(v54, 0, &pCon, 0, 1);
      if ( v40 )
      {
        _pt_automine_getoutore_zocl::_pt_automine_getoutore_zocl(&v41);
        v41.wItemSerial = pCon.m_wSerial;
        pbyType = 14;
        v43 = 22;
        v20 = _pt_automine_getoutore_zocl::size(&v41);
        CNetProcess::LoadSendMsg(unk_1414F2088, v54->m_id.wIndex, &pbyType, &v41.byRetCode, v20);
        v21 = pItem->bySlotIndex;
        v22 = (unsigned __int8)*v36;
        v48 = &v53->m_Inven;
        v44 = TInventory<_INVENKEY>::get_slot(&v53->m_Inven, v22, v21);
        if ( v44 )
        {
          v23 = TInvenSlot<_INVENKEY>::get_overlapnum(v44);
          AutoMineMachine::push_dqs_getore(v53, (unsigned __int8)*v36, pItem->bySlotIndex, v23);
        }
        else
        {
          v24 = pItem->bySlotIndex;
          v25 = (unsigned __int8)*v36;
          v49 = &v53->m_sysLog;
          CLogFile::Write(&v53->m_sysLog, "ERR - get_slot(%d, %d) is NULL", v25, v24);
        }
        v50 = (unsigned __int8)v36[10];
        v26 = pItem->wItemIndex;
        nTableCode = pItem->byTableCode;
        v27 = GetItemKorName(nTableCode, v26);
        v28 = pItem->bySlotIndex;
        v29 = (unsigned __int8)*v36;
        v52 = v53->m_pOwnerGuild;
        v34 = v50;
        v33 = v27;
        v32 = v28;
        nNum = v29;
        CLogFile::Write(
          &v53->m_Log,
          "[POP ORE] GuildSerial%d MasterSerial:%d [Page:%d_Slot:%d_Ore:%s], Num:%d",
          v52->m_dwSerial,
          v52->m_MasterData.dwSerial);
      }
      else
      {
        v11 = (unsigned __int8)v36[10];
        v12 = pItem->bySlotIndex;
        v13 = (unsigned __int8)*v36;
        v47 = &v53->m_Inven;
        if ( TInventory<_INVENKEY>::push(&v53->m_Inven, v13, v12, pItem, v11) )
        {
          v14 = (unsigned __int8)v36[10];
          v15 = pItem->wItemIndex;
          v16 = pItem->byTableCode;
          v17 = pItem->bySlotIndex;
          v18 = pItem->bySlotIndex;
          v19 = (unsigned __int8)*v36;
          v35 = 466;
          v34 = v14;
          LODWORD(v33) = v15;
          v32 = v16;
          nNum = v17;
          CLogFile::Write(
            &v53->m_sysLog,
            "[ERR-GetOutOreInAutoMine]::m_Inven.push(%d, %d, [%d/%d/%d], %d)_LINE:%d",
            v19,
            v18);
        }
        AutoMineMachine::SendMsg_ResultCode(v53, v54->m_id.wIndex, 21, 12);
      }
    }
  }
  else
  {
    AutoMineMachine::SendMsg_ResultCode(v53, pUser->m_id.wIndex, 21, 5);
    CLogFile::Write(&v53->m_sysLog, "[ERR-GetOutOreInAutoMine]:automine_isnot_owner_guild");
  }
}
