/*
 * Function: _CUnmannedTraderScheduler::CompleteClear_::_1_::dtor$1
 * Address: 0x140393CE0
 */

void __fastcall CUnmannedTraderScheduler::CompleteClear_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>((std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)(a2 + 72));
}
