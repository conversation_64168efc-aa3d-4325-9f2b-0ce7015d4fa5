/*
 * Function: ?_TowerAllReturn@CPlayer@@QEAAXE_N@Z
 * Address: 0x1400A1BD0
 */

void __fastcall CPlayer::_TowerAllReturn(CPlayer *this, char byDestroyType, bool bForceReturn)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-58h]@1
  bool bUpdate; // [sp+20h] [bp-38h]@9
  bool bSend; // [sp+28h] [bp-30h]@9
  int j; // [sp+30h] [bp-28h]@4
  CGuardTower *v10; // [sp+38h] [bp-20h]@8
  int nAlter; // [sp+40h] [bp-18h]@9
  int k; // [sp+44h] [bp-14h]@11
  char *v13; // [sp+48h] [bp-10h]@13
  CPlayer *v14; // [sp+60h] [bp+8h]@1
  char v15; // [sp+68h] [bp+10h]@1
  bool v16; // [sp+70h] [bp+18h]@1

  v16 = bForceReturn;
  v15 = byDestroyType;
  v14 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 6; ++j )
  {
    if ( v14->m_pmTwr.m_List[j].m_pTowerItem )
    {
      _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&v14->m_pmTwr.m_List[j].m_pTowerItem->m_bLoad, 0);
      v10 = v14->m_pmTwr.m_List[j].m_pTowerObj;
      if ( v10->m_bLive )
      {
        nAlter = v14->m_pmTwr.m_List[j].m_pTowerObj->m_nHP - LODWORD(v14->m_pmTwr.m_List[j].m_pTowerItem->m_dwDur);
        v5 = v14->m_pmTwr.m_List[j].m_pTowerItem;
        bSend = 0;
        bUpdate = 0;
        CPlayer::Emb_AlterDurPoint(v14, 0, v5->m_byStorageIndex, nAlter, 0, 0);
        if ( !v16 && v14->m_bOper )
        {
          for ( k = 0; k < 2532; ++k )
          {
            v13 = (char *)&CGuardTower::s_Temp + 40 * k;
            if ( *(_DWORD *)v13 == -1 )
            {
              *(_DWORD *)v13 = v10->m_pMasterTwr->m_dwObjSerial;
              v13[4] = v10->m_pItem->m_byStorageIndex;
              *((_QWORD *)v13 + 1) = v10->m_pCurMap;
              memcpy_0(v13 + 16, v10->m_fCurPos, 0xCui64);
              v13[32] = v10->m_bComplete;
              *((_DWORD *)v13 + 7) = GetLoopTime();
              break;
            }
          }
        }
        CGuardTower::Destroy(v10, v15, 0);
      }
      _TOWER_PARAM::_list::init(&v14->m_pmTwr.m_List[j]);
      --v14->m_pmTwr.m_nCount;
    }
  }
}
