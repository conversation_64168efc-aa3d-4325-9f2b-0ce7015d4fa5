/*
 * Function: ?dev_max_level_ext@CPlayer@@QEAA_NE@Z
 * Address: 0x1400BE670
 */

char __fastcall CPlayer::dev_max_level_ext(CPlayer *this, char byMaxLevel)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1

  v9 = byMaxLevel;
  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CPlayerDB::SetMaxLevel(&v8->m_Param, (unsigned __int8)byMaxLevel);
  if ( v8->m_pUserDB )
    CUserDB::Update_MaxLevel(v8->m_pUserDB, v9);
  v7 = (unsigned __int8)v9;
  v4 = CPlayerDB::GetLevel(&v8->m_Param);
  if ( v7 < v4 )
  {
    CPlayerDB::SetExp(&v8->m_Param, 0.0);
    v8->m_dwExpRate = 0;
    if ( v9 )
      CPlayer::SetLevelD(v8, v9);
    else
      CPlayer::SetLevelD(v8, 50);
  }
  return 1;
}
