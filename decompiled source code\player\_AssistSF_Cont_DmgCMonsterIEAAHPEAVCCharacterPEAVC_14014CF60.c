/*
 * Function: ?_AssistSF_Cont_Dmg@CMonster@@IEAAHPEAVCCharacter@@PEAVCMonsterSkill@@@Z
 * Address: 0x14014CF60
 */

__int64 __fastcall CMonster::_AssistSF_Cont_Dmg(CMonster *this, CCharacter *pDst, CMonsterSkill *pskill)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@15
  unsigned int v7; // eax@16
  unsigned int v8; // eax@25
  unsigned int v9; // eax@26
  __int64 v10; // [sp+0h] [bp-108h]@1
  unsigned int v11; // [sp+40h] [bp-C8h]@4
  char v12; // [sp+44h] [bp-C4h]@8
  char v13; // [sp+45h] [bp-C3h]@8
  _force_fld *v14; // [sp+48h] [bp-C0h]@13
  _force_fld *pForceFld; // [sp+50h] [bp-B8h]@14
  char v16; // [sp+64h] [bp-A4h]@14
  bool v17; // [sp+84h] [bp-84h]@14
  int nForceLv; // [sp+94h] [bp-74h]@14
  _skill_fld *v19; // [sp+98h] [bp-70h]@20
  _skill_fld *pSkillFld; // [sp+A0h] [bp-68h]@21
  char v21; // [sp+B4h] [bp-54h]@21
  bool v22; // [sp+D4h] [bp-34h]@21
  int nSkillLv; // [sp+E4h] [bp-24h]@21
  int nEffectCode; // [sp+E8h] [bp-20h]@24
  int v25; // [sp+ECh] [bp-1Ch]@8
  int v26; // [sp+F0h] [bp-18h]@22
  CMonster *v27; // [sp+110h] [bp+8h]@1
  CCharacter *pDstChar; // [sp+118h] [bp+10h]@1
  CMonsterSkill *v29; // [sp+120h] [bp+18h]@1

  v29 = pskill;
  pDstChar = pDst;
  v27 = this;
  v3 = &v10;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = 0;
  if ( !pskill )
    return v11;
  if ( !pDst )
    return v11;
  v12 = 0;
  v13 = 0;
  v25 = CMonsterSkill::GetType(pskill);
  if ( v25 > 0 )
  {
    if ( v25 <= 2 )
    {
      if ( _effect_parameter::GetEff_State(&v27->m_EP, 4) )
        return 0i64;
      v19 = (_skill_fld *)CMonsterSkill::GetFld(v29);
      if ( v19 )
      {
        pSkillFld = v19;
        v21 = 0;
        v22 = 0;
        nSkillLv = CMonsterSkill::GetSFLv(v29);
        if ( CMonsterSkill::GetType(v29) == 1 )
          v26 = 0;
        else
          v26 = 2;
        nEffectCode = v26;
        if ( CCharacter::AssistSkill((CCharacter *)&v27->vfptr, pDstChar, v26, pSkillFld, nSkillLv, &v21, &v22) )
        {
          CMonster::CheckEventEmotionPresentation(v27, 6, 0i64);
          CMonster::SendMsg_Assist_Skill(v27, v21, nEffectCode, pDstChar, pSkillFld, nSkillLv);
          v8 = GetLoopTime();
          CMonsterSkill::Use(v29, v8, 1);
          v11 = 1;
        }
        else
        {
          v9 = GetLoopTime();
          CMonsterSkill::Use(v29, v9, 0);
          v11 = 0;
        }
      }
    }
    else if ( v25 == 3 )
    {
      if ( _effect_parameter::GetEff_State(&v27->m_EP, 4) )
        return 0i64;
      v14 = (_force_fld *)CMonsterSkill::GetFld(v29);
      if ( v14 )
      {
        pForceFld = v14;
        v16 = 0;
        v17 = 0;
        nForceLv = CMonsterSkill::GetSFLv(v29);
        if ( CCharacter::AssistForce((CCharacter *)&v27->vfptr, pDstChar, pForceFld, nForceLv, &v16, &v17) )
        {
          CMonster::CheckEventEmotionPresentation(v27, 6, 0i64);
          CMonster::SendMsg_Assist_Force(v27, v16, pDstChar, pForceFld, nForceLv);
          v6 = GetLoopTime();
          CMonsterSkill::Use(v29, v6, 1);
          v11 = 1;
        }
        else
        {
          v7 = GetLoopTime();
          CMonsterSkill::Use(v29, v7, 0);
          v11 = 0;
        }
      }
    }
  }
  if ( v27->m_bMove )
  {
    if ( v11 )
      CCharacter::Stop((CCharacter *)&v27->vfptr);
  }
  return v11;
}
