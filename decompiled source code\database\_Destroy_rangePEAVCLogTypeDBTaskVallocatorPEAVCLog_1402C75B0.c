/*
 * Function: ??$_Destroy_range@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@YAXPEAPEAVCLogTypeDBTask@@0AEAV?$allocator@PEAVCLogTypeDBTask@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x1402C75B0
 */

void __fastcall std::_Destroy_range<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(CLogTypeDBTask **_First, CLogTypeDBTask **_Last, std::allocator<CLogTypeDBTask *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  ;
}
