/*
 * Function: ?update_amine_moveore@CRFWorldDatabase@@QEAA_NEEKEKEEKE@Z
 * Address: 0x1404A9570
 */

bool __fastcall CRFWorldDatabase::update_amine_moveore(CRFWorldDatabase *this, char byType, char byRace, unsigned int dwSerial, char bySSlot, unsigned int dwSK, char bySNum, char byDSlot, unsigned int dwDK, char byDNum)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v13; // [sp+0h] [bp-4B8h]@1
  int v14; // [sp+20h] [bp-498h]@4
  int v15; // [sp+28h] [bp-490h]@4
  int v16; // [sp+30h] [bp-488h]@4
  unsigned int v17; // [sp+38h] [bp-480h]@4
  int v18; // [sp+40h] [bp-478h]@4
  int v19; // [sp+48h] [bp-470h]@4
  int v20; // [sp+50h] [bp-468h]@4
  int v21; // [sp+58h] [bp-460h]@4
  unsigned int v22; // [sp+60h] [bp-458h]@4
  char Dest; // [sp+80h] [bp-438h]@4
  char v24; // [sp+81h] [bp-437h]@4
  unsigned __int64 v25; // [sp+490h] [bp-28h]@4
  CRFWorldDatabase *v26; // [sp+4C0h] [bp+8h]@1

  v26 = this;
  v10 = &v13;
  for ( i = 298i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  v25 = (unsigned __int64)&v13 ^ _security_cookie;
  Dest = 0;
  memset(&v24, 0, 0x3FFui64);
  v22 = dwSerial;
  v21 = (unsigned __int8)byRace;
  v20 = (unsigned __int8)byType;
  v19 = (unsigned __int8)byDNum;
  v18 = (unsigned __int8)byDSlot;
  v17 = dwDK;
  v16 = (unsigned __int8)byDSlot;
  v15 = (unsigned __int8)bySNum;
  v14 = (unsigned __int8)bySSlot;
  sprintf(
    &Dest,
    "update [dbo].[tbl_automine_inven] set k_%d=%d, o_%d=%d, k_%d=%d, o_%d=%d where dck = 0 and collisiontype = %d and ra"
    "ce = %d and guildserial = %d",
    (unsigned __int8)bySSlot,
    dwSK);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v26->vfptr, &Dest, 1);
}
