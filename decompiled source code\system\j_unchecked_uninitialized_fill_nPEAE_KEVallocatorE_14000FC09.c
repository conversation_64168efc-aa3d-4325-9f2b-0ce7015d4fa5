/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAE_KEV?$allocator@E@std@@@stdext@@YAXPEAE_KAEBEAEAV?$allocator@E@std@@@Z
 * Address: 0x14000FC09
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<unsigned char *,unsigned __int64,unsigned char,std::allocator<unsigned char>>(char *_First, unsigned __int64 _Count, const char *_Val, std::allocator<unsigned char> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<unsigned char *,unsigned __int64,unsigned char,std::allocator<unsigned char>>(
    _First,
    _Count,
    _Val,
    _Al);
}
