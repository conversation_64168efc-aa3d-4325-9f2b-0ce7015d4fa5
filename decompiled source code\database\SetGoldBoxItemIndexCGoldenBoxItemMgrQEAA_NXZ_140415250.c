/*
 * Function: ?SetGoldBoxItemIndex@CGoldenBoxItemMgr@@QEAA_NXZ
 * Address: 0x140415250
 */

char __fastcall CGoldenBoxItemMgr::SetGoldBoxItemIndex(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@4
  int v4; // eax@8
  char v5; // al@12
  int v6; // eax@12
  char result; // al@13
  int v8; // eax@16
  __int64 v9; // rax@20
  __int64 v10; // [sp+0h] [bp-88h]@1
  unsigned __int16 v11; // [sp+20h] [bp-68h]@4
  __int16 v12; // [sp+24h] [bp-64h]@4
  int n; // [sp+28h] [bp-60h]@7
  _base_fld *v14; // [sp+30h] [bp-58h]@9
  int v15; // [sp+38h] [bp-50h]@14
  int v16; // [sp+3Ch] [bp-4Ch]@14
  _base_fld *v17; // [sp+40h] [bp-48h]@17
  int __n[2]; // [sp+48h] [bp-40h]@4
  void *v19; // [sp+50h] [bp-38h]@7
  void *__t; // [sp+58h] [bp-30h]@4
  __int64 v21; // [sp+60h] [bp-28h]@4
  void *v22; // [sp+68h] [bp-20h]@5
  int v23; // [sp+70h] [bp-18h]@12
  int v24; // [sp+74h] [bp-14h]@12
  CGoldenBoxItemMgr *v25; // [sp+90h] [bp+8h]@1

  v25 = this;
  v1 = &v10;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v21 = -2i64;
  v11 = 0;
  v3 = CGoldenBoxItemMgr::GetLoopCount(v25);
  v12 = (unsigned __int8)v3;
  *(_QWORD *)__n = (unsigned __int8)v3;
  __t = operator new[](saturated_mul(0x42ui64, (unsigned __int8)v3));
  if ( __t )
  {
    `vector constructor iterator'(__t, 0x42ui64, __n[0], (void *(__cdecl *)(void *))_goldbox_index::_goldbox_index);
    v22 = __t;
  }
  else
  {
    v22 = 0i64;
  }
  v19 = v22;
  v25->m_pItemIndex = (_goldbox_index *)v22;
  for ( n = 0; ; ++n )
  {
    v4 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 17);
    if ( n >= v4 )
      break;
    v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, n);
    if ( !*(_DWORD *)&v14[3].m_strCode[4] )
      ++v11;
  }
  v23 = v11;
  v5 = CGoldenBoxItemMgr::GetLoopCount(v25);
  v24 = (unsigned __int8)v5 + v23;
  v6 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 17);
  if ( v24 == v6 )
  {
    v15 = 0;
    v16 = 0;
    while ( 1 )
    {
      v8 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 17);
      if ( v15 >= v8 )
        break;
      v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, v15);
      if ( v17 )
      {
        if ( *(_DWORD *)&v17[3].m_strCode[4] )
        {
          strcpy_0(v25->m_pItemIndex[v16].szItemCode, v17->m_strCode);
          v25->m_pItemIndex[v16++].wItemIndex = v17->m_dwIndex;
        }
        v9 = (unsigned __int8)CGoldenBoxItemMgr::GetLoopCount(v25);
        if ( v16 > (unsigned __int8)v9 )
          return 0;
      }
      ++v15;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
