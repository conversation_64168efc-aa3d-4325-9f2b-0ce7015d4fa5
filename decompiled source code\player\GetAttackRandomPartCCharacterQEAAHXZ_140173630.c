/*
 * Function: ?GetAttackRandomPart@CCharacter@@QEAAHXZ
 * Address: 0x140173630
 */

signed __int64 __fastcall CCharacter::GetAttackRandomPart(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v4; // [sp+0h] [bp-58h]@1
  int v5; // [sp+28h] [bp-30h]@7
  int v6; // [sp+2Ch] [bp-2Ch]@7
  int v7; // [sp+30h] [bp-28h]@7
  int v8; // [sp+34h] [bp-24h]@7
  int v9; // [sp+38h] [bp-20h]@7
  int v10; // [sp+44h] [bp-14h]@7
  unsigned int j; // [sp+48h] [bp-10h]@7
  CCharacter *v12; // [sp+60h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->m_ObjID.m_byID || v12[25].m_SFCont[0][5].m_wszPlayerName[16] == 255 )
  {
    v5 = 23;
    v6 = 45;
    v7 = 63;
    v8 = 80;
    v9 = 100;
    v10 = rand() % 100;
    for ( j = 0; (signed int)j < 5; ++j )
    {
      if ( v10 < *(&v5 + (signed int)j) )
        return j;
    }
    result = 0xFFFFFFFFi64;
  }
  else
  {
    result = v12[25].m_SFCont[0][5].m_wszPlayerName[16];
  }
  return result;
}
