/*
 * Function: SQLInstallODBCW
 * Address: 0x1404DB000
 */

int __fastcall SQLInstallODBCW(HWND__ *hwndParent, const unsigned __int16 *lpszInfFile, const unsigned __int16 *lpszSrcPath, const unsigned __int16 *lpszDrivers)
{
  HWND__ *v4; // rbp@1
  const unsigned __int16 *v5; // rbx@1
  const unsigned __int16 *v6; // rdi@1
  const unsigned __int16 *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = hwndParent;
  v5 = lpszDrivers;
  v6 = lpszSrcPath;
  v7 = lpszInfFile;
  v8 = ODBC___GetSetupProc("SQLInstallODBCW");
  if ( v8 )
    result = ((int (__fastcall *)(HWND__ *, const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
