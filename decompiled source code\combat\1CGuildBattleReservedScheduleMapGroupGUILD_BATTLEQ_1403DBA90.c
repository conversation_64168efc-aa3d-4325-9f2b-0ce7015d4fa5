/*
 * Function: ??1CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403DBA90
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::~CGuildBattleReservedScheduleMapGroup(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  unsigned int j; // [sp+20h] [bp-38h]@7
  void *v5; // [sp+28h] [bp-30h]@5
  GUILD_BATTLE::CGuildBattleReservedSchedule *v6; // [sp+30h] [bp-28h]@9
  GUILD_BATTLE::CGuildBattleReservedSchedule *v7; // [sp+38h] [bp-20h]@9
  void *v8; // [sp+40h] [bp-18h]@13
  void *v9; // [sp+48h] [bp-10h]@10
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo )
  {
    v5 = GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo;
    operator delete[](GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo);
    GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::ms_pkDBScheduleInfo = 0i64;
  }
  if ( v10->m_ppkReservedSchedule )
  {
    for ( j = 0; j < v10->m_uiMapCnt; ++j )
    {
      v7 = v10->m_ppkReservedSchedule[j];
      v6 = v7;
      if ( v7 )
        v9 = GUILD_BATTLE::CGuildBattleReservedSchedule::`scalar deleting destructor'(v6, 1u);
      else
        v9 = 0i64;
    }
    v8 = v10->m_ppkReservedSchedule;
    operator delete[](v8);
    v10->m_ppkReservedSchedule = 0i64;
  }
}
