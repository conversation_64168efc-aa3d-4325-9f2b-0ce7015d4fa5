/*
 * Function: ?CreateItemBox@@YAPEAVCItemBox@@PEAU_db_con@_STORAGE_LIST@@EPEAVCMapData@@GPEAM_NPEAVCPlayer@@HE@Z
 * Address: 0x140166CB0
 */

CItemBox *__fastcall CreateItemBox(_STORAGE_LIST::_db_con *pItem, char byCreateCode, CMapData *pMap, unsigned __int16 wLayerIndex, float *pStdPos, bool bHide, CPlayer *pAttacker, int bHolyScanner, char byEventItemLootAuth)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  CItemBox *result; // rax@10
  __int64 v12; // [sp+0h] [bp-D8h]@1
  CItemBox *v13; // [sp+20h] [bp-B8h]@4
  int j; // [sp+28h] [bp-B0h]@4
  _itembox_create_setdata Dst; // [sp+40h] [bp-98h]@11
  _STORAGE_LIST::_db_con *Src; // [sp+E0h] [bp+8h]@1
  char v17; // [sp+E8h] [bp+10h]@1
  CMapData *v18; // [sp+F0h] [bp+18h]@1
  unsigned __int16 v19; // [sp+F8h] [bp+20h]@1

  v19 = wLayerIndex;
  v18 = pMap;
  v17 = byCreateCode;
  Src = pItem;
  v9 = &v12;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v13 = 0i64;
  for ( j = 0; j < 5064; ++j )
  {
    if ( !g_ItemBox[j].m_bLive )
    {
      v13 = &g_ItemBox[j];
      break;
    }
  }
  if ( v13 )
  {
    _itembox_create_setdata::_itembox_create_setdata(&Dst);
    memcpy_0(&Dst.Item, Src, 0x32ui64);
    Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + Src->m_byTableCode, Src->m_wItemIndex);
    if ( Dst.m_pRecordSet )
    {
      Dst.byCreateCode = v17;
      Dst.pOwner = 0i64;
      Dst.bParty = 0;
      Dst.pThrower = 0i64;
      Dst.m_pMap = v18;
      Dst.m_nLayerIndex = v19;
      Dst.dwPartyBossSerial = -1;
      memcpy_0(Dst.m_fStartPos, pStdPos, 0xCui64);
      Dst.byEventItemLootAuth = byEventItemLootAuth;
      Dst.bHolyScanner = bHolyScanner;
      Dst.pAttacker = pAttacker;
      if ( CItemBox::Create(v13, &Dst, bHide) )
        result = v13;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
