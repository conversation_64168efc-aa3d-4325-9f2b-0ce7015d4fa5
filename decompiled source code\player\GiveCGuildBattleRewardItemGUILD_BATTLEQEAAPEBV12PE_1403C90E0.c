/*
 * Function: ?Give@CGuildBattleRewardItem@GUILD_BATTLE@@QEAAPEBV12@PEAVCPlayer@@@Z
 * Address: 0x1403C90E0
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall GUILD_BATTLE::CGuildBattleRewardItem::Give(GUILD_BATTLE::CGuildBattleRewardItem *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleRewardItem *result; // rax@10
  __int64 v5; // [sp+0h] [bp-98h]@1
  unsigned int v6; // [sp+20h] [bp-78h]@4
  char v7; // [sp+24h] [bp-74h]@7
  char v8; // [sp+25h] [bp-73h]@7
  unsigned int v9; // [sp+28h] [bp-70h]@9
  _STORAGE_LIST::_db_con kItem; // [sp+38h] [bp-60h]@9
  char *szReason; // [sp+78h] [bp-20h]@9
  unsigned int v12; // [sp+80h] [bp-18h]@5
  GUILD_BATTLE::CGuildBattleRewardItem *v13; // [sp+A0h] [bp+8h]@1
  CPlayer *v14; // [sp+A8h] [bp+10h]@1

  v14 = pkPlayer;
  v13 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  if ( IsOverLapItem(v13->m_ucTableCode) )
    v12 = v13->m_ucD;
  else
    v12 = GetItemDurPoint(v13->m_ucTableCode, v13->m_pFld->m_dwIndex);
  v6 = v12;
  v7 = GetDefItemUpgSocketNum(v13->m_ucTableCode, v13->m_pFld->m_dwIndex);
  v8 = 0;
  if ( (signed int)(unsigned __int8)v7 > 0 )
    v8 = rand() % (unsigned __int8)v7 + 1;
  v9 = GetBitAfterSetLimSocket(v8);
  _STORAGE_LIST::_db_con::_db_con(&kItem);
  kItem.m_byTableCode = v13->m_ucTableCode;
  kItem.m_wItemIndex = v13->m_pFld->m_dwIndex;
  kItem.m_dwDur = v6;
  kItem.m_dwLv = v9;
  szReason = "GuildBattle Reward";
  if ( CPlayer::pc_GiveItem(v14, &kItem, "GuildBattle Reward", 1) )
    result = v13;
  else
    result = &GUILD_BATTLE::CGuildBattleRewardItem::ms_kNullObj;
  return result;
}
