/*
 * Function: ?ExitMember@CTransportShip@@QEAAXPEAVCPlayer@@_N@Z
 * Address: 0x1402649C0
 */

void __fastcall CTransportShip::ExitMember(CTransportShip *this, CPlayer *pExiter, bool bLogoff)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  CTransportShip::__mgr_member *v7; // [sp+28h] [bp-20h]@7
  CTransportShip::__mgr_member *v8; // [sp+30h] [bp-18h]@18
  CTransportShip *v9; // [sp+50h] [bp+8h]@1
  CPlayer *v10; // [sp+58h] [bp+10h]@1
  bool v11; // [sp+60h] [bp+18h]@1

  v11 = bLogoff;
  v10 = pExiter;
  v9 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    v7 = &v9->m_NewMember[j];
    if ( CTransportShip::__mgr_member::is_fill(v7) && v7->pPtr == v10 && v7->dwSerial == v10->m_dwObjSerial )
    {
      if ( v11 && !v9->m_bAnchor )
        CNetIndexList::PushNode_Back(&v9->m_listLogoffMember, v10->m_dwObjSerial);
      CTransportShip::__mgr_member::init(v7);
    }
  }
  for ( j = 0; j < 2532; ++j )
  {
    v8 = &v9->m_OldMember[j];
    if ( CTransportShip::__mgr_member::is_fill(v8) && v8->pPtr == v10 && v8->dwSerial == v10->m_dwObjSerial )
      CTransportShip::__mgr_member::init(v8);
  }
}
