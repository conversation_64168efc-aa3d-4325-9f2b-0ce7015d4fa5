/*
 * Function: ?SendMsg_AddEffect@CNuclearBomb@@QEAAXXZ
 * Address: 0x14013CE00
 */

void __fastcall CNuclearBomb::SendMsg_AddEffect(CNuclearBomb *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@9
  __int64 v4; // [sp+0h] [bp-88h]@1
  int j; // [sp+30h] [bp-58h]@4
  _nuclear_bomb_explosion_result_zocl v6; // [sp+44h] [bp-44h]@9
  char pbyType; // [sp+64h] [bp-24h]@9
  char v8; // [sp+65h] [bp-23h]@9
  CNuclearBomb *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < v9->m_nEffObjNum; ++j )
  {
    if ( v9->m_EffList[j].m_pChar )
    {
      if ( !strcmp_0(v9->m_EffList[j].m_pChar->m_pCurMap->m_pMapSet->m_strCode, "resources") )
      {
        _nuclear_bomb_explosion_result_zocl::_nuclear_bomb_explosion_result_zocl(&v6);
        v6.byRaceCode = CPlayerDB::GetRaceCode(&v9->m_pMaster->m_Param);
        v6.byUseClass = CNuclearBomb::GetMasterClass(v9);
        pbyType = 60;
        v8 = 6;
        v3 = _nuclear_bomb_explosion_result_zocl::size(&v6);
        CNetProcess::LoadSendMsg(
          unk_1414F2088,
          v9->m_EffList[j].m_pChar->m_ObjID.m_wIndex,
          &pbyType,
          &v6.byRaceCode,
          v3);
      }
    }
  }
  CNuclearBomb::SendMsg_InformAttack(v9);
}
