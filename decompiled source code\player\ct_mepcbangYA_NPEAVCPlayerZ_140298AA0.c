/*
 * Function: ?ct_mepcbang@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140298AA0
 */

char __fastcall ct_mepcbang(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPcBangFavor *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-58h]@1
  unsigned int v6; // [sp+40h] [bp-18h]@6
  _AVATOR_DATA *pData; // [sp+48h] [bp-10h]@6
  CPlayer *pOnea; // [sp+60h] [bp+8h]@1

  pOnea = pOne;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pOnea )
  {
    pData = &pOnea->m_pUserDB->m_AvatorData;
    v4 = CPcBangFavor::Instance();
    v6 = CPcBangFavor::ClassCodePasing(v4, pData, pOnea);
    if ( v6 == -1 )
    {
      result = 0;
    }
    else
    {
      sprintf(wszRespon, "you pcbang reward list index : %u", v6);
      CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
