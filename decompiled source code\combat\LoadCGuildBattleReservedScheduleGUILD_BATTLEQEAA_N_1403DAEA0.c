/*
 * Function: ?Load@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA_N_NPEAU_worlddb_guild_battle_schedule_list@@@Z
 * Address: 0x1403DAEA0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Load(GUILD_BATTLE::CGuildBattleReservedSchedule *this, bool bToday, _worlddb_guild_battle_schedule_list *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@12
  GUILD_BATTLE::CGuildBattleSchedulePool *v7; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@15
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@20
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@24
  __int64 v11; // [sp+0h] [bp-138h]@1
  __int64 tTime; // [sp+20h] [bp-118h]@12
  unsigned __int16 wTumeMin[2]; // [sp+28h] [bp-110h]@16
  int v14; // [sp+30h] [bp-108h]@20
  int v15; // [sp+38h] [bp-100h]@20
  int v16; // [sp+40h] [bp-F8h]@20
  int v17; // [sp+48h] [bp-F0h]@20
  int v18; // [sp+50h] [bp-E8h]@20
  int v19; // [sp+58h] [bp-E0h]@20
  unsigned int v20; // [sp+60h] [bp-D8h]@8
  GUILD_BATTLE::CGuildBattleSchedule *v21; // [sp+68h] [bp-D0h]@8
  _worlddb_guild_battle_schedule_list::__list *v22; // [sp+70h] [bp-C8h]@8
  unsigned int j; // [sp+78h] [bp-C0h]@8
  ATL::CTime v24; // [sp+88h] [bp-B0h]@17
  ATL::CTime result; // [sp+A8h] [bp-90h]@21
  __int64 v26; // [sp+B8h] [bp-80h]@14
  __int64 v27; // [sp+C0h] [bp-78h]@15
  const char *v28; // [sp+C8h] [bp-70h]@18
  int v29; // [sp+D0h] [bp-68h]@20
  int v30; // [sp+D4h] [bp-64h]@20
  int v31; // [sp+D8h] [bp-60h]@20
  int v32; // [sp+DCh] [bp-5Ch]@20
  int v33; // [sp+E0h] [bp-58h]@20
  int v34; // [sp+E4h] [bp-54h]@20
  int v35; // [sp+E8h] [bp-50h]@20
  int v36; // [sp+ECh] [bp-4Ch]@20
  __int64 v37; // [sp+F0h] [bp-48h]@20
  const char *v38; // [sp+F8h] [bp-40h]@22
  int v39; // [sp+100h] [bp-38h]@24
  int v40; // [sp+104h] [bp-34h]@24
  int v41; // [sp+108h] [bp-30h]@24
  int v42; // [sp+10Ch] [bp-2Ch]@24
  int v43; // [sp+110h] [bp-28h]@24
  int v44; // [sp+114h] [bp-24h]@24
  int v45; // [sp+118h] [bp-20h]@24
  int v46; // [sp+11Ch] [bp-1Ch]@24
  __int64 v47; // [sp+120h] [bp-18h]@24
  GUILD_BATTLE::CGuildBattleReservedSchedule *v48; // [sp+140h] [bp+8h]@1
  bool v49; // [sp+148h] [bp+10h]@1
  _worlddb_guild_battle_schedule_list *v50; // [sp+150h] [bp+18h]@1

  v50 = pkInfo;
  v49 = bToday;
  v48 = this;
  v3 = &v11;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pkInfo )
  {
    if ( pkInfo->wCount )
    {
      v20 = 0;
      v21 = 0i64;
      v22 = pkInfo->list;
      for ( j = 0; j < v50->wCount; ++j )
      {
        if ( v48->m_uiScheduleListID != v22[j].dwSLID )
        {
          v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          LODWORD(tTime) = v22->dwSLID;
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v6,
            "CGuildBattleReservedSchedule::Load()  m_uiScheduleID(%u) != pkInfo->list[%u].uiSLID(%u) Fail!",
            v48->m_uiScheduleListID,
            j);
          return 0;
        }
        if ( v22[j].ucState == 1 )
        {
          v26 = 32i64 * j;
          v7 = GUILD_BATTLE::CGuildBattleSchedulePool::Instance();
          v21 = GUILD_BATTLE::CGuildBattleSchedulePool::Get(v7, v22[(unsigned __int64)v26 / 0x20].dwID);
          if ( !v21 )
          {
            v27 = 32i64 * j;
            v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v8,
              "CGuildBattleReservedSchedule::Load() CGuildBattleSchedulePool::Instance()->Get( %u ) Fail!",
              v22[(unsigned __int64)v27 / 0x20].dwID);
            return 0;
          }
          wTumeMin[0] = v22[j].wTumeMin;
          tTime = v22[j].tStartTime;
          if ( GUILD_BATTLE::CGuildBattleSchedule::Load(v21, v49, v22[j].dwID, v22[j].ucState, tTime, wTumeMin[0]) )
          {
            v20 = v22[j].dwID % 0x17;
            v48->m_pkSchedule[v20] = v21;
            GUILD_BATTLE::CGuildBattleSchedule::GetTime(v21, &result);
            if ( v49 )
              v38 = "today";
            else
              v38 = "tommorrow";
            v39 = v22[j].wTumeMin;
            v40 = ATL::CTime::GetSecond(&result);
            v41 = ATL::CTime::GetMinute(&result);
            v42 = ATL::CTime::GetHour(&result);
            v43 = ATL::CTime::GetDay(&result);
            v44 = ATL::CTime::GetMonth(&result);
            v45 = ATL::CTime::GetYear(&result);
            v46 = v22[j].ucState;
            v47 = 32i64 * j;
            v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            v19 = v39;
            v18 = v40;
            v17 = v41;
            v16 = v42;
            v15 = v43;
            v14 = v44;
            *(_DWORD *)wTumeMin = v45;
            LODWORD(tTime) = v46;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v10,
              "CGuildBattleReservedSchedule::Load() : %s : pkSchedule->Load( %u, %u, %04d-%02d-%02d %02d:%02d:%02d, %u ) Load!",
              v38,
              v22[(unsigned __int64)v47 / 0x20].dwID);
          }
          else
          {
            GUILD_BATTLE::CGuildBattleSchedule::Clear(v21);
            ATL::CTime::CTime(&v24, v22[j].tStartTime);
            if ( v49 )
              v28 = "today";
            else
              v28 = "tommorrow";
            v29 = v22[j].wTumeMin;
            v30 = ATL::CTime::GetSecond(&v24);
            v31 = ATL::CTime::GetMinute(&v24);
            v32 = ATL::CTime::GetHour(&v24);
            v33 = ATL::CTime::GetDay(&v24);
            v34 = ATL::CTime::GetMonth(&v24);
            v35 = ATL::CTime::GetYear(&v24);
            v36 = v22[j].ucState;
            v37 = 32i64 * j;
            v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            v19 = v29;
            v18 = v30;
            v17 = v31;
            v16 = v32;
            v15 = v33;
            v14 = v34;
            *(_DWORD *)wTumeMin = v35;
            LODWORD(tTime) = v36;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v9,
              "CGuildBattleReservedSchedule::Load() : %s : pkSchedule->Load( %u, %u, %04d-%02d-%02d %02d:%02d:%02d, %u ) Skip!",
              v28,
              v22[(unsigned __int64)v37 / 0x20].dwID);
          }
        }
      }
      v5 = 1;
    }
    else
    {
      v5 = 1;
    }
  }
  else
  {
    v5 = 0;
  }
  return v5;
}
