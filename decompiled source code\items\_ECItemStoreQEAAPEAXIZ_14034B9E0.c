/*
 * Function: ??_ECItemStore@@QEAAPEAXI@Z
 * Address: 0x14034B9E0
 */

CItemStore *__fastcall CItemStore::`vector deleting destructor'(CItemStore *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CItemStore *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CItemStore *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x78ui64,
      ptr[-1].m_dwLastTradeActPoint[2],
      (void (__cdecl *)(void *))CItemStore::~CItemStore);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].m_dwLastTradeActPoint[2]);
    result = (CItemStore *)((char *)ptr - 8);
  }
  else
  {
    CItemStore::~CItemStore(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
