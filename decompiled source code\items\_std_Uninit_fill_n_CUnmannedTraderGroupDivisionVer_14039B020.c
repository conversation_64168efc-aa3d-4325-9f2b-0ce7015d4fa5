/*
 * Function: _std::_Uninit_fill_n_CUnmannedTraderGroupDivisionVersionInfo_____ptr64_unsigned___int64_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo____::_1_::catch$0
 * Address: 0x14039B020
 */

void __fastcall __noreturn std::_Uninit_fill_n_CUnmannedTraderGroupDivisionVersionInfo_____ptr64_unsigned___int64_CUnmannedTraderGroupDivisionVersionInfo_std::allocator_CUnmannedTraderGroupDivisionVersionInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 64); *(_QWORD *)(i + 32) += 48i64 )
    std::allocator<CUnmannedTraderGroupDivisionVersionInfo>::destroy(
      *(std::allocator<CUnmannedTraderGroupDivisionVersionInfo> **)(i + 88),
      *(CUnmannedTraderGroupDivisionVersionInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
