/*
 * Function: ?NotifyDestoryBall@CNormalGuildBattle@GUILD_BATTLE@@QEAAXK@Z
 * Address: 0x1403E5510
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::NotifyDestoryBall(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwOwnerSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char pMsg[4]; // [sp+24h] [bp-44h]@4
  char byType; // [sp+44h] [bp-24h]@4
  char v7; // [sp+45h] [bp-23h]@4
  GUILD_BATTLE::CNormalGuildBattle *v8; // [sp+70h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)pMsg = dwOwnerSerial;
  byType = 27;
  v7 = 81;
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v8->m_k1P, &byType, pMsg, 4u);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(&v8->m_k2P, &byType, pMsg, 4u);
}
