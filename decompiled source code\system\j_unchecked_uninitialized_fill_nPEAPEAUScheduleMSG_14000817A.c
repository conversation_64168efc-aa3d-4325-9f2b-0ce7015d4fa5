/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAUScheduleMSG@@_KPEAU1@V?$allocator@PEAUScheduleMSG@@@std@@@stdext@@YAXPEAPEAUSchedule<PERSON><PERSON>@@_KAEBQEAU1@AEAV?$allocator@PEAUScheduleMSG@@@std@@@Z
 * Address: 0x14000817A
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<ScheduleMSG * *,unsigned __int64,ScheduleMSG *,std::allocator<ScheduleMSG *>>(ScheduleMSG **_First, unsigned __int64 _Count, ScheduleMSG *const *_Val, std::allocator<ScheduleMSG *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<ScheduleMSG * *,unsigned __int64,ScheduleMSG *,std::allocator<ScheduleMSG *>>(
    _First,
    _Count,
    _<PERSON>,
    _<PERSON>);
}
