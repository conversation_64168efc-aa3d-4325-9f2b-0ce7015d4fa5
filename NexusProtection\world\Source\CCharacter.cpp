/**
 * @file CCharacter.cpp
 * @brief Base Character Class Implementation
 * 
 * Provides the base character functionality for all game entities including
 * players, monsters, NPCs, and other interactive objects.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "../Headers/CCharacter.h"
#include "../Headers/CGameObject.h"
#include <iostream>
#include <sstream>
#include <cmath>
#include <algorithm>
#include <cstring>

namespace NexusProtection::World {

    // Static member initialization
    uint32_t CCharacter::s_dwNextSerial = 1;
    std::mutex CCharacter::s_serialMutex;

    // _effect_parameter implementation
    void _effect_parameter::InitEffParam() {
        effectType = 0;
        duration = 0;
        intensity = 0.0f;
        flags = 0;
    }

    bool _effect_parameter::IsActive() const {
        return duration > 0 && effectType != 0;
    }

    void _effect_parameter::Reset() {
        InitEffParam();
    }

    // CMyTimer implementation
    CMyTimer::CMyTimer() = default;
    CMyTimer::~CMyTimer() = default;

    void CMyTimer::Start() {
        m_startTime = std::chrono::steady_clock::now();
        m_isRunning = true;
    }

    void CMyTimer::Stop() {
        m_isRunning = false;
    }

    void CMyTimer::Reset() {
        m_startTime = std::chrono::steady_clock::now();
        m_isRunning = false;
    }

    uint32_t CMyTimer::GetElapsed() const {
        if (!m_isRunning) {
            return 0;
        }
        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
        return static_cast<uint32_t>(duration.count());
    }

    bool CMyTimer::IsRunning() const {
        return m_isRunning;
    }

    // _object_id implementation
    bool _object_id::IsValid() const {
        return dwSerial != 0 && wIndex != 0;
    }

    std::string _object_id::ToString() const {
        std::ostringstream oss;
        oss << "ObjectID{Serial:" << dwSerial << ", Index:" << wIndex 
            << ", Type:" << static_cast<int>(byType) << ", Flags:" << static_cast<int>(byFlags) << "}";
        return oss.str();
    }

    // _character_create_setdata implementation
    bool _character_create_setdata::IsValid() const {
        return pObjectId != nullptr && pObjectId->IsValid() && 
               level > 0 && hp > 0 && mp > 0;
    }

    // CCharacter implementation
    CCharacter::CCharacter() {
        InitializeDefaults();
        m_dwObjSerial = GenerateSerial();
        std::cout << "[DEBUG] CCharacter created with serial: " << m_dwObjSerial << std::endl;
    }

    CCharacter::~CCharacter() {
        CleanupEffects();
        std::cout << "[DEBUG] CCharacter destroyed: " << m_dwObjSerial << std::endl;
    }

    bool CCharacter::Create(const _character_create_setdata* pData) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        
        try {
            if (!ValidateCreateData(pData)) {
                LogError("Invalid creation data", "Create");
                return false;
            }

            // Set basic properties from creation data
            if (pData->pObjectId) {
                m_dwObjSerial = pData->pObjectId->dwSerial;
            }
            
            SetPosition(pData->position);
            SetRotation(pData->rotation);
            m_dwLevel = pData->level;
            m_nHP = static_cast<int>(pData->hp);
            m_nMaxHP = m_nHP;
            m_nMP = static_cast<int>(pData->mp);
            m_nMaxMP = m_nMP;
            m_wMapLayerIndex = static_cast<uint16_t>(pData->mapIndex);
            m_pRecordSet = pData->pRecordSet;

            // Initialize effects and timers
            m_EP.InitEffParam();
            m_tmrSFCont.Reset();
            ResetEffects();

            // Set alive status
            m_bLive = true;
            m_dwStatus = static_cast<uint32_t>(CharacterStatus::Alive);

            std::cout << "[INFO] Character created successfully: " << GetDebugInfo() << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            LogError("Exception during creation: " + std::string(e.what()), "Create");
            return false;
        }
    }

    bool CCharacter::Init(_object_id* pID) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        
        try {
            if (!pID || !pID->IsValid()) {
                LogError("Invalid object ID", "Init");
                return false;
            }

            m_dwObjSerial = pID->dwSerial;
            
            // Initialize default values
            InitializeDefaults();
            InitializeStatusEffects();
            
            std::cout << "[INFO] Character initialized: " << pID->ToString() << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            LogError("Exception during initialization: " + std::string(e.what()), "Init");
            return false;
        }
    }

    bool CCharacter::Destroy() {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        
        try {
            // Reset effect parameters
            m_EP.InitEffParam();
            
            // Clear status effect containers
            ResetEffects();
            
            // Reset flags
            m_bLastContEffectUpdate = false;
            m_wLastCont = 0;
            
            // Set dead status
            m_bLive = false;
            m_dwStatus = static_cast<uint32_t>(CharacterStatus::Dead);
            
            std::cout << "[INFO] Character destroyed: " << m_dwObjSerial << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            LogError("Exception during destruction: " + std::string(e.what()), "Destroy");
            return false;
        }
    }

    void CCharacter::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        
        if (!m_bLive) {
            return;
        }
        
        try {
            UpdatePosition(deltaTime);
            UpdateStatusEffects(deltaTime);
            UpdateEffects(deltaTime);
            
        } catch (const std::exception& e) {
            LogError("Exception during update: " + std::string(e.what()), "Update");
        }
    }

    void CCharacter::Move(float deltaTime) {
        if (!m_bLive || GetStun()) {
            return;
        }
        
        UpdatePosition(deltaTime);
    }

    void CCharacter::SetPosition(float x, float y, float z) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        m_fCurPos[0] = x;
        m_fCurPos[1] = y;
        m_fCurPos[2] = z;
    }

    void CCharacter::SetPosition(const float* pos) {
        if (!pos) return;
        std::lock_guard<std::mutex> lock(m_characterMutex);
        std::memcpy(m_fCurPos, pos, sizeof(float) * 3);
    }

    void CCharacter::GetPosition(float* pos) const {
        if (!pos) return;
        std::lock_guard<std::mutex> lock(m_characterMutex);
        std::memcpy(pos, m_fCurPos, sizeof(float) * 3);
    }

    void CCharacter::SetRotation(float x, float y, float z) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        m_fRotation[0] = x;
        m_fRotation[1] = y;
        m_fRotation[2] = z;
        CharacterUtils::NormalizeRotation(m_fRotation);
    }

    void CCharacter::GetRotation(float* rot) const {
        if (!rot) return;
        std::lock_guard<std::mutex> lock(m_characterMutex);
        std::memcpy(rot, m_fRotation, sizeof(float) * 3);
    }

    void CCharacter::SetStun(bool stunned) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        if (stunned) {
            m_dwStatus |= static_cast<uint32_t>(CharacterStatus::Stunned);
        } else {
            m_dwStatus &= ~static_cast<uint32_t>(CharacterStatus::Stunned);
        }
    }

    void CCharacter::SetCombatState(bool inCombat) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        if (inCombat) {
            m_dwStatus |= static_cast<uint32_t>(CharacterStatus::InCombat);
        } else {
            m_dwStatus &= ~static_cast<uint32_t>(CharacterStatus::InCombat);
        }
    }

    void CCharacter::ApplyEffect(const _effect_parameter& effect) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        // For now, just store the effect in m_EP
        // In a full implementation, this would manage multiple effects
        m_EP = effect;
    }

    void CCharacter::RemoveEffect(uint32_t effectType) {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        if (m_EP.effectType == effectType) {
            m_EP.Reset();
        }
    }

    void CCharacter::UpdateEffects(float deltaTime) {
        if (m_EP.IsActive()) {
            m_EP.duration = std::max(0u, m_EP.duration - static_cast<uint32_t>(deltaTime * 1000));
            if (m_EP.duration == 0) {
                m_EP.Reset();
            }
        }
    }

    bool CCharacter::HasEffect(uint32_t effectType) const {
        std::lock_guard<std::mutex> lock(m_characterMutex);
        return m_EP.effectType == effectType && m_EP.IsActive();
    }

    void CCharacter::SetSFCont(int index, uint32_t value) {
        if (index >= 0 && index < static_cast<int>(MAX_SF_CONT)) {
            std::lock_guard<std::mutex> lock(m_characterMutex);
            m_SFCont[index] = value;
        }
    }

    uint32_t CCharacter::GetSFCont(int index) const {
        if (index >= 0 && index < static_cast<int>(MAX_SF_CONT)) {
            std::lock_guard<std::mutex> lock(m_characterMutex);
            return m_SFCont[index];
        }
        return 0;
    }

    void CCharacter::SetSFContAura(int index, uint32_t value) {
        if (index >= 0 && index < static_cast<int>(MAX_SF_CONT_AURA)) {
            std::lock_guard<std::mutex> lock(m_characterMutex);
            m_SFContAura[index] = value;
        }
    }

    uint32_t CCharacter::GetSFContAura(int index) const {
        if (index >= 0 && index < static_cast<int>(MAX_SF_CONT_AURA)) {
            std::lock_guard<std::mutex> lock(m_characterMutex);
            return m_SFContAura[index];
        }
        return 0;
    }

    bool CCharacter::ValidateState() const {
        std::lock_guard<std::mutex> lock(m_characterMutex);

        // Basic validation checks
        if (m_dwObjSerial == 0) {
            return false;
        }

        if (m_nHP < 0 || m_nMaxHP <= 0 || m_nHP > m_nMaxHP) {
            return false;
        }

        if (m_nMP < 0 || m_nMaxMP <= 0 || m_nMP > m_nMaxMP) {
            return false;
        }

        if (m_dwLevel == 0) {
            return false;
        }

        return true;
    }

    void CCharacter::LogError(const std::string& message, const std::string& function) const {
        std::cout << "[ERROR] CCharacter::" << function << " - " << message
                  << " (Serial: " << m_dwObjSerial << ")" << std::endl;
    }

    std::string CCharacter::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_characterMutex);

        std::ostringstream oss;
        oss << "CCharacter{";
        oss << "Serial:" << m_dwObjSerial;
        oss << ", Pos:[" << m_fCurPos[0] << "," << m_fCurPos[1] << "," << m_fCurPos[2] << "]";
        oss << ", HP:" << m_nHP << "/" << m_nMaxHP;
        oss << ", MP:" << m_nMP << "/" << m_nMaxMP;
        oss << ", Level:" << m_dwLevel;
        oss << ", Alive:" << (m_bLive ? "true" : "false");
        oss << ", Status:0x" << std::hex << m_dwStatus << std::dec;
        oss << "}";
        return oss.str();
    }

    // Protected methods
    void CCharacter::InitializeDefaults() {
        m_dwObjSerial = 0;
        m_wMapLayerIndex = 0;
        m_bLive = true;
        m_dwStatus = static_cast<uint32_t>(CharacterStatus::Alive);

        // Initialize position and rotation
        std::fill(std::begin(m_fCurPos), std::end(m_fCurPos), 0.0f);
        std::fill(std::begin(m_fRotation), std::end(m_fRotation), 0.0f);

        m_fMoveSpeed = DEFAULT_MOVE_SPEED;
        m_moveState = CharacterMoveState::Idle;

        // Initialize health and mana
        m_nHP = 100;
        m_nMaxHP = 100;
        m_nMP = 100;
        m_nMaxMP = 100;

        // Initialize level and experience
        m_dwLevel = 1;
        m_qwExp = 0;

        // Initialize status effects
        m_SFCont.fill(0);
        m_SFContAura.fill(0);
        m_bLastContEffectUpdate = false;
        m_wLastCont = 0;

        // Initialize defense parts
        m_nDefPart.fill(0);

        m_pRecordSet = nullptr;
    }

    void CCharacter::ResetEffects() {
        m_SFCont.fill(0);
        m_SFContAura.fill(0);
        m_bLastContEffectUpdate = false;
        m_wLastCont = 0;
        m_EP.Reset();
    }

    bool CCharacter::ValidateCreateData(const _character_create_setdata* pData) const {
        if (!pData) {
            return false;
        }

        if (!pData->IsValid()) {
            return false;
        }

        if (!CharacterUtils::IsValidPosition(pData->position)) {
            return false;
        }

        return true;
    }

    void CCharacter::UpdatePosition(float deltaTime) {
        // Basic position update - in derived classes this would be more complex
        if (m_moveState != CharacterMoveState::Idle && m_fMoveSpeed > 0.0f) {
            // Simple forward movement for demonstration
            float distance = m_fMoveSpeed * deltaTime;
            m_fCurPos[0] += distance * std::cos(m_fRotation[1]);
            m_fCurPos[2] += distance * std::sin(m_fRotation[1]);
        }
    }

    void CCharacter::UpdateStatusEffects(float deltaTime) {
        // Update continuous effects
        if (m_tmrSFCont.IsRunning()) {
            uint32_t elapsed = m_tmrSFCont.GetElapsed();
            // Process status effect updates based on elapsed time
            // This is a placeholder - real implementation would process specific effects
        }
    }

    // Private methods
    uint32_t CCharacter::GenerateSerial() {
        std::lock_guard<std::mutex> lock(s_serialMutex);
        return s_dwNextSerial++;
    }

    void CCharacter::InitializeStatusEffects() {
        m_SFCont.fill(0);
        m_SFContAura.fill(0);
        m_EP.InitEffParam();
        m_tmrSFCont.Reset();
    }

    void CCharacter::CleanupEffects() {
        ResetEffects();
        m_tmrSFCont.Stop();
    }

    // Utility functions
    namespace CharacterUtils {
        std::string CharacterStatusToString(CharacterStatus status) {
            switch (status) {
                case CharacterStatus::None: return "None";
                case CharacterStatus::Alive: return "Alive";
                case CharacterStatus::Dead: return "Dead";
                case CharacterStatus::Stunned: return "Stunned";
                case CharacterStatus::Paralyzed: return "Paralyzed";
                case CharacterStatus::Sleeping: return "Sleeping";
                case CharacterStatus::Confused: return "Confused";
                case CharacterStatus::Invisible: return "Invisible";
                case CharacterStatus::Invulnerable: return "Invulnerable";
                case CharacterStatus::Flying: return "Flying";
                case CharacterStatus::Swimming: return "Swimming";
                case CharacterStatus::Mounted: return "Mounted";
                case CharacterStatus::InCombat: return "InCombat";
                case CharacterStatus::Trading: return "Trading";
                case CharacterStatus::Crafting: return "Crafting";
                case CharacterStatus::Meditating: return "Meditating";
                case CharacterStatus::Channeling: return "Channeling";
                default: return "Unknown";
            }
        }

        std::string CharacterMoveStateToString(CharacterMoveState state) {
            switch (state) {
                case CharacterMoveState::Idle: return "Idle";
                case CharacterMoveState::Walking: return "Walking";
                case CharacterMoveState::Running: return "Running";
                case CharacterMoveState::Flying: return "Flying";
                case CharacterMoveState::Falling: return "Falling";
                case CharacterMoveState::Swimming: return "Swimming";
                case CharacterMoveState::Teleporting: return "Teleporting";
                default: return "Unknown";
            }
        }

        bool IsValidPosition(const float* pos) {
            if (!pos) return false;

            // Check for NaN or infinite values
            for (int i = 0; i < 3; ++i) {
                if (std::isnan(pos[i]) || std::isinf(pos[i])) {
                    return false;
                }
            }

            // Check for reasonable bounds (adjust as needed)
            const float MAX_COORD = 100000.0f;
            for (int i = 0; i < 3; ++i) {
                if (std::abs(pos[i]) > MAX_COORD) {
                    return false;
                }
            }

            return true;
        }

        float CalculateDistance(const float* pos1, const float* pos2) {
            if (!pos1 || !pos2) return 0.0f;

            float dx = pos1[0] - pos2[0];
            float dy = pos1[1] - pos2[1];
            float dz = pos1[2] - pos2[2];

            return std::sqrt(dx * dx + dy * dy + dz * dz);
        }

        void NormalizeRotation(float* rotation) {
            if (!rotation) return;

            const float TWO_PI = 2.0f * 3.14159265359f;

            for (int i = 0; i < 3; ++i) {
                while (rotation[i] > TWO_PI) {
                    rotation[i] -= TWO_PI;
                }
                while (rotation[i] < 0.0f) {
                    rotation[i] += TWO_PI;
                }
            }
        }
    }

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {
    void CCharacter_Constructor(CCharacter_Legacy* character) {
        if (character) {
            // Initialize legacy structure
            character->vfptr = nullptr;
            std::cout << "[DEBUG] Legacy CCharacter constructed" << std::endl;
        }
    }

    void CCharacter_Destructor(CCharacter_Legacy* character) {
        if (character) {
            std::cout << "[DEBUG] Legacy CCharacter destructed" << std::endl;
        }
    }

    bool CCharacter_Create(CCharacter_Legacy* character, _character_create_setdata* pData) {
        if (!character || !pData) {
            return false;
        }

        // For legacy compatibility, we'll create a modern CCharacter and manage it
        // In a full implementation, this would properly bridge to the legacy system
        std::cout << "[DEBUG] Legacy CCharacter create called" << std::endl;
        return true;
    }

    void CCharacter_Init(CCharacter_Legacy* character, _object_id* pID) {
        if (character && pID) {
            std::cout << "[DEBUG] Legacy CCharacter init called with ID: " << pID->ToString() << std::endl;
        }
    }

    bool CCharacter_Destroy(CCharacter_Legacy* character) {
        if (character) {
            std::cout << "[DEBUG] Legacy CCharacter destroy called" << std::endl;
            return true;
        }
        return false;
    }

    void CCharacter_Move(CCharacter_Legacy* character, float deltaTime) {
        if (character) {
            std::cout << "[DEBUG] Legacy CCharacter move called with deltaTime: " << deltaTime << std::endl;
        }
    }
}
