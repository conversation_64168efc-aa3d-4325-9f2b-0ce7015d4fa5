/*
 * Function: j_??$_Iter_random@PEAPEAVCUnmannedTraderSortType@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCUnmannedTraderSortType@@0@Z
 * Address: 0x1400071BC
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(CUnmannedTraderSortType **const *__formal, CUnmannedTraderSortType **const *a2)
{
  return std::_Iter_random<CUnmannedTraderSortType * *,CUnmannedTraderSortType * *>(__formal, a2);
}
