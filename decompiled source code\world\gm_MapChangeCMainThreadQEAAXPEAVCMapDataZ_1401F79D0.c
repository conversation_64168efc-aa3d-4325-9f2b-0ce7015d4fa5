/*
 * Function: ?gm_MapChange@CMainThread@@QEAAXPEAVCMapData@@@Z
 * Address: 0x1401F79D0
 */

void __fastcall CMainThread::gm_MapChange(CMainThread *this, CMapData *pMap)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMapData *pMapa; // [sp+38h] [bp+10h]@1

  pMapa = pMap;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  stru_141473720.m_bExtendMode = 0;
  CDisplayView::SetExtendMode(&g_pDoc->m_DisplayView, 0);
  CMapDisplay::ChangeMap(&g_MapDisplay, pMapa);
}
