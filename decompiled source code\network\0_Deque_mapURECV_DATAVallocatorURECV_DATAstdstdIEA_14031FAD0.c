/*
 * Function: ??0?$_Deque_map@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@IEAA@V?$allocator@URECV_DATA@@@1@@Z
 * Address: 0x14031FAD0
 */

void __fastcall std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>::_Deque_map<RECV_DATA,std::allocator<RECV_DATA>>(std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA> > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Deque_map<RECV_DATA,std::allocator<RECV_DATA> > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<RECV_DATA> *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<RECV_DATA> *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<RECV_DATA *>::allocator<RECV_DATA *>(&v5->_Almap, __formal);
}
