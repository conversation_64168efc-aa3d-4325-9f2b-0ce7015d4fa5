/*
 * Function: ?Insert_NpcLog@CRFWorldDatabase@@QEAA_NKPEADEKK@Z
 * Address: 0x14049F4C0
 */

bool __fastcall CRFWorldDatabase::Insert_NpcLog(CRFWorldDatabase *this, unsigned int dwSerial, char *pwszName, char byIndex, unsigned int dwOrgValue, unsigned int dwChgValue)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-178h]@1
  int v10; // [sp+20h] [bp-158h]@4
  unsigned int v11; // [sp+28h] [bp-150h]@4
  unsigned int v12; // [sp+30h] [bp-148h]@4
  char Dest; // [sp+50h] [bp-128h]@4
  unsigned __int64 v14; // [sp+160h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+180h] [bp+8h]@1

  v15 = this;
  v6 = &v9;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  v12 = dwChgValue;
  v11 = dwOrgValue;
  v10 = (unsigned __int8)byIndex;
  sprintf(&Dest, "{ CALL pInsert_NpcLog( %d, '%s', %d, %d, %d ) }", dwSerial, pwszName);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v15->vfptr, &Dest, 1);
}
