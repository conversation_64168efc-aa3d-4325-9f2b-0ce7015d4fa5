/*
 * Function: j_?Select_UnmannedTraderBuySingleItemInfo@CRFWorldDatabase@@QEAAEEKAEAU_unmannedtrader_buy_item_info@@@Z
 * Address: 0x14000F62D
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(CRFWorldDatabase *this, char byType, unsigned int dwRegistSerial, _unmannedtrader_buy_item_info *kData)
{
  return CRFWorldDatabase::Select_UnmannedTraderBuySingleItemInfo(this, byType, dwRegistSerial, kData);
}
