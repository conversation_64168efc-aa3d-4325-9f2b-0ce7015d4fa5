/*
 * Function: j_?_Kfn@?$_Hmap_traits@HPEBU_CashShop_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_CashShop_fld@@@std@@@std@@$0A@@stdext@@SAAEBHAEBU?$pair@$$CBHPEBU_CashShop_fld@@@std@@@Z
 * Address: 0x140012BA7
 */

const int *__fastcall stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>::_Kfn(stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_CashShop_fld const *> >,0> *this, std::pair<int const ,_CashShop_fld const *> *_Val)
{
  return stdext::_Hmap_traits<int,_CashShop_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_CashShop_fld const *>>,0>::_Kfn(
           this,
           _Val);
}
