/*
 * Function: ?_Tidy@?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@IEAAXXZ
 * Address: 0x14031FBC0
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::_Tidy(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  unsigned __int64 j; // [sp+20h] [bp-18h]@6
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  while ( !std::deque<RECV_DATA,std::allocator<RECV_DATA>>::empty(v5) )
    std::deque<RECV_DATA,std::allocator<RECV_DATA>>::pop_back(v5);
  for ( j = v5->_Mapsize; j; std::allocator<RECV_DATA *>::destroy(&v5->_Almap, &v5->_Map[j]) )
  {
    if ( v5->_Map[--j] )
      std::allocator<RECV_DATA>::deallocate(&v5->_Alval, v5->_Map[j], 1ui64);
  }
  if ( v5->_Map )
    std::allocator<RECV_DATA *>::deallocate(&v5->_Almap, v5->_Map, v5->_Mapsize);
  v5->_Mapsize = 0i64;
  v5->_Map = 0i64;
}
