/*
 * Function: _std::find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor$5
 * Address: 0x140369130
 */

void __fastcall std::find_std::_Vector_iterator_CUnmannedTraderUserInfo_std::allocator_CUnmannedTraderUserInfo____unsigned_long__::_1_::dtor_5(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 1 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(*(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > **)(a2 + 192));
  }
}
