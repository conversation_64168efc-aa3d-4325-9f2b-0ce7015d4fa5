/*
 * Function: ?SendMsg_AlterTranspar@CTrap@@QEAAX_N@Z
 * Address: 0x14013FD90
 */

void __fastcall CTrap::SendMsg_AlterTranspar(CTrap *this, bool bTranspar)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  bool v6; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CTrap *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = v9->m_dwObjSerial;
  v6 = bTranspar;
  pbyType = 4;
  v8 = 35;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 5, 0);
}
