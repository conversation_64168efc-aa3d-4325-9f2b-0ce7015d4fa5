/*
 * Function: ?SendSearchErrorResult@CUnmannedTraderUserInfo@@QEAAXGE@Z
 * Address: 0x140358150
 */

void __fastcall CUnmannedTraderUserInfo::SendSearchErrorResult(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, char byRet)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-288h]@1
  char szMsg; // [sp+40h] [bp-248h]@4
  char pbyType; // [sp+254h] [bp-34h]@4
  char v8; // [sp+255h] [bp-33h]@4
  unsigned __int64 v9; // [sp+270h] [bp-18h]@4

  v3 = &v5;
  for ( i = 160i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byRet;
  pbyType = 30;
  v8 = 33;
  CNetProcess::LoadSendMsg(unk_1414F2088, wInx, &pbyType, &szMsg, 0x1F3u);
}
