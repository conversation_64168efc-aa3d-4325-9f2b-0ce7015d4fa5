/*
 * Function: ??0?$_List_val@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@2@@std@@QEAA@V?$allocator@U?$pair@$$CBHPEAVCAsyncLogInfo@@@std@@@1@@Z
 * Address: 0x1403C66F0
 */

void __fastcall std::_List_val<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_List_val<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(std::_List_val<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<int const ,CAsyncLogInfo *> > v4; // al@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  std::allocator<std::pair<int const ,CAsyncLogInfo *> > *v7; // [sp+28h] [bp-10h]@4
  std::_List_val<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *v8; // [sp+40h] [bp+8h]@1
  std::allocator<std::pair<int const ,CAsyncLogInfo *> > *__formal; // [sp+48h] [bp+10h]@1

  __formal = (std::allocator<std::pair<int const ,CAsyncLogInfo *> > *)_Al;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (std::allocator<std::pair<int const ,CAsyncLogInfo *> > *)&v6;
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(
    (std::allocator<std::pair<int const ,CAsyncLogInfo *> > *)&v6,
    (std::allocator<std::pair<int const ,CAsyncLogInfo *> > *)_Al);
  std::_List_ptr<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_List_ptr<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>(
    (std::_List_ptr<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > > *)&v8->_Myfirstiter,
    v4);
  std::allocator<std::pair<int const,CAsyncLogInfo *>>::allocator<std::pair<int const,CAsyncLogInfo *>>(
    &v8->_Alval,
    __formal);
}
