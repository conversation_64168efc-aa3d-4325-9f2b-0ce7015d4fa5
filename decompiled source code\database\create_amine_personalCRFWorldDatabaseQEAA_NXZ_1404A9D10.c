/*
 * Function: ?create_amine_personal@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404A9D10
 */

bool __fastcall CRFWorldDatabase::create_amine_personal(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@4
  char v6; // [sp+31h] [bp-127h]@4
  unsigned __int64 v7; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+160h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  Dest = 0;
  memset(&v6, 0, 0xFFui64);
  sprintf(&Dest, "{ call pcreate_aminepersonal_inven }");
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &Dest, 1);
}
