/*
 * Function: ?SendMsg_BuddyAddAnswerResult@CPlayer@@QEAAXE_NKGKPEAD@Z
 * Address: 0x1400E4AC0
 */

void __fastcall CPlayer::SendMsg_BuddyAddAnswerResult(CPlayer *this, char byRet<PERSON><PERSON>, bool bAccept, unsigned int dwAskerSerial, unsigned __int16 wIndex, unsigned int dwSerial, char *pwszCharName)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-A8h]@1
  char szMsg; // [sp+38h] [bp-70h]@4
  bool v11; // [sp+39h] [bp-6Fh]@4
  unsigned int v12; // [sp+3Ah] [bp-6Eh]@4
  unsigned __int16 v13; // [sp+3Eh] [bp-6Ah]@4
  unsigned int v14; // [sp+40h] [bp-68h]@4
  char Dst; // [sp+44h] [bp-64h]@5
  char v16; // [sp+54h] [bp-54h]@5
  char pbyType; // [sp+74h] [bp-34h]@6
  char v18; // [sp+75h] [bp-33h]@6
  unsigned __int64 v19; // [sp+90h] [bp-18h]@4
  CPlayer *v20; // [sp+B0h] [bp+8h]@1

  v20 = this;
  v7 = &v9;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  szMsg = byRetCode;
  v11 = bAccept;
  v12 = dwAskerSerial;
  v13 = wIndex;
  v14 = dwSerial;
  if ( !byRetCode )
  {
    memcpy_0(&Dst, pwszCharName, 0x10ui64);
    v16 = 0;
  }
  pbyType = 31;
  v18 = 13;
  CNetProcess::LoadSendMsg(unk_1414F2088, v20->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x1Du);
}
