/*
 * Function: j_??$SearchSlotIndex@UORDER_INC@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAAKAEBU_State@CLuaLooting_Novus_Item@@@Z
 * Address: 0x140008AC1
 */

unsigned int __fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::SearchSlotIndex<US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::ORDER_INC>(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this, CLuaLooting_Novus_Item::_State *state)
{
  return US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::SearchSlotIndex<US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::ORDER_INC>(
           this,
           state);
}
