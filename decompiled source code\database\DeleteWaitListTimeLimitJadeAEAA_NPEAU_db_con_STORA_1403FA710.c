/*
 * Function: ?DeleteWaitList@TimeLimitJade@@AEAA_NPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1403FA710
 */

bool __fastcall TimeLimitJade::DeleteWaitList(TimeLimitJade *this, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  TimeLimitJade::WaitCell *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-48h]@1
  TimeLimitJade::WaitCell *v7; // [sp+20h] [bp-28h]@6
  TimeLimitJade::WaitCell v8; // [sp+28h] [bp-20h]@10
  TimeLimitJade *v9; // [sp+50h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItem; // [sp+58h] [bp+10h]@1

  pItem = pkItem;
  v9 = this;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( ListHeap<TimeLimitJade::WaitCell>::empty(&v9->_heapWaitRow) )
  {
    result = 0;
  }
  else
  {
    v7 = ListHeap<TimeLimitJade::WaitCell>::top(&v9->_heapWaitRow);
    if ( v7 )
    {
      if ( v7->_pkItem == pItem )
      {
        result = ListHeap<TimeLimitJade::WaitCell>::pop(&v9->_heapWaitRow);
      }
      else
      {
        TimeLimitJade::WaitCell::WaitCell(&v8, pItem);
        result = ListHeap<TimeLimitJade::WaitCell>::pop(&v9->_heapWaitRow, v5);
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
