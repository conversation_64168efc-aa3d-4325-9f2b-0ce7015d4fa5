/*
 * Function: ?RequestRecall@CRecallEffectController@@QEAA_NPEAVCPlayer@@PEAVCCharacter@@_N22@Z
 * Address: 0x14024E340
 */

char __fastcall CRecallEffectController::RequestRecall(CRecallEffectController *this, CPlayer *pkPerformer, CCharacter *pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned __int16 v9; // ax@6
  __int64 v10; // [sp+0h] [bp-68h]@1
  CRecallRequest *pkRequest; // [sp+48h] [bp-20h]@4
  char v12; // [sp+54h] [bp-14h]@4
  CRecallEffectController *v13; // [sp+70h] [bp+8h]@1
  CPlayer *pkPerformera; // [sp+78h] [bp+10h]@1
  CPlayer *pkDesta; // [sp+80h] [bp+18h]@1

  pkDesta = (CPlayer *)pkDest;
  pkPerformera = pkPerformer;
  v13 = this;
  v6 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  pkRequest = 0i64;
  v12 = CRecallEffectController::ProcessRequestRecall(
          v13,
          pkPerformer,
          pkDest,
          &pkRequest,
          bRecallParty,
          bStone,
          bBattleModeUse);
  if ( v12 )
  {
    CRecallEffectController::SendRecallReqeustResult(v13, v12, pkPerformera);
    result = 0;
  }
  else
  {
    v9 = CRecallRequest::GetID(pkRequest);
    CRecallEffectController::SendRecallReqeustToDest(v13, v9, pkPerformera, pkDesta);
    result = 1;
  }
  return result;
}
