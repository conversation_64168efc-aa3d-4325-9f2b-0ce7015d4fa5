/*
 * Function: ?InsertSettlementOwner@CWeeklyGuildRankManager@@QEAA_NPEAVCRFWorldDatabase@@PEAD@Z
 * Address: 0x1402CCE80
 */

char __fastcall CWeeklyGuildRankManager::InsertSettlementOwner(CWeeklyGuildRankManager *this, CRFWorldDatabase *pkWorldDB, char *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char *wszGuildName; // [sp+20h] [bp-68h]@10
  unsigned __int16 wRank; // [sp+28h] [bp-60h]@10
  char byGrade; // [sp+30h] [bp-58h]@10
  long double v10; // [sp+38h] [bp-50h]@10
  long double v11; // [sp+40h] [bp-48h]@10
  unsigned int dwSumLv; // [sp+48h] [bp-40h]@10
  char v13; // [sp+50h] [bp-38h]@4
  char *v14; // [sp+58h] [bp-30h]@4
  int v15; // [sp+60h] [bp-28h]@4
  int j; // [sp+64h] [bp-24h]@4
  int k; // [sp+68h] [bp-20h]@6
  CRFWorldDatabase *v18; // [sp+98h] [bp+10h]@1

  v18 = pkWorldDB;
  v3 = &v6;
  for ( i = 30i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v13 = 1;
  v14 = pData;
  v15 = 0;
  for ( j = 0; j < 3; ++j )
  {
    for ( k = 0; k < 2; ++k )
    {
      v15 = k + 2 * j;
      if ( *(_DWORD *)&v14[56 * (k + 2 * j)] )
      {
        dwSumLv = *(_DWORD *)&v14[56 * v15 + 48];
        v11 = *(double *)&v14[56 * v15 + 40];
        v10 = *(double *)&v14[56 * v15 + 32];
        byGrade = v14[56 * v15 + 24];
        wRank = *(_WORD *)&v14[56 * v15 + 22];
        wszGuildName = &v14[56 * v15 + 4];
        if ( !CRFWorldDatabase::Insert_SettlementOwnerLog(
                v18,
                k,
                v14[56 * v15 + 21],
                *(_DWORD *)&v14[56 * v15],
                wszGuildName,
                wRank,
                byGrade,
                v10,
                v11,
                dwSumLv) )
          v13 = 0;
      }
    }
  }
  return v13;
}
