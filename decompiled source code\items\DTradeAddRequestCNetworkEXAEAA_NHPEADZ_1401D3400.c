/*
 * Function: ?DTradeAddRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D3400
 */

char __fastcall CNetworkEX::DTradeAddRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  char *v6; // rax@8
  char *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-58h]@1
  char byAmount[4]; // [sp+20h] [bp-38h]@8
  char *v10; // [sp+30h] [bp-28h]@4
  CPlayer *v11; // [sp+38h] [bp-20h]@4
  unsigned int v12; // [sp+40h] [bp-18h]@8
  unsigned int v13; // [sp+44h] [bp-14h]@10
  CNetworkEX *v14; // [sp+60h] [bp+8h]@1

  v14 = this;
  v3 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( v11->m_bOper && !v11->m_bCorpse )
  {
    if ( (signed int)(unsigned __int8)v10[5] <= 15 )
    {
      if ( (signed int)(unsigned __int8)*v10 < 8 )
      {
        byAmount[0] = v10[6];
        CPlayer::pc_DTradeAddRequest(v11, v10[5], *v10, *(_DWORD *)(v10 + 1), byAmount[0]);
        result = 1;
      }
      else
      {
        v13 = (unsigned __int8)*v10;
        v7 = CPlayerDB::GetCharNameA(&v11->m_Param);
        *(_DWORD *)byAmount = 8;
        CLogFile::Write(
          &v14->m_LogFile,
          "odd.. %s: DTradeAddRequest() : pRecv->byStorageCode(%d) >= _STORAGE_POS::STORAGE_NUM(%d)",
          v7,
          v13);
        result = 0;
      }
    }
    else
    {
      v12 = (unsigned __int8)v10[5];
      v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
      *(_DWORD *)byAmount = 15;
      CLogFile::Write(
        &v14->m_LogFile,
        "odd.. %s: DTradeAddRequest() : pRecv->bySlotIndex(%d) > max_d_trade_item(%d)",
        v6,
        v12);
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
