/*
 * Function: ?CheckAlterMaxPoint@CPlayer@@QEAAXXZ
 * Address: 0x1400A2420
 */

void __fastcall CPlayer::CheckAlterMaxPoint(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@13
  int v4; // eax@14
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+20h] [bp-28h]@4
  int v7; // [sp+24h] [bp-24h]@4
  int v8; // [sp+28h] [bp-20h]@4
  int v9; // [sp+2Ch] [bp-1Ch]@4
  int v10; // [sp+30h] [bp-18h]@13
  CPlayer *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = 0;
  v7 = -1;
  v8 = -1;
  v9 = -1;
  if ( v11->m_nOldPoint[0] != ((int (__fastcall *)(CPlayer *))v11->vfptr->GetMaxHP)(v11) )
  {
    v6 = 1;
    v11->m_nOldPoint[0] = ((int (__fastcall *)(CPlayer *))v11->vfptr->GetMaxHP)(v11);
  }
  if ( v11->m_nOldPoint[1] != CPlayer::GetMaxFP(v11) )
  {
    v6 = 1;
    v11->m_nOldPoint[1] = CPlayer::GetMaxFP(v11);
  }
  if ( v11->m_nOldPoint[2] != CPlayer::GetMaxSP(v11) )
  {
    v6 = 1;
    v11->m_nOldPoint[2] = CPlayer::GetMaxSP(v11);
  }
  if ( v6 )
  {
    CPlayer::SendMsg_MaxHFSP(v11);
    CPlayer::SendMsg_Recover(v11);
  }
  if ( v11->m_nOldMaxDP != CPlayer::GetMaxDP(v11) )
  {
    v10 = CPlayer::GetDP(v11);
    v3 = CPlayer::GetMaxDP(v11);
    if ( v10 > v3 )
    {
      v4 = CPlayer::GetMaxDP(v11);
      CPlayer::SetDP(v11, v4, 0);
      CPlayer::SendMsg_SetDPInform(v11);
    }
    CPlayer::SendMsg_AlterMaxDP(v11);
    v11->m_nOldMaxDP = CPlayer::GetMaxDP(v11);
  }
}
