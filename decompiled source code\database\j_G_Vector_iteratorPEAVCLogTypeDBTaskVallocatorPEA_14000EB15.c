/*
 * Function: j_??G?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEBA?AV01@_J@Z
 * Address: 0x14000EB15
 */

std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator-(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *result, __int64 _Off)
{
  return std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator-(this, result, _Off);
}
