/*
 * Function: ?QryCaseAddpvppoint@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F41F0
 */

void __fastcall CMainThread::QryCaseAddpvppoint(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char *v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+28h] [bp-10h]@4

  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = pData->m_sData;
  v6 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)&pData->m_sData[0]);
  if ( v6 )
  {
    if ( v6->m_bLoad )
      CPlayer::AlterPvPPoint(v6, (double)*((signed int *)v5 + 1), logoff_inc, 0xFFFFFFFF);
  }
}
