/*
 * Function: ?Insert_UnmannedTraderItemStateRecord@CRFWorldDatabase@@QEAA_NKPEAPEA_W@Z
 * Address: 0x1404AB4A0
 */

char __fastcall CRFWorldDatabase::Insert_UnmannedTraderItemStateRecord(CRFWorldDatabase *this, unsigned int dwRowCnt, wchar_t **ppwszStr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-858h]@1
  wchar_t _String; // [sp+30h] [bp-828h]@8
  int v8; // [sp+834h] [bp-24h]@6
  unsigned int j; // [sp+838h] [bp-20h]@6
  unsigned __int64 v10; // [sp+848h] [bp-10h]@4
  CRFWorldDatabase *v11; // [sp+860h] [bp+8h]@1
  unsigned int v12; // [sp+868h] [bp+10h]@1
  wchar_t **v13; // [sp+870h] [bp+18h]@1

  v13 = ppwszStr;
  v12 = dwRowCnt;
  v11 = this;
  v3 = &v6;
  for ( i = 532i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( dwRowCnt )
  {
    v8 = 0;
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 0);
    for ( j = 0; j < v12; ++j )
    {
      swprintf(&_String, L"insert into [dbo].[tbl_utresultstateid] ([id], [desc]) values ( %u, N'%s' )", j, v13[j]);
      if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &_String, 1) )
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v11->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
        return 0;
      }
    }
    CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v11->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v11->vfptr, 1);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
