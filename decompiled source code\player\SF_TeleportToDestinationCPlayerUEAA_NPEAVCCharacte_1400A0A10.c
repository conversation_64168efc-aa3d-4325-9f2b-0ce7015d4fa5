/*
 * Function: ?SF_TeleportToDestination@CPlayer@@UEAA_NPEAVCCharacter@@_N@Z
 * Address: 0x1400A0A10
 */

char __fastcall CPlayer::SF_TeleportToDestination(CPlayer *this, CCharacter *pDstObj, bool bStone)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CNormalGuildBattleManager *v6; // rax@16
  GUILD_BATTLE::CNormalGuildBattle *v7; // rax@16
  GUILD_BATTLE::CNormalGuildBattleField *v8; // rax@16
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // rax@16
  GUILD_BATTLE::CNormalGuildBattle *v10; // rax@16
  GUILD_BATTLE::CNormalGuildBattleField *v11; // rax@16
  int v12; // eax@36
  int v13; // eax@39
  CGuildRoomSystem *v14; // rax@47
  CMapData *v15; // rax@47
  unsigned int v16; // eax@49
  __int64 v17; // [sp+0h] [bp-A8h]@1
  float *pfStartPos; // [sp+20h] [bp-88h]@53
  char v19; // [sp+30h] [bp-78h]@4
  CMapData *v20; // [sp+38h] [bp-70h]@16
  CMapData *v21; // [sp+40h] [bp-68h]@16
  int v22; // [sp+48h] [bp-60h]@36
  int v23; // [sp+4Ch] [bp-5Ch]@38
  CCharacter *v24; // [sp+50h] [bp-58h]@44
  int j; // [sp+58h] [bp-50h]@45
  int v26; // [sp+5Ch] [bp-4Ch]@45
  CMapData *pIntoMap; // [sp+60h] [bp-48h]@53
  float pNewPos; // [sp+78h] [bp-30h]@53
  unsigned int dwGuildSerial; // [sp+94h] [bp-14h]@16
  unsigned int v30; // [sp+98h] [bp-10h]@16
  unsigned int v31; // [sp+9Ch] [bp-Ch]@49
  CPlayer *pApplyPlayer; // [sp+B0h] [bp+8h]@1
  CCharacter *v33; // [sp+B8h] [bp+10h]@1
  bool v34; // [sp+C0h] [bp+18h]@1

  v34 = bStone;
  v33 = pDstObj;
  pApplyPlayer = this;
  v3 = &v17;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v19 = 0;
  if ( pDstObj->m_ObjID.m_byID )
    return 0;
  if ( pApplyPlayer->m_bInGuildBattle || BYTE2(pDstObj[1].m_fCurPos[2]) )
  {
    if ( pApplyPlayer->m_bInGuildBattle && !BYTE2(pDstObj[1].m_fCurPos[2]) )
    {
      v19 = 10;
      goto $RESULT_22;
    }
    if ( !pApplyPlayer->m_bInGuildBattle || BYTE2(pDstObj[1].m_fCurPos[2]) )
    {
      v19 = 9;
      goto $RESULT_22;
    }
    if ( pApplyPlayer->m_bInGuildBattle && BYTE2(pDstObj[1].m_fCurPos[2]) )
    {
      dwGuildSerial = CPlayerDB::GetGuildSerial(&pApplyPlayer->m_Param);
      v6 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
      v7 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v6, dwGuildSerial);
      v8 = GUILD_BATTLE::CNormalGuildBattle::GetField(v7);
      v20 = GUILD_BATTLE::CNormalGuildBattleField::GetMap(v8);
      v30 = CPlayerDB::GetGuildSerial((CPlayerDB *)&v33[1].m_fOldPos[2]);
      v9 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
      v10 = GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(v9, v30);
      v11 = GUILD_BATTLE::CNormalGuildBattle::GetField(v10);
      v21 = GUILD_BATTLE::CNormalGuildBattleField::GetMap(v11);
      if ( v20 != v21 )
      {
        v19 = 9;
        goto $RESULT_22;
      }
      if ( pApplyPlayer->m_bTakeGravityStone )
      {
        v19 = 5;
        goto $RESULT_22;
      }
    }
  }
  if ( CGameObject::GetCurSecNum((CGameObject *)&v33->vfptr) == -1 || v33->m_bMapLoading )
  {
    v19 = 7;
    goto $RESULT_22;
  }
  if ( !*(_QWORD *)&v33[25].m_SFCont[0][5].m_byLv && !pApplyPlayer->m_pDHChannel )
    goto LABEL_36;
  if ( pApplyPlayer->m_pDHChannel && *(_QWORD *)&v33[25].m_SFCont[0][5].m_byLv )
  {
    if ( *(CDarkHoleChannel **)&v33[25].m_SFCont[0][5].m_byLv != pApplyPlayer->m_pDHChannel )
    {
      v19 = 6;
      goto $RESULT_22;
    }
LABEL_36:
    v22 = CMapData::GetLevelLimit(v33->m_pCurMap);
    v12 = ((int (__fastcall *)(CPlayer *))pApplyPlayer->vfptr->GetLevel)(pApplyPlayer);
    if ( v12 >= v22 )
    {
      v23 = v33->m_pCurMap->m_pMapSet->m_nUpLevelLim;
      if ( v23 == -1 || (v13 = ((int (__fastcall *)(CPlayer *))pApplyPlayer->vfptr->GetLevel)(pApplyPlayer), v13 <= v23) )
      {
        if ( CHolyStoneSystem::GetDestroyerState(&g_HolySys) != 2
          || CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) != pApplyPlayer->m_dwObjSerial )
        {
          v24 = v33;
          if ( *(_QWORD *)&v33[23].m_SFCont[0][0].m_wszPlayerName[16] )
          {
            v26 = CPlayerDB::GetRaceCode((CPlayerDB *)&v24[1].m_fOldPos[2]);
            for ( j = 0; j < 2; ++j )
            {
              v14 = CGuildRoomSystem::GetInstance();
              v15 = CGuildRoomSystem::GetMapData(v14, v26, j);
              if ( v15 == v24->m_pCurMap )
              {
                if ( !pApplyPlayer->m_Param.m_pGuild
                  || (v31 = CPlayerDB::GetGuildSerial(&pApplyPlayer->m_Param),
                      v16 = CPlayerDB::GetGuildSerial((CPlayerDB *)&v24[1].m_fOldPos[2]),
                      v31 != v16) )
                {
                  v19 = 11;
                }
                goto $RESULT_22;
              }
            }
          }
        }
        else
        {
          v19 = 4;
        }
      }
      else
      {
        v19 = 2;
      }
    }
    else
    {
      v19 = 2;
    }
    goto $RESULT_22;
  }
  if ( pApplyPlayer->m_pDHChannel || !*(_QWORD *)&v33[25].m_SFCont[0][5].m_byLv )
  {
    if ( !pApplyPlayer->m_pDHChannel || *(_QWORD *)&v33[25].m_SFCont[0][5].m_byLv )
      goto LABEL_36;
    v19 = 8;
  }
  else
  {
    v19 = 6;
  }
$RESULT_22:
  if ( v19 )
  {
    CPlayer::SendMsg_TeleportError(pApplyPlayer, v19, v33->m_pCurMap->m_pMapSet->m_dwIndex);
    result = 0;
  }
  else
  {
    pIntoMap = 0i64;
    pIntoMap = v33->m_pCurMap;
    CMapData::GetRandPosInRange(pIntoMap, v33->m_fCurPos, 30, &pNewPos);
    pfStartPos = &pNewPos;
    CPlayer::OutOfMap(pApplyPlayer, pIntoMap, v33->m_wMapLayerIndex, 3, &pNewPos);
    CPlayer::SendMsg_MovePortal(pApplyPlayer, pIntoMap->m_pMapSet->m_dwIndex, &pNewPos, 2);
    if ( v34 )
    {
      CPotionMgr::InsertMovePotionStoneEffect(&g_PotionMgr, pApplyPlayer);
      CPlayer::SenseState(pApplyPlayer);
      CPlayer::SendMsg_NewMovePotionResult(pApplyPlayer);
    }
    result = 1;
  }
  return result;
}
