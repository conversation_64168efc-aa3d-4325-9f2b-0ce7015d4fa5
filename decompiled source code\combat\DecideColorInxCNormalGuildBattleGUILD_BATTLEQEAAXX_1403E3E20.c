/*
 * Function: ?DecideColorInx@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E3E20
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::DecideColorInx(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // kr00_8@4
  __int64 v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+24h] [bp-24h]@4
  char v6; // [sp+25h] [bp-23h]@4
  char v7; // [sp+26h] [bp-22h]@4
  unsigned __int8 v8; // [sp+34h] [bp-14h]@4
  char v9; // [sp+35h] [bp-13h]@4
  char v10; // [sp+36h] [bp-12h]@4
  GUILD_BATTLE::CNormalGuildBattle *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  v6 = 1;
  v7 = 0;
  v3 = rand();
  v8 = (BYTE4(v3) ^ v3 & 1) - BYTE4(v3);
  v9 = *(&v5 + v8);
  v10 = *(&v5 + v8 + 1);
  GUILD_BATTLE::CNormalGuildBattleGuild::SetColorInx(&v11->m_k1P, v9);
  GUILD_BATTLE::CNormalGuildBattleGuild::SetColorInx(&v11->m_k2P, v10);
  if ( v9 )
  {
    v11->m_pkRed = &v11->m_k2P;
    v11->m_pkBlue = &v11->m_k1P;
  }
  else
  {
    v11->m_pkRed = &v11->m_k1P;
    v11->m_pkBlue = &v11->m_k2P;
  }
}
