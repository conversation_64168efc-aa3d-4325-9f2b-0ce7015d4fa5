/*
 * Function: ?ProcessLastBlock@CBC_CTS_Encryption@CryptoPP@@UEAAXPEAEPEBE_K@Z
 * Address: 0x140619290
 */

void __fastcall CryptoPP::CBC_CTS_Encryption::ProcessLastBlock(CryptoPP::CBC_CTS_Encryption *this, unsigned __int8 *a2, const unsigned __int8 *a3, unsigned __int64 a4)
{
  char *v4; // rax@6
  unsigned __int64 v5; // r9@6
  unsigned __int8 *v6; // rax@6
  char *v7; // rax@7
  unsigned __int64 v8; // r9@7
  unsigned __int8 *v9; // rax@7
  CryptoPP::InvalidArgument v10; // [sp+20h] [bp-C8h]@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-78h]@3
  unsigned __int8 v12; // [sp+A0h] [bp-48h]@3
  __int64 v13; // [sp+A8h] [bp-40h]@1
  unsigned __int8 *v14; // [sp+B0h] [bp-38h]@6
  void *v15; // [sp+B8h] [bp-30h]@6
  unsigned __int64 v16; // [sp+C0h] [bp-28h]@7
  CryptoPP::CBC_CTS_Encryption *v17; // [sp+F0h] [bp+8h]@1
  unsigned __int8 *v18; // [sp+F8h] [bp+10h]@1
  const unsigned __int8 *v19; // [sp+100h] [bp+18h]@1
  unsigned __int8 *v20; // [sp+108h] [bp+20h]@1

  v20 = (unsigned __int8 *)a4;
  v19 = a3;
  v18 = a2;
  v17 = this;
  v13 = -2i64;
  if ( a4 > CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&this->vfptr) )
  {
    v14 = (unsigned __int8 *)CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v17->vfptr);
    v4 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v17->m_register);
    CryptoPP::xorbuf((CryptoPP *)v4, (unsigned __int8 *)v19, v14, v5);
    v6 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v17->m_register);
    CryptoPP::BlockTransformation::ProcessBlock((CryptoPP::BlockTransformation *)&v17->m_cipher->vfptr, v6);
    v19 += CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v17->vfptr);
    v20 -= CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v17->vfptr);
    v15 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v17->m_register);
    qmemcpy(
      &v18[CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v17->vfptr)],
      v15,
      (unsigned __int64)v20);
  }
  else
  {
    if ( !v17->m_stolenIV )
    {
      memset(&v12, 0, sizeof(v12));
      std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
        &s,
        "CBC_Encryption: message is too short for ciphertext stealing",
        v12);
      CryptoPP::InvalidArgument::InvalidArgument(&v10, &s);
      CxxThrowException_0((__int64)&v10, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
    }
    qmemcpy(
      v18,
      CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v17->m_register),
      (unsigned __int64)v20);
    v18 = (unsigned __int8 *)v17->m_stolenIV;
  }
  v7 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v17->m_register);
  CryptoPP::xorbuf((CryptoPP *)v7, (unsigned __int8 *)v19, v20, v8);
  v9 = (unsigned __int8 *)CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v17->m_register);
  CryptoPP::BlockTransformation::ProcessBlock((CryptoPP::BlockTransformation *)&v17->m_cipher->vfptr, v9);
  v16 = CryptoPP::CipherModeBase::BlockSize((CryptoPP::CipherModeBase *)&v17->vfptr);
  qmemcpy(
    v18,
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v17->m_register),
    v16);
}
