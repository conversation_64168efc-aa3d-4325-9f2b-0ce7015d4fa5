/*
 * Function: j_??$_Uninit_copy@V?$_Vector_const_iterator@KV?$allocator@K@std@@@std@@PEAKV?$allocator@K@2@@std@@YAPEAKV?$_Vector_const_iterator@KV?$allocator@K@std@@@0@0PEAKAEAV?$allocator@K@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400065E6
 */

unsigned int *__fastcall std::_Uninit_copy<std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>,unsigned long *,std::allocator<unsigned long>>(std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_First, std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *_Last, unsigned int *_Dest, std::allocator<unsigned long> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>,unsigned long *,std::allocator<unsigned long>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
