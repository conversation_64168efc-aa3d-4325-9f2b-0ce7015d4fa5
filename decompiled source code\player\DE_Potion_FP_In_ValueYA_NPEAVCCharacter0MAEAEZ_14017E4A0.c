/*
 * Function: ?DE_Potion_FP_In_Value@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017E4A0
 */

bool __fastcall DE_Potion_FP_In_Value(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v7; // eax@9
  float v8; // xmm0_4@9
  __int64 v9; // [sp+0h] [bp-38h]@1
  CPlayer *v10; // [sp+20h] [bp-18h]@9
  int nFP; // [sp+28h] [bp-10h]@9
  float v12; // [sp+2Ch] [bp-Ch]@9
  CCharacter *v13; // [sp+40h] [bp+8h]@1

  v13 = pActChar;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pTargetChar )
  {
    if ( v13->m_ObjID.m_byID || pTargetChar->m_ObjID.m_byID )
    {
      result = 0;
    }
    else
    {
      v10 = (CPlayer *)pTargetChar;
      v7 = CPlayer::GetFP((CPlayer *)pTargetChar);
      v8 = (float)v7;
      v12 = (float)v7;
      _effect_parameter::GetEff_Rate(&v10->m_EP, 19);
      nFP = (signed int)ffloor(v12 + (float)(fEffectValue * v8));
      result = CPlayer::SetFP(v10, nFP, 0) != 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
