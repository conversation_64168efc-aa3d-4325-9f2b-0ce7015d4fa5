/*
 * Function: ?SearchNearPlayer@CMonsterHelper@@SAPEAVCPlayer@@PEAVCMonster@@H@Z
 * Address: 0x140158540
 */

CPlayer *__usercall CMonsterHelper::SearchNearPlayer@<rax>(CMonster *pMon@<rcx>, int nType@<edx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float v5; // xmm0_4@11
  int v6; // eax@11
  _sec_info *v7; // rax@16
  __int64 v8; // rdx@20
  int v9; // eax@27
  __int64 v11; // [sp+0h] [bp-1D8h]@1
  CMapData *v12; // [sp+30h] [bp-1A8h]@8
  float v13; // [sp+38h] [bp-1A0h]@11
  int nRadius; // [sp+3Ch] [bp-19Ch]@11
  float v15; // [sp+40h] [bp-198h]@11
  _pnt_rect pRect; // [sp+58h] [bp-180h]@11
  int __t[2]; // [sp+90h] [bp-148h]@11
  __int64 v18[15]; // [sp+98h] [bp-140h]@54
  int j; // [sp+114h] [bp-C4h]@11
  int k; // [sp+118h] [bp-C0h]@13
  unsigned int dwSecIndex; // [sp+11Ch] [bp-BCh]@16
  CObjectList *v22; // [sp+120h] [bp-B8h]@16
  _object_list_point *v23; // [sp+128h] [bp-B0h]@17
  CCharacter *v24; // [sp+130h] [bp-A8h]@19
  char *v25; // [sp+138h] [bp-A0h]@19
  float v26; // [sp+144h] [bp-94h]@28
  CCharacter *v27; // [sp+158h] [bp-80h]@30
  int l; // [sp+160h] [bp-78h]@56
  int v29; // [sp+164h] [bp-74h]@59
  __int64 v30; // [sp+168h] [bp-70h]@66
  float v31[3]; // [sp+178h] [bp-60h]@69
  int m; // [sp+194h] [bp-44h]@66
  float v33; // [sp+198h] [bp-40h]@9
  _monster_fld *v34; // [sp+1A0h] [bp-38h]@27
  CGameObjectVtbl *v35; // [sp+1A8h] [bp-30h]@27
  float *chkpos; // [sp+1B0h] [bp-28h]@29
  int v37; // [sp+1B8h] [bp-20h]@30
  void *Dst; // [sp+1C0h] [bp-18h]@60
  CMonster *pMona; // [sp+1E0h] [bp+8h]@1
  int v40; // [sp+1E8h] [bp+10h]@1

  v40 = nType;
  pMona = pMon;
  v3 = &v11;
  for ( i = 116i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( nType < 0 )
    v40 = 0;
  if ( v40 >= 11 )
    v40 = 0;
  v12 = pMona->m_pCurMap;
  CMonster::GetVisualField(pMona);
  if ( a3 >= 300.0 )
  {
    v33 = FLOAT_300_0;
  }
  else
  {
    CMonster::GetVisualField(pMona);
    v33 = a3;
  }
  v13 = v33;
  v5 = (float)(v33 / 100.0) + 0.5;
  nRadius = (signed int)ffloor(v5);
  CMonster::GetVisualAngle(pMona);
  v15 = v5;
  `vector constructor iterator'(__t, 0x18ui64, 5, (void *(__cdecl *)(void *))_NEAR_DATA::_NEAR_DATA);
  v6 = CGameObject::GetCurSecNum((CGameObject *)&pMona->vfptr);
  CMapData::GetRectInRadius(v12, &pRect, nRadius, v6);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      v7 = CMapData::GetSecInfo(v12);
      dwSecIndex = v7->m_nSecNumW * j + k;
      v22 = CMapData::GetSectorListPlayer(v12, pMona->m_wMapLayerIndex, dwSecIndex);
      if ( v22 )
      {
        v23 = v22->m_Head.m_pNext;
LABEL_18:
        while ( v23 != &v22->m_Tail )
        {
          v24 = (CCharacter *)v23->m_pItem;
          v23 = v23->m_pNext;
          v25 = &v24->m_ObjID.m_byKind;
          if ( !v24->m_bCorpse
            && !CCharacter::GetStealth(v24, 1)
            && !v24->m_ObjID.m_byID
            && !v24->m_ObjID.m_byKind
            && !BYTE2(v24[1].m_fCurPos[2]) )
          {
            LOBYTE(v8) = 1;
            if ( (unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v24->vfptr->IsBeAttackedAble)(v24, v8) )
            {
              v34 = pMona->m_pMonRec;
              v35 = v24->vfptr;
              v9 = ((int (__fastcall *)(CCharacter *))v35->GetObjRace)(v24);
              if ( v34->m_nRaceCode != v9 )
              {
                v26 = 0.0;
                if ( CMonsterHelper::CheckPreAttackRangeTargetAbleCharacter(pMona, (CGameObject *)&v24->vfptr)
                  || (chkpos = v24->m_fCurPos,
                      CMonsterHelper::IsInSector(v24->m_fCurPos, pMona->m_fCurPos, pMona->m_fLookAtPos, v15, v13, &v26)) )
                {
                  v27 = v24;
                  v37 = v40;
                  switch ( v40 )
                  {
                    case 1:
                      if ( v27[23].m_SFCont[0][3].m_wszPlayerName[14] )
                        v26 = v26 - 100.0;
                      else
                        v24 = 0i64;
                      break;
                    case 2:
                      if ( v27[23].m_SFCont[0][3].m_wszPlayerName[15] == 1 )
                        v26 = v26 - 100.0;
                      else
                        v24 = 0i64;
                      break;
                    case 3:
                      v26 = (float)((int (__fastcall *)(CCharacter *))v27->vfptr->GetHP)(v27);
                      break;
                    case 4:
                      v26 = (float)-((int (__fastcall *)(CCharacter *))v27->vfptr->GetHP)(v27);
                      break;
                    case 5:
                      v26 = -0.0 - (float)(10 * *(_DWORD *)&v27[23].m_SFCont[1][2].m_wszPlayerName[8]);
                      break;
                    case 6:
                      v26 = -0.0 - (float)((int (__fastcall *)(CCharacter *))v27->vfptr->GetLevel)(v27);
                      break;
                    case 7:
                      v26 = (float)((int (__fastcall *)(CCharacter *))v27->vfptr->GetLevel)(v27);
                      break;
                    case 8:
                      if ( ((int (__fastcall *)(CCharacter *))v27->vfptr->GetObjRace)(v27) )
                        v24 = 0i64;
                      break;
                    case 9:
                      if ( ((int (__fastcall *)(CCharacter *))v27->vfptr->GetObjRace)(v27) != 1 )
                        v24 = 0i64;
                      break;
                    case 10:
                      if ( ((int (__fastcall *)(CCharacter *))v27->vfptr->GetObjRace)(v27) != 2 )
                        v24 = 0i64;
                      break;
                    case 0:
                      break;
                  }
                  if ( v24 )
                  {
                    if ( v18[0] )
                    {
                      for ( l = 0; l < 5; ++l )
                      {
                        if ( *(float *)&__t[6 * l] > v26 )
                        {
                          v29 = 4 - l;
                          if ( 4 - l > 0 )
                          {
                            Dst = &__t[6 * (l + 1)];
                            memcpy_0(&__t[6 * (l + 1)], &__t[6 * l], 24i64 * v29);
                          }
                          *(float *)&__t[6 * l] = v26;
                          v18[3 * l] = (__int64)v24;
                          goto LABEL_18;
                        }
                      }
                    }
                    else
                    {
                      *(float *)__t = v26;
                      v18[0] = (__int64)v24;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  v30 = 0i64;
  for ( m = 0; m < 5 && v18[3 * m]; ++m )
  {
    if ( (unsigned int)CBsp::CanYouGoThere(
                         v12->m_Level.mBsp,
                         pMona->m_fCurPos,
                         (float *const )(v18[3 * m] + 40),
                         (float (*)[3])v31) )
    {
      v30 = v18[3 * m];
      break;
    }
  }
  if ( v30 )
    pMona->m_LifeCicle = GetLoopTime();
  return (CPlayer *)v30;
}
