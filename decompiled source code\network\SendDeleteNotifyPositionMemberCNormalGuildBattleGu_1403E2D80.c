/*
 * Function: ?SendDeleteNotifyPositionMember@CNormalGuildBattleGuild@GUILD_BATTLE@@IEAAXH@Z
 * Address: 0x1403E2D80
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendDeleteNotifyPositionMember(GUILD_BATTLE::CNormalGuildBattleGuild *this, int iMemberInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  CPlayer *v5; // [sp+30h] [bp-58h]@4
  char pMsg[4]; // [sp+44h] [bp-44h]@5
  char byType; // [sp+64h] [bp-24h]@5
  char v8; // [sp+65h] [bp-23h]@5
  GUILD_BATTLE::CNormalGuildBattleGuild *v9; // [sp+90h] [bp+8h]@1
  int v10; // [sp+98h] [bp+10h]@1

  v10 = iMemberInx;
  v9 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v9->m_kMember[iMemberInx]);
  if ( v5 )
  {
    *(_DWORD *)pMsg = v5->m_dwObjSerial;
    byType = 27;
    v8 = 83;
    GUILD_BATTLE::CNormalGuildBattleGuild::SendMsg(v9, &byType, pMsg, 4u, v10);
  }
}
