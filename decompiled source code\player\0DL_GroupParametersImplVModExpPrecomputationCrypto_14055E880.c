/*
 * Function: ??0?$DL_GroupParametersImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14055E880
 */

CryptoPP::DL_GroupParameters_IntegerBased *__fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>(__int64 a1, int a2)
{
  CryptoPP::DL_GroupParameters_IntegerBased *v3; // [sp+40h] [bp+8h]@1

  v3 = (CryptoPP::DL_GroupParameters_IntegerBased *)a1;
  if ( a2 )
  {
    *(_QWORD *)(a1 + 16) = &CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vbtable';
    CryptoPP::CryptoMaterial::CryptoMaterial((CryptoPP::CryptoMaterial *)(a1 + 232));
  }
  CryptoPP::DL_GroupParameters_IntegerBased::DL_GroupParameters_IntegerBased(v3);
  v3->vfptr = (CryptoPP::ASN1ObjectVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable';
  v3->vfptr = (CryptoPP::GeneratableCryptoMaterialVtbl *)&CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable'{for `CryptoPP::GeneratableCryptoMaterial'};
  *(_QWORD *)&v3->gap8[*(_DWORD *)(*(_QWORD *)&v3->gap8[0] + 4i64)] = &CryptoPP::DL_GroupParametersImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>,CryptoPP::DL_GroupParameters_IntegerBased>::`vftable'{for `CryptoPP::CryptoMaterial'};
  *(_DWORD *)((char *)&v3->vfptr + *(_DWORD *)(*(_QWORD *)&v3->gap8[0] + 4i64) + 4) = 0;
  CryptoPP::ModExpPrecomputation::ModExpPrecomputation((CryptoPP::ModExpPrecomputation *)v3->gap48);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(&v3[1]);
  return v3;
}
