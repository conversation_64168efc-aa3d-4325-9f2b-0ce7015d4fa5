/*
 * Function: ?Update_UnitData@CUserDB@@QEAA_NEPEAU_LIST@_UNIT_DB_BASE@@@Z
 * Address: 0x140115440
 */

char __fastcall CUserDB::Update_UnitData(CUserDB *this, char bySlotIndex, _UNIT_DB_BASE::_LIST *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)bySlotIndex < 4 )
  {
    if ( v7->m_AvatorData.dbUnit.m_List[(unsigned __int8)bySlotIndex].byFrame == 255 )
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_UnitData(EXIST) : slot : %d",
        v7->m_aszAvatorName,
        (unsigned __int8)bySlotIndex);
      result = 0;
    }
    else
    {
      memcpy_0((char *)&v7->m_AvatorData.dbUnit + 62 * (unsigned __int8)bySlotIndex, pData, 0x3Eui64);
      v7->m_bDataUpdate = 1;
      result = 1;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_UnitData(SlotIndex OVER) : slot : %d",
      v7->m_aszAvatorName,
      (unsigned __int8)bySlotIndex);
    result = 0;
  }
  return result;
}
