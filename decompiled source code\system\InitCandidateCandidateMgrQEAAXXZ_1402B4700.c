/*
 * Function: ?InitCandidate@CandidateMgr@@QEAAXXZ
 * Address: 0x1402B4700
 */

void __fastcall CandidateMgr::InitCandidate(CandidateMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@6
  int l; // [sp+28h] [bp-10h]@9
  int m; // [sp+2Ch] [bp-Ch]@12
  CandidateMgr *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    v8->_nCandidateCnt_1st[j] = 0;
    v8->_nCandidateCnt_2st[j] = 0;
    for ( k = 0; k < 500; ++k )
    {
      v8->_pkCandidateLink_1st[j][k] = 0i64;
      _candidate_info::_Init(&v8->_kCandidate[j][k]);
    }
    for ( l = 0; l < 8; ++l )
      v8->_pkCandidateLink_2st[(signed __int64)j][l] = 0i64;
    for ( m = 0; m < 9; ++m )
      v8->_pkLeader[j][m] = 0i64;
  }
}
