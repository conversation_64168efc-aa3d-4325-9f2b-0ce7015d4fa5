/*
 * Function: ?SendMsg_Init_Action_Point@CPlayer@@QEAAXXZ
 * Address: 0x1400E8F00
 */

void __fastcall CPlayer::SendMsg_Init_Action_Point(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-88h]@1
  _Init_action_point_zocl v4; // [sp+38h] [bp-50h]@4
  int j; // [sp+54h] [bp-34h]@4
  char pbyType; // [sp+64h] [bp-24h]@7
  char v7; // [sp+65h] [bp-23h]@7
  CPlayer *v8; // [sp+90h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _Init_action_point_zocl::_Init_action_point_zocl(&v4);
  for ( j = 0; j < 3; ++j )
    v4.dwActionPoint[j] = v8->m_pUserDB->m_AvatorData.dbSupplement.dwActionPoint[j];
  pbyType = 11;
  v7 = 37;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, (char *)&v4, 0xCu);
}
