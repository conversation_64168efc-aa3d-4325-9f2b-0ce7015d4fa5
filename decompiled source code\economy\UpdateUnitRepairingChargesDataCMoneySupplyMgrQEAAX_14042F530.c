/*
 * Function: ?UpdateUnitRepairingChargesData@CMoneySupplyMgr@@QEAAXHK@Z
 * Address: 0x14042F530
 */

void __fastcall CMoneySupplyMgr::UpdateUnitRepairingChargesData(CMoneySupplyMgr *this, int nLv, unsigned int nAmount)
{
  this->m_MS_data.dwAmount[8] += nAmount;
  switch ( nLv )
  {
    case 30:
      ++this->m_MS_data.nUnitRepairLv[0];
      break;
    case 40:
      ++this->m_MS_data.nUnitRepairLv[1];
      break;
    case 50:
      ++this->m_MS_data.nUnitRepairLv[2];
      break;
    case 60:
      ++this->m_MS_data.nUnitRepairLv[3];
      break;
  }
}
