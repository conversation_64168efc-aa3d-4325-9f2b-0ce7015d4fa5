/*
 * Function: ?Goal@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAVCNormalGuildBattleGuildMember@2@@Z
 * Address: 0x1403E1600
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Goal(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleGuildMember *pkMember)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleGuild *v5; // [sp+30h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v6; // [sp+38h] [bp+10h]@1

  v6 = pkMember;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ++v5->m_dwGoalCnt;
  GUILD_BATTLE::CNormalGuildBattleGuild::UpdateScore(v5);
  if ( v6 )
  {
    if ( !GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEmpty(v6) )
      GUILD_BATTLE::CNormalGuildBattleGuildMember::AddGoldCnt(v6);
  }
}
