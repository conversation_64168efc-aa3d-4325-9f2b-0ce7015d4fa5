/*
 * Function: ?capacity@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_KXZ
 * Address: 0x140594960
 */

__int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::capacity(__int64 a1)
{
  __int64 v2; // [sp+0h] [bp-18h]@2

  if ( *(_QWORD *)(a1 + 16) )
    v2 = (*(_QWORD *)(a1 + 32) - *(_QWORD *)(a1 + 16)) >> 7;
  else
    v2 = 0i64;
  return v2;
}
