/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAUAreaData@@PEAU1@V?$allocator@UAreaData@@@std@@@stdext@@YAPEAUAreaData@@PEAU1@00AEAV?$allocator@UAreaData@@@std@@@Z
 * Address: 0x14000C5EA
 */

AreaData *__fastcall stdext::_Unchecked_uninitialized_move<AreaData *,AreaData *,std::allocator<AreaData>>(AreaData *_First, AreaData *_Last, AreaData *_Dest, std::allocator<AreaData> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<AreaData *,AreaData *,std::allocator<AreaData>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
