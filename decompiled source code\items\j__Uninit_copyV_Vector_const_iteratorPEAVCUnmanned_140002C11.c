/*
 * Function: j_??$_Uninit_copy@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@2@@std@@YAPEAPEAVCUnmannedTraderSubClassInfo@@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@0@0PEAPEAV1@AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140002C11
 */

CUnmannedTraderSubClassInfo **__fastcall std::_Uninit_copy<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Last, CUnmannedTraderSubClassInfo **_Dest, std::allocator<CUnmannedTraderSubClassInfo *> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
