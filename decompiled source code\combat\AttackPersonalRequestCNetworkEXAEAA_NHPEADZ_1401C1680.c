/*
 * Function: ?AttackPersonalRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C1680
 */

char __fastcall CNetworkEX::AttackPersonalRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  char *v7; // rax@11
  __int64 v8; // [sp+0h] [bp-58h]@1
  unsigned __int16 wEffBtSerial; // [sp+20h] [bp-38h]@12
  bool bCount; // [sp+28h] [bp-30h]@12
  char *v11; // [sp+30h] [bp-28h]@4
  CPlayer *v12; // [sp+38h] [bp-20h]@4
  CCharacter *pDst; // [sp+40h] [bp-18h]@8
  CNetworkEX *v14; // [sp+60h] [bp+8h]@1

  v14 = this;
  v3 = &v8;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = pBuf;
  v12 = &g_Player + n;
  if ( !v12->m_bOper || v12->m_pmTrd.bDTradeMode || v12->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)*v11, *(_WORD *)(v11 + 1));
    if ( pDst )
    {
      if ( (signed int)(unsigned __int8)v11[3] < 5 )
      {
        bCount = 0;
        wEffBtSerial = *((_WORD *)v11 + 3);
        CPlayer::pc_PlayAttack_Gen(v12, pDst, v11[3], *((_WORD *)v11 + 2), wEffBtSerial, 0);
        result = 1;
      }
      else
      {
        v7 = CPlayerDB::GetCharNameA(&v12->m_Param);
        CLogFile::Write(
          &v14->m_LogFile,
          "odd.. %s: AttackPersonalRequest()..  if(pRecv->byAttPart >= base_fix_num)",
          v7);
        result = 0;
      }
    }
    else
    {
      v6 = CPlayerDB::GetCharNameA(&v12->m_Param);
      CLogFile::Write(&v14->m_LogFile, "odd.. %s: AttackPersonalRequest()..  if(!pDst)", v6);
      result = 0;
    }
  }
  return result;
}
