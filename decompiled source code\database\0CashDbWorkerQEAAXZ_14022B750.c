/*
 * Function: ??0CashDbWorker@@QEAA@XZ
 * Address: 0x14022B750
 */

void __fastcall CashDbWorker::CashDbWorker(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __int64 v4; // [sp+30h] [bp-18h]@4
  CashDbWorker *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  Worker::Worker((Worker *)&v5->vfptr, "CashDbWorker", 2532, 2500);
  v5->vfptr = (WorkerVtbl *)&CashDbWorker::`vftable';
  `eh vector constructor iterator'(
    v5->_kLogger,
    0xB8ui64,
    2,
    (void (__cdecl *)(void *))CLogFile::CLogFile,
    (void (__cdecl *)(void *))CLogFile::~CLogFile);
  v5->_pkDb = 0i64;
}
