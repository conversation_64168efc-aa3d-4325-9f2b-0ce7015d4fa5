/*
 * Function: ??0?$deque@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@@std@@QEAA@XZ
 * Address: 0x14031F980
 */

void __fastcall std::deque<RECV_DATA,std::allocator<RECV_DATA>>::deque<RECV_DATA,std::allocator<RECV_DATA>>(std::deque<RECV_DATA,std::allocator<RECV_DATA> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<RECV_DATA> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<RECV_DATA> *v6; // [sp+28h] [bp-10h]@4
  std::deque<RECV_DATA,std::allocator<RECV_DATA> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<RECV_DATA> *)&v5;
  std::allocator<RECV_DATA>::allocator<RECV_DATA>((std::allocator<RECV_DATA> *)&v5);
  std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA>>::_Deque_val<RECV_DATA,std::allocator<RECV_DATA>>(
    (std::_Deque_val<RECV_DATA,std::allocator<RECV_DATA> > *)&v7->_Myfirstiter,
    v3);
  v7->_Map = 0i64;
  v7->_Mapsize = 0i64;
  v7->_Myoff = 0i64;
  v7->_Mysize = 0i64;
}
