/*
 * Function: ?SendMsg_UnitBulletFillResult@CPlayer@@QEAAXEEPEAGPEAK@Z
 * Address: 0x1400DB090
 */

void __fastcall CPlayer::SendMsg_UnitBulletFillResult(CPlayer *this, char byRetCode, char bySlotIndex, unsigned __int16 *pwBulletIndex, unsigned int *pdwConsumMoney)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-C8h]@1
  char szMsg; // [sp+38h] [bp-90h]@4
  char v9; // [sp+39h] [bp-8Fh]@4
  char Dst; // [sp+3Ah] [bp-8Eh]@4
  unsigned int v11; // [sp+3Eh] [bp-8Ah]@4
  unsigned int v12; // [sp+42h] [bp-86h]@4
  unsigned int v13; // [sp+5Ah] [bp-6Eh]@4
  unsigned int v14; // [sp+5Eh] [bp-6Ah]@4
  char pbyType; // [sp+94h] [bp-34h]@4
  char v16; // [sp+95h] [bp-33h]@4
  unsigned __int64 v17; // [sp+B0h] [bp-18h]@4
  CPlayer *v18; // [sp+D0h] [bp+8h]@1

  v18 = this;
  v5 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = (unsigned __int64)&v7 ^ _security_cookie;
  szMsg = byRetCode;
  v9 = bySlotIndex;
  memcpy_0(&Dst, pwBulletIndex, 2ui64);
  v11 = *pdwConsumMoney;
  v12 = pdwConsumMoney[1];
  v13 = CPlayerDB::GetDalant(&v18->m_Param);
  v14 = CPlayerDB::GetGold(&v18->m_Param);
  pbyType = 23;
  v16 = 10;
  CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x3Eu);
}
