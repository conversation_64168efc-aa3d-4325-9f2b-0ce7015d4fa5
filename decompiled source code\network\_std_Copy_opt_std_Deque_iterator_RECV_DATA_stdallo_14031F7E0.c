/*
 * Function: _std::_Copy_opt_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::random_access_iterator_tag__::_1_::dtor$1
 * Address: 0x14031F7E0
 */

void __fastcall std::_Copy_opt_std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::_Deque_iterator_RECV_DATA_std::allocator_RECV_DATA__0__std::random_access_iterator_tag__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>::~_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>(*(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> **)(a2 + 128));
}
