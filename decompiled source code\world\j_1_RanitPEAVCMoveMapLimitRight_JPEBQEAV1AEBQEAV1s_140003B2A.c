/*
 * Function: j_??1?$_Ranit@PEAVCMoveMapLimitRight@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140003B2A
 */

void __fastcall std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::~_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>(std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *this)
{
  std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::~_<PERSON>t<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>(this);
}
