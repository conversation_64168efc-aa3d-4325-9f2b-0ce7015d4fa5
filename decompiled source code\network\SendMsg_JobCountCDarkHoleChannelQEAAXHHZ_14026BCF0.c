/*
 * Function: ?SendMsg_JobCount@CDarkHoleChannel@@QEAAXHH@Z
 * Address: 0x14026BCF0
 */

void __fastcall CDarkHoleChannel::SendMsg_JobCount(CDarkHoleChannel *this, int nJobIndex, int nCount)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@7
  __int64 v6; // [sp+0h] [bp-78h]@1
  _darkhole_job_count_inform_zocl v7; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  _dh_player_mgr *v11; // [sp+68h] [bp-10h]@6
  CDarkHoleChannel *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.byJobIndex = nJobIndex;
  v7.wJobCount = nCount;
  pbyType = 35;
  v9 = 6;
  for ( j = 0; j < 32; ++j )
  {
    v11 = &v12->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v11) )
    {
      v5 = _darkhole_job_count_inform_zocl::size(&v7);
      CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_Quester[j].pOne->m_ObjID.m_wIndex, &pbyType, &v7.byJobIndex, v5);
    }
  }
}
