/*
 * Function: ?Accept_Server@CNetSocket@@QEAAKXZ
 * Address: 0x14047E3B0
 */

signed __int64 __fastcall CNetSocket::Accept_Server(CNetSocket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  unsigned int *v4; // rcx@12
  unsigned int *v5; // rcx@12
  __int64 v6; // [sp+0h] [bp-C8h]@1
  struct sockaddr addr; // [sp+28h] [bp-A0h]@4
  int addrlen; // [sp+54h] [bp-74h]@4
  SOCKET s; // [sp+68h] [bp-60h]@4
  unsigned int v10; // [sp+70h] [bp-58h]@6
  _socket *v11; // [sp+78h] [bp-50h]@8
  char v12; // [sp+80h] [bp-48h]@9
  int v13; // [sp+84h] [bp-44h]@9
  int j; // [sp+88h] [bp-40h]@9
  _IP_CHECK_NODE *v15; // [sp+90h] [bp-38h]@11
  CNetIndexList *v16; // [sp+A0h] [bp-28h]@12
  CNetIndexList *v17; // [sp+A8h] [bp-20h]@12
  unsigned __int64 v18; // [sp+B0h] [bp-18h]@4
  CNetSocket *v19; // [sp+D0h] [bp+8h]@1

  v19 = this;
  v1 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  addrlen = 16;
  s = accept(v19->m_sAccept, &addr, &addrlen);
  if ( s == -1i64 )
  {
    result = 0xFFFFFFFFi64;
  }
  else
  {
    v10 = CNetSocket::FindEmptySocket(v19);
    if ( v10 == -1 )
    {
      closesocket(s);
      result = 0xFFFFFFFFi64;
    }
    else
    {
      v11 = &v19->m_Socket[v10];
      v11->m_Socket = s;
      memcpy_0(&v11->m_Addr, &addr, 0x10ui64);
      if ( !v19->m_SockType.m_bAcceptIPCheck )
        goto LABEL_23;
      v12 = 0;
      v13 = CNetIndexList::CopyIndexList(&v19->m_listIPCheck, v19->m_dwIPCheckBufferList, v19->m_nIPCheckNodeNum);
      for ( j = 0; j < v13; ++j )
      {
        v15 = &v19->m_ndIPCheck[v19->m_dwIPCheckBufferList[j]];
        if ( v15->dwIP == v11->m_Addr.sin_addr.S_un.S_addr )
        {
          v4 = v19->m_dwIPCheckBufferList;
          v16 = &v19->m_listIPCheck;
          CNetIndexList::FindNode(&v19->m_listIPCheck, v4[j]);
          v5 = v19->m_dwIPCheckBufferList;
          v17 = &v19->m_listIPCheck_Empty;
          CNetIndexList::PushNode_Back(&v19->m_listIPCheck_Empty, v5[j]);
          v12 = 1;
          break;
        }
      }
      if ( v12 )
      {
LABEL_23:
        if ( WSAEventSelect(v11->m_Socket, v19->m_Event[v10], 33) == -1 )
        {
          closesocket(v11->m_Socket);
          result = 0xFFFFFFFFi64;
        }
        else
        {
          v11->m_bAccept = 1;
          v11->m_bSendable = 1;
          v11->m_dwConnectTime = timeGetTime();
          v11->m_dwLastRecvTime = timeGetTime();
          v11->m_dwLastSendTime = v11->m_dwLastRecvTime;
          v11->m_dwSerial = v19->m_dwSerialCounter++;
          ++v19->m_TotalCount.m_dwAcceptNum;
          if ( v11->m_Addr.sin_addr.S_un.S_addr == *(_DWORD *)byLocalIP )
            GetIPAddress(&v11->m_Addr);
          result = v10;
        }
      }
      else
      {
        closesocket(v11->m_Socket);
        result = 0xFFFFFFFFi64;
      }
    }
  }
  return result;
}
