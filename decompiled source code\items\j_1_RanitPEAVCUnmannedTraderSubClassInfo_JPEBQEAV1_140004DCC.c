/*
 * Function: j_??1?$_Ranit@PEAVCUnmannedTraderSubClassInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140004DCC
 */

void __fastcall std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>::~_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>(std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &> *this)
{
  std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>::~_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>(this);
}
