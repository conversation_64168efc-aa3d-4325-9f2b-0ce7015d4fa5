/*
 * Function: ??1CashDb<PERSON><PERSON><PERSON>@@UEAA@XZ
 * Address: 0x14022B8D0
 */

void __fastcall CashDbWorker::~CashDbWorker(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CashDbWorker *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  v5->vfptr = (WorkerVtbl *)&CashDbWorker::`vftable';
  `eh vector destructor iterator'(v5->_kLogger, 0xB8ui64, 2, (void (__cdecl *)(void *))CLogFile::~CLogFile);
  Worker::~Worker((Worker *)&v5->vfptr);
}
