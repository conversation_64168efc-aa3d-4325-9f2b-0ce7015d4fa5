/*
 * Function: ?ClearAll@CUnmannedTraderScheduler@@AEAAXXZ
 * Address: 0x140351A60
 */

void __fastcall CUnmannedTraderScheduler::ClearAll(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v3; // rax@5
  CUnmannedTraderSchedule *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  unsigned __int64 v7; // [sp+28h] [bp-10h]@5
  CUnmannedTraderScheduler *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; ; ++j )
  {
    v7 = j;
    v3 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::size(&v8->m_veckSchdule);
    if ( v7 >= v3 )
      break;
    v4 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](&v8->m_veckSchdule, j);
    CUnmannedTraderSchedule::Clear(v4);
  }
}
