/*
 * Function: j_??0?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@XZ
 * Address: 0x140012DE6
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(this);
}
