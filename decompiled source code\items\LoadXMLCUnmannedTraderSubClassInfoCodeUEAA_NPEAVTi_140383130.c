/*
 * Function: ?LoadXML@CUnmannedTraderSubClassInfoCode@@UEAA_NPEAVTiXmlElement@@AEAVCLogFile@@KK@Z
 * Address: 0x140383130
 */

char __fastcall CUnmannedTraderSubClassInfoCode::LoadXML(CUnmannedTraderSubClassInfoCode *this, TiXmlElement *elemSubClass, CLogFile *kLogger, unsigned int dwDivisionID, unsigned int dwClassID)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderItemCodeInfo *v7; // rax@5
  char result; // al@5
  CUnmannedTraderItemCodeInfo *v9; // rax@10
  __int64 v10; // [sp+0h] [bp-148h]@1
  char *szCode; // [sp+20h] [bp-128h]@4
  unsigned int dwStartIndex; // [sp+34h] [bp-114h]@4
  unsigned int dwEndIndex; // [sp+54h] [bp-F4h]@4
  TiXmlNode *v14; // [sp+68h] [bp-E0h]@6
  int v15; // [sp+70h] [bp-D8h]@6
  CUnmannedTraderItemCodeInfo v16; // [sp+74h] [bp-D4h]@5
  CUnmannedTraderItemCodeInfo v17; // [sp+BCh] [bp-8Ch]@10
  __int64 v18; // [sp+108h] [bp-40h]@4
  CUnmannedTraderItemCodeInfo *v19; // [sp+110h] [bp-38h]@5
  CUnmannedTraderItemCodeInfo *_Val; // [sp+118h] [bp-30h]@5
  CUnmannedTraderItemCodeInfo *v21; // [sp+120h] [bp-28h]@10
  CUnmannedTraderItemCodeInfo *v22; // [sp+128h] [bp-20h]@10
  int v23; // [sp+130h] [bp-18h]@11
  CUnmannedTraderSubClassInfoCode *v24; // [sp+150h] [bp+8h]@1
  TiXmlElement *v25; // [sp+158h] [bp+10h]@1
  CLogFile *v26; // [sp+160h] [bp+18h]@1
  unsigned int v27; // [sp+168h] [bp+20h]@1

  v27 = dwDivisionID;
  v26 = kLogger;
  v25 = elemSubClass;
  v24 = this;
  v5 = &v10;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v18 = -2i64;
  szCode = 0i64;
  dwStartIndex = 0;
  dwEndIndex = 0;
  szCode = (char *)TiXmlElement::Attribute(elemSubClass, "code");
  if ( szCode )
  {
    TiXmlElement::Attribute(v25, "startindex", (int *)&dwStartIndex);
    TiXmlElement::Attribute(v25, "endindex", (int *)&dwEndIndex);
    CUnmannedTraderItemCodeInfo::CUnmannedTraderItemCodeInfo(&v16, szCode, dwStartIndex, dwEndIndex);
    v19 = v7;
    _Val = v7;
    std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::push_back(
      &v24->m_vecCodeList,
      v7);
    CUnmannedTraderItemCodeInfo::~CUnmannedTraderItemCodeInfo(&v16);
    result = 1;
  }
  else
  {
    v14 = (TiXmlNode *)TiXmlNode::FirstChildElement((TiXmlNode *)&v25->vfptr, "code");
    v15 = 0;
    while ( v14 )
    {
      szCode = (char *)TiXmlElement::Attribute((TiXmlElement *)v14, "code");
      if ( !szCode )
      {
        CLogFile::Write(
          v26,
          "CUnmannedTraderClassInfoTableCodeType::LoadXML( TiXmlElement * elemClass, CLogFile & kLogger, DWORD dwDivision"
          "ID )\r\n"
          "\t\tDivisionID(%u), ClassID(%u) pkElement->Attribute( code ) NULL!\r\n",
          v27,
          v24->m_dwID);
        return 0;
      }
      TiXmlElement::Attribute((TiXmlElement *)v14, "startindex", (int *)&dwStartIndex);
      TiXmlElement::Attribute((TiXmlElement *)v14, "endindex", (int *)&dwEndIndex);
      CUnmannedTraderItemCodeInfo::CUnmannedTraderItemCodeInfo(&v17, szCode, dwStartIndex, dwEndIndex);
      v21 = v9;
      v22 = v9;
      std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::push_back(
        &v24->m_vecCodeList,
        v9);
      CUnmannedTraderItemCodeInfo::~CUnmannedTraderItemCodeInfo(&v17);
      v14 = (TiXmlNode *)TiXmlNode::NextSiblingElement(v14, "code");
      ++v15;
    }
    v23 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::empty(&v24->m_vecCodeList) == 0;
    result = v23;
  }
  return result;
}
