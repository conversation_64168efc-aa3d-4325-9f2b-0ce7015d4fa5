/*
 * Function: j_?GetPrivateExponent@?$DL_PrivateKeyImpl@V?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@@CryptoPP@@UEBAAEBVInteger@2@XZ
 * Address: 0x14000844A
 */

CryptoPP::Integer *__fastcall CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::GetPrivateExponent(CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> > *this)
{
  return CryptoPP::DL_PrivateKeyImpl<CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>>::GetPrivateExponent(this);
}
