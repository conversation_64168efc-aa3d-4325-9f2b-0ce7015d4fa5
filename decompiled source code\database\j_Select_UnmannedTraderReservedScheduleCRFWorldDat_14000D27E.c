/*
 * Function: j_?Select_UnmannedTraderReservedSchedule@CRFWorldDatabase@@QEAAEKPEAU_unmannedtrader_reserved_schedule_info@@@Z
 * Address: 0x14000D27E
 */

char __fastcall CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(CRFWorldDatabase *this, unsigned int dwMaxCnt, _unmannedtrader_reserved_schedule_info *pkInfo)
{
  return CRFWorldDatabase::Select_UnmannedTraderReservedSchedule(this, dwMaxCnt, pkInfo);
}
