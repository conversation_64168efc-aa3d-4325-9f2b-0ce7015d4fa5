/*
 * Function: ?_db_Load_Trunk@CMainThread@@AEAAEKKEPEAU_TRUNK_DB_BASE@@@Z
 * Address: 0x1401A8830
 */

char __usercall CMainThread::_db_Load_Trunk@<al>(CMainThread *this@<rcx>, unsigned int dwSerial@<edx>, unsigned int dwAccountSerial@<r8d>, char byRace@<r9b>, signed __int64 a5@<rax>, _TRUNK_DB_BASE *pTrunk)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v10; // [sp-30h] [bp-1678h]@1
  char Dst; // [sp+0h] [bp-1648h]@4
  long double v12; // [sp+10h] [bp-1638h]@11
  long double v13; // [sp+18h] [bp-1630h]@11
  char v14; // [sp+20h] [bp-1628h]@11
  char Source; // [sp+21h] [bp-1627h]@11
  char v16; // [sp+32h] [bp-1616h]@11
  int pl_nKey[2]; // [sp+38h] [bp-1610h]@14
  __int64 v18; // [sp+40h] [bp-1608h]@14
  int v19; // [sp+48h] [bp-1600h]@14
  char v20[4]; // [sp+4Ch] [bp-15FCh]@14
  int v21[2]; // [sp+50h] [bp-15F8h]@14
  __int64 v22[496]; // [sp+58h] [bp-15F0h]@14
  char v23; // [sp+FD8h] [bp-670h]@24
  int v24[2]; // [sp+FE0h] [bp-668h]@27
  __int64 v25; // [sp+FE8h] [bp-660h]@27
  int v26; // [sp+FF0h] [bp-658h]@27
  char v27[4]; // [sp+FF4h] [bp-654h]@27
  int v28[2]; // [sp+FF8h] [bp-650h]@27
  __int64 v29[196]; // [sp+1000h] [bp-648h]@27
  char v30; // [sp+1624h] [bp-24h]@4
  int j; // [sp+1628h] [bp-20h]@11
  char v32; // [sp+162Ch] [bp-1Ch]@17
  unsigned __int64 v33; // [sp+1638h] [bp-10h]@4
  CMainThread *v34; // [sp+1650h] [bp+8h]@1
  unsigned int v35; // [sp+1658h] [bp+10h]@1
  unsigned int dwAccountSeriala; // [sp+1660h] [bp+18h]@1
  char v37; // [sp+1668h] [bp+20h]@1

  v37 = byRace;
  dwAccountSeriala = dwAccountSerial;
  v35 = dwSerial;
  v34 = this;
  v6 = alloca(a5);
  v7 = &v10;
  for ( i = 1436i64; i; --i )
  {
    *(_DWORD *)v7 = -*********;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v33 = (unsigned __int64)&v10 ^ _security_cookie;
  memset_0(&Dst, 0, 0x1620ui64);
  v30 = CRFWorldDatabase::Select_AccountTrunk(v34->m_pWorldDB, dwAccountSeriala, v37, (_worlddb_trunk_info *)&Dst);
  if ( v30 == 1 )
    return 24;
  if ( v30 == 2 )
  {
    if ( !CRFWorldDatabase::Insert_AccountTrunk(v34->m_pWorldDB, dwAccountSeriala) )
      return 24;
    if ( CRFWorldDatabase::Select_AccountTrunk(v34->m_pWorldDB, dwAccountSeriala, v37, (_worlddb_trunk_info *)&Dst) )
      return 24;
  }
  pTrunk->bySlotNum = v16;
  strcpy_0(pTrunk->wszPasswd, &Dst);
  pTrunk->dDalant = v12;
  pTrunk->dGold = v13;
  pTrunk->byHintIndex = v14;
  strcpy_0(pTrunk->wszHintAnswer, &Source);
  for ( j = 0; j < 100; ++j )
  {
    if ( j >= (unsigned __int8)v16 )
    {
      _INVENKEY::SetRelease(&pTrunk->m_List[j].Key);
    }
    else
    {
      _INVENKEY::LoadDBKey(&pTrunk->m_List[j].Key, pl_nKey[10 * j]);
      pTrunk->m_List[j].dwDur = *(&v18 + 5 * j);
      pTrunk->m_List[j].dwUpt = *(&v19 + 10 * j);
      pTrunk->m_List[j].byRace = v20[40 * j];
      pTrunk->m_List[j].dwT = v21[10 * j];
      pTrunk->m_List[j].lnUID = v22[5 * j];
    }
  }
  v32 = CRFWorldDatabase::Select_AccountTrunkExtend(v34->m_pWorldDB, dwAccountSeriala, (_worlddb_trunk_info *)&Dst);
  if ( v32 == 1 )
    return 24;
  if ( v32 != 2 )
    goto LABEL_33;
  if ( !CRFWorldDatabase::Insert_AccountTrunkExtend(v34->m_pWorldDB, dwAccountSeriala) )
    return 24;
  if ( CRFWorldDatabase::Select_AccountTrunkExtend(v34->m_pWorldDB, dwAccountSeriala, (_worlddb_trunk_info *)&Dst) )
  {
    result = 24;
  }
  else
  {
LABEL_33:
    pTrunk->byExtSlotNum = v23;
    for ( j = 0; j < 40; ++j )
    {
      if ( j >= (unsigned __int8)v23 )
      {
        _INVENKEY::SetRelease(&pTrunk->m_ExtList[j].Key);
      }
      else
      {
        _INVENKEY::LoadDBKey(&pTrunk->m_ExtList[j].Key, v24[10 * j]);
        pTrunk->m_ExtList[j].dwDur = *(&v25 + 5 * j);
        pTrunk->m_ExtList[j].dwUpt = *(&v26 + 10 * j);
        pTrunk->m_ExtList[j].byRace = v27[40 * j];
        pTrunk->m_ExtList[j].dwT = v28[10 * j];
        pTrunk->m_ExtList[j].lnUID = v29[5 * j];
      }
    }
    result = 0;
  }
  return result;
}
