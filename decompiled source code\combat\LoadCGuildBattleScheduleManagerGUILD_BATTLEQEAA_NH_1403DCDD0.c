/*
 * Function: ?Load@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAA_NHIHHHH@Z
 * Address: 0x1403DCDD0
 */

char __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Load(GUILD_BATTLE::CGuildBattleScheduleManager *this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTodayDayID, int iTomorrow, int iTomorrowDayID)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@6
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@9
  GUILD_BATTLE::CGuildBattleLogger *v12; // rax@11
  GUILD_BATTLE::CGuildBattleLogger *v13; // rax@15
  GUILD_BATTLE::CGuildBattleLogger *v14; // rax@17
  __int64 v15; // [sp+0h] [bp-58h]@1
  int v16; // [sp+20h] [bp-38h]@6
  int v17; // [sp+28h] [bp-30h]@6
  int v18; // [sp+30h] [bp-28h]@6
  int v19; // [sp+38h] [bp-20h]@6
  int v20; // [sp+40h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleScheduleManager *v21; // [sp+60h] [bp+8h]@1
  int v22; // [sp+68h] [bp+10h]@1
  unsigned int v23; // [sp+70h] [bp+18h]@1
  int v24; // [sp+78h] [bp+20h]@1

  v24 = iToday;
  v23 = uiOldMapCnt;
  v22 = iCurDay;
  v21 = this;
  v7 = &v15;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v21->m_bLoad = 1;
  v20 = CRFWorldDatabase::SelectRowCountGuildBattleScheduleInfo(pkDB);
  if ( v21->m_uiMapCnt != v23 || 46 * v21->m_uiMapCnt != v20 )
  {
    v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    v19 = iTomorrowDayID;
    v18 = iTomorrow;
    v17 = iTodayDayID;
    v16 = v24;
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v9,
      "CGuildBattleScheduleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTomor"
      "rowDayID(%d) : AddDefaultDBTable()",
      (unsigned int)v22,
      v23);
    return GUILD_BATTLE::CGuildBattleScheduleManager::AddDefaultDBTable(v21);
  }
  if ( v24 == v22 )
  {
    v21->m_pkTodaySchedule = &v21->m_kSchdule[iTodayDayID];
    v21->m_pkTomorrowSchedule = &v21->m_kSchdule[iTomorrowDayID];
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Load(v21->m_pkTodaySchedule, 1) )
    {
      v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v19 = iTomorrowDayID;
      v18 = iTomorrow;
      v17 = iTodayDayID;
      v16 = v24;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v11,
        "CGuildBattleScheduleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTom"
        "orrowDayID(%d) : ( iToday == iCurDay ) : m_pkTodaySchedule->Load(true) Fail!",
        (unsigned int)v22,
        v23);
      return 0;
    }
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Load(v21->m_pkTomorrowSchedule, 0) )
    {
      v12 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v19 = iTomorrowDayID;
      v18 = iTomorrow;
      v17 = iTodayDayID;
      v16 = v24;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v12,
        "CGuildBattleScheduleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTom"
        "orrowDayID(%d) : ( iToday == iCurDay ) : m_pkTomorrowSchedule->Load(false) Fail!",
        (unsigned int)v22,
        v23);
      return 0;
    }
  }
  else
  {
    if ( iTomorrow != v22 )
    {
      v14 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v19 = iTomorrowDayID;
      v18 = iTomorrow;
      v17 = iTodayDayID;
      v16 = v24;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v14,
        "CGuildBattleScheduleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTom"
        "orrowDayID(%d) : AddDefaultDBTable()",
        (unsigned int)v22,
        v23);
      return GUILD_BATTLE::CGuildBattleScheduleManager::AddDefaultDBTable(v21);
    }
    v21->m_pkTodaySchedule = &v21->m_kSchdule[iTomorrowDayID];
    v21->m_pkTomorrowSchedule = &v21->m_kSchdule[iTodayDayID];
    GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Clear(v21->m_pkTomorrowSchedule);
    if ( !GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Load(v21->m_pkTodaySchedule, 1) )
    {
      v13 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      v19 = iTomorrowDayID;
      v18 = iTomorrow;
      v17 = iTodayDayID;
      v16 = v24;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v13,
        "CGuildBattleScheduleManager::Load( iCurDay(%d), uiOldMapCnt(%u), iToday(%d), iTodayDayID(%d), iTomorrow(%d) iTom"
        "orrowDayID(%d) : ( iTomorrow == iCurDay ) : m_pkTodaySchedule->Load(true) Fail!",
        (unsigned int)v22,
        v23);
      return 0;
    }
  }
  return 1;
}
