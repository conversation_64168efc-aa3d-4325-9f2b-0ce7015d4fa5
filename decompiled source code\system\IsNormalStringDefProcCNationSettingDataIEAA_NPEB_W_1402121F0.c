/*
 * Function: ?IsNormalStringDefProc@CNationSettingData@@IEAA_NPEB_W0@Z
 * Address: 0x1402121F0
 */

char __fastcall CNationSettingData::IsNormalStringDefProc(CNationSettingData *this, const wchar_t *wszString, const wchar_t *wszException)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@6
  __int64 v7; // [sp+0h] [bp-58h]@1
  int j; // [sp+20h] [bp-38h]@4
  wchar_t SubStr; // [sp+34h] [bp-24h]@16
  __int16 v10; // [sp+36h] [bp-22h]@16
  CNationSettingDataVtbl *v11; // [sp+48h] [bp-10h]@13
  CNationSettingData *v12; // [sp+60h] [bp+8h]@1
  LPCWSTR lpString; // [sp+68h] [bp+10h]@1
  wchar_t *Str; // [sp+70h] [bp+18h]@1

  Str = (wchar_t *)wszException;
  lpString = wszString;
  v12 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; ; ++j )
  {
    v5 = lstrlenW(lpString);
    if ( j >= v5 )
      break;
    if ( ((signed int)lpString[j] < 65 || (signed int)lpString[j] > 90)
      && ((signed int)lpString[j] < 97 || (signed int)lpString[j] > 122)
      && !iswctype(lpString[j], 4u) )
    {
      v11 = v12->vfptr;
      if ( !(unsigned __int8)((int (__fastcall *)(CNationSettingData *, _QWORD))v11->IsNormalChar)(v12, lpString[j])
        && !iswctype(lpString[j], 0x103u) )
      {
        if ( !Str )
          return 0;
        SubStr = lpString[j];
        v10 = 0;
        if ( !wcsstr(Str, &SubStr) )
          return 0;
      }
    }
  }
  return 1;
}
