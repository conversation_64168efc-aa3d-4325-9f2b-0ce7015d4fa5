/*
 * Function: ?IsExistGroupID@CUnmannedTraderDivisionInfo@@QEAA_NEEEEAEAK@Z
 * Address: 0x14036DE80
 */

char __fastcall CUnmannedTraderDivisionInfo::IsExistGroupID(CUnmannedTraderDivisionInfo *this, char byDivision, char byClass, char bySubClass, char bySortType, unsigned int *dwListIndex)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@5
  __int64 v9; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > result; // [sp+28h] [bp-70h]@10
  unsigned int j; // [sp+44h] [bp-54h]@10
  bool v12; // [sp+48h] [bp-50h]@11
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > v13; // [sp+50h] [bp-48h]@11
  char v14; // [sp+68h] [bp-30h]@13
  char v15; // [sp+69h] [bp-2Fh]@15
  __int64 v16; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v17; // [sp+78h] [bp-20h]@11
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Right; // [sp+80h] [bp-18h]@11
  CUnmannedTraderClassInfo *v19; // [sp+88h] [bp-10h]@12
  CUnmannedTraderDivisionInfo *v20; // [sp+A0h] [bp+8h]@1
  char v21; // [sp+B0h] [bp+18h]@1
  char v22; // [sp+B8h] [bp+20h]@1

  v22 = bySubClass;
  v21 = byClass;
  v20 = this;
  v6 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v16 = -2i64;
  *dwListIndex = 0;
  if ( v20->m_dwID == (unsigned __int8)byDivision )
  {
    if ( (unsigned __int8)bySortType == 255 )
    {
      v8 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::empty(&v20->m_vecSortType);
    }
    else if ( CUnmannedTraderDivisionInfo::IsExistSortTypeID(v20, (unsigned __int8)bySortType) )
    {
      std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(
        &v20->m_vecClass,
        &result);
      for ( j = 0; ; ++j )
      {
        v17 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
                &v20->m_vecClass,
                &v13);
        _Right = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)v17;
        v12 = std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator!=(
                (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&result._Mycont,
                (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v17->_Mycont);
        std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v13);
        if ( !v12 )
          break;
        v19 = *std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator*(&result);
        if ( (unsigned __int8)((int (__fastcall *)(CUnmannedTraderClassInfo *, _QWORD, _QWORD))v19->vfptr->IsExistGroupID)(
                                v19,
                                (unsigned __int8)v21,
                                (unsigned __int8)v22) )
        {
          *dwListIndex = j;
          v14 = 1;
          std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
          return v14;
        }
        std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(&result);
      }
      v15 = 0;
      std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&result);
      v8 = v15;
    }
    else
    {
      v8 = 0;
    }
  }
  else
  {
    v8 = 0;
  }
  return v8;
}
