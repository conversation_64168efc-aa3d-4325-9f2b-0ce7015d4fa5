/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_AreaData_std::allocator_AreaData____AreaData_____ptr64_std::allocator_AreaData____::_1_::dtor$0
 * Address: 0x1401937B0
 */

void __fastcall std::_Uninit_copy_std::_Vector_const_iterator_AreaData_std::allocator_AreaData____AreaData_____ptr64_std::allocator_AreaData____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<AreaData,std::allocator<AreaData>>::~_Vector_const_iterator<AreaData,std::allocator<AreaData>>(*(std::_Vector_const_iterator<AreaData,std::allocator<AreaData> > **)(a2 + 104));
}
