/*
 * Function: ?TicksPerSecond@Timer@CryptoPP@@UEAA_KXZ
 * Address: 0x140660BE0
 */

__int64 __fastcall CryptoPP::Timer::TicksPerSecond(CryptoPP::Timer *this)
{
  unsigned int v1; // eax@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v2; // rax@3
  CryptoPP::Exception v4; // [sp+20h] [bp-E8h]@3
  char v5; // [sp+70h] [bp-98h]@3
  char v6; // [sp+A0h] [bp-68h]@3
  __int64 v7; // [sp+D0h] [bp-38h]@1
  __int64 v8; // [sp+D8h] [bp-30h]@3
  __int64 v9; // [sp+E0h] [bp-28h]@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v10; // [sp+E8h] [bp-20h]@3
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *s; // [sp+F0h] [bp-18h]@3

  v7 = -2i64;
  if ( !Frequency.QuadPart && !QueryPerformanceFrequency(&Frequency) )
  {
    v1 = GetLastError();
    v8 = CryptoPP::IntToString<unsigned long>((__int64)&v5, v1, 0xAu);
    v9 = v8;
    LODWORD(v2) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(
                    &v6,
                    "Timer: QueryPerformanceFrequency failed with error ",
                    v8);
    v10 = v2;
    s = v2;
    CryptoPP::Exception::Exception(&v4, OTHER_ERROR, v2);
    CxxThrowException_0((__int64)&v4, (__int64)&TI2_AVException_CryptoPP__);
  }
  return Frequency.QuadPart;
}
