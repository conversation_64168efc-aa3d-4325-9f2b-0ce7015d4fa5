/*
 * Function: ?Next@CGuildBattleReservedSchedule@GUILD_BATTLE@@AEAA_NXZ
 * Address: 0x1403DB7F0
 */

char __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Next(GUILD_BATTLE::CGuildBattleReservedSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+24h] [bp-14h]@4
  GUILD_BATTLE::CGuildBattleReservedSchedule *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ++v7->m_uiCurScheduleInx;
  v5 = 0;
  for ( j = v7->m_uiCurScheduleInx; j < 0x17; ++j )
  {
    if ( v7->m_pkSchedule[j] && GUILD_BATTLE::CGuildBattleSchedule::IsWait(v7->m_pkSchedule[j]) )
    {
      v7->m_uiCurScheduleInx = j;
      GUILD_BATTLE::CGuildBattleSchedule::SetProcState(v7->m_pkSchedule[v7->m_uiCurScheduleInx]);
      v5 = 1;
      break;
    }
  }
  if ( v5 )
  {
    result = 1;
  }
  else
  {
    v7->m_uiCurScheduleInx = 0;
    result = 0;
  }
  return result;
}
