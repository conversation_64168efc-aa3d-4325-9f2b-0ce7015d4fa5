/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0___::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0____::_1_::dtor$0
 * Address: 0x1403064D0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0___::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::~list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>((std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > > *)(*(_QWORD *)(a2 + 112) + 16i64));
}
