/*
 * Function: j_?SendMsg_TestAttackResult@CPlayer@@QEAAXEEGEEPEAF@Z
 * Address: 0x14000795A
 */

void __fastcall CPlayer::SendMsg_TestAttackResult(CPlayer *this, char byEffectCode, char byEffectIndex, unsigned __int16 wBulletItemIndex, char byEffectLv, char by<PERSON>eapon<PERSON><PERSON>, __int16 *pzTar)
{
  CPlayer::SendMsg_TestAttackResult(
    this,
    byEffectCode,
    byEffectIndex,
    wBulletItemIndex,
    byEffectLv,
    byWeaponPart,
    pzTar);
}
