/*
 * Function: _std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::insert_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64______::_1_::dtor$4
 * Address: 0x140380370
 */

void __fastcall std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::insert_std::_Vector_const_iterator_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64______::_1_::dtor_4(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 112));
}
