/*
 * Function: _std::vector_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::_Insert_n_::_1_::catch$1
 * Address: 0x1403728E0
 */

void __fastcall __noreturn std::vector_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Destroy(
    *(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > **)(a2 + 160),
    (CUnmannedTraderSortType **)(*(_QWORD *)(*(_QWORD *)(a2 + 168) + 16i64) + 8i64 * *(_QWORD *)(a2 + 176)),
    (CUnmannedTraderSortType **)(*(_QWORD *)(*(_QWORD *)(a2 + 160) + 24i64) + 8i64 * *(_QWORD *)(a2 + 176)));
  CxxThrowException_0(0i64, 0i64);
}
