/*
 * Function: j_?CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGE@DfAIMgr@@SAHPEAVCMonsterSkill@@HPEAVCMonsterAI@@PEAVCMonster@@PEAP<PERSON><PERSON><PERSON>haracter@@@Z
 * Address: 0x14000E124
 */

int __fastcall DfAIMgr::CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGE(CMonsterSkill *pSkill, int nMotiveValue, CMonsterAI *pAI, CMonster *pMon, CCharacter **ppTar)
{
  return DfAIMgr::CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGE(pSkill, nMotiveValue, pAI, pMon, ppTar);
}
