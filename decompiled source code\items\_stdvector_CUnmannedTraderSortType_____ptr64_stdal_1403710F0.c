/*
 * Function: _std::vector_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::erase_::_1_::dtor$2
 * Address: 0x1403710F0
 */

void __fastcall std::vector_CUnmannedTraderSortType_____ptr64_std::allocator_CUnmannedTraderSortType_____ptr64___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(*(std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > **)(a2 + 88));
  }
}
