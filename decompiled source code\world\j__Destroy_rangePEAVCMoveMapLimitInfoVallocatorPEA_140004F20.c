/*
 * Function: j_??$_Destroy_range@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@YAXPEAPEAVCMoveMapLimitInfo@@0AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@@Z
 * Address: 0x140004F20
 */

void __fastcall std::_Destroy_range<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, std::allocator<CMoveMapLimitInfo *> *_Al)
{
  std::_Destroy_range<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(_First, _Last, _Al);
}
