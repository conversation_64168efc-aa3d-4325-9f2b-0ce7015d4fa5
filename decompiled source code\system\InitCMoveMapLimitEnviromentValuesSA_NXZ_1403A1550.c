/*
 * Function: ?Init@CMoveMapLimitEnviromentValues@@SA_NXZ
 * Address: 0x1403A1550
 */

char __cdecl CMoveMapLimitEnviromentValues::Init()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4

  v0 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  for ( j = 0; j < dword_141470B98; ++j )
  {
    if ( !strcmp_0((const char *)(*(_QWORD *)(unk_141470BC8 + 1504i64 * j + 744) + 4i64), "Elan") )
    {
      CMoveMapLimitEnviromentValues::ELAN_MAP_CODE = *(_DWORD *)(unk_141470BC8 + 1504i64 * j + 584);
      return 1;
    }
  }
  return 1;
}
