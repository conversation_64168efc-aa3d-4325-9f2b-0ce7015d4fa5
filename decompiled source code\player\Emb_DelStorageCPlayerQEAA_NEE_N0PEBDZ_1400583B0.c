/*
 * Function: ?Emb_DelStorage@CPlayer@@QEAA_NEE_N0PEBD@Z
 * Address: 0x1400583B0
 */

char __fastcall CPlayer::Emb_DelStorage(CPlayer *this, char byStorageCode, char byStorageIndex, bool bEquip<PERSON>hange, bool bD<PERSON>te, const char *strErrorCodePos)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@6
  char *v9; // rax@7
  char result; // al@8
  LendItemMng *v11; // rax@10
  char *v12; // rax@14
  char *v13; // rax@15
  CPlayer::CashChangeStateFlag *v14; // rax@32
  TimeLimitJadeMng *v15; // rax@49
  char *v16; // rax@50
  __int64 v17; // [sp+0h] [bp-78h]@1
  int nAlter; // [sp+20h] [bp-58h]@6
  const char *v19; // [sp+28h] [bp-50h]@6
  _STORAGE_LIST::_db_con *pkItem; // [sp+30h] [bp-48h]@9
  _ResourceItem_fld *pFld; // [sp+38h] [bp-40h]@45
  CPlayer::CashChangeStateFlag v22; // [sp+40h] [bp-38h]@32
  int v23; // [sp+44h] [bp-34h]@6
  unsigned int v24; // [sp+48h] [bp-30h]@6
  int v25; // [sp+4Ch] [bp-2Ch]@7
  unsigned int v26; // [sp+50h] [bp-28h]@7
  int v27; // [sp+54h] [bp-24h]@14
  unsigned int v28; // [sp+58h] [bp-20h]@14
  int v29; // [sp+5Ch] [bp-1Ch]@15
  unsigned int v30; // [sp+60h] [bp-18h]@15
  int v31; // [sp+64h] [bp-14h]@50
  int v32; // [sp+68h] [bp-10h]@50
  unsigned int v33; // [sp+6Ch] [bp-Ch]@50
  CPlayer *v34; // [sp+80h] [bp+8h]@1
  char v35; // [sp+88h] [bp+10h]@1
  char v36; // [sp+90h] [bp+18h]@1
  bool v37; // [sp+98h] [bp+20h]@1

  v37 = bEquipChange;
  v36 = byStorageIndex;
  v35 = byStorageCode;
  v34 = this;
  v6 = &v17;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( (signed int)(unsigned __int8)byStorageCode < 8 )
  {
    pkItem = &v34->m_Param.m_pStoragePtr[(unsigned __int8)byStorageCode]->m_pStorageList[(unsigned __int8)byStorageIndex];
    if ( !pkItem->m_byCsMethod
      || (v11 = LendItemMng::Instance(), LendItemMng::DeleteLink(v11, v34->m_ObjID.m_wIndex, v35, pkItem)) )
    {
      if ( _STORAGE_LIST::EmptyCon(v34->m_Param.m_pStoragePtr[(unsigned __int8)v35], (unsigned __int8)v36) )
      {
        if ( v34->m_pUserDB )
          CUserDB::Update_ItemDelete(v34->m_pUserDB, v35, v36, bDelete);
        if ( v35 == 1 || v35 == 2 )
        {
          if ( pkItem->m_byTableCode != 10 && CPlayer::GetEffectEquipCode(v34, v35, v36) == 1 )
            CPlayer::SetEquipEffect(v34, (_STORAGE_LIST::_storage_con *)&pkItem->m_bLoad, 0);
          CPlayer::SetEffectEquipCode(v34, v35, v36, 0);
          if ( pkItem->m_byTableCode < 5
            || pkItem->m_byTableCode == 5
            || pkItem->m_byTableCode == 8
            || pkItem->m_byTableCode == 7
            || pkItem->m_byTableCode == 9 )
          {
            CPlayer::CalcDefTol(v34);
          }
        }
        if ( v35 == 1 )
        {
          CEquipItemSFAgent::ReleaseSFCont(&v34->EquipItemSFAgent, pkItem->m_byTableCode);
          if ( !v37 )
          {
            CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v22, 0);
            CPlayer::UpdateVisualVer(v34, (CPlayer::CashChangeStateFlag)v14->0);
            CPlayer::SendMsg_EquipPartChange(v34, pkItem->m_byTableCode);
            if ( pkItem->m_byTableCode == 6 && !CPlayer::IsRidingUnit(v34) )
              _WEAPON_PARAM::FixWeapon(&v34->m_pmWpn, 0i64);
          }
          if ( pkItem->m_byTableCode == 6 && _effect_parameter::GetEff_State(&v34->m_EP, 14) )
            CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v34->vfptr, 2, 14);
          CPlayer::CalcEquipSpeed(v34);
          CPlayer::CalcEquipMaxDP(v34, 0);
          if ( pkItem->m_byTableCode == 6 )
          {
            if ( v34->m_bMineMode )
            {
              v34->m_bMineMode = 0;
              v34->m_dwMineNextTime = -1;
              CPlayer::SendMsg_MineCancle(v34);
            }
            if ( CPlayer::IsSiegeMode(v34) )
              CPlayer::SetSiege(v34, 0i64);
          }
        }
        if ( !v35 )
        {
          if ( pkItem->m_byTableCode == 18 )
          {
            pFld = (_ResourceItem_fld *)CRecordData::GetRecord(
                                          (CRecordData *)&unk_1799C6AA0 + pkItem->m_byTableCode,
                                          pkItem->m_wItemIndex);
            if ( pFld->m_nEffectDataNum > 0 )
            {
              CPlayer::SetHaveEffect(v34, 0);
              if ( !pFld->m_nEffType1 )
                CPlayer::SetMstHaveEffect(v34, pFld, pkItem, 0, 0);
              if ( pFld->m_nStartTime != -1 )
              {
                v15 = TimeLimitJadeMng::Instance();
                if ( !TimeLimitJadeMng::DeleteList(v15, v34->m_ObjID.m_wIndex, pkItem) )
                {
                  v31 = pkItem->m_wItemIndex;
                  v32 = pkItem->m_byTableCode;
                  v33 = 0;
                  v16 = CPlayerDB::GetCharNameA(&v34->m_Param);
                  LODWORD(v19) = v31;
                  nAlter = v32;
                  CLogFile::Write(
                    &stru_1799C8E78,
                    "%s: Emb_DelStorage.. TimeLimitJadeMng::DeleteList() error storage: %d, item: %d-%d: ",
                    v16,
                    v33);
                }
              }
            }
          }
          if ( CPlayer::IsSiegeMode(v34) && pkItem == v34->m_pSiegeItem )
            CPlayer::SetSiege(v34, 0i64);
        }
        if ( bDelete )
          CPlayer::SendMsg_DeleteStorageInform(v34, v35, pkItem->m_wSerial);
        result = 1;
      }
      else
      {
        if ( strErrorCodePos )
        {
          v27 = (unsigned __int8)v36;
          v28 = (unsigned __int8)v35;
          v12 = CPlayerDB::GetCharNameA(&v34->m_Param);
          v19 = strErrorCodePos;
          nAlter = v27;
          CLogFile::Write(
            &stru_1799C8E78,
            "%s: Emb_DelStorage.. EmptyCon()error storage: %d, slot: %d: CodePos: %s",
            v12,
            v28);
        }
        else
        {
          v29 = (unsigned __int8)v36;
          v30 = (unsigned __int8)v35;
          v13 = CPlayerDB::GetCharNameA(&v34->m_Param);
          nAlter = v29;
          CLogFile::Write(&stru_1799C8E78, "%s: Emb_DelStorage.. EmptyCon()error storage: %d, slot: %d: ", v13, v30);
        }
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    if ( strErrorCodePos )
    {
      v23 = (unsigned __int8)byStorageIndex;
      v24 = (unsigned __int8)byStorageCode;
      v8 = CPlayerDB::GetCharNameA(&v34->m_Param);
      v19 = strErrorCodePos;
      nAlter = v23;
      CLogFile::Write(
        &stru_1799C8E78,
        "%s: Emb_DelStorage.. Storage Code Over storage: %d, slot: %d: CodePos: %s",
        v8,
        v24);
    }
    else
    {
      v25 = (unsigned __int8)byStorageIndex;
      v26 = (unsigned __int8)byStorageCode;
      v9 = CPlayerDB::GetCharNameA(&v34->m_Param);
      nAlter = v25;
      CLogFile::Write(&stru_1799C8E78, "%s: Emb_DelStorage.. Storage Code Over storage: %d, slot: %d: ", v9, v26);
    }
    result = 0;
  }
  return result;
}
