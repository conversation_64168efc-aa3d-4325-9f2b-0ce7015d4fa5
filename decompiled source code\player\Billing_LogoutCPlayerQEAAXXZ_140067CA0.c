/*
 * Function: ?Billing_Logout@CPlayer@@QEAAXXZ
 * Address: 0x140067CA0
 */

void __fastcall CPlayer::Billing_Logout(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@4
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  v4 = CNationSettingManager::GetBillingForceCloseDelay(v3);
  CPlayer::SendMsg_BillingExipreInform(v6, 0, v4);
  CPlayer::ReservationForceClose(v6);
}
