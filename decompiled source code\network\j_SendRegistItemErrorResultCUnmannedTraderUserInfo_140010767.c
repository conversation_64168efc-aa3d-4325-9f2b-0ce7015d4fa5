/*
 * Function: j_?SendRegistItemError<PERSON><PERSON>ult@CUnmannedTraderUserInfo@@QEAAXGEGK@Z
 * Address: 0x140010767
 */

void __fastcall CUnmannedTraderUserInfo::SendRegistItemErrorResult(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, char byRet, unsigned __int16 wItemSerial, unsigned int dwRetParam1)
{
  CUnmannedTraderUserInfo::SendRegistItemErrorResult(this, wInx, byRet, wItemSerial, dwRetParam1);
}
