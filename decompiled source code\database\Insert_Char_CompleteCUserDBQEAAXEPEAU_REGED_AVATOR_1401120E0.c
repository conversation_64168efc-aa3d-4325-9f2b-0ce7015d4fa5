/*
 * Function: ?Insert_Char_Complete@CUserDB@@QEAAXEPEAU_REGED_AVATOR_DB@@@Z
 * Address: 0x1401120E0
 */

void __fastcall CUserDB::Insert_Char_Complete(CUserDB *this, char byRetCode, _REGED_AVATOR_DB *pInsertData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@6
  __int64 v6; // [sp+0h] [bp-88h]@1
  unsigned __int8 v7; // [sp+30h] [bp-58h]@4
  _add_char_result_zone v8; // [sp+44h] [bp-44h]@6
  char pbyType; // [sp+64h] [bp-24h]@6
  char v10; // [sp+65h] [bp-23h]@6
  CUserDB *v11; // [sp+90h] [bp+8h]@1
  char v12; // [sp+98h] [bp+10h]@1
  _REGED_AVATOR_DB *Src; // [sp+A0h] [bp+18h]@1

  Src = pInsertData;
  v12 = byRetCode;
  v11 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11->m_bDBWaitState = 0;
  v11->m_dwOperLobbyTime = timeGetTime();
  v7 = -1;
  if ( !v12 )
  {
    v7 = Src->m_bySlotIndex;
    memcpy_0(&v11->m_RegedList[v7], Src, 0x45ui64);
  }
  CMgrAccountLobbyHistory::add_char_complete(&CUserDB::s_MgrLobbyHistory, v12, Src, v11->m_szLobbyHistoryFileName);
  v8.byRetCode = v12;
  v8.byAddSlotIndex = v7;
  pbyType = 1;
  v10 = 11;
  v5 = _add_char_result_zone::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_idWorld.wIndex, &pbyType, &v8.byRetCode, v5);
}
