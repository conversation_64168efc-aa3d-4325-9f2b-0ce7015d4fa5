/*
 * Function: ?SetBase@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEAAXAEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@AEBUECPPoint@2@@Z
 * Address: 0x140577FC0
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::SetBase(__int64 a1, int (__fastcall ***a2)(_QWORD), CryptoPP::ECPPoint *a3)
{
  CryptoPP::ECPPoint *v3; // rax@2
  __int64 v4; // rax@7
  CryptoPP::ECPPoint *v5; // rax@8
  CryptoPP::ECPPoint *result; // rax@9
  CryptoPP::ECPPoint v7; // [sp+28h] [bp-90h]@2
  int v8; // [sp+80h] [bp-38h]@1
  __int64 v9; // [sp+88h] [bp-30h]@1
  CryptoPP::ECPPoint *v10; // [sp+90h] [bp-28h]@2
  CryptoPP::ECPPoint *v11; // [sp+98h] [bp-20h]@2
  CryptoPP::ECPPoint *v12; // [sp+A0h] [bp-18h]@2
  CryptoPP::ECPPoint *v13; // [sp+A8h] [bp-10h]@8
  __int64 v14; // [sp+C0h] [bp+8h]@1
  int (__fastcall ***v15)(_QWORD); // [sp+C8h] [bp+10h]@1
  CryptoPP::ECPPoint *v16; // [sp+D0h] [bp+18h]@1

  v16 = a3;
  v15 = a2;
  v14 = a1;
  v9 = -2i64;
  v8 = 0;
  if ( (unsigned __int8)(**a2)(a2) )
  {
    LODWORD(v3) = ((int (__fastcall *)(int (__fastcall ***)(_QWORD), CryptoPP::ECPPoint *, CryptoPP::ECPPoint *))(*v15)[1])(
                    v15,
                    &v7,
                    v16);
    v10 = v3;
    v11 = v3;
    v8 |= 1u;
    v12 = v3;
  }
  else
  {
    v12 = v16;
  }
  CryptoPP::ECPPoint::operator=((CryptoPP::ECPPoint *)(v14 + 8), v12);
  if ( v8 & 1 )
  {
    v8 &= 0xFFFFFFFE;
    CryptoPP::ECPPoint::~ECPPoint(&v7);
  }
  if ( std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::empty((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v14 + 144))
    || (LODWORD(v4) = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](v14 + 144, 0i64),
        !CryptoPP::ECPPoint::operator==(v14 + 8, v4)) )
  {
    std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::resize(v14 + 144, 1i64);
    v13 = (CryptoPP::ECPPoint *)(v14 + 8);
    LODWORD(v5) = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::operator[](v14 + 144, 0i64);
    CryptoPP::ECPPoint::operator=(v5, v13);
  }
  result = (CryptoPP::ECPPoint *)(unsigned __int8)(**v15)(v15);
  if ( (_BYTE)result )
    result = CryptoPP::ECPPoint::operator=((CryptoPP::ECPPoint *)(v14 + 8), v16);
  return result;
}
