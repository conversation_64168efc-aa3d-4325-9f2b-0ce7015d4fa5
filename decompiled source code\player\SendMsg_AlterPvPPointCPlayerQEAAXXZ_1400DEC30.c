/*
 * Function: ?SendMsg_AlterPvPPoint@CPlayer@@QEAAXXZ
 * Address: 0x1400DEC30
 */

void __usercall CPlayer::SendMsg_AlterPvPPoint(CPlayer *this@<rcx>, __int64 a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[8]; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CPlayer *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CPlayerDB::GetPvPPoint(&v8->m_Param);
  *(_QWORD *)szMsg = a2;
  pbyType = 11;
  v7 = 10;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, szMsg, 8u);
}
