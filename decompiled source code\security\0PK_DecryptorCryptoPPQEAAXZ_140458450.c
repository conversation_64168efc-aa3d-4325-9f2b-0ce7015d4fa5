/*
 * Function: ??0PK_Decryptor@CryptoPP@@QEAA@XZ
 * Address: 0x140458450
 */

void __fastcall CryptoPP::PK_Decryptor::PK_Decryptor(CryptoPP::PK_Decryptor *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::PK_Decryptor *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CryptoPP::PK_CryptoSystem::PK_CryptoSystem((CryptoPP::PK_CryptoSystem *)&v5->vfptr);
  CryptoPP::PrivateKeyAlgorithm::PrivateKeyAlgorithm((CryptoPP::PrivateKeyAlgorithm *)&v5->vfptr);
}
