/*
 * Function: j_??$unchecked_uninitialized_copy@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@stdext@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@Z
 * Address: 0x140010965
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall stdext::unchecked_uninitialized_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al)
{
  return stdext::unchecked_uninitialized_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
