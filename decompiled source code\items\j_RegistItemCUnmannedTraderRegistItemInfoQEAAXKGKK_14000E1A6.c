/*
 * Function: j_?RegistItem@CUnmannedTraderRegistItemInfo@@QEAAXKGKKEEGE_KK_N@Z
 * Address: 0x14000E1A6
 */

void __fastcall CUnmannedTraderRegistItemInfo::RegistItem(CUnmannedTraderRegistItemInfo *this, unsigned int dwRegistSerial, unsigned __int16 wItemSerial, unsigned int dwETSerialNumber, unsigned int dwPrice, char bySellTurm, char byTableCode, unsigned __int16 wItemIndex, char byStorageIndex, unsigned __int64 dwD, unsigned int dwU, bool bInserted)
{
  CUnmannedTraderRegistItemInfo::RegistItem(
    this,
    dwRegistSerial,
    wItemSerial,
    dwETSerialNumber,
    dwPrice,
    bySellTurm,
    byTableCode,
    wItemIndex,
    byStorageIndex,
    dwD,
    dwU,
    bInserted);
}
