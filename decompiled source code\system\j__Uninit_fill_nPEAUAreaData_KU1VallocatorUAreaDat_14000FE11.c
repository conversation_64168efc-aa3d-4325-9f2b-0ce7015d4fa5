/*
 * Function: j_??$_Uninit_fill_n@PEAUAreaData@@_KU1@V?$allocator@UAreaData@@@std@@@std@@YAXPEAUAreaData@@_KAEBU1@AEAV?$allocator@UAreaData@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000FE11
 */

void __fastcall std::_Uninit_fill_n<AreaData *,unsigned __int64,AreaData,std::allocator<AreaData>>(AreaData *_First, unsigned __int64 _Count, AreaData *_Val, std::allocator<AreaData> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<AreaData *,unsigned __int64,AreaData,std::allocator<AreaData>>(
    _First,
    _Count,
    _<PERSON>,
    _Al,
    __formal,
    a6);
}
