/*
 * Function: ?AddGDalant@TRC_AutoTrade@@QEAAXPEAD@Z
 * Address: 0x1402D85C0
 */

void __fastcall TRC_AutoTrade::AddGDalant(TRC_AutoTrade *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  long double v4; // xmm0_8@7
  __int64 v5; // [sp+0h] [bp-58h]@1
  _DWORD bInPut[2]; // [sp+20h] [bp-38h]@6
  char *pbyDate; // [sp+28h] [bp-30h]@6
  long double v8; // [sp+30h] [bp-28h]@7
  char *pszFileName; // [sp+38h] [bp-20h]@7
  char *v10; // [sp+40h] [bp-18h]@4
  TRC_AutoTrade *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = pdata;
  if ( v11->m_pOwnerGuild && v11->m_pOwnerGuild->m_dwSerial == *((_DWORD *)v10 + 1) )
  {
    v11->m_pOwnerGuild->m_dTotalDalant = *((long double *)v10 + 2);
    CGuild::MakeDownMemberPacket(v11->m_pOwnerGuild);
    v11->m_pOwnerGuild->m_byMoneyOutputKind = 6;
    v4 = (double)*((signed int *)v10 + 3);
    pbyDate = v10 + 32;
    LOBYTE(bInPut[0]) = 1;
    CGuild::SendMsg_IOMoney(v11->m_pOwnerGuild, *((_DWORD *)v10 + 2), v4, 0.0, 1, v10 + 32);
    pszFileName = v11->m_pOwnerGuild->m_szHistoryFileName;
    v8 = *((double *)v10 + 3);
    pbyDate = (char *)*((_QWORD *)v10 + 2);
    bInPut[0] = 0;
    CMgrGuildHistory::push_money(
      &CGuild::s_MgrHistory,
      "Auto Trade Tax",
      *((_DWORD *)v10 + 2),
      *((_DWORD *)v10 + 3),
      0,
      *(long double *)&pbyDate,
      v8,
      pszFileName);
  }
  else
  {
    pbyDate = (char *)*((_QWORD *)v10 + 2);
    bInPut[0] = *((_DWORD *)v10 + 3);
    CLogFile::Write(
      &v11->m_sysLog,
      "Failed TRC_AutoTrade::AddGDalant(GuildSerial:%d,seller:%d,in:@%d,total:@%.0f)",
      *((_DWORD *)v10 + 1),
      *((_DWORD *)v10 + 2));
  }
}
