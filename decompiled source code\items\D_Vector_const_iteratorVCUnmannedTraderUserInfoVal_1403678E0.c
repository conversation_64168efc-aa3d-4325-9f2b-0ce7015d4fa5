/*
 * Function: ??D?$_Vector_const_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEBAAEBVCUnmannedTraderUserInfo@@XZ
 * Address: 0x1403678E0
 */

CUnmannedTraderUserInfo *__fastcall std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator*(std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  return this->_Myptr;
}
