/*
 * Function: ?Attack@CTrap@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x14013EBF0
 */

void __fastcall CTrap::Attack(CTrap *this, <PERSON>haracter *pTarget)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@14
  __int64 v5; // [sp+0h] [bp-418h]@1
  bool v6; // [sp+20h] [bp-3F8h]@14
  int v7; // [sp+28h] [bp-3F0h]@14
  int v8; // [sp+30h] [bp-3E8h]@14
  char v9; // [sp+38h] [bp-3E0h]@14
  CAttack pAt; // [sp+50h] [bp-3C8h]@4
  _attack_param Dst; // [sp+360h] [bp-B8h]@4
  int v12; // [sp+3E4h] [bp-34h]@7
  int j; // [sp+3E8h] [bp-30h]@7
  _be_damaged_char *v14; // [sp+3F0h] [bp-28h]@9
  CCharacter *v15; // [sp+3F8h] [bp-20h]@14
  CGameObjectVtbl *v16; // [sp+400h] [bp-18h]@14
  CTrap *pThis; // [sp+420h] [bp+8h]@1
  CCharacter *v18; // [sp+428h] [bp+10h]@1

  v18 = pTarget;
  pThis = this;
  v2 = &v5;
  for ( i = 260i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CAttack::CAttack(&pAt, (CCharacter *)&pThis->vfptr);
  _attack_param::_attack_param(&Dst);
  Dst.pDst = v18;
  memcpy_0(Dst.fArea, v18->m_fCurPos, 0xCui64);
  if ( v18 )
    Dst.nPart = CCharacter::GetAttackRandomPart(v18);
  else
    Dst.nPart = CCharacter::GetAttackRandomPart((CCharacter *)&pThis->vfptr);
  Dst.nTol = *(_DWORD *)&pThis->m_pRecordSet[7].m_strCode[8];
  Dst.nClass = 1;
  Dst.nMinAF = *(_DWORD *)&pThis->m_pRecordSet[5].m_strCode[44];
  Dst.nMaxAF = *(_DWORD *)&pThis->m_pRecordSet[5].m_strCode[48];
  Dst.nMinSel = *(_DWORD *)&pThis->m_pRecordSet[5].m_strCode[52];
  Dst.nMaxSel = *(_DWORD *)&pThis->m_pRecordSet[5].m_strCode[56];
  Dst.nAttactType = 6;
  Dst.nExtentRange = (signed int)ffloor(*(float *)&pThis->m_pRecordSet[5].m_strCode[32]);
  Dst.nMaxAttackPnt = pThis->m_nTrapMaxAttackPnt;
  CAttack::AttackGen(&pAt, &Dst, 0, 0);
  v12 = 0;
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v14 = &pAt.m_DamList[j];
    v12 += pAt.m_DamList[j].m_nDamage;
  }
  if ( pAt.m_nDamagedObjNum > 0 )
    CTrap::SendMsg_Attack(pThis, &pAt);
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v4 = ((int (__fastcall *)(CTrap *))pThis->vfptr->GetLevel)(pThis);
    v15 = pAt.m_DamList[j].m_pChar;
    v16 = v15->vfptr;
    v9 = 1;
    v8 = 0;
    v7 = -1;
    v6 = pAt.m_bIsCrtAtt;
    ((void (__fastcall *)(CCharacter *, _QWORD, CTrap *, _QWORD))v16->SetDamage)(
      v15,
      pAt.m_DamList[j].m_nDamage,
      pThis,
      (unsigned int)v4);
  }
}
