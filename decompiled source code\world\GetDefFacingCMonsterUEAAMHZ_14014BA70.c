/*
 * Function: ?GetDefFacing@CMonster@@UEAAMH@Z
 * Address: 0x14014BA70
 */

float __fastcall CMonster::GetDefFacing(CMonster *this, int nPart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-18h]@1
  CMonster *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return *(float *)&v6->m_pRecordSet[5].m_strCode[8];
}
