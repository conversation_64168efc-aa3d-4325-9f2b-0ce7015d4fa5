/*
 * Function: ?DecPvpPoint@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAANAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403DFE10
 */

double __fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::DecPvpPoint(GUILD_BATTLE::CNormalGuildBattleGuildMember *this, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double result; // xmm0_8@8
  char *v5; // rax@12
  __int64 v6; // [sp+0h] [bp-68h]@1
  double v7; // [sp+20h] [bp-48h]@12
  double v8; // [sp+40h] [bp-28h]@4
  CPlayer *v9; // [sp+48h] [bp-20h]@9
  double v10; // [sp+50h] [bp-18h]@6
  double v11; // [sp+58h] [bp-10h]@10
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v12; // [sp+70h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleLogger *v13; // [sp+78h] [bp+10h]@1

  v13 = kLogger;
  v12 = this;
  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0.0;
  if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(v12) )
  {
    v9 = v12->m_pkMember->pPlayer;
    CPlayerDB::GetPvPPoint(&v9->m_Param);
    v8 = 0.0;
    if ( 0.0 < 10000.0 )
      v11 = v8;
    else
      v11 = DOUBLE_10000_0;
    v8 = v11;
    CPlayer::AlterPvPPoint(v9, -0.0 - v11, guildbattle, 0xFFFFFFFF);
    v5 = CPlayerDB::GetCharNameW(&v9->m_Param);
    v7 = v8;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v13,
      "CNormalGuildBattleGuildMember::DecPvpPoint() : %s(%d) DecPvpPoint(%f)!",
      v5,
      v9->m_dwObjSerial);
    result = v8;
  }
  else
  {
    if ( v12->m_dPvpPoint < 10000.0 )
      v10 = v12->m_dPvpPoint;
    else
      v10 = DOUBLE_10000_0;
    v8 = v10;
    GUILD_BATTLE::CNormalGuildBattleGuildMember::PushDQSPvpPoint(v12, (signed int)floor(-0.0 - v10));
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      v13,
      "CNormalGuildBattleGuildMember::DecPvpPoint() : Not Connected! Serial(%d) DecPvpPoint(%f)!",
      v12->m_dwSerial,
      v8);
    result = 0.0;
  }
  return result;
}
