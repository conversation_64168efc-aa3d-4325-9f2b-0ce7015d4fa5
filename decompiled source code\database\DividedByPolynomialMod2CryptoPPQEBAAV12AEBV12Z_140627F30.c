/*
 * Function: ?DividedBy@PolynomialMod2@CryptoPP@@QEBA?AV12@AEBV12@@Z
 * Address: 0x140627F30
 */

struct CryptoPP::PolynomialMod2 *__fastcall CryptoPP::PolynomialMod2::DividedBy(CryptoPP::PolynomialMod2 *this, struct CryptoPP::PolynomialMod2 *retstr, const struct CryptoPP::PolynomialMod2 *a3)
{
  struct CryptoPP::PolynomialMod2 v4; // [sp+20h] [bp-48h]@1
  struct CryptoPP::PolynomialMod2 v5; // [sp+38h] [bp-30h]@1
  int v6; // [sp+50h] [bp-18h]@1
  __int64 v7; // [sp+58h] [bp-10h]@1
  const struct CryptoPP::PolynomialMod2 *v8; // [sp+70h] [bp+8h]@1
  CryptoPP::PolynomialMod2 *v9; // [sp+78h] [bp+10h]@1
  struct CryptoPP::PolynomialMod2 *v10; // [sp+80h] [bp+18h]@1

  v10 = (struct CryptoPP::PolynomialMod2 *)a3;
  v9 = retstr;
  v8 = this;
  v7 = -2i64;
  v6 = 0;
  CryptoPP::PolynomialMod2::PolynomialMod2(&v4);
  CryptoPP::PolynomialMod2::PolynomialMod2(&v5);
  CryptoPP::PolynomialMod2::Divide(&v4, &v5, v8, v10);
  CryptoPP::PolynomialMod2::PolynomialMod2(v9, &v5);
  v6 |= 1u;
  CryptoPP::PolynomialMod2::~PolynomialMod2(&v5);
  CryptoPP::PolynomialMod2::~PolynomialMod2(&v4);
  return v9;
}
