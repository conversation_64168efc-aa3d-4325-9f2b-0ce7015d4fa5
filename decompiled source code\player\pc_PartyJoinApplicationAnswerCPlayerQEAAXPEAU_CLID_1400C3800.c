/*
 * Function: ?pc_PartyJoinApplicationAnswer@CPlayer@@QEAAXPEAU_CLID@@@Z
 * Address: 0x1400C3800
 */

void __usercall CPlayer::pc_PartyJoinApplicationAnswer(CPlayer *this@<rcx>, _CLID *pidApplicant@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@13
  int v6; // eax@19
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPlayer *v8; // [sp+20h] [bp-18h]@7
  int v9; // [sp+28h] [bp-10h]@13
  float v10; // [sp+2Ch] [bp-Ch]@19
  CPlayer *v11; // [sp+40h] [bp+8h]@1
  _CLID *v12; // [sp+48h] [bp+10h]@1

  v12 = pidApplicant;
  v11 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v11->m_id.wIndex) == 99 )
  {
    CPlayer::SendMsg_TLStatusPenalty(v11, 1);
    return;
  }
  if ( !CPlayer::IsPunished(v11, 2, 1) )
  {
    v8 = &g_Player + v12->wIndex;
    if ( v8->m_id.dwSerial == v12->dwSerial )
    {
      if ( v8->m_bLive )
      {
        if ( !v8->m_bCorpse && v8->m_pCurMap == v11->m_pCurMap && !CPlayer::IsPunished(v8, 2, 0) )
        {
          v9 = CPlayerDB::GetRaceCode(&v11->m_Param);
          v5 = CPlayerDB::GetRaceCode(&v8->m_Param);
          if ( v9 == v5 )
          {
            if ( v8->m_byUserDgr )
            {
              if ( !v11->m_byUserDgr )
                return;
            }
            else if ( v11->m_byUserDgr )
            {
              return;
            }
            if ( !v11->m_pPartyMgr->m_bLock )
            {
              _effect_parameter::GetEff_Have(&v8->m_EP, 53);
              v10 = a3;
              v6 = ((int (__fastcall *)(CPlayer *))v8->vfptr->GetLevel)(v8);
              if ( CPartyPlayer::IsJoinPartyLevel(v11->m_pPartyMgr, v6, v10) )
                wa_PartyJoin(&v11->m_id, &v8->m_id);
              else
                CPlayer::SendMsg_PartyJoinFailLevel(v11);
            }
          }
        }
      }
    }
  }
}
