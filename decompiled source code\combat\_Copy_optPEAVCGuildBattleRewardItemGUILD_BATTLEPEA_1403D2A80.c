/*
 * Function: ??$_Copy_opt@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@Urandom_access_iterator_tag@std@@@std@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1403D2A80
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::_Copy_opt<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::random_access_iterator_tag>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  char *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v9; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *v10; // [sp+30h] [bp+8h]@1
  GUILD_BATTLE::CGuildBattleRewardItem *v11; // [sp+40h] [bp+18h]@1

  v11 = _Dest;
  v10 = _First;
  v6 = &v9;
  for ( i = 6i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 += 4;
  }
  while ( v10 != _Last )
  {
    qmemcpy(&v9, v10, 0x10ui64);
    qmemcpy(v11, &v9, sizeof(GUILD_BATTLE::CGuildBattleRewardItem));
    ++v11;
    ++v10;
  }
  return v11;
}
