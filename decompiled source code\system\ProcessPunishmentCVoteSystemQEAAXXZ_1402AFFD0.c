/*
 * Function: ?ProcessPunishment@CVoteSystem@@QEAAXXZ
 * Address: 0x1402AFFD0
 */

void __fastcall CVoteSystem::ProcessPunishment(CVoteSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int128 v3; // tt@4
  PatriarchElectProcessor *v4; // rax@6
  unsigned __int16 v5; // ax@12
  PatriarchElectProcessor *v6; // rax@13
  int v7; // eax@13
  __int64 v8; // [sp+0h] [bp-F8h]@1
  __int64 _Time; // [sp+38h] [bp-C0h]@4
  unsigned int v10; // [sp+44h] [bp-B4h]@4
  CGameObject *v11; // [sp+48h] [bp-B0h]@4
  char pbyType; // [sp+54h] [bp-A4h]@7
  char v13; // [sp+55h] [bp-A3h]@7
  _pt_result_punishment_zocl v14; // [sp+78h] [bp-80h]@7
  unsigned int dwClientIndex; // [sp+94h] [bp-64h]@7
  CPlayer *v16; // [sp+98h] [bp-60h]@10
  _qry_case_update_punishment v17; // [sp+A8h] [bp-50h]@13
  unsigned __int64 v18; // [sp+E0h] [bp-18h]@4
  CVoteSystem *v19; // [sp+100h] [bp+8h]@1

  v19 = this;
  v1 = &v8;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v18 = (unsigned __int64)&v8 ^ _security_cookie;
  time_11(&_Time);
  *(_QWORD *)&v3 = _Time;
  *((_QWORD *)&v3 + 1) = (unsigned __int128)_Time >> 64;
  v10 = v3 / 60;
  v11 = CMainThread::GetCharW(&g_Main, v19->m_wszCharName);
  if ( v11 && *(_QWORD *)&v11[10].m_ObjID.m_byKind )
  {
    *(_DWORD *)(*(_QWORD *)&v11[10].m_ObjID.m_byKind + 4i64 * v19->m_byPunishType + 1321) = v10;
    v4 = PatriarchElectProcessor::Instance();
    *(_DWORD *)(*(_QWORD *)&v11[10].m_ObjID.m_byKind + 4i64 * v19->m_byPunishType + 1333) = PatriarchElectProcessor::GetCurrPatriarchElectSerial(v4);
  }
  pbyType = 13;
  v13 = 116;
  v14.byType = v19->m_byPunishType;
  strcpy_0(v14.wszCharName, v19->m_wszCharName);
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    v16 = &g_Player + (signed int)dwClientIndex;
    if ( v16->m_bLive )
    {
      if ( CPlayerDB::GetRaceCode(&v16->m_Param) == v19->m_byRaceCode )
      {
        v5 = _pt_result_punishment_zocl::size(&v14);
        CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v14.byType, v5);
      }
    }
  }
  v17.byType = v19->m_byPunishType;
  v17.dwValue = v10;
  v17.dwAvatorSerial = v19->m_dwAvatorSerial;
  v6 = PatriarchElectProcessor::Instance();
  v17.dwElectSerial = PatriarchElectProcessor::GetCurrPatriarchElectSerial(v6);
  strcpy_0(v17.wszCharName, v19->m_wszCharName);
  v7 = _qry_case_update_punishment::size(&v17);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 127, &v17.byType, v7);
}
