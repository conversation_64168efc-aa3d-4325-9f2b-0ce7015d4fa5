/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0___::lower_bound_::_1_::dtor$2
 * Address: 0x140306900
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int__CashShop_fld_const_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const___CashShop_fld_const_____ptr64____0___::lower_bound_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 144) & 1 )
  {
    *(_DWORD *)(a2 + 144) &= 0xFFFFFFFE;
    std::list<std::pair<int const,_CashShop_fld const *>,std::allocator<std::pair<int const,_CashShop_fld const *>>>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<int const ,_CashShop_fld const *>,std::allocator<std::pair<int const ,_CashShop_fld const *> > >::_Iterator<0> *)(a2 + 120));
  }
}
