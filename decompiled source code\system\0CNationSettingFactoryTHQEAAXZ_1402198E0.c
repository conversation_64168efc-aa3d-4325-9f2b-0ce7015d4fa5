/*
 * Function: ??0CNationSettingFactoryTH@@QEAA@XZ
 * Address: 0x1402198E0
 */

void __fastcall CNationSettingFactoryTH::CNationSettingFactoryTH(CNationSettingFactoryTH *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CNationSettingFactoryTH *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNationSettingFactory::CNationSettingFactory((CNationSettingFactory *)&v4->vfptr, 764);
  v4->vfptr = (CNationSettingFactoryVtbl *)&CNationSettingFactoryTH::`vftable';
}
