/*
 * Function: j_?GetMaxSymmetricPlaintextLength@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x14000324C
 */

unsigned __int64 __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::GetMaxSymmetricPlaintextLength(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, unsigned __int64 ciphertextLength)
{
  return CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::GetMaxSymmetricPlaintextLength(
           this,
           ciphertextLength);
}
