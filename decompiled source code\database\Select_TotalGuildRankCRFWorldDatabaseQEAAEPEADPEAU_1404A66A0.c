/*
 * Function: ?Select_TotalGuildRank@CRFWorldDatabase@@QEAAEPEADPEAU_total_guild_rank_info@@@Z
 * Address: 0x1404A66A0
 */

char __fastcall CRFWorldDatabase::Select_TotalGuildRank(CRFWorldDatabase *this, char *szDate, _total_guild_rank_info *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int *v6; // rax@19
  long double *v7; // rax@19
  char *v8; // rax@19
  char *v9; // rax@19
  char *v10; // rax@19
  char *v11; // rax@19
  unsigned int *v12; // rax@19
  char *v13; // rax@19
  CNationSettingManager *v14; // rax@20
  char *v15; // rax@20
  __int64 v16; // rax@21
  __int64 v17; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@19
  char Dest; // [sp+40h] [bp-448h]@6
  SQLLEN v21; // [sp+458h] [bp-30h]@19
  __int16 v22; // [sp+464h] [bp-24h]@11
  int v23; // [sp+468h] [bp-20h]@6
  int v24; // [sp+46Ch] [bp-1Ch]@6
  unsigned __int64 v25; // [sp+478h] [bp-10h]@4
  CRFWorldDatabase *v26; // [sp+490h] [bp+8h]@1
  _total_guild_rank_info *v27; // [sp+4A0h] [bp+18h]@1

  v27 = pkInfo;
  v26 = this;
  v3 = &v17;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v25 = (unsigned __int64)&v17 ^ _security_cookie;
  if ( pkInfo )
  {
    v23 = 0;
    v24 = 0;
    sprintf(
      &Dest,
      "select top %u r.serial, r.GuildPower, g.id, g.race, g.grade, g.MasterSerial, (select name from [dbo].[tbl_base] wh"
      "ere serial = g.MasterSerial ) as MasterName from [dbo].[tbl_GuildRank%s] as r join [dbo].[tbl_Guild] as g on r.ser"
      "ial = g.serial and g.dck = 0 order by race, rank",
      500i64,
      szDate);
    if ( v26->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v26->vfptr, &Dest);
    if ( v26->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v26->vfptr) )
    {
      v22 = SQLExecDirect_0(v26->m_hStmtSelect, &Dest, -3);
      if ( v22 && v22 != 1 )
      {
        if ( v22 == 100 )
        {
          result = 2;
        }
        else
        {
          SQLStmt = v26->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v26->vfptr, v22, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v26->vfptr, v22, v26->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        for ( v27->wCount = 0; ; ++v27->wCount )
        {
          v22 = SQLFetch_0(v26->m_hStmtSelect);
          if ( v22 )
          {
            if ( v22 != 1 )
              break;
          }
          v6 = &v27->list[(unsigned __int64)v27->wCount].dwSerial;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 1u, -18, v6, 0i64, &v21);
          v7 = &v27->list[(unsigned __int64)v27->wCount].dPowerPoint;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 2u, 8, v7, 0i64, &v21);
          v8 = v27->list[(unsigned __int64)v27->wCount].wszGuildName;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)17;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 3u, 1, v8, 17i64, &v21);
          v9 = v27->list[(unsigned __int64)v27->wCount].wszGuildName;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)17;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 2u, 1, v9, 17i64, &v21);
          v10 = &v27->list[(unsigned __int64)v27->wCount].byRace;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 4u, -6, v10, 0i64, &v21);
          v11 = &v27->list[(unsigned __int64)v27->wCount].byGrade;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 5u, -6, v11, 0i64, &v21);
          v12 = &v27->list[(unsigned __int64)v27->wCount].dwMasterSerial;
          StrLen_or_IndPtr = &v21;
          SQLStmt = 0i64;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 6u, -18, v12, 0i64, &v21);
          v13 = v27->list[(unsigned __int64)v27->wCount].wszMasterName;
          StrLen_or_IndPtr = &v21;
          SQLStmt = (void *)17;
          v22 = SQLGetData_0(v26->m_hStmtSelect, 7u, 1, v13, 17i64, &v21);
          if ( !v27->list[(unsigned __int64)v27->wCount].dwMasterSerial )
          {
            v14 = CTSingleton<CNationSettingManager>::Instance();
            v15 = CNationSettingManager::GetNoneString(v14);
            strcpy_0(v27->list[(unsigned __int64)v27->wCount].wszMasterName, v15);
          }
          v16 = v27->list[(unsigned __int64)v27->wCount].byRace;
          if ( v27->list[(unsigned __int64)v27->wCount].byRace >= 3 )
            ++v27->wRaceCnt[3];
          else
            ++v27->wRaceCnt[v27->list[(unsigned __int64)v27->wCount].byRace];
          v27->list[(unsigned __int64)v27->wCount].wRank = 0;
        }
        if ( v26->m_hStmtSelect )
          SQLCloseCursor_0(v26->m_hStmtSelect);
        if ( v26->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v26->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v26->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
