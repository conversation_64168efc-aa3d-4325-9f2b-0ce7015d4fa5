/*
 * Function: SQLWritePrivateProfileStringW
 * Address: 0x1404DB110
 */

int __fastcall SQLWritePrivateProfileStringW(const unsigned __int16 *lpszSection, const unsigned __int16 *lpszEntry, const unsigned __int16 *lpszString, const unsigned __int16 *lpszFilename)
{
  const unsigned __int16 *v4; // rbp@1
  const unsigned __int16 *v5; // rbx@1
  const unsigned __int16 *v6; // rdi@1
  const unsigned __int16 *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszSection;
  v5 = lpszFilename;
  v6 = lpszString;
  v7 = lpszEntry;
  v8 = ODBC___GetSetupProc("SQLWritePrivateProfileStringW");
  if ( v8 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
