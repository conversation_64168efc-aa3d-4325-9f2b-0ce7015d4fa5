/*
 * Function: ?AttackSkill@CPlayerAttack@@QEAAXPEAU_attack_param@@_N@Z
 * Address: 0x14016E140
 */

void __usercall CPlayerAttack::AttackSkill(CPlayerAttack *this@<rcx>, _attack_param *pParam@<rdx>, bool bUseEffBullet@<r8b>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@11
  float v7; // xmm0_4@17
  float v8; // xmm0_4@17
  signed int v9; // eax@23
  float v10; // xmm1_4@23
  CPvpUserAndGuildRankingSystem *v11; // rax@40
  float v12; // xmm0_4@47
  float v13; // xmm0_4@48
  __int64 v14; // rax@50
  __int64 v15; // rdx@50
  float *v16; // rax@51
  __int64 v17; // rcx@51
  CCharacter **v18; // rax@52
  CCharacter **v19; // rcx@52
  __int64 v20; // rdx@52
  CCharacter **v21; // rcx@55
  CCharacter **v22; // rdx@55
  CCharacter **v23; // r8@55
  CCharacter **v24; // rcx@56
  CCharacter **v25; // rdx@56
  CCharacter **v26; // r8@56
  __int64 v27; // [sp+0h] [bp-A8h]@1
  int nEffAttPower[2]; // [sp+20h] [bp-88h]@50
  int bUseEffBulleta[2]; // [sp+28h] [bp-80h]@50
  int v30; // [sp+30h] [bp-78h]@52
  bool v31; // [sp+38h] [bp-70h]@52
  _base_fld *v32; // [sp+40h] [bp-68h]@4
  char v33; // [sp+48h] [bp-60h]@4
  int v34; // [sp+4Ch] [bp-5Ch]@5
  char v35; // [sp+50h] [bp-58h]@8
  float v36; // [sp+54h] [bp-54h]@17
  float v37; // [sp+58h] [bp-50h]@17
  int v38; // [sp+5Ch] [bp-4Ch]@23
  int nAttPower; // [sp+60h] [bp-48h]@35
  int nAttPnt; // [sp+64h] [bp-44h]@35
  char v41; // [sp+68h] [bp-40h]@40
  float v42; // [sp+6Ch] [bp-3Ch]@11
  float v43; // [sp+70h] [bp-38h]@17
  float v44; // [sp+74h] [bp-34h]@17
  float v45; // [sp+78h] [bp-30h]@23
  int v46; // [sp+7Ch] [bp-2Ch]@26
  int v47; // [sp+80h] [bp-28h]@29
  unsigned int dwSerial; // [sp+84h] [bp-24h]@40
  int v49; // [sp+88h] [bp-20h]@40
  char v50; // [sp+8Ch] [bp-1Ch]@40
  int v51; // [sp+90h] [bp-18h]@49
  int *v52; // [sp+98h] [bp-10h]@50
  CPlayerAttack *v53; // [sp+B0h] [bp+8h]@1
  bool v54; // [sp+C0h] [bp+18h]@1

  v54 = bUseEffBullet;
  v53 = this;
  v4 = &v27;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v53->m_nDamagedObjNum = 0;
  v53->m_bIsCrtAtt = 0;
  v53->m_pp = pParam;
  v32 = v53->m_pp->pFld;
  v33 = 1;
  CCharacter::BreakStealth(v53->m_pAttChar);
  if ( v53->m_pp->byEffectCode )
    v34 = *(_DWORD *)&v32[11].m_strCode[0];
  else
    v34 = *(_DWORD *)&v32[11].m_strCode[4 * (v53->m_pp->nLevel - 1)];
  if ( !v53->m_pp->pDst )
    goto LABEL_61;
  v35 = 0;
  if ( _effect_parameter::GetEff_State(&v53->m_pp->pDst->m_EP, 14) )
  {
    v35 = 1;
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v53->m_pp->pDst->m_EP, 27);
    if ( a4 > 0.0 )
    {
      v6 = rand();
      a4 = (float)(v6 % 100);
      v42 = (float)(v6 % 100);
      _effect_parameter::GetEff_Plus(&v53->m_pp->pDst->m_EP, 27);
      if ( a4 > v42 )
        v35 = 1;
    }
  }
  if ( v35 )
  {
    if ( !v53->m_pp->bPassCount
      && !v53->m_pp->nClass
      && !((int (__fastcall *)(CCharacter *))v53->m_pp->pDst->vfptr->GetWeaponClass)(v53->m_pp->pDst) )
    {
      ((void (__fastcall *)(CCharacter *))v53->m_pp->pDst->vfptr->GetAttackRange)(v53->m_pp->pDst);
      v43 = a4;
      ((void (__fastcall *)(CCharacter *))v53->m_pAttChar->vfptr->GetWidth)(v53->m_pAttChar);
      v7 = v43 + (float)(a4 / 2.0);
      v44 = v7;
      _effect_parameter::GetEff_Plus(&v53->m_pp->pDst->m_EP, 4);
      v8 = v44 + v7;
      v36 = v8;
      GetSqrt(v53->m_pp->pDst->m_fCurPos, v53->m_pAttChar->m_fCurPos);
      v37 = v8;
      a4 = v36;
      if ( v36 >= v37 )
      {
        v53->m_DamList[0].m_pChar = v53->m_pp->pDst;
        v53->m_DamList[0].m_nDamage = -1;
        v53->m_nDamagedObjNum = 1;
        CCharacter::SendMsg_AttackActEffect(v53->m_pAttChar, 0, v53->m_pp->pDst);
        return;
      }
    }
    _effect_parameter::GetEff_Plus(&v53->m_pp->pDst->m_EP, 27);
    if ( a4 > 0.0 )
    {
      v53->m_DamList[0].m_pChar = v53->m_pp->pDst;
      v53->m_DamList[0].m_nDamage = 0;
      v53->m_nDamagedObjNum = 1;
      return;
    }
  }
  if ( _effect_parameter::GetEff_State(&v53->m_pp->pDst->m_EP, 8) )
  {
    v33 = 0;
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v53->m_pAttChar->m_EP, 30);
    v45 = a4 + 100.0;
    v9 = ((int (__fastcall *)(CCharacter *))v53->m_pp->pDst->vfptr->GetAvoidRate)(v53->m_pp->pDst);
    v10 = v45 - (float)v9;
    a4 = v10;
    v38 = (signed int)ffloor(v10);
    if ( !v53->m_pAttChar->m_ObjID.m_byID )
    {
      _effect_parameter::GetEff_Plus(&v53->m_pAttChar->m_EP, 40);
      a4 = (float)v38 + v10;
      v38 = (signed int)ffloor(a4);
    }
    if ( v38 <= 0 )
      v46 = 0;
    else
      v46 = v38;
    v38 = v46;
    if ( v46 >= 100 )
      v47 = 100;
    else
      v47 = v38;
    v38 = v47;
    if ( rand() % 100 >= v38 )
      v33 = 0;
  }
  if ( v33 )
  {
LABEL_61:
    nAttPower = v53->m_pp->nAddAttPnt + CPlayerAttack::_CalcSkillAttPnt(v53, 0);
    nAttPnt = v53->m_pp->nAddAttPnt + CPlayerAttack::_CalcSkillAttPnt(v53, v54);
    if ( !v53->m_pAttChar->m_ObjID.m_byID
      && (CHolyStoneSystem::GetDestroyerSerial(&g_HolySys) == v53->m_pAttChar->m_dwObjSerial
       || CPlayer::IsLastAttBuff((CPlayer *)v53->m_pAttChar)) )
    {
      nAttPower = (signed int)ffloor((float)nAttPower * 1.3);
      a4 = (float)nAttPnt * 1.3;
      nAttPnt = (signed int)ffloor(a4);
    }
    if ( !v53->m_pAttPlayer->m_bInGuildBattle )
    {
      dwSerial = CPlayerDB::GetCharSerial(&v53->m_pAttPlayer->m_Param);
      v49 = CPlayerDB::GetRaceCode(&v53->m_pAttPlayer->m_Param);
      v11 = CPvpUserAndGuildRankingSystem::Instance();
      v41 = CPvpUserAndGuildRankingSystem::GetBossType(v11, v49, dwSerial);
      v50 = v41;
      if ( v41 )
      {
        if ( v50 == 2 || v50 == 6 )
        {
          nAttPower = (signed int)ffloor((float)nAttPower * 1.2);
          a4 = (float)nAttPnt * 1.2;
          nAttPnt = (signed int)ffloor(a4);
        }
      }
      else
      {
        nAttPower = (signed int)ffloor((float)nAttPower * 1.3);
        a4 = (float)nAttPnt * 1.3;
        nAttPnt = (signed int)ffloor(a4);
      }
    }
    if ( v53->m_pp->nWpType == 7 )
    {
      _effect_parameter::GetEff_Rate(&v53->m_pAttChar->m_EP, 29);
      v12 = (float)nAttPower * a4;
      nAttPower = (signed int)ffloor(v12);
      _effect_parameter::GetEff_Rate(&v53->m_pAttChar->m_EP, 29);
      nAttPnt = (signed int)ffloor((float)nAttPnt * v12);
    }
    else
    {
      _effect_parameter::GetEff_Rate(&v53->m_pAttChar->m_EP, v53->m_pp->nClass + 2);
      v13 = (float)nAttPower * a4;
      nAttPower = (signed int)ffloor(v13);
      _effect_parameter::GetEff_Rate(&v53->m_pAttChar->m_EP, v53->m_pp->nClass + 2);
      nAttPnt = (signed int)ffloor((float)nAttPnt * v13);
    }
    v51 = v34;
    switch ( v34 )
    {
      case 5:
        v14 = *(_DWORD *)&v32[4].m_strCode[60];
        v15 = *(_DWORD *)&v32[4].m_strCode[60];
        v52 = s_nLimitDist;
        LOBYTE(bUseEffBulleta[0]) = v54;
        nEffAttPower[0] = nAttPnt;
        CAttack::FlashDamageProc(
          (CAttack *)&v53->m_pp,
          s_nLimitDist[v15],
          nAttPower,
          s_nLimitAngle[0][v14],
          nAttPnt,
          v54);
        goto LABEL_58;
      case 4:
      case 6:
        v16 = v53->m_pp->fArea;
        v17 = *(_DWORD *)&v32[4].m_strCode[60];
        LOBYTE(bUseEffBulleta[0]) = v54;
        nEffAttPower[0] = nAttPnt;
        CAttack::AreaDamageProc((CAttack *)&v53->m_pp, s_nLimitRadius[v17], nAttPower, v16, nAttPnt, v54);
        goto LABEL_58;
      case 7:
        v18 = &v53->m_pp->pDst;
        v19 = &v53->m_pp->pDst;
        v20 = *(_DWORD *)&v32[4].m_strCode[60];
        v31 = v54;
        v30 = nAttPnt;
        bUseEffBulleta[0] = *((_DWORD *)v18 + 9);
        nEffAttPower[0] = *((_DWORD *)v19 + 10);
        CAttack::SectorDamageProc(
          (CAttack *)&v53->m_pp,
          *(_DWORD *)&v32[4].m_strCode[60],
          nAttPower,
          s_nLimitAngle[0][v20],
          nEffAttPower[0],
          bUseEffBulleta[0],
          nAttPnt,
          v54);
        goto LABEL_58;
      case 0:
      case 1:
      case 2:
      case 3:
        if ( v53->m_pp->pDst )
        {
          v53->m_DamList[0].m_pChar = v53->m_pp->pDst;
          if ( v54 )
          {
            v21 = &v53->m_pp->pDst;
            v22 = &v53->m_pp->pDst;
            v23 = &v53->m_pp->pDst;
            LOBYTE(bUseEffBulleta[0]) = v53->m_pp->bBackAttack;
            *(_QWORD *)nEffAttPower = *v21;
            v53->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                            v53->m_pAttChar,
                                            nAttPnt,
                                            *((_DWORD *)v23 + 2),
                                            *((_DWORD *)v22 + 3),
                                            *(CCharacter **)nEffAttPower,
                                            bUseEffBulleta[0]);
          }
          else
          {
            v24 = &v53->m_pp->pDst;
            v25 = &v53->m_pp->pDst;
            v26 = &v53->m_pp->pDst;
            LOBYTE(bUseEffBulleta[0]) = v53->m_pp->bBackAttack;
            *(_QWORD *)nEffAttPower = *v24;
            v53->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                            v53->m_pAttChar,
                                            nAttPower,
                                            *((_DWORD *)v26 + 2),
                                            *((_DWORD *)v25 + 3),
                                            *(CCharacter **)nEffAttPower,
                                            bUseEffBulleta[0]);
          }
          v53->m_nDamagedObjNum = 1;
        }
LABEL_58:
        CAttack::CalcAvgDamage((CAttack *)&v53->m_pp);
        break;
      default:
        return;
    }
  }
  else
  {
    v53->m_DamList[0].m_pChar = v53->m_pp->pDst;
    v53->m_DamList[0].m_nDamage = 0;
    v53->m_nDamagedObjNum = 1;
  }
}
