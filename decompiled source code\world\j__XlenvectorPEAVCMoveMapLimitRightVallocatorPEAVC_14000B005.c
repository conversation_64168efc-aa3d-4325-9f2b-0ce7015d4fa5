/*
 * Function: j_?_<PERSON><PERSON>@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@KAXXZ
 * Address: 0x14000B005
 */

void __fastcall __noreturn std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_<PERSON>len(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_<PERSON>len(this);
}
