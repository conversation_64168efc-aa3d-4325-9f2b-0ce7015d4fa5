/*
 * Function: ?Init@CMsgListManager@RACE_BOSS_MSG@@IEAA_NXZ
 * Address: 0x1402A0090
 */

char __fastcall RACE_BOSS_MSG::CMsgListManager::Init(RACE_BOSS_MSG::CMsgListManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@7
  __int64 v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@4
  int j; // [sp+24h] [bp-34h]@4
  RACE_BOSS_MSG::CMsgList *v8; // [sp+28h] [bp-30h]@9
  RACE_BOSS_MSG::CMsgList *v9; // [sp+30h] [bp-28h]@6
  __int64 v10; // [sp+38h] [bp-20h]@4
  RACE_BOSS_MSG::CMsgList *v11; // [sp+40h] [bp-18h]@7
  RACE_BOSS_MSG::CMsgListManager *v12; // [sp+60h] [bp+8h]@1

  v12 = this;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = -2i64;
  v6 = 1;
  for ( j = 0; j < 3; ++j )
  {
    v9 = (RACE_BOSS_MSG::CMsgList *)operator new(0x1F8ui64);
    if ( v9 )
    {
      RACE_BOSS_MSG::CMsgList::CMsgList(v9, j, 4u);
      v11 = (RACE_BOSS_MSG::CMsgList *)v3;
    }
    else
    {
      v11 = 0i64;
    }
    v8 = v11;
    v12->m_pkMsgList[j] = v11;
    if ( !v12->m_pkMsgList[j] )
      v6 = 0;
  }
  return v6;
}
