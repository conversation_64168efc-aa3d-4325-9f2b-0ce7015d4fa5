/*
 * Function: _dynamic_atexit_destructor_for__g_MonsterEventRespawn__
 * Address: 0x1406E8EA0
 */

void __cdecl dynamic_atexit_destructor_for__g_MonsterEventRespawn__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CMonsterEventRespawn::~CMonsterEventRespawn(&g_MonsterEventRespawn);
}
