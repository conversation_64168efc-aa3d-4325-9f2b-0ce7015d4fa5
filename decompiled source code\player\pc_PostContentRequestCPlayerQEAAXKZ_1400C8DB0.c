/*
 * Function: ?pc_PostContentRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400C8DB0
 */

void __fastcall CPlayer::pc_PostContentRequest(CPlayer *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@10
  __int64 v5; // [sp+0h] [bp-178h]@1
  char byTableCode[8]; // [sp+20h] [bp-158h]@9
  unsigned __int16 wItemIndex[2]; // [sp+28h] [bp-150h]@9
  unsigned __int64 dwDur; // [sp+30h] [bp-148h]@9
  unsigned int dwLv; // [sp+38h] [bp-140h]@9
  unsigned int dwGold; // [sp+40h] [bp-138h]@9
  CPostData *v11; // [sp+50h] [bp-128h]@4
  _qry_case_post_content_get v12; // [sp+70h] [bp-108h]@10
  unsigned __int64 v13; // [sp+160h] [bp-18h]@4
  CPlayer *v14; // [sp+180h] [bp+8h]@1
  int nIndex; // [sp+188h] [bp+10h]@1

  nIndex = dwIndex;
  v14 = this;
  v2 = &v5;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  v11 = CPostStorage::GetPostDataFromInx(&v14->m_Param.m_PostStorage, dwIndex);
  if ( v11 )
  {
    if ( CPostData::GetState(v11) != 1 )
    {
      CPostData::SetState(v11, 1);
      CPlayer::UpdatePost(v14, nIndex);
    }
    if ( CPostStorage::IsContentLoad(&v14->m_Param.m_PostStorage, nIndex) )
    {
      dwGold = v11->m_dwGold;
      dwLv = v11->m_dwUpt;
      dwDur = v11->m_dwDur;
      wItemIndex[0] = v11->m_Key.wItemIndex;
      byTableCode[0] = v11->m_Key.byTableCode;
      CPlayer::SendMsg_PostContent(
        v14,
        0,
        nIndex,
        v11->m_wszContent,
        byTableCode[0],
        wItemIndex[0],
        dwDur,
        dwLv,
        dwGold);
    }
    else
    {
      _qry_case_post_content_get::_qry_case_post_content_get(&v12);
      v12.dwMasterSerial = v14->m_pUserDB->m_dwSerial;
      v12.dwSerial = v11->m_dwPSSerial;
      v12.dwIndex = nIndex;
      v4 = _qry_case_post_content_get::size(&v12);
      CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 82, (char *)&v12, v4);
    }
  }
  else
  {
    CPlayer::SendMsg_PostItemGold(v14, 11);
  }
}
