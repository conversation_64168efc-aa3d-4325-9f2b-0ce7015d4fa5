/*
 * Function: ??_ECLuaLooting_Novus_Item@@QEAAPEAXI@Z
 * Address: 0x140405990
 */

void *__fastcall CLuaLooting_Novus_Item::`vector deleting destructor'(CLuaLooting_Novus_Item *this, int a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  void *result; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CLuaLooting_Novus_Item *ptr; // [sp+30h] [bp+8h]@1
  int v7; // [sp+38h] [bp+10h]@1

  v7 = a2;
  ptr = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( a2 & 2 )
  {
    `eh vector destructor iterator'(
      ptr,
      0x58ui64,
      *(_DWORD *)&ptr[-1].m_wLootRange,
      (void (__cdecl *)(void *))CLuaLooting_Novus_Item::~CLuaLooting_Novus_Item);
    if ( v7 & 1 )
      operator delete[](&ptr[-1].m_wLootRange);
    result = &ptr[-1].m_wLootRange;
  }
  else
  {
    CLuaLooting_Novus_Item::~CLuaLooting_Novus_Item(ptr);
    if ( v7 & 1 )
      operator delete(ptr);
    result = ptr;
  }
  return result;
}
