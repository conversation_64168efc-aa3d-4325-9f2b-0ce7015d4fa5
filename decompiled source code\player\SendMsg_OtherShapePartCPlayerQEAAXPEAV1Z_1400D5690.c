/*
 * Function: ?SendMsg_OtherShapePart@CPlayer@@QEAAXPEAV1@@Z
 * Address: 0x1400D5690
 */

void __fastcall CPlayer::SendMsg_OtherShapePart(CPlayer *this, CPlayer *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@8
  __int64 v5; // [sp+0h] [bp-58h]@1
  char pbyType; // [sp+34h] [bp-24h]@8
  char v7; // [sp+35h] [bp-23h]@8
  CPlayer *v8; // [sp+60h] [bp+8h]@1
  CPlayer *pDsta; // [sp+68h] [bp+10h]@1

  pDsta = pDst;
  v8 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( !v8->m_bObserver || pDst->m_byUserDgr )
  {
    if ( v8->m_bLive )
    {
      pbyType = 3;
      v7 = 32;
      v4 = _other_shape_part_zocl::size(&v8->m_bufSpapePart);
      CNetProcess::LoadSendMsg(unk_1414F2088, pDsta->m_ObjID.m_wIndex, &pbyType, (char *)&v8->m_bufSpapePart, v4);
    }
    else
    {
      CPlayer::SendMsg_OtherShapeError(v8, pDst, 0);
    }
  }
}
