/*
 * Function: ?_SearchPlayer@@YAKPEAD@Z
 * Address: 0x1403F8710
 */

unsigned int __fastcall _SearchPlayer(char *szCharName)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int result; // eax@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  CUserDB *v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+28h] [bp-10h]@5
  char *pwszName; // [sp+40h] [bp+8h]@1

  pwszName = szCharName;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = SearchAvatorWithName(g_UserDB, 2532, pwszName);
  if ( v5 )
  {
    v6 = &g_Player + v5->m_idWorld.wIndex;
    if ( v6->m_bLive )
      result = CPlayerDB::GetCharSerial(&v6->m_Param);
    else
      result = -1;
  }
  else
  {
    result = -1;
  }
  return result;
}
