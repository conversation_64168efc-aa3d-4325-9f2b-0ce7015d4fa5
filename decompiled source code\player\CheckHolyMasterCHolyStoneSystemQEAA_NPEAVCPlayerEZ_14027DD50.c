/*
 * Function: ?CheckHolyMaster@CHolyStoneSystem@@QEAA_NPEAVCPlayer@@E@Z
 * Address: 0x14027DD50
 */

char __fastcall CHolyStoneSystem::CheckHolyMaster(CHolyStoneSystem *this, CPlayer *pAtter, char byDestroyStoneRaceCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v6; // al@6
  int v7; // eax@9
  CRaceBuffManager *v8; // rax@9
  long double v9; // xmm0_8@12
  char v10; // al@14
  int v11; // eax@14
  int v12; // eax@14
  char *v13; // rax@14
  int v14; // eax@18
  char *v15; // rax@35
  CRaceBossWinRate *v16; // rax@35
  CPvpUserAndGuildRankingSystem *v17; // rax@35
  CPvpUserAndGuildRankingSystem *v18; // rax@35
  CPvpUserAndGuildRankingSystem *v19; // rax@35
  char *v20; // rax@36
  unsigned __int16 v21; // ax@36
  __int64 v22; // [sp+0h] [bp-178h]@1
  int nChangeReason[2]; // [sp+20h] [bp-158h]@14
  CHolyScheduleData::__HolyScheduleNode *v24; // [sp+30h] [bp-148h]@6
  int v25; // [sp+38h] [bp-140h]@9
  float v26; // [sp+3Ch] [bp-13Ch]@12
  int v27; // [sp+40h] [bp-138h]@14
  char v28; // [sp+44h] [bp-134h]@14
  int j; // [sp+48h] [bp-130h]@14
  CPlayer *v30; // [sp+50h] [bp-128h]@16
  long double v31; // [sp+58h] [bp-120h]@20
  int k; // [sp+60h] [bp-118h]@24
  CPlayer *v33; // [sp+68h] [bp-110h]@27
  int nControlSec; // [sp+70h] [bp-108h]@32
  char pQryData; // [sp+88h] [bp-F0h]@35
  unsigned int v36; // [sp+8Ch] [bp-ECh]@35
  char v37; // [sp+90h] [bp-E8h]@35
  char v38; // [sp+91h] [bp-E7h]@35
  unsigned int v39; // [sp+94h] [bp-E4h]@35
  unsigned int v40; // [sp+98h] [bp-E0h]@35
  unsigned int v41; // [sp+9Ch] [bp-DCh]@35
  _holy_quest_report_wrac v42; // [sp+B8h] [bp-C0h]@36
  char pbyType; // [sp+E4h] [bp-94h]@36
  char v44; // [sp+E5h] [bp-93h]@36
  float v45; // [sp+100h] [bp-78h]@10
  struct CHolyStone *v46; // [sp+108h] [bp-70h]@11
  int v47; // [sp+110h] [bp-68h]@14
  char *v48; // [sp+118h] [bp-60h]@14
  unsigned int v49; // [sp+120h] [bp-58h]@14
  unsigned int v50; // [sp+124h] [bp-54h]@14
  __int64 v51; // [sp+128h] [bp-50h]@14
  char **v52; // [sp+130h] [bp-48h]@14
  __int64 v53; // [sp+138h] [bp-40h]@14
  char **v54; // [sp+140h] [bp-38h]@14
  int v55; // [sp+148h] [bp-30h]@18
  int v56; // [sp+14Ch] [bp-2Ch]@35
  char *szMasterClass; // [sp+150h] [bp-28h]@35
  int v58; // [sp+158h] [bp-20h]@35
  unsigned __int64 v59; // [sp+160h] [bp-18h]@4
  CHolyStoneSystem *v60; // [sp+180h] [bp+8h]@1
  CPlayer *pDestroyer; // [sp+188h] [bp+10h]@1
  char v62; // [sp+190h] [bp+18h]@1

  v62 = byDestroyStoneRaceCode;
  pDestroyer = pAtter;
  v60 = this;
  v3 = &v22;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v59 = (unsigned __int64)&v22 ^ _security_cookie;
  if ( CHolyStoneSystem::GetSceneCode(v60) == 1 )
  {
    v6 = CHolyStoneSystem::GetNumOfTime(v60);
    v24 = CHolyScheduleData::GetIndex(&v60->m_ScheculeData, (unsigned __int8)v6);
    if ( v24 )
    {
      if ( CHolyStoneSystem::GetHolyMasterRace(v60) == -1 )
      {
        CHolyStoneSystem::WriteLogPer10Min_Combat(v60);
        v7 = CPlayerDB::GetRaceCode(&pDestroyer->m_Param);
        CHolyStoneSystem::SetHolyMasterRace(v60, v7);
        CHolyStoneSystem::SetDestroyStoneRace(v60, (unsigned __int8)v62);
        v8 = CRaceBuffManager::Instance();
        CRaceBuffManager::RequestHolyQuestRaceBuff(v8, 2);
        CHolyStoneSystem::CheckKeeperPlusTime(v60);
        v25 = v24->m_nSceneTime[3];
        if ( v60->m_SaveData.m_nStartStoneHP )
        {
          v46 = &g_Stone[CHolyStoneSystem::GetHolyMasterRace(v60)];
          v45 = (float)((int (__fastcall *)(struct CHolyStone *))v46->vfptr->GetHP)(v46)
              / (float)v60->m_SaveData.m_nStartStoneHP;
        }
        else
        {
          v45 = 0.0;
        }
        v26 = v45;
        *(_QWORD *)&v9 = LODWORD(v45);
        if ( v45 >= 1.0 )
        {
          *(_QWORD *)&v9 = LODWORD(FLOAT_1_0);
          v26 = FLOAT_1_0;
        }
        *(float *)&v9 = (float)v25 * v26;
        v27 = (signed int)ffloor(*(float *)&v9);
        v60->m_SaveData.m_dwTerm[1] = v27;
        v10 = CHolyStoneSystem::GetNumOfTime(v60);
        CHolyStoneSystem::SetScene(v60, v10, 2, 0, 5);
        v47 = (unsigned __int8)v62;
        v48 = CPlayerDB::GetCharNameA(&pDestroyer->m_Param);
        v11 = CPlayerDB::GetRaceCode(&pDestroyer->m_Param);
        nChangeReason[0] = v47;
        CLogFile::Write(&v60->m_logQuest, "Create Master >> race:%d, name:%s, stone:%d", (unsigned int)v11, v48);
        v49 = v60->m_SaveData.m_dwTerm[1] / 0x3E8;
        v50 = v60->m_SaveData.m_dwTerm[0] / 0x3E8;
        v12 = CHolyStoneSystem::GetSceneCode(v60);
        nChangeReason[0] = v49;
        CLogFile::Write(
          &v60->m_logQuest,
          ">> Change Schedule : %d -> HS_SCENE_BATTLE_END_WAIT_TIME, PlusSecTime:%d, controlSecTime:%d",
          (unsigned int)v12,
          v50);
        v51 = (unsigned __int8)v62;
        v52 = szRaceCode;
        v53 = CPlayerDB::GetRaceCode(&pDestroyer->m_Param);
        v54 = szRaceCode;
        v13 = CPlayerDB::GetCharNameA(&pDestroyer->m_Param);
        *(_QWORD *)nChangeReason = v52[v51];
        CLogFile::Write(&v60->m_logPer10Min, "WIN : %s (%s), Destroy(%s)", v13, v54[v53]);
        v28 = 3 - (v62 + CPlayerDB::GetRaceCode(&pDestroyer->m_Param));
        CHolyStoneSystem::PeneltyLoseRace(v60, v62);
        CHolyStoneSystem::PeneltyFailRace(v60, v28);
        v60->m_pkDestroyer = pDestroyer;
        v60->m_SaveData.m_dwDestroyerSerial = pDestroyer->m_dwObjSerial;
        v60->m_SaveData.m_eDestroyerState = 2;
        CHolyStoneSystem::SendNotifyHolyStoneDestroyedToRaceBoss(v60);
        for ( j = 0; j < 2532; ++j )
        {
          v30 = &g_Player + j;
          if ( v30->m_bOper && v30->m_bLive )
          {
            v55 = CHolyStoneSystem::GetHolyMasterRace(v60);
            v14 = CPlayerDB::GetRaceCode(&v30->m_Param);
            if ( v55 == v14 && v30->m_byHSKQuestCode != 100 )
            {
              CPlayer::GetPvpPointLeak(v30);
              v31 = v9;
              v9 = 0.0;
              if ( v31 < 0.0 )
              {
                v9 = v31;
                abs(v31);
                CPlayer::AlterPvPPoint(v30, v9, holy_award, 0xFFFFFFFF);
              }
            }
            CPlayer::SetPvpPointLeak(v30, 0.0);
          }
        }
        for ( k = 0; k < 2532; ++k )
        {
          v33 = &g_Player + k;
          if ( v33->m_bLive && (v33->m_byHSKQuestCode != 100 || pDestroyer == v33) )
            CPlayer::HSKQuestEnd_Att(v33, v62, pDestroyer);
        }
        nControlSec = (v60->m_SaveData.m_dwTerm[1] + v60->m_SaveData.m_dwTerm[0] + v24->m_nSceneTime[0]) / 0x3E8;
        CHolyStoneSystem::SendMsg_CreateHolyMaster(v60, pDestroyer, nControlSec);
        if ( v60->m_pkDestroyer->m_Param.m_pGuild )
          v60->m_SaveData.m_dwDestroyerGuildSerial = v60->m_pkDestroyer->m_Param.m_pGuild->m_dwSerial;
        else
          v60->m_SaveData.m_dwDestroyerGuildSerial = -1;
        CHolyStoneSystem::SetEffectToDestroyerGuildMember(v60);
        v56 = ((int (__fastcall *)(CPlayer *))pDestroyer->vfptr->GetLevel)(pDestroyer);
        szMasterClass = pDestroyer->m_Param.m_pClassData->m_strCode;
        v15 = CPlayerDB::GetCharNameW(&pDestroyer->m_Param);
        CHolyStoneSystem::SendSMS_CompleteQuest(v60, v62, v15, nControlSec, szMasterClass, v56);
        CHolyStoneSystem::SendMsg_EndBattle(v60, v62);
        CHolyStoneSystem::DestroyHolyStone(v60);
        CHolyStoneSystem::SendMsg_ExitStone(v60);
        CHolyStoneSystem::RecoverPvpCash(v60);
        v58 = CPlayerDB::GetRaceCode(&pDestroyer->m_Param);
        v16 = CRaceBossWinRate::Instance();
        CRaceBossWinRate::UpdateWinCnt(v16, v58);
        pQryData = v60->m_SaveData.m_byNumOfTime;
        v36 = GetKorLocalTime();
        v37 = CPlayerDB::GetRaceCode(&pDestroyer->m_Param);
        v38 = v62;
        v17 = CPvpUserAndGuildRankingSystem::Instance();
        v39 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v17, 0, 0);
        v18 = CPvpUserAndGuildRankingSystem::Instance();
        v40 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v18, 1, 0);
        v19 = CPvpUserAndGuildRankingSystem::Instance();
        v41 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v19, 2, 0);
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -119, &pQryData, 24);
        if ( CMainThread::IsReleaseServiceMode(&g_Main) )
        {
          v42.byRaceCode = CPlayerDB::GetRaceCode(&pDestroyer->m_Param);
          v20 = CPlayerDB::GetCharNameW(&pDestroyer->m_Param);
          strcpy_0(v42.wszCharName, v20);
          v42.byDestroyedRaceCode = v62;
          pbyType = 50;
          v44 = 101;
          v21 = _holy_quest_report_wrac::size(&v42);
          CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &v42.byRaceCode, v21);
        }
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
