/*
 * Function: ?Init@CCouponMgr@@QEAAXG@Z
 * Address: 0x1403FD6C0
 */

void __fastcall CCouponMgr::Init(CCouponMgr *this, unsigned __int16 wIdx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int v6; // [sp+20h] [bp-18h]@5
  CCouponMgr *v7; // [sp+40h] [bp+8h]@1
  unsigned __int16 v8; // [sp+48h] [bp+10h]@1

  v8 = wIdx;
  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CTSingleton<CNationSettingManager>::Instance();
  if ( CNationSettingManager::GetNationCode(v4) == 410 )
  {
    CMyTimer::BeginTimer(&v7->m_tmrCheckConnMin, 0xEA60u);
    v6 = v7->m_dwContTime % 0x3C;
    CMyTimer::BeginTimer(&v7->m_tmrCouponEnableTime, 0x36EE80u);
    if ( v6 )
      CMyTimer::CountingAddTickOld(&v7->m_tmrCouponEnableTime, 60000 * v6);
    CCouponMgr::InitCuponInfo(v7);
    CCouponMgr::SendMsg_InPcBangTime(v7, v8);
    CCouponMgr::SendMsg_CouponEnsure(v7, v8, v7->m_byRemainTime);
    CCouponMgr::SendMsg_RemainCouponInform(v7, v8, 5 - v7->m_byReceiveCoupon);
    v7->m_bTimeReset = 0;
  }
}
