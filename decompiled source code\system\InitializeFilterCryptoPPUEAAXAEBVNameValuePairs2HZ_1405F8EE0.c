/*
 * Function: ?Initialize@Filter@CryptoPP@@UEAAXAEBVNameValuePairs@2@H@Z
 * Address: 0x1405F8EE0
 */

int __fastcall CryptoPP::Filter::Initialize(__int64 a1, __int64 a2, unsigned int a3)
{
  __int64 v4; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+38h] [bp+10h]@1
  unsigned int v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = a1;
  *(_DWORD *)(a1 + 40) = 0;
  (*(void (**)(void))(*(_QWORD *)a1 + 56i64))();
  return CryptoPP::Filter::PropagateInitialize(v4, v5, v6);
}
