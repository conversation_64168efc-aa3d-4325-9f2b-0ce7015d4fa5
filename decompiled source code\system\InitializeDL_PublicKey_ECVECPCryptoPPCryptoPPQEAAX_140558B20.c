/*
 * Function: ?Initialize@?$DL_PublicKey_EC@VECP@CryptoPP@@@CryptoPP@@QEAAXAEBV?$DL_GroupParameters_EC@VECP@CryptoPP@@@2@AEBUECPPoint@2@@Z
 * Address: 0x140558B20
 */

int __fastcall CryptoPP::DL_PublicKey_EC<CryptoPP::ECP>::Initialize(__int64 a1, CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *a2, __int64 a3)
{
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *v3; // rax@1
  __int64 v5; // [sp+30h] [bp+8h]@1
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *__that; // [sp+38h] [bp+10h]@1
  __int64 v7; // [sp+40h] [bp+18h]@1

  v7 = a3;
  __that = a2;
  v5 = a1;
  v3 = CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>,CryptoPP::OID>::AccessGroupParameters((CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>,CryptoPP::OID> *)(a1 + 8));
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::operator=(v3, __that);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v5 + 24i64))(v5, v7);
}
