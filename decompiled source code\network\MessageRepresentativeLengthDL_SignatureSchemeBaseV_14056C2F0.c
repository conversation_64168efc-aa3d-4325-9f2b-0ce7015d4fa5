/*
 * Function: ?MessageRepresentativeLength@?$DL_SignatureSchemeBase@VPK_Signer@CryptoPP@@V?$DL_PrivateKey@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@IEBA_KXZ
 * Address: 0x14056C2F0
 */

unsigned __int64 __fastcall CryptoPP::DL_SignatureSchemeBase<CryptoPP::P<PERSON>_Signer,CryptoPP::DL_PrivateKey<CryptoPP::EC2NPoint>>::MessageRepresentativeLength(__int64 a1)
{
  CryptoPP *v1; // rax@1

  LODWORD(v1) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::P<PERSON>_Sign<PERSON>,CryptoPP::DL_PrivateKey<CryptoPP::EC2NPoint>>::MessageRepresentativeBitLength(a1);
  return CryptoPP::BitsToBytes(v1);
}
