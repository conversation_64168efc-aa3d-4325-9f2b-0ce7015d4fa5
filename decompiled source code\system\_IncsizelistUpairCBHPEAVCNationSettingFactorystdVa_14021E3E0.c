/*
 * Function: ?_Incsize@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@IEAAX_K@Z
 * Address: 0x14021E3E0
 */

void __fastcall std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Incsize(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *this, unsigned __int64 _Count)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-B8h]@1
  std::length_error v5; // [sp+20h] [bp-98h]@5
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@5
  unsigned __int8 v7; // [sp+98h] [bp-20h]@5
  __int64 v8; // [sp+A0h] [bp-18h]@4
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *v9; // [sp+C0h] [bp+8h]@1
  unsigned __int64 v10; // [sp+C8h] [bp+10h]@1

  v10 = _Count;
  v9 = this;
  v2 = &v4;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = -2i64;
  if ( std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::max_size(v9)
     - v9->_Mysize < _Count )
  {
    memset(&v7, 0, sizeof(v7));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &_Message,
      "list<T> too long",
      v7);
    std::length_error::length_error(&v5, &_Message);
    CxxThrowException_0(&v5, &TI3_AVlength_error_std__);
  }
  v9->_Mysize += v10;
}
