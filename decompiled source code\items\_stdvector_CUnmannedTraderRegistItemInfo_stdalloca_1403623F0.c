/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Insert_n_::_1_::catch$0
 * Address: 0x1403623F0
 */

void __fastcall __noreturn std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 400),
    *(CUnmannedTraderRegistItemInfo **)(a2 + 176),
    *(CUnmannedTraderRegistItemInfo **)(a2 + 184));
  std::allocator<CUnmannedTraderRegistItemInfo>::deallocate(
    (std::allocator<CUnmannedTraderRegistItemInfo> *)(*(_QWORD *)(v2 + 400) + 8i64),
    *(CUnmannedTraderRegistItemInfo **)(v2 + 176),
    *(_QWORD *)(v2 + 168));
  CxxThrowException_0(0i64, 0i64);
}
