/**
 * @file AccountServerLogin.cpp
 * @brief Implementation of account server login functionality
 * 
 * Refactored from decompiled source: AccountServerLoginCMainThreadQEAAXXZ_1401F8140.c
 * Original function: CMainThread::AccountServerLogin
 * 
 * <AUTHOR> for VS2022 C++20 compatibility
 * @date 2024
 */

#include "../Headers/AccountServerLogin.h"
#include <iostream>
#include <cstring>
#include <cassert>
#include <chrono>
#include <thread>
#include <algorithm>
#include <sstream>
#include <iomanip>

// Forward declaration for CMainThread
class CMainThread {
public:
    char m_szWorldName[256];  // World name buffer
};

// External size calculation function
struct _open_world_request_wrac {
    static uint16_t size(void* data);
};

/**
 * @brief WorldConnectionInfo constructor
 */
WorldConnectionInfo::WorldConnectionInfo() {
    Reset();
}

/**
 * @brief Reset connection info to default values
 */
void WorldConnectionInfo::Reset() {
    worldName.clear();
    gateIP.clear();
    ipAddress = 0;
    std::memset(hashVerify, 0, sizeof(hashVerify));
    connectionType = 1;
    protocolVersion = 1;
}

/**
 * @brief Check if connection info is valid
 * @return true if valid, false otherwise
 */
bool WorldConnectionInfo::IsValid() const {
    return !worldName.empty() && ipAddress != 0;
}

/**
 * @brief Constructor
 */
AccountServerLogin::AccountServerLogin()
    : m_securityCookie(0), m_mainThread(nullptr) {
    // Initialize with current timestamp for tracking
    m_connectionInfo.Reset();
}

/**
 * @brief Destructor
 */
AccountServerLogin::~AccountServerLogin() = default;

/**
 * @brief Execute account server login
 * 
 * Performs the account server login process for the specified main thread.
 * This includes reading configuration, resolving IP addresses, and sending
 * the login request to the account server.
 * 
 * @param mainThread Pointer to CMainThread instance
 * @return AccountServerLoginResult indicating success or failure
 */
AccountServerLoginResult AccountServerLogin::ExecuteLogin(CMainThread* mainThread) {
    auto startTime = std::chrono::steady_clock::now();

    try {
        if (!mainThread) {
            SetLastError("Invalid CMainThread pointer");
            return AccountServerLoginResult::SystemError;
        }

        m_mainThread = mainThread;

        // Security cookie setup (equivalent to original stack protection)
        m_securityCookie = reinterpret_cast<uint64_t>(this) ^ _security_cookie;

        std::cout << "[INFO] Starting account server login process for world: "
                  << std::string(mainThread->m_szWorldName) << std::endl;
        
        // Initialize connection information
        if (!InitializeConnectionInfo(mainThread)) {
            return AccountServerLoginResult::InvalidWorldName;
        }
        
        // Read configuration from WorldInfo.ini
        if (!ReadConfiguration()) {
            return AccountServerLoginResult::ConfigurationError;
        }
        
        // Prepare hash verification data
        if (!PrepareHashVerification()) {
            return AccountServerLoginResult::SecurityError;
        }
        
        // Send login request to account server
        if (!SendLoginRequest()) {
            return AccountServerLoginResult::NetworkError;
        }
        
        // Send cash database DSN request
        if (!SendCashDBRequest()) {
            return AccountServerLoginResult::NetworkError;
        }
        
        // Verify security cookie (equivalent to original stack protection check)
        if (!ValidateSecurityCookie()) {
            SetLastError("Security cookie verification failed - stack corruption detected");
            return AccountServerLoginResult::SecurityError;
        }

        auto endTime = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);

        std::cout << "[INFO] Account server login completed successfully in "
                  << duration.count() << "ms" << std::endl;
        return AccountServerLoginResult::Success;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Exception during account server login: " << e.what();
        SetLastError(oss.str());
        return AccountServerLoginResult::SystemError;
    }
}

/**
 * @brief Legacy AccountServerLogin function for backward compatibility
 * 
 * Maintains the original function signature for existing code.
 * 
 * @param mainThread Pointer to CMainThread instance
 */
void AccountServerLogin::AccountServerLogin_Legacy(CMainThread* mainThread) {
    try {
        AccountServerLogin loginHandler;
        AccountServerLoginResult result = loginHandler.ExecuteLogin(mainThread);
        
        if (result != AccountServerLoginResult::Success) {
            std::cerr << "[ERROR] Account server login failed: " 
                      << loginHandler.GetLastError() << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in AccountServerLogin_Legacy: " << e.what() << std::endl;
    } catch (...) {
        std::cerr << "[ERROR] Unknown exception in AccountServerLogin_Legacy" << std::endl;
    }
}

/**
 * @brief Initialize world connection information
 * @param mainThread Pointer to CMainThread instance
 * @return true if successful, false otherwise
 */
bool AccountServerLogin::InitializeConnectionInfo(CMainThread* mainThread) {
    try {
        m_connectionInfo.Reset();
        
        // Copy world name from main thread (equivalent to strcpy_0(&Dest, v13->m_szWorldName))
        m_connectionInfo.worldName = std::string(mainThread->m_szWorldName);
        
        if (m_connectionInfo.worldName.empty()) {
            SetLastError("World name is empty");
            return false;
        }
        
        std::cout << "[INFO] Initialized connection info for world: "
                  << m_connectionInfo.worldName << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Failed to initialize connection info: " << e.what();
        SetLastError(oss.str());
        return false;
    }
}

/**
 * @brief Read configuration from WorldInfo.ini
 * @return true if successful, false otherwise
 */
bool AccountServerLogin::ReadConfiguration() {
    try {
        char gateIPBuffer[MAX_IP_STRING_LENGTH];
        
        // Read GateIP from configuration file
        int result = GetPrivateProfileStringA(
            CONFIG_SECTION,
            CONFIG_KEY_GATE_IP,
            CONFIG_DEFAULT_IP,
            gateIPBuffer,
            static_cast<uint32_t>(MAX_IP_STRING_LENGTH),
            CONFIG_FILE_PATH
        );
        
        if (result == 0) {
            SetLastError("Failed to read configuration file");
            return false;
        }
        
        m_connectionInfo.gateIP = std::string(gateIPBuffer);
        
        // Resolve IP address
        m_connectionInfo.ipAddress = ResolveIPAddress(m_connectionInfo.gateIP);
        
        if (m_connectionInfo.ipAddress == 0) {
            SetLastError("Failed to resolve IP address");
            return false;
        }
        
        std::cout << "[INFO] Configuration read successfully - GateIP: "
                  << m_connectionInfo.gateIP << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Failed to read configuration: " << e.what();
        SetLastError(oss.str());
        return false;
    }
}

/**
 * @brief Resolve IP address
 * @param gateIP Gate IP string from configuration
 * @return Resolved IP address, or 0 if failed
 */
uint32_t AccountServerLogin::ResolveIPAddress(const std::string& gateIP) {
    try {
        // Check if GateIP is the default value "X"
        if (gateIP == CONFIG_DEFAULT_IP) {
            // Use local IP address (equivalent to GetIPAddress())
            return GetIPAddress();
        } else {
            // Convert string IP to address (equivalent to inet_addr(&ReturnedString))
            return inet_addr(gateIP.c_str());
        }
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Failed to resolve IP address: " << e.what();
        SetLastError(oss.str());
        return 0;
    }
}

/**
 * @brief Prepare hash verification data
 * @return true if successful, false otherwise
 */
bool AccountServerLogin::PrepareHashVerification() {
    try {
        // Copy hash verification data (equivalent to memcpy_s(&Dst, 0x20ui64, g_cbHashVerify, 0x20ui64))
        std::memcpy(m_connectionInfo.hashVerify, g_cbHashVerify, HASH_VERIFY_SIZE);
        
        // Set connection type and protocol version
        m_connectionInfo.connectionType = 1;
        m_connectionInfo.protocolVersion = 1;
        
        std::cout << "[INFO] Hash verification data prepared" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Failed to prepare hash verification: " << e.what();
        SetLastError(oss.str());
        return false;
    }
}

/**
 * @brief Send login request to account server
 * @return true if successful, false otherwise
 */
bool AccountServerLogin::SendLoginRequest() {
    try {
        // Calculate message size (equivalent to _open_world_request_wrac::size)
        uint16_t messageSize = _open_world_request_wrac::size(&m_connectionInfo);
        
        // Send login message (equivalent to CNetProcess::LoadSendMsg)
        void* result = CNetProcess_LoadSendMsg(
            unk_1414F2090,
            0,
            &m_connectionInfo.connectionType,
            &m_connectionInfo,
            messageSize
        );
        
        if (!result) {
            SetLastError("Failed to send login request");
            return false;
        }
        
        std::cout << "[INFO] Login request sent to account server" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Failed to send login request: " << e.what();
        SetLastError(oss.str());
        return false;
    }
}

/**
 * @brief Send cash database DSN request
 * @return true if successful, false otherwise
 */
bool AccountServerLogin::SendCashDBRequest() {
    try {
        // Get nation setting manager instance
        CNationSettingManager* nationManager = CTSingleton_CNationSettingManager_Instance();
        
        if (!nationManager) {
            SetLastError("Failed to get nation setting manager");
            return false;
        }
        
        // Send cash database DSN request
        CNationSettingManager_SendCashDBDSNRequest(nationManager);
        
        std::cout << "[INFO] Cash database DSN request sent" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        std::ostringstream oss;
        oss << "Failed to send cash DB request: " << e.what();
        SetLastError(oss.str());
        return false;
    }
}

/**
 * @brief Get the last error message
 * @return string containing the last error message
 */
std::string AccountServerLogin::GetLastError() const {
    return m_lastError;
}

/**
 * @brief Get world connection information
 * @return const reference to world connection info
 */
const WorldConnectionInfo& AccountServerLogin::GetConnectionInfo() const {
    return m_connectionInfo;
}

/**
 * @brief Set the last error message
 * @param error Error message
 */
void AccountServerLogin::SetLastError(const std::string& error) {
    m_lastError = error;
}

/**
 * @brief Validate security cookie
 * @return true if valid, false if corrupted
 */
bool AccountServerLogin::ValidateSecurityCookie() const {
    return (reinterpret_cast<uint64_t>(this) ^ _security_cookie) == m_securityCookie;
}

/**
 * @brief Convert AccountServerLoginResult enum to string for logging
 * @param result The login result
 * @return String representation of the result
 */
std::string AccountServerLoginResultToString(AccountServerLoginResult result) {
    switch (result) {
        case AccountServerLoginResult::Success: return "Success";
        case AccountServerLoginResult::Failure: return "Failure";
        case AccountServerLoginResult::InvalidWorldName: return "InvalidWorldName";
        case AccountServerLoginResult::NetworkError: return "NetworkError";
        case AccountServerLoginResult::ConfigurationError: return "ConfigurationError";
        case AccountServerLoginResult::SecurityError: return "SecurityError";
        case AccountServerLoginResult::SystemError: return "SystemError";
        default: return "Unknown";
    }
}
