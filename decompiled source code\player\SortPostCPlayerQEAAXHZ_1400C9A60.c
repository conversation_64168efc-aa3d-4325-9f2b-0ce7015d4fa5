/*
 * Function: ?SortPost@CPlayer@@QEAAXH@Z
 * Address: 0x1400C9A60
 */

void __fastcall CPlayer::SortPost(CPlayer *this, int nNumber)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int dwIndex; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+40h] [bp+8h]@1
  int v7; // [sp+48h] [bp+10h]@1

  v7 = nNumber;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( dwIndex = 0; (signed int)dwIndex < 50; ++dwIndex )
  {
    if ( (unsigned __int8)CPostData::GetState((CPostData *)&v6->m_Param.m_PostStorage + (signed int)dwIndex) != 255
      && v6->m_Param.m_PostStorage.m_PostData[dwIndex].m_nNumber > v7 )
    {
      --v6->m_Param.m_PostStorage.m_PostData[dwIndex].m_nNumber;
      CPlayer::UpdatePost(v6, dwIndex);
    }
  }
}
