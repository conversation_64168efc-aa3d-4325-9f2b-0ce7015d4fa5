/*
 * Function: ?Save@CNormalGuildBattleManager@GUILD_BATTLE@@QEAA_NK@Z
 * Address: 0x1403D3D10
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Save(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@10
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleManager *v7; // [sp+30h] [bp+8h]@1
  unsigned int v8; // [sp+38h] [bp+10h]@1

  v8 = dwID;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_ppkNormalBattle && v7->m_uiMaxBattleCnt > dwID )
  {
    if ( v7->m_ppkNormalBattle[dwID] )
    {
      if ( GUILD_BATTLE::CNormalGuildBattle::Save(v7->m_ppkNormalBattle[dwID]) )
      {
        result = 1;
      }
      else
      {
        v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(v5, "CNormalGuildBattleManager::Save( %u ) Fail!", v8);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
