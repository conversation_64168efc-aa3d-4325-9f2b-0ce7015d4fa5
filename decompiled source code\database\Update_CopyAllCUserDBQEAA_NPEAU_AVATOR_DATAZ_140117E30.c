/*
 * Function: ?Update_CopyAll@CUserDB@@QEAA_NPEAU_AVATOR_DATA@@@Z
 * Address: 0x140117E30
 */

char __usercall CUserDB::Update_CopyAll@<al>(CUserDB *this@<rcx>, _AVATOR_DATA *pSrc@<rdx>, signed __int64 a3@<rax>)
{
  void *v3; // rsp@1
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-30h] [bp-91B8h]@1
  _AVATOR_DATA Dst; // [sp+0h] [bp-9188h]@4
  char v9; // [sp+9170h] [bp-18h]@4
  unsigned __int64 v10; // [sp+9178h] [bp-10h]@4
  CUserDB *v11; // [sp+9190h] [bp+8h]@1
  _AVATOR_DATA *Src; // [sp+9198h] [bp+10h]@1

  Src = pSrc;
  v11 = this;
  v3 = alloca(a3);
  v4 = &v7;
  for ( i = 9324i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = (unsigned __int64)&v7 ^ _security_cookie;
  _AVATOR_DATA::_AVATOR_DATA(&Dst);
  memcpy_0(&Dst, &v11->m_AvatorData, 0x915Fui64);
  memcpy_0(&v11->m_AvatorData, Src, 0x915Fui64);
  strcpy_0(v11->m_AvatorData.dbAvator.m_wszAvatorName, Dst.dbAvator.m_wszAvatorName);
  v11->m_AvatorData.dbAvator.m_dwRecordNum = Dst.dbAvator.m_dwRecordNum;
  v11->m_AvatorData.dbAvator.m_byRaceSexCode = Dst.dbAvator.m_byRaceSexCode;
  v11->m_AvatorData.dbAvator.m_bySlotIndex = Dst.dbAvator.m_bySlotIndex;
  v11->m_AvatorData.dbAvator.m_dwBaseShape = Dst.dbAvator.m_dwBaseShape;
  v11->m_AvatorData.dbAvator.m_dwLastConnTime = Dst.dbAvator.m_dwLastConnTime;
  v11->m_AvatorData.dbAvator.m_byMapCode = Dst.dbAvator.m_byMapCode;
  memcpy_0(v11->m_AvatorData.dbAvator.m_fStartPos, Dst.dbAvator.m_fStartPos, 0xCui64);
  v11->m_AvatorData.dbAvator.m_dwTotalPlayMin = Dst.dbAvator.m_dwTotalPlayMin;
  v11->m_AvatorData.dbAvator.m_dwStartPlayTime = Dst.dbAvator.m_dwStartPlayTime;
  v11->m_bNoneUpdateData = 1;
  v11->m_bDataUpdate = 1;
  v9 = 1;
  _AVATOR_DATA::~_AVATOR_DATA(&Dst);
  return v9;
}
