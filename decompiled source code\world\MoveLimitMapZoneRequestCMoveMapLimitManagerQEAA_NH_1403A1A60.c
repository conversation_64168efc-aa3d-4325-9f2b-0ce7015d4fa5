/*
 * Function: ?MoveLimitMapZoneRequest@CMoveMapLimitManager@@QEAA_NHPEAD@Z
 * Address: 0x1403A1A60
 */

char __fastcall CMoveMapLimitManager::MoveLimitMapZoneRequest(CMoveMapLimitManager *this, int iUserInx, char *pRequest)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  CMapData *v6; // rax@8
  __int64 v7; // [sp+0h] [bp-58h]@1
  unsigned int dwStoreRecordIndex; // [sp+20h] [bp-38h]@8
  int iUserInxa; // [sp+28h] [bp-30h]@8
  char *pRequesta; // [sp+30h] [bp-28h]@8
  CPlayer *v11; // [sp+40h] [bp-18h]@4
  char *v12; // [sp+48h] [bp-10h]@8
  CMoveMapLimitManager *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = &g_Player + iUserInx;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    v12 = pRequest;
    v6 = v11->m_pCurMap;
    pRequesta = pRequest;
    iUserInxa = iUserInx;
    dwStoreRecordIndex = *(_DWORD *)pRequest;
    CMoveMapLimitManager::Request(v13, 0, 2, v6->m_nMapCode, dwStoreRecordIndex, iUserInx, pRequest);
    result = 1;
  }
  return result;
}
