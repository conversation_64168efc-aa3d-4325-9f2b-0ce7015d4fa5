/*
 * Function: ?PostSendRequest@CPostSystemManager@@QEAA_NPEAVCPlayer@@PEAD11PEAU_STORAGE_POS_INDIV@@KE@Z
 * Address: 0x1403252A0
 */

char __fastcall CPostSystemManager::PostSendRequest(CPostSystemManager *this, CPlayer *pOne, char *wszRecvName, char *wszTitle, char *wszContent, _STORAGE_POS_INDIV *pItemInfo, unsigned int dwGold, char byRace)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  char *v11; // rax@20
  char *v12; // rax@22
  char *v13; // rax@24
  char *v14; // rax@45
  CUserDB *v15; // rcx@45
  __int64 v16; // [sp+0h] [bp-158h]@1
  _STORAGE_LIST::_db_con **pItem; // [sp+20h] [bp-138h]@20
  bool bSend[8]; // [sp+28h] [bp-130h]@45
  _INVENKEY Key; // [sp+30h] [bp-128h]@45
  unsigned __int64 dwDur; // [sp+38h] [bp-120h]@45
  unsigned int dwUpt; // [sp+40h] [bp-118h]@45
  unsigned int v22; // [sp+48h] [bp-110h]@45
  unsigned int dwPSSerial; // [sp+50h] [bp-108h]@45
  char bySendRace; // [sp+58h] [bp-100h]@45
  char bySenderDgr; // [sp+60h] [bp-F8h]@45
  char v26; // [sp+70h] [bp-E8h]@6
  _STORAGE_LIST::_db_con *v27; // [sp+88h] [bp-D0h]@4
  int j; // [sp+94h] [bp-C4h]@7
  CPlayer *v29; // [sp+98h] [bp-C0h]@10
  int v30; // [sp+A0h] [bp-B8h]@31
  _STORAGE_LIST::_db_con Dst; // [sp+B8h] [bp-A0h]@31
  char v32; // [sp+F4h] [bp-64h]@31
  _INVENKEY v33; // [sp+104h] [bp-54h]@43
  unsigned __int64 v34; // [sp+118h] [bp-40h]@43
  unsigned int v35; // [sp+120h] [bp-38h]@43
  unsigned __int64 lnUID; // [sp+128h] [bp-30h]@43
  unsigned int pdwOutIndex; // [sp+134h] [bp-24h]@45
  CPostData *v38; // [sp+148h] [bp-10h]@45
  CPostSystemManager *v39; // [sp+160h] [bp+8h]@1
  CPlayer *pOnea; // [sp+168h] [bp+10h]@1
  char *Str1; // [sp+170h] [bp+18h]@1
  char *wszStr; // [sp+178h] [bp+20h]@1

  wszStr = wszTitle;
  Str1 = wszRecvName;
  pOnea = pOne;
  v39 = this;
  v8 = &v16;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v27 = 0i64;
  if ( !pOne->m_bPostLoad )
  {
    CPlayer::SendMsg_PostSendReply(pOne, 8);
    return 0;
  }
  v26 = CPostSystemManager::CheckRegister(v39, pOne, pItemInfo, dwGold, &v27);
  if ( v26 != 17 )
  {
    for ( j = 0; j < 2532; ++j )
    {
      v29 = &g_Player + j;
      if ( v29 && v29->m_bOper && v29->m_bLive && !strcmp_0(Str1, v29->m_pUserDB->m_AvatorData.dbAvator.m_wszAvatorName) )
      {
        if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v29->m_id.wIndex) == 99 )
          v26 = 18;
        break;
      }
    }
  }
  if ( !IsSQLValidString(Str1) )
  {
    v11 = CPlayerDB::GetCharNameA(&pOnea->m_Param);
    pItem = (_STORAGE_LIST::_db_con **)Str1;
    CLogFile::Write(
      &stru_1799C8E78,
      "CPostSystemManager::PostSendRequest() : %u(%s) ::IsSQLValidString( wszRecvName(%s) ) Invalid!",
      pOnea->m_dwObjSerial,
      v11);
    v26 = 16;
  }
  if ( !IsSQLValidString(wszStr) )
  {
    v12 = CPlayerDB::GetCharNameA(&pOnea->m_Param);
    pItem = (_STORAGE_LIST::_db_con **)wszStr;
    CLogFile::Write(
      &stru_1799C8E78,
      "CPostSystemManager::PostSendRequest() : %u(%s) ::IsSQLValidString( wszTitle(%s) ) Invalid!",
      pOnea->m_dwObjSerial,
      v12);
    v26 = 16;
  }
  if ( !IsSQLValidString(wszContent) )
  {
    v13 = CPlayerDB::GetCharNameA(&pOnea->m_Param);
    pItem = (_STORAGE_LIST::_db_con **)wszContent;
    CLogFile::Write(
      &stru_1799C8E78,
      "CPostSystemManager::PostSendRequest() : %u(%s) ::IsSQLValidString( wszContent(%s) ) Invalid!",
      pOnea->m_dwObjSerial,
      v13);
    v26 = 16;
  }
  if ( !strlen_0(wszStr) || !strlen_0(Str1) )
    v26 = 8;
  if ( !strlen_0(wszContent) )
    strcpy_0(wszContent, " ");
  if ( !v26 )
  {
    v30 = 0;
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    v32 = 0;
    if ( v27 )
    {
      v32 = 1;
      memcpy_0(&Dst, v27, 0x32ui64);
      v30 = IsOverLapItem((unsigned __int8)Dst.m_byTableCode);
      if ( v30 )
      {
        Dst.m_dwDur = pItemInfo->byNum;
        CPlayer::Emb_AlterDurPoint(pOnea, 0, Dst.m_byStorageIndex, -LODWORD(Dst.m_dwDur), 0, 0);
        Dst.m_lnUID = 0i64;
      }
      else if ( !CPlayer::Emb_DelStorage(pOnea, 0, Dst.m_byStorageIndex, 0, 1, "CPostSystemManager::PostSendRequest()") )
      {
        CPlayer::SendMsg_PostSendReply(pOnea, 8);
        return 0;
      }
    }
    CPlayer::SubGold(pOnea, dwGold + 5);
    CPlayer::SendMsg_AlterMoneyInform(pOnea, 0);
    if ( v32 || dwGold )
    {
      if ( v30 )
      {
        CMgrAvatorItemHistory::post_senditem(
          &CPlayer::s_MgrItemHistory,
          Str1,
          &Dst,
          Dst.m_dwDur,
          dwGold,
          pOnea->m_szItemHistoryFileName);
      }
      else if ( v27 )
      {
        CMgrAvatorItemHistory::post_senditem(
          &CPlayer::s_MgrItemHistory,
          Str1,
          &Dst,
          Dst.m_dwDur,
          dwGold,
          pOnea->m_szItemHistoryFileName);
      }
      else
      {
        CMgrAvatorItemHistory::post_senditem(
          &CPlayer::s_MgrItemHistory,
          Str1,
          0i64,
          0i64,
          dwGold,
          pOnea->m_szItemHistoryFileName);
      }
    }
    _INVENKEY::_INVENKEY(&v33);
    v34 = 0i64;
    v35 = 0xFFFFFFF;
    lnUID = 0i64;
    _INVENKEY::SetRelease(&v33);
    if ( v32 )
    {
      v33.byTableCode = Dst.m_byTableCode;
      v33.wItemIndex = Dst.m_wItemIndex;
      v34 = Dst.m_dwDur;
      v35 = Dst.m_dwLv;
      lnUID = Dst.m_lnUID;
    }
    CNetIndexList::PopNode_Front(&v39->m_listEmpty, &pdwOutIndex);
    CPostData::SetState(&v39->m_PostData[pdwOutIndex], 0);
    v14 = pOnea->m_pUserDB->m_wszAvatorName;
    v15 = pOnea->m_pUserDB;
    v38 = &v39->m_PostData[pdwOutIndex];
    bySenderDgr = pOnea->m_byUserDgr;
    bySendRace = byRace;
    dwPSSerial = 0;
    v22 = dwGold;
    dwUpt = v35;
    dwDur = v34;
    Key = v33;
    *(_QWORD *)bSend = wszStr;
    pItem = (_STORAGE_LIST::_db_con **)Str1;
    CPostData::SetPostData(
      v38,
      pdwOutIndex,
      v15->m_dwSerial,
      v14,
      Str1,
      wszStr,
      v33,
      v34,
      v35,
      dwGold,
      0,
      byRace,
      bySenderDgr);
    CPostData::SetPostContent(&v39->m_PostData[pdwOutIndex], wszContent);
    CPostData::SetPostItemSerial(&v39->m_PostData[pdwOutIndex], lnUID);
    CNetIndexList::PushNode_Back(&v39->m_listRegist, pdwOutIndex);
  }
  if ( v26 )
    CPlayer::SendMsg_PostSendReply(pOnea, v26);
  else
    CPlayer::pc_UpdateDataForPostSend(pOnea);
  return 1;
}
