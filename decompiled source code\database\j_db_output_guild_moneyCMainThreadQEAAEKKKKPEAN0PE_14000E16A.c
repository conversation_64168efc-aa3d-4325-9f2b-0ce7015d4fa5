/*
 * Function: j_?db_output_guild_money@CMainThread@@QEAAEKKKKPEAN0PEAEPEAD1@Z
 * Address: 0x14000E16A
 */

char __fastcall CMainThread::db_output_guild_money(CMainThread *this, unsigned int dwPusherSerial, unsigned int dwGuildSerial, unsigned int dwSubDalant, unsigned int dwSubGold, long double *dTotalDalant, long double *dTotalGold, char *byDate, char *pwszName, char *pbyProcRet)
{
  return CMainThread::db_output_guild_money(
           this,
           dwPusherSerial,
           dwGuildSerial,
           dwSubDalant,
           dwSubGold,
           dTotalDalant,
           dTotalGold,
           byDate,
           pwszName,
           pbyProcRet);
}
