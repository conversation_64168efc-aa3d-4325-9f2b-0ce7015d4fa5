/*
 * Function: ??_GMessageQueue@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x1406552B0
 */

CryptoPP::MessageQueue *__fastcall CryptoPP::MessageQueue::`scalar deleting destructor'(CryptoPP::MessageQueue *a1, int a2)
{
  CryptoPP::MessageQueue *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::MessageQueue::~MessageQueue(a1);
  if ( v4 & 1 )
    operator delete((void *)v3);
  return v3;
}
