/*
 * Function: ?GetAfterEffect@CPlayer@@QEAAPEAU_sf_continous@@XZ
 * Address: 0x14004EDF0
 */

_sf_continous *__fastcall CPlayer::GetAfterEffect(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  _sf_continous *result; // rax@5
  char v4; // al@17
  char v5; // al@18
  char v6; // al@18
  char *v7; // rax@27
  __int64 v8; // [sp+0h] [bp-78h]@1
  int v9; // [sp+20h] [bp-58h]@27
  int v10; // [sp+28h] [bp-50h]@27
  _SFCONT_DB_BASE *v11; // [sp+30h] [bp-48h]@4
  _base_fld *v12; // [sp+38h] [bp-40h]@4
  bool *v13; // [sp+40h] [bp-38h]@4
  unsigned int v14; // [sp+48h] [bp-30h]@10
  int j; // [sp+4Ch] [bp-2Ch]@10
  int k; // [sp+50h] [bp-28h]@12
  _SFCONT_DB_BASE::_LIST *v17; // [sp+58h] [bp-20h]@15
  char v18; // [sp+60h] [bp-18h]@18
  char v19; // [sp+61h] [bp-17h]@20
  int v20; // [sp+64h] [bp-14h]@27
  int v21; // [sp+68h] [bp-10h]@27
  unsigned int v22; // [sp+6Ch] [bp-Ch]@27
  CPlayer *v23; // [sp+80h] [bp+8h]@1

  v23 = this;
  v1 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = &v23->m_pUserDB->m_AvatorData.dbSfcont;
  v12 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
  v13 = 0i64;
  if ( v11 )
  {
    if ( v12 )
    {
      if ( v23->m_pUserDB )
      {
        v14 = _sf_continous::GetSFContCurTime();
        for ( j = 0; j < 2; ++j )
        {
          for ( k = 0; k < 8; ++k )
          {
            v17 = (_SFCONT_DB_BASE::_LIST *)((char *)v11 + 32 * j + 4 * k);
            if ( _SFCONT_DB_BASE::_LIST::IsFilled(v17) )
            {
              if ( _SFCONT_DB_BASE::_LIST::GetEffectCode(v17) == 3
                || (v4 = _SFCONT_DB_BASE::_LIST::GetEffectCode(v17), (unsigned __int8)v4 == v12->m_dwIndex) )
              {
                v13 = &v23->m_SFCont[j][k].m_bExist;
                v13[1] = _SFCONT_DB_BASE::_LIST::GetEffectCode(v17);
                *((_WORD *)v13 + 1) = _SFCONT_DB_BASE::_LIST::GetEffectIndex(v17);
                v5 = _SFCONT_DB_BASE::_LIST::GetLv(v17);
                v13[4] = v5 + 1;
                *((_DWORD *)v13 + 2) = v14;
                *((_WORD *)v13 + 6) = _SFCONT_DB_BASE::_LIST::GetLeftTime(v17);
                v18 = _SFCONT_DB_BASE::_LIST::GetOrder(v17);
                v6 = _SFCONT_DB_BASE::_LIST::GetOrder(v17);
                if ( v14 > (unsigned __int8)v6 )
                {
                  *((_DWORD *)v13 + 2) = v14 - (unsigned __int8)v18;
                  *((_WORD *)v13 + 6) += (unsigned __int8)v18;
                }
                v19 = 0;
                if ( v13[1] < 4 )
                {
                  if ( CRecordData::GetRecord(&stru_1799C8410 + v13[1], *((_WORD *)v13 + 1)) )
                  {
                    if ( v13[4] > 7 )
                      v19 = 1;
                  }
                  else
                  {
                    v19 = 1;
                  }
                }
                else
                {
                  v19 = 1;
                }
                if ( !v19 )
                  return (_sf_continous *)v13;
                CUserDB::Update_SFContDelete(v23->m_pUserDB, j, k);
                v20 = v13[4];
                v21 = *((_WORD *)v13 + 1);
                v22 = v13[1];
                v7 = CPlayerDB::GetCharNameA(&v23->m_Param);
                v10 = v20;
                v9 = v21;
                CLogFile::Write(&stru_1799C8E78, "%s: error stored effect, code: %d, idx: %d: lv: %d", v7, v22);
              }
            }
          }
        }
        result = 0i64;
      }
      else
      {
        result = 0i64;
      }
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
