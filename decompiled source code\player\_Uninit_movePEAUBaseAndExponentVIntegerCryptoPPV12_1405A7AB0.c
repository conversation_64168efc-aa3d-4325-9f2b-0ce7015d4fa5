/*
 * Function: ??$_Uninit_move@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU12@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@U_Undefined_move_tag@4@@std@@YAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU12@00AEAV?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7AB0
 */

int std::_Uninit_move<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>,std::_Undefined_move_tag>()
{
  return stdext::unchecked_uninitialized_copy<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
}
