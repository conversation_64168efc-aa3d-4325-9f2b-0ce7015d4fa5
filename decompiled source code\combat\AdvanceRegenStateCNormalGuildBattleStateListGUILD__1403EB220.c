/*
 * Function: ?AdvanceRegenState@CNormalGuildBattleStateList@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403EB220
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleStateList::AdvanceRegenState(GUILD_BATTLE::CNormalGuildBattleStateList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CNormalGuildBattleStateList *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GUILD_BATTLE::CNormalGuildBattleStateList::IsInBattle(v5) )
    result = GUILD_BATTLE::CNormalGuildBattleStateInBattle::AdvanceRegenState(&v5->INBATTLE);
  else
    result = 0;
  return result;
}
