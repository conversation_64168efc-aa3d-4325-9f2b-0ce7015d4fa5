/*
 * Function: ?SendMsg_LinkBoardDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400DA1C0
 */

void __fastcall CPlayer::SendMsg_LinkBoardDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-168h]@1
  char szMsg[2]; // [sp+40h] [bp-128h]@7
  char v5; // [sp+A4h] [bp-C4h]@13
  char Dst; // [sp+A5h] [bp-C3h]@13
  char v7; // [sp+ADh] [bp-BBh]@13
  char v8; // [sp+B5h] [bp-B3h]@13
  char v9; // [sp+BDh] [bp-ABh]@13
  unsigned int v10; // [sp+C5h] [bp-A3h]@13
  char v11; // [sp+C9h] [bp-9Fh]@13
  int j; // [sp+E4h] [bp-84h]@5
  _LINKKEY *v13; // [sp+E8h] [bp-80h]@7
  unsigned __int16 v14; // [sp+F0h] [bp-78h]@8
  unsigned __int16 v15; // [sp+F4h] [bp-74h]@9
  int v16; // [sp+F8h] [bp-70h]@9
  int v17; // [sp+FCh] [bp-6Ch]@9
  char *v18; // [sp+100h] [bp-68h]@10
  _LINK_DB_BASE::_LIST v19; // [sp+114h] [bp-54h]@11
  char pbyType; // [sp+134h] [bp-34h]@13
  char v21; // [sp+135h] [bp-33h]@13
  unsigned __int64 v22; // [sp+150h] [bp-18h]@4
  CPlayer *v23; // [sp+170h] [bp+8h]@1

  v23 = this;
  v1 = &v3;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = (unsigned __int64)&v3 ^ _security_cookie;
  if ( v23->m_pUserDB )
  {
    for ( j = 0; j < 50; ++j )
    {
      v13 = (_LINKKEY *)((char *)&v23->m_pUserDB->m_AvatorData.dbLink + 2 * j);
      *(_LINKKEY *)&szMsg[2 * j] = (_LINKKEY)v13->wEffectCode;
      if ( _LINKKEY::IsFilled(v13) )
      {
        v14 = _LINKKEY::GetCode(v13);
        if ( v14 == 4 )
        {
          v15 = _LINKKEY::GetIndex(v13);
          v16 = (signed int)v15 >> 8;
          v17 = (unsigned __int8)v15;
          if ( IsStorageRange(SHIBYTE(v15), v15) )
          {
            v18 = &v23->m_Param.m_pStoragePtr[v16]->m_pStorageList[v17].m_bLoad;
            if ( v18 )
            {
              _LINK_DB_BASE::_LIST::_LIST(&v19);
              _LINKKEY::SetData(&v19.Key, v14, *(_WORD *)(v18 + 17));
              *(_LINK_DB_BASE::_LIST *)&szMsg[2 * j] = v19;
            }
          }
        }
      }
    }
    v5 = v23->m_pUserDB->m_AvatorData.dbLink.m_byLinkBoardLock;
    memcpy_0(&Dst, v23->m_pUserDB->m_AvatorData.dbLink.m_dwSkill, 8ui64);
    memcpy_0(&v7, v23->m_pUserDB->m_AvatorData.dbLink.m_dwForce, 8ui64);
    memcpy_0(&v8, v23->m_pUserDB->m_AvatorData.dbLink.m_dwCharacter, 8ui64);
    memcpy_0(&v9, v23->m_pUserDB->m_AvatorData.dbLink.m_dwAnimus, 8ui64);
    memcpy_0(&v11, v23->m_pUserDB->m_AvatorData.dbLink.m_dwInvenBag, 0x14ui64);
    v10 = v23->m_pUserDB->m_AvatorData.dbLink.m_dwInven;
    pbyType = 3;
    v21 = 45;
    CNetProcess::LoadSendMsg(unk_1414F2088, v23->m_ObjID.m_wIndex, &pbyType, szMsg, 0x9Du);
  }
}
