/*
 * Function: ?SendStartNotifyCommitteeMemberPosition@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXH@Z
 * Address: 0x1403E2250
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendStartNotifyCommitteeMemberPosition(GUILD_BATTLE::CNormalGuildBattleGuild *this, int iMember)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CPlayer *pkPlayer; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pkPlayer = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v6->m_kMember[iMember]);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendOhterNotifyCommitteeMemberPosition(v6, pkPlayer);
  GUILD_BATTLE::CNormalGuildBattleGuild::SendSelfNotifyCommitteeMemberPositionList(v6, pkPlayer);
}
