/*
 * Function: ?Initialize@CGoldenBoxItemMgr@@QEAA_NXZ
 * Address: 0x1404120D0
 */

char __fastcall CGoldenBoxItemMgr::Initialize(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CGoldenBoxItemMgr *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CGoldenBoxItemMgr::_init_loggers(v5);
  CMyTimer::BeginTimer(&v5->m_tmLoopTimer, 0x2710u);
  if ( CGoldenBoxItemMgr::Load_Golden_Box_Item_Event(v5) )
  {
    GetLocalTime(&v5->tm1);
    GetLocalTime(&v5->tm);
    v5->m_bInit = 0;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
