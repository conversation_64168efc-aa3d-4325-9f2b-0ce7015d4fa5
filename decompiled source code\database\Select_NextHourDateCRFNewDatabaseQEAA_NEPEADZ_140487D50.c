/*
 * Function: ?Select_NextHourDate@CRFNewDatabase@@QEAA_NEPEAD@Z
 * Address: 0x140487D50
 */

char __fastcall CRFNewDatabase::Select_NextHourDate(CRFNewDatabase *this, char byAddHour, char *szDate)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@21
  SQLLEN v9; // [sp+38h] [bp-150h]@21
  __int16 v10; // [sp+44h] [bp-144h]@9
  char DstBuf; // [sp+60h] [bp-128h]@4
  unsigned __int64 v12; // [sp+170h] [bp-18h]@4
  CRFNewDatabase *v13; // [sp+190h] [bp+8h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1

  TargetValue = szDate;
  v13 = this;
  v3 = &v6;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v12 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf_s(
    &DstBuf,
    0x100ui64,
    "select convert( varchar(9), dateadd(hour, %d, getdate()) , 112 )",
    (unsigned __int8)byAddHour);
  if ( v13->m_bSaveDBLog )
    CLogFile::WriteString(&v13->m_ProcessLogA, &DstBuf);
  if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase(v13) )
  {
    v10 = SQLExecDirectA_0(v13->m_hStmtSelect, &DstBuf, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v13->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog(v13, v10, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction(v13, v10, v13->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v10 = SQLFetch_0(v13->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        if ( v13->m_hStmtSelect )
          SQLCloseCursor_0(v13->m_hStmtSelect);
        if ( v10 == 100 )
        {
          result = 0;
        }
        else
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog(v13, v10, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction(v13, v10, v13->m_hStmtSelect);
          result = 0;
        }
      }
      else
      {
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)9;
        v10 = SQLGetData_0(v13->m_hStmtSelect, 1u, 1, TargetValue, 9i64, &v9);
        if ( v10 && v10 != 1 )
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog(v13, v10, &DstBuf, "SQLGetData", SQLStmt);
          CRFNewDatabase::ErrorAction(v13, v10, v13->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          if ( v13->m_bSaveDBLog )
            CRFNewDatabase::FmtLog(v13, "%s Success", &DstBuf);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog(v13, "ReConnectDataBase() Fail. Query : %s", &DstBuf);
    result = 0;
  }
  return result;
}
