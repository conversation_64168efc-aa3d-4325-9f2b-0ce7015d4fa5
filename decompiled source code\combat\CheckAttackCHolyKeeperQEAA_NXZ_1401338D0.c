/*
 * Function: ?Check<PERSON><PERSON>ck@CHoly<PERSON><PERSON>er@@QEAA_NXZ
 * Address: 0x1401338D0
 */

char __fastcall CHolyKeeper::CheckAttack(CHolyKeeper *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // eax@23
  CAttack *v5; // rcx@23
  __int64 v6; // [sp+0h] [bp-78h]@1
  char v7; // [sp+20h] [bp-58h]@23
  int v8; // [sp+28h] [bp-50h]@23
  int v9; // [sp+30h] [bp-48h]@23
  char v10; // [sp+38h] [bp-40h]@23
  unsigned int v11; // [sp+40h] [bp-38h]@10
  CCharacter *v12; // [sp+48h] [bp-30h]@15
  int j; // [sp+50h] [bp-28h]@21
  __int64 *v14; // [sp+58h] [bp-20h]@23
  __int64 v15; // [sp+60h] [bp-18h]@23
  CHolyKeeper *v16; // [sp+80h] [bp+8h]@1

  v16 = this;
  v1 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v16->m_bMove )
  {
    result = 0;
  }
  else if ( v16->m_bExit )
  {
    result = 0;
  }
  else if ( v16->m_nHP )
  {
    v11 = GetLoopTime();
    if ( v11 >= v16->m_dwNextGenAttackTime )
    {
      if ( v16->m_bChaos )
        v16->m_dwNextGenAttackTime = (signed int)ffloor((float)(signed int)v16->m_dwNextGenAttackTime + (float)(v16->m_pRec->m_fAttSpd * 4.0));
      else
        v16->m_dwNextGenAttackTime = (signed int)ffloor((float)(signed int)v16->m_dwNextGenAttackTime + (float)(v16->m_pRec->m_fAttSpd * 2.0));
      v12 = CHolyKeeper::SearchAttackTarget(v16);
      if ( v12 )
      {
        v16->m_ap.pDst = v12;
        memcpy_0(v16->m_ap.fArea, v12->m_fCurPos, 0xCui64);
        if ( !v12 || v12->m_ObjID.m_byID || v12[25].m_SFCont[0][5].m_wszPlayerName[16] == 255 )
          v16->m_ap.nPart = 0;
        else
          v16->m_ap.nPart = v12[25].m_SFCont[0][5].m_wszPlayerName[16];
        CAttack::AttackGen(v16->m_at, &v16->m_ap, 0, 0);
        for ( j = 0; j < v16->m_at->m_nDamagedObjNum; ++j )
        {
          v4 = ((int (__fastcall *)(CHolyKeeper *))v16->vfptr->GetLevel)(v16);
          v5 = v16->m_at;
          v14 = (__int64 *)v16->m_at->m_DamList[j].m_pChar;
          v15 = *v14;
          v10 = 1;
          v9 = 0;
          v8 = -1;
          v7 = 0;
          (*(void (__fastcall **)(__int64 *, _QWORD, CHolyKeeper *, _QWORD))(v15 + 184))(
            v14,
            v5->m_DamList[j].m_nDamage,
            v16,
            (unsigned int)v4);
        }
        CHolyKeeper::SendMsg_Attack(v16);
      }
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
