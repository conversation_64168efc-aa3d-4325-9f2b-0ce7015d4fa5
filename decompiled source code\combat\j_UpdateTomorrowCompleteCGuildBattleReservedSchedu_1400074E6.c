/*
 * Function: j_?UpdateTomorrowComplete@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAAXKPEAE@Z
 * Address: 0x1400074E6
 */

void __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowComplete(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int dwMapID, char *pLoadData)
{
  GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateTomorrowComplete(this, dwMapID, pLoadData);
}
