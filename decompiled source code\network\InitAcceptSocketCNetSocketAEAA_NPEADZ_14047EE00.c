/*
 * Function: ?InitAcceptSocket@CNetSocket@@AEAA_NPEAD@Z
 * Address: 0x14047EE00
 */

char __fastcall CNetSocket::InitAcceptSocket(CNetSocket *this, char *pszErrMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@9
  char result; // al@9
  int v6; // eax@15
  int v7; // eax@17
  int v8; // eax@19
  __int64 v9; // [sp+0h] [bp-78h]@1
  int namelen; // [sp+20h] [bp-58h]@11
  __int16 Dst; // [sp+38h] [bp-40h]@14
  u_short v12; // [sp+3Ah] [bp-3Eh]@14
  u_long v13; // [sp+3Ch] [bp-3Ch]@14
  unsigned __int64 v14; // [sp+60h] [bp-18h]@4
  CNetSocket *v15; // [sp+80h] [bp+8h]@1
  LPSTR v16; // [sp+88h] [bp+10h]@1

  v16 = pszErrMsg;
  v15 = this;
  v2 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( v15->m_SockType.m_byProtocolID )
  {
    if ( v15->m_SockType.m_byProtocolID == 1 )
      v15->m_sAccept = socket(6, 5, 1256);
  }
  else
  {
    v15->m_sAccept = socket(2, 1, 0);
  }
  if ( v15->m_sAccept == -1i64 )
  {
    v4 = WSAGetLastError();
    wsprintfA(v16, "socket() Failure => %d", (unsigned int)v4);
    result = 0;
  }
  else
  {
    if ( v15->m_SockType.m_byProtocolID )
    {
      if ( v15->m_SockType.m_byProtocolID == 1 )
        namelen = 14;
    }
    else
    {
      namelen = 16;
    }
    memset_0(&Dst, 0, 0x10ui64);
    Dst = 2;
    v12 = htons(v15->m_SockType.m_wPort);
    v13 = htonl(0);
    if ( bind(v15->m_sAccept, (const struct sockaddr *)&Dst, namelen) == -1 )
    {
      v6 = WSAGetLastError();
      wsprintfA(v16, "bind() Failure => %d", (unsigned int)v6);
      result = 0;
    }
    else if ( listen(v15->m_sAccept, 5) == -1 )
    {
      v7 = WSAGetLastError();
      wsprintfA(v16, "listen() Failure => %d", (unsigned int)v7);
      result = 0;
    }
    else
    {
      v15->m_AcceptEvent = WSACreateEvent();
      if ( WSAEventSelect(v15->m_sAccept, v15->m_AcceptEvent, 8) == -1 )
      {
        v8 = WSAGetLastError();
        wsprintfA(v16, "WSAEventSelect() Failure => %d", (unsigned int)v8);
        result = 0;
      }
      else
      {
        result = 1;
      }
    }
  }
  return result;
}
