/*
 * Function: ?SendMsg_Alter_Action_Point@CPlayer@@QEAAXEK@Z
 * Address: 0x1400E8E60
 */

void __fastcall CPlayer::SendMsg_Alter_Action_Point(CPlayer *this, char byActCode, unsigned int dwActPoint)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  _alter_action_point_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer *v9; // [sp+80h] [bp+8h]@1
  char v10; // [sp+88h] [bp+10h]@1
  unsigned int v11; // [sp+90h] [bp+18h]@1

  v11 = dwActPoint;
  v10 = byActCode;
  v9 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _alter_action_point_zocl::_alter_action_point_zocl(&v6);
  v6.byActionCode = v10;
  v6.dwActionPoint = v11;
  pbyType = 11;
  v8 = 36;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &v6.byActionCode, 5u);
}
