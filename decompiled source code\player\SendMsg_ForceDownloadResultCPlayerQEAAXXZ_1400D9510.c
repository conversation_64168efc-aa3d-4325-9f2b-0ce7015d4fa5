/*
 * Function: ?SendMsg_ForceDownloadResult@CPlayer@@QEAAXXZ
 * Address: 0x1400D9510
 */

void __fastcall CPlayer::SendMsg_ForceDownloadResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@9
  __int64 v4; // [sp+0h] [bp-458h]@1
  _force_download_result_zocl v5; // [sp+40h] [bp-418h]@4
  int v6; // [sp+414h] [bp-44h]@4
  int j; // [sp+418h] [bp-40h]@4
  char *v8; // [sp+420h] [bp-38h]@6
  char pbyType; // [sp+434h] [bp-24h]@9
  char v10; // [sp+435h] [bp-23h]@9
  CPlayer *v11; // [sp+460h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11->m_bForceDownload = 1;
  _force_download_result_zocl::_force_download_result_zocl(&v5);
  v5.byRetCode = 0;
  v6 = 0;
  for ( j = 0; j < 88; ++j )
  {
    v8 = &v11->m_Param.m_dbForce.m_pStorageList[j].m_bLoad;
    if ( *v8 )
    {
      v5.ItemSlotInfo[v6].dwCum = *(_DWORD *)(v8 + 5);
      v5.ItemSlotInfo[v6].wItemIndex = *(_WORD *)(v8 + 3);
      v5.ItemSlotInfo[v6].byCsMethod = v8[32];
      v5.ItemSlotInfo[v6++].dwT = *(_DWORD *)(v8 + 33);
    }
  }
  v5.bySlotNum = v6;
  pbyType = 3;
  v10 = 10;
  v3 = _force_download_result_zocl::size(&v5);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &v5.byRetCode, v3);
}
