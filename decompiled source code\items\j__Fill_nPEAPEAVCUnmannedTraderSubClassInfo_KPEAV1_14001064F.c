/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001064F
 */

void __fastcall std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *,std::random_access_iterator_tag>(CUnmannedTraderSubClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _<PERSON>,
    __formal,
    a5);
}
