/*
 * Function: ?LogIn@CMoveMapLimitRightInfoList@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403ADCA0
 */

void __fastcall CMoveMapLimitRightInfoList::LogIn(CMoveMapLimitRightInfoList *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@5
  CMoveMapLimitRightInfo *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfoList *v7; // [sp+30h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+38h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v7 = this;
  v2 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pkPlayer )
  {
    v4 = pkPlayer->m_ObjID.m_wIndex;
    if ( std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::size(&v7->m_vecRight) > v4 )
    {
      v5 = std::vector<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::operator[](
             &v7->m_vecRight,
             pkPlayera->m_ObjID.m_wIndex);
      CMoveMapLimitRightInfo::LogIn(v5, pkPlayera);
    }
  }
}
