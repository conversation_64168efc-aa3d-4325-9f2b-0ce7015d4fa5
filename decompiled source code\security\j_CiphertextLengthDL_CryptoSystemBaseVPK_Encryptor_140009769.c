/*
 * Function: j_?CiphertextLength@?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@UECPPoint@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x140009769
 */

unsigned __int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::CiphertextLength(CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint> > *this, unsigned __int64 plaintextLength)
{
  return CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::ECPPoint>>::CiphertextLength(
           this,
           plaintextLength);
}
