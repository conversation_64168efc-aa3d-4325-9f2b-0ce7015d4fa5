/*
 * Function: ?GetSendMsg@CMsgList@RACE_BOSS_MSG@@QEAAPEAVCMsg@2@XZ
 * Address: 0x14029E920
 */

RACE_BOSS_MSG::CMsg *__fastcall RACE_BOSS_MSG::CMsgList::GetSendMsg(RACE_BOSS_MSG::CMsgList *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  RACE_BOSS_MSG::CMsg *result; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@4
  RACE_BOSS_MSG::CMsgList *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pdwOutIndex = 0;
  if ( CNetIndexList::CopyFront(&v6->m_kUseInxList, &pdwOutIndex) )
  {
    if ( RACE_BOSS_MSG::CMsg::IsSendTime(v6->m_ppMsg[pdwOutIndex]) )
      result = v6->m_ppMsg[pdwOutIndex];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
