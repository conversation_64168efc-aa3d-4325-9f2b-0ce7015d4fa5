/*
 * Function: ?pop@?$ListHeap@UCell@LendItemSheet@@@@QEAA_NXZ
 * Address: 0x14030FD50
 */

bool __fastcall ListHeap<LendItemSheet::Cell>::pop(ListHeap<LendItemSheet::Cell> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@4
  ListHeap<LendItemSheet::Cell> *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pdwOutIndex = -1;
  if ( CNetIndexList::PopNode_Front((CNetIndexList *)&v6->_listData.m_Head, &pdwOutIndex) )
    result = CNetIndexList::PushNode_Back((CNetIndexList *)&v6->_listEmpty.m_Head, pdwOutIndex) != 0;
  else
    result = 0;
  return result;
}
