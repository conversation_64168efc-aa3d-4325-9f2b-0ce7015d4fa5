/*
 * Function: ??0CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA@E@Z
 * Address: 0x1403E04C0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::CNormalGuildBattleGuild(GUILD_BATTLE::CNormalGuildBattleGuild *this, char byID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __int64 v5; // [sp+30h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  v6->m_byID = byID;
  `eh vector constructor iterator'(
    v6->m_kMember,
    0x48ui64,
    150,
    (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleGuildMember::CNormalGuildBattleGuildMember,
    (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleGuildMember::~CNormalGuildBattleGuildMember);
  GUILD_BATTLE::CNormalGuildBattleGuild::Clear(v6);
}
