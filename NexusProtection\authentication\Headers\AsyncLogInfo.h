#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <cstdint>
#include <mutex>
#include <atomic>
#include <filesystem>
#include <unordered_map>
#include <vector>

namespace NexusProtection::Authentication {

    /**
     * @brief Asynchronous Log Information System
     * 
     * Represents an asynchronous logging system that handles log file management,
     * automatic filename updates with timestamps, and thread-safe log counting.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - 0CAsyncLogInfoQEAAXZ_1403BC9F0.c (Constructor)
     * - 1CAsyncLogInfoQEAAXZ_1403BCA80.c (Destructor)
     * - InitCAsyncLogInfoQEAA_NW4ASYNC_LOG_TYPEPEBD1_NKAEA_1403BCB80.c (Init)
     * - GetCountCAsyncLogInfoQEAAKXZ_1403C16B0.c (GetCount)
     * - IncreaseCountCAsyncLogInfoQEAAXXZ_1403C16F0.c (IncreaseCount)
     * - UpdateLogFileNameCAsyncLogInfoQEAAXXZ_1403BD0F0.c (UpdateLogFileName)
     */

    /**
     * @brief Async Log Type Enumeration
     */
    enum class AsyncLogType : int32_t {
        UNKNOWN = -1,
        SYSTEM_LOG = 0,
        ERROR_LOG = 1,
        DEBUG_LOG = 2,
        NETWORK_LOG = 3,
        DATABASE_LOG = 4,
        SECURITY_LOG = 5,
        PERFORMANCE_LOG = 6,
        USER_ACTION_LOG = 7,
        BILLING_LOG = 8,
        TRADE_LOG = 9,
        GUILD_LOG = 10,
        CHAT_LOG = 11,
        ADMIN_LOG = 12,
        AUDIT_LOG = 13,
        CUSTOM_LOG = 14,
        MAX_LOG_TYPES = 15
    };

    /**
     * @brief Forward declarations
     */
    class CLogFile;
    class CMyTimer;

    /**
     * @brief Timer interface for async log filename updates
     */
    class IAsyncLogTimer {
    public:
        virtual ~IAsyncLogTimer() = default;
        virtual void BeginTimer(uint32_t delayMs) = 0;
        virtual bool CountingTimer() const = 0;
        virtual void StopTimer() = 0;
        virtual void Reset() = 0;
        virtual bool IsRunning() const = 0;
    };

    /**
     * @brief Modern implementation of CMyTimer for async logging
     */
    class AsyncLogTimer : public IAsyncLogTimer {
    public:
        AsyncLogTimer();
        ~AsyncLogTimer() override;

        // IAsyncLogTimer implementation
        void BeginTimer(uint32_t delayMs) override;
        bool CountingTimer() const override;
        void StopTimer() override;
        void Reset() override;
        bool IsRunning() const override { return m_isRunning; }

        // Additional functionality
        uint32_t GetElapsedMs() const;

    private:
        std::chrono::steady_clock::time_point m_startTime;
        std::chrono::milliseconds m_delay{0};
        std::atomic<bool> m_isRunning{false};
        mutable std::mutex m_timerMutex;
    };

    /**
     * @brief Asynchronous Log Information Class
     * 
     * Manages asynchronous logging with automatic file naming, thread-safe operations,
     * and configurable timer-based filename updates.
     */
    class CAsyncLogInfo {
    public:
        // Constructor and destructor
        CAsyncLogInfo();
        ~CAsyncLogInfo();

        // Copy and move semantics
        CAsyncLogInfo(const CAsyncLogInfo& other) = delete;
        CAsyncLogInfo& operator=(const CAsyncLogInfo& other) = delete;
        CAsyncLogInfo(CAsyncLogInfo&& other) noexcept;
        CAsyncLogInfo& operator=(CAsyncLogInfo&& other) noexcept;

        // Core initialization
        bool Init(AsyncLogType logType, 
                 const std::string& dirPath, 
                 const std::string& typeName, 
                 bool addDateFileName = true,
                 uint32_t updateFileNameDelay = 10000, // 10 seconds default
                 CLogFile* logLoading = nullptr);

        // Log management
        uint32_t GetCount() const;
        void IncreaseCount();
        void ResetCount();
        void UpdateLogFileName();

        // Configuration
        AsyncLogType GetLogType() const { return m_logType; }
        const std::string& GetLogDirPath() const { return m_logDirPath; }
        const std::string& GetLogFileName() const { return m_logFileName; }
        const std::string& GetTypeName() const { return m_typeName; }
        
        // Timer management
        void SetUpdateDelay(uint32_t delayMs);
        uint32_t GetUpdateDelay() const;
        bool IsTimerActive() const;
        void StartTimer();
        void StopTimer();

        // File operations
        bool CreateLogDirectory() const;
        bool DeleteLogFile() const;
        bool LogFileExists() const;
        std::filesystem::path GetFullLogPath() const;

        // Utility
        std::string ToString() const;
        bool IsInitialized() const { return m_isInitialized; }

        // Static utilities
        static std::string AsyncLogTypeToString(AsyncLogType type);
        static AsyncLogType StringToAsyncLogType(const std::string& typeStr);
        static std::string GetCurrentDateTimeString();
        static bool IsValidLogType(AsyncLogType type);

    private:
        // Core data members
        AsyncLogType m_logType{AsyncLogType::UNKNOWN};
        std::atomic<uint32_t> m_logCount{0};
        std::string m_logDirPath;
        std::string m_logFileName;
        std::string m_typeName;
        
        // Timer for automatic filename updates
        std::unique_ptr<IAsyncLogTimer> m_timer;
        uint32_t m_updateFileNameDelay{10000}; // milliseconds
        bool m_addDateFileName{true};
        
        // Thread safety
        mutable std::mutex m_dataMutex;
        mutable std::mutex m_fileNameMutex;
        
        // State
        std::atomic<bool> m_isInitialized{false};

        // Private helper methods
        bool ValidateInitParameters(AsyncLogType logType, 
                                   const std::string& dirPath, 
                                   const std::string& typeName,
                                   CLogFile* logLoading) const;
        
        std::string GenerateLogFileName(const std::string& dirPath, 
                                       const std::string& typeName, 
                                       bool addDate) const;
        
        std::string GenerateLogDirPath(const std::string& dirPath, 
                                      const std::string& typeName) const;
        
        void CleanupResources();
        void LogError(const std::string& message, CLogFile* logFile = nullptr) const;
        
        // Move helper
        void MoveFrom(CAsyncLogInfo&& other) noexcept;
    };

    /**
     * @brief Async Log Manager
     * 
     * Manages multiple async log instances for different log types.
     */
    class CAsyncLogManager {
    public:
        CAsyncLogManager();
        ~CAsyncLogManager();

        // Log management
        bool RegisterLogInfo(AsyncLogType type, std::unique_ptr<CAsyncLogInfo> logInfo);
        CAsyncLogInfo* GetLogInfo(AsyncLogType type) const;
        bool RemoveLogInfo(AsyncLogType type);
        
        // Bulk operations
        void UpdateAllLogFileNames();
        void IncreaseCountForType(AsyncLogType type);
        uint32_t GetTotalLogCount() const;
        uint32_t GetCountForType(AsyncLogType type) const;
        
        // Configuration
        void SetGlobalUpdateDelay(uint32_t delayMs);
        void StartAllTimers();
        void StopAllTimers();
        
        // Utility
        std::vector<AsyncLogType> GetRegisteredTypes() const;
        std::string GetStatusReport() const;
        void Cleanup();

    private:
        std::unordered_map<AsyncLogType, std::unique_ptr<CAsyncLogInfo>> m_logInfoMap;
        mutable std::mutex m_managerMutex;
        uint32_t m_globalUpdateDelay{10000};
    };

    /**
     * @brief Async Log Factory
     * 
     * Factory class for creating async log instances.
     */
    class AsyncLogFactory {
    public:
        // Factory methods
        static std::unique_ptr<CAsyncLogInfo> CreateAsyncLogInfo(AsyncLogType type,
                                                                const std::string& dirPath,
                                                                const std::string& typeName,
                                                                bool addDateFileName = true,
                                                                uint32_t updateDelay = 10000);
        
        static std::unique_ptr<CAsyncLogManager> CreateAsyncLogManager();
        
        static std::unique_ptr<IAsyncLogTimer> CreateTimer();
        
        // Predefined configurations
        static std::unique_ptr<CAsyncLogManager> CreateStandardLogManager(const std::string& baseDir);
        static std::unique_ptr<CAsyncLogManager> CreateDebugLogManager(const std::string& baseDir);
        static std::unique_ptr<CAsyncLogManager> CreateProductionLogManager(const std::string& baseDir);
    };

} // namespace NexusProtection::Authentication
