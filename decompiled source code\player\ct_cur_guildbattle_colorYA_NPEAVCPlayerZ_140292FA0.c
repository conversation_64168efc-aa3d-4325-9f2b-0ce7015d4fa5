/*
 * Function: ?ct_cur_guildbattle_color@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292FA0
 */

char __fastcall ct_cur_guildbattle_color(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-F8h]@1
  char Dest; // [sp+50h] [bp-A8h]@6
  unsigned __int64 v6; // [sp+E0h] [bp-18h]@4
  CPlayer *v7; // [sp+100h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v7 )
  {
    sprintf(&Dest, "Guild Battle Currect Color : %d", v7->m_byGuildBattleColorInx);
    CPlayer::SendData_ChatTrans(v7, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
