/*
 * Function: ?GetItemUpgLimSocket@@YAEK@Z
 * Address: 0x14003E290
 */

char __fastcall GetItemUpgLimSocket(unsigned int dwLvBit)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // [sp+0h] [bp-18h]@1
  unsigned int v5; // [sp+20h] [bp+8h]@1

  v5 = dwLvBit;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v5 )
    result = v5 >> 28;
  else
    result = 0;
  return result;
}
