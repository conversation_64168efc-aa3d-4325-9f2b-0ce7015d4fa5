/*
 * Function: ?UILockInfo_Update@CUserDB@@QEAAXPEAD@Z
 * Address: 0x1401184D0
 */

void __fastcall CUserDB::UILockInfo_Update(CUserDB *this, char *pMsg)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char *v5; // [sp+30h] [bp-58h]@5
  char Dst; // [sp+44h] [bp-44h]@5
  char pbyType; // [sp+64h] [bp-24h]@14
  char v8; // [sp+65h] [bp-23h]@14
  CUserDB *v9; // [sp+90h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pMsg )
  {
    v5 = pMsg;
    memset_0(&Dst, 0, 1ui64);
    if ( v9->m_byUILock >= 2 )
    {
      if ( *v5 )
      {
        if ( *v5 == 1 )
        {
          Dst = 10;
        }
        else if ( *v5 == 2 )
        {
          Dst = 12;
        }
        else
        {
          Dst = 8;
        }
      }
      else
      {
        Dst = 0;
        v9->m_byUILock = 2;
        strcpy_0(v9->m_szUILock_PW, v5 + 3);
        v9->m_byUILock_HintIndex = v5[16];
        strcpy_0(v9->m_uszUILock_HintAnswer, v5 + 17);
      }
    }
    else
    {
      Dst = 7;
    }
    pbyType = 13;
    v8 = -124;
    CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_idWorld.wIndex, &pbyType, &Dst, 1u);
  }
}
