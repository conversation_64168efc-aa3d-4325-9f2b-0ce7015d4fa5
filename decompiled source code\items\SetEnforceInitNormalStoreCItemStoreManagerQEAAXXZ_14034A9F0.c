/*
 * Function: ?SetEnforceInitNormalStore@CItemStoreManager@@QEAAXXZ
 * Address: 0x14034A9F0
 */

void __fastcall CItemStoreManager::SetEnforceInitNormalStore(CItemStoreManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char *Str1; // [sp+28h] [bp-50h]@4
  const char *v5; // [sp+30h] [bp-48h]@4
  const char *v6; // [sp+38h] [bp-40h]@4
  int k; // [sp+44h] [bp-34h]@9
  int j; // [sp+48h] [bp-30h]@5
  int v9; // [sp+4Ch] [bp-2Ch]@7
  int v10; // [sp+50h] [bp-28h]@4
  bool *v11; // [sp+58h] [bp-20h]@4
  CItemStore *v12; // [sp+60h] [bp-18h]@4
  CItemStoreManager *v13; // [sp+80h] [bp+8h]@1

  v13 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  Str1 = "030F1";
  v5 = "040F1";
  v6 = "050F1";
  v10 = 0;
  v11 = 0i64;
  v12 = 0i64;
LABEL_5:
  for ( j = 0; ; ++j )
  {
    if ( j >= v13->m_nMapItemStoreListNum )
      goto LABEL_5;
    v11 = &v13->m_MapItemStoreList[j].m_bUse;
    v9 = 0;
LABEL_8:
    if ( v9 < *((_DWORD *)v11 + 2) )
      break;
  }
  v12 = (CItemStore *)(*((_QWORD *)v11 + 2) + 120i64 * v9);
  for ( k = 0; ; ++k )
  {
    if ( k >= 3 )
    {
LABEL_17:
      ++v9;
      goto LABEL_8;
    }
    if ( k >= v10 && !strcmp_0((&Str1)[8 * k], v12->m_pRec->m_strStore_NPCcode) )
      break;
  }
  v13->m_pLimitInitNormalStore[v10++] = v12;
  if ( v10 < 3 )
    goto LABEL_17;
}
