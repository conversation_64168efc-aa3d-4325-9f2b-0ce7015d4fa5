/*
 * Function: ?UpdateRegistItem@CUnmannedTraderController@@QEAAEPEAD@Z
 * Address: 0x14034CDD0
 */

char __fastcall CUnmannedTraderController::UpdateRegistItem(CUnmannedTraderController *this, char *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char *v6; // [sp+20h] [bp-98h]@4
  bool pbRecordInserted; // [sp+34h] [bp-84h]@4
  char v8; // [sp+44h] [bp-74h]@4
  _unmannedtrader_registsingleitem kInfo; // [sp+60h] [bp-58h]@5
  CUnmannedTraderController *v10; // [sp+C0h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pData;
  pbRecordInserted = 0;
  v8 = CUnmannedTraderController::GetEmptyRecordSerial(v10, (unsigned int *)pData + 5, &pbRecordInserted);
  if ( !v8 )
  {
    kInfo.byType = v6[25];
    kInfo.bySellTurm = v6[26];
    kInfo.byRace = v6[27];
    kInfo.dwOwnerSerial = *((_DWORD *)v6 + 7);
    kInfo.dwPrice = *((_DWORD *)v6 + 8);
    kInfo.byInveninx = v6[36];
    kInfo.dwK = *((_DWORD *)v6 + 10);
    kInfo.dwD = *((_QWORD *)v6 + 6);
    kInfo.dwU = *((_DWORD *)v6 + 14);
    kInfo.byLv = v6[60];
    kInfo.byGrade = v6[61];
    kInfo.byClass1 = v6[62];
    kInfo.byClass2 = v6[63];
    kInfo.byClass3 = v6[64];
    kInfo.dwT = *((_DWORD *)v6 + 17);
    kInfo.lnUID = *((_QWORD *)v6 + 9);
    kInfo.dwTax = *((_DWORD *)v6 + 20);
    if ( !CRFWorldDatabase::Regist_UnmannedTraderSingleItem(pkDB, *((_DWORD *)v6 + 5), &kInfo, pbRecordInserted) )
      return 24;
    v6[24] = pbRecordInserted;
  }
  return v8;
}
