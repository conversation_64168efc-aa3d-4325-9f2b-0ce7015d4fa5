/*
 * Function: ?DeleteUseList@TimeLimitJade@@AEAA_NPEAU_db_con@_STORAGE_LIST@@_N@Z
 * Address: 0x1403FA860
 */

bool __fastcall TimeLimitJade::DeleteUseList(TimeLimitJade *this, _STORAGE_LIST::_db_con *pkItem, bool bItemDel)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@7
  TimeLimitJade::UseCell *v6; // rax@12
  __int64 v7; // [sp+0h] [bp-48h]@1
  TimeLimitJade::UseCell *v8; // [sp+20h] [bp-28h]@8
  TimeLimitJade::UseCell v9; // [sp+28h] [bp-20h]@12
  TimeLimitJade *v10; // [sp+50h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pkItema; // [sp+58h] [bp+10h]@1

  pkItema = pkItem;
  v10 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !bItemDel )
    TimeLimitJade::InsertWaitList(v10, pkItem);
  if ( ListHeap<TimeLimitJade::UseCell>::empty(&v10->_heapUseRow) )
  {
    result = 0;
  }
  else
  {
    v8 = ListHeap<TimeLimitJade::UseCell>::top(&v10->_heapUseRow);
    if ( v8 )
    {
      if ( v8->_pkItem == pkItema )
      {
        CPlayer::SetHaveEffectUseTime(v10->_pkOwner, pkItema, 0);
        result = ListHeap<TimeLimitJade::UseCell>::pop(&v10->_heapUseRow);
      }
      else
      {
        TimeLimitJade::UseCell::UseCell(&v9, pkItema);
        if ( ListHeap<TimeLimitJade::UseCell>::pop(&v10->_heapUseRow, v6) )
        {
          CPlayer::SetHaveEffectUseTime(v10->_pkOwner, pkItema, 0);
          result = 1;
        }
        else
        {
          result = 0;
        }
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
