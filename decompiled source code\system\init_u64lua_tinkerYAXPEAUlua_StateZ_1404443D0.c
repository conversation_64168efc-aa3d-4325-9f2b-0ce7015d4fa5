/*
 * Function: ?init_u64@lua_tinker@@YAXPEAUlua_State@@@Z
 * Address: 0x1404443D0
 */

void __fastcall lua_tinker::init_u64(struct lua_State *L)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  const char *v4; // [sp+20h] [bp-18h]@4
  struct lua_State *v5; // [sp+40h] [bp+8h]@1

  v5 = L;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = "__u64";
  lua_pushstring(v5, "__u64");
  lua_createtable(v5, 0i64, 0i64);
  lua_pushstring(v5, "__name");
  lua_pushstring(v5, v4);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__tostring");
  lua_pushcclosure(v5, tostring_u64, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__eq");
  lua_pushcclosure(v5, eq_u64, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__lt");
  lua_pushcclosure(v5, lt_u64, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_pushstring(v5, "__le");
  lua_pushcclosure(v5, le_u64, 0i64);
  lua_rawset(v5, 4294967293i64);
  lua_settable(v5, 4294957294i64);
}
