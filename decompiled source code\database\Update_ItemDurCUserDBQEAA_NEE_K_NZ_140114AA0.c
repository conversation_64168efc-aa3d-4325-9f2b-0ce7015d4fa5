/*
 * Function: ?Update_ItemDur@CUserDB@@QEAA_NEE_K_N@Z
 * Address: 0x140114AA0
 */

char __fastcall CUserDB::Update_ItemDur(CUserDB *this, char storage, char slot, unsigned __int64 amount, bool bUpdate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-78h]@1
  int v9; // [sp+20h] [bp-58h]@5
  _INVENKEY *v10; // [sp+30h] [bp-48h]@7
  _EMBELLKEY *v11; // [sp+38h] [bp-40h]@11
  _FORCEKEY *v12; // [sp+40h] [bp-38h]@15
  _ANIMUSKEY *v13; // [sp+48h] [bp-30h]@19
  _INVENKEY *v14; // [sp+50h] [bp-28h]@23
  _INVENKEY *v15; // [sp+58h] [bp-20h]@27
  _INVENKEY *v16; // [sp+60h] [bp-18h]@31
  CUserDB *v17; // [sp+80h] [bp+8h]@1
  char v18; // [sp+88h] [bp+10h]@1
  char v19; // [sp+90h] [bp+18h]@1
  unsigned __int64 pl_dwStat; // [sp+98h] [bp+20h]@1

  pl_dwStat = amount;
  v19 = slot;
  v18 = storage;
  v17 = this;
  v5 = &v8;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( !IsStorageRange(storage, slot) )
  {
    v9 = (unsigned __int8)v19;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_ItemDur(CODE) : scode : %d, icode : %d  ",
      v17->m_aszAvatorName,
      (unsigned __int8)v18);
    return 0;
  }
  if ( v18 )
  {
    switch ( v18 )
    {
      case 2:
        v11 = (_EMBELLKEY *)((char *)&v17->m_AvatorData.dbEquip + 27 * (unsigned __int8)v19);
        if ( !_EMBELLKEY::IsFilled(v11) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemDur(EMBELL, Idx:%d)",
            v17->m_aszAvatorName,
            (unsigned __int8)v19);
          return 0;
        }
        *(_WORD *)&v11[1].bySlotIndex = pl_dwStat;
        break;
      case 3:
        v12 = (_FORCEKEY *)((char *)&v17->m_AvatorData.dbForce + 25 * (unsigned __int8)v19);
        if ( !_FORCEKEY::IsFilled(v12) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemDur(FORCE, Idx:%d)",
            v17->m_aszAvatorName,
            (unsigned __int8)v19);
          return 0;
        }
        _FORCEKEY::SetStat(v12, pl_dwStat);
        break;
      case 4:
        v13 = (_ANIMUSKEY *)&v17->m_AvatorData.dbAnimus + 34 * (unsigned __int8)v19;
        if ( !_ANIMUSKEY::IsFilled(v13) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemDur(ANIMUS, Idx:%d)",
            v17->m_aszAvatorName,
            (unsigned __int8)v19);
          return 0;
        }
        *(_QWORD *)&v13[1].byItemIndex = pl_dwStat;
        break;
      case 5:
        v14 = &v17->m_AvatorData.dbTrunk.m_List[(unsigned __int8)v19].Key;
        if ( !_INVENKEY::IsFilled(v14) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemDur(TRUNK, Idx:%d)",
            v17->m_aszAvatorName,
            (unsigned __int8)v19);
          return 0;
        }
        *(_QWORD *)&v14[1].bySlotIndex = pl_dwStat;
        break;
      case 6:
        v15 = (_INVENKEY *)&v17->m_AvatorData.dbPersonalAmineInven.m_List[(unsigned __int8)v19];
        if ( !_INVENKEY::IsFilled(v15) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemDur(TRUNK, Idx:%d)",
            v17->m_aszAvatorName,
            (unsigned __int8)v19);
          return 0;
        }
        v15[1] = (_INVENKEY)pl_dwStat;
        break;
      case 7:
        v16 = &v17->m_AvatorData.dbTrunk.m_ExtList[(unsigned __int8)v19].Key;
        if ( !_INVENKEY::IsFilled(v16) )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "%s:Update_ItemDur(EXT_TRUNK, Idx:%d)",
            v17->m_aszAvatorName,
            (unsigned __int8)v19);
          return 0;
        }
        *(_QWORD *)&v16[1].bySlotIndex = pl_dwStat;
        break;
    }
  }
  else
  {
    v10 = (_INVENKEY *)((char *)&v17->m_AvatorData.dbInven + 37 * (unsigned __int8)v19);
    if ( !_INVENKEY::IsFilled(v10) )
    {
      CLogFile::Write(&stru_1799C8E78, "%s:Update_ItemDur(INVEN, Idx:%d)", v17->m_aszAvatorName, (unsigned __int8)v19);
      return 0;
    }
    *(_QWORD *)&v10[1].bySlotIndex = pl_dwStat;
  }
  v17->m_bDataUpdate = 1;
  return 1;
}
