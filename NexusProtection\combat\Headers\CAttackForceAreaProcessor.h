/*
 * CAttackForceAreaProcessor.h - Area and Flash Damage Processing System
 * Handles area damage, flash damage, and sector damage calculations
 */

#pragma once

#include "CAttackForceConstants.h"
#include "CAttackForceDamageCalculator.h"
#include "../Headers/CDamageProcessor.h"

#include <vector>
#include <memory>
#include <functional>

// Forward declarations
class CCharacter;
struct _attack_param;
struct _base_fld;

namespace NexusProtection {
namespace Combat {

/**
 * Area damage configuration
 */
struct AreaDamageConfig {
    float fCenterX{0.0f};
    float fCenterY{0.0f};
    float fCenterZ{0.0f};
    float fRadius{0.0f};
    float fInnerRadius{0.0f};
    bool bUseFalloff{true};
    bool bIgnoreObstacles{false};
    int nMaxTargets{50};
    
    AreaDamageConfig() = default;
    
    bool IsValid() const {
        return fRadius > 0.0f && nMaxTargets > 0;
    }
    
    bool IsInRange(float x, float y, float z) const {
        float fDistSq = (x - fCenterX) * (x - fCenterX) + 
                       (y - fCenterY) * (y - fCenterY) + 
                       (z - fCenterZ) * (z - fCenterZ);
        return fDistSq <= (fRadius * fRadius);
    }
    
    float GetDamageFalloff(float x, float y, float z) const {
        if (!bUseFalloff) return 1.0f;
        
        float fDist = std::sqrt((x - fCenterX) * (x - fCenterX) + 
                               (y - fCenterY) * (y - fCenterY) + 
                               (z - fCenterZ) * (z - fCenterZ));
        
        if (fDist <= fInnerRadius) return 1.0f;
        if (fDist >= fRadius) return 0.0f;
        
        return 1.0f - ((fDist - fInnerRadius) / (fRadius - fInnerRadius));
    }
};

/**
 * Flash damage configuration
 */
struct FlashDamageConfig {
    float fOriginX{0.0f};
    float fOriginY{0.0f};
    float fOriginZ{0.0f};
    float fDirectionX{1.0f};
    float fDirectionY{0.0f};
    float fDirectionZ{0.0f};
    float fMaxDistance{0.0f};
    float fAngle{0.0f};
    bool bUseFalloff{true};
    int nMaxTargets{50};
    
    FlashDamageConfig() = default;
    
    bool IsValid() const {
        return fMaxDistance > 0.0f && fAngle > 0.0f && nMaxTargets > 0;
    }
    
    bool IsInCone(float x, float y, float z) const {
        // Vector from origin to target
        float fTargetX = x - fOriginX;
        float fTargetY = y - fOriginY;
        float fTargetZ = z - fOriginZ;
        
        // Distance check
        float fDistSq = fTargetX * fTargetX + fTargetY * fTargetY + fTargetZ * fTargetZ;
        if (fDistSq > fMaxDistance * fMaxDistance) return false;
        
        // Angle check
        float fDist = std::sqrt(fDistSq);
        if (fDist < 0.001f) return true; // Very close to origin
        
        float fDotProduct = (fTargetX * fDirectionX + fTargetY * fDirectionY + fTargetZ * fDirectionZ) / fDist;
        float fTargetAngle = std::acos(std::max(-1.0f, std::min(1.0f, fDotProduct))) * 180.0f / 3.14159f;
        
        return fTargetAngle <= fAngle;
    }
    
    float GetDamageFalloff(float x, float y, float z) const {
        if (!bUseFalloff) return 1.0f;
        
        float fDist = std::sqrt((x - fOriginX) * (x - fOriginX) + 
                               (y - fOriginY) * (y - fOriginY) + 
                               (z - fOriginZ) * (z - fOriginZ));
        
        if (fDist >= fMaxDistance) return 0.0f;
        
        return 1.0f - (fDist / fMaxDistance);
    }
};

/**
 * Sector damage configuration (combination of area and directional)
 */
struct SectorDamageConfig {
    float fOriginX{0.0f};
    float fOriginY{0.0f};
    float fOriginZ{0.0f};
    float fDirectionX{1.0f};
    float fDirectionY{0.0f};
    float fDirectionZ{0.0f};
    float fRadius{0.0f};
    float fAngle{0.0f};
    bool bUseFalloff{true};
    int nMaxTargets{50};
    
    SectorDamageConfig() = default;
    
    bool IsValid() const {
        return fRadius > 0.0f && fAngle > 0.0f && nMaxTargets > 0;
    }
    
    bool IsInSector(float x, float y, float z) const {
        // Distance check
        float fDistSq = (x - fOriginX) * (x - fOriginX) + 
                       (y - fOriginY) * (y - fOriginY) + 
                       (z - fOriginZ) * (z - fOriginZ);
        if (fDistSq > fRadius * fRadius) return false;
        
        // Angle check
        float fDist = std::sqrt(fDistSq);
        if (fDist < 0.001f) return true; // Very close to origin
        
        float fTargetX = (x - fOriginX) / fDist;
        float fTargetY = (y - fOriginY) / fDist;
        float fTargetZ = (z - fOriginZ) / fDist;
        
        float fDotProduct = fTargetX * fDirectionX + fTargetY * fDirectionY + fTargetZ * fDirectionZ;
        float fTargetAngle = std::acos(std::max(-1.0f, std::min(1.0f, fDotProduct))) * 180.0f / 3.14159f;
        
        return fTargetAngle <= fAngle;
    }
    
    float GetDamageFalloff(float x, float y, float z) const {
        if (!bUseFalloff) return 1.0f;
        
        float fDist = std::sqrt((x - fOriginX) * (x - fOriginX) + 
                               (y - fOriginY) * (y - fOriginY) + 
                               (z - fOriginZ) * (z - fOriginZ));
        
        if (fDist >= fRadius) return 0.0f;
        
        return 1.0f - (fDist / fRadius);
    }
};

/**
 * Area damage processing result
 */
struct AreaDamageResult {
    std::vector<CCharacter*> hitTargets;
    std::vector<int> damageValues;
    std::vector<float> falloffValues;
    int nTotalDamage{0};
    int nTargetsHit{0};
    bool bSuccess{false};
    std::string errorMessage;
    
    AreaDamageResult() = default;
    
    void AddTarget(CCharacter* pTarget, int nDamage, float fFalloff = 1.0f) {
        if (pTarget && nDamage > 0) {
            hitTargets.push_back(pTarget);
            damageValues.push_back(nDamage);
            falloffValues.push_back(fFalloff);
            nTotalDamage += nDamage;
            nTargetsHit++;
        }
    }
    
    void Reset() {
        hitTargets.clear();
        damageValues.clear();
        falloffValues.clear();
        nTotalDamage = 0;
        nTargetsHit = 0;
        bSuccess = false;
        errorMessage.clear();
    }
    
    bool IsValid() const {
        return bSuccess && nTargetsHit > 0;
    }
};

/**
 * Area and Flash Damage Processing System
 */
class CAttackForceAreaProcessor {
public:
    /**
     * Constructor
     * @param pDamageCalculator Damage calculator instance
     */
    explicit CAttackForceAreaProcessor(std::shared_ptr<CAttackForceDamageCalculator> pDamageCalculator);
    
    /**
     * Destructor
     */
    virtual ~CAttackForceAreaProcessor();
    
    /**
     * Process area damage attack
     * @param pAttacker Attacking character
     * @param config Area damage configuration
     * @param pParam Attack parameters
     * @return Area damage result
     */
    AreaDamageResult ProcessAreaDamage(CCharacter* pAttacker, const AreaDamageConfig& config, _attack_param* pParam);
    
    /**
     * Process flash damage attack
     * @param pAttacker Attacking character
     * @param config Flash damage configuration
     * @param pParam Attack parameters
     * @return Area damage result (reused structure)
     */
    AreaDamageResult ProcessFlashDamage(CCharacter* pAttacker, const FlashDamageConfig& config, _attack_param* pParam);
    
    /**
     * Process sector damage attack
     * @param pAttacker Attacking character
     * @param config Sector damage configuration
     * @param pParam Attack parameters
     * @return Area damage result (reused structure)
     */
    AreaDamageResult ProcessSectorDamage(CCharacter* pAttacker, const SectorDamageConfig& config, _attack_param* pParam);
    
    /**
     * Find targets in area
     * @param config Area damage configuration
     * @param pAttacker Attacking character (to exclude)
     * @return List of potential targets
     */
    std::vector<CCharacter*> FindTargetsInArea(const AreaDamageConfig& config, CCharacter* pAttacker);
    
    /**
     * Find targets in flash cone
     * @param config Flash damage configuration
     * @param pAttacker Attacking character (to exclude)
     * @return List of potential targets
     */
    std::vector<CCharacter*> FindTargetsInFlash(const FlashDamageConfig& config, CCharacter* pAttacker);
    
    /**
     * Find targets in sector
     * @param config Sector damage configuration
     * @param pAttacker Attacking character (to exclude)
     * @return List of potential targets
     */
    std::vector<CCharacter*> FindTargetsInSector(const SectorDamageConfig& config, CCharacter* pAttacker);
    
    /**
     * Calculate damage for target with falloff
     * @param pAttacker Attacking character
     * @param pTarget Target character
     * @param pParam Attack parameters
     * @param fFalloff Damage falloff multiplier
     * @return Calculated damage
     */
    int CalculateTargetDamage(CCharacter* pAttacker, CCharacter* pTarget, _attack_param* pParam, float fFalloff = 1.0f);
    
    /**
     * Check if target is valid for area damage
     * @param pAttacker Attacking character
     * @param pTarget Potential target
     * @return true if target is valid
     */
    bool IsValidTarget(CCharacter* pAttacker, CCharacter* pTarget);
    
    /**
     * Set maximum targets per attack
     * @param nMaxTargets Maximum number of targets
     */
    void SetMaxTargets(int nMaxTargets) { m_nMaxTargets = nMaxTargets; }
    
    /**
     * Get maximum targets per attack
     * @return Maximum number of targets
     */
    int GetMaxTargets() const { return m_nMaxTargets; }

protected:
    /**
     * Get all characters in range
     * @param fCenterX Center X coordinate
     * @param fCenterY Center Y coordinate
     * @param fCenterZ Center Z coordinate
     * @param fRadius Search radius
     * @return List of characters in range
     */
    virtual std::vector<CCharacter*> GetCharactersInRange(float fCenterX, float fCenterY, float fCenterZ, float fRadius);
    
    /**
     * Check line of sight between two points
     * @param fFromX From X coordinate
     * @param fFromY From Y coordinate
     * @param fFromZ From Z coordinate
     * @param fToX To X coordinate
     * @param fToY To Y coordinate
     * @param fToZ To Z coordinate
     * @return true if line of sight is clear
     */
    virtual bool CheckLineOfSight(float fFromX, float fFromY, float fFromZ, float fToX, float fToY, float fToZ);

private:
    std::shared_ptr<CAttackForceDamageCalculator> m_pDamageCalculator;
    int m_nMaxTargets{50};
    
    // Disable copy constructor and assignment operator
    CAttackForceAreaProcessor(const CAttackForceAreaProcessor&) = delete;
    CAttackForceAreaProcessor& operator=(const CAttackForceAreaProcessor&) = delete;
};

/**
 * Area damage utility functions
 */
namespace AreaDamageUtils {
    /**
     * Create area damage config from legacy parameters
     * @param pParam Attack parameters
     * @param nLimitRadius Radius limit
     * @return Area damage configuration
     */
    AreaDamageConfig CreateAreaConfig(_attack_param* pParam, int nLimitRadius);
    
    /**
     * Create flash damage config from legacy parameters
     * @param pParam Attack parameters
     * @param nLimitDistance Distance limit
     * @param nLimitAngle Angle limit
     * @return Flash damage configuration
     */
    FlashDamageConfig CreateFlashConfig(_attack_param* pParam, int nLimitDistance, int nLimitAngle);
    
    /**
     * Create sector damage config from legacy parameters
     * @param pParam Attack parameters
     * @param nLimitRadius Radius limit
     * @param nLimitAngle Angle limit
     * @return Sector damage configuration
     */
    SectorDamageConfig CreateSectorConfig(_attack_param* pParam, int nLimitRadius, int nLimitAngle);
    
    /**
     * Calculate 3D distance between two points
     * @param x1 First point X
     * @param y1 First point Y
     * @param z1 First point Z
     * @param x2 Second point X
     * @param y2 Second point Y
     * @param z2 Second point Z
     * @return Distance
     */
    float CalculateDistance3D(float x1, float y1, float z1, float x2, float y2, float z2);
    
    /**
     * Normalize a 3D vector
     * @param x Vector X component (modified)
     * @param y Vector Y component (modified)
     * @param z Vector Z component (modified)
     * @return Vector length before normalization
     */
    float NormalizeVector3D(float& x, float& y, float& z);
}

} // namespace Combat
} // namespace NexusProtection
