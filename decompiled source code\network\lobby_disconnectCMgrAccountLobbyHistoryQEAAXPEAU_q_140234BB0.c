/*
 * Function: ?lobby_disconnect@CMgrAccountLobbyHistory@@QEAAXPEAU_qry_case_lobby_logout@@PEAD@Z
 * Address: 0x140234BB0
 */

void __fastcall CMgrAccountLobbyHistory::lobby_disconnect(CMgrAccountLobbyHistory *this, _qry_case_lobby_logout *pRegeData, char *pszFileName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned int v6; // [sp+20h] [bp-48h]@11
  int v7; // [sp+28h] [bp-40h]@11
  unsigned int v8; // [sp+30h] [bp-38h]@11
  unsigned int v9; // [sp+38h] [bp-30h]@11
  bool v10; // [sp+40h] [bp-28h]@4
  unsigned int j; // [sp+44h] [bp-24h]@8
  bool v12; // [sp+48h] [bp-20h]@4
  const char *v13; // [sp+50h] [bp-18h]@5
  CMgrAccountLobbyHistory *v14; // [sp+70h] [bp+8h]@1
  _qry_case_lobby_logout *v15; // [sp+78h] [bp+10h]@1
  char *pszFileNamea; // [sp+80h] [bp+18h]@1

  pszFileNamea = pszFileName;
  v15 = pRegeData;
  v14 = this;
  v3 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  sLData[0] = 0;
  v12 = pRegeData->byDBRet == 0;
  v10 = v12;
  if ( v12 )
    v13 = "SUCCESS";
  else
    v13 = "ERROR";
  sprintf_s<10240>((char (*)[10240])sLBuf, "Lobby Logout RegedDB Result: %d (%s)\r\n", pRegeData->byDBRet, v13);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  sprintf_s<10240>((char (*)[10240])sLBuf, "AccountSerial: %d\r\n", v15->dwAccountSerial);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  if ( v10 )
  {
    sprintf_s<10240>((char (*)[10240])sLBuf, "CharNum: %d\r\n", v15->nRegeNum);
    strcat_s<20000>((char (*)[20000])sLData, sLBuf);
    for ( j = 0; (signed int)j < 3; ++j )
    {
      if ( v15->RegeList[j].bySlotIndex != 255 )
      {
        v9 = v15->RegeList[j].dwGold;
        v8 = v15->RegeList[j].dwDalant;
        v7 = v15->RegeList[j].nLevel;
        v6 = v15->RegeList[j].dwCharSerial;
        sprintf_s<10240>(
          (char (*)[10240])sLBuf,
          "[Slot%d]\r\nNAME: %s\r\nCharSR: %d\r\nLV: %d\r\n$D: %d\r\n$G: %d\r\n\r\n",
          j,
          v15->RegeList[j].szCharName);
        strcat_s<20000>((char (*)[20000])sLData, sLBuf);
      }
    }
  }
  sprintf_s<10240>(
    (char (*)[10240])sLBuf,
    "Lobby Logout RegedDB Complete [%s %s]\r\n",
    v14->m_szCurDate,
    v14->m_szCurTime);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v14, pszFileNamea, sLData);
}
