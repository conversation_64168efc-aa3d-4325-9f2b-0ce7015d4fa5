/*
 * Function: j_??$_Max_element@PEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@VCTopKillPrediCate@CNormalGuildBattleGuild@2@@std@@YAPEAVCNormalGuildBattleGuildMember@GUILD_BATTLE@@PEAV12@0VCTopKillPrediCate@CNormalGuildBattleGuild@2@@Z
 * Address: 0x140005B32
 */

GUILD_BATTLE::CNormalGuildBattleGuildMember *__fastcall std::_Max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate>(GUILD_BATTLE::CNormalGuildBattleGuildMember *_First, GUILD_BATTLE::CNormalGuildBattleGuildMember *_Last, GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate _Pred)
{
  return std::_Max_element<GUILD_BATTLE::CNormalGuildBattleGuildMember *,GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate>(
           _First,
           _Last,
           _Pred);
}
