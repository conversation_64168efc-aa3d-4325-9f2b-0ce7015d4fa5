/*
 * Function: ?Loop@CNationSettingDataCN@@UEAAXXZ
 * Address: 0x1402306A0
 */

void __fastcall CNationSettingDataCN::Loop(CNationSettingDataCN *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CChiNetworkEX *v3; // rax@4
  CChiNetworkEX *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-28h]@1

  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CChiNetworkEX::Instance();
  CChiNetworkEX::CheckApexLine(v3);
  v4 = CChiNetworkEX::Instance();
  v4->OnLoop();
}
