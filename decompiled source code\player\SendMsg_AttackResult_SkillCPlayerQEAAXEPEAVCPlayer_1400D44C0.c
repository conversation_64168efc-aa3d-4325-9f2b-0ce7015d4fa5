/*
 * Function: ?SendMsg_AttackResult_Skill@CPlayer@@QEAAXEPEAVCPlayerAttack@@G@Z
 * Address: 0x1400D44C0
 */

void __fastcall CPlayer::SendMsg_AttackResult_Skill(CPlayer *this, char byEffectCode, CPlayerAttack *pAt, unsigned __int16 wBulletIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@7
  __int64 v7; // [sp+0h] [bp-1E8h]@1
  _attack_skill_result_zocl v8; // [sp+40h] [bp-1A8h]@4
  int j; // [sp+1A4h] [bp-44h]@4
  char pbyType; // [sp+1B4h] [bp-34h]@7
  char v11; // [sp+1B5h] [bp-33h]@7
  unsigned __int64 v12; // [sp+1D0h] [bp-18h]@4
  CPlayer *v13; // [sp+1F0h] [bp+8h]@1
  char v14; // [sp+1F8h] [bp+10h]@1
  CPlayerAttack *v15; // [sp+200h] [bp+18h]@1
  unsigned __int16 v16; // [sp+208h] [bp+20h]@1

  v16 = wBulletIndex;
  v15 = pAt;
  v14 = byEffectCode;
  v13 = this;
  v4 = &v7;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v12 = (unsigned __int64)&v7 ^ _security_cookie;
  _attack_skill_result_zocl::_attack_skill_result_zocl(&v8);
  v8.byAtterID = v13->m_ObjID.m_byID;
  v8.dwAtterSerial = v13->m_dwObjSerial;
  v8.byEffectCode = v14;
  v8.wSkillIndex = v15->m_pp->pFld->m_dwIndex;
  v8.bySkillLv = v15->m_pp->nLevel;
  v8.byAttackPart = v15->m_pp->nPart;
  v8.bCritical = v15->m_bIsCrtAtt;
  v8.wBulletIndex = v16;
  v8.zAttackPos[0] = (signed int)ffloor(v15->m_pp->fArea[0]);
  v8.zAttackPos[1] = (signed int)ffloor(v15->m_pp->fArea[2]);
  v8.bWPActive = v15->m_bActiveSucc;
  v8.byListNum = v15->m_nDamagedObjNum;
  for ( j = 0; j < v15->m_nDamagedObjNum; ++j )
  {
    v8.DamList[j].byDstID = v15->m_DamList[j].m_pChar->m_ObjID.m_byID;
    v8.DamList[j].dwDstSerial = v15->m_DamList[j].m_pChar->m_dwObjSerial;
    v8.DamList[j].wDamage = v15->m_DamList[j].m_nDamage;
    v8.DamList[j].bActive = v15->m_DamList[j].m_bActiveSucc;
    v8.DamList[j].wActiveDamage = v15->m_DamList[j].m_nActiveDamage;
  }
  pbyType = 5;
  v11 = 8;
  v6 = _attack_skill_result_zocl::size(&v8);
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, &v8.byAtterID, v6, 1);
}
