/*
 * Function: ?SendMsg_FixPosition@CItemBox@@UEAAXH@Z
 * Address: 0x1401668B0
 */

void __fastcall CItemBox::SendMsg_FixPosition(CItemBox *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@5
  __int16 v6; // [sp+39h] [bp-5Fh]@5
  char v7; // [sp+3Bh] [bp-5Dh]@5
  unsigned __int16 v8; // [sp+3Ch] [bp-5Ch]@5
  unsigned int v9; // [sp+3Eh] [bp-5Ah]@5
  __int16 pShort; // [sp+42h] [bp-56h]@5
  char v11; // [sp+48h] [bp-50h]@5
  char v12; // [sp+49h] [bp-4Fh]@5
  char pbyType; // [sp+64h] [bp-34h]@5
  char v14; // [sp+65h] [bp-33h]@5
  unsigned __int64 v15; // [sp+80h] [bp-18h]@4
  CItemBox *v16; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v16 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( !v16->m_bHide )
  {
    szMsg = v16->m_Item.m_byTableCode;
    v6 = v16->m_pRecordSet->m_dwIndex;
    v7 = v16->m_Item.m_dwDur;
    v11 = v16->m_nStateCode;
    v8 = v16->m_ObjID.m_wIndex;
    v9 = v16->m_dwOwnerSerial;
    v12 = v16->m_byThrowerRaceCode;
    FloatToShort(v16->m_fCurPos, &pShort, 3);
    pbyType = 4;
    v14 = 15;
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 0x12u);
  }
}
