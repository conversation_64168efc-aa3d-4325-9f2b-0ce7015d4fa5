/*
 * Function: ?SendMsg_PartyJoinJoinerR<PERSON>ult@CPlayer@@QEAAXXZ
 * Address: 0x1400DCE50
 */

void __fastcall CPlayer::SendMsg_PartyJoinJoinerResult(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@11
  __int64 v4; // [sp+0h] [bp-158h]@1
  _party_join_joiner_result_zocl v5; // [sp+40h] [bp-118h]@4
  CPartyPlayer **v6; // [sp+108h] [bp-50h]@4
  int j; // [sp+110h] [bp-48h]@5
  char pbyType; // [sp+124h] [bp-34h]@11
  char v9; // [sp+125h] [bp-33h]@11
  unsigned __int64 v10; // [sp+140h] [bp-18h]@4
  CPlayer *v11; // [sp+160h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  _party_join_joiner_result_zocl::_party_join_joiner_result_zocl(&v5);
  v6 = CPartyPlayer::GetPtrPartyMember(v11->m_pPartyMgr);
  if ( v6 )
  {
    v5.byLootShareMode = v11->m_pPartyMgr->m_pPartyBoss->m_byLootShareSystem;
    v5.byListNum = CPartyPlayer::GetPopPartyMember(v11->m_pPartyMgr) - 1;
    for ( j = 0; j < (unsigned __int8)v5.byListNum && v6[j]; ++j )
    {
      if ( v6[j] != v11->m_pPartyMgr )
      {
        v5.List[j].wIndex = v6[j]->m_id.wIndex;
        v5.List[j].dwSerial = v6[j]->m_id.dwSerial;
        strcpy_0(v5.List[j].wszAvatorName, v6[j]->m_wszName);
      }
    }
    pbyType = 16;
    v9 = 7;
    v3 = _party_join_joiner_result_zocl::size(&v5);
    CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &v5.byLootShareMode, v3);
  }
}
