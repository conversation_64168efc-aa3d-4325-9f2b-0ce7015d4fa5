/*
 * Function: ?invoke@?$lua2object@PEAVCMonster@@@lua_tinker@@SAPEAVCMonster@@PEAUlua_State@@H@Z
 * Address: 0x14040B1C0
 */

CMonster *__fastcall lua_tinker::lua2object<CMonster *>::invoke(lua_tinker::lua2object<CMonster *> *this, struct lua_State *L, __int64 index)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // er8@4
  lua_tinker::user *v6; // rax@7
  void *v7; // rdx@7
  __int64 v9; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<lua_tinker::user *> *v10; // [sp+30h] [bp+8h]@1
  int La; // [sp+38h] [bp+10h]@1

  La = (signed int)L;
  v10 = (lua_tinker::user2type<lua_tinker::user *> *)this;
  v3 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !lua_isuserdata(v10, (unsigned int)L, index) )
  {
    lua_pushstring(v10, "no class at first argument. (forgot ':' expression ?)");
    lua_error(v10);
  }
  v6 = lua_tinker::user2type<lua_tinker::user *>::invoke(v10, (struct lua_State *)(unsigned int)La, v5);
  return lua_tinker::void2type<CMonster *>::invoke((lua_tinker::void2type<CMonster *> *)v6->m_p, v7);
}
