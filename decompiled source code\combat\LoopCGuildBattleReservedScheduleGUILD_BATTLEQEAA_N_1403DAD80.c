/*
 * Function: ?Loop@CGuildBattleReservedSchedule@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403DAD80
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedSchedule::Loop(GUILD_BATTLE::CGuildBattleReservedSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int iRet; // [sp+20h] [bp-18h]@9
  GUILD_BATTLE::CGuildBattleReservedSchedule *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_bDone )
  {
    result = 0;
  }
  else if ( v6->m_pkSchedule[v6->m_uiCurScheduleInx] || GUILD_BATTLE::CGuildBattleReservedSchedule::Next(v6) )
  {
    iRet = GUILD_BATTLE::CGuildBattleSchedule::Check(v6->m_pkSchedule[v6->m_uiCurScheduleInx]);
    result = GUILD_BATTLE::CGuildBattleReservedSchedule::CheckNextEvent(v6, iRet);
  }
  else
  {
    v6->m_bDone = 1;
    result = 0;
  }
  return result;
}
