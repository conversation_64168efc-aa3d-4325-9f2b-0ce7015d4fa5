/*
 * Function: ?Regist@CUnmannedTraderUserInfoTable@@QEAAXGEPEAU_a_trade_reg_item_request_clzo@@@Z
 * Address: 0x1401D45B0
 */

void __fastcall CUnmannedTraderUserInfoTable::Regist(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, char byType, _a_trade_reg_item_request_clzo *pRequest)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfo *v6; // rax@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v8; // [sp+30h] [bp+8h]@1
  unsigned __int16 v9; // [sp+38h] [bp+10h]@1
  char v10; // [sp+40h] [bp+18h]@1
  _a_trade_reg_item_request_clzo *pRequesta; // [sp+48h] [bp+20h]@1

  pRequesta = pRequest;
  v10 = byType;
  v9 = wInx;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CUnmannedTraderUserInfoTable::CheckwIndexAndType(v8, wInx, byType, "CUnmannedTraderUserInfoTable::Regist(...)") )
  {
    v6 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v8->m_veckInfo, v9);
    CUnmannedTraderUserInfo::Regist(v6, v10, pRequesta, v8->m_pkLogger);
  }
}
