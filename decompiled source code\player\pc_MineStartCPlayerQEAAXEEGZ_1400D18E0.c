/*
 * Function: ?pc_MineStart@CPlayer@@QEAAXEEG@Z
 * Address: 0x1400D18E0
 */

void __usercall CPlayer::pc_MineStart(CPlayer *this@<rcx>, char by<PERSON>ineIndex@<dl>, char byOreIndex@<r8b>, unsigned __int16 wBatterySerial@<r9w>, double a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v7; // rax@30
  COreAmountMgr *v8; // rax@41
  bool v9; // al@44
  float v10; // xmm0_4@46
  bool v11; // al@51
  __int64 v12; // [sp+0h] [bp-78h]@1
  char v13; // [sp+20h] [bp-58h]@4
  _base_fld *v14; // [sp+28h] [bp-50h]@4
  char *v15; // [sp+30h] [bp-48h]@4
  _res_dummy *v16; // [sp+38h] [bp-40h]@4
  int v17; // [sp+40h] [bp-38h]@4
  _base_fld *v18; // [sp+48h] [bp-30h]@4
  _STORAGE_LIST::_db_con *v19; // [sp+50h] [bp-28h]@4
  int v20; // [sp+58h] [bp-20h]@44
  float v21; // [sp+5Ch] [bp-1Ch]@44
  double v22; // [sp+60h] [bp-18h]@44
  int v23; // [sp+68h] [bp-10h]@44
  float v24; // [sp+6Ch] [bp-Ch]@48
  CPlayer *v25; // [sp+80h] [bp+8h]@1
  char v26; // [sp+88h] [bp+10h]@1
  char v27; // [sp+90h] [bp+18h]@1
  unsigned __int16 v28; // [sp+98h] [bp+20h]@1

  v28 = wBatterySerial;
  v27 = byOreIndex;
  v26 = byMineIndex;
  v25 = this;
  v5 = &v12;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v13 = 0;
  v14 = 0i64;
  v15 = 0i64;
  v16 = 0i64;
  v17 = -1;
  v18 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 17, (unsigned __int8)byOreIndex);
  v19 = 0i64;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v25->m_id.wIndex) == 99 )
  {
    v13 = 33;
  }
  else if ( v25->m_Param.m_pAPM && AutominePersonal::is_installed(v25->m_Param.m_pAPM) )
  {
    v13 = 25;
  }
  else if ( v25->m_dwMineNextTime == -1 )
  {
    if ( *(_DWORD *)&v18[3].m_strCode[0] <= 2u )
    {
      if ( (unsigned __int8)v26 < v25->m_pCurMap->m_nResDumNum )
      {
        v16 = &v25->m_pCurMap->m_pResDummy[(unsigned __int8)v26];
        if ( v16 && _res_dummy::GetQualityGrade(v16) != 2 )
        {
          if ( _res_dummy::GetQualityGrade(v16) == 2 || CPlayer::IsMiningByMinigTicket(v25) )
          {
            v17 = CMapData::GetResDummySector(v25->m_pCurMap, (unsigned __int8)v26, v25->m_fCurPos);
            if ( v17 == -1 )
            {
              v13 = 2;
            }
            else
            {
              v15 = &v25->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
              if ( *v15 )
              {
                if ( v15[19] )
                {
                  v13 = 12;
                }
                else
                {
                  v14 = CRecordData::GetRecord(
                          (CRecordData *)&unk_1799C6AA0 + 6,
                          v25->m_Param.m_dbEquip.m_pStorageList[6].m_wItemIndex);
                  if ( v14 )
                  {
                    if ( *(_DWORD *)&v14[6].m_strCode[8] == 10 )
                    {
                      v7 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v25->m_Param.m_dbInven.m_nListNum, v28);
                      v19 = v7;
                      if ( v7 )
                      {
                        if ( v19->m_byTableCode == 16 )
                        {
                          if ( (signed int)v19->m_wItemIndex < 6 || (signed int)v19->m_wItemIndex > 9 )
                          {
                            if ( v19->m_bLock )
                            {
                              v13 = 12;
                            }
                            else
                            {
                              v8 = COreAmountMgr::Instance();
                              if ( !COreAmountMgr::IsOreRemain(v8) )
                                v13 = 27;
                            }
                          }
                          else
                          {
                            v13 = 8;
                          }
                        }
                        else
                        {
                          v13 = 8;
                        }
                      }
                      else
                      {
                        v13 = 8;
                      }
                    }
                    else
                    {
                      v13 = 5;
                    }
                  }
                  else
                  {
                    v13 = 5;
                  }
                }
              }
              else
              {
                v13 = 4;
              }
            }
          }
          else
          {
            v13 = 26;
          }
        }
        else
        {
          v13 = 28;
        }
      }
      else
      {
        v13 = 1;
      }
    }
    else
    {
      v13 = 9;
    }
  }
  else
  {
    v13 = 7;
  }
  if ( !v13 )
  {
    v25->m_bMineMode = 1;
    v9 = _BILLING_INFO::IsPcBangType(&v25->m_pUserDB->m_BillingInfo);
    v20 = _res_dummy::GetDelay(&v25->m_pCurMap->m_pResDummy[(unsigned __int8)v26], v17, v9);
    _effect_parameter::GetEff_Have(&v25->m_EP, 7);
    v21 = *(float *)&a5;
    TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v25->m_id.wIndex);
    v22 = a5;
    v23 = 0;
    if ( a5 != 0.0 )
      v23 = (signed int)floor(1.0 / v22);
    v10 = v21;
    if ( v21 > 1.0 )
    {
      v10 = (float)v20 / v21;
      v20 = (signed int)ffloor(v10);
    }
    _effect_parameter::GetEff_Rate(&v25->m_EP, 37);
    v24 = v10;
    if ( v10 > 1.0 )
      v20 = (signed int)ffloor((float)v20 / v24);
    v20 *= v23;
    v25->m_dwMineNextTime = v20 + GetLoopTime();
    v25->m_byDelaySec = v20 / 0x3E8u;
    v25->m_wBatterySerialTmp = v28;
    v25->m_bySelectOreIndex = v27;
    v25->m_zMinePos[0] = (signed int)ffloor(v25->m_fCurPos[0]);
    v25->m_zMinePos[1] = (signed int)ffloor(v25->m_fCurPos[2]);
    if ( v25->m_bMove )
    {
      v11 = CPlayer::IsOutExtraStopPos(v25, v25->m_fCurPos);
      CPlayer::SendMsg_Stop(v25, v11);
      CCharacter::Stop((CCharacter *)&v25->vfptr);
    }
    CCharacter::BreakStealth((CCharacter *)&v25->vfptr);
  }
  CPlayer::SendMsg_MineStartResult(v25, v13);
}
