/*
 * Function: ?CompleteUpdateState@CUnmannedTraderController@@QEAAXEPEAD@Z
 * Address: 0x14034F0E0
 */

void __fastcall CUnmannedTraderController::CompleteUpdateState(CUnmannedTraderController *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v5; // rax@8
  CUnmannedTraderGroupItemInfoTable *v6; // rax@9
  int v7; // ecx@11
  int v8; // edx@11
  int v9; // er8@11
  __int64 v10; // [sp+0h] [bp-68h]@1
  int v11; // [sp+20h] [bp-48h]@11
  int v12; // [sp+28h] [bp-40h]@11
  int v13; // [sp+30h] [bp-38h]@11
  int v14; // [sp+38h] [bp-30h]@11
  int v15; // [sp+40h] [bp-28h]@11
  int v16; // [sp+48h] [bp-20h]@11
  char *v17; // [sp+50h] [bp-18h]@4
  int v18; // [sp+58h] [bp-10h]@4
  int v19; // [sp+5Ch] [bp-Ch]@4
  CUnmannedTraderController *v20; // [sp+70h] [bp+8h]@1
  char v21; // [sp+78h] [bp+10h]@1

  v21 = byRet;
  v20 = this;
  v3 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = pLoadData;
  v18 = (unsigned __int8)pLoadData[8];
  v19 = v18;
  if ( (v18 == 3 || v19 == 5 || v19 == 7) && !byRet )
  {
    v5 = CUnmannedTraderUserInfoTable::Instance();
    if ( CUnmannedTraderUserInfoTable::CompleteUpdateState(v5, *((_DWORD *)v17 + 3), *((_DWORD *)v17 + 1), v17[8]) )
    {
      v6 = CUnmannedTraderGroupItemInfoTable::Instance();
      CUnmannedTraderGroupItemInfoTable::IncreaseVersion(v6, v17[16], *((_WORD *)v17 + 9));
    }
  }
  if ( v21 )
  {
    v7 = (unsigned __int8)v17[16];
    v8 = (unsigned __int8)v17[8];
    v9 = (unsigned __int8)*v17;
    v16 = *((_WORD *)v17 + 9);
    v15 = v7;
    v14 = *((_DWORD *)v17 + 3);
    v13 = v8;
    v12 = *((_DWORD *)v17 + 1);
    v11 = v9;
    CUnmannedTraderController::Log(
      v20,
      "CUnmannedTraderController::CompleteUpdateState( byRet(%u), char * pLoadData )\r\n"
      "\t\tType(%u) RegistSerial(%u) State(%u) : Owner(%u) TableCode(%u) TableIndex(%u)\r\n"
      "\t\tRET_CODE_SUCCESS != byRet\r\n",
      (unsigned __int8)v21,
      (unsigned __int8)v21);
  }
}
