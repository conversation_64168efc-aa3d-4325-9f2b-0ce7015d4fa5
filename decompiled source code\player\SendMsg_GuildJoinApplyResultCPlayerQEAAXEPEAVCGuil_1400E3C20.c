/*
 * Function: ?SendMsg_GuildJoinApply<PERSON><PERSON>ult@CPlayer@@QEAAXEPEAVCGuild@@@Z
 * Address: 0x1400E3C20
 */

void __fastcall CPlayer::SendMsg_GuildJoinApplyResult(CPlayer *this, char byRetCode, CGuild *pApplyGuild)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  unsigned int v7; // [sp+39h] [bp-5Fh]@5
  char Dest; // [sp+3Dh] [bp-5Bh]@5
  char pbyType; // [sp+64h] [bp-34h]@7
  char v10; // [sp+65h] [bp-33h]@7
  unsigned __int64 v11; // [sp+80h] [bp-18h]@4
  CPlayer *v12; // [sp+A0h] [bp+8h]@1

  v12 = this;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byRetCode;
  if ( pApplyGuild )
  {
    v7 = pApplyGuild->m_dwSerial;
    strcpy_0(&Dest, pApplyGuild->m_wszName);
  }
  else
  {
    v7 = -1;
    Dest = 0;
  }
  pbyType = 27;
  v10 = 7;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x16u);
}
