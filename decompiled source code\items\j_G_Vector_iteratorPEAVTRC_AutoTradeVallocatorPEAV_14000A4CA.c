/*
 * Function: j_??G?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBA?AV01@_J@Z
 * Address: 0x14000A4CA
 */

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator-(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result, __int64 _Off)
{
  return std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator-(this, result, _Off);
}
