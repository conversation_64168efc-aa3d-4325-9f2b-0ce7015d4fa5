/*
 * Function: ?CopyRangeTo2@MessageQueue@CryptoPP@@UEBA_KAEAVBufferedTransformation@2@AEA_K_KAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_N@Z
 * Address: 0x1406543C0
 */

int __fastcall CryptoPP::MessageQueue::CopyRangeTo2(__int64 a1, __int64 a2, unsigned __int64 *a3, unsigned __int64 a4)
{
  unsigned __int64 v4; // rax@1
  int result; // eax@2
  unsigned __int64 v6; // rax@3
  const unsigned __int64 *v7; // rax@3
  unsigned __int64 a; // [sp+30h] [bp-18h]@3
  __int64 v9; // [sp+38h] [bp-10h]@3
  __int64 v10; // [sp+50h] [bp+8h]@1
  __int64 v11; // [sp+58h] [bp+10h]@1
  unsigned __int64 *v12; // [sp+60h] [bp+18h]@1
  unsigned __int64 b; // [sp+68h] [bp+20h]@1

  b = a4;
  v12 = a3;
  v11 = a2;
  v10 = a1;
  LODWORD(v4) = (*(int (**)(void))(*(_QWORD *)a1 + 120i64))();
  if ( *v12 < v4 )
  {
    LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v10 + 120i64))(v10);
    a = v6;
    v7 = CryptoPP::STDMIN<unsigned __int64>(&a, &b);
    v9 = *(_QWORD *)(v10 + 32);
    result = (*(int (__fastcall **)(signed __int64, __int64, unsigned __int64 *, const unsigned __int64))(v9 + 256))(
               v10 + 32,
               v11,
               v12,
               *v7);
  }
  else
  {
    result = 0;
  }
  return result;
}
