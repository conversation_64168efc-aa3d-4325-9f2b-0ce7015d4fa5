/*
 * Function: _std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::_Insert_n_::_1_::catch$1
 * Address: 0x1403963A0
 */

void __fastcall __noreturn std::vector_CUnmannedTraderSchedule_std::allocator_CUnmannedTraderSchedule___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Destroy(
    *(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > **)(a2 + 240),
    (CUnmannedTraderSchedule *)(*(_QWORD *)(*(_QWORD *)(a2 + 248) + 16i64) + 32i64 * *(_QWORD *)(a2 + 256)),
    (CUnmannedTraderSchedule *)(*(_QWORD *)(*(_QWORD *)(a2 + 240) + 24i64) + 32i64 * *(_QWORD *)(a2 + 256)));
  CxxThrowException_0(0i64, 0i64);
}
