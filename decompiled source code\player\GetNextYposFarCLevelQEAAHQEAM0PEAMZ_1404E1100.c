/*
 * Function: ?GetNextYposFar@CLevel@@QEAAHQEAM0PEAM@Z
 * Address: 0x1404E1100
 */

__int64 __usercall CLevel::GetNextYposFar@<rax>(CLevel *this@<rcx>, float *const a2@<rdx>, float *const a3@<r8>, float *a4@<r9>, signed __int64 a5@<rax>)
{
  void *v5; // rsp@1
  bool v6; // zf@1
  float *v7; // r13@1
  float *v8; // rbp@1
  CLevel *v9; // r12@1
  __int64 result; // rax@2
  float v11; // xmm2_4@3
  float v12; // xmm3_4@3
  float v13; // xmm1_4@4
  float v14; // xmm1_4@7
  float v15; // xmm7_4@7
  float v16; // xmm0_4@7
  int v17; // xmm1_4@7
  float v18; // xmm9_4@8
  float v19; // xmm6_4@8
  float v20; // xmm7_4@9
  float v21; // xmm8_4@9
  float v22; // xmm6_4@9
  float v23; // xmm11_4@9
  float v24; // xmm10_4@9
  float v25; // xmm12_4@9
  float v26; // xmm0_4@9
  float v27; // xmm11_4@9
  int v28; // xmm12_4@9
  float v29; // xmm10_4@9
  float v30; // xmm6_4@11
  CBsp *v31; // rcx@13
  __int32 v32; // ebx@13
  signed int v33; // esi@13
  float v34; // xmm5_4@13
  float *v35; // rdi@14
  CBsp *v36; // rcx@15
  float v37; // xmm0_4@15
  float v38; // xmm1_4@21
  int v39; // xmm0_4@23
  __int64 v40; // [sp-20h] [bp-FB78h]@1
  unsigned __int32 v41; // [sp+8h] [bp-FB50h]@15
  float v42; // [sp+10h] [bp-FB48h]@7
  float v43; // [sp+14h] [bp-FB44h]@7
  int v44; // [sp+18h] [bp-FB40h]@7
  __int32 v45; // [sp+20h] [bp-FB38h]@1
  float v46; // [sp+28h] [bp-FB30h]@7
  float v47; // [sp+2Ch] [bp-FB2Ch]@7
  float v48; // [sp+30h] [bp-FB28h]@7
  float v49; // [sp+38h] [bp-FB20h]@13
  float v50; // [sp+3Ch] [bp-FB1Ch]@13
  int v51; // [sp+40h] [bp-FB18h]@13
  float v52; // [sp+48h] [bp-FB10h]@13
  float v53; // [sp+4Ch] [bp-FB0Ch]@13
  int v54; // [sp+50h] [bp-FB08h]@13
  float v55; // [sp+58h] [bp-FB00h]@13
  float v56; // [sp+5Ch] [bp-FAFCh]@13
  int v57; // [sp+60h] [bp-FAF8h]@13
  float v58; // [sp+70h] [bp-FAE8h]@13
  unsigned __int64 v59; // [sp+FA70h] [bp-E8h]@1

  v5 = alloca(a5);
  v59 = (unsigned __int64)&v40 ^ _security_cookie;
  v6 = this->mIsLoadedBsp == 0;
  v7 = a4;
  v8 = a3;
  v9 = this;
  v45 = 0;
  if ( v6 )
  {
    result = 0i64;
  }
  else
  {
    v11 = *a2;
    v12 = *a3;
    if ( *a2 != *a3 || (v13 = a2[1], v13 != a3[1]) || a2[2] != a3[2] )
    {
      v14 = a2[2];
      v15 = FLOAT_9999_0;
      v47 = a2[1];
      v16 = a3[1];
      v48 = v14;
      v17 = *((int *)a3 + 2);
      v46 = v11;
      v42 = v12;
      v43 = v16;
      v44 = v17;
      while ( 1 )
      {
        v18 = FLOAT_N100000_0;
        v19 = GetDist(&v46, &v42);
        if ( v19 <= 25.0 )
        {
          v28 = v44;
          v29 = v43;
          v27 = v42;
        }
        else
        {
          v20 = v46;
          v21 = v47;
          v22 = v48;
          v23 = v42 - v46;
          v24 = v43 - v47;
          v25 = *(float *)&v44 - v48;
          v26 = sqrtf_0((float)((float)(v24 * v24) + (float)(v23 * v23)) + (float)(v25 * v25));
          v27 = (float)((float)(v23 / v26) * 25.0) + v20;
          v15 = FLOAT_9999_0;
          *(float *)&v28 = (float)((float)(v25 / v26) * 25.0) + v22;
          v19 = FLOAT_25_0;
          v42 = v27;
          v44 = v28;
          v29 = (float)((float)(v24 / v26) * 25.0) + v21;
        }
        v30 = v19 * 1.73;
        if ( v30 < 23.0 )
          v30 = FLOAT_23_0;
        v31 = v9->mBsp;
        v49 = v27;
        v51 = v28;
        v43 = v29 + (float)(0.0 - v30);
        v50 = v30 + v29;
        CBsp::GetLeafList(v31, &v49, &v42, &v45, (__int16 *)&v58, 0x7D00u);
        v32 = 0;
        v33 = 0;
        v52 = v42;
        v54 = v44;
        v55 = v42;
        v57 = v44;
        v43 = v43 + v30;
        v34 = v43;
        v53 = v43 + v15;
        v56 = v43 - v15;
        if ( v45 <= 0 )
          break;
        v35 = &v58;
        while ( 1 )
        {
          v36 = v9->mBsp;
          v41 = *(_WORD *)v35;
          v37 = CBsp::GetYposInLeaf(v36, &v52, &v55, v30, v34, v41);
          if ( v37 != -32000.0 && v37 > v18 )
          {
            v33 = 1;
            v18 = v37;
          }
          ++v32;
          v35 = (float *)((char *)v35 + 2);
          if ( v32 >= v45 )
            break;
          v34 = v43;
        }
        if ( !v33 )
          break;
        v38 = *v8;
        if ( v42 == *v8 && *(float *)&v44 == v8[2] )
        {
          *v7 = v18;
          return 1i64;
        }
        v48 = *(float *)&v44;
        v39 = *((int *)v8 + 2);
        v46 = v42;
        v44 = v39;
        v42 = v38;
        v47 = v18;
        v43 = v18;
      }
      result = 0i64;
    }
    else
    {
      *a4 = v13;
      result = 1i64;
    }
  }
  return result;
}
