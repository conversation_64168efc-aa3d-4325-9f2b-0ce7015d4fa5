/*
 * Function: j_??$_Uninit_copy@PEAPEAURECV_DATA@@PEAPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAPEAPEAURECV_DATA@@PEAPEAU1@00AEAV?$allocator@PEAURECV_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140011B62
 */

RECV_DATA **__fastcall std::_Uninit_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, RECV_DATA **_Last, RECV_DATA **_Dest, std::allocator<RECV_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
