#pragma once

#include <cstdint>
#include <string>
#include <vector>
#include <chrono>
#include <mutex>
#include <fstream>

namespace NexusProtection::Economy {

    /**
     * @brief Guild money transaction record
     */
    struct GuildMoneyTransaction {
        std::string operatorName;           // Name of the operator
        uint32_t operatorSerial{0};         // Operator serial number
        int32_t dalantAmount{0};            // Dalant amount (positive for input, negative for output)
        int32_t goldAmount{0};              // Gold amount (positive for input, negative for output)
        double totalDalantAfter{0.0};       // Total dalant after transaction
        double totalGoldAfter{0.0};         // Total gold after transaction
        std::chrono::system_clock::time_point timestamp; // Transaction timestamp
        bool isInput{true};                 // True for input, false for output
        std::string fileName;               // Associated file name
        
        GuildMoneyTransaction() : timestamp(std::chrono::system_clock::now()) {}
        
        void SetData(const std::string& opName, uint32_t opSerial, int32_t dalant, int32_t gold,
                    double totalDalant, double totalGold, bool input, const std::string& file) {
            operatorName = opName;
            operatorSerial = opSerial;
            dalantAmount = dalant;
            goldAmount = gold;
            totalDalantAfter = totalDalant;
            totalGoldAfter = totalGold;
            isInput = input;
            fileName = file;
            timestamp = std::chrono::system_clock::now();
        }
    };

    /**
     * @brief Guild History Manager - Manages guild money transaction history
     * 
     * This class manages guild money transaction history including:
     * - Recording money input/output operations
     * - Writing transaction logs to files
     * - Managing transaction history
     * Refactored from decompiled C source to modern C++17/20.
     */
    class GuildHistoryManager {
    public:
        // Constructor and destructor
        GuildHistoryManager();
        ~GuildHistoryManager();

        // Core functionality
        void Initialize();
        void Shutdown();

        // Money transaction recording
        void PushMoney(const std::string& operatorName, uint32_t operatorSerial,
                      int32_t pushDalant, int32_t pushGold,
                      double totalDalant, double totalGold,
                      const std::string& fileName);

        void PopMoney(const std::string& operatorName, uint32_t operatorSerial,
                     int32_t popDalant, int32_t popGold,
                     double totalDalant, double totalGold,
                     const std::string& fileName);

        // File operations
        void WriteFile(const std::string& fileName, const std::string& data);
        void SetCurrentTime(const std::string& timeStr);
        const std::string& GetCurrentTime() const { return m_currentTime; }

        // History management
        void AddTransaction(const GuildMoneyTransaction& transaction);
        std::vector<GuildMoneyTransaction> GetTransactionHistory(size_t maxCount = 100) const;
        void ClearHistory();

        // Statistics
        size_t GetTransactionCount() const;
        double GetTotalDalantFlow() const;
        double GetTotalGoldFlow() const;

        // Legacy C interface compatibility
        void PushMoney(char* operatorName, uint32_t operatorSerial,
                      int32_t pushDalant, int32_t pushGold,
                      long double totalDalant, long double totalGold,
                      char* fileName);

        void PopMoney(char* operatorName, uint32_t operatorSerial,
                     int32_t popDalant, int32_t popGold,
                     long double totalDalant, long double totalGold,
                     char* fileName);

    private:
        // Internal methods
        std::string FormatTransactionLog(const GuildMoneyTransaction& transaction) const;
        std::string FormatTimestamp(const std::chrono::system_clock::time_point& time) const;
        void EnsureLogDirectory(const std::string& fileName);

        // Member variables
        std::vector<GuildMoneyTransaction> m_transactionHistory;
        std::string m_currentTime;
        std::string m_logDirectory{"./Logs/Guild/"};
        
        mutable std::mutex m_historyMutex;
        mutable std::mutex m_fileMutex;

        // Statistics
        size_t m_transactionCount{0};
        double m_totalDalantFlow{0.0};
        double m_totalGoldFlow{0.0};

        // Configuration
        size_t m_maxHistorySize{1000};
        bool m_enableFileLogging{true};
    };

    /**
     * @brief Guild History Manager Factory
     */
    class GuildHistoryManagerFactory {
    public:
        static std::unique_ptr<GuildHistoryManager> CreateManager();
        static std::unique_ptr<GuildHistoryManager> CreateManager(const std::string& logDirectory);
    };

    // Legacy C interface
    extern "C" {
        struct CMgrGuildHistory {
            char m_szCurTime[64];
            void* m_pLogFile;
            bool m_bInitialized;
        };

        // Legacy function declarations
        void CMgrGuildHistory_push_money(CMgrGuildHistory* mgr, char* operatorName, 
                                        unsigned int operatorSerial, int pushDalant, int pushGold,
                                        long double totalDalant, long double totalGold, 
                                        char* fileName);

        void CMgrGuildHistory_pop_money(CMgrGuildHistory* mgr, char* operatorName,
                                       unsigned int operatorSerial, int popDalant, int popGold,
                                       long double totalDalant, long double totalGold,
                                       char* fileName);

        void CMgrGuildHistory_WriteFile(CMgrGuildHistory* mgr, char* fileName, char* data);
        void CMgrGuildHistory_SetCurrentTime(CMgrGuildHistory* mgr, char* timeStr);

        // Global legacy data
        extern char sData_2[10000]; // Global buffer for formatting log messages
    }

    // Global instance access
    GuildHistoryManager& GetGuildHistoryManager();

} // namespace NexusProtection::Economy

// Global legacy compatibility
extern NexusProtection::Economy::GuildHistoryManager* g_pGuildHistoryManager;
