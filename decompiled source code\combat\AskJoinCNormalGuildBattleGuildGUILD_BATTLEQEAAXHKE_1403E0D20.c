/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXHKEPEADAEAVCNormalGuildBattleLogger@2@@Z
 * Address: 0x1403E0D20
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Ask<PERSON>oin(GUILD_BATTLE::CNormalGuildBattleGuild *this, int n, unsigned int dwSerial, char GuildBattleNumber, char *wszDestGuild, GUILD_BATTLE::CNormalGuildBattleLogger *kLogger)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@6
  char *v10; // [sp+28h] [bp-10h]@6
  GUILD_BATTLE::CNormalGuildBattleGuild *v11; // [sp+40h] [bp+8h]@1
  int na; // [sp+48h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+50h] [bp+18h]@1
  char v14; // [sp+58h] [bp+20h]@1

  v14 = GuildBattleNumber;
  dwSeriala = dwSerial;
  na = n;
  v11 = this;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !GUILD_BATTLE::CNormalGuildBattleGuild::IsJoinMember(v11, dwSerial)
    && (unsigned __int8)v14 > v11->m_dwCurJoinMember )
  {
    GUILD_BATTLE::CNormalGuildBattleGuild::AskJoin(v11, na, wszDestGuild, kLogger);
    v10 = wszDestGuild;
    v9 = (unsigned __int8)v14;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      kLogger,
      "CNormalGuildBattleGuild::AskJoin( n(%d), dwSerial(%u), GuildBattleNumber(%u), %s ) : Ask Join",
      (unsigned int)na,
      dwSeriala);
  }
}
