/*
 * Function: ?Update_RaceRank_Step8@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x1404B70A0
 */

char __fastcall CRFWorldDatabase::Update_RaceRank_Step8(CRFWorldDatabase *this, char *szDate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-468h]@1
  char *v6; // [sp+20h] [bp-448h]@4
  char *v7; // [sp+28h] [bp-440h]@4
  char DstBuf; // [sp+40h] [bp-428h]@4
  char v9; // [sp+41h] [bp-427h]@4
  unsigned __int64 v10; // [sp+450h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+470h] [bp+8h]@1
  char *v12; // [sp+478h] [bp+10h]@1

  v12 = szDate;
  v11 = this;
  v2 = &v5;
  for ( i = 280i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v11->vfptr,
    "CRFWorldDatabase::Update_RaceRank_Step8(szDate(%s)) : Start Set GuildName tbl_PvpRank%s Table",
    szDate,
    szDate);
  DstBuf = 0;
  memset(&v9, 0, 0x3FFui64);
  v7 = v12;
  v6 = v12;
  sprintf_s(
    &DstBuf,
    0x400ui64,
    "update tbl_PvpRank%s set GuildName = g.id from(select serial, id from tbl_Guild) as g where tbl_PvpRank%s.GuildSeria"
    "l = g.serial and tbl_PvpRank%s.GuildSerial > 0",
    v12);
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v11->vfptr, &DstBuf, 0) )
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step8(szDate(%s)) : End Set GuildName tbl_PvpRank%s Table",
      v12,
      v12);
    result = 1;
  }
  else
  {
    CRFNewDatabase::FmtLog(
      (CRFNewDatabase *)&v11->vfptr,
      "CRFWorldDatabase::Update_RaceRank_Step8(szDate(%s)) : %s Fail!",
      v12,
      &DstBuf);
    result = 0;
  }
  return result;
}
