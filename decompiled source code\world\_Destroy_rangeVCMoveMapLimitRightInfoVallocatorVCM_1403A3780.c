/*
 * Function: ??$_Destroy_range@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@YAXPEAVCMoveMapLimitRightInfo@@0AEAV?$allocator@VCMoveMapLimitRightInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x1403A3780
 */

void __fastcall std::_Destroy_range<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(CMoveMapLimitRightInfo *_First, CMoveMapLimitRightInfo *_Last, std::allocator<CMoveMapLimitRightInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CMoveMapLimitRightInfo *_Ptr; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRightInfo *v8; // [sp+38h] [bp+10h]@1
  std::allocator<CMoveMapLimitRightInfo> *v9; // [sp+40h] [bp+18h]@1

  v9 = _Al;
  v8 = _Last;
  _Ptr = _First;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  while ( _Ptr != v8 )
  {
    std::allocator<CMoveMapLimitRightInfo>::destroy(v9, _Ptr);
    ++_Ptr;
  }
}
