/*
 * Function: ?UpdatePacket@ClassOrderProcessor@@QEAAXEE@Z
 * Address: 0x1402B9180
 */

void __fastcall ClassOrderProcessor::UpdatePacket(ClassOrderProcessor *this, char byRace, char byClassType)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CandidateMgr *v5; // rax@7
  __int64 v6; // [sp+0h] [bp-48h]@1
  int v7; // [sp+20h] [bp-28h]@4
  _candidate_info *v8; // [sp+28h] [bp-20h]@7
  _candidate_info::ClassType eType; // [sp+30h] [bp-18h]@7
  ClassOrderProcessor *v10; // [sp+50h] [bp+8h]@1
  char v11; // [sp+58h] [bp+10h]@1
  char v12; // [sp+60h] [bp+18h]@1

  v12 = byClassType;
  v11 = byRace;
  v10 = this;
  v3 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned __int8)byClassType - 5;
  if ( v7 >= 0 && v7 < 4 )
  {
    eType = (unsigned __int8)byClassType;
    v5 = CandidateMgr::Instance();
    v8 = CandidateMgr::GetPatriarchGroup(v5, v11, eType);
    if ( v8 )
    {
      if ( v8->eStatus == 4 )
        v10->_kSend[(unsigned __int8)v11].body[v7].byLevel = 0;
      else
        v10->_kSend[(unsigned __int8)v11].body[v7].byLevel = v8->byLevel;
      v10->_kSend[(unsigned __int8)v11].body[v7].byClassType = LOBYTE(v8->eClassType) - 5;
      v10->_kSend[(unsigned __int8)v11].body[v7].dPvpPoint = v8->dPvpPoint;
      strcpy_0(v10->_kSend[(unsigned __int8)v11].body[v7].wszAvatorName, v8->wszName);
    }
    else
    {
      v10->_kSend[(unsigned __int8)v11].body[v7].byClassType = v12 - 5;
      v10->_kSend[(unsigned __int8)v11].body[v7].byLevel = -1;
    }
  }
}
