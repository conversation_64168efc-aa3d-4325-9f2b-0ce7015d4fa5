/*
 * Function: ?IsPartyMember@CPartyPlayer@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140045030
 */

char __fastcall CPartyPlayer::IsPartyMember(CPartyPlayer *this, CPlayer *pkObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  CPartyPlayer **v6; // [sp+20h] [bp-18h]@4
  int j; // [sp+28h] [bp-10h]@6
  CPartyPlayer *v8; // [sp+40h] [bp+8h]@1
  CPlayer *v9; // [sp+48h] [bp+10h]@1

  v9 = pkObj;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = CPartyPlayer::GetPtrPartyMember(v8);
  if ( v6 )
  {
    for ( j = 0; j < 8 && v6[j]; ++j )
    {
      if ( v6[j]->m_id.dwSerial == v9->m_dwObjSerial )
        return 1;
    }
    result = 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
