/*
 * Function: ?_Tidy@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAXXZ
 * Address: 0x140361520
 */

void __fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Tidy(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->_Myfirst )
  {
    std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Destroy(
      v4,
      v4->_Myfirst,
      v4->_Mylast);
    std::allocator<CUnmannedTraderRegistItemInfo>::deallocate(
      &v4->_Alval,
      v4->_Myfirst,
      (unsigned int)((char *)v4->_Myend - (char *)v4->_Myfirst) / 104i64);
  }
  v4->_Myfirst = 0i64;
  v4->_Mylast = 0i64;
  v4->_Myend = 0i64;
}
