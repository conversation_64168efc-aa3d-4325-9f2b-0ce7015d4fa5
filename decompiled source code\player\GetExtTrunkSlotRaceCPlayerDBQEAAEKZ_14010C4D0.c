/*
 * Function: ?GetExtTrunkSlotRace@CPlayerDB@@QEAAEK@Z
 * Address: 0x14010C4D0
 */

char __fastcall CPlayerDB::GetExtTrunkSlotRace(CPlayerDB *this, unsigned int dwItemSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  CPlayerDB *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = _STORAGE_LIST::GetIndexFromSerial((_STORAGE_LIST *)&v7->m_dbExtTrunk.m_nListNum, dwItemSerial);
  if ( v6 == 255 )
    result = -1;
  else
    result = v7->m_dbExtTrunk.m_byItemSlotRace[v6];
  return result;
}
