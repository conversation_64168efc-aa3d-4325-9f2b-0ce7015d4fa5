/*
 * Function: ?Release@CashDbWorker@@UEAAXXZ
 * Address: 0x1402EEB60
 */

void __fastcall CashDbWorker::Release(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  CRFCashItemDatabase *v5; // [sp+20h] [bp-28h]@5
  CRFCashItemDatabase *v6; // [sp+28h] [bp-20h]@5
  __int64 v7; // [sp+30h] [bp-18h]@6
  CashDbWorker *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->_pkDb )
  {
    v6 = v8->_pkDb;
    v5 = v6;
    if ( v6 )
    {
      LODWORD(v3) = ((int (__fastcall *)(CRFCashItemDatabase *, signed __int64))v5->vfptr->__vecDelDtor)(v5, 1i64);
      v7 = v3;
    }
    else
    {
      v7 = 0i64;
    }
    v8->_pkDb = 0i64;
  }
}
