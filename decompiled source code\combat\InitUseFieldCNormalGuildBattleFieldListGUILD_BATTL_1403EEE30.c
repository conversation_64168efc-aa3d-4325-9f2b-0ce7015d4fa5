/*
 * Function: ?InitUseField@CNormalGuildBattleFieldList@GUILD_BATTLE@@AEAA_NEPEAD0QEAPEAD@Z
 * Address: 0x1403EEE30
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::InitUseField(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char by<PERSON><PERSON>, char *szKey<PERSON>ame, char *szStr<PERSON>uff, char **szParseBuff)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@10
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@12
  __int64 v11; // [sp+0h] [bp-68h]@1
  DWORD nSize[2]; // [sp+20h] [bp-48h]@7
  LPCSTR lpFileName; // [sp+28h] [bp-40h]@7
  int v14; // [sp+30h] [bp-38h]@8
  int v15; // [sp+34h] [bp-34h]@13
  int j; // [sp+38h] [bp-30h]@13
  GUILD_BATTLE::CNormalGuildBattleField **v17; // [sp+40h] [bp-28h]@11
  unsigned int v18; // [sp+48h] [bp-20h]@7
  unsigned int v19; // [sp+4Ch] [bp-1Ch]@10
  __int64 v20; // [sp+50h] [bp-18h]@11
  DWORD v21; // [sp+58h] [bp-10h]@12
  unsigned int v22; // [sp+5Ch] [bp-Ch]@12
  GUILD_BATTLE::CNormalGuildBattleFieldList *v23; // [sp+70h] [bp+8h]@1
  char v24; // [sp+78h] [bp+10h]@1
  char *lpKeyName; // [sp+80h] [bp+18h]@1
  LPSTR lpReturnedString; // [sp+88h] [bp+20h]@1

  lpReturnedString = szStrBuff;
  lpKeyName = szKeyName;
  v24 = byRace;
  v23 = this;
  v5 = &v11;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    *szStrBuff = 0;
    GetPrivateProfileStringA("UseMap", szKeyName, "X", szStrBuff, 0x7FFu, "./Initialize/NormalGuildBattle.ini");
    if ( !strcmp_0("X", lpReturnedString) )
    {
      v18 = (unsigned __int8)v24;
      v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      lpFileName = "./Initialize/NormalGuildBattle.ini";
      *(_QWORD *)nSize = lpKeyName;
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v8,
        "CNormalGuildBattleFieldList::InitUseField( %u, %s, szUseMapInxStr, szBuff ) : GetPrivateProfileInt( UseMap, %s, "
        "X, szStr, 2047, %s ) = X",
        v18,
        lpKeyName);
      result = 0;
    }
    else
    {
      v14 = ParsingCommandA(lpReturnedString, 100, szParseBuff, 255);
      if ( v14 && v14 <= 100 )
      {
        v20 = v14;
        v17 = (GUILD_BATTLE::CNormalGuildBattleField **)operator new[](saturated_mul(8ui64, v14));
        v23->m_ppkUseFieldByRace[(unsigned __int8)v24] = v17;
        if ( v23->m_pkField )
        {
          v23->m_byUseFieldCnt[(unsigned __int8)v24] = v14;
          v15 = 0;
          for ( j = 0; j < v14; ++j )
          {
            v15 = atoi(szParseBuff[j]);
            v23->m_ppkUseFieldByRace[(unsigned __int8)v24][j] = &v23->m_pkField[v15];
          }
          result = 1;
        }
        else
        {
          v21 = (unsigned __int8)v24;
          v22 = (unsigned __int8)v24;
          v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          LODWORD(lpFileName) = v23->m_dwCnt;
          nSize[0] = v21;
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v10,
            "CNormalGuildBattleFieldList::InitUseField( %u, %s, szUseMapInxStr, szBuff ) : m_ppkUseFieldByRace[%u] == new"
            " CNormalGuildBattleField * [%u]",
            v22,
            lpKeyName);
          result = 0;
        }
      }
      else
      {
        v19 = (unsigned __int8)v24;
        v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        nSize[0] = v14;
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v9,
          "CNormalGuildBattleFieldList::InitUseField( %u, %s, szUseMapInxStr, szBuff ) : ::ParsingCommandA( szStrBuff, 10"
          "0, szParseBuff, 255 ) == %u Invalid!",
          v19,
          lpKeyName);
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
