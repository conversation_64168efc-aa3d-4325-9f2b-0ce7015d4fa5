/*
 * Function: ?CompleteSearch@CUnmannedTraderUserInfoTable@@QEAAXEEPEAD@Z
 * Address: 0x1403642F0
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteSearch(CUnmannedTraderUserInfoTable *this, char byDBRet, char byProcRet, char *pLoadData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char *v7; // [sp+20h] [bp-28h]@4
  CUnmannedTraderUserInfo *v8; // [sp+28h] [bp-20h]@4
  CPlayer *v9; // [sp+30h] [bp-18h]@7
  CUnmannedTraderUserInfoTable *v10; // [sp+50h] [bp+8h]@1
  char v11; // [sp+58h] [bp+10h]@1
  char v12; // [sp+60h] [bp+18h]@1
  char *pLoadDataa; // [sp+68h] [bp+20h]@1

  pLoadDataa = pLoadData;
  v12 = byProcRet;
  v11 = byDBRet;
  v10 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = pLoadData;
  v8 = CUnmannedTraderUserInfoTable::FindUser(v10, *(_WORD *)pLoadData, *((_DWORD *)pLoadData + 1));
  if ( !CUnmannedTraderUserInfo::IsNull(v8) )
  {
    if ( *(&g_Player.m_bOper + 50856 * CUnmannedTraderUserInfo::GetIndex(v8)) )
    {
      CUnmannedTraderUserInfo::ClearRequest(v8);
      v9 = CUnmannedTraderUserInfo::FindOwner(v8);
      if ( v9 )
      {
        if ( v11 || v12 )
          CUnmannedTraderUserInfo::SendSearchErrorResult(v8, v9->m_ObjID.m_wIndex, v12);
        else
          CUnmannedTraderUserInfo::SendSearchResult(v8, v9->m_ObjID.m_wIndex, pLoadDataa);
      }
    }
  }
}
