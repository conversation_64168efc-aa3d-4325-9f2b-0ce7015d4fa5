/*
 * Function: ?pc_Revival@CPlayer@@QEAAX_N@Z
 * Address: 0x1400C4E90
 */

void __fastcall CPlayer::pc_Revival(CPlayer *this, bool bUseableJade)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@19
  __int64 v5; // r8@19
  int v6; // eax@19
  int v7; // eax@19
  CNuclearBombMgr *v8; // rax@20
  __int64 v9; // [sp+0h] [bp-98h]@1
  char v10; // [sp+30h] [bp-68h]@5
  CMapData *pIntoMap; // [sp+38h] [bp-60h]@5
  CMapData *v12; // [sp+40h] [bp-58h]@5
  char Dst; // [sp+58h] [bp-40h]@13
  _dh_player_mgr *v14; // [sp+78h] [bp-20h]@12
  char v15; // [sp+80h] [bp-18h]@17
  CGameObjectVtbl *v16; // [sp+88h] [bp-10h]@19
  CPlayer *v17; // [sp+A0h] [bp+8h]@1

  v17 = this;
  v2 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v17->m_pUserDB )
  {
    v10 = 0;
    pIntoMap = 0i64;
    v12 = v17->m_pCurMap;
    if ( CGameObject::GetCurSecNum((CGameObject *)&v17->vfptr) == -1 || v17->m_bMapLoading )
    {
      v10 = 5;
    }
    else if ( v17->m_bCorpse )
    {
      if ( v17->m_pCurMap->m_pMapSet->m_nMapType == 1 )
      {
        if ( v17->m_pDHChannel )
        {
          v14 = CDarkHoleChannel::GetPlayerInfo(v17->m_pDHChannel, v17->m_dwObjSerial);
          if ( v14 )
          {
            pIntoMap = v14->LastPos.pMap;
            memcpy_0(&Dst, v14->LastPos.fPos, 0xCui64);
            CDarkHoleChannel::ClearMember(v17->m_pDHChannel, v17, 0, 0i64);
          }
        }
      }
      if ( !pIntoMap )
      {
        pIntoMap = CPlayer::GetBindMap(v17, (float *)&Dst, 0);
        if ( !pIntoMap )
          v10 = 2;
      }
    }
    else
    {
      v10 = 1;
    }
    v15 = 0;
    if ( !v10 )
    {
      if ( pIntoMap )
      {
        v17->m_bCorpse = 0;
        v17->m_byModeType = 0;
        v17->m_byMoveType = 1;
        v4 = ((int (__fastcall *)(CPlayer *))v17->vfptr->GetMaxHP)(v17);
        v16 = v17->vfptr;
        LOBYTE(v5) = 1;
        ((void (__fastcall *)(CPlayer *, _QWORD, __int64))v16->SetHP)(v17, (unsigned int)v4, v5);
        v6 = CPlayer::GetMaxFP(v17);
        CPlayer::SetFP(v17, v6, 1);
        v7 = CPlayer::GetMaxSP(v17);
        CPlayer::SetSP(v17, v7, 1);
        CPlayer::OutOfMap(v17, pIntoMap, 0, 2, (float *)&Dst);
      }
      else
      {
        CPlayer::pc_NuclearAfterEffect(v17);
        v8 = CNuclearBombMgr::Instance();
        CNuclearBombMgr::CheckNuclearState(v8, v17);
      }
    }
    CPlayer::SendMsg_Revival(v17, v10, 1);
  }
}
