/*
 * Function: ?GetUseCashQueryStr@CCashDbWorkerTH@@UEAAXAEAU_param_cash_update@@HPEAD_K@Z
 * Address: 0x1403225A0
 */

void __fastcall CCashDbWorkerTH::GetUseCashQueryStr(CCashDbWorkerTH *this, _param_cash_update *rParam, int nIdx, char *wszQuery, unsigned __int64 tBufferSize)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // er8@4
  int v8; // er9@4
  __int64 v9; // [sp+0h] [bp-78h]@1
  char *v10; // [sp+20h] [bp-58h]@4
  int v11; // [sp+28h] [bp-50h]@4
  int v12; // [sp+30h] [bp-48h]@4
  int v13; // [sp+38h] [bp-40h]@4
  unsigned __int64 v14; // [sp+40h] [bp-38h]@4
  char *v15; // [sp+48h] [bp-30h]@4
  char *v16; // [sp+50h] [bp-28h]@4
  int v17; // [sp+90h] [bp+18h]@1
  char *DstBuf; // [sp+98h] [bp+20h]@1

  DstBuf = wszQuery;
  v17 = nIdx;
  v5 = &v9;
  for ( i = 26i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v7 = rParam->in_item[(signed __int64)nIdx].in_nDiscount;
  v8 = rParam->in_item[(signed __int64)v17].in_byOverlapNum;
  v16 = rParam->in_szAvatorName;
  v15 = rParam->in_szSvrName;
  v14 = rParam->in_item[(signed __int64)v17].in_lnUID;
  v13 = v7;
  v12 = v8;
  v11 = rParam->in_item[(signed __int64)v17].in_nPrice;
  v10 = rParam->in_item[(signed __int64)v17].in_strItemCode;
  sprintf_s(
    DstBuf,
    tBufferSize,
    "declare @out_status char(1) declare @out_amount_s int  exec prc_rfonline_use @s_userid = '%s', @s_gcode = 'RFO',@s_p"
    "scode='%s', @s_price=%d,@s_quantity=%d,@s_discount=%d,@s_scode=%I64d,@s_server='%s',@s_character='%s', @s_status = @"
    "out_status output, @s_amount = @out_amount_s output select @out_status, @out_amount_s",
    rParam->in_szAcc);
}
