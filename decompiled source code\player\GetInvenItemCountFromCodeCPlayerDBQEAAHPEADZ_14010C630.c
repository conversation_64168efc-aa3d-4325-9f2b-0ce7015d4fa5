/*
 * Function: ?GetInvenItemCountFromCode@CPlayerDB@@QEAAHPEAD@Z
 * Address: 0x14010C630
 */

__int64 __fastcall CPlayerDB::GetInvenItemCountFromCode(CPlayerDB *this, char *pszItemCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int v6; // [sp+20h] [bp-28h]@4
  _base_fld *v7; // [sp+28h] [bp-20h]@4
  int j; // [sp+30h] [bp-18h]@4
  CPlayerDB *v9; // [sp+50h] [bp+8h]@1
  const char *Str2; // [sp+58h] [bp+10h]@1

  Str2 = pszItemCode;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  v7 = 0i64;
  for ( j = 0; j < v9->m_dbInven.m_nUsedNum; ++j )
  {
    if ( v9->m_dbInven.m_pStorageList[j].m_bLoad )
    {
      v7 = CRecordData::GetRecord(
             (CRecordData *)&unk_1799C6AA0 + v9->m_dbInven.m_pStorageList[j].m_byTableCode,
             v9->m_dbInven.m_pStorageList[j].m_wItemIndex);
      if ( v7 )
      {
        if ( !strcmp_0(v7->m_strCode, Str2) )
          v6 += LODWORD(v9->m_dbInven.m_pStorageList[j].m_dwDur);
      }
    }
  }
  return v6;
}
