/*
 * Function: j_??$_Uninit_copy@PEAPEAUINI_Section@@PEAPEAU1@V?$allocator@PEAUINI_Section@@@std@@@std@@YAPEAPEAUINI_Section@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Section@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400027CF
 */

INI_Section **__fastcall std::_Uninit_copy<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(INI_Section **_First, INI_Section **_Last, INI_Section **_Dest, std::allocator<INI_Section *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
