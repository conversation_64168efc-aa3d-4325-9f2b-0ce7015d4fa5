/*
 * Function: ??$_Umove@PEAPEAVCUnmannedTraderClassInfo@@@?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV2@00@Z
 * Address: 0x140373910
 */

CUnmannedTraderClassInfo **__fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Umove<CUnmannedTraderClassInfo * *>(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Ptr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return stdext::_Unchecked_uninitialized_move<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>>(
           _First,
           _Last,
           _Ptr,
           &v8->_Alval);
}
