/*
 * Function: j_?back@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAAAEAPEAVTRC_AutoTrade@@XZ
 * Address: 0x140007A45
 */

TRC_AutoTrade **__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::back(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::back(this);
}
