/*
 * Function: j_?Find@CUnmannedTraderUserInfo@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@K@Z
 * Address: 0x14000D373
 */

std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall CUnmannedTraderUserInfo::Find(CUnmannedTraderUserInfo *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result, unsigned int dwRegistSerial)
{
  return CUnmannedTraderUserInfo::Find(this, result, dwRegistSerial);
}
