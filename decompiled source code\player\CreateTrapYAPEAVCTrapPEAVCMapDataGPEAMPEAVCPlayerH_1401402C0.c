/*
 * Function: ?CreateTrap@@YAPEAVCTrap@@PEAVCMapData@@GPEAMPEAVCPlayer@@H@Z
 * Address: 0x1401402C0
 */

CTrap *__fastcall CreateTrap(CMapData *pMap, unsigned __int16 wLayer, float *fPos, CPlayer *pMaster, int nTrapItemIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CTrap *result; // rax@10
  __int64 v8; // [sp+0h] [bp-88h]@1
  CTrap *v9; // [sp+20h] [bp-68h]@4
  int j; // [sp+28h] [bp-60h]@4
  _trap_create_setdata Dst; // [sp+38h] [bp-50h]@11
  CMapData *v12; // [sp+90h] [bp+8h]@1
  unsigned __int16 v13; // [sp+98h] [bp+10h]@1
  float *Src; // [sp+A0h] [bp+18h]@1
  CPlayer *v15; // [sp+A8h] [bp+20h]@1

  v15 = pMaster;
  Src = fPos;
  v13 = wLayer;
  v12 = pMap;
  v5 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v9 = 0i64;
  for ( j = 0; j < 507; ++j )
  {
    if ( !g_Trap[j].m_bLive )
    {
      v9 = &g_Trap[j];
      break;
    }
  }
  if ( v9 )
  {
    _trap_create_setdata::_trap_create_setdata(&Dst);
    Dst.m_pMap = v12;
    Dst.m_nLayerIndex = v13;
    Dst.m_pRecordSet = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 26, nTrapItemIndex);
    if ( Dst.m_pRecordSet )
    {
      memcpy_0(Dst.m_fStartPos, Src, 0xCui64);
      Dst.pMaster = v15;
      Dst.nTrapMaxAttackPnt = v15->m_nTrapMaxAttackPnt;
      if ( CTrap::Create(v9, &Dst) )
        result = v9;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
