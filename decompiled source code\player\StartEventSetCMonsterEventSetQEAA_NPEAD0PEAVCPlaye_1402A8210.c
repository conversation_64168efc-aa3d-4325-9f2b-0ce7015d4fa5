/*
 * Function: ?StartEventSet@CMonsterEventSet@@QEAA_NPEAD0PEAVCPlayer@@@Z
 * Address: 0x1402A8210
 */

char __fastcall CMonsterEventSet::StartEventSet(CMonsterEventSet *this, char *pszEventCode, char *pwszErrCode, CPlayer *pOne)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v7; // rax@32
  __int64 v8; // [sp+0h] [bp-2E8h]@1
  float *pNewPos; // [sp+20h] [bp-2C8h]@32
  bool bRobExp; // [sp+28h] [bp-2C0h]@32
  bool bRewardExp; // [sp+30h] [bp-2B8h]@32
  bool bDungeon; // [sp+38h] [bp-2B0h]@32
  bool bWithoutFail; // [sp+40h] [bp-2A8h]@32
  bool bApplyRopExpField; // [sp+48h] [bp-2A0h]@32
  char Dest; // [sp+60h] [bp-288h]@4
  int v16; // [sp+264h] [bp-84h]@10
  int j; // [sp+268h] [bp-80h]@10
  _event_set *pEventSet; // [sp+270h] [bp-78h]@13
  int k; // [sp+278h] [bp-70h]@17
  int v20; // [sp+27Ch] [bp-6Ch]@20
  _event_set::_monster_set *v21; // [sp+280h] [bp-68h]@20
  int nRange; // [sp+288h] [bp-60h]@26
  int l; // [sp+28Ch] [bp-5Ch]@26
  float Dst; // [sp+298h] [bp-50h]@30
  int v25; // [sp+29Ch] [bp-4Ch]@30
  int v26; // [sp+2A0h] [bp-48h]@30
  CMapData *v27; // [sp+2B8h] [bp-30h]@30
  CMonster *v28; // [sp+2C0h] [bp-28h]@32
  int v29; // [sp+2D0h] [bp-18h]@24
  int v30; // [sp+2D4h] [bp-14h]@29
  unsigned __int64 v31; // [sp+2D8h] [bp-10h]@4
  CMonsterEventSet *v32; // [sp+2F0h] [bp+8h]@1
  const char *Str2; // [sp+2F8h] [bp+10h]@1
  char *fmt; // [sp+300h] [bp+18h]@1
  CPlayer *v35; // [sp+308h] [bp+20h]@1

  v35 = pOne;
  fmt = pwszErrCode;
  Str2 = pszEventCode;
  v32 = this;
  v4 = &v8;
  for ( i = 184i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v31 = (unsigned __int64)&v8 ^ _security_cookie;
  strcpy_0(&Dest, "without reload ini file");
  if ( !v32->m_bLoadEventLooting )
  {
    sprintf(fmt, "Event Set Looting File Not Loaded : %s", ".\\Initialize\\EventSetLooting.ini");
    CLogFile::Write(&stru_1799C95A8, fmt);
    return 0;
  }
  if ( CMonsterEventSet::IsINIFileChanged(v32, ".\\Initialize\\EventSet.ini", v32->m_ftWrite) )
  {
    memset_0(v32->m_EventSet, 0, 0x3C870ui64);
    if ( !CMonsterEventSet::LoadEventSet(v32, &Dest) )
    {
      sprintf(fmt, "Reload INI file fail!!! : %s", &Dest);
      CLogFile::Write(&stru_1799C95A8, "Reload INI file fail!!! : %s >> %s", ".\\Initialize\\EventSet.ini", &Dest);
      return 0;
    }
    sprintf(&Dest, "with reload ini file : %s", ".\\Initialize\\EventSet.ini");
    CLogFile::Write(&stru_1799C95A8, "Reload INI file for Monster Event Set >> %s", ".\\Initialize\\EventSet.ini");
  }
  v16 = 0;
  for ( j = 0; j < 10; ++j )
  {
    if ( !strcmp_0(v32->m_EventSet[j].m_strId, Str2) )
    {
      pEventSet = &v32->m_EventSet[j];
      if ( v32->m_EventSet[j].m_bOper )
      {
        if ( fmt )
          sprintf(fmt, "now actived");
        return 0;
      }
      ++v16;
      for ( k = 0; k < 10; ++k )
      {
        v20 = 0;
        v21 = &pEventSet->m_MonSet[k];
        if ( pEventSet->m_MonSet[k].bIsSet )
        {
          _event_set::_monster_set::_state::init(&v21->m_State);
          if ( v21->bUnknownMap )
          {
            v21->pMap = v35->m_pCurMap;
            memcpy_0(v21->fPos, v35->m_fCurPos, 0xCui64);
          }
          if ( 20 * v21->wNum >= 500 )
            v29 = 500;
          else
            v29 = 20 * v21->wNum;
          nRange = v29;
          for ( l = 0; l < v21->wNum; ++l )
          {
            v30 = v21->byRegenProb;
            if ( v30 >= rand() % 100 )
            {
              Dst = 0.0;
              v25 = 0;
              v26 = 0;
              v27 = v21->pMap;
              if ( !CMapData::GetRandPosVirtualDumExcludeStdRange(v27, v21->fPos, nRange, 0, &Dst) )
                memcpy_0(&Dst, v21->fPos, 0xCui64);
              v7 = v21->pMonsterFld->m_strCode;
              bApplyRopExpField = 0;
              bWithoutFail = 0;
              bDungeon = 0;
              bRewardExp = 1;
              bRobExp = 0;
              pNewPos = 0i64;
              v28 = CreateRepMonster(v21->pMap, 0, &Dst, v7, 0i64, 0, 1, 0, 0, 0);
              if ( v28 )
              {
                v21->m_State.MonInfo[v20].pMon = v28;
                v21->m_State.MonInfo[v20].dwSerial = v28->m_dwObjSerial;
                v21->m_State.MonInfo[v20++].pMonFld = v21->pMonsterFld;
                CMonster::DisableStdItemLoot(v28);
                CMonster::LinkEventSet(v28, pEventSet);
              }
            }
          }
          v21->m_State.nRespawnNum = v20;
          v21->m_State.dwLastUpdateTime = timeGetTime();
          v21->m_State.dwStartTime = timeGetTime();
          v21->m_State.bOper = 1;
        }
      }
      pEventSet->m_bOper = 1;
    }
  }
  if ( v16 )
  {
    strcpy_0(fmt, &Dest);
    CLogFile::Write(&stru_1799C95A8, "Start Event Set (by cheat) >> %s", Str2);
    result = 1;
  }
  else
  {
    if ( fmt )
      sprintf(fmt, "can't find event set id");
    result = 0;
  }
  return result;
}
