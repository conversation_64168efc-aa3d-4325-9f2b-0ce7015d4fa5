#pragma once

#include "AuthenticationTypes.h"
#include <memory>
#include <mutex>
#include <unordered_map>
#include <vector>
#include <atomic>

namespace NexusProtection::Authentication {

    /**
     * @brief Security verification result
     */
    struct SecurityVerificationResult {
        bool isValid{false};
        SecurityLevel detectedLevel{SecurityLevel::None};
        std::string errorMessage;
        uint32_t errorCode{0};
        std::chrono::steady_clock::time_point verificationTime;
        
        SecurityVerificationResult() : verificationTime(std::chrono::steady_clock::now()) {}
    };

    /**
     * @brief Client security information
     */
    struct ClientSecurityInfo {
        uint32_t sessionId{0};
        std::string clientVersion;
        std::string clientHash;
        SecurityLevel securityLevel{SecurityLevel::Basic};
        bool integrityVerified{false};
        bool antiCheatActive{false};
        std::chrono::steady_clock::time_point lastVerification;
        uint32_t verificationCount{0};

        // HackShield specific fields
        int32_t socketIndex{-1};
        uint8_t verifyState{0};
        std::array<uint8_t, 256> guidClientInfo{};
        std::vector<uint8_t> crcInfo;

        void Reset() noexcept {
            sessionId = 0;
            clientVersion.clear();
            clientHash.clear();
            securityLevel = SecurityLevel::Basic;
            integrityVerified = false;
            antiCheatActive = false;
            lastVerification = std::chrono::steady_clock::now();
            verificationCount = 0;
            socketIndex = -1;
            verifyState = 0;
            guidClientInfo.fill(0);
            crcInfo.clear();
        }
    };

    /**
     * @brief HackShield message types
     */
    enum class HackShieldMessageType : uint8_t {
        ClientCheckSumResponse = 98,
        ServerCheckSumRequest = 99,
        ClientCrcResponse = 100,
        SessionVerification = 101
    };

    /**
     * @brief HackShield protocol constants
     */
    struct HackShieldConstants {
        static constexpr uint64_t CLIENT_CHECKSUM_MESSAGE_SIZE = 342;
        static constexpr uint64_t SERVER_CHECKSUM_MESSAGE_SIZE = 256;
        static constexpr uint64_t CLIENT_CRC_MESSAGE_SIZE = 128;
        static constexpr int32_t MAX_SOCKET_INDEX = 2532;
        static constexpr uint8_t VERIFY_STATE_INITIAL = 0;
        static constexpr uint8_t VERIFY_STATE_CHECKSUM_SENT = 1;
        static constexpr uint8_t VERIFY_STATE_VERIFIED = 2;
    };

    /**
     * @brief HackShield Extended System - Anti-cheat and security management
     * 
     * Manages client security verification, anti-cheat systems, and session integrity.
     * Refactored from decompiled C source to modern C++20.
     */
    class CHackShieldExSystem {
    public:
        // Constructor and destructor
        CHackShieldExSystem();
        ~CHackShieldExSystem();

        // Core lifecycle
        bool Initialize();
        void Shutdown();
        bool LoadConfiguration();

        // Session verification
        SecurityVerificationResult OnCheckSession_FirstVerify(uint32_t sessionId,
                                                             const std::string& clientData);
        bool VerifySessionIntegrity(uint32_t sessionId);
        bool UpdateSessionSecurity(uint32_t sessionId, const SecurityParams& params);

        // Network message handling (from original decompiled source)
        bool OnRecvSession_ClientCheckSum_Response(uint32_t sessionId, uint64_t messageSize, const char* messageData);
        bool OnRecvSession_ServerCheckSum_Request(uint32_t sessionId, uint64_t messageSize, const char* messageData);
        bool OnRecvSession_ClientCrc_Response(uint32_t sessionId, uint64_t messageSize, const char* messageData);
        bool OnRecvSession(uint32_t sessionId, const std::vector<uint8_t>& sessionData);

        // Client verification
        bool VerifyClientIntegrity(uint32_t sessionId, const std::string& clientHash);
        bool CheckClientVersion(uint32_t sessionId, const std::string& clientVersion);
        SecurityLevel DetermineSecurityLevel(const ClientSecurityInfo& clientInfo);

        // Anti-cheat operations
        bool EnableAntiCheat(uint32_t sessionId);
        bool DisableAntiCheat(uint32_t sessionId);
        bool IsAntiCheatActive(uint32_t sessionId) const;
        void ProcessAntiCheatData(uint32_t sessionId, const std::vector<uint8_t>& data);

        // Security monitoring
        void MonitorSession(uint32_t sessionId);
        void ReportSecurityViolation(uint32_t sessionId, const std::string& violation);
        std::vector<std::string> GetSecurityViolations(uint32_t sessionId) const;

        // Client management
        bool RegisterClient(uint32_t sessionId, const ClientSecurityInfo& clientInfo);
        bool UnregisterClient(uint32_t sessionId);
        ClientSecurityInfo GetClientInfo(uint32_t sessionId) const;
        void UpdateClientActivity(uint32_t sessionId);

        // Configuration and state
        bool IsInitialized() const noexcept { return m_isInitialized; }
        bool IsOperational() const noexcept { return m_isOperational; }
        const SecurityParams& GetSecurityParams() const noexcept { return m_securityParams; }
        void SetSecurityParams(const SecurityParams& params);

        // Statistics
        struct SecurityStatistics {
            uint32_t totalVerifications{0};
            uint32_t successfulVerifications{0};
            uint32_t failedVerifications{0};
            uint32_t securityViolations{0};
            uint32_t activeClients{0};
            std::chrono::steady_clock::time_point startTime;
        };
        
        const SecurityStatistics& GetStatistics() const noexcept { return m_statistics; }
        void ResetStatistics();

        // Legacy C interface compatibility
        bool OnCheckSession_FirstVerify(uint32_t sessionId, char* clientData);
        void SetSecurityLevel(uint32_t sessionId, uint8_t level);
        uint8_t GetSecurityLevel(uint32_t sessionId) const;

    private:
        // Internal methods
        bool LoadSecurityConfiguration();
        bool InitializeAntiCheatSystem();
        void CleanupExpiredClients();
        bool ValidateClientData(const std::string& clientData) const;
        void LogSecurityEvent(const std::string& event, uint32_t sessionId);
        std::string GenerateClientHash(const ClientSecurityInfo& clientInfo) const;

        // Security verification helpers
        bool PerformIntegrityCheck(const ClientSecurityInfo& clientInfo);
        bool VerifyAntiCheatSignature(const std::vector<uint8_t>& data);
        SecurityLevel CalculateSecurityLevel(const ClientSecurityInfo& clientInfo);

        // HackShield protocol helpers
        uint32_t AnalyzeClientChecksum(const char* checksumData, const uint8_t* guidClientInfo, const std::vector<uint8_t>& crcInfo);
        bool SendVerificationResponse(uint32_t sessionId);
        bool ProcessServerChecksumRequest(uint32_t sessionId, const char* messageData, uint64_t messageSize);
        bool ProcessClientCrcResponse(uint32_t sessionId, const char* messageData, uint64_t messageSize);
        void KickClient(uint32_t sessionId, uint32_t reason, uint32_t errorCode);

        // Utility methods
        uint32_t CalculateCRC32(const void* data, size_t length);
        bool SendNetworkMessage(uint32_t sessionId, const std::vector<uint8_t>& message);

        // Member variables
        std::unordered_map<uint32_t, ClientSecurityInfo> m_clients;
        std::unordered_map<uint32_t, std::vector<std::string>> m_securityViolations;
        
        SecurityParams m_securityParams;
        SecurityStatistics m_statistics;
        
        bool m_isInitialized{false};
        bool m_isOperational{false};
        
        mutable std::mutex m_clientsMutex;
        mutable std::mutex m_violationsMutex;
        mutable std::mutex m_statisticsMutex;

        // Configuration
        std::string m_configPath;
        std::chrono::minutes m_clientCleanupInterval{10};
        std::chrono::steady_clock::time_point m_lastCleanup;

        // Anti-cheat system
        std::atomic<bool> m_antiCheatEnabled{true};
        std::vector<std::string> m_trustedClientHashes;
        std::vector<std::string> m_bannedClientHashes;

        // Monitoring
        std::chrono::seconds m_monitoringInterval{30};
        std::chrono::steady_clock::time_point m_lastMonitoring;
    };

    /**
     * @brief Security event handler interface
     */
    class ISecurityEventHandler {
    public:
        virtual ~ISecurityEventHandler() = default;
        virtual void OnSecurityViolation(uint32_t sessionId, const std::string& violation) = 0;
        virtual void OnClientVerificationFailed(uint32_t sessionId, const std::string& reason) = 0;
        virtual void OnAntiCheatDetection(uint32_t sessionId, const std::string& detection) = 0;
    };

    /**
     * @brief Default security event handler
     */
    class DefaultSecurityEventHandler : public ISecurityEventHandler {
    public:
        void OnSecurityViolation(uint32_t sessionId, const std::string& violation) override;
        void OnClientVerificationFailed(uint32_t sessionId, const std::string& reason) override;
        void OnAntiCheatDetection(uint32_t sessionId, const std::string& detection) override;
    };

    // Legacy C interface
    extern "C" {
        struct CHackShieldExSystem_Legacy {
            bool m_bInitialized;
            bool m_bOperational;
            uint32_t m_dwActiveClients;
            uint32_t m_dwTotalVerifications;
            uint32_t m_dwFailedVerifications;
        };

        struct CLIENT_SECURITY_DATA {
            uint32_t dwSessionId;
            char szClientVersion[32];
            char szClientHash[64];
            uint8_t bySecurityLevel;
            bool bIntegrityVerified;
            bool bAntiCheatActive;
            uint32_t dwLastVerification;
            uint32_t dwVerificationCount;
        };
    }

    // Global instance access
    CHackShieldExSystem& GetHackShieldExSystem();

} // namespace NexusProtection::Authentication

// Legacy C interface (outside namespace)
extern "C" {
    struct CHackShieldExSystem_Legacy {
        bool m_bInitialized;
        bool m_bOperational;
        uint32_t m_dwActiveClients;
        uint32_t m_dwTotalVerifications;
        uint32_t m_dwFailedVerifications;
    };

    struct CHackShieldExSystem_ClientInfo {
        uint32_t dwSessionId;
        uint8_t bySecurityLevel;
        bool bIntegrityVerified;
        bool bAntiCheatActive;
        uint32_t dwLastVerification;
        uint32_t dwVerificationCount;
    };

    // Legacy function declarations
    CHackShieldExSystem_Legacy* CHackShieldExSystem_Create();
    void CHackShieldExSystem_Destroy(CHackShieldExSystem_Legacy* system);
    bool CHackShieldExSystem_Initialize(CHackShieldExSystem_Legacy* system);
    bool CHackShieldExSystem_OnCheckSession_FirstVerify(CHackShieldExSystem_Legacy* system,
                                                       uint32_t sessionId, char* clientData);
    void CHackShieldExSystem_SetSecurityLevel(CHackShieldExSystem_Legacy* system,
                                             uint32_t sessionId, uint8_t level);
    uint8_t CHackShieldExSystem_GetSecurityLevel(CHackShieldExSystem_Legacy* system,
                                                uint32_t sessionId);
    bool CHackShieldExSystem_VerifyClientIntegrity(CHackShieldExSystem_Legacy* system,
                                                  uint32_t sessionId, const char* clientHash);
    bool CHackShieldExSystem_EnableAntiCheat(CHackShieldExSystem_Legacy* system,
                                            uint32_t sessionId);
    bool CHackShieldExSystem_DisableAntiCheat(CHackShieldExSystem_Legacy* system,
                                             uint32_t sessionId);
}

// Global legacy compatibility
extern NexusProtection::Authentication::CHackShieldExSystem* g_pHackShieldExSystem;
