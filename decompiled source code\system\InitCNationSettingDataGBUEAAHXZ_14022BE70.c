/*
 * Function: ?Init@CNationSettingDataGB@@UEAAHXZ
 * Address: 0x14022BE70
 */

signed __int64 __fastcall CNationSettingDataGB::Init(CNationSettingDataGB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CEngNetworkBillEX *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-28h]@1
  CNationSettingDataGB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CNationSettingData::GetFireGuardEnableSetting((CNationSettingData *)&v6->vfptr) )
  {
    MyMessageBox("CNationSettingDataGB::Init()", "FireGuard Setting Enabled Invalid!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataGB::Init() : FireGuard Setting Enabled Invalid!");
    result = 0xFFFFFFFFi64;
  }
  else if ( CNationSettingData::GetTimeLimitEnableSetting((CNationSettingData *)&v6->vfptr) )
  {
    MyMessageBox("CNationSettingDataGB::Init()", "Time Limit System Setting Eanbled Invalid!");
    CLogFile::Write(&stru_1799C8F30, "CNationSettingDataGB::Init() : Time Limit System Setting Eanbled Invalid!");
    result = 4294967294i64;
  }
  else
  {
    v4 = CTSingleton<CEngNetworkBillEX>::Instance();
    if ( CEngNetworkBillEX::Initialize(v4) )
    {
      if ( (unsigned __int8)((int (__fastcall *)(CNationSettingDataGB *))v6->vfptr->ReadSystemPass)(v6) )
      {
        result = 0i64;
      }
      else
      {
        MyMessageBox("CNationSettingDataGB::Init()", "All Event Error!");
        CLogFile::Write(&stru_1799C8F30, "CNationSettingDataGB::Init() : All Event Error!");
        result = 0xFFFFFFFFi64;
      }
    }
    else
    {
      MyMessageBox("CNationSettingDataGB::Init", "CEngNetworkBillEX::Instance()->Initialize() Fail!");
      result = 4294967293i64;
    }
  }
  return result;
}
