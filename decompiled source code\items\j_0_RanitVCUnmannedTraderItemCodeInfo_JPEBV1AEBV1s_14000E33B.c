/*
 * Function: j_??0?$_Ranit@VCUnmannedTraderItemCodeInfo@@_JPEBV1@AEBV1@@std@@QEAA@AEBU01@@Z
 * Address: 0x14000E33B
 */

void __fastcall std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::_<PERSON>t<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>(std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &> *this, std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &> *__that)
{
  std::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>::_Ranit<CUnmannedTraderItemCodeInfo,__int64,CUnmannedTraderItemCodeInfo const *,CUnmannedTraderItemCodeInfo const &>(
    this,
    __that);
}
