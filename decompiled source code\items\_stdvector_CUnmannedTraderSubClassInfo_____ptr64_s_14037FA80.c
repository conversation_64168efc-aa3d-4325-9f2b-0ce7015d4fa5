/*
 * Function: _std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x14037FA80
 */

void __fastcall __noreturn std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::_Destroy(
    *(std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 160),
    *(CUnmannedTraderSubClassInfo ***)(a2 + 64),
    *(CUnmannedTraderSubClassInfo ***)(a2 + 72));
  std::allocator<CUnmannedTraderSubClassInfo *>::deallocate(
    (std::allocator<CUnmannedTraderSubClassInfo *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(CUnmannedTraderSubClassInfo ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
