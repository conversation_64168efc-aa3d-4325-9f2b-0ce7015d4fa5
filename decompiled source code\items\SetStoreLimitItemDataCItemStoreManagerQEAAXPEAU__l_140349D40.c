/*
 * Function: ?SetStoreLimitItemData@CItemStoreManager@@QEAAXPEAU__list@_qry_case_all_store_limit_item@@@Z
 * Address: 0x140349D40
 */

void __fastcall CItemStoreManager::SetStoreLimitItemData(CItemStoreManager *this, _qry_case_all_store_limit_item::__list *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool v4; // zf@12
  CItemStore *v5; // rax@12
  __int64 v6; // [sp+0h] [bp-58h]@1
  CMapItemStoreList *v7; // [sp+20h] [bp-38h]@7
  CItemStore *v8; // [sp+28h] [bp-30h]@14
  char v9; // [sp+30h] [bp-28h]@18
  int nIndex; // [sp+34h] [bp-24h]@18
  int v11; // [sp+38h] [bp-20h]@18
  int v12; // [sp+3Ch] [bp-1Ch]@18
  _limit_item_info *v13; // [sp+40h] [bp-18h]@18
  CItemStoreManager *v14; // [sp+60h] [bp+8h]@1
  _qry_case_all_store_limit_item::__list *v15; // [sp+68h] [bp+10h]@1

  v15 = pData;
  v14 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pData && pData->nTypeSerial >= 0 )
  {
    v7 = 0i64;
    if ( pData->byType )
    {
      if ( pData->byType == 1 )
        v7 = CItemStoreManager::GetInstanceStoreListBySerial(v14, pData->nTypeSerial);
    }
    else
    {
      v7 = CItemStoreManager::GetMapItemStoreListBySerial(v14, pData->nTypeSerial);
    }
    if ( v7 )
    {
      v4 = v15->dwStoreIndex == 0;
      v5 = CMapItemStoreList::GetItemStoreFromRecIndex(v7, v15->dwStoreIndex);
      v8 = v5;
      if ( v5 )
      {
        v8->m_dwDBSerial = v15->dwDBSerial;
        if ( v15->dwLimitInitTime )
          v8->m_dwLimitInitTime = v15->dwLimitInitTime;
        else
          CItemStore::UpdateLimitItemNum(v8, 1);
        v9 = 0;
        v11 = -1;
        v12 = -1;
        v13 = 0i64;
        for ( nIndex = 0; nIndex < 16; ++nIndex )
        {
          v13 = CItemStore::GetLimitItem(v8, nIndex);
          if ( v13 )
          {
            v12 = _INVENKEY::CovDBKey(&v13->Key);
            v11 = _INVENKEY::CovDBKey((_INVENKEY *)&v15->ItemData[nIndex]);
            if ( v12 != v11 )
              v9 = 1;
            if ( v12 != -1 && v12 == v11 )
              v13->nLimitNum = v15->ItemData[nIndex].nLimitNum;
          }
        }
        if ( v9 )
          CItemStore::UpdateLimitItemNum(v8, 1);
        v8->m_bDBDataCheck = 1;
      }
    }
  }
}
