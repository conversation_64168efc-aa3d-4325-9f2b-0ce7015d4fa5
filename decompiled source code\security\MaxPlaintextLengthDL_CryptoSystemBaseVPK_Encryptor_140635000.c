/*
 * Function: ?MaxPlaintextLength@?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@VInteger@CryptoPP@@@2@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x140635000
 */

__int64 __fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::MaxPlaintextLength(__int64 a1, unsigned __int64 a2)
{
  __int64 v2; // rax@1
  __int64 v3; // rdx@1
  __int64 v4; // rax@3
  __int64 v5; // rax@3
  int v7; // [sp+20h] [bp-38h]@1
  __int64 v8; // [sp+30h] [bp-28h]@2
  __int64 v9; // [sp+60h] [bp+8h]@1
  unsigned __int64 v10; // [sp+68h] [bp+10h]@1

  v10 = a2;
  v9 = a1;
  LODWORD(v2) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>::GetAbstractGroupParameters(a1 + 16);
  LOBYTE(v3) = 1;
  v7 = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v2 + 96i64))(v2, v3);
  if ( v10 >= (unsigned int)v7 )
  {
    LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v9 + 80i64))(v9);
    LODWORD(v5) = (*(int (__fastcall **)(__int64, unsigned __int64))(*(_QWORD *)v4 + 24i64))(v4, v10 - (unsigned int)v7);
    v8 = v5;
  }
  else
  {
    v8 = 0i64;
  }
  return v8;
}
