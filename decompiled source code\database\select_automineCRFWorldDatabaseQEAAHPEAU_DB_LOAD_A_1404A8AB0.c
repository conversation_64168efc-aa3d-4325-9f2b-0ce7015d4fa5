/*
 * Function: ?select_automine@CRFWorldDatabase@@QEAAHPEAU_DB_LOAD_AUTOMINE_MACHINE@@@Z
 * Address: 0x1404A8AB0
 */

signed __int64 __fastcall CRFWorldDatabase::select_automine(CRFWorldDatabase *this, _DB_LOAD_AUTOMINE_MACHINE *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-1C8h]@1
  void *SQLStmt; // [sp+20h] [bp-1A8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-1A0h]@22
  SQLLEN v8; // [sp+38h] [bp-190h]@22
  __int16 v9; // [sp+44h] [bp-184h]@9
  char Dest; // [sp+60h] [bp-168h]@4
  char v11; // [sp+61h] [bp-167h]@4
  unsigned __int8 v12; // [sp+164h] [bp-64h]@16
  int v13; // [sp+168h] [bp-60h]@22
  int TargetValue; // [sp+174h] [bp-54h]@22
  unsigned __int8 v15; // [sp+194h] [bp-34h]@22
  int j; // [sp+1A4h] [bp-24h]@22
  unsigned __int8 v17; // [sp+1A8h] [bp-20h]@26
  unsigned __int64 v18; // [sp+1B8h] [bp-10h]@4
  CRFWorldDatabase *v19; // [sp+1D0h] [bp+8h]@1
  _DB_LOAD_AUTOMINE_MACHINE *v20; // [sp+1D8h] [bp+10h]@1

  v20 = pdata;
  v19 = this;
  v2 = &v5;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v18 = (unsigned __int64)&v5 ^ _security_cookie;
  Dest = 0;
  memset(&v11, 0, 0xFFui64);
  sprintf(&Dest, "{ CALL pselect_automine_inven(%d, %d) }", pdata->byCollisionType, pdata->byRace);
  if ( v19->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v19->vfptr, &Dest);
  if ( v19->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v19->vfptr) )
  {
    v9 = SQLExecDirect_0(v19->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v9, v19->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v19->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        v12 = 0;
        if ( v9 == 100 )
        {
          v12 = 2;
        }
        else
        {
          SQLStmt = v19->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v9, v19->m_hStmtSelect);
          v12 = 1;
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        result = v12;
      }
      else
      {
        v13 = 1;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v19->m_hStmtSelect, 1u, -6, &v20->byRace, 0i64, &v8);
        ++v13;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v19->m_hStmtSelect, v13++, -18, &v20->dwGuildSerial, 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v19->m_hStmtSelect, v13++, -6, &v20->bWorking, 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v19->m_hStmtSelect, v13++, -6, &v20->bySelectedOre, 0i64, &v8);
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v19->m_hStmtSelect, v13, -18, &v20->dwBatteryGage, 0i64, &v8);
        TargetValue = 0;
        v15 = 0;
        for ( j = 0; j < 80; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v8;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v19->m_hStmtSelect, v13++, 4, &TargetValue, 0i64, &v8);
          StrLen_or_IndPtr = &v8;
          SQLStmt = 0i64;
          v9 = SQLGetData_0(v19->m_hStmtSelect, v13, -6, &v15, 0i64, &v8);
          if ( v9 && v9 != 1 )
          {
            v17 = 0;
            if ( v9 == 100 )
            {
              v17 = 2;
            }
            else
            {
              SQLStmt = v19->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v9, &Dest, "SQLExecDirect", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v9, v19->m_hStmtSelect);
              v17 = 1;
            }
            if ( v19->m_hStmtSelect )
              SQLCloseCursor_0(v19->m_hStmtSelect);
            return v17;
          }
          if ( TargetValue != -1 && (signed int)v15 > 0 )
          {
            v20->slot[v20->bySlotCnt].nLumpIndex = j / 40;
            memcpy_0(&v20->slot[v20->bySlotCnt].item, &TargetValue, 4ui64);
            v20->slot[v20->bySlotCnt++].nOverlapNum = v15;
          }
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        if ( v19->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v19->vfptr, "%s Success", &Dest);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v19->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
