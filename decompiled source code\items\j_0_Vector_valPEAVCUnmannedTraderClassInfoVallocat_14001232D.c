/*
 * Function: j_??0?$_Vector_val@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@IEAA@V?$allocator@PEAVCUnmannedTraderClassInfo@@@1@@Z
 * Address: 0x14001232D
 */

void __fastcall std::_Vector_val<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_val<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(std::_Vector_val<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, std::allocator<CUnmannedTraderClassInfo *> _Al)
{
  std::_Vector_val<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::_Vector_val<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(
    this,
    _Al);
}
