/*
 * Function: ?Check_Event_Status@CGoldenBoxItemMgr@@QEAAXXZ
 * Address: 0x140412B80
 */

void __fastcall CGoldenBoxItemMgr::Check_Event_Status(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-58h]@1
  char v4; // [sp+20h] [bp-38h]@4
  __time32_t Time; // [sp+34h] [bp-24h]@4
  char v6; // [sp+44h] [bp-14h]@4
  CGoldenBoxItemMgr *v7; // [sp+60h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = CGoldenBoxItemMgr::Get_Event_Status(v7);
  _time32(&Time);
  v6 = v4;
  if ( v4 )
  {
    if ( v6 == 1 )
    {
      if ( Time >= v7->m_golden_box_event.m_ini.m_EventTime[0] )
      {
        CGoldenBoxItemMgr::Set_Event_Status(v7, 2);
        CGoldenBoxItemMgr::Set_DCK(v7, 1);
        CGoldenBoxItemMgr::Set_ToStruct(v7);
      }
    }
    else if ( v6 == 2 && v7->m_golden_box_event.m_ini.m_EventTime[1] <= Time )
    {
      CGoldenBoxItemMgr::Set_Event_Status(v7, 3);
      CGoldenBoxItemMgr::Set_DCK(v7, 0);
      CGoldenBoxItemMgr::Set_ToStruct(v7);
    }
  }
  else if ( !v7->m_golden_box_event.m_ini.m_bUse_event )
  {
    CGoldenBoxItemMgr::Set_Event_Status(v7, 0);
    CGoldenBoxItemMgr::Set_DCK(v7, 0);
    CGoldenBoxItemMgr::Set_ToStruct(v7);
  }
}
