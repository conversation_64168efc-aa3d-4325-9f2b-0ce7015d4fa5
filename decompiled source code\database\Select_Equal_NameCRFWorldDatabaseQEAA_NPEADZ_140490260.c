/*
 * Function: ?Select_Equal_Name@CRFWorldDatabase@@QEAA_NPEAD@Z
 * Address: 0x140490260
 */

char __fastcall CRFWorldDatabase::Select_Equal_Name(CRFWorldDatabase *this, char *pwszCharacterName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-178h]@1
  void *SQLStmt; // [sp+20h] [bp-158h]@13
  __int16 v7; // [sp+30h] [bp-148h]@9
  char Dest; // [sp+50h] [bp-128h]@4
  char v9; // [sp+154h] [bp-24h]@16
  unsigned __int64 v10; // [sp+160h] [bp-18h]@4
  CRFWorldDatabase *v11; // [sp+180h] [bp+8h]@1

  v11 = this;
  v2 = &v5;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pSelect_Equal_Name('%s') }", pwszCharacterName);
  if ( v11->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v11->vfptr, &Dest);
  if ( v11->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v11->vfptr) )
  {
    v7 = SQLExecDirectA_0(v11->m_hStmtSelect, &Dest, -3);
    if ( v7 && v7 != 1 )
    {
      if ( v7 == 100 )
      {
        result = 1;
      }
      else
      {
        SQLStmt = v11->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "_SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v11->vfptr, v7, v11->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v7 = SQLFetch_0(v11->m_hStmtSelect);
      if ( v7 && v7 != 1 )
      {
        v9 = 1;
        if ( v7 == 100 )
        {
          v9 = 1;
        }
        else
        {
          SQLStmt = v11->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v11->vfptr, v7, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v11->vfptr, v7, v11->m_hStmtSelect);
          v9 = 0;
        }
        if ( v11->m_hStmtSelect )
          SQLCloseCursor_0(v11->m_hStmtSelect);
        result = v9;
      }
      else
      {
        if ( v11->m_hStmtSelect )
          SQLCloseCursor_0(v11->m_hStmtSelect);
        if ( v11->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v11->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v11->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
