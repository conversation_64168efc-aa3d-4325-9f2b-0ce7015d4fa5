/*
 * Function: ?Init@CPlayer@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x140048660
 */

char __fastcall CPlayer::Init(CPlayer *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1
  _object_id *pIDa; // [sp+38h] [bp+10h]@1

  pIDa = pID;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v6->vfptr, pID);
  _effect_parameter::AllocEffParam(&v6->m_EP);
  CMyTimer::BeginTimer(&v6->m_tmrIntervalSec, 0x3E8u);
  CMyTimer::BeginTimer(&v6->m_tmrBilling, 0x3A980u);
  v6->m_pPartyMgr = (CPartyPlayer *)((char *)&g_PartyPlayer + 128 * (unsigned __int64)pIDa->m_wIndex);
  v6->m_bLoad = 0;
  v6->m_bOper = 0;
  v6->m_bPostLoad = 0;
  v6->m_bFullMode = 0;
  v6->m_byPosRaceTown = -1;
  v6->m_bCheat_100SuccMake = 0;
  v6->m_bCheat_makeitem_no_use_matrial = 0;
  v6->m_bCheat_Matchless = 0;
  v6->m_bFreeRecallWaitTime = 0;
  v6->m_pUserDB = 0i64;
  v6->m_pUsingUnit = 0i64;
  v6->m_pParkingUnit = 0i64;
  v6->m_dwUnitViewOverTime = -1;
  v6->m_pRecalledAnimusItem = 0i64;
  v6->m_pRecalledAnimusChar = 0i64;
  v6->m_dwLastRecallTime = 0;
  v6->m_byNextRecallReturn = -1;
  v6->m_id.dwSerial = -1;
  v6->m_id.wIndex = pIDa->m_wIndex;
  v6->m_dwObjSerial = -1;
  v6->m_byUserDgr = 0;
  v6->m_pBindMapData = 0i64;
  v6->m_pBindDummyData = 0i64;
  v6->m_dwNextTimeDungeonDie = 0;
  v6->m_bNeverDie = 0;
  v6->m_bRecvMapChat = 0;
  CPlayer::PastWhisperInit(v6);
  CSetItemEffect::Init_Info(&v6->m_clsSetItem);
  v6->m_nChaosMode = 0;
  v6->m_dwChaosModeTime10Per = 0;
  v6->m_dwChaosModeEndTime = 0;
  MiningTicket::Init(&v6->m_MinigTicket);
  v6->m_bSnowMan = 0;
  CMyTimer::BeginTimer(&v6->m_tmrGroupTargeting, 0x2710u);
  v6->m_bAfterEffect = 0;
  v6->m_bSFDelayNotCheck = 0;
  CMyTimer::BeginTimer(&v6->m_tmrEffectStartTime, 0x36EE80u);
  CMyTimer::BeginTimer(&v6->m_tmrEffectEndTime, 0xEA60u);
  CRealMoveRequestDelayChecker::Init(&v6->m_kMoveDelayChecker, 0xAu);
  _NameChangeBuddyInfo::Init(&v6->m_NameChangeBuddyInfo);
  v6->m_dwPcBangGiveItemListIndex = -1;
  CMyTimer::BeginTimer(&v6->m_tmrAccumPlayingTime, 0x493E0u);
  CMyTimer::BeginTimer(&v6->m_tmrPremiumPVPInform, 0x1B7740u);
  return 1;
}
