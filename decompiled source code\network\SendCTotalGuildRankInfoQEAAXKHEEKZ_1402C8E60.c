/*
 * Function: ?Send@CTotalGuildRankInfo@@QEAAXKHEEK@Z
 * Address: 0x1402C8E60
 */

void __fastcall CTotalGuildRankInfo::Send(CTotalGuildRankInfo *this, unsigned int dwVer, int n, char byTabRace, char by<PERSON><PERSON><PERSON><PERSON>, unsigned int dwGuildSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@15
  __int64 v9; // [sp+0h] [bp-88h]@1
  unsigned __int16 nLen[2]; // [sp+20h] [bp-68h]@9
  unsigned int v11; // [sp+28h] [bp-60h]@9
  unsigned int v12; // [sp+30h] [bp-58h]@11
  int v13; // [sp+38h] [bp-50h]@11
  unsigned int v14; // [sp+40h] [bp-48h]@11
  int v15; // [sp+50h] [bp-38h]@12
  char pbyType; // [sp+64h] [bp-24h]@15
  char v17; // [sp+65h] [bp-23h]@15
  CTotalGuildRankInfo *v18; // [sp+90h] [bp+8h]@1
  unsigned int v19; // [sp+98h] [bp+10h]@1
  int dwClientIndex; // [sp+A0h] [bp+18h]@1
  char v21; // [sp+A8h] [bp+20h]@1

  v21 = byTabRace;
  dwClientIndex = n;
  v19 = dwVer;
  v18 = this;
  v6 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( v18->m_bInit && (signed int)(unsigned __int8)byTabRace < 3 && (signed int)(unsigned __int8)bySelfRace < 3 )
  {
    if ( v18->m_bNoData )
    {
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CTotalGuildRankInfo::Send( %u, %d, %u, %u, %u ) : No Data!",
        dwVer,
        (unsigned int)n,
        (unsigned __int8)byTabRace);
    }
    else if ( dwVer == v18->m_pkSendList[(unsigned __int8)byTabRace].dwVer )
    {
      v14 = v18->m_pkSendList[(unsigned __int8)byTabRace].dwVer;
      v13 = (unsigned __int8)byTabRace;
      v12 = dwVer;
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CTotalGuildRankInfo::Send( %u, %d, %u, %u, %u ) : %u == m_pkSendList[%u].dwVer(%u)",
        dwVer,
        (unsigned int)n);
    }
    else
    {
      v15 = CTotalGuildRankInfo::Find(v18, bySelfRace, dwGuildSerial);
      v12 = v15;
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CTotalGuildRankInfo::Send( %u, %d, %u, %u, %u ) : Find( byRace, dwGuildSerial ) -> %d!",
        v19,
        (unsigned int)dwClientIndex,
        (unsigned __int8)v21);
      if ( v15 < 0 )
      {
        v18->m_pkSendList[(unsigned __int8)v21].list[10].byRank = 0;
        memset_0(v18->m_pkSendList[(unsigned __int8)v21].list[10].wszGuildName, 0, 0x11ui64);
        v18->m_pkSendList[(unsigned __int8)v21].list[10].byGrade = 0;
        memset_0(v18->m_pkSendList[(unsigned __int8)v21].list[10].wszMasterName, 0, 0x11ui64);
        v18->m_pkSendList[(unsigned __int8)v21].byExistSelfRankInfo = 0;
      }
      else
      {
        v18->m_pkSendList[(unsigned __int8)v21].list[10].byRank = v18->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v15]->m_wRank;
        strcpy_0(
          v18->m_pkSendList[(unsigned __int8)v21].list[10].wszGuildName,
          v18->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v15]->m_wszGuildName);
        v18->m_pkSendList[(unsigned __int8)v21].list[10].byGrade = v18->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v15]->m_byGrade;
        strcpy_0(
          v18->m_pkSendList[(unsigned __int8)v21].list[10].wszMasterName,
          v18->m_ppkRaceStartPos[(unsigned __int8)bySelfRace][v15]->m_wszMasterName);
        v18->m_pkSendList[(unsigned __int8)v21].byExistSelfRankInfo = 1;
      }
      pbyType = 13;
      v17 = 38;
      v8 = _total_guild_rank_result_zocl::size(&v18->m_pkSendList[(unsigned __int8)v21]);
      CNetProcess::LoadSendMsg(
        unk_1414F2088,
        dwClientIndex,
        &pbyType,
        (char *)&v18->m_pkSendList[(unsigned __int8)v21],
        v8);
      v11 = dwGuildSerial;
      *(_DWORD *)nLen = (unsigned __int8)bySelfRace;
      __trace(
        "CTotalGuildRankInfo::Send( %u, %d, %u, %u, %u ) : Send",
        v19,
        (unsigned int)dwClientIndex,
        (unsigned __int8)v21);
    }
  }
}
