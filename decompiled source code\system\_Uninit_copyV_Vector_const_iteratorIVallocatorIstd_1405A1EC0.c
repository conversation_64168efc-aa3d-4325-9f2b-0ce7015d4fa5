/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@IV?$allocator@I@std@@@std@@PEAIV?$allocator@I@2@@std@@YAPEAIV?$_Vector_const_iterator@IV?$allocator@I@std@@@0@0PEAIAEAV?$allocator@I@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A1EC0
 */

__int64 __fastcall std::_Uninit_copy<std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>,unsigned int *,std::allocator<unsigned int>>(std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > *a1, std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > *a2, __int64 a3, __int64 a4)
{
  __int64 v4; // ST40_8@3
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > *i; // [sp+60h] [bp+8h]@1
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > *_Right; // [sp+68h] [bp+10h]@1
  __int64 v8; // [sp+70h] [bp+18h]@1
  __int64 v9; // [sp+78h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  _Right = a2;
  for ( i = a1;
        std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::operator!=(i, _Right);
        std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::operator++((__int64)i) )
  {
    v4 = std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::operator*((__int64)i);
    std::allocator<unsigned int>::construct(v9, v8, v4);
    v8 += 4i64;
  }
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::~_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>(i);
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::~_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>(_Right);
  return v8;
}
