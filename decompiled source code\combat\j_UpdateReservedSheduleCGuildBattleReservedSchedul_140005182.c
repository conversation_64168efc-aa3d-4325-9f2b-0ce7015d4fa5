/*
 * Function: j_?UpdateReservedShedule@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAA_NKPEAE@Z
 * Address: 0x140005182
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateReservedShedule(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, unsigned int dwSLID, char *byOutData)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleListManager::UpdateReservedShedule(this, dwSLID, byOutData);
}
