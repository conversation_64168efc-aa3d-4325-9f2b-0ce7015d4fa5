/*
 * Function: ?inform_cashdiscount_event@CashItemRemoteStore@@QEAAXG@Z
 * Address: 0x1402F6F60
 */

void __fastcall CashItemRemoteStore::inform_cashdiscount_event(CashItemRemoteStore *this, unsigned __int16 widx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ICsSendInterface::SendMsg_CashDiscountEventInform(widx, v5->m_cde.m_cde_status, &v5->m_cde.m_ini);
}
