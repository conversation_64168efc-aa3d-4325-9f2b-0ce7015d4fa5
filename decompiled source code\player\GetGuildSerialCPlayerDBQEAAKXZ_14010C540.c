/*
 * Function: ?GetGuildSerial@CPlayerDB@@QEAAKXZ
 * Address: 0x14010C540
 */

__int64 __fastcall CPlayerDB::GetGuildSerial(CPlayerDB *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = (int *)&v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  if ( v5->m_pGuild )
    v4 = v5->m_pGuild->m_dwSerial;
  else
    v4 = -1;
  return v4;
}
