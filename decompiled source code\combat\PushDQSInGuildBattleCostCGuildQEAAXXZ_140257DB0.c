/*
 * Function: ?PushDQSInGuildBattleCost@CGuild@@QEAAXXZ
 * Address: 0x140257DB0
 */

void __fastcall CGuild::PushDQSInGuildBattleCost(CGuild *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-88h]@1
  _qry_case_in_guildbattlecost v5; // [sp+38h] [bp-50h]@4
  unsigned __int64 v6; // [sp+70h] [bp-18h]@4
  CGuild *v7; // [sp+90h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  v5.dwGuildIndex = v7->m_nIndex;
  v5.dwGuildSerial = v7->m_dwSerial;
  v5.dwAddDalant = 0;
  v5.dwAddGold = 5000;
  *(_QWORD *)&v5.out_totalgold = 0i64;
  *(_QWORD *)&v5.out_totaldalant = 0i64;
  v5.byDate[0] = GetCurrentMonth();
  v5.byDate[1] = GetCurrentDay();
  v5.byDate[2] = GetCurrentHour();
  v5.byDate[3] = GetCurrentMin();
  v3 = _qry_case_in_guildbattlecost::size(&v5);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 37, (char *)&v5, v3);
  v7->m_bIOWait = 1;
}
