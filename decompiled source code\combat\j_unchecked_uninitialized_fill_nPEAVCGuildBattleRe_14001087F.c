/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@stdext@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KAEBV12@AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@Z
 * Address: 0x14001087F
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<GUILD_BATTLE::CGuildBattleRewardItem *,unsigned __int64,GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(GUILD_BATTLE::CGuildBattleRewardItem *_First, unsigned __int64 _Count, GUILD_BATTLE::CGuildBattleRewardItem *_Val, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<GUILD_BATTLE::CGuildBattleRewardItem *,unsigned __int64,GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(
    _First,
    _Count,
    _Val,
    _Al);
}
