/*
 * Function: j_??E?$_Vector_const_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x14000990D
 */

std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *__fastcall std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator++(std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this)
{
  return std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator++(this);
}
