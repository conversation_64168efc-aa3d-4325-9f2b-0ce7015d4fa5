/*
 * Function: ?GetBattleByGuildSerial@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattle@2@K@Z
 * Address: 0x1403D5580
 */

GUILD_BATTLE::CNormalGuildBattle *__fastcall GUILD_BATTLE::CNormalGuildBattleManager::GetBattleByGuildSerial(GUILD_BATTLE::CNormalGuildBattleManager *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattle *result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CNormalGuildBattle **v6; // [sp+20h] [bp-18h]@6
  unsigned int j; // [sp+28h] [bp-10h]@6
  unsigned __int8 k; // [sp+2Ch] [bp-Ch]@8
  GUILD_BATTLE::CNormalGuildBattleManager *v9; // [sp+40h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+48h] [bp+10h]@1

  dwGuildSeriala = dwGuildSerial;
  v9 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_ppkNormalBattle )
  {
    v6 = 0i64;
    for ( j = 0; j < v9->m_uiMapCnt; ++j )
    {
      v6 = &v9->m_ppkTodayBattle[23 * j];
      for ( k = 0; (signed int)k < 23; ++k )
      {
        if ( !GUILD_BATTLE::CNormalGuildBattle::IsEmpty(v6[k])
          && GUILD_BATTLE::CNormalGuildBattle::IsMemberGuild(v6[k], dwGuildSeriala) )
        {
          return v6[k];
        }
      }
    }
    result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
