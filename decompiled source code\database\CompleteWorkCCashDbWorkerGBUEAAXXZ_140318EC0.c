/*
 * Function: ?CompleteWork@CCashDbWorkerGB@@UEAAXXZ
 * Address: 0x140318EC0
 */

void __fastcall CCashDbWorkerGB::CompleteWork(CCashDbWorkerGB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@4
  char *v4; // rax@7
  char *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-58h]@1
  unsigned int nIdx; // [sp+24h] [bp-34h]@5
  Task *pkTsk; // [sp+38h] [bp-20h]@5
  int v9; // [sp+40h] [bp-18h]@10
  CCashDbWorkerGB *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v1 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  if ( CNationSettingManager::IsCashDBInit(v3) )
  {
    nIdx = 0;
    pkTsk = TaskPool::PopCompleteTsk(v10->_pkPool, &nIdx);
    if ( pkTsk )
    {
      if ( Task::GetTaskCode(pkTsk) )
      {
        if ( Task::GetTaskCode(pkTsk) == 1 )
        {
          v5 = Task::GetTaskBuf(pkTsk);
          Task::SetRetCode(pkTsk, *((_WORD *)v5 + 54));
        }
      }
      else
      {
        v4 = Task::GetTaskBuf(pkTsk);
        Task::SetRetCode(pkTsk, *((_WORD *)v4 + 20));
      }
      v9 = Task::GetTaskCode(pkTsk);
      if ( v9 )
      {
        switch ( v9 )
        {
          case 1:
            CashDbWorker::_complete_tsk_cash_update((CashDbWorker *)&v10->vfptr, pkTsk);
            break;
          case 2:
            CashDbWorker::_complete_tsk_cash_rollback((CashDbWorker *)&v10->vfptr, pkTsk);
            break;
          case 3:
            CashDbWorker::_complete_tsk_cashitem_buy_dblog((CashDbWorker *)&v10->vfptr, pkTsk);
            break;
          case 4:
            CashDbWorker::_complete_tsk_cash_total_selling_select((CashDbWorker *)&v10->vfptr, pkTsk);
            break;
        }
      }
      else
      {
        CashDbWorker::_complete_tsk_cash_select((CashDbWorker *)&v10->vfptr, pkTsk);
      }
      TaskPool::PushEmptyTsk(v10->_pkPool, nIdx);
    }
  }
}
