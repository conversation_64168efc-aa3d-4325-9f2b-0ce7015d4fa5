/*
 * Function: ?front@?$deque@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@@std@@QEAAAEAUMessageRange@MeterFilter@CryptoPP@@XZ
 * Address: 0x1405FFF00
 */

__int64 __fastcall std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::front(__int64 a1)
{
  __int64 v1; // rax@1
  __int64 v2; // rax@1
  __int64 v3; // ST20_8@1
  char v5; // [sp+28h] [bp-40h]@1
  __int64 v6; // [sp+48h] [bp-20h]@1
  __int64 v7; // [sp+50h] [bp-18h]@1
  __int64 v8; // [sp+58h] [bp-10h]@1

  v6 = -2i64;
  v1 = std::deque<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>>::begin(
         a1,
         (__int64)&v5);
  v7 = v1;
  v8 = v1;
  LODWORD(v2) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(v1);
  v3 = v2;
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return v3;
}
