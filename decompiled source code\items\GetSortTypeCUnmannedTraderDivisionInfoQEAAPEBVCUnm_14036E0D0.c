/*
 * Function: ?GetSortType@CUnmannedTraderDivisionInfo@@QEAAPEBVCUnmannedTraderSortType@@E@Z
 * Address: 0x14036E0D0
 */

CUnmannedTraderSortType *__fastcall CUnmannedTraderDivisionInfo::GetSortType(CUnmannedTraderDivisionInfo *this, char bySortType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSortType *v4; // rax@6
  CUnmannedTraderSortType **v5; // rax@9
  __int64 v6; // [sp+0h] [bp-98h]@1
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > result; // [sp+28h] [bp-70h]@7
  bool v8; // [sp+44h] [bp-54h]@8
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > v9; // [sp+48h] [bp-50h]@8
  CUnmannedTraderSortType *v10; // [sp+60h] [bp-38h]@10
  CUnmannedTraderSortType *v11; // [sp+68h] [bp-30h]@12
  __int64 v12; // [sp+70h] [bp-28h]@4
  std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *v13; // [sp+78h] [bp-20h]@8
  std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *_Right; // [sp+80h] [bp-18h]@8
  CUnmannedTraderDivisionInfo *v15; // [sp+A0h] [bp+8h]@1
  char v16; // [sp+A8h] [bp+10h]@1

  v16 = bySortType;
  v15 = this;
  v2 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  if ( (unsigned __int8)bySortType == 255
    || std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::empty(&v15->m_vecSortType) )
  {
    v4 = 0i64;
  }
  else
  {
    std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::begin(
      &v15->m_vecSortType,
      &result);
    while ( 1 )
    {
      v13 = std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::end(
              &v15->m_vecSortType,
              &v9);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)v13;
      v8 = std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator!=(
             (std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)&result._Mycont,
             (std::_Vector_const_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *)&v13->_Mycont);
      std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(&v9);
      if ( !v8 )
        break;
      v5 = std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(&result);
      if ( CUnmannedTraderSortType::GetID(*v5) == (unsigned __int8)v16 )
      {
        v10 = *std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator*(&result);
        std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(&result);
        return v10;
      }
      std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::operator++(&result);
    }
    v11 = 0i64;
    std::_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::~_Vector_iterator<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(&result);
    v4 = v11;
  }
  return v4;
}
