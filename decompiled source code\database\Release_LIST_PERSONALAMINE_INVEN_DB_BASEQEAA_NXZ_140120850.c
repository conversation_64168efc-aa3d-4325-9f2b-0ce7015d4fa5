/*
 * Function: ?Release@_LIST@_PERSONALAMINE_INVEN_DB_BASE@@QEAA_NXZ
 * Address: 0x140120850
 */

char __fastcall _PERSONALAMINE_INVEN_DB_BASE::_LIST::Release(_PERSONALAMINE_INVEN_DB_BASE::_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  _PERSONALAMINE_INVEN_DB_BASE::_LIST *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( _INVENKEY::IsFilled((_INVENKEY *)v5) )
  {
    _INVENKEY::SetRelease((_INVENKEY *)v5);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
