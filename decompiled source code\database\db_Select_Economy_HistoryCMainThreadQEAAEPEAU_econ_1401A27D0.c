/*
 * Function: ?db_Select_Economy_History@CMainThread@@QEAAEPEAU_economy_history_data@@PEAH101K@Z
 * Address: 0x1401A27D0
 */

char __fastcall CMainThread::db_Select_Economy_History(CMainThread *this, _economy_history_data *pCurData, int *pnCurMgrValue, int *pnNextMgrValue, _economy_history_data *pHisData, int *pHistoryNum, unsigned int dwDate)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@13
  __int64 v10; // [sp+0h] [bp-AB8h]@1
  _worlddb_economy_history_info_array::_worlddb_economy_history_info pEconomyData; // [sp+30h] [bp-A88h]@4
  char v12; // [sp+104h] [bp-9B4h]@4
  int j; // [sp+108h] [bp-9B0h]@5
  int k; // [sp+10Ch] [bp-9ACh]@7
  unsigned __int16 Dst; // [sp+120h] [bp-998h]@12
  char v16[2412]; // [sp+128h] [bp-990h]@19
  char v17; // [sp+A94h] [bp-24h]@12
  int v18; // [sp+A98h] [bp-20h]@14
  int l; // [sp+A9Ch] [bp-1Ch]@14
  int m; // [sp+AA0h] [bp-18h]@17
  int n; // [sp+AA4h] [bp-14h]@19
  CMainThread *v22; // [sp+AC0h] [bp+8h]@1
  _economy_history_data *v23; // [sp+AC8h] [bp+10h]@1
  int *v24; // [sp+AD0h] [bp+18h]@1
  int *v25; // [sp+AD8h] [bp+20h]@1

  v25 = pnNextMgrValue;
  v24 = pnCurMgrValue;
  v23 = pCurData;
  v22 = this;
  v7 = &v10;
  for ( i = 684i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v12 = CRFWorldDatabase::Select_Exist_Economy(v22->m_pWorldDB, dwDate, &pEconomyData);
  if ( !v12 )
  {
    for ( j = 0; j < 3; ++j )
    {
      v23->dTradeDalant[j] = pEconomyData.dTradeDalant[j];
      v23->dTradeGold[j] = pEconomyData.dTradeGold[j];
      for ( k = 0; k < 3; ++k )
      {
        v23->dOreMineCount[j][k] = pEconomyData.dMineOre[j][k];
        v23->dOreCutCount[j][k] = pEconomyData.dCutOre[j][k];
      }
    }
    *v24 = pEconomyData.dwManageValue;
    *v25 = pEconomyData.dwManageValue;
  }
  memset_0(&Dst, 0, 0x968ui64);
  v17 = CRFWorldDatabase::Select_Economy_History(v22->m_pWorldDB, (_worlddb_economy_history_info_array *)&Dst, dwDate);
  if ( v17 == 1 )
  {
    result = 24;
  }
  else
  {
    v18 = 12;
    for ( l = 0; l < Dst; ++l )
    {
      if ( --v18 < 0 )
        break;
      for ( m = 0; m < 3; ++m )
      {
        pHisData[v18].dTradeDalant[m] = *((long double *)&v16[200 * l + 24] + m);
        pHisData[v18].dTradeGold[m] = *((long double *)&v16[200 * l] + m);
        for ( n = 0; n < 3; ++n )
        {
          pHisData[v18].dOreMineCount[m][n] = *((long double *)&v16[200 * l + 56] + 3 * m + n);
          pHisData[v18].dOreCutCount[m][n] = *((long double *)&v16[200 * l + 128] + 3 * m + n);
        }
      }
    }
    *pHistoryNum = Dst;
    result = 0;
  }
  return result;
}
