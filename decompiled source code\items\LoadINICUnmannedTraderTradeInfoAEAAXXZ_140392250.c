/*
 * Function: ?<PERSON>adINI@CUnmannedTraderTradeInfo@@AEAAXXZ
 * Address: 0x140392250
 */

void __usercall CUnmannedTraderTradeInfo::LoadINI(CUnmannedTraderTradeInfo *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp-20h] [bp-1468h]@1
  char ReturnedString; // [sp+20h] [bp-1428h]@4
  char v7; // [sp+21h] [bp-1427h]@4
  unsigned __int64 v8; // [sp+1430h] [bp-18h]@4
  CUnmannedTraderTradeInfo *v9; // [sp+1450h] [bp+8h]@1

  v9 = this;
  v2 = alloca(a2);
  v3 = &v5;
  for ( i = 1304i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  ReturnedString = 0;
  memset(&v7, 0, 0x13FFui64);
  GetPrivateProfileStringA(
    CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_SECTION_NAME,
    "OldIncome",
    "0",
    &ReturnedString,
    0x1400u,
    CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_FILE_NAME);
  v9->m_ui64TotalOldIncome = _strtoui64(&ReturnedString, 0i64, 10);
  GetPrivateProfileStringA(
    CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_SECTION_NAME,
    "CurrentIncome",
    "0",
    &ReturnedString,
    0x1400u,
    CUnmannedTraderEnvironmentValue::UNMANNEDTRADETRADEINFO_SAVE_INI_FILE_NAME);
  v9->m_ui64TotalCurrentIncome = _strtoui64(&ReturnedString, 0i64, 10);
}
