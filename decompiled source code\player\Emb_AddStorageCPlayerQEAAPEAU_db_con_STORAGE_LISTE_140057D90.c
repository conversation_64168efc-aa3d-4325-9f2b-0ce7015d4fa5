/*
 * Function: ?Emb_AddStorage@CPlayer@@QEAAPEAU_db_con@_STORAGE_LIST@@EPEAU_storage_con@3@_N1@Z
 * Address: 0x140057D90
 */

_STORAGE_LIST::_db_con *__fastcall CPlayer::Emb_AddStorage(CPlayer *this, char byStorageCode, _STORAGE_LIST::_storage_con *pCon, bool bEquipChange, bool bAdd)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char *v7; // rax@5
  _STORAGE_LIST::_db_con *result; // rax@5
  char *v9; // rax@7
  char *v10; // rax@9
  LendItemMng *v11; // rax@13
  char *v12; // rax@14
  CPlayer::CashChangeStateFlag *v13; // rax@31
  __int64 v14; // [sp+0h] [bp-88h]@1
  _DWORD bUpdate[2]; // [sp+20h] [bp-68h]@5
  int v16; // [sp+28h] [bp-60h]@5
  unsigned int v17; // [sp+30h] [bp-58h]@6
  _STORAGE_LIST::_db_con *pkItem; // [sp+38h] [bp-50h]@8
  _ResourceItem_fld *pFld; // [sp+40h] [bp-48h]@41
  CPlayer::CashChangeStateFlag v20; // [sp+48h] [bp-40h]@31
  int v21; // [sp+4Ch] [bp-3Ch]@5
  int v22; // [sp+50h] [bp-38h]@5
  unsigned int v23; // [sp+54h] [bp-34h]@5
  int v24; // [sp+58h] [bp-30h]@7
  int v25; // [sp+5Ch] [bp-2Ch]@7
  unsigned int v26; // [sp+60h] [bp-28h]@7
  int v27; // [sp+64h] [bp-24h]@9
  int v28; // [sp+68h] [bp-20h]@9
  unsigned int v29; // [sp+6Ch] [bp-1Ch]@9
  int v30; // [sp+70h] [bp-18h]@14
  int v31; // [sp+74h] [bp-14h]@14
  unsigned int v32; // [sp+78h] [bp-10h]@14
  CPlayer *v33; // [sp+90h] [bp+8h]@1
  char v34; // [sp+98h] [bp+10h]@1
  _STORAGE_LIST::_storage_con *pCona; // [sp+A0h] [bp+18h]@1

  pCona = pCon;
  v34 = byStorageCode;
  v33 = this;
  v5 = &v14;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( (signed int)(unsigned __int8)byStorageCode < 8 )
  {
    v17 = _STORAGE_LIST::TransInCon(v33->m_Param.m_pStoragePtr[(unsigned __int8)byStorageCode], pCon);
    if ( v17 == 255 )
    {
      v24 = pCona->m_wItemIndex;
      v25 = pCona->m_byTableCode;
      v26 = (unsigned __int8)v34;
      v9 = CPlayerDB::GetCharNameA(&v33->m_Param);
      v16 = v24;
      bUpdate[0] = v25;
      CLogFile::Write(&stru_1799C8E78, "%s: Emb_AddStorage.. TransInCon()error storage: %d, item: %d-%d: ", v9, v26);
      result = 0i64;
    }
    else
    {
      pkItem = &v33->m_Param.m_pStoragePtr[(unsigned __int8)v34]->m_pStorageList[v17];
      if ( pkItem )
      {
        if ( !pkItem->m_lnUID )
          pkItem->m_lnUID = UIDGenerator::getuid(unk_1799C608C);
        if ( !pkItem->m_byCsMethod
          || (v11 = LendItemMng::Instance(), LendItemMng::InsertLink(v11, v33->m_ObjID.m_wIndex, v34, pkItem)) )
        {
          if ( v33->m_pUserDB )
          {
            LOBYTE(bUpdate[0]) = bAdd;
            CUserDB::Update_ItemAdd(v33->m_pUserDB, v34, v17, pkItem, bAdd);
          }
          if ( v34 == 1 || v34 == 2 )
          {
            if ( pkItem->m_byTableCode != 10 )
              CPlayer::SetEquipEffect(v33, pCona, 1);
            CPlayer::SetEffectEquipCode(v33, v34, v17, 1);
          }
          if ( (v34 == 1 || v34 == 2)
            && (pkItem->m_byTableCode < 5
             || pkItem->m_byTableCode == 5
             || pkItem->m_byTableCode == 8
             || pkItem->m_byTableCode == 7
             || pkItem->m_byTableCode == 9) )
          {
            CPlayer::CalcDefTol(v33);
          }
          if ( v34 == 1 )
          {
            CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v20, 0);
            CPlayer::UpdateVisualVer(v33, (CPlayer::CashChangeStateFlag)v13->0);
            CPlayer::SendMsg_EquipPartChange(v33, pCona->m_byTableCode);
            if ( pkItem->m_byTableCode == 6 )
            {
              if ( !CPlayer::IsRidingUnit(v33) )
                _WEAPON_PARAM::FixWeapon(&v33->m_pmWpn, pkItem);
              if ( v33->m_bMineMode )
              {
                v33->m_bMineMode = 0;
                v33->m_dwMineNextTime = -1;
                CPlayer::SendMsg_MineCancle(v33);
              }
              if ( CPlayer::IsSiegeMode(v33) )
                CPlayer::SetSiege(v33, 0i64);
            }
            CPlayer::CalcEquipSpeed(v33);
            CPlayer::CalcEquipMaxDP(v33, 0);
          }
          if ( !v34 && pkItem->m_byTableCode == 18 )
          {
            pFld = (_ResourceItem_fld *)CRecordData::GetRecord(
                                          (CRecordData *)&unk_1799C6AA0 + pkItem->m_byTableCode,
                                          pkItem->m_wItemIndex);
            if ( pFld->m_nEffectDataNum > 0 )
            {
              CPlayer::SetHaveEffect(v33, 0);
              if ( !pFld->m_nEffType1 )
                CPlayer::SetMstHaveEffect(v33, pFld, pkItem, 1, 0);
            }
          }
          if ( v34 == 5 )
            v33->m_Param.m_dbTrunk.m_byItemSlotRace[v17] = CPlayerDB::GetRaceCode(&v33->m_Param);
          if ( v34 == 7 )
            v33->m_Param.m_dbExtTrunk.m_byItemSlotRace[v17] = CPlayerDB::GetRaceCode(&v33->m_Param);
          result = pkItem;
        }
        else
        {
          v30 = pCona->m_wItemIndex;
          v31 = pCona->m_byTableCode;
          v32 = (unsigned __int8)v34;
          v12 = CPlayerDB::GetCharNameA(&v33->m_Param);
          v16 = v30;
          bUpdate[0] = v31;
          CLogFile::Write(
            &stru_1799C8E78,
            "%s: Emb_AddStorage.. InsertLink ()error storage: %d, item: %d-%d: ",
            v12,
            v32);
          result = 0i64;
        }
      }
      else
      {
        v27 = pCona->m_wItemIndex;
        v28 = pCona->m_byTableCode;
        v29 = (unsigned __int8)v34;
        v10 = CPlayerDB::GetCharNameA(&v33->m_Param);
        v16 = v27;
        bUpdate[0] = v28;
        CLogFile::Write(&stru_1799C8E78, "%s: Emb_AddStorage.. Item is not exist. storage: %d, item: %d-%d: ", v10, v29);
        result = 0i64;
      }
    }
  }
  else
  {
    v21 = pCon->m_wItemIndex;
    v22 = pCon->m_byTableCode;
    v23 = (unsigned __int8)byStorageCode;
    v7 = CPlayerDB::GetCharNameA(&v33->m_Param);
    v16 = v21;
    bUpdate[0] = v22;
    CLogFile::Write(
      &stru_1799C8E78,
      "%s: Emb_AddStorage.. total_storage_num is over. storage: %d, item: %d-%d: ",
      v7,
      v23);
    result = 0i64;
  }
  return result;
}
