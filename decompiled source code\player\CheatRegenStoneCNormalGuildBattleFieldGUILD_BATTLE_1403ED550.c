/*
 * Function: ?CheatRegenStone@CNormalGuildBattleField@GUILD_BATTLE@@QEAAHPEAVCPlayer@@@Z
 * Address: 0x1403ED550
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattleField::CheatRegenStone(GUILD_BATTLE::CNormalGuildBattleField *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-A8h]@1
  float *pNewPos; // [sp+20h] [bp-88h]@7
  float Src; // [sp+38h] [bp-70h]@7
  float v8; // [sp+3Ch] [bp-6Ch]@8
  float v9; // [sp+40h] [bp-68h]@8
  _object_create_setdata Dst; // [sp+68h] [bp-40h]@9
  GUILD_BATTLE::CNormalGuildBattleField *v11; // [sp+B0h] [bp+8h]@1
  CPlayer *v12; // [sp+B8h] [bp+10h]@1

  v12 = pkPlayer;
  v11 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v11->m_bInit && pkPlayer )
  {
    pNewPos = &Src;
    if ( !CMapData::GetRandPosVirtualDumExcludeStdRange(pkPlayer->m_pCurMap, pkPlayer->m_fCurPos, 20, 10, &Src) )
    {
      Src = v12->m_fCurPos[0];
      v8 = v12->m_fCurPos[1];
      v9 = v12->m_fCurPos[2];
    }
    _object_create_setdata::_object_create_setdata(&Dst);
    Dst.m_nLayerIndex = 0;
    Dst.m_pMap = v11->m_pkMap;
    memcpy_0(Dst.m_fStartPos, &Src, 0xCui64);
    Dst.m_pRecordSet = 0i64;
    CGravityStone::Destroy(v11->m_pkBall);
    if ( CGravityStone::Regen(v11->m_pkBall, &Dst) )
      result = 0i64;
    else
      result = 4294967293i64;
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
