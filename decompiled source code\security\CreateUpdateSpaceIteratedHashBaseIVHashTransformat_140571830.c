/*
 * Function: ?CreateUpdateSpace@?$IteratedHashBase@IVHashTransformation@CryptoPP@@@CryptoPP@@UEAAPEAEAEA_K@Z
 * Address: 0x140571830
 */

__int64 __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::HashTransformation>::CreateUpdateSpace(__int64 a1, _QWORD *a2)
{
  unsigned int v2; // eax@1
  unsigned int v3; // ST20_4@1
  __int64 v4; // rax@1
  int v6; // [sp+24h] [bp-14h]@1
  __int64 v7; // [sp+40h] [bp+8h]@1
  _QWORD *v8; // [sp+48h] [bp+10h]@1

  v8 = a2;
  v7 = a1;
  v6 = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  v2 = CryptoPP::ModPowerOf2<unsigned int,unsigned int>((_DWORD *)(v7 + 8), (const unsigned int *)&v6);
  v3 = v2;
  *v8 = v6 - v2;
  LODWORD(v4) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v7 + 176i64))(v7);
  return v3 + v4;
}
