/*
 * Function: j_??0?$_List_nod@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@IEAA@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@1@@Z
 * Address: 0x140005CF9
 */

void __fastcall std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > > *this, std::allocator<std::pair<int const ,CNationSettingFactory *> > _Al)
{
  std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>(
    this,
    _Al);
}
