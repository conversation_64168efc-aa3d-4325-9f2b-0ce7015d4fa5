/*
 * Function: ?_db_Check_NpcData@CMainThread@@AEAAEKPEAU_AVATOR_DATA@@@Z
 * Address: 0x1401A9B20
 */

char __fastcall CMainThread::_db_Check_NpcData(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pAvatorData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-E8h]@1
  char v7; // [sp+20h] [bp-C8h]@6
  CCheckSumCharacAccountTrunkData kSrcValue; // [sp+38h] [bp-B0h]@6
  CCheckSumCharacAccountTrunkData v9; // [sp+88h] [bp-60h]@6
  int v10; // [sp+C4h] [bp-24h]@6
  char v11; // [sp+C8h] [bp-20h]@7
  char v12; // [sp+C9h] [bp-1Fh]@9
  char v13; // [sp+CAh] [bp-1Eh]@11
  char v14; // [sp+CBh] [bp-1Dh]@12
  __int64 v15; // [sp+D0h] [bp-18h]@4
  CMainThread *v16; // [sp+F0h] [bp+8h]@1
  unsigned int dwSeriala; // [sp+F8h] [bp+10h]@1
  _AVATOR_DATA *pAvator; // [sp+100h] [bp+18h]@1

  pAvator = pAvatorData;
  dwSeriala = dwSerial;
  v16 = this;
  v3 = &v6;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  if ( v16->m_bCheckSumActive )
  {
    v7 = pAvatorData->dbAvator.m_byRaceSexCode >> 1;
    CCheckSumCharacAccountTrunkData::CCheckSumCharacAccountTrunkData(
      &kSrcValue,
      dwSerial,
      pAvatorData->dbAvator.m_dwAccountSerial,
      v7);
    CCheckSumCharacAccountTrunkData::CCheckSumCharacAccountTrunkData(
      &v9,
      dwSeriala,
      pAvator->dbAvator.m_dwAccountSerial,
      v7);
    CCheckSumCharacAccountTrunkData::Encode(&kSrcValue, pAvator);
    v10 = CCheckSumCharacAccountTrunkData::Load(&v9, v16->m_pWorldDB, &kSrcValue);
    if ( v10 >= 0 )
    {
      v10 = CCheckSumCharacAccountTrunkData::CheckDiff(
              &v9,
              v16->m_pWorldDB,
              pAvator->dbAvator.m_wszAvatorName,
              &kSrcValue);
      if ( v10 >= 0 )
      {
        if ( v10 <= 0 )
        {
          v14 = 0;
          CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v9);
          CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&kSrcValue);
          result = v14;
        }
        else
        {
          v13 = 40;
          CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v9);
          CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&kSrcValue);
          result = v13;
        }
      }
      else
      {
        v12 = 24;
        CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v9);
        CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&kSrcValue);
        result = v12;
      }
    }
    else
    {
      v11 = 24;
      CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&v9);
      CCheckSumCharacAccountTrunkData::~CCheckSumCharacAccountTrunkData(&kSrcValue);
      result = v11;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
