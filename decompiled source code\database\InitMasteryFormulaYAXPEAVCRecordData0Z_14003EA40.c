/*
 * Function: ?InitMasteryFormula@@YAXPEAVCRecordData@@0@Z
 * Address: 0x14003EA40
 */

void __fastcall InitMasteryFormula(CRecordData *pSkillData, CRecordData *pForceData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@5
  int v5; // eax@11
  __int64 v6; // [sp+0h] [bp-48h]@1
  int n; // [sp+20h] [bp-28h]@4
  _base_fld *v8; // [sp+28h] [bp-20h]@6
  _base_fld *v9; // [sp+30h] [bp-18h]@12
  CRecordData *v10; // [sp+50h] [bp+8h]@1
  CRecordData *v11; // [sp+58h] [bp+10h]@1

  v11 = pForceData;
  v10 = pSkillData;
  v2 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memset_0(s_nSkillLvPerMastery, -1, 0x20ui64);
  memset_0(s_nForceLvPerMastery, -1, 0x60ui64);
  for ( n = 0; ; ++n )
  {
    v4 = CRecordData::GetRecordNum(v10);
    if ( n >= v4 )
      break;
    v8 = CRecordData::GetRecord(v10, n);
    if ( *(_DWORD *)&v8[1].m_strCode[4] >= 0 && *(_DWORD *)&v8[1].m_strCode[4] < 8 )
      s_nSkillLvPerMastery[*(_DWORD *)&v8[1].m_strCode[4]] = *(_DWORD *)&v8[4].m_strCode[60];
  }
  for ( n = 0; ; ++n )
  {
    v5 = CRecordData::GetRecordNum(v11);
    if ( n >= v5 )
      break;
    v9 = CRecordData::GetRecord(v11, n);
    if ( *(_DWORD *)&v9[1].m_strCode[4] >= 0 && *(_DWORD *)&v9[1].m_strCode[4] < 24 )
      s_nForceLvPerMastery[*(_DWORD *)&v9[1].m_strCode[4]] = *(_DWORD *)&v9[4].m_strCode[60];
  }
}
