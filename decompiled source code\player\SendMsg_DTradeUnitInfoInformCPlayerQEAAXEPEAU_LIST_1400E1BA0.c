/*
 * Function: ?SendMsg_DTradeUnitInfoInform@CPlayer@@QEAAXEPEAU_LIST@_UNIT_DB_BASE@@@Z
 * Address: 0x1400E1BA0
 */

void __fastcall CPlayer::SendMsg_DTradeUnitInfoInform(CPlayer *this, char byTradeSlotIndex, _UNIT_DB_BASE::_LIST *pUnitData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char szMsg; // [sp+38h] [bp-80h]@4
  char v7; // [sp+39h] [bp-7Fh]@4
  unsigned int v8; // [sp+3Ah] [bp-7Eh]@4
  char Dst; // [sp+3Eh] [bp-7Ah]@4
  char v10; // [sp+44h] [bp-74h]@4
  char v11; // [sp+4Ch] [bp-6Ch]@4
  int v12; // [sp+6Ch] [bp-4Ch]@4
  char pbyType; // [sp+84h] [bp-34h]@4
  char v14; // [sp+85h] [bp-33h]@4
  unsigned __int64 v15; // [sp+A0h] [bp-18h]@4
  CPlayer *v16; // [sp+C0h] [bp+8h]@1
  _UNIT_DB_BASE::_LIST *v17; // [sp+D0h] [bp+18h]@1

  v17 = pUnitData;
  v16 = this;
  v3 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byTradeSlotIndex;
  v7 = pUnitData->byFrame;
  v8 = pUnitData->dwGauge;
  memcpy_0(&Dst, pUnitData->byPart, 6ui64);
  memcpy_0(&v10, v17->dwBullet, 8ui64);
  memcpy_0(&v11, v17->dwSpare, 0x20ui64);
  v12 = v17->nPullingFee;
  pbyType = 18;
  v14 = 27;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x38u);
}
