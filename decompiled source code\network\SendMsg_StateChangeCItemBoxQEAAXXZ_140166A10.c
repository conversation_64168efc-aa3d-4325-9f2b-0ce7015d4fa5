/*
 * Function: ?SendMsg_StateChange@CItemBox@@QEAAXXZ
 * Address: 0x140166A10
 */

void __fastcall CItemBox::SendMsg_StateChange(CItemBox *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@5
  unsigned int v5; // [sp+36h] [bp-42h]@5
  char v6; // [sp+3Ah] [bp-3Eh]@5
  char pbyType; // [sp+54h] [bp-24h]@5
  char v8; // [sp+55h] [bp-23h]@5
  CItemBox *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v9->m_bHide )
  {
    v6 = v9->m_nStateCode;
    *(_WORD *)szMsg = v9->m_ObjID.m_wIndex;
    v5 = v9->m_dwOwnerSerial;
    pbyType = 7;
    v8 = 1;
    CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 7, 0);
  }
}
