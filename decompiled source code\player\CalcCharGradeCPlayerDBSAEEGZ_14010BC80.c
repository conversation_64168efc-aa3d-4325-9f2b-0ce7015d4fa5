/*
 * Function: ?CalcChar<PERSON>rade@CPlayerDB@@SAEEG@Z
 * Address: 0x14010BC80
 */

char __fastcall CPlayerDB::CalcCharGrade(char byLv, unsigned __int16 wRankRate)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  float v6; // [sp+20h] [bp-68h]@6
  char Dest; // [sp+38h] [bp-50h]@6
  _base_fld *v8; // [sp+68h] [bp-20h]@6
  unsigned __int64 v9; // [sp+78h] [bp-10h]@4
  char v10; // [sp+90h] [bp+8h]@1
  unsigned __int16 v11; // [sp+98h] [bp+10h]@1

  v11 = wRankRate;
  v10 = byLv;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( (signed int)wRankRate >= 10000 )
    v11 = 0;
  v6 = (float)((float)v11 / 100.0) / 100.0;
  _itoa((unsigned __int8)v10, &Dest, 10);
  v8 = CRecordData::GetRecord(&stru_1799C6580, &Dest);
  while ( v8 )
  {
    if ( *(float *)&v8[1].m_dwIndex >= v6 )
      return v8[1].m_strCode[0];
    v8 = CRecordData::GetRecord(&stru_1799C6580, v8->m_dwIndex + 1);
    if ( !v8 )
      return 0;
    if ( strcmp_0(v8->m_strCode, &Dest) )
      return 0;
  }
  return 0;
}
