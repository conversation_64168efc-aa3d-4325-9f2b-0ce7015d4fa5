/*
 * Function: ?TransferMessagesTo@BufferedTransformation@CryptoPP@@QEAAIAEAV12@IAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x1405F7980
 */

__int64 __fastcall CryptoPP::BufferedTransformation::TransferMessagesTo(__int64 a1, __int64 a2, unsigned int a3, __int64 a4)
{
  unsigned int v5; // [sp+50h] [bp+18h]@1
  __int64 v6; // [sp+58h] [bp+20h]@1

  v6 = a4;
  v5 = a3;
  CryptoPP::BufferedTransformation::TransferMessagesTo2(a1, a2, (int *)&v5, a4, 1u);
  return v5;
}
