/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140002671
 */

void __fastcall std::_Uninit_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val, std::allocator<CMoveMapLimitInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
