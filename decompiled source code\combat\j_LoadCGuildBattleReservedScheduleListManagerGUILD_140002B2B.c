/*
 * Function: j_?Load@CGuildBattleReservedScheduleListManager@GUILD_BATTLE@@QEAA_NHIHH@Z
 * Address: 0x140002B2B
 */

bool __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Load(GUILD_BATTLE::CGuildBattleReservedScheduleListManager *this, int iCurDay, unsigned int uiOldMapCnt, int iToday, int iTomorrow)
{
  return GUILD_BATTLE::CGuildBattleReservedScheduleListManager::Load(this, iCurDay, uiOldMapCnt, iToday, iTomorrow);
}
