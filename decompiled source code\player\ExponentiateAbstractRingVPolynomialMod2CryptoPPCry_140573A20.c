/*
 * Function: ?Exponentiate@?$AbstractRing@VPolynomialMod2@CryptoPP@@@CryptoPP@@UEBA?AVPolynomialMod2@2@AEBV32@AEBVInteger@2@@Z
 * Address: 0x140573A20
 */

CryptoPP::PolynomialMod2 *__fastcall CryptoPP::AbstractRing<CryptoPP::PolynomialMod2>::Exponentiate(__int64 a1, CryptoPP::PolynomialMod2 *a2, __int64 a3, __int64 a4)
{
  struct CryptoPP::PolynomialMod2 v5; // [sp+30h] [bp-38h]@1
  int v6; // [sp+48h] [bp-20h]@1
  __int64 v7; // [sp+50h] [bp-18h]@1
  __int64 v8; // [sp+70h] [bp+8h]@1
  CryptoPP::PolynomialMod2 *v9; // [sp+78h] [bp+10h]@1
  __int64 v10; // [sp+80h] [bp+18h]@1
  __int64 v11; // [sp+88h] [bp+20h]@1

  v11 = a4;
  v10 = a3;
  v9 = a2;
  v8 = a1;
  v7 = -2i64;
  v6 = 0;
  CryptoPP::PolynomialMod2::PolynomialMod2(&v5);
  (*(void (__fastcall **)(__int64, struct CryptoPP::PolynomialMod2 *, __int64, __int64))(*(_QWORD *)v8 + 168i64))(
    v8,
    &v5,
    v10,
    v11);
  CryptoPP::PolynomialMod2::PolynomialMod2(v9, &v5);
  v6 |= 1u;
  CryptoPP::PolynomialMod2::~PolynomialMod2(&v5);
  return v9;
}
