/*
 * Function: ?_pre_check_unit_attack@CPlayer@@QEAAHPEAVCCharacter@@EPEAPEAU_UnitPart_fld@@PEAPEAU_UnitBullet_fld@@PEAPEAU_unit_bullet_param@@@Z
 * Address: 0x1400869E0
 */

signed __int64 __usercall CPlayer::_pre_check_unit_attack@<rax>(CPlayer *this@<rcx>, CCharacter *pDst@<rdx>, char by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@<r8b>, _UnitPart_fld **ppWeaponFld@<r9>, float a5@<xmm0>, _UnitBullet_fld **ppBulletFld, _unit_bullet_param **ppBulletParam)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v10; // rdx@42
  __int64 v11; // [sp+0h] [bp-48h]@1
  _base_fld *v12; // [sp+20h] [bp-28h]@4
  _UnitBullet_fld *v13; // [sp+28h] [bp-20h]@4
  _unit_bullet_param *v14; // [sp+30h] [bp-18h]@4
  int v15; // [sp+38h] [bp-10h]@42
  CPlayer *v16; // [sp+50h] [bp+8h]@1
  CCharacter *v17; // [sp+58h] [bp+10h]@1
  char v18; // [sp+60h] [bp+18h]@1
  _UnitPart_fld **v19; // [sp+68h] [bp+20h]@1

  v19 = ppWeaponFld;
  v18 = byWeaponPart;
  v17 = pDst;
  v16 = this;
  v7 = &v11;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v12 = 0i64;
  v13 = 0i64;
  v14 = 0i64;
  if ( !v16->m_bSFDelayNotCheck && !_ATTACK_DELAY_CHECKER::IsDelay(&v16->m_AttDelayChker, -1, 0xFFu, -1) )
    return 4294967291i64;
  if ( !v17 )
    return 4294967290i64;
  if ( !CPlayer::IsRidingUnit(v16) )
    return 4294967275i64;
  if ( v16->m_byMoveType == 2 )
    return 4294967255i64;
  if ( _effect_parameter::GetEff_State(&v16->m_EP, 20) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v16->m_EP, 28) )
    return 4294967259i64;
  v14 = (_unit_bullet_param *)&v16->m_pUsingUnit->dwBullet[(unsigned __int8)v18];
  if ( !v14->wLeftNum || v14->wLeftNum == 0xFFFF )
    return 4294967279i64;
  v13 = (_UnitBullet_fld *)CRecordData::GetRecord(&stru_1799C8AF0, v14->wBulletIndex);
  if ( !v13 )
    return 4294967236i64;
  if ( v18 )
    v12 = CRecordData::GetRecord(&stru_1799C86D0 + 4, v16->m_pUsingUnit->byPart[4]);
  else
    v12 = CRecordData::GetRecord(&stru_1799C86D0 + 3, v16->m_pUsingUnit->byPart[3]);
  if ( !v12 )
    return 4294967236i64;
  if ( !v17 )
    goto LABEL_52;
  if ( (CPlayer *)v17 == v16 )
    return 4294967290i64;
  if ( !v17->m_bLive
    || v17->m_bCorpse
    || v17->m_pCurMap != v16->m_pCurMap
    || CGameObject::GetCurSecNum((CGameObject *)&v17->vfptr) == -1 )
  {
    return 4294967290i64;
  }
  if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v16->vfptr->IsAttackableInTown)(v16)
    && !(unsigned __int8)((int (__fastcall *)(CCharacter *))v17->vfptr->IsAttackableInTown)(v17)
    && ((unsigned __int8)((int (__fastcall *)(CPlayer *))v16->vfptr->IsInTown)(v16)
     || (unsigned __int8)((int (__fastcall *)(CCharacter *))v17->vfptr->IsInTown)(v17)) )
  {
    return 4294967265i64;
  }
  if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, CPlayer *))v17->vfptr->IsBeDamagedAble)(v17, v16) )
    return 4294967290i64;
  v15 = CPlayer::_pre_check_in_guild_battle(v16, v17);
  if ( v15 )
    return (unsigned int)v15;
  LOBYTE(v10) = 1;
  if ( (unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v17->vfptr->IsBeAttackedAble)(v17, v10) )
  {
LABEL_52:
    GetSqrt(v17->m_fCurPos, v16->m_fCurPos);
    if ( a5 <= (float)(*(float *)&v12[4].m_strCode[44] + 40.0)
      || (GetSqrt(v17->m_fOldPos, v16->m_fCurPos), a5 <= (float)(*(float *)&v12[4].m_strCode[44] + 40.0)) )
    {
      *v19 = (_UnitPart_fld *)v12;
      *ppBulletFld = v13;
      *ppBulletParam = v14;
      result = 0i64;
    }
    else
    {
      result = 4294967293i64;
    }
  }
  else
  {
    result = 4294967290i64;
  }
  return result;
}
