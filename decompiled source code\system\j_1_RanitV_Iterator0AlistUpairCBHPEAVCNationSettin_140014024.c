/*
 * Function: j_??1?$_Ranit@V?$_Iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@_JPEBV123@AEBV123@@std@@QEAA@XZ
 * Address: 0x140014024
 */

void __fastcall std::_Ranit<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,__int64,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const &>::~_Ranit<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,__int64,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const &>(std::_Ranit<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0>,__int64,std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> const *,std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Iterator<0> const &> *this)
{
  std::_Ranit<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,__int64,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const &>::~_Ranit<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0>,__int64,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const *,std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Iterator<0> const &>(this);
}
