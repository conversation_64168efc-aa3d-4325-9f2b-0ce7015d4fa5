/*
 * Function: ?Encrypt@?$DL_EncryptorBase@VInteger@CryptoPP@@@CryptoPP@@UEBAXAEAVRandomNumberGenerator@2@PEBE_KPEAEAEBVNameValuePairs@2@@Z
 * Address: 0x140634BB0
 */

void __fastcall CryptoPP::DL_EncryptorBase<CryptoPP::Integer>::Encrypt(__int64 a1, struct CryptoPP::RandomNumberGenerator *a2, __int64 a3, __int64 a4, __int64 a5)
{
  void (__fastcall ***v5)(_QWORD, _QWORD, _QWORD, _QWORD); // rax@1
  __int64 *v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  __int64 v9; // rax@1
  struct CryptoPP::Integer *v10; // rax@1
  struct CryptoPP::Integer *v11; // rax@1
  struct CryptoPP::Integer *v12; // rax@1
  const struct CryptoPP::Integer *v13; // rax@1
  __int64 v14; // rdx@1
  __int64 v15; // rdx@1
  __int64 v16; // rax@1
  unsigned __int64 v17; // rax@1
  char *v18; // rax@1
  char *v19; // rax@1
  CryptoPP::Integer v20; // [sp+40h] [bp-148h]@1
  int v21; // [sp+68h] [bp-120h]@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v22; // [sp+70h] [bp-118h]@1
  __int64 *v23; // [sp+88h] [bp-100h]@1
  __int64 v24; // [sp+90h] [bp-F8h]@1
  void (__fastcall ***v25)(_QWORD, _QWORD, _QWORD, _QWORD); // [sp+98h] [bp-F0h]@1
  __int64 v26; // [sp+A0h] [bp-E8h]@1
  CryptoPP::Integer v27; // [sp+A8h] [bp-E0h]@1
  CryptoPP::Integer v28; // [sp+D0h] [bp-B8h]@1
  __int64 v29; // [sp+F8h] [bp-90h]@1
  CryptoPP::Integer v30; // [sp+100h] [bp-88h]@1
  __int64 v31; // [sp+128h] [bp-60h]@1
  __int64 v32; // [sp+130h] [bp-58h]@1
  struct CryptoPP::Integer *v33; // [sp+138h] [bp-50h]@1
  struct CryptoPP::Integer *v34; // [sp+140h] [bp-48h]@1
  __int64 v35; // [sp+148h] [bp-40h]@1
  struct CryptoPP::Integer *v36; // [sp+150h] [bp-38h]@1
  struct CryptoPP::Integer *v37; // [sp+158h] [bp-30h]@1
  void (__fastcall **v38)(_QWORD, _QWORD, _QWORD, _QWORD); // [sp+160h] [bp-28h]@1
  unsigned __int64 v39; // [sp+168h] [bp-20h]@1
  __int64 v40; // [sp+170h] [bp-18h]@1
  __int64 v41; // [sp+178h] [bp-10h]@1
  __int64 v42; // [sp+190h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v43; // [sp+198h] [bp+10h]@1
  __int64 v44; // [sp+1A0h] [bp+18h]@1
  __int64 v45; // [sp+1A8h] [bp+20h]@1

  v45 = a4;
  v44 = a3;
  v43 = a2;
  v42 = a1;
  v31 = -2i64;
  LODWORD(v5) = (*(int (**)(void))(*(_QWORD *)a1 + 64i64))();
  v25 = v5;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v42 + 72i64))(v42);
  v23 = v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v42 + 80i64))(v42);
  v29 = v7;
  LODWORD(v8) = CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>::GetAbstractGroupParameters(v42 + 16);
  v24 = v8;
  v32 = *(_QWORD *)(v42 + 16);
  LODWORD(v9) = (*(int (__fastcall **)(signed __int64))(v32 + 8))(v42 + 16);
  v26 = v9;
  LODWORD(v10) = CryptoPP::Integer::One();
  v33 = v10;
  LODWORD(v11) = CryptoPP::Integer::Zero();
  v34 = v11;
  v35 = *(_QWORD *)v24;
  LODWORD(v12) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *))(v35 + 72))(v24, &v30);
  v36 = v12;
  v37 = v12;
  LODWORD(v13) = CryptoPP::Integer::One();
  CryptoPP::Integer::Integer((__int64)&v20, v43, v13, v37, 0, v34, v33);
  CryptoPP::Integer::~Integer(&v30);
  (*(void (__fastcall **)(__int64, CryptoPP::Integer *, CryptoPP::Integer *))(*(_QWORD *)v24 + 24i64))(v24, &v27, &v20);
  LOBYTE(v14) = 1;
  (*(void (__fastcall **)(__int64, __int64, CryptoPP::Integer *, __int64))(*(_QWORD *)v24 + 104i64))(v24, v14, &v27, a5);
  LOBYTE(v15) = 1;
  v21 = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v24 + 96i64))(v24, v15);
  LODWORD(v16) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v26 + 48i64))(v26);
  v38 = *v25;
  (*v38)(v25, &v28, v24, v16);
  LODWORD(v17) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v29 + 8i64))(v29, v45);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v22,
    v17);
  v39 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v22);
  v18 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v22);
  v40 = *v23;
  (*(void (__fastcall **)(__int64 *, __int64, char *, unsigned __int64))(v40 + 8))(v23, v24, v18, v39);
  v19 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v22);
  v41 = *(_QWORD *)v29;
  (*(void (__fastcall **)(__int64, struct CryptoPP::RandomNumberGenerator *, char *, __int64))(v41 + 32))(
    v29,
    v43,
    v19,
    v44);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v22);
  CryptoPP::Integer::~Integer(&v28);
  CryptoPP::Integer::~Integer(&v27);
  CryptoPP::Integer::~Integer(&v20);
}
