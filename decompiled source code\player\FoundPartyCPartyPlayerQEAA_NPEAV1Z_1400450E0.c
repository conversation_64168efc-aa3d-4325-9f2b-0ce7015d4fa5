/*
 * Function: ?FoundParty@CPartyPlayer@@QEAA_NPEAV1@@Z
 * Address: 0x1400450E0
 */

char __fastcall CPartyPlayer::FoundParty(CPartyPlayer *this, CPartyPlayer *pParticiper)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPartyPlayer *v6; // [sp+30h] [bp+8h]@1
  CPartyPlayer *pJoiner; // [sp+38h] [bp+10h]@1

  pJoiner = pParticiper;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v6) )
  {
    result = 0;
  }
  else if ( CPartyPlayer::IsPartyMode(pJoiner) )
  {
    result = 0;
  }
  else
  {
    CPartyPlayer::PartyListInit(v6);
    v6->m_pPartyBoss = v6;
    v6->m_pPartyMember[0] = v6;
    v6->m_byLootShareSystem = 0;
    v6->m_pLootAuthor = v6;
    CPartyPlayer::InsertPartyMember(v6, pJoiner);
    result = 1;
  }
  return result;
}
