/*
 * Function: ?Init@_EMBELLISH_LIST@_EQUIP_DB_BASE@@QEAAXXZ
 * Address: 0x140075AD0
 */

void __fastcall _EQUIP_DB_BASE::_EMBELLISH_LIST::Init(_EQUIP_DB_BASE::_EMBELLISH_LIST *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _EQUIP_DB_BASE::_EMBELLISH_LIST *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _EMBELLKEY::SetRelease(&v4->Key);
  v4->wAmount = 0;
  v4->dwItemETSerial = 0;
  v4->lnUID = 0i64;
  v4->dwT = -1;
  v4->byCsMethod = 0;
  v4->dwLendRegdTime = -1;
}
