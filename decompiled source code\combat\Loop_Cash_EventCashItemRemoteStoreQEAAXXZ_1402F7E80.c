/*
 * Function: ?Loop_Cash_Event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402F7E80
 */

void __fastcall CashItemRemoteStore::Loop_Cash_Event(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CashItemRemoteStore *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    CashItemRemoteStore::Check_CashEvent_INI(v5, j);
    if ( pkDB )
    {
      if ( CRFNewDatabase::IsConectionActive((CRFNewDatabase *)&pkDB->vfptr) )
        CashItemRemoteStore::Check_CashEvent_Status(v5, j);
    }
  }
}
