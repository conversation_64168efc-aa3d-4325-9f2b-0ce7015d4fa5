/*
 * Function: j_??0?$pair@KPEAU_TimeItem_fld@@@std@@QEAA@AEBKAEBQEAU_TimeItem_fld@@@Z
 * Address: 0x14001039D
 */

void __fastcall std::pair<unsigned long,_TimeItem_fld *>::pair<unsigned long,_TimeItem_fld *>(std::pair<unsigned long,_TimeItem_fld *> *this, const unsigned int *_Val1, _TimeItem_fld *const *_Val2)
{
  std::pair<unsigned long,_TimeItem_fld *>::pair<unsigned long,_TimeItem_fld *>(this, _Val1, _Val2);
}
