/*
 * Function: ?GetSlot@CCharacter@@QEAAHPEAV1@@Z
 * Address: 0x140173B40
 */

signed __int64 __fastcall CCharacter::GetSlot(CCharacter *this, CCharacter *p)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  CCharacter *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  for ( j = 0; (signed int)j < 5; ++j )
  {
    if ( v6->m_AroundSlot[j] == p )
      return j;
  }
  return 0xFFFFFFFFi64;
}
