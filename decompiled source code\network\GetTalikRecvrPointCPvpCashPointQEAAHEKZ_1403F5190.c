/*
 * Function: ?GetTalikRecvrPoint@CPvpCashPoint@@QEAAHEK@Z
 * Address: 0x1403F5190
 */

int __fastcall CPvpCashPoint::GetTalikRecvrPoint(CPvpCashPoint *this, char byTblCode, unsigned int dwIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpCashMng *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-28h]@1
  char v8; // [sp+38h] [bp+10h]@1
  unsigned int dwIndexa; // [sp+40h] [bp+18h]@1

  dwIndexa = dwIndex;
  v8 = byTblCode;
  v3 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CPvpCashMng::Instance();
  return CPvpCashMng::GetTalikRecvrPoint(v5, v8, dwIndexa);
}
