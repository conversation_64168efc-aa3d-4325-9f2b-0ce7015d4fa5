/*
 * Function: ??0?$simple_ptr@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14063C350
 */

_QWORD *__fastcall CryptoPP::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>(_QWORD *a1)
{
  _QWORD *result; // rax@1

  result = a1;
  *a1 = 0i64;
  return result;
}
