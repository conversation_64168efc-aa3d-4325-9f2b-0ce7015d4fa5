/*
 * Function: ??0HashInputTooLong@CryptoPP@@QEAA@AEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@Z
 * Address: 0x1405705C0
 */

CryptoPP::InvalidDataFormat *__fastcall CryptoPP::HashInputTooLong::HashInputTooLong(CryptoPP::InvalidDataFormat *a1, __int64 a2)
{
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v2; // rax@1
  char v4; // [sp+20h] [bp-58h]@1
  __int64 v5; // [sp+50h] [bp-28h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *v6; // [sp+58h] [bp-20h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > *s; // [sp+60h] [bp-18h]@1
  CryptoPP::InvalidDataFormat *v8; // [sp+80h] [bp+8h]@1

  v8 = a1;
  v5 = -2i64;
  LODWORD(v2) = std::operator+<char,std::char_traits<char>,std::allocator<char>>(
                  &v4,
                  "IteratedHashBase: input data exceeds maximum allowed by hash function ",
                  a2);
  v6 = v2;
  s = v2;
  CryptoPP::InvalidDataFormat::InvalidDataFormat(v8, v2);
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(&v4);
  v8->vfptr = (std::exceptionVtbl *)&CryptoPP::HashInputTooLong::`vftable';
  return v8;
}
