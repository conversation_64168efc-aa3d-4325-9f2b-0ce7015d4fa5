/*
 * Function: ?SetChatLock@CUserDB@@QEAAX_N@Z
 * Address: 0x1401104A0
 */

void __fastcall CUserDB::SetChatLock(CUserDB *this, bool bLock)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@5
  __int64 v5; // [sp+0h] [bp-78h]@1
  _chat_lock_inform_zocl v6; // [sp+34h] [bp-44h]@5
  char pbyType; // [sp+54h] [bp-24h]@5
  char v8; // [sp+55h] [bp-23h]@5
  CUserDB *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9->m_bChatLock = bLock;
  if ( bLock )
  {
    v6.bLock = 1;
    pbyType = 2;
    v8 = 12;
    v4 = _chat_lock_inform_zocl::size(&v6);
    CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_idWorld.wIndex, &pbyType, (char *)&v6.bLock, v4);
  }
}
