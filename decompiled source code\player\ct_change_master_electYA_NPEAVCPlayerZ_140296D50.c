/*
 * Function: ?ct_change_master_elect@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296D50
 */

char __fastcall ct_change_master_elect(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v4; // [sp+0h] [bp-38h]@1
  bool v5; // [sp+20h] [bp-18h]@11
  bool v6; // [sp+21h] [bp-17h]@11
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    if ( v7->m_Param.m_pGuild )
    {
      if ( s_nWordCount == 1 )
      {
        v6 = atoi(s_pwszDstCheat[0]) != 0;
        v5 = v6;
        v7->m_Param.m_pGuild->m_bPossibleElectMaster = v6;
        CGuild::MakeDownMemberPacket(v7->m_Param.m_pGuild);
        CGuild::SendMsg_MasterElectPossible(v7->m_Param.m_pGuild, v5);
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
