/*
 * CAttackForceDamageCalculator.cpp - Advanced Damage Calculation System Implementation
 * Handles complex damage calculations for the AttackForce system
 */

#include "../Headers/CAttackForceDamageCalculator.h"
#include "../Headers/CAttackForceIntegration.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CMonsterAttack.h"

#include <cmath>
#include <random>
#include <algorithm>
#include <sstream>

using namespace NexusProtection::Combat::AttackForceConstants;

namespace NexusProtection {
namespace Combat {

/**
 * Constructor
 */
CAttackForceDamageCalculator::CAttackForceDamageCalculator() 
    : m_bEnableDetailedLogging(false)
    , m_bEnableCriticalHits(true)
    , m_bEnableBackAttackBonus(true) {
    
    InitializeModifierRegistry();
    Logger::Debug("CAttackForceDamageCalculator::CAttackForceDamageCalculator - Damage calculator initialized");
}

/**
 * Destructor
 */
CAttackForceDamageCalculator::~CAttackForceDamageCalculator() {
    m_modifierRegistry.clear();
    Logger::Debug("CAttackForceDamageCalculator::~CAttackForceDamageCalculator - Damage calculator destroyed");
}

/**
 * Calculate damage with full context
 */
DetailedDamageResult CAttackForceDamageCalculator::CalculateDamage(const DamageCalculationContext& context) {
    DetailedDamageResult result;
    
    if (!context.IsValid()) {
        Logger::Error("CAttackForceDamageCalculator::CalculateDamage - Invalid context");
        return result;
    }
    
    try {
        result.LogCalculation("Starting damage calculation");
        
        // Calculate base attack power (original lines 106-113)
        float fBaseAttackPower = CalculateBaseAttackPower(context.pAttacker, context.pParam, context.bUseEffBullet);
        result.nBaseDamage = static_cast<int>(std::floor(fBaseAttackPower));
        result.LogCalculation("Base damage: " + std::to_string(result.nBaseDamage));
        
        // Apply damage modifiers
        ApplyDamageModifiers(context, result);
        
        // Calculate final damage
        if (context.pTarget) {
            result.nFinalDamage = CalculateFinalDamage(
                context.pAttacker,
                context.pTarget,
                static_cast<int>(fBaseAttackPower * result.fTotalMultiplier),
                context.pParam->nPart,
                context.pParam->nTol,
                context.bBackAttack
            );
        } else {
            result.nFinalDamage = static_cast<int>(result.nBaseDamage * result.fTotalMultiplier);
        }
        
        // Apply variance
        result.nFinalDamage = DamageCalculationUtils::ApplyDamageVariance(result.nFinalDamage);
        
        // Clamp to valid range
        result.nFinalDamage = DamageCalculationUtils::ClampDamage(result.nFinalDamage);
        
        result.LogCalculation("Final damage: " + std::to_string(result.nFinalDamage));
        
        Logger::Debug("CAttackForceDamageCalculator::CalculateDamage - Final damage: %d (multiplier: %.2f)", 
                     result.nFinalDamage, result.fTotalMultiplier);
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::CalculateDamage - Exception: %s", e.what());
        result = DetailedDamageResult(); // Reset to default
    }
    
    return result;
}

/**
 * Calculate base attack power
 */
float CAttackForceDamageCalculator::CalculateBaseAttackPower(CCharacter* pAttacker, _attack_param* pParam, bool bUseEffBullet) {
    if (!pAttacker || !pParam) {
        return 0.0f;
    }
    
    try {
        // Get character-specific attack power (calls legacy _CalcForceAttPnt)
        int nCharacterAttackPower = GetCharacterAttackPower(pAttacker, bUseEffBullet);
        
        // Add parameter-based attack points (original line 106, 110)
        float fBaseAttackPower = static_cast<float>(pParam->nAddAttPnt + nCharacterAttackPower);
        
        // Apply effect rate modifier (original lines 108, 112)
        float fEffectRate = CAttackForceIntegration::GetEffectRate(pAttacker, static_cast<int>(EffectType::DamageRate));
        fBaseAttackPower *= fEffectRate;
        
        Logger::Debug("CAttackForceDamageCalculator::CalculateBaseAttackPower - Base: %.1f (char: %d, add: %d, rate: %.2f)", 
                     fBaseAttackPower, nCharacterAttackPower, pParam->nAddAttPnt, fEffectRate);
        
        return fBaseAttackPower;
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::CalculateBaseAttackPower - Exception: %s", e.what());
        return 0.0f;
    }
}

/**
 * Calculate hit chance
 */
float CAttackForceDamageCalculator::CalculateHitChance(CCharacter* pAttacker, CCharacter* pTarget, float fAccuracyBonus) {
    if (!pAttacker || !pTarget) {
        return 0.0f;
    }
    
    try {
        // Base hit chance with accuracy bonus (original lines 72-73)
        float fHitChance = HitChance::BASE_HIT_CHANCE + fAccuracyBonus;
        
        // Get accuracy bonus effect (original line 72)
        float fAccuracyEffect = CAttackForceIntegration::GetEffectPlus(pAttacker, static_cast<int>(EffectType::AccuracyBonus));
        fHitChance += fAccuracyEffect;
        
        // Get target's avoidance rate (original lines 74-75)
        int nAvoidanceRate = CAttackForceIntegration::GetAvoidanceRate(pTarget);
        fHitChance -= static_cast<float>(nAvoidanceRate);
        
        // Apply player-specific accuracy bonus (original lines 77-81)
        if (pAttacker->m_ObjID.m_byID == 0) { // Player character
            float fPlayerAccuracy = CAttackForceIntegration::GetEffectPlus(pAttacker, static_cast<int>(EffectType::PlayerAccuracy));
            fHitChance += fPlayerAccuracy;
        }
        
        // Clamp to valid range (original lines 82-91)
        fHitChance = Utils::Clamp(fHitChance, static_cast<float>(HitChance::MIN_HIT_CHANCE), static_cast<float>(HitChance::MAX_HIT_CHANCE));
        
        return fHitChance;
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::CalculateHitChance - Exception: %s", e.what());
        return 0.0f;
    }
}

/**
 * Calculate critical hit chance
 */
float CAttackForceDamageCalculator::CalculateCriticalChance(CCharacter* pAttacker, CCharacter* pTarget) {
    if (!pAttacker) {
        return 0.0f;
    }
    
    try {
        // Base critical hit chance
        float fCriticalChance = static_cast<float>(DamageCalculation::CRITICAL_HIT_CHANCE);
        
        // Apply character-specific modifiers
        // This would involve checking equipment, skills, buffs, etc.
        // For now, use a simple implementation
        
        return fCriticalChance;
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::CalculateCriticalChance - Exception: %s", e.what());
        return 0.0f;
    }
}

/**
 * Apply damage modifiers
 */
void CAttackForceDamageCalculator::ApplyDamageModifiers(const DamageCalculationContext& context, DetailedDamageResult& result) {
    if (!context.IsValid()) {
        return;
    }
    
    try {
        // Apply destroyer bonus (original lines 114-120)
        ApplyDestroyerBonus(context.pAttacker, result);
        
        // Apply PvP ranking bonuses (original lines 121-145)
        ApplyPvPBonuses(context.pAttacker, result);
        
        // Apply effect-based modifiers
        if (context.pTarget) {
            ApplyEffectModifiers(context.pAttacker, context.pTarget, result);
        }
        
        // Apply critical hit modifier
        if (context.bCriticalHit && m_bEnableCriticalHits) {
            ApplyCriticalHit(context, result);
        }
        
        // Apply back attack modifier
        if (context.bBackAttack && m_bEnableBackAttackBonus) {
            ApplyBackAttack(context, result);
        }
        
        // Apply registered modifiers
        for (const auto& [name, calculator] : m_modifierRegistry) {
            try {
                DamageModifier modifier = calculator(context);
                if (modifier.multiplier != 1.0f) {
                    result.AddModifier(modifier);
                    result.LogCalculation(name + ": " + std::to_string(modifier.multiplier));
                }
            } catch (const std::exception& e) {
                Logger::Warning("CAttackForceDamageCalculator::ApplyDamageModifiers - Modifier '%s' failed: %s", name.c_str(), e.what());
            }
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::ApplyDamageModifiers - Exception: %s", e.what());
    }
}

/**
 * Apply destroyer bonus
 */
void CAttackForceDamageCalculator::ApplyDestroyerBonus(CCharacter* pAttacker, DetailedDamageResult& result) {
    if (!pAttacker) {
        return;
    }
    
    try {
        // Check for destroyer or last attack buff bonuses (original lines 114-120)
        if (pAttacker->m_ObjID.m_byID == 0) { // Player character
            bool bIsDestroyer = CAttackForceIntegration::IsDestroyer(pAttacker);
            bool bHasLastAttBuff = CAttackForceIntegration::HasLastAttackBuff(static_cast<CPlayer*>(pAttacker));
            
            if (bIsDestroyer || bHasLastAttBuff) {
                DamageModifier modifier("Destroyer/LastAttack Bonus", DamageMultipliers::DESTROYER_BONUS);
                result.AddModifier(modifier);
                result.LogCalculation("Applied destroyer/last attack bonus");
            }
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::ApplyDestroyerBonus - Exception: %s", e.what());
    }
}

/**
 * Apply PvP ranking bonuses
 */
void CAttackForceDamageCalculator::ApplyPvPBonuses(CCharacter* pAttacker, DetailedDamageResult& result) {
    if (!pAttacker) {
        return;
    }
    
    try {
        // Check for PvP ranking bonuses (original lines 121-145)
        if (pAttacker->m_ObjID.m_byID == 0) { // Player character
            int nBossType = CAttackForceIntegration::GetPvPBossType(pAttacker);
            
            if (nBossType == static_cast<int>(PvPBossType::BossType2) || 
                nBossType == static_cast<int>(PvPBossType::BossType6)) {
                DamageModifier modifier("PvP Boss Type 2/6 Bonus", DamageMultipliers::BOSS_TYPE_2_6_BONUS);
                result.AddModifier(modifier);
                result.LogCalculation("Applied boss type 2/6 bonus");
            } else if (nBossType == static_cast<int>(PvPBossType::NoBoss)) {
                DamageModifier modifier("PvP No Boss Bonus", DamageMultipliers::BOSS_TYPE_0_BONUS);
                result.AddModifier(modifier);
                result.LogCalculation("Applied no boss bonus");
            }
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::ApplyPvPBonuses - Exception: %s", e.what());
    }
}

/**
 * Apply effect-based modifiers
 */
void CAttackForceDamageCalculator::ApplyEffectModifiers(CCharacter* pAttacker, CCharacter* pTarget, DetailedDamageResult& result) {
    if (!pAttacker || !pTarget) {
        return;
    }
    
    try {
        // Apply various effect-based damage modifiers
        // This would involve checking buffs, debuffs, equipment effects, etc.
        // For now, implement basic effect checking
        
        // Check for damage enhancement effects
        float fDamageEnhancement = CAttackForceIntegration::GetEffectPlus(pAttacker, 50); // Hypothetical damage enhancement effect
        if (fDamageEnhancement > 0.0f) {
            DamageModifier modifier("Damage Enhancement", 1.0f + (fDamageEnhancement / 100.0f), true);
            result.AddModifier(modifier);
            result.LogCalculation("Applied damage enhancement");
        }
        
        // Check for target vulnerability effects
        float fVulnerability = CAttackForceIntegration::GetEffectPlus(pTarget, 51); // Hypothetical vulnerability effect
        if (fVulnerability > 0.0f) {
            DamageModifier modifier("Target Vulnerability", 1.0f + (fVulnerability / 100.0f), true);
            result.AddModifier(modifier);
            result.LogCalculation("Applied target vulnerability");
        }
        
    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::ApplyEffectModifiers - Exception: %s", e.what());
    }
}

/**
 * Apply critical hit modifier
 */
void CAttackForceDamageCalculator::ApplyCriticalHit(const DamageCalculationContext& context, DetailedDamageResult& result) {
    try {
        DamageModifier modifier("Critical Hit", DamageMultipliers::CRITICAL_HIT_MULTIPLIER);
        result.AddModifier(modifier);
        result.bCriticalHit = true;
        result.LogCalculation("Applied critical hit");

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::ApplyCriticalHit - Exception: %s", e.what());
    }
}

/**
 * Apply back attack modifier
 */
void CAttackForceDamageCalculator::ApplyBackAttack(const DamageCalculationContext& context, DetailedDamageResult& result) {
    try {
        DamageModifier modifier("Back Attack", 1.2f); // 20% bonus for back attacks
        result.AddModifier(modifier);
        result.bBackAttack = true;
        result.LogCalculation("Applied back attack bonus");

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::ApplyBackAttack - Exception: %s", e.what());
    }
}

/**
 * Calculate final damage from attack power
 */
int CAttackForceDamageCalculator::CalculateFinalDamage(CCharacter* pAttacker, CCharacter* pTarget, int nAttackPower, int nPart, int nTol, bool bBackAttack) {
    if (!pAttacker || !pTarget) {
        return 0;
    }

    try {
        // Use legacy damage calculation system (original lines 163-186)
        return CAttackForceIntegration::CalculateAttackDamagePoint(pAttacker, nAttackPower, nPart, nTol, pTarget, bBackAttack);

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::CalculateFinalDamage - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Get character-specific attack power
 */
int CAttackForceDamageCalculator::GetCharacterAttackPower(CCharacter* pCharacter, bool bUseEffBullet) {
    if (!pCharacter) {
        return 0;
    }

    try {
        // Call legacy force attack point calculation
        return CAttackForceIntegration::CalculateForceAttackPoints(pCharacter, nullptr, bUseEffBullet);

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::GetCharacterAttackPower - Exception: %s", e.what());
        return 0;
    }
}

/**
 * Get weapon-specific modifiers
 */
float CAttackForceDamageCalculator::GetWeaponModifier(CCharacter* pCharacter) {
    if (!pCharacter) {
        return 1.0f;
    }

    try {
        // This would check the character's equipped weapon and return appropriate modifiers
        // For now, return a default modifier
        return 1.0f;

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::GetWeaponModifier - Exception: %s", e.what());
        return 1.0f;
    }
}

/**
 * Get level-based modifiers
 */
float CAttackForceDamageCalculator::GetLevelModifier(CCharacter* pAttacker, CCharacter* pTarget) {
    if (!pAttacker || !pTarget) {
        return 1.0f;
    }

    try {
        // This would calculate level-based damage modifiers
        // For now, return a default modifier
        return 1.0f;

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::GetLevelModifier - Exception: %s", e.what());
        return 1.0f;
    }
}

/**
 * Get defense reduction
 */
float CAttackForceDamageCalculator::GetDefenseReduction(CCharacter* pTarget, int nAttackType) {
    if (!pTarget) {
        return 0.0f;
    }

    try {
        // This would calculate defense-based damage reduction
        // For now, return a default reduction
        return 0.1f; // 10% defense reduction

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::GetDefenseReduction - Exception: %s", e.what());
        return 0.0f;
    }
}

/**
 * Initialize modifier registry
 */
void CAttackForceDamageCalculator::InitializeModifierRegistry() {
    try {
        // Register weapon-based modifiers
        RegisterModifier("WeaponModifier", [this](const DamageCalculationContext& context) -> DamageModifier {
            float modifier = GetWeaponModifier(context.pAttacker);
            return DamageModifier("Weapon Modifier", modifier);
        });

        // Register level-based modifiers
        RegisterModifier("LevelModifier", [this](const DamageCalculationContext& context) -> DamageModifier {
            float modifier = GetLevelModifier(context.pAttacker, context.pTarget);
            return DamageModifier("Level Modifier", modifier);
        });

        // Register defense reduction
        RegisterModifier("DefenseReduction", [this](const DamageCalculationContext& context) -> DamageModifier {
            if (!context.pTarget) return DamageModifier();
            float reduction = GetDefenseReduction(context.pTarget, 0);
            return DamageModifier("Defense Reduction", 1.0f - reduction);
        });

        Logger::Debug("CAttackForceDamageCalculator::InitializeModifierRegistry - Registered %zu modifiers", m_modifierRegistry.size());

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::InitializeModifierRegistry - Exception: %s", e.what());
    }
}

/**
 * Register a damage modifier
 */
void CAttackForceDamageCalculator::RegisterModifier(const std::string& name, std::function<DamageModifier(const DamageCalculationContext&)> calculator) {
    try {
        m_modifierRegistry[name] = calculator;
        Logger::Debug("CAttackForceDamageCalculator::RegisterModifier - Registered modifier: %s", name.c_str());

    } catch (const std::exception& e) {
        Logger::Error("CAttackForceDamageCalculator::RegisterModifier - Exception: %s", e.what());
    }
}

/**
 * Log calculation step
 */
void CAttackForceDamageCalculator::LogCalculationStep(const std::string& step, float value) {
    if (m_bEnableDetailedLogging) {
        Logger::Debug("CAttackForceDamageCalculator - %s: %.2f", step.c_str(), value);
    }
}

/**
 * Damage calculation utility functions
 */
namespace DamageCalculationUtils {

/**
 * Apply damage variance
 */
int ApplyDamageVariance(int nBaseDamage, float fVariancePercent) {
    if (nBaseDamage <= 0 || fVariancePercent <= 0.0f) {
        return nBaseDamage;
    }

    try {
        static std::random_device rd;
        static std::mt19937 gen(rd());

        int nVariance = static_cast<int>(nBaseDamage * fVariancePercent);
        std::uniform_int_distribution<> dis(-nVariance, nVariance);

        return nBaseDamage + dis(gen);

    } catch (const std::exception& e) {
        Logger::Error("DamageCalculationUtils::ApplyDamageVariance - Exception: %s", e.what());
        return nBaseDamage;
    }
}

/**
 * Calculate elemental damage modifier
 */
float CalculateElementalModifier(int nAttackerElement, int nTargetElement) {
    // Simple elemental system - in a real implementation this would be more complex
    if (nAttackerElement == nTargetElement) {
        return 0.8f; // Same element = reduced damage
    } else if ((nAttackerElement + 1) % 4 == nTargetElement) {
        return 1.2f; // Advantage = increased damage
    } else if ((nTargetElement + 1) % 4 == nAttackerElement) {
        return 0.8f; // Disadvantage = reduced damage
    }

    return 1.0f; // Neutral
}

/**
 * Calculate distance-based damage falloff
 */
float CalculateDistanceFalloff(float fDistance, float fMaxRange) {
    if (fDistance <= 0.0f || fMaxRange <= 0.0f) {
        return 1.0f;
    }

    if (fDistance >= fMaxRange) {
        return 0.0f;
    }

    // Linear falloff
    return 1.0f - (fDistance / fMaxRange);
}

/**
 * Check if attack is a critical hit
 */
bool IsCriticalHit(float fCriticalChance) {
    if (fCriticalChance <= 0.0f) {
        return false;
    }

    try {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        std::uniform_real_distribution<> dis(0.0, 100.0);

        return dis(gen) < fCriticalChance;

    } catch (const std::exception& e) {
        Logger::Error("DamageCalculationUtils::IsCriticalHit - Exception: %s", e.what());
        return false;
    }
}

/**
 * Clamp damage to valid range
 */
int ClampDamage(int nDamage) {
    return Utils::Clamp(nDamage, DamageCalculation::MIN_DAMAGE, DamageCalculation::MAX_DAMAGE);
}

} // namespace DamageCalculationUtils

} // namespace Combat
} // namespace NexusProtection
