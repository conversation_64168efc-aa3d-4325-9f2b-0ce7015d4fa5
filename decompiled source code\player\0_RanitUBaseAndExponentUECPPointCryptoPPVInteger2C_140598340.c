/*
 * Function: ??0?$_Ranit@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@_JPEBU12@AEBU12@@std@@QEAA@XZ
 * Address: 0x140598340
 */

std::_Iterator_base *__fastcall std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> const &>(std::_Iterator_base *a1)
{
  std::_Iterator_base *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  std::_Iterator_base::_Iterator_base(a1);
  return v2;
}
