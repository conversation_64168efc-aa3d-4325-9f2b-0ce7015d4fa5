/*
 * Function: ?pc_BackTowerRequest@CPlayer@@QEAAXK@Z
 * Address: 0x14009D050
 */

void __fastcall CPlayer::pc_BackTowerRequest(CPlayer *this, unsigned int dwTowerObjSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-58h]@1
  char v5; // [sp+20h] [bp-38h]@4
  _STORAGE_LIST::_db_con *pTowerItem; // [sp+28h] [bp-30h]@4
  int j; // [sp+30h] [bp-28h]@4
  char *v8; // [sp+38h] [bp-20h]@9
  __int16 v9; // [sp+40h] [bp-18h]@17
  unsigned __int16 v10; // [sp+44h] [bp-14h]@17
  CPlayer *v11; // [sp+60h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  pTowerItem = 0i64;
  for ( j = 0; j < 6; ++j )
  {
    if ( v11->m_pmTwr.m_List[j].m_pTowerItem && v11->m_pmTwr.m_List[j].m_pTowerObj->m_dwObjSerial == dwTowerObjSerial )
    {
      v8 = &v11->m_pmTwr.m_List[j].m_pTowerItem->m_bLoad;
      pTowerItem = _STORAGE_LIST::GetPtrFromSerial(
                     (_STORAGE_LIST *)&v11->m_Param.m_dbInven.m_nListNum,
                     *(_WORD *)(v8 + 17));
      if ( !pTowerItem )
      {
        v5 = 2;
        goto $RESULT_14;
      }
      if ( pTowerItem->m_byTableCode != 25 )
      {
        v5 = 3;
        goto $RESULT_14;
      }
      break;
    }
  }
  if ( !pTowerItem )
    v5 = 3;
$RESULT_14:
  v9 = -1;
  v10 = 0;
  if ( !v5 )
  {
    v10 = CPlayer::_TowerReturn(v11, pTowerItem);
    v9 = pTowerItem->m_wSerial;
  }
  CPlayer::SendMsg_BackTowerResult(v11, v5, v9, v10);
}
