/*
 * Function: ?pay_money@CMgrAvatorItemHistory@@QEAAXHPEADKKKK0@Z
 * Address: 0x140238120
 */

void __fastcall CMgrAvatorItemHistory::pay_money(CMgrAvatorItemHistory *this, int n, char *pszClause, unsigned int dwPayDalant, unsigned int dwPayGold, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-48h]@1
  unsigned int v11; // [sp+20h] [bp-28h]@4
  unsigned int v12; // [sp+28h] [bp-20h]@4
  unsigned int v13; // [sp+30h] [bp-18h]@4
  CMgrAvatorItemHistory *v14; // [sp+50h] [bp+8h]@1

  v14 = this;
  v8 = &v10;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v13 = dwNewGold;
  v12 = dwNewDalant;
  v11 = dwPayGold;
  sprintf(sData, "PAY: %s pay(D:%u G:%u) $D:%u $G:%u\r\n", pszClause);
  CMgrAvatorItemHistory::WriteFile(v14, pszFileName, sData);
}
