/*
 * Function: ?pc_PostItemGoldRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400C8F90
 */

void __fastcall CPlayer::pc_PostItemGoldRequest(CPlayer *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@10
  __int64 v5; // [sp+0h] [bp-B8h]@1
  bool bAdd[8]; // [sp+20h] [bp-98h]@20
  unsigned int dwGold; // [sp+28h] [bp-90h]@20
  char *pFileName; // [sp+30h] [bp-88h]@20
  CPostData *v9; // [sp+40h] [bp-78h]@4
  bool v10; // [sp+48h] [bp-70h]@6
  int nItemKey; // [sp+4Ch] [bp-6Ch]@6
  _STORAGE_LIST::_db_con *pItem; // [sp+50h] [bp-68h]@6
  char v13; // [sp+58h] [bp-60h]@6
  _STORAGE_LIST::_db_con v14; // [sp+68h] [bp-50h]@13
  CPlayer *v15; // [sp+C0h] [bp+8h]@1
  int nIndex; // [sp+C8h] [bp+10h]@1

  nIndex = dwIndex;
  v15 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = CPostStorage::GetPostDataFromInx(&v15->m_Param.m_PostStorage, dwIndex);
  if ( !v9 )
  {
    CPlayer::SendMsg_PostItemGold(v15, 11);
    return;
  }
  v10 = 0;
  nItemKey = -1;
  pItem = 0i64;
  v13 = 0;
  if ( _INVENKEY::IsFilled(&v9->m_Key)
    && _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v15->m_Param.m_dbInven.m_nListNum) == 255 )
  {
    CPlayer::SendMsg_PostItemGold(v15, 14);
    return;
  }
  if ( v9->m_dwGold )
  {
    v4 = CPlayer::GetMoney(v15, 1);
    if ( v9->m_dwGold + v4 > 0x7A120 )
    {
      CPlayer::SendMsg_PostItemGold(v15, 15);
      return;
    }
  }
  if ( _INVENKEY::IsFilled(&v9->m_Key) )
  {
    _STORAGE_LIST::_db_con::_db_con(&v14);
    v14.m_byTableCode = v9->m_Key.byTableCode;
    v14.m_wItemIndex = v9->m_Key.wItemIndex;
    v14.m_dwDur = v9->m_dwDur;
    v14.m_dwLv = v9->m_dwUpt;
    v14.m_lnUID = v9->m_lnUID;
    v14.m_wSerial = CPlayerDB::GetNewItemSerial(&v15->m_Param);
    pItem = CPlayer::Emb_AddStorage(v15, 0, (_STORAGE_LIST::_storage_con *)&v14.m_bLoad, 0, 1);
    if ( !pItem )
    {
      CPlayer::SendMsg_PostItemGold(v15, 2);
      return;
    }
    CPlayer::SendMsg_RewardAddItem(v15, pItem, 5);
    v10 = 1;
    nItemKey = _INVENKEY::CovDBKey(&v9->m_Key);
    v13 = 1;
  }
  if ( v9->m_dwGold )
  {
    CPlayer::AddGold(v15, v9->m_dwGold, 1);
    CPlayer::SendMsg_AlterMoneyInform(v15, 0);
    v13 = 1;
  }
  if ( pItem || v9->m_dwGold )
  {
    pFileName = v15->m_szItemHistoryFileName;
    dwGold = v9->m_dwGold;
    *(_QWORD *)bAdd = v9->m_dwDur;
    CMgrAvatorItemHistory::post_getpresent(
      &CPlayer::s_MgrItemHistory,
      v9->m_wszSendName,
      v9->m_dwPSSerial,
      pItem,
      *(unsigned __int64 *)bAdd,
      dwGold,
      v15->m_szItemHistoryFileName);
  }
  _INVENKEY::SetRelease(&v9->m_Key);
  v9->m_dwDur = 0i64;
  v9->m_dwUpt = 0xFFFFFFF;
  v9->m_lnUID = 0i64;
  v9->m_dwGold = 0;
  if ( v13 )
  {
    CPlayer::UpdatePostAddLog(v15, nIndex, v10, nItemKey);
    CPlayer::SendMsg_PostItemGold(v15, 0);
  }
  else
  {
    CPlayer::SendMsg_PostItemGold(v15, 12);
  }
}
