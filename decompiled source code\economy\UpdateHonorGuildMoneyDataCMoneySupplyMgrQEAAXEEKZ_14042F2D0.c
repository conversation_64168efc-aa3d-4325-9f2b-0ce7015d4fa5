/*
 * Function: ?UpdateHonorGuildMoneyData@CMoneySupplyMgr@@QEAAXEEK@Z
 * Address: 0x14042F2D0
 */

void __fastcall CMoneySupplyMgr::UpdateHonorGuildMoneyData(CMoneySupplyMgr *this, char byTradeType, char byRace, unsigned int nAmount)
{
  if ( byTradeType )
  {
    if ( byTradeType == 1 )
    {
      this->m_MS_data.dwAmount[6] += nAmount;
      if ( !byRace )
        ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][0];
      if ( byRace == 1 )
        ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][1];
      else
        ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][2];
    }
  }
  else
  {
    this->m_MS_data.dwAmount[3] += nAmount;
    if ( !byRace )
      ++this->m_MS_data.nHonorGuildRace[0][0];
    if ( byRace == 1 )
      ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][1];
    else
      ++this->m_MS_data.nHonorGuildRace[(unsigned __int8)byTradeType][2];
  }
}
