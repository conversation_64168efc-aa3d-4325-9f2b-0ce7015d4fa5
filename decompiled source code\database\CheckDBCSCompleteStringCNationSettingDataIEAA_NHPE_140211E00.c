/*
 * Function: ?CheckDBCSCompleteString@CNationSettingData@@IEAA_NHPEBDPEA_K@Z
 * Address: 0x140211E00
 */

char __fastcall CNationSettingData::CheckDBCSCompleteString(CNationSettingData *this, int nCodePage, const char *strData, unsigned __int64 *pCharacterCount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  size_t v8; // [sp+20h] [bp-28h]@8
  unsigned __int64 v9; // [sp+28h] [bp-20h]@8
  unsigned __int64 j; // [sp+30h] [bp-18h]@8
  int CodePage; // [sp+58h] [bp+10h]@1
  char *Str; // [sp+60h] [bp+18h]@1
  unsigned __int64 *v13; // [sp+68h] [bp+20h]@1

  v13 = pCharacterCount;
  Str = (char *)strData;
  CodePage = nCodePage;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pCharacterCount )
    *pCharacterCount = 0i64;
  if ( !strData )
    return 0;
  v8 = strlen_0(strData);
  v9 = 0i64;
  for ( j = 0i64; ; ++j )
  {
    if ( j >= v8 )
      return 1;
    ++v9;
    if ( v13 )
      *v13 = v9;
    if ( IsDBCSLeadByteEx(CodePage, Str[j]) )
      break;
LABEL_18:
    ;
  }
  if ( j != v8 - 1 )
  {
    ++j;
    goto LABEL_18;
  }
  --v9;
  if ( v13 )
    *v13 = v9;
  return 0;
}
