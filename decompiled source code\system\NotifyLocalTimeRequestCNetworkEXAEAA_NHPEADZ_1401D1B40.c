/*
 * Function: ?NotifyLocalTimeRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D1B40
 */

char __fastcall CNetworkEX::NotifyLocalTimeRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@5
  char result; // al@5
  unsigned __int16 v7; // ax@7
  unsigned __int16 v8; // ax@8
  __int64 v9; // [sp+0h] [bp-B8h]@1
  char Dst; // [sp+38h] [bp-80h]@4
  int v11; // [sp+39h] [bp-7Fh]@8
  int v12; // [sp+3Dh] [bp-7Bh]@8
  int v13; // [sp+41h] [bp-77h]@8
  int v14; // [sp+45h] [bp-73h]@8
  int v15; // [sp+49h] [bp-6Fh]@8
  int v16; // [sp+4Dh] [bp-6Bh]@8
  int v17; // [sp+51h] [bp-67h]@8
  int v18; // [sp+55h] [bp-63h]@8
  int v19; // [sp+59h] [bp-5Fh]@8
  char pbyType; // [sp+74h] [bp-44h]@4
  char v21; // [sp+75h] [bp-43h]@4
  CPlayer *v22; // [sp+88h] [bp-30h]@4
  __int64 _Time; // [sp+98h] [bp-20h]@6
  tm *v24; // [sp+A8h] [bp-10h]@6
  int v25; // [sp+C8h] [bp+10h]@1

  v25 = n;
  v3 = &v9;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(&Dst, 0, 0x25ui64);
  pbyType = 1;
  v21 = 27;
  v22 = &g_Player + v25;
  if ( v22->m_bOper )
  {
    time_5(&_Time);
    v24 = localtime_3(&_Time);
    if ( v24 )
    {
      v11 = v24->tm_sec;
      v12 = v24->tm_min;
      v13 = v24->tm_hour;
      v14 = v24->tm_mday;
      v15 = v24->tm_mon;
      v16 = v24->tm_year;
      v17 = v24->tm_wday;
      v18 = v24->tm_yday;
      v19 = v24->tm_isdst;
      v8 = _notify_local_time_result_zocl::size((_notify_local_time_result_zocl *)&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &pbyType, &Dst, v8);
      result = 1;
    }
    else
    {
      Dst = 2;
      v7 = _notify_local_time_result_zocl::size((_notify_local_time_result_zocl *)&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &pbyType, &Dst, v7);
      result = 1;
    }
  }
  else
  {
    Dst = 1;
    v5 = _notify_local_time_result_zocl::size((_notify_local_time_result_zocl *)&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, v22->m_ObjID.m_wIndex, &pbyType, &Dst, v5);
    result = 1;
  }
  return result;
}
