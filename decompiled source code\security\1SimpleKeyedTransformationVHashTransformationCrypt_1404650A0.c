/*
 * Function: ??1?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@UEAA@XZ
 * Address: 0x1404650A0
 */

void __fastcall CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>::~SimpleKeyedTransformation<CryptoPP::HashTransformation>(CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CryptoPP::SimpleKeyingInterface *v5; // [sp+28h] [bp-10h]@5
  CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation> *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  if ( v6 )
    v5 = (CryptoPP::SimpleKeyingInterface *)&v6->vfptr;
  else
    v5 = 0i64;
  CryptoPP::SimpleKeyingInterface::~SimpleKeyingInterface(v5);
  CryptoPP::HashTransformation::~HashTransformation((CryptoPP::HashTransformation *)&v6->vfptr);
}
