/*
 * Function: ?NewViewCircleObject@CPlayer@@QEAAXXZ
 * Address: 0x14005D4A0
 */

void __fastcall CPlayer::NewViewCircleObject(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  unsigned int v4; // eax@14
  unsigned int v5; // eax@15
  __int64 v6; // [sp+0h] [bp-98h]@1
  _sec_info *v7; // [sp+20h] [bp-78h]@4
  _pnt_rect pRect; // [sp+38h] [bp-60h]@4
  int j; // [sp+54h] [bp-44h]@4
  int k; // [sp+58h] [bp-40h]@6
  unsigned int dwSecIndex; // [sp+5Ch] [bp-3Ch]@9
  CObjectList *v12; // [sp+60h] [bp-38h]@9
  CObjectList *v13; // [sp+68h] [bp-30h]@10
  CPlayer *v14; // [sp+70h] [bp-28h]@12
  int nSecNum; // [sp+78h] [bp-20h]@4
  CGameObjectVtbl *v16; // [sp+80h] [bp-18h]@14
  CGameObjectVtbl *v17; // [sp+88h] [bp-10h]@15
  CPlayer *v18; // [sp+A0h] [bp+8h]@1

  v18 = this;
  v1 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = CMapData::GetSecInfo(v18->m_pCurMap);
  nSecNum = CGameObject::GetCurSecNum((CGameObject *)&v18->vfptr);
  v3 = CGameObject::GetUseSectorRange((CGameObject *)&v18->vfptr);
  CMapData::GetRectInRadius(v18->m_pCurMap, &pRect, v3, nSecNum);
  for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
  {
    for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
    {
      dwSecIndex = v7->m_nSecNumW * j + k;
      v12 = CMapData::GetSectorListObj(v18->m_pCurMap, v18->m_wMapLayerIndex, dwSecIndex);
      if ( v12 )
      {
        v13 = (CObjectList *)v12->m_Head.m_pNext;
        while ( (_object_list_point *)v13 != &v12->m_Tail )
        {
          v14 = (CPlayer *)v13->vfptr;
          v13 = (CObjectList *)v13->m_Head.m_pItem;
          if ( v14 != v18 )
          {
            if ( v14->m_bMove )
            {
              v5 = v18->m_ObjID.m_wIndex;
              v17 = v14->vfptr;
              (*(void (__fastcall **)(__int64, _QWORD))&v17->gap8[56])((__int64)v14, v5);
            }
            else
            {
              v4 = v18->m_ObjID.m_wIndex;
              v16 = v14->vfptr;
              (*(void (__fastcall **)(__int64, _QWORD))&v16->gap8[48])((__int64)v14, v4);
            }
          }
        }
      }
    }
  }
}
