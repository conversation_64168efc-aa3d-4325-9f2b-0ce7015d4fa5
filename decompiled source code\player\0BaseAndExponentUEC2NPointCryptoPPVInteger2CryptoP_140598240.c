/*
 * Function: ??0?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@AEBU01@@Z
 * Address: 0x140598240
 */

CryptoPP::EC2NPoint *__fastcall CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>(CryptoPP::EC2NPoint *a1, const struct CryptoPP::EC2NPoint *a2)
{
  CryptoPP::EC2NPoint *v3; // [sp+40h] [bp+8h]@1
  struct CryptoPP::EC2NPoint *v4; // [sp+48h] [bp+10h]@1

  v4 = (struct CryptoPP::EC2NPoint *)a2;
  v3 = a1;
  CryptoPP::EC2NPoint::EC2NPoint(a1, a2);
  CryptoPP::Integer::Integer((CryptoPP::Integer *)&v3[1], (const struct CryptoPP::Integer *)&v4[1]);
  return v3;
}
