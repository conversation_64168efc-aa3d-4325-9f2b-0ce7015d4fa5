/*
 * Function: ?Init@CUserDB@@QEAAXK@Z
 * Address: 0x140110030
 */

void __fastcall CUserDB::Init(CUserDB *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUserDB *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_idWorld.wIndex = dwIndex;
  CUserDB::ParamInit(v5);
  CMyTimer::BeginTimer(&v5->m_tmrCheckPlayMin, 0xEA60u);
}
