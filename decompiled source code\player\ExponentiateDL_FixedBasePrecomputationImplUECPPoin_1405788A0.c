/*
 * Function: ?Exponentiate@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@AEBVInteger@2@@Z
 * Address: 0x1405788A0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::Exponentiate(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  unsigned __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  char v10; // [sp+20h] [bp-108h]@1
  char v11; // [sp+48h] [bp-E0h]@1
  char *v12; // [sp+60h] [bp-C8h]@1
  char v13; // [sp+68h] [bp-C0h]@1
  char *v14; // [sp+80h] [bp-A8h]@1
  CryptoPP::ECPPoint v15; // [sp+88h] [bp-A0h]@1
  int v16; // [sp+E0h] [bp-48h]@1
  __int64 v17; // [sp+E8h] [bp-40h]@1
  __int64 v18; // [sp+F0h] [bp-38h]@1
  __int64 v19; // [sp+F8h] [bp-30h]@1
  __int64 v20; // [sp+100h] [bp-28h]@1
  __int64 v21; // [sp+108h] [bp-20h]@1
  __int64 v22; // [sp+110h] [bp-18h]@1
  __int64 v23; // [sp+118h] [bp-10h]@1
  __int64 v24; // [sp+130h] [bp+8h]@1
  __int64 v25; // [sp+138h] [bp+10h]@1
  __int64 v26; // [sp+140h] [bp+18h]@1
  __int64 v27; // [sp+148h] [bp+20h]@1

  v27 = a4;
  v26 = a3;
  v25 = a2;
  v24 = a1;
  v17 = -2i64;
  v16 = 0;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(&v10);
  v4 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::size((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v24 + 144));
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::reserve(
    &v10,
    v4);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::PrepareCascade(v24, v26, &v10, v27);
  v12 = &v11;
  v14 = &v13;
  LODWORD(v5) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::end(
                  &v10,
                  &v11);
  v18 = v5;
  v19 = v5;
  LODWORD(v6) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::begin(
                  &v10,
                  v14);
  v20 = v6;
  v21 = v6;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v26 + 24i64))(v26);
  LODWORD(v8) = CryptoPP::GeneralCascadeMultiplication<CryptoPP::ECPPoint,std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>>(
                  &v15,
                  v7,
                  v21,
                  v19);
  v22 = v8;
  v23 = v8;
  (*(void (__fastcall **)(__int64, __int64, __int64))(*(_QWORD *)v26 + 16i64))(v26, v25, v8);
  v16 |= 1u;
  CryptoPP::ECPPoint::~ECPPoint(&v15);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(&v10);
  return v25;
}
