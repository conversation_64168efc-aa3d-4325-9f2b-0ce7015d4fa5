/*
 * Function: ?post_getpresent@CMgrAvatorItemHistory@@QEAAXPEADKPEAU_db_con@_STORAGE_LIST@@_KK0@Z
 * Address: 0x14023E420
 */

void __fastcall CMgrAvatorItemHistory::post_getpresent(CMgrAvatorItemHistory *this, char *wszSendName, unsigned int dwPostSerial, _STORAGE_LIST::_db_con *Item, unsigned __int64 dwDur, unsigned int dwGold, char *pFileName)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char *v9; // rax@5
  __int64 v10; // [sp+0h] [bp-E8h]@1
  unsigned __int64 v11; // [sp+20h] [bp-C8h]@5
  char *v12; // [sp+28h] [bp-C0h]@5
  unsigned __int64 v13; // [sp+30h] [bp-B8h]@5
  char *v14; // [sp+38h] [bp-B0h]@7
  char szTran[2]; // [sp+48h] [bp-A0h]@4
  char v16; // [sp+4Ah] [bp-9Eh]@4
  char DstBuf[2]; // [sp+80h] [bp-68h]@4
  char v18; // [sp+82h] [bp-66h]@4
  _base_fld *v19; // [sp+C8h] [bp-20h]@5
  unsigned __int64 v20; // [sp+D8h] [bp-10h]@4
  CMgrAvatorItemHistory *v21; // [sp+F0h] [bp+8h]@1
  unsigned int v22; // [sp+100h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v23; // [sp+108h] [bp+20h]@1

  v23 = Item;
  v22 = dwPostSerial;
  v21 = this;
  v7 = &v10;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v20 = (unsigned __int64)&v10 ^ _security_cookie;
  *(_WORD *)szTran = 0;
  memset(&v16, 0, 0xFui64);
  *(_WORD *)DstBuf = 0;
  memset(&v18, 0, 0x3Eui64);
  sData[0] = 0;
  W2M(wszSendName, szTran, 0x11u);
  if ( v23 )
  {
    v19 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v23->m_byTableCode, v23->m_wItemIndex);
    v9 = DisplayItemUpgInfo(v23->m_byTableCode, v23->m_dwLv);
    v13 = v23->m_lnUID;
    v12 = v9;
    v11 = dwDur;
    sprintf_s(DstBuf, 0x40ui64, "%s_%I64u_@%s[%I64u]", v19->m_strCode);
  }
  else
  {
    sprintf_s(DstBuf, 0x40ui64, "NoItem");
  }
  v14 = v21->m_szCurTime;
  v13 = (unsigned __int64)v21->m_szCurDate;
  v12 = szTran;
  LODWORD(v11) = dwGold;
  sprintf(
    sData,
    "[PostSystem : Get Item & Gold In Inven] - PostSerial[%u] - Item[%s] - Gold[%u] - Sender[%s] - [%s %s]\r\n",
    v22,
    DstBuf);
  CMgrAvatorItemHistory::WriteFile(v21, pFileName, sData);
}
