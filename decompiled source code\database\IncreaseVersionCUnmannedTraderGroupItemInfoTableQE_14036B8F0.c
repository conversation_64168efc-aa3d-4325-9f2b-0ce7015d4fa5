/*
 * Function: ?IncreaseVersion@CUnmannedTraderGroupItemInfoTable@@QEAA_NEE@Z
 * Address: 0x14036B8F0
 */

char __fastcall CUnmannedTraderGroupItemInfoTable::IncreaseVersion(CUnmannedTraderGroupItemInfoTable *this, char byDivision, char byClass)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderGroupItemInfoTable *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  char v9; // [sp+40h] [bp+18h]@1

  v9 = byClass;
  v8 = byDivision;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CUnmannedTraderGroupVersionInfo::IncreaseVersion(&v7->m_kVerInfo, byDivision, byClass) )
  {
    result = 1;
  }
  else
  {
    CUnmannedTraderGroupItemInfoTable::Log(
      v7,
      "CUnmannedTraderGroupItemInfoTable::IncreaseVersion( BYTE byDivision, BYTE byClass )\r\n"
      "\t\tm_kVerInfo.IncreaseVersion( byDivision(%u), byClass(%u) ) Fail!\r\n",
      (unsigned __int8)v8,
      (unsigned __int8)v9);
    result = 0;
  }
  return result;
}
