/*
 * Function: ?pc_MoveToOwnStoneMapRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400CE6D0
 */

void __fastcall CPlayer::pc_MoveToOwnStoneMapRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // al@29
  __int64 v4; // [sp+0h] [bp-78h]@1
  float pos; // [sp+38h] [bp-40h]@4
  int v6; // [sp+3Ch] [bp-3Ch]@4
  int v7; // [sp+40h] [bp-38h]@4
  CMapData *pIntoMap; // [sp+58h] [bp-20h]@4
  _portal_dummy *v9; // [sp+60h] [bp-18h]@4
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pos = 0.0;
  v6 = 0;
  v7 = 0;
  pIntoMap = 0i64;
  v9 = 0i64;
  if ( v10->m_byStoneMapMoveInfo == 1 )
  {
    if ( CGameObject::GetCurSecNum((CGameObject *)&v10->vfptr) == -1 || v10->m_bMapLoading )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 2, 0, &pos);
    }
    else if ( CPlayer::IsRidingUnit(v10) )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 3, 0, &pos);
    }
    else if ( v10->m_byStandType == 1 )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 4, 0, &pos);
    }
    else if ( v10->m_pmTrd.bDTradeMode )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 5, 0, &pos);
    }
    else if ( CPlayer::IsSiegeMode(v10) )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 6, 0, &pos);
    }
    else if ( v10->m_bCorpse )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 7, 0, &pos);
    }
    else if ( v10->m_pCurMap == g_TransportShip[10162 * CPlayerDB::GetRaceCode(&v10->m_Param) + 2] )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 8, 0, &pos);
    }
    else if ( v10->m_bInGuildBattle )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 11, 0, &pos);
    }
    else if ( v10->m_pCurMap->m_pMapSet->m_nMapType )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 12, 0, &pos);
    }
    else if ( _effect_parameter::GetEff_State(&v10->m_EP, 20) )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 12, 0, &pos);
    }
    else if ( _effect_parameter::GetEff_State(&v10->m_EP, 28) )
    {
      CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 12, 0, &pos);
    }
    else
    {
      pIntoMap = CHolyStoneSystem::GetMapData(&g_HolySys);
      v3 = CPlayerDB::GetRaceCode(&v10->m_Param);
      v9 = CHolyStoneSystem::GetPortalDummy(&g_HolySys, v3);
      if ( v9 && pIntoMap )
      {
        if ( CMapData::GetRandPosInDummy(pIntoMap, v9->m_pDumPos, &pos, 1) )
        {
          CPlayer::OutOfMap(v10, pIntoMap, 0, 3, &pos);
          v10->m_byStoneMapMoveInfo = 2;
          CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 50, pIntoMap->m_pMapSet->m_dwIndex, &pos);
        }
        else
        {
          CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 10, 0, &pos);
        }
      }
      else
      {
        CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 9, 0, &pos);
      }
    }
  }
  else
  {
    CPlayer::SendMsg_MoveToOwnStoneMapResult(v10, 1, 0, &pos);
  }
}
