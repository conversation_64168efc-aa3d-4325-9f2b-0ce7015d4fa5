/*
 * Function: ?CascadeExponentiate@?$DL_FixedBasePrecomputationImpl@UECPPoint@CryptoPP@@@CryptoPP@@UEBA?AUECPPoint@2@AEBV?$DL_GroupPrecomputation@UECPPoint@CryptoPP@@@2@AEBVInteger@2@AEBV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@1@Z
 * Address: 0x140578AF0
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::CascadeExponentiate(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5, __int64 a6)
{
  unsigned __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  __int64 v9; // rax@1
  __int64 v10; // rax@1
  char v12; // [sp+28h] [bp-110h]@1
  char v13; // [sp+50h] [bp-E8h]@1
  char *v14; // [sp+68h] [bp-D0h]@1
  char v15; // [sp+70h] [bp-C8h]@1
  char *v16; // [sp+88h] [bp-B0h]@1
  CryptoPP::ECPPoint v17; // [sp+90h] [bp-A8h]@1
  int v18; // [sp+E8h] [bp-50h]@1
  __int64 v19; // [sp+F0h] [bp-48h]@1
  unsigned __int64 v20; // [sp+F8h] [bp-40h]@1
  __int64 v21; // [sp+100h] [bp-38h]@1
  __int64 v22; // [sp+108h] [bp-30h]@1
  __int64 v23; // [sp+110h] [bp-28h]@1
  __int64 v24; // [sp+118h] [bp-20h]@1
  __int64 v25; // [sp+120h] [bp-18h]@1
  __int64 v26; // [sp+128h] [bp-10h]@1
  __int64 v27; // [sp+140h] [bp+8h]@1
  __int64 v28; // [sp+148h] [bp+10h]@1
  __int64 v29; // [sp+150h] [bp+18h]@1
  __int64 v30; // [sp+158h] [bp+20h]@1

  v30 = a4;
  v29 = a3;
  v28 = a2;
  v27 = a1;
  v19 = -2i64;
  v18 = 0;
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(&v12);
  v20 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::size((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(v27 + 144));
  v6 = std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint>>::size((std::vector<CryptoPP::ECPPoint,std::allocator<CryptoPP::ECPPoint> > *)(a5 + 144));
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::reserve(
    &v12,
    v6 + v20);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::PrepareCascade(v27, v29, &v12, v30);
  CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::ECPPoint>::PrepareCascade(a5, v29, &v12, a6);
  v14 = &v13;
  v16 = &v15;
  LODWORD(v7) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::end(
                  &v12,
                  &v13);
  v21 = v7;
  v22 = v7;
  LODWORD(v8) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::begin(
                  &v12,
                  v16);
  v23 = v8;
  v24 = v8;
  LODWORD(v9) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v29 + 24i64))(v29);
  LODWORD(v10) = CryptoPP::GeneralCascadeMultiplication<CryptoPP::ECPPoint,std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>>(
                   &v17,
                   v9,
                   v24,
                   v22);
  v25 = v10;
  v26 = v10;
  (*(void (__fastcall **)(__int64, __int64, __int64))(*(_QWORD *)v29 + 16i64))(v29, v28, v10);
  v18 |= 1u;
  CryptoPP::ECPPoint::~ECPPoint(&v17);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(&v12);
  return v28;
}
