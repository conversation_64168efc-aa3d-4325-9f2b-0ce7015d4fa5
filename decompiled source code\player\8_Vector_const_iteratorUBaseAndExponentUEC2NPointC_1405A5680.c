/*
 * Function: ??8?$_Vector_const_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x1405A5680
 */

bool __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator==(__int64 a1, __int64 a2)
{
  return *(_QWORD *)(a1 + 16) == *(_QWORD *)(a2 + 16);
}
