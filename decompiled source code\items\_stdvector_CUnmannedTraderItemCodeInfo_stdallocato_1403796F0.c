/*
 * Function: _std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_n_::_1_::catch$0
 * Address: 0x1403796F0
 */

void __fastcall __noreturn std::vector_CUnmannedTraderItemCodeInfo_std::allocator_CUnmannedTraderItemCodeInfo___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Destroy(
    *(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > **)(a2 + 256),
    *(CUnmannedTraderItemCodeInfo **)(a2 + 144),
    *(CUnmannedTraderItemCodeInfo **)(a2 + 152));
  std::allocator<CUnmannedTraderItemCodeInfo>::deallocate(
    (std::allocator<CUnmannedTraderItemCodeInfo> *)(*(_QWORD *)(v2 + 256) + 8i64),
    *(CUnmannedTraderItemCodeInfo **)(v2 + 144),
    *(_QWORD *)(v2 + 136));
  CxxThrowException_0(0i64, 0i64);
}
