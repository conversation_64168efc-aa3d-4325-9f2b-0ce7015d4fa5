/*
 * Function: ?SendMsg_CouponLendResult@CCouponMgr@@QEAAXGPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1403FE230
 */

void __fastcall CCouponMgr::SendMsg_CouponLendResult(CCouponMgr *this, unsigned __int16 wIndx, _STORAGE_LIST::_db_con *pCoupon)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@4
  __int64 v6; // [sp+0h] [bp-88h]@1
  _add_lend_item_result_zocl v7; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v9; // [sp+65h] [bp-23h]@4
  unsigned __int16 v10; // [sp+98h] [bp+10h]@1

  v10 = wIndx;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.byTblCode = pCoupon->m_byTableCode;
  v7.wItemIdx = pCoupon->m_wItemIndex;
  v7.dwDur = pCoupon->m_dwDur;
  v7.dwUp = pCoupon->m_dwLv;
  v7.dwItemSerial = pCoupon->m_wSerial;
  v7.byCsMethod = pCoupon->m_byCsMethod;
  v7.dwT = pCoupon->m_dwT;
  pbyType = 7;
  v9 = 69;
  v5 = _add_lend_item_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10, &pbyType, &v7.byTblCode, v5);
}
