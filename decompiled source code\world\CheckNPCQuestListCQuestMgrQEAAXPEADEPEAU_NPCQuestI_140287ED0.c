/*
 * Function: ?CheckNPCQuestList@CQuestMgr@@QEAAXPEADEPEAU_NPCQuestIndexTempData@@@Z
 * Address: 0x140287ED0
 */

void __fastcall CQuestMgr::CheckNPCQuestList(CQuestMgr *this, char *pszEventCode, char byRaceCode, _NPCQuestIndexTempData *pQuestIndexData)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@22
  __int64 v7; // [sp+0h] [bp-68h]@1
  int v8; // [sp+20h] [bp-48h]@4
  _base_fld *v9; // [sp+28h] [bp-40h]@4
  int n; // [sp+30h] [bp-38h]@5
  int j; // [sp+34h] [bp-34h]@5
  char *v12; // [sp+38h] [bp-30h]@10
  char v13; // [sp+40h] [bp-28h]@12
  int k; // [sp+44h] [bp-24h]@12
  _happen_event_condition_node *pCond; // [sp+48h] [bp-20h]@14
  _base_fld *v16; // [sp+50h] [bp-18h]@20
  CQuestMgr *v17; // [sp+70h] [bp+8h]@1
  const char *szRecordCode; // [sp+78h] [bp+10h]@1
  char v19; // [sp+80h] [bp+18h]@1
  _NPCQuestIndexTempData *v20; // [sp+88h] [bp+20h]@1

  v20 = pQuestIndexData;
  v19 = byRaceCode;
  szRecordCode = pszEventCode;
  v17 = this;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = CRecordData::GetRecord(CQuestMgr::s_tblQuestHappenEvent + 1, pszEventCode);
  if ( v9 )
  {
    n = v9->m_dwIndex;
    for ( j = 0; ; ++j )
    {
      if ( j >= 30 )
        return;
      v9 = CRecordData::GetRecord(CQuestMgr::s_tblQuestHappenEvent + 1, n);
      if ( !v9 || strcmp_0(szRecordCode, v9->m_strCode) )
        return;
      v12 = &v9[1].m_strCode[704 * (unsigned __int8)v19];
      if ( !*(_DWORD *)v12 )
      {
        ++n;
        continue;
      }
      v13 = 1;
      for ( k = 0; k < 5; ++k )
      {
        pCond = (_happen_event_condition_node *)&v12[72 * k + 24];
        if ( pCond->m_nCondType == -1 )
          break;
        if ( !CQuestMgr::_CheckCondition(v17, pCond) )
        {
          v13 = 0;
          break;
        }
      }
      if ( !v13 )
      {
        ++n;
        continue;
      }
      v16 = CRecordData::GetRecord(CQuestMgr::s_tblQuest, v12 + 384);
      if ( !v16
        || v16[1].m_dwIndex != -1
        && (v6 = CPlayerDB::GetLevel(&v17->m_pMaster->m_Param), (signed int)v16[1].m_dwIndex < v6) )
      {
        ++n;
        continue;
      }
      if ( !CQuestMgr::IsCompleteNpcQuest(v17, v16->m_strCode, *(_DWORD *)&v16[1].m_strCode[4])
        && !CQuestMgr::IsProcNpcQuest(v17, v16->m_strCode) )
      {
        if ( *(_DWORD *)&v16[1].m_strCode[4] == 1 )
        {
          if ( !CQuestMgr::IsProcLinkNpcQuest(v17, v16->m_strCode, *(_DWORD *)&v16[27].m_strCode[24]) )
          {
            ++n;
            continue;
          }
          if ( !CQuestMgr::IsPossibleRepeatNpcQuest(v17, v16->m_strCode, *(_DWORD *)&v16[27].m_strCode[24]) )
          {
            ++n;
            continue;
          }
        }
        v20->IndexData[v8].dwQuestIndex = v16->m_dwIndex;
        v20->IndexData[v8++].dwQuestHappenIndex = n;
        v20->nQuestNum = v8;
      }
      ++n;
    }
  }
}
