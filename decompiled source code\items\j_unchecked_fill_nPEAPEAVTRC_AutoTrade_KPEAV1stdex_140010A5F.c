/*
 * Function: j_??$unchecked_fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@@stdext@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@@Z
 * Address: 0x140010A5F
 */

void __fastcall stdext::unchecked_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val)
{
  stdext::unchecked_fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *>(_First, _Count, _Val);
}
