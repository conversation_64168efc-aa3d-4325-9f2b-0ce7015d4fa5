#pragma once

#include <cstdint>
#include <array>
#include <string>
#include <chrono>

namespace NexusProtection::Economy {

    // Forward declarations
    class CMoneySupplyMgr;
    class EconomySystem;
    struct MoneySupplyData;

    // Constants
    constexpr size_t MAX_RACES = 3;
    constexpr size_t MAX_LEVELS = 4;  // 30, 40, 50, 60
    constexpr size_t MAX_MONEY_TYPES = 8;
    constexpr size_t MAX_TRADE_TYPES = 4;
    constexpr size_t MAX_HONOR_GUILD_TYPES = 2;

    // Enumerations
    enum class RaceType : uint8_t {
        Bellato = 0,
        Cora = 1,
        Accretia = 2,
        Count = 3
    };

    enum class LevelRange : uint8_t {
        Level30 = 0,
        Level40 = 1,
        Level50 = 2,
        Level60 = 3,
        Count = 4
    };

    enum class MoneyType : uint8_t {
        Sell = 0,
        QuestReward = 1,
        GateReward = 2,
        HonorGuildDalant = 3,
        Buy = 4,
        Fee = 5,
        HonorGuildGold = 6,
        BuyUnit = 7,
        Count = 8
    };

    enum class TradeType : uint8_t {
        Dalant = 0,
        Gold = 1,
        Count = 2
    };

    enum class CurrencyType : uint8_t {
        Gold = 0,
        Dalant = 1
    };

    // Data structures
    struct LevelStatistics {
        std::array<int32_t, static_cast<size_t>(LevelRange::Count)> levelCounts{};
        
        void Reset() noexcept {
            levelCounts.fill(0);
        }
        
        int32_t GetLevelCount(LevelRange level) const noexcept {
            return levelCounts[static_cast<size_t>(level)];
        }
        
        void IncrementLevel(int32_t level) noexcept {
            switch (level) {
                case 30: ++levelCounts[static_cast<size_t>(LevelRange::Level30)]; break;
                case 40: ++levelCounts[static_cast<size_t>(LevelRange::Level40)]; break;
                case 50: ++levelCounts[static_cast<size_t>(LevelRange::Level50)]; break;
                case 60: ++levelCounts[static_cast<size_t>(LevelRange::Level60)]; break;
                default: break;
            }
        }
    };

    struct RaceStatistics {
        std::array<int32_t, static_cast<size_t>(RaceType::Count)> raceCounts{};
        
        void Reset() noexcept {
            raceCounts.fill(0);
        }
        
        int32_t GetRaceCount(RaceType race) const noexcept {
            return raceCounts[static_cast<size_t>(race)];
        }
        
        void IncrementRace(RaceType race) noexcept {
            if (race < RaceType::Count) {
                ++raceCounts[static_cast<size_t>(race)];
            }
        }
    };

    struct MoneySupplyStatistics {
        LevelStatistics levelStats;
        RaceStatistics raceStats;
        
        void Reset() noexcept {
            levelStats.Reset();
            raceStats.Reset();
        }
    };

} // namespace NexusProtection::Economy
