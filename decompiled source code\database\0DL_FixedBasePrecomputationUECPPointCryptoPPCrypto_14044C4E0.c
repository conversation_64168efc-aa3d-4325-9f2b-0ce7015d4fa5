/*
 * Function: ??0?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x14044C4E0
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>(CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *this)
{
  this->vfptr = (CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>Vtbl *)&CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::`vftable';
}
