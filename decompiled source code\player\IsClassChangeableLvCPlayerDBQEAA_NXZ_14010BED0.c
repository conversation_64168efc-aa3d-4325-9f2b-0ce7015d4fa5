/*
 * Function: ?IsClassChangeableLv@CPlayerDB@@QEAA_NXZ
 * Address: 0x14010BED0
 */

bool __fastcall CPlayerDB::IsClassChangeableLv(CPlayerDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v4; // eax@6
  __int64 v5; // [sp+0h] [bp-38h]@1
  _class_fld *v6; // [sp+20h] [bp-18h]@6
  CPlayerDB *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7->m_pClassData )
  {
    v6 = v7->m_pClassData;
    v4 = CPlayerDB::GetLevel(v7);
    result = v6->m_nUpGradeLv <= v4;
  }
  else
  {
    result = 0;
  }
  return result;
}
