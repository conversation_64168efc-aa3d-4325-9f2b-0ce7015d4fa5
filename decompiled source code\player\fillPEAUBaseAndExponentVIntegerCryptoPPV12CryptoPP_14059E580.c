/*
 * Function: ??$fill@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@U12@@std@@YAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@0AEBU12@@Z
 * Address: 0x14059E580
 */

int std::fill<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>()
{
  return std::_Fill<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>();
}
