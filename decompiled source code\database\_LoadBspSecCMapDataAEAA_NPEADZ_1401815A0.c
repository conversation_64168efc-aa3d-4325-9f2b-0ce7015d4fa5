/*
 * Function: ?_LoadBspSec@CMapData@@AEAA_NPEAD@Z
 * Address: 0x1401815A0
 */

char __fastcall CMapData::_LoadBspSec(CMapData *this, char *pszMapCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-D8h]@1
  char Dest; // [sp+30h] [bp-A8h]@6
  int j; // [sp+B4h] [bp-24h]@6
  unsigned __int64 v8; // [sp+C0h] [bp-18h]@4
  CMapData *v9; // [sp+E0h] [bp+8h]@1
  const char *Str; // [sp+E8h] [bp+10h]@1

  Str = pszMapCode;
  v9 = this;
  v2 = &v5;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( CLevel::IsLoadedBsp(&v9->m_Level) )
  {
    MyMessageBox("CMapData Error", "m_Level.IsLoadedBsp() == false");
    result = 0;
  }
  else
  {
    sprintf(&Dest, ".\\Map\\%s\\%s.bsp", Str, Str);
    CLevel::LoadLevel(&v9->m_Level, &Dest);
    sprintf(v9->m_BspInfo.m_szBspName, "%s", Str);
    v9->m_BspInfo.m_szBspName[strlen_0(Str)] = 0;
    v9->m_BspInfo.m_nLeafNum = v9->m_Level.mBsp->mLeafNum;
    for ( j = 0; j < 3; ++j )
    {
      v9->m_BspInfo.m_nMapMaxSize[j] = v9->m_Level.mBsp->mNode[1].bb_max[j];
      v9->m_BspInfo.m_nMapMinSize[j] = v9->m_Level.mBsp->mNode[1].bb_min[j];
      v9->m_BspInfo.m_nMapSize[j] = v9->m_BspInfo.m_nMapMaxSize[j] - v9->m_BspInfo.m_nMapMinSize[j];
    }
    v9->m_SecInfo.m_nSecNumW = v9->m_BspInfo.m_nMapSize[0] / 100 + 1;
    v9->m_SecInfo.m_nSecNumH = v9->m_BspInfo.m_nMapSize[2] / 100 + 1;
    v9->m_SecInfo.m_nSecNum = v9->m_SecInfo.m_nSecNumH * v9->m_SecInfo.m_nSecNumW;
    result = 1;
  }
  return result;
}
