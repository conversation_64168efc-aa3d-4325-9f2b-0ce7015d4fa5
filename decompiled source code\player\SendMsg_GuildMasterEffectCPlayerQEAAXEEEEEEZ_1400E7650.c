/*
 * Function: ?SendMsg_GuildMasterEffect@CPlayer@@QEAAXEEEEEE@Z
 * Address: 0x1400E7650
 */

void __fastcall CPlayer::SendMsg_GuildMasterEffect(CPlayer *this, char byState, char by<PERSON>rade, char by<PERSON>ff<PERSON><PERSON><PERSON><PERSON><PERSON>, char by<PERSON>ff<PERSON>ub<PERSON><PERSON><PERSON>, char by<PERSON>ff<PERSON>dd<PERSON><PERSON><PERSON>, char byEffAddDefence)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+34h] [bp-54h]@4
  char v11; // [sp+35h] [bp-53h]@4
  char v12; // [sp+36h] [bp-52h]@4
  char v13; // [sp+37h] [bp-51h]@4
  char v14; // [sp+38h] [bp-50h]@4
  char v15; // [sp+39h] [bp-4Fh]@4
  char pbyType; // [sp+54h] [bp-34h]@4
  char v17; // [sp+55h] [bp-33h]@4
  unsigned __int64 v18; // [sp+70h] [bp-18h]@4
  CPlayer *v19; // [sp+90h] [bp+8h]@1

  v19 = this;
  v7 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v18 = (unsigned __int64)&v9 ^ _security_cookie;
  szMsg = byState;
  v11 = byGrade;
  v12 = byEffSubAttack;
  v13 = byEffSubDefence;
  v14 = byEffAddAttack;
  v15 = byEffAddDefence;
  pbyType = 27;
  v17 = 120;
  CNetProcess::LoadSendMsg(unk_1414F2088, v19->m_ObjID.m_wIndex, &pbyType, &szMsg, 6u);
}
