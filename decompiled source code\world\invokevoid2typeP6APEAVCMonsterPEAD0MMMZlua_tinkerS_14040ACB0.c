/*
 * Function: ?invoke@?$void2type@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@SAP6APEAVCMonster@@PEAD0MMM@ZPEAX@Z
 * Address: 0x14040ACB0
 */

CMonster *(__cdecl *__fastcall lua_tinker::void2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(lua_tinker::void2type<CMonster * (__cdecl*)(char *,char *,float,float,float)> *this, void *ptr))(char *, char *, float, float, float)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  lua_tinker::void2ptr<CMonster * __cdecl(char *,char *,float,float,float)> *v6; // [sp+30h] [bp+8h]@1

  v6 = (lua_tinker::void2ptr<CMonster * __cdecl(char *,char *,float,float,float)> *)this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return lua_tinker::void2ptr<CMonster * (char *,char *,float,float,float)>::invoke(v6, ptr);
}
