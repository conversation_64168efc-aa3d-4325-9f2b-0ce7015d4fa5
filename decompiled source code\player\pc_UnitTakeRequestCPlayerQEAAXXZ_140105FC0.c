/*
 * Function: ?pc_UnitTakeRequest@CPlayer@@QEAAXXZ
 * Address: 0x140105FC0
 */

void __usercall CPlayer::pc_UnitTakeRequest(CPlayer *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char v5; // [sp+20h] [bp-58h]@4
  int j; // [sp+24h] [bp-54h]@8
  char v7; // [sp+28h] [bp-50h]@8
  __int64 v8; // [sp+30h] [bp-48h]@10
  float pNewPos; // [sp+48h] [bp-30h]@30
  float v10; // [sp+4Ch] [bp-2Ch]@30
  float v11; // [sp+50h] [bp-28h]@30
  CPlayer *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = 0;
  CPlayer::BreakCloakBooster(v12);
  if ( v12->m_bInGuildBattle && v12->m_bTakeGravityStone )
  {
    v5 = 31;
  }
  else
  {
    if ( v12->m_bFreeSFByClass )
      goto LABEL_34;
    v7 = 0;
    for ( j = 0; j < 4; ++j )
    {
      v8 = (__int64)*v12->m_Param.m_ppHistoryEffect[j];
      if ( !v8 )
        break;
      if ( *(_DWORD *)(v8 + 1436) )
      {
        v7 = 1;
        break;
      }
    }
    if ( v7 )
    {
LABEL_34:
      if ( v12->m_pSiegeItem )
      {
        v5 = 28;
      }
      else if ( v12->m_pParkingUnit && v12->m_pUsingUnit )
      {
        if ( v12->m_pParkingUnit->m_dwOwnerSerial == v12->m_dwObjSerial )
        {
          GetSqrt(v12->m_fCurPos, v12->m_pParkingUnit->m_fCurPos);
          if ( a2 <= 50.0 )
          {
            if ( v12->m_bAfterEffect )
            {
              v5 = 32;
            }
            else if ( ((int (__fastcall *)(CParkingUnit *))v12->m_pParkingUnit->vfptr->GetHP)(v12->m_pParkingUnit) <= 0 )
            {
              v5 = 35;
            }
          }
          else
          {
            v5 = 20;
          }
        }
        else
        {
          v5 = 2;
        }
      }
      else
      {
        v5 = 2;
      }
    }
    else
    {
      v5 = 28;
    }
  }
  if ( !v5 )
  {
    pNewPos = v12->m_pParkingUnit->m_fCurPos[0];
    v10 = v12->m_pParkingUnit->m_fCurPos[1];
    v11 = v12->m_pParkingUnit->m_fCurPos[2];
    CMapData::GetRandPosInRange(v12->m_pCurMap, v12->m_pParkingUnit->m_fCurPos, 10, &pNewPos);
    memcpy_0(v12->m_fOldPos, v12->m_fCurPos, 0xCui64);
    memcpy_0(v12->m_fCurPos, &pNewPos, 0xCui64);
    CPlayer::Emb_RidindUnit(v12, 1, 0i64);
    CPlayer::SendMsg_AlterUnitHPInform(v12, v12->m_pUsingUnit->bySlotIndex, v12->m_pUsingUnit->dwGauge);
  }
  CPlayer::SendMsg_UnitTakeResult(v12, v5);
}
