/*
 * Function: ??E?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x140371540
 */

std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__fastcall std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator++(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this)
{
  ++this->_Myptr;
  return this;
}
