/*
 * Function: ?Check_EquipItem@CSetItemEffect@@AEAAEPEAU_AVATOR_DATA@@PEAU_SetItemEff_fld@@@Z
 * Address: 0x1402E2510
 */

char __fastcall CSetItemEffect::Check_EquipItem(CSetItemEffect *this, _AVATOR_DATA *pData, _SetItemEff_fld *pSetFld)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char v6; // al@8
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@8
  CSetItemEffect *v9; // [sp+40h] [bp+8h]@1
  _AVATOR_DATA *pDataa; // [sp+48h] [bp+10h]@1
  _SetItemEff_fld *pSetFlda; // [sp+50h] [bp+18h]@1

  pSetFlda = pSetFld;
  pDataa = pData;
  v9 = this;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pData )
  {
    if ( pSetFld )
    {
      v8 = 0;
      v8 = CSetItemEffect::Check_Base_EquipItem(v9, pData, pSetFld);
      v6 = CSetItemEffect::Check_Other_EquipItem(v9, pDataa, pSetFlda);
      result = v6 + v8;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
