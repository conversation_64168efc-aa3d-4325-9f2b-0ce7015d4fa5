/*
 * Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor$3
 * Address: 0x1403AF100
 */

void __fastcall std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *)(a2 + 96));
}
