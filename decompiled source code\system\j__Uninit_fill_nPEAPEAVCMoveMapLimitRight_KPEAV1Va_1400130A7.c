/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCMoveMapLimitRight@@_KPEAV1@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@YAXPEAPEAVCMoveMapLimitRight@@_KAEBQEAV1@AEAV?$allocator@PEAVCMoveMapLimitRight@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400130A7
 */

void __fastcall std::_Uninit_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(CMoveMapLimitRight **_First, unsigned __int64 _Count, CMoveMapLimitRight *const *_Val, std::allocator<CMoveMapLimitRight *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CMoveMapLimitRight * *,unsigned __int64,CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
