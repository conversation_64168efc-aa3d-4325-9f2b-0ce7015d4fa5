/*
 * Function: ?Copy@CUnmannedTraderDivisionInfo@@IEAAAEBV1@AEBV1@@Z
 * Address: 0x14036E470
 */

CUnmannedTraderDivisionInfo *__fastcall CUnmannedTraderDivisionInfo::Copy(CUnmannedTraderDivisionInfo *this, CUnmannedTraderDivisionInfo *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char v6; // [sp+20h] [bp-98h]@5
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *result; // [sp+38h] [bp-80h]@5
  char v8; // [sp+40h] [bp-78h]@5
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v9; // [sp+58h] [bp-60h]@5
  char v10; // [sp+60h] [bp-58h]@5
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v11; // [sp+78h] [bp-40h]@5
  __int64 v12; // [sp+80h] [bp-38h]@4
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v13; // [sp+88h] [bp-30h]@5
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v14; // [sp+90h] [bp-28h]@5
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v15; // [sp+98h] [bp-20h]@5
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v16; // [sp+A0h] [bp-18h]@5
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *v17; // [sp+A8h] [bp-10h]@5
  CUnmannedTraderDivisionInfo *v18; // [sp+C0h] [bp+8h]@1
  CUnmannedTraderDivisionInfo *v19; // [sp+C8h] [bp+10h]@1

  v19 = lhs;
  v18 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v18->m_dwID = lhs->m_dwID;
  strcpy_0(v18->m_szName, lhs->m_szName);
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::clear(&v18->m_vecClass);
  if ( !std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::empty(&v19->m_vecClass) )
  {
    result = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v6;
    v9 = (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v8;
    v11 = (std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v10;
    v13 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::end(
            &v19->m_vecClass,
            (std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)&v6);
    v14 = v13;
    v15 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(
            &v19->m_vecClass,
            v9);
    v16 = v15;
    v17 = std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::begin(
            &v18->m_vecClass,
            v11);
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::insert<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>>(
      &v18->m_vecClass,
      v17,
      v16,
      v14);
  }
  return v18;
}
