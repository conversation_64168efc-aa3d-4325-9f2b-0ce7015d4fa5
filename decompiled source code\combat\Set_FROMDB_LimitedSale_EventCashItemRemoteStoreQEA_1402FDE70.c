/*
 * Function: ?Set_FROMDB_LimitedSale_Event@CashItemRemoteStore@@QEAAXPEAU_db_cash_limited_sale@@@Z
 * Address: 0x1402FDE70
 */

void __fastcall CashItemRemoteStore::Set_FROMDB_LimitedSale_Event(CashItemRemoteStore *this, _db_cash_limited_sale *Sheet)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-128h]@1
  _db_cash_limited_sale::_db_cash_limited_info *v5; // [sp+8h] [bp-120h]@6
  char v6; // [sp+10h] [bp-118h]@7
  CashItemRemoteStore *v7; // [sp+130h] [bp+8h]@1

  v7 = this;
  v2 = &j;
  for ( i = 70i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v7->m_lim_event_New.DCK = Sheet->byDck;
  v7->m_lim_event_New.m_byEventNum = Sheet->byLimited_sale_num;
  for ( j = 0; j < v7->m_lim_event_New.m_byEventNum; ++j )
  {
    v5 = &Sheet->List[j];
    v7->m_lim_event_New.m_EventItemInfo[j].byTableCode = BYTE1(Sheet->List[j].nLimcode);
    v7->m_lim_event_New.m_EventItemInfo[j].dwIndex = HIWORD(v5->nLimcode);
    v7->m_lim_event_New.m_EventItemInfo[j].wCount = Sheet->List[j].nLimcount;
  }
  qmemcpy(&v6, &v7->m_lim_event_New, 0xF4ui64);
  qmemcpy(&v7->m_lim_event_Old, &v6, sizeof(v7->m_lim_event_Old));
}
