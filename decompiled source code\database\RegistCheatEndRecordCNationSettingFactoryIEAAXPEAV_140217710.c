/*
 * Function: ?RegistCheatEndRecord@CNationSettingFactory@@IEAAXPEAVCNationSettingData@@@Z
 * Address: 0x140217710
 */

void __fastcall CNationSettingFactory::RegistCheatEndRecord(CNationSettingFactory *this, CNationSettingData *pkData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-118h]@1
  CHEAT_COMMAND _Val; // [sp+28h] [bp-F0h]@4
  char v6; // [sp+58h] [bp-C0h]@4
  char v7; // [sp+78h] [bp-A0h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *result; // [sp+90h] [bp-88h]@4
  char v9; // [sp+98h] [bp-80h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v10; // [sp+B0h] [bp-68h]@4
  char v11; // [sp+C0h] [bp-58h]@4
  __int64 v12; // [sp+E0h] [bp-38h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v13; // [sp+E8h] [bp-30h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v14; // [sp+F0h] [bp-28h]@4
  std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *v15; // [sp+F8h] [bp-20h]@4
  CNationSettingData *v16; // [sp+128h] [bp+10h]@1

  v16 = pkData;
  v2 = &v4;
  for ( i = 66i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  _Val.pwszCommand = 0i64;
  _Val.uiCmdLen = 0;
  _Val.fn = 0i64;
  _Val.nUseDegree = 0;
  _Val.nMgrDegree = 0;
  std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::push_back(&pkData->m_vecCheatData, &_Val);
  memset(&v6, 0, 0x20ui64);
  result = (std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v7;
  v10 = (std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v9;
  qmemcpy(&v11, &v6, 0x20ui64);
  v13 = std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::end(
          &v16->m_vecCheatData,
          (std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND> > *)&v7);
  v14 = v13;
  v15 = std::vector<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>::begin(&v16->m_vecCheatData, v10);
  std::sort<std::_Vector_iterator<CHEAT_COMMAND,std::allocator<CHEAT_COMMAND>>,CHEAT_COMMAND>(
    v15,
    v14,
    (CHEAT_COMMAND *)&v11);
}
