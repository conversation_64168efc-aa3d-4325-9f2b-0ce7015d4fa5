/*
 * Function: j_??0?$_Vector_val@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderRegistItemInfo@@@1@@Z
 * Address: 0x140012616
 */

void __fastcall std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, std::allocator<CUnmannedTraderRegistItemInfo> _Al)
{
  std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    this,
    _Al);
}
