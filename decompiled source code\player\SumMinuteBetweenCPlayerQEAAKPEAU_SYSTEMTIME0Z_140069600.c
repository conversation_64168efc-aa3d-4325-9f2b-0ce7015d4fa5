/*
 * Function: ?SumMinuteBetween@CPlayer@@QEAAKPEAU_SYSTEMTIME@@0@Z
 * Address: 0x140069600
 */

__int64 __fastcall CPlayer::SumMinuteBetween(CPlayer *this, _SYSTEMTIME *tmLast, _SYSTEMTIME *tmLocal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // eax@11
  unsigned int v6; // eax@11
  unsigned int v7; // eax@13
  unsigned int v8; // eax@15
  __int64 v10; // [sp+0h] [bp-898h]@1
  unsigned __int16 v11; // [sp+20h] [bp-878h]@4
  unsigned __int16 v12; // [sp+24h] [bp-874h]@4
  unsigned int v13; // [sp+28h] [bp-870h]@6
  _SYSTEMTIME v14[126]; // [sp+40h] [bp-858h]@7
  _SYSTEMTIME tm; // [sp+828h] [bp-70h]@8
  int j; // [sp+844h] [bp-54h]@8
  int k; // [sp+848h] [bp-50h]@11
  char v18; // [sp+850h] [bp-48h]@7
  char v19; // [sp+860h] [bp-38h]@7
  unsigned int v20; // [sp+870h] [bp-28h]@11
  unsigned int v21; // [sp+874h] [bp-24h]@15
  CPlayer *v22; // [sp+8A0h] [bp+8h]@1
  _SYSTEMTIME *v23; // [sp+8A8h] [bp+10h]@1
  _SYSTEMTIME *v24; // [sp+8B0h] [bp+18h]@1

  v24 = tmLocal;
  v23 = tmLast;
  v22 = this;
  v3 = &v10;
  for ( i = 546i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = tmLocal->wYear - tmLast->wYear;
  v12 = 0;
  if ( (signed int)v11 > 0 )
    v12 = v11 - 1;
  v13 = 0;
  if ( (signed int)v12 <= 1 )
  {
    v21 = CPlayer::SumMinuteOne(v22, tmLocal);
    v8 = CPlayer::SumMinuteOne(v22, v23);
    v13 = v21 - v8;
  }
  else
  {
    qmemcpy(&v19, tmLast, 0x10ui64);
    qmemcpy(v14, &v19, 0x10ui64);
    qmemcpy(&v18, tmLocal, 0x10ui64);
    qmemcpy(&v14[v11], &v18, sizeof(v14[v11]));
    if ( (signed int)v12 > 0 )
    {
      tm.wMonth = 12;
      tm.wDay = 31;
      tm.wHour = 23;
      tm.wMinute = 59;
      for ( j = 0; j < v12; ++j )
      {
        tm.wYear = tmLast->wYear + j + 1;
        qmemcpy(&v14[j + 1], &tm, sizeof(v14[j + 1]));
      }
    }
    tm.wYear = tmLast->wYear;
    tm.wMonth = 12;
    tm.wDay = 31;
    tm.wHour = 23;
    tm.wMinute = 59;
    v20 = CPlayer::SumMinuteOne(v22, &tm);
    v5 = CPlayer::SumMinuteOne(v22, v23);
    v13 = v20 - v5;
    v6 = CPlayer::SumMinuteOne(v22, v24);
    v13 += v6;
    for ( k = 1; k < v12; ++k )
    {
      v7 = CPlayer::SumMinuteOne(v22, &v14[k]);
      v13 += v7;
    }
  }
  return v13;
}
