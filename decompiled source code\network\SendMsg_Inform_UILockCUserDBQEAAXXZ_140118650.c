/*
 * Function: ?SendMsg_Inform_UILock@CUserDB@@QEAAXXZ
 * Address: 0x140118650
 */

void __fastcall CUserDB::SendMsg_Inform_UILock(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v5; // [sp+35h] [bp-43h]@4
  char v6; // [sp+36h] [bp-42h]@4
  char v7; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CUserDB *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  szMsg = v10->m_byUILock;
  v5 = v10->m_byUILock_FailCnt;
  v6 = v10->m_byUILock_HintIndex;
  v7 = v10->m_byUILockFindPassFailCount;
  pbyType = 13;
  v9 = -123;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_idWorld.wIndex, &pbyType, &szMsg, 4u);
}
