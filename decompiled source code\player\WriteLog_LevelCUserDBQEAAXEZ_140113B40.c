/*
 * Function: ?WriteLog_Level@CUserDB@@QEAAXE@Z
 * Address: 0x140113B40
 */

void __fastcall CUserDB::WriteLog_Level(CUserDB *this, char byLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  char *pQryData; // [sp+20h] [bp-48h]@5
  int nSize; // [sp+28h] [bp-40h]@5
  _log_sheet_lv v7; // [sp+38h] [bp-30h]@5
  CUserDB *v8; // [sp+70h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bField )
  {
    v7.dwAvatorSerial = v8->m_dwSerial;
    v7.byLv = byLv;
    v7.dwTotalPlayMin = v8->m_AvatorData.dbAvator.m_dwTotalPlayMin;
    nSize = _log_sheet_lv::size(&v7);
    pQryData = (char *)&v7;
    CMainThread::PushDQSData(&g_Main, v8->m_dwAccountSerial, &v8->m_idWorld, 7, (char *)&v7, nSize);
  }
}
