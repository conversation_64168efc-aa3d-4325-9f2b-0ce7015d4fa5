/*
 * Function: j_?combine_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAEE0KKPEAD@Z
 * Address: 0x14000AFB5
 */

void __fastcall CMgrAvatorItemHistory::combine_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pMaterial, char *pbyMtrNum, char byMaterialNum, _STORAGE_LIST::_db_con *pMakeItem, unsigned int dwFee, unsigned int dwLeftDalant, char *pszFileName)
{
  CMgrAvatorItemHistory::combine_item(
    this,
    n,
    pMaterial,
    pbyMtrNum,
    byMaterialNum,
    pMakeItem,
    dwFee,
    dwLeftDalant,
    pszFileName);
}
