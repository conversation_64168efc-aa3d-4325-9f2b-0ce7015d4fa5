/*
 * Function: ?DTradeAnswerRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D3150
 */

char __fastcall CNetworkEX::DTradeAnswerRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-58h]@1
  int v8; // [sp+20h] [bp-38h]@9
  _CLID *pidAsker; // [sp+30h] [bp-28h]@4
  CPlayer *v10; // [sp+38h] [bp-20h]@4
  unsigned int v11; // [sp+40h] [bp-18h]@9
  CNetworkEX *v12; // [sp+60h] [bp+8h]@1

  v12 = this;
  v3 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pidAsker = (_CLID *)pBuf;
  v10 = &g_Player + n;
  if ( !v10->m_bOper || v10->m_pmTrd.bDTradeMode || v10->m_bCorpse )
  {
    result = 1;
  }
  else if ( (signed int)pidAsker->wIndex < 2532 )
  {
    CPlayer::pc_DTradeAnswerRequest(v10, pidAsker);
    result = 1;
  }
  else
  {
    v11 = pidAsker->wIndex;
    v6 = CPlayerDB::GetCharNameA(&v10->m_Param);
    v8 = 2532;
    CLogFile::Write(
      &v12->m_LogFile,
      "odd.. %s: DTradeAnswerRequest() : pRecv->idAsker.wIndex(%d) >= MAX_PLAYER(%d)",
      v6,
      v11);
    result = 0;
  }
  return result;
}
