/*
 * Function: ??1CMoveMapLimitInfoPortal@@QEAA@XZ
 * Address: 0x1403A3FD0
 */

void __fastcall CMoveMapLimitInfoPortal::~CMoveMapLimitInfoPortal(CMoveMapLimitInfoPortal *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-48h]@1
  CMyTimer *v5; // [sp+20h] [bp-28h]@4
  CMyTimer *v6; // [sp+28h] [bp-20h]@4
  __int64 v7; // [sp+30h] [bp-18h]@4
  __int64 v8; // [sp+38h] [bp-10h]@5
  CMoveMapLimitInfoPortal *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  v9->vfptr = (CMoveMapLimitInfoVtbl *)&CMoveMapLimitInfoPortal::`vftable';
  v9->m_pStoreNPC = 0i64;
  v9->m_pStoreNPC = 0i64;
  v9->m_pkRegenDummy = 0i64;
  std::vector<char *,std::allocator<char *>>::clear(&v9->m_vecAllowDummyCode);
  v9->m_uiProcNotifyInx = 0;
  v6 = v9->m_pkNotifyForceMoveHQTimer;
  v5 = v6;
  if ( v6 )
  {
    LODWORD(v3) = ((int (__fastcall *)(CMyTimer *, signed __int64))v5->vfptr->__vecDelDtor)(v5, 1i64);
    v8 = v3;
  }
  else
  {
    v8 = 0i64;
  }
  std::vector<char *,std::allocator<char *>>::~vector<char *,std::allocator<char *>>(&v9->m_vecAllowDummyCode);
  CMoveMapLimitInfo::~CMoveMapLimitInfo((CMoveMapLimitInfo *)&v9->vfptr);
}
