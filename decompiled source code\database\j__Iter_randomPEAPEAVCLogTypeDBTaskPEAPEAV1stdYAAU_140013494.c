/*
 * Function: j_??$_Iter_random@PEAPEAVCLogTypeDBTask@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCLogTypeDBTask@@0@Z
 * Address: 0x140013494
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CLogTypeDBTask * *,CLogTypeDBTask * *>(CLogTypeDBTask **const *__formal, CLogTypeDBTask **const *a2)
{
  return std::_Iter_random<CLogTypeDBTask * *,CLogTypeDBTask * *>(__formal, a2);
}
