/*
 * Function: ?CashDBInit@CMainThread@@AEAA_NPEAD000K@Z
 * Address: 0x1401ED110
 */

char __fastcall CMainThread::CashDBInit(CMainThread *this, char *szIP, char *szDBName, char *szAccount, char *szPassword, unsigned int dwPort)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v8; // rax@4
  CCashDBWorkManager *v9; // rax@4
  char result; // al@5
  __int64 v11; // [sp+0h] [bp-38h]@1
  CMainThread *v12; // [sp+40h] [bp+8h]@1
  char *szIPa; // [sp+48h] [bp+10h]@1
  char *szDBNamea; // [sp+50h] [bp+18h]@1
  char *szAccounta; // [sp+58h] [bp+20h]@1

  szAccounta = szAccount;
  szDBNamea = szDBName;
  szIPa = szIP;
  v12 = this;
  v6 = &v11;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v8 = CTSingleton<CNationSettingManager>::Instance();
  CNationSettingManager::SetCashDBDSN(v8, szIPa, szDBNamea, szAccounta, szPassword, dwPort);
  v9 = CTSingleton<CCashDBWorkManager>::Instance();
  if ( CCashDBWorkManager::InitializeWorker(v9) )
  {
    CLogFile::Write(
      &v12->m_logLoadingError,
      "CMainThread::CashDBInit() : Cash Item DataBase Setting Complete!! (%s : %s)",
      szIPa,
      szDBNamea);
    result = 1;
  }
  else
  {
    MyMessageBox("CMainThread::Init() : ", "CCashDBWorkManager::Instance()->Initialize() Fail!");
    CLogFile::Write(&v12->m_logLoadingError, "CashDbWorker::Instance()->Initialize() Fail!");
    result = 0;
  }
  return result;
}
