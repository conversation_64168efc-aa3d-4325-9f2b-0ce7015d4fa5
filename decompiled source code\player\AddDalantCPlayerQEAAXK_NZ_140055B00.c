/*
 * Function: ?AddDalant@CPlayer@@QEAAXK_N@Z
 * Address: 0x140055B00
 */

void __usercall CPlayer::AddDalant(CPlayer *this@<rcx>, unsigned int dwPush@<edx>, bool bApply@<r8b>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@6
  unsigned int v7; // eax@8
  unsigned int v8; // eax@9
  __int64 v9; // [sp+0h] [bp-38h]@1
  double v10; // [sp+20h] [bp-18h]@5
  unsigned int dwDt; // [sp+28h] [bp-10h]@6
  unsigned int gold; // [sp+2Ch] [bp-Ch]@9
  CPlayer *v13; // [sp+40h] [bp+8h]@1
  unsigned int ui64AddMoney; // [sp+48h] [bp+10h]@1

  ui64AddMoney = dwPush;
  v13 = this;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( bApply )
  {
    TimeLimitMgr::GetPlayerPenalty(qword_1799CA2D0, v13->m_id.wIndex);
    v10 = a4;
    ui64AddMoney = (signed int)floor((double)(signed int)ui64AddMoney * a4);
  }
  dwDt = ui64AddMoney + CPlayerDB::GetDalant(&v13->m_Param);
  v6 = CPlayerDB::GetDalant(&v13->m_Param);
  if ( !CanAddMoneyForMaxLimMoney(ui64AddMoney, v6) )
    dwDt = 2000000000;
  v7 = CPlayerDB::GetDalant(&v13->m_Param);
  if ( dwDt != v7 )
  {
    CPlayerDB::SetDalant(&v13->m_Param, dwDt);
    gold = CPlayerDB::GetGold(&v13->m_Param);
    v8 = CPlayerDB::GetDalant(&v13->m_Param);
    CUserDB::Update_Money(v13->m_pUserDB, v8, gold);
  }
}
