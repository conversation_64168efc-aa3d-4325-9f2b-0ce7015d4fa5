/*
 * Function: j_?_Tidy@?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@IEAAXXZ
 * Address: 0x140013971
 */

void __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Tidy(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Tidy(this);
}
