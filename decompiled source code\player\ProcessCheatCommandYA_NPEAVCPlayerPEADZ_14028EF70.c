/*
 * Function: ?ProcessCheatCommand@@YA_NPEAVCPlayer@@PEAD@Z
 * Address: 0x14028EF70
 */

char __fastcall ProcessCheatCommand(CPlayer *pOne, char *pwszCommand)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v4; // rax@4
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-68h]@1
  CHEAT_COMMAND *v7; // [sp+40h] [bp-28h]@4
  CHEAT_COMMAND *pCmd; // [sp+48h] [bp-20h]@4
  int j; // [sp+50h] [bp-18h]@6
  CPlayer *pOnea; // [sp+70h] [bp+8h]@1
  char *_Str; // [sp+78h] [bp+10h]@1

  _Str = pwszCommand;
  pOnea = pOne;
  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = CTSingleton<CNationSettingManager>::Instance();
  v7 = CNationSettingManager::GetCheatTable(v4);
  pCmd = 0i64;
  if ( strchr(_Str, 37) )
  {
    sprintf(wszRespon, "%s >> FAIL(grammar or logic)", _Str);
    CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
    result = 1;
  }
  else
  {
    for ( j = 0; ; ++j )
    {
      pCmd = &v7[j];
      if ( !pCmd->pwszCommand )
        break;
      if ( !_strnicmp(pCmd->pwszCommand, _Str, pCmd->uiCmdLen) )
      {
        if ( AuthorityFilter(pCmd, pOnea) )
        {
          s_nWordCount = ParsingCommandW(&_Str[pCmd->uiCmdLen], 6, s_pwszDstCheat, 32);
          if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))pCmd->fn)(pOnea) )
          {
            WriteCheatLog(_Str, pOnea);
            if ( pOnea )
            {
              sprintf(wszRespon, "%s >> OK", _Str);
              CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
            }
            result = 1;
          }
          else
          {
            if ( pOnea )
            {
              sprintf(wszRespon, "%s >> FAIL(grammar or logic)", _Str);
              CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
            }
            result = 0;
          }
        }
        else
        {
          sprintf(wszRespon, "%s >> ERROR (authority)", _Str);
          CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
          result = 0;
        }
        return result;
      }
    }
    if ( pOnea )
    {
      sprintf(wszRespon, "%s >> ERROR (command)", _Str);
      CPlayer::SendData_ChatTrans(pOnea, 0, 0xFFFFFFFF, -1, 0, wszRespon, -1, 0i64);
    }
    result = 0;
  }
  return result;
}
