/*
 * Function: ?name@?$class_name@VCMonster@@@lua_tinker@@SAPEBDPEBD@Z
 * Address: 0x1404082A0
 */

char *__fastcall lua_tinker::class_name<CMonster>::name(const char *name)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  char *Src; // [sp+30h] [bp+8h]@1

  Src = (char *)name;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( Src )
    strcpy_s(`lua_tinker::class_name<CMonster>::name'::`2'::temp, 0x100ui64, Src);
  return `lua_tinker::class_name<CMonster>::name'::`2'::temp;
}
