/*
 * Function: ?SignAndRestart@?$DL_SignerBase@VInteger@CryptoPP@@@CryptoPP@@UEBA_KAEAVRandomNumberGenerator@2@AEAVPK_MessageAccumulator@2@PEAE_N@Z
 * Address: 0x1405622F0
 */

__int64 __fastcall CryptoPP::DL_SignerBase<CryptoPP::Integer>::SignAndRestart(__int64 a1, struct CryptoPP::RandomNumberGenerator *a2, __int64 a3, unsigned __int8 *a4, char a5)
{
  CryptoPP::CryptoMaterial *v5; // rax@1
  __int64 v6; // rax@1
  __int64 v7; // rax@1
  __int64 v8; // rax@1
  unsigned __int64 v9; // rax@1
  __int64 *v10; // rax@1
  __int64 v11; // rax@1
  const void *v12; // rax@1
  __int64 v13; // rax@1
  char *v14; // rax@1
  char v15; // ST30_1@1
  char *v16; // rax@1
  char *v17; // rax@2
  CryptoPP::Integer *v18; // rax@3
  __int64 v19; // rax@3
  __int64 v20; // rax@3
  __int64 v21; // rax@3
  unsigned __int64 v22; // rax@3
  unsigned __int64 v23; // rax@3
  __int64 v24; // rax@5
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v26; // [sp+50h] [bp-2A8h]@1
  __int64 v27; // [sp+68h] [bp-290h]@1
  CryptoPP::Integer v28; // [sp+70h] [bp-288h]@3
  __int64 v29; // [sp+98h] [bp-260h]@1
  __int64 v30; // [sp+A0h] [bp-258h]@1
  CryptoPP::Integer v31; // [sp+A8h] [bp-250h]@3
  __int64 v32; // [sp+D0h] [bp-228h]@1
  CryptoPP::Integer v33; // [sp+D8h] [bp-220h]@3
  unsigned __int64 v34; // [sp+100h] [bp-1F8h]@3
  CryptoPP::Integer v35; // [sp+108h] [bp-1F0h]@1
  char v36; // [sp+130h] [bp-1C8h]@1
  CryptoPP::Integer b; // [sp+140h] [bp-1B8h]@3
  CryptoPP::Integer result; // [sp+168h] [bp-190h]@3
  CryptoPP::Integer v39; // [sp+190h] [bp-168h]@3
  CryptoPP::Integer v40; // [sp+1B8h] [bp-140h]@3
  CryptoPP::Integer v41; // [sp+1E0h] [bp-118h]@3
  __int64 v42; // [sp+208h] [bp-F0h]@5
  char v43; // [sp+210h] [bp-E8h]@1
  __int64 v44; // [sp+220h] [bp-D8h]@1
  __int64 v45; // [sp+228h] [bp-D0h]@1
  __int64 v46; // [sp+230h] [bp-C8h]@1
  __int64 *v47; // [sp+238h] [bp-C0h]@1
  __int64 v48; // [sp+240h] [bp-B8h]@1
  char *v49; // [sp+248h] [bp-B0h]@1
  __int64 v50; // [sp+250h] [bp-A8h]@1
  __int64 v51; // [sp+258h] [bp-A0h]@1
  unsigned __int64 v52; // [sp+260h] [bp-98h]@1
  __int64 v53; // [sp+268h] [bp-90h]@1
  unsigned __int64 v54; // [sp+270h] [bp-88h]@1
  unsigned __int64 v55; // [sp+278h] [bp-80h]@2
  CryptoPP::ClonableVtbl *v56; // [sp+280h] [bp-78h]@2
  struct CryptoPP::Integer *v57; // [sp+288h] [bp-70h]@3
  struct CryptoPP::Integer *v58; // [sp+290h] [bp-68h]@3
  __int64 v59; // [sp+298h] [bp-60h]@3
  struct CryptoPP::Integer *v60; // [sp+2A0h] [bp-58h]@3
  struct CryptoPP::Integer *v61; // [sp+2A8h] [bp-50h]@3
  __int64 v62; // [sp+2B0h] [bp-48h]@3
  __int64 v63; // [sp+2B8h] [bp-40h]@3
  __int64 v64; // [sp+2C0h] [bp-38h]@3
  __int64 v65; // [sp+2C8h] [bp-30h]@3
  void (__fastcall **v66)(_QWORD, _QWORD, _QWORD, _QWORD); // [sp+2D0h] [bp-28h]@3
  __int64 v67; // [sp+300h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v68; // [sp+308h] [bp+10h]@1
  __int64 v69; // [sp+310h] [bp+18h]@1
  unsigned __int8 *v70; // [sp+318h] [bp+20h]@1

  v70 = a4;
  v69 = a3;
  v68 = a2;
  v67 = a1;
  v44 = -2i64;
  v45 = *(_QWORD *)(a1 + 8);
  LODWORD(v5) = (*(int (__fastcall **)(signed __int64))(v45 + 32))(a1 + 8);
  CryptoPP::CryptoMaterial::DoQuickSanityCheck(v5);
  v27 = v69;
  LODWORD(v6) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v67 + 120i64))(v67);
  v32 = v6;
  LODWORD(v7) = CryptoPP::DL_Base<CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::GetAbstractGroupParameters(v67 + 16);
  v29 = v7;
  v46 = *(_QWORD *)(v67 + 16);
  LODWORD(v8) = (*(int (__fastcall **)(signed __int64))(v46 + 8))(v67 + 16);
  v30 = v8;
  LODWORD(v9) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::MessageRepresentativeLength(v67);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v26,
    v9);
  LODWORD(v10) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v67 + 128i64))(v67);
  v47 = v10;
  LODWORD(v11) = CryptoPP::DL_SignatureSchemeBase<CryptoPP::PK_Signer,CryptoPP::DL_PrivateKey<CryptoPP::Integer>>::MessageRepresentativeBitLength(v67);
  v48 = v11;
  v49 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v26);
  v50 = *(_QWORD *)v67;
  LODWORD(v12) = (*(int (__fastcall **)(__int64, char *))(v50 + 136))(v67, &v36);
  qmemcpy(&v43, v12, 0x10ui64);
  LODWORD(v13) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v27 + 144i64))(v27);
  v51 = v13;
  v52 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v27 + 8));
  v14 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *((CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > *)(v27 + 8));
  v53 = *v47;
  v15 = *(_BYTE *)(v27 + 184);
  (*(void (__fastcall **)(__int64 *, struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))(v53 + 48))(
    v47,
    v68,
    v14,
    v52);
  *(_BYTE *)(v27 + 184) = 1;
  v54 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v26);
  v16 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v26);
  CryptoPP::Integer::Integer(&v35, (const unsigned __int8 *)v16, v54, 0);
  if ( (unsigned __int8)((int (__fastcall *)(struct CryptoPP::RandomNumberGenerator *))v68->vfptr[2].__vecDelDtor)(v68) )
  {
    v55 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v26);
    v17 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v26);
    v56 = v68->vfptr;
    ((void (__fastcall *)(struct CryptoPP::RandomNumberGenerator *, char *, unsigned __int64))v56[1].Clone)(
      v68,
      v17,
      v55);
  }
  CryptoPP::Integer::Integer(&b, 1);
  CryptoPP::Integer::Integer(&v39, 1);
  v57 = (struct CryptoPP::Integer *)CryptoPP::Integer::One();
  v58 = (struct CryptoPP::Integer *)CryptoPP::Integer::Zero();
  v59 = *(_QWORD *)v29;
  LODWORD(v18) = (*(int (__fastcall **)(__int64))(v59 + 64))(v29);
  v60 = CryptoPP::operator-(&result, v18, &b);
  v61 = v60;
  CryptoPP::Integer::Integer((enum CryptoPP::Integer::RandomNumberType)&v31, v68, &v39, v60, 0, v58, v57);
  CryptoPP::Integer::~Integer(&result);
  CryptoPP::Integer::~Integer(&v39);
  CryptoPP::Integer::~Integer(&b);
  CryptoPP::Integer::Integer(&v33);
  CryptoPP::Integer::Integer(&v28);
  LODWORD(v19) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, CryptoPP::Integer *))(*(_QWORD *)v29 + 24i64))(
                   v29,
                   &v40,
                   &v31);
  v62 = v19;
  v63 = v19;
  LODWORD(v20) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *, __int64))(*(_QWORD *)v29 + 120i64))(
                   v29,
                   &v41,
                   v19);
  v64 = v20;
  v65 = v20;
  CryptoPP::Integer::operator=(&v33);
  CryptoPP::Integer::~Integer(&v41);
  CryptoPP::Integer::~Integer(&v40);
  LODWORD(v21) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v30 + 16i64))(v30);
  v66 = *(void (__fastcall ***)(_QWORD, _QWORD, _QWORD, _QWORD))v32;
  (*v66)(v32, v29, v21, &v31);
  LODWORD(v22) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v32 + 24i64))(v32, v29);
  v34 = v22;
  CryptoPP::Integer::Encode(&v33, v70, v22, 0);
  LODWORD(v23) = (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v32 + 32i64))(v32, v29);
  CryptoPP::Integer::Encode(&v28, &v70[v34], v23, 0);
  if ( a5 )
    CryptoPP::DL_SignerBase<CryptoPP::Integer>::RestartMessageAccumulator(v67, v68, v27);
  LODWORD(v24) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v67 + 8i64))(v67);
  v42 = v24;
  CryptoPP::Integer::~Integer(&v28);
  CryptoPP::Integer::~Integer(&v33);
  CryptoPP::Integer::~Integer(&v31);
  CryptoPP::Integer::~Integer(&v35);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v26);
  return v42;
}
