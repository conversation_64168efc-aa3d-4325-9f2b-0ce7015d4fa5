/*
 * Function: ?ct_recv_total_guild_rank@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140293A40
 */

char __fastcall ct_recv_total_guild_rank(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CTotalGuildRankManager *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  unsigned int dwVer; // [sp+20h] [bp-18h]@8
  char v7; // [sp+24h] [bp-14h]@8
  CPlayer *pkPlayer; // [sp+40h] [bp+8h]@1

  pkPlayer = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( pkPlayer )
  {
    if ( s_nWordCount == 2 )
    {
      dwVer = atoi(s_pwszDstCheat[0]);
      v7 = atoi(s_pwszDstCheat[1]);
      v4 = CTotalGuildRankManager::Instance();
      CTotalGuildRankManager::Send(v4, dwVer, v7, pkPlayer);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
