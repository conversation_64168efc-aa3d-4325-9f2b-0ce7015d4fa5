/*
 * Function: j_??A?$vector@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEAAAEAVCUnmannedTraderGroupDivisionVersionInfo@@_K@Z
 * Address: 0x14001000F
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator[](std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator[](
           this,
           _Pos);
}
