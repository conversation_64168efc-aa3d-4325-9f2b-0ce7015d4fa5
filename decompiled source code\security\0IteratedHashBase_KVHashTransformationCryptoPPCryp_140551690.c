/*
 * Function: ??0?$IteratedHashBase@_KVHashTransformation@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140551690
 */

CryptoPP::HashTransformation *__fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>(CryptoPP::HashTransformation *a1)
{
  CryptoPP::HashTransformation *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::HashTransformation::HashTransformation(a1);
  v2[1].vfptr = 0i64;
  v2[2].vfptr = 0i64;
  return v2;
}
