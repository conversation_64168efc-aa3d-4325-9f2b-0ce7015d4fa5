/*
 * Function: ?_CheckUserInfo@ClassOrderProcessor@@AEAAHEEPEAVCPlayer@@@Z
 * Address: 0x1402B9060
 */

signed __int64 __fastcall ClassOrderProcessor::_CheckUserInfo(ClassOrderProcessor *this, char byRace, char byClassType, CPlayer *pUser)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  CandidateMgr *v7; // rax@9
  CandidateMgr *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-38h]@1
  _candidate_info *v10; // [sp+20h] [bp-18h]@9
  _candidate_info::ClassType eType; // [sp+28h] [bp-10h]@9
  unsigned int dwASerial; // [sp+2Ch] [bp-Ch]@11
  char v13; // [sp+48h] [bp+10h]@1
  char v14; // [sp+50h] [bp+18h]@1
  CPlayer *v15; // [sp+58h] [bp+20h]@1

  v15 = pUser;
  v14 = byClassType;
  v13 = byRace;
  v4 = &v9;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pUser && pUser->m_bOper )
  {
    if ( CPlayerDB::GetRaceCode(&pUser->m_Param) == (unsigned __int8)byRace )
    {
      eType = (unsigned __int8)v14;
      v7 = CandidateMgr::Instance();
      v10 = CandidateMgr::GetPatriarchGroup(v7, v13, eType);
      if ( v10 )
      {
        result = 2i64;
      }
      else
      {
        dwASerial = CPlayerDB::GetCharSerial(&v15->m_Param);
        v8 = CandidateMgr::Instance();
        v10 = CandidateMgr::GetPatriarchGroupBySerial(v8, v13, dwASerial);
        if ( v10 )
        {
          result = 3i64;
        }
        else if ( v15->m_Param.m_byPvPGrade >= 3 )
        {
          result = 0i64;
        }
        else
        {
          result = 4i64;
        }
      }
    }
    else
    {
      result = 1i64;
    }
  }
  else
  {
    result = 1i64;
  }
  return result;
}
