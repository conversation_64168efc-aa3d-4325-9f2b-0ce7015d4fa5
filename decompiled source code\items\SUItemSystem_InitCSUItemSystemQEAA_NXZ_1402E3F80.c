/*
 * Function: ?SUItemSystem_Init@CSUItemSystem@@QEAA_NXZ
 * Address: 0x1402E3F80
 */

char __fastcall CSUItemSystem::SUItemSystem_Init(CSUItemSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-D8h]@1
  char pszErrMsg; // [sp+30h] [bp-A8h]@4
  unsigned __int64 v6; // [sp+C0h] [bp-18h]@4
  CSUItemSystem *v7; // [sp+E0h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (unsigned __int64)&v4 ^ _security_cookie;
  CSUItemSystem::Class_Init(v7);
  if ( CRecordData::ReadRecord(v7->m_SUOrigin, ".\\script\\SetItemEff.dat", 0x404u, &pszErrMsg) )
  {
    v7->m_bLoadData[0] = 1;
    if ( CSetItemType::SetItemType_Init(&v7->m_SetItemType, v7->m_SUOrigin) )
    {
      v7->m_bChangeData[0] = 1;
      ++v7->m_bySUItemCount;
      result = 1;
    }
    else
    {
      MyMessageBox("Failed Interpret Script data File : CSUItemSystem_Init()", "Error Interpeting Of SetItemType Data");
      result = 0;
    }
  }
  else
  {
    MyMessageBox("Failed Load Script Data File : CSUItemSystem_Init()", &pszErrMsg);
    result = 0;
  }
  return result;
}
