/*
 * Function: ?DE_BattleMode_RecallCommonPlayer@@YA_NPEAVCCharacter@@0MAEAE@Z
 * Address: 0x14017ED80
 */

bool __fastcall DE_BattleMode_RecallCommonPlayer(CCharacter *pActChar, CCharacter *pTargetChar, float fEffectValue, char *byRet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CRecallEffectController *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-38h]@1
  CPlayer *pkPerformer; // [sp+40h] [bp+8h]@1
  CCharacter *pkDest; // [sp+48h] [bp+10h]@1

  pkDest = pTargetChar;
  pkPerformer = (CPlayer *)pActChar;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pkPerformer->m_ObjID.m_byID )
  {
    result = 0;
  }
  else
  {
    v7 = CRecallEffectController::Instance();
    result = CRecallEffectController::RequestRecall(v7, pkPerformer, pkDest, 0, 0, 1);
  }
  return result;
}
