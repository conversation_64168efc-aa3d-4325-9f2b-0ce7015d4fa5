/*
 * Function: ?PushTask@CCashDBWorkManager@@QEAA_NHPEAE_K@Z
 * Address: 0x1402F3340
 */

bool __fastcall CCashDBWorkManager::PushTask(CCashDBWorkManager *this, int nTaskCode, char *p, unsigned __int64 size)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CCashDBWorkManager *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return CashDbWorker::PushTask(v8->m_pWorker, nTaskCode, p, size);
}
