/*
 * Function: ?_InternalPacketProcess@CNetProcess@@AEAA_NKPEAU_MSG_HEADER@@PEAD@Z
 * Address: 0x14047A4F0
 */

bool __fastcall CNetProcess::_InternalPacketProcess(CNetProcess *this, unsigned int dwSocketIndex, _MSG_HEADER *pMsgHeader, char *pMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v7; // r9@28
  CNetWorking *v8; // rcx@29
  CNetWorkingVtbl *v9; // rax@29
  __int64 v10; // r9@32
  CNetWorking *v11; // rcx@33
  CNetWorkingVtbl *v12; // rax@33
  __int64 v13; // [sp+0h] [bp-78h]@1
  __int64 v14; // [sp+20h] [bp-58h]@29
  _socket *v15; // [sp+30h] [bp-48h]@12
  void *Src; // [sp+38h] [bp-40h]@12
  _socket *v17; // [sp+40h] [bp-38h]@14
  void *Buf1; // [sp+48h] [bp-30h]@15
  DWORD v19; // [sp+50h] [bp-28h]@17
  _socket *v20; // [sp+58h] [bp-20h]@28
  _socket *v21; // [sp+60h] [bp-18h]@32
  CNetProcess *v22; // [sp+80h] [bp+8h]@1
  int dwIndex; // [sp+88h] [bp+10h]@1
  char *v24; // [sp+98h] [bp+20h]@1

  v24 = pMsg;
  dwIndex = dwSocketIndex;
  v22 = this;
  v4 = &v13;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pMsgHeader->m_byType[0] == 101 )
  {
    if ( pMsgHeader->m_byType[1] == 1 )
    {
      result = 1;
    }
    else if ( pMsgHeader->m_byType[1] == 2 )
    {
      result = 1;
    }
    else
    {
      result = pMsgHeader->m_byType[1] == 3;
    }
    return result;
  }
  if ( pMsgHeader->m_byType[0] != 102 )
    return 0;
  if ( pMsgHeader->m_byType[1] == 2 )
  {
    v15 = CNetSocket::GetSocket(&v22->m_NetSocket, dwSocketIndex);
    v15->m_dwResponSpeedHackTime = timeGetTime();
    Src = v24;
    memcpy_0(v15->m_dwSpeedHackKey, v24, 0x10ui64);
    return 1;
  }
  if ( pMsgHeader->m_byType[1] == 3 )
  {
    v17 = CNetSocket::GetSocket(&v22->m_NetSocket, dwSocketIndex);
    if ( v17 )
    {
      Buf1 = v24;
      if ( memcmp_0(v24, v17->m_dwSpeedHackKey, 0x10ui64) )
      {
        CLogFile::Write(&v22->m_LogHack, "SH: %s (key)", v17->m_szID);
        CNetProcess::PushCloseNode(v22, dwIndex);
        return 1;
      }
      v19 = timeGetTime() - v17->m_dwSendSpeedHackTime;
      if ( v19 < 0x1388 )
      {
        if ( ++v17->m_bySpeedHackContCount > 5 )
        {
          if ( v19 >= 0x125C )
          {
            CLogFile::Write(&v22->m_LogHack, "SH: %s (T:%d) O", v17->m_szID, v19);
          }
          else
          {
            CLogFile::Write(&v22->m_LogHack, "SH: %s (T:%d) X", v17->m_szID, v19);
            CNetProcess::PushCloseNode(v22, dwIndex);
          }
          return 1;
        }
        if ( v19 < 0x1194 )
        {
          CLogFile::Write(&v22->m_LogHack, "SH %s (T:%d) XX", v17->m_szID, v19);
          CNetProcess::PushCloseNode(v22, dwIndex);
          return 1;
        }
      }
      v17->m_bySpeedHackMissCount = 0;
      CNetProcess::_SendSpeedHackCheckMsg(v22, dwIndex);
    }
    return 1;
  }
  if ( pMsgHeader->m_byType[1] == 4 )
  {
    v20 = CNetSocket::GetSocket(&v22->m_NetSocket, dwSocketIndex);
    if ( v20 )
    {
      v8 = v22->m_pNetwork;
      v9 = v22->m_pNetwork->vfptr;
      v14 = 0i64;
      LOBYTE(v7) = 4;
      ((void (__fastcall *)(CNetWorking *, _QWORD, _QWORD, __int64))v9->ExpulsionSocket)(
        v8,
        v22->m_nIndex,
        (unsigned int)dwIndex,
        v7);
    }
    result = 1;
  }
  else if ( pMsgHeader->m_byType[1] == 5 )
  {
    v21 = CNetSocket::GetSocket(&v22->m_NetSocket, dwSocketIndex);
    if ( v21 )
    {
      v11 = v22->m_pNetwork;
      v12 = v22->m_pNetwork->vfptr;
      v14 = 0i64;
      LOBYTE(v10) = 6;
      ((void (__fastcall *)(CNetWorking *, _QWORD, _QWORD, __int64))v12->ExpulsionSocket)(
        v11,
        v22->m_nIndex,
        (unsigned int)dwIndex,
        v10);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
