/*
 * Function: j_??0?$_Ranit@PEAVCUnmannedTraderSubClassInfo@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x14000E62E
 */

void __fastcall std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>(std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &> *this)
{
  std::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>::_Ranit<CUnmannedTraderSubClassInfo *,__int64,CUnmannedTraderSubClassInfo * const *,CUnmannedTraderSubClassInfo * const &>(this);
}
