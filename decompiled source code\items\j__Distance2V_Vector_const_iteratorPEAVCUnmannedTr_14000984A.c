/*
 * Function: j_??$_Distance2@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@_K@std@@YAXV?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@0AEA_KUrandom_access_iterator_tag@0@@Z
 * Address: 0x14000984A
 */

void __fastcall std::_Distance2<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,unsigned __int64>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *_Last, unsigned __int64 *_Off, std::random_access_iterator_tag __formal)
{
  std::_Distance2<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,unsigned __int64>(
    _First,
    _Last,
    _Off,
    __formal);
}
