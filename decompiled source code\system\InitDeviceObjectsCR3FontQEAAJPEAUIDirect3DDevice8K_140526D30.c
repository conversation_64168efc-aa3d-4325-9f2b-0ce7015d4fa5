/*
 * Function: ?InitDeviceObjects@CR3Font@@QEAAJPEAUIDirect3DDevice8@@KKK@Z
 * Address: 0x140526D30
 */

__int64 __fastcall CR3Font::InitDeviceObjects(CR3Font *this, struct IDirect3DDevice8 *a2, unsigned int a3, unsigned int a4, unsigned __int32 a5)
{
  CR3Font *v5; // rbp@1
  unsigned int v6; // edi@1
  unsigned int v7; // esi@1
  __int64 v8; // rcx@1
  int v9; // ebx@1
  int v10; // eax@1
  char v12; // [sp+40h] [bp-E8h]@1

  *(_QWORD *)this = a2;
  *((_DWORD *)this + 20) = 1065353216;
  v5 = this;
  v6 = a4;
  v7 = a3;
  ((void (__fastcall *)(struct IDirect3DDevice8 *, char *))a2->vfptr[2].AddRef)(a2, &v12);
  v8 = *(_QWORD *)v5;
  v9 = 0;
  *((_DWORD *)v5 + 36) = v7;
  *((_DWORD *)v5 + 37) = v6;
  v10 = (*(int (__fastcall **)(__int64, _QWORD, _QWORD, signed __int64))(*(_QWORD *)v8 + 160i64))(v8, v7, v6, 1i64);
  if ( v10 < 0 )
    v9 = v10;
  *((_DWORD *)v5 + 21) = a5;
  return (unsigned int)v9;
}
