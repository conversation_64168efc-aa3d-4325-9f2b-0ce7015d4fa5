/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____CUnmannedTraderClassInfo_____ptr64_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____::_1_::catch$0
 * Address: 0x140376810
 */

void __fastcall __noreturn std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____CUnmannedTraderClassInfo_____ptr64_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for ( i = a2; *(_QWORD *)(i + 32) != *(_QWORD *)(i + 112); *(_QWORD *)(i + 32) += 8i64 )
    std::allocator<CUnmannedTraderClassInfo *>::destroy(
      *(std::allocator<CUnmannedTraderClassInfo *> **)(i + 120),
      *(CUnmannedTraderClassInfo ***)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}
