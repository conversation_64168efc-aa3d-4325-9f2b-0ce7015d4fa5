/*
 * Function: ?SendMsg_EquipPartChange@CPlayer@@QEAAXE@Z
 * Address: 0x1400D5340
 */

void __fastcall CPlayer::SendMsg_EquipPartChange(CPlayer *this, char byPart)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  unsigned int v6; // [sp+3Ah] [bp-4Eh]@4
  __int16 v7; // [sp+3Eh] [bp-4Ah]@4
  char v8; // [sp+40h] [bp-48h]@4
  unsigned __int16 v9; // [sp+41h] [bp-47h]@5
  char v10; // [sp+43h] [bp-45h]@5
  char pbyType; // [sp+64h] [bp-24h]@9
  char v12; // [sp+65h] [bp-23h]@9
  CPlayer *v13; // [sp+90h] [bp+8h]@1
  char v14; // [sp+98h] [bp+10h]@1

  v14 = byPart;
  v13 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_WORD *)szMsg = v13->m_ObjID.m_wIndex;
  v6 = v13->m_dwObjSerial;
  v7 = CPlayer::GetVisualVer(v13);
  v8 = v14;
  if ( v13->m_Param.m_dbEquip.m_pStorageList[(unsigned __int8)v14].m_bLoad )
  {
    v9 = v13->m_Param.m_dbEquip.m_pStorageList[(unsigned __int8)v14].m_wItemIndex;
    v10 = GetItemUpgedLv(v13->m_Param.m_dbEquip.m_pStorageList[(unsigned __int8)v14].m_dwLv);
  }
  else if ( (signed int)(unsigned __int8)v14 >= 5 )
  {
    v9 = -1;
  }
  else
  {
    v9 = v13->m_Param.m_dbChar.m_byDftPart[(unsigned __int8)v14];
    v10 = 0;
  }
  pbyType = 3;
  v12 = 34;
  CGameObject::CircleReport((CGameObject *)&v13->vfptr, &pbyType, szMsg, 12, 0);
}
