/*
 * Function: ?Initialize@Voter@@UEAA_NXZ
 * Address: 0x1402BE940
 */

char __fastcall Voter::Initialize(Voter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  PatriarchElectProcessor *v4; // rax@4
  __int64 v6; // [sp+0h] [bp-168h]@1
  char _Dest[256]; // [sp+40h] [bp-128h]@4
  unsigned __int64 v8; // [sp+150h] [bp-18h]@4
  Voter *v9; // [sp+170h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  v3 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\Patriarch\\Voter_%d.log", v3);
  CLogFile::SetWriteLogFile(&v9->_kSysLog, _Dest, 1, 0, 1, 1);
  v4 = PatriarchElectProcessor::Instance();
  PatriarchElectProcessor::PushDQSCheckInvalidChar(v4);
  ElectProcessor::Initialize((ElectProcessor *)&v9->vfptr);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 120, 0i64, 0);
  return 1;
}
