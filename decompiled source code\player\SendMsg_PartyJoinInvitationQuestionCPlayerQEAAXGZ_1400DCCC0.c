/*
 * Function: ?SendMsg_PartyJoinInvitationQuestion@CPlayer@@QEAAXG@Z
 * Address: 0x1400DCCC0
 */

void __fastcall CPlayer::SendMsg_PartyJoinInvitationQuestion(CPlayer *this, unsigned __int16 wJoinerIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  unsigned int v6; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = v9->m_dwObjSerial;
  *(_WORD *)szMsg = v9->m_ObjID.m_wIndex;
  pbyType = 16;
  v8 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, wJoinerIndex, &pbyType, szMsg, 6u);
}
