/*
 * Function: ??0?$_Vector_const_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAA@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x140596710
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  __int64 v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  std::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>::_Ranit<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> const &>();
  *(_QWORD *)(v3 + 16) = v4;
  return v3;
}
