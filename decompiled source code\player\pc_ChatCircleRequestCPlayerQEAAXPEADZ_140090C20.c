/*
 * Function: ?pc_ChatCircleRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140090C20
 */

void __usercall CPlayer::pc_ChatCircleRequest(CPlayer *this@<rcx>, char *pwszChatData@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@10
  char *v6; // rax@10
  int v7; // eax@10
  CChatStealSystem *v8; // rax@10
  int v9; // eax@19
  CPvpUserAndGuildRankingSystem *v10; // rax@22
  unsigned int v11; // eax@22
  unsigned __int16 v12; // ax@23
  unsigned __int16 v13; // ax@24
  __int64 v14; // [sp+0h] [bp-358h]@1
  _chat_message_receipt_udp Dst; // [sp+40h] [bp-318h]@10
  _chat_message_receipt_udp v16; // [sp+180h] [bp-1D8h]@10
  char pbyType; // [sp+2B4h] [bp-A4h]@10
  char v18; // [sp+2B5h] [bp-A3h]@10
  _pnt_rect pRect; // [sp+2D8h] [bp-80h]@10
  _sec_info *v20; // [sp+2F8h] [bp-60h]@10
  int j; // [sp+300h] [bp-58h]@10
  int k; // [sp+304h] [bp-54h]@12
  unsigned int dwSecIndex; // [sp+308h] [bp-50h]@15
  CObjectList *v24; // [sp+310h] [bp-48h]@15
  CObjectList *v25; // [sp+318h] [bp-40h]@16
  CObjectListVtbl *v26; // [sp+320h] [bp-38h]@18
  CObjectListVtbl *v27; // [sp+328h] [bp-30h]@18
  CObjectListVtbl *v28; // [sp+330h] [bp-28h]@18
  int v29; // [sp+340h] [bp-18h]@19
  int v30; // [sp+344h] [bp-14h]@22
  unsigned __int64 v31; // [sp+348h] [bp-10h]@4
  CPlayer *v32; // [sp+360h] [bp+8h]@1
  const char *Str; // [sp+368h] [bp+10h]@1

  Str = pwszChatData;
  v32 = this;
  v3 = &v14;
  for ( i = 212i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v31 = (unsigned __int64)&v14 ^ _security_cookie;
  if ( (!v32->m_pUserDB || !v32->m_pUserDB->m_bChatLock)
    && v32->m_pCurMap
    && CGameObject::GetCurSecNum((CGameObject *)&v32->vfptr) != -1
    && !v32->m_bMapLoading )
  {
    _chat_message_receipt_udp::_chat_message_receipt_udp(&Dst);
    Dst.byMessageType = 1;
    Dst.dwSenderSerial = v32->m_dwObjSerial;
    Dst.byRaceCode = CPlayerDB::GetRaceCode(&v32->m_Param);
    Dst.bFiltering = 0;
    Dst.bySize = strlen_0(Str);
    memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
    Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
    v5 = CPlayerDB::GetCharNameW(&v32->m_Param);
    strcpy_0(Dst.wszSenderName, v5);
    Dst.byPvpGrade = v32->m_Param.m_byPvPGrade;
    _chat_message_receipt_udp::_chat_message_receipt_udp(&v16);
    v16.byMessageType = 1;
    v16.dwSenderSerial = v32->m_dwObjSerial;
    v16.byRaceCode = CPlayerDB::GetRaceCode(&v32->m_Param);
    v16.bFiltering = 1;
    v16.wszChatData[0] = 0;
    v16.bySize = 0;
    v6 = CPlayerDB::GetCharNameW(&v32->m_Param);
    strcpy_0(v16.wszSenderName, v6);
    v16.byPvpGrade = v32->m_Param.m_byPvPGrade;
    pbyType = 2;
    v18 = 10;
    v20 = CMapData::GetSecInfo(v32->m_pCurMap);
    v7 = CGameObject::GetCurSecNum((CGameObject *)&v32->vfptr);
    CMapData::GetRectInRadius(v32->m_pCurMap, &pRect, 3, v7);
    v8 = CChatStealSystem::Instance();
    CChatStealSystem::StealChatMsg(v8, v32, 1, (char *)Str);
    for ( j = pRect.nStarty; j <= pRect.nEndy; ++j )
    {
      for ( k = pRect.nStartx; k <= pRect.nEndx; ++k )
      {
        dwSecIndex = v20->m_nSecNumW * j + k;
        v24 = CMapData::GetSectorListPlayer(v32->m_pCurMap, v32->m_wMapLayerIndex, dwSecIndex);
        if ( v24 )
        {
          v25 = (CObjectList *)v24->m_Head.m_pNext;
          while ( (_object_list_point *)v25 != &v24->m_Tail )
          {
            v26 = v25->vfptr;
            v25 = (CObjectList *)v25->m_Head.m_pItem;
            v27 = v26 + 2;
            v28 = v26;
            if ( v32->m_byUserDgr == 2
              || (v29 = CPlayerDB::GetRaceCode((CPlayerDB *)&v28[244]),
                  v9 = CPlayerDB::GetRaceCode(&v32->m_Param),
                  v29 == v9)
              || (signed int)BYTE4(v28[234].__vecDelDtor) >= 2
              || (_effect_parameter::GetEff_Have((_effect_parameter *)&v28[225], 3), a3 != 0.0)
              || (v30 = CPlayerDB::GetRaceCode((CPlayerDB *)&v28[244]),
                  v10 = CPvpUserAndGuildRankingSystem::Instance(),
                  v11 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v10, v30, 0),
                  v11 == HIDWORD(v28[2].__vecDelDtor)) )
            {
              v13 = _chat_message_receipt_udp::size(&Dst);
              CNetProcess::LoadSendMsg(unk_1414F2088, WORD1(v27->__vecDelDtor), &pbyType, &Dst.byMessageType, v13);
            }
            else
            {
              v12 = _chat_message_receipt_udp::size(&v16);
              CNetProcess::LoadSendMsg(unk_1414F2088, WORD1(v27->__vecDelDtor), &pbyType, &v16.byMessageType, v12);
            }
          }
        }
      }
    }
  }
}
