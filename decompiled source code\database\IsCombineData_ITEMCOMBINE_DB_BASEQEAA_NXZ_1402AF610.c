/*
 * Function: ?IsCombineData@_ITEMCOMBINE_DB_BASE@@QEAA_NXZ
 * Address: 0x1402AF610
 */

bool __fastcall _ITEMCOMBINE_DB_BASE::IsCombineData(_ITEMCOMBINE_DB_BASE *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // [sp+0h] [bp-18h]@1
  _ITEMCOMBINE_DB_BASE *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  return v5->m_bIsResult && v5->m_dwCheckKey != -1;
}
