/*
 * Function: ?SendMsg_Notify_Me_Get_Golden_Box@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400E8FD0
 */

void __fastcall CPlayer::SendMsg_Notify_Me_Get_Golden_Box(CPlayer *this, char byBoxType, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-A8h]@1
  char szMsg; // [sp+38h] [bp-70h]@4
  char v8; // [sp+39h] [bp-6Fh]@4
  unsigned __int16 v9; // [sp+3Ah] [bp-6Eh]@4
  char v10; // [sp+3Ch] [bp-6Ch]@4
  unsigned int v11; // [sp+3Dh] [bp-6Bh]@4
  char Dest; // [sp+41h] [bp-67h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v14; // [sp+75h] [bp-33h]@4
  unsigned __int64 v15; // [sp+90h] [bp-18h]@4
  CPlayer *v16; // [sp+B0h] [bp+8h]@1

  v16 = this;
  v3 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v6 ^ _security_cookie;
  szMsg = byBoxType;
  v8 = pItem->m_byTableCode;
  v10 = pItem->m_dwDur;
  v9 = pItem->m_wItemIndex;
  v11 = CPlayerDB::GetCharSerial(&v16->m_Param);
  v5 = CPlayerDB::GetCharNameA(&v16->m_Param);
  strcpy_0(&Dest, v5);
  pbyType = 13;
  v14 = -107;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x1Au);
}
