/*
 * Function: j_??A?$vector@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAAAEAVCUnmannedTraderUserInfo@@_K@Z
 * Address: 0x14000B0AF
 */

CUnmannedTraderUserInfo *__fastcall std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](this, _Pos);
}
