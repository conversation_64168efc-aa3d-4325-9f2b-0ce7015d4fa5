/*
 * Function: ?Init@CLogTypeDBTaskManager@@QEAA_NXZ
 * Address: 0x1402C2CD0
 */

char __fastcall CLogTypeDBTaskManager::Init(CLogTypeDBTaskManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // rax@7
  __int64 v5; // [sp+0h] [bp-48h]@1
  CRFWorldDatabase *v6; // [sp+20h] [bp-28h]@9
  CRFWorldDatabase *v7; // [sp+28h] [bp-20h]@6
  __int64 v8; // [sp+30h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+38h] [bp-10h]@7
  CLogTypeDBTaskManager *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( CLogTypeDBTaskManager::InitLogger(v10) )
  {
    v7 = (CRFWorldDatabase *)operator new(0x3F8ui64);
    if ( v7 )
    {
      CRFWorldDatabase::CRFWorldDatabase(v7);
      v9 = (CRFWorldDatabase *)v4;
    }
    else
    {
      v9 = 0i64;
    }
    v6 = v9;
    v10->m_pkWorldDB = v9;
    if ( v10->m_pkWorldDB )
    {
      if ( CLogTypeDBTaskPool::Init(&v10->m_kPool, 0x400u, 0x13C8u, v10->m_pkLogger) )
      {
        if ( _beginthread((void (__cdecl *)(void *))CLogTypeDBTaskManager::ProcThread, 0, v10) == -1i64 )
        {
          CLogTypeDBTaskManager::Log(v10, "CLogTypeDBTaskManager::Init() : _beginthread Fail!");
          result = 0;
        }
        else
        {
          v10->m_eState = 0;
          result = 1;
        }
      }
      else
      {
        CLogTypeDBTaskManager::Log(v10, "CLogTypeDBTaskManager::Init() : m_kPool.Init( %u, %u )!", 1024i64, 5064i64);
        result = 0;
      }
    }
    else
    {
      CLogTypeDBTaskManager::Log(v10, "CLogTypeDBTaskManager::Init() : new CRFWorldDatabase NULL!");
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
