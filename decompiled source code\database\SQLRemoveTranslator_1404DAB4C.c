/*
 * Function: SQLRemoveTranslator
 * Address: 0x1404DAB4C
 */

int __fastcall SQLRemoveTranslator(const char *lpszTranslator, unsigned int *lpdwUsageCount)
{
  const char *v2; // rdi@1
  unsigned int *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  int result; // eax@2

  v2 = lpszTranslator;
  v3 = lpdwUsageCount;
  v4 = ODBC___GetSetupProc("SQLRemoveTranslator");
  if ( v4 )
    result = ((int (__fastcall *)(const char *, unsigned int *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
