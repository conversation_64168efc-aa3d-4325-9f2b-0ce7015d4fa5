/*
 * Function: ??$_Copy_opt@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@PEAVCUnmannedTraderItemCodeInfo@@Urandom_access_iterator_tag@2@@std@@YAPEAVCUnmannedTraderItemCodeInfo@@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@0@0PEAV1@Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14037C700
 */

CUnmannedTraderItemCodeInfo *__fastcall std::_Copy_opt<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *,std::random_access_iterator_tag>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_First, std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Last, CUnmannedTraderItemCodeInfo *_Dest, std::random_access_iterator_tag __formal, std::_Nonscalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderItemCodeInfo *v8; // rax@6
  __int64 v10; // [sp+0h] [bp-38h]@1
  CUnmannedTraderItemCodeInfo *v11; // [sp+20h] [bp-18h]@7
  __int64 v12; // [sp+28h] [bp-10h]@4
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v13; // [sp+40h] [bp+8h]@1
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Right; // [sp+48h] [bp+10h]@1
  CUnmannedTraderItemCodeInfo *v15; // [sp+50h] [bp+18h]@1

  v15 = _Dest;
  _Right = _Last;
  v13 = _First;
  v6 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v12 = -2i64;
  while ( std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator!=(
            v13,
            _Right) )
  {
    v8 = std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator*(v13);
    CUnmannedTraderItemCodeInfo::operator=(v15, v8);
    ++v15;
    std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator++(v13);
  }
  v11 = v15;
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(v13);
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(_Right);
  return v11;
}
