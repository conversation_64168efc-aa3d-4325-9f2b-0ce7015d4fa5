/*
 * Function: j_?VerifyTruncatedDigest@HashTransformation@CryptoPP@@UEAA_NPEBE_K01@Z
 * Address: 0x1400120B7
 */

bool __fastcall CryptoPP::HashTransformation::VerifyTruncatedDigest(CryptoPP::HashTransformation *this, const char *digest, unsigned __int64 digestLength, const char *input, unsigned __int64 length)
{
  return CryptoPP::HashTransformation::VerifyTruncatedDigest(this, digest, digestLength, input, length);
}
