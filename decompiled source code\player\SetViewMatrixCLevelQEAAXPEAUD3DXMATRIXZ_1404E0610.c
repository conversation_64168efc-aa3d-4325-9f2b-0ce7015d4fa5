/*
 * Function: ?SetViewMatrix@CLevel@@QEAAXPEAUD3DXMATRIX@@@Z
 * Address: 0x1404E0610
 */

void __fastcall CLevel::SetViewMatrix(CLevel *this, struct D3DXMATRIX *a2)
{
  CLevel *v2; // rdi@1
  float v3; // xmm2_4@1
  float v4; // xmm3_4@1
  float v5; // xmm4_4@1
  float v6; // [sp+20h] [bp-18h]@1
  float v7; // [sp+24h] [bp-14h]@1
  float v8; // [sp+28h] [bp-10h]@1

  v2 = this;
  v6 = 0.0;
  v7 = 0.0;
  v8 = 0.0;
  memcpy_0(&this->mMatView, a2, 0x40ui64);
  memcpy_0(&stru_184A79A6C, &v2->mMatView, 0x40ui64);
  R3GetQuakeVector(&v6);
  v3 = v6;
  v4 = v7;
  v5 = v8;
  v2->mMatView._41 = v6 + v2->mMatView._41;
  v2->mMatView._42 = v4 + v2->mMatView._42;
  v2->mMatView._43 = v5 + v2->mMatView._43;
  stru_184A79A6C._41 = stru_184A79A6C._41 + v3;
  stru_184A79A6C._42 = stru_184A79A6C._42 + v4;
  stru_184A79A6C._43 = stru_184A79A6C._43 + v5;
}
