/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAURECV_DATA@@PEAPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@stdext@@YAPEAPEAURECV_DATA@@PEAPEAU1@00AEAV?$allocator@PEAURECV_DATA@@@std@@@Z
 * Address: 0x14000B280
 */

RECV_DATA **__fastcall stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, RECV_DATA **_Last, RECV_DATA **_Dest, std::allocator<RECV_DATA *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<RECV_DATA * *,RECV_DATA * *,std::allocator<RECV_DATA *>>(
           _First,
           _Last,
           _Dest,
           _<PERSON>);
}
