/*
 * Function: ?AfxInitialize@@YAHHK@Z
 * Address: 0x140676BE0
 */

signed __int64 __fastcall AfxInitialize(int bDLL, unsigned int dwVersion)
{
  struct AFX_MODULE_STATE *v3; // [sp+20h] [bp-18h]@1
  int v4; // [sp+40h] [bp+8h]@1
  unsigned int v5; // [sp+48h] [bp+10h]@1

  v5 = dwVersion;
  v4 = bDLL;
  v3 = AfxGetModuleState();
  v3->m_bDLL = v4;
  if ( v5 > 0x800 && AfxAssertFailedLine("f:\\dd\\vctools\\vc7libs\\ship\\atlmfc\\src\\mfc\\appmodul.cpp", 42) )
    __debugbreak();
  v3->m_dwVersion = v5;
  if ( !v4 )
    _setmbcp(-3);
  return 1i64;
}
