/*
 * Function: ?Push@CTalkCrystalCombineManager@@IEAAEPEAU_db_con@_STORAGE_LIST@@EE@Z
 * Address: 0x1404312E0
 */

char __fastcall CTalkCrystalCombineManager::Push(CTalkCrystalCombineManager *this, _STORAGE_LIST::_db_con *pItem, char byUseCount, char byClientIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-C8h]@1
  char v8; // [sp+30h] [bp-98h]@4
  int pMixIndex; // [sp+44h] [bp-84h]@4
  char pbyTableCode; // [sp+64h] [bp-64h]@4
  unsigned __int16 v11; // [sp+84h] [bp-44h]@4
  int nNeedItemNum; // [sp+A4h] [bp-24h]@4
  _talk_crystal_matrial_combine_node *v13; // [sp+B8h] [bp-10h]@6
  CTalkCrystalCombineManager *v14; // [sp+D0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *pItema; // [sp+D8h] [bp+10h]@1
  char v16; // [sp+E0h] [bp+18h]@1
  char v17; // [sp+E8h] [bp+20h]@1

  v17 = byClientIndex;
  v16 = byUseCount;
  pItema = pItem;
  v14 = this;
  v4 = &v7;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  pMixIndex = -1;
  pbyTableCode = -1;
  v11 = -1;
  nNeedItemNum = -1;
  v8 = CTalkCrystalCombineManager::CheckMixItem(v14, pItem, &pMixIndex, &pbyTableCode, &v11, &nNeedItemNum);
  if ( v8 )
  {
    result = v8;
  }
  else
  {
    v13 = CTalkCrystalCombineManager::GetMixNode(v14, pMixIndex);
    if ( !v13 )
      v13 = CTalkCrystalCombineManager::MakeMixNode(v14, pMixIndex, nNeedItemNum, pbyTableCode, v11);
    if ( v13 )
    {
      if ( _talk_crystal_matrial_combine_node::Push(v13, pItema, v16, v17) )
        result = 0;
      else
        result = 12;
    }
    else
    {
      result = 12;
    }
  }
  return result;
}
