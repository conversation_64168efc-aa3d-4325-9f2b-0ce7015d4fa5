/*
 * Function: j_?UpdateClearRerservedDayInfo@CGuildBattleController@@QEAA_NKKKK@Z
 * Address: 0x1400042A5
 */

bool __fastcall CGuildBattleController::UpdateClearRerservedDayInfo(CGuildBattleController *this, unsigned int dwStartSLID, unsigned int dwEndSLID, unsigned int dwStartSID, unsigned int dwEndSID)
{
  return CGuildBattleController::UpdateClearRerservedDayInfo(this, dwStartSLID, dwEndSLID, dwStartSID, dwEndSID);
}
