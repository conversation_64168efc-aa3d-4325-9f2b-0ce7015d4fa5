/*
 * Function: ??$_Allocate@PEAUMessageRange@MeterFilter@CryptoPP@@@std@@YAPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAPEAU123@@Z
 * Address: 0x140601750
 */

void *__fastcall std::_Allocate<CryptoPP::MeterFilter::MessageRange *>(unsigned __int64 a1)
{
  std::bad_alloc v2; // [sp+20h] [bp-28h]@4
  unsigned __int64 v3; // [sp+50h] [bp+8h]@1

  v3 = a1;
  if ( a1 )
  {
    if ( 0xFFFFFFFFFFFFFFFFui64 / a1 < 8 )
    {
      std::bad_alloc::bad_alloc(&v2, 0i64);
      CxxThrowException_0((__int64)&v2, (__int64)&TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v3 = 0i64;
  }
  return operator new(8 * v3);
}
