/*
 * Function: ??0?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA@PEAPEAVCMoveMapLimitRight@@@Z
 * Address: 0x1403AF860
 */

void __fastcall std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, CMoveMapLimitRight **_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *v5; // [sp+30h] [bp+8h]@1
  CMoveMapLimitRight **v6; // [sp+38h] [bp+10h]@1

  v6 = _Ptr;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &>((std::_Ranit<CMoveMapLimitRight *,__int64,CMoveMapLimitRight * const *,CMoveMapLimitRight * const &> *)&v5->_Mycont);
  v5->_Myptr = v6;
}
