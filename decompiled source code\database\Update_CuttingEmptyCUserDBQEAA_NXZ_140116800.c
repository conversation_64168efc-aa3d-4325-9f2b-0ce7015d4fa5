/*
 * Function: ?Update_CuttingEmpty@CUserDB@@QEAA_NXZ
 * Address: 0x140116800
 */

char __fastcall CUserDB::Update_CuttingEmpty(CUserDB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUserDB *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _CUTTING_DB_BASE::Init(&v5->m_AvatorData.dbCutting);
  v5->m_bDataUpdate = 1;
  return 1;
}
