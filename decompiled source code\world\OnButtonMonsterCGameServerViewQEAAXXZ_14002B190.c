/*
 * Function: ?OnButtonMonster@CGameServerView@@QEAAXXZ
 * Address: 0x14002B190
 */

void __fastcall CGameServerView::OnButtonMonster(CGameServerView *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
    CMsgData::PackingMsg(&stru_1415B7048, 0x3EBu, 0, 0, 0);
}
