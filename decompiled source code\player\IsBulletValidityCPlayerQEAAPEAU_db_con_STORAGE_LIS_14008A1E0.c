/*
 * Function: ?IsBulletValidity@CPlayer@@QEAAPEAU_db_con@_STORAGE_LIST@@G@Z
 * Address: 0x14008A1E0
 */

_STORAGE_LIST::_db_con *__fastcall CPlayer::IsBulletValidity(CPlayer *this, unsigned __int16 wBulletSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *result; // rax@7
  __int64 v5; // [sp+0h] [bp-68h]@1
  _STORAGE_LIST::_db_con *v6; // [sp+20h] [bp-48h]@5
  char *v7; // [sp+28h] [bp-40h]@6
  int nCashType; // [sp+30h] [bp-38h]@10
  _base_fld *v9; // [sp+38h] [bp-30h]@12
  char v10; // [sp+40h] [bp-28h]@12
  _base_fld *v11; // [sp+48h] [bp-20h]@14
  int v12; // [sp+50h] [bp-18h]@17
  int j; // [sp+54h] [bp-14h]@17
  __int64 v14; // [sp+58h] [bp-10h]@15
  CPlayer *v15; // [sp+70h] [bp+8h]@1

  v15 = this;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( wBulletSerial == 255 )
    goto LABEL_27;
  v6 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v15->m_Param.m_dbEmbellish.m_nListNum, wBulletSerial);
  if ( !v6 )
    goto LABEL_27;
  v7 = &v15->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
  if ( !*v7 )
    return 0i64;
  if ( v6->m_byTableCode != 10 )
    return 0i64;
  nCashType = GetUsePcCashType(v6->m_byTableCode, v6->m_wItemIndex);
  if ( !CPlayer::IsUsableAccountType(v15, nCashType) )
  {
    CPlayer::SendMsg_PremiumCashItemUse(v15, 0xFFFFu);
    return 0i64;
  }
  v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v7 + 3));
  v10 = 0;
  if ( !strncmp(&v9[4].m_strCode[16], "-1", 2ui64) )
  {
    v10 = 1;
  }
  else
  {
    v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v6->m_wItemIndex);
    v14 = strlen_0(v11[4].m_strCode) >= 0x40 ? 64i64 : strlen_0(v11[4].m_strCode);
    v12 = v14;
    for ( j = 0; j < v12; ++j )
    {
      if ( strchr(&v9[4].m_strCode[16], v11[4].m_strCode[j]) )
        return v6;
    }
  }
  if ( v10 )
    result = v6;
  else
LABEL_27:
    result = 0i64;
  return result;
}
