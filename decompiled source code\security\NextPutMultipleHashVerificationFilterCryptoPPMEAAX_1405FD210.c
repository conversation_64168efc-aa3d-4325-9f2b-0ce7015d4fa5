/*
 * Function: ?NextPutMultiple@HashVerificationFilter@CryptoPP@@MEAAXPEBE_K@Z
 * Address: 0x1405FD210
 */

void __fastcall CryptoPP::HashVerificationFilter::NextPutMultiple(CryptoPP::HashVerificationFilter *this, const unsigned __int8 *a2, __int64 a3)
{
  CryptoPP::BufferedTransformation *v3; // rax@2
  CryptoPP::HashVerificationFilter *v4; // [sp+30h] [bp+8h]@1
  unsigned __int8 *v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = (unsigned __int8 *)a2;
  v4 = this;
  ((void (*)(void))this->m_hashModule->vfptr[1].Clone)();
  if ( v4->m_flags & 2 )
  {
    LODWORD(v3) = ((int (__fastcall *)(CryptoPP::HashVerificationFilter *))v4->vfptr[20].Clone)(v4);
    CryptoPP::BufferedTransformation::Put(v3, v5, v6);
  }
}
