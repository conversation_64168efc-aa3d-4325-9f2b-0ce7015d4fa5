/*
 * Function: ?CompleteReRegist@CUnmannedTraderUserInfo@@QEAAXPEADPEAVCLogFile@@@Z
 * Address: 0x1403558E0
 */

void __fastcall CUnmannedTraderUserInfo::CompleteReRegist(CUnmannedTraderUserInfo *this, char *pLoadData, CLogFile *pkLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@8
  int v6; // edx@14
  unsigned int v7; // edi@21
  unsigned int v8; // eax@26
  CUnmannedTraderTaxRateManager *v9; // rax@27
  CHonorGuild *v10; // rax@27
  int v11; // eax@28
  CUnmannedTraderTaxRateManager *v12; // rax@31
  CHonorGuild *v13; // rax@31
  unsigned __int16 v14; // ax@35
  __int64 v15; // [sp+0h] [bp-218h]@1
  char *pszFileName; // [sp+20h] [bp-1F8h]@8
  char *pbyProcRet; // [sp+28h] [bp-1F0h]@8
  unsigned int dwLeftDalant; // [sp+30h] [bp-1E8h]@26
  char *v19; // [sp+38h] [bp-1E0h]@26
  CPlayer *v20; // [sp+40h] [bp-1D8h]@4
  _STORAGE_LIST::_storage_con *v21; // [sp+48h] [bp-1D0h]@4
  char *v22; // [sp+50h] [bp-1C8h]@4
  char pQryData; // [sp+70h] [bp-1A8h]@4
  unsigned __int8 v24; // [sp+71h] [bp-1A7h]@4
  unsigned __int16 v25; // [sp+72h] [bp-1A6h]@4
  unsigned int v26; // [sp+78h] [bp-1A0h]@4
  char v27[3]; // [sp+7Dh] [bp-19Bh]@10
  int v28[28]; // [sp+80h] [bp-198h]@10
  _unmannedtrader_re_regist_result_zocl v29; // [sp+F0h] [bp-128h]@4
  unsigned __int8 j; // [sp+1C4h] [bp-54h]@4
  unsigned int v31; // [sp+1C8h] [bp-50h]@27
  unsigned int dwTax; // [sp+1CCh] [bp-4Ch]@27
  char pbyType; // [sp+1D4h] [bp-44h]@35
  char v34; // [sp+1D5h] [bp-43h]@35
  int v35; // [sp+1E4h] [bp-34h]@8
  __int64 v36; // [sp+1E8h] [bp-30h]@8
  char *v37; // [sp+1F0h] [bp-28h]@26
  int n; // [sp+1F8h] [bp-20h]@26
  int v39; // [sp+1FCh] [bp-1Ch]@27
  int v40; // [sp+200h] [bp-18h]@27
  int v41; // [sp+204h] [bp-14h]@28
  int v42; // [sp+208h] [bp-10h]@31
  int v43; // [sp+20Ch] [bp-Ch]@31
  CUnmannedTraderUserInfo *v44; // [sp+220h] [bp+8h]@1
  char *v45; // [sp+228h] [bp+10h]@1
  CLogFile *pkLoggera; // [sp+230h] [bp+18h]@1

  pkLoggera = pkLogger;
  v45 = pLoadData;
  v44 = this;
  v3 = &v15;
  for ( i = 132i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v20 = CUnmannedTraderUserInfo::FindOwner(v44);
  v21 = 0i64;
  v22 = v45;
  pQryData = 0;
  memset(&v24, 0, 0x5Bui64);
  v26 = v44->m_dwUserSerial;
  pQryData = *v45;
  v25 = v44->m_wInx;
  v29.byNum = v45[4];
  for ( j = 0; j < (signed int)(unsigned __int8)v22[4]; ++j )
  {
    v29.List[j].byRet = v22[28 * j + 12];
    v29.List[j].bRegist = v22[28 * j + 13];
    v29.List[j].wItemSerial = *(_WORD *)&v22[28 * j + 14];
    v29.List[j].dwPrice = *(_DWORD *)&v22[28 * j + 28];
    v29.List[j].dwRegedSerial = *(_DWORD *)&v22[28 * j + 32];
    v29.List[j].dwListIndex = *(_DWORD *)&v22[28 * j + 20];
    v29.List[j].dwTax = *(_DWORD *)&v22[28 * j + 16];
    v21 = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                           (_STORAGE_LIST *)&v20->m_Param.m_dbInven.m_nListNum,
                                           *(_WORD *)&v22[28 * j + 14]);
    if ( v21 )
    {
      if ( v22[28 * j + 13] )
      {
        if ( v22[28 * j + 12] )
        {
          if ( (unsigned __int8)v22[28 * j + 12] == 202 || v22[28 * j + 12] == 24 )
            CPlayer::AddDalant(v20, *(_DWORD *)&v22[28 * j + 16], 1);
          if ( CUnmannedTraderUserInfo::CompleteCancelRegistItem(
                 v44,
                 *(_DWORD *)&v22[28 * j + 32],
                 *(_WORD *)&v22[28 * j + 14],
                 pkLoggera) )
          {
            _STORAGE_LIST::_storage_con::lock(v21, 0);
          }
          else
          {
            v7 = (unsigned __int8)v22[28 * j + 12];
            LODWORD(pbyProcRet) = *(_WORD *)&v22[28 * j + 14];
            LODWORD(pszFileName) = *(_DWORD *)&v22[28 * j + 32];
            CLogFile::Write(
              pkLoggera,
              "CUnmannedTraderUserInfo::CompleteReRegist(...) : if( pkQuery->List[i].bRegist && 0 != pkQuery->List[i].byP"
              "rocRet(%u) )CompleteCancelRegistItem( pkQuery->List[%u].dwRegedSerial(%u), pkQuery->List[%u].wItemSerial(%u) Fail!",
              v7,
              j);
          }
        }
        else
        {
          pbyProcRet = &v22[28 * j + 12];
          pszFileName = (char *)pkLoggera;
          if ( CUnmannedTraderUserInfo::CompleteReRegistItem(
                 v44,
                 *(_DWORD *)&v22[28 * j + 32],
                 *(_WORD *)&v22[28 * j + 14],
                 *(_DWORD *)&v22[28 * j + 28],
                 pkLoggera,
                 pbyProcRet) )
          {
            v37 = v20->m_szItemHistoryFileName;
            v8 = CPlayerDB::GetDalant(&v20->m_Param);
            n = v20->m_ObjID.m_wIndex;
            v19 = v37;
            dwLeftDalant = v8;
            LODWORD(pbyProcRet) = *(_DWORD *)&v22[28 * j + 16];
            LODWORD(pszFileName) = *(_DWORD *)&v22[28 * j + 28];
            CMgrAvatorItemHistory::re_reg_auto_trade(
              &CPlayer::s_MgrItemHistory,
              n,
              *(_DWORD *)&v22[28 * j + 32],
              (_STORAGE_LIST::_db_con *)v21,
              (unsigned int)pszFileName,
              (unsigned int)pbyProcRet,
              v8,
              v37);
            if ( CHolyStoneSystem::GetHolyMasterRace(&g_HolySys) == -1 )
            {
              v31 = 75 * *(_DWORD *)&v22[28 * j + 16] / 0x64u;
              dwTax = v31 / 2;
              v39 = CPlayerDB::GetRaceCode(&v20->m_Param);
              v9 = CUnmannedTraderTaxRateManager::Instance();
              CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(v9, v39, dwTax);
              v40 = CPlayerDB::GetRaceCode(&v20->m_Param);
              v10 = CHonorGuild::Instance();
              CHonorGuild::SetGuildMaintainMoney(v10, v40, v31, v20->m_dwObjSerial);
            }
            else
            {
              v41 = CPlayerDB::GetRaceCode(&v20->m_Param);
              v11 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
              if ( v41 == v11 )
              {
                v31 = 75 * *(_DWORD *)&v22[28 * j + 16] / 0x64u;
                dwTax = v31 / 2;
              }
              else
              {
                v31 = 50 * *(_DWORD *)&v22[28 * j + 16] / 0x64u;
                dwTax = v31 / 2;
              }
              v42 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
              v12 = CUnmannedTraderTaxRateManager::Instance();
              CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(v12, v42, dwTax);
              v43 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
              v13 = CHonorGuild::Instance();
              CHonorGuild::SetGuildMaintainMoney(v13, v43, v31, v20->m_dwObjSerial);
            }
          }
          else
          {
            v29.List[j].byRet = v22[28 * j + 12];
            CPlayer::AddDalant(v20, *(_DWORD *)&v22[28 * j + 16], 1);
            v27[8 * v24] = 12;
            v28[2 * (unsigned __int64)v24++] = *(_DWORD *)&v22[28 * j + 32];
          }
        }
      }
      else if ( CUnmannedTraderUserInfo::CompleteCancelRegistItem(
                  v44,
                  *(_DWORD *)&v22[28 * j + 32],
                  *(_WORD *)&v22[28 * j + 14],
                  pkLoggera) )
      {
        _STORAGE_LIST::_storage_con::lock(v21, 0);
        v6 = v20->m_ObjID.m_wIndex;
        pszFileName = v20->m_szItemHistoryFileName;
        CMgrAvatorItemHistory::self_cancel_auto_trade(
          &CPlayer::s_MgrItemHistory,
          v6,
          *(_DWORD *)&v22[28 * j + 32],
          (_STORAGE_LIST::_db_con *)v21,
          v20->m_szItemHistoryFileName);
      }
      else
      {
        LODWORD(pszFileName) = *(_WORD *)&v22[28 * j + 14];
        CLogFile::Write(
          pkLoggera,
          "CUnmannedTraderUserInfo::CompleteReRegist(...) : if( !pkQuery->List[i].bRegist )CompleteCancelRegistItem( pkQu"
          "ery->List[%u].dwRegedSerial(%u), pkQuery->List[%u].wItemSerial(%u) Fail!",
          j,
          *(_DWORD *)&v22[28 * j + 32]);
      }
    }
    else
    {
      v22[28 * j + 12] = 25;
      v29.List[j].byRet = 25;
      v35 = *(_WORD *)&v22[28 * j + 14];
      v36 = 28i64 * j;
      v5 = CPlayerDB::GetCharNameA(&v20->m_Param);
      LODWORD(pbyProcRet) = v35;
      LODWORD(pszFileName) = *(_DWORD *)&v22[v36 + 32];
      CLogFile::Write(
        pkLoggera,
        "CUnmannedTraderUserInfo::CompleteReRegist(...)\r\n"
        "\t\tOnwer : Serial(%u) Name(%s)\r\n"
        "\t\tdwRegistSerial(%u)\r\n"
        "\t\tpkRegister->m_Param.m_dbInven.GetPtrFromSerial( wItemSerial(%u) ) NULL!\r\n",
        v20->m_dwObjSerial,
        v5);
      if ( v22[28 * j + 13] )
        CPlayer::AddDalant(v20, *(_DWORD *)&v22[28 * j + 16], 1);
      v27[8 * v24] = 11;
      v28[2 * (unsigned __int64)v24++] = *(_DWORD *)&v22[28 * j + 32];
    }
  }
  if ( (signed int)v24 > 0 )
    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -109, &pQryData, 92);
  v29.dwLeftDalant = CPlayerDB::GetDalant(&v20->m_Param);
  pbyType = 30;
  v34 = 37;
  v14 = _unmannedtrader_re_regist_result_zocl::size(&v29);
  CNetProcess::LoadSendMsg(unk_1414F2088, v44->m_wInx, &pbyType, (char *)&v29, v14);
}
