/*
 * CDatabaseInitializationManager.h - Modern Database Initialization System
 * Refactored from decompiled C database initialization functions
 * Provides comprehensive database setup and connection management
 */

#pragma once

#include <string>
#include <memory>
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <atomic>
#include <mutex>

// Forward declarations
class CMainThread;
class CRFNewDatabase;
class CRFWorldDatabase;
class CUserDB;
class CLogTypeDBTaskManager;
class LtdWriter;

namespace NexusProtection {
namespace Database {

/**
 * Database initialization result codes
 */
enum class DatabaseInitResult : int {
    Success = 1,
    Failure = 0,
    ODBCConfigFailed = -1,
    ConnectionFailed = -2,
    AuthenticationFailed = -3,
    WorldDBFailed = -4,
    LogDBFailed = -5,
    TaskManagerFailed = -6,
    SystemError = -7
};

/**
 * Database initialization phase
 */
enum class DatabaseInitPhase : int {
    PreInit = 0,
    ODBCConfiguration = 1,
    WorldDatabaseInit = 2,
    GameDatabaseInit = 3,
    LogDatabaseInit = 4,
    TaskManagerInit = 5,
    PostInit = 6,
    Complete = 7
};

/**
 * Database connection configuration
 */
struct DatabaseConnectionConfig {
    std::string odbcName;
    std::string serverIP;
    std::string databaseName;
    std::string accountName;
    std::string password;
    uint16_t port{1433};
    uint32_t connectionTimeout{30};
    uint32_t queryTimeout{60};
    bool enableLogging{true};
    bool autoReconnect{true};
    
    DatabaseConnectionConfig() = default;
    
    bool IsValid() const {
        return !odbcName.empty() && !serverIP.empty() && 
               !databaseName.empty() && !accountName.empty();
    }
};

/**
 * Database initialization context
 */
struct DatabaseInitContext {
    CMainThread* pMainThread{nullptr};
    DatabaseConnectionConfig config;
    std::string logPath{"..\\ZoneServerLog\\"};
    bool enableDetailedLogging{false};
    std::chrono::system_clock::time_point startTime;
    
    DatabaseInitContext() : startTime(std::chrono::system_clock::now()) {}
    
    bool IsValid() const {
        return pMainThread != nullptr && config.IsValid();
    }
};

/**
 * Database initialization result with detailed information
 */
struct DatabaseInitializationResult {
    DatabaseInitResult result{DatabaseInitResult::Success};
    DatabaseInitPhase completedPhase{DatabaseInitPhase::PreInit};
    std::string errorMessage;
    std::chrono::milliseconds initializationTime{0};
    std::vector<std::string> warnings;
    
    // Database instances created
    std::shared_ptr<CRFWorldDatabase> pWorldDatabase;
    std::shared_ptr<CUserDB> pUserDatabase;
    std::shared_ptr<CLogTypeDBTaskManager> pTaskManager;
    std::shared_ptr<LtdWriter> pLtdWriter;
    
    DatabaseInitializationResult() = default;
    
    bool IsSuccess() const {
        return result == DatabaseInitResult::Success;
    }
    
    std::string GetResultString() const {
        switch (result) {
            case DatabaseInitResult::Success: return "Success";
            case DatabaseInitResult::Failure: return "General failure";
            case DatabaseInitResult::ODBCConfigFailed: return "ODBC configuration failed";
            case DatabaseInitResult::ConnectionFailed: return "Database connection failed";
            case DatabaseInitResult::AuthenticationFailed: return "Authentication failed";
            case DatabaseInitResult::WorldDBFailed: return "World database initialization failed";
            case DatabaseInitResult::LogDBFailed: return "Log database initialization failed";
            case DatabaseInitResult::TaskManagerFailed: return "Task manager initialization failed";
            case DatabaseInitResult::SystemError: return "System error";
            default: return "Unknown error";
        }
    }
};

/**
 * Database initialization statistics
 */
struct DatabaseInitStats {
    std::atomic<uint64_t> totalInitializations{0};
    std::atomic<uint64_t> successfulInitializations{0};
    std::atomic<uint64_t> failedInitializations{0};
    std::atomic<uint64_t> odbcConfigurations{0};
    std::atomic<uint64_t> connectionAttempts{0};
    std::chrono::system_clock::time_point lastInitialization;
    std::chrono::milliseconds averageInitTime{0};
    
    DatabaseInitStats() : lastInitialization(std::chrono::system_clock::now()) {}
    
    void RecordInitialization(bool success, std::chrono::milliseconds duration) {
        totalInitializations++;
        if (success) {
            successfulInitializations++;
        } else {
            failedInitializations++;
        }
        lastInitialization = std::chrono::system_clock::now();
        
        // Update average time (simple moving average)
        uint64_t total = totalInitializations.load();
        averageInitTime = std::chrono::milliseconds(
            (averageInitTime.count() * (total - 1) + duration.count()) / total
        );
    }
    
    double GetSuccessRate() const {
        uint64_t total = totalInitializations.load();
        return total > 0 ? static_cast<double>(successfulInitializations.load()) / total * 100.0 : 0.0;
    }
};

/**
 * Modern Database Initialization Manager
 * Refactored from legacy decompiled C functions
 */
class CDatabaseInitializationManager {
public:
    /**
     * Constructor
     */
    CDatabaseInitializationManager();
    
    /**
     * Destructor
     */
    virtual ~CDatabaseInitializationManager();
    
    /**
     * Initialize complete database system
     * Refactored from: DatabaseInitCMainThreadAEAA_NPEAD0Z_1401ED230.c
     * @param context Initialization context
     * @return Detailed initialization result
     */
    DatabaseInitializationResult InitializeDatabaseSystem(const DatabaseInitContext& context);
    
    /**
     * Initialize world database
     * Refactored from: DatabaseInitCMainThreadAEAA_NPEAD0Z_1401ED230.c (lines 43-72)
     * @param config Database configuration
     * @param logPath Log file path
     * @return World database instance or nullptr
     */
    std::shared_ptr<CRFWorldDatabase> InitializeWorldDatabase(const DatabaseConnectionConfig& config, 
                                                             const std::string& logPath);
    
    /**
     * Initialize game database system
     * Refactored from: _GameDataBaseInitCMainThreadAEAA_NXZ_1401ED4F0.c
     * @param pMainThread Main thread instance
     * @return true if successful
     */
    bool InitializeGameDatabase(CMainThread* pMainThread);
    
    /**
     * Initialize log database and task manager
     * Refactored from: InitDBCLogTypeDBTaskManagerQEAA_NPEBD0Z_1402C2E50.c
     * @param config Database configuration
     * @return Task manager instance or nullptr
     */
    std::shared_ptr<CLogTypeDBTaskManager> InitializeLogDatabase(const DatabaseConnectionConfig& config);
    
    /**
     * Initialize LTD writer log database
     * Refactored from: InitLogDBLtdWriterQEAA_NPEAD0Z_14024A850.c
     * @param config Database configuration
     * @return LTD writer instance or nullptr
     */
    std::shared_ptr<LtdWriter> InitializeLtdWriter(const DatabaseConnectionConfig& config);
    
    /**
     * Configure ODBC connection
     * @param config Database configuration
     * @return true if successful
     */
    bool ConfigureODBC(const DatabaseConnectionConfig& config);
    
    /**
     * Validate database configuration
     * @param config Configuration to validate
     * @return true if valid
     */
    bool ValidateConfiguration(const DatabaseConnectionConfig& config);
    
    /**
     * Test database connection
     * @param config Database configuration
     * @return true if connection successful
     */
    bool TestConnection(const DatabaseConnectionConfig& config);
    
    /**
     * Get initialization statistics
     * @return Current statistics
     */
    const DatabaseInitStats& GetStatistics() const { return m_stats; }
    
    /**
     * Reset statistics
     */
    void ResetStatistics();
    
    /**
     * Set progress callback for initialization monitoring
     * @param callback Progress callback function
     */
    void SetProgressCallback(std::function<void(DatabaseInitPhase, const std::string&)> callback);
    
    /**
     * Enable/disable detailed logging
     * @param bEnable Enable flag
     */
    void SetDetailedLogging(bool bEnable) { m_bDetailedLogging = bEnable; }

protected:
    /**
     * Execute initialization phase
     * @param phase Phase to execute
     * @param context Initialization context
     * @return true if successful
     */
    virtual bool ExecuteInitializationPhase(DatabaseInitPhase phase, const DatabaseInitContext& context);
    
    /**
     * Configure ODBC data source
     * @param config Database configuration
     * @return true if successful
     */
    virtual bool ConfigureODBCDataSource(const DatabaseConnectionConfig& config);
    
    /**
     * Establish database connection
     * @param config Database configuration
     * @return Database instance or nullptr
     */
    virtual std::shared_ptr<CRFNewDatabase> EstablishConnection(const DatabaseConnectionConfig& config);
    
    /**
     * Validate database credentials
     * @param config Database configuration
     * @return true if valid
     */
    virtual bool ValidateCredentials(const DatabaseConnectionConfig& config);
    
    /**
     * Setup database logging
     * @param pDatabase Database instance
     * @param logPath Log file path
     * @param databaseName Database name
     */
    virtual void SetupDatabaseLogging(CRFNewDatabase* pDatabase, const std::string& logPath, 
                                    const std::string& databaseName);

private:
    DatabaseInitStats m_stats;
    std::function<void(DatabaseInitPhase, const std::string&)> m_progressCallback;
    bool m_bDetailedLogging{false};
    mutable std::mutex m_statsMutex;
    
    /**
     * Create initialization result with timing
     * @param result Result code
     * @param phase Completed phase
     * @param startTime Initialization start time
     * @param errorMessage Error message (optional)
     * @return Complete initialization result
     */
    DatabaseInitializationResult CreateResult(DatabaseInitResult result, DatabaseInitPhase phase,
                                            std::chrono::high_resolution_clock::time_point startTime,
                                            const std::string& errorMessage = "");
    
    /**
     * Update progress and call callback
     * @param phase Current phase
     * @param message Progress message
     */
    void UpdateProgress(DatabaseInitPhase phase, const std::string& message);
    
    /**
     * Log initialization step
     * @param message Log message
     * @param isError Whether this is an error message
     */
    void LogStep(const std::string& message, bool isError = false);
    
    // Disable copy constructor and assignment operator
    CDatabaseInitializationManager(const CDatabaseInitializationManager&) = delete;
    CDatabaseInitializationManager& operator=(const CDatabaseInitializationManager&) = delete;
};

/**
 * Legacy compatibility functions
 * Maintain exact signatures for backward compatibility
 */
namespace LegacyCompatibility {
    /**
     * Legacy database initialization wrapper
     * @param pMainThread Main thread instance
     * @param pszDBName Database name
     * @param pszDBIP Database IP address
     * @return 1 if successful, 0 if failed
     */
    char DatabaseInit_Legacy(CMainThread* pMainThread, char* pszDBName, char* pszDBIP);
    
    /**
     * Legacy log database initialization wrapper
     * @param pLtdWriter LTD writer instance
     * @param szDBName Database name
     * @param szIP Database IP address
     * @return 1 if successful, 0 if failed
     */
    char InitLogDB_Legacy(LtdWriter* pLtdWriter, char* szDBName, char* szIP);
    
    /**
     * Legacy task manager initialization wrapper
     * @param szDBName Database name
     * @param szIP Database IP address
     * @return 1 if successful, 0 if failed
     */
    char InitDB_TaskManager_Legacy(const char* szDBName, const char* szIP);
}

/**
 * Utility functions for database initialization
 */
namespace DatabaseInitUtils {
    /**
     * Convert legacy database name to modern string
     * @param pszDBName Legacy database name
     * @return Modern string
     */
    std::string ConvertDatabaseName(const char* pszDBName);
    
    /**
     * Parse database connection string
     * @param connectionString Connection string to parse
     * @return Database configuration
     */
    DatabaseConnectionConfig ParseConnectionString(const std::string& connectionString);
    
    /**
     * Build ODBC connection string
     * @param config Database configuration
     * @return ODBC connection string
     */
    std::string BuildConnectionString(const DatabaseConnectionConfig& config);
    
    /**
     * Validate database server accessibility
     * @param serverIP Server IP address
     * @param port Server port
     * @return true if accessible
     */
    bool ValidateServerAccessibility(const std::string& serverIP, uint16_t port);
}

} // namespace Database
} // namespace NexusProtection
