/*
 * Function: ?DatabaseInit@CMainThread@@AEAA_NPEAD0@Z
 * Address: 0x1401ED230
 */

char __fastcall CMainThread::DatabaseInit(CMainThread *this, char *pszDBName, char *pszDBIP)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // rax@6
  char result; // al@9
  CNationSettingManager *v7; // rax@10
  CNationSettingManager *v8; // rax@10
  const char *v9; // rax@10
  CLogTypeDBTaskManager *v10; // rax@14
  CLogTypeDBTaskManager *v11; // rax@15
  __int64 v12; // [sp+0h] [bp-68h]@1
  unsigned __int16 wPort; // [sp+20h] [bp-48h]@8
  CRFWorldDatabase *v14; // [sp+30h] [bp-38h]@8
  CRFWorldDatabase *v15; // [sp+38h] [bp-30h]@5
  __int64 v16; // [sp+40h] [bp-28h]@4
  CRFWorldDatabase *v17; // [sp+48h] [bp-20h]@6
  char *passWord; // [sp+50h] [bp-18h]@10
  CMainThread *v19; // [sp+70h] [bp+8h]@1
  const char *Source; // [sp+78h] [bp+10h]@1
  const char *szServer; // [sp+80h] [bp+18h]@1

  szServer = pszDBIP;
  Source = pszDBName;
  v19 = this;
  v3 = &v12;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = -2i64;
  CLogFile::Write(&v19->m_logLoadingError, "DataBase Setting Start!! (%s : %s)", pszDBIP);
  strcpy_0(v19->m_szWorldDBName, Source);
  CWnd::SendMessageA(g_pFrame, 0xCu, 0i64, 0i64);
  if ( v19->m_pWorldDB )
    goto LABEL_20;
  v15 = (CRFWorldDatabase *)operator new(0x3F8ui64);
  if ( v15 )
  {
    CRFWorldDatabase::CRFWorldDatabase(v15);
    v17 = (CRFWorldDatabase *)v5;
  }
  else
  {
    v17 = 0i64;
  }
  v14 = v17;
  v19->m_pWorldDB = v17;
  CRFNewDatabase::SetLogFile((CRFNewDatabase *)&v19->m_pWorldDB->vfptr, "..\\ZoneServerLog\\", v19->m_szWorldDBName);
  wPort = -4103;
  if ( !CRFNewDatabase::ConfigUserODBC((CRFNewDatabase *)&v19->m_pWorldDB->vfptr, Source, szServer, Source, 0xEFF9u) )
  {
    MyMessageBox("DatabaseInit", "World DB ODBC Setting Faild!");
    return 0;
  }
  CLogFile::Write(&v19->m_logLoadingError, "World DB ODBC Config Complete!!");
  v7 = CTSingleton<CNationSettingManager>::Instance();
  passWord = (char *)CNationSettingManager::GetWorldDBPW(v7);
  v8 = CTSingleton<CNationSettingManager>::Instance();
  v9 = CNationSettingManager::GetWorldDBID(v8);
  if ( !CRFNewDatabase::StartDataBase((CRFNewDatabase *)&v19->m_pWorldDB->vfptr, v19->m_szWorldDBName, v9, passWord) )
  {
    MyMessageBox("DatabaseInit", "Connect World DB Failed!");
    return 0;
  }
  CLogFile::Write(&v19->m_logLoadingError, "Start World DataBase Complete!!");
  if ( CMainThread::_GameDataBaseInit(v19) )
  {
LABEL_20:
    v10 = CLogTypeDBTaskManager::Instance();
    if ( CLogTypeDBTaskManager::IsInitialized(v10)
      || (v11 = CLogTypeDBTaskManager::Instance(), CLogTypeDBTaskManager::InitDB(v11, Source, szServer)) )
    {
      CLogFile::Write(&v19->m_logLoadingError, "DataBase Setting Complete!! (%s : %s)", szServer, Source);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
