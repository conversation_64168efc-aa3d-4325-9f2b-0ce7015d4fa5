/*
 * Function: _std::_Tree_std::_Tmap_traits_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList____0___::_Insert_::_1_::dtor$0
 * Address: 0x140190BD0
 */

int __fastcall std::_Tree_std::_Tmap_traits_std::basic_string_char_std::char_traits_char__std::allocator_char____AreaList_std::less_std::basic_string_char_std::char_traits_char__std::allocator_char______std::allocator_std::pair_std::basic_string_char_std::char_traits_char__std::allocator_char____const__AreaList____0___::_Insert_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  return std::basic_string<char,std::char_traits<char>,std::allocator<char>>::~basic_string<char,std::char_traits<char>,std::allocator<char>>(a2 + 136);
}
