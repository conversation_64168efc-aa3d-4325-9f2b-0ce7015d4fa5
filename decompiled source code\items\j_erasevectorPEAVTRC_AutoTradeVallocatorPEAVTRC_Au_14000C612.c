/*
 * Function: j_?erase@?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA?AV?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@2@V32@0@Z
 * Address: 0x14000C612
 */

std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::erase(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *result, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *_First, std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *_Last)
{
  return std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::erase(this, result, _First, _Last);
}
