/*
 * Function: ?MakeLoot@@YAPEAU_db_con@_STORAGE_LIST@@EG@Z
 * Address: 0x1401FB840
 */

_STORAGE_LIST::_db_con *__fastcall MakeLoot(char byTableCode, unsigned __int16 wItemIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  unsigned __int64 dwExp; // [sp+20h] [bp-48h]@4
  char v7; // [sp+28h] [bp-40h]@4
  char v8; // [sp+29h] [bp-3Fh]@5
  char v9; // [sp+2Ah] [bp-3Eh]@5
  _TimeItem_fld *v10; // [sp+30h] [bp-38h]@13
  __time32_t Time; // [sp+44h] [bp-24h]@15
  __int64 v12; // [sp+58h] [bp-10h]@4
  char v13; // [sp+70h] [bp+8h]@1
  unsigned __int16 v14; // [sp+78h] [bp+10h]@1

  v14 = wItemIndex;
  v13 = byTableCode;
  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  dwExp = __PAIR__(0xFFFFFFF, GetItemDurPoint((unsigned __int8)v13, wItemIndex));
  v7 = GetItemKindCode((unsigned __int8)v13);
  if ( v7 )
  {
    if ( v7 != 1 )
      return 0i64;
    HIDWORD(dwExp) = GetMaxParamFromExp(v14, (unsigned int)dwExp);
  }
  else
  {
    v8 = GetDefItemUpgSocketNum((unsigned __int8)v13, v14);
    v9 = 0;
    if ( (signed int)(unsigned __int8)v8 > 0 )
      v9 = rand() % (unsigned __int8)v8 + 1;
    HIDWORD(dwExp) = GetBitAfterSetLimSocket(v9);
  }
  if ( !(_S4 & 1) )
  {
    _S4 |= 1u;
    _STORAGE_LIST::_db_con::_db_con(&item_0);
  }
  _STORAGE_LIST::_db_con::Init(&item_0);
  item_0.m_byTableCode = v13;
  item_0.m_wItemIndex = v14;
  item_0.m_dwDur = (unsigned int)dwExp;
  item_0.m_dwLv = HIDWORD(dwExp);
  v10 = TimeItem::FindTimeRec((unsigned __int8)v13, v14);
  if ( v10 )
  {
    if ( v10->m_nCheckType )
    {
      _time32(&Time);
      item_0.m_byCsMethod = v10->m_nCheckType;
      item_0.m_dwT = v10->m_nUseTime + Time;
      item_0.m_dwLendRegdTime = Time;
    }
  }
  return &item_0;
}
