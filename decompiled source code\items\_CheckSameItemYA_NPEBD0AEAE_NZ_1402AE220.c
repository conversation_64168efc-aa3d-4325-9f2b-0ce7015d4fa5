/*
 * Function: ?_CheckSameItem@@YA_NPEBD0AEAE_N@Z
 * Address: 0x1402AE220
 */

char __fastcall _CheckSameItem(const char *strLinkItem, const char *strDst, char *bySelectLinkIndex, bool bStuff)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@11
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+20h] [bp-28h]@4
  int j; // [sp+24h] [bp-24h]@8
  _base_fld *v10; // [sp+28h] [bp-20h]@13
  _base_fld *v11; // [sp+30h] [bp-18h]@23
  char v12; // [sp+38h] [bp-10h]@8
  int v13; // [sp+3Ch] [bp-Ch]@12
  char *Str1; // [sp+50h] [bp+8h]@1
  char *Str2; // [sp+58h] [bp+10h]@1
  char *v16; // [sp+60h] [bp+18h]@1

  v16 = bySelectLinkIndex;
  Str2 = (char *)strDst;
  Str1 = (char *)strLinkItem;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  if ( !strncmp(Str1, "LL", 2ui64) )
  {
    v8 = 1;
  }
  else if ( !strncmp(Str1, "LR", 2ui64) )
  {
    v8 = 2;
  }
  j = 0;
  v12 = v8;
  if ( v8 )
  {
    if ( v12 == 1 )
    {
      v10 = CRecordData::GetRecord(&ItemCombineMgr::ms_tbl_ItemCombine_Link_Stuff, Str1);
      if ( v10 )
      {
        for ( j = 0; j < 100; ++j )
        {
          if ( !strncmp((const char *)&v10[1] + 64 * (signed __int64)j, "-", 1ui64)
            || !strncmp((const char *)&v10[1] + 64 * (signed __int64)j, "-1", 2ui64) )
          {
            return 0;
          }
          if ( !strncmp((const char *)&v10[1] + 64 * (signed __int64)j, Str2, 7ui64) )
          {
            *v16 = j;
            return 1;
          }
        }
      }
      result = 0;
    }
    else if ( v12 == 2 )
    {
      v11 = CRecordData::GetRecord(&ItemCombineMgr::ms_tbl_ItemCombine_Link_Result, Str1);
      if ( v11 )
      {
        for ( j = 0; j < 100; ++j )
        {
          if ( !strncmp((const char *)&v11[1] + 64 * (signed __int64)j, "-", 1ui64)
            || !strncmp((const char *)&v11[1] + 64 * (signed __int64)j, "-1", 2ui64) )
          {
            return 0;
          }
          if ( !strncmp((const char *)&v11[1] + 64 * (signed __int64)j, Str2, 7ui64) )
          {
            *v16 = j;
            return 1;
          }
        }
      }
      result = 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    v13 = strncmp(Str1, Str2, 7ui64) == 0;
    result = v13;
  }
  return result;
}
