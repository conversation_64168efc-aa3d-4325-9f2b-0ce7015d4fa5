/*
 * Function: ?Savechedule@CBossMonsterScheduleSystem@@IEAAXPEAUBossSchedule_Map@@PEAUBossSchedule@@@Z
 * Address: 0x140419020
 */

void __fastcall CBossMonsterScheduleSystem::Savechedule(CBossMonsterScheduleSystem *this, BossSchedule_Map *pMapSchedule, BossSchedule *pSchedule)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char strBuff; // [sp+30h] [bp-68h]@5
  unsigned __int64 v7; // [sp+80h] [bp-18h]@4
  BossSchedule_Map *v8; // [sp+A8h] [bp+10h]@1
  BossSchedule *strSection; // [sp+B0h] [bp+18h]@1

  strSection = pSchedule;
  v8 = pMapSchedule;
  v3 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( pMapSchedule )
  {
    CIniFile::WriteString(
      &pMapSchedule->m_INIFile,
      pSchedule->m_strSection,
      BossSchedule::ms_KeyPair,
      pSchedule->m_strMonCode);
    BossSchedule::Make_LastTimeRespawnSystemTimeString(strSection, &strBuff, 64);
    CIniFile::WriteString(&v8->m_INIFile, strSection->m_strSection, strKey, &strBuff);
    BossSchedule::Make_LiveCountString(strSection, &strBuff, 64);
    CIniFile::WriteString(&v8->m_INIFile, strSection->m_strSection, off_140973118, &strBuff);
  }
}
