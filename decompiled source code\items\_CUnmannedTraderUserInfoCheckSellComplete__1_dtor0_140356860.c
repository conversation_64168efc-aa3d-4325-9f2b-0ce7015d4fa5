/*
 * Function: _CUnmannedTraderUserInfo::CheckSellComplete_::_1_::dtor$0
 * Address: 0x140356860
 */

void __fastcall CUnmannedTraderUserInfo::CheckSellComplete_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>((std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)(a2 + 56));
}
