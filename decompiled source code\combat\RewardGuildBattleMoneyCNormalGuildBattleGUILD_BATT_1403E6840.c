/*
 * Function: ?RewardGuildBattleMoney@CNormalGuildBattle@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E6840
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::RewardGuildBattleMoney(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGuild *v3; // rax@5
  CGuild *v4; // rax@5
  char *v5; // rax@5
  CGuild *v6; // rax@6
  char *v7; // rax@6
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *v9; // [sp+20h] [bp-28h]@5
  int v10; // [sp+28h] [bp-20h]@5
  char *v11; // [sp+30h] [bp-18h]@5
  GUILD_BATTLE::CNormalGuildBattle *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v1 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v12->m_pkWin )
  {
    v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(v12->m_pkWin);
    CGuild::PushDQSInGuildBattleRewardMoney(v6);
    v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(v12->m_pkWin);
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v12->m_kLogger,
      "CNormalGuildBattle::RewardGuildBattleMoney() : %s %u Gold",
      v7,
      10000i64);
  }
  else
  {
    v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v12->m_k1P);
    CGuild::PushDQSInGuildBattleCost(v3);
    v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v12->m_k2P);
    CGuild::PushDQSInGuildBattleCost(v4);
    v11 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v12->m_k2P);
    v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v12->m_k1P);
    v10 = 5000;
    v9 = v11;
    GUILD_BATTLE::CNormalGuildBattleLogger::Log(
      &v12->m_kLogger,
      "CNormalGuildBattle::RewardGuildBattleMoney() : Draw %s %u Gold, %s %u Gold",
      v5,
      5000i64);
  }
}
