/*
 * Function: ?SendMsg_PvpRankListNodata@CPvpUserRankingInfo@@AEAAXGEEE@Z
 * Address: 0x14032DF70
 */

void __fastcall CPvpUserRankingInfo::SendMsg_PvpRankListNodata(CPvpUserRankingInfo *this, unsigned __int16 wIndex, char byRace, char byPage, char byRet)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v9; // [sp+35h] [bp-43h]@4
  char v10; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v12; // [sp+55h] [bp-23h]@4

  v5 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byRace;
  v9 = byPage;
  v10 = byRet;
  pbyType = 13;
  v12 = 19;
  CNetProcess::LoadSendMsg(unk_1414F2088, wIndex, &pbyType, &szMsg, 3u);
}
