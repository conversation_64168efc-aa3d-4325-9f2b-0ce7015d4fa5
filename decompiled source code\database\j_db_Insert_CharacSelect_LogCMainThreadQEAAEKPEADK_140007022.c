/*
 * Function: j_?db_Insert_CharacSelect_Log@CMainThread@@QEAAEKPEADK0GEEEEE@Z
 * Address: 0x140007022
 */

char __fastcall CMainThread::db_Insert_CharacSelect_Log(CMainThread *this, unsigned int dwAccountSerial, char *szAccount, unsigned int dwCharacSerial, char *pwszCharacName, unsigned __int16 dwYear, char by<PERSON><PERSON>h, char byDay, char byHour, char byMin, char bySec)
{
  return CMainThread::db_Insert_CharacSelect_Log(
           this,
           dwAccountSerial,
           szAccount,
           dwCharacSerial,
           pwszCharacName,
           dwYear,
           byMonth,
           byDay,
           byHour,
           byMin,
           bySec);
}
