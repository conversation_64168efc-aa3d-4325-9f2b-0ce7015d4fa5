/*
 * Function: ??$_Ptr_cat@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@0@Z
 * Address: 0x1405A3A80
 */

char std::_Ptr_cat<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *>()
{
  char v1; // [sp+0h] [bp-18h]@0

  return v1;
}
