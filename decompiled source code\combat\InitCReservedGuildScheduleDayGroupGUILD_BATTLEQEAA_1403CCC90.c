/*
 * Function: ?Init@CReservedGuildScheduleDayGroup@GUILD_BATTLE@@QEAA_NI@Z
 * Address: 0x1403CCC90
 */

char __fastcall GUILD_BATTLE::CReservedGuildScheduleDayGroup::Init(GUILD_BATTLE::CReservedGuildScheduleDayGroup *this, unsigned int uiMapCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  signed __int64 v5; // rax@6
  unsigned __int8 v6; // cf@8
  unsigned __int64 v7; // rax@8
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@18
  __int64 v10; // [sp+0h] [bp-68h]@1
  unsigned int uiMapInx; // [sp+30h] [bp-38h]@15
  int count[2]; // [sp+38h] [bp-30h]@6
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v13; // [sp+40h] [bp-28h]@13
  void *v14; // [sp+48h] [bp-20h]@10
  __int64 v15; // [sp+50h] [bp-18h]@4
  GUILD_BATTLE::CReservedGuildScheduleMapGroup *v16; // [sp+58h] [bp-10h]@11
  GUILD_BATTLE::CReservedGuildScheduleDayGroup *v17; // [sp+70h] [bp+8h]@1
  unsigned int v18; // [sp+78h] [bp+10h]@1

  v18 = uiMapCnt;
  v17 = this;
  v2 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = -2i64;
  v17->m_uiMapCnt = uiMapCnt;
  if ( v17->m_uiMapCnt )
  {
    *(_QWORD *)count = v17->m_uiMapCnt;
    v5 = 344i64 * *(_QWORD *)count;
    if ( !is_mul_ok(0x158ui64, *(unsigned __int64 *)count) )
      v5 = -1i64;
    v6 = __CFADD__(v5, 8i64);
    v7 = v5 + 8;
    if ( v6 )
      v7 = -1i64;
    v14 = operator new[](v7);
    if ( v14 )
    {
      *(_DWORD *)v14 = count[0];
      `eh vector constructor iterator'(
        (char *)v14 + 8,
        0x158ui64,
        count[0],
        (void (__cdecl *)(void *))GUILD_BATTLE::CReservedGuildScheduleMapGroup::CReservedGuildScheduleMapGroup,
        (void (__cdecl *)(void *))GUILD_BATTLE::CReservedGuildScheduleMapGroup::~CReservedGuildScheduleMapGroup);
      v16 = (GUILD_BATTLE::CReservedGuildScheduleMapGroup *)((char *)v14 + 8);
    }
    else
    {
      v16 = 0i64;
    }
    v13 = v16;
    v17->m_pkList = v16;
    if ( v17->m_pkList )
    {
      for ( uiMapInx = 0; uiMapInx < v17->m_uiMapCnt; ++uiMapInx )
      {
        if ( !GUILD_BATTLE::CReservedGuildScheduleMapGroup::Init(&v17->m_pkList[uiMapInx], uiMapInx) )
        {
          v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v9,
            "CReservedGuildScheduleDayGroup::Init(%u) m_pkList[i].Init( i ) Fail",
            v17->m_uiMapCnt);
          return 0;
        }
      }
      v17->m_byToday = GetCurDay();
      v17->m_byTommorow = GetNextDay();
      result = 1;
    }
    else
    {
      v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v8,
        "CReservedGuildScheduleDayGroup::Init(%u) m_pkList = new CReservedGuildScheduleDayGroup[%u] NULL!",
        v18,
        v18);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
