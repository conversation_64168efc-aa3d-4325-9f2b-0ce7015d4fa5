/*
 * Function: ??0?$vector@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@XZ
 * Address: 0x14038F0A0
 */

void __fastcall std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<TRC_AutoTrade *> v3; // al@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  std::allocator<TRC_AutoTrade *> *v6; // [sp+28h] [bp-10h]@4
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6 = (std::allocator<TRC_AutoTrade *> *)&v5;
  std::allocator<TRC_AutoTrade *>::allocator<TRC_AutoTrade *>((std::allocator<TRC_AutoTrade *> *)&v5);
  std::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(
    (std::_Vector_val<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *)&v7->_Myfirstiter,
    v3);
  std::vector<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Buy(v7, 0i64);
}
