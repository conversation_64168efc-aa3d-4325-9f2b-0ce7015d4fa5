/*
 * Function: ?SendMsg_TowerCompleteInform@CGuardTower@@QEAAXXZ
 * Address: 0x140130A80
 */

void __fastcall CGuardTower::SendMsg_TowerCompleteInform(CGuardTower *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+38h] [bp-40h]@4
  unsigned int v5; // [sp+3Ch] [bp-3Ch]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CGuardTower *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_DWORD *)szMsg = v8->m_dwObjSerial;
  v5 = v8->m_dwMasterSerial;
  pbyType = 17;
  v7 = 23;
  CGameObject::CircleReport((CGameObject *)&v8->vfptr, &pbyType, szMsg, 8, 0);
}
