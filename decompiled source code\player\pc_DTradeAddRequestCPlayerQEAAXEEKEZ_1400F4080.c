/*
 * Function: ?pc_DTradeAddRequest@CPlayer@@QEAAXEEKE@Z
 * Address: 0x1400F4080
 */

void __fastcall CPlayer::pc_DTradeAddRequest(CPlayer *this, char bySlotIndex, char byStorageCode, unsigned int dwSerial, char byAmount)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-68h]@1
  char v8; // [sp+20h] [bp-48h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+28h] [bp-40h]@4
  CPlayer *p_pDst; // [sp+38h] [bp-30h]@4
  __int64 v11; // [sp+48h] [bp-20h]@4
  int j; // [sp+50h] [bp-18h]@26
  bool *v13; // [sp+58h] [bp-10h]@29
  CPlayer *lp_pOne; // [sp+70h] [bp+8h]@1
  char v15; // [sp+78h] [bp+10h]@1
  char v16; // [sp+80h] [bp+18h]@1
  unsigned int p_dwSerial; // [sp+88h] [bp+20h]@1

  p_dwSerial = dwSerial;
  v16 = byStorageCode;
  v15 = bySlotIndex;
  lp_pOne = this;
  v5 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = 0;
  pItem = 0i64;
  p_pDst = 0i64;
  v11 = 0i64;
  if ( DTradeEqualPerson(lp_pOne, &p_pDst) )
  {
    if ( lp_pOne->m_pCurMap->m_pMapSet->m_nMapType == 1 )
    {
      v8 = 6;
    }
    else if ( lp_pOne->m_pmTrd.bDTradeLock )
    {
      v8 = 2;
    }
    else if ( (signed int)(unsigned __int8)v15 < 15 )
    {
      if ( lp_pOne->m_pmTrd.DItemNode[(unsigned __int8)v15].bLoad )
      {
        v8 = 4;
      }
      else
      {
        pItem = _STORAGE_LIST::GetPtrFromSerial(lp_pOne->m_Param.m_pStoragePtr[(unsigned __int8)v16], p_dwSerial);
        if ( pItem )
        {
          if ( pItem->m_bLock )
          {
            v8 = 9;
          }
          else if ( pItem->m_byTableCode == 19 )
          {
            v8 = 7;
          }
          else if ( IsExchangeItem(pItem->m_byTableCode, pItem->m_wItemIndex) )
          {
            if ( !IsOverLapItem(pItem->m_byTableCode) || (unsigned __int8)byAmount <= pItem->m_dwDur && byAmount )
            {
              for ( j = 0; j < 15; ++j )
              {
                v13 = &lp_pOne->m_pmTrd.DItemNode[j].bLoad;
                if ( *v13 && *((_DWORD *)v13 + 1) == p_dwSerial )
                {
                  v8 = 6;
                  break;
                }
              }
            }
            else
            {
              v8 = 6;
            }
          }
          else
          {
            v8 = 10;
          }
        }
        else
        {
          v8 = 5;
        }
      }
    }
    else
    {
      v8 = 3;
    }
  }
  else
  {
    v8 = 1;
  }
  if ( v8 )
  {
    _DTRADE_PARAM::Init(&lp_pOne->m_pmTrd);
    CPlayer::SendMsg_DTradeCloseInform(lp_pOne, 0);
    if ( p_pDst )
    {
      _DTRADE_PARAM::Init(&p_pDst->m_pmTrd);
      CPlayer::SendMsg_DTradeCloseInform(p_pDst, 0);
    }
  }
  else
  {
    if ( !v16 )
    {
      if ( IsOverLapItem(pItem->m_byTableCode) )
      {
        if ( pItem->m_dwDur == (unsigned __int8)byAmount )
          ++lp_pOne->m_pmTrd.byEmptyInvenNum;
      }
      else
      {
        ++lp_pOne->m_pmTrd.byEmptyInvenNum;
      }
    }
    ++lp_pOne->m_pmTrd.bySellItemNum;
    _DTRADE_ITEM::SetData(&lp_pOne->m_pmTrd.DItemNode[(unsigned __int8)v15], v16, p_dwSerial, byAmount);
    CPlayer::SendMsg_DTradeAddInform(p_pDst, v15, pItem, byAmount);
    CPlayer::SendMsg_DTradeAddResult(lp_pOne, v8);
  }
}
