/*
 * Function: ?Remaintime_Personal@CBillingManager@@QEAAXPEADFJPEAU_SYSTEMTIME@@@Z
 * Address: 0x1401C3FE0
 */

void __fastcall CBillingManager::Remaintime_Personal(CBillingManager *this, char *szID, __int16 iType, int lRemaintime, _SYSTEMTIME *pstEndDate)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  _SYSTEMTIME *v8; // [sp+20h] [bp-18h]@4
  CBillingManager *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v8 = pstEndDate;
  CBilling::Remaintime_Personal(v9->m_pBill, szID, iType, lRemaintime, pstEndDate);
}
