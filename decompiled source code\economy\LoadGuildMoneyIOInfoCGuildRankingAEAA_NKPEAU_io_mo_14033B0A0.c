/*
 * Function: ?LoadGuildMoneyIOInfo@CGuildRanking@@AEAA_NKPEAU_io_money_data@@PEAH@Z
 * Address: 0x14033B0A0
 */

char __usercall CGuildRanking::LoadGuildMoneyIOInfo@<al>(CGuildRanking *this@<rcx>, unsigned int dwGuildSerial@<edx>, _io_money_data *pkIOInfo@<r8>, int *pnIOMonHisNum@<r9>, signed __int64 a5@<rax>)
{
  void *v5; // rsp@1
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v9; // [sp-30h] [bp-1968h]@1
  unsigned __int16 Dst; // [sp+0h] [bp-1938h]@4
  char Source[20]; // [sp+8h] [bp-1930h]@8
  int v12; // [sp+1Ch] [bp-191Ch]@8
  __int64 v13; // [sp+20h] [bp-1918h]@8
  __int64 v14; // [sp+28h] [bp-1910h]@8
  __int64 v15; // [sp+30h] [bp-1908h]@8
  __int64 v16; // [sp+38h] [bp-1900h]@8
  char Src[6356]; // [sp+40h] [bp-18F8h]@8
  int j; // [sp+1914h] [bp-24h]@6
  int v19; // [sp+1918h] [bp-20h]@8
  unsigned __int64 v20; // [sp+1928h] [bp-10h]@4
  CGuildRanking *v21; // [sp+1940h] [bp+8h]@1
  unsigned int dwGuildSeriala; // [sp+1948h] [bp+10h]@1
  _io_money_data *v23; // [sp+1950h] [bp+18h]@1
  int *v24; // [sp+1958h] [bp+20h]@1

  v24 = pnIOMonHisNum;
  v23 = pkIOInfo;
  dwGuildSeriala = dwGuildSerial;
  v21 = this;
  v5 = alloca(a5);
  v6 = &v9;
  for ( i = 1624i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v20 = (unsigned __int64)&v9 ^ _security_cookie;
  memset_0(&Dst, 0, 0x1908ui64);
  if ( CRFWorldDatabase::Select_GuildMoneyIOData(pkDB, dwGuildSeriala, (_worlddb_guild_money_io_info *)&Dst) )
  {
    for ( j = 0; j < Dst; ++j )
    {
      v19 = Dst - (j + 1);
      v23[(signed __int64)j].dwIOerSerial = *(&v12 + 16 * (signed __int64)v19);
      *(_QWORD *)&v23[(signed __int64)j].dIODalant = *(&v13 + 8 * (signed __int64)v19);
      *(_QWORD *)&v23[(signed __int64)j].dIOGold = *(&v14 + 8 * (signed __int64)v19);
      *(_QWORD *)&v23[(signed __int64)j].dLeftDalant = *(&v15 + 8 * (signed __int64)v19);
      *(_QWORD *)&v23[(signed __int64)j].dLeftGold = *(&v16 + 8 * (signed __int64)v19);
      memcpy_0(v23[(signed __int64)j].byDate, &Src[64 * (signed __int64)v19], 4ui64);
      strcpy_0(v23[(signed __int64)j].wszIOerName, &Source[64 * (signed __int64)v19]);
    }
    *v24 = Dst;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
