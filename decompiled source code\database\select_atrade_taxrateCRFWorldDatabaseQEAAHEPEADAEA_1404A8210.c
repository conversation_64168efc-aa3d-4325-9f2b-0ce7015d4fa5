/*
 * Function: ?select_atrade_taxrate@CRFWorldDatabase@@QEAAHEPEADAEAE1@Z
 * Address: 0x1404A8210
 */

signed __int64 __fastcall CRFWorldDatabase::select_atrade_taxrate(CRFWorldDatabase *this, char by<PERSON>ace, char *pwszName, char *byCurrTax, char *byNextTax)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v8; // [sp+0h] [bp-108h]@1
  void *SQLStmt; // [sp+20h] [bp-E8h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-E0h]@22
  SQLLEN v11; // [sp+38h] [bp-D0h]@22
  __int16 v12; // [sp+44h] [bp-C4h]@9
  char Dest; // [sp+60h] [bp-A8h]@4
  char v14; // [sp+61h] [bp-A7h]@4
  unsigned __int8 v15; // [sp+E4h] [bp-24h]@16
  unsigned __int8 v16; // [sp+E5h] [bp-23h]@24
  unsigned __int64 v17; // [sp+F0h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+110h] [bp+8h]@1
  char *TargetValue; // [sp+120h] [bp+18h]@1
  char *v20; // [sp+128h] [bp+20h]@1

  v20 = byCurrTax;
  TargetValue = pwszName;
  v18 = this;
  v5 = &v8;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v17 = (unsigned __int64)&v8 ^ _security_cookie;
  Dest = 0;
  memset(&v14, 0, 0x7Fui64);
  sprintf(
    &Dest,
    "SELECT TOP 1 SuggesterName, Tax, NextTax FROM [dbo].[tbl_ATradeTaxRate] WHERE Race=%d ORDER BY serial DESC",
    (unsigned __int8)byRace);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v12 = SQLExecDirect_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v12 && v12 != 1 )
    {
      if ( v12 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v12 = SQLFetch_0(v18->m_hStmtSelect);
      if ( v12 && v12 != 1 )
      {
        v15 = 0;
        if ( v12 == 100 )
        {
          v15 = 2;
        }
        else
        {
          SQLStmt = v18->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
          v15 = 1;
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        result = v15;
      }
      else
      {
        StrLen_or_IndPtr = &v11;
        SQLStmt = (void *)17;
        v12 = SQLGetData_0(v18->m_hStmtSelect, 1u, 1, TargetValue, 17i64, &v11);
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v18->m_hStmtSelect, 2u, -6, v20, 0i64, &v11);
        StrLen_or_IndPtr = &v11;
        SQLStmt = 0i64;
        v12 = SQLGetData_0(v18->m_hStmtSelect, 3u, -6, byNextTax, 0i64, &v11);
        if ( v12 && v12 != 1 )
        {
          v16 = 0;
          if ( v12 == 100 )
          {
            v16 = 2;
          }
          else
          {
            SQLStmt = v18->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v12, &Dest, "SQLExecDirect", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v12, v18->m_hStmtSelect);
            v16 = 1;
          }
          if ( v18->m_hStmtSelect )
            SQLCloseCursor_0(v18->m_hStmtSelect);
          result = v16;
        }
        else
        {
          if ( v18->m_hStmtSelect )
            SQLCloseCursor_0(v18->m_hStmtSelect);
          if ( v18->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
