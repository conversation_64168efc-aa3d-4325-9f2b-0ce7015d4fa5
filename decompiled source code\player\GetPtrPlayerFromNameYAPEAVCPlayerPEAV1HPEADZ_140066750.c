/*
 * Function: ?GetPtrPlayerFromName@@YAPEAVCPlayer@@PEAV1@HPEAD@Z
 * Address: 0x140066750
 */

CPlayer *__fastcall GetPtrPlayerFromName(CPlayer *pData, int nNum, char *pwszName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned __int8 v8; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  size_t MaxCount; // [sp+28h] [bp-10h]@9
  CPlayer *v11; // [sp+40h] [bp+8h]@1
  int v12; // [sp+48h] [bp+10h]@1
  const char *Str; // [sp+50h] [bp+18h]@1

  Str = pwszName;
  v12 = nNum;
  v11 = pData;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = strlen_0(pwszName);
  for ( j = 0; j < v12; ++j )
  {
    if ( v11[j].m_bLive )
    {
      if ( v11[j].m_Param.m_byNameLen == v8 )
      {
        MaxCount = v8;
        v5 = CPlayerDB::GetCharNameW(&v11[j].m_Param);
        if ( !strncmp(v5, Str, MaxCount) )
          return &v11[j];
      }
    }
  }
  return 0i64;
}
