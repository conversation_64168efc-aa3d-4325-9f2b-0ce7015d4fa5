/*
 * Function: ?ChangeTargetPos@DfAIMgr@@SAXPEAVCMonster@@PEAM@Z
 * Address: 0x140150E20
 */

void __fastcall DfAIMgr::ChangeTargetPos(CMonster *pMon, float *pTarPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@13
  float v5; // xmm0_4@14
  __int64 v6; // [sp+0h] [bp-38h]@1
  void *Src; // [sp+20h] [bp-18h]@9
  CMonster *v8; // [sp+40h] [bp+8h]@1
  float *v9; // [sp+48h] [bp+10h]@1

  v9 = pTarPos;
  v8 = pMon;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8 )
  {
    if ( pTarPos )
    {
      if ( !_effect_parameter::GetEff_State(&v8->m_EP, 6) )
      {
        if ( v9 )
        {
          Src = v9;
          memcpy_0(NewTar, v9, 0xCui64);
          memcpy_0(fBeforeTar, v8->m_fTarPos, 0xCui64);
          if ( CCharacter::SetTarPos((CCharacter *)&v8->vfptr, NewTar, 1) )
          {
            if ( v8->m_fCurPos[0] != v8->m_fTarPos[0]
              || v8->m_fCurPos[1] != v8->m_fTarPos[1]
              || v8->m_fCurPos[2] != v8->m_fTarPos[2] )
            {
              v4 = fBeforeTar[0] - NewTar[0];
              abs(fBeforeTar[0] - NewTar[0]);
              if ( v4 >= 5.0 || (v5 = fBeforeTar[2] - NewTar[2], abs(fBeforeTar[2] - NewTar[2]), v5 >= 5.0) )
                CMonster::SendMsg_Move(v8);
            }
            Us_HFSM::SendMsg((Us_HFSM *)&v8->m_AI.vfptr, 7u, 0x22u, 0i64);
          }
        }
      }
    }
  }
}
