/*
 * Function: ?IsolatedInitialize@?$StringSinkTemplate@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x140551460
 */

__int64 __fastcall CryptoPP::StringSinkTemplate<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>::IsolatedInitialize(__int64 a1, __int64 a2)
{
  __int64 result; // rax@1
  CryptoPP::InvalidArgument v3; // [sp+20h] [bp-98h]@2
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > s; // [sp+70h] [bp-48h]@2
  unsigned __int8 v5; // [sp+A0h] [bp-18h]@2
  __int64 v6; // [sp+A8h] [bp-10h]@1

  v6 = -2i64;
  result = (unsigned __int8)CryptoPP::NameValuePairs::GetValue<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *>(
                              a2,
                              "OutputStringPointer",
                              a1 + 24);
  if ( !(_BYTE)result )
  {
    memset(&v5, 0, sizeof(v5));
    std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
      &s,
      "StringSink: OutputStringPointer not specified",
      v5);
    CryptoPP::InvalidArgument::InvalidArgument(&v3, &s);
    CxxThrowException_0((__int64)&v3, (__int64)&TI3_AVInvalidArgument_CryptoPP__);
  }
  return result;
}
