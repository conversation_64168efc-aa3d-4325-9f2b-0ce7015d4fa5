/*
 * Function: ?Select_CharacterGeneralInfo@CRFWorldDatabase@@QEAAEKPEAU_worlddb_character_general_info@@@Z
 * Address: 0x14048B290
 */

char __fastcall CRFWorldDatabase::Select_CharacterGeneralInfo(CRFWorldDatabase *this, unsigned int dwCharacterSerial, _worlddb_character_general_info *pCharacterData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v6; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@22
  SQLLEN v9; // [sp+38h] [bp-170h]@22
  __int16 v10; // [sp+44h] [bp-164h]@9
  char Dest; // [sp+60h] [bp-148h]@4
  int j; // [sp+164h] [bp-44h]@4
  int v13; // [sp+168h] [bp-40h]@22
  char v14; // [sp+16Ch] [bp-3Ch]@16
  unsigned __int64 v15; // [sp+178h] [bp-30h]@42
  int v16; // [sp+184h] [bp-24h]@42
  unsigned __int64 v17; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v18; // [sp+1B0h] [bp+8h]@1
  _worlddb_character_general_info *TargetValue; // [sp+1C0h] [bp+18h]@1

  TargetValue = pCharacterData;
  v18 = this;
  v3 = &v6;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v17 = (unsigned __int64)&v6 ^ _security_cookie;
  j = 0;
  sprintf(&Dest, "{ CALL pSelect_CharacterGeneralInfo_20081216( %d ) }", dwCharacterSerial);
  if ( v18->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v18->vfptr, &Dest);
  if ( v18->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v18->vfptr) )
  {
    v10 = SQLExecDirectA_0(v18->m_hStmtSelect, &Dest, -3);
    if ( v10 && v10 != 1 )
    {
      if ( v10 == 100 )
      {
        result = 2;
      }
      else
      {
        SQLStmt = v18->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v10, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v10, v18->m_hStmtSelect);
        result = 1;
      }
    }
    else
    {
      v10 = SQLFetch_0(v18->m_hStmtSelect);
      if ( v10 && v10 != 1 )
      {
        v14 = 0;
        if ( v10 == 100 )
        {
          v14 = 2;
        }
        else
        {
          SQLStmt = v18->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v18->vfptr, v10, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v18->vfptr, v10, v18->m_hStmtSelect);
          v14 = 1;
        }
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        result = v14;
      }
      else
      {
        v13 = 1;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, 1u, -18, TargetValue, 0i64, &v9);
        ++v13;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwFP, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwSP, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 8, &TargetValue->dExp, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 8, &TargetValue->dLoseExp, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 5, &TargetValue->byMaxLevel, 0i64, &v9);
        if ( TargetValue->byMaxLevel < 50 )
          TargetValue->byMaxLevel = 50;
        ++v13;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 5, &TargetValue->byBagNum, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 5, &TargetValue->byMapCode, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 7, TargetValue->fStartPos, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 7, &TargetValue->fStartPos[1], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 7, &TargetValue->fStartPos[2], 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwTotalPlayMin, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)160;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 1, TargetValue->szLeftResList, 160i64, &v9);
        for ( j = 0; j < 7; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 4, &TargetValue->lEK[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 5, &TargetValue->wED[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -25, &TargetValue->lnUID_E[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 4, &TargetValue->dwET[j], 0i64, &v9);
        }
        for ( j = 0; j < 88; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 4, &TargetValue->lF[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, -25, &TargetValue->lnUID_F[j], 0i64, &v9);
        }
        for ( j = 0; j < 2; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 4, &TargetValue->dwWM[j], 0i64, &v9);
        }
        for ( j = 0; j < 24; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 4, &TargetValue->dwFM[j], 0i64, &v9);
        }
        for ( j = 0; j < 48; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 4, &TargetValue->dwSM[j], 0i64, &v9);
        }
        for ( j = 0; j < 3; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 4, &TargetValue->dwMI[j], 0i64, &v9);
        }
        ++v13;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 4, &TargetValue->dwSR, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 4, &TargetValue->dwDM, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 4, &TargetValue->dwPM, 0i64, &v9);
        v15 = 0i64;
        v16 = 0;
        for ( j = 0; j < 4; ++j )
        {
          v15 = 0i64;
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -28, &TargetValue->byAK[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, -25, &v15, 0i64, &v9);
          v16 = v15;
          TargetValue->dwAD[j] = v15;
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 4, &TargetValue->dwAP[j], 0i64, &v9);
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, -25, &TargetValue->lnUID_A[j], 0i64, &v9);
        }
        for ( j = 0; j < 3; ++j )
        {
          ++v13;
          StrLen_or_IndPtr = &v9;
          SQLStmt = 0i64;
          v10 = SQLGetData_0(v18->m_hStmtSelect, v13, 5, &TargetValue->zClassHistory[j], 0i64, &v9);
        }
        ++v13;
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwClassInitCnt, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -28, &TargetValue->byLastClassGrade, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 8, &TargetValue->dPvPPoint, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 8, &TargetValue->dPvPCashBag, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)12;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 1, TargetValue->szBindMapCode, 12i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = (void *)12;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, 1, TargetValue->szBindDummy, 12i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwGuildSerial, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -28, &TargetValue->byGuildGrade, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwDP, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwRadarDelayTime, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13++, -18, &TargetValue->dwTakeLastMentalTicket, 0i64, &v9);
        StrLen_or_IndPtr = &v9;
        SQLStmt = 0i64;
        v10 = SQLGetData_0(v18->m_hStmtSelect, v13, -18, &TargetValue->dwTakeLastCriTicket, 0i64, &v9);
        if ( v18->m_hStmtSelect )
          SQLCloseCursor_0(v18->m_hStmtSelect);
        if ( v18->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v18->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v18->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
