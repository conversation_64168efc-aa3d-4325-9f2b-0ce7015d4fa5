/*
 * Function: ?SendTargetMonsterSFContInfo@CPlayer@@QEAAXXZ
 * Address: 0x1400E5520
 */

void __fastcall CPlayer::SendTargetMonsterSFContInfo(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@11
  int v4; // eax@13
  unsigned __int16 v5; // ax@18
  __int64 v6; // [sp+0h] [bp-A8h]@1
  CMonster *v7; // [sp+30h] [bp-78h]@4
  CMonster *v8; // [sp+38h] [bp-70h]@8
  _target_monster_contsf_allinform_zocl src1; // [sp+48h] [bp-60h]@8
  char v10; // [sp+64h] [bp-44h]@8
  int j; // [sp+68h] [bp-40h]@8
  int k; // [sp+6Ch] [bp-3Ch]@10
  bool *v13; // [sp+70h] [bp-38h]@12
  char pbyType; // [sp+84h] [bp-24h]@18
  char v15; // [sp+85h] [bp-23h]@18
  int v16; // [sp+94h] [bp-14h]@13
  CPlayer *v17; // [sp+B0h] [bp+8h]@1

  v17 = this;
  v1 = &v6;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (CMonster *)CPlayer::GetTargetObj(v17);
  if ( v7 && !v7->m_ObjID.m_byKind && v7->m_ObjID.m_byID == 1 )
  {
    v8 = v7;
    _target_monster_contsf_allinform_zocl::_target_monster_contsf_allinform_zocl(&src1);
    src1.dwSerial = v8->m_dwObjSerial;
    v10 = 0;
    for ( j = 0; j < 2; ++j )
    {
      for ( k = 0; ; ++k )
      {
        v3 = CMonster::GetMaxDMGSFContCount(v8);
        if ( k >= v3 )
          break;
        v13 = &v8->m_SFCont[j][k].m_bExist;
        if ( *v13 )
        {
          v16 = (unsigned __int8)v10;
          v4 = CMonster::GetMaxDMGSFContCount(v8);
          if ( v16 < v4 )
          {
            src1.m_MonContSf[(unsigned __int8)v10++].wSfcode = CCharacter::CalcEffectBit(
                                                                 (CCharacter *)&v17->vfptr,
                                                                 v13[1],
                                                                 *((_WORD *)v13 + 1));
            src1.byContCount = v10;
          }
        }
      }
    }
    if ( !_target_monster_contsf_allinform_zocl::IsSame(&src1, &v17->m_TargetObject.m_PrevTargetMonsterContInfo) )
    {
      pbyType = 13;
      v15 = 100;
      v5 = _target_monster_contsf_allinform_zocl::size(&src1);
      CNetProcess::LoadSendMsg(unk_1414F2088, v17->m_ObjID.m_wIndex, &pbyType, (char *)&src1, v5);
      memcpy_0(&v17->m_TargetObject.m_PrevTargetMonsterContInfo, &src1, 0x15ui64);
    }
  }
}
