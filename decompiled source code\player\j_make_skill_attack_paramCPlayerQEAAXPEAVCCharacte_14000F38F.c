/*
 * Function: j_?make_skill_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAMEPEAU_skill_fld@@HPEAU_db_con@_STORAGE_LIST@@MPEAU_attack_param@@3M@Z
 * Address: 0x14000F38F
 */

void __fastcall CPlayer::make_skill_attack_param(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byEffectCode, _skill_fld *pSkillFld, int nAttType, _STORAGE_LIST::_db_con *pBulletItem, float fAddBulletFc, _attack_param *pAP, _STORAGE_LIST::_db_con *pEffBulletItem, float fAddEffBulletFc)
{
  CPlayer::make_skill_attack_param(
    this,
    pDst,
    pfAttackPos,
    byEffectCode,
    pSkillFld,
    nAttType,
    pBulletItem,
    fAddBulletFc,
    pAP,
    pEffBulletItem,
    fAddEffBulletFc);
}
