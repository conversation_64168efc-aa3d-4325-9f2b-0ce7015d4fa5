/*
 * Function: ?Insert_GreetingRecord@CRFWorldDatabase@@QEAA_NHPEAD0@Z
 * Address: 0x14049BF70
 */

bool __fastcall CRFWorldDatabase::Insert_GreetingRecord(CRFWorldDatabase *this, int nUseType, char *wszName, char *wszMessage)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-268h]@1
  char *v8; // [sp+20h] [bp-248h]@4
  char Dest; // [sp+40h] [bp-228h]@4
  char v10; // [sp+41h] [bp-227h]@4
  unsigned __int64 v11; // [sp+250h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+270h] [bp+8h]@1

  v12 = this;
  v4 = &v7;
  for ( i = 152i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  Dest = 0;
  memset(&v10, 0, 0x1FFui64);
  v8 = wszMessage;
  sprintf(&Dest, "{CALL pInsert_GreetingMsgRecord_071119(%d,'%s','%s')}", (unsigned int)nUseType, wszName);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v12->vfptr, &Dest, 1);
}
