/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAU_guild_member_refresh_data@@PEAPEAU1@V?$allocator@PEAU_guild_member_refresh_data@@@std@@@stdext@@YAPEAPEAU_guild_member_refresh_data@@PEAPEAU1@00AEAV?$allocator@PEAU_guild_member_refresh_data@@@std@@@Z
 * Address: 0x140009570
 */

_guild_member_refresh_data **__fastcall stdext::unchecked_uninitialized_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(_guild_member_refresh_data **_First, _guild_member_refresh_data **_Last, _guild_member_refresh_data **_Dest, std::allocator<_guild_member_refresh_data *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<_guild_member_refresh_data * *,_guild_member_refresh_data * *,std::allocator<_guild_member_refresh_data *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
