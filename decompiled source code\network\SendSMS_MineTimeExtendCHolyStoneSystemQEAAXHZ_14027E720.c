/*
 * Function: ?SendSMS_MineTimeExtend@CHolyStoneSystem@@QEAAXH@Z
 * Address: 0x14027E720
 */

void __fastcall CHolyStoneSystem::SendSMS_MineTimeExtend(CHolyStoneSystem *this, int nControlSec)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  char szMsg[4]; // [sp+34h] [bp-54h]@6
  char v6; // [sp+38h] [bp-50h]@6
  char v7; // [sp+39h] [bp-4Fh]@6
  char v8; // [sp+3Ah] [bp-4Eh]@6
  int v9; // [sp+44h] [bp-44h]@4
  int v10; // [sp+48h] [bp-40h]@4
  int v11; // [sp+4Ch] [bp-3Ch]@4
  int v12; // [sp+50h] [bp-38h]@4
  char pbyType; // [sp+64h] [bp-24h]@6
  char v14; // [sp+65h] [bp-23h]@6
  CHolyStoneSystem *v15; // [sp+90h] [bp+8h]@1
  int v16; // [sp+98h] [bp+10h]@1

  v16 = nControlSec;
  v15 = this;
  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = GetCurrentHour();
  v10 = (v16 / 60 / 60 + v9) % 24;
  v11 = GetCurrentMin();
  v12 = (v16 / 60 + v11) % 60;
  if ( v12 < v11 )
    v10 = (v10 + 1) % 24;
  *(_DWORD *)szMsg = unk_1799C608C;
  v8 = CHolyStoneSystem::GetNumOfTime(v15);
  v6 = v10;
  v7 = v12;
  pbyType = 51;
  v14 = 2;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, szMsg, 7u);
}
