/*
 * Function: ?PushIPCheckList@CNetSocket@@QEAA_NK@Z
 * Address: 0x14047ED20
 */

bool __fastcall CNetSocket::PushIPCheckList(CNetSocket *this, unsigned int dwIP)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@6
  bool v7; // [sp+34h] [bp-14h]@8
  CNetSocket *v8; // [sp+50h] [bp+8h]@1
  unsigned int v9; // [sp+58h] [bp+10h]@1

  v9 = dwIP;
  v8 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_SockType.m_bAcceptIPCheck )
  {
    if ( CNetIndexList::PopNode_Front(&v8->m_listIPCheck_Empty, &pdwOutIndex) )
    {
      v8->m_ndIPCheck[pdwOutIndex].dwIP = v9;
      v8->m_ndIPCheck[pdwOutIndex].dwWaitStartTime = timeGetTime();
      v7 = CNetIndexList::PushNode_Back(&v8->m_listIPCheck, pdwOutIndex);
      result = v7 != 0;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
