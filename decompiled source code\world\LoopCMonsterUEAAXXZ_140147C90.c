/*
 * Function: ?Loop@CMonster@@UEAAXXZ
 * Address: 0x140147C90
 */

void __usercall CMonster::Loop(CMonster *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@22
  __int64 v5; // rax@26
  __int64 v6; // [sp+0h] [bp-38h]@1
  Us_HFSMVtbl *v7; // [sp+20h] [bp-18h]@22
  CMonster *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_bLive )
  {
    (*(void (__fastcall **)(CMonster *))&v8->vfptr[1].gap8[0])(v8);
    if ( v8->m_bLive )
    {
      if ( !CMonster::CheckRespawnProcess(v8) && v8->m_bLive )
      {
        if ( CMonster::CheckMonsterStateData(v8) )
          CMonster::SendMsg_Change_MonsterState(v8);
        if ( !_effect_parameter::GetEff_State(&v8->m_EP, 20) && !_effect_parameter::GetEff_State(&v8->m_EP, 28) )
        {
          if ( !CGameObject::GetStun((CGameObject *)&v8->vfptr) )
          {
            if ( v8->m_bMove )
            {
              if ( _effect_parameter::GetEff_State(&v8->m_EP, 6) )
              {
                CMonster::GetMoveSpeed(v8);
                CCharacter::Move((CCharacter *)&v8->vfptr, a2);
                CMonster::GetMoveSpeed(v8);
                CCharacter::MoveBreak((CCharacter *)&v8->vfptr, a2);
                CCharacter::Stop((CCharacter *)&v8->vfptr);
                CGameObject::SendMsg_BreakStop((CGameObject *)&v8->vfptr);
              }
              else
              {
                CMonster::GetMoveSpeed(v8);
                CCharacter::Move((CCharacter *)&v8->vfptr, a2);
                CMonster::UpdateLookAtPos(v8);
              }
            }
            else
            {
              CMonster::CheckMonsterRotate(v8);
            }
          }
          if ( v8->m_bLive )
          {
            CMonsterAggroMgr::Process(&v8->m_AggroMgr);
            MonsterSFContDamageToleracne::Update(&v8->m_SFContDamageTolerance);
            if ( v8->m_bLive )
            {
              if ( !CGameObject::GetStun((CGameObject *)&v8->vfptr) )
              {
                v4 = GetLoopTime();
                v7 = v8->m_AI.vfptr;
                ((void (__fastcall *)(signed __int64, _QWORD))v7->OnProcess)((signed __int64)&v8->m_AI, v4);
              }
              CMonster::CheckEmotionPresentation(v8);
              if ( v8->m_bLive )
              {
                CMonster::CheckAutoRecoverHP(v8);
                if ( v8->m_bLive )
                {
                  CMonsterHierarchy::OnChildRegenLoop(&v8->m_MonHierarcy);
                  if ( v8->m_bLive )
                    v5 = CMonster::CheckDelayDestroy(v8);
                }
              }
            }
          }
        }
      }
    }
  }
}
