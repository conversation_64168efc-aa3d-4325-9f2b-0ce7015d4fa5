/*
 * Function: j_??$_Iter_random@PEAVCUnmannedTraderGroupDivisionVersionInfo@@PEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAVCUnmannedTraderGroupDivisionVersionInfo@@0@Z
 * Address: 0x14000262B
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(CUnmannedTraderGroupDivisionVersionInfo *const *__formal, CUnmannedTraderGroupDivisionVersionInfo *const *a2)
{
  return std::_Iter_random<CUnmannedTraderGroupDivisionVersionInfo *,CUnmannedTraderGroupDivisionVersionInfo *>(
           __formal,
           a2);
}
