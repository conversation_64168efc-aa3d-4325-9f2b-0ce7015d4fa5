/*
 * Function: ??$_Uninit_fill_n@PEAP8CUserRankingProcess@@EAAXXZ_KP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@std@@YAXPEAP8CUserRankingProcess@@EAAXXZ_KAEBQ81@EAAXXZAEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140347990
 */

void __fastcall std::_Uninit_fill_n<void (CUserRankingProcess::**)(void),unsigned __int64,void (CUserRankingProcess::*)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), unsigned __int64 _Count, void (__cdecl *const *_Val)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  void (__cdecl **v9)(CUserRankingProcess *); // [sp+20h] [bp-18h]@4
  __int64 v10; // [sp+28h] [bp-10h]@4
  void (__cdecl **_Ptr)(CUserRankingProcess *); // [sp+40h] [bp+8h]@1
  unsigned __int64 v12; // [sp+48h] [bp+10h]@1
  void (__cdecl **_Vala)(CUserRankingProcess *); // [sp+50h] [bp+18h]@1
  std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *v14; // [sp+58h] [bp+20h]@1

  v14 = _Al;
  _Vala = (void (__cdecl **)(CUserRankingProcess *))_Val;
  v12 = _Count;
  _Ptr = _First;
  v6 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = -2i64;
  v9 = _Ptr;
  while ( v12 )
  {
    std::allocator<void (CUserRankingProcess::*)(void)>::construct(v14, _Ptr, _Vala);
    --v12;
    ++_Ptr;
  }
}
