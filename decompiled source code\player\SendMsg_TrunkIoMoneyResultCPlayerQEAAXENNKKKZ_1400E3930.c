/*
 * Function: ?SendMsg_TrunkIoM<PERSON><PERSON><PERSON>ult@CPlayer@@QEAAXENNKKK@Z
 * Address: 0x1400E3930
 */

void __fastcall CPlayer::SendMsg_TrunkIoMoneyResult(CPlayer *this, char byRetCode, long double dTrunkDalant, long double dTrunkGold, unsigned int dwDalant, unsigned int dwGold, unsigned int dwFeeDalant)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v9; // [sp-20h] [bp-98h]@1
  char szMsg; // [sp+18h] [bp-60h]@4
  long double v11; // [sp+19h] [bp-5Fh]@4
  long double v12; // [sp+21h] [bp-57h]@4
  unsigned int v13; // [sp+29h] [bp-4Fh]@4
  unsigned int v14; // [sp+2Dh] [bp-4Bh]@4
  unsigned int v15; // [sp+31h] [bp-47h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v17; // [sp+55h] [bp-23h]@4
  CPlayer *v18; // [sp+80h] [bp+8h]@1

  v18 = this;
  v7 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  szMsg = byRetCode;
  v11 = dTrunkDalant;
  v12 = dTrunkGold;
  v13 = dwDalant;
  v14 = dwGold;
  v15 = dwFeeDalant;
  pbyType = 34;
  v17 = 19;
  CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x1Du);
}
