# MonsterUtils Refactoring Documentation

## Overview
This document describes the refactoring of the SearchEmptyMonster utility function from decompiled C source to modern C++20 compatible code for Visual Studio 2022.

## Original Files Refactored
The following decompiled source file was refactored into the new MonsterUtils module:

### Core Function
- `SearchEmptyMonsterYAPEAVCMonster_NZ_140148F20.c` - SearchEmptyMonster utility function

## Function Analysis

### Original Decompiled Code Characteristics
1. **Global Monster Array Access**
   - Direct access to global `g_Monster` array
   - Hard-coded array size (30000) and slot size (6424)
   - Manual pointer arithmetic for array indexing

2. **Search Algorithm**
   - Three-pass search algorithm:
     1. Look for completely empty slots (byte at offset 24 == 0)
     2. Look for non-live monsters if `withoutFail` is true
     3. Look for destroyable monsters with specific criteria

3. **Monster Destruction Logic**
   - Complex criteria for determining if a monster can be safely destroyed
   - Checks for map type, hierarchy, condition, and emotion state

## Refactoring Changes

### Modern C++ Features Applied

1. **Namespace Organization**
   - Created `NexusProtection::World::MonsterUtils` namespace
   - Organized related functionality together

2. **Type Safety**
   - Introduced `MonsterSearchCriteria` enum class for type-safe search options
   - Added `MonsterSearchOptions` struct for configurable search parameters
   - Used `std::optional` for safe index operations

3. **STL Integration**
   - Used `std::vector` for returning multiple results
   - Used `std::function` for callback-based operations
   - Used `constexpr` for compile-time constants

4. **Exception Safety**
   - Added try-catch blocks for exception handling
   - Safe pointer validation and bounds checking

5. **Modern Function Attributes**
   - Used `[[nodiscard]]` for functions that return important values
   - Used `constexpr` for compile-time evaluable functions
   - Used `noexcept` where appropriate

### API Design

#### Core Functions
```cpp
// Original function signature (modernized)
CMonster* SearchEmptyMonster(bool withoutFail = false);

// Enhanced version with options
CMonster* SearchEmptyMonster(const MonsterSearchOptions& options);

// Flexible search with criteria
std::vector<CMonster*> FindMonsters(MonsterSearchCriteria criteria, 
                                   std::size_t maxResults = 100);
```

#### Utility Functions
- `IsEmptySlot()` - Check if a monster slot is empty
- `CanDestroyMonster()` - Check if a monster can be safely destroyed
- `GetMonsterByIndex()` - Safe array access by index
- `GetMonsterIndex()` - Get index of a monster in the array
- `ValidateMonsterPointer()` - Validate monster pointer bounds

#### Statistics and Management
- `CountEmptySlots()` - Count available slots
- `CountLiveMonsters()` - Count active monsters
- `GetMonsterStatistics()` - Comprehensive statistics
- `CleanupDestroyedMonsters()` - Cleanup operations

### Search Criteria System

#### Enum-Based Criteria
```cpp
enum class MonsterSearchCriteria : uint32_t {
    Any = 0,
    EmptySlot = 1,
    NotLive = 2,
    NoChildren = 4,
    NoParent = 8,
    NoTarget = 16,
    NormalCondition = 32,
    NoEmotion = 64,
    InNormalMap = 128
};
```

#### Bitwise Operations
- Support for combining criteria with `|` operator
- Testing criteria with `&` operator
- Helper function `HasCriteria()` for readable code

### Memory Management Improvements

1. **Safe Pointer Arithmetic**
   - Bounds checking for all array access
   - Validation of pointer alignment to slot boundaries
   - Protection against out-of-bounds access

2. **Const Correctness**
   - Proper const qualifiers for read-only operations
   - Immutable access where appropriate

### Error Handling

1. **Null Pointer Safety**
   - All functions check for null pointers
   - Safe handling of uninitialized global array

2. **Bounds Checking**
   - Index validation for all array operations
   - Alignment checking for monster pointers

3. **Exception Safety**
   - Exception handling in critical functions
   - Graceful degradation on errors

## Performance Considerations

### Optimizations Applied
- `constexpr` functions for compile-time evaluation
- `std::vector::reserve()` to minimize reallocations
- Efficient bitwise operations for criteria checking
- Early termination in search loops

### Memory Efficiency
- No unnecessary copying of monster objects
- Efficient pointer-based operations
- Minimal overhead for utility functions

## Dependencies
The refactored module has dependencies on the following classes that will need to be refactored in future iterations:

- `CMonster` - Core monster class
- `CMapData` - Map data management
- `CMonsterHierarchy` - Monster hierarchy system
- `MonsterRecord` - Monster record structure

## Compilation Notes
- Compatible with Visual Studio 2022 (v143 toolset)
- Requires C++20 standard for `std::optional` and other features
- Uses modern STL features

## Testing Recommendations
1. Unit tests for search algorithms
2. Tests for criteria combinations
3. Bounds checking tests
4. Performance benchmarks
5. Memory safety tests
6. Integration tests with monster system

## Future Improvements
1. Consider using `std::span` for array-like interfaces
2. Add logging for debugging purposes
3. Consider thread safety for multi-threaded access
4. Add configuration for array size and slot size
5. Implement custom allocators for monster management

## Backward Compatibility
- Legacy C-style interface maintained for compatibility
- Same function signatures as original decompiled code
- Identical behavior for existing code

## Usage Examples

### Basic Usage
```cpp
// Find an empty monster slot
CMonster* emptySlot = MonsterUtils::SearchEmptyMonster();

// Find with destruction allowed
CMonster* slot = MonsterUtils::SearchEmptyMonster(true);
```

### Advanced Usage
```cpp
// Find monsters with specific criteria
auto criteria = MonsterSearchCriteria::NotLive | 
                MonsterSearchCriteria::NoChildren;
auto monsters = MonsterUtils::FindMonsters(criteria, 10);

// Get statistics
auto stats = MonsterUtils::GetMonsterStatistics();
std::cout << "Live monsters: " << stats.liveMonsters << std::endl;
```

### Functional Programming
```cpp
// Apply operation to all destroyable monsters
MonsterUtils::ForEachMonster(
    MonsterSearchCriteria::NotLive,
    [](CMonster* monster) {
        // Cleanup operation
        monster->Reset();
    }
);
```
