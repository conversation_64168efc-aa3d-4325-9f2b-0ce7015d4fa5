/*
 * Function: ?ProcComplete@CLogTypeDBTaskManager@@AEAAXXZ
 * Address: 0x1402C3390
 */

void __fastcall CLogTypeDBTaskManager::ProcComplete(CLogTypeDBTaskManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderController *v3; // rax@7
  __int64 v4; // [sp+0h] [bp-48h]@1
  CLogTypeDBTask *pTask; // [sp+20h] [bp-28h]@4
  int v6; // [sp+28h] [bp-20h]@6
  char *pLoadData; // [sp+30h] [bp-18h]@7
  char v8; // [sp+38h] [bp-10h]@7
  char v9; // [sp+39h] [bp-Fh]@7
  CLogTypeDBTaskManager *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( pTask = 0i64; ; CLogTypeDBTaskPool::SetEmpty(&v10->m_kPool, pTask, v10->m_pkLogger) )
  {
    pTask = CLogTypeDBTaskPool::GetComplete(&v10->m_kPool);
    if ( !pTask )
      break;
    v6 = CLogTypeDBTask::GetQueryType(pTask);
    if ( v6 == 1 )
    {
      pLoadData = CLogTypeDBTask::GetData(pTask);
      v8 = CLogTypeDBTask::GetProcRet(pTask);
      v9 = CLogTypeDBTask::GetDBRet(pTask);
      v3 = CUnmannedTraderController::Instance();
      CUnmannedTraderController::CompleteSelectSearchList(v3, v9, v8, pLoadData);
    }
  }
}
