/*
 * Function: ?SendMsg_FixPosition@AutominePersonal@@UEAAXH@Z
 * Address: 0x1402DCD60
 */

void __fastcall AutominePersonal::SendMsg_FixPosition(AutominePersonal *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@5
  __int64 v5; // [sp+0h] [bp-98h]@1
  _personal_amine_fixpos_zocl Dst; // [sp+38h] [bp-60h]@5
  char pbyType; // [sp+74h] [bp-24h]@5
  char v8; // [sp+75h] [bp-23h]@5
  AutominePersonal *v9; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v9 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_bInstalled )
  {
    _personal_amine_fixpos_zocl::_personal_amine_fixpos_zocl(&Dst);
    Dst.wObjIndex = v9->m_ObjID.m_wIndex;
    Dst.dwObjSerial = v9->m_dwObjSerial;
    Dst.wItemTblIndex = v9->m_pItem->m_wItemIndex;
    memcpy_0(Dst.fFixPos, v9->m_fCurPos, 0xCui64);
    Dst.wItemSerial = v9->m_pItem->m_wSerial;
    Dst.byAct = v9->m_bStart;
    Dst.dwOwnerSeiral = AutominePersonal::get_ownerserial(v9);
    pbyType = 14;
    v8 = 59;
    v4 = _personal_amine_fixpos_zocl::size(&Dst);
    CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, (char *)&Dst, v4);
  }
}
