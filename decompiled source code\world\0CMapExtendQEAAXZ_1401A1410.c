/*
 * Function: ??0CMapExtend@@QEAA@XZ
 * Address: 0x1401A1410
 */

void __fastcall CMapExtend::CMapExtend(CMapExtend *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMapExtend *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CPoint::CPoint(&v4->m_ptStartMap);
  CPoint::CPoint(&v4->m_ptEndMap);
  CPoint::CPoint(&v4->m_ptCenter);
  CRect::CRect(&v4->m_rcExtend);
  CPoint::CPoint(&v4->m_ptStartScreen);
  CPoint::CPoint(&v4->m_ptEndScreen);
  CPoint::CPoint(&v4->m_ptMoveScreen);
  CSize::CSize(&v4->m_sizeExtend);
  v4->m_bSetArea = 0;
  v4->m_bExtendMode = 0;
  v4->m_hPen = CreatePen(0, 1, 0x646464u);
}
