/*
 * Function: ?GetGroupID@CUnmannedTraderClassInfoTableCodeType@@UEAA_NEGAEAE@Z
 * Address: 0x1403773A0
 */

char __fastcall CUnmannedTraderClassInfoTableCodeType::GetGroupID(CUnmannedTraderClassInfoTableCodeType *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byClass)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@5
  CUnmannedTraderItemCodeInfo *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-C8h]@1
  _base_fld *v9; // [sp+20h] [bp-A8h]@6
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > result; // [sp+38h] [bp-90h]@8
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > v11; // [sp+58h] [bp-70h]@12
  bool v12; // [sp+70h] [bp-58h]@9
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > v13; // [sp+78h] [bp-50h]@9
  char v14; // [sp+90h] [bp-38h]@11
  bool v15; // [sp+91h] [bp-37h]@13
  __int64 v16; // [sp+98h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v17; // [sp+A0h] [bp-28h]@9
  std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *_Right; // [sp+A8h] [bp-20h]@9
  char *szCode; // [sp+B0h] [bp-18h]@10
  CUnmannedTraderClassInfoTableCodeType *v20; // [sp+D0h] [bp+8h]@1
  char *v21; // [sp+E8h] [bp+20h]@1

  v21 = byClass;
  v20 = this;
  v4 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = -2i64;
  if ( v20->m_byTableCode == (unsigned __int8)byTableCode )
  {
    v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)byTableCode, wItemTableIndex);
    if ( v9 )
    {
      std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::begin(
        &v20->m_vecCodeList,
        &result);
      while ( 1 )
      {
        v17 = std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::end(
                &v20->m_vecCodeList,
                &v13);
        _Right = (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)v17;
        v12 = std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator!=(
                (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&result._Mycont,
                (std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *)&v17->_Mycont);
        std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(&v13);
        if ( !v12 )
          break;
        szCode = v9->m_strCode;
        v7 = std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator*(&result);
        if ( CUnmannedTraderItemCodeInfo::operator==(v7, szCode) )
        {
          *v21 = v20->m_dwID;
          v14 = 1;
          std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(&result);
          return v14;
        }
        std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::operator++(
          &result,
          &v11,
          0);
        std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(&v11);
      }
      v15 = 0;
      std::_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::~_Vector_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>(&result);
      v6 = v15;
    }
    else
    {
      v6 = 0;
    }
  }
  else
  {
    v6 = 0;
  }
  return v6;
}
