/*
 * Function: _std::vector_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0_____::resize_::_1_::dtor$2
 * Address: 0x1403C4120
 */

void __fastcall std::vector_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CAsyncLogInfo_____ptr64__std::allocator_std::pair_int_const__CAsyncLogInfo_____ptr64_____::_Iterator_0_____::resize_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>>::~_Vector_iterator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo *>>>::_Iterator<0>>>(*(std::_Vector_iterator<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0>,std::allocator<std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *> > >::_Iterator<0> > > **)(a2 + 88));
}
