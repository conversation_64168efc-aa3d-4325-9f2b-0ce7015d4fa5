/*
 * Function: ?IsItemLootAuthority@CHolyStoneSystem@@QEAA_NPEAVCPlayer@@E@Z
 * Address: 0x1402811D0
 */

char __fastcall CHolyStoneSystem::IsItemLootAuthority(CHolyStoneSystem *this, CPlayer *pOne, char byCreateCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@7
  int v7; // eax@12
  __int64 v8; // rax@18
  __int64 v9; // rax@23
  __int64 v10; // [sp+0h] [bp-38h]@1
  int v11; // [sp+20h] [bp-18h]@7
  int v12; // [sp+24h] [bp-14h]@12
  int v13; // [sp+28h] [bp-10h]@18
  int v14; // [sp+2Ch] [bp-Ch]@23
  CHolyStoneSystem *v15; // [sp+40h] [bp+8h]@1
  CPlayer *v16; // [sp+48h] [bp+10h]@1

  v16 = pOne;
  v15 = this;
  v3 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( byCreateCode == 4 )
  {
    if ( CMainThread::IsReleaseServiceMode(&g_Main) )
    {
      if ( CPlayerDB::GetLevel(&v16->m_Param) >= 25 )
      {
        v11 = CPlayerDB::GetRaceCode(&v16->m_Param);
        v5 = CHolyStoneSystem::GetHolyMasterRace(v15);
        if ( v11 == v5 && !v16->m_byUserDgr )
          return 1;
      }
    }
    else if ( CPlayerDB::GetLevel(&v16->m_Param) >= 25 )
    {
      v12 = CPlayerDB::GetRaceCode(&v16->m_Param);
      v7 = CHolyStoneSystem::GetHolyMasterRace(v15);
      if ( v12 == v7 )
        return 1;
    }
  }
  else if ( byCreateCode == 6 )
  {
    if ( CMainThread::IsReleaseServiceMode(&g_Main) )
    {
      if ( CPlayerDB::GetLevel(&v16->m_Param) >= 25 )
      {
        v13 = CPlayerDB::GetRaceCode(&v16->m_Param);
        v8 = (unsigned __int8)CHolyStoneSystem::GetKeeperDestroyRace(v15);
        if ( v13 == (unsigned __int8)v8 && !v16->m_byUserDgr )
          return 1;
      }
    }
    else if ( CPlayerDB::GetLevel(&v16->m_Param) >= 25 )
    {
      v14 = CPlayerDB::GetRaceCode(&v16->m_Param);
      v9 = (unsigned __int8)CHolyStoneSystem::GetKeeperDestroyRace(v15);
      if ( v14 == (unsigned __int8)v9 )
        return 1;
    }
  }
  return 0;
}
