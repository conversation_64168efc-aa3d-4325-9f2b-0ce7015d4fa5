/*
 * Function: j_??$_Uninit_move@PEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCUnmannedTraderClassInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderClassInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140007DAB
 */

CUnmannedTraderClassInfo **__fastcall std::_Uninit_move<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>,std::_Undefined_move_tag>(CUnmannedTraderClassInfo **_First, CUnmannedTraderClassInfo **_Last, CUnmannedTraderClassInfo **_Dest, std::allocator<CUnmannedTraderClassInfo *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderClassInfo * *,CUnmannedTraderClassInfo * *,std::allocator<CUnmannedTraderClassInfo *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
