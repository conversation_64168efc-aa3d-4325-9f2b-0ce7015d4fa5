/*
 * Function: ?SetBindPosition@CPlayer@@QEAA_NPEAVCMapData@@PEAU_dummy_position@@@Z
 * Address: 0x1400A03C0
 */

bool __fastcall CPlayer::SetBindPosition(CPlayer *this, CMapData *pMap, _dummy_position *pDummy)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@8
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_bOper && pMap && pDummy && v7->m_pUserDB )
  {
    v7->m_pBindMapData = pMap;
    v7->m_pBindDummyData = pDummy;
    result = CUserDB::Update_Bind(v7->m_pUserDB, pMap->m_pMapSet->m_strCode, pDummy->m_szCode, 1);
  }
  else
  {
    result = 0;
  }
  return result;
}
