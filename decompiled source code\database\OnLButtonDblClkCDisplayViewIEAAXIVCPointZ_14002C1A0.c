/*
 * Function: ?OnLButtonDblClk@CDisplayView@@IEAAXIVCPoint@@@Z
 * Address: 0x14002C1A0
 */

void __fastcall CDisplayView::OnLButtonDblClk(CDisplayView *this, unsigned int nFlags, CPoint point)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CDisplayView *v6; // [sp+30h] [bp+8h]@1
  unsigned int v7; // [sp+38h] [bp+10h]@1
  struct CPoint v8; // [sp+40h] [bp+18h]@1

  v8 = point;
  v7 = nFlags;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !CMainThread::IsExcuteService(&g_Main) && unk_1414736F8 )
  {
    stru_141473720.m_bExtendMode = 0;
    CDisplayView::SetExtendMode(v6, 0);
  }
  CWnd::OnLButtonDblClk((CWnd *)&v6->vfptr, v7, v8);
}
