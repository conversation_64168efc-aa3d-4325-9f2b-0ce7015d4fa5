#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <vector>
#include <array>
#include <chrono>
#include <mutex>
#include <unordered_map>
#include <functional>
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cstring>
#include <cmath>
#include <random>
#include "CCharacter.h"
#include "CMonsterManagement.h"
#include "CMonsterDataStructures.h"

namespace NexusProtection::World {

// Forward declarations for external classes not yet implemented
class CRFMonsterAIMgr;
class UsStateTBL;
class Us_HFSM;
template<typename T> class UsPoint;

// Legacy structures that need proper definitions
struct _monster_fld {
    const char* m_strName;       // Monster name string
    float m_fMaxHP;              // Maximum HP
    float m_fMovSpd;             // Normal movement speed
    float m_fWarMovSpd;          // War movement speed
    uint8_t m_bMonsterCondition; // Monster condition
    float m_fHPRecDelay;         // HP recovery delay in seconds
    float m_fHPRecUnit;          // HP recovery amount per tick
    uint32_t m_dwMonsterID;      // Monster ID
    uint16_t m_wLevel;           // Monster level
    uint32_t m_dwExp;            // Experience points
    float m_fAttackRange;        // Attack range
    float m_fSightRange;         // Sight range
};

struct _event_respawn;
struct _event_set;
struct _mon_active;
struct _dummy_position;

struct _monster_create_setdata {
    void* m_pRecordSet;
    float m_fStartPos[3];
    bool bRobExp;
    bool bRewardExp;
    bool bDungeon;
    _mon_active* pActiveRec;
    _dummy_position* pDumPosition;
    void* pParent;
    int m_nLayerIndex;
    void* m_pMap;
};

// Map-related structures for GetObjName method
struct _map_set {
    const char* m_strCode;  // Map code string
    // Additional map set data would be defined here
};

struct _map_data {
    _map_set* m_pMapSet;    // Pointer to map set data
    // Additional map data would be defined here
};

/**
 * @class CMonster
 * @brief Monster entity class for game world
 *
 * This class represents a monster entity in the game world, handling
 * AI, combat, movement, state management, and lifecycle. It inherits from
 * CCharacter and provides extensive functionality for monster behavior.
 *
 * Key Features:
 * - Monster AI and state machine
 * - Combat and aggro management
 * - Movement and rotation control
 * - Emotion and state tracking
 * - Lifecycle and respawn management
 * - Hierarchy for parent/child relationships
 *
 * @note Refactored from decompiled CMonster structure
 */
class CMonster : public NexusProtection::World::CCharacter {
public:
    // Constants
    static constexpr int MAX_EMOTION_STATES = 8;
    static constexpr int EMOTION_STATE_MASK = 0x7;
    static constexpr int EMOTION_STATE_SHIFT = 2;
    static constexpr int EMOTION_STATE_BITS = 0xFFFFFFE3;
    static constexpr uint32_t INVALID_DESTROY_TIME = 0xFFFFFFFF;

    /**
     * @brief Default constructor
     * Initializes the monster with default values
     */
    CMonster();

    /**
     * @brief Virtual destructor
     * Cleans up all allocated resources
     */
    virtual ~CMonster();

    /**
     * @brief Main update loop for monster logic
     * Processes AI, movement, combat, and state changes
     */
    void Loop();

    /**
     * @brief Checks if the monster can move
     * @return true if the monster has a non-zero movement speed
     */
    bool IsMovable() const;

    /**
     * @brief Gets the current movement speed
     * @return Current movement speed based on state
     */
    float GetMoveSpeed() const;

    /**
     * @brief Gets the current movement type
     * @return Current movement type (0 = normal, 1 = combat)
     */
    uint8_t GetMoveType() const;

    /**
     * @brief Sets the movement type
     * @param moveType Movement type to set
     */
    void SetMoveType(uint8_t moveType);

    /**
     * @brief Gets the current HP
     * @return Current health points
     */
    virtual int GetHP() const;

    /**
     * @brief Gets the maximum HP
     * @return Maximum health points
     */
    virtual int GetMaxHP() const;

    /**
     * @brief Gets the object name with position information for debugging
     * @return Formatted string with monster name and position
     *
     * This method is equivalent to the original decompiled GetObjName method from
     * GetObjNameCMonsterUEAAPEADXZ_140142700.c
     */
    virtual const char* GetObjName() const;

    /**
     * @brief Validates the current monster state
     * @return True if monster state is valid, false otherwise
     */
    virtual bool ValidateState() const;

    /**
     * @brief Sets the HP value
     * @param hp New HP value
     * @param notify Whether to notify about the change
     * @return true if HP was set successfully
     */
    bool SetHP(int hp, bool notify = true);

    /**
     * @brief Gets the base HP recovery amount from effect parameters
     * @return Base HP recovery amount per tick
     */
    int GetBaseHPRecovery() const;

    /**
     * @brief Gets effect parameter plus value for a specific effect type
     * @param effectType The effect type to query (e.g., 32 for HP recovery)
     * @return The effect parameter plus value
     *
     * This method is equivalent to _effect_parameter::GetEff_Plus(&m_EP, effectType)
     * from the original decompiled code.
     */
    int GetEffectParameterPlus(int effectType) const;

    /**
     * @brief Gets the current emotion state
     * @return Current emotion state (0-7)
     */
    uint8_t GetEmotionState() const;

    /**
     * @brief Sets the emotion state
     * @param emotionState New emotion state (0-7)
     */
    void SetEmotionState(uint8_t emotionState);

    /**
     * @brief Gets the combat state
     * @return Current combat state
     */
    uint8_t GetCombatState() const;

    /**
     * @brief Sets the combat state
     * @param combatState New combat state
     */
    void SetCombatState(uint8_t combatState);

    /**
     * @brief Creates and initializes the monster with given setup data
     * @param pData Pointer to monster creation setup data
     * @return true if monster was created successfully, false otherwise
     *
     * This method is equivalent to the original decompiled Create method from
     * CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_140141C50.c
     */
    bool Create(const _monster_create_setdata* pData);

    /**
     * @brief Creates AI for the monster
     * @param aiType Type of AI to create
     * @return true if AI was created successfully
     */
    bool CreateAI(int aiType);

private:
    // Helper methods for Create implementation

    /**
     * @brief Creates the character base for the monster
     * @param pData Monster creation data
     * @return true if character base was created successfully
     */
    bool CreateCharacterBase(const _monster_create_setdata* pData);

    /**
     * @brief Resets the emotion presentation checker
     */
    void ResetEmotionPresentationChecker();

    /**
     * @brief Processes active record data during creation
     * @param pData Monster creation data
     */
    void ProcessActiveRecord(const _monster_create_setdata* pData);

    /**
     * @brief Processes dummy position data during creation
     * @param pData Monster creation data
     */
    void ProcessDummyPosition(const _monster_create_setdata* pData);

    /**
     * @brief Processes rotate monster without dummy position
     */
    void ProcessRotateMonsterWithoutDummy();

    /**
     * @brief Initializes all monster systems
     */
    void InitializeMonsterSystems();

    /**
     * @brief Initializes boss-specific monster features
     */
    void InitializeBossMonster();

    /**
     * @brief Initializes the looting manager
     * @param nUserNode Number of user nodes
     */
    void InitializeLootingManager(int nUserNode);

    /**
     * @brief Initializes the aggro manager
     */
    void InitializeAggroManager();

    /**
     * @brief Initializes damage tolerance system
     * @param monsterGrade Monster grade for tolerance calculation
     */
    void InitializeDamageTolerance(int monsterGrade);

    /**
     * @brief Initializes Lua signal reactor
     */
    void InitializeLuaSignalReactor();

    /**
     * @brief Initializes skill pool
     */
    void InitializeSkillPool();

    /**
     * @brief Sets default parts for the monster
     * @param pMonRec Monster record data
     */
    void SetDefPart(_monster_fld* pMonRec);

    /**
     * @brief Writes boss birth log
     */
    void WriteBossBirthLog();

    /**
     * @brief Updates monster hierarchy
     * @param pData Monster creation data
     */
    void UpdateMonsterHierarchy(const _monster_create_setdata* pData);

    /**
     * @brief Increments live monster counter
     */
    void IncrementLiveCounter();

    /**
     * @brief Gets current time
     * @return Current time in milliseconds
     */
    uint32_t GetCurrentTime();

    /**
     * @brief Gets random number
     * @return Random integer
     */
    int GetRandomNumber();



    /**
     * @brief Logs error message
     * @param message Error message to log
     */
    void LogError(const std::string& message);

    /**
     * @brief Sends creation message
     */
    void SendMsg_Create();

public:

    /**
     * @brief Checks and processes monster rotation
     */
    void CheckMonsterRotate();



    /**
     * @brief Sends a message to notify clients about monster rotation change
     */
    void SendMsg_Change_MonsterRotate();

    /**
     * @brief Checks and processes auto HP recovery
     */
    void CheckAutoRecoverHP();

    /**
     * @brief Performs automatic HP recovery with optional bonus
     * @param bonusRecovery Additional recovery amount (default: 0.0f)
     *
     * This method handles the automatic HP recovery for monsters, including:
     * - Base recovery from effect parameters
     * - Optional bonus recovery amount
     * - Minimum HP threshold checks (10% of max HP)
     * - HP bounds validation
     */
    void AutoRecover(float bonusRecovery = 0.0f);

    /**
     * @brief Checks and processes emotion presentation
     */
    void CheckEmotionPresentation();

    /**
     * @brief Checks if the monster should be destroyed after delay
     * @return true if the monster should be destroyed
     */
    bool CheckDelayDestroy();

    /**
     * @brief Checks if respawn process should be initiated
     * @return true if respawn process should start
     */
    bool CheckRespawnProcess();

    /**
     * @brief Checks monster state data for validity
     * @return true if state data is valid
     */
    bool CheckMonsterStateData();

    /**
     * @brief Updates the look-at position
     */
    void UpdateLookAtPos();

    /**
     * @brief Updates the look-at position with specific coordinates
     * @param position Position array [x, y, z]
     */
    void UpdateLookAtPos(const float* position);

    /**
     * @brief Checks if emotion presentation should be triggered
     * @param checkType Type of check to perform
     * @param pTarget Target character
     * @return true if emotion presentation should be triggered
     */
    bool CheckEventEmotionPresentation(uint8_t checkType, CCharacter* pTarget);

    /**
     * @brief Clears emotion presentation state
     */
    void ClearEmotionPresentation();

    /**
     * @brief Sends emotion presentation message to clients
     * @param emotionType Type of emotion to display
     * @param emotionIndex Main emotion index
     * @param randIndex Random emotion index
     * @param targetIndex Target character index (-1 if no target)
     */
    void SendMsg_Emotion_Presentation(uint8_t emotionType, uint16_t emotionIndex,
                                     uint16_t randIndex, int targetIndex);

    /**
     * @brief Gets the monster grade/level
     * @return Monster grade
     */
    int GetMonsterGrade() const;

    /**
     * @brief Gets the visual field range
     * @return Visual field range in units
     */
    float GetVisualField() const;

    /**
     * @brief Gets the visual angle
     * @return Visual angle in degrees
     */
    float GetVisualAngle() const;

    /**
     * @brief Gets the Y-axis angle
     * @return Y-axis angle in degrees
     */
    float GetYAngle() const;

    /**
     * @brief Gets the Y-axis angle as a byte
     * @return Y-axis angle as a byte (0-255)
     */
    uint8_t GetYAngleByte() const;

    /**
     * @brief Links event respawn data to the monster
     * @param pEventRespawn Pointer to event respawn data
     */
    void LinkEventRespawn(_event_respawn* pEventRespawn);

    /**
     * @brief Links event set data to the monster
     * @param pEventSet Pointer to event set data
     */
    void LinkEventSet(_event_set* pEventSet);

    /**
     * @brief Changes apparition state
     * @param apparition Whether to enable apparition
     * @param time Time for apparition effect
     */
    void ChangeApparition(bool apparition, uint32_t time);

    /**
     * @brief Destroys the monster with optional notification
     * @param notify Whether to notify about destruction
     * @param pGameObject Game object that triggered destruction
     * @return true if destruction was successful
     */
    bool DestroyMonster(bool notify, CGameObject* pGameObject);

    /**
     * @brief Commands destruction of child monsters
     * @param serial Serial number of child to destroy
     */
    void Command_ChildMonDestroy(uint32_t serial);

    /**
     * @brief Gets the Lua signal reactor
     * @return Pointer to Lua signal reactor
     */
    CLuaSignalReActor* GetSignalReActor();

    /**
     * @brief Gets a new monster serial number
     * @return New unique serial number
     */
    static uint32_t GetNewMonSerial();

private:
    // Member variables (maintaining original structure layout where possible)
    NexusProtection::World::CLootingMgr m_LootMgr;                          ///< Looting manager
    NexusProtection::World::CMonsterAggroMgr m_AggroMgr;                    ///< Aggro management system
    NexusProtection::World::CMonsterHierarchy m_MonHierarcy;                ///< Monster hierarchy system
    NexusProtection::World::MonsterSFContDamageTolerance m_SFContDamageTolerance; ///< Special function damage tolerance
    NexusProtection::World::EmotionPresentationChecker m_EmotionPresentationCheck; ///< Emotion presentation checker
    NexusProtection::World::MonsterStateData m_MonsterStateData;            ///< Current monster state data
    NexusProtection::World::MonsterStateData m_BeforeMonsterStateData;      ///< Previous monster state data
    NexusProtection::World::CCharacter* m_pTargetChar;                      ///< Current target character
    NexusProtection::World::CMonsterSkillPool m_MonsterSkillPool;           ///< Monster skill pool
    NexusProtection::World::CMonsterAI m_AI;                                ///< Monster AI system
    NexusProtection::World::CLuaSignalReActor m_LuaSignalReActor;           ///< Lua signal reactor
    _monster_fld* m_pMonRec;                        ///< Monster record data
    NexusProtection::World::_effect_parameter* m_EP; ///< Effect parameters

    // Map and position data for GetObjName method
    _map_data* m_pCurMap;                           ///< Current map data
    float m_fTarPos[3];                             ///< Target position

    _event_respawn* m_pEventRespawn;                ///< Event respawn data
    _event_set* m_pEventSet;                        ///< Event set data
    _mon_active* m_pActiveRec;                      ///< Active record
    _dummy_position* m_pDumPosition;                ///< Dummy position
    uint32_t m_dwObjSerial;                         ///< Object serial number
    uint32_t m_dwDestroyNextTime;                   ///< Time for next destroy check
    uint32_t m_dwLastRecoverTime;                   ///< Last HP recovery time
    uint32_t m_LifeCicle;                           ///< Life cycle timer
    uint32_t m_LifeMax;                             ///< Maximum life time
    int m_nHP;                                      ///< Current health points
    uint32_t m_nCommonStateChunk;                   ///< Common state data chunk
    uint8_t m_bRotateMonster;                       ///< Rotation flag
    uint8_t m_bStdItemLoot;                         ///< Standard item loot flag
    uint8_t m_bRobExp;                              ///< Rob experience flag
    uint8_t m_bRewardExp;                           ///< Reward experience flag
    uint8_t m_bDungeon;                             ///< Dungeon monster flag
    uint8_t m_bApparition;                          ///< Apparition flag

    // Position arrays for rotation checking
    float m_fCreatePos[3];                          ///< Initial creation position (x, y, z)
    float m_fCurPos[3];                             ///< Current position (x, y, z)
    float m_fPrevPos[3];                            ///< Previous position for movement direction (x, y, z)
    float m_fStartLookAtPos[3];                     ///< Initial look-at position (x, y, z)
    float m_fLookAtPos[3];                          ///< Current look-at position (x, y, z)
    float m_fYAngle;                                ///< Y-axis rotation angle in degrees
    int m_nEventItemNum;                            ///< Number of event items
    bool m_bLive;                                   ///< Is alive flag
    bool m_bOper;                                   ///< Operation flag
    uint8_t m_emotionState;                         ///< Current emotion state
    uint8_t m_byCreateDate[4];                      ///< Creation date [month, day, hour, minute]

    // Static members
    static int s_nAllocNum;                         ///< Allocation counter
    static int s_nLiveNum;                          ///< Live monster counter

    // Private helper methods
    void InitializeDefaults();
    void InitializeComponents();
    void CleanupResources();
    void ProcessMovement();
    void ProcessCombat();
    void ProcessAI();
    void ProcessLifecycle();
    static void _InitSDM();
    static void _DestroySDM();

    // Disable copy constructor and assignment operator
    CMonster(const CMonster&) = delete;
    CMonster& operator=(const CMonster&) = delete;
};

// Utility functions for monster management
namespace CMonsterUtils {
    /**
     * @brief Creates a new monster instance
     * @return Unique pointer to the created CMonster
     */
    std::unique_ptr<CMonster> CreateMonster();

    /**
     * @brief Validates a monster configuration
     * @param pMonster Pointer to the CMonster to validate
     * @return true if the configuration is valid
     */
    bool ValidateMonster(const CMonster* pMonster);

    /**
     * @brief Gets the memory footprint of a monster
     * @param pMonster Pointer to the CMonster
     * @return Size in bytes of the monster's memory usage
     */
    size_t GetMemoryFootprint(const CMonster* pMonster);
}

} // namespace NexusProtection::World

// Legacy C-style interface for compatibility
extern "C" {
    void CMonster_Constructor(NexusProtection::World::CMonster* pThis);
    void CMonster_Destructor(NexusProtection::World::CMonster* pThis);
    bool CMonster_IsMovable(NexusProtection::World::CMonster* pThis);
    float CMonster_GetMoveSpeed(NexusProtection::World::CMonster* pThis);
    uint8_t CMonster_GetMoveType(NexusProtection::World::CMonster* pThis);
    void CMonster_SetMoveType(NexusProtection::World::CMonster* pThis, uint8_t moveType);
    int CMonster_GetHP(NexusProtection::World::CMonster* pThis);
    int CMonster_GetMaxHP(NexusProtection::World::CMonster* pThis);
    const char* CMonster_GetObjName(NexusProtection::World::CMonster* pThis);
    bool CMonster_SetHP(NexusProtection::World::CMonster* pThis, int hp, bool notify);
    uint8_t CMonster_GetEmotionState(NexusProtection::World::CMonster* pThis);
    void CMonster_SetEmotionState(NexusProtection::World::CMonster* pThis, uint8_t emotionState);
}
