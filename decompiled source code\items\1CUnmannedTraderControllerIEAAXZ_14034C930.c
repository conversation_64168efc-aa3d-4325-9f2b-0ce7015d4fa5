/*
 * Function: ??1CUnmannedTraderController@@IEAA@XZ
 * Address: 0x14034C930
 */

void __fastcall CUnmannedTraderController::~CUnmannedTraderController(CUnmannedTraderController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-68h]@1
  CLogFile *v4; // [sp+20h] [bp-48h]@5
  CLogFile *v5; // [sp+28h] [bp-40h]@5
  CLogFile *v6; // [sp+30h] [bp-38h]@10
  CLogFile *v7; // [sp+38h] [bp-30h]@10
  __int64 v8; // [sp+40h] [bp-28h]@4
  void *v9; // [sp+48h] [bp-20h]@6
  void *v10; // [sp+50h] [bp-18h]@11
  CUnmannedTraderController *v11; // [sp+70h] [bp+8h]@1

  v11 = this;
  v1 = &v3;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v8 = -2i64;
  if ( v11->m_pkLogger )
  {
    v5 = v11->m_pkLogger;
    v4 = v5;
    if ( v5 )
      v9 = CLogFile::`scalar deleting destructor'(v4, 1u);
    else
      v9 = 0i64;
    v11->m_pkLogger = 0i64;
  }
  if ( v11->m_pkServiceLogger )
  {
    v7 = v11->m_pkServiceLogger;
    v6 = v7;
    if ( v7 )
      v10 = CLogFile::`scalar deleting destructor'(v6, 1u);
    else
      v10 = 0i64;
    v11->m_pkServiceLogger = 0i64;
  }
  CUnmannedTraderUserInfoTable::Destroy();
  CUnmannedTraderScheduler::Destroy();
  CUnmannedTraderLazyCleaner::~CUnmannedTraderLazyCleaner(&v11->m_kLazyCleaner);
  CUnmannedTraderTradeInfo::~CUnmannedTraderTradeInfo(&v11->m_kTradeInfo);
}
