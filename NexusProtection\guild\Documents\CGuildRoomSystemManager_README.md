# CGuildRoomSystemManager - Modern C++20 Guild Room System Manager

## Overview

The `CGuildRoomSystemManager` is a comprehensive, modern C++20 implementation that manages the guild room system initialization, map loading, room configuration, and all guild room operations. This system refactors the original decompiled C functions into a robust, maintainable, and feature-rich guild room management system.

## Refactored Sources

This implementation modernizes the following decompiled files:
- **InitCGuildRoomSystemQEAA_NXZ_1402E9A00.c** (316 lines) - Main guild room system initialization
- **0CGuildRoomSystemAEAAXZ_1402E9610.c** (22 lines) - Guild room system constructor
- **1CGuildRoomSystemQEAAXZ_1402E9650.c** (21 lines) - Guild room system destructor
- **RoomInCGuildRoomSystemQEAAHKHKZ_1402EA5F0.c** - Room entry logic
- **RoomOutCGuildRoomSystemQEAAHKHKZ_1402EA6C0.c** - Room exit logic
- **RentRoomCGuildRoomSystemQEAAEEEHKPEAUtagTIMESTAMP__1402EA230.c** - Room rental logic

## Key Features

### 🚀 **Modern C++20 Implementation**
- **RAII Memory Management**: Automatic resource cleanup and exception safety
- **Smart Pointers**: Safe memory handling with proper ownership semantics
- **STL Containers**: Modern containers for efficient data management
- **Atomic Operations**: Thread-safe operations for concurrent access
- **Exception Safety**: Comprehensive error handling and recovery

### 🗺️ **Advanced Map Management**
- **Multi-Race Support**: Separate maps for Bellato, Cora, and Accretia races
- **Room Type Management**: Standard and premium room configurations
- **Map Loading**: Automatic loading and validation of all required maps
- **Portal Management**: Portal dummy position loading and management
- **Neutral Map Support**: Neutral zone map management for each race

### 🏠 **Comprehensive Room System**
- **Room Configuration**: Flexible room setup with configurable parameters
- **Room Rental**: Complete room rental system with pricing and duration
- **Room Access Control**: Entry and exit management with permission checking
- **Room Statistics**: Real-time tracking of room usage and operations
- **Multi-Layer Support**: Support for multiple map layers per room

### 📊 **Advanced Statistics and Monitoring**
- **Real-time Metrics**: Track room initialization, rentals, entries, and exits
- **Performance Monitoring**: Map loading success rates and error tracking
- **Occupancy Tracking**: Monitor active room rentals and usage patterns
- **Error Analytics**: Comprehensive error tracking and reporting

## Architecture

### Class Structure

```cpp
namespace NexusProtection::Guild {
    class CGuildRoomSystemManager {
        // Singleton pattern with thread-safe initialization
        // Comprehensive room system management
        // Event-driven architecture with callbacks
        // Advanced map loading and configuration
    };
}
```

### Key Components

1. **Map Loading Engine**: Automatic loading of all required guild room maps
2. **Room Configuration System**: Flexible room setup and management
3. **Access Control**: Room entry/exit management with validation
4. **Rental System**: Complete room rental with pricing and duration management
5. **Statistics Collection**: Real-time metrics and performance monitoring
6. **Event System**: Callback-based notifications for system events

## Room System Architecture

### Race and Room Type Matrix

The system supports 3 races × 2 room types = 6 different map configurations:

| Race | Standard Room Map | Premium Room Map |
|------|------------------|------------------|
| Bellato | BellaGSD | BellaGSP |
| Cora | CoraGSD | CoraGSP |
| Accretia | AccGSD | AccGSP |

### Room Index Allocation

- **Total Rooms**: 90 (30 per race)
- **Bellato Rooms**: 0-29 (0-19 standard, 20-29 premium)
- **Cora Rooms**: 30-59 (30-49 standard, 50-59 premium)
- **Accretia Rooms**: 60-89 (60-79 standard, 80-89 premium)

## Usage Examples

### Basic Initialization

```cpp
#include "CGuildRoomSystemManager.h"

using namespace NexusProtection::Guild;

// Get singleton instance
auto& roomManager = CGuildRoomSystemManager::GetInstance();

// Configure system
GuildRoomConfig config;
config.maxRoomsPerRace = 30;
config.standardRoomsPerRace = 20;
config.premiumRoomsPerRace = 10;
config.standardRoomPrice = 5000000;
config.premiumRoomPrice = 20000000;

// Initialize system
auto result = roomManager.Initialize(config);
if (result != GuildRoomResult::Success) {
    std::cerr << "Failed to initialize guild room system" << std::endl;
    return -1;
}
```

### Room Rental

```cpp
// Rent a premium room for Bellato guild
uint8_t result = roomManager.RentRoom(
    GuildRace::Bellato,           // Race
    GuildRoomType::Premium,       // Room type
    123,                          // Guild index
    456789,                       // Guild serial
    &timestamp,                   // Rental timestamp
    false                         // Not a restoration
);

if (result == 0) {
    std::cout << "Room rented successfully" << std::endl;
} else {
    std::cerr << "Room rental failed with code: " << static_cast<int>(result) << std::endl;
}
```

### Room Access Management

```cpp
// Character enters room
int entryResult = roomManager.RoomIn(
    456789,    // Guild serial
    25,        // Room index (Bellato premium room)
    987654     // Character serial
);

// Character exits room
int exitResult = roomManager.RoomOut(
    456789,    // Guild serial
    25,        // Room index
    987654     // Character serial
);
```

### Event Callbacks

```cpp
// Set room rental callback
roomManager.SetRoomRentedCallback([](uint32_t guildSerial, int roomIndex, GuildRoomType type) {
    std::cout << "Guild " << guildSerial << " rented ";
    std::cout << (type == GuildRoomType::Premium ? "premium" : "standard");
    std::cout << " room " << roomIndex << std::endl;
});

// Set room access callback
roomManager.SetRoomAccessCallback([](const RoomAccessInfo& info) {
    std::cout << "Character " << info.characterSerial;
    std::cout << " from guild " << info.guildSerial;
    std::cout << (info.isEntry ? " entered" : " exited");
    std::cout << " room " << info.roomIndex << std::endl;
});

// Set map loading callback
roomManager.SetMapLoadedCallback([](const std::string& mapName, bool success) {
    std::cout << "Map " << mapName << " loading: ";
    std::cout << (success ? "SUCCESS" : "FAILED") << std::endl;
});
```

### Statistics Monitoring

```cpp
// Get current statistics
auto stats = roomManager.GetStatistics();

std::cout << "Guild Room System Statistics:" << std::endl;
std::cout << "  Total Rooms Initialized: " << stats.totalRoomsInitialized.load() << std::endl;
std::cout << "  Total Rooms Rented: " << stats.totalRoomsRented.load() << std::endl;
std::cout << "  Active Rentals: " << stats.activeRentals.load() << std::endl;
std::cout << "  Total Room Entries: " << stats.totalRoomEntries.load() << std::endl;
std::cout << "  Total Room Exits: " << stats.totalRoomExits.load() << std::endl;
std::cout << "  Map Loading Errors: " << stats.mapLoadingErrors.load() << std::endl;
std::cout << "  Room Operation Errors: " << stats.roomOperationErrors.load() << std::endl;
std::cout << "  Average Occupancy: " << stats.GetAverageOccupancy() << "%" << std::endl;
std::cout << "  System Uptime: " << stats.GetUptime().count() << " seconds" << std::endl;
```

### Legacy Compatibility

```cpp
// Legacy C-style function calls are automatically handled
extern "C" {
    bool CGuildRoomSystem_Init();
    int CGuildRoomSystem_RoomIn(uint32_t guildSerial, int roomIndex, uint32_t characterSerial);
    int CGuildRoomSystem_RoomOut(uint32_t guildSerial, int roomIndex, uint32_t characterSerial);
    uint8_t CGuildRoomSystem_RentRoom(uint8_t race, uint8_t roomType, int guildIndex, 
                                      uint32_t guildSerial, void* timestamp);
}

// These functions automatically delegate to the modern implementation
bool success = CGuildRoomSystem_Init();  // Calls CGuildRoomSystemManager internally
```

## Configuration Options

### GuildRoomConfig Structure

```cpp
struct GuildRoomConfig {
    uint32_t maxRoomsPerRace = 30;           // Maximum rooms per race
    uint32_t standardRoomsPerRace = 20;      // Standard rooms per race
    uint32_t premiumRoomsPerRace = 10;       // Premium rooms per race
    uint32_t totalRooms = 90;                // Total rooms (3 races * 30 rooms)
    
    // Room pricing and duration
    uint32_t standardRoomPrice = 5000000;    // Standard room price (5M credits)
    uint32_t standardRoomDuration = 604800;  // Standard room duration (7 days)
    uint32_t premiumRoomPrice = 20000000;    // Premium room price (20M credits)
    uint32_t premiumRoomDuration = 2592000;  // Premium room duration (30 days)
    
    // Room limits
    uint8_t standardRoomMaxMembers = 30;     // Standard room member limit
    uint8_t premiumRoomMaxMembers = 30;      // Premium room member limit
};
```

### Configuration Validation

The system automatically validates configuration parameters:
- `maxRoomsPerRace`: Must be greater than 0
- `standardRoomsPerRace + premiumRoomsPerRace`: Must not exceed `maxRoomsPerRace`
- `totalRooms`: Must equal `maxRoomsPerRace * 3` (for 3 races)
- All pricing and duration values are validated for reasonable ranges

## Map Loading System

### Required Maps

The system automatically loads the following maps:

#### Room Maps
- **BellaGSD**: Bellato standard guild rooms
- **CoraGSD**: Cora standard guild rooms
- **AccGSD**: Accretia standard guild rooms
- **BellaGSP**: Bellato premium guild rooms
- **CoraGSP**: Cora premium guild rooms
- **AccGSP**: Accretia premium guild rooms

#### Neutral Maps
- **NeutralB**: Bellato neutral zone
- **NeutralC**: Cora neutral zone
- **NeutralA**: Accretia neutral zone

### Portal Management

The system loads portal dummy positions for each neutral map:
- **dpfrom_bellato_camp**: Bellato HQ portal dummy
- **dpfrom_cora_camp**: Cora HQ portal dummy
- **dpfrom_accretia_camp**: Accretia HQ portal dummy

## Error Handling

### Result Codes

```cpp
enum class GuildRoomResult : uint8_t {
    Success = 0,              // Operation completed successfully
    InitializationFailed,     // System initialization failed
    MapLoadingFailed,         // Map loading failed
    InvalidParameters,        // Invalid parameters provided
    RoomNotAvailable,         // Requested room not available
    RoomAlreadyRented,        // Room already rented by another guild
    InsufficientPermissions,  // Insufficient permissions for operation
    DatabaseError,            // Database operation failed
    SystemError              // General system error
};
```

### Error Recovery

The system implements comprehensive error recovery:
- **Graceful Degradation**: Continue operation even if some maps fail to load
- **Error Tracking**: Monitor consecutive errors and error rates
- **Automatic Recovery**: Reset error counters on successful operations
- **Callback Notifications**: Custom error handling through callbacks

## Performance Characteristics

### Optimizations

- **Atomic Operations**: Lock-free statistics updates where possible
- **Minimal Locking**: Reduced contention with targeted mutex usage
- **Exception Safety**: Strong exception safety guarantees
- **Memory Efficiency**: Minimal memory overhead with efficient data structures
- **Map Caching**: Efficient map data caching and reuse

### Scalability

- **Thread-Safe**: Safe for concurrent access from multiple threads
- **Configurable Limits**: Adjustable room limits based on system capacity
- **Resource Management**: Automatic cleanup and resource management
- **Performance Monitoring**: Real-time performance metrics

## Integration

### Project Integration

The guild room system manager integrates seamlessly with the existing NexusProtection architecture:

1. **Header Inclusion**: Include `CGuildRoomSystemManager.h` in your project
2. **Namespace Usage**: Use `NexusProtection::Guild` namespace
3. **Legacy Compatibility**: Existing C-style calls work automatically
4. **Event Integration**: Connect to existing event systems through callbacks

### Dependencies

- **C++20 Standard**: Requires C++20 compatible compiler
- **STL Libraries**: Uses standard library containers and utilities
- **Legacy Map System**: Interfaces with existing CMapData and CMapOperation
- **Threading Support**: Requires std::thread and std::mutex support

## Best Practices

### Initialization

1. **Early Initialization**: Initialize the room system early in application startup
2. **Configuration Validation**: Always validate configuration before use
3. **Error Checking**: Check initialization results and handle failures
4. **Resource Cleanup**: Ensure proper shutdown on application exit

### Usage Patterns

1. **Regular Monitoring**: Monitor statistics for system health
2. **Event Handling**: Set up callbacks for important events
3. **Error Monitoring**: Monitor error rates and implement alerting
4. **Performance Tuning**: Adjust configuration based on usage patterns

### Performance Tips

1. **Batch Operations**: Process multiple room operations efficiently
2. **Configuration Tuning**: Adjust room limits based on server capacity
3. **Statistics Usage**: Disable statistics in production if not needed
4. **Callback Efficiency**: Keep callback functions lightweight and fast
