/*
 * Function: ??1?$_Vector_const_iterator@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@QEAA@XZ
 * Address: 0x1403D0FB0
 */

void __fastcall std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>::~_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &>((std::_Ranit<GUILD_BATTLE::CGuildBattleRewardItem,__int64,GUILD_BATTLE::CGuildBattleRewardItem const *,GUILD_BATTLE::CGuildBattleRewardItem const &> *)&v4->_Mycont);
}
