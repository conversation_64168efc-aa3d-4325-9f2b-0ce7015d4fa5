/*
 * Function: j_?AuthLastCriTicket@MiningTicket@@QEAAHGEEEE@Z
 * Address: 0x14000DDAA
 */

int __fastcall MiningTicket::AuthLastCriTicket(MiningTicket *this, unsigned __int16 byCurrent<PERSON>ear, char by<PERSON>ur<PERSON><PERSON><PERSON><PERSON>, char byCurrentDay, char byCurrent<PERSON><PERSON>, char byNumOfTime)
{
  return MiningTicket::AuthLastCriTicket(this, byCurrent<PERSON>ear, byCurrentMonth, byCurrentDay, byCurrentHour, byNumOfTime);
}
