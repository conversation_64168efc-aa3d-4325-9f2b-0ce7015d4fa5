/*
 * Function: SQLWritePrivateProfileString
 * Address: 0x1404DAA10
 */

int __fastcall SQLWritePrivateProfileString(const char *lpszSection, const char *lpszEntry, const char *lpszString, const char *lpszFilename)
{
  const char *v4; // rbp@1
  const char *v5; // rbx@1
  const char *v6; // rdi@1
  const char *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszSection;
  v5 = lpszFilename;
  v6 = lpszString;
  v7 = lpszEntry;
  v8 = ODBC___GetSetupProc("SQLWritePrivateProfileString");
  if ( v8 )
    result = ((int (__fastcall *)(const char *, const char *, const char *, const char *))v8)(v4, v7, v6, v5);
  else
    result = 0;
  return result;
}
