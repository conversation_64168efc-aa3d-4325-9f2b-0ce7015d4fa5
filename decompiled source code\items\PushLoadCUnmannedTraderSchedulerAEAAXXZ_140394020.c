/*
 * Function: ?PushLoad@CUnmannedTraderScheduler@@AEAAXXZ
 * Address: 0x140394020
 */

void __fastcall CUnmannedTraderScheduler::PushLoad(CUnmannedTraderScheduler *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1

  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 58, 0i64, 0);
}
