/*
 * Function: ?mgr_kick@CPlayer@@QEAA_NPEAD@Z
 * Address: 0x1400B8B30
 */

char __fastcall CPlayer::mgr_kick(CPlayer *this, char *pwszCharName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *pszCause; // [sp+20h] [bp-28h]@18
  CUserDB *v7; // [sp+30h] [bp-18h]@5
  CGameObject *v8; // [sp+38h] [bp-10h]@18
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pwszCharName )
  {
    v7 = SearchAvatorWithName(g_UserDB, 2532, pwszCharName);
    if ( v7 )
    {
      CUserDB::ForceCloseCommand(v7, 0, 0, 1, "Kick By GM");
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else if ( v9->m_TargetObject.pObject )
  {
    if ( v9->m_TargetObject.pObject->m_bLive )
    {
      if ( v9->m_TargetObject.pObject->m_ObjID.m_byKind || v9->m_TargetObject.pObject->m_ObjID.m_byID )
      {
        result = 0;
      }
      else if ( v9->m_TargetObject.byKind == v9->m_TargetObject.pObject->m_ObjID.m_byKind
             && v9->m_TargetObject.byID == v9->m_TargetObject.pObject->m_ObjID.m_byID )
      {
        v8 = v9->m_TargetObject.pObject;
        pszCause = "Kick By GM";
        CUserDB::ForceCloseCommand(*(CUserDB **)&v8[10].m_ObjID.m_byKind, 0, 0, 1, "Kick By GM");
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
