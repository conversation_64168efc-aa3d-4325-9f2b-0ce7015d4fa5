/*
 * Function: _CCryptParam::CheckPublicKeyHash_::_1_::dtor$0
 * Address: 0x140448110
 */

void __fastcall CCryptParam::CheckPublicKeyHash_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::<PERSON><PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWith<PERSON>leanup<unsigned char,1>>::~Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,1>>((CryptoPP::<PERSON><PERSON><PERSON><PERSON><unsigned char,CryptoPP::Allocator<PERSON>ith<PERSON>leanup<unsigned char,1> > *)(a2 + 40));
}
