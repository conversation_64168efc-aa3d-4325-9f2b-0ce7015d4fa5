/*
 * Function: ?GetRaceTown@CMapData@@QEAAEPEAME@Z
 * Address: 0x1401862D0
 */

char __fastcall CMapData::GetRaceTown(CMapData *this, float *fPos, char byRaceCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int32 v7; // [sp+20h] [bp-18h]@10
  unsigned __int32 j; // [sp+24h] [bp-14h]@10
  char *Str1; // [sp+28h] [bp-10h]@13
  CMapData *v10; // [sp+40h] [bp+8h]@1
  float *v11; // [sp+48h] [bp+10h]@1
  char v12; // [sp+50h] [bp+18h]@1

  v12 = byRaceCode;
  v11 = fPos;
  v10 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v10->m_pExtDummy_Town )
  {
    if ( v10->m_pMapSet->m_nMapType == 2 )
    {
      if ( (signed int)(unsigned __int8)byRaceCode >= 3 )
        result = -1;
      else
        result = byRaceCode;
    }
    else
    {
      v7 = CExtDummy::GetTotalNum(v10->m_pExtDummy_Town);
      for ( j = 0; ; ++j )
      {
        if ( j >= v7 )
          return -1;
        if ( CExtDummy::IsInBBox(v10->m_pExtDummy_Town, j, v11) )
          break;
      }
      Str1 = (char *)CExtDummy::GetDummy(v10->m_pExtDummy_Town, j);
      switch ( *Str1 )
      {
        case 98:
          result = 0;
          break;
        case 99:
          result = 1;
          break;
        case 97:
          result = 2;
          break;
        default:
          if ( !_strnicmp(Str1, "elan", 4ui64) )
          {
            result = 3;
          }
          else if ( (signed int)(unsigned __int8)v12 >= 3 )
          {
            result = -1;
          }
          else
          {
            result = v12;
          }
          break;
      }
    }
  }
  else
  {
    result = -1;
  }
  return result;
}
