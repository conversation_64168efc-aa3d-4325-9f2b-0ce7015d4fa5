/*
 * Function: j_??A?$vector@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderClassInfo@@_K@Z
 * Address: 0x140004F57
 */

CUnmannedTraderClassInfo **__fastcall std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator[](std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::operator[](this, _Pos);
}
