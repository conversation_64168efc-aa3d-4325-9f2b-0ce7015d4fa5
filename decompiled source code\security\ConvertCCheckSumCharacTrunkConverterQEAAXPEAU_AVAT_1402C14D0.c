/*
 * Function: ?Convert@CCheckSumCharacTrunkConverter@@QEAAXPEAU_AVATOR_DATA@@PEAVCCheckSumCharacAccountTrunkData@@@Z
 * Address: 0x1402C14D0
 */

void __usercall CCheckSumCharacTrunkConverter::Convert(CCheckSumCharacTrunkConverter *this@<rcx>, _AVATOR_DATA *pAvator@<rdx>, CCheckSumCharacAccountTrunkData *pkCheckSum@<r8>, long double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@5
  unsigned int v7; // eax@5
  unsigned int v8; // eax@5
  unsigned int v9; // eax@5
  unsigned int v10; // eax@5
  unsigned int v11; // eax@5
  __int64 v12; // [sp+0h] [bp-28h]@1
  CCheckSumCharacTrunkConverter *v13; // [sp+30h] [bp+8h]@1
  _AVATOR_DATA *v14; // [sp+38h] [bp+10h]@1
  CCheckSumCharacAccountTrunkData *v15; // [sp+40h] [bp+18h]@1

  v15 = pkCheckSum;
  v14 = pAvator;
  v13 = this;
  v4 = &v12;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pAvator )
  {
    v6 = CCheckSumBaseConverter::ProcCode(&v13->0, 0, pkCheckSum->m_dwSerial, pAvator->dbAvator.m_dwDalant);
    CCheckSumCharacAccountTrunkData::SetValue(v15, 0, v6);
    v7 = CCheckSumBaseConverter::ProcCode(&v13->0, 1, v15->m_dwSerial, v14->dbAvator.m_dwGold);
    CCheckSumCharacAccountTrunkData::SetValue(v15, CDWT_GOLD, v7);
    v8 = CCheckSumBaseConverter::ProcCode(&v13->0, 2, v15->m_dwSerial, v14->dbAvator.m_byLevel);
    CCheckSumCharacAccountTrunkData::SetValue(v15, CDWT_LV, v8);
    v9 = CCheckSumBaseConverter::ProcCode(&v13->0, 3, v15->m_dwSerial, v14->dbStat.m_dwDamWpCnt[0]);
    CCheckSumCharacAccountTrunkData::SetValue(v15, CDWT_NEAR_MASTERY, v9);
    v10 = CCheckSumBaseConverter::ProcCode(&v13->0, 4, v15->m_dwSerial, v14->dbStat.m_dwDefenceCnt);
    CCheckSumCharacAccountTrunkData::SetValue(v15, CDWT_DIS_MASTERY, v10);
    v11 = CCheckSumBaseConverter::ProcCode(&v13->0, 5, v15->m_dwSerial, v14->dbStat.m_dwDamWpCnt[1]);
    CCheckSumCharacAccountTrunkData::SetValue(v15, CDWT_DEF_MASTERY, v11);
    CCheckSumBaseConverter::ProcCode(&v13->0, 2 * v15->m_byRace, v15->m_dwAccountSerial, v14->dbTrunk.dDalant);
    CCheckSumCharacAccountTrunkData::SetValue(v15, 0, a4);
    CCheckSumBaseConverter::ProcCode(&v13->0, 2 * v15->m_byRace + 1, v15->m_dwAccountSerial, v14->dbTrunk.dGold);
    CCheckSumCharacAccountTrunkData::SetValue(v15, CDT_TRUNK_GOLD, a4);
  }
}
