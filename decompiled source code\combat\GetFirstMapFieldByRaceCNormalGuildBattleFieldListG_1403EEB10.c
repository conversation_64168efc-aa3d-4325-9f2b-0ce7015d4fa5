/*
 * Function: ?GetFirstMapFieldByRace@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAAPEAVCNormalGuildBattleField@2@E@Z
 * Address: 0x1403EEB10
 */

GUILD_BATTLE::CNormalGuildBattleField *__fastcall GUILD_BATTLE::CNormalGuildBattleFieldList::GetFirstMapFieldByRace(GUILD_BATTLE::CNormalGuildBattleFieldList *this, char byRace)
{
  GUILD_BATTLE::CNormalGuildBattleField *result; // rax@2

  if ( (signed int)(unsigned __int8)byRace < 3 )
  {
    if ( this->m_byUseFieldCnt[(unsigned __int8)byRace] && this->m_ppkUseFieldByRace[(unsigned __int8)byRace] )
      result = *this->m_ppkUseFieldByRace[(unsigned __int8)byRace];
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
