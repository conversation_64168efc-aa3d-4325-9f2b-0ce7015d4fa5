/*
 * Function: ?Select_PatriarchComm@CRFWorldDatabase@@QEAAHKPEAU_patriarch_comm_list@@@Z
 * Address: 0x1404BF720
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PatriarchComm(CRFWorldDatabase *this, unsigned int dwSerial, _patriarch_comm_list *pOutList)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  _patriarch_comm_list::__list *v6; // rax@20
  char *v7; // rax@20
  __int64 v8; // [sp+0h] [bp-138h]@1
  void *SQLStmt; // [sp+20h] [bp-118h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-110h]@20
  SQLLEN v11; // [sp+38h] [bp-100h]@20
  __int16 v12; // [sp+44h] [bp-F4h]@9
  char szToday; // [sp+58h] [bp-E0h]@4
  char Dest; // [sp+90h] [bp-A8h]@4
  unsigned __int64 v15; // [sp+120h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+140h] [bp+8h]@1
  unsigned int v17; // [sp+148h] [bp+10h]@1
  _patriarch_comm_list *v18; // [sp+150h] [bp+18h]@1

  v18 = pOutList;
  v17 = dwSerial;
  v16 = this;
  v3 = &v8;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = (unsigned __int64)&v8 ^ _security_cookie;
  GetTodayStr(&szToday);
  sprintf(&Dest, "{ CALL pSelect_PatriarchComm( %d, '%s' ) }", v17, &szToday);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( !v16->m_hStmtSelect && !CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    return 1i64;
  }
  v12 = SQLExecDirectA_0(v16->m_hStmtSelect, &Dest, -3);
  if ( v12 && v12 != 1 )
  {
    if ( v12 == 100 )
    {
      result = 2i64;
    }
    else
    {
      SQLStmt = v16->m_hStmtSelect;
      CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLExecDirectA", SQLStmt);
      CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
      result = 1i64;
    }
    return result;
  }
  while ( 1 )
  {
    v12 = SQLFetch_0(v16->m_hStmtSelect);
    if ( v12 && v12 != 1 )
    {
      if ( v12 != 100 )
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLFetch", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
        if ( v16->m_hStmtSelect )
          SQLCloseCursor_0(v16->m_hStmtSelect);
        return 1i64;
      }
      goto LABEL_27;
    }
    v6 = &v18->List[v18->dwCount];
    StrLen_or_IndPtr = &v11;
    SQLStmt = 0i64;
    v12 = SQLGetData_0(v16->m_hStmtSelect, 1u, 4, v6, 0i64, &v11);
    v7 = v18->List[v18->dwCount].pszDepDate;
    StrLen_or_IndPtr = &v11;
    SQLStmt = (void *)9;
    v12 = SQLGetData_0(v16->m_hStmtSelect, 2u, 1, v7, 9i64, &v11);
    if ( v12 )
    {
      if ( v12 != 1 )
        break;
    }
    ++v18->dwCount;
  }
  if ( v12 == 100 )
  {
LABEL_27:
    if ( v16->m_hStmtSelect )
      SQLCloseCursor_0(v16->m_hStmtSelect);
    if ( v16->m_bSaveDBLog )
      CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
    return 0i64;
  }
  SQLStmt = v16->m_hStmtSelect;
  CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v12, &Dest, "SQLFetch", SQLStmt);
  CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v12, v16->m_hStmtSelect);
  if ( v16->m_hStmtSelect )
    SQLCloseCursor_0(v16->m_hStmtSelect);
  return 1i64;
}
