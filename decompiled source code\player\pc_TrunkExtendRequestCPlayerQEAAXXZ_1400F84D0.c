/*
 * Function: ?pc_TrunkExtendRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400F84D0
 */

void __fastcall CPlayer::pc_TrunkExtendRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@12
  char v4; // al@15
  CMoneySupplyMgr *v5; // rax@19
  char v6; // al@20
  unsigned int v7; // eax@20
  char v8; // al@21
  __int64 v9; // [sp+0h] [bp-118h]@1
  char v10; // [sp+40h] [bp-D8h]@4
  unsigned int dwSub; // [sp+44h] [bp-D4h]@4
  int v12; // [sp+48h] [bp-D0h]@15
  char Dest; // [sp+60h] [bp-B8h]@20
  unsigned int dwLeftDalant; // [sp+E4h] [bp-34h]@21
  int nLv; // [sp+F0h] [bp-28h]@19
  int v16; // [sp+F4h] [bp-24h]@19
  char *v17; // [sp+F8h] [bp-20h]@20
  unsigned int v18; // [sp+100h] [bp-18h]@20
  unsigned __int64 v19; // [sp+108h] [bp-10h]@4
  CPlayer *p; // [sp+120h] [bp+8h]@1

  p = this;
  v1 = &v9;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  v10 = 0;
  dwSub = 500000;
  if ( IsBeNearStore(p, 10) )
  {
    if ( p->m_Param.m_bTrunkOpen )
    {
      if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) < 100 )
      {
        if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) > 0 )
        {
          v3 = CPlayerDB::GetDalant(&p->m_Param);
          if ( v3 < dwSub )
            v10 = 6;
        }
        else
        {
          v10 = 2;
        }
      }
      else
      {
        v10 = 4;
      }
    }
    else
    {
      v10 = 14;
    }
  }
  else
  {
    v10 = 13;
  }
  if ( !v10 )
  {
    CPlayer::SubDalant(p, dwSub);
    p->m_Param.m_byTrunkSlotNum += 20;
    _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&p->m_Param.m_dbTrunk.m_nListNum, p->m_Param.m_byTrunkSlotNum);
    v4 = CPlayerDB::GetTrunkSlotNum(&p->m_Param);
    CUserDB::Update_TrunkSlotNum(p->m_pUserDB, v4);
    v12 = CPlayerDB::GetLevel(&p->m_Param);
    if ( v12 == 30 || v12 == 40 || v12 == 50 || v12 == 60 )
    {
      nLv = CPlayerDB::GetLevel(&p->m_Param);
      v16 = CPlayerDB::GetRaceCode(&p->m_Param);
      v5 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v5, v16, nLv, dwSub);
    }
    v6 = CPlayerDB::GetTrunkSlotNum(&p->m_Param);
    sprintf(&Dest, "EXTEND TRUNK(%d)", (unsigned __int8)v6);
    v17 = p->m_szItemHistoryFileName;
    v18 = CPlayerDB::GetGold(&p->m_Param);
    v7 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::pay_money(&CPlayer::s_MgrItemHistory, p->m_ObjID.m_wIndex, &Dest, dwSub, 0, v7, v18, v17);
  }
  dwLeftDalant = CPlayerDB::GetDalant(&p->m_Param);
  v8 = CPlayerDB::GetTrunkSlotNum(&p->m_Param);
  CPlayer::SendMsg_TrunkExtendResult(p, v10, v8, dwLeftDalant, dwSub);
}
