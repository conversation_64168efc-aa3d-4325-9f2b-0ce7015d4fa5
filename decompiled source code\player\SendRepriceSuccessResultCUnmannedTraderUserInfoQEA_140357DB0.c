/*
 * Function: ?SendRepriceSuccessResult@CUnmannedTraderUserInfo@@QEAAXPEAVCPlayer@@GKKK@Z
 * Address: 0x140357DB0
 */

void __fastcall CUnmannedTraderUserInfo::SendRepriceSuccessResult(CUnmannedTraderUserInfo *this, CPlayer *pReceiver, unsigned __int16 wItemSerial, unsigned int dwNewPrice, unsigned int dwRegistSerial, unsigned int dwTax)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@4
  __int64 v9; // [sp+0h] [bp-88h]@1
  _a_trade_adjust_price_result_zocl v10; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  CPlayer *v13; // [sp+98h] [bp+10h]@1

  v13 = pReceiver;
  v6 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10.byRetCode = 0;
  v10.wItemSerial = wItemSerial;
  v10.dwNewPrice = dwNewPrice;
  v10.dwRegistSerial = dwRegistSerial;
  v10.dwLeftDalant = CPlayerDB::GetDalant(&pReceiver->m_Param);
  v10.byTaxRate = 1;
  v10.dwTax = dwTax;
  pbyType = 30;
  v12 = 10;
  v8 = _a_trade_adjust_price_result_zocl::size(&v10);
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &v10.byRetCode, v8);
}
