/*
 * Function: ?_db_qry_update_moveore@AutoMineMachineMng@@AEAAEPEAD@Z
 * Address: 0x1402D71F0
 */

char __fastcall AutoMineMachineMng::_db_qry_update_moveore(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-68h]@1
  char bySSlot; // [sp+20h] [bp-48h]@4
  unsigned int dwSK; // [sp+28h] [bp-40h]@4
  char bySNum; // [sp+30h] [bp-38h]@4
  char byDSlot; // [sp+38h] [bp-30h]@4
  unsigned int dwDK; // [sp+40h] [bp-28h]@4
  char byDNum; // [sp+48h] [bp-20h]@4
  char *v12; // [sp+50h] [bp-18h]@4

  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = pdata;
  byDNum = pdata[18];
  dwDK = *(_DWORD *)(pdata + 14);
  byDSlot = pdata[13];
  bySNum = pdata[12];
  dwSK = *((_DWORD *)pdata + 2);
  bySSlot = pdata[7];
  if ( CRFWorldDatabase::update_amine_moveore(
         pkDB,
         pdata[1],
         pdata[2],
         *(_DWORD *)(pdata + 3),
         bySSlot,
         dwSK,
         bySNum,
         byDSlot,
         dwDK,
         byDNum) )
  {
    result = 0;
  }
  else
  {
    result = 24;
  }
  return result;
}
