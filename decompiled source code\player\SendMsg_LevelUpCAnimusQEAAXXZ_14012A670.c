/*
 * Function: ?SendMsg_LevelUp@CAnimus@@QEAAXXZ
 * Address: 0x14012A670
 */

void __fastcall CAnimus::SendMsg_LevelUp(CAnimus *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  unsigned int v5; // [sp+36h] [bp-42h]@4
  char v6; // [sp+3Ah] [bp-3Eh]@5
  char pbyType; // [sp+54h] [bp-24h]@7
  char v8; // [sp+55h] [bp-23h]@7
  CAnimus *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_WORD *)szMsg = v9->m_ObjID.m_wIndex;
  v5 = v9->m_dwObjSerial;
  if ( v9->m_pRecord )
    v6 = v9->m_pRecord->m_nLevel;
  else
    v6 = 1;
  pbyType = 22;
  v8 = 12;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, szMsg, 7, 0);
}
