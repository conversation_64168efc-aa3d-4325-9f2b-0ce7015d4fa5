/*
 * Function: ??0_itembox_create_setdata@@QEAA@XZ
 * Address: 0x140167830
 */

void __fastcall _itembox_create_setdata::_itembox_create_setdata(_itembox_create_setdata *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _itembox_create_setdata *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _object_create_setdata::_object_create_setdata((_object_create_setdata *)&v4->m_pRecordSet);
  _STORAGE_LIST::_db_con::_db_con(&v4->Item);
  v4->pOwner = 0i64;
  v4->pThrower = 0i64;
  v4->byCreateCode = 0;
  v4->bParty = 0;
  v4->dwPartyBossSerial = -1;
  v4->pAttacker = 0i64;
  v4->bHolyScanner = 0;
  v4->byEventItemLootAuth = 3;
}
