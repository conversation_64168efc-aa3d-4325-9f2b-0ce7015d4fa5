/*
 * Function: ?LoadGuildBattleInfo@CRFWorldDatabase@@QEAA_NKKPEAU_worlddb_guild_battle_info@@@Z
 * Address: 0x1404A25D0
 */

char __fastcall CRFWorldDatabase::LoadGuildBattleInfo(CRFWorldDatabase *this, unsigned int dwStartID, unsigned int dwRowCnt, _worlddb_guild_battle_info *pkInfo)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-488h]@1
  void *SQLStmt; // [sp+20h] [bp-468h]@15
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-460h]@18
  SQLLEN v10; // [sp+38h] [bp-450h]@18
  __int16 v11; // [sp+44h] [bp-444h]@11
  char Dest; // [sp+60h] [bp-428h]@6
  unsigned int v13; // [sp+464h] [bp-24h]@6
  unsigned __int64 v14; // [sp+470h] [bp-18h]@4
  CRFWorldDatabase *v15; // [sp+490h] [bp+8h]@1
  unsigned int v16; // [sp+4A0h] [bp+18h]@1
  _worlddb_guild_battle_info *v17; // [sp+4A8h] [bp+20h]@1

  v17 = pkInfo;
  v16 = dwRowCnt;
  v15 = this;
  v4 = &v7;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pkInfo )
  {
    v13 = 0;
    sprintf(&Dest, "{ CALL pSelect_ReservedGuildBattleInfo(%u,%u) }", dwStartID, dwRowCnt + dwStartID);
    if ( v15->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v15->vfptr, &Dest);
    if ( v15->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v15->vfptr) )
    {
      v11 = SQLExecDirect_0(v15->m_hStmtSelect, &Dest, -3);
      if ( v11 && v11 != 1 )
      {
        if ( v11 == 100 )
        {
          result = 0;
        }
        else
        {
          SQLStmt = v15->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v15->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v15->vfptr, v11, v15->m_hStmtSelect);
          result = 0;
        }
      }
      else
      {
        do
        {
          v11 = SQLFetch_0(v15->m_hStmtSelect);
          if ( v11 && v11 != 1 )
            break;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v15->m_hStmtSelect, 1u, -18, &v17->list[v13], 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v15->m_hStmtSelect, 2u, -18, &v17->list[v13].dwP1GuildSerial, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v15->m_hStmtSelect, 3u, -18, &v17->list[v13].dwP2GuildSerial, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v15->m_hStmtSelect, 4u, -18, &v17->list[v13].dwMapID, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v15->m_hStmtSelect, 5u, -6, &v17->list[v13++].byNumber, 0i64, &v10);
        }
        while ( v16 > v13 );
        v17->wCount = v13;
        if ( v15->m_hStmtSelect )
          SQLCloseCursor_0(v15->m_hStmtSelect);
        if ( v15->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v15->vfptr, "%s Success", &Dest);
        result = 1;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v15->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
