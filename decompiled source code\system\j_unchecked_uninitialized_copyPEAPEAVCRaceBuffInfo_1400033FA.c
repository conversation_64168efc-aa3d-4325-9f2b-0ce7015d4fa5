/*
 * Function: j_??$unchecked_uninitialized_copy@PEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@stdext@@YAPEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@Z
 * Address: 0x1400033FA
 */

CRaceBuffInfoByHolyQuestfGroup **__fastcall stdext::unchecked_uninitialized_copy<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(CRaceBuffInfoByHolyQuestfGroup **_First, CRaceBuffInfoByHolyQuestfGroup **_Last, CRaceBuffInfoByHolyQuestfGroup **_Dest, std::allocator<CRaceBuffInfoByHolyQuestfGroup *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
