/*
 * Function: j_??$_Uninit_move@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14001057D
 */

CUnmannedTraderDivisionInfo **__fastcall std::_Uninit_move<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>,std::_Undefined_move_tag>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Dest, std::allocator<CUnmannedTraderDivisionInfo *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
