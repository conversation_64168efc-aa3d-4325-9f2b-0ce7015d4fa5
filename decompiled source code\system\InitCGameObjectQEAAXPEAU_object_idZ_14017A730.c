/*
 * Function: ?Init@CGameObject@@QEAAXPEAU_object_id@@@Z
 * Address: 0x14017A730
 */

void __fastcall CGameObject::Init(CGameObject *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CGameObject *pItem; // [sp+30h] [bp+8h]@1

  pItem = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  pItem->m_ObjID = *pID;
  _object_list_point::SetPoint(&pItem->m_SectorPoint, pItem);
  _object_list_point::SetPoint(&pItem->m_SectorNetPoint, pItem);
  pItem->m_bMapLoading = 0;
  pItem->m_bMaxVision = 0;
  pItem->m_nCirclePlayerNum = 0;
  CGameObject::s_pTotalObject[CGameObject::s_nTotalObjectNum] = pItem;
  pItem->m_nTotalObjIndex = CGameObject::s_nTotalObjectNum;
  pItem->m_pRecordSet = 0i64;
  pItem->m_dwObjSerial = 0;
  pItem->m_bLive = 0;
  pItem->m_bCorpse = 0;
  pItem->m_bMove = 0;
  pItem->m_bStun = 0;
  pItem->m_dwLastSendTime = 0;
  LODWORD(pItem->m_fCurPos[0]) = 0;
  LODWORD(pItem->m_fCurPos[1]) = 0;
  LODWORD(pItem->m_fCurPos[2]) = 0;
  LODWORD(pItem->m_fAbsPos[0]) = 0;
  LODWORD(pItem->m_fAbsPos[1]) = 0;
  LODWORD(pItem->m_fAbsPos[2]) = 0;
  pItem->m_nScreenPos[0] = 0;
  pItem->m_nScreenPos[1] = 0;
  LODWORD(pItem->m_fOldPos[0]) = 0;
  LODWORD(pItem->m_fOldPos[1]) = 0;
  LODWORD(pItem->m_fOldPos[2]) = 0;
  pItem->m_pCurMap = 0i64;
  pItem->m_wMapLayerIndex = 0;
  pItem->m_dwNextFreeStunTime = -1;
  pItem->m_dwOldTickBreakTranspar = -1;
  pItem->m_bBreakTranspar = 0;
  pItem->m_bPlayerCircleList = 0i64;
  pItem->m_bObserver = 0;
  pItem->m_dwCurSec = 0;
  if ( ++CGameObject::s_nTotalObjectNum > 42642 )
  {
    MyMessageBox("error", "CGameObject::Init : Lack Object Num");
    ServerProgramExit("CGameObject::Init()", 0);
  }
}
