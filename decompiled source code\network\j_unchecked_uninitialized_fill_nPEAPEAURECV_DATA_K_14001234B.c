/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAURECV_DATA@@_KPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@stdext@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAURECV_DATA@@@std@@@Z
 * Address: 0x14001234B
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, unsigned __int64 _Count, RECV_DATA *const *_Val, std::allocator<RECV_DATA *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(
    _First,
    _<PERSON>,
    _<PERSON>,
    _<PERSON>);
}
