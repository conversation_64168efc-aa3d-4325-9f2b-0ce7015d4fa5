/*
 * Function: j_??$_Ptr_cat@V?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@PEAVCUnmannedTraderItemCodeInfo@@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Vector_const_iterator@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@0@AEAPEAVCUnmannedTraderItemCodeInfo@@@Z
 * Address: 0x1400043B8
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *__formal, CUnmannedTraderItemCodeInfo **a2)
{
  return std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>,CUnmannedTraderItemCodeInfo *>(
           __formal,
           a2);
}
