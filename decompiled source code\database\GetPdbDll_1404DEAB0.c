/*
 * Function: GetPdbDll
 * Address: 0x1404DEAB0
 */

HINSTANCE__ *__cdecl GetPdbDll()
{
  HINSTANCE__ *result; // rax@2
  HMODULE v1; // rsi@4
  HINSTANCE__ *v2; // rbx@5
  HINSTANCE__ *v3; // rbp@6
  FARPROC v4; // r12@7
  signed __int64 v5; // rdi@11
  int v6; // ebx@15
  int v7; // edx@16
  LPCSTR v8; // r8@19
  char v9; // al@20
  __int64 v10; // rcx@20
  int v11; // [sp+30h] [bp-138h]@12
  char v12; // [sp+34h] [bp-134h]@11
  __int64 v13; // [sp+38h] [bp-130h]@11
  char LibFileName[272]; // [sp+40h] [bp-128h]@16

  if ( alreadyTried )
    return 0i64;
  alreadyTried = 1;
  result = LoadLibraryA(mspdbName);
  if ( !result )
  {
    result = LoadLibraryA("ADVAPI32.DLL");
    v1 = result;
    if ( result )
    {
      result = (HINSTANCE__ *)GetProcAddress(result, "RegOpenKeyExA");
      v2 = result;
      if ( result )
      {
        result = (HINSTANCE__ *)GetProcAddress(v1, "RegQueryValueExA");
        v3 = result;
        if ( result )
        {
          v4 = GetProcAddress(v1, "RegCloseKey");
          if ( !v4 )
            return 0i64;
          if ( ((int (__fastcall *)(signed __int64, const char *, _QWORD, signed __int64))v2)(
                 -2147483646i64,
                 "SOFTWARE\\Microsoft\\VisualStudio\\8.0\\Setup\\VS",
                 0i64,
                 1i64) )
          {
            FreeLibrary(v1);
            return 0i64;
          }
          v5 = 0i64;
          if ( ((int (__fastcall *)(__int64, const char *, _QWORD, char *))v3)(v13, "EnvironmentDirectory", 0i64, &v12)
            || (unsigned int)(v11 + 13) >= 0x104 )
          {
            FreeLibrary(v1);
            return 0i64;
          }
          v6 = ((int (__fastcall *)(__int64, const char *, _QWORD, char *))v3)(v13, "EnvironmentDirectory", 0i64, &v12);
          ((void (__fastcall *)(__int64))v4)(v13);
          FreeLibrary(v1);
          if ( v6 )
            return 0i64;
          v7 = v11;
          if ( LibFileName[v11 - 2] == 92 )
            v7 = v11-- - 1;
          else
            LibFileName[v11 - 1] = 92;
          v8 = mspdbName;
          do
          {
            v9 = v8[v5];
            v10 = (unsigned int)v7;
            ++v5;
            ++v7;
            LibFileName[v10] = v9;
          }
          while ( v5 <= 11 );
          result = LoadLibraryA(LibFileName);
        }
      }
    }
  }
  return result;
}
