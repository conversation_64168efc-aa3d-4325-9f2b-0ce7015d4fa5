/*
 * Function: ?SF_HPInc_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009DE60
 */

char __usercall CPlayer::SF_HPInc_Once@<al>(CPlayer *this@<rcx>, CCharacter *pDstObj@<rdx>, float fEffectValue@<xmm2>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  char v8; // [sp+20h] [bp-28h]@4
  int v9; // [sp+24h] [bp-24h]@8
  int v10; // [sp+28h] [bp-20h]@8
  float v11; // [sp+2Ch] [bp-1Ch]@8
  int v12; // [sp+30h] [bp-18h]@8
  CGameObjectVtbl *v13; // [sp+38h] [bp-10h]@8
  CCharacter *v14; // [sp+58h] [bp+10h]@1

  v14 = pDstObj;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  if ( !pDstObj->m_ObjID.m_byID && !pDstObj->m_bCorpse )
    v8 = 1;
  if ( v8 )
  {
    v9 = ((int (__fastcall *)(CCharacter *))pDstObj->vfptr->GetHP)(pDstObj);
    v10 = ((int (__fastcall *)(CCharacter *))v14->vfptr->GetMaxHP)(v14);
    _effect_parameter::GetEff_Rate(&v14->m_EP, 18);
    v11 = fEffectValue * a4;
    v12 = (signed int)ffloor((float)v10 * (float)(fEffectValue * a4));
    v13 = v14->vfptr;
    ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD))v13->SetHP)(v14, (unsigned int)(v12 + v9), 0i64);
    if ( (float)((float)v9 / (float)v10) <= 0.80000001 )
    {
      (*(void (__fastcall **)(CCharacter *))&v14->vfptr->gap8[72])(v14);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
