/*
 * Function: ?ConvertOut@ModExpPrecomputation@CryptoPP@@UEBA?AVInteger@2@AEBV32@@Z
 * Address: 0x14055F160
 */

struct CryptoPP::Integer *__fastcall CryptoPP::ModExpPrecomputation::ConvertOut(CryptoPP::ModExpPrecomputation *this, struct CryptoPP::Integer *retstr, const struct CryptoPP::Integer *a3)
{
  __int64 v3; // rax@1
  struct CryptoPP::Integer *v5; // [sp+48h] [bp+10h]@1
  const struct CryptoPP::Integer *v6; // [sp+50h] [bp+18h]@1

  v6 = a3;
  v5 = retstr;
  v3 = CryptoPP::member_ptr<CryptoPP::MontgomeryRepresentation>::operator->((__int64)&this->m_mr);
  (*(void (__fastcall **)(__int64, struct CryptoPP::Integer *, const struct CryptoPP::Integer *))(*(_QWORD *)v3 + 208i64))(
    v3,
    v5,
    v6);
  return v5;
}
