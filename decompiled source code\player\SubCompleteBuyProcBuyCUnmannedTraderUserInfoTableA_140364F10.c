/*
 * Function: ?SubCompleteBuyProcBuy@CUnmannedTraderUserInfoTable@@AEAA_NPEAVCPlayer@@PEAVCUnmannedTraderUserInfo@@_JQEBU__list@_qry_case_unmandtrader_buy_update_wait@@PEAU4_unmannedtrader_buy_item_result_zocl@@PEAU4_qry_case_unmandtrader_buy_update_complete@@PEAEPEAKPEAVCUnmannedTraderTradeInfo@@@Z
 * Address: 0x140364F10
 */

char __fastcall CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuy(CUnmannedTraderUserInfoTable *this, CPlayer *pkBuyPlayer, CUnmannedTraderUserInfo *pkBuyUser, __int64 tResultTime, _qry_case_unmandtrader_buy_update_wait::__list *const pkQueryList, _unmannedtrader_buy_item_result_zocl::__list *pSendResultList, _qry_case_unmandtrader_buy_update_complete::__list *pUpdateCompleteList, char *byCompleteUpdateNum, unsigned int *pdwPayDalant, CUnmannedTraderTradeInfo *pkTaradInfo)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CUnmannedTraderTaxRateManager *v13; // rax@20
  CHonorGuild *v14; // rax@20
  int v15; // eax@21
  CUnmannedTraderTaxRateManager *v16; // rax@24
  CHonorGuild *v17; // rax@24
  __int64 v18; // [sp+0h] [bp-E8h]@1
  unsigned int dwRealPrice[2]; // [sp+20h] [bp-C8h]@10
  CLogFile *pkLogger; // [sp+28h] [bp-C0h]@10
  unsigned int dwRegistSerial; // [sp+30h] [bp-B8h]@15
  __int64 tResultTimea; // [sp+38h] [bp-B0h]@15
  CLogFile *v23; // [sp+40h] [bp-A8h]@15
  unsigned int dwPrice; // [sp+48h] [bp-A0h]@17
  unsigned __int64 lnUID; // [sp+50h] [bp-98h]@17
  CLogFile *v26; // [sp+58h] [bp-90h]@17
  unsigned __int16 *wAddItemSerial; // [sp+60h] [bp-88h]@17
  char v28; // [sp+70h] [bp-78h]@6
  unsigned int v29; // [sp+74h] [bp-74h]@6
  CPlayer *pkSellPlayer; // [sp+78h] [bp-70h]@6
  CUnmannedTraderUserInfo *v31; // [sp+80h] [bp-68h]@6
  char v32; // [sp+88h] [bp-60h]@15
  unsigned __int16 v33; // [sp+94h] [bp-54h]@17
  char v34; // [sp+A4h] [bp-44h]@17
  _INVENKEY v35; // [sp+B4h] [bp-34h]@19
  unsigned int v36; // [sp+C4h] [bp-24h]@20
  unsigned int dwTax; // [sp+C8h] [bp-20h]@20
  int v38; // [sp+CCh] [bp-1Ch]@20
  int v39; // [sp+D0h] [bp-18h]@20
  int v40; // [sp+D4h] [bp-14h]@21
  int v41; // [sp+D8h] [bp-10h]@24
  int v42; // [sp+DCh] [bp-Ch]@24
  CUnmannedTraderUserInfoTable *v43; // [sp+F0h] [bp+8h]@1
  CPlayer *pkBuyer; // [sp+F8h] [bp+10h]@1
  CUnmannedTraderUserInfo *v45; // [sp+100h] [bp+18h]@1
  __int64 v46; // [sp+108h] [bp+20h]@1

  v46 = tResultTime;
  v45 = pkBuyUser;
  pkBuyer = pkBuyPlayer;
  v43 = this;
  v10 = &v18;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v10 = -*********;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  pSendResultList->byRet = pkQueryList->byProcRet;
  pSendResultList->dwPrice = pkQueryList->dwPrice;
  pUpdateCompleteList->byProcUpdate = -1;
  pUpdateCompleteList->dwSeller = pkQueryList->dwSeller;
  pUpdateCompleteList->dwRegistSerial = pkQueryList->dwRegistSerial;
  pUpdateCompleteList->byUpdateState = pkQueryList->byOldState;
  if ( pSendResultList->byRet )
    return 0;
  v28 = 1;
  v29 = pkQueryList->dwPrice - pkQueryList->dwTax;
  pkSellPlayer = 0i64;
  v31 = CUnmannedTraderUserInfoTable::Find(v43, pkQueryList->dwSeller);
  if ( !CUnmannedTraderUserInfo::IsNull(v31)
    && *(&g_Player.m_bOper + 50856 * CUnmannedTraderUserInfo::GetIndex(v31))
    && (pkSellPlayer = CUnmannedTraderUserInfo::FindOwner(v31)) != 0i64 )
  {
    pkLogger = v43->m_pkLogger;
    dwRealPrice[0] = v29;
    pSendResultList->byRet = CUnmannedTraderUserInfo::CheckSellComplete(
                               v31,
                               pkSellPlayer,
                               pkBuyer,
                               pkQueryList->dwRegistSerial,
                               v29,
                               pkLogger);
    if ( pSendResultList->byRet )
    {
      CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(
        v43,
        pSendResultList->byRet,
        pUpdateCompleteList,
        byCompleteUpdateNum);
      return 0;
    }
  }
  else
  {
    CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(v43, 81, pUpdateCompleteList, byCompleteUpdateNum);
    v28 = 0;
  }
  pSendResultList->byRet = CUnmannedTraderUserInfo::CheckBuyComplete(v45, pkBuyer, pkQueryList->dwPrice);
  if ( pSendResultList->byRet )
  {
    CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(
      v43,
      pSendResultList->byRet,
      pUpdateCompleteList,
      byCompleteUpdateNum);
    result = 0;
  }
  else if ( v28
         && (v23 = v43->m_pkLogger,
             tResultTimea = v46,
             dwRegistSerial = pkQueryList->dwRegistSerial,
             LODWORD(pkLogger) = pkQueryList->dwTax,
             dwRealPrice[0] = v29,
             v32 = CUnmannedTraderUserInfo::SellComplete(
                     v31,
                     pkSellPlayer,
                     pkBuyer,
                     pkQueryList->dwPrice,
                     v29,
                     (unsigned int)pkLogger,
                     dwRegistSerial,
                     v46,
                     v23),
             CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(
               v43,
               v32,
               pUpdateCompleteList,
               byCompleteUpdateNum),
             v32 != 90) )
  {
    pSendResultList->byRet = v32;
    result = 0;
  }
  else
  {
    v33 = 255;
    v34 = 0;
    wAddItemSerial = &v33;
    v26 = v43->m_pkLogger;
    lnUID = pkQueryList->lnUID;
    dwPrice = pkQueryList->dwPrice;
    LODWORD(v23) = pkQueryList->dwU;
    tResultTimea = pkQueryList->dwD;
    dwRegistSerial = pkQueryList->dwK;
    LODWORD(pkLogger) = pkQueryList->dwRegistSerial;
    *(_QWORD *)dwRealPrice = (char *)pkQueryList + 8;
    v34 = CUnmannedTraderUserInfo::BuyComplete(
            v45,
            pkBuyer,
            pkQueryList->dwSeller,
            pkQueryList->wszName,
            pkQueryList->szAccountID,
            (unsigned int)pkLogger,
            dwRegistSerial,
            tResultTimea,
            (unsigned int)v23,
            dwPrice,
            lnUID,
            v26,
            &v33);
    if ( v34 )
    {
      CUnmannedTraderUserInfoTable::SubCompleteBuyProcBuyResult(v43, v34, pUpdateCompleteList, byCompleteUpdateNum);
      pSendResultList->byRet = v34;
      result = 0;
    }
    else
    {
      _INVENKEY::_INVENKEY(&v35);
      _INVENKEY::LoadDBKey(&v35, pkQueryList->dwK);
      pSendResultList->dwNewItemSerial = v33;
      pSendResultList->byItemTableCode = v35.byTableCode;
      pSendResultList->wItemIndex = v35.wItemIndex;
      pSendResultList->dwDur = pkQueryList->dwD;
      pSendResultList->dwLv = pkQueryList->dwU;
      *pdwPayDalant += v29;
      if ( CHolyStoneSystem::GetHolyMasterRace(&g_HolySys) == -1 )
      {
        v36 = 75 * pkQueryList->dwTax / 0x64;
        dwTax = v36 / 2;
        v38 = CPlayerDB::GetRaceCode(&pkBuyer->m_Param);
        v13 = CUnmannedTraderTaxRateManager::Instance();
        CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(v13, v38, dwTax);
        v39 = CPlayerDB::GetRaceCode(&pkBuyer->m_Param);
        v14 = CHonorGuild::Instance();
        CHonorGuild::SetGuildMaintainMoney(v14, v39, v36, pkQueryList->dwSeller);
      }
      else
      {
        v40 = CPlayerDB::GetRaceCode(&pkBuyer->m_Param);
        v15 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
        if ( v40 == v15 )
        {
          v36 = 75 * pkQueryList->dwTax / 0x64;
          dwTax = v36 / 2;
        }
        else
        {
          v36 = 50 * pkQueryList->dwTax / 0x64;
          dwTax = v36 / 2;
        }
        v41 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
        v16 = CUnmannedTraderTaxRateManager::Instance();
        CUnmannedTraderTaxRateManager::SetPatriarchTaxMoney(v16, v41, dwTax);
        v42 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
        v17 = CHonorGuild::Instance();
        CHonorGuild::SetGuildMaintainMoney(v17, v42, v36, pkQueryList->dwSeller);
      }
      if ( pkTaradInfo )
        CUnmannedTraderTradeInfo::AddIncome(pkTaradInfo, pkQueryList->dwPrice);
      result = 1;
    }
  }
  return result;
}
