/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Assign_n_::_1_::dtor$0
 * Address: 0x140361340
 */

void __fastcall std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::_Assign_n_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CUnmannedTraderRegistItemInfo::~CUnmannedTraderRegistItemInfo((CUnmannedTraderRegistItemInfo *)(a2 + 48));
}
