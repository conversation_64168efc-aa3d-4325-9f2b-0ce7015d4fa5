/*
 * Function: ?CompleteAnsyncConnect@CNetProcess@@QEAAXXZ
 * Address: 0x140479970
 */

void __fastcall CNetProcess::CompleteAnsyncConnect(CNetProcess *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@5
  __int16 *v5; // [sp+38h] [bp-10h]@6
  CNetProcess *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6->m_Type.m_bAnSyncConnect )
  {
    while ( CNetIndexList::PopNode_Front(&v6->m_listAnsyncConnectComplete, &pdwOutIndex) )
    {
      v5 = &v6->m_AnsyncConnectData[pdwOutIndex].Addr.sin_family;
      ((void (__fastcall *)(CNetWorking *, _QWORD, _QWORD, _QWORD))v6->m_pNetwork->vfptr->AnsyncConnectComplete)(
        v6->m_pNetwork,
        v6->m_nIndex,
        pdwOutIndex,
        *((_DWORD *)v5 + 4));
    }
  }
}
