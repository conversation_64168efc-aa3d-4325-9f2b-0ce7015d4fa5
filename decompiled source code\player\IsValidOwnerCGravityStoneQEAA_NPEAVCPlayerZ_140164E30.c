/*
 * Function: ?IsValidOwner@CGravityStone@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140164E30
 */

bool __fastcall CGravityStone::IsValidOwner(CGravityStone *this, CPlayer *pkPlayer)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  CGravityStone *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  return v6->m_pkOwner
      && v6->m_pkOwner == pkPlayer
      && v6->m_pkOwner->m_pUserDB->m_dwSerial == pkPlayer->m_pUserDB->m_dwSerial;
}
