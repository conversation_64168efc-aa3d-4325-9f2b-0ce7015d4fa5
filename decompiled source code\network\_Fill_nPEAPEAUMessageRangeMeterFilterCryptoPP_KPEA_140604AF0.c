/*
 * Function: ??$_Fill_n@PEAPEAUMessageRange@MeterFilter@CryptoPP@@_KPEAU123@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAUMessageRange@MeterFilter@CryptoPP@@_KAEBQEAU123@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140604AF0
 */

int __fastcall std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *,std::random_access_iterator_tag>(__int64 a1, __int64 a2, __int64 a3)
{
  unsigned __int8 v4; // [sp+20h] [bp-18h]@1

  memset(&v4, 0, sizeof(v4));
  return std::_Fill_n<CryptoPP::MeterFilter::MessageRange * *,unsigned __int64,CryptoPP::MeterFilter::MessageRange *>(
           a1,
           a2,
           a3,
           v4);
}
