/*
 * Function: ?Send@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAAXHI@Z
 * Address: 0x1403CE3D0
 */

void __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::Send(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, int n, unsigned int uiMapID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char pbyType; // [sp+34h] [bp-44h]@7
  char v7; // [sp+35h] [bp-43h]@11
  char v8; // [sp+44h] [bp-34h]@10
  char szMsg; // [sp+54h] [bp-24h]@11
  int v10; // [sp+64h] [bp-14h]@8
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v11; // [sp+80h] [bp+8h]@1
  int dwClientIndex; // [sp+88h] [bp+10h]@1
  unsigned int uiMapIDa; // [sp+90h] [bp+18h]@1

  uiMapIDa = uiMapID;
  dwClientIndex = n;
  v11 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v11->m_bInit && v11->m_uiMapCnt > uiMapID )
  {
    pbyType = 27;
    if ( v11->m_pbUpdate[uiMapID] )
      v10 = (unsigned __int8)GUILD_BATTLE::CCurrentGuildBattleInfoManager::GetLeftTime(v11, uiMapID);
    else
      v10 = 1;
    v8 = v10;
    if ( (_BYTE)v10 )
    {
      v7 = 63;
      szMsg = v8;
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &szMsg, 1u);
    }
    else
    {
      v7 = 64;
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, v11->m_pkInfo[uiMapIDa].wszLeftRedName, 0x35u);
    }
  }
}
