/*
 * Function: j_?max_size@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@QEBA_KXZ
 * Address: 0x140010370
 */

unsigned __int64 __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::max_size(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  return std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::max_size(this);
}
