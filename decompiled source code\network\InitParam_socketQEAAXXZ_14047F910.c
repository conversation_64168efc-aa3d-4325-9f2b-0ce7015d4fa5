/*
 * Function: ?InitParam@_socket@@QEAAXXZ
 * Address: 0x14047F910
 */

void __fastcall _socket::InitParam(_socket *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _socket *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_nIndex = -1;
  v4->m_dwTotalRecvMsg = 0;
  v4->m_dwTotalSendMsg = 0;
  v4->m_dwTotalRecvBlock = 0;
  v4->m_dwLastCloseTime = 0;
  v4->m_dwRecvPopMissTime = 0;
  v4->m_dwSendSpeedHackTime = 0;
  v4->m_dwResponSpeedHackTime = 0;
  v4->m_bySpeedHackContCount = 0;
  v4->m_bySpeedHackMissCount = 0;
  v4->m_dwBoundResponTerm = 200000;
  v4->m_bAccept = 0;
  v4->m_bSendable = 0;
  v4->m_bEnterCheck = 0;
  v4->m_Socket = -1i64;
  v4->m_dwPingPongCount = 0;
  v4->m_dwSpeedHackCount = 0;
  strcpy_s(v4->m_szID, 0x14ui64, "null");
  v4->m_hFGContext = 0i64;
}
