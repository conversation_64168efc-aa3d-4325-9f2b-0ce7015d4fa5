/*
 * Function: ?Initialize@PatriarchElectProcessor@@QEAA_NXZ
 * Address: 0x1402B9F10
 */

char __fastcall PatriarchElectProcessor::Initialize(PatriarchElectProcessor *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  __int64 v4; // rax@5
  __int64 v5; // rax@8
  __int64 v6; // rax@11
  __int64 v7; // rax@14
  __int64 v8; // rax@17
  __int64 v9; // rax@20
  ElectProcessor::ProcessorType v10; // eax@24
  __int64 v12; // [sp+0h] [bp-1F8h]@1
  char DstBuf; // [sp+40h] [bp-1B8h]@4
  char v14; // [sp+41h] [bp-1B7h]@4
  int j; // [sp+144h] [bp-B4h]@22
  ElectProcessor *v16; // [sp+150h] [bp-A8h]@7
  CandidateRegister *v17; // [sp+158h] [bp-A0h]@4
  ElectProcessor *v18; // [sp+160h] [bp-98h]@10
  SecondCandidateCrystallizer *v19; // [sp+168h] [bp-90h]@7
  ElectProcessor *v20; // [sp+170h] [bp-88h]@13
  Voter *v21; // [sp+178h] [bp-80h]@10
  ElectProcessor *v22; // [sp+180h] [bp-78h]@16
  FinalDecisionProcessor *v23; // [sp+188h] [bp-70h]@13
  ElectProcessor *v24; // [sp+190h] [bp-68h]@19
  FinalDecisionApplyer *v25; // [sp+198h] [bp-60h]@16
  ElectProcessor *v26; // [sp+1A0h] [bp-58h]@22
  ClassOrderProcessor *v27; // [sp+1A8h] [bp-50h]@19
  __int64 v28; // [sp+1B0h] [bp-48h]@4
  ElectProcessor *v29; // [sp+1B8h] [bp-40h]@5
  ElectProcessor *v30; // [sp+1C0h] [bp-38h]@8
  ElectProcessor *v31; // [sp+1C8h] [bp-30h]@11
  ElectProcessor *v32; // [sp+1D0h] [bp-28h]@14
  ElectProcessor *v33; // [sp+1D8h] [bp-20h]@17
  ElectProcessor *v34; // [sp+1E0h] [bp-18h]@20
  unsigned __int64 v35; // [sp+1E8h] [bp-10h]@4
  PatriarchElectProcessor *v36; // [sp+200h] [bp+8h]@1

  v36 = this;
  v1 = &v12;
  for ( i = 124i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v28 = -2i64;
  v35 = (unsigned __int64)&v12 ^ _security_cookie;
  CreateDirectoryA("..\\ZoneServerLog\\Systemlog\\Patriarch", 0i64);
  clear_file("..\\ZoneServerLog\\Systemlog\\Patriarch", 0x1Eu);
  DstBuf = 0;
  memset(&v14, 0, 0xFFui64);
  v3 = GetKorLocalTime();
  sprintf_s(&DstBuf, 0x100ui64, "..\\ZoneServerLog\\SystemLog\\Patriarch\\PatriarchElect_%d.log", v3);
  CLogFile::SetWriteLogFile(&v36->_kSysLog, &DstBuf, 1, 0, 1, 1);
  CreateDirectoryA("..\\ZoneServerLog\\ServiceLog\\Patriarch", 0i64);
  clear_file("..\\ZoneServerLog\\ServiceLog\\Patriarch", 0x1Eu);
  v17 = (CandidateRegister *)operator new(0x11438ui64, THIS_FILE_14, 76);
  if ( v17 )
  {
    CandidateRegister::CandidateRegister(v17);
    v29 = (ElectProcessor *)v4;
  }
  else
  {
    v29 = 0i64;
  }
  v16 = v29;
  v36->_kProcessor[0] = v29;
  v19 = (SecondCandidateCrystallizer *)operator new(0xC8ui64, THIS_FILE_14, 77);
  if ( v19 )
  {
    SecondCandidateCrystallizer::SecondCandidateCrystallizer(v19);
    v30 = (ElectProcessor *)v5;
  }
  else
  {
    v30 = 0i64;
  }
  v18 = v30;
  v36->_kProcessor[1] = v30;
  v21 = (Voter *)operator new(0x648ui64, THIS_FILE_14, 78);
  if ( v21 )
  {
    Voter::Voter(v21);
    v31 = (ElectProcessor *)v6;
  }
  else
  {
    v31 = 0i64;
  }
  v20 = v31;
  v36->_kProcessor[2] = v31;
  v23 = (FinalDecisionProcessor *)operator new(0x1C8ui64, THIS_FILE_14, 79);
  if ( v23 )
  {
    FinalDecisionProcessor::FinalDecisionProcessor(v23);
    v32 = (ElectProcessor *)v7;
  }
  else
  {
    v32 = 0i64;
  }
  v22 = v32;
  v36->_kProcessor[3] = v32;
  v25 = (FinalDecisionApplyer *)operator new(0xC8ui64, THIS_FILE_14, 80);
  if ( v25 )
  {
    FinalDecisionApplyer::FinalDecisionApplyer(v25);
    v33 = (ElectProcessor *)v8;
  }
  else
  {
    v33 = 0i64;
  }
  v24 = v33;
  v36->_kProcessor[4] = v33;
  v27 = (ClassOrderProcessor *)operator new(0x210ui64, THIS_FILE_14, 81);
  if ( v27 )
  {
    ClassOrderProcessor::ClassOrderProcessor(v27);
    v34 = (ElectProcessor *)v9;
  }
  else
  {
    v34 = 0i64;
  }
  v26 = v34;
  v36->_kProcessor[5] = v34;
  for ( j = 0; j < 6; ++j )
  {
    v10 = ElectProcessor::GetProcessorType(v36->_kProcessor[j]);
    if ( v10 != j )
      return 0;
  }
  v36->_eProcessType = 255;
  v36->_dwNextCheckTime = timeGetTime() + 60000;
  return 1;
}
