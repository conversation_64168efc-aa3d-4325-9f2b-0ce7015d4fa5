/*
 * Function: ?RemovePartyMember@CPartyPlayer@@QEAA_NPEAV1@PEAPEAV1@@Z
 * Address: 0x1400453A0
 */

char __fastcall CPartyPlayer::RemovePartyMember(CPartyPlayer *this, CPartyPlayer *pExiter, CPartyPlayer **ppoutNewBoss)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-68h]@1
  char v7; // [sp+20h] [bp-48h]@8
  int j; // [sp+24h] [bp-44h]@8
  int v9; // [sp+28h] [bp-40h]@15
  CPartyPlayer *v10; // [sp+30h] [bp-38h]@19
  int k; // [sp+38h] [bp-30h]@20
  int l; // [sp+3Ch] [bp-2Ch]@32
  CPlayer *v13; // [sp+40h] [bp-28h]@43
  char v14; // [sp+48h] [bp-20h]@48
  int m; // [sp+4Ch] [bp-1Ch]@51
  CPlayer *v16; // [sp+50h] [bp-18h]@60
  CPartyPlayer *v17; // [sp+70h] [bp+8h]@1
  CPartyPlayer *v18; // [sp+78h] [bp+10h]@1
  CPartyPlayer **v19; // [sp+80h] [bp+18h]@1

  v19 = ppoutNewBoss;
  v18 = pExiter;
  v17 = this;
  v3 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v17) )
  {
    if ( v18->m_pPartyBoss == v17 )
    {
      v7 = 0;
      for ( j = 0; j < 8; ++j )
      {
        if ( v17->m_pPartyMember[j] == v18 )
        {
          v7 = 1;
          break;
        }
      }
      if ( v7 )
      {
        v9 = CPartyPlayer::GetPopPartyMember(v17);
        if ( v9 > 2 )
        {
          v10 = v17;
          if ( v17->m_pLootAuthor == v18 )
          {
            for ( k = 0; k < 8 && v17->m_pPartyMember[k]; ++k )
            {
              if ( v17->m_pLootAuthor == v17->m_pPartyMember[k] )
              {
                if ( k == 7 )
                {
                  v17->m_pLootAuthor = v17->m_pPartyMember[0];
                }
                else if ( v17->m_pPartyMember[k + 1] )
                {
                  v17->m_pLootAuthor = v17->m_pPartyMember[k + 1];
                }
                else
                {
                  v17->m_pLootAuthor = v17->m_pPartyMember[0];
                }
                break;
              }
            }
          }
          if ( v17 == v18 )
          {
            v10 = v17->m_pPartyMember[1];
            CPartyPlayer::PartyListInit(v10);
            v10->m_pPartyBoss = v10;
            for ( l = 1; l < 8; ++l )
            {
              if ( v17->m_pPartyMember[l] )
              {
                v10->m_pPartyMember[l - 1] = v17->m_pPartyMember[l];
                v10->m_pPartyMember[l - 1]->m_pPartyBoss = v10;
              }
            }
            for ( l = v9 - 1; l < 8; ++l )
              v10->m_pPartyMember[l] = 0i64;
            for ( l = 0; l < 8 && v10->m_pPartyMember[l]; ++l )
            {
              v13 = &g_Player + v10->m_pPartyMember[l]->m_wZoneIndex;
              if ( v13->m_bOper && CPlayer::GetGroupTarget(v13, 0)->pObject )
                CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v13, 0);
            }
          }
          else
          {
            v14 = 0;
            for ( j = 1; j < 8; ++j )
            {
              if ( v17->m_pPartyMember[j] == v18 )
              {
                v14 = 1;
                for ( m = j + 1; m < 8; ++m )
                  v17->m_pPartyMember[m - 1] = v17->m_pPartyMember[m];
                break;
              }
              if ( !v17->m_pPartyMember[j] )
                break;
            }
            for ( j = v9 - 1; j < 8; ++j )
              v17->m_pPartyMember[j] = 0i64;
            v16 = &g_Player + v18->m_wZoneIndex;
            if ( v16->m_bOper && CPlayer::GetGroupTarget(v16, 0)->pObject )
              CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v16, 0);
          }
          CPartyPlayer::PartyListInit(v18);
          if ( v19 )
            *v19 = v10;
          result = 1;
        }
        else
        {
          CPartyPlayer::DisjointParty(v17);
          if ( v19 )
            *v19 = 0i64;
          result = 1;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
