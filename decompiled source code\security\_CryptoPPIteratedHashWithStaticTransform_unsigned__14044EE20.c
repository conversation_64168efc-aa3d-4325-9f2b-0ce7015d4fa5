/*
 * Function: _CryptoPP::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0_::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0__::_1_::dtor$0
 * Address: 0x14044EE20
 */

void __fastcall CryptoPP::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0_::IteratedHashWithStaticTransform_unsigned_int_CryptoPP::EnumToType_enum_CryptoPP::ByteOrder_1__64_32_CryptoPP::SHA256_0__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256>>::~ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum  CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256>>(*(CryptoPP::ClonableImpl<CryptoPP::SHA256,CryptoPP::AlgorithmImpl<CryptoPP::IteratedHash<unsigned int,CryptoPP::EnumToType<enum CryptoPP::ByteOrder,1>,64,CryptoPP::HashTransformation>,CryptoPP::SHA256> > **)(a2 + 64));
}
