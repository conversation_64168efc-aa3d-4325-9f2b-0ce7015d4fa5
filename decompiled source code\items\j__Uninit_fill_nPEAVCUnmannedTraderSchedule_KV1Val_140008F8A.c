/*
 * Function: j_??$_Uninit_fill_n@PEAVCUnmannedTraderSchedule@@_KV1@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@YAXPEAVCUnmannedTraderSchedule@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderSchedule@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140008F8A
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderSchedule *,unsigned __int64,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(CUnmannedTraderSchedule *_First, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val, std::allocator<CUnmannedTraderSchedule> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderSchedule *,unsigned __int64,CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
