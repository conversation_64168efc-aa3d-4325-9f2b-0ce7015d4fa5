/*
 * Function: ?MoveMap@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXIPEAVCNormalGuildBattleField@2@@Z
 * Address: 0x1403E0F60
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::MoveMap(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int uiID, GUILD_BATTLE::CNormalGuildBattleField *pkField)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@12
  CPlayer *v6; // rax@13
  __int64 v7; // [sp+0h] [bp-78h]@1
  char pbyType; // [sp+34h] [bp-44h]@7
  char v9; // [sp+35h] [bp-43h]@7
  char szMsg; // [sp+54h] [bp-24h]@7
  int j; // [sp+64h] [bp-14h]@7
  GUILD_BATTLE::CNormalGuildBattleGuild *v12; // [sp+80h] [bp+8h]@1
  GUILD_BATTLE::CNormalGuildBattleField *v13; // [sp+90h] [bp+18h]@1

  v13 = pkField;
  v12 = this;
  v3 = &v7;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v12->m_pkGuild && pkField )
  {
    v12->m_pkGuild->m_bInGuildBattle = 1;
    pbyType = 27;
    v9 = 66;
    szMsg = -1;
    for ( j = 0; j < 50; ++j )
    {
      if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsExist(&v12->m_kMember[j]) )
      {
        if ( GUILD_BATTLE::CNormalGuildBattleGuildMember::IsEnableStart(&v12->m_kMember[j]) )
        {
          GUILD_BATTLE::CNormalGuildBattleGuildMember::StockOldInfo(&v12->m_kMember[j]);
          GUILD_BATTLE::CNormalGuildBattleGuildMember::SetBattleState(&v12->m_kMember[j], 1, v12->m_byColorInx);
          v6 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v12->m_kMember[j]);
          GUILD_BATTLE::CNormalGuildBattleField::Start(v13, v12->m_byColorInx, v6);
        }
        else
        {
          v5 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetIndex(&v12->m_kMember[j]);
          CNetProcess::LoadSendMsg(unk_1414F2088, v5, &pbyType, &szMsg, 1u);
        }
      }
    }
  }
}
