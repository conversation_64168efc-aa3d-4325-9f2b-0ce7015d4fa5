/*
 * Function: ?ct_alter_pvp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140290F60
 */

bool __fastcall ct_alter_pvp(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  long double v4; // xmm0_8@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v4 = (double)atoi(s_pwszDstCheat[0]);
      result = CPlayer::dev_up_pvp(v6, v4);
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
