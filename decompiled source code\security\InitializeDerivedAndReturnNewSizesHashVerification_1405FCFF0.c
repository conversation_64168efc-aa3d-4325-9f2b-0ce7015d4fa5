/*
 * Function: ?InitializeDerivedAndReturnNewSizes@HashVerificationFilter@CryptoPP@@MEAAXAEBVNameValuePairs@2@AEA_K11@Z
 * Address: 0x1405FCFF0
 */

void __fastcall CryptoPP::HashVerificationFilter::InitializeDerivedAndReturnNewSizes(CryptoPP::HashVerificationFilter *this, const struct CryptoPP::NameValuePairs *a2, unsigned __int64 *a3, unsigned __int64 *a4, unsigned __int64 *a5)
{
  const char *v5; // rax@1
  unsigned __int64 v6; // [sp+20h] [bp-28h]@1
  unsigned __int64 v7; // [sp+28h] [bp-20h]@2
  unsigned __int64 v8; // [sp+30h] [bp-18h]@5
  CryptoPP::HashVerificationFilter *v9; // [sp+50h] [bp+8h]@1
  const struct CryptoPP::NameValuePairs *v10; // [sp+58h] [bp+10h]@1
  unsigned __int64 *v11; // [sp+60h] [bp+18h]@1
  unsigned __int64 *v12; // [sp+68h] [bp+20h]@1

  v12 = a4;
  v11 = a3;
  v10 = a2;
  v9 = this;
  v5 = CryptoPP::Name::HashVerificationFilterFlags((CryptoPP::Name *)this);
  v9->m_flags = CryptoPP::NameValuePairs::GetValueWithDefault<unsigned int>(v10, v5, 9i64);
  ((void (__fastcall *)(_QWORD))v9->m_hashModule->vfptr[3].__vecDelDtor)(v9->m_hashModule);
  v6 = (unsigned int)((int (__fastcall *)(_QWORD))v9->m_hashModule->vfptr[3].Clone)(v9->m_hashModule);
  v9->m_verified = 0;
  if ( v9->m_flags & 1 )
    v7 = v6;
  else
    v7 = 0i64;
  *v11 = v7;
  *v12 = 1i64;
  if ( v9->m_flags & 1 )
    v8 = 0i64;
  else
    v8 = v6;
  *a5 = v8;
}
