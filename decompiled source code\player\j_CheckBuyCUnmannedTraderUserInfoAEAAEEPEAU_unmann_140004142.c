/*
 * Function: j_?CheckBuy@CUnmannedTraderUserInfo@@AEAAEEPEAU_unmannedtrader_buy_item_request_clzo@@AEAPEAVCPlayer@@PEAVCLogFile@@@Z
 * Address: 0x140004142
 */

char __fastcall CUnmannedTraderUserInfo::CheckBuy(CUnmannedTraderUserInfo *this, char byType, _unmannedtrader_buy_item_request_clzo *pRequest, CPlayer **pkBuyer, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::CheckBuy(this, byType, pRequest, pkBuyer, pkLogger);
}
