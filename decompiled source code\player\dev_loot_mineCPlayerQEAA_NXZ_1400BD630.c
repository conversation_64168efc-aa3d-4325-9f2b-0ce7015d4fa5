/*
 * Function: ?dev_loot_mine@CPlayer@@QEAA_NXZ
 * Address: 0x1400BD630
 */

char __fastcall CPlayer::dev_loot_mine(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@6
  int v4; // eax@9
  int v5; // eax@14
  int v6; // eax@17
  __int64 v8; // [sp+0h] [bp-68h]@1
  int n; // [sp+30h] [bp-38h]@4
  _base_fld *v10; // [sp+38h] [bp-30h]@7
  char *v11; // [sp+40h] [bp-28h]@9
  _base_fld *v12; // [sp+48h] [bp-20h]@15
  char *v13; // [sp+50h] [bp-18h]@16
  CPlayer *pOwner; // [sp+70h] [bp+8h]@1

  pOwner = this;
  v1 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( n = 0; ; ++n )
  {
    v3 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 6);
    if ( n >= v3 )
      break;
    v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, n);
    if ( v10[1].m_dwIndex && *(_DWORD *)&v10[6].m_strCode[8] == 10 )
    {
      v11 = GetItemEquipCivil(6, n);
      v4 = CPlayerDB::GetRaceSexCode(&pOwner->m_Param);
      if ( v11[v4] == 49 )
        loot_item(pOwner, v10->m_strCode, 1, 0i64, 0);
    }
  }
  for ( n = 0; ; ++n )
  {
    v5 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 16);
    if ( n >= v5 )
      break;
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 16, n);
    if ( v12[1].m_dwIndex )
    {
      v13 = GetItemEquipCivil(16, n);
      if ( v13 )
      {
        v6 = CPlayerDB::GetRaceSexCode(&pOwner->m_Param);
        if ( v13[v6] == 49 )
          loot_item(pOwner, v12->m_strCode, 1, 0i64, 0);
      }
    }
  }
  return 1;
}
