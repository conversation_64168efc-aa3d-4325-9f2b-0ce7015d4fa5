/*
 * Function: j_??RCTopGoalPrediCate@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NAEBVCNormalGuildBattleGuildMember@2@0@Z
 * Address: 0x140001104
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate::operator()(GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate *this, GUILD_BATTLE::CNormalGuildBattleGuildMember *lhs, GUILD_BATTLE::CNormalGuildBattleGuildMember *rhs)
{
  return GUILD_BATTLE::CNormalGuildBattleGuild::CTopGoalPrediCate::operator()(this, lhs, rhs);
}
