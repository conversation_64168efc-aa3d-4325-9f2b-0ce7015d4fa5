/*
 * Function: j_?Add@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAEIKKPEAPEAVCGuildBattleSchedule@2@AEAI@Z
 * Address: 0x14000774D
 */

char __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Add(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiFieldInx, unsigned int dwStartTimeInx, unsigned int dwElapseTimeCnt, GUILD_BATTLE::CGuildBattleSchedule **ppkSchedule, unsigned int *uiSLID)
{
  return GUILD_BATTLE::CGuildBattleScheduleManager::Add(
           this,
           uiFieldInx,
           dwStartTimeInx,
           dwElapseTimeCnt,
           ppkSchedule,
           uiSLID);
}
