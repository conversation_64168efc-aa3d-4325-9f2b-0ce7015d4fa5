/*
 * Function: ?UpdateVisualVer@CPlayer@@QEAAXTCashChangeStateFlag@1@@Z
 * Address: 0x140053970
 */

void __fastcall CPlayer::UpdateVisualVer(CPlayer *this, CPlayer::CashChangeStateFlag byChangeFlagMask)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ++v5->m_wVisualVer;
  if ( byChangeFlagMask.m_byStateFlag & 7 )
    v5->m_CashChangeStateFlag.0 = ($621D0DDFB6A4DE55506A65C7CCDC95CE)(((byChangeFlagMask.m_byStateFlag & 7)
                                                                     + (*(_DWORD *)&v5->m_CashChangeStateFlag.0 & 7)) & 7 | *(_DWORD *)&v5->m_CashChangeStateFlag.0 & 0xFFFFFFF8);
  CPlayer::SetShapeAllBuffer(v5);
}
