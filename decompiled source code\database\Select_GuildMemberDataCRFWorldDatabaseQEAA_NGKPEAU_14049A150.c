/*
 * Function: ?Select_GuildMemberData@CRFWorldDatabase@@QEAA_NGKPEAU_worlddb_guild_member_info@@@Z
 * Address: 0x14049A150
 */

char __fastcall CRFWorldDatabase::Select_GuildMemberData(CRFWorldDatabase *this, unsigned __int16 wMaxMember, unsigned int dwGuildSerial, _worlddb_guild_member_info *pGuildMemberInfo)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v7; // [sp+0h] [bp-1A8h]@1
  void *SQLStmt; // [sp+20h] [bp-188h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-180h]@16
  SQLLEN v10; // [sp+38h] [bp-170h]@16
  __int16 v11; // [sp+44h] [bp-164h]@9
  char Dest; // [sp+60h] [bp-148h]@4
  int v13; // [sp+164h] [bp-44h]@4
  double TargetValue; // [sp+178h] [bp-30h]@14
  unsigned __int64 v15; // [sp+190h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+1B0h] [bp+8h]@1
  _worlddb_guild_member_info *v17; // [sp+1C8h] [bp+20h]@1

  v17 = pGuildMemberInfo;
  v16 = this;
  v4 = &v7;
  for ( i = 104i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = (unsigned __int64)&v7 ^ _security_cookie;
  v13 = 0;
  sprintf(
    &Dest,
    "select top %u g.serial, g.pvppoint, g.guildstatus, g.guildrank, b.lv, b.name from tbl_general as g join tbl_base as "
    "b on g.serial = b.serial where g.guildserial = %d and b.dck=0 order by guildstatus ",
    wMaxMember,
    dwGuildSerial);
  if ( v16->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v16->vfptr, &Dest);
  if ( v16->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v16->vfptr) )
  {
    v11 = SQLExecDirect_0(v16->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v16->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v16->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v16->vfptr, v11, v16->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      while ( 1 )
      {
        TargetValue = 0.0;
        v11 = SQLFetch_0(v16->m_hStmtSelect);
        if ( v11 )
        {
          if ( v11 != 1 )
            break;
        }
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 1u, 4, &v17->MemberData[v13], 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 2u, 8, &TargetValue, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 3u, -6, &v17->MemberData[v13].byClassInGuild, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 4u, 5, &v17->MemberData[v13].wRank, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 5u, -6, &v17->MemberData[v13].byLv, 0i64, &v10);
        StrLen_or_IndPtr = &v10;
        SQLStmt = (void *)17;
        v11 = SQLGetData_0(v16->m_hStmtSelect, 6u, 1, v17->MemberData[v13].wszName, 17i64, &v10);
        v17->MemberData[v13++].dwPvpPoint = (signed int)floor(TargetValue);
      }
      v17->wMemberCount = v13;
      if ( v16->m_hStmtSelect )
        SQLCloseCursor_0(v16->m_hStmtSelect);
      if ( v16->m_bSaveDBLog )
        CRFNewDatabase::FmtLog((CRFNewDatabase *)&v16->vfptr, "%s Success", &Dest);
      result = 1;
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v16->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
