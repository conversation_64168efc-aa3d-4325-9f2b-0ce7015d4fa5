/*
 * Function: ?GetPath<PERSON>romDepth@CLevel@@QEAAKQEAM0HQEAY02MPEAK@Z
 * Address: 0x1404E21B0
 */

unsigned __int32 __usercall CLevel::GetPath<PERSON>romDepth@<eax>(CLevel *this@<rcx>, float *const a2@<rdx>, float *const a3@<r8>, int a4@<r9d>, __m128d a5@<xmm1>, float (*const a6)[3], unsigned __int32 *a7)
{
  __m128d v7; // xmm1@1
  float v8; // xmm0_4@1
  float v9; // xmm2_4@2
  unsigned __int32 result; // eax@3

  a5.m128d_f64[0] = (float)(*a2 - *a3);
  v7 = _mm_and_pd(a5, (__m128d)(unsigned __int64)_mask__AbsDouble_);
  v8 = v7.m128d_f64[0];
  if ( v8 >= 0.1
    || (v7.m128d_f64[0] = (float)(a2[2] - a3[2]),
        v9 = COERCE_DOUBLE(_mm_and_pd(v7, (__m128d)(unsigned __int64)_mask__AbsDouble_)),
        v9 >= 0.1) )
  {
    result = CBsp::GetPathFind(this->mBsp, a2, a3, a6, a7, a4);
  }
  else
  {
    (*a6)[0] = *a2;
    (*a6)[1] = a2[1];
    (*a6)[2] = a2[2];
    *a7 = 0;
    result = 0;
  }
  return result;
}
