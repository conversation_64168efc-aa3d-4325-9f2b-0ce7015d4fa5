/*
 * Function: ?Set@CLogTypeDBTask@@QEAA_NEPEADG@Z
 * Address: 0x1402C1E10
 */

char __fastcall CLogTypeDBTask::Set(CLogTypeDBTask *this, char byQueryType, char *pcData, unsigned __int16 wSize)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  __int64 v7; // [sp+0h] [bp-28h]@1
  CLogTypeDBTask *v8; // [sp+30h] [bp+8h]@1
  char v9; // [sp+38h] [bp+10h]@1

  v9 = byQueryType;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)byQueryType < 3 && pcData && (signed int)wSize <= 1024 )
  {
    memcpy_0(v8->m_pcData, pcData, wSize);
    v8->m_eQueryType = (unsigned __int8)v9;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
