/*
 * Function: ?CheckPosInTown@CPlayer@@QEAAXXZ
 * Address: 0x1400648F0
 */

void __fastcall CPlayer::CheckPosInTown(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  float v3; // xmm0_4@5
  float v4; // xmm0_4@6
  char v5; // al@7
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int8 v7; // [sp+20h] [bp-18h]@7
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_pBeforeTownCheckMap != v8->m_pCurMap
    || (v3 = v8->m_fCurPos[0] - v8->m_fBeforeTownCheckPos[0], abs(v3), v3 > 50.0)
    || (v4 = v8->m_fCurPos[2] - v8->m_fBeforeTownCheckPos[1], abs(v4), v4 > 50.0) )
  {
    v5 = CPlayerDB::GetRaceCode(&v8->m_Param);
    v7 = CMapData::GetRaceTown(v8->m_pCurMap, v8->m_fCurPos, v5);
    if ( v8->m_byPosRaceTown != v7 || !v8->m_pBeforeTownCheckMap )
    {
      v8->m_byPosRaceTown = v7;
      CPlayer::SendMsg_AlterTownOrField(v8);
    }
    v8->m_pBeforeTownCheckMap = v8->m_pCurMap;
    v8->m_fBeforeTownCheckPos[0] = v8->m_fCurPos[0];
    v8->m_fBeforeTownCheckPos[1] = v8->m_fCurPos[2];
  }
}
