/*
 * Function: ?pc_UnitPartTuningRequest@CPlayer@@QEAAXEEPEAU_tuning_data@@H@Z
 * Address: 0x140103740
 */

void __usercall CPlayer::pc_UnitPartTuningRequest(CPlayer *this@<rcx>, char by<PERSON>lotIndex@<dl>, char byTuning<PERSON><PERSON>@<r8b>, _tuning_data *pTuningData@<r9>, float a5@<xmm0>, int bUseNPCLinkIntem)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // xmm0_4@4
  int v9; // eax@30
  int v10; // eax@33
  char v11; // al@41
  unsigned int v12; // eax@55
  unsigned int v13; // eax@75
  CMoneySupplyMgr *v14; // rax@82
  CMoneySupplyMgr *v15; // rax@84
  __int64 v16; // [sp+0h] [bp-158h]@1
  char v17; // [sp+40h] [bp-118h]@4
  _UNIT_DB_BASE::_LIST *pData; // [sp+48h] [bp-110h]@4
  unsigned __int8 v19; // [sp+50h] [bp-108h]@4
  unsigned int dwSub; // [sp+68h] [bp-F0h]@4
  unsigned int v21; // [sp+6Ch] [bp-ECh]@4
  unsigned int v22; // [sp+94h] [bp-C4h]@4
  unsigned int v23; // [sp+98h] [bp-C0h]@4
  int v24; // [sp+9Ch] [bp-BCh]@4
  float v25; // [sp+A0h] [bp-B8h]@4
  int j; // [sp+A4h] [bp-B4h]@22
  _base_fld *v27; // [sp+A8h] [bp-B0h]@16
  _base_fld *v28; // [sp+B0h] [bp-A8h]@24
  char *v29; // [sp+B8h] [bp-A0h]@35
  int k; // [sp+C0h] [bp-98h]@35
  int nEquipMasteryCode; // [sp+C4h] [bp-94h]@38
  unsigned __int64 v32; // [sp+C8h] [bp-90h]@44
  int l; // [sp+D0h] [bp-88h]@45
  unsigned int *v34; // [sp+D8h] [bp-80h]@49
  _base_fld *v35; // [sp+E0h] [bp-78h]@49
  unsigned __int64 v36; // [sp+E8h] [bp-70h]@50
  int v37; // [sp+F0h] [bp-68h]@66
  unsigned int *v38; // [sp+F8h] [bp-60h]@67
  _base_fld *v39; // [sp+100h] [bp-58h]@70
  _base_fld *v40; // [sp+108h] [bp-50h]@71
  int v41; // [sp+110h] [bp-48h]@77
  __int64 v42; // [sp+118h] [bp-40h]@44
  __int64 v43; // [sp+120h] [bp-38h]@50
  __int64 v44; // [sp+128h] [bp-30h]@55
  char *v45; // [sp+130h] [bp-28h]@75
  unsigned int v46; // [sp+138h] [bp-20h]@75
  unsigned int nAmount; // [sp+13Ch] [bp-1Ch]@82
  int nLv; // [sp+140h] [bp-18h]@82
  int v49; // [sp+144h] [bp-14h]@84
  CPlayer *p; // [sp+160h] [bp+8h]@1
  char v51; // [sp+168h] [bp+10h]@1
  char v52; // [sp+170h] [bp+18h]@1
  _tuning_data *v53; // [sp+178h] [bp+20h]@1

  v53 = pTuningData;
  v52 = byTuningNum;
  v51 = bySlotIndex;
  p = this;
  v6 = &v16;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v17 = 0;
  pData = &p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex];
  v19 = p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byFrame;
  dwSub = 0;
  memset(&v21, 0, 0x18ui64);
  v22 = eGetTexRate(0) + 10000;
  v23 = 10000 - eGetTexRate(0);
  eGetTex(0);
  *(float *)&v8 = a5 + 1.0;
  v24 = v8;
  eGetTex(0);
  v25 = 1.0 - *(float *)&v8;
  if ( p->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      v17 = 34;
    }
    else if ( bUseNPCLinkIntem || IsBeNearStore(p, 4) )
    {
      if ( CPlayerDB::GetRaceCode(&p->m_Param) )
      {
        v17 = 1;
      }
      else if ( p->m_pUsingUnit )
      {
        v17 = 2;
      }
      else if ( v19 == 255 )
      {
        v17 = 5;
      }
      else
      {
        v27 = CRecordData::GetRecord(&stru_1799C8BA0, v19);
        if ( v27 )
        {
          if ( *(_DWORD *)&v27[1].m_strCode[60] )
          {
            if ( pData->dwGauge )
            {
              for ( j = 0; j < (unsigned __int8)v52; ++j )
              {
                v28 = CRecordData::GetRecord(&stru_1799C86D0 + v53[j].byPartCode, v53[j].byPartIndex);
                if ( !v28 )
                {
                  v17 = 8;
                  goto LABEL_58;
                }
                if ( *((_BYTE *)&v28[3].m_dwIndex + v19) == 48 )
                {
                  v17 = 6;
                  goto LABEL_58;
                }
                if ( pData->byPart[v53[j].byPartCode] == v53[j].byPartIndex )
                {
                  v17 = 29;
                  goto LABEL_58;
                }
                v9 = CPlayerDB::GetLevel(&p->m_Param);
                if ( *(_DWORD *)&v28[4].m_strCode[12] > v9 )
                {
                  v17 = 12;
                  goto LABEL_58;
                }
                if ( *(_DWORD *)&v28[4].m_strCode[16] != -1 )
                {
                  v10 = CPlayerDB::GetLevel(&p->m_Param);
                  if ( *(_DWORD *)&v28[4].m_strCode[16] < v10 )
                  {
                    v17 = 12;
                    goto LABEL_58;
                  }
                }
                v29 = &v28[4].m_strCode[20];
                for ( k = 0; k < 2; ++k )
                {
                  nEquipMasteryCode = *(_DWORD *)&v29[8 * k];
                  if ( nEquipMasteryCode != -1 )
                  {
                    if ( nEquipMasteryCode >= 6 )
                    {
                      v17 = 13;
                      goto LABEL_58;
                    }
                    v11 = _MASTERY_PARAM::GetEquipMastery(&p->m_pmMst, nEquipMasteryCode);
                    if ( (signed int)(unsigned __int8)v11 < *(_DWORD *)&v29[8 * k + 4] )
                    {
                      v17 = 13;
                      goto LABEL_58;
                    }
                  }
                }
                v32 = *(_DWORD *)&v28[5].m_strCode[40];
                v32 *= v22;
                v42 = (signed int)*(&dwSub + *(_DWORD *)&v28[5].m_strCode[36]);
                *(&dwSub + *(_DWORD *)&v28[5].m_strCode[36]) = v32 / 0x2710 + v42;
                if ( v53[j].byPartCode == 5 )
                {
                  for ( l = 0; l < 8; ++l )
                  {
                    if ( pData->dwSpare[l] != -1 )
                    {
                      v34 = &pData->dwSpare[l];
                      v35 = CRecordData::GetRecord(&stru_1799C8AF0, *(_WORD *)v34);
                      if ( v35 )
                      {
                        v36 = *(_DWORD *)&v35[3].m_strCode[8] / 2;
                        v36 = v23 * v36 + 5000;
                        v43 = (signed int)*(&dwSub + *(_DWORD *)&v35[3].m_strCode[4]);
                        *(&dwSub + *(_DWORD *)&v35[3].m_strCode[4]) = v43 - v36 / 0x2710;
                      }
                    }
                  }
                }
              }
              for ( j = 0; j < 7; ++j )
              {
                v44 = j;
                v12 = CPlayer::GetMoney(p, j);
                if ( *(&dwSub + v44) > v12 )
                {
                  v17 = 7;
                  break;
                }
              }
            }
            else
            {
              v17 = 36;
            }
          }
          else
          {
            v17 = 38;
          }
        }
        else
        {
          v17 = 5;
        }
      }
    }
    else
    {
      v17 = 21;
    }
LABEL_58:
    if ( !v17 )
    {
      for ( j = 0; j < (unsigned __int8)v52; ++j )
      {
        pData->byPart[v53[j].byPartCode] = v53[j].byPartIndex;
        if ( v53[j].byPartCode == 5 )
          memset_0(pData->dwSpare, -1, 0x20ui64);
        if ( v53[j].byPartCode == 3 || v53[j].byPartCode == 4 )
        {
          v37 = gnBulletPerPart[(unsigned __int64)v53[j].byPartCode];
          if ( v37 != -1 )
          {
            v38 = &pData->dwBullet[v37];
            if ( HIWORD(pData->dwBullet[v37]) )
            {
              if ( *((_WORD *)v38 + 1) != 0xFFFF )
              {
                v39 = CRecordData::GetRecord(&stru_1799C8AF0, *(_WORD *)v38);
                if ( v39 )
                {
                  v40 = CRecordData::GetRecord(&stru_1799C86D0 + v53[j].byPartCode, v53[j].byPartIndex);
                  if ( v40 )
                  {
                    if ( *(_DWORD *)&v39[2].m_strCode[60] != *(_DWORD *)&v40[3].m_strCode[60] )
                      pData->dwBullet[v37] = -1;
                  }
                }
              }
            }
          }
        }
      }
      CPlayer::SubDalant(p, dwSub);
      CPlayer::SubGold(p, v21);
      CUserDB::Update_UnitData(p->m_pUserDB, v51, pData);
      v45 = p->m_szItemHistoryFileName;
      v46 = CPlayerDB::GetGold(&p->m_Param);
      v13 = CPlayerDB::GetDalant(&p->m_Param);
      CMgrAvatorItemHistory::tuning_unit(
        &CPlayer::s_MgrItemHistory,
        p->m_ObjID.m_wIndex,
        v51,
        pData,
        (int *)&dwSub,
        v13,
        v46,
        v45);
      if ( !p->m_byUserDgr )
      {
        eAddGold(0, v21);
        eAddDalant(0, dwSub);
      }
      v41 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v41 == 30 || v41 == 40 || v41 == 50 || v41 == 60 )
      {
        if ( (signed int)v21 > 0 )
        {
          nAmount = 2000 * v21;
          nLv = CPlayerDB::GetLevel(&p->m_Param);
          v14 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v14, nLv, nAmount);
        }
        if ( (signed int)dwSub > 0 )
        {
          v49 = CPlayerDB::GetLevel(&p->m_Param);
          v15 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v15, v49, dwSub);
        }
      }
    }
    CPlayer::SendMsg_UnitPartTuningResult(p, v17, v51, (int *)&dwSub);
  }
}
