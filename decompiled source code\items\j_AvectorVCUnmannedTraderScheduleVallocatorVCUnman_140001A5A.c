/*
 * Function: j_??A?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAAAEAVCUnmannedTraderSchedule@@_K@Z
 * Address: 0x140001A5A
 */

CUnmannedTraderSchedule *__fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned __int64 _Pos)
{
  return std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](this, _Pos);
}
