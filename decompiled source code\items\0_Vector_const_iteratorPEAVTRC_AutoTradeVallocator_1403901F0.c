/*
 * Function: ??0?$_Vector_const_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x1403901F0
 */

void __fastcall std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this, std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *v5; // [sp+30h] [bp+8h]@1
  std::_Vector_const_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(
    (std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *)&v5->_Mycont,
    (std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *)&__that->_Mycont);
  v5->_Myptr = __thata->_Myptr;
}
