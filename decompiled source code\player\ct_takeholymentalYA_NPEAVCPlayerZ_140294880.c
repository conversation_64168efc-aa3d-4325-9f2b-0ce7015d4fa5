/*
 * Function: ?ct_takeholymental@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294880
 */

char __fastcall ct_takeholymental(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  const char *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  CPlayer *v6; // [sp+30h] [bp+8h]@1

  v6 = pOne;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v6 && v6->m_bOper )
  {
    v4 = CHolyStoneSystem::GetHolyMentalString(&g_HolySys);
    CPlayer::CheckMentalTakeAndUpdateLastMetalTicket(v6, v4);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
