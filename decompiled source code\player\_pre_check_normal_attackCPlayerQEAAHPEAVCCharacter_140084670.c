/*
 * Function: ?_pre_check_normal_attack@CPlayer@@QEAAHPEAVCCharacter@@G_NPEAPEAU_db_con@_STORAGE_LIST@@PEAPEAU_BulletItem_fld@@G23@Z
 * Address: 0x140084670
 */

signed __int64 __fastcall CPlayer::_pre_check_normal_attack(CPlayer *this, CCharacter *pDst, unsigned __int16 wBulletSerial, bool bCount, _STORAGE_LIST::_db_con **ppBulletProp, _BulletItem_fld **ppfldBullet, unsigned __int16 wEffBtSerial, _STORAGE_LIST::_db_con **ppEffBtProp, _BulletItem_fld **ppfldEffBt)
{
  __int64 *v9; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@7
  __int64 v12; // rdx@30
  float v13; // xmm0_4@74
  float v14; // xmm0_4@74
  int v15; // xmm0_4@74
  float v16; // xmm0_4@75
  float v17; // xmm0_4@75
  __int64 v18; // [sp+0h] [bp-78h]@1
  _STORAGE_LIST::_db_con *v19; // [sp+20h] [bp-58h]@4
  _BulletItem_fld *v20; // [sp+28h] [bp-50h]@4
  _STORAGE_LIST::_db_con *v21; // [sp+30h] [bp-48h]@4
  _BulletItem_fld *v22; // [sp+38h] [bp-40h]@4
  int v23; // [sp+40h] [bp-38h]@23
  char v24; // [sp+44h] [bp-34h]@36
  int j; // [sp+48h] [bp-30h]@36
  __int64 v26; // [sp+50h] [bp-28h]@38
  float v27; // [sp+58h] [bp-20h]@73
  int v28; // [sp+5Ch] [bp-1Ch]@76
  float v29; // [sp+60h] [bp-18h]@74
  float v30; // [sp+64h] [bp-14h]@74
  float v31; // [sp+68h] [bp-10h]@75
  float v32; // [sp+6Ch] [bp-Ch]@75
  CPlayer *v33; // [sp+80h] [bp+8h]@1
  CCharacter *v34; // [sp+88h] [bp+10h]@1
  unsigned __int16 v35; // [sp+90h] [bp+18h]@1

  v35 = wBulletSerial;
  v34 = pDst;
  v33 = this;
  v9 = &v18;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v9 = -858993460;
    v9 = (__int64 *)((char *)v9 + 4);
  }
  v19 = 0i64;
  v20 = 0i64;
  v21 = 0i64;
  v22 = 0i64;
  if ( !bCount && !v33->m_bSFDelayNotCheck && !_ATTACK_DELAY_CHECKER::IsDelay(&v33->m_AttDelayChker, -1, 0xFFu, -1) )
    return 4294967291i64;
  if ( !v34 )
    return 4294967290i64;
  if ( CPlayerDB::GetRaceCode(&v33->m_Param) == 2 && CPlayer::IsActingSiegeMode(v33) )
    return 4294967236i64;
  if ( v34 )
  {
    if ( !v34->m_bLive
      || v34->m_bCorpse
      || v34->m_pCurMap != v33->m_pCurMap
      || CGameObject::GetCurSecNum((CGameObject *)&v34->vfptr) == -1 )
    {
      return 4294967290i64;
    }
    if ( (CPlayer *)v34 == v33 )
      return 4294967290i64;
    if ( v34 == (CCharacter *)v33->m_pRecalledAnimusChar )
      return 4294967254i64;
    v23 = CPlayer::_pre_check_in_guild_battle(v33, v34);
    if ( v23 )
      return (unsigned int)v23;
    if ( !(unsigned __int8)((int (__fastcall *)(CPlayer *))v33->vfptr->IsAttackableInTown)(v33)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))v34->vfptr->IsAttackableInTown)(v34)
      && ((unsigned __int8)((int (__fastcall *)(CPlayer *))v33->vfptr->IsInTown)(v33)
       || (unsigned __int8)((int (__fastcall *)(CCharacter *))v34->vfptr->IsInTown)(v34)) )
    {
      return 4294967265i64;
    }
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, CPlayer *))v34->vfptr->IsBeDamagedAble)(v34, v33) )
      return 4294967290i64;
    LOBYTE(v12) = 1;
    if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *, __int64))v34->vfptr->IsBeAttackedAble)(v34, v12) )
      return 4294967290i64;
  }
  if ( v33->m_pmWpn.byWpType == 7 )
  {
    if ( !v33->m_bFreeSFByClass )
    {
      v24 = 0;
      for ( j = 0; j < 4; ++j )
      {
        v26 = (__int64)*v33->m_Param.m_ppHistoryEffect[j];
        if ( !v26 )
          break;
        if ( *(_DWORD *)(v26 + 1444) )
        {
          v24 = 1;
          break;
        }
      }
      if ( !v24 )
        return 4294967269i64;
    }
  }
  else if ( v33->m_pmWpn.byWpType == 11 || v33->m_pmWpn.byWpType == 10 )
  {
    return 4294967287i64;
  }
  if ( CPlayer::IsRidingUnit(v33) )
    return 4294967275i64;
  if ( _effect_parameter::GetEff_State(&v33->m_EP, 20) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v33->m_EP, 28) )
    return 4294967259i64;
  if ( _effect_parameter::GetEff_State(&v33->m_EP, 21) )
    return 4294967258i64;
  if ( v33->m_byMoveType == 2 )
    return 4294967255i64;
  if ( v35 != 0xFFFF )
  {
    v19 = CPlayer::IsBulletValidity(v33, v35);
    if ( !v19 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v33, 2, v35, 0);
      return 4294967279i64;
    }
    v20 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v19->m_wItemIndex);
    if ( v19->m_bLock )
      return 4294967267i64;
  }
  if ( (v33->m_pmWpn.byWpType == 5
     || v33->m_pmWpn.byWpType == 6
     || v33->m_pmWpn.byWpType == 7
     || v33->m_pmWpn.byWpType == 11)
    && !v20 )
  {
    return 4294967279i64;
  }
  if ( wEffBtSerial != 0xFFFF )
  {
    v21 = CPlayer::IsEffBulletValidity(v33, wEffBtSerial);
    if ( !v21 )
    {
      CPlayer::SendMsg_AdjustAmountInform(v33, 2, wEffBtSerial, 0);
      return 4294967233i64;
    }
    v22 = (_BulletItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, v21->m_wItemIndex);
    *ppEffBtProp = v21;
    *ppfldEffBt = v22;
  }
  v27 = 0.0;
  if ( v33->m_pmWpn.byWpType == 7 )
  {
    v31 = (float)v33->m_pmWpn.wGaAttRange;
    v16 = v31;
    ((void (__fastcall *)(CCharacter *))v34->vfptr->GetWidth)(v34);
    v17 = v31 + (float)(v16 / 2.0);
    v32 = v17;
    _effect_parameter::GetEff_Plus(&v33->m_EP, 36);
    *(float *)&v15 = v32 + v17;
    v27 = *(float *)&v15;
  }
  else
  {
    v29 = (float)v33->m_pmWpn.wGaAttRange;
    v13 = v29;
    ((void (__fastcall *)(CCharacter *))v34->vfptr->GetWidth)(v34);
    v14 = v29 + (float)(v13 / 2.0);
    v30 = v14;
    _effect_parameter::GetEff_Plus(&v33->m_EP, v33->m_pmWpn.byWpClass + 4);
    *(float *)&v15 = v30 + v14;
    v27 = *(float *)&v15;
  }
  GetSqrt(v34->m_fCurPos, v33->m_fCurPos);
  v28 = v15;
  if ( *(float *)&v15 <= v27 || (GetSqrt(v34->m_fOldPos, v33->m_fCurPos), v28 = v15, *(float *)&v15 <= v27) )
  {
    *ppBulletProp = v19;
    *ppfldBullet = v20;
    result = 0i64;
  }
  else
  {
    result = 4294967293i64;
  }
  return result;
}
