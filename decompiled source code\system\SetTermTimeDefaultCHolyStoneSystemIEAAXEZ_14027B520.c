/*
 * Function: ?SetTermTimeDefault@CHolyStoneSystem@@IEAAXE@Z
 * Address: 0x14027B520
 */

void __fastcall CHolyStoneSystem::SetTermTimeDefault(CHolyStoneSystem *this, char byNumOfTime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  CHolyScheduleData::__HolyScheduleNode *v5; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = CHolyScheduleData::GetIndex(&v6->m_ScheculeData, (unsigned __int8)byNumOfTime);
  if ( v5 )
  {
    v6->m_SaveData.m_dwTerm[0] = v5->m_nSceneTime[5] - (v5->m_nSceneTime[2] + v5->m_nSceneTime[0] + v5->m_nSceneTime[1]);
    v6->m_SaveData.m_dwTerm[1] = v5->m_nSceneTime[3];
  }
}
