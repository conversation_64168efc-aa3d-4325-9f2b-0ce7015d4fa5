/*
 * Function: j_?construct@?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@QEAAXPEAU?$pair@$$CBHPEAVCNationSettingFactory@@@2@AEBU32@@Z
 * Address: 0x14000A6CD
 */

void __fastcall std::allocator<std::pair<int const,CNationSettingFactory *>>::construct(std::allocator<std::pair<int const ,CNationSettingFactory *> > *this, std::pair<int const ,CNationSettingFactory *> *_Ptr, std::pair<int const ,CNationSettingFactory *> *_Val)
{
  std::allocator<std::pair<int const,CNationSettingFactory *>>::construct(this, _Ptr, _Val);
}
