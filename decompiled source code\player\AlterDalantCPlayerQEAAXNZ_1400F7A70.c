/*
 * Function: ?Alter<PERSON><PERSON>t@CPlayer@@QEAAXN@Z
 * Address: 0x1400F7A70
 */

void __fastcall CPlayer::AlterDalant(CPlayer *this, long double dDalant)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp-28h] [bp-28h]@1
  CPlayer *v5; // [sp+8h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dDalant <= 0.0 )
  {
    if ( dDalant < 0.0 )
      CPlayer::SubDalant(v5, (signed int)floor(-0.0 - dDalant));
  }
  else
  {
    CPlayer::AddDalant(v5, (signed int)floor(dDalant), 1);
  }
}
