/*
 * Function: ?_init_database@CashDbWorker@@MEAA_NXZ
 * Address: 0x1402F0720
 */

char __fastcall CashDbWorker::_init_database(CashDbWorker *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  char result; // al@8
  CNationSettingManager *v5; // rax@9
  CNationSettingManager *v6; // rax@11
  CNationSettingManager *v7; // rax@13
  const char *v8; // rax@13
  CNationSettingManager *v9; // rax@13
  CNationSettingManager *v10; // rax@13
  CNationSettingManager *v11; // rax@13
  CNationSettingManager *v12; // rax@13
  const char *v13; // rax@13
  CNationSettingManager *v14; // rax@14
  CNationSettingManager *v15; // rax@14
  CNationSettingManager *v16; // rax@14
  CNationSettingManager *v17; // rax@14
  CNationSettingManager *v18; // rax@14
  CNationSettingManager *v19; // rax@14
  CNationSettingManager *v20; // rax@14
  const char *v21; // rax@14
  CNationSettingManager *v22; // rax@15
  CNationSettingManager *v23; // rax@15
  CNationSettingManager *v24; // rax@15
  const char *v25; // rax@15
  CNationSettingManager *v26; // rax@16
  CNationSettingManager *v27; // rax@16
  CNationSettingManager *v28; // rax@16
  CNationSettingManager *v29; // rax@16
  CNationSettingManager *v30; // rax@16
  CNationSettingManager *v31; // rax@16
  CNationSettingManager *v32; // rax@16
  const char *v33; // rax@16
  CNationSettingManager *v34; // rax@17
  __int64 v35; // [sp+0h] [bp-108h]@1
  unsigned __int16 wPort[4]; // [sp+20h] [bp-E8h]@13
  const char *v37; // [sp+28h] [bp-E0h]@14
  int v38; // [sp+30h] [bp-D8h]@14
  const char *v39; // [sp+38h] [bp-D0h]@14
  const char *v40; // [sp+40h] [bp-C8h]@14
  CRFCashItemDatabase *v41; // [sp+50h] [bp-B8h]@7
  CRFCashItemDatabase *v42; // [sp+58h] [bp-B0h]@4
  __int64 v43; // [sp+60h] [bp-A8h]@4
  CRFCashItemDatabase *v44; // [sp+68h] [bp-A0h]@5
  unsigned __int16 v45; // [sp+70h] [bp-98h]@13
  char *szDatabase; // [sp+78h] [bp-90h]@13
  char *szServer; // [sp+80h] [bp-88h]@13
  const char *v48; // [sp+88h] [bp-80h]@14
  const char *v49; // [sp+90h] [bp-78h]@14
  int v50; // [sp+98h] [bp-70h]@14
  const char *v51; // [sp+A0h] [bp-68h]@14
  const char *v52; // [sp+A8h] [bp-60h]@14
  const char *v53; // [sp+B0h] [bp-58h]@14
  char *passWord; // [sp+B8h] [bp-50h]@15
  char *accountName; // [sp+C0h] [bp-48h]@15
  const char *v56; // [sp+C8h] [bp-40h]@16
  const char *v57; // [sp+D0h] [bp-38h]@16
  int v58; // [sp+D8h] [bp-30h]@16
  const char *v59; // [sp+E0h] [bp-28h]@16
  const char *v60; // [sp+E8h] [bp-20h]@16
  const char *v61; // [sp+F0h] [bp-18h]@16
  CashDbWorker *v62; // [sp+110h] [bp+8h]@1

  v62 = this;
  v1 = &v35;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v43 = -2i64;
  v42 = (CRFCashItemDatabase *)operator new(0x3F8ui64);
  if ( v42 )
  {
    CRFCashItemDatabase::CRFCashItemDatabase(v42);
    v44 = (CRFCashItemDatabase *)v3;
  }
  else
  {
    v44 = 0i64;
  }
  v41 = v44;
  v62->_pkDb = v44;
  if ( v62->_pkDb )
  {
    v5 = CTSingleton<CNationSettingManager>::Instance();
    if ( CNationSettingManager::IsCashDBInit(v5) )
    {
      MyMessageBox("CashDbWorker::_init_database()", "Cash DB Already Initialized!\r\n");
      result = 0;
    }
    else
    {
      v6 = CTSingleton<CNationSettingManager>::Instance();
      if ( CNationSettingManager::IsCashDBDSNSetted(v6) )
      {
        v7 = CTSingleton<CNationSettingManager>::Instance();
        v8 = CNationSettingManager::GetCashDBName(v7);
        CRFNewDatabase::SetLogFile((CRFNewDatabase *)&v62->_pkDb->vfptr, "..\\ZoneServerLog\\", v8);
        v9 = CTSingleton<CNationSettingManager>::Instance();
        v45 = CNationSettingManager::GetCashDBPort(v9);
        v10 = CTSingleton<CNationSettingManager>::Instance();
        szDatabase = (char *)CNationSettingManager::GetCashDBName(v10);
        v11 = CTSingleton<CNationSettingManager>::Instance();
        szServer = (char *)CNationSettingManager::GetCashDBDBIP(v11);
        v12 = CTSingleton<CNationSettingManager>::Instance();
        v13 = CNationSettingManager::GetCashDBName(v12);
        wPort[0] = v45;
        if ( CRFNewDatabase::ConfigUserODBC((CRFNewDatabase *)&v62->_pkDb->vfptr, v13, szServer, szDatabase, v45) )
        {
          v22 = CTSingleton<CNationSettingManager>::Instance();
          passWord = (char *)CNationSettingManager::GetCashDBPW(v22);
          v23 = CTSingleton<CNationSettingManager>::Instance();
          accountName = (char *)CNationSettingManager::GetCashDBID(v23);
          v24 = CTSingleton<CNationSettingManager>::Instance();
          v25 = CNationSettingManager::GetCashDBName(v24);
          if ( CRFNewDatabase::StartDataBase((CRFNewDatabase *)&v62->_pkDb->vfptr, v25, accountName, passWord) )
          {
            v34 = CTSingleton<CNationSettingManager>::Instance();
            CNationSettingManager::SetCashDBInitState(v34);
            CRFNewDatabase::SetReconnectFailExitFlag((CRFNewDatabase *)&v62->_pkDb->vfptr, 0);
            result = 1;
          }
          else
          {
            v26 = CTSingleton<CNationSettingManager>::Instance();
            v56 = CNationSettingManager::GetCashDBPW(v26);
            v27 = CTSingleton<CNationSettingManager>::Instance();
            v57 = CNationSettingManager::GetCashDBID(v27);
            v28 = CTSingleton<CNationSettingManager>::Instance();
            v58 = CNationSettingManager::GetCashDBPort(v28);
            v29 = CTSingleton<CNationSettingManager>::Instance();
            v59 = CNationSettingManager::GetCashDBName(v29);
            v30 = CTSingleton<CNationSettingManager>::Instance();
            v60 = CNationSettingManager::GetCashDBDBIP(v30);
            v31 = CTSingleton<CNationSettingManager>::Instance();
            v61 = CNationSettingManager::GetCashDBName(v31);
            v32 = CTSingleton<CNationSettingManager>::Instance();
            v33 = CNationSettingManager::GetNationCodeStr(v32);
            v40 = v56;
            v39 = v57;
            v38 = v58;
            v37 = v59;
            *(_QWORD *)wPort = v60;
            MyMessageBox(
              "CashDbWorker::_init_database()",
              "StartDataBase Failed!\r\n"
              "Nation(%s) DSN(%s) IP(%s) DBName(%s) Port(%u)\r\n"
              "ID(%s) PW(%s)\r\n"
              "Check Cash DB DSN Setting!",
              v33,
              v61);
            result = 0;
          }
        }
        else
        {
          v14 = CTSingleton<CNationSettingManager>::Instance();
          v48 = CNationSettingManager::GetCashDBPW(v14);
          v15 = CTSingleton<CNationSettingManager>::Instance();
          v49 = CNationSettingManager::GetCashDBID(v15);
          v16 = CTSingleton<CNationSettingManager>::Instance();
          v50 = CNationSettingManager::GetCashDBPort(v16);
          v17 = CTSingleton<CNationSettingManager>::Instance();
          v51 = CNationSettingManager::GetCashDBName(v17);
          v18 = CTSingleton<CNationSettingManager>::Instance();
          v52 = CNationSettingManager::GetCashDBDBIP(v18);
          v19 = CTSingleton<CNationSettingManager>::Instance();
          v53 = CNationSettingManager::GetCashDBName(v19);
          v20 = CTSingleton<CNationSettingManager>::Instance();
          v21 = CNationSettingManager::GetNationCodeStr(v20);
          v40 = v48;
          v39 = v49;
          v38 = v50;
          v37 = v51;
          *(_QWORD *)wPort = v52;
          MyMessageBox(
            "CashDbWorker::_init_database()",
            "Cash DB ODBC Setting Failed!\r\n"
            "Nation(%s) DSN(%s) IP(%s) DBName(%s) Port(%u)\r\n"
            "ID(%s) PW(%s)\r\n"
            "Check Cash DB DSN Setting!",
            v21,
            v53);
          result = 0;
        }
      }
      else
      {
        MyMessageBox("CashDbWorker::_init_database()", "Cash DB DSN Setting Not Setted!!\r\n");
        result = 0;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
