#include "../Headers/AuthenticationTypes.h"
#include <cstring>
#include <sstream>
#include <iomanip>

namespace NexusProtection::Authentication {

    void ConvertToLegacy(const AccountInfo& modern, _ACCOUNT_DATA& legacy) noexcept {
        // Clear legacy structure
        std::memset(&legacy, 0, sizeof(_ACCOUNT_DATA));

        // Copy basic data
        legacy.dwAccountSerial = modern.accountSerial;
        legacy.byBillingType = static_cast<uint8_t>(modern.billingType);
        legacy.bActive = modern.isActive;
        legacy.dwLoginCount = modern.loginCount;

        // Copy strings with bounds checking
        if (!modern.accountName.empty()) {
            size_t copyLen = std::min(modern.accountName.length(), static_cast<size_t>(MAX_ACCOUNT_NAME_LENGTH - 1));
            std::memcpy(legacy.szAccountName, modern.accountName.c_str(), copyLen);
            legacy.szAccountName[copyLen] = '\0';
        }

        if (!modern.hashedPassword.empty()) {
            size_t copyLen = std::min(modern.hashedPassword.length(), static_cast<size_t>(MAX_PASSWORD_LENGTH - 1));
            std::memcpy(legacy.szPassword, modern.hashedPassword.c_str(), copyLen);
            legacy.szPassword[copyLen] = '\0';
        }

        // Convert time to SYSTEMTIME
        auto lastLoginTime = std::chrono::system_clock::to_time_t(modern.lastLogin);
        auto* tm = std::localtime(&lastLoginTime);
        if (tm) {
            legacy.stLastLogin.wYear = static_cast<uint16_t>(tm->tm_year + 1900);
            legacy.stLastLogin.wMonth = static_cast<uint16_t>(tm->tm_mon + 1);
            legacy.stLastLogin.wDay = static_cast<uint16_t>(tm->tm_mday);
            legacy.stLastLogin.wHour = static_cast<uint16_t>(tm->tm_hour);
            legacy.stLastLogin.wMinute = static_cast<uint16_t>(tm->tm_min);
            legacy.stLastLogin.wSecond = static_cast<uint16_t>(tm->tm_sec);
            legacy.stLastLogin.wDayOfWeek = static_cast<uint16_t>(tm->tm_wday);
            legacy.stLastLogin.wMilliseconds = 0;
        }
    }

    void ConvertFromLegacy(const _ACCOUNT_DATA& legacy, AccountInfo& modern) noexcept {
        // Reset modern structure
        modern.Reset();

        // Copy basic data
        modern.accountSerial = legacy.dwAccountSerial;
        modern.billingType = static_cast<BillingType>(legacy.byBillingType);
        modern.isActive = legacy.bActive;
        modern.loginCount = legacy.dwLoginCount;

        // Copy strings
        modern.accountName = std::string(legacy.szAccountName);
        modern.hashedPassword = std::string(legacy.szPassword);

        // Convert SYSTEMTIME to time_point
        std::tm tm = {};
        tm.tm_year = legacy.stLastLogin.wYear - 1900;
        tm.tm_mon = legacy.stLastLogin.wMonth - 1;
        tm.tm_mday = legacy.stLastLogin.wDay;
        tm.tm_hour = legacy.stLastLogin.wHour;
        tm.tm_min = legacy.stLastLogin.wMinute;
        tm.tm_sec = legacy.stLastLogin.wSecond;
        
        auto timeT = std::mktime(&tm);
        if (timeT != -1) {
            modern.lastLogin = std::chrono::system_clock::from_time_t(timeT);
        }
    }

    void ConvertToLegacy(const SessionInfo& modern, _SESSION_DATA& legacy) noexcept {
        // Clear legacy structure
        std::memset(&legacy, 0, sizeof(_SESSION_DATA));

        // Copy basic data
        legacy.dwSessionId = modern.sessionId;
        legacy.dwAccountSerial = modern.accountSerial;
        legacy.byState = static_cast<uint8_t>(modern.state);
        legacy.wClientPort = modern.clientPort;
        legacy.bySecurityLevel = static_cast<uint8_t>(modern.securityLevel);

        // Convert time points to milliseconds since epoch
        legacy.dwStartTime = static_cast<uint32_t>(
            std::chrono::duration_cast<std::chrono::milliseconds>(
                modern.startTime.time_since_epoch()).count());
        legacy.dwLastActivity = static_cast<uint32_t>(
            std::chrono::duration_cast<std::chrono::milliseconds>(
                modern.lastActivity.time_since_epoch()).count());

        // Copy IP address with bounds checking
        if (!modern.clientIP.empty()) {
            size_t copyLen = std::min(modern.clientIP.length(), static_cast<size_t>(15));
            std::memcpy(legacy.szClientIP, modern.clientIP.c_str(), copyLen);
            legacy.szClientIP[copyLen] = '\0';
        }
    }

    void ConvertFromLegacy(const _SESSION_DATA& legacy, SessionInfo& modern) noexcept {
        // Reset modern structure
        modern.Reset();

        // Copy basic data
        modern.sessionId = legacy.dwSessionId;
        modern.accountSerial = legacy.dwAccountSerial;
        modern.state = static_cast<SessionState>(legacy.byState);
        modern.clientPort = legacy.wClientPort;
        modern.securityLevel = static_cast<SecurityLevel>(legacy.bySecurityLevel);

        // Convert milliseconds to time points
        modern.startTime = std::chrono::steady_clock::time_point(
            std::chrono::milliseconds(legacy.dwStartTime));
        modern.lastActivity = std::chrono::steady_clock::time_point(
            std::chrono::milliseconds(legacy.dwLastActivity));

        // Copy IP address
        modern.clientIP = std::string(legacy.szClientIP);
    }

    std::string AuthenticationResultToString(AuthenticationResult result) {
        switch (result) {
            case AuthenticationResult::Success: return "Success";
            case AuthenticationResult::Failure: return "Failure";
            case AuthenticationResult::InvalidCredentials: return "Invalid Credentials";
            case AuthenticationResult::AccountLocked: return "Account Locked";
            case AuthenticationResult::SystemError: return "System Error";
            case AuthenticationResult::SecurityViolation: return "Security Violation";
            case AuthenticationResult::BillingError: return "Billing Error";
            case AuthenticationResult::SessionExpired: return "Session Expired";
            default: return "Unknown";
        }
    }

    std::string BillingTypeToString(BillingType type) {
        switch (type) {
            case BillingType::Free: return "Free";
            case BillingType::Premium: return "Premium";
            case BillingType::VIP: return "VIP";
            case BillingType::Trial: return "Trial";
            case BillingType::Suspended: return "Suspended";
            default: return "Unknown";
        }
    }

    std::string LogTypeToString(LogType type) {
        switch (type) {
            case LogType::System: return "System";
            case LogType::Authentication: return "Authentication";
            case LogType::Billing: return "Billing";
            case LogType::Security: return "Security";
            case LogType::Error: return "Error";
            case LogType::Debug: return "Debug";
            default: return "Unknown";
        }
    }

    std::string SecurityLevelToString(SecurityLevel level) {
        switch (level) {
            case SecurityLevel::None: return "None";
            case SecurityLevel::Basic: return "Basic";
            case SecurityLevel::Enhanced: return "Enhanced";
            case SecurityLevel::Maximum: return "Maximum";
            default: return "Unknown";
        }
    }

    std::string SessionStateToString(SessionState state) {
        switch (state) {
            case SessionState::Disconnected: return "Disconnected";
            case SessionState::Connecting: return "Connecting";
            case SessionState::Connected: return "Connected";
            case SessionState::Authenticated: return "Authenticated";
            case SessionState::Active: return "Active";
            case SessionState::Suspended: return "Suspended";
            case SessionState::Terminating: return "Terminating";
            default: return "Unknown";
        }
    }

} // namespace NexusProtection::Authentication
