/*
 * Function: ?Select_ItemCharge@CRFWorldDatabase@@QEAA_NKPEAEPEAKPEA_K11PEAH@Z
 * Address: 0x140493FC0
 */

char __fastcall CRFWorldDatabase::Select_ItemCharge(CRFWorldDatabase *this, unsigned int dwAvatorSerial, char *pbyType, unsigned int *pDwItemCode_K, unsigned __int64 *pDwItemCode_D, unsigned int *pDwItemCode_U, unsigned int *pDwItemChargeIndex, int *piTime)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v11; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@21
  SQLLEN v14; // [sp+38h] [bp-150h]@21
  __int16 v15; // [sp+44h] [bp-144h]@9
  char Dest; // [sp+60h] [bp-128h]@4
  int v17; // [sp+164h] [bp-24h]@4
  unsigned __int64 v18; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v19; // [sp+190h] [bp+8h]@1
  char *v20; // [sp+1A0h] [bp+18h]@1
  unsigned int *TargetValue; // [sp+1A8h] [bp+20h]@1

  TargetValue = pDwItemCode_K;
  v20 = pbyType;
  v19 = this;
  v8 = &v11;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v18 = (unsigned __int64)&v11 ^ _security_cookie;
  v17 = 0;
  sprintf(&Dest, "{ CALL pSelect_ItemCharge_20070130( %d ) }", dwAvatorSerial);
  if ( v19->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v19->vfptr, &Dest);
  if ( v19->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v19->vfptr) )
  {
    v15 = SQLExecDirectA_0(v19->m_hStmtSelect, &Dest, -3);
    if ( v15 && v15 != 1 )
    {
      if ( v15 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v15, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v15, v19->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v15 = SQLFetch_0(v19->m_hStmtSelect);
      if ( v15 && v15 != 1 )
      {
        if ( v15 != 100 )
        {
          SQLStmt = v19->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v15, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v15, v19->m_hStmtSelect);
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        result = 0;
      }
      else
      {
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v19->m_hStmtSelect, 1u, 4, pDwItemChargeIndex, 0i64, &v14);
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v19->m_hStmtSelect, 2u, 4, TargetValue, 0i64, &v14);
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v19->m_hStmtSelect, 3u, -25, pDwItemCode_D, 0i64, &v14);
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v19->m_hStmtSelect, 4u, 4, pDwItemCode_U, 0i64, &v14);
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v19->m_hStmtSelect, 5u, -28, v20, 0i64, &v14);
        StrLen_or_IndPtr = &v14;
        SQLStmt = 0i64;
        v15 = SQLGetData_0(v19->m_hStmtSelect, 6u, 4, piTime, 0i64, &v14);
        if ( v15 == 100 )
        {
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v19->m_hStmtSelect )
            SQLCloseCursor_0(v19->m_hStmtSelect);
          if ( v19->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v19->vfptr, "%s Success", &Dest);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v19->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
