/*
 * Function: ?RoomTimer@CGuildRoomInfo@@QEAAXXZ
 * Address: 0x1402E5F20
 */

void __fastcall CGuildRoomInfo::RoomTimer(CGuildRoomInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@4
  CGuildRoomInfo *v5; // [sp+50h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _time32(&Time);
  if ( Time >= v5->m_timer )
    CGuildRoomInfo::TimeOver(v5);
}
