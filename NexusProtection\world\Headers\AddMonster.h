#pragma once

#include <cstdint>
#include <memory>
#include <string>
#include <unordered_map>
#include <functional>
#include <vector>

namespace NexusProtection::World {

    // Forward declarations
    class ReactObj;
    class ReactArea;

    /**
     * @brief React Object - Lua signal reactor for objects
     * 
     * Represents a reactive object that can respond to Lua signals and events.
     * This is a simplified modern implementation of the original _react_obj.
     */
    class ReactObj {
    public:
        ReactObj();
        ~ReactObj() = default;

        // Copy and move semantics
        ReactObj(const ReactObj&) = default;
        ReactObj& operator=(const ReactObj&) = default;
        ReactObj(ReactObj&&) = default;
        ReactObj& operator=(ReactObj&&) = default;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsActive() const { return m_isActive; }
        void SetActive(bool active) { m_isActive = active; }

        // Signal handling
        void ProcessSignal(const std::string& signal);
        void RegisterSignalHandler(const std::string& signal, std::function<void()> handler);

    private:
        bool m_isActive{false};
        std::unordered_map<std::string, std::function<void()>> m_signalHandlers;
    };

    /**
     * @brief React Area - Lua signal reactor for areas
     * 
     * Represents a reactive area that can respond to Lua signals and spatial events.
     * This is a simplified modern implementation of the original _react_area.
     */
    class ReactArea {
    public:
        ReactArea();
        ~ReactArea() = default;

        // Copy and move semantics
        ReactArea(const ReactArea&) = default;
        ReactArea& operator=(const ReactArea&) = default;
        ReactArea(ReactArea&&) = default;
        ReactArea& operator=(ReactArea&&) = default;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsActive() const { return m_isActive; }
        void SetActive(bool active) { m_isActive = active; }

        // Area management
        void SetArea(float x, float y, float width, float height);
        bool IsPointInArea(float x, float y) const;
        void ProcessAreaEvent(const std::string& event, float x, float y);

    private:
        bool m_isActive{false};
        float m_x{0.0f}, m_y{0.0f};
        float m_width{0.0f}, m_height{0.0f};
        std::unordered_map<std::string, std::function<void(float, float)>> m_areaHandlers;
    };

    /**
     * @brief Add Monster Configuration
     * 
     * Represents configuration and reactive components for monster addition operations.
     * This class manages reactive objects and areas that respond to monster-related events.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - 0__add_monsterQEAAXZ_14027A3F0.c (constructor)
     */
    class AddMonster {
    public:
        // Constructor and destructor
        AddMonster();
        ~AddMonster() = default;

        // Copy and move semantics
        AddMonster(const AddMonster& other);
        AddMonster& operator=(const AddMonster& other);
        AddMonster(AddMonster&& other) noexcept;
        AddMonster& operator=(AddMonster&& other) noexcept;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsValid() const;

        // React object management
        ReactObj& GetReactObj() { return m_reactObj; }
        const ReactObj& GetReactObj() const { return m_reactObj; }
        void SetReactObj(const ReactObj& reactObj) { m_reactObj = reactObj; }

        // React area management
        ReactArea& GetReactArea() { return m_reactArea; }
        const ReactArea& GetReactArea() const { return m_reactArea; }
        void SetReactArea(const ReactArea& reactArea) { m_reactArea = reactArea; }

        // Monster addition operations
        void ProcessMonsterAddition(const std::string& monsterType, float x, float y);
        void HandleMonsterEvent(const std::string& event, const std::string& data);

        // Configuration management
        void ConfigureReactiveComponents(bool enableObj, bool enableArea);
        bool AreReactiveComponentsActive() const;

        // Utility methods
        std::string ToString() const;
        size_t GetMemoryUsage() const;

        // Legacy C interface compatibility
        void InitializeLegacy();
        void* GetReactObjPtr() { return &m_reactObj; }
        void* GetReactAreaPtr() { return &m_reactArea; }

        // Validation
        bool ValidateConfiguration() const;

    private:
        // Member variables
        ReactObj m_reactObj;              // Reactive object component (originally ReactObj)
        ReactArea m_reactArea;            // Reactive area component (originally ReactArea)
        
        // Internal state
        bool m_isInitialized{false};
        
        // Internal methods
        void InitializeComponents();
        void ResetComponents();
    };

    /**
     * @brief Add Monster Factory
     * 
     * Factory class for creating AddMonster instances with proper configuration.
     */
    class AddMonsterFactory {
    public:
        static std::unique_ptr<AddMonster> CreateAddMonster();
        static std::unique_ptr<AddMonster> CreateAddMonster(bool enableReactObj, bool enableReactArea);
        
        // Batch creation
        static std::vector<std::unique_ptr<AddMonster>> CreateAddMonsters(size_t count);
    };

    /**
     * @brief Add Monster Utilities
     * 
     * Utility functions for AddMonster management and operations.
     */
    namespace AddMonsterUtils {
        // Validation utilities
        bool ValidateAddMonster(const AddMonster& addMonster);
        bool ValidateReactiveComponents(const ReactObj& reactObj, const ReactArea& reactArea);
        
        // Memory utilities
        size_t CalculateMemoryFootprint(const AddMonster& addMonster);
        
        // Configuration utilities
        void ConfigureDefaultReactiveComponents(AddMonster& addMonster);
        void OptimizeReactiveComponents(AddMonster& addMonster);
    }

    // Legacy C interface
    extern "C" {
        // Legacy structure for compatibility
        struct _add_monster {
            void* ReactObj;               // Pointer to reactive object
            void* ReactArea;              // Pointer to reactive area
            char padding[32];             // Additional padding for original structure size
        };

        // Legacy _react_obj structure
        struct _react_obj {
            char data[64];                // Placeholder for original structure data
        };

        // Legacy _react_area structure
        struct _react_area {
            char data[64];                // Placeholder for original structure data
        };

        // Legacy function declarations
        void __add_monster_Constructor(_add_monster* this_ptr);
        void __add_monster_Destructor(_add_monster* this_ptr);
        
        // React object functions
        void _react_obj_Constructor(_react_obj* this_ptr);
        void _react_obj_Destructor(_react_obj* this_ptr);
        
        // React area functions
        void _react_area_Constructor(_react_area* this_ptr);
        void _react_area_Destructor(_react_area* this_ptr);
        
        // Legacy utility functions
        void __add_monster_Initialize(_add_monster* this_ptr);
        void __add_monster_Reset(_add_monster* this_ptr);
        void* __add_monster_GetReactObj(_add_monster* this_ptr);
        void* __add_monster_GetReactArea(_add_monster* this_ptr);
    }

} // namespace NexusProtection::World

// Legacy global compatibility
extern NexusProtection::World::AddMonster* g_pAddMonster;
