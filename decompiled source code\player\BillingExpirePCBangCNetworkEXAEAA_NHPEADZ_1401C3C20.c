/*
 * Function: ?BillingExpirePCBang@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C3C20
 */

char __fastcall CNetworkEX::BillingExpirePCBang(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CBillingManager *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *szCMS; // [sp+20h] [bp-18h]@4

  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szCMS = pBuf;
  v5 = CTSingleton<CBillingManager>::Instance();
  CBillingManager::Expire_PCBang(v5, szCMS);
  return 1;
}
