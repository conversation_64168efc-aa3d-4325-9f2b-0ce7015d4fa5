/*
 * Function: ?SendWebAddScheduleInfo@CNormalGuildBattle@GUILD_BATTLE@@IEAAXXZ
 * Address: 0x1403E8690
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattle::SendWebAddScheduleInfo(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char *v3; // rax@4
  char *v4; // rax@4
  char *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-C8h]@1
  char szMsg; // [sp+38h] [bp-90h]@4
  char v8; // [sp+39h] [bp-8Fh]@4
  unsigned int v9; // [sp+3Ah] [bp-8Eh]@4
  char v10; // [sp+3Eh] [bp-8Ah]@4
  char Dst; // [sp+3Fh] [bp-89h]@4
  unsigned int v12; // [sp+50h] [bp-78h]@4
  char v13; // [sp+54h] [bp-74h]@4
  char v14; // [sp+55h] [bp-73h]@4
  char v15; // [sp+66h] [bp-62h]@4
  int v16; // [sp+67h] [bp-61h]@4
  char v17; // [sp+6Bh] [bp-5Dh]@4
  char pbyType; // [sp+94h] [bp-34h]@4
  char v19; // [sp+95h] [bp-33h]@4
  unsigned __int64 v20; // [sp+B0h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattle *v21; // [sp+D0h] [bp+8h]@1

  v21 = this;
  v1 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v20 = (unsigned __int64)&v6 ^ _security_cookie;
  szMsg = v21->m_dwID % 0x17;
  v8 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildRace(&v21->m_k1P);
  v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v21->m_k1P);
  v10 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v21->m_k1P)->m_byGrade;
  v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v21->m_k1P);
  strcpy_s(&Dst, 0x11ui64, v3);
  v12 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildSerial(&v21->m_k2P);
  v13 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuild(&v21->m_k2P)->m_byGrade;
  v4 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v21->m_k2P);
  strcpy_s(&v14, 0x11ui64, v4);
  v15 = v21->m_byGuildBattleNumber;
  v16 = 5000;
  v5 = GUILD_BATTLE::CNormalGuildBattleField::GetMapStrCode(v21->m_pkField);
  strcpy_s(&v17, 0xCui64, v5);
  pbyType = 51;
  v19 = 16;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, &szMsg, 0x3Fu);
}
