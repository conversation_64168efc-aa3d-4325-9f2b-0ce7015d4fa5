/*
 * Function: ?RegistCode@CNationCodeStrTable@@AEAAHXZ
 * Address: 0x14020AE30
 */

signed __int64 __fastcall CNationCodeStrTable::RegistCode(CNationCodeStrTable *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationCodeStr *v3; // rax@5
  signed __int64 result; // rax@8
  CNationCodeStr *v5; // rax@10
  CNationCodeStr *v6; // rax@15
  CNationCodeStr *v7; // rax@20
  CNationCodeStr *v8; // rax@25
  CNationCodeStr *v9; // rax@30
  CNationCodeStr *v10; // rax@35
  CNationCodeStr *v11; // rax@40
  CNationCodeStr *v12; // rax@45
  CNationCodeStr *v13; // rax@50
  CNationCodeStr *v14; // rax@55
  CNationCodeStr *v15; // rax@60
  __int64 v16; // [sp+0h] [bp-158h]@1
  int v17; // [sp+20h] [bp-138h]@7
  CNationCodeStr *pData; // [sp+28h] [bp-130h]@7
  CNationCodeStr *v19; // [sp+30h] [bp-128h]@4
  CNationCodeStr *v20; // [sp+38h] [bp-120h]@12
  CNationCodeStr *v21; // [sp+40h] [bp-118h]@9
  CNationCodeStr *v22; // [sp+48h] [bp-110h]@17
  CNationCodeStr *v23; // [sp+50h] [bp-108h]@14
  CNationCodeStr *v24; // [sp+58h] [bp-100h]@22
  CNationCodeStr *v25; // [sp+60h] [bp-F8h]@19
  CNationCodeStr *v26; // [sp+68h] [bp-F0h]@27
  CNationCodeStr *v27; // [sp+70h] [bp-E8h]@24
  CNationCodeStr *v28; // [sp+78h] [bp-E0h]@32
  CNationCodeStr *v29; // [sp+80h] [bp-D8h]@29
  CNationCodeStr *v30; // [sp+88h] [bp-D0h]@37
  CNationCodeStr *v31; // [sp+90h] [bp-C8h]@34
  CNationCodeStr *v32; // [sp+98h] [bp-C0h]@42
  CNationCodeStr *v33; // [sp+A0h] [bp-B8h]@39
  CNationCodeStr *v34; // [sp+A8h] [bp-B0h]@47
  CNationCodeStr *v35; // [sp+B0h] [bp-A8h]@44
  CNationCodeStr *v36; // [sp+B8h] [bp-A0h]@52
  CNationCodeStr *v37; // [sp+C0h] [bp-98h]@49
  CNationCodeStr *v38; // [sp+C8h] [bp-90h]@57
  CNationCodeStr *v39; // [sp+D0h] [bp-88h]@54
  CNationCodeStr *v40; // [sp+D8h] [bp-80h]@62
  CNationCodeStr *v41; // [sp+E0h] [bp-78h]@59
  __int64 v42; // [sp+E8h] [bp-70h]@4
  CNationCodeStr *v43; // [sp+F0h] [bp-68h]@5
  CNationCodeStr *v44; // [sp+F8h] [bp-60h]@10
  CNationCodeStr *v45; // [sp+100h] [bp-58h]@15
  CNationCodeStr *v46; // [sp+108h] [bp-50h]@20
  CNationCodeStr *v47; // [sp+110h] [bp-48h]@25
  CNationCodeStr *v48; // [sp+118h] [bp-40h]@30
  CNationCodeStr *v49; // [sp+120h] [bp-38h]@35
  CNationCodeStr *v50; // [sp+128h] [bp-30h]@40
  CNationCodeStr *v51; // [sp+130h] [bp-28h]@45
  CNationCodeStr *v52; // [sp+138h] [bp-20h]@50
  CNationCodeStr *v53; // [sp+140h] [bp-18h]@55
  CNationCodeStr *v54; // [sp+148h] [bp-10h]@60
  CNationCodeStrTable *v55; // [sp+160h] [bp+8h]@1

  v55 = this;
  v1 = &v16;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v42 = -2i64;
  v19 = (CNationCodeStr *)operator new(8ui64);
  if ( v19 )
  {
    CNationCodeStr::CNationCodeStr(v19, 410, "KR");
    v43 = v3;
  }
  else
  {
    v43 = 0i64;
  }
  pData = v43;
  v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v43);
  if ( v17 )
  {
    result = 0xFFFFFFFFi64;
  }
  else
  {
    v21 = (CNationCodeStr *)operator new(8ui64);
    if ( v21 )
    {
      CNationCodeStr::CNationCodeStr(v21, 826, "GB");
      v44 = v5;
    }
    else
    {
      v44 = 0i64;
    }
    v20 = v44;
    v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v44);
    if ( v17 )
    {
      result = 0xFFFFFFFFi64;
    }
    else
    {
      v23 = (CNationCodeStr *)operator new(8ui64);
      if ( v23 )
      {
        CNationCodeStr::CNationCodeStr(v23, 360, "ID");
        v45 = v6;
      }
      else
      {
        v45 = 0i64;
      }
      v22 = v45;
      v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v45);
      if ( v17 )
      {
        result = 0xFFFFFFFFi64;
      }
      else
      {
        v25 = (CNationCodeStr *)operator new(8ui64);
        if ( v25 )
        {
          CNationCodeStr::CNationCodeStr(v25, 392, "JP");
          v46 = v7;
        }
        else
        {
          v46 = 0i64;
        }
        v24 = v46;
        v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v46);
        if ( v17 )
        {
          result = 0xFFFFFFFFi64;
        }
        else
        {
          v27 = (CNationCodeStr *)operator new(8ui64);
          if ( v27 )
          {
            CNationCodeStr::CNationCodeStr(v27, 608, "PH");
            v47 = v8;
          }
          else
          {
            v47 = 0i64;
          }
          v26 = v47;
          v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v47);
          if ( v17 )
          {
            result = 0xFFFFFFFFi64;
          }
          else
          {
            v29 = (CNationCodeStr *)operator new(8ui64);
            if ( v29 )
            {
              CNationCodeStr::CNationCodeStr(v29, 643, "RU");
              v48 = v9;
            }
            else
            {
              v48 = 0i64;
            }
            v28 = v48;
            v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v48);
            if ( v17 )
            {
              result = 0xFFFFFFFFi64;
            }
            else
            {
              v31 = (CNationCodeStr *)operator new(8ui64);
              if ( v31 )
              {
                CNationCodeStr::CNationCodeStr(v31, 76, "BR");
                v49 = v10;
              }
              else
              {
                v49 = 0i64;
              }
              v30 = v49;
              v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v49);
              if ( v17 )
              {
                result = 0xFFFFFFFFi64;
              }
              else
              {
                v33 = (CNationCodeStr *)operator new(8ui64);
                if ( v33 )
                {
                  CNationCodeStr::CNationCodeStr(v33, 158, "TW");
                  v50 = v11;
                }
                else
                {
                  v50 = 0i64;
                }
                v32 = v50;
                v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v50);
                if ( v17 )
                {
                  result = 0xFFFFFFFFi64;
                }
                else
                {
                  v35 = (CNationCodeStr *)operator new(8ui64);
                  if ( v35 )
                  {
                    CNationCodeStr::CNationCodeStr(v35, 156, "CN");
                    v51 = v12;
                  }
                  else
                  {
                    v51 = 0i64;
                  }
                  v34 = v51;
                  v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v51);
                  if ( v17 )
                  {
                    result = 0xFFFFFFFFi64;
                  }
                  else
                  {
                    v37 = (CNationCodeStr *)operator new(8ui64);
                    if ( v37 )
                    {
                      CNationCodeStr::CNationCodeStr(v37, 840, "US");
                      v52 = v13;
                    }
                    else
                    {
                      v52 = 0i64;
                    }
                    v36 = v52;
                    v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v52);
                    if ( v17 )
                    {
                      result = 0xFFFFFFFFi64;
                    }
                    else
                    {
                      v39 = (CNationCodeStr *)operator new(8ui64);
                      if ( v39 )
                      {
                        CNationCodeStr::CNationCodeStr(v39, 724, "ES");
                        v53 = v14;
                      }
                      else
                      {
                        v53 = 0i64;
                      }
                      v38 = v53;
                      v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v53);
                      if ( v17 )
                      {
                        result = 0xFFFFFFFFi64;
                      }
                      else
                      {
                        v41 = (CNationCodeStr *)operator new(8ui64);
                        if ( v41 )
                        {
                          CNationCodeStr::CNationCodeStr(v41, 764, "TH");
                          v54 = v15;
                        }
                        else
                        {
                          v54 = 0i64;
                        }
                        v40 = v54;
                        v17 = CHashMapPtrPool<int,CNationCodeStr>::regist(&v55->m_kTable, v54);
                        if ( v17 )
                          result = 0xFFFFFFFFi64;
                        else
                          result = 0i64;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  return result;
}
