/*
 * Function: j_??$_Uninit_copy@PEAUCHEAT_COMMAND@@PEAU1@V?$allocator@UCHEAT_COMMAND@@@std@@@std@@YAPEAUCHEAT_COMMAND@@PEAU1@00AEAV?$allocator@UCHEAT_COMMAND@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000A39E
 */

CHEAT_COMMAND *__fastcall std::_Uninit_copy<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(CHEAT_COMMAND *_First, CHEAT_COMMAND *_Last, CHEAT_COMMAND *_Dest, std::allocator<CHEAT_COMMAND> *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CHEAT_COMMAND *,CHEAT_COMMAND *,std::allocator<CHEAT_COMMAND>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
