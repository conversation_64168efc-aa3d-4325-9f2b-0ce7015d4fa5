/*
 * Function: ?DeleteUseConsumeItem@CPlayer@@QEAA_NPEAPEAU_db_con@_STORAGE_LIST@@PEAHPEA_N@Z
 * Address: 0x140067F60
 */

char __fastcall CPlayer::DeleteUseConsumeItem(CPlayer *this, _STORAGE_LIST::_db_con **ppConsumeItems, int *pnConsume, bool *pbOverLap)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  int v7; // eax@13
  _STORAGE_LIST::_db_con *v8; // rcx@13
  _STORAGE_LIST *v9; // rdx@13
  _STORAGE_LIST::_db_con *v10; // rax@17
  _STORAGE_LIST *v11; // rcx@17
  __int64 v12; // [sp+0h] [bp-48h]@1
  bool bUpdate; // [sp+20h] [bp-28h]@13
  bool bSend[8]; // [sp+28h] [bp-20h]@13
  int j; // [sp+30h] [bp-18h]@8
  unsigned int dwDur; // [sp+34h] [bp-14h]@13
  CPlayer *v17; // [sp+50h] [bp+8h]@1
  _STORAGE_LIST::_db_con **v18; // [sp+58h] [bp+10h]@1
  int *v19; // [sp+60h] [bp+18h]@1
  bool *v20; // [sp+68h] [bp+20h]@1

  v20 = pbOverLap;
  v19 = pnConsume;
  v18 = ppConsumeItems;
  v17 = this;
  v4 = &v12;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( ppConsumeItems && pnConsume && pbOverLap )
  {
    for ( j = 0; j < 3; ++j )
    {
      if ( v18[j] && v19[j] > 0 )
      {
        if ( v20[j] )
        {
          v7 = -v19[j];
          v8 = v18[j];
          v9 = v18[j]->m_pInList;
          bSend[0] = 0;
          bUpdate = 0;
          dwDur = CPlayer::Emb_AlterDurPoint(v17, v9->m_nListCode, v8->m_byStorageIndex, v7, 0, 0);
          if ( dwDur )
            CPlayer::SendMsg_AdjustAmountInform(v17, 0, v18[j]->m_wSerial, dwDur);
          else
            CMgrAvatorItemHistory::consume_del_item(
              &CPlayer::s_MgrItemHistory,
              v17->m_ObjID.m_wIndex,
              v18[j],
              v17->m_szItemHistoryFileName);
        }
        else
        {
          v10 = v18[j];
          v11 = v18[j]->m_pInList;
          *(_QWORD *)bSend = "CPlayer::DeleteUseConsumeItem()";
          bUpdate = 1;
          CPlayer::Emb_DelStorage(v17, v11->m_nListCode, v10->m_byStorageIndex, 0, 1, "CPlayer::DeleteUseConsumeItem()");
          CMgrAvatorItemHistory::consume_del_item(
            &CPlayer::s_MgrItemHistory,
            v17->m_ObjID.m_wIndex,
            v18[j],
            v17->m_szItemHistoryFileName);
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
