/*
 * Function: j_??0?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140002838
 */

void __fastcall std::allocator<CUnmannedTraderRegistItemInfo>::allocator<CUnmannedTraderRegistItemInfo>(std::allocator<CUnmannedTraderRegistItemInfo> *this, std::allocator<CUnmannedTraderRegistItemInfo> *__formal)
{
  std::allocator<CUnmannedTraderRegistItemInfo>::allocator<CUnmannedTraderRegistItemInfo>(this, __formal);
}
