/*
 * Function: ?SendCancleInfomSender@CRaceBossMsgController@@IEAAXK@Z
 * Address: 0x1402A1610
 */

void __fastcall CRaceBossMsgController::SendCancleInfomSender(CRaceBossMsgController *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-88h]@1
  CPlayer *v5; // [sp+30h] [bp-58h]@4
  char szMsg; // [sp+44h] [bp-44h]@6
  char pbyType; // [sp+64h] [bp-24h]@6
  char v8; // [sp+65h] [bp-23h]@6

  v2 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = GetPtrPlayerFromSerial(&g_Player, 2532, dwSerial);
  if ( v5 )
  {
    if ( v5->m_bLive )
    {
      pbyType = 52;
      v8 = 3;
      CNetProcess::LoadSendMsg(unk_1414F2088, v5->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
    }
  }
}
