/*
 * Function: j_??D?$_Vector_iterator@VCUnmannedTraderGroupDivisionVersionInfo@@V?$allocator@VCUnmannedTraderGroupDivisionVersionInfo@@@std@@@std@@QEBAAEAVCUnmannedTraderGroupDivisionVersionInfo@@XZ
 * Address: 0x14000A132
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator*(std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo> > *this)
{
  return std::_Vector_iterator<CUnmannedTraderGroupDivisionVersionInfo,std::allocator<CUnmannedTraderGroupDivisionVersionInfo>>::operator*(this);
}
