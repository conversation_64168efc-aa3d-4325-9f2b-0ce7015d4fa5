/*
 * Function: ?SaveStateData@CHolyStoneSystemDataMgr@@SA_NAEAVCHolyStoneSaveData@@@Z
 * Address: 0x140286760
 */

char __fastcall CHolyStoneSystemDataMgr::SaveStateData(CHolyStoneSaveData *clsSaveDummy)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@8
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char Dest; // [sp+28h] [bp-90h]@4
  unsigned int j; // [sp+54h] [bp-64h]@4
  char KeyName; // [sp+68h] [bp-50h]@8
  unsigned __int64 v9; // [sp+A0h] [bp-18h]@4
  CHolyStoneSaveData *v10; // [sp+C0h] [bp+8h]@1

  v10 = clsSaveDummy;
  v1 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  _itoa(v10->m_nSceneCode, &Dest, 10);
  WritePrivateProfileStringA("HSK", "scene", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwPassTimeInScene, &Dest, 10);
  WritePrivateProfileStringA("HSK", "pass", &Dest, "..\\SystemSave\\ServerState.ini");
  for ( j = 0; (signed int)j < 3; ++j )
  {
    if ( g_Stone[j].m_bOper )
    {
      v3 = ((int (__fastcall *)(struct CHolyStone *))g_Stone[j].vfptr->GetHP)(&g_Stone[j]);
      _itoa(v3, &Dest, 10);
      sprintf(&KeyName, "%d S_hp", j);
      WritePrivateProfileStringA("HSK", &KeyName, &Dest, "..\\SystemSave\\ServerState.ini");
    }
  }
  _itoa(v10->m_nStartStoneHP, &Dest, 10);
  WritePrivateProfileStringA("HSK", "starthp", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_nHolyMasterRace, &Dest, 10);
  WritePrivateProfileStringA("HSK", "master", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_nDestroyStoneRace, &Dest, 10);
  WritePrivateProfileStringA("HSK", "destroyedrace", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwTerm[0], &Dest, 10);
  WritePrivateProfileStringA("HSK", "plustime", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwTerm[1], &Dest, 10);
  WritePrivateProfileStringA("HSK", "controlterm", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_byNumOfTime, &Dest, 10);
  WritePrivateProfileStringA("HSK", "numoftime", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwCumPlayerNum, &Dest, 10);
  WritePrivateProfileStringA("HSK", "cumplayernum", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwCumCount, &Dest, 10);
  WritePrivateProfileStringA("HSK", "cumcount", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_wStartYear, &Dest, 10);
  WritePrivateProfileStringA("HSK", "startyear", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_byStartMonth, &Dest, 10);
  WritePrivateProfileStringA("HSK", "startmonth", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_byStartDay, &Dest, 10);
  WritePrivateProfileStringA("HSK", "startday", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_byStartHour, &Dest, 10);
  WritePrivateProfileStringA("HSK", "starthour", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_byStartMin, &Dest, 10);
  WritePrivateProfileStringA("HSK", "startmin", &Dest, "..\\SystemSave\\ServerState.ini");
  sprintf(&Dest, "%u", v10->m_dwDestroyerSerial);
  WritePrivateProfileStringA("HSK", "destroyer", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_eDestroyerState, &Dest, 10);
  WritePrivateProfileStringA("HSK", "destroyerstate", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwOreRemainAmount, &Dest, 10);
  WritePrivateProfileStringA("HSK", "oreremain", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwOreTotalAmount, &Dest, 10);
  WritePrivateProfileStringA("HSK", "oretotal", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwDestroyerGuildSerial, &Dest, 10);
  WritePrivateProfileStringA("HSK", "destroyerguild", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_byOreTransferCount, &Dest, 10);
  WritePrivateProfileStringA("HSK", "oretransfercount", &Dest, "..\\SystemSave\\ServerState.ini");
  _itoa(v10->m_dwOreTransferAmount, &Dest, 10);
  WritePrivateProfileStringA("HSK", "oretransferamount", &Dest, "..\\SystemSave\\ServerState.ini");
  return 1;
}
