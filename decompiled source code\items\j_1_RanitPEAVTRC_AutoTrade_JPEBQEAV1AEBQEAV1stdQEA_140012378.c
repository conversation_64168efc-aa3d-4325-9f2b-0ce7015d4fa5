/*
 * Function: j_??1?$_<PERSON>t@PEAVTRC_AutoTrade@@_JPEBQEAV1@AEBQEAV1@@std@@QEAA@XZ
 * Address: 0x140012378
 */

void __fastcall std::_<PERSON>t<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::~_<PERSON><PERSON><TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &> *this)
{
  std::_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>::~_Ranit<TRC_AutoTrade *,__int64,TRC_AutoTrade * const *,TRC_AutoTrade * const &>(this);
}
