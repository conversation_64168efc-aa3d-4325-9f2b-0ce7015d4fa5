/*
 * Function: ?Loop@CPostSystemManager@@QEAAXXZ
 * Address: 0x140324910
 */

void __usercall CPostSystemManager::Loop(CPostSystemManager *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@11
  int v6; // eax@22
  __int64 v7; // [sp-20h] [bp-1AB8h]@1
  char v8; // [sp+10h] [bp-1A88h]@5
  int j; // [sp+14h] [bp-1A84h]@7
  int v10; // [sp+18h] [bp-1A80h]@6
  _qry_case_post_list_regi v11; // [sp+30h] [bp-1A68h]@7
  unsigned int pdwOutIndex; // [sp+1814h] [bp-284h]@10
  int *v13; // [sp+1828h] [bp-270h]@10
  _qry_case_post_serial_check v14; // [sp+1840h] [bp-258h]@14
  unsigned int v15; // [sp+1A44h] [bp-54h]@17
  int *v16; // [sp+1A58h] [bp-40h]@17
  __int64 _Time; // [sp+1A68h] [bp-30h]@23
  unsigned __int64 v18; // [sp+1A80h] [bp-18h]@4
  CPostSystemManager *v19; // [sp+1AA0h] [bp+8h]@1

  v19 = this;
  v2 = alloca(a2);
  v3 = &v7;
  for ( i = 1708i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pkDB )
  {
    v8 = 0;
    if ( CMyTimer::CountingTimer(&v19->m_tmrRegiTime) )
    {
      v10 = CNetIndexList::size(&v19->m_listRegist);
      if ( v10 > 0 )
      {
        _qry_case_post_list_regi::_qry_case_post_list_regi(&v11);
        for ( j = 0; j < v10 && j < 20; ++j )
        {
          CNetIndexList::PopNode_Front(&v19->m_listRegist, &pdwOutIndex);
          v13 = &v19->m_PostData[pdwOutIndex].m_nNumber;
          v11.List[j].dwIndex = pdwOutIndex;
          v11.List[j].bySendRace = *((_BYTE *)v13 + 6);
          v11.List[j].bySenderDgr = *((_BYTE *)v13 + 7);
          v11.List[j].dwSenderSerial = v13[2];
          _INVENKEY::operator=(&v11.List[j].key, (_INVENKEY *)v13 + 67);
          v11.List[j].dwDur = *((_QWORD *)v13 + 34);
          v11.List[j].dwUpt = v13[70];
          v11.List[j].lnUID = *((_QWORD *)v13 + 36);
          v11.List[j].dwGold = v13[74];
          strcpy_s(v11.List[j].wszSendName, 0x11ui64, (const char *)v13 + 12);
          strcpy_s(v11.List[j].wszRecvName, 0x11ui64, (const char *)v13 + 29);
          strcpy_s(v11.List[j].wszTitle, 0x15ui64, (const char *)v13 + 46);
          strcpy_s(v11.List[j].wszContent, 0xC9ui64, (const char *)v13 + 67);
          ++v11.dwCount;
        }
        v5 = _qry_case_post_list_regi::size(&v11);
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 77, (char *)&v11, v5);
      }
    }
    v8 = 0;
    if ( CMyTimer::CountingTimer(&v19->m_tmrProcTime) )
    {
      v10 = CNetIndexList::size(&v19->m_listProc);
      if ( v10 > 0 )
      {
        _qry_case_post_serial_check::_qry_case_post_serial_check(&v14);
        for ( j = 0; j < v10 && j < 15; ++j )
        {
          CNetIndexList::PopNode_Front(&v19->m_listProc, &v15);
          v16 = &v19->m_PostData[v15].m_nNumber;
          v14.List[j].dwIndex = v15;
          v14.List[j].bySenderDgr = *((_BYTE *)v16 + 7);
          v14.List[j].bySenderRace = *((_BYTE *)v16 + 6);
          strcpy_s(v14.List[j].wszRecvName, 0x11ui64, (const char *)v16 + 29);
          if ( _INVENKEY::IsFilled((_INVENKEY *)v16 + 67) || v16[74] || (signed int)*((_BYTE *)v16 + 7) >= 2 )
            v14.List[j].bCheckDgr = 1;
          ++v14.dwCount;
        }
        v19->m_nPostProcCountPerDay += v14.dwCount;
        v6 = _qry_case_post_serial_check::size(&v14);
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -127, (char *)&v14, v6);
      }
    }
    time_14(&_Time);
    if ( _Time > v19->m_tNextWrite )
    {
      CPostSystemManager::Log(
        v19,
        "Post Use Count > Total Use: %d, Return Post: %d\r\n",
        v19->m_nPostProcCountPerDay,
        v19->m_nPostReturnCountPerDay);
      v19->m_nPostProcCountPerDay = 0;
      v19->m_nPostReturnCountPerDay = 0;
      CPostSystemManager::SetNextWriteTime(v19);
    }
  }
}
