/*
 * Function: ?init@_REGED@@QEAAXXZ
 * Address: 0x1400755A0
 */

void __fastcall _REGED::init(_REGED *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _REGED *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _REGED_AVATOR_DB::Init((_REGED_AVATOR_DB *)v5->m_wszAvatorName);
  for ( j = 0; j < 8; ++j )
  {
    v5->m_dwFixEquipLv[j] = 0xFFFFFFF;
    v5->m_dwItemETSerial[j] = 0;
    v5->m_lnUID[j] = 0i64;
    v5->m_dwET[j] = -1;
    v5->m_byCsMethod[j] = 0;
    v5->m_dwLendRegdTime[j] = -1;
  }
}
