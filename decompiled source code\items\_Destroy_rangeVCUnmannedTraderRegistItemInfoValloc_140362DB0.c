/*
 * Function: ??$_Destroy_range@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@0AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@0@U_Nonscalar_ptr_iterator_tag@0@@Z
 * Address: 0x140362DB0
 */

void __fastcall std::_Destroy_range<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, std::allocator<CUnmannedTraderRegistItemInfo> *_Al, std::_Nonscalar_ptr_iterator_tag __formal)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *_Ptr; // [sp+30h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *v8; // [sp+38h] [bp+10h]@1
  std::allocator<CUnmannedTraderRegistItemInfo> *v9; // [sp+40h] [bp+18h]@1

  v9 = _Al;
  v8 = _Last;
  _Ptr = _First;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  while ( _Ptr != v8 )
  {
    std::allocator<CUnmannedTraderRegistItemInfo>::destroy(v9, _Ptr);
    ++_Ptr;
  }
}
