/*
 * Function: ?InitMgr@CQuestMgr@@QEAAXPEAVCPlayer@@PEAU_QUEST_DB_BASE@@@Z
 * Address: 0x140287820
 */

void __fastcall CQuestMgr::InitMgr(CQuestMgr *this, CPlayer *pMaster, _QUEST_DB_BASE *pQuestData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CQuestMgr *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_pMaster = pMaster;
  v7->m_pQuestData = pQuestData;
  _happen_event_cont::init(&v7->m_LastHappenEvent);
  v7->m_dwOldTimeoutChecktime = timeGetTime();
  for ( j = 0; j < 3; ++j )
    _happen_event_cont::init(&v7->m_pTempHappenEvent[j]);
}
