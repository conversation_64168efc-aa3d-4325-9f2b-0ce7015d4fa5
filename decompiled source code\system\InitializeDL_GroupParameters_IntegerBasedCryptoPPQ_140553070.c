/*
 * Function: ?Initialize@DL_GroupParameters_IntegerBased@CryptoPP@@QEAAXAEBV12@@Z
 * Address: 0x140553070
 */

void __fastcall CryptoPP::DL_GroupParameters_IntegerBased::Initialize(CryptoPP::DL_GroupParameters_IntegerBased *this, const struct CryptoPP::DL_GroupParameters_IntegerBased *a2)
{
  struct CryptoPP::Integer *v2; // rax@1
  struct CryptoPP::Integer *v3; // ST28_8@1
  struct CryptoPP::Integer *v4; // rax@1
  struct CryptoPP::Integer *v5; // ST30_8@1
  const struct CryptoPP::Integer *v6; // rax@1
  CryptoPP::DL_GroupParameters_IntegerBased *v7; // [sp+50h] [bp+8h]@1
  const struct CryptoPP::DL_GroupParameters_IntegerBased *v8; // [sp+58h] [bp+10h]@1

  v8 = a2;
  v7 = this;
  LODWORD(v2) = ((int (__fastcall *)(_QWORD))a2->vfptr[1].__vecDelDtor)(&a2->vfptr);
  v3 = v2;
  LODWORD(v4) = ((int (__fastcall *)(signed __int64))v8->vfptr[8].__vecDelDtor)((signed __int64)&v8->vfptr);
  v5 = v4;
  LODWORD(v6) = ((int (__fastcall *)(const struct CryptoPP::DL_GroupParameters_IntegerBased *))v8->vfptr[1].__vecDelDtor)(v8);
  CryptoPP::DL_GroupParameters_IntegerBased::Initialize(v7, v6, v5, v3);
}
