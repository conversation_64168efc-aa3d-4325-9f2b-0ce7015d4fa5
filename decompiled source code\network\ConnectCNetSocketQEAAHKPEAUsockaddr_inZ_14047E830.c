/*
 * Function: ?Connect@CNetSocket@@QEAAHKPEAUsockaddr_in@@@Z
 * Address: 0x14047E830
 */

int __fastcall CNetSocket::Connect(CNetSocket *this, unsigned int n, sockaddr_in *pAddr)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  void **v6; // rcx@12
  __int64 v7; // [sp+0h] [bp-48h]@1
  int namelen; // [sp+20h] [bp-28h]@7
  __int64 v9; // [sp+28h] [bp-20h]@12
  _socket *v10; // [sp+30h] [bp-18h]@12
  CNetSocket *v11; // [sp+50h] [bp+8h]@1
  unsigned int v12; // [sp+58h] [bp+10h]@1
  struct sockaddr *name; // [sp+60h] [bp+18h]@1

  name = (struct sockaddr *)pAddr;
  v12 = n;
  v11 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v11->m_Socket[n].m_bAccept )
  {
    result = -1;
  }
  else
  {
    if ( v11->m_SockType.m_byProtocolID )
    {
      if ( v11->m_SockType.m_byProtocolID == 1 )
      {
        v11->m_Socket[n].m_Socket = socket(6, 5, 1256);
        namelen = 14;
      }
    }
    else
    {
      v11->m_Socket[n].m_Socket = socket(2, 1, 0);
      namelen = 16;
    }
    if ( connect(v11->m_Socket[v12].m_Socket, name, namelen) == -1 )
    {
      closesocket(v11->m_Socket[v12].m_Socket);
      result = WSAGetLastError();
    }
    else
    {
      v6 = v11->m_Event;
      v9 = 160i64 * v12;
      v10 = v11->m_Socket;
      if ( WSAEventSelect(v10[v12].m_Socket, v6[v12], 49) == -1 )
        result = -3;
      else
        result = 0;
    }
  }
  return result;
}
