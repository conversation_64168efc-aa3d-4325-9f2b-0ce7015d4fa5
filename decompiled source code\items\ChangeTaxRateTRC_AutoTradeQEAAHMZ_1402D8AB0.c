/*
 * Function: ?ChangeTaxRate@TRC_AutoTrade@@QEAAHM@Z
 * Address: 0x1402D8AB0
 */

signed __int64 __usercall TRC_AutoTrade::ChangeTaxRate@<rax>(TRC_AutoTrade *this@<rcx>, float fNewTaxRate@<xmm1>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CPvpUserAndGuildRankingSystem *v6; // rax@8
  unsigned int v7; // eax@8
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@7
  CPlayer *v10; // [sp+28h] [bp-10h]@8
  TRC_AutoTrade *v11; // [sp+40h] [bp+8h]@1

  v11 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( ControllerTaxRate::checkLimitTaxRate(&v11->m_Controller, fNewTaxRate) )
  {
    ControllerTaxRate::getCurTaxRate(&v11->m_Controller);
    if ( *(float *)&a3 != fNewTaxRate )
    {
      ControllerTaxRate::getCurTaxRate(&v11->m_Controller);
      v9 = SLODWORD(a3);
      ControllerTaxRate::setCurTaxRate(&v11->m_Controller, fNewTaxRate);
      a3 = fNewTaxRate;
      CLogFile::Write(&v11->m_serviceLog, "[ChangeTaxRate]:Current(%.2f) ==> Next(%.2f)", v9, fNewTaxRate);
    }
    TRC_AutoTrade::PushDQSData(v11);
    v11->m_bChangeTaxRate = 0;
    TRC_AutoTrade::get_taxrate(v11);
    CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateTaxRate(
      &stru_1799C9AF8,
      v11->m_byRace,
      (signed int)ffloor(*(float *)&a3 * 100.0));
    CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, v11->m_byRace);
    v6 = CPvpUserAndGuildRankingSystem::Instance();
    v7 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v6, v11->m_byRace, 5);
    v10 = GetPtrPlayerFromSerial(&g_Player, 2532, v7);
    if ( v10 )
      TRC_AutoTrade::SendMsg_PatriarchTaxRate(v11, v10->m_ObjID.m_wIndex);
    result = 0i64;
  }
  else
  {
    CLogFile::Write(&v11->m_serviceLog, "[ChangeTaxRate]:Invalid value :: (%.2f)", fNewTaxRate);
    result = 3i64;
  }
  return result;
}
