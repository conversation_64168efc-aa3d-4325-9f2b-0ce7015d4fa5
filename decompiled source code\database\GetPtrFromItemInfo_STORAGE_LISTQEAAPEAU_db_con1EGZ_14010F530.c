/*
 * Function: ?GetPtrFromItemInfo@_STORAGE_LIST@@QEAAPEAU_db_con@1@EG@Z
 * Address: 0x14010F530
 */

_STORAGE_LIST::_db_con *__fastcall _STORAGE_LIST::GetPtrFromItemInfo(_STORAGE_LIST *this, char byTableCode, unsigned __int16 ItemIndex)
{
  int *v3; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  _STORAGE_LIST *v7; // [sp+20h] [bp+8h]@1

  v7 = this;
  v3 = &j;
  for ( i = 4i64; i; --i )
  {
    *v3 = -858993460;
    ++v3;
  }
  for ( j = 0; j < v7->m_nUsedNum; ++j )
  {
    if ( v7->m_pStorageList[j].m_bLoad
      && v7->m_pStorageList[j].m_byTableCode == (unsigned __int8)byTableCode
      && v7->m_pStorageList[j].m_wItemIndex == ItemIndex )
    {
      v7->m_pStorageList[j].m_pInList = v7;
      v7->m_pStorageList[j].m_byStorageIndex = j;
      return &v7->m_pStorageList[j];
    }
  }
  return 0i64;
}
