/*
 * Function: ?GetStateAtPtr@?$CArrayEx@VCLuaLooting_Novus_Item@@U_State@1@@US@@QEAAPEAU_State@CLuaLooting_Novus_Item@@K@Z
 * Address: 0x140405750
 */

CLuaLooting_Novus_Item::_State *__fastcall US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State>::GetStateAtPtr(US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  US::CArrayEx<CLuaLooting_Novus_Item,CLuaLooting_Novus_Item::_State> *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return US::CArray<CLuaLooting_Novus_Item::_State>::GetAtPtr(&v6->m_StateAr, dwIndex);
}
