/*
 * Function: ?Initialize@cStaticMember_Player@@QEAA_NXZ
 * Address: 0x14010E590
 */

bool __fastcall cStaticMember_Player::Initialize(cStaticMember_Player *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  cStaticMember_Player *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  return cStaticMember_Player::loadLimitExpData(v5) != 0;
}
