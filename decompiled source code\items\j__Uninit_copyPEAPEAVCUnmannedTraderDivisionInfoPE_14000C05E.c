/*
 * Function: j_??$_Uninit_copy@PEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@YAPEAPEAVCUnmannedTraderDivisionInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCUnmannedTraderDivisionInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C05E
 */

CUnmannedTraderDivisionInfo **__fastcall std::_Uninit_copy<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>>(CUnmannedTraderDivisionInfo **_First, CUnmannedTraderDivisionInfo **_Last, CUnmannedTraderDivisionInfo **_Dest, std::allocator<CUnmannedTraderDivisionInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CUnmannedTraderDivisionInfo * *,CUnmannedTraderDivisionInfo * *,std::allocator<CUnmannedTraderDivisionInfo *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
