/*
 * Function: ?SetDamage@CHolyStone@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x140137590
 */

__int64 __fastcall CHolyStone::SetDamage(CHolyStone *this, int nDam, CCharacter *pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v11; // [sp+0h] [bp-38h]@1
  CCharacter *v12; // [sp+20h] [bp-18h]@6
  int v13; // [sp+28h] [bp-10h]@17
  CHolyStone *v14; // [sp+40h] [bp+8h]@1
  int v15; // [sp+48h] [bp+10h]@1
  CCharacter *pAtter; // [sp+50h] [bp+18h]@1

  pAtter = pDst;
  v15 = nDam;
  v14 = this;
  v8 = &v11;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) == 1 )
  {
    v12 = 0i64;
    if ( !pAtter->m_ObjID.m_byID )
      v12 = pAtter;
    if ( pAtter->m_ObjID.m_byID == 3 )
      v12 = *(CCharacter **)&pAtter[1].m_bLive;
    if ( v12 )
    {
      if ( CMainThread::IsReleaseServiceMode(&g_Main) && BYTE4(v12[1].vfptr) )
      {
        result = v14->m_nHP;
      }
      else
      {
        if ( v15 > 1 )
        {
          if ( v14->m_nHP - v15 <= 0 )
            v13 = 0;
          else
            v13 = v14->m_nHP - v15;
          v14->m_nHP = v13;
        }
        if ( !v14->m_nHP )
          CHolyStone::Destroy(v14, 0, pAtter);
        result = v14->m_nHP;
      }
    }
    else
    {
      result = v14->m_nHP;
    }
  }
  else
  {
    result = v14->m_nHP;
  }
  return result;
}
