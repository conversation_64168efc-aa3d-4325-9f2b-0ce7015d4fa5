/*
 * Function: ?ReceiveD<PERSON><PERSON><PERSON><PERSON><PERSON>@CHolyStoneSystem@@QEAAXPEAVCCharacter@@@Z
 * Address: 0x14027CD30
 */

void __fastcall CHolyStoneSystem::ReceiveDestroyKeeper(CHolyStoneSystem *this, CCharacter *p<PERSON>haracter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  char v5; // al@11
  char v6; // al@14
  COreAmountMgr *v7; // rax@15
  COreAmountMgr *v8; // rax@15
  COreAmountMgr *v9; // rax@15
  COreAmountMgr *v10; // rax@15
  __int64 v11; // [sp+0h] [bp-58h]@1
  CPlayer *v12; // [sp+30h] [bp-28h]@8
  CPlayer *v13; // [sp+38h] [bp-20h]@11
  CPlayer *v14; // [sp+40h] [bp-18h]@13
  CHolyStoneSystem *v15; // [sp+60h] [bp+8h]@1
  CPlayer *pPlayer; // [sp+68h] [bp+10h]@1

  pPlayer = (CPlayer *)pCharacter;
  v15 = this;
  v2 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CHolyStoneSystem::GetSceneCode(v15) == 3 )
  {
    v4 = CHolyStoneSystem::GetNumOfTime(v15);
    CHolyStoneSystem::SetScene(v15, v4, 5, 0, 3);
    if ( pPlayer->m_ObjID.m_byID )
    {
      if ( pPlayer->m_ObjID.m_byID == 3 )
      {
        v12 = pPlayer;
        if ( *(_QWORD *)&pPlayer->m_bBaseDownload )
          CHolyStoneSystem::SendMsg_to_webagent_about_last_attacker_for_keeper(
            v15,
            *(CPlayer **)&v12->m_bBaseDownload,
            1);
      }
    }
    else
    {
      CHolyStoneSystem::SendMsg_to_webagent_about_last_attacker_for_keeper(v15, pPlayer, 0);
    }
    if ( pPlayer->m_ObjID.m_byID )
    {
      if ( pPlayer->m_ObjID.m_byID == 3 )
      {
        v14 = pPlayer;
        if ( *(_QWORD *)&pPlayer->m_bBaseDownload )
        {
          v6 = CPlayerDB::GetRaceCode((CPlayerDB *)(*(_QWORD *)&v14->m_bBaseDownload + 1952i64));
          CHolyStoneSystem::SetKeeperDestroyRace(v15, v6);
        }
      }
    }
    else
    {
      v13 = pPlayer;
      v5 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
      CHolyStoneSystem::SetKeeperDestroyRace(v15, v5);
    }
    v7 = COreAmountMgr::Instance();
    COreAmountMgr::InitRemainOreAmount(v7, 0xFFFFFFFF, 0xFFFFFFFF);
    v8 = COreAmountMgr::Instance();
    COreAmountMgr::InsertOreLog(v8, 1);
    v9 = COreAmountMgr::Instance();
    COreAmountMgr::IncreaseOreAmount(v9);
    v10 = COreAmountMgr::Instance();
    COreAmountMgr::IncreaseOreCount(v10);
  }
}
