/*
 * Function: ?CheckTicket_Pass@CTransportShip@@QEAAXPEAVCPlayer@@H@Z
 * Address: 0x140264170
 */

void __fastcall CTransportShip::CheckTicket_Pass(CTransportShip *this, CPlayer *pPtr, int nPortalIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  _portal_dummy *v6; // [sp+20h] [bp-58h]@4
  CMapData *v7; // [sp+28h] [bp-50h]@6
  _portal_dummy *v8; // [sp+30h] [bp-48h]@7
  float pNewPos; // [sp+48h] [bp-30h]@8
  CTransportShip *v10; // [sp+80h] [bp+8h]@1
  CPlayer *v11; // [sp+88h] [bp+10h]@1

  v11 = pPtr;
  v10 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = CMapData::GetPortal(v10->m_pLinkShipMap, nPortalIndex);
  if ( v6 )
  {
    if ( v6->m_pPortalRec )
    {
      v7 = CMapOperation::GetMap(&g_MapOper, v6->m_pPortalRec->m_strLinkMapCode);
      if ( v7 )
      {
        v8 = CMapData::GetPortal(v7, v6->m_pPortalRec->m_strLinkPortalCode);
        if ( v8 )
        {
          if ( !CMapData::GetRandPosInDummy(v7, v8->m_pDumPos, &pNewPos, 1) )
            memcpy_0(&pNewPos, v8->m_pDumPos->m_fCenterPos, 0xCui64);
          if ( v11->m_pUserDB )
            CUserDB::Update_Map(v11->m_pUserDB, v7->m_pMapSet->m_dwIndex, &pNewPos);
        }
      }
    }
  }
}
