/*
 * Function: ?Add_PvpPoint@CRFWorldDatabase@@QEAA_NKKK@Z
 * Address: 0x1404987E0
 */

bool __fastcall CRFWorldDatabase::Add_PvpPoint(CRFWorldDatabase *this, unsigned int dwSerial, unsigned int dwPoint, unsigned int dwCashBag)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-168h]@1
  unsigned int v8; // [sp+20h] [bp-148h]@4
  char Dest; // [sp+40h] [bp-128h]@4
  char v10; // [sp+41h] [bp-127h]@4
  unsigned __int64 v11; // [sp+150h] [bp-18h]@4
  CRFWorldDatabase *v12; // [sp+170h] [bp+8h]@1

  v12 = this;
  v4 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  Dest = 0;
  memset(&v10, 0, 0xFFui64);
  v8 = dwCashBag;
  sprintf(&Dest, "{ CALL pAdd_PvpPoint( %d, %d, %d ) }", dwSerial, dwPoint);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v12->vfptr, &Dest, 1);
}
