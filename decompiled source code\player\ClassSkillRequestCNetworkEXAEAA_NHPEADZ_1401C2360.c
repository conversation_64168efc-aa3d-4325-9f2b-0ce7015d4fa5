/*
 * Function: ?ClassSkillRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C2360
 */

char __fastcall CNetworkEX::ClassSkillRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  int v7; // eax@10
  char *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-48h]@1
  char *v10; // [sp+20h] [bp-28h]@4
  CPlayer *v11; // [sp+28h] [bp-20h]@4
  int v12; // [sp+30h] [bp-18h]@10
  CNetworkEX *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v3 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = pBuf;
  v11 = &g_Player + n;
  if ( !v11->m_bOper || v11->m_pmTrd.bDTradeMode || v11->m_bCorpse )
  {
    result = 1;
  }
  else if ( CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)v10[2], *(_WORD *)(v10 + 3)) )
  {
    v12 = *(_WORD *)v10;
    v7 = CRecordData::GetRecordNum(&stru_1799C8410 + 2);
    if ( v12 < v7 )
    {
      CPlayer::pc_ClassSkillRequest(v11, *(_WORD *)v10, (_CHRID *)(v10 + 2), (unsigned __int16 *)v10 + 5);
      result = 1;
    }
    else
    {
      v8 = CPlayerDB::GetCharNameA(&v11->m_Param);
      CLogFile::Write(
        &v13->m_LogFile,
        "odd.. %s: ClassSkillRequest()..  if(pRecv->wSkillIndex >= g_Main.m_tblEffectData[effect_code_class].GetRecordNum())",
        v8);
      result = 0;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v11->m_Param);
    CLogFile::Write(
      &v13->m_LogFile,
      "odd.. %s: ClassSkillRequest()..  if(!g_Main.GetObject(obj_kind_char, pRecv->idDst.byID, pRecv->idDst.wIndex))",
      v6);
    result = 0;
  }
  return result;
}
