/*
 * Function: ?PvpRankListRequest@CPvpUserAndGuildRankingSystem@@QEAAXGEEE@Z
 * Address: 0x1401D9410
 */

void __fastcall CPvpUserAndGuildRankingSystem::PvpRankListRequest(CPvpUserAndGuildRankingSystem *this, unsigned __int16 wIndex, char byRace, char byVersion, char byPage)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-38h]@1
  CPvpUserAndGuildRankingSystem *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v5 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  CUserRankingProcess::PvpRankListRequest(&v8->m_kUserRankingProcess, wIndex, byRace, byVersion, byPage);
}
