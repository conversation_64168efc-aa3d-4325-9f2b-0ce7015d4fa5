#pragma once

#include "MoneySupplyData.h"
#include "EconomyTypes.h"
#include <memory>
#include <mutex>
#include <chrono>
#include <string>

namespace NexusProtection::Economy {

    /**
     * @brief Money Supply Manager - Singleton class for managing economic statistics
     * 
     * This class tracks and manages all economic data including:
     * - Buy/Sell transactions
     * - Quest and gate rewards
     * - Guild money operations
     * - Fee collections
     * - Unit purchases
     * 
     * The manager periodically sends data to web services for analysis.
     */
    class CMoneySupplyMgr {
    public:
        // Singleton access
        static CMoneySupplyMgr& Instance();
        static void DestroyInstance();

        // Core lifecycle
        void Initialize();
        void LoopMoneySupply();

        // Transaction tracking methods
        void UpdateSellData(RaceType race, int32_t level, const std::string& className, uint32_t amount);
        void UpdateBuyData(RaceType race, int32_t level, const std::string& className, uint32_t amount);
        void UpdateBuyUnitData(int32_t level, uint32_t amount);
        void UpdateFeeMoneyData(RaceType race, int32_t level, uint32_t amount);
        void UpdateQuestRewardMoneyData(RaceType race, int32_t level, const std::string& className, uint32_t amount);
        void UpdateGateRewardMoneyData(RaceType race, int32_t level, const std::string& className, uint32_t amount);
        void UpdateHonorGuildMoneyData(TradeType tradeType, RaceType race, uint32_t amount);

        // Data access
        const MoneySupplyData& GetCurrentData() const noexcept { return m_currentData; }
        const MoneySupplyData& GetSendData() const noexcept { return m_sendData; }

        // Timing information
        std::chrono::steady_clock::time_point GetLastSendTime() const noexcept { return m_lastSendTime; }
        std::chrono::steady_clock::time_point GetSystemStartTime() const noexcept { return m_systemStartTime; }

        // Legacy C interface compatibility
        void UpdateSellData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateBuyData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateBuyUnitData(int nLv, unsigned int nAmount);
        void UpdateFeeMoneyData(char byRace, int nLv, unsigned int nAmount);
        void UpdateQuestRewardMoneyData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateGateRewardMoneyData(char byRace, int nLv, char* szClass, unsigned int nAmount);
        void UpdateHonorGuildMoneyData(char byTradeType, char byRace, unsigned int nAmount);

    private:
        // Private constructor for singleton
        CMoneySupplyMgr();
        ~CMoneySupplyMgr() = default;

        // Prevent copying
        CMoneySupplyMgr(const CMoneySupplyMgr&) = delete;
        CMoneySupplyMgr& operator=(const CMoneySupplyMgr&) = delete;

        // Internal methods
        void SendMoneySupplyDataToWeb(const MoneySupplyData& data);
        void UpdateTradeStatistics(size_t tradeIndex, RaceType race, int32_t level, const std::string& className);
        RaceType ConvertLegacyRace(char byRace) const noexcept;
        TradeType ConvertLegacyTradeType(char byTradeType) const noexcept;

        // Member variables
        static std::unique_ptr<CMoneySupplyMgr> s_instance;
        static std::mutex s_instanceMutex;

        MoneySupplyData m_currentData;
        MoneySupplyData m_sendData;
        
        std::chrono::steady_clock::time_point m_lastSendTime;
        std::chrono::steady_clock::time_point m_systemStartTime;
        
        mutable std::mutex m_dataMutex;

        // Constants
        static constexpr std::chrono::milliseconds SEND_INTERVAL{60000}; // 60 seconds
    };

    // Legacy C interface
    extern "C" {
        // Legacy structure for compatibility
        struct CMoneySupplyMgrVtbl;
        
        struct CMoneySupplyMgr_Legacy {
            CMoneySupplyMgrVtbl* vfptr;
            _MONEY_SUPPLY_DATA m_MS_data;
            _MONEY_SUPPLY_DATA m_MS_Senddata;
            uint32_t m_dwLastSendTime;
            uint32_t m_dwSystemOperStartTime;
        };

        // Legacy function declarations
        CMoneySupplyMgr_Legacy* CMoneySupplyMgr_Instance();
        void CMoneySupplyMgr_Initialize(CMoneySupplyMgr_Legacy* mgr);
        void CMoneySupplyMgr_LoopMoneySupply(CMoneySupplyMgr_Legacy* mgr);
        void CMoneySupplyMgr_UpdateSellData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount);
        void CMoneySupplyMgr_UpdateBuyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount);
        void CMoneySupplyMgr_UpdateBuyUnitData(CMoneySupplyMgr_Legacy* mgr, int nLv, unsigned int nAmount);
        void CMoneySupplyMgr_UpdateFeeMoneyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, unsigned int nAmount);
        void CMoneySupplyMgr_UpdateQuestRewardMoneyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount);
        void CMoneySupplyMgr_UpdateGateRewardMoneyData(CMoneySupplyMgr_Legacy* mgr, char byRace, int nLv, char* szClass, unsigned int nAmount);
        void CMoneySupplyMgr_UpdateHonorGuildMoneyData(CMoneySupplyMgr_Legacy* mgr, char byTradeType, char byRace, unsigned int nAmount);
        void CMoneySupplyMgr_SendMsg_MoneySupplyDataToWeb(CMoneySupplyMgr_Legacy* mgr, _MONEY_SUPPLY_DATA* pMSData);
    }

} // namespace NexusProtection::Economy

// Global legacy compatibility
extern NexusProtection::Economy::CMoneySupplyMgr* g_pMoneySupplyMgr;
