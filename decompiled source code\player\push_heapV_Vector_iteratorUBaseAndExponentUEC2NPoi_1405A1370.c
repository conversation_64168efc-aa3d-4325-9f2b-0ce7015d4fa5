/*
 * Function: ??$push_heap@V?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@0@Z
 * Address: 0x1405A1370
 */

int __fastcall std::push_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@2
  __int64 v3; // rax@2
  __int64 v4; // rax@2
  __int64 v5; // rax@2
  char v7; // [sp+20h] [bp-C8h]@2
  char *v8; // [sp+38h] [bp-B0h]@2
  char v9; // [sp+40h] [bp-A8h]@2
  char *v10; // [sp+58h] [bp-90h]@2
  char v11; // [sp+60h] [bp-88h]@2
  char *v12; // [sp+78h] [bp-70h]@2
  char v13; // [sp+80h] [bp-68h]@2
  char *v14; // [sp+98h] [bp-50h]@2
  __int64 v15; // [sp+A0h] [bp-48h]@1
  __int64 v16; // [sp+A8h] [bp-40h]@2
  __int64 v17; // [sp+B0h] [bp-38h]@2
  __int64 v18; // [sp+B8h] [bp-30h]@2
  __int64 v19; // [sp+C0h] [bp-28h]@2
  __int64 v20; // [sp+C8h] [bp-20h]@2
  __int64 v21; // [sp+D0h] [bp-18h]@2
  __int64 v22; // [sp+D8h] [bp-10h]@2
  __int64 v23; // [sp+F8h] [bp+10h]@1

  v23 = a2;
  v15 = -2i64;
  if ( (unsigned __int8)std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator!=() )
  {
    v8 = &v7;
    v10 = &v9;
    v12 = &v11;
    v14 = &v13;
    v2 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)&v7);
    v16 = v2;
    LODWORD(v3) = std::_Val_type<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(v2);
    v17 = v3;
    v4 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v10);
    v18 = v4;
    LODWORD(v5) = std::_Dist_type<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>>(v4);
    v19 = v5;
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator--(v23);
    v20 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v12);
    v21 = v20;
    v22 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>((__int64)v14);
    std::_Push_heap_0<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(
      v22,
      v21,
      v19,
      v17);
  }
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>();
}
