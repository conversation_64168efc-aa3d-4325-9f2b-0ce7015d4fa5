/*
 * Function: j_??$_Fill_n@PEAPEAVCUnmannedTraderClassInfo@@_KPEAV1@@std@@YAXPEAPEAVCUnmannedTraderClassInfo@@_KAEBQEAV1@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006E88
 */

void __fastcall std::_Fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *>(CUnmannedTraderClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderClassInfo *const *_Val, std::_Range_checked_iterator_tag __formal)
{
  std::_Fill_n<CUnmannedTraderClassInfo * *,unsigned __int64,CUnmannedTraderClassInfo *>(_First, _Count, _Val, __formal);
}
