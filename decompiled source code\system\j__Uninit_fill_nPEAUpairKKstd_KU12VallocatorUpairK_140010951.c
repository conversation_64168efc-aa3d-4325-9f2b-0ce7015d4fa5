/*
 * Function: j_??$_Uninit_fill_n@PEAU?$pair@KK@std@@_KU12@V?$allocator@U?$pair@KK@std@@@2@@std@@YAXPEAU?$pair@KK@0@_KAEBU10@AEAV?$allocator@U?$pair@KK@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140010951
 */

void __fastcall std::_Uninit_fill_n<std::pair<unsigned long,unsigned long> *,unsigned __int64,std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(std::pair<unsigned long,unsigned long> *_First, unsigned __int64 _Count, std::pair<unsigned long,unsigned long> *_Val, std::allocator<std::pair<unsigned long,unsigned long> > *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<std::pair<unsigned long,unsigned long> *,unsigned __int64,std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
