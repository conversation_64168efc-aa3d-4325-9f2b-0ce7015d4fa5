/*
 * Function: ?UpdateNotifyHolyStoneHPToRaceBoss@CHolyStoneSystem@@IEAAXXZ
 * Address: 0x14027CC80
 */

void __fastcall CHolyStoneSystem::UpdateNotifyHolyStoneHPToRaceBoss(CHolyStoneSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
  {
    if ( g_Stone[j].m_bLive && CHolyStone::IsChangedHP(&g_Stone[j], 1u) )
    {
      CHolyStoneSystem::SendHolyStoneHPToRaceBoss(v5);
      return;
    }
  }
}
