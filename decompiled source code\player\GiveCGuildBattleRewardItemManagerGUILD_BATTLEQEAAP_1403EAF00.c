/*
 * Function: ?Give@CGuildBattleRewardItemManager@GUILD_BATTLE@@QEAAPEBVCGuildBattleRewardItem@2@PEAVCPlayer@@@Z
 * Address: 0x1403EAF00
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall GUILD_BATTLE::CGuildBattleRewardItemManager::Give(GUILD_BATTLE::CGuildBattleRewardItemManager *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v4; // rax@4
  GUILD_BATTLE::CGuildBattleRewardItem *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned __int64 v8; // [sp+20h] [bp-18h]@4
  unsigned __int64 v9; // [sp+28h] [bp-10h]@4
  GUILD_BATTLE::CGuildBattleRewardItemManager *v10; // [sp+40h] [bp+8h]@1
  CPlayer *pkPlayera; // [sp+48h] [bp+10h]@1

  pkPlayera = pkPlayer;
  v10 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = rand();
  v4 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::size(&v10->m_kItem);
  v9 = v4;
  v5 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::operator[](
         &v10->m_kItem,
         v8 % v4);
  return GUILD_BATTLE::CGuildBattleRewardItem::Give(v5, pkPlayera);
}
