/*
 * Function: ?UpdateLookAtPos@CMonster@@QEAAXXZ
 * Address: 0x140148090
 */

void __fastcall CMonster::UpdateLookAtPos(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // xmm0_4@6
  float v4; // xmm0_4@6
  __int64 v5; // [sp+0h] [bp-58h]@1
  float v; // [sp+28h] [bp-30h]@6
  float v7; // [sp+2Ch] [bp-2Ch]@6
  float v8; // [sp+30h] [bp-28h]@6
  int v9; // [sp+44h] [bp-14h]@6
  CMonster *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v10->m_fCurPos[0] != v10->m_fOldPos[0] || v10->m_fCurPos[2] != v10->m_fOldPos[2] )
  {
    v = v10->m_fCurPos[0] - v10->m_fOldPos[0];
    v7 = v10->m_fCurPos[1] - v10->m_fOldPos[1];
    v8 = v10->m_fCurPos[2] - v10->m_fOldPos[2];
    *(float *)&v3 = v8;
    Normalize(&v);
    CMonster::GetVisualField(v10);
    v9 = v3;
    v = *(float *)&v3 * v;
    v7 = *(float *)&v3 * v7;
    v8 = *(float *)&v3 * v8;
    v10->m_fLookAtPos[0] = v10->m_fCurPos[0] + v;
    v10->m_fLookAtPos[1] = v10->m_fCurPos[1] + v7;
    v4 = v10->m_fCurPos[2] + v8;
    v10->m_fLookAtPos[2] = v4;
    GetYAngle(v10->m_fCurPos, v10->m_fLookAtPos);
    v10->m_fYAngle = v4;
  }
}
