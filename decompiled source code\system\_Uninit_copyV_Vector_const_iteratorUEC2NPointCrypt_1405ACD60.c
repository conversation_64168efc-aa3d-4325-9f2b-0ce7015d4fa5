/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@UEC2NPoint@CryptoPP@@V?$allocator@UEC2NPoint@CryptoPP@@@std@@@std@@PEAUEC2NPoint@CryptoPP@@V?$allocator@UEC2NPoint@CryptoPP@@@2@@std@@YAPEAUEC2NPoint@CryptoPP@@V?$_Vector_const_iterator@UEC2NPoint@CryptoPP@@V?$allocator@UEC2NPoint@CryptoPP@@@std@@@0@0PEAU12@AEAV?$allocator@UEC2NPoint@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405ACD60
 */

__int64 __fastcall std::_Uninit_copy<std::_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>,CryptoPP::EC2NPoint *,std::allocator<CryptoPP::EC2NPoint>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@3
  __int64 v6; // [sp+60h] [bp+8h]@1
  __int64 v7; // [sp+70h] [bp+18h]@1
  __int64 v8; // [sp+78h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a1;
  while ( std::_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator!=() )
  {
    LODWORD(v4) = std::_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator*(v6);
    std::allocator<CryptoPP::EC2NPoint>::construct(v8, v7, v4);
    v7 += 56i64;
    std::_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::operator++(v6);
  }
  std::_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::~_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>();
  std::_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>::~_Vector_const_iterator<CryptoPP::EC2NPoint,std::allocator<CryptoPP::EC2NPoint>>();
  return v7;
}
