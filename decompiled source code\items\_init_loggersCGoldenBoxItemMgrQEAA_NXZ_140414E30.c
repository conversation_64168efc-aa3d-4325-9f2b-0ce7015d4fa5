/*
 * Function: ?_init_loggers@CGoldenBoxItemMgr@@QEAA_NXZ
 * Address: 0x140414E30
 */

char __fastcall CGoldenBoxItemMgr::_init_loggers(CGoldenBoxItemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v3; // rax@4
  CAsyncLogger *v4; // rax@4
  unsigned int v5; // eax@4
  __int64 v7; // [sp+0h] [bp-168h]@1
  char _Dest[256]; // [sp+40h] [bp-128h]@4
  unsigned __int64 v9; // [sp+150h] [bp-18h]@4
  CGoldenBoxItemMgr *v10; // [sp+170h] [bp+8h]@1

  v10 = this;
  v1 = &v7;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = (unsigned __int64)&v7 ^ _security_cookie;
  v3 = CAsyncLogger::Instance();
  CAsyncLogger::Regist(
    v3,
    ALT_GETGOLDBAR_LOG,
    "..\\ZoneServerLog\\ServiceLog\\GoldBoxSystem",
    "GetGoldBarHistory",
    1,
    0x5265C00u);
  v4 = CAsyncLogger::Instance();
  CAsyncLogger::Regist(
    v4,
    ALT_GETEVENTCOUPON_LOG,
    "..\\ZoneServerLog\\ServiceLog\\GoldBoxSystem",
    "GetCouponHistory",
    1,
    0x5265C00u);
  CreateDirectoryA("..\\ZoneServerLog\\SystemLog\\GoldenAgeEvent", 0i64);
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  v5 = GetKorLocalTime();
  sprintf_s<256>((char (*)[256])_Dest, "..\\ZoneServerLog\\SystemLog\\GoldenAgeEvent\\%d.his", v5);
  CLogFile::SetWriteLogFile(&v10->_kLogger, _Dest, 1, 0, 1, 1);
  return 1;
}
