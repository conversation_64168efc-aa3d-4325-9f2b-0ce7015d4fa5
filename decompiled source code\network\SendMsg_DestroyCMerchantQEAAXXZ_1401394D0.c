/*
 * Function: ?SendMsg_Destroy@CMerchant@@QEAAXXZ
 * Address: 0x1401394D0
 */

void __fastcall CMerchant::SendMsg_Destroy(CMerchant *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  unsigned int v5; // [sp+36h] [bp-42h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CMerchant *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v3;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(_WORD *)szMsg = v8->m_ObjID.m_wIndex;
  v5 = v8->m_dwObjSerial;
  pbyType = 3;
  v7 = 25;
  CGameObject::CircleReport((CGameObject *)&v8->vfptr, &pbyType, szMsg, 6, 0);
}
