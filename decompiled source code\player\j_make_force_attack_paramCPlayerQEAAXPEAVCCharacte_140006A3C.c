/*
 * Function: j_?make_force_attack_param@CPlayer@@QEAAXPEAVCCharacter@@PEAU_force_fld@@PEAU_db_con@_STORAGE_LIST@@PEAMPEAU_attack_param@@2M@Z
 * Address: 0x140006A3C
 */

void __fastcall CPlayer::make_force_attack_param(CPlayer *this, CCharacter *pDst, _force_fld *pForceFld, _STORAGE_LIST::_db_con *pForceItem, float *pTar, _attack_param *pAP, _STORAGE_LIST::_db_con *pEffBulletItem, float fAddEffBtFc)
{
  CPlayer::make_force_attack_param(this, pDst, pForceFld, pForceItem, pTar, pAP, pEffBulletItem, fAddEffBtFc);
}
