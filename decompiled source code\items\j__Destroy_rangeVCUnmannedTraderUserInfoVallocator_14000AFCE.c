/*
 * Function: j_??$_Destroy_range@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@YAXPEAVCUnmannedTraderUserInfo@@0AEAV?$allocator@VCUnmannedTraderUserInfo@@@0@@Z
 * Address: 0x14000AFCE
 */

void __fastcall std::_Destroy_range<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, std::allocator<CUnmannedTraderUserInfo> *_Al)
{
  std::_Destroy_range<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(_First, _Last, _Al);
}
