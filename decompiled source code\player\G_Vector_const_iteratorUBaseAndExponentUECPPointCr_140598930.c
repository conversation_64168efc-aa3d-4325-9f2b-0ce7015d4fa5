/*
 * Function: ??G?$_Vector_const_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x140598930
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator-(__int64 a1, __int64 a2)
{
  return (*(_QWORD *)(a1 + 16) - *(_QWORD *)(a2 + 16)) >> 7;
}
