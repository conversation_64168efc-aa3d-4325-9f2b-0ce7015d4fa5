/*
 * Function: ?SetPotionActDelay@CPlayer@@QEAAXEKK@Z
 * Address: 0x1400A3920
 */

void __fastcall CPlayer::SetPotionActDelay(CPlayer *this, char byPotionClass, unsigned int dwCurrTime, unsigned int dwActDelay)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  unsigned int dwCurrTimea; // [sp+40h] [bp+18h]@1
  unsigned int dwActDelaya; // [sp+48h] [bp+20h]@1

  dwActDelaya = dwActDelay;
  dwCurrTimea = dwCurrTime;
  v8 = byPotionClass;
  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  CPotionParam::SetPotionActDelay(&v7->m_PotionParam, byPotionClass, dwCurrTime, dwActDelay);
  CUserDB::Update_PotionNextUseTime(v7->m_pUserDB, v8, dwActDelaya + dwCurrTimea);
}
