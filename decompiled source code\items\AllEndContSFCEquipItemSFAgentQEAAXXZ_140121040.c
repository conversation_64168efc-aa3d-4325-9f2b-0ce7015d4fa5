/*
 * Function: ?AllEndContSF@CEquipItemSFAgent@@QEAAXXZ
 * Address: 0x140121040
 */

void __fastcall CEquipItemSFAgent::AllEndContSF(CEquipItemSFAgent *this)
{
  int *v1; // rdi@1
  signed __int64 i; // rcx@1
  int j; // [sp+0h] [bp-18h]@1
  CEquipItemSFAgent *v4; // [sp+20h] [bp+8h]@1

  v4 = this;
  v1 = &j;
  for ( i = 4i64; i; --i )
  {
    *v1 = -858993460;
    ++v1;
  }
  for ( j = 0; j < 8; ++j )
    v4->m_pContSF[j] = 0i64;
}
