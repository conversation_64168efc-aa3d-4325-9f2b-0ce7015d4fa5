/*
 * Function: ?UpdateSFCont@CCharacter@@UEAAXXZ
 * Address: 0x140174DE0
 */

void __fastcall CCharacter::UpdateSFCont(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@13
  __int64 v4; // [sp+0h] [bp-88h]@1
  unsigned int v5; // [sp+30h] [bp-58h]@5
  int j; // [sp+34h] [bp-54h]@5
  int k; // [sp+38h] [bp-50h]@7
  bool *v8; // [sp+40h] [bp-48h]@10
  unsigned int v9; // [sp+48h] [bp-40h]@11
  unsigned int v10; // [sp+4Ch] [bp-3Ch]@17
  bool *v11; // [sp+50h] [bp-38h]@17
  int l; // [sp+58h] [bp-30h]@17
  int m; // [sp+5Ch] [bp-2Ch]@19
  bool *v14; // [sp+60h] [bp-28h]@22
  unsigned __int16 v15; // [sp+68h] [bp-20h]@27
  CGameObjectVtbl *v16; // [sp+70h] [bp-18h]@13
  CCharacter *v17; // [sp+90h] [bp+8h]@1

  v17 = this;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CMyTimer::CountingTimer(&v17->m_tmrSFCont) )
  {
    v5 = _sf_continous::GetSFContCurTime();
    for ( j = 0; j < 2; ++j )
    {
      for ( k = 0; k < 8; ++k )
      {
        v8 = &v17->m_SFCont[j][k].m_bExist;
        if ( *v8 )
        {
          v9 = v5 - *((_DWORD *)v8 + 2);
          if ( v9 < *((_WORD *)v8 + 6) )
          {
            v3 = *((_WORD *)v8 + 6) - v9;
            v16 = v17->vfptr;
            ((void (__fastcall *)(CCharacter *, _QWORD, _QWORD, _QWORD))v16->SFContUpdateTimeMessage)(
              v17,
              (unsigned __int8)j,
              (unsigned __int8)k,
              (unsigned int)v3);
          }
          else
          {
            CCharacter::RemoveSFContEffect(v17, j, k, 0, 0);
          }
        }
      }
    }
    if ( v17->m_bLastContEffectUpdate )
    {
      v10 = 0;
      v11 = 0i64;
      for ( l = 0; l < 2; ++l )
      {
        for ( m = 0; m < 8; ++m )
        {
          v14 = &v17->m_SFCont[l][m].m_bExist;
          if ( *v14 && v10 <= *((_DWORD *)v14 + 4) )
          {
            v10 = *((_DWORD *)v14 + 4);
            v11 = v14;
          }
        }
      }
      v15 = v17->m_wLastContEffect;
      if ( v11 )
        v17->m_wLastContEffect = CCharacter::CalcEffectBit(v17, v11[1], *((_WORD *)v11 + 1));
      else
        v17->m_wLastContEffect = -1;
      if ( v15 != v17->m_wLastContEffect )
        CCharacter::SendMsg_LastEffectChangeInform(v17);
      v17->m_bLastContEffectUpdate = 0;
    }
    if ( !v17->m_ObjID.m_byID )
      CPlayer::UpdateAuraSFCont((CPlayer *)v17);
  }
}
