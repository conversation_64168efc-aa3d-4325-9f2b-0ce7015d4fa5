/*
 * Function: ?GuildBattleJoinGuildBattleRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C8820
 */

char __fastcall CNetworkEX::GuildBattleJoinGuildBattleRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CGuildBattleController *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  char *v8; // [sp+20h] [bp-18h]@4
  CPlayer *pkPlayer; // [sp+28h] [bp-10h]@4

  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = pBuf;
  pkPlayer = &g_Player + n;
  if ( pkPlayer->m_bOper )
  {
    v6 = CGuildBattleController::Instance();
    CGuildBattleController::Join(v6, pkPlayer);
    result = 1;
  }
  else
  {
    result = 1;
  }
  return result;
}
