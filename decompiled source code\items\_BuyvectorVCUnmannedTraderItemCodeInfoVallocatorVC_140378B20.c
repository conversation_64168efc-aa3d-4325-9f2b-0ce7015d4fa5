/*
 * Function: ?_Buy@?$vector@VCUnmannedTraderItemCodeInfo@@V?$allocator@VCUnmannedTraderItemCodeInfo@@@std@@@std@@IEAA_N_K@Z
 * Address: 0x140378B20
 */

char __fastcall std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Buy(std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *this, unsigned __int64 _Capacity)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo> > *v6; // [sp+30h] [bp+8h]@1
  unsigned __int64 _Count; // [sp+38h] [bp+10h]@1

  _Count = _Capacity;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6->_Myfirst = 0i64;
  v6->_Mylast = 0i64;
  v6->_Myend = 0i64;
  if ( _Capacity )
  {
    if ( std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::max_size(v6) < _Capacity )
      std::vector<CUnmannedTraderItemCodeInfo,std::allocator<CUnmannedTraderItemCodeInfo>>::_Xlen();
    v6->_Myfirst = std::allocator<CUnmannedTraderItemCodeInfo>::allocate(&v6->_Alval, _Count);
    v6->_Mylast = v6->_Myfirst;
    v6->_Myend = &v6->_Myfirst[_Count];
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
