/*
 * Function: ?pc_PartyLockReqeuest@CPlayer@@QEAAX_N@Z
 * Address: 0x1400C3C50
 */

void __fastcall CPlayer::pc_PartyLockReqeuest(CPlayer *this, bool bLock)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1
  bool v6; // [sp+38h] [bp+10h]@1

  v6 = bLock;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CPartyPlayer::IsPartyBoss(v5->m_pPartyMgr) )
  {
    if ( v5->m_pPartyMgr->m_bLock == v6 )
      CPlayer::SendMsg_PartyLockResult(v5, -1);
    else
      wa_PartyLock(&v5->m_id, v6);
  }
  else
  {
    CPlayer::SendMsg_PartyLockResult(v5, -1);
  }
}
