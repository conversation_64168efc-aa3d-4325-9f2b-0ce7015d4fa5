/*
 * Function: ?SendMsg_EconomyDataToWeb@@YAXXZ
 * Address: 0x1402A5110
 */

void SendMsg_EconomyDataToWeb(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  float v2; // xmm0_4@6
  __int64 v3; // [sp+0h] [bp-A8h]@1
  float szMsg[3]; // [sp+38h] [bp-70h]@6
  int v5[3]; // [sp+44h] [bp-64h]@6
  __int16 v6[3]; // [sp+50h] [bp-58h]@6
  __int16 v7; // [sp+56h] [bp-52h]@7
  char v8; // [sp+58h] [bp-50h]@7
  char v9; // [sp+59h] [bp-4Fh]@7
  int nRaceCode; // [sp+64h] [bp-44h]@4
  char pbyType; // [sp+74h] [bp-34h]@7
  char v12; // [sp+75h] [bp-33h]@7
  unsigned __int64 v13; // [sp+90h] [bp-18h]@4

  v0 = &v3;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v13 = (unsigned __int64)&v3 ^ _security_cookie;
  for ( nRaceCode = 0; nRaceCode < 3; ++nRaceCode )
  {
    v2 = (float)eGetRate(nRaceCode);
    szMsg[nRaceCode] = v2;
    eGetTex(nRaceCode);
    *(float *)&v5[nRaceCode] = v2;
    v6[nRaceCode] = eGetGuide(nRaceCode);
  }
  v7 = GetCurrentYear();
  v8 = GetCurrentMonth();
  v9 = GetCurrentDay();
  pbyType = 51;
  v12 = 5;
  if ( unk_1799C9ADE )
    CNetProcess::LoadSendMsg(unk_1414F2098, unk_1799C9ADD, &pbyType, (char *)szMsg, 0x22u);
}
