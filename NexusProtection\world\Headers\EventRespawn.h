#pragma once

#include <string>
#include <memory>
#include <vector>
#include <cstdint>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <array>

namespace NexusProtection::World {

    /**
     * @brief Event Respawn System
     * 
     * Represents an event-driven respawn system with state management and reward handling.
     * This class manages event respawn configurations, state tracking, and reward item management
     * for coordinated event-based monster respawning in the game world.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - 0_event_respawnQEAAXZ_1402A7740.c (main constructor)
     * - 0_state_event_respawnQEAAXZ_1402A77C0.c (state constructor)
     * - j_0_event_respawnQEAAXZ_14000211C.c (main jump table)
     * - j_0_state_event_respawnQEAAXZ_1400087D3.c (state jump table)
     */
    class EventRespawn {
    public:
        /**
         * @brief Nested State class for event respawn state management
         * 
         * Manages the internal state of event respawn operations including
         * timing, conditions, and state transitions.
         */
        class State {
        public:
            // Constructor and destructor
            State();
            ~State() = default;

            // Copy and move semantics
            State(const State& other);
            State& operator=(const State& other);
            State(State&& other) noexcept;
            State& operator=(State&& other) noexcept;

            // Core functionality
            void Initialize();
            void Reset();
            bool IsValid() const;

            // State management
            void SetActive(bool active) { m_isActive = active; }
            bool IsActive() const { return m_isActive; }
            void SetPending(bool pending) { m_isPending = pending; }
            bool IsPending() const { return m_isPending; }
            void SetCompleted(bool completed) { m_isCompleted = completed; }
            bool IsCompleted() const { return m_isCompleted; }

            // Timing management
            void SetStartTime(const std::chrono::steady_clock::time_point& time) { m_startTime = time; }
            std::chrono::steady_clock::time_point GetStartTime() const { return m_startTime; }
            void SetDuration(std::chrono::milliseconds duration) { m_duration = duration; }
            std::chrono::milliseconds GetDuration() const { return m_duration; }
            std::chrono::milliseconds GetElapsedTime() const;
            std::chrono::milliseconds GetRemainingTime() const;

            // Event tracking
            void SetEventId(uint32_t eventId) { m_eventId = eventId; }
            uint32_t GetEventId() const { return m_eventId; }
            void SetEventType(const std::string& eventType) { m_eventType = eventType; }
            const std::string& GetEventType() const { return m_eventType; }

            // Progress tracking
            void SetProgress(float progress) { m_progress = std::clamp(progress, 0.0f, 1.0f); }
            float GetProgress() const { return m_progress; }
            void IncrementProgress(float increment) { SetProgress(m_progress + increment); }

            // State transitions
            void StartEvent();
            void PauseEvent();
            void ResumeEvent();
            void CompleteEvent();
            void CancelEvent();

            // Utility methods
            std::string ToString() const;
            size_t GetMemoryUsage() const;

        private:
            // State flags
            bool m_isActive{false};
            bool m_isPending{false};
            bool m_isCompleted{false};
            bool m_isPaused{false};

            // Timing
            std::chrono::steady_clock::time_point m_startTime;
            std::chrono::milliseconds m_duration{0};

            // Event information
            uint32_t m_eventId{0};
            std::string m_eventType;
            float m_progress{0.0f};

            // Internal state data (8 DWORDs from original)
            std::array<uint32_t, 8> m_internalState{};

            // Internal methods
            void InitializeInternalState();
            void UpdateInternalState();
        };

        // Constructor and destructor
        EventRespawn();
        ~EventRespawn() = default;

        // Copy and move semantics
        EventRespawn(const EventRespawn& other);
        EventRespawn& operator=(const EventRespawn& other);
        EventRespawn(EventRespawn&& other) noexcept;
        EventRespawn& operator=(EventRespawn&& other) noexcept;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsValid() const;

        // State access
        State& GetState() { return m_state; }
        const State& GetState() const { return m_state; }

        // Load management
        bool IsLoaded() const { return m_isLoaded; }
        void SetLoaded(bool loaded) { m_isLoaded = loaded; }
        bool LoadEventConfiguration(const std::string& configPath);
        bool SaveEventConfiguration(const std::string& configPath) const;

        // Active management
        bool IsActive() const { return m_isActive; }
        void SetActive(bool active);
        void ActivateEvent();
        void DeactivateEvent();

        // Reward item management
        uint32_t GetUseRewardItemNum() const { return m_useRewardItemNum; }
        void SetUseRewardItemNum(uint32_t num) { m_useRewardItemNum = num; }
        void IncrementRewardItemNum() { ++m_useRewardItemNum; }
        void DecrementRewardItemNum() { if (m_useRewardItemNum > 0) --m_useRewardItemNum; }
        void ResetRewardItemNum() { m_useRewardItemNum = 0; }

        // Event operations
        void StartEventRespawn();
        void StopEventRespawn();
        void PauseEventRespawn();
        void ResumeEventRespawn();
        void ProcessEventRespawn(float deltaTime);

        // Event configuration
        void SetEventName(const std::string& name) { m_eventName = name; }
        const std::string& GetEventName() const { return m_eventName; }
        void SetEventDescription(const std::string& description) { m_eventDescription = description; }
        const std::string& GetEventDescription() const { return m_eventDescription; }

        // Reward configuration
        void AddRewardItem(uint32_t itemId, uint32_t quantity);
        void RemoveRewardItem(uint32_t itemId);
        void ClearRewardItems();
        const std::unordered_map<uint32_t, uint32_t>& GetRewardItems() const { return m_rewardItems; }

        // Event conditions
        void SetTriggerCondition(const std::string& condition) { m_triggerCondition = condition; }
        const std::string& GetTriggerCondition() const { return m_triggerCondition; }
        void SetCompletionCondition(const std::string& condition) { m_completionCondition = condition; }
        const std::string& GetCompletionCondition() const { return m_completionCondition; }

        // Event timing
        void SetEventDuration(std::chrono::milliseconds duration);
        std::chrono::milliseconds GetEventDuration() const;
        void SetCooldownDuration(std::chrono::milliseconds cooldown) { m_cooldownDuration = cooldown; }
        std::chrono::milliseconds GetCooldownDuration() const { return m_cooldownDuration; }

        // Event participants
        void AddParticipant(uint32_t playerId);
        void RemoveParticipant(uint32_t playerId);
        void ClearParticipants();
        const std::vector<uint32_t>& GetParticipants() const { return m_participants; }
        size_t GetParticipantCount() const { return m_participants.size(); }

        // Event handlers
        void RegisterEventHandler(const std::string& event, std::function<void(const EventRespawn&)> handler);
        void TriggerEvent(const std::string& event);

        // Validation
        bool ValidateEventConfiguration() const;
        bool ValidateRewardConfiguration() const;
        bool ValidateStateConfiguration() const;

        // Utility methods
        std::string ToString() const;
        size_t GetMemoryUsage() const;

        // Legacy C interface compatibility
        bool GetLoadLegacy() const { return m_isLoaded; }
        void SetLoadLegacy(bool loaded) { m_isLoaded = loaded; }
        bool GetActiveLegacy() const { return m_isActive; }
        void SetActiveLegacy(bool active) { SetActive(active); }
        uint32_t GetUseRewardItemNumLegacy() const { return m_useRewardItemNum; }
        void SetUseRewardItemNumLegacy(uint32_t num) { m_useRewardItemNum = num; }
        void* GetStatePtr() { return &m_state; }

    private:
        // Core member variables (matching original structure)
        State m_state;                          // Nested state object (originally State)
        bool m_isLoaded{false};                 // Load flag (originally bLoad)
        bool m_isActive{false};                 // Active flag (originally bActive)
        uint32_t m_useRewardItemNum{0};         // Reward item count (originally nUseRewardItemNum)

        // Extended functionality
        std::string m_eventName;
        std::string m_eventDescription;
        std::string m_triggerCondition;
        std::string m_completionCondition;
        std::chrono::milliseconds m_cooldownDuration{0};
        
        // Reward system
        std::unordered_map<uint32_t, uint32_t> m_rewardItems; // itemId -> quantity
        
        // Participants
        std::vector<uint32_t> m_participants;
        
        // Event handling
        std::unordered_map<std::string, std::function<void(const EventRespawn&)>> m_eventHandlers;
        
        // Configuration limits
        static constexpr size_t MAX_EVENT_NAME_LENGTH = 128;
        static constexpr size_t MAX_EVENT_DESCRIPTION_LENGTH = 512;
        static constexpr size_t MAX_CONDITION_LENGTH = 256;
        static constexpr uint32_t MAX_REWARD_ITEMS = 100;
        static constexpr uint32_t MAX_PARTICIPANTS = 1000;
        static constexpr std::chrono::milliseconds MAX_EVENT_DURATION{3600000}; // 1 hour
        
        // Internal methods
        void InitializeComponents();
        void ResetComponents();
        void ValidateAndTruncate();
        bool IsValidString(const std::string& str, size_t maxLength) const;
        void NotifyEventStateChange();
        void ProcessEventLogic(float deltaTime);
        void UpdateParticipants();
        void DistributeRewards();
    };

    /**
     * @brief Event Respawn Factory
     * 
     * Factory class for creating EventRespawn instances with proper configuration.
     */
    class EventRespawnFactory {
    public:
        static std::unique_ptr<EventRespawn> CreateEventRespawn();
        static std::unique_ptr<EventRespawn> CreateEventRespawn(const std::string& eventName);
        static std::unique_ptr<EventRespawn> CreateEventRespawn(const std::string& eventName, 
                                                               std::chrono::milliseconds duration);
        
        // Predefined event types
        static std::unique_ptr<EventRespawn> CreateTimedEvent(const std::string& eventName, 
                                                             std::chrono::milliseconds duration);
        static std::unique_ptr<EventRespawn> CreateConditionalEvent(const std::string& eventName, 
                                                                   const std::string& condition);
        static std::unique_ptr<EventRespawn> CreateRewardEvent(const std::string& eventName, 
                                                              const std::unordered_map<uint32_t, uint32_t>& rewards);
        
        // Batch creation
        static std::vector<std::unique_ptr<EventRespawn>> CreateEventRespawns(
            const std::vector<std::string>& eventNames);
    };

    /**
     * @brief Event Respawn Manager
     *
     * Manages multiple event respawn configurations and their interactions.
     */
    class EventRespawnManager {
    public:
        EventRespawnManager();
        ~EventRespawnManager() = default;

        // Event management
        void AddEventRespawn(std::unique_ptr<EventRespawn> eventRespawn);
        void RemoveEventRespawn(const std::string& eventName);
        std::shared_ptr<EventRespawn> GetEventRespawn(const std::string& eventName) const;
        const std::vector<std::shared_ptr<EventRespawn>>& GetAllEventRespawns() const { return m_eventRespawns; }

        // Batch operations
        void StartAllEvents();
        void StopAllEvents();
        void UpdateAllEvents(float deltaTime);
        void ProcessAllEventRespawns();

        // Statistics
        size_t GetTotalEventCount() const { return m_eventRespawns.size(); }
        size_t GetActiveEventCount() const;
        size_t GetLoadedEventCount() const;

        // Configuration
        void SetGlobalEventDuration(std::chrono::milliseconds duration);
        std::chrono::milliseconds GetGlobalEventDuration() const { return m_globalEventDuration; }

    private:
        std::vector<std::shared_ptr<EventRespawn>> m_eventRespawns;
        std::unordered_map<std::string, size_t> m_eventNameToIndex;
        std::chrono::milliseconds m_globalEventDuration{300000}; // Default 5 minutes
    };

    /**
     * @brief Event Respawn Utilities
     *
     * Utility functions for EventRespawn management and operations.
     */
    namespace EventRespawnUtils {
        // Validation utilities
        bool ValidateEventRespawn(const EventRespawn& eventRespawn);
        bool ValidateEventName(const std::string& name);
        bool ValidateEventCondition(const std::string& condition);

        // String utilities
        std::string SanitizeEventName(const std::string& name);
        std::string SanitizeEventCondition(const std::string& condition);
        std::string GenerateUniqueEventName(const std::string& baseName);

        // Memory utilities
        size_t CalculateMemoryFootprint(const EventRespawn& eventRespawn);

        // Configuration utilities
        void ConfigureDefaultEvent(EventRespawn& eventRespawn);
        void OptimizeEventConfiguration(EventRespawn& eventRespawn);

        // Conversion utilities
        std::string EventRespawnToJson(const EventRespawn& eventRespawn);
        std::unique_ptr<EventRespawn> EventRespawnFromJson(const std::string& json);

        // Event analysis
        std::vector<std::string> AnalyzeEventConfiguration(const EventRespawn& eventRespawn);
        float CalculateEventEfficiency(const EventRespawn& eventRespawn);
        std::string GetEventStatusSummary(const EventRespawn& eventRespawn);
    }

} // namespace NexusProtection::World

// Legacy C interface
extern "C" {
    // Legacy structure for compatibility
    struct _event_respawn {
        struct _state {
            uint32_t internalState[8];      // Internal state array (originally 8 DWORDs)
            char padding[32];               // Additional padding for original structure size
        } State;                            // Nested state object

        bool bLoad;                         // Load flag
        bool bActive;                       // Active flag
        uint32_t nUseRewardItemNum;         // Reward item count
        char padding[64];                   // Additional padding for original structure size
    };

    // Legacy function declarations
    void _event_respawn_Constructor(_event_respawn* this_ptr);
    void _event_respawn_Destructor(_event_respawn* this_ptr);
    void _event_respawn_state_Constructor(_event_respawn::_state* this_ptr);
    void _event_respawn_state_Destructor(_event_respawn::_state* this_ptr);

    // Legacy utility functions
    void _event_respawn_SetLoad(_event_respawn* this_ptr, bool load);
    void _event_respawn_SetActive(_event_respawn* this_ptr, bool active);
    void _event_respawn_SetUseRewardItemNum(_event_respawn* this_ptr, uint32_t num);
    bool _event_respawn_GetLoad(_event_respawn* this_ptr);
    bool _event_respawn_GetActive(_event_respawn* this_ptr);
    uint32_t _event_respawn_GetUseRewardItemNum(_event_respawn* this_ptr);
    void* _event_respawn_GetState(_event_respawn* this_ptr);
    void _event_respawn_Initialize(_event_respawn* this_ptr);
    void _event_respawn_Reset(_event_respawn* this_ptr);
    void _event_respawn_StartEvent(_event_respawn* this_ptr);
    void _event_respawn_StopEvent(_event_respawn* this_ptr);
}

// Legacy global compatibility
extern NexusProtection::World::EventRespawn* g_pEventRespawn;
