/*
 * Function: ?Initialize@CCashDbWorkerGB@@UEAA_NXZ
 * Address: 0x140318E40
 */

char __fastcall CCashDbWorkerGB::Initialize(CCashDbWorkerGB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v3; // rax@4
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-28h]@1
  CCashDbWorkerGB *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v1 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CNationSettingManager>::Instance();
  if ( CNationSettingManager::IsCashDBInit(v3) )
  {
    result = 0;
  }
  else if ( CashDbWorker::Initialize((CashDbWorker *)&v6->vfptr) )
  {
    CEnglandBillingMgr::SetPoolPointer(v6->_pkNet, v6->_pkPool);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
