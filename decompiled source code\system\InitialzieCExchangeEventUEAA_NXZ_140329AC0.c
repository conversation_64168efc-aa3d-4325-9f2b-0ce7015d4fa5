/*
 * Function: ?Initialzie@CExchangeEvent@@UEAA_NXZ
 * Address: 0x140329AC0
 */

char __fastcall CExchangeEvent::Initialzie(CExchangeEvent *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@8
  unsigned __int8 v6; // [sp+24h] [bp-14h]@11
  _base_fld *v7; // [sp+28h] [bp-10h]@12
  CExchangeEvent *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( GetLastWriteFileTime(".\\Initialize\\WorldSystem.ini", &v8->m_ftWrite) )
  {
    CExchangeEvent::ReadBuddhaEventInfo(v8);
    v8->m_bEnable = v8->m_bModifyEnable;
    v8->m_bDelete = v8->m_bModifyDelete;
    memcpy_0(v8->m_EventItemCode, v8->m_ModifyItemCode, 0x100ui64);
    if ( v8->m_bEnable || v8->m_bDelete )
    {
      for ( j = 0; j < 4; ++j )
      {
        v6 = GetItemTableCode(v8->m_EventItemCode[(signed __int64)j]);
        if ( v6 != 255 )
        {
          v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v6, v8->m_EventItemCode[(signed __int64)j]);
          if ( v7 )
          {
            v8->m_EventItemInfo[j].byTableCode = v6;
            v8->m_EventItemInfo[j].dwIndex = v7->m_dwIndex;
          }
        }
      }
    }
    CMyTimer::BeginTimer(&v8->m_tmDataFileCheckTime, 0x2710u);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
