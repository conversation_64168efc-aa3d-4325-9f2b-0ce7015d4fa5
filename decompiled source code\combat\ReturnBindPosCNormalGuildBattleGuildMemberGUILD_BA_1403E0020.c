/*
 * Function: ?ReturnBindPos@CNormalGuildBattleGuildMember@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403E0020
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuildMember::ReturnBindPos(GUILD_BATTLE::CNormalGuildBattleGuildMember *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CPlayer *v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuildMember *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CPlayer::SetBindMapData(v5->m_pkMember->pPlayer, v5->m_pOldBindMapData);
  CPlayer::SetBindDummy(v5->m_pkMember->pPlayer, v5->m_pOldBindDummyData);
  v4 = v5->m_pkMember->pPlayer;
  CUserDB::Update_Bind(v4->m_pUserDB, v5->m_szOldBindMapCode, v5->m_szOldBindDummy, 1);
}
