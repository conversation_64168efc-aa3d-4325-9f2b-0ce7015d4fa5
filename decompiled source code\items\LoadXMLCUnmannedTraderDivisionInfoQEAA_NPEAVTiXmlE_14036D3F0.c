/*
 * Function: ?LoadXML@CUnmannedTraderDivisionInfo@@QEAA_NPEAVTiXmlElement@@AEAVCLogFile@@@Z
 * Address: 0x14036D3F0
 */

char __fastcall CUnmannedTraderDivisionInfo::LoadXML(CUnmannedTraderDivisionInfo *this, TiXmlElement *pkElement, CLogFile *kLogger)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSortType *v6; // rax@8
  CUnmannedTraderSortType *v7; // rax@20
  __int64 v8; // [sp+0h] [bp-148h]@1
  __int64 v9; // [sp+20h] [bp-128h]@11
  unsigned int dwID; // [sp+34h] [bp-114h]@6
  TiXmlElement *pkElemSortType; // [sp+48h] [bp-100h]@6
  CUnmannedTraderSortType *_Val; // [sp+58h] [bp-F0h]@10
  unsigned int v13; // [sp+64h] [bp-E4h]@13
  CUnmannedTraderSortType *v14; // [sp+78h] [bp-D0h]@13
  char *szType; // [sp+88h] [bp-C0h]@27
  CUnmannedTraderClassInfo *v16; // [sp+98h] [bp-B0h]@27
  CUnmannedTraderClassInfoFactory v17; // [sp+B8h] [bp-90h]@27
  TiXmlNode *v18; // [sp+E8h] [bp-60h]@27
  unsigned int v19; // [sp+F0h] [bp-58h]@27
  CUnmannedTraderSortType *v20; // [sp+F8h] [bp-50h]@10
  CUnmannedTraderSortType *v21; // [sp+100h] [bp-48h]@7
  CUnmannedTraderSortType *v22; // [sp+108h] [bp-40h]@22
  CUnmannedTraderSortType *v23; // [sp+110h] [bp-38h]@19
  char v24; // [sp+118h] [bp-30h]@30
  char v25; // [sp+119h] [bp-2Fh]@32
  char v26; // [sp+11Ah] [bp-2Eh]@34
  char v27; // [sp+11Bh] [bp-2Dh]@36
  char v28; // [sp+11Ch] [bp-2Ch]@38
  __int64 v29; // [sp+120h] [bp-28h]@4
  CUnmannedTraderSortType *v30; // [sp+128h] [bp-20h]@8
  CUnmannedTraderSortType *v31; // [sp+130h] [bp-18h]@20
  CUnmannedTraderDivisionInfo *v32; // [sp+150h] [bp+8h]@1
  TiXmlElement *v33; // [sp+158h] [bp+10h]@1
  CLogFile *kLoggera; // [sp+160h] [bp+18h]@1

  kLoggera = kLogger;
  v33 = pkElement;
  v32 = this;
  v3 = &v8;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v29 = -2i64;
  if ( !pkElement )
    return 0;
  dwID = -1;
  pkElemSortType = TiXmlNode::FirstChildElement((TiXmlNode *)&pkElement->vfptr, "sorttype");
  if ( !pkElemSortType )
  {
    v21 = (CUnmannedTraderSortType *)operator new(0x104ui64);
    if ( v21 )
    {
      CUnmannedTraderSortType::CUnmannedTraderSortType(v21, 0);
      v30 = v6;
    }
    else
    {
      v30 = 0i64;
    }
    v20 = v30;
    _Val = v30;
    if ( !v30 )
    {
      v9 = 0i64;
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tDivisionID(%u) %dth sorttype new CUnmannedTraderSortType( %u ) NULL!\r\n",
        v32->m_dwID,
        0i64);
      return 0;
    }
    std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::push_back(
      &v32->m_vecSortType,
      &_Val);
  }
  v13 = 0;
  v14 = 0i64;
  while ( pkElemSortType )
  {
    dwID = -1;
    if ( !TiXmlElement::Attribute(pkElemSortType, "id", (int *)&dwID) )
    {
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tDivisionID(%u) %dth sorttype elemSortType->Attribute( id, &iID ) Invalid!\r\n",
        v32->m_dwID,
        v13);
      return 0;
    }
    if ( CUnmannedTraderDivisionInfo::IsExistSortTypeID(v32, dwID) )
    {
      LODWORD(v9) = dwID;
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tDivisionID(%u) %dth sorttype IsValidSortTypeID( iID(%u) ) Repeated ID!\r\n",
        v32->m_dwID,
        v13);
      return 0;
    }
    v23 = (CUnmannedTraderSortType *)operator new(0x104ui64);
    if ( v23 )
    {
      CUnmannedTraderSortType::CUnmannedTraderSortType(v23, dwID);
      v31 = v7;
    }
    else
    {
      v31 = 0i64;
    }
    v22 = v31;
    v14 = v31;
    if ( !v31 )
    {
      LODWORD(v9) = dwID;
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tDivisionID(%u) %dth sorttype new CUnmannedTraderSortType( %u ) NULL!\r\n",
        v32->m_dwID,
        v13);
      return 0;
    }
    if ( !CUnmannedTraderSortType::LoadXML(v14, pkElemSortType, kLoggera, v32->m_dwID) )
      return 0;
    std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::push_back(
      &v32->m_vecSortType,
      &v14);
    pkElemSortType = TiXmlNode::NextSiblingElement((TiXmlNode *)&pkElemSortType->vfptr, "sorttype");
    ++v13;
  }
  szType = 0i64;
  v16 = 0i64;
  CUnmannedTraderClassInfoFactory::CUnmannedTraderClassInfoFactory(&v17);
  v18 = (TiXmlNode *)TiXmlNode::FirstChildElement((TiXmlNode *)&v33->vfptr, "class");
  v19 = 0;
  while ( v18 )
  {
    dwID = -1;
    if ( !TiXmlElement::Attribute((TiXmlElement *)v18, "id", (int *)&dwID) )
    {
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\t0 == elemClass->Attribute( id, &iID ) Fail!\r\n");
      v24 = 0;
      CUnmannedTraderClassInfoFactory::~CUnmannedTraderClassInfoFactory(&v17);
      return v24;
    }
    if ( !CUnmannedTraderDivisionInfo::IsValidID(v32, dwID) )
    {
      LODWORD(v9) = dwID;
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\tDivisionID(%u) %dth Class IsValidID( iID(%u) ) Invalid!\r\n",
        v32->m_dwID,
        v19);
      v25 = 0;
      CUnmannedTraderClassInfoFactory::~CUnmannedTraderClassInfoFactory(&v17);
      return v25;
    }
    szType = (char *)TiXmlElement::Attribute((TiXmlElement *)v18, "type");
    if ( !szType )
    {
      CLogFile::Write(
        kLoggera,
        "CUnmannedTraderDivisionInfo::LoadXML( TiXmlElement * pkElement, CLogFile & kLogger )\r\n"
        "\t\t0 == elemClass->Attribute( type ) Fail!\r\n");
      v26 = 0;
      CUnmannedTraderClassInfoFactory::~CUnmannedTraderClassInfoFactory(&v17);
      return v26;
    }
    v16 = CUnmannedTraderClassInfoFactory::Create(&v17, szType, dwID);
    if ( !(unsigned __int8)((int (__fastcall *)(CUnmannedTraderClassInfo *, TiXmlNode *, CLogFile *, _QWORD))v16->vfptr->LoadXML)(
                             v16,
                             v18,
                             kLoggera,
                             v32->m_dwID) )
    {
      v27 = 0;
      CUnmannedTraderClassInfoFactory::~CUnmannedTraderClassInfoFactory(&v17);
      return v27;
    }
    std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::push_back(
      &v32->m_vecClass,
      &v16);
    v18 = (TiXmlNode *)TiXmlNode::NextSiblingElement(v18, "class");
    ++v19;
  }
  v28 = 1;
  CUnmannedTraderClassInfoFactory::~CUnmannedTraderClassInfoFactory(&v17);
  return v28;
}
