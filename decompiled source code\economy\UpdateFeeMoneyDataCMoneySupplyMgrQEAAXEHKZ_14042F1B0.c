/*
 * Function: ?UpdateFeeMoneyData@CMoneySupplyMgr@@QEAAXEHK@Z
 * Address: 0x14042F1B0
 */

void __fastcall CMoneySupplyMgr::UpdateFeeMoneyData(CMoneySupplyMgr *this, char byRace, int nLv, unsigned int nAmount)
{
  this->m_MS_data.dwAmount[5] += nAmount;
  switch ( nLv )
  {
    case 30:
      ++this->m_MS_data.nFeeLv[0];
      break;
    case 40:
      ++this->m_MS_data.nFeeLv[1];
      break;
    case 50:
      ++this->m_MS_data.nFeeLv[2];
      break;
    case 60:
      ++this->m_MS_data.nFeeLv[3];
      break;
  }
  if ( !byRace )
    ++this->m_MS_data.nFeeRace[0];
  if ( byRace == 1 )
    ++this->m_MS_data.nFeeRace[1];
  else
    ++this->m_MS_data.nFeeRace[2];
}
