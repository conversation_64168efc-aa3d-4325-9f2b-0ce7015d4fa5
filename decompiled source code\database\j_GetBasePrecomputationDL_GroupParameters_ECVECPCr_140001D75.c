/*
 * Function: j_?GetBasePrecomputation@?$DL_GroupParameters_EC@VECP@CryptoPP@@@CryptoPP@@UEBAAEBV?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@2@XZ
 * Address: 0x140001D75
 */

CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *__fastcall CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetBasePrecomputation(CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *this)
{
  return CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::GetBasePrecomputation(this);
}
