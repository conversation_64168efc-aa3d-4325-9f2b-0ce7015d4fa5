/*
 * Function: ?pc_RequestWatchingWithNPC@CPlayer@@QEAAXPEAVCItemStore@@@Z
 * Address: 0x1400CC650
 */

void __usercall CPlayer::pc_RequestWatchingWithNPC(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  float *v5; // rax@4
  int v6; // eax@5
  __int64 v7; // [sp+0h] [bp-48h]@1
  char *pszReqCode; // [sp+30h] [bp-18h]@7
  int v9; // [sp+38h] [bp-10h]@5
  CPlayer *v10; // [sp+50h] [bp+8h]@1
  CItemStore *v11; // [sp+58h] [bp+10h]@1

  v11 = pStore;
  v10 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CItemStore::GetStorePos(pStore);
  GetSqrt(v10->m_fCurPos, v5);
  if ( a3 <= 60.0 )
  {
    v9 = v11->m_byNpcRaceCode;
    v6 = CPlayerDB::GetRaceCode(&v10->m_Param);
    if ( v9 == v6 || v11->m_byNpcRaceCode == 255 )
    {
      pszReqCode = CItemStore::GetNpcCode(v11);
      if ( pszReqCode )
        CPlayer::Emb_CheckActForQuest(v10, 17, pszReqCode, 1u, 0);
    }
  }
}
