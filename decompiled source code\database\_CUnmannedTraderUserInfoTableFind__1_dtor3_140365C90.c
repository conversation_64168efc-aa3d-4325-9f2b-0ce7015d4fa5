/*
 * Function: _CUnmannedTraderUserInfoTable::Find_::_1_::dtor$3
 * Address: 0x140365C90
 */

void __fastcall CUnmannedTraderUserInfoTable::Find_::_1_::dtor_3(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::~_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>((std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)(a2 + 144));
}
