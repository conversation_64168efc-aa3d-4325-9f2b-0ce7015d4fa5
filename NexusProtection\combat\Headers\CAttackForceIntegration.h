/*
 * CAttackForceIntegration.h - Integration with Existing Combat Systems
 * Provides interfaces to existing refactored and legacy systems
 */

#pragma once

#include "CAttackForce.h"
#include "CAttackForceConstants.h"
#include "../Headers/CDamageProcessor.h"
#include "../../player/Headers/CMonsterAttack.h"
#include "../../world/Headers/MapStructures.h"

// Forward declarations for legacy systems
class CHolyStoneSystem;
class CPvpUserAndGuildRankingSystem;
class CPlayerDB;
class CLogFile;

// External global variables (legacy)
extern CHolyStoneSystem g_HolySys;
extern CPvpUserAndGuildRankingSystem* g_pPvpRankingSystem;

namespace NexusProtection {
namespace Combat {

/**
 * Integration adapter for legacy systems
 */
class CAttackForceIntegration {
public:
    /**
     * Initialize integration with legacy systems
     * @return true if successful
     */
    static bool Initialize();
    
    /**
     * Shutdown integration
     */
    static void Shutdown();
    
    /**
     * Check if integration is initialized
     * @return true if initialized
     */
    static bool IsInitialized();

    // Legacy system interfaces
    
    /**
     * Break stealth for character (legacy interface)
     * @param pCharacter Character to break stealth for
     */
    static void BreakStealth(CCharacter* pCharacter);
    
    /**
     * Get effect state from character (legacy interface)
     * @param pCharacter Character to check
     * @param nEffectType Effect type
     * @return true if character has effect
     */
    static bool GetEffectState(CCharacter* pCharacter, int nEffectType);
    
    /**
     * Get effect plus value (legacy interface)
     * @param pCharacter Character to check
     * @param nEffectType Effect type
     * @return Effect plus value
     */
    static float GetEffectPlus(CCharacter* pCharacter, int nEffectType);
    
    /**
     * Get effect rate multiplier (legacy interface)
     * @param pCharacter Character to check
     * @param nEffectType Effect type
     * @return Effect rate multiplier
     */
    static float GetEffectRate(CCharacter* pCharacter, int nEffectType);
    
    /**
     * Get character avoidance rate (legacy interface)
     * @param pCharacter Character to check
     * @return Avoidance rate percentage
     */
    static int GetAvoidanceRate(CCharacter* pCharacter);
    
    /**
     * Check if character is destroyer (legacy interface)
     * @param pCharacter Character to check
     * @return true if character is destroyer
     */
    static bool IsDestroyer(CCharacter* pCharacter);
    
    /**
     * Check if player has last attack buff (legacy interface)
     * @param pPlayer Player to check
     * @return true if player has last attack buff
     */
    static bool HasLastAttackBuff(CPlayer* pPlayer);
    
    /**
     * Get PvP boss type for character (legacy interface)
     * @param pCharacter Character to check
     * @return Boss type (0, 2, 6)
     */
    static int GetPvPBossType(CCharacter* pCharacter);
    
    /**
     * Calculate attack damage point (legacy interface)
     * @param pAttacker Attacking character
     * @param nAttPower Attack power
     * @param nPart Attack part
     * @param nTol Tolerance/element type
     * @param pTarget Target character
     * @param bBackAttack Back attack flag
     * @return Calculated damage
     */
    static int CalculateAttackDamagePoint(CCharacter* pAttacker, int nAttPower, int nPart, int nTol, CCharacter* pTarget, bool bBackAttack);
    
    /**
     * Calculate force attack points (legacy interface)
     * @param pAttacker Attacking character
     * @param pParam Attack parameters
     * @param bUseEffBullet Use effect bullet flag
     * @return Force attack points
     */
    static int CalculateForceAttackPoints(CCharacter* pAttacker, _attack_param* pParam, bool bUseEffBullet);

    // Modern system interfaces
    
    /**
     * Process area damage using modern damage processor
     * @param pAttacker Attacking character
     * @param params Area damage parameters
     * @param pAttackParam Attack parameters
     * @return Damage results
     */
    static std::vector<DamageResult> ProcessAreaDamage(CCharacter* pAttacker, const AreaDamageParams& params, _attack_param* pAttackParam);
    
    /**
     * Process flash damage using modern damage processor
     * @param pAttacker Attacking character
     * @param params Flash damage parameters
     * @param pAttackParam Attack parameters
     * @return Damage results
     */
    static std::vector<DamageResult> ProcessFlashDamage(CCharacter* pAttacker, const FlashDamageParams& params, _attack_param* pAttackParam);
    
    /**
     * Process sector damage using modern damage processor
     * @param pAttacker Attacking character
     * @param params Sector damage parameters
     * @param pAttackParam Attack parameters
     * @return Damage results
     */
    static std::vector<DamageResult> ProcessSectorDamage(CCharacter* pAttacker, const SectorDamageParams& params, _attack_param* pAttackParam);

    // Utility functions
    
    /**
     * Convert legacy attack type to modern enum
     * @param nLegacyType Legacy attack type
     * @return Modern attack type enum
     */
    static AttackForceConstants::AttackType ConvertAttackType(int nLegacyType);
    
    /**
     * Convert modern attack type to legacy value
     * @param attackType Modern attack type enum
     * @return Legacy attack type value
     */
    static int ConvertAttackType(AttackForceConstants::AttackType attackType);
    
    /**
     * Validate attack parameters
     * @param pParam Attack parameters
     * @return true if parameters are valid
     */
    static bool ValidateAttackParameters(_attack_param* pParam);
    
    /**
     * Log attack force event
     * @param level Log level
     * @param message Log message
     * @param pAttacker Attacking character (optional)
     * @param pTarget Target character (optional)
     */
    static void LogAttackEvent(int level, const std::string& message, CCharacter* pAttacker = nullptr, CCharacter* pTarget = nullptr);

private:
    static bool s_bInitialized;
    static std::unique_ptr<CDamageProcessor> s_pDamageProcessor;
    
    // Disable instantiation
    CAttackForceIntegration() = delete;
    ~CAttackForceIntegration() = delete;
    CAttackForceIntegration(const CAttackForceIntegration&) = delete;
    CAttackForceIntegration& operator=(const CAttackForceIntegration&) = delete;
};

/**
 * RAII wrapper for attack force operations
 */
class CAttackForceScope {
public:
    /**
     * Constructor - ensures integration is initialized
     */
    CAttackForceScope();
    
    /**
     * Destructor - cleanup if needed
     */
    ~CAttackForceScope();
    
    /**
     * Check if scope is valid
     * @return true if valid
     */
    bool IsValid() const { return m_bValid; }

private:
    bool m_bValid;
};

/**
 * Attack force factory for creating appropriate attack instances
 */
class CAttackForceFactory {
public:
    /**
     * Create attack force instance for character
     * @param pAttacker Attacking character
     * @return Unique pointer to attack force instance
     */
    static std::unique_ptr<CAttackForce> CreateAttackForce(CCharacter* pAttacker);
    
    /**
     * Create attack force instance with specific configuration
     * @param pAttacker Attacking character
     * @param config Attack configuration
     * @return Unique pointer to attack force instance
     */
    static std::unique_ptr<CAttackForce> CreateAttackForce(CCharacter* pAttacker, const AttackForceConstants::AttackForceFlags& config);

private:
    CAttackForceFactory() = delete;
};

/**
 * Compatibility macros for legacy code
 */
#define ATTACK_FORCE_BREAK_STEALTH(pChar) \
    NexusProtection::Combat::CAttackForceIntegration::BreakStealth(pChar)

#define ATTACK_FORCE_GET_EFFECT_STATE(pChar, nType) \
    NexusProtection::Combat::CAttackForceIntegration::GetEffectState(pChar, nType)

#define ATTACK_FORCE_GET_EFFECT_PLUS(pChar, nType) \
    NexusProtection::Combat::CAttackForceIntegration::GetEffectPlus(pChar, nType)

#define ATTACK_FORCE_GET_EFFECT_RATE(pChar, nType) \
    NexusProtection::Combat::CAttackForceIntegration::GetEffectRate(pChar, nType)

#define ATTACK_FORCE_IS_DESTROYER(pChar) \
    NexusProtection::Combat::CAttackForceIntegration::IsDestroyer(pChar)

#define ATTACK_FORCE_HAS_LAST_ATTACK_BUFF(pPlayer) \
    NexusProtection::Combat::CAttackForceIntegration::HasLastAttackBuff(pPlayer)

#define ATTACK_FORCE_GET_PVP_BOSS_TYPE(pChar) \
    NexusProtection::Combat::CAttackForceIntegration::GetPvPBossType(pChar)

} // namespace Combat
} // namespace NexusProtection
