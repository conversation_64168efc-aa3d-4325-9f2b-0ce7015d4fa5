/*
 * Function: ?SetTargetInfoFromCharacter@CChatStealSystem@@QEAA_NEPEAD@Z
 * Address: 0x1403F87C0
 */

char __fastcall CChatStealSystem::SetTargetInfoFromCharacter(CChatStealSystem *this, char byType, char *szCharName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned int v7; // [sp+20h] [bp-18h]@4
  CChatStealSystem *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1

  v9 = byType;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = _SearchPlayer(szCharName);
  if ( v7 == -1 )
  {
    result = 0;
  }
  else
  {
    v8->m_TargetInfo.m_dwTargetSerial = v7;
    v8->m_TargetInfo.m_byStealType = v9;
    result = 1;
  }
  return result;
}
