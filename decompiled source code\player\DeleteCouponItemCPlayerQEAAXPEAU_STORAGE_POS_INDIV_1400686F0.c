/*
 * Function: ?DeleteCouponItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@H@Z
 * Address: 0x1400686F0
 */

void __fastcall CPlayer::DeleteCouponItem(CPlayer *this, _STORAGE_POS_INDIV *CouponItem, int n)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  bool bDelete; // [sp+20h] [bp-38h]@10
  char *strErrorCodePos; // [sp+28h] [bp-30h]@10
  _STORAGE_LIST *v8; // [sp+30h] [bp-28h]@7
  int j; // [sp+38h] [bp-20h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+40h] [bp-18h]@7
  _base_fld *v11; // [sp+48h] [bp-10h]@8
  CPlayer *v12; // [sp+60h] [bp+8h]@1
  _STORAGE_POS_INDIV *v13; // [sp+68h] [bp+10h]@1
  int v14; // [sp+70h] [bp+18h]@1

  v14 = n;
  v13 = CouponItem;
  v12 = this;
  v3 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < v14; ++j )
  {
    v8 = v12->m_Param.m_pStoragePtr[v13[j].byStorageCode];
    pItem = _STORAGE_LIST::GetPtrFromSerial(v8, v13[j].wItemSerial);
    if ( pItem )
    {
      v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
      if ( v11 )
      {
        if ( !*(_DWORD *)&v11[4].m_strCode[48] )
        {
          strErrorCodePos = "CPlayer::DeleteCouponItem";
          bDelete = 1;
          CPlayer::Emb_DelStorage(v12, 0, pItem->m_byStorageIndex, 0, 1, "CPlayer::DeleteCouponItem");
          CMgrAvatorItemHistory::consume_del_item(
            &CPlayer::s_MgrItemHistory,
            v12->m_ObjID.m_wIndex,
            pItem,
            v12->m_szItemHistoryFileName);
        }
      }
    }
  }
}
