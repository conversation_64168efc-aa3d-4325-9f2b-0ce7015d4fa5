#pragma once

/**
 * @file CBspEntityLoader.h
 * @brief Modern C++20 BSP Entity Loading System
 * 
 * This file provides the entity loading functionality for BSP (Binary Space Partitioning)
 * maps with proper error handling, memory management, and modern C++ patterns.
 * 
 * Refactored from: decompiled source code/world/LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.c
 * Original Function: CBsp::LoadEntities
 * Original Address: 0x1404F96C0
 * 
 * <AUTHOR> Refactoring Team
 * @date 2024
 * @version 1.0
 */

#include <memory>
#include <string>
#include <vector>
#include <array>
#include <functional>
#include <stdexcept>
#include <chrono>
#include <unordered_map>
#include <optional>

// Forward declarations for BSP and entity systems
class CBsp;
class CEntity;
class CParticle;

// Legacy structure definitions (to be modernized)
struct _READ_MAP_ENTITIES_LIST;
struct _ENTITY_LIST;

namespace NexusProtection {
namespace World {

/**
 * @brief Entity loading result enumeration
 */
enum class EntityLoadResult : uint8_t {
    Success = 0,
    MemoryAllocationFailed,
    EntityFileNotFound,
    ParticleFileNotFound,
    InvalidEntityData,
    ShaderLoadFailed,
    ParticleInitFailed,
    EntityInitFailed,
    UnknownError
};

/**
 * @brief Entity type enumeration
 */
enum class EntityType : uint8_t {
    Unknown = 0,
    StaticEntity,
    ParticleEntity,
    ShaderEntity,
    AnimatedEntity
};

/**
 * @brief Entity loading flags
 */
enum class EntityLoadFlags : uint32_t {
    None = 0x00,
    UseShader = 0x02,
    RestoreTexture = 0x20,
    EnableFlag = 0x40
};

/**
 * @brief Entity position and transformation data
 */
struct EntityTransform {
    std::array<float, 3> position{0.0f, 0.0f, 0.0f};
    float rotationX{0.0f};
    float rotationY{0.0f};
    float scale{1.0f};
    std::array<int16_t, 3> boundingBoxMin{0, 0, 0};
    std::array<int16_t, 3> boundingBoxMax{0, 0, 0};
    float additionalFrame{0.0f};
    
    EntityTransform() = default;
    EntityTransform(const std::array<float, 3>& pos, float rotX, float rotY, float scl)
        : position(pos), rotationX(rotX), rotationY(rotY), scale(scl) {}
};

/**
 * @brief Entity loading statistics
 */
struct EntityLoadingStats {
    uint32_t totalEntities{0};
    uint32_t successfullyLoaded{0};
    uint32_t failedToLoad{0};
    uint32_t particleEntities{0};
    uint32_t staticEntities{0};
    size_t totalMemoryAllocated{0};
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    std::vector<std::string> failedFiles;
    
    EntityLoadingStats() : startTime(std::chrono::steady_clock::now()) {}
    
    void Complete() {
        endTime = std::chrono::steady_clock::now();
    }
    
    std::chrono::milliseconds GetDuration() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(endTime - startTime);
    }
    
    double GetSuccessRate() const {
        return totalEntities > 0 ? (static_cast<double>(successfullyLoaded) / totalEntities) * 100.0 : 0.0;
    }
};

/**
 * @brief Entity information structure
 */
struct EntityInfo {
    uint16_t id{0};
    EntityType type{EntityType::Unknown};
    std::string filename;
    EntityTransform transform;
    bool isFileExist{false};
    bool isParticle{false};
    uint32_t shaderID{0};
    uint32_t flags{0};
    std::unique_ptr<CParticle> particle{nullptr};
    
    EntityInfo() = default;
    EntityInfo(uint16_t entityId, const std::string& file, EntityType entityType)
        : id(entityId), filename(file), type(entityType) {}
};

/**
 * @brief Modern C++20 BSP Entity Loader class
 * 
 * This class provides a modern, maintainable approach to loading BSP entities
 * with proper error handling, memory management, and resource cleanup.
 */
class CBspEntityLoader {
public:
    // Constructor and Destructor
    CBspEntityLoader();
    explicit CBspEntityLoader(CBsp* bspInstance);
    virtual ~CBspEntityLoader() = default;

    // Disable copy constructor and assignment operator
    CBspEntityLoader(const CBspEntityLoader&) = delete;
    CBspEntityLoader& operator=(const CBspEntityLoader&) = delete;

    // Enable move constructor and assignment operator
    CBspEntityLoader(CBspEntityLoader&&) noexcept = default;
    CBspEntityLoader& operator=(CBspEntityLoader&&) noexcept = default;

    /**
     * @brief Load entities from BSP data
     * 
     * Modern implementation of the original CBsp::LoadEntities method.
     * Loads all entities and particles with proper error handling and memory management.
     * 
     * @param bsp Pointer to the BSP instance
     * @param entityList Pointer to the entity list data
     * @return EntityLoadResult indicating success or specific failure
     */
    EntityLoadResult LoadEntities(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList);

    /**
     * @brief Load entities with custom configuration
     * 
     * @param bsp Pointer to the BSP instance
     * @param entityList Pointer to the entity list data
     * @param loadFlags Entity loading flags
     * @return EntityLoadResult indicating success or specific failure
     */
    EntityLoadResult LoadEntitiesWithFlags(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList, 
                                          EntityLoadFlags loadFlags);

    /**
     * @brief Load individual entity
     * 
     * @param entityInfo Entity information structure
     * @param basePath Base path for entity files
     * @param loadFlags Loading flags
     * @return true if successful, false otherwise
     */
    bool LoadSingleEntity(EntityInfo& entityInfo, const std::string& basePath, 
                         EntityLoadFlags loadFlags = EntityLoadFlags::None);

    /**
     * @brief Load particle entity
     * 
     * @param entityInfo Entity information structure
     * @param basePath Base path for particle files
     * @return true if successful, false otherwise
     */
    bool LoadParticleEntity(EntityInfo& entityInfo, const std::string& basePath);

    /**
     * @brief Cleanup loaded entities
     * 
     * @return true if cleanup successful, false otherwise
     */
    bool CleanupEntities();

    /**
     * @brief Get loading statistics
     * 
     * @return Current loading statistics
     */
    const EntityLoadingStats& GetStatistics() const { return m_stats; }

    /**
     * @brief Get last error message
     * 
     * @return Last error message string
     */
    const std::string& GetLastError() const { return m_lastError; }

    /**
     * @brief Check if entities are loaded
     * 
     * @return true if entities are loaded, false otherwise
     */
    bool AreEntitiesLoaded() const { return m_entitiesLoaded; }

    /**
     * @brief Set verbose logging
     * 
     * @param verbose Enable/disable verbose logging
     */
    void SetVerboseLogging(bool verbose) { m_verboseLogging = verbose; }

    /**
     * @brief Get entity count
     * 
     * @return Number of loaded entities
     */
    size_t GetEntityCount() const { return m_loadedEntities.size(); }

    /**
     * @brief Get entity by ID
     * 
     * @param entityId Entity ID
     * @return Optional entity info
     */
    std::optional<EntityInfo> GetEntityById(uint16_t entityId) const;

    /**
     * @brief Get entities by type
     * 
     * @param type Entity type
     * @return Vector of entity infos
     */
    std::vector<EntityInfo> GetEntitiesByType(EntityType type) const;

    /**
     * @brief Get entity loading result as string
     * 
     * @param result Entity loading result
     * @return String representation of the result
     */
    static std::string EntityLoadResultToString(EntityLoadResult result);

protected:
    // BSP reference
    CBsp* m_bsp{nullptr};
    
    // Loading state
    bool m_entitiesLoaded{false};
    EntityLoadingStats m_stats;
    std::string m_lastError;
    bool m_verboseLogging{true};
    
    // Loaded entities
    std::vector<EntityInfo> m_loadedEntities;
    std::unordered_map<uint16_t, size_t> m_entityIdToIndex;

private:
    /**
     * @brief Initialize entity arrays
     * 
     * @param bsp BSP instance
     * @param entityCount Number of entities
     * @return true if successful, false otherwise
     */
    bool InitializeEntityArrays(CBsp* bsp, uint32_t entityCount);

    /**
     * @brief Build entity file path
     * 
     * @param basePath Base path
     * @param entityName Entity name
     * @return Complete file path
     */
    std::string BuildEntityFilePath(const std::string& basePath, const std::string& entityName);

    /**
     * @brief Process entity name
     * 
     * @param entityName Raw entity name
     * @return Processed entity name
     */
    std::string ProcessEntityName(const std::string& entityName);

    /**
     * @brief Load static entity
     *
     * @param entityInfo Entity information
     * @param filePath File path
     * @param loadFlags Loading flags
     * @return true if successful, false otherwise
     */
    bool LoadStaticEntity(EntityInfo& entityInfo, const std::string& filePath, EntityLoadFlags loadFlags);

    /**
     * @brief Initialize particle system
     *
     * @param entityInfo Entity information
     * @param particle Particle instance
     * @return true if successful, false otherwise
     */
    bool InitializeParticleSystem(EntityInfo& entityInfo, CParticle* particle);

    /**
     * @brief Setup entity transform
     *
     * @param entityInfo Entity information
     * @param entityData Raw entity data
     */
    void SetupEntityTransform(EntityInfo& entityInfo, const void* entityData);

    /**
     * @brief Generate random frame offset
     *
     * @return Random frame offset value
     */
    float GenerateRandomFrameOffset();

    /**
     * @brief Log loading message
     *
     * @param message Message to log
     * @param isError Whether this is an error message
     */
    void LogMessage(const std::string& message, bool isError = false);

    /**
     * @brief Update loading statistics
     *
     * @param success Whether the operation was successful
     * @param entityType Type of entity
     * @param memoryUsed Memory used by the entity
     */
    void UpdateStatistics(bool success, EntityType entityType, size_t memoryUsed = 0);

    /**
     * @brief Validate entity data
     *
     * @param entityList Entity list data
     * @return true if valid, false otherwise
     */
    bool ValidateEntityData(const struct _READ_MAP_ENTITIES_LIST* entityList);

    /**
     * @brief Cleanup single entity
     *
     * @param entityInfo Entity to cleanup
     * @return true if successful, false otherwise
     */
    bool CleanupSingleEntity(EntityInfo& entityInfo);

    /**
     * @brief Process map entities list
     *
     * @param bsp BSP instance
     * @param entityList Entity list data
     */
    void ProcessMapEntitiesList(CBsp* bsp, struct _READ_MAP_ENTITIES_LIST* entityList);
};

/**
 * @brief BSP Entity Loader Factory
 */
class CBspEntityLoaderFactory {
public:
    /**
     * @brief Create default entity loader
     *
     * @return Unique pointer to entity loader
     */
    static std::unique_ptr<CBspEntityLoader> CreateDefaultLoader();

    /**
     * @brief Create entity loader with BSP instance
     *
     * @param bsp BSP instance
     * @return Unique pointer to entity loader
     */
    static std::unique_ptr<CBspEntityLoader> CreateLoader(CBsp* bsp);
};

/**
 * @brief Utility functions for BSP entity loading
 */
namespace BspEntityLoaderUtils {
    /**
     * @brief Convert entity load result to string
     *
     * @param result Entity load result
     * @return String representation
     */
    std::string EntityLoadResultToString(EntityLoadResult result);

    /**
     * @brief Check if entity load result indicates success
     *
     * @param result Entity load result
     * @return true if successful, false otherwise
     */
    bool IsSuccessResult(EntityLoadResult result);

    /**
     * @brief Get entity type from flags
     *
     * @param isParticle Whether entity is a particle
     * @param shaderID Shader ID
     * @param flags Entity flags
     * @return Entity type
     */
    EntityType GetEntityTypeFromFlags(bool isParticle, uint32_t shaderID, uint32_t flags);

    /**
     * @brief Calculate memory usage for entity
     *
     * @param entityType Type of entity
     * @return Memory usage in bytes
     */
    size_t CalculateEntityMemoryUsage(EntityType entityType);

    /**
     * @brief Validate entity file path
     *
     * @param filePath File path to validate
     * @return true if valid, false otherwise
     */
    bool ValidateEntityFilePath(const std::string& filePath);

    /**
     * @brief Create entity transform from raw data
     *
     * @param rawData Raw entity data
     * @return Entity transform
     */
    EntityTransform CreateEntityTransformFromRawData(const void* rawData);
}

} // namespace World
} // namespace NexusProtection
};
