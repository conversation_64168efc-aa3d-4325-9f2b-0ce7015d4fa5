/*
 * Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor$0
 * Address: 0x140452650
 */

void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>>::~ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>>((CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec> > *)(*(_QWORD *)(a2 + 64) + 96i64));
}
