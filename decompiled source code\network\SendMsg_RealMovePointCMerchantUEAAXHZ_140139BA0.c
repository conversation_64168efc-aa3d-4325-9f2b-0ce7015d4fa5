/*
 * Function: ?SendMsg_RealMovePoint@CMerchant@@UEAAXH@Z
 * Address: 0x140139BA0
 */

void __fastcall CMerchant::SendMsg_RealMovePoint(CMerchant *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned __int16 v6; // [sp+3Ah] [bp-5Eh]@4
  unsigned int v7; // [sp+3Ch] [bp-5Ch]@4
  unsigned __int16 v8; // [sp+40h] [bp-58h]@4
  __int16 pShort; // [sp+42h] [bp-56h]@4
  __int16 v10; // [sp+48h] [bp-50h]@4
  __int16 v11; // [sp+4Ah] [bp-4Eh]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v13; // [sp+65h] [bp-33h]@4
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4
  CMerchant *v15; // [sp+A0h] [bp+8h]@1
  int dwClientIndex; // [sp+A8h] [bp+10h]@1

  dwClientIndex = n;
  v15 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_WORD *)szMsg = v15->m_pRecordSet->m_dwIndex;
  v6 = v15->m_ObjID.m_wIndex;
  v7 = v15->m_dwObjSerial;
  FloatToShort(v15->m_fCurPos, &pShort, 3);
  v10 = (signed int)ffloor(v15->m_fTarPos[0]);
  v11 = (signed int)ffloor(v15->m_fTarPos[2]);
  v8 = v15->m_wLastContEffect;
  pbyType = 4;
  v13 = 23;
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, szMsg, 0x14u);
}
