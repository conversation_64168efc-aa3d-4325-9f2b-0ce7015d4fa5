/*
 * Function: ?Clear@CGuildBattleSchedule@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403D9E90
 */

void __fastcall GUILD_BATTLE::CGuildBattleSchedule::Clear(GUILD_BATTLE::CGuildBattleSchedule *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-38h]@1
  ATL::CTimeSpan v5; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CGuildBattleSchedule *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v6->m_eState = 0;
  ATL::CTime::operator=(&v6->m_kNextStartTime, 0i64);
  ATL::CTimeSpan::CTimeSpan(&v5, 0i64);
  v6->m_kBattleTime.m_timeSpan = *v3;
  v6->m_pkStateList = &STATE_LIST_NULL_10;
}
