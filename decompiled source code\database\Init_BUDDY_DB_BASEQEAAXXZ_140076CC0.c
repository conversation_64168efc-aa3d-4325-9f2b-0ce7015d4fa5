/*
 * Function: ?Init@_BUDDY_DB_BASE@@QEAAXXZ
 * Address: 0x140076CC0
 */

void __fastcall _BUDDY_DB_BASE::Init(_BUDDY_DB_BASE *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _BUDDY_DB_BASE *v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 50; ++j )
    _BUDDY_DB_BASE::_LIST::Init((_BUDDY_DB_BASE::_LIST *)v5 + j);
}
