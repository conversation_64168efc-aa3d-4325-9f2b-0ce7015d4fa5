/*
 * Function: j_??$_Destroy_range@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@YAXPEAPEAVTRC_AutoTrade@@0AEAV?$allocator@PEAVTRC_AutoTrade@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x140008427
 */

void __fastcall std::_Destroy_range<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(TRC_AutoTrade **_First, TRC_AutoTrade **_Last, std::allocator<TRC_AutoTrade *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(_First, _Last, _Al, __formal);
}
