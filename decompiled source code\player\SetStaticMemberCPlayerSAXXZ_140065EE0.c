/*
 * Function: ?SetStaticMember@CPlayer@@SAXXZ
 * Address: 0x140065EE0
 */

void CPlayer::SetStaticMember(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  int v2; // eax@5
  int v3; // eax@13
  float v4; // xmm0_4@23
  float v5; // xmm0_4@23
  CNationSettingManager *v6; // rax@34
  unsigned __int16 v7; // ax@34
  __int64 v8; // [sp+0h] [bp-258h]@1
  int n; // [sp+20h] [bp-238h]@4
  _base_fld *v10; // [sp+28h] [bp-230h]@6
  _base_fld *v11; // [sp+30h] [bp-228h]@8
  _base_fld *v12; // [sp+38h] [bp-220h]@14
  _DWORD *v13; // [sp+40h] [bp-218h]@19
  char pszErrMsg; // [sp+60h] [bp-1F8h]@24
  UINT v15; // [sp+E4h] [bp-174h]@28
  char Dest; // [sp+100h] [bp-158h]@29
  UINT v17; // [sp+184h] [bp-D4h]@31
  char String; // [sp+1A0h] [bp-B8h]@32
  void *v19; // [sp+230h] [bp-28h]@4
  unsigned __int64 v20; // [sp+238h] [bp-20h]@4
  unsigned __int64 v21; // [sp+240h] [bp-18h]@4

  v0 = &v8;
  for ( i = 148i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v21 = (unsigned __int64)&v8 ^ _security_cookie;
  n = 0;
  v20 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 15);
  v19 = operator new[](saturated_mul(4ui64, v20));
  CPlayer::s_pnLinkForceItemToEffect = v19;
  memset_0(v19, -1, 8ui64);
  for ( n = 0; ; ++n )
  {
    v2 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 15);
    if ( n >= v2 )
      break;
    v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 15, n);
    if ( !v10 )
    {
      CLogFile::Write(&stru_1799C8E78, "CPlayer::SetStaticMember() : %d force..NULL", (unsigned int)n);
      break;
    }
    v11 = CRecordData::GetRecord(&stru_1799C8410 + 1, &v10[4].m_strCode[28]);
    if ( !v11 )
    {
      CLogFile::Write(&stru_1799C8E78, "CPlayer::SetStaticMember() : %s force..NULL", &v10[4].m_strCode[28]);
      break;
    }
    *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + n) = v11->m_dwIndex;
  }
  for ( n = 0; ; ++n )
  {
    v3 = CRecordData::GetRecordNum(&stru_1799C8410);
    if ( n >= v3 )
      break;
    v12 = CRecordData::GetRecord(&stru_1799C8410, n);
    if ( (!v12[1].m_dwIndex || v12[1].m_dwIndex == 1)
      && *(_DWORD *)&v12[1].m_strCode[4] >= 0
      && *(_DWORD *)&v12[1].m_strCode[4] < 8
      && v12[1].m_dwIndex != 2 )
    {
      v13 = (_UNKNOWN *)((char *)&CPlayer::s_SkillIndexPerMastery + 196 * *(_DWORD *)&v12[1].m_strCode[4]);
      v13[(*v13)++ + 1] = n;
    }
  }
  memset_0(CPlayer::s_nAddMstFc, 1, 0x190ui64);
  for ( n = 0; n < 100; ++n )
  {
    v4 = (float)n;
    pow((float)n, 3);
    v5 = v4 * 2.0;
    sqrt(v5);
    CPlayer::s_nAddMstFc[n] = (signed int)ffloor(v5);
  }
  if ( !LoadMasteryLimFile(&pszErrMsg) )
  {
    MyMessageBox("CPlayer::SetStaticMember()", &pszErrMsg);
    ServerProgramExit("MasteryLimData Load Error", 0);
  }
  if ( !CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    v15 = GetPrivateProfileIntA("Formula", "StdDefPoint", 0x7FFFFFFF, ".\\Initialize\\WorldSystem.ini");
    if ( v15 == 0x7FFFFFFF )
    {
      _itoa(s_nStdDefPoint, &Dest, 10);
      WritePrivateProfileStringA("Formula", "StdDefPoint", &Dest, ".\\Initialize\\WorldSystem.ini");
    }
    else
    {
      s_nStdDefPoint = v15;
    }
    v17 = GetPrivateProfileIntA("Formula", "RcvDefPoint", 0x7FFFFFFF, ".\\Initialize\\WorldSystem.ini");
    if ( v17 == 0x7FFFFFFF )
    {
      _itoa(s_nRevDefPoint, &String, 10);
      WritePrivateProfileStringA("Formula", "RcvDefPoint", &String, ".\\Initialize\\WorldSystem.ini");
    }
    else
    {
      s_nRevDefPoint = v17;
    }
  }
  v6 = CTSingleton<CNationSettingManager>::Instance();
  v7 = CNationSettingManager::GetBillingForceCloseDelay(v6);
  _DELAY_PROCESS::Init(&CPlayer::s_BillingForceCloseDelay, 0x9E4u, 1000 * v7);
}
