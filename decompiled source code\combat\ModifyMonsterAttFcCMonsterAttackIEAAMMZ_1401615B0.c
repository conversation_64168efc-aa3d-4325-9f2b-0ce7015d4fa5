/*
 * Function: ?ModifyMonsterAttFc@CMonsterAttack@@IEAAMM@Z
 * Address: 0x1401615B0
 */

float __fastcall CMonsterAttack::ModifyMonsterAttFc(CMonsterAttack *this, float fAttFc)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float result; // xmm0_4@5
  __int64 v5; // [sp+0h] [bp-18h]@1

  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (float)(1.0 - fAttFc) <= 0.0 )
    result = fAttFc;
  else
    result = 1.0 - (float)((float)(1.0 - fAttFc) * 0.25);
  return result;
}
