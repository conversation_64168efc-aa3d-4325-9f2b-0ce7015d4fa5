/*
 * Function: ?Kill@CNormalGuildBattle@GUILD_BATTLE@@QEAAHKK@Z
 * Address: 0x1403E5C30
 */

signed __int64 __fastcall GUILD_BATTLE::CNormalGuildBattle::Kill(GUILD_BATTLE::CNormalGuildBattle *this, unsigned int dwSrcCharacSerial, unsigned int dwDestCharacSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@9
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v6; // rax@19
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v7; // rax@22
  char *v8; // rax@29
  __int64 v9; // [sp+0h] [bp-C8h]@1
  char *v10; // [sp+20h] [bp-A8h]@29
  int v11; // [sp+28h] [bp-A0h]@29
  char *v12; // [sp+30h] [bp-98h]@29
  char *v13; // [sp+38h] [bp-90h]@29
  int v14; // [sp+40h] [bp-88h]@29
  char *v15; // [sp+48h] [bp-80h]@29
  int v16; // [sp+50h] [bp-78h]@29
  GUILD_BATTLE::CNormalGuildBattleGuildMember *pkDestMember; // [sp+60h] [bp-68h]@4
  CPlayer *v18; // [sp+68h] [bp-60h]@4
  char v19; // [sp+70h] [bp-58h]@10
  GUILD_BATTLE::CNormalGuildBattleGuildMember *pkSrcMember; // [sp+78h] [bp-50h]@10
  CPlayer *v21; // [sp+80h] [bp-48h]@10
  int v22; // [sp+88h] [bp-40h]@17
  unsigned int dwScore; // [sp+8Ch] [bp-3Ch]@19
  char v24; // [sp+90h] [bp-38h]@19
  unsigned int uiMapID; // [sp+94h] [bp-34h]@19
  unsigned int v26; // [sp+98h] [bp-30h]@22
  char v27; // [sp+9Ch] [bp-2Ch]@22
  unsigned int v28; // [sp+A0h] [bp-28h]@22
  char *v29; // [sp+A8h] [bp-20h]@24
  char *v30; // [sp+B0h] [bp-18h]@27
  char *v31; // [sp+B8h] [bp-10h]@29
  GUILD_BATTLE::CNormalGuildBattle *v32; // [sp+D0h] [bp+8h]@1
  int v33; // [sp+D8h] [bp+10h]@1
  int dwSerial; // [sp+E0h] [bp+18h]@1

  dwSerial = dwDestCharacSerial;
  v33 = dwSrcCharacSerial;
  v32 = this;
  v3 = &v9;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  pkDestMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr(&v32->m_k1P, dwDestCharacSerial);
  v18 = 0i64;
  if ( !pkDestMember || (v18 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(pkDestMember)) == 0i64 )
  {
    pkDestMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr(&v32->m_k2P, dwSerial);
    if ( pkDestMember )
      v18 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(pkDestMember);
  }
  if ( v18 )
  {
    v19 = 1;
    pkSrcMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr(&v32->m_k1P, v33);
    v21 = 0i64;
    if ( !pkSrcMember || (v21 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(pkSrcMember)) == 0i64 )
    {
      pkSrcMember = GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPtr(&v32->m_k2P, v33);
      if ( pkSrcMember )
        v21 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(pkSrcMember);
      v19 = 0;
    }
    if ( v21 )
    {
      v22 = 0;
      if ( v19 )
      {
        v22 = GUILD_BATTLE::CNormalGuildBattleGuild::Kill(&v32->m_k1P, pkSrcMember, pkDestMember);
        if ( v22 > 0 )
        {
          dwScore = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(&v32->m_k1P);
          v24 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v32->m_k1P);
          uiMapID = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v32->m_pkField);
          v6 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
          GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(v6, uiMapID, v24, dwScore);
        }
      }
      else
      {
        v22 = GUILD_BATTLE::CNormalGuildBattleGuild::Kill(&v32->m_k2P, pkSrcMember, pkDestMember);
        if ( v22 > 0 )
        {
          v26 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(&v32->m_k2P);
          v27 = GUILD_BATTLE::CNormalGuildBattleGuild::GetColorInx(&v32->m_k1P);
          v28 = GUILD_BATTLE::CNormalGuildBattleField::GetMapID(v32->m_pkField);
          v7 = GUILD_BATTLE::CCurrentGuildBattleInfoManager::Instance();
          GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(v7, v28, v27, v26);
        }
      }
      if ( v19 )
        v29 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v32->m_k2P);
      else
        v29 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v32->m_k1P);
      if ( v19 )
        v30 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v32->m_k1P);
      else
        v30 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGuildName(&v32->m_k2P);
      v31 = CPlayerDB::GetCharNameW(&v18->m_Param);
      v8 = CPlayerDB::GetCharNameW(&v21->m_Param);
      v16 = v22;
      v15 = v31;
      v14 = dwSerial;
      v13 = v29;
      v12 = v8;
      v11 = v33;
      v10 = v30;
      GUILD_BATTLE::CNormalGuildBattleLogger::Log(
        &v32->m_kLogger,
        "CNormalGuildBattle::Kill( %u, %u ) : %s : (%u)%s -> %s : (%u)%s Kill! (%u)PointUp!",
        (unsigned int)v33,
        (unsigned int)dwSerial);
      if ( v22 > 0 )
        GUILD_BATTLE::CNormalGuildBattle::SendKillInform(v32);
      result = (unsigned int)v22;
    }
    else
    {
      result = 145i64;
    }
  }
  else
  {
    result = 145i64;
  }
  return result;
}
