/*
 * Function: j_?assign@?$vector@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAAX_KAEBVCUnmannedTraderSchedule@@@Z
 * Address: 0x140008387
 */

void __fastcall std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::assign(std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, unsigned __int64 _Count, CUnmannedTraderSchedule *_Val)
{
  std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::assign(this, _Count, _Val);
}
