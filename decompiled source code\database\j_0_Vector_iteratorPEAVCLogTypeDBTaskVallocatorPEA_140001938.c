/*
 * Function: j_??0?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140001938
 */

void __fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__that)
{
  std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    this,
    __that);
}
