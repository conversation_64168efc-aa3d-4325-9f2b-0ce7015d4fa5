/*
 * Function: _stdext::_Hash_stdext::_Hmap_traits_int_CNationCodeStr_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationCodeStr_____ptr64____0___::insert_::_1_::dtor$7
 * Address: 0x14020CCD0
 */

void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CNationCodeStr_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationCodeStr_____ptr64____0___::insert_::_1_::dtor_7(__int64 a1, __int64 a2)
{
  std::list<std::pair<int const,CNationCodeStr *>,std::allocator<std::pair<int const,CNationCodeStr *>>>::_Iterator<0>::~_Iterator<0>(*(std::list<std::pair<int const ,CNationCodeStr *>,std::allocator<std::pair<int const ,CNationCodeStr *> > >::_Iterator<0> **)(a2 + 368));
}
