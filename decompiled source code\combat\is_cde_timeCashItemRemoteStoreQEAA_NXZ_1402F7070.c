/*
 * Function: ?is_cde_time@CashItemRemoteStore@@QEAA_NXZ
 * Address: 0x1402F7070
 */

char __fastcall CashItemRemoteStore::is_cde_time(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  __time32_t Time; // [sp+24h] [bp-24h]@7
  CashItemRemoteStore *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v6->m_con_event.m_bConEvent && !v6->m_cde.m_ini.m_bUseCashDiscount )
    return 0;
  _time32(&Time);
  if ( v6->m_con_event.m_bConEvent )
  {
    if ( v6->m_con_event.m_ini.m_byEventKind == 2
      && Time > v6->m_con_event.m_eventtime.m_EventTime[0]
      && Time < v6->m_con_event.m_eventtime.m_EventTime[1] )
    {
      return 1;
    }
  }
  else if ( Time > v6->m_cde.m_ini.m_cdeTime[0] && Time < v6->m_cde.m_ini.m_cdeTime[1] )
  {
    return 1;
  }
  return 0;
}
