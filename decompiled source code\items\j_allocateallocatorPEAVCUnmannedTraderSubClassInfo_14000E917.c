/*
 * Function: j_?allocate@?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@QEAAPEAPEAVCUnmannedTraderSubClassInfo@@_K@Z
 * Address: 0x14000E917
 */

CUnmannedTraderSubClassInfo **__fastcall std::allocator<CUnmannedTraderSubClassInfo *>::allocate(std::allocator<CUnmannedTraderSubClassInfo *> *this, unsigned __int64 _Count)
{
  return std::allocator<CUnmannedTraderSubClassInfo *>::allocate(this, _Count);
}
