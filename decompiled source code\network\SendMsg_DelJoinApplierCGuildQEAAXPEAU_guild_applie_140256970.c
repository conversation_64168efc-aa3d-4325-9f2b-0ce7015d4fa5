/*
 * Function: ?SendMsg_DelJoinApplier@CGuild@@QEAAXPEAU_guild_applier_info@@E@Z
 * Address: 0x140256970
 */

void __fastcall CGuild::SendMsg_DelJoinApplier(CGuild *this, _guild_applier_info *p, char byDelCode)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char v7; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  int j; // [sp+64h] [bp-14h]@4
  CGuild *v11; // [sp+80h] [bp+8h]@1

  v11 = this;
  v3 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(_DWORD *)szMsg = p->pPlayer->m_dwObjSerial;
  v7 = byDelCode;
  pbyType = 27;
  v9 = 9;
  for ( j = 0; j < 50; ++j )
  {
    if ( _guild_member_info::IsFill(&v11->m_MemberData[j])
      && v11->m_MemberData[j].pPlayer
      && (v11->m_MemberData[j].byClassInGuild == 1 || v11->m_MemberData[j].byClassInGuild == 2) )
    {
      CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_MemberData[j].pPlayer->m_ObjID.m_wIndex, &pbyType, szMsg, 5u);
    }
  }
}
