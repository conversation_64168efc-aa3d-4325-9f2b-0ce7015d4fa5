/*
 * Function: ?Update_PvpPointInfo@CRFWorldDatabase@@QEAA_NKPEAFN@Z
 * Address: 0x140496FF0
 */

bool __fastcall CRFWorldDatabase::Update_PvpPointInfo(CRFWorldDatabase *this, unsigned int dwSerial, __int16 *zClass, long double dPvpPoint)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@4
  int v7; // ecx@4
  unsigned int v8; // edx@4
  __int64 v10; // [sp-20h] [bp-178h]@1
  int v11; // [sp+0h] [bp-158h]@4
  int v12; // [sp+8h] [bp-150h]@4
  long double v13; // [sp+10h] [bp-148h]@4
  char Dest; // [sp+30h] [bp-128h]@4
  unsigned __int64 v15; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v16; // [sp+160h] [bp+8h]@1
  unsigned int v17; // [sp+168h] [bp+10h]@1

  v17 = dwSerial;
  v16 = this;
  v4 = &v10;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = (unsigned __int64)&v10 ^ _security_cookie;
  v6 = zClass[2];
  v7 = zClass[1];
  v8 = *zClass;
  v13 = dPvpPoint;
  v12 = v6;
  v11 = v7;
  sprintf(&Dest, "{ CALL pUpdate_PvpPointInfo( %d, %d, %d, %d, %f ) }", v17, v8);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v16->vfptr, &Dest, 1);
}
