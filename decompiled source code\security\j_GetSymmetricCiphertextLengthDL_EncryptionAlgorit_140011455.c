/*
 * Function: j_?GetSymmetricCiphertextLength@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$0A@@CryptoPP@@UEBA_K_K@Z
 * Address: 0x140011455
 */

unsigned __int64 __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::GetSymmetricCiphertextLength(CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0> *this, unsigned __int64 plaintextLength)
{
  return CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,0>::GetSymmetricCiphertextLength(
           this,
           plaintextLength);
}
