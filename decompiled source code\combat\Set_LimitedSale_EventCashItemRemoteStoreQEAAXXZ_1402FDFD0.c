/*
 * Function: ?Set_LimitedSale_Event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FDFD0
 */

void __fastcall CashItemRemoteStore::Set_LimitedSale_Event(CashItemRemoteStore *this)
{
  char *v1; // rdi@1
  signed __int64 i; // rcx@1
  char v3; // [sp+0h] [bp-118h]@1
  CashItemRemoteStore *v4; // [sp+120h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 66i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 += 4;
  }
  qmemcpy(&v3, &v4->m_lim_event_New, 0xF4ui64);
  qmemcpy(&v4->m_lim_event, &v3, sizeof(v4->m_lim_event));
}
