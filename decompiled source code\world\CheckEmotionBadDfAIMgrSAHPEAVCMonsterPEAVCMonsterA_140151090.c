/*
 * Function: ?CheckEmotionBad@DfAIMgr@@SAHPEAVCMonster@@PEAVCMonsterAI@@H@Z
 * Address: 0x140151090
 */

__int64 __fastcall DfAIMgr::CheckEmotionBad(CMonster *pMon, CMonsterAI *pAI, int nDamage)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@6
  Us_FSM_Node *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  int v8; // [sp+20h] [bp-18h]@9
  int v9; // [sp+24h] [bp-14h]@20
  unsigned int v10; // [sp+28h] [bp-10h]@9
  CMonster *v11; // [sp+40h] [bp+8h]@1

  v11 = pMon;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v11 && pAI )
  {
    if ( !v11->m_pRecordSet )
      _wassert(
        L"pMon->m_pRecordSet",
        L"G:\\00_ZoneServer_Source\\03_Temp_Source\\2009_05_13_Source_Oversea\\zoneserver\\GameMain\\NewMonster\\NewMonste"
         "rAI_Df_Handler.cpp",
        0x16Cu);
    v8 = 100;
    v6 = Us_HFSM::GetNode((Us_HFSM *)&pAI->vfptr, 3u);
    v10 = Us_FSM_Node::GetState(v6);
    switch ( v10 )
    {
      case 0xBu:
        v8 = EmotionType[5 * ((signed int)ffloor(*(float *)&v11->m_pRecordSet[29].m_strCode[28]) - 1)];
        break;
      case 0xCu:
        v8 = EmotionType[5 * ((signed int)ffloor(*(float *)&v11->m_pRecordSet[29].m_strCode[28]) - 1) + 1];
        break;
      case 0xDu:
        v8 = EmotionType[5 * ((signed int)ffloor(*(float *)&v11->m_pRecordSet[29].m_strCode[28]) - 1) + 2];
        break;
      case 0xEu:
        v8 = EmotionType[5 * ((signed int)ffloor(*(float *)&v11->m_pRecordSet[29].m_strCode[28]) - 1) + 3];
        break;
      case 0xFu:
        v8 = EmotionType[5 * ((signed int)ffloor(*(float *)&v11->m_pRecordSet[29].m_strCode[28]) - 1) + 4];
        break;
    }
    v9 = rand() % 100;
    result = v9 <= v8;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
