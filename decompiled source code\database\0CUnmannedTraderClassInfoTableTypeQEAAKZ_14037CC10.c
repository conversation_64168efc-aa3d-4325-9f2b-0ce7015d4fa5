/*
 * Function: ??0CUnmannedTraderClassInfoTableType@@QEAA@K@Z
 * Address: 0x14037CC10
 */

void __fastcall CUnmannedTraderClassInfoTableType::CUnmannedTraderClassInfoTableType(CUnmannedTraderClassInfoTableType *this, unsigned int dwID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int64 v5; // [sp+20h] [bp-18h]@4
  CUnmannedTraderClassInfoTableType *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = -2i64;
  CUnmannedTraderClassInfo::CUnmannedTraderClassInfo((CUnmannedTraderClassInfo *)&v6->vfptr, dwID);
  v6->vfptr = (CUnmannedTraderClassInfoVtbl *)&CUnmannedTraderClassInfoTableType::`vftable';
  v6->m_byTableCode = -1;
  std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v6->m_vecSubClass);
  strcpy_0(v6->m_szTypeName, "table");
}
