/*
 * Function: ??$unchecked_uninitialized_fill_n@PEAPEAVCRaceBuffInfoByHolyQuest@@_KPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@stdext@@YAXPEAPEAVCRaceBuffInfoByHolyQuest@@_KAEBQEAV1@AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuest@@@std@@@Z
 * Address: 0x1403BB600
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CRaceBuffInfoByHolyQuest * *,unsigned __int64,CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>(CRaceBuffInfoByHolyQuest **_First, unsigned __int64 _Count, CRaceBuffInfoByHolyQuest *const *_Val, std::allocator<CRaceBuffInfoByHolyQuest *> *_Al)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Scalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  CRaceBuffInfoByHolyQuest **__formal; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  CRaceBuffInfoByHolyQuest **_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<CRaceBuffInfoByHolyQuest *> *v12; // [sp+68h] [bp+20h]@1

  v12 = _Al;
  _Vala = (CRaceBuffInfoByHolyQuest **)_Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<CRaceBuffInfoByHolyQuest * *,CRaceBuffInfoByHolyQuest * *>(&__formal, &__formal);
  std::_Uninit_fill_n<CRaceBuffInfoByHolyQuest * *,unsigned __int64,CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>(
    __formal,
    _Counta,
    _Vala,
    v12,
    v8,
    v7);
}
