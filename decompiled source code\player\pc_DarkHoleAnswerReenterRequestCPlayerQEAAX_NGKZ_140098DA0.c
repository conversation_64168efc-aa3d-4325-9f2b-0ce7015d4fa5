/*
 * Function: ?pc_DarkHoleAnswerReenterRequest@CPlayer@@QEAAX_NGK@Z
 * Address: 0x140098DA0
 */

void __fastcall CPlayer::pc_DarkHoleAnswerReenterRequest(CPlayer *this, bool bEnter, unsigned __int16 wChannelIndex, unsigned int dwChannelSerial)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@20
  char *v7; // rax@25
  __int64 v8; // [sp+0h] [bp-C8h]@1
  float *pfStartPos; // [sp+20h] [bp-A8h]@25
  float *pfOldPos; // [sp+28h] [bp-A0h]@25
  char v11; // [sp+30h] [bp-98h]@4
  _ENTER_DUNGEON_NEW_POS pNewPos; // [sp+48h] [bp-80h]@22
  CDarkHoleChannel *v13; // [sp+68h] [bp-60h]@4
  CMapData *v14; // [sp+70h] [bp-58h]@25
  float v15; // [sp+88h] [bp-40h]@25
  float v16; // [sp+8Ch] [bp-3Ch]@25
  float v17; // [sp+90h] [bp-38h]@25
  unsigned __int16 v18; // [sp+A4h] [bp-24h]@25
  CMapData *pIntoMap; // [sp+A8h] [bp-20h]@25
  float *v20; // [sp+B0h] [bp-18h]@25
  char *v21; // [sp+B8h] [bp-10h]@25
  CPlayer *v22; // [sp+D0h] [bp+8h]@1
  unsigned int v23; // [sp+E8h] [bp+20h]@1

  v23 = dwChannelSerial;
  v22 = this;
  v4 = &v8;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = 0;
  v13 = CDarkHoleDungeonQuest::GetChannel(&g_DarkHoleQuest, wChannelIndex);
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v22->m_id.wIndex) == 99 )
  {
    v11 = 28;
  }
  else if ( v22->m_pDHChannel )
  {
    v11 = 15;
  }
  else if ( CDarkHoleChannel::IsFill(v13) && v13->m_dwChannelSerial == v23 )
  {
    if ( CDarkHoleChannel::IsReEnterable(v13, v22->m_dwObjSerial) )
    {
      if ( v22->m_pCurMap->m_pMapSet->m_nMapType )
      {
        v11 = 11;
      }
      else if ( CGameObject::GetCurSecNum((CGameObject *)&v22->vfptr) == -1 || v22->m_bMapLoading )
      {
        v11 = 8;
      }
      else if ( CPlayer::IsRidingUnit(v22) )
      {
        v11 = 12;
      }
      else
      {
        v6 = CDarkHoleChannel::GetCurrentMemberNum(v13);
        if ( v6 < v13->m_pQuestSetup->dwMaxMember )
        {
          if ( !CDarkHoleChannel::GetEnterNewPos(v13, &pNewPos) )
            v11 = 7;
        }
        else
        {
          v11 = 18;
        }
      }
    }
    else
    {
      v11 = 5;
    }
  }
  else
  {
    v11 = 5;
  }
  if ( !v11 )
  {
    v14 = v22->m_pCurMap;
    v15 = v22->m_fCurPos[0];
    v16 = v22->m_fCurPos[1];
    v17 = v22->m_fCurPos[2];
    v18 = v22->m_wMapLayerIndex;
    pIntoMap = CMapOperation::GetMap(&g_MapOper, (unsigned __int8)pNewPos.byMapCode);
    CPlayer::OutOfMap(v22, pIntoMap, pNewPos.wLayerIndex, 5, pNewPos.fPos);
    pfOldPos = &v15;
    LOWORD(pfStartPos) = v18;
    CDarkHoleChannel::PushMember(v13, v22, 1, v22->m_pCurMap, v18, &v15);
    v20 = (float *)v13->m_pQuestSetup->szQuestTitle;
    v21 = v13->m_aszOpenerName;
    v7 = CPlayerDB::GetCharNameA(&v22->m_Param);
    pfStartPos = v20;
    CLogFile::Write(&stru_1799C8FE8, "REENTER: %s, (OPENER:%s, DARKHOLE:%s)", v7, v21);
  }
  CPlayer::SendMsg_ReEnterDarkHoleResult(v22, v11);
}
