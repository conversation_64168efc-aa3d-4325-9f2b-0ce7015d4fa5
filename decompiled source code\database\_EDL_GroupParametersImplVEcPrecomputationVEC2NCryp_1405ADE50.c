/*
 * Function: ??_E?$DL_GroupParametersImpl@V?$EcPrecomputation@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@2@V?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x1405ADE50
 */

signed __int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::`vector deleting destructor'(__int64 a1, int a2)
{
  __int64 v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::`vbase destructor(a1 - 312);
  if ( v4 & 1 )
    operator delete((void *)(v3 - 312));
  return v3 - 312;
}
