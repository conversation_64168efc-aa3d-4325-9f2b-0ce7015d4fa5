/*
 * Function: ??1?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAA@XZ
 * Address: 0x14058A5B0
 */

void __fastcall CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(__int64 a1)
{
  CryptoPP::ECPPoint *v1; // [sp+40h] [bp+8h]@1

  v1 = (CryptoPP::ECPPoint *)a1;
  CryptoPP::Integer::~Integer((CryptoPP::Integer *)(a1 + 88));
  CryptoPP::ECPPoint::~ECPPoint(v1);
}
