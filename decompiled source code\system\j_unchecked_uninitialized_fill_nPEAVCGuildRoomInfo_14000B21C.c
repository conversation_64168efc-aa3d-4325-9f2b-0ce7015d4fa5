/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCGuildRoomInfo@@_KV1@V?$allocator@VCGuildRoomInfo@@@std@@@stdext@@YAXPEAVCGuildRoomInfo@@_KAEBV1@AEAV?$allocator@VCGuildRoomInfo@@@std@@@Z
 * Address: 0x14000B21C
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CGuildRoomInfo *,unsigned __int64,CGuildRoomInfo,std::allocator<CGuildRoomInfo>>(CGuildRoomInfo *_First, unsigned __int64 _Count, CGuildRoomInfo *_Val, std::allocator<CGuildRoomInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CGuildRoomInfo *,unsigned __int64,CGuildRoomInfo,std::allocator<CGuildRoomInfo>>(
    _First,
    _Count,
    _<PERSON>,
    _<PERSON>);
}
