/*
 * Function: ?_CheckForcePullUnit@CPlayer@@QEAAXXZ
 * Address: 0x140106A10
 */

void __usercall CPlayer::_CheckForcePullUnit(CPlayer *this@<rcx>, float a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  DWORD v6; // [sp+20h] [bp-18h]@5
  DWORD v7; // [sp+24h] [bp-14h]@5
  int v8; // [sp+28h] [bp-10h]@6
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v9->m_pParkingUnit )
  {
    v6 = timeGetTime();
    v7 = v6 - v9->m_dwLastTimeCheckUnitViewOver;
    if ( v7 > 0x7D0 )
    {
      v9->m_dwLastTimeCheckUnitViewOver = v6;
      v8 = 60000;
      if ( v9->m_pCurMap != v9->m_pParkingUnit->m_pCurMap
        || (GetSqrt(v9->m_fCurPos, v9->m_pParkingUnit->m_fCurPos), a2 >= 540.0)
        || (v4 = v9->m_fCurPos[1] - v9->m_pParkingUnit->m_fCurPos[1], abs(v4), v4 >= 100.0) )
      {
        if ( v9->m_dwUnitViewOverTime == -1 )
        {
          v9->m_dwUnitViewOverTime = v6;
        }
        else if ( v6 - v9->m_dwUnitViewOverTime > 0xEA60 )
        {
          CPlayer::ForcePullUnit(v9, 0);
        }
      }
      else
      {
        v9->m_dwUnitViewOverTime = -1;
      }
    }
  }
}
