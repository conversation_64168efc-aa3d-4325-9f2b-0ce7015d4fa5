/*
 * Function: j_??0?$allocator@PEAVCUnmannedTraderSortType@@@std@@QEAA@AEBV01@@Z
 * Address: 0x14000370B
 */

void __fastcall std::allocator<CUnmannedTraderSortType *>::allocator<CUnmannedTraderSortType *>(std::allocator<CUnmannedTraderSortType *> *this, std::allocator<CUnmannedTraderSortType *> *__formal)
{
  std::allocator<CUnmannedTraderSortType *>::allocator<CUnmannedTraderSortType *>(this, __formal);
}
