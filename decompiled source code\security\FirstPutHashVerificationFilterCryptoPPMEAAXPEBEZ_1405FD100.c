/*
 * Function: ?FirstPut@HashVerificationFilter@CryptoPP@@MEAAXPEBE@Z
 * Address: 0x1405FD100
 */

void __fastcall CryptoPP::HashVerificationFilter::FirstPut(CryptoPP::HashVerificationFilter *this, const unsigned __int8 *a2)
{
  int v2; // eax@2
  unsigned __int64 v3; // ST20_8@2
  __int64 v4; // ST28_8@3
  CryptoPP::BufferedTransformation *v5; // rax@3
  CryptoPP::HashVerificationFilter *v6; // [sp+60h] [bp+8h]@1
  unsigned __int8 *v7; // [sp+68h] [bp+10h]@1

  v7 = (unsigned __int8 *)a2;
  v6 = this;
  if ( this->m_flags & 1 )
  {
    v2 = ((int (__fastcall *)(_QWORD))this->m_hashModule->vfptr[3].Clone)(this->m_hashModule);
    CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::New(
      &v6->m_expectedHash,
      (unsigned int)v2);
    v3 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v6->m_expectedHash);
    qmemcpy(
      CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v6->m_expectedHash),
      v7,
      v3);
    if ( v6->m_flags & 4 )
    {
      v4 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::size(&v6->m_expectedHash);
      LODWORD(v5) = ((int (__fastcall *)(CryptoPP::HashVerificationFilter *))v6->vfptr[20].Clone)(v6);
      CryptoPP::BufferedTransformation::Put(v5, v7, v4);
    }
  }
}
