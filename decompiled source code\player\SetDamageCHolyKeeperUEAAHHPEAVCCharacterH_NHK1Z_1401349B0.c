/*
 * Function: ?SetDamage@CHoly<PERSON>eeper@@UEAAHHPEAVCCharacter@@H_NHK1@Z
 * Address: 0x1401349B0
 */

__int64 __fastcall CHolyKeeper::SetDamage(CHolyKeeper *this, int nDam, CCharacter *pDst, int nDstLv, bool bCrt, int nAttackType, unsigned int dwAttackSerial, bool bJadeReturn)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int v10; // eax@5
  __int64 v12; // [sp+0h] [bp-48h]@1
  int v13; // [sp+20h] [bp-28h]@5
  CGameObjectVtbl *v14; // [sp+28h] [bp-20h]@5
  int v15; // [sp+30h] [bp-18h]@8
  CHolyKeeper *v16; // [sp+50h] [bp+8h]@1
  int v17; // [sp+58h] [bp+10h]@1
  CCharacter *pCharacter; // [sp+60h] [bp+18h]@1

  pCharacter = pDst;
  v17 = nDam;
  v16 = this;
  v8 = &v12;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) == 3 )
  {
    v13 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
    v14 = pCharacter->vfptr;
    v10 = ((int (__fastcall *)(CCharacter *))v14->GetObjRace)(pCharacter);
    if ( v13 != v10 )
    {
      if ( v17 > 1 )
      {
        if ( v16->m_nHP - v17 <= 0 )
          v15 = 0;
        else
          v15 = v16->m_nHP - v17;
        v16->m_nHP = v15;
      }
      if ( !v16->m_nHP )
      {
        CHolyKeeper::Destroy(v16, 0, 0i64);
        CHolyStoneSystem::ReceiveDestroyKeeper(&g_HolySys, pCharacter);
      }
    }
  }
  return v16->m_nHP;
}
