/*
 * Function: ?pc_TalikCrystalExchange@CPlayer@@QEAAXEPEAU_list@_talik_crystal_exchange_clzo@@@Z
 * Address: 0x1400B5EE0
 */

void __fastcall CPlayer::pc_TalikCrystalExchange(CPlayer *this, char byExchangeNum, _talik_crystal_exchange_clzo::_list *pList)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CTalkCrystalCombineManager *v5; // rax@4
  __int64 v6; // [sp+0h] [bp-28h]@1
  CPlayer *pPlayer; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1
  _talik_crystal_exchange_clzo::_list *pLista; // [sp+40h] [bp+18h]@1

  pLista = pList;
  v8 = byExchangeNum;
  pPlayer = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = CTalkCrystalCombineManager::Instance();
  CTalkCrystalCombineManager::Doit(v5, pPlayer, v8, pLista);
}
