/*
 * Function: ??0_worlddb_arrange_char_info@@QEAA@XZ
 * Address: 0x1401BF040
 */

void __fastcall _worlddb_arrange_char_info::_worlddb_arrange_char_info(_worlddb_arrange_char_info *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _worlddb_arrange_char_info *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `vector constructor iterator'(
    v4->ArrangeChar,
    0x45ui64,
    50,
    (void *(__cdecl *)(void *))_NOT_ARRANGED_AVATOR_DB::_NOT_ARRANGED_AVATOR_DB);
}
