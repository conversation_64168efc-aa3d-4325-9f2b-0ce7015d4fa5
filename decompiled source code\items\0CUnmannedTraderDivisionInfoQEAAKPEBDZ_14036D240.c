/*
 * Function: ??0CUnmannedTraderDivisionInfo@@QEAA@KPEBD@Z
 * Address: 0x14036D240
 */

void __fastcall CUnmannedTraderDivisionInfo::CUnmannedTraderDivisionInfo(CUnmannedTraderDivisionInfo *this, unsigned int dwID, const char *szName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  __int64 v6; // [sp+20h] [bp-18h]@4
  char *Source; // [sp+28h] [bp-10h]@5
  CUnmannedTraderDivisionInfo *v8; // [sp+40h] [bp+8h]@1
  char *v9; // [sp+50h] [bp+18h]@1

  v9 = (char *)szName;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = -2i64;
  v8->m_dwID = dwID;
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(&v8->m_vecClass);
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>(&v8->m_vecSortType);
  if ( v9 )
    Source = v9;
  else
    Source = "NONE";
  strcpy_0(v8->m_szName, Source);
}
