/*
 * Function: ?CompleteReRegistItem@CUnmannedTraderUserInfo@@AEAA_NKGKPEAVCLogFile@@PEAE@Z
 * Address: 0x14035AC10
 */

char __fastcall CUnmannedTraderUserInfo::CompleteReRegistItem(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, unsigned __int16 dwItemSerial, unsigned int dwPrice, CLogFile *pkLogger, char *pbyProcRet)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@5
  CUnmannedTraderRegistItemInfo *v9; // rax@6
  CUnmannedTraderRegistItemInfo *v10; // rax@7
  unsigned __int16 v11; // ax@7
  CUnmannedTraderRegistItemInfo *v12; // rax@8
  __int64 v13; // [sp+0h] [bp-A8h]@1
  int v14; // [sp+20h] [bp-88h]@7
  int v15; // [sp+28h] [bp-80h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-70h]@4
  bool v17; // [sp+54h] [bp-54h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v18; // [sp+58h] [bp-50h]@4
  char v19; // [sp+70h] [bp-38h]@5
  char v20; // [sp+71h] [bp-37h]@7
  char v21; // [sp+72h] [bp-36h]@8
  __int64 v22; // [sp+78h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v23; // [sp+80h] [bp-28h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v24; // [sp+88h] [bp-20h]@4
  int v25; // [sp+90h] [bp-18h]@7
  CUnmannedTraderUserInfo *v26; // [sp+B0h] [bp+8h]@1
  int dwRegistSeriala; // [sp+B8h] [bp+10h]@1
  unsigned __int16 v28; // [sp+C0h] [bp+18h]@1
  unsigned int dwPricea; // [sp+C8h] [bp+20h]@1

  dwPricea = dwPrice;
  v28 = dwItemSerial;
  dwRegistSeriala = dwRegistSerial;
  v26 = this;
  v6 = &v13;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v22 = -2i64;
  CUnmannedTraderUserInfo::Find(v26, &result, dwRegistSerial);
  v23 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v26->m_vecRegistItemInfo,
          &v18);
  v24 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v23;
  v17 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v23->_Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v18);
  if ( v17 )
  {
    *pbyProcRet = 25;
    CLogFile::Write(
      pkLogger,
      "CUnmannedTraderUserInfo::CompleteReRegistItem(...)\r\n\t\t Find( dwRegistSerial(%u) ) Not Exist!\r\n",
      (unsigned int)dwRegistSeriala);
    v19 = 0;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v8 = v19;
  }
  else
  {
    v9 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    if ( CUnmannedTraderRegistItemInfo::GetItemSerial(v9) == v28 )
    {
      v12 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
      CUnmannedTraderRegistItemInfo::ReRegistItem(v12, dwPricea);
      CUnmannedTraderUserInfo::CountRegistItem(v26);
      v21 = 1;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v8 = v21;
    }
    else
    {
      *pbyProcRet = 25;
      v25 = v28;
      v10 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
      v11 = CUnmannedTraderRegistItemInfo::GetItemSerial(v10);
      v15 = dwRegistSeriala;
      v14 = v25;
      CLogFile::Write(
        pkLogger,
        "CUnmannedTraderUserInfo::CompleteReRegistItem( DWORD dwRegistSerial(%u), WORD dwItemSerial(%u) )\r\n"
        "\t\t (*iFind).GetItemSerial()(%u) != dwItemSerial(%u) Invalid!\r\n",
        (unsigned int)dwRegistSeriala,
        v11);
      v20 = 0;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v8 = v20;
    }
  }
  return v8;
}
