/*
 * Function: ?_Kfn@?$_Hmap_traits@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@$0A@@stdext@@SAAEBQEAUScheduleMSG@@AEBU?$pair@QEAUScheduleMSG@@K@std@@@Z
 * Address: 0x140424160
 */

ScheduleMSG *const *__fastcall stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>::_Kfn(stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> *this, std::pair<ScheduleMSG * const,unsigned long> *_Val)
{
  return (ScheduleMSG *const *)this;
}
