/*
 * Function: ?dev_view_boss@CPlayer@@QEAA_NXZ
 * Address: 0x1400BAD40
 */

char __fastcall CPlayer::dev_view_boss(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  unsigned __int64 v5; // [sp+30h] [bp-68h]@4
  char szMsg[4]; // [sp+48h] [bp-50h]@4
  unsigned __int64 v7; // [sp+4Ch] [bp-4Ch]@4
  char pbyType; // [sp+74h] [bp-24h]@4
  char v9; // [sp+75h] [bp-23h]@4
  CPlayer *v10; // [sp+A0h] [bp+8h]@1

  v10 = this;
  v1 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = v10->m_dwLastState;
  v5 |= 0x10000ui64;
  *(_DWORD *)szMsg = v10->m_dwObjSerial;
  v7 = v5;
  pbyType = 4;
  v9 = 25;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 0xCu);
  return 1;
}
