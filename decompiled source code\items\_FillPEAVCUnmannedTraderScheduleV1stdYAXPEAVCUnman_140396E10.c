/*
 * Function: ??$_Fill@PEAVCUnmannedTraderSchedule@@V1@@std@@YAXPEAVCUnmannedTraderSchedule@@0AEBV1@@Z
 * Address: 0x140396E10
 */

void __fastcall std::_Fill<CUnmannedTraderSchedule *,CUnmannedTraderSchedule>(CUnmannedTraderSchedule *_First, CUnmannedTraderSchedule *_Last, CUnmannedTraderSchedule *_Val)
{
  char *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // [sp+0h] [bp-38h]@1
  CUnmannedTraderSchedule *v6; // [sp+40h] [bp+8h]@1

  v6 = _First;
  v3 = &v5;
  for ( i = 10i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 += 4;
  }
  while ( v6 != _Last )
  {
    qmemcpy(&v5, _<PERSON>, 0x20ui64);
    qmemcpy(v6, &v5, sizeof(CUnmannedTraderSchedule));
    ++v6;
  }
}
