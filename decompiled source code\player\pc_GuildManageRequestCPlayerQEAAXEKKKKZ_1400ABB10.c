/*
 * Function: ?pc_GuildManageRequest@CPlayer@@QEAAXEKKKK@Z
 * Address: 0x1400ABB10
 */

void __fastcall CPlayer::pc_GuildManageRequest(CPlayer *this, char byType, unsigned int dwDst, unsigned int dwObj1, unsigned int dwObj2, unsigned int dwObj3)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v8; // eax@6
  __int64 v9; // [sp+0h] [bp-58h]@1
  char v10; // [sp+30h] [bp-28h]@4
  CGuild *v11; // [sp+38h] [bp-20h]@4
  int v12; // [sp+40h] [bp-18h]@9
  bool v13; // [sp+44h] [bp-14h]@14
  bool v14; // [sp+45h] [bp-13h]@15
  CPlayer *v15; // [sp+60h] [bp+8h]@1
  char v16; // [sp+68h] [bp+10h]@1
  unsigned int dwDestGuild; // [sp+70h] [bp+18h]@1
  unsigned int dwStartTimeIdx; // [sp+78h] [bp+20h]@1

  dwStartTimeIdx = dwObj1;
  dwDestGuild = dwDst;
  v16 = byType;
  v15 = this;
  v6 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10 = 0;
  v11 = v15->m_Param.m_pGuild;
  if ( !v11 )
    v10 = -54;
  v8 = CPlayerDB::GetCharSerial(&v15->m_Param);
  if ( v11->m_MasterData.dwSerial != v8 )
    v10 = -53;
  if ( !v10 )
  {
    v12 = (unsigned __int8)v16;
    switch ( v16 )
    {
      case 0:
        v10 = CGuild::ManageProposeGuildBattle(v11, dwDestGuild, dwStartTimeIdx, dwObj2, dwObj3);
        break;
      case 1:
        v10 = CGuild::ManageExpulseMember(v11, dwDestGuild);
        break;
      case 2:
        v10 = CGuild::ManagePopGuildMoney(v11, dwDestGuild, dwStartTimeIdx, dwObj2);
        break;
      case 3:
        v10 = CGuild::ManageBuyGuildEmblem(v11, dwDestGuild, dwStartTimeIdx, dwObj2);
        break;
      case 4:
        v13 = dwStartTimeIdx != 0;
        v10 = CGuild::ManageGuildCommittee(v11, dwDestGuild, dwStartTimeIdx != 0);
        break;
      case 5:
        v14 = dwDestGuild != 0;
        v10 = CGuild::ManageAcceptORRefuseGuildBattle(v11, dwDestGuild != 0);
        break;
      default:
        break;
    }
  }
  CPlayer::SendMsg_GuildManageResult(v15, v10);
}
