/*
 * Function: ?Resurrect@CPlayer@@QEAAXXZ
 * Address: 0x1400500A0
 */

void __fastcall CPlayer::Resurrect(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@9
  int v4; // eax@9
  int v5; // eax@9
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  CGameObjectVtbl *v8; // [sp+28h] [bp-10h]@9
  CPlayer *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = 0;
  if ( v9->m_bCorpse )
  {
    if ( v9->m_pCurMap->m_pMapSet->m_nMapType == 1 )
      v7 = 2;
  }
  else
  {
    v7 = 1;
  }
  if ( !v7 )
  {
    v9->m_bCorpse = 0;
    v9->m_byModeType = 0;
    v9->m_byMoveType = 1;
    v3 = ((int (__fastcall *)(CPlayer *))v9->vfptr->GetMaxHP)(v9);
    v8 = v9->vfptr;
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v8->SetHP)(v9, (unsigned int)v3, 0i64);
    v4 = CPlayer::GetMaxFP(v9);
    CPlayer::SetFP(v9, v4, 0);
    v5 = CPlayer::GetMaxSP(v9);
    CPlayer::SetSP(v9, v5, 0);
    CPlayer::SendMsg_ResurrectInform(v9);
  }
  CPlayer::SendMsg_Resurrect(v9, v7, 0);
  if ( v9->m_bAfterEffect )
    CPlayer::pc_NuclearAfterEffect(v9);
}
