/*
 * Function: ?Ref@?$Singleton@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@U?$NewObject@V?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@@2@$0A@@CryptoPP@@QEBAAEBV?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@2@XZ
 * Address: 0x14063AD80
 */

__int64 __fastcall CryptoPP::Singleton<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>,CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>,0>::Ref(__int64 a1)
{
  __int64 v1; // rax@6
  __int64 v3; // [sp+50h] [bp+8h]@1

  v3 = a1;
  if ( !(dword_184A8A638 & 1) )
  {
    dword_184A8A638 |= 1u;
    CryptoPP::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>::simple_ptr<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>(&qword_184A8A630);
    atexit(sub_1406E9B70);
  }
  while ( byte_184A8A62C )
  {
    if ( byte_184A8A62C != 1 )
      return qword_184A8A630;
  }
  byte_184A8A62C = 1;
  LODWORD(v1) = CryptoPP::NewObject<CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>>::operator()(v3);
  qword_184A8A630 = v1;
  byte_184A8A62C = 2;
  return qword_184A8A630;
}
