/*
 * Function: ?CheckModifyPrice@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_adjust_price_request_clzo@@AEAKPEAVCLogFile@@PEAK@Z
 * Address: 0x14035AE60
 */

char __fastcall CUnmannedTraderUserInfo::CheckModifyPrice(CUnmannedTraderUserInfo *this, char byType, _a_trade_adjust_price_request_clzo *pRequest, unsigned int *dwOldPrice, CLogFile *pkLogger, unsigned int *pdwTax)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@5
  CUnmannedTraderRegistItemInfo *v9; // rax@20
  CUnmannedTraderRegistItemInfo *v10; // rax@20
  unsigned __int16 v11; // ax@20
  __int64 v12; // [sp+0h] [bp-A8h]@1
  CPlayer *v13; // [sp+20h] [bp-88h]@6
  _STORAGE_LIST::_db_con *v14; // [sp+28h] [bp-80h]@14
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-70h]@18
  bool v16; // [sp+54h] [bp-54h]@18
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v17; // [sp+58h] [bp-50h]@18
  char v18; // [sp+70h] [bp-38h]@19
  char v19; // [sp+71h] [bp-37h]@21
  char v20; // [sp+72h] [bp-36h]@22
  __int64 v21; // [sp+78h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v22; // [sp+80h] [bp-28h]@18
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+88h] [bp-20h]@18
  int v24; // [sp+90h] [bp-18h]@20
  CUnmannedTraderUserInfo *v25; // [sp+B0h] [bp+8h]@1
  _a_trade_adjust_price_request_clzo *v26; // [sp+C0h] [bp+18h]@1
  unsigned int *v27; // [sp+C8h] [bp+20h]@1

  v27 = dwOldPrice;
  v26 = pRequest;
  v25 = this;
  v6 = &v12;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v21 = -2i64;
  if ( (signed int)v25->m_wInx < 2532 )
  {
    v13 = &g_Player + v25->m_wInx;
    if ( v13->m_dwObjSerial == v25->m_dwUserSerial )
    {
      if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v13->m_id.wIndex) == 99 )
      {
        v8 = -52;
      }
      else
      {
        *pdwTax = v26->dwNewPrice / 0x3E8;
        if ( *pdwTax <= CPlayerDB::GetDalant(&v13->m_Param) )
        {
          if ( CUnmannedTraderRequestLimiter::IsEmpty(&v25->m_kRequestState) )
          {
            v14 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v13->m_Param.m_dbInven.m_nListNum, v26->wItemSerial);
            if ( v14 )
            {
              if ( v26->dwRegistSerial )
              {
                CUnmannedTraderUserInfo::Find(v25, &result, v26->dwRegistSerial);
                v22 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
                        &v25->m_vecRegistItemInfo,
                        &v17);
                _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v22;
                v16 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
                        (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
                        (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v22->_Mycont);
                std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v17);
                if ( v16 )
                {
                  v18 = 14;
                  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
                  v8 = v18;
                }
                else
                {
                  v9 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
                  *v27 = CUnmannedTraderRegistItemInfo::GetPrice(v9);
                  v24 = v26->wItemSerial;
                  v10 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
                  v11 = CUnmannedTraderRegistItemInfo::GetItemSerial(v10);
                  if ( v24 == v11 )
                  {
                    v20 = 0;
                    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
                    v8 = v20;
                  }
                  else
                  {
                    v19 = 25;
                    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
                    v8 = v19;
                  }
                }
              }
              else
              {
                v8 = 70;
              }
            }
            else
            {
              v8 = 14;
            }
          }
          else
          {
            v8 = 95;
          }
        }
        else
        {
          v8 = -55;
        }
      }
    }
    else
    {
      v8 = 99;
    }
  }
  else
  {
    v8 = 99;
  }
  return v8;
}
