/*
 * Function: ?extract_battery@AutominePersonal@@QEAA_NEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402DA880
 */

char __fastcall AutominePersonal::extract_battery(AutominePersonal *this, char bySlotIdx, _STORAGE_LIST::_db_con *pBattery)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@6
  unsigned __int8 v8; // [sp+24h] [bp-14h]@8
  AutominePersonal *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1

  v10 = bySlotIdx;
  v9 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)bySlotIdx < 2 )
  {
    v7 = 0;
    if ( AP_BatterySlot::extract(&v9->m_pBatterySlot[(unsigned __int8)bySlotIdx], pBattery) )
    {
      v8 = s_conver_index[(unsigned __int64)(unsigned __int8)v10];
      if ( AP_BatterySlot::get_dur(&v9->m_pBatterySlot[v8]) )
        v9->m_byUseBattery = v8;
      else
        v9->m_byUseBattery = -1;
      AutominePersonal::send_current_state(v9);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(&v9->m_logSysErr, "extract_battery(%d, n)::excess of max solt index", (unsigned __int8)bySlotIdx);
    result = 0;
  }
  return result;
}
