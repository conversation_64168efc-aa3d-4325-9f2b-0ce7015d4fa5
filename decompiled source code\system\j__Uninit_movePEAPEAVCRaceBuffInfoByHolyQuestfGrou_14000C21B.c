/*
 * Function: j_??$_Uninit_move@PEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000C21B
 */

CRaceBuffInfoByHolyQuestfGroup **__fastcall std::_Uninit_move<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>,std::_Undefined_move_tag>(CRaceBuffInfoByHolyQuestfGroup **_First, CRaceBuffInfoByHolyQuestfGroup **_Last, CRaceBuffInfoByHolyQuestfGroup **_Dest, std::allocator<CRaceBuffInfoByHolyQuestfGroup *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
