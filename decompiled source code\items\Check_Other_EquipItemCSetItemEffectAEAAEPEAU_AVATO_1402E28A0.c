/*
 * Function: ?Check_Other_EquipItem@CSetItemEffect@@AEAAEPEAU_AVATOR_DATA@@PEAU_SetItemEff_fld@@@Z
 * Address: 0x1402E28A0
 */

char __fastcall CSetItemEffect::Check_Other_EquipItem(CSetItemEffect *this, _AVATOR_DATA *pData, _SetItemEff_fld *pSetFld)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  char v7; // [sp+20h] [bp-78h]@4
  int j; // [sp+24h] [bp-74h]@4
  int n; // [sp+28h] [bp-70h]@4
  int v10; // [sp+2Ch] [bp-6Ch]@4
  char Dest; // [sp+38h] [bp-60h]@4
  char v12; // [sp+39h] [bp-5Fh]@4
  _base_fld *v13; // [sp+58h] [bp-40h]@4
  char v14; // [sp+64h] [bp-34h]@4
  char v15; // [sp+65h] [bp-33h]@4
  char v16; // [sp+66h] [bp-32h]@4
  char v17; // [sp+67h] [bp-31h]@4
  unsigned __int64 v18; // [sp+80h] [bp-18h]@4
  _AVATOR_DATA *v19; // [sp+A8h] [bp+10h]@1
  _SetItemEff_fld *v20; // [sp+B0h] [bp+18h]@1

  v20 = pSetFld;
  v19 = pData;
  v3 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  v7 = 0;
  n = 0;
  v10 = 0;
  Dest = 0;
  memset(&v12, 0, 9ui64);
  v13 = 0i64;
  v14 = 0;
  v15 = 0;
  v16 = 0;
  v17 = 0;
  for ( j = 0; j < 7; ++j )
  {
    v10 = v19->dbEquip.m_EmbellishList[j].Key.byTableCode;
    n = v19->dbEquip.m_EmbellishList[j].Key.wItemIndex;
    if ( v10 >= 0 && v10 < 37 )
    {
      v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v10, n);
      if ( v13 )
      {
        strcpy_0(&Dest, v13->m_strCode);
        if ( v14 || strcmp_0(&Dest, v20->m_strset_amulet1) )
        {
          if ( v15 || strcmp_0(&Dest, v20->m_strset_amulet2) )
          {
            if ( v16 || strcmp_0(&Dest, v20->m_strset_ring1) )
            {
              if ( !v17 && !strcmp_0(&Dest, v20->m_strset_ring2) )
              {
                ++v7;
                v17 = 1;
              }
            }
            else
            {
              ++v7;
              v16 = 1;
            }
          }
          else
          {
            ++v7;
            v15 = 1;
          }
        }
        else
        {
          ++v7;
          v14 = 1;
        }
      }
    }
  }
  return v7;
}
