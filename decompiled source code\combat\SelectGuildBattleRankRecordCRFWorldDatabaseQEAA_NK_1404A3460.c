/*
 * Function: ?SelectGuildBattleRankRecord@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404A3460
 */

char __fastcall CRFWorldDatabase::SelectGuildBattleRankRecord(CRFWorldDatabase *this, unsigned int dwGuildSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v5; // [sp+0h] [bp-4A8h]@1
  void *SQLStmt; // [sp+20h] [bp-488h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-480h]@21
  SQLLEN v8; // [sp+38h] [bp-470h]@21
  __int16 v9; // [sp+44h] [bp-464h]@9
  char Dest; // [sp+60h] [bp-448h]@4
  int TargetValue; // [sp+474h] [bp-34h]@21
  unsigned __int64 v12; // [sp+490h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+4B0h] [bp+8h]@1

  v13 = this;
  v2 = &v5;
  for ( i = 296i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pSelect_GuildBattleRank(%u) }", dwGuildSerial);
  if ( v13->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &Dest);
  if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
  {
    v9 = SQLExecDirectA_0(v13->m_hStmtSelect, &Dest, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 0;
      }
      else
      {
        SQLStmt = v13->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
        result = 0;
      }
    }
    else
    {
      v9 = SQLFetch_0(v13->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        if ( v9 != 100 )
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
        }
        if ( v13->m_hStmtSelect )
          SQLCloseCursor_0(v13->m_hStmtSelect);
        result = 0;
      }
      else
      {
        TargetValue = 0;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 1u, -18, &TargetValue, 0i64, &v8);
        if ( v9 == 100 )
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          result = 0;
        }
        else
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          if ( v13->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &Dest);
          result = 1;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 0;
  }
  return result;
}
