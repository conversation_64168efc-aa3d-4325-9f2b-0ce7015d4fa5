/*
 * Function: ??4CUnmannedTraderSubClassInfoForceLiverGrade@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x140383CE0
 */

CUnmannedTraderSubClassInfoForceLiverGrade *__fastcall CUnmannedTraderSubClassInfoForceLiverGrade::operator=(CUnmannedTraderSubClassInfoForceLiverGrade *this, CUnmannedTraderSubClassInfoForceLiverGrade *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderSubClassInfoForceLiverGrade *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderSubClassInfoForceLiverGrade *lhsa; // [sp+38h] [bp+10h]@1

  lhsa = lhs;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CUnmannedTraderSubClassInfo::Copy(
    (CUnmannedTraderSubClassInfo *)&v6->vfptr,
    (CUnmannedTraderSubClassInfo *)&lhs->vfptr);
  v6->m_byGrade = lhsa->m_byGrade;
  return v6;
}
