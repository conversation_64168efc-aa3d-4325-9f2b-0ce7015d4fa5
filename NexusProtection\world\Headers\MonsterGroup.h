#pragma once

#include <string>
#include <memory>
#include <vector>
#include <cstdint>
#include <unordered_map>
#include <functional>

namespace NexusProtection::World {

    // Forward declarations
    class Monster;
    class MonsterSpawnData;

    /**
     * @brief Monster Group Configuration
     * 
     * Represents a group of monsters with shared properties and behaviors.
     * This class manages monster group names, sub-monster counts, and group-level
     * operations for coordinated monster management.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - 0__monster_groupQEAAXZ_140279FB0.c (constructor)
     * - j_0__monster_groupQEAAXZ_14000BC26.c (jump table)
     */
    class MonsterGroup {
    public:
        // Constructor and destructor
        MonsterGroup();
        explicit MonsterGroup(const std::string& groupName, uint32_t subMonsterNum = 0);
        ~MonsterGroup() = default;

        // Copy and move semantics
        MonsterGroup(const MonsterGroup& other);
        MonsterGroup& operator=(const MonsterGroup& other);
        MonsterGroup(MonsterGroup&& other) noexcept;
        MonsterGroup& operator=(MonsterGroup&& other) noexcept;

        // Core functionality
        void Initialize();
        void Reset();
        bool IsValid() const;

        // Group name management
        void SetGroupName(const std::string& name);
        const std::string& GetGroupName() const { return m_groupName; }
        bool HasGroupName() const { return !m_groupName.empty(); }

        // Sub-monster count management
        void SetSubMonsterCount(uint32_t count);
        uint32_t GetSubMonsterCount() const { return m_subMonsterCount; }
        void IncrementSubMonsterCount() { ++m_subMonsterCount; }
        void DecrementSubMonsterCount() { if (m_subMonsterCount > 0) --m_subMonsterCount; }

        // Monster management
        void AddMonster(std::shared_ptr<Monster> monster);
        void RemoveMonster(const std::string& monsterId);
        std::shared_ptr<Monster> GetMonster(const std::string& monsterId) const;
        const std::vector<std::shared_ptr<Monster>>& GetMonsters() const { return m_monsters; }
        size_t GetActiveMonsterCount() const { return m_monsters.size(); }

        // Group operations
        void SpawnGroup(float x, float y, float z = 0.0f);
        void DespawnGroup();
        void UpdateGroup(float deltaTime);
        void SetGroupBehavior(const std::string& behavior);
        const std::string& GetGroupBehavior() const { return m_groupBehavior; }

        // Group state management
        bool IsGroupActive() const { return m_isActive; }
        void SetGroupActive(bool active) { m_isActive = active; }
        bool IsGroupSpawned() const { return m_isSpawned; }

        // Configuration management
        void SetGroupConfiguration(const std::string& name, uint32_t count, const std::string& behavior = "");
        void ClearGroup();

        // Utility methods
        std::string ToString() const;
        size_t GetMemoryUsage() const;

        // Legacy C interface compatibility
        const char* GetGroupNameCStr() const;
        void SetGroupNameFromCStr(const char* name);
        uint32_t GetSubMonsterCountLegacy() const { return m_subMonsterCount; }
        void SetSubMonsterCountLegacy(uint32_t count) { m_subMonsterCount = count; }

        // Validation
        bool ValidateGroupName() const;
        bool ValidateSubMonsterCount() const;
        bool ValidateGroupConfiguration() const;

        // Event handling
        void RegisterGroupEventHandler(const std::string& event, std::function<void(const MonsterGroup&)> handler);
        void TriggerGroupEvent(const std::string& event);

    private:
        // Member variables
        std::string m_groupName;                    // Group name (originally pszGroupName)
        uint32_t m_subMonsterCount{0};              // Sub-monster count (originally nSubMonsterNum)
        
        // Extended functionality
        std::vector<std::shared_ptr<Monster>> m_monsters;
        std::string m_groupBehavior;
        bool m_isActive{false};
        bool m_isSpawned{false};
        bool m_isInitialized{false};
        
        // Event handling
        std::unordered_map<std::string, std::function<void(const MonsterGroup&)>> m_eventHandlers;
        
        // Configuration limits
        static constexpr size_t MAX_GROUP_NAME_LENGTH = 128;
        static constexpr uint32_t MAX_SUB_MONSTER_COUNT = 1000;
        static constexpr size_t MAX_MONSTERS_PER_GROUP = 100;
        
        // Internal methods
        void ValidateAndTruncate();
        bool IsValidString(const std::string& str, size_t maxLength) const;
        void NotifyGroupStateChange();
        void CleanupInactiveMonsters();
    };

    /**
     * @brief Monster Group Factory
     * 
     * Factory class for creating MonsterGroup instances with proper configuration.
     */
    class MonsterGroupFactory {
    public:
        static std::unique_ptr<MonsterGroup> CreateMonsterGroup();
        static std::unique_ptr<MonsterGroup> CreateMonsterGroup(const std::string& groupName);
        static std::unique_ptr<MonsterGroup> CreateMonsterGroup(const std::string& groupName, uint32_t subMonsterCount);
        
        // Predefined group types
        static std::unique_ptr<MonsterGroup> CreateOrcGroup(const std::string& name = "OrcGroup");
        static std::unique_ptr<MonsterGroup> CreateGoblinGroup(const std::string& name = "GoblinGroup");
        static std::unique_ptr<MonsterGroup> CreateEliteGroup(const std::string& name = "EliteGroup");
        
        // Batch creation
        static std::vector<std::unique_ptr<MonsterGroup>> CreateMonsterGroups(
            const std::vector<std::pair<std::string, uint32_t>>& groupConfigs);
    };

    /**
     * @brief Monster Group Manager
     * 
     * Manages multiple monster groups and their interactions.
     */
    class MonsterGroupManager {
    public:
        MonsterGroupManager();
        ~MonsterGroupManager() = default;

        // Group management
        void AddGroup(std::unique_ptr<MonsterGroup> group);
        void RemoveGroup(const std::string& groupName);
        std::shared_ptr<MonsterGroup> GetGroup(const std::string& groupName) const;
        const std::vector<std::shared_ptr<MonsterGroup>>& GetAllGroups() const { return m_groups; }

        // Batch operations
        void SpawnAllGroups();
        void DespawnAllGroups();
        void UpdateAllGroups(float deltaTime);

        // Statistics
        size_t GetTotalGroupCount() const { return m_groups.size(); }
        size_t GetActiveGroupCount() const;
        size_t GetTotalMonsterCount() const;

    private:
        std::vector<std::shared_ptr<MonsterGroup>> m_groups;
        std::unordered_map<std::string, size_t> m_groupNameToIndex;
    };

    /**
     * @brief Monster Group Utilities
     * 
     * Utility functions for MonsterGroup management and operations.
     */
    namespace MonsterGroupUtils {
        // Validation utilities
        bool ValidateMonsterGroup(const MonsterGroup& group);
        bool ValidateGroupName(const std::string& name);
        bool ValidateSubMonsterCount(uint32_t count);
        
        // String utilities
        std::string SanitizeGroupName(const std::string& name);
        std::string GenerateUniqueGroupName(const std::string& baseName);
        
        // Memory utilities
        size_t CalculateMemoryFootprint(const MonsterGroup& group);
        
        // Configuration utilities
        void ConfigureDefaultGroup(MonsterGroup& group);
        void OptimizeGroupConfiguration(MonsterGroup& group);
        
        // Conversion utilities
        std::string MonsterGroupToJson(const MonsterGroup& group);
        std::unique_ptr<MonsterGroup> MonsterGroupFromJson(const std::string& json);
        
        // Group analysis
        std::vector<std::string> AnalyzeGroupComposition(const MonsterGroup& group);
        float CalculateGroupThreatLevel(const MonsterGroup& group);
    }

    // Legacy C interface
    extern "C" {
        // Legacy structure for compatibility
        struct _monster_group {
            char* pszGroupName;         // Group name pointer
            uint32_t nSubMonsterNum;    // Sub-monster count
            char padding[32];           // Additional padding for original structure size
        };

        // Legacy function declarations
        void __monster_group_Constructor(_monster_group* this_ptr);
        void __monster_group_Destructor(_monster_group* this_ptr);
        
        // Legacy utility functions
        void __monster_group_SetGroupName(_monster_group* this_ptr, const char* name);
        void __monster_group_SetSubMonsterCount(_monster_group* this_ptr, uint32_t count);
        const char* __monster_group_GetGroupName(_monster_group* this_ptr);
        uint32_t __monster_group_GetSubMonsterCount(_monster_group* this_ptr);
        void __monster_group_Initialize(_monster_group* this_ptr);
        void __monster_group_Reset(_monster_group* this_ptr);
    }

} // namespace NexusProtection::World

// Legacy global compatibility
extern NexusProtection::World::MonsterGroup* g_pMonsterGroup;
