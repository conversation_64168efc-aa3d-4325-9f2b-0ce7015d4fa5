/*
 * Function: j_??$_Fill_n@PEAPEAVTRC_AutoTrade@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVTRC_AutoTrade@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000A164
 */

void __fastcall std::_Fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::random_access_iterator_tag>(TRC_AutoTrade **_First, unsigned __int64 _Count, TRC_AutoTrade *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<TRC_AutoTrade * *,unsigned __int64,TRC_AutoTrade *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
