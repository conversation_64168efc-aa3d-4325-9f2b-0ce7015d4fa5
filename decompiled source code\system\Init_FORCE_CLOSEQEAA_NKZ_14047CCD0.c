/*
 * Function: ?Init@_FORCE_CLOSE@@QEAA_NK@Z
 * Address: 0x14047CCD0
 */

char __fastcall _FORCE_CLOSE::Init(_FORCE_CLOSE *this, unsigned int dwNodeNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-48h]@1
  unsigned int dwIndex; // [sp+20h] [bp-28h]@7
  _FORCE_CLOSE::__FD_NODE *v7; // [sp+28h] [bp-20h]@7
  __int64 v8; // [sp+30h] [bp-18h]@7
  _FORCE_CLOSE *v9; // [sp+50h] [bp+8h]@1
  unsigned int dwMaxBufNum; // [sp+58h] [bp+10h]@1

  dwMaxBufNum = dwNodeNum;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwNodeNum && dwNodeNum <= 0xFFFF )
  {
    v9->m_dwNodeNum = dwNodeNum;
    v8 = dwNodeNum;
    v7 = (_FORCE_CLOSE::__FD_NODE *)operator new(saturated_mul(0xCui64, dwNodeNum));
    v9->m_pFDData = v7;
    CNetTimer::BeginTimer(&v9->m_tmFD, 0x9C4u);
    CNetIndexList::SetList(&v9->m_listFD, dwMaxBufNum);
    CNetIndexList::SetList(&v9->m_listFDEmpty, dwMaxBufNum);
    for ( dwIndex = 0; dwIndex < dwMaxBufNum; ++dwIndex )
      CNetIndexList::PushNode_Back(&v9->m_listFDEmpty, dwIndex);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
