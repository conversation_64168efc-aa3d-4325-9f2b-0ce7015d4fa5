/*
 * Function: ?pc_CharacterRename<PERSON>heck@CPlayer@@QEAAEPEBD@Z
 * Address: 0x1400B5C40
 */

char __fastcall CPlayer::pc_CharacterRenameCheck(CPlayer *this, const char *strCharacterName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v4; // rax@12
  __int64 v6; // [sp+0h] [bp-78h]@1
  char v7; // [sp+20h] [bp-58h]@4
  char _Dest[17]; // [sp+38h] [bp-40h]@4
  int j; // [sp+54h] [bp-24h]@7
  unsigned __int64 v10; // [sp+60h] [bp-18h]@4

  v2 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v6 ^ _security_cookie;
  v7 = 0;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0x10ui64);
  strncpy_s<17>((char (*)[17])_Dest, strCharacterName, 0x10ui64);
  if ( _Dest[0] == 42 || _Dest[0] == 33 )
    v7 = 5;
  for ( j = 0; j < 3; ++j )
  {
    if ( !strcmp_0(_Dest, wszNonMakeName[j]) )
    {
      v7 = 6;
      break;
    }
  }
  v4 = CTSingleton<CNationSettingManager>::Instance();
  if ( !CNationSettingManager::IsNormalString(v4, _Dest) )
    v7 = 5;
  return v7;
}
