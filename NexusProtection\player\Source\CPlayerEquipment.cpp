/*
 * CPlayerEquipment.cpp - Modern Player Equipment System Implementation
 * Refactored from decompiled pc_EquipPart function (Address: 0x1400AD960)
 * Provides type-safe, modern C++ implementation with comprehensive error handling
 */

#include "../Headers/CPlayerEquipment.h"
#include "../../common/Headers/Logger.h"
#include "../../player/Headers/CPlayer.h"

#include <algorithm>
#include <chrono>
#include <stdexcept>
#include <cstring>

// External references to legacy systems
extern "C" {
    // Legacy function declarations
    struct _STORAGE_POS_INDIV;
    struct _STORAGE_LIST;
    struct _base_fld;
    struct _effect_parameter;
    
    extern void memcpy_0(void* dest, const void* src, size_t count);
    extern void memset_0(void* dest, int value, size_t count);
    extern _STORAGE_LIST* _STORAGE_LIST_GetPtrFromSerial(_STORAGE_LIST* pList, uint16_t serial);
    extern _base_fld* CRecordData_GetRecord(void* pRecordData, uint16_t itemIndex);
    extern bool _effect_parameter_GetEff_State(_effect_parameter* pEP, int effectType);
    extern uint8_t GetItemEquipGrade(uint8_t tableCode, uint16_t itemIndex);
    extern bool CPlayer_IsEquipAbleGrade(CPlayer* pPlayer, uint8_t grade);
    extern bool CPlayer_check_equip_part(CPlayer* pPlayer, _STORAGE_LIST* pItem);
    extern bool CPlayer_Emb_DelStorage(CPlayer* pPlayer, int listCode, uint8_t storageIndex, 
                                      int param1, int param2, const char* errorPos);
    extern bool CPlayer_Emb_AddStorage(CPlayer* pPlayer, int listCode, void* pStorageCon, 
                                      int param1, int param2);
    extern void CPlayer_Emb_EquipLink(CPlayer* pPlayer);
    extern void CPlayer_SendMsg_EquipPartResult(CPlayer* pPlayer, int8_t errorCode);
    extern void _STORAGE_LIST_db_con_Constructor(void* pDbCon);
}

namespace NexusProtection {
namespace Player {

/**
 * Constructor
 */
CPlayerEquipment::CPlayerEquipment() 
    : m_bDetailedLogging(false) {
    
    Logger::Debug("CPlayerEquipment::CPlayerEquipment - Player equipment system created");
}

/**
 * Main equipment function - refactored from pc_EquipPart
 */
EquipmentResult CPlayerEquipment::EquipItem(const EquipmentContext& context) {
    auto startTime = std::chrono::high_resolution_clock::now();
    
    try {
        if (m_bDetailedLogging) {
            Logger::Debug("CPlayerEquipment::EquipItem - Starting equipment operation");
        }
        
        // Validate context
        if (!context.IsValid()) {
            return CreateResult(EquipmentErrorCode::SystemError, startTime, "Invalid context");
        }
        
        CPlayer* pPlayer = context.pPlayer;
        _STORAGE_POS_INDIV* pItemPos = context.pItemPosition;
        
        // Initialize stack variables (equivalent to original stack initialization)
        // Original: for ( i = 68i64; i; --i ) { *(_DWORD *)v2 = -858993460; v2 = (__int64 *)((char *)v2 + 4); }
        
        // Step 1: Validate player state (original lines 35-42)
        EquipmentErrorCode stateError = ValidatePlayerState(pPlayer);
        if (stateError != EquipmentErrorCode::Success) {
            auto result = CreateResult(stateError, startTime);
            SendEquipmentResult(pPlayer, stateError);
            return result;
        }
        
        // Step 2: Get item from inventory (original lines 44-50)
        _STORAGE_LIST* pInventoryList = &pPlayer->m_Param.m_dbInven;  // Original: v8
        _STORAGE_LIST* pFixingItem = _STORAGE_LIST_GetPtrFromSerial(pInventoryList, pItemPos->wItemSerial);
        
        if (!pFixingItem) {
            auto result = CreateResult(EquipmentErrorCode::ItemNotFound, startTime, "Item not found in inventory");
            SendEquipmentResult(pPlayer, EquipmentErrorCode::ItemNotFound);
            return result;
        }
        
        // Step 3: Validate equipment requirements (original lines 51-85)
        EquipmentErrorCode validationError = ValidateEquipmentRequirements(pPlayer, pFixingItem);
        if (validationError != EquipmentErrorCode::Success) {
            auto result = CreateResult(validationError, startTime);
            SendEquipmentResult(pPlayer, validationError);
            return result;
        }
        
        // Step 4: Get current equipped item (original lines 61-65)
        EquipmentSlot equipSlot = GetEquipmentSlot(pFixingItem->m_byTableCode);
        _STORAGE_LIST* pCurrentEquipped = nullptr;
        if (equipSlot != EquipmentSlot::Invalid) {
            pCurrentEquipped = &pPlayer->m_Param.m_dbEquip.m_pStorageList[static_cast<int>(equipSlot)];
        }
        
        // Step 5: Perform storage operations (original lines 95-156)
        if (!PerformStorageOperations(context, pCurrentEquipped, pFixingItem)) {
            auto result = CreateResult(EquipmentErrorCode::SystemError, startTime, "Storage operation failed");
            SendEquipmentResult(pPlayer, EquipmentErrorCode::SystemError);
            return result;
        }
        
        // Step 6: Update equipment links (original line 137)
        CPlayer_Emb_EquipLink(pPlayer);
        
        // Success
        auto result = CreateResult(EquipmentErrorCode::Success, startTime);
        SendEquipmentResult(pPlayer, EquipmentErrorCode::Success);
        
        if (m_equipmentCallback) {
            m_equipmentCallback(result);
        }
        
        return result;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerEquipment::EquipItem - Exception: %s", e.what());
        auto result = CreateResult(EquipmentErrorCode::SystemError, startTime, e.what());
        SendEquipmentResult(context.pPlayer, EquipmentErrorCode::SystemError);
        return result;
    }
}

/**
 * Validate player state for equipment operations
 */
EquipmentErrorCode CPlayerEquipment::ValidatePlayerState(CPlayer* pPlayer) {
    try {
        if (!pPlayer) {
            return EquipmentErrorCode::SystemError;
        }
        
        // Check effect states (original lines 35-42)
        // if ( _effect_parameter::GetEff_State(&v16->m_EP, 20) ) v7 = 8;
        // else if ( _effect_parameter::GetEff_State(&v16->m_EP, 28) ) v7 = 8;
        if (_effect_parameter_GetEff_State(&pPlayer->m_EP, 20) ||
            _effect_parameter_GetEff_State(&pPlayer->m_EP, 28)) {
            return EquipmentErrorCode::PlayerStateError;
        }
        
        return EquipmentErrorCode::Success;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerEquipment::ValidatePlayerState - Exception: %s", e.what());
        return EquipmentErrorCode::SystemError;
    }
}

/**
 * Validate equipment requirements
 */
EquipmentErrorCode CPlayerEquipment::ValidateEquipmentRequirements(CPlayer* pPlayer, _STORAGE_LIST* pItem) {
    try {
        if (!pPlayer || !pItem) {
            return EquipmentErrorCode::SystemError;
        }
        
        // Check if item is equipment (original line 55: if ( pFixingItem->m_byTableCode < 8 ))
        if (!IsEquipmentTableCode(pItem->m_byTableCode)) {
            return EquipmentErrorCode::InvalidTableCode;
        }
        
        // Check if item is locked (original lines 57-60)
        if (pItem->m_bLock) {
            return EquipmentErrorCode::ItemLocked;
        }
        
        // Get equipment slot and check if occupied (original lines 61-68)
        EquipmentSlot slot = GetEquipmentSlot(pItem->m_byTableCode);
        if (slot != EquipmentSlot::Invalid) {
            _STORAGE_LIST* pCurrentEquipped = &pPlayer->m_Param.m_dbEquip.m_pStorageList[static_cast<int>(slot)];
            if (pCurrentEquipped && pCurrentEquipped->m_bLoad && pCurrentEquipped->m_bLock) {
                return EquipmentErrorCode::ItemLocked;
            }
        }
        
        // Check equipment grade (original lines 70-76)
        uint8_t itemGrade = GetItemEquipGrade(pItem->m_byTableCode, pItem->m_wItemIndex);
        if (!CPlayer_IsEquipAbleGrade(pPlayer, itemGrade)) {
            return EquipmentErrorCode::GradeRequirementFailed;
        }
        
        // Check equipment part requirements (original lines 77-79)
        if (!CPlayer_check_equip_part(pPlayer, pItem)) {
            return EquipmentErrorCode::EquipmentRequirementFailed;
        }
        
        return EquipmentErrorCode::Success;
        
    } catch (const std::exception& e) {
        Logger::Error("CPlayerEquipment::ValidateEquipmentRequirements - Exception: %s", e.what());
        return EquipmentErrorCode::SystemError;
    }
}

/**
 * Get equipment slot from table code
 */
EquipmentSlot CPlayerEquipment::GetEquipmentSlot(uint8_t tableCode) {
    if (tableCode < 8) {
        return static_cast<EquipmentSlot>(tableCode);
    }
    return EquipmentSlot::Invalid;
}

/**
 * Check if table code represents equipment
 */
bool CPlayerEquipment::IsEquipmentTableCode(uint8_t tableCode) {
    return tableCode < 8;  // Equipment items have table codes 0-7
}

/**
 * Set equipment operation callback
 */
void CPlayerEquipment::SetEquipmentCallback(std::function<void(const EquipmentResult&)> callback) {
    m_equipmentCallback = std::move(callback);
}

/**
 * Enable/disable detailed logging
 */
void CPlayerEquipment::SetDetailedLogging(bool enable) {
    m_bDetailedLogging = enable;
}

/**
 * Create equipment result with timing
 */
EquipmentResult CPlayerEquipment::CreateResult(EquipmentErrorCode errorCode,
                                             std::chrono::high_resolution_clock::time_point startTime,
                                             const std::string& errorMessage) {
    EquipmentResult result;
    result.errorCode = errorCode;
    result.errorMessage = errorMessage;
    result.executionTime = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::high_resolution_clock::now() - startTime);
    
    return result;
}

/**
 * Send equipment result to player
 */
void CPlayerEquipment::SendEquipmentResult(CPlayer* pPlayer, EquipmentErrorCode errorCode) {
    if (pPlayer) {
        CPlayer_SendMsg_EquipPartResult(pPlayer, static_cast<int8_t>(errorCode));
    }
}

} // namespace Player
} // namespace NexusProtection
