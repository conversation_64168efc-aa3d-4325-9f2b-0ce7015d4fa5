/*
 * Function: j_??0?$_Vector_val@VCMoveMapLimitRightInfo@@V?$allocator@VCMoveMapLimitRightInfo@@@std@@@std@@IEAA@V?$allocator@VCMoveMapLimitRightInfo@@@1@@Z
 * Address: 0x14000DA26
 */

void __fastcall std::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(std::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo> > *this, std::allocator<CMoveMapLimitRightInfo> _Al)
{
  std::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>::_Vector_val<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>>(
    this,
    _Al);
}
