/*
 * Function: ??$_Uninit_copy@V?$_Vector_const_iterator@VInteger@CryptoPP@@V?$allocator@VInteger@CryptoPP@@@std@@@std@@PEAVInteger@CryptoPP@@V?$allocator@VInteger@CryptoPP@@@2@@std@@YAPEAVInteger@CryptoPP@@V?$_Vector_const_iterator@VInteger@CryptoPP@@V?$allocator@VInteger@CryptoPP@@@std@@@0@0PEAV12@AEAV?$allocator@VInteger@CryptoPP@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A20E0
 */

__int64 __fastcall std::_Uninit_copy<std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>,CryptoPP::Integer *,std::allocator<CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  __int64 v4; // rax@3
  __int64 v6; // [sp+60h] [bp+8h]@1
  __int64 v7; // [sp+70h] [bp+18h]@1
  __int64 v8; // [sp+78h] [bp+20h]@1

  v8 = a4;
  v7 = a3;
  v6 = a1;
  while ( std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator!=() )
  {
    LODWORD(v4) = std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator*(v6);
    std::allocator<CryptoPP::Integer>::construct(v8, v7, v4);
    v7 += 40i64;
    std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::operator++(v6);
  }
  std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::~_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>();
  std::_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::~_Vector_const_iterator<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>();
  return v7;
}
