/*
 * Function: ??$_Ptr_cat@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@0@Z
 * Address: 0x14031F400
 */

char __fastcall std::_Ptr_cat<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__formal, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *a2)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+24h] [bp-24h]@4

  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  return v6;
}
