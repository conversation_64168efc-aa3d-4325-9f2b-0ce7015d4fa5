/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@stdext@@YAPEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@Z
 * Address: 0x14001073A
 */

CRaceBuffInfoByHolyQuestfGroup **__fastcall stdext::_Unchecked_uninitialized_move<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(CRaceBuffInfoByHolyQuestfGroup **_First, CRaceBuffInfoByHolyQuestfGroup **_Last, CRaceBuffInfoByHolyQuestfGroup **_Dest, std::allocator<CRaceBuffInfoByHolyQuestfGroup *> *_Al)
{
  return stdext::_Unchecked_uninitialized_move<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
