/*
 * Function: ?IsActableClassSkill@CPlayerDB@@QEAA_NPEADPEAH@Z
 * Address: 0x14010BF40
 */

bool __fastcall CPlayerDB::IsActableClassSkill(CPlayerDB *this, char *pszSkillCode, int *pnClassGrade)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@23
  __int64 v6; // [sp+0h] [bp-58h]@1
  _class_fld *v7; // [sp+20h] [bp-38h]@6
  int v8; // [sp+28h] [bp-30h]@8
  __int64 v9; // [sp+30h] [bp-28h]@8
  int j; // [sp+38h] [bp-20h]@8
  __int64 v11; // [sp+40h] [bp-18h]@10
  char v12; // [sp+48h] [bp-10h]@11
  int k; // [sp+4Ch] [bp-Ch]@11
  CPlayerDB *v14; // [sp+60h] [bp+8h]@1
  const char *Str; // [sp+68h] [bp+10h]@1
  int *v16; // [sp+70h] [bp+18h]@1

  v16 = pnClassGrade;
  Str = pszSkillCode;
  v14 = this;
  v3 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( pnClassGrade )
    *pnClassGrade = -1;
  v7 = *v14->m_ppHistoryEffect[1];
  if ( !v7 )
    v7 = v14->m_pClassData;
  v8 = strlen_0(pszSkillCode);
  v9 = 0i64;
  for ( j = 0; j < 4; ++j )
  {
    v11 = (__int64)*v14->m_ppHistoryEffect[j];
    if ( !v11 )
      break;
    v12 = 0;
    for ( k = 0; k < 10 && strncmp((const char *)(v11 + ((signed __int64)k << 6) + 796), "-1", 2ui64); ++k )
    {
      if ( !strncmp((const char *)(v11 + ((signed __int64)k << 6) + 796), Str, v8) )
      {
        v12 = 1;
        break;
      }
    }
    if ( v12 )
    {
      v9 = v11;
      break;
    }
  }
  if ( v9 )
  {
    if ( v16 )
      *v16 = *(_DWORD *)(v9 + 80);
    result = v7->m_nClass == *(_DWORD *)(v9 + 72);
  }
  else
  {
    result = 0;
  }
  return result;
}
