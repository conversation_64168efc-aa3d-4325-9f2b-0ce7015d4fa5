/*
 * Function: ?Flip@CReservedGuildSchedulePage@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403CC090
 */

void __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::Flip(GUILD_BATTLE::CReservedGuildSchedulePage *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v3; // rax@7
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@5
  int v6; // [sp+24h] [bp-14h]@5
  unsigned int v7; // [sp+28h] [bp-10h]@7
  GUILD_BATTLE::CReservedGuildSchedulePage *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8->m_pkList )
  {
    v5 = GetCurDay();
    v6 = GetNextDay();
    if ( v5 != -1 && v6 != -1 )
    {
      v8->m_pkList->dwVer = ++v8->m_dwVer;
      v8->m_pkList->byDate = 0;
      v8->m_pkList->byToDay = v5;
      v8->m_pkList->byTomorrow = v6;
    }
    else
    {
      v7 = v8->m_ucPageInx;
      v3 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v3,
        "CReservedGuildSchedulePage::InitClear(%u) : -1 == iToDay || -1 == iTomorrow Fail!",
        v7);
    }
  }
}
