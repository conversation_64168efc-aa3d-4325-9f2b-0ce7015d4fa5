/*
 * Function: ?Initialize@?$DL_PublicKey_EC@VECP@CryptoPP@@@CryptoPP@@QEAAXAEBVECP@2@AEBUECPPoint@2@AEBVInteger@2@1@Z
 * Address: 0x140558B70
 */

int __fastcall CryptoPP::DL_PublicKey_EC<CryptoPP::ECP>::Initialize(__int64 a1, __int64 a2, __int64 a3, __int64 a4, __int64 a5)
{
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP> *v5; // rax@1
  __int64 v7; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1

  v9 = a3;
  v8 = a2;
  v7 = a1;
  CryptoPP::Integer::Zero();
  v5 = CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>,CryptoPP::OID>::AccessGroupParameters((CryptoPP::DL_KeyImpl<CryptoPP::X509PublicKey,CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>,CryptoPP::OID> *)(v7 + 8));
  CryptoPP::DL_GroupParameters_EC<CryptoPP::ECP>::Initialize((__int64)v5, v8, v9);
  return (*(int (__fastcall **)(__int64, __int64))(*(_QWORD *)v7 + 24i64))(v7, a5);
}
