/*
 * Function: ?Release@CRaceBuffInfoByHolyQuestfGroup@@QEAA_NHPEAVCPlayer@@@Z
 * Address: 0x1403B4D10
 */

bool __fastcall CRaceBuffInfoByHolyQuestfGroup::Release(CRaceBuffInfoByHolyQuestfGroup *this, int iResultType, CPlayer *pkDest)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int64 v5; // rax@7
  bool result; // al@8
  CRaceBuffInfoByHolyQuest **v7; // rax@9
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned __int64 v9; // [sp+20h] [bp-18h]@7
  CRaceBuffInfoByHolyQuestfGroup *v10; // [sp+40h] [bp+8h]@1
  int v11; // [sp+48h] [bp+10h]@1
  CPlayer *pkDesta; // [sp+50h] [bp+18h]@1

  pkDesta = pkDest;
  v11 = iResultType;
  v10 = this;
  v3 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( iResultType >= 0
    && iResultType < 4
    && pkDest
    && (v9 = iResultType,
        v5 = std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::size(&v10->m_vecInfo),
        v9 < v5) )
  {
    v7 = std::vector<CRaceBuffInfoByHolyQuest *,std::allocator<CRaceBuffInfoByHolyQuest *>>::operator[](
           &v10->m_vecInfo,
           v11);
    result = CRaceBuffInfoByHolyQuest::Release(*v7, pkDesta);
  }
  else
  {
    result = 0;
  }
  return result;
}
