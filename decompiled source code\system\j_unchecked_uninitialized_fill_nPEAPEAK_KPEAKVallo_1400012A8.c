/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAPEAK_KPEAKV?$allocator@PEAK@std@@@stdext@@YAXPEAPEAK_KAEBQEAKAEAV?$allocator@PEAK@std@@@Z
 * Address: 0x1400012A8
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<unsigned long * *,unsigned __int64,unsigned long *,std::allocator<unsigned long *>>(unsigned int **_First, unsigned __int64 _Count, unsigned int *const *_Val, std::allocator<unsigned long *> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<unsigned long * *,unsigned __int64,unsigned long *,std::allocator<unsigned long *>>(
    _First,
    _Count,
    _Val,
    _<PERSON>);
}
