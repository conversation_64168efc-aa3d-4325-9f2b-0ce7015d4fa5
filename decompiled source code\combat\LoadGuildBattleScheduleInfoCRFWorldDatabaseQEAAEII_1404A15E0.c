/*
 * Function: ?LoadGuildBattleScheduleInfo@CRFWorldDatabase@@QEAAEIIPEAU_worlddb_guild_battle_schedule_list@@@Z
 * Address: 0x1404A15E0
 */

char __fastcall CRFWorldDatabase::LoadGuildBattleScheduleInfo(CRFWorldDatabase *this, unsigned int uiStartListID, unsigned int uiScheduleUnitCnt, _worlddb_guild_battle_schedule_list *pkInfo)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v7; // [sp+0h] [bp-4F8h]@1
  void *SQLStmt; // [sp+20h] [bp-4D8h]@16
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-4D0h]@20
  SQLLEN v10; // [sp+38h] [bp-4C0h]@20
  __int16 v11; // [sp+44h] [bp-4B4h]@12
  char Dest; // [sp+60h] [bp-498h]@7
  unsigned int v13; // [sp+464h] [bp-94h]@17
  __int16 TargetValue; // [sp+478h] [bp-80h]@20
  unsigned __int16 v15; // [sp+47Ah] [bp-7Eh]@20
  unsigned __int16 v16; // [sp+47Ch] [bp-7Ch]@20
  unsigned __int16 v17; // [sp+47Eh] [bp-7Ah]@20
  unsigned __int16 v18; // [sp+480h] [bp-78h]@20
  unsigned __int16 v19; // [sp+482h] [bp-76h]@20
  tm _Tm; // [sp+4A8h] [bp-50h]@20
  __int64 v21; // [sp+4D8h] [bp-20h]@20
  unsigned __int64 v22; // [sp+4E8h] [bp-10h]@4
  CRFWorldDatabase *v23; // [sp+500h] [bp+8h]@1
  unsigned int v24; // [sp+510h] [bp+18h]@1
  _worlddb_guild_battle_schedule_list *v25; // [sp+518h] [bp+20h]@1

  v25 = pkInfo;
  v24 = uiScheduleUnitCnt;
  v23 = this;
  v4 = &v7;
  for ( i = 316i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v22 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( uiScheduleUnitCnt && pkInfo )
  {
    sprintf(&Dest, "{ CALL pSelect_ReservedGuildBattleSchedule( %u, %u ) }", uiStartListID, uiStartListID);
    if ( v23->m_bSaveDBLog )
      CRFNewDatabase::Log((CRFNewDatabase *)&v23->vfptr, &Dest);
    if ( v23->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v23->vfptr) )
    {
      v11 = SQLExecDirect_0(v23->m_hStmtSelect, &Dest, -3);
      if ( v11 && v11 != 1 )
      {
        if ( v11 == 100 )
        {
          result = 2;
        }
        else
        {
          SQLStmt = v23->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v23->vfptr, v11, &Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v23->vfptr, v11, v23->m_hStmtSelect);
          result = 1;
        }
      }
      else
      {
        v13 = 0;
        v25->wCount = 0;
        do
        {
          v11 = SQLFetch_0(v23->m_hStmtSelect);
          if ( v11 && v11 != 1 )
            break;
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v23->m_hStmtSelect, 1u, -18, &v25->list[v13], 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v23->m_hStmtSelect, 2u, -18, &v25->list[v13].dwSLID, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v23->m_hStmtSelect, 3u, -6, &v25->list[v13].ucState, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v23->m_hStmtSelect, 4u, 93, &TargetValue, 0i64, &v10);
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v23->m_hStmtSelect, 5u, -17, &v25->list[v13].wTumeMin, 0i64, &v10);
          _Tm.tm_year = TargetValue - 1900;
          _Tm.tm_mon = v15 - 1;
          _Tm.tm_mday = v16;
          _Tm.tm_hour = v17;
          _Tm.tm_min = v18;
          _Tm.tm_sec = v19;
          _Tm.tm_isdst = -1;
          v21 = mktime_3(&_Tm);
          if ( v21 == -1 )
            v21 = 0i64;
          v25->list[v13++].tStartTime = v21;
          v25->wCount = v13;
        }
        while ( v24 > v13 );
        if ( v23->m_hStmtSelect )
          SQLCloseCursor_0(v23->m_hStmtSelect);
        if ( v23->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v23->vfptr, "%s Success", &Dest);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v23->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
