/*
 * Function: ?GetCountOfItem@si_interpret@@QEAAEH@Z
 * Address: 0x1402E3B90
 */

char __fastcall si_interpret::GetCountOfItem(si_interpret *this, int idx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v5; // [sp+0h] [bp-28h]@1
  si_interpret *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( idx >= 0 && idx <= 8 )
    result = si_effect::GetCountOfItem(&v6->effect_type[idx]);
  else
    result = 100;
  return result;
}
