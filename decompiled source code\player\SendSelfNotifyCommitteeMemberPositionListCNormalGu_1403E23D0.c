/*
 * Function: ?SendSelfNotifyCommitteeMemberPositionList@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403E23D0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendSelfNotifyCommitteeMemberPositionList(GUILD_BATTLE::CNormalGuildBattleGuild *this, CPlayer *pkPlayer)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  char szMsg[2]; // [sp+38h] [bp-70h]@10
  __int16 v6; // [sp+3Ah] [bp-6Eh]@10
  char v7; // [sp+3Ch] [bp-6Ch]@4
  unsigned int v8; // [sp+3Dh] [bp-6Bh]@10
  char pbyType; // [sp+64h] [bp-44h]@4
  char v10; // [sp+65h] [bp-43h]@4
  CPlayer *v11; // [sp+78h] [bp-30h]@4
  int j; // [sp+80h] [bp-28h]@4
  unsigned __int64 v13; // [sp+90h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v14; // [sp+B0h] [bp+8h]@1
  CPlayer *v15; // [sp+B8h] [bp+10h]@1

  v15 = pkPlayer;
  v14 = this;
  v2 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v4 ^ _security_cookie;
  v7 = v14->m_byColorInx;
  pbyType = 27;
  v10 = 82;
  v11 = 0i64;
  for ( j = 0; j < 10; ++j )
  {
    if ( v14->m_pkNotifyPositionMember[j] )
    {
      if ( v15->m_dwObjSerial != GUILD_BATTLE::CNormalGuildBattleGuildMember::GetSerial(v14->m_pkNotifyPositionMember[j]) )
      {
        v11 = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(v14->m_pkNotifyPositionMember[j]);
        v8 = v11->m_dwObjSerial;
        *(_WORD *)szMsg = (signed int)ffloor(v11->m_fCurPos[0]);
        v6 = (signed int)ffloor(v11->m_fCurPos[2]);
        CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, szMsg, 9u);
      }
    }
  }
}
