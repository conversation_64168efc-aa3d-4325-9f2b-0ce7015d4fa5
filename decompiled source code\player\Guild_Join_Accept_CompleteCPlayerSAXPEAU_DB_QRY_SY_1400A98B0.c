/*
 * Function: ?Guild_Join_Accept_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400A98B0
 */

void __usercall CPlayer::Guild_Join_Accept_Complete(_DB_QRY_SYN_DATA *pData@<rcx>, double a2@<xmm0>)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@12
  CPlayer::CashChangeStateFlag *v5; // rax@14
  char *v6; // rax@14
  CGuildBattleController *v7; // rax@14
  __int64 v8; // [sp+0h] [bp-198h]@1
  unsigned int dwOKSerial; // [sp+20h] [bp-178h]@14
  int nMemNum; // [sp+28h] [bp-170h]@14
  char *pszFileName; // [sp+30h] [bp-168h]@14
  int v12; // [sp+40h] [bp-158h]@4
  int v13; // [sp+44h] [bp-154h]@4
  unsigned int v14; // [sp+48h] [bp-150h]@4
  unsigned int dwApplierSerial; // [sp+4Ch] [bp-14Ch]@4
  unsigned int dwMemberSerial; // [sp+50h] [bp-148h]@4
  char *v17; // [sp+58h] [bp-140h]@4
  CPlayer *v18; // [sp+60h] [bp-138h]@4
  CGuild *v19; // [sp+68h] [bp-130h]@7
  _guild_member_info *v20; // [sp+70h] [bp-128h]@10
  _guild_applier_info *v21; // [sp+78h] [bp-120h]@11
  _guild_member_info pSheet; // [sp+88h] [bp-110h]@12
  _guild_member_info *p; // [sp+C8h] [bp-D0h]@12
  char szTran; // [sp+E0h] [bp-B8h]@14
  CPlayer::CashChangeStateFlag v25; // [sp+170h] [bp-28h]@14
  char *v26; // [sp+178h] [bp-20h]@14
  int n; // [sp+180h] [bp-18h]@14
  unsigned __int64 v28; // [sp+188h] [bp-10h]@4
  _DB_QRY_SYN_DATA *v29; // [sp+1A0h] [bp+8h]@1

  v29 = pData;
  v2 = &v8;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v28 = (unsigned __int64)&v8 ^ _security_cookie;
  v12 = -1;
  v13 = -1;
  v14 = -1;
  dwApplierSerial = -1;
  dwMemberSerial = -1;
  v17 = v29->m_sData;
  v12 = *(_DWORD *)&v29->m_sData[0];
  v13 = *(_DWORD *)&v29->m_sData[4];
  v14 = *(_DWORD *)&v29->m_sData[8];
  dwApplierSerial = *(_DWORD *)&v29->m_sData[12];
  dwMemberSerial = *(_DWORD *)&v29->m_sData[16];
  v18 = &g_Player + v14;
  if ( v18->m_bLive && v18->m_dwObjSerial == dwApplierSerial )
    v18->m_Param.m_bGuildLock = 0;
  v19 = &g_Guild[v12];
  if ( v19->m_dwSerial == v13 )
  {
    --v19->m_nTempMemberNum;
    if ( v19->m_nMemberNum < 50 && !v29->m_byResult )
    {
      v20 = CGuild::GetMemberFromSerial(v19, dwMemberSerial);
      if ( v20 )
      {
        v21 = CGuild::GetApplierFromSerial(v19, dwApplierSerial);
        if ( v21 )
        {
          v18 = v21->pPlayer;
          CPlayerDB::SetClassInGuild(&v18->m_Param, 0);
          v18->m_Param.m_pApplyGuild = 0i64;
          CGuild::PopApplier(v19, dwApplierSerial, 0);
          _guild_member_info::_guild_member_info(&pSheet);
          pSheet.dwSerial = v18->m_dwObjSerial;
          v4 = CPlayerDB::GetCharNameW(&v18->m_Param);
          strcpy_0(pSheet.wszName, v4);
          pSheet.byLv = ((int (__fastcall *)(CPlayer *))v18->vfptr->GetLevel)(v18);
          CPlayerDB::GetPvPPoint(&v18->m_Param);
          pSheet.dwPvpPoint = (signed int)floor(a2);
          pSheet.byClassInGuild = CPlayerDB::GetClassInGuild(&v18->m_Param);
          pSheet.pPlayer = v18;
          p = CGuild::PushMember(v19, &pSheet);
          if ( p )
            CGuild::SendMsg_GuildJoinAcceptInform(v19, p, dwMemberSerial);
          v18->m_Param.m_pGuild = v19;
          v18->m_Param.m_pGuildMemPtr = p;
          CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v25, 0);
          CPlayer::UpdateVisualVer(v18, (CPlayer::CashChangeStateFlag)v5->0);
          CPlayer::SendMsg_GuildJoinOtherInform(v18);
          CGuild::SendMsg_GuildMemberLogin(v19, v18->m_dwObjSerial, v18->m_wRegionMapIndex, v18->m_wRegionIndex);
          W2M(v20->wszName, &szTran, 0x80u);
          v26 = v19->m_szHistoryFileName;
          v6 = CPlayerDB::GetCharNameA(&v18->m_Param);
          pszFileName = v26;
          nMemNum = v19->m_nMemberNum;
          dwOKSerial = v20->dwSerial;
          CMgrGuildHistory::join_member(
            &CGuild::s_MgrHistory,
            v6,
            v18->m_dwObjSerial,
            &szTran,
            dwOKSerial,
            nMemNum,
            v26);
          n = v18->m_ObjID.m_wIndex;
          v7 = CGuildBattleController::Instance();
          CGuildBattleController::JoinGuild(v7, n, v19->m_dwSerial, v18->m_dwObjSerial);
        }
      }
    }
  }
}
