/*
 * Function: ?SendMsg_ExchangeLendItemResult@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400D8440
 */

void __fastcall CPlayer::SendMsg_ExchangeLendItemResult(CPlayer *this, char byErrCode, _STORAGE_LIST::_db_con *pNewItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@6
  __int64 v6; // [sp+0h] [bp-88h]@1
  _exchange_lend_item_result_zocl v7; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-24h]@6
  char v9; // [sp+65h] [bp-23h]@6
  CPlayer *v10; // [sp+90h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.byErrorCode = byErrCode;
  if ( !byErrCode )
  {
    v7.Item.byTblCode = pNewItem->m_byTableCode;
    v7.Item.wItemIdx = pNewItem->m_wItemIndex;
    v7.Item.dwDur = pNewItem->m_dwDur;
    v7.Item.dwUp = pNewItem->m_dwLv;
    v7.Item.dwItemSerial = pNewItem->m_wSerial;
    v7.Item.byCsMethod = pNewItem->m_byCsMethod;
    v7.Item.dwT = pNewItem->m_dwT;
  }
  pbyType = 7;
  v9 = 62;
  v5 = _exchange_lend_item_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v7.byErrorCode, v5);
}
