/*
 * Function: ?_<PERSON>len@?$vector@PEAVCUnmannedTraderDivisionInfo@@V?$allocator@PEAVCUnmannedTraderDivisionInfo@@@std@@@std@@KAXXZ
 * Address: 0x14038A380
 */

void __fastcall __noreturn std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Xlen(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-B8h]@1
  std::length_error v4; // [sp+20h] [bp-98h]@4
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@4
  unsigned __int8 v6; // [sp+98h] [bp-20h]@4
  __int64 v7; // [sp+A0h] [bp-18h]@4

  v1 = &v3;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  memset(&v6, 0, sizeof(v6));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "vector<T> too long",
    v6);
  std::length_error::length_error(&v4, &_Message);
  CxxThrowException_0(&v4, &TI3_AVlength_error_std__);
}
