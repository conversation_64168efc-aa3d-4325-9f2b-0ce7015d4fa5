/*
 * Function: ??0CReservedGuildSchedulePage@GUILD_BATTLE@@QEAA@XZ
 * Address: 0x1403CBC30
 */

void __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::CReservedGuildSchedulePage(GUILD_BATTLE::CReservedGuildSchedulePage *this)
{
  this->m_pkList = 0i64;
  this->m_ucPageInx = -1;
  this->m_dwVer = -1;
  this->m_dw1PGuildSerial[0] = 0;
  this->m_dw1PGuildSerial[1] = 0;
  this->m_dw1PGuildSerial[2] = 0;
  this->m_dw1PGuildSerial[3] = 0;
  this->m_dw1PGuildSerial[4] = 0;
  this->m_dw2PGuildSerial[0] = 0;
  this->m_dw2PGuildSerial[1] = 0;
  this->m_dw2PGuildSerial[2] = 0;
  this->m_dw2PGuildSerial[3] = 0;
  this->m_dw2PGuildSerial[4] = 0;
}
