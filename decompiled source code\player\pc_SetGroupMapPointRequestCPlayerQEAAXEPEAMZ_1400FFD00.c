/*
 * Function: ?pc_SetGroupMapPointRequest@CPlayer@@QEAAXEPEAM@Z
 * Address: 0x1400FFD00
 */

void __fastcall CPlayer::pc_SetGroupMapPointRequest(CPlayer *this, char byGroupType, float *pzTar)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char *v5; // rax@5
  char *v6; // rax@11
  char *v7; // rax@13
  char *v8; // rax@15
  char *v9; // rax@25
  char *v10; // rax@28
  unsigned int v11; // eax@29
  char *v12; // rax@30
  char *v13; // rax@32
  char *v14; // rax@42
  CPvpUserAndGuildRankingSystem *v15; // rax@44
  unsigned int v16; // eax@44
  char *v17; // rax@45
  char *v18; // rax@47
  char *v19; // rax@49
  char *v20; // rax@51
  int v21; // eax@56
  char *v22; // rax@58
  __int64 v23; // [sp+0h] [bp-A8h]@1
  float *pzTara; // [sp+20h] [bp-88h]@5
  char byRemain; // [sp+28h] [bp-80h]@5
  int j; // [sp+30h] [bp-78h]@17
  CPlayer *v27; // [sp+38h] [bp-70h]@4
  CPartyPlayer **v28; // [sp+40h] [bp-68h]@4
  _guild_member_info *v29; // [sp+48h] [bp-60h]@4
  unsigned int v30; // [sp+50h] [bp-58h]@4
  float v31; // [sp+68h] [bp-40h]@4
  int v32; // [sp+70h] [bp-38h]@4
  char v33; // [sp+84h] [bp-24h]@6
  unsigned int v34; // [sp+88h] [bp-20h]@29
  int v35; // [sp+8Ch] [bp-1Ch]@44
  unsigned int v36; // [sp+90h] [bp-18h]@44
  int v37; // [sp+94h] [bp-14h]@56
  CPlayer *v38; // [sp+B0h] [bp+8h]@1
  char v39; // [sp+B8h] [bp+10h]@1
  float *v40; // [sp+C0h] [bp+18h]@1

  v40 = pzTar;
  v39 = byGroupType;
  v38 = this;
  v3 = &v23;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v27 = 0i64;
  v28 = 0i64;
  v29 = 0i64;
  v30 = GetLoopTime();
  v31 = *v40;
  v32 = *((_DWORD *)v40 + 1);
  if ( 65535.0 == CLevel::GetFirstYpos(&v38->m_pCurMap->m_Level, &v31, -1) )
  {
    v5 = (char *)v38->m_pCurMap->m_pMapSet;
    byRemain = 0;
    pzTara = v40;
    CPlayer::SendMsg_SetGroupMapPoint(v38, 5, v39, *v5, v40, 0);
    return;
  }
  v33 = v39;
  if ( v39 )
  {
    if ( v33 == 1 )
    {
      if ( !v38->m_Param.m_pGuild )
      {
        v10 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 1, v39, *v10, v40, 0);
        return;
      }
      v34 = CGuild::GetGuildMasterSerial(v38->m_Param.m_pGuild);
      v11 = CPlayerDB::GetCharSerial(&v38->m_Param);
      if ( v34 != v11 )
      {
        v12 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 1, v39, *v12, v40, 0);
        return;
      }
      if ( v30 - v38->m_dwLastGroupMapPointTime[(unsigned __int8)v39] < 0x57E40 )
      {
        *v40 = (float)((360000 - (v30 - v38->m_dwLastGroupMapPointTime[(unsigned __int8)v39])) / 0x3E8);
        v13 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 2, v39, *v13, v40, 0);
        return;
      }
      for ( j = 0; j < 50; ++j )
      {
        v29 = &v38->m_Param.m_pGuild->m_MemberData[j];
        if ( _guild_member_info::IsFill(v29) )
        {
          v27 = v29->pPlayer;
          if ( v27 )
          {
            if ( v27->m_bOper && v27->m_pCurMap == v38->m_pCurMap && v27->m_wMapLayerIndex == v38->m_wMapLayerIndex )
            {
              v14 = (char *)v38->m_pCurMap->m_pMapSet;
              byRemain = 60;
              pzTara = v40;
              CPlayer::SendMsg_SetGroupMapPoint(v27, 0, v39, *v14, v40, 60);
            }
          }
        }
      }
    }
    else if ( v33 == 2 )
    {
      v35 = CPlayerDB::GetRaceCode(&v38->m_Param);
      v15 = CPvpUserAndGuildRankingSystem::Instance();
      v36 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v15, v35, 0);
      v16 = CPlayerDB::GetCharSerial(&v38->m_Param);
      if ( v36 != v16 )
      {
        v17 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 1, v39, *v17, v40, 0);
        return;
      }
      if ( strcmp_0(v38->m_pCurMap->m_pMapSet->m_strCode, "resources") )
      {
        v18 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 3, v39, *v18, v40, 0);
        return;
      }
      if ( CHolyStoneSystem::GetSceneCode(&g_HolySys) != 1 )
      {
        v19 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 4, v39, *v19, v40, 0);
        return;
      }
      if ( v30 - v38->m_dwLastGroupMapPointTime[(unsigned __int8)v39] < 0x2BF20 )
      {
        *v40 = (float)((180000 - (v30 - v38->m_dwLastGroupMapPointTime[(unsigned __int8)v39])) / 0x3E8);
        v20 = (char *)v38->m_pCurMap->m_pMapSet;
        byRemain = 0;
        pzTara = v40;
        CPlayer::SendMsg_SetGroupMapPoint(v38, 2, v39, *v20, v40, 0);
        return;
      }
      for ( j = 0; j < 2532; ++j )
      {
        v27 = &g_Player + j;
        if ( v27->m_bOper )
        {
          v37 = CPlayerDB::GetRaceCode(&v27->m_Param);
          v21 = CPlayerDB::GetRaceCode(&v38->m_Param);
          if ( v37 == v21 && v27->m_pCurMap == v38->m_pCurMap )
          {
            v22 = (char *)v38->m_pCurMap->m_pMapSet;
            byRemain = 60;
            pzTara = v40;
            CPlayer::SendMsg_SetGroupMapPoint(v27, 0, v39, *v22, v40, 60);
          }
        }
      }
    }
    goto LABEL_59;
  }
  if ( !CPartyPlayer::IsPartyMode(v38->m_pPartyMgr) )
  {
    v6 = (char *)v38->m_pCurMap->m_pMapSet;
    byRemain = 0;
    pzTara = v40;
    CPlayer::SendMsg_SetGroupMapPoint(v38, 1, 0, *v6, v40, 0);
    return;
  }
  if ( !CPartyPlayer::IsPartyBoss(v38->m_pPartyMgr) )
  {
    v7 = (char *)v38->m_pCurMap->m_pMapSet;
    byRemain = 0;
    pzTara = v40;
    CPlayer::SendMsg_SetGroupMapPoint(v38, 1, 0, *v7, v40, 0);
    return;
  }
  if ( v30 - v38->m_dwLastGroupMapPointTime[0] < 0xEA60 )
  {
    *v40 = (float)((60000 - (v30 - v38->m_dwLastGroupMapPointTime[0])) / 0x3E8);
    v8 = (char *)v38->m_pCurMap->m_pMapSet;
    byRemain = 0;
    pzTara = v40;
    CPlayer::SendMsg_SetGroupMapPoint(v38, 2, 0, *v8, v40, 0);
    return;
  }
  v28 = CPartyPlayer::GetPtrPartyMember(v38->m_pPartyMgr);
  if ( v28 )
  {
    for ( j = 0; j < 8; ++j )
    {
      if ( v28[j] )
      {
        v27 = &g_Player + v28[j]->m_id.wIndex;
        if ( v27->m_bOper )
        {
          if ( v27->m_pCurMap == v38->m_pCurMap && v27->m_wMapLayerIndex == v38->m_wMapLayerIndex )
          {
            v9 = (char *)v38->m_pCurMap->m_pMapSet;
            byRemain = 60;
            pzTara = v40;
            CPlayer::SendMsg_SetGroupMapPoint(v27, 0, 0, *v9, v40, 60);
          }
        }
      }
    }
LABEL_59:
    v38->m_dwLastGroupMapPointTime[(unsigned __int8)v39] = v30;
    v38->m_fGroupMapPoint[(unsigned __int8)v39][0] = *v40;
    v38->m_fGroupMapPoint[(unsigned __int8)v39][1] = v40[1];
    v38->m_byGroupMapPointMapCode[(unsigned __int8)v39] = CMapData::GetMapCode(v38->m_pCurMap);
    v38->m_wGroupMapPointLayerIndex[(unsigned __int8)v39] = v38->m_wMapLayerIndex;
  }
}
