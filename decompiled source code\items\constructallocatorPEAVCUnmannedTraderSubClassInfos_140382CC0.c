/*
 * Function: ?construct@?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@QEAAXPEAPEAVCUnmannedTraderSubClassInfo@@AEBQEAV3@@Z
 * Address: 0x140382CC0
 */

void __fastcall std::allocator<CUnmannedTraderSubClassInfo *>::construct(std::allocator<CUnmannedTraderSubClassInfo *> *this, CUnmannedTraderSubClassInfo **_Ptr, CUnmannedTraderSubClassInfo *const *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1

  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  std::_Construct<CUnmannedTraderSubClassInfo *,CUnmannedTraderSubClassInfo *>(_Ptr, _Val);
}
