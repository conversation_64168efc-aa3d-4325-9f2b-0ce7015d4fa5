/*
 * Function: ?ct_CashEventStart@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140296170
 */

bool __fastcall ct_CashEventStart(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  CashItemRemoteStore *v4; // rax@12
  __int64 v5; // [sp+0h] [bp-68h]@1
  int iBegin_TT; // [sp+38h] [bp-30h]@6
  int iB30_TT; // [sp+3Ch] [bp-2Ch]@6
  int iB5_TT; // [sp+40h] [bp-28h]@6
  int v9; // [sp+44h] [bp-24h]@6
  char v10; // [sp+54h] [bp-14h]@6
  CPlayer *v11; // [sp+70h] [bp+8h]@1

  v11 = pOne;
  v1 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11 )
  {
    iBegin_TT = 0;
    memset(&iB30_TT, 0, 0xCui64);
    iBegin_TT = atoi(s_pwszDstCheat[0]);
    iB30_TT = atoi(s_pwszDstCheat[1]);
    iB5_TT = atoi(s_pwszDstCheat[2]);
    v9 = atoi(s_pwszDstCheat[3]);
    v10 = atoi(s_pwszDstCheat[4]);
    if ( iBegin_TT > 0 && iB30_TT > 0 && iB5_TT > 0 && v9 > 0 && (signed int)(unsigned __int8)v10 <= 1 )
    {
      v4 = CashItemRemoteStore::Instance();
      result = CashItemRemoteStore::start_cashevent(v4, iBegin_TT, iB30_TT, iB5_TT, v9, v10);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
