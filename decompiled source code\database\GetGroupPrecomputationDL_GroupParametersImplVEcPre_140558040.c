/*
 * Function: ?GetGroupPrecomputation@?$DL_GroupParametersImpl@V?$EcPrecomputation@VEC2N@CryptoPP@@@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@UEC2NPoint@CryptoPP@@@2@V?$DL_GroupParameters@UEC2NPoint@CryptoPP@@@2@@CryptoPP@@UEBAAEBV?$DL_GroupPrecomputation@UEC2NPoint@CryptoPP@@@2@XZ
 * Address: 0x140558040
 */

signed __int64 __fastcall CryptoPP::DL_GroupParametersImpl<CryptoPP::EcPrecomputation<CryptoPP::EC2N>,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::EC2NPoint>,CryptoPP::DL_GroupParameters<CryptoPP::EC2NPoint>>::GetGroupPrecomputation(__int64 a1)
{
  return a1 + 24;
}
