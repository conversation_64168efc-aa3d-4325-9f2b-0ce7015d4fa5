/*
 * Function: j_??_E?$hash_map@HPEBU_TimeItem_fld@@V?$hash_compare@HU?$less@H@std@@@stdext@@V?$allocator@U?$pair@$$CBHPEBU_TimeItem_fld@@@std@@@std@@@stdext@@QEAAPEAXI@Z
 * Address: 0x14000ADEE
 */

void *__fastcall stdext::hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::`vector deleting destructor'(stdext::hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> > > *this, unsigned int a2)
{
  return stdext::hash_map<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::`vector deleting destructor'(
           this,
           a2);
}
