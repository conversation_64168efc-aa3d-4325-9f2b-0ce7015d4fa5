/*
 * Function: ?Set@CPvpPointLimiter@@QEAA_NNPEAV_PVPPOINT_LIMIT_DB_BASE@@@Z
 * Address: 0x140125120
 */

char __fastcall CPvpPointLimiter::Set(CPvpPointLimiter *this, long double dOriginalPvpPoint, _PVPPOINT_LIMIT_DB_BASE *pkInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@10
  __int64 v6; // [sp-20h] [bp-D8h]@1
  char Dst; // [sp+18h] [bp-A0h]@4
  int v8; // [sp+24h] [bp-94h]@9
  int v9; // [sp+28h] [bp-90h]@8
  int v10; // [sp+2Ch] [bp-8Ch]@7
  tm *v11; // [sp+48h] [bp-70h]@4
  __int64 tUpdateTime; // [sp+58h] [bp-60h]@6
  tm *v13; // [sp+68h] [bp-50h]@6
  char v14; // [sp+70h] [bp-48h]@5
  CPvpPointLimiter *v15; // [sp+C0h] [bp+8h]@1
  _PVPPOINT_LIMIT_DB_BASE *_Time; // [sp+D0h] [bp+18h]@1

  _Time = pkInfo;
  v15 = this;
  v3 = &v6;
  for ( i = 50i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset_0(&Dst, 0, 0x24ui64);
  v11 = localtime_1(&_Time->tUpdatedate);
  if ( v11 )
  {
    qmemcpy(&v14, v11, 0x24ui64);
    qmemcpy(&Dst, &v14, 0x24ui64);
  }
  tUpdateTime = 0i64;
  time_3(&tUpdateTime);
  v13 = localtime_1(&tUpdateTime);
  v15->m_pkInfo = _Time;
  if ( _Time->byLimitRate != 3 || v10 != v13->tm_year || v9 != v13->tm_mon || v8 != v13->tm_mday )
  {
    CPvpPointLimiter::Update(v15, tUpdateTime, dOriginalPvpPoint, 0.0, 0);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
