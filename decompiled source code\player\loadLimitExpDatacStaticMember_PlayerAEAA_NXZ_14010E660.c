/*
 * Function: ?loadLimitExpData@cStaticMember_Player@@AEAA_NXZ
 * Address: 0x14010E660
 */

char __fastcall cStaticMember_Player::loadLimitExpData(cStaticMember_Player *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int n; // [sp+20h] [bp-28h]@10
  _base_fld *v6; // [sp+28h] [bp-20h]@12
  long double *v7; // [sp+30h] [bp-18h]@7
  unsigned __int64 v8; // [sp+38h] [bp-10h]@7
  cStaticMember_Player *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9->_nMaxLv = CRecordData::GetRecordNum(&stru_1799C64D0);
  if ( v9->_nMaxLv < 50 )
  {
    CLogFile::Write(&stru_1799C8E78, "cStaticMember_Player::loadLimitExpData() : wrong _nMaxLv[%d]", v9->_nMaxLv);
    ServerProgramExit("Exp Data Load Error", 0);
  }
  v8 = v9->_nMaxLv;
  v7 = (long double *)operator new[](saturated_mul(8ui64, v8));
  v9->_pLimExp = v7;
  if ( !v9->_pLimExp )
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "cStaticMember_Player::loadLimitExpData() : failed allocate _pLimExp[%d]",
      v9->_nMaxLv);
    ServerProgramExit("Exp Data Load Error", 0);
  }
  for ( n = 0; n < v9->_nMaxLv; ++n )
  {
    v6 = CRecordData::GetRecord(&stru_1799C64D0, n);
    if ( !v6 )
    {
      CLogFile::Write(&stru_1799C8E78, "CPlayer::SetStaticMember() : %d Exp Data..NULL", (unsigned int)n);
      ServerProgramExit("Exp Data Load Error", 0);
    }
    v9->_pLimExp[n] = atof((const char *)&v6[1]);
  }
  return 1;
}
