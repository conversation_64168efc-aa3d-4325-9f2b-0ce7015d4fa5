/*
 * Function: ??1HashVerificationFilter@CryptoPP@@UEAA@XZ
 * Address: 0x1405FF6E0
 */

void __fastcall CryptoPP::HashVerificationFilter::~HashVerificationFilter(CryptoPP::HashVerificationFilter *this)
{
  CryptoPP::HashVerificationFilter *v1; // [sp+40h] [bp+8h]@1

  v1 = this;
  CryptoPP::Sec<PERSON>lock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~Se<PERSON><PERSON><PERSON><unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&this->m_expectedHash);
  CryptoPP::FilterWithBufferedInput::~FilterWithBufferedInput((CryptoPP::FilterWithBufferedInput *)&v1->vfptr);
}
