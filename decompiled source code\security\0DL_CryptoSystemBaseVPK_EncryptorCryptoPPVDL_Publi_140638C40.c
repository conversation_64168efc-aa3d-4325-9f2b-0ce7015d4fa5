/*
 * Function: ??0?$DL_CryptoSystemBase@VPK_Encryptor@CryptoPP@@V?$DL_PublicKey@VInteger@CryptoPP@@@2@@CryptoPP@@QEAA@XZ
 * Address: 0x140638C40
 */

CryptoPP::PK_Encryptor *__fastcall CryptoPP::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>::DL_CryptoSystemBase<CryptoPP::PK_Encryptor,CryptoPP::DL_PublicKey<CryptoPP::Integer>>(CryptoPP::PK_Encryptor *a1)
{
  CryptoPP::PK_Encryptor *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::PK_Encryptor::PK_Encryptor(a1);
  CryptoPP::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>::DL_Base<CryptoPP::DL_PublicKey<CryptoPP::Integer>>((__int64)&v2[1]);
  return v2;
}
