/*
 * Function: ?TruncatedVerify@HashTransformation@CryptoPP@@UEAA_NPEBE_K@Z
 * Address: 0x1405F4330
 */

char __fastcall CryptoPP::HashTransformation::TruncatedVerify(CryptoPP::HashTransformation *this, const unsigned __int8 *a2, unsigned __int64 a3)
{
  char *v3; // rax@1
  void *v4; // rax@1
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0> > v6; // [sp+20h] [bp-48h]@1
  char v7; // [sp+38h] [bp-30h]@1
  __int64 v8; // [sp+40h] [bp-28h]@1
  CryptoPP::ClonableVtbl *v9; // [sp+48h] [bp-20h]@1
  int v10; // [sp+50h] [bp-18h]@1
  CryptoPP::HashTransformation *v11; // [sp+70h] [bp+8h]@1
  const unsigned __int8 *Buf2; // [sp+78h] [bp+10h]@1
  unsigned __int64 size; // [sp+80h] [bp+18h]@1

  size = a3;
  Buf2 = a2;
  v11 = this;
  v8 = -2i64;
  CryptoPP::HashTransformation::ThrowIfInvalidTruncatedSize(this, a3);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(
    &v6,
    size);
  v3 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator unsigned char *(&v6);
  v9 = v11->vfptr;
  ((void (__fastcall *)(CryptoPP::HashTransformation *, char *, unsigned __int64))v9[7].__vecDelDtor)(v11, v3, size);
  v4 = CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::operator void *(&v6);
  v10 = memcmp_0(v4, Buf2, size) == 0;
  v7 = v10;
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>::~SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0>>(&v6);
  return v7;
}
