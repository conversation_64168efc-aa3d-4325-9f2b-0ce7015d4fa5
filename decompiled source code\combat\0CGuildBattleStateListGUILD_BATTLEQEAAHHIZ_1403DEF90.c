/*
 * Function: ??0CGuildBattleStateList@GUILD_BATTLE@@QEAA@HHI@Z
 * Address: 0x1403DEF90
 */

void __fastcall GUILD_BATTLE::CGuildBattleStateList::CGuildBattleStateList(GUILD_BATTLE::CGuildBattleStateList *this, int iStateMax, int iLoopType, unsigned int uiLoopCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleStateList *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v4 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7->vfptr = (GUILD_BATTLE::CGuildBattleStateListVtbl *)&GUILD_BATTLE::CGuildBattleStateList::`vftable';
  v7->STATE_MAX = iStateMax;
  v7->m_uiLoopCnt = uiLoopCnt;
  if ( iLoopType < 3 && iLoopType >= 0 )
    v7->m_eLoopType = iLoopType;
  else
    v7->m_eLoopType = 0;
  GUILD_BATTLE::CGuildBattleStateList::Clear(v7);
}
