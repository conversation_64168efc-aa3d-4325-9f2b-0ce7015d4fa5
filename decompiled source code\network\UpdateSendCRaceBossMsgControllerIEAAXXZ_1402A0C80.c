/*
 * Function: ?UpdateSend@CRaceBossMsgController@@IEAAXXZ
 * Address: 0x1402A0C80
 */

void __fastcall CRaceBossMsgController::UpdateSend(CRaceBossMsgController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@6
  __int64 v4; // [sp+0h] [bp-58h]@1
  char ucRace; // [sp+24h] [bp-34h]@4
  RACE_BOSS_MSG::CMsg *pkMsg; // [sp+38h] [bp-20h]@4
  char v7; // [sp+40h] [bp-18h]@6
  CRaceBossMsgController *v8; // [sp+60h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  ucRace = 0;
  pkMsg = RACE_BOSS_MSG::CMsgListManager::GetSendMsg(&v8->m_kManager, &ucRace);
  if ( pkMsg )
  {
    if ( !RACE_BOSS_MSG::CMsg::IsSendFromWeb(pkMsg) )
    {
      v7 = RACE_BOSS_MSG::CMsgListManager::GetRemainCnt(&v8->m_kManager, ucRace);
      v3 = RACE_BOSS_MSG::CMsg::GetSerial(pkMsg);
      CRaceBossMsgController::SendInfomSender(v8, v3, v7);
    }
    CRaceBossMsgController::SendRequestWeb(v8, ucRace, pkMsg);
    RACE_BOSS_MSG::CMsgListManager::Release(&v8->m_kManager, ucRace, pkMsg);
    RACE_BOSS_MSG::CMsgListManager::Save(&v8->m_kManager, ucRace);
    CRaceBossMsgController::SaveCurTime(v8);
  }
}
