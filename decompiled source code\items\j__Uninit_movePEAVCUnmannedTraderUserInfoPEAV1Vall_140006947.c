/*
 * Function: j_??$_Uninit_move@PEAVCUnmannedTraderUserInfo@@PEAV1@V?$allocator@VCUnmannedTraderUserInfo@@@std@@U_Undefined_move_tag@3@@std@@YAPEAVCUnmannedTraderUserInfo@@PEAV1@00AEAV?$allocator@VCUnmannedTraderUserInfo@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140006947
 */

CUnmannedTraderUserInfo *__fastcall std::_Uninit_move<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>,std::_Undefined_move_tag>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Dest, std::allocator<CUnmannedTraderUserInfo> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo *,std::allocator<CUnmannedTraderUserInfo>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
