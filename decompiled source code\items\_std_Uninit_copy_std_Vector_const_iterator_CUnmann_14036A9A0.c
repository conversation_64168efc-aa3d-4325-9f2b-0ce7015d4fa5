/*
 * Function: _std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::dtor$0
 * Address: 0x14036A9A0
 */

void __fastcall std::_Uninit_copy_std::_Vector_const_iterator_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo____CUnmannedTraderRegistItemInfo_____ptr64_std::allocator_CUnmannedTraderRegistItemInfo____::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(*(std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 104));
}
