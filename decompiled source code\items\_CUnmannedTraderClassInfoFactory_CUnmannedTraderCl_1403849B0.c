/*
 * Function: _CUnmannedTraderClassInfoFactory::_CUnmannedTraderClassInfoFactory_::_1_::dtor$0
 * Address: 0x1403849B0
 */

void __fastcall CUnmannedTraderClassInfoFactory::_CUnmannedTraderClassInfoFactory_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(*(std::vector<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 64));
}
