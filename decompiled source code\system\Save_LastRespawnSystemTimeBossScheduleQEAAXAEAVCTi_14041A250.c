/*
 * Function: ?Save_LastRespawnSystemTime@BossSchedule@@QEAAXAEAVCTime@ATL@@@Z
 * Address: 0x14041A250
 */

void __fastcall BossSchedule::Save_LastRespawnSystemTime(BossSchedule *this, ATL::CTime *systime)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  ScheduleMSG *pTask; // [sp+20h] [bp-18h]@6
  BossSchedule *_Source; // [sp+40h] [bp+8h]@1
  ATL::CTime *v7; // [sp+48h] [bp+10h]@1

  v7 = systime;
  _Source = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( _Source->m_pParent )
  {
    if ( _Source->m_pParent->m_pSystem )
    {
      pTask = US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>::PopEmpty((US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0> > *)&_Source->m_pParent->m_pSystem->m_MSG_POOL.vfptr);
      if ( pTask )
      {
        ScheduleMSG::Init(pTask);
        _Source->m_LastRespawnSystemTime = (ATL::CTime)v7->m_time;
        pTask->m_byKey = 1;
        strcpy_s<64>((char (*)[64])pTask->m_strSection, _Source->m_strSection);
        pTask->m_wIniFileIndex = _Source->m_pParent->m_nIndex;
        if ( BossSchedule::Make_LastTimeRespawnSystemTimeString(_Source, pTask->m_strValue, 64) )
          US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0>>::PushProc(
            (US::AbstractTaskPool<ScheduleMSG,US::CCircularFIFO<unsigned long,US::CriticalSection,0> > *)&_Source->m_pParent->m_pSystem->m_MSG_POOL.vfptr,
            pTask);
      }
    }
  }
}
