/*
 * Function: ?CombineItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CAEC0
 */

char __fastcall CNetworkEX::CombineItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  int v7; // eax@10
  char *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-58h]@1
  unsigned __int16 wOverlapSerial; // [sp+20h] [bp-38h]@12
  char *v11; // [sp+30h] [bp-28h]@4
  CPlayer *v12; // [sp+38h] [bp-20h]@4
  int v13; // [sp+40h] [bp-18h]@10
  CNetworkEX *v14; // [sp+60h] [bp+8h]@1

  v14 = this;
  v3 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v11 = pBuf;
  v12 = &g_Player + n;
  if ( !v12->m_bOper || v12->m_pmTrd.bDTradeMode || v12->m_bCorpse )
  {
    result = 1;
  }
  else if ( (signed int)(unsigned __int8)v11[4] <= 100 )
  {
    v13 = *(_WORD *)v11;
    v7 = CRecordData::GetRecordNum(&stru_1799C6878);
    if ( v13 < v7 )
    {
      wOverlapSerial = *((_WORD *)v11 + 1);
      CPlayer::pc_CombineItem(v12, *(_WORD *)v11, v11[4], (_STORAGE_POS_INDIV *)(v11 + 5), wOverlapSerial);
      result = 1;
    }
    else
    {
      v8 = CPlayerDB::GetCharNameA(&v12->m_Param);
      CLogFile::Write(
        &v14->m_LogFile,
        "odd.. %s: CombineItemRequest()..  if(pRecv->wManualIndex >= g_Main.m_tblItemCombineData.GetRecordNum())",
        v8);
      result = 0;
    }
  }
  else
  {
    v6 = CPlayerDB::GetCharNameA(&v12->m_Param);
    CLogFile::Write(
      &v14->m_LogFile,
      "odd.. %s: CombineItemRequest()..  if(pRecv->byMaterialNum > _combine_item_request_clzo::material_num)",
      v6);
    result = 0;
  }
  return result;
}
