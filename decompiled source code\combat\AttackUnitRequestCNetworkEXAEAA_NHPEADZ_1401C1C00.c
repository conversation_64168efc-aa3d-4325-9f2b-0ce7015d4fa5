/*
 * Function: ?AttackUnitRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401C1C00
 */

char __fastcall CNetworkEX::AttackUnitRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@7
  char *v6; // rax@9
  char *v7; // rax@11
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *v9; // [sp+20h] [bp-28h]@4
  CPlayer *v10; // [sp+28h] [bp-20h]@4
  CCharacter *pDst; // [sp+30h] [bp-18h]@8
  CNetworkEX *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = pBuf;
  v10 = &g_Player + n;
  if ( !v10->m_bOper || v10->m_pmTrd.bDTradeMode || v10->m_bCorpse )
  {
    result = 1;
  }
  else
  {
    pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, (unsigned __int8)*v9, *(_WORD *)(v9 + 1));
    if ( pDst )
    {
      if ( (signed int)(unsigned __int8)v9[3] < 2 )
      {
        CPlayer::pc_PlayAttack_Unit(v10, pDst, v9[3]);
        result = 1;
      }
      else
      {
        v7 = CPlayerDB::GetCharNameA(&v10->m_Param);
        CLogFile::Write(
          &v12->m_LogFile,
          "odd.. %s: AttackUnitRequest()..  if(pRecv->byWeaponPart >= UNIT_BULLET_NUM)",
          v7);
        result = 0;
      }
    }
    else
    {
      v6 = CPlayerDB::GetCharNameA(&v10->m_Param);
      CLogFile::Write(&v12->m_LogFile, "odd.. %s: AttackUnitRequest()..  if(!pDst)", v6);
      result = 0;
    }
  }
  return result;
}
