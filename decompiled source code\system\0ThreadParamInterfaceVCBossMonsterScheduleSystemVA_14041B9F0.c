/*
 * Function: ??0?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@QEAA@XZ
 * Address: 0x14041B9F0
 */

void __fastcall US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>(US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int v4; // [sp+8h] [bp-30h]@4
  int v5; // [sp+Ch] [bp-2Ch]@4
  int v6; // [sp+10h] [bp-28h]@4
  US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  for ( i = 10i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7->m_pOwner = 0i64;
  v7->m_pMyThreadPool = 0i64;
  v3 = 0i64;
  v4 = 0;
  v5 = 0;
  v6 = -1;
  qmemcpy(&v7->m_pOwnerMemberFunc, &v3, 0x18ui64);
}
