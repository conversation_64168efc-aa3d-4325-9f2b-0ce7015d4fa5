/*
 * Function: ?ct_animus_attack_grade@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140292430
 */

bool __fastcall ct_animus_attack_grade(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-38h]@1
  int nPoint; // [sp+20h] [bp-18h]@7
  CPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = pOne;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !v6 )
    return 0;
  if ( s_nWordCount < 1 )
  {
    result = 0;
  }
  else
  {
    nPoint = 0;
    if ( !strcmp_0("max", s_pwszDstCheat[0]) )
    {
      nPoint = 1;
    }
    else if ( !strcmp_0("min", s_pwszDstCheat[0]) )
    {
      nPoint = -1;
    }
    else
    {
      if ( strcmp_0("normal", s_pwszDstCheat[0]) )
        return 0;
      nPoint = 0;
    }
    result = CPlayer::mgr_set_animus_attack_point(v6, nPoint);
  }
  return result;
}
