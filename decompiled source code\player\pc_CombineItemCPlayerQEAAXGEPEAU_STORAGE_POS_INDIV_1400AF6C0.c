/*
 * Function: ?pc_CombineItem@CPlayer@@QEAAXGEPEAU_STORAGE_POS_INDIV@@G@Z
 * Address: 0x1400AF6C0
 */

void __usercall CPlayer::pc_CombineItem(CPlayer *this@<rcx>, unsigned __int16 wManualIndex@<dx>, char by<PERSON><PERSON><PERSON><PERSON><PERSON>@<r8b>, _STORAGE_POS_INDIV *pipMaterials@<r9>, signed __int64 a5@<rax>, unsigned __int16 wOverlapSerial)
{
  void *v6; // rsp@1
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  int v9; // eax@6
  unsigned int v10; // eax@8
  CGoldenBoxItemMgr *v11; // rax@51
  unsigned int v12; // eax@53
  unsigned int v13; // eax@55
  unsigned int v14; // eax@56
  unsigned int v15; // eax@56
  CGoldenBoxItemMgr *v16; // rax@76
  unsigned int v17; // eax@77
  unsigned int v18; // eax@77
  CGoldenBoxItemMgr *v19; // rax@81
  unsigned int v20; // eax@84
  int v21; // eax@89
  __int64 v22; // rcx@89
  CMoneySupplyMgr *v23; // rax@95
  unsigned int v24; // eax@96
  __int64 v25; // [sp-20h] [bp-1928h]@1
  bool bAdd[8]; // [sp+0h] [bp-1908h]@86
  bool bCircle[8]; // [sp+8h] [bp-1900h]@86
  char v28; // [sp+30h] [bp-18D8h]@4
  void *Src; // [sp+50h] [bp-18B8h]@25
  int j; // [sp+374h] [bp-1594h]@23
  _base_fld *v31; // [sp+378h] [bp-1590h]@4
  char Dst[8]; // [sp+388h] [bp-1580h]@23
  int v33[17]; // [sp+390h] [bp-1578h]@39
  char v34; // [sp+3D4h] [bp-1534h]@4
  _base_fld *v35; // [sp+3D8h] [bp-1530h]@4
  void *v36; // [sp+3E0h] [bp-1528h]@4
  _STORAGE_LIST::_db_con *v37; // [sp+3E8h] [bp-1520h]@17
  int k; // [sp+3F0h] [bp-1518h]@31
  char v39; // [sp+3F4h] [bp-1514h]@36
  _base_fld *v40; // [sp+3F8h] [bp-1510h]@36
  int l; // [sp+400h] [bp-1508h]@36
  unsigned int dwFee; // [sp+404h] [bp-1504h]@57
  _STORAGE_LIST::_db_con pNewItem; // [sp+418h] [bp-14F0h]@57
  _TimeItem_fld *v44; // [sp+458h] [bp-14B0h]@57
  char __t[5040]; // [sp+470h] [bp-1498h]@58
  char pbyMtrNum[116]; // [sp+1820h] [bp-E8h]@60
  char v47; // [sp+1894h] [bp-74h]@64
  char v48; // [sp+1895h] [bp-73h]@64
  char v49; // [sp+1896h] [bp-72h]@67
  __time32_t Time; // [sp+18A4h] [bp-64h]@69
  _base_fld *v51; // [sp+18B8h] [bp-50h]@82
  int v52; // [sp+18C0h] [bp-48h]@91
  int v53; // [sp+18D0h] [bp-38h]@65
  char *szCharName; // [sp+18D8h] [bp-30h]@84
  int nLv; // [sp+18E0h] [bp-28h]@95
  int v56; // [sp+18E4h] [bp-24h]@95
  char *v57; // [sp+18E8h] [bp-20h]@96
  unsigned __int64 v58; // [sp+18F0h] [bp-18h]@4
  CPlayer *v59; // [sp+1910h] [bp+8h]@1
  unsigned __int16 v60; // [sp+1918h] [bp+10h]@1
  char v61; // [sp+1920h] [bp+18h]@1
  _STORAGE_POS_INDIV *v62; // [sp+1928h] [bp+20h]@1

  v62 = pipMaterials;
  v61 = byMaterialNum;
  v60 = wManualIndex;
  v59 = this;
  v6 = alloca(a5);
  v7 = &v25;
  for ( i = 1608i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v58 = (unsigned __int64)&v25 ^ _security_cookie;
  v28 = 0;
  v31 = CRecordData::GetRecord(&stru_1799C6878, wManualIndex);
  v34 = -1;
  v35 = 0i64;
  v36 = 0i64;
  if ( v31 )
  {
    v9 = CPlayerDB::GetRaceSexCode(&v59->m_Param);
    if ( v31[1].m_strCode[v9 + 4] == 49 )
    {
      v10 = CPlayerDB::GetDalant(&v59->m_Param);
      if ( *(_DWORD *)&v31[1].m_strCode[0] <= v10 )
      {
        if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v59->m_Param.m_dbInven.m_nListNum) == 255 )
        {
          v28 = 3;
        }
        else
        {
          v34 = GetItemTableCode(v31->m_strCode);
          if ( (unsigned __int8)v34 == 255 )
          {
            v28 = 12;
          }
          else
          {
            v35 = CRecordData::GetRecordByHash(
                    (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v34,
                    v31->m_strCode,
                    2,
                    5);
            if ( v35 )
            {
              if ( wOverlapSerial != 0xFFFF )
              {
                v37 = _STORAGE_LIST::GetPtrFromSerial(
                        (_STORAGE_LIST *)&v59->m_Param.m_dbInven.m_nListNum,
                        wOverlapSerial);
                if ( v37 )
                {
                  if ( v37->m_byTableCode == (unsigned __int8)v34
                    && v37->m_wItemIndex == v35->m_dwIndex
                    && v37->m_dwDur < 0x63
                    && !v37->m_bLock )
                  {
                    v36 = v37;
                  }
                }
              }
              memcpy_0(Dst, &v31[2].m_strCode[8], 0x3Cui64);
              for ( j = 0; j < (unsigned __int8)v61; ++j )
              {
                *(&Src + j) = _STORAGE_LIST::GetPtrFromSerial(
                                (_STORAGE_LIST *)&v59->m_Param.m_dbInven.m_nListNum,
                                v62[j].wItemSerial);
                if ( !*(&Src + j) )
                {
                  CPlayer::SendMsg_AdjustAmountInform(v59, 0, v62[j].wItemSerial, 0);
                  v28 = 4;
                  goto $RESULT_38;
                }
                if ( *((_BYTE *)*(&Src + j) + 19) )
                {
                  v28 = 10;
                  goto $RESULT_38;
                }
                if ( (unsigned __int64)v62[j].byNum > *(_QWORD *)((char *)*(&Src + j) + 5) )
                {
                  CPlayer::SendMsg_AdjustAmountInform(v59, 0, v62[j].wItemSerial, *(_DWORD *)((char *)*(&Src + j) + 5));
                  v28 = 5;
                  goto $RESULT_38;
                }
                for ( k = 0; k < j; ++k )
                {
                  if ( v62[k].wItemSerial == v62[j].wItemSerial )
                  {
                    v28 = 5;
                    goto $RESULT_38;
                  }
                }
                v39 = 0;
                v40 = CRecordData::GetRecord(
                        (CRecordData *)&unk_1799C6AA0 + *((_BYTE *)*(&Src + j) + 1),
                        *(_WORD *)((char *)*(&Src + j) + 3));
                for ( l = 0; l < 5; ++l )
                {
                  if ( !strncmp(v40->m_strCode, &Dst[12 * l], 7ui64) )
                  {
                    v33[3 * l] -= v62[j].byNum;
                    v39 = 1;
                    break;
                  }
                }
                if ( !v39 )
                {
                  v28 = 8;
                  goto $RESULT_38;
                }
              }
              for ( j = 0; j < 5; ++j )
              {
                if ( v33[3 * j] > 0 )
                {
                  v28 = 6;
                  goto $RESULT_38;
                }
              }
              if ( *(_DWORD *)&v31[2].m_strCode[4] != -1 && *(_DWORD *)&v31[2].m_strCode[0] == 6 )
              {
                v11 = CGoldenBoxItemMgr::Instance();
                if ( CGoldenBoxItemMgr::Get_Event_Status(v11) == 2 )
                {
                  v12 = CUserDB::GetActPoint(v59->m_pUserDB, 2);
                  if ( v12 >= *(_DWORD *)&v31[2].m_strCode[4] )
                  {
                    v13 = CUserDB::GetActPoint(v59->m_pUserDB, 2);
                    if ( v13 >= *(_DWORD *)&v31[2].m_strCode[4] )
                    {
                      v14 = CUserDB::GetActPoint(v59->m_pUserDB, 2);
                      CUserDB::SetActPoint(v59->m_pUserDB, 2, v14 - *(_DWORD *)&v31[2].m_strCode[4]);
                      v15 = CUserDB::GetActPoint(v59->m_pUserDB, 2);
                      CPlayer::SendMsg_Alter_Action_Point(v59, 2, v15);
                    }
                  }
                  else
                  {
                    v28 = 15;
                  }
                }
                else
                {
                  v28 = 12;
                }
              }
            }
            else
            {
              v28 = 12;
            }
          }
        }
      }
      else
      {
        v28 = 14;
      }
    }
    else
    {
      v28 = 11;
    }
  }
  else
  {
    v28 = 12;
  }
$RESULT_38:
  dwFee = 0;
  _STORAGE_LIST::_db_con::_db_con(&pNewItem);
  v44 = 0i64;
  if ( v28 )
    goto LABEL_97;
  `vector constructor iterator'(__t, 0x32ui64, 100, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
  for ( j = 0; j < (unsigned __int8)v61; ++j )
  {
    memcpy_0(&__t[50 * j], *(&Src + j), 0x32ui64);
    pbyMtrNum[j] = v62[j].byNum;
  }
  v44 = TimeItem::FindTimeRec((unsigned __int8)v34, v35->m_dwIndex);
  if ( v36 && (!v44 || !v44->m_nCheckType) )
  {
    memcpy_0(&pNewItem, v36, 0x32ui64);
    ++pNewItem.m_dwDur;
    bCircle[0] = 1;
    bAdd[0] = 0;
    CPlayer::Emb_AlterDurPoint(v59, 0, *((_BYTE *)v36 + 49), 1, 0, 1);
LABEL_87:
    for ( j = 0; j < (unsigned __int8)v61; ++j )
    {
      v21 = -v62[j].byNum;
      v22 = (__int64)*(&Src + j);
      bCircle[0] = 0;
      bAdd[0] = 0;
      CPlayer::Emb_AlterDurPoint(v59, 0, *(_BYTE *)(v22 + 49), v21, 0, 0);
    }
    dwFee = *(_DWORD *)&v31[1].m_strCode[0];
    if ( dwFee )
    {
      CPlayer::SubDalant(v59, dwFee);
      v52 = CPlayerDB::GetLevel(&v59->m_Param);
      if ( v52 == 30 || v52 == 40 || v52 == 50 || v52 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&v59->m_Param);
        v56 = CPlayerDB::GetRaceCode(&v59->m_Param);
        v23 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v23, v56, nLv, dwFee);
      }
    }
    CPlayer::SendMsg_ExchangeMoneyResult(v59, 0);
    v57 = v59->m_szItemHistoryFileName;
    v24 = CPlayerDB::GetDalant(&v59->m_Param);
    CMgrAvatorItemHistory::combine_item(
      &CPlayer::s_MgrItemHistory,
      v59->m_ObjID.m_wIndex,
      (_STORAGE_LIST::_db_con *)__t,
      pbyMtrNum,
      v61,
      &pNewItem,
      dwFee,
      v24,
      v57);
LABEL_97:
    if ( v44 && v44->m_nCheckType )
      CPlayer::SendMsg_CombineLendItemResult(v59, v28, dwFee, &pNewItem);
    else
      CPlayer::SendMsg_CombineItemResult(v59, v28, dwFee, &pNewItem);
    return;
  }
  pNewItem.m_byTableCode = v34;
  pNewItem.m_wItemIndex = v35->m_dwIndex;
  pNewItem.m_dwDur = GetItemDurPoint((unsigned __int8)v34, v35->m_dwIndex);
  v47 = GetItemKindCode((unsigned __int8)v34);
  v48 = GetDefItemUpgSocketNum((unsigned __int8)v34, v35->m_dwIndex);
  if ( (signed int)(unsigned __int8)v48 <= 0 )
    v53 = 0;
  else
    v53 = rand() % (unsigned __int8)v48 + 1;
  v49 = v53;
  pNewItem.m_dwLv = GetBitAfterSetLimSocket(v53);
  pNewItem.m_wSerial = CPlayerDB::GetNewItemSerial(&v59->m_Param);
  if ( v44 && v44->m_nCheckType )
  {
    pNewItem.m_byCsMethod = v44->m_nCheckType;
    _time32(&Time);
    pNewItem.m_dwT = v44->m_nUseTime + Time;
    pNewItem.m_dwLendRegdTime = Time;
  }
  if ( CPlayer::Emb_AddStorage(v59, 0, (_STORAGE_LIST::_storage_con *)&pNewItem.m_bLoad, 0, 1) )
  {
    CPlayer::SendMsg_FanfareItem(v59, 1, &pNewItem, 0i64);
    if ( *(_DWORD *)&v31[2].m_strCode[4] != -1 && *(_DWORD *)&v31[2].m_strCode[0] == 6 )
    {
      v19 = CGoldenBoxItemMgr::Instance();
      if ( CGoldenBoxItemMgr::Get_Event_Status(v19) == 2 )
      {
        v51 = CRecordData::GetRecord(
                (CRecordData *)&unk_1799C6AA0 + (unsigned __int8)pNewItem.m_byTableCode,
                pNewItem.m_wItemIndex);
        if ( !v51 )
          return;
        if ( !strcmp_0(v51->m_strCode, "bxgol04") )
        {
          szCharName = CPlayerDB::GetCharNameA(&v59->m_Param);
          v20 = CPlayerDB::GetCharSerial(&v59->m_Param);
          CPlayer::SendMsg_Notify_Get_Golden_Box(v59, 5, v20, szCharName, &pNewItem, 0);
        }
      }
    }
    goto LABEL_87;
  }
  if ( v44 && v44->m_nCheckType )
    CPlayer::SendMsg_CombineLendItemResult(v59, -1, dwFee, &pNewItem);
  else
    CPlayer::SendMsg_CombineItemResult(v59, -1, dwFee, &pNewItem);
  CMgrAvatorItemHistory::add_storage_fail(
    &CPlayer::s_MgrItemHistory,
    v59->m_ObjID.m_wIndex,
    &pNewItem,
    "CPlayer::pc_CombineItem - Emb_AddStorage() Fail",
    v59->m_szItemHistoryFileName);
  if ( *(_DWORD *)&v31[2].m_strCode[0] == 6 )
  {
    v16 = CGoldenBoxItemMgr::Instance();
    if ( CGoldenBoxItemMgr::Get_Event_Status(v16) == 2 )
    {
      v17 = CUserDB::GetActPoint(v59->m_pUserDB, 2);
      CUserDB::SetActPoint(v59->m_pUserDB, 2, *(_DWORD *)&v31[2].m_strCode[4] + v17);
      v18 = CUserDB::GetActPoint(v59->m_pUserDB, 2);
      CPlayer::SendMsg_Alter_Action_Point(v59, 2, *(_DWORD *)&v31[2].m_strCode[4] + v18);
    }
  }
}
