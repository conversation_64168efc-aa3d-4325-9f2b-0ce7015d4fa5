/*
 * Function: j_?_pre_check_unit_attack@CPlayer@@QEAAHPEAVCCharacter@@EPEAPEAU_UnitPart_fld@@PEAPEAU_UnitBullet_fld@@PEAPEAU_unit_bullet_param@@@Z
 * Address: 0x14000D8FA
 */

int __fastcall CPlayer::_pre_check_unit_attack(CPlayer *this, CCharacter *pDst, char byWeaponPart, _UnitPart_fld **ppWeaponFld, _UnitBullet_fld **ppBulletFld, _unit_bullet_param **ppBulletParam)
{
  return CPlayer::_pre_check_unit_attack(this, pDst, byWeaponPart, ppWeaponFld, ppBulletFld, ppBulletParam);
}
