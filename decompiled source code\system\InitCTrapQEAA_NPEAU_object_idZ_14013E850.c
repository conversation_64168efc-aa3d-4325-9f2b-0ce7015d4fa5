/*
 * Function: ?Init@CTrap@@QEAA_NPEAU_object_id@@@Z
 * Address: 0x14013E850
 */

char __fastcall CTrap::Init(CTrap *this, _object_id *pID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CTrap *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CCharacter::Init((CCharacter *)&v6->vfptr, pID);
  v6->m_dwLastDestroyTime = 0;
  v6->m_dwMasterSerial = -1;
  v6->m_nHP = 0;
  v6->m_pMaster = 0i64;
  v6->m_byRaceCode = -1;
  memset_0(v6->m_wszMasterName, 0, 0x11ui64);
  memset_0(v6->m_aszMasterName, 0, 0x11ui64);
  *(_QWORD *)&v6->m_dMasterPvPPoint = 0i64;
  v6->m_dwStartMakeTime = 0;
  v6->m_bComplete = 0;
  v6->m_bBreakTransparBuffer = 0;
  v6->m_nTrapMaxAttackPnt = 0;
  return 1;
}
