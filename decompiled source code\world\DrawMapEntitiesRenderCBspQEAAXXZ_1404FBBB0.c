/*
 * Function: ?DrawMapEntitiesRender@CBsp@@QEAAXXZ
 * Address: 0x1404FBBB0
 */

void __fastcall CBsp::DrawMapEntitiesRender(CBsp *this)
{
  char *v1; // rbx@1
  CBsp *v2; // rsi@1
  struct IDirect3DDevice8 *v3; // rax@2
  struct IDirect3DDevice8 *v4; // rax@3
  int v5; // er12@4
  int v6; // er13@5
  char *v7; // rbp@5
  signed int v8; // ebx@7
  char v9; // di@7
  unsigned __int16 v10; // dx@9
  __int64 v11; // r8@9

  v1 = this->mEntityCache;
  v2 = this;
  if ( (unsigned int)GetReflectionState() )
  {
    v3 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v3->vfptr[16].Release)(
      v3,
      22i64,
      2i64);
  }
  else
  {
    v4 = GetD3dDevice();
    ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64, signed __int64))v4->vfptr[16].Release)(
      v4,
      22i64,
      3i64);
  }
  v5 = v2->mEntityCacheSize - 1;
  if ( v5 >= 0 )
  {
    v6 = 8 * v5;
    v7 = &v1[v5];
    do
    {
      if ( *v7 )
      {
        v8 = 0;
        v9 = 1;
        do
        {
          if ( (unsigned __int8)v9 & (unsigned __int8)*v7 )
          {
            v10 = v6 + v8;
            v11 = v2->mMapEntitiesList[v6 + v8].ID;
            if ( v2->mEntity[v11].mIsAlpha || v2->mEntityList[v11].IsParticle )
              CAlpha::SetAlphaEntityStack(&v2->mAlpha, v10);
            else
              CBsp::RenderOneEntityRender(v2, v10);
          }
          ++v8;
          v9 *= 2;
        }
        while ( v8 < 8 );
      }
      v6 -= 8;
      --v7;
      --v5;
    }
    while ( v5 >= 0 );
  }
}
