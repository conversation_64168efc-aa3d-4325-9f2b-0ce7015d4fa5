/*
 * Function: ??D?$_Deque_const_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@QEBAAEBUMessageRange@MeterFilter@CryptoPP@@XZ
 * Address: 0x140600D30
 */

__int64 __fastcall std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*(__int64 a1)
{
  __int64 v1; // rt1@1
  unsigned __int64 v3; // [sp+0h] [bp-18h]@1

  v3 = *(_QWORD *)(a1 + 24);
  v1 = *(_QWORD *)(a1 + 24);
  if ( *(_QWORD *)(*(_QWORD *)(a1 + 16) + 32i64) <= v3 )
    v3 -= *(_QWORD *)(*(_QWORD *)(a1 + 16) + 32i64);
  return *(_QWORD *)(*(_QWORD *)(*(_QWORD *)(a1 + 16) + 24i64) + 8 * v3);
}
