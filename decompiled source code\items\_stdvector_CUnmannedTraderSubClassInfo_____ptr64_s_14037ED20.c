/*
 * Function: _std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::insert_::_1_::dtor$4
 * Address: 0x14037ED20
 */

void __fastcall std::vector_CUnmannedTraderSubClassInfo_____ptr64_std::allocator_CUnmannedTraderSubClassInfo_____ptr64___::insert_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 2 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(*(std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > **)(a2 + 216));
  }
}
