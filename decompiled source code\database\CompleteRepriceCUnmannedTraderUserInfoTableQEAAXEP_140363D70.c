/*
 * Function: ?CompleteReprice@CUnmannedTraderUserInfoTable@@QEAAXEPEAD@Z
 * Address: 0x140363D70
 */

void __fastcall CUnmannedTraderUserInfoTable::CompleteReprice(CUnmannedTraderUserInfoTable *this, char byRet, char *pLoadData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  CUnmannedTraderUserInfo *v7; // [sp+28h] [bp-10h]@4
  CUnmannedTraderUserInfoTable *v8; // [sp+40h] [bp+8h]@1
  char v9; // [sp+48h] [bp+10h]@1
  char *pLoadDataa; // [sp+50h] [bp+18h]@1

  pLoadDataa = pLoadData;
  v9 = byRet;
  v8 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = pLoadData;
  v7 = CUnmannedTraderUserInfoTable::FindUser(v8, *((_WORD *)pLoadData + 2), *((_DWORD *)pLoadData + 2));
  if ( !CUnmannedTraderUserInfo::IsNull(v7) )
  {
    if ( *(&g_Player.m_bOper + 50856 * CUnmannedTraderUserInfo::GetIndex(v7)) )
    {
      CUnmannedTraderUserInfo::ClearRequest(v7);
      CUnmannedTraderUserInfo::CompleteReprice(v7, v9, pLoadDataa, v8->m_pkLogger);
    }
  }
}
