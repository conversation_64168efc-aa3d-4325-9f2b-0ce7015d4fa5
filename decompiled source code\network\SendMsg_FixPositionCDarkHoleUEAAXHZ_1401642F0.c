/*
 * Function: ?SendMsg_FixPosition@CDarkHole@@UEAAXH@Z
 * Address: 0x1401642F0
 */

void __fastcall CDarkHole::SendMsg_FixPosition(CDarkHole *this, int n)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-A8h]@1
  _darkhole_fixpositon_zocl v6; // [sp+38h] [bp-70h]@4
  char pbyType; // [sp+74h] [bp-34h]@4
  char v8; // [sp+75h] [bp-33h]@4
  unsigned __int64 v9; // [sp+90h] [bp-18h]@4
  CDarkHole *v10; // [sp+B0h] [bp+8h]@1
  int dwClientIndex; // [sp+B8h] [bp+10h]@1

  dwClientIndex = n;
  v10 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v5 ^ _security_cookie;
  v6.wQuestIndex = v10->m_pRecordSet->m_dwIndex;
  v6.wIndex = v10->m_ObjID.m_wIndex;
  v6.dwSerial = v10->m_dwObjSerial;
  FloatToShort(v10->m_fCurPos, v6.zPos, 3);
  v6.bHurry = v10->m_bHurry;
  strcpy_0(v6.wszOpenerName, v10->m_wszOpenerName);
  v6.dwOpenerSerial = v10->m_dwOpenerSerial;
  pbyType = 35;
  v8 = 104;
  v4 = _darkhole_fixpositon_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, (char *)&v6, v4);
}
