/*
 * Function: SQLGetPrivateProfileStringW
 * Address: 0x1404DB150
 */

int __fastcall SQLGetPrivateProfileStringW(const unsigned __int16 *lpszSection, const unsigned __int16 *lpszEntry, const unsigned __int16 *lpszDefault, unsigned __int16 *lpszRetBuffer, int cbRetBuffer, const unsigned __int16 *lpszFilename)
{
  const unsigned __int16 *v6; // rbp@1
  unsigned __int16 *v7; // rbx@1
  const unsigned __int16 *v8; // rdi@1
  const unsigned __int16 *v9; // rsi@1
  __int64 (__cdecl *v10)(); // rax@1
  int result; // eax@2

  v6 = lpszSection;
  v7 = lpszRetBuffer;
  v8 = lpszDefault;
  v9 = lpszEntry;
  v10 = ODBC___GetSetupProc("SQLGetPrivateProfileStringW");
  if ( v10 )
    result = ((int (__fastcall *)(const unsigned __int16 *, const unsigned __int16 *, const unsigned __int16 *, unsigned __int16 *))v10)(
               v6,
               v9,
               v8,
               v7);
  else
    result = 0;
  return result;
}
