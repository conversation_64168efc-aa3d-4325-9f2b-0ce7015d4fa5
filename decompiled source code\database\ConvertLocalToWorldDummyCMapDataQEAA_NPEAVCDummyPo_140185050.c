/*
 * Function: ?ConvertLocalToWorldDummy@CMapData@@QEAA_NPEAVCDummyPosTable@@_N@Z
 * Address: 0x140185050
 */

char __fastcall CMapData::ConvertLocalToWorldDummy(CMapData *this, CDummyPosTable *pPosTable, bool bCheckCenter)
{
  __int64 *v3; // rdi@1
  signed __int64 j; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  int i; // [sp+24h] [bp-14h]@4
  _dummy_position *pPos; // [sp+28h] [bp-10h]@6
  CMapData *v10; // [sp+40h] [bp+8h]@1
  CDummyPosTable *v11; // [sp+48h] [bp+10h]@1

  v11 = pPosTable;
  v10 = this;
  v3 = &v6;
  for ( j = 12i64; j; --j )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = CDummyPosTable::GetRecordNum(pPosTable);
  for ( i = 0; i < v7; ++i )
  {
    pPos = CDummyPosTable::GetRecord(v11, i);
    if ( !CMapData::ConvertLocal(v10, pPos) )
      return 0;
    CMapData::CheckCenterPosDummy(v10, pPos);
  }
  return 1;
}
