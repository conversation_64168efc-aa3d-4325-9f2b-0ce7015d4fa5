/*
 * Function: ??0?$vector@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAA@XZ
 * Address: 0x14058DCF0
 */

__int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(__int64 a1)
{
  __int64 v1; // rax@1
  char v3; // [sp+20h] [bp-18h]@1
  char *v4; // [sp+28h] [bp-10h]@1
  __int64 v5; // [sp+40h] [bp+8h]@1

  v5 = a1;
  v4 = &v3;
  LODWORD(v1) = std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>(&v3);
  std::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Vector_val<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>(
    v5,
    v1);
  std::vector<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::_Buy(
    v5,
    0i64);
  return v5;
}
