/*
 * Function: ?Initialize@CChiNetworkEX@@QEAAHXZ
 * Address: 0x14040FA80
 */

signed __int64 __fastcall CChiNetworkEX::Initialize(CChiNetworkEX *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CChiNetworkEX *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CChiNetworkEX::LoadINIFile(v5) )
  {
    if ( CNetwork::LoadDll((CNetwork *)&v5->vfptr, "CHI_NETD.dll") )
    {
      ((void (__fastcall *)(bool (__fastcall *)(unsigned int, unsigned int, _MSG_HEADER *, char *)))v5->SetDataAnalysisFunc)(CChiNetworkEX::s_DataAnalysis);
      CNetwork::InitNetwork((CNetwork *)&v5->vfptr);
      CMyTimer::BeginTimer(&v5->m_kCheckApexLineTimer, 0x2710u);
      result = 0i64;
    }
    else
    {
      result = 4294967294i64;
    }
  }
  else
  {
    result = 0xFFFFFFFFi64;
  }
  return result;
}
