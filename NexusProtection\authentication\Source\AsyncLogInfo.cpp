#include "../Headers/AsyncLogInfo.h"
#include <algorithm>
#include <sstream>
#include <iomanip>
#include <cstring>
#include <stdexcept>
#include <fstream>

namespace NexusProtection::Authentication {

    // AsyncLogTimer implementation

    AsyncLogTimer::AsyncLogTimer() = default;

    AsyncLogTimer::~AsyncLogTimer() {
        StopTimer();
    }

    void AsyncLogTimer::BeginTimer(uint32_t delayMs) {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        m_delay = std::chrono::milliseconds(delayMs);
        m_startTime = std::chrono::steady_clock::now();
        m_isRunning = true;
    }

    bool AsyncLogTimer::CountingTimer() const {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        if (!m_isRunning) {
            return false;
        }
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
        return elapsed >= m_delay;
    }

    void AsyncLogTimer::StopTimer() {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        m_isRunning = false;
    }

    void AsyncLogTimer::Reset() {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        m_startTime = std::chrono::steady_clock::now();
    }

    uint32_t AsyncLogTimer::GetElapsedMs() const {
        std::lock_guard<std::mutex> lock(m_timerMutex);
        if (!m_isRunning) {
            return 0;
        }
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
        return static_cast<uint32_t>(elapsed.count());
    }

    // CAsyncLogInfo implementation

    CAsyncLogInfo::CAsyncLogInfo() = default;

    CAsyncLogInfo::~CAsyncLogInfo() {
        CleanupResources();
    }

    CAsyncLogInfo::CAsyncLogInfo(CAsyncLogInfo&& other) noexcept {
        MoveFrom(std::move(other));
    }

    CAsyncLogInfo& CAsyncLogInfo::operator=(CAsyncLogInfo&& other) noexcept {
        if (this != &other) {
            CleanupResources();
            MoveFrom(std::move(other));
        }
        return *this;
    }

    bool CAsyncLogInfo::Init(AsyncLogType logType, 
                            const std::string& dirPath, 
                            const std::string& typeName, 
                            bool addDateFileName,
                            uint32_t updateFileNameDelay, 
                            CLogFile* logLoading) {
        
        // Validate parameters
        if (!ValidateInitParameters(logType, dirPath, typeName, logLoading)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_dataMutex);

        try {
            // Set basic properties
            m_logType = logType;
            m_typeName = typeName;
            m_addDateFileName = addDateFileName;
            m_updateFileNameDelay = updateFileNameDelay;

            // Generate file paths
            m_logFileName = GenerateLogFileName(dirPath, typeName, addDateFileName);
            m_logDirPath = GenerateLogDirPath(dirPath, typeName);

            // Create directory if it doesn't exist
            if (!CreateLogDirectory()) {
                LogError("Failed to create log directory: " + m_logDirPath, logLoading);
                return false;
            }

            // Delete existing log file
            DeleteLogFile();

            // Initialize timer if needed
            if (updateFileNameDelay != static_cast<uint32_t>(-1) && updateFileNameDelay >= 10000) {
                m_timer = std::make_unique<AsyncLogTimer>();
                if (m_timer) {
                    m_timer->BeginTimer(updateFileNameDelay);
                } else {
                    LogError("Failed to create timer for log updates", logLoading);
                    return false;
                }
            }

            m_isInitialized = true;
            return true;

        } catch (const std::exception& e) {
            LogError("Exception during initialization: " + std::string(e.what()), logLoading);
            return false;
        }
    }

    uint32_t CAsyncLogInfo::GetCount() const {
        return m_logCount.load();
    }

    void CAsyncLogInfo::IncreaseCount() {
        ++m_logCount;
    }

    void CAsyncLogInfo::ResetCount() {
        m_logCount = 0;
    }

    void CAsyncLogInfo::UpdateLogFileName() {
        if (!m_timer || !m_timer->CountingTimer()) {
            return;
        }

        std::lock_guard<std::mutex> lock(m_fileNameMutex);

        try {
            std::string dateTimeStr = GetCurrentDateTimeString();
            if (dateTimeStr.empty()) {
                return;
            }

            std::string newFileName = m_logDirPath + "/" + m_typeName + "_" + dateTimeStr + ".log";
            
            // Update filename atomically
            {
                std::lock_guard<std::mutex> dataLock(m_dataMutex);
                m_logFileName = newFileName;
            }

            // Reset timer for next update
            m_timer->Reset();

        } catch (const std::exception&) {
            // Silently handle errors in filename update
        }
    }

    void CAsyncLogInfo::SetUpdateDelay(uint32_t delayMs) {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        m_updateFileNameDelay = delayMs;
        
        if (m_timer && delayMs >= 10000) {
            m_timer->BeginTimer(delayMs);
        }
    }

    uint32_t CAsyncLogInfo::GetUpdateDelay() const {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        return m_updateFileNameDelay;
    }

    bool CAsyncLogInfo::IsTimerActive() const {
        return m_timer && m_timer->IsRunning();
    }

    void CAsyncLogInfo::StartTimer() {
        if (m_timer && m_updateFileNameDelay >= 10000) {
            m_timer->BeginTimer(m_updateFileNameDelay);
        }
    }

    void CAsyncLogInfo::StopTimer() {
        if (m_timer) {
            m_timer->StopTimer();
        }
    }

    bool CAsyncLogInfo::CreateLogDirectory() const {
        try {
            std::filesystem::path dirPath(m_logDirPath);
            return std::filesystem::create_directories(dirPath) || std::filesystem::exists(dirPath);
        } catch (const std::exception&) {
            return false;
        }
    }

    bool CAsyncLogInfo::DeleteLogFile() const {
        try {
            std::filesystem::path filePath(m_logFileName);
            if (std::filesystem::exists(filePath)) {
                return std::filesystem::remove(filePath);
            }
            return true; // File doesn't exist, consider it "deleted"
        } catch (const std::exception&) {
            return false;
        }
    }

    bool CAsyncLogInfo::LogFileExists() const {
        try {
            return std::filesystem::exists(m_logFileName);
        } catch (const std::exception&) {
            return false;
        }
    }

    std::filesystem::path CAsyncLogInfo::GetFullLogPath() const {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        return std::filesystem::path(m_logFileName);
    }

    std::string CAsyncLogInfo::ToString() const {
        std::lock_guard<std::mutex> lock(m_dataMutex);
        std::ostringstream oss;
        oss << "CAsyncLogInfo{";
        oss << "Type: " << AsyncLogTypeToString(m_logType) << ", ";
        oss << "TypeName: \"" << m_typeName << "\", ";
        oss << "DirPath: \"" << m_logDirPath << "\", ";
        oss << "FileName: \"" << m_logFileName << "\", ";
        oss << "Count: " << m_logCount.load() << ", ";
        oss << "UpdateDelay: " << m_updateFileNameDelay << "ms, ";
        oss << "TimerActive: " << (IsTimerActive() ? "true" : "false") << ", ";
        oss << "Initialized: " << (m_isInitialized ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    // Static utility methods

    std::string CAsyncLogInfo::AsyncLogTypeToString(AsyncLogType type) {
        switch (type) {
            case AsyncLogType::SYSTEM_LOG: return "System";
            case AsyncLogType::ERROR_LOG: return "Error";
            case AsyncLogType::DEBUG_LOG: return "Debug";
            case AsyncLogType::NETWORK_LOG: return "Network";
            case AsyncLogType::DATABASE_LOG: return "Database";
            case AsyncLogType::SECURITY_LOG: return "Security";
            case AsyncLogType::PERFORMANCE_LOG: return "Performance";
            case AsyncLogType::USER_ACTION_LOG: return "UserAction";
            case AsyncLogType::BILLING_LOG: return "Billing";
            case AsyncLogType::TRADE_LOG: return "Trade";
            case AsyncLogType::GUILD_LOG: return "Guild";
            case AsyncLogType::CHAT_LOG: return "Chat";
            case AsyncLogType::ADMIN_LOG: return "Admin";
            case AsyncLogType::AUDIT_LOG: return "Audit";
            case AsyncLogType::CUSTOM_LOG: return "Custom";
            default: return "Unknown";
        }
    }

    AsyncLogType CAsyncLogInfo::StringToAsyncLogType(const std::string& typeStr) {
        if (typeStr == "System") return AsyncLogType::SYSTEM_LOG;
        if (typeStr == "Error") return AsyncLogType::ERROR_LOG;
        if (typeStr == "Debug") return AsyncLogType::DEBUG_LOG;
        if (typeStr == "Network") return AsyncLogType::NETWORK_LOG;
        if (typeStr == "Database") return AsyncLogType::DATABASE_LOG;
        if (typeStr == "Security") return AsyncLogType::SECURITY_LOG;
        if (typeStr == "Performance") return AsyncLogType::PERFORMANCE_LOG;
        if (typeStr == "UserAction") return AsyncLogType::USER_ACTION_LOG;
        if (typeStr == "Billing") return AsyncLogType::BILLING_LOG;
        if (typeStr == "Trade") return AsyncLogType::TRADE_LOG;
        if (typeStr == "Guild") return AsyncLogType::GUILD_LOG;
        if (typeStr == "Chat") return AsyncLogType::CHAT_LOG;
        if (typeStr == "Admin") return AsyncLogType::ADMIN_LOG;
        if (typeStr == "Audit") return AsyncLogType::AUDIT_LOG;
        if (typeStr == "Custom") return AsyncLogType::CUSTOM_LOG;
        return AsyncLogType::UNKNOWN;
    }

    std::string CAsyncLogInfo::GetCurrentDateTimeString() {
        try {
            auto now = std::chrono::system_clock::now();
            auto time_t = std::chrono::system_clock::to_time_t(now);

            std::tm timeinfo;
#ifdef _WIN32
            if (localtime_s(&timeinfo, &time_t) != 0) {
                return "";
            }
#else
            if (localtime_r(&time_t, &timeinfo) == nullptr) {
                return "";
            }
#endif

            std::ostringstream oss;
            oss << std::put_time(&timeinfo, "%Y%m%d_%H%M%S");
            return oss.str();
        } catch (const std::exception&) {
            return "";
        }
    }

    bool CAsyncLogInfo::IsValidLogType(AsyncLogType type) {
        return type >= AsyncLogType::SYSTEM_LOG && type < AsyncLogType::MAX_LOG_TYPES;
    }

    // Private helper methods

    bool CAsyncLogInfo::ValidateInitParameters(AsyncLogType logType,
                                              const std::string& dirPath,
                                              const std::string& typeName,
                                              CLogFile* logLoading) const {
        if (!IsValidLogType(logType)) {
            LogError("Invalid log type: " + std::to_string(static_cast<int>(logType)), logLoading);
            return false;
        }

        if (dirPath.empty()) {
            LogError("Directory path cannot be empty", logLoading);
            return false;
        }

        if (typeName.empty()) {
            LogError("Type name cannot be empty", logLoading);
            return false;
        }

        return true;
    }

    std::string CAsyncLogInfo::GenerateLogFileName(const std::string& dirPath,
                                                  const std::string& typeName,
                                                  bool addDate) const {
        std::ostringstream oss;
        oss << dirPath << "/" << typeName;

        if (addDate) {
            std::string dateTime = GetCurrentDateTimeString();
            if (!dateTime.empty()) {
                oss << "_" << dateTime;
            }
        }

        oss << ".log";
        return oss.str();
    }

    std::string CAsyncLogInfo::GenerateLogDirPath(const std::string& dirPath,
                                                 const std::string& typeName) const {
        return dirPath + "/" + typeName;
    }

    void CAsyncLogInfo::CleanupResources() {
        StopTimer();
        m_timer.reset();
        m_isInitialized = false;
    }

    void CAsyncLogInfo::LogError(const std::string& message, CLogFile* logFile) const {
        // In the original code, this would call CLogFile::Write
        // For now, we'll just store the error or output to debug
        // In a real implementation, this would integrate with the logging system

        // TODO: Integrate with actual CLogFile when available
        // if (logFile) {
        //     logFile->Write("CAsyncLogInfo Error: %s", message.c_str());
        // }
    }

    void CAsyncLogInfo::MoveFrom(CAsyncLogInfo&& other) noexcept {
        m_logType = other.m_logType;
        m_logCount.store(other.m_logCount.load());
        m_logDirPath = std::move(other.m_logDirPath);
        m_logFileName = std::move(other.m_logFileName);
        m_typeName = std::move(other.m_typeName);
        m_timer = std::move(other.m_timer);
        m_updateFileNameDelay = other.m_updateFileNameDelay;
        m_addDateFileName = other.m_addDateFileName;
        m_isInitialized.store(other.m_isInitialized.load());

        // Reset other object
        other.m_logType = AsyncLogType::UNKNOWN;
        other.m_logCount = 0;
        other.m_updateFileNameDelay = 10000;
        other.m_addDateFileName = true;
        other.m_isInitialized = false;
    }

    // CAsyncLogManager implementation

    CAsyncLogManager::CAsyncLogManager() = default;

    CAsyncLogManager::~CAsyncLogManager() {
        Cleanup();
    }

    bool CAsyncLogManager::RegisterLogInfo(AsyncLogType type, std::unique_ptr<CAsyncLogInfo> logInfo) {
        if (!logInfo || !CAsyncLogInfo::IsValidLogType(type)) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_managerMutex);
        m_logInfoMap[type] = std::move(logInfo);
        return true;
    }

    CAsyncLogInfo* CAsyncLogManager::GetLogInfo(AsyncLogType type) const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        auto it = m_logInfoMap.find(type);
        return it != m_logInfoMap.end() ? it->second.get() : nullptr;
    }

    bool CAsyncLogManager::RemoveLogInfo(AsyncLogType type) {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        auto it = m_logInfoMap.find(type);
        if (it != m_logInfoMap.end()) {
            m_logInfoMap.erase(it);
            return true;
        }
        return false;
    }

    void CAsyncLogManager::UpdateAllLogFileNames() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        for (auto& pair : m_logInfoMap) {
            if (pair.second) {
                pair.second->UpdateLogFileName();
            }
        }
    }

    void CAsyncLogManager::IncreaseCountForType(AsyncLogType type) {
        CAsyncLogInfo* logInfo = GetLogInfo(type);
        if (logInfo) {
            logInfo->IncreaseCount();
        }
    }

    uint32_t CAsyncLogManager::GetTotalLogCount() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        uint32_t total = 0;
        for (const auto& pair : m_logInfoMap) {
            if (pair.second) {
                total += pair.second->GetCount();
            }
        }
        return total;
    }

    uint32_t CAsyncLogManager::GetCountForType(AsyncLogType type) const {
        CAsyncLogInfo* logInfo = GetLogInfo(type);
        return logInfo ? logInfo->GetCount() : 0;
    }

    void CAsyncLogManager::SetGlobalUpdateDelay(uint32_t delayMs) {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        m_globalUpdateDelay = delayMs;

        for (auto& pair : m_logInfoMap) {
            if (pair.second) {
                pair.second->SetUpdateDelay(delayMs);
            }
        }
    }

    void CAsyncLogManager::StartAllTimers() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        for (auto& pair : m_logInfoMap) {
            if (pair.second) {
                pair.second->StartTimer();
            }
        }
    }

    void CAsyncLogManager::StopAllTimers() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        for (auto& pair : m_logInfoMap) {
            if (pair.second) {
                pair.second->StopTimer();
            }
        }
    }

    std::vector<AsyncLogType> CAsyncLogManager::GetRegisteredTypes() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        std::vector<AsyncLogType> types;
        types.reserve(m_logInfoMap.size());

        for (const auto& pair : m_logInfoMap) {
            types.push_back(pair.first);
        }

        return types;
    }

    std::string CAsyncLogManager::GetStatusReport() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        std::ostringstream oss;
        oss << "CAsyncLogManager Status Report:\n";
        oss << "Total registered log types: " << m_logInfoMap.size() << "\n";
        oss << "Global update delay: " << m_globalUpdateDelay << "ms\n";
        oss << "Total log count: " << GetTotalLogCount() << "\n\n";

        for (const auto& pair : m_logInfoMap) {
            if (pair.second) {
                oss << "- " << CAsyncLogInfo::AsyncLogTypeToString(pair.first) << ": ";
                oss << pair.second->GetCount() << " logs, ";
                oss << (pair.second->IsTimerActive() ? "Timer Active" : "Timer Inactive") << "\n";
            }
        }

        return oss.str();
    }

    void CAsyncLogManager::Cleanup() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        m_logInfoMap.clear();
    }

    // AsyncLogFactory implementation

    std::unique_ptr<CAsyncLogInfo> AsyncLogFactory::CreateAsyncLogInfo(AsyncLogType type,
                                                                      const std::string& dirPath,
                                                                      const std::string& typeName,
                                                                      bool addDateFileName,
                                                                      uint32_t updateDelay) {
        auto logInfo = std::make_unique<CAsyncLogInfo>();

        if (logInfo->Init(type, dirPath, typeName, addDateFileName, updateDelay)) {
            return logInfo;
        }

        return nullptr;
    }

    std::unique_ptr<CAsyncLogManager> AsyncLogFactory::CreateAsyncLogManager() {
        return std::make_unique<CAsyncLogManager>();
    }

    std::unique_ptr<IAsyncLogTimer> AsyncLogFactory::CreateTimer() {
        return std::make_unique<AsyncLogTimer>();
    }

    std::unique_ptr<CAsyncLogManager> AsyncLogFactory::CreateStandardLogManager(const std::string& baseDir) {
        auto manager = std::make_unique<CAsyncLogManager>();

        // Register standard log types
        std::vector<std::pair<AsyncLogType, std::string>> standardLogs = {
            {AsyncLogType::SYSTEM_LOG, "System"},
            {AsyncLogType::ERROR_LOG, "Error"},
            {AsyncLogType::NETWORK_LOG, "Network"},
            {AsyncLogType::DATABASE_LOG, "Database"},
            {AsyncLogType::SECURITY_LOG, "Security"}
        };

        for (const auto& logConfig : standardLogs) {
            auto logInfo = CreateAsyncLogInfo(
                logConfig.first,
                baseDir,
                logConfig.second,
                true,  // Add date to filename
                30000  // 30 second update interval
            );

            if (logInfo) {
                manager->RegisterLogInfo(logConfig.first, std::move(logInfo));
            }
        }

        return manager;
    }

    std::unique_ptr<CAsyncLogManager> AsyncLogFactory::CreateDebugLogManager(const std::string& baseDir) {
        auto manager = std::make_unique<CAsyncLogManager>();

        // Register debug-focused log types
        std::vector<std::pair<AsyncLogType, std::string>> debugLogs = {
            {AsyncLogType::DEBUG_LOG, "Debug"},
            {AsyncLogType::ERROR_LOG, "Error"},
            {AsyncLogType::PERFORMANCE_LOG, "Performance"},
            {AsyncLogType::ADMIN_LOG, "Admin"}
        };

        for (const auto& logConfig : debugLogs) {
            auto logInfo = CreateAsyncLogInfo(
                logConfig.first,
                baseDir,
                logConfig.second,
                true,  // Add date to filename
                10000  // 10 second update interval for faster debugging
            );

            if (logInfo) {
                manager->RegisterLogInfo(logConfig.first, std::move(logInfo));
            }
        }

        return manager;
    }

    std::unique_ptr<CAsyncLogManager> AsyncLogFactory::CreateProductionLogManager(const std::string& baseDir) {
        auto manager = std::make_unique<CAsyncLogManager>();

        // Register production log types
        std::vector<std::pair<AsyncLogType, std::string>> productionLogs = {
            {AsyncLogType::SYSTEM_LOG, "System"},
            {AsyncLogType::ERROR_LOG, "Error"},
            {AsyncLogType::SECURITY_LOG, "Security"},
            {AsyncLogType::AUDIT_LOG, "Audit"},
            {AsyncLogType::BILLING_LOG, "Billing"},
            {AsyncLogType::TRADE_LOG, "Trade"},
            {AsyncLogType::USER_ACTION_LOG, "UserAction"}
        };

        for (const auto& logConfig : productionLogs) {
            auto logInfo = CreateAsyncLogInfo(
                logConfig.first,
                baseDir,
                logConfig.second,
                true,   // Add date to filename
                60000   // 60 second update interval for production
            );

            if (logInfo) {
                manager->RegisterLogInfo(logConfig.first, std::move(logInfo));
            }
        }

        return manager;
    }

} // namespace NexusProtection::Authentication
