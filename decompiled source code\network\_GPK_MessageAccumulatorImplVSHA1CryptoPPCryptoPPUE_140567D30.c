/*
 * Function: ??_G?$PK_MessageAccumulatorImpl@VSHA1@CryptoPP@@@CryptoPP@@UEAAPEAXI@Z
 * Address: 0x140567D30
 */

void *__fastcall CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::`scalar deleting destructor'(void *a1, int a2)
{
  void *v3; // [sp+30h] [bp+8h]@1
  int v4; // [sp+38h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::~PK_MessageAccumulatorImpl<CryptoPP::SHA1>();
  if ( v4 & 1 )
    operator delete(v3);
  return v3;
}
