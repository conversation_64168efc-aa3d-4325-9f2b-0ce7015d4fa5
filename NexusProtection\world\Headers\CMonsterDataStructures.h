#pragma once

/**
 * @file CMonsterDataStructures.h
 * @brief Monster Data Structures and Support Classes
 * 
 * Provides essential data structures for monster state management,
 * emotion presentation, damage tolerance, and Lua signal reaction
 * systems for the NexusProtection engine.
 * 
 * Refactored from decompiled C source to modern C++20 standards.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include <cstdint>
#include <string>
#include <memory>
#include <mutex>
#include <chrono>
#include <array>
#include <vector>
#include <unordered_map>
#include <functional>

namespace NexusProtection::World {

    // Forward declarations
    class CMonster;
    struct _object_id;

    /**
     * @brief Monster state enumeration
     */
    enum class MonsterState : uint8_t {
        Idle = 0,
        Moving = 1,
        Attacking = 2,
        Defending = 3,
        Casting = 4,
        Stunned = 5,
        Dead = 6,
        Spawning = 7,
        Despawning = 8,
        Sleeping = 9,
        Enraged = 10,
        Fleeing = 11
    };

    /**
     * @brief Monster emotion type enumeration
     */
    enum class MonsterEmotion : uint8_t {
        Neutral = 0,
        Angry = 1,
        Happy = 2,
        Sad = 3,
        Excited = 4,
        Fearful = 5,
        Confused = 6,
        Aggressive = 7,
        Defensive = 8,
        Curious = 9
    };

    /**
     * @brief Damage tolerance level enumeration
     */
    enum class DamageToleranceLevel : uint8_t {
        VeryLow = 0,
        Low = 1,
        Normal = 2,
        High = 3,
        VeryHigh = 4,
        Immune = 5
    };

    /**
     * @brief Lua signal type enumeration
     */
    enum class LuaSignalType : uint8_t {
        OnSpawn = 0,
        OnDeath = 1,
        OnDamage = 2,
        OnHeal = 3,
        OnAttack = 4,
        OnSkillUse = 5,
        OnStateChange = 6,
        OnTargetChange = 7,
        OnEmotionChange = 8,
        OnCustomEvent = 9
    };

    /**
     * @brief Monster State Data Structure
     * 
     * Manages comprehensive monster state information including
     * current state, transitions, timing, and state-specific data.
     */
    struct MonsterStateData {
        // Core state information
        MonsterState currentState{MonsterState::Idle};
        MonsterState previousState{MonsterState::Idle};
        MonsterState nextState{MonsterState::Idle};
        
        // State timing
        std::chrono::steady_clock::time_point stateStartTime;
        std::chrono::steady_clock::time_point lastStateChange;
        uint32_t stateDuration{0};                          ///< Duration in milliseconds
        uint32_t stateTimeout{0};                           ///< Timeout in milliseconds
        
        // State flags
        bool isTransitioning{false};
        bool canInterrupt{true};
        bool isLooping{false};
        bool hasTimeout{false};
        
        // State-specific data
        uint32_t stateData[4]{0, 0, 0, 0};                  ///< Generic state data array
        float stateFloatData[4]{0.0f, 0.0f, 0.0f, 0.0f};   ///< Generic float data array
        void* statePointer{nullptr};                        ///< Generic pointer data
        
        // Animation and visual data
        uint32_t animationId{0};
        uint32_t effectId{0};
        uint32_t soundId{0};
        
        // Constructor
        MonsterStateData();
        
        // Core methods
        void Initialize();
        void Reset();
        bool Update(float deltaTime);
        
        // State management
        bool CanTransitionTo(MonsterState newState) const;
        bool TransitionTo(MonsterState newState);
        void ForceState(MonsterState newState);
        
        // Timing methods
        uint32_t GetElapsedTime() const;
        uint32_t GetRemainingTime() const;
        bool HasExpired() const;
        
        // Utility methods
        bool IsValid() const;
        std::string ToString() const;
        std::string GetStateString() const;
    };

    /**
     * @brief Emotion Presentation Checker Class
     * 
     * Manages monster emotion states, presentation logic,
     * and emotion-based behavior modifications.
     */
    class EmotionPresentationChecker {
    public:
        // Constants
        static constexpr size_t MAX_EMOTIONS = 16;
        static constexpr uint32_t EMOTION_DURATION_MS = 5000;  // 5 seconds default
        static constexpr float EMOTION_INTENSITY_MAX = 1.0f;

        // Constructor and Destructor
        EmotionPresentationChecker();
        virtual ~EmotionPresentationChecker();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // Emotion management
        virtual bool SetEmotion(MonsterEmotion emotion, float intensity = 1.0f);
        virtual bool AddEmotion(MonsterEmotion emotion, float intensity, uint32_t duration);
        virtual bool RemoveEmotion(MonsterEmotion emotion);
        virtual void ClearEmotions();

        // Emotion queries
        virtual MonsterEmotion GetPrimaryEmotion() const;
        virtual float GetEmotionIntensity(MonsterEmotion emotion) const;
        virtual bool HasEmotion(MonsterEmotion emotion) const;
        virtual std::vector<MonsterEmotion> GetActiveEmotions() const;

        // Presentation logic
        virtual bool ShouldPresentEmotion(MonsterEmotion emotion) const;
        virtual uint32_t GetEmotionAnimationId(MonsterEmotion emotion) const;
        virtual uint32_t GetEmotionEffectId(MonsterEmotion emotion) const;
        virtual uint32_t GetEmotionSoundId(MonsterEmotion emotion) const;

        // Behavior modifiers
        virtual float GetDamageModifier() const;
        virtual float GetSpeedModifier() const;
        virtual float GetAggroModifier() const;

        // Configuration
        virtual void SetEmotionDuration(uint32_t duration) { m_defaultDuration = duration; }
        virtual void SetIntensityThreshold(float threshold) { m_intensityThreshold = threshold; }

        // Utility
        virtual std::string GetDebugInfo() const;

    protected:
        struct EmotionEntry {
            MonsterEmotion emotion{MonsterEmotion::Neutral};
            float intensity{0.0f};
            uint32_t duration{EMOTION_DURATION_MS};
            std::chrono::steady_clock::time_point startTime;
            bool isActive{false};
            
            bool IsExpired() const;
            float GetDecayedIntensity() const;
        };

        std::vector<EmotionEntry> m_emotions;
        uint32_t m_defaultDuration{EMOTION_DURATION_MS};
        float m_intensityThreshold{0.1f};
        mutable std::mutex m_emotionMutex;

        virtual void UpdateEmotions(float deltaTime);
        virtual void CleanupExpiredEmotions();
        virtual EmotionEntry* FindEmotion(MonsterEmotion emotion);
        virtual float CalculateEmotionPriority(const EmotionEntry& entry) const;

    public:
        // Legacy compatibility member variables (for backward compatibility with old CMonster code)
        bool m_bIsSet{false};              ///< Whether emotion presentation is set
        CCharacter* m_pTarget{nullptr};    ///< Target character for emotion
        uint8_t m_byType{0};               ///< Emotion type
        uint16_t m_wIndex{0};              ///< Emotion index
        uint16_t m_wRandIndex{0};          ///< Random emotion index

        // Legacy static method for compatibility
        static void ReSet(EmotionPresentationChecker* pChecker);
    };

    /**
     * @brief Monster SF Container Damage Tolerance Class
     * 
     * Manages monster damage tolerance levels for different
     * damage types and special effects (SF) containers.
     */
    class MonsterSFContDamageTolerance {
    public:
        // Constants
        static constexpr size_t MAX_DAMAGE_TYPES = 32;
        static constexpr size_t MAX_SF_CONTAINERS = 192;
        static constexpr float DEFAULT_TOLERANCE = 1.0f;

        // Constructor and Destructor
        MonsterSFContDamageTolerance();
        virtual ~MonsterSFContDamageTolerance();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // Damage tolerance management
        virtual void SetDamageTolerance(uint32_t damageType, float tolerance);
        virtual float GetDamageTolerance(uint32_t damageType) const;
        virtual void SetToleranceLevel(uint32_t damageType, DamageToleranceLevel level);
        virtual DamageToleranceLevel GetToleranceLevel(uint32_t damageType) const;

        // SF Container management
        virtual void SetSFContainerTolerance(uint32_t containerId, float tolerance);
        virtual float GetSFContainerTolerance(uint32_t containerId) const;
        virtual bool IsSFContainerImmune(uint32_t containerId) const;

        // Damage calculation
        virtual float CalculateDamageReduction(uint32_t damageType, float baseDamage) const;
        virtual float CalculateSFReduction(uint32_t containerId, float baseEffect) const;
        virtual bool ShouldBlockDamage(uint32_t damageType) const;

        // Bulk operations
        virtual void SetAllTolerances(float tolerance);
        virtual void ResetToDefaults();
        virtual void ApplyToleranceModifier(float modifier);

        // Configuration
        virtual void LoadToleranceData(const std::string& configFile);
        virtual void SaveToleranceData(const std::string& configFile) const;

        // Utility
        virtual std::string GetDebugInfo() const;

    protected:
        std::array<float, MAX_DAMAGE_TYPES> m_damageTolerances;
        std::array<float, MAX_SF_CONTAINERS> m_sfContainerTolerances;
        mutable std::mutex m_toleranceMutex;

        virtual float ToleranceLevelToFloat(DamageToleranceLevel level) const;
        virtual DamageToleranceLevel FloatToToleranceLevel(float tolerance) const;
        virtual void InitializeDefaults();
    };

    /**
     * @brief Lua Signal Reactor Class
     * 
     * Manages Lua script signal handling and reaction system
     * for monster events and custom scripted behaviors.
     */
    class CLuaSignalReActor {
    public:
        // Type definitions
        using LuaCallback = std::function<void(const std::string&, const std::vector<std::string>&)>;
        using SignalHandler = std::function<bool(LuaSignalType, const std::vector<std::string>&)>;

        // Constructor and Destructor
        CLuaSignalReActor();
        virtual ~CLuaSignalReActor();

        // Core functionality
        virtual bool Initialize();
        virtual void Shutdown();
        virtual void Update(float deltaTime);

        // Signal management
        virtual bool RegisterSignal(LuaSignalType signalType, const std::string& scriptFunction);
        virtual bool UnregisterSignal(LuaSignalType signalType);
        virtual void ClearSignals();

        // Signal emission
        virtual bool EmitSignal(LuaSignalType signalType, const std::vector<std::string>& parameters = {});
        virtual bool EmitCustomSignal(const std::string& signalName, const std::vector<std::string>& parameters = {});

        // Handler management
        virtual void SetSignalHandler(SignalHandler handler) { m_signalHandler = handler; }
        virtual void SetLuaCallback(LuaCallback callback) { m_luaCallback = callback; }

        // Script management
        virtual bool LoadScript(const std::string& scriptFile);
        virtual bool ExecuteScript(const std::string& script);
        virtual bool CallLuaFunction(const std::string& functionName, const std::vector<std::string>& parameters = {});

        // Query methods
        virtual bool IsSignalRegistered(LuaSignalType signalType) const;
        virtual std::vector<LuaSignalType> GetRegisteredSignals() const;
        virtual size_t GetSignalCount() const { return m_signalMap.size(); }

        // Configuration
        virtual void SetScriptTimeout(uint32_t timeoutMs) { m_scriptTimeoutMs = timeoutMs; }
        virtual void SetMaxSignalsPerFrame(uint32_t maxSignals) { m_maxSignalsPerFrame = maxSignals; }

        // Utility
        virtual std::string GetDebugInfo() const;

    protected:
        struct SignalEntry {
            LuaSignalType signalType{LuaSignalType::OnSpawn};
            std::string scriptFunction;
            bool isActive{true};
            uint32_t callCount{0};
            std::chrono::steady_clock::time_point lastCall;
        };

        std::unordered_map<LuaSignalType, SignalEntry> m_signalMap;
        std::queue<std::pair<LuaSignalType, std::vector<std::string>>> m_signalQueue;
        
        SignalHandler m_signalHandler;
        LuaCallback m_luaCallback;
        
        uint32_t m_scriptTimeoutMs{5000};  // 5 second timeout
        uint32_t m_maxSignalsPerFrame{10}; // Max signals per update
        
        mutable std::mutex m_signalMutex;

        virtual void ProcessSignalQueue();
        virtual bool ExecuteSignalScript(const SignalEntry& entry, const std::vector<std::string>& parameters);
        virtual std::string SignalTypeToString(LuaSignalType signalType) const;
    };

    /**
     * @brief Utility functions for monster data structures
     */
    namespace MonsterDataUtils {
        std::string MonsterStateToString(MonsterState state);
        std::string MonsterEmotionToString(MonsterEmotion emotion);
        std::string DamageToleranceLevelToString(DamageToleranceLevel level);
        std::string LuaSignalTypeToString(LuaSignalType signalType);
        
        MonsterState StringToMonsterState(const std::string& str);
        MonsterEmotion StringToMonsterEmotion(const std::string& str);
        DamageToleranceLevel StringToDamageToleranceLevel(const std::string& str);
        LuaSignalType StringToLuaSignalType(const std::string& str);
        
        bool IsValidState(MonsterState state);
        bool IsValidEmotion(MonsterEmotion emotion);
        bool IsValidToleranceLevel(DamageToleranceLevel level);
        bool IsValidSignalType(LuaSignalType signalType);
    }

} // namespace NexusProtection::World

// Legacy C interface for compatibility
extern "C" {
    // MonsterStateData legacy interface
    struct MonsterStateData_Legacy {
        uint32_t currentState;
        uint32_t previousState;
        uint32_t stateData[4];
        float stateFloatData[4];
        void* statePointer;
    };

    void MonsterStateData_Initialize(MonsterStateData_Legacy* data);
    void MonsterStateData_Reset(MonsterStateData_Legacy* data);
    bool MonsterStateData_TransitionTo(MonsterStateData_Legacy* data, uint32_t newState);

    // EmotionPresentationChecker legacy interface
    struct EmotionPresentationChecker_Legacy {
        void* vfptr;
        // Additional legacy fields
    };

    void EmotionPresentationChecker_Constructor(EmotionPresentationChecker_Legacy* checker);
    void EmotionPresentationChecker_Destructor(EmotionPresentationChecker_Legacy* checker);
    bool EmotionPresentationChecker_SetEmotion(EmotionPresentationChecker_Legacy* checker, uint32_t emotion, float intensity);

    // MonsterSFContDamageTolerance legacy interface
    struct MonsterSFContDamageTolerance_Legacy {
        void* vfptr;
        float damageTolerances[32];
        float sfContainerTolerances[192];
    };

    void MonsterSFContDamageTolerance_Constructor(MonsterSFContDamageTolerance_Legacy* tolerance);
    void MonsterSFContDamageTolerance_Destructor(MonsterSFContDamageTolerance_Legacy* tolerance);
    void MonsterSFContDamageTolerance_SetTolerance(MonsterSFContDamageTolerance_Legacy* tolerance, uint32_t type, float value);

    // CLuaSignalReActor legacy interface
    struct CLuaSignalReActor_Legacy {
        void* vfptr;
        // Additional legacy fields
    };

    void CLuaSignalReActor_Constructor(CLuaSignalReActor_Legacy* reactor);
    void CLuaSignalReActor_Destructor(CLuaSignalReActor_Legacy* reactor);
    bool CLuaSignalReActor_EmitSignal(CLuaSignalReActor_Legacy* reactor, uint32_t signalType);
}
