/*
 * Function: ?Init@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAA_NI@Z
 * Address: 0x1403DCB50
 */

char __fastcall GUILD_BATTLE::CGuildBattleScheduleManager::Init(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiMapCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // rax@5
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@8
  char v6; // al@8
  __int64 v7; // rax@10
  GUILD_BATTLE::CGuildBattleLogger *v8; // rax@13
  GUILD_BATTLE::CGuildBattleLogger *v9; // rax@16
  __int64 v10; // [sp+0h] [bp-68h]@1
  ATL::CTime *v11; // [sp+20h] [bp-48h]@4
  ATL::CTime result; // [sp+28h] [bp-40h]@9
  CMyTimer *v13; // [sp+30h] [bp-38h]@12
  CMyTimer *v14; // [sp+38h] [bp-30h]@9
  __int64 v15; // [sp+40h] [bp-28h]@4
  ATL::CTime *v16; // [sp+48h] [bp-20h]@5
  CMyTimer *v17; // [sp+50h] [bp-18h]@10
  GUILD_BATTLE::CGuildBattleScheduleManager *v18; // [sp+70h] [bp+8h]@1
  unsigned int uiMapCnta; // [sp+78h] [bp+10h]@1

  uiMapCnta = uiMapCnt;
  v18 = this;
  v2 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v15 = -2i64;
  v11 = (ATL::CTime *)operator new(8ui64);
  if ( v11 )
  {
    ATL::CTime::CTime(v11);
    v16 = (ATL::CTime *)v4;
  }
  else
  {
    v16 = 0i64;
  }
  v18->m_pkOldDayTime = v16;
  if ( v18->m_pkOldDayTime )
  {
    v18->m_pkOldDayTime->m_time = ATL::CTime::GetTickCount(&result)->m_time;
    v14 = (CMyTimer *)operator new(0x18ui64);
    if ( v14 )
    {
      CMyTimer::CMyTimer(v14);
      v17 = (CMyTimer *)v7;
    }
    else
    {
      v17 = 0i64;
    }
    v13 = v17;
    v18->m_pkTimer = v17;
    if ( v18->m_pkTimer )
    {
      if ( GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Init(v18->m_kSchdule, 0, uiMapCnta)
        && GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::Init(&v18->m_kSchdule[1], 1u, uiMapCnta) )
      {
        CMyTimer::BeginTimer(v18->m_pkTimer, 0x3E8u);
        v18->m_uiMapCnt = uiMapCnta;
        v6 = 1;
      }
      else
      {
        v9 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(v9, "CGuildBattleScheduler::Init(%u) m_kSchdule[].Init Fail!", uiMapCnta);
        v6 = 0;
      }
    }
    else
    {
      v8 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(v8, "CGuildBattleScheduler::Init(%u) new CMyTimer Fail!", uiMapCnta);
      v6 = 0;
    }
  }
  else
  {
    v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(v5, "CGuildBattleScheduler::Init(%u) m_pkOldDayTime NULL!", uiMapCnta);
    v6 = 0;
  }
  return v6;
}
