/*
 * Function: ?SendMsg_DTradeStartInform@CPlayer@@QEAAXPEAV1@0PEAK@Z
 * Address: 0x1400E11D0
 */

void __fastcall CPlayer::SendMsg_DTradeStartInform(CPlayer *this, CPlayer *pAsker, CPlayer *pAnswer, unsigned int *pdwKey)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned int v8; // [sp+3Ah] [bp-5Eh]@4
  char v9; // [sp+3Eh] [bp-5Ah]@4
  unsigned __int16 v10; // [sp+3Fh] [bp-59h]@4
  unsigned int v11; // [sp+41h] [bp-57h]@4
  char v12; // [sp+45h] [bp-53h]@4
  char Dst; // [sp+46h] [bp-52h]@5
  char pbyType; // [sp+74h] [bp-24h]@6
  char v15; // [sp+75h] [bp-23h]@6
  CPlayer *v16; // [sp+A0h] [bp+8h]@1

  v16 = this;
  v4 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = pAsker->m_dwObjSerial;
  *(_WORD *)szMsg = pAsker->m_ObjID.m_wIndex;
  v9 = pAsker->m_pmTrd.byEmptyInvenNum;
  v11 = pAnswer->m_dwObjSerial;
  v10 = pAnswer->m_ObjID.m_wIndex;
  v12 = pAnswer->m_pmTrd.byEmptyInvenNum;
  if ( pdwKey )
    memcpy_0(&Dst, pdwKey, 0x10ui64);
  pbyType = 18;
  v15 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, szMsg, 0x1Eu);
}
