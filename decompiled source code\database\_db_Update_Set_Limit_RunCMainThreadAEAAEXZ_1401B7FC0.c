/*
 * Function: ?_db_Update_Set_Limit_Run@CMainThread@@AEAAEXZ
 * Address: 0x1401B7FC0
 */

char __fastcall CMainThread::_db_Update_Set_Limit_Run(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CCryptor *v3; // rax@4
  __int64 v5; // [sp+0h] [bp-F8h]@1
  char pHash; // [sp+38h] [bp-C0h]@4
  char pOut; // [sp+80h] [bp-78h]@6
  char v8; // [sp+81h] [bp-77h]@6
  char v9; // [sp+D4h] [bp-24h]@6
  unsigned __int64 v10; // [sp+E0h] [bp-18h]@4
  CMainThread *v11; // [sp+100h] [bp+8h]@1

  v11 = this;
  v1 = &v5;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  v3 = CTSingleton<CCryptor>::Instance();
  if ( !CCryptor::MakeHash(v3, g_cbHashVerify, 0x20ui64, &pHash, 0x20ui64) )
    return 82;
  pOut = 0;
  memset(&v8, 0, 0x42ui64);
  MakeBinaryStr(&pHash, 0x20ui64, &pOut, 0x43ui64);
  v9 = CRFWorldDatabase::Select_Limit_Run_Record(v11->m_pWorldDB);
  if ( v9 )
  {
    if ( v9 == 2 )
    {
      if ( !CRFWorldDatabase::Insert_Set_Limit_Run(v11->m_pWorldDB, &pHash, 32) )
        return 24;
    }
    else if ( v9 == 1 )
    {
      return 24;
    }
  }
  else if ( !CRFWorldDatabase::Update_Set_Limit_Run(v11->m_pWorldDB, &pHash, 32) )
  {
    return 24;
  }
  return 0;
}
