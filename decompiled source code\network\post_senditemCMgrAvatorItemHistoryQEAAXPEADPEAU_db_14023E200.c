/*
 * Function: ?post_senditem@CMgrAvatorItemHistory@@QEAAXPEADPEAU_db_con@_STORAGE_LIST@@_KK0@Z
 * Address: 0x14023E200
 */

void __fastcall CMgrAvatorItemHistory::post_senditem(CMgrAvatorItemHistory *this, char *wszRecvName, _STORAGE_LIST::_db_con *Item, unsigned __int64 dwDur, unsigned int dwGold, char *pFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@5
  __int64 v9; // [sp+0h] [bp-E8h]@1
  char *v10; // [sp+20h] [bp-C8h]@5
  char *v11; // [sp+28h] [bp-C0h]@5
  unsigned __int64 v12; // [sp+30h] [bp-B8h]@5
  char szTran[2]; // [sp+48h] [bp-A0h]@4
  char v14; // [sp+4Ah] [bp-9Eh]@4
  char DstBuf[2]; // [sp+80h] [bp-68h]@4
  char v16; // [sp+82h] [bp-66h]@4
  _base_fld *v17; // [sp+C8h] [bp-20h]@5
  unsigned __int64 v18; // [sp+D8h] [bp-10h]@4
  CMgrAvatorItemHistory *v19; // [sp+F0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v20; // [sp+100h] [bp+18h]@1
  char *v21; // [sp+108h] [bp+20h]@1

  v21 = (char *)dwDur;
  v20 = Item;
  v19 = this;
  v6 = &v9;
  for ( i = 56i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v18 = (unsigned __int64)&v9 ^ _security_cookie;
  *(_WORD *)szTran = 0;
  memset(&v14, 0, 0xFui64);
  *(_WORD *)DstBuf = 0;
  memset(&v16, 0, 0x3Eui64);
  sData[0] = 0;
  W2M(wszRecvName, szTran, 0x11u);
  if ( v20 )
  {
    v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v20->m_byTableCode, v20->m_wItemIndex);
    v8 = DisplayItemUpgInfo(v20->m_byTableCode, v20->m_dwLv);
    v12 = v20->m_lnUID;
    v11 = v8;
    v10 = v21;
    sprintf_s(DstBuf, 0x40ui64, "%s_%I64u_@%s[%I64u]", v17->m_strCode);
  }
  else
  {
    sprintf_s(DstBuf, 0x40ui64, "NoItem");
  }
  v12 = (unsigned __int64)v19->m_szCurTime;
  v11 = v19->m_szCurDate;
  v10 = szTran;
  sprintf(sData, "[PostSystem : Send Item & Gold] - Item[%s] - Gold[%u] - Receiver[%s] - [%s %s]\r\n", DstBuf, dwGold);
  CMgrAvatorItemHistory::WriteFile(v19, pFileName, sData);
}
