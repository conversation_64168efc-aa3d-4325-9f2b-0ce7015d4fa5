/*
 * Function: ?DeleteList@TimeLimitJadeMng@@QEAA_NGPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1403FAC50
 */

char __fastcall TimeLimitJadeMng::DeleteList(TimeLimitJadeMng *this, unsigned __int16 wIdx, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  unsigned int v6; // eax@10
  unsigned int v7; // ecx@10
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned __int64 v9; // [sp+20h] [bp-28h]@10
  CLogFile *v10; // [sp+30h] [bp-18h]@10
  TimeLimitJadeMng *v11; // [sp+50h] [bp+8h]@1
  unsigned __int16 v12; // [sp+58h] [bp+10h]@1
  _STORAGE_LIST::_db_con *pkItema; // [sp+60h] [bp+18h]@1

  pkItema = pkItem;
  v12 = wIdx;
  v11 = this;
  v3 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)wIdx <= 2532 )
  {
    if ( pkItem )
    {
      if ( TimeLimitJade::DeleteUseList(v11->_ppkTimeLimitJade[wIdx], pkItem, 1)
        || TimeLimitJade::DeleteWaitList(v11->_ppkTimeLimitJade[v12], pkItema) )
      {
        result = 1;
      }
      else
      {
        v6 = pkItema->m_wItemIndex;
        v7 = pkItema->m_byTableCode;
        v10 = &v11->_kLogSys;
        v9 = pkItema->m_lnUID;
        CLogFile::Write(&v11->_kLogSys, "TimeLimitJadeMng::DeleteList() >>  - item: %d-%d[%I64u]", v7, v6);
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
