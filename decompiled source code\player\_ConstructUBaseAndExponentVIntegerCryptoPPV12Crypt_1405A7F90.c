/*
 * Function: ??$_Construct@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@U12@@std@@YAXPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@AEBU12@@Z
 * Address: 0x1405A7F90
 */

CryptoPP::Integer *__fastcall std::_Construct<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(void *a1, const struct CryptoPP::Integer *a2)
{
  CryptoPP::Integer *v3; // [sp+30h] [bp-28h]@1
  CryptoPP::Integer *v4; // [sp+40h] [bp-18h]@2
  const struct CryptoPP::Integer *v5; // [sp+68h] [bp+10h]@1

  v5 = a2;
  v3 = (CryptoPP::Integer *)operator new(0x50ui64, a1);
  if ( v3 )
    v4 = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
           v3,
           v5);
  else
    v4 = 0i64;
  return v4;
}
