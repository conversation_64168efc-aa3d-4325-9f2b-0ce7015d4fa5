/*
 * Function: ?pc_ChatRePresentationRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x140093380
 */

void __fastcall CPlayer::pc_ChatRePresentationRequest(CPlayer *this, char *pwszChatData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@7
  char *v5; // rax@11
  CChatStealSystem *v6; // rax@11
  int v7; // eax@15
  CPvpUserAndGuildRankingSystem *v8; // rax@16
  __int64 v9; // [sp+0h] [bp-1C8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-188h]@11
  char pbyType; // [sp+174h] [bp-54h]@11
  char v12; // [sp+175h] [bp-53h]@11
  int v13; // [sp+184h] [bp-44h]@11
  int j; // [sp+188h] [bp-40h]@11
  CPlayer *v15; // [sp+190h] [bp-38h]@14
  int v16; // [sp+1A0h] [bp-28h]@7
  int v17; // [sp+1A4h] [bp-24h]@15
  int v18; // [sp+1A8h] [bp-20h]@16
  unsigned __int64 v19; // [sp+1B0h] [bp-18h]@4
  CPlayer *pPlayer; // [sp+1D0h] [bp+8h]@1
  const char *Str; // [sp+1D8h] [bp+10h]@1

  Str = pwszChatData;
  pPlayer = this;
  v2 = &v9;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v19 = (unsigned __int64)&v9 ^ _security_cookie;
  if ( pPlayer->m_pUserDB )
  {
    if ( !pPlayer->m_pUserDB->m_bChatLock )
    {
      v16 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
      v4 = CPvpUserAndGuildRankingSystem::Instance();
      if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v4, v16, pPlayer->m_dwObjSerial)
        || pPlayer->m_Param.m_byClassInGuild == 2 && pPlayer->m_Param.m_pGuild )
      {
        _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
        Dst.byMessageType = 13;
        Dst.bySenderRace = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
        Dst.dwSenderSerial = pPlayer->m_dwObjSerial;
        v5 = CPlayerDB::GetCharNameW(&pPlayer->m_Param);
        strcpy_0(Dst.wszSenderName, v5);
        Dst.bySize = strlen_0(Str);
        memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
        Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
        Dst.byPvpGrade = -1;
        pbyType = 2;
        v12 = 11;
        v6 = CChatStealSystem::Instance();
        CChatStealSystem::StealChatMsg(v6, pPlayer, Dst.byMessageType, (char *)Str);
        v13 = _announ_message_receipt_udp::size(&Dst);
        for ( j = 0; j < 2532; ++j )
        {
          v15 = &g_Player + j;
          if ( v15->m_bLive )
          {
            v17 = CPlayerDB::GetRaceCode(&v15->m_Param);
            v7 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
            if ( v17 == v7 )
            {
              v18 = CPlayerDB::GetRaceCode(&pPlayer->m_Param);
              v8 = CPvpUserAndGuildRankingSystem::Instance();
              if ( CPvpUserAndGuildRankingSystem::IsCurrentRaceBossGroup(v8, v18, v15->m_dwObjSerial)
                || v15->m_Param.m_byClassInGuild == 2 && v15->m_Param.m_pGuild )
              {
                CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v13);
              }
            }
          }
        }
      }
    }
  }
}
