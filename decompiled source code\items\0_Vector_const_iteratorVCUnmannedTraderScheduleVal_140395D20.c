/*
 * Function: ??0?$_Vector_const_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA@PEAVCUnmannedTraderSchedule@@@Z
 * Address: 0x140395D20
 */

void __fastcall std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, CUnmannedTraderSchedule *_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_const_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v5; // [sp+30h] [bp+8h]@1
  CUnmannedTraderSchedule *v6; // [sp+38h] [bp+10h]@1

  v6 = _Ptr;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &>((std::_Ranit<CUnmannedTraderSchedule,__int64,CUnmannedTraderSchedule const *,CUnmannedTraderSchedule const &> *)&v5->_Mycont);
  v5->_Myptr = v6;
}
