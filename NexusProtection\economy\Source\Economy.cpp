#include "../Headers/Economy.h"
#include <iostream>
#include <chrono>

namespace NexusProtection::Economy {

    // Module state
    static bool s_moduleInitialized = false;
    static std::chrono::steady_clock::time_point s_moduleStartTime;

    bool InitializeEconomyModule() {
        if (s_moduleInitialized) {
            return true; // Already initialized
        }

        try {
            // Record start time
            s_moduleStartTime = std::chrono::steady_clock::now();

            // Initialize economy system
            g_EconomySystem.Initialize();
            
            // Read configuration
            if (!g_EconomySystem.ReadEconomyIniFile()) {
                std::cerr << "Failed to read economy configuration file" << std::endl;
                return false;
            }

            // Initialize money supply manager
            auto& moneySupplyMgr = CMoneySupplyMgr::Instance();
            moneySupplyMgr.Initialize();

            // Set global pointer for legacy compatibility
            g_pMoneySupplyMgr = &moneySupplyMgr;

            s_moduleInitialized = true;
            
            std::cout << "Economy Module initialized successfully" << std::endl;
            return true;
        }
        catch (const std::exception& e) {
            std::cerr << "Failed to initialize Economy Module: " << e.what() << std::endl;
            return false;
        }
    }

    void ShutdownEconomyModule() {
        if (!s_moduleInitialized) {
            return; // Not initialized
        }

        try {
            // Destroy money supply manager instance
            CMoneySupplyMgr::DestroyInstance();
            g_pMoneySupplyMgr = nullptr;

            s_moduleInitialized = false;
            
            std::cout << "Economy Module shut down successfully" << std::endl;
        }
        catch (const std::exception& e) {
            std::cerr << "Error during Economy Module shutdown: " << e.what() << std::endl;
        }
    }

    void UpdateEconomyModule(std::chrono::milliseconds deltaTime) {
        if (!s_moduleInitialized) {
            return;
        }

        try {
            // Update money supply manager
            auto& moneySupplyMgr = CMoneySupplyMgr::Instance();
            moneySupplyMgr.LoopMoneySupply();

            // Update economy system
            bool changeDay = false; // This would be determined by the main game loop
            g_EconomySystem.UpdateEconomySystem(&changeDay);
        }
        catch (const std::exception& e) {
            std::cerr << "Error during Economy Module update: " << e.what() << std::endl;
        }
    }

    const char* GetEconomyModuleVersion() {
        return ECONOMY_MODULE_VERSION;
    }

    const char* GetEconomyModuleBuildInfo() {
        return "NexusProtection Economy Module v" ECONOMY_MODULE_VERSION " - Built on " ECONOMY_MODULE_BUILD_DATE;
    }

} // namespace NexusProtection::Economy

// Legacy C interface implementation
extern "C" {
    int InitializeEconomy() {
        return NexusProtection::Economy::InitializeEconomyModule() ? 1 : 0;
    }

    void ShutdownEconomy() {
        NexusProtection::Economy::ShutdownEconomyModule();
    }

    void UpdateEconomy(unsigned int deltaTimeMs) {
        std::chrono::milliseconds deltaTime(deltaTimeMs);
        NexusProtection::Economy::UpdateEconomyModule(deltaTime);
    }

    const char* GetEconomyVersion() {
        return NexusProtection::Economy::GetEconomyModuleVersion();
    }
}
