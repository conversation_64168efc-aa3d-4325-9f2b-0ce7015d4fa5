/*
 * Function: ?RemoveAllContinousEffect@CCharacter@@QEAA_NXZ
 * Address: 0x1401787C0
 */

char __fastcall CCharacter::RemoveAllContinousEffect(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  unsigned int uiEffectCodeType; // [sp+24h] [bp-14h]@4
  CCharacter *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  for ( uiEffectCodeType = 0; uiEffectCodeType < 2; ++uiEffectCodeType )
  {
    if ( CCharacter::RemoveAllContinousEffectGroup(v7, uiEffectCodeType) )
      v5 = 1;
  }
  return v5;
}
