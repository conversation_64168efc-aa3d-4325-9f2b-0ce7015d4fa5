/*
 * Function: ??1CMonsterSkillPool@@QEAA@XZ
 * Address: 0x14014B600
 */

void __fastcall CMonsterSkillPool::~CMonsterSkillPool(CMonsterSkillPool *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CMonsterSkillPool *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `eh vector destructor iterator'(v4->m_MonSkill, 0x60ui64, 16, (void (__cdecl *)(void *))CMonsterSkill::~CMonsterSkill);
}
