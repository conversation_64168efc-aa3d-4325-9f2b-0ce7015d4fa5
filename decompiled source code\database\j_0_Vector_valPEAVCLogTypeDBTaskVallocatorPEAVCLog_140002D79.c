/*
 * Function: j_??0?$_Vector_val@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@IEAA@V?$allocator@PEAVCLogTypeDBTask@@@1@@Z
 * Address: 0x140002D79
 */

void __fastcall std::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(std::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this, std::allocator<CLogTypeDBTask *> _Al)
{
  std::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::_Vector_val<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(
    this,
    _Al);
}
