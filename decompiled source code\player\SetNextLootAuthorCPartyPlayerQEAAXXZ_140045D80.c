/*
 * Function: ?SetNextLootAuthor@CPartyPlayer@@QEAAXXZ
 * Address: 0x140045D80
 */

void __fastcall CPartyPlayer::SetNextLootAuthor(CPartyPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CPartyPlayer **v4; // [sp+20h] [bp-18h]@6
  int j; // [sp+28h] [bp-10h]@6
  CPartyPlayer *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v6) && v6->m_pPartyBoss->m_byLootShareSystem == 2 )
  {
    v4 = CPartyPlayer::GetPtrPartyMember(v6);
    for ( j = 0; j < 8; ++j )
    {
      if ( v4[j] == v6->m_pPartyBoss->m_pLootAuthor )
      {
        if ( j == 7 )
        {
          v6->m_pPartyBoss->m_pLootAuthor = *v4;
        }
        else if ( v4[j + 1] )
        {
          v6->m_pPartyBoss->m_pLootAuthor = v4[j + 1];
        }
        else
        {
          v6->m_pPartyBoss->m_pLootAuthor = *v4;
        }
        return;
      }
    }
  }
}
