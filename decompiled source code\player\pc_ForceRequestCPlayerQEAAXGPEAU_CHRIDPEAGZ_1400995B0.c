/*
 * Function: ?pc_ForceRequest@CPlayer@@QEAAXGPEAU_CHRID@@PEAG@Z
 * Address: 0x1400995B0
 */

void __usercall CPlayer::pc_ForceRequest(CPlayer *this@<rcx>, unsigned __int16 wForceSerial@<dx>, _CHRID *pidDst@<r8>, unsigned __int16 *pConsumeSerial@<r9>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v7; // rax@50
  int v8; // eax@80
  CGuildRoomSystem *v9; // rax@82
  int v10; // eax@83
  int v11; // eax@87
  signed int v12; // eax@92
  int v13; // eax@97
  _STORAGE_LIST *v14; // rax@101
  int v15; // eax@108
  int v16; // eax@109
  __int64 v17; // [sp+0h] [bp-1E8h]@1
  int *pnConsume; // [sp+20h] [bp-1C8h]@89
  bool *pbOverLap; // [sp+28h] [bp-1C0h]@89
  bool bPcbangPrimiumFavorReward; // [sp+30h] [bp-1B8h]@89
  char pbyErrorCode; // [sp+44h] [bp-1A4h]@4
  _STORAGE_LIST::_db_con *pForceItem; // [sp+58h] [bp-190h]@4
  _base_fld *pSFFld; // [sp+60h] [bp-188h]@4
  CCharacter *pDst; // [sp+68h] [bp-180h]@4
  unsigned __int16 pwDelPoint; // [sp+74h] [bp-174h]@4
  char v26; // [sp+76h] [bp-172h]@4
  int nForceLv; // [sp+84h] [bp-164h]@75
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+98h] [bp-150h]@73
  char v29; // [sp+A0h] [bp-148h]@73
  int v30; // [sp+C8h] [bp-120h]@73
  char v31; // [sp+CCh] [bp-11Ch]@73
  bool v32; // [sp+F4h] [bp-F4h]@73
  char v33; // [sp+F5h] [bp-F3h]@73
  bool v34; // [sp+104h] [bp-E4h]@77
  bool pbUpMty; // [sp+114h] [bp-D4h]@77
  char v36; // [sp+124h] [bp-C4h]@77
  unsigned int dwAlter; // [sp+128h] [bp-C0h]@83
  CPlayer *v38; // [sp+130h] [bp-B8h]@88
  unsigned int v39; // [sp+138h] [bp-B0h]@92
  int nAlter; // [sp+13Ch] [bp-ACh]@92
  int v41; // [sp+140h] [bp-A8h]@92
  float v42; // [sp+144h] [bp-A4h]@95
  unsigned int dwNewStat; // [sp+148h] [bp-A0h]@100
  int nParamCode; // [sp+14Ch] [bp-9Ch]@105
  int nValue; // [sp+150h] [bp-98h]@111
  CUserDB *v46; // [sp+160h] [bp-88h]@50
  int n; // [sp+168h] [bp-80h]@50
  CGuild *v48; // [sp+170h] [bp-78h]@50
  int iDstLevel; // [sp+178h] [bp-70h]@80
  CGameObjectVtbl *v50; // [sp+180h] [bp-68h]@80
  CUserDB *v51; // [sp+188h] [bp-60h]@82
  int v52; // [sp+190h] [bp-58h]@82
  CGuild *v53; // [sp+198h] [bp-50h]@82
  int v54; // [sp+1A0h] [bp-48h]@83
  CGameObjectVtbl *v55; // [sp+1A8h] [bp-40h]@83
  int v56; // [sp+1B0h] [bp-38h]@87
  CGameObjectVtbl *v57; // [sp+1B8h] [bp-30h]@87
  int v58; // [sp+1C0h] [bp-28h]@97
  CGameObjectVtbl *v59; // [sp+1C8h] [bp-20h]@97
  int v60; // [sp+1D0h] [bp-18h]@109
  unsigned __int64 v61; // [sp+1D8h] [bp-10h]@4
  CPlayer *v62; // [sp+1F0h] [bp+8h]@1
  unsigned __int16 v63; // [sp+1F8h] [bp+10h]@1
  _CHRID *pidDsta; // [sp+200h] [bp+18h]@1
  unsigned __int16 *pItemSerials; // [sp+208h] [bp+20h]@1

  pItemSerials = pConsumeSerial;
  pidDsta = pidDst;
  v63 = wForceSerial;
  v62 = this;
  v5 = &v17;
  for ( i = 120i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v61 = (unsigned __int64)&v17 ^ _security_cookie;
  pbyErrorCode = 0;
  pForceItem = 0i64;
  pSFFld = 0i64;
  pDst = 0i64;
  pwDelPoint = 0;
  memset(&v26, 0, 4ui64);
  if ( CPlayer::IsRidingUnit(v62) )
  {
    pbyErrorCode = 14;
    goto $RESULT_10;
  }
  if ( v62->m_byMoveType == 2 )
  {
    pbyErrorCode = 28;
    goto $RESULT_10;
  }
  if ( _effect_parameter::GetEff_State(&v62->m_EP, 20) )
  {
    pbyErrorCode = 24;
    goto $RESULT_10;
  }
  if ( _effect_parameter::GetEff_State(&v62->m_EP, 28) )
  {
    pbyErrorCode = 24;
    goto $RESULT_10;
  }
  if ( _effect_parameter::GetEff_State(&v62->m_EP, 21) )
  {
    pbyErrorCode = 25;
    goto $RESULT_10;
  }
  if ( v62->m_dwSelfDestructionTime )
  {
    pbyErrorCode = 26;
    goto $RESULT_10;
  }
  if ( v62->m_bInGuildBattle && v62->m_bTakeGravityStone )
  {
    pbyErrorCode = 27;
    goto $RESULT_10;
  }
  pForceItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v62->m_Param.m_dbForce.m_nListNum, v63);
  if ( !pForceItem )
  {
    pbyErrorCode = 1;
    goto $RESULT_10;
  }
  if ( pForceItem->m_bLock )
  {
    pbyErrorCode = 17;
    goto $RESULT_10;
  }
  pSFFld = CRecordData::GetRecord(
             &stru_1799C8410 + 1,
             *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + pForceItem->m_wItemIndex));
  if ( !pSFFld )
  {
    pbyErrorCode = 1;
    goto $RESULT_10;
  }
  if ( !v62->m_bSFDelayNotCheck
    && !_ATTACK_DELAY_CHECKER::IsDelay(&v62->m_AttDelayChker, 1, pSFFld->m_dwIndex, pSFFld[1].m_strCode[4]) )
  {
    pbyErrorCode = 9;
    goto $RESULT_10;
  }
  if ( *(_DWORD *)&pSFFld[12].m_strCode[12] >= 150 )
  {
    pbyErrorCode = 21;
    goto $RESULT_10;
  }
  pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, pidDsta->byID, pidDsta->wIndex);
  if ( !pDst )
  {
    pbyErrorCode = 2;
    goto $RESULT_10;
  }
  if ( !pDst->m_bLive || pDst->m_pCurMap != v62->m_pCurMap )
  {
    pbyErrorCode = 2;
    goto $RESULT_10;
  }
  if ( !CCharacter::IsEffectableDst((CCharacter *)&v62->vfptr, pSFFld[6].m_strCode, pDst) )
  {
    pbyErrorCode = 5;
    goto $RESULT_10;
  }
  if ( *(_DWORD *)&pSFFld[12].m_strCode[48] != -1 )
  {
    if ( _effect_parameter::GetEff_State(&v62->m_EP, 4) && !*(_DWORD *)&pSFFld[12].m_strCode[48] )
    {
      pbyErrorCode = 7;
      goto $RESULT_10;
    }
    if ( _effect_parameter::GetEff_State(&v62->m_EP, 3) && *(_DWORD *)&pSFFld[12].m_strCode[48] == 1 )
    {
      pbyErrorCode = 7;
      goto $RESULT_10;
    }
    if ( !*(_DWORD *)&pSFFld[12].m_strCode[48]
      && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v62->vfptr->IsAttackableInTown)(v62)
      && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst) )
    {
      if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))v62->vfptr->IsInTown)(v62)
        || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst)
        || v62->m_Param.m_pGuild
        && (v46 = v62->m_pUserDB,
            n = v62->m_ObjID.m_wIndex,
            v48 = v62->m_Param.m_pGuild,
            v7 = CGuildRoomSystem::GetInstance(),
            CGuildRoomSystem::IsGuildRoomMemberIn(v7, v48->m_dwSerial, n, v46->m_dwSerial)) )
      {
        pbyErrorCode = 18;
        goto $RESULT_10;
      }
    }
  }
  if ( *(_DWORD *)&pSFFld[12].m_strCode[12] == -1 && *(_DWORD *)&pSFFld[12].m_strCode[48] == -1 )
  {
    pbyErrorCode = 8;
    goto $RESULT_10;
  }
  if ( *(_DWORD *)&pSFFld[12].m_strCode[48] != -1
    && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst) )
  {
    pbyErrorCode = 13;
    goto $RESULT_10;
  }
  if ( !_effect_parameter::GetEff_State(&pDst->m_EP, 20) )
    goto LABEL_118;
  if ( *(_DWORD *)&pSFFld[12].m_strCode[12] == -1 )
  {
    pbyErrorCode = 24;
    goto $RESULT_10;
  }
  if ( IsUsableTempEffectAtStoneState(*(_DWORD *)&pSFFld[12].m_strCode[12]) )
  {
LABEL_118:
    if ( _effect_parameter::GetEff_State(&pDst->m_EP, 28) )
    {
      pbyErrorCode = 24;
    }
    else if ( CPlayer::IsSFUsableSFMastery(v62, 4, *(_DWORD *)&pSFFld[1].m_strCode[4]) )
    {
      if ( CPlayer::IsSFActableByClass(v62, 1, pSFFld) )
      {
        if ( CPlayer::IsSFUseableRace(v62, 1, pSFFld->m_dwIndex) )
        {
          if ( CPlayer::IsSFUsableGauge(v62, 1, pSFFld->m_dwIndex, &pwDelPoint) )
          {
            ppConsumeItems = 0i64;
            memset(&v29, 0, 0x10ui64);
            v30 = 0;
            memset(&v31, 0, 8ui64);
            v32 = 0;
            memset(&v33, 0, 2ui64);
            if ( CPlayer::GetUseConsumeItem(
                   v62,
                   (_consume_item_list *)&pSFFld[9].m_strCode[16],
                   pItemSerials,
                   &ppConsumeItems,
                   &v30,
                   &v32) )
            {
              nForceLv = GetSFLevel(*(_DWORD *)&pSFFld[4].m_strCode[60], pForceItem->m_dwDur);
            }
            else
            {
              pbyErrorCode = 32;
            }
          }
          else
          {
            pbyErrorCode = 6;
          }
        }
        else
        {
          pbyErrorCode = 4;
        }
      }
      else
      {
        pbyErrorCode = 16;
      }
    }
    else
    {
      pbyErrorCode = 3;
    }
  }
  else
  {
    pbyErrorCode = 24;
  }
$RESULT_10:
  if ( !pbyErrorCode )
  {
    v34 = CCharacter::GetStealth((CCharacter *)&v62->vfptr, 1);
    pbUpMty = 0;
    v36 = 0;
    if ( CCharacter::AssistForce(
           (CCharacter *)&v62->vfptr,
           pDst,
           (_force_fld *)pSFFld,
           nForceLv,
           &pbyErrorCode,
           &pbUpMty) )
    {
      v36 = 1;
      if ( pbUpMty && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v62->vfptr->IsInTown)(v62) )
      {
        iDstLevel = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetLevel)(pDst);
        v50 = v62->vfptr;
        v8 = ((int (__fastcall *)(CPlayer *))v50->GetLevel)(v62);
        if ( _CheckDestMonsterLimitLv(v8, iDstLevel, pDst->m_ObjID.m_byID) )
        {
          if ( !v62->m_Param.m_pGuild
            || (v51 = v62->m_pUserDB,
                v52 = v62->m_ObjID.m_wIndex,
                v53 = v62->m_Param.m_pGuild,
                v9 = CGuildRoomSystem::GetInstance(),
                !CGuildRoomSystem::IsGuildRoomMemberIn(v9, v53->m_dwSerial, v52, v51->m_dwSerial)) )
          {
            dwAlter = 1;
            v54 = ((int (__fastcall *)(CPlayer *))v62->vfptr->GetObjRace)(v62);
            v55 = pDst->vfptr;
            v10 = ((int (__fastcall *)(CCharacter *))v55->GetObjRace)(pDst);
            if ( v54 != v10 || !CPlayer::IsChaosMode(v62) || *(_DWORD *)&pSFFld[12].m_strCode[48] )
            {
              if ( pDst->m_ObjID.m_byID
                || (v56 = ((int (__fastcall *)(CPlayer *))v62->vfptr->GetObjRace)(v62),
                    v57 = pDst->vfptr,
                    v11 = ((int (__fastcall *)(CCharacter *))v57->GetObjRace)(pDst),
                    v56 == v11) )
              {
                bPcbangPrimiumFavorReward = 1;
                pbOverLap = (bool *)"CPlayer::pc_ForceRequest()---0";
                LOBYTE(pnConsume) = 0;
                CPlayer::Emb_AlterStat(v62, 4, pSFFld[1].m_strCode[4], dwAlter, 0, "CPlayer::pc_ForceRequest()---0", 1);
              }
              else
              {
                v38 = (CPlayer *)pDst;
                if ( !CPlayer::IsPunished((CPlayer *)pDst, 1, 0) )
                {
                  bPcbangPrimiumFavorReward = 1;
                  pbOverLap = (bool *)"CPlayer::pc_ForceRequest()---0";
                  LOBYTE(pnConsume) = 0;
                  CPlayer::Emb_AlterStat(
                    v62,
                    4,
                    pSFFld[1].m_strCode[4],
                    dwAlter,
                    0,
                    "CPlayer::pc_ForceRequest()---0",
                    1);
                }
              }
            }
            v39 = pForceItem->m_dwDur;
            nAlter = dwAlter;
            v12 = ((int (__fastcall *)(CPlayer *))v62->vfptr->GetLevel)(v62);
            nAlter *= v12 / 10 + 1;
            v41 = nAlter;
            if ( CPlayer::IsApplyPcbangPrimium(v62) )
            {
              a5 = (float)nAlter + (float)((float)v41 * (float)(PCBANG_PRIMIUM_FAVOR::SKILL_FORCE_MASTERY - 1.0));
              nAlter = (signed int)ffloor(a5);
            }
            else
            {
              a5 = (float)nAlter + (float)((float)v41 * (float)(FORCE_LIVER_ACCUM_RATE - 1.0));
              nAlter = (signed int)ffloor(a5);
            }
            _effect_parameter::GetEff_Have(&v62->m_EP, 6);
            v42 = a5;
            if ( a5 > 1.0 )
            {
              a5 = (float)nAlter + (float)((float)v41 * (float)(v42 - 1.0));
              nAlter = (signed int)ffloor(a5);
            }
            v58 = ((int (__fastcall *)(CPlayer *))v62->vfptr->GetObjRace)(v62);
            v59 = pDst->vfptr;
            v13 = ((int (__fastcall *)(CCharacter *))v59->GetObjRace)(pDst);
            if ( v58 != v13 || !CPlayer::IsChaosMode(v62) || *(_DWORD *)&pSFFld[12].m_strCode[48] )
            {
              dwNewStat = v39;
              if ( nAlter > 0 )
              {
                v14 = pForceItem->m_pInList;
                LOBYTE(pbOverLap) = 0;
                LOBYTE(pnConsume) = 0;
                dwNewStat = CPlayer::Emb_AlterDurPoint(
                              v62,
                              v14->m_nListCode,
                              pForceItem->m_byStorageIndex,
                              nAlter,
                              0,
                              0);
              }
              if ( dwNewStat != v39 )
                CPlayer::SendMsg_FcitemInform(v62, v63, dwNewStat);
            }
          }
        }
      }
    }
    if ( v36 )
    {
      for ( nParamCode = 0; nParamCode < 3; ++nParamCode )
      {
        if ( (signed int)*(&pwDelPoint + nParamCode) > 0 )
        {
          v15 = CPlayer::GetGauge(v62, nParamCode);
          if ( v15 - *(&pwDelPoint + nParamCode) <= 0 )
          {
            v60 = 0;
          }
          else
          {
            v16 = CPlayer::GetGauge(v62, nParamCode);
            v60 = v16 - *(&pwDelPoint + nParamCode);
          }
          nValue = v60;
          CPlayer::SetGauge(v62, nParamCode, v60, 1);
        }
      }
      CPlayer::SendMsg_Recover(v62);
      CPlayer::DeleteUseConsumeItem(v62, &ppConsumeItems, &v30, &v32);
      _effect_parameter::GetEff_Plus(&v62->m_EP, 13);
      _ATTACK_DELAY_CHECKER::SetDelay(
        &v62->m_AttDelayChker,
        (signed int)ffloor(*(float *)&pSFFld[9].m_strCode[52] + a5));
      if ( v34 )
        CCharacter::BreakStealth((CCharacter *)&v62->vfptr);
    }
  }
  CPlayer::SendMsg_ForceResult(v62, pbyErrorCode, pidDsta, pForceItem, nForceLv);
}
