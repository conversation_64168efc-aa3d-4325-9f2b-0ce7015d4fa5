/*
 * Function: ?CalcDPRate@CPlayer@@QEAAMXZ
 * Address: 0x14005FB70
 */

float __fastcall CPlayer::CalcDPRate(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  __int64 v5; // [sp+0h] [bp-48h]@1
  float v6; // [sp+30h] [bp-18h]@4
  float v7; // [sp+34h] [bp-14h]@4
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = 2.0 * (float)CPlayer::GetDP(v8);
  v3 = CPlayer::GetMaxDP(v8);
  v6 = (float)(v7 / (float)v3) - 0.40000001;
  if ( v6 >= 0.0 )
  {
    if ( v6 > 1.0 )
      v6 = FLOAT_1_0;
  }
  else
  {
    v6 = 0.0;
  }
  return v6;
}
