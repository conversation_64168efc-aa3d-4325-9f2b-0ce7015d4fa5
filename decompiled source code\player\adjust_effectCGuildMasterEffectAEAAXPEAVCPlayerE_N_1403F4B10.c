/*
 * Function: ?adjust_effect@CGuildMasterEffect@@AEAAXPEAVCPlayer@@E_N@Z
 * Address: 0x1403F4B10
 */

void __fastcall CGuildMasterEffect::adjust_effect(CGuildMasterEffect *this, CPlayer *pP, char byGrade, bool bAdd)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  unsigned __int8 v7; // [sp+20h] [bp-18h]@8
  CGuildMasterEffect *v8; // [sp+40h] [bp+8h]@1
  CPlayer *v9; // [sp+48h] [bp+10h]@1
  bool v10; // [sp+58h] [bp+20h]@1

  v10 = bAdd;
  v9 = pP;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pP && byGrade && (unsigned __int8)byGrade >= (signed int)v8->m_byAdjustableGrade )
  {
    v7 = byGrade - 1;
    CPlayer::apply_normal_item_std_effect(pP, 6, v8->m_EffectData[(unsigned __int8)(byGrade - 1)].attack_value, bAdd);
    CPlayer::apply_normal_item_std_effect(v9, 7, v8->m_EffectData[v7].defence_value, v10);
  }
}
