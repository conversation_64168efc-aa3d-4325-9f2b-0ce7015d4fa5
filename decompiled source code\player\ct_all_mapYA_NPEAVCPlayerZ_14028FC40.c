/*
 * Function: ?ct_all_map@@YA_NPEAVCPlayer@@@Z
 * Address: 0x14028FC40
 */

char __fastcall ct_all_map(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-48h]@1
  int n; // [sp+30h] [bp-18h]@6
  _base_fld *v7; // [sp+38h] [bp-10h]@8
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = pOne;
  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v8 )
  {
    for ( n = 0; ; ++n )
    {
      v4 = CRecordData::GetRecordNum((CRecordData *)&unk_1799C6AA0 + 21);
      if ( n >= v4 )
        break;
      v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 21, n);
      CPlayer::dev_loot_item(v8, v7->m_strCode, 1, 0i64, 0);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
