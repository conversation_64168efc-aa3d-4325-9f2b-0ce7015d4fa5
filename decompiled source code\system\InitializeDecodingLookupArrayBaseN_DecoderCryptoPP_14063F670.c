/*
 * Function: ?InitializeDecodingLookupArray@BaseN_Decoder@CryptoPP@@SAXPEAHPEBEI_N@Z
 * Address: 0x14063F670
 */

void __fastcall CryptoPP::BaseN_Decoder::InitializeDecodingLookupArray(int *a1, const unsigned __int8 *a2, unsigned int a3, char a4)
{
  int i; // [sp+20h] [bp-18h]@1
  int _Val; // [sp+24h] [bp-14h]@1
  int *_First; // [sp+40h] [bp+8h]@1
  const unsigned __int8 *v7; // [sp+48h] [bp+10h]@1
  unsigned int v8; // [sp+50h] [bp+18h]@1
  char v9; // [sp+58h] [bp+20h]@1

  v9 = a4;
  v8 = a3;
  v7 = a2;
  _First = a1;
  _Val = -1;
  std::fill<int *,int>(a1, a1 + 256, &_Val);
  for ( i = 0; i < v8; ++i )
  {
    if ( v9 && isalpha(v7[i]) )
    {
      if ( _First[toupper(v7[i])] != -1 )
        _wassert(
          L"lookup[toupper(alphabet[i])] == -1",
          L"D:\\RF Project\\RF_Server64\\28 Crypto++\\basecode.cpp",
          0xB1u);
      _First[toupper(v7[i])] = i;
      if ( _First[tolower(v7[i])] != -1 )
        _wassert(
          L"lookup[tolower(alphabet[i])] == -1",
          L"D:\\RF Project\\RF_Server64\\28 Crypto++\\basecode.cpp",
          0xB3u);
      _First[tolower(v7[i])] = i;
    }
    else
    {
      if ( _First[v7[i]] != -1 )
        _wassert(L"lookup[alphabet[i]] == -1", L"D:\\RF Project\\RF_Server64\\28 Crypto++\\basecode.cpp", 0xB8u);
      _First[v7[i]] = i;
    }
  }
}
