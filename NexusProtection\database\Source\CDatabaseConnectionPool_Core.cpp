/**
 * @file CDatabaseConnectionPool_Core.cpp
 * @brief Modern C++20 Database Connection Pool core implementation
 * 
 * This file provides the core implementation of the CDatabaseConnectionPool class
 * with thread safety, automatic reconnection, and performance optimization.
 */

#include "../Headers/CDatabaseConnectionPool.h"
#include "../Headers/CRFNewDatabase.h"
#include <iostream>
#include <stdexcept>
#include <algorithm>
#include <thread>
#include <sstream>

namespace NexusProtection {
namespace Database {

// DatabaseConnection implementation
DatabaseConnection::DatabaseConnection(uint32_t id, const ConnectionPoolConfig& config)
    : m_id(id)
    , m_config(config)
    , m_creationTime(std::chrono::steady_clock::now())
    , m_lastUsed(std::chrono::steady_clock::now())
    , m_lastHealthCheck(std::chrono::steady_clock::now())
{
    std::cout << "[DEBUG] DatabaseConnection " << m_id << " created" << std::endl;
}

DatabaseConnection::~DatabaseConnection() {
    std::cout << "[DEBUG] DatabaseConnection " << m_id << " destroyed" << std::endl;
    Disconnect();
}

bool DatabaseConnection::Connect() {
    try {
        std::cout << "[DEBUG] Connecting DatabaseConnection " << m_id << std::endl;
        
        m_state = ConnectionState::Connecting;
        
        // Create database instance
        m_database = std::make_shared<CRFNewDatabase>();
        
        // Initialize database connection
        if (!m_database->StartDataBase(m_config.odbcName, m_config.accountName, m_config.password)) {
            SetLastError("Failed to start database connection");
            m_state = ConnectionState::Error;
            return false;
        }
        
        m_state = ConnectionState::Connected;
        m_lastUsed = std::chrono::steady_clock::now();
        
        std::cout << "[DEBUG] DatabaseConnection " << m_id << " connected successfully" << std::endl;
        return true;
        
    } catch (const std::exception& e) {
        SetLastError(std::string("Exception during connection: ") + e.what());
        m_state = ConnectionState::Error;
        std::cerr << "[ERROR] Exception in DatabaseConnection::Connect: " << e.what() << std::endl;
        return false;
    }
}

void DatabaseConnection::Disconnect() {
    try {
        if (m_database) {
            m_database->EndDataBase();
            m_database.reset();
        }
        
        m_state = ConnectionState::Disconnected;
        std::cout << "[DEBUG] DatabaseConnection " << m_id << " disconnected" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in DatabaseConnection::Disconnect: " << e.what() << std::endl;
    }
}

bool DatabaseConnection::Reconnect() {
    try {
        std::cout << "[DEBUG] Reconnecting DatabaseConnection " << m_id << std::endl;
        
        m_state = ConnectionState::Reconnecting;
        m_reconnectCount++;
        
        // Disconnect first
        Disconnect();
        
        // Wait a moment before reconnecting
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // Attempt to reconnect
        bool success = Connect();
        
        if (success) {
            std::cout << "[DEBUG] DatabaseConnection " << m_id << " reconnected successfully" << std::endl;
        } else {
            std::cerr << "[ERROR] DatabaseConnection " << m_id << " reconnection failed" << std::endl;
        }
        
        return success;
        
    } catch (const std::exception& e) {
        SetLastError(std::string("Exception during reconnection: ") + e.what());
        m_state = ConnectionState::Error;
        std::cerr << "[ERROR] Exception in DatabaseConnection::Reconnect: " << e.what() << std::endl;
        return false;
    }
}

bool DatabaseConnection::IsConnected() const {
    return m_state == ConnectionState::Connected || m_state == ConnectionState::InUse;
}

bool DatabaseConnection::IsHealthy() const {
    if (!IsConnected() || !m_database) {
        return false;
    }
    
    // Check if database connection is still valid
    return m_database->IsConnected();
}

bool DatabaseConnection::ValidateConnection() {
    try {
        if (!m_database) {
            return false;
        }
        
        // Perform a simple query to validate connection
        // This would be implemented based on the specific database interface
        // For now, just check if the database object is valid
        bool isValid = m_database->IsConnected();
        
        m_lastHealthCheck = std::chrono::steady_clock::now();
        
        if (!isValid) {
            SetLastError("Connection validation failed");
            m_state = ConnectionState::Error;
        }
        
        return isValid;
        
    } catch (const std::exception& e) {
        SetLastError(std::string("Exception during validation: ") + e.what());
        m_state = ConnectionState::Error;
        return false;
    }
}

bool DatabaseConnection::IsExpired() const {
    auto age = GetAge();
    return age >= m_config.maxLifetime;
}

bool DatabaseConnection::IsIdle() const {
    auto idleTime = GetIdleTime();
    return idleTime >= m_config.idleTimeout;
}

std::chrono::seconds DatabaseConnection::GetAge() const {
    return std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - m_creationTime);
}

std::chrono::seconds DatabaseConnection::GetIdleTime() const {
    return std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now() - m_lastUsed);
}

void DatabaseConnection::SetLastError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_errorMutex);
    m_lastError = error;
    std::cerr << "[ERROR] DatabaseConnection " << m_id << ": " << error << std::endl;
}

// ConnectionGuard implementation
ConnectionGuard::ConnectionGuard(std::shared_ptr<DatabaseConnection> connection, 
                               std::function<void(std::shared_ptr<DatabaseConnection>)> releaseFunc)
    : m_connection(std::move(connection))
    , m_releaseFunc(std::move(releaseFunc))
{
}

ConnectionGuard::~ConnectionGuard() {
    Release();
}

ConnectionGuard::ConnectionGuard(ConnectionGuard&& other) noexcept
    : m_connection(std::move(other.m_connection))
    , m_releaseFunc(std::move(other.m_releaseFunc))
    , m_released(other.m_released)
{
    other.m_released = true;
}

ConnectionGuard& ConnectionGuard::operator=(ConnectionGuard&& other) noexcept {
    if (this != &other) {
        Release();
        m_connection = std::move(other.m_connection);
        m_releaseFunc = std::move(other.m_releaseFunc);
        m_released = other.m_released;
        other.m_released = true;
    }
    return *this;
}

void ConnectionGuard::Release() {
    if (!m_released && m_connection && m_releaseFunc) {
        m_releaseFunc(m_connection);
        m_released = true;
    }
}

// CDatabaseConnectionPool implementation
CDatabaseConnectionPool::CDatabaseConnectionPool(const ConnectionPoolConfig& config)
    : m_config(config)
{
    std::cout << "[INFO] CDatabaseConnectionPool constructor called" << std::endl;
    
    if (!ValidateConfig(config)) {
        throw std::invalid_argument("Invalid connection pool configuration");
    }
}

CDatabaseConnectionPool::~CDatabaseConnectionPool() {
    std::cout << "[INFO] CDatabaseConnectionPool destructor called" << std::endl;
    Shutdown();
}

bool CDatabaseConnectionPool::Initialize() {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    try {
        std::cout << "[INFO] Initializing CDatabaseConnectionPool" << std::endl;
        
        if (m_isInitialized) {
            std::cout << "[WARNING] Connection pool already initialized" << std::endl;
            return true;
        }
        
        // Create initial connections
        for (uint32_t i = 0; i < m_config.initialConnections; ++i) {
            auto connection = CreateConnection();
            if (connection && connection->Connect()) {
                m_allConnections.push_back(connection);
                m_availableConnections.push(connection);
                m_connectionMap[connection->GetId()] = connection;
                m_stats.totalConnections++;
                m_stats.idleConnections++;
            } else {
                std::cerr << "[ERROR] Failed to create initial connection " << i << std::endl;
                return false;
            }
        }
        
        // Start maintenance thread
        m_shutdownRequested = false;
        m_maintenanceThread = std::make_unique<std::thread>(&CDatabaseConnectionPool::MaintenanceThreadFunc, this);
        
        m_isInitialized = true;
        
        std::cout << "[INFO] CDatabaseConnectionPool initialized with " 
                 << m_allConnections.size() << " connections" << std::endl;
        
        return true;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CDatabaseConnectionPool::Initialize: " << e.what() << std::endl;
        return false;
    }
}

void CDatabaseConnectionPool::Shutdown() {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    try {
        std::cout << "[INFO] Shutting down CDatabaseConnectionPool" << std::endl;
        
        if (!m_isInitialized) {
            return;
        }
        
        // Signal shutdown to maintenance thread
        m_shutdownRequested = true;
        
        // Wait for maintenance thread to finish
        if (m_maintenanceThread && m_maintenanceThread->joinable()) {
            m_maintenanceThread->join();
        }
        
        // Close all connections
        for (auto& connection : m_allConnections) {
            connection->Disconnect();
        }
        
        // Clear all containers
        m_allConnections.clear();
        while (!m_availableConnections.empty()) {
            m_availableConnections.pop();
        }
        m_connectionMap.clear();
        
        // Reset statistics
        m_stats.totalConnections = 0;
        m_stats.activeConnections = 0;
        m_stats.idleConnections = 0;
        m_stats.errorConnections = 0;
        
        m_isInitialized = false;
        
        std::cout << "[INFO] CDatabaseConnectionPool shutdown completed" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CDatabaseConnectionPool::Shutdown: " << e.what() << std::endl;
    }
}

std::unique_ptr<ConnectionGuard> CDatabaseConnectionPool::AcquireConnection(std::chrono::seconds timeout) {
    std::unique_lock<std::mutex> lock(m_poolMutex);
    
    try {
        m_stats.totalAcquires++;
        
        // Wait for available connection with timeout
        auto deadline = std::chrono::steady_clock::now() + timeout;
        
        while (m_availableConnections.empty() && std::chrono::steady_clock::now() < deadline) {
            // Try to create new connection if under limit
            if (m_allConnections.size() < m_config.maxConnections) {
                auto connection = CreateConnection();
                if (connection && connection->Connect()) {
                    m_allConnections.push_back(connection);
                    m_availableConnections.push(connection);
                    m_connectionMap[connection->GetId()] = connection;
                    m_stats.totalConnections++;
                    break;
                }
            }
            
            // Wait for connection to become available
            auto waitTime = std::min(std::chrono::seconds(1), 
                                   std::chrono::duration_cast<std::chrono::seconds>(deadline - std::chrono::steady_clock::now()));
            
            if (waitTime.count() <= 0) {
                break;
            }
            
            m_connectionAvailable.wait_for(lock, waitTime);
        }
        
        if (m_availableConnections.empty()) {
            m_stats.timeoutAcquires++;
            m_stats.failedAcquires++;
            std::cerr << "[ERROR] Connection acquisition timeout" << std::endl;
            return nullptr;
        }
        
        // Get available connection
        auto connection = m_availableConnections.front();
        m_availableConnections.pop();
        
        // Validate connection health
        if (!connection->IsHealthy()) {
            // Try to reconnect
            if (!connection->Reconnect()) {
                // Connection failed, try to get another one
                m_stats.failedAcquires++;
                return AcquireConnection(timeout); // Recursive call with remaining timeout
            }
        }
        
        // Mark connection as in use
        connection->MarkInUse();
        m_stats.activeConnections++;
        m_stats.idleConnections--;
        m_stats.successfulAcquires++;
        m_stats.lastActivity = std::chrono::steady_clock::now();
        
        // Create connection guard with release function
        auto releaseFunc = [this](std::shared_ptr<DatabaseConnection> conn) {
            this->ReleaseConnection(conn);
        };
        
        std::cout << "[DEBUG] Connection " << connection->GetId() << " acquired" << std::endl;
        
        return std::make_unique<ConnectionGuard>(connection, releaseFunc);
        
    } catch (const std::exception& e) {
        m_stats.failedAcquires++;
        std::cerr << "[ERROR] Exception in CDatabaseConnectionPool::AcquireConnection: " << e.what() << std::endl;
        return nullptr;
    }
}

void CDatabaseConnectionPool::ReleaseConnection(std::shared_ptr<DatabaseConnection> connection) {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    try {
        if (!connection) {
            return;
        }
        
        std::cout << "[DEBUG] Releasing connection " << connection->GetId() << std::endl;
        
        // Mark connection as available
        connection->MarkAvailable();
        
        // Add back to available pool
        m_availableConnections.push(connection);
        
        // Update statistics
        m_stats.activeConnections--;
        m_stats.idleConnections++;
        m_stats.totalReleases++;
        m_stats.lastActivity = std::chrono::steady_clock::now();
        
        // Notify waiting threads
        m_connectionAvailable.notify_one();
        
        std::cout << "[DEBUG] Connection " << connection->GetId() << " released" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CDatabaseConnectionPool::ReleaseConnection: " << e.what() << std::endl;
    }
}

std::shared_ptr<DatabaseConnection> CDatabaseConnectionPool::CreateConnection() {
    try {
        uint32_t id = m_nextConnectionId++;
        auto connection = std::make_shared<DatabaseConnection>(id, m_config);
        
        std::cout << "[DEBUG] Created new DatabaseConnection " << id << std::endl;
        return connection;
        
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in CreateConnection: " << e.what() << std::endl;
        return nullptr;
    }
}

bool CDatabaseConnectionPool::ValidateConfig(const ConnectionPoolConfig& config) const {
    return config.IsValid();
}

void CDatabaseConnectionPool::MaintenanceThreadFunc() {
    std::cout << "[DEBUG] Connection pool maintenance thread started" << std::endl;
    
    while (!m_shutdownRequested) {
        try {
            // Perform maintenance tasks
            PerformHealthChecks();
            CleanupConnections();
            EnsureMinimumConnections();
            UpdateStatistics();
            
            // Sleep for health check interval
            std::this_thread::sleep_for(m_config.healthCheckInterval);
            
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Exception in maintenance thread: " << e.what() << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }
    
    std::cout << "[DEBUG] Connection pool maintenance thread stopped" << std::endl;
}

void CDatabaseConnectionPool::PerformHealthChecks() {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    // Check health of all connections
    for (auto& connection : m_allConnections) {
        if (connection->GetState() == ConnectionState::Connected) {
            if (!connection->ValidateConnection()) {
                m_stats.healthCheckFailures++;
                std::cout << "[WARNING] Health check failed for connection " << connection->GetId() << std::endl;
            }
        }
    }
}

void CDatabaseConnectionPool::CleanupConnections() {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    // Remove expired and idle connections
    auto it = m_allConnections.begin();
    while (it != m_allConnections.end()) {
        auto& connection = *it;
        
        if (connection->GetState() == ConnectionState::Connected && 
            (connection->IsExpired() || connection->IsIdle())) {
            
            // Don't remove if we're at minimum connections
            if (m_allConnections.size() <= m_config.minConnections) {
                ++it;
                continue;
            }
            
            std::cout << "[DEBUG] Removing expired/idle connection " << connection->GetId() << std::endl;
            
            // Remove from available queue
            std::queue<std::shared_ptr<DatabaseConnection>> tempQueue;
            while (!m_availableConnections.empty()) {
                auto availableConn = m_availableConnections.front();
                m_availableConnections.pop();
                if (availableConn != connection) {
                    tempQueue.push(availableConn);
                }
            }
            m_availableConnections = std::move(tempQueue);
            
            // Remove from connection map
            m_connectionMap.erase(connection->GetId());
            
            // Disconnect and remove
            connection->Disconnect();
            it = m_allConnections.erase(it);
            
            m_stats.totalConnections--;
            m_stats.idleConnections--;
        } else {
            ++it;
        }
    }
}

void CDatabaseConnectionPool::EnsureMinimumConnections() {
    std::lock_guard<std::mutex> lock(m_poolMutex);
    
    // Create connections if below minimum
    while (m_allConnections.size() < m_config.minConnections) {
        auto connection = CreateConnection();
        if (connection && connection->Connect()) {
            m_allConnections.push_back(connection);
            m_availableConnections.push(connection);
            m_connectionMap[connection->GetId()] = connection;
            m_stats.totalConnections++;
            m_stats.idleConnections++;
            
            std::cout << "[DEBUG] Created connection to maintain minimum pool size" << std::endl;
        } else {
            std::cerr << "[ERROR] Failed to create connection for minimum pool size" << std::endl;
            break;
        }
    }
}

void CDatabaseConnectionPool::UpdateStatistics() {
    // Statistics are updated in real-time, this is for any periodic calculations
    m_stats.lastActivity = std::chrono::steady_clock::now();
}

// Factory implementation
std::unique_ptr<CDatabaseConnectionPool> CDatabaseConnectionPoolFactory::CreateConnectionPool(
    const ConnectionPoolConfig& config) {
    return std::make_unique<CDatabaseConnectionPool>(config);
}

std::unique_ptr<CDatabaseConnectionPool> CDatabaseConnectionPoolFactory::CreateDefaultConnectionPool(
    const std::string& odbcName, const std::string& serverIP,
    const std::string& accountName, const std::string& password) {
    
    ConnectionPoolConfig config;
    config.odbcName = odbcName;
    config.serverIP = serverIP;
    config.accountName = accountName;
    config.password = password;
    
    return CreateConnectionPool(config);
}

} // namespace Database
} // namespace NexusProtection
