/*
 * Function: ?have_item_close@CMgrAvatorItemHistory@@QEAAXHPEADPEAU_AVATOR_DATA@@10KEKKPEBVCUnmannedTraderRegistItemInfo@@E0@Z
 * Address: 0x140237500
 */

void __fastcall CMgrAvatorItemHistory::have_item_close(CMgrAvatorItemHistory *this, int n, char *pszName, _AVATOR_DATA *pLoadData, _AVATOR_DATA *pBackupData, char *pszID, unsigned int dwIDSerial, char byDgr, unsigned int dwIP, unsigned int dwExpRate, CUnmannedTraderRegistItemInfo *pkInfo, char byMaxCnt, char *pszFileName)
{
  __int64 *v13; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v15; // eax@4
  char *v16; // rax@8
  char *v17; // rax@19
  char v18; // al@24
  unsigned int v19; // eax@24
  unsigned int v20; // eax@30
  int v21; // ecx@37
  int v22; // edx@37
  int v23; // er8@37
  int v24; // er9@37
  int v25; // er10@37
  unsigned int v26; // er11@37
  unsigned int v27; // eax@45
  char *v28; // rax@49
  __int64 v29; // [sp+0h] [bp-118h]@1
  long double v30; // [sp+20h] [bp-F8h]@4
  __int64 v31; // [sp+28h] [bp-F0h]@4
  char *v32; // [sp+30h] [bp-E8h]@4
  long double v33; // [sp+38h] [bp-E0h]@4
  long double v34; // [sp+40h] [bp-D8h]@4
  unsigned int v35; // [sp+48h] [bp-D0h]@4
  _base_fld *v36; // [sp+50h] [bp-C8h]@4
  _base_fld *v37; // [sp+58h] [bp-C0h]@4
  _base_fld *v38; // [sp+60h] [bp-B8h]@4
  int nTableCode; // [sp+68h] [bp-B0h]@4
  _EQUIPKEY *v40; // [sp+70h] [bp-A8h]@7
  _base_fld *v41; // [sp+78h] [bp-A0h]@8
  _EMBELLKEY *v42; // [sp+80h] [bp-98h]@12
  _base_fld *v43; // [sp+88h] [bp-90h]@13
  _INVENKEY *v44; // [sp+90h] [bp-88h]@17
  _base_fld *v45; // [sp+98h] [bp-80h]@18
  _FORCEKEY *v46; // [sp+A0h] [bp-78h]@23
  _base_fld *v47; // [sp+A8h] [bp-70h]@24
  _INVENKEY *v48; // [sp+B0h] [bp-68h]@28
  _base_fld *v49; // [sp+B8h] [bp-60h]@29
  _UNIT_DB_BASE *v50; // [sp+C0h] [bp-58h]@36
  _ANIMUSKEY *v51; // [sp+C8h] [bp-50h]@43
  _base_fld *v52; // [sp+D0h] [bp-48h]@44
  _INVENKEY *v53; // [sp+D8h] [bp-40h]@48
  _base_fld *v54; // [sp+E0h] [bp-38h]@49
  __int64 v55; // [sp+E8h] [bp-30h]@8
  __int64 v56; // [sp+F0h] [bp-28h]@49
  char **v57; // [sp+F8h] [bp-20h]@49
  int v58; // [sp+100h] [bp-18h]@49
  CMgrAvatorItemHistory *v59; // [sp+120h] [bp+8h]@1
  int na; // [sp+128h] [bp+10h]@1
  _AVATOR_DATA *v61; // [sp+138h] [bp+20h]@1

  v61 = pLoadData;
  na = n;
  v59 = this;
  v13 = &v29;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v13 = -858993460;
    v13 = (__int64 *)((char *)v13 + 4);
  }
  sData[0] = 0;
  v36 = CRecordData::GetRecord(&stru_1799C6420, pLoadData->dbAvator.m_zClassHistory[0]);
  v37 = CRecordData::GetRecord(&stru_1799C6420, v61->dbAvator.m_zClassHistory[1]);
  v38 = CRecordData::GetRecord(&stru_1799C6420, v61->dbAvator.m_zClassHistory[2]);
  strcat_0(sData, "\r\n\t============\r\n\r\n");
  CMgrAvatorItemHistory::have_auto_item(v59, na, pkInfo, byMaxCnt);
  v15 = v61->dbAvator.m_byLevel;
  v35 = v61->dbAvator.m_dwTotalPlayMin;
  v34 = v61->dbAvator.m_dPvPCashBag;
  v33 = v61->dbAvator.m_dPvPPoint;
  LODWORD(v32) = v61->dbAvator.m_dwGold;
  LODWORD(v31) = v61->dbAvator.m_dwDalant;
  LODWORD(v30) = dwExpRate;
  sprintf(
    sBuf,
    "LV: %d\r\nXP: %.0f (%d)\r\n$D: %u\r\n$G: %u\r\nPvP: %.0f\r\nCB: %.0f\r\nTIME: %d\r\n\r\n",
    v15,
    v61->dbAvator.m_dExp);
  strcat_0(sData, sBuf);
  sprintf(sBuf, "EQUIP\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 8; ++nTableCode )
  {
    v40 = &v61->dbAvator.m_EquipKey[nTableCode];
    if ( _EQUIPKEY::IsFilled(v40) )
    {
      v41 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, v40->zItemIndex);
      v55 = nTableCode;
      v16 = DisplayItemUpgInfo(nTableCode, v61->dbAvator.m_dwFixEquipLv[nTableCode]);
      v30 = *(double *)&v61->dbAvator.m_lnUID[v55];
      sprintf(sBuf, "\t%s_@%s[%I64u]\r\n", v41->m_strCode, v16);
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "EMBELL\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 7; ++nTableCode )
  {
    v42 = (_EMBELLKEY *)((char *)&v61->dbEquip + 27 * nTableCode);
    if ( _EMBELLKEY::IsFilled(v42) )
    {
      v43 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v42->byTableCode, v42->wItemIndex);
      sprintf(sBuf, "\t%s[%I64u]\r\n", v43->m_strCode, *(_QWORD *)&v42[2].wItemIndex);
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "INVEN\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 20 * v61->dbAvator.m_byBagNum; ++nTableCode )
  {
    v44 = (_INVENKEY *)((char *)&v61->dbInven + 37 * nTableCode);
    if ( _INVENKEY::IsFilled(v44) )
    {
      v45 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v44->byTableCode, v44->wItemIndex);
      if ( v45 )
      {
        v17 = DisplayItemUpgInfo(v44->byTableCode, *(_DWORD *)&v44[3]);
        v31 = *(_QWORD *)&v44[5].bySlotIndex;
        v30 = *(double *)&v17;
        sprintf(sBuf, "\t%s_%u_@%s[%I64u]\r\n", v45->m_strCode, *(_QWORD *)&v44[1].bySlotIndex);
        strcat_0(sData, sBuf);
      }
    }
  }
  sprintf(sBuf, "FORCE\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 88; ++nTableCode )
  {
    v46 = (_FORCEKEY *)((char *)&v61->dbForce + 25 * nTableCode);
    if ( _FORCEKEY::IsFilled(v46) )
    {
      v18 = _FORCEKEY::GetIndex(v46);
      v47 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 15, (unsigned __int8)v18);
      v19 = _FORCEKEY::GetStat(v46);
      v30 = *(double *)&v46[2].dwKey;
      sprintf(sBuf, "\t%s_%u[%I64u]\r\n", v47->m_strCode, v19);
      strcat_0(sData, sBuf);
    }
  }
  sprintf(sBuf, "RES\r\n");
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < 20; ++nTableCode )
  {
    v48 = (_INVENKEY *)&v61->dbCutting.m_List[nTableCode];
    if ( _INVENKEY::IsFilled(v48) )
    {
      v49 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, v48->wItemIndex);
      if ( v49 )
      {
        sprintf(sBuf, "\t%s_%u\r\n", v49->m_strCode, *(_DWORD *)&v48[1]);
        strcat_0(sData, sBuf);
      }
      else
      {
        v20 = v48->wItemIndex;
        LODWORD(v30) = dwIDSerial;
        CLogFile::Write(
          &stru_1799C8E78,
          "CMgrAvatorItemHistory::have_item_close() : _CUTTING_DB_BASE::_LIST* pList->Key.wItemIndex(%u) i(%d) Serial(%u)",
          v20,
          (unsigned int)nTableCode);
      }
    }
  }
  if ( v61->dbAvator.m_byRaceSexCode >> 1 )
  {
    if ( v61->dbAvator.m_byRaceSexCode >> 1 == 1 )
    {
      sprintf(sBuf, "ANIMUS\r\n");
      strcat_0(sData, sBuf);
      for ( nTableCode = 0; nTableCode < 4; ++nTableCode )
      {
        v51 = (_ANIMUSKEY *)&v61->dbAnimus + 34 * nTableCode;
        if ( _ANIMUSKEY::IsFilled(v51) )
        {
          v52 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 24, v51->byItemIndex);
          v30 = *(double *)&v51[17].byItemIndex;
          sprintf(sBuf, "\t%s_%u[%I64u]\r\n", v52->m_strCode, *(_QWORD *)&v51[1].byItemIndex);
          strcat_0(sData, sBuf);
        }
      }
    }
  }
  else
  {
    sprintf(sBuf, "UNIT\r\n");
    strcat_0(sData, sBuf);
    for ( nTableCode = 0; nTableCode < 4; ++nTableCode )
    {
      v50 = (_UNIT_DB_BASE *)((char *)&v61->dbUnit + 62 * nTableCode);
      if ( v61->dbUnit.m_List[nTableCode].byFrame != 255 )
      {
        v21 = v50->m_List[0].byPart[4];
        v22 = v50->m_List[0].byPart[3];
        v23 = v50->m_List[0].byPart[2];
        v24 = v50->m_List[0].byPart[1];
        v25 = v50->m_List[0].byPart[0];
        v26 = v50->m_List[0].byFrame;
        v35 = v50->m_List[0].byPart[5];
        LODWORD(v34) = v21;
        LODWORD(v33) = v22;
        LODWORD(v32) = v23;
        LODWORD(v31) = v24;
        LODWORD(v30) = v25;
        sprintf(sBuf, "\t%d>fr:%d %d/%d/%d/%d/%d/%d\r\n", (unsigned int)nTableCode, v26);
        strcat_0(sData, sBuf);
      }
    }
  }
  v27 = v61->dbTrunk.bySlotNum;
  v30 = v61->dbTrunk.dGold;
  sprintf(sBuf, "TRUNK (slot:%d, ^D:%.0f, ^G:%.0f)\r\n", v27, v61->dbTrunk.dDalant);
  strcat_0(sData, sBuf);
  for ( nTableCode = 0; nTableCode < v61->dbTrunk.bySlotNum; ++nTableCode )
  {
    v53 = &v61->dbTrunk.m_List[nTableCode].Key;
    if ( _INVENKEY::IsFilled(v53) )
    {
      v54 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v53->byTableCode, v53->wItemIndex);
      v56 = v53[4].bySlotIndex;
      v57 = pRace_0;
      v58 = v53->byTableCode;
      v28 = DisplayItemUpgInfo(v58, *(_DWORD *)&v53[3]);
      v32 = v57[v56];
      v31 = *(_QWORD *)&v53[5].byTableCode;
      v30 = *(double *)&v28;
      sprintf(sBuf, "\t%s_%u_@%s[%I64u] %s\r\n", v54->m_strCode, *(_QWORD *)&v53[1].bySlotIndex);
      strcat_0(sData, sBuf);
    }
  }
  CMgrAvatorItemHistory::WriteFile(v59, pszFileName, sData);
}
