/*
 * Function: ??R?$hash_compare@HU?$less@H@std@@@stdext@@QEBA_NAEBH0@Z
 * Address: 0x14020DA40
 */

bool __fastcall stdext::hash_compare<int,std::less<int>>::operator()(stdext::hash_compare<int,std::less<int> > *this, const int *_Keyval1, const int *_Keyval2)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-28h]@1
  stdext::hash_compare<int,std::less<int> > *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  return std::less<int>::operator()(&v7->comp, _Keyval1, _Keyval2);
}
