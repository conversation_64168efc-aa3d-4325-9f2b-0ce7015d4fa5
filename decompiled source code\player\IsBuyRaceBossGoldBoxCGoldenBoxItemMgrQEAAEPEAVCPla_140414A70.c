/*
 * Function: ?IsBuyRaceBossGoldBox@CGoldenBoxItemMgr@@QEAAEPEAVCPlayer@@@Z
 * Address: 0x140414A70
 */

char __fastcall CGoldenBoxItemMgr::IsBuyRaceBossGoldBox(CGoldenBoxItemMgr *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  char result; // al@5
  int v6; // eax@6
  CGoldenBoxItemMgr *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-38h]@1
  int v9; // [sp+20h] [bp-18h]@4
  int v10; // [sp+24h] [bp-14h]@6
  CPlayer *v11; // [sp+48h] [bp+10h]@1

  v11 = pOne;
  v2 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = CPlayerDB::GetRaceCode(&pOne->m_Param);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  if ( v11->m_dwObjSerial == CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v9, 0) )
  {
    v10 = CPlayerDB::GetRaceCode(&v11->m_Param);
    v6 = CHolyStoneSystem::GetHolyMasterRace(&g_HolySys);
    if ( v10 == v6 )
    {
      if ( CHolyStoneSystem::GetGoldBoxConsumable(&g_HolySys) == 1 )
      {
        v7 = CGoldenBoxItemMgr::Instance();
        if ( CGoldenBoxItemMgr::Get_Event_Status(v7) == 2 )
          result = 0;
        else
          result = 17;
      }
      else
      {
        result = 31;
      }
    }
    else
    {
      result = 30;
    }
  }
  else
  {
    result = 29;
  }
  return result;
}
