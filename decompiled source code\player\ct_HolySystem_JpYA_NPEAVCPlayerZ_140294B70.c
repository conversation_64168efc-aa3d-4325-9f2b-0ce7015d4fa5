/*
 * Function: ?ct_HolySystem_Jp@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294B70
 */

char __fastcall ct_HolySystem_Jp(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@10
  int nRace; // [sp+24h] [bp-24h]@18
  int nPassTime; // [sp+28h] [bp-20h]@18
  int v8; // [sp+2Ch] [bp-1Ch]@24
  int v9; // [sp+30h] [bp-18h]@24
  int v10; // [sp+34h] [bp-14h]@30
  CPlayer *pOnea; // [sp+50h] [bp+8h]@1

  pOnea = pOne;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( !pOnea || !pOnea->m_bOper )
    return 0;
  if ( s_nWordCount < 1 )
    return 0;
  if ( !strcmp_0("start", s_pwszDstCheat[0]) )
  {
    if ( s_nWordCount >= 2 )
    {
      v5 = atoi(s_pwszDstCheat[1]);
      CHolyStoneSystem::AlterSchedule(&g_HolySys, 1, v5);
      return 1;
    }
    return 0;
  }
  if ( !strcmp_0("end", s_pwszDstCheat[0]) )
    return CHolyStoneSystem::ct_StopBattle(&g_HolySys);
  if ( strcmp_0("keeper", s_pwszDstCheat[0]) )
  {
    if ( s_nWordCount >= 1 && !strcmp_0("state", s_pwszDstCheat[0]) )
      return CHolyStoneSystem::ct_State(&g_HolySys, pOnea);
    return 0;
  }
  if ( s_nWordCount < 2 )
    return 0;
  if ( !strcmp_0("neutral", s_pwszDstCheat[1]) )
  {
    if ( s_nWordCount >= 3 )
    {
      nRace = atoi(s_pwszDstCheat[2]);
      nPassTime = 0;
      if ( s_nWordCount >= 4 )
        nPassTime = atoi(s_pwszDstCheat[3]);
      return CHolyStoneSystem::ct_KeeperStart(&g_HolySys, 3, nRace, nPassTime);
    }
    return 0;
  }
  if ( strcmp_0("invincible", s_pwszDstCheat[1]) )
  {
    if ( !strcmp_0("chaos", s_pwszDstCheat[1]) && s_nWordCount >= 3 )
    {
      v10 = atoi(s_pwszDstCheat[2]);
      return CHolyStoneSystem::ct_KeeperStart(&g_HolySys, 6, v10, 0);
    }
    return 0;
  }
  if ( s_nWordCount < 3 )
    return 0;
  v8 = atoi(s_pwszDstCheat[2]);
  v9 = 0;
  if ( s_nWordCount >= 4 )
    v9 = atoi(s_pwszDstCheat[3]);
  return CHolyStoneSystem::ct_KeeperStart(&g_HolySys, 4, v8, v9);
}
