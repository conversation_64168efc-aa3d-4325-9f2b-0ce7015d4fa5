/*
 * Function: ?dev_after_effect@CPlayer@@QEAA_NXZ
 * Address: 0x1400C0370
 */

char __fastcall CPlayer::dev_after_effect(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGameObjectVtbl *v3; // rax@5
  __int64 v4; // r8@5
  char result; // al@7
  __int64 v6; // [sp+0h] [bp-88h]@1
  __int16 v7; // [sp+20h] [bp-68h]@5
  char v8; // [sp+28h] [bp-60h]@5
  char *v9; // [sp+30h] [bp-58h]@5
  __int64 v10; // [sp+38h] [bp-50h]@5
  _base_fld *v11; // [sp+40h] [bp-48h]@4
  char v12; // [sp+54h] [bp-34h]@5
  int v13; // [sp+64h] [bp-24h]@5
  unsigned int v14; // [sp+68h] [bp-20h]@5
  __int16 v15; // [sp+6Ch] [bp-1Ch]@5
  char v16; // [sp+70h] [bp-18h]@5
  CPlayer *v17; // [sp+90h] [bp+8h]@1

  v17 = this;
  v1 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v11 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
  if ( v11 )
  {
    v13 = 1;
    v14 = _sf_continous::GetSFContCurTime();
    v15 = *(_WORD *)&v11[16].m_strCode[24];
    v3 = v17->vfptr;
    v10 = 0i64;
    v9 = &v12;
    v8 = 1;
    v7 = *(_WORD *)&v11[16].m_strCode[24];
    LOBYTE(v4) = 3;
    v16 = ((int (__fastcall *)(CPlayer *, _QWORD, __int64, _QWORD))v3[1].__vecDelDtor)(
            v17,
            v11[13].m_strCode[32],
            v4,
            v11->m_dwIndex);
    if ( !v16 )
      v17->m_bAfterEffect = 1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
