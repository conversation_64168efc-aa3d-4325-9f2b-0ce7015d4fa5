/*
 * Function: ?Ticketting@CTransportShip@@QEAA_NPEAVCPlayer@@@Z
 * Address: 0x140264450
 */

char __fastcall CTransportShip::Ticketting(CTransportShip *this, CPlayer *pExiter)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@14
  char result; // al@14
  int v6; // eax@15
  int v7; // eax@21
  __int64 v8; // [sp+0h] [bp-D8h]@1
  void *Src; // [sp+30h] [bp-A8h]@4
  int *v10; // [sp+38h] [bp-A0h]@4
  int j; // [sp+40h] [bp-98h]@4
  __int64 v12; // [sp+48h] [bp-90h]@7
  _TicketItem_fld *pTicketFld; // [sp+50h] [bp-88h]@9
  _STORAGE_LIST::_db_con Dst; // [sp+68h] [bp-70h]@13
  int nPortalIndex; // [sp+A4h] [bp-34h]@14
  int v16; // [sp+A8h] [bp-30h]@15
  char v17; // [sp+ACh] [bp-2Ch]@15
  CGameStatistics::_DAY *v18; // [sp+B0h] [bp-28h]@19
  int v19; // [sp+B8h] [bp-20h]@21
  int v20; // [sp+BCh] [bp-1Ch]@14
  int v21; // [sp+C0h] [bp-18h]@21
  CTransportShip *v22; // [sp+E0h] [bp+8h]@1
  CPlayer *pPtr; // [sp+E8h] [bp+10h]@1

  pPtr = pExiter;
  v22 = this;
  v2 = &v8;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  Src = 0i64;
  v10 = &pExiter->m_Param.m_dbInven.m_nListNum;
  for ( j = 0; j < v10[1]; ++j )
  {
    v12 = *(_QWORD *)(v10 + 3) + 50i64 * j;
    if ( *(_BYTE *)v12 )
    {
      if ( *(_BYTE *)(v12 + 1) == 28 )
      {
        pTicketFld = (_TicketItem_fld *)CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 28, *(_WORD *)(v12 + 3));
        if ( CTransportShip::GetCurRideShipThisTicket(v22, pTicketFld) )
        {
          Src = (void *)v12;
          break;
        }
      }
    }
  }
  if ( Src )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, Src, 0x32ui64);
    if ( CPlayer::Emb_DelStorage(pPtr, 0, j, 0, 1, "CTransportShip::Ticketting()") )
    {
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        pPtr->m_ObjID.m_wIndex,
        &Dst,
        pPtr->m_szItemHistoryFileName);
      CTransportShip::SendMsg_TicketCheck(v22, pPtr->m_ObjID.m_wIndex, 1, Dst.m_wSerial);
      v6 = CPlayerDB::GetRaceCode(&pPtr->m_Param);
      v16 = CTransportShip::GetOutPortalIndex(v22, v6, v22->m_byDirect);
      CTransportShip::CheckTicket_Pass(v22, pPtr, v16);
      v17 = 1;
      if ( CMainThread::IsReleaseServiceMode(&g_Main) && pPtr->m_byUserDgr )
        v17 = 0;
      if ( v17 )
      {
        v18 = CGameStatistics::CurWriteData(&g_GameStatistics);
        ++v18->dwEderEnter_Evt;
      }
      result = 1;
    }
    else
    {
      CTransportShip::SendMsg_TicketCheck(v22, pPtr->m_ObjID.m_wIndex, 0, 0xFFFFu);
      v20 = v22->m_byDirect == 0;
      v4 = CPlayerDB::GetRaceCode(&pPtr->m_Param);
      nPortalIndex = CTransportShip::GetOutPortalIndex(v22, v4, v20);
      CTransportShip::CheckTicket_Kick(v22, pPtr, nPortalIndex);
      result = 1;
    }
  }
  else
  {
    CTransportShip::SendMsg_TicketCheck(v22, pPtr->m_ObjID.m_wIndex, 0, 0xFFFFu);
    v21 = v22->m_byDirect == 0;
    v7 = CPlayerDB::GetRaceCode(&pPtr->m_Param);
    v19 = CTransportShip::GetOutPortalIndex(v22, v7, v21);
    CTransportShip::CheckTicket_Kick(v22, pPtr, v19);
    result = 1;
  }
  return result;
}
