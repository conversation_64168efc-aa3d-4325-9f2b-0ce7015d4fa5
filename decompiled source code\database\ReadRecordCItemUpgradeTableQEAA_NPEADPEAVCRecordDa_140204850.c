/*
 * Function: ?ReadRecord@CItemUpgradeTable@@QEAA_NPEADPEAVCRecordData@@0@Z
 * Address: 0x140204850
 */

bool __fastcall CItemUpgradeTable::ReadRecord(CItemUpgradeTable *this, char *szFile, CRecordData *pResRec, char *pszErrMsg)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  unsigned __int16 *v7; // rax@10
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned __int16 *v9; // [sp+20h] [bp-18h]@10
  unsigned __int64 v10; // [sp+28h] [bp-10h]@10
  CItemUpgradeTable *v11; // [sp+40h] [bp+8h]@1
  CRecordData *pResReca; // [sp+50h] [bp+18h]@1
  char *pszErrMsga; // [sp+58h] [bp+20h]@1

  pszErrMsga = pszErrMsg;
  pResReca = pResRec;
  v11 = this;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CRecordData::ReadRecord(&v11->m_tblItemUpgrade, szFile, 0xD8u, pszErrMsg) )
  {
    v11->m_nResNum = CRecordData::GetRecordNum(&v11->m_tblItemUpgrade);
    if ( v11->m_nResNum > 0 )
    {
      v10 = v11->m_nResNum;
      v7 = (unsigned __int16 *)operator new[](saturated_mul(2ui64, v10));
      v9 = v7;
      v11->m_pwResIndex = v7;
      result = CItemUpgradeTable::Indexing(v11, pResReca, pszErrMsga);
    }
    else
    {
      if ( pszErrMsga )
        sprintf(pszErrMsga, "CItemUpgradeTable.. record Num <= 0");
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
