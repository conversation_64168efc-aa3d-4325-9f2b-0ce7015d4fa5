/*
 * Function: ?<PERSON><PERSON><PERSON><PERSON>@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAD@Z
 * Address: 0x1403E1ED0
 */

void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::Ask<PERSON>oin(GUILD_BATTLE::CNormalGuildBattleGuild *this, char *wszDestGuildName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-A8h]@1
  _guild_member_info *v5; // [sp+30h] [bp-78h]@7
  char Dest; // [sp+48h] [bp-60h]@8
  char pbyType; // [sp+74h] [bp-34h]@8
  char v8; // [sp+75h] [bp-33h]@8
  int j; // [sp+84h] [bp-24h]@8
  unsigned __int64 v10; // [sp+90h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v11; // [sp+B0h] [bp+8h]@1

  v11 = this;
  v2 = &v4;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( v11->m_pkGuild )
  {
    if ( wszDestGuildName )
    {
      v5 = v11->m_pkGuild->m_MemberData;
      if ( v5 )
      {
        strcpy_0(&Dest, wszDestGuildName);
        pbyType = 27;
        v8 = 55;
        for ( j = 0; j < 50; ++j )
        {
          if ( _guild_member_info::IsFill(&v5[j]) )
          {
            if ( v5[j].pPlayer )
              CNetProcess::LoadSendMsg(unk_1414F2088, v5[j].pPlayer->m_ObjID.m_wIndex, &pbyType, &Dest, 0x11u);
          }
        }
      }
    }
  }
}
