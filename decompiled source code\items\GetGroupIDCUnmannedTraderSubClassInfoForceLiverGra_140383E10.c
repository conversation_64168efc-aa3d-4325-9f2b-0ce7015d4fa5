/*
 * Function: ?GetGroupID@CUnmannedTraderSubClassInfoForceLiverGrade@@UEAA_NEGAEAE@Z
 * Address: 0x140383E10
 */

char __fastcall CUnmannedTraderSubClassInfoForceLiverGrade::GetGroupID(CUnmannedTraderSubClassInfoForceLiverGrade *this, char byTableCode, unsigned __int16 wItemTableIndex, char *bySubClass)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _base_fld *v8; // [sp+20h] [bp-18h]@6
  CUnmannedTraderSubClassInfoForceLiverGrade *v9; // [sp+40h] [bp+8h]@1
  char *v10; // [sp+58h] [bp+20h]@1

  v10 = bySubClass;
  v9 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( byTableCode == 15 )
  {
    v8 = CRecordData::GetRecord(&stru_1799C8410 + 1, *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + wItemTableIndex));
    if ( v8 )
    {
      if ( v9->m_byGrade == v8[4].m_strCode[60] )
      {
        *v10 = v9->m_dwID;
        result = 1;
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
