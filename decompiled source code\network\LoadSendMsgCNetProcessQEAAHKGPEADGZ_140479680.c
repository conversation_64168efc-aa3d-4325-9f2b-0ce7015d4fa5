/*
 * Function: ?LoadSendMsg@CNetProcess@@QEAAHKGPEADG@Z
 * Address: 0x140479680
 */

int __fastcall CNetProcess::LoadSendMsg(CNetProcess *this, unsigned int dwClientIndex, unsigned __int16 wType, char *szMsg, unsigned __int16 nLen)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-38h]@1
  CNetProcess *v9; // [sp+40h] [bp+8h]@1
  unsigned __int16 pbyType; // [sp+50h] [bp+18h]@1
  char *szMsga; // [sp+58h] [bp+20h]@1

  szMsga = szMsg;
  pbyType = wType;
  v9 = this;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  return CNetProcess::LoadSendMsg(v9, dwClientIndex, (char *)&pbyType, szMsg, nLen);
}
