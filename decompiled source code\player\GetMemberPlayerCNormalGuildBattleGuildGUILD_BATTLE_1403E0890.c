/*
 * Function: ?GetMemberPlayer@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAPEAVCPlayer@@K@Z
 * Address: 0x1403E0890
 */

CPlayer *__fastcall GUILD_BATTLE::CNormalGuildBattleGuild::GetMemberPlayer(GUILD_BATTLE::CNormalGuildBattleGuild *this, unsigned int dwSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPlayer *result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleGuild *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetMember(v7, dwSerial);
  if ( v6 >= 0 )
    result = GUILD_BATTLE::CNormalGuildBattleGuildMember::GetPlayer(&v7->m_kMember[v6]);
  else
    result = 0i64;
  return result;
}
