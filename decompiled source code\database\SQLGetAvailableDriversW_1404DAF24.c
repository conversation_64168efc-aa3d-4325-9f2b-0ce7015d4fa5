/*
 * Function: SQLGetAvailableDriversW
 * Address: 0x1404DAF24
 */

int __fastcall SQLGetAvailableDriversW(const unsigned __int16 *lpszInfFile, unsigned __int16 *lpszBuf, unsigned __int16 cbBufMax, unsigned __int16 *pcbBufOut)
{
  const unsigned __int16 *v4; // rbp@1
  unsigned __int16 *v5; // rbx@1
  unsigned __int16 v6; // di@1
  unsigned __int16 *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = lpszInfFile;
  v5 = pcbBufOut;
  v6 = cbBufMax;
  v7 = lpszBuf;
  v8 = ODBC___GetSetupProc("SQLGetAvailableDriversW");
  if ( v8 )
    result = ((int (__fastcall *)(const unsigned __int16 *, unsigned __int16 *, _QWORD, unsigned __int16 *))v8)(
               v4,
               v7,
               v6,
               v5);
  else
    result = 0;
  return result;
}
