/*
 * Function: j_?push_money@CMgrGuildHistory@@QEAAXPEADKHHNN0@Z
 * Address: 0x14000E1AB
 */

void __fastcall CMgrGuildHistory::push_money(CMgrGuildHistory *this, char *pszIOerName, unsigned int dwIOerSerial, int nPushDalant, int nPushGold, long double dTotalDalant, long double dTotalGold, char *pszFileName)
{
  CMgrGuildHistory::push_money(
    this,
    pszIOerName,
    dwIOerSerial,
    nPushDalant,
    nPushGold,
    dTotalDalant,
    dTotalGold,
    pszFileName);
}
