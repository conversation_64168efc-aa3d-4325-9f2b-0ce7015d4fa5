/*
 * Function: ?create_automine_table@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404A8760
 */

bool __fastcall CRFWorldDatabase::create_automine_table(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v5->vfptr, "[dbo].[tbl_automine_inven]") )
    result = 1;
  else
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v5->vfptr, "{ CALL pcreate_automine }", 1);
  return result;
}
