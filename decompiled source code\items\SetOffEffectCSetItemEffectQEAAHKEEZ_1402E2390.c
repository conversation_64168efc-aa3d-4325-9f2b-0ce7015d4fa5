/*
 * Function: ?SetOffEffect@CSetItemEffect@@QEAAHKEE@Z
 * Address: 0x1402E2390
 */

signed __int64 __fastcall CSetItemEffect::SetOffEffect(CSetItemEffect *this, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CSetItemEffect *v8; // [sp+30h] [bp+8h]@1
  unsigned int dwSetItema; // [sp+38h] [bp+10h]@1

  dwSetItema = dwSetItem;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( CSetItemEffect::IsSetOnComplete(v8, dwSetItem, bySetItemNum, bySetEffectNum) )
  {
    if ( CSetItemEffect::Detach_Set(v8, dwSetItema) )
      result = 1i64;
    else
      result = 6i64;
  }
  else
  {
    result = 6i64;
  }
  return result;
}
