/*
 * Function: ?Update_RankInGuild_Step6@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404B9520
 */

char __fastcall CRFWorldDatabase::Update_RankInGuild_Step6(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CRFWorldDatabase *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CRFNewDatabase::FmtLog(
    (CRFNewDatabase *)&v5->vfptr,
    "CRFWorldDatabase::Update_RankInGuild_Step6() : Start drop #tbl_RankInGuild, #tbl_RankInGuildAll, #tbl_RankInGuildCom Table");
  if ( CRFWorldDatabase::Update_RankInGuild_Step7(v5) )
  {
    if ( CRFWorldDatabase::Update_RankInGuild_Step8(v5) )
    {
      if ( CRFWorldDatabase::Update_RankInGuild_Step9(v5) )
      {
        CRFNewDatabase::FmtLog(
          (CRFNewDatabase *)&v5->vfptr,
          "CRFWorldDatabase::Update_RankInGuild_Step6() : End drop #tbl_RankInGuild, #tbl_RankInGuildAll, #tbl_RankInGuildCom Table");
        result = 1;
      }
      else
      {
        CRFWorldDatabase::Update_RankInGuild_Step9(v5);
        result = 0;
      }
    }
    else
    {
      CRFWorldDatabase::Update_RankInGuild_Step8(v5);
      result = 0;
    }
  }
  else
  {
    CRFWorldDatabase::Update_RankInGuild_Step7(v5);
    result = 0;
  }
  return result;
}
