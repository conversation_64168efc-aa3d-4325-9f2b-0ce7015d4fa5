/*
 * Function: ?GetRand@_100_per_random_table@@QEAAGXZ
 * Address: 0x1400B81D0
 */

__int64 __fastcall _100_per_random_table::GetRand(_100_per_random_table *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned __int16 v5; // [sp+20h] [bp-18h]@6
  _100_per_random_table *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( (signed int)v6->m_wCurPoint >= 100 )
  {
    v6->m_wCurTable = rand() % 10;
    v6->m_wCurPoint = 0;
  }
  v5 = *(&_100_per_random_table::s_wRecord[100 * (unsigned __int64)v6->m_wCurTable] + v6->m_wCurPoint++);
  return (unsigned int)(v5 % 100);
}
