/*
 * Function: ?GetGenAttackProb@CPlayer@@UEAAHPEAVCCharacter@@H_N@Z
 * Address: 0x1400614C0
 */

__int64 __fastcall CPlayer::GetGenAttackProb(CPlayer *this, CCharacter *pDst, int nPart, bool bBackAttack)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@10
  int v7; // eax@18
  int v8; // eax@18
  float v9; // xmm0_4@21
  __int64 v11; // [sp+0h] [bp-A8h]@1
  float v12; // [sp+20h] [bp-88h]@5
  float v13; // [sp+24h] [bp-84h]@9
  _base_fld *v14; // [sp+28h] [bp-80h]@11
  _base_fld *v15; // [sp+30h] [bp-78h]@13
  float v16; // [sp+38h] [bp-70h]@15
  int v17; // [sp+3Ch] [bp-6Ch]@16
  unsigned int v18; // [sp+40h] [bp-68h]@21
  float v19; // [sp+58h] [bp-50h]@21
  float v20; // [sp+5Ch] [bp-4Ch]@21
  float v21; // [sp+60h] [bp-48h]@21
  float v22; // [sp+64h] [bp-44h]@21
  float v23; // [sp+68h] [bp-40h]@21
  float v24; // [sp+74h] [bp-34h]@21
  int v25; // [sp+78h] [bp-30h]@21
  int v26; // [sp+7Ch] [bp-2Ch]@24
  int v27; // [sp+80h] [bp-28h]@18
  CGameObjectVtbl *v28; // [sp+88h] [bp-20h]@18
  int v29; // [sp+90h] [bp-18h]@25
  unsigned int v30; // [sp+94h] [bp-14h]@28
  CPlayer *v31; // [sp+B0h] [bp+8h]@1
  CCharacter *v32; // [sp+B8h] [bp+10h]@1
  int v33; // [sp+C0h] [bp+18h]@1
  bool v34; // [sp+C8h] [bp+20h]@1

  v34 = bBackAttack;
  v33 = nPart;
  v32 = pDst;
  v31 = this;
  v4 = &v11;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( v31->m_pmWpn.byWpType == 255 )
  {
    v12 = FLOAT_10_0;
  }
  else if ( v31->m_pmWpn.byWpType == 9 )
  {
    v12 = FLOAT_10_0;
  }
  else
  {
    v12 = (float)_MASTERY_PARAM::GetMasteryPerMast(&v31->m_pmMst, 0, v31->m_pmWpn.byWpClass);
  }
  v13 = FLOAT_1_0;
  if ( CPlayer::IsRidingUnit(v31) )
  {
    v14 = CRecordData::GetRecord(
            &stru_1799C86D0 + v31->m_byUsingWeaponPart,
            v31->m_pUsingUnit->byPart[v31->m_byUsingWeaponPart]);
    if ( v14 )
      v13 = (float)(signed int)v14[5].m_dwIndex;
    v15 = CRecordData::GetRecord(&stru_1799C86D0, v31->m_pUsingUnit->byPart[0]);
    if ( v15 )
      v13 = v13 + (float)(signed int)v15[5].m_dwIndex;
  }
  else
  {
    v6 = CPlayerDB::GetLevel(&v31->m_Param);
    v13 = (float)v6 + v12;
  }
  v16 = 0.0;
  if ( v32->m_ObjID.m_byID )
  {
    if ( v32->m_ObjID.m_byID == 1 )
      v16 = (float)((int (__fastcall *)(CCharacter *, _QWORD))v32->vfptr->GetDefSkill)(v32, v34);
  }
  else
  {
    v17 = 1;
    if ( BYTE2(v32[1].m_fCurPos[2]) )
      v17 = 2;
    v7 = CPlayerDB::GetLevel((CPlayerDB *)&v32[1].m_fOldPos[2]);
    v27 = v17 * v7;
    v28 = v32->vfptr;
    v8 = ((int (__fastcall *)(CCharacter *, _QWORD))v28->GetDefSkill)(v32, v34);
    v16 = (float)(v8 + v27);
  }
  v18 = (signed int)ffloor(v13 - v16) / 4 + 95;
  v19 = FLOAT_0_23;
  v20 = FLOAT_0_22;
  v21 = FLOAT_0_18000001;
  v22 = FLOAT_0_17;
  v23 = FLOAT_0_2;
  v24 = 5.0 * *(&v19 + v33);
  v9 = (float)(signed int)v18 * v24;
  v18 = (signed int)ffloor(v9);
  v25 = 0;
  if ( v31->m_pmWpn.byWpType == 7 )
  {
    _effect_parameter::GetEff_Plus(&v31->m_EP, 2);
    v25 = (signed int)ffloor(v9);
  }
  else
  {
    _effect_parameter::GetEff_Plus(&v31->m_EP, v31->m_pmWpn.byWpClass);
    v25 = (signed int)ffloor(v9);
  }
  v26 = ((int (__fastcall *)(CCharacter *))v32->vfptr->GetAvoidRate)(v32);
  _effect_parameter::GetEff_Plus(&v31->m_EP, 40);
  v25 = (signed int)ffloor((float)v25 + v9);
  v18 += v25 - v26;
  if ( (signed int)v18 <= 5 )
    v29 = 5;
  else
    v29 = v18;
  v18 = v29;
  if ( v29 >= 95 )
    v30 = 95;
  else
    v30 = v18;
  return v30;
}
