/*
 * Function: ?Load_Conditional_Event@CashItemRemoteStore@@QEAAXXZ
 * Address: 0x1402FC330
 */

void __fastcall CashItemRemoteStore::Load_Conditional_Event(CashItemRemoteStore *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CashItemRemoteStore *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CLogFile::Write(&v4->m_con_event.m_conevent_log, "Loading Conditional Event");
  v4->m_con_event.m_conevent_status = 0;
  CashItemRemoteStore::Set_Conditional_Evnet_Status(v4, 0);
  CashItemRemoteStore::load_con_event_ini(v4, &v4->m_con_event.m_ini, &v4->m_con_event.m_conevent_ini_file_time);
  CLogFile::Write(&v4->m_con_event.m_conevent_log, "Complete For Conditional Event");
}
