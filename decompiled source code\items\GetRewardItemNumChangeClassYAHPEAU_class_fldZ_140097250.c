/*
 * Function: ?GetRewardItemNumChangeClass@@YAHPEAU_class_fld@@@Z
 * Address: 0x140097250
 */

__int64 __fastcall GetRewardItemNumChangeClass(_class_fld *pClassFld)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned int v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  char *Str1; // [sp+28h] [bp-10h]@7
  _class_fld *v8; // [sp+40h] [bp+8h]@1

  v8 = pClassFld;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = 0;
  for ( j = 0; j < 9; ++j )
  {
    Str1 = v8->m_DefaultItem[j].strDefaultItem;
    if ( strncmp(Str1, "-1", 2ui64) )
      ++v5;
  }
  return v5;
}
