/*
 * Function: ?SendMsg_HonorGuildMark@CPlayer@@QEAAXE@Z
 * Address: 0x1400E7860
 */

void __fastcall CPlayer::SendMsg_HonorGuildMark(CPlayer *this, char byRank)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned int v6; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byRank;
  v6 = v9->m_dwObjSerial;
  pbyType = 27;
  v8 = 122;
  CGameObject::CircleReport((CGameObject *)&v9->vfptr, &pbyType, &szMsg, 5, 1);
}
