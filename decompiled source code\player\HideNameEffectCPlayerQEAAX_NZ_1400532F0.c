/*
 * Function: ?HideNameEffect@CPlayer@@QEAAX_N@Z
 * Address: 0x1400532F0
 */

void __fastcall CPlayer::HideNameEffect(CPlayer *this, bool bAdd)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  char *v5; // rax@35
  char *v6; // rax@35
  CGuildBattleController *v7; // rax@42
  char v8; // al@43
  CGuildMasterEffect *v9; // rax@45
  __int64 v10; // [sp+0h] [bp-88h]@1
  char v11; // [sp+20h] [bp-68h]@4
  int j; // [sp+24h] [bp-64h]@11
  _BUDDY_LIST::__list *v13; // [sp+28h] [bp-60h]@14
  int k; // [sp+30h] [bp-58h]@24
  _BUDDY_LIST::__list *v15; // [sp+38h] [bp-50h]@27
  int l; // [sp+40h] [bp-48h]@28
  CPlayer *ptr; // [sp+48h] [bp-40h]@31
  _guild_member_info *v18; // [sp+50h] [bp-38h]@40
  CGuildBattleController *v19; // [sp+58h] [bp-30h]@43
  unsigned int dwSerial; // [sp+60h] [bp-28h]@4
  int v21; // [sp+64h] [bp-24h]@4
  CPlayer *pkPlayer; // [sp+68h] [bp-20h]@42
  char v23; // [sp+70h] [bp-18h]@45
  CPlayer *pLoger; // [sp+90h] [bp+8h]@1
  bool v25; // [sp+98h] [bp+10h]@1

  v25 = bAdd;
  pLoger = this;
  v2 = &v10;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  dwSerial = CPlayerDB::GetCharSerial(&pLoger->m_Param);
  v21 = CPlayerDB::GetRaceCode(&pLoger->m_Param);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v11 = CPvpUserAndGuildRankingSystem::GetBossType(v4, v21, dwSerial);
  if ( v11 && v11 != 1 && v11 != 5 )
  {
    if ( v25 )
    {
      if ( CPartyPlayer::IsPartyMode(pLoger->m_pPartyMgr) )
        CPlayer::pc_PartyLeaveSelfReqeuest(pLoger);
      for ( j = 0; j < 50; ++j )
      {
        v13 = &pLoger->m_pmBuddy.m_List[j];
        if ( _BUDDY_LIST::__list::fill(v13)
          && v13->pPtr
          && _BUDDY_LIST::SearchBuddyLogoff(&v13->pPtr->m_pmBuddy, pLoger->m_dwObjSerial) )
        {
          CPlayer::SendMsg_BuddyLogoffInform(v13->pPtr, pLoger->m_dwObjSerial);
        }
      }
      if ( pLoger->m_Param.m_pGuild )
      {
        CGuild::LogoffMember(pLoger->m_Param.m_pGuild, pLoger->m_dwObjSerial);
        CGuild::SendMsg_GuildMemberLogoff(pLoger->m_Param.m_pGuild, pLoger->m_dwObjSerial);
      }
      if ( pLoger->m_Param.m_pApplyGuild )
        CGuild::PopApplier(pLoger->m_Param.m_pApplyGuild, pLoger->m_dwObjSerial, 2);
    }
    else
    {
      for ( k = 0; k < 50; ++k )
      {
        v15 = &pLoger->m_pmBuddy.m_List[k];
        if ( _BUDDY_LIST::__list::fill(v15) )
        {
          for ( l = 0; l < 2532; ++l )
          {
            ptr = &g_Player + l;
            if ( ptr->m_bLive && ptr != pLoger && ptr->m_dwObjSerial == v15->dwSerial )
            {
              if ( _BUDDY_LIST::IsBuddy(&ptr->m_pmBuddy, pLoger->m_dwObjSerial) )
              {
                v5 = CPlayerDB::GetCharNameW(&ptr->m_Param);
                _BUDDY_LIST::__list::ON(v15, v5, ptr);
                v6 = CPlayerDB::GetCharNameW(&pLoger->m_Param);
                _BUDDY_LIST::SearchBuddyLogin(&ptr->m_pmBuddy, pLoger, pLoger->m_dwObjSerial, v6);
                CPlayer::SendMsg_BuddyLoginInform(
                  ptr,
                  pLoger->m_dwObjSerial,
                  pLoger->m_wRegionMapIndex,
                  pLoger->m_wRegionIndex);
              }
              break;
            }
          }
        }
      }
      if ( pLoger->m_Param.m_pGuild )
      {
        v18 = CGuild::LoginMember(pLoger->m_Param.m_pGuild, pLoger->m_dwObjSerial, pLoger);
        if ( v18 )
        {
          pLoger->m_Param.m_pGuildMemPtr = v18;
          CGuild::SendMsg_GuildMemberLogin(
            pLoger->m_Param.m_pGuild,
            pLoger->m_dwObjSerial,
            pLoger->m_wRegionMapIndex,
            pLoger->m_wRegionIndex);
          pkPlayer = &g_Player + pLoger->m_ObjID.m_wIndex;
          v7 = CGuildBattleController::Instance();
          CGuildBattleController::LogIn(v7, pkPlayer);
          if ( v18->byClassInGuild == 2 )
          {
            v19 = CGuildBattleController::Instance();
            v8 = CPlayerDB::GetRaceCode(&pLoger->m_Param);
            CGuildBattleController::SendPossibleBattleGuildListFirst(v19, pLoger->m_ObjID.m_wIndex, v8);
          }
          if ( v18->byClassInGuild == 2 )
          {
            v23 = CGuild::GetGrade(pLoger->m_Param.m_pGuild);
            v9 = CGuildMasterEffect::GetInstance();
            CGuildMasterEffect::in_player(v9, pLoger, v23);
          }
        }
        else
        {
          pLoger->m_Param.m_pGuild = 0i64;
        }
      }
      if ( pLoger->m_Param.m_pApplyGuild )
        CGuild::PushApplier(pLoger->m_Param.m_pApplyGuild, pLoger);
    }
  }
  else
  {
    LODWORD(pLoger->m_EP.m_pDataParam->m_fEff_Have[50]) = 0;
  }
}
