/*
 * Function: j_??$_Iter_random@PEAPEAVCMoveMapLimitRight@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitRight@@0@Z
 * Address: 0x14000E4B2
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(CMoveMapLimitRight **const *__formal, CMoveMapLimitRight **const *a2)
{
  return std::_Iter_random<CMoveMapLimitRight * *,CMoveMapLimitRight * *>(__formal, a2);
}
