/*
 * Function: ??0?$_Const_iterator@$0A@@?$list@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@QEAA@AEBV012@@Z
 * Address: 0x14021C840
 */

void __fastcall std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory *>>>::_Const_iterator<0>::_Const_iterator<0>(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *this, std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *v5; // [sp+30h] [bp+8h]@1
  std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *> > >::_Const_iterator<0> *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>::_Bidit<std::pair<int const,CNationSettingFactory *>,__int64,std::pair<int const,CNationSettingFactory *> const *,std::pair<int const,CNationSettingFactory *> const &>(
    (std::_Bidit<std::pair<int const ,CNationSettingFactory *>,__int64,std::pair<int const ,CNationSettingFactory *> const *,std::pair<int const ,CNationSettingFactory *> const &> *)&v5->_Mycont,
    (std::_Bidit<std::pair<int const ,CNationSettingFactory *>,__int64,std::pair<int const ,CNationSettingFactory *> const *,std::pair<int const ,CNationSettingFactory *> const &> *)&__that->_Mycont);
  v5->_Ptr = __thata->_Ptr;
}
