/*
 * Function: ?push_back@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAXAEBU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x14058E570
 */

int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::push_back(__int64 a1, __int64 a2)
{
  unsigned __int64 v2; // rax@1
  unsigned __int64 v3; // rax@1
  __int64 v4; // rax@2
  char v6; // [sp+20h] [bp-58h]@3
  char *v7; // [sp+38h] [bp-40h]@3
  char v8; // [sp+40h] [bp-38h]@3
  unsigned __int64 v9; // [sp+58h] [bp-20h]@1
  __int64 v10; // [sp+60h] [bp-18h]@3
  __int64 v11; // [sp+80h] [bp+8h]@1
  __int64 v12; // [sp+88h] [bp+10h]@1

  v12 = a2;
  v11 = a1;
  LODWORD(v2) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(a1);
  v9 = v2;
  LODWORD(v3) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::capacity(v11);
  if ( v9 >= v3 )
  {
    v7 = &v6;
    v10 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::end(
            v11,
            (__int64)&v6);
    std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::insert(
      v11,
      &v8,
      v10,
      v12);
    LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  }
  else
  {
    LODWORD(v4) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Ufill(
                    v11,
                    *(_QWORD *)(v11 + 24),
                    1i64,
                    v12);
    *(_QWORD *)(v11 + 24) = v4;
  }
  return v4;
}
