/*
 * Function: ??A?$vector@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@QEAAAEAPEAVCUnmannedTraderSubClassInfo@@_K@Z
 * Address: 0x14037E470
 */

CUnmannedTraderSubClassInfo **__fastcall std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator[](std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
