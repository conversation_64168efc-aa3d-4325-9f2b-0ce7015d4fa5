/**
 * @file CMonsterManagement.cpp
 * @brief Monster Management Classes Implementation
 * 
 * Provides comprehensive monster management functionality including looting,
 * aggro management, hierarchy, skill pools, and AI systems.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @date 2024
 */

#include "../Headers/CMonsterManagement.h"
#include "../Headers/CGameObject.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <random>
#include <cmath>

namespace NexusProtection::World {

    // LootItem implementation
    bool LootItem::IsValid() const {
        return itemId != 0 && quantity > 0 && dropChance >= 0.0f && dropChance <= 1.0f;
    }

    std::string LootItem::ToString() const {
        std::ostringstream oss;
        oss << "LootItem{ID:" << itemId << ", Qty:" << quantity 
            << ", Chance:" << dropChance << ", Rare:" << (isRare ? "true" : "false") << "}";
        return oss.str();
    }

    // AggroEntry implementation
    bool AggroEntry::IsValid() const {
        return targetId != nullptr && targetId->IsValid() && aggroValue > 0;
    }

    void AggroEntry::UpdateAggro(uint32_t value) {
        aggroValue += value;
        lastUpdate = std::chrono::steady_clock::now();
    }

    uint32_t AggroEntry::GetDecayedAggro() const {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastUpdate).count();
        
        // Decay aggro over time
        uint32_t decay = static_cast<uint32_t>(elapsed * 10); // 10 aggro per second decay
        return (aggroValue > decay) ? (aggroValue - decay) : 0;
    }

    // MonsterSkill implementation
    bool MonsterSkill::CanUse() const {
        if (!isActive) return false;
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUsed).count();
        return elapsed >= cooldown;
    }

    void MonsterSkill::Use() {
        lastUsed = std::chrono::steady_clock::now();
    }

    uint32_t MonsterSkill::GetRemainingCooldown() const {
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastUsed).count();
        return (elapsed < cooldown) ? (cooldown - static_cast<uint32_t>(elapsed)) : 0;
    }

    // CLootingMgr implementation
    CLootingMgr::CLootingMgr() {
        std::cout << "[DEBUG] CLootingMgr created" << std::endl;
    }

    CLootingMgr::~CLootingMgr() {
        Shutdown();
        std::cout << "[DEBUG] CLootingMgr destroyed" << std::endl;
    }

    bool CLootingMgr::Initialize() {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        try {
            m_lootItems.clear();
            m_playerLoot.clear();
            m_dropChanceMultiplier = 1.0f;
            m_lootTimeoutMs = LOOT_TIMEOUT_MS;
            
            std::cout << "[INFO] CLootingMgr initialized successfully" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[ERROR] CLootingMgr initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void CLootingMgr::Shutdown() {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        m_lootItems.clear();
        m_playerLoot.clear();
        
        std::cout << "[INFO] CLootingMgr shutdown complete" << std::endl;
    }

    void CLootingMgr::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        CleanupExpiredLoot();
    }

    std::vector<LootItem> CLootingMgr::GenerateLoot(uint32_t monsterId, uint32_t monsterLevel) {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        std::vector<LootItem> generatedLoot;
        
        // Generate basic loot based on monster level
        for (const auto& item : m_lootItems) {
            if (monsterLevel >= item.minLevel && monsterLevel <= item.maxLevel) {
                float adjustedChance = CalculateDropChance(item, monsterLevel);
                if (RollForDrop(adjustedChance)) {
                    generatedLoot.push_back(item);
                }
            }
        }
        
        return generatedLoot;
    }

    bool CLootingMgr::AddLootItem(const LootItem& item) {
        if (!item.IsValid()) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        if (m_lootItems.size() >= MAX_LOOT_ITEMS) {
            return false;
        }
        
        m_lootItems.push_back(item);
        return true;
    }

    bool CLootingMgr::RemoveLootItem(uint32_t itemId) {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        auto it = std::remove_if(m_lootItems.begin(), m_lootItems.end(),
            [itemId](const LootItem& item) { return item.itemId == itemId; });
        
        if (it != m_lootItems.end()) {
            m_lootItems.erase(it, m_lootItems.end());
            return true;
        }
        
        return false;
    }

    void CLootingMgr::ClearLoot() {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        m_lootItems.clear();
        m_playerLoot.clear();
    }

    bool CLootingMgr::DistributeLoot(CMonster* monster, CPlayer* killer) {
        if (!monster || !killer) {
            return false;
        }
        
        // Implementation would distribute loot to eligible players
        // For now, just log the action
        std::cout << "[DEBUG] Distributing loot from monster to player" << std::endl;
        return true;
    }

    bool CLootingMgr::CanLoot(CPlayer* player, CMonster* monster) const {
        if (!player || !monster) {
            return false;
        }
        
        // Implementation would check loot rights
        // For now, always allow looting
        return true;
    }

    std::vector<LootItem> CLootingMgr::GetPlayerLoot(CPlayer* player) const {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        // Implementation would return player-specific loot
        // For now, return empty vector
        return std::vector<LootItem>();
    }

    std::string CLootingMgr::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_lootMutex);
        
        std::ostringstream oss;
        oss << "CLootingMgr{";
        oss << "Items:" << m_lootItems.size();
        oss << ", Players:" << m_playerLoot.size();
        oss << ", Multiplier:" << m_dropChanceMultiplier;
        oss << ", Timeout:" << m_lootTimeoutMs;
        oss << "}";
        return oss.str();
    }

    float CLootingMgr::CalculateDropChance(const LootItem& item, uint32_t monsterLevel) const {
        float baseChance = item.dropChance * m_dropChanceMultiplier;
        
        // Adjust chance based on monster level vs item level range
        float levelFactor = 1.0f;
        uint32_t midLevel = (item.minLevel + item.maxLevel) / 2;
        if (monsterLevel > midLevel) {
            levelFactor = 1.0f + (monsterLevel - midLevel) * 0.01f; // 1% increase per level
        }
        
        return std::min(baseChance * levelFactor, 1.0f);
    }

    bool CLootingMgr::RollForDrop(float chance) const {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        static std::uniform_real_distribution<float> dis(0.0f, 1.0f);
        
        return dis(gen) <= chance;
    }

    void CLootingMgr::CleanupExpiredLoot() {
        // Implementation would clean up expired loot
        // For now, just log
        if (!m_playerLoot.empty()) {
            std::cout << "[DEBUG] Cleaning up expired loot" << std::endl;
        }
    }

    // CMonsterAggroMgr implementation
    CMonsterAggroMgr::CMonsterAggroMgr() {
        std::cout << "[DEBUG] CMonsterAggroMgr created" << std::endl;
    }

    CMonsterAggroMgr::~CMonsterAggroMgr() {
        Shutdown();
        std::cout << "[DEBUG] CMonsterAggroMgr destroyed" << std::endl;
    }

    bool CMonsterAggroMgr::Initialize() {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        try {
            m_aggroEntries.clear();
            m_aggroDecayRate = AGGRO_DECAY_RATE;
            m_aggroTimeoutMs = AGGRO_TIMEOUT_MS;
            
            std::cout << "[INFO] CMonsterAggroMgr initialized successfully" << std::endl;
            return true;
            
        } catch (const std::exception& e) {
            std::cout << "[ERROR] CMonsterAggroMgr initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void CMonsterAggroMgr::Shutdown() {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        m_aggroEntries.clear();
        
        std::cout << "[INFO] CMonsterAggroMgr shutdown complete" << std::endl;
    }

    void CMonsterAggroMgr::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        DecayAggro(deltaTime);
        CleanupExpiredAggro();
        SortAggroEntries();
    }

    void CMonsterAggroMgr::AddAggro(_object_id* targetId, uint32_t aggroValue) {
        if (!targetId || !targetId->IsValid() || aggroValue == 0) {
            return;
        }
        
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        AggroEntry* entry = FindAggroEntry(targetId);
        if (entry) {
            entry->UpdateAggro(aggroValue);
        } else {
            if (m_aggroEntries.size() < MAX_AGGRO_ENTRIES) {
                AggroEntry newEntry;
                newEntry.targetId = targetId;
                newEntry.aggroValue = aggroValue;
                newEntry.lastUpdate = std::chrono::steady_clock::now();
                newEntry.isActive = true;
                m_aggroEntries.push_back(newEntry);
            }
        }
    }

    void CMonsterAggroMgr::RemoveAggro(_object_id* targetId) {
        if (!targetId) return;
        
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        m_aggroEntries.erase(
            std::remove_if(m_aggroEntries.begin(), m_aggroEntries.end(),
                [targetId](const AggroEntry& entry) {
                    return entry.targetId && entry.targetId->dwSerial == targetId->dwSerial;
                }),
            m_aggroEntries.end());
    }

    void CMonsterAggroMgr::ClearAggro() {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        m_aggroEntries.clear();
    }

    void CMonsterAggroMgr::DecayAggro(float deltaTime) {
        uint32_t decayAmount = static_cast<uint32_t>(m_aggroDecayRate * deltaTime);
        
        for (auto& entry : m_aggroEntries) {
            if (entry.aggroValue > decayAmount) {
                entry.aggroValue -= decayAmount;
            } else {
                entry.aggroValue = 0;
                entry.isActive = false;
            }
        }
    }

    _object_id* CMonsterAggroMgr::GetPrimaryTarget() const {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        if (m_aggroEntries.empty()) {
            return nullptr;
        }
        
        // Return the target with highest aggro
        auto maxEntry = std::max_element(m_aggroEntries.begin(), m_aggroEntries.end(),
            [](const AggroEntry& a, const AggroEntry& b) {
                return a.GetDecayedAggro() < b.GetDecayedAggro();
            });
        
        return (maxEntry != m_aggroEntries.end() && maxEntry->isActive) ? maxEntry->targetId : nullptr;
    }

    _object_id* CMonsterAggroMgr::GetSecondaryTarget() const {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        if (m_aggroEntries.size() < 2) {
            return nullptr;
        }
        
        // Sort by aggro and return second highest
        auto sortedEntries = m_aggroEntries;
        std::sort(sortedEntries.begin(), sortedEntries.end(),
            [](const AggroEntry& a, const AggroEntry& b) {
                return a.GetDecayedAggro() > b.GetDecayedAggro();
            });
        
        return (sortedEntries.size() > 1 && sortedEntries[1].isActive) ? sortedEntries[1].targetId : nullptr;
    }

    std::vector<_object_id*> CMonsterAggroMgr::GetAllTargets() const {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        std::vector<_object_id*> targets;
        for (const auto& entry : m_aggroEntries) {
            if (entry.isActive && entry.targetId) {
                targets.push_back(entry.targetId);
            }
        }
        
        return targets;
    }

    uint32_t CMonsterAggroMgr::GetAggro(_object_id* targetId) const {
        if (!targetId) return 0;
        
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        for (const auto& entry : m_aggroEntries) {
            if (entry.targetId && entry.targetId->dwSerial == targetId->dwSerial) {
                return entry.GetDecayedAggro();
            }
        }
        
        return 0;
    }

    bool CMonsterAggroMgr::HasAggro(_object_id* targetId) const {
        return GetAggro(targetId) > 0;
    }

    std::string CMonsterAggroMgr::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_aggroMutex);
        
        std::ostringstream oss;
        oss << "CMonsterAggroMgr{";
        oss << "Entries:" << m_aggroEntries.size();
        oss << ", DecayRate:" << m_aggroDecayRate;
        oss << ", Timeout:" << m_aggroTimeoutMs;
        oss << "}";
        return oss.str();
    }

    void CMonsterAggroMgr::SortAggroEntries() {
        std::sort(m_aggroEntries.begin(), m_aggroEntries.end(),
            [](const AggroEntry& a, const AggroEntry& b) {
                return a.GetDecayedAggro() > b.GetDecayedAggro();
            });
    }

    void CMonsterAggroMgr::CleanupExpiredAggro() {
        m_aggroEntries.erase(
            std::remove_if(m_aggroEntries.begin(), m_aggroEntries.end(),
                [](const AggroEntry& entry) {
                    return !entry.isActive || entry.GetDecayedAggro() == 0;
                }),
            m_aggroEntries.end());
    }

    AggroEntry* CMonsterAggroMgr::FindAggroEntry(_object_id* targetId) {
        if (!targetId) return nullptr;
        
        for (auto& entry : m_aggroEntries) {
            if (entry.targetId && entry.targetId->dwSerial == targetId->dwSerial) {
                return &entry;
            }
        }
        
        return nullptr;
    }

    // CMonsterHierarchy implementation
    CMonsterHierarchy::CMonsterHierarchy() {
        std::cout << "[DEBUG] CMonsterHierarchy created" << std::endl;
    }

    CMonsterHierarchy::~CMonsterHierarchy() {
        Shutdown();
        std::cout << "[DEBUG] CMonsterHierarchy destroyed" << std::endl;
    }

    bool CMonsterHierarchy::Initialize() {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        try {
            m_monsterHierarchy.clear();
            LoadDefaultHierarchy();

            std::cout << "[INFO] CMonsterHierarchy initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] CMonsterHierarchy initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void CMonsterHierarchy::Shutdown() {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        m_monsterHierarchy.clear();

        std::cout << "[INFO] CMonsterHierarchy shutdown complete" << std::endl;
    }

    void CMonsterHierarchy::OnChildRegenLoop() {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        // Process child monster regeneration loop
        // This would typically handle respawning of child monsters
        // For now, provide a placeholder implementation

        std::cout << "[DEBUG] CMonsterHierarchy processing child regeneration loop" << std::endl;
    }

    CMonster* CMonsterHierarchy::GetParent() const {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        // Return parent monster (if any)
        // For now, return nullptr as placeholder
        return nullptr;
    }

    uint32_t CMonsterHierarchy::ChildKindCount() const {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        // Return count of child monsters
        // For now, return 0 as placeholder
        return 0;
    }

    MonsterHierarchyLevel CMonsterHierarchy::GetHierarchyLevel(uint32_t monsterId) const {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        auto it = m_monsterHierarchy.find(monsterId);
        return (it != m_monsterHierarchy.end()) ? it->second : MonsterHierarchyLevel::Minion;
    }

    void CMonsterHierarchy::SetHierarchyLevel(uint32_t monsterId, MonsterHierarchyLevel level) {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);
        m_monsterHierarchy[monsterId] = level;
    }

    bool CMonsterHierarchy::IsElite(uint32_t monsterId) const {
        MonsterHierarchyLevel level = GetHierarchyLevel(monsterId);
        return level >= MonsterHierarchyLevel::Elite;
    }

    bool CMonsterHierarchy::IsBoss(uint32_t monsterId) const {
        MonsterHierarchyLevel level = GetHierarchyLevel(monsterId);
        return level >= MonsterHierarchyLevel::Boss;
    }

    float CMonsterHierarchy::GetHealthMultiplier(MonsterHierarchyLevel level) const {
        switch (level) {
            case MonsterHierarchyLevel::Minion: return 1.0f;
            case MonsterHierarchyLevel::Elite: return 2.0f;
            case MonsterHierarchyLevel::Champion: return 3.5f;
            case MonsterHierarchyLevel::Boss: return 5.0f;
            case MonsterHierarchyLevel::WorldBoss: return 10.0f;
            case MonsterHierarchyLevel::RaidBoss: return 20.0f;
            default: return 1.0f;
        }
    }

    float CMonsterHierarchy::GetDamageMultiplier(MonsterHierarchyLevel level) const {
        switch (level) {
            case MonsterHierarchyLevel::Minion: return 1.0f;
            case MonsterHierarchyLevel::Elite: return 1.5f;
            case MonsterHierarchyLevel::Champion: return 2.0f;
            case MonsterHierarchyLevel::Boss: return 2.5f;
            case MonsterHierarchyLevel::WorldBoss: return 3.0f;
            case MonsterHierarchyLevel::RaidBoss: return 4.0f;
            default: return 1.0f;
        }
    }

    float CMonsterHierarchy::GetExperienceMultiplier(MonsterHierarchyLevel level) const {
        switch (level) {
            case MonsterHierarchyLevel::Minion: return 1.0f;
            case MonsterHierarchyLevel::Elite: return 2.0f;
            case MonsterHierarchyLevel::Champion: return 4.0f;
            case MonsterHierarchyLevel::Boss: return 8.0f;
            case MonsterHierarchyLevel::WorldBoss: return 15.0f;
            case MonsterHierarchyLevel::RaidBoss: return 25.0f;
            default: return 1.0f;
        }
    }

    float CMonsterHierarchy::GetLootMultiplier(MonsterHierarchyLevel level) const {
        switch (level) {
            case MonsterHierarchyLevel::Minion: return 1.0f;
            case MonsterHierarchyLevel::Elite: return 1.5f;
            case MonsterHierarchyLevel::Champion: return 2.5f;
            case MonsterHierarchyLevel::Boss: return 4.0f;
            case MonsterHierarchyLevel::WorldBoss: return 6.0f;
            case MonsterHierarchyLevel::RaidBoss: return 10.0f;
            default: return 1.0f;
        }
    }

    std::string CMonsterHierarchy::GetHierarchyName(MonsterHierarchyLevel level) const {
        return MonsterManagementUtils::MonsterHierarchyLevelToString(level);
    }

    std::string CMonsterHierarchy::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_hierarchyMutex);

        std::ostringstream oss;
        oss << "CMonsterHierarchy{";
        oss << "Monsters:" << m_monsterHierarchy.size();
        oss << "}";
        return oss.str();
    }

    void CMonsterHierarchy::LoadDefaultHierarchy() {
        // Load default monster hierarchy mappings
        // This would typically be loaded from a configuration file

        // Example mappings (monster ID -> hierarchy level)
        m_monsterHierarchy[1001] = MonsterHierarchyLevel::Minion;    // Basic Orc
        m_monsterHierarchy[1002] = MonsterHierarchyLevel::Elite;     // Orc Warrior
        m_monsterHierarchy[1003] = MonsterHierarchyLevel::Champion;  // Orc Captain
        m_monsterHierarchy[1004] = MonsterHierarchyLevel::Boss;      // Orc Chief

        std::cout << "[DEBUG] Loaded " << m_monsterHierarchy.size() << " default hierarchy entries" << std::endl;
    }

    // CMonsterSkillPool implementation
    CMonsterSkillPool::CMonsterSkillPool() {
        std::cout << "[DEBUG] CMonsterSkillPool created" << std::endl;
    }

    CMonsterSkillPool::~CMonsterSkillPool() {
        Shutdown();
        std::cout << "[DEBUG] CMonsterSkillPool destroyed" << std::endl;
    }

    bool CMonsterSkillPool::Initialize() {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        try {
            m_skills.clear();

            std::cout << "[INFO] CMonsterSkillPool initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] CMonsterSkillPool initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void CMonsterSkillPool::Shutdown() {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        m_skills.clear();

        std::cout << "[INFO] CMonsterSkillPool shutdown complete" << std::endl;
    }

    void CMonsterSkillPool::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_skillMutex);
        UpdateCooldowns(deltaTime);
    }

    bool CMonsterSkillPool::AddSkill(const MonsterSkill& skill) {
        if (skill.skillId == 0) {
            return false;
        }

        std::lock_guard<std::mutex> lock(m_skillMutex);

        if (m_skills.size() >= MAX_SKILLS) {
            return false;
        }

        // Check if skill already exists
        for (const auto& existingSkill : m_skills) {
            if (existingSkill.skillId == skill.skillId) {
                return false; // Skill already exists
            }
        }

        m_skills.push_back(skill);
        return true;
    }

    bool CMonsterSkillPool::RemoveSkill(uint32_t skillId) {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        auto it = std::remove_if(m_skills.begin(), m_skills.end(),
            [skillId](const MonsterSkill& skill) { return skill.skillId == skillId; });

        if (it != m_skills.end()) {
            m_skills.erase(it, m_skills.end());
            return true;
        }

        return false;
    }

    void CMonsterSkillPool::ClearSkills() {
        std::lock_guard<std::mutex> lock(m_skillMutex);
        m_skills.clear();
    }

    MonsterSkill* CMonsterSkillPool::GetSkill(uint32_t skillId) {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        for (auto& skill : m_skills) {
            if (skill.skillId == skillId) {
                return &skill;
            }
        }

        return nullptr;
    }

    MonsterSkill* CMonsterSkillPool::SelectRandomSkill() {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        std::vector<MonsterSkill*> availableSkills;
        for (auto& skill : m_skills) {
            if (skill.CanUse()) {
                availableSkills.push_back(&skill);
            }
        }

        if (availableSkills.empty()) {
            return nullptr;
        }

        static std::random_device rd;
        static std::mt19937 gen(rd());
        std::uniform_int_distribution<size_t> dis(0, availableSkills.size() - 1);

        return availableSkills[dis(gen)];
    }

    MonsterSkill* CMonsterSkillPool::SelectBestSkill(float targetDistance) const {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        MonsterSkill* bestSkill = nullptr;
        uint32_t highestLevel = 0;

        for (auto& skill : m_skills) {
            if (skill.CanUse() && IsSkillInRange(skill, targetDistance)) {
                if (skill.level > highestLevel) {
                    highestLevel = skill.level;
                    bestSkill = const_cast<MonsterSkill*>(&skill);
                }
            }
        }

        return bestSkill;
    }

    bool CMonsterSkillPool::CanUseSkill(uint32_t skillId) const {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        for (const auto& skill : m_skills) {
            if (skill.skillId == skillId) {
                return skill.CanUse();
            }
        }

        return false;
    }

    bool CMonsterSkillPool::UseSkill(uint32_t skillId) {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        for (auto& skill : m_skills) {
            if (skill.skillId == skillId && skill.CanUse()) {
                skill.Use();
                return true;
            }
        }

        return false;
    }

    std::vector<MonsterSkill> CMonsterSkillPool::GetAvailableSkills() const {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        std::vector<MonsterSkill> availableSkills;
        for (const auto& skill : m_skills) {
            if (skill.CanUse()) {
                availableSkills.push_back(skill);
            }
        }

        return availableSkills;
    }

    std::string CMonsterSkillPool::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_skillMutex);

        std::ostringstream oss;
        oss << "CMonsterSkillPool{";
        oss << "Skills:" << m_skills.size();
        oss << ", Available:" << GetAvailableSkills().size();
        oss << "}";
        return oss.str();
    }

    void CMonsterSkillPool::UpdateCooldowns(float deltaTime) {
        // Cooldowns are handled in MonsterSkill::CanUse() and MonsterSkill::GetRemainingCooldown()
        // This method could be used for additional cooldown processing if needed
    }

    bool CMonsterSkillPool::IsSkillInRange(const MonsterSkill& skill, float distance) const {
        return distance <= skill.range || skill.range == 0.0f; // 0.0f means unlimited range
    }

    // CMonsterAI implementation
    CMonsterAI::CMonsterAI() {
        std::cout << "[DEBUG] CMonsterAI created" << std::endl;
    }

    CMonsterAI::~CMonsterAI() {
        Shutdown();
        std::cout << "[DEBUG] CMonsterAI destroyed" << std::endl;
    }

    bool CMonsterAI::Initialize(CMonster* monster) {
        std::lock_guard<std::mutex> lock(m_aiMutex);

        if (!monster) {
            std::cout << "[ERROR] CMonsterAI: Cannot initialize with null monster" << std::endl;
            return false;
        }

        try {
            m_monster = monster;
            m_currentState = MonsterAIState::Idle;
            m_previousState = MonsterAIState::Idle;
            m_currentTarget = nullptr;

            m_lastStateChange = std::chrono::steady_clock::now();
            m_lastUpdate = m_lastStateChange;

            std::cout << "[INFO] CMonsterAI initialized successfully" << std::endl;
            return true;

        } catch (const std::exception& e) {
            std::cout << "[ERROR] CMonsterAI initialization failed: " << e.what() << std::endl;
            return false;
        }
    }

    void CMonsterAI::Shutdown() {
        std::lock_guard<std::mutex> lock(m_aiMutex);

        m_monster = nullptr;
        m_currentTarget = nullptr;
        m_currentState = MonsterAIState::Idle;

        std::cout << "[INFO] CMonsterAI shutdown complete" << std::endl;
    }

    void CMonsterAI::Update(float deltaTime) {
        std::lock_guard<std::mutex> lock(m_aiMutex);

        if (!m_monster) {
            return;
        }

        m_lastUpdate = std::chrono::steady_clock::now();

        // Update based on current state
        switch (m_currentState) {
            case MonsterAIState::Idle:
                UpdateIdleState(deltaTime);
                break;
            case MonsterAIState::Patrol:
                UpdatePatrolState(deltaTime);
                break;
            case MonsterAIState::Chasing:
                UpdateChasingState(deltaTime);
                break;
            case MonsterAIState::Combat:
                UpdateCombatState(deltaTime);
                break;
            case MonsterAIState::Returning:
                UpdateReturningState(deltaTime);
                break;
            case MonsterAIState::Stunned:
                UpdateStunnedState(deltaTime);
                break;
            case MonsterAIState::Fleeing:
                UpdateFleeingState(deltaTime);
                break;
            case MonsterAIState::Dead:
                // Dead monsters don't update
                break;
        }
    }

    void CMonsterAI::SetState(MonsterAIState state) {
        std::lock_guard<std::mutex> lock(m_aiMutex);

        if (m_currentState != state) {
            m_previousState = m_currentState;
            m_currentState = state;
            m_lastStateChange = std::chrono::steady_clock::now();

            std::cout << "[DEBUG] Monster AI state changed: "
                      << GetStateString() << std::endl;
        }
    }

    void CMonsterAI::ForceState(MonsterAIState state) {
        std::lock_guard<std::mutex> lock(m_aiMutex);

        m_previousState = m_currentState;
        m_currentState = state;
        m_lastStateChange = std::chrono::steady_clock::now();

        std::cout << "[DEBUG] Monster AI state forced: "
                  << GetStateString() << std::endl;
    }

    void CMonsterAI::SetTarget(_object_id* targetId) {
        std::lock_guard<std::mutex> lock(m_aiMutex);
        m_currentTarget = targetId;

        if (targetId && m_currentState == MonsterAIState::Idle) {
            SetState(MonsterAIState::Chasing);
        }
    }

    void CMonsterAI::ClearTarget() {
        std::lock_guard<std::mutex> lock(m_aiMutex);
        m_currentTarget = nullptr;

        if (m_currentState == MonsterAIState::Chasing || m_currentState == MonsterAIState::Combat) {
            SetState(MonsterAIState::Returning);
        }
    }

    std::string CMonsterAI::GetStateString() const {
        return MonsterManagementUtils::MonsterAIStateToString(m_currentState);
    }

    std::string CMonsterAI::GetDebugInfo() const {
        std::lock_guard<std::mutex> lock(m_aiMutex);

        std::ostringstream oss;
        oss << "CMonsterAI{";
        oss << "State:" << GetStateString();
        oss << ", HasTarget:" << (m_currentTarget ? "true" : "false");
        oss << ", AggroRange:" << m_aggroRange;
        oss << ", ChaseRange:" << m_chaseRange;
        oss << "}";
        return oss.str();
    }

    // State update methods
    void CMonsterAI::UpdateIdleState(float deltaTime) {
        // Look for nearby enemies
        _object_id* nearestEnemy = FindNearestEnemy();
        if (nearestEnemy && IsTargetInRange(nearestEnemy, m_aggroRange)) {
            SetTarget(nearestEnemy);
            SetState(MonsterAIState::Chasing);
        } else {
            // Occasionally switch to patrol
            static std::random_device rd;
            static std::mt19937 gen(rd());
            std::uniform_real_distribution<float> dis(0.0f, 1.0f);

            if (dis(gen) < 0.01f) { // 1% chance per update
                SetState(MonsterAIState::Patrol);
            }
        }
    }

    void CMonsterAI::UpdatePatrolState(float deltaTime) {
        // Look for nearby enemies
        _object_id* nearestEnemy = FindNearestEnemy();
        if (nearestEnemy && IsTargetInRange(nearestEnemy, m_aggroRange)) {
            SetTarget(nearestEnemy);
            SetState(MonsterAIState::Chasing);
            return;
        }

        // Continue patrolling for a while, then return to idle
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_lastStateChange).count();

        if (elapsed > 10) { // Patrol for 10 seconds
            SetState(MonsterAIState::Idle);
        }
    }

    void CMonsterAI::UpdateChasingState(float deltaTime) {
        if (!m_currentTarget) {
            SetState(MonsterAIState::Idle);
            return;
        }

        float distance = GetDistanceToTarget(m_currentTarget);

        if (distance <= 2.0f) { // Close enough for combat
            SetState(MonsterAIState::Combat);
        } else if (distance > m_chaseRange || ShouldReturnToSpawn()) {
            ClearTarget();
            SetState(MonsterAIState::Returning);
        }
        // Continue chasing
    }

    void CMonsterAI::UpdateCombatState(float deltaTime) {
        if (!m_currentTarget) {
            SetState(MonsterAIState::Idle);
            return;
        }

        float distance = GetDistanceToTarget(m_currentTarget);

        if (distance > 5.0f) { // Target moved away
            SetState(MonsterAIState::Chasing);
        } else if (ShouldReturnToSpawn()) {
            ClearTarget();
            SetState(MonsterAIState::Returning);
        }
        // Continue combat
    }

    void CMonsterAI::UpdateReturningState(float deltaTime) {
        // Check if we're back at spawn point
        // For now, just return to idle after a short time
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_lastStateChange).count();

        if (elapsed > 5) { // Return for 5 seconds
            SetState(MonsterAIState::Idle);
        }
    }

    void CMonsterAI::UpdateStunnedState(float deltaTime) {
        // Check if stun has worn off
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_lastStateChange).count();

        if (elapsed > 3) { // Stunned for 3 seconds
            SetState(m_previousState); // Return to previous state
        }
    }

    void CMonsterAI::UpdateFleeingState(float deltaTime) {
        // Check if we should stop fleeing
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - m_lastStateChange).count();

        if (elapsed > 5) { // Flee for 5 seconds
            SetState(MonsterAIState::Returning);
        }
    }

    // Utility methods
    bool CMonsterAI::IsTargetInRange(_object_id* target, float range) const {
        if (!target) return false;
        return GetDistanceToTarget(target) <= range;
    }

    float CMonsterAI::GetDistanceToTarget(_object_id* target) const {
        if (!target || !m_monster) return 999999.0f;

        // This would calculate actual distance between monster and target
        // For now, return a placeholder value
        return 10.0f;
    }

    _object_id* CMonsterAI::FindNearestEnemy() const {
        if (!m_monster) return nullptr;

        // This would search for the nearest enemy within aggro range
        // For now, return nullptr
        return nullptr;
    }

    bool CMonsterAI::ShouldReturnToSpawn() const {
        if (!m_monster) return true;

        // Check if monster is too far from spawn point
        // For now, return false
        return false;
    }

    // Utility functions
    namespace MonsterManagementUtils {
        std::string MonsterHierarchyLevelToString(MonsterHierarchyLevel level) {
            switch (level) {
                case MonsterHierarchyLevel::Minion: return "Minion";
                case MonsterHierarchyLevel::Elite: return "Elite";
                case MonsterHierarchyLevel::Champion: return "Champion";
                case MonsterHierarchyLevel::Boss: return "Boss";
                case MonsterHierarchyLevel::WorldBoss: return "WorldBoss";
                case MonsterHierarchyLevel::RaidBoss: return "RaidBoss";
                default: return "Unknown";
            }
        }

        std::string MonsterAIStateToString(MonsterAIState state) {
            switch (state) {
                case MonsterAIState::Idle: return "Idle";
                case MonsterAIState::Patrol: return "Patrol";
                case MonsterAIState::Chasing: return "Chasing";
                case MonsterAIState::Combat: return "Combat";
                case MonsterAIState::Returning: return "Returning";
                case MonsterAIState::Dead: return "Dead";
                case MonsterAIState::Stunned: return "Stunned";
                case MonsterAIState::Fleeing: return "Fleeing";
                default: return "Unknown";
            }
        }

        float CalculateDistance(const float* pos1, const float* pos2) {
            if (!pos1 || !pos2) return 0.0f;

            float dx = pos1[0] - pos2[0];
            float dy = pos1[1] - pos2[1];
            float dz = pos1[2] - pos2[2];

            return std::sqrt(dx * dx + dy * dy + dz * dz);
        }

        bool IsValidObjectId(const _object_id* id) {
            return id != nullptr && id->IsValid();
        }

        uint32_t GenerateRandomValue(uint32_t min, uint32_t max) {
            static std::random_device rd;
            static std::mt19937 gen(rd());
            std::uniform_int_distribution<uint32_t> dis(min, max);
            return dis(gen);
        }

        float GenerateRandomFloat(float min, float max) {
            static std::random_device rd;
            static std::mt19937 gen(rd());
            std::uniform_real_distribution<float> dis(min, max);
            return dis(gen);
        }
    }

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {
    void CLootingMgr_Constructor(CLootingMgr_Legacy* mgr) {
        if (mgr) {
            mgr->vfptr = nullptr;
            std::cout << "[DEBUG] Legacy CLootingMgr constructed" << std::endl;
        }
    }

    void CLootingMgr_Destructor(CLootingMgr_Legacy* mgr) {
        if (mgr) {
            std::cout << "[DEBUG] Legacy CLootingMgr destructed" << std::endl;
        }
    }

    bool CLootingMgr_Initialize(CLootingMgr_Legacy* mgr) {
        if (mgr) {
            std::cout << "[DEBUG] Legacy CLootingMgr initialize called" << std::endl;
            return true;
        }
        return false;
    }

    void CLootingMgr_GenerateLoot(CLootingMgr_Legacy* mgr, uint32_t monsterId, uint32_t level) {
        if (mgr) {
            std::cout << "[DEBUG] Legacy CLootingMgr generate loot called for monster "
                      << monsterId << " level " << level << std::endl;
        }
    }

    void CMonsterAggroMgr_Constructor(CMonsterAggroMgr_Legacy* mgr) {
        if (mgr) {
            mgr->vfptr = nullptr;
            std::cout << "[DEBUG] Legacy CMonsterAggroMgr constructed" << std::endl;
        }
    }

    void CMonsterAggroMgr_Destructor(CMonsterAggroMgr_Legacy* mgr) {
        if (mgr) {
            std::cout << "[DEBUG] Legacy CMonsterAggroMgr destructed" << std::endl;
        }
    }

    void CMonsterAggroMgr_AddAggro(CMonsterAggroMgr_Legacy* mgr, _object_id* target, uint32_t value) {
        if (mgr && target) {
            std::cout << "[DEBUG] Legacy CMonsterAggroMgr add aggro called: "
                      << value << " to target " << target->dwSerial << std::endl;
        }
    }

    _object_id* CMonsterAggroMgr_GetPrimaryTarget(CMonsterAggroMgr_Legacy* mgr) {
        if (mgr) {
            std::cout << "[DEBUG] Legacy CMonsterAggroMgr get primary target called" << std::endl;
        }
        return nullptr;
    }
}
