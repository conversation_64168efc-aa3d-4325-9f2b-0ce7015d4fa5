/*
 * Function: ?combine_ex_using_material@CMgrAvatorItemHistory@@QEAAXHKEPEAPEAU_db_con@_STORAGE_LIST@@PEAEKPEADHK@Z
 * Address: 0x14023D440
 */

void __fastcall CMgrAvatorItemHistory::combine_ex_using_material(CMgrAvatorItemHistory *this, int n, unsigned int dwCheckKey, char bySlot<PERSON>um, _STORAGE_LIST::_db_con **ppMaterial, char *pbyMtrNum, unsigned int dwFee, char *strFileName, int bSucc, unsigned int dwFailCount)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  int v12; // eax@15
  int v13; // eax@16
  __int64 v14; // [sp+0h] [bp-78h]@1
  int v15; // [sp+20h] [bp-58h]@8
  __int64 v16; // [sp+28h] [bp-50h]@8
  char *v17; // [sp+30h] [bp-48h]@8
  char *v18; // [sp+38h] [bp-40h]@8
  unsigned int v19; // [sp+40h] [bp-38h]@9
  unsigned __int16 v20; // [sp+50h] [bp-28h]@7
  int j; // [sp+54h] [bp-24h]@10
  char *v22; // [sp+58h] [bp-20h]@12
  _base_fld *v23; // [sp+60h] [bp-18h]@13
  CMgrAvatorItemHistory *v24; // [sp+80h] [bp+8h]@1
  unsigned int dwCombineExCheckKey; // [sp+90h] [bp+18h]@1
  char v26; // [sp+98h] [bp+20h]@1

  v26 = bySlotNum;
  dwCombineExCheckKey = dwCheckKey;
  v24 = this;
  v10 = &v14;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  if ( ppMaterial && pbyMtrNum )
  {
    v20 = GetExcelIndexFromCombineExCheckKey(dwCheckKey);
    if ( bSucc )
    {
      v18 = v24->m_szCurTime;
      v17 = v24->m_szCurDate;
      LODWORD(v16) = dwFee;
      v15 = (unsigned __int8)v26;
      sprintf(
        sData,
        "\r\nCOMBINE_EX[CONSUME]\r\n\tCombine%d@%d,  num:%d, (D:%d) [%s %s] : Succ \r\n",
        (unsigned int)v20 + 1,
        dwCombineExCheckKey);
    }
    else
    {
      v19 = dwFailCount;
      v18 = v24->m_szCurTime;
      v17 = v24->m_szCurDate;
      LODWORD(v16) = dwFee;
      v15 = (unsigned __int8)v26;
      sprintf(
        sData,
        "\r\nCOMBINE_EX[CONSUME]\r\n\tCombine%d@%d,  num:%d, (D:%d) [%s %s] : Fail( %u ) \r\n",
        (unsigned int)v20 + 1,
        dwCombineExCheckKey);
    }
    for ( j = 0; j < (unsigned __int8)v26; ++j )
    {
      v22 = &ppMaterial[j]->m_bLoad;
      if ( v22 )
      {
        v23 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v22[1], *(_WORD *)(v22 + 3));
        if ( v23 )
        {
          if ( (signed int)(unsigned __int8)pbyMtrNum[j] <= 0 )
          {
            v13 = (unsigned __int8)pbyMtrNum[j];
            v16 = *((_QWORD *)v22 + 3);
            v15 = v13;
            sprintf(sBuf, "\t - %s_%u [%u] [%I64u]\r\n", v23->m_strCode, *(_QWORD *)(v22 + 5));
          }
          else
          {
            v12 = (unsigned __int8)pbyMtrNum[j];
            v16 = *((_QWORD *)v22 + 3);
            v15 = v12;
            sprintf(sBuf, "\t - %s_%u [%u] [%I64u] Delete \r\n", v23->m_strCode, *(_QWORD *)(v22 + 5));
          }
          strcat_0(sData, sBuf);
        }
      }
    }
    CMgrAvatorItemHistory::WriteFile(v24, strFileName, sData);
  }
}
