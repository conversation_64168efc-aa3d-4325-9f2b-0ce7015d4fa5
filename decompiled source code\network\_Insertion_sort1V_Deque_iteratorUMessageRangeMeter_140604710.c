/*
 * Function: ??$_Insertion_sort1@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@UMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@0PEAUMessageRange@MeterFilter@CryptoPP@@@Z
 * Address: 0x140604710
 */

int __fastcall std::_Insertion_sort1<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,CryptoPP::MeterFilter::MessageRange>(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@3
  const void *v3; // rax@4
  __int64 v4; // rax@4
  char *v5; // rax@5
  __int64 v6; // rax@7
  const void *v7; // rax@8
  char *v8; // rax@8
  char *v9; // rax@9
  char v11; // [sp+20h] [bp-188h]@2
  char v12; // [sp+40h] [bp-168h]@4
  char v13; // [sp+60h] [bp-148h]@4
  char v14; // [sp+78h] [bp-130h]@6
  char v15; // [sp+98h] [bp-110h]@5
  char *v16; // [sp+B8h] [bp-F0h]@5
  char v17; // [sp+C0h] [bp-E8h]@5
  char *v18; // [sp+E0h] [bp-C8h]@5
  char v19; // [sp+E8h] [bp-C0h]@5
  char *v20; // [sp+108h] [bp-A0h]@5
  char v21; // [sp+110h] [bp-98h]@5
  char v22; // [sp+130h] [bp-78h]@8
  char v23; // [sp+148h] [bp-60h]@4
  __int64 v24; // [sp+160h] [bp-48h]@1
  __int64 v25; // [sp+168h] [bp-40h]@5
  __int64 v26; // [sp+170h] [bp-38h]@5
  __int64 v27; // [sp+178h] [bp-30h]@5
  __int64 v28; // [sp+180h] [bp-28h]@5
  __int64 v29; // [sp+188h] [bp-20h]@5
  __int64 v30; // [sp+1B8h] [bp+10h]@1

  v30 = a2;
  v24 = -2i64;
  if ( std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator!=(
         a1,
         a2) )
  {
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v11);
    while ( 1 )
    {
      v2 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++((__int64)&v11);
      if ( !std::_Deque_const_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator!=(
              v2,
              v30) )
        break;
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v12);
      LODWORD(v3) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
      qmemcpy(&v23, v3, 0x18ui64);
      qmemcpy(&v13, &v23, 0x18ui64);
      LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
      if ( CryptoPP::MeterFilter::MessageRange::operator<((__int64)&v13, v4) )
      {
        v16 = &v15;
        v18 = &v17;
        v20 = &v19;
        std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator++((__int64)&v12);
        v25 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v16);
        v26 = v25;
        v27 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v18);
        v28 = v27;
        v29 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v20);
        stdext::unchecked_copy_backward<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>>(
          &v21,
          v29,
          v28,
          v26);
        std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
        LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
        qmemcpy(v5, &v13, 0x18ui64);
      }
      else
      {
        std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)&v14);
        while ( 1 )
        {
          std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator--((__int64)&v14);
          LODWORD(v6) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
          if ( !CryptoPP::MeterFilter::MessageRange::operator<((__int64)&v13, v6) )
            break;
          LODWORD(v7) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
          qmemcpy(&v22, v7, 0x18ui64);
          LODWORD(v8) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
          qmemcpy(v8, &v22, 0x18ui64);
          std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator=((__int64)&v12);
        }
        std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
        LODWORD(v9) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
        qmemcpy(v9, &v13, 0x18ui64);
      }
      std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
    }
    std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  }
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
