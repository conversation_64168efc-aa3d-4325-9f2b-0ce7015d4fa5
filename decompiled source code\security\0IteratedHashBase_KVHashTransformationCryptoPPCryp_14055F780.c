/*
 * Function: ??0?$IteratedHashBase@_KVHashTransformation@CryptoPP@@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14055F780
 */

CryptoPP::HashTransformation *__fastcall CryptoPP::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>::IteratedHashBase<unsigned __int64,CryptoPP::HashTransformation>(CryptoPP::HashTransformation *a1, CryptoPP::HashTransformation *a2)
{
  CryptoPP::HashTransformation *v3; // [sp+30h] [bp+8h]@1
  CryptoPP::HashTransformation *__that; // [sp+38h] [bp+10h]@1

  __that = a2;
  v3 = a1;
  CryptoPP::HashTransformation::HashTransformation(a1, a2);
  v3[1].vfptr = __that[1].vfptr;
  v3[2].vfptr = __that[2].vfptr;
  return v3;
}
