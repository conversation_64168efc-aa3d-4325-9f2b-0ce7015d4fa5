/*
 * Function: ?UpdateClearDanglingOwnerRecord@CUnmannedTraderController@@IEAA_NXZ
 * Address: 0x1403505B0
 */

char __fastcall CUnmannedTraderController::UpdateClearDanglingOwnerRecord(CUnmannedTraderController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUnmannedTraderController *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFWorldDatabase::Update_UnmannedTraderClearDanglingOwnerRecord(pkDB) )
  {
    result = 1;
  }
  else
  {
    CUnmannedTraderController::Log(
      v5,
      "CUnmannedTraderController::UpdateClearDanglingOwnerRecord()\r\n"
      "\t\tg_Main.m_pWorldDB->Update_UnmannedTraderClearDanglingOwnerRecord() Fail!\r\n");
    result = 0;
  }
  return result;
}
