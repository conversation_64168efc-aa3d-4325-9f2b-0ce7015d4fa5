/*
 * Function: j_??$_Uninit_copy@PEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@V?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@std@@@std@@YAPEAPEAVCRaceBuffInfoByHolyQuestfGroup@@PEAPEAV1@00AEAV?$allocator@PEAVCRaceBuffInfoByHolyQuestfGroup@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x140005853
 */

CRaceBuffInfoByHolyQuestfGroup **__fastcall std::_Uninit_copy<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(CRaceBuffInfoByHolyQuestfGroup **_First, CRaceBuffInfoByHolyQuestfGroup **_Last, CRaceBuffInfoByHolyQuestfGroup **_Dest, std::allocator<CRaceBuffInfoByHolyQuestfGroup *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CRaceBuffInfoByHolyQuestfGroup * *,CRaceBuffInfoByHolyQuestfGroup * *,std::allocator<CRaceBuffInfoByHolyQuestfGroup *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
