/*
 * Function: ?IsolatedInitialize@ByteQueue@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x1405491C0
 */

void __fastcall CryptoPP::ByteQueue::IsolatedInitialize(CryptoPP::ByteQueue *this, const struct CryptoPP::NameValuePairs *a2)
{
  CryptoPP::ByteQueue *v2; // [sp+30h] [bp+8h]@1

  v2 = this;
  this->m_nodeSize = (signed int)CryptoPP::NameValuePairs::GetIntValueWithDefault(
                                   (CryptoPP::NameValuePairs *)a2,
                                   "NodeSize",
                                   256);
  CryptoPP::ByteQueue::Clear(v2);
}
