/*
 * Function: SQLGetPrivateProfileString
 * Address: 0x1404DAA50
 */

int __fastcall SQLGetPrivateProfileString(const char *lpszSection, const char *lpszEntry, const char *lpszDefault, char *lpszRetBuffer, int cbRetBuffer, const char *lpszFilename)
{
  const char *v6; // rbp@1
  char *v7; // rbx@1
  const char *v8; // rdi@1
  const char *v9; // rsi@1
  __int64 (__cdecl *v10)(); // rax@1
  int result; // eax@2

  v6 = lpszSection;
  v7 = lpszRetBuffer;
  v8 = lpszDefault;
  v9 = lpszEntry;
  v10 = ODBC___GetSetupProc("SQLGetPrivateProfileString");
  if ( v10 )
    result = ((int (__fastcall *)(const char *, const char *, const char *, char *))v10)(v6, v9, v8, v7);
  else
    result = 0;
  return result;
}
