/*
 * Function: ?pc_GuildEstablishRequest@CPlayer@@QEAAXPEAD@Z
 * Address: 0x1400A6D10
 */

void __usercall CPlayer::pc_GuildEstablishRequest(CPlayer *this@<rcx>, char *pwsz<PERSON><PERSON><PERSON>ame@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v5; // rax@12
  char *v6; // rax@15
  unsigned int v7; // eax@24
  int v8; // eax@43
  char v9; // al@45
  char *v10; // rax@45
  int v11; // eax@75
  unsigned int v12; // eax@81
  CMoneySupplyMgr *v13; // rax@85
  unsigned int v14; // eax@86
  __int64 v15; // [sp+0h] [bp-438h]@1
  char *pQryData; // [sp+20h] [bp-418h]@15
  int k; // [sp+30h] [bp-408h]@29
  char v18; // [sp+34h] [bp-404h]@4
  unsigned int v19; // [sp+38h] [bp-400h]@4
  char v20; // [sp+3Ch] [bp-3FCh]@4
  int Dst[16]; // [sp+48h] [bp-3F0h]@4
  int Src[16]; // [sp+88h] [bp-3B0h]@4
  int v23[16]; // [sp+C8h] [bp-370h]@4
  char v24[32]; // [sp+108h] [bp-330h]@4
  char v25[40]; // [sp+128h] [bp-310h]@4
  char Dest[148]; // [sp+150h] [bp-2E8h]@4
  int v27; // [sp+1E4h] [bp-254h]@4
  CPartyPlayer **v28; // [sp+1E8h] [bp-250h]@4
  CGuild *v29; // [sp+1F0h] [bp-248h]@4
  char *j; // [sp+1F8h] [bp-240h]@6
  CPlayer *v31; // [sp+200h] [bp-238h]@32
  char v32; // [sp+208h] [bp-230h]@75
  _qry_case_insertguild v33; // [sp+220h] [bp-218h]@75
  CPlayer *v34; // [sp+348h] [bp-F0h]@80
  int v35; // [sp+350h] [bp-E8h]@81
  char szTran; // [sp+370h] [bp-C8h]@86
  unsigned int v37; // [sp+400h] [bp-38h]@24
  int v38; // [sp+404h] [bp-34h]@43
  unsigned int nAmount; // [sp+408h] [bp-30h]@85
  int nLv; // [sp+40Ch] [bp-2Ch]@85
  int v41; // [sp+410h] [bp-28h]@85
  char *pszFileName; // [sp+418h] [bp-20h]@86
  unsigned int dwLeftDalant; // [sp+420h] [bp-18h]@86
  unsigned __int64 v44; // [sp+428h] [bp-10h]@4
  CPlayer *v45; // [sp+440h] [bp+8h]@1
  char *Str; // [sp+448h] [bp+10h]@1

  Str = pwszGuildName;
  v45 = this;
  v3 = &v15;
  for ( i = 268i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v44 = (unsigned __int64)&v15 ^ _security_cookie;
  v18 = 0;
  v19 = -1;
  v20 = 0;
  v27 = strlen_0(pwszGuildName);
  memset_0(Dst, -1, 0x20ui64);
  memset_0(Src, -1, 0x20ui64);
  memset_0(v23, 0, 0x20ui64);
  memset_0(v24, 0, 8ui64);
  memset_0(v25, 0, 8ui64);
  memset_0(Dest, 0, 0x88ui64);
  v28 = CPartyPlayer::GetPtrPartyMember(v45->m_pPartyMgr);
  v29 = 0i64;
  if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v45->m_id.wIndex) == 99 )
  {
    v18 = 106;
    goto $RESULT_24;
  }
  for ( j = Str; ; ++j )
  {
    if ( *j == 32 || *j == 39 )
    {
      v18 = 15;
      goto $RESULT_24;
    }
    if ( !*j )
      break;
  }
  v5 = CTSingleton<CNationSettingManager>::Instance();
  if ( !CNationSettingManager::IsNormalString(v5, Str) )
  {
    v18 = 15;
    goto $RESULT_24;
  }
  if ( !IsSQLValidString(Str) )
  {
    v6 = CPlayerDB::GetCharNameA(&v45->m_Param);
    pQryData = Str;
    CLogFile::Write(
      &stru_1799C8E78,
      "CPlayer::pc_GuildEstablishRequest() : %u(%s) ::IsSQLValidString(pwszGuildName(%s)) Invalid!",
      v45->m_dwObjSerial,
      v6);
    v18 = 15;
    goto $RESULT_24;
  }
  if ( CPlayer::IsPunished(v45, 1, 1) )
    return;
  if ( !CPartyPlayer::IsPartyMode(v45->m_pPartyMgr) )
  {
    v18 = 1;
    goto $RESULT_24;
  }
  if ( !CPartyPlayer::IsPartyBoss(v45->m_pPartyMgr) )
  {
    v18 = 2;
    goto $RESULT_24;
  }
  if ( CMainThread::IsReleaseServiceMode(&g_Main) && v45->m_byUserDgr )
  {
    v18 = 13;
    goto $RESULT_24;
  }
  v37 = CPlayerDB::GetDalant(&v45->m_Param);
  v7 = GuildCreateEventInfo::GetEstConsumeDalant(&stru_1799CA220);
  if ( v37 < v7 )
  {
    v18 = 12;
    goto $RESULT_24;
  }
  v29 = GetEmptyGuildData(g_Guild, 500);
  if ( !v29 )
  {
    v18 = 10;
    goto $RESULT_24;
  }
  if ( !v28 )
  {
    v18 = 1;
    goto $RESULT_24;
  }
  for ( k = 0; k < 8 && v28[k]; ++k )
  {
    v31 = &g_Player + v28[k]->m_wZoneIndex;
    if ( !v31->m_bLive )
    {
      v18 = 9;
      goto $RESULT_24;
    }
    if ( CPlayer::IsPunished(v31, 1, 1) )
    {
      v18 = 70;
      goto $RESULT_24;
    }
    if ( v31->m_Param.m_pGuild )
    {
      v18 = 7;
      goto $RESULT_24;
    }
    if ( v31->m_Param.m_pApplyGuild )
    {
      v18 = 8;
      goto $RESULT_24;
    }
    if ( v31->m_Param.m_bGuildLock )
    {
      v18 = 7;
      goto $RESULT_24;
    }
    if ( !v31->m_Param.m_pClassHistory[0] )
    {
      v18 = 11;
      goto $RESULT_24;
    }
    v38 = CPlayerDB::GetRaceCode(&v31->m_Param);
    v8 = CPlayerDB::GetRaceCode(&v45->m_Param);
    if ( v38 != v8 )
    {
      v18 = 70;
      goto $RESULT_24;
    }
    Dst[k] = v31->m_id.wIndex;
    Src[k] = v31->m_id.dwSerial;
    CPlayerDB::GetPvPPoint(&v31->m_Param);
    v23[k] = (signed int)floor(a3);
    v9 = CPlayerDB::GetLevel(&v31->m_Param);
    v24[k] = v9;
    v25[k] = v31->m_Param.m_byPvPGrade;
    v10 = CPlayerDB::GetCharNameW(&v31->m_Param);
    strcpy_0(&Dest[17 * k], v10);
    ++v20;
  }
  if ( CMainThread::IsReleaseServiceMode(&g_Main) )
  {
    if ( v45->m_byUserDgr )
    {
      v18 = 13;
      goto $RESULT_24;
    }
    if ( (signed int)(unsigned __int8)v20 < 8 )
    {
      v18 = 9;
      goto $RESULT_24;
    }
LABEL_58:
    for ( k = 0; k < 500; ++k )
    {
      if ( (CGuild::IsFill(&g_Guild[k]) || g_Guild[k].m_bDBWait) && !strcmp_0(Str, g_Guild[k].m_wszName) )
      {
        v18 = 4;
        goto $RESULT_24;
      }
    }
    for ( k = 0; k < 500; ++k )
    {
      if ( !CGuild::IsFill(&g_Guild[k]) && !g_Guild[k].m_bDBWait )
      {
        g_Guild[k].m_nIndex = k;
        CGuild::SetTemp(&g_Guild[k], Str);
        v19 = k;
        break;
      }
    }
    if ( v19 == -1 )
      v18 = 10;
    goto $RESULT_24;
  }
  if ( v45->m_byUserDgr || (signed int)(unsigned __int8)v20 >= 8 )
    goto LABEL_58;
  v18 = 9;
$RESULT_24:
  if ( v18 )
  {
    CPlayer::SendMsg_GuildEstablishFail(v45, v18);
  }
  else
  {
    v32 = 1;
    v33.in_guildindex = v19;
    v33.tmp_Esterindex = v45->m_id.wIndex;
    v33.tmp_Esterserial = v45->m_id.dwSerial;
    v33.in_guildRace = CPlayerDB::GetRaceCode(&v45->m_Param);
    v33.in_membernum = v20;
    strcpy_0(v33.in_w_guildName, Str);
    memcpy_0(v33.in_memberindex, Dst, 0x20ui64);
    memcpy_0(v33.in_memberserial, Src, 0x20ui64);
    memcpy_0(v33.tmp_pvp, v23, 0x20ui64);
    memcpy_0(v33.tmp_lv, v24, 8ui64);
    memcpy_0(v33.tmp_grade, v25, 8ui64);
    memcpy_0(v33.tmp_w_membername, Dest, 0x88ui64);
    v11 = _qry_case_insertguild::size(&v33);
    if ( !CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 15, (char *)&v33, v11) )
      v32 = 0;
    if ( v32 )
    {
      CGuild::SetTemp(v29, Str);
      for ( k = 0; k < (unsigned __int8)v20; ++k )
      {
        v34 = &g_Player + v28[k]->m_wZoneIndex;
        v34->m_Param.m_bGuildLock = 1;
      }
      v12 = GuildCreateEventInfo::GetEstConsumeDalant(&stru_1799CA220);
      CPlayer::SubDalant(v45, v12);
      v35 = CPlayerDB::GetLevel(&v45->m_Param);
      if ( v35 == 30 || v35 == 40 || v35 == 50 || v35 == 60 )
      {
        nAmount = GuildCreateEventInfo::GetEstConsumeDalant(&stru_1799CA220);
        nLv = CPlayerDB::GetLevel(&v45->m_Param);
        v41 = CPlayerDB::GetRaceCode(&v45->m_Param);
        v13 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v13, v41, nLv, nAmount);
      }
      W2M(Str, &szTran, 0x80u);
      pszFileName = v45->m_szItemHistoryFileName;
      dwLeftDalant = CPlayerDB::GetDalant(&v45->m_Param);
      v14 = GuildCreateEventInfo::GetEstConsumeDalant(&stru_1799CA220);
      CMgrAvatorItemHistory::guild_est_money(
        &CPlayer::s_MgrItemHistory,
        v45->m_ObjID.m_wIndex,
        &szTran,
        v14,
        dwLeftDalant,
        pszFileName);
    }
    else
    {
      v18 = -1;
    }
  }
}
