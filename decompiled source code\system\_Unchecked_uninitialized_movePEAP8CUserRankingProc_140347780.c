/*
 * Function: ??$_Unchecked_uninitialized_move@PEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@stdext@@YAPEAP8CUserRankingProcess@@EAAXXZPEAP81@EAAXXZ00AEAV?$allocator@P8CUserRankingProcess@@EAAXXZ@std@@@Z
 * Address: 0x140347780
 */

void (__cdecl **__fastcall stdext::_Unchecked_uninitialized_move<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>>(void (__cdecl **_First)(CUserRankingProcess *this), void (__cdecl **_Last)(CUserRankingProcess *this), void (__cdecl **_Dest)(CUserRankingProcess *this), std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Al))(CUserRankingProcess *this)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Undefined_move_tag v9; // [sp+31h] [bp-17h]@4
  void (__cdecl **_Firsta)(CUserRankingProcess *); // [sp+50h] [bp+8h]@1
  void (__cdecl **_Lasta)(CUserRankingProcess *); // [sp+58h] [bp+10h]@1
  void (__cdecl **__formal)(CUserRankingProcess *); // [sp+60h] [bp+18h]@1
  std::allocator<void (__cdecl CUserRankingProcess::*)(void)> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  __formal = _Dest;
  _Lasta = _Last;
  _Firsta = _First;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Move_cat<void (CUserRankingProcess::**)(void)>(&__formal);
  return std::_Uninit_move<void (CUserRankingProcess::**)(void),void (CUserRankingProcess::**)(void),std::allocator<void (CUserRankingProcess::*)(void)>,std::_Undefined_move_tag>(
           _Firsta,
           _Lasta,
           __formal,
           _Ala,
           v9,
           v8);
}
