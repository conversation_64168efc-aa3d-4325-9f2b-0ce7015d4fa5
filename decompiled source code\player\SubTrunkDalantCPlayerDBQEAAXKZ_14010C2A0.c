/*
 * Function: ?SubTrunkDalant@CPlayerDB@@QEAAXK@Z
 * Address: 0x14010C2A0
 */

void __fastcall CPlayerDB::SubTrunkDalant(CPlayerDB *this, unsigned int dwSub)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  double v4; // [sp+0h] [bp-18h]@1
  CPlayerDB *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v2 = (__int64 *)&v4;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = v5->m_dTrunkDalant - (double)(signed int)dwSub;
  if ( (double)(signed int)dwSub > v5->m_dTrunkDalant )
    v4 = 0.0;
  v5->m_dTrunkDalant = v4;
}
