/*
 * Function: j_??$_Unchecked_uninitialized_move@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@stdext@@YAPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@PEAV12@00AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@Z
 * Address: 0x14000D305
 */

std::basic_string<char,std::char_traits<char>,std::allocator<char> > *__fastcall stdext::_Unchecked_uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_First, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Last, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Dest, std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Al)
{
  return stdext::_Unchecked_uninitialized_move<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
