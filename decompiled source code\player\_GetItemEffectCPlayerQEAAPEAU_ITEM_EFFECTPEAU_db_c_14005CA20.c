/*
 * Function: ?_GetItemEffect@CPlayer@@QEAAPEAU_ITEM_EFFECT@@PEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14005CA20
 */

_ITEM_EFFECT *__fastcall CPlayer::_GetItemEffect(CPlayer *this, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _ITEM_EFFECT *result; // rax@6
  __int64 v5; // [sp+0h] [bp-68h]@1
  _base_fld *v6; // [sp+20h] [bp-48h]@5
  _base_fld *v7; // [sp+28h] [bp-40h]@9
  _base_fld *v8; // [sp+30h] [bp-38h]@12
  _base_fld *v9; // [sp+38h] [bp-30h]@15
  _base_fld *v10; // [sp+40h] [bp-28h]@18
  _base_fld *v11; // [sp+48h] [bp-20h]@21
  _base_fld *v12; // [sp+50h] [bp-18h]@24
  int v13; // [sp+58h] [bp-10h]@8

  v2 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pItem->m_byTableCode >= 5 )
  {
    v13 = pItem->m_byTableCode;
    v13 -= 5;
    switch ( v13 )
    {
      case 1:
        v7 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
        if ( v7 )
          result = (_ITEM_EFFECT *)&v7[10].m_strCode[28];
        else
          result = 0i64;
        break;
      case 0:
        v8 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
        if ( v8 )
          result = (_ITEM_EFFECT *)&v8[6];
        else
          result = 0i64;
        break;
      case 2:
        v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
        if ( v9 )
          result = (_ITEM_EFFECT *)&v9[6];
        else
          result = 0i64;
        break;
      case 3:
        v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
        if ( v10 )
          result = (_ITEM_EFFECT *)v10[5].m_strCode;
        else
          result = 0i64;
        break;
      case 4:
        v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
        if ( v11 )
          result = (_ITEM_EFFECT *)v11[5].m_strCode;
        else
          result = 0i64;
        break;
      case 22:
        v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
        if ( v12 )
          result = (_ITEM_EFFECT *)&v12[5].m_strCode[28];
        else
          result = 0i64;
        break;
      default:
        result = 0i64;
        break;
    }
  }
  else
  {
    v6 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pItem->m_byTableCode, pItem->m_wItemIndex);
    if ( v6 )
      result = (_ITEM_EFFECT *)&v6[6];
    else
      result = 0i64;
  }
  return result;
}
