/*
 * Function: j_??1?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@QEAA@XZ
 * Address: 0x14000CA36
 */

void __fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this)
{
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(this);
}
