/*
 * Function: _std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::operator__::_1_::dtor$1
 * Address: 0x1402C5DE0
 */

void __fastcall std::_Vector_iterator_CLogTypeDBTask_____ptr64_std::allocator_CLogTypeDBTask_____ptr64___::operator__::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 68) & 1 )
  {
    *(_DWORD *)(a2 + 68) &= 0xFFFFFFFE;
    std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::~_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>(*(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > **)(a2 + 104));
  }
}
