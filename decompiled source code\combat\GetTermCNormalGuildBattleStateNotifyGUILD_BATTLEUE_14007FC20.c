/*
 * Function: ?GetTerm@CNormalGuildBattleStateNotify@GUILD_BATTLE@@UEAA?AVCTimeSpan@ATL@@XZ
 * Address: 0x14007FC20
 */

ATL::CTimeSpan *__fastcall GUILD_BATTLE::CNormalGuildBattleStateNotify::GetTerm(GUILD_BATTLE::CNormalGuildBattleStateNotify *this, ATL::CTimeSpan *result)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  ATL::CTimeSpan *v6; // [sp+48h] [bp+10h]@1

  v6 = result;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  ATL::CTimeSpan::CTimeSpan(result, 0, 0, 5, 0);
  return v6;
}
