/*
 * Function: ?UseRecallTeleportItemRequest@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401CBA50
 */

char __fastcall CNetworkEX::UseRecallTeleportItemRequest(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CNationSettingManager *v6; // rax@8
  CRecallEffectController *v7; // rax@9
  CRecallEffectController *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-78h]@1
  _STORAGE_POS_INDIV *pItem; // [sp+20h] [bp-58h]@4
  CPlayer *pkObj; // [sp+28h] [bp-50h]@4
  char _Dest[17]; // [sp+38h] [bp-40h]@8
  CPlayer *pTargetPlayer; // [sp+58h] [bp-20h]@10
  unsigned __int64 v14; // [sp+68h] [bp-10h]@4

  v3 = &v9;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v9 ^ _security_cookie;
  pItem = (_STORAGE_POS_INDIV *)pBuf;
  pkObj = &g_Player + n;
  if ( pkObj->m_bOper )
  {
    if ( pItem->byStorageCode )
      CPlayer::SendMsg_UsePotionResult(pkObj, 25, pItem->wItemSerial, 0);
    _Dest[0] = 0;
    memset(&_Dest[1], 0, 0x10ui64);
    strncpy_s<17>((char (*)[17])_Dest, &pItem[1].byStorageCode, 0x10ui64);
    v6 = CTSingleton<CNationSettingManager>::Instance();
    if ( CNationSettingManager::IsNormalString(v6, _Dest) )
    {
      pTargetPlayer = GetPtrPlayerFromName(&g_Player, 2532, _Dest);
      if ( pTargetPlayer )
      {
        CPlayer::pc_UsePotionItem(pkObj, pTargetPlayer, pItem);
        result = 1;
      }
      else
      {
        v8 = CRecallEffectController::Instance();
        CRecallEffectController::SendRecallReqeustResult(v8, 17, pkObj);
        result = 1;
      }
    }
    else
    {
      v7 = CRecallEffectController::Instance();
      CRecallEffectController::SendRecallReqeustResult(v7, 17, pkObj);
      result = 1;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
