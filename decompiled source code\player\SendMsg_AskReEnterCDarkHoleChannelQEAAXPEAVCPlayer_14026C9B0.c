/*
 * Function: ?SendMsg_AskReEnter@CDarkHoleChannel@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14026C9B0
 */

void __fastcall CDarkHoleChannel::SendMsg_AskReEnter(CDarkHoleChannel *this, CPlayer *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  _darkhole_ask_reenter_inform_zocl v6; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CDarkHoleChannel *v9; // [sp+80h] [bp+8h]@1
  CPlayer *v10; // [sp+88h] [bp+10h]@1

  v10 = pDst;
  v9 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6.wChannelIndex = v9->m_wChannelIndex;
  v6.dwChannelSerial = v9->m_dwChannelSerial;
  pbyType = 35;
  v8 = -47;
  v4 = _darkhole_ask_reenter_inform_zocl::size(&v6);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, (char *)&v6, v4);
}
