/*
 * Function: ?ActVote@CVoteSystem@@QEAA_NKE@Z
 * Address: 0x1402B0580
 */

char __fastcall CVoteSystem::ActVote(CVoteSystem *this, unsigned int dwAvatorSerial, char byPoint)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CVoteSystem *v7; // [sp+30h] [bp+8h]@1
  unsigned int dwIndex; // [sp+38h] [bp+10h]@1
  char v9; // [sp+40h] [bp+18h]@1

  v9 = byPoint;
  dwIndex = dwAvatorSerial;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( v7->m_bActive )
  {
    if ( CNetIndexList::IsInList(&v7->m_listVote, dwAvatorSerial) )
    {
      result = 0;
    }
    else
    {
      CNetIndexList::PushNode_Back(&v7->m_listVote, dwIndex);
      ++v7->m_dwPoint[(unsigned __int8)v9];
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
