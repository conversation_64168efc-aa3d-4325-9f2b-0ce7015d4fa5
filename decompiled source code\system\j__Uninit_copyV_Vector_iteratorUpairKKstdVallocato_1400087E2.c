/*
 * Function: j_??$_Uninit_copy@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@std@@PEAU?$pair@KK@2@V?$allocator@U?$pair@KK@std@@@2@@std@@YAPEAU?$pair@KK@0@V?$_Vector_iterator@U?$pair@KK@std@@V?$allocator@U?$pair@KK@std@@@2@@0@0PEAU10@AEAV?$allocator@U?$pair@KK@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400087E2
 */

std::pair<unsigned long,unsigned long> *__fastcall std::_Uninit_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_First, std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long> > > *_Last, std::pair<unsigned long,unsigned long> *_Dest, std::allocator<std::pair<unsigned long,unsigned long> > *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<std::_Vector_iterator<std::pair<unsigned long,unsigned long>,std::allocator<std::pair<unsigned long,unsigned long>>>,std::pair<unsigned long,unsigned long> *,std::allocator<std::pair<unsigned long,unsigned long>>>(
           _First,
           _Last,
           _Dest,
           _Al,
           __formal,
           a6);
}
