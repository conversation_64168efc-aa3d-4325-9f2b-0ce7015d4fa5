/*
 * Function: ?_db_Update_OreCutting@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEADH@Z
 * Address: 0x1401B6F60
 */

char __fastcall CMainThread::_db_Update_OreCutting(CMainThread *this, unsigned int dwSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *szOreCuttingQuery, int nSize)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  int v8; // eax@6
  size_t v9; // rax@11
  __int64 v11; // [sp+0h] [bp-F8h]@1
  unsigned int v12; // [sp+20h] [bp-D8h]@7
  char DstBuf; // [sp+40h] [bp-B8h]@4
  char v14; // [sp+41h] [bp-B7h]@4
  int v15; // [sp+C4h] [bp-34h]@4
  unsigned int j; // [sp+C8h] [bp-30h]@4
  int v17; // [sp+CCh] [bp-2Ch]@6
  unsigned int v18; // [sp+D0h] [bp-28h]@8
  unsigned __int64 v19; // [sp+E0h] [bp-18h]@4
  unsigned int v20; // [sp+108h] [bp+10h]@1
  _AVATOR_DATA *v21; // [sp+110h] [bp+18h]@1
  _AVATOR_DATA *v22; // [sp+118h] [bp+20h]@1

  v22 = pOldData;
  v21 = pNewData;
  v20 = dwSerial;
  v6 = &v11;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v19 = (unsigned __int64)&v11 ^ _security_cookie;
  DstBuf = 0;
  memset(&v14, 0, 0x7Fui64);
  sprintf_s(szOreCuttingQuery, nSize, "UPDATE tbl_OreCutting Set ");
  v15 = strlen_0(szOreCuttingQuery);
  for ( j = 0; (signed int)j < 20; ++j )
  {
    v17 = _INVENKEY::CovDBKey((_INVENKEY *)&v21->dbCutting.m_List[j]);
    v8 = _INVENKEY::CovDBKey((_INVENKEY *)&v22->dbCutting.m_List[j]);
    if ( v17 != v8 )
    {
      v12 = v17;
      sprintf_s(&DstBuf, 0x80ui64, "K%d=%d,", j);
      strcat_s(szOreCuttingQuery, nSize, &DstBuf);
    }
    v18 = v21->dbCutting.m_List[j].dwDur;
    if ( v18 != v22->dbCutting.m_List[j].dwDur )
    {
      v12 = v18;
      sprintf_s(&DstBuf, 0x80ui64, "D%d=%d,", j);
      strcat_s(szOreCuttingQuery, nSize, &DstBuf);
    }
  }
  v9 = strlen_0(szOreCuttingQuery);
  if ( v9 <= v15 )
  {
    memset_0(szOreCuttingQuery, 0, v15);
  }
  else
  {
    sprintf_s(&DstBuf, 0x80ui64, "WHERE Serial = %d", v20);
    szOreCuttingQuery[strlen_0(szOreCuttingQuery) - 1] = 32;
    strcat_s(szOreCuttingQuery, nSize, &DstBuf);
  }
  return 1;
}
