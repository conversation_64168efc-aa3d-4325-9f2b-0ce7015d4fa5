/*
 * Function: ?ElapsedTimeAsDouble@TimerBase@CryptoPP@@QEAANXZ
 * Address: 0x1406609A0
 */

double __fastcall CryptoPP::TimerBase::ElapsedTimeAsDouble(CryptoPP::TimerBase *this)
{
  double result; // xmm0_8@2
  unsigned __int64 v2; // rax@4
  CryptoPP::TimerBase *v3; // [sp+40h] [bp+8h]@1

  v3 = this;
  if ( *((_BYTE *)this + 12) )
  {
    result = 0.0;
  }
  else if ( *((_BYTE *)this + 13) )
  {
    LODWORD(v2) = (**(int (***)(void))this)();
    if ( *((_QWORD *)v3 + 3) < v2 )
      *((_QWORD *)v3 + 3) = v2;
    CryptoPP::TimerBase::ConvertTo((__int64)v3, *((_QWORD *)v3 + 3) - *((_QWORD *)v3 + 2), *((_DWORD *)v3 + 2));
  }
  else
  {
    CryptoPP::TimerBase::StartTimer(this);
    result = 0.0;
  }
  return result;
}
