/*
 * Function: ?Update@CUnmannedTraderScheduler@@QEAAXPEAU_unmannedtrader_reserved_schedule_info@@@Z
 * Address: 0x1403936B0
 */

void __fastcall CUnmannedTraderScheduler::Update(CUnmannedTraderScheduler *this, _unmannedtrader_reserved_schedule_info *pkInfo)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderSchedule *v4; // rax@9
  CUnmannedTraderSchedule *v5; // rax@9
  __int64 v6; // [sp+0h] [bp-98h]@1
  unsigned int dwOwnerSerial; // [sp+20h] [bp-78h]@9
  unsigned int dwK; // [sp+28h] [bp-70h]@9
  unsigned int j; // [sp+30h] [bp-68h]@7
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > result; // [sp+38h] [bp-60h]@10
  __int64 v11; // [sp+50h] [bp-48h]@4
  __int64 v12; // [sp+58h] [bp-40h]@9
  __int64 v13; // [sp+60h] [bp-38h]@9
  __int64 v14; // [sp+68h] [bp-30h]@9
  __int64 v15; // [sp+70h] [bp-28h]@9
  __int64 v16; // [sp+78h] [bp-20h]@9
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *v17; // [sp+80h] [bp-18h]@10
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__that; // [sp+88h] [bp-10h]@10
  CUnmannedTraderScheduler *v19; // [sp+A0h] [bp+8h]@1
  _unmannedtrader_reserved_schedule_info *v20; // [sp+A8h] [bp+10h]@1

  v20 = pkInfo;
  v19 = this;
  v2 = &v6;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = -2i64;
  if ( pkInfo->dwCnt
    && !std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::empty(&v19->m_veckSchdule) )
  {
    for ( j = 0; j < v20->dwCnt; ++j )
    {
      v4 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](
             &v19->m_veckSchdule,
             (signed int)j);
      CUnmannedTraderSchedule::Clear(v4);
      v12 = 24i64 * (signed int)j;
      v13 = 24i64 * (signed int)j;
      v14 = 24i64 * (signed int)j;
      v15 = 24i64 * (signed int)j;
      v16 = 24i64 * (signed int)j;
      v5 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator[](
             &v19->m_veckSchdule,
             (signed int)j);
      dwK = v20->list[(unsigned __int64)v12 / 0x18].dwK;
      dwOwnerSerial = v20->list[(unsigned __int64)v13 / 0x18].dwOwnerSerial;
      CUnmannedTraderSchedule::Set(
        v5,
        v20->list[(unsigned __int64)v16 / 0x18].byType,
        v20->list[(unsigned __int64)v15 / 0x18].dwItemSerial,
        v20->list[(unsigned __int64)v14 / 0x18].tEndTime,
        dwOwnerSerial,
        dwK);
    }
    v17 = std::vector<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::begin(
            &v19->m_veckSchdule,
            &result);
    __that = v17;
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator=(
      &v19->m_iterSchedule,
      v17);
    std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>(&result);
  }
}
