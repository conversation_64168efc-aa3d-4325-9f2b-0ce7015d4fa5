/*
 * Function: j_?InsertPlayerStatus@TimeLimitMgr@@QEAAXGKEKK_N@Z
 * Address: 0x140002EAA
 */

void __fastcall TimeLimitMgr::InsertPlayerStatus(TimeLimitMgr *this, unsigned __int16 wIndex, unsigned int dwAccountSerial, char byStatus, unsigned int dwFatigue, unsigned int dwLastLogoutTime, bool bAgeLimit)
{
  TimeLimitMgr::InsertPlayerStatus(this, wIndex, dwAccountSerial, byStatus, dwFatigue, dwLastLogoutTime, bAgeLimit);
}
