/*
 * Function: ??$_Fill@PEAVCUnmannedTraderRegistItemInfo@@V1@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@0AEBV1@@Z
 * Address: 0x140362EE0
 */

void __fastcall std::_Fill<CUnmannedTraderRegistItemInfo *,CUnmannedTraderRegistItemInfo>(CUnmannedTraderRegistItemInfo *_First, CUnmannedTraderRegistItemInfo *_Last, CUnmannedTraderRegistItemInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderRegistItemInfo *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *v7; // [sp+38h] [bp+10h]@1
  CUnmannedTraderRegistItemInfo *rhs; // [sp+40h] [bp+18h]@1

  rhs = _Val;
  v7 = _Last;
  v6 = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  while ( v6 != v7 )
  {
    CUnmannedTraderRegistItemInfo::operator=(v6, rhs);
    ++v6;
  }
}
