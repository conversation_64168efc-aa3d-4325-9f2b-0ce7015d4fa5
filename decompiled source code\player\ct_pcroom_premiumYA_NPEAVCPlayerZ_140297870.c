/*
 * Function: ?ct_pcroom_premium@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297870
 */

char __fastcall ct_pcroom_premium(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1

  v5 = pOne;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v5 )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      if ( !strcmp_0("y", s_pwszDstCheat[0]) )
      {
        v5->m_pUserDB->m_BillingInfo.bPCCheat = 1;
        v5->m_pUserDB->m_BillingInfo.bIsPcBang = 1;
        v5->m_pUserDB->m_BillingInfo.iType = 7;
      }
      else if ( !strcmp_0("n", s_pwszDstCheat[0]) )
      {
        v5->m_pUserDB->m_BillingInfo.bPCCheat = 0;
        v5->m_pUserDB->m_BillingInfo.bIsPcBang = 0;
        v5->m_pUserDB->m_BillingInfo.iType = 1;
      }
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
