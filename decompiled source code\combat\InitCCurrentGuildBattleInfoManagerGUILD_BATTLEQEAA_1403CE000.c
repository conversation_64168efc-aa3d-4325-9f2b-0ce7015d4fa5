/*
 * Function: ?Init@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403CE000
 */

char __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::Init(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v3; // rax@4
  char result; // al@5
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@7
  GUILD_BATTLE::CGuildBattleLogger *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-48h]@1
  _guild_battle_current_battle_info_result_zocl *v8; // [sp+20h] [bp-28h]@6
  bool *v9; // [sp+28h] [bp-20h]@8
  unsigned __int64 v10; // [sp+30h] [bp-18h]@6
  GUILD_BATTLE::CCurrentGuildBattleInfoManager *v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v1 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  v11->m_uiMapCnt = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v3);
  if ( v11->m_uiMapCnt )
  {
    v10 = v11->m_uiMapCnt;
    v8 = (_guild_battle_current_battle_info_result_zocl *)operator new[](saturated_mul(0x35ui64, v10));
    v11->m_pkInfo = v8;
    if ( v11->m_pkInfo )
    {
      memset_0(v11->m_pkInfo, 0, 53i64 * v11->m_uiMapCnt);
      v9 = (bool *)operator new[](v11->m_uiMapCnt);
      v11->m_pbUpdate = v9;
      if ( v11->m_pbUpdate )
      {
        memset_0(v11->m_pbUpdate, 0, v11->m_uiMapCnt);
        v11->m_bInit = 1;
        result = 1;
      }
      else
      {
        v6 = GUILD_BATTLE::CGuildBattleLogger::Instance();
        GUILD_BATTLE::CGuildBattleLogger::Log(
          v6,
          "CCurrentGuildBattleInfoManager::Init()new bool[%u] NULL!",
          v11->m_uiMapCnt);
        result = 0;
      }
    }
    else
    {
      v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v5,
        "CCurrentGuildBattleInfoManager::Init()new _guild_battle_current_battle_info_result_zocl[%u] NULL!",
        v11->m_uiMapCnt);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
