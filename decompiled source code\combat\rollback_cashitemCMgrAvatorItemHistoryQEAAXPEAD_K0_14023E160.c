/*
 * Function: ?rollback_cashitem@CMgrAvatorItemHistory@@QEAAXPEAD_K0H0@Z
 * Address: 0x14023E160
 */

void __fastcall CMgrAvatorItemHistory::rollback_cashitem(CMgrAvatorItemHistory *this, char *szRet, unsigned __int64 lnUID, char *strItemCode, int nCash, char *pFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-48h]@1
  char *v9; // [sp+20h] [bp-28h]@4
  int v10; // [sp+28h] [bp-20h]@4
  unsigned int v11; // [sp+30h] [bp-18h]@4
  CMgrAvatorItemHistory *v12; // [sp+50h] [bp+8h]@1
  char *v13; // [sp+58h] [bp+10h]@1
  unsigned __int64 v14; // [sp+60h] [bp+18h]@1
  char *v15; // [sp+68h] [bp+20h]@1

  v15 = strItemCode;
  v14 = lnUID;
  v13 = szRet;
  v12 = this;
  v6 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  sData[0] = 0;
  v11 = GetKorLocalTime();
  v10 = nCash;
  v9 = v15;
  sprintf(sData, "[CS_ROLLBACK_%s] UID:%I64u ICODE:%s Cash:%d[T:%u]\r\n", v13, v14);
  CMgrAvatorItemHistory::WriteFile(v12, pFileName, sData);
}
