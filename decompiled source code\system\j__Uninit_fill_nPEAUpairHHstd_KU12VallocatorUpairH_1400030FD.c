/*
 * Function: j_??$_Uninit_fill_n@PEAU?$pair@HH@std@@_KU12@V?$allocator@U?$pair@HH@std@@@2@@std@@YAXPEAU?$pair@HH@0@_KAEBU10@AEAV?$allocator@U?$pair@HH@std@@@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400030FD
 */

void __fastcall std::_Uninit_fill_n<std::pair<int,int> *,unsigned __int64,std::pair<int,int>,std::allocator<std::pair<int,int>>>(std::pair<int,int> *_First, unsigned __int64 _Count, std::pair<int,int> *_Val, std::allocator<std::pair<int,int> > *_Al, std::_Nonscalar_ptr_iterator_tag __formal, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<std::pair<int,int> *,unsigned __int64,std::pair<int,int>,std::allocator<std::pair<int,int>>>(
    _First,
    _Count,
    _Val,
    _Al,
    __formal,
    a6);
}
