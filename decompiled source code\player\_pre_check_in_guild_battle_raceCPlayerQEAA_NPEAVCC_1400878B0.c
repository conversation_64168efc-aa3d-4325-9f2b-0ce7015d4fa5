/*
 * Function: ?_pre_check_in_guild_battle_race@CPlayer@@QEAA_NPEAVCCharacter@@_N@Z
 * Address: 0x1400878B0
 */

char __fastcall CPlayer::_pre_check_in_guild_battle_race(CPlayer *this, <PERSON>haracter *pDst, bool bEqueal)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v6; // eax@25
  int v7; // eax@28
  int v8; // eax@51
  int v9; // eax@54
  int v10; // eax@63
  int v11; // eax@66
  int v12; // eax@75
  int v13; // eax@78
  __int64 v14; // [sp+0h] [bp-D8h]@1
  CCharacter *v15; // [sp+20h] [bp-B8h]@8
  CPlayer *v16; // [sp+28h] [bp-B0h]@23
  __int64 v17; // [sp+30h] [bp-A8h]@33
  CPlayer *v18; // [sp+38h] [bp-A0h]@49
  CPlayer *v19; // [sp+40h] [bp-98h]@61
  CPlayer *v20; // [sp+48h] [bp-90h]@73
  int v21; // [sp+50h] [bp-88h]@25
  CGameObjectVtbl *v22; // [sp+58h] [bp-80h]@25
  int v23; // [sp+60h] [bp-78h]@28
  CGameObjectVtbl *v24; // [sp+68h] [bp-70h]@28
  int v25; // [sp+70h] [bp-68h]@51
  CGameObjectVtbl *v26; // [sp+78h] [bp-60h]@51
  int v27; // [sp+80h] [bp-58h]@54
  CGameObjectVtbl *v28; // [sp+88h] [bp-50h]@54
  int v29; // [sp+90h] [bp-48h]@63
  CGameObjectVtbl *v30; // [sp+98h] [bp-40h]@63
  int v31; // [sp+A0h] [bp-38h]@66
  CGameObjectVtbl *v32; // [sp+A8h] [bp-30h]@66
  int v33; // [sp+B0h] [bp-28h]@75
  CGameObjectVtbl *v34; // [sp+B8h] [bp-20h]@75
  int v35; // [sp+C0h] [bp-18h]@78
  CGameObjectVtbl *v36; // [sp+C8h] [bp-10h]@78
  CPlayer *v37; // [sp+E0h] [bp+8h]@1
  CPlayer *v38; // [sp+E8h] [bp+10h]@1

  v38 = (CPlayer *)pDst;
  v37 = this;
  v3 = &v14;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( !pDst )
    return 0;
  if ( pDst->m_ObjID.m_byID || v37->m_ObjID.m_byID )
  {
    if ( pDst->m_ObjID.m_byID || v37->m_ObjID.m_byID != 3 )
    {
      if ( bEqueal )
      {
        if ( CPlayer::IsChaosMode(v37) )
          return 0;
        if ( !v38->m_ObjID.m_byID )
        {
          v20 = v38;
          if ( CPlayer::IsPunished(v38, 1, 0) )
            return 0;
        }
        v33 = ((int (__fastcall *)(CPlayer *))v38->vfptr->GetObjRace)(v38);
        v34 = v37->vfptr;
        v12 = ((int (__fastcall *)(CPlayer *))v34->GetObjRace)(v37);
        if ( v33 == v12 )
          return 1;
      }
      else
      {
        v35 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
        v36 = v37->vfptr;
        v13 = ((int (__fastcall *)(CPlayer *))v36->GetObjRace)(v37);
        if ( v35 != v13 )
          return 1;
      }
    }
    else
    {
      v17 = *(_QWORD *)&pDst[1].m_bLive;
      if ( v17 )
      {
        if ( *(_BYTE *)(v17 + 1922) && v37->m_bInGuildBattle )
        {
          if ( bEqueal )
          {
            if ( v37->m_byGuildBattleColorInx == *(_BYTE *)(v17 + 1924) )
              return 1;
          }
          else if ( v37->m_byGuildBattleColorInx != *(_BYTE *)(v17 + 1924) )
          {
            return 1;
          }
        }
        else if ( !*(_BYTE *)(v17 + 1922) && !v37->m_bInGuildBattle )
        {
          if ( bEqueal )
          {
            if ( CPlayer::IsChaosMode(v37) )
              return 0;
            if ( !v38->m_ObjID.m_byID )
            {
              v18 = v38;
              if ( CPlayer::IsPunished(v38, 1, 0) )
                return 0;
            }
            v25 = ((int (__fastcall *)(CPlayer *))v38->vfptr->GetObjRace)(v38);
            v26 = v37->vfptr;
            v8 = ((int (__fastcall *)(CPlayer *))v26->GetObjRace)(v37);
            if ( v25 == v8 )
              return 1;
          }
          else
          {
            v27 = ((int (__fastcall *)(CCharacter *))pDst->vfptr->GetObjRace)(pDst);
            v28 = v37->vfptr;
            v9 = ((int (__fastcall *)(CPlayer *))v28->GetObjRace)(v37);
            if ( v27 != v9 )
              return 1;
          }
        }
      }
      else if ( bEqueal )
      {
        if ( CPlayer::IsChaosMode(v37) )
          return 0;
        if ( !v38->m_ObjID.m_byID )
        {
          v19 = v38;
          if ( CPlayer::IsPunished(v38, 1, 0) )
            return 0;
        }
        v29 = ((int (__fastcall *)(CPlayer *))v38->vfptr->GetObjRace)(v38);
        v30 = v37->vfptr;
        v10 = ((int (__fastcall *)(CPlayer *))v30->GetObjRace)(v37);
        if ( v29 == v10 )
          return 1;
      }
      else
      {
        v31 = ((int (__fastcall *)(_QWORD))pDst->vfptr->GetObjRace)(pDst);
        v32 = v37->vfptr;
        v11 = ((int (__fastcall *)(CPlayer *))v32->GetObjRace)(v37);
        if ( v31 != v11 )
          return 1;
      }
    }
  }
  else
  {
    v15 = pDst;
    if ( BYTE2(pDst[1].m_fCurPos[2]) && v37->m_bInGuildBattle )
    {
      if ( bEqueal )
      {
        if ( v37->m_byGuildBattleColorInx == LOBYTE(v15[1].m_fAbsPos[0]) )
          return 1;
      }
      else if ( v37->m_byGuildBattleColorInx != LOBYTE(v15[1].m_fAbsPos[0]) )
      {
        return 1;
      }
    }
    else if ( !BYTE2(v15[1].m_fCurPos[2]) && !v37->m_bInGuildBattle )
    {
      if ( bEqueal )
      {
        if ( CPlayer::IsChaosMode(v37) )
          return 0;
        if ( !v38->m_ObjID.m_byID )
        {
          v16 = v38;
          if ( CPlayer::IsPunished(v38, 1, 0) )
            return 0;
        }
        v21 = ((int (__fastcall *)(_QWORD))v38->vfptr->GetObjRace)(v38);
        v22 = v37->vfptr;
        v6 = ((int (__fastcall *)(CPlayer *))v22->GetObjRace)(v37);
        if ( v21 == v6 )
          return 1;
      }
      else
      {
        v23 = ((int (__fastcall *)(_QWORD))pDst->vfptr->GetObjRace)(pDst);
        v24 = v37->vfptr;
        v7 = ((int (__fastcall *)(CPlayer *))v24->GetObjRace)(v37);
        if ( v23 != v7 )
          return 1;
      }
    }
  }
  return 0;
}
