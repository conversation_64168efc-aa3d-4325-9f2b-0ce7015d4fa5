/*
 * Function: ??0?$hash_map@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$pair@QEAUScheduleMSG@@K@std@@@std@@@stdext@@QEAA@XZ
 * Address: 0x1404205E0
 */

void __fastcall stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>>(stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  std::allocator<std::pair<ScheduleMSG * const,unsigned long> > *v3; // rax@4
  stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-38h]@1
  char v6; // [sp+20h] [bp-18h]@4
  char v7; // [sp+21h] [bp-17h]@4
  std::allocator<std::pair<ScheduleMSG * const,unsigned long> > *_Al; // [sp+28h] [bp-10h]@4
  stdext::hash_map<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> > > *v9; // [sp+40h] [bp+8h]@1

  v9 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  std::allocator<std::pair<ScheduleMSG * const,unsigned long>>::allocator<std::pair<ScheduleMSG * const,unsigned long>>((std::allocator<std::pair<ScheduleMSG * const,unsigned long> > *)&v6);
  _Al = v3;
  stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>((stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *)&v7);
  stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>,std::allocator<std::pair<ScheduleMSG * const,unsigned long>>,0>>(
    (stdext::_Hash<stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> >,std::allocator<std::pair<ScheduleMSG * const,unsigned long> >,0> > *)&v9->_Myfirstiter,
    v4,
    _Al);
}
