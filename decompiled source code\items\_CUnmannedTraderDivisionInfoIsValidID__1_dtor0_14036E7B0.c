/*
 * Function: _CUnmannedTraderDivisionInfo::IsValidID_::_1_::dtor$0
 * Address: 0x14036E7B0
 */

void __fastcall CUnmannedTraderDivisionInfo::IsValidID_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>((std::_Vector_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *)(a2 + 40));
}
