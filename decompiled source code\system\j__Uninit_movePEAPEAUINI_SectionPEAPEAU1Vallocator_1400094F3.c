/*
 * Function: j_??$_Uninit_move@PEAPEAUINI_Section@@PEAPEAU1@V?$allocator@PEAUINI_Section@@@std@@U_Undefined_move_tag@3@@std@@YAPEAPEAUINI_Section@@PEAPEAU1@00AEAV?$allocator@PEAUINI_Section@@@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400094F3
 */

INI_Section **__fastcall std::_Uninit_move<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>,std::_Undefined_move_tag>(INI_Section **_First, INI_Section **_Last, INI_Section **_Dest, std::allocator<INI_Section *> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<INI_Section * *,INI_Section * *,std::allocator<INI_Section *>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _Dest,
           _<PERSON>,
           __formal,
           a6);
}
