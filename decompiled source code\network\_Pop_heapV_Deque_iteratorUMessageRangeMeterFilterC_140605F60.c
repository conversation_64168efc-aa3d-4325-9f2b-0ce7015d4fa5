/*
 * Function: ??$_Pop_heap@V?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@std@@_JUMessageRange@MeterFilter@CryptoPP@@@std@@YAXV?$_Deque_iterator@UMessageRange@MeterFilter@CryptoPP@@V?$allocator@UMessageRange@MeterFilter@CryptoPP@@@std@@$0A@@0@00UMessageRange@MeterFilter@CryptoPP@@PEA_J@Z
 * Address: 0x140605F60
 */

int __fastcall std::_Pop_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(__int64 a1, __int64 a2, __int64 a3, const void *a4)
{
  const void *v4; // rax@1
  char *v5; // rax@1
  __int64 v6; // rax@1
  char v8; // [sp+20h] [bp-98h]@1
  char *v9; // [sp+40h] [bp-78h]@1
  char v10; // [sp+50h] [bp-68h]@1
  char v11; // [sp+70h] [bp-48h]@1
  __int64 v12; // [sp+88h] [bp-30h]@1
  __int64 v13; // [sp+90h] [bp-28h]@1
  __int64 v14; // [sp+98h] [bp-20h]@1
  const void *v15; // [sp+D8h] [bp+20h]@1

  v15 = a4;
  v12 = -2i64;
  LODWORD(v4) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  qmemcpy(&v11, v4, 0x18ui64);
  LODWORD(v5) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator*();
  qmemcpy(v5, &v11, 0x18ui64);
  v9 = &v8;
  qmemcpy(&v10, v15, 0x18ui64);
  LODWORD(v6) = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::operator-();
  v13 = v6;
  v14 = std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>((__int64)v9);
  std::_Adjust_heap<std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>,__int64,CryptoPP::MeterFilter::MessageRange>(
    v14,
    0i64,
    v13,
    &v10);
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
  return std::_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>::~_Deque_iterator<CryptoPP::MeterFilter::MessageRange,std::allocator<CryptoPP::MeterFilter::MessageRange>,0>();
}
