/*
 * Function: ??4?$_Ranit@VCUnmannedTraderUserInfo@@_JPEBV1@AEBV1@@std@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x1403698F0
 */

std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *__fastcall std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &>::operator=(std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *this, std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::_Ranit<CUnmannedTraderUserInfo,__int64,CUnmannedTraderUserInfo const *,CUnmannedTraderUserInfo const &> *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Iterator_base::operator=((std::_Iterator_base *)&v6->_Mycont, (std::_Iterator_base *)&__that->_Mycont);
  return v6;
}
