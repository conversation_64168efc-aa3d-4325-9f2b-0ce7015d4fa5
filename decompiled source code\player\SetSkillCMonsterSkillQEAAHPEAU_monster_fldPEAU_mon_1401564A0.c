/*
 * Function: ?SetSkill@CMonsterSkill@@QEAAHPEAU_monster_fld@@PEAU_monster_sp_fld@@HHPEAU_skill_fld@@KMKHHH@Z
 * Address: 0x1401564A0
 */

signed __int64 __fastcall CMonsterSkill::SetSkill(CMonsterSkill *this, _monster_fld *pMonsterFld, _monster_sp_fld *pSPCont, int nSFLv, int nEffectType, _skill_fld *pSkillFld, unsigned int dwDelayTime, float fAttackDist, unsigned int dwCastDelay, int nMotive, int nMotiveValue, int skillDestType)
{
  __int64 *v12; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v15; // [sp+0h] [bp-38h]@1
  int v16; // [sp+20h] [bp-18h]@9
  float v17; // [sp+24h] [bp-14h]@12
  int v18; // [sp+28h] [bp-10h]@5
  CMonsterSkill *v19; // [sp+40h] [bp+8h]@1
  _monster_fld *v20; // [sp+48h] [bp+10h]@1
  _monster_sp_fld *v21; // [sp+50h] [bp+18h]@1
  int v22; // [sp+58h] [bp+20h]@1

  v22 = nSFLv;
  v21 = pSPCont;
  v20 = pMonsterFld;
  v19 = this;
  v12 = &v15;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v12 = -858993460;
    v12 = (__int64 *)((char *)v12 + 4);
  }
  CMonsterSkill::Init(v19);
  if ( nEffectType == 2 )
    v18 = 2;
  else
    v18 = 1;
  v19->m_nSFCode = v18;
  v19->m_pSF_Fld = (_base_fld *)pSkillFld;
  v19->m_pSPConst = v21;
  if ( v19->m_pSF_Fld )
  {
    v19->m_wSFIndex = v19->m_pSF_Fld->m_dwIndex;
    v19->m_dwDelayTime = dwDelayTime;
    v19->m_fAttackDist = fAttackDist;
    v19->m_dwCastDelay = dwCastDelay;
    v19->m_nSFLv = v22;
    v19->m_Element = v20->m_nProperty;
    v19->m_StdDmg = (signed int)ffloor(v20->m_fAttFcStd);
    v16 = (signed int)ffloor((float)v19->m_StdDmg * 0.89999998);
    if ( v19->m_StdDmg - v16 <= 0 )
      v19->m_MinDmg = 0;
    else
      v19->m_MinDmg = rand() % (v19->m_StdDmg - v16) + v16;
    v19->m_MaxDmg = 2 * v19->m_StdDmg - v19->m_MinDmg;
    v17 = (float)(v19->m_MaxDmg + 125) / (float)(v19->m_MaxDmg + 50);
    v19->m_MinProb = (signed int)ffloor(v20->m_fMinAFSelProb);
    v19->m_MaxProb = (signed int)ffloor(v20->m_fMaxAFSelProb);
    if ( nEffectType )
    {
      if ( nEffectType == 2 )
        v19->m_UseType = _Check_SF_UseType((_base_fld *)&pSkillFld->m_dwIndex, 2);
    }
    else
    {
      v19->m_UseType = _Check_SF_UseType((_base_fld *)&pSkillFld->m_dwIndex, 0);
    }
    v19->m_nMotive = nMotive;
    v19->m_nMotivevalue = nMotiveValue;
    v19->m_nCaseType = skillDestType;
    v19->m_bExit = 1;
    result = 1i64;
  }
  else
  {
    CMonsterSkill::Init(v19);
    result = 0i64;
  }
  return result;
}
