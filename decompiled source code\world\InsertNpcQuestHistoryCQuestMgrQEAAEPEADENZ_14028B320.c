/*
 * Function: ?InsertNpcQuestHistory@CQuestMgr@@QEAAEPEADEN@Z
 * Address: 0x14028B320
 */

char __fastcall CQuestMgr::InsertNpcQuestHistory(CQuestMgr *this, char *pszQuestCode, char byLevel, long double dRepeatTime)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp-20h] [bp-48h]@1
  int v8; // [sp+0h] [bp-28h]@4
  unsigned __int8 v9; // [sp+4h] [bp-24h]@4
  int j; // [sp+8h] [bp-20h]@4
  char *Dest; // [sp+10h] [bp-18h]@6
  char *v12; // [sp+18h] [bp-10h]@11
  CQuestMgr *v13; // [sp+30h] [bp+8h]@1
  char v14; // [sp+40h] [bp+18h]@1

  v14 = byLevel;
  v13 = this;
  v4 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  v9 = -1;
  for ( j = 0; j < 70; ++j )
  {
    Dest = v13->m_pQuestData->m_History[j].szQuestCode;
    if ( (unsigned __int8)Dest[12] == 255 )
    {
      strcpy_0(Dest, pszQuestCode);
      Dest[12] = v14;
      *(_DWORD *)(Dest + 13) = GetConnectTime_AddBySec((signed int)floor(dRepeatTime));
      return j;
    }
    if ( (unsigned __int8)Dest[12] < (signed int)v9 )
    {
      v9 = Dest[12];
      v8 = j;
    }
  }
  v12 = v13->m_pQuestData->m_History[v8].szQuestCode;
  strcpy_0(v12, pszQuestCode);
  v12[12] = v14;
  *(_DWORD *)(v12 + 13) = GetConnectTime_AddBySec((signed int)floor(dRepeatTime));
  return v8;
}
