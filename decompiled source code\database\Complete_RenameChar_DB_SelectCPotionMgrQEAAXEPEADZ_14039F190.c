/*
 * Function: ?Complete_RenameChar_DB_Select@CPotionMgr@@QEAAXEPEAD@Z
 * Address: 0x14039F190
 */

void __fastcall CPotionMgr::Complete_RenameChar_DB_Select(CPotionMgr *this, char byRet, char *p)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  CPlayer *v7; // [sp+28h] [bp-10h]@4
  char v8; // [sp+48h] [bp+10h]@1

  v8 = byRet;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = p;
  v7 = GetPtrPlayerFromSerial(&g_Player, 2532, *(_DWORD *)p);
  if ( v7 && v7->m_bOper )
  {
    if ( v8 )
    {
      CPlayer::SendMsg_CharacterRenameCashResult(v7, 0, 26);
    }
    else if ( *((_DWORD *)v6 + 1) == -1 )
    {
      memcpy_s(&v7->m_ReNamePotionUseInfo, 4ui64, v6 + 8, 4ui64);
      strcpy_s(v7->m_ReNamePotionUseInfo.wszChangeName, 0x11ui64, v6 + 12);
      CPlayer::SendMsg_CharacterRenameCashResult(v7, 0, 0);
    }
    else
    {
      CPlayer::SendMsg_CharacterRenameCashResult(v7, 0, 10);
    }
  }
}
