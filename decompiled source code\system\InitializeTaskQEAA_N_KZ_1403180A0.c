/*
 * Function: ?Initialize@Task@@QEAA_N_K@Z
 * Address: 0x1403180A0
 */

bool __fastcall Task::Initialize(Task *this, unsigned __int64 nMaxBufSize)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4
  Task *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7->_nMaxBufSize = nMaxBufSize;
  v6 = (char *)operator new[](v7->_nMaxBufSize);
  v7->_pBuf = v6;
  return v7->_pBuf != 0i64;
}
