/*
 * Function: ??0CLogTypeDBTaskManager@@IEAA@XZ
 * Address: 0x1402C2A90
 */

void __fastcall CLogTypeDBTaskManager::CLogTypeDBTaskManager(CLogTypeDBTaskManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CLogTypeDBTaskManager *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_eState = -1;
  v4->m_bDBProc = 1;
  v4->m_pkWorldDB = 0i64;
  CLogTypeDBTaskPool::CLogTypeDBTaskPool(&v4->m_kPool);
  v4->m_pkLogger = 0i64;
}
