/*
 * Function: ?make_gen_attack_param@CMonster@@QEAAXPEAVCCharacter@@PEAU_attack_param@@@Z
 * Address: 0x14014DE80
 */

void __fastcall CMonster::make_gen_attack_param(CMonster *this, CCharacter *pDst, _attack_param *pAP)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  CMonsterSkill *v6; // [sp+20h] [bp-18h]@4
  CMonster *v7; // [sp+40h] [bp+8h]@1
  CCharacter *v8; // [sp+48h] [bp+10h]@1
  _attack_param *v9; // [sp+50h] [bp+18h]@1

  v9 = pAP;
  v8 = pDst;
  v7 = this;
  v3 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = CMonsterSkillPool::GetMonSkillKind(&v7->m_MonsterSkillPool, 0);
  if ( v6 )
  {
    v9->pDst = v8;
    if ( v8 )
      v9->nPart = CCharacter::GetAttackRandomPart(v8);
    else
      v9->nPart = CMonster::GetAttackPart(v7);
    v9->nClass = v7->m_pMonRec->m_bAttRangeType;
    v9->nTol = CMonsterSkill::GetElement(v6);
    v9->nMinAF = CMonsterSkill::GetMinDmg(v6);
    v9->nMaxAF = CMonsterSkill::GetMaxDmg(v6);
    v9->nMinSel = CMonsterSkill::GetMinProb(v6);
    v9->nMaxSel = CMonsterSkill::GetMaxProb(v6);
  }
  else
  {
    v9->pDst = v8;
    if ( v8 )
      v9->nPart = CCharacter::GetAttackRandomPart(v8);
    else
      v9->nPart = CMonster::GetAttackPart(v7);
    v9->nClass = v7->m_pMonRec->m_bAttRangeType;
    v9->nTol = -1;
    v9->nMinAF = 0;
    v9->nMaxAF = 500;
    v9->nMinSel = 0;
    v9->nMaxSel = 100;
  }
  v9->bPassCount = v7->m_pMonRec->m_bMonsterCondition == 1;
  if ( v7->m_pMonRec->m_nAttType > 2 )
  {
    v9->nAttactType = 6;
    v9->nExtentRange = 90;
  }
  if ( v8 )
    memcpy_0(v9->fArea, v8->m_fCurPos, 0xCui64);
}
