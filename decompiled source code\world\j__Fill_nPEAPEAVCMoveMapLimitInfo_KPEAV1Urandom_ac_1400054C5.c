/*
 * Function: j_??$_Fill_n@PEAPEAVCMoveMapLimitInfo@@_KPEAV1@Urandom_access_iterator_tag@std@@@std@@YAXPEAPEAVCMoveMapLimitInfo@@_KAEBQEAV1@Urandom_access_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400054C5
 */

void __fastcall std::_Fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::random_access_iterator_tag>(CMoveMapLimitInfo **_First, unsigned __int64 _Count, CMoveMapLimitInfo *const *_Val, std::random_access_iterator_tag __formal, std::_Range_checked_iterator_tag a5)
{
  std::_Fill_n<CMoveMapLimitInfo * *,unsigned __int64,CMoveMapLimitInfo *,std::random_access_iterator_tag>(
    _First,
    _Count,
    _Val,
    __formal,
    a5);
}
