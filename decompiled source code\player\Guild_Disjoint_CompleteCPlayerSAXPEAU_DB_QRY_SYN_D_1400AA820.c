/*
 * Function: ?Guild_Disjoint_Complete@CPlayer@@SAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1400AA820
 */

void __fastcall CPlayer::Guild_Disjoint_Complete(_DB_QRY_SYN_DATA *pData)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  int v4; // [sp+20h] [bp-28h]@4
  int v5; // [sp+24h] [bp-24h]@4
  char *v6; // [sp+28h] [bp-20h]@4
  CGuild *v7; // [sp+30h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v8; // [sp+50h] [bp+8h]@1

  v8 = pData;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -1;
  v5 = -1;
  v6 = v8->m_sData;
  v4 = *(_DWORD *)&v8->m_sData[4];
  v5 = *(_DWORD *)&v8->m_sData[0];
  v7 = &g_Guild[v4];
  if ( v7 && v7->m_dwSerial == v5 && !v8->m_byResult )
  {
    if ( v7->m_nApplierNum > 0 )
      CGuild::SendMsg_GuildDisjointInform(v7);
    CGuild::Release(v7);
  }
}
