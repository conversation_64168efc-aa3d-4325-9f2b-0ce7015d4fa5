/*
 * Function: ?GetSFLevel@@YAHHK@Z
 * Address: 0x14003EF10
 */

__int64 __fastcall GetSFLevel(int nLv, unsigned int dwHitCount)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@4
  __int64 v6; // [sp+0h] [bp-38h]@1
  int v7; // [sp+20h] [bp-18h]@4
  int v8; // [sp+40h] [bp+8h]@1

  v8 = nLv;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = (float)(signed int)(dwHitCount + 1) / sR[v8];
  sqrt(v4);
  sqrt(v4);
  v7 = (signed int)floor(v4 + 0.9999);
  if ( v7 <= 7 )
  {
    if ( v7 < 1 )
      v7 = 1;
  }
  else
  {
    v7 = 7;
  }
  return (unsigned int)v7;
}
