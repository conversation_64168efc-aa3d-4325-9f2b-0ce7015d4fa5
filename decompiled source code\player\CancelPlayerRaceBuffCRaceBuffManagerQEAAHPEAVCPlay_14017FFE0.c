/*
 * Function: ?CancelPlayerRaceBuff@CRaceBuffManager@@QEAAHPEAVCPlayer@@W4RESULT_TYPE@CRaceBuffInfoByHolyQuestfGroup@@I@Z
 * Address: 0x14017FFE0
 */

int __fastcall CRaceBuffManager::CancelPlayerRaceBuff(CRaceBuffManager *this, CPlayer *pkPlayer, CRaceBuffInfoByHolyQuestfGroup::RESULT_TYPE eReleaseType, unsigned int uiContinueCnt)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  CRaceBuffManager *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  return CRaceBuffByHolyQuestProcedure::CancelPlayerRaceBuff(
           &v8->m_kBuffByHolyQuest,
           pkPlayer,
           eReleaseType,
           uiContinueCnt);
}
