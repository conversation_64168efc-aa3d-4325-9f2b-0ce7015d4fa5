/*
 * Function: j_??$fill@PEAPEAVCUnmannedTraderSortType@@PEAV1@@std@@YAXPEAPEAVCUnmannedTraderSortType@@0AEBQEAV1@@Z
 * Address: 0x140002A13
 */

void __fastcall std::fill<CUnmannedTraderSortType * *,CUnmannedTraderSortType *>(CUnmannedTraderSortType **_First, CUnmannedTraderSortType **_Last, CUnmannedTraderSortType *const *_Val)
{
  std::fill<CUnmannedTraderSortType * *,CUnmannedTraderSortType *>(_First, _Last, _Val);
}
