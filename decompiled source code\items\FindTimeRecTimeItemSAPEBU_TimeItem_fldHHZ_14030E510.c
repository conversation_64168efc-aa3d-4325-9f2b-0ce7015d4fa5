/*
 * Function: ?FindTimeRec@TimeItem@@SAPEBU_TimeItem_fld@@HH@Z
 * Address: 0x14030E510
 */

_TimeItem_fld *__fastcall TimeItem::FindTimeRec(int nTbl, int nIdx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _TimeItem_fld *v4; // rax@6
  __int64 v5; // [sp+0h] [bp-98h]@1
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> result; // [sp+28h] [bp-70h]@7
  bool v7; // [sp+44h] [bp-54h]@7
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> v8; // [sp+48h] [bp-50h]@7
  _TimeItem_fld *v9; // [sp+60h] [bp-38h]@8
  _TimeItem_fld *v10; // [sp+68h] [bp-30h]@9
  __int64 v11; // [sp+70h] [bp-28h]@4
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Iterator<0> *v12; // [sp+78h] [bp-20h]@7
  std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Const_iterator<0> *_Right; // [sp+80h] [bp-18h]@7
  int v14; // [sp+A0h] [bp+8h]@1
  int _Keyval; // [sp+A8h] [bp+10h]@1

  _Keyval = nIdx;
  v14 = nTbl;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v11 = -2i64;
  if ( v14 >= 0 && v14 < 37 )
  {
    stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::find(
      (stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *)(120i64 * v14 + TimeItem::_phmapTbl),
      &result,
      &_Keyval);
    v12 = stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int>>,std::allocator<std::pair<int const,_TimeItem_fld const *>>,0>>::end(
            (stdext::_Hash<stdext::_Hmap_traits<int,_TimeItem_fld const *,stdext::hash_compare<int,std::less<int> >,std::allocator<std::pair<int const ,_TimeItem_fld const *> >,0> > *)(120i64 * v14 + TimeItem::_phmapTbl),
            &v8);
    _Right = (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Const_iterator<0> *)v12;
    v7 = std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Const_iterator<0>::operator==(
           (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Const_iterator<0> *)&result._Mycont,
           (std::list<std::pair<int const ,_TimeItem_fld const *>,std::allocator<std::pair<int const ,_TimeItem_fld const *> > >::_Const_iterator<0> *)&v12->_Mycont);
    std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&v8);
    if ( v7 )
    {
      v9 = 0i64;
      std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&result);
      v4 = v9;
    }
    else
    {
      v10 = std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::operator->(&result)->second;
      std::list<std::pair<int const,_TimeItem_fld const *>,std::allocator<std::pair<int const,_TimeItem_fld const *>>>::_Iterator<0>::~_Iterator<0>(&result);
      v4 = v10;
    }
  }
  else
  {
    v4 = 0i64;
  }
  return v4;
}
