/*
 * Function: ?DecideWin@CNormalGuildBattle@GUILD_BATTLE@@IEAAEXZ
 * Address: 0x1403E76F0
 */

char __fastcall GUILD_BATTLE::CNormalGuildBattle::DecideWin(GUILD_BATTLE::CNormalGuildBattle *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  char result; // al@5
  unsigned int v5; // eax@6
  unsigned int v6; // eax@8
  unsigned int v7; // eax@10
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned int v9; // [sp+20h] [bp-18h]@4
  unsigned int v10; // [sp+24h] [bp-14h]@6
  unsigned int v11; // [sp+28h] [bp-10h]@8
  unsigned int v12; // [sp+2Ch] [bp-Ch]@10
  GUILD_BATTLE::CNormalGuildBattle *v13; // [sp+40h] [bp+8h]@1

  v13 = this;
  v1 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v9 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v13->m_pkRed);
  v3 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v13->m_pkBlue);
  if ( v9 <= v3 )
  {
    v10 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v13->m_pkRed);
    v5 = GUILD_BATTLE::CNormalGuildBattleGuild::GetScore(v13->m_pkBlue);
    if ( v10 >= v5 )
    {
      v11 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v13->m_pkRed);
      v6 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v13->m_pkBlue);
      if ( v11 <= v6 )
      {
        v12 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v13->m_pkRed);
        v7 = GUILD_BATTLE::CNormalGuildBattleGuild::GetGoalCnt(v13->m_pkBlue);
        if ( v12 >= v7 )
        {
          v13->m_pkWin = 0i64;
          v13->m_pkLose = 0i64;
          result = 0;
        }
        else
        {
          v13->m_pkWin = v13->m_pkBlue;
          v13->m_pkLose = v13->m_pkRed;
          result = 2;
        }
      }
      else
      {
        v13->m_pkWin = v13->m_pkRed;
        v13->m_pkLose = v13->m_pkBlue;
        result = 1;
      }
    }
    else
    {
      v13->m_pkWin = v13->m_pkBlue;
      v13->m_pkLose = v13->m_pkRed;
      result = 2;
    }
  }
  else
  {
    v13->m_pkWin = v13->m_pkRed;
    v13->m_pkLose = v13->m_pkBlue;
    result = 1;
  }
  return result;
}
