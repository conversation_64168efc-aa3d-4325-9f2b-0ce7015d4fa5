/*
 * Function: ?Accept_Client@CNetSocket@@QEAA_NK@Z
 * Address: 0x14047E750
 */

char __fastcall CNetSocket::Accept_Client(CNetSocket *this, unsigned int dwSocketIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  _socket *v6; // [sp+20h] [bp-18h]@4
  CNetSocket *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = &v7->m_Socket[dwSocketIndex];
  if ( v6->m_bAccept )
  {
    result = 0;
  }
  else
  {
    v6->m_bAccept = 1;
    v6->m_bSendable = 1;
    v6->m_dwConnectTime = timeGetTime();
    v6->m_dwLastRecvTime = timeGetTime();
    v6->m_dwLastSendTime = v6->m_dwLastRecvTime;
    v6->m_dwSerial = v7->m_dwSerialCounter++;
    ++v7->m_TotalCount.m_dwAcceptNum;
    result = 1;
  }
  return result;
}
