#include "../Headers/EconomyDataStructures.h"
#include <cstring>
#include <ctime>

namespace NexusProtection::Economy {

    // Conversion functions between modern and legacy structures

    void ConvertToLegacy(const LogSheetEconomy& modern, _log_sheet_economy& legacy) {
        std::memset(&legacy, 0, sizeof(_log_sheet_economy));
        std::memcpy(legacy.data, &modern, std::min(sizeof(modern), sizeof(legacy.data)));
    }

    void ConvertFromLegacy(const _log_sheet_economy& legacy, LogSheetEconomy& modern) {
        std::memset(&modern, 0, sizeof(LogSheetEconomy));
        std::memcpy(&modern, legacy.data, std::min(sizeof(legacy.data), sizeof(modern)));
    }

    void ConvertToLegacy(const QueryCaseInputGuildMoney& modern, _qry_case_inputgmoney& legacy) {
        std::memset(&legacy, 0, sizeof(_qry_case_inputgmoney));
        std::memcpy(legacy.data, &modern, std::min(sizeof(modern), sizeof(legacy.data)));
    }

    void ConvertFromLegacy(const _qry_case_inputgmoney& legacy, QueryCaseInputGuildMoney& modern) {
        std::memset(&modern, 0, sizeof(QueryCaseInputGuildMoney));
        std::memcpy(&modern, legacy.data, std::min(sizeof(legacy.data), sizeof(modern)));
    }

    void ConvertToLegacy(const QueryCaseOutputGuildMoney& modern, _qry_case_outputgmoney& legacy) {
        std::memset(&legacy, 0, sizeof(_qry_case_outputgmoney));
        std::memcpy(legacy.data, &modern, std::min(sizeof(modern), sizeof(legacy.data)));
    }

    void ConvertFromLegacy(const _qry_case_outputgmoney& legacy, QueryCaseOutputGuildMoney& modern) {
        std::memset(&modern, 0, sizeof(QueryCaseOutputGuildMoney));
        std::memcpy(&modern, legacy.data, std::min(sizeof(legacy.data), sizeof(modern)));
    }

    void ConvertToLegacy(const EconomyHistoryData& modern, _economy_history_data& legacy) {
        for (size_t i = 0; i < 3; ++i) {
            legacy.dOldDalant[i] = modern.oldDalant[i];
            legacy.dCurDalant[i] = modern.currentDalant[i];
            legacy.dBufTradeDalant[i] = modern.bufferTradeDalant[i];
        }
    }

    void ConvertFromLegacy(const _economy_history_data& legacy, EconomyHistoryData& modern) {
        for (size_t i = 0; i < 3; ++i) {
            modern.oldDalant[i] = legacy.dOldDalant[i];
            modern.currentDalant[i] = legacy.dCurDalant[i];
            modern.bufferTradeDalant[i] = legacy.dBufTradeDalant[i];
        }
    }

    void ConvertToLegacy(const EconomyCalculationData& modern, _economy_calc_data& legacy) {
        for (size_t i = 0; i < 3; ++i) {
            legacy.dTradeDalant[i] = modern.tradeDalant[i];
            legacy.dTradeGold[i] = modern.tradeGold[i];
            
            for (size_t j = 0; j < 3; ++j) {
                legacy.dOreMineCount[i][j] = modern.oreMineCount[i][j];
                legacy.dOreCutCount[i][j] = modern.oreCutCount[i][j];
            }
        }
    }

    void ConvertFromLegacy(const _economy_calc_data& legacy, EconomyCalculationData& modern) {
        for (size_t i = 0; i < 3; ++i) {
            modern.tradeDalant[i] = legacy.dTradeDalant[i];
            modern.tradeGold[i] = legacy.dTradeGold[i];
            
            for (size_t j = 0; j < 3; ++j) {
                modern.oreMineCount[i][j] = legacy.dOreMineCount[i][j];
                modern.oreCutCount[i][j] = legacy.dOreCutCount[i][j];
            }
        }
    }

} // namespace NexusProtection::Economy

// Legacy C interface implementation
extern "C" {
    signed __int64 _log_sheet_economy_size(_log_sheet_economy* obj) {
        (void)obj; // Suppress unused parameter warning
        return 208;
    }

    signed __int64 _qry_case_inputgmoney_size(_qry_case_inputgmoney* obj) {
        (void)obj; // Suppress unused parameter warning
        return 64;
    }

    signed __int64 _qry_case_outputgmoney_size(_qry_case_outputgmoney* obj) {
        (void)obj; // Suppress unused parameter warning
        return 72;
    }

    signed __int64 _guild_money_io_download_zocl_size(_guild_money_io_download_zocl* obj) {
        (void)obj; // Suppress unused parameter warning
        return 96;
    }

    void _economy_history_data_constructor(_economy_history_data* obj) {
        if (obj) {
            std::memset(obj, 0, sizeof(_economy_history_data));
        }
    }

    // Legacy size function implementations that match the original decompiled code
    signed __int64 __fastcall size_log_sheet_economy(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        return 208;
    }

    signed __int64 __fastcall size_qry_case_inputgmoney(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        return 64;
    }

    signed __int64 __fastcall size_qry_case_outputgmoney(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        return 72;
    }

    signed __int64 __fastcall size_guild_money_io_download_zocl(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        return 96;
    }

    // Constructor implementations
    void __fastcall constructor_economy_history_data(void* this_ptr) {
        if (this_ptr) {
            std::memset(this_ptr, 0, sizeof(_economy_history_data));
        }
    }

    void __fastcall constructor_guild_money_io_download_zocl(void* this_ptr) {
        if (this_ptr) {
            std::memset(this_ptr, 0, 96); // Size of the structure
        }
    }

    // Destructor implementations (these are typically empty for simple data structures)
    void __fastcall destructor_economy_history_data(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        // No cleanup needed for simple data structure
    }

    void __fastcall destructor_guild_money_io_download_zocl(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        // No cleanup needed for simple data structure
    }

    void __fastcall destructor_log_sheet_economy(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        // No cleanup needed for simple data structure
    }

    void __fastcall destructor_qry_case_inputgmoney(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        // No cleanup needed for simple data structure
    }

    void __fastcall destructor_qry_case_outputgmoney(void* this_ptr) {
        (void)this_ptr; // Suppress unused parameter warning
        // No cleanup needed for simple data structure
    }
}
