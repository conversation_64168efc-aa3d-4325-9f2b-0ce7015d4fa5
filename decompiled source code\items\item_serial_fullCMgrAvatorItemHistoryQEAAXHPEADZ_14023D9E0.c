/*
 * Function: ?item_serial_full@CMgrAvatorItemHistory@@QEAAXHPEAD@Z
 * Address: 0x14023D9E0
 */

void __fastcall CMgrAvatorItemHistory::item_serial_full(CMgrAvatorItemHistory *this, int n, char *pszFileName)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMgrAvatorItemHistory *v6; // [sp+30h] [bp+8h]@1
  char *pszFileNamea; // [sp+40h] [bp+18h]@1

  pszFileNamea = pszFileName;
  v6 = this;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  sprintf(sData, "\r\nITEM SERIAL FULL [%s %s]\r\n", v6->m_szCurDate, v6->m_szCurTime);
  CMgrAvatorItemHistory::WriteFile(v6, pszFileNamea, sData);
}
