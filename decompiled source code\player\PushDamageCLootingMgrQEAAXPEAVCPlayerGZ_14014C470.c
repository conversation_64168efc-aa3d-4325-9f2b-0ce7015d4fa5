/*
 * Function: ?PushDamage@CLootingMgr@@QEAAXPEAVCPlayer@@G@Z
 * Address: 0x14014C470
 */

void __fastcall CLootingMgr::PushDamage(CLootingMgr *this, CPlayer *pAtter, unsigned __int16 wDamage)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@4
  CLootingMgr::_list *v7; // [sp+28h] [bp-40h]@7
  CLootingMgr::_list *v8; // [sp+30h] [bp-38h]@17
  CLootingMgr::_list *v9; // [sp+38h] [bp-30h]@19
  unsigned int v10; // [sp+40h] [bp-28h]@23
  CLootingMgr::_list *v11; // [sp+48h] [bp-20h]@25
  unsigned int v12; // [sp+50h] [bp-18h]@10
  unsigned int v13; // [sp+54h] [bp-14h]@13
  unsigned int v14; // [sp+58h] [bp-10h]@31
  unsigned int v15; // [sp+5Ch] [bp-Ch]@34
  CLootingMgr *v16; // [sp+70h] [bp+8h]@1
  CPlayer *v17; // [sp+78h] [bp+10h]@1
  unsigned __int16 v18; // [sp+80h] [bp+18h]@1

  v18 = wDamage;
  v17 = pAtter;
  v16 = this;
  v3 = &v5;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  for ( j = 0; j < v16->m_byUserNode; ++j )
  {
    v7 = &v16->m_AtterList[j];
    if ( v7->pAtter == pAtter )
    {
      if ( v7->dwAtterSerial == pAtter->m_dwObjSerial )
      {
        if ( v7->dwAttCount <= v7->dwAttCount + 1 )
          v12 = v7->dwAttCount + 1;
        else
          v12 = v7->dwAttCount;
        v7->dwAttCount = v12;
        if ( v7->dwDamage <= v7->dwDamage + wDamage )
          v13 = v7->dwDamage + wDamage;
        else
          v13 = v7->dwDamage;
        v7->dwDamage = v13;
        v7->dwLastAttTime = timeGetTime();
        return;
      }
      CLootingMgr::_list::Init(v7);
      break;
    }
  }
  v8 = 0i64;
  for ( j = 0; j < v16->m_byUserNode; ++j )
  {
    v9 = &v16->m_AtterList[j];
    if ( !v9->pAtter )
    {
      v8 = v9;
      break;
    }
  }
  if ( !v8 )
  {
    v10 = v16->m_AtterList[0].dwLastAttTime;
    v8 = v16->m_AtterList;
    for ( j = 1; j < v16->m_byUserNode; ++j )
    {
      v11 = &v16->m_AtterList[j];
      if ( v10 > v16->m_AtterList[j].dwLastAttTime )
      {
        v10 = v11->dwLastAttTime;
        v8 = v11;
      }
    }
    CLootingMgr::_list::Init(v8);
    if ( v8 == v16->m_AtterList )
      v16->m_bFirst = 0;
  }
  v8->pAtter = v17;
  v8->dwAtterSerial = v17->m_dwObjSerial;
  if ( v8->dwAttCount <= v8->dwAttCount + 1 )
    v14 = v8->dwAttCount + 1;
  else
    v14 = v8->dwAttCount;
  v8->dwAttCount = v14;
  if ( v8->dwDamage <= v8->dwDamage + v18 )
    v15 = v8->dwDamage + v18;
  else
    v15 = v8->dwDamage;
  v8->dwDamage = v15;
  v8->dwLastAttTime = timeGetTime();
}
