/*
 * Function: j_??0?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@QEAA@AEBV01@@Z
 * Address: 0x14000CC5C
 */

void __fastcall std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocator<GUILD_BATTLE::CGuildBattleRewardItem>(std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *this, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *__formal)
{
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>::allocator<GUILD_BATTLE::CGuildBattleRewardItem>(this, __formal);
}
