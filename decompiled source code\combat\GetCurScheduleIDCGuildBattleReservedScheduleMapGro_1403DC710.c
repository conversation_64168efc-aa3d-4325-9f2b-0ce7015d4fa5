/*
 * Function: ?GetCurScheduleID@CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAII@Z
 * Address: 0x1403DC710
 */

unsigned int __fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::GetCurScheduleID(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, unsigned int uiMapID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int result; // eax@7
  __int64 v5; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v6; // [sp+30h] [bp+8h]@1

  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_uiMapCnt && v6->m_uiMapCnt > uiMapID && v6->m_ppkReservedSchedule[uiMapID] )
    result = GUILD_BATTLE::CGuildBattleReservedSchedule::GetCurScheduleID(v6->m_ppkReservedSchedule[uiMapID]);
  else
    result = 0;
  return result;
}
