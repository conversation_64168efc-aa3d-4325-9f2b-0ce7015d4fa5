#include "../Headers/BillingLogin.h"
#include <algorithm>
#include <sstream>
#include <cstring>
#include <stdexcept>
#include <fstream>
#include <iomanip>

namespace NexusProtection::Authentication {

    // BillingInfo implementation

    BillingInfo::BillingInfo() {
        Reset();
    }

    void BillingInfo::Reset() {
        iType = 0;
        lRemainTime = 0;
        stEndDate = std::chrono::system_clock::now();
        szCMS.clear();
        bIsPcBang = false;
        bIsActive = true;
    }

    bool BillingInfo::IsValid() const {
        return bIsActive && lRemainTime >= 0 && !szCMS.empty();
    }

    std::string BillingInfo::ToString() const {
        std::ostringstream oss;
        oss << "BillingInfo{";
        oss << "Type: " << iType << ", ";
        oss << "RemainTime: " << lRemainTime << "s, ";
        oss << "CMS: \"" << szCMS << "\", ";
        oss << "PcBang: " << (bIsPcBang ? "true" : "false") << ", ";
        oss << "Active: " << (bIsActive ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    // UserBillingData implementation

    UserBillingData::UserBillingData() {
        Reset();
    }

    void UserBillingData::Reset() {
        szAccountID.clear();
        dwIP = 0;
        billingInfo.Reset();
        bBillingNoLogout = false;
    }

    bool UserBillingData::IsValid() const {
        return !szAccountID.empty() && dwIP != 0 && billingInfo.IsValid();
    }

    std::string UserBillingData::GetIPAddressString() const {
        // Convert IP address to string format (inet_ntoa equivalent)
        uint32_t ip = dwIP;
        return std::to_string((ip >> 24) & 0xFF) + "." +
               std::to_string((ip >> 16) & 0xFF) + "." +
               std::to_string((ip >> 8) & 0xFF) + "." +
               std::to_string(ip & 0xFF);
    }

    // IBillingLogin implementation

    void IBillingLogin::UpdateStatistics(bool success) {
        ++m_totalLoginAttempts;
        if (success) {
            ++m_successfulLogins;
        } else {
            ++m_failedLogins;
        }
    }

    // CBillingLogin implementation

    CBillingLogin::CBillingLogin() = default;

    CBillingLogin::~CBillingLogin() {
        CleanupExpiredUsers();
    }

    CBillingLogin::CBillingLogin(CBillingLogin&& other) noexcept
        : m_activeUsers(std::move(other.m_activeUsers)) {
        // Move atomic values manually
        m_isEnabled = other.m_isEnabled;
        m_totalLoginAttempts.store(other.m_totalLoginAttempts.load());
        m_successfulLogins.store(other.m_successfulLogins.load());
        m_failedLogins.store(other.m_failedLogins.load());

        // Reset other's values
        other.m_isEnabled = true;
        other.m_totalLoginAttempts.store(0);
        other.m_successfulLogins.store(0);
        other.m_failedLogins.store(0);
    }

    CBillingLogin& CBillingLogin::operator=(CBillingLogin&& other) noexcept {
        if (this != &other) {
            std::lock_guard<std::mutex> lock(m_usersMutex);
            m_activeUsers = std::move(other.m_activeUsers);

            // Move atomic values manually
            m_isEnabled = other.m_isEnabled;
            m_totalLoginAttempts.store(other.m_totalLoginAttempts.load());
            m_successfulLogins.store(other.m_successfulLogins.load());
            m_failedLogins.store(other.m_failedLogins.load());

            // Reset other's values
            other.m_isEnabled = true;
            other.m_totalLoginAttempts.store(0);
            other.m_successfulLogins.store(0);
            other.m_failedLogins.store(0);
        }
        return *this;
    }

    bool CBillingLogin::Login(const UserBillingData& userData) {
        if (!IsEnabled() || !userData.IsValid()) {
            UpdateStatistics(false);
            return false;
        }

        bool success = ProcessLogin(userData);
        UpdateStatistics(success);
        return success;
    }

    bool CBillingLogin::Logout(const std::string& accountID) {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        auto it = m_activeUsers.find(accountID);
        if (it != m_activeUsers.end()) {
            m_activeUsers.erase(it);
            return true;
        }
        return false;
    }

    bool CBillingLogin::IsLoggedIn(const std::string& accountID) const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return m_activeUsers.find(accountID) != m_activeUsers.end();
    }

    uint32_t CBillingLogin::GetActiveUserCount() const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return static_cast<uint32_t>(m_activeUsers.size());
    }

    std::string CBillingLogin::ToString() const {
        std::ostringstream oss;
        oss << "CBillingLogin{";
        oss << "Type: " << GetBillingTypeName() << ", ";
        oss << "Enabled: " << (IsEnabled() ? "true" : "false") << ", ";
        oss << "ActiveUsers: " << GetActiveUserCount() << ", ";
        oss << "TotalAttempts: " << GetTotalLoginAttempts() << ", ";
        oss << "Successful: " << GetSuccessfulLogins() << ", ";
        oss << "Failed: " << GetFailedLogins();
        oss << "}";
        return oss.str();
    }

    bool CBillingLogin::SendLoginMessage(const std::string& accountID, const std::string& ipAddress, const std::string& cms) {
        // Original implementation would call virtual function through vtable
        // For now, we simulate the message sending
        if (accountID.empty() || ipAddress.empty()) {
            return false;
        }

        // Simulate successful message sending
        // In the original code, this would call:
        // ((bool (__fastcall *)(CBilling *, const char *, const char *, const char *))vfptr->SendMsg_Login)(this, accountID, ipAddress, cms)
        
        return true; // Assume success for now
    }

    bool CBillingLogin::ValidateBillingInfo(const BillingInfo& billingInfo) const {
        return billingInfo.IsValid() && billingInfo.lRemainTime > 0;
    }

    bool CBillingLogin::ProcessLogin(const UserBillingData& userData) {
        if (!ValidateBillingInfo(userData.billingInfo)) {
            return false;
        }

        // Send login message
        std::string ipStr = userData.GetIPAddressString();
        if (!SendLoginMessage(userData.szAccountID, ipStr, userData.billingInfo.szCMS)) {
            return false;
        }

        // Add user to active users list
        {
            std::lock_guard<std::mutex> lock(m_usersMutex);
            m_activeUsers[userData.szAccountID] = userData;
        }

        // Set billing no logout flag (equivalent to CUserDB::SetBillingNoLogout(pUserDB, 0))
        // This would be handled by the calling code in the original implementation

        return true;
    }

    void CBillingLogin::CleanupExpiredUsers() {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        auto now = std::chrono::system_clock::now();
        
        for (auto it = m_activeUsers.begin(); it != m_activeUsers.end();) {
            const auto& billingInfo = it->second.billingInfo;
            if (!billingInfo.bIsActive || billingInfo.stEndDate < now) {
                it = m_activeUsers.erase(it);
            } else {
                ++it;
            }
        }
    }

    // CBillingIDLogin implementation

    CBillingIDLogin::CBillingIDLogin() = default;

    CBillingIDLogin::~CBillingIDLogin() = default;

    CBillingIDLogin::CBillingIDLogin(CBillingIDLogin&& other) noexcept
        : m_activeUsers(std::move(other.m_activeUsers))
        , m_pcBangMode(other.m_pcBangMode) {
        // Move atomic values manually
        m_isEnabled = other.m_isEnabled;
        m_totalLoginAttempts.store(other.m_totalLoginAttempts.load());
        m_successfulLogins.store(other.m_successfulLogins.load());
        m_failedLogins.store(other.m_failedLogins.load());

        // Reset other's values
        other.m_isEnabled = true;
        other.m_totalLoginAttempts.store(0);
        other.m_successfulLogins.store(0);
        other.m_failedLogins.store(0);
        other.m_pcBangMode = false;
    }

    CBillingIDLogin& CBillingIDLogin::operator=(CBillingIDLogin&& other) noexcept {
        if (this != &other) {
            std::lock_guard<std::mutex> lock(m_usersMutex);
            m_activeUsers = std::move(other.m_activeUsers);
            m_pcBangMode = other.m_pcBangMode;

            // Move atomic values manually
            m_isEnabled = other.m_isEnabled;
            m_totalLoginAttempts.store(other.m_totalLoginAttempts.load());
            m_successfulLogins.store(other.m_successfulLogins.load());
            m_failedLogins.store(other.m_failedLogins.load());

            // Reset other's values
            other.m_isEnabled = true;
            other.m_totalLoginAttempts.store(0);
            other.m_successfulLogins.store(0);
            other.m_failedLogins.store(0);
            other.m_pcBangMode = false;
        }
        return *this;
    }

    bool CBillingIDLogin::Login(const UserBillingData& userData) {
        if (!IsEnabled() || !userData.IsValid()) {
            UpdateStatistics(false);
            return false;
        }

        // Check if this is a PC Bang user (original condition: if (pUserDB->m_BillingInfo.bIsPcBang))
        bool success = false;
        if (userData.billingInfo.bIsPcBang) {
            success = ProcessPcBangLogin(userData);
        } else {
            // Non-PC Bang users are not processed in the original CBillingID::Login
            success = false;
        }

        UpdateStatistics(success);
        return success;
    }

    bool CBillingIDLogin::Logout(const std::string& accountID) {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        auto it = m_activeUsers.find(accountID);
        if (it != m_activeUsers.end()) {
            m_activeUsers.erase(it);
            return true;
        }
        return false;
    }

    bool CBillingIDLogin::IsLoggedIn(const std::string& accountID) const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return m_activeUsers.find(accountID) != m_activeUsers.end();
    }

    uint32_t CBillingIDLogin::GetActiveUserCount() const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return static_cast<uint32_t>(m_activeUsers.size());
    }

    std::string CBillingIDLogin::ToString() const {
        std::ostringstream oss;
        oss << "CBillingIDLogin{";
        oss << "Type: " << GetBillingTypeName() << ", ";
        oss << "Enabled: " << (IsEnabled() ? "true" : "false") << ", ";
        oss << "PcBangMode: " << (m_pcBangMode ? "true" : "false") << ", ";
        oss << "ActiveUsers: " << GetActiveUserCount() << ", ";
        oss << "TotalAttempts: " << GetTotalLoginAttempts() << ", ";
        oss << "Successful: " << GetSuccessfulLogins() << ", ";
        oss << "Failed: " << GetFailedLogins();
        oss << "}";
        return oss.str();
    }

    bool CBillingIDLogin::IsPcBangUser(const std::string& accountID) const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        auto it = m_activeUsers.find(accountID);
        return it != m_activeUsers.end() && it->second.billingInfo.bIsPcBang;
    }

    bool CBillingIDLogin::ProcessPcBangLogin(const UserBillingData& userData) {
        if (!ValidatePcBangUser(userData)) {
            return false;
        }

        // Send login message (equivalent to the original vtable call)
        std::string ipStr = userData.GetIPAddressString();
        // Original: ((void (__fastcall *)(CBillingID *, const char *, const char *, const char *))v11->SendMsg_Login)(...)
        
        // Add user to active users list
        {
            std::lock_guard<std::mutex> lock(m_usersMutex);
            m_activeUsers[userData.szAccountID] = userData;
        }

        // Set billing no logout flag (equivalent to CUserDB::SetBillingNoLogout(v13, 0))
        // This would be handled by the calling code

        return true;
    }

    bool CBillingIDLogin::ValidatePcBangUser(const UserBillingData& userData) const {
        return userData.billingInfo.bIsPcBang && userData.billingInfo.IsValid();
    }

    // CBillingJPLogin implementation

    CBillingJPLogin::CBillingJPLogin() = default;

    CBillingJPLogin::~CBillingJPLogin() = default;

    bool CBillingJPLogin::Login(const UserBillingData& userData) {
        if (!IsEnabled() || !userData.IsValid()) {
            UpdateStatistics(false);
            return false;
        }

        bool success = ProcessJapaneseLogin(userData);
        UpdateStatistics(success);
        return success;
    }

    bool CBillingJPLogin::Logout(const std::string& accountID) {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        auto it = m_activeUsers.find(accountID);
        if (it != m_activeUsers.end()) {
            m_activeUsers.erase(it);
            return true;
        }
        return false;
    }

    bool CBillingJPLogin::IsLoggedIn(const std::string& accountID) const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return m_activeUsers.find(accountID) != m_activeUsers.end();
    }

    uint32_t CBillingJPLogin::GetActiveUserCount() const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return static_cast<uint32_t>(m_activeUsers.size());
    }

    std::string CBillingJPLogin::ToString() const {
        std::ostringstream oss;
        oss << "CBillingJPLogin{";
        oss << "Type: " << GetBillingTypeName() << ", ";
        oss << "Enabled: " << (IsEnabled() ? "true" : "false") << ", ";
        oss << "Region: \"" << m_japaneseRegion << "\", ";
        oss << "ActiveUsers: " << GetActiveUserCount() << ", ";
        oss << "TotalAttempts: " << GetTotalLoginAttempts() << ", ";
        oss << "Successful: " << GetSuccessfulLogins() << ", ";
        oss << "Failed: " << GetFailedLogins();
        oss << "}";
        return oss.str();
    }

    bool CBillingJPLogin::ProcessJapaneseLogin(const UserBillingData& userData) {
        if (!ValidateJapaneseUser(userData)) {
            return false;
        }

        // Japanese-specific login processing
        std::lock_guard<std::mutex> lock(m_usersMutex);
        m_activeUsers[userData.szAccountID] = userData;
        return true;
    }

    bool CBillingJPLogin::ValidateJapaneseUser(const UserBillingData& userData) const {
        return userData.billingInfo.IsValid() && m_japaneseRegion == "JP";
    }

    // CBillingNULLLogin implementation

    CBillingNULLLogin::CBillingNULLLogin() = default;

    CBillingNULLLogin::~CBillingNULLLogin() = default;

    bool CBillingNULLLogin::Login(const UserBillingData& userData) {
        if (!IsEnabled() || !userData.IsValid()) {
            UpdateStatistics(false);
            return false;
        }

        bool success = false;
        if (CanAcceptNewUser()) {
            std::lock_guard<std::mutex> lock(m_usersMutex);
            m_activeUsers[userData.szAccountID] = userData;
            success = true;
        }

        UpdateStatistics(success);
        return success;
    }

    bool CBillingNULLLogin::Logout(const std::string& accountID) {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        auto it = m_activeUsers.find(accountID);
        if (it != m_activeUsers.end()) {
            m_activeUsers.erase(it);
            return true;
        }
        return false;
    }

    bool CBillingNULLLogin::IsLoggedIn(const std::string& accountID) const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return m_activeUsers.find(accountID) != m_activeUsers.end();
    }

    uint32_t CBillingNULLLogin::GetActiveUserCount() const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return static_cast<uint32_t>(m_activeUsers.size());
    }

    std::string CBillingNULLLogin::ToString() const {
        std::ostringstream oss;
        oss << "CBillingNULLLogin{";
        oss << "Type: " << GetBillingTypeName() << ", ";
        oss << "Enabled: " << (IsEnabled() ? "true" : "false") << ", ";
        oss << "FreeMode: " << (m_freeMode ? "true" : "false") << ", ";
        oss << "MaxUsers: " << m_maxFreeUsers << ", ";
        oss << "ActiveUsers: " << GetActiveUserCount() << ", ";
        oss << "TotalAttempts: " << GetTotalLoginAttempts() << ", ";
        oss << "Successful: " << GetSuccessfulLogins() << ", ";
        oss << "Failed: " << GetFailedLogins();
        oss << "}";
        return oss.str();
    }

    bool CBillingNULLLogin::CanAcceptNewUser() const {
        std::lock_guard<std::mutex> lock(m_usersMutex);
        return m_freeMode && m_activeUsers.size() < m_maxFreeUsers;
    }

    // CBillingManager implementation

    CBillingManager::CBillingManager() {
        InitializeDefaultProviders();
    }

    CBillingManager::~CBillingManager() {
        CleanupProviders();
    }

    bool CBillingManager::Initialize() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        if (m_isInitialized) {
            return true;
        }

        // Initialize all providers
        for (auto& provider : m_billingProviders) {
            if (provider) {
                provider->SetEnabled(true);
            }
        }

        m_isInitialized = true;
        return true;
    }

    void CBillingManager::Shutdown() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        if (!m_isInitialized) {
            return;
        }

        // Disable all providers
        for (auto& provider : m_billingProviders) {
            if (provider) {
                provider->SetEnabled(false);
            }
        }

        m_isInitialized = false;
    }

    bool CBillingManager::Login(const UserBillingData& userData) {
        if (!m_isInitialized || !userData.IsValid()) {
            return false;
        }

        BillingType billingType = DetermineBillingType(userData);
        IBillingLogin* provider = GetBillingProvider(billingType);

        if (provider && provider->IsEnabled()) {
            return provider->Login(userData);
        }

        return false;
    }

    bool CBillingManager::Logout(const std::string& accountID) {
        if (!m_isInitialized || accountID.empty()) {
            return false;
        }

        // Try to logout from all providers
        bool success = false;
        for (auto& provider : m_billingProviders) {
            if (provider && provider->IsEnabled()) {
                if (provider->Logout(accountID)) {
                    success = true;
                }
            }
        }

        return success;
    }

    void CBillingManager::SetBillingProvider(BillingType type, std::unique_ptr<IBillingLogin> provider) {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        size_t index = static_cast<size_t>(type);
        if (index < m_billingProviders.size()) {
            m_billingProviders[index] = std::move(provider);
        }
    }

    IBillingLogin* CBillingManager::GetBillingProvider(BillingType type) const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        size_t index = static_cast<size_t>(type);
        if (index < m_billingProviders.size()) {
            return m_billingProviders[index].get();
        }
        return nullptr;
    }

    uint32_t CBillingManager::GetTotalActiveUsers() const {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        uint32_t total = 0;
        for (const auto& provider : m_billingProviders) {
            if (provider) {
                total += provider->GetActiveUserCount();
            }
        }
        return total;
    }

    uint32_t CBillingManager::GetActiveUsersByType(BillingType type) const {
        IBillingLogin* provider = GetBillingProvider(type);
        return provider ? provider->GetActiveUserCount() : 0;
    }

    std::vector<std::string> CBillingManager::GetActiveUserList() const {
        std::vector<std::string> userList;
        // This would require additional interface methods to get user lists from providers
        // For now, return empty list
        return userList;
    }

    bool CBillingManager::LoadConfiguration(const std::string& configPath) {
        try {
            std::ifstream file(configPath);
            if (!file.is_open()) {
                return false;
            }

            // Simple configuration loading - in production, use proper JSON/XML parser
            std::string line;
            while (std::getline(file, line)) {
                // Parse configuration lines
                // This is a simplified implementation
            }

            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    bool CBillingManager::SaveConfiguration(const std::string& configPath) const {
        try {
            std::ofstream file(configPath);
            if (!file.is_open()) {
                return false;
            }

            // Simple configuration saving
            file << "DefaultBillingType=" << static_cast<int>(m_defaultBillingType) << std::endl;

            return true;
        } catch (const std::exception&) {
            return false;
        }
    }

    std::string CBillingManager::BillingTypeToString(BillingType type) {
        switch (type) {
            case BillingType::STANDARD: return "Standard";
            case BillingType::ID_BASED: return "ID-based";
            case BillingType::JAPANESE: return "Japanese";
            case BillingType::NULL_BILLING: return "NULL/Free";
            default: return "Unknown";
        }
    }

    CBillingManager::BillingType CBillingManager::StringToBillingType(const std::string& typeStr) {
        if (typeStr == "Standard") return BillingType::STANDARD;
        if (typeStr == "ID-based") return BillingType::ID_BASED;
        if (typeStr == "Japanese") return BillingType::JAPANESE;
        if (typeStr == "NULL/Free") return BillingType::NULL_BILLING;
        return BillingType::UNKNOWN;
    }

    std::string CBillingManager::ToString() const {
        std::ostringstream oss;
        oss << "CBillingManager{";
        oss << "Initialized: " << (m_isInitialized ? "true" : "false") << ", ";
        oss << "DefaultType: " << BillingTypeToString(m_defaultBillingType) << ", ";
        oss << "TotalActiveUsers: " << GetTotalActiveUsers();
        oss << "}";
        return oss.str();
    }

    CBillingManager::BillingType CBillingManager::DetermineBillingType(const UserBillingData& userData) const {
        // Determine billing type based on user data
        if (userData.billingInfo.bIsPcBang) {
            return BillingType::ID_BASED;
        }

        // Check for Japanese users (simplified logic)
        if (userData.billingInfo.szCMS.find("JP") != std::string::npos) {
            return BillingType::JAPANESE;
        }

        // Check for NULL billing (free users)
        if (userData.billingInfo.iType == 0 || userData.billingInfo.lRemainTime <= 0) {
            return BillingType::NULL_BILLING;
        }

        return m_defaultBillingType;
    }

    void CBillingManager::InitializeDefaultProviders() {
        m_billingProviders[static_cast<size_t>(BillingType::STANDARD)] = std::make_unique<CBillingLogin>();
        m_billingProviders[static_cast<size_t>(BillingType::ID_BASED)] = std::make_unique<CBillingIDLogin>();
        m_billingProviders[static_cast<size_t>(BillingType::JAPANESE)] = std::make_unique<CBillingJPLogin>();
        m_billingProviders[static_cast<size_t>(BillingType::NULL_BILLING)] = std::make_unique<CBillingNULLLogin>();
    }

    void CBillingManager::CleanupProviders() {
        std::lock_guard<std::mutex> lock(m_managerMutex);
        for (auto& provider : m_billingProviders) {
            provider.reset();
        }
    }

    // BillingLoginFactory implementation

    std::unique_ptr<IBillingLogin> BillingLoginFactory::CreateBillingLogin(CBillingManager::BillingType type) {
        switch (type) {
            case CBillingManager::BillingType::STANDARD:
                return std::make_unique<CBillingLogin>();
            case CBillingManager::BillingType::ID_BASED:
                return std::make_unique<CBillingIDLogin>();
            case CBillingManager::BillingType::JAPANESE:
                return std::make_unique<CBillingJPLogin>();
            case CBillingManager::BillingType::NULL_BILLING:
                return std::make_unique<CBillingNULLLogin>();
            default:
                return nullptr;
        }
    }

    std::unique_ptr<CBillingManager> BillingLoginFactory::CreateBillingManager() {
        return std::make_unique<CBillingManager>();
    }

    std::unique_ptr<CBillingManager> BillingLoginFactory::CreateStandardBillingManager() {
        auto manager = std::make_unique<CBillingManager>();
        manager->SetDefaultBillingType(CBillingManager::BillingType::STANDARD);
        manager->Initialize();
        return manager;
    }

    std::unique_ptr<CBillingManager> BillingLoginFactory::CreateTestBillingManager() {
        auto manager = std::make_unique<CBillingManager>();
        manager->SetDefaultBillingType(CBillingManager::BillingType::NULL_BILLING);

        // Configure for testing
        auto nullProvider = std::make_unique<CBillingNULLLogin>();
        nullProvider->SetFreeMode(true);
        nullProvider->SetMaxFreeUsers(100);
        manager->SetBillingProvider(CBillingManager::BillingType::NULL_BILLING, std::move(nullProvider));

        manager->Initialize();
        return manager;
    }

    std::unique_ptr<CBillingManager> BillingLoginFactory::CreateProductionBillingManager() {
        auto manager = std::make_unique<CBillingManager>();
        manager->SetDefaultBillingType(CBillingManager::BillingType::STANDARD);

        // Configure for production
        auto idProvider = std::make_unique<CBillingIDLogin>();
        idProvider->SetPcBangMode(true);
        manager->SetBillingProvider(CBillingManager::BillingType::ID_BASED, std::move(idProvider));

        manager->Initialize();
        return manager;
    }

} // namespace NexusProtection::Authentication
