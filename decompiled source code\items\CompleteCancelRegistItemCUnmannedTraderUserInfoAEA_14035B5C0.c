/*
 * Function: ?CompleteCancelRegistItem@CUnmannedTraderUserInfo@@AEAA_NKGPEAVCLogFile@@@Z
 * Address: 0x14035B5C0
 */

char __fastcall CUnmannedTraderUserInfo::CompleteCancelRegistItem(CUnmannedTraderUserInfo *this, unsigned int dwRegistSerial, unsigned __int16 dwItemSerial, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@5
  CUnmannedTraderRegistItemInfo *v7; // rax@6
  CUnmannedTraderRegistItemInfo *v8; // rax@7
  unsigned __int16 v9; // ax@7
  CUnmannedTraderRegistItemInfo *v10; // rax@8
  __int64 v11; // [sp+0h] [bp-98h]@1
  int v12; // [sp+20h] [bp-78h]@5
  int v13; // [sp+28h] [bp-70h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+38h] [bp-60h]@4
  bool v15; // [sp+54h] [bp-44h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v16; // [sp+58h] [bp-40h]@4
  char v17; // [sp+70h] [bp-28h]@5
  char v18; // [sp+71h] [bp-27h]@8
  __int64 v19; // [sp+78h] [bp-20h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v20; // [sp+80h] [bp-18h]@4
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v21; // [sp+88h] [bp-10h]@4
  CUnmannedTraderUserInfo *v22; // [sp+A0h] [bp+8h]@1
  int dwRegistSeriala; // [sp+A8h] [bp+10h]@1
  unsigned __int16 v24; // [sp+B0h] [bp+18h]@1
  CLogFile *v25; // [sp+B8h] [bp+20h]@1

  v25 = pkLogger;
  v24 = dwItemSerial;
  dwRegistSeriala = dwRegistSerial;
  v22 = this;
  v4 = &v11;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v19 = -2i64;
  CUnmannedTraderUserInfo::Find(v22, &result, dwRegistSerial);
  v20 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
          &v22->m_vecRegistItemInfo,
          &v16);
  v21 = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v20;
  v15 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v20->_Mycont,
          (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont);
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v16);
  if ( v15 )
  {
    v12 = dwRegistSeriala;
    CLogFile::Write(
      v25,
      "CUnmannedTraderUserInfo::CompleteCancelRegistItem( DWORD dwRegistSerial(%u), WORD dwItemSerial(%u) )\r\n"
      "\t\t Find( dwRegistSerial(%u) ) NULL!\r\n",
      (unsigned int)dwRegistSeriala,
      v24);
    v17 = 0;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v6 = v17;
  }
  else
  {
    v7 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    if ( CUnmannedTraderRegistItemInfo::GetItemSerial(v7) != v24 )
    {
      v8 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator*(&result);
      v9 = CUnmannedTraderRegistItemInfo::GetItemSerial(v8);
      v13 = dwRegistSeriala;
      v12 = v9;
      CLogFile::Write(
        v25,
        "CUnmannedTraderUserInfo::CompleteCancelRegistItem( DWORD dwRegistSerial(%u), WORD dwItemSerial(%u) )\r\n"
        "\t\t (*iFind).GetItemSerial()(%u) != dwItemSerial(%u) Invalid!\r\n",
        (unsigned int)dwRegistSeriala,
        v24);
    }
    v10 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
    CUnmannedTraderRegistItemInfo::ClearRegist(v10);
    CUnmannedTraderUserInfo::CountRegistItem(v22);
    v18 = 1;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
    v6 = v18;
  }
  return v6;
}
