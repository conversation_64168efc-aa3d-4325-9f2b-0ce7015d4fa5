/*
 * Function: ?guild_est_money@CMgrAvatorItemHistory@@QEAAXHPEADKK0@Z
 * Address: 0x14023AFC0
 */

void __fastcall CMgrAvatorItemHistory::guild_est_money(CMgrAvatorItemHistory *this, int n, char *pszGuildName, unsigned int dwEstDalant, unsigned int dwLeftDalant, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-48h]@1
  unsigned int v9; // [sp+20h] [bp-28h]@4
  char *v10; // [sp+28h] [bp-20h]@4
  char *v11; // [sp+30h] [bp-18h]@4
  CMgrAvatorItemHistory *v12; // [sp+50h] [bp+8h]@1

  v12 = this;
  v6 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v11 = v12->m_szCurTime;
  v10 = v12->m_szCurDate;
  v9 = dwLeftDalant;
  sprintf(sData, "GUILD EST PAY: guild(%s) pay(D:%u) $D:%u [%s %s]\r\n", pszGuildName);
  CMgrAvatorItemHistory::WriteFile(v12, pszFileName, sData);
}
