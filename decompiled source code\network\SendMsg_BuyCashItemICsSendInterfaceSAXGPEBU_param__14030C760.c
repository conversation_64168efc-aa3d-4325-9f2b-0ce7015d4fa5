/*
 * Function: ?SendMsg_BuyCashItem@ICsSendInterface@@SAXGPEBU_param_cash_update@@0@Z
 * Address: 0x14030C760
 */

void __fastcall ICsSendInterface::SendMsg_BuyCashItem(unsigned __int16 wSock, _param_cash_update *psheet, _param_cash_update *sheetplus)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@20
  __int64 v6; // [sp+0h] [bp-3A8h]@1
  _result_csi_buy_zocl v7; // [sp+40h] [bp-368h]@4
  int j; // [sp+374h] [bp-34h]@4
  int k; // [sp+378h] [bp-30h]@13
  char pbyType; // [sp+384h] [bp-24h]@20
  char v11; // [sp+385h] [bp-23h]@20
  unsigned __int16 v12; // [sp+3B0h] [bp+8h]@1
  _param_cash_update *v13; // [sp+3B8h] [bp+10h]@1
  _param_cash_update *v14; // [sp+3C0h] [bp+18h]@1

  v14 = sheetplus;
  v13 = psheet;
  v12 = wSock;
  v3 = &v6;
  for ( i = 232i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _result_csi_buy_zocl::_result_csi_buy_zocl(&v7);
  v7.nNum = 0;
  v7.nCashAmount = v13->out_nCashAmount;
  for ( j = 0; j < v13->in_nNum10; ++j )
  {
    if ( !v13->in_item[(signed __int64)j].out_cState )
    {
      v7.item[(unsigned __int8)v7.nNum].byTblCode = v13->in_item[(signed __int64)j].in_byTblCode;
      v7.item[(unsigned __int8)v7.nNum].wItemIdx = v13->in_item[(signed __int64)j].in_wItemIdx;
      if ( IsOverLapItem(v13->in_item[(signed __int64)j].in_byTblCode) )
        v7.item[(unsigned __int8)v7.nNum].dwDur = v13->in_item[(signed __int64)j].in_byOverlapNum;
      else
        v7.item[(unsigned __int8)v7.nNum].dwDur = GetItemDurPoint(
                                                    v13->in_item[(signed __int64)j].in_byTblCode,
                                                    v13->in_item[(signed __int64)j].in_wItemIdx);
      v7.item[(unsigned __int8)v7.nNum].dwUp = (unsigned __int8)GetDefItemUpgSocketNum(
                                                                  v13->in_item[(signed __int64)j].in_byTblCode,
                                                                  v13->in_item[(signed __int64)j].in_wItemIdx);
      v7.item[(unsigned __int8)v7.nNum].dwItemSerial = v13->in_item[(signed __int64)j].out_wItemSerial;
      v7.item[(unsigned __int8)v7.nNum].byCsMethod = v13->in_item[(signed __int64)j].in_nLendType;
      v7.item[(unsigned __int8)v7.nNum].dwT = v13->in_item[(signed __int64)j].out_dwT;
      ++v7.nNum;
    }
  }
  if ( v14 )
  {
    for ( k = 0; k < v14->in_nNum10; ++k )
    {
      v7.item[(unsigned __int8)v7.nNum].byTblCode = v14->in_item[(signed __int64)k].in_byTblCode;
      v7.item[(unsigned __int8)v7.nNum].wItemIdx = v14->in_item[(signed __int64)k].in_wItemIdx;
      if ( IsOverLapItem(v14->in_item[(signed __int64)k].in_byTblCode) )
        v7.item[(unsigned __int8)v7.nNum].dwDur = v14->in_item[(signed __int64)k].in_byOverlapNum;
      else
        v7.item[(unsigned __int8)v7.nNum].dwDur = GetItemDurPoint(
                                                    v14->in_item[(signed __int64)k].in_byTblCode,
                                                    v14->in_item[(signed __int64)k].in_wItemIdx);
      v7.item[(unsigned __int8)v7.nNum].dwUp = (unsigned __int8)GetDefItemUpgSocketNum(
                                                                  v14->in_item[(signed __int64)k].in_byTblCode,
                                                                  v14->in_item[(signed __int64)k].in_wItemIdx);
      v7.item[(unsigned __int8)v7.nNum].dwItemSerial = v14->in_item[(signed __int64)k].out_wItemSerial;
      v7.item[(unsigned __int8)v7.nNum].byCsMethod = v14->in_item[(signed __int64)k].in_nLendType;
      v7.item[(unsigned __int8)v7.nNum].dwT = v14->in_item[(signed __int64)k].out_dwT;
      ++v7.nNum;
    }
  }
  v7.bAdjustDiscount = v13->in_bAdjustDiscount;
  if ( (signed int)(unsigned __int8)v7.nNum >= 1 )
  {
    pbyType = 57;
    v11 = 4;
    v5 = _result_csi_buy_zocl::size(&v7);
    CNetProcess::LoadSendMsg(unk_1414F2088, v12, &pbyType, (char *)&v7, v5);
  }
}
