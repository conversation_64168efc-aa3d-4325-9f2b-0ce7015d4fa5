/*
 * Function: ?Initialize@OutputProxy@CryptoPP@@UEAAXAEBVNameValuePairs@2@H@Z
 * Address: 0x1405FEE70
 */

void __fastcall CryptoPP::OutputProxy::Initialize(CryptoPP::OutputProxy *this, const struct CryptoPP::NameValuePairs *a2, unsigned int a3)
{
  __int64 v3; // rax@2
  const struct CryptoPP::NameValuePairs *v4; // [sp+48h] [bp+10h]@1
  unsigned int v5; // [sp+50h] [bp+18h]@1

  v5 = a3;
  v4 = a2;
  if ( this->m_passSignal )
  {
    LODWORD(v3) = ((int (__fastcall *)(CryptoPP::BufferedTransformation *))this->m_owner->vfptr[20].Clone)(this->m_owner);
    (*(void (__fastcall **)(__int64, const struct CryptoPP::NameValuePairs *, _QWORD))(*(_QWORD *)v3 + 80i64))(
      v3,
      v4,
      v5);
  }
}
