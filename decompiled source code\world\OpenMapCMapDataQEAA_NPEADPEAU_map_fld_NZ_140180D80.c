/*
 * Function: ?OpenMap@CMapData@@QEAA_NPEADPEAU_map_fld@@_N@Z
 * Address: 0x140180D80
 */

char __fastcall CMapData::OpenMap(CMapData *this, char *pszMapCode, _map_fld *pMapSet, bool bUse)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  signed __int64 v7; // rax@12
  unsigned __int8 v8; // cf@14
  unsigned __int64 v9; // rax@14
  signed __int64 v10; // rax@19
  unsigned __int64 v11; // rax@21
  __int64 v12; // rax@30
  __int64 v13; // [sp+0h] [bp-158h]@1
  void (__cdecl *pDtor)(void *); // [sp+20h] [bp-138h]@5
  char Dest; // [sp+40h] [bp-118h]@26
  int j; // [sp+C4h] [bp-94h]@39
  int count[2]; // [sp+D0h] [bp-88h]@12
  _LAYER_SET *v18; // [sp+D8h] [bp-80h]@19
  void *v19; // [sp+E0h] [bp-78h]@16
  int v20[2]; // [sp+E8h] [bp-70h]@19
  _MULTI_BLOCK *v21; // [sp+F0h] [bp-68h]@26
  void *v22; // [sp+F8h] [bp-60h]@23
  CExtDummy *v23; // [sp+100h] [bp-58h]@32
  void *v24; // [sp+108h] [bp-50h]@29
  CExtDummy *v25; // [sp+110h] [bp-48h]@33
  CExtDummy *v26; // [sp+118h] [bp-40h]@33
  __int64 v27; // [sp+120h] [bp-38h]@4
  _LAYER_SET *v28; // [sp+128h] [bp-30h]@17
  _MULTI_BLOCK *v29; // [sp+130h] [bp-28h]@24
  CExtDummy *v30; // [sp+138h] [bp-20h]@30
  void *v31; // [sp+140h] [bp-18h]@34
  unsigned __int64 v32; // [sp+148h] [bp-10h]@4
  CMapData *v33; // [sp+160h] [bp+8h]@1
  char *pszMapCodea; // [sp+168h] [bp+10h]@1
  _map_fld *pMapFld; // [sp+170h] [bp+18h]@1
  bool v36; // [sp+178h] [bp+20h]@1

  v36 = bUse;
  pMapFld = pMapSet;
  pszMapCodea = pszMapCode;
  v33 = this;
  v4 = &v13;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v27 = -2i64;
  v32 = (unsigned __int64)&v13 ^ _security_cookie;
  if ( pMapSet->m_nLayerNum <= 500 )
  {
    if ( !pMapSet->m_nMapType && pMapSet->m_nLayerNum > 1 )
      pMapSet->m_nLayerNum = 1;
    if ( !pMapSet->m_nMapType && pMapSet->m_nMonsterSetFileNum > 1 )
      pMapSet->m_nMonsterSetFileNum = 1;
    *(_QWORD *)count = pMapSet->m_nLayerNum;
    v7 = 2400048i64 * *(_QWORD *)count;
    if ( !is_mul_ok(0x249F30ui64, *(unsigned __int64 *)count) )
      v7 = -1i64;
    v8 = __CFADD__(v7, 8i64);
    v9 = v7 + 8;
    if ( v8 )
      v9 = -1i64;
    v19 = operator new[](v9);
    if ( v19 )
    {
      *(_DWORD *)v19 = count[0];
      `eh vector constructor iterator'(
        (char *)v19 + 8,
        0x249F30ui64,
        count[0],
        (void (__cdecl *)(void *))_LAYER_SET::_LAYER_SET,
        (void (__cdecl *)(void *))_LAYER_SET::~_LAYER_SET);
      v28 = (_LAYER_SET *)((char *)v19 + 8);
    }
    else
    {
      v28 = 0i64;
    }
    v18 = v28;
    v33->m_ls = v28;
    *(_QWORD *)v20 = pMapFld->m_nMonsterSetFileNum;
    v10 = 24i64 * *(_QWORD *)v20;
    if ( !is_mul_ok(0x18ui64, *(unsigned __int64 *)v20) )
      v10 = -1i64;
    v8 = __CFADD__(v10, 8i64);
    v11 = v10 + 8;
    if ( v8 )
      v11 = -1i64;
    v22 = operator new[](v11);
    if ( v22 )
    {
      *(_DWORD *)v22 = v20[0];
      `eh vector constructor iterator'(
        (char *)v22 + 8,
        0x18ui64,
        v20[0],
        (void (__cdecl *)(void *))_MULTI_BLOCK::_MULTI_BLOCK,
        (void (__cdecl *)(void *))_MULTI_BLOCK::~_MULTI_BLOCK);
      v29 = (_MULTI_BLOCK *)((char *)v22 + 8);
    }
    else
    {
      v29 = 0i64;
    }
    v21 = v29;
    v33->m_mb = v29;
    v33->m_pMapSet = pMapFld;
    v33->m_bUse = v36;
    v33->m_nMapIndex = pMapFld->m_dwIndex;
    sprintf(&Dest, ".\\map\\%s\\%s.spt", pszMapCodea, pszMapCodea);
    if ( CExtDummy::LoadExtDummy(&v33->m_Dummy, &Dest) )
    {
      sprintf(&Dest, ".\\map\\%s\\%sEXT.spt", pszMapCodea, pszMapCodea);
      if ( CalcFileSize(&Dest) <= 0
        || ((v24 = operator new(0x10ui64)) == 0i64 ? (v30 = 0i64) : (LODWORD(v12) = CExtDummy::CExtDummy(v24),
                                                                     v30 = (CExtDummy *)v12),
            v23 = v30,
            v33->m_pExtDummy_Town = v30,
            CExtDummy::LoadExtDummy(v33->m_pExtDummy_Town, &Dest)) )
      {
        if ( CMapData::_LoadBspSec(v33, pszMapCodea) )
        {
          for ( j = 0; j < pMapFld->m_nLayerNum; ++j )
            _LAYER_SET::CreateLayer(&v33->m_ls[j], v33->m_SecInfo.m_nSecNum);
          if ( CMapData::_LoadMonBlk(v33, pszMapCodea, pMapFld) )
          {
            if ( CMapData::_LoadPortal(v33, pszMapCodea) )
            {
              if ( CMapData::_LoadStoreDummy(v33, pszMapCodea) )
              {
                if ( CMapData::_LoadStart(v33, pszMapCodea) )
                {
                  if ( CMapData::_LoadResource(v33, pszMapCodea) )
                  {
                    if ( CMapData::_LoadBind(v33, pszMapCodea) )
                    {
                      if ( CMapData::_LoadQuest(v33, pszMapCodea) )
                      {
                        if ( CMapData::_LoadSafe(v33, pszMapCodea) )
                        {
                          CMyTimer::BeginTimer(&v33->m_tmrMineGradeReSet, 0x1B7740u);
                          v33->m_bLoad = 1;
                          if ( !pMapFld->m_nMapType )
                            _LAYER_SET::ActiveLayer(v33->m_ls, v33->m_mb);
                          result = 1;
                        }
                        else
                        {
                          result = 0;
                        }
                      }
                      else
                      {
                        result = 0;
                      }
                    }
                    else
                    {
                      result = 0;
                    }
                  }
                  else
                  {
                    result = 0;
                  }
                }
                else
                {
                  result = 0;
                }
              }
              else
              {
                result = 0;
              }
            }
            else
            {
              result = 0;
            }
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        v26 = v33->m_pExtDummy_Town;
        v25 = v26;
        if ( v26 )
          v31 = CExtDummy::`scalar deleting destructor'(v25, 1u);
        else
          v31 = 0i64;
        v33->m_pExtDummy_Town = 0i64;
        MyMessageBox("CMapData Error", "read town-dummy fail(%s)", pszMapCodea);
        result = 0;
      }
    }
    else
    {
      MyMessageBox("CMapData Error", "read dummy-list fail(%s)", pszMapCodea);
      result = 0;
    }
  }
  else
  {
    LODWORD(pDtor) = 500;
    MyMessageBox("CMapData Error", "%s: layer num (%d > %d)..", pszMapCode, pMapSet->m_nLayerNum);
    result = 0;
  }
  return result;
}
