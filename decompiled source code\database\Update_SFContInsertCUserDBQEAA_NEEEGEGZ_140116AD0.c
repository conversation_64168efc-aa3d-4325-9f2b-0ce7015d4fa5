/*
 * Function: ?Update_SFContInsert@CUserDB@@QEAA_NEEEGEG@Z
 * Address: 0x140116AD0
 */

char __fastcall CUserDB::Update_SFContInsert(CUserDB *this, char byContCode, char bySlotIndex, char byEffectCode, unsigned __int16 wEffectIndex, char byLv, unsigned __int16 wDurSec)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v10; // [sp+0h] [bp-38h]@1
  char pl_byLv[4]; // [sp+20h] [bp-18h]@9
  CUserDB *v12; // [sp+40h] [bp+8h]@1
  char v13; // [sp+48h] [bp+10h]@1
  char v14; // [sp+50h] [bp+18h]@1
  char v15; // [sp+58h] [bp+20h]@1

  v15 = byEffectCode;
  v14 = bySlotIndex;
  v13 = byContCode;
  v12 = this;
  v7 = &v10;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  if ( (signed int)(unsigned __int8)byContCode < 2 )
  {
    if ( (signed int)(unsigned __int8)bySlotIndex < 8 )
    {
      if ( _SFCONT_DB_BASE::_LIST::IsFilled((_SFCONT_DB_BASE::_LIST *)&v12->m_AvatorData.dbSfcont + 8 * (unsigned __int8)byContCode + (unsigned __int8)bySlotIndex) )
      {
        *(_DWORD *)pl_byLv = (unsigned __int8)v14;
        CLogFile::Write(
          &stru_1799C8E78,
          "%s : Update_SFContInsert(EXIST) : code : %d, slot : %d",
          v12->m_aszAvatorName,
          (unsigned __int8)v13);
        result = 0;
      }
      else
      {
        _SFCONT_DB_BASE::_LIST::SetKey(
          (_SFCONT_DB_BASE::_LIST *)&v12->m_AvatorData.dbSfcont + 8 * (unsigned __int8)v13 + (unsigned __int8)v14,
          0,
          v15,
          wEffectIndex,
          byLv,
          wDurSec);
        result = 1;
      }
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_SFContInsert(SlotIndex OVER) : slot : %d",
        v12->m_aszAvatorName,
        (unsigned __int8)bySlotIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_SFContInsert(byContCode OVER) : code : %d",
      v12->m_aszAvatorName,
      (unsigned __int8)byContCode);
    result = 0;
  }
  return result;
}
