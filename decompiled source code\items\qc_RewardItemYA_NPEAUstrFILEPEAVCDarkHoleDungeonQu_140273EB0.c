/*
 * Function: ?qc_RewardItem@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 * Address: 0x140273EB0
 */

char __fastcall qc_RewardItem(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // rax@37
  __int64 v7; // [sp+0h] [bp-208h]@1
  char poutszWord; // [sp+30h] [bp-1D8h]@4
  char v9; // [sp+B4h] [bp-154h]@6
  char psItemCode; // [sp+D0h] [bp-138h]@8
  _dh_reward_sub_setup *v11; // [sp+158h] [bp-B0h]@13
  unsigned __int8 v12; // [sp+160h] [bp-A8h]@13
  _base_fld *v13; // [sp+168h] [bp-A0h]@15
  char v14; // [sp+170h] [bp-98h]@17
  unsigned int v15; // [sp+174h] [bp-94h]@17
  char v16; // [sp+178h] [bp-90h]@18
  int v17; // [sp+17Ch] [bp-8Ch]@22
  int pnoutVal; // [sp+184h] [bp-84h]@22
  float pfoutVal; // [sp+1A4h] [bp-64h]@28
  int v20; // [sp+1B4h] [bp-54h]@33
  char *v21; // [sp+1B8h] [bp-50h]@39
  _STORAGE_LIST::_db_con *v22; // [sp+1C8h] [bp-40h]@39
  _STORAGE_LIST::_db_con *v23; // [sp+1D0h] [bp-38h]@36
  __int64 v24; // [sp+1D8h] [bp-30h]@4
  _dh_reward_sub_setup *v25; // [sp+1E0h] [bp-28h]@11
  _STORAGE_LIST::_db_con *v26; // [sp+1E8h] [bp-20h]@37
  unsigned __int64 v27; // [sp+1F0h] [bp-18h]@4
  strFILE *fstra; // [sp+210h] [bp+8h]@1
  CDarkHoleDungeonQuestSetup *pSetupa; // [sp+218h] [bp+10h]@1

  pSetupa = pSetup;
  fstra = fstr;
  v3 = &v7;
  for ( i = 128i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v24 = -2i64;
  v27 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( !strFILE::word(fstra, &poutszWord) )
    return _false(fstra, pSetupa);
  v9 = 0;
  if ( !strcmp_0(&poutszWord, "one") )
    v9 = 1;
  if ( !strFILE::word(fstra, &psItemCode) )
    return _false(fstra, pSetupa);
  if ( v9 )
    v25 = &pSetupa->m_pCurLoadQuest->RewardOne;
  else
    v25 = &pSetupa->m_pCurLoadQuest->RewardOther;
  v11 = v25;
  v12 = GetItemTableCode(&psItemCode);
  if ( (signed int)v12 >= 37 )
    return _false(fstra, pSetupa);
  v13 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v12, &psItemCode);
  if ( !v13 )
    return _false(fstra, pSetupa);
  v14 = GetItemKindCode(v12);
  v15 = 0xFFFFFFF;
  if ( v14 )
  {
    if ( v14 != 1 )
      return _false(fstra, pSetupa);
    v15 = GetMaxParamFromExp(v13->m_dwIndex, 0i64);
  }
  else
  {
    v16 = GetDefItemUpgSocketNum(v12, v13->m_dwIndex);
    v15 = GetBitAfterSetLimSocket(v16);
  }
  v17 = GetItemDurPoint(v12, v13->m_dwIndex);
  if ( strFILE::word(fstra, &pnoutVal) )
  {
    if ( IsOverLapItem(v12) )
    {
      if ( pnoutVal > 99 )
        pnoutVal = 99;
      v17 = pnoutVal;
    }
    pfoutVal = 0.0;
    if ( strFILE::word(fstra, &pfoutVal) )
    {
      if ( pfoutVal >= 0.0 && pfoutVal <= 1.0 )
      {
        v20 = v11->nItemNum;
        if ( (float)(pfoutVal * TSVR_ADD_DARKHOLE_REWARD_RATE) <= 1.0 )
          v11->m_dwGivePercent[v20] = (signed int)ffloor(pfoutVal * 2147450900.0);
        else
          v11->m_dwGivePercent[v20] = 2147450879;
        v23 = (_STORAGE_LIST::_db_con *)operator new(0x32ui64);
        if ( v23 )
        {
          _STORAGE_LIST::_db_con::_db_con(v23);
          v26 = (_STORAGE_LIST::_db_con *)v6;
        }
        else
        {
          v26 = 0i64;
        }
        v22 = v26;
        v11->Item[v20] = v26;
        v21 = &v11->Item[v20]->m_bLoad;
        v21[1] = v12;
        *(_WORD *)(v21 + 3) = v13->m_dwIndex;
        *(_QWORD *)(v21 + 5) = (unsigned int)v17;
        *(_DWORD *)(v21 + 13) = v15;
        ++v11->nItemNum;
        result = 1;
      }
      else
      {
        result = _false(fstra, pSetupa);
      }
    }
    else
    {
      result = _false(fstra, pSetupa);
    }
  }
  else
  {
    result = _false(fstra, pSetupa);
  }
  return result;
}
