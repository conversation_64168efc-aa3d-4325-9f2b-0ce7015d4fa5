/*
 * Function: ??$_Construct@VCUnmannedTraderRegistItemInfo@@V1@@std@@YAXPEAVCUnmannedTraderRegistItemInfo@@AEBV1@@Z
 * Address: 0x140363350
 */

void __fastcall std::_Construct<CUnmannedTraderRegistItemInfo,CUnmannedTraderRegistItemInfo>(CUnmannedTraderRegistItemInfo *_Ptr, CUnmannedTraderRegistItemInfo *_Val)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-B8h]@1
  void *_Where; // [sp+20h] [bp-98h]@4
  char *v6; // [sp+28h] [bp-90h]@4
  char v7; // [sp+30h] [bp-88h]@5
  CUnmannedTraderRegistItemInfo *v8; // [sp+C0h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *v9; // [sp+C8h] [bp+10h]@1

  v9 = _Val;
  v8 = _Ptr;
  v2 = &v4;
  for ( i = 42i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _Where = v8;
  v6 = (char *)operator new(0x68ui64, v8);
  if ( v6 )
  {
    qmemcpy(&v7, v9, 0x68ui64);
    qmemcpy(v6, &v7, 0x68ui64);
  }
}
