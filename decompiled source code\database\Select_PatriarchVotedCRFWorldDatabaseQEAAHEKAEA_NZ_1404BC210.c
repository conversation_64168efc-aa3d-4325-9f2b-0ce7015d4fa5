/*
 * Function: ?Select_PatriarchVoted@CRFWorldDatabase@@QEAAHEKAEA_N@Z
 * Address: 0x1404BC210
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PatriarchVoted(CRFWorldDatabase *this, char by<PERSON><PERSON>, unsigned int dwSerial, bool *bOverlapVote)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v7; // [sp+0h] [bp-2A8h]@1
  void *SQLStmt; // [sp+20h] [bp-288h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-280h]@22
  SQLLEN v10; // [sp+38h] [bp-270h]@22
  __int16 v11; // [sp+44h] [bp-264h]@9
  char Dest; // [sp+60h] [bp-248h]@4
  unsigned __int8 v13; // [sp+264h] [bp-44h]@16
  unsigned int TargetValue; // [sp+274h] [bp-34h]@22
  unsigned __int8 v15; // [sp+284h] [bp-24h]@24
  unsigned __int64 v16; // [sp+290h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+2B0h] [bp+8h]@1
  bool *v18; // [sp+2C8h] [bp+20h]@1

  v18 = bOverlapVote;
  v17 = this;
  v4 = &v7;
  for ( i = 168i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  sprintf(
    &Dest,
    "SELECT\tDATEDIFF(day, tmracebossvote, getdate()) as days FROM \t[dbo].[tbl_general] WHERE\tSerial = %d",
    dwSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, &Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v11 = SQLExecDirectA_0(v17->m_hStmtSelect, &Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        *v18 = 0;
        result = 2i64;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          *v18 = 0;
          v13 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
          v13 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v17->m_hStmtSelect, 1u, 4, &TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v15 = 0;
          if ( v11 == 100 )
          {
            *v18 = 0;
            v15 = 2;
          }
          else
          {
            SQLStmt = v17->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, &Dest, "SQLFetch", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
            v15 = 1;
          }
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = v15;
        }
        else
        {
          *v18 = TargetValue < 6;
          if ( v17->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", &Dest);
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", &Dest);
    result = 1i64;
  }
  return result;
}
