#include "../Headers/AddMonster.h"
#include <algorithm>
#include <sstream>
#include <cstring>
#include <stdexcept>

namespace NexusProtection::World {

    // ReactObj implementation

    ReactObj::ReactObj() {
        Initialize();
    }

    void ReactObj::Initialize() {
        m_isActive = false;
        m_signalHandlers.clear();
    }

    void ReactObj::Reset() {
        Initialize();
    }

    void ReactObj::ProcessSignal(const std::string& signal) {
        if (!m_isActive) {
            return;
        }

        auto it = m_signalHandlers.find(signal);
        if (it != m_signalHandlers.end() && it->second) {
            it->second();
        }
    }

    void ReactObj::RegisterSignalHandler(const std::string& signal, std::function<void()> handler) {
        m_signalHandlers[signal] = std::move(handler);
    }

    // ReactArea implementation

    ReactArea::ReactArea() {
        Initialize();
    }

    void ReactArea::Initialize() {
        m_isActive = false;
        m_x = 0.0f;
        m_y = 0.0f;
        m_width = 0.0f;
        m_height = 0.0f;
        m_areaHandlers.clear();
    }

    void ReactArea::Reset() {
        Initialize();
    }

    void ReactArea::SetArea(float x, float y, float width, float height) {
        m_x = x;
        m_y = y;
        m_width = width;
        m_height = height;
    }

    bool ReactArea::IsPointInArea(float x, float y) const {
        return (x >= m_x && x <= m_x + m_width &&
                y >= m_y && y <= m_y + m_height);
    }

    void ReactArea::ProcessAreaEvent(const std::string& event, float x, float y) {
        if (!m_isActive || !IsPointInArea(x, y)) {
            return;
        }

        auto it = m_areaHandlers.find(event);
        if (it != m_areaHandlers.end() && it->second) {
            it->second(x, y);
        }
    }

    // AddMonster implementation

    AddMonster::AddMonster() {
        Initialize();
    }

    AddMonster::AddMonster(const AddMonster& other)
        : m_reactObj(other.m_reactObj)
        , m_reactArea(other.m_reactArea)
        , m_isInitialized(other.m_isInitialized) {
    }

    AddMonster& AddMonster::operator=(const AddMonster& other) {
        if (this != &other) {
            m_reactObj = other.m_reactObj;
            m_reactArea = other.m_reactArea;
            m_isInitialized = other.m_isInitialized;
        }
        return *this;
    }

    AddMonster::AddMonster(AddMonster&& other) noexcept
        : m_reactObj(std::move(other.m_reactObj))
        , m_reactArea(std::move(other.m_reactArea))
        , m_isInitialized(other.m_isInitialized) {
        other.m_isInitialized = false;
    }

    AddMonster& AddMonster::operator=(AddMonster&& other) noexcept {
        if (this != &other) {
            m_reactObj = std::move(other.m_reactObj);
            m_reactArea = std::move(other.m_reactArea);
            m_isInitialized = other.m_isInitialized;
            other.m_isInitialized = false;
        }
        return *this;
    }

    void AddMonster::Initialize() {
        InitializeComponents();
        m_isInitialized = true;
    }

    void AddMonster::Reset() {
        ResetComponents();
        m_isInitialized = false;
    }

    bool AddMonster::IsValid() const {
        return m_isInitialized;
    }

    void AddMonster::ProcessMonsterAddition(const std::string& monsterType, float x, float y) {
        if (!IsValid()) {
            return;
        }

        // Process through reactive area
        m_reactArea.ProcessAreaEvent("monster_add", x, y);
        
        // Process through reactive object
        m_reactObj.ProcessSignal("monster_add_" + monsterType);
    }

    void AddMonster::HandleMonsterEvent(const std::string& event, const std::string& data) {
        if (!IsValid()) {
            return;
        }

        // Process through reactive object
        m_reactObj.ProcessSignal(event + "_" + data);
    }

    void AddMonster::ConfigureReactiveComponents(bool enableObj, bool enableArea) {
        m_reactObj.SetActive(enableObj);
        m_reactArea.SetActive(enableArea);
    }

    bool AddMonster::AreReactiveComponentsActive() const {
        return m_reactObj.IsActive() || m_reactArea.IsActive();
    }

    std::string AddMonster::ToString() const {
        std::ostringstream oss;
        oss << "AddMonster{";
        oss << "Initialized: " << (m_isInitialized ? "true" : "false") << ", ";
        oss << "ReactObj Active: " << (m_reactObj.IsActive() ? "true" : "false") << ", ";
        oss << "ReactArea Active: " << (m_reactArea.IsActive() ? "true" : "false");
        oss << "}";
        return oss.str();
    }

    size_t AddMonster::GetMemoryUsage() const {
        return sizeof(AddMonster) + 
               sizeof(ReactObj) + 
               sizeof(ReactArea);
    }

    void AddMonster::InitializeLegacy() {
        // Legacy initialization matching original constructor behavior
        Initialize();
    }

    bool AddMonster::ValidateConfiguration() const {
        return IsValid();
    }

    void AddMonster::InitializeComponents() {
        m_reactObj.Initialize();
        m_reactArea.Initialize();
    }

    void AddMonster::ResetComponents() {
        m_reactObj.Reset();
        m_reactArea.Reset();
    }

    // AddMonsterFactory implementation

    std::unique_ptr<AddMonster> AddMonsterFactory::CreateAddMonster() {
        return std::make_unique<AddMonster>();
    }

    std::unique_ptr<AddMonster> AddMonsterFactory::CreateAddMonster(bool enableReactObj, bool enableReactArea) {
        auto addMonster = std::make_unique<AddMonster>();
        addMonster->ConfigureReactiveComponents(enableReactObj, enableReactArea);
        return addMonster;
    }

    std::vector<std::unique_ptr<AddMonster>> AddMonsterFactory::CreateAddMonsters(size_t count) {
        std::vector<std::unique_ptr<AddMonster>> result;
        result.reserve(count);
        
        for (size_t i = 0; i < count; ++i) {
            result.push_back(CreateAddMonster());
        }
        
        return result;
    }

    // AddMonsterUtils implementation

    namespace AddMonsterUtils {

        bool ValidateAddMonster(const AddMonster& addMonster) {
            return addMonster.IsValid() && addMonster.ValidateConfiguration();
        }

        bool ValidateReactiveComponents(const ReactObj& reactObj, const ReactArea& reactArea) {
            // Basic validation - both components should be in a valid state
            return true; // Simplified validation
        }

        size_t CalculateMemoryFootprint(const AddMonster& addMonster) {
            return addMonster.GetMemoryUsage();
        }

        void ConfigureDefaultReactiveComponents(AddMonster& addMonster) {
            addMonster.ConfigureReactiveComponents(true, true);
        }

        void OptimizeReactiveComponents(AddMonster& addMonster) {
            // Optimization logic could be added here
            // For now, just ensure components are properly configured
            if (!addMonster.AreReactiveComponentsActive()) {
                ConfigureDefaultReactiveComponents(addMonster);
            }
        }

    } // namespace AddMonsterUtils

} // namespace NexusProtection::World

// Legacy C interface implementation
extern "C" {

    void __add_monster_Constructor(_add_monster* this_ptr) {
        if (this_ptr) {
            // Initialize pointers to null (matching original constructor behavior)
            this_ptr->ReactObj = nullptr;
            this_ptr->ReactArea = nullptr;

            // Clear padding
            std::memset(this_ptr->padding, 0, sizeof(this_ptr->padding));

            // Allocate and initialize reactive components
            this_ptr->ReactObj = new _react_obj();
            this_ptr->ReactArea = new _react_area();

            _react_obj_Constructor(static_cast<_react_obj*>(this_ptr->ReactObj));
            _react_area_Constructor(static_cast<_react_area*>(this_ptr->ReactArea));
        }
    }

    void __add_monster_Destructor(_add_monster* this_ptr) {
        if (this_ptr) {
            // Clean up reactive components
            if (this_ptr->ReactObj) {
                _react_obj_Destructor(static_cast<_react_obj*>(this_ptr->ReactObj));
                delete static_cast<_react_obj*>(this_ptr->ReactObj);
                this_ptr->ReactObj = nullptr;
            }

            if (this_ptr->ReactArea) {
                _react_area_Destructor(static_cast<_react_area*>(this_ptr->ReactArea));
                delete static_cast<_react_area*>(this_ptr->ReactArea);
                this_ptr->ReactArea = nullptr;
            }
        }
    }

    void _react_obj_Constructor(_react_obj* this_ptr) {
        if (this_ptr) {
            // Initialize data to zero (matching original constructor behavior)
            std::memset(this_ptr->data, 0, sizeof(this_ptr->data));
        }
    }

    void _react_obj_Destructor(_react_obj* this_ptr) {
        if (this_ptr) {
            // No cleanup needed for simple data structure
            std::memset(this_ptr->data, 0, sizeof(this_ptr->data));
        }
    }

    void _react_area_Constructor(_react_area* this_ptr) {
        if (this_ptr) {
            // Initialize data to zero (matching original constructor behavior)
            std::memset(this_ptr->data, 0, sizeof(this_ptr->data));
        }
    }

    void _react_area_Destructor(_react_area* this_ptr) {
        if (this_ptr) {
            // No cleanup needed for simple data structure
            std::memset(this_ptr->data, 0, sizeof(this_ptr->data));
        }
    }

    void __add_monster_Initialize(_add_monster* this_ptr) {
        if (this_ptr) {
            // Re-initialize components
            if (this_ptr->ReactObj) {
                _react_obj_Constructor(static_cast<_react_obj*>(this_ptr->ReactObj));
            }
            if (this_ptr->ReactArea) {
                _react_area_Constructor(static_cast<_react_area*>(this_ptr->ReactArea));
            }
        }
    }

    void __add_monster_Reset(_add_monster* this_ptr) {
        if (this_ptr) {
            __add_monster_Initialize(this_ptr);
        }
    }

    void* __add_monster_GetReactObj(_add_monster* this_ptr) {
        return (this_ptr) ? this_ptr->ReactObj : nullptr;
    }

    void* __add_monster_GetReactArea(_add_monster* this_ptr) {
        return (this_ptr) ? this_ptr->ReactArea : nullptr;
    }

} // extern "C"

// Global legacy compatibility
NexusProtection::World::AddMonster* g_pAddMonster = nullptr;
