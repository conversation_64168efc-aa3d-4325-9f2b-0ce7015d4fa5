/*
 * Function: ?SetDefPart@CMonster@@QEAAXPEAU_monster_fld@@@Z
 * Address: 0x140142B40
 */

void __fastcall CMonster::SetDefPart(CMonster *this, _monster_fld *pRecordSet)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  int v5; // [sp+20h] [bp-28h]@4
  float v6; // [sp+24h] [bp-24h]@4
  int k; // [sp+28h] [bp-20h]@5
  float v8; // [sp+2Ch] [bp-1Ch]@10
  int j; // [sp+30h] [bp-18h]@10
  CMonster *v10; // [sp+50h] [bp+8h]@1

  v10 = this;
  v2 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = (signed int)ffloor(pRecordSet->m_fWeakPart + 1.0);
  v6 = pRecordSet->m_fStdDefFc;
  if ( v5 )
  {
    if ( v5 <= 6 )
    {
      v8 = 0.0;
      for ( j = 0; j < 5; ++j )
      {
        v8 = (float)(rand() % 20) / 100.0;
        v10->m_DefPart[j] = (signed int)ffloor((float)(v6 * v8) + v6);
      }
      if ( v5 != 6 )
        v10->m_DefPart[v5 - 1] = (signed int)ffloor(v6);
    }
  }
  else
  {
    for ( k = 0; k < 5; ++k )
      v10->m_DefPart[k] = (signed int)ffloor(v6);
  }
}
