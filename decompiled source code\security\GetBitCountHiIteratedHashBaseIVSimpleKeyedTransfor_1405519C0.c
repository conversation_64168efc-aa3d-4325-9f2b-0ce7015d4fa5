/*
 * Function: ?GetBitCountHi@?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@IEBAIXZ
 * Address: 0x1405519C0
 */

__int64 __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation>>::GetBitCountHi(__int64 a1)
{
  return (unsigned int)((*(_DWORD *)(a1 + 16) >> 29) + 8 * *(_DWORD *)(a1 + 20));
}
