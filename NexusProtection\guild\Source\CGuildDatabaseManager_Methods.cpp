/**
 * @file CGuildDatabaseManager_Methods.cpp
 * @brief Additional methods for CGuildDatabaseManager
 * 
 * This file contains the callback methods and utility functions for the guild database manager.
 */

#include "../Headers/CGuildDatabaseManager.h"
#include <iostream>
#include <stdexcept>

namespace NexusProtection::Guild {

void CGuildDatabaseManager::SetGuildCreatedCallback(GuildCreatedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_guildCreatedCallback = std::move(callback);
}

void CGuildDatabaseManager::SetGuildDeletedCallback(GuildDeletedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_guildDeletedCallback = std::move(callback);
}

void CGuildDatabaseManager::SetGuildUpdatedCallback(GuildUpdatedCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_guildUpdatedCallback = std::move(callback);
}

void CGuildDatabaseManager::SetDatabaseErrorCallback(DatabaseErrorCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_databaseErrorCallback = std::move(callback);
}

void CGuildDatabaseManager::SetTransactionCallback(TransactionCallback callback) {
    std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_transactionCallback = std::move(callback);
}

void CGuildDatabaseManager::NotifyGuildCreated(uint32_t guildSerial, const std::string& guildName) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_guildCreatedCallback) {
            try {
                m_guildCreatedCallback(guildSerial, guildName);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in guild created callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyGuildCreated: " << e.what() << std::endl;
    }
}

void CGuildDatabaseManager::NotifyGuildDeleted(uint32_t guildSerial) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_guildDeletedCallback) {
            try {
                m_guildDeletedCallback(guildSerial);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in guild deleted callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyGuildDeleted: " << e.what() << std::endl;
    }
}

void CGuildDatabaseManager::NotifyGuildUpdated(uint32_t guildSerial, const std::string& operation) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_guildUpdatedCallback) {
            try {
                m_guildUpdatedCallback(guildSerial, operation);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in guild updated callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyGuildUpdated: " << e.what() << std::endl;
    }
}

void CGuildDatabaseManager::NotifyDatabaseError(GuildDatabaseOperation operation, const std::string& error) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_databaseErrorCallback) {
            try {
                m_databaseErrorCallback(operation, error);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in database error callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyDatabaseError: " << e.what() << std::endl;
    }
}

void CGuildDatabaseManager::NotifyTransaction(bool success, const std::string& operation) {
    try {
        std::lock_guard<std::mutex> lock(m_callbackMutex);
        if (m_transactionCallback) {
            try {
                m_transactionCallback(success, operation);
            } catch (const std::exception& e) {
                std::cerr << "[ERROR] Exception in transaction callback: " << e.what() << std::endl;
            }
        }
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Exception in NotifyTransaction: " << e.what() << std::endl;
    }
}

} // namespace NexusProtection::Guild

// Legacy C-style wrapper functions for compatibility
extern "C" {
    /**
     * @brief Legacy wrapper for guild insertion
     * 
     * @param memberSerials Array of member serials
     * @param guildName Guild name
     * @param race Guild race
     * @param guildSerial Output guild serial
     * @return Legacy result code (0 = success, 24 = error)
     */
    uint8_t CMainThread_db_Insert_guild(uint32_t* memberSerials, const char* guildName, uint8_t race, uint32_t* guildSerial) {
        return NexusProtection::Guild::CGuildDatabaseManager::GetInstance().db_Insert_guild(memberSerials, guildName, race, guildSerial);
    }
    
    /**
     * @brief Legacy wrapper for guild deletion
     * 
     * @param guildSerial Guild serial number
     * @return Legacy result code (0 = success, 24 = error)
     */
    uint8_t CMainThread_db_disjoint_guild(uint32_t guildSerial) {
        return NexusProtection::Guild::CGuildDatabaseManager::GetInstance().db_disjoint_guild(guildSerial);
    }
    
    /**
     * @brief Initialize guild database manager
     * 
     * @return true if successful, false otherwise
     */
    bool CGuildDatabaseManager_Initialize() {
        auto result = NexusProtection::Guild::CGuildDatabaseManager::GetInstance().Initialize();
        return result == NexusProtection::Guild::GuildDatabaseResult::Success;
    }
    
    /**
     * @brief Shutdown guild database manager
     * 
     * @return true if successful, false otherwise
     */
    bool CGuildDatabaseManager_Shutdown() {
        auto result = NexusProtection::Guild::CGuildDatabaseManager::GetInstance().Shutdown();
        return result == NexusProtection::Guild::GuildDatabaseResult::Success;
    }
    
    /**
     * @brief Check if guild exists
     * 
     * @param guildName Guild name to check
     * @return true if exists, false otherwise
     */
    bool CGuildDatabaseManager_GuildExists(const char* guildName) {
        if (!guildName) return false;
        return NexusProtection::Guild::CGuildDatabaseManager::GetInstance().GuildExists(guildName);
    }
    
    /**
     * @brief Get guild serial by name
     * 
     * @param guildName Guild name
     * @param guildSerial Output guild serial
     * @return true if found, false otherwise
     */
    bool CGuildDatabaseManager_GetGuildSerial(const char* guildName, uint32_t* guildSerial) {
        if (!guildName || !guildSerial) return false;
        return NexusProtection::Guild::CGuildDatabaseManager::GetInstance().GetGuildSerial(guildName, *guildSerial);
    }
    
    /**
     * @brief Validate guild name
     * 
     * @param guildName Guild name to validate
     * @return true if valid, false otherwise
     */
    bool CGuildDatabaseManager_ValidateGuildName(const char* guildName) {
        if (!guildName) return false;
        return NexusProtection::Guild::CGuildDatabaseManager::GetInstance().ValidateGuildName(guildName);
    }
    
    /**
     * @brief Get database success rate
     * 
     * @return Success rate as percentage (0.0 - 100.0)
     */
    double CGuildDatabaseManager_GetSuccessRate() {
        auto stats = NexusProtection::Guild::CGuildDatabaseManager::GetInstance().GetStatistics();
        return stats.GetSuccessRate();
    }
    
    /**
     * @brief Get total database operations
     * 
     * @return Total number of operations performed
     */
    uint64_t CGuildDatabaseManager_GetTotalOperations() {
        auto stats = NexusProtection::Guild::CGuildDatabaseManager::GetInstance().GetStatistics();
        return stats.successfulOperations.load() + stats.failedOperations.load();
    }
    
    /**
     * @brief Reset database statistics
     */
    void CGuildDatabaseManager_ResetStatistics() {
        NexusProtection::Guild::CGuildDatabaseManager::GetInstance().ResetStatistics();
    }
}
