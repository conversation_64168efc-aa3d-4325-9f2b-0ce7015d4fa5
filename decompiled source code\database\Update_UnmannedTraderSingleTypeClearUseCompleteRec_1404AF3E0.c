/*
 * Function: ?Update_UnmannedTraderSingleTypeClearUseCompleteRecord@CRFWorldDatabase@@QEAA_NKPEBU_SYSTEMTIME@@@Z
 * Address: 0x1404AF3E0
 */

char __fastcall CRFWorldDatabase::Update_UnmannedTraderSingleTypeClearUseCompleteRecord(CRFWorldDatabase *this, unsigned int dwSerial, _SYSTEMTIME *pkCurTime)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int v6; // ecx@6
  int v7; // edx@6
  int v8; // edi@6
  int v9; // er8@6
  int v10; // er9@6
  unsigned int v11; // er10@6
  int v12; // ecx@8
  int v13; // edx@8
  int v14; // edi@8
  int v15; // er8@8
  int v16; // er9@8
  unsigned int v17; // er10@8
  __int64 v18; // [sp+0h] [bp-4B8h]@1
  int v19; // [sp+20h] [bp-498h]@6
  int v20; // [sp+28h] [bp-490h]@6
  int v21; // [sp+30h] [bp-488h]@6
  int v22; // [sp+38h] [bp-480h]@6
  int v23; // [sp+40h] [bp-478h]@6
  int v24; // [sp+48h] [bp-470h]@6
  char Dest; // [sp+60h] [bp-458h]@4
  char v26; // [sp+61h] [bp-457h]@4
  char Dst; // [sp+478h] [bp-40h]@4
  unsigned __int64 v28; // [sp+4A0h] [bp-18h]@4
  CRFWorldDatabase *v29; // [sp+4C0h] [bp+8h]@1
  unsigned int v30; // [sp+4C8h] [bp+10h]@1
  _SYSTEMTIME *v31; // [sp+4D0h] [bp+18h]@1

  v31 = pkCurTime;
  v30 = dwSerial;
  v29 = this;
  v3 = &v18;
  for ( i = 300i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v28 = (unsigned __int64)&v18 ^ _security_cookie;
  Dest = 0;
  memset(&v26, 0, 0x3FFui64);
  memset_0(&Dst, 0, 0x10ui64);
  GetLocalTime((LPSYSTEMTIME)&Dst);
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 0);
  sprintf(&Dest, "{ CALL pUpdateClear_utsingleiteminfo(%u) }", v30);
  if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v29->vfptr, &Dest, 1) )
  {
    v6 = v31->wSecond;
    v7 = v31->wMinute;
    v8 = v31->wHour;
    v9 = v31->wDay;
    v10 = v31->wMonth;
    v11 = v31->wYear;
    v24 = v31->wMilliseconds;
    v23 = v6;
    v22 = v7;
    v21 = v8;
    v20 = v9;
    v19 = v10;
    sprintf(&Dest, "{ CALL pUpdateClear_utsellinfo(%u,'%04d-%02d-%02d %02d:%02d:%02d.%03d') }", v30, v11);
    if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v29->vfptr, &Dest, 1) )
    {
      v12 = v31->wSecond;
      v13 = v31->wMinute;
      v14 = v31->wHour;
      v15 = v31->wDay;
      v16 = v31->wMonth;
      v17 = v31->wYear;
      v24 = v31->wMilliseconds;
      v23 = v12;
      v22 = v13;
      v21 = v14;
      v20 = v15;
      v19 = v16;
      sprintf(&Dest, "{ CALL pUpdateClear_utresultinfo(%u,'%04d-%02d-%02d %02d:%02d:%02d.%03d') }", v30, v17);
      if ( CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v29->vfptr, &Dest, 1) )
      {
        CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v29->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
        result = 1;
      }
      else
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v29->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
        result = 0;
      }
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v29->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
      result = 0;
    }
  }
  else
  {
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v29->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v29->vfptr, 1);
    result = 0;
  }
  return result;
}
