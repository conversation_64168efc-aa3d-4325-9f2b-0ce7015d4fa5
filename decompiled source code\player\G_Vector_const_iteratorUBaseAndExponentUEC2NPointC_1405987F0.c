/*
 * Function: ??G?$_Vector_const_iterator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x1405987F0
 */

__int64 __fastcall std::_Vector_const_iterator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>>::operator-(__int64 a1, __int64 a2)
{
  __int128 v2; // ax@1
  __int128 v4; // tt@1

  v2 = *(_QWORD *)(a1 + 16) - *(_QWORD *)(a2 + 16);
  *(_QWORD *)&v4 = v2;
  *((_QWORD *)&v4 + 1) = *((_QWORD *)&v2 + 1);
  return v4 / 96;
}
