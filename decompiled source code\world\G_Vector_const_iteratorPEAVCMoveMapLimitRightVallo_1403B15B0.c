/*
 * Function: ??G?$_Vector_const_iterator@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEBA_JAEBV01@@Z
 * Address: 0x1403B15B0
 */

__int64 __fastcall std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::operator-(std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this, std::_Vector_const_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *_Right)
{
  return this->_Myptr - _Right->_Myptr;
}
