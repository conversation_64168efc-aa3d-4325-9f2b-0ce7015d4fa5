/*
 * Function: j_?make_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@PEAEEE_N0PEAD@Z
 * Address: 0x140011E41
 */

void __fastcall CMgrAvatorItemHistory::make_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pMaterial, char *pbyMtrNum, char byMaterialNum, char byRetCode, bool bInsert, _STORAGE_LIST::_db_con *pMakeItem, char *pszFileName)
{
  CMgrAvatorItemHistory::make_item(
    this,
    n,
    pMaterial,
    pbyMtrNum,
    byMaterialNum,
    byRetCode,
    bInsert,
    pMakeItem,
    pszFileName);
}
