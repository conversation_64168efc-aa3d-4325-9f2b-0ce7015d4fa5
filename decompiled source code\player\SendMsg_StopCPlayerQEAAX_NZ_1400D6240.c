/*
 * Function: ?SendMsg_Stop@CPlayer@@QEAAX_N@Z
 * Address: 0x1400D6240
 */

void __fastcall CPlayer::SendMsg_Stop(CPlayer *this, bool bAll)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-98h]@1
  char szMsg[4]; // [sp+38h] [bp-60h]@4
  __int16 pShort; // [sp+3Ch] [bp-5Ch]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v8; // [sp+65h] [bp-33h]@4
  unsigned __int64 v9; // [sp+80h] [bp-18h]@4
  CPlayer *v10; // [sp+A0h] [bp+8h]@1
  bool v11; // [sp+A8h] [bp+10h]@1

  v11 = bAll;
  v10 = this;
  v2 = &v4;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = (unsigned __int64)&v4 ^ _security_cookie;
  *(_DWORD *)szMsg = v10->m_dwObjSerial;
  FloatToShort(v10->m_fCurPos, &pShort, 3);
  pbyType = 4;
  v8 = 20;
  if ( v11 )
    CGameObject::CircleReport((CGameObject *)&v10->vfptr, &pbyType, szMsg, 10, 1);
  else
    CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, szMsg, 0xAu);
}
