/*
 * Function: ?_BossBirthWriteLog@CMonster@@QEAAXXZ
 * Address: 0x140143910
 */

void __fastcall CMonster::_BossBirthWriteLog(CMonster *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed int v3; // ecx@5
  signed int v4; // edx@5
  signed __int64 v5; // r8@5
  signed __int64 v6; // r9@5
  signed __int64 v7; // r10@5
  __int64 v8; // [sp+0h] [bp-48h]@1
  __int64 v9; // [sp+20h] [bp-28h]@5
  int v10; // [sp+28h] [bp-20h]@5
  int v11; // [sp+30h] [bp-18h]@5
  int v12; // [sp+38h] [bp-10h]@5
  CMonster *v13; // [sp+50h] [bp+8h]@1

  v13 = this;
  v1 = &v8;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v13->m_pMonRec->m_bMonsterCondition == 1 )
  {
    v3 = (signed int)ffloor(v13->m_fCurPos[1]);
    v4 = (signed int)ffloor(v13->m_fCurPos[0]);
    v5 = (signed __int64)v13->m_pCurMap->m_pMapSet->m_strCode;
    v6 = (signed __int64)v13->m_pMonRec->m_strName;
    v7 = (signed __int64)v13->m_pMonRec->m_strCode;
    v12 = (signed int)ffloor(v13->m_fCurPos[2]);
    v11 = v3;
    v10 = v4;
    v9 = v5;
    CLogFile::Write(&CMonster::s_logTrace_Boss_BirthAndDeath, "Birth %s >> %s Map(%s) Pos(%d, %d, %d)", v7, v6);
  }
}
