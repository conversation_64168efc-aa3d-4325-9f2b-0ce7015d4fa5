/*
 * Function: ?<PERSON><PERSON>y@CHoly<PERSON>eeper@@QEAA_NEPEAVCCharacter@@@Z
 * Address: 0x1401332E0
 */

bool __fastcall CHolyKeeper::Destroy(CHolyKeeper *this, char byD<PERSON>roy<PERSON><PERSON>, CCharacter *pAtter)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CHolyKeeper *v7; // [sp+30h] [bp+8h]@1
  char v8; // [sp+38h] [bp+10h]@1

  v8 = byDestroyCode;
  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7->m_dwLastDestroyTime = timeGetTime();
  v7->m_nMasterRace = -1;
  v7->m_pPosCreate = 0i64;
  v7->m_pPosActive = 0i64;
  v7->m_pPosCenter = 0i64;
  v7->m_bExit = 0;
  CHolyKeeper::SendMsg_Destroy(v7, v8);
  if ( v8 )
  {
    v7->m_dwObjSerial = -1;
    --CHolyKeeper::s_nLiveNum;
    result = CCharacter::Destroy((CCharacter *)&v7->vfptr);
  }
  else
  {
    CHolyKeeper::SetDropItem(v7);
    CHolyKeeper::DropItem(v7);
    result = 1;
  }
  return result;
}
