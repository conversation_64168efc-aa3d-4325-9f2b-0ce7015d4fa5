/*
 * Function: j_??$_Destroy_range@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@0AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@U_Scalar_ptr_iterator_tag@0@@Z
 * Address: 0x1400035C1
 */

void __fastcall std::_Destroy_range<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, CUnmannedTraderSubClassInfo **_Last, std::allocator<CUnmannedTraderSubClassInfo *> *_Al, std::_Scalar_ptr_iterator_tag __formal)
{
  std::_Destroy_range<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    _First,
    _Last,
    _Al,
    __formal);
}
