/*
 * Function: ?CheckRegist@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_reg_item_request_clzo@@PEAVCLogFile@@AEAE222AEAK3@Z
 * Address: 0x14035A1A0
 */

char __fastcall CUnmannedTraderUserInfo::CheckRegist(CUnmannedTraderUserInfo *this, char byType, _a_trade_reg_item_request_clzo *pRequest, CLogFile *pkLogger, char *byTempSlotIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex, unsigned int *dwTax)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CUnmannedTraderGroupItemInfoTable *v13; // rax@46
  __int64 v14; // [sp+0h] [bp-58h]@1
  char *v15; // [sp+20h] [bp-38h]@46
  char *v16; // [sp+28h] [bp-30h]@46
  unsigned int *v17; // [sp+30h] [bp-28h]@46
  CPlayer *p; // [sp+40h] [bp-18h]@7
  _STORAGE_LIST::_db_con *v19; // [sp+48h] [bp-10h]@19
  CUnmannedTraderUserInfo *v20; // [sp+60h] [bp+8h]@1
  _a_trade_reg_item_request_clzo *v21; // [sp+70h] [bp+18h]@1

  v21 = pRequest;
  v20 = this;
  v10 = &v14;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v10 = -*********;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  if ( (signed int)v20->m_wInx < 2532 && (signed int)(unsigned __int8)byType < 2 )
  {
    p = &g_Player + v20->m_wInx;
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      result = -52;
    }
    else if ( p->m_dwObjSerial == v20->m_dwUserSerial )
    {
      if ( CUnmannedTraderRequestLimiter::IsEmpty(&v20->m_kRequestState) )
      {
        if ( v21->bUseNpcLink || IsBeNearStore(p, -1) )
        {
          if ( CMainThread::IsReleaseServiceMode(&g_Main) && p->m_byUserDgr )
          {
            result = 21;
          }
          else
          {
            v19 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&p->m_Param.m_dbInven.m_nListNum, v21->wItemSerial);
            if ( v19 )
            {
              if ( v19->m_bLock )
              {
                result = 15;
              }
              else if ( v20->m_byRegistCnt < v20->m_byMaxRegistCnt )
              {
                if ( v19->m_byCsMethod )
                {
                  result = 14;
                }
                else
                {
                  *dwTax = v21->dwPrice / 0x3E8;
                  if ( *dwTax <= CPlayerDB::GetDalant(&p->m_Param) )
                  {
                    if ( v21->dwPrice <= 0x77359400 )
                    {
                      if ( v19->m_byTableCode == v21->byItemTableCode && v19->m_wItemIndex == v21->wItemIndex )
                      {
                        if ( IsExchangeItem(v19->m_byTableCode, v19->m_wItemIndex) )
                        {
                          if ( IsOverLapItem(v19->m_byTableCode) && v21->byAmount > v19->m_dwDur )
                          {
                            result = 17;
                          }
                          else if ( IsOverLapItem(v19->m_byTableCode)
                                 && v19->m_dwDur > v21->byAmount
                                 && (*byTempSlotIndex = _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&p->m_Param.m_dbInven.m_nListNum),
                                     (unsigned __int8)*byTempSlotIndex == 255) )
                          {
                            result = 13;
                          }
                          else if ( v19->m_byTableCode != 15
                                 || CRecordData::GetRecord(
                                      &stru_1799C8410 + 1,
                                      *((_DWORD *)CPlayer::s_pnLinkForceItemToEffect + v19->m_wItemIndex)) )
                          {
                            v13 = CUnmannedTraderGroupItemInfoTable::Instance();
                            v17 = dwListIndex;
                            v16 = bySubClass;
                            v15 = byClass;
                            if ( CUnmannedTraderGroupItemInfoTable::GetGroupID(
                                   v13,
                                   v19->m_byTableCode,
                                   v19->m_wItemIndex,
                                   byDivision,
                                   byClass,
                                   bySubClass,
                                   dwListIndex) )
                            {
                              result = 0;
                            }
                            else
                            {
                              result = 3;
                            }
                          }
                          else
                          {
                            result = 23;
                          }
                        }
                        else
                        {
                          result = 3;
                        }
                      }
                      else
                      {
                        result = 9;
                      }
                    }
                    else
                    {
                      result = 22;
                    }
                  }
                  else
                  {
                    result = -55;
                  }
                }
              }
              else
              {
                result = 6;
              }
            }
            else
            {
              result = 8;
            }
          }
        }
        else
        {
          result = 2;
        }
      }
      else
      {
        result = 95;
      }
    }
    else
    {
      result = 99;
    }
  }
  else
  {
    result = 99;
  }
  return result;
}
