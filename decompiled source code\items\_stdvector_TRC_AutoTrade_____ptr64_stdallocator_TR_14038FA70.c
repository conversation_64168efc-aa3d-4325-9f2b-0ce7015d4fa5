/*
 * Function: _std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::insert_::_1_::dtor$4
 * Address: 0x14038FA70
 */

void __fastcall std::vector_TRC_AutoTrade_____ptr64_std::allocator_TRC_AutoTrade_____ptr64___::insert_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 120) & 2 )
  {
    *(_DWORD *)(a2 + 120) &= 0xFFFFFFFD;
    std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::~_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>(*(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > **)(a2 + 216));
  }
}
