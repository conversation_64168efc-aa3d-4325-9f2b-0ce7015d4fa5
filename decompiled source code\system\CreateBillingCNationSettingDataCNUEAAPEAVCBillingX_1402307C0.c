/*
 * Function: ?CreateBilling@CNationSettingDataCN@@UEAAPEAVCBilling@@XZ
 * Address: 0x1402307C0
 */

CBilling *__fastcall CNationSettingDataCN::CreateBilling(CNationSettingDataCN *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // rax@5
  __int64 v5; // [sp+0h] [bp-48h]@1
  CBillingCN *v6; // [sp+28h] [bp-20h]@4
  __int64 v7; // [sp+30h] [bp-18h]@4
  __int64 v8; // [sp+38h] [bp-10h]@5

  v1 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = -2i64;
  v6 = (CBillingCN *)operator new(0x10ui64);
  if ( v6 )
  {
    CBillingCN::CBillingCN(v6);
    v8 = v3;
  }
  else
  {
    v8 = 0i64;
  }
  return (CBilling *)v8;
}
