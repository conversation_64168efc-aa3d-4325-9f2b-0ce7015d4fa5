/*
 * Function: j_??E?$_Vector_iterator@VCUnmannedTraderSchedule@@V?$allocator@VCUnmannedTraderSchedule@@@std@@@std@@QEAA?AV01@H@Z
 * Address: 0x140008553
 */

std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *__fastcall std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator++(std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *this, std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *result, int __formal)
{
  return std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::operator++(
           this,
           result,
           __formal);
}
