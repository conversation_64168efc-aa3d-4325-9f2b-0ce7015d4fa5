/*
 * Function: ?AgreeWithEphemeralPrivate<PERSON>ey@?$DL_KeyAgreementAlgorithm_DH@VInteger@CryptoPP@@U?$EnumToType@W4CofactorMultiplicationOption@CryptoPP@@$0A@@2@@CryptoPP@@UEBA?AVInteger@2@AEBV?$DL_GroupParameters@VInteger@CryptoPP@@@2@AEBV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@AEBV32@@Z
 * Address: 0x14058EE30
 */

__int64 __fastcall CryptoPP::DL_KeyAgreementAlgorithm_DH<CryptoPP::Integer,CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>>::AgreeWithEphemeralPrivateKey(CryptoPP::Integer *a, __int64 a2, __int64 a3, __int64 *a4, CryptoPP::Integer *aa)
{
  CryptoPP::Integer *v5; // rax@2
  CryptoPP::Integer *v6; // ST20_8@4
  __int64 v7; // rax@4
  CryptoPP::Integer v9; // [sp+28h] [bp-A0h]@2
  CryptoPP::Integer result; // [sp+50h] [bp-78h]@2
  int v11; // [sp+78h] [bp-50h]@1
  __int64 v12; // [sp+80h] [bp-48h]@1
  CryptoPP::Integer *v13; // [sp+88h] [bp-40h]@2
  CryptoPP::Integer *b; // [sp+90h] [bp-38h]@2
  CryptoPP::Integer *v15; // [sp+98h] [bp-30h]@2
  CryptoPP::Integer *v16; // [sp+A0h] [bp-28h]@2
  CryptoPP::Integer *v17; // [sp+A8h] [bp-20h]@2
  __int64 v18; // [sp+B0h] [bp-18h]@4
  __int64 v19; // [sp+D8h] [bp+10h]@1
  __int64 v20; // [sp+E0h] [bp+18h]@1
  __int64 *v21; // [sp+E8h] [bp+20h]@1

  v21 = a4;
  v20 = a3;
  v19 = a2;
  v12 = -2i64;
  v11 = 0;
  if ( CryptoPP::EnumToType<enum  CryptoPP::CofactorMultiplicationOption,0>::ToEnum() == 2 )
  {
    LODWORD(v5) = (*(int (__fastcall **)(__int64, CryptoPP::Integer *))(*(_QWORD *)v20 + 88i64))(v20, &v9);
    v13 = v5;
    b = v5;
    v11 |= 1u;
    v15 = CryptoPP::operator*(&result, aa, v5);
    v16 = v15;
    v11 |= 2u;
    v17 = v15;
  }
  else
  {
    v17 = aa;
  }
  v6 = v17;
  LODWORD(v7) = (*(int (__fastcall **)(__int64))(*(_QWORD *)v20 + 40i64))(v20);
  v18 = *v21;
  (*(void (__fastcall **)(__int64 *, __int64, __int64, CryptoPP::Integer *))(v18 + 48))(v21, v19, v7, v6);
  v11 |= 4u;
  if ( v11 & 2 )
  {
    v11 &= 0xFFFFFFFD;
    CryptoPP::Integer::~Integer(&result);
  }
  if ( v11 & 1 )
  {
    v11 &= 0xFFFFFFFE;
    CryptoPP::Integer::~Integer(&v9);
  }
  return v19;
}
