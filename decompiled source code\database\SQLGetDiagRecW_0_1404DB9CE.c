/*
 * Function: SQLGetDiagRecW_0
 * Address: 0x1404DB9CE
 */

SQLRETURN __stdcall SQLGetDiagRecW_0(SQLSMALLINT fHandleType, S<PERSON>HA<PERSON><PERSON> handle, SQLSMALLINT iRecord, SQLWCHAR *szSqlState, SQLINTEGER *pfNativeError, SQLWCHAR *szErrorMsg, SQLSMALLINT cchErrorMsgMax, SQLSMALLINT *pcchErrorMsg)
{
  return SQLGetDiagRecW(
           fHandleType,
           handle,
           iRecord,
           szSqlState,
           pfNativeError,
           szErrorMsg,
           cchErrorMsgMax,
           pcchErrorMsg);
}
