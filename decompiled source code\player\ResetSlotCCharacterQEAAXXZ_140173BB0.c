/*
 * Function: ?ResetSlot@CCharacter@@QEAAXXZ
 * Address: 0x140173BB0
 */

void __fastcall CCharacter::ResetSlot(CCharacter *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CCharacter *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v4->m_AroundNum )
  {
    memset_0(v4->m_AroundSlot, 0, 0x28ui64);
    v4->m_AroundNum = 0;
  }
}
