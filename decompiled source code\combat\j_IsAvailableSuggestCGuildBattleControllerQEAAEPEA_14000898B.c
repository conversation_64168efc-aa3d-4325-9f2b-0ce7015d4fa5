/*
 * Function: j_?IsAvailableSuggest@CGuildBattleController@@QEAAEPEAVCGuild@@KKKK@Z
 * Address: 0x14000898B
 */

char __fastcall CGuildBattleController::IsAvailableSuggest(CGuildBattleController *this, CGuild *pSrcGuild, unsigned int dwDestGuild, unsigned int dwStartTime, unsigned int dwNumber, unsigned int dwMapCode)
{
  return CGuildBattleController::IsAvailableSuggest(this, pSrcGuild, dwDestGuild, dwStartTime, dwNumber, dwMapCode);
}
