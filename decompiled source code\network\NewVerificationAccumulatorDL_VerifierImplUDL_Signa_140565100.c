/*
 * Function: ?NewVerificationAccumulator@?$DL_VerifierImpl@U?$DL_SignatureSchemeOptions@V?$DL_SS@U?$DL_Keys_ECDSA@VECP@CryptoPP@@@CryptoPP@@V?$DL_Algorithm_ECDSA@VECP@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@H@CryptoPP@@U?$DL_Keys_ECDSA@VECP@CryptoPP@@@2@V?$DL_Algorithm_ECDSA@VECP@CryptoPP@@@2@VDL_SignatureMessageEncodingMethod_DSA@2@VSHA1@2@@CryptoPP@@@CryptoPP@@UEBAPEAVPK_MessageAccumulator@2@XZ
 * Address: 0x140565100
 */

CryptoPP::PK_MessageAccumulatorBase *CryptoPP::DL_VerifierImpl<CryptoPP::DL_SignatureSchemeOptions<CryptoPP::DL_SS<CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1,int>,CryptoPP::DL_Keys_ECDSA<CryptoPP::ECP>,CryptoPP::DL_Algorithm_ECDSA<CryptoPP::ECP>,CryptoPP::DL_SignatureMessageEncodingMethod_DSA,CryptoPP::SHA1>>::NewVerificationAccumulator()
{
  CryptoPP::PK_MessageAccumulatorBase *v1; // [sp+28h] [bp-20h]@1
  CryptoPP::PK_MessageAccumulatorBase *v2; // [sp+38h] [bp-10h]@2

  v1 = (CryptoPP::PK_MessageAccumulatorBase *)operator new(0x180ui64);
  if ( v1 )
    v2 = CryptoPP::PK_MessageAccumulatorImpl<CryptoPP::SHA1>::PK_MessageAccumulatorImpl<CryptoPP::SHA1>(v1);
  else
    v2 = 0i64;
  return v2;
}
