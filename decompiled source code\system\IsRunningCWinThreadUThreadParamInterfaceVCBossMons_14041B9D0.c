/*
 * Function: ?IsRunning@?$CWinThread@U?$ThreadParamInterface@VCBossMonsterScheduleSystem@@VAbstractThreadPool@US@@@US@@@US@@QEAA?C_NXZ
 * Address: 0x14041B9D0
 */

bool __fastcall US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool>>::IsRunning(US::CWinThread<US::ThreadParamInterface<CBossMonsterScheduleSystem,US::AbstractThreadPool> > *this)
{
  return this->m_bRunning;
}
