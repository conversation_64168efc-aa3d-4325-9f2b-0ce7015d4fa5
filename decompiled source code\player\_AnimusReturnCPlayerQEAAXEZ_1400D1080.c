/*
 * Function: ?_AnimusReturn@CPlayer@@QEAAXE@Z
 * Address: 0x1400D1080
 */

void __fastcall CPlayer::_AnimusReturn(CPlayer *this, char byReturnType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  unsigned __int16 v5; // [sp+20h] [bp-18h]@5
  CPlayer *v6; // [sp+40h] [bp+8h]@1
  char v7; // [sp+48h] [bp+10h]@1

  v7 = byReturnType;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v6->m_pRecalledAnimusChar )
  {
    v5 = v6->m_pRecalledAnimusItem->m_wSerial;
    if ( byReturnType == 3 )
      v6->m_wTimeFreeRecallSerial = v6->m_pRecalledAnimusItem->m_wSerial;
    else
      v6->m_wTimeFreeRecallSerial = -1;
    v6->m_dwLastRecallTime = GetLoopTime();
    _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&v6->m_pRecalledAnimusItem->m_bLoad, 0);
    _DELAY_PROCESS::Delete(&CPlayer::s_AnimusReturnDelay, v6->m_ObjID.m_wIndex, v6->m_dwObjSerial);
    CAnimus::Destroy(v6->m_pRecalledAnimusChar);
    v6->m_pRecalledAnimusItem = 0i64;
    v6->m_pRecalledAnimusChar = 0i64;
    CPlayer::SendMsg_AnimusReturnResult(v6, 0, v5, v7);
  }
}
