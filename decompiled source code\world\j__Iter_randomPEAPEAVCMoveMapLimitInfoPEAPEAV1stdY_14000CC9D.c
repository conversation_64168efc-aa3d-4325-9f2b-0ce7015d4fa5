/*
 * Function: j_??$_Iter_random@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@@std@@YA?AUrandom_access_iterator_tag@0@AEBQEAPEAVCMoveMapLimitInfo@@0@Z
 * Address: 0x14000CC9D
 */

std::random_access_iterator_tag __fastcall std::_Iter_random<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(CMoveMapLimitInfo **const *__formal, CMoveMapLimitInfo **const *a2)
{
  return std::_Iter_random<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *>(__formal, a2);
}
