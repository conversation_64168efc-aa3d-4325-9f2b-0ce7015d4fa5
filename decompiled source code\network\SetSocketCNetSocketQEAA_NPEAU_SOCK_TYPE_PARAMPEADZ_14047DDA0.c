/*
 * Function: ?SetSocket@CNetSocket@@QEAA_NPEAU_SOCK_TYPE_PARAM@@PEAD@Z
 * Address: 0x14047DDA0
 */

char __fastcall CNetSocket::SetSocket(CNetSocket *this, _SOCK_TYPE_PARAM *pType, char *pszErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v5; // rax@4
  unsigned __int8 v6; // cf@6
  unsigned __int64 v7; // rax@6
  char result; // al@21
  __int64 v9; // [sp+0h] [bp-98h]@1
  unsigned int j; // [sp+30h] [bp-68h]@11
  unsigned int dwIndex; // [sp+34h] [bp-64h]@15
  int count[2]; // [sp+38h] [bp-60h]@4
  _socket *v13; // [sp+40h] [bp-58h]@11
  void *v14; // [sp+48h] [bp-50h]@8
  void **v15; // [sp+50h] [bp-48h]@11
  unsigned int *v16; // [sp+58h] [bp-40h]@15
  _IP_CHECK_NODE *v17; // [sp+60h] [bp-38h]@15
  __int64 v18; // [sp+68h] [bp-30h]@4
  _socket *v19; // [sp+70h] [bp-28h]@9
  unsigned __int64 v20; // [sp+78h] [bp-20h]@11
  unsigned __int64 v21; // [sp+80h] [bp-18h]@15
  unsigned __int64 v22; // [sp+88h] [bp-10h]@15
  CNetSocket *v23; // [sp+A0h] [bp+8h]@1
  _SOCK_TYPE_PARAM *Src; // [sp+A8h] [bp+10h]@1
  char *pszErrMsga; // [sp+B0h] [bp+18h]@1

  pszErrMsga = pszErrMsg;
  Src = pType;
  v23 = this;
  v3 = &v9;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = -2i64;
  memcpy_0(&v23->m_SockType, pType, 0x18ui64);
  *(_QWORD *)count = Src->m_wSocketMaxNum;
  v5 = 160i64 * *(_QWORD *)count;
  if ( !is_mul_ok(0xA0ui64, *(unsigned __int64 *)count) )
    v5 = -1i64;
  v6 = __CFADD__(v5, 8i64);
  v7 = v5 + 8;
  if ( v6 )
    v7 = -1i64;
  v14 = operator new(v7);
  if ( v14 )
  {
    *(_DWORD *)v14 = count[0];
    `eh vector constructor iterator'(
      (char *)v14 + 8,
      0xA0ui64,
      count[0],
      (void (__cdecl *)(void *))_socket::_socket,
      (void (__cdecl *)(void *))_socket::~_socket);
    v19 = (_socket *)((char *)v14 + 8);
  }
  else
  {
    v19 = 0i64;
  }
  v13 = v19;
  v23->m_Socket = v19;
  v20 = Src->m_wSocketMaxNum;
  v15 = (void **)operator new(saturated_mul(8ui64, v20));
  v23->m_Event = v15;
  for ( j = 0; j < v23->m_SockType.m_wSocketMaxNum; ++j )
    v23->m_Event[j] = WSACreateEvent();
  if ( Src->m_bAcceptIPCheck )
  {
    v23->m_nIPCheckNodeNum = 10 * Src->m_wSocketMaxNum;
    v21 = v23->m_nIPCheckNodeNum;
    v16 = (unsigned int *)operator new(saturated_mul(4ui64, v21));
    v23->m_dwIPCheckBufferList = v16;
    v22 = v23->m_nIPCheckNodeNum;
    v17 = (_IP_CHECK_NODE *)operator new(saturated_mul(8ui64, v22));
    v23->m_ndIPCheck = v17;
    CNetIndexList::SetList(&v23->m_listIPCheck, v23->m_nIPCheckNodeNum);
    CNetIndexList::SetList(&v23->m_listIPCheck_Empty, v23->m_nIPCheckNodeNum);
    for ( dwIndex = 0; (signed int)dwIndex < v23->m_nIPCheckNodeNum; ++dwIndex )
      CNetIndexList::PushNode_Back(&v23->m_listIPCheck_Empty, dwIndex);
    CNetTimer::BeginTimer(&v23->m_tmrListCheckerIPCheck, Src->m_dwIPCheckTerm / 2);
  }
  if ( !v23->m_SockType.m_bServer || CNetSocket::InitAcceptSocket(v23, pszErrMsga) )
  {
    v23->m_bSetSocket = 1;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
