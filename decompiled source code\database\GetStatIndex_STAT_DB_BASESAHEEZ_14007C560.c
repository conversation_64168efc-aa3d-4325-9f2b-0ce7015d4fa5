/*
 * Function: ?GetStatIndex@_STAT_DB_BASE@@SAHEE@Z
 * Address: 0x14007C560
 */

__int64 __fastcall _STAT_DB_BASE::GetStatIndex(char byMasteryClass, char byIndex)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // [sp+0h] [bp-18h]@1
  int v6; // [sp+4h] [bp-14h]@4
  char v7; // [sp+20h] [bp+8h]@1

  v7 = byMasteryClass;
  v2 = &v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = -1;
  v6 = (unsigned __int8)v7;
  switch ( v7 )
  {
    case 0:
      v5 = (unsigned __int8)byIndex;
      break;
    case 1:
      v5 = 2;
      break;
    case 2:
      v5 = 3;
      break;
    case 3:
      v5 = (unsigned __int8)byIndex + 4;
      break;
    case 4:
      v5 = (unsigned __int8)byIndex + 52;
      break;
    case 5:
      v5 = (unsigned __int8)byIndex + 76;
      break;
    case 6:
      v5 = 79;
      break;
    default:
      return (unsigned int)v5;
  }
  return (unsigned int)v5;
}
