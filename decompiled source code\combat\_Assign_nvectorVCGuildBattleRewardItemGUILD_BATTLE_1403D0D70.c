/*
 * Function: ?_Assign_n@?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@IEAAX_KAEBVCGuildBattleRewardItem@GUILD_BATTLE@@@Z
 * Address: 0x1403D0D70
 */

void __fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Assign_n(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, unsigned __int64 _Count, GUILD_BATTLE::CGuildBattleRewardItem *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-118h]@1
  char v6; // [sp+28h] [bp-F0h]@4
  char v7; // [sp+48h] [bp-D0h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *result; // [sp+60h] [bp-B8h]@4
  char v9; // [sp+68h] [bp-B0h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v10; // [sp+80h] [bp-98h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > v11; // [sp+88h] [bp-90h]@4
  char v12; // [sp+A0h] [bp-78h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v13; // [sp+B8h] [bp-60h]@4
  char v14; // [sp+C0h] [bp-58h]@4
  __int64 v15; // [sp+D0h] [bp-48h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v16; // [sp+D8h] [bp-40h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v17; // [sp+E0h] [bp-38h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v18; // [sp+E8h] [bp-30h]@4
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v19; // [sp+F0h] [bp-28h]@4
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *v20; // [sp+120h] [bp+8h]@1
  unsigned __int64 v21; // [sp+128h] [bp+10h]@1

  v21 = _Count;
  v20 = this;
  v3 = &v5;
  for ( i = 66i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v15 = -2i64;
  qmemcpy(&v14, _Val, 0x10ui64);
  qmemcpy(&v6, &v14, 0x10ui64);
  result = (std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *)&v7;
  v10 = (std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *)&v9;
  v16 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::end(
          v20,
          (std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *)&v7);
  v17 = v16;
  v18 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::begin(
          v20,
          v10);
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::erase(
    v20,
    &v11,
    v18,
    v17);
  std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::~_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>(&v11);
  v13 = (std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *)&v12;
  v19 = std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::begin(
          v20,
          (std::_Vector_iterator<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *)&v12);
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::insert(
    v20,
    v19,
    v21,
    (GUILD_BATTLE::CGuildBattleRewardItem *)&v6);
}
