/*
 * Function: j_??9?$_Vector_const_iterator@PEAVCMoveMapLimitInfo@@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@QEBA_NAEBV01@@Z
 * Address: 0x14000A867
 */

bool __fastcall std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator!=(std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *this, std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > *_Right)
{
  return std::_Vector_const_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::operator!=(this, _Right);
}
