/*
 * Function: ?Indexing@CItemLootTable@@AEAA_NPEAVCRecordData@@PEAD@Z
 * Address: 0x140203F20
 */

char __fastcall CItemLootTable::Indexing(CItemLootTable *this, CRecordData *pItemRec, char *pszErrMsg)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  size_t v5; // rax@13
  __int64 v7; // [sp+0h] [bp-88h]@1
  DWORD v8; // [sp+20h] [bp-68h]@4
  int n; // [sp+24h] [bp-64h]@4
  _base_fld *v10; // [sp+28h] [bp-60h]@7
  int v11; // [sp+30h] [bp-58h]@8
  int j; // [sp+34h] [bp-54h]@8
  char *Str1; // [sp+38h] [bp-50h]@10
  char *psItemCode; // [sp+40h] [bp-48h]@15
  int v15; // [sp+48h] [bp-40h]@15
  _base_fld *v16; // [sp+50h] [bp-38h]@19
  DWORD v17; // [sp+58h] [bp-30h]@25
  CItemLootTable::_linker_code *v18; // [sp+60h] [bp-28h]@13
  unsigned __int64 v19; // [sp+68h] [bp-20h]@13
  CItemLootTable::_linker_code **v20; // [sp+70h] [bp-18h]@13
  CItemLootTable *v21; // [sp+90h] [bp+8h]@1
  CRecordData *v22; // [sp+98h] [bp+10h]@1
  char *Dest; // [sp+A0h] [bp+18h]@1

  Dest = pszErrMsg;
  v22 = pItemRec;
  v21 = this;
  v3 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = timeGetTime();
  for ( n = 0; n < v21->m_nLootNum; ++n )
  {
    v10 = CRecordData::GetRecord(&v21->m_tblLoot, n);
    v21->m_ppLinkCode[n] = 0i64;
    if ( *(_DWORD *)&v10[1].m_strCode[8] > 0 )
    {
      v11 = 0;
      for ( j = 0; j < *(_DWORD *)&v10[1].m_strCode[8]; ++j )
      {
        Str1 = &v10[1].m_strCode[8 * j + 12];
        if ( !strcmp_0(Str1, "0") )
          break;
        ++v11;
      }
      *(_DWORD *)&v10[1].m_strCode[8] = v11;
      if ( *(_DWORD *)&v10[1].m_strCode[8] )
      {
        v19 = *(_DWORD *)&v10[1].m_strCode[8];
        v18 = (CItemLootTable::_linker_code *)operator new[](saturated_mul(8ui64, v19));
        v21->m_ppLinkCode[n] = v18;
        v5 = 8i64 * *(_DWORD *)&v10[1].m_strCode[8];
        v20 = v21->m_ppLinkCode;
        memset_0(v20[n], 0, v5);
        for ( j = 0; j < *(_DWORD *)&v10[1].m_strCode[8]; ++j )
        {
          psItemCode = &v10[1].m_strCode[8 * j + 12];
          v15 = GetItemTableCode(psItemCode);
          if ( v15 == -1 )
          {
            if ( Dest )
              sprintf(Dest, "CItemLootTable.. %d rec %s item no search table", (unsigned int)n, psItemCode);
            return 0;
          }
          v16 = CRecordData::GetRecordByHash(&v22[v15], psItemCode, 2, 5);
          if ( !v16 )
          {
            if ( Dest )
              sprintf(Dest, "CItemLootTable.. %d rec %s item no search index", (unsigned int)n, psItemCode);
            return 0;
          }
          v21->m_ppLinkCode[n][j].byTableCode = v15;
          v21->m_ppLinkCode[n][j].wItemIndex = v16->m_dwIndex;
          v21->m_ppLinkCode[n][j].bExist = LOBYTE(v16[1].m_dwIndex);
        }
      }
    }
  }
  v17 = timeGetTime();
  __trace("loot load time :  %d", v17 - v8);
  return 1;
}
