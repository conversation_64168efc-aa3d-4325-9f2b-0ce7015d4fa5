/*
 * Function: ??0?$_Vector_iterator@VCUnmannedTraderUserInfo@@V?$allocator@VCUnmannedTraderUserInfo@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140368230
 */

void __fastcall std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *this, std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>(
    (std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&v5->_Mycont,
    (std::_Vector_const_iterator<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo> > *)&__that->_Mycont);
}
