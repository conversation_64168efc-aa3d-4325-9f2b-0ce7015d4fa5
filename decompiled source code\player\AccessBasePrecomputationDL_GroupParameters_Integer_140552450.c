/*
 * Function: ?AccessBasePrecomputation@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAAAEAV?$DL_FixedBasePrecomputation@VInteger@CryptoPP@@@2@XZ
 * Address: 0x140552450
 */

signed __int64 __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AccessBasePrecomputation(__int64 a1)
{
  return a1 + 80;
}
