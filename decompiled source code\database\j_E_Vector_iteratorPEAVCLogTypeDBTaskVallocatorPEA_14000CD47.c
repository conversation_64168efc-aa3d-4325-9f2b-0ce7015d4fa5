/*
 * Function: j_??E?$_Vector_iterator@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEAAAEAV01@XZ
 * Address: 0x14000CD47
 */

std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *__fastcall std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator++(std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  return std::_Vector_iterator<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::operator++(this);
}
