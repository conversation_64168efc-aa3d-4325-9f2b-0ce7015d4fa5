/*
 * Function: j_?FindEmpty@CUnmannedTraderUserInfo@@AEAA?AV?$_Vector_iterator@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@XZ
 * Address: 0x14000A376
 */

std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__fastcall CUnmannedTraderUserInfo::FindEmpty(CUnmannedTraderUserInfo *this, std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *result)
{
  return CUnmannedTraderUserInfo::FindEmpty(this, result);
}
