/*
 * Function: ?invoke@?$user2type@P6APEAVCMonster@@PEAD0MMM@Z@lua_tinker@@SAP6APEAVCMonster@@PEAD0MMM@ZPEAUlua_State@@H@Z
 * Address: 0x14040A830
 */

CMonster *(__cdecl *__fastcall lua_tinker::user2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(lua_tinker::user2type<CMonster * (__cdecl*)(char *,char *,float,float,float)> *this, struct lua_State *L, int index))(char *, char *, float, float, float)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  lua_tinker::void2type<CMonster * (__cdecl*)(char *,char *,float,float,float)> *v5; // rax@4
  void *v6; // rdx@4
  __int64 v8; // [sp+0h] [bp-28h]@1
  lua_tinker::user2type<CMonster * (__cdecl*)(char *,char *,float,float,float)> *v9; // [sp+30h] [bp+8h]@1

  v9 = this;
  v3 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  LODWORD(v5) = lua_touserdata(v9, (unsigned int)L);
  return lua_tinker::void2type<CMonster * (*)(char *,char *,float,float,float)>::invoke(v5, v6);
}
