/*
 * Function: ?Update_CharacterReName@CRFWorldDatabase@@QEAA_NPEADK@Z
 * Address: 0x1404C46C0
 */

bool __fastcall CRFWorldDatabase::Update_CharacterReName(CRFWorldDatabase *this, char *pwszName, unsigned int dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@6
  unsigned __int64 v8; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+160h] [bp+8h]@1
  char *pwszNamea; // [sp+168h] [bp+10h]@1
  unsigned int pSerial; // [sp+170h] [bp+18h]@1

  pSerial = dwSerial;
  pwszNamea = pwszName;
  v9 = this;
  v3 = &v6;
  for ( i = 84i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  if ( CRFWorldDatabase::Select_CharacterReName(v9, pwszName, &pSerial) )
  {
    sprintf(&Dest, "{ CALL pUpdate_CharacterReName( '%s', %d ) }", pwszNamea, pSerial);
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
  }
  else
  {
    result = 0;
  }
  return result;
}
