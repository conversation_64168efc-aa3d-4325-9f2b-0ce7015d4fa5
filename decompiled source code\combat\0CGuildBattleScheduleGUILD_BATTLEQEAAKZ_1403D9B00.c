/*
 * Function: ??0CGuildBattleSchedule@GUILD_BATTLE@@QEAA@K@Z
 * Address: 0x1403D9B00
 */

void __fastcall GUILD_BATTLE::CGuildBattleSchedule::CGuildBattleSchedule(GUILD_BATTLE::CGuildBattleSchedule *this, unsigned int dwScheduleID)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  GUILD_BATTLE::CGuildBattleSchedule *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5->m_dwScheduleID = dwScheduleID;
  ATL::CTime::CTime(&v5->m_kNextStartTime);
  ATL::CTime::CTime(&v5->m_kBattleStartTime);
  ATL::CTimeSpan::CTimeSpan(&v5->m_kBattleTime);
  GUILD_BATTLE::CGuildBattleSchedule::Clear(v5);
}
