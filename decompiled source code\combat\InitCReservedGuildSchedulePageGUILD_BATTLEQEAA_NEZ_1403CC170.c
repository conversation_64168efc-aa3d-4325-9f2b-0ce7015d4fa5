/*
 * Function: ?Init@CReservedGuildSchedulePage@GUILD_BATTLE@@QEAA_NE@Z
 * Address: 0x1403CC170
 */

bool __fastcall GUILD_BATTLE::CReservedGuildSchedulePage::Init(GUILD_BATTLE::CReservedGuildSchedulePage *this, char ucPageInx)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v4; // rax@5
  bool result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _guild_battle_reserved_schedule_result_zocl *v7; // [sp+20h] [bp-18h]@4
  unsigned int v8; // [sp+28h] [bp-10h]@5
  GUILD_BATTLE::CReservedGuildSchedulePage *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1

  v10 = ucPageInx;
  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = (_guild_battle_reserved_schedule_result_zocl *)operator new(0xD3ui64);
  v9->m_pkList = v7;
  if ( v9->m_pkList )
  {
    v9->m_ucPageInx = v10;
    result = GUILD_BATTLE::CReservedGuildSchedulePage::Clear(v9) != 0;
  }
  else
  {
    v8 = (unsigned __int8)v10;
    v4 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v4,
      "CReservedGuildSchedulePage::Init(%u) : m_pkList = new _guild_battle_reserved_schedule_result_zocl NULL!",
      v8);
    result = 0;
  }
  return result;
}
