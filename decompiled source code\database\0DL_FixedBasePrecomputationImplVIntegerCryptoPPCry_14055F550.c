/*
 * Function: ??0?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@CryptoPP@@QEAA@AEBV01@@Z
 * Address: 0x14055F550
 */

__int64 __fastcall CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>(__int64 a1, __int64 a2)
{
  __int64 v3; // [sp+40h] [bp+8h]@1
  __int64 v4; // [sp+48h] [bp+10h]@1

  v4 = a2;
  v3 = a1;
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::Integer>::DL_FixedBasePrecomputation<CryptoPP::Integer>();
  *(_QWORD *)v3 = &CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>::`vftable';
  CryptoPP::Integer::Integer((CryptoPP::Integer *)(v3 + 8), (const struct CryptoPP::Integer *)(v4 + 8));
  *(_DWORD *)(v3 + 48) = *(_DWORD *)(v4 + 48);
  CryptoPP::Integer::Integer((CryptoPP::Integer *)(v3 + 56), (const struct CryptoPP::Integer *)(v4 + 56));
  std::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>::vector<CryptoPP::Integer,std::allocator<CryptoPP::Integer>>(
    v3 + 96,
    v4 + 96);
  return v3;
}
