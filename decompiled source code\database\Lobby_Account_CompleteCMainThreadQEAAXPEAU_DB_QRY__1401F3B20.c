/*
 * Function: ?Lobby_Account_Complete@CMainThread@@QEAAXPEAU_DB_QRY_SYN_DATA@@@Z
 * Address: 0x1401F3B20
 */

void __fastcall CMainThread::Lobby_Account_Complete(CMainThread *this, _DB_QRY_SYN_DATA *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // edx@8
  __int64 v5; // [sp+0h] [bp-108h]@1
  char *pszLog; // [sp+20h] [bp-E8h]@8
  char *v7; // [sp+28h] [bp-E0h]@8
  unsigned int v8; // [sp+30h] [bp-D8h]@8
  char v9; // [sp+40h] [bp-C8h]@4
  CUserDB *v10; // [sp+48h] [bp-C0h]@4
  char Dest; // [sp+60h] [bp-A8h]@8
  unsigned __int64 v12; // [sp+F0h] [bp-18h]@4
  _DB_QRY_SYN_DATA *v13; // [sp+118h] [bp+10h]@1

  v13 = pData;
  v2 = &v5;
  for ( i = 64i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  v9 = 0;
  v10 = &g_UserDB[pData->m_idWorld.wIndex];
  if ( v10->m_bActive && v10->m_idWorld.dwSerial == pData->m_idWorld.dwSerial )
  {
    if ( pData->m_byResult )
    {
      v4 = pData->m_byResult;
      v8 = v10->m_dwSerial;
      v7 = v10->m_aszAvatorName;
      LODWORD(pszLog) = v10->m_dwAccountSerial;
      sprintf(&Dest, "Lobby_Account_Complete Database Error(%d) account(%s , %d), char(%s, %d)", v4, v10->m_szAccountID);
      CNetworkEX::Close(&g_Network, 0, v13->m_idWorld.wIndex, 0, &Dest);
    }
    else
    {
      CUserDB::Lobby_Char_Complete(v10, pData->m_byResult);
    }
  }
}
