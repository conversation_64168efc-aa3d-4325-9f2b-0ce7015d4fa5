/*
 * Function: j_?mastery_change_jade@CMgrAvatorItemHistory@@QEAAXHKKHMPEADH@Z
 * Address: 0x1400049F8
 */

void __fastcall CMgrAvatorItemHistory::mastery_change_jade(CMgrAvatorItemHistory *this, int nMstCode, unsigned int dwOldCum, unsigned int dwNewCum, int nLv, float fVal, char *szFileName, int nWpType)
{
  CMgrAvatorItemHistory::mastery_change_jade(this, nMstCode, dwOldCum, dwNewCum, nLv, fVal, szFileName, nWpType);
}
