/*
 * Function: ?SendMsg_DTradeDelInform@CPlayer@@QEAAXE@Z
 * Address: 0x1400E1750
 */

void __fastcall CPlayer::SendMsg_DTradeDelInform(CPlayer *this, char bySlotIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v6; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = bySlotIndex;
  v6 = v9->m_pmTrd.byEmptyInvenNum;
  pbyType = 18;
  v8 = 18;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &szMsg, 2u);
}
