/*
 * Function: ?GetPtrFromSerial@_STORAGE_LIST@@QEAAPEAU_db_con@1@G@Z
 * Address: 0x14010EE40
 */

_STORAGE_LIST::_db_con *__fastcall _STORAGE_LIST::GetPtrFromSerial(_STORAGE_LIST *this, unsigned __int16 wSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@4
  _STORAGE_LIST *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = _STORAGE_LIST::GetIndexFromSerial(v7, wSerial);
  if ( v6 == 255 )
    result = 0i64;
  else
    result = &v7->m_pStorageList[v6];
  return result;
}
