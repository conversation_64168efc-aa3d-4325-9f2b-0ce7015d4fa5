/*
 * Function: j_??$_Uninit_fill_n@PEAPEAVCUnmannedTraderSubClassInfo@@_KPEAV1@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@YAXPEAPEAVCUnmannedTraderSubClassInfo@@_KAEBQEAV1@AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000D58A
 */

void __fastcall std::_Uninit_fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(CUnmannedTraderSubClassInfo **_First, unsigned __int64 _Count, CUnmannedTraderSubClassInfo *const *_Val, std::allocator<CUnmannedTraderSubClassInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<CUnmannedTraderSubClassInfo * *,unsigned __int64,CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
