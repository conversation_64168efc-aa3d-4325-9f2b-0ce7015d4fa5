/*
 * Function: ?GetCSetItemType@CSUItemSystem@@QEAAPEAVCSetItemType@@XZ
 * Address: 0x1402E46D0
 */

CSetItemType *__fastcall CSUItemSystem::GetCSetItemType(CSUItemSystem *this)
{
  CSetItemType *result; // rax@2

  if ( this->m_bySUItemCount )
  {
    if ( this->m_bLoadData[0] )
    {
      if ( this->m_bChangeData[0] )
        result = &this->m_SetItemType;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
