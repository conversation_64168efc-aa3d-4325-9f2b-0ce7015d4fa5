/*
 * Function: ??0qry_case_golden_box_item@@QEAA@XZ
 * Address: 0x140416BC0
 */

void __fastcall qry_case_golden_box_item::qry_case_golden_box_item(qry_case_golden_box_item *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  qry_case_golden_box_item *Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _db_golden_box_item::_db_golden_box_item(&Dst->NewData);
  _db_golden_box_item::_db_golden_box_item(&Dst->OldData);
  memset_0(Dst, 0, 0xCB4ui64);
}
