/*
 * Function: ?ct_<PERSON><PERSON>eeperAttack@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294DF0
 */

char __fastcall ct_HolyKeeperAttack(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CGameObjectVtbl *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-58h]@1
  char v6; // [sp+20h] [bp-38h]@8
  int v7; // [sp+28h] [bp-30h]@8
  int v8; // [sp+30h] [bp-28h]@8
  char v9; // [sp+38h] [bp-20h]@8
  int v10; // [sp+40h] [bp-18h]@8
  CPlayer *v11; // [sp+60h] [bp+8h]@1

  v11 = pOne;
  v1 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v11 && v11->m_bOper )
  {
    if ( s_nWordCount < 1 )
    {
      result = 0;
    }
    else
    {
      v10 = atoi(s_pwszDstCheat[0]);
      v4 = g_Keeper->vfptr;
      v9 = 1;
      v8 = 0;
      v7 = -1;
      v6 = 0;
      ((void (__fastcall *)(CHolyKeeper *, _QWORD, CPlayer *, signed __int64))v4->SetDamage)(
        g_Keeper,
        (unsigned int)v10,
        v11,
        1i64);
      result = 1;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
