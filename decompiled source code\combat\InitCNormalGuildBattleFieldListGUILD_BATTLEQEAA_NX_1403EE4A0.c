/*
 * Function: ?Init@CNormalGuildBattleFieldList@GUILD_BATTLE@@QEAA_NXZ
 * Address: 0x1403EE4A0
 */

char __usercall GUILD_BATTLE::CNormalGuildBattleFieldList::Init@<al>(GUILD_BATTLE::CNormalGuildBattleFieldList *this@<rcx>, signed __int64 a2@<rax>)
{
  void *v2; // rsp@1
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CGuildBattleLogger *v5; // rax@5
  char result; // al@5
  signed __int64 v7; // rax@6
  unsigned __int8 v8; // cf@8
  unsigned __int64 v9; // rax@8
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@14
  GUILD_BATTLE::CGuildBattleLogger *v11; // rax@18
  __int64 v12; // [sp-20h] [bp-6C18h]@1
  void (__cdecl *pDtor)(void *); // [sp+0h] [bp-6BF8h]@25
  char Dst; // [sp+20h] [bp-6BD8h]@4
  UINT v15; // [sp+424h] [bp-67D4h]@4
  unsigned int uiMapInx; // [sp+428h] [bp-67D0h]@15
  char v17[25632]; // [sp+440h] [bp-67B8h]@22
  char *szParseBuff[100]; // [sp+6860h] [bp-398h]@22
  int j; // [sp+6B84h] [bp-74h]@20
  char *szKeyName; // [sp+6B98h] [bp-60h]@23
  const char *v21; // [sp+6BA0h] [bp-58h]@23
  const char *v22; // [sp+6BA8h] [bp-50h]@23
  int k; // [sp+6BB4h] [bp-44h]@23
  int count[2]; // [sp+6BC0h] [bp-38h]@6
  GUILD_BATTLE::CNormalGuildBattleField *v25; // [sp+6BC8h] [bp-30h]@13
  void *v26; // [sp+6BD0h] [bp-28h]@10
  __int64 v27; // [sp+6BD8h] [bp-20h]@4
  GUILD_BATTLE::CNormalGuildBattleField *v28; // [sp+6BE0h] [bp-18h]@11
  unsigned __int64 v29; // [sp+6BE8h] [bp-10h]@4
  GUILD_BATTLE::CNormalGuildBattleFieldList *v30; // [sp+6C00h] [bp+8h]@1

  v30 = this;
  v2 = alloca(a2);
  v3 = &v12;
  for ( i = 6916i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v27 = -2i64;
  v29 = (unsigned __int64)&v12 ^ _security_cookie;
  memset_0(&Dst, 0, 0x400ui64);
  v15 = 0;
  v15 = GetPrivateProfileIntA("Field", "mapcnt", 100, "./Initialize/NormalGuildBattle.ini");
  if ( v15 )
  {
    v30->m_dwCnt = v15;
    *(_QWORD *)count = v30->m_dwCnt;
    v7 = 88i64 * *(_QWORD *)count;
    if ( !is_mul_ok(0x58ui64, *(unsigned __int64 *)count) )
      v7 = -1i64;
    v8 = __CFADD__(v7, 8i64);
    v9 = v7 + 8;
    if ( v8 )
      v9 = -1i64;
    v26 = operator new[](v9);
    if ( v26 )
    {
      *(_DWORD *)v26 = count[0];
      `eh vector constructor iterator'(
        (char *)v26 + 8,
        0x58ui64,
        count[0],
        (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleField::CNormalGuildBattleField,
        (void (__cdecl *)(void *))GUILD_BATTLE::CNormalGuildBattleField::~CNormalGuildBattleField);
      v28 = (GUILD_BATTLE::CNormalGuildBattleField *)((char *)v26 + 8);
    }
    else
    {
      v28 = 0i64;
    }
    v25 = v28;
    v30->m_pkField = v28;
    if ( v30->m_pkField )
    {
      for ( uiMapInx = 0; uiMapInx < v30->m_dwCnt; ++uiMapInx )
      {
        if ( !GUILD_BATTLE::CNormalGuildBattleField::Init(&v30->m_pkField[uiMapInx], uiMapInx) )
        {
          v11 = GUILD_BATTLE::CGuildBattleLogger::Instance();
          GUILD_BATTLE::CGuildBattleLogger::Log(
            v11,
            "CNormalGuildBattleFieldList::Init() m_pkField[%u].Init(%u) = false",
            uiMapInx,
            uiMapInx);
          return 0;
        }
      }
      for ( j = 0; j < 100; ++j )
        szParseBuff[j] = &v17[256 * (signed __int64)j];
      memset_0(v17, 0, 0x6400ui64);
      szKeyName = "Bellato";
      v21 = "Cora";
      v22 = "Accratia";
      for ( k = 0; k < 3; ++k )
      {
        pDtor = (void (__cdecl *)(void *))szParseBuff;
        if ( !GUILD_BATTLE::CNormalGuildBattleFieldList::InitUseField(v30, k, (&szKeyName)[8 * k], &Dst, szParseBuff) )
          return 0;
      }
      result = 1;
    }
    else
    {
      v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
      GUILD_BATTLE::CGuildBattleLogger::Log(
        v10,
        "CNormalGuildBattleFieldList::Init() NULL == new CNormalGuildBattleField[%u]",
        v30->m_dwCnt);
      result = 0;
    }
  }
  else
  {
    v5 = GUILD_BATTLE::CGuildBattleLogger::Instance();
    GUILD_BATTLE::CGuildBattleLogger::Log(
      v5,
      "CNormalGuildBattleFieldList::Init() GetPrivateProfileInt( Field, mapcnt, 0, %s ) = 0",
      "./Initialize/NormalGuildBattle.ini");
    result = 0;
  }
  return result;
}
