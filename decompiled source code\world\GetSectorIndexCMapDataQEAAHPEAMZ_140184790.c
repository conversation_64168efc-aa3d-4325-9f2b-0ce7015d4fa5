/*
 * Function: ?GetSectorIndex@CMapData@@QEAAHPEAM@Z
 * Address: 0x140184790
 */

signed __int64 __fastcall CMapData::GetSectorIndex(CMapData *this, float *pPos)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  __int64 v5; // [sp+0h] [bp-58h]@1
  float v6; // [sp+28h] [bp-30h]@4
  float v7; // [sp+30h] [bp-28h]@4
  int v8; // [sp+44h] [bp-14h]@4
  int v9; // [sp+48h] [bp-10h]@4
  CMapData *v10; // [sp+60h] [bp+8h]@1

  v10 = this;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = (float)-v10->m_BspInfo.m_nMapMinSize[0] + *pPos;
  v7 = (float)v10->m_BspInfo.m_nMapMaxSize[2] - pPos[2];
  v8 = (signed int)ffloor(v6 / 100.0);
  v9 = (signed int)ffloor(v7 / 100.0);
  if ( (unsigned int)v8 < v10->m_SecInfo.m_nSecNumW && (unsigned int)v9 < v10->m_SecInfo.m_nSecNumH )
    result = (unsigned int)(v10->m_SecInfo.m_nSecNumW * v9 + v8);
  else
    result = 0xFFFFFFFFi64;
  return result;
}
