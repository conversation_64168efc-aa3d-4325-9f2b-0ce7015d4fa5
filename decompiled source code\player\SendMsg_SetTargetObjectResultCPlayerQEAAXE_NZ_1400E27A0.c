/*
 * Function: ?SendMsg_SetTargetObjectResult@CPlayer@@QEAAXE_N@Z
 * Address: 0x1400E27A0
 */

void __fastcall CPlayer::SendMsg_SetTargetObjectResult(CPlayer *this, char byRetCode, bool bForce)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v7; // [sp+39h] [bp-4Fh]@6
  char v8; // [sp+3Ah] [bp-4Eh]@6
  unsigned int v9; // [sp+3Bh] [bp-4Dh]@6
  unsigned __int16 v10; // [sp+3Fh] [bp-49h]@6
  bool v11; // [sp+41h] [bp-47h]@8
  bool v12; // [sp+42h] [bp-46h]@9
  char pbyType; // [sp+64h] [bp-24h]@9
  char v14; // [sp+65h] [bp-23h]@9
  CPlayer *v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v3 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byRetCode;
  if ( !byRetCode )
  {
    if ( v15->m_TargetObject.pObject )
    {
      v7 = v15->m_TargetObject.byKind;
      v8 = v15->m_TargetObject.byID;
      v9 = v15->m_TargetObject.dwSerial;
      v10 = v15->m_TargetObject.wHPRate;
      if ( !v15->m_TargetObject.byKind && !v15->m_TargetObject.byID )
        v11 = v15->m_TargetObject.pObject[10].m_bCorpse;
    }
  }
  v12 = bForce;
  pbyType = 13;
  v14 = 27;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xBu);
}
