/*
 * Function: ?_db_qry_insert_newowner@AutoMineMachineMng@@AEAAEPEAD@Z
 * Address: 0x1402D6DC0
 */

char __fastcall AutoMineMachineMng::_db_qry_insert_newowner(AutoMineMachineMng *this, char *pdata)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  char *v6; // [sp+20h] [bp-18h]@4

  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = pdata;
  CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 0);
  if ( CRFWorldDatabase::update_amine_dck(pkDB, v6[1], v6[2], *(_DWORD *)(v6 + 3)) )
  {
    if ( CRFWorldDatabase::insert_amine_newowner(pkDB, v6[1], v6[2], *(_DWORD *)(v6 + 3)) )
    {
      CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&pkDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
      result = 0;
    }
    else
    {
      CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&pkDB->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
      result = 24;
    }
  }
  else
  {
    CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&pkDB->vfptr);
    CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&pkDB->vfptr, 1);
    result = 24;
  }
  return result;
}
