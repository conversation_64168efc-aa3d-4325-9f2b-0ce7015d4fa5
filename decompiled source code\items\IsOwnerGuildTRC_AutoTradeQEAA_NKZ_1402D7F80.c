/*
 * Function: ?IsOwnerGuild@TRC_AutoTrade@@QEAA_NK@Z
 * Address: 0x1402D7F80
 */

bool __fastcall TRC_AutoTrade::IsOwnerGuild(TRC_AutoTrade *this, unsigned int nGuildSerial)
{
  char *v2; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  bool v5; // [sp+0h] [bp-18h]@1
  TRC_AutoTrade *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (char *)&v5;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 += 4;
  }
  if ( v6->m_pOwnerGuild )
  {
    v5 = nGuildSerial == v6->m_pOwnerGuild->m_dwSerial;
    result = v5;
  }
  else
  {
    result = 0;
  }
  return result;
}
