/*
 * Function: _std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::erase_::_1_::dtor$2
 * Address: 0x140361A00
 */

void __fastcall std::vector_CUnmannedTraderRegistItemInfo_std::allocator_CUnmannedTraderRegistItemInfo___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if ( *(_DWORD *)(a2 + 40) & 1 )
  {
    *(_DWORD *)(a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(*(std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > **)(a2 + 88));
  }
}
