/*
 * Function: j_??$_Uninit_move@PEAKPEAKV?$allocator@K@std@@U_Undefined_move_tag@2@@std@@YAPEAKPEAK00AEAV?$allocator@K@0@U_Undefined_move_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1400055F6
 */

unsigned int *__fastcall std::_Uninit_move<unsigned long *,unsigned long *,std::allocator<unsigned long>,std::_Undefined_move_tag>(unsigned int *_First, unsigned int *_Last, unsigned int *_Dest, std::allocator<unsigned long> *_Al, std::_Undefined_move_tag __formal, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_move<unsigned long *,unsigned long *,std::allocator<unsigned long>,std::_Undefined_move_tag>(
           _First,
           _Last,
           _<PERSON>t,
           _<PERSON>,
           __formal,
           a6);
}
