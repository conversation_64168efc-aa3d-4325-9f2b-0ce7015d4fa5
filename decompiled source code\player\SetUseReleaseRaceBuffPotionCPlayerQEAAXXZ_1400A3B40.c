/*
 * Function: ?SetUseReleaseRaceBuffPotion@CPlayer@@QEAAXXZ
 * Address: 0x1400A3B40
 */

void __fastcall CPlayer::SetUseReleaseRaceBuffPotion(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@4
  __int64 v4; // [sp+0h] [bp-48h]@1
  char v5; // [sp+30h] [bp-18h]@4
  char v6; // [sp+31h] [bp-17h]@4
  char v7; // [sp+32h] [bp-16h]@4
  char v8; // [sp+33h] [bp-15h]@4
  CPlayer *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v1 = &v4;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CHolyStoneSystem::GetNumOfTime(&g_HolySys);
  v6 = CHolyStoneSystem::GetStartHour(&g_HolySys);
  v7 = CHolyStoneSystem::GetStartDay(&g_HolySys);
  v8 = CHolyStoneSystem::GetStartMonth(&g_HolySys);
  v3 = CHolyStoneSystem::GetStartYear(&g_HolySys);
  MiningTicket::_AuthKeyTicket::Set(&v9->m_dwRaceBuffClearKey, v3, v8, v7, v6, v5);
  v9->m_pUserDB->m_AvatorData.dbSupplement.dwRaceBuffClear = v9->m_dwRaceBuffClearKey.uiData;
  v9->m_pUserDB->m_bDataUpdate = 1;
}
