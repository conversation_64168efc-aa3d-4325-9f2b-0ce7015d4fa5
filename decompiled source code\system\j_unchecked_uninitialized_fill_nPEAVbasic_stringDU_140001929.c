/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_KV12@V?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@stdext@@YAXPEAV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@_KAEBV12@AEAV?$allocator@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@@Z
 * Address: 0x140001929
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_First, unsigned __int64 _Count, std::basic_string<char,std::char_traits<char>,std::allocator<char> > *_Val, std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char> > > *_Al)
{
  stdext::unchecked_uninitialized_fill_n<std::basic_string<char,std::char_traits<char>,std::allocator<char>> *,unsigned __int64,std::basic_string<char,std::char_traits<char>,std::allocator<char>>,std::allocator<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>>(
    _First,
    _Count,
    _Val,
    _Al);
}
