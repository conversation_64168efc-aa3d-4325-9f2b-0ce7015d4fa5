/*
 * Function: ??4CGuildBattleReservedScheduleMapGroup@GUILD_BATTLE@@QEAAAEBV01@AEBV01@@Z
 * Address: 0x1403DC790
 */

GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *__fastcall GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup::operator=(GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *this, GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *kObj)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int j; // [sp+0h] [bp-18h]@1
  GUILD_BATTLE::CGuildBattleReservedScheduleMapGroup *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v6->m_bDone = kObj->m_bDone;
  v6->m_uiMapCnt = kObj->m_uiMapCnt;
  for ( j = 0; j < v6->m_uiMapCnt; ++j )
    v6->m_ppkReservedSchedule[j] = kObj->m_ppkReservedSchedule[j];
  return v6;
}
