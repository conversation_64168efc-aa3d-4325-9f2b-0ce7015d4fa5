/*
 * Function: j_??$_Uninit_copy@PEAPEAVCMoveMapLimitInfo@@PEAPEAV1@V?$allocator@PEAVCMoveMapLimitInfo@@@std@@@std@@YAPEAPEAVCMoveMapLimitInfo@@PEAPEAV1@00AEAV?$allocator@PEAVCMoveMapLimitInfo@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000F1D7
 */

CMoveMapLimitInfo **__fastcall std::_Uninit_copy<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(CMoveMapLimitInfo **_First, CMoveMapLimitInfo **_Last, CMoveMapLimitInfo **_Dest, std::allocator<CMoveMapLimitInfo *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  return std::_Uninit_copy<CMoveMapLimitInfo * *,CMoveMapLimitInfo * *,std::allocator<CMoveMapLimitInfo *>>(
           _First,
           _Last,
           _Dest,
           __formal,
           a5,
           a6);
}
