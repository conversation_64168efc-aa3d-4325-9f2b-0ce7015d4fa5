/*
 * Function: ?SendMsg_TrunkResDivision@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@0@Z
 * Address: 0x1400E36A0
 */

void __fastcall CPlayer::SendMsg_TrunkResDivision(CPlayer *this, char byErrCode, _STORAGE_LIST::_db_con *pStartOre, _STORAGE_LIST::_db_con *pTargetOre)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v8; // [sp+39h] [bp-4Fh]@5
  __int16 v9; // [sp+3Bh] [bp-4Dh]@6
  unsigned __int16 v10; // [sp+3Dh] [bp-4Bh]@8
  __int16 v11; // [sp+3Fh] [bp-49h]@8
  char pbyType; // [sp+64h] [bp-24h]@9
  char v13; // [sp+65h] [bp-23h]@9
  CPlayer *v14; // [sp+90h] [bp+8h]@1

  v14 = this;
  v4 = &v6;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  if ( !byErrCode )
  {
    v8 = pStartOre->m_wSerial;
    if ( pStartOre->m_bLoad )
      v9 = pStartOre->m_dwDur;
    else
      v9 = 0;
    v10 = pTargetOre->m_wSerial;
    v11 = pTargetOre->m_dwDur;
  }
  pbyType = 34;
  v13 = 11;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 9u);
}
