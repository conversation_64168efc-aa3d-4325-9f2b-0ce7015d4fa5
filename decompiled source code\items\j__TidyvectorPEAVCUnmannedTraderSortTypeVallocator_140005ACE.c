/*
 * Function: j_?_Tidy@?$vector@PEAVCUnmannedTraderSortType@@V?$allocator@PEAVCUnmannedTraderSortType@@@std@@@std@@IEAAXXZ
 * Address: 0x140005ACE
 */

void __fastcall std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Tidy(std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *> > *this)
{
  std::vector<CUnmannedTraderSortType *,std::allocator<CUnmannedTraderSortType *>>::_Tidy(this);
}
