/*
 * Function: ?init@__list@_qry_case_all_store_limit_item@@QEAAXXZ
 * Address: 0x14034BCA0
 */

void __fastcall _qry_case_all_store_limit_item::__list::init(_qry_case_all_store_limit_item::__list *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _qry_case_all_store_limit_item::__list *Dst; // [sp+40h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0xA0ui64);
  for ( j = 0; j < 16; ++j )
    _INVENKEY::SetRelease((_INVENKEY *)&Dst->ItemData[j]);
}
