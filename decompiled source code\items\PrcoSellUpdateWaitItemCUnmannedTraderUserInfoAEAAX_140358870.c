/*
 * Function: ?PrcoSellUpdateWaitItem@CUnmannedTraderUserInfo@@AEAAXPEAU_qry_case_unmandtrader_log_in_proc_update_complete@@EPEAVCLogFile@@@Z
 * Address: 0x140358870
 */

void __fastcall CUnmannedTraderUserInfo::PrcoSellUpdateWaitItem(CUnmannedTraderUserInfo *this, _qry_case_unmandtrader_log_in_proc_update_complete *pkResult, char byGroupType, CLogFile *pkLogger)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderRegistItemInfo *v6; // rax@13
  CUnmannedTraderRegistItemInfo *v7; // rax@14
  unsigned __int16 v8; // ax@14
  CUnmannedTraderRegistItemInfo *v9; // rax@15
  CUnmannedTraderRegistItemInfo *v10; // rax@15
  CUnmannedTraderRegistItemInfo *v11; // rax@16
  CUnmannedTraderRegistItemInfo *v12; // rax@16
  CUnmannedTraderRegistItemInfo *v13; // rax@16
  __int64 v14; // [sp+0h] [bp-48h]@1
  CPlayer *v15; // [sp+20h] [bp-28h]@4
  _STORAGE_LIST::_db_con *v16; // [sp+28h] [bp-20h]@10
  int j; // [sp+30h] [bp-18h]@10
  unsigned int v18; // [sp+34h] [bp-14h]@7
  CUnmannedTraderUserInfo *v19; // [sp+50h] [bp+8h]@1
  _qry_case_unmandtrader_log_in_proc_update_complete *v20; // [sp+58h] [bp+10h]@1
  CLogFile *v21; // [sp+68h] [bp+20h]@1

  v21 = pkLogger;
  v20 = pkResult;
  v19 = this;
  v4 = &v14;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = CUnmannedTraderUserInfo::FindOwner(v19);
  if ( v15 && v15->m_bOper )
  {
    v16 = 0i64;
    for ( j = 0; j < 20; ++j )
    {
      v20->List[j].byProcUpdate = -1;
      v6 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
             &v19->m_vecLoadItemInfo,
             j);
      if ( CUnmannedTraderRegistItemInfo::IsSellUpdateWait(v6) )
      {
        v7 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
               &v19->m_vecLoadItemInfo,
               j);
        v8 = CUnmannedTraderRegistItemInfo::GetItemSerial(v7);
        v16 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v15->m_Param.m_dbInven.m_nListNum, v8);
        if ( v16 )
        {
          v20->List[v20->wNum].byProcUpdate = 92;
          v20->List[v20->wNum].dwBuyer = 0;
          v11 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v19->m_vecLoadItemInfo,
                  j);
          v20->List[v20->wNum].dwRegistSerial = CUnmannedTraderRegistItemInfo::GetRegistSerial(v11);
          v20->List[v20->wNum++].byUpdateState = 1;
          v12 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v19->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::ClearBuyerInfo(v12);
          v13 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v19->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::SetState(v13, 1);
        }
        else
        {
          v20->List[v20->wNum].byProcUpdate = 83;
          v20->List[v20->wNum].dwBuyer = 0;
          v9 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                 &v19->m_vecLoadItemInfo,
                 j);
          v20->List[v20->wNum].dwRegistSerial = CUnmannedTraderRegistItemInfo::GetRegistSerial(v9);
          v20->List[v20->wNum++].byUpdateState = 5;
          v10 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator[](
                  &v19->m_vecLoadItemInfo,
                  j);
          CUnmannedTraderRegistItemInfo::ClearToWaitState(v10);
        }
      }
    }
  }
  else
  {
    if ( v15 )
      v18 = v15->m_bOper;
    else
      v18 = -1;
    CLogFile::Write(
      v21,
      "CUnmannedTraderUserInfo::PrcoSellUpdateWaitItem()\r\n\t\t( 0 == pkOwner || !pkOwner->m_bOper(%d) )\r\n",
      v18);
  }
}
