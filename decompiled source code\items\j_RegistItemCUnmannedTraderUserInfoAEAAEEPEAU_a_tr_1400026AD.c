/*
 * Function: j_?RegistItem@CUnmannedTraderUserInfo@@AEAAEEPEAU_a_trade_reg_item_request_clzo@@EEEEKK@Z
 * Address: 0x1400026AD
 */

char __fastcall CUnmannedTraderUserInfo::RegistItem(CUnmannedTraderUserInfo *this, char byType, _a_trade_reg_item_request_clzo *pRequest, char byTempSlotIndex, char byDivision, char byClass, char bySubClass, unsigned int dwListIndex, unsigned int dwTax)
{
  return CUnmannedTraderUserInfo::RegistItem(
           this,
           byType,
           pRequest,
           byTempSlotIndex,
           byDivision,
           byClass,
           bySubClass,
           dwListIndex,
           dwTax);
}
