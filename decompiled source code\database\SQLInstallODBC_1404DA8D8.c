/*
 * Function: SQLInstallODBC
 * Address: 0x1404DA8D8
 */

int __fastcall SQLInstallODBC(HWND__ *hwndParent, const char *lpszInfFile, const char *lpszSrcPath, const char *lpszDrivers)
{
  HWND__ *v4; // rbp@1
  const char *v5; // rbx@1
  const char *v6; // rdi@1
  const char *v7; // rsi@1
  __int64 (__cdecl *v8)(); // rax@1
  int result; // eax@2

  v4 = hwndParent;
  v5 = lpszDrivers;
  v6 = lpszSrcPath;
  v7 = lpszInfFile;
  v8 = ODBC___GetSetupProc("SQLInstallODBC");
  if ( v8 )
    result = ((int (__fastcall *)(HWND__ *, const char *, const char *, const char *))v8)(v4, v7, v6, v5);
  else
    result = 0;
  return result;
}
