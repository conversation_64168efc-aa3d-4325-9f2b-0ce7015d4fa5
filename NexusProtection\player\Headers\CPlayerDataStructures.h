#pragma once

/**
 * @file CPlayerDataStructures.h
 * @brief Modern C++20 player data structures
 * 
 * This file provides modern, type-safe implementations of player-related
 * data structures, replacing legacy C structures with modern C++20 equivalents.
 */

#include <memory>
#include <string>
#include <array>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <atomic>
#include <cstdint>

namespace NexusProtection {
namespace Player {

/**
 * @brief Object identifier structure
 */
struct ObjectId {
    uint16_t wIndex{0};      ///< Object index
    uint32_t dwSerial{0};    ///< Object serial number
    
    ObjectId() = default;
    ObjectId(uint16_t index, uint32_t serial) : wIndex(index), dwSerial(serial) {}
    
    bool IsValid() const { return wIndex != 0 || dwSerial != 0; }
    void Clear() { wIndex = 0; dwSerial = 0; }
    
    bool operator==(const ObjectId& other) const {
        return wIndex == other.wIndex && dwSerial == other.dwSerial;
    }
    
    bool operator!=(const ObjectId& other) const {
        return !(*this == other);
    }
};

/**
 * @brief Player position structure
 */
struct PlayerPosition {
    std::array<float, 3> coordinates{0.0f, 0.0f, 0.0f};  ///< X, Y, Z coordinates
    float rotation{0.0f};                                  ///< Player rotation
    uint8_t mapCode{0};                                   ///< Current map code
    uint16_t layerIndex{0};                               ///< Layer index
    
    PlayerPosition() = default;
    
    void SetPosition(float x, float y, float z) {
        coordinates[0] = x;
        coordinates[1] = y;
        coordinates[2] = z;
    }
    
    void GetPosition(float& x, float& y, float& z) const {
        x = coordinates[0];
        y = coordinates[1];
        z = coordinates[2];
    }
};

/**
 * @brief Player statistics structure
 */
struct PlayerStatistics {
    // Core stats
    uint32_t level{1};
    uint64_t experience{0};
    uint32_t skillPoints{0};
    uint32_t statPoints{0};
    
    // Attributes
    uint32_t strength{0};
    uint32_t dexterity{0};
    uint32_t intelligence{0};
    uint32_t constitution{0};
    
    // Derived stats
    uint32_t maxHP{100};
    uint32_t maxFP{100};
    uint32_t maxSP{100};
    uint32_t maxDP{0};
    
    // Current values
    uint32_t currentHP{100};
    uint32_t currentFP{100};
    uint32_t currentSP{100};
    uint32_t currentDP{0};
    
    // Combat stats
    uint32_t attackPower{0};
    uint32_t defensePower{0};
    uint32_t accuracy{0};
    uint32_t avoidance{0};
    uint32_t criticalRate{0};
    
    PlayerStatistics() = default;
    
    bool IsValid() const {
        return level > 0 && maxHP > 0 && maxFP > 0 && maxSP > 0;
    }
    
    void RestoreToFull() {
        currentHP = maxHP;
        currentFP = maxFP;
        currentSP = maxSP;
        currentDP = maxDP;
    }
};

/**
 * @brief Player equipment slot enumeration
 */
enum class EquipmentSlot : uint8_t {
    Weapon = 0,
    Shield,
    Helmet,
    Armor,
    Gloves,
    Boots,
    Ring1,
    Ring2,
    Necklace,
    Earring1,
    Earring2,
    Belt,
    MaxSlots = 12
};

/**
 * @brief Item information structure
 */
struct ItemInfo {
    uint32_t itemCode{0};        ///< Item type code
    uint32_t itemSerial{0};      ///< Unique item serial
    uint16_t quantity{0};        ///< Item quantity
    uint8_t durability{100};     ///< Item durability (0-100)
    uint8_t enhancement{0};      ///< Enhancement level
    uint32_t options{0};         ///< Item options bitfield
    
    ItemInfo() = default;
    ItemInfo(uint32_t code, uint16_t qty = 1) : itemCode(code), quantity(qty) {}
    
    bool IsValid() const { return itemCode != 0 && quantity > 0; }
    bool IsEmpty() const { return itemCode == 0 || quantity == 0; }
    void Clear() { *this = ItemInfo{}; }
};

/**
 * @brief Player inventory structure
 */
class PlayerInventory {
public:
    static constexpr size_t INVENTORY_SIZE = 100;
    static constexpr size_t EQUIPMENT_SLOTS = static_cast<size_t>(EquipmentSlot::MaxSlots);
    
    PlayerInventory() {
        m_items.fill(ItemInfo{});
        m_equipment.fill(ItemInfo{});
    }
    
    // Inventory management
    bool AddItem(const ItemInfo& item, size_t& slot);
    bool RemoveItem(size_t slot, uint16_t quantity = 0);
    ItemInfo GetItem(size_t slot) const;
    bool SetItem(size_t slot, const ItemInfo& item);
    
    // Equipment management
    bool EquipItem(EquipmentSlot slot, const ItemInfo& item);
    bool UnequipItem(EquipmentSlot slot);
    ItemInfo GetEquippedItem(EquipmentSlot slot) const;
    
    // Utility
    size_t GetFreeSlot() const;
    size_t GetItemCount() const;
    bool IsFull() const;
    void Clear();
    
private:
    std::array<ItemInfo, INVENTORY_SIZE> m_items;
    std::array<ItemInfo, EQUIPMENT_SLOTS> m_equipment;
    mutable std::mutex m_inventoryMutex;
};

/**
 * @brief Player skill information
 */
struct SkillInfo {
    uint32_t skillCode{0};       ///< Skill type code
    uint8_t level{0};            ///< Skill level
    uint32_t experience{0};      ///< Skill experience
    uint32_t cooldownEnd{0};     ///< Cooldown end time
    bool isActive{false};        ///< Whether skill is active
    
    SkillInfo() = default;
    SkillInfo(uint32_t code, uint8_t lvl = 1) : skillCode(code), level(lvl) {}
    
    bool IsValid() const { return skillCode != 0; }
    bool IsOnCooldown(uint32_t currentTime) const { return currentTime < cooldownEnd; }
    void SetCooldown(uint32_t currentTime, uint32_t duration) { cooldownEnd = currentTime + duration; }
};

/**
 * @brief Player skill manager
 */
class PlayerSkillManager {
public:
    static constexpr size_t MAX_SKILLS = 200;
    
    PlayerSkillManager() = default;
    
    // Skill management
    bool LearnSkill(uint32_t skillCode, uint8_t level = 1);
    bool ForgetSkill(uint32_t skillCode);
    bool UpgradeSkill(uint32_t skillCode);
    SkillInfo GetSkill(uint32_t skillCode) const;
    
    // Skill usage
    bool CanUseSkill(uint32_t skillCode, uint32_t currentTime) const;
    bool UseSkill(uint32_t skillCode, uint32_t currentTime, uint32_t cooldown);
    
    // Utility
    std::vector<SkillInfo> GetAllSkills() const;
    size_t GetSkillCount() const;
    void Clear();
    
private:
    std::unordered_map<uint32_t, SkillInfo> m_skills;
    mutable std::mutex m_skillMutex;
};

/**
 * @brief Player quest information
 */
struct QuestInfo {
    uint32_t questCode{0};       ///< Quest type code
    uint8_t state{0};            ///< Quest state (0=inactive, 1=active, 2=completed)
    uint32_t progress{0};        ///< Quest progress value
    uint32_t startTime{0};       ///< Quest start timestamp
    uint32_t completeTime{0};    ///< Quest completion timestamp
    std::array<uint32_t, 8> variables{};  ///< Quest variables
    
    QuestInfo() = default;
    QuestInfo(uint32_t code) : questCode(code) {}
    
    bool IsValid() const { return questCode != 0; }
    bool IsActive() const { return state == 1; }
    bool IsCompleted() const { return state == 2; }
    void SetActive() { state = 1; }
    void SetCompleted() { state = 2; completeTime = static_cast<uint32_t>(std::time(nullptr)); }
};

/**
 * @brief Player quest manager
 */
class PlayerQuestManager {
public:
    static constexpr size_t MAX_ACTIVE_QUESTS = 50;
    
    PlayerQuestManager() = default;
    
    // Quest management
    bool StartQuest(uint32_t questCode);
    bool CompleteQuest(uint32_t questCode);
    bool AbandonQuest(uint32_t questCode);
    QuestInfo GetQuest(uint32_t questCode) const;
    
    // Quest progress
    bool UpdateQuestProgress(uint32_t questCode, uint32_t progress);
    bool SetQuestVariable(uint32_t questCode, size_t index, uint32_t value);
    uint32_t GetQuestVariable(uint32_t questCode, size_t index) const;
    
    // Utility
    std::vector<QuestInfo> GetActiveQuests() const;
    std::vector<QuestInfo> GetCompletedQuests() const;
    size_t GetActiveQuestCount() const;
    void Clear();
    
private:
    std::unordered_map<uint32_t, QuestInfo> m_quests;
    mutable std::mutex m_questMutex;
};

/**
 * @brief Player social information
 */
struct SocialInfo {
    std::string guildName;       ///< Guild name
    uint8_t guildRank{0};        ///< Guild rank
    uint32_t guildJoinTime{0};   ///< Guild join timestamp
    std::vector<std::string> friendList;    ///< Friend list
    std::vector<std::string> blockList;     ///< Blocked players list
    
    SocialInfo() = default;
    
    bool IsInGuild() const { return !guildName.empty(); }
    bool IsFriend(const std::string& playerName) const;
    bool IsBlocked(const std::string& playerName) const;
    void AddFriend(const std::string& playerName);
    void RemoveFriend(const std::string& playerName);
    void BlockPlayer(const std::string& playerName);
    void UnblockPlayer(const std::string& playerName);
};

/**
 * @brief Complete avatar data structure
 * 
 * Modern replacement for the legacy _AVATOR_DATA structure
 */
struct AvatarData {
    // Basic information
    ObjectId playerId;
    std::string playerName;
    std::string accountName;
    uint8_t playerClass{0};
    uint8_t gender{0};
    uint32_t creationTime{0};
    uint32_t lastLoginTime{0};
    uint32_t totalPlayTime{0};
    
    // Position and map
    PlayerPosition position;
    PlayerPosition lastSafePosition;
    
    // Statistics
    PlayerStatistics stats;
    
    // Game systems
    PlayerInventory inventory;
    PlayerSkillManager skillManager;
    PlayerQuestManager questManager;
    SocialInfo socialInfo;
    
    // Flags and settings
    uint32_t playerFlags{0};
    uint32_t systemFlags{0};
    uint8_t pkMode{0};
    uint8_t tradeMode{0};
    
    AvatarData() = default;
    
    bool IsValid() const {
        return playerId.IsValid() && !playerName.empty() && stats.IsValid();
    }
    
    void Clear() {
        *this = AvatarData{};
    }
};

} // namespace Player
} // namespace NexusProtection
