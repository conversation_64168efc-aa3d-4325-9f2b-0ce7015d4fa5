/*
 * Function: ?UpdateClearGuildBattleInfo@CRFWorldDatabase@@QEAA_NKK@Z
 * Address: 0x1404A2AC0
 */

bool __fastcall CRFWorldDatabase::UpdateClearGuildBattleInfo(CRFWorldDatabase *this, unsigned int dwStartID, unsigned int dwEndID)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@4
  unsigned __int64 v8; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v9; // [sp+460h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pUpdate_ClearGuildBattleInfo( %u, %u ) }", dwStartID, dwEndID);
  return CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v9->vfptr, &Dest, 1);
}
