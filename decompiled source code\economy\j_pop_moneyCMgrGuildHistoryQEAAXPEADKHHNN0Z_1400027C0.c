/*
 * Function: j_?pop_money@CMgrGuildHistory@@QEAAXPEADKHHNN0@Z
 * Address: 0x1400027C0
 */

void __fastcall CMgrGuildHistory::pop_money(CMgrGuildHistory *this, char *pszIOerName, unsigned int dwIOerSerial, int nPopDalant, int nPopGold, long double dTotalDalant, long double dTotalGold, char *pszFileName)
{
  CMgrGuildHistory::pop_money(
    this,
    pszIOerName,
    dwIOerSerial,
    nPopDalant,
    nPopGold,
    dTotalDalant,
    dTotalGold,
    pszFileName);
}
