/*
 * Function: ?RentRoomTimer@CGuildRoomSystem@@QEAAXXZ
 * Address: 0x1402EA1A0
 */

void __fastcall CGuildRoomSystem::RentRoomTimer(CGuildRoomSystem *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomInfo *v3; // rax@6
  CGuildRoomInfo *v4; // rax@7
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  CGuildRoomSystem *v7; // [sp+40h] [bp+8h]@1

  v7 = this;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 90; ++j )
  {
    v3 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v7->m_vecGuildRoom, j);
    if ( CGuildRoomInfo::IsRent(v3) )
    {
      v4 = std::vector<CGuildRoomInfo,std::allocator<CGuildRoomInfo>>::operator[](&v7->m_vecGuildRoom, j);
      CGuildRoomInfo::RoomTimer(v4);
    }
  }
}
