/*
 * Function: ?PushBuddy@_BUDDY_LIST@@QEAAHKPEADPEAVCPlayer@@@Z
 * Address: 0x1400904C0
 */

signed __int64 __fastcall _BUDDY_LIST::PushBuddy(_BUDDY_LIST *this, unsigned int dwSerial, char *pwszName, CPlayer *pPtr)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@18
  __int64 v7; // [sp+0h] [bp-38h]@1
  unsigned int v8; // [sp+20h] [bp-18h]@4
  unsigned int j; // [sp+24h] [bp-14h]@4
  _BUDDY_LIST *v10; // [sp+28h] [bp-10h]@19
  _BUDDY_LIST *v11; // [sp+40h] [bp+8h]@1
  unsigned int v12; // [sp+48h] [bp+10h]@1
  const char *Source; // [sp+50h] [bp+18h]@1
  CPlayer *v14; // [sp+58h] [bp+20h]@1

  v14 = pPtr;
  Source = pwszName;
  v12 = dwSerial;
  v11 = this;
  v4 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = -1;
  for ( j = 0; (signed int)j < 50; ++j )
  {
    if ( _BUDDY_LIST::__list::fill((_BUDDY_LIST::__list *)v11 + (signed int)j) && v11->m_List[j].dwSerial == v12 )
    {
      v8 = j;
      break;
    }
  }
  if ( v8 == -1 )
  {
    for ( j = 0; (signed int)j < 50; ++j )
    {
      if ( !_BUDDY_LIST::__list::fill((_BUDDY_LIST::__list *)v11 + (signed int)j) )
      {
        v8 = j;
        break;
      }
    }
  }
  if ( v8 == -1 )
  {
    result = 0xFFFFFFFFi64;
  }
  else
  {
    v10 = (_BUDDY_LIST *)((char *)v11 + 32 * (signed int)v8);
    v10->m_List[0].dwSerial = v12;
    strcpy_0(v10->m_List[0].wszName, Source);
    v10->m_List[0].pPtr = v14;
    result = v8;
  }
  return result;
}
