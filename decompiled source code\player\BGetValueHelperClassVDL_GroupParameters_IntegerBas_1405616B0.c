/*
 * Function: ??B?$GetValueHelperClass@V?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@VDL_GroupParameters_IntegerBased@2@@CryptoPP@@QEBA_NXZ
 * Address: 0x1405616B0
 */

char __fastcall CryptoPP::GetValueHelperClass<CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>,CryptoPP::DL_GroupParameters_IntegerBased>::operator bool(__int64 a1)
{
  return *(_BYTE *)(a1 + 32);
}
