/*
 * Function: ?GetPtrFromItemCode@_STORAGE_LIST@@QEAAPEAU_db_con@1@PEAD@Z
 * Address: 0x14010F6D0
 */

_STORAGE_LIST::_db_con *__fastcall _STORAGE_LIST::GetPtrFromItemCode(_STORAGE_LIST *this, char *pwszItemCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _base_fld *v7; // [sp+28h] [bp-10h]@8
  _STORAGE_LIST *v8; // [sp+40h] [bp+8h]@1
  const char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = pwszItemCode;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  for ( j = 0; j < v8->m_nUsedNum; ++j )
  {
    if ( v8->m_pStorageList[j].m_bLoad )
    {
      v7 = CRecordData::GetRecord(
             (CRecordData *)&unk_1799C6AA0 + v8->m_pStorageList[j].m_byTableCode,
             v8->m_pStorageList[j].m_wItemIndex);
      if ( v7 )
      {
        if ( !strcmp_0(v7->m_strCode, Str2) )
          return &v8->m_pStorageList[j];
      }
    }
  }
  return 0i64;
}
