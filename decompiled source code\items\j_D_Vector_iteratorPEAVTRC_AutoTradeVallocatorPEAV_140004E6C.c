/*
 * Function: j_??D?$_Vector_iterator@PEAVTRC_AutoTrade@@V?$allocator@PEAVTRC_AutoTrade@@@std@@@std@@QEBAAEAPEAVTRC_AutoTrade@@XZ
 * Address: 0x140004E6C
 */

TRC_AutoTrade **__fastcall std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator*(std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *> > *this)
{
  return std::_Vector_iterator<TRC_AutoTrade *,std::allocator<TRC_AutoTrade *>>::operator*(this);
}
