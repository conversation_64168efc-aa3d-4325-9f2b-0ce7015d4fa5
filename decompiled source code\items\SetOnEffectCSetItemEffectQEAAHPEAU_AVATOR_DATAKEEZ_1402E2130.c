/*
 * Function: ?SetOnEffect@CSetItemEffect@@QEAAHPEAU_AVATOR_DATA@@KEE@Z
 * Address: 0x1402E2130
 */

signed __int64 __fastcall CSetItemEffect::SetOnEffect(CSetItemEffect *this, _AVATOR_DATA *pData, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  CSUItemSystem *v8; // rax@6
  CSUItemSystem *v9; // rax@10
  char v10; // al@19
  __int64 v11; // [sp+0h] [bp-58h]@1
  CRecordData *v12; // [sp+20h] [bp-38h]@4
  _SetItemEff_fld *pSetFld; // [sp+28h] [bp-30h]@4
  CSetItemType *v14; // [sp+30h] [bp-28h]@4
  si_interpret *v15; // [sp+38h] [bp-20h]@4
  char v16; // [sp+40h] [bp-18h]@4
  char v17; // [sp+41h] [bp-17h]@4
  unsigned __int8 j; // [sp+42h] [bp-16h]@4
  int v19; // [sp+44h] [bp-14h]@19
  CSetItemEffect *v20; // [sp+60h] [bp+8h]@1
  _AVATOR_DATA *pDataa; // [sp+68h] [bp+10h]@1
  int n; // [sp+70h] [bp+18h]@1
  char v23; // [sp+78h] [bp+20h]@1

  v23 = bySetItemNum;
  n = dwSetItem;
  pDataa = pData;
  v20 = this;
  v5 = &v11;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v12 = 0i64;
  pSetFld = 0i64;
  v14 = 0i64;
  v15 = 0i64;
  v16 = 0;
  v17 = 0;
  j = 0;
  if ( pData )
  {
    v8 = CSUItemSystem::Instance();
    v12 = CSUItemSystem::GetCRecordData_SetItem(v8);
    if ( v12 )
    {
      pSetFld = (_SetItemEff_fld *)CRecordData::GetRecord(v12, n);
      if ( pSetFld )
      {
        v9 = CSUItemSystem::Instance();
        v14 = CSUItemSystem::GetCSetItemType(v9);
        if ( v14 )
        {
          v15 = CSetItemType::Getsi_interpret(v14, n);
          if ( v15 )
          {
            v16 = CSetItemEffect::Check_EquipItem(v20, pDataa, pSetFld);
            if ( (unsigned __int8)v16 >= (signed int)(unsigned __int8)v23 )
            {
              v17 = si_interpret::GetEffectTypeCount(v15);
              for ( j = 0; j < (signed int)(unsigned __int8)v17; ++j )
              {
                if ( (unsigned __int8)si_interpret::GetCountOfItem(v15, j) == (unsigned __int8)v23 )
                {
                  v19 = (unsigned __int8)bySetEffectNum;
                  v10 = si_interpret::GetCountOfEffect(v15, j);
                  if ( v19 == (unsigned __int8)v10 )
                    break;
                }
              }
              if ( j == (unsigned __int8)v17 )
              {
                result = 4i64;
              }
              else if ( CSetItemEffect::IsSetOn(v20, n) )
              {
                if ( CSetItemEffect::IsSetOnComplete(v20, n, v23, bySetEffectNum) )
                {
                  result = 5i64;
                }
                else
                {
                  CSetItemEffect::Reset_Set(v20, n, v23, bySetEffectNum);
                  result = 8i64;
                }
              }
              else
              {
                CSetItemEffect::Attach_Set(v20, n, v23, bySetEffectNum);
                result = 0i64;
              }
            }
            else
            {
              result = 3i64;
            }
          }
          else
          {
            result = 2i64;
          }
        }
        else
        {
          result = 7i64;
        }
      }
      else
      {
        result = 2i64;
      }
    }
    else
    {
      result = 7i64;
    }
  }
  else
  {
    result = 7i64;
  }
  return result;
}
