/*
 * Function: ?pc_PvpCashRecorver@CPlayer@@QEAAXKE@Z
 * Address: 0x1400F5FD0
 */

void __usercall CPlayer::pc_PvpCashRecorver(CPlayer *this@<rcx>, unsigned int dwItemSerial@<edx>, char byItemCnt@<r8b>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CPvpCashMng *v6; // rax@9
  __int64 v7; // [sp+0h] [bp-88h]@1
  bool bDelete; // [sp+20h] [bp-68h]@21
  char *strErrorCodePos; // [sp+28h] [bp-60h]@21
  char v10; // [sp+30h] [bp-58h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+38h] [bp-50h]@4
  _base_fld *v12; // [sp+40h] [bp-48h]@7
  int nRecvrPoint; // [sp+48h] [bp-40h]@12
  int v14; // [sp+4Ch] [bp-3Ch]@12
  int v15; // [sp+50h] [bp-38h]@12
  double v16; // [sp+58h] [bp-30h]@12
  double v17; // [sp+60h] [bp-28h]@18
  int j; // [sp+68h] [bp-20h]@15
  char *strCode; // [sp+70h] [bp-18h]@9
  CPlayer *v20; // [sp+90h] [bp+8h]@1
  char v21; // [sp+A0h] [bp+18h]@1

  v21 = byItemCnt;
  v20 = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v10 = 0;
  pItem = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v20->m_Param.m_dbInven.m_nListNum, dwItemSerial);
  if ( pItem && pItem->m_dwDur >= (unsigned __int8)v21 )
  {
    v12 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, pItem->m_wItemIndex);
    if ( v12 )
    {
      strCode = v12->m_strCode;
      v6 = CPvpCashMng::Instance();
      if ( !CPvpCashMng::IsTalikItem(v6, strCode) )
        v10 = 1;
    }
    else
    {
      v10 = 1;
    }
  }
  else
  {
    v10 = 1;
  }
  if ( v10 )
  {
    CPvpCashPoint::SendMsg_RecoverResult(&v20->m_kPvpCashPoint, v20->m_ObjID.m_wIndex, v10, 0);
  }
  else
  {
    nRecvrPoint = CPvpCashPoint::GetTalikRecvrPoint(&v20->m_kPvpCashPoint, pItem->m_byTableCode, v12->m_dwIndex);
    v14 = 0;
    v15 = 0;
    CPvpOrderView::GetPvpCash(&v20->m_kPvpOrderView);
    v16 = a4;
    if ( a4 >= 0.0 )
      v10 = 2;
    CPvpCashPoint::SendMsg_RecoverResult(&v20->m_kPvpCashPoint, v20->m_ObjID.m_wIndex, v10, nRecvrPoint);
    if ( !v10 )
    {
      for ( j = 1; j <= (unsigned __int8)v21; ++j )
      {
        v15 += nRecvrPoint;
        v14 = j;
        if ( v16 + (double)v15 >= 0.0 )
        {
          v17 = -0.0 - v16;
          break;
        }
        v17 = (double)v15;
      }
      CPlayer::AlterPvPCashBag(v20, v17, pm_reward);
      if ( IsOverLapItem(pItem->m_byTableCode) )
      {
        LOBYTE(strErrorCodePos) = 1;
        bDelete = 1;
        CPlayer::Emb_AlterDurPoint(v20, 0, pItem->m_byStorageIndex, -v14, 1, 1);
      }
      else
      {
        strErrorCodePos = "CPlayer::pc_PvpCashRecorver";
        bDelete = 1;
        if ( CPlayer::Emb_DelStorage(v20, 0, pItem->m_byStorageIndex, 0, 1, "CPlayer::pc_PvpCashRecorver") )
          CMgrAvatorItemHistory::consume_del_item(
            &CPlayer::s_MgrItemHistory,
            v20->m_ObjID.m_wIndex,
            pItem,
            v20->m_szItemHistoryFileName);
        else
          CPlayer::AlterPvPCashBag(v20, -0.0 - v17, pm_reward);
      }
    }
  }
}
