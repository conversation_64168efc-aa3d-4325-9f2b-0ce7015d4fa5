/*
 * Function: ?SF_IncHPCircleParty@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x14009F820
 */

bool __fastcall CPlayer::SF_IncHPCircleParty(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  int v6; // eax@15
  __int64 v7; // [sp+0h] [bp-B8h]@1
  int v8; // [sp+20h] [bp-98h]@6
  CPlayer *out_ppMember; // [sp+40h] [bp-78h]@6
  char v10; // [sp+84h] [bp-34h]@6
  int j; // [sp+88h] [bp-30h]@6
  int v12; // [sp+8Ch] [bp-2Ch]@15
  int v13; // [sp+90h] [bp-28h]@15
  __int64 *v14; // [sp+98h] [bp-20h]@15
  __int64 v15; // [sp+A0h] [bp-18h]@15
  CPlayer *v16; // [sp+C0h] [bp+8h]@1

  v16 = this;
  v3 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CPartyPlayer::IsPartyMode(v16->m_pPartyMgr) )
  {
    v8 = 0;
    v10 = CPlayer::_GetPartyMemberInCircle(v16, &out_ppMember, 8, 1);
    for ( j = 0; j < (unsigned __int8)v10; ++j )
    {
      if ( !(*(&out_ppMember + j))->m_bCorpse
        && (!(*(&out_ppMember + j))->m_bInGuildBattle || !(*(&out_ppMember + j))->m_bTakeGravityStone)
        && (!v16->m_bInGuildBattle
         || (*(&out_ppMember + j))->m_bInGuildBattle
         && v16->m_byGuildBattleColorInx == (*(&out_ppMember + j))->m_byGuildBattleColorInx) )
      {
        v12 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetHP)(*(&out_ppMember + j));
        v13 = (signed int)ffloor(fEffectValue * (float)((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetMaxHP)(*(&out_ppMember + j)));
        v14 = (__int64 *)*(&out_ppMember + j);
        v15 = *v14;
        (*(void (__fastcall **)(__int64 *, _QWORD, _QWORD))(v15 + 96))(v14, (unsigned int)(v13 + v12), 0i64);
        v6 = ((int (__fastcall *)(_QWORD))(*(&out_ppMember + j))->vfptr->GetHP)(*(&out_ppMember + j));
        if ( v12 != v6 )
        {
          ++v8;
          (*(void (__fastcall **)(_QWORD))&(*(&out_ppMember + j))->vfptr->gap8[72])(*(&out_ppMember + j));
        }
      }
    }
    result = v8 > 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
