/*
 * Function: _std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::_Insert_n_::_1_::catch$1
 * Address: 0x1403D2070
 */

void __fastcall __noreturn std::vector_GUILD_BATTLE::CGuildBattleRewardItem_std::allocator_GUILD_BATTLE::CGuildBattleRewardItem___::_Insert_n_::_1_::catch_1(__int64 a1, __int64 a2)
{
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::_Destroy(
    *(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > **)(a2 + 208),
    (GUILD_BATTLE::CGuildBattleRewardItem *)(*(_QWORD *)(*(_QWORD *)(a2 + 216) + 16i64) + 16i64 * *(_QWORD *)(a2 + 224)),
    (GUILD_BATTLE::CGuildBattleRewardItem *)(*(_QWORD *)(*(_QWORD *)(a2 + 208) + 24i64) + 16i64 * *(_QWORD *)(a2 + 224)));
  CxxThrowException_0(0i64, 0i64);
}
