/*
 * Function: j_?invoke@?$void2val@P8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@SAP8CMonster@@EAAPEAVCLuaSignalReActor@@XZPEAX@Z
 * Address: 0x140013377
 */

CLuaSignalReActor *(__cdecl *__fastcall lua_tinker::void2val<CLuaSignalReActor * (CMonster::*)(void)>::invoke(lua_tinker::void2val<CLuaSignalReActor * (__cdecl CMonster::*)(void)> *this, void *input))(CMonster *this)
{
  return lua_tinker::void2val<CLuaSignalReActor * (CMonster::*)(void)>::invoke(this, input);
}
