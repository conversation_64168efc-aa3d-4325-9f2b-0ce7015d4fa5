/*
 * Function: ?Send_ClienInform@CChiNetworkEX@@QEAAXPEAVCPlayer@@GPEAD@Z
 * Address: 0x1404102D0
 */

void __fastcall CChiNetworkEX::Send_ClienInform(CChiNetworkEX *this, CPlayer *pOne, unsigned __int16 wSize, char *pBuf)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  _apex_id *v6; // rax@4
  char *v7; // rax@4
  CAsyncLogger *v8; // rax@4
  __int64 v9; // [sp+0h] [bp-58h]@1
  unsigned __int16 nLen; // [sp+20h] [bp-38h]@4
  unsigned __int16 v11; // [sp+30h] [bp-28h]@4
  _apex_id v12; // [sp+34h] [bp-24h]@4
  CUserDB *v13; // [sp+38h] [bp-20h]@4
  CUserDB *v14; // [sp+40h] [bp-18h]@4
  CChiNetworkEX *v15; // [sp+60h] [bp+8h]@1
  CPlayer *v16; // [sp+68h] [bp+10h]@1
  char *szMsg; // [sp+78h] [bp+20h]@1

  szMsg = pBuf;
  v16 = pOne;
  v15 = this;
  v4 = &v9;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = wSize - 4;
  v13 = pOne->m_pUserDB;
  _apex_id::_apex_id(&v12, 84);
  v7 = _apex_id::operator&(v6);
  nLen = v11;
  CChiNetworkEX::Send(v15, v7, v13->m_dwAccountSerial, szMsg, v11);
  v14 = v16->m_pUserDB;
  v8 = CAsyncLogger::Instance();
  CAsyncLogger::FormatLog(v8, 12, "Send_ClienInform - %d", v14->m_dwAccountSerial);
}
