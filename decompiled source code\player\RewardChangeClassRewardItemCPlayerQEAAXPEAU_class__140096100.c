/*
 * Function: ?RewardChangeClassRewardItem@CPlayer@@QEAAXPEAU_class_fld@@E@Z
 * Address: 0x140096100
 */

void __fastcall CPlayer::RewardChangeClassRewardItem(CPlayer *this, _class_fld *pClassFld, char bySelectRewardItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-218h]@1
  CMapData *pMap; // [sp+30h] [bp-1E8h]@24
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-1E0h]@24
  float *pStdPos; // [sp+40h] [bp-1D8h]@24
  bool bHide; // [sp+48h] [bp-1D0h]@24
  int j; // [sp+50h] [bp-1C8h]@4
  char *Str1; // [sp+58h] [bp-1C0h]@9
  _STORAGE_LIST::_db_con pItem; // [sp+68h] [bp-1B0h]@10
  int nTableCode; // [sp+A4h] [bp-174h]@10
  _base_fld *v14; // [sp+A8h] [bp-170h]@14
  unsigned __int64 dwExp; // [sp+B0h] [bp-168h]@16
  char v16; // [sp+B8h] [bp-160h]@16
  char v17; // [sp+B9h] [bp-15Fh]@17
  char Dest; // [sp+D0h] [bp-148h]@23
  char pszClause; // [sp+170h] [bp-A8h]@24
  unsigned __int64 v20; // [sp+200h] [bp-18h]@4
  CPlayer *pOwner; // [sp+220h] [bp+8h]@1
  _class_fld *v22; // [sp+228h] [bp+10h]@1
  char v23; // [sp+230h] [bp+18h]@1

  v23 = bySelectRewardItem;
  v22 = pClassFld;
  pOwner = this;
  v3 = &v5;
  for ( i = 132i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v20 = (unsigned __int64)&v5 ^ _security_cookie;
  for ( j = 0; j < 9; ++j )
  {
    if ( (unsigned __int8)v23 == 255 || (unsigned __int8)v23 == j )
    {
      Str1 = v22->m_DefaultItem[j].strDefaultItem;
      if ( strncmp(Str1, "-1", 2ui64) )
      {
        _STORAGE_LIST::_db_con::_db_con(&pItem);
        nTableCode = GetItemTableCode(Str1);
        if ( nTableCode == -1 )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "error bonus item of class change .., class: %s, error table code",
            v22->m_strCode,
            Str1);
          continue;
        }
        if ( nTableCode == 19 )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "error bonus item of class change .., class: %s, unit key",
            v22->m_strCode,
            Str1);
          continue;
        }
        v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + nTableCode, Str1);
        if ( !v14 )
        {
          CLogFile::Write(
            &stru_1799C8E78,
            "error bonus item of class change .., class: %s, nothing in table",
            v22->m_strCode,
            Str1);
          continue;
        }
        dwExp = *((_DWORD *)Str1 + 16);
        v16 = GetItemKindCode(nTableCode);
        if ( v16 )
        {
          if ( v16 != 1 )
            continue;
          HIDWORD(dwExp) = GetMaxParamFromExp(v14->m_dwIndex, (unsigned int)dwExp);
        }
        else
        {
          v17 = GetDefItemUpgSocketNum(nTableCode, v14->m_dwIndex);
          HIDWORD(dwExp) = GetBitAfterSetLimSocket(v17);
        }
        pItem.m_byTableCode = nTableCode;
        pItem.m_wItemIndex = v14->m_dwIndex;
        pItem.m_dwDur = (unsigned int)dwExp;
        pItem.m_dwLv = HIDWORD(dwExp);
        if ( _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&pOwner->m_Param.m_dbInven.m_nListNum) == 255 )
        {
          bHide = 0;
          pStdPos = pOwner->m_fCurPos;
          wLayerIndex = pOwner->m_wMapLayerIndex;
          pMap = pOwner->m_pCurMap;
          CreateItemBox(&pItem, pOwner, 0xFFFFFFFF, 0, 0i64, 3, pMap, wLayerIndex, pOwner->m_fCurPos, 0);
          sprintf(&pszClause, "Class G (%s)", v22->m_strCode);
          CMgrAvatorItemHistory::reward_add_item(
            &CPlayer::s_MgrItemHistory,
            pOwner->m_ObjID.m_wIndex,
            &pszClause,
            &pItem,
            pOwner->m_szItemHistoryFileName);
        }
        else
        {
          pItem.m_wSerial = CPlayerDB::GetNewItemSerial(&pOwner->m_Param);
          if ( CPlayer::Emb_AddStorage(pOwner, 0, (_STORAGE_LIST::_storage_con *)&pItem.m_bLoad, 0, 1) )
          {
            CPlayer::SendMsg_RewardAddItem(pOwner, &pItem, 1);
            sprintf(&Dest, "Class (%s)", v22->m_strCode);
            CMgrAvatorItemHistory::reward_add_item(
              &CPlayer::s_MgrItemHistory,
              pOwner->m_ObjID.m_wIndex,
              &Dest,
              &pItem,
              pOwner->m_szItemHistoryFileName);
          }
        }
      }
    }
  }
}
