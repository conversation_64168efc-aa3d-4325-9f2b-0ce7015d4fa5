/*
 * Function: ?ChannelMessageEnd@BufferedTransformation@CryptoPP@@QEAA_NAEBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@H_N@Z
 * Address: 0x1405F7AA0
 */

bool __fastcall CryptoPP::BufferedTransformation::ChannelMessageEnd(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1

  LODWORD(v2) = (*(int (__fastcall **)(__int64, __int64, _QWORD, _QWORD))(*(_QWORD *)a1 + 272i64))(a1, a2, 0i64, 0i64);
  return v2 != 0;
}
