/*
 * Function: ?Select_PotionDelay@CRFWorldDatabase@@QEAAHKPEAU_worlddb_potion_delay_info@@@Z
 * Address: 0x1404C5220
 */

signed __int64 __fastcall CRFWorldDatabase::Select_PotionDelay(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_potion_delay_info *pPotionDelayInfo)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@13
  __int64 v6; // [sp+0h] [bp-538h]@1
  void *SQLStmt; // [sp+20h] [bp-518h]@18
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-510h]@30
  char v9; // [sp+3Fh] [bp-4F9h]@9
  char DstBuf; // [sp+40h] [bp-4F8h]@4
  char _Source; // [sp+460h] [bp-D8h]@4
  char v12; // [sp+461h] [bp-D7h]@4
  SQLLEN v13; // [sp+4F8h] [bp-40h]@30
  __int16 v14; // [sp+504h] [bp-34h]@14
  int nIndex; // [sp+508h] [bp-30h]@4
  unsigned __int8 v16; // [sp+50Ch] [bp-2Ch]@21
  int v17; // [sp+510h] [bp-28h]@27
  unsigned __int64 v18; // [sp+520h] [bp-18h]@4
  CRFWorldDatabase *v19; // [sp+540h] [bp+8h]@1
  unsigned int v20; // [sp+548h] [bp+10h]@1
  _worlddb_potion_delay_info *v21; // [sp+550h] [bp+18h]@1

  v21 = pPotionDelayInfo;
  v20 = dwSerial;
  v19 = this;
  v3 = &v6;
  for ( i = 332i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  _Source = 0;
  memset(&v12, 0, 0x7Fui64);
  sprintf_s(&DstBuf, 0x400ui64, "select ");
  for ( nIndex = 0; nIndex < 38; ++nIndex )
  {
    if ( CPotionMgr::IsPotionDelayUseIndex(&g_PotionMgr, nIndex) )
    {
      sprintf_s(&_Source, 0x80ui64, "PD%d,", (unsigned int)nIndex);
      strcat_s<1024>((char (*)[1024])&DstBuf, &_Source);
    }
  }
  *(&v9 + strlen_0(&DstBuf)) = 32;
  sprintf_s(&_Source, 0x80ui64, "from tbl_potion_delay where serial=%u", v20);
  strcat_s<1024>((char (*)[1024])&DstBuf, &_Source);
  if ( v19->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v19->vfptr, &DstBuf);
  if ( v19->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v19->vfptr) )
  {
    v14 = SQLExecDirect_0(v19->m_hStmtSelect, &DstBuf, -3);
    if ( v14 && v14 != 1 )
    {
      if ( v14 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v19->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v14, &DstBuf, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v14, v19->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v14 = SQLFetch_0(v19->m_hStmtSelect);
      if ( v14 && v14 != 1 )
      {
        v16 = 0;
        if ( v14 == 100 )
        {
          v16 = 2;
        }
        else
        {
          SQLStmt = v19->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v14, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v14, v19->m_hStmtSelect);
          v16 = 1;
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        result = v16;
      }
      else
      {
        v17 = 1;
        for ( nIndex = 0; nIndex < 38; ++nIndex )
        {
          if ( CPotionMgr::IsPotionDelayUseIndex(&g_PotionMgr, nIndex) )
          {
            StrLen_or_IndPtr = &v13;
            SQLStmt = 0i64;
            v14 = SQLGetData_0(v19->m_hStmtSelect, v17, 4, (char *)v21 + 4 * nIndex, 0i64, &v13);
            if ( v14 && v14 != 1 )
            {
              SQLStmt = v19->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v19->vfptr, v14, &DstBuf, "SQLGetData", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v19->vfptr, v14, v19->m_hStmtSelect);
              if ( v19->m_hStmtSelect )
                SQLCloseCursor_0(v19->m_hStmtSelect);
              return 1i64;
            }
            ++v17;
          }
        }
        if ( v19->m_hStmtSelect )
          SQLCloseCursor_0(v19->m_hStmtSelect);
        if ( v19->m_bSaveDBLog )
          CRFNewDatabase::FmtLog((CRFNewDatabase *)&v19->vfptr, "%s Success", &DstBuf);
        result = 0i64;
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v19->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
