/*
 * Function: ?create@?$TInventory@U_INVENKEY@@@@QEAA_NHHH@Z
 * Address: 0x1402D4440
 */

char __fastcall TInventory<_INVENKEY>::create(TInventory<_INVENKEY> *this, int nMaxPage, int nMaxSlot, int nMaxOverlap)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v6; // rax@4
  unsigned __int8 v7; // cf@6
  unsigned __int64 v8; // rax@6
  char result; // al@12
  __int64 v10; // [sp+0h] [bp-68h]@1
  int j; // [sp+30h] [bp-38h]@13
  int count[2]; // [sp+38h] [bp-30h]@4
  TInvenPage<_INVENKEY> *v13; // [sp+40h] [bp-28h]@11
  void *v14; // [sp+48h] [bp-20h]@8
  __int64 v15; // [sp+50h] [bp-18h]@4
  TInvenPage<_INVENKEY> *v16; // [sp+58h] [bp-10h]@9
  TInventory<_INVENKEY> *v17; // [sp+70h] [bp+8h]@1

  v17 = this;
  v4 = &v10;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v15 = -2i64;
  v17->m_nMaxPageNum = nMaxPage;
  v17->m_nMaxSlotNum = nMaxSlot;
  v17->m_nMaxOverlapNum = nMaxOverlap;
  *(_QWORD *)count = v17->m_nMaxPageNum;
  v6 = 16i64 * *(_QWORD *)count;
  if ( !is_mul_ok(0x10ui64, *(unsigned __int64 *)count) )
    v6 = -1i64;
  v7 = __CFADD__(v6, 8i64);
  v8 = v6 + 8;
  if ( v7 )
    v8 = -1i64;
  v14 = operator new[](v8);
  if ( v14 )
  {
    *(_DWORD *)v14 = count[0];
    `eh vector constructor iterator'(
      (char *)v14 + 8,
      0x10ui64,
      count[0],
      (void (__cdecl *)(void *))TInvenPage<_INVENKEY>::TInvenPage<_INVENKEY>,
      (void (__cdecl *)(void *))TInvenPage<_INVENKEY>::~TInvenPage<_INVENKEY>);
    v16 = (TInvenPage<_INVENKEY> *)((char *)v14 + 8);
  }
  else
  {
    v16 = 0i64;
  }
  v13 = v16;
  v17->m_pPage = v16;
  if ( v17->m_pPage )
  {
    for ( j = 0; j < v17->m_nMaxPageNum; ++j )
    {
      if ( !TInvenPage<_INVENKEY>::create(&v17->m_pPage[j], v17->m_nMaxSlotNum, v17->m_nMaxOverlapNum) )
        return 0;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
