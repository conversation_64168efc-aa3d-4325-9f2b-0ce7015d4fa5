/*
 * Function: ??0?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@QEAA@XZ
 * Address: 0x14063CCE0
 */

CryptoPP::DL_SymmetricEncryptionAlgorithm *__fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>(CryptoPP::DL_SymmetricEncryptionAlgorithm *a1)
{
  CryptoPP::DL_SymmetricEncryptionAlgorithm *v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  CryptoPP::DL_SymmetricEncryptionAlgorithm::DL_SymmetricEncryptionAlgorithm(a1);
  v2->vfptr = (CryptoPP::DL_SymmetricEncryptionAlgorithmVtbl *)&CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>::`vftable';
  return v2;
}
