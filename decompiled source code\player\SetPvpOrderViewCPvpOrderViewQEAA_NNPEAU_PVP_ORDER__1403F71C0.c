/*
 * Function: ?SetPvpOrderView@CPvpOrderView@@QEAA_NNPEAU_PVP_ORDER_VIEW_DB_BASE@@PEAVCPlayer@@@Z
 * Address: 0x1403F71C0
 */

char __fastcall CPvpOrderView::SetPvpOrderView(CPvpOrderView *this, long double dPvpPoint, _PVP_ORDER_VIEW_DB_BASE *pkInfo, CPlayer *pOne)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@9
  __int64 v7; // [sp-20h] [bp-E8h]@1
  char Dst; // [sp+28h] [bp-A0h]@4
  int v9; // [sp+34h] [bp-94h]@8
  int v10; // [sp+38h] [bp-90h]@7
  int v11; // [sp+3Ch] [bp-8Ch]@6
  tm *v12; // [sp+58h] [bp-70h]@4
  __int64 tUpdateDate; // [sp+68h] [bp-60h]@6
  tm *v14; // [sp+78h] [bp-50h]@6
  char v15; // [sp+80h] [bp-48h]@5
  CPvpOrderView *v16; // [sp+D0h] [bp+8h]@1
  _PVP_ORDER_VIEW_DB_BASE *_Time; // [sp+E0h] [bp+18h]@1

  _Time = pkInfo;
  v16 = this;
  v4 = &v7;
  for ( i = 54i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset_0(&Dst, 0, 0x24ui64);
  v12 = localtime_15(&_Time->tUpdatedate);
  if ( v12 )
  {
    qmemcpy(&v15, v12, 0x24ui64);
    qmemcpy(&Dst, &v15, 0x24ui64);
  }
  tUpdateDate = 0i64;
  time_20(&tUpdateDate);
  v14 = localtime_15(&tUpdateDate);
  v16->m_pkInfo = _Time;
  if ( v11 != v14->tm_year || v10 != v14->tm_mon || v9 != v14->tm_mday )
  {
    v16->m_dPvpCash = _Time->dPvpTempCash + _Time->dPvpCash;
    CPvpOrderView::UpdatePvpCash(v16, v16->m_dPvpCash);
    CPvpOrderView::Update(v16, tUpdateDate, 0, 0, 0.0, dPvpPoint, 0.0);
    CPvpOrderView::Update_RaceWarRecvr(v16, 0);
    CPvpOrderView::ResetPvPOrderView(v16);
    v16->m_dOriginalPvpPoint = _Time->dPvpPoint;
    result = 1;
  }
  else
  {
    v16->m_nKillCnt = _Time->nKill;
    v16->m_nDeahtCnt = _Time->nDeath;
    v16->m_dTodayPvpPoint = _Time->dTodayStacked;
    v16->m_dOriginalPvpPoint = _Time->dPvpPoint;
    *(_QWORD *)&v16->m_dPvpPoint = 0i64;
    v16->m_dPvpTempCash = _Time->dPvpTempCash;
    v16->m_dPvpCash = _Time->dPvpCash;
    result = 0;
  }
  return result;
}
