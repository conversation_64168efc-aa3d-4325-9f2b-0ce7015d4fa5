/*
 * Function: ?VerifyTruncatedDigest@HashTransformation@CryptoPP@@UEAA_NPEBE_K01@Z
 * Address: 0x14044DFE0
 */

int __fastcall CryptoPP::HashTransformation::VerifyTruncatedDigest(CryptoPP::HashTransformation *this, const char *digest, unsigned __int64 digestLength, const char *input, unsigned __int64 length)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v8; // [sp+0h] [bp-28h]@1
  CryptoPP::HashTransformation *v9; // [sp+30h] [bp+8h]@1
  const char *v10; // [sp+38h] [bp+10h]@1
  unsigned __int64 v11; // [sp+40h] [bp+18h]@1

  v11 = digestLength;
  v10 = digest;
  v9 = this;
  v5 = &v8;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  ((void (__fastcall *)(CryptoPP::HashTransformation *, const char *, unsigned __int64))v9->vfptr[1].Clone)(
    v9,
    input,
    length);
  return ((int (__fastcall *)(CryptoPP::HashTransformation *, const char *, unsigned __int64))v9->vfptr[8].__vecDelDtor)(
           v9,
           v10,
           v11);
}
