/*
 * Function: ?Select_UnmannedTraderItemStateInfoCnt@CRFWorldDatabase@@QEAAHPEAK@Z
 * Address: 0x1404AADF0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_UnmannedTraderItemStateInfoCnt(CRFWorldDatabase *this, unsigned int *pdwCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v5; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@21
  SQLLEN v8; // [sp+38h] [bp-150h]@21
  __int16 v9; // [sp+44h] [bp-144h]@9
  char DstBuf; // [sp+60h] [bp-128h]@4
  int v11; // [sp+164h] [bp-24h]@4
  unsigned __int64 v12; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v13; // [sp+190h] [bp+8h]@1
  unsigned int *TargetValue; // [sp+198h] [bp+10h]@1

  TargetValue = pdwCnt;
  v13 = this;
  v2 = &v5;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  v11 = 0;
  sprintf_s(&DstBuf, 0x100ui64, "select count([id]) from [dbo].[tbl_utresultstateid]");
  if ( v13->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v13->vfptr, &DstBuf);
  if ( v13->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v13->vfptr) )
  {
    v9 = SQLExecDirectA_0(v13->m_hStmtSelect, &DstBuf, -3);
    if ( v9 && v9 != 1 )
    {
      if ( v9 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v13->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &DstBuf, "SQLExecDirectA", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v9 = SQLFetch_0(v13->m_hStmtSelect);
      if ( v9 && v9 != 1 )
      {
        if ( v9 != 100 )
        {
          SQLStmt = v13->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v13->vfptr, v9, &DstBuf, "SQLFetch", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v13->vfptr, v9, v13->m_hStmtSelect);
        }
        if ( v13->m_hStmtSelect )
          SQLCloseCursor_0(v13->m_hStmtSelect);
        result = 1i64;
      }
      else
      {
        *TargetValue = 0;
        StrLen_or_IndPtr = &v8;
        SQLStmt = 0i64;
        v9 = SQLGetData_0(v13->m_hStmtSelect, 1u, 4, TargetValue, 0i64, &v8);
        if ( v9 == 100 )
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          result = 2i64;
        }
        else
        {
          if ( v13->m_hStmtSelect )
            SQLCloseCursor_0(v13->m_hStmtSelect);
          if ( v13->m_bSaveDBLog )
            CRFNewDatabase::FmtLog((CRFNewDatabase *)&v13->vfptr, "%s Success", &DstBuf);
          result = 0i64;
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v13->vfptr, "ReConnectDataBase Fail. Query : %s", &DstBuf);
    result = 1i64;
  }
  return result;
}
