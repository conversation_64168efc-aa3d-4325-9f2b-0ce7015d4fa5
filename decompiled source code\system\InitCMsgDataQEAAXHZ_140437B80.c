/*
 * Function: ?Init@CMsgData@@QEAAXH@Z
 * Address: 0x140437B80
 */

void __fastcall CMsgData::Init(CMsgData *this, int nObjNum)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v4; // rax@4
  unsigned __int8 v5; // cf@6
  unsigned __int64 v6; // rax@6
  __int64 v7; // [sp+0h] [bp-68h]@1
  int j; // [sp+30h] [bp-38h]@11
  int count[2]; // [sp+38h] [bp-30h]@4
  _message *v10; // [sp+40h] [bp-28h]@11
  void *v11; // [sp+48h] [bp-20h]@8
  __int64 v12; // [sp+50h] [bp-18h]@4
  _message *v13; // [sp+58h] [bp-10h]@9
  CMsgData *v14; // [sp+70h] [bp+8h]@1

  v14 = this;
  v2 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v14->m_nObjNum = nObjNum;
  v14->m_nMaxBufNum = 100 * nObjNum;
  *(_QWORD *)count = v14->m_nMaxBufNum;
  v4 = 32i64 * *(_QWORD *)count;
  if ( !is_mul_ok(0x20ui64, *(unsigned __int64 *)count) )
    v4 = -1i64;
  v5 = __CFADD__(v4, 8i64);
  v6 = v4 + 8;
  if ( v5 )
    v6 = -1i64;
  v11 = operator new[](v6, THIS_FILE_15, 34);
  if ( v11 )
  {
    *(_DWORD *)v11 = count[0];
    `eh vector constructor iterator'(
      (char *)v11 + 8,
      0x20ui64,
      count[0],
      (void (__cdecl *)(void *))_message::_message,
      (void (__cdecl *)(void *))_message::~_message);
    v13 = (_message *)((char *)v11 + 8);
  }
  else
  {
    v13 = 0i64;
  }
  v10 = v13;
  v14->m_gmBuf = v13;
  v14->m_gmListEmptyHead.pNext = &v14->m_gmListEmptyTail;
  v14->m_gmListEmptyTail.pPrev = &v14->m_gmListEmptyHead;
  v14->m_gmListEmptyHead.pPrev = &v14->m_gmListEmptyHead;
  v14->m_gmListEmptyTail.pNext = &v14->m_gmListEmptyTail;
  for ( j = 0; j < v14->m_nMaxBufNum; ++j )
  {
    v14->m_gmBuf[j].pNext = &v14->m_gmListEmptyTail;
    v14->m_gmBuf[j].pPrev = v14->m_gmListEmptyTail.pPrev;
    v14->m_gmListEmptyTail.pPrev->pNext = &v14->m_gmBuf[j];
    v14->m_gmListEmptyTail.pPrev = &v14->m_gmBuf[j];
  }
  v14->m_gmListHead.pNext = &v14->m_gmListTail;
  v14->m_gmListTail.pPrev = &v14->m_gmListHead;
  v14->m_gmListHead.pPrev = &v14->m_gmListHead;
  v14->m_gmListTail.pNext = &v14->m_gmListTail;
}
