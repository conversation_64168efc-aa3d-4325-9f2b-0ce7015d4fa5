/*
 * Function: ?SendMsg_OreTransferCount@CPlayer@@QEAAXXZ
 * Address: 0x1400E8780
 */

void __fastcall CPlayer::SendMsg_OreTransferCount(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  COreAmountMgr *v3; // rax@4
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CPlayer *v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = COreAmountMgr::Instance();
  szMsg = COreAmountMgr::GetOreTransferCount(v3);
  pbyType = 14;
  v7 = 69;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
}
