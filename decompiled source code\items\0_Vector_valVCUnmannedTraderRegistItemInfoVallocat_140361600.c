/*
 * Function: ??0?$_Vector_val@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAA@V?$allocator@VCUnmannedTraderRegistItemInfo@@@1@@Z
 * Address: 0x140361600
 */

void __fastcall std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, __int64 _Al)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  std::_Vector_val<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v5; // [sp+30h] [bp+8h]@1
  std::allocator<CUnmannedTraderRegistItemInfo> *__formal; // [sp+38h] [bp+10h]@1

  __formal = (std::allocator<CUnmannedTraderRegistItemInfo> *)_Al;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Container_base::_Container_base((std::_Container_base *)&v5->_Myfirstiter);
  std::allocator<CUnmannedTraderRegistItemInfo>::allocator<CUnmannedTraderRegistItemInfo>(&v5->_Alval, __formal);
}
