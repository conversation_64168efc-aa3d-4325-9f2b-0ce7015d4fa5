/*
 * Function: ?CheatCancelRegistSingle@CUnmannedTraderUserInfo@@AEAA_NE@Z
 * Address: 0x14035C100
 */

char __fastcall CUnmannedTraderUserInfo::CheatCancelRegistSingle(CUnmannedTraderUserInfo *this, char byNth)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  unsigned __int64 v5; // rax@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v6; // rax@8
  CUnmannedTraderRegistItemInfo *v7; // rax@14
  CUnmannedTraderRegistItemInfo *v8; // rax@15
  CUnmannedTraderItemState::STATE v9; // eax@15
  CUnmannedTraderRegistItemInfo *v10; // rax@16
  __int64 v11; // [sp+0h] [bp-3D8h]@1
  char Dst; // [sp+50h] [bp-388h]@4
  char v13; // [sp+51h] [bp-387h]@16
  unsigned __int16 v14; // [sp+52h] [bp-386h]@16
  unsigned int v15; // [sp+54h] [bp-384h]@16
  unsigned int v16; // [sp+5Ch] [bp-37Ch]@16
  CPlayer *v17; // [sp+B8h] [bp-320h]@4
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > result; // [sp+C8h] [bp-310h]@6
  char v19; // [sp+E4h] [bp-2F4h]@6
  char v20; // [sp+E5h] [bp-2F3h]@6
  unsigned __int8 j; // [sp+E6h] [bp-2F2h]@6
  char Dest; // [sp+100h] [bp-2D8h]@13
  char v23; // [sp+101h] [bp-2D7h]@13
  char v24; // [sp+220h] [bp-1B8h]@15
  char v25; // [sp+221h] [bp-1B7h]@15
  char v26; // [sp+330h] [bp-A8h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v27; // [sp+348h] [bp-90h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v28; // [sp+350h] [bp-88h]@8
  bool v29; // [sp+368h] [bp-70h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > v30; // [sp+370h] [bp-68h]@8
  char v31; // [sp+388h] [bp-50h]@13
  char v32; // [sp+389h] [bp-4Fh]@15
  char v33; // [sp+38Ah] [bp-4Eh]@16
  __int64 v34; // [sp+390h] [bp-48h]@4
  unsigned __int64 v35; // [sp+398h] [bp-40h]@7
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v36; // [sp+3A0h] [bp-38h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v37; // [sp+3A8h] [bp-30h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *__that; // [sp+3B0h] [bp-28h]@8
  std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v39; // [sp+3B8h] [bp-20h]@8
  std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *_Right; // [sp+3C0h] [bp-18h]@8
  unsigned __int64 v41; // [sp+3C8h] [bp-10h]@4
  CUnmannedTraderUserInfo *v42; // [sp+3E0h] [bp+8h]@1
  char v43; // [sp+3E8h] [bp+10h]@1

  v43 = byNth;
  v42 = this;
  v2 = &v11;
  for ( i = 244i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v34 = -2i64;
  v41 = (unsigned __int64)&v11 ^ _security_cookie;
  memset_0(&Dst, 0, 0x58ui64);
  v17 = CUnmannedTraderUserInfo::FindOwner(v42);
  if ( v17 )
  {
    std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::begin(
      &v42->m_vecRegistItemInfo,
      &result);
    v19 = 0;
    v20 = 0;
    for ( j = 0; ; ++j )
    {
      v35 = j;
      v5 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::size(&v42->m_vecRegistItemInfo);
      if ( v35 >= v5 )
        break;
      v27 = (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v26;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
        (std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v26,
        &result);
      v36 = v6;
      v37 = CUnmannedTraderUserInfo::FindRegist(v42, &v28, v6);
      __that = v37;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator=(
        &result,
        v37);
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v28);
      v39 = std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::end(
              &v42->m_vecRegistItemInfo,
              &v30);
      _Right = (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)v39;
      v29 = std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator==(
              (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&result._Mycont,
              (std::_Vector_const_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *)&v39->_Mycont);
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&v30);
      if ( v29 )
        break;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator++(&result);
      if ( (unsigned __int8)v43 <= (signed int)(unsigned __int8)++v20 )
      {
        v19 = 1;
        break;
      }
    }
    if ( v19 )
    {
      v7 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
      if ( CUnmannedTraderRegistItemInfo::IsRegist(v7) )
      {
        v13 = 1;
        v14 = v42->m_wInx;
        v15 = v42->m_dwUserSerial;
        Dst = 0;
        v10 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
        v16 = CUnmannedTraderRegistItemInfo::GetRegistSerial(v10);
        CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -115, &Dst, 88);
        v33 = 1;
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
        v4 = v33;
      }
      else
      {
        v24 = 0;
        memset(&v25, 0, 0xFEui64);
        v8 = std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::operator->(&result);
        v9 = CUnmannedTraderRegistItemInfo::GetState(v8);
        sprintf(&v24, "(%u)th : %u State(%d) Not Regist Item!", (unsigned __int8)v43, v9);
        CPlayer::SendData_ChatTrans(v17, 0, 0xFFFFFFFF, -1, 0, &v24, -1, 0i64);
        v32 = 0;
        std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
        v4 = v32;
      }
    }
    else
    {
      Dest = 0;
      memset(&v23, 0, 0xFEui64);
      sprintf(&Dest, "Not Exist (%u)th Index %u!", (unsigned __int8)v43);
      CPlayer::SendData_ChatTrans(v17, 0, 0xFFFFFFFF, -1, 0, &Dest, -1, 0i64);
      v31 = 0;
      std::_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::~_Vector_iterator<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(&result);
      v4 = v31;
    }
  }
  else
  {
    v4 = 0;
  }
  return v4;
}
