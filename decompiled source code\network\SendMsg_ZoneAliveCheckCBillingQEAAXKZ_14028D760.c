/*
 * Function: ?SendMsg_ZoneAliveCheck@CBilling@@QEAAXK@Z
 * Address: 0x14028D760
 */

void __fastcall CBilling::SendMsg_ZoneAliveCheck(CBilling *this, unsigned int dwData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4

  v2 = &v4;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  *(_DWORD *)szMsg = dwData;
  pbyType = 29;
  v7 = 91;
  CNetProcess::LoadSendMsg(qword_1414F20A0, 0, &pbyType, szMsg, 4u);
}
