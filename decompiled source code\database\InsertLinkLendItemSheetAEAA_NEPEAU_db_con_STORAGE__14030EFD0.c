/*
 * Function: ?InsertLink@LendItemSheet@@AEAA_NEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x14030EFD0
 */

bool __fastcall LendItemSheet::InsertLink(LendItemSheet *this, char byStorageCode, _STORAGE_LIST::_db_con *pkItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  LendItemSheet::Cell *v5; // rax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  LendItemSheet::Cell v8; // [sp+20h] [bp-28h]@4
  LendItemSheet *v9; // [sp+50h] [bp+8h]@1

  v9 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  LendItemSheet::Cell::Cell(&v8, byStorageCode, pkItem);
  return ListHeap<LendItemSheet::Cell>::push(&v9->_heapFixRow, v5);
}
