/*
 * Function: j_??$unchecked_uninitialized_fill_n@PEAVCUnmannedTraderRegistItemInfo@@_KV1@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@stdext@@YAXPEAVCUnmannedTraderRegistItemInfo@@_KAEBV1@AEAV?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@Z
 * Address: 0x1400054B1
 */

void __fastcall stdext::unchecked_uninitialized_fill_n<CUnmannedTraderRegistItemInfo *,unsigned __int64,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(CUnmannedTraderRegistItemInfo *_First, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val, std::allocator<CUnmannedTraderRegistItemInfo> *_Al)
{
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderRegistItemInfo *,unsigned __int64,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    _First,
    _Count,
    _Val,
    _Al);
}
