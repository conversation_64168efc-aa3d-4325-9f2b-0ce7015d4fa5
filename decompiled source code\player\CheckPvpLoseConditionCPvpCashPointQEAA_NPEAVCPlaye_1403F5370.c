/*
 * Function: ?CheckPvpLoseCondition@CPvpCashPoint@@QEAA_NPEAVCPlayer@@0@Z
 * Address: 0x1403F5370
 */

bool __usercall CPvpCashPoint::CheckPvpLoseCondition@<al>(CPvpCashPoint *this@<rcx>, CPlayer *pKiller@<rdx>, CPlayer *pDier@<r8>, double a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  unsigned int v7; // eax@9
  int v8; // eax@11
  CPvpOrderView *v9; // rax@13
  char v10; // al@13
  double v11; // xmm0_8@13
  __int64 v12; // [sp+0h] [bp-48h]@1
  int v13; // [sp+20h] [bp-28h]@11
  double v14; // [sp+28h] [bp-20h]@13
  bool v15; // [sp+30h] [bp-18h]@13
  CGameObjectVtbl *v16; // [sp+38h] [bp-10h]@13
  CPvpCashPoint *v17; // [sp+50h] [bp+8h]@1
  CPlayer *pOne; // [sp+58h] [bp+10h]@1
  CPlayer *v19; // [sp+60h] [bp+18h]@1

  v19 = pDier;
  pOne = pKiller;
  v17 = this;
  v4 = &v12;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( pKiller && pDier )
  {
    if ( CPvpCashPoint::IsPvpMap(v17, pKiller) )
    {
      v7 = CPlayerDB::GetCharSerial(&pOne->m_Param);
      if ( CIndexList::IsInList(&v17->m_KillerList, v7, 0i64) )
      {
        CPvpCashPoint::SendMsg_PvpCashInform(v17, v19->m_ObjID.m_wIndex, 3);
        result = 0;
      }
      else
      {
        v13 = CPlayerDB::GetRaceCode(&pOne->m_Param);
        v8 = CPlayerDB::GetRaceCode(&v19->m_Param);
        if ( v13 == v8 )
        {
          result = 0;
        }
        else
        {
          v9 = CPlayer::GetPvpOrderView(pOne);
          CPvpOrderView::GetPvpTempCash(v9);
          v14 = a4;
          v15 = CPlayer::IsApplyPcbangPrimium(pOne);
          v16 = pOne->vfptr;
          v10 = ((int (__fastcall *)(CPlayer *))v16->GetLevel)(pOne);
          v11 = (double)CPvpCashPoint::GetMaxTempPoint(&pOne->m_kPvpCashPoint, v10, v15);
          result = v14 < v11;
        }
      }
    }
    else
    {
      CPvpCashPoint::SendMsg_PvpCashInform(v17, v19->m_ObjID.m_wIndex, 9);
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
