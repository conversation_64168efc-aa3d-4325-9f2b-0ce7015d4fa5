/*
 * Function: ?DecodeValue@CCheckSum@@QEAAKEKK@Z
 * Address: 0x1402C0620
 */

__int64 __fastcall CCheckSum::DecodeValue(CCheckSum *this, char byIndex, unsigned int dwSerial, unsigned int dwValue)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  const unsigned int *v7; // [sp+0h] [bp-18h]@1

  v4 = (__int64 *)&v7;
  for ( i = 4i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = check_key_value[(unsigned __int8)byIndex];
  return v7[dwSerial % 0xA] ^ dwValue;
}
