/*
 * Function: ?ct_cashitembuy@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140294590
 */

bool __fastcall ct_cashitembuy(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  CashItemRemoteStore *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-38h]@1
  int nNum; // [sp+20h] [bp-18h]@7
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 && v7->m_bOper )
  {
    nNum = atoi(s_pwszDstCheat[1]);
    if ( nNum >= 0 && nNum <= 99 )
    {
      v4 = CashItemRemoteStore::Instance();
      result = CashItemRemoteStore::CheatBuy(v4, v7->m_ObjID.m_wIndex, s_pwszDstCheat[0], nNum);
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
