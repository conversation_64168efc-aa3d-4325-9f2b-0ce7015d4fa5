/*
 * Function: j_??RCTopKillPrediCate@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAA_NAEBVCNormalGuildBattleGuildMember@2@0@Z
 * Address: 0x14000658C
 */

bool __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate::operator()(GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate *this, GUILD_BATTLE::CNormalGuildBattleGuildMember *lhs, GUILD_BATTLE::CNormalGuildBattleGuildMember *rhs)
{
  return GUILD_BATTLE::CNormalGuildBattleGuild::CTopKillPrediCate::operator()(this, lhs, rhs);
}
