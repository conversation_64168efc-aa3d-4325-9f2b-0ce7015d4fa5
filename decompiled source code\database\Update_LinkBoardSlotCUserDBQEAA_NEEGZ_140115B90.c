/*
 * Function: ?Update_LinkBoardSlot@CUserDB@@QEAA_NEEG@Z
 * Address: 0x140115B90
 */

char __fastcall CUserDB::Update_LinkBoardSlot(CUserDB *this, char bySlot, char byLinkCode, unsigned __int16 wIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-28h]@1
  CUserDB *v8; // [sp+30h] [bp+8h]@1

  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)(unsigned __int8)bySlot < 50 )
  {
    if ( (unsigned __int8)byLinkCode == 255 )
    {
      _LINKKEY::SetRelease((_LINKKEY *)&v8->m_AvatorData.dbLink + (unsigned __int8)bySlot);
    }
    else if ( (signed int)(unsigned __int8)byLinkCode < 7 )
    {
      _LINKKEY::SetData(
        (_LINKKEY *)&v8->m_AvatorData.dbLink + (unsigned __int8)bySlot,
        (unsigned __int8)byLinkCode,
        wIndex);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
