/*
 * Function: ?FindFarChatPlayerWithTemp@CPlayer@@QEAAPEAV1@PEAD@Z
 * Address: 0x140091160
 */

CPlayer *__fastcall CPlayer::FindFarChatPlayerWithTemp(CPlayer *this, char *pwszName)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@13
  char *v5; // rax@22
  char *v6; // rax@37
  __int64 v8; // [sp+0h] [bp-88h]@1
  unsigned __int8 v9; // [sp+20h] [bp-68h]@4
  CPlayer *v10; // [sp+28h] [bp-60h]@4
  int j; // [sp+30h] [bp-58h]@4
  bool *v12; // [sp+38h] [bp-50h]@7
  int k; // [sp+40h] [bp-48h]@17
  CPlayer *v14; // [sp+48h] [bp-40h]@20
  bool *v15; // [sp+50h] [bp-38h]@26
  int l; // [sp+58h] [bp-30h]@26
  unsigned int v17; // [sp+5Ch] [bp-2Ch]@32
  int m; // [sp+60h] [bp-28h]@32
  size_t MaxCount; // [sp+68h] [bp-20h]@13
  char *Str2; // [sp+70h] [bp-18h]@13
  size_t v21; // [sp+78h] [bp-10h]@22
  CPlayer *v22; // [sp+90h] [bp+8h]@1
  const char *Str; // [sp+98h] [bp+10h]@1

  Str = pwszName;
  v22 = this;
  v2 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = strlen_0(pwszName);
  v10 = 0i64;
  for ( j = 0; j < 10; ++j )
  {
    v12 = &v22->m_PastWhiper[j].bMemory;
    if ( *v12 && v12[18] == v9 && !strncmp((const char *)v12 + 1, Str, v9) )
    {
      if ( *((_QWORD *)v12 + 3)
        && *(_BYTE *)(*((_QWORD *)v12 + 3) + 24i64)
        && *(_BYTE *)(*((_QWORD *)v12 + 3) + 42760i64) == v9
        && (MaxCount = v9,
            Str2 = (char *)(v12 + 1),
            v4 = CPlayerDB::GetCharNameW((CPlayerDB *)(*((_QWORD *)v12 + 3) + 1952i64)),
            !strncmp(v4, Str2, MaxCount)) )
      {
        v10 = (CPlayer *)*((_QWORD *)v12 + 3);
      }
      else
      {
        *v12 = 0;
      }
      break;
    }
  }
  if ( !v10 )
  {
    for ( k = 0; k < 2532; ++k )
    {
      v14 = &g_Player + k;
      if ( v14->m_bLive )
      {
        if ( v14->m_Param.m_byNameLen == v9 )
        {
          v21 = v9;
          v5 = CPlayerDB::GetCharNameW(&v14->m_Param);
          if ( !strncmp(v5, Str, v21) )
          {
            v10 = v14;
            break;
          }
        }
      }
    }
    if ( v10 )
    {
      v15 = 0i64;
      for ( l = 0; l < 10; ++l )
      {
        if ( !v22->m_PastWhiper[l].bMemory )
        {
          v15 = &v22->m_PastWhiper[l].bMemory;
          break;
        }
      }
      if ( !v15 )
      {
        v17 = -1;
        for ( m = 0; m < 10; ++m )
        {
          if ( v22->m_PastWhiper[m].dwMemoryTime < v17 )
          {
            v17 = v22->m_PastWhiper[m].dwMemoryTime;
            v15 = &v22->m_PastWhiper[m].bMemory;
          }
        }
      }
      *v15 = 1;
      *((_DWORD *)v15 + 8) = timeGetTime();
      *((_QWORD *)v15 + 3) = v10;
      v6 = CPlayerDB::GetCharNameW(&v10->m_Param);
      strcpy_0((char *)v15 + 1, v6);
      v15[18] = strlen_0((const char *)v15 + 1);
    }
  }
  return v10;
}
