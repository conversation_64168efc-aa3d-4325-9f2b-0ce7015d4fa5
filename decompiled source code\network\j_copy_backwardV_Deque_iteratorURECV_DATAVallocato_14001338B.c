/*
 * Function: j_??$copy_backward@V?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@std@@V12@@std@@YA?AV?$_Deque_iterator@URECV_DATA@@V?$allocator@URECV_DATA@@@std@@$0A@@0@V10@00@Z
 * Address: 0x14001338B
 */

std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *__fastcall std::copy_backward<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *result, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_First, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Last, std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0> *_Dest)
{
  return std::copy_backward<std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>,std::_Deque_iterator<RECV_DATA,std::allocator<RECV_DATA>,0>>(
           result,
           _First,
           _Last,
           _Dest);
}
