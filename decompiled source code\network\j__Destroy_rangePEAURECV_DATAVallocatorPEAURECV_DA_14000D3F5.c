/*
 * Function: j_??$_Destroy_range@PEAURECV_DATA@@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAXPEAPEAURECV_DATA@@0AEAV?$allocator@PEAURECV_DATA@@@0@@Z
 * Address: 0x14000D3F5
 */

void __fastcall std::_Destroy_range<RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, RECV_DATA **_Last, std::allocator<RECV_DATA *> *_Al)
{
  std::_Destroy_range<RECV_DATA *,std::allocator<RECV_DATA *>>(_First, _Last, _Al);
}
