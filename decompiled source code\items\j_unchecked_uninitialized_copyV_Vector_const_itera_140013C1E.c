/*
 * Function: j_??$unchecked_uninitialized_copy@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@2@@stdext@@YAPEAPEAVCUnmannedTraderSubClassInfo@@V?$_Vector_const_iterator@PEAVCUnmannedTraderSubClassInfo@@V?$allocator@PEAVCUnmannedTraderSubClassInfo@@@std@@@std@@0PEAPEAV1@AEAV?$allocator@PEAVCUnmannedTraderSubClassInfo@@@3@@Z
 * Address: 0x140013C1E
 */

CUnmannedTraderSubClassInfo **__fastcall stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_First, std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Last, CUnmannedTraderSubClassInfo **_Dest, std::allocator<CUnmannedTraderSubClassInfo *> *_Al)
{
  return stdext::unchecked_uninitialized_copy<std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>,CUnmannedTraderSubClassInfo * *,std::allocator<CUnmannedTraderSubClassInfo *>>(
           _First,
           _Last,
           _Dest,
           _Al);
}
