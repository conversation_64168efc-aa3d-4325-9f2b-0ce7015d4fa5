/*
 * Function: j_?UpdateScore@CCurrentGuildBattleInfoManager@GUILD_BATTLE@@QEAAXIEK@Z
 * Address: 0x140002022
 */

void __fastcall GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(GUILD_BATTLE::CCurrentGuildBattleInfoManager *this, unsigned int uiMapID, char byColorInx, unsigned int dwScore)
{
  GUILD_BATTLE::CCurrentGuildBattleInfoManager::UpdateScore(this, uiMapID, byColorInx, dwScore);
}
