/*
 * Function: ?SendMsg_TakeAddResult@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1400D6800
 */

void __fastcall CPlayer::SendMsg_TakeAddResult(CPlayer *this, char byErrCode, _STORAGE_LIST::_db_con *pItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@6
  __int64 v6; // [sp+0h] [bp-78h]@1
  _itembox_take_add_result_zocl v7; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@6
  char v9; // [sp+55h] [bp-23h]@6
  CPlayer *v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.sErrorCode = byErrCode;
  if ( !byErrCode )
  {
    v7.wItemSerial = pItem->m_wSerial;
    v7.byAmount = pItem->m_dwDur;
  }
  pbyType = 7;
  v9 = 4;
  v5 = _itembox_take_add_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v7.sErrorCode, v5);
}
