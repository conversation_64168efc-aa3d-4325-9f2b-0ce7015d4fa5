#pragma once

#include <string>
#include <memory>
#include <chrono>
#include <cstdint>
#include <mutex>
#include <atomic>
#include <optional>
#include <functional>
#include <unordered_map>

namespace NexusProtection::Authentication {

    /**
     * @brief RF Cash Item Database Authentication System
     * 
     * Represents a cash item database authentication system that handles
     * regional authentication procedures for cash item verification.
     * Supports both Japanese and Standard billing authentication with
     * SQL stored procedures and comprehensive error handling.
     * 
     * Refactored from decompiled C source to modern C++17/20 standards.
     * 
     * Original files:
     * - CallProc_RFOnlineAuth_JapCRFCashItemDatabaseQEAAHA_1404836A0.c (Japanese Auth)
     * - CallProc_RFOnlineAuthCRFCashItemDatabaseQEAAHAEAU__140482430.c (Standard Auth)
     */

    /**
     * @brief Forward declarations
     */
    class CRFNewDatabase;
    class IDatabaseConnection;
    class ISQLStatement;

    /**
     * @brief Authentication result enumeration
     */
    enum class AuthResult : int32_t {
        SUCCESS = 0,           // Authentication successful
        ERROR = 1,             // General error
        NO_DATA = 2,           // No data found
        CONNECTION_FAILED = 3, // Database connection failed
        INVALID_PARAMETER = 4, // Invalid input parameters
        SQL_ERROR = 5,         // SQL execution error
        TIMEOUT = 6,           // Operation timeout
        UNKNOWN = -1           // Unknown error
    };

    /**
     * @brief Regional authentication type
     */
    enum class AuthRegion : int32_t {
        STANDARD = 0,          // Standard global authentication
        JAPANESE = 1,          // Japanese regional authentication
        KOREAN = 2,            // Korean regional authentication
        CHINESE = 3,           // Chinese regional authentication
        EUROPEAN = 4,          // European regional authentication
        UNKNOWN = -1           // Unknown region
    };

    /**
     * @brief Cash authentication parameters
     */
    struct CashAuthParams {
        std::string accountId;                      // Account identifier
        uint32_t cashAmount{0};                     // Cash amount (output)
        AuthRegion region{AuthRegion::STANDARD};    // Authentication region
        std::chrono::milliseconds timeout{30000};   // Operation timeout
        bool enableLogging{true};                   // Enable database logging
        
        CashAuthParams() = default;
        explicit CashAuthParams(const std::string& accId, AuthRegion authRegion = AuthRegion::STANDARD);
        
        bool IsValid() const;
        std::string ToString() const;
        void Reset();
    };

    /**
     * @brief Authentication result with detailed information
     */
    struct AuthResultInfo {
        AuthResult result{AuthResult::UNKNOWN};
        uint32_t cashAmount{0};
        std::string errorMessage;
        std::string sqlQuery;
        std::chrono::milliseconds executionTime{0};
        std::chrono::system_clock::time_point timestamp;
        
        AuthResultInfo() : timestamp(std::chrono::system_clock::now()) {}
        explicit AuthResultInfo(AuthResult res) : result(res), timestamp(std::chrono::system_clock::now()) {}
        
        bool IsSuccess() const { return result == AuthResult::SUCCESS; }
        bool IsError() const { return result != AuthResult::SUCCESS; }
        std::string ToString() const;
    };

    /**
     * @brief Database connection interface
     */
    class IDatabaseConnection {
    public:
        virtual ~IDatabaseConnection() = default;
        
        virtual bool IsConnected() const = 0;
        virtual bool Reconnect() = 0;
        virtual void Disconnect() = 0;
        virtual std::string GetConnectionString() const = 0;
        virtual std::chrono::system_clock::time_point GetLastActivity() const = 0;
    };

    /**
     * @brief SQL statement interface
     */
    class ISQLStatement {
    public:
        virtual ~ISQLStatement() = default;
        
        virtual bool Prepare(const std::string& query) = 0;
        virtual bool Execute() = 0;
        virtual bool Fetch() = 0;
        virtual bool GetData(int column, uint32_t& value) = 0;
        virtual void CloseCursor() = 0;
        virtual bool IsValid() const = 0;
        virtual std::string GetLastError() const = 0;
    };

    /**
     * @brief Modern SQL statement implementation
     */
    class ModernSQLStatement : public ISQLStatement {
    public:
        ModernSQLStatement();
        ~ModernSQLStatement() override;

        // ISQLStatement implementation
        bool Prepare(const std::string& query) override;
        bool Execute() override;
        bool Fetch() override;
        bool GetData(int column, uint32_t& value) override;
        void CloseCursor() override;
        bool IsValid() const override;
        std::string GetLastError() const override;

        // Additional functionality
        void SetTimeout(std::chrono::milliseconds timeout);
        std::chrono::milliseconds GetExecutionTime() const;

    private:
        struct Impl;
        std::unique_ptr<Impl> m_pImpl;
    };

    /**
     * @brief Database logger interface
     */
    class IDatabaseLogger {
    public:
        virtual ~IDatabaseLogger() = default;
        
        virtual void Log(const std::string& message) = 0;
        virtual void LogFormat(const char* format, ...) = 0;
        virtual void ErrorLog(const std::string& message) = 0;
        virtual void ErrorLogFormat(const char* format, ...) = 0;
        virtual bool IsLoggingEnabled() const = 0;
        virtual void SetLoggingEnabled(bool enabled) = 0;
    };

    /**
     * @brief RF Cash Item Database Authentication Class
     * 
     * Handles cash item authentication for different regions with
     * SQL stored procedures, connection management, and comprehensive error handling.
     */
    class CRFCashItemDatabase {
    public:
        // Constructor and destructor
        CRFCashItemDatabase();
        explicit CRFCashItemDatabase(std::unique_ptr<IDatabaseConnection> connection);
        ~CRFCashItemDatabase();

        // Copy and move semantics
        CRFCashItemDatabase(const CRFCashItemDatabase& other) = delete;
        CRFCashItemDatabase& operator=(const CRFCashItemDatabase& other) = delete;
        CRFCashItemDatabase(CRFCashItemDatabase&& other) noexcept;
        CRFCashItemDatabase& operator=(CRFCashItemDatabase&& other) noexcept;

        // Core authentication methods
        AuthResultInfo CallProc_RFOnlineAuth_Jap(const CashAuthParams& params);
        AuthResultInfo CallProc_RFOnlineAuth(const CashAuthParams& params);
        AuthResultInfo AuthenticateUser(const CashAuthParams& params);

        // Connection management
        bool Initialize(const std::string& connectionString);
        bool IsConnected() const;
        bool Reconnect();
        void Disconnect();
        void Shutdown();

        // Configuration
        void SetDatabaseLogger(std::unique_ptr<IDatabaseLogger> logger);
        IDatabaseLogger* GetDatabaseLogger() const { return m_logger.get(); }
        void SetLoggingEnabled(bool enabled);
        bool IsLoggingEnabled() const;
        void SetDefaultTimeout(std::chrono::milliseconds timeout);
        std::chrono::milliseconds GetDefaultTimeout() const;

        // Statistics
        uint32_t GetTotalAuthAttempts() const;
        uint32_t GetSuccessfulAuths() const;
        uint32_t GetFailedAuths() const;
        double GetSuccessRate() const;
        std::chrono::system_clock::time_point GetLastActivity() const;

        // Regional support
        void SetSupportedRegions(const std::vector<AuthRegion>& regions);
        std::vector<AuthRegion> GetSupportedRegions() const;
        bool IsRegionSupported(AuthRegion region) const;

        // Utility
        std::string ToString() const;
        bool IsInitialized() const { return m_isInitialized; }

        // Static utilities
        static std::string AuthResultToString(AuthResult result);
        static std::string AuthRegionToString(AuthRegion region);
        static AuthRegion StringToAuthRegion(const std::string& regionStr);
        static bool IsValidAccountId(const std::string& accountId);

    private:
        // Core data members
        std::unique_ptr<IDatabaseConnection> m_connection;
        std::unique_ptr<ISQLStatement> m_statement;
        std::unique_ptr<IDatabaseLogger> m_logger;
        
        // Configuration
        std::chrono::milliseconds m_defaultTimeout{30000};
        std::vector<AuthRegion> m_supportedRegions;
        
        // Statistics
        std::atomic<uint32_t> m_totalAuthAttempts{0};
        std::atomic<uint32_t> m_successfulAuths{0};
        std::atomic<uint32_t> m_failedAuths{0};
        std::atomic<std::chrono::system_clock::time_point> m_lastActivity;
        
        // Thread safety
        mutable std::mutex m_connectionMutex;
        mutable std::mutex m_configMutex;
        
        // State
        std::atomic<bool> m_isInitialized{false};
        std::atomic<bool> m_loggingEnabled{true};

        // Private helper methods
        AuthResultInfo ExecuteAuthProcedure(const std::string& procedureName, 
                                           const CashAuthParams& params);
        
        std::string BuildJapaneseQuery(const std::string& accountId) const;
        std::string BuildStandardQuery(const std::string& accountId) const;
        
        bool ValidateParameters(const CashAuthParams& params) const;
        void UpdateStatistics(bool success);
        void LogAuthAttempt(const CashAuthParams& params, const AuthResultInfo& result);
        
        AuthResult HandleSQLError(int16_t sqlResult, const std::string& query, const std::string& operation);
        void CleanupStatement();
        
        // Move helper
        void MoveFrom(CRFCashItemDatabase&& other) noexcept;
    };

    /**
     * @brief Cash Item Database Manager
     * 
     * Manages multiple cash item database instances for different regions.
     */
    class CRFCashItemDatabaseManager {
    public:
        CRFCashItemDatabaseManager();
        ~CRFCashItemDatabaseManager();

        // Database management
        bool RegisterDatabase(AuthRegion region, std::unique_ptr<CRFCashItemDatabase> database);
        CRFCashItemDatabase* GetDatabase(AuthRegion region) const;
        bool RemoveDatabase(AuthRegion region);
        
        // Authentication operations
        AuthResultInfo AuthenticateUser(const CashAuthParams& params);
        AuthResultInfo AuthenticateUserByRegion(AuthRegion region, const CashAuthParams& params);
        
        // Bulk operations
        void SetLoggingEnabled(bool enabled);
        void SetDefaultTimeout(std::chrono::milliseconds timeout);
        bool ReconnectAll();
        void ShutdownAll();
        
        // Statistics
        uint32_t GetTotalDatabases() const;
        uint32_t GetConnectedDatabases() const;
        std::unordered_map<AuthRegion, uint32_t> GetAuthStatsByRegion() const;
        
        // Utility
        std::vector<AuthRegion> GetRegisteredRegions() const;
        std::string GetStatusReport() const;

    private:
        std::unordered_map<AuthRegion, std::unique_ptr<CRFCashItemDatabase>> m_databases;
        mutable std::mutex m_managerMutex;
        std::chrono::milliseconds m_globalTimeout{30000};
        bool m_globalLoggingEnabled{true};
    };

    /**
     * @brief Cash Item Database Factory
     * 
     * Factory class for creating cash item database instances.
     */
    class CRFCashItemDatabaseFactory {
    public:
        // Factory methods
        static std::unique_ptr<CRFCashItemDatabase> CreateDatabase(AuthRegion region);
        static std::unique_ptr<CRFCashItemDatabase> CreateDatabase(const std::string& connectionString, AuthRegion region);
        static std::unique_ptr<CRFCashItemDatabaseManager> CreateManager();
        
        // Predefined configurations
        static std::unique_ptr<CRFCashItemDatabaseManager> CreateStandardManager();
        static std::unique_ptr<CRFCashItemDatabaseManager> CreateTestManager();
        static std::unique_ptr<CRFCashItemDatabaseManager> CreateProductionManager();
    };

} // namespace NexusProtection::Authentication
