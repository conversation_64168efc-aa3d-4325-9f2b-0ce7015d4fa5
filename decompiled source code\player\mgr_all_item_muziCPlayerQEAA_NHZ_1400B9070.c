/*
 * Function: ?mgr_all_item_muzi@CPlayer@@QEAA_NH@Z
 * Address: 0x1400B9070
 */

char __fastcall CPlayer::mgr_all_item_muzi(CPlayer *this, int nLv)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  char v5; // al@9
  __int64 v6; // [sp+0h] [bp-58h]@1
  int j; // [sp+30h] [bp-28h]@7
  _STORAGE_LIST::_db_con *pItem; // [sp+38h] [bp-20h]@11
  unsigned __int8 v9; // [sp+40h] [bp-18h]@13
  unsigned int dwCurBit; // [sp+44h] [bp-14h]@14
  int k; // [sp+48h] [bp-10h]@14
  CPlayer *v12; // [sp+60h] [bp+8h]@1
  int v13; // [sp+68h] [bp+10h]@1

  v13 = nLv;
  v12 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( nLv <= 7 && nLv >= 0 )
  {
    for ( j = 0; ; ++j )
    {
      v5 = CPlayerDB::GetBagNum(&v12->m_Param);
      if ( j >= 20 * (unsigned __int8)v5 )
        break;
      if ( v12->m_Param.m_dbInven.m_pStorageList[j].m_bLoad )
      {
        pItem = &v12->m_Param.m_dbInven.m_pStorageList[j];
        if ( pItem->m_bLoad )
        {
          if ( pItem->m_byTableCode == 6 )
          {
            v9 = GetItemUpgLimSocket(pItem->m_dwLv);
            if ( v9 >= v13 )
            {
              dwCurBit = GetBitAfterSetLimSocket(v9);
              for ( k = 0; k < v13; ++k )
                dwCurBit = GetBitAfterUpgrade(dwCurBit, 0, k);
              CPlayer::Emb_ItemUpgrade(v12, 0, 0, j, dwCurBit);
              CPlayer::SendMsg_DeleteStorageInform(v12, 0, pItem->m_wSerial);
              CPlayer::SendMsg_RewardAddItem(v12, pItem, 0);
            }
          }
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
