/*
 * Function: j_??0?$DL_FixedBasePrecomputation@UECPPoint@CryptoPP@@@CryptoPP@@QEAA@XZ
 * Address: 0x140006E5B
 */

void __fastcall CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>(CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint> *this)
{
  CryptoPP::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>::DL_FixedBasePrecomputation<CryptoPP::ECPPoint>(this);
}
