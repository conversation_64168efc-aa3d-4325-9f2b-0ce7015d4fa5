#pragma once

/**
 * @file CDatabaseConnectionPool.h
 * @brief Modern C++20 Database Connection Pool class definition
 * 
 * This file provides a comprehensive database connection pooling system
 * with thread safety, automatic reconnection, and performance optimization.
 */

#include <memory>
#include <string>
#include <vector>
#include <queue>
#include <unordered_map>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <chrono>
#include <functional>
#include <thread>

// Forward declarations
class CRFNewDatabase;

namespace NexusProtection {
namespace Database {

/**
 * @brief Connection state enumeration
 */
enum class ConnectionState : uint8_t {
    Disconnected = 0,
    Connecting,
    Connected,
    InUse,
    Error,
    Reconnecting
};

/**
 * @brief Connection pool configuration
 */
struct ConnectionPoolConfig {
    std::string odbcName;
    std::string serverIP;
    std::string accountName;
    std::string password;
    uint16_t port{1433};
    
    // Pool settings
    uint32_t minConnections{2};
    uint32_t maxConnections{10};
    uint32_t initialConnections{3};
    
    // Timeout settings
    std::chrono::seconds connectionTimeout{30};
    std::chrono::seconds idleTimeout{300};
    std::chrono::seconds maxLifetime{3600};
    std::chrono::seconds acquireTimeout{10};
    
    // Health check settings
    std::chrono::seconds healthCheckInterval{60};
    std::string healthCheckQuery{"SELECT 1"};
    
    // Retry settings
    uint32_t maxRetries{3};
    std::chrono::seconds retryDelay{5};
    
    // Performance settings
    bool enableConnectionValidation{true};
    bool enableAutoReconnect{true};
    bool enableStatistics{true};
    
    ConnectionPoolConfig() = default;
    
    bool IsValid() const {
        return !odbcName.empty() && !serverIP.empty() && 
               !accountName.empty() && !password.empty() &&
               minConnections > 0 && maxConnections >= minConnections &&
               initialConnections >= minConnections && initialConnections <= maxConnections;
    }
};

/**
 * @brief Database connection wrapper
 */
class DatabaseConnection {
public:
    DatabaseConnection(uint32_t id, const ConnectionPoolConfig& config);
    ~DatabaseConnection();

    // Disable copy constructor and assignment operator
    DatabaseConnection(const DatabaseConnection&) = delete;
    DatabaseConnection& operator=(const DatabaseConnection&) = delete;

    // Enable move constructor and assignment operator
    DatabaseConnection(DatabaseConnection&&) noexcept = default;
    DatabaseConnection& operator=(DatabaseConnection&&) noexcept = default;

    // Connection management
    bool Connect();
    void Disconnect();
    bool Reconnect();
    bool IsConnected() const;
    bool IsHealthy() const;
    bool ValidateConnection();

    // State management
    ConnectionState GetState() const { return m_state; }
    void SetState(ConnectionState state) { m_state = state; }

    // Usage tracking
    void MarkInUse() { 
        m_state = ConnectionState::InUse; 
        m_lastUsed = std::chrono::steady_clock::now();
        m_useCount++;
    }
    
    void MarkAvailable() { 
        m_state = ConnectionState::Connected; 
        m_lastUsed = std::chrono::steady_clock::now();
    }

    // Getters
    uint32_t GetId() const { return m_id; }
    std::shared_ptr<CRFNewDatabase> GetDatabase() const { return m_database; }
    std::chrono::steady_clock::time_point GetCreationTime() const { return m_creationTime; }
    std::chrono::steady_clock::time_point GetLastUsed() const { return m_lastUsed; }
    uint64_t GetUseCount() const { return m_useCount; }
    const std::string& GetLastError() const { return m_lastError; }

    // Lifetime management
    bool IsExpired() const;
    bool IsIdle() const;
    std::chrono::seconds GetAge() const;
    std::chrono::seconds GetIdleTime() const;

private:
    uint32_t m_id;
    ConnectionPoolConfig m_config;
    std::shared_ptr<CRFNewDatabase> m_database;
    std::atomic<ConnectionState> m_state{ConnectionState::Disconnected};
    
    // Timing
    std::chrono::steady_clock::time_point m_creationTime;
    std::chrono::steady_clock::time_point m_lastUsed;
    std::chrono::steady_clock::time_point m_lastHealthCheck;
    
    // Statistics
    std::atomic<uint64_t> m_useCount{0};
    std::atomic<uint32_t> m_reconnectCount{0};
    
    // Error handling
    std::string m_lastError;
    mutable std::mutex m_errorMutex;
    
    void SetLastError(const std::string& error);
};

/**
 * @brief Connection pool statistics
 */
struct ConnectionPoolStats {
    std::atomic<uint32_t> totalConnections{0};
    std::atomic<uint32_t> activeConnections{0};
    std::atomic<uint32_t> idleConnections{0};
    std::atomic<uint32_t> errorConnections{0};
    
    std::atomic<uint64_t> totalAcquires{0};
    std::atomic<uint64_t> successfulAcquires{0};
    std::atomic<uint64_t> failedAcquires{0};
    std::atomic<uint64_t> timeoutAcquires{0};
    
    std::atomic<uint64_t> totalReleases{0};
    std::atomic<uint32_t> reconnections{0};
    std::atomic<uint32_t> healthCheckFailures{0};
    
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point lastActivity;
    
    ConnectionPoolStats() {
        auto now = std::chrono::steady_clock::now();
        startTime = now;
        lastActivity = now;
    }
    
    double GetSuccessRate() const {
        uint64_t total = totalAcquires.load();
        return total > 0 ? (static_cast<double>(successfulAcquires.load()) / total) * 100.0 : 0.0;
    }
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief RAII connection guard for automatic release
 */
class ConnectionGuard {
public:
    ConnectionGuard(std::shared_ptr<DatabaseConnection> connection, 
                   std::function<void(std::shared_ptr<DatabaseConnection>)> releaseFunc);
    ~ConnectionGuard();

    // Disable copy constructor and assignment operator
    ConnectionGuard(const ConnectionGuard&) = delete;
    ConnectionGuard& operator=(const ConnectionGuard&) = delete;

    // Enable move constructor and assignment operator
    ConnectionGuard(ConnectionGuard&&) noexcept;
    ConnectionGuard& operator=(ConnectionGuard&&) noexcept;

    // Access
    std::shared_ptr<DatabaseConnection> Get() const { return m_connection; }
    std::shared_ptr<DatabaseConnection> operator->() const { return m_connection; }
    DatabaseConnection& operator*() const { return *m_connection; }

    // Manual release
    void Release();

private:
    std::shared_ptr<DatabaseConnection> m_connection;
    std::function<void(std::shared_ptr<DatabaseConnection>)> m_releaseFunc;
    bool m_released{false};
};

/**
 * @brief Modern C++20 Database Connection Pool class
 * 
 * This class provides a comprehensive database connection pooling system
 * with thread safety, automatic reconnection, and performance optimization.
 */
class CDatabaseConnectionPool {
public:
    // Constructor and Destructor
    explicit CDatabaseConnectionPool(const ConnectionPoolConfig& config);
    virtual ~CDatabaseConnectionPool();

    // Disable copy constructor and assignment operator
    CDatabaseConnectionPool(const CDatabaseConnectionPool&) = delete;
    CDatabaseConnectionPool& operator=(const CDatabaseConnectionPool&) = delete;

    // Enable move constructor and assignment operator
    CDatabaseConnectionPool(CDatabaseConnectionPool&&) noexcept = default;
    CDatabaseConnectionPool& operator=(CDatabaseConnectionPool&&) noexcept = default;

    /**
     * @brief Initialize connection pool
     * 
     * Creates initial connections and starts background maintenance.
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown connection pool
     * 
     * Closes all connections and stops background maintenance.
     */
    void Shutdown();

    /**
     * @brief Acquire connection from pool
     * 
     * Gets an available connection from the pool with timeout.
     * 
     * @param timeout Maximum time to wait for connection
     * @return Connection guard or nullptr if failed
     */
    std::unique_ptr<ConnectionGuard> AcquireConnection(
        std::chrono::seconds timeout = std::chrono::seconds(10));

    /**
     * @brief Release connection back to pool
     * 
     * Returns a connection to the available pool.
     * 
     * @param connection Connection to release
     */
    void ReleaseConnection(std::shared_ptr<DatabaseConnection> connection);

    /**
     * @brief Get pool statistics
     * 
     * @return Current pool statistics
     */
    const ConnectionPoolStats& GetStats() const { return m_stats; }

    /**
     * @brief Get pool configuration
     * 
     * @return Current pool configuration
     */
    const ConnectionPoolConfig& GetConfig() const { return m_config; }

    /**
     * @brief Check if pool is healthy
     * 
     * @return true if pool has healthy connections available
     */
    bool IsHealthy() const;

    /**
     * @brief Get pool status report
     * 
     * @return Detailed status report string
     */
    std::string GetStatusReport() const;

    /**
     * @brief Force health check on all connections
     */
    void ForceHealthCheck();

    /**
     * @brief Resize pool
     * 
     * @param newSize New target pool size
     * @return true if resize successful, false otherwise
     */
    bool ResizePool(uint32_t newSize);

protected:
    // Configuration
    ConnectionPoolConfig m_config;
    
    // Connection management
    std::vector<std::shared_ptr<DatabaseConnection>> m_allConnections;
    std::queue<std::shared_ptr<DatabaseConnection>> m_availableConnections;
    std::unordered_map<uint32_t, std::shared_ptr<DatabaseConnection>> m_connectionMap;
    
    // Statistics
    ConnectionPoolStats m_stats;
    
    // Synchronization
    mutable std::mutex m_poolMutex;
    std::condition_variable m_connectionAvailable;
    
    // Background maintenance
    std::unique_ptr<std::thread> m_maintenanceThread;
    std::atomic<bool> m_shutdownRequested{false};
    std::atomic<bool> m_isInitialized{false};
    
    // Connection ID generation
    std::atomic<uint32_t> m_nextConnectionId{1};

private:
    /**
     * @brief Create new database connection
     * 
     * @return New connection or nullptr if failed
     */
    std::shared_ptr<DatabaseConnection> CreateConnection();

    /**
     * @brief Remove connection from pool
     * 
     * @param connection Connection to remove
     */
    void RemoveConnection(std::shared_ptr<DatabaseConnection> connection);

    /**
     * @brief Background maintenance thread function
     */
    void MaintenanceThreadFunc();

    /**
     * @brief Perform health checks on connections
     */
    void PerformHealthChecks();

    /**
     * @brief Remove expired and idle connections
     */
    void CleanupConnections();

    /**
     * @brief Ensure minimum connections are available
     */
    void EnsureMinimumConnections();

    /**
     * @brief Update pool statistics
     */
    void UpdateStatistics();

    /**
     * @brief Validate pool configuration
     * 
     * @param config Configuration to validate
     * @return true if valid, false otherwise
     */
    bool ValidateConfig(const ConnectionPoolConfig& config) const;
};

/**
 * @brief Database Connection Pool Factory
 */
class CDatabaseConnectionPoolFactory {
public:
    /**
     * @brief Create standard connection pool
     * 
     * @param config Pool configuration
     * @return Unique pointer to connection pool
     */
    static std::unique_ptr<CDatabaseConnectionPool> CreateConnectionPool(
        const ConnectionPoolConfig& config);

    /**
     * @brief Create connection pool with default configuration
     *
     * @param odbcName ODBC data source name
     * @param serverIP Database server IP
     * @param accountName Database account name
     * @param password Database password
     * @return Unique pointer to connection pool
     */
    static std::unique_ptr<CDatabaseConnectionPool> CreateDefaultConnectionPool(
        const std::string& odbcName, const std::string& serverIP,
        const std::string& accountName, const std::string& password);
};

/**
 * @brief Connection pool manager for multiple pools
 */
class CDatabaseConnectionPoolManager {
public:
    static CDatabaseConnectionPoolManager& Instance();

    bool RegisterPool(const std::string& name, std::unique_ptr<CDatabaseConnectionPool> pool);
    CDatabaseConnectionPool* GetPool(const std::string& name);
    void RemovePool(const std::string& name);
    void ShutdownAll();

    std::vector<std::string> GetPoolNames() const;
    std::string GetGlobalStatusReport() const;

private:
    std::unordered_map<std::string, std::unique_ptr<CDatabaseConnectionPool>> m_pools;
    mutable std::mutex m_poolsMutex;
    static std::unique_ptr<CDatabaseConnectionPoolManager> s_instance;
    static std::mutex s_instanceMutex;
};

} // namespace Database
} // namespace NexusProtection
