/*
 * Function: ?Update_QuestInsert@CUserDB@@QEAA_NEPEAU_LIST@_QUEST_DB_BASE@@@Z
 * Address: 0x140115530
 */

char __fastcall CUserDB::Update_QuestInsert(CUserDB *this, char bySlotIndex, _QUEST_DB_BASE::_LIST *pSlotData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-28h]@1
  CUserDB *v7; // [sp+30h] [bp+8h]@1

  v7 = this;
  v3 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( (signed int)(unsigned __int8)bySlotIndex < 30 )
  {
    if ( v7->m_AvatorData.dbQuest.m_List[(unsigned __int8)bySlotIndex].byQuestType == 255 )
    {
      memcpy_0((char *)&v7->m_AvatorData.dbQuest + 13 * (unsigned __int8)bySlotIndex, pSlotData, 0xDui64);
      v7->m_bDataUpdate = 1;
      result = 1;
    }
    else
    {
      CLogFile::Write(
        &stru_1799C8E78,
        "%s : Update_QuestInsert(EXIST) : slot : %d",
        v7->m_aszAvatorName,
        (unsigned __int8)bySlotIndex);
      result = 0;
    }
  }
  else
  {
    CLogFile::Write(
      &stru_1799C8E78,
      "%s : Update_QuestInsert(SlotIndex OVER) : slot : %d",
      v7->m_aszAvatorName,
      (unsigned __int8)bySlotIndex);
    result = 0;
  }
  return result;
}
