/*
 * Function: ?GetMaxRegistCnt@CUnmannedTraderUserInfoTable@@QEAAEGK@Z
 * Address: 0x140365830
 */

char __fastcall CUnmannedTraderUserInfoTable::GetMaxRegistCnt(CUnmannedTraderUserInfoTable *this, unsigned __int16 wInx, unsigned int dwSerial)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CUnmannedTraderUserInfo *v6; // rax@7
  CUnmannedTraderUserInfo *v7; // rax@9
  CUnmannedTraderUserInfo *v8; // rax@11
  __int64 v9; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfoTable *v10; // [sp+30h] [bp+8h]@1
  unsigned __int16 v11; // [sp+38h] [bp+10h]@1
  unsigned int v12; // [sp+40h] [bp+18h]@1

  v12 = dwSerial;
  v11 = wInx;
  v10 = this;
  v3 = &v9;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::empty(&v10->m_veckInfo)
    || std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::size(&v10->m_veckInfo) <= v11 )
  {
    result = 0;
  }
  else
  {
    v6 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](&v10->m_veckInfo, v11);
    if ( CUnmannedTraderUserInfo::IsLogInState(v6) )
    {
      v7 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](
             &v10->m_veckInfo,
             v11);
      if ( CUnmannedTraderUserInfo::GetSerial(v7) == v12 )
      {
        v8 = std::vector<CUnmannedTraderUserInfo,std::allocator<CUnmannedTraderUserInfo>>::operator[](
               &v10->m_veckInfo,
               v11);
        result = CUnmannedTraderUserInfo::GetMaxRegistCnt(v8);
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  return result;
}
