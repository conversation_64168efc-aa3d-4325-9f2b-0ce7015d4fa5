/*
 * Function: j_??1?$vector@PEAVCMoveMapLimitRight@@V?$allocator@PEAVCMoveMapLimitRight@@@std@@@std@@QEAA@XZ
 * Address: 0x14000C478
 */

void __fastcall std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *> > *this)
{
  std::vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>::~vector<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>>(this);
}
