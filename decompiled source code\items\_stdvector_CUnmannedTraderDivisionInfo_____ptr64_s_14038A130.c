/*
 * Function: _std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x14038A130
 */

void __fastcall __noreturn std::vector_CUnmannedTraderDivisionInfo_____ptr64_std::allocator_CUnmannedTraderDivisionInfo_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *>>::_Destroy(
    *(std::vector<CUnmannedTraderDivisionInfo *,std::allocator<CUnmannedTraderDivisionInfo *> > **)(a2 + 160),
    *(CUnmannedTraderDivisionInfo ***)(a2 + 64),
    *(CUnmannedTraderDivisionInfo ***)(a2 + 72));
  std::allocator<CUnmannedTraderDivisionInfo *>::deallocate(
    (std::allocator<CUnmannedTraderDivisionInfo *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(CUnmannedTraderDivisionInfo ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
