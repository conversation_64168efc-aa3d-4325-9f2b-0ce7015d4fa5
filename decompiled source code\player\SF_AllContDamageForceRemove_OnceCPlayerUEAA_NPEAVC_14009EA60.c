/*
 * Function: ?SF_AllContDamageForceRemove_Once@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x14009EA60
 */

bool __fastcall CPlayer::SF_AllContDamageForceRemove_Once(CPlayer *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-58h]@1
  int v6; // [sp+30h] [bp-28h]@4
  int j; // [sp+34h] [bp-24h]@4
  _sf_continous (*v8)[8]; // [sp+38h] [bp-20h]@7
  _base_fld *v9; // [sp+40h] [bp-18h]@9
  CCharacter *v10; // [sp+48h] [bp-10h]@10
  CCharacter *v11; // [sp+68h] [bp+10h]@1

  v11 = pDstObj;
  v2 = &v5;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v6 = 0;
  for ( j = 0; j < 8; ++j )
  {
    v8 = (_sf_continous (*)[8])((char *)v11->m_SFCont + 48 * j);
    if ( v8 )
    {
      if ( (*v8)[0].m_byEffectCode == 1 )
      {
        v9 = CRecordData::GetRecord(&stru_1799C8410 + 3, "17");
        if ( !v9
          || (v10 = v11, !HIBYTE(v11[25].m_SFContAura[0][5].m_wDurSec))
          || (*v8)[0].m_byEffectCode != 3
          || (*v8)[0].m_wEffectIndex != v9->m_dwIndex )
        {
          CCharacter::RemoveSFContEffect(v11, 0, j, 0, 0);
          ++v6;
        }
      }
    }
  }
  return v6 > 0;
}
