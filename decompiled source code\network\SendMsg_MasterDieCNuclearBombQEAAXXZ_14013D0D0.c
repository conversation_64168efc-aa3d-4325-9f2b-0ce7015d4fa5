/*
 * Function: ?SendMsg_MasterDie@CNuclearBomb@@QEAAXXZ
 * Address: 0x14013D0D0
 */

void __fastcall CNuclearBomb::SendMsg_MasterDie(CNuclearBomb *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v3; // ax@15
  __int64 v4; // [sp+0h] [bp-88h]@1
  char v5; // [sp+30h] [bp-58h]@4
  unsigned int dwClientIndex; // [sp+34h] [bp-54h]@4
  CPlayer *v7; // [sp+38h] [bp-50h]@7
  _nuclear_bomb_destruction_zocl v8; // [sp+44h] [bp-44h]@15
  char pbyType; // [sp+64h] [bp-24h]@15
  char v10; // [sp+65h] [bp-23h]@15
  CNuclearBomb *v11; // [sp+90h] [bp+8h]@1

  v11 = this;
  v1 = &v4;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = CPlayerDB::GetRaceCode(&v11->m_pMaster->m_Param);
  for ( dwClientIndex = 0; (signed int)dwClientIndex < 2532; ++dwClientIndex )
  {
    v7 = &g_Player + (signed int)dwClientIndex;
    if ( v7->m_bOper
      && !v7->m_bCorpse
      && v7->m_bLive
      && !strcmp_0(v7->m_pCurMap->m_pMapSet->m_strCode, "resources")
      && v11->m_byBombState >= 1
      && v11->m_byBombState < 5 )
    {
      _nuclear_bomb_destruction_zocl::_nuclear_bomb_destruction_zocl(&v8);
      v8.byRaceCode = v5;
      v8.byUseClass = CNuclearBomb::GetMasterClass(v11);
      pbyType = 60;
      v10 = 9;
      v3 = _nuclear_bomb_destruction_zocl::size(&v8);
      CNetProcess::LoadSendMsg(unk_1414F2088, dwClientIndex, &pbyType, &v8.byRaceCode, v3);
    }
  }
}
