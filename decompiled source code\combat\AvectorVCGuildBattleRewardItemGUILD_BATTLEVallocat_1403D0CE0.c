/*
 * Function: ??A?$vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@QEAAAEAVCGuildBattleRewardItem@GUILD_BATTLE@@_K@Z
 * Address: 0x1403D0CE0
 */

GUILD_BATTLE::CGuildBattleRewardItem *__fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>>::operator[](std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> > *this, unsigned __int64 _Pos)
{
  return &this->_Myfirst[_Pos];
}
