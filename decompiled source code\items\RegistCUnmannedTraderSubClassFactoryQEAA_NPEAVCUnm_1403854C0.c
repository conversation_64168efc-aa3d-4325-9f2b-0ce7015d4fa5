/*
 * Function: ?Regist@CUnmannedTraderSubClassFactory@@QEAA_NPEAVCUnmannedTraderSubClassInfo@@@Z
 * Address: 0x1403854C0
 */

char __fastcall CUnmannedTraderSubClassFactory::Regist(CUnmannedTraderSubClassFactory *this, CUnmannedTraderSubClassInfo *pkType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@5
  CUnmannedTraderSubClassInfo **v5; // rax@11
  const char *v6; // rax@11
  __int64 v7; // [sp+0h] [bp-A8h]@1
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > result; // [sp+28h] [bp-80h]@7
  bool v9; // [sp+44h] [bp-64h]@9
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > v10; // [sp+48h] [bp-60h]@9
  CUnmannedTraderSubClassInfo *v11; // [sp+60h] [bp-48h]@12
  CUnmannedTraderSubClassInfo *v12; // [sp+68h] [bp-40h]@12
  char v13; // [sp+70h] [bp-38h]@15
  __int64 v14; // [sp+78h] [bp-30h]@4
  std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *v15; // [sp+80h] [bp-28h]@9
  std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *_Right; // [sp+88h] [bp-20h]@9
  char *Str2; // [sp+90h] [bp-18h]@11
  void *v18; // [sp+98h] [bp-10h]@13
  CUnmannedTraderSubClassFactory *v19; // [sp+B0h] [bp+8h]@1
  CUnmannedTraderSubClassInfo *_Val; // [sp+B8h] [bp+10h]@1

  _Val = pkType;
  v19 = this;
  v2 = &v7;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = -2i64;
  if ( pkType )
  {
    if ( std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::empty(&v19->m_vecTable) )
    {
LABEL_18:
      std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::push_back(
        &v19->m_vecTable,
        &_Val);
      v4 = 1;
    }
    else
    {
      std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::begin(
        &v19->m_vecTable,
        &result);
      while ( 1 )
      {
        v15 = std::vector<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::end(
                &v19->m_vecTable,
                &v10);
        _Right = (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)v15;
        v9 = std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator!=(
               (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&result._Mycont,
               (std::_Vector_const_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *> > *)&v15->_Mycont);
        std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&v10);
        if ( !v9 )
        {
          std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
          goto LABEL_18;
        }
        if ( *std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator*(&result) )
        {
          Str2 = (char *)CUnmannedTraderSubClassInfo::GetTypeName(_Val);
          v5 = std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator*(&result);
          v6 = CUnmannedTraderSubClassInfo::GetTypeName(*v5);
          if ( !strcmp_0(v6, Str2) )
            break;
        }
        std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::operator++(&result);
      }
      v12 = _Val;
      v11 = _Val;
      if ( _Val )
        v18 = CUnmannedTraderSubClassInfo::`scalar deleting destructor'(v11, 1u);
      else
        v18 = 0i64;
      v13 = 0;
      std::_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>::~_Vector_iterator<CUnmannedTraderSubClassInfo *,std::allocator<CUnmannedTraderSubClassInfo *>>(&result);
      v4 = v13;
    }
  }
  else
  {
    v4 = 0;
  }
  return v4;
}
