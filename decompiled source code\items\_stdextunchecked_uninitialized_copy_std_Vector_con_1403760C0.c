/*
 * Function: _stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____CUnmannedTraderClassInfo_____ptr64_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____::_1_::dtor$2
 * Address: 0x1403760C0
 */

void __fastcall stdext::unchecked_uninitialized_copy_std::_Vector_const_iterator_CUnmannedTraderClassInfo_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____CUnmannedTraderClassInfo_____ptr64_____ptr64_std::allocator_CUnmannedTraderClassInfo_____ptr64____::_1_::dtor_2(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>::~_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>(*(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > **)(a2 + 88));
}
