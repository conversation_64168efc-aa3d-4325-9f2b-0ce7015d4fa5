/*
 * Function: ?Get24TimeFromTickTime@C24Timer@@QEAAKK@Z
 * Address: 0x140284D30
 */

__int64 __fastcall C24Timer::Get24TimeFromTickTime(C24Timer *this, unsigned int dwTickTime)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v5; // [sp+0h] [bp-18h]@1
  C24Timer *v6; // [sp+20h] [bp+8h]@1

  v6 = this;
  v2 = (int *)&v5;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  v5 = (dwTickTime - v6->m_dwBaseTickTime) / 0xEA60;
  return v5 + v6->m_dwBase24Time;
}
