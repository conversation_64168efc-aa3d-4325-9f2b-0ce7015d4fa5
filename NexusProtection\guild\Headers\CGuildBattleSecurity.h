#pragma once

/**
 * @file CGuildBattleSecurity.h
 * @brief Modern C++20 Guild Battle Security System
 * 
 * This file provides comprehensive guild battle security including anti-cheat
 * measures, validation, and fair play enforcement.
 * 
 * Refactored from decompiled sources:
 * - CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjectP_14016B570.c
 * - ct_check_guild_batlle_goalYA_NPEAVCPlayerZ_140293470.c
 * - CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4910.c
 * - Various speed hack detection and validation systems
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <optional>
#include <array>

// Forward declarations
class CPlayer;
class CGameObject;
class CAttack;

namespace NexusProtection {
namespace Guild {

/**
 * @brief Security threat level enumeration
 */
enum class SecurityThreatLevel : uint8_t {
    None = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
};

/**
 * @brief Security violation type enumeration
 */
enum class SecurityViolationType : uint8_t {
    None = 0,
    SpeedHack = 1,
    PositionHack = 2,
    DamageHack = 3,
    SkillHack = 4,
    ItemHack = 5,
    GuildBattleExploit = 6,
    UnauthorizedAccess = 7,
    InvalidBattleState = 8,
    SuspiciousActivity = 9,
    ClientModification = 10
};

/**
 * @brief Security action enumeration
 */
enum class SecurityAction : uint8_t {
    None = 0,
    Log = 1,
    Warn = 2,
    Kick = 3,
    TempBan = 4,
    PermBan = 5,
    Alert = 6
};

/**
 * @brief Guild battle security violation
 */
struct GuildBattleSecurityViolation {
    uint32_t violationId{0};
    uint32_t playerId{0};
    uint32_t guildId{0};
    uint32_t battleId{0};
    SecurityViolationType type{SecurityViolationType::None};
    SecurityThreatLevel level{SecurityThreatLevel::None};
    std::string description;
    std::chrono::system_clock::time_point timestamp;
    std::string playerName;
    std::string guildName;
    std::vector<uint8_t> evidenceData;
    
    GuildBattleSecurityViolation() : timestamp(std::chrono::system_clock::now()) {}
    GuildBattleSecurityViolation(uint32_t player, uint32_t guild, uint32_t battle, 
                                SecurityViolationType vType, const std::string& desc)
        : playerId(player), guildId(guild), battleId(battle), type(vType), description(desc)
        , timestamp(std::chrono::system_clock::now()) {}
};

/**
 * @brief Player security profile
 */
struct PlayerSecurityProfile {
    uint32_t playerId{0};
    uint32_t guildId{0};
    std::string playerName;
    std::chrono::system_clock::time_point firstSeen;
    std::chrono::system_clock::time_point lastActivity;
    
    // Violation tracking
    std::unordered_map<SecurityViolationType, uint32_t> violationCounts;
    std::vector<GuildBattleSecurityViolation> recentViolations;
    SecurityThreatLevel currentThreatLevel{SecurityThreatLevel::None};
    
    // Battle behavior tracking
    std::chrono::steady_clock::time_point lastPositionUpdate;
    std::chrono::steady_clock::time_point lastSkillUse;
    std::chrono::steady_clock::time_point lastDamageDealt;
    
    // Speed hack detection
    std::array<uint32_t, 4> speedHackKey{};
    std::chrono::steady_clock::time_point lastSpeedHackChallenge;
    std::chrono::steady_clock::time_point lastSpeedHackResponse;
    uint32_t speedHackViolations{0};
    
    // Position tracking
    struct Position { float x, y, z; } lastPosition{0.0f, 0.0f, 0.0f};
    std::vector<Position> positionHistory;
    double averageSpeed{0.0};
    
    // Damage tracking
    uint32_t totalDamageDealt{0};
    uint32_t totalDamageReceived{0};
    uint32_t suspiciousDamageEvents{0};
    
    PlayerSecurityProfile() {
        auto now = std::chrono::system_clock::now();
        firstSeen = now;
        lastActivity = now;
        lastPositionUpdate = std::chrono::steady_clock::now();
        lastSkillUse = std::chrono::steady_clock::now();
        lastDamageDealt = std::chrono::steady_clock::now();
    }
    
    void AddViolation(const GuildBattleSecurityViolation& violation);
    SecurityThreatLevel CalculateThreatLevel() const;
    bool IsSuspicious() const;
};

/**
 * @brief Security configuration
 */
struct SecurityConfig {
    // Speed hack detection
    bool enableSpeedHackDetection{true};
    std::chrono::milliseconds speedHackChallengeInterval{5000};
    std::chrono::milliseconds maxResponseTime{5500};
    std::chrono::milliseconds minResponseTime{4500};
    uint32_t maxSpeedHackViolations{3};
    
    // Position validation
    bool enablePositionValidation{true};
    double maxMovementSpeed{15.0}; // units per second
    double maxTeleportDistance{50.0}; // units
    uint32_t positionHistorySize{10};
    
    // Damage validation
    bool enableDamageValidation{true};
    double maxDamageMultiplier{2.0};
    uint32_t maxDamagePerSecond{10000};
    uint32_t suspiciousDamageThreshold{5};
    
    // Battle validation
    bool enableBattleValidation{true};
    bool allowFriendlyFire{false};
    uint32_t maxSkillsPerSecond{5};
    std::chrono::seconds battleTimeoutCheck{30};
    
    // Actions
    SecurityAction defaultAction{SecurityAction::Log};
    std::unordered_map<SecurityViolationType, SecurityAction> violationActions;
    
    SecurityConfig() {
        // Initialize default actions
        violationActions[SecurityViolationType::SpeedHack] = SecurityAction::TempBan;
        violationActions[SecurityViolationType::PositionHack] = SecurityAction::Kick;
        violationActions[SecurityViolationType::DamageHack] = SecurityAction::TempBan;
        violationActions[SecurityViolationType::GuildBattleExploit] = SecurityAction::PermBan;
    }
};

/**
 * @brief Security statistics
 */
struct SecurityStatistics {
    std::atomic<uint64_t> totalViolations{0};
    std::atomic<uint64_t> speedHackDetections{0};
    std::atomic<uint64_t> positionHackDetections{0};
    std::atomic<uint64_t> damageHackDetections{0};
    std::atomic<uint64_t> playersKicked{0};
    std::atomic<uint64_t> playersBanned{0};
    std::atomic<uint32_t> activeMonitoredPlayers{0};
    std::chrono::steady_clock::time_point startTime;
    
    SecurityStatistics() : startTime(std::chrono::steady_clock::now()) {}
    
    std::chrono::seconds GetUptime() const {
        return std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime);
    }
};

/**
 * @brief Security event callback function type
 */
using SecurityEventCallback = std::function<void(const GuildBattleSecurityViolation&)>;

/**
 * @brief Modern C++20 Guild Battle Security class
 * 
 * This class provides comprehensive guild battle security including anti-cheat
 * measures, validation, and fair play enforcement.
 */
class CGuildBattleSecurity {
public:
    // Constructor and Destructor
    CGuildBattleSecurity();
    explicit CGuildBattleSecurity(const SecurityConfig& config);
    virtual ~CGuildBattleSecurity();

    // Disable copy constructor and assignment operator
    CGuildBattleSecurity(const CGuildBattleSecurity&) = delete;
    CGuildBattleSecurity& operator=(const CGuildBattleSecurity&) = delete;

    // Enable move constructor and assignment operator
    CGuildBattleSecurity(CGuildBattleSecurity&&) noexcept = default;
    CGuildBattleSecurity& operator=(CGuildBattleSecurity&&) noexcept = default;

    /**
     * @brief Initialize security system
     * 
     * @return true if initialization successful, false otherwise
     */
    bool Initialize();

    /**
     * @brief Shutdown security system
     */
    void Shutdown();

    /**
     * @brief Check guild battle limit
     * 
     * Modern implementation of CAttack::CheckGuildBattleLimit method.
     * 
     * @param attacker Attacking object
     * @param target Target object
     * @param inGuildBattle Output parameter for guild battle status
     * @return true if attack is allowed, false otherwise
     */
    bool CheckGuildBattleLimit(CGameObject* attacker, CGameObject* target, bool* inGuildBattle);

    /**
     * @brief Check guild battle goal
     * 
     * Modern implementation of ct_check_guild_batlle_goal function.
     * 
     * @param player Player to check
     * @param portalIndex Portal index
     * @return true if goal check passed, false otherwise
     */
    bool CheckGuildBattleGoal(CPlayer* player, int portalIndex);

    /**
     * @brief Add player to monitoring
     * 
     * @param playerId Player ID
     * @param guildId Guild ID
     * @param playerName Player name
     * @return true if added successfully, false otherwise
     */
    bool AddPlayerMonitoring(uint32_t playerId, uint32_t guildId, const std::string& playerName);

    /**
     * @brief Remove player from monitoring
     * 
     * @param playerId Player ID
     * @return true if removed successfully, false otherwise
     */
    bool RemovePlayerMonitoring(uint32_t playerId);

    /**
     * @brief Validate player position
     * 
     * @param playerId Player ID
     * @param x X coordinate
     * @param y Y coordinate
     * @param z Z coordinate
     * @return true if position is valid, false otherwise
     */
    bool ValidatePlayerPosition(uint32_t playerId, float x, float y, float z);

    /**
     * @brief Validate damage dealt
     * 
     * @param attackerId Attacker ID
     * @param targetId Target ID
     * @param damage Damage amount
     * @param skillId Skill ID used
     * @return true if damage is valid, false otherwise
     */
    bool ValidateDamage(uint32_t attackerId, uint32_t targetId, uint32_t damage, uint32_t skillId);

    /**
     * @brief Process speed hack challenge
     * 
     * @param playerId Player ID
     * @param challengeData Challenge data
     * @return true if challenge processed successfully, false otherwise
     */
    bool ProcessSpeedHackChallenge(uint32_t playerId, const std::array<uint32_t, 4>& challengeData);

    /**
     * @brief Process speed hack response
     * 
     * @param playerId Player ID
     * @param responseData Response data
     * @param responseTime Response time in milliseconds
     * @return true if response is valid, false otherwise
     */
    bool ProcessSpeedHackResponse(uint32_t playerId, const std::array<uint32_t, 4>& responseData, 
                                 uint32_t responseTime);

    /**
     * @brief Validate skill usage
     * 
     * @param playerId Player ID
     * @param skillId Skill ID
     * @param targetId Target ID
     * @return true if skill usage is valid, false otherwise
     */
    bool ValidateSkillUsage(uint32_t playerId, uint32_t skillId, uint32_t targetId);

    /**
     * @brief Report security violation
     * 
     * @param violation Security violation details
     * @return Security action to take
     */
    SecurityAction ReportViolation(const GuildBattleSecurityViolation& violation);

    /**
     * @brief Get player security profile
     * 
     * @param playerId Player ID
     * @return Player security profile or nullptr if not found
     */
    std::shared_ptr<PlayerSecurityProfile> GetPlayerProfile(uint32_t playerId) const;

    /**
     * @brief Check if player is banned
     * 
     * @param playerId Player ID
     * @return true if player is banned, false otherwise
     */
    bool IsPlayerBanned(uint32_t playerId) const;

    /**
     * @brief Ban player
     * 
     * @param playerId Player ID
     * @param reason Ban reason
     * @param duration Ban duration (0 for permanent)
     * @return true if ban applied successfully, false otherwise
     */
    bool BanPlayer(uint32_t playerId, const std::string& reason, 
                  std::chrono::seconds duration = std::chrono::seconds(0));

    /**
     * @brief Kick player from battle
     * 
     * @param playerId Player ID
     * @param reason Kick reason
     * @return true if kick successful, false otherwise
     */
    bool KickPlayer(uint32_t playerId, const std::string& reason);

    /**
     * @brief Update player activity
     * 
     * @param playerId Player ID
     */
    void UpdatePlayerActivity(uint32_t playerId);

    /**
     * @brief Get security statistics
     * 
     * @return Current security statistics
     */
    const SecurityStatistics& GetStatistics() const { return m_stats; }

    /**
     * @brief Reset statistics
     */
    void ResetStatistics();

    /**
     * @brief Set security configuration
     * 
     * @param config New security configuration
     */
    void SetConfiguration(const SecurityConfig& config);

    /**
     * @brief Get security configuration
     * 
     * @return Current security configuration
     */
    const SecurityConfig& GetConfiguration() const { return m_config; }

    /**
     * @brief Register security event callback
     * 
     * @param callback Callback function
     */
    void RegisterEventCallback(SecurityEventCallback callback);

    /**
     * @brief Get security report
     * 
     * @return Detailed security report string
     */
    std::string GetSecurityReport() const;

    /**
     * @brief Check if security system is initialized
     * 
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const { return m_isInitialized; }

    // Singleton access (for legacy compatibility)
    static CGuildBattleSecurity& Instance();
    static void SetInstance(std::unique_ptr<CGuildBattleSecurity> instance);

protected:
    // Configuration
    SecurityConfig m_config;
    
    // Player profiles
    std::unordered_map<uint32_t, std::shared_ptr<PlayerSecurityProfile>> m_playerProfiles;
    
    // Banned players
    std::unordered_set<uint32_t> m_bannedPlayers;
    std::unordered_map<uint32_t, std::chrono::system_clock::time_point> m_banExpirations;
    
    // Violation tracking
    std::vector<GuildBattleSecurityViolation> m_violations;
    
    // Statistics
    SecurityStatistics m_stats;
    
    // Event callback
    SecurityEventCallback m_eventCallback;
    
    // Synchronization
    mutable std::mutex m_profilesMutex;
    mutable std::mutex m_bansMutex;
    mutable std::mutex m_violationsMutex;
    mutable std::mutex m_configMutex;
    mutable std::mutex m_callbackMutex;
    
    // State
    std::atomic<bool> m_isInitialized{false};
    std::atomic<bool> m_isShutdown{false};
    std::atomic<uint32_t> m_nextViolationId{1};
    
    // Singleton instance
    static std::unique_ptr<CGuildBattleSecurity> s_instance;
    static std::mutex s_instanceMutex;

private:
    /**
     * @brief Validate player object
     * 
     * @param player Player to validate
     * @return true if valid, false otherwise
     */
    bool ValidatePlayer(CPlayer* player) const;

    /**
     * @brief Validate game object
     * 
     * @param object Game object to validate
     * @return true if valid, false otherwise
     */
    bool ValidateGameObject(CGameObject* object) const;

    /**
     * @brief Calculate movement speed
     * 
     * @param profile Player security profile
     * @param newX New X coordinate
     * @param newY New Y coordinate
     * @param newZ New Z coordinate
     * @return Movement speed in units per second
     */
    double CalculateMovementSpeed(const PlayerSecurityProfile& profile, float newX, float newY, float newZ) const;

    /**
     * @brief Execute security action
     * 
     * @param playerId Player ID
     * @param action Security action to execute
     * @param violation Violation details
     */
    void ExecuteSecurityAction(uint32_t playerId, SecurityAction action, 
                              const GuildBattleSecurityViolation& violation);

    /**
     * @brief Generate speed hack challenge
     * 
     * @return Challenge data array
     */
    std::array<uint32_t, 4> GenerateSpeedHackChallenge() const;

    /**
     * @brief Cleanup expired bans
     */
    void CleanupExpiredBans();

    /**
     * @brief Notify security event
     * 
     * @param violation Security violation
     */
    void NotifySecurityEvent(const GuildBattleSecurityViolation& violation);

    /**
     * @brief Generate unique violation ID
     * 
     * @return Unique violation identifier
     */
    uint32_t GenerateViolationId();
};

/**
 * @brief Guild Battle Security Factory
 */
class CGuildBattleSecurityFactory {
public:
    /**
     * @brief Create security system with default configuration
     * 
     * @return Unique pointer to security system
     */
    static std::unique_ptr<CGuildBattleSecurity> CreateDefaultSecurity();

    /**
     * @brief Create security system with custom configuration
     * 
     * @param config Security configuration
     * @return Unique pointer to security system
     */
    static std::unique_ptr<CGuildBattleSecurity> CreateSecurity(const SecurityConfig& config);
};

/**
 * @brief Guild battle security utility functions
 */
namespace SecurityUtils {
    std::string SecurityThreatLevelToString(SecurityThreatLevel level);
    std::string SecurityViolationTypeToString(SecurityViolationType type);
    std::string SecurityActionToString(SecurityAction action);
    SecurityThreatLevel StringToSecurityThreatLevel(const std::string& levelStr);
    SecurityViolationType StringToSecurityViolationType(const std::string& typeStr);
    SecurityAction StringToSecurityAction(const std::string& actionStr);
    std::string FormatSecurityViolation(const GuildBattleSecurityViolation& violation);
    bool IsValidPlayerId(uint32_t playerId);
    bool IsValidGuildId(uint32_t guildId);
}

} // namespace Guild
} // namespace NexusProtection
