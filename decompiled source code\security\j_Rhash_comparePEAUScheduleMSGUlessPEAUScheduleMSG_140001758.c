/*
 * Function: j_??R?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@QEBA_NAEBQEAUScheduleMSG@@0@Z
 * Address: 0x140001758
 */

bool __fastcall stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *> > *this, ScheduleMSG *const *_Keyval1, ScheduleMSG *const *_Keyval2)
{
  return stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>>::operator()(this, _Keyval1, _Keyval2);
}
