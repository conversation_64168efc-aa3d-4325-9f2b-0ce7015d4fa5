/*
 * Function: ??$fill@PEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@U12@@std@@YAXPEAU?$BaseAndExponent@UEC2NPoint@CryptoPP@@VInteger@2@@CryptoPP@@0AEBU12@@Z
 * Address: 0x14059E920
 */

int std::fill<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>()
{
  return std::_Fill<CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::EC2NPoint,CryptoPP::Integer>>();
}
