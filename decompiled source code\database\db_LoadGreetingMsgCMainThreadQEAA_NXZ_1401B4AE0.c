/*
 * Function: ?db_LoadGreetingMsg@CMainThread@@QEAA_NXZ
 * Address: 0x1401B4AE0
 */

bool __fastcall CMainThread::db_LoadGreetingMsg(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@6
  __int64 v4; // [sp+0h] [bp-78h]@1
  char *pwszRaceGreetingmsgC; // [sp+20h] [bp-58h]@13
  char *pwszGMName; // [sp+28h] [bp-50h]@13
  char *pwszNameA; // [sp+30h] [bp-48h]@13
  char *pwszNameB; // [sp+38h] [bp-40h]@13
  char *pwszNameC; // [sp+40h] [bp-38h]@13
  char v10; // [sp+50h] [bp-28h]@4
  int nUseType; // [sp+54h] [bp-24h]@7
  CMainThread *v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v1 = &v4;
  for ( i = 26i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v10 = CRFWorldDatabase::Select_CheckGreetRecord(v12->m_pWorldDB, 255);
  if ( v10 != 2 || CRFWorldDatabase::Insert_GreetingRecord(v12->m_pWorldDB, 255, "GM", "Welcome to RF World ") )
  {
    for ( nUseType = 0; nUseType < 3; ++nUseType )
    {
      v10 = CRFWorldDatabase::Select_CheckGreetRecord(v12->m_pWorldDB, nUseType);
      if ( v10 == 2
        && !CRFWorldDatabase::Insert_GreetingRecord(v12->m_pWorldDB, nUseType, "RaceBoss", "There is no Message ") )
      {
        return 0;
      }
    }
    pwszNameC = v12->m_wszBossName[2];
    pwszNameB = v12->m_wszBossName[1];
    pwszNameA = (char *)v12->m_wszBossName;
    pwszGMName = v12->m_wszGMName;
    pwszRaceGreetingmsgC = v12->m_wszRaceGreetingMsg[2];
    result = CRFWorldDatabase::LoadGreetingMsg(
               v12->m_pWorldDB,
               v12->m_wszMainGreetingMsg,
               (char *)v12->m_wszRaceGreetingMsg,
               v12->m_wszRaceGreetingMsg[1],
               v12->m_wszRaceGreetingMsg[2],
               v12->m_wszGMName,
               (char *)v12->m_wszBossName,
               v12->m_wszBossName[1],
               v12->m_wszBossName[2]) != 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
