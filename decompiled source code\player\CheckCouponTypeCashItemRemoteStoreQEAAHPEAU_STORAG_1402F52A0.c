/*
 * Function: ?CheckCouponType@CashItemRemoteStore@@QEAAHPEAU_STORAGE_POS_INDIV@@PEAVCPlayer@@E@Z
 * Address: 0x1402F52A0
 */

__int64 __fastcall CashItemRemoteStore::CheckCouponType(CashItemRemoteStore *this, _STORAGE_POS_INDIV *pCoupon, CPlayer *pOne, char byCouponNum)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@12
  __int64 v7; // [sp+0h] [bp-88h]@1
  _STORAGE_LIST *v8; // [sp+20h] [bp-68h]@7
  unsigned __int8 v9; // [sp+34h] [bp-54h]@4
  unsigned __int8 v10; // [sp+35h] [bp-53h]@4
  unsigned __int8 v11; // [sp+36h] [bp-52h]@4
  unsigned __int8 v12; // [sp+54h] [bp-34h]@4
  unsigned __int8 v13; // [sp+55h] [bp-33h]@4
  unsigned __int8 v14; // [sp+56h] [bp-32h]@4
  unsigned int v15; // [sp+64h] [bp-24h]@4
  int j; // [sp+68h] [bp-20h]@4
  _STORAGE_LIST::_db_con *v17; // [sp+70h] [bp-18h]@8
  _base_fld *v18; // [sp+78h] [bp-10h]@9
  _STORAGE_POS_INDIV *v19; // [sp+98h] [bp+10h]@1
  CPlayer *v20; // [sp+A0h] [bp+18h]@1
  char v21; // [sp+A8h] [bp+20h]@1

  v21 = byCouponNum;
  v20 = pOne;
  v19 = pCoupon;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = -1;
  v10 = -1;
  v11 = -1;
  v12 = -1;
  v13 = -1;
  v14 = -1;
  v15 = 0;
  for ( j = 0; j < (unsigned __int8)v21; ++j )
  {
    v8 = v20->m_Param.m_pStoragePtr[v19[j].byStorageCode];
    if ( v8 )
    {
      v17 = _STORAGE_LIST::GetPtrFromSerial(v8, v19[j].wItemSerial);
      if ( v17 )
      {
        v18 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v17->m_byTableCode, v17->m_wItemIndex);
        if ( v18 )
        {
          *(&v9 + j) = v18[4].m_strCode[0];
          *(&v12 + j) = v18[4].m_strCode[4];
          ++v15;
        }
      }
    }
  }
  if ( (signed int)v15 >= 2 )
  {
    if ( (v9 != v10 || v10 != v11) && (v9 != v11 || v11 != 255) )
    {
      if ( (v12 == v13 || v13 == v14) && (v12 == v13 || v14 != 255) )
        result = v15;
      else
        result = 0i64;
    }
    else
    {
      result = 0i64;
    }
  }
  else
  {
    result = v15;
  }
  return result;
}
