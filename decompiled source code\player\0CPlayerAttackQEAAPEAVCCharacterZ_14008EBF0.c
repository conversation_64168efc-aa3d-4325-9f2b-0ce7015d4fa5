/*
 * Function: ??0CPlayerAttack@@QEAA@PEAVCCharacter@@@Z
 * Address: 0x14008EBF0
 */

void __fastcall CPlayerAttack::CPlayerAttack(CPlayerAttack *this, CCharacter *pThis)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayerAttack *v5; // [sp+30h] [bp+8h]@1
  CCharacter *pThisa; // [sp+38h] [bp+10h]@1

  pThisa = pThis;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CAttack::CAttack((CAttack *)&v5->m_pp, pThis);
  v5->m_pAttPlayer = (CPlayer *)pThisa;
}
