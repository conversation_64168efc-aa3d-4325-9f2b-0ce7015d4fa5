/*
 * Function: j_?max_size@?$vector@PEAVCLogTypeDBTask@@V?$allocator@PEAVCLogTypeDBTask@@@std@@@std@@QEBA_KXZ
 * Address: 0x140006771
 */

unsigned __int64 __fastcall std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::max_size(std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *> > *this)
{
  return std::vector<CLogTypeDBTask *,std::allocator<CLogTypeDBTask *>>::max_size(this);
}
