/*
 * Function: ??4?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@QEAAAEAU01@AEBU01@@Z
 * Address: 0x1405A5060
 */

CryptoPP::ECPPoint *__fastcall CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(CryptoPP::ECPPoint *a1, CryptoPP::ECPPoint *a2)
{
  CryptoPP::ECPPoint *v3; // [sp+30h] [bp+8h]@1

  v3 = a1;
  CryptoPP::ECPPoint::operator=(a1, a2);
  CryptoPP::Integer::operator=(&v3[1]);
  return v3;
}
