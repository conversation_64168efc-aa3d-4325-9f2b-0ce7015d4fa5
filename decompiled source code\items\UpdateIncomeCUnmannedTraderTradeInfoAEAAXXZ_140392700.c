/*
 * Function: ?UpdateIncome@CUnmannedTraderTradeInfo@@AEAAXXZ
 * Address: 0x140392700
 */

void __fastcall CUnmannedTraderTradeInfo::UpdateIncome(CUnmannedTraderTradeInfo *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CUnmannedTraderTradeInfo *v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_ui64TotalOldIncome = v4->m_ui64TotalCurrentIncome;
  if ( GetCurwDay() == 1 )
    v4->m_ui64TotalCurrentIncome = 0i64;
  v4->m_bNeedUpdateSave = 1;
}
