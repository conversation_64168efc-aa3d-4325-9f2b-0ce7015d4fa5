#pragma once

/**
 * @file CDatabaseInitializer.h
 * @brief Modern C++20 Database Initialization System
 * 
 * This file provides a comprehensive database initialization system that
 * coordinates all database subsystems including world DB, user DB, and
 * specialized managers in the correct dependency order.
 */

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <future>

// Forward declarations
class CDatabaseManager;

namespace NexusProtection {
namespace Database {

/**
 * @brief Initialization phase enumeration
 */
enum class InitializationPhase : uint8_t {
    PreInit = 0,
    DatabaseConnections,
    CoreManagers,
    EconomySystem,
    PvPSystem,
    GuildSystem,
    AutomineSystem,
    PostSystem,
    ItemSystem,
    LogSystem,
    Finalization,
    Complete
};

/**
 * @brief Initialization priority levels
 */
enum class InitializationPriority : uint8_t {
    Critical = 0,    // Must succeed for system to function
    High = 1,        // Important for core functionality
    Medium = 2,      // Standard game features
    Low = 3,         // Optional features
    Optional = 4     // Can fail without affecting core systems
};

/**
 * @brief Manager initialization task
 */
struct InitializationTask {
    std::string name;
    std::string description;
    InitializationPhase phase{InitializationPhase::CoreManagers};
    InitializationPriority priority{InitializationPriority::Medium};
    std::function<bool()> initFunction;
    std::function<bool()> loadFunction;
    std::vector<std::string> dependencies;
    std::vector<std::string> optionalDependencies;
    std::chrono::milliseconds timeout{std::chrono::seconds(30)};
    uint32_t retryCount{3};
    bool isInitialized{false};
    bool hasFailed{false};
    std::string lastError;
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point endTime;
    
    InitializationTask() = default;
    InitializationTask(const std::string& n, const std::string& desc, 
                      std::function<bool()> init, std::function<bool()> load)
        : name(n), description(desc), initFunction(std::move(init)), loadFunction(std::move(load)) {}
    
    std::chrono::milliseconds GetExecutionTime() const {
        if (startTime.time_since_epoch().count() == 0) return std::chrono::milliseconds(0);
        auto end = endTime.time_since_epoch().count() > 0 ? endTime : std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(end - startTime);
    }
};

/**
 * @brief Initialization progress information
 */
struct InitializationProgress {
    InitializationPhase currentPhase{InitializationPhase::PreInit};
    uint32_t totalTasks{0};
    uint32_t completedTasks{0};
    uint32_t failedTasks{0};
    uint32_t skippedTasks{0};
    std::chrono::steady_clock::time_point startTime;
    std::chrono::steady_clock::time_point currentPhaseStartTime;
    std::string currentTaskName;
    std::vector<std::string> failedTaskNames;
    std::vector<std::string> skippedTaskNames;
    
    InitializationProgress() {
        auto now = std::chrono::steady_clock::now();
        startTime = now;
        currentPhaseStartTime = now;
    }
    
    float GetProgressPercentage() const {
        return totalTasks > 0 ? (static_cast<float>(completedTasks) / totalTasks) * 100.0f : 0.0f;
    }
    
    std::chrono::milliseconds GetTotalTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - startTime);
    }
    
    std::chrono::milliseconds GetCurrentPhaseTime() const {
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::steady_clock::now() - currentPhaseStartTime);
    }
};

/**
 * @brief Progress callback function type
 */
using ProgressCallback = std::function<void(const InitializationProgress&)>;

/**
 * @brief Modern C++20 Database Initializer class
 * 
 * This class coordinates the initialization of all database systems
 * and managers in the correct dependency order with comprehensive
 * error handling and progress reporting.
 */
class CDatabaseInitializer {
public:
    // Constructor and Destructor
    CDatabaseInitializer();
    virtual ~CDatabaseInitializer();

    // Disable copy constructor and assignment operator
    CDatabaseInitializer(const CDatabaseInitializer&) = delete;
    CDatabaseInitializer& operator=(const CDatabaseInitializer&) = delete;

    // Enable move constructor and assignment operator
    CDatabaseInitializer(CDatabaseInitializer&&) noexcept = default;
    CDatabaseInitializer& operator=(CDatabaseInitializer&&) noexcept = default;

    /**
     * @brief Initialize all database systems
     * 
     * Coordinates the complete initialization sequence for all database
     * systems and managers in the correct dependency order.
     * 
     * @param databaseManager Reference to database manager
     * @param progressCallback Optional progress callback function
     * @return true if initialization successful, false otherwise
     */
    bool InitializeAll(CDatabaseManager& databaseManager, 
                      ProgressCallback progressCallback = nullptr);

    /**
     * @brief Initialize specific phase
     * 
     * Initialize all tasks in a specific phase.
     * 
     * @param phase Phase to initialize
     * @param databaseManager Reference to database manager
     * @return true if phase initialization successful, false otherwise
     */
    bool InitializePhase(InitializationPhase phase, CDatabaseManager& databaseManager);

    /**
     * @brief Register initialization task
     * 
     * Register a new initialization task with dependencies.
     * 
     * @param task Task to register
     * @return true if registration successful, false otherwise
     */
    bool RegisterTask(const InitializationTask& task);

    /**
     * @brief Register multiple tasks
     * 
     * Register multiple initialization tasks at once.
     * 
     * @param tasks Vector of tasks to register
     * @return Number of successfully registered tasks
     */
    size_t RegisterTasks(const std::vector<InitializationTask>& tasks);

    /**
     * @brief Get initialization progress
     * 
     * @return Current initialization progress
     */
    InitializationProgress GetProgress() const;

    /**
     * @brief Check if initialization is complete
     * 
     * @return true if all critical tasks completed successfully
     */
    bool IsInitializationComplete() const;

    /**
     * @brief Check if initialization failed
     * 
     * @return true if critical tasks failed
     */
    bool HasInitializationFailed() const;

    /**
     * @brief Get failed tasks
     * 
     * @return Vector of failed task names
     */
    std::vector<std::string> GetFailedTasks() const;

    /**
     * @brief Get initialization report
     * 
     * @return Detailed initialization report
     */
    std::string GetInitializationReport() const;

    /**
     * @brief Reset initializer state
     * 
     * Reset all tasks and progress for re-initialization.
     */
    void Reset();

    /**
     * @brief Set progress callback
     * 
     * @param callback Progress callback function
     */
    void SetProgressCallback(ProgressCallback callback) { m_progressCallback = std::move(callback); }

    /**
     * @brief Enable/disable parallel initialization
     * 
     * @param enable Whether to enable parallel initialization
     */
    void SetParallelInitialization(bool enable) { m_parallelInitialization = enable; }

    /**
     * @brief Set maximum parallel tasks
     * 
     * @param maxTasks Maximum number of parallel tasks
     */
    void SetMaxParallelTasks(uint32_t maxTasks) { m_maxParallelTasks = maxTasks; }

protected:
    // Task management
    std::unordered_map<std::string, InitializationTask> m_tasks;
    std::unordered_map<InitializationPhase, std::vector<std::string>> m_phaseTaskMap;
    std::vector<std::string> m_initializationOrder;
    
    // Progress tracking
    InitializationProgress m_progress;
    ProgressCallback m_progressCallback;
    
    // Configuration
    std::atomic<bool> m_parallelInitialization{false};
    std::atomic<uint32_t> m_maxParallelTasks{4};
    
    // Synchronization
    mutable std::mutex m_taskMutex;
    mutable std::mutex m_progressMutex;
    
    // State
    std::atomic<bool> m_isInitializing{false};
    std::atomic<bool> m_initializationComplete{false};
    std::atomic<bool> m_initializationFailed{false};

private:
    /**
     * @brief Register default initialization tasks
     */
    void RegisterDefaultTasks();

    /**
     * @brief Calculate task execution order
     * 
     * @return true if order calculated successfully, false if circular dependencies
     */
    bool CalculateExecutionOrder();

    /**
     * @brief Execute task with timeout and retry
     * 
     * @param taskName Name of task to execute
     * @param databaseManager Reference to database manager
     * @return true if task executed successfully, false otherwise
     */
    bool ExecuteTask(const std::string& taskName, CDatabaseManager& databaseManager);

    /**
     * @brief Execute tasks in parallel
     * 
     * @param taskNames Vector of task names to execute
     * @param databaseManager Reference to database manager
     * @return Number of successfully executed tasks
     */
    size_t ExecuteTasksParallel(const std::vector<std::string>& taskNames, 
                               CDatabaseManager& databaseManager);

    /**
     * @brief Check if task dependencies are satisfied
     * 
     * @param taskName Name of task to check
     * @return true if dependencies satisfied, false otherwise
     */
    bool AreDependenciesSatisfied(const std::string& taskName) const;

    /**
     * @brief Update progress information
     * 
     * @param taskName Current task name
     * @param phase Current phase
     */
    void UpdateProgress(const std::string& taskName, InitializationPhase phase);

    /**
     * @brief Notify progress callback
     */
    void NotifyProgress();

    /**
     * @brief Validate task configuration
     * 
     * @param task Task to validate
     * @return true if valid, false otherwise
     */
    bool ValidateTask(const InitializationTask& task) const;

    /**
     * @brief Get tasks for phase
     * 
     * @param phase Phase to get tasks for
     * @return Vector of task names for the phase
     */
    std::vector<std::string> GetTasksForPhase(InitializationPhase phase) const;

    /**
     * @brief Check for circular dependencies
     * 
     * @return true if circular dependencies detected, false otherwise
     */
    bool HasCircularDependencies() const;

    /**
     * @brief Perform topological sort for dependency resolution
     * 
     * @return Sorted task order or empty vector if circular dependencies
     */
    std::vector<std::string> TopologicalSort() const;
};

/**
 * @brief Database Initializer Factory
 */
class CDatabaseInitializerFactory {
public:
    /**
     * @brief Create standard database initializer
     * 
     * @return Unique pointer to database initializer
     */
    static std::unique_ptr<CDatabaseInitializer> CreateStandardInitializer();

    /**
     * @brief Create custom database initializer
     * 
     * @param tasks Custom initialization tasks
     * @return Unique pointer to database initializer
     */
    static std::unique_ptr<CDatabaseInitializer> CreateCustomInitializer(
        const std::vector<InitializationTask>& tasks);
};

/**
 * @brief Utility functions for phase management
 */
namespace InitializationUtils {
    std::string PhaseToString(InitializationPhase phase);
    std::string PriorityToString(InitializationPriority priority);
    InitializationPhase StringToPhase(const std::string& phaseStr);
    InitializationPriority StringToPriority(const std::string& priorityStr);
}

} // namespace Database
} // namespace NexusProtection
