/*
 * Function: ??$class_def@VCMonster@@P81@EAAPEAVCLuaSignalReActor@@XZ@lua_tinker@@YAXPEAUlua_State@@PEBDP8CMonster@@EAAPEAVCLuaSignalReActor@@XZ@Z
 * Address: 0x140407D20
 */

void __fastcall lua_tinker::class_def<CMonster,CLuaSignalReActor * (CMonster::*)(void)>(struct lua_State *L, const char *name, CLuaSignalReActor *(__cdecl *func)(CMonster *this))
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  const char *v5; // rax@4
  void *v6; // rax@5
  __int64 v7; // [sp+0h] [bp-38h]@1
  _QWORD *v8; // [sp+20h] [bp-18h]@5
  _QWORD *v9; // [sp+28h] [bp-10h]@6
  struct lua_State *La; // [sp+40h] [bp+8h]@1
  const char *v11; // [sp+48h] [bp+10h]@1
  CLuaSignalReActor *(__cdecl *funca)(CMonster *); // [sp+50h] [bp+18h]@1

  funca = func;
  v11 = name;
  La = L;
  v3 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v5 = lua_tinker::class_name<CMonster>::name(0i64);
  lua_tinker::meta_push(La, v5);
  if ( lua_type(La, 0xFFFFFFFFi64) == 5 )
  {
    lua_pushstring(La, v11);
    LODWORD(v6) = lua_newuserdata(La, 8i64);
    v8 = operator new(8ui64, v6);
    if ( v8 )
    {
      *v8 = funca;
      v9 = v8;
    }
    else
    {
      v9 = 0i64;
    }
    lua_tinker::push_functor<CLuaSignalReActor *,CMonster>(La, funca);
    lua_rawset(La, 4294967293i64);
  }
  lua_settop(La, 4294967294i64);
}
