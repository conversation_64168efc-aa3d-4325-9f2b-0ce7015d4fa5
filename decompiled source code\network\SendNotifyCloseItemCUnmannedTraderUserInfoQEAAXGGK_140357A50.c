/*
 * Function: ?SendNotifyCloseItem@CUnmannedTraderUserInfo@@QEAAXGGKKE@Z
 * Address: 0x140357A50
 */

void __fastcall CUnmannedTraderUserInfo::SendNotifyCloseItem(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, unsigned __int16 wItemSerial, unsigned int dwRegistSerial, unsigned int dwPrice, char byTax)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v8; // ax@4
  __int64 v9; // [sp+0h] [bp-88h]@1
  _unmannedtrader_close_item_inform_zocl v10; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v12; // [sp+65h] [bp-23h]@4
  unsigned __int16 v13; // [sp+98h] [bp+10h]@1

  v13 = wInx;
  v6 = &v9;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v10.byTaxRate = 1;
  v10.dwPrice = dwPrice;
  v10.wItemSerial = wItemSerial;
  v10.dwRegistSerial = dwRegistSerial;
  pbyType = 30;
  v12 = 27;
  v8 = _unmannedtrader_close_item_inform_zocl::size(&v10);
  CNetProcess::LoadSendMsg(unk_1414F2088, v13, &pbyType, &v10.byTaxRate, v8);
}
