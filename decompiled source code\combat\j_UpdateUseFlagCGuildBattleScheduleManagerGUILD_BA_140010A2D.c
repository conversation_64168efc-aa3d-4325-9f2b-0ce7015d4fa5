/*
 * Function: j_?UpdateUseFlag@CGuildBattleScheduleManager@GUILD_BATTLE@@QEAAPEAVCGuildBattleSchedule@2@IIK@Z
 * Address: 0x140010A2D
 */

GUILD_BATTLE::CGuildBattleSchedule *__fastcall GUILD_BATTLE::CGuildBattleScheduleManager::UpdateUseFlag(GUILD_BATTLE::CGuildBattleScheduleManager *this, unsigned int uiDayID, unsigned int uiMapID, unsigned int dwID)
{
  return GUILD_BATTLE::CGuildBattleScheduleManager::UpdateUseFlag(this, uiDayID, uiMapID, dwID);
}
