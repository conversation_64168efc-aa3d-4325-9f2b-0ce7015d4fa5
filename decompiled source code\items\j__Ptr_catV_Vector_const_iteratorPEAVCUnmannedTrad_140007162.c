/*
 * Function: j_??$_Ptr_cat@V?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@std@@PEAPEAVCUnmannedTraderClassInfo@@@std@@YA?AU_Nonscalar_ptr_iterator_tag@0@AEAV?$_Vector_const_iterator@PEAVCUnmannedTraderClassInfo@@V?$allocator@PEAVCUnmannedTraderClassInfo@@@std@@@0@AEAPEAPEAVCUnmannedTraderClassInfo@@@Z
 * Address: 0x140007162
 */

std::_Nonscalar_ptr_iterator_tag __fastcall std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *> > *__formal, CUnmannedTraderClassInfo ***a2)
{
  return std::_Ptr_cat<std::_Vector_const_iterator<CUnmannedTraderClassInfo *,std::allocator<CUnmannedTraderClassInfo *>>,CUnmannedTraderClassInfo * *>(
           __formal,
           a2);
}
