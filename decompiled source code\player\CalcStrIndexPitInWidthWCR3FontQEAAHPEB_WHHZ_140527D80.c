/*
 * Function: ?CalcStrIndexPitInWidthW@CR3Font@@QEAAHPEB_WHH@Z
 * Address: 0x140527D80
 */

__int64 __fastcall CR3Font::CalcStrIndexPitInWidthW(CR3Font *this, const wchar_t *a2, int a3, int a4)
{
  HDC v4; // rcx@1
  tagSIZE Size; // [sp+40h] [bp-18h]@1
  int nFit; // [sp+60h] [bp+8h]@1

  v4 = (HDC)*((_QWORD *)this + 15);
  nFit = 0;
  GetTextExtentExPointW(v4, a2, a4, a3, &nFit, 0i64, &Size);
  return (unsigned int)nFit;
}
