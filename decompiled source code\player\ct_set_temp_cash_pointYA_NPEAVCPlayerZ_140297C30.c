/*
 * Function: ?ct_set_temp_cash_point@@YA_NPEAVCPlayer@@@Z
 * Address: 0x140297C30
 */

char __fastcall ct_set_temp_cash_point(CPlayer *pOne)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CPvpOrderView *v4; // rax@8
  __int64 v5; // [sp+0h] [bp-38h]@1
  double v6; // [sp+20h] [bp-18h]@8
  CPlayer *v7; // [sp+40h] [bp+8h]@1

  v7 = pOne;
  v1 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( v7 )
  {
    if ( s_nWordCount <= 2 )
    {
      v6 = (double)atoi(s_pwszDstCheat[0]);
      v4 = CPlayer::GetPvpOrderView(v7);
      CPvpOrderView::Update_PvpTempCash(v4, v7->m_ObjID.m_wIndex, v6);
      result = 1;
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
