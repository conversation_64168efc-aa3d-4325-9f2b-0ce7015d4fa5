/*
 * Function: ?PushCreateGuildBattleRankTable@CGuildBattleRankManager@GUILD_BATTLE@@QEAAXXZ
 * Address: 0x1403CB250
 */

void __fastcall GUILD_BATTLE::CGuildBattleRankManager::PushCreateGuildBattleRankTable(GUILD_BATTLE::CGuildBattleRankManager *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  char *pQryData; // [sp+20h] [bp-78h]@4
  int nSize; // [sp+28h] [bp-70h]@4
  char DstBuf; // [sp+38h] [bp-60h]@4
  char v9; // [sp+39h] [bp-5Fh]@4
  ATL::CTime result; // [sp+68h] [bp-30h]@4
  int v11; // [sp+80h] [bp-18h]@4
  int v12; // [sp+84h] [bp-14h]@4
  unsigned __int64 v13; // [sp+88h] [bp-10h]@4

  v1 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v13 = (unsigned __int64)&v5 ^ _security_cookie;
  DstBuf = 0;
  memset(&v9, 0, 9ui64);
  ATL::CTime::GetTickCount(&result);
  v11 = ATL::CTime::GetDay(&result);
  v12 = ATL::CTime::GetMonth(&result);
  v3 = ATL::CTime::GetYear(&result);
  nSize = v11;
  LODWORD(pQryData) = v12;
  sprintf_s(&DstBuf, 0xAui64, "%04d%02d%02d", (unsigned int)v3);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 34, &DstBuf, 10);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  CPvpUserAndGuildRankingSystem::Log(v4, "CGuildBattleRankManager::PushCreateGuildBattleRankTable()");
}
