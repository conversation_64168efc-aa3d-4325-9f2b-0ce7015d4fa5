/*
 * Function: ?WPActiveAttackForce@CPlayerAttack@@QEAAXPEAU_attack_param@@@Z
 * Address: 0x14016FD90
 */

void __fastcall CPlayerAttack::WPActiveAttackForce(CPlayerAttack *this, _attack_param *pParam)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  float v4; // xmm0_4@4
  CPvpUserAndGuildRankingSystem *v5; // rax@5
  __int64 v6; // rcx@17
  float *v7; // rax@18
  __int64 v8; // rdx@18
  _attack_param *v9; // rax@20
  CCharacter **v10; // rcx@20
  _attack_param *v11; // rdx@20
  _attack_param *v12; // r8@20
  __int64 v13; // [sp+0h] [bp-78h]@1
  int nEffAttPower[2]; // [sp+20h] [bp-58h]@17
  bool bUseEffBullet; // [sp+28h] [bp-50h]@17
  _base_fld *v16; // [sp+30h] [bp-48h]@4
  float v17; // [sp+38h] [bp-40h]@4
  char v18; // [sp+3Ch] [bp-3Ch]@5
  unsigned int dwSerial; // [sp+40h] [bp-38h]@5
  int v20; // [sp+44h] [bp-34h]@5
  char v21; // [sp+48h] [bp-30h]@5
  int v22; // [sp+4Ch] [bp-2Ch]@11
  __int64 v23; // [sp+50h] [bp-28h]@17
  int *v24; // [sp+58h] [bp-20h]@17
  int *v25; // [sp+60h] [bp-18h]@18
  int nAttPnt; // [sp+68h] [bp-10h]@20
  CPlayerAttack *v27; // [sp+80h] [bp+8h]@1

  v27 = this;
  v2 = &v13;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v27->m_nDamagedObjNum = 0;
  v27->m_bIsCrtAtt = 0;
  v27->m_pp = pParam;
  v16 = v27->m_pp->pFld;
  v17 = (float)(v27->m_pp->nAddAttPnt + CAttack::_CalcForceAttPnt((CAttack *)&v27->m_pp, 0));
  v4 = v17;
  _effect_parameter::GetEff_Rate(&v27->m_pAttChar->m_EP, 4);
  v17 = v17 * v4;
  if ( !v27->m_pAttPlayer->m_bInGuildBattle )
  {
    dwSerial = CPlayerDB::GetCharSerial(&v27->m_pAttPlayer->m_Param);
    v20 = CPlayerDB::GetRaceCode(&v27->m_pAttPlayer->m_Param);
    v5 = CPvpUserAndGuildRankingSystem::Instance();
    v18 = CPvpUserAndGuildRankingSystem::GetBossType(v5, v20, dwSerial);
    v21 = v18;
    if ( v18 )
    {
      if ( v21 == 2 || v21 == 6 )
        v17 = v17 * 1.2;
    }
    else
    {
      v17 = v17 * 1.3;
    }
  }
  v22 = *(_DWORD *)&v16[11].m_strCode[4];
  if ( v22 >= 0 )
  {
    if ( v22 <= 2 )
    {
      if ( v27->m_pp->pDst )
      {
        v27->m_DamList[0].m_pChar = v27->m_pp->pDst;
        v9 = v27->m_pp;
        v10 = &v27->m_pp->pDst;
        v11 = v27->m_pp;
        v12 = v27->m_pp;
        nAttPnt = (signed int)ffloor(v17);
        bUseEffBullet = v9->bBackAttack;
        *(_QWORD *)nEffAttPower = *v10;
        v27->m_DamList[0].m_nDamage = CCharacter::GetAttackDamPoint(
                                        v27->m_pAttChar,
                                        nAttPnt,
                                        v12->nPart,
                                        v11->nTol,
                                        *(CCharacter **)nEffAttPower,
                                        bUseEffBullet);
        v27->m_nDamagedObjNum = 1;
      }
      goto LABEL_23;
    }
    if ( v22 != 4 )
    {
      if ( v22 == 5 )
      {
        v6 = *(_DWORD *)&v16[4].m_strCode[60];
        v23 = *(_DWORD *)&v16[4].m_strCode[60];
        v24 = s_nLimitDist;
        bUseEffBullet = 0;
        nEffAttPower[0] = 0;
        CAttack::FlashDamageProc(
          (CAttack *)&v27->m_pp,
          s_nLimitDist[v23],
          (signed int)ffloor(v17),
          s_nLimitAngle[1][v6],
          0,
          0);
LABEL_23:
        CAttack::CalcAvgDamage((CAttack *)&v27->m_pp);
        return;
      }
      if ( v22 != 6 )
        return;
    }
    v7 = v27->m_pp->fArea;
    v8 = *(_DWORD *)&v16[4].m_strCode[60];
    v25 = s_nLimitRadius;
    bUseEffBullet = 0;
    nEffAttPower[0] = 0;
    CAttack::AreaDamageProc((CAttack *)&v27->m_pp, s_nLimitRadius[v8], (signed int)ffloor(v17), v7, 0, 0);
    goto LABEL_23;
  }
}
