/*
 * Function: j_?AddNovusItem@CLuaLootingMgr@@QEAA_NPEBDPEAVCMapData@@GPEAMGKKE@Z
 * Address: 0x14000A853
 */

bool __fastcall CLuaLootingMgr::AddNovusItem(CLuaLootingMgr *this, const char *strItemCode, CMapData *pMap, unsigned __int16 wLayerIndex, float *fPos, unsigned __int16 wLootRange, unsigned int dwOverlapCnt, unsigned int dwItemNum, char byCreateType)
{
  return CLuaLootingMgr::AddNovusItem(
           this,
           strItemCode,
           pMap,
           wLayerIndex,
           fPos,
           wLootRange,
           dwOverlapCnt,
           dwItemNum,
           byCreateType);
}
