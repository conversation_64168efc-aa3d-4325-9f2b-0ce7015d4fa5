/*
 * Function: ?dtor$0@?0???$_Cancel@V?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@@?$task_completion_event@E@Concurrency@@QEBA_NV?$shared_ptr@U_ExceptionHolder@details@Concurrency@@@std@@PEAX@Z@4HA_6
 * Address: 0x1405A6C30
 */

void __fastcall `Concurrency::task_completion_event<unsigned char>::_Cancel<std::shared_ptr<Concurrency::details::_ExceptionHolder>>'::`1'::dtor$0(__int64 a1, __int64 a2)
{
  std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>::~_Vector_const_iterator<unsigned int,std::allocator<unsigned int>>(*(std::_Vector_const_iterator<unsigned int,std::allocator<unsigned int> > **)(a2 + 120));
}
