/*
 * Function: j_?ClassUP@CMgrAvatorItemHistory@@QEAAXEEPEAD0PEAH10@Z
 * Address: 0x14000A7D1
 */

void __fastcall CMgrAvatorItemHistory::ClassUP(CMgrAvatorItemHistory *this, char byCurClassGrade, char byLastClassGrade, char *szOldClass, char *szCurClass, int *piOldMaxPoint, int *piAlterMaxPoint, char *pszFileName)
{
  CMgrAvatorItemHistory::ClassUP(
    this,
    byCurClassGrade,
    byLastClassGrade,
    szOldClass,
    szCurClass,
    piOldMaxPoint,
    piAlterMaxPoint,
    pszFileName);
}
