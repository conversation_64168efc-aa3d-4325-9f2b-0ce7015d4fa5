/*
 * Function: ?Load@CUnmannedTraderController@@QEAA_NGKAEAU_TRADE_DB_BASE@@@Z
 * Address: 0x140078C10
 */

bool __fastcall CUnmannedTraderController::Load(CUnmannedTraderController *this, unsigned __int16 wInx, unsigned int dwSerial, _TRADE_DB_BASE *kInfo)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  CUnmannedTraderUserInfoTable *v6; // rax@4
  __int64 v8; // [sp+0h] [bp-38h]@1
  unsigned __int16 v9; // [sp+48h] [bp+10h]@1
  unsigned int dwSeriala; // [sp+50h] [bp+18h]@1
  _TRADE_DB_BASE *v11; // [sp+58h] [bp+20h]@1

  v11 = kInfo;
  dwSeriala = dwSerial;
  v9 = wInx;
  v4 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v6 = CUnmannedTraderUserInfoTable::Instance();
  return CUnmannedTraderUserInfoTable::Load(v6, 0, v9, dwSeriala, v11);
}
