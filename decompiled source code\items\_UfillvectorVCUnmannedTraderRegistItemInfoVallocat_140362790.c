/*
 * Function: ?_Ufill@?$vector@VCUnmannedTraderRegistItemInfo@@V?$allocator@VCUnmannedTraderRegistItemInfo@@@std@@@std@@IEAAPEAVCUnmannedTraderRegistItemInfo@@PEAV3@_KAEBV3@@Z
 * Address: 0x140362790
 */

CUnmannedTraderRegistItemInfo *__fastcall std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>::_Ufill(std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *this, CUnmannedTraderRegistItemInfo *_Ptr, unsigned __int64 _Count, CUnmannedTraderRegistItemInfo *_Val)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-28h]@1
  std::vector<CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo> > *v8; // [sp+30h] [bp+8h]@1
  CUnmannedTraderRegistItemInfo *_First; // [sp+38h] [bp+10h]@1
  unsigned __int64 _Counta; // [sp+40h] [bp+18h]@1

  _Counta = _Count;
  _First = _Ptr;
  v8 = this;
  v4 = &v7;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  stdext::unchecked_uninitialized_fill_n<CUnmannedTraderRegistItemInfo *,unsigned __int64,CUnmannedTraderRegistItemInfo,std::allocator<CUnmannedTraderRegistItemInfo>>(
    _Ptr,
    _Count,
    _Val,
    &v8->_Alval);
  return &_First[_Counta];
}
