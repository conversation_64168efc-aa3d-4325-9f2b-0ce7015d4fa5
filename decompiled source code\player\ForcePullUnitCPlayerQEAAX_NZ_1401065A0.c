/*
 * Function: ?ForcePullUnit@CPlayer@@QEAAX_N@Z
 * Address: 0x1401065A0
 */

void __fastcall CPlayer::ForcePullUnit(CPlayer *this, bool bLogout)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CMoneySupplyMgr *v4; // rax@16
  __int64 v5; // [sp+0h] [bp-38h]@1
  int v6; // [sp+20h] [bp-18h]@12
  int nLv; // [sp+24h] [bp-14h]@16
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  bool v9; // [sp+48h] [bp+10h]@1

  v9 = bLogout;
  v8 = this;
  v2 = &v5;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v8->m_pUsingUnit && (bLogout || v8->m_pParkingUnit) )
  {
    if ( v8->m_pParkingUnit )
      CParkingUnit::Destroy(v8->m_pParkingUnit, 0);
    if ( v9 )
    {
      CPlayer::_UpdateUnitDebt(v8, v8->m_pUsingUnit->bySlotIndex, 0x3E8u);
    }
    else if ( CPlayerDB::GetDalant(&v8->m_Param) < 0x3E8 )
    {
      CPlayer::_UpdateUnitDebt(v8, v8->m_pUsingUnit->bySlotIndex, 0x3E8u);
    }
    else
    {
      CPlayer::SubDalant(v8, 0x3E8u);
      v6 = CPlayerDB::GetLevel(&v8->m_Param);
      if ( v6 == 30 || v6 == 40 || v6 == 50 || v6 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&v8->m_Param);
        v4 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateBuyUnitData(v4, nLv, 0x3E8u);
      }
    }
    if ( v9 )
    {
      if ( v8->m_bOper )
      {
        v8->m_pUsingUnit->dwCutTime = GetKorLocalTime();
        if ( v8->m_pUserDB )
          CUserDB::Update_UnitData(v8->m_pUserDB, v8->m_pUsingUnit->bySlotIndex, v8->m_pUsingUnit);
      }
    }
    if ( !v9 )
      CPlayer::SendMsg_UnitForceReturnInform(v8, v8->m_pUsingUnit->bySlotIndex, 0x3E8u);
    CPlayer::_LockUnitKey(v8, v8->m_pUsingUnit->bySlotIndex, 0);
    v8->m_pUsingUnit = 0i64;
    v8->m_pParkingUnit = 0i64;
  }
}
