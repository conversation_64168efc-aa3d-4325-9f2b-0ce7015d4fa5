/*
 * Function: ??8CUnmannedTraderItemCodeInfo@@QEAA_NPEBD@Z
 * Address: 0x14038D8B0
 */

char __fastcall CUnmannedTraderItemCodeInfo::operator==(CUnmannedTraderItemCodeInfo *this, const char *szCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  size_t v5; // rax@6
  __int64 v6; // [sp+0h] [bp-38h]@1
  char *Str; // [sp+20h] [bp-18h]@11
  int v8; // [sp+28h] [bp-10h]@13
  int v9; // [sp+2Ch] [bp-Ch]@15
  CUnmannedTraderItemCodeInfo *v10; // [sp+40h] [bp+8h]@1
  char *Str2; // [sp+48h] [bp+10h]@1

  Str2 = (char *)szCode;
  v10 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( szCode )
  {
    v5 = strlen_0(v10->m_szCode);
    if ( !strncmp(v10->m_szCode, Str2, v5) )
    {
      if ( v10->m_dwStartInx || v10->m_dwEndInx )
      {
        Str = strpbrk(Str2, "0123456789");
        if ( Str )
        {
          v8 = atoi(Str);
          v9 = v10->m_dwStartInx <= v8 && v8 <= v10->m_dwEndInx;
          result = v9;
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 1;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
