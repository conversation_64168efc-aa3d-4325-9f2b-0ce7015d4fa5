/*
 * Function: SQLRemoveTranslatorW
 * Address: 0x1404DB224
 */

int __fastcall SQLRemoveTranslatorW(const unsigned __int16 *lpszTranslator, unsigned int *lpdwUsageCount)
{
  const unsigned __int16 *v2; // rdi@1
  unsigned int *v3; // rbx@1
  __int64 (__cdecl *v4)(); // rax@1
  int result; // eax@2

  v2 = lpszTranslator;
  v3 = lpdwUsageCount;
  v4 = ODBC___GetSetupProc("SQLRemoveTranslatorW");
  if ( v4 )
    result = ((int (__fastcall *)(const unsigned __int16 *, unsigned int *))v4)(v2, v3);
  else
    result = 0;
  return result;
}
