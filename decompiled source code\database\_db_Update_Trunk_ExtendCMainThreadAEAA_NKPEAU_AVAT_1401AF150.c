/*
 * Function: ?_db_Update_Trunk_Extend@CMainThread@@AEAA_NKPEAU_AVATOR_DATA@@0PEAD@Z
 * Address: 0x1401AF150
 */

char __fastcall CMainThread::_db_Update_Trunk_Extend(CMainThread *this, unsigned int dwAccountSerial, _AVATOR_DATA *pNewData, _AVATOR_DATA *pOldData, char *pwszQuery)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  int v7; // eax@10
  int v8; // eax@13
  int v9; // eax@14
  signed __int64 v10; // rdi@35
  int v11; // eax@35
  size_t v12; // rax@37
  __int64 v14; // [sp+0h] [bp-168h]@1
  unsigned int v15; // [sp+20h] [bp-148h]@35
  unsigned __int64 v16; // [sp+28h] [bp-140h]@35
  unsigned int v17; // [sp+30h] [bp-138h]@35
  unsigned int v18; // [sp+38h] [bp-130h]@35
  unsigned int v19; // [sp+40h] [bp-128h]@35
  int v20; // [sp+48h] [bp-120h]@35
  unsigned int v21; // [sp+50h] [bp-118h]@35
  unsigned __int64 v22; // [sp+58h] [bp-110h]@35
  unsigned int v23; // [sp+60h] [bp-108h]@35
  unsigned int v24; // [sp+68h] [bp-100h]@35
  char Source; // [sp+80h] [bp-E8h]@4
  char v26; // [sp+81h] [bp-E7h]@4
  char *Dest; // [sp+108h] [bp-60h]@4
  size_t Size; // [sp+110h] [bp-58h]@4
  unsigned int j; // [sp+118h] [bp-50h]@6
  unsigned int v30; // [sp+11Ch] [bp-4Ch]@24
  unsigned int v31; // [sp+120h] [bp-48h]@24
  unsigned int v32; // [sp+124h] [bp-44h]@31
  int v33; // [sp+130h] [bp-38h]@13
  __int64 v34; // [sp+138h] [bp-30h]@35
  int v35; // [sp+140h] [bp-28h]@35
  __int64 v36; // [sp+148h] [bp-20h]@35
  unsigned __int64 v37; // [sp+150h] [bp-18h]@4
  unsigned int v38; // [sp+178h] [bp+10h]@1
  _AVATOR_DATA *v39; // [sp+180h] [bp+18h]@1
  _AVATOR_DATA *v40; // [sp+188h] [bp+20h]@1

  v40 = pOldData;
  v39 = pNewData;
  v38 = dwAccountSerial;
  v5 = &v14;
  for ( i = 88i64; i; --i )
  {
    *(_DWORD *)v5 = -*********;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v37 = (unsigned __int64)&v14 ^ _security_cookie;
  Source = 0;
  memset(&v26, 0, 0x7Fui64);
  Dest = pwszQuery;
  sprintf(pwszQuery, "UPDATE tbl_AccountTrunk_Extend SET ");
  LODWORD(Size) = strlen_0(Dest);
  BYTE4(Size) = v39->dbTrunk.byExtSlotNum;
  BYTE5(Size) = v39->dbAvator.m_byRaceSexCode >> 1;
  if ( v39->dbTrunk.byExtSlotNum != v40->dbTrunk.byExtSlotNum )
  {
    sprintf(&Source, "EstSlot=%d,", v39->dbTrunk.byExtSlotNum);
    strcat_0(Dest, &Source);
  }
  for ( j = 0; (signed int)j < BYTE4(Size); ++j )
  {
    if ( _INVENKEY::IsFilled(&v39->dbTrunk.m_ExtList[j].Key) )
    {
      if ( _INVENKEY::IsFilled(&v40->dbTrunk.m_ExtList[j].Key) )
      {
        v33 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_ExtList[j].Key);
        v8 = _INVENKEY::CovDBKey(&v40->dbTrunk.m_ExtList[j].Key);
        if ( v33 != v8 )
        {
          v9 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_ExtList[j].Key);
          sprintf(&Source, "K%d=%d,", j, (unsigned int)v9);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_ExtList[j].dwDur != v40->dbTrunk.m_ExtList[j].dwDur )
        {
          sprintf(&Source, "D%d=%I64d,", j, v39->dbTrunk.m_ExtList[j].dwDur);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_ExtList[j].dwUpt != v40->dbTrunk.m_ExtList[j].dwUpt )
        {
          sprintf(&Source, "U%d=%d,", j, v39->dbTrunk.m_ExtList[j].dwUpt);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_ExtList[j].byRace != v40->dbTrunk.m_ExtList[j].byRace )
        {
          sprintf(&Source, "R%d=%d,", j, v39->dbTrunk.m_ExtList[j].byRace);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_ExtList[j].lnUID != v40->dbTrunk.m_ExtList[j].lnUID )
        {
          sprintf(&Source, "S%d=%I64d,", j, v39->dbTrunk.m_ExtList[j].lnUID);
          strcat_0(Dest, &Source);
        }
        if ( v39->dbTrunk.m_ExtList[j].byCsMethod )
        {
          v30 = 0;
          v31 = 0;
          if ( v39->dbTrunk.m_ExtList[j].byCsMethod == 1 )
          {
            v30 = v39->dbTrunk.m_ExtList[j].dwT - Time;
            v31 = v40->dbTrunk.m_ExtList[j].dwT - Time;
          }
          else if ( v39->dbTrunk.m_ExtList[j].byCsMethod == 2 )
          {
            v30 = v39->dbTrunk.m_ExtList[j].dwT;
            v31 = v40->dbTrunk.m_ExtList[j].dwT;
          }
          if ( v30 != v31 )
          {
            sprintf(&Source, "T%d=%d,", j, v30);
            strcat_0(Dest, &Source);
          }
        }
      }
      else
      {
        v32 = 0;
        if ( v39->dbTrunk.m_ExtList[j].byCsMethod == 1 )
        {
          v32 = v39->dbTrunk.m_ExtList[j].dwT - Time;
        }
        else if ( v39->dbTrunk.m_ExtList[j].byCsMethod == 2 )
        {
          v32 = v39->dbTrunk.m_ExtList[j].dwT;
        }
        v34 = 38i64 * (signed int)j;
        v35 = v39->dbTrunk.m_ExtList[j].byRace;
        v36 = 38i64 * (signed int)j;
        v10 = (signed int)j;
        v11 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_ExtList[j].Key);
        v24 = v32;
        v23 = j;
        v22 = v39->dbTrunk.m_ExtList[(unsigned __int64)v34 / 0x26].lnUID;
        v21 = j;
        v20 = v35;
        v19 = j;
        v18 = v39->dbTrunk.m_ExtList[(unsigned __int64)v36 / 0x26].dwUpt;
        v17 = j;
        v16 = v39->dbTrunk.m_ExtList[v10].dwDur;
        v15 = j;
        sprintf(&Source, "K%d=%d,D%d=%I64d,U%d=%d,R%d=%d,S%d=%I64d,T%d=%d,", j, (unsigned int)v11);
        strcat_0(Dest, &Source);
      }
    }
    else if ( _INVENKEY::IsFilled(&v40->dbTrunk.m_ExtList[j].Key) )
    {
      v7 = _INVENKEY::CovDBKey(&v39->dbTrunk.m_ExtList[j].Key);
      sprintf(&Source, "K%d=%d,", j, (unsigned int)v7);
      strcat_0(Dest, &Source);
    }
  }
  v12 = strlen_0(Dest);
  if ( v12 <= (unsigned int)Size )
  {
    memset_0(Dest, 0, (unsigned int)Size);
  }
  else
  {
    sprintf(&Source, "WHERE AccountSerial=%d", v38);
    Dest[strlen_0(Dest) - 1] = 32;
    strcat_0(Dest, &Source);
  }
  return 1;
}
