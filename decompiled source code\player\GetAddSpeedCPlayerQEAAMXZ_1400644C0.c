/*
 * Function: ?GetAddSpeed@CPlayer@@QEAAMXZ
 * Address: 0x1400644C0
 */

float __fastcall CPlayer::GetAddSpeed(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed int v3; // eax@4
  float v4; // xmm0_4@4
  __int64 v6; // [sp+0h] [bp-48h]@1
  float v7; // [sp+30h] [bp-18h]@4
  CPlayer *v8; // [sp+50h] [bp+8h]@1

  v8 = this;
  v1 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = 0.0;
  _effect_parameter::GetEff_Plus(&v8->m_EP, 20);
  v3 = (signed int)ffloor(0.0 * 100.0);
  v4 = (float)(signed __int16)v3;
  v7 = (float)(signed __int16)v3;
  if ( v8->m_byMoveType == 2 && CEquipItemSFAgent::IsUseBooster(&v8->EquipItemSFAgent) )
  {
    CEquipItemSFAgent::GetBoosterAddSpeed(&v8->EquipItemSFAgent);
    v7 = (float)(signed __int16)(signed int)ffloor(v4 * 100.0);
  }
  return v7;
}
