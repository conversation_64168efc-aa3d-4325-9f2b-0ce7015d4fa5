/*
 * Function: ?SF_AllContHelpSkillRemove_Once@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 * Address: 0x14009E840
 */

bool __fastcall CPlayer::SF_AllContHelpSkillRemove_Once(CPlayer *this, CCharacter *pDstObj)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  float v5; // xmm0_4@4
  bool result; // al@5
  __int64 v7; // [sp+0h] [bp-58h]@1
  int v8; // [sp+30h] [bp-28h]@6
  int j; // [sp+34h] [bp-24h]@6
  bool *v10; // [sp+38h] [bp-20h]@9
  float v11; // [sp+40h] [bp-18h]@4
  CCharacter *v12; // [sp+68h] [bp+10h]@1

  v12 = pDstObj;
  v2 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v4 = rand();
  v5 = (float)(v4 % 100);
  v11 = (float)(v4 % 100);
  _effect_parameter::GetEff_Plus(&v12->m_EP, 38);
  if ( v5 <= v11 )
  {
    v8 = 0;
    for ( j = 0; j < 8; ++j )
    {
      v10 = &v12->m_SFCont[1][j].m_bExist;
      if ( *v10 && !v10[1] )
      {
        CCharacter::RemoveSFContEffect(v12, 1, j, 0, 0);
        ++v8;
      }
    }
    result = v8 > 0;
  }
  else
  {
    result = 0;
  }
  return result;
}
