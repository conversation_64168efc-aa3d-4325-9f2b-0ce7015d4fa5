/*
 * Function: j_??0?$allocator@U?$pair@$$CBHPEAVCNationSettingFactory@@@std@@@std@@QEAA@AEBV01@@Z
 * Address: 0x140002AE0
 */

void __fastcall std::allocator<std::pair<int const,CNationSettingFactory *>>::allocator<std::pair<int const,CNationSettingFactory *>>(std::allocator<std::pair<int const ,CNationSettingFactory *> > *this, std::allocator<std::pair<int const ,CNationSettingFactory *> > *__formal)
{
  std::allocator<std::pair<int const,CNationSettingFactory *>>::allocator<std::pair<int const,CNationSettingFactory *>>(
    this,
    __formal);
}
