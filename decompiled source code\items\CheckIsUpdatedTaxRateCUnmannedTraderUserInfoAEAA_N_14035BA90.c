/*
 * Function: ?CheckIsUpdatedTaxRate@CUnmannedTraderUserInfo@@AEAA_NEPEAVCLogFile@@@Z
 * Address: 0x14035BA90
 */

char __usercall CUnmannedTraderUserInfo::CheckIsUpdatedTaxRate@<al>(CUnmannedTraderUserInfo *this@<rcx>, char byTax@<dl>, CLogFile *pkLogger@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  CUnmannedTraderTaxRateManager *v7; // rax@8
  __int64 v8; // [sp+0h] [bp-88h]@1
  CPlayer *v9; // [sp+30h] [bp-58h]@6
  char v10; // [sp+38h] [bp-50h]@8
  char szMsg; // [sp+44h] [bp-44h]@9
  char pbyType; // [sp+64h] [bp-24h]@9
  char v13; // [sp+65h] [bp-23h]@9
  int v14; // [sp+74h] [bp-14h]@8
  CUnmannedTraderUserInfo *v15; // [sp+90h] [bp+8h]@1
  char v16; // [sp+98h] [bp+10h]@1

  v16 = byTax;
  v15 = this;
  v4 = &v8;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  if ( (signed int)v15->m_wInx < 2532 )
  {
    v9 = &g_Player + v15->m_wInx;
    if ( v9->m_dwObjSerial == v15->m_dwUserSerial )
    {
      v14 = CPlayerDB::GetRaceCode(&v9->m_Param);
      v7 = CUnmannedTraderTaxRateManager::Instance();
      CUnmannedTraderTaxRateManager::GetTaxRate(v7, v14);
      v10 = (signed int)ffloor(a4 * 100.0);
      if ( (unsigned __int8)v10 == (unsigned __int8)v16 )
      {
        result = 0;
      }
      else
      {
        szMsg = v10;
        pbyType = 30;
        v13 = 36;
        CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_wInx, &pbyType, &szMsg, 1u);
        result = 1;
      }
    }
    else
    {
      result = 1;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
