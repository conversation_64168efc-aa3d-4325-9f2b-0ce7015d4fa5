/*
 * Function: ?destroy@?$allocator@VCUnmannedTraderUserInfo@@@std@@QEAAXPEAVCUnmannedTraderUserInfo@@@Z
 * Address: 0x14036A680
 */

void __fastcall std::allocator<CUnmannedTraderUserInfo>::destroy(std::allocator<CUnmannedTraderUserInfo> *this, CUnmannedTraderUserInfo *_Ptr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1

  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Destroy<CUnmannedTraderUserInfo>(_Ptr);
}
