/*
 * Function: ?_wait_tsk_cash_select@CCashDbWorkerGB@@IEAAHPEAVTask@@H@Z
 * Address: 0x140319040
 */

__int64 __fastcall CCashDbWorkerGB::_wait_tsk_cash_select(CCashDbWorkerGB *this, Task *pkTsk, int nIdx)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  _param_cash_select *rParam; // [sp+20h] [bp-18h]@4
  CCashDbWorkerGB *v8; // [sp+40h] [bp+8h]@1
  int nIdxa; // [sp+50h] [bp+18h]@1

  nIdxa = nIdx;
  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  rParam = (_param_cash_select *)Task::GetTaskBuf(pkTsk);
  return CEnglandBillingMgr::CallFunc_RFOnline_Auth(v8->_pkNet, rParam, nIdxa) != 0;
}
