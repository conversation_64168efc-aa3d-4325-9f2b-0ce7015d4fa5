/*
 * Function: ?GetItem@CPlayerDB@@QEAAPEAU_db_con@_STORAGE_LIST@@E@Z
 * Address: 0x14010C5A0
 */

_STORAGE_LIST::_db_con *__fastcall CPlayerDB::GetItem(CPlayerDB *this, char byInvenIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@4
  _STORAGE_LIST::_db_con *result; // rax@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  _STORAGE_LIST::_db_con *v7; // [sp+20h] [bp-18h]@6
  int v8; // [sp+28h] [bp-10h]@4
  CPlayerDB *v9; // [sp+40h] [bp+8h]@1
  char v10; // [sp+48h] [bp+10h]@1

  v10 = byInvenIndex;
  v9 = this;
  v2 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = (unsigned __int8)byInvenIndex;
  v4 = CPlayerDB::GetUseSlot(v9);
  if ( v8 <= (unsigned __int8)v4 )
  {
    v7 = &v9->m_dbInven.m_pStorageList[(unsigned __int8)v10];
    if ( v7->m_bLoad )
      result = v7;
    else
      result = 0i64;
  }
  else
  {
    result = 0i64;
  }
  return result;
}
