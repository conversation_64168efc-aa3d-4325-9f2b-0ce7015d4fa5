/*
 * Function: ?SF_ConvertTargetDest@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 * Address: 0x1400A0720
 */

int __fastcall CPlayer::SF_ConvertTargetDest(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  CGameObjectVtbl *v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v3 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = pDstObj->vfptr;
  return ((int (__fastcall *)(CCharacter *, CPlayer *, _QWORD))v7->FixTargetWhile)(
           pDstObj,
           v8,
           (unsigned int)(signed int)ffloor(fEffectValue));
}
