/*
 * Function: ?IsSame@_target_monster_contsf_allinform_zocl@@SA_NAEAU1@0@Z
 * Address: 0x1400F01A0
 */

char __fastcall _target_monster_contsf_allinform_zocl::IsSame(_target_monster_contsf_allinform_zocl *src1, _target_monster_contsf_allinform_zocl *src2)
{
  int *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  int j; // [sp+0h] [bp-18h]@1
  _target_monster_contsf_allinform_zocl *v6; // [sp+20h] [bp+8h]@1

  v6 = src1;
  v2 = &j;
  for ( i = 4i64; i; --i )
  {
    *v2 = -858993460;
    ++v2;
  }
  if ( v6->dwSerial == src2->dwSerial )
  {
    for ( j = 0; j < 8; ++j )
    {
      if ( v6->m_MonContSf[j].wSfcode != src2->m_MonContSf[j].wSfcode )
        return 0;
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
