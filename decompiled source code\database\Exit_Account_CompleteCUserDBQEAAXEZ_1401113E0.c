/*
 * Function: ?Exit_Account_Complete@CUserDB@@QEAAXE@Z
 * Address: 0x1401113E0
 */

void __fastcall CUserDB::Exit_Account_Complete(CUserDB *this, char byRetCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v4; // ax@4
  __int64 v5; // [sp+0h] [bp-78h]@1
  char Dst; // [sp+38h] [bp-40h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CUserDB *v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v5;
  for ( i = 28i64; i; --i )
  {
    *(_DWORD *)v2 = -*********;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9->m_bDBWaitState = 0;
  memcpy_0(&Dst, &v9->m_gidGlobal, 8ui64);
  pbyType = 1;
  v8 = 5;
  v4 = _logout_account_request_wrac::size((_logout_account_request_wrac *)&Dst);
  CNetProcess::LoadSendMsg(unk_1414F2090, 0, &pbyType, &Dst, v4);
  CUserDB::ParamInit(v9);
}
