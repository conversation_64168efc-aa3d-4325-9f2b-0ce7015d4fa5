/*
 * Function: ?<PERSON><PERSON><PERSON>@CGuildBattleController@@AEAA_NXZ
 * Address: 0x1403D7960
 */

char __fastcall CGuildBattleController::SaveINI(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  GUILD_BATTLE::CNormalGuildBattleFieldList *v3; // rax@4
  char *v4; // rax@4
  char result; // al@5
  char *v6; // rax@6
  char *v7; // rax@8
  char *v8; // rax@10
  char *v9; // rax@12
  GUILD_BATTLE::CGuildBattleLogger *v10; // rax@14
  __int64 v11; // [sp+0h] [bp-198h]@1
  int v12; // [sp+20h] [bp-178h]@14
  int v13; // [sp+28h] [bp-170h]@14
  __int32 v14; // [sp+30h] [bp-168h]@14
  GUILD_BATTLE::CGuildBattleScheduleManager *v15; // [sp+40h] [bp-158h]@4
  char Dest; // [sp+60h] [bp-138h]@4
  __int32 v17; // [sp+164h] [bp-34h]@4
  int Value; // [sp+168h] [bp-30h]@4
  int v19; // [sp+16Ch] [bp-2Ch]@4
  int v20; // [sp+170h] [bp-28h]@4
  int v21; // [sp+174h] [bp-24h]@4
  unsigned __int64 v22; // [sp+180h] [bp-18h]@4

  v1 = &v11;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = (unsigned __int64)&v11 ^ _security_cookie;
  v15 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
  v3 = GUILD_BATTLE::CNormalGuildBattleFieldList::Instance();
  v17 = GUILD_BATTLE::CNormalGuildBattleFieldList::GetMapCnt(v3);
  Value = GetCurDay();
  v19 = GUILD_BATTLE::CGuildBattleScheduleManager::GetTodayDayID(v15);
  v20 = GetNextDay();
  v21 = GUILD_BATTLE::CGuildBattleScheduleManager::GetTomorrowDayID(v15);
  v4 = _itoa(Value, &Dest, 10);
  if ( WritePrivateProfileStringA("GuildBattle", "Today", v4, "..\\SystemSave\\ServerState.ini") )
  {
    v6 = _itoa(v19, &Dest, 10);
    if ( WritePrivateProfileStringA("GuildBattle", "TodayDayID", v6, "..\\SystemSave\\ServerState.ini") )
    {
      v7 = _itoa(v20, &Dest, 10);
      if ( WritePrivateProfileStringA("GuildBattle", "Tomorrow", v7, "..\\SystemSave\\ServerState.ini") )
      {
        v8 = _itoa(v21, &Dest, 10);
        if ( WritePrivateProfileStringA("GuildBattle", "TomorrowDayID", v8, "..\\SystemSave\\ServerState.ini") )
        {
          v9 = _ltoa(v17, &Dest, 10);
          if ( WritePrivateProfileStringA("GuildBattle", "MapCnt", v9, "..\\SystemSave\\ServerState.ini") )
          {
            v10 = GUILD_BATTLE::CGuildBattleLogger::Instance();
            v14 = v17;
            v13 = v21;
            v12 = v20;
            GUILD_BATTLE::CGuildBattleLogger::Log(
              v10,
              "CGuildBattleController::SaveINI() : iToday : %d, iTodayDayID : %u, iTommorow : %d, iTomorrowDayID : %u, ui"
              "MapCnt : %u Save!",
              (unsigned int)Value,
              (unsigned int)v19);
            result = 1;
          }
          else
          {
            result = 0;
          }
        }
        else
        {
          result = 0;
        }
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}
