/*
 * Function: ?AssignFrom@?$DL_GroupParameters_IntegerBasedImpl@VModExpPrecomputation@CryptoPP@@V?$DL_FixedBasePrecomputationImpl@VInteger@CryptoPP@@@2@@CryptoPP@@UEAAXAEBVNameValuePairs@2@@Z
 * Address: 0x140552400
 */

int __fastcall CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>::AssignFrom(__int64 a1, __int64 a2)
{
  char v3; // [sp+20h] [bp-28h]@1

  return CryptoPP::AssignFromHelper<CryptoPP::DL_GroupParameters_IntegerBased,CryptoPP::DL_GroupParameters_IntegerBasedImpl<CryptoPP::ModExpPrecomputation,CryptoPP::DL_FixedBasePrecomputationImpl<CryptoPP::Integer>>>(
           &v3,
           a1 - 232,
           a2,
           0i64);
}
