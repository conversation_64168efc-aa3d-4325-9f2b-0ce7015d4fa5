/*
 * Function: ??$_Fill@PEAVCUnmannedTraderUserInfo@@V1@@std@@YAXPEAVCUnmannedTraderUserInfo@@0AEBV1@@Z
 * Address: 0x140369FC0
 */

void __fastcall std::_Fill<CUnmannedTraderUserInfo *,CUnmannedTraderUserInfo>(CUnmannedTraderUserInfo *_First, CUnmannedTraderUserInfo *_Last, CUnmannedTraderUserInfo *_Val)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CUnmannedTraderUserInfo *v6; // [sp+30h] [bp+8h]@1
  CUnmannedTraderUserInfo *v7; // [sp+38h] [bp+10h]@1
  CUnmannedTraderUserInfo *__that; // [sp+40h] [bp+18h]@1

  __that = _Val;
  v7 = _Last;
  v6 = _First;
  v3 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  while ( v6 != v7 )
  {
    CUnmannedTraderUserInfo::operator=(v6, __that);
    ++v6;
  }
}
