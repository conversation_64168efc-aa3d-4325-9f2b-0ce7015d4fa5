/*
 * Function: ?Loop@CGuildBattleController@@QEAAXXZ
 * Address: 0x1403D6760
 */

void __fastcall CGuildBattleController::Loop(CGuildBattleController *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  GUILD_BATTLE::CGuildBattleScheduleManager *v4; // [sp+20h] [bp-18h]@4
  GUILD_BATTLE::CNormalGuildBattleManager *v5; // [sp+28h] [bp-10h]@4

  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = GUILD_BATTLE::CGuildBattleScheduleManager::Instance();
  GUILD_BATTLE::CGuildBattleScheduleManager::Loop(v4);
  v5 = GUILD_BATTLE::CNormalGuildBattleManager::Instance();
  GUILD_BATTLE::CNormalGuildBattleManager::Loop(v5);
}
