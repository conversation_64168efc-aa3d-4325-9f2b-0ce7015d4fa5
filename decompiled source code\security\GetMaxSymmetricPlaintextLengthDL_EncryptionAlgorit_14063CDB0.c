/*
 * Function: ?GetMaxSymmetricPlaintextLength@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@UEBA_K_K@Z
 * Address: 0x14063CDB0
 */

__int64 __fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1>::GetMaxSymmetricPlaintextLength(__int64 a1, unsigned __int64 a2)
{
  unsigned int b; // [sp+20h] [bp-18h]@1
  unsigned __int64 a; // [sp+48h] [bp+10h]@1

  a = a2;
  b = 20;
  return (unsigned int)CryptoPP::SaturatingSubtract<unsigned __int64,unsigned int>(&a, &b);
}
