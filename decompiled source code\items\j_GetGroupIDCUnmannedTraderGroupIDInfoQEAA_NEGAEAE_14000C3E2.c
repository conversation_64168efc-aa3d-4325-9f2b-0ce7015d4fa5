/*
 * Function: j_?GetGroupID@CUnmannedTraderGroupIDInfo@@QEAA_NEGAEAE00AEAK@Z
 * Address: 0x14000C3E2
 */

bool __fastcall CUnmannedTraderGroupIDInfo::GetGroupID(CUnmannedTraderGroupIDInfo *this, char byTableCode, unsigned __int16 wItemTableIndex, char *byDivision, char *byClass, char *bySubClass, unsigned int *dwListIndex)
{
  return CUnmannedTraderGroupIDInfo::GetGroupID(
           this,
           byTableCode,
           wItemTableIndex,
           byDivision,
           byClass,
           bySubClass,
           dwListIndex);
}
