/*
 * Function: ?Release@?$ListHeap@UCell@LendItemSheet@@@@QEAAXXZ
 * Address: 0x140310040
 */

void __fastcall ListHeap<LendItemSheet::Cell>::Release(ListHeap<LendItemSheet::Cell> *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-48h]@1
  unsigned int pdwOutIndex; // [sp+24h] [bp-24h]@4
  unsigned __int64 j; // [sp+38h] [bp-10h]@4
  ListHeap<LendItemSheet::Cell> *v6; // [sp+50h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  pdwOutIndex = 0;
  for ( j = 0i64;
        j < v6->_nMaxSize && CNetIndexList::PopNode_Front((CNetIndexList *)&v6->_listData.m_Head, &pdwOutIndex);
        ++j )
  {
    CNetIndexList::PushNode_Back((CNetIndexList *)&v6->_listEmpty.m_Head, pdwOutIndex);
  }
}
