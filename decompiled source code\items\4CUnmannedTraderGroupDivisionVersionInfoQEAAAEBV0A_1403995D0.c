/*
 * Function: ??4CUnmannedTraderGroupDivisionVersionInfo@@QEAAAEBV0@AEBV0@@Z
 * Address: 0x1403995D0
 */

CUnmannedTraderGroupDivisionVersionInfo *__fastcall CUnmannedTraderGroupDivisionVersionInfo::operator=(CUnmannedTraderGroupDivisionVersionInfo *this, CUnmannedTraderGroupDivisionVersionInfo *lhs)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char v6; // [sp+20h] [bp-98h]@5
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *result; // [sp+38h] [bp-80h]@5
  char v8; // [sp+40h] [bp-78h]@5
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *v9; // [sp+58h] [bp-60h]@5
  char v10; // [sp+60h] [bp-58h]@5
  std::_Vector_iterator<unsigned long,std::allocator<unsigned long> > *v11; // [sp+78h] [bp-40h]@5
  __int64 v12; // [sp+80h] [bp-38h]@4
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *v13; // [sp+88h] [bp-30h]@5
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *v14; // [sp+90h] [bp-28h]@5
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *v15; // [sp+98h] [bp-20h]@5
  std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *v16; // [sp+A0h] [bp-18h]@5
  std::_Vector_iterator<unsigned long,std::allocator<unsigned long> > *v17; // [sp+A8h] [bp-10h]@5
  CUnmannedTraderGroupDivisionVersionInfo *v18; // [sp+C0h] [bp+8h]@1
  CUnmannedTraderGroupDivisionVersionInfo *v19; // [sp+C8h] [bp+10h]@1

  v19 = lhs;
  v18 = this;
  v2 = &v5;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  v18->m_iType = lhs->m_iType;
  std::vector<unsigned long,std::allocator<unsigned long>>::clear(&v18->m_vecuiVersion);
  if ( !std::vector<unsigned long,std::allocator<unsigned long>>::empty(&v19->m_vecuiVersion) )
  {
    result = (std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *)&v6;
    v9 = (std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *)&v8;
    v11 = (std::_Vector_iterator<unsigned long,std::allocator<unsigned long> > *)&v10;
    v13 = std::vector<unsigned long,std::allocator<unsigned long>>::end(
            &v19->m_vecuiVersion,
            (std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long> > *)&v6);
    v14 = v13;
    v15 = std::vector<unsigned long,std::allocator<unsigned long>>::begin(&v19->m_vecuiVersion, v9);
    v16 = v15;
    v17 = std::vector<unsigned long,std::allocator<unsigned long>>::begin(&v18->m_vecuiVersion, v11);
    std::vector<unsigned long,std::allocator<unsigned long>>::insert<std::_Vector_const_iterator<unsigned long,std::allocator<unsigned long>>>(
      &v18->m_vecuiVersion,
      v17,
      v16,
      v14);
  }
  return v18;
}
