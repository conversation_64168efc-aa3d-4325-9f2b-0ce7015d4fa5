/*
 * Function: ?pc_CashDBInfoRecvResult@CMainThread@@QEAAXPEAD000K@Z
 * Address: 0x1401F5200
 */

void __fastcall CMainThread::pc_CashDBInfoRecvResult(CMainThread *this, char *szIP, char *szDBName, char *szAccount, char *szPassword, unsigned int dwPort)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  CCashDBWorkManager *v8; // rax@9
  __int64 v9; // [sp+0h] [bp-198h]@1
  char *v10; // [sp+20h] [bp-178h]@6
  unsigned int v11[2]; // [sp+28h] [bp-170h]@6
  unsigned int v12; // [sp+30h] [bp-168h]@6
  char Buffer; // [sp+50h] [bp-148h]@4
  char v14; // [sp+F0h] [bp-A8h]@4
  unsigned __int64 v15; // [sp+180h] [bp-18h]@4
  CMainThread *v16; // [sp+1A0h] [bp+8h]@1
  const char *Str1; // [sp+1A8h] [bp+10h]@1
  const char *szDBNamea; // [sp+1B0h] [bp+18h]@1
  char *szAccounta; // [sp+1B8h] [bp+20h]@1

  szAccounta = szAccount;
  szDBNamea = szDBName;
  Str1 = szIP;
  v16 = this;
  v6 = &v9;
  for ( i = 100i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v15 = (unsigned __int64)&v9 ^ _security_cookie;
  _strdate(&Buffer);
  _strtime(&v14);
  WriteServerStartHistory("Cash DB Init Begin >> name: %s, ip: %s", szDBNamea, Str1);
  if ( !strcmp_0(Str1, "None") || !strcmp_0(szDBNamea, "None") )
  {
    v12 = dwPort;
    *(_QWORD *)v11 = szPassword;
    v10 = szAccounta;
    MyMessageBox(
      "CMainThread::pc_CashDBInfoRecvResult",
      "DSN : szIP(%s), szDBName(%s), szAccount(%s), szPassword(%s) dwPort(%d) Invalid",
      Str1,
      szDBNamea);
    v12 = dwPort;
    *(_QWORD *)v11 = szPassword;
    v10 = szAccounta;
    CLogFile::Write(
      &v16->m_logLoadingError,
      "CMainThread::pc_CashDBInfoRecvResult : DSN : szIP(%s), szDBName(%s), szAccount(%s), szPassword(%s) dwPort(%d) Invalid",
      Str1,
      szDBNamea);
    v12 = dwPort;
    *(_QWORD *)v11 = szPassword;
    v10 = szAccounta;
    CLogFile::Write(
      &v16->m_logSystemError,
      "CMainThread::pc_CashDBInfoRecvResult : DSN : szIP(%s), szDBName(%s), szAccount(%s), szPassword(%s) dwPort(%d) Invalid",
      Str1,
      szDBNamea);
    ServerProgramExit("CMainThread::pc_CashDBInfoRecvResult Invalid DSN!", 1);
  }
  if ( !CMainThread::CashDBInit(v16, (char *)Str1, (char *)szDBNamea, szAccounta, szPassword, dwPort) )
  {
    v12 = dwPort;
    *(_QWORD *)v11 = szPassword;
    v10 = szAccounta;
    MyMessageBox(
      "CMainThread::pc_CashDBInfoRecvResult",
      "CashDBInit( szIP(%s), szDBName(%s), szAccount(%s), szPassword(%s) dwPort(%d) ) Fail!",
      Str1,
      szDBNamea);
    v12 = dwPort;
    *(_QWORD *)v11 = szPassword;
    v10 = szAccounta;
    CLogFile::Write(
      &v16->m_logLoadingError,
      "CMainThread::pc_CashDBInfoRecvResult : CashDBInit( szIP(%s), szDBName(%s), szAccount(%s), szPassword(%s) dwPort(%d) ) Fail!",
      Str1,
      szDBNamea);
    v12 = dwPort;
    *(_QWORD *)v11 = szPassword;
    v10 = szAccounta;
    CLogFile::Write(
      &v16->m_logSystemError,
      "CMainThread::pc_CashDBInfoRecvResult : CashDBInit( szIP(%s), szDBName(%s), szAccount(%s), szPassword(%s) dwPort(%d) ) Fail!",
      Str1,
      szDBNamea);
    ServerProgramExit("CMainThread::pc_CashDBInfoRecvResult CashDBInit Fail!", 1);
  }
  WriteServerStartHistory("Cash DB Init Complete >>");
  CLogFile::WriteString(&v16->m_logLoadingError, "Cash DB Init Complete >>");
  v8 = CTSingleton<CCashDBWorkManager>::Instance();
  CCashDBWorkManager::Start(v8);
  __trace("%s-%s: Open Cash DB", &Buffer, &v14);
}
