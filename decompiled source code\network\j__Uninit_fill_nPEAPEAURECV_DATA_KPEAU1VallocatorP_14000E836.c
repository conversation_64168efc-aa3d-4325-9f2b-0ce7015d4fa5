/*
 * Function: j_??$_Uninit_fill_n@PEAPEAURECV_DATA@@_KPEAU1@V?$allocator@PEAURECV_DATA@@@std@@@std@@YAXPEAPEAURECV_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAURECV_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000E836
 */

void __fastcall std::_Uninit_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(RECV_DATA **_First, unsigned __int64 _Count, RECV_DATA *const *_Val, std::allocator<RECV_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<RECV_DATA * *,unsigned __int64,RECV_DATA *,std::allocator<RECV_DATA *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
