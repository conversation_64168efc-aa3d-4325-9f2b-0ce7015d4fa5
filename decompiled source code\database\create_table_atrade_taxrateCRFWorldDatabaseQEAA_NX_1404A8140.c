/*
 * Function: ?create_table_atrade_taxrate@CRFWorldDatabase@@QEAA_NXZ
 * Address: 0x1404A8140
 */

bool __fastcall CRFWorldDatabase::create_table_atrade_taxrate(CRFWorldDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  bool result; // al@5
  __int64 v4; // [sp+0h] [bp-458h]@1
  char Dest; // [sp+30h] [bp-428h]@6
  char v6; // [sp+31h] [bp-427h]@6
  unsigned __int64 v7; // [sp+440h] [bp-18h]@4
  CRFWorldDatabase *v8; // [sp+460h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 276i64; i; --i )
  {
    *(_DWORD *)v1 = -*********;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v7 = (unsigned __int64)&v4 ^ _security_cookie;
  if ( CRFNewDatabase::TableExist((CRFNewDatabase *)&v8->vfptr, "tbl_ATradeTaxRate") )
  {
    result = 1;
  }
  else
  {
    Dest = 0;
    memset(&v6, 0, 0x3FFui64);
    sprintf(
      &Dest,
      "CREATE TABLE [dbo].[tbl_ATradeTaxRate] ( [serial] [int] IDENTITY (1, 1) NOT NULL , [Race] [tinyint] NOT NULL , [GS"
      "erial] [int] NOT NULL , [GName] [nvarchar] (24) NOT NULL , [Tax] [tinyint] NOT NULL , [NextTax] [tinyint] NOT NULL"
      " , [UpdateTime] [datetime] NOT NULL ) ON [PRIMARY] ALTER TABLE [dbo].[tbl_ATradeTaxRate] WITH NOCHECK ADD CONSTRAI"
      "NT [PK_tbl_ATradeTaxRate] PRIMARY KEY  CLUSTERED ([serial])  ON [PRIMARY] ALTER TABLE [dbo].[tbl_ATradeTaxRate] WI"
      "TH NOCHECK ADD CONSTRAINT [DF_tbl_ATradeTaxRate_GSerial] DEFAULT (-1) FOR [GSerial], CONSTRAINT [DF_tbl_ATradeTaxR"
      "ate_GName] DEFAULT ('*') FOR [GName], CONSTRAINT [DF_tbl_ATradeTaxRate_Tax] DEFAULT (5) FOR [Tax], CONSTRAINT [DF_"
      "tbl_ATradeTaxRate_NextTax] DEFAULT (5) FOR [NextTax], CONSTRAINT [DF_tbl_ATradeTaxRate_UpdateTime] DEFAULT (getdat"
      "e()) FOR [UpdateTime] CREATE  INDEX [IX_Race] ON [dbo].[tbl_ATradeTaxRate]([Race]) ON [PRIMARY]");
    result = CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v8->vfptr, &Dest, 1);
  }
  return result;
}
