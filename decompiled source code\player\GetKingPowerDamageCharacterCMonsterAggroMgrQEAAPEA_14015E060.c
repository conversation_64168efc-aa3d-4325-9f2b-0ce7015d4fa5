/*
 * Function: ?GetKingPowerDamageCharacter@CMonsterAggroMgr@@QEAAPEAVCCharacter@@XZ
 * Address: 0x14015E060
 */

CCharacter *__fastcall CMonsterAggroMgr::GetKingPowerDamageCharacter(CMonsterAggroMgr *this)
{
  CCharacter *result; // rax@4

  if ( this->m_pKingPowerDamageCharacter
    && this->m_pKingPowerDamageCharacter->m_bLive
    && !this->m_pKingPowerDamageCharacter->m_bCorpse )
  {
    result = this->m_pKingPowerDamageCharacter;
  }
  else
  {
    this->m_pKingPowerDamageCharacter = 0i64;
    result = 0i64;
  }
  return result;
}
