/*
 * Function: ?RequestUILockInit@CNetworkEX@@AEAA_NHPEAD@Z
 * Address: 0x1401D8760
 */

char __fastcall CNetworkEX::RequestUILockInit(CNetworkEX *this, int n, char *pBuf)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-F8h]@1
  char byUILock_HintIndex; // [sp+20h] [bp-D8h]@4
  char *uszUILock_HintAnswer; // [sp+28h] [bp-D0h]@4
  void *Src; // [sp+30h] [bp-C8h]@4
  CPlayer *v10; // [sp+38h] [bp-C0h]@4
  CUserDB *pUserDB; // [sp+40h] [bp-B8h]@4
  char Dst; // [sp+58h] [bp-A0h]@4
  char v13; // [sp+59h] [bp-9Fh]@4
  char szUILockPW_Confirm; // [sp+88h] [bp-70h]@4
  char v15; // [sp+89h] [bp-6Fh]@4
  char v16; // [sp+B8h] [bp-40h]@4
  char v17; // [sp+B9h] [bp-3Fh]@4
  unsigned __int64 v18; // [sp+E0h] [bp-18h]@4

  v3 = &v6;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v18 = (unsigned __int64)&v6 ^ _security_cookie;
  Src = pBuf;
  v10 = &g_Player + n;
  pUserDB = &g_UserDB[n];
  Dst = 0;
  memset(&v13, 0, 0xCui64);
  szUILockPW_Confirm = 0;
  memset(&v15, 0, 0xCui64);
  v16 = 0;
  memset(&v17, 0, 0x10ui64);
  memcpy_0(&Dst, pBuf, 0xCui64);
  memcpy_0(&szUILockPW_Confirm, (char *)Src + 13, 0xCui64);
  memcpy_0(&v16, (char *)Src + 27, 0x10ui64);
  uszUILock_HintAnswer = &v16;
  byUILock_HintIndex = *((_BYTE *)Src + 26);
  CPlayer::pc_RequestUILockInit(v10, pUserDB, &Dst, &szUILockPW_Confirm, byUILock_HintIndex, &v16);
  return 1;
}
