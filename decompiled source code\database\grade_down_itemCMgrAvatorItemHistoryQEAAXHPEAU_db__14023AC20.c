/*
 * Function: ?grade_down_item@CMgrAvatorItemHistory@@QEAAXHPEAU_db_con@_STORAGE_LIST@@0KPEAD@Z
 * Address: 0x14023AC20
 */

void __fastcall CMgrAvatorItemHistory::grade_down_item(CMgrAvatorItemHistory *this, int n, _STORAGE_LIST::_db_con *pItem, _STORAGE_LIST::_db_con *pTalik, unsigned int dwAfterLv, char *pszFileName)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@4
  char *v9; // rax@4
  __int64 v10; // [sp+0h] [bp-C8h]@1
  char *v11; // [sp+20h] [bp-A8h]@4
  unsigned __int64 v12; // [sp+28h] [bp-A0h]@4
  char *v13; // [sp+30h] [bp-98h]@4
  char *v14; // [sp+38h] [bp-90h]@4
  char *v15; // [sp+40h] [bp-88h]@4
  char Dest; // [sp+58h] [bp-70h]@4
  _base_fld *v17; // [sp+88h] [bp-40h]@4
  _base_fld *v18; // [sp+90h] [bp-38h]@4
  char *v19; // [sp+A0h] [bp-28h]@4
  char *v20; // [sp+A8h] [bp-20h]@4
  int nTableCode; // [sp+B0h] [bp-18h]@4
  unsigned __int64 v22; // [sp+B8h] [bp-10h]@4
  CMgrAvatorItemHistory *v23; // [sp+D0h] [bp+8h]@1
  _STORAGE_LIST::_db_con *v24; // [sp+E0h] [bp+18h]@1
  _STORAGE_LIST::_db_con *v25; // [sp+E8h] [bp+20h]@1

  v25 = pTalik;
  v24 = pItem;
  v23 = this;
  v6 = &v10;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v22 = (unsigned __int64)&v10 ^ _security_cookie;
  sData[0] = 0;
  v8 = DisplayItemUpgInfo(pItem->m_byTableCode, pItem->m_dwLv);
  strcpy_0(&Dest, v8);
  v17 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v24->m_byTableCode, v24->m_wItemIndex);
  v19 = v23->m_szCurTime;
  v20 = v23->m_szCurDate;
  nTableCode = v24->m_byTableCode;
  v9 = DisplayItemUpgInfo(nTableCode, dwAfterLv);
  v15 = v19;
  v14 = v20;
  v13 = v9;
  v12 = v24->m_lnUID;
  v11 = &Dest;
  sprintf(sBuf, "DOWNGRADE: %s_%u_@%s[%I64u] -> %s [%s %s]\r\n", v17->m_strCode, v24->m_dwDur);
  strcat_0(sData, sBuf);
  v18 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + v25->m_byTableCode, v25->m_wItemIndex);
  sprintf(sBuf, "\t- T %s\r\n", v18->m_strCode);
  strcat_0(sData, sBuf);
  CMgrAvatorItemHistory::WriteFile(v23, pszFileName, sData);
}
