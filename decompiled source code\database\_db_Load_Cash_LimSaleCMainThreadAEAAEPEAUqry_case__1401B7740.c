/*
 * Function: ?_db_Load_Cash_LimSale@CMainThread@@AEAAEPEAUqry_case_cash_limsale@@@Z
 * Address: 0x1401B7740
 */

char __fastcall CMainThread::_db_Load_Cash_LimSale(CMainThread *this, qry_case_cash_limsale *pDbLimitedSale)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-F8h]@1
  _worlddb_cash_limited_sale pcashlimitedsale; // [sp+30h] [bp-C8h]@4
  char v7; // [sp+E4h] [bp-14h]@4
  int j; // [sp+E8h] [bp-10h]@11
  CMainThread *v9; // [sp+100h] [bp+8h]@1
  qry_case_cash_limsale *v10; // [sp+108h] [bp+10h]@1

  v10 = pDbLimitedSale;
  v9 = this;
  v2 = &v5;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = CRFWorldDatabase::Select_CashLimSale(v9->m_pWorldDB, &pcashlimitedsale);
  if ( v7 == 1 )
    return 24;
  if ( v7 != 2 )
    goto LABEL_17;
  if ( !CRFWorldDatabase::Insert_CashLimSale(v9->m_pWorldDB) )
    return 24;
  if ( CRFWorldDatabase::Select_CashLimSale(v9->m_pWorldDB, &pcashlimitedsale) )
  {
    result = 24;
  }
  else
  {
LABEL_17:
    v10->NewSale.byDck = pcashlimitedsale.byDck;
    v10->NewSale.byLimited_sale_num = pcashlimitedsale.byLimited_sale_num;
    v10->OldSale.byDck = pcashlimitedsale.byDck;
    v10->OldSale.byLimited_sale_num = pcashlimitedsale.byLimited_sale_num;
    for ( j = 0; j < (unsigned __int8)pcashlimitedsale.byLimited_sale_num; ++j )
    {
      v10->NewSale.List[j].nLimcode = pcashlimitedsale.List[j].nLimcode;
      v10->NewSale.List[j].nLimcount = pcashlimitedsale.List[j].nLimcount;
      v10->OldSale.List[j].nLimcode = pcashlimitedsale.List[j].nLimcode;
      v10->OldSale.List[j].nLimcount = pcashlimitedsale.List[j].nLimcount;
    }
    result = 0;
  }
  return result;
}
