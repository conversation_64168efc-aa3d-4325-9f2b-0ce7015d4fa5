/*
 * Function: j_??$_Uninit_fill_n@PEAPEAU_PVP_RANK_PACKED_DATA@@_KPEAU1@V?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@std@@@std@@YAXPEAPEAU_PVP_RANK_PACKED_DATA@@_KAEBQEAU1@AEAV?$allocator@PEAU_PVP_RANK_PACKED_DATA@@@0@U_Scalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x14000E160
 */

void __fastcall std::_Uninit_fill_n<_PVP_RANK_PACKED_DATA * *,unsigned __int64,_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *>>(_PVP_RANK_PACKED_DATA **_First, unsigned __int64 _Count, _PVP_RANK_PACKED_DATA *const *_Val, std::allocator<_PVP_RANK_PACKED_DATA *> *__formal, std::_Scalar_ptr_iterator_tag a5, std::_Range_checked_iterator_tag a6)
{
  std::_Uninit_fill_n<_PVP_RANK_PACKED_DATA * *,unsigned __int64,_PVP_RANK_PACKED_DATA *,std::allocator<_PVP_RANK_PACKED_DATA *>>(
    _First,
    _Count,
    _Val,
    __formal,
    a5,
    a6);
}
