/*
 * Function: ?SendMsg_MemberInfo@CDarkHoleChannel@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x14026BB00
 */

void __fastcall CDarkHoleChannel::SendMsg_MemberInfo(CDarkHoleChannel *this, CPlayer *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char *v4; // rax@11
  unsigned __int16 v5; // ax@12
  __int64 v6; // [sp+0h] [bp-348h]@1
  _darkhole_member_info_inform_zocl v7; // [sp+40h] [bp-308h]@4
  int v8; // [sp+2F4h] [bp-54h]@7
  int j; // [sp+2F8h] [bp-50h]@7
  _dh_player_mgr *v10; // [sp+300h] [bp-48h]@10
  char pbyType; // [sp+314h] [bp-34h]@12
  char v12; // [sp+315h] [bp-33h]@12
  unsigned __int64 v13; // [sp+330h] [bp-18h]@4
  CDarkHoleChannel *v14; // [sp+350h] [bp+8h]@1
  CPlayer *v15; // [sp+358h] [bp+10h]@1

  v15 = pDst;
  v14 = this;
  v2 = &v6;
  for ( i = 208i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v13 = (unsigned __int64)&v6 ^ _security_cookie;
  _darkhole_member_info_inform_zocl::_darkhole_member_info_inform_zocl(&v7);
  if ( v14->m_pLeaderPtr )
    v7.dwLeaderSerial = v14->m_pLeaderPtr->dwSerial;
  else
    v7.dwLeaderSerial = -1;
  v8 = 0;
  for ( j = 0; j < 32; ++j )
  {
    v10 = &v14->m_Quester[j];
    if ( _dh_player_mgr::IsFill(v10) )
    {
      v7.List[v8].dwSerial = v10->dwSerial;
      v4 = CPlayerDB::GetCharNameW(&v14->m_Quester[j].pOne->m_Param);
      strcpy_0(v7.List[v8++].wszName, v4);
    }
  }
  v7.wMemberNum = v8;
  pbyType = 35;
  v12 = 5;
  v5 = _darkhole_member_info_inform_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, (char *)&v7, v5);
}
