/*
 * Function: ?DelPostData@CUserDB@@QEAAXK@Z
 * Address: 0x140117BE0
 */

void __fastcall CUserDB::DelPostData(CUserDB *this, unsigned int dwIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CUserDB *v5; // [sp+30h] [bp+8h]@1
  unsigned int v6; // [sp+38h] [bp+10h]@1

  v6 = dwIndex;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwIndex < 0x32 )
  {
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].dwPSSerial = 0;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].nNumber = 0;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].byState = -1;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].nKey = -1;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].dwDur = 0i64;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].dwUpt = 0;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].dwGold = 0;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].lnUID = 0i64;
    memset_0(v5->m_AvatorData.dbPostData.dbPost.m_PostList[dwIndex].wszSendName, 0, 0x11ui64);
    memset_0(v5->m_AvatorData.dbPostData.dbPost.m_PostList[v6].wszRecvName, 0, 0x11ui64);
    memset_0(v5->m_AvatorData.dbPostData.dbPost.m_PostList[v6].wszTitle, 0, 0x15ui64);
    memset_0(v5->m_AvatorData.dbPostData.dbPost.m_PostList[v6].wszContent, 0, 0xC9ui64);
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[v6].bNew = 0;
    v5->m_AvatorData.dbPostData.dbPost.m_PostList[v6].bUpdate = 0;
  }
}
