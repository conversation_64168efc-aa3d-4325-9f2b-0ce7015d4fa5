/*
 * Function: ??$_Copy_backward_opt@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU12@Urandom_access_iterator_tag@std@@@std@@YAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU12@00Urandom_access_iterator_tag@0@U_Nonscalar_ptr_iterator_tag@0@U_Range_checked_iterator_tag@0@@Z
 * Address: 0x1405A7AF0
 */

__int64 __fastcall std::_Copy_backward_opt<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,std::random_access_iterator_tag>(__int64 a1, __int64 a2, __int64 a3)
{
  __int64 v4; // [sp+30h] [bp+8h]@1
  __int64 v5; // [sp+38h] [bp+10h]@1
  __int64 v6; // [sp+40h] [bp+18h]@1

  v6 = a3;
  v5 = a2;
  v4 = a1;
  while ( v4 != v5 )
  {
    v5 -= 80i64;
    v6 -= 80i64;
    CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::operator=(v6);
  }
  return v6;
}
