/*
 * Function: ?SendMsg_CastVoteResult@CPlayer@@QEAAXE@Z
 * Address: 0x1400E2D90
 */

void __fastcall CPlayer::SendMsg_CastVoteResult(CPlayer *this, char byRetCode)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@7
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg; // [sp+38h] [bp-60h]@4
  int v7; // [sp+39h] [bp-5Fh]@5
  __int16 v8[11]; // [sp+3Dh] [bp-5Bh]@7
  int j; // [sp+54h] [bp-44h]@5
  char pbyType; // [sp+64h] [bp-34h]@8
  char v11; // [sp+65h] [bp-33h]@8
  unsigned __int64 v12; // [sp+80h] [bp-18h]@4
  CPlayer *v13; // [sp+A0h] [bp+8h]@1

  v13 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = (unsigned __int64)&v5 ^ _security_cookie;
  szMsg = byRetCode;
  if ( !byRetCode )
  {
    v7 = v13->m_nVoteSerial;
    for ( j = 0; j < 3; ++j )
    {
      v4 = CPlayerDB::GetRaceCode(&v13->m_Param);
      v8[j] = *(&g_VoteSys[760 * v4 + 6] + 2 * j);
    }
  }
  pbyType = 26;
  v11 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xBu);
}
