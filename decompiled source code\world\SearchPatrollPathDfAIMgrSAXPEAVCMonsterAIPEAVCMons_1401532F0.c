/*
 * Function: ?SearchPatrollPath@DfAIMgr@@SAXPEAVCMonsterAI@@PEAVCMonster@@@Z
 * Address: 0x1401532F0
 */

void __fastcall DfAIMgr::SearchPatrollPath(CMonsterAI *pAI, CMonster *pMon)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMonster *mon; // [sp+38h] [bp+10h]@1

  mon = pMon;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( CMonsterHelper::SearchPatrolMovePos(pMon, (float (*)[3])vTargetPos_0) )
    DfAIMgr::ChangeTargetPos(mon, vTargetPos_0);
}
