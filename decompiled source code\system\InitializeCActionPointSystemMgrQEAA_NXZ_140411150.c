/*
 * Function: ?Initialize@CActionPointSystemMgr@@QEAA_NXZ
 * Address: 0x140411150
 */

char __fastcall CActionPointSystemMgr::Initialize(CActionPointSystemMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  int k; // [sp+24h] [bp-14h]@7
  int l; // [sp+28h] [bp-10h]@10
  CActionPointSystemMgr *v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 3; ++j )
    v8->m_bActive[j] = 0;
  for ( k = 0; k < 3; ++k )
    CActionPointSystemMgr::Load_Event_INI(v8, (_action_point_system_ini *)v8 + k, k);
  for ( l = 0; l < 3; ++l )
  {
    if ( v8->m_st_ini_list[l].m_bUse_event )
      CActionPointSystemMgr::Check_Load_Event_Status(v8, l, (_action_point_system_ini *)v8 + l);
  }
  return 1;
}
