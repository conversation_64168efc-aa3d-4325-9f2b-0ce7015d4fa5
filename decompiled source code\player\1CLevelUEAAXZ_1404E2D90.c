/*
 * Function: ??1<PERSON><PERSON>l@@UEAA@XZ
 * Address: 0x1404E2D90
 */

__int64 __fastcall CLevel::~CLevel(CLevel *this)
{
  CLevel *v1; // rbx@1
  CSkyBox *v2; // rdi@1
  CBsp *v3; // rdi@3

  v1 = this;
  this->vfptr = (CLevelVtbl *)&CLevel::`vftable';
  v2 = this->mSkyBox;
  if ( v2 )
  {
    CSkyBox::~CSkyBox(this->mSkyBox);
    operator delete(v2);
  }
  v3 = v1->mBsp;
  if ( v3 )
  {
    CBsp::~CBsp(v1->mBsp);
    operator delete(v3);
  }
  CExtDummy::~CExtDummy(&v1->mDummy);
  return CAniCamera::~CAniCamera(&v1->mAutoAniCam);
}
