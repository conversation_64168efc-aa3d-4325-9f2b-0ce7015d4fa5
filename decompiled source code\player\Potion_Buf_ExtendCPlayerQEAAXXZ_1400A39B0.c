/*
 * Function: ?Potion_Buf_Extend@CPlayer@@QEAAXXZ
 * Address: 0x1400A39B0
 */

void __fastcall CPlayer::Potion_Buf_Extend(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPlayer *pOne; // [sp+30h] [bp+8h]@1

  pOne = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CExtPotionBuf::UseBuffPotion(&pOne->m_PotionBufUse, pOne);
}
