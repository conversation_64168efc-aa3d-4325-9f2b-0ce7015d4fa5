/*
 * Function: j_?sell_unit@CMgrAvatorItemHistory@@QEAAXHEEMKKKKPEAD@Z
 * Address: 0x14000B776
 */

void __fastcall CMgrAvatorItemHistory::sell_unit(CMgrAvatorItemHistory *this, int n, char bySlotIndex, char byFrameCode, float fGaugeRate, unsigned int dwSellMoney, unsigned int dwPayDalant, unsigned int dwNewDalant, unsigned int dwNewGold, char *pszFileName)
{
  CMgrAvatorItemHistory::sell_unit(
    this,
    n,
    bySlotIndex,
    byFrameCode,
    fGaugeRate,
    dwSellMoney,
    dwPayDalant,
    dwNewDalant,
    dwNewGold,
    pszFileName);
}
