/*
 * Function: ?CheckPotionTime@CExtPotionBuf@@QEAAXPEAVCPlayer@@@Z
 * Address: 0x1403A0050
 */

void __fastcall CExtPotionBuf::CheckPotionTime(CExtPotionBuf *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int nEndHour; // [sp+20h] [bp-18h]@5
  int nEndMin; // [sp+28h] [bp-10h]@5
  CExtPotionBuf *v7; // [sp+40h] [bp+8h]@1
  CPlayer *pOnea; // [sp+48h] [bp+10h]@1

  pOnea = pOne;
  v7 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v7->m_dwEndPotionTime <= GetKorLocalTime() )
  {
    v7->m_bExtPotionBufUse = 0;
    pOnea->m_pUserDB->m_AvatorData.dbSupplement.dwBufPotionEndTime = 0;
    nEndMin = 0;
    nEndHour = 0;
    CExtPotionBuf::SendMsg_RemainBufUseTime(v7, 0, pOnea->m_ObjID.m_wIndex, 0, 0, 0);
    CPotionMgr::SelectDeleteBuf(&g_PotionMgr, pOnea, 1, 1);
  }
  if ( !v7->m_bDayChange && !GetCurrentHour() )
  {
    v7->m_bDayChange = 1;
    CExtPotionBuf::CalcRemainTime(v7, pOnea->m_ObjID.m_wIndex, 1);
  }
  if ( v7->m_bDayChange )
  {
    if ( GetCurrentHour() > 0 )
      v7->m_bDayChange = 0;
  }
}
