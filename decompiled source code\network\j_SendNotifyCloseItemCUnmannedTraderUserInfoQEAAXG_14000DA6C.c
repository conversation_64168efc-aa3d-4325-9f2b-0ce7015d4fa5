/*
 * Function: j_?SendNotifyCloseItem@CUnmannedTraderUserInfo@@QEAAXGGKKE@Z
 * Address: 0x14000DA6C
 */

void __fastcall CUnmannedTraderUserInfo::SendNotifyCloseItem(CUnmannedTraderUserInfo *this, unsigned __int16 wInx, unsigned __int16 wItemSerial, unsigned int dwRegistSerial, unsigned int dwPrice, char byTax)
{
  CUnmannedTraderUserInfo::SendNotifyCloseItem(this, wInx, wItemSerial, dwRegistSerial, dwPrice, byTax);
}
