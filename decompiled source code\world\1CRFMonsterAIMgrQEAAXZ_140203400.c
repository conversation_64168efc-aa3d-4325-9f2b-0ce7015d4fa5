/*
 * Function: ??1CRFMonsterAIMgr@@QEAA@XZ
 * Address: 0x140203400
 */

void __fastcall CRFMonsterAIMgr::~CRFMonsterAIMgr(CRFMonsterAIMgr *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CRFMonsterAIMgr *ptr; // [sp+30h] [bp+8h]@1

  ptr = this;
  v1 = &v3;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `eh vector destructor iterator'(ptr, 8ui64, 1, (void (__cdecl *)(void *))UsPoint<UsStateTBL>::~UsPoint<UsStateTBL>);
}
