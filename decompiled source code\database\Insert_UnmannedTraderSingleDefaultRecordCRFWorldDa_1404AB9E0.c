/*
 * Function: ?Insert_UnmannedTraderSingleDefaultRecord@CRFWorldDatabase@@QEAA_NK@Z
 * Address: 0x1404AB9E0
 */

char __fastcall CRFWorldDatabase::Insert_UnmannedTraderSingleDefaultRecord(CRFWorldDatabase *this, unsigned int dwRowCnt)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // [sp+0h] [bp-4D8h]@1
  int v6; // [sp+20h] [bp-4B8h]@12
  int v7; // [sp+28h] [bp-4B0h]@12
  int v8; // [sp+30h] [bp-4A8h]@12
  int v9; // [sp+38h] [bp-4A0h]@12
  int v10; // [sp+40h] [bp-498h]@12
  int v11; // [sp+48h] [bp-490h]@12
  char Dest; // [sp+60h] [bp-478h]@8
  unsigned int dwSerial; // [sp+474h] [bp-64h]@6
  unsigned __int16 Dst; // [sp+498h] [bp-40h]@6
  unsigned __int16 v15; // [sp+49Ah] [bp-3Eh]@12
  unsigned __int16 v16; // [sp+49Eh] [bp-3Ah]@12
  unsigned __int16 v17; // [sp+4A0h] [bp-38h]@12
  unsigned __int16 v18; // [sp+4A2h] [bp-36h]@12
  unsigned __int16 v19; // [sp+4A4h] [bp-34h]@12
  unsigned __int16 v20; // [sp+4A6h] [bp-32h]@12
  unsigned int j; // [sp+4B4h] [bp-24h]@6
  unsigned __int64 v22; // [sp+4C0h] [bp-18h]@4
  CRFWorldDatabase *v23; // [sp+4E0h] [bp+8h]@1
  unsigned int v24; // [sp+4E8h] [bp+10h]@1

  v24 = dwRowCnt;
  v23 = this;
  v2 = &v5;
  for ( i = 308i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v22 = (unsigned __int64)&v5 ^ _security_cookie;
  if ( dwRowCnt )
  {
    dwSerial = 0;
    memset_0(&Dst, 0, 0x10ui64);
    GetLocalTime((LPSYSTEMTIME)&Dst);
    for ( j = 0; j < v24; ++j )
    {
      sprintf(&Dest, "{ CALL pInsert_defaultutsingleiteminfo }");
      if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v23->vfptr, &Dest, 1) )
        return 0;
      if ( !CRFWorldDatabase::Select_UnmannedTraderSingleItemBottomSerial(v23, &dwSerial) )
        return 0;
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 0);
      v11 = v20;
      v10 = v19;
      v9 = v18;
      v8 = v17;
      v7 = v16;
      v6 = v15;
      sprintf(&Dest, "{ CALL pInsert_defaultutsellinfo(0,%u,'%04d-%02d-%02d %02d:%02d:%02d.%03d') }", dwSerial, Dst);
      if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v23->vfptr, &Dest, 1) )
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
        return 0;
      }
      v11 = v20;
      v10 = v19;
      v9 = v18;
      v8 = v17;
      v7 = v16;
      v6 = v15;
      sprintf(&Dest, "{ CALL pInsert_defaultutresultinfo(0,%u,'%04d-%02d-%02d %02d:%02d:%02d.%03d') }", dwSerial, Dst);
      if ( !CRFNewDatabase::ExecUpdateQuery((CRFNewDatabase *)&v23->vfptr, &Dest, 1) )
      {
        CRFNewDatabase::RollbackTransaction((CRFNewDatabase *)&v23->vfptr);
        CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
        return 0;
      }
      CRFNewDatabase::CommitTransaction((CRFNewDatabase *)&v23->vfptr);
      CRFNewDatabase::SetAutoCommitMode((CRFNewDatabase *)&v23->vfptr, 1);
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
