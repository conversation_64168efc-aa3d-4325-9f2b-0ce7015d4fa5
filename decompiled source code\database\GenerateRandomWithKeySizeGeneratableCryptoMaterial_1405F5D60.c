/*
 * Function: ?GenerateRandomWithKeySize@GeneratableCryptoMaterial@CryptoPP@@QEAAXAEAVRandomNumberGenerator@2@I@Z
 * Address: 0x1405F5D60
 */

void __fastcall CryptoPP::GeneratableCryptoMaterial::GenerateRandomWithKeySize(CryptoPP::GeneratableCryptoMaterial *this, struct CryptoPP::RandomNumberGenerator *a2, int a3, __int64 a4)
{
  __int64 v4; // rax@1
  int v5; // [sp+20h] [bp-58h]@1
  char v6; // [sp+28h] [bp-50h]@1
  __int64 v7; // [sp+50h] [bp-28h]@1
  __int64 v8; // [sp+58h] [bp-20h]@1
  __int64 v9; // [sp+60h] [bp-18h]@1
  CryptoPP::GeneratableCryptoMaterial *v10; // [sp+80h] [bp+8h]@1
  struct CryptoPP::RandomNumberGenerator *v11; // [sp+88h] [bp+10h]@1

  v11 = a2;
  v10 = this;
  v7 = -2i64;
  v5 = a3;
  LOBYTE(a4) = 1;
  LODWORD(v4) = CryptoPP::MakeParameters<int>(&v6, "KeySize", &v5, a4);
  v8 = v4;
  v9 = v4;
  ((void (__fastcall *)(CryptoPP::GeneratableCryptoMaterial *, struct CryptoPP::RandomNumberGenerator *, __int64))v10->vfptr->__vecDelDtor)(
    v10,
    v11,
    v4);
  CryptoPP::AlgorithmParameters<CryptoPP::NullNameValuePairs,int>::~AlgorithmParameters<CryptoPP::NullNameValuePairs,int>(&v6);
}
