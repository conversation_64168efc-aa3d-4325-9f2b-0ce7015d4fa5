/*
 * Function: ?GetCode@CNationCodeStrTable@@QEAAHPEBD@Z
 * Address: 0x14020ACA0
 */

__int64 __fastcall CNationCodeStrTable::GetCode(CNationCodeStrTable *this, const char *szCodeStr)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-88h]@1
  int iCode; // [sp+24h] [bp-64h]@4
  CNationCodeStr pkData; // [sp+48h] [bp-40h]@4
  unsigned int v8; // [sp+60h] [bp-28h]@5
  int v9; // [sp+64h] [bp-24h]@6
  __int64 v10; // [sp+68h] [bp-20h]@4
  unsigned __int64 v11; // [sp+70h] [bp-18h]@4
  CNationCodeStrTable *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v2 = &v5;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = -2i64;
  v11 = (unsigned __int64)&v5 ^ _security_cookie;
  iCode = -1;
  CNationCodeStr::CNationCodeStr(&pkData, -1, szCodeStr);
  if ( CHashMapPtrPool<int,CNationCodeStr>::findkey(&v12->m_kTable, &pkData, &iCode) )
  {
    v9 = iCode;
    CNationCodeStr::~CNationCodeStr(&pkData);
    result = (unsigned int)v9;
  }
  else
  {
    v8 = -1;
    CNationCodeStr::~CNationCodeStr(&pkData);
    result = v8;
  }
  return result;
}
