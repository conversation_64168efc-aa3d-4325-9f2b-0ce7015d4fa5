/*
 * Function: ?Init@_ATTACK_DELAY_CHECKER@@QEAAXXZ
 * Address: 0x140072E60
 */

void __fastcall _ATTACK_DELAY_CHECKER::Init(_ATTACK_DELAY_CHECKER *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  DWORD v5; // [sp+24h] [bp-14h]@7
  _ATTACK_DELAY_CHECKER *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  for ( j = 0; j < 10; ++j )
  {
    _ATTACK_DELAY_CHECKER::_eff_list::init((_ATTACK_DELAY_CHECKER::_eff_list *)v6 + j);
    _ATTACK_DELAY_CHECKER::_mas_list::init(&v6->MAS[j]);
  }
  v5 = timeGetTime();
  v6->dwNextEffTime = v5;
  v6->dwNextGenTime = v5;
  v6->dwLastGnAttackTime = v5;
  v6->dwLastSFAttackTime = v5;
  v6->nFailCount = 0;
  v6->m_nNextAddTime = 0;
}
