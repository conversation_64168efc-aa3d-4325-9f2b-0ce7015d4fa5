/*
 * Function: _CUnmannedTraderScheduler::Loop_::_1_::dtor$0
 * Address: 0x140393AF0
 */

void __fastcall CUnmannedTraderScheduler::Loop_::_1_::dtor_0(__int64 a1, __int64 a2)
{
  std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>::~_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule>>((std::_Vector_iterator<CUnmannedTraderSchedule,std::allocator<CUnmannedTraderSchedule> > *)(a2 + 40));
}
