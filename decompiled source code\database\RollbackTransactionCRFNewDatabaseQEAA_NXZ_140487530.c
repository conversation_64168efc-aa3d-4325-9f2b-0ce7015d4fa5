/*
 * Function: ?RollbackTransaction@CRFNewDatabase@@QEAA_NXZ
 * Address: 0x140487530
 */

bool __fastcall CRFNewDatabase::RollbackTransaction(CRFNewDatabase *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  __int16 v5; // [sp+20h] [bp-18h]@4
  CRFNewDatabase *v6; // [sp+40h] [bp+8h]@1

  v6 = this;
  v1 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = SQLEndTran_0(2, v6->m_hDbc, 1);
  return !v5 || v5 == 1;
}
