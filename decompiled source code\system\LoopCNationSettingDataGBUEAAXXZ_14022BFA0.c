/*
 * Function: ?Loop@CNationSettingDataGB@@UEAAXXZ
 * Address: 0x14022BFA0
 */

void __fastcall CNationSettingDataGB::Loop(CNationSettingDataGB *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  CEngNetworkBillEX *v3; // rax@4
  CEngNetworkBillEX *v4; // rax@5
  CEngNetworkBillEX *v5; // rax@6
  __int64 v6; // [sp+0h] [bp-28h]@1

  v1 = &v6;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = CTSingleton<CEngNetworkBillEX>::Instance();
  if ( !CEngNetworkBillEX::IsGetConnected(v3) )
  {
    v4 = CTSingleton<CEngNetworkBillEX>::Instance();
    CEngNetworkBillEX::ReInitialize(v4);
  }
  v5 = CTSingleton<CEngNetworkBillEX>::Instance();
  v5->OnLoop();
}
