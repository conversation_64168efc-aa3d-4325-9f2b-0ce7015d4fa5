/*
 * Function: ?PumpMessages2@?$SourceTemplate@VStringStore@CryptoPP@@@CryptoPP@@UEAA_KAEAI_N@Z
 * Address: 0x14057E280
 */

__int64 __fastcall CryptoPP::SourceTemplate<CryptoPP::StringStore>::PumpMessages2(__int64 a1)
{
  __int64 v2; // [sp+40h] [bp+8h]@1

  v2 = a1;
  (*(void (**)(void))(*(_QWORD *)a1 + 328i64))();
  return CryptoPP::BufferedTransformation::TransferMessagesTo2((unsigned __int8)v2 + 48);
}
