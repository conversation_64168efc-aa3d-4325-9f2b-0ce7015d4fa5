/*
 * Function: ??4const_iterator@?$_Tree@V?$_Tmap_traits@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@U?$less@V?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@@2@V?$allocator@U?$pair@$$CBV?$basic_string@DU?$char_traits@D@std@@V?$allocator@D@2@@std@@UAreaList@@@std@@@2@$0A@@std@@@std@@QEAAAEAV012@AEBV012@@Z
 * Address: 0x14018CB30
 */

std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *__fastcall std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char>>,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char>>>,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>>,0>>::const_iterator::operator=(std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *this, std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *__that)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *v6; // [sp+30h] [bp+8h]@1
  std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *__thata; // [sp+38h] [bp+10h]@1

  __thata = __that;
  v6 = this;
  v2 = &v5;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  std::_Bidit<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList>,__int64,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList> const *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char>> const,AreaList> const &>::operator=(
    (std::_Bidit<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList>,__int64,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> const *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> const &> *)&v6->_Mycont,
    (std::_Bidit<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList>,__int64,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> const *,std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> const &> *)&__that->_Mycont);
  v6->_Ptr = __thata->_Ptr;
  return v6;
}
