/*
 * Function: ?_db_init_classrefine_count@CMainThread@@QEAAHXZ
 * Address: 0x1401BFDB0
 */

signed __int64 __fastcall CMainThread::_db_init_classrefine_count(CMainThread *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v4; // [sp+0h] [bp-28h]@1
  CMainThread *v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if ( CRFNewDatabase::ExecUpdateQuery(
         (CRFNewDatabase *)&v5->m_pWorldDB->vfptr,
         "update [dbo].[tbl_event] set ClassRefineCnt=0,ClassRefineDate=0",
         1) )
  {
    result = 0i64;
  }
  else
  {
    result = 24i64;
  }
  return result;
}
