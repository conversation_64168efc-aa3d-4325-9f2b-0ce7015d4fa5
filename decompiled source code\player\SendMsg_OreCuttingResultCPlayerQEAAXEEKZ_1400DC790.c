/*
 * Function: ?SendMsg_OreCuttingResult@CPlayer@@QEAAXEEK@Z
 * Address: 0x1400DC790
 */

void __fastcall CPlayer::SendMsg_OreCuttingResult(CPlayer *this, char byErrCode, char byLeftOreNum, unsigned int dwConsumDalant)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@11
  __int64 v7; // [sp+0h] [bp-1B8h]@1
  _ore_cutting_result_zocl v8; // [sp+40h] [bp-178h]@4
  int v9; // [sp+184h] [bp-34h]@5
  int j; // [sp+188h] [bp-30h]@5
  char pbyType; // [sp+194h] [bp-24h]@11
  char v12; // [sp+195h] [bp-23h]@11
  CPlayer *v13; // [sp+1C0h] [bp+8h]@1
  char v14; // [sp+1C8h] [bp+10h]@1
  char v15; // [sp+1D0h] [bp+18h]@1
  unsigned int v16; // [sp+1D8h] [bp+20h]@1

  v16 = dwConsumDalant;
  v15 = byLeftOreNum;
  v14 = byErrCode;
  v13 = this;
  v4 = &v7;
  for ( i = 108i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  _ore_cutting_result_zocl::_ore_cutting_result_zocl(&v8);
  v8.byErrCode = v14;
  v8.byCuttingNum = 0;
  if ( !v14 )
  {
    v8.byLeftNum = v15;
    v8.dwLeftDalant = CPlayerDB::GetDalant(&v13->m_Param);
    v8.dwConsumDalant = v16;
    v9 = 0;
    for ( j = 0; j < GetMaxResKind(); ++j )
    {
      if ( (signed int)v13->m_Param.m_wCuttingResBuffer[j] > 0 )
      {
        v8.ResList[v9].wResIndex = j;
        v8.ResList[v9++].byAddAmount = v13->m_Param.m_wCuttingResBuffer[j];
      }
    }
    v8.byCuttingNum = v9;
  }
  pbyType = 14;
  v12 = 10;
  v6 = _ore_cutting_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &v8.byErrCode, v6);
}
