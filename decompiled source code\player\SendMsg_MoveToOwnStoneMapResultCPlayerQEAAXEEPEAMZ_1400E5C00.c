/*
 * Function: ?SendMsg_MoveToOwnStoneMapResult@CPlayer@@QEAAXEEPEAM@Z
 * Address: 0x1400E5C00
 */

void __fastcall CPlayer::SendMsg_MoveToOwnStoneMapResult(CPlayer *this, char byRetCode, char byMapIndex, float *pos)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v6; // ax@4
  __int64 v7; // [sp+0h] [bp-88h]@1
  _move_to_own_stonemap_result_zocl v8; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+54h] [bp-34h]@4
  char v10; // [sp+55h] [bp-33h]@4
  unsigned __int64 v11; // [sp+70h] [bp-18h]@4
  CPlayer *v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = (unsigned __int64)&v7 ^ _security_cookie;
  v8.byRetCode = byRetCode;
  v8.byMapIndex = byMapIndex;
  FloatToShort(pos, v8.sNewPos, 3);
  pbyType = 25;
  v10 = 25;
  v6 = _move_to_own_stonemap_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, (char *)&v8, v6);
}
