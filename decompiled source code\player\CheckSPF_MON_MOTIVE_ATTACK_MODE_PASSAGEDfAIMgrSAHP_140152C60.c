/*
 * Function: ?CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGE@DfAIMgr@@SAHPEAVCMonsterSkill@@HPEAVCMonsterAI@@PEAVCMonster@@PEAPEAVC<PERSON>haracter@@@Z
 * Address: 0x140152C60
 */

signed __int64 __fastcall DfAIMgr::CheckSPF_MON_MOTIVE_ATTACK_MODE_PASSAGE(CMonsterSkill *pSkill, int nMotiveValue, CMonsterAI *pAI, CMonster *pMon, CCharacter **ppTar)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  unsigned int v8; // eax@9
  int v9; // eax@10
  float v10; // xmm0_4@12
  float v11; // xmm0_4@14
  unsigned int v12; // eax@15
  float v13; // xmm1_4@15
  __int64 v14; // [sp+0h] [bp-48h]@1
  CCharacter *v15; // [sp+20h] [bp-28h]@10
  float v16; // [sp+28h] [bp-20h]@12
  unsigned int v17; // [sp+2Ch] [bp-1Ch]@9
  float v18; // [sp+30h] [bp-18h]@14
  float v19; // [sp+34h] [bp-14h]@15
  float v20; // [sp+38h] [bp-10h]@15
  CMonsterSkill *pSkilla; // [sp+50h] [bp+8h]@1
  int v22; // [sp+58h] [bp+10h]@1
  CMonsterAI *pAIa; // [sp+60h] [bp+18h]@1
  CMonster *pMona; // [sp+68h] [bp+20h]@1

  pMona = pMon;
  pAIa = pAI;
  v22 = nMotiveValue;
  pSkilla = pSkill;
  v5 = &v14;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( !pMon || !pAI || !pSkilla || !ppTar )
    return 0i64;
  v17 = GetLoopTime();
  v8 = CMonsterAI::GetBattleModeTime(pAIa);
  if ( v17 - v8 <= 1000 * v22 )
    goto LABEL_22;
  v9 = CMonsterSkill::GetDstCaseType(pSkilla);
  v15 = DfAIMgr::GetWisdomTarget(v9, pAIa, pMona);
  if ( !v15 )
    return 0i64;
  v10 = 0.0;
  v16 = 0.0;
  Get3DSqrt(pMona->m_fCurPos, v15->m_fCurPos);
  v16 = 0.0;
  if ( v15->m_bMove )
  {
    ((void (__fastcall *)(CMonster *))pMona->vfptr->GetAttackRange)(pMona);
    if ( 0.0 < 50.0 )
    {
      v11 = R3GetLoopTime() * 15.0;
      v18 = v11;
      CMonster::GetMoveSpeed(pMona);
      v10 = v16 - (float)((float)(v18 * v11) * 0.40000001);
      v16 = v10;
    }
  }
  CMonster::GetSkillDelayTime(pMona, pSkilla);
  v19 = v10;
  v20 = (float)(signed int)GetLoopTime();
  v12 = CMonsterSkill::GetBeforeTime(pSkilla);
  v13 = v20 - (float)(signed int)v12;
  if ( v13 >= v19 && (CMonsterSkill::GetAttackDist(pSkilla), v13 >= v16) && ppTar )
  {
    *ppTar = v15;
    result = 1i64;
  }
  else
  {
LABEL_22:
    result = 0i64;
  }
  return result;
}
