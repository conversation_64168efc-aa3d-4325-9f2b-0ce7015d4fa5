/*
 * Function: ?Select_RFEvent_ClassRefine@CRFWorldDatabase@@QEAAHKAEAEAEAK@Z
 * Address: 0x1404B47E0
 */

signed __int64 __fastcall CRFWorldDatabase::Select_RFEvent_ClassRefine(CRFWorldDatabase *this, unsigned int dwAvatorSerial, char *byRefinedCnt, unsigned int *dwRefineDate)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@8
  __int64 v7; // [sp+0h] [bp-188h]@1
  void *SQLStmt; // [sp+20h] [bp-168h]@13
  SQLLEN *StrLen_or_IndPtr; // [sp+28h] [bp-160h]@22
  SQLLEN v10; // [sp+38h] [bp-150h]@22
  __int16 v11; // [sp+44h] [bp-144h]@9
  char _Dest[256]; // [sp+60h] [bp-128h]@4
  unsigned __int8 v13; // [sp+164h] [bp-24h]@16
  unsigned __int8 v14; // [sp+165h] [bp-23h]@24
  unsigned __int8 v15; // [sp+166h] [bp-22h]@32
  unsigned __int64 v16; // [sp+170h] [bp-18h]@4
  CRFWorldDatabase *v17; // [sp+190h] [bp+8h]@1
  char *TargetValue; // [sp+1A0h] [bp+18h]@1
  unsigned int *v19; // [sp+1A8h] [bp+20h]@1

  v19 = dwRefineDate;
  TargetValue = byRefinedCnt;
  v17 = this;
  v4 = &v7;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v16 = (unsigned __int64)&v7 ^ _security_cookie;
  _Dest[0] = 0;
  memset(&_Dest[1], 0, 0xFFui64);
  sprintf_s<256>(
    (char (*)[256])_Dest,
    "select ClassRefineCnt,ClassRefineDate from [dbo].[tbl_event] where avatorserial = %d",
    dwAvatorSerial);
  if ( v17->m_bSaveDBLog )
    CRFNewDatabase::Log((CRFNewDatabase *)&v17->vfptr, _Dest);
  if ( v17->m_hStmtSelect || CRFNewDatabase::ReConnectDataBase((CRFNewDatabase *)&v17->vfptr) )
  {
    v11 = SQLExecDirect_0(v17->m_hStmtSelect, _Dest, -3);
    if ( v11 && v11 != 1 )
    {
      if ( v11 == 100 )
      {
        result = 2i64;
      }
      else
      {
        SQLStmt = v17->m_hStmtSelect;
        CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, _Dest, "SQLExecDirect", SQLStmt);
        CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
        result = 1i64;
      }
    }
    else
    {
      v11 = SQLFetch_0(v17->m_hStmtSelect);
      if ( v11 && v11 != 1 )
      {
        v13 = 0;
        if ( v11 == 100 )
        {
          v13 = 2;
        }
        else
        {
          SQLStmt = v17->m_hStmtSelect;
          CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, _Dest, "SQLExecDirect", SQLStmt);
          CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
          v13 = 1;
        }
        if ( v17->m_hStmtSelect )
          SQLCloseCursor_0(v17->m_hStmtSelect);
        result = v13;
      }
      else
      {
        StrLen_or_IndPtr = &v10;
        SQLStmt = 0i64;
        v11 = SQLGetData_0(v17->m_hStmtSelect, 1u, -6, TargetValue, 0i64, &v10);
        if ( v11 && v11 != 1 )
        {
          v14 = 0;
          if ( v11 == 100 )
          {
            v14 = 2;
          }
          else
          {
            SQLStmt = v17->m_hStmtSelect;
            CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, _Dest, "SQLGetData", SQLStmt);
            CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
            v14 = 1;
          }
          if ( v17->m_hStmtSelect )
            SQLCloseCursor_0(v17->m_hStmtSelect);
          result = v14;
        }
        else
        {
          StrLen_or_IndPtr = &v10;
          SQLStmt = 0i64;
          v11 = SQLGetData_0(v17->m_hStmtSelect, 2u, 4, v19, 0i64, &v10);
          if ( v11 && v11 != 1 )
          {
            v15 = 0;
            if ( v11 == 100 )
            {
              v15 = 2;
            }
            else
            {
              SQLStmt = v17->m_hStmtSelect;
              CRFNewDatabase::ErrorMsgLog((CRFNewDatabase *)&v17->vfptr, v11, _Dest, "SQLGetData", SQLStmt);
              CRFNewDatabase::ErrorAction((CRFNewDatabase *)&v17->vfptr, v11, v17->m_hStmtSelect);
              v15 = 1;
            }
            if ( v17->m_hStmtSelect )
              SQLCloseCursor_0(v17->m_hStmtSelect);
            result = v15;
          }
          else
          {
            if ( v17->m_hStmtSelect )
              SQLCloseCursor_0(v17->m_hStmtSelect);
            if ( v17->m_bSaveDBLog )
              CRFNewDatabase::FmtLog((CRFNewDatabase *)&v17->vfptr, "%s Success", _Dest);
            result = 0i64;
          }
        }
      }
    }
  }
  else
  {
    CRFNewDatabase::ErrFmtLog((CRFNewDatabase *)&v17->vfptr, "ReConnectDataBase Fail. Query : %s", _Dest);
    result = 1i64;
  }
  return result;
}
