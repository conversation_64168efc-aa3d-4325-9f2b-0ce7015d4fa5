/*
 * Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Insert_n_::_1_::catch$0
 * Address: 0x1403AA380
 */

void __fastcall __noreturn std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::_Insert_n_::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1

  v2 = a2;
  std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>>::_Destroy(
    *(std::vector<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *> > **)(a2 + 160),
    *(CMoveMapLimitInfo ***)(a2 + 64),
    *(CMoveMapLimitInfo ***)(a2 + 72));
  std::allocator<CMoveMapLimitInfo *>::deallocate(
    (std::allocator<CMoveMapLimitInfo *> *)(*(_QWORD *)(v2 + 160) + 8i64),
    *(CMoveMapLimitInfo ***)(v2 + 64),
    *(_QWORD *)(v2 + 56));
  CxxThrowException_0(0i64, 0i64);
}
