/*
 * Function: j_??$_Move_cat@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@@std@@YA?AU_Undefined_move_tag@0@AEBQEAVCGuildBattleRewardItem@GUILD_BATTLE@@@Z
 * Address: 0x14001233C
 */

std::_Undefined_move_tag __fastcall std::_Move_cat<GUILD_BATTLE::CGuildBattleRewardItem *>(GUILD_BATTLE::CGuildBattleRewardItem *const *__formal)
{
  return std::_Move_cat<GUILD_BATTLE::CGuildBattleRewardItem *>(__formal);
}
